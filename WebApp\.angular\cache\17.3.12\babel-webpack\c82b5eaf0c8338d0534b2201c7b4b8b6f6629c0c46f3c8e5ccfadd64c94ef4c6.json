{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./ai-agent.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"C:/Users/<USER>/source/ai-hub/WebApp/src/app/ai-settings/ai-agent/ai-agent.component.ts.css?ngResource!=!C:\\\\Users\\\\<USER>\\\\source\\\\ai-hub\\\\WebApp\\\\node_modules\\\\@ngtools\\\\webpack\\\\src\\\\loaders\\\\inline-resource.js?data=CiAgICAgIC5zZWFyY2gtaXRlbSB7CiAgICAgICAgZGlzcGxheTogZmxleDsKICAgICAgfQoKICAgICAgLnNlYXJjaC1pdGVtLWRlc2MgewogICAgICAgIGZsZXg6IGF1dG87CiAgICAgICAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7CiAgICAgICAgb3ZlcmZsb3c6IGhpZGRlbjsKICAgICAgfQoKICAgICAgLnNlYXJjaC1pdGVtLWNvdW50IHsKICAgICAgICBmbGV4OiBub25lOwogICAgICB9CiAgICAgIC5hbnQtc2VsZWN0LWRyb3Bkb3duewogICAgICAgIGJhY2tncm91bmQ6YmxhY2sgIWltcG9ydGFudDsKICAgICAgfQogICAg!C:/Users/<USER>/source/ai-hub/WebApp/src/app/ai-settings/ai-agent/ai-agent.component.ts\";\nimport { Component, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { AgentDefinitionServiceProxy, ModelDetailsServiceProxy } from '../../../shared/service-proxies/service-proxies';\nimport { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';\nimport { NzAutocompleteModule } from 'ng-zorro-antd/auto-complete';\nimport { NzInputModule } from 'ng-zorro-antd/input';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nlet AiAgentComponent = class AiAgentComponent {\n  constructor(agentDefinitionService, modelDetailsService) {\n    this.agentDefinitionService = agentDefinitionService;\n    this.modelDetailsService = modelDetailsService;\n    this.agents = [];\n    this.currentAgent = {\n      id: '',\n      agentName: '',\n      instructions: ''\n    };\n    this.isEditing = false;\n    this.showForm = false;\n    this.activeModels = [];\n    this.modelSearchQuery = '';\n    this.selectedModel = '';\n    this.filteredModels = [];\n  }\n  ngOnInit() {\n    // Initialize agents if needed\n    this.loadAiAgents();\n    this.loadActiveModels();\n  }\n  loadAiAgents() {\n    this.agentDefinitionService.getAll().subscribe(result => {\n      this.agents = result;\n      // console.log(this.agents);\n    });\n  }\n  loadActiveModels() {\n    this.modelDetailsService.getAllActiveModel().subscribe(result => {\n      this.activeModels = result;\n      this.filteredModels = result;\n    });\n  }\n  addNewAgent() {\n    this.isEditing = false;\n    this.currentAgent = {\n      agentName: '',\n      instructions: ''\n    };\n    this.showForm = true;\n  }\n  editAgent(agent) {\n    this.showForm = true;\n    this.isEditing = true;\n    this.currentAgent = {\n      ...agent\n    };\n    this.selectedModel = agent.modelName;\n    this.modelSearchQuery = agent.modelName;\n  }\n  saveEditedAgent() {\n    this.currentAgent.modelName = this.selectedModel;\n    // Ensure we maintain the workspace when editing\n    // If no workspace is set (from global settings), use \"Default\"\n    if (!this.currentAgent.workspace) {\n      this.currentAgent.workspace = \"Default\";\n    }\n    this.agentDefinitionService.createOrUpdate(this.currentAgent).subscribe(res => {\n      console.log(res);\n      if (res) {\n        // let updatedModel = this.agents.filter(\n        //   (agent) => agent.id == this.currentAgent.id,\n        // );\n        // console.log(this.agents);\n        this.loadAiAgents();\n        this.showForm = false;\n      }\n    });\n    this.resetForm();\n  }\n  updateModel(model) {\n    this.selectedModel = model;\n  }\n  saveAgent() {\n    this.currentAgent.modelName = this.selectedModel;\n    // Set workspace to Default when adding from global settings\n    this.currentAgent.workspace = \"Default\";\n    console.log(this.currentAgent);\n    this.agentDefinitionService.createOrUpdate(this.currentAgent).subscribe(res => {\n      console.log(res);\n      if (res) {\n        this.agents.push(res);\n        this.showForm = false;\n      }\n    });\n    this.resetForm();\n    this.selectedModel = '';\n    this.modelSearchQuery = '';\n  }\n  deleteAgent(name) {\n    this.agentDefinitionService.delete(name).subscribe(res => {\n      if (res) {\n        this.agents = this.agents.filter(agent => agent.agentName !== name);\n      }\n    });\n  }\n  resetForm() {\n    this.currentAgent = {\n      id: '',\n      agentName: '',\n      instructions: ''\n    };\n    this.selectedModel = '';\n    this.modelSearchQuery = '';\n    this.showForm = false;\n  }\n  onChange(e) {\n    const value = e.target.value;\n    this.filteredModels = this.activeModels.filter(model => model.modelName.toLowerCase().includes(value.toLowerCase()));\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: AgentDefinitionServiceProxy\n    }, {\n      type: ModelDetailsServiceProxy\n    }];\n  }\n};\nAiAgentComponent = __decorate([Component({\n  selector: 'app-ai-agent',\n  standalone: true,\n  imports: [CommonModule, FormsModule, ServiceProxyModule, NzInputModule, NzIconModule, NzAutocompleteModule],\n  template: __NG_CLI_RESOURCE__0,\n  schemas: [CUSTOM_ELEMENTS_SCHEMA],\n  styles: [__NG_CLI_RESOURCE__1]\n})], AiAgentComponent);\nexport { AiAgentComponent };", "map": {"version": 3, "names": ["Component", "CUSTOM_ELEMENTS_SCHEMA", "CommonModule", "FormsModule", "AgentDefinitionServiceProxy", "ModelDetailsServiceProxy", "ServiceProxyModule", "NzAutocompleteModule", "NzInputModule", "NzIconModule", "AiAgentComponent", "constructor", "agentDefinitionService", "modelDetailsService", "agents", "currentAgent", "id", "<PERSON><PERSON><PERSON>", "instructions", "isEditing", "showForm", "activeModels", "modelSearchQuery", "selected<PERSON><PERSON>l", "filteredModels", "ngOnInit", "loadAiAgents", "loadActiveModels", "getAll", "subscribe", "result", "getAllActiveModel", "addNewAgent", "editAgent", "agent", "modelName", "saveEditedAgent", "workspace", "createOrUpdate", "res", "console", "log", "resetForm", "updateModel", "model", "saveAgent", "push", "deleteAgent", "name", "delete", "filter", "onChange", "e", "value", "target", "toLowerCase", "includes", "__decorate", "selector", "standalone", "imports", "template", "__NG_CLI_RESOURCE__0", "schemas"], "sources": ["C:\\Users\\<USER>\\source\\ai-hub\\WebApp\\src\\app\\ai-settings\\ai-agent\\ai-agent.component.ts"], "sourcesContent": ["import { Component, CUSTOM_ELEMENTS_SCHEMA, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport {\r\n  AgentDefinitionServiceProxy,\r\n  ModelDetailsServiceProxy,\r\n} from '../../../shared/service-proxies/service-proxies';\r\nimport { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';\r\n\r\nimport { NzAutocompleteModule } from 'ng-zorro-antd/auto-complete';\r\nimport { NzInputModule } from 'ng-zorro-antd/input';\r\nimport { NzIconModule } from 'ng-zorro-antd/icon';\r\n\r\n@Component({\r\n  selector: 'app-ai-agent',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    ServiceProxyModule,\r\n    NzInputModule,\r\n    NzIconModule,\r\n    NzAutocompleteModule,\r\n  ],\r\n  templateUrl: './ai-agent.component.html',\r\n  schemas: [CUSTOM_ELEMENTS_SCHEMA],\r\n  styles: [\r\n    `\r\n      .search-item {\r\n        display: flex;\r\n      }\r\n\r\n      .search-item-desc {\r\n        flex: auto;\r\n        text-overflow: ellipsis;\r\n        overflow: hidden;\r\n      }\r\n\r\n      .search-item-count {\r\n        flex: none;\r\n      }\r\n      .ant-select-dropdown{\r\n        background:black !important;\r\n      }\r\n    `,\r\n  ],\r\n})\r\nexport class AiAgentComponent implements OnInit {\r\n  agents: any[] = [];\r\n  currentAgent: any = {\r\n    id: '',\r\n    agentName: '',\r\n    instructions: '',\r\n  };\r\n  isEditing = false;\r\n  showForm = false;\r\n  activeModels: any = [];\r\n  modelSearchQuery = '';\r\n  selectedModel = '';\r\n  filteredModels: any[] = [];\r\n  constructor(\r\n    private agentDefinitionService: AgentDefinitionServiceProxy,\r\n    private modelDetailsService: ModelDetailsServiceProxy\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    // Initialize agents if needed\r\n    this.loadAiAgents();\r\n    this.loadActiveModels();\r\n  }\r\n  loadAiAgents() {\r\n    this.agentDefinitionService.getAll().subscribe((result) => {\r\n      this.agents = result;\r\n      // console.log(this.agents);\r\n    });\r\n  }\r\n  loadActiveModels() {\r\n    this.modelDetailsService.getAllActiveModel().subscribe((result: any) => {\r\n      this.activeModels = result;\r\n      this.filteredModels = result;\r\n    });\r\n  }\r\n\r\n  addNewAgent(): void {\r\n    this.isEditing = false;\r\n    this.currentAgent = {\r\n      agentName: '',\r\n      instructions: '',\r\n    };\r\n    this.showForm = true;\r\n  }\r\n\r\n  editAgent(agent: any): void {\r\n    this.showForm = true;\r\n    this.isEditing = true;\r\n    this.currentAgent = { ...agent };\r\n    this.selectedModel = agent.modelName;\r\n    this.modelSearchQuery = agent.modelName;\r\n  }\r\n  saveEditedAgent(): void {\r\n    this.currentAgent.modelName = this.selectedModel;\r\n\r\n    // Ensure we maintain the workspace when editing\r\n    // If no workspace is set (from global settings), use \"Default\"\r\n    if (!this.currentAgent.workspace) {\r\n      this.currentAgent.workspace = \"Default\";\r\n    }\r\n\r\n    this.agentDefinitionService\r\n      .createOrUpdate(this.currentAgent)\r\n      .subscribe((res: any) => {\r\n        console.log(res);\r\n        if (res) {\r\n          // let updatedModel = this.agents.filter(\r\n          //   (agent) => agent.id == this.currentAgent.id,\r\n          // );\r\n          // console.log(this.agents);\r\n          this.loadAiAgents();\r\n          this.showForm = false;\r\n        }\r\n      });\r\n    this.resetForm();\r\n  }\r\n  updateModel(model: string) {\r\n\r\n    this.selectedModel = model;\r\n  }\r\n  saveAgent(): void {\r\n    this.currentAgent.modelName = this.selectedModel;\r\n    // Set workspace to Default when adding from global settings\r\n    this.currentAgent.workspace = \"Default\";\r\n\r\n    console.log(this.currentAgent);\r\n    this.agentDefinitionService\r\n      .createOrUpdate(this.currentAgent)\r\n      .subscribe((res: any) => {\r\n        console.log(res);\r\n        if (res) {\r\n          this.agents.push(res);\r\n          this.showForm = false;\r\n        }\r\n      });\r\n    this.resetForm();\r\n    this.selectedModel = '';\r\n    this.modelSearchQuery = '';\r\n  }\r\n\r\n  deleteAgent(name: string): void {\r\n    this.agentDefinitionService.delete(name).subscribe((res: any) => {\r\n      if (res) {\r\n        this.agents = this.agents.filter((agent) => agent.agentName !== name);\r\n      }\r\n    });\r\n  }\r\n\r\n  resetForm(): void {\r\n    this.currentAgent = {\r\n      id: '',\r\n      agentName: '',\r\n      instructions: '',\r\n    };\r\n    this.selectedModel = '';\r\n    this.modelSearchQuery = '';\r\n    this.showForm = false;\r\n\r\n  }\r\n\r\n  onChange(e: Event): void {\r\n    const value = (e.target as HTMLInputElement).value;\r\n    this.filteredModels = this.activeModels.filter((model: any) =>\r\n      model.modelName.toLowerCase().includes(value.toLowerCase())\r\n    );\r\n  }\r\n}\r\n"], "mappings": ";;;AAAA,SAASA,SAAS,EAAEC,sBAAsB,QAAgB,eAAe;AACzE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SACEC,2BAA2B,EAC3BC,wBAAwB,QACnB,iDAAiD;AACxD,SAASC,kBAAkB,QAAQ,sDAAsD;AAEzF,SAASC,oBAAoB,QAAQ,6BAA6B;AAClE,SAASC,aAAa,QAAQ,qBAAqB;AACnD,SAASC,YAAY,QAAQ,oBAAoB;AAoC1C,IAAMC,gBAAgB,GAAtB,MAAMA,gBAAgB;EAa3BC,YACUC,sBAAmD,EACnDC,mBAA6C;IAD7C,KAAAD,sBAAsB,GAAtBA,sBAAsB;IACtB,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAd7B,KAAAC,MAAM,GAAU,EAAE;IAClB,KAAAC,YAAY,GAAQ;MAClBC,EAAE,EAAE,EAAE;MACNC,SAAS,EAAE,EAAE;MACbC,YAAY,EAAE;KACf;IACD,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAC,YAAY,GAAQ,EAAE;IACtB,KAAAC,gBAAgB,GAAG,EAAE;IACrB,KAAAC,aAAa,GAAG,EAAE;IAClB,KAAAC,cAAc,GAAU,EAAE;EAItB;EAEJC,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACC,gBAAgB,EAAE;EACzB;EACAD,YAAYA,CAAA;IACV,IAAI,CAACd,sBAAsB,CAACgB,MAAM,EAAE,CAACC,SAAS,CAAEC,MAAM,IAAI;MACxD,IAAI,CAAChB,MAAM,GAAGgB,MAAM;MACpB;IACF,CAAC,CAAC;EACJ;EACAH,gBAAgBA,CAAA;IACd,IAAI,CAACd,mBAAmB,CAACkB,iBAAiB,EAAE,CAACF,SAAS,CAAEC,MAAW,IAAI;MACrE,IAAI,CAACT,YAAY,GAAGS,MAAM;MAC1B,IAAI,CAACN,cAAc,GAAGM,MAAM;IAC9B,CAAC,CAAC;EACJ;EAEAE,WAAWA,CAAA;IACT,IAAI,CAACb,SAAS,GAAG,KAAK;IACtB,IAAI,CAACJ,YAAY,GAAG;MAClBE,SAAS,EAAE,EAAE;MACbC,YAAY,EAAE;KACf;IACD,IAAI,CAACE,QAAQ,GAAG,IAAI;EACtB;EAEAa,SAASA,CAACC,KAAU;IAClB,IAAI,CAACd,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACD,SAAS,GAAG,IAAI;IACrB,IAAI,CAACJ,YAAY,GAAG;MAAE,GAAGmB;IAAK,CAAE;IAChC,IAAI,CAACX,aAAa,GAAGW,KAAK,CAACC,SAAS;IACpC,IAAI,CAACb,gBAAgB,GAAGY,KAAK,CAACC,SAAS;EACzC;EACAC,eAAeA,CAAA;IACb,IAAI,CAACrB,YAAY,CAACoB,SAAS,GAAG,IAAI,CAACZ,aAAa;IAEhD;IACA;IACA,IAAI,CAAC,IAAI,CAACR,YAAY,CAACsB,SAAS,EAAE;MAChC,IAAI,CAACtB,YAAY,CAACsB,SAAS,GAAG,SAAS;;IAGzC,IAAI,CAACzB,sBAAsB,CACxB0B,cAAc,CAAC,IAAI,CAACvB,YAAY,CAAC,CACjCc,SAAS,CAAEU,GAAQ,IAAI;MACtBC,OAAO,CAACC,GAAG,CAACF,GAAG,CAAC;MAChB,IAAIA,GAAG,EAAE;QACP;QACA;QACA;QACA;QACA,IAAI,CAACb,YAAY,EAAE;QACnB,IAAI,CAACN,QAAQ,GAAG,KAAK;;IAEzB,CAAC,CAAC;IACJ,IAAI,CAACsB,SAAS,EAAE;EAClB;EACAC,WAAWA,CAACC,KAAa;IAEvB,IAAI,CAACrB,aAAa,GAAGqB,KAAK;EAC5B;EACAC,SAASA,CAAA;IACP,IAAI,CAAC9B,YAAY,CAACoB,SAAS,GAAG,IAAI,CAACZ,aAAa;IAChD;IACA,IAAI,CAACR,YAAY,CAACsB,SAAS,GAAG,SAAS;IAEvCG,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC1B,YAAY,CAAC;IAC9B,IAAI,CAACH,sBAAsB,CACxB0B,cAAc,CAAC,IAAI,CAACvB,YAAY,CAAC,CACjCc,SAAS,CAAEU,GAAQ,IAAI;MACtBC,OAAO,CAACC,GAAG,CAACF,GAAG,CAAC;MAChB,IAAIA,GAAG,EAAE;QACP,IAAI,CAACzB,MAAM,CAACgC,IAAI,CAACP,GAAG,CAAC;QACrB,IAAI,CAACnB,QAAQ,GAAG,KAAK;;IAEzB,CAAC,CAAC;IACJ,IAAI,CAACsB,SAAS,EAAE;IAChB,IAAI,CAACnB,aAAa,GAAG,EAAE;IACvB,IAAI,CAACD,gBAAgB,GAAG,EAAE;EAC5B;EAEAyB,WAAWA,CAACC,IAAY;IACtB,IAAI,CAACpC,sBAAsB,CAACqC,MAAM,CAACD,IAAI,CAAC,CAACnB,SAAS,CAAEU,GAAQ,IAAI;MAC9D,IAAIA,GAAG,EAAE;QACP,IAAI,CAACzB,MAAM,GAAG,IAAI,CAACA,MAAM,CAACoC,MAAM,CAAEhB,KAAK,IAAKA,KAAK,CAACjB,SAAS,KAAK+B,IAAI,CAAC;;IAEzE,CAAC,CAAC;EACJ;EAEAN,SAASA,CAAA;IACP,IAAI,CAAC3B,YAAY,GAAG;MAClBC,EAAE,EAAE,EAAE;MACNC,SAAS,EAAE,EAAE;MACbC,YAAY,EAAE;KACf;IACD,IAAI,CAACK,aAAa,GAAG,EAAE;IACvB,IAAI,CAACD,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACF,QAAQ,GAAG,KAAK;EAEvB;EAEA+B,QAAQA,CAACC,CAAQ;IACf,MAAMC,KAAK,GAAID,CAAC,CAACE,MAA2B,CAACD,KAAK;IAClD,IAAI,CAAC7B,cAAc,GAAG,IAAI,CAACH,YAAY,CAAC6B,MAAM,CAAEN,KAAU,IACxDA,KAAK,CAACT,SAAS,CAACoB,WAAW,EAAE,CAACC,QAAQ,CAACH,KAAK,CAACE,WAAW,EAAE,CAAC,CAC5D;EACH;;;;;;;;;AA7HW7C,gBAAgB,GAAA+C,UAAA,EAlC5BzD,SAAS,CAAC;EACT0D,QAAQ,EAAE,cAAc;EACxBC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACP1D,YAAY,EACZC,WAAW,EACXG,kBAAkB,EAClBE,aAAa,EACbC,YAAY,EACZF,oBAAoB,CACrB;EACDsD,QAAA,EAAAC,oBAAwC;EACxCC,OAAO,EAAE,CAAC9D,sBAAsB,CAAC;;CAqBlC,CAAC,C,EACWS,gBAAgB,CA8H5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}