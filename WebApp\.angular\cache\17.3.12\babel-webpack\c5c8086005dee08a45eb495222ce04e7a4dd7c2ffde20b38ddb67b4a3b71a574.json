{"ast": null, "code": "/**\n * @license Angular v17.3.12\n * (c) 2010-2024 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { assertInInjectionContext, inject, DestroyRef, ɵRuntimeError, ɵgetOutputDestroyRef, Injector, effect, untracked, assertNotInReactiveContext, signal, computed } from '@angular/core';\nimport { Observable, ReplaySubject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\n\n/**\n * Operator which completes the Observable when the calling context (component, directive, service,\n * etc) is destroyed.\n *\n * @param destroyRef optionally, the `DestroyRef` representing the current context. This can be\n *     passed explicitly to use `takeUntilDestroyed` outside of an [injection\n * context](guide/dependency-injection-context). Otherwise, the current `DestroyRef` is injected.\n *\n * @developerPreview\n */\nfunction takeUntilDestroyed(destroyRef) {\n  if (!destroyRef) {\n    assertInInjectionContext(takeUntilDestroyed);\n    destroyRef = inject(DestroyRef);\n  }\n  const destroyed$ = new Observable(observer => {\n    const unregisterFn = destroyRef.onDestroy(observer.next.bind(observer));\n    return unregisterFn;\n  });\n  return source => {\n    return source.pipe(takeUntil(destroyed$));\n  };\n}\n\n/**\n * Implementation of `OutputRef` that emits values from\n * an RxJS observable source.\n *\n * @internal\n */\nclass OutputFromObservableRef {\n  constructor(source) {\n    this.source = source;\n    this.destroyed = false;\n    this.destroyRef = inject(DestroyRef);\n    this.destroyRef.onDestroy(() => {\n      this.destroyed = true;\n    });\n  }\n  subscribe(callbackFn) {\n    if (this.destroyed) {\n      throw new ɵRuntimeError(953 /* ɵRuntimeErrorCode.OUTPUT_REF_DESTROYED */, ngDevMode && 'Unexpected subscription to destroyed `OutputRef`. ' + 'The owning directive/component is destroyed.');\n    }\n    // Stop yielding more values when the directive/component is already destroyed.\n    const subscription = this.source.pipe(takeUntilDestroyed(this.destroyRef)).subscribe({\n      next: value => callbackFn(value)\n    });\n    return {\n      unsubscribe: () => subscription.unsubscribe()\n    };\n  }\n}\n/**\n * Declares an Angular output that is using an RxJS observable as a source\n * for events dispatched to parent subscribers.\n *\n * The behavior for an observable as source is defined as followed:\n *    1. New values are forwarded to the Angular output (next notifications).\n *    2. Errors notifications are not handled by Angular. You need to handle these manually.\n *       For example by using `catchError`.\n *    3. Completion notifications stop the output from emitting new values.\n *\n * @usageNotes\n * Initialize an output in your directive by declaring a\n * class field and initializing it with the `outputFromObservable()` function.\n *\n * ```ts\n * @Directive({..})\n * export class MyDir {\n *   nameChange$ = <some-observable>;\n *   nameChange = outputFromObservable(this.nameChange$);\n * }\n * ```\n *\n * @developerPreview\n */\nfunction outputFromObservable(observable, opts) {\n  ngDevMode && assertInInjectionContext(outputFromObservable);\n  return new OutputFromObservableRef(observable);\n}\n\n/**\n * Converts an Angular output declared via `output()` or `outputFromObservable()`\n * to an observable.\n *\n * You can subscribe to the output via `Observable.subscribe` then.\n *\n * @developerPreview\n */\nfunction outputToObservable(ref) {\n  const destroyRef = ɵgetOutputDestroyRef(ref);\n  return new Observable(observer => {\n    // Complete the observable upon directive/component destroy.\n    // Note: May be `undefined` if an `EventEmitter` is declared outside\n    // of an injection context.\n    destroyRef?.onDestroy(() => observer.complete());\n    const subscription = ref.subscribe(v => observer.next(v));\n    return () => subscription.unsubscribe();\n  });\n}\n\n/**\n * Exposes the value of an Angular `Signal` as an RxJS `Observable`.\n *\n * The signal's value will be propagated into the `Observable`'s subscribers using an `effect`.\n *\n * `toObservable` must be called in an injection context unless an injector is provided via options.\n *\n * @developerPreview\n */\nfunction toObservable(source, options) {\n  !options?.injector && assertInInjectionContext(toObservable);\n  const injector = options?.injector ?? inject(Injector);\n  const subject = new ReplaySubject(1);\n  const watcher = effect(() => {\n    let value;\n    try {\n      value = source();\n    } catch (err) {\n      untracked(() => subject.error(err));\n      return;\n    }\n    untracked(() => subject.next(value));\n  }, {\n    injector,\n    manualCleanup: true\n  });\n  injector.get(DestroyRef).onDestroy(() => {\n    watcher.destroy();\n    subject.complete();\n  });\n  return subject.asObservable();\n}\n\n/**\n * Get the current value of an `Observable` as a reactive `Signal`.\n *\n * `toSignal` returns a `Signal` which provides synchronous reactive access to values produced\n * by the given `Observable`, by subscribing to that `Observable`. The returned `Signal` will always\n * have the most recent value emitted by the subscription, and will throw an error if the\n * `Observable` errors.\n *\n * With `requireSync` set to `true`, `toSignal` will assert that the `Observable` produces a value\n * immediately upon subscription. No `initialValue` is needed in this case, and the returned signal\n * does not include an `undefined` type.\n *\n * By default, the subscription will be automatically cleaned up when the current [injection\n * context](/guide/dependency-injection-context) is destroyed. For example, when `toSignal` is\n * called during the construction of a component, the subscription will be cleaned up when the\n * component is destroyed. If an injection context is not available, an explicit `Injector` can be\n * passed instead.\n *\n * If the subscription should persist until the `Observable` itself completes, the `manualCleanup`\n * option can be specified instead, which disables the automatic subscription teardown. No injection\n * context is needed in this configuration as well.\n *\n * @developerPreview\n */\nfunction toSignal(source, options) {\n  ngDevMode && assertNotInReactiveContext(toSignal, 'Invoking `toSignal` causes new subscriptions every time. ' + 'Consider moving `toSignal` outside of the reactive context and read the signal value where needed.');\n  const requiresCleanup = !options?.manualCleanup;\n  requiresCleanup && !options?.injector && assertInInjectionContext(toSignal);\n  const cleanupRef = requiresCleanup ? options?.injector?.get(DestroyRef) ?? inject(DestroyRef) : null;\n  // Note: T is the Observable value type, and U is the initial value type. They don't have to be\n  // the same - the returned signal gives values of type `T`.\n  let state;\n  if (options?.requireSync) {\n    // Initially the signal is in a `NoValue` state.\n    state = signal({\n      kind: 0 /* StateKind.NoValue */\n    });\n  } else {\n    // If an initial value was passed, use it. Otherwise, use `undefined` as the initial value.\n    state = signal({\n      kind: 1 /* StateKind.Value */,\n      value: options?.initialValue\n    });\n  }\n  // Note: This code cannot run inside a reactive context (see assertion above). If we'd support\n  // this, we would subscribe to the observable outside of the current reactive context, avoiding\n  // that side-effect signal reads/writes are attribute to the current consumer. The current\n  // consumer only needs to be notified when the `state` signal changes through the observable\n  // subscription. Additional context (related to async pipe):\n  // https://github.com/angular/angular/pull/50522.\n  const sub = source.subscribe({\n    next: value => state.set({\n      kind: 1 /* StateKind.Value */,\n      value\n    }),\n    error: error => {\n      if (options?.rejectErrors) {\n        // Kick the error back to RxJS. It will be caught and rethrown in a macrotask, which causes\n        // the error to end up as an uncaught exception.\n        throw error;\n      }\n      state.set({\n        kind: 2 /* StateKind.Error */,\n        error\n      });\n    }\n    // Completion of the Observable is meaningless to the signal. Signals don't have a concept of\n    // \"complete\".\n  });\n  if (ngDevMode && options?.requireSync && state().kind === 0 /* StateKind.NoValue */) {\n    throw new ɵRuntimeError(601 /* ɵRuntimeErrorCode.REQUIRE_SYNC_WITHOUT_SYNC_EMIT */, '`toSignal()` called with `requireSync` but `Observable` did not emit synchronously.');\n  }\n  // Unsubscribe when the current context is destroyed, if requested.\n  cleanupRef?.onDestroy(sub.unsubscribe.bind(sub));\n  // The actual returned signal is a `computed` of the `State` signal, which maps the various states\n  // to either values or errors.\n  return computed(() => {\n    const current = state();\n    switch (current.kind) {\n      case 1 /* StateKind.Value */:\n        return current.value;\n      case 2 /* StateKind.Error */:\n        throw current.error;\n      case 0 /* StateKind.NoValue */:\n        // This shouldn't really happen because the error is thrown on creation.\n        // TODO(alxhub): use a RuntimeError when we finalize the error semantics\n        throw new ɵRuntimeError(601 /* ɵRuntimeErrorCode.REQUIRE_SYNC_WITHOUT_SYNC_EMIT */, '`toSignal()` called with `requireSync` but `Observable` did not emit synchronously.');\n    }\n  });\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { outputFromObservable, outputToObservable, takeUntilDestroyed, toObservable, toSignal };", "map": {"version": 3, "names": ["assertInInjectionContext", "inject", "DestroyRef", "ɵRuntimeError", "ɵgetOutputDestroyRef", "Injector", "effect", "untracked", "assertNotInReactiveContext", "signal", "computed", "Observable", "ReplaySubject", "takeUntil", "takeUntilDestroyed", "destroyRef", "destroyed$", "observer", "unregisterFn", "onDestroy", "next", "bind", "source", "pipe", "OutputFromObservableRef", "constructor", "destroyed", "subscribe", "callbackFn", "ngDevMode", "subscription", "value", "unsubscribe", "outputFromObservable", "observable", "opts", "outputToObservable", "ref", "complete", "v", "toObservable", "options", "injector", "subject", "watcher", "err", "error", "manualCleanup", "get", "destroy", "asObservable", "toSignal", "requiresCleanup", "cleanupRef", "state", "requireSync", "kind", "initialValue", "sub", "set", "rejectErrors", "current"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/@angular/core/fesm2022/rxjs-interop.mjs"], "sourcesContent": ["/**\n * @license Angular v17.3.12\n * (c) 2010-2024 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { assertInInjectionContext, inject, DestroyRef, ɵRuntimeError, ɵgetOutputDestroyRef, Injector, effect, untracked, assertNotInReactiveContext, signal, computed } from '@angular/core';\nimport { Observable, ReplaySubject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\n\n/**\n * Operator which completes the Observable when the calling context (component, directive, service,\n * etc) is destroyed.\n *\n * @param destroyRef optionally, the `DestroyRef` representing the current context. This can be\n *     passed explicitly to use `takeUntilDestroyed` outside of an [injection\n * context](guide/dependency-injection-context). Otherwise, the current `DestroyRef` is injected.\n *\n * @developerPreview\n */\nfunction takeUntilDestroyed(destroyRef) {\n    if (!destroyRef) {\n        assertInInjectionContext(takeUntilDestroyed);\n        destroyRef = inject(DestroyRef);\n    }\n    const destroyed$ = new Observable(observer => {\n        const unregisterFn = destroyRef.onDestroy(observer.next.bind(observer));\n        return unregisterFn;\n    });\n    return (source) => {\n        return source.pipe(takeUntil(destroyed$));\n    };\n}\n\n/**\n * Implementation of `OutputRef` that emits values from\n * an RxJS observable source.\n *\n * @internal\n */\nclass OutputFromObservableRef {\n    constructor(source) {\n        this.source = source;\n        this.destroyed = false;\n        this.destroyRef = inject(DestroyRef);\n        this.destroyRef.onDestroy(() => {\n            this.destroyed = true;\n        });\n    }\n    subscribe(callbackFn) {\n        if (this.destroyed) {\n            throw new ɵRuntimeError(953 /* ɵRuntimeErrorCode.OUTPUT_REF_DESTROYED */, ngDevMode &&\n                'Unexpected subscription to destroyed `OutputRef`. ' +\n                    'The owning directive/component is destroyed.');\n        }\n        // Stop yielding more values when the directive/component is already destroyed.\n        const subscription = this.source.pipe(takeUntilDestroyed(this.destroyRef)).subscribe({\n            next: value => callbackFn(value),\n        });\n        return {\n            unsubscribe: () => subscription.unsubscribe(),\n        };\n    }\n}\n/**\n * Declares an Angular output that is using an RxJS observable as a source\n * for events dispatched to parent subscribers.\n *\n * The behavior for an observable as source is defined as followed:\n *    1. New values are forwarded to the Angular output (next notifications).\n *    2. Errors notifications are not handled by Angular. You need to handle these manually.\n *       For example by using `catchError`.\n *    3. Completion notifications stop the output from emitting new values.\n *\n * @usageNotes\n * Initialize an output in your directive by declaring a\n * class field and initializing it with the `outputFromObservable()` function.\n *\n * ```ts\n * @Directive({..})\n * export class MyDir {\n *   nameChange$ = <some-observable>;\n *   nameChange = outputFromObservable(this.nameChange$);\n * }\n * ```\n *\n * @developerPreview\n */\nfunction outputFromObservable(observable, opts) {\n    ngDevMode && assertInInjectionContext(outputFromObservable);\n    return new OutputFromObservableRef(observable);\n}\n\n/**\n * Converts an Angular output declared via `output()` or `outputFromObservable()`\n * to an observable.\n *\n * You can subscribe to the output via `Observable.subscribe` then.\n *\n * @developerPreview\n */\nfunction outputToObservable(ref) {\n    const destroyRef = ɵgetOutputDestroyRef(ref);\n    return new Observable(observer => {\n        // Complete the observable upon directive/component destroy.\n        // Note: May be `undefined` if an `EventEmitter` is declared outside\n        // of an injection context.\n        destroyRef?.onDestroy(() => observer.complete());\n        const subscription = ref.subscribe(v => observer.next(v));\n        return () => subscription.unsubscribe();\n    });\n}\n\n/**\n * Exposes the value of an Angular `Signal` as an RxJS `Observable`.\n *\n * The signal's value will be propagated into the `Observable`'s subscribers using an `effect`.\n *\n * `toObservable` must be called in an injection context unless an injector is provided via options.\n *\n * @developerPreview\n */\nfunction toObservable(source, options) {\n    !options?.injector && assertInInjectionContext(toObservable);\n    const injector = options?.injector ?? inject(Injector);\n    const subject = new ReplaySubject(1);\n    const watcher = effect(() => {\n        let value;\n        try {\n            value = source();\n        }\n        catch (err) {\n            untracked(() => subject.error(err));\n            return;\n        }\n        untracked(() => subject.next(value));\n    }, { injector, manualCleanup: true });\n    injector.get(DestroyRef).onDestroy(() => {\n        watcher.destroy();\n        subject.complete();\n    });\n    return subject.asObservable();\n}\n\n/**\n * Get the current value of an `Observable` as a reactive `Signal`.\n *\n * `toSignal` returns a `Signal` which provides synchronous reactive access to values produced\n * by the given `Observable`, by subscribing to that `Observable`. The returned `Signal` will always\n * have the most recent value emitted by the subscription, and will throw an error if the\n * `Observable` errors.\n *\n * With `requireSync` set to `true`, `toSignal` will assert that the `Observable` produces a value\n * immediately upon subscription. No `initialValue` is needed in this case, and the returned signal\n * does not include an `undefined` type.\n *\n * By default, the subscription will be automatically cleaned up when the current [injection\n * context](/guide/dependency-injection-context) is destroyed. For example, when `toSignal` is\n * called during the construction of a component, the subscription will be cleaned up when the\n * component is destroyed. If an injection context is not available, an explicit `Injector` can be\n * passed instead.\n *\n * If the subscription should persist until the `Observable` itself completes, the `manualCleanup`\n * option can be specified instead, which disables the automatic subscription teardown. No injection\n * context is needed in this configuration as well.\n *\n * @developerPreview\n */\nfunction toSignal(source, options) {\n    ngDevMode &&\n        assertNotInReactiveContext(toSignal, 'Invoking `toSignal` causes new subscriptions every time. ' +\n            'Consider moving `toSignal` outside of the reactive context and read the signal value where needed.');\n    const requiresCleanup = !options?.manualCleanup;\n    requiresCleanup && !options?.injector && assertInInjectionContext(toSignal);\n    const cleanupRef = requiresCleanup ? options?.injector?.get(DestroyRef) ?? inject(DestroyRef) : null;\n    // Note: T is the Observable value type, and U is the initial value type. They don't have to be\n    // the same - the returned signal gives values of type `T`.\n    let state;\n    if (options?.requireSync) {\n        // Initially the signal is in a `NoValue` state.\n        state = signal({ kind: 0 /* StateKind.NoValue */ });\n    }\n    else {\n        // If an initial value was passed, use it. Otherwise, use `undefined` as the initial value.\n        state = signal({ kind: 1 /* StateKind.Value */, value: options?.initialValue });\n    }\n    // Note: This code cannot run inside a reactive context (see assertion above). If we'd support\n    // this, we would subscribe to the observable outside of the current reactive context, avoiding\n    // that side-effect signal reads/writes are attribute to the current consumer. The current\n    // consumer only needs to be notified when the `state` signal changes through the observable\n    // subscription. Additional context (related to async pipe):\n    // https://github.com/angular/angular/pull/50522.\n    const sub = source.subscribe({\n        next: value => state.set({ kind: 1 /* StateKind.Value */, value }),\n        error: error => {\n            if (options?.rejectErrors) {\n                // Kick the error back to RxJS. It will be caught and rethrown in a macrotask, which causes\n                // the error to end up as an uncaught exception.\n                throw error;\n            }\n            state.set({ kind: 2 /* StateKind.Error */, error });\n        },\n        // Completion of the Observable is meaningless to the signal. Signals don't have a concept of\n        // \"complete\".\n    });\n    if (ngDevMode && options?.requireSync && state().kind === 0 /* StateKind.NoValue */) {\n        throw new ɵRuntimeError(601 /* ɵRuntimeErrorCode.REQUIRE_SYNC_WITHOUT_SYNC_EMIT */, '`toSignal()` called with `requireSync` but `Observable` did not emit synchronously.');\n    }\n    // Unsubscribe when the current context is destroyed, if requested.\n    cleanupRef?.onDestroy(sub.unsubscribe.bind(sub));\n    // The actual returned signal is a `computed` of the `State` signal, which maps the various states\n    // to either values or errors.\n    return computed(() => {\n        const current = state();\n        switch (current.kind) {\n            case 1 /* StateKind.Value */:\n                return current.value;\n            case 2 /* StateKind.Error */:\n                throw current.error;\n            case 0 /* StateKind.NoValue */:\n                // This shouldn't really happen because the error is thrown on creation.\n                // TODO(alxhub): use a RuntimeError when we finalize the error semantics\n                throw new ɵRuntimeError(601 /* ɵRuntimeErrorCode.REQUIRE_SYNC_WITHOUT_SYNC_EMIT */, '`toSignal()` called with `requireSync` but `Observable` did not emit synchronously.');\n        }\n    });\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { outputFromObservable, outputToObservable, takeUntilDestroyed, toObservable, toSignal };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,wBAAwB,EAAEC,MAAM,EAAEC,UAAU,EAAEC,aAAa,EAAEC,oBAAoB,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,0BAA0B,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AAC5L,SAASC,UAAU,EAAEC,aAAa,QAAQ,MAAM;AAChD,SAASC,SAAS,QAAQ,gBAAgB;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,kBAAkBA,CAACC,UAAU,EAAE;EACpC,IAAI,CAACA,UAAU,EAAE;IACbf,wBAAwB,CAACc,kBAAkB,CAAC;IAC5CC,UAAU,GAAGd,MAAM,CAACC,UAAU,CAAC;EACnC;EACA,MAAMc,UAAU,GAAG,IAAIL,UAAU,CAACM,QAAQ,IAAI;IAC1C,MAAMC,YAAY,GAAGH,UAAU,CAACI,SAAS,CAACF,QAAQ,CAACG,IAAI,CAACC,IAAI,CAACJ,QAAQ,CAAC,CAAC;IACvE,OAAOC,YAAY;EACvB,CAAC,CAAC;EACF,OAAQI,MAAM,IAAK;IACf,OAAOA,MAAM,CAACC,IAAI,CAACV,SAAS,CAACG,UAAU,CAAC,CAAC;EAC7C,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMQ,uBAAuB,CAAC;EAC1BC,WAAWA,CAACH,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACI,SAAS,GAAG,KAAK;IACtB,IAAI,CAACX,UAAU,GAAGd,MAAM,CAACC,UAAU,CAAC;IACpC,IAAI,CAACa,UAAU,CAACI,SAAS,CAAC,MAAM;MAC5B,IAAI,CAACO,SAAS,GAAG,IAAI;IACzB,CAAC,CAAC;EACN;EACAC,SAASA,CAACC,UAAU,EAAE;IAClB,IAAI,IAAI,CAACF,SAAS,EAAE;MAChB,MAAM,IAAIvB,aAAa,CAAC,GAAG,CAAC,8CAA8C0B,SAAS,IAC/E,oDAAoD,GAChD,8CAA8C,CAAC;IAC3D;IACA;IACA,MAAMC,YAAY,GAAG,IAAI,CAACR,MAAM,CAACC,IAAI,CAACT,kBAAkB,CAAC,IAAI,CAACC,UAAU,CAAC,CAAC,CAACY,SAAS,CAAC;MACjFP,IAAI,EAAEW,KAAK,IAAIH,UAAU,CAACG,KAAK;IACnC,CAAC,CAAC;IACF,OAAO;MACHC,WAAW,EAAEA,CAAA,KAAMF,YAAY,CAACE,WAAW,CAAC;IAChD,CAAC;EACL;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,oBAAoBA,CAACC,UAAU,EAAEC,IAAI,EAAE;EAC5CN,SAAS,IAAI7B,wBAAwB,CAACiC,oBAAoB,CAAC;EAC3D,OAAO,IAAIT,uBAAuB,CAACU,UAAU,CAAC;AAClD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,kBAAkBA,CAACC,GAAG,EAAE;EAC7B,MAAMtB,UAAU,GAAGX,oBAAoB,CAACiC,GAAG,CAAC;EAC5C,OAAO,IAAI1B,UAAU,CAACM,QAAQ,IAAI;IAC9B;IACA;IACA;IACAF,UAAU,EAAEI,SAAS,CAAC,MAAMF,QAAQ,CAACqB,QAAQ,CAAC,CAAC,CAAC;IAChD,MAAMR,YAAY,GAAGO,GAAG,CAACV,SAAS,CAACY,CAAC,IAAItB,QAAQ,CAACG,IAAI,CAACmB,CAAC,CAAC,CAAC;IACzD,OAAO,MAAMT,YAAY,CAACE,WAAW,CAAC,CAAC;EAC3C,CAAC,CAAC;AACN;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASQ,YAAYA,CAAClB,MAAM,EAAEmB,OAAO,EAAE;EACnC,CAACA,OAAO,EAAEC,QAAQ,IAAI1C,wBAAwB,CAACwC,YAAY,CAAC;EAC5D,MAAME,QAAQ,GAAGD,OAAO,EAAEC,QAAQ,IAAIzC,MAAM,CAACI,QAAQ,CAAC;EACtD,MAAMsC,OAAO,GAAG,IAAI/B,aAAa,CAAC,CAAC,CAAC;EACpC,MAAMgC,OAAO,GAAGtC,MAAM,CAAC,MAAM;IACzB,IAAIyB,KAAK;IACT,IAAI;MACAA,KAAK,GAAGT,MAAM,CAAC,CAAC;IACpB,CAAC,CACD,OAAOuB,GAAG,EAAE;MACRtC,SAAS,CAAC,MAAMoC,OAAO,CAACG,KAAK,CAACD,GAAG,CAAC,CAAC;MACnC;IACJ;IACAtC,SAAS,CAAC,MAAMoC,OAAO,CAACvB,IAAI,CAACW,KAAK,CAAC,CAAC;EACxC,CAAC,EAAE;IAAEW,QAAQ;IAAEK,aAAa,EAAE;EAAK,CAAC,CAAC;EACrCL,QAAQ,CAACM,GAAG,CAAC9C,UAAU,CAAC,CAACiB,SAAS,CAAC,MAAM;IACrCyB,OAAO,CAACK,OAAO,CAAC,CAAC;IACjBN,OAAO,CAACL,QAAQ,CAAC,CAAC;EACtB,CAAC,CAAC;EACF,OAAOK,OAAO,CAACO,YAAY,CAAC,CAAC;AACjC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAAC7B,MAAM,EAAEmB,OAAO,EAAE;EAC/BZ,SAAS,IACLrB,0BAA0B,CAAC2C,QAAQ,EAAE,2DAA2D,GAC5F,oGAAoG,CAAC;EAC7G,MAAMC,eAAe,GAAG,CAACX,OAAO,EAAEM,aAAa;EAC/CK,eAAe,IAAI,CAACX,OAAO,EAAEC,QAAQ,IAAI1C,wBAAwB,CAACmD,QAAQ,CAAC;EAC3E,MAAME,UAAU,GAAGD,eAAe,GAAGX,OAAO,EAAEC,QAAQ,EAAEM,GAAG,CAAC9C,UAAU,CAAC,IAAID,MAAM,CAACC,UAAU,CAAC,GAAG,IAAI;EACpG;EACA;EACA,IAAIoD,KAAK;EACT,IAAIb,OAAO,EAAEc,WAAW,EAAE;IACtB;IACAD,KAAK,GAAG7C,MAAM,CAAC;MAAE+C,IAAI,EAAE,CAAC,CAAC;IAAwB,CAAC,CAAC;EACvD,CAAC,MACI;IACD;IACAF,KAAK,GAAG7C,MAAM,CAAC;MAAE+C,IAAI,EAAE,CAAC,CAAC;MAAuBzB,KAAK,EAAEU,OAAO,EAAEgB;IAAa,CAAC,CAAC;EACnF;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MAAMC,GAAG,GAAGpC,MAAM,CAACK,SAAS,CAAC;IACzBP,IAAI,EAAEW,KAAK,IAAIuB,KAAK,CAACK,GAAG,CAAC;MAAEH,IAAI,EAAE,CAAC,CAAC;MAAuBzB;IAAM,CAAC,CAAC;IAClEe,KAAK,EAAEA,KAAK,IAAI;MACZ,IAAIL,OAAO,EAAEmB,YAAY,EAAE;QACvB;QACA;QACA,MAAMd,KAAK;MACf;MACAQ,KAAK,CAACK,GAAG,CAAC;QAAEH,IAAI,EAAE,CAAC,CAAC;QAAuBV;MAAM,CAAC,CAAC;IACvD;IACA;IACA;EACJ,CAAC,CAAC;EACF,IAAIjB,SAAS,IAAIY,OAAO,EAAEc,WAAW,IAAID,KAAK,CAAC,CAAC,CAACE,IAAI,KAAK,CAAC,CAAC,yBAAyB;IACjF,MAAM,IAAIrD,aAAa,CAAC,GAAG,CAAC,wDAAwD,qFAAqF,CAAC;EAC9K;EACA;EACAkD,UAAU,EAAElC,SAAS,CAACuC,GAAG,CAAC1B,WAAW,CAACX,IAAI,CAACqC,GAAG,CAAC,CAAC;EAChD;EACA;EACA,OAAOhD,QAAQ,CAAC,MAAM;IAClB,MAAMmD,OAAO,GAAGP,KAAK,CAAC,CAAC;IACvB,QAAQO,OAAO,CAACL,IAAI;MAChB,KAAK,CAAC,CAAC;QACH,OAAOK,OAAO,CAAC9B,KAAK;MACxB,KAAK,CAAC,CAAC;QACH,MAAM8B,OAAO,CAACf,KAAK;MACvB,KAAK,CAAC,CAAC;QACH;QACA;QACA,MAAM,IAAI3C,aAAa,CAAC,GAAG,CAAC,wDAAwD,qFAAqF,CAAC;IAClL;EACJ,CAAC,CAAC;AACN;;AAEA;AACA;AACA;;AAEA,SAAS8B,oBAAoB,EAAEG,kBAAkB,EAAEtB,kBAAkB,EAAE0B,YAAY,EAAEW,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}