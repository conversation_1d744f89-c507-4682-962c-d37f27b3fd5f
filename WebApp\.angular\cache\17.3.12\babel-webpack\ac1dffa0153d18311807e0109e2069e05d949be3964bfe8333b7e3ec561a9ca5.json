{"ast": null, "code": "import Quill from './core.js';\nimport { AlignClass, AlignStyle } from './formats/align.js';\nimport { DirectionAttribute, DirectionClass, DirectionStyle } from './formats/direction.js';\nimport Indent from './formats/indent.js';\nimport Blockquote from './formats/blockquote.js';\nimport Header from './formats/header.js';\nimport List from './formats/list.js';\nimport { BackgroundClass, BackgroundStyle } from './formats/background.js';\nimport { ColorClass, ColorStyle } from './formats/color.js';\nimport { FontClass, FontStyle } from './formats/font.js';\nimport { SizeClass, SizeStyle } from './formats/size.js';\nimport Bold from './formats/bold.js';\nimport Italic from './formats/italic.js';\nimport Link from './formats/link.js';\nimport Script from './formats/script.js';\nimport Strike from './formats/strike.js';\nimport Underline from './formats/underline.js';\nimport Formula from './formats/formula.js';\nimport Image from './formats/image.js';\nimport Video from './formats/video.js';\nimport CodeBlock, { Code as InlineCode } from './formats/code.js';\nimport Syntax from './modules/syntax.js';\nimport Table from './modules/table.js';\nimport Toolbar from './modules/toolbar.js';\nimport Icons from './ui/icons.js';\nimport Picker from './ui/picker.js';\nimport ColorPicker from './ui/color-picker.js';\nimport IconPicker from './ui/icon-picker.js';\nimport Tooltip from './ui/tooltip.js';\nimport BubbleTheme from './themes/bubble.js';\nimport SnowTheme from './themes/snow.js';\nQuill.register({\n  'attributors/attribute/direction': DirectionAttribute,\n  'attributors/class/align': AlignClass,\n  'attributors/class/background': BackgroundClass,\n  'attributors/class/color': ColorClass,\n  'attributors/class/direction': DirectionClass,\n  'attributors/class/font': FontClass,\n  'attributors/class/size': SizeClass,\n  'attributors/style/align': AlignStyle,\n  'attributors/style/background': BackgroundStyle,\n  'attributors/style/color': ColorStyle,\n  'attributors/style/direction': DirectionStyle,\n  'attributors/style/font': FontStyle,\n  'attributors/style/size': SizeStyle\n}, true);\nQuill.register({\n  'formats/align': AlignClass,\n  'formats/direction': DirectionClass,\n  'formats/indent': Indent,\n  'formats/background': BackgroundStyle,\n  'formats/color': ColorStyle,\n  'formats/font': FontClass,\n  'formats/size': SizeClass,\n  'formats/blockquote': Blockquote,\n  'formats/code-block': CodeBlock,\n  'formats/header': Header,\n  'formats/list': List,\n  'formats/bold': Bold,\n  'formats/code': InlineCode,\n  'formats/italic': Italic,\n  'formats/link': Link,\n  'formats/script': Script,\n  'formats/strike': Strike,\n  'formats/underline': Underline,\n  'formats/formula': Formula,\n  'formats/image': Image,\n  'formats/video': Video,\n  'modules/syntax': Syntax,\n  'modules/table': Table,\n  'modules/toolbar': Toolbar,\n  'themes/bubble': BubbleTheme,\n  'themes/snow': SnowTheme,\n  'ui/icons': Icons,\n  'ui/picker': Picker,\n  'ui/icon-picker': IconPicker,\n  'ui/color-picker': ColorPicker,\n  'ui/tooltip': Tooltip\n}, true);\nexport { AttributeMap, Delta, Module, Op, OpIterator, Parchment, Range } from './core.js';\nexport default Quill;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "AlignClass", "AlignStyle", "DirectionAttribute", "DirectionClass", "DirectionStyle", "Indent", "Blockquote", "Header", "List", "BackgroundClass", "BackgroundStyle", "ColorClass", "ColorStyle", "FontClass", "FontStyle", "SizeClass", "SizeStyle", "Bold", "Italic", "Link", "<PERSON><PERSON><PERSON>", "Strike", "Underline", "Formula", "Image", "Video", "CodeBlock", "Code", "InlineCode", "Syntax", "Table", "<PERSON><PERSON><PERSON>", "Icons", "Picker", "ColorPicker", "IconPicker", "<PERSON><PERSON><PERSON>", "BubbleTheme", "SnowTheme", "register", "AttributeMap", "Delta", "<PERSON><PERSON><PERSON>", "Op", "OpIterator", "Parchment", "Range"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/quill/quill.js"], "sourcesContent": ["import Quill from './core.js';\nimport { AlignClass, AlignStyle } from './formats/align.js';\nimport { DirectionAttribute, DirectionClass, DirectionStyle } from './formats/direction.js';\nimport Indent from './formats/indent.js';\nimport Blockquote from './formats/blockquote.js';\nimport Header from './formats/header.js';\nimport List from './formats/list.js';\nimport { BackgroundClass, BackgroundStyle } from './formats/background.js';\nimport { ColorClass, ColorStyle } from './formats/color.js';\nimport { FontClass, FontStyle } from './formats/font.js';\nimport { SizeClass, SizeStyle } from './formats/size.js';\nimport Bold from './formats/bold.js';\nimport Italic from './formats/italic.js';\nimport Link from './formats/link.js';\nimport Script from './formats/script.js';\nimport Strike from './formats/strike.js';\nimport Underline from './formats/underline.js';\nimport Formula from './formats/formula.js';\nimport Image from './formats/image.js';\nimport Video from './formats/video.js';\nimport CodeBlock, { Code as InlineCode } from './formats/code.js';\nimport Syntax from './modules/syntax.js';\nimport Table from './modules/table.js';\nimport Toolbar from './modules/toolbar.js';\nimport Icons from './ui/icons.js';\nimport Picker from './ui/picker.js';\nimport ColorPicker from './ui/color-picker.js';\nimport IconPicker from './ui/icon-picker.js';\nimport Tooltip from './ui/tooltip.js';\nimport BubbleTheme from './themes/bubble.js';\nimport SnowTheme from './themes/snow.js';\nQuill.register({\n  'attributors/attribute/direction': DirectionAttribute,\n  'attributors/class/align': AlignClass,\n  'attributors/class/background': BackgroundClass,\n  'attributors/class/color': ColorClass,\n  'attributors/class/direction': DirectionClass,\n  'attributors/class/font': FontClass,\n  'attributors/class/size': SizeClass,\n  'attributors/style/align': AlignStyle,\n  'attributors/style/background': BackgroundStyle,\n  'attributors/style/color': ColorStyle,\n  'attributors/style/direction': DirectionStyle,\n  'attributors/style/font': FontStyle,\n  'attributors/style/size': SizeStyle\n}, true);\nQuill.register({\n  'formats/align': AlignClass,\n  'formats/direction': DirectionClass,\n  'formats/indent': Indent,\n  'formats/background': BackgroundStyle,\n  'formats/color': ColorStyle,\n  'formats/font': FontClass,\n  'formats/size': SizeClass,\n  'formats/blockquote': Blockquote,\n  'formats/code-block': CodeBlock,\n  'formats/header': Header,\n  'formats/list': List,\n  'formats/bold': Bold,\n  'formats/code': InlineCode,\n  'formats/italic': Italic,\n  'formats/link': Link,\n  'formats/script': Script,\n  'formats/strike': Strike,\n  'formats/underline': Underline,\n  'formats/formula': Formula,\n  'formats/image': Image,\n  'formats/video': Video,\n  'modules/syntax': Syntax,\n  'modules/table': Table,\n  'modules/toolbar': Toolbar,\n  'themes/bubble': BubbleTheme,\n  'themes/snow': SnowTheme,\n  'ui/icons': Icons,\n  'ui/picker': Picker,\n  'ui/icon-picker': IconPicker,\n  'ui/color-picker': ColorPicker,\n  'ui/tooltip': Tooltip\n}, true);\nexport { AttributeMap, Delta, Module, Op, OpIterator, Parchment, Range } from './core.js';\nexport default Quill;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,WAAW;AAC7B,SAASC,UAAU,EAAEC,UAAU,QAAQ,oBAAoB;AAC3D,SAASC,kBAAkB,EAAEC,cAAc,EAAEC,cAAc,QAAQ,wBAAwB;AAC3F,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,IAAI,MAAM,mBAAmB;AACpC,SAASC,eAAe,EAAEC,eAAe,QAAQ,yBAAyB;AAC1E,SAASC,UAAU,EAAEC,UAAU,QAAQ,oBAAoB;AAC3D,SAASC,SAAS,EAAEC,SAAS,QAAQ,mBAAmB;AACxD,SAASC,SAAS,EAAEC,SAAS,QAAQ,mBAAmB;AACxD,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,KAAK,MAAM,oBAAoB;AACtC,OAAOC,KAAK,MAAM,oBAAoB;AACtC,OAAOC,SAAS,IAAIC,IAAI,IAAIC,UAAU,QAAQ,mBAAmB;AACjE,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,KAAK,MAAM,oBAAoB;AACtC,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,MAAM,MAAM,gBAAgB;AACnC,OAAOC,WAAW,MAAM,sBAAsB;AAC9C,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,WAAW,MAAM,oBAAoB;AAC5C,OAAOC,SAAS,MAAM,kBAAkB;AACxCvC,KAAK,CAACwC,QAAQ,CAAC;EACb,iCAAiC,EAAErC,kBAAkB;EACrD,yBAAyB,EAAEF,UAAU;EACrC,8BAA8B,EAAES,eAAe;EAC/C,yBAAyB,EAAEE,UAAU;EACrC,6BAA6B,EAAER,cAAc;EAC7C,wBAAwB,EAAEU,SAAS;EACnC,wBAAwB,EAAEE,SAAS;EACnC,yBAAyB,EAAEd,UAAU;EACrC,8BAA8B,EAAES,eAAe;EAC/C,yBAAyB,EAAEE,UAAU;EACrC,6BAA6B,EAAER,cAAc;EAC7C,wBAAwB,EAAEU,SAAS;EACnC,wBAAwB,EAAEE;AAC5B,CAAC,EAAE,IAAI,CAAC;AACRjB,KAAK,CAACwC,QAAQ,CAAC;EACb,eAAe,EAAEvC,UAAU;EAC3B,mBAAmB,EAAEG,cAAc;EACnC,gBAAgB,EAAEE,MAAM;EACxB,oBAAoB,EAAEK,eAAe;EACrC,eAAe,EAAEE,UAAU;EAC3B,cAAc,EAAEC,SAAS;EACzB,cAAc,EAAEE,SAAS;EACzB,oBAAoB,EAAET,UAAU;EAChC,oBAAoB,EAAEoB,SAAS;EAC/B,gBAAgB,EAAEnB,MAAM;EACxB,cAAc,EAAEC,IAAI;EACpB,cAAc,EAAES,IAAI;EACpB,cAAc,EAAEW,UAAU;EAC1B,gBAAgB,EAAEV,MAAM;EACxB,cAAc,EAAEC,IAAI;EACpB,gBAAgB,EAAEC,MAAM;EACxB,gBAAgB,EAAEC,MAAM;EACxB,mBAAmB,EAAEC,SAAS;EAC9B,iBAAiB,EAAEC,OAAO;EAC1B,eAAe,EAAEC,KAAK;EACtB,eAAe,EAAEC,KAAK;EACtB,gBAAgB,EAAEI,MAAM;EACxB,eAAe,EAAEC,KAAK;EACtB,iBAAiB,EAAEC,OAAO;EAC1B,eAAe,EAAEM,WAAW;EAC5B,aAAa,EAAEC,SAAS;EACxB,UAAU,EAAEN,KAAK;EACjB,WAAW,EAAEC,MAAM;EACnB,gBAAgB,EAAEE,UAAU;EAC5B,iBAAiB,EAAED,WAAW;EAC9B,YAAY,EAAEE;AAChB,CAAC,EAAE,IAAI,CAAC;AACR,SAASI,YAAY,EAAEC,KAAK,EAAEC,MAAM,EAAEC,EAAE,EAAEC,UAAU,EAAEC,SAAS,EAAEC,KAAK,QAAQ,WAAW;AACzF,eAAe/C,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}