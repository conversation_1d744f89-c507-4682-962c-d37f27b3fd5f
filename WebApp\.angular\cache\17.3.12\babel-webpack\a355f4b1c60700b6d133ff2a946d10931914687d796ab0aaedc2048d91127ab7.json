{"ast": null, "code": "import { merge } from 'lodash-es';\nimport Emitter from '../core/emitter.js';\nimport Theme from '../core/theme.js';\nimport ColorPicker from '../ui/color-picker.js';\nimport IconPicker from '../ui/icon-picker.js';\nimport Picker from '../ui/picker.js';\nimport Tooltip from '../ui/tooltip.js';\nconst ALIGNS = [false, 'center', 'right', 'justify'];\nconst COLORS = ['#000000', '#e60000', '#ff9900', '#ffff00', '#008a00', '#0066cc', '#9933ff', '#ffffff', '#facccc', '#ffebcc', '#ffffcc', '#cce8cc', '#cce0f5', '#ebd6ff', '#bbbbbb', '#f06666', '#ffc266', '#ffff66', '#66b966', '#66a3e0', '#c285ff', '#888888', '#a10000', '#b26b00', '#b2b200', '#006100', '#0047b2', '#6b24b2', '#444444', '#5c0000', '#663d00', '#666600', '#003700', '#002966', '#3d1466'];\nconst FONTS = [false, 'serif', 'monospace'];\nconst HEADERS = ['1', '2', '3', false];\nconst SIZES = ['small', false, 'large', 'huge'];\nclass BaseTheme extends Theme {\n  constructor(quill, options) {\n    super(quill, options);\n    const listener = e => {\n      if (!document.body.contains(quill.root)) {\n        document.body.removeEventListener('click', listener);\n        return;\n      }\n      if (this.tooltip != null &&\n      // @ts-expect-error\n      !this.tooltip.root.contains(e.target) &&\n      // @ts-expect-error\n      document.activeElement !== this.tooltip.textbox && !this.quill.hasFocus()) {\n        this.tooltip.hide();\n      }\n      if (this.pickers != null) {\n        this.pickers.forEach(picker => {\n          // @ts-expect-error\n          if (!picker.container.contains(e.target)) {\n            picker.close();\n          }\n        });\n      }\n    };\n    quill.emitter.listenDOM('click', document.body, listener);\n  }\n  addModule(name) {\n    const module = super.addModule(name);\n    if (name === 'toolbar') {\n      // @ts-expect-error\n      this.extendToolbar(module);\n    }\n    return module;\n  }\n  buildButtons(buttons, icons) {\n    Array.from(buttons).forEach(button => {\n      const className = button.getAttribute('class') || '';\n      className.split(/\\s+/).forEach(name => {\n        if (!name.startsWith('ql-')) return;\n        name = name.slice('ql-'.length);\n        if (icons[name] == null) return;\n        if (name === 'direction') {\n          // @ts-expect-error\n          button.innerHTML = icons[name][''] + icons[name].rtl;\n        } else if (typeof icons[name] === 'string') {\n          // @ts-expect-error\n          button.innerHTML = icons[name];\n        } else {\n          // @ts-expect-error\n          const value = button.value || '';\n          // @ts-expect-error\n          if (value != null && icons[name][value]) {\n            // @ts-expect-error\n            button.innerHTML = icons[name][value];\n          }\n        }\n      });\n    });\n  }\n  buildPickers(selects, icons) {\n    this.pickers = Array.from(selects).map(select => {\n      if (select.classList.contains('ql-align')) {\n        if (select.querySelector('option') == null) {\n          fillSelect(select, ALIGNS);\n        }\n        if (typeof icons.align === 'object') {\n          return new IconPicker(select, icons.align);\n        }\n      }\n      if (select.classList.contains('ql-background') || select.classList.contains('ql-color')) {\n        const format = select.classList.contains('ql-background') ? 'background' : 'color';\n        if (select.querySelector('option') == null) {\n          fillSelect(select, COLORS, format === 'background' ? '#ffffff' : '#000000');\n        }\n        return new ColorPicker(select, icons[format]);\n      }\n      if (select.querySelector('option') == null) {\n        if (select.classList.contains('ql-font')) {\n          fillSelect(select, FONTS);\n        } else if (select.classList.contains('ql-header')) {\n          fillSelect(select, HEADERS);\n        } else if (select.classList.contains('ql-size')) {\n          fillSelect(select, SIZES);\n        }\n      }\n      return new Picker(select);\n    });\n    const update = () => {\n      this.pickers.forEach(picker => {\n        picker.update();\n      });\n    };\n    this.quill.on(Emitter.events.EDITOR_CHANGE, update);\n  }\n}\nBaseTheme.DEFAULTS = merge({}, Theme.DEFAULTS, {\n  modules: {\n    toolbar: {\n      handlers: {\n        formula() {\n          this.quill.theme.tooltip.edit('formula');\n        },\n        image() {\n          let fileInput = this.container.querySelector('input.ql-image[type=file]');\n          if (fileInput == null) {\n            fileInput = document.createElement('input');\n            fileInput.setAttribute('type', 'file');\n            fileInput.setAttribute('accept', this.quill.uploader.options.mimetypes.join(', '));\n            fileInput.classList.add('ql-image');\n            fileInput.addEventListener('change', () => {\n              const range = this.quill.getSelection(true);\n              this.quill.uploader.upload(range, fileInput.files);\n              fileInput.value = '';\n            });\n            this.container.appendChild(fileInput);\n          }\n          fileInput.click();\n        },\n        video() {\n          this.quill.theme.tooltip.edit('video');\n        }\n      }\n    }\n  }\n});\nclass BaseTooltip extends Tooltip {\n  constructor(quill, boundsContainer) {\n    super(quill, boundsContainer);\n    this.textbox = this.root.querySelector('input[type=\"text\"]');\n    this.listen();\n  }\n  listen() {\n    // @ts-expect-error Fix me later\n    this.textbox.addEventListener('keydown', event => {\n      if (event.key === 'Enter') {\n        this.save();\n        event.preventDefault();\n      } else if (event.key === 'Escape') {\n        this.cancel();\n        event.preventDefault();\n      }\n    });\n  }\n  cancel() {\n    this.hide();\n    this.restoreFocus();\n  }\n  edit() {\n    let mode = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'link';\n    let preview = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n    this.root.classList.remove('ql-hidden');\n    this.root.classList.add('ql-editing');\n    if (this.textbox == null) return;\n    if (preview != null) {\n      this.textbox.value = preview;\n    } else if (mode !== this.root.getAttribute('data-mode')) {\n      this.textbox.value = '';\n    }\n    const bounds = this.quill.getBounds(this.quill.selection.savedRange);\n    if (bounds != null) {\n      this.position(bounds);\n    }\n    this.textbox.select();\n    this.textbox.setAttribute('placeholder', this.textbox.getAttribute(`data-${mode}`) || '');\n    this.root.setAttribute('data-mode', mode);\n  }\n  restoreFocus() {\n    this.quill.focus({\n      preventScroll: true\n    });\n  }\n  save() {\n    // @ts-expect-error Fix me later\n    let {\n      value\n    } = this.textbox;\n    switch (this.root.getAttribute('data-mode')) {\n      case 'link':\n        {\n          const {\n            scrollTop\n          } = this.quill.root;\n          if (this.linkRange) {\n            this.quill.formatText(this.linkRange, 'link', value, Emitter.sources.USER);\n            delete this.linkRange;\n          } else {\n            this.restoreFocus();\n            this.quill.format('link', value, Emitter.sources.USER);\n          }\n          this.quill.root.scrollTop = scrollTop;\n          break;\n        }\n      case 'video':\n        {\n          value = extractVideoUrl(value);\n        }\n      // eslint-disable-next-line no-fallthrough\n      case 'formula':\n        {\n          if (!value) break;\n          const range = this.quill.getSelection(true);\n          if (range != null) {\n            const index = range.index + range.length;\n            this.quill.insertEmbed(index,\n            // @ts-expect-error Fix me later\n            this.root.getAttribute('data-mode'), value, Emitter.sources.USER);\n            if (this.root.getAttribute('data-mode') === 'formula') {\n              this.quill.insertText(index + 1, ' ', Emitter.sources.USER);\n            }\n            this.quill.setSelection(index + 2, Emitter.sources.USER);\n          }\n          break;\n        }\n      default:\n    }\n    // @ts-expect-error Fix me later\n    this.textbox.value = '';\n    this.hide();\n  }\n}\nfunction extractVideoUrl(url) {\n  let match = url.match(/^(?:(https?):\\/\\/)?(?:(?:www|m)\\.)?youtube\\.com\\/watch.*v=([a-zA-Z0-9_-]+)/) || url.match(/^(?:(https?):\\/\\/)?(?:(?:www|m)\\.)?youtu\\.be\\/([a-zA-Z0-9_-]+)/);\n  if (match) {\n    return `${match[1] || 'https'}://www.youtube.com/embed/${match[2]}?showinfo=0`;\n  }\n  // eslint-disable-next-line no-cond-assign\n  if (match = url.match(/^(?:(https?):\\/\\/)?(?:www\\.)?vimeo\\.com\\/(\\d+)/)) {\n    return `${match[1] || 'https'}://player.vimeo.com/video/${match[2]}/`;\n  }\n  return url;\n}\nfunction fillSelect(select, values) {\n  let defaultValue = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  values.forEach(value => {\n    const option = document.createElement('option');\n    if (value === defaultValue) {\n      option.setAttribute('selected', 'selected');\n    } else {\n      option.setAttribute('value', String(value));\n    }\n    select.appendChild(option);\n  });\n}\nexport { BaseTooltip, BaseTheme as default };", "map": {"version": 3, "names": ["merge", "Emitter", "Theme", "ColorPicker", "IconPicker", "Picker", "<PERSON><PERSON><PERSON>", "ALIGNS", "COLORS", "FONTS", "HEADERS", "SIZES", "BaseTheme", "constructor", "quill", "options", "listener", "e", "document", "body", "contains", "root", "removeEventListener", "tooltip", "target", "activeElement", "textbox", "hasFocus", "hide", "pickers", "for<PERSON>ach", "picker", "container", "close", "emitter", "listenDOM", "addModule", "name", "module", "extendToolbar", "buildButtons", "buttons", "icons", "Array", "from", "button", "className", "getAttribute", "split", "startsWith", "slice", "length", "innerHTML", "rtl", "value", "buildPickers", "selects", "map", "select", "classList", "querySelector", "fillSelect", "align", "format", "update", "on", "events", "EDITOR_CHANGE", "DEFAULTS", "modules", "toolbar", "handlers", "formula", "theme", "edit", "image", "fileInput", "createElement", "setAttribute", "uploader", "mimetypes", "join", "add", "addEventListener", "range", "getSelection", "upload", "files", "append<PERSON><PERSON><PERSON>", "click", "video", "BaseTooltip", "boundsContainer", "listen", "event", "key", "save", "preventDefault", "cancel", "restoreFocus", "mode", "arguments", "undefined", "preview", "remove", "bounds", "getBounds", "selection", "savedRange", "position", "focus", "preventScroll", "scrollTop", "linkRange", "formatText", "sources", "USER", "extractVideoUrl", "index", "insertEmbed", "insertText", "setSelection", "url", "match", "values", "defaultValue", "option", "String", "default"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/quill/themes/base.js"], "sourcesContent": ["import { merge } from 'lodash-es';\nimport Emitter from '../core/emitter.js';\nimport Theme from '../core/theme.js';\nimport ColorPicker from '../ui/color-picker.js';\nimport IconPicker from '../ui/icon-picker.js';\nimport Picker from '../ui/picker.js';\nimport Tooltip from '../ui/tooltip.js';\nconst ALIGNS = [false, 'center', 'right', 'justify'];\nconst COLORS = ['#000000', '#e60000', '#ff9900', '#ffff00', '#008a00', '#0066cc', '#9933ff', '#ffffff', '#facccc', '#ffebcc', '#ffffcc', '#cce8cc', '#cce0f5', '#ebd6ff', '#bbbbbb', '#f06666', '#ffc266', '#ffff66', '#66b966', '#66a3e0', '#c285ff', '#888888', '#a10000', '#b26b00', '#b2b200', '#006100', '#0047b2', '#6b24b2', '#444444', '#5c0000', '#663d00', '#666600', '#003700', '#002966', '#3d1466'];\nconst FONTS = [false, 'serif', 'monospace'];\nconst HEADERS = ['1', '2', '3', false];\nconst SIZES = ['small', false, 'large', 'huge'];\nclass BaseTheme extends Theme {\n  constructor(quill, options) {\n    super(quill, options);\n    const listener = e => {\n      if (!document.body.contains(quill.root)) {\n        document.body.removeEventListener('click', listener);\n        return;\n      }\n      if (this.tooltip != null &&\n      // @ts-expect-error\n      !this.tooltip.root.contains(e.target) &&\n      // @ts-expect-error\n      document.activeElement !== this.tooltip.textbox && !this.quill.hasFocus()) {\n        this.tooltip.hide();\n      }\n      if (this.pickers != null) {\n        this.pickers.forEach(picker => {\n          // @ts-expect-error\n          if (!picker.container.contains(e.target)) {\n            picker.close();\n          }\n        });\n      }\n    };\n    quill.emitter.listenDOM('click', document.body, listener);\n  }\n  addModule(name) {\n    const module = super.addModule(name);\n    if (name === 'toolbar') {\n      // @ts-expect-error\n      this.extendToolbar(module);\n    }\n    return module;\n  }\n  buildButtons(buttons, icons) {\n    Array.from(buttons).forEach(button => {\n      const className = button.getAttribute('class') || '';\n      className.split(/\\s+/).forEach(name => {\n        if (!name.startsWith('ql-')) return;\n        name = name.slice('ql-'.length);\n        if (icons[name] == null) return;\n        if (name === 'direction') {\n          // @ts-expect-error\n          button.innerHTML = icons[name][''] + icons[name].rtl;\n        } else if (typeof icons[name] === 'string') {\n          // @ts-expect-error\n          button.innerHTML = icons[name];\n        } else {\n          // @ts-expect-error\n          const value = button.value || '';\n          // @ts-expect-error\n          if (value != null && icons[name][value]) {\n            // @ts-expect-error\n            button.innerHTML = icons[name][value];\n          }\n        }\n      });\n    });\n  }\n  buildPickers(selects, icons) {\n    this.pickers = Array.from(selects).map(select => {\n      if (select.classList.contains('ql-align')) {\n        if (select.querySelector('option') == null) {\n          fillSelect(select, ALIGNS);\n        }\n        if (typeof icons.align === 'object') {\n          return new IconPicker(select, icons.align);\n        }\n      }\n      if (select.classList.contains('ql-background') || select.classList.contains('ql-color')) {\n        const format = select.classList.contains('ql-background') ? 'background' : 'color';\n        if (select.querySelector('option') == null) {\n          fillSelect(select, COLORS, format === 'background' ? '#ffffff' : '#000000');\n        }\n        return new ColorPicker(select, icons[format]);\n      }\n      if (select.querySelector('option') == null) {\n        if (select.classList.contains('ql-font')) {\n          fillSelect(select, FONTS);\n        } else if (select.classList.contains('ql-header')) {\n          fillSelect(select, HEADERS);\n        } else if (select.classList.contains('ql-size')) {\n          fillSelect(select, SIZES);\n        }\n      }\n      return new Picker(select);\n    });\n    const update = () => {\n      this.pickers.forEach(picker => {\n        picker.update();\n      });\n    };\n    this.quill.on(Emitter.events.EDITOR_CHANGE, update);\n  }\n}\nBaseTheme.DEFAULTS = merge({}, Theme.DEFAULTS, {\n  modules: {\n    toolbar: {\n      handlers: {\n        formula() {\n          this.quill.theme.tooltip.edit('formula');\n        },\n        image() {\n          let fileInput = this.container.querySelector('input.ql-image[type=file]');\n          if (fileInput == null) {\n            fileInput = document.createElement('input');\n            fileInput.setAttribute('type', 'file');\n            fileInput.setAttribute('accept', this.quill.uploader.options.mimetypes.join(', '));\n            fileInput.classList.add('ql-image');\n            fileInput.addEventListener('change', () => {\n              const range = this.quill.getSelection(true);\n              this.quill.uploader.upload(range, fileInput.files);\n              fileInput.value = '';\n            });\n            this.container.appendChild(fileInput);\n          }\n          fileInput.click();\n        },\n        video() {\n          this.quill.theme.tooltip.edit('video');\n        }\n      }\n    }\n  }\n});\nclass BaseTooltip extends Tooltip {\n  constructor(quill, boundsContainer) {\n    super(quill, boundsContainer);\n    this.textbox = this.root.querySelector('input[type=\"text\"]');\n    this.listen();\n  }\n  listen() {\n    // @ts-expect-error Fix me later\n    this.textbox.addEventListener('keydown', event => {\n      if (event.key === 'Enter') {\n        this.save();\n        event.preventDefault();\n      } else if (event.key === 'Escape') {\n        this.cancel();\n        event.preventDefault();\n      }\n    });\n  }\n  cancel() {\n    this.hide();\n    this.restoreFocus();\n  }\n  edit() {\n    let mode = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'link';\n    let preview = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n    this.root.classList.remove('ql-hidden');\n    this.root.classList.add('ql-editing');\n    if (this.textbox == null) return;\n    if (preview != null) {\n      this.textbox.value = preview;\n    } else if (mode !== this.root.getAttribute('data-mode')) {\n      this.textbox.value = '';\n    }\n    const bounds = this.quill.getBounds(this.quill.selection.savedRange);\n    if (bounds != null) {\n      this.position(bounds);\n    }\n    this.textbox.select();\n    this.textbox.setAttribute('placeholder', this.textbox.getAttribute(`data-${mode}`) || '');\n    this.root.setAttribute('data-mode', mode);\n  }\n  restoreFocus() {\n    this.quill.focus({\n      preventScroll: true\n    });\n  }\n  save() {\n    // @ts-expect-error Fix me later\n    let {\n      value\n    } = this.textbox;\n    switch (this.root.getAttribute('data-mode')) {\n      case 'link':\n        {\n          const {\n            scrollTop\n          } = this.quill.root;\n          if (this.linkRange) {\n            this.quill.formatText(this.linkRange, 'link', value, Emitter.sources.USER);\n            delete this.linkRange;\n          } else {\n            this.restoreFocus();\n            this.quill.format('link', value, Emitter.sources.USER);\n          }\n          this.quill.root.scrollTop = scrollTop;\n          break;\n        }\n      case 'video':\n        {\n          value = extractVideoUrl(value);\n        }\n      // eslint-disable-next-line no-fallthrough\n      case 'formula':\n        {\n          if (!value) break;\n          const range = this.quill.getSelection(true);\n          if (range != null) {\n            const index = range.index + range.length;\n            this.quill.insertEmbed(index,\n            // @ts-expect-error Fix me later\n            this.root.getAttribute('data-mode'), value, Emitter.sources.USER);\n            if (this.root.getAttribute('data-mode') === 'formula') {\n              this.quill.insertText(index + 1, ' ', Emitter.sources.USER);\n            }\n            this.quill.setSelection(index + 2, Emitter.sources.USER);\n          }\n          break;\n        }\n      default:\n    }\n    // @ts-expect-error Fix me later\n    this.textbox.value = '';\n    this.hide();\n  }\n}\nfunction extractVideoUrl(url) {\n  let match = url.match(/^(?:(https?):\\/\\/)?(?:(?:www|m)\\.)?youtube\\.com\\/watch.*v=([a-zA-Z0-9_-]+)/) || url.match(/^(?:(https?):\\/\\/)?(?:(?:www|m)\\.)?youtu\\.be\\/([a-zA-Z0-9_-]+)/);\n  if (match) {\n    return `${match[1] || 'https'}://www.youtube.com/embed/${match[2]}?showinfo=0`;\n  }\n  // eslint-disable-next-line no-cond-assign\n  if (match = url.match(/^(?:(https?):\\/\\/)?(?:www\\.)?vimeo\\.com\\/(\\d+)/)) {\n    return `${match[1] || 'https'}://player.vimeo.com/video/${match[2]}/`;\n  }\n  return url;\n}\nfunction fillSelect(select, values) {\n  let defaultValue = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  values.forEach(value => {\n    const option = document.createElement('option');\n    if (value === defaultValue) {\n      option.setAttribute('selected', 'selected');\n    } else {\n      option.setAttribute('value', String(value));\n    }\n    select.appendChild(option);\n  });\n}\nexport { BaseTooltip, BaseTheme as default };\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,WAAW;AACjC,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,KAAK,MAAM,kBAAkB;AACpC,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,OAAOC,UAAU,MAAM,sBAAsB;AAC7C,OAAOC,MAAM,MAAM,iBAAiB;AACpC,OAAOC,OAAO,MAAM,kBAAkB;AACtC,MAAMC,MAAM,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC;AACpD,MAAMC,MAAM,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;AAChZ,MAAMC,KAAK,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,WAAW,CAAC;AAC3C,MAAMC,OAAO,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,CAAC;AACtC,MAAMC,KAAK,GAAG,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC;AAC/C,MAAMC,SAAS,SAASV,KAAK,CAAC;EAC5BW,WAAWA,CAACC,KAAK,EAAEC,OAAO,EAAE;IAC1B,KAAK,CAACD,KAAK,EAAEC,OAAO,CAAC;IACrB,MAAMC,QAAQ,GAAGC,CAAC,IAAI;MACpB,IAAI,CAACC,QAAQ,CAACC,IAAI,CAACC,QAAQ,CAACN,KAAK,CAACO,IAAI,CAAC,EAAE;QACvCH,QAAQ,CAACC,IAAI,CAACG,mBAAmB,CAAC,OAAO,EAAEN,QAAQ,CAAC;QACpD;MACF;MACA,IAAI,IAAI,CAACO,OAAO,IAAI,IAAI;MACxB;MACA,CAAC,IAAI,CAACA,OAAO,CAACF,IAAI,CAACD,QAAQ,CAACH,CAAC,CAACO,MAAM,CAAC;MACrC;MACAN,QAAQ,CAACO,aAAa,KAAK,IAAI,CAACF,OAAO,CAACG,OAAO,IAAI,CAAC,IAAI,CAACZ,KAAK,CAACa,QAAQ,CAAC,CAAC,EAAE;QACzE,IAAI,CAACJ,OAAO,CAACK,IAAI,CAAC,CAAC;MACrB;MACA,IAAI,IAAI,CAACC,OAAO,IAAI,IAAI,EAAE;QACxB,IAAI,CAACA,OAAO,CAACC,OAAO,CAACC,MAAM,IAAI;UAC7B;UACA,IAAI,CAACA,MAAM,CAACC,SAAS,CAACZ,QAAQ,CAACH,CAAC,CAACO,MAAM,CAAC,EAAE;YACxCO,MAAM,CAACE,KAAK,CAAC,CAAC;UAChB;QACF,CAAC,CAAC;MACJ;IACF,CAAC;IACDnB,KAAK,CAACoB,OAAO,CAACC,SAAS,CAAC,OAAO,EAAEjB,QAAQ,CAACC,IAAI,EAAEH,QAAQ,CAAC;EAC3D;EACAoB,SAASA,CAACC,IAAI,EAAE;IACd,MAAMC,MAAM,GAAG,KAAK,CAACF,SAAS,CAACC,IAAI,CAAC;IACpC,IAAIA,IAAI,KAAK,SAAS,EAAE;MACtB;MACA,IAAI,CAACE,aAAa,CAACD,MAAM,CAAC;IAC5B;IACA,OAAOA,MAAM;EACf;EACAE,YAAYA,CAACC,OAAO,EAAEC,KAAK,EAAE;IAC3BC,KAAK,CAACC,IAAI,CAACH,OAAO,CAAC,CAACX,OAAO,CAACe,MAAM,IAAI;MACpC,MAAMC,SAAS,GAAGD,MAAM,CAACE,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE;MACpDD,SAAS,CAACE,KAAK,CAAC,KAAK,CAAC,CAAClB,OAAO,CAACO,IAAI,IAAI;QACrC,IAAI,CAACA,IAAI,CAACY,UAAU,CAAC,KAAK,CAAC,EAAE;QAC7BZ,IAAI,GAAGA,IAAI,CAACa,KAAK,CAAC,KAAK,CAACC,MAAM,CAAC;QAC/B,IAAIT,KAAK,CAACL,IAAI,CAAC,IAAI,IAAI,EAAE;QACzB,IAAIA,IAAI,KAAK,WAAW,EAAE;UACxB;UACAQ,MAAM,CAACO,SAAS,GAAGV,KAAK,CAACL,IAAI,CAAC,CAAC,EAAE,CAAC,GAAGK,KAAK,CAACL,IAAI,CAAC,CAACgB,GAAG;QACtD,CAAC,MAAM,IAAI,OAAOX,KAAK,CAACL,IAAI,CAAC,KAAK,QAAQ,EAAE;UAC1C;UACAQ,MAAM,CAACO,SAAS,GAAGV,KAAK,CAACL,IAAI,CAAC;QAChC,CAAC,MAAM;UACL;UACA,MAAMiB,KAAK,GAAGT,MAAM,CAACS,KAAK,IAAI,EAAE;UAChC;UACA,IAAIA,KAAK,IAAI,IAAI,IAAIZ,KAAK,CAACL,IAAI,CAAC,CAACiB,KAAK,CAAC,EAAE;YACvC;YACAT,MAAM,CAACO,SAAS,GAAGV,KAAK,CAACL,IAAI,CAAC,CAACiB,KAAK,CAAC;UACvC;QACF;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EACAC,YAAYA,CAACC,OAAO,EAAEd,KAAK,EAAE;IAC3B,IAAI,CAACb,OAAO,GAAGc,KAAK,CAACC,IAAI,CAACY,OAAO,CAAC,CAACC,GAAG,CAACC,MAAM,IAAI;MAC/C,IAAIA,MAAM,CAACC,SAAS,CAACvC,QAAQ,CAAC,UAAU,CAAC,EAAE;QACzC,IAAIsC,MAAM,CAACE,aAAa,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAE;UAC1CC,UAAU,CAACH,MAAM,EAAEnD,MAAM,CAAC;QAC5B;QACA,IAAI,OAAOmC,KAAK,CAACoB,KAAK,KAAK,QAAQ,EAAE;UACnC,OAAO,IAAI1D,UAAU,CAACsD,MAAM,EAAEhB,KAAK,CAACoB,KAAK,CAAC;QAC5C;MACF;MACA,IAAIJ,MAAM,CAACC,SAAS,CAACvC,QAAQ,CAAC,eAAe,CAAC,IAAIsC,MAAM,CAACC,SAAS,CAACvC,QAAQ,CAAC,UAAU,CAAC,EAAE;QACvF,MAAM2C,MAAM,GAAGL,MAAM,CAACC,SAAS,CAACvC,QAAQ,CAAC,eAAe,CAAC,GAAG,YAAY,GAAG,OAAO;QAClF,IAAIsC,MAAM,CAACE,aAAa,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAE;UAC1CC,UAAU,CAACH,MAAM,EAAElD,MAAM,EAAEuD,MAAM,KAAK,YAAY,GAAG,SAAS,GAAG,SAAS,CAAC;QAC7E;QACA,OAAO,IAAI5D,WAAW,CAACuD,MAAM,EAAEhB,KAAK,CAACqB,MAAM,CAAC,CAAC;MAC/C;MACA,IAAIL,MAAM,CAACE,aAAa,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAE;QAC1C,IAAIF,MAAM,CAACC,SAAS,CAACvC,QAAQ,CAAC,SAAS,CAAC,EAAE;UACxCyC,UAAU,CAACH,MAAM,EAAEjD,KAAK,CAAC;QAC3B,CAAC,MAAM,IAAIiD,MAAM,CAACC,SAAS,CAACvC,QAAQ,CAAC,WAAW,CAAC,EAAE;UACjDyC,UAAU,CAACH,MAAM,EAAEhD,OAAO,CAAC;QAC7B,CAAC,MAAM,IAAIgD,MAAM,CAACC,SAAS,CAACvC,QAAQ,CAAC,SAAS,CAAC,EAAE;UAC/CyC,UAAU,CAACH,MAAM,EAAE/C,KAAK,CAAC;QAC3B;MACF;MACA,OAAO,IAAIN,MAAM,CAACqD,MAAM,CAAC;IAC3B,CAAC,CAAC;IACF,MAAMM,MAAM,GAAGA,CAAA,KAAM;MACnB,IAAI,CAACnC,OAAO,CAACC,OAAO,CAACC,MAAM,IAAI;QAC7BA,MAAM,CAACiC,MAAM,CAAC,CAAC;MACjB,CAAC,CAAC;IACJ,CAAC;IACD,IAAI,CAAClD,KAAK,CAACmD,EAAE,CAAChE,OAAO,CAACiE,MAAM,CAACC,aAAa,EAAEH,MAAM,CAAC;EACrD;AACF;AACApD,SAAS,CAACwD,QAAQ,GAAGpE,KAAK,CAAC,CAAC,CAAC,EAAEE,KAAK,CAACkE,QAAQ,EAAE;EAC7CC,OAAO,EAAE;IACPC,OAAO,EAAE;MACPC,QAAQ,EAAE;QACRC,OAAOA,CAAA,EAAG;UACR,IAAI,CAAC1D,KAAK,CAAC2D,KAAK,CAAClD,OAAO,CAACmD,IAAI,CAAC,SAAS,CAAC;QAC1C,CAAC;QACDC,KAAKA,CAAA,EAAG;UACN,IAAIC,SAAS,GAAG,IAAI,CAAC5C,SAAS,CAAC4B,aAAa,CAAC,2BAA2B,CAAC;UACzE,IAAIgB,SAAS,IAAI,IAAI,EAAE;YACrBA,SAAS,GAAG1D,QAAQ,CAAC2D,aAAa,CAAC,OAAO,CAAC;YAC3CD,SAAS,CAACE,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC;YACtCF,SAAS,CAACE,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAChE,KAAK,CAACiE,QAAQ,CAAChE,OAAO,CAACiE,SAAS,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClFL,SAAS,CAACjB,SAAS,CAACuB,GAAG,CAAC,UAAU,CAAC;YACnCN,SAAS,CAACO,gBAAgB,CAAC,QAAQ,EAAE,MAAM;cACzC,MAAMC,KAAK,GAAG,IAAI,CAACtE,KAAK,CAACuE,YAAY,CAAC,IAAI,CAAC;cAC3C,IAAI,CAACvE,KAAK,CAACiE,QAAQ,CAACO,MAAM,CAACF,KAAK,EAAER,SAAS,CAACW,KAAK,CAAC;cAClDX,SAAS,CAACtB,KAAK,GAAG,EAAE;YACtB,CAAC,CAAC;YACF,IAAI,CAACtB,SAAS,CAACwD,WAAW,CAACZ,SAAS,CAAC;UACvC;UACAA,SAAS,CAACa,KAAK,CAAC,CAAC;QACnB,CAAC;QACDC,KAAKA,CAAA,EAAG;UACN,IAAI,CAAC5E,KAAK,CAAC2D,KAAK,CAAClD,OAAO,CAACmD,IAAI,CAAC,OAAO,CAAC;QACxC;MACF;IACF;EACF;AACF,CAAC,CAAC;AACF,MAAMiB,WAAW,SAASrF,OAAO,CAAC;EAChCO,WAAWA,CAACC,KAAK,EAAE8E,eAAe,EAAE;IAClC,KAAK,CAAC9E,KAAK,EAAE8E,eAAe,CAAC;IAC7B,IAAI,CAAClE,OAAO,GAAG,IAAI,CAACL,IAAI,CAACuC,aAAa,CAAC,oBAAoB,CAAC;IAC5D,IAAI,CAACiC,MAAM,CAAC,CAAC;EACf;EACAA,MAAMA,CAAA,EAAG;IACP;IACA,IAAI,CAACnE,OAAO,CAACyD,gBAAgB,CAAC,SAAS,EAAEW,KAAK,IAAI;MAChD,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,EAAE;QACzB,IAAI,CAACC,IAAI,CAAC,CAAC;QACXF,KAAK,CAACG,cAAc,CAAC,CAAC;MACxB,CAAC,MAAM,IAAIH,KAAK,CAACC,GAAG,KAAK,QAAQ,EAAE;QACjC,IAAI,CAACG,MAAM,CAAC,CAAC;QACbJ,KAAK,CAACG,cAAc,CAAC,CAAC;MACxB;IACF,CAAC,CAAC;EACJ;EACAC,MAAMA,CAAA,EAAG;IACP,IAAI,CAACtE,IAAI,CAAC,CAAC;IACX,IAAI,CAACuE,YAAY,CAAC,CAAC;EACrB;EACAzB,IAAIA,CAAA,EAAG;IACL,IAAI0B,IAAI,GAAGC,SAAS,CAAClD,MAAM,GAAG,CAAC,IAAIkD,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,MAAM;IACrF,IAAIE,OAAO,GAAGF,SAAS,CAAClD,MAAM,GAAG,CAAC,IAAIkD,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;IACtF,IAAI,CAAChF,IAAI,CAACsC,SAAS,CAAC6C,MAAM,CAAC,WAAW,CAAC;IACvC,IAAI,CAACnF,IAAI,CAACsC,SAAS,CAACuB,GAAG,CAAC,YAAY,CAAC;IACrC,IAAI,IAAI,CAACxD,OAAO,IAAI,IAAI,EAAE;IAC1B,IAAI6E,OAAO,IAAI,IAAI,EAAE;MACnB,IAAI,CAAC7E,OAAO,CAAC4B,KAAK,GAAGiD,OAAO;IAC9B,CAAC,MAAM,IAAIH,IAAI,KAAK,IAAI,CAAC/E,IAAI,CAAC0B,YAAY,CAAC,WAAW,CAAC,EAAE;MACvD,IAAI,CAACrB,OAAO,CAAC4B,KAAK,GAAG,EAAE;IACzB;IACA,MAAMmD,MAAM,GAAG,IAAI,CAAC3F,KAAK,CAAC4F,SAAS,CAAC,IAAI,CAAC5F,KAAK,CAAC6F,SAAS,CAACC,UAAU,CAAC;IACpE,IAAIH,MAAM,IAAI,IAAI,EAAE;MAClB,IAAI,CAACI,QAAQ,CAACJ,MAAM,CAAC;IACvB;IACA,IAAI,CAAC/E,OAAO,CAACgC,MAAM,CAAC,CAAC;IACrB,IAAI,CAAChC,OAAO,CAACoD,YAAY,CAAC,aAAa,EAAE,IAAI,CAACpD,OAAO,CAACqB,YAAY,CAAE,QAAOqD,IAAK,EAAC,CAAC,IAAI,EAAE,CAAC;IACzF,IAAI,CAAC/E,IAAI,CAACyD,YAAY,CAAC,WAAW,EAAEsB,IAAI,CAAC;EAC3C;EACAD,YAAYA,CAAA,EAAG;IACb,IAAI,CAACrF,KAAK,CAACgG,KAAK,CAAC;MACfC,aAAa,EAAE;IACjB,CAAC,CAAC;EACJ;EACAf,IAAIA,CAAA,EAAG;IACL;IACA,IAAI;MACF1C;IACF,CAAC,GAAG,IAAI,CAAC5B,OAAO;IAChB,QAAQ,IAAI,CAACL,IAAI,CAAC0B,YAAY,CAAC,WAAW,CAAC;MACzC,KAAK,MAAM;QACT;UACE,MAAM;YACJiE;UACF,CAAC,GAAG,IAAI,CAAClG,KAAK,CAACO,IAAI;UACnB,IAAI,IAAI,CAAC4F,SAAS,EAAE;YAClB,IAAI,CAACnG,KAAK,CAACoG,UAAU,CAAC,IAAI,CAACD,SAAS,EAAE,MAAM,EAAE3D,KAAK,EAAErD,OAAO,CAACkH,OAAO,CAACC,IAAI,CAAC;YAC1E,OAAO,IAAI,CAACH,SAAS;UACvB,CAAC,MAAM;YACL,IAAI,CAACd,YAAY,CAAC,CAAC;YACnB,IAAI,CAACrF,KAAK,CAACiD,MAAM,CAAC,MAAM,EAAET,KAAK,EAAErD,OAAO,CAACkH,OAAO,CAACC,IAAI,CAAC;UACxD;UACA,IAAI,CAACtG,KAAK,CAACO,IAAI,CAAC2F,SAAS,GAAGA,SAAS;UACrC;QACF;MACF,KAAK,OAAO;QACV;UACE1D,KAAK,GAAG+D,eAAe,CAAC/D,KAAK,CAAC;QAChC;MACF;MACA,KAAK,SAAS;QACZ;UACE,IAAI,CAACA,KAAK,EAAE;UACZ,MAAM8B,KAAK,GAAG,IAAI,CAACtE,KAAK,CAACuE,YAAY,CAAC,IAAI,CAAC;UAC3C,IAAID,KAAK,IAAI,IAAI,EAAE;YACjB,MAAMkC,KAAK,GAAGlC,KAAK,CAACkC,KAAK,GAAGlC,KAAK,CAACjC,MAAM;YACxC,IAAI,CAACrC,KAAK,CAACyG,WAAW,CAACD,KAAK;YAC5B;YACA,IAAI,CAACjG,IAAI,CAAC0B,YAAY,CAAC,WAAW,CAAC,EAAEO,KAAK,EAAErD,OAAO,CAACkH,OAAO,CAACC,IAAI,CAAC;YACjE,IAAI,IAAI,CAAC/F,IAAI,CAAC0B,YAAY,CAAC,WAAW,CAAC,KAAK,SAAS,EAAE;cACrD,IAAI,CAACjC,KAAK,CAAC0G,UAAU,CAACF,KAAK,GAAG,CAAC,EAAE,GAAG,EAAErH,OAAO,CAACkH,OAAO,CAACC,IAAI,CAAC;YAC7D;YACA,IAAI,CAACtG,KAAK,CAAC2G,YAAY,CAACH,KAAK,GAAG,CAAC,EAAErH,OAAO,CAACkH,OAAO,CAACC,IAAI,CAAC;UAC1D;UACA;QACF;MACF;IACF;IACA;IACA,IAAI,CAAC1F,OAAO,CAAC4B,KAAK,GAAG,EAAE;IACvB,IAAI,CAAC1B,IAAI,CAAC,CAAC;EACb;AACF;AACA,SAASyF,eAAeA,CAACK,GAAG,EAAE;EAC5B,IAAIC,KAAK,GAAGD,GAAG,CAACC,KAAK,CAAC,4EAA4E,CAAC,IAAID,GAAG,CAACC,KAAK,CAAC,gEAAgE,CAAC;EAClL,IAAIA,KAAK,EAAE;IACT,OAAQ,GAAEA,KAAK,CAAC,CAAC,CAAC,IAAI,OAAQ,4BAA2BA,KAAK,CAAC,CAAC,CAAE,aAAY;EAChF;EACA;EACA,IAAIA,KAAK,GAAGD,GAAG,CAACC,KAAK,CAAC,gDAAgD,CAAC,EAAE;IACvE,OAAQ,GAAEA,KAAK,CAAC,CAAC,CAAC,IAAI,OAAQ,6BAA4BA,KAAK,CAAC,CAAC,CAAE,GAAE;EACvE;EACA,OAAOD,GAAG;AACZ;AACA,SAAS7D,UAAUA,CAACH,MAAM,EAAEkE,MAAM,EAAE;EAClC,IAAIC,YAAY,GAAGxB,SAAS,CAAClD,MAAM,GAAG,CAAC,IAAIkD,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;EAC5FuB,MAAM,CAAC9F,OAAO,CAACwB,KAAK,IAAI;IACtB,MAAMwE,MAAM,GAAG5G,QAAQ,CAAC2D,aAAa,CAAC,QAAQ,CAAC;IAC/C,IAAIvB,KAAK,KAAKuE,YAAY,EAAE;MAC1BC,MAAM,CAAChD,YAAY,CAAC,UAAU,EAAE,UAAU,CAAC;IAC7C,CAAC,MAAM;MACLgD,MAAM,CAAChD,YAAY,CAAC,OAAO,EAAEiD,MAAM,CAACzE,KAAK,CAAC,CAAC;IAC7C;IACAI,MAAM,CAAC8B,WAAW,CAACsC,MAAM,CAAC;EAC5B,CAAC,CAAC;AACJ;AACA,SAASnC,WAAW,EAAE/E,SAAS,IAAIoH,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}