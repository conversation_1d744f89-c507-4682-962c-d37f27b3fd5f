{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./search-documents.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./search-documents.component.css?ngResource\";\nimport { Component, EventEmitter, Output } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { NotesService } from '../services/notes.service';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { MarkdownModule } from 'ngx-markdown';\nimport { DomSanitizer } from '@angular/platform-browser';\nlet SearchDocumentsComponent = class SearchDocumentsComponent {\n  constructor(notesService, sanitizer, route, router) {\n    this.notesService = notesService;\n    this.sanitizer = sanitizer;\n    this.route = route;\n    this.router = router;\n    this.searchQuery = '';\n    this.searchResults = [];\n    this.isLoading = false;\n    this.favoriteToggled = new EventEmitter();\n  }\n  ngOnInit() {\n    this.route.queryParams.subscribe(params => {\n      if (params['q']) {\n        this.searchQuery = params['q'];\n        this.onSearch();\n      }\n    });\n  }\n  onSearch() {\n    this.isLoading = true;\n    this.notesService.searchNotes(this.searchQuery).subscribe({\n      next: results => {\n        console.log('Search results:', results);\n        this.searchResults = results.map(note => ({\n          ...note,\n          content: this.formatContent(note.content)\n        }));\n        this.isLoading = false;\n      },\n      error: error => {\n        console.error('Error searching notes:', error);\n        this.isLoading = false;\n      }\n    });\n  }\n  formatContent(content) {\n    try {\n      const editorData = JSON.parse(content);\n      let html = '';\n      editorData.blocks.forEach(block => {\n        switch (block.type) {\n          case 'paragraph':\n            html += `<p class=\"mb-4\">${block.data.text || ''}</p>`;\n            break;\n          case 'list':\n            html += '<ul class=\"list-disc pl-6 mb-4\">';\n            block.data.items?.forEach(item => {\n              html += `<li class=\"mb-2\">${item?.content || ''}</li>`;\n            });\n            html += '</ul>';\n            break;\n          case 'link':\n            const {\n              link,\n              meta\n            } = block.data;\n            html += `\n              <div class=\"mb-4 p-4 border rounded-lg border-gray-200 dark:border-gray-700\">\n                <a href=\"${link}\" target=\"_blank\" rel=\"noopener noreferrer\"\n                   class=\"flex items-start no-underline\">\n                  ${meta?.image?.url ? `<div class=\"flex-shrink-0 mr-4\"><img src=\"${meta.image.url}\" alt=\"\" class=\"w-16 h-16 object-cover rounded\"></div>` : ''}\n                  <div class=\"flex-grow\">\n                    <h3 class=\"text-lg font-semibold text-blue-600 dark:text-blue-400 mb-1\">\n                      ${meta?.title || link}\n                    </h3>\n                    ${meta?.description ? `<p class=\"text-gray-600 dark:text-gray-300 text-sm line-clamp-2\">${meta.description}</p>` : ''}\n                    <span class=\"text-gray-500 dark:text-gray-400 text-xs\">${link ? new URL(link).hostname : ''}</span>\n                  </div>\n                </a>\n              </div>\n            `;\n            break;\n        }\n      });\n      return this.sanitizer.bypassSecurityTrustHtml(html);\n    } catch (e) {\n      console.error('Error parsing content:', e);\n      return this.sanitizer.bypassSecurityTrustHtml('<p>Error displaying content</p>');\n    }\n  }\n  toggleFavorite(note) {\n    if (!note?.id) return;\n    this.notesService.toggleFavorite(note.id).subscribe({\n      next: updatedNote => {\n        console.log(updatedNote);\n        note.isFavourite = updatedNote.isFavourite;\n        this.favoriteToggled.emit(note);\n      },\n      error: error => {\n        console.error('Error toggling favorite:', error);\n      }\n    });\n  }\n  goToNote(noteId) {\n    this.router.navigate(['student/search', noteId]);\n  }\n  getSlicedContent(note) {\n    return note?.content?.length > 50 ? note.content.slice(0, 50) + '...' : note.content;\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: NotesService\n    }, {\n      type: DomSanitizer\n    }, {\n      type: ActivatedRoute\n    }, {\n      type: Router\n    }];\n  }\n  static {\n    this.propDecorators = {\n      favoriteToggled: [{\n        type: Output\n      }]\n    };\n  }\n};\nSearchDocumentsComponent = __decorate([Component({\n  selector: 'app-search-documents',\n  template: __NG_CLI_RESOURCE__0,\n  imports: [CommonModule, FormsModule, MarkdownModule],\n  standalone: true,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SearchDocumentsComponent);\nexport { SearchDocumentsComponent };", "map": {"version": 3, "names": ["Component", "EventEmitter", "Output", "ActivatedRoute", "Router", "NotesService", "CommonModule", "FormsModule", "MarkdownModule", "Dom<PERSON><PERSON><PERSON>zer", "SearchDocumentsComponent", "constructor", "notesService", "sanitizer", "route", "router", "searchQuery", "searchResults", "isLoading", "favoriteToggled", "ngOnInit", "queryParams", "subscribe", "params", "onSearch", "searchNotes", "next", "results", "console", "log", "map", "note", "content", "formatContent", "error", "editorData", "JSON", "parse", "html", "blocks", "for<PERSON>ach", "block", "type", "data", "text", "items", "item", "link", "meta", "image", "url", "title", "description", "URL", "hostname", "bypassSecurityTrustHtml", "e", "toggleFavorite", "id", "updatedNote", "isFavourite", "emit", "goToNote", "noteId", "navigate", "getSlicedContent", "length", "slice", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0", "imports", "standalone"], "sources": ["C:\\Users\\<USER>\\source\\ai-hub\\WebApp\\src\\app\\MyNotesProjects\\search-documents\\search-documents.component.ts"], "sourcesContent": ["import { Component, EventEmitter, OnInit, Output } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { NotesService, Note } from '../services/notes.service';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { MarkdownModule } from 'ngx-markdown';\r\nimport { DomSanitizer, SafeHtml } from '@angular/platform-browser';\r\n\r\ninterface EditorBlock {\r\n  id: string;\r\n  type: string;\r\n  data: {\r\n    text?: string;\r\n    style?: string;\r\n    items?: Array<{ content: string; items: any[] }>;\r\n    link?: string;\r\n    meta?: { image?: { url?: string }; title?: string; description?: string };\r\n  };\r\n}\r\n\r\ninterface EditorData {\r\n  time: number;\r\n  blocks: EditorBlock[];\r\n  version: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-search-documents',\r\n  templateUrl: './search-documents.component.html',\r\n  styleUrls: ['./search-documents.component.css'],\r\n  imports: [CommonModule, FormsModule, MarkdownModule],\r\n  standalone: true,\r\n})\r\nexport class SearchDocumentsComponent implements OnInit {\r\n  searchQuery: string = '';\r\n  searchResults: Note[] = [];\r\n  isLoading: boolean = false;\r\n  @Output() favoriteToggled = new EventEmitter<Note>();\r\n\r\n  constructor(\r\n    private notesService: NotesService,\r\n    private sanitizer: DomSanitizer,\r\n    private route: ActivatedRoute,\r\n    private router: Router\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.route.queryParams.subscribe(params => {\r\n      if (params['q']) {\r\n        this.searchQuery = params['q'];\r\n        this.onSearch();\r\n      }\r\n    });\r\n  }\r\n\r\n  onSearch() {\r\n    this.isLoading = true;\r\n    this.notesService.searchNotes(this.searchQuery).subscribe({\r\n      next: results => {\r\n        console.log('Search results:', results);\r\n        this.searchResults = results.map(note => ({\r\n          ...note,\r\n          content: this.formatContent(note.content)\r\n        }));\r\n        this.isLoading = false;\r\n      },\r\n      error: error => {\r\n        console.error('Error searching notes:', error);\r\n        this.isLoading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  private formatContent(content: string): SafeHtml {\r\n    try {\r\n      const editorData: EditorData = JSON.parse(content);\r\n      let html = '';\r\n\r\n      editorData.blocks.forEach(block => {\r\n        switch (block.type) {\r\n          case 'paragraph':\r\n            html += `<p class=\"mb-4\">${block.data.text || ''}</p>`;\r\n            break;\r\n          case 'list':\r\n            html += '<ul class=\"list-disc pl-6 mb-4\">';\r\n            block.data.items?.forEach(item => {\r\n              html += `<li class=\"mb-2\">${item?.content || ''}</li>`;\r\n            });\r\n            html += '</ul>';\r\n            break;\r\n          case 'link':\r\n            const { link, meta } = block.data;\r\n            html += `\r\n              <div class=\"mb-4 p-4 border rounded-lg border-gray-200 dark:border-gray-700\">\r\n                <a href=\"${link}\" target=\"_blank\" rel=\"noopener noreferrer\"\r\n                   class=\"flex items-start no-underline\">\r\n                  ${meta?.image?.url ? `<div class=\"flex-shrink-0 mr-4\"><img src=\"${meta.image.url}\" alt=\"\" class=\"w-16 h-16 object-cover rounded\"></div>` : ''}\r\n                  <div class=\"flex-grow\">\r\n                    <h3 class=\"text-lg font-semibold text-blue-600 dark:text-blue-400 mb-1\">\r\n                      ${meta?.title || link}\r\n                    </h3>\r\n                    ${meta?.description ? `<p class=\"text-gray-600 dark:text-gray-300 text-sm line-clamp-2\">${meta.description}</p>` : ''}\r\n                    <span class=\"text-gray-500 dark:text-gray-400 text-xs\">${link ? new URL(link).hostname : ''}</span>\r\n                  </div>\r\n                </a>\r\n              </div>\r\n            `;\r\n            break;\r\n        }\r\n      });\r\n\r\n      return this.sanitizer.bypassSecurityTrustHtml(html);\r\n    } catch (e) {\r\n      console.error('Error parsing content:', e);\r\n      return this.sanitizer.bypassSecurityTrustHtml('<p>Error displaying content</p>');\r\n    }\r\n  }\r\n\r\n  toggleFavorite(note: Note) {\r\n    if (!note?.id) return;\r\n\r\n    this.notesService.toggleFavorite(note.id).subscribe({\r\n      next: updatedNote => {\r\n        console.log(updatedNote);\r\n        note.isFavourite = updatedNote.isFavourite;\r\n        this.favoriteToggled.emit(note);\r\n      },\r\n      error: error => {\r\n        console.error('Error toggling favorite:', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  goToNote(noteId: number) {\r\n    this.router.navigate(['student/search', noteId]);\r\n  }\r\n\r\n  getSlicedContent(note: Note): string {\r\n    return note?.content?.length > 50 ? note.content.slice(0, 50) + '...' : note.content;\r\n  }\r\n}\r\n"], "mappings": ";;;AAAA,SAASA,SAAS,EAAEC,YAAY,EAAUC,MAAM,QAAQ,eAAe;AACvE,SAASC,cAAc,EAAEC,MAAM,QAAQ,iBAAiB;AACxD,SAASC,YAAY,QAAc,2BAA2B;AAC9D,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,cAAc,QAAQ,cAAc;AAC7C,SAASC,YAAY,QAAkB,2BAA2B;AA2B3D,IAAMC,wBAAwB,GAA9B,MAAMA,wBAAwB;EAMnCC,YACUC,YAA0B,EAC1BC,SAAuB,EACvBC,KAAqB,EACrBC,MAAc;IAHd,KAAAH,YAAY,GAAZA,YAAY;IACZ,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IAThB,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,aAAa,GAAW,EAAE;IAC1B,KAAAC,SAAS,GAAY,KAAK;IAChB,KAAAC,eAAe,GAAG,IAAIlB,YAAY,EAAQ;EAOjD;EAEHmB,QAAQA,CAAA;IACN,IAAI,CAACN,KAAK,CAACO,WAAW,CAACC,SAAS,CAACC,MAAM,IAAG;MACxC,IAAIA,MAAM,CAAC,GAAG,CAAC,EAAE;QACf,IAAI,CAACP,WAAW,GAAGO,MAAM,CAAC,GAAG,CAAC;QAC9B,IAAI,CAACC,QAAQ,EAAE;;IAEnB,CAAC,CAAC;EACJ;EAEAA,QAAQA,CAAA;IACN,IAAI,CAACN,SAAS,GAAG,IAAI;IACrB,IAAI,CAACN,YAAY,CAACa,WAAW,CAAC,IAAI,CAACT,WAAW,CAAC,CAACM,SAAS,CAAC;MACxDI,IAAI,EAAEC,OAAO,IAAG;QACdC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,OAAO,CAAC;QACvC,IAAI,CAACV,aAAa,GAAGU,OAAO,CAACG,GAAG,CAACC,IAAI,KAAK;UACxC,GAAGA,IAAI;UACPC,OAAO,EAAE,IAAI,CAACC,aAAa,CAACF,IAAI,CAACC,OAAO;SACzC,CAAC,CAAC;QACH,IAAI,CAACd,SAAS,GAAG,KAAK;MACxB,CAAC;MACDgB,KAAK,EAAEA,KAAK,IAAG;QACbN,OAAO,CAACM,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAAChB,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EAEQe,aAAaA,CAACD,OAAe;IACnC,IAAI;MACF,MAAMG,UAAU,GAAeC,IAAI,CAACC,KAAK,CAACL,OAAO,CAAC;MAClD,IAAIM,IAAI,GAAG,EAAE;MAEbH,UAAU,CAACI,MAAM,CAACC,OAAO,CAACC,KAAK,IAAG;QAChC,QAAQA,KAAK,CAACC,IAAI;UAChB,KAAK,WAAW;YACdJ,IAAI,IAAI,mBAAmBG,KAAK,CAACE,IAAI,CAACC,IAAI,IAAI,EAAE,MAAM;YACtD;UACF,KAAK,MAAM;YACTN,IAAI,IAAI,kCAAkC;YAC1CG,KAAK,CAACE,IAAI,CAACE,KAAK,EAAEL,OAAO,CAACM,IAAI,IAAG;cAC/BR,IAAI,IAAI,oBAAoBQ,IAAI,EAAEd,OAAO,IAAI,EAAE,OAAO;YACxD,CAAC,CAAC;YACFM,IAAI,IAAI,OAAO;YACf;UACF,KAAK,MAAM;YACT,MAAM;cAAES,IAAI;cAAEC;YAAI,CAAE,GAAGP,KAAK,CAACE,IAAI;YACjCL,IAAI,IAAI;;2BAEOS,IAAI;;oBAEXC,IAAI,EAAEC,KAAK,EAAEC,GAAG,GAAG,6CAA6CF,IAAI,CAACC,KAAK,CAACC,GAAG,wDAAwD,GAAG,EAAE;;;wBAGvIF,IAAI,EAAEG,KAAK,IAAIJ,IAAI;;sBAErBC,IAAI,EAAEI,WAAW,GAAG,oEAAoEJ,IAAI,CAACI,WAAW,MAAM,GAAG,EAAE;6EAC5DL,IAAI,GAAG,IAAIM,GAAG,CAACN,IAAI,CAAC,CAACO,QAAQ,GAAG,EAAE;;;;aAIlG;YACD;;MAEN,CAAC,CAAC;MAEF,OAAO,IAAI,CAACzC,SAAS,CAAC0C,uBAAuB,CAACjB,IAAI,CAAC;KACpD,CAAC,OAAOkB,CAAC,EAAE;MACV5B,OAAO,CAACM,KAAK,CAAC,wBAAwB,EAAEsB,CAAC,CAAC;MAC1C,OAAO,IAAI,CAAC3C,SAAS,CAAC0C,uBAAuB,CAAC,iCAAiC,CAAC;;EAEpF;EAEAE,cAAcA,CAAC1B,IAAU;IACvB,IAAI,CAACA,IAAI,EAAE2B,EAAE,EAAE;IAEf,IAAI,CAAC9C,YAAY,CAAC6C,cAAc,CAAC1B,IAAI,CAAC2B,EAAE,CAAC,CAACpC,SAAS,CAAC;MAClDI,IAAI,EAAEiC,WAAW,IAAG;QAClB/B,OAAO,CAACC,GAAG,CAAC8B,WAAW,CAAC;QACxB5B,IAAI,CAAC6B,WAAW,GAAGD,WAAW,CAACC,WAAW;QAC1C,IAAI,CAACzC,eAAe,CAAC0C,IAAI,CAAC9B,IAAI,CAAC;MACjC,CAAC;MACDG,KAAK,EAAEA,KAAK,IAAG;QACbN,OAAO,CAACM,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAClD;KACD,CAAC;EACJ;EAEA4B,QAAQA,CAACC,MAAc;IACrB,IAAI,CAAChD,MAAM,CAACiD,QAAQ,CAAC,CAAC,gBAAgB,EAAED,MAAM,CAAC,CAAC;EAClD;EAEAE,gBAAgBA,CAAClC,IAAU;IACzB,OAAOA,IAAI,EAAEC,OAAO,EAAEkC,MAAM,GAAG,EAAE,GAAGnC,IAAI,CAACC,OAAO,CAACmC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAGpC,IAAI,CAACC,OAAO;EACtF;;;;;;;;;;;;;;;cAtGC9B;MAAM;;;;AAJIQ,wBAAwB,GAAA0D,UAAA,EAPpCpE,SAAS,CAAC;EACTqE,QAAQ,EAAE,sBAAsB;EAChCC,QAAA,EAAAC,oBAAgD;EAEhDC,OAAO,EAAE,CAAClE,YAAY,EAAEC,WAAW,EAAEC,cAAc,CAAC;EACpDiE,UAAU,EAAE,IAAI;;CACjB,CAAC,C,EACW/D,wBAAwB,CA2GpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}