{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport * as ApiServiceProxies from './service-proxies';\nlet ServiceProxyModule = class ServiceProxyModule {};\nServiceProxyModule = __decorate([NgModule({\n  providers: [ApiServiceProxies.UserAccountServiceProxy, ApiServiceProxies.ChatServiceProxy, ApiServiceProxies.AgentDefinitionServiceProxy, ApiServiceProxies.ChatModelServiceProxy, ApiServiceProxies.ModelDetailsServiceProxy, ApiServiceProxies.ApiCredentialsServiceProxy, ApiServiceProxies.WorkspaceServiceProxy, ApiServiceProxies.ProjectMemoryServiceProxy, ApiServiceProxies.AssignWorkspaceServiceProxy, ApiServiceProxies.DocsServiceProxy, ApiServiceProxies.FileServiceProxy, ApiServiceProxies.AiServiceProxy,\n  // ApiServiceProxies.EmbeddingConfigServiceProxy,\n  ApiServiceProxies.PromptLibraryServiceProxy, ApiServiceProxies.AuthServiceProxy, ApiServiceProxies.PluginServiceProxy, ApiServiceProxies.DailyInsightServiceProxy, ApiServiceProxies.MemoryServiceProxy, ApiServiceProxies.SqlConnectionServiceProxy, ApiServiceProxies.EmailServiceProxy, ApiServiceProxies.AgentChatServiceProxy]\n})], ServiceProxyModule);\nexport { ServiceProxyModule };", "map": {"version": 3, "names": ["NgModule", "ApiServiceProxies", "ServiceProxyModule", "__decorate", "providers", "UserAccountServiceProxy", "ChatServiceProxy", "AgentDefinitionServiceProxy", "ChatModelServiceProxy", "ModelDetailsServiceProxy", "ApiCredentialsServiceProxy", "WorkspaceServiceProxy", "ProjectMemoryServiceProxy", "AssignWorkspaceServiceProxy", "DocsServiceProxy", "FileServiceProxy", "AiServiceProxy", "PromptLibraryServiceProxy", "AuthServiceProxy", "PluginServiceProxy", "DailyInsightServiceProxy", "MemoryServiceProxy", "SqlConnectionServiceProxy", "EmailServiceProxy", "AgentChatServiceProxy"], "sources": ["C:\\Users\\<USER>\\source\\ai-hub\\WebApp\\src\\shared\\service-proxies\\service-proxy.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport * as ApiServiceProxies from './service-proxies';\r\n\r\n@NgModule({\r\n  providers: [\r\n    ApiServiceProxies.UserAccountServiceProxy,\r\n    ApiServiceProxies.ChatServiceProxy,\r\n    ApiServiceProxies.AgentDefinitionServiceProxy,\r\n    ApiServiceProxies.ChatModelServiceProxy,\r\n    ApiServiceProxies.ModelDetailsServiceProxy,\r\n    ApiServiceProxies.ApiCredentialsServiceProxy,\r\n    ApiServiceProxies.WorkspaceServiceProxy,\r\n    ApiServiceProxies.ProjectMemoryServiceProxy,\r\n    ApiServiceProxies.AssignWorkspaceServiceProxy,\r\n    ApiServiceProxies.DocsServiceProxy,\r\n    ApiServiceProxies.FileServiceProxy,\r\n    ApiServiceProxies.AiServiceProxy,\r\n    // ApiServiceProxies.EmbeddingConfigServiceProxy,\r\n    ApiServiceProxies.PromptLibraryServiceProxy,\r\n    ApiServiceProxies.AuthServiceProxy,\r\n    ApiServiceProxies.PluginServiceProxy,\r\n    ApiServiceProxies.DailyInsightServiceProxy,\r\n    ApiServiceProxies.MemoryServiceProxy,\r\n    ApiServiceProxies.SqlConnectionServiceProxy,\r\n    ApiServiceProxies.EmailServiceProxy,\r\n    ApiServiceProxies.AgentChatServiceProxy\r\n  ],\r\n})\r\nexport class ServiceProxyModule { }\r\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,OAAO,KAAKC,iBAAiB,MAAM,mBAAmB;AA2B/C,IAAMC,kBAAkB,GAAxB,MAAMA,kBAAkB,GAAI;AAAtBA,kBAAkB,GAAAC,UAAA,EAzB9BH,QAAQ,CAAC;EACRI,SAAS,EAAE,CACTH,iBAAiB,CAACI,uBAAuB,EACzCJ,iBAAiB,CAACK,gBAAgB,EAClCL,iBAAiB,CAACM,2BAA2B,EAC7CN,iBAAiB,CAACO,qBAAqB,EACvCP,iBAAiB,CAACQ,wBAAwB,EAC1CR,iBAAiB,CAACS,0BAA0B,EAC5CT,iBAAiB,CAACU,qBAAqB,EACvCV,iBAAiB,CAACW,yBAAyB,EAC3CX,iBAAiB,CAACY,2BAA2B,EAC7CZ,iBAAiB,CAACa,gBAAgB,EAClCb,iBAAiB,CAACc,gBAAgB,EAClCd,iBAAiB,CAACe,cAAc;EAChC;EACAf,iBAAiB,CAACgB,yBAAyB,EAC3ChB,iBAAiB,CAACiB,gBAAgB,EAClCjB,iBAAiB,CAACkB,kBAAkB,EACpClB,iBAAiB,CAACmB,wBAAwB,EAC1CnB,iBAAiB,CAACoB,kBAAkB,EACpCpB,iBAAiB,CAACqB,yBAAyB,EAC3CrB,iBAAiB,CAACsB,iBAAiB,EACnCtB,iBAAiB,CAACuB,qBAAqB;CAE1C,CAAC,C,EACWtB,kBAAkB,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}