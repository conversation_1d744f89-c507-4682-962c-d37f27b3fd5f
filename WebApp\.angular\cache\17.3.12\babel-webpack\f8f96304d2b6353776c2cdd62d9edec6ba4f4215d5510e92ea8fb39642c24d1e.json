{"ast": null, "code": "import { EmbedBlot } from 'parchment';\nimport TextBlot from './text.js';\nconst GUARD_TEXT = '\\uFEFF';\nclass Embed extends EmbedBlot {\n  constructor(scroll, node) {\n    super(scroll, node);\n    this.contentNode = document.createElement('span');\n    this.contentNode.setAttribute('contenteditable', 'false');\n    Array.from(this.domNode.childNodes).forEach(childNode => {\n      this.contentNode.appendChild(childNode);\n    });\n    this.leftGuard = document.createTextNode(GUARD_TEXT);\n    this.rightGuard = document.createTextNode(GUARD_TEXT);\n    this.domNode.appendChild(this.leftGuard);\n    this.domNode.appendChild(this.contentNode);\n    this.domNode.appendChild(this.rightGuard);\n  }\n  index(node, offset) {\n    if (node === this.leftGuard) return 0;\n    if (node === this.rightGuard) return 1;\n    return super.index(node, offset);\n  }\n  restore(node) {\n    let range = null;\n    let textNode;\n    const text = node.data.split(GUARD_TEXT).join('');\n    if (node === this.leftGuard) {\n      if (this.prev instanceof TextBlot) {\n        const prevLength = this.prev.length();\n        this.prev.insertAt(prevLength, text);\n        range = {\n          startNode: this.prev.domNode,\n          startOffset: prevLength + text.length\n        };\n      } else {\n        textNode = document.createTextNode(text);\n        this.parent.insertBefore(this.scroll.create(textNode), this);\n        range = {\n          startNode: textNode,\n          startOffset: text.length\n        };\n      }\n    } else if (node === this.rightGuard) {\n      if (this.next instanceof TextBlot) {\n        this.next.insertAt(0, text);\n        range = {\n          startNode: this.next.domNode,\n          startOffset: text.length\n        };\n      } else {\n        textNode = document.createTextNode(text);\n        this.parent.insertBefore(this.scroll.create(textNode), this.next);\n        range = {\n          startNode: textNode,\n          startOffset: text.length\n        };\n      }\n    }\n    node.data = GUARD_TEXT;\n    return range;\n  }\n  update(mutations, context) {\n    mutations.forEach(mutation => {\n      if (mutation.type === 'characterData' && (mutation.target === this.leftGuard || mutation.target === this.rightGuard)) {\n        const range = this.restore(mutation.target);\n        if (range) context.range = range;\n      }\n    });\n  }\n}\nexport default Embed;", "map": {"version": 3, "names": ["EmbedBlot", "TextBlot", "GUARD_TEXT", "Embed", "constructor", "scroll", "node", "contentNode", "document", "createElement", "setAttribute", "Array", "from", "domNode", "childNodes", "for<PERSON>ach", "childNode", "append<PERSON><PERSON><PERSON>", "leftGuard", "createTextNode", "<PERSON><PERSON><PERSON>", "index", "offset", "restore", "range", "textNode", "text", "data", "split", "join", "prev", "prevLength", "length", "insertAt", "startNode", "startOffset", "parent", "insertBefore", "create", "next", "update", "mutations", "context", "mutation", "type", "target"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/quill/blots/embed.js"], "sourcesContent": ["import { EmbedBlot } from 'parchment';\nimport TextBlot from './text.js';\nconst GUARD_TEXT = '\\uFEFF';\nclass Embed extends EmbedBlot {\n  constructor(scroll, node) {\n    super(scroll, node);\n    this.contentNode = document.createElement('span');\n    this.contentNode.setAttribute('contenteditable', 'false');\n    Array.from(this.domNode.childNodes).forEach(childNode => {\n      this.contentNode.appendChild(childNode);\n    });\n    this.leftGuard = document.createTextNode(GUARD_TEXT);\n    this.rightGuard = document.createTextNode(GUARD_TEXT);\n    this.domNode.appendChild(this.leftGuard);\n    this.domNode.appendChild(this.contentNode);\n    this.domNode.appendChild(this.rightGuard);\n  }\n  index(node, offset) {\n    if (node === this.leftGuard) return 0;\n    if (node === this.rightGuard) return 1;\n    return super.index(node, offset);\n  }\n  restore(node) {\n    let range = null;\n    let textNode;\n    const text = node.data.split(GUARD_TEXT).join('');\n    if (node === this.leftGuard) {\n      if (this.prev instanceof TextBlot) {\n        const prevLength = this.prev.length();\n        this.prev.insertAt(prevLength, text);\n        range = {\n          startNode: this.prev.domNode,\n          startOffset: prevLength + text.length\n        };\n      } else {\n        textNode = document.createTextNode(text);\n        this.parent.insertBefore(this.scroll.create(textNode), this);\n        range = {\n          startNode: textNode,\n          startOffset: text.length\n        };\n      }\n    } else if (node === this.rightGuard) {\n      if (this.next instanceof TextBlot) {\n        this.next.insertAt(0, text);\n        range = {\n          startNode: this.next.domNode,\n          startOffset: text.length\n        };\n      } else {\n        textNode = document.createTextNode(text);\n        this.parent.insertBefore(this.scroll.create(textNode), this.next);\n        range = {\n          startNode: textNode,\n          startOffset: text.length\n        };\n      }\n    }\n    node.data = GUARD_TEXT;\n    return range;\n  }\n  update(mutations, context) {\n    mutations.forEach(mutation => {\n      if (mutation.type === 'characterData' && (mutation.target === this.leftGuard || mutation.target === this.rightGuard)) {\n        const range = this.restore(mutation.target);\n        if (range) context.range = range;\n      }\n    });\n  }\n}\nexport default Embed;\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,WAAW;AACrC,OAAOC,QAAQ,MAAM,WAAW;AAChC,MAAMC,UAAU,GAAG,QAAQ;AAC3B,MAAMC,KAAK,SAASH,SAAS,CAAC;EAC5BI,WAAWA,CAACC,MAAM,EAAEC,IAAI,EAAE;IACxB,KAAK,CAACD,MAAM,EAAEC,IAAI,CAAC;IACnB,IAAI,CAACC,WAAW,GAAGC,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;IACjD,IAAI,CAACF,WAAW,CAACG,YAAY,CAAC,iBAAiB,EAAE,OAAO,CAAC;IACzDC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACC,OAAO,CAACC,UAAU,CAAC,CAACC,OAAO,CAACC,SAAS,IAAI;MACvD,IAAI,CAACT,WAAW,CAACU,WAAW,CAACD,SAAS,CAAC;IACzC,CAAC,CAAC;IACF,IAAI,CAACE,SAAS,GAAGV,QAAQ,CAACW,cAAc,CAACjB,UAAU,CAAC;IACpD,IAAI,CAACkB,UAAU,GAAGZ,QAAQ,CAACW,cAAc,CAACjB,UAAU,CAAC;IACrD,IAAI,CAACW,OAAO,CAACI,WAAW,CAAC,IAAI,CAACC,SAAS,CAAC;IACxC,IAAI,CAACL,OAAO,CAACI,WAAW,CAAC,IAAI,CAACV,WAAW,CAAC;IAC1C,IAAI,CAACM,OAAO,CAACI,WAAW,CAAC,IAAI,CAACG,UAAU,CAAC;EAC3C;EACAC,KAAKA,CAACf,IAAI,EAAEgB,MAAM,EAAE;IAClB,IAAIhB,IAAI,KAAK,IAAI,CAACY,SAAS,EAAE,OAAO,CAAC;IACrC,IAAIZ,IAAI,KAAK,IAAI,CAACc,UAAU,EAAE,OAAO,CAAC;IACtC,OAAO,KAAK,CAACC,KAAK,CAACf,IAAI,EAAEgB,MAAM,CAAC;EAClC;EACAC,OAAOA,CAACjB,IAAI,EAAE;IACZ,IAAIkB,KAAK,GAAG,IAAI;IAChB,IAAIC,QAAQ;IACZ,MAAMC,IAAI,GAAGpB,IAAI,CAACqB,IAAI,CAACC,KAAK,CAAC1B,UAAU,CAAC,CAAC2B,IAAI,CAAC,EAAE,CAAC;IACjD,IAAIvB,IAAI,KAAK,IAAI,CAACY,SAAS,EAAE;MAC3B,IAAI,IAAI,CAACY,IAAI,YAAY7B,QAAQ,EAAE;QACjC,MAAM8B,UAAU,GAAG,IAAI,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC;QACrC,IAAI,CAACF,IAAI,CAACG,QAAQ,CAACF,UAAU,EAAEL,IAAI,CAAC;QACpCF,KAAK,GAAG;UACNU,SAAS,EAAE,IAAI,CAACJ,IAAI,CAACjB,OAAO;UAC5BsB,WAAW,EAAEJ,UAAU,GAAGL,IAAI,CAACM;QACjC,CAAC;MACH,CAAC,MAAM;QACLP,QAAQ,GAAGjB,QAAQ,CAACW,cAAc,CAACO,IAAI,CAAC;QACxC,IAAI,CAACU,MAAM,CAACC,YAAY,CAAC,IAAI,CAAChC,MAAM,CAACiC,MAAM,CAACb,QAAQ,CAAC,EAAE,IAAI,CAAC;QAC5DD,KAAK,GAAG;UACNU,SAAS,EAAET,QAAQ;UACnBU,WAAW,EAAET,IAAI,CAACM;QACpB,CAAC;MACH;IACF,CAAC,MAAM,IAAI1B,IAAI,KAAK,IAAI,CAACc,UAAU,EAAE;MACnC,IAAI,IAAI,CAACmB,IAAI,YAAYtC,QAAQ,EAAE;QACjC,IAAI,CAACsC,IAAI,CAACN,QAAQ,CAAC,CAAC,EAAEP,IAAI,CAAC;QAC3BF,KAAK,GAAG;UACNU,SAAS,EAAE,IAAI,CAACK,IAAI,CAAC1B,OAAO;UAC5BsB,WAAW,EAAET,IAAI,CAACM;QACpB,CAAC;MACH,CAAC,MAAM;QACLP,QAAQ,GAAGjB,QAAQ,CAACW,cAAc,CAACO,IAAI,CAAC;QACxC,IAAI,CAACU,MAAM,CAACC,YAAY,CAAC,IAAI,CAAChC,MAAM,CAACiC,MAAM,CAACb,QAAQ,CAAC,EAAE,IAAI,CAACc,IAAI,CAAC;QACjEf,KAAK,GAAG;UACNU,SAAS,EAAET,QAAQ;UACnBU,WAAW,EAAET,IAAI,CAACM;QACpB,CAAC;MACH;IACF;IACA1B,IAAI,CAACqB,IAAI,GAAGzB,UAAU;IACtB,OAAOsB,KAAK;EACd;EACAgB,MAAMA,CAACC,SAAS,EAAEC,OAAO,EAAE;IACzBD,SAAS,CAAC1B,OAAO,CAAC4B,QAAQ,IAAI;MAC5B,IAAIA,QAAQ,CAACC,IAAI,KAAK,eAAe,KAAKD,QAAQ,CAACE,MAAM,KAAK,IAAI,CAAC3B,SAAS,IAAIyB,QAAQ,CAACE,MAAM,KAAK,IAAI,CAACzB,UAAU,CAAC,EAAE;QACpH,MAAMI,KAAK,GAAG,IAAI,CAACD,OAAO,CAACoB,QAAQ,CAACE,MAAM,CAAC;QAC3C,IAAIrB,KAAK,EAAEkB,OAAO,CAAClB,KAAK,GAAGA,KAAK;MAClC;IACF,CAAC,CAAC;EACJ;AACF;AACA,eAAerB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}