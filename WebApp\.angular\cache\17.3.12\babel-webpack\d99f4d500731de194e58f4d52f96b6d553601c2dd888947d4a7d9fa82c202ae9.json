{"ast": null, "code": "import { BlockEmbed } from '../blots/block.js';\nimport Link from './link.js';\nconst ATTRIBUTES = ['height', 'width'];\nclass Video extends BlockEmbed {\n  static blotName = 'video';\n  static className = 'ql-video';\n  static tagName = 'IFRAME';\n  static create(value) {\n    const node = super.create(value);\n    node.setAttribute('frameborder', '0');\n    node.setAttribute('allowfullscreen', 'true');\n    node.setAttribute('src', this.sanitize(value));\n    return node;\n  }\n  static formats(domNode) {\n    return ATTRIBUTES.reduce((formats, attribute) => {\n      if (domNode.hasAttribute(attribute)) {\n        formats[attribute] = domNode.getAttribute(attribute);\n      }\n      return formats;\n    }, {});\n  }\n  static sanitize(url) {\n    return Link.sanitize(url);\n  }\n  static value(domNode) {\n    return domNode.getAttribute('src');\n  }\n  format(name, value) {\n    if (ATTRIBUTES.indexOf(name) > -1) {\n      if (value) {\n        this.domNode.setAttribute(name, value);\n      } else {\n        this.domNode.removeAttribute(name);\n      }\n    } else {\n      super.format(name, value);\n    }\n  }\n  html() {\n    const {\n      video\n    } = this.value();\n    return `<a href=\"${video}\">${video}</a>`;\n  }\n}\nexport default Video;", "map": {"version": 3, "names": ["BlockEmbed", "Link", "ATTRIBUTES", "Video", "blotName", "className", "tagName", "create", "value", "node", "setAttribute", "sanitize", "formats", "domNode", "reduce", "attribute", "hasAttribute", "getAttribute", "url", "format", "name", "indexOf", "removeAttribute", "html", "video"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/quill/formats/video.js"], "sourcesContent": ["import { BlockEmbed } from '../blots/block.js';\nimport Link from './link.js';\nconst ATTRIBUTES = ['height', 'width'];\nclass Video extends BlockEmbed {\n  static blotName = 'video';\n  static className = 'ql-video';\n  static tagName = 'IFRAME';\n  static create(value) {\n    const node = super.create(value);\n    node.setAttribute('frameborder', '0');\n    node.setAttribute('allowfullscreen', 'true');\n    node.setAttribute('src', this.sanitize(value));\n    return node;\n  }\n  static formats(domNode) {\n    return ATTRIBUTES.reduce((formats, attribute) => {\n      if (domNode.hasAttribute(attribute)) {\n        formats[attribute] = domNode.getAttribute(attribute);\n      }\n      return formats;\n    }, {});\n  }\n  static sanitize(url) {\n    return Link.sanitize(url);\n  }\n  static value(domNode) {\n    return domNode.getAttribute('src');\n  }\n  format(name, value) {\n    if (ATTRIBUTES.indexOf(name) > -1) {\n      if (value) {\n        this.domNode.setAttribute(name, value);\n      } else {\n        this.domNode.removeAttribute(name);\n      }\n    } else {\n      super.format(name, value);\n    }\n  }\n  html() {\n    const {\n      video\n    } = this.value();\n    return `<a href=\"${video}\">${video}</a>`;\n  }\n}\nexport default Video;\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,mBAAmB;AAC9C,OAAOC,IAAI,MAAM,WAAW;AAC5B,MAAMC,UAAU,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC;AACtC,MAAMC,KAAK,SAASH,UAAU,CAAC;EAC7B,OAAOI,QAAQ,GAAG,OAAO;EACzB,OAAOC,SAAS,GAAG,UAAU;EAC7B,OAAOC,OAAO,GAAG,QAAQ;EACzB,OAAOC,MAAMA,CAACC,KAAK,EAAE;IACnB,MAAMC,IAAI,GAAG,KAAK,CAACF,MAAM,CAACC,KAAK,CAAC;IAChCC,IAAI,CAACC,YAAY,CAAC,aAAa,EAAE,GAAG,CAAC;IACrCD,IAAI,CAACC,YAAY,CAAC,iBAAiB,EAAE,MAAM,CAAC;IAC5CD,IAAI,CAACC,YAAY,CAAC,KAAK,EAAE,IAAI,CAACC,QAAQ,CAACH,KAAK,CAAC,CAAC;IAC9C,OAAOC,IAAI;EACb;EACA,OAAOG,OAAOA,CAACC,OAAO,EAAE;IACtB,OAAOX,UAAU,CAACY,MAAM,CAAC,CAACF,OAAO,EAAEG,SAAS,KAAK;MAC/C,IAAIF,OAAO,CAACG,YAAY,CAACD,SAAS,CAAC,EAAE;QACnCH,OAAO,CAACG,SAAS,CAAC,GAAGF,OAAO,CAACI,YAAY,CAACF,SAAS,CAAC;MACtD;MACA,OAAOH,OAAO;IAChB,CAAC,EAAE,CAAC,CAAC,CAAC;EACR;EACA,OAAOD,QAAQA,CAACO,GAAG,EAAE;IACnB,OAAOjB,IAAI,CAACU,QAAQ,CAACO,GAAG,CAAC;EAC3B;EACA,OAAOV,KAAKA,CAACK,OAAO,EAAE;IACpB,OAAOA,OAAO,CAACI,YAAY,CAAC,KAAK,CAAC;EACpC;EACAE,MAAMA,CAACC,IAAI,EAAEZ,KAAK,EAAE;IAClB,IAAIN,UAAU,CAACmB,OAAO,CAACD,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;MACjC,IAAIZ,KAAK,EAAE;QACT,IAAI,CAACK,OAAO,CAACH,YAAY,CAACU,IAAI,EAAEZ,KAAK,CAAC;MACxC,CAAC,MAAM;QACL,IAAI,CAACK,OAAO,CAACS,eAAe,CAACF,IAAI,CAAC;MACpC;IACF,CAAC,MAAM;MACL,KAAK,CAACD,MAAM,CAACC,IAAI,EAAEZ,KAAK,CAAC;IAC3B;EACF;EACAe,IAAIA,CAAA,EAAG;IACL,MAAM;MACJC;IACF,CAAC,GAAG,IAAI,CAAChB,KAAK,CAAC,CAAC;IAChB,OAAQ,YAAWgB,KAAM,KAAIA,KAAM,MAAK;EAC1C;AACF;AACA,eAAerB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}