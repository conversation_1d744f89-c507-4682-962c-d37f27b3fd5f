{"ast": null, "code": "import { Attributor, BlockBlot, ClassAttributor, EmbedBlot, Scope, StyleAttributor } from 'parchment';\nimport Delta from 'quill-delta';\nimport { BlockEmbed } from '../blots/block.js';\nimport logger from '../core/logger.js';\nimport Module from '../core/module.js';\nimport Quill from '../core/quill.js';\nimport { AlignAttribute, AlignStyle } from '../formats/align.js';\nimport { BackgroundStyle } from '../formats/background.js';\nimport CodeBlock from '../formats/code.js';\nimport { ColorStyle } from '../formats/color.js';\nimport { DirectionAttribute, DirectionStyle } from '../formats/direction.js';\nimport { FontStyle } from '../formats/font.js';\nimport { SizeStyle } from '../formats/size.js';\nimport { deleteRange } from './keyboard.js';\nimport normalizeExternalHTML from './normalizeExternalHTML/index.js';\nconst debug = logger('quill:clipboard');\nconst CLIPBOARD_CONFIG = [[Node.TEXT_NODE, matchText], [Node.TEXT_NODE, matchNewline], ['br', matchBreak], [Node.ELEMENT_NODE, matchNewline], [Node.ELEMENT_NODE, matchBlot], [Node.ELEMENT_NODE, matchAttributor], [Node.ELEMENT_NODE, matchStyles], ['li', matchIndent], ['ol, ul', matchList], ['pre', matchCodeBlock], ['tr', matchTable], ['b', createMatchAlias('bold')], ['i', createMatchAlias('italic')], ['strike', createMatchAlias('strike')], ['style', matchIgnore]];\nconst ATTRIBUTE_ATTRIBUTORS = [AlignAttribute, DirectionAttribute].reduce((memo, attr) => {\n  memo[attr.keyName] = attr;\n  return memo;\n}, {});\nconst STYLE_ATTRIBUTORS = [AlignStyle, BackgroundStyle, ColorStyle, DirectionStyle, FontStyle, SizeStyle].reduce((memo, attr) => {\n  memo[attr.keyName] = attr;\n  return memo;\n}, {});\nclass Clipboard extends Module {\n  static DEFAULTS = {\n    matchers: []\n  };\n  constructor(quill, options) {\n    super(quill, options);\n    this.quill.root.addEventListener('copy', e => this.onCaptureCopy(e, false));\n    this.quill.root.addEventListener('cut', e => this.onCaptureCopy(e, true));\n    this.quill.root.addEventListener('paste', this.onCapturePaste.bind(this));\n    this.matchers = [];\n    CLIPBOARD_CONFIG.concat(this.options.matchers ?? []).forEach(_ref => {\n      let [selector, matcher] = _ref;\n      this.addMatcher(selector, matcher);\n    });\n  }\n  addMatcher(selector, matcher) {\n    this.matchers.push([selector, matcher]);\n  }\n  convert(_ref2) {\n    let {\n      html,\n      text\n    } = _ref2;\n    let formats = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    if (formats[CodeBlock.blotName]) {\n      return new Delta().insert(text || '', {\n        [CodeBlock.blotName]: formats[CodeBlock.blotName]\n      });\n    }\n    if (!html) {\n      return new Delta().insert(text || '', formats);\n    }\n    const delta = this.convertHTML(html);\n    // Remove trailing newline\n    if (deltaEndsWith(delta, '\\n') && (delta.ops[delta.ops.length - 1].attributes == null || formats.table)) {\n      return delta.compose(new Delta().retain(delta.length() - 1).delete(1));\n    }\n    return delta;\n  }\n  normalizeHTML(doc) {\n    normalizeExternalHTML(doc);\n  }\n  convertHTML(html) {\n    const doc = new DOMParser().parseFromString(html, 'text/html');\n    this.normalizeHTML(doc);\n    const container = doc.body;\n    const nodeMatches = new WeakMap();\n    const [elementMatchers, textMatchers] = this.prepareMatching(container, nodeMatches);\n    return traverse(this.quill.scroll, container, elementMatchers, textMatchers, nodeMatches);\n  }\n  dangerouslyPasteHTML(index, html) {\n    let source = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : Quill.sources.API;\n    if (typeof index === 'string') {\n      const delta = this.convert({\n        html: index,\n        text: ''\n      });\n      // @ts-expect-error\n      this.quill.setContents(delta, html);\n      this.quill.setSelection(0, Quill.sources.SILENT);\n    } else {\n      const paste = this.convert({\n        html,\n        text: ''\n      });\n      this.quill.updateContents(new Delta().retain(index).concat(paste), source);\n      this.quill.setSelection(index + paste.length(), Quill.sources.SILENT);\n    }\n  }\n  onCaptureCopy(e) {\n    let isCut = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    if (e.defaultPrevented) return;\n    e.preventDefault();\n    const [range] = this.quill.selection.getRange();\n    if (range == null) return;\n    const {\n      html,\n      text\n    } = this.onCopy(range, isCut);\n    e.clipboardData?.setData('text/plain', text);\n    e.clipboardData?.setData('text/html', html);\n    if (isCut) {\n      deleteRange({\n        range,\n        quill: this.quill\n      });\n    }\n  }\n\n  /*\n   * https://www.iana.org/assignments/media-types/text/uri-list\n   */\n  normalizeURIList(urlList) {\n    return urlList.split(/\\r?\\n/)\n    // Ignore all comments\n    .filter(url => url[0] !== '#').join('\\n');\n  }\n  onCapturePaste(e) {\n    if (e.defaultPrevented || !this.quill.isEnabled()) return;\n    e.preventDefault();\n    const range = this.quill.getSelection(true);\n    if (range == null) return;\n    const html = e.clipboardData?.getData('text/html');\n    let text = e.clipboardData?.getData('text/plain');\n    if (!html && !text) {\n      const urlList = e.clipboardData?.getData('text/uri-list');\n      if (urlList) {\n        text = this.normalizeURIList(urlList);\n      }\n    }\n    const files = Array.from(e.clipboardData?.files || []);\n    if (!html && files.length > 0) {\n      this.quill.uploader.upload(range, files);\n      return;\n    }\n    if (html && files.length > 0) {\n      const doc = new DOMParser().parseFromString(html, 'text/html');\n      if (doc.body.childElementCount === 1 && doc.body.firstElementChild?.tagName === 'IMG') {\n        this.quill.uploader.upload(range, files);\n        return;\n      }\n    }\n    this.onPaste(range, {\n      html,\n      text\n    });\n  }\n  onCopy(range) {\n    const text = this.quill.getText(range);\n    const html = this.quill.getSemanticHTML(range);\n    return {\n      html,\n      text\n    };\n  }\n  onPaste(range, _ref3) {\n    let {\n      text,\n      html\n    } = _ref3;\n    const formats = this.quill.getFormat(range.index);\n    const pastedDelta = this.convert({\n      text,\n      html\n    }, formats);\n    debug.log('onPaste', pastedDelta, {\n      text,\n      html\n    });\n    const delta = new Delta().retain(range.index).delete(range.length).concat(pastedDelta);\n    this.quill.updateContents(delta, Quill.sources.USER);\n    // range.length contributes to delta.length()\n    this.quill.setSelection(delta.length() - range.length, Quill.sources.SILENT);\n    this.quill.scrollSelectionIntoView();\n  }\n  prepareMatching(container, nodeMatches) {\n    const elementMatchers = [];\n    const textMatchers = [];\n    this.matchers.forEach(pair => {\n      const [selector, matcher] = pair;\n      switch (selector) {\n        case Node.TEXT_NODE:\n          textMatchers.push(matcher);\n          break;\n        case Node.ELEMENT_NODE:\n          elementMatchers.push(matcher);\n          break;\n        default:\n          Array.from(container.querySelectorAll(selector)).forEach(node => {\n            if (nodeMatches.has(node)) {\n              const matches = nodeMatches.get(node);\n              matches?.push(matcher);\n            } else {\n              nodeMatches.set(node, [matcher]);\n            }\n          });\n          break;\n      }\n    });\n    return [elementMatchers, textMatchers];\n  }\n}\nfunction applyFormat(delta, format, value, scroll) {\n  if (!scroll.query(format)) {\n    return delta;\n  }\n  return delta.reduce((newDelta, op) => {\n    if (!op.insert) return newDelta;\n    if (op.attributes && op.attributes[format]) {\n      return newDelta.push(op);\n    }\n    const formats = value ? {\n      [format]: value\n    } : {};\n    return newDelta.insert(op.insert, {\n      ...formats,\n      ...op.attributes\n    });\n  }, new Delta());\n}\nfunction deltaEndsWith(delta, text) {\n  let endText = '';\n  for (let i = delta.ops.length - 1; i >= 0 && endText.length < text.length; --i // eslint-disable-line no-plusplus\n  ) {\n    const op = delta.ops[i];\n    if (typeof op.insert !== 'string') break;\n    endText = op.insert + endText;\n  }\n  return endText.slice(-1 * text.length) === text;\n}\nfunction isLine(node, scroll) {\n  if (!(node instanceof Element)) return false;\n  const match = scroll.query(node);\n  // @ts-expect-error\n  if (match && match.prototype instanceof EmbedBlot) return false;\n  return ['address', 'article', 'blockquote', 'canvas', 'dd', 'div', 'dl', 'dt', 'fieldset', 'figcaption', 'figure', 'footer', 'form', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'header', 'iframe', 'li', 'main', 'nav', 'ol', 'output', 'p', 'pre', 'section', 'table', 'td', 'tr', 'ul', 'video'].includes(node.tagName.toLowerCase());\n}\nfunction isBetweenInlineElements(node, scroll) {\n  return node.previousElementSibling && node.nextElementSibling && !isLine(node.previousElementSibling, scroll) && !isLine(node.nextElementSibling, scroll);\n}\nconst preNodes = new WeakMap();\nfunction isPre(node) {\n  if (node == null) return false;\n  if (!preNodes.has(node)) {\n    // @ts-expect-error\n    if (node.tagName === 'PRE') {\n      preNodes.set(node, true);\n    } else {\n      preNodes.set(node, isPre(node.parentNode));\n    }\n  }\n  return preNodes.get(node);\n}\nfunction traverse(scroll, node, elementMatchers, textMatchers, nodeMatches) {\n  // Post-order\n  if (node.nodeType === node.TEXT_NODE) {\n    return textMatchers.reduce((delta, matcher) => {\n      return matcher(node, delta, scroll);\n    }, new Delta());\n  }\n  if (node.nodeType === node.ELEMENT_NODE) {\n    return Array.from(node.childNodes || []).reduce((delta, childNode) => {\n      let childrenDelta = traverse(scroll, childNode, elementMatchers, textMatchers, nodeMatches);\n      if (childNode.nodeType === node.ELEMENT_NODE) {\n        childrenDelta = elementMatchers.reduce((reducedDelta, matcher) => {\n          return matcher(childNode, reducedDelta, scroll);\n        }, childrenDelta);\n        childrenDelta = (nodeMatches.get(childNode) || []).reduce((reducedDelta, matcher) => {\n          return matcher(childNode, reducedDelta, scroll);\n        }, childrenDelta);\n      }\n      return delta.concat(childrenDelta);\n    }, new Delta());\n  }\n  return new Delta();\n}\nfunction createMatchAlias(format) {\n  return (_node, delta, scroll) => {\n    return applyFormat(delta, format, true, scroll);\n  };\n}\nfunction matchAttributor(node, delta, scroll) {\n  const attributes = Attributor.keys(node);\n  const classes = ClassAttributor.keys(node);\n  const styles = StyleAttributor.keys(node);\n  const formats = {};\n  attributes.concat(classes).concat(styles).forEach(name => {\n    let attr = scroll.query(name, Scope.ATTRIBUTE);\n    if (attr != null) {\n      formats[attr.attrName] = attr.value(node);\n      if (formats[attr.attrName]) return;\n    }\n    attr = ATTRIBUTE_ATTRIBUTORS[name];\n    if (attr != null && (attr.attrName === name || attr.keyName === name)) {\n      formats[attr.attrName] = attr.value(node) || undefined;\n    }\n    attr = STYLE_ATTRIBUTORS[name];\n    if (attr != null && (attr.attrName === name || attr.keyName === name)) {\n      attr = STYLE_ATTRIBUTORS[name];\n      formats[attr.attrName] = attr.value(node) || undefined;\n    }\n  });\n  return Object.entries(formats).reduce((newDelta, _ref4) => {\n    let [name, value] = _ref4;\n    return applyFormat(newDelta, name, value, scroll);\n  }, delta);\n}\nfunction matchBlot(node, delta, scroll) {\n  const match = scroll.query(node);\n  if (match == null) return delta;\n  // @ts-expect-error\n  if (match.prototype instanceof EmbedBlot) {\n    const embed = {};\n    // @ts-expect-error\n    const value = match.value(node);\n    if (value != null) {\n      // @ts-expect-error\n      embed[match.blotName] = value;\n      // @ts-expect-error\n      return new Delta().insert(embed, match.formats(node, scroll));\n    }\n  } else {\n    // @ts-expect-error\n    if (match.prototype instanceof BlockBlot && !deltaEndsWith(delta, '\\n')) {\n      delta.insert('\\n');\n    }\n    if ('blotName' in match && 'formats' in match && typeof match.formats === 'function') {\n      return applyFormat(delta, match.blotName, match.formats(node, scroll), scroll);\n    }\n  }\n  return delta;\n}\nfunction matchBreak(node, delta) {\n  if (!deltaEndsWith(delta, '\\n')) {\n    delta.insert('\\n');\n  }\n  return delta;\n}\nfunction matchCodeBlock(node, delta, scroll) {\n  const match = scroll.query('code-block');\n  const language = match && 'formats' in match && typeof match.formats === 'function' ? match.formats(node, scroll) : true;\n  return applyFormat(delta, 'code-block', language, scroll);\n}\nfunction matchIgnore() {\n  return new Delta();\n}\nfunction matchIndent(node, delta, scroll) {\n  const match = scroll.query(node);\n  if (match == null ||\n  // @ts-expect-error\n  match.blotName !== 'list' || !deltaEndsWith(delta, '\\n')) {\n    return delta;\n  }\n  let indent = -1;\n  let parent = node.parentNode;\n  while (parent != null) {\n    // @ts-expect-error\n    if (['OL', 'UL'].includes(parent.tagName)) {\n      indent += 1;\n    }\n    parent = parent.parentNode;\n  }\n  if (indent <= 0) return delta;\n  return delta.reduce((composed, op) => {\n    if (!op.insert) return composed;\n    if (op.attributes && typeof op.attributes.indent === 'number') {\n      return composed.push(op);\n    }\n    return composed.insert(op.insert, {\n      indent,\n      ...(op.attributes || {})\n    });\n  }, new Delta());\n}\nfunction matchList(node, delta, scroll) {\n  const element = node;\n  let list = element.tagName === 'OL' ? 'ordered' : 'bullet';\n  const checkedAttr = element.getAttribute('data-checked');\n  if (checkedAttr) {\n    list = checkedAttr === 'true' ? 'checked' : 'unchecked';\n  }\n  return applyFormat(delta, 'list', list, scroll);\n}\nfunction matchNewline(node, delta, scroll) {\n  if (!deltaEndsWith(delta, '\\n')) {\n    if (isLine(node, scroll) && (node.childNodes.length > 0 || node instanceof HTMLParagraphElement)) {\n      return delta.insert('\\n');\n    }\n    if (delta.length() > 0 && node.nextSibling) {\n      let nextSibling = node.nextSibling;\n      while (nextSibling != null) {\n        if (isLine(nextSibling, scroll)) {\n          return delta.insert('\\n');\n        }\n        const match = scroll.query(nextSibling);\n        // @ts-expect-error\n        if (match && match.prototype instanceof BlockEmbed) {\n          return delta.insert('\\n');\n        }\n        nextSibling = nextSibling.firstChild;\n      }\n    }\n  }\n  return delta;\n}\nfunction matchStyles(node, delta, scroll) {\n  const formats = {};\n  const style = node.style || {};\n  if (style.fontStyle === 'italic') {\n    formats.italic = true;\n  }\n  if (style.textDecoration === 'underline') {\n    formats.underline = true;\n  }\n  if (style.textDecoration === 'line-through') {\n    formats.strike = true;\n  }\n  if (style.fontWeight?.startsWith('bold') ||\n  // @ts-expect-error Fix me later\n  parseInt(style.fontWeight, 10) >= 700) {\n    formats.bold = true;\n  }\n  delta = Object.entries(formats).reduce((newDelta, _ref5) => {\n    let [name, value] = _ref5;\n    return applyFormat(newDelta, name, value, scroll);\n  }, delta);\n  // @ts-expect-error\n  if (parseFloat(style.textIndent || 0) > 0) {\n    // Could be 0.5in\n    return new Delta().insert('\\t').concat(delta);\n  }\n  return delta;\n}\nfunction matchTable(node, delta, scroll) {\n  const table = node.parentElement?.tagName === 'TABLE' ? node.parentElement : node.parentElement?.parentElement;\n  if (table != null) {\n    const rows = Array.from(table.querySelectorAll('tr'));\n    const row = rows.indexOf(node) + 1;\n    return applyFormat(delta, 'table', row, scroll);\n  }\n  return delta;\n}\nfunction matchText(node, delta, scroll) {\n  // @ts-expect-error\n  let text = node.data;\n  // Word represents empty line with <o:p>&nbsp;</o:p>\n  if (node.parentElement?.tagName === 'O:P') {\n    return delta.insert(text.trim());\n  }\n  if (!isPre(node)) {\n    if (text.trim().length === 0 && text.includes('\\n') && !isBetweenInlineElements(node, scroll)) {\n      return delta;\n    }\n    // convert all non-nbsp whitespace into regular space\n    text = text.replace(/[^\\S\\u00a0]/g, ' ');\n    // collapse consecutive spaces into one\n    text = text.replace(/ {2,}/g, ' ');\n    if (node.previousSibling == null && node.parentElement != null && isLine(node.parentElement, scroll) || node.previousSibling instanceof Element && isLine(node.previousSibling, scroll)) {\n      // block structure means we don't need leading space\n      text = text.replace(/^ /, '');\n    }\n    if (node.nextSibling == null && node.parentElement != null && isLine(node.parentElement, scroll) || node.nextSibling instanceof Element && isLine(node.nextSibling, scroll)) {\n      // block structure means we don't need trailing space\n      text = text.replace(/ $/, '');\n    }\n    // done removing whitespace and can normalize all to regular space\n    text = text.replaceAll('\\u00a0', ' ');\n  }\n  return delta.insert(text);\n}\nexport { Clipboard as default, matchAttributor, matchBlot, matchNewline, matchText, traverse };", "map": {"version": 3, "names": ["Attributor", "BlockBlot", "ClassAttributor", "EmbedBlot", "<PERSON><PERSON>", "StyleAttributor", "Delta", "BlockEmbed", "logger", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "AlignAttribute", "AlignStyle", "BackgroundStyle", "CodeBlock", "ColorStyle", "DirectionAttribute", "DirectionStyle", "FontStyle", "SizeStyle", "deleteRange", "normalizeExternalHTML", "debug", "CLIPBOARD_CONFIG", "Node", "TEXT_NODE", "matchText", "matchNewline", "matchBreak", "ELEMENT_NODE", "matchBlot", "matchAttributor", "matchStyles", "matchIndent", "matchList", "matchCodeBlock", "matchTable", "createMatchAlias", "matchIgnore", "ATTRIBUTE_ATTRIBUTORS", "reduce", "memo", "attr", "keyName", "STYLE_ATTRIBUTORS", "Clipboard", "DEFAULTS", "matchers", "constructor", "quill", "options", "root", "addEventListener", "e", "onCaptureCopy", "onCapturePaste", "bind", "concat", "for<PERSON>ach", "_ref", "selector", "matcher", "addMatcher", "push", "convert", "_ref2", "html", "text", "formats", "arguments", "length", "undefined", "blotName", "insert", "delta", "convertHTML", "deltaEndsWith", "ops", "attributes", "table", "compose", "retain", "delete", "normalizeHTML", "doc", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "container", "body", "nodeMatches", "WeakMap", "elementMatchers", "textMatchers", "prepareMatching", "traverse", "scroll", "dangerouslyPasteHTML", "index", "source", "sources", "API", "setContents", "setSelection", "SILENT", "paste", "updateContents", "isCut", "defaultPrevented", "preventDefault", "range", "selection", "getRange", "onCopy", "clipboardData", "setData", "normalizeURIList", "urlList", "split", "filter", "url", "join", "isEnabled", "getSelection", "getData", "files", "Array", "from", "uploader", "upload", "childElementCount", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "tagName", "onPaste", "getText", "getSemanticHTML", "_ref3", "getFormat", "pastedDelta", "log", "USER", "scrollSelectionIntoView", "pair", "querySelectorAll", "node", "has", "matches", "get", "set", "applyFormat", "format", "value", "query", "newDelta", "op", "endText", "i", "slice", "isLine", "Element", "match", "prototype", "includes", "toLowerCase", "isBetweenInlineElements", "previousElementSibling", "nextElement<PERSON><PERSON>ling", "preNodes", "isPre", "parentNode", "nodeType", "childNodes", "childNode", "<PERSON><PERSON><PERSON><PERSON>", "reducedDelta", "_node", "keys", "classes", "styles", "name", "ATTRIBUTE", "attrName", "Object", "entries", "_ref4", "embed", "language", "indent", "parent", "composed", "element", "list", "checkedAttr", "getAttribute", "HTMLParagraphElement", "nextS<PERSON>ling", "<PERSON><PERSON><PERSON><PERSON>", "style", "fontStyle", "italic", "textDecoration", "underline", "strike", "fontWeight", "startsWith", "parseInt", "bold", "_ref5", "parseFloat", "textIndent", "parentElement", "rows", "row", "indexOf", "data", "trim", "replace", "previousSibling", "replaceAll", "default"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/quill/modules/clipboard.js"], "sourcesContent": ["import { Attributor, BlockBlot, ClassAttributor, EmbedBlot, Scope, StyleAttributor } from 'parchment';\nimport Delta from 'quill-delta';\nimport { BlockEmbed } from '../blots/block.js';\nimport logger from '../core/logger.js';\nimport Module from '../core/module.js';\nimport Quill from '../core/quill.js';\nimport { AlignAttribute, AlignStyle } from '../formats/align.js';\nimport { BackgroundStyle } from '../formats/background.js';\nimport CodeBlock from '../formats/code.js';\nimport { ColorStyle } from '../formats/color.js';\nimport { DirectionAttribute, DirectionStyle } from '../formats/direction.js';\nimport { FontStyle } from '../formats/font.js';\nimport { SizeStyle } from '../formats/size.js';\nimport { deleteRange } from './keyboard.js';\nimport normalizeExternalHTML from './normalizeExternalHTML/index.js';\nconst debug = logger('quill:clipboard');\nconst CLIPBOARD_CONFIG = [[Node.TEXT_NODE, matchText], [Node.TEXT_NODE, matchNewline], ['br', matchBreak], [Node.ELEMENT_NODE, matchNewline], [Node.ELEMENT_NODE, matchBlot], [Node.ELEMENT_NODE, matchAttributor], [Node.ELEMENT_NODE, matchStyles], ['li', matchIndent], ['ol, ul', matchList], ['pre', matchCodeBlock], ['tr', matchTable], ['b', createMatchAlias('bold')], ['i', createMatchAlias('italic')], ['strike', createMatchAlias('strike')], ['style', matchIgnore]];\nconst ATTRIBUTE_ATTRIBUTORS = [AlignAttribute, DirectionAttribute].reduce((memo, attr) => {\n  memo[attr.keyName] = attr;\n  return memo;\n}, {});\nconst STYLE_ATTRIBUTORS = [AlignStyle, BackgroundStyle, ColorStyle, DirectionStyle, FontStyle, SizeStyle].reduce((memo, attr) => {\n  memo[attr.keyName] = attr;\n  return memo;\n}, {});\nclass Clipboard extends Module {\n  static DEFAULTS = {\n    matchers: []\n  };\n  constructor(quill, options) {\n    super(quill, options);\n    this.quill.root.addEventListener('copy', e => this.onCaptureCopy(e, false));\n    this.quill.root.addEventListener('cut', e => this.onCaptureCopy(e, true));\n    this.quill.root.addEventListener('paste', this.onCapturePaste.bind(this));\n    this.matchers = [];\n    CLIPBOARD_CONFIG.concat(this.options.matchers ?? []).forEach(_ref => {\n      let [selector, matcher] = _ref;\n      this.addMatcher(selector, matcher);\n    });\n  }\n  addMatcher(selector, matcher) {\n    this.matchers.push([selector, matcher]);\n  }\n  convert(_ref2) {\n    let {\n      html,\n      text\n    } = _ref2;\n    let formats = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    if (formats[CodeBlock.blotName]) {\n      return new Delta().insert(text || '', {\n        [CodeBlock.blotName]: formats[CodeBlock.blotName]\n      });\n    }\n    if (!html) {\n      return new Delta().insert(text || '', formats);\n    }\n    const delta = this.convertHTML(html);\n    // Remove trailing newline\n    if (deltaEndsWith(delta, '\\n') && (delta.ops[delta.ops.length - 1].attributes == null || formats.table)) {\n      return delta.compose(new Delta().retain(delta.length() - 1).delete(1));\n    }\n    return delta;\n  }\n  normalizeHTML(doc) {\n    normalizeExternalHTML(doc);\n  }\n  convertHTML(html) {\n    const doc = new DOMParser().parseFromString(html, 'text/html');\n    this.normalizeHTML(doc);\n    const container = doc.body;\n    const nodeMatches = new WeakMap();\n    const [elementMatchers, textMatchers] = this.prepareMatching(container, nodeMatches);\n    return traverse(this.quill.scroll, container, elementMatchers, textMatchers, nodeMatches);\n  }\n  dangerouslyPasteHTML(index, html) {\n    let source = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : Quill.sources.API;\n    if (typeof index === 'string') {\n      const delta = this.convert({\n        html: index,\n        text: ''\n      });\n      // @ts-expect-error\n      this.quill.setContents(delta, html);\n      this.quill.setSelection(0, Quill.sources.SILENT);\n    } else {\n      const paste = this.convert({\n        html,\n        text: ''\n      });\n      this.quill.updateContents(new Delta().retain(index).concat(paste), source);\n      this.quill.setSelection(index + paste.length(), Quill.sources.SILENT);\n    }\n  }\n  onCaptureCopy(e) {\n    let isCut = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    if (e.defaultPrevented) return;\n    e.preventDefault();\n    const [range] = this.quill.selection.getRange();\n    if (range == null) return;\n    const {\n      html,\n      text\n    } = this.onCopy(range, isCut);\n    e.clipboardData?.setData('text/plain', text);\n    e.clipboardData?.setData('text/html', html);\n    if (isCut) {\n      deleteRange({\n        range,\n        quill: this.quill\n      });\n    }\n  }\n\n  /*\n   * https://www.iana.org/assignments/media-types/text/uri-list\n   */\n  normalizeURIList(urlList) {\n    return urlList.split(/\\r?\\n/)\n    // Ignore all comments\n    .filter(url => url[0] !== '#').join('\\n');\n  }\n  onCapturePaste(e) {\n    if (e.defaultPrevented || !this.quill.isEnabled()) return;\n    e.preventDefault();\n    const range = this.quill.getSelection(true);\n    if (range == null) return;\n    const html = e.clipboardData?.getData('text/html');\n    let text = e.clipboardData?.getData('text/plain');\n    if (!html && !text) {\n      const urlList = e.clipboardData?.getData('text/uri-list');\n      if (urlList) {\n        text = this.normalizeURIList(urlList);\n      }\n    }\n    const files = Array.from(e.clipboardData?.files || []);\n    if (!html && files.length > 0) {\n      this.quill.uploader.upload(range, files);\n      return;\n    }\n    if (html && files.length > 0) {\n      const doc = new DOMParser().parseFromString(html, 'text/html');\n      if (doc.body.childElementCount === 1 && doc.body.firstElementChild?.tagName === 'IMG') {\n        this.quill.uploader.upload(range, files);\n        return;\n      }\n    }\n    this.onPaste(range, {\n      html,\n      text\n    });\n  }\n  onCopy(range) {\n    const text = this.quill.getText(range);\n    const html = this.quill.getSemanticHTML(range);\n    return {\n      html,\n      text\n    };\n  }\n  onPaste(range, _ref3) {\n    let {\n      text,\n      html\n    } = _ref3;\n    const formats = this.quill.getFormat(range.index);\n    const pastedDelta = this.convert({\n      text,\n      html\n    }, formats);\n    debug.log('onPaste', pastedDelta, {\n      text,\n      html\n    });\n    const delta = new Delta().retain(range.index).delete(range.length).concat(pastedDelta);\n    this.quill.updateContents(delta, Quill.sources.USER);\n    // range.length contributes to delta.length()\n    this.quill.setSelection(delta.length() - range.length, Quill.sources.SILENT);\n    this.quill.scrollSelectionIntoView();\n  }\n  prepareMatching(container, nodeMatches) {\n    const elementMatchers = [];\n    const textMatchers = [];\n    this.matchers.forEach(pair => {\n      const [selector, matcher] = pair;\n      switch (selector) {\n        case Node.TEXT_NODE:\n          textMatchers.push(matcher);\n          break;\n        case Node.ELEMENT_NODE:\n          elementMatchers.push(matcher);\n          break;\n        default:\n          Array.from(container.querySelectorAll(selector)).forEach(node => {\n            if (nodeMatches.has(node)) {\n              const matches = nodeMatches.get(node);\n              matches?.push(matcher);\n            } else {\n              nodeMatches.set(node, [matcher]);\n            }\n          });\n          break;\n      }\n    });\n    return [elementMatchers, textMatchers];\n  }\n}\nfunction applyFormat(delta, format, value, scroll) {\n  if (!scroll.query(format)) {\n    return delta;\n  }\n  return delta.reduce((newDelta, op) => {\n    if (!op.insert) return newDelta;\n    if (op.attributes && op.attributes[format]) {\n      return newDelta.push(op);\n    }\n    const formats = value ? {\n      [format]: value\n    } : {};\n    return newDelta.insert(op.insert, {\n      ...formats,\n      ...op.attributes\n    });\n  }, new Delta());\n}\nfunction deltaEndsWith(delta, text) {\n  let endText = '';\n  for (let i = delta.ops.length - 1; i >= 0 && endText.length < text.length; --i // eslint-disable-line no-plusplus\n  ) {\n    const op = delta.ops[i];\n    if (typeof op.insert !== 'string') break;\n    endText = op.insert + endText;\n  }\n  return endText.slice(-1 * text.length) === text;\n}\nfunction isLine(node, scroll) {\n  if (!(node instanceof Element)) return false;\n  const match = scroll.query(node);\n  // @ts-expect-error\n  if (match && match.prototype instanceof EmbedBlot) return false;\n  return ['address', 'article', 'blockquote', 'canvas', 'dd', 'div', 'dl', 'dt', 'fieldset', 'figcaption', 'figure', 'footer', 'form', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'header', 'iframe', 'li', 'main', 'nav', 'ol', 'output', 'p', 'pre', 'section', 'table', 'td', 'tr', 'ul', 'video'].includes(node.tagName.toLowerCase());\n}\nfunction isBetweenInlineElements(node, scroll) {\n  return node.previousElementSibling && node.nextElementSibling && !isLine(node.previousElementSibling, scroll) && !isLine(node.nextElementSibling, scroll);\n}\nconst preNodes = new WeakMap();\nfunction isPre(node) {\n  if (node == null) return false;\n  if (!preNodes.has(node)) {\n    // @ts-expect-error\n    if (node.tagName === 'PRE') {\n      preNodes.set(node, true);\n    } else {\n      preNodes.set(node, isPre(node.parentNode));\n    }\n  }\n  return preNodes.get(node);\n}\nfunction traverse(scroll, node, elementMatchers, textMatchers, nodeMatches) {\n  // Post-order\n  if (node.nodeType === node.TEXT_NODE) {\n    return textMatchers.reduce((delta, matcher) => {\n      return matcher(node, delta, scroll);\n    }, new Delta());\n  }\n  if (node.nodeType === node.ELEMENT_NODE) {\n    return Array.from(node.childNodes || []).reduce((delta, childNode) => {\n      let childrenDelta = traverse(scroll, childNode, elementMatchers, textMatchers, nodeMatches);\n      if (childNode.nodeType === node.ELEMENT_NODE) {\n        childrenDelta = elementMatchers.reduce((reducedDelta, matcher) => {\n          return matcher(childNode, reducedDelta, scroll);\n        }, childrenDelta);\n        childrenDelta = (nodeMatches.get(childNode) || []).reduce((reducedDelta, matcher) => {\n          return matcher(childNode, reducedDelta, scroll);\n        }, childrenDelta);\n      }\n      return delta.concat(childrenDelta);\n    }, new Delta());\n  }\n  return new Delta();\n}\nfunction createMatchAlias(format) {\n  return (_node, delta, scroll) => {\n    return applyFormat(delta, format, true, scroll);\n  };\n}\nfunction matchAttributor(node, delta, scroll) {\n  const attributes = Attributor.keys(node);\n  const classes = ClassAttributor.keys(node);\n  const styles = StyleAttributor.keys(node);\n  const formats = {};\n  attributes.concat(classes).concat(styles).forEach(name => {\n    let attr = scroll.query(name, Scope.ATTRIBUTE);\n    if (attr != null) {\n      formats[attr.attrName] = attr.value(node);\n      if (formats[attr.attrName]) return;\n    }\n    attr = ATTRIBUTE_ATTRIBUTORS[name];\n    if (attr != null && (attr.attrName === name || attr.keyName === name)) {\n      formats[attr.attrName] = attr.value(node) || undefined;\n    }\n    attr = STYLE_ATTRIBUTORS[name];\n    if (attr != null && (attr.attrName === name || attr.keyName === name)) {\n      attr = STYLE_ATTRIBUTORS[name];\n      formats[attr.attrName] = attr.value(node) || undefined;\n    }\n  });\n  return Object.entries(formats).reduce((newDelta, _ref4) => {\n    let [name, value] = _ref4;\n    return applyFormat(newDelta, name, value, scroll);\n  }, delta);\n}\nfunction matchBlot(node, delta, scroll) {\n  const match = scroll.query(node);\n  if (match == null) return delta;\n  // @ts-expect-error\n  if (match.prototype instanceof EmbedBlot) {\n    const embed = {};\n    // @ts-expect-error\n    const value = match.value(node);\n    if (value != null) {\n      // @ts-expect-error\n      embed[match.blotName] = value;\n      // @ts-expect-error\n      return new Delta().insert(embed, match.formats(node, scroll));\n    }\n  } else {\n    // @ts-expect-error\n    if (match.prototype instanceof BlockBlot && !deltaEndsWith(delta, '\\n')) {\n      delta.insert('\\n');\n    }\n    if ('blotName' in match && 'formats' in match && typeof match.formats === 'function') {\n      return applyFormat(delta, match.blotName, match.formats(node, scroll), scroll);\n    }\n  }\n  return delta;\n}\nfunction matchBreak(node, delta) {\n  if (!deltaEndsWith(delta, '\\n')) {\n    delta.insert('\\n');\n  }\n  return delta;\n}\nfunction matchCodeBlock(node, delta, scroll) {\n  const match = scroll.query('code-block');\n  const language = match && 'formats' in match && typeof match.formats === 'function' ? match.formats(node, scroll) : true;\n  return applyFormat(delta, 'code-block', language, scroll);\n}\nfunction matchIgnore() {\n  return new Delta();\n}\nfunction matchIndent(node, delta, scroll) {\n  const match = scroll.query(node);\n  if (match == null ||\n  // @ts-expect-error\n  match.blotName !== 'list' || !deltaEndsWith(delta, '\\n')) {\n    return delta;\n  }\n  let indent = -1;\n  let parent = node.parentNode;\n  while (parent != null) {\n    // @ts-expect-error\n    if (['OL', 'UL'].includes(parent.tagName)) {\n      indent += 1;\n    }\n    parent = parent.parentNode;\n  }\n  if (indent <= 0) return delta;\n  return delta.reduce((composed, op) => {\n    if (!op.insert) return composed;\n    if (op.attributes && typeof op.attributes.indent === 'number') {\n      return composed.push(op);\n    }\n    return composed.insert(op.insert, {\n      indent,\n      ...(op.attributes || {})\n    });\n  }, new Delta());\n}\nfunction matchList(node, delta, scroll) {\n  const element = node;\n  let list = element.tagName === 'OL' ? 'ordered' : 'bullet';\n  const checkedAttr = element.getAttribute('data-checked');\n  if (checkedAttr) {\n    list = checkedAttr === 'true' ? 'checked' : 'unchecked';\n  }\n  return applyFormat(delta, 'list', list, scroll);\n}\nfunction matchNewline(node, delta, scroll) {\n  if (!deltaEndsWith(delta, '\\n')) {\n    if (isLine(node, scroll) && (node.childNodes.length > 0 || node instanceof HTMLParagraphElement)) {\n      return delta.insert('\\n');\n    }\n    if (delta.length() > 0 && node.nextSibling) {\n      let nextSibling = node.nextSibling;\n      while (nextSibling != null) {\n        if (isLine(nextSibling, scroll)) {\n          return delta.insert('\\n');\n        }\n        const match = scroll.query(nextSibling);\n        // @ts-expect-error\n        if (match && match.prototype instanceof BlockEmbed) {\n          return delta.insert('\\n');\n        }\n        nextSibling = nextSibling.firstChild;\n      }\n    }\n  }\n  return delta;\n}\nfunction matchStyles(node, delta, scroll) {\n  const formats = {};\n  const style = node.style || {};\n  if (style.fontStyle === 'italic') {\n    formats.italic = true;\n  }\n  if (style.textDecoration === 'underline') {\n    formats.underline = true;\n  }\n  if (style.textDecoration === 'line-through') {\n    formats.strike = true;\n  }\n  if (style.fontWeight?.startsWith('bold') ||\n  // @ts-expect-error Fix me later\n  parseInt(style.fontWeight, 10) >= 700) {\n    formats.bold = true;\n  }\n  delta = Object.entries(formats).reduce((newDelta, _ref5) => {\n    let [name, value] = _ref5;\n    return applyFormat(newDelta, name, value, scroll);\n  }, delta);\n  // @ts-expect-error\n  if (parseFloat(style.textIndent || 0) > 0) {\n    // Could be 0.5in\n    return new Delta().insert('\\t').concat(delta);\n  }\n  return delta;\n}\nfunction matchTable(node, delta, scroll) {\n  const table = node.parentElement?.tagName === 'TABLE' ? node.parentElement : node.parentElement?.parentElement;\n  if (table != null) {\n    const rows = Array.from(table.querySelectorAll('tr'));\n    const row = rows.indexOf(node) + 1;\n    return applyFormat(delta, 'table', row, scroll);\n  }\n  return delta;\n}\nfunction matchText(node, delta, scroll) {\n  // @ts-expect-error\n  let text = node.data;\n  // Word represents empty line with <o:p>&nbsp;</o:p>\n  if (node.parentElement?.tagName === 'O:P') {\n    return delta.insert(text.trim());\n  }\n  if (!isPre(node)) {\n    if (text.trim().length === 0 && text.includes('\\n') && !isBetweenInlineElements(node, scroll)) {\n      return delta;\n    }\n    // convert all non-nbsp whitespace into regular space\n    text = text.replace(/[^\\S\\u00a0]/g, ' ');\n    // collapse consecutive spaces into one\n    text = text.replace(/ {2,}/g, ' ');\n    if (node.previousSibling == null && node.parentElement != null && isLine(node.parentElement, scroll) || node.previousSibling instanceof Element && isLine(node.previousSibling, scroll)) {\n      // block structure means we don't need leading space\n      text = text.replace(/^ /, '');\n    }\n    if (node.nextSibling == null && node.parentElement != null && isLine(node.parentElement, scroll) || node.nextSibling instanceof Element && isLine(node.nextSibling, scroll)) {\n      // block structure means we don't need trailing space\n      text = text.replace(/ $/, '');\n    }\n    // done removing whitespace and can normalize all to regular space\n    text = text.replaceAll('\\u00a0', ' ');\n  }\n  return delta.insert(text);\n}\nexport { Clipboard as default, matchAttributor, matchBlot, matchNewline, matchText, traverse };\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,SAAS,EAAEC,eAAe,EAAEC,SAAS,EAAEC,KAAK,EAAEC,eAAe,QAAQ,WAAW;AACrG,OAAOC,KAAK,MAAM,aAAa;AAC/B,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,KAAK,MAAM,kBAAkB;AACpC,SAASC,cAAc,EAAEC,UAAU,QAAQ,qBAAqB;AAChE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,OAAOC,SAAS,MAAM,oBAAoB;AAC1C,SAASC,UAAU,QAAQ,qBAAqB;AAChD,SAASC,kBAAkB,EAAEC,cAAc,QAAQ,yBAAyB;AAC5E,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,WAAW,QAAQ,eAAe;AAC3C,OAAOC,qBAAqB,MAAM,kCAAkC;AACpE,MAAMC,KAAK,GAAGd,MAAM,CAAC,iBAAiB,CAAC;AACvC,MAAMe,gBAAgB,GAAG,CAAC,CAACC,IAAI,CAACC,SAAS,EAAEC,SAAS,CAAC,EAAE,CAACF,IAAI,CAACC,SAAS,EAAEE,YAAY,CAAC,EAAE,CAAC,IAAI,EAAEC,UAAU,CAAC,EAAE,CAACJ,IAAI,CAACK,YAAY,EAAEF,YAAY,CAAC,EAAE,CAACH,IAAI,CAACK,YAAY,EAAEC,SAAS,CAAC,EAAE,CAACN,IAAI,CAACK,YAAY,EAAEE,eAAe,CAAC,EAAE,CAACP,IAAI,CAACK,YAAY,EAAEG,WAAW,CAAC,EAAE,CAAC,IAAI,EAAEC,WAAW,CAAC,EAAE,CAAC,QAAQ,EAAEC,SAAS,CAAC,EAAE,CAAC,KAAK,EAAEC,cAAc,CAAC,EAAE,CAAC,IAAI,EAAEC,UAAU,CAAC,EAAE,CAAC,GAAG,EAAEC,gBAAgB,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,GAAG,EAAEA,gBAAgB,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,QAAQ,EAAEA,gBAAgB,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,OAAO,EAAEC,WAAW,CAAC,CAAC;AACld,MAAMC,qBAAqB,GAAG,CAAC5B,cAAc,EAAEK,kBAAkB,CAAC,CAACwB,MAAM,CAAC,CAACC,IAAI,EAAEC,IAAI,KAAK;EACxFD,IAAI,CAACC,IAAI,CAACC,OAAO,CAAC,GAAGD,IAAI;EACzB,OAAOD,IAAI;AACb,CAAC,EAAE,CAAC,CAAC,CAAC;AACN,MAAMG,iBAAiB,GAAG,CAAChC,UAAU,EAAEC,eAAe,EAAEE,UAAU,EAAEE,cAAc,EAAEC,SAAS,EAAEC,SAAS,CAAC,CAACqB,MAAM,CAAC,CAACC,IAAI,EAAEC,IAAI,KAAK;EAC/HD,IAAI,CAACC,IAAI,CAACC,OAAO,CAAC,GAAGD,IAAI;EACzB,OAAOD,IAAI;AACb,CAAC,EAAE,CAAC,CAAC,CAAC;AACN,MAAMI,SAAS,SAASpC,MAAM,CAAC;EAC7B,OAAOqC,QAAQ,GAAG;IAChBC,QAAQ,EAAE;EACZ,CAAC;EACDC,WAAWA,CAACC,KAAK,EAAEC,OAAO,EAAE;IAC1B,KAAK,CAACD,KAAK,EAAEC,OAAO,CAAC;IACrB,IAAI,CAACD,KAAK,CAACE,IAAI,CAACC,gBAAgB,CAAC,MAAM,EAAEC,CAAC,IAAI,IAAI,CAACC,aAAa,CAACD,CAAC,EAAE,KAAK,CAAC,CAAC;IAC3E,IAAI,CAACJ,KAAK,CAACE,IAAI,CAACC,gBAAgB,CAAC,KAAK,EAAEC,CAAC,IAAI,IAAI,CAACC,aAAa,CAACD,CAAC,EAAE,IAAI,CAAC,CAAC;IACzE,IAAI,CAACJ,KAAK,CAACE,IAAI,CAACC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACG,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IACzE,IAAI,CAACT,QAAQ,GAAG,EAAE;IAClBxB,gBAAgB,CAACkC,MAAM,CAAC,IAAI,CAACP,OAAO,CAACH,QAAQ,IAAI,EAAE,CAAC,CAACW,OAAO,CAACC,IAAI,IAAI;MACnE,IAAI,CAACC,QAAQ,EAAEC,OAAO,CAAC,GAAGF,IAAI;MAC9B,IAAI,CAACG,UAAU,CAACF,QAAQ,EAAEC,OAAO,CAAC;IACpC,CAAC,CAAC;EACJ;EACAC,UAAUA,CAACF,QAAQ,EAAEC,OAAO,EAAE;IAC5B,IAAI,CAACd,QAAQ,CAACgB,IAAI,CAAC,CAACH,QAAQ,EAAEC,OAAO,CAAC,CAAC;EACzC;EACAG,OAAOA,CAACC,KAAK,EAAE;IACb,IAAI;MACFC,IAAI;MACJC;IACF,CAAC,GAAGF,KAAK;IACT,IAAIG,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpF,IAAID,OAAO,CAACtD,SAAS,CAAC0D,QAAQ,CAAC,EAAE;MAC/B,OAAO,IAAIlE,KAAK,CAAC,CAAC,CAACmE,MAAM,CAACN,IAAI,IAAI,EAAE,EAAE;QACpC,CAACrD,SAAS,CAAC0D,QAAQ,GAAGJ,OAAO,CAACtD,SAAS,CAAC0D,QAAQ;MAClD,CAAC,CAAC;IACJ;IACA,IAAI,CAACN,IAAI,EAAE;MACT,OAAO,IAAI5D,KAAK,CAAC,CAAC,CAACmE,MAAM,CAACN,IAAI,IAAI,EAAE,EAAEC,OAAO,CAAC;IAChD;IACA,MAAMM,KAAK,GAAG,IAAI,CAACC,WAAW,CAACT,IAAI,CAAC;IACpC;IACA,IAAIU,aAAa,CAACF,KAAK,EAAE,IAAI,CAAC,KAAKA,KAAK,CAACG,GAAG,CAACH,KAAK,CAACG,GAAG,CAACP,MAAM,GAAG,CAAC,CAAC,CAACQ,UAAU,IAAI,IAAI,IAAIV,OAAO,CAACW,KAAK,CAAC,EAAE;MACvG,OAAOL,KAAK,CAACM,OAAO,CAAC,IAAI1E,KAAK,CAAC,CAAC,CAAC2E,MAAM,CAACP,KAAK,CAACJ,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAACY,MAAM,CAAC,CAAC,CAAC,CAAC;IACxE;IACA,OAAOR,KAAK;EACd;EACAS,aAAaA,CAACC,GAAG,EAAE;IACjB/D,qBAAqB,CAAC+D,GAAG,CAAC;EAC5B;EACAT,WAAWA,CAACT,IAAI,EAAE;IAChB,MAAMkB,GAAG,GAAG,IAAIC,SAAS,CAAC,CAAC,CAACC,eAAe,CAACpB,IAAI,EAAE,WAAW,CAAC;IAC9D,IAAI,CAACiB,aAAa,CAACC,GAAG,CAAC;IACvB,MAAMG,SAAS,GAAGH,GAAG,CAACI,IAAI;IAC1B,MAAMC,WAAW,GAAG,IAAIC,OAAO,CAAC,CAAC;IACjC,MAAM,CAACC,eAAe,EAAEC,YAAY,CAAC,GAAG,IAAI,CAACC,eAAe,CAACN,SAAS,EAAEE,WAAW,CAAC;IACpF,OAAOK,QAAQ,CAAC,IAAI,CAAC7C,KAAK,CAAC8C,MAAM,EAAER,SAAS,EAAEI,eAAe,EAAEC,YAAY,EAAEH,WAAW,CAAC;EAC3F;EACAO,oBAAoBA,CAACC,KAAK,EAAE/B,IAAI,EAAE;IAChC,IAAIgC,MAAM,GAAG7B,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG3D,KAAK,CAACyF,OAAO,CAACC,GAAG;IAClG,IAAI,OAAOH,KAAK,KAAK,QAAQ,EAAE;MAC7B,MAAMvB,KAAK,GAAG,IAAI,CAACV,OAAO,CAAC;QACzBE,IAAI,EAAE+B,KAAK;QACX9B,IAAI,EAAE;MACR,CAAC,CAAC;MACF;MACA,IAAI,CAAClB,KAAK,CAACoD,WAAW,CAAC3B,KAAK,EAAER,IAAI,CAAC;MACnC,IAAI,CAACjB,KAAK,CAACqD,YAAY,CAAC,CAAC,EAAE5F,KAAK,CAACyF,OAAO,CAACI,MAAM,CAAC;IAClD,CAAC,MAAM;MACL,MAAMC,KAAK,GAAG,IAAI,CAACxC,OAAO,CAAC;QACzBE,IAAI;QACJC,IAAI,EAAE;MACR,CAAC,CAAC;MACF,IAAI,CAAClB,KAAK,CAACwD,cAAc,CAAC,IAAInG,KAAK,CAAC,CAAC,CAAC2E,MAAM,CAACgB,KAAK,CAAC,CAACxC,MAAM,CAAC+C,KAAK,CAAC,EAAEN,MAAM,CAAC;MAC1E,IAAI,CAACjD,KAAK,CAACqD,YAAY,CAACL,KAAK,GAAGO,KAAK,CAAClC,MAAM,CAAC,CAAC,EAAE5D,KAAK,CAACyF,OAAO,CAACI,MAAM,CAAC;IACvE;EACF;EACAjD,aAAaA,CAACD,CAAC,EAAE;IACf,IAAIqD,KAAK,GAAGrC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;IACrF,IAAIhB,CAAC,CAACsD,gBAAgB,EAAE;IACxBtD,CAAC,CAACuD,cAAc,CAAC,CAAC;IAClB,MAAM,CAACC,KAAK,CAAC,GAAG,IAAI,CAAC5D,KAAK,CAAC6D,SAAS,CAACC,QAAQ,CAAC,CAAC;IAC/C,IAAIF,KAAK,IAAI,IAAI,EAAE;IACnB,MAAM;MACJ3C,IAAI;MACJC;IACF,CAAC,GAAG,IAAI,CAAC6C,MAAM,CAACH,KAAK,EAAEH,KAAK,CAAC;IAC7BrD,CAAC,CAAC4D,aAAa,EAAEC,OAAO,CAAC,YAAY,EAAE/C,IAAI,CAAC;IAC5Cd,CAAC,CAAC4D,aAAa,EAAEC,OAAO,CAAC,WAAW,EAAEhD,IAAI,CAAC;IAC3C,IAAIwC,KAAK,EAAE;MACTtF,WAAW,CAAC;QACVyF,KAAK;QACL5D,KAAK,EAAE,IAAI,CAACA;MACd,CAAC,CAAC;IACJ;EACF;;EAEA;AACF;AACA;EACEkE,gBAAgBA,CAACC,OAAO,EAAE;IACxB,OAAOA,OAAO,CAACC,KAAK,CAAC,OAAO;IAC5B;IAAA,CACCC,MAAM,CAACC,GAAG,IAAIA,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;EAC3C;EACAjE,cAAcA,CAACF,CAAC,EAAE;IAChB,IAAIA,CAAC,CAACsD,gBAAgB,IAAI,CAAC,IAAI,CAAC1D,KAAK,CAACwE,SAAS,CAAC,CAAC,EAAE;IACnDpE,CAAC,CAACuD,cAAc,CAAC,CAAC;IAClB,MAAMC,KAAK,GAAG,IAAI,CAAC5D,KAAK,CAACyE,YAAY,CAAC,IAAI,CAAC;IAC3C,IAAIb,KAAK,IAAI,IAAI,EAAE;IACnB,MAAM3C,IAAI,GAAGb,CAAC,CAAC4D,aAAa,EAAEU,OAAO,CAAC,WAAW,CAAC;IAClD,IAAIxD,IAAI,GAAGd,CAAC,CAAC4D,aAAa,EAAEU,OAAO,CAAC,YAAY,CAAC;IACjD,IAAI,CAACzD,IAAI,IAAI,CAACC,IAAI,EAAE;MAClB,MAAMiD,OAAO,GAAG/D,CAAC,CAAC4D,aAAa,EAAEU,OAAO,CAAC,eAAe,CAAC;MACzD,IAAIP,OAAO,EAAE;QACXjD,IAAI,GAAG,IAAI,CAACgD,gBAAgB,CAACC,OAAO,CAAC;MACvC;IACF;IACA,MAAMQ,KAAK,GAAGC,KAAK,CAACC,IAAI,CAACzE,CAAC,CAAC4D,aAAa,EAAEW,KAAK,IAAI,EAAE,CAAC;IACtD,IAAI,CAAC1D,IAAI,IAAI0D,KAAK,CAACtD,MAAM,GAAG,CAAC,EAAE;MAC7B,IAAI,CAACrB,KAAK,CAAC8E,QAAQ,CAACC,MAAM,CAACnB,KAAK,EAAEe,KAAK,CAAC;MACxC;IACF;IACA,IAAI1D,IAAI,IAAI0D,KAAK,CAACtD,MAAM,GAAG,CAAC,EAAE;MAC5B,MAAMc,GAAG,GAAG,IAAIC,SAAS,CAAC,CAAC,CAACC,eAAe,CAACpB,IAAI,EAAE,WAAW,CAAC;MAC9D,IAAIkB,GAAG,CAACI,IAAI,CAACyC,iBAAiB,KAAK,CAAC,IAAI7C,GAAG,CAACI,IAAI,CAAC0C,iBAAiB,EAAEC,OAAO,KAAK,KAAK,EAAE;QACrF,IAAI,CAAClF,KAAK,CAAC8E,QAAQ,CAACC,MAAM,CAACnB,KAAK,EAAEe,KAAK,CAAC;QACxC;MACF;IACF;IACA,IAAI,CAACQ,OAAO,CAACvB,KAAK,EAAE;MAClB3C,IAAI;MACJC;IACF,CAAC,CAAC;EACJ;EACA6C,MAAMA,CAACH,KAAK,EAAE;IACZ,MAAM1C,IAAI,GAAG,IAAI,CAAClB,KAAK,CAACoF,OAAO,CAACxB,KAAK,CAAC;IACtC,MAAM3C,IAAI,GAAG,IAAI,CAACjB,KAAK,CAACqF,eAAe,CAACzB,KAAK,CAAC;IAC9C,OAAO;MACL3C,IAAI;MACJC;IACF,CAAC;EACH;EACAiE,OAAOA,CAACvB,KAAK,EAAE0B,KAAK,EAAE;IACpB,IAAI;MACFpE,IAAI;MACJD;IACF,CAAC,GAAGqE,KAAK;IACT,MAAMnE,OAAO,GAAG,IAAI,CAACnB,KAAK,CAACuF,SAAS,CAAC3B,KAAK,CAACZ,KAAK,CAAC;IACjD,MAAMwC,WAAW,GAAG,IAAI,CAACzE,OAAO,CAAC;MAC/BG,IAAI;MACJD;IACF,CAAC,EAAEE,OAAO,CAAC;IACX9C,KAAK,CAACoH,GAAG,CAAC,SAAS,EAAED,WAAW,EAAE;MAChCtE,IAAI;MACJD;IACF,CAAC,CAAC;IACF,MAAMQ,KAAK,GAAG,IAAIpE,KAAK,CAAC,CAAC,CAAC2E,MAAM,CAAC4B,KAAK,CAACZ,KAAK,CAAC,CAACf,MAAM,CAAC2B,KAAK,CAACvC,MAAM,CAAC,CAACb,MAAM,CAACgF,WAAW,CAAC;IACtF,IAAI,CAACxF,KAAK,CAACwD,cAAc,CAAC/B,KAAK,EAAEhE,KAAK,CAACyF,OAAO,CAACwC,IAAI,CAAC;IACpD;IACA,IAAI,CAAC1F,KAAK,CAACqD,YAAY,CAAC5B,KAAK,CAACJ,MAAM,CAAC,CAAC,GAAGuC,KAAK,CAACvC,MAAM,EAAE5D,KAAK,CAACyF,OAAO,CAACI,MAAM,CAAC;IAC5E,IAAI,CAACtD,KAAK,CAAC2F,uBAAuB,CAAC,CAAC;EACtC;EACA/C,eAAeA,CAACN,SAAS,EAAEE,WAAW,EAAE;IACtC,MAAME,eAAe,GAAG,EAAE;IAC1B,MAAMC,YAAY,GAAG,EAAE;IACvB,IAAI,CAAC7C,QAAQ,CAACW,OAAO,CAACmF,IAAI,IAAI;MAC5B,MAAM,CAACjF,QAAQ,EAAEC,OAAO,CAAC,GAAGgF,IAAI;MAChC,QAAQjF,QAAQ;QACd,KAAKpC,IAAI,CAACC,SAAS;UACjBmE,YAAY,CAAC7B,IAAI,CAACF,OAAO,CAAC;UAC1B;QACF,KAAKrC,IAAI,CAACK,YAAY;UACpB8D,eAAe,CAAC5B,IAAI,CAACF,OAAO,CAAC;UAC7B;QACF;UACEgE,KAAK,CAACC,IAAI,CAACvC,SAAS,CAACuD,gBAAgB,CAAClF,QAAQ,CAAC,CAAC,CAACF,OAAO,CAACqF,IAAI,IAAI;YAC/D,IAAItD,WAAW,CAACuD,GAAG,CAACD,IAAI,CAAC,EAAE;cACzB,MAAME,OAAO,GAAGxD,WAAW,CAACyD,GAAG,CAACH,IAAI,CAAC;cACrCE,OAAO,EAAElF,IAAI,CAACF,OAAO,CAAC;YACxB,CAAC,MAAM;cACL4B,WAAW,CAAC0D,GAAG,CAACJ,IAAI,EAAE,CAAClF,OAAO,CAAC,CAAC;YAClC;UACF,CAAC,CAAC;UACF;MACJ;IACF,CAAC,CAAC;IACF,OAAO,CAAC8B,eAAe,EAAEC,YAAY,CAAC;EACxC;AACF;AACA,SAASwD,WAAWA,CAAC1E,KAAK,EAAE2E,MAAM,EAAEC,KAAK,EAAEvD,MAAM,EAAE;EACjD,IAAI,CAACA,MAAM,CAACwD,KAAK,CAACF,MAAM,CAAC,EAAE;IACzB,OAAO3E,KAAK;EACd;EACA,OAAOA,KAAK,CAAClC,MAAM,CAAC,CAACgH,QAAQ,EAAEC,EAAE,KAAK;IACpC,IAAI,CAACA,EAAE,CAAChF,MAAM,EAAE,OAAO+E,QAAQ;IAC/B,IAAIC,EAAE,CAAC3E,UAAU,IAAI2E,EAAE,CAAC3E,UAAU,CAACuE,MAAM,CAAC,EAAE;MAC1C,OAAOG,QAAQ,CAACzF,IAAI,CAAC0F,EAAE,CAAC;IAC1B;IACA,MAAMrF,OAAO,GAAGkF,KAAK,GAAG;MACtB,CAACD,MAAM,GAAGC;IACZ,CAAC,GAAG,CAAC,CAAC;IACN,OAAOE,QAAQ,CAAC/E,MAAM,CAACgF,EAAE,CAAChF,MAAM,EAAE;MAChC,GAAGL,OAAO;MACV,GAAGqF,EAAE,CAAC3E;IACR,CAAC,CAAC;EACJ,CAAC,EAAE,IAAIxE,KAAK,CAAC,CAAC,CAAC;AACjB;AACA,SAASsE,aAAaA,CAACF,KAAK,EAAEP,IAAI,EAAE;EAClC,IAAIuF,OAAO,GAAG,EAAE;EAChB,KAAK,IAAIC,CAAC,GAAGjF,KAAK,CAACG,GAAG,CAACP,MAAM,GAAG,CAAC,EAAEqF,CAAC,IAAI,CAAC,IAAID,OAAO,CAACpF,MAAM,GAAGH,IAAI,CAACG,MAAM,EAAE,EAAEqF,CAAC,CAAC;EAAA,EAC7E;IACA,MAAMF,EAAE,GAAG/E,KAAK,CAACG,GAAG,CAAC8E,CAAC,CAAC;IACvB,IAAI,OAAOF,EAAE,CAAChF,MAAM,KAAK,QAAQ,EAAE;IACnCiF,OAAO,GAAGD,EAAE,CAAChF,MAAM,GAAGiF,OAAO;EAC/B;EACA,OAAOA,OAAO,CAACE,KAAK,CAAC,CAAC,CAAC,GAAGzF,IAAI,CAACG,MAAM,CAAC,KAAKH,IAAI;AACjD;AACA,SAAS0F,MAAMA,CAACd,IAAI,EAAEhD,MAAM,EAAE;EAC5B,IAAI,EAAEgD,IAAI,YAAYe,OAAO,CAAC,EAAE,OAAO,KAAK;EAC5C,MAAMC,KAAK,GAAGhE,MAAM,CAACwD,KAAK,CAACR,IAAI,CAAC;EAChC;EACA,IAAIgB,KAAK,IAAIA,KAAK,CAACC,SAAS,YAAY7J,SAAS,EAAE,OAAO,KAAK;EAC/D,OAAO,CAAC,SAAS,EAAE,SAAS,EAAE,YAAY,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,YAAY,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC8J,QAAQ,CAAClB,IAAI,CAACZ,OAAO,CAAC+B,WAAW,CAAC,CAAC,CAAC;AACnU;AACA,SAASC,uBAAuBA,CAACpB,IAAI,EAAEhD,MAAM,EAAE;EAC7C,OAAOgD,IAAI,CAACqB,sBAAsB,IAAIrB,IAAI,CAACsB,kBAAkB,IAAI,CAACR,MAAM,CAACd,IAAI,CAACqB,sBAAsB,EAAErE,MAAM,CAAC,IAAI,CAAC8D,MAAM,CAACd,IAAI,CAACsB,kBAAkB,EAAEtE,MAAM,CAAC;AAC3J;AACA,MAAMuE,QAAQ,GAAG,IAAI5E,OAAO,CAAC,CAAC;AAC9B,SAAS6E,KAAKA,CAACxB,IAAI,EAAE;EACnB,IAAIA,IAAI,IAAI,IAAI,EAAE,OAAO,KAAK;EAC9B,IAAI,CAACuB,QAAQ,CAACtB,GAAG,CAACD,IAAI,CAAC,EAAE;IACvB;IACA,IAAIA,IAAI,CAACZ,OAAO,KAAK,KAAK,EAAE;MAC1BmC,QAAQ,CAACnB,GAAG,CAACJ,IAAI,EAAE,IAAI,CAAC;IAC1B,CAAC,MAAM;MACLuB,QAAQ,CAACnB,GAAG,CAACJ,IAAI,EAAEwB,KAAK,CAACxB,IAAI,CAACyB,UAAU,CAAC,CAAC;IAC5C;EACF;EACA,OAAOF,QAAQ,CAACpB,GAAG,CAACH,IAAI,CAAC;AAC3B;AACA,SAASjD,QAAQA,CAACC,MAAM,EAAEgD,IAAI,EAAEpD,eAAe,EAAEC,YAAY,EAAEH,WAAW,EAAE;EAC1E;EACA,IAAIsD,IAAI,CAAC0B,QAAQ,KAAK1B,IAAI,CAACtH,SAAS,EAAE;IACpC,OAAOmE,YAAY,CAACpD,MAAM,CAAC,CAACkC,KAAK,EAAEb,OAAO,KAAK;MAC7C,OAAOA,OAAO,CAACkF,IAAI,EAAErE,KAAK,EAAEqB,MAAM,CAAC;IACrC,CAAC,EAAE,IAAIzF,KAAK,CAAC,CAAC,CAAC;EACjB;EACA,IAAIyI,IAAI,CAAC0B,QAAQ,KAAK1B,IAAI,CAAClH,YAAY,EAAE;IACvC,OAAOgG,KAAK,CAACC,IAAI,CAACiB,IAAI,CAAC2B,UAAU,IAAI,EAAE,CAAC,CAAClI,MAAM,CAAC,CAACkC,KAAK,EAAEiG,SAAS,KAAK;MACpE,IAAIC,aAAa,GAAG9E,QAAQ,CAACC,MAAM,EAAE4E,SAAS,EAAEhF,eAAe,EAAEC,YAAY,EAAEH,WAAW,CAAC;MAC3F,IAAIkF,SAAS,CAACF,QAAQ,KAAK1B,IAAI,CAAClH,YAAY,EAAE;QAC5C+I,aAAa,GAAGjF,eAAe,CAACnD,MAAM,CAAC,CAACqI,YAAY,EAAEhH,OAAO,KAAK;UAChE,OAAOA,OAAO,CAAC8G,SAAS,EAAEE,YAAY,EAAE9E,MAAM,CAAC;QACjD,CAAC,EAAE6E,aAAa,CAAC;QACjBA,aAAa,GAAG,CAACnF,WAAW,CAACyD,GAAG,CAACyB,SAAS,CAAC,IAAI,EAAE,EAAEnI,MAAM,CAAC,CAACqI,YAAY,EAAEhH,OAAO,KAAK;UACnF,OAAOA,OAAO,CAAC8G,SAAS,EAAEE,YAAY,EAAE9E,MAAM,CAAC;QACjD,CAAC,EAAE6E,aAAa,CAAC;MACnB;MACA,OAAOlG,KAAK,CAACjB,MAAM,CAACmH,aAAa,CAAC;IACpC,CAAC,EAAE,IAAItK,KAAK,CAAC,CAAC,CAAC;EACjB;EACA,OAAO,IAAIA,KAAK,CAAC,CAAC;AACpB;AACA,SAAS+B,gBAAgBA,CAACgH,MAAM,EAAE;EAChC,OAAO,CAACyB,KAAK,EAAEpG,KAAK,EAAEqB,MAAM,KAAK;IAC/B,OAAOqD,WAAW,CAAC1E,KAAK,EAAE2E,MAAM,EAAE,IAAI,EAAEtD,MAAM,CAAC;EACjD,CAAC;AACH;AACA,SAAShE,eAAeA,CAACgH,IAAI,EAAErE,KAAK,EAAEqB,MAAM,EAAE;EAC5C,MAAMjB,UAAU,GAAG9E,UAAU,CAAC+K,IAAI,CAAChC,IAAI,CAAC;EACxC,MAAMiC,OAAO,GAAG9K,eAAe,CAAC6K,IAAI,CAAChC,IAAI,CAAC;EAC1C,MAAMkC,MAAM,GAAG5K,eAAe,CAAC0K,IAAI,CAAChC,IAAI,CAAC;EACzC,MAAM3E,OAAO,GAAG,CAAC,CAAC;EAClBU,UAAU,CAACrB,MAAM,CAACuH,OAAO,CAAC,CAACvH,MAAM,CAACwH,MAAM,CAAC,CAACvH,OAAO,CAACwH,IAAI,IAAI;IACxD,IAAIxI,IAAI,GAAGqD,MAAM,CAACwD,KAAK,CAAC2B,IAAI,EAAE9K,KAAK,CAAC+K,SAAS,CAAC;IAC9C,IAAIzI,IAAI,IAAI,IAAI,EAAE;MAChB0B,OAAO,CAAC1B,IAAI,CAAC0I,QAAQ,CAAC,GAAG1I,IAAI,CAAC4G,KAAK,CAACP,IAAI,CAAC;MACzC,IAAI3E,OAAO,CAAC1B,IAAI,CAAC0I,QAAQ,CAAC,EAAE;IAC9B;IACA1I,IAAI,GAAGH,qBAAqB,CAAC2I,IAAI,CAAC;IAClC,IAAIxI,IAAI,IAAI,IAAI,KAAKA,IAAI,CAAC0I,QAAQ,KAAKF,IAAI,IAAIxI,IAAI,CAACC,OAAO,KAAKuI,IAAI,CAAC,EAAE;MACrE9G,OAAO,CAAC1B,IAAI,CAAC0I,QAAQ,CAAC,GAAG1I,IAAI,CAAC4G,KAAK,CAACP,IAAI,CAAC,IAAIxE,SAAS;IACxD;IACA7B,IAAI,GAAGE,iBAAiB,CAACsI,IAAI,CAAC;IAC9B,IAAIxI,IAAI,IAAI,IAAI,KAAKA,IAAI,CAAC0I,QAAQ,KAAKF,IAAI,IAAIxI,IAAI,CAACC,OAAO,KAAKuI,IAAI,CAAC,EAAE;MACrExI,IAAI,GAAGE,iBAAiB,CAACsI,IAAI,CAAC;MAC9B9G,OAAO,CAAC1B,IAAI,CAAC0I,QAAQ,CAAC,GAAG1I,IAAI,CAAC4G,KAAK,CAACP,IAAI,CAAC,IAAIxE,SAAS;IACxD;EACF,CAAC,CAAC;EACF,OAAO8G,MAAM,CAACC,OAAO,CAAClH,OAAO,CAAC,CAAC5B,MAAM,CAAC,CAACgH,QAAQ,EAAE+B,KAAK,KAAK;IACzD,IAAI,CAACL,IAAI,EAAE5B,KAAK,CAAC,GAAGiC,KAAK;IACzB,OAAOnC,WAAW,CAACI,QAAQ,EAAE0B,IAAI,EAAE5B,KAAK,EAAEvD,MAAM,CAAC;EACnD,CAAC,EAAErB,KAAK,CAAC;AACX;AACA,SAAS5C,SAASA,CAACiH,IAAI,EAAErE,KAAK,EAAEqB,MAAM,EAAE;EACtC,MAAMgE,KAAK,GAAGhE,MAAM,CAACwD,KAAK,CAACR,IAAI,CAAC;EAChC,IAAIgB,KAAK,IAAI,IAAI,EAAE,OAAOrF,KAAK;EAC/B;EACA,IAAIqF,KAAK,CAACC,SAAS,YAAY7J,SAAS,EAAE;IACxC,MAAMqL,KAAK,GAAG,CAAC,CAAC;IAChB;IACA,MAAMlC,KAAK,GAAGS,KAAK,CAACT,KAAK,CAACP,IAAI,CAAC;IAC/B,IAAIO,KAAK,IAAI,IAAI,EAAE;MACjB;MACAkC,KAAK,CAACzB,KAAK,CAACvF,QAAQ,CAAC,GAAG8E,KAAK;MAC7B;MACA,OAAO,IAAIhJ,KAAK,CAAC,CAAC,CAACmE,MAAM,CAAC+G,KAAK,EAAEzB,KAAK,CAAC3F,OAAO,CAAC2E,IAAI,EAAEhD,MAAM,CAAC,CAAC;IAC/D;EACF,CAAC,MAAM;IACL;IACA,IAAIgE,KAAK,CAACC,SAAS,YAAY/J,SAAS,IAAI,CAAC2E,aAAa,CAACF,KAAK,EAAE,IAAI,CAAC,EAAE;MACvEA,KAAK,CAACD,MAAM,CAAC,IAAI,CAAC;IACpB;IACA,IAAI,UAAU,IAAIsF,KAAK,IAAI,SAAS,IAAIA,KAAK,IAAI,OAAOA,KAAK,CAAC3F,OAAO,KAAK,UAAU,EAAE;MACpF,OAAOgF,WAAW,CAAC1E,KAAK,EAAEqF,KAAK,CAACvF,QAAQ,EAAEuF,KAAK,CAAC3F,OAAO,CAAC2E,IAAI,EAAEhD,MAAM,CAAC,EAAEA,MAAM,CAAC;IAChF;EACF;EACA,OAAOrB,KAAK;AACd;AACA,SAAS9C,UAAUA,CAACmH,IAAI,EAAErE,KAAK,EAAE;EAC/B,IAAI,CAACE,aAAa,CAACF,KAAK,EAAE,IAAI,CAAC,EAAE;IAC/BA,KAAK,CAACD,MAAM,CAAC,IAAI,CAAC;EACpB;EACA,OAAOC,KAAK;AACd;AACA,SAASvC,cAAcA,CAAC4G,IAAI,EAAErE,KAAK,EAAEqB,MAAM,EAAE;EAC3C,MAAMgE,KAAK,GAAGhE,MAAM,CAACwD,KAAK,CAAC,YAAY,CAAC;EACxC,MAAMkC,QAAQ,GAAG1B,KAAK,IAAI,SAAS,IAAIA,KAAK,IAAI,OAAOA,KAAK,CAAC3F,OAAO,KAAK,UAAU,GAAG2F,KAAK,CAAC3F,OAAO,CAAC2E,IAAI,EAAEhD,MAAM,CAAC,GAAG,IAAI;EACxH,OAAOqD,WAAW,CAAC1E,KAAK,EAAE,YAAY,EAAE+G,QAAQ,EAAE1F,MAAM,CAAC;AAC3D;AACA,SAASzD,WAAWA,CAAA,EAAG;EACrB,OAAO,IAAIhC,KAAK,CAAC,CAAC;AACpB;AACA,SAAS2B,WAAWA,CAAC8G,IAAI,EAAErE,KAAK,EAAEqB,MAAM,EAAE;EACxC,MAAMgE,KAAK,GAAGhE,MAAM,CAACwD,KAAK,CAACR,IAAI,CAAC;EAChC,IAAIgB,KAAK,IAAI,IAAI;EACjB;EACAA,KAAK,CAACvF,QAAQ,KAAK,MAAM,IAAI,CAACI,aAAa,CAACF,KAAK,EAAE,IAAI,CAAC,EAAE;IACxD,OAAOA,KAAK;EACd;EACA,IAAIgH,MAAM,GAAG,CAAC,CAAC;EACf,IAAIC,MAAM,GAAG5C,IAAI,CAACyB,UAAU;EAC5B,OAAOmB,MAAM,IAAI,IAAI,EAAE;IACrB;IACA,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC1B,QAAQ,CAAC0B,MAAM,CAACxD,OAAO,CAAC,EAAE;MACzCuD,MAAM,IAAI,CAAC;IACb;IACAC,MAAM,GAAGA,MAAM,CAACnB,UAAU;EAC5B;EACA,IAAIkB,MAAM,IAAI,CAAC,EAAE,OAAOhH,KAAK;EAC7B,OAAOA,KAAK,CAAClC,MAAM,CAAC,CAACoJ,QAAQ,EAAEnC,EAAE,KAAK;IACpC,IAAI,CAACA,EAAE,CAAChF,MAAM,EAAE,OAAOmH,QAAQ;IAC/B,IAAInC,EAAE,CAAC3E,UAAU,IAAI,OAAO2E,EAAE,CAAC3E,UAAU,CAAC4G,MAAM,KAAK,QAAQ,EAAE;MAC7D,OAAOE,QAAQ,CAAC7H,IAAI,CAAC0F,EAAE,CAAC;IAC1B;IACA,OAAOmC,QAAQ,CAACnH,MAAM,CAACgF,EAAE,CAAChF,MAAM,EAAE;MAChCiH,MAAM;MACN,IAAIjC,EAAE,CAAC3E,UAAU,IAAI,CAAC,CAAC;IACzB,CAAC,CAAC;EACJ,CAAC,EAAE,IAAIxE,KAAK,CAAC,CAAC,CAAC;AACjB;AACA,SAAS4B,SAASA,CAAC6G,IAAI,EAAErE,KAAK,EAAEqB,MAAM,EAAE;EACtC,MAAM8F,OAAO,GAAG9C,IAAI;EACpB,IAAI+C,IAAI,GAAGD,OAAO,CAAC1D,OAAO,KAAK,IAAI,GAAG,SAAS,GAAG,QAAQ;EAC1D,MAAM4D,WAAW,GAAGF,OAAO,CAACG,YAAY,CAAC,cAAc,CAAC;EACxD,IAAID,WAAW,EAAE;IACfD,IAAI,GAAGC,WAAW,KAAK,MAAM,GAAG,SAAS,GAAG,WAAW;EACzD;EACA,OAAO3C,WAAW,CAAC1E,KAAK,EAAE,MAAM,EAAEoH,IAAI,EAAE/F,MAAM,CAAC;AACjD;AACA,SAASpE,YAAYA,CAACoH,IAAI,EAAErE,KAAK,EAAEqB,MAAM,EAAE;EACzC,IAAI,CAACnB,aAAa,CAACF,KAAK,EAAE,IAAI,CAAC,EAAE;IAC/B,IAAImF,MAAM,CAACd,IAAI,EAAEhD,MAAM,CAAC,KAAKgD,IAAI,CAAC2B,UAAU,CAACpG,MAAM,GAAG,CAAC,IAAIyE,IAAI,YAAYkD,oBAAoB,CAAC,EAAE;MAChG,OAAOvH,KAAK,CAACD,MAAM,CAAC,IAAI,CAAC;IAC3B;IACA,IAAIC,KAAK,CAACJ,MAAM,CAAC,CAAC,GAAG,CAAC,IAAIyE,IAAI,CAACmD,WAAW,EAAE;MAC1C,IAAIA,WAAW,GAAGnD,IAAI,CAACmD,WAAW;MAClC,OAAOA,WAAW,IAAI,IAAI,EAAE;QAC1B,IAAIrC,MAAM,CAACqC,WAAW,EAAEnG,MAAM,CAAC,EAAE;UAC/B,OAAOrB,KAAK,CAACD,MAAM,CAAC,IAAI,CAAC;QAC3B;QACA,MAAMsF,KAAK,GAAGhE,MAAM,CAACwD,KAAK,CAAC2C,WAAW,CAAC;QACvC;QACA,IAAInC,KAAK,IAAIA,KAAK,CAACC,SAAS,YAAYzJ,UAAU,EAAE;UAClD,OAAOmE,KAAK,CAACD,MAAM,CAAC,IAAI,CAAC;QAC3B;QACAyH,WAAW,GAAGA,WAAW,CAACC,UAAU;MACtC;IACF;EACF;EACA,OAAOzH,KAAK;AACd;AACA,SAAS1C,WAAWA,CAAC+G,IAAI,EAAErE,KAAK,EAAEqB,MAAM,EAAE;EACxC,MAAM3B,OAAO,GAAG,CAAC,CAAC;EAClB,MAAMgI,KAAK,GAAGrD,IAAI,CAACqD,KAAK,IAAI,CAAC,CAAC;EAC9B,IAAIA,KAAK,CAACC,SAAS,KAAK,QAAQ,EAAE;IAChCjI,OAAO,CAACkI,MAAM,GAAG,IAAI;EACvB;EACA,IAAIF,KAAK,CAACG,cAAc,KAAK,WAAW,EAAE;IACxCnI,OAAO,CAACoI,SAAS,GAAG,IAAI;EAC1B;EACA,IAAIJ,KAAK,CAACG,cAAc,KAAK,cAAc,EAAE;IAC3CnI,OAAO,CAACqI,MAAM,GAAG,IAAI;EACvB;EACA,IAAIL,KAAK,CAACM,UAAU,EAAEC,UAAU,CAAC,MAAM,CAAC;EACxC;EACAC,QAAQ,CAACR,KAAK,CAACM,UAAU,EAAE,EAAE,CAAC,IAAI,GAAG,EAAE;IACrCtI,OAAO,CAACyI,IAAI,GAAG,IAAI;EACrB;EACAnI,KAAK,GAAG2G,MAAM,CAACC,OAAO,CAAClH,OAAO,CAAC,CAAC5B,MAAM,CAAC,CAACgH,QAAQ,EAAEsD,KAAK,KAAK;IAC1D,IAAI,CAAC5B,IAAI,EAAE5B,KAAK,CAAC,GAAGwD,KAAK;IACzB,OAAO1D,WAAW,CAACI,QAAQ,EAAE0B,IAAI,EAAE5B,KAAK,EAAEvD,MAAM,CAAC;EACnD,CAAC,EAAErB,KAAK,CAAC;EACT;EACA,IAAIqI,UAAU,CAACX,KAAK,CAACY,UAAU,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE;IACzC;IACA,OAAO,IAAI1M,KAAK,CAAC,CAAC,CAACmE,MAAM,CAAC,IAAI,CAAC,CAAChB,MAAM,CAACiB,KAAK,CAAC;EAC/C;EACA,OAAOA,KAAK;AACd;AACA,SAAStC,UAAUA,CAAC2G,IAAI,EAAErE,KAAK,EAAEqB,MAAM,EAAE;EACvC,MAAMhB,KAAK,GAAGgE,IAAI,CAACkE,aAAa,EAAE9E,OAAO,KAAK,OAAO,GAAGY,IAAI,CAACkE,aAAa,GAAGlE,IAAI,CAACkE,aAAa,EAAEA,aAAa;EAC9G,IAAIlI,KAAK,IAAI,IAAI,EAAE;IACjB,MAAMmI,IAAI,GAAGrF,KAAK,CAACC,IAAI,CAAC/C,KAAK,CAAC+D,gBAAgB,CAAC,IAAI,CAAC,CAAC;IACrD,MAAMqE,GAAG,GAAGD,IAAI,CAACE,OAAO,CAACrE,IAAI,CAAC,GAAG,CAAC;IAClC,OAAOK,WAAW,CAAC1E,KAAK,EAAE,OAAO,EAAEyI,GAAG,EAAEpH,MAAM,CAAC;EACjD;EACA,OAAOrB,KAAK;AACd;AACA,SAAShD,SAASA,CAACqH,IAAI,EAAErE,KAAK,EAAEqB,MAAM,EAAE;EACtC;EACA,IAAI5B,IAAI,GAAG4E,IAAI,CAACsE,IAAI;EACpB;EACA,IAAItE,IAAI,CAACkE,aAAa,EAAE9E,OAAO,KAAK,KAAK,EAAE;IACzC,OAAOzD,KAAK,CAACD,MAAM,CAACN,IAAI,CAACmJ,IAAI,CAAC,CAAC,CAAC;EAClC;EACA,IAAI,CAAC/C,KAAK,CAACxB,IAAI,CAAC,EAAE;IAChB,IAAI5E,IAAI,CAACmJ,IAAI,CAAC,CAAC,CAAChJ,MAAM,KAAK,CAAC,IAAIH,IAAI,CAAC8F,QAAQ,CAAC,IAAI,CAAC,IAAI,CAACE,uBAAuB,CAACpB,IAAI,EAAEhD,MAAM,CAAC,EAAE;MAC7F,OAAOrB,KAAK;IACd;IACA;IACAP,IAAI,GAAGA,IAAI,CAACoJ,OAAO,CAAC,cAAc,EAAE,GAAG,CAAC;IACxC;IACApJ,IAAI,GAAGA,IAAI,CAACoJ,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC;IAClC,IAAIxE,IAAI,CAACyE,eAAe,IAAI,IAAI,IAAIzE,IAAI,CAACkE,aAAa,IAAI,IAAI,IAAIpD,MAAM,CAACd,IAAI,CAACkE,aAAa,EAAElH,MAAM,CAAC,IAAIgD,IAAI,CAACyE,eAAe,YAAY1D,OAAO,IAAID,MAAM,CAACd,IAAI,CAACyE,eAAe,EAAEzH,MAAM,CAAC,EAAE;MACvL;MACA5B,IAAI,GAAGA,IAAI,CAACoJ,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;IAC/B;IACA,IAAIxE,IAAI,CAACmD,WAAW,IAAI,IAAI,IAAInD,IAAI,CAACkE,aAAa,IAAI,IAAI,IAAIpD,MAAM,CAACd,IAAI,CAACkE,aAAa,EAAElH,MAAM,CAAC,IAAIgD,IAAI,CAACmD,WAAW,YAAYpC,OAAO,IAAID,MAAM,CAACd,IAAI,CAACmD,WAAW,EAAEnG,MAAM,CAAC,EAAE;MAC3K;MACA5B,IAAI,GAAGA,IAAI,CAACoJ,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;IAC/B;IACA;IACApJ,IAAI,GAAGA,IAAI,CAACsJ,UAAU,CAAC,QAAQ,EAAE,GAAG,CAAC;EACvC;EACA,OAAO/I,KAAK,CAACD,MAAM,CAACN,IAAI,CAAC;AAC3B;AACA,SAAStB,SAAS,IAAI6K,OAAO,EAAE3L,eAAe,EAAED,SAAS,EAAEH,YAAY,EAAED,SAAS,EAAEoE,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}