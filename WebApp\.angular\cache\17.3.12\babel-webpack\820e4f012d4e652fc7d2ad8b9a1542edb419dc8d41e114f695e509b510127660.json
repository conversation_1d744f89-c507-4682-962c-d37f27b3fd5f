{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/source/ai-hub/WebApp/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { TestBed } from '@angular/core/testing';\nimport { NewNoteBookComponent } from './new-note-book.component';\ndescribe('NewNoteBookComponent', () => {\n  let component;\n  let fixture;\n  beforeEach( /*#__PURE__*/_asyncToGenerator(function* () {\n    yield TestBed.configureTestingModule({\n      imports: [NewNoteBookComponent]\n    }).compileComponents();\n    fixture = TestBed.createComponent(NewNoteBookComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  }));\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "NewNoteBookComponent", "describe", "component", "fixture", "beforeEach", "_asyncToGenerator", "configureTestingModule", "imports", "compileComponents", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["C:\\Users\\<USER>\\source\\ai-hub\\WebApp\\src\\app\\MyNotesProjects\\new-note-book\\new-note-book.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed } from '@angular/core/testing';\r\n\r\nimport { NewNoteBookComponent } from './new-note-book.component';\r\n\r\ndescribe('NewNoteBookComponent', () => {\r\n  let component: NewNoteBookComponent;\r\n  let fixture: ComponentFixture<NewNoteBookComponent>;\r\n\r\n  beforeEach(async () => {\r\n    await TestBed.configureTestingModule({\r\n      imports: [NewNoteBookComponent]\r\n    })\r\n    .compileComponents();\r\n\r\n    fixture = TestBed.createComponent(NewNoteBookComponent);\r\n    component = fixture.componentInstance;\r\n    fixture.detectChanges();\r\n  });\r\n\r\n  it('should create', () => {\r\n    expect(component).toBeTruthy();\r\n  });\r\n});\r\n"], "mappings": ";AAAA,SAA2BA,OAAO,QAAQ,uBAAuB;AAEjE,SAASC,oBAAoB,QAAQ,2BAA2B;AAEhEC,QAAQ,CAAC,sBAAsB,EAAE,MAAK;EACpC,IAAIC,SAA+B;EACnC,IAAIC,OAA+C;EAEnDC,UAAU,eAAAC,iBAAA,CAAC,aAAW;IACpB,MAAMN,OAAO,CAACO,sBAAsB,CAAC;MACnCC,OAAO,EAAE,CAACP,oBAAoB;KAC/B,CAAC,CACDQ,iBAAiB,EAAE;IAEpBL,OAAO,GAAGJ,OAAO,CAACU,eAAe,CAACT,oBAAoB,CAAC;IACvDE,SAAS,GAAGC,OAAO,CAACO,iBAAiB;IACrCP,OAAO,CAACQ,aAAa,EAAE;EACzB,CAAC,EAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACX,SAAS,CAAC,CAACY,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}