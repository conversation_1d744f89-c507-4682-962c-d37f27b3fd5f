{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./notes-sidebar.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./notes-sidebar.component.css?ngResource\";\nimport { CommonModule } from '@angular/common';\nimport { Component, Input, inject } from '@angular/core';\nimport { Router, NavigationEnd } from '@angular/router';\nimport { filter } from 'rxjs/operators';\nimport { ThemeService } from '../../../../shared/services/theam.service';\nimport { DocsServiceProxy } from '../../../../shared/service-proxies/service-proxies';\nimport { DocumentSyncService } from '../../../shared/services/document-sync.service';\nimport { ActiveDocumentService } from '../../../shared/services/active-document.service';\nlet NotesSidebarComponent = class NotesSidebarComponent {\n  constructor(docsService, router) {\n    this.docsService = docsService;\n    this.router = router;\n    this.workspaceName = '';\n    this.favorites = [];\n    this.recentDocuments = [];\n    this.allDocuments = [];\n    this.allNotes = []; // Complete list of all notes for the workspace\n    this.isPublicRoute = false;\n    this.activeFilter = 'all';\n    this.subscriptions = [];\n    this.lastSelectedDocumentId = null;\n    this.selectionDebounceTimeout = null;\n    // Inject services\n    this.themeService = inject(ThemeService);\n    this.documentSyncService = inject(DocumentSyncService);\n    this.activeDocumentService = inject(ActiveDocumentService);\n  }\n  ngOnInit() {\n    // Determine if this is a public route\n    this.isPublicRoute = this.router.url.includes('/notes');\n    // Set workspace name based on route\n    if (this.isPublicRoute) {\n      this.workspaceName = 'GlobalNotes';\n    } else {\n      // Extract workspace name from URL if not provided via @Input\n      if (!this.workspaceName) {\n        const urlSegments = this.router.url.split('/');\n        if (urlSegments.includes('workspaces') && urlSegments.length > 2) {\n          const workspaceIndex = urlSegments.indexOf('workspaces');\n          this.workspaceName = decodeURIComponent(urlSegments[workspaceIndex + 1]);\n        } else {\n          this.workspaceName = 'GlobalNotes'; // Fallback\n        }\n      }\n    }\n    console.log('🚀 Notes sidebar initialized for workspace:', this.workspaceName);\n    console.log('📍 Is public route:', this.isPublicRoute);\n    console.log('🔗 Current URL:', this.router.url);\n    this.loadSidebarData();\n    // Subscribe to active document changes from the service\n    const activeDocumentSubscription = this.activeDocumentService.activeDocument$.subscribe(activeDoc => {\n      console.log('📄 Active document changed:', activeDoc);\n      // The component will automatically update through the isDocumentActive method\n    });\n    // Subscribe to route changes to refresh data when navigating\n    const routeSubscription = this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(() => {\n      const currentRoute = this.router.url;\n      const wasPublicRoute = this.isPublicRoute;\n      this.isPublicRoute = currentRoute.includes('/notes');\n      // Update workspace name if route type changed\n      if (this.isPublicRoute !== wasPublicRoute) {\n        if (this.isPublicRoute) {\n          this.workspaceName = 'GlobalNotes';\n        }\n        this.loadSidebarData();\n      }\n    });\n    // Subscribe to document updates to refresh sidebar data\n    const documentUpdateSubscription = this.documentSyncService.documentUpdated$.subscribe(event => {\n      console.log('Notes sidebar received document update event:', event);\n      console.log('Current workspace:', this.workspaceName);\n      console.log('Event workspace:', event.workspaceName);\n      // Check workspace matching more carefully\n      const isWorkspaceMatch = !event.workspaceName || event.workspaceName === this.workspaceName || event.workspaceName === 'GlobalNotes' && this.workspaceName === 'GlobalNotes';\n      if (isWorkspaceMatch) {\n        console.log('✅ Workspace match - processing event for workspace:', this.workspaceName);\n        // Handle different types of document events with optimized reloading\n        if (event.type === 'created' && event.document) {\n          console.log('🆕 Adding new document to recent list immediately:', event.document);\n          this.addNewDocumentToRecent(event.document);\n          // Only reload all notes for new documents to include them in the complete list\n          setTimeout(() => {\n            console.log('🔄 Reloading all notes for new document');\n            this.loadAllNotes();\n          }, 1000);\n        } else if (event.type === 'favorited' && event.document) {\n          console.log('🌟 Document favorite status changed:', event.document);\n          this.handleFavoriteChange(event.document);\n          // Only reload favorites for favorite changes\n          setTimeout(() => {\n            console.log('🔄 Reloading favorites for favorite change');\n            this.loadFavoritesOnly();\n          }, 200);\n        } else if (event.type === 'deleted' && event.document) {\n          console.log('🗑️ Document deleted, refreshing all data');\n          // For deletions, we need to refresh all data\n          setTimeout(() => {\n            console.log('🔄 Refreshing all data for deletion');\n            this.loadSidebarData();\n          }, 100);\n        }\n        // Note: No automatic reload for 'updated' events to prevent unnecessary reloads on document selection\n      } else {\n        console.log('❌ Workspace mismatch - ignoring event');\n      }\n    });\n    this.subscriptions.push(activeDocumentSubscription, routeSubscription, documentUpdateSubscription);\n  }\n  ngOnChanges() {\n    // Reload data when workspace changes\n    if (this.workspaceName) {\n      console.log('🔄 Workspace changed, reloading data for:', this.workspaceName);\n      this.loadSidebarData();\n    }\n  }\n  ngOnDestroy() {\n    // Unsubscribe from all subscriptions to prevent memory leaks\n    this.subscriptions.forEach(subscription => subscription.unsubscribe());\n    // Clear any pending debounce timeout\n    if (this.selectionDebounceTimeout) {\n      clearTimeout(this.selectionDebounceTimeout);\n    }\n  }\n  // Filter methods\n  toggleFilter(filter) {\n    this.activeFilter = filter;\n  }\n  getFilteredDocuments() {\n    switch (this.activeFilter) {\n      case 'favorites':\n        return this.favorites;\n      case 'recent':\n        return this.recentDocuments;\n      case 'all':\n      default:\n        return this.allNotes;\n      // Use complete notes list for \"All Notes\" filter\n    }\n  }\n  getCurrentFilterTitle() {\n    switch (this.activeFilter) {\n      case 'favorites':\n        return 'Favorites';\n      case 'recent':\n        return 'Recent';\n      case 'all':\n      default:\n        return 'All Notes';\n    }\n  }\n  loadSidebarData() {\n    this.updateFavoritesAndRecent();\n    this.loadAllNotes();\n  }\n  // Public method to refresh data (can be called from parent components)\n  refreshData() {\n    this.loadSidebarData();\n  }\n  updateFavoritesAndRecent() {\n    console.log('Updating favorites and recent documents for workspace:', this.workspaceName);\n    // Get favorites from API\n    this.docsService.getFavorites(this.workspaceName).subscribe(data => {\n      console.log('Loaded favorites:', data);\n      this.favorites = data;\n      this.updateAllDocuments();\n      // Update favorite status in all notes after loading favorites\n      this.updateFavoriteStatusInAllNotes();\n    }, error => {\n      console.error('Error loading favorites:', error);\n      this.favorites = [];\n    });\n    // Get recently opened documents from API\n    this.docsService.getRecentlyOpened(this.workspaceName, 5).subscribe(data => {\n      console.log('Loaded recent documents:', data);\n      this.recentDocuments = data;\n      this.updateAllDocuments();\n    }, error => {\n      console.error('Error loading recent documents:', error);\n      this.recentDocuments = [];\n    });\n  }\n  updateAllDocuments() {\n    // Combine favorites and recent documents, removing duplicates\n    const allDocs = [...this.favorites, ...this.recentDocuments];\n    const uniqueDocs = allDocs.filter((doc, index, self) => index === self.findIndex(d => d.id === doc.id));\n    this.allDocuments = uniqueDocs;\n  }\n  loadAllNotes() {\n    console.log('Loading all notes for workspace:', this.workspaceName);\n    // Get all documents from API using getByWorkspaceName\n    this.docsService.getByWorkspaceName(this.workspaceName).subscribe(data => {\n      console.log('Loaded all notes:', data);\n      this.allNotes = data || [];\n      // Update favorite status for all notes based on favorites list\n      this.updateFavoriteStatusInAllNotes();\n    }, error => {\n      console.error('Error loading all notes:', error);\n      this.allNotes = [];\n    });\n  }\n  loadFavoritesOnly() {\n    console.log('Loading favorites only for workspace:', this.workspaceName);\n    // Get favorites from API\n    this.docsService.getFavorites(this.workspaceName).subscribe(data => {\n      console.log('Loaded favorites only:', data);\n      this.favorites = data;\n      // Update favorite status in all notes after loading favorites\n      this.updateFavoriteStatusInAllNotes();\n    }, error => {\n      console.error('Error loading favorites only:', error);\n      this.favorites = [];\n    });\n  }\n  updateFavoriteStatusInAllNotes() {\n    // Update the favorite status for all notes based on the favorites list\n    this.allNotes = this.allNotes.map(note => ({\n      ...note,\n      isFavorite: this.favorites.some(fav => fav.id === note.id)\n    }));\n  }\n  selectDocument(document) {\n    // Set the active document using the service for instant UI feedback\n    this.activeDocumentService.setActiveDocument(document, {\n      workspaceName: this.workspaceName,\n      route: this.isPublicRoute ? 'notes' : 'workspace-documents'\n    });\n    console.log('📄 Document selected, setting as active:', document.title);\n    this.addToRecentDocuments(document);\n    // Navigate to the document\n    if (this.isPublicRoute) {\n      this.router.navigate(['/notes'], {\n        queryParams: {\n          docId: document.id\n        }\n      });\n    } else {\n      this.router.navigate(['/workspaces', this.workspaceName, 'documents'], {\n        queryParams: {\n          docId: document.id\n        }\n      });\n    }\n  }\n  addDocument(event) {\n    event.stopImmediatePropagation();\n    // Navigate to add document\n    if (this.isPublicRoute) {\n      this.router.navigate(['/notes'], {\n        queryParams: {\n          action: 'add'\n        }\n      });\n    } else {\n      this.router.navigate(['/workspaces', this.workspaceName, 'documents'], {\n        queryParams: {\n          action: 'add'\n        }\n      });\n    }\n  }\n  showDocumentsList() {\n    // Navigate to documents list\n    console.log('🔗 View All button clicked');\n    console.log('📍 Is public route:', this.isPublicRoute);\n    console.log('📁 Workspace name:', this.workspaceName);\n    if (this.isPublicRoute) {\n      console.log('🔗 Navigating to public notes: /notes');\n      this.router.navigate(['/notes']);\n    } else {\n      console.log('🔗 Navigating to workspace documents:', `/workspaces/${this.workspaceName}/documents`);\n      this.router.navigate(['/workspaces', this.workspaceName, 'documents']);\n    }\n  }\n  isDocumentFavorite(document) {\n    return this.favorites.some(fav => fav.id === document.id);\n  }\n  isDocumentActive(document) {\n    return this.activeDocumentService.isDocumentActive(document.id);\n  }\n  toggleFavorite(document) {\n    const isFavorite = this.isDocumentFavorite(document);\n    // Immediately update local state for better UX\n    if (isFavorite) {\n      this.favorites = this.favorites.filter(fav => fav.id !== document.id);\n    } else {\n      this.favorites.push({\n        ...document,\n        isFavorite: true\n      });\n    }\n    // Update favorite status in all notes array immediately\n    this.updateFavoriteStatusInAllNotes();\n    // Use the API to update favorite status (fire and forget for performance)\n    this.docsService.updateFavoriteStatus({\n      id: document.id,\n      isFavorite: !isFavorite\n    }).subscribe(() => {\n      // API call successful - no additional action needed since we already updated local state\n      console.log('Favorite status updated successfully for:', document.title);\n      // Notify other components about the favorite change\n      this.documentSyncService.notifyDocumentFavorited({\n        ...document,\n        isFavorite: !isFavorite\n      }, this.workspaceName);\n    }, error => {\n      console.error('Error updating favorite status:', error);\n      // Revert local state changes on API error\n      if (!isFavorite) {\n        this.favorites = this.favorites.filter(fav => fav.id !== document.id);\n      } else {\n        this.favorites.push({\n          ...document,\n          isFavorite: true\n        });\n      }\n      this.updateFavoriteStatusInAllNotes();\n    });\n  }\n  addToRecentDocuments(document) {\n    // Immediately update local state for better UX\n    this.recentDocuments = this.recentDocuments.filter(doc => doc.id !== document.id);\n    this.recentDocuments.unshift(document);\n    if (this.recentDocuments.length > 5) {\n      this.recentDocuments = this.recentDocuments.slice(0, 5);\n    }\n    // Debounce API calls to prevent excessive requests when users click rapidly\n    if (this.selectionDebounceTimeout) {\n      clearTimeout(this.selectionDebounceTimeout);\n    }\n    // Only make API call if this is a different document or after debounce delay\n    if (this.lastSelectedDocumentId !== document.id) {\n      this.lastSelectedDocumentId = document.id;\n      this.selectionDebounceTimeout = setTimeout(() => {\n        // Use the API to track document opens (fire and forget for performance)\n        this.docsService.trackDocumentOpen({\n          id: document.id\n        }).subscribe(() => {\n          // API call successful - no additional action needed since we already updated local state\n          console.log('Document open tracked successfully for:', document.title);\n        }, error => {\n          console.error('Error tracking document open:', error);\n          // Note: We don't revert local state changes on API error to maintain good UX\n        });\n      }, 300); // 300ms debounce delay\n    }\n  }\n  addNewDocumentToRecent(document) {\n    // Immediately add the new document to the recent list for instant visibility\n    if (document && document.id) {\n      console.log('🆕 Adding new document to recent list:', document);\n      // Remove if already exists (shouldn't happen for new docs, but just in case)\n      this.recentDocuments = this.recentDocuments.filter(doc => doc.id !== document.id);\n      // Add to the beginning of the list\n      this.recentDocuments.unshift(document);\n      // Keep only the latest 5\n      if (this.recentDocuments.length > 5) {\n        this.recentDocuments = this.recentDocuments.slice(0, 5);\n      }\n      console.log('✅ Updated recent documents list:', this.recentDocuments);\n      // Force change detection to ensure UI updates\n      setTimeout(() => {\n        console.log('🔄 Forcing UI update for new document');\n      }, 0);\n    }\n  }\n  handleFavoriteChange(document) {\n    // Handle immediate favorite status changes in the local lists\n    if (document && document.id) {\n      console.log('🌟 Handling favorite change for document:', document);\n      const isFavorite = document.isFavorite || document.isFavourite;\n      if (isFavorite) {\n        // Add to favorites if not already there\n        const existsInFavorites = this.favorites.some(fav => fav.id === document.id);\n        if (!existsInFavorites) {\n          this.favorites.push(document);\n          console.log('✅ Added to favorites list');\n        }\n      } else {\n        // Remove from favorites\n        this.favorites = this.favorites.filter(fav => fav.id !== document.id);\n        console.log('✅ Removed from favorites list');\n      }\n      // Update the document in recent list if it exists there\n      const recentIndex = this.recentDocuments.findIndex(doc => doc.id === document.id);\n      if (recentIndex !== -1) {\n        this.recentDocuments[recentIndex] = {\n          ...this.recentDocuments[recentIndex],\n          ...document\n        };\n        console.log('✅ Updated document in recent list');\n      }\n    }\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: DocsServiceProxy\n    }, {\n      type: Router\n    }];\n  }\n  static {\n    this.propDecorators = {\n      workspaceName: [{\n        type: Input\n      }]\n    };\n  }\n};\nNotesSidebarComponent = __decorate([Component({\n  selector: 'app-notes-sidebar',\n  standalone: true,\n  imports: [CommonModule],\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], NotesSidebarComponent);\nexport { NotesSidebarComponent };", "map": {"version": 3, "names": ["CommonModule", "Component", "Input", "inject", "Router", "NavigationEnd", "filter", "ThemeService", "DocsServiceProxy", "DocumentSyncService", "ActiveDocumentService", "NotesSidebarComponent", "constructor", "docsService", "router", "workspaceName", "favorites", "recentDocuments", "allDocuments", "allNotes", "isPublicRoute", "activeFilter", "subscriptions", "lastSelectedDocumentId", "selectionDebounceTimeout", "themeService", "documentSyncService", "activeDocumentService", "ngOnInit", "url", "includes", "urlSegments", "split", "length", "workspaceIndex", "indexOf", "decodeURIComponent", "console", "log", "loadSidebarData", "activeDocumentSubscription", "activeDocument$", "subscribe", "activeDoc", "routeSubscription", "events", "pipe", "event", "currentRoute", "wasPublicRoute", "documentUpdateSubscription", "documentUpdated$", "isWorkspaceMatch", "type", "document", "addNewDocumentToRecent", "setTimeout", "loadAllNotes", "handleFavoriteChange", "loadFavoritesOnly", "push", "ngOnChanges", "ngOnDestroy", "for<PERSON>ach", "subscription", "unsubscribe", "clearTimeout", "toggleFilter", "getFilteredDocuments", "getCurrentFilterTitle", "updateFavoritesAndRecent", "refreshData", "getFavorites", "data", "updateAllDocuments", "updateFavoriteStatusInAllNotes", "error", "getRecentlyOpened", "allDocs", "uniqueDocs", "doc", "index", "self", "findIndex", "d", "id", "getByWorkspaceName", "map", "note", "isFavorite", "some", "fav", "selectDocument", "setActiveDocument", "route", "title", "addToRecentDocuments", "navigate", "queryParams", "docId", "addDocument", "stopImmediatePropagation", "action", "showDocumentsList", "isDocumentFavorite", "isDocumentActive", "toggleFavorite", "updateFavoriteStatus", "notifyDocumentFavorited", "unshift", "slice", "trackDocumentOpen", "isFavourite", "existsInFavorites", "recentIndex", "__decorate", "selector", "standalone", "imports", "template", "__NG_CLI_RESOURCE__0"], "sources": ["C:\\Users\\<USER>\\source\\ai-hub\\WebApp\\src\\app\\components\\@leftSideComponents\\notes-sidebar\\notes-sidebar.component.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\nimport { Component, Input, Output, EventEmitter, OnInit, OnChanges, OnDestroy, inject } from '@angular/core';\nimport { Router, NavigationEnd } from '@angular/router';\nimport { firstValueFrom, Subscription } from 'rxjs';\nimport { filter } from 'rxjs/operators';\nimport { ThemeService } from '../../../../shared/services/theam.service';\nimport { DocsServiceProxy, UpdateFavoriteDto, TrackDocumentOpenDto } from '../../../../shared/service-proxies/service-proxies';\nimport { DocumentSyncService, DocumentUpdateEvent } from '../../../shared/services/document-sync.service';\nimport { ActiveDocumentService } from '../../../shared/services/active-document.service';\n\n@Component({\n  selector: 'app-notes-sidebar',\n  standalone: true,\n  imports: [CommonModule],\n  templateUrl: './notes-sidebar.component.html',\n  styleUrl: './notes-sidebar.component.css'\n})\nexport class NotesSidebarComponent implements OnInit, OnChanges, OnDestroy {\n  @Input() workspaceName: string = '';\n\n  favorites: any[] = [];\n  recentDocuments: any[] = [];\n  allDocuments: any[] = [];\n  allNotes: any[] = []; // Complete list of all notes for the workspace\n  isPublicRoute: boolean = false;\n  activeFilter: 'all' | 'favorites' | 'recent' = 'all';\n\n  private subscriptions: Subscription[] = [];\n  private lastSelectedDocumentId: number | null = null;\n  private selectionDebounceTimeout: any = null;\n\n  // Inject services\n  themeService = inject(ThemeService);\n  documentSyncService = inject(DocumentSyncService);\n  activeDocumentService = inject(ActiveDocumentService);\n\n  constructor(\n    private docsService: DocsServiceProxy,\n    private router: Router\n  ) { }\n\n  ngOnInit() {\n    // Determine if this is a public route\n    this.isPublicRoute = this.router.url.includes('/notes');\n\n    // Set workspace name based on route\n    if (this.isPublicRoute) {\n      this.workspaceName = 'GlobalNotes';\n    } else {\n      // Extract workspace name from URL if not provided via @Input\n      if (!this.workspaceName) {\n        const urlSegments = this.router.url.split('/');\n        if (urlSegments.includes('workspaces') && urlSegments.length > 2) {\n          const workspaceIndex = urlSegments.indexOf('workspaces');\n          this.workspaceName = decodeURIComponent(urlSegments[workspaceIndex + 1]);\n        } else {\n          this.workspaceName = 'GlobalNotes'; // Fallback\n        }\n      }\n    }\n\n    console.log('🚀 Notes sidebar initialized for workspace:', this.workspaceName);\n    console.log('📍 Is public route:', this.isPublicRoute);\n    console.log('🔗 Current URL:', this.router.url);\n\n    this.loadSidebarData();\n\n    // Subscribe to active document changes from the service\n    const activeDocumentSubscription = this.activeDocumentService.activeDocument$.subscribe(activeDoc => {\n      console.log('📄 Active document changed:', activeDoc);\n      // The component will automatically update through the isDocumentActive method\n    });\n\n    // Subscribe to route changes to refresh data when navigating\n    const routeSubscription = this.router.events\n      .pipe(filter(event => event instanceof NavigationEnd))\n      .subscribe(() => {\n        const currentRoute = this.router.url;\n        const wasPublicRoute = this.isPublicRoute;\n        this.isPublicRoute = currentRoute.includes('/notes');\n\n        // Update workspace name if route type changed\n        if (this.isPublicRoute !== wasPublicRoute) {\n          if (this.isPublicRoute) {\n            this.workspaceName = 'GlobalNotes';\n          }\n          this.loadSidebarData();\n        }\n      });\n\n    // Subscribe to document updates to refresh sidebar data\n    const documentUpdateSubscription = this.documentSyncService.documentUpdated$.subscribe((event: DocumentUpdateEvent) => {\n      console.log('Notes sidebar received document update event:', event);\n      console.log('Current workspace:', this.workspaceName);\n      console.log('Event workspace:', event.workspaceName);\n\n      // Check workspace matching more carefully\n      const isWorkspaceMatch = !event.workspaceName ||\n                              event.workspaceName === this.workspaceName ||\n                              (event.workspaceName === 'GlobalNotes' && this.workspaceName === 'GlobalNotes');\n\n      if (isWorkspaceMatch) {\n        console.log('✅ Workspace match - processing event for workspace:', this.workspaceName);\n\n        // Handle different types of document events with optimized reloading\n        if (event.type === 'created' && event.document) {\n          console.log('🆕 Adding new document to recent list immediately:', event.document);\n          this.addNewDocumentToRecent(event.document);\n          // Only reload all notes for new documents to include them in the complete list\n          setTimeout(() => {\n            console.log('🔄 Reloading all notes for new document');\n            this.loadAllNotes();\n          }, 1000);\n        } else if (event.type === 'favorited' && event.document) {\n          console.log('🌟 Document favorite status changed:', event.document);\n          this.handleFavoriteChange(event.document);\n          // Only reload favorites for favorite changes\n          setTimeout(() => {\n            console.log('🔄 Reloading favorites for favorite change');\n            this.loadFavoritesOnly();\n          }, 200);\n        } else if (event.type === 'deleted' && event.document) {\n          console.log('🗑️ Document deleted, refreshing all data');\n          // For deletions, we need to refresh all data\n          setTimeout(() => {\n            console.log('🔄 Refreshing all data for deletion');\n            this.loadSidebarData();\n          }, 100);\n        }\n        // Note: No automatic reload for 'updated' events to prevent unnecessary reloads on document selection\n      } else {\n        console.log('❌ Workspace mismatch - ignoring event');\n      }\n    });\n\n    this.subscriptions.push(activeDocumentSubscription, routeSubscription, documentUpdateSubscription);\n  }\n\n  ngOnChanges() {\n    // Reload data when workspace changes\n    if (this.workspaceName) {\n      console.log('🔄 Workspace changed, reloading data for:', this.workspaceName);\n      this.loadSidebarData();\n    }\n  }\n\n  ngOnDestroy() {\n    // Unsubscribe from all subscriptions to prevent memory leaks\n    this.subscriptions.forEach(subscription => subscription.unsubscribe());\n\n    // Clear any pending debounce timeout\n    if (this.selectionDebounceTimeout) {\n      clearTimeout(this.selectionDebounceTimeout);\n    }\n  }\n\n  // Filter methods\n  toggleFilter(filter: 'all' | 'favorites' | 'recent') {\n    this.activeFilter = filter;\n  }\n\n  getFilteredDocuments(): any[] {\n    switch (this.activeFilter) {\n      case 'favorites':\n        return this.favorites;\n      case 'recent':\n        return this.recentDocuments;\n      case 'all':\n      default:\n        return this.allNotes; // Use complete notes list for \"All Notes\" filter\n    }\n  }\n\n  getCurrentFilterTitle(): string {\n    switch (this.activeFilter) {\n      case 'favorites':\n        return 'Favorites';\n      case 'recent':\n        return 'Recent';\n      case 'all':\n      default:\n        return 'All Notes';\n    }\n  }\n\n  loadSidebarData() {\n    this.updateFavoritesAndRecent();\n    this.loadAllNotes();\n  }\n\n  // Public method to refresh data (can be called from parent components)\n  refreshData() {\n    this.loadSidebarData();\n  }\n\n  updateFavoritesAndRecent() {\n    console.log('Updating favorites and recent documents for workspace:', this.workspaceName);\n\n    // Get favorites from API\n    this.docsService.getFavorites(this.workspaceName).subscribe(\n      (data: any) => {\n        console.log('Loaded favorites:', data);\n        this.favorites = data;\n        this.updateAllDocuments();\n        // Update favorite status in all notes after loading favorites\n        this.updateFavoriteStatusInAllNotes();\n      },\n      (error: any) => {\n        console.error('Error loading favorites:', error);\n        this.favorites = [];\n      }\n    );\n\n    // Get recently opened documents from API\n    this.docsService.getRecentlyOpened(this.workspaceName, 5).subscribe(\n      (data: any) => {\n        console.log('Loaded recent documents:', data);\n        this.recentDocuments = data;\n        this.updateAllDocuments();\n      },\n      (error: any) => {\n        console.error('Error loading recent documents:', error);\n        this.recentDocuments = [];\n      }\n    );\n  }\n\n  private updateAllDocuments() {\n    // Combine favorites and recent documents, removing duplicates\n    const allDocs = [...this.favorites, ...this.recentDocuments];\n    const uniqueDocs = allDocs.filter((doc, index, self) =>\n      index === self.findIndex(d => d.id === doc.id)\n    );\n    this.allDocuments = uniqueDocs;\n  }\n\n  loadAllNotes() {\n    console.log('Loading all notes for workspace:', this.workspaceName);\n\n    // Get all documents from API using getByWorkspaceName\n    this.docsService.getByWorkspaceName(this.workspaceName).subscribe(\n      (data: any) => {\n        console.log('Loaded all notes:', data);\n        this.allNotes = data || [];\n\n        // Update favorite status for all notes based on favorites list\n        this.updateFavoriteStatusInAllNotes();\n      },\n      (error: any) => {\n        console.error('Error loading all notes:', error);\n        this.allNotes = [];\n      }\n    );\n  }\n\n  loadFavoritesOnly() {\n    console.log('Loading favorites only for workspace:', this.workspaceName);\n\n    // Get favorites from API\n    this.docsService.getFavorites(this.workspaceName).subscribe(\n      (data: any) => {\n        console.log('Loaded favorites only:', data);\n        this.favorites = data;\n        // Update favorite status in all notes after loading favorites\n        this.updateFavoriteStatusInAllNotes();\n      },\n      (error: any) => {\n        console.error('Error loading favorites only:', error);\n        this.favorites = [];\n      }\n    );\n  }\n\n  private updateFavoriteStatusInAllNotes() {\n    // Update the favorite status for all notes based on the favorites list\n    this.allNotes = this.allNotes.map(note => ({\n      ...note,\n      isFavorite: this.favorites.some(fav => fav.id === note.id)\n    }));\n  }\n\n  selectDocument(document: any) {\n    // Set the active document using the service for instant UI feedback\n    this.activeDocumentService.setActiveDocument(document, {\n      workspaceName: this.workspaceName,\n      route: this.isPublicRoute ? 'notes' : 'workspace-documents'\n    });\n    console.log('📄 Document selected, setting as active:', document.title);\n\n    this.addToRecentDocuments(document);\n\n    // Navigate to the document\n    if (this.isPublicRoute) {\n      this.router.navigate(['/notes'], { queryParams: { docId: document.id } });\n    } else {\n      this.router.navigate(['/workspaces', this.workspaceName, 'documents'], { queryParams: { docId: document.id } });\n    }\n  }\n\n  addDocument(event: Event) {\n    event.stopImmediatePropagation();\n\n    // Navigate to add document\n    if (this.isPublicRoute) {\n      this.router.navigate(['/notes'], { queryParams: { action: 'add' } });\n    } else {\n      this.router.navigate(['/workspaces', this.workspaceName, 'documents'], { queryParams: { action: 'add' } });\n    }\n  }\n\n  showDocumentsList() {\n    // Navigate to documents list\n    console.log('🔗 View All button clicked');\n    console.log('📍 Is public route:', this.isPublicRoute);\n    console.log('📁 Workspace name:', this.workspaceName);\n\n    if (this.isPublicRoute) {\n      console.log('🔗 Navigating to public notes: /notes');\n      this.router.navigate(['/notes']);\n    } else {\n      console.log('🔗 Navigating to workspace documents:', `/workspaces/${this.workspaceName}/documents`);\n      this.router.navigate(['/workspaces', this.workspaceName, 'documents']);\n    }\n  }\n\n  isDocumentFavorite(document: any): boolean {\n    return this.favorites.some((fav) => fav.id === document.id);\n  }\n\n  isDocumentActive(document: any): boolean {\n    return this.activeDocumentService.isDocumentActive(document.id);\n  }\n\n  toggleFavorite(document: any) {\n    const isFavorite = this.isDocumentFavorite(document);\n\n    // Immediately update local state for better UX\n    if (isFavorite) {\n      this.favorites = this.favorites.filter((fav) => fav.id !== document.id);\n    } else {\n      this.favorites.push({ ...document, isFavorite: true });\n    }\n\n    // Update favorite status in all notes array immediately\n    this.updateFavoriteStatusInAllNotes();\n\n    // Use the API to update favorite status (fire and forget for performance)\n    this.docsService.updateFavoriteStatus({\n      id: document.id,\n      isFavorite: !isFavorite\n    } as UpdateFavoriteDto).subscribe(\n      () => {\n        // API call successful - no additional action needed since we already updated local state\n        console.log('Favorite status updated successfully for:', document.title);\n\n        // Notify other components about the favorite change\n        this.documentSyncService.notifyDocumentFavorited(\n          { ...document, isFavorite: !isFavorite },\n          this.workspaceName\n        );\n      },\n      (error: any) => {\n        console.error('Error updating favorite status:', error);\n        // Revert local state changes on API error\n        if (!isFavorite) {\n          this.favorites = this.favorites.filter((fav) => fav.id !== document.id);\n        } else {\n          this.favorites.push({ ...document, isFavorite: true });\n        }\n        this.updateFavoriteStatusInAllNotes();\n      }\n    );\n  }\n\n  addToRecentDocuments(document: any) {\n    // Immediately update local state for better UX\n    this.recentDocuments = this.recentDocuments.filter(\n      (doc) => doc.id !== document.id\n    );\n    this.recentDocuments.unshift(document);\n    if (this.recentDocuments.length > 5) {\n      this.recentDocuments = this.recentDocuments.slice(0, 5);\n    }\n\n    // Debounce API calls to prevent excessive requests when users click rapidly\n    if (this.selectionDebounceTimeout) {\n      clearTimeout(this.selectionDebounceTimeout);\n    }\n\n    // Only make API call if this is a different document or after debounce delay\n    if (this.lastSelectedDocumentId !== document.id) {\n      this.lastSelectedDocumentId = document.id;\n\n      this.selectionDebounceTimeout = setTimeout(() => {\n        // Use the API to track document opens (fire and forget for performance)\n        this.docsService.trackDocumentOpen({\n          id: document.id\n        } as TrackDocumentOpenDto).subscribe(\n          () => {\n            // API call successful - no additional action needed since we already updated local state\n            console.log('Document open tracked successfully for:', document.title);\n          },\n          (error: any) => {\n            console.error('Error tracking document open:', error);\n            // Note: We don't revert local state changes on API error to maintain good UX\n          }\n        );\n      }, 300); // 300ms debounce delay\n    }\n  }\n\n  private addNewDocumentToRecent(document: any) {\n    // Immediately add the new document to the recent list for instant visibility\n    if (document && document.id) {\n      console.log('🆕 Adding new document to recent list:', document);\n\n      // Remove if already exists (shouldn't happen for new docs, but just in case)\n      this.recentDocuments = this.recentDocuments.filter(\n        (doc) => doc.id !== document.id\n      );\n\n      // Add to the beginning of the list\n      this.recentDocuments.unshift(document);\n\n      // Keep only the latest 5\n      if (this.recentDocuments.length > 5) {\n        this.recentDocuments = this.recentDocuments.slice(0, 5);\n      }\n\n      console.log('✅ Updated recent documents list:', this.recentDocuments);\n\n      // Force change detection to ensure UI updates\n      setTimeout(() => {\n        console.log('🔄 Forcing UI update for new document');\n      }, 0);\n    }\n  }\n\n  private handleFavoriteChange(document: any) {\n    // Handle immediate favorite status changes in the local lists\n    if (document && document.id) {\n      console.log('🌟 Handling favorite change for document:', document);\n\n      const isFavorite = document.isFavorite || document.isFavourite;\n\n      if (isFavorite) {\n        // Add to favorites if not already there\n        const existsInFavorites = this.favorites.some(fav => fav.id === document.id);\n        if (!existsInFavorites) {\n          this.favorites.push(document);\n          console.log('✅ Added to favorites list');\n        }\n      } else {\n        // Remove from favorites\n        this.favorites = this.favorites.filter(fav => fav.id !== document.id);\n        console.log('✅ Removed from favorites list');\n      }\n\n      // Update the document in recent list if it exists there\n      const recentIndex = this.recentDocuments.findIndex(doc => doc.id === document.id);\n      if (recentIndex !== -1) {\n        this.recentDocuments[recentIndex] = { ...this.recentDocuments[recentIndex], ...document };\n        console.log('✅ Updated document in recent list');\n      }\n    }\n  }\n}\n"], "mappings": ";;;AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,SAAS,EAAEC,KAAK,EAAsDC,MAAM,QAAQ,eAAe;AAC5G,SAASC,MAAM,EAAEC,aAAa,QAAQ,iBAAiB;AAEvD,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,YAAY,QAAQ,2CAA2C;AACxE,SAASC,gBAAgB,QAAiD,oDAAoD;AAC9H,SAASC,mBAAmB,QAA6B,gDAAgD;AACzG,SAASC,qBAAqB,QAAQ,kDAAkD;AASjF,IAAMC,qBAAqB,GAA3B,MAAMA,qBAAqB;EAmBhCC,YACUC,WAA6B,EAC7BC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IApBP,KAAAC,aAAa,GAAW,EAAE;IAEnC,KAAAC,SAAS,GAAU,EAAE;IACrB,KAAAC,eAAe,GAAU,EAAE;IAC3B,KAAAC,YAAY,GAAU,EAAE;IACxB,KAAAC,QAAQ,GAAU,EAAE,CAAC,CAAC;IACtB,KAAAC,aAAa,GAAY,KAAK;IAC9B,KAAAC,YAAY,GAAmC,KAAK;IAE5C,KAAAC,aAAa,GAAmB,EAAE;IAClC,KAAAC,sBAAsB,GAAkB,IAAI;IAC5C,KAAAC,wBAAwB,GAAQ,IAAI;IAE5C;IACA,KAAAC,YAAY,GAAGtB,MAAM,CAACI,YAAY,CAAC;IACnC,KAAAmB,mBAAmB,GAAGvB,MAAM,CAACM,mBAAmB,CAAC;IACjD,KAAAkB,qBAAqB,GAAGxB,MAAM,CAACO,qBAAqB,CAAC;EAKjD;EAEJkB,QAAQA,CAAA;IACN;IACA,IAAI,CAACR,aAAa,GAAG,IAAI,CAACN,MAAM,CAACe,GAAG,CAACC,QAAQ,CAAC,QAAQ,CAAC;IAEvD;IACA,IAAI,IAAI,CAACV,aAAa,EAAE;MACtB,IAAI,CAACL,aAAa,GAAG,aAAa;KACnC,MAAM;MACL;MACA,IAAI,CAAC,IAAI,CAACA,aAAa,EAAE;QACvB,MAAMgB,WAAW,GAAG,IAAI,CAACjB,MAAM,CAACe,GAAG,CAACG,KAAK,CAAC,GAAG,CAAC;QAC9C,IAAID,WAAW,CAACD,QAAQ,CAAC,YAAY,CAAC,IAAIC,WAAW,CAACE,MAAM,GAAG,CAAC,EAAE;UAChE,MAAMC,cAAc,GAAGH,WAAW,CAACI,OAAO,CAAC,YAAY,CAAC;UACxD,IAAI,CAACpB,aAAa,GAAGqB,kBAAkB,CAACL,WAAW,CAACG,cAAc,GAAG,CAAC,CAAC,CAAC;SACzE,MAAM;UACL,IAAI,CAACnB,aAAa,GAAG,aAAa,CAAC,CAAC;;;;IAK1CsB,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAE,IAAI,CAACvB,aAAa,CAAC;IAC9EsB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAAClB,aAAa,CAAC;IACtDiB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAACxB,MAAM,CAACe,GAAG,CAAC;IAE/C,IAAI,CAACU,eAAe,EAAE;IAEtB;IACA,MAAMC,0BAA0B,GAAG,IAAI,CAACb,qBAAqB,CAACc,eAAe,CAACC,SAAS,CAACC,SAAS,IAAG;MAClGN,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEK,SAAS,CAAC;MACrD;IACF,CAAC,CAAC;IAEF;IACA,MAAMC,iBAAiB,GAAG,IAAI,CAAC9B,MAAM,CAAC+B,MAAM,CACzCC,IAAI,CAACxC,MAAM,CAACyC,KAAK,IAAIA,KAAK,YAAY1C,aAAa,CAAC,CAAC,CACrDqC,SAAS,CAAC,MAAK;MACd,MAAMM,YAAY,GAAG,IAAI,CAAClC,MAAM,CAACe,GAAG;MACpC,MAAMoB,cAAc,GAAG,IAAI,CAAC7B,aAAa;MACzC,IAAI,CAACA,aAAa,GAAG4B,YAAY,CAAClB,QAAQ,CAAC,QAAQ,CAAC;MAEpD;MACA,IAAI,IAAI,CAACV,aAAa,KAAK6B,cAAc,EAAE;QACzC,IAAI,IAAI,CAAC7B,aAAa,EAAE;UACtB,IAAI,CAACL,aAAa,GAAG,aAAa;;QAEpC,IAAI,CAACwB,eAAe,EAAE;;IAE1B,CAAC,CAAC;IAEJ;IACA,MAAMW,0BAA0B,GAAG,IAAI,CAACxB,mBAAmB,CAACyB,gBAAgB,CAACT,SAAS,CAAEK,KAA0B,IAAI;MACpHV,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAES,KAAK,CAAC;MACnEV,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE,IAAI,CAACvB,aAAa,CAAC;MACrDsB,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAES,KAAK,CAAChC,aAAa,CAAC;MAEpD;MACA,MAAMqC,gBAAgB,GAAG,CAACL,KAAK,CAAChC,aAAa,IACrBgC,KAAK,CAAChC,aAAa,KAAK,IAAI,CAACA,aAAa,IACzCgC,KAAK,CAAChC,aAAa,KAAK,aAAa,IAAI,IAAI,CAACA,aAAa,KAAK,aAAc;MAEvG,IAAIqC,gBAAgB,EAAE;QACpBf,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAE,IAAI,CAACvB,aAAa,CAAC;QAEtF;QACA,IAAIgC,KAAK,CAACM,IAAI,KAAK,SAAS,IAAIN,KAAK,CAACO,QAAQ,EAAE;UAC9CjB,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAES,KAAK,CAACO,QAAQ,CAAC;UACjF,IAAI,CAACC,sBAAsB,CAACR,KAAK,CAACO,QAAQ,CAAC;UAC3C;UACAE,UAAU,CAAC,MAAK;YACdnB,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;YACtD,IAAI,CAACmB,YAAY,EAAE;UACrB,CAAC,EAAE,IAAI,CAAC;SACT,MAAM,IAAIV,KAAK,CAACM,IAAI,KAAK,WAAW,IAAIN,KAAK,CAACO,QAAQ,EAAE;UACvDjB,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAES,KAAK,CAACO,QAAQ,CAAC;UACnE,IAAI,CAACI,oBAAoB,CAACX,KAAK,CAACO,QAAQ,CAAC;UACzC;UACAE,UAAU,CAAC,MAAK;YACdnB,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;YACzD,IAAI,CAACqB,iBAAiB,EAAE;UAC1B,CAAC,EAAE,GAAG,CAAC;SACR,MAAM,IAAIZ,KAAK,CAACM,IAAI,KAAK,SAAS,IAAIN,KAAK,CAACO,QAAQ,EAAE;UACrDjB,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;UACxD;UACAkB,UAAU,CAAC,MAAK;YACdnB,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;YAClD,IAAI,CAACC,eAAe,EAAE;UACxB,CAAC,EAAE,GAAG,CAAC;;QAET;OACD,MAAM;QACLF,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;;IAExD,CAAC,CAAC;IAEF,IAAI,CAAChB,aAAa,CAACsC,IAAI,CAACpB,0BAA0B,EAAEI,iBAAiB,EAAEM,0BAA0B,CAAC;EACpG;EAEAW,WAAWA,CAAA;IACT;IACA,IAAI,IAAI,CAAC9C,aAAa,EAAE;MACtBsB,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE,IAAI,CAACvB,aAAa,CAAC;MAC5E,IAAI,CAACwB,eAAe,EAAE;;EAE1B;EAEAuB,WAAWA,CAAA;IACT;IACA,IAAI,CAACxC,aAAa,CAACyC,OAAO,CAACC,YAAY,IAAIA,YAAY,CAACC,WAAW,EAAE,CAAC;IAEtE;IACA,IAAI,IAAI,CAACzC,wBAAwB,EAAE;MACjC0C,YAAY,CAAC,IAAI,CAAC1C,wBAAwB,CAAC;;EAE/C;EAEA;EACA2C,YAAYA,CAAC7D,MAAsC;IACjD,IAAI,CAACe,YAAY,GAAGf,MAAM;EAC5B;EAEA8D,oBAAoBA,CAAA;IAClB,QAAQ,IAAI,CAAC/C,YAAY;MACvB,KAAK,WAAW;QACd,OAAO,IAAI,CAACL,SAAS;MACvB,KAAK,QAAQ;QACX,OAAO,IAAI,CAACC,eAAe;MAC7B,KAAK,KAAK;MACV;QACE,OAAO,IAAI,CAACE,QAAQ;MAAE;;EAE5B;EAEAkD,qBAAqBA,CAAA;IACnB,QAAQ,IAAI,CAAChD,YAAY;MACvB,KAAK,WAAW;QACd,OAAO,WAAW;MACpB,KAAK,QAAQ;QACX,OAAO,QAAQ;MACjB,KAAK,KAAK;MACV;QACE,OAAO,WAAW;;EAExB;EAEAkB,eAAeA,CAAA;IACb,IAAI,CAAC+B,wBAAwB,EAAE;IAC/B,IAAI,CAACb,YAAY,EAAE;EACrB;EAEA;EACAc,WAAWA,CAAA;IACT,IAAI,CAAChC,eAAe,EAAE;EACxB;EAEA+B,wBAAwBA,CAAA;IACtBjC,OAAO,CAACC,GAAG,CAAC,wDAAwD,EAAE,IAAI,CAACvB,aAAa,CAAC;IAEzF;IACA,IAAI,CAACF,WAAW,CAAC2D,YAAY,CAAC,IAAI,CAACzD,aAAa,CAAC,CAAC2B,SAAS,CACxD+B,IAAS,IAAI;MACZpC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEmC,IAAI,CAAC;MACtC,IAAI,CAACzD,SAAS,GAAGyD,IAAI;MACrB,IAAI,CAACC,kBAAkB,EAAE;MACzB;MACA,IAAI,CAACC,8BAA8B,EAAE;IACvC,CAAC,EACAC,KAAU,IAAI;MACbvC,OAAO,CAACuC,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,IAAI,CAAC5D,SAAS,GAAG,EAAE;IACrB,CAAC,CACF;IAED;IACA,IAAI,CAACH,WAAW,CAACgE,iBAAiB,CAAC,IAAI,CAAC9D,aAAa,EAAE,CAAC,CAAC,CAAC2B,SAAS,CAChE+B,IAAS,IAAI;MACZpC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEmC,IAAI,CAAC;MAC7C,IAAI,CAACxD,eAAe,GAAGwD,IAAI;MAC3B,IAAI,CAACC,kBAAkB,EAAE;IAC3B,CAAC,EACAE,KAAU,IAAI;MACbvC,OAAO,CAACuC,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,IAAI,CAAC3D,eAAe,GAAG,EAAE;IAC3B,CAAC,CACF;EACH;EAEQyD,kBAAkBA,CAAA;IACxB;IACA,MAAMI,OAAO,GAAG,CAAC,GAAG,IAAI,CAAC9D,SAAS,EAAE,GAAG,IAAI,CAACC,eAAe,CAAC;IAC5D,MAAM8D,UAAU,GAAGD,OAAO,CAACxE,MAAM,CAAC,CAAC0E,GAAG,EAAEC,KAAK,EAAEC,IAAI,KACjDD,KAAK,KAAKC,IAAI,CAACC,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKL,GAAG,CAACK,EAAE,CAAC,CAC/C;IACD,IAAI,CAACnE,YAAY,GAAG6D,UAAU;EAChC;EAEAtB,YAAYA,CAAA;IACVpB,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE,IAAI,CAACvB,aAAa,CAAC;IAEnE;IACA,IAAI,CAACF,WAAW,CAACyE,kBAAkB,CAAC,IAAI,CAACvE,aAAa,CAAC,CAAC2B,SAAS,CAC9D+B,IAAS,IAAI;MACZpC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEmC,IAAI,CAAC;MACtC,IAAI,CAACtD,QAAQ,GAAGsD,IAAI,IAAI,EAAE;MAE1B;MACA,IAAI,CAACE,8BAA8B,EAAE;IACvC,CAAC,EACAC,KAAU,IAAI;MACbvC,OAAO,CAACuC,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,IAAI,CAACzD,QAAQ,GAAG,EAAE;IACpB,CAAC,CACF;EACH;EAEAwC,iBAAiBA,CAAA;IACftB,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE,IAAI,CAACvB,aAAa,CAAC;IAExE;IACA,IAAI,CAACF,WAAW,CAAC2D,YAAY,CAAC,IAAI,CAACzD,aAAa,CAAC,CAAC2B,SAAS,CACxD+B,IAAS,IAAI;MACZpC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEmC,IAAI,CAAC;MAC3C,IAAI,CAACzD,SAAS,GAAGyD,IAAI;MACrB;MACA,IAAI,CAACE,8BAA8B,EAAE;IACvC,CAAC,EACAC,KAAU,IAAI;MACbvC,OAAO,CAACuC,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,IAAI,CAAC5D,SAAS,GAAG,EAAE;IACrB,CAAC,CACF;EACH;EAEQ2D,8BAA8BA,CAAA;IACpC;IACA,IAAI,CAACxD,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACoE,GAAG,CAACC,IAAI,KAAK;MACzC,GAAGA,IAAI;MACPC,UAAU,EAAE,IAAI,CAACzE,SAAS,CAAC0E,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACN,EAAE,KAAKG,IAAI,CAACH,EAAE;KAC1D,CAAC,CAAC;EACL;EAEAO,cAAcA,CAACtC,QAAa;IAC1B;IACA,IAAI,CAAC3B,qBAAqB,CAACkE,iBAAiB,CAACvC,QAAQ,EAAE;MACrDvC,aAAa,EAAE,IAAI,CAACA,aAAa;MACjC+E,KAAK,EAAE,IAAI,CAAC1E,aAAa,GAAG,OAAO,GAAG;KACvC,CAAC;IACFiB,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEgB,QAAQ,CAACyC,KAAK,CAAC;IAEvE,IAAI,CAACC,oBAAoB,CAAC1C,QAAQ,CAAC;IAEnC;IACA,IAAI,IAAI,CAAClC,aAAa,EAAE;MACtB,IAAI,CAACN,MAAM,CAACmF,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE;QAAEC,WAAW,EAAE;UAAEC,KAAK,EAAE7C,QAAQ,CAAC+B;QAAE;MAAE,CAAE,CAAC;KAC1E,MAAM;MACL,IAAI,CAACvE,MAAM,CAACmF,QAAQ,CAAC,CAAC,aAAa,EAAE,IAAI,CAAClF,aAAa,EAAE,WAAW,CAAC,EAAE;QAAEmF,WAAW,EAAE;UAAEC,KAAK,EAAE7C,QAAQ,CAAC+B;QAAE;MAAE,CAAE,CAAC;;EAEnH;EAEAe,WAAWA,CAACrD,KAAY;IACtBA,KAAK,CAACsD,wBAAwB,EAAE;IAEhC;IACA,IAAI,IAAI,CAACjF,aAAa,EAAE;MACtB,IAAI,CAACN,MAAM,CAACmF,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE;QAAEC,WAAW,EAAE;UAAEI,MAAM,EAAE;QAAK;MAAE,CAAE,CAAC;KACrE,MAAM;MACL,IAAI,CAACxF,MAAM,CAACmF,QAAQ,CAAC,CAAC,aAAa,EAAE,IAAI,CAAClF,aAAa,EAAE,WAAW,CAAC,EAAE;QAAEmF,WAAW,EAAE;UAAEI,MAAM,EAAE;QAAK;MAAE,CAAE,CAAC;;EAE9G;EAEAC,iBAAiBA,CAAA;IACf;IACAlE,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;IACzCD,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAAClB,aAAa,CAAC;IACtDiB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE,IAAI,CAACvB,aAAa,CAAC;IAErD,IAAI,IAAI,CAACK,aAAa,EAAE;MACtBiB,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MACpD,IAAI,CAACxB,MAAM,CAACmF,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;KACjC,MAAM;MACL5D,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE,eAAe,IAAI,CAACvB,aAAa,YAAY,CAAC;MACnG,IAAI,CAACD,MAAM,CAACmF,QAAQ,CAAC,CAAC,aAAa,EAAE,IAAI,CAAClF,aAAa,EAAE,WAAW,CAAC,CAAC;;EAE1E;EAEAyF,kBAAkBA,CAAClD,QAAa;IAC9B,OAAO,IAAI,CAACtC,SAAS,CAAC0E,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACN,EAAE,KAAK/B,QAAQ,CAAC+B,EAAE,CAAC;EAC7D;EAEAoB,gBAAgBA,CAACnD,QAAa;IAC5B,OAAO,IAAI,CAAC3B,qBAAqB,CAAC8E,gBAAgB,CAACnD,QAAQ,CAAC+B,EAAE,CAAC;EACjE;EAEAqB,cAAcA,CAACpD,QAAa;IAC1B,MAAMmC,UAAU,GAAG,IAAI,CAACe,kBAAkB,CAAClD,QAAQ,CAAC;IAEpD;IACA,IAAImC,UAAU,EAAE;MACd,IAAI,CAACzE,SAAS,GAAG,IAAI,CAACA,SAAS,CAACV,MAAM,CAAEqF,GAAG,IAAKA,GAAG,CAACN,EAAE,KAAK/B,QAAQ,CAAC+B,EAAE,CAAC;KACxE,MAAM;MACL,IAAI,CAACrE,SAAS,CAAC4C,IAAI,CAAC;QAAE,GAAGN,QAAQ;QAAEmC,UAAU,EAAE;MAAI,CAAE,CAAC;;IAGxD;IACA,IAAI,CAACd,8BAA8B,EAAE;IAErC;IACA,IAAI,CAAC9D,WAAW,CAAC8F,oBAAoB,CAAC;MACpCtB,EAAE,EAAE/B,QAAQ,CAAC+B,EAAE;MACfI,UAAU,EAAE,CAACA;KACO,CAAC,CAAC/C,SAAS,CAC/B,MAAK;MACH;MACAL,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEgB,QAAQ,CAACyC,KAAK,CAAC;MAExE;MACA,IAAI,CAACrE,mBAAmB,CAACkF,uBAAuB,CAC9C;QAAE,GAAGtD,QAAQ;QAAEmC,UAAU,EAAE,CAACA;MAAU,CAAE,EACxC,IAAI,CAAC1E,aAAa,CACnB;IACH,CAAC,EACA6D,KAAU,IAAI;MACbvC,OAAO,CAACuC,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD;MACA,IAAI,CAACa,UAAU,EAAE;QACf,IAAI,CAACzE,SAAS,GAAG,IAAI,CAACA,SAAS,CAACV,MAAM,CAAEqF,GAAG,IAAKA,GAAG,CAACN,EAAE,KAAK/B,QAAQ,CAAC+B,EAAE,CAAC;OACxE,MAAM;QACL,IAAI,CAACrE,SAAS,CAAC4C,IAAI,CAAC;UAAE,GAAGN,QAAQ;UAAEmC,UAAU,EAAE;QAAI,CAAE,CAAC;;MAExD,IAAI,CAACd,8BAA8B,EAAE;IACvC,CAAC,CACF;EACH;EAEAqB,oBAAoBA,CAAC1C,QAAa;IAChC;IACA,IAAI,CAACrC,eAAe,GAAG,IAAI,CAACA,eAAe,CAACX,MAAM,CAC/C0E,GAAG,IAAKA,GAAG,CAACK,EAAE,KAAK/B,QAAQ,CAAC+B,EAAE,CAChC;IACD,IAAI,CAACpE,eAAe,CAAC4F,OAAO,CAACvD,QAAQ,CAAC;IACtC,IAAI,IAAI,CAACrC,eAAe,CAACgB,MAAM,GAAG,CAAC,EAAE;MACnC,IAAI,CAAChB,eAAe,GAAG,IAAI,CAACA,eAAe,CAAC6F,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;;IAGzD;IACA,IAAI,IAAI,CAACtF,wBAAwB,EAAE;MACjC0C,YAAY,CAAC,IAAI,CAAC1C,wBAAwB,CAAC;;IAG7C;IACA,IAAI,IAAI,CAACD,sBAAsB,KAAK+B,QAAQ,CAAC+B,EAAE,EAAE;MAC/C,IAAI,CAAC9D,sBAAsB,GAAG+B,QAAQ,CAAC+B,EAAE;MAEzC,IAAI,CAAC7D,wBAAwB,GAAGgC,UAAU,CAAC,MAAK;QAC9C;QACA,IAAI,CAAC3C,WAAW,CAACkG,iBAAiB,CAAC;UACjC1B,EAAE,EAAE/B,QAAQ,CAAC+B;SACU,CAAC,CAAC3C,SAAS,CAClC,MAAK;UACH;UACAL,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEgB,QAAQ,CAACyC,KAAK,CAAC;QACxE,CAAC,EACAnB,KAAU,IAAI;UACbvC,OAAO,CAACuC,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;UACrD;QACF,CAAC,CACF;MACH,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;EAEb;EAEQrB,sBAAsBA,CAACD,QAAa;IAC1C;IACA,IAAIA,QAAQ,IAAIA,QAAQ,CAAC+B,EAAE,EAAE;MAC3BhD,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEgB,QAAQ,CAAC;MAE/D;MACA,IAAI,CAACrC,eAAe,GAAG,IAAI,CAACA,eAAe,CAACX,MAAM,CAC/C0E,GAAG,IAAKA,GAAG,CAACK,EAAE,KAAK/B,QAAQ,CAAC+B,EAAE,CAChC;MAED;MACA,IAAI,CAACpE,eAAe,CAAC4F,OAAO,CAACvD,QAAQ,CAAC;MAEtC;MACA,IAAI,IAAI,CAACrC,eAAe,CAACgB,MAAM,GAAG,CAAC,EAAE;QACnC,IAAI,CAAChB,eAAe,GAAG,IAAI,CAACA,eAAe,CAAC6F,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;;MAGzDzE,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE,IAAI,CAACrB,eAAe,CAAC;MAErE;MACAuC,UAAU,CAAC,MAAK;QACdnB,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MACtD,CAAC,EAAE,CAAC,CAAC;;EAET;EAEQoB,oBAAoBA,CAACJ,QAAa;IACxC;IACA,IAAIA,QAAQ,IAAIA,QAAQ,CAAC+B,EAAE,EAAE;MAC3BhD,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEgB,QAAQ,CAAC;MAElE,MAAMmC,UAAU,GAAGnC,QAAQ,CAACmC,UAAU,IAAInC,QAAQ,CAAC0D,WAAW;MAE9D,IAAIvB,UAAU,EAAE;QACd;QACA,MAAMwB,iBAAiB,GAAG,IAAI,CAACjG,SAAS,CAAC0E,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACN,EAAE,KAAK/B,QAAQ,CAAC+B,EAAE,CAAC;QAC5E,IAAI,CAAC4B,iBAAiB,EAAE;UACtB,IAAI,CAACjG,SAAS,CAAC4C,IAAI,CAACN,QAAQ,CAAC;UAC7BjB,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;;OAE3C,MAAM;QACL;QACA,IAAI,CAACtB,SAAS,GAAG,IAAI,CAACA,SAAS,CAACV,MAAM,CAACqF,GAAG,IAAIA,GAAG,CAACN,EAAE,KAAK/B,QAAQ,CAAC+B,EAAE,CAAC;QACrEhD,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;;MAG9C;MACA,MAAM4E,WAAW,GAAG,IAAI,CAACjG,eAAe,CAACkE,SAAS,CAACH,GAAG,IAAIA,GAAG,CAACK,EAAE,KAAK/B,QAAQ,CAAC+B,EAAE,CAAC;MACjF,IAAI6B,WAAW,KAAK,CAAC,CAAC,EAAE;QACtB,IAAI,CAACjG,eAAe,CAACiG,WAAW,CAAC,GAAG;UAAE,GAAG,IAAI,CAACjG,eAAe,CAACiG,WAAW,CAAC;UAAE,GAAG5D;QAAQ,CAAE;QACzFjB,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;;;EAGtD;;;;;;;;;;;cA/bCpC;MAAK;;;;AADKS,qBAAqB,GAAAwG,UAAA,EAPjClH,SAAS,CAAC;EACTmH,QAAQ,EAAE,mBAAmB;EAC7BC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACtH,YAAY,CAAC;EACvBuH,QAAA,EAAAC,oBAA6C;;CAE9C,CAAC,C,EACW7G,qBAAqB,CAicjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}