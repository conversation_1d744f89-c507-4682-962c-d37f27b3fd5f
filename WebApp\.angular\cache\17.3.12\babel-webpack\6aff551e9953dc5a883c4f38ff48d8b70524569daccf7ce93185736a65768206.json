{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/source/ai-hub/WebApp/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./editor.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./editor.component.css?ngResource\";\nimport { Component, ViewChild } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NotesService } from '../services/notes.service';\nimport { MarkdownModule } from 'ngx-markdown';\nimport EditorJS from '@editorjs/editorjs';\nimport Header from '@editorjs/header';\nimport List from '@editorjs/list';\nimport Checklist from '@editorjs/checklist';\nimport Quote from '@editorjs/quote';\nimport Warning from '@editorjs/warning';\nimport Marker from '@editorjs/marker';\nimport CodeTool from '@editorjs/code';\nimport Delimiter from '@editorjs/delimiter';\nimport InlineCode from '@editorjs/inline-code';\nimport Link from '@editorjs/link';\nimport Table from '@editorjs/table';\nimport ImageTool from '@editorjs/image';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { HttpClient } from '@angular/common/http';\nimport 'quill/dist/quill';\nlet EditorComponent = class EditorComponent {\n  constructor(notesService, router, route, http) {\n    this.notesService = notesService;\n    this.router = router;\n    this.route = route;\n    this.http = http;\n    // @ViewChild('editor') private editorElement!: ElementRef;\n    this.editor = null;\n    this.quillMarkdown = null;\n    this.isToogled = false;\n    this.wordCount = 0;\n    this.markdownContent = '';\n    this.isSaving = false;\n    this.isLoading = false;\n    this.isGenerating = false;\n    this.generateModalVisible = false;\n    this.aiPrompt = '';\n    this.contentType = 'article';\n    this.useCurrentContent = false;\n    this.chatMessages = [{\n      text: \"Hello! How can I help you today?\",\n      isUser: false,\n      timestamp: new Date()\n    }];\n    this.selectedOption = 'article';\n    this.documents = [];\n    // Add title property\n    this.noteTitle = '';\n    this.currentNoteId = null;\n    this.isDocsOpen = false; // Set default to false to hide docs section\n    this.showDeleteModal = false;\n    this.noteToDelete = null;\n    this.sessionId = '';\n    this.hasContent = false;\n    this.journal = '';\n    this.isjournal = false;\n    this.editorInitialized = false;\n    this.pendingContent = null;\n    this.isFavourite = false;\n  }\n  ngOnInit() {\n    this.route.params.subscribe(params => {\n      this.currentNoteId = params['id'];\n      console.log(this.currentNoteId);\n      if (this.currentNoteId) {\n        this.notesService.getNoteById(this.currentNoteId).subscribe(note => {\n          if (note.isJournal === true) {\n            {\n              this.journal = 'journal';\n              this.isjournal = true;\n            }\n          }\n          console.log(note);\n          this.noteTitle = note.title;\n          if (note?.content) {\n            try {\n              const parsedContent = JSON.parse(note.content);\n              if (this.editorInitialized) {\n                this.updateEditorContent(parsedContent);\n              } else {\n                this.pendingContent = parsedContent;\n              }\n            } catch (error) {\n              console.error('Error parsing note content:', error);\n            }\n          }\n        });\n      }\n      if (this.route.snapshot.url[0].path === 'journal') {\n        console.log('Journal route detected');\n        this.journal = 'journal';\n        this.isjournal = true;\n      }\n    });\n    this.sessionId = crypto.randomUUID();\n  }\n  ngAfterViewInit() {\n    if (!this.editor) {\n      setTimeout(() => {\n        this.initializeEditor();\n      }, 0);\n    }\n  }\n  createNewDocument() {\n    this.isToogled = false;\n    this.clearContent();\n  }\n  toggleFavorite() {\n    this.isFavourite = !this.isFavourite;\n    // Add your API call here to update the favorite status\n  }\n  initializeEditor() {\n    if (this.editor) return; // Prevent re-initialization\n    const editorElement = document.getElementById('editor');\n    if (!editorElement) {\n      console.error('Element with ID \"editor\" is missing.');\n      return;\n    }\n    this.editor = new EditorJS({\n      holder: 'editor',\n      minHeight: 200,\n      placeholder: 'Start writing or paste your content here...',\n      onChange: () => {\n        this.editor?.save().then(data => {\n          this.hasContent = data.blocks.length > 0;\n        });\n      },\n      onReady: () => {\n        this.editorInitialized = true;\n        if (this.pendingContent) {\n          this.updateEditorContent(this.pendingContent);\n          this.pendingContent = null;\n        }\n      },\n      tools: {\n        header: {\n          class: Header,\n          inlineToolbar: true,\n          config: {\n            levels: [1, 2, 3, 4],\n            defaultLevel: 1\n          }\n        },\n        list: List,\n        image: {\n          class: ImageTool,\n          config: {\n            field: 'file',\n            types: 'image/*',\n            captionPlaceholder: 'Caption',\n            buttonContent: 'Select an Image',\n            additionalRequestHeaders: {\n              'Content-Type': 'multipart/form-data'\n            },\n            uploader: {\n              uploadByFile: file => {\n                return new Promise((resolve, reject) => {\n                  const reader = new FileReader();\n                  reader.onload = () => {\n                    resolve({\n                      success: 1,\n                      file: {\n                        url: reader.result\n                      }\n                    });\n                  };\n                  reader.onerror = error => {\n                    reject({\n                      success: 0,\n                      message: 'Could not read file'\n                    });\n                  };\n                  reader.readAsDataURL(file);\n                });\n              }\n            }\n          }\n        },\n        checklist: Checklist,\n        quote: Quote,\n        warning: Warning,\n        marker: Marker,\n        code: CodeTool,\n        delimiter: Delimiter,\n        inlineCode: InlineCode,\n        link: {\n          class: Link,\n          config: {\n            endpoint: 'https://localhost:44350/api/Link/fetch' // Update with your API URL\n          }\n        },\n        table: Table\n      }\n    });\n  }\n  // Convert Quill content to Markdown\n  getMarkdownContent() {\n    if (!this.editor) return '';\n    const delta = this.editor.save().then(data => {\n      const markdown = this.deltaToMarkdown(data);\n      return markdown;\n    }).then(markdown => markdown);\n    return '';\n  }\n  // Helper function to convert Delta to Markdown\n  deltaToMarkdown(delta) {\n    if (!delta || !delta.ops) {\n      console.error('Invalid delta object:', delta);\n      return '';\n    }\n    let markdown = '';\n    delta.ops.forEach(op => {\n      if (typeof op.insert === 'string') {\n        let text = op.insert;\n        if (op.attributes) {\n          if (op.attributes.bold) text = `***${text}***`;\n          if (op.attributes.italic) text = `*${text}*`;\n          if (op.attributes.code) text = `\\`${text}\\``;\n          if (op.attributes.header === 1) text = `# ${text}`;\n          if (op.attributes.blockquote) text = `> ${text}`;\n          if (op.attributes.list === 'bullet') text = `- ${text}`;\n          if (op.attributes.list === 'ordered') text = `1. ${text}`;\n        }\n        markdown += text;\n      } else if (op.insert && op.insert.image) {\n        markdown += `![Image](${op.insert.image})\\n`;\n      }\n    });\n    return markdown;\n  }\n  generateContent() {\n    this.generateModalVisible = true;\n    const currentContent = this.getMarkdownContent();\n    if (currentContent) {\n      this.aiPrompt = `Based on this context:\\n${currentContent}\\n\\nPlease generate a ${this.selectedOption}`;\n      this.useCurrentContent = true;\n    }\n  }\n  saveContent() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (!_this.editor) return;\n      _this.isSaving = true;\n      try {\n        const editorData = yield _this.editor.save();\n        if (!editorData || !editorData.blocks) {\n          throw new Error('Invalid editor data');\n        }\n        const note = {\n          id: _this.currentNoteId || undefined,\n          title: _this.noteTitle || 'Untitled Note',\n          isFavourite: _this.isFavourite,\n          content: JSON.stringify(editorData),\n          isJournal: _this.journal ? true : false\n        };\n        console.log(note);\n        yield _this.notesService.createOrUpdateNote(note).toPromise();\n        _this.isToogled = true;\n        _this.clearContent();\n      } catch (error) {\n        console.error('Error saving note:', error);\n      } finally {\n        if (_this.isjournal) {\n          _this.router.navigate(['student/documents/journal']);\n        } else {\n          _this.router.navigate(['student/all-documents']);\n        }\n      }\n    })();\n  }\n  ngOnDestroy() {\n    if (this.quillMarkdown) {\n      this.quillMarkdown.destroy();\n    }\n    this.isjournal = false;\n  }\n  updateWordCount() {\n    if (this.editor) {\n      this.editor.save().then(data => {\n        const text = data.blocks.map(block => block.data.text || '').join(' ');\n        this.wordCount = text.trim() ? text.trim().split(/\\s+/).length : 0;\n      });\n    }\n  }\n  clearContent() {\n    if (this.editor && this.editor.blocks) {\n      this.editor.blocks.clear(); // Use blocks.clear() to clear the content\n      this.noteTitle = '';\n      this.currentNoteId = null;\n    }\n  }\n  deleteDocument(id) {\n    this.noteToDelete = id;\n    this.showDeleteModal = true;\n  }\n  editDocument(note) {\n    if (this.editor) {\n      this.noteTitle = note.title;\n      this.currentNoteId = note.id;\n      // Parse markdown and apply formatting\n      this.editor.blocks.clear();\n      try {\n        const content = JSON.parse(note.content);\n        this.editor.render(content);\n      } catch (e) {\n        console.error('Error parsing document content:', e);\n      }\n      this.updateWordCount();\n    }\n  }\n  viewDocument(doc) {\n    if (this.editor) {\n      this.noteTitle = doc.title;\n      this.currentNoteId = doc.id;\n      try {\n        const content = JSON.parse(doc.content);\n        this.editor.render(content);\n      } catch (e) {\n        console.error('Error parsing document content:', e);\n      }\n      this.isToogled = false;\n    }\n  }\n  toggleDocs() {\n    this.isDocsOpen = !this.isDocsOpen;\n    if (this.isDocsOpen && !this.editor) {\n      this.initializeEditor();\n    }\n  }\n  confirmDelete() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (_this2.noteToDelete) {\n        try {\n          yield _this2.notesService.deleteNote(_this2.noteToDelete).toPromise();\n        } catch (error) {\n          console.error('Error deleting document:', error);\n        } finally {\n          _this2.showDeleteModal = false;\n          _this2.noteToDelete = null;\n        }\n      }\n    })();\n  }\n  updateEditorContent(newContent) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        if (!_this3.editor) {\n          console.warn('Editor not initialized yet');\n          return;\n        }\n        // Wait for any pending operations to complete\n        yield _this3.editor.isReady;\n        // Clear existing content first\n        yield _this3.editor.blocks.clear();\n        // Render new content\n        yield _this3.editor.render(newContent);\n        // Update word count and other UI elements\n        _this3.updateWordCount();\n        _this3.hasContent = true;\n      } catch (error) {\n        console.error('Error updating editor content:', error);\n      }\n    })();\n  }\n  // Alternative method if you want to add blocks programmatically\n  addContentToEditor(content) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      if (_this4.editor) {\n        yield _this4.editor.blocks.insert('paragraph', {\n          text: content\n        });\n      }\n    })();\n  }\n  // If you need to update specific blocks\n  updateSpecificBlock(blockIndex, newData) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      if (_this5.editor) {\n        const currentBlock = yield _this5.editor.blocks.getBlockByIndex(blockIndex);\n        if (currentBlock) {\n          yield _this5.editor.blocks.update(currentBlock.id, newData);\n        }\n      }\n    })();\n  }\n  executeCommand(command) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this6.editor) return;\n      switch (command) {\n        case 'link':\n          // The editor instance already has the link tool configured\n          // We just need to trigger the link tool UI\n          yield _this6.editor.blocks.insert('link', {\n            link: '',\n            meta: {\n              title: '',\n              description: '',\n              image: {\n                url: ''\n              }\n            }\n          });\n          break;\n        // Add other commands here if needed\n        default:\n          console.warn(`Unknown command: ${command}`);\n      }\n    })();\n  }\n  addBlock(blockType) {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this7.editor) return;\n      try {\n        yield _this7.editor.blocks.insert(blockType);\n      } catch (error) {\n        console.error(`Error adding block of type ${blockType}:`, error);\n      }\n    })();\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: NotesService\n    }, {\n      type: Router\n    }, {\n      type: ActivatedRoute\n    }, {\n      type: HttpClient\n    }];\n  }\n  static {\n    this.propDecorators = {\n      chatContainer: [{\n        type: ViewChild,\n        args: ['chatContainer']\n      }],\n      chatInput: [{\n        type: ViewChild,\n        args: ['chatInput']\n      }]\n    };\n  }\n};\nEditorComponent = __decorate([Component({\n  selector: 'app-editor',\n  imports: [CommonModule, FormsModule, MarkdownModule],\n  standalone: true,\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], EditorComponent);\nexport { EditorComponent };", "map": {"version": 3, "names": ["Component", "ViewChild", "CommonModule", "FormsModule", "NotesService", "MarkdownModule", "EditorJS", "Header", "List", "Checklist", "Quote", "Warning", "<PERSON><PERSON>", "CodeTool", "Delimiter", "InlineCode", "Link", "Table", "ImageTool", "ActivatedRoute", "Router", "HttpClient", "EditorComponent", "constructor", "notesService", "router", "route", "http", "editor", "quillMarkdown", "isToogled", "wordCount", "markdownContent", "isSaving", "isLoading", "isGenerating", "generateModalVisible", "aiPrompt", "contentType", "useCurrentContent", "chatMessages", "text", "isUser", "timestamp", "Date", "selectedOption", "documents", "noteTitle", "currentNoteId", "isDocsOpen", "showDeleteModal", "noteToDelete", "sessionId", "<PERSON><PERSON><PERSON><PERSON>", "journal", "isjournal", "editorInitialized", "pendingContent", "isFavourite", "ngOnInit", "params", "subscribe", "console", "log", "getNoteById", "note", "isJournal", "title", "content", "parsed<PERSON><PERSON><PERSON>", "JSON", "parse", "updateEditorContent", "error", "snapshot", "url", "path", "crypto", "randomUUID", "ngAfterViewInit", "setTimeout", "initializeEditor", "createNewDocument", "clearContent", "toggleFavorite", "editor<PERSON><PERSON>", "document", "getElementById", "holder", "minHeight", "placeholder", "onChange", "save", "then", "data", "blocks", "length", "onReady", "tools", "header", "class", "inlineToolbar", "config", "levels", "defaultLevel", "list", "image", "field", "types", "captionPlaceholder", "buttonContent", "additionalRequestHeaders", "uploader", "uploadByFile", "file", "Promise", "resolve", "reject", "reader", "FileReader", "onload", "success", "result", "onerror", "message", "readAsDataURL", "checklist", "quote", "warning", "marker", "code", "delimiter", "inlineCode", "link", "endpoint", "table", "getMarkdownContent", "delta", "markdown", "deltaToMarkdown", "ops", "for<PERSON>ach", "op", "insert", "attributes", "bold", "italic", "blockquote", "generateContent", "currentC<PERSON>nt", "saveContent", "_this", "_asyncToGenerator", "editorData", "Error", "id", "undefined", "stringify", "createOrUpdateNote", "to<PERSON>romise", "navigate", "ngOnDestroy", "destroy", "updateWordCount", "map", "block", "join", "trim", "split", "clear", "deleteDocument", "editDocument", "render", "e", "viewDocument", "doc", "toggleDocs", "confirmDelete", "_this2", "deleteNote", "newContent", "_this3", "warn", "isReady", "addContentToEditor", "_this4", "updateSpecificBlock", "blockIndex", "newData", "_this5", "currentBlock", "getBlockByIndex", "update", "executeCommand", "command", "_this6", "meta", "description", "addBlock", "blockType", "_this7", "args", "__decorate", "selector", "imports", "standalone", "template", "__NG_CLI_RESOURCE__0"], "sources": ["C:\\Users\\<USER>\\source\\ai-hub\\WebApp\\src\\app\\MyNotesProjects\\editor\\editor.component.ts"], "sourcesContent": ["import { Component, OnInit, ViewChild, ElementRef, AfterViewInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport QuillMarkdown from 'quilljs-markdown';\r\nimport { NotesService, Note } from '../services/notes.service';\r\n\r\nimport { MarkdownModule } from 'ngx-markdown';\r\nimport EditorJS, { ToolConstructable } from '@editorjs/editorjs';\r\nimport Header from '@editorjs/header';\r\nimport List from '@editorjs/list';\r\nimport SimpleImage from '@editorjs/simple-image';\r\nimport Checklist from '@editorjs/checklist';\r\nimport Quote from '@editorjs/quote';\r\nimport Warning from '@editorjs/warning';\r\nimport Marker from '@editorjs/marker';\r\nimport CodeTool from '@editorjs/code';\r\nimport Delimiter from '@editorjs/delimiter';\r\nimport InlineCode from '@editorjs/inline-code';\r\nimport Link from '@editorjs/link';\r\nimport Table from '@editorjs/table';\r\nimport ImageTool from '@editorjs/image';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport 'quill/dist/quill';\r\ninterface ChatMessage {\r\n  text: string;\r\n  isUser: boolean;\r\n  timestamp: Date;\r\n  hasCodeBlock?: boolean;\r\n  codeLanguage?: string;\r\n  code?: string;\r\n}\r\n\r\n\r\n@Component({\r\n  selector: 'app-editor',\r\n  imports: [CommonModule, FormsModule, MarkdownModule],\r\n  standalone: true,\r\n  templateUrl: './editor.component.html',\r\n  styleUrl: './editor.component.css'\r\n})\r\nexport class EditorComponent {\r\n  @ViewChild('chatContainer') private chatContainer!: ElementRef;\r\n  @ViewChild('chatInput') private chatInput!: ElementRef;\r\n  // @ViewChild('editor') private editorElement!: ElementRef;\r\n\r\n  private editor: EditorJS | null = null;\r\n  private quillMarkdown: QuillMarkdown | null = null;\r\n  isToogled = false;\r\n  wordCount = 0;\r\n  markdownContent = '';\r\n  isSaving = false;\r\n  isLoading = false;\r\n  isGenerating = false;\r\n  generateModalVisible = false;\r\n  aiPrompt = '';\r\n  contentType = 'article';\r\n  useCurrentContent = false;\r\n  chatMessages: ChatMessage[] = [\r\n    {\r\n      text: \"Hello! How can I help you today?\",\r\n      isUser: false,\r\n      timestamp: new Date(),\r\n    }\r\n  ];\r\n  selectedOption = 'article';\r\n  documents: Note[] = [];\r\n  // Add title property\r\n  noteTitle: string = '';\r\n  currentNoteId: number | null = null;\r\n\r\n  isDocsOpen = false;  // Set default to false to hide docs section\r\n  showDeleteModal = false;\r\n  noteToDelete: number | null = null;\r\n  sessionId: string = '';\r\n  hasContent = false;\r\n  journal: string = '';\r\n  isjournal: boolean = false;\r\n  private editorInitialized = false;\r\n  private pendingContent: any = null;\r\n\r\n  constructor(private notesService: NotesService, private router: Router, private route: ActivatedRoute, private http: HttpClient) { }\r\n\r\n  ngOnInit() {\r\n    this.route.params.subscribe(params => {\r\n      this.currentNoteId = params['id'];\r\n      console.log(this.currentNoteId);\r\n      if (this.currentNoteId) {\r\n        this.notesService.getNoteById(this.currentNoteId).subscribe(note => {\r\n          if (note.isJournal === true) {\r\n            {\r\n              this.journal = 'journal';\r\n              this.isjournal = true;\r\n            }\r\n          }\r\n\r\n          console.log(note);\r\n          this.noteTitle = note.title;\r\n          if (note?.content) {\r\n            try {\r\n              const parsedContent = JSON.parse(note.content);\r\n              if (this.editorInitialized) {\r\n                this.updateEditorContent(parsedContent);\r\n              } else {\r\n                this.pendingContent = parsedContent;\r\n              }\r\n            } catch (error) {\r\n              console.error('Error parsing note content:', error);\r\n            }\r\n          }\r\n        });\r\n      }\r\n\r\n      if (this.route.snapshot.url[0].path === 'journal') {\r\n        console.log('Journal route detected');\r\n        this.journal = 'journal';\r\n        this.isjournal = true;\r\n      }\r\n    });\r\n\r\n    this.sessionId = crypto.randomUUID();\r\n  }\r\n\r\n\r\n  ngAfterViewInit() {\r\n    if (!this.editor) {\r\n      setTimeout(() => {\r\n        this.initializeEditor();\r\n      }, 0);\r\n    }\r\n  }\r\n\r\n  createNewDocument() {\r\n    this.isToogled = false;\r\n    this.clearContent();\r\n  }\r\n  isFavourite: boolean = false;\r\n\r\n  toggleFavorite() {\r\n    this.isFavourite = !this.isFavourite;\r\n    // Add your API call here to update the favorite status\r\n  }\r\n\r\n  private initializeEditor() {\r\n    if (this.editor) return; // Prevent re-initialization\r\n\r\n    const editorElement = document.getElementById('editor');\r\n    if (!editorElement) {\r\n      console.error('Element with ID \"editor\" is missing.');\r\n      return;\r\n    }\r\n\r\n    this.editor = new EditorJS({\r\n      holder: 'editor',\r\n      minHeight: 200,\r\n      placeholder: 'Start writing or paste your content here...',\r\n      onChange: () => {\r\n        this.editor?.save().then(data => {\r\n          this.hasContent = data.blocks.length > 0;\r\n        });\r\n      },\r\n      onReady: () => {\r\n        this.editorInitialized = true;\r\n        if (this.pendingContent) {\r\n          this.updateEditorContent(this.pendingContent);\r\n          this.pendingContent = null;\r\n        }\r\n      },\r\n      tools: {\r\n        header: {\r\n          class: Header as unknown as ToolConstructable,\r\n          inlineToolbar: true,\r\n          config: {\r\n            levels: [1, 2, 3, 4],\r\n            defaultLevel: 1\r\n          }\r\n        },\r\n        list: List as unknown as ToolConstructable,\r\n        image: {\r\n          class: ImageTool as unknown as { new(): any }, // Ensure type compatibility\r\n          config: {\r\n            field: 'file', // Form field name for the file\r\n            types: 'image/*', // Acceptable file types\r\n            captionPlaceholder: 'Caption', // Placeholder for the caption input\r\n            buttonContent: 'Select an Image', // Button content\r\n            additionalRequestHeaders: {\r\n              'Content-Type': 'multipart/form-data'\r\n            },\r\n            uploader: {\r\n              uploadByFile: (file: File) => {\r\n                return new Promise((resolve, reject) => {\r\n                  const reader = new FileReader();\r\n                  reader.onload = () => {\r\n                    resolve({\r\n                      success: 1,\r\n                      file: {\r\n                        url: reader.result as string\r\n                      }\r\n                    });\r\n                  };\r\n                  reader.onerror = (error) => {\r\n                    reject({\r\n                      success: 0,\r\n                      message: 'Could not read file'\r\n                    });\r\n                  };\r\n                  reader.readAsDataURL(file);\r\n                });\r\n              }\r\n            }\r\n          }\r\n        },\r\n        checklist: Checklist as unknown as ToolConstructable,\r\n        quote: Quote as unknown as ToolConstructable,\r\n        warning: Warning as unknown as ToolConstructable,\r\n        marker: Marker as unknown as ToolConstructable,\r\n        code: CodeTool as unknown as ToolConstructable,\r\n        delimiter: Delimiter as unknown as ToolConstructable,\r\n        inlineCode: InlineCode as unknown as ToolConstructable,\r\n        link: {\r\n          class: Link as unknown as ToolConstructable,\r\n          config: {\r\n            endpoint: 'https://localhost:44350/api/Link/fetch', // Update with your API URL\r\n          }\r\n        },\r\n        table: Table as unknown as ToolConstructable\r\n      }\r\n    });\r\n  }\r\n\r\n  // Convert Quill content to Markdown\r\n  private getMarkdownContent(): string {\r\n    if (!this.editor) return '';\r\n\r\n    const delta = this.editor.save().then(data => {\r\n      const markdown = this.deltaToMarkdown(data);\r\n      return markdown;\r\n    }).then(markdown => markdown);\r\n    return '';\r\n  }\r\n\r\n  // Helper function to convert Delta to Markdown\r\n  private deltaToMarkdown(delta: any): string {\r\n    if (!delta || !delta.ops) {\r\n      console.error('Invalid delta object:', delta);\r\n      return '';\r\n    }\r\n\r\n    let markdown = '';\r\n    delta.ops.forEach((op: any) => {\r\n      if (typeof op.insert === 'string') {\r\n        let text = op.insert;\r\n        if (op.attributes) {\r\n          if (op.attributes.bold) text = `***${text}***`;\r\n          if (op.attributes.italic) text = `*${text}*`;\r\n          if (op.attributes.code) text = `\\`${text}\\``;\r\n          if (op.attributes.header === 1) text = `# ${text}`;\r\n          if (op.attributes.blockquote) text = `> ${text}`;\r\n          if (op.attributes.list === 'bullet') text = `- ${text}`;\r\n          if (op.attributes.list === 'ordered') text = `1. ${text}`;\r\n        }\r\n        markdown += text;\r\n      } else if (op.insert && op.insert.image) {\r\n        markdown += `![Image](${op.insert.image})\\n`;\r\n      }\r\n    });\r\n    return markdown;\r\n  }\r\n\r\n  generateContent() {\r\n    this.generateModalVisible = true;\r\n    const currentContent = this.getMarkdownContent();\r\n    if (currentContent) {\r\n      this.aiPrompt = `Based on this context:\\n${currentContent}\\n\\nPlease generate a ${this.selectedOption}`;\r\n      this.useCurrentContent = true;\r\n    }\r\n  }\r\n\r\n  async saveContent() {\r\n    if (!this.editor) return;\r\n\r\n    this.isSaving = true;\r\n    try {\r\n      const editorData = await this.editor.save();\r\n      if (!editorData || !editorData.blocks) {\r\n        throw new Error('Invalid editor data');\r\n      }\r\n      const note: Partial<Note> = {\r\n        id: this.currentNoteId || undefined,\r\n        title: this.noteTitle || 'Untitled Note',\r\n        isFavourite: this.isFavourite,\r\n        content: JSON.stringify(editorData), // Save as JSON string\r\n        isJournal: this.journal ? true : false\r\n      };\r\n\r\n\r\n      console.log(note);\r\n      await this.notesService.createOrUpdateNote(note).toPromise();\r\n      this.isToogled = true;\r\n      this.clearContent();\r\n\r\n\r\n    }\r\n    catch (error) {\r\n      console.error('Error saving note:', error);\r\n    } finally {\r\n\r\n      if (this.isjournal) {\r\n        this.router.navigate(['student/documents/journal']);\r\n      } else {\r\n        this.router.navigate(['student/all-documents']);\r\n      }\r\n    }\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    if (this.quillMarkdown) {\r\n      this.quillMarkdown.destroy();\r\n    }\r\n    this.isjournal = false;\r\n  }\r\n\r\n  private updateWordCount() {\r\n    if (this.editor) {\r\n      this.editor.save().then(data => {\r\n        const text = data.blocks.map(block => block.data.text || '').join(' ');\r\n        this.wordCount = text.trim() ? text.trim().split(/\\s+/).length : 0;\r\n      });\r\n    }\r\n  }\r\n\r\n  clearContent() {\r\n    if (this.editor && this.editor.blocks) {\r\n      this.editor.blocks.clear(); // Use blocks.clear() to clear the content\r\n      this.noteTitle = '';\r\n      this.currentNoteId = null;\r\n    }\r\n  }\r\n\r\n\r\n\r\n\r\n  deleteDocument(id: number) {\r\n    this.noteToDelete = id;\r\n    this.showDeleteModal = true;\r\n  }\r\n\r\n  editDocument(note: Note) {\r\n    if (this.editor) {\r\n      this.noteTitle = note.title;\r\n      this.currentNoteId = note.id;\r\n      // Parse markdown and apply formatting\r\n      this.editor.blocks.clear();\r\n      try {\r\n        const content = JSON.parse(note.content);\r\n        this.editor.render(content);\r\n      } catch (e) {\r\n        console.error('Error parsing document content:', e);\r\n      }\r\n      this.updateWordCount();\r\n    }\r\n  }\r\n\r\n  viewDocument(doc: Note) {\r\n    if (this.editor) {\r\n      this.noteTitle = doc.title;\r\n      this.currentNoteId = doc.id;\r\n      try {\r\n        const content = JSON.parse(doc.content);\r\n        this.editor.render(content);\r\n      } catch (e) {\r\n        console.error('Error parsing document content:', e);\r\n      }\r\n      this.isToogled = false;\r\n    }\r\n  }\r\n\r\n  toggleDocs() {\r\n    this.isDocsOpen = !this.isDocsOpen;\r\n    if (this.isDocsOpen && !this.editor) {\r\n      this.initializeEditor();\r\n    }\r\n  }\r\n\r\n  async confirmDelete() {\r\n    if (this.noteToDelete) {\r\n      try {\r\n        await this.notesService.deleteNote(this.noteToDelete).toPromise();\r\n      } catch (error) {\r\n        console.error('Error deleting document:', error);\r\n      } finally {\r\n        this.showDeleteModal = false;\r\n        this.noteToDelete = null;\r\n      }\r\n    }\r\n  }\r\n\r\n  async updateEditorContent(newContent: any) {\r\n    try {\r\n      if (!this.editor) {\r\n        console.warn('Editor not initialized yet');\r\n        return;\r\n      }\r\n\r\n      // Wait for any pending operations to complete\r\n      await this.editor.isReady;\r\n\r\n      // Clear existing content first\r\n      await this.editor.blocks.clear();\r\n\r\n      // Render new content\r\n      await this.editor.render(newContent);\r\n\r\n      // Update word count and other UI elements\r\n      this.updateWordCount();\r\n      this.hasContent = true;\r\n    } catch (error) {\r\n      console.error('Error updating editor content:', error);\r\n    }\r\n  }\r\n\r\n  // Alternative method if you want to add blocks programmatically\r\n  async addContentToEditor(content: string) {\r\n    if (this.editor) {\r\n      await this.editor.blocks.insert('paragraph', {\r\n        text: content\r\n      });\r\n    }\r\n  }\r\n\r\n  // If you need to update specific blocks\r\n  async updateSpecificBlock(blockIndex: number, newData: any) {\r\n    if (this.editor) {\r\n      const currentBlock = await this.editor.blocks.getBlockByIndex(blockIndex);\r\n      if (currentBlock) {\r\n        await this.editor.blocks.update(currentBlock.id, newData);\r\n      }\r\n    }\r\n  }\r\n\r\n  async executeCommand(command: string) {\r\n    if (!this.editor) return;\r\n\r\n    switch (command) {\r\n      case 'link':\r\n        // The editor instance already has the link tool configured\r\n        // We just need to trigger the link tool UI\r\n        await this.editor.blocks.insert('link', {\r\n          link: '',\r\n          meta: {\r\n            title: '',\r\n            description: '',\r\n            image: {\r\n              url: ''\r\n            }\r\n          }\r\n        });\r\n        break;\r\n\r\n      // Add other commands here if needed\r\n      default:\r\n        console.warn(`Unknown command: ${command}`);\r\n    }\r\n  }\r\n\r\n  async addBlock(blockType: string) {\r\n    if (!this.editor) return;\r\n\r\n    try {\r\n      await this.editor.blocks.insert(blockType);\r\n    } catch (error) {\r\n      console.error(`Error adding block of type ${blockType}:`, error);\r\n    }\r\n  }\r\n\r\n}\r\n"], "mappings": ";;;;AAAA,SAASA,SAAS,EAAUC,SAAS,QAAmC,eAAe;AACvF,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,SAASC,YAAY,QAAc,2BAA2B;AAE9D,SAASC,cAAc,QAAQ,cAAc;AAC7C,OAAOC,QAA+B,MAAM,oBAAoB;AAChE,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAOC,IAAI,MAAM,gBAAgB;AAEjC,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,OAAOC,KAAK,MAAM,iBAAiB;AACnC,OAAOC,OAAO,MAAM,mBAAmB;AACvC,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,OAAOC,IAAI,MAAM,gBAAgB;AACjC,OAAOC,KAAK,MAAM,iBAAiB;AACnC,OAAOC,SAAS,MAAM,iBAAiB;AACvC,SAASC,cAAc,EAAEC,MAAM,QAAQ,iBAAiB;AACxD,SAASC,UAAU,QAAQ,sBAAsB;AACjD,OAAO,kBAAkB;AAkBlB,IAAMC,eAAe,GAArB,MAAMA,eAAe;EAwC1BC,YAAoBC,YAA0B,EAAUC,MAAc,EAAUC,KAAqB,EAAUC,IAAgB;IAA3G,KAAAH,YAAY,GAAZA,YAAY;IAAwB,KAAAC,MAAM,GAANA,MAAM;IAAkB,KAAAC,KAAK,GAALA,KAAK;IAA0B,KAAAC,IAAI,GAAJA,IAAI;IArCnH;IAEQ,KAAAC,MAAM,GAAoB,IAAI;IAC9B,KAAAC,aAAa,GAAyB,IAAI;IAClD,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,eAAe,GAAG,EAAE;IACpB,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,oBAAoB,GAAG,KAAK;IAC5B,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,WAAW,GAAG,SAAS;IACvB,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,YAAY,GAAkB,CAC5B;MACEC,IAAI,EAAE,kCAAkC;MACxCC,MAAM,EAAE,KAAK;MACbC,SAAS,EAAE,IAAIC,IAAI;KACpB,CACF;IACD,KAAAC,cAAc,GAAG,SAAS;IAC1B,KAAAC,SAAS,GAAW,EAAE;IACtB;IACA,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAC,aAAa,GAAkB,IAAI;IAEnC,KAAAC,UAAU,GAAG,KAAK,CAAC,CAAE;IACrB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,YAAY,GAAkB,IAAI;IAClC,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,OAAO,GAAW,EAAE;IACpB,KAAAC,SAAS,GAAY,KAAK;IAClB,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,cAAc,GAAQ,IAAI;IAyDlC,KAAAC,WAAW,GAAY,KAAK;EAvDuG;EAEnIC,QAAQA,CAAA;IACN,IAAI,CAACjC,KAAK,CAACkC,MAAM,CAACC,SAAS,CAACD,MAAM,IAAG;MACnC,IAAI,CAACZ,aAAa,GAAGY,MAAM,CAAC,IAAI,CAAC;MACjCE,OAAO,CAACC,GAAG,CAAC,IAAI,CAACf,aAAa,CAAC;MAC/B,IAAI,IAAI,CAACA,aAAa,EAAE;QACtB,IAAI,CAACxB,YAAY,CAACwC,WAAW,CAAC,IAAI,CAAChB,aAAa,CAAC,CAACa,SAAS,CAACI,IAAI,IAAG;UACjE,IAAIA,IAAI,CAACC,SAAS,KAAK,IAAI,EAAE;YAC3B;cACE,IAAI,CAACZ,OAAO,GAAG,SAAS;cACxB,IAAI,CAACC,SAAS,GAAG,IAAI;;;UAIzBO,OAAO,CAACC,GAAG,CAACE,IAAI,CAAC;UACjB,IAAI,CAAClB,SAAS,GAAGkB,IAAI,CAACE,KAAK;UAC3B,IAAIF,IAAI,EAAEG,OAAO,EAAE;YACjB,IAAI;cACF,MAAMC,aAAa,GAAGC,IAAI,CAACC,KAAK,CAACN,IAAI,CAACG,OAAO,CAAC;cAC9C,IAAI,IAAI,CAACZ,iBAAiB,EAAE;gBAC1B,IAAI,CAACgB,mBAAmB,CAACH,aAAa,CAAC;eACxC,MAAM;gBACL,IAAI,CAACZ,cAAc,GAAGY,aAAa;;aAEtC,CAAC,OAAOI,KAAK,EAAE;cACdX,OAAO,CAACW,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;;;QAGzD,CAAC,CAAC;;MAGJ,IAAI,IAAI,CAAC/C,KAAK,CAACgD,QAAQ,CAACC,GAAG,CAAC,CAAC,CAAC,CAACC,IAAI,KAAK,SAAS,EAAE;QACjDd,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;QACrC,IAAI,CAACT,OAAO,GAAG,SAAS;QACxB,IAAI,CAACC,SAAS,GAAG,IAAI;;IAEzB,CAAC,CAAC;IAEF,IAAI,CAACH,SAAS,GAAGyB,MAAM,CAACC,UAAU,EAAE;EACtC;EAGAC,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAACnD,MAAM,EAAE;MAChBoD,UAAU,CAAC,MAAK;QACd,IAAI,CAACC,gBAAgB,EAAE;MACzB,CAAC,EAAE,CAAC,CAAC;;EAET;EAEAC,iBAAiBA,CAAA;IACf,IAAI,CAACpD,SAAS,GAAG,KAAK;IACtB,IAAI,CAACqD,YAAY,EAAE;EACrB;EAGAC,cAAcA,CAAA;IACZ,IAAI,CAAC1B,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;IACpC;EACF;EAEQuB,gBAAgBA,CAAA;IACtB,IAAI,IAAI,CAACrD,MAAM,EAAE,OAAO,CAAC;IAEzB,MAAMyD,aAAa,GAAGC,QAAQ,CAACC,cAAc,CAAC,QAAQ,CAAC;IACvD,IAAI,CAACF,aAAa,EAAE;MAClBvB,OAAO,CAACW,KAAK,CAAC,sCAAsC,CAAC;MACrD;;IAGF,IAAI,CAAC7C,MAAM,GAAG,IAAItB,QAAQ,CAAC;MACzBkF,MAAM,EAAE,QAAQ;MAChBC,SAAS,EAAE,GAAG;MACdC,WAAW,EAAE,6CAA6C;MAC1DC,QAAQ,EAAEA,CAAA,KAAK;QACb,IAAI,CAAC/D,MAAM,EAAEgE,IAAI,EAAE,CAACC,IAAI,CAACC,IAAI,IAAG;UAC9B,IAAI,CAACzC,UAAU,GAAGyC,IAAI,CAACC,MAAM,CAACC,MAAM,GAAG,CAAC;QAC1C,CAAC,CAAC;MACJ,CAAC;MACDC,OAAO,EAAEA,CAAA,KAAK;QACZ,IAAI,CAACzC,iBAAiB,GAAG,IAAI;QAC7B,IAAI,IAAI,CAACC,cAAc,EAAE;UACvB,IAAI,CAACe,mBAAmB,CAAC,IAAI,CAACf,cAAc,CAAC;UAC7C,IAAI,CAACA,cAAc,GAAG,IAAI;;MAE9B,CAAC;MACDyC,KAAK,EAAE;QACLC,MAAM,EAAE;UACNC,KAAK,EAAE7F,MAAsC;UAC7C8F,aAAa,EAAE,IAAI;UACnBC,MAAM,EAAE;YACNC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACpBC,YAAY,EAAE;;SAEjB;QACDC,IAAI,EAAEjG,IAAoC;QAC1CkG,KAAK,EAAE;UACLN,KAAK,EAAElF,SAAsC;UAC7CoF,MAAM,EAAE;YACNK,KAAK,EAAE,MAAM;YACbC,KAAK,EAAE,SAAS;YAChBC,kBAAkB,EAAE,SAAS;YAC7BC,aAAa,EAAE,iBAAiB;YAChCC,wBAAwB,EAAE;cACxB,cAAc,EAAE;aACjB;YACDC,QAAQ,EAAE;cACRC,YAAY,EAAGC,IAAU,IAAI;gBAC3B,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;kBACrC,MAAMC,MAAM,GAAG,IAAIC,UAAU,EAAE;kBAC/BD,MAAM,CAACE,MAAM,GAAG,MAAK;oBACnBJ,OAAO,CAAC;sBACNK,OAAO,EAAE,CAAC;sBACVP,IAAI,EAAE;wBACJvC,GAAG,EAAE2C,MAAM,CAACI;;qBAEf,CAAC;kBACJ,CAAC;kBACDJ,MAAM,CAACK,OAAO,GAAIlD,KAAK,IAAI;oBACzB4C,MAAM,CAAC;sBACLI,OAAO,EAAE,CAAC;sBACVG,OAAO,EAAE;qBACV,CAAC;kBACJ,CAAC;kBACDN,MAAM,CAACO,aAAa,CAACX,IAAI,CAAC;gBAC5B,CAAC,CAAC;cACJ;;;SAGL;QACDY,SAAS,EAAErH,SAAyC;QACpDsH,KAAK,EAAErH,KAAqC;QAC5CsH,OAAO,EAAErH,OAAuC;QAChDsH,MAAM,EAAErH,MAAsC;QAC9CsH,IAAI,EAAErH,QAAwC;QAC9CsH,SAAS,EAAErH,SAAyC;QACpDsH,UAAU,EAAErH,UAA0C;QACtDsH,IAAI,EAAE;UACJjC,KAAK,EAAEpF,IAAoC;UAC3CsF,MAAM,EAAE;YACNgC,QAAQ,EAAE,wCAAwC,CAAE;;SAEvD;QACDC,KAAK,EAAEtH;;KAEV,CAAC;EACJ;EAEA;EACQuH,kBAAkBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAAC5G,MAAM,EAAE,OAAO,EAAE;IAE3B,MAAM6G,KAAK,GAAG,IAAI,CAAC7G,MAAM,CAACgE,IAAI,EAAE,CAACC,IAAI,CAACC,IAAI,IAAG;MAC3C,MAAM4C,QAAQ,GAAG,IAAI,CAACC,eAAe,CAAC7C,IAAI,CAAC;MAC3C,OAAO4C,QAAQ;IACjB,CAAC,CAAC,CAAC7C,IAAI,CAAC6C,QAAQ,IAAIA,QAAQ,CAAC;IAC7B,OAAO,EAAE;EACX;EAEA;EACQC,eAAeA,CAACF,KAAU;IAChC,IAAI,CAACA,KAAK,IAAI,CAACA,KAAK,CAACG,GAAG,EAAE;MACxB9E,OAAO,CAACW,KAAK,CAAC,uBAAuB,EAAEgE,KAAK,CAAC;MAC7C,OAAO,EAAE;;IAGX,IAAIC,QAAQ,GAAG,EAAE;IACjBD,KAAK,CAACG,GAAG,CAACC,OAAO,CAAEC,EAAO,IAAI;MAC5B,IAAI,OAAOA,EAAE,CAACC,MAAM,KAAK,QAAQ,EAAE;QACjC,IAAItG,IAAI,GAAGqG,EAAE,CAACC,MAAM;QACpB,IAAID,EAAE,CAACE,UAAU,EAAE;UACjB,IAAIF,EAAE,CAACE,UAAU,CAACC,IAAI,EAAExG,IAAI,GAAG,MAAMA,IAAI,KAAK;UAC9C,IAAIqG,EAAE,CAACE,UAAU,CAACE,MAAM,EAAEzG,IAAI,GAAG,IAAIA,IAAI,GAAG;UAC5C,IAAIqG,EAAE,CAACE,UAAU,CAACd,IAAI,EAAEzF,IAAI,GAAG,KAAKA,IAAI,IAAI;UAC5C,IAAIqG,EAAE,CAACE,UAAU,CAAC7C,MAAM,KAAK,CAAC,EAAE1D,IAAI,GAAG,KAAKA,IAAI,EAAE;UAClD,IAAIqG,EAAE,CAACE,UAAU,CAACG,UAAU,EAAE1G,IAAI,GAAG,KAAKA,IAAI,EAAE;UAChD,IAAIqG,EAAE,CAACE,UAAU,CAACvC,IAAI,KAAK,QAAQ,EAAEhE,IAAI,GAAG,KAAKA,IAAI,EAAE;UACvD,IAAIqG,EAAE,CAACE,UAAU,CAACvC,IAAI,KAAK,SAAS,EAAEhE,IAAI,GAAG,MAAMA,IAAI,EAAE;;QAE3DiG,QAAQ,IAAIjG,IAAI;OACjB,MAAM,IAAIqG,EAAE,CAACC,MAAM,IAAID,EAAE,CAACC,MAAM,CAACrC,KAAK,EAAE;QACvCgC,QAAQ,IAAI,YAAYI,EAAE,CAACC,MAAM,CAACrC,KAAK,KAAK;;IAEhD,CAAC,CAAC;IACF,OAAOgC,QAAQ;EACjB;EAEAU,eAAeA,CAAA;IACb,IAAI,CAAChH,oBAAoB,GAAG,IAAI;IAChC,MAAMiH,cAAc,GAAG,IAAI,CAACb,kBAAkB,EAAE;IAChD,IAAIa,cAAc,EAAE;MAClB,IAAI,CAAChH,QAAQ,GAAG,2BAA2BgH,cAAc,yBAAyB,IAAI,CAACxG,cAAc,EAAE;MACvG,IAAI,CAACN,iBAAiB,GAAG,IAAI;;EAEjC;EAEM+G,WAAWA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACf,IAAI,CAACD,KAAI,CAAC3H,MAAM,EAAE;MAElB2H,KAAI,CAACtH,QAAQ,GAAG,IAAI;MACpB,IAAI;QACF,MAAMwH,UAAU,SAASF,KAAI,CAAC3H,MAAM,CAACgE,IAAI,EAAE;QAC3C,IAAI,CAAC6D,UAAU,IAAI,CAACA,UAAU,CAAC1D,MAAM,EAAE;UACrC,MAAM,IAAI2D,KAAK,CAAC,qBAAqB,CAAC;;QAExC,MAAMzF,IAAI,GAAkB;UAC1B0F,EAAE,EAAEJ,KAAI,CAACvG,aAAa,IAAI4G,SAAS;UACnCzF,KAAK,EAAEoF,KAAI,CAACxG,SAAS,IAAI,eAAe;UACxCW,WAAW,EAAE6F,KAAI,CAAC7F,WAAW;UAC7BU,OAAO,EAAEE,IAAI,CAACuF,SAAS,CAACJ,UAAU,CAAC;UACnCvF,SAAS,EAAEqF,KAAI,CAACjG,OAAO,GAAG,IAAI,GAAG;SAClC;QAGDQ,OAAO,CAACC,GAAG,CAACE,IAAI,CAAC;QACjB,MAAMsF,KAAI,CAAC/H,YAAY,CAACsI,kBAAkB,CAAC7F,IAAI,CAAC,CAAC8F,SAAS,EAAE;QAC5DR,KAAI,CAACzH,SAAS,GAAG,IAAI;QACrByH,KAAI,CAACpE,YAAY,EAAE;OAGpB,CACD,OAAOV,KAAK,EAAE;QACZX,OAAO,CAACW,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;OAC3C,SAAS;QAER,IAAI8E,KAAI,CAAChG,SAAS,EAAE;UAClBgG,KAAI,CAAC9H,MAAM,CAACuI,QAAQ,CAAC,CAAC,2BAA2B,CAAC,CAAC;SACpD,MAAM;UACLT,KAAI,CAAC9H,MAAM,CAACuI,QAAQ,CAAC,CAAC,uBAAuB,CAAC,CAAC;;;IAElD;EACH;EAEAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACpI,aAAa,EAAE;MACtB,IAAI,CAACA,aAAa,CAACqI,OAAO,EAAE;;IAE9B,IAAI,CAAC3G,SAAS,GAAG,KAAK;EACxB;EAEQ4G,eAAeA,CAAA;IACrB,IAAI,IAAI,CAACvI,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACgE,IAAI,EAAE,CAACC,IAAI,CAACC,IAAI,IAAG;QAC7B,MAAMrD,IAAI,GAAGqD,IAAI,CAACC,MAAM,CAACqE,GAAG,CAACC,KAAK,IAAIA,KAAK,CAACvE,IAAI,CAACrD,IAAI,IAAI,EAAE,CAAC,CAAC6H,IAAI,CAAC,GAAG,CAAC;QACtE,IAAI,CAACvI,SAAS,GAAGU,IAAI,CAAC8H,IAAI,EAAE,GAAG9H,IAAI,CAAC8H,IAAI,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC,CAACxE,MAAM,GAAG,CAAC;MACpE,CAAC,CAAC;;EAEN;EAEAb,YAAYA,CAAA;IACV,IAAI,IAAI,CAACvD,MAAM,IAAI,IAAI,CAACA,MAAM,CAACmE,MAAM,EAAE;MACrC,IAAI,CAACnE,MAAM,CAACmE,MAAM,CAAC0E,KAAK,EAAE,CAAC,CAAC;MAC5B,IAAI,CAAC1H,SAAS,GAAG,EAAE;MACnB,IAAI,CAACC,aAAa,GAAG,IAAI;;EAE7B;EAKA0H,cAAcA,CAACf,EAAU;IACvB,IAAI,CAACxG,YAAY,GAAGwG,EAAE;IACtB,IAAI,CAACzG,eAAe,GAAG,IAAI;EAC7B;EAEAyH,YAAYA,CAAC1G,IAAU;IACrB,IAAI,IAAI,CAACrC,MAAM,EAAE;MACf,IAAI,CAACmB,SAAS,GAAGkB,IAAI,CAACE,KAAK;MAC3B,IAAI,CAACnB,aAAa,GAAGiB,IAAI,CAAC0F,EAAE;MAC5B;MACA,IAAI,CAAC/H,MAAM,CAACmE,MAAM,CAAC0E,KAAK,EAAE;MAC1B,IAAI;QACF,MAAMrG,OAAO,GAAGE,IAAI,CAACC,KAAK,CAACN,IAAI,CAACG,OAAO,CAAC;QACxC,IAAI,CAACxC,MAAM,CAACgJ,MAAM,CAACxG,OAAO,CAAC;OAC5B,CAAC,OAAOyG,CAAC,EAAE;QACV/G,OAAO,CAACW,KAAK,CAAC,iCAAiC,EAAEoG,CAAC,CAAC;;MAErD,IAAI,CAACV,eAAe,EAAE;;EAE1B;EAEAW,YAAYA,CAACC,GAAS;IACpB,IAAI,IAAI,CAACnJ,MAAM,EAAE;MACf,IAAI,CAACmB,SAAS,GAAGgI,GAAG,CAAC5G,KAAK;MAC1B,IAAI,CAACnB,aAAa,GAAG+H,GAAG,CAACpB,EAAE;MAC3B,IAAI;QACF,MAAMvF,OAAO,GAAGE,IAAI,CAACC,KAAK,CAACwG,GAAG,CAAC3G,OAAO,CAAC;QACvC,IAAI,CAACxC,MAAM,CAACgJ,MAAM,CAACxG,OAAO,CAAC;OAC5B,CAAC,OAAOyG,CAAC,EAAE;QACV/G,OAAO,CAACW,KAAK,CAAC,iCAAiC,EAAEoG,CAAC,CAAC;;MAErD,IAAI,CAAC/I,SAAS,GAAG,KAAK;;EAE1B;EAEAkJ,UAAUA,CAAA;IACR,IAAI,CAAC/H,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;IAClC,IAAI,IAAI,CAACA,UAAU,IAAI,CAAC,IAAI,CAACrB,MAAM,EAAE;MACnC,IAAI,CAACqD,gBAAgB,EAAE;;EAE3B;EAEMgG,aAAaA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA1B,iBAAA;MACjB,IAAI0B,MAAI,CAAC/H,YAAY,EAAE;QACrB,IAAI;UACF,MAAM+H,MAAI,CAAC1J,YAAY,CAAC2J,UAAU,CAACD,MAAI,CAAC/H,YAAY,CAAC,CAAC4G,SAAS,EAAE;SAClE,CAAC,OAAOtF,KAAK,EAAE;UACdX,OAAO,CAACW,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;SACjD,SAAS;UACRyG,MAAI,CAAChI,eAAe,GAAG,KAAK;UAC5BgI,MAAI,CAAC/H,YAAY,GAAG,IAAI;;;IAE3B;EACH;EAEMqB,mBAAmBA,CAAC4G,UAAe;IAAA,IAAAC,MAAA;IAAA,OAAA7B,iBAAA;MACvC,IAAI;QACF,IAAI,CAAC6B,MAAI,CAACzJ,MAAM,EAAE;UAChBkC,OAAO,CAACwH,IAAI,CAAC,4BAA4B,CAAC;UAC1C;;QAGF;QACA,MAAMD,MAAI,CAACzJ,MAAM,CAAC2J,OAAO;QAEzB;QACA,MAAMF,MAAI,CAACzJ,MAAM,CAACmE,MAAM,CAAC0E,KAAK,EAAE;QAEhC;QACA,MAAMY,MAAI,CAACzJ,MAAM,CAACgJ,MAAM,CAACQ,UAAU,CAAC;QAEpC;QACAC,MAAI,CAAClB,eAAe,EAAE;QACtBkB,MAAI,CAAChI,UAAU,GAAG,IAAI;OACvB,CAAC,OAAOoB,KAAK,EAAE;QACdX,OAAO,CAACW,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;;IACvD;EACH;EAEA;EACM+G,kBAAkBA,CAACpH,OAAe;IAAA,IAAAqH,MAAA;IAAA,OAAAjC,iBAAA;MACtC,IAAIiC,MAAI,CAAC7J,MAAM,EAAE;QACf,MAAM6J,MAAI,CAAC7J,MAAM,CAACmE,MAAM,CAACgD,MAAM,CAAC,WAAW,EAAE;UAC3CtG,IAAI,EAAE2B;SACP,CAAC;;IACH;EACH;EAEA;EACMsH,mBAAmBA,CAACC,UAAkB,EAAEC,OAAY;IAAA,IAAAC,MAAA;IAAA,OAAArC,iBAAA;MACxD,IAAIqC,MAAI,CAACjK,MAAM,EAAE;QACf,MAAMkK,YAAY,SAASD,MAAI,CAACjK,MAAM,CAACmE,MAAM,CAACgG,eAAe,CAACJ,UAAU,CAAC;QACzE,IAAIG,YAAY,EAAE;UAChB,MAAMD,MAAI,CAACjK,MAAM,CAACmE,MAAM,CAACiG,MAAM,CAACF,YAAY,CAACnC,EAAE,EAAEiC,OAAO,CAAC;;;IAE5D;EACH;EAEMK,cAAcA,CAACC,OAAe;IAAA,IAAAC,MAAA;IAAA,OAAA3C,iBAAA;MAClC,IAAI,CAAC2C,MAAI,CAACvK,MAAM,EAAE;MAElB,QAAQsK,OAAO;QACb,KAAK,MAAM;UACT;UACA;UACA,MAAMC,MAAI,CAACvK,MAAM,CAACmE,MAAM,CAACgD,MAAM,CAAC,MAAM,EAAE;YACtCV,IAAI,EAAE,EAAE;YACR+D,IAAI,EAAE;cACJjI,KAAK,EAAE,EAAE;cACTkI,WAAW,EAAE,EAAE;cACf3F,KAAK,EAAE;gBACL/B,GAAG,EAAE;;;WAGV,CAAC;UACF;QAEF;QACA;UACEb,OAAO,CAACwH,IAAI,CAAC,oBAAoBY,OAAO,EAAE,CAAC;;IAC9C;EACH;EAEMI,QAAQA,CAACC,SAAiB;IAAA,IAAAC,MAAA;IAAA,OAAAhD,iBAAA;MAC9B,IAAI,CAACgD,MAAI,CAAC5K,MAAM,EAAE;MAElB,IAAI;QACF,MAAM4K,MAAI,CAAC5K,MAAM,CAACmE,MAAM,CAACgD,MAAM,CAACwD,SAAS,CAAC;OAC3C,CAAC,OAAO9H,KAAK,EAAE;QACdX,OAAO,CAACW,KAAK,CAAC,8BAA8B8H,SAAS,GAAG,EAAE9H,KAAK,CAAC;;IACjE;EACH;;;;;;;;;;;;;;;cA/aCxE,SAAS;QAAAwM,IAAA,GAAC,eAAe;MAAA;;cACzBxM,SAAS;QAAAwM,IAAA,GAAC,WAAW;MAAA;;;;AAFXnL,eAAe,GAAAoL,UAAA,EAP3B1M,SAAS,CAAC;EACT2M,QAAQ,EAAE,YAAY;EACtBC,OAAO,EAAE,CAAC1M,YAAY,EAAEC,WAAW,EAAEE,cAAc,CAAC;EACpDwM,UAAU,EAAE,IAAI;EAChBC,QAAA,EAAAC,oBAAsC;;CAEvC,CAAC,C,EACWzL,eAAe,CAkb3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}