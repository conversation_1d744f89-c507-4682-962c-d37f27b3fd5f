{"ast": null, "code": "import { Attributor, ClassAttributor, Scope, StyleAttributor } from 'parchment';\nconst config = {\n  scope: Scope.BLOCK,\n  whitelist: ['rtl']\n};\nconst DirectionAttribute = new Attributor('direction', 'dir', config);\nconst DirectionClass = new ClassAttributor('direction', 'ql-direction', config);\nconst DirectionStyle = new StyleAttributor('direction', 'direction', config);\nexport { DirectionAttribute, DirectionClass, DirectionStyle };", "map": {"version": 3, "names": ["Attributor", "ClassAttributor", "<PERSON><PERSON>", "StyleAttributor", "config", "scope", "BLOCK", "whitelist", "DirectionAttribute", "DirectionClass", "DirectionStyle"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/quill/formats/direction.js"], "sourcesContent": ["import { Attributor, ClassAttributor, Scope, StyleAttributor } from 'parchment';\nconst config = {\n  scope: Scope.BLOCK,\n  whitelist: ['rtl']\n};\nconst DirectionAttribute = new Attributor('direction', 'dir', config);\nconst DirectionClass = new ClassAttributor('direction', 'ql-direction', config);\nconst DirectionStyle = new StyleAttributor('direction', 'direction', config);\nexport { DirectionAttribute, DirectionClass, DirectionStyle };\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,eAAe,EAAEC,KAAK,EAAEC,eAAe,QAAQ,WAAW;AAC/E,MAAMC,MAAM,GAAG;EACbC,KAAK,EAAEH,KAAK,CAACI,KAAK;EAClBC,SAAS,EAAE,CAAC,KAAK;AACnB,CAAC;AACD,MAAMC,kBAAkB,GAAG,IAAIR,UAAU,CAAC,WAAW,EAAE,KAAK,EAAEI,MAAM,CAAC;AACrE,MAAMK,cAAc,GAAG,IAAIR,eAAe,CAAC,WAAW,EAAE,cAAc,EAAEG,MAAM,CAAC;AAC/E,MAAMM,cAAc,GAAG,IAAIP,eAAe,CAAC,WAAW,EAAE,WAAW,EAAEC,MAAM,CAAC;AAC5E,SAASI,kBAAkB,EAAEC,cAAc,EAAEC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}