{"ast": null, "code": "// these aren't really private, but nor are they really useful to document\n\n/**\n * @private\n */\nclass LuxonError extends Error {}\n\n/**\n * @private\n */\nexport class InvalidDateTimeError extends LuxonError {\n  constructor(reason) {\n    super(`Invalid DateTime: ${reason.toMessage()}`);\n  }\n}\n\n/**\n * @private\n */\nexport class InvalidIntervalError extends LuxonError {\n  constructor(reason) {\n    super(`Invalid Interval: ${reason.toMessage()}`);\n  }\n}\n\n/**\n * @private\n */\nexport class InvalidDurationError extends LuxonError {\n  constructor(reason) {\n    super(`Invalid Duration: ${reason.toMessage()}`);\n  }\n}\n\n/**\n * @private\n */\nexport class ConflictingSpecificationError extends LuxonError {}\n\n/**\n * @private\n */\nexport class InvalidUnitError extends LuxonError {\n  constructor(unit) {\n    super(`Invalid unit ${unit}`);\n  }\n}\n\n/**\n * @private\n */\nexport class InvalidArgumentError extends LuxonError {}\n\n/**\n * @private\n */\nexport class ZoneIsAbstractError extends LuxonError {\n  constructor() {\n    super(\"Zone is an abstract class\");\n  }\n}", "map": {"version": 3, "names": ["LuxonError", "Error", "InvalidDateTimeError", "constructor", "reason", "toMessage", "InvalidIntervalError", "InvalidDurationError", "ConflictingSpecificationError", "InvalidUnitError", "unit", "InvalidArgumentError", "ZoneIsAbstractError"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/luxon/src/errors.js"], "sourcesContent": ["// these aren't really private, but nor are they really useful to document\n\n/**\n * @private\n */\nclass LuxonError extends Error {}\n\n/**\n * @private\n */\nexport class InvalidDateTimeError extends LuxonError {\n  constructor(reason) {\n    super(`Invalid DateTime: ${reason.toMessage()}`);\n  }\n}\n\n/**\n * @private\n */\nexport class InvalidIntervalError extends LuxonError {\n  constructor(reason) {\n    super(`Invalid Interval: ${reason.toMessage()}`);\n  }\n}\n\n/**\n * @private\n */\nexport class InvalidDurationError extends LuxonError {\n  constructor(reason) {\n    super(`Invalid Duration: ${reason.toMessage()}`);\n  }\n}\n\n/**\n * @private\n */\nexport class ConflictingSpecificationError extends LuxonError {}\n\n/**\n * @private\n */\nexport class InvalidUnitError extends LuxonError {\n  constructor(unit) {\n    super(`Invalid unit ${unit}`);\n  }\n}\n\n/**\n * @private\n */\nexport class InvalidArgumentError extends LuxonError {}\n\n/**\n * @private\n */\nexport class ZoneIsAbstractError extends LuxonError {\n  constructor() {\n    super(\"Zone is an abstract class\");\n  }\n}\n"], "mappings": "AAAA;;AAEA;AACA;AACA;AACA,MAAMA,UAAU,SAASC,KAAK,CAAC;;AAE/B;AACA;AACA;AACA,OAAO,MAAMC,oBAAoB,SAASF,UAAU,CAAC;EACnDG,WAAWA,CAACC,MAAM,EAAE;IAClB,KAAK,CAAE,qBAAoBA,MAAM,CAACC,SAAS,CAAC,CAAE,EAAC,CAAC;EAClD;AACF;;AAEA;AACA;AACA;AACA,OAAO,MAAMC,oBAAoB,SAASN,UAAU,CAAC;EACnDG,WAAWA,CAACC,MAAM,EAAE;IAClB,KAAK,CAAE,qBAAoBA,MAAM,CAACC,SAAS,CAAC,CAAE,EAAC,CAAC;EAClD;AACF;;AAEA;AACA;AACA;AACA,OAAO,MAAME,oBAAoB,SAASP,UAAU,CAAC;EACnDG,WAAWA,CAACC,MAAM,EAAE;IAClB,KAAK,CAAE,qBAAoBA,MAAM,CAACC,SAAS,CAAC,CAAE,EAAC,CAAC;EAClD;AACF;;AAEA;AACA;AACA;AACA,OAAO,MAAMG,6BAA6B,SAASR,UAAU,CAAC;;AAE9D;AACA;AACA;AACA,OAAO,MAAMS,gBAAgB,SAAST,UAAU,CAAC;EAC/CG,WAAWA,CAACO,IAAI,EAAE;IAChB,KAAK,CAAE,gBAAeA,IAAK,EAAC,CAAC;EAC/B;AACF;;AAEA;AACA;AACA;AACA,OAAO,MAAMC,oBAAoB,SAASX,UAAU,CAAC;;AAErD;AACA;AACA;AACA,OAAO,MAAMY,mBAAmB,SAASZ,UAAU,CAAC;EAClDG,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,2BAA2B,CAAC;EACpC;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}