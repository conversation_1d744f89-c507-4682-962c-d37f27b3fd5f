{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./memory.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./memory.component.css?ngResource\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router, RouterLink, RouterModule } from '@angular/router';\nimport { FormsModule } from '@angular/forms';\nimport { MemoryServiceProxy } from '../../../shared/service-proxies/service-proxies';\nimport { MarkdownModule } from 'ngx-markdown';\nlet MemoryComponent = class MemoryComponent {\n  constructor(_memoryService, router) {\n    this._memoryService = _memoryService;\n    this.router = router;\n    this.memory = [];\n    this.filteredMemory = [];\n    this.searchTerm = '';\n    this.loading = true;\n    this.confirmDeleteId = null;\n    this.selectedMemory = null;\n  }\n  ngOnInit() {\n    this.loadMemory();\n  }\n  loadMemory() {\n    this.loading = true;\n    this._memoryService.getAll().subscribe(res => {\n      this.memory = res;\n      this.filteredMemory = [...this.memory];\n      this.loading = false;\n      console.log(res);\n    });\n  }\n  deleteMemory(id) {\n    this._memoryService.delete(id).subscribe({\n      next: () => {\n        this.memory = this.memory.filter(x => x.id !== id);\n        this.filteredMemory = this.filteredMemory.filter(x => x.id !== id);\n        this.confirmDeleteId = null;\n      },\n      error: err => {\n        console.error('Error deleting Memory:', err);\n        this.confirmDeleteId = null;\n      }\n    });\n  }\n  confirmDelete(id) {\n    this.confirmDeleteId = id;\n  }\n  cancelDelete() {\n    this.confirmDeleteId = null;\n  }\n  searchMemory() {\n    if (!this.searchTerm.trim()) {\n      this.filteredMemory = [...this.memory];\n      return;\n    }\n    const term = this.searchTerm.toLowerCase().trim();\n    this.filteredMemory = this.memory.filter(memory => {\n      // Search in content\n      const content = this.getDisplayContent(memory);\n      // Search in email\n      const email = memory.email?.toLowerCase() || '';\n      return content.toLowerCase().includes(term) || email.includes(term);\n    });\n  }\n  addMemory() {\n    this.router.navigate(['/settings/memory/add']);\n  }\n  getDisplayContent(memory) {\n    if (!memory.content) return '';\n    try {\n      // Check if content is in EditorJS format\n      if (memory.content.startsWith('{')) {\n        const parsed = JSON.parse(memory.content);\n        if (parsed.blocks && Array.isArray(parsed.blocks)) {\n          // For card display, extract text from blocks\n          return parsed.blocks.map(block => {\n            // Handle different block types\n            switch (block.type) {\n              case 'paragraph':\n              case 'header':\n                return block.data?.text || '';\n              case 'list':\n                if (block.data?.items && Array.isArray(block.data.items)) {\n                  // Extract text from list items\n                  return block.data.items.map(item => this.getItemText(item)).join(', ');\n                }\n                return '';\n              case 'checklist':\n                if (block.data?.items && Array.isArray(block.data.items)) {\n                  // Extract text from checklist items\n                  return block.data.items.map(item => this.getItemText(item)).join(', ');\n                }\n                return '';\n              case 'quote':\n                return block.data?.text || '';\n              case 'code':\n                return '[Code block]';\n              case 'table':\n                return '[Table]';\n              default:\n                return block.data?.text || '';\n            }\n          }).filter(text => text.trim() !== '').join(' ');\n        }\n      }\n      // Return as is if not in EditorJS format\n      return memory.content;\n    } catch (e) {\n      // If parsing fails, return content as is\n      return memory.content;\n    }\n  }\n  // Format EditorJS content to HTML for detailed display\n  formatEditorContent(content) {\n    if (!content) return '';\n    try {\n      const parsedContent = JSON.parse(content);\n      if (!parsedContent.blocks || !Array.isArray(parsedContent.blocks)) {\n        return content; // Return original content if not in expected format\n      }\n      let html = '';\n      parsedContent.blocks.forEach(block => {\n        switch (block.type) {\n          case 'header':\n            const level = block.data.level || 2;\n            html += `<h${level} class=\"text-[var(--text-dark)] font-semibold mb-3 mt-4\">${block.data.text}</h${level}>`;\n            break;\n          case 'paragraph':\n            html += `<p class=\"text-[var(--text-dark)] mb-3\">${block.data.text}</p>`;\n            break;\n          case 'list':\n            const listTag = block.data.style === 'ordered' ? 'ol' : 'ul';\n            html += `<${listTag} class=\"list-${block.data.style === 'ordered' ? 'decimal' : 'disc'} pl-5 mb-3 text-[var(--text-dark)]\">`;\n            if (block.data.items && Array.isArray(block.data.items)) {\n              block.data.items.forEach(item => {\n                // Handle different item formats (string, object, etc.)\n                const itemText = this.getItemText(item);\n                html += `<li class=\"mb-1\">${itemText}</li>`;\n              });\n            }\n            html += `</${listTag}>`;\n            break;\n          case 'checklist':\n            html += `<div class=\"mb-3\">`;\n            if (block.data.items && Array.isArray(block.data.items)) {\n              block.data.items.forEach(item => {\n                // Get the checked state (default to false if not present)\n                const isChecked = item.checked === true;\n                // Get the text content using our helper method\n                const itemText = this.getItemText(item);\n                html += `\n                  <div class=\"checklist-item ${isChecked ? 'checked' : ''}\">\n                    <div class=\"checklist-item-checkbox ${isChecked ? 'checked' : ''}\">\n                      ${isChecked ? '<i class=\"ri-check-line text-white text-xs\"></i>' : ''}\n                    </div>\n                    <span class=\"checklist-item-text\">${itemText}</span>\n                  </div>\n                `;\n              });\n            }\n            html += `</div>`;\n            break;\n          case 'quote':\n            html += `\n              <blockquote class=\"border-l-4 border-[var(--primary-purple)] pl-4 py-1 mb-3 italic text-[var(--text-dark)]\">\n                <p>${block.data.text}</p>\n                ${block.data.caption ? `<cite class=\"text-sm text-[var(--text-medium-gray)] mt-1 block\">— ${block.data.caption}</cite>` : ''}\n              </blockquote>\n            `;\n            break;\n          case 'delimiter':\n            html += `<hr class=\"my-4 border-t border-[var(--hover-blue-gray)]\">`;\n            break;\n          case 'code':\n            html += `\n              <pre class=\"bg-[var(--background-light-gray)] p-3 rounded-lg mb-3 overflow-x-auto\">\n                <code class=\"text-[var(--text-dark)] text-sm font-mono\">${block.data.code}</code>\n              </pre>\n            `;\n            break;\n          case 'table':\n            html += `<div class=\"mb-4 overflow-x-auto\">\n              <table class=\"min-w-full border-collapse border border-[var(--hover-blue-gray)]\">\n                <tbody>`;\n            block.data.content.forEach(row => {\n              html += `<tr>`;\n              row.forEach(cell => {\n                html += `<td class=\"border border-[var(--hover-blue-gray)] p-2 text-[var(--text-dark)]\">${cell}</td>`;\n              });\n              html += `</tr>`;\n            });\n            html += `</tbody></table></div>`;\n            break;\n          default:\n            if (block.data && block.data.text) {\n              html += `<p class=\"text-[var(--text-dark)] mb-3\">${block.data.text}</p>`;\n            }\n        }\n      });\n      return html;\n    } catch (e) {\n      console.error('Error formatting EditorJS content:', e);\n      return content; // Return original content if parsing fails\n    }\n  }\n  // Select a memory to view details\n  selectMemory(memory) {\n    this.selectedMemory = memory;\n    // Add any additional logic needed when selecting a memory\n    console.log('Selected memory:', memory);\n  }\n  // Close the memory detail view\n  closeMemoryDetail() {\n    this.selectedMemory = null;\n  }\n  // Helper method to safely extract text from various data types\n  getItemText(item) {\n    if (item === null || item === undefined) {\n      return '';\n    }\n    // If item is a string, return it directly\n    if (typeof item === 'string') {\n      return item;\n    }\n    // If item is an object with a 'content' property (common in EditorJS)\n    if (typeof item === 'object') {\n      // Check for common properties that might contain text\n      if (item.content) {\n        return item.content;\n      }\n      if (item.text) {\n        return item.text;\n      }\n      if (item.value) {\n        return item.value;\n      }\n      // If it's an object but doesn't have expected properties, try to get a meaningful string\n      try {\n        // Try to convert to JSON string if it's a complex object\n        const jsonStr = JSON.stringify(item);\n        // If it looks like a complex object, return a simplified version\n        if (jsonStr.startsWith('{') && jsonStr.length > 2) {\n          return item.toString ? item.toString() : 'List item';\n        }\n        return jsonStr;\n      } catch (e) {\n        return 'List item';\n      }\n    }\n    // For any other type, convert to string\n    return String(item);\n  }\n  formatDate(dateString) {\n    if (!dateString) return '';\n    // Handle different date formats\n    let date;\n    if (typeof dateString === 'string') {\n      date = new Date(dateString);\n    } else if (dateString && typeof dateString === 'object' && dateString.toString) {\n      date = new Date(dateString.toString());\n    } else {\n      return 'Unknown date';\n    }\n    // Check if date is valid\n    if (isNaN(date.getTime())) {\n      return 'Invalid date';\n    }\n    const now = new Date();\n    const yesterday = new Date(now);\n    yesterday.setDate(yesterday.getDate() - 1);\n    // Check if date is today\n    if (date.toDateString() === now.toDateString()) {\n      return 'Today, ' + date.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    }\n    // Check if date is yesterday\n    if (date.toDateString() === yesterday.toDateString()) {\n      return 'Yesterday, ' + date.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    }\n    // Otherwise return formatted date\n    return date.toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    });\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: MemoryServiceProxy\n    }, {\n      type: Router\n    }];\n  }\n};\nMemoryComponent = __decorate([Component({\n  selector: 'app-memory',\n  standalone: true,\n  imports: [CommonModule, RouterModule, FormsModule, RouterLink, MarkdownModule],\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], MemoryComponent);\nexport { MemoryComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "Router", "RouterLink", "RouterModule", "FormsModule", "MemoryServiceProxy", "MarkdownModule", "MemoryComponent", "constructor", "_memoryService", "router", "memory", "filteredMemory", "searchTerm", "loading", "confirmDeleteId", "<PERSON><PERSON><PERSON><PERSON>", "ngOnInit", "loadMemory", "getAll", "subscribe", "res", "console", "log", "deleteMemory", "id", "delete", "next", "filter", "x", "error", "err", "confirmDelete", "cancelDelete", "searchMemory", "trim", "term", "toLowerCase", "content", "getDisplayContent", "email", "includes", "addMemory", "navigate", "startsWith", "parsed", "JSON", "parse", "blocks", "Array", "isArray", "map", "block", "type", "data", "text", "items", "item", "getItemText", "join", "e", "formatEditorContent", "parsed<PERSON><PERSON><PERSON>", "html", "for<PERSON>ach", "level", "listTag", "style", "itemText", "isChecked", "checked", "caption", "code", "row", "cell", "selectMemory", "closeMemoryDetail", "undefined", "value", "jsonStr", "stringify", "length", "toString", "String", "formatDate", "dateString", "date", "Date", "isNaN", "getTime", "now", "yesterday", "setDate", "getDate", "toDateString", "toLocaleTimeString", "hour", "minute", "toLocaleDateString", "year", "month", "day", "__decorate", "selector", "standalone", "imports", "template", "__NG_CLI_RESOURCE__0"], "sources": ["C:\\Users\\<USER>\\source\\ai-hub\\WebApp\\src\\app\\admin\\Notes\\memory.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { Router, RouterLink, RouterModule } from '@angular/router';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { Memory, MemoryServiceProxy } from '../../../shared/service-proxies/service-proxies';\r\nimport { MarkdownModule } from 'ngx-markdown';\r\n\r\n@Component({\r\n  selector: 'app-memory',\r\n  standalone: true,\r\n  imports: [CommonModule, RouterModule, FormsModule,RouterLink,MarkdownModule],\r\n  templateUrl: './memory.component.html',\r\n  styleUrl: './memory.component.css',\r\n})\r\nexport class MemoryComponent implements OnInit {\r\n  memory: Memory[] = [];\r\n  filteredMemory: Memory[] = [];\r\n  searchTerm: string = '';\r\n  loading: boolean = true;\r\n  confirmDeleteId: string | null = null;\r\n  selectedMemory: Memory | null = null;\r\n\r\n  constructor(private _memoryService: MemoryServiceProxy, private router: Router) { }\r\n\r\n  ngOnInit() {\r\n    this.loadMemory();\r\n  }\r\n\r\n  loadMemory() {\r\n    this.loading = true;\r\n    this._memoryService.getAll().subscribe((res: any) => {\r\n      this.memory = res;\r\n      this.filteredMemory = [...this.memory];\r\n      this.loading = false;\r\n      console.log(res);\r\n    });\r\n  }\r\n\r\n  deleteMemory(id: string) {\r\n    this._memoryService.delete(id).subscribe({\r\n      next: () => {\r\n        this.memory = this.memory.filter(x => x.id !== id);\r\n        this.filteredMemory = this.filteredMemory.filter(x => x.id !== id);\r\n        this.confirmDeleteId = null;\r\n      },\r\n      error: (err) => {\r\n        console.error('Error deleting Memory:', err);\r\n        this.confirmDeleteId = null;\r\n      }\r\n    });\r\n  }\r\n\r\n  confirmDelete(id: string) {\r\n    this.confirmDeleteId = id;\r\n  }\r\n\r\n  cancelDelete() {\r\n    this.confirmDeleteId = null;\r\n  }\r\n\r\n  searchMemory() {\r\n    if (!this.searchTerm.trim()) {\r\n      this.filteredMemory = [...this.memory];\r\n      return;\r\n    }\r\n\r\n    const term = this.searchTerm.toLowerCase().trim();\r\n    this.filteredMemory = this.memory.filter(memory => {\r\n      // Search in content\r\n      const content = this.getDisplayContent(memory);\r\n      // Search in email\r\n      const email = memory.email?.toLowerCase() || '';\r\n\r\n      return content.toLowerCase().includes(term) || email.includes(term);\r\n    });\r\n  }\r\n\r\n  addMemory() {\r\n    this.router.navigate(['/settings/memory/add']);\r\n  }\r\n\r\n\r\n  getDisplayContent(memory: Memory): string {\r\n    if (!memory.content) return '';\r\n\r\n    try {\r\n      // Check if content is in EditorJS format\r\n      if (memory.content.startsWith('{')) {\r\n        const parsed = JSON.parse(memory.content);\r\n        if (parsed.blocks && Array.isArray(parsed.blocks)) {\r\n          // For card display, extract text from blocks\r\n          return parsed.blocks.map((block: any) => {\r\n            // Handle different block types\r\n            switch (block.type) {\r\n              case 'paragraph':\r\n              case 'header':\r\n                return block.data?.text || '';\r\n\r\n              case 'list':\r\n                if (block.data?.items && Array.isArray(block.data.items)) {\r\n                  // Extract text from list items\r\n                  return block.data.items.map((item: any) => this.getItemText(item)).join(', ');\r\n                }\r\n                return '';\r\n\r\n              case 'checklist':\r\n                if (block.data?.items && Array.isArray(block.data.items)) {\r\n                  // Extract text from checklist items\r\n                  return block.data.items.map((item: any) => this.getItemText(item)).join(', ');\r\n                }\r\n                return '';\r\n\r\n              case 'quote':\r\n                return block.data?.text || '';\r\n\r\n              case 'code':\r\n                return '[Code block]';\r\n\r\n              case 'table':\r\n                return '[Table]';\r\n\r\n              default:\r\n                return block.data?.text || '';\r\n            }\r\n          }).filter((text: string) => text.trim() !== '').join(' ');\r\n        }\r\n      }\r\n      // Return as is if not in EditorJS format\r\n      return memory.content;\r\n    } catch (e) {\r\n      // If parsing fails, return content as is\r\n      return memory.content;\r\n    }\r\n  }\r\n\r\n  // Format EditorJS content to HTML for detailed display\r\n  formatEditorContent(content: string): string {\r\n    if (!content) return '';\r\n\r\n    try {\r\n      const parsedContent = JSON.parse(content);\r\n\r\n      if (!parsedContent.blocks || !Array.isArray(parsedContent.blocks)) {\r\n        return content; // Return original content if not in expected format\r\n      }\r\n\r\n      let html = '';\r\n\r\n      parsedContent.blocks.forEach((block: any) => {\r\n        switch (block.type) {\r\n          case 'header':\r\n            const level = block.data.level || 2;\r\n            html += `<h${level} class=\"text-[var(--text-dark)] font-semibold mb-3 mt-4\">${block.data.text}</h${level}>`;\r\n            break;\r\n\r\n          case 'paragraph':\r\n            html += `<p class=\"text-[var(--text-dark)] mb-3\">${block.data.text}</p>`;\r\n            break;\r\n\r\n          case 'list':\r\n            const listTag = block.data.style === 'ordered' ? 'ol' : 'ul';\r\n            html += `<${listTag} class=\"list-${block.data.style === 'ordered' ? 'decimal' : 'disc'} pl-5 mb-3 text-[var(--text-dark)]\">`;\r\n            if (block.data.items && Array.isArray(block.data.items)) {\r\n              block.data.items.forEach((item: any) => {\r\n                // Handle different item formats (string, object, etc.)\r\n                const itemText = this.getItemText(item);\r\n                html += `<li class=\"mb-1\">${itemText}</li>`;\r\n              });\r\n            }\r\n            html += `</${listTag}>`;\r\n            break;\r\n\r\n          case 'checklist':\r\n            html += `<div class=\"mb-3\">`;\r\n            if (block.data.items && Array.isArray(block.data.items)) {\r\n              block.data.items.forEach((item: any) => {\r\n                // Get the checked state (default to false if not present)\r\n                const isChecked = item.checked === true;\r\n                // Get the text content using our helper method\r\n                const itemText = this.getItemText(item);\r\n\r\n                html += `\r\n                  <div class=\"checklist-item ${isChecked ? 'checked' : ''}\">\r\n                    <div class=\"checklist-item-checkbox ${isChecked ? 'checked' : ''}\">\r\n                      ${isChecked ? '<i class=\"ri-check-line text-white text-xs\"></i>' : ''}\r\n                    </div>\r\n                    <span class=\"checklist-item-text\">${itemText}</span>\r\n                  </div>\r\n                `;\r\n              });\r\n            }\r\n            html += `</div>`;\r\n            break;\r\n\r\n          case 'quote':\r\n            html += `\r\n              <blockquote class=\"border-l-4 border-[var(--primary-purple)] pl-4 py-1 mb-3 italic text-[var(--text-dark)]\">\r\n                <p>${block.data.text}</p>\r\n                ${block.data.caption ? `<cite class=\"text-sm text-[var(--text-medium-gray)] mt-1 block\">— ${block.data.caption}</cite>` : ''}\r\n              </blockquote>\r\n            `;\r\n            break;\r\n\r\n          case 'delimiter':\r\n            html += `<hr class=\"my-4 border-t border-[var(--hover-blue-gray)]\">`;\r\n            break;\r\n\r\n          case 'code':\r\n            html += `\r\n              <pre class=\"bg-[var(--background-light-gray)] p-3 rounded-lg mb-3 overflow-x-auto\">\r\n                <code class=\"text-[var(--text-dark)] text-sm font-mono\">${block.data.code}</code>\r\n              </pre>\r\n            `;\r\n            break;\r\n\r\n          case 'table':\r\n            html += `<div class=\"mb-4 overflow-x-auto\">\r\n              <table class=\"min-w-full border-collapse border border-[var(--hover-blue-gray)]\">\r\n                <tbody>`;\r\n\r\n            block.data.content.forEach((row: string[]) => {\r\n              html += `<tr>`;\r\n              row.forEach((cell: string) => {\r\n                html += `<td class=\"border border-[var(--hover-blue-gray)] p-2 text-[var(--text-dark)]\">${cell}</td>`;\r\n              });\r\n              html += `</tr>`;\r\n            });\r\n\r\n            html += `</tbody></table></div>`;\r\n            break;\r\n\r\n          default:\r\n            if (block.data && block.data.text) {\r\n              html += `<p class=\"text-[var(--text-dark)] mb-3\">${block.data.text}</p>`;\r\n            }\r\n        }\r\n      });\r\n\r\n      return html;\r\n    } catch (e) {\r\n      console.error('Error formatting EditorJS content:', e);\r\n      return content; // Return original content if parsing fails\r\n    }\r\n  }\r\n\r\n  // Select a memory to view details\r\n  selectMemory(memory: Memory) {\r\n    this.selectedMemory = memory;\r\n\r\n    // Add any additional logic needed when selecting a memory\r\n    console.log('Selected memory:', memory);\r\n  }\r\n\r\n  // Close the memory detail view\r\n  closeMemoryDetail() {\r\n    this.selectedMemory = null;\r\n  }\r\n\r\n  // Helper method to safely extract text from various data types\r\n  getItemText(item: any): string {\r\n    if (item === null || item === undefined) {\r\n      return '';\r\n    }\r\n\r\n    // If item is a string, return it directly\r\n    if (typeof item === 'string') {\r\n      return item;\r\n    }\r\n\r\n    // If item is an object with a 'content' property (common in EditorJS)\r\n    if (typeof item === 'object') {\r\n      // Check for common properties that might contain text\r\n      if (item.content) {\r\n        return item.content;\r\n      }\r\n\r\n      if (item.text) {\r\n        return item.text;\r\n      }\r\n\r\n      if (item.value) {\r\n        return item.value;\r\n      }\r\n\r\n      // If it's an object but doesn't have expected properties, try to get a meaningful string\r\n      try {\r\n        // Try to convert to JSON string if it's a complex object\r\n        const jsonStr = JSON.stringify(item);\r\n        // If it looks like a complex object, return a simplified version\r\n        if (jsonStr.startsWith('{') && jsonStr.length > 2) {\r\n          return item.toString ? item.toString() : 'List item';\r\n        }\r\n        return jsonStr;\r\n      } catch (e) {\r\n        return 'List item';\r\n      }\r\n    }\r\n\r\n    // For any other type, convert to string\r\n    return String(item);\r\n  }\r\n\r\n  formatDate(dateString: any): string {\r\n    if (!dateString) return '';\r\n\r\n    // Handle different date formats\r\n    let date: Date;\r\n    if (typeof dateString === 'string') {\r\n      date = new Date(dateString);\r\n    } else if (dateString && typeof dateString === 'object' && dateString.toString) {\r\n      date = new Date(dateString.toString());\r\n    } else {\r\n      return 'Unknown date';\r\n    }\r\n\r\n    // Check if date is valid\r\n    if (isNaN(date.getTime())) {\r\n      return 'Invalid date';\r\n    }\r\n\r\n    const now = new Date();\r\n    const yesterday = new Date(now);\r\n    yesterday.setDate(yesterday.getDate() - 1);\r\n\r\n    // Check if date is today\r\n    if (date.toDateString() === now.toDateString()) {\r\n      return 'Today, ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\r\n    }\r\n\r\n    // Check if date is yesterday\r\n    if (date.toDateString() === yesterday.toDateString()) {\r\n      return 'Yesterday, ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\r\n    }\r\n\r\n    // Otherwise return formatted date\r\n    return date.toLocaleDateString('en-US', {\r\n      year: 'numeric',\r\n      month: 'short',\r\n      day: 'numeric'\r\n    });\r\n  }\r\n}\r\n"], "mappings": ";;;AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,MAAM,EAAEC,UAAU,EAAEC,YAAY,QAAQ,iBAAiB;AAClE,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAAiBC,kBAAkB,QAAQ,iDAAiD;AAC5F,SAASC,cAAc,QAAQ,cAAc;AAStC,IAAMC,eAAe,GAArB,MAAMA,eAAe;EAQ1BC,YAAoBC,cAAkC,EAAUC,MAAc;IAA1D,KAAAD,cAAc,GAAdA,cAAc;IAA8B,KAAAC,MAAM,GAANA,MAAM;IAPtE,KAAAC,MAAM,GAAa,EAAE;IACrB,KAAAC,cAAc,GAAa,EAAE;IAC7B,KAAAC,UAAU,GAAW,EAAE;IACvB,KAAAC,OAAO,GAAY,IAAI;IACvB,KAAAC,eAAe,GAAkB,IAAI;IACrC,KAAAC,cAAc,GAAkB,IAAI;EAE8C;EAElFC,QAAQA,CAAA;IACN,IAAI,CAACC,UAAU,EAAE;EACnB;EAEAA,UAAUA,CAAA;IACR,IAAI,CAACJ,OAAO,GAAG,IAAI;IACnB,IAAI,CAACL,cAAc,CAACU,MAAM,EAAE,CAACC,SAAS,CAAEC,GAAQ,IAAI;MAClD,IAAI,CAACV,MAAM,GAAGU,GAAG;MACjB,IAAI,CAACT,cAAc,GAAG,CAAC,GAAG,IAAI,CAACD,MAAM,CAAC;MACtC,IAAI,CAACG,OAAO,GAAG,KAAK;MACpBQ,OAAO,CAACC,GAAG,CAACF,GAAG,CAAC;IAClB,CAAC,CAAC;EACJ;EAEAG,YAAYA,CAACC,EAAU;IACrB,IAAI,CAAChB,cAAc,CAACiB,MAAM,CAACD,EAAE,CAAC,CAACL,SAAS,CAAC;MACvCO,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAChB,MAAM,GAAG,IAAI,CAACA,MAAM,CAACiB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACJ,EAAE,KAAKA,EAAE,CAAC;QAClD,IAAI,CAACb,cAAc,GAAG,IAAI,CAACA,cAAc,CAACgB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACJ,EAAE,KAAKA,EAAE,CAAC;QAClE,IAAI,CAACV,eAAe,GAAG,IAAI;MAC7B,CAAC;MACDe,KAAK,EAAGC,GAAG,IAAI;QACbT,OAAO,CAACQ,KAAK,CAAC,wBAAwB,EAAEC,GAAG,CAAC;QAC5C,IAAI,CAAChB,eAAe,GAAG,IAAI;MAC7B;KACD,CAAC;EACJ;EAEAiB,aAAaA,CAACP,EAAU;IACtB,IAAI,CAACV,eAAe,GAAGU,EAAE;EAC3B;EAEAQ,YAAYA,CAAA;IACV,IAAI,CAAClB,eAAe,GAAG,IAAI;EAC7B;EAEAmB,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAACrB,UAAU,CAACsB,IAAI,EAAE,EAAE;MAC3B,IAAI,CAACvB,cAAc,GAAG,CAAC,GAAG,IAAI,CAACD,MAAM,CAAC;MACtC;;IAGF,MAAMyB,IAAI,GAAG,IAAI,CAACvB,UAAU,CAACwB,WAAW,EAAE,CAACF,IAAI,EAAE;IACjD,IAAI,CAACvB,cAAc,GAAG,IAAI,CAACD,MAAM,CAACiB,MAAM,CAACjB,MAAM,IAAG;MAChD;MACA,MAAM2B,OAAO,GAAG,IAAI,CAACC,iBAAiB,CAAC5B,MAAM,CAAC;MAC9C;MACA,MAAM6B,KAAK,GAAG7B,MAAM,CAAC6B,KAAK,EAAEH,WAAW,EAAE,IAAI,EAAE;MAE/C,OAAOC,OAAO,CAACD,WAAW,EAAE,CAACI,QAAQ,CAACL,IAAI,CAAC,IAAII,KAAK,CAACC,QAAQ,CAACL,IAAI,CAAC;IACrE,CAAC,CAAC;EACJ;EAEAM,SAASA,CAAA;IACP,IAAI,CAAChC,MAAM,CAACiC,QAAQ,CAAC,CAAC,sBAAsB,CAAC,CAAC;EAChD;EAGAJ,iBAAiBA,CAAC5B,MAAc;IAC9B,IAAI,CAACA,MAAM,CAAC2B,OAAO,EAAE,OAAO,EAAE;IAE9B,IAAI;MACF;MACA,IAAI3B,MAAM,CAAC2B,OAAO,CAACM,UAAU,CAAC,GAAG,CAAC,EAAE;QAClC,MAAMC,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACpC,MAAM,CAAC2B,OAAO,CAAC;QACzC,IAAIO,MAAM,CAACG,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACL,MAAM,CAACG,MAAM,CAAC,EAAE;UACjD;UACA,OAAOH,MAAM,CAACG,MAAM,CAACG,GAAG,CAAEC,KAAU,IAAI;YACtC;YACA,QAAQA,KAAK,CAACC,IAAI;cAChB,KAAK,WAAW;cAChB,KAAK,QAAQ;gBACX,OAAOD,KAAK,CAACE,IAAI,EAAEC,IAAI,IAAI,EAAE;cAE/B,KAAK,MAAM;gBACT,IAAIH,KAAK,CAACE,IAAI,EAAEE,KAAK,IAAIP,KAAK,CAACC,OAAO,CAACE,KAAK,CAACE,IAAI,CAACE,KAAK,CAAC,EAAE;kBACxD;kBACA,OAAOJ,KAAK,CAACE,IAAI,CAACE,KAAK,CAACL,GAAG,CAAEM,IAAS,IAAK,IAAI,CAACC,WAAW,CAACD,IAAI,CAAC,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC;;gBAE/E,OAAO,EAAE;cAEX,KAAK,WAAW;gBACd,IAAIP,KAAK,CAACE,IAAI,EAAEE,KAAK,IAAIP,KAAK,CAACC,OAAO,CAACE,KAAK,CAACE,IAAI,CAACE,KAAK,CAAC,EAAE;kBACxD;kBACA,OAAOJ,KAAK,CAACE,IAAI,CAACE,KAAK,CAACL,GAAG,CAAEM,IAAS,IAAK,IAAI,CAACC,WAAW,CAACD,IAAI,CAAC,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC;;gBAE/E,OAAO,EAAE;cAEX,KAAK,OAAO;gBACV,OAAOP,KAAK,CAACE,IAAI,EAAEC,IAAI,IAAI,EAAE;cAE/B,KAAK,MAAM;gBACT,OAAO,cAAc;cAEvB,KAAK,OAAO;gBACV,OAAO,SAAS;cAElB;gBACE,OAAOH,KAAK,CAACE,IAAI,EAAEC,IAAI,IAAI,EAAE;;UAEnC,CAAC,CAAC,CAAC3B,MAAM,CAAE2B,IAAY,IAAKA,IAAI,CAACpB,IAAI,EAAE,KAAK,EAAE,CAAC,CAACwB,IAAI,CAAC,GAAG,CAAC;;;MAG7D;MACA,OAAOhD,MAAM,CAAC2B,OAAO;KACtB,CAAC,OAAOsB,CAAC,EAAE;MACV;MACA,OAAOjD,MAAM,CAAC2B,OAAO;;EAEzB;EAEA;EACAuB,mBAAmBA,CAACvB,OAAe;IACjC,IAAI,CAACA,OAAO,EAAE,OAAO,EAAE;IAEvB,IAAI;MACF,MAAMwB,aAAa,GAAGhB,IAAI,CAACC,KAAK,CAACT,OAAO,CAAC;MAEzC,IAAI,CAACwB,aAAa,CAACd,MAAM,IAAI,CAACC,KAAK,CAACC,OAAO,CAACY,aAAa,CAACd,MAAM,CAAC,EAAE;QACjE,OAAOV,OAAO,CAAC,CAAC;;MAGlB,IAAIyB,IAAI,GAAG,EAAE;MAEbD,aAAa,CAACd,MAAM,CAACgB,OAAO,CAAEZ,KAAU,IAAI;QAC1C,QAAQA,KAAK,CAACC,IAAI;UAChB,KAAK,QAAQ;YACX,MAAMY,KAAK,GAAGb,KAAK,CAACE,IAAI,CAACW,KAAK,IAAI,CAAC;YACnCF,IAAI,IAAI,KAAKE,KAAK,4DAA4Db,KAAK,CAACE,IAAI,CAACC,IAAI,MAAMU,KAAK,GAAG;YAC3G;UAEF,KAAK,WAAW;YACdF,IAAI,IAAI,2CAA2CX,KAAK,CAACE,IAAI,CAACC,IAAI,MAAM;YACxE;UAEF,KAAK,MAAM;YACT,MAAMW,OAAO,GAAGd,KAAK,CAACE,IAAI,CAACa,KAAK,KAAK,SAAS,GAAG,IAAI,GAAG,IAAI;YAC5DJ,IAAI,IAAI,IAAIG,OAAO,gBAAgBd,KAAK,CAACE,IAAI,CAACa,KAAK,KAAK,SAAS,GAAG,SAAS,GAAG,MAAM,sCAAsC;YAC5H,IAAIf,KAAK,CAACE,IAAI,CAACE,KAAK,IAAIP,KAAK,CAACC,OAAO,CAACE,KAAK,CAACE,IAAI,CAACE,KAAK,CAAC,EAAE;cACvDJ,KAAK,CAACE,IAAI,CAACE,KAAK,CAACQ,OAAO,CAAEP,IAAS,IAAI;gBACrC;gBACA,MAAMW,QAAQ,GAAG,IAAI,CAACV,WAAW,CAACD,IAAI,CAAC;gBACvCM,IAAI,IAAI,oBAAoBK,QAAQ,OAAO;cAC7C,CAAC,CAAC;;YAEJL,IAAI,IAAI,KAAKG,OAAO,GAAG;YACvB;UAEF,KAAK,WAAW;YACdH,IAAI,IAAI,oBAAoB;YAC5B,IAAIX,KAAK,CAACE,IAAI,CAACE,KAAK,IAAIP,KAAK,CAACC,OAAO,CAACE,KAAK,CAACE,IAAI,CAACE,KAAK,CAAC,EAAE;cACvDJ,KAAK,CAACE,IAAI,CAACE,KAAK,CAACQ,OAAO,CAAEP,IAAS,IAAI;gBACrC;gBACA,MAAMY,SAAS,GAAGZ,IAAI,CAACa,OAAO,KAAK,IAAI;gBACvC;gBACA,MAAMF,QAAQ,GAAG,IAAI,CAACV,WAAW,CAACD,IAAI,CAAC;gBAEvCM,IAAI,IAAI;+CACuBM,SAAS,GAAG,SAAS,GAAG,EAAE;0DACfA,SAAS,GAAG,SAAS,GAAG,EAAE;wBAC5DA,SAAS,GAAG,kDAAkD,GAAG,EAAE;;wDAEnCD,QAAQ;;iBAE/C;cACH,CAAC,CAAC;;YAEJL,IAAI,IAAI,QAAQ;YAChB;UAEF,KAAK,OAAO;YACVA,IAAI,IAAI;;qBAECX,KAAK,CAACE,IAAI,CAACC,IAAI;kBAClBH,KAAK,CAACE,IAAI,CAACiB,OAAO,GAAG,qEAAqEnB,KAAK,CAACE,IAAI,CAACiB,OAAO,SAAS,GAAG,EAAE;;aAE/H;YACD;UAEF,KAAK,WAAW;YACdR,IAAI,IAAI,4DAA4D;YACpE;UAEF,KAAK,MAAM;YACTA,IAAI,IAAI;;0EAEsDX,KAAK,CAACE,IAAI,CAACkB,IAAI;;aAE5E;YACD;UAEF,KAAK,OAAO;YACVT,IAAI,IAAI;;wBAEI;YAEZX,KAAK,CAACE,IAAI,CAAChB,OAAO,CAAC0B,OAAO,CAAES,GAAa,IAAI;cAC3CV,IAAI,IAAI,MAAM;cACdU,GAAG,CAACT,OAAO,CAAEU,IAAY,IAAI;gBAC3BX,IAAI,IAAI,kFAAkFW,IAAI,OAAO;cACvG,CAAC,CAAC;cACFX,IAAI,IAAI,OAAO;YACjB,CAAC,CAAC;YAEFA,IAAI,IAAI,wBAAwB;YAChC;UAEF;YACE,IAAIX,KAAK,CAACE,IAAI,IAAIF,KAAK,CAACE,IAAI,CAACC,IAAI,EAAE;cACjCQ,IAAI,IAAI,2CAA2CX,KAAK,CAACE,IAAI,CAACC,IAAI,MAAM;;;MAGhF,CAAC,CAAC;MAEF,OAAOQ,IAAI;KACZ,CAAC,OAAOH,CAAC,EAAE;MACVtC,OAAO,CAACQ,KAAK,CAAC,oCAAoC,EAAE8B,CAAC,CAAC;MACtD,OAAOtB,OAAO,CAAC,CAAC;;EAEpB;EAEA;EACAqC,YAAYA,CAAChE,MAAc;IACzB,IAAI,CAACK,cAAc,GAAGL,MAAM;IAE5B;IACAW,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEZ,MAAM,CAAC;EACzC;EAEA;EACAiE,iBAAiBA,CAAA;IACf,IAAI,CAAC5D,cAAc,GAAG,IAAI;EAC5B;EAEA;EACA0C,WAAWA,CAACD,IAAS;IACnB,IAAIA,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAKoB,SAAS,EAAE;MACvC,OAAO,EAAE;;IAGX;IACA,IAAI,OAAOpB,IAAI,KAAK,QAAQ,EAAE;MAC5B,OAAOA,IAAI;;IAGb;IACA,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MAC5B;MACA,IAAIA,IAAI,CAACnB,OAAO,EAAE;QAChB,OAAOmB,IAAI,CAACnB,OAAO;;MAGrB,IAAImB,IAAI,CAACF,IAAI,EAAE;QACb,OAAOE,IAAI,CAACF,IAAI;;MAGlB,IAAIE,IAAI,CAACqB,KAAK,EAAE;QACd,OAAOrB,IAAI,CAACqB,KAAK;;MAGnB;MACA,IAAI;QACF;QACA,MAAMC,OAAO,GAAGjC,IAAI,CAACkC,SAAS,CAACvB,IAAI,CAAC;QACpC;QACA,IAAIsB,OAAO,CAACnC,UAAU,CAAC,GAAG,CAAC,IAAImC,OAAO,CAACE,MAAM,GAAG,CAAC,EAAE;UACjD,OAAOxB,IAAI,CAACyB,QAAQ,GAAGzB,IAAI,CAACyB,QAAQ,EAAE,GAAG,WAAW;;QAEtD,OAAOH,OAAO;OACf,CAAC,OAAOnB,CAAC,EAAE;QACV,OAAO,WAAW;;;IAItB;IACA,OAAOuB,MAAM,CAAC1B,IAAI,CAAC;EACrB;EAEA2B,UAAUA,CAACC,UAAe;IACxB,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;IAE1B;IACA,IAAIC,IAAU;IACd,IAAI,OAAOD,UAAU,KAAK,QAAQ,EAAE;MAClCC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;KAC5B,MAAM,IAAIA,UAAU,IAAI,OAAOA,UAAU,KAAK,QAAQ,IAAIA,UAAU,CAACH,QAAQ,EAAE;MAC9EI,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAACH,QAAQ,EAAE,CAAC;KACvC,MAAM;MACL,OAAO,cAAc;;IAGvB;IACA,IAAIM,KAAK,CAACF,IAAI,CAACG,OAAO,EAAE,CAAC,EAAE;MACzB,OAAO,cAAc;;IAGvB,MAAMC,GAAG,GAAG,IAAIH,IAAI,EAAE;IACtB,MAAMI,SAAS,GAAG,IAAIJ,IAAI,CAACG,GAAG,CAAC;IAC/BC,SAAS,CAACC,OAAO,CAACD,SAAS,CAACE,OAAO,EAAE,GAAG,CAAC,CAAC;IAE1C;IACA,IAAIP,IAAI,CAACQ,YAAY,EAAE,KAAKJ,GAAG,CAACI,YAAY,EAAE,EAAE;MAC9C,OAAO,SAAS,GAAGR,IAAI,CAACS,kBAAkB,CAAC,EAAE,EAAE;QAAEC,IAAI,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAS,CAAE,CAAC;;IAGxF;IACA,IAAIX,IAAI,CAACQ,YAAY,EAAE,KAAKH,SAAS,CAACG,YAAY,EAAE,EAAE;MACpD,OAAO,aAAa,GAAGR,IAAI,CAACS,kBAAkB,CAAC,EAAE,EAAE;QAAEC,IAAI,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAS,CAAE,CAAC;;IAG5F;IACA,OAAOX,IAAI,CAACY,kBAAkB,CAAC,OAAO,EAAE;MACtCC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE;KACN,CAAC;EACJ;;;;;;;;;AAtUW9F,eAAe,GAAA+F,UAAA,EAP3BvG,SAAS,CAAC;EACTwG,QAAQ,EAAE,YAAY;EACtBC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACzG,YAAY,EAAEG,YAAY,EAAEC,WAAW,EAACF,UAAU,EAACI,cAAc,CAAC;EAC5EoG,QAAA,EAAAC,oBAAsC;;CAEvC,CAAC,C,EACWpG,eAAe,CAuU3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}