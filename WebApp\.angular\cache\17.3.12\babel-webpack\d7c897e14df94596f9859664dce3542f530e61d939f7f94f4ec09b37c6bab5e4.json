{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./source-references.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./source-references.component.css?ngResource\";\nimport { Component, Input, Output, EventEmitter, inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterLink } from '@angular/router';\nimport { ThemeService } from '../../../../shared/services/theam.service';\nlet SourceReferencesComponent = class SourceReferencesComponent {\n  constructor() {\n    // Inject ThemeService\n    this.themeService = inject(ThemeService);\n    this.showSidebar = false;\n    this.searchResults = [];\n    this.currentSourceName = '';\n    this.closeSidebar = new EventEmitter();\n  }\n  /**\n   * Closes the sidebar\n   */\n  onCloseSidebar() {\n    this.closeSidebar.emit();\n  }\n  /**\n   * Opens a search result URL in a new tab\n   * @param url The URL to open\n   */\n  openSearchResult(url) {\n    if (url && url !== '#') {\n      window.open(url, '_blank');\n    }\n  }\n  static {\n    this.propDecorators = {\n      showSidebar: [{\n        type: Input\n      }],\n      searchResults: [{\n        type: Input\n      }],\n      currentSourceName: [{\n        type: Input\n      }],\n      closeSidebar: [{\n        type: Output\n      }]\n    };\n  }\n};\nSourceReferencesComponent = __decorate([Component({\n  selector: 'app-source-references',\n  standalone: true,\n  imports: [CommonModule, RouterLink],\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SourceReferencesComponent);\nexport { SourceReferencesComponent };", "map": {"version": 3, "names": ["Component", "Input", "Output", "EventEmitter", "inject", "CommonModule", "RouterLink", "ThemeService", "SourceReferencesComponent", "constructor", "themeService", "showSidebar", "searchResults", "currentSourceName", "closeSidebar", "onCloseSidebar", "emit", "openSearchResult", "url", "window", "open", "__decorate", "selector", "standalone", "imports", "template", "__NG_CLI_RESOURCE__0"], "sources": ["C:\\Users\\<USER>\\source\\ai-hub\\WebApp\\src\\app\\components\\@rightSideComponents\\source-references\\source-references.component.ts"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterLink } from '@angular/router';\nimport { ThemeService } from '../../../../shared/services/theam.service';\n\n@Component({\n  selector: 'app-source-references',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterLink\n  ],\n  templateUrl: './source-references.component.html',\n  styleUrls: ['./source-references.component.css']\n})\nexport class SourceReferencesComponent {\n  // Inject ThemeService\n  themeService = inject(ThemeService);\n\n  // Input properties\n  @Input() showSidebar: boolean = false;\n  @Input() searchResults: any[] = [];\n  @Input() currentSourceName: string = '';\n\n  // Output events\n  @Output() closeSidebar = new EventEmitter<void>();\n\n  /**\n   * Closes the sidebar\n   */\n  onCloseSidebar(): void {\n    this.closeSidebar.emit();\n  }\n\n  /**\n   * Opens a search result URL in a new tab\n   * @param url The URL to open\n   */\n  openSearchResult(url: string): void {\n    if (url && url !== '#') {\n      window.open(url, '_blank');\n    }\n  }\n}\n"], "mappings": ";;;AAAA,SAASA,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,YAAY,EAAEC,MAAM,QAAQ,eAAe;AAC9E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,SAASC,YAAY,QAAQ,2CAA2C;AAYjE,IAAMC,yBAAyB,GAA/B,MAAMA,yBAAyB;EAA/BC,YAAA;IACL;IACA,KAAAC,YAAY,GAAGN,MAAM,CAACG,YAAY,CAAC;IAG1B,KAAAI,WAAW,GAAY,KAAK;IAC5B,KAAAC,aAAa,GAAU,EAAE;IACzB,KAAAC,iBAAiB,GAAW,EAAE;IAG7B,KAAAC,YAAY,GAAG,IAAIX,YAAY,EAAQ;EAkBnD;EAhBE;;;EAGAY,cAAcA,CAAA;IACZ,IAAI,CAACD,YAAY,CAACE,IAAI,EAAE;EAC1B;EAEA;;;;EAIAC,gBAAgBA,CAACC,GAAW;IAC1B,IAAIA,GAAG,IAAIA,GAAG,KAAK,GAAG,EAAE;MACtBC,MAAM,CAACC,IAAI,CAACF,GAAG,EAAE,QAAQ,CAAC;;EAE9B;;;;cAtBCjB;MAAK;;cACLA;MAAK;;cACLA;MAAK;;cAGLC;MAAM;;;;AAVIM,yBAAyB,GAAAa,UAAA,EAVrCrB,SAAS,CAAC;EACTsB,QAAQ,EAAE,uBAAuB;EACjCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPnB,YAAY,EACZC,UAAU,CACX;EACDmB,QAAA,EAAAC,oBAAiD;;CAElD,CAAC,C,EACWlB,yBAAyB,CA4BrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}