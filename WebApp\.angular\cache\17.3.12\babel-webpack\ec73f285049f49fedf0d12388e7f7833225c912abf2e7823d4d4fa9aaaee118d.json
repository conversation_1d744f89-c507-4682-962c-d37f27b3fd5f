{"ast": null, "code": "// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\n// This will be treated as a bit flag in the future, so we keep it using power-of-two values.\n/** Specifies a specific HTTP transport type. */\nexport var HttpTransportType;\n(function (HttpTransportType) {\n  /** Specifies no transport preference. */\n  HttpTransportType[HttpTransportType[\"None\"] = 0] = \"None\";\n  /** Specifies the WebSockets transport. */\n  HttpTransportType[HttpTransportType[\"WebSockets\"] = 1] = \"WebSockets\";\n  /** Specifies the Server-Sent Events transport. */\n  HttpTransportType[HttpTransportType[\"ServerSentEvents\"] = 2] = \"ServerSentEvents\";\n  /** Specifies the Long Polling transport. */\n  HttpTransportType[HttpTransportType[\"LongPolling\"] = 4] = \"LongPolling\";\n})(HttpTransportType || (HttpTransportType = {}));\n/** Specifies the transfer format for a connection. */\nexport var TransferFormat;\n(function (TransferFormat) {\n  /** Specifies that only text data will be transmitted over the connection. */\n  TransferFormat[TransferFormat[\"Text\"] = 1] = \"Text\";\n  /** Specifies that binary data will be transmitted over the connection. */\n  TransferFormat[TransferFormat[\"Binary\"] = 2] = \"Binary\";\n})(TransferFormat || (TransferFormat = {}));", "map": {"version": 3, "names": ["HttpTransportType", "TransferFormat"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/@microsoft/signalr/dist/esm/ITransport.js"], "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n// This will be treated as a bit flag in the future, so we keep it using power-of-two values.\r\n/** Specifies a specific HTTP transport type. */\r\nexport var HttpTransportType;\r\n(function (HttpTransportType) {\r\n    /** Specifies no transport preference. */\r\n    HttpTransportType[HttpTransportType[\"None\"] = 0] = \"None\";\r\n    /** Specifies the WebSockets transport. */\r\n    HttpTransportType[HttpTransportType[\"WebSockets\"] = 1] = \"WebSockets\";\r\n    /** Specifies the Server-Sent Events transport. */\r\n    HttpTransportType[HttpTransportType[\"ServerSentEvents\"] = 2] = \"ServerSentEvents\";\r\n    /** Specifies the Long Polling transport. */\r\n    HttpTransportType[HttpTransportType[\"LongPolling\"] = 4] = \"LongPolling\";\r\n})(HttpTransportType || (HttpTransportType = {}));\r\n/** Specifies the transfer format for a connection. */\r\nexport var TransferFormat;\r\n(function (TransferFormat) {\r\n    /** Specifies that only text data will be transmitted over the connection. */\r\n    TransferFormat[TransferFormat[\"Text\"] = 1] = \"Text\";\r\n    /** Specifies that binary data will be transmitted over the connection. */\r\n    TransferFormat[TransferFormat[\"Binary\"] = 2] = \"Binary\";\r\n})(TransferFormat || (TransferFormat = {}));\r\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,IAAIA,iBAAiB;AAC5B,CAAC,UAAUA,iBAAiB,EAAE;EAC1B;EACAA,iBAAiB,CAACA,iBAAiB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EACzD;EACAA,iBAAiB,CAACA,iBAAiB,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY;EACrE;EACAA,iBAAiB,CAACA,iBAAiB,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,GAAG,kBAAkB;EACjF;EACAA,iBAAiB,CAACA,iBAAiB,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,GAAG,aAAa;AAC3E,CAAC,EAAEA,iBAAiB,KAAKA,iBAAiB,GAAG,CAAC,CAAC,CAAC,CAAC;AACjD;AACA,OAAO,IAAIC,cAAc;AACzB,CAAC,UAAUA,cAAc,EAAE;EACvB;EACAA,cAAc,CAACA,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EACnD;EACAA,cAAc,CAACA,cAAc,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;AAC3D,CAAC,EAAEA,cAAc,KAAKA,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}