{"ast": null, "code": "(function () {\n  var r;\n  \"use strict\";\n  try {\n    if (typeof document < \"u\") {\n      var o = document.createElement(\"style\");\n      o.nonce = (r = document.head.querySelector(\"meta[property=csp-nonce]\")) == null ? void 0 : r.content, o.appendChild(document.createTextNode('.tc-wrap{--color-background:#f9f9fb;--color-text-secondary:#7b7e89;--color-border:#e8e8eb;--cell-size:34px;--toolbox-icon-size:18px;--toolbox-padding:6px;--toolbox-aiming-field-size:calc(var(--toolbox-icon-size) + var(--toolbox-padding)*2);border-left:0;position:relative;height:100%;width:100%;margin-top:var(--toolbox-icon-size);box-sizing:border-box;display:grid;grid-template-columns:calc(100% - var(--cell-size)) var(--cell-size);z-index:0}.tc-wrap--readonly{grid-template-columns:100% var(--cell-size)}.tc-wrap svg{vertical-align:top}@media print{.tc-wrap{border-left-color:var(--color-border);border-left-style:solid;border-left-width:1px;grid-template-columns:100% var(--cell-size)}}@media print{.tc-wrap .tc-row:after{display:none}}.tc-table{position:relative;width:100%;height:100%;display:grid;font-size:14px;border-top:1px solid var(--color-border);line-height:1.4}.tc-table:after{width:calc(var(--cell-size));height:100%;left:calc(var(--cell-size)*-1);top:0}.tc-table:after,.tc-table:before{position:absolute;content:\"\"}.tc-table:before{width:100%;height:var(--toolbox-aiming-field-size);top:calc(var(--toolbox-aiming-field-size)*-1);left:0}.tc-table--heading .tc-row:first-child{font-weight:600;border-bottom:2px solid var(--color-border)}.tc-table--heading .tc-row:first-child [contenteditable]:empty:before{content:attr(heading);color:var(--color-text-secondary)}.tc-table--heading .tc-row:first-child:after{bottom:-2px;border-bottom:2px solid var(--color-border)}.tc-add-column,.tc-add-row{display:flex;color:var(--color-text-secondary)}@media print{.tc-add{display:none}}.tc-add-column{padding:4px 0;justify-content:center;border-top:1px solid var(--color-border)}.tc-add-column--disabled{visibility:hidden}@media print{.tc-add-column{display:none}}.tc-add-row{height:var(--cell-size);align-items:center;padding-left:4px;position:relative}.tc-add-row--disabled{display:none}.tc-add-row:before{content:\"\";position:absolute;right:calc(var(--cell-size)*-1);width:var(--cell-size);height:100%}@media print{.tc-add-row{display:none}}.tc-add-column,.tc-add-row{transition:0s;cursor:pointer;will-change:background-color}.tc-add-column:hover,.tc-add-row:hover{transition:background-color .1s ease;background-color:var(--color-background)}.tc-add-row{margin-top:1px}.tc-add-row:hover:before{transition:.1s;background-color:var(--color-background)}.tc-row{display:grid;grid-template-columns:repeat(auto-fit,minmax(10px,1fr));position:relative;border-bottom:1px solid var(--color-border)}.tc-row:after{content:\"\";pointer-events:none;position:absolute;width:var(--cell-size);height:100%;bottom:-1px;right:calc(var(--cell-size)*-1);border-bottom:1px solid var(--color-border)}.tc-row--selected{background:var(--color-background)}.tc-row--selected:after{background:var(--color-background)}.tc-cell{border-right:1px solid var(--color-border);padding:6px 12px;overflow:hidden;outline:none;line-break:normal}.tc-cell--selected{background:var(--color-background)}.tc-wrap--readonly .tc-row:after{display:none}.tc-toolbox{--toolbox-padding:6px;--popover-margin:30px;--toggler-click-zone-size:30px;--toggler-dots-color:#7b7e89;--toggler-dots-color-hovered:#1d202b;position:absolute;cursor:pointer;z-index:1;opacity:0;transition:opacity .1s;will-change:left,opacity}.tc-toolbox--column{top:calc(var(--toggler-click-zone-size)*-1);transform:translate(calc(var(--toggler-click-zone-size)*-1/2));will-change:left,opacity}.tc-toolbox--row{left:calc(var(--popover-margin)*-1);transform:translateY(calc(var(--toggler-click-zone-size)*-1/2));margin-top:-1px;will-change:top,opacity}.tc-toolbox--showed{opacity:1}.tc-toolbox .tc-popover{position:absolute;top:0;left:var(--popover-margin)}.tc-toolbox__toggler{display:flex;align-items:center;justify-content:center;width:var(--toggler-click-zone-size);height:var(--toggler-click-zone-size);color:var(--toggler-dots-color);opacity:0;transition:opacity .15s ease;will-change:opacity}.tc-toolbox__toggler:hover{color:var(--toggler-dots-color-hovered)}.tc-toolbox__toggler svg{fill:currentColor}.tc-wrap:hover .tc-toolbox__toggler{opacity:1}.tc-settings .cdx-settings-button{width:50%;margin:0}.tc-popover{--color-border:#eaeaea;--color-background:#fff;--color-background-hover:rgba(232,232,235,.49);--color-background-confirm:#e24a4a;--color-background-confirm-hover:#d54040;--color-text-confirm:#fff;background:var(--color-background);border:1px solid var(--color-border);box-shadow:0 3px 15px -3px #0d142121;border-radius:6px;padding:6px;display:none;will-change:opacity,transform}.tc-popover--opened{display:block;animation:menuShowing .1s cubic-bezier(.215,.61,.355,1) forwards}.tc-popover__item{display:flex;align-items:center;padding:2px 14px 2px 2px;border-radius:5px;cursor:pointer;white-space:nowrap;-webkit-user-select:none;-moz-user-select:none;user-select:none}.tc-popover__item:hover{background:var(--color-background-hover)}.tc-popover__item:not(:last-of-type){margin-bottom:2px}.tc-popover__item-icon{display:inline-flex;width:26px;height:26px;align-items:center;justify-content:center;background:var(--color-background);border-radius:5px;border:1px solid var(--color-border);margin-right:8px}.tc-popover__item-label{line-height:22px;font-size:14px;font-weight:500}.tc-popover__item--confirm{background:var(--color-background-confirm);color:var(--color-text-confirm)}.tc-popover__item--confirm:hover{background-color:var(--color-background-confirm-hover)}.tc-popover__item--confirm .tc-popover__item-icon{background:var(--color-background-confirm);border-color:#0000001a}.tc-popover__item--confirm .tc-popover__item-icon svg{transition:transform .2s ease-in;transform:rotate(90deg) scale(1.2)}.tc-popover__item--hidden{display:none}@keyframes menuShowing{0%{opacity:0;transform:translateY(-8px) scale(.9)}70%{opacity:1;transform:translateY(2px)}to{transform:translateY(0)}}')), document.head.appendChild(o);\n    }\n  } catch (e) {\n    console.error(\"vite-plugin-css-injected-by-js\", e);\n  }\n})();\nfunction c(d, t, e = {}) {\n  const o = document.createElement(d);\n  Array.isArray(t) ? o.classList.add(...t) : t && o.classList.add(t);\n  for (const i in e) Object.prototype.hasOwnProperty.call(e, i) && (o[i] = e[i]);\n  return o;\n}\nfunction f(d) {\n  const t = d.getBoundingClientRect();\n  return {\n    y1: Math.floor(t.top + window.pageYOffset),\n    x1: Math.floor(t.left + window.pageXOffset),\n    x2: Math.floor(t.right + window.pageXOffset),\n    y2: Math.floor(t.bottom + window.pageYOffset)\n  };\n}\nfunction g(d, t) {\n  const e = f(d),\n    o = f(t);\n  return {\n    fromTopBorder: o.y1 - e.y1,\n    fromLeftBorder: o.x1 - e.x1,\n    fromRightBorder: e.x2 - o.x2,\n    fromBottomBorder: e.y2 - o.y2\n  };\n}\nfunction k(d, t) {\n  const e = d.getBoundingClientRect(),\n    {\n      width: o,\n      height: i,\n      x: n,\n      y: r\n    } = e,\n    {\n      clientX: h,\n      clientY: l\n    } = t;\n  return {\n    width: o,\n    height: i,\n    x: h - n,\n    y: l - r\n  };\n}\nfunction m(d, t) {\n  return t.parentNode.insertBefore(d, t);\n}\nfunction C(d, t = !0) {\n  const e = document.createRange(),\n    o = window.getSelection();\n  e.selectNodeContents(d), e.collapse(t), o.removeAllRanges(), o.addRange(e);\n}\nclass a {\n  /**\n   * @param {object} options - constructor options\n   * @param {PopoverItem[]} options.items - constructor options\n   */\n  constructor({\n    items: t\n  }) {\n    this.items = t, this.wrapper = void 0, this.itemEls = [];\n  }\n  /**\n   * Set of CSS classnames used in popover\n   *\n   * @returns {object}\n   */\n  static get CSS() {\n    return {\n      popover: \"tc-popover\",\n      popoverOpened: \"tc-popover--opened\",\n      item: \"tc-popover__item\",\n      itemHidden: \"tc-popover__item--hidden\",\n      itemConfirmState: \"tc-popover__item--confirm\",\n      itemIcon: \"tc-popover__item-icon\",\n      itemLabel: \"tc-popover__item-label\"\n    };\n  }\n  /**\n   * Returns the popover element\n   *\n   * @returns {Element}\n   */\n  render() {\n    return this.wrapper = c(\"div\", a.CSS.popover), this.items.forEach((t, e) => {\n      const o = c(\"div\", a.CSS.item),\n        i = c(\"div\", a.CSS.itemIcon, {\n          innerHTML: t.icon\n        }),\n        n = c(\"div\", a.CSS.itemLabel, {\n          textContent: t.label\n        });\n      o.dataset.index = e, o.appendChild(i), o.appendChild(n), this.wrapper.appendChild(o), this.itemEls.push(o);\n    }), this.wrapper.addEventListener(\"click\", t => {\n      this.popoverClicked(t);\n    }), this.wrapper;\n  }\n  /**\n   * Popover wrapper click listener\n   * Used to delegate clicks in items\n   *\n   * @returns {void}\n   */\n  popoverClicked(t) {\n    const e = t.target.closest(`.${a.CSS.item}`);\n    if (!e) return;\n    const o = e.dataset.index,\n      i = this.items[o];\n    if (i.confirmationRequired && !this.hasConfirmationState(e)) {\n      this.setConfirmationState(e);\n      return;\n    }\n    i.onClick();\n  }\n  /**\n   * Enable the confirmation state on passed item\n   *\n   * @returns {void}\n   */\n  setConfirmationState(t) {\n    t.classList.add(a.CSS.itemConfirmState);\n  }\n  /**\n   * Disable the confirmation state on passed item\n   *\n   * @returns {void}\n   */\n  clearConfirmationState(t) {\n    t.classList.remove(a.CSS.itemConfirmState);\n  }\n  /**\n   * Check if passed item has the confirmation state\n   *\n   * @returns {boolean}\n   */\n  hasConfirmationState(t) {\n    return t.classList.contains(a.CSS.itemConfirmState);\n  }\n  /**\n   * Return an opening state\n   *\n   * @returns {boolean}\n   */\n  get opened() {\n    return this.wrapper.classList.contains(a.CSS.popoverOpened);\n  }\n  /**\n   * Opens the popover\n   *\n   * @returns {void}\n   */\n  open() {\n    this.items.forEach((t, e) => {\n      typeof t.hideIf == \"function\" && this.itemEls[e].classList.toggle(a.CSS.itemHidden, t.hideIf());\n    }), this.wrapper.classList.add(a.CSS.popoverOpened);\n  }\n  /**\n   * Closes the popover\n   *\n   * @returns {void}\n   */\n  close() {\n    this.wrapper.classList.remove(a.CSS.popoverOpened), this.itemEls.forEach(t => {\n      this.clearConfirmationState(t);\n    });\n  }\n}\nconst R = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 9L10 12M10 12L7 15M10 12H4\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 9L14 12M14 12L17 15M14 12H20\"/></svg>',\n  b = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M8 8L12 12M12 12L16 16M12 12L16 8M12 12L8 16\"/></svg>',\n  x = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M14.8833 9.16666L18.2167 12.5M18.2167 12.5L14.8833 15.8333M18.2167 12.5H10.05C9.16594 12.5 8.31809 12.1488 7.69297 11.5237C7.06785 10.8986 6.71666 10.0507 6.71666 9.16666\"/></svg>',\n  S = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M14.9167 14.9167L11.5833 18.25M11.5833 18.25L8.25 14.9167M11.5833 18.25L11.5833 10.0833C11.5833 9.19928 11.9345 8.35143 12.5596 7.72631C13.1848 7.10119 14.0326 6.75 14.9167 6.75\"/></svg>',\n  y = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9.13333 14.9167L12.4667 18.25M12.4667 18.25L15.8 14.9167M12.4667 18.25L12.4667 10.0833C12.4667 9.19928 12.1155 8.35143 11.4904 7.72631C10.8652 7.10119 10.0174 6.75 9.13333 6.75\"/></svg>',\n  L = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M14.8833 15.8333L18.2167 12.5M18.2167 12.5L14.8833 9.16667M18.2167 12.5L10.05 12.5C9.16595 12.5 8.31811 12.8512 7.69299 13.4763C7.06787 14.1014 6.71667 14.9493 6.71667 15.8333\"/></svg>',\n  M = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2.6\" d=\"M9.41 9.66H9.4\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2.6\" d=\"M14.6 9.66H14.59\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2.6\" d=\"M9.31 14.36H9.3\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2.6\" d=\"M14.6 14.36H14.59\"/></svg>',\n  v = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M12 7V12M12 17V12M17 12H12M12 12H7\"/></svg>',\n  O = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 9L20 12L17 15\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M14 12H20\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 9L4 12L7 15\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 12H10\"/></svg>',\n  T = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-width=\"2\" d=\"M5 10H19\"/><rect width=\"14\" height=\"14\" x=\"5\" y=\"5\" stroke=\"currentColor\" stroke-width=\"2\" rx=\"4\"/></svg>',\n  H = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-width=\"2\" d=\"M10 5V18.5\"/><path stroke=\"currentColor\" stroke-width=\"2\" d=\"M14 5V18.5\"/><path stroke=\"currentColor\" stroke-width=\"2\" d=\"M5 10H19\"/><path stroke=\"currentColor\" stroke-width=\"2\" d=\"M5 14H19\"/><rect width=\"14\" height=\"14\" x=\"5\" y=\"5\" stroke=\"currentColor\" stroke-width=\"2\" rx=\"4\"/></svg>',\n  A = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-width=\"2\" d=\"M10 5V18.5\"/><path stroke=\"currentColor\" stroke-width=\"2\" d=\"M5 10H19\"/><rect width=\"14\" height=\"14\" x=\"5\" y=\"5\" stroke=\"currentColor\" stroke-width=\"2\" rx=\"4\"/></svg>';\nclass w {\n  /**\n   * Creates toolbox buttons and toolbox menus\n   *\n   * @param {Object} config\n   * @param {any} config.api - Editor.js api\n   * @param {PopoverItem[]} config.items - Editor.js api\n   * @param {function} config.onOpen - callback fired when the Popover is opening\n   * @param {function} config.onClose - callback fired when the Popover is closing\n   * @param {string} config.cssModifier - the modifier for the Toolbox. Allows to add some specific styles.\n   */\n  constructor({\n    api: t,\n    items: e,\n    onOpen: o,\n    onClose: i,\n    cssModifier: n = \"\"\n  }) {\n    this.api = t, this.items = e, this.onOpen = o, this.onClose = i, this.cssModifier = n, this.popover = null, this.wrapper = this.createToolbox();\n  }\n  /**\n   * Style classes\n   */\n  static get CSS() {\n    return {\n      toolbox: \"tc-toolbox\",\n      toolboxShowed: \"tc-toolbox--showed\",\n      toggler: \"tc-toolbox__toggler\"\n    };\n  }\n  /**\n   * Returns rendered Toolbox element\n   */\n  get element() {\n    return this.wrapper;\n  }\n  /**\n   * Creating a toolbox to open menu for a manipulating columns\n   *\n   * @returns {Element}\n   */\n  createToolbox() {\n    const t = c(\"div\", [w.CSS.toolbox, this.cssModifier ? `${w.CSS.toolbox}--${this.cssModifier}` : \"\"]);\n    t.dataset.mutationFree = \"true\";\n    const e = this.createPopover(),\n      o = this.createToggler();\n    return t.appendChild(o), t.appendChild(e), t;\n  }\n  /**\n   * Creates the Toggler\n   *\n   * @returns {Element}\n   */\n  createToggler() {\n    const t = c(\"div\", w.CSS.toggler, {\n      innerHTML: M\n    });\n    return t.addEventListener(\"click\", () => {\n      this.togglerClicked();\n    }), t;\n  }\n  /**\n   * Creates the Popover instance and render it\n   *\n   * @returns {Element}\n   */\n  createPopover() {\n    return this.popover = new a({\n      items: this.items\n    }), this.popover.render();\n  }\n  /**\n   * Toggler click handler. Opens/Closes the popover\n   *\n   * @returns {void}\n   */\n  togglerClicked() {\n    this.popover.opened ? (this.popover.close(), this.onClose()) : (this.popover.open(), this.onOpen());\n  }\n  /**\n   * Shows the Toolbox\n   *\n   * @param {function} computePositionMethod - method that returns the position coordinate\n   * @returns {void}\n   */\n  show(t) {\n    const e = t();\n    Object.entries(e).forEach(([o, i]) => {\n      this.wrapper.style[o] = i;\n    }), this.wrapper.classList.add(w.CSS.toolboxShowed);\n  }\n  /**\n   * Hides the Toolbox\n   *\n   * @returns {void}\n   */\n  hide() {\n    this.popover.close(), this.wrapper.classList.remove(w.CSS.toolboxShowed);\n  }\n}\nfunction B(d, t) {\n  let e = 0;\n  return function (...o) {\n    const i = ( /* @__PURE__ */new Date()).getTime();\n    if (!(i - e < d)) return e = i, t(...o);\n  };\n}\nconst s = {\n  wrapper: \"tc-wrap\",\n  wrapperReadOnly: \"tc-wrap--readonly\",\n  table: \"tc-table\",\n  row: \"tc-row\",\n  withHeadings: \"tc-table--heading\",\n  rowSelected: \"tc-row--selected\",\n  cell: \"tc-cell\",\n  cellSelected: \"tc-cell--selected\",\n  addRow: \"tc-add-row\",\n  addRowDisabled: \"tc-add-row--disabled\",\n  addColumn: \"tc-add-column\",\n  addColumnDisabled: \"tc-add-column--disabled\"\n};\nclass E {\n  /**\n   * Creates\n   *\n   * @constructor\n   * @param {boolean} readOnly - read-only mode flag\n   * @param {object} api - Editor.js API\n   * @param {TableData} data - Editor.js API\n   * @param {TableConfig} config - Editor.js API\n   */\n  constructor(t, e, o, i) {\n    this.readOnly = t, this.api = e, this.data = o, this.config = i, this.wrapper = null, this.table = null, this.toolboxColumn = this.createColumnToolbox(), this.toolboxRow = this.createRowToolbox(), this.createTableWrapper(), this.hoveredRow = 0, this.hoveredColumn = 0, this.selectedRow = 0, this.selectedColumn = 0, this.tunes = {\n      withHeadings: !1\n    }, this.resize(), this.fill(), this.focusedCell = {\n      row: 0,\n      column: 0\n    }, this.documentClicked = n => {\n      const r = n.target.closest(`.${s.table}`) !== null,\n        h = n.target.closest(`.${s.wrapper}`) === null;\n      (r || h) && this.hideToolboxes();\n      const u = n.target.closest(`.${s.addRow}`),\n        p = n.target.closest(`.${s.addColumn}`);\n      u && u.parentNode === this.wrapper ? (this.addRow(void 0, !0), this.hideToolboxes()) : p && p.parentNode === this.wrapper && (this.addColumn(void 0, !0), this.hideToolboxes());\n    }, this.readOnly || this.bindEvents();\n  }\n  /**\n   * Returns the rendered table wrapper\n   *\n   * @returns {Element}\n   */\n  getWrapper() {\n    return this.wrapper;\n  }\n  /**\n   * Hangs the necessary handlers to events\n   */\n  bindEvents() {\n    document.addEventListener(\"click\", this.documentClicked), this.table.addEventListener(\"mousemove\", B(150, t => this.onMouseMoveInTable(t)), {\n      passive: !0\n    }), this.table.onkeypress = t => this.onKeyPressListener(t), this.table.addEventListener(\"keydown\", t => this.onKeyDownListener(t)), this.table.addEventListener(\"focusin\", t => this.focusInTableListener(t));\n  }\n  /**\n   * Configures and creates the toolbox for manipulating with columns\n   *\n   * @returns {Toolbox}\n   */\n  createColumnToolbox() {\n    return new w({\n      api: this.api,\n      cssModifier: \"column\",\n      items: [{\n        label: this.api.i18n.t(\"Add column to left\"),\n        icon: S,\n        hideIf: () => this.numberOfColumns === this.config.maxcols,\n        onClick: () => {\n          this.addColumn(this.selectedColumn, !0), this.hideToolboxes();\n        }\n      }, {\n        label: this.api.i18n.t(\"Add column to right\"),\n        icon: y,\n        hideIf: () => this.numberOfColumns === this.config.maxcols,\n        onClick: () => {\n          this.addColumn(this.selectedColumn + 1, !0), this.hideToolboxes();\n        }\n      }, {\n        label: this.api.i18n.t(\"Delete column\"),\n        icon: b,\n        hideIf: () => this.numberOfColumns === 1,\n        confirmationRequired: !0,\n        onClick: () => {\n          this.deleteColumn(this.selectedColumn), this.hideToolboxes();\n        }\n      }],\n      onOpen: () => {\n        this.selectColumn(this.hoveredColumn), this.hideRowToolbox();\n      },\n      onClose: () => {\n        this.unselectColumn();\n      }\n    });\n  }\n  /**\n   * Configures and creates the toolbox for manipulating with rows\n   *\n   * @returns {Toolbox}\n   */\n  createRowToolbox() {\n    return new w({\n      api: this.api,\n      cssModifier: \"row\",\n      items: [{\n        label: this.api.i18n.t(\"Add row above\"),\n        icon: L,\n        hideIf: () => this.numberOfRows === this.config.maxrows,\n        onClick: () => {\n          this.addRow(this.selectedRow, !0), this.hideToolboxes();\n        }\n      }, {\n        label: this.api.i18n.t(\"Add row below\"),\n        icon: x,\n        hideIf: () => this.numberOfRows === this.config.maxrows,\n        onClick: () => {\n          this.addRow(this.selectedRow + 1, !0), this.hideToolboxes();\n        }\n      }, {\n        label: this.api.i18n.t(\"Delete row\"),\n        icon: b,\n        hideIf: () => this.numberOfRows === 1,\n        confirmationRequired: !0,\n        onClick: () => {\n          this.deleteRow(this.selectedRow), this.hideToolboxes();\n        }\n      }],\n      onOpen: () => {\n        this.selectRow(this.hoveredRow), this.hideColumnToolbox();\n      },\n      onClose: () => {\n        this.unselectRow();\n      }\n    });\n  }\n  /**\n   * When you press enter it moves the cursor down to the next row\n   * or creates it if the click occurred on the last one\n   */\n  moveCursorToNextRow() {\n    this.focusedCell.row !== this.numberOfRows ? (this.focusedCell.row += 1, this.focusCell(this.focusedCell)) : (this.addRow(), this.focusedCell.row += 1, this.focusCell(this.focusedCell), this.updateToolboxesPosition(0, 0));\n  }\n  /**\n   * Get table cell by row and col index\n   *\n   * @param {number} row - cell row coordinate\n   * @param {number} column - cell column coordinate\n   * @returns {HTMLElement}\n   */\n  getCell(t, e) {\n    return this.table.querySelectorAll(`.${s.row}:nth-child(${t}) .${s.cell}`)[e - 1];\n  }\n  /**\n   * Get table row by index\n   *\n   * @param {number} row - row coordinate\n   * @returns {HTMLElement}\n   */\n  getRow(t) {\n    return this.table.querySelector(`.${s.row}:nth-child(${t})`);\n  }\n  /**\n   * The parent of the cell which is the row\n   *\n   * @param {HTMLElement} cell - cell element\n   * @returns {HTMLElement}\n   */\n  getRowByCell(t) {\n    return t.parentElement;\n  }\n  /**\n   * Ger row's first cell\n   *\n   * @param {Element} row - row to find its first cell\n   * @returns {Element}\n   */\n  getRowFirstCell(t) {\n    return t.querySelector(`.${s.cell}:first-child`);\n  }\n  /**\n   * Set the sell's content by row and column numbers\n   *\n   * @param {number} row - cell row coordinate\n   * @param {number} column - cell column coordinate\n   * @param {string} content - cell HTML content\n   */\n  setCellContent(t, e, o) {\n    const i = this.getCell(t, e);\n    i.innerHTML = o;\n  }\n  /**\n   * Add column in table on index place\n   * Add cells in each row\n   *\n   * @param {number} columnIndex - number in the array of columns, where new column to insert, -1 if insert at the end\n   * @param {boolean} [setFocus] - pass true to focus the first cell\n   */\n  addColumn(t = -1, e = !1) {\n    var n;\n    let o = this.numberOfColumns;\n    if (this.config && this.config.maxcols && this.numberOfColumns >= this.config.maxcols) return;\n    for (let r = 1; r <= this.numberOfRows; r++) {\n      let h;\n      const l = this.createCell();\n      if (t > 0 && t <= o ? (h = this.getCell(r, t), m(l, h)) : h = this.getRow(r).appendChild(l), r === 1) {\n        const u = this.getCell(r, t > 0 ? t : o + 1);\n        u && e && C(u);\n      }\n    }\n    const i = this.wrapper.querySelector(`.${s.addColumn}`);\n    (n = this.config) != null && n.maxcols && this.numberOfColumns > this.config.maxcols - 1 && i && i.classList.add(s.addColumnDisabled), this.addHeadingAttrToFirstRow();\n  }\n  /**\n   * Add row in table on index place\n   *\n   * @param {number} index - number in the array of rows, where new column to insert, -1 if insert at the end\n   * @param {boolean} [setFocus] - pass true to focus the inserted row\n   * @returns {HTMLElement} row\n   */\n  addRow(t = -1, e = !1) {\n    let o,\n      i = c(\"div\", s.row);\n    this.tunes.withHeadings && this.removeHeadingAttrFromFirstRow();\n    let n = this.numberOfColumns;\n    if (this.config && this.config.maxrows && this.numberOfRows >= this.config.maxrows && h) return;\n    if (t > 0 && t <= this.numberOfRows) {\n      let l = this.getRow(t);\n      o = m(i, l);\n    } else o = this.table.appendChild(i);\n    this.fillRow(o, n), this.tunes.withHeadings && this.addHeadingAttrToFirstRow();\n    const r = this.getRowFirstCell(o);\n    r && e && C(r);\n    const h = this.wrapper.querySelector(`.${s.addRow}`);\n    return this.config && this.config.maxrows && this.numberOfRows >= this.config.maxrows && h && h.classList.add(s.addRowDisabled), o;\n  }\n  /**\n   * Delete a column by index\n   *\n   * @param {number} index\n   */\n  deleteColumn(t) {\n    for (let o = 1; o <= this.numberOfRows; o++) {\n      const i = this.getCell(o, t);\n      if (!i) return;\n      i.remove();\n    }\n    const e = this.wrapper.querySelector(`.${s.addColumn}`);\n    e && e.classList.remove(s.addColumnDisabled);\n  }\n  /**\n   * Delete a row by index\n   *\n   * @param {number} index\n   */\n  deleteRow(t) {\n    this.getRow(t).remove();\n    const e = this.wrapper.querySelector(`.${s.addRow}`);\n    e && e.classList.remove(s.addRowDisabled), this.addHeadingAttrToFirstRow();\n  }\n  /**\n   * Create a wrapper containing a table, toolboxes\n   * and buttons for adding rows and columns\n   *\n   * @returns {HTMLElement} wrapper - where all buttons for a table and the table itself will be\n   */\n  createTableWrapper() {\n    if (this.wrapper = c(\"div\", s.wrapper), this.table = c(\"div\", s.table), this.readOnly && this.wrapper.classList.add(s.wrapperReadOnly), this.wrapper.appendChild(this.toolboxRow.element), this.wrapper.appendChild(this.toolboxColumn.element), this.wrapper.appendChild(this.table), !this.readOnly) {\n      const t = c(\"div\", s.addColumn, {\n          innerHTML: v\n        }),\n        e = c(\"div\", s.addRow, {\n          innerHTML: v\n        });\n      this.wrapper.appendChild(t), this.wrapper.appendChild(e);\n    }\n  }\n  /**\n   * Returns the size of the table based on initial data or config \"size\" property\n   *\n   * @return {{rows: number, cols: number}} - number of cols and rows\n   */\n  computeInitialSize() {\n    const t = this.data && this.data.content,\n      e = Array.isArray(t),\n      o = e ? t.length : !1,\n      i = e ? t.length : void 0,\n      n = o ? t[0].length : void 0,\n      r = Number.parseInt(this.config && this.config.rows),\n      h = Number.parseInt(this.config && this.config.cols),\n      l = !isNaN(r) && r > 0 ? r : void 0,\n      u = !isNaN(h) && h > 0 ? h : void 0;\n    return {\n      rows: i || l || 2,\n      cols: n || u || 2\n    };\n  }\n  /**\n   * Resize table to match config size or transmitted data size\n   *\n   * @return {{rows: number, cols: number}} - number of cols and rows\n   */\n  resize() {\n    const {\n      rows: t,\n      cols: e\n    } = this.computeInitialSize();\n    for (let o = 0; o < t; o++) this.addRow();\n    for (let o = 0; o < e; o++) this.addColumn();\n  }\n  /**\n   * Fills the table with data passed to the constructor\n   *\n   * @returns {void}\n   */\n  fill() {\n    const t = this.data;\n    if (t && t.content) for (let e = 0; e < t.content.length; e++) for (let o = 0; o < t.content[e].length; o++) this.setCellContent(e + 1, o + 1, t.content[e][o]);\n  }\n  /**\n   * Fills a row with cells\n   *\n   * @param {HTMLElement} row - row to fill\n   * @param {number} numberOfColumns - how many cells should be in a row\n   */\n  fillRow(t, e) {\n    for (let o = 1; o <= e; o++) {\n      const i = this.createCell();\n      t.appendChild(i);\n    }\n  }\n  /**\n   * Creating a cell element\n   *\n   * @return {Element}\n   */\n  createCell() {\n    return c(\"div\", s.cell, {\n      contentEditable: !this.readOnly\n    });\n  }\n  /**\n   * Get number of rows in the table\n   */\n  get numberOfRows() {\n    return this.table.childElementCount;\n  }\n  /**\n   * Get number of columns in the table\n   */\n  get numberOfColumns() {\n    return this.numberOfRows ? this.table.querySelectorAll(`.${s.row}:first-child .${s.cell}`).length : 0;\n  }\n  /**\n   * Is the column toolbox menu displayed or not\n   *\n   * @returns {boolean}\n   */\n  get isColumnMenuShowing() {\n    return this.selectedColumn !== 0;\n  }\n  /**\n   * Is the row toolbox menu displayed or not\n   *\n   * @returns {boolean}\n   */\n  get isRowMenuShowing() {\n    return this.selectedRow !== 0;\n  }\n  /**\n   * Recalculate position of toolbox icons\n   *\n   * @param {Event} event - mouse move event\n   */\n  onMouseMoveInTable(t) {\n    const {\n      row: e,\n      column: o\n    } = this.getHoveredCell(t);\n    this.hoveredColumn = o, this.hoveredRow = e, this.updateToolboxesPosition();\n  }\n  /**\n   * Prevents default Enter behaviors\n   * Adds Shift+Enter processing\n   *\n   * @param {KeyboardEvent} event - keypress event\n   */\n  onKeyPressListener(t) {\n    if (t.key === \"Enter\") {\n      if (t.shiftKey) return !0;\n      this.moveCursorToNextRow();\n    }\n    return t.key !== \"Enter\";\n  }\n  /**\n   * Prevents tab keydown event from bubbling\n   * so that it only works inside the table\n   *\n   * @param {KeyboardEvent} event - keydown event\n   */\n  onKeyDownListener(t) {\n    t.key === \"Tab\" && t.stopPropagation();\n  }\n  /**\n   * Set the coordinates of the cell that the focus has moved to\n   *\n   * @param {FocusEvent} event - focusin event\n   */\n  focusInTableListener(t) {\n    const e = t.target,\n      o = this.getRowByCell(e);\n    this.focusedCell = {\n      row: Array.from(this.table.querySelectorAll(`.${s.row}`)).indexOf(o) + 1,\n      column: Array.from(o.querySelectorAll(`.${s.cell}`)).indexOf(e) + 1\n    };\n  }\n  /**\n   * Unselect row/column\n   * Close toolbox menu\n   * Hide toolboxes\n   *\n   * @returns {void}\n   */\n  hideToolboxes() {\n    this.hideRowToolbox(), this.hideColumnToolbox(), this.updateToolboxesPosition();\n  }\n  /**\n   * Unselect row, close toolbox\n   *\n   * @returns {void}\n   */\n  hideRowToolbox() {\n    this.unselectRow(), this.toolboxRow.hide();\n  }\n  /**\n   * Unselect column, close toolbox\n   *\n   * @returns {void}\n   */\n  hideColumnToolbox() {\n    this.unselectColumn(), this.toolboxColumn.hide();\n  }\n  /**\n   * Set the cursor focus to the focused cell\n   *\n   * @returns {void}\n   */\n  focusCell() {\n    this.focusedCellElem.focus();\n  }\n  /**\n   * Get current focused element\n   *\n   * @returns {HTMLElement} - focused cell\n   */\n  get focusedCellElem() {\n    const {\n      row: t,\n      column: e\n    } = this.focusedCell;\n    return this.getCell(t, e);\n  }\n  /**\n   * Update toolboxes position\n   *\n   * @param {number} row - hovered row\n   * @param {number} column - hovered column\n   */\n  updateToolboxesPosition(t = this.hoveredRow, e = this.hoveredColumn) {\n    this.isColumnMenuShowing || e > 0 && e <= this.numberOfColumns && this.toolboxColumn.show(() => ({\n      left: `calc((100% - var(--cell-size)) / (${this.numberOfColumns} * 2) * (1 + (${e} - 1) * 2))`\n    })), this.isRowMenuShowing || t > 0 && t <= this.numberOfRows && this.toolboxRow.show(() => {\n      const o = this.getRow(t),\n        {\n          fromTopBorder: i\n        } = g(this.table, o),\n        {\n          height: n\n        } = o.getBoundingClientRect();\n      return {\n        top: `${Math.ceil(i + n / 2)}px`\n      };\n    });\n  }\n  /**\n   * Makes the first row headings\n   *\n   * @param {boolean} withHeadings - use headings row or not\n   */\n  setHeadingsSetting(t) {\n    this.tunes.withHeadings = t, t ? (this.table.classList.add(s.withHeadings), this.addHeadingAttrToFirstRow()) : (this.table.classList.remove(s.withHeadings), this.removeHeadingAttrFromFirstRow());\n  }\n  /**\n   * Adds an attribute for displaying the placeholder in the cell\n   */\n  addHeadingAttrToFirstRow() {\n    for (let t = 1; t <= this.numberOfColumns; t++) {\n      let e = this.getCell(1, t);\n      e && e.setAttribute(\"heading\", this.api.i18n.t(\"Heading\"));\n    }\n  }\n  /**\n   * Removes an attribute for displaying the placeholder in the cell\n   */\n  removeHeadingAttrFromFirstRow() {\n    for (let t = 1; t <= this.numberOfColumns; t++) {\n      let e = this.getCell(1, t);\n      e && e.removeAttribute(\"heading\");\n    }\n  }\n  /**\n   * Add effect of a selected row\n   *\n   * @param {number} index\n   */\n  selectRow(t) {\n    const e = this.getRow(t);\n    e && (this.selectedRow = t, e.classList.add(s.rowSelected));\n  }\n  /**\n   * Remove effect of a selected row\n   */\n  unselectRow() {\n    if (this.selectedRow <= 0) return;\n    const t = this.table.querySelector(`.${s.rowSelected}`);\n    t && t.classList.remove(s.rowSelected), this.selectedRow = 0;\n  }\n  /**\n   * Add effect of a selected column\n   *\n   * @param {number} index\n   */\n  selectColumn(t) {\n    for (let e = 1; e <= this.numberOfRows; e++) {\n      const o = this.getCell(e, t);\n      o && o.classList.add(s.cellSelected);\n    }\n    this.selectedColumn = t;\n  }\n  /**\n   * Remove effect of a selected column\n   */\n  unselectColumn() {\n    if (this.selectedColumn <= 0) return;\n    let t = this.table.querySelectorAll(`.${s.cellSelected}`);\n    Array.from(t).forEach(e => {\n      e.classList.remove(s.cellSelected);\n    }), this.selectedColumn = 0;\n  }\n  /**\n   * Calculates the row and column that the cursor is currently hovering over\n   * The search was optimized from O(n) to O (log n) via bin search to reduce the number of calculations\n   *\n   * @param {Event} event - mousemove event\n   * @returns hovered cell coordinates as an integer row and column\n   */\n  getHoveredCell(t) {\n    let e = this.hoveredRow,\n      o = this.hoveredColumn;\n    const {\n      width: i,\n      height: n,\n      x: r,\n      y: h\n    } = k(this.table, t);\n    return r >= 0 && (o = this.binSearch(this.numberOfColumns, l => this.getCell(1, l), ({\n      fromLeftBorder: l\n    }) => r < l, ({\n      fromRightBorder: l\n    }) => r > i - l)), h >= 0 && (e = this.binSearch(this.numberOfRows, l => this.getCell(l, 1), ({\n      fromTopBorder: l\n    }) => h < l, ({\n      fromBottomBorder: l\n    }) => h > n - l)), {\n      row: e || this.hoveredRow,\n      column: o || this.hoveredColumn\n    };\n  }\n  /**\n   * Looks for the index of the cell the mouse is hovering over.\n   * Cells can be represented as ordered intervals with left and\n   * right (upper and lower for rows) borders inside the table, if the mouse enters it, then this is our index\n   *\n   * @param {number} numberOfCells - upper bound of binary search\n   * @param {function} getCell - function to take the currently viewed cell\n   * @param {function} beforeTheLeftBorder - determines the cursor position, to the left of the cell or not\n   * @param {function} afterTheRightBorder - determines the cursor position, to the right of the cell or not\n   * @returns {number}\n   */\n  binSearch(t, e, o, i) {\n    let n = 0,\n      r = t + 1,\n      h = 0,\n      l;\n    for (; n < r - 1 && h < 10;) {\n      l = Math.ceil((n + r) / 2);\n      const u = e(l),\n        p = g(this.table, u);\n      if (o(p)) r = l;else if (i(p)) n = l;else break;\n      h++;\n    }\n    return l;\n  }\n  /**\n   * Collects data from cells into a two-dimensional array\n   *\n   * @returns {string[][]}\n   */\n  getData() {\n    const t = [];\n    for (let e = 1; e <= this.numberOfRows; e++) {\n      const o = this.table.querySelector(`.${s.row}:nth-child(${e})`),\n        i = Array.from(o.querySelectorAll(`.${s.cell}`));\n      i.every(r => !r.textContent.trim()) || t.push(i.map(r => r.innerHTML));\n    }\n    return t;\n  }\n  /**\n   * Remove listeners on the document\n   */\n  destroy() {\n    document.removeEventListener(\"click\", this.documentClicked);\n  }\n}\nclass F {\n  /**\n   * Notify core that read-only mode is supported\n   *\n   * @returns {boolean}\n   */\n  static get isReadOnlySupported() {\n    return !0;\n  }\n  /**\n   * Allow to press Enter inside the CodeTool textarea\n   *\n   * @returns {boolean}\n   * @public\n   */\n  static get enableLineBreaks() {\n    return !0;\n  }\n  /**\n   * Render plugin`s main Element and fill it with saved data\n   *\n   * @param {TableConstructor} init\n   */\n  constructor({\n    data: t,\n    config: e,\n    api: o,\n    readOnly: i,\n    block: n\n  }) {\n    this.api = o, this.readOnly = i, this.config = e, this.data = {\n      withHeadings: this.getConfig(\"withHeadings\", !1, t),\n      stretched: this.getConfig(\"stretched\", !1, t),\n      content: t && t.content ? t.content : []\n    }, this.table = null, this.block = n;\n  }\n  /**\n   * Get Tool toolbox settings\n   * icon - Tool icon's SVG\n   * title - title to show in toolbox\n   *\n   * @returns {{icon: string, title: string}}\n   */\n  static get toolbox() {\n    return {\n      icon: A,\n      title: \"Table\"\n    };\n  }\n  /**\n   * Return Tool's view\n   *\n   * @returns {HTMLDivElement}\n   */\n  render() {\n    return this.table = new E(this.readOnly, this.api, this.data, this.config), this.container = c(\"div\", this.api.styles.block), this.container.appendChild(this.table.getWrapper()), this.table.setHeadingsSetting(this.data.withHeadings), this.container;\n  }\n  /**\n   * Returns plugin settings\n   *\n   * @returns {Array}\n   */\n  renderSettings() {\n    return [{\n      label: this.api.i18n.t(\"With headings\"),\n      icon: T,\n      isActive: this.data.withHeadings,\n      closeOnActivate: !0,\n      toggle: !0,\n      onActivate: () => {\n        this.data.withHeadings = !0, this.table.setHeadingsSetting(this.data.withHeadings);\n      }\n    }, {\n      label: this.api.i18n.t(\"Without headings\"),\n      icon: H,\n      isActive: !this.data.withHeadings,\n      closeOnActivate: !0,\n      toggle: !0,\n      onActivate: () => {\n        this.data.withHeadings = !1, this.table.setHeadingsSetting(this.data.withHeadings);\n      }\n    }, {\n      label: this.data.stretched ? this.api.i18n.t(\"Collapse\") : this.api.i18n.t(\"Stretch\"),\n      icon: this.data.stretched ? R : O,\n      closeOnActivate: !0,\n      toggle: !0,\n      onActivate: () => {\n        this.data.stretched = !this.data.stretched, this.block.stretched = this.data.stretched;\n      }\n    }];\n  }\n  /**\n   * Extract table data from the view\n   *\n   * @returns {TableData} - saved data\n   */\n  save() {\n    const t = this.table.getData();\n    return {\n      withHeadings: this.data.withHeadings,\n      stretched: this.data.stretched,\n      content: t\n    };\n  }\n  /**\n   * Plugin destroyer\n   *\n   * @returns {void}\n   */\n  destroy() {\n    this.table.destroy();\n  }\n  /**\n   * A helper to get config value.\n   *\n   * @param {string} configName - the key to get from the config.\n   * @param {any} defaultValue - default value if config doesn't have passed key\n   * @param {object} savedData - previously saved data. If passed, the key will be got from there, otherwise from the config\n   * @returns {any} - config value.\n   */\n  getConfig(t, e = void 0, o = void 0) {\n    const i = this.data || o;\n    return i ? i[t] ? i[t] : e : this.config && this.config[t] ? this.config[t] : e;\n  }\n  /**\n   * Table onPaste configuration\n   *\n   * @public\n   */\n  static get pasteConfig() {\n    return {\n      tags: [\"TABLE\", \"TR\", \"TH\", \"TD\"]\n    };\n  }\n  /**\n   * On paste callback that is fired from Editor\n   *\n   * @param {PasteEvent} event - event with pasted data\n   */\n  onPaste(t) {\n    const e = t.detail.data,\n      o = e.querySelector(\":scope > thead, tr:first-of-type th\"),\n      n = Array.from(e.querySelectorAll(\"tr\")).map(r => Array.from(r.querySelectorAll(\"th, td\")).map(l => l.innerHTML));\n    this.data = {\n      withHeadings: o !== null,\n      content: n\n    }, this.table.wrapper && this.table.wrapper.replaceWith(this.render());\n  }\n}\nexport { F as default };", "map": {"version": 3, "names": ["r", "document", "o", "createElement", "nonce", "head", "querySelector", "content", "append<PERSON><PERSON><PERSON>", "createTextNode", "e", "console", "error", "c", "d", "t", "Array", "isArray", "classList", "add", "i", "Object", "prototype", "hasOwnProperty", "call", "f", "getBoundingClientRect", "y1", "Math", "floor", "top", "window", "pageYOffset", "x1", "left", "pageXOffset", "x2", "right", "y2", "bottom", "g", "fromTopBorder", "fromLeftBorder", "fromRightBorder", "fromBottomBorder", "k", "width", "height", "x", "n", "y", "clientX", "h", "clientY", "l", "m", "parentNode", "insertBefore", "C", "createRange", "getSelection", "selectNodeContents", "collapse", "removeAllRanges", "addRange", "a", "constructor", "items", "wrapper", "itemEls", "CSS", "popover", "popoverOpened", "item", "itemHidden", "itemConfirmState", "itemIcon", "itemLabel", "render", "for<PERSON>ach", "innerHTML", "icon", "textContent", "label", "dataset", "index", "push", "addEventListener", "popoverClicked", "target", "closest", "confirmationRequired", "hasConfirmationState", "setConfirmationState", "onClick", "clearConfirmationState", "remove", "contains", "opened", "open", "hideIf", "toggle", "close", "R", "b", "S", "L", "M", "v", "O", "T", "H", "A", "w", "api", "onOpen", "onClose", "cssModifier", "createToolbox", "toolbox", "toolboxShowed", "toggler", "element", "mutationFree", "createPopover", "create<PERSON><PERSON><PERSON>", "togglerClicked", "show", "entries", "style", "hide", "B", "Date", "getTime", "s", "wrapperReadOnly", "table", "row", "withHeadings", "rowSelected", "cell", "cellSelected", "addRow", "addRowDisabled", "addColumn", "addColumnDisabled", "E", "readOnly", "data", "config", "toolboxColumn", "createColumnToolbox", "toolboxRow", "createRowToolbox", "createTableWrapper", "hoveredRow", "hoveredColumn", "selectedRow", "selectedColumn", "tunes", "resize", "fill", "focusedCell", "column", "documentClicked", "hideToolboxes", "u", "p", "bindEvents", "getWrapper", "onMouseMoveInTable", "passive", "onkeypress", "onKeyPressListener", "onKeyDownListener", "focusInTableListener", "i18n", "numberOfColumns", "maxcols", "deleteColumn", "selectColumn", "hideRowToolbox", "unselectColumn", "numberOfRows", "maxrows", "deleteRow", "selectRow", "hideColumnToolbox", "unselectRow", "moveCursorToNextRow", "focusCell", "updateToolboxesPosition", "getCell", "querySelectorAll", "getRow", "getRowByCell", "parentElement", "getRowFirstCell", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "createCell", "addHeadingAttrToFirstRow", "removeHeadingAttrFromFirstRow", "fillRow", "computeInitialSize", "length", "Number", "parseInt", "rows", "cols", "isNaN", "contentEditable", "childElementCount", "isColumnMenuShowing", "isRowMenuShowing", "getHoveredCell", "key", "shift<PERSON>ey", "stopPropagation", "from", "indexOf", "focusedCellElem", "focus", "ceil", "setHeadingsSetting", "setAttribute", "removeAttribute", "binSearch", "getData", "every", "trim", "map", "destroy", "removeEventListener", "F", "isReadOnlySupported", "enableLineBreaks", "block", "getConfig", "stretched", "title", "container", "styles", "renderSettings", "isActive", "closeOnActivate", "onActivate", "save", "pasteConfig", "tags", "onPaste", "detail", "replaceWith", "default"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/@editorjs/table/dist/table.mjs"], "sourcesContent": ["(function(){var r;\"use strict\";try{if(typeof document<\"u\"){var o=document.createElement(\"style\");o.nonce=(r=document.head.querySelector(\"meta[property=csp-nonce]\"))==null?void 0:r.content,o.appendChild(document.createTextNode('.tc-wrap{--color-background:#f9f9fb;--color-text-secondary:#7b7e89;--color-border:#e8e8eb;--cell-size:34px;--toolbox-icon-size:18px;--toolbox-padding:6px;--toolbox-aiming-field-size:calc(var(--toolbox-icon-size) + var(--toolbox-padding)*2);border-left:0;position:relative;height:100%;width:100%;margin-top:var(--toolbox-icon-size);box-sizing:border-box;display:grid;grid-template-columns:calc(100% - var(--cell-size)) var(--cell-size);z-index:0}.tc-wrap--readonly{grid-template-columns:100% var(--cell-size)}.tc-wrap svg{vertical-align:top}@media print{.tc-wrap{border-left-color:var(--color-border);border-left-style:solid;border-left-width:1px;grid-template-columns:100% var(--cell-size)}}@media print{.tc-wrap .tc-row:after{display:none}}.tc-table{position:relative;width:100%;height:100%;display:grid;font-size:14px;border-top:1px solid var(--color-border);line-height:1.4}.tc-table:after{width:calc(var(--cell-size));height:100%;left:calc(var(--cell-size)*-1);top:0}.tc-table:after,.tc-table:before{position:absolute;content:\"\"}.tc-table:before{width:100%;height:var(--toolbox-aiming-field-size);top:calc(var(--toolbox-aiming-field-size)*-1);left:0}.tc-table--heading .tc-row:first-child{font-weight:600;border-bottom:2px solid var(--color-border)}.tc-table--heading .tc-row:first-child [contenteditable]:empty:before{content:attr(heading);color:var(--color-text-secondary)}.tc-table--heading .tc-row:first-child:after{bottom:-2px;border-bottom:2px solid var(--color-border)}.tc-add-column,.tc-add-row{display:flex;color:var(--color-text-secondary)}@media print{.tc-add{display:none}}.tc-add-column{padding:4px 0;justify-content:center;border-top:1px solid var(--color-border)}.tc-add-column--disabled{visibility:hidden}@media print{.tc-add-column{display:none}}.tc-add-row{height:var(--cell-size);align-items:center;padding-left:4px;position:relative}.tc-add-row--disabled{display:none}.tc-add-row:before{content:\"\";position:absolute;right:calc(var(--cell-size)*-1);width:var(--cell-size);height:100%}@media print{.tc-add-row{display:none}}.tc-add-column,.tc-add-row{transition:0s;cursor:pointer;will-change:background-color}.tc-add-column:hover,.tc-add-row:hover{transition:background-color .1s ease;background-color:var(--color-background)}.tc-add-row{margin-top:1px}.tc-add-row:hover:before{transition:.1s;background-color:var(--color-background)}.tc-row{display:grid;grid-template-columns:repeat(auto-fit,minmax(10px,1fr));position:relative;border-bottom:1px solid var(--color-border)}.tc-row:after{content:\"\";pointer-events:none;position:absolute;width:var(--cell-size);height:100%;bottom:-1px;right:calc(var(--cell-size)*-1);border-bottom:1px solid var(--color-border)}.tc-row--selected{background:var(--color-background)}.tc-row--selected:after{background:var(--color-background)}.tc-cell{border-right:1px solid var(--color-border);padding:6px 12px;overflow:hidden;outline:none;line-break:normal}.tc-cell--selected{background:var(--color-background)}.tc-wrap--readonly .tc-row:after{display:none}.tc-toolbox{--toolbox-padding:6px;--popover-margin:30px;--toggler-click-zone-size:30px;--toggler-dots-color:#7b7e89;--toggler-dots-color-hovered:#1d202b;position:absolute;cursor:pointer;z-index:1;opacity:0;transition:opacity .1s;will-change:left,opacity}.tc-toolbox--column{top:calc(var(--toggler-click-zone-size)*-1);transform:translate(calc(var(--toggler-click-zone-size)*-1/2));will-change:left,opacity}.tc-toolbox--row{left:calc(var(--popover-margin)*-1);transform:translateY(calc(var(--toggler-click-zone-size)*-1/2));margin-top:-1px;will-change:top,opacity}.tc-toolbox--showed{opacity:1}.tc-toolbox .tc-popover{position:absolute;top:0;left:var(--popover-margin)}.tc-toolbox__toggler{display:flex;align-items:center;justify-content:center;width:var(--toggler-click-zone-size);height:var(--toggler-click-zone-size);color:var(--toggler-dots-color);opacity:0;transition:opacity .15s ease;will-change:opacity}.tc-toolbox__toggler:hover{color:var(--toggler-dots-color-hovered)}.tc-toolbox__toggler svg{fill:currentColor}.tc-wrap:hover .tc-toolbox__toggler{opacity:1}.tc-settings .cdx-settings-button{width:50%;margin:0}.tc-popover{--color-border:#eaeaea;--color-background:#fff;--color-background-hover:rgba(232,232,235,.49);--color-background-confirm:#e24a4a;--color-background-confirm-hover:#d54040;--color-text-confirm:#fff;background:var(--color-background);border:1px solid var(--color-border);box-shadow:0 3px 15px -3px #0d142121;border-radius:6px;padding:6px;display:none;will-change:opacity,transform}.tc-popover--opened{display:block;animation:menuShowing .1s cubic-bezier(.215,.61,.355,1) forwards}.tc-popover__item{display:flex;align-items:center;padding:2px 14px 2px 2px;border-radius:5px;cursor:pointer;white-space:nowrap;-webkit-user-select:none;-moz-user-select:none;user-select:none}.tc-popover__item:hover{background:var(--color-background-hover)}.tc-popover__item:not(:last-of-type){margin-bottom:2px}.tc-popover__item-icon{display:inline-flex;width:26px;height:26px;align-items:center;justify-content:center;background:var(--color-background);border-radius:5px;border:1px solid var(--color-border);margin-right:8px}.tc-popover__item-label{line-height:22px;font-size:14px;font-weight:500}.tc-popover__item--confirm{background:var(--color-background-confirm);color:var(--color-text-confirm)}.tc-popover__item--confirm:hover{background-color:var(--color-background-confirm-hover)}.tc-popover__item--confirm .tc-popover__item-icon{background:var(--color-background-confirm);border-color:#0000001a}.tc-popover__item--confirm .tc-popover__item-icon svg{transition:transform .2s ease-in;transform:rotate(90deg) scale(1.2)}.tc-popover__item--hidden{display:none}@keyframes menuShowing{0%{opacity:0;transform:translateY(-8px) scale(.9)}70%{opacity:1;transform:translateY(2px)}to{transform:translateY(0)}}')),document.head.appendChild(o)}}catch(e){console.error(\"vite-plugin-css-injected-by-js\",e)}})();\nfunction c(d, t, e = {}) {\n  const o = document.createElement(d);\n  Array.isArray(t) ? o.classList.add(...t) : t && o.classList.add(t);\n  for (const i in e)\n    Object.prototype.hasOwnProperty.call(e, i) && (o[i] = e[i]);\n  return o;\n}\nfunction f(d) {\n  const t = d.getBoundingClientRect();\n  return {\n    y1: Math.floor(t.top + window.pageYOffset),\n    x1: Math.floor(t.left + window.pageXOffset),\n    x2: Math.floor(t.right + window.pageXOffset),\n    y2: Math.floor(t.bottom + window.pageYOffset)\n  };\n}\nfunction g(d, t) {\n  const e = f(d), o = f(t);\n  return {\n    fromTopBorder: o.y1 - e.y1,\n    fromLeftBorder: o.x1 - e.x1,\n    fromRightBorder: e.x2 - o.x2,\n    fromBottomBorder: e.y2 - o.y2\n  };\n}\nfunction k(d, t) {\n  const e = d.getBoundingClientRect(), { width: o, height: i, x: n, y: r } = e, { clientX: h, clientY: l } = t;\n  return {\n    width: o,\n    height: i,\n    x: h - n,\n    y: l - r\n  };\n}\nfunction m(d, t) {\n  return t.parentNode.insertBefore(d, t);\n}\nfunction C(d, t = !0) {\n  const e = document.createRange(), o = window.getSelection();\n  e.selectNodeContents(d), e.collapse(t), o.removeAllRanges(), o.addRange(e);\n}\nclass a {\n  /**\n   * @param {object} options - constructor options\n   * @param {PopoverItem[]} options.items - constructor options\n   */\n  constructor({ items: t }) {\n    this.items = t, this.wrapper = void 0, this.itemEls = [];\n  }\n  /**\n   * Set of CSS classnames used in popover\n   *\n   * @returns {object}\n   */\n  static get CSS() {\n    return {\n      popover: \"tc-popover\",\n      popoverOpened: \"tc-popover--opened\",\n      item: \"tc-popover__item\",\n      itemHidden: \"tc-popover__item--hidden\",\n      itemConfirmState: \"tc-popover__item--confirm\",\n      itemIcon: \"tc-popover__item-icon\",\n      itemLabel: \"tc-popover__item-label\"\n    };\n  }\n  /**\n   * Returns the popover element\n   *\n   * @returns {Element}\n   */\n  render() {\n    return this.wrapper = c(\"div\", a.CSS.popover), this.items.forEach((t, e) => {\n      const o = c(\"div\", a.CSS.item), i = c(\"div\", a.CSS.itemIcon, {\n        innerHTML: t.icon\n      }), n = c(\"div\", a.CSS.itemLabel, {\n        textContent: t.label\n      });\n      o.dataset.index = e, o.appendChild(i), o.appendChild(n), this.wrapper.appendChild(o), this.itemEls.push(o);\n    }), this.wrapper.addEventListener(\"click\", (t) => {\n      this.popoverClicked(t);\n    }), this.wrapper;\n  }\n  /**\n   * Popover wrapper click listener\n   * Used to delegate clicks in items\n   *\n   * @returns {void}\n   */\n  popoverClicked(t) {\n    const e = t.target.closest(`.${a.CSS.item}`);\n    if (!e)\n      return;\n    const o = e.dataset.index, i = this.items[o];\n    if (i.confirmationRequired && !this.hasConfirmationState(e)) {\n      this.setConfirmationState(e);\n      return;\n    }\n    i.onClick();\n  }\n  /**\n   * Enable the confirmation state on passed item\n   *\n   * @returns {void}\n   */\n  setConfirmationState(t) {\n    t.classList.add(a.CSS.itemConfirmState);\n  }\n  /**\n   * Disable the confirmation state on passed item\n   *\n   * @returns {void}\n   */\n  clearConfirmationState(t) {\n    t.classList.remove(a.CSS.itemConfirmState);\n  }\n  /**\n   * Check if passed item has the confirmation state\n   *\n   * @returns {boolean}\n   */\n  hasConfirmationState(t) {\n    return t.classList.contains(a.CSS.itemConfirmState);\n  }\n  /**\n   * Return an opening state\n   *\n   * @returns {boolean}\n   */\n  get opened() {\n    return this.wrapper.classList.contains(a.CSS.popoverOpened);\n  }\n  /**\n   * Opens the popover\n   *\n   * @returns {void}\n   */\n  open() {\n    this.items.forEach((t, e) => {\n      typeof t.hideIf == \"function\" && this.itemEls[e].classList.toggle(a.CSS.itemHidden, t.hideIf());\n    }), this.wrapper.classList.add(a.CSS.popoverOpened);\n  }\n  /**\n   * Closes the popover\n   *\n   * @returns {void}\n   */\n  close() {\n    this.wrapper.classList.remove(a.CSS.popoverOpened), this.itemEls.forEach((t) => {\n      this.clearConfirmationState(t);\n    });\n  }\n}\nconst R = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 9L10 12M10 12L7 15M10 12H4\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 9L14 12M14 12L17 15M14 12H20\"/></svg>', b = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M8 8L12 12M12 12L16 16M12 12L16 8M12 12L8 16\"/></svg>', x = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M14.8833 9.16666L18.2167 12.5M18.2167 12.5L14.8833 15.8333M18.2167 12.5H10.05C9.16594 12.5 8.31809 12.1488 7.69297 11.5237C7.06785 10.8986 6.71666 10.0507 6.71666 9.16666\"/></svg>', S = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M14.9167 14.9167L11.5833 18.25M11.5833 18.25L8.25 14.9167M11.5833 18.25L11.5833 10.0833C11.5833 9.19928 11.9345 8.35143 12.5596 7.72631C13.1848 7.10119 14.0326 6.75 14.9167 6.75\"/></svg>', y = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9.13333 14.9167L12.4667 18.25M12.4667 18.25L15.8 14.9167M12.4667 18.25L12.4667 10.0833C12.4667 9.19928 12.1155 8.35143 11.4904 7.72631C10.8652 7.10119 10.0174 6.75 9.13333 6.75\"/></svg>', L = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M14.8833 15.8333L18.2167 12.5M18.2167 12.5L14.8833 9.16667M18.2167 12.5L10.05 12.5C9.16595 12.5 8.31811 12.8512 7.69299 13.4763C7.06787 14.1014 6.71667 14.9493 6.71667 15.8333\"/></svg>', M = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2.6\" d=\"M9.41 9.66H9.4\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2.6\" d=\"M14.6 9.66H14.59\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2.6\" d=\"M9.31 14.36H9.3\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2.6\" d=\"M14.6 14.36H14.59\"/></svg>', v = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M12 7V12M12 17V12M17 12H12M12 12H7\"/></svg>', O = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 9L20 12L17 15\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M14 12H20\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 9L4 12L7 15\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 12H10\"/></svg>', T = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-width=\"2\" d=\"M5 10H19\"/><rect width=\"14\" height=\"14\" x=\"5\" y=\"5\" stroke=\"currentColor\" stroke-width=\"2\" rx=\"4\"/></svg>', H = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-width=\"2\" d=\"M10 5V18.5\"/><path stroke=\"currentColor\" stroke-width=\"2\" d=\"M14 5V18.5\"/><path stroke=\"currentColor\" stroke-width=\"2\" d=\"M5 10H19\"/><path stroke=\"currentColor\" stroke-width=\"2\" d=\"M5 14H19\"/><rect width=\"14\" height=\"14\" x=\"5\" y=\"5\" stroke=\"currentColor\" stroke-width=\"2\" rx=\"4\"/></svg>', A = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-width=\"2\" d=\"M10 5V18.5\"/><path stroke=\"currentColor\" stroke-width=\"2\" d=\"M5 10H19\"/><rect width=\"14\" height=\"14\" x=\"5\" y=\"5\" stroke=\"currentColor\" stroke-width=\"2\" rx=\"4\"/></svg>';\nclass w {\n  /**\n   * Creates toolbox buttons and toolbox menus\n   *\n   * @param {Object} config\n   * @param {any} config.api - Editor.js api\n   * @param {PopoverItem[]} config.items - Editor.js api\n   * @param {function} config.onOpen - callback fired when the Popover is opening\n   * @param {function} config.onClose - callback fired when the Popover is closing\n   * @param {string} config.cssModifier - the modifier for the Toolbox. Allows to add some specific styles.\n   */\n  constructor({ api: t, items: e, onOpen: o, onClose: i, cssModifier: n = \"\" }) {\n    this.api = t, this.items = e, this.onOpen = o, this.onClose = i, this.cssModifier = n, this.popover = null, this.wrapper = this.createToolbox();\n  }\n  /**\n   * Style classes\n   */\n  static get CSS() {\n    return {\n      toolbox: \"tc-toolbox\",\n      toolboxShowed: \"tc-toolbox--showed\",\n      toggler: \"tc-toolbox__toggler\"\n    };\n  }\n  /**\n   * Returns rendered Toolbox element\n   */\n  get element() {\n    return this.wrapper;\n  }\n  /**\n   * Creating a toolbox to open menu for a manipulating columns\n   *\n   * @returns {Element}\n   */\n  createToolbox() {\n    const t = c(\"div\", [\n      w.CSS.toolbox,\n      this.cssModifier ? `${w.CSS.toolbox}--${this.cssModifier}` : \"\"\n    ]);\n    t.dataset.mutationFree = \"true\";\n    const e = this.createPopover(), o = this.createToggler();\n    return t.appendChild(o), t.appendChild(e), t;\n  }\n  /**\n   * Creates the Toggler\n   *\n   * @returns {Element}\n   */\n  createToggler() {\n    const t = c(\"div\", w.CSS.toggler, {\n      innerHTML: M\n    });\n    return t.addEventListener(\"click\", () => {\n      this.togglerClicked();\n    }), t;\n  }\n  /**\n   * Creates the Popover instance and render it\n   *\n   * @returns {Element}\n   */\n  createPopover() {\n    return this.popover = new a({\n      items: this.items\n    }), this.popover.render();\n  }\n  /**\n   * Toggler click handler. Opens/Closes the popover\n   *\n   * @returns {void}\n   */\n  togglerClicked() {\n    this.popover.opened ? (this.popover.close(), this.onClose()) : (this.popover.open(), this.onOpen());\n  }\n  /**\n   * Shows the Toolbox\n   *\n   * @param {function} computePositionMethod - method that returns the position coordinate\n   * @returns {void}\n   */\n  show(t) {\n    const e = t();\n    Object.entries(e).forEach(([o, i]) => {\n      this.wrapper.style[o] = i;\n    }), this.wrapper.classList.add(w.CSS.toolboxShowed);\n  }\n  /**\n   * Hides the Toolbox\n   *\n   * @returns {void}\n   */\n  hide() {\n    this.popover.close(), this.wrapper.classList.remove(w.CSS.toolboxShowed);\n  }\n}\nfunction B(d, t) {\n  let e = 0;\n  return function(...o) {\n    const i = (/* @__PURE__ */ new Date()).getTime();\n    if (!(i - e < d))\n      return e = i, t(...o);\n  };\n}\nconst s = {\n  wrapper: \"tc-wrap\",\n  wrapperReadOnly: \"tc-wrap--readonly\",\n  table: \"tc-table\",\n  row: \"tc-row\",\n  withHeadings: \"tc-table--heading\",\n  rowSelected: \"tc-row--selected\",\n  cell: \"tc-cell\",\n  cellSelected: \"tc-cell--selected\",\n  addRow: \"tc-add-row\",\n  addRowDisabled: \"tc-add-row--disabled\",\n  addColumn: \"tc-add-column\",\n  addColumnDisabled: \"tc-add-column--disabled\"\n};\nclass E {\n  /**\n   * Creates\n   *\n   * @constructor\n   * @param {boolean} readOnly - read-only mode flag\n   * @param {object} api - Editor.js API\n   * @param {TableData} data - Editor.js API\n   * @param {TableConfig} config - Editor.js API\n   */\n  constructor(t, e, o, i) {\n    this.readOnly = t, this.api = e, this.data = o, this.config = i, this.wrapper = null, this.table = null, this.toolboxColumn = this.createColumnToolbox(), this.toolboxRow = this.createRowToolbox(), this.createTableWrapper(), this.hoveredRow = 0, this.hoveredColumn = 0, this.selectedRow = 0, this.selectedColumn = 0, this.tunes = {\n      withHeadings: !1\n    }, this.resize(), this.fill(), this.focusedCell = {\n      row: 0,\n      column: 0\n    }, this.documentClicked = (n) => {\n      const r = n.target.closest(`.${s.table}`) !== null, h = n.target.closest(`.${s.wrapper}`) === null;\n      (r || h) && this.hideToolboxes();\n      const u = n.target.closest(`.${s.addRow}`), p = n.target.closest(`.${s.addColumn}`);\n      u && u.parentNode === this.wrapper ? (this.addRow(void 0, !0), this.hideToolboxes()) : p && p.parentNode === this.wrapper && (this.addColumn(void 0, !0), this.hideToolboxes());\n    }, this.readOnly || this.bindEvents();\n  }\n  /**\n   * Returns the rendered table wrapper\n   *\n   * @returns {Element}\n   */\n  getWrapper() {\n    return this.wrapper;\n  }\n  /**\n   * Hangs the necessary handlers to events\n   */\n  bindEvents() {\n    document.addEventListener(\"click\", this.documentClicked), this.table.addEventListener(\"mousemove\", B(150, (t) => this.onMouseMoveInTable(t)), { passive: !0 }), this.table.onkeypress = (t) => this.onKeyPressListener(t), this.table.addEventListener(\"keydown\", (t) => this.onKeyDownListener(t)), this.table.addEventListener(\"focusin\", (t) => this.focusInTableListener(t));\n  }\n  /**\n   * Configures and creates the toolbox for manipulating with columns\n   *\n   * @returns {Toolbox}\n   */\n  createColumnToolbox() {\n    return new w({\n      api: this.api,\n      cssModifier: \"column\",\n      items: [\n        {\n          label: this.api.i18n.t(\"Add column to left\"),\n          icon: S,\n          hideIf: () => this.numberOfColumns === this.config.maxcols,\n          onClick: () => {\n            this.addColumn(this.selectedColumn, !0), this.hideToolboxes();\n          }\n        },\n        {\n          label: this.api.i18n.t(\"Add column to right\"),\n          icon: y,\n          hideIf: () => this.numberOfColumns === this.config.maxcols,\n          onClick: () => {\n            this.addColumn(this.selectedColumn + 1, !0), this.hideToolboxes();\n          }\n        },\n        {\n          label: this.api.i18n.t(\"Delete column\"),\n          icon: b,\n          hideIf: () => this.numberOfColumns === 1,\n          confirmationRequired: !0,\n          onClick: () => {\n            this.deleteColumn(this.selectedColumn), this.hideToolboxes();\n          }\n        }\n      ],\n      onOpen: () => {\n        this.selectColumn(this.hoveredColumn), this.hideRowToolbox();\n      },\n      onClose: () => {\n        this.unselectColumn();\n      }\n    });\n  }\n  /**\n   * Configures and creates the toolbox for manipulating with rows\n   *\n   * @returns {Toolbox}\n   */\n  createRowToolbox() {\n    return new w({\n      api: this.api,\n      cssModifier: \"row\",\n      items: [\n        {\n          label: this.api.i18n.t(\"Add row above\"),\n          icon: L,\n          hideIf: () => this.numberOfRows === this.config.maxrows,\n          onClick: () => {\n            this.addRow(this.selectedRow, !0), this.hideToolboxes();\n          }\n        },\n        {\n          label: this.api.i18n.t(\"Add row below\"),\n          icon: x,\n          hideIf: () => this.numberOfRows === this.config.maxrows,\n          onClick: () => {\n            this.addRow(this.selectedRow + 1, !0), this.hideToolboxes();\n          }\n        },\n        {\n          label: this.api.i18n.t(\"Delete row\"),\n          icon: b,\n          hideIf: () => this.numberOfRows === 1,\n          confirmationRequired: !0,\n          onClick: () => {\n            this.deleteRow(this.selectedRow), this.hideToolboxes();\n          }\n        }\n      ],\n      onOpen: () => {\n        this.selectRow(this.hoveredRow), this.hideColumnToolbox();\n      },\n      onClose: () => {\n        this.unselectRow();\n      }\n    });\n  }\n  /**\n   * When you press enter it moves the cursor down to the next row\n   * or creates it if the click occurred on the last one\n   */\n  moveCursorToNextRow() {\n    this.focusedCell.row !== this.numberOfRows ? (this.focusedCell.row += 1, this.focusCell(this.focusedCell)) : (this.addRow(), this.focusedCell.row += 1, this.focusCell(this.focusedCell), this.updateToolboxesPosition(0, 0));\n  }\n  /**\n   * Get table cell by row and col index\n   *\n   * @param {number} row - cell row coordinate\n   * @param {number} column - cell column coordinate\n   * @returns {HTMLElement}\n   */\n  getCell(t, e) {\n    return this.table.querySelectorAll(`.${s.row}:nth-child(${t}) .${s.cell}`)[e - 1];\n  }\n  /**\n   * Get table row by index\n   *\n   * @param {number} row - row coordinate\n   * @returns {HTMLElement}\n   */\n  getRow(t) {\n    return this.table.querySelector(`.${s.row}:nth-child(${t})`);\n  }\n  /**\n   * The parent of the cell which is the row\n   *\n   * @param {HTMLElement} cell - cell element\n   * @returns {HTMLElement}\n   */\n  getRowByCell(t) {\n    return t.parentElement;\n  }\n  /**\n   * Ger row's first cell\n   *\n   * @param {Element} row - row to find its first cell\n   * @returns {Element}\n   */\n  getRowFirstCell(t) {\n    return t.querySelector(`.${s.cell}:first-child`);\n  }\n  /**\n   * Set the sell's content by row and column numbers\n   *\n   * @param {number} row - cell row coordinate\n   * @param {number} column - cell column coordinate\n   * @param {string} content - cell HTML content\n   */\n  setCellContent(t, e, o) {\n    const i = this.getCell(t, e);\n    i.innerHTML = o;\n  }\n  /**\n   * Add column in table on index place\n   * Add cells in each row\n   *\n   * @param {number} columnIndex - number in the array of columns, where new column to insert, -1 if insert at the end\n   * @param {boolean} [setFocus] - pass true to focus the first cell\n   */\n  addColumn(t = -1, e = !1) {\n    var n;\n    let o = this.numberOfColumns;\n    if (this.config && this.config.maxcols && this.numberOfColumns >= this.config.maxcols)\n      return;\n    for (let r = 1; r <= this.numberOfRows; r++) {\n      let h;\n      const l = this.createCell();\n      if (t > 0 && t <= o ? (h = this.getCell(r, t), m(l, h)) : h = this.getRow(r).appendChild(l), r === 1) {\n        const u = this.getCell(r, t > 0 ? t : o + 1);\n        u && e && C(u);\n      }\n    }\n    const i = this.wrapper.querySelector(`.${s.addColumn}`);\n    (n = this.config) != null && n.maxcols && this.numberOfColumns > this.config.maxcols - 1 && i && i.classList.add(s.addColumnDisabled), this.addHeadingAttrToFirstRow();\n  }\n  /**\n   * Add row in table on index place\n   *\n   * @param {number} index - number in the array of rows, where new column to insert, -1 if insert at the end\n   * @param {boolean} [setFocus] - pass true to focus the inserted row\n   * @returns {HTMLElement} row\n   */\n  addRow(t = -1, e = !1) {\n    let o, i = c(\"div\", s.row);\n    this.tunes.withHeadings && this.removeHeadingAttrFromFirstRow();\n    let n = this.numberOfColumns;\n    if (this.config && this.config.maxrows && this.numberOfRows >= this.config.maxrows && h)\n      return;\n    if (t > 0 && t <= this.numberOfRows) {\n      let l = this.getRow(t);\n      o = m(i, l);\n    } else\n      o = this.table.appendChild(i);\n    this.fillRow(o, n), this.tunes.withHeadings && this.addHeadingAttrToFirstRow();\n    const r = this.getRowFirstCell(o);\n    r && e && C(r);\n    const h = this.wrapper.querySelector(`.${s.addRow}`);\n    return this.config && this.config.maxrows && this.numberOfRows >= this.config.maxrows && h && h.classList.add(s.addRowDisabled), o;\n  }\n  /**\n   * Delete a column by index\n   *\n   * @param {number} index\n   */\n  deleteColumn(t) {\n    for (let o = 1; o <= this.numberOfRows; o++) {\n      const i = this.getCell(o, t);\n      if (!i)\n        return;\n      i.remove();\n    }\n    const e = this.wrapper.querySelector(`.${s.addColumn}`);\n    e && e.classList.remove(s.addColumnDisabled);\n  }\n  /**\n   * Delete a row by index\n   *\n   * @param {number} index\n   */\n  deleteRow(t) {\n    this.getRow(t).remove();\n    const e = this.wrapper.querySelector(`.${s.addRow}`);\n    e && e.classList.remove(s.addRowDisabled), this.addHeadingAttrToFirstRow();\n  }\n  /**\n   * Create a wrapper containing a table, toolboxes\n   * and buttons for adding rows and columns\n   *\n   * @returns {HTMLElement} wrapper - where all buttons for a table and the table itself will be\n   */\n  createTableWrapper() {\n    if (this.wrapper = c(\"div\", s.wrapper), this.table = c(\"div\", s.table), this.readOnly && this.wrapper.classList.add(s.wrapperReadOnly), this.wrapper.appendChild(this.toolboxRow.element), this.wrapper.appendChild(this.toolboxColumn.element), this.wrapper.appendChild(this.table), !this.readOnly) {\n      const t = c(\"div\", s.addColumn, {\n        innerHTML: v\n      }), e = c(\"div\", s.addRow, {\n        innerHTML: v\n      });\n      this.wrapper.appendChild(t), this.wrapper.appendChild(e);\n    }\n  }\n  /**\n   * Returns the size of the table based on initial data or config \"size\" property\n   *\n   * @return {{rows: number, cols: number}} - number of cols and rows\n   */\n  computeInitialSize() {\n    const t = this.data && this.data.content, e = Array.isArray(t), o = e ? t.length : !1, i = e ? t.length : void 0, n = o ? t[0].length : void 0, r = Number.parseInt(this.config && this.config.rows), h = Number.parseInt(this.config && this.config.cols), l = !isNaN(r) && r > 0 ? r : void 0, u = !isNaN(h) && h > 0 ? h : void 0;\n    return {\n      rows: i || l || 2,\n      cols: n || u || 2\n    };\n  }\n  /**\n   * Resize table to match config size or transmitted data size\n   *\n   * @return {{rows: number, cols: number}} - number of cols and rows\n   */\n  resize() {\n    const { rows: t, cols: e } = this.computeInitialSize();\n    for (let o = 0; o < t; o++)\n      this.addRow();\n    for (let o = 0; o < e; o++)\n      this.addColumn();\n  }\n  /**\n   * Fills the table with data passed to the constructor\n   *\n   * @returns {void}\n   */\n  fill() {\n    const t = this.data;\n    if (t && t.content)\n      for (let e = 0; e < t.content.length; e++)\n        for (let o = 0; o < t.content[e].length; o++)\n          this.setCellContent(e + 1, o + 1, t.content[e][o]);\n  }\n  /**\n   * Fills a row with cells\n   *\n   * @param {HTMLElement} row - row to fill\n   * @param {number} numberOfColumns - how many cells should be in a row\n   */\n  fillRow(t, e) {\n    for (let o = 1; o <= e; o++) {\n      const i = this.createCell();\n      t.appendChild(i);\n    }\n  }\n  /**\n   * Creating a cell element\n   *\n   * @return {Element}\n   */\n  createCell() {\n    return c(\"div\", s.cell, {\n      contentEditable: !this.readOnly\n    });\n  }\n  /**\n   * Get number of rows in the table\n   */\n  get numberOfRows() {\n    return this.table.childElementCount;\n  }\n  /**\n   * Get number of columns in the table\n   */\n  get numberOfColumns() {\n    return this.numberOfRows ? this.table.querySelectorAll(`.${s.row}:first-child .${s.cell}`).length : 0;\n  }\n  /**\n   * Is the column toolbox menu displayed or not\n   *\n   * @returns {boolean}\n   */\n  get isColumnMenuShowing() {\n    return this.selectedColumn !== 0;\n  }\n  /**\n   * Is the row toolbox menu displayed or not\n   *\n   * @returns {boolean}\n   */\n  get isRowMenuShowing() {\n    return this.selectedRow !== 0;\n  }\n  /**\n   * Recalculate position of toolbox icons\n   *\n   * @param {Event} event - mouse move event\n   */\n  onMouseMoveInTable(t) {\n    const { row: e, column: o } = this.getHoveredCell(t);\n    this.hoveredColumn = o, this.hoveredRow = e, this.updateToolboxesPosition();\n  }\n  /**\n   * Prevents default Enter behaviors\n   * Adds Shift+Enter processing\n   *\n   * @param {KeyboardEvent} event - keypress event\n   */\n  onKeyPressListener(t) {\n    if (t.key === \"Enter\") {\n      if (t.shiftKey)\n        return !0;\n      this.moveCursorToNextRow();\n    }\n    return t.key !== \"Enter\";\n  }\n  /**\n   * Prevents tab keydown event from bubbling\n   * so that it only works inside the table\n   *\n   * @param {KeyboardEvent} event - keydown event\n   */\n  onKeyDownListener(t) {\n    t.key === \"Tab\" && t.stopPropagation();\n  }\n  /**\n   * Set the coordinates of the cell that the focus has moved to\n   *\n   * @param {FocusEvent} event - focusin event\n   */\n  focusInTableListener(t) {\n    const e = t.target, o = this.getRowByCell(e);\n    this.focusedCell = {\n      row: Array.from(this.table.querySelectorAll(`.${s.row}`)).indexOf(o) + 1,\n      column: Array.from(o.querySelectorAll(`.${s.cell}`)).indexOf(e) + 1\n    };\n  }\n  /**\n   * Unselect row/column\n   * Close toolbox menu\n   * Hide toolboxes\n   *\n   * @returns {void}\n   */\n  hideToolboxes() {\n    this.hideRowToolbox(), this.hideColumnToolbox(), this.updateToolboxesPosition();\n  }\n  /**\n   * Unselect row, close toolbox\n   *\n   * @returns {void}\n   */\n  hideRowToolbox() {\n    this.unselectRow(), this.toolboxRow.hide();\n  }\n  /**\n   * Unselect column, close toolbox\n   *\n   * @returns {void}\n   */\n  hideColumnToolbox() {\n    this.unselectColumn(), this.toolboxColumn.hide();\n  }\n  /**\n   * Set the cursor focus to the focused cell\n   *\n   * @returns {void}\n   */\n  focusCell() {\n    this.focusedCellElem.focus();\n  }\n  /**\n   * Get current focused element\n   *\n   * @returns {HTMLElement} - focused cell\n   */\n  get focusedCellElem() {\n    const { row: t, column: e } = this.focusedCell;\n    return this.getCell(t, e);\n  }\n  /**\n   * Update toolboxes position\n   *\n   * @param {number} row - hovered row\n   * @param {number} column - hovered column\n   */\n  updateToolboxesPosition(t = this.hoveredRow, e = this.hoveredColumn) {\n    this.isColumnMenuShowing || e > 0 && e <= this.numberOfColumns && this.toolboxColumn.show(() => ({\n      left: `calc((100% - var(--cell-size)) / (${this.numberOfColumns} * 2) * (1 + (${e} - 1) * 2))`\n    })), this.isRowMenuShowing || t > 0 && t <= this.numberOfRows && this.toolboxRow.show(() => {\n      const o = this.getRow(t), { fromTopBorder: i } = g(this.table, o), { height: n } = o.getBoundingClientRect();\n      return {\n        top: `${Math.ceil(i + n / 2)}px`\n      };\n    });\n  }\n  /**\n   * Makes the first row headings\n   *\n   * @param {boolean} withHeadings - use headings row or not\n   */\n  setHeadingsSetting(t) {\n    this.tunes.withHeadings = t, t ? (this.table.classList.add(s.withHeadings), this.addHeadingAttrToFirstRow()) : (this.table.classList.remove(s.withHeadings), this.removeHeadingAttrFromFirstRow());\n  }\n  /**\n   * Adds an attribute for displaying the placeholder in the cell\n   */\n  addHeadingAttrToFirstRow() {\n    for (let t = 1; t <= this.numberOfColumns; t++) {\n      let e = this.getCell(1, t);\n      e && e.setAttribute(\"heading\", this.api.i18n.t(\"Heading\"));\n    }\n  }\n  /**\n   * Removes an attribute for displaying the placeholder in the cell\n   */\n  removeHeadingAttrFromFirstRow() {\n    for (let t = 1; t <= this.numberOfColumns; t++) {\n      let e = this.getCell(1, t);\n      e && e.removeAttribute(\"heading\");\n    }\n  }\n  /**\n   * Add effect of a selected row\n   *\n   * @param {number} index\n   */\n  selectRow(t) {\n    const e = this.getRow(t);\n    e && (this.selectedRow = t, e.classList.add(s.rowSelected));\n  }\n  /**\n   * Remove effect of a selected row\n   */\n  unselectRow() {\n    if (this.selectedRow <= 0)\n      return;\n    const t = this.table.querySelector(`.${s.rowSelected}`);\n    t && t.classList.remove(s.rowSelected), this.selectedRow = 0;\n  }\n  /**\n   * Add effect of a selected column\n   *\n   * @param {number} index\n   */\n  selectColumn(t) {\n    for (let e = 1; e <= this.numberOfRows; e++) {\n      const o = this.getCell(e, t);\n      o && o.classList.add(s.cellSelected);\n    }\n    this.selectedColumn = t;\n  }\n  /**\n   * Remove effect of a selected column\n   */\n  unselectColumn() {\n    if (this.selectedColumn <= 0)\n      return;\n    let t = this.table.querySelectorAll(`.${s.cellSelected}`);\n    Array.from(t).forEach((e) => {\n      e.classList.remove(s.cellSelected);\n    }), this.selectedColumn = 0;\n  }\n  /**\n   * Calculates the row and column that the cursor is currently hovering over\n   * The search was optimized from O(n) to O (log n) via bin search to reduce the number of calculations\n   *\n   * @param {Event} event - mousemove event\n   * @returns hovered cell coordinates as an integer row and column\n   */\n  getHoveredCell(t) {\n    let e = this.hoveredRow, o = this.hoveredColumn;\n    const { width: i, height: n, x: r, y: h } = k(this.table, t);\n    return r >= 0 && (o = this.binSearch(\n      this.numberOfColumns,\n      (l) => this.getCell(1, l),\n      ({ fromLeftBorder: l }) => r < l,\n      ({ fromRightBorder: l }) => r > i - l\n    )), h >= 0 && (e = this.binSearch(\n      this.numberOfRows,\n      (l) => this.getCell(l, 1),\n      ({ fromTopBorder: l }) => h < l,\n      ({ fromBottomBorder: l }) => h > n - l\n    )), {\n      row: e || this.hoveredRow,\n      column: o || this.hoveredColumn\n    };\n  }\n  /**\n   * Looks for the index of the cell the mouse is hovering over.\n   * Cells can be represented as ordered intervals with left and\n   * right (upper and lower for rows) borders inside the table, if the mouse enters it, then this is our index\n   *\n   * @param {number} numberOfCells - upper bound of binary search\n   * @param {function} getCell - function to take the currently viewed cell\n   * @param {function} beforeTheLeftBorder - determines the cursor position, to the left of the cell or not\n   * @param {function} afterTheRightBorder - determines the cursor position, to the right of the cell or not\n   * @returns {number}\n   */\n  binSearch(t, e, o, i) {\n    let n = 0, r = t + 1, h = 0, l;\n    for (; n < r - 1 && h < 10; ) {\n      l = Math.ceil((n + r) / 2);\n      const u = e(l), p = g(this.table, u);\n      if (o(p))\n        r = l;\n      else if (i(p))\n        n = l;\n      else\n        break;\n      h++;\n    }\n    return l;\n  }\n  /**\n   * Collects data from cells into a two-dimensional array\n   *\n   * @returns {string[][]}\n   */\n  getData() {\n    const t = [];\n    for (let e = 1; e <= this.numberOfRows; e++) {\n      const o = this.table.querySelector(`.${s.row}:nth-child(${e})`), i = Array.from(o.querySelectorAll(`.${s.cell}`));\n      i.every((r) => !r.textContent.trim()) || t.push(i.map((r) => r.innerHTML));\n    }\n    return t;\n  }\n  /**\n   * Remove listeners on the document\n   */\n  destroy() {\n    document.removeEventListener(\"click\", this.documentClicked);\n  }\n}\nclass F {\n  /**\n   * Notify core that read-only mode is supported\n   *\n   * @returns {boolean}\n   */\n  static get isReadOnlySupported() {\n    return !0;\n  }\n  /**\n   * Allow to press Enter inside the CodeTool textarea\n   *\n   * @returns {boolean}\n   * @public\n   */\n  static get enableLineBreaks() {\n    return !0;\n  }\n  /**\n   * Render plugin`s main Element and fill it with saved data\n   *\n   * @param {TableConstructor} init\n   */\n  constructor({ data: t, config: e, api: o, readOnly: i, block: n }) {\n    this.api = o, this.readOnly = i, this.config = e, this.data = {\n      withHeadings: this.getConfig(\"withHeadings\", !1, t),\n      stretched: this.getConfig(\"stretched\", !1, t),\n      content: t && t.content ? t.content : []\n    }, this.table = null, this.block = n;\n  }\n  /**\n   * Get Tool toolbox settings\n   * icon - Tool icon's SVG\n   * title - title to show in toolbox\n   *\n   * @returns {{icon: string, title: string}}\n   */\n  static get toolbox() {\n    return {\n      icon: A,\n      title: \"Table\"\n    };\n  }\n  /**\n   * Return Tool's view\n   *\n   * @returns {HTMLDivElement}\n   */\n  render() {\n    return this.table = new E(this.readOnly, this.api, this.data, this.config), this.container = c(\"div\", this.api.styles.block), this.container.appendChild(this.table.getWrapper()), this.table.setHeadingsSetting(this.data.withHeadings), this.container;\n  }\n  /**\n   * Returns plugin settings\n   *\n   * @returns {Array}\n   */\n  renderSettings() {\n    return [\n      {\n        label: this.api.i18n.t(\"With headings\"),\n        icon: T,\n        isActive: this.data.withHeadings,\n        closeOnActivate: !0,\n        toggle: !0,\n        onActivate: () => {\n          this.data.withHeadings = !0, this.table.setHeadingsSetting(this.data.withHeadings);\n        }\n      },\n      {\n        label: this.api.i18n.t(\"Without headings\"),\n        icon: H,\n        isActive: !this.data.withHeadings,\n        closeOnActivate: !0,\n        toggle: !0,\n        onActivate: () => {\n          this.data.withHeadings = !1, this.table.setHeadingsSetting(this.data.withHeadings);\n        }\n      },\n      {\n        label: this.data.stretched ? this.api.i18n.t(\"Collapse\") : this.api.i18n.t(\"Stretch\"),\n        icon: this.data.stretched ? R : O,\n        closeOnActivate: !0,\n        toggle: !0,\n        onActivate: () => {\n          this.data.stretched = !this.data.stretched, this.block.stretched = this.data.stretched;\n        }\n      }\n    ];\n  }\n  /**\n   * Extract table data from the view\n   *\n   * @returns {TableData} - saved data\n   */\n  save() {\n    const t = this.table.getData();\n    return {\n      withHeadings: this.data.withHeadings,\n      stretched: this.data.stretched,\n      content: t\n    };\n  }\n  /**\n   * Plugin destroyer\n   *\n   * @returns {void}\n   */\n  destroy() {\n    this.table.destroy();\n  }\n  /**\n   * A helper to get config value.\n   *\n   * @param {string} configName - the key to get from the config.\n   * @param {any} defaultValue - default value if config doesn't have passed key\n   * @param {object} savedData - previously saved data. If passed, the key will be got from there, otherwise from the config\n   * @returns {any} - config value.\n   */\n  getConfig(t, e = void 0, o = void 0) {\n    const i = this.data || o;\n    return i ? i[t] ? i[t] : e : this.config && this.config[t] ? this.config[t] : e;\n  }\n  /**\n   * Table onPaste configuration\n   *\n   * @public\n   */\n  static get pasteConfig() {\n    return { tags: [\"TABLE\", \"TR\", \"TH\", \"TD\"] };\n  }\n  /**\n   * On paste callback that is fired from Editor\n   *\n   * @param {PasteEvent} event - event with pasted data\n   */\n  onPaste(t) {\n    const e = t.detail.data, o = e.querySelector(\":scope > thead, tr:first-of-type th\"), n = Array.from(e.querySelectorAll(\"tr\")).map((r) => Array.from(r.querySelectorAll(\"th, td\")).map((l) => l.innerHTML));\n    this.data = {\n      withHeadings: o !== null,\n      content: n\n    }, this.table.wrapper && this.table.wrapper.replaceWith(this.render());\n  }\n}\nexport {\n  F as default\n};\n"], "mappings": "AAAA,CAAC,YAAU;EAAC,IAAIA,CAAC;EAAC,YAAY;EAAC,IAAG;IAAC,IAAG,OAAOC,QAAQ,GAAC,GAAG,EAAC;MAAC,IAAIC,CAAC,GAACD,QAAQ,CAACE,aAAa,CAAC,OAAO,CAAC;MAACD,CAAC,CAACE,KAAK,GAAC,CAACJ,CAAC,GAACC,QAAQ,CAACI,IAAI,CAACC,aAAa,CAAC,0BAA0B,CAAC,KAAG,IAAI,GAAC,KAAK,CAAC,GAACN,CAAC,CAACO,OAAO,EAACL,CAAC,CAACM,WAAW,CAACP,QAAQ,CAACQ,cAAc,CAAC,isLAAisL,CAAC,CAAC,EAACR,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACN,CAAC,CAAC;IAAA;EAAC,CAAC,QAAMQ,CAAC,EAAC;IAACC,OAAO,CAACC,KAAK,CAAC,gCAAgC,EAACF,CAAC,CAAC;EAAA;AAAC,CAAC,EAAE,CAAC;AACngM,SAASG,CAACA,CAACC,CAAC,EAAEC,CAAC,EAAEL,CAAC,GAAG,CAAC,CAAC,EAAE;EACvB,MAAMR,CAAC,GAAGD,QAAQ,CAACE,aAAa,CAACW,CAAC,CAAC;EACnCE,KAAK,CAACC,OAAO,CAACF,CAAC,CAAC,GAAGb,CAAC,CAACgB,SAAS,CAACC,GAAG,CAAC,GAAGJ,CAAC,CAAC,GAAGA,CAAC,IAAIb,CAAC,CAACgB,SAAS,CAACC,GAAG,CAACJ,CAAC,CAAC;EAClE,KAAK,MAAMK,CAAC,IAAIV,CAAC,EACfW,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACd,CAAC,EAAEU,CAAC,CAAC,KAAKlB,CAAC,CAACkB,CAAC,CAAC,GAAGV,CAAC,CAACU,CAAC,CAAC,CAAC;EAC7D,OAAOlB,CAAC;AACV;AACA,SAASuB,CAACA,CAACX,CAAC,EAAE;EACZ,MAAMC,CAAC,GAAGD,CAAC,CAACY,qBAAqB,CAAC,CAAC;EACnC,OAAO;IACLC,EAAE,EAAEC,IAAI,CAACC,KAAK,CAACd,CAAC,CAACe,GAAG,GAAGC,MAAM,CAACC,WAAW,CAAC;IAC1CC,EAAE,EAAEL,IAAI,CAACC,KAAK,CAACd,CAAC,CAACmB,IAAI,GAAGH,MAAM,CAACI,WAAW,CAAC;IAC3CC,EAAE,EAAER,IAAI,CAACC,KAAK,CAACd,CAAC,CAACsB,KAAK,GAAGN,MAAM,CAACI,WAAW,CAAC;IAC5CG,EAAE,EAAEV,IAAI,CAACC,KAAK,CAACd,CAAC,CAACwB,MAAM,GAAGR,MAAM,CAACC,WAAW;EAC9C,CAAC;AACH;AACA,SAASQ,CAACA,CAAC1B,CAAC,EAAEC,CAAC,EAAE;EACf,MAAML,CAAC,GAAGe,CAAC,CAACX,CAAC,CAAC;IAAEZ,CAAC,GAAGuB,CAAC,CAACV,CAAC,CAAC;EACxB,OAAO;IACL0B,aAAa,EAAEvC,CAAC,CAACyB,EAAE,GAAGjB,CAAC,CAACiB,EAAE;IAC1Be,cAAc,EAAExC,CAAC,CAAC+B,EAAE,GAAGvB,CAAC,CAACuB,EAAE;IAC3BU,eAAe,EAAEjC,CAAC,CAAC0B,EAAE,GAAGlC,CAAC,CAACkC,EAAE;IAC5BQ,gBAAgB,EAAElC,CAAC,CAAC4B,EAAE,GAAGpC,CAAC,CAACoC;EAC7B,CAAC;AACH;AACA,SAASO,CAACA,CAAC/B,CAAC,EAAEC,CAAC,EAAE;EACf,MAAML,CAAC,GAAGI,CAAC,CAACY,qBAAqB,CAAC,CAAC;IAAE;MAAEoB,KAAK,EAAE5C,CAAC;MAAE6C,MAAM,EAAE3B,CAAC;MAAE4B,CAAC,EAAEC,CAAC;MAAEC,CAAC,EAAElD;IAAE,CAAC,GAAGU,CAAC;IAAE;MAAEyC,OAAO,EAAEC,CAAC;MAAEC,OAAO,EAAEC;IAAE,CAAC,GAAGvC,CAAC;EAC5G,OAAO;IACL+B,KAAK,EAAE5C,CAAC;IACR6C,MAAM,EAAE3B,CAAC;IACT4B,CAAC,EAAEI,CAAC,GAAGH,CAAC;IACRC,CAAC,EAAEI,CAAC,GAAGtD;EACT,CAAC;AACH;AACA,SAASuD,CAACA,CAACzC,CAAC,EAAEC,CAAC,EAAE;EACf,OAAOA,CAAC,CAACyC,UAAU,CAACC,YAAY,CAAC3C,CAAC,EAAEC,CAAC,CAAC;AACxC;AACA,SAAS2C,CAACA,CAAC5C,CAAC,EAAEC,CAAC,GAAG,CAAC,CAAC,EAAE;EACpB,MAAML,CAAC,GAAGT,QAAQ,CAAC0D,WAAW,CAAC,CAAC;IAAEzD,CAAC,GAAG6B,MAAM,CAAC6B,YAAY,CAAC,CAAC;EAC3DlD,CAAC,CAACmD,kBAAkB,CAAC/C,CAAC,CAAC,EAAEJ,CAAC,CAACoD,QAAQ,CAAC/C,CAAC,CAAC,EAAEb,CAAC,CAAC6D,eAAe,CAAC,CAAC,EAAE7D,CAAC,CAAC8D,QAAQ,CAACtD,CAAC,CAAC;AAC5E;AACA,MAAMuD,CAAC,CAAC;EACN;AACF;AACA;AACA;EACEC,WAAWA,CAAC;IAAEC,KAAK,EAAEpD;EAAE,CAAC,EAAE;IACxB,IAAI,CAACoD,KAAK,GAAGpD,CAAC,EAAE,IAAI,CAACqD,OAAO,GAAG,KAAK,CAAC,EAAE,IAAI,CAACC,OAAO,GAAG,EAAE;EAC1D;EACA;AACF;AACA;AACA;AACA;EACE,WAAWC,GAAGA,CAAA,EAAG;IACf,OAAO;MACLC,OAAO,EAAE,YAAY;MACrBC,aAAa,EAAE,oBAAoB;MACnCC,IAAI,EAAE,kBAAkB;MACxBC,UAAU,EAAE,0BAA0B;MACtCC,gBAAgB,EAAE,2BAA2B;MAC7CC,QAAQ,EAAE,uBAAuB;MACjCC,SAAS,EAAE;IACb,CAAC;EACH;EACA;AACF;AACA;AACA;AACA;EACEC,MAAMA,CAAA,EAAG;IACP,OAAO,IAAI,CAACV,OAAO,GAAGvD,CAAC,CAAC,KAAK,EAAEoD,CAAC,CAACK,GAAG,CAACC,OAAO,CAAC,EAAE,IAAI,CAACJ,KAAK,CAACY,OAAO,CAAC,CAAChE,CAAC,EAAEL,CAAC,KAAK;MAC1E,MAAMR,CAAC,GAAGW,CAAC,CAAC,KAAK,EAAEoD,CAAC,CAACK,GAAG,CAACG,IAAI,CAAC;QAAErD,CAAC,GAAGP,CAAC,CAAC,KAAK,EAAEoD,CAAC,CAACK,GAAG,CAACM,QAAQ,EAAE;UAC3DI,SAAS,EAAEjE,CAAC,CAACkE;QACf,CAAC,CAAC;QAAEhC,CAAC,GAAGpC,CAAC,CAAC,KAAK,EAAEoD,CAAC,CAACK,GAAG,CAACO,SAAS,EAAE;UAChCK,WAAW,EAAEnE,CAAC,CAACoE;QACjB,CAAC,CAAC;MACFjF,CAAC,CAACkF,OAAO,CAACC,KAAK,GAAG3E,CAAC,EAAER,CAAC,CAACM,WAAW,CAACY,CAAC,CAAC,EAAElB,CAAC,CAACM,WAAW,CAACyC,CAAC,CAAC,EAAE,IAAI,CAACmB,OAAO,CAAC5D,WAAW,CAACN,CAAC,CAAC,EAAE,IAAI,CAACmE,OAAO,CAACiB,IAAI,CAACpF,CAAC,CAAC;IAC5G,CAAC,CAAC,EAAE,IAAI,CAACkE,OAAO,CAACmB,gBAAgB,CAAC,OAAO,EAAGxE,CAAC,IAAK;MAChD,IAAI,CAACyE,cAAc,CAACzE,CAAC,CAAC;IACxB,CAAC,CAAC,EAAE,IAAI,CAACqD,OAAO;EAClB;EACA;AACF;AACA;AACA;AACA;AACA;EACEoB,cAAcA,CAACzE,CAAC,EAAE;IAChB,MAAML,CAAC,GAAGK,CAAC,CAAC0E,MAAM,CAACC,OAAO,CAAE,IAAGzB,CAAC,CAACK,GAAG,CAACG,IAAK,EAAC,CAAC;IAC5C,IAAI,CAAC/D,CAAC,EACJ;IACF,MAAMR,CAAC,GAAGQ,CAAC,CAAC0E,OAAO,CAACC,KAAK;MAAEjE,CAAC,GAAG,IAAI,CAAC+C,KAAK,CAACjE,CAAC,CAAC;IAC5C,IAAIkB,CAAC,CAACuE,oBAAoB,IAAI,CAAC,IAAI,CAACC,oBAAoB,CAAClF,CAAC,CAAC,EAAE;MAC3D,IAAI,CAACmF,oBAAoB,CAACnF,CAAC,CAAC;MAC5B;IACF;IACAU,CAAC,CAAC0E,OAAO,CAAC,CAAC;EACb;EACA;AACF;AACA;AACA;AACA;EACED,oBAAoBA,CAAC9E,CAAC,EAAE;IACtBA,CAAC,CAACG,SAAS,CAACC,GAAG,CAAC8C,CAAC,CAACK,GAAG,CAACK,gBAAgB,CAAC;EACzC;EACA;AACF;AACA;AACA;AACA;EACEoB,sBAAsBA,CAAChF,CAAC,EAAE;IACxBA,CAAC,CAACG,SAAS,CAAC8E,MAAM,CAAC/B,CAAC,CAACK,GAAG,CAACK,gBAAgB,CAAC;EAC5C;EACA;AACF;AACA;AACA;AACA;EACEiB,oBAAoBA,CAAC7E,CAAC,EAAE;IACtB,OAAOA,CAAC,CAACG,SAAS,CAAC+E,QAAQ,CAAChC,CAAC,CAACK,GAAG,CAACK,gBAAgB,CAAC;EACrD;EACA;AACF;AACA;AACA;AACA;EACE,IAAIuB,MAAMA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC9B,OAAO,CAAClD,SAAS,CAAC+E,QAAQ,CAAChC,CAAC,CAACK,GAAG,CAACE,aAAa,CAAC;EAC7D;EACA;AACF;AACA;AACA;AACA;EACE2B,IAAIA,CAAA,EAAG;IACL,IAAI,CAAChC,KAAK,CAACY,OAAO,CAAC,CAAChE,CAAC,EAAEL,CAAC,KAAK;MAC3B,OAAOK,CAAC,CAACqF,MAAM,IAAI,UAAU,IAAI,IAAI,CAAC/B,OAAO,CAAC3D,CAAC,CAAC,CAACQ,SAAS,CAACmF,MAAM,CAACpC,CAAC,CAACK,GAAG,CAACI,UAAU,EAAE3D,CAAC,CAACqF,MAAM,CAAC,CAAC,CAAC;IACjG,CAAC,CAAC,EAAE,IAAI,CAAChC,OAAO,CAAClD,SAAS,CAACC,GAAG,CAAC8C,CAAC,CAACK,GAAG,CAACE,aAAa,CAAC;EACrD;EACA;AACF;AACA;AACA;AACA;EACE8B,KAAKA,CAAA,EAAG;IACN,IAAI,CAAClC,OAAO,CAAClD,SAAS,CAAC8E,MAAM,CAAC/B,CAAC,CAACK,GAAG,CAACE,aAAa,CAAC,EAAE,IAAI,CAACH,OAAO,CAACU,OAAO,CAAEhE,CAAC,IAAK;MAC9E,IAAI,CAACgF,sBAAsB,CAAChF,CAAC,CAAC;IAChC,CAAC,CAAC;EACJ;AACF;AACA,MAAMwF,CAAC,GAAG,wWAAwW;EAAEC,CAAC,GAAG,6NAA6N;EAAExD,CAAC,GAAG,mXAAmX;EAAEyD,CAAC,GAAG,0XAA0X;EAAEvD,CAAC,GAAG,0XAA0X;EAAEwD,CAAC,GAAG,wXAAwX;EAAEC,CAAC,GAAG,qdAAqd;EAAEC,CAAC,GAAG,mNAAmN;EAAEC,CAAC,GAAG,+hBAA+hB;EAAEC,CAAC,GAAG,0PAA0P;EAAEC,CAAC,GAAG,+aAA+a;EAAEC,CAAC,GAAG,uTAAuT;AACvxI,MAAMC,CAAC,CAAC;EACN;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE/C,WAAWA,CAAC;IAAEgD,GAAG,EAAEnG,CAAC;IAAEoD,KAAK,EAAEzD,CAAC;IAAEyG,MAAM,EAAEjH,CAAC;IAAEkH,OAAO,EAAEhG,CAAC;IAAEiG,WAAW,EAAEpE,CAAC,GAAG;EAAG,CAAC,EAAE;IAC5E,IAAI,CAACiE,GAAG,GAAGnG,CAAC,EAAE,IAAI,CAACoD,KAAK,GAAGzD,CAAC,EAAE,IAAI,CAACyG,MAAM,GAAGjH,CAAC,EAAE,IAAI,CAACkH,OAAO,GAAGhG,CAAC,EAAE,IAAI,CAACiG,WAAW,GAAGpE,CAAC,EAAE,IAAI,CAACsB,OAAO,GAAG,IAAI,EAAE,IAAI,CAACH,OAAO,GAAG,IAAI,CAACkD,aAAa,CAAC,CAAC;EACjJ;EACA;AACF;AACA;EACE,WAAWhD,GAAGA,CAAA,EAAG;IACf,OAAO;MACLiD,OAAO,EAAE,YAAY;MACrBC,aAAa,EAAE,oBAAoB;MACnCC,OAAO,EAAE;IACX,CAAC;EACH;EACA;AACF;AACA;EACE,IAAIC,OAAOA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACtD,OAAO;EACrB;EACA;AACF;AACA;AACA;AACA;EACEkD,aAAaA,CAAA,EAAG;IACd,MAAMvG,CAAC,GAAGF,CAAC,CAAC,KAAK,EAAE,CACjBoG,CAAC,CAAC3C,GAAG,CAACiD,OAAO,EACb,IAAI,CAACF,WAAW,GAAI,GAAEJ,CAAC,CAAC3C,GAAG,CAACiD,OAAQ,KAAI,IAAI,CAACF,WAAY,EAAC,GAAG,EAAE,CAChE,CAAC;IACFtG,CAAC,CAACqE,OAAO,CAACuC,YAAY,GAAG,MAAM;IAC/B,MAAMjH,CAAC,GAAG,IAAI,CAACkH,aAAa,CAAC,CAAC;MAAE1H,CAAC,GAAG,IAAI,CAAC2H,aAAa,CAAC,CAAC;IACxD,OAAO9G,CAAC,CAACP,WAAW,CAACN,CAAC,CAAC,EAAEa,CAAC,CAACP,WAAW,CAACE,CAAC,CAAC,EAAEK,CAAC;EAC9C;EACA;AACF;AACA;AACA;AACA;EACE8G,aAAaA,CAAA,EAAG;IACd,MAAM9G,CAAC,GAAGF,CAAC,CAAC,KAAK,EAAEoG,CAAC,CAAC3C,GAAG,CAACmD,OAAO,EAAE;MAChCzC,SAAS,EAAE2B;IACb,CAAC,CAAC;IACF,OAAO5F,CAAC,CAACwE,gBAAgB,CAAC,OAAO,EAAE,MAAM;MACvC,IAAI,CAACuC,cAAc,CAAC,CAAC;IACvB,CAAC,CAAC,EAAE/G,CAAC;EACP;EACA;AACF;AACA;AACA;AACA;EACE6G,aAAaA,CAAA,EAAG;IACd,OAAO,IAAI,CAACrD,OAAO,GAAG,IAAIN,CAAC,CAAC;MAC1BE,KAAK,EAAE,IAAI,CAACA;IACd,CAAC,CAAC,EAAE,IAAI,CAACI,OAAO,CAACO,MAAM,CAAC,CAAC;EAC3B;EACA;AACF;AACA;AACA;AACA;EACEgD,cAAcA,CAAA,EAAG;IACf,IAAI,CAACvD,OAAO,CAAC2B,MAAM,IAAI,IAAI,CAAC3B,OAAO,CAAC+B,KAAK,CAAC,CAAC,EAAE,IAAI,CAACc,OAAO,CAAC,CAAC,KAAK,IAAI,CAAC7C,OAAO,CAAC4B,IAAI,CAAC,CAAC,EAAE,IAAI,CAACgB,MAAM,CAAC,CAAC,CAAC;EACrG;EACA;AACF;AACA;AACA;AACA;AACA;EACEY,IAAIA,CAAChH,CAAC,EAAE;IACN,MAAML,CAAC,GAAGK,CAAC,CAAC,CAAC;IACbM,MAAM,CAAC2G,OAAO,CAACtH,CAAC,CAAC,CAACqE,OAAO,CAAC,CAAC,CAAC7E,CAAC,EAAEkB,CAAC,CAAC,KAAK;MACpC,IAAI,CAACgD,OAAO,CAAC6D,KAAK,CAAC/H,CAAC,CAAC,GAAGkB,CAAC;IAC3B,CAAC,CAAC,EAAE,IAAI,CAACgD,OAAO,CAAClD,SAAS,CAACC,GAAG,CAAC8F,CAAC,CAAC3C,GAAG,CAACkD,aAAa,CAAC;EACrD;EACA;AACF;AACA;AACA;AACA;EACEU,IAAIA,CAAA,EAAG;IACL,IAAI,CAAC3D,OAAO,CAAC+B,KAAK,CAAC,CAAC,EAAE,IAAI,CAAClC,OAAO,CAAClD,SAAS,CAAC8E,MAAM,CAACiB,CAAC,CAAC3C,GAAG,CAACkD,aAAa,CAAC;EAC1E;AACF;AACA,SAASW,CAACA,CAACrH,CAAC,EAAEC,CAAC,EAAE;EACf,IAAIL,CAAC,GAAG,CAAC;EACT,OAAO,UAAS,GAAGR,CAAC,EAAE;IACpB,MAAMkB,CAAC,GAAG,EAAC,eAAgB,IAAIgH,IAAI,CAAC,CAAC,EAAEC,OAAO,CAAC,CAAC;IAChD,IAAI,EAAEjH,CAAC,GAAGV,CAAC,GAAGI,CAAC,CAAC,EACd,OAAOJ,CAAC,GAAGU,CAAC,EAAEL,CAAC,CAAC,GAAGb,CAAC,CAAC;EACzB,CAAC;AACH;AACA,MAAMoI,CAAC,GAAG;EACRlE,OAAO,EAAE,SAAS;EAClBmE,eAAe,EAAE,mBAAmB;EACpCC,KAAK,EAAE,UAAU;EACjBC,GAAG,EAAE,QAAQ;EACbC,YAAY,EAAE,mBAAmB;EACjCC,WAAW,EAAE,kBAAkB;EAC/BC,IAAI,EAAE,SAAS;EACfC,YAAY,EAAE,mBAAmB;EACjCC,MAAM,EAAE,YAAY;EACpBC,cAAc,EAAE,sBAAsB;EACtCC,SAAS,EAAE,eAAe;EAC1BC,iBAAiB,EAAE;AACrB,CAAC;AACD,MAAMC,CAAC,CAAC;EACN;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEhF,WAAWA,CAACnD,CAAC,EAAEL,CAAC,EAAER,CAAC,EAAEkB,CAAC,EAAE;IACtB,IAAI,CAAC+H,QAAQ,GAAGpI,CAAC,EAAE,IAAI,CAACmG,GAAG,GAAGxG,CAAC,EAAE,IAAI,CAAC0I,IAAI,GAAGlJ,CAAC,EAAE,IAAI,CAACmJ,MAAM,GAAGjI,CAAC,EAAE,IAAI,CAACgD,OAAO,GAAG,IAAI,EAAE,IAAI,CAACoE,KAAK,GAAG,IAAI,EAAE,IAAI,CAACc,aAAa,GAAG,IAAI,CAACC,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAACC,UAAU,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC,EAAE,IAAI,CAACC,kBAAkB,CAAC,CAAC,EAAE,IAAI,CAACC,UAAU,GAAG,CAAC,EAAE,IAAI,CAACC,aAAa,GAAG,CAAC,EAAE,IAAI,CAACC,WAAW,GAAG,CAAC,EAAE,IAAI,CAACC,cAAc,GAAG,CAAC,EAAE,IAAI,CAACC,KAAK,GAAG;MACvUrB,YAAY,EAAE,CAAC;IACjB,CAAC,EAAE,IAAI,CAACsB,MAAM,CAAC,CAAC,EAAE,IAAI,CAACC,IAAI,CAAC,CAAC,EAAE,IAAI,CAACC,WAAW,GAAG;MAChDzB,GAAG,EAAE,CAAC;MACN0B,MAAM,EAAE;IACV,CAAC,EAAE,IAAI,CAACC,eAAe,GAAInH,CAAC,IAAK;MAC/B,MAAMjD,CAAC,GAAGiD,CAAC,CAACwC,MAAM,CAACC,OAAO,CAAE,IAAG4C,CAAC,CAACE,KAAM,EAAC,CAAC,KAAK,IAAI;QAAEpF,CAAC,GAAGH,CAAC,CAACwC,MAAM,CAACC,OAAO,CAAE,IAAG4C,CAAC,CAAClE,OAAQ,EAAC,CAAC,KAAK,IAAI;MAClG,CAACpE,CAAC,IAAIoD,CAAC,KAAK,IAAI,CAACiH,aAAa,CAAC,CAAC;MAChC,MAAMC,CAAC,GAAGrH,CAAC,CAACwC,MAAM,CAACC,OAAO,CAAE,IAAG4C,CAAC,CAACQ,MAAO,EAAC,CAAC;QAAEyB,CAAC,GAAGtH,CAAC,CAACwC,MAAM,CAACC,OAAO,CAAE,IAAG4C,CAAC,CAACU,SAAU,EAAC,CAAC;MACnFsB,CAAC,IAAIA,CAAC,CAAC9G,UAAU,KAAK,IAAI,CAACY,OAAO,IAAI,IAAI,CAAC0E,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAACuB,aAAa,CAAC,CAAC,IAAIE,CAAC,IAAIA,CAAC,CAAC/G,UAAU,KAAK,IAAI,CAACY,OAAO,KAAK,IAAI,CAAC4E,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAACqB,aAAa,CAAC,CAAC,CAAC;IACjL,CAAC,EAAE,IAAI,CAAClB,QAAQ,IAAI,IAAI,CAACqB,UAAU,CAAC,CAAC;EACvC;EACA;AACF;AACA;AACA;AACA;EACEC,UAAUA,CAAA,EAAG;IACX,OAAO,IAAI,CAACrG,OAAO;EACrB;EACA;AACF;AACA;EACEoG,UAAUA,CAAA,EAAG;IACXvK,QAAQ,CAACsF,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC6E,eAAe,CAAC,EAAE,IAAI,CAAC5B,KAAK,CAACjD,gBAAgB,CAAC,WAAW,EAAE4C,CAAC,CAAC,GAAG,EAAGpH,CAAC,IAAK,IAAI,CAAC2J,kBAAkB,CAAC3J,CAAC,CAAC,CAAC,EAAE;MAAE4J,OAAO,EAAE,CAAC;IAAE,CAAC,CAAC,EAAE,IAAI,CAACnC,KAAK,CAACoC,UAAU,GAAI7J,CAAC,IAAK,IAAI,CAAC8J,kBAAkB,CAAC9J,CAAC,CAAC,EAAE,IAAI,CAACyH,KAAK,CAACjD,gBAAgB,CAAC,SAAS,EAAGxE,CAAC,IAAK,IAAI,CAAC+J,iBAAiB,CAAC/J,CAAC,CAAC,CAAC,EAAE,IAAI,CAACyH,KAAK,CAACjD,gBAAgB,CAAC,SAAS,EAAGxE,CAAC,IAAK,IAAI,CAACgK,oBAAoB,CAAChK,CAAC,CAAC,CAAC;EAClX;EACA;AACF;AACA;AACA;AACA;EACEwI,mBAAmBA,CAAA,EAAG;IACpB,OAAO,IAAItC,CAAC,CAAC;MACXC,GAAG,EAAE,IAAI,CAACA,GAAG;MACbG,WAAW,EAAE,QAAQ;MACrBlD,KAAK,EAAE,CACL;QACEgB,KAAK,EAAE,IAAI,CAAC+B,GAAG,CAAC8D,IAAI,CAACjK,CAAC,CAAC,oBAAoB,CAAC;QAC5CkE,IAAI,EAAEwB,CAAC;QACPL,MAAM,EAAEA,CAAA,KAAM,IAAI,CAAC6E,eAAe,KAAK,IAAI,CAAC5B,MAAM,CAAC6B,OAAO;QAC1DpF,OAAO,EAAEA,CAAA,KAAM;UACb,IAAI,CAACkD,SAAS,CAAC,IAAI,CAACc,cAAc,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAACO,aAAa,CAAC,CAAC;QAC/D;MACF,CAAC,EACD;QACElF,KAAK,EAAE,IAAI,CAAC+B,GAAG,CAAC8D,IAAI,CAACjK,CAAC,CAAC,qBAAqB,CAAC;QAC7CkE,IAAI,EAAE/B,CAAC;QACPkD,MAAM,EAAEA,CAAA,KAAM,IAAI,CAAC6E,eAAe,KAAK,IAAI,CAAC5B,MAAM,CAAC6B,OAAO;QAC1DpF,OAAO,EAAEA,CAAA,KAAM;UACb,IAAI,CAACkD,SAAS,CAAC,IAAI,CAACc,cAAc,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAACO,aAAa,CAAC,CAAC;QACnE;MACF,CAAC,EACD;QACElF,KAAK,EAAE,IAAI,CAAC+B,GAAG,CAAC8D,IAAI,CAACjK,CAAC,CAAC,eAAe,CAAC;QACvCkE,IAAI,EAAEuB,CAAC;QACPJ,MAAM,EAAEA,CAAA,KAAM,IAAI,CAAC6E,eAAe,KAAK,CAAC;QACxCtF,oBAAoB,EAAE,CAAC,CAAC;QACxBG,OAAO,EAAEA,CAAA,KAAM;UACb,IAAI,CAACqF,YAAY,CAAC,IAAI,CAACrB,cAAc,CAAC,EAAE,IAAI,CAACO,aAAa,CAAC,CAAC;QAC9D;MACF,CAAC,CACF;MACDlD,MAAM,EAAEA,CAAA,KAAM;QACZ,IAAI,CAACiE,YAAY,CAAC,IAAI,CAACxB,aAAa,CAAC,EAAE,IAAI,CAACyB,cAAc,CAAC,CAAC;MAC9D,CAAC;MACDjE,OAAO,EAAEA,CAAA,KAAM;QACb,IAAI,CAACkE,cAAc,CAAC,CAAC;MACvB;IACF,CAAC,CAAC;EACJ;EACA;AACF;AACA;AACA;AACA;EACE7B,gBAAgBA,CAAA,EAAG;IACjB,OAAO,IAAIxC,CAAC,CAAC;MACXC,GAAG,EAAE,IAAI,CAACA,GAAG;MACbG,WAAW,EAAE,KAAK;MAClBlD,KAAK,EAAE,CACL;QACEgB,KAAK,EAAE,IAAI,CAAC+B,GAAG,CAAC8D,IAAI,CAACjK,CAAC,CAAC,eAAe,CAAC;QACvCkE,IAAI,EAAEyB,CAAC;QACPN,MAAM,EAAEA,CAAA,KAAM,IAAI,CAACmF,YAAY,KAAK,IAAI,CAAClC,MAAM,CAACmC,OAAO;QACvD1F,OAAO,EAAEA,CAAA,KAAM;UACb,IAAI,CAACgD,MAAM,CAAC,IAAI,CAACe,WAAW,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAACQ,aAAa,CAAC,CAAC;QACzD;MACF,CAAC,EACD;QACElF,KAAK,EAAE,IAAI,CAAC+B,GAAG,CAAC8D,IAAI,CAACjK,CAAC,CAAC,eAAe,CAAC;QACvCkE,IAAI,EAAEjC,CAAC;QACPoD,MAAM,EAAEA,CAAA,KAAM,IAAI,CAACmF,YAAY,KAAK,IAAI,CAAClC,MAAM,CAACmC,OAAO;QACvD1F,OAAO,EAAEA,CAAA,KAAM;UACb,IAAI,CAACgD,MAAM,CAAC,IAAI,CAACe,WAAW,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAACQ,aAAa,CAAC,CAAC;QAC7D;MACF,CAAC,EACD;QACElF,KAAK,EAAE,IAAI,CAAC+B,GAAG,CAAC8D,IAAI,CAACjK,CAAC,CAAC,YAAY,CAAC;QACpCkE,IAAI,EAAEuB,CAAC;QACPJ,MAAM,EAAEA,CAAA,KAAM,IAAI,CAACmF,YAAY,KAAK,CAAC;QACrC5F,oBAAoB,EAAE,CAAC,CAAC;QACxBG,OAAO,EAAEA,CAAA,KAAM;UACb,IAAI,CAAC2F,SAAS,CAAC,IAAI,CAAC5B,WAAW,CAAC,EAAE,IAAI,CAACQ,aAAa,CAAC,CAAC;QACxD;MACF,CAAC,CACF;MACDlD,MAAM,EAAEA,CAAA,KAAM;QACZ,IAAI,CAACuE,SAAS,CAAC,IAAI,CAAC/B,UAAU,CAAC,EAAE,IAAI,CAACgC,iBAAiB,CAAC,CAAC;MAC3D,CAAC;MACDvE,OAAO,EAAEA,CAAA,KAAM;QACb,IAAI,CAACwE,WAAW,CAAC,CAAC;MACpB;IACF,CAAC,CAAC;EACJ;EACA;AACF;AACA;AACA;EACEC,mBAAmBA,CAAA,EAAG;IACpB,IAAI,CAAC3B,WAAW,CAACzB,GAAG,KAAK,IAAI,CAAC8C,YAAY,IAAI,IAAI,CAACrB,WAAW,CAACzB,GAAG,IAAI,CAAC,EAAE,IAAI,CAACqD,SAAS,CAAC,IAAI,CAAC5B,WAAW,CAAC,KAAK,IAAI,CAACpB,MAAM,CAAC,CAAC,EAAE,IAAI,CAACoB,WAAW,CAACzB,GAAG,IAAI,CAAC,EAAE,IAAI,CAACqD,SAAS,CAAC,IAAI,CAAC5B,WAAW,CAAC,EAAE,IAAI,CAAC6B,uBAAuB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAC/N;EACA;AACF;AACA;AACA;AACA;AACA;AACA;EACEC,OAAOA,CAACjL,CAAC,EAAEL,CAAC,EAAE;IACZ,OAAO,IAAI,CAAC8H,KAAK,CAACyD,gBAAgB,CAAE,IAAG3D,CAAC,CAACG,GAAI,cAAa1H,CAAE,MAAKuH,CAAC,CAACM,IAAK,EAAC,CAAC,CAAClI,CAAC,GAAG,CAAC,CAAC;EACnF;EACA;AACF;AACA;AACA;AACA;AACA;EACEwL,MAAMA,CAACnL,CAAC,EAAE;IACR,OAAO,IAAI,CAACyH,KAAK,CAAClI,aAAa,CAAE,IAAGgI,CAAC,CAACG,GAAI,cAAa1H,CAAE,GAAE,CAAC;EAC9D;EACA;AACF;AACA;AACA;AACA;AACA;EACEoL,YAAYA,CAACpL,CAAC,EAAE;IACd,OAAOA,CAAC,CAACqL,aAAa;EACxB;EACA;AACF;AACA;AACA;AACA;AACA;EACEC,eAAeA,CAACtL,CAAC,EAAE;IACjB,OAAOA,CAAC,CAACT,aAAa,CAAE,IAAGgI,CAAC,CAACM,IAAK,cAAa,CAAC;EAClD;EACA;AACF;AACA;AACA;AACA;AACA;AACA;EACE0D,cAAcA,CAACvL,CAAC,EAAEL,CAAC,EAAER,CAAC,EAAE;IACtB,MAAMkB,CAAC,GAAG,IAAI,CAAC4K,OAAO,CAACjL,CAAC,EAAEL,CAAC,CAAC;IAC5BU,CAAC,CAAC4D,SAAS,GAAG9E,CAAC;EACjB;EACA;AACF;AACA;AACA;AACA;AACA;AACA;EACE8I,SAASA,CAACjI,CAAC,GAAG,CAAC,CAAC,EAAEL,CAAC,GAAG,CAAC,CAAC,EAAE;IACxB,IAAIuC,CAAC;IACL,IAAI/C,CAAC,GAAG,IAAI,CAAC+K,eAAe;IAC5B,IAAI,IAAI,CAAC5B,MAAM,IAAI,IAAI,CAACA,MAAM,CAAC6B,OAAO,IAAI,IAAI,CAACD,eAAe,IAAI,IAAI,CAAC5B,MAAM,CAAC6B,OAAO,EACnF;IACF,KAAK,IAAIlL,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,IAAI,CAACuL,YAAY,EAAEvL,CAAC,EAAE,EAAE;MAC3C,IAAIoD,CAAC;MACL,MAAME,CAAC,GAAG,IAAI,CAACiJ,UAAU,CAAC,CAAC;MAC3B,IAAIxL,CAAC,GAAG,CAAC,IAAIA,CAAC,IAAIb,CAAC,IAAIkD,CAAC,GAAG,IAAI,CAAC4I,OAAO,CAAChM,CAAC,EAAEe,CAAC,CAAC,EAAEwC,CAAC,CAACD,CAAC,EAAEF,CAAC,CAAC,IAAIA,CAAC,GAAG,IAAI,CAAC8I,MAAM,CAAClM,CAAC,CAAC,CAACQ,WAAW,CAAC8C,CAAC,CAAC,EAAEtD,CAAC,KAAK,CAAC,EAAE;QACpG,MAAMsK,CAAC,GAAG,IAAI,CAAC0B,OAAO,CAAChM,CAAC,EAAEe,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAGb,CAAC,GAAG,CAAC,CAAC;QAC5CoK,CAAC,IAAI5J,CAAC,IAAIgD,CAAC,CAAC4G,CAAC,CAAC;MAChB;IACF;IACA,MAAMlJ,CAAC,GAAG,IAAI,CAACgD,OAAO,CAAC9D,aAAa,CAAE,IAAGgI,CAAC,CAACU,SAAU,EAAC,CAAC;IACvD,CAAC/F,CAAC,GAAG,IAAI,CAACoG,MAAM,KAAK,IAAI,IAAIpG,CAAC,CAACiI,OAAO,IAAI,IAAI,CAACD,eAAe,GAAG,IAAI,CAAC5B,MAAM,CAAC6B,OAAO,GAAG,CAAC,IAAI9J,CAAC,IAAIA,CAAC,CAACF,SAAS,CAACC,GAAG,CAACmH,CAAC,CAACW,iBAAiB,CAAC,EAAE,IAAI,CAACuD,wBAAwB,CAAC,CAAC;EACxK;EACA;AACF;AACA;AACA;AACA;AACA;AACA;EACE1D,MAAMA,CAAC/H,CAAC,GAAG,CAAC,CAAC,EAAEL,CAAC,GAAG,CAAC,CAAC,EAAE;IACrB,IAAIR,CAAC;MAAEkB,CAAC,GAAGP,CAAC,CAAC,KAAK,EAAEyH,CAAC,CAACG,GAAG,CAAC;IAC1B,IAAI,CAACsB,KAAK,CAACrB,YAAY,IAAI,IAAI,CAAC+D,6BAA6B,CAAC,CAAC;IAC/D,IAAIxJ,CAAC,GAAG,IAAI,CAACgI,eAAe;IAC5B,IAAI,IAAI,CAAC5B,MAAM,IAAI,IAAI,CAACA,MAAM,CAACmC,OAAO,IAAI,IAAI,CAACD,YAAY,IAAI,IAAI,CAAClC,MAAM,CAACmC,OAAO,IAAIpI,CAAC,EACrF;IACF,IAAIrC,CAAC,GAAG,CAAC,IAAIA,CAAC,IAAI,IAAI,CAACwK,YAAY,EAAE;MACnC,IAAIjI,CAAC,GAAG,IAAI,CAAC4I,MAAM,CAACnL,CAAC,CAAC;MACtBb,CAAC,GAAGqD,CAAC,CAACnC,CAAC,EAAEkC,CAAC,CAAC;IACb,CAAC,MACCpD,CAAC,GAAG,IAAI,CAACsI,KAAK,CAAChI,WAAW,CAACY,CAAC,CAAC;IAC/B,IAAI,CAACsL,OAAO,CAACxM,CAAC,EAAE+C,CAAC,CAAC,EAAE,IAAI,CAAC8G,KAAK,CAACrB,YAAY,IAAI,IAAI,CAAC8D,wBAAwB,CAAC,CAAC;IAC9E,MAAMxM,CAAC,GAAG,IAAI,CAACqM,eAAe,CAACnM,CAAC,CAAC;IACjCF,CAAC,IAAIU,CAAC,IAAIgD,CAAC,CAAC1D,CAAC,CAAC;IACd,MAAMoD,CAAC,GAAG,IAAI,CAACgB,OAAO,CAAC9D,aAAa,CAAE,IAAGgI,CAAC,CAACQ,MAAO,EAAC,CAAC;IACpD,OAAO,IAAI,CAACO,MAAM,IAAI,IAAI,CAACA,MAAM,CAACmC,OAAO,IAAI,IAAI,CAACD,YAAY,IAAI,IAAI,CAAClC,MAAM,CAACmC,OAAO,IAAIpI,CAAC,IAAIA,CAAC,CAAClC,SAAS,CAACC,GAAG,CAACmH,CAAC,CAACS,cAAc,CAAC,EAAE7I,CAAC;EACpI;EACA;AACF;AACA;AACA;AACA;EACEiL,YAAYA,CAACpK,CAAC,EAAE;IACd,KAAK,IAAIb,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,IAAI,CAACqL,YAAY,EAAErL,CAAC,EAAE,EAAE;MAC3C,MAAMkB,CAAC,GAAG,IAAI,CAAC4K,OAAO,CAAC9L,CAAC,EAAEa,CAAC,CAAC;MAC5B,IAAI,CAACK,CAAC,EACJ;MACFA,CAAC,CAAC4E,MAAM,CAAC,CAAC;IACZ;IACA,MAAMtF,CAAC,GAAG,IAAI,CAAC0D,OAAO,CAAC9D,aAAa,CAAE,IAAGgI,CAAC,CAACU,SAAU,EAAC,CAAC;IACvDtI,CAAC,IAAIA,CAAC,CAACQ,SAAS,CAAC8E,MAAM,CAACsC,CAAC,CAACW,iBAAiB,CAAC;EAC9C;EACA;AACF;AACA;AACA;AACA;EACEwC,SAASA,CAAC1K,CAAC,EAAE;IACX,IAAI,CAACmL,MAAM,CAACnL,CAAC,CAAC,CAACiF,MAAM,CAAC,CAAC;IACvB,MAAMtF,CAAC,GAAG,IAAI,CAAC0D,OAAO,CAAC9D,aAAa,CAAE,IAAGgI,CAAC,CAACQ,MAAO,EAAC,CAAC;IACpDpI,CAAC,IAAIA,CAAC,CAACQ,SAAS,CAAC8E,MAAM,CAACsC,CAAC,CAACS,cAAc,CAAC,EAAE,IAAI,CAACyD,wBAAwB,CAAC,CAAC;EAC5E;EACA;AACF;AACA;AACA;AACA;AACA;EACE9C,kBAAkBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACtF,OAAO,GAAGvD,CAAC,CAAC,KAAK,EAAEyH,CAAC,CAAClE,OAAO,CAAC,EAAE,IAAI,CAACoE,KAAK,GAAG3H,CAAC,CAAC,KAAK,EAAEyH,CAAC,CAACE,KAAK,CAAC,EAAE,IAAI,CAACW,QAAQ,IAAI,IAAI,CAAC/E,OAAO,CAAClD,SAAS,CAACC,GAAG,CAACmH,CAAC,CAACC,eAAe,CAAC,EAAE,IAAI,CAACnE,OAAO,CAAC5D,WAAW,CAAC,IAAI,CAACgJ,UAAU,CAAC9B,OAAO,CAAC,EAAE,IAAI,CAACtD,OAAO,CAAC5D,WAAW,CAAC,IAAI,CAAC8I,aAAa,CAAC5B,OAAO,CAAC,EAAE,IAAI,CAACtD,OAAO,CAAC5D,WAAW,CAAC,IAAI,CAACgI,KAAK,CAAC,EAAE,CAAC,IAAI,CAACW,QAAQ,EAAE;MACrS,MAAMpI,CAAC,GAAGF,CAAC,CAAC,KAAK,EAAEyH,CAAC,CAACU,SAAS,EAAE;UAC9BhE,SAAS,EAAE4B;QACb,CAAC,CAAC;QAAElG,CAAC,GAAGG,CAAC,CAAC,KAAK,EAAEyH,CAAC,CAACQ,MAAM,EAAE;UACzB9D,SAAS,EAAE4B;QACb,CAAC,CAAC;MACF,IAAI,CAACxC,OAAO,CAAC5D,WAAW,CAACO,CAAC,CAAC,EAAE,IAAI,CAACqD,OAAO,CAAC5D,WAAW,CAACE,CAAC,CAAC;IAC1D;EACF;EACA;AACF;AACA;AACA;AACA;EACEiM,kBAAkBA,CAAA,EAAG;IACnB,MAAM5L,CAAC,GAAG,IAAI,CAACqI,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC7I,OAAO;MAAEG,CAAC,GAAGM,KAAK,CAACC,OAAO,CAACF,CAAC,CAAC;MAAEb,CAAC,GAAGQ,CAAC,GAAGK,CAAC,CAAC6L,MAAM,GAAG,CAAC,CAAC;MAAExL,CAAC,GAAGV,CAAC,GAAGK,CAAC,CAAC6L,MAAM,GAAG,KAAK,CAAC;MAAE3J,CAAC,GAAG/C,CAAC,GAAGa,CAAC,CAAC,CAAC,CAAC,CAAC6L,MAAM,GAAG,KAAK,CAAC;MAAE5M,CAAC,GAAG6M,MAAM,CAACC,QAAQ,CAAC,IAAI,CAACzD,MAAM,IAAI,IAAI,CAACA,MAAM,CAAC0D,IAAI,CAAC;MAAE3J,CAAC,GAAGyJ,MAAM,CAACC,QAAQ,CAAC,IAAI,CAACzD,MAAM,IAAI,IAAI,CAACA,MAAM,CAAC2D,IAAI,CAAC;MAAE1J,CAAC,GAAG,CAAC2J,KAAK,CAACjN,CAAC,CAAC,IAAIA,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAG,KAAK,CAAC;MAAEsK,CAAC,GAAG,CAAC2C,KAAK,CAAC7J,CAAC,CAAC,IAAIA,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAG,KAAK,CAAC;IACpU,OAAO;MACL2J,IAAI,EAAE3L,CAAC,IAAIkC,CAAC,IAAI,CAAC;MACjB0J,IAAI,EAAE/J,CAAC,IAAIqH,CAAC,IAAI;IAClB,CAAC;EACH;EACA;AACF;AACA;AACA;AACA;EACEN,MAAMA,CAAA,EAAG;IACP,MAAM;MAAE+C,IAAI,EAAEhM,CAAC;MAAEiM,IAAI,EAAEtM;IAAE,CAAC,GAAG,IAAI,CAACiM,kBAAkB,CAAC,CAAC;IACtD,KAAK,IAAIzM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGa,CAAC,EAAEb,CAAC,EAAE,EACxB,IAAI,CAAC4I,MAAM,CAAC,CAAC;IACf,KAAK,IAAI5I,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGQ,CAAC,EAAER,CAAC,EAAE,EACxB,IAAI,CAAC8I,SAAS,CAAC,CAAC;EACpB;EACA;AACF;AACA;AACA;AACA;EACEiB,IAAIA,CAAA,EAAG;IACL,MAAMlJ,CAAC,GAAG,IAAI,CAACqI,IAAI;IACnB,IAAIrI,CAAC,IAAIA,CAAC,CAACR,OAAO,EAChB,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGK,CAAC,CAACR,OAAO,CAACqM,MAAM,EAAElM,CAAC,EAAE,EACvC,KAAK,IAAIR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGa,CAAC,CAACR,OAAO,CAACG,CAAC,CAAC,CAACkM,MAAM,EAAE1M,CAAC,EAAE,EAC1C,IAAI,CAACoM,cAAc,CAAC5L,CAAC,GAAG,CAAC,EAAER,CAAC,GAAG,CAAC,EAAEa,CAAC,CAACR,OAAO,CAACG,CAAC,CAAC,CAACR,CAAC,CAAC,CAAC;EAC1D;EACA;AACF;AACA;AACA;AACA;AACA;EACEwM,OAAOA,CAAC3L,CAAC,EAAEL,CAAC,EAAE;IACZ,KAAK,IAAIR,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIQ,CAAC,EAAER,CAAC,EAAE,EAAE;MAC3B,MAAMkB,CAAC,GAAG,IAAI,CAACmL,UAAU,CAAC,CAAC;MAC3BxL,CAAC,CAACP,WAAW,CAACY,CAAC,CAAC;IAClB;EACF;EACA;AACF;AACA;AACA;AACA;EACEmL,UAAUA,CAAA,EAAG;IACX,OAAO1L,CAAC,CAAC,KAAK,EAAEyH,CAAC,CAACM,IAAI,EAAE;MACtBsE,eAAe,EAAE,CAAC,IAAI,CAAC/D;IACzB,CAAC,CAAC;EACJ;EACA;AACF;AACA;EACE,IAAIoC,YAAYA,CAAA,EAAG;IACjB,OAAO,IAAI,CAAC/C,KAAK,CAAC2E,iBAAiB;EACrC;EACA;AACF;AACA;EACE,IAAIlC,eAAeA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACM,YAAY,GAAG,IAAI,CAAC/C,KAAK,CAACyD,gBAAgB,CAAE,IAAG3D,CAAC,CAACG,GAAI,iBAAgBH,CAAC,CAACM,IAAK,EAAC,CAAC,CAACgE,MAAM,GAAG,CAAC;EACvG;EACA;AACF;AACA;AACA;AACA;EACE,IAAIQ,mBAAmBA,CAAA,EAAG;IACxB,OAAO,IAAI,CAACtD,cAAc,KAAK,CAAC;EAClC;EACA;AACF;AACA;AACA;AACA;EACE,IAAIuD,gBAAgBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACxD,WAAW,KAAK,CAAC;EAC/B;EACA;AACF;AACA;AACA;AACA;EACEa,kBAAkBA,CAAC3J,CAAC,EAAE;IACpB,MAAM;MAAE0H,GAAG,EAAE/H,CAAC;MAAEyJ,MAAM,EAAEjK;IAAE,CAAC,GAAG,IAAI,CAACoN,cAAc,CAACvM,CAAC,CAAC;IACpD,IAAI,CAAC6I,aAAa,GAAG1J,CAAC,EAAE,IAAI,CAACyJ,UAAU,GAAGjJ,CAAC,EAAE,IAAI,CAACqL,uBAAuB,CAAC,CAAC;EAC7E;EACA;AACF;AACA;AACA;AACA;AACA;EACElB,kBAAkBA,CAAC9J,CAAC,EAAE;IACpB,IAAIA,CAAC,CAACwM,GAAG,KAAK,OAAO,EAAE;MACrB,IAAIxM,CAAC,CAACyM,QAAQ,EACZ,OAAO,CAAC,CAAC;MACX,IAAI,CAAC3B,mBAAmB,CAAC,CAAC;IAC5B;IACA,OAAO9K,CAAC,CAACwM,GAAG,KAAK,OAAO;EAC1B;EACA;AACF;AACA;AACA;AACA;AACA;EACEzC,iBAAiBA,CAAC/J,CAAC,EAAE;IACnBA,CAAC,CAACwM,GAAG,KAAK,KAAK,IAAIxM,CAAC,CAAC0M,eAAe,CAAC,CAAC;EACxC;EACA;AACF;AACA;AACA;AACA;EACE1C,oBAAoBA,CAAChK,CAAC,EAAE;IACtB,MAAML,CAAC,GAAGK,CAAC,CAAC0E,MAAM;MAAEvF,CAAC,GAAG,IAAI,CAACiM,YAAY,CAACzL,CAAC,CAAC;IAC5C,IAAI,CAACwJ,WAAW,GAAG;MACjBzB,GAAG,EAAEzH,KAAK,CAAC0M,IAAI,CAAC,IAAI,CAAClF,KAAK,CAACyD,gBAAgB,CAAE,IAAG3D,CAAC,CAACG,GAAI,EAAC,CAAC,CAAC,CAACkF,OAAO,CAACzN,CAAC,CAAC,GAAG,CAAC;MACxEiK,MAAM,EAAEnJ,KAAK,CAAC0M,IAAI,CAACxN,CAAC,CAAC+L,gBAAgB,CAAE,IAAG3D,CAAC,CAACM,IAAK,EAAC,CAAC,CAAC,CAAC+E,OAAO,CAACjN,CAAC,CAAC,GAAG;IACpE,CAAC;EACH;EACA;AACF;AACA;AACA;AACA;AACA;AACA;EACE2J,aAAaA,CAAA,EAAG;IACd,IAAI,CAACgB,cAAc,CAAC,CAAC,EAAE,IAAI,CAACM,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAACI,uBAAuB,CAAC,CAAC;EACjF;EACA;AACF;AACA;AACA;AACA;EACEV,cAAcA,CAAA,EAAG;IACf,IAAI,CAACO,WAAW,CAAC,CAAC,EAAE,IAAI,CAACpC,UAAU,CAACtB,IAAI,CAAC,CAAC;EAC5C;EACA;AACF;AACA;AACA;AACA;EACEyD,iBAAiBA,CAAA,EAAG;IAClB,IAAI,CAACL,cAAc,CAAC,CAAC,EAAE,IAAI,CAAChC,aAAa,CAACpB,IAAI,CAAC,CAAC;EAClD;EACA;AACF;AACA;AACA;AACA;EACE4D,SAASA,CAAA,EAAG;IACV,IAAI,CAAC8B,eAAe,CAACC,KAAK,CAAC,CAAC;EAC9B;EACA;AACF;AACA;AACA;AACA;EACE,IAAID,eAAeA,CAAA,EAAG;IACpB,MAAM;MAAEnF,GAAG,EAAE1H,CAAC;MAAEoJ,MAAM,EAAEzJ;IAAE,CAAC,GAAG,IAAI,CAACwJ,WAAW;IAC9C,OAAO,IAAI,CAAC8B,OAAO,CAACjL,CAAC,EAAEL,CAAC,CAAC;EAC3B;EACA;AACF;AACA;AACA;AACA;AACA;EACEqL,uBAAuBA,CAAChL,CAAC,GAAG,IAAI,CAAC4I,UAAU,EAAEjJ,CAAC,GAAG,IAAI,CAACkJ,aAAa,EAAE;IACnE,IAAI,CAACwD,mBAAmB,IAAI1M,CAAC,GAAG,CAAC,IAAIA,CAAC,IAAI,IAAI,CAACuK,eAAe,IAAI,IAAI,CAAC3B,aAAa,CAACvB,IAAI,CAAC,OAAO;MAC/F7F,IAAI,EAAG,qCAAoC,IAAI,CAAC+I,eAAgB,iBAAgBvK,CAAE;IACpF,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC2M,gBAAgB,IAAItM,CAAC,GAAG,CAAC,IAAIA,CAAC,IAAI,IAAI,CAACwK,YAAY,IAAI,IAAI,CAAC/B,UAAU,CAACzB,IAAI,CAAC,MAAM;MAC1F,MAAM7H,CAAC,GAAG,IAAI,CAACgM,MAAM,CAACnL,CAAC,CAAC;QAAE;UAAE0B,aAAa,EAAErB;QAAE,CAAC,GAAGoB,CAAC,CAAC,IAAI,CAACgG,KAAK,EAAEtI,CAAC,CAAC;QAAE;UAAE6C,MAAM,EAAEE;QAAE,CAAC,GAAG/C,CAAC,CAACwB,qBAAqB,CAAC,CAAC;MAC5G,OAAO;QACLI,GAAG,EAAG,GAAEF,IAAI,CAACkM,IAAI,CAAC1M,CAAC,GAAG6B,CAAC,GAAG,CAAC,CAAE;MAC/B,CAAC;IACH,CAAC,CAAC;EACJ;EACA;AACF;AACA;AACA;AACA;EACE8K,kBAAkBA,CAAChN,CAAC,EAAE;IACpB,IAAI,CAACgJ,KAAK,CAACrB,YAAY,GAAG3H,CAAC,EAAEA,CAAC,IAAI,IAAI,CAACyH,KAAK,CAACtH,SAAS,CAACC,GAAG,CAACmH,CAAC,CAACI,YAAY,CAAC,EAAE,IAAI,CAAC8D,wBAAwB,CAAC,CAAC,KAAK,IAAI,CAAChE,KAAK,CAACtH,SAAS,CAAC8E,MAAM,CAACsC,CAAC,CAACI,YAAY,CAAC,EAAE,IAAI,CAAC+D,6BAA6B,CAAC,CAAC,CAAC;EACpM;EACA;AACF;AACA;EACED,wBAAwBA,CAAA,EAAG;IACzB,KAAK,IAAIzL,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,IAAI,CAACkK,eAAe,EAAElK,CAAC,EAAE,EAAE;MAC9C,IAAIL,CAAC,GAAG,IAAI,CAACsL,OAAO,CAAC,CAAC,EAAEjL,CAAC,CAAC;MAC1BL,CAAC,IAAIA,CAAC,CAACsN,YAAY,CAAC,SAAS,EAAE,IAAI,CAAC9G,GAAG,CAAC8D,IAAI,CAACjK,CAAC,CAAC,SAAS,CAAC,CAAC;IAC5D;EACF;EACA;AACF;AACA;EACE0L,6BAA6BA,CAAA,EAAG;IAC9B,KAAK,IAAI1L,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,IAAI,CAACkK,eAAe,EAAElK,CAAC,EAAE,EAAE;MAC9C,IAAIL,CAAC,GAAG,IAAI,CAACsL,OAAO,CAAC,CAAC,EAAEjL,CAAC,CAAC;MAC1BL,CAAC,IAAIA,CAAC,CAACuN,eAAe,CAAC,SAAS,CAAC;IACnC;EACF;EACA;AACF;AACA;AACA;AACA;EACEvC,SAASA,CAAC3K,CAAC,EAAE;IACX,MAAML,CAAC,GAAG,IAAI,CAACwL,MAAM,CAACnL,CAAC,CAAC;IACxBL,CAAC,KAAK,IAAI,CAACmJ,WAAW,GAAG9I,CAAC,EAAEL,CAAC,CAACQ,SAAS,CAACC,GAAG,CAACmH,CAAC,CAACK,WAAW,CAAC,CAAC;EAC7D;EACA;AACF;AACA;EACEiD,WAAWA,CAAA,EAAG;IACZ,IAAI,IAAI,CAAC/B,WAAW,IAAI,CAAC,EACvB;IACF,MAAM9I,CAAC,GAAG,IAAI,CAACyH,KAAK,CAAClI,aAAa,CAAE,IAAGgI,CAAC,CAACK,WAAY,EAAC,CAAC;IACvD5H,CAAC,IAAIA,CAAC,CAACG,SAAS,CAAC8E,MAAM,CAACsC,CAAC,CAACK,WAAW,CAAC,EAAE,IAAI,CAACkB,WAAW,GAAG,CAAC;EAC9D;EACA;AACF;AACA;AACA;AACA;EACEuB,YAAYA,CAACrK,CAAC,EAAE;IACd,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,IAAI,CAAC6K,YAAY,EAAE7K,CAAC,EAAE,EAAE;MAC3C,MAAMR,CAAC,GAAG,IAAI,CAAC8L,OAAO,CAACtL,CAAC,EAAEK,CAAC,CAAC;MAC5Bb,CAAC,IAAIA,CAAC,CAACgB,SAAS,CAACC,GAAG,CAACmH,CAAC,CAACO,YAAY,CAAC;IACtC;IACA,IAAI,CAACiB,cAAc,GAAG/I,CAAC;EACzB;EACA;AACF;AACA;EACEuK,cAAcA,CAAA,EAAG;IACf,IAAI,IAAI,CAACxB,cAAc,IAAI,CAAC,EAC1B;IACF,IAAI/I,CAAC,GAAG,IAAI,CAACyH,KAAK,CAACyD,gBAAgB,CAAE,IAAG3D,CAAC,CAACO,YAAa,EAAC,CAAC;IACzD7H,KAAK,CAAC0M,IAAI,CAAC3M,CAAC,CAAC,CAACgE,OAAO,CAAErE,CAAC,IAAK;MAC3BA,CAAC,CAACQ,SAAS,CAAC8E,MAAM,CAACsC,CAAC,CAACO,YAAY,CAAC;IACpC,CAAC,CAAC,EAAE,IAAI,CAACiB,cAAc,GAAG,CAAC;EAC7B;EACA;AACF;AACA;AACA;AACA;AACA;AACA;EACEwD,cAAcA,CAACvM,CAAC,EAAE;IAChB,IAAIL,CAAC,GAAG,IAAI,CAACiJ,UAAU;MAAEzJ,CAAC,GAAG,IAAI,CAAC0J,aAAa;IAC/C,MAAM;MAAE9G,KAAK,EAAE1B,CAAC;MAAE2B,MAAM,EAAEE,CAAC;MAAED,CAAC,EAAEhD,CAAC;MAAEkD,CAAC,EAAEE;IAAE,CAAC,GAAGP,CAAC,CAAC,IAAI,CAAC2F,KAAK,EAAEzH,CAAC,CAAC;IAC5D,OAAOf,CAAC,IAAI,CAAC,KAAKE,CAAC,GAAG,IAAI,CAACgO,SAAS,CAClC,IAAI,CAACjD,eAAe,EACnB3H,CAAC,IAAK,IAAI,CAAC0I,OAAO,CAAC,CAAC,EAAE1I,CAAC,CAAC,EACzB,CAAC;MAAEZ,cAAc,EAAEY;IAAE,CAAC,KAAKtD,CAAC,GAAGsD,CAAC,EAChC,CAAC;MAAEX,eAAe,EAAEW;IAAE,CAAC,KAAKtD,CAAC,GAAGoB,CAAC,GAAGkC,CACtC,CAAC,CAAC,EAAEF,CAAC,IAAI,CAAC,KAAK1C,CAAC,GAAG,IAAI,CAACwN,SAAS,CAC/B,IAAI,CAAC3C,YAAY,EAChBjI,CAAC,IAAK,IAAI,CAAC0I,OAAO,CAAC1I,CAAC,EAAE,CAAC,CAAC,EACzB,CAAC;MAAEb,aAAa,EAAEa;IAAE,CAAC,KAAKF,CAAC,GAAGE,CAAC,EAC/B,CAAC;MAAEV,gBAAgB,EAAEU;IAAE,CAAC,KAAKF,CAAC,GAAGH,CAAC,GAAGK,CACvC,CAAC,CAAC,EAAE;MACFmF,GAAG,EAAE/H,CAAC,IAAI,IAAI,CAACiJ,UAAU;MACzBQ,MAAM,EAAEjK,CAAC,IAAI,IAAI,CAAC0J;IACpB,CAAC;EACH;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEsE,SAASA,CAACnN,CAAC,EAAEL,CAAC,EAAER,CAAC,EAAEkB,CAAC,EAAE;IACpB,IAAI6B,CAAC,GAAG,CAAC;MAAEjD,CAAC,GAAGe,CAAC,GAAG,CAAC;MAAEqC,CAAC,GAAG,CAAC;MAAEE,CAAC;IAC9B,OAAOL,CAAC,GAAGjD,CAAC,GAAG,CAAC,IAAIoD,CAAC,GAAG,EAAE,GAAI;MAC5BE,CAAC,GAAG1B,IAAI,CAACkM,IAAI,CAAC,CAAC7K,CAAC,GAAGjD,CAAC,IAAI,CAAC,CAAC;MAC1B,MAAMsK,CAAC,GAAG5J,CAAC,CAAC4C,CAAC,CAAC;QAAEiH,CAAC,GAAG/H,CAAC,CAAC,IAAI,CAACgG,KAAK,EAAE8B,CAAC,CAAC;MACpC,IAAIpK,CAAC,CAACqK,CAAC,CAAC,EACNvK,CAAC,GAAGsD,CAAC,CAAC,KACH,IAAIlC,CAAC,CAACmJ,CAAC,CAAC,EACXtH,CAAC,GAAGK,CAAC,CAAC,KAEN;MACFF,CAAC,EAAE;IACL;IACA,OAAOE,CAAC;EACV;EACA;AACF;AACA;AACA;AACA;EACE6K,OAAOA,CAAA,EAAG;IACR,MAAMpN,CAAC,GAAG,EAAE;IACZ,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,IAAI,CAAC6K,YAAY,EAAE7K,CAAC,EAAE,EAAE;MAC3C,MAAMR,CAAC,GAAG,IAAI,CAACsI,KAAK,CAAClI,aAAa,CAAE,IAAGgI,CAAC,CAACG,GAAI,cAAa/H,CAAE,GAAE,CAAC;QAAEU,CAAC,GAAGJ,KAAK,CAAC0M,IAAI,CAACxN,CAAC,CAAC+L,gBAAgB,CAAE,IAAG3D,CAAC,CAACM,IAAK,EAAC,CAAC,CAAC;MACjHxH,CAAC,CAACgN,KAAK,CAAEpO,CAAC,IAAK,CAACA,CAAC,CAACkF,WAAW,CAACmJ,IAAI,CAAC,CAAC,CAAC,IAAItN,CAAC,CAACuE,IAAI,CAAClE,CAAC,CAACkN,GAAG,CAAEtO,CAAC,IAAKA,CAAC,CAACgF,SAAS,CAAC,CAAC;IAC5E;IACA,OAAOjE,CAAC;EACV;EACA;AACF;AACA;EACEwN,OAAOA,CAAA,EAAG;IACRtO,QAAQ,CAACuO,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACpE,eAAe,CAAC;EAC7D;AACF;AACA,MAAMqE,CAAC,CAAC;EACN;AACF;AACA;AACA;AACA;EACE,WAAWC,mBAAmBA,CAAA,EAAG;IAC/B,OAAO,CAAC,CAAC;EACX;EACA;AACF;AACA;AACA;AACA;AACA;EACE,WAAWC,gBAAgBA,CAAA,EAAG;IAC5B,OAAO,CAAC,CAAC;EACX;EACA;AACF;AACA;AACA;AACA;EACEzK,WAAWA,CAAC;IAAEkF,IAAI,EAAErI,CAAC;IAAEsI,MAAM,EAAE3I,CAAC;IAAEwG,GAAG,EAAEhH,CAAC;IAAEiJ,QAAQ,EAAE/H,CAAC;IAAEwN,KAAK,EAAE3L;EAAE,CAAC,EAAE;IACjE,IAAI,CAACiE,GAAG,GAAGhH,CAAC,EAAE,IAAI,CAACiJ,QAAQ,GAAG/H,CAAC,EAAE,IAAI,CAACiI,MAAM,GAAG3I,CAAC,EAAE,IAAI,CAAC0I,IAAI,GAAG;MAC5DV,YAAY,EAAE,IAAI,CAACmG,SAAS,CAAC,cAAc,EAAE,CAAC,CAAC,EAAE9N,CAAC,CAAC;MACnD+N,SAAS,EAAE,IAAI,CAACD,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE9N,CAAC,CAAC;MAC7CR,OAAO,EAAEQ,CAAC,IAAIA,CAAC,CAACR,OAAO,GAAGQ,CAAC,CAACR,OAAO,GAAG;IACxC,CAAC,EAAE,IAAI,CAACiI,KAAK,GAAG,IAAI,EAAE,IAAI,CAACoG,KAAK,GAAG3L,CAAC;EACtC;EACA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,WAAWsE,OAAOA,CAAA,EAAG;IACnB,OAAO;MACLtC,IAAI,EAAE+B,CAAC;MACP+H,KAAK,EAAE;IACT,CAAC;EACH;EACA;AACF;AACA;AACA;AACA;EACEjK,MAAMA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC0D,KAAK,GAAG,IAAIU,CAAC,CAAC,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACjC,GAAG,EAAE,IAAI,CAACkC,IAAI,EAAE,IAAI,CAACC,MAAM,CAAC,EAAE,IAAI,CAAC2F,SAAS,GAAGnO,CAAC,CAAC,KAAK,EAAE,IAAI,CAACqG,GAAG,CAAC+H,MAAM,CAACL,KAAK,CAAC,EAAE,IAAI,CAACI,SAAS,CAACxO,WAAW,CAAC,IAAI,CAACgI,KAAK,CAACiC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAACjC,KAAK,CAACuF,kBAAkB,CAAC,IAAI,CAAC3E,IAAI,CAACV,YAAY,CAAC,EAAE,IAAI,CAACsG,SAAS;EAC1P;EACA;AACF;AACA;AACA;AACA;EACEE,cAAcA,CAAA,EAAG;IACf,OAAO,CACL;MACE/J,KAAK,EAAE,IAAI,CAAC+B,GAAG,CAAC8D,IAAI,CAACjK,CAAC,CAAC,eAAe,CAAC;MACvCkE,IAAI,EAAE6B,CAAC;MACPqI,QAAQ,EAAE,IAAI,CAAC/F,IAAI,CAACV,YAAY;MAChC0G,eAAe,EAAE,CAAC,CAAC;MACnB/I,MAAM,EAAE,CAAC,CAAC;MACVgJ,UAAU,EAAEA,CAAA,KAAM;QAChB,IAAI,CAACjG,IAAI,CAACV,YAAY,GAAG,CAAC,CAAC,EAAE,IAAI,CAACF,KAAK,CAACuF,kBAAkB,CAAC,IAAI,CAAC3E,IAAI,CAACV,YAAY,CAAC;MACpF;IACF,CAAC,EACD;MACEvD,KAAK,EAAE,IAAI,CAAC+B,GAAG,CAAC8D,IAAI,CAACjK,CAAC,CAAC,kBAAkB,CAAC;MAC1CkE,IAAI,EAAE8B,CAAC;MACPoI,QAAQ,EAAE,CAAC,IAAI,CAAC/F,IAAI,CAACV,YAAY;MACjC0G,eAAe,EAAE,CAAC,CAAC;MACnB/I,MAAM,EAAE,CAAC,CAAC;MACVgJ,UAAU,EAAEA,CAAA,KAAM;QAChB,IAAI,CAACjG,IAAI,CAACV,YAAY,GAAG,CAAC,CAAC,EAAE,IAAI,CAACF,KAAK,CAACuF,kBAAkB,CAAC,IAAI,CAAC3E,IAAI,CAACV,YAAY,CAAC;MACpF;IACF,CAAC,EACD;MACEvD,KAAK,EAAE,IAAI,CAACiE,IAAI,CAAC0F,SAAS,GAAG,IAAI,CAAC5H,GAAG,CAAC8D,IAAI,CAACjK,CAAC,CAAC,UAAU,CAAC,GAAG,IAAI,CAACmG,GAAG,CAAC8D,IAAI,CAACjK,CAAC,CAAC,SAAS,CAAC;MACrFkE,IAAI,EAAE,IAAI,CAACmE,IAAI,CAAC0F,SAAS,GAAGvI,CAAC,GAAGM,CAAC;MACjCuI,eAAe,EAAE,CAAC,CAAC;MACnB/I,MAAM,EAAE,CAAC,CAAC;MACVgJ,UAAU,EAAEA,CAAA,KAAM;QAChB,IAAI,CAACjG,IAAI,CAAC0F,SAAS,GAAG,CAAC,IAAI,CAAC1F,IAAI,CAAC0F,SAAS,EAAE,IAAI,CAACF,KAAK,CAACE,SAAS,GAAG,IAAI,CAAC1F,IAAI,CAAC0F,SAAS;MACxF;IACF,CAAC,CACF;EACH;EACA;AACF;AACA;AACA;AACA;EACEQ,IAAIA,CAAA,EAAG;IACL,MAAMvO,CAAC,GAAG,IAAI,CAACyH,KAAK,CAAC2F,OAAO,CAAC,CAAC;IAC9B,OAAO;MACLzF,YAAY,EAAE,IAAI,CAACU,IAAI,CAACV,YAAY;MACpCoG,SAAS,EAAE,IAAI,CAAC1F,IAAI,CAAC0F,SAAS;MAC9BvO,OAAO,EAAEQ;IACX,CAAC;EACH;EACA;AACF;AACA;AACA;AACA;EACEwN,OAAOA,CAAA,EAAG;IACR,IAAI,CAAC/F,KAAK,CAAC+F,OAAO,CAAC,CAAC;EACtB;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEM,SAASA,CAAC9N,CAAC,EAAEL,CAAC,GAAG,KAAK,CAAC,EAAER,CAAC,GAAG,KAAK,CAAC,EAAE;IACnC,MAAMkB,CAAC,GAAG,IAAI,CAACgI,IAAI,IAAIlJ,CAAC;IACxB,OAAOkB,CAAC,GAAGA,CAAC,CAACL,CAAC,CAAC,GAAGK,CAAC,CAACL,CAAC,CAAC,GAAGL,CAAC,GAAG,IAAI,CAAC2I,MAAM,IAAI,IAAI,CAACA,MAAM,CAACtI,CAAC,CAAC,GAAG,IAAI,CAACsI,MAAM,CAACtI,CAAC,CAAC,GAAGL,CAAC;EACjF;EACA;AACF;AACA;AACA;AACA;EACE,WAAW6O,WAAWA,CAAA,EAAG;IACvB,OAAO;MAAEC,IAAI,EAAE,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;IAAE,CAAC;EAC9C;EACA;AACF;AACA;AACA;AACA;EACEC,OAAOA,CAAC1O,CAAC,EAAE;IACT,MAAML,CAAC,GAAGK,CAAC,CAAC2O,MAAM,CAACtG,IAAI;MAAElJ,CAAC,GAAGQ,CAAC,CAACJ,aAAa,CAAC,qCAAqC,CAAC;MAAE2C,CAAC,GAAGjC,KAAK,CAAC0M,IAAI,CAAChN,CAAC,CAACuL,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAACqC,GAAG,CAAEtO,CAAC,IAAKgB,KAAK,CAAC0M,IAAI,CAAC1N,CAAC,CAACiM,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAACqC,GAAG,CAAEhL,CAAC,IAAKA,CAAC,CAAC0B,SAAS,CAAC,CAAC;IAC1M,IAAI,CAACoE,IAAI,GAAG;MACVV,YAAY,EAAExI,CAAC,KAAK,IAAI;MACxBK,OAAO,EAAE0C;IACX,CAAC,EAAE,IAAI,CAACuF,KAAK,CAACpE,OAAO,IAAI,IAAI,CAACoE,KAAK,CAACpE,OAAO,CAACuL,WAAW,CAAC,IAAI,CAAC7K,MAAM,CAAC,CAAC,CAAC;EACxE;AACF;AACA,SACE2J,CAAC,IAAImB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}