{"ast": null, "code": "import { combineLatest } from './combineLatest';\nexport function combineLatestWith(...otherSources) {\n  return combineLatest(...otherSources);\n}", "map": {"version": 3, "names": ["combineLatest", "combineLatestWith", "otherSources"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/rxjs/dist/esm/internal/operators/combineLatestWith.js"], "sourcesContent": ["import { combineLatest } from './combineLatest';\nexport function combineLatestWith(...otherSources) {\n    return combineLatest(...otherSources);\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,iBAAiB;AAC/C,OAAO,SAASC,iBAAiBA,CAAC,GAAGC,YAAY,EAAE;EAC/C,OAAOF,aAAa,CAAC,GAAGE,YAAY,CAAC;AACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}