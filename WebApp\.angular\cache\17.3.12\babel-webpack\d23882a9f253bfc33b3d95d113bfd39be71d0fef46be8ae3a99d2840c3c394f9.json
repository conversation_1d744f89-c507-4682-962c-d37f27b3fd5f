{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/source/ai-hub/WebApp/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./agent-chat.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./agent-chat.component.css?ngResource\";\nimport { Component, ViewChild, inject, HostListener, Pipe } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { DomSanitizer } from '@angular/platform-browser';\nimport { marked } from 'marked';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { AgentDefinitionServiceProxy, WorkspaceServiceProxy, AgentChatServiceProxy, AgentChatRequestDto, AgentChatHistoryDto, AgentChatConversationDto, AgentChatResponseDto } from '../../../shared/service-proxies/service-proxies';\nimport { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';\nimport { AuthService } from '../../../shared/services/auth.service';\nimport { ChatService } from '../../services/chat.service';\nimport { NzDrawerModule } from 'ng-zorro-antd/drawer';\nimport { NzSelectModule } from 'ng-zorro-antd/select';\nimport { NzBadgeModule } from 'ng-zorro-antd/badge';\nimport { NzToolTipModule } from 'ng-zorro-antd/tooltip';\nimport { NzModalModule, NzModalService } from 'ng-zorro-antd/modal';\nimport { NzMessageService } from 'ng-zorro-antd/message';\nimport { MarkdownModule } from 'ngx-markdown';\nimport { SourceReferencesComponent } from '../../components/@rightSideComponents/source-references/source-references.component';\nimport { AgentSidebarComponent } from '../../components/@rightSideComponents/agent-sidebar/agent-sidebar.component';\nimport { AngularSplitModule } from 'angular-split';\nimport { ChatListService } from '../../services/chat-list.service';\nimport { DateTime } from 'luxon';\n// Simple RelativeTime pipe for timestamp display\nlet RelativeTimePipe = class RelativeTimePipe {\n  transform(value) {\n    if (!value) return '';\n    const date = new Date(value);\n    const now = new Date();\n    const diffInMs = now.getTime() - date.getTime();\n    const diffInMinutes = Math.floor(diffInMs / (1000 * 60));\n    const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));\n    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));\n    if (diffInMinutes < 1) {\n      return Promise.resolve('just now');\n    } else if (diffInMinutes < 60) {\n      return Promise.resolve(`${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`);\n    } else if (diffInHours < 24) {\n      return Promise.resolve(`${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`);\n    } else if (diffInDays < 7) {\n      return Promise.resolve(`${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`);\n    } else {\n      return Promise.resolve(date.toLocaleDateString());\n    }\n  }\n};\nRelativeTimePipe = __decorate([Pipe({\n  name: 'relativeTime',\n  standalone: true\n})], RelativeTimePipe);\nexport { RelativeTimePipe };\nlet AgentChatComponent = class AgentChatComponent {\n  constructor(router, authService, agentDefinition, sanitizer, chatService, auth, message, workspaceService, modal, agentChatService) {\n    this.router = router;\n    this.authService = authService;\n    this.agentDefinition = agentDefinition;\n    this.sanitizer = sanitizer;\n    this.chatService = chatService;\n    this.auth = auth;\n    this.message = message;\n    this.workspaceService = workspaceService;\n    this.modal = modal;\n    this.agentChatService = agentChatService;\n    // User message input\n    this.userInput = new AgentChatRequestDto();\n    // Chat data\n    this.conversations = [];\n    this.currentConversation = new AgentChatConversationDto({\n      agentName: '',\n      histories: []\n    });\n    this.isMessageLoading = false;\n    this.previousResponse = '';\n    // Search results and source references\n    this.searchResults = [];\n    this.currentSourceName = '';\n    // Response navigation tracking\n    this.currentResponseIndexes = {};\n    // Streaming properties (similar to hero component)\n    this.isStreamingActive = false;\n    // Workspaces and agents\n    this.workspaces = [];\n    this.selectedWorkspace = '';\n    this.workspaceAgents = [];\n    // UI state\n    this.showScrollButton = false;\n    this.isChatHistoryOpen = false;\n    this.showAgentDropdown = false;\n    this.selectedAgent = '';\n    this.isAgentSidebarOpen = false;\n    this.agentSidebarTitle = 'Agent Tools';\n    this.mentionDropdownVisible = false;\n    this.mentionFilteredAgents = [];\n    this.selectedMentionIndex = 0;\n    this.showPromptDialog = false;\n    this.filteredPrompts = [];\n    this.selectedPromptIndex = 0;\n    this.isRecording = false;\n    this.hasNoHistories = true;\n    // Splitter properties (enhanced from hero component)\n    this.DEFAULT_RIGHT_SIDEBAR_WIDTH = 350; // Fixed initial width in pixels\n    this.RIGHT_SIDEBAR_STORAGE_KEY = 'agentChatRightSidebarWidth'; // Key for localStorage\n    this.rightSidebarWidth = this.DEFAULT_RIGHT_SIDEBAR_WIDTH; // Current width in pixels\n    this.isDragging = false;\n    this.minRightSidebarWidth = 250; // Minimum width in pixels\n    this.maxRightSidebarWidth = 800; // Maximum width in pixels\n    this.autoCloseThreshold = 200; // Width below which sidebar auto-closes\n    this.lastValidWidth = this.DEFAULT_RIGHT_SIDEBAR_WIDTH; // Store last valid width for recovery\n    // Split sizes for the main content and right sidebar\n    this.mainContentSplitSize = 100; // Default to 100% when no sidebar is visible\n    this.rightSidebarSplitSize = 0; // Default to 0% when no sidebar is visible\n    this.showSearchResultsSidebar = false;\n    this.route = inject(ActivatedRoute);\n    this.chatListService = inject(ChatListService);\n    // Configure marked options\n    marked.setOptions({\n      breaks: true,\n      gfm: true\n    });\n  }\n  ngOnInit() {\n    // Initialize component\n    this.loadSavedRightSidebarWidth();\n    this.loadWorkspaces();\n    // Set up streaming message subscription\n    this.setupStreamingSubscription();\n    // Get agent name from route parameter\n    this.route.paramMap.subscribe(params => {\n      const agentName = params.get('name');\n      if (agentName) {\n        this.selectedAgent = agentName;\n        this.userInput.agentName = agentName;\n      }\n      // Load agent chat histories using the route parameter if available\n      this.loadAgentChatHistories(agentName || undefined);\n    });\n  }\n  ngAfterViewInit() {\n    this.scrollToBottom();\n    this.adjustInputHeight();\n  }\n  ngOnDestroy() {\n    if (this.messageSubscription) {\n      this.messageSubscription.unsubscribe();\n    }\n  }\n  onWindowResize(event) {\n    // Update split sizes when window is resized\n    this.updateSplitSizes();\n  }\n  /**\n   * Sets up streaming message subscription similar to hero component\n   */\n  setupStreamingSubscription() {\n    this.messageSubscription = this.chatService.messageReceived$.subscribe(({\n      message,\n      isError,\n      isComplete\n    }) => {\n      console.log('Agent chat - Message received:', message);\n      if (isError) {\n        this.isMessageLoading = false;\n        this.isStreamingActive = false;\n        this.currentStreamingMessageId = undefined;\n        // Handle error message\n        if (this.currentConversation?.histories) {\n          const lastMessage = this.currentConversation.histories[this.currentConversation.histories.length - 1];\n          if (lastMessage && lastMessage.id === this.currentStreamingMessageId) {\n            const messageText = this.extractMessageText(message);\n            if (!lastMessage.responses) {\n              lastMessage.responses = [];\n            }\n            lastMessage.responses.push(new AgentChatResponseDto({\n              id: '',\n              responseText: messageText,\n              chatSource: 'Error',\n              timestamp: DateTime.now()\n            }));\n          }\n        }\n      } else {\n        const messageText = this.extractMessageText(message);\n        console.log('Agent chat - Extracted text:', messageText);\n        if (messageText && this.isStreamingActive && this.currentStreamingMessageId) {\n          // Handle streaming completion\n          if (isComplete) {\n            this.handleStreamingComplete();\n            return;\n          }\n          // Find the streaming message and append text\n          if (this.currentConversation?.histories) {\n            const streamingMessage = this.currentConversation.histories.find(h => h.id === this.currentStreamingMessageId);\n            if (streamingMessage) {\n              // Initialize responses if needed\n              if (!streamingMessage.responses) {\n                streamingMessage.responses = [];\n              }\n              // Get or create the current response\n              if (streamingMessage.responses.length === 0) {\n                streamingMessage.responses.push(new AgentChatResponseDto({\n                  id: '',\n                  responseText: messageText,\n                  chatSource: 'Streaming',\n                  timestamp: DateTime.now()\n                }));\n              } else {\n                // Append to existing response\n                const lastResponse = streamingMessage.responses[streamingMessage.responses.length - 1];\n                lastResponse.responseText = (lastResponse.responseText || '') + messageText;\n              }\n              // Transition from loading to streaming state\n              if (streamingMessage.isLoading) {\n                streamingMessage.isLoading = false;\n                // Add isStreaming property dynamically (similar to hero component)\n                streamingMessage.isStreaming = true;\n              }\n              // Auto-scroll as content streams in\n              setTimeout(() => this.scrollToBottom(), 50);\n            }\n          }\n        }\n      }\n    });\n  }\n  /**\n   * Handles streaming completion\n   */\n  handleStreamingComplete() {\n    if (this.currentStreamingMessageId && this.currentConversation?.histories) {\n      const streamingMessage = this.currentConversation.histories.find(h => h.id === this.currentStreamingMessageId);\n      if (streamingMessage) {\n        // Clear streaming state\n        streamingMessage.isStreaming = false;\n        streamingMessage.isLoading = false;\n        this.isStreamingActive = false;\n        this.currentStreamingMessageId = undefined;\n        this.isMessageLoading = false;\n        this.scrollToBottom();\n      }\n    }\n  }\n  /**\n   * Extracts message text from ResponseMessage (same as hero component)\n   */\n  extractMessageText(message) {\n    return message.isError ? ' ' : message.message;\n  }\n  /**\n   * Check if a message is currently streaming\n   */\n  isMessageStreaming(message) {\n    return message.isStreaming === true;\n  }\n  /**\n   * Loads agent chat histories from API\n   * @param agentName Optional agent name to filter histories\n   */\n  loadAgentChatHistories(agentName) {\n    this.isMessageLoading = true;\n    // Log the agent name for debugging\n    console.log('Loading chat histories for agent:', agentName || 'all agents');\n    this.agentChatService.histories(agentName).subscribe({\n      next: conversations => {\n        // Reverse the conversations array so the last conversation appears first\n        let convArr = Array.isArray(conversations) ? conversations : [conversations];\n        convArr = convArr.reverse();\n        console.log(convArr);\n        // Reverse the histories inside each conversation so the latest message is first\n        convArr.forEach(c => {\n          if (c.histories && Array.isArray(c.histories)) {\n            c.histories = c.histories.reverse();\n            // Set the current response index to the latest response for each history\n            c.histories.forEach(history => {\n              if (history.responses && history.responses.length > 0) {\n                this.currentResponseIndexes[history.id || ''] = history.responses.length - 1;\n              }\n            });\n          }\n        });\n        this.conversations = convArr;\n        // Set hasNoHistories flag based on conversations returned\n        this.hasNoHistories = this.conversations.length === 0 || this.conversations.every(c => !c.histories || c.histories.length === 0);\n        if (this.conversations.length > 0) {\n          // Set current conversation to first agent or selected agent\n          const foundConversation = agentName ? this.conversations.find(c => c.agentName === agentName) : this.conversations[0];\n          if (foundConversation) {\n            this.currentConversation = foundConversation;\n            // Check if this conversation has any histories\n            if (foundConversation.histories && foundConversation.histories.length > 0) {\n              this.hasNoHistories = false;\n            }\n            // Set selected agent from conversation if not already set from URL\n            if (this.currentConversation.agentName && !this.selectedAgent) {\n              this.selectedAgent = this.currentConversation.agentName;\n              this.userInput.agentName = this.currentConversation.agentName;\n            }\n          } else if (agentName) {\n            // If we're searching for a specific agent but didn't find conversations,\n            // create a new conversation for that agent\n            this.currentConversation = new AgentChatConversationDto({\n              agentName: agentName,\n              histories: []\n            });\n            this.conversations.push(this.currentConversation);\n          }\n        } else if (agentName) {\n          // No conversations exist but we have an agent name, create a new conversation\n          this.currentConversation = new AgentChatConversationDto({\n            agentName: agentName,\n            histories: []\n          });\n          this.conversations.push(this.currentConversation);\n          this.hasNoHistories = true;\n        } else {\n          // No conversations at all\n          this.hasNoHistories = true;\n        }\n        this.isMessageLoading = false;\n        setTimeout(() => this.scrollToBottom(), 100);\n      },\n      error: error => {\n        console.error('Error loading agent chat histories:', error);\n        this.message.error('Failed to load chat histories');\n        this.isMessageLoading = false;\n      }\n    });\n  }\n  /**\n   * Loads workspaces from the service\n   */\n  loadWorkspaces() {\n    this.workspaceService.getAll().subscribe({\n      next: workspaces => {\n        this.workspaces = workspaces;\n        if (this.workspaces.length > 0) {\n          this.selectedWorkspace = this.workspaces[0].title;\n          this.loadAgentsForWorkspace(this.selectedWorkspace);\n        }\n      },\n      error: error => {\n        console.error('Error loading workspaces:', error);\n      }\n    });\n  }\n  /**\n   * Loads agents for a specific workspace\n   */\n  loadAgentsForWorkspace(workspaceName) {\n    if (!workspaceName) return;\n    this.agentDefinition.getAllByWorkspace(workspaceName).subscribe({\n      next: agents => {\n        this.workspaceAgents = agents;\n        // Update the mentionFilteredAgents with our agents\n        this.mentionFilteredAgents = [...this.workspaceAgents];\n      },\n      error: error => {\n        console.error('Error loading agents for workspace:', error);\n      }\n    });\n  }\n  /**\n   * Adds a suggestion to the chat input\n   */\n  addSuggestionToChat(suggestion) {\n    this.userInput.question = suggestion.text;\n    if (suggestion.isUserPrompt) {\n      this.sendMessage();\n    }\n  }\n  /**\n   * Helper function to extract content for different response types\n   * These functions would normally parse special format responses\n   * For now they act as basic stubs\n   */\n  hasSqlContent(text) {\n    return !!text && text.includes('```sql');\n  }\n  extractNonSqlContent(text) {\n    if (!text) return '';\n    return text.replace(/```sql[\\s\\S]*?```/g, '');\n  }\n  extractSqlContent(text) {\n    if (!text || !this.hasSqlContent(text)) return '';\n    const match = text.match(/```sql([\\s\\S]*?)```/);\n    return match ? match[1].trim() : '';\n  }\n  extractBlogTitle(text) {\n    if (!text) return 'Blog Post';\n    const titleMatch = text.match(/^#\\s*(.*?)$/m);\n    return titleMatch ? titleMatch[1] : 'Blog Post';\n  }\n  extractEmailSubject(text) {\n    if (!text) return '';\n    const subjectMatch = text.match(/Subject:(.*?)$/m);\n    return subjectMatch ? subjectMatch[1].trim() : '';\n  }\n  extractEmailTo(text) {\n    if (!text) return '';\n    const toMatch = text.match(/To:(.*?)$/m);\n    return toMatch ? toMatch[1].trim() : '';\n  }\n  extractEmailCc(text) {\n    if (!text) return '';\n    const ccMatch = text.match(/Cc:(.*?)$/m);\n    return ccMatch ? ccMatch[1].trim() : '';\n  }\n  extractEmailBody(text) {\n    if (!text) return '';\n    const parts = text.split(/^Body:/m);\n    return parts.length > 1 ? parts[1].trim() : text;\n  }\n  /**\n   * Mentions and prompt handling\n   */\n  selectMentionedAgent(agent) {\n    if (!agent) return;\n    this.userInput.agentName = agent.agentName;\n    this.mentionDropdownVisible = false;\n  }\n  selectPrompt(prompt) {\n    if (!prompt) return;\n    this.userInput.question = prompt.prompt;\n    this.showPromptDialog = false;\n    this.adjustInputHeight();\n  }\n  /**\n   * Loads the saved width from localStorage with validation (enhanced from hero component)\n   */\n  loadSavedRightSidebarWidth() {\n    try {\n      const savedWidth = localStorage.getItem(this.RIGHT_SIDEBAR_STORAGE_KEY);\n      if (savedWidth) {\n        const parsedWidth = parseInt(savedWidth, 10);\n        // Validate the saved width to ensure it's within acceptable range\n        if (!isNaN(parsedWidth) && parsedWidth >= this.minRightSidebarWidth && parsedWidth <= this.maxRightSidebarWidth) {\n          this.rightSidebarWidth = parsedWidth;\n          this.lastValidWidth = parsedWidth; // Store as last valid width\n        } else {\n          // If saved width is invalid, use the default width\n          this.rightSidebarWidth = this.DEFAULT_RIGHT_SIDEBAR_WIDTH;\n          // Update localStorage with the default width\n          this.saveRightSidebarWidth(this.DEFAULT_RIGHT_SIDEBAR_WIDTH);\n        }\n      } else {\n        // If no saved width, use the default width\n        this.rightSidebarWidth = this.DEFAULT_RIGHT_SIDEBAR_WIDTH;\n        // Save the default width to localStorage\n        this.saveRightSidebarWidth(this.DEFAULT_RIGHT_SIDEBAR_WIDTH);\n      }\n      // Update split sizes based on sidebar visibility\n      this.updateSplitSizes();\n    } catch (error) {\n      console.error('Error loading saved width:', error);\n      this.rightSidebarWidth = this.DEFAULT_RIGHT_SIDEBAR_WIDTH;\n      this.saveRightSidebarWidth(this.DEFAULT_RIGHT_SIDEBAR_WIDTH);\n    }\n  }\n  /**\n   * Saves the width to localStorage\n   * @param width The width to save\n   */\n  saveRightSidebarWidth(width) {\n    try {\n      localStorage.setItem(this.RIGHT_SIDEBAR_STORAGE_KEY, width.toString());\n    } catch (error) {\n      console.error('Error saving width to localStorage:', error);\n    }\n  }\n  /**\n   * Scrolls the chat container to the bottom\n   */\n  scrollToBottom() {\n    try {\n      if (this.chatContainer) {\n        setTimeout(() => {\n          this.chatContainer.nativeElement.scrollTop = this.chatContainer.nativeElement.scrollHeight;\n        }, 100);\n      }\n    } catch (err) {\n      console.error('Error scrolling to bottom:', err);\n    }\n  }\n  /**\n   * Adjusts the height of the input textarea\n   */\n  adjustInputHeight() {\n    if (this.chatInput && this.chatInput.nativeElement) {\n      const element = this.chatInput.nativeElement;\n      element.style.height = 'auto';\n      element.style.height = Math.min(element.scrollHeight, 200) + 'px';\n    }\n  }\n  /**\n   * Sends a message to agent chat API\n   */\n  sendMessage() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (!_this.userInput.question?.trim()) return;\n      if (!_this.userInput.agentName && _this.selectedAgent) {\n        _this.userInput.agentName = _this.selectedAgent;\n      }\n      // Store the original message for immediate display\n      const originalMessage = _this.userInput.question;\n      const agentName = _this.userInput.agentName || _this.selectedAgent;\n      // Find current conversation or create a new one\n      if (_this.currentConversation.agentName !== agentName) {\n        const foundConversation = _this.conversations.find(c => c.agentName === agentName);\n        if (foundConversation) {\n          _this.currentConversation = foundConversation;\n        } else {\n          _this.currentConversation = new AgentChatConversationDto({\n            agentName: agentName,\n            histories: []\n          });\n          _this.conversations.push(_this.currentConversation);\n        }\n      }\n      // Ensure histories array exists\n      if (!_this.currentConversation.histories) {\n        _this.currentConversation.histories = [];\n      }\n      // Create a temporary message object to show immediately in the UI\n      const tempMessage = new AgentChatHistoryDto({\n        id: 'temp-' + Date.now(),\n        question: originalMessage,\n        responses: [],\n        timestamp: DateTime.now()\n      });\n      tempMessage.isLoading = true;\n      // Add the temporary message to the conversation immediately\n      _this.currentConversation.histories.push(tempMessage);\n      // Update hasNoHistories flag since we now have a conversation\n      _this.hasNoHistories = false;\n      // Clear input immediately for better UX\n      _this.userInput.question = '';\n      _this.adjustInputHeight();\n      // Set loading state\n      _this.isMessageLoading = true;\n      // Scroll to show the new message\n      setTimeout(() => _this.scrollToBottom(), 100);\n      console.log('Sending message to agent:', agentName);\n      // Set up streaming for this message\n      _this.currentStreamingMessageId = tempMessage.id;\n      _this.isStreamingActive = true;\n      // Send the actual request\n      const requestDto = new AgentChatRequestDto({\n        question: originalMessage,\n        agentName: agentName\n      });\n      _this.agentChatService.sendAgentMessage(requestDto).subscribe({\n        next: response => {\n          if (!response) {\n            console.error('Received null response from agent chat service');\n            _this.message.error('Received invalid response from server');\n            _this.isMessageLoading = false;\n            return;\n          }\n          // Find and replace the temporary message with the actual response\n          if (_this.currentConversation.histories) {\n            const tempIndex = _this.currentConversation.histories.findIndex(h => h.id === tempMessage.id);\n            if (tempIndex !== -1) {\n              // Replace the temporary message with the actual response\n              _this.currentConversation.histories[tempIndex] = response;\n              // Set the current response index to show the latest response\n              if (response.id) {\n                _this.currentResponseIndexes[response.id] = (response.responses || []).length - 1;\n              }\n            } else {\n              // Fallback: just add the response if temp message not found\n              _this.currentConversation.histories.push(response);\n              if (response.id) {\n                _this.currentResponseIndexes[response.id] = (response.responses || []).length - 1;\n              }\n            }\n          }\n          // Clear loading state\n          _this.isMessageLoading = false;\n          _this.scrollToBottom();\n        },\n        error: error => {\n          console.error('Error sending agent message:', error);\n          _this.message.error('Failed to send message');\n          // Remove the temporary message on error\n          if (_this.currentConversation.histories) {\n            const tempIndex = _this.currentConversation.histories.findIndex(h => h.id === tempMessage.id);\n            if (tempIndex !== -1) {\n              _this.currentConversation.histories.splice(tempIndex, 1);\n            }\n          }\n          // Restore the original message to the input\n          _this.userInput.question = originalMessage;\n          _this.adjustInputHeight();\n          _this.isMessageLoading = false;\n        }\n      });\n    })();\n  }\n  /**\n   * Regenerate response for a specific chat message\n   */\n  regenerateResponse(history) {\n    if (!history || !history.id) return;\n    // Set loading state for this specific message\n    history.isLoading = true;\n    const agentName = this.currentConversation?.agentName;\n    this.agentChatService.agentChatRegenerate(history.id, agentName).subscribe({\n      next: response => {\n        // Clear loading state for this message\n        history.isLoading = false;\n        // Update the responses in the history\n        if (!history.responses) {\n          history.responses = [];\n        }\n        // Create a new response from regenerated content\n        if (response.responseText) {\n          const newResponse = new AgentChatResponseDto({\n            id: response.id || '',\n            responseText: response.responseText,\n            chatSource: 'Regenerated',\n            timestamp: DateTime.now()\n          });\n          history.responses.push(newResponse);\n          // Update the current response index to show the latest response\n          const historyId = history.id || '';\n          this.currentResponseIndexes[historyId] = history.responses.length - 1;\n        }\n        this.scrollToBottom();\n      },\n      error: error => {\n        console.error('Error regenerating response:', error);\n        this.message.error('Failed to regenerate response');\n        // Clear loading state for this message on error\n        history.isLoading = false;\n      }\n    });\n  }\n  /**\n   * Edit an existing message\n   */\n  editMessage(history) {\n    if (!history) return;\n    // Store the edited question\n    const editedQuestion = history.question;\n    // Exit editing mode\n    history.editingMode = false;\n    // Set the input to the edited question and send it\n    this.userInput.question = editedQuestion;\n    this.sendMessage();\n  }\n  /**\n   * Functions for voice commands and recording\n   */\n  startRecording() {\n    this.isRecording = true;\n    // Placeholder for actual recording implementation\n    this.message.info('Recording started');\n  }\n  stopRecording() {\n    this.isRecording = false;\n    // Placeholder for actual recording implementation\n    this.message.info('Recording stopped');\n  }\n  /**\n   * Text-to-speech functionality\n   */\n  textToSpeech(text) {\n    // Placeholder for actual TTS implementation\n    this.message.info('Text-to-speech started');\n  }\n  stopSpeech() {\n    // Placeholder for stopping TTS\n    this.message.info('Text-to-speech stopped');\n  }\n  // Input handling methods\n  handleKeyDown(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  onInput(event) {\n    this.adjustInputHeight();\n  }\n  onChatScroll() {\n    if (this.chatContainer) {\n      const element = this.chatContainer.nativeElement;\n      this.showScrollButton = element.scrollHeight - element.scrollTop - element.clientHeight > 100;\n    }\n  }\n  /**\n   * Updates the split sizes based on sidebar visibility (from hero component)\n   */\n  updateSplitSizes() {\n    if (this.showSearchResultsSidebar || this.isAgentSidebarOpen) {\n      // Calculate the percentage for the right sidebar\n      const windowWidth = window.innerWidth;\n      // Ensure the sidebar takes at least 20% of the window width for better visibility\n      const minPercentage = 20;\n      const calculatedPercentage = this.rightSidebarWidth / windowWidth * 100;\n      this.rightSidebarSplitSize = Math.max(calculatedPercentage, minPercentage);\n      this.mainContentSplitSize = 100 - this.rightSidebarSplitSize;\n    } else {\n      // If no sidebar is visible, set main content to 100%\n      this.mainContentSplitSize = 100;\n      this.rightSidebarSplitSize = 0;\n    }\n  }\n  /**\n   * Toggles the search results sidebar visibility and populates it with data (from hero component)\n   * @param chatSourceDescriptions Optional array of source descriptions to display\n   * @param sourceName Optional name of the source (e.g., \"Google\", \"Bing\")\n   */\n  toggleSearchResultsSidebar(chatSourceDescriptions, sourceName = '') {\n    console.log('Toggling search results sidebar. Current state:', this.showSearchResultsSidebar);\n    // If we have chat source descriptions, use them\n    if (chatSourceDescriptions && chatSourceDescriptions.length > 0) {\n      console.log('Using provided chat source descriptions:', chatSourceDescriptions);\n      // Store the source name\n      this.currentSourceName = sourceName;\n      // Format the search results\n      this.searchResults = chatSourceDescriptions.map(desc => ({\n        title: desc.title || 'No Title',\n        url: desc.url || '',\n        domain: desc.url,\n        description: desc.description || 'No description available'\n      }));\n      // Make sure the sidebar is open\n      this.showSearchResultsSidebar = true;\n      // Close agent sidebar if it's open\n      if (this.isAgentSidebarOpen) {\n        this.isAgentSidebarOpen = false;\n      }\n    } else {\n      // If no descriptions provided, toggle the sidebar visibility\n      this.showSearchResultsSidebar = !this.showSearchResultsSidebar;\n    }\n    // Update split sizes based on sidebar visibility\n    this.updateSplitSizes();\n    // Add a small delay to allow the DOM to update before any animations\n    setTimeout(() => {\n      // Force a layout recalculation to ensure smooth transitions\n      window.dispatchEvent(new Event('resize'));\n    }, 10);\n  }\n  // UI control methods\n  toggleAgentSidebar() {\n    this.isAgentSidebarOpen = !this.isAgentSidebarOpen;\n    // Close search results sidebar if agent sidebar is opening\n    if (this.isAgentSidebarOpen && this.showSearchResultsSidebar) {\n      this.showSearchResultsSidebar = false;\n    }\n    // Update split sizes based on sidebar visibility\n    this.updateSplitSizes();\n    // Add a small delay to allow the DOM to update before any animations\n    setTimeout(() => {\n      // Force a layout recalculation to ensure smooth transitions\n      window.dispatchEvent(new Event('resize'));\n    }, 10);\n  }\n  /**\n   * Enhanced splitter methods from hero component\n   */\n  onSplitDragEnd(event) {\n    // Get the actual width from the split area\n    const splitArea = event.sizes[1]; // Right sidebar is the second area\n    if (splitArea && splitArea > 0) {\n      const windowWidth = window.innerWidth;\n      this.rightSidebarWidth = Math.round(splitArea / 100 * windowWidth);\n      // Validate and constrain the width\n      this.rightSidebarWidth = Math.max(this.minRightSidebarWidth, Math.min(this.rightSidebarWidth, this.maxRightSidebarWidth));\n      // Check if width is below auto-close threshold\n      if (this.rightSidebarWidth < this.autoCloseThreshold) {\n        this.showSearchResultsSidebar = false;\n        this.isAgentSidebarOpen = false;\n        this.updateSplitSizes();\n      } else {\n        // Store as last valid width\n        this.lastValidWidth = this.rightSidebarWidth;\n        // Save to localStorage\n        this.saveRightSidebarWidth(this.rightSidebarWidth);\n      }\n    }\n    this.isDragging = false;\n  }\n  onSplitDragProgress(event) {\n    this.isDragging = true;\n    // Optional: Update width in real-time during drag\n    const splitArea = event.sizes[1];\n    if (splitArea && splitArea > 0) {\n      const windowWidth = window.innerWidth;\n      const newWidth = Math.round(splitArea / 100 * windowWidth);\n      // Check if dragging below auto-close threshold\n      if (newWidth < this.autoCloseThreshold) {\n        // Visual feedback that sidebar will close\n        // You could add a visual indicator here\n      }\n    }\n  }\n  onGutterDoubleClick(event) {\n    if (this.showSearchResultsSidebar || this.isAgentSidebarOpen) {\n      // Close the sidebar\n      this.showSearchResultsSidebar = false;\n      this.isAgentSidebarOpen = false;\n    } else {\n      // Open the last active sidebar (prioritize search results)\n      if (this.searchResults.length > 0) {\n        this.showSearchResultsSidebar = true;\n      } else {\n        this.isAgentSidebarOpen = true;\n      }\n      // Restore the last valid width\n      this.rightSidebarWidth = this.lastValidWidth;\n    }\n    this.updateSplitSizes();\n  }\n  selectWorkspace(workspace) {\n    this.selectedWorkspace = workspace;\n    this.loadAgentsForWorkspace(workspace);\n  }\n  selectAgent(agent) {\n    this.selectedAgent = agent.agentName || agent;\n    this.userInput.agentName = this.selectedAgent;\n    // Navigate to the agent's chat URL\n    this.router.navigate(['/agent-chat', this.selectedAgent]);\n    // Close sidebar if open\n    if (this.isAgentSidebarOpen) {\n      this.isAgentSidebarOpen = false;\n      this.rightSidebarSplitSize = 0;\n      this.mainContentSplitSize = 100;\n    }\n  }\n  clearSelectedAgent() {\n    this.selectedAgent = '';\n    this.userInput.agentName = '';\n    // Navigate back to the base agent-chat URL\n    this.router.navigate(['/agent-chat']);\n    // Load all chat histories\n    this.loadAgentChatHistories();\n  }\n  // Helper methods\n  copyContent(content) {\n    if (content) {\n      navigator.clipboard.writeText(content);\n      this.message.success('Content copied to clipboard');\n    }\n  }\n  copySqlContent(content) {\n    this.copyContent(content);\n  }\n  copyBlogContent(content) {\n    this.copyContent(content);\n  }\n  copyEmailContent(content) {\n    this.copyContent(content);\n  }\n  // Response navigation methods\n  getCurrentResponseIndex(historyId) {\n    return this.currentResponseIndexes[historyId] || 0;\n  }\n  getCurrentResponse(history) {\n    if (!history || !history.responses || history.responses.length === 0) return null;\n    const historyId = history.id || '';\n    const currentIndex = this.getCurrentResponseIndex(historyId);\n    const response = history.responses[currentIndex];\n    return response || null;\n  }\n  goToPreviousResponse(history) {\n    if (!history || !history.responses || history.responses.length <= 1) return;\n    const historyId = history.id || '';\n    const currentIndex = this.getCurrentResponseIndex(historyId);\n    if (currentIndex > 0) {\n      this.currentResponseIndexes[historyId] = currentIndex - 1;\n    }\n  }\n  nextResponse(history) {\n    if (!history || !history.responses || history.responses.length <= 1) return;\n    const historyId = history.id || '';\n    const currentIndex = this.getCurrentResponseIndex(historyId);\n    if (currentIndex < history.responses.length - 1) {\n      this.currentResponseIndexes[historyId] = currentIndex + 1;\n    }\n  }\n  getResponseNavigation(history) {\n    if (!history || !history.responses || history.responses.length <= 1) return '';\n    const historyId = history.id || '';\n    const currentIndex = this.getCurrentResponseIndex(historyId);\n    return `${currentIndex + 1}/${history.responses.length}`;\n  }\n  openSqlConnectionDialog(query) {\n    // Placeholder for SQL connection dialog\n    this.message.info('SQL Connection dialog would open here');\n  }\n  openBlogShareDialog(content) {\n    // Placeholder for blog sharing\n    this.message.info('Blog sharing dialog would open here');\n  }\n  sendEmail(content) {\n    // Placeholder for email sending\n    this.message.info('Email sending would happen here');\n  }\n  /**\n   * Handles clicking on source indicators in chat responses (from hero component)\n   * @param response The response object containing source descriptions\n   */\n  onSourceClick(response) {\n    if (response && response.chatSourceDescriptions && response.chatSourceDescriptions.length > 0) {\n      console.log('Source clicked, opening sidebar with descriptions:', response.chatSourceDescriptions);\n      // Extract source name from the response\n      const sourceName = response.chatSource || 'Web Search';\n      // Open the source references sidebar with the descriptions\n      this.toggleSearchResultsSidebar(response.chatSourceDescriptions, sourceName);\n    } else {\n      console.log('No source descriptions found for this response');\n      // Still open the sidebar but with empty results\n      this.toggleSearchResultsSidebar([], 'No Sources');\n    }\n  }\n  /**\n   * Checks if a response has source descriptions that can be displayed\n   * @param response The response object to check\n   * @returns True if the response has source descriptions\n   */\n  hasSourceDescriptions(response) {\n    return response && response.chatSourceDescriptions && Array.isArray(response.chatSourceDescriptions) && response.chatSourceDescriptions.length > 0;\n  }\n  /**\n   * Gets the count of source descriptions for a response\n   * @param response The response object\n   * @returns The number of source descriptions\n   */\n  getSourceCount(response) {\n    if (this.hasSourceDescriptions(response)) {\n      return response.chatSourceDescriptions.length;\n    }\n    return 0;\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: Router\n    }, {\n      type: AuthService\n    }, {\n      type: AgentDefinitionServiceProxy\n    }, {\n      type: DomSanitizer\n    }, {\n      type: ChatService\n    }, {\n      type: AuthService\n    }, {\n      type: NzMessageService\n    }, {\n      type: WorkspaceServiceProxy\n    }, {\n      type: NzModalService\n    }, {\n      type: AgentChatServiceProxy\n    }];\n  }\n  static {\n    this.propDecorators = {\n      chatInput: [{\n        type: ViewChild,\n        args: ['chatInput']\n      }],\n      chatContainer: [{\n        type: ViewChild,\n        args: ['chatContainer']\n      }],\n      promptDialog: [{\n        type: ViewChild,\n        args: ['promptDialog']\n      }],\n      toggleWorkspaceList: [{\n        type: ViewChild,\n        args: ['toggleWorkspaceList']\n      }],\n      agentListDialog: [{\n        type: ViewChild,\n        args: ['agentListDialog']\n      }],\n      onWindowResize: [{\n        type: HostListener,\n        args: ['window:resize', ['$event']]\n      }]\n    };\n  }\n};\nAgentChatComponent = __decorate([Component({\n  selector: 'app-agent-chat',\n  standalone: true,\n  imports: [CommonModule, FormsModule, ServiceProxyModule, NzDrawerModule, MarkdownModule, NzSelectModule, NzBadgeModule, NzToolTipModule, NzModalModule, SourceReferencesComponent, AgentSidebarComponent, AngularSplitModule],\n  providers: [NzModalService],\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], AgentChatComponent);\nexport { AgentChatComponent };", "map": {"version": 3, "names": ["Component", "ViewChild", "inject", "HostListener", "<PERSON><PERSON>", "CommonModule", "FormsModule", "Dom<PERSON><PERSON><PERSON>zer", "marked", "ActivatedRoute", "Router", "AgentDefinitionServiceProxy", "WorkspaceServiceProxy", "AgentChatServiceProxy", "AgentChatRequestDto", "AgentChatHistoryDto", "AgentChatConversationDto", "AgentChatResponseDto", "ServiceProxyModule", "AuthService", "ChatService", "NzDrawerModule", "NzSelectModule", "NzBadgeModule", "NzToolTipModule", "NzModalModule", "NzModalService", "NzMessageService", "MarkdownModule", "SourceReferencesComponent", "AgentSidebarComponent", "AngularSplitModule", "ChatListService", "DateTime", "RelativeTimePipe", "transform", "value", "date", "Date", "now", "diffInMs", "getTime", "diffInMinutes", "Math", "floor", "diffInHours", "diffInDays", "Promise", "resolve", "toLocaleDateString", "__decorate", "name", "standalone", "AgentChatComponent", "constructor", "router", "authService", "agentDefinition", "sanitizer", "chatService", "auth", "message", "workspaceService", "modal", "agentChatService", "userInput", "conversations", "currentConversation", "<PERSON><PERSON><PERSON>", "histories", "isMessageLoading", "previousResponse", "searchResults", "currentSourceName", "currentResponseIndexes", "isStreamingActive", "workspaces", "selectedWorkspace", "workspaceAgents", "showScrollButton", "isChatHistoryOpen", "showAgentDropdown", "selectedAgent", "isAgentSidebarOpen", "agentSidebarTitle", "mentionDropdownVisible", "mentionFilteredAgents", "selectedMentionIndex", "showPromptDialog", "filteredPrompts", "selectedPromptIndex", "isRecording", "hasNoHistories", "DEFAULT_RIGHT_SIDEBAR_WIDTH", "RIGHT_SIDEBAR_STORAGE_KEY", "rightSidebarWidth", "isDragging", "minRightSidebarWidth", "maxRightSidebarWidth", "autoCloseThreshold", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mainContentSplitSize", "rightSidebarSplitSize", "showSearchResultsSidebar", "route", "chatListService", "setOptions", "breaks", "gfm", "ngOnInit", "loadSavedRightSidebarWidth", "loadWorkspaces", "setupStreamingSubscription", "paramMap", "subscribe", "params", "get", "loadAgentChatHistories", "undefined", "ngAfterViewInit", "scrollToBottom", "adjustInputHeight", "ngOnDestroy", "messageSubscription", "unsubscribe", "onWindowResize", "event", "updateSplitSizes", "messageReceived$", "isError", "isComplete", "console", "log", "currentStreamingMessageId", "lastMessage", "length", "id", "messageText", "extractMessageText", "responses", "push", "responseText", "chatSource", "timestamp", "handleStreamingComplete", "streamingMessage", "find", "h", "lastResponse", "isLoading", "isStreaming", "setTimeout", "isMessageStreaming", "next", "convArr", "Array", "isArray", "reverse", "for<PERSON>ach", "c", "history", "every", "foundConversation", "error", "getAll", "title", "loadAgentsForWorkspace", "workspaceName", "getAllByWorkspace", "agents", "addSuggestionToChat", "suggestion", "question", "text", "isUserPrompt", "sendMessage", "hasSqlContent", "includes", "extractNonSqlContent", "replace", "extractSqlContent", "match", "trim", "extractBlogTitle", "titleMatch", "extractEmailSubject", "subjectMatch", "extractEmailTo", "toMatch", "extractEmailCc", "ccMatch", "extractEmailBody", "parts", "split", "selectMentionedAgent", "agent", "selectPrompt", "prompt", "saved<PERSON>idth", "localStorage", "getItem", "parsedWidth", "parseInt", "isNaN", "saveRightSidebarWidth", "width", "setItem", "toString", "chatContainer", "nativeElement", "scrollTop", "scrollHeight", "err", "chatInput", "element", "style", "height", "min", "_this", "_asyncToGenerator", "originalMessage", "tempMessage", "requestDto", "sendAgentMessage", "response", "tempIndex", "findIndex", "splice", "regenerateResponse", "agentChatRegenerate", "newResponse", "historyId", "editMessage", "editedQuestion", "editingMode", "startRecording", "info", "stopRecording", "textToSpeech", "stopSpeech", "handleKeyDown", "key", "shift<PERSON>ey", "preventDefault", "onInput", "onChatScroll", "clientHeight", "windowWidth", "window", "innerWidth", "minPercentage", "calculatedPercentage", "max", "toggleSearchResultsSidebar", "chatSourceDescriptions", "sourceName", "map", "desc", "url", "domain", "description", "dispatchEvent", "Event", "toggleAgentSidebar", "onSplitDragEnd", "splitArea", "sizes", "round", "onSplitDragProgress", "newWidth", "onGutterDoubleClick", "selectWorkspace", "workspace", "selectAgent", "navigate", "clearSelectedAgent", "copyContent", "content", "navigator", "clipboard", "writeText", "success", "copySqlContent", "copyBlogContent", "copyEmailContent", "getCurrentResponseIndex", "getCurrentResponse", "currentIndex", "goToPreviousResponse", "nextResponse", "getResponseNavigation", "openSqlConnectionDialog", "query", "openBlogShareDialog", "sendEmail", "onSourceClick", "hasSourceDescriptions", "getSourceCount", "args", "selector", "imports", "providers", "template", "__NG_CLI_RESOURCE__0"], "sources": ["C:\\Users\\<USER>\\source\\ai-hub\\WebApp\\src\\app\\pages\\agent-chat\\agent-chat.component.ts"], "sourcesContent": ["import {\r\n  Component,\r\n  ViewChild,\r\n  ElementRef,\r\n  OnInit,\r\n  inject,\r\n  AfterViewInit,\r\n  OnDestroy,\r\n  HostListener,\r\n  Pipe,\r\n  PipeTransform,\r\n  isDevMode,\r\n} from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { DomSanitizer } from '@angular/platform-browser';\r\nimport { marked } from 'marked';\r\nimport { Subscription } from 'rxjs';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport {\r\n  AgentDefinitionServiceProxy,\r\n  WorkspaceServiceProxy,\r\n  AgentChatServiceProxy,\r\n  AgentChatRequestDto,\r\n  AgentChatHistoryDto,\r\n  AgentChatConversationDto,\r\n  AgentChatResponseDto,\r\n  ChatResponseDto,\r\n} from '../../../shared/service-proxies/service-proxies';\r\nimport { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';\r\nimport { AuthService } from '../../../shared/services/auth.service';\r\nimport { ChatService } from '../../services/chat.service';\r\nimport { NzDrawerModule } from 'ng-zorro-antd/drawer';\r\nimport { NzSelectModule } from 'ng-zorro-antd/select';\r\nimport { NzBadgeModule } from 'ng-zorro-antd/badge';\r\nimport { NzToolTipModule } from 'ng-zorro-antd/tooltip';\r\nimport { NzModalModule, NzModalService } from 'ng-zorro-antd/modal';\r\nimport { NzMessageService } from 'ng-zorro-antd/message';\r\nimport { MarkdownModule } from 'ngx-markdown';\r\nimport { SourceReferencesComponent } from '../../components/@rightSideComponents/source-references/source-references.component';\r\nimport { AgentSidebarComponent } from '../../components/@rightSideComponents/agent-sidebar/agent-sidebar.component';\r\nimport { AngularSplitModule } from 'angular-split';\r\nimport { ChatListService } from '../../services/chat-list.service';\r\nimport { DateTime } from 'luxon';\r\n\r\n// Simple RelativeTime pipe for timestamp display\r\n@Pipe({\r\n  name: 'relativeTime',\r\n  standalone: true,\r\n})\r\nexport class RelativeTimePipe implements PipeTransform {\r\n  transform(value: string | Date): any {\r\n    if (!value) return '';\r\n\r\n    const date = new Date(value);\r\n    const now = new Date();\r\n    const diffInMs = now.getTime() - date.getTime();\r\n    const diffInMinutes = Math.floor(diffInMs / (1000 * 60));\r\n    const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));\r\n    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));\r\n\r\n    if (diffInMinutes < 1) {\r\n      return Promise.resolve('just now');\r\n    } else if (diffInMinutes < 60) {\r\n      return Promise.resolve(\r\n        `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`\r\n      );\r\n    } else if (diffInHours < 24) {\r\n      return Promise.resolve(\r\n        `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`\r\n      );\r\n    } else if (diffInDays < 7) {\r\n      return Promise.resolve(\r\n        `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`\r\n      );\r\n    } else {\r\n      return Promise.resolve(date.toLocaleDateString());\r\n    }\r\n  }\r\n}\r\n\r\n@Component({\r\n  selector: 'app-agent-chat',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    ServiceProxyModule,\r\n    NzDrawerModule,\r\n    MarkdownModule,\r\n    NzSelectModule,\r\n    NzBadgeModule,\r\n    NzToolTipModule,\r\n    NzModalModule,\r\n    SourceReferencesComponent,\r\n    AgentSidebarComponent,\r\n    AngularSplitModule,\r\n  ],\r\n  providers: [NzModalService],\r\n  templateUrl: './agent-chat.component.html',\r\n  styleUrl: './agent-chat.component.css',\r\n})\r\nexport class AgentChatComponent implements OnInit, AfterViewInit, OnDestroy {\r\n  @ViewChild('chatInput') chatInput!: ElementRef;\r\n  @ViewChild('chatContainer') chatContainer!: ElementRef;\r\n  @ViewChild('promptDialog') promptDialog!: ElementRef;\r\n  @ViewChild('toggleWorkspaceList') toggleWorkspaceList!: ElementRef;\r\n  @ViewChild('agentListDialog') agentListDialog!: ElementRef;\r\n\r\n  // User message input\r\n  userInput: AgentChatRequestDto = new AgentChatRequestDto();\r\n\r\n  // Chat data\r\n  conversations: any[] = [];\r\n  currentConversation: AgentChatConversationDto = new AgentChatConversationDto({\r\n    agentName: '',\r\n    histories: []\r\n  });\r\n  isMessageLoading = false;\r\n  previousResponse = '';\r\n\r\n  // Search results and source references\r\n  searchResults: any[] = [];\r\n  currentSourceName = '';\r\n\r\n  // Response navigation tracking\r\n  currentResponseIndexes: { [key: string]: number } = {}\r\n\r\n  // Streaming properties (similar to hero component)\r\n  isStreamingActive = false;\r\n  currentStreamingMessageId?: string;\r\n\r\n  // Workspaces and agents\r\n  workspaces: any = [];\r\n  selectedWorkspace = '';\r\n  workspaceAgents: any = [];\r\n  // UI state\r\n  showScrollButton: boolean = false;\r\n  isChatHistoryOpen: boolean = false;\r\n  showAgentDropdown = false;\r\n  selectedAgent = '';\r\n  isAgentSidebarOpen = false;\r\n  agentSidebarTitle = 'Agent Tools';\r\n  mentionDropdownVisible = false;\r\n  mentionFilteredAgents: any[] = [];\r\n  selectedMentionIndex = 0;\r\n  showPromptDialog = false;\r\n  filteredPrompts: any[] = [];\r\n  selectedPromptIndex = 0;\r\n  isRecording = false;\r\n  hasNoHistories = true;\r\n\r\n  // Splitter properties (enhanced from hero component)\r\n  readonly DEFAULT_RIGHT_SIDEBAR_WIDTH: number = 350; // Fixed initial width in pixels\r\n  readonly RIGHT_SIDEBAR_STORAGE_KEY: string = 'agentChatRightSidebarWidth'; // Key for localStorage\r\n  rightSidebarWidth: number = this.DEFAULT_RIGHT_SIDEBAR_WIDTH; // Current width in pixels\r\n  isDragging: boolean = false;\r\n  minRightSidebarWidth: number = 250; // Minimum width in pixels\r\n  maxRightSidebarWidth: number = 800; // Maximum width in pixels\r\n  autoCloseThreshold: number = 200; // Width below which sidebar auto-closes\r\n  private lastValidWidth: number = this.DEFAULT_RIGHT_SIDEBAR_WIDTH; // Store last valid width for recovery\r\n\r\n  // Split sizes for the main content and right sidebar\r\n  mainContentSplitSize: number = 100; // Default to 100% when no sidebar is visible\r\n  rightSidebarSplitSize: number = 0; // Default to 0% when no sidebar is visible\r\n  showSearchResultsSidebar: boolean = false;\r\n\r\n\r\n\r\n  // Message subscription\r\n  private messageSubscription?: Subscription;\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private authService: AuthService,\r\n    private agentDefinition: AgentDefinitionServiceProxy,\r\n    private sanitizer: DomSanitizer,\r\n    private chatService: ChatService,\r\n    public auth: AuthService,\r\n    private message: NzMessageService,\r\n    private workspaceService: WorkspaceServiceProxy,\r\n    private modal: NzModalService,\r\n    private agentChatService: AgentChatServiceProxy\r\n  ) {\r\n    // Configure marked options\r\n    marked.setOptions({\r\n      breaks: true,\r\n      gfm: true,\r\n    });\r\n  }\r\n\r\n  route = inject(ActivatedRoute);\r\n  chatListService = inject(ChatListService);\r\n  ngOnInit(): void {\r\n    // Initialize component\r\n    this.loadSavedRightSidebarWidth();\r\n    this.loadWorkspaces();\r\n\r\n    // Set up streaming message subscription\r\n    this.setupStreamingSubscription();\r\n\r\n    // Get agent name from route parameter\r\n    this.route.paramMap.subscribe(params => {\r\n      const agentName = params.get('name');\r\n      if (agentName) {\r\n        this.selectedAgent = agentName;\r\n        this.userInput.agentName = agentName;\r\n      }\r\n\r\n      // Load agent chat histories using the route parameter if available\r\n      this.loadAgentChatHistories(agentName || undefined);\r\n    });\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    this.scrollToBottom();\r\n    this.adjustInputHeight();\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    if (this.messageSubscription) {\r\n      this.messageSubscription.unsubscribe();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Handle window resize events to update split sizes (from hero component)\r\n   */\r\n  @HostListener('window:resize', ['$event'])\r\n  onWindowResize(event: any): void {\r\n    // Update split sizes when window is resized\r\n    this.updateSplitSizes();\r\n  }\r\n\r\n  /**\r\n   * Sets up streaming message subscription similar to hero component\r\n   */\r\n  private setupStreamingSubscription(): void {\r\n    this.messageSubscription = this.chatService.messageReceived$.subscribe(\r\n      ({ message, isError, isComplete }) => {\r\n        console.log('Agent chat - Message received:', message);\r\n\r\n        if (isError) {\r\n          this.isMessageLoading = false;\r\n          this.isStreamingActive = false;\r\n          this.currentStreamingMessageId = undefined;\r\n\r\n          // Handle error message\r\n          if (this.currentConversation?.histories) {\r\n            const lastMessage = this.currentConversation.histories[this.currentConversation.histories.length - 1];\r\n            if (lastMessage && lastMessage.id === this.currentStreamingMessageId) {\r\n              const messageText = this.extractMessageText(message);\r\n              if (!lastMessage.responses) {\r\n                lastMessage.responses = [];\r\n              }\r\n              lastMessage.responses.push(new AgentChatResponseDto({\r\n                id: '',\r\n                responseText: messageText,\r\n                chatSource: 'Error',\r\n                timestamp: DateTime.now()\r\n              }));\r\n            }\r\n          }\r\n        } else {\r\n          const messageText = this.extractMessageText(message);\r\n          console.log('Agent chat - Extracted text:', messageText);\r\n\r\n          if (messageText && this.isStreamingActive && this.currentStreamingMessageId) {\r\n            // Handle streaming completion\r\n            if (isComplete) {\r\n              this.handleStreamingComplete();\r\n              return;\r\n            }\r\n\r\n            // Find the streaming message and append text\r\n            if (this.currentConversation?.histories) {\r\n              const streamingMessage = this.currentConversation.histories.find(h => h.id === this.currentStreamingMessageId);\r\n              if (streamingMessage) {\r\n                // Initialize responses if needed\r\n                if (!streamingMessage.responses) {\r\n                  streamingMessage.responses = [];\r\n                }\r\n\r\n                // Get or create the current response\r\n                if (streamingMessage.responses.length === 0) {\r\n                  streamingMessage.responses.push(new AgentChatResponseDto({\r\n                    id: '',\r\n                    responseText: messageText,\r\n                    chatSource: 'Streaming',\r\n                    timestamp: DateTime.now()\r\n                  }));\r\n                } else {\r\n                  // Append to existing response\r\n                  const lastResponse = streamingMessage.responses[streamingMessage.responses.length - 1];\r\n                  lastResponse.responseText = (lastResponse.responseText || '') + messageText;\r\n                }\r\n\r\n                // Transition from loading to streaming state\r\n                if (streamingMessage.isLoading) {\r\n                  streamingMessage.isLoading = false;\r\n                  // Add isStreaming property dynamically (similar to hero component)\r\n                  (streamingMessage as any).isStreaming = true;\r\n                }\r\n\r\n                // Auto-scroll as content streams in\r\n                setTimeout(() => this.scrollToBottom(), 50);\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Handles streaming completion\r\n   */\r\n  private handleStreamingComplete(): void {\r\n    if (this.currentStreamingMessageId && this.currentConversation?.histories) {\r\n      const streamingMessage = this.currentConversation.histories.find(h => h.id === this.currentStreamingMessageId);\r\n      if (streamingMessage) {\r\n        // Clear streaming state\r\n        (streamingMessage as any).isStreaming = false;\r\n        streamingMessage.isLoading = false;\r\n        this.isStreamingActive = false;\r\n        this.currentStreamingMessageId = undefined;\r\n        this.isMessageLoading = false;\r\n\r\n        this.scrollToBottom();\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Extracts message text from ResponseMessage (same as hero component)\r\n   */\r\n  private extractMessageText(message: any): string {\r\n    return message.isError ? ' ' : message.message;\r\n  }\r\n\r\n  /**\r\n   * Check if a message is currently streaming\r\n   */\r\n  isMessageStreaming(message: any): boolean {\r\n    return (message as any).isStreaming === true;\r\n  }\r\n\r\n  /**\r\n   * Loads agent chat histories from API\r\n   * @param agentName Optional agent name to filter histories\r\n   */\r\n  loadAgentChatHistories(agentName?: string) {\r\n    this.isMessageLoading = true;\r\n\r\n    // Log the agent name for debugging\r\n    console.log('Loading chat histories for agent:', agentName || 'all agents');\r\n\r\n    this.agentChatService.histories(agentName).subscribe({\r\n      next: (conversations) => {\r\n        // Reverse the conversations array so the last conversation appears first\r\n        let convArr = Array.isArray(conversations) ? conversations : [conversations];\r\n        convArr = convArr.reverse();\r\n        console.log(convArr)\r\n\r\n        // Reverse the histories inside each conversation so the latest message is first\r\n        convArr.forEach(c => {\r\n          if (c.histories && Array.isArray(c.histories)) {\r\n            c.histories = c.histories.reverse();\r\n            // Set the current response index to the latest response for each history\r\n            c.histories.forEach(history => {\r\n              if (history.responses && history.responses.length > 0) {\r\n                this.currentResponseIndexes[history.id || ''] = history.responses.length - 1;\r\n              }\r\n            });\r\n          }\r\n        });\r\n\r\n        this.conversations = convArr;\r\n\r\n        // Set hasNoHistories flag based on conversations returned\r\n        this.hasNoHistories = this.conversations.length === 0 ||\r\n          this.conversations.every(c => !c.histories || c.histories.length === 0);\r\n\r\n        if (this.conversations.length > 0) {\r\n          // Set current conversation to first agent or selected agent\r\n          const foundConversation = agentName\r\n            ? this.conversations.find(c => c.agentName === agentName)\r\n            : this.conversations[0];          if (foundConversation) {\r\n            this.currentConversation = foundConversation;\r\n\r\n            // Check if this conversation has any histories\r\n            if (foundConversation.histories && foundConversation.histories.length > 0) {\r\n              this.hasNoHistories = false;\r\n            }\r\n\r\n            // Set selected agent from conversation if not already set from URL\r\n            if (this.currentConversation.agentName && !this.selectedAgent) {\r\n              this.selectedAgent = this.currentConversation.agentName;\r\n              this.userInput.agentName = this.currentConversation.agentName;\r\n            }\r\n          } else if (agentName) {\r\n            // If we're searching for a specific agent but didn't find conversations,\r\n            // create a new conversation for that agent\r\n            this.currentConversation = new AgentChatConversationDto({\r\n              agentName: agentName,\r\n              histories: []\r\n            });\r\n            this.conversations.push(this.currentConversation);\r\n          }\r\n        } else if (agentName) {\r\n          // No conversations exist but we have an agent name, create a new conversation\r\n          this.currentConversation = new AgentChatConversationDto({\r\n            agentName: agentName,\r\n            histories: []\r\n          });\r\n          this.conversations.push(this.currentConversation);\r\n          this.hasNoHistories = true;\r\n        } else {\r\n          // No conversations at all\r\n          this.hasNoHistories = true;\r\n        }\r\n\r\n        this.isMessageLoading = false;\r\n        setTimeout(() => this.scrollToBottom(), 100);\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading agent chat histories:', error);\r\n        this.message.error('Failed to load chat histories');\r\n        this.isMessageLoading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Loads workspaces from the service\r\n   */\r\n  loadWorkspaces() {\r\n    this.workspaceService.getAll().subscribe({\r\n      next: (workspaces) => {\r\n        this.workspaces = workspaces;\r\n        if (this.workspaces.length > 0) {\r\n          this.selectedWorkspace = this.workspaces[0].title;\r\n          this.loadAgentsForWorkspace(this.selectedWorkspace);\r\n        }\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading workspaces:', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Loads agents for a specific workspace\r\n   */\r\n  loadAgentsForWorkspace(workspaceName: string) {\r\n    if (!workspaceName) return;\r\n\r\n    this.agentDefinition.getAllByWorkspace(workspaceName).subscribe({\r\n      next: (agents) => {\r\n        this.workspaceAgents = agents;\r\n        // Update the mentionFilteredAgents with our agents\r\n        this.mentionFilteredAgents = [...this.workspaceAgents];\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading agents for workspace:', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Adds a suggestion to the chat input\r\n   */\r\n  addSuggestionToChat(suggestion: any) {\r\n    this.userInput.question = suggestion.text;\r\n    if (suggestion.isUserPrompt) {\r\n      this.sendMessage();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Helper function to extract content for different response types\r\n   * These functions would normally parse special format responses\r\n   * For now they act as basic stubs\r\n   */  hasSqlContent(text: string): boolean {\r\n    return !!text && text.includes('```sql');\r\n  }\r\n\r\n  extractNonSqlContent(text: string): string {\r\n    if (!text) return '';\r\n    return text.replace(/```sql[\\s\\S]*?```/g, '');\r\n  }\r\n\r\n  extractSqlContent(text: string): string {\r\n    if (!text || !this.hasSqlContent(text)) return '';\r\n    const match = text.match(/```sql([\\s\\S]*?)```/);\r\n    return match ? match[1].trim() : '';\r\n  }\r\n\r\n  extractBlogTitle(text: string): string {\r\n    if (!text) return 'Blog Post';\r\n    const titleMatch = text.match(/^#\\s*(.*?)$/m);\r\n    return titleMatch ? titleMatch[1] : 'Blog Post';\r\n  }\r\n\r\n  extractEmailSubject(text: string): string {\r\n    if (!text) return '';\r\n    const subjectMatch = text.match(/Subject:(.*?)$/m);\r\n    return subjectMatch ? subjectMatch[1].trim() : '';\r\n  }\r\n\r\n  extractEmailTo(text: string): string {\r\n    if (!text) return '';\r\n    const toMatch = text.match(/To:(.*?)$/m);\r\n    return toMatch ? toMatch[1].trim() : '';\r\n  }\r\n\r\n  extractEmailCc(text: string): string {\r\n    if (!text) return '';\r\n    const ccMatch = text.match(/Cc:(.*?)$/m);\r\n    return ccMatch ? ccMatch[1].trim() : '';\r\n  }\r\n\r\n  extractEmailBody(text: string): string {\r\n    if (!text) return '';\r\n    const parts = text.split(/^Body:/m);\r\n    return parts.length > 1 ? parts[1].trim() : text;\r\n  }\r\n\r\n  /**\r\n   * Mentions and prompt handling\r\n   */\r\n  selectMentionedAgent(agent: any) {\r\n    if (!agent) return;\r\n    this.userInput.agentName = agent.agentName;\r\n    this.mentionDropdownVisible = false;\r\n  }\r\n\r\n  selectPrompt(prompt: any) {\r\n    if (!prompt) return;\r\n    this.userInput.question = prompt.prompt;\r\n    this.showPromptDialog = false;\r\n    this.adjustInputHeight();\r\n  }\r\n\r\n  /**\r\n   * Loads the saved width from localStorage with validation (enhanced from hero component)\r\n   */\r\n  private loadSavedRightSidebarWidth(): void {\r\n    try {\r\n      const savedWidth = localStorage.getItem(this.RIGHT_SIDEBAR_STORAGE_KEY);\r\n      if (savedWidth) {\r\n        const parsedWidth = parseInt(savedWidth, 10);\r\n        // Validate the saved width to ensure it's within acceptable range\r\n        if (!isNaN(parsedWidth) && parsedWidth >= this.minRightSidebarWidth && parsedWidth <= this.maxRightSidebarWidth) {\r\n          this.rightSidebarWidth = parsedWidth;\r\n          this.lastValidWidth = parsedWidth; // Store as last valid width\r\n        } else {\r\n          // If saved width is invalid, use the default width\r\n          this.rightSidebarWidth = this.DEFAULT_RIGHT_SIDEBAR_WIDTH;\r\n          // Update localStorage with the default width\r\n          this.saveRightSidebarWidth(this.DEFAULT_RIGHT_SIDEBAR_WIDTH);\r\n        }\r\n      } else {\r\n        // If no saved width, use the default width\r\n        this.rightSidebarWidth = this.DEFAULT_RIGHT_SIDEBAR_WIDTH;\r\n        // Save the default width to localStorage\r\n        this.saveRightSidebarWidth(this.DEFAULT_RIGHT_SIDEBAR_WIDTH);\r\n      }\r\n\r\n      // Update split sizes based on sidebar visibility\r\n      this.updateSplitSizes();\r\n    } catch (error) {\r\n      console.error('Error loading saved width:', error);\r\n      this.rightSidebarWidth = this.DEFAULT_RIGHT_SIDEBAR_WIDTH;\r\n      this.saveRightSidebarWidth(this.DEFAULT_RIGHT_SIDEBAR_WIDTH);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Saves the width to localStorage\r\n   * @param width The width to save\r\n   */\r\n  private saveRightSidebarWidth(width: number): void {\r\n    try {\r\n      localStorage.setItem(this.RIGHT_SIDEBAR_STORAGE_KEY, width.toString());\r\n    } catch (error) {\r\n      console.error('Error saving width to localStorage:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Scrolls the chat container to the bottom\r\n   */\r\n  scrollToBottom() {\r\n    try {\r\n      if (this.chatContainer) {\r\n        setTimeout(() => {\r\n          this.chatContainer.nativeElement.scrollTop =\r\n            this.chatContainer.nativeElement.scrollHeight;\r\n        }, 100);\r\n      }\r\n    } catch (err) {\r\n      console.error('Error scrolling to bottom:', err);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Adjusts the height of the input textarea\r\n   */\r\n  adjustInputHeight() {\r\n    if (this.chatInput && this.chatInput.nativeElement) {\r\n      const element = this.chatInput.nativeElement;\r\n      element.style.height = 'auto';\r\n      element.style.height = Math.min(element.scrollHeight, 200) + 'px';\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Sends a message to agent chat API\r\n   */\r\n  async sendMessage() {\r\n    if (!this.userInput.question?.trim()) return;\r\n    if (!this.userInput.agentName && this.selectedAgent) {\r\n      this.userInput.agentName = this.selectedAgent;\r\n    }\r\n\r\n    // Store the original message for immediate display\r\n    const originalMessage = this.userInput.question;\r\n    const agentName = this.userInput.agentName || this.selectedAgent;\r\n\r\n    // Find current conversation or create a new one\r\n    if (this.currentConversation.agentName !== agentName) {\r\n      const foundConversation = this.conversations.find(c => c.agentName === agentName);\r\n\r\n      if (foundConversation) {\r\n        this.currentConversation = foundConversation;\r\n      } else {\r\n        this.currentConversation = new AgentChatConversationDto({\r\n          agentName: agentName,\r\n          histories: []\r\n        });\r\n\r\n        this.conversations.push(this.currentConversation);\r\n      }\r\n    }\r\n\r\n    // Ensure histories array exists\r\n    if (!this.currentConversation.histories) {\r\n      this.currentConversation.histories = [];\r\n    }\r\n\r\n    // Create a temporary message object to show immediately in the UI\r\n    const tempMessage = new AgentChatHistoryDto({\r\n      id: 'temp-' + Date.now(), // Temporary ID\r\n      question: originalMessage,\r\n      responses: [], // Empty responses initially\r\n      timestamp: DateTime.now()\r\n    });\r\n    tempMessage.isLoading = true;\r\n\r\n    // Add the temporary message to the conversation immediately\r\n    this.currentConversation.histories.push(tempMessage);\r\n\r\n    // Update hasNoHistories flag since we now have a conversation\r\n    this.hasNoHistories = false;\r\n\r\n    // Clear input immediately for better UX\r\n    this.userInput.question = '';\r\n    this.adjustInputHeight();\r\n\r\n    // Set loading state\r\n    this.isMessageLoading = true;\r\n\r\n    // Scroll to show the new message\r\n    setTimeout(() => this.scrollToBottom(), 100);\r\n\r\n    console.log('Sending message to agent:', agentName);\r\n\r\n    // Set up streaming for this message\r\n    this.currentStreamingMessageId = tempMessage.id;\r\n    this.isStreamingActive = true;\r\n\r\n    // Send the actual request\r\n    const requestDto = new AgentChatRequestDto({\r\n      question: originalMessage,\r\n      agentName: agentName\r\n    });\r\n\r\n    this.agentChatService.sendAgentMessage(requestDto).subscribe({\r\n      next: (response) => {\r\n        if (!response) {\r\n          console.error('Received null response from agent chat service');\r\n          this.message.error('Received invalid response from server');\r\n          this.isMessageLoading = false;\r\n          return;\r\n        }\r\n\r\n        // Find and replace the temporary message with the actual response\r\n        if (this.currentConversation.histories) {\r\n          const tempIndex = this.currentConversation.histories.findIndex(h => h.id === tempMessage.id);\r\n          if (tempIndex !== -1) {\r\n            // Replace the temporary message with the actual response\r\n            this.currentConversation.histories[tempIndex] = response;\r\n\r\n            // Set the current response index to show the latest response\r\n            if (response.id) {\r\n              this.currentResponseIndexes[response.id] = (response.responses || []).length - 1;\r\n            }\r\n          } else {\r\n            // Fallback: just add the response if temp message not found\r\n            this.currentConversation.histories.push(response);\r\n            if (response.id) {\r\n              this.currentResponseIndexes[response.id] = (response.responses || []).length - 1;\r\n            }\r\n          }\r\n        }\r\n\r\n        // Clear loading state\r\n        this.isMessageLoading = false;\r\n        this.scrollToBottom();\r\n      },\r\n      error: (error) => {\r\n        console.error('Error sending agent message:', error);\r\n        this.message.error('Failed to send message');\r\n\r\n        // Remove the temporary message on error\r\n        if (this.currentConversation.histories) {\r\n          const tempIndex = this.currentConversation.histories.findIndex(h => h.id === tempMessage.id);\r\n          if (tempIndex !== -1) {\r\n            this.currentConversation.histories.splice(tempIndex, 1);\r\n          }\r\n        }\r\n\r\n        // Restore the original message to the input\r\n        this.userInput.question = originalMessage;\r\n        this.adjustInputHeight();\r\n\r\n        this.isMessageLoading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Regenerate response for a specific chat message\r\n   */\r\n  regenerateResponse(history: AgentChatHistoryDto) {\r\n    if (!history || !history.id) return;\r\n\r\n    // Set loading state for this specific message\r\n    history.isLoading = true;\r\n    const agentName = this.currentConversation?.agentName;\r\n\r\n    this.agentChatService.agentChatRegenerate(history.id, agentName).subscribe({\r\n      next: (response) => {\r\n        // Clear loading state for this message\r\n        history.isLoading = false;\r\n\r\n        // Update the responses in the history\r\n        if (!history.responses) {\r\n          history.responses = [];\r\n        }\r\n\r\n        // Create a new response from regenerated content\r\n        if (response.responseText) {\r\n          const newResponse = new AgentChatResponseDto({\r\n            id: response.id || '',\r\n            responseText: response.responseText,\r\n            chatSource: 'Regenerated',\r\n            timestamp: DateTime.now()\r\n          });\r\n          history.responses.push(newResponse);\r\n\r\n          // Update the current response index to show the latest response\r\n          const historyId = history.id || '';\r\n          this.currentResponseIndexes[historyId] = history.responses.length - 1;\r\n        }\r\n\r\n        this.scrollToBottom();\r\n      },\r\n      error: (error) => {\r\n        console.error('Error regenerating response:', error);\r\n        this.message.error('Failed to regenerate response');\r\n\r\n        // Clear loading state for this message on error\r\n        history.isLoading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Edit an existing message\r\n   */\r\n  editMessage(history: AgentChatHistoryDto) {\r\n    if (!history) return;\r\n\r\n    // Store the edited question\r\n    const editedQuestion = history.question;\r\n\r\n    // Exit editing mode\r\n    history.editingMode = false;\r\n\r\n    // Set the input to the edited question and send it\r\n    this.userInput.question = editedQuestion;\r\n    this.sendMessage();\r\n  }\r\n\r\n  /**\r\n   * Functions for voice commands and recording\r\n   */\r\n  startRecording() {\r\n    this.isRecording = true;\r\n    // Placeholder for actual recording implementation\r\n    this.message.info('Recording started');\r\n  }\r\n\r\n  stopRecording() {\r\n    this.isRecording = false;\r\n    // Placeholder for actual recording implementation\r\n    this.message.info('Recording stopped');\r\n  }\r\n\r\n  /**\r\n   * Text-to-speech functionality\r\n   */\r\n  textToSpeech(text: string) {\r\n    // Placeholder for actual TTS implementation\r\n    this.message.info('Text-to-speech started');\r\n  }\r\n\r\n  stopSpeech() {\r\n    // Placeholder for stopping TTS\r\n    this.message.info('Text-to-speech stopped');\r\n  }\r\n\r\n  // Input handling methods\r\n  handleKeyDown(event: any) {\r\n    if (event.key === 'Enter' && !event.shiftKey) {\r\n      event.preventDefault();\r\n      this.sendMessage();\r\n    }\r\n  }\r\n\r\n  onInput(event: any) {\r\n    this.adjustInputHeight();\r\n  }\r\n\r\n  onChatScroll() {\r\n    if (this.chatContainer) {\r\n      const element = this.chatContainer.nativeElement;\r\n      this.showScrollButton = element.scrollHeight - element.scrollTop - element.clientHeight > 100;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Updates the split sizes based on sidebar visibility (from hero component)\r\n   */\r\n  private updateSplitSizes(): void {\r\n    if (this.showSearchResultsSidebar || this.isAgentSidebarOpen) {\r\n      // Calculate the percentage for the right sidebar\r\n      const windowWidth = window.innerWidth;\r\n      // Ensure the sidebar takes at least 20% of the window width for better visibility\r\n      const minPercentage = 20;\r\n      const calculatedPercentage = (this.rightSidebarWidth / windowWidth) * 100;\r\n      this.rightSidebarSplitSize = Math.max(calculatedPercentage, minPercentage);\r\n      this.mainContentSplitSize = 100 - this.rightSidebarSplitSize;\r\n    } else {\r\n      // If no sidebar is visible, set main content to 100%\r\n      this.mainContentSplitSize = 100;\r\n      this.rightSidebarSplitSize = 0;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Toggles the search results sidebar visibility and populates it with data (from hero component)\r\n   * @param chatSourceDescriptions Optional array of source descriptions to display\r\n   * @param sourceName Optional name of the source (e.g., \"Google\", \"Bing\")\r\n   */\r\n  toggleSearchResultsSidebar(chatSourceDescriptions?: any[], sourceName: string = ''): void {\r\n    console.log('Toggling search results sidebar. Current state:', this.showSearchResultsSidebar);\r\n\r\n    // If we have chat source descriptions, use them\r\n    if (chatSourceDescriptions && chatSourceDescriptions.length > 0) {\r\n      console.log('Using provided chat source descriptions:', chatSourceDescriptions);\r\n\r\n      // Store the source name\r\n      this.currentSourceName = sourceName;\r\n\r\n      // Format the search results\r\n      this.searchResults = chatSourceDescriptions.map(desc => ({\r\n        title: desc.title || 'No Title',\r\n        url: desc.url || '',\r\n        domain: desc.url,\r\n        description: desc.description || 'No description available'\r\n      }));\r\n\r\n      // Make sure the sidebar is open\r\n      this.showSearchResultsSidebar = true;\r\n\r\n      // Close agent sidebar if it's open\r\n      if (this.isAgentSidebarOpen) {\r\n        this.isAgentSidebarOpen = false;\r\n      }\r\n    } else {\r\n      // If no descriptions provided, toggle the sidebar visibility\r\n      this.showSearchResultsSidebar = !this.showSearchResultsSidebar;\r\n    }\r\n\r\n    // Update split sizes based on sidebar visibility\r\n    this.updateSplitSizes();\r\n\r\n    // Add a small delay to allow the DOM to update before any animations\r\n    setTimeout(() => {\r\n      // Force a layout recalculation to ensure smooth transitions\r\n      window.dispatchEvent(new Event('resize'));\r\n    }, 10);\r\n  }\r\n\r\n  // UI control methods\r\n  toggleAgentSidebar() {\r\n    this.isAgentSidebarOpen = !this.isAgentSidebarOpen;\r\n\r\n    // Close search results sidebar if agent sidebar is opening\r\n    if (this.isAgentSidebarOpen && this.showSearchResultsSidebar) {\r\n      this.showSearchResultsSidebar = false;\r\n    }\r\n\r\n    // Update split sizes based on sidebar visibility\r\n    this.updateSplitSizes();\r\n\r\n    // Add a small delay to allow the DOM to update before any animations\r\n    setTimeout(() => {\r\n      // Force a layout recalculation to ensure smooth transitions\r\n      window.dispatchEvent(new Event('resize'));\r\n    }, 10);\r\n  }\r\n\r\n  /**\r\n   * Enhanced splitter methods from hero component\r\n   */\r\n  onSplitDragEnd(event: any): void {\r\n    // Get the actual width from the split area\r\n    const splitArea = event.sizes[1]; // Right sidebar is the second area\r\n    if (splitArea && splitArea > 0) {\r\n      const windowWidth = window.innerWidth;\r\n      this.rightSidebarWidth = Math.round((splitArea / 100) * windowWidth);\r\n\r\n      // Validate and constrain the width\r\n      this.rightSidebarWidth = Math.max(this.minRightSidebarWidth,\r\n        Math.min(this.rightSidebarWidth, this.maxRightSidebarWidth));\r\n\r\n      // Check if width is below auto-close threshold\r\n      if (this.rightSidebarWidth < this.autoCloseThreshold) {\r\n        this.showSearchResultsSidebar = false;\r\n        this.isAgentSidebarOpen = false;\r\n        this.updateSplitSizes();\r\n      } else {\r\n        // Store as last valid width\r\n        this.lastValidWidth = this.rightSidebarWidth;\r\n        // Save to localStorage\r\n        this.saveRightSidebarWidth(this.rightSidebarWidth);\r\n      }\r\n    }\r\n\r\n    this.isDragging = false;\r\n  }\r\n\r\n  onSplitDragProgress(event: any): void {\r\n    this.isDragging = true;\r\n\r\n    // Optional: Update width in real-time during drag\r\n    const splitArea = event.sizes[1];\r\n    if (splitArea && splitArea > 0) {\r\n      const windowWidth = window.innerWidth;\r\n      const newWidth = Math.round((splitArea / 100) * windowWidth);\r\n\r\n      // Check if dragging below auto-close threshold\r\n      if (newWidth < this.autoCloseThreshold) {\r\n        // Visual feedback that sidebar will close\r\n        // You could add a visual indicator here\r\n      }\r\n    }\r\n  }\r\n\r\n  onGutterDoubleClick(event: any): void {\r\n    if (this.showSearchResultsSidebar || this.isAgentSidebarOpen) {\r\n      // Close the sidebar\r\n      this.showSearchResultsSidebar = false;\r\n      this.isAgentSidebarOpen = false;\r\n    } else {\r\n      // Open the last active sidebar (prioritize search results)\r\n      if (this.searchResults.length > 0) {\r\n        this.showSearchResultsSidebar = true;\r\n      } else {\r\n        this.isAgentSidebarOpen = true;\r\n      }\r\n\r\n      // Restore the last valid width\r\n      this.rightSidebarWidth = this.lastValidWidth;\r\n    }\r\n\r\n    this.updateSplitSizes();\r\n  }\r\n\r\n  selectWorkspace(workspace: string) {\r\n    this.selectedWorkspace = workspace;\r\n    this.loadAgentsForWorkspace(workspace);\r\n  }\r\n  selectAgent(agent: any) {\r\n    this.selectedAgent = agent.agentName || agent;\r\n    this.userInput.agentName = this.selectedAgent;\r\n\r\n    // Navigate to the agent's chat URL\r\n    this.router.navigate(['/agent-chat', this.selectedAgent]);\r\n\r\n    // Close sidebar if open\r\n    if (this.isAgentSidebarOpen) {\r\n      this.isAgentSidebarOpen = false;\r\n      this.rightSidebarSplitSize = 0;\r\n      this.mainContentSplitSize = 100;\r\n    }\r\n  }\r\n  clearSelectedAgent() {\r\n    this.selectedAgent = '';\r\n    this.userInput.agentName = '';\r\n\r\n    // Navigate back to the base agent-chat URL\r\n    this.router.navigate(['/agent-chat']);\r\n\r\n    // Load all chat histories\r\n    this.loadAgentChatHistories();\r\n  }\r\n  // Helper methods\r\n  copyContent(content: string | undefined) {\r\n    if (content) {\r\n      navigator.clipboard.writeText(content);\r\n      this.message.success('Content copied to clipboard');\r\n    }\r\n  }\r\n\r\n  copySqlContent(content: string) {\r\n    this.copyContent(content);\r\n  }\r\n\r\n  copyBlogContent(content: string) {\r\n    this.copyContent(content);\r\n  }\r\n\r\n  copyEmailContent(content: string) {\r\n    this.copyContent(content);\r\n  }\r\n\r\n  // Response navigation methods\r\n  getCurrentResponseIndex(historyId: string): number {\r\n    return this.currentResponseIndexes[historyId] || 0;\r\n  }\r\n\r\n  getCurrentResponse(history: AgentChatHistoryDto): AgentChatResponseDto | null {\r\n    if (!history || !history.responses || history.responses.length === 0) return null;\r\n\r\n    const historyId = history.id || '';\r\n    const currentIndex = this.getCurrentResponseIndex(historyId);\r\n    const response = history.responses[currentIndex];\r\n\r\n    return response || null;\r\n  }\r\n\r\n  goToPreviousResponse(history: AgentChatHistoryDto) {\r\n    if (!history || !history.responses || history.responses.length <= 1) return;\r\n\r\n    const historyId = history.id || '';\r\n    const currentIndex = this.getCurrentResponseIndex(historyId);\r\n\r\n    if (currentIndex > 0) {\r\n      this.currentResponseIndexes[historyId] = currentIndex - 1;\r\n    }\r\n  }\r\n\r\n  nextResponse(history: AgentChatHistoryDto) {\r\n    if (!history || !history.responses || history.responses.length <= 1) return;\r\n\r\n    const historyId = history.id || '';\r\n    const currentIndex = this.getCurrentResponseIndex(historyId);\r\n\r\n    if (currentIndex < history.responses.length - 1) {\r\n      this.currentResponseIndexes[historyId] = currentIndex + 1;\r\n    }\r\n  }\r\n\r\n  getResponseNavigation(history: AgentChatHistoryDto): string {\r\n    if (!history || !history.responses || history.responses.length <= 1) return '';\r\n\r\n    const historyId = history.id || '';\r\n    const currentIndex = this.getCurrentResponseIndex(historyId);\r\n\r\n    return `${currentIndex + 1}/${history.responses.length}`;\r\n  }\r\n\r\n  openSqlConnectionDialog(query: string) {\r\n    // Placeholder for SQL connection dialog\r\n    this.message.info('SQL Connection dialog would open here');\r\n  }\r\n\r\n  openBlogShareDialog(content: string) {\r\n    // Placeholder for blog sharing\r\n    this.message.info('Blog sharing dialog would open here');\r\n  }\r\n\r\n  sendEmail(content: string) {\r\n    // Placeholder for email sending\r\n    this.message.info('Email sending would happen here');\r\n  }\r\n\r\n  /**\r\n   * Handles clicking on source indicators in chat responses (from hero component)\r\n   * @param response The response object containing source descriptions\r\n   */\r\n  onSourceClick(response: any): void {\r\n    if (response && response.chatSourceDescriptions && response.chatSourceDescriptions.length > 0) {\r\n      console.log('Source clicked, opening sidebar with descriptions:', response.chatSourceDescriptions);\r\n\r\n      // Extract source name from the response\r\n      const sourceName = response.chatSource || 'Web Search';\r\n\r\n      // Open the source references sidebar with the descriptions\r\n      this.toggleSearchResultsSidebar(response.chatSourceDescriptions, sourceName);\r\n    } else {\r\n      console.log('No source descriptions found for this response');\r\n      // Still open the sidebar but with empty results\r\n      this.toggleSearchResultsSidebar([], 'No Sources');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Checks if a response has source descriptions that can be displayed\r\n   * @param response The response object to check\r\n   * @returns True if the response has source descriptions\r\n   */\r\n  hasSourceDescriptions(response: any): boolean {\r\n    return response &&\r\n           response.chatSourceDescriptions &&\r\n           Array.isArray(response.chatSourceDescriptions) &&\r\n           response.chatSourceDescriptions.length > 0;\r\n  }\r\n\r\n  /**\r\n   * Gets the count of source descriptions for a response\r\n   * @param response The response object\r\n   * @returns The number of source descriptions\r\n   */\r\n  getSourceCount(response: any): number {\r\n    if (this.hasSourceDescriptions(response)) {\r\n      return response.chatSourceDescriptions.length;\r\n    }\r\n    return 0;\r\n  }\r\n}\r\n\r\n// Add this to the AgentChatHistoryDto class to support editing and loading states\r\ndeclare module '../../../shared/service-proxies/service-proxies' {\r\n  interface AgentChatHistoryDto {\r\n    editingMode?: boolean;\r\n    isLoading?: boolean;\r\n  }\r\n\r\n  interface AgentChatResponseDto {\r\n    copied?: boolean;\r\n  }\r\n}\r\n"], "mappings": ";;;;AAAA,SACEA,SAAS,EACTC,SAAS,EAGTC,MAAM,EAGNC,YAAY,EACZC,IAAI,QAGC,eAAe;AACtB,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,MAAM,QAAQ,QAAQ;AAE/B,SAASC,cAAc,EAAEC,MAAM,QAAQ,iBAAiB;AACxD,SACEC,2BAA2B,EAC3BC,qBAAqB,EACrBC,qBAAqB,EACrBC,mBAAmB,EACnBC,mBAAmB,EACnBC,wBAAwB,EACxBC,oBAAoB,QAEf,iDAAiD;AACxD,SAASC,kBAAkB,QAAQ,sDAAsD;AACzF,SAASC,WAAW,QAAQ,uCAAuC;AACnE,SAASC,WAAW,QAAQ,6BAA6B;AACzD,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,aAAa,QAAQ,qBAAqB;AACnD,SAASC,eAAe,QAAQ,uBAAuB;AACvD,SAASC,aAAa,EAAEC,cAAc,QAAQ,qBAAqB;AACnE,SAASC,gBAAgB,QAAQ,uBAAuB;AACxD,SAASC,cAAc,QAAQ,cAAc;AAC7C,SAASC,yBAAyB,QAAQ,qFAAqF;AAC/H,SAASC,qBAAqB,QAAQ,6EAA6E;AACnH,SAASC,kBAAkB,QAAQ,eAAe;AAClD,SAASC,eAAe,QAAQ,kCAAkC;AAClE,SAASC,QAAQ,QAAQ,OAAO;AAEhC;AAKO,IAAMC,gBAAgB,GAAtB,MAAMA,gBAAgB;EAC3BC,SAASA,CAACC,KAAoB;IAC5B,IAAI,CAACA,KAAK,EAAE,OAAO,EAAE;IAErB,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,KAAK,CAAC;IAC5B,MAAMG,GAAG,GAAG,IAAID,IAAI,EAAE;IACtB,MAAME,QAAQ,GAAGD,GAAG,CAACE,OAAO,EAAE,GAAGJ,IAAI,CAACI,OAAO,EAAE;IAC/C,MAAMC,aAAa,GAAGC,IAAI,CAACC,KAAK,CAACJ,QAAQ,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC;IACxD,MAAMK,WAAW,GAAGF,IAAI,CAACC,KAAK,CAACJ,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAC3D,MAAMM,UAAU,GAAGH,IAAI,CAACC,KAAK,CAACJ,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE/D,IAAIE,aAAa,GAAG,CAAC,EAAE;MACrB,OAAOK,OAAO,CAACC,OAAO,CAAC,UAAU,CAAC;KACnC,MAAM,IAAIN,aAAa,GAAG,EAAE,EAAE;MAC7B,OAAOK,OAAO,CAACC,OAAO,CACpB,GAAGN,aAAa,UAAUA,aAAa,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,MAAM,CAC7D;KACF,MAAM,IAAIG,WAAW,GAAG,EAAE,EAAE;MAC3B,OAAOE,OAAO,CAACC,OAAO,CACpB,GAAGH,WAAW,QAAQA,WAAW,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,MAAM,CACvD;KACF,MAAM,IAAIC,UAAU,GAAG,CAAC,EAAE;MACzB,OAAOC,OAAO,CAACC,OAAO,CACpB,GAAGF,UAAU,OAAOA,UAAU,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,MAAM,CACpD;KACF,MAAM;MACL,OAAOC,OAAO,CAACC,OAAO,CAACX,IAAI,CAACY,kBAAkB,EAAE,CAAC;;EAErD;CACD;AA7BYf,gBAAgB,GAAAgB,UAAA,EAJ5B9C,IAAI,CAAC;EACJ+C,IAAI,EAAE,cAAc;EACpBC,UAAU,EAAE;CACb,CAAC,C,EACWlB,gBAAgB,CA6B5B;;AAuBM,IAAMmB,kBAAkB,GAAxB,MAAMA,kBAAkB;EAsE7BC,YACUC,MAAc,EACdC,WAAwB,EACxBC,eAA4C,EAC5CC,SAAuB,EACvBC,WAAwB,EACzBC,IAAiB,EAChBC,OAAyB,EACzBC,gBAAuC,EACvCC,KAAqB,EACrBC,gBAAuC;IATvC,KAAAT,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,WAAW,GAAXA,WAAW;IACZ,KAAAC,IAAI,GAAJA,IAAI;IACH,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAzE1B;IACA,KAAAC,SAAS,GAAwB,IAAInD,mBAAmB,EAAE;IAE1D;IACA,KAAAoD,aAAa,GAAU,EAAE;IACzB,KAAAC,mBAAmB,GAA6B,IAAInD,wBAAwB,CAAC;MAC3EoD,SAAS,EAAE,EAAE;MACbC,SAAS,EAAE;KACZ,CAAC;IACF,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,gBAAgB,GAAG,EAAE;IAErB;IACA,KAAAC,aAAa,GAAU,EAAE;IACzB,KAAAC,iBAAiB,GAAG,EAAE;IAEtB;IACA,KAAAC,sBAAsB,GAA8B,EAAE;IAEtD;IACA,KAAAC,iBAAiB,GAAG,KAAK;IAGzB;IACA,KAAAC,UAAU,GAAQ,EAAE;IACpB,KAAAC,iBAAiB,GAAG,EAAE;IACtB,KAAAC,eAAe,GAAQ,EAAE;IACzB;IACA,KAAAC,gBAAgB,GAAY,KAAK;IACjC,KAAAC,iBAAiB,GAAY,KAAK;IAClC,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,aAAa,GAAG,EAAE;IAClB,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,iBAAiB,GAAG,aAAa;IACjC,KAAAC,sBAAsB,GAAG,KAAK;IAC9B,KAAAC,qBAAqB,GAAU,EAAE;IACjC,KAAAC,oBAAoB,GAAG,CAAC;IACxB,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,eAAe,GAAU,EAAE;IAC3B,KAAAC,mBAAmB,GAAG,CAAC;IACvB,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,cAAc,GAAG,IAAI;IAErB;IACS,KAAAC,2BAA2B,GAAW,GAAG,CAAC,CAAC;IAC3C,KAAAC,yBAAyB,GAAW,4BAA4B,CAAC,CAAC;IAC3E,KAAAC,iBAAiB,GAAW,IAAI,CAACF,2BAA2B,CAAC,CAAC;IAC9D,KAAAG,UAAU,GAAY,KAAK;IAC3B,KAAAC,oBAAoB,GAAW,GAAG,CAAC,CAAC;IACpC,KAAAC,oBAAoB,GAAW,GAAG,CAAC,CAAC;IACpC,KAAAC,kBAAkB,GAAW,GAAG,CAAC,CAAC;IAC1B,KAAAC,cAAc,GAAW,IAAI,CAACP,2BAA2B,CAAC,CAAC;IAEnE;IACA,KAAAQ,oBAAoB,GAAW,GAAG,CAAC,CAAC;IACpC,KAAAC,qBAAqB,GAAW,CAAC,CAAC,CAAC;IACnC,KAAAC,wBAAwB,GAAY,KAAK;IA0BzC,KAAAC,KAAK,GAAGtG,MAAM,CAACO,cAAc,CAAC;IAC9B,KAAAgG,eAAe,GAAGvG,MAAM,CAAC8B,eAAe,CAAC;IARvC;IACAxB,MAAM,CAACkG,UAAU,CAAC;MAChBC,MAAM,EAAE,IAAI;MACZC,GAAG,EAAE;KACN,CAAC;EACJ;EAIAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,0BAA0B,EAAE;IACjC,IAAI,CAACC,cAAc,EAAE;IAErB;IACA,IAAI,CAACC,0BAA0B,EAAE;IAEjC;IACA,IAAI,CAACR,KAAK,CAACS,QAAQ,CAACC,SAAS,CAACC,MAAM,IAAG;MACrC,MAAM/C,SAAS,GAAG+C,MAAM,CAACC,GAAG,CAAC,MAAM,CAAC;MACpC,IAAIhD,SAAS,EAAE;QACb,IAAI,CAACc,aAAa,GAAGd,SAAS;QAC9B,IAAI,CAACH,SAAS,CAACG,SAAS,GAAGA,SAAS;;MAGtC;MACA,IAAI,CAACiD,sBAAsB,CAACjD,SAAS,IAAIkD,SAAS,CAAC;IACrD,CAAC,CAAC;EACJ;EAEAC,eAAeA,CAAA;IACb,IAAI,CAACC,cAAc,EAAE;IACrB,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACC,mBAAmB,EAAE;MAC5B,IAAI,CAACA,mBAAmB,CAACC,WAAW,EAAE;;EAE1C;EAMAC,cAAcA,CAACC,KAAU;IACvB;IACA,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEA;;;EAGQf,0BAA0BA,CAAA;IAChC,IAAI,CAACW,mBAAmB,GAAG,IAAI,CAAChE,WAAW,CAACqE,gBAAgB,CAACd,SAAS,CACpE,CAAC;MAAErD,OAAO;MAAEoE,OAAO;MAAEC;IAAU,CAAE,KAAI;MACnCC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEvE,OAAO,CAAC;MAEtD,IAAIoE,OAAO,EAAE;QACX,IAAI,CAAC3D,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAACK,iBAAiB,GAAG,KAAK;QAC9B,IAAI,CAAC0D,yBAAyB,GAAGf,SAAS;QAE1C;QACA,IAAI,IAAI,CAACnD,mBAAmB,EAAEE,SAAS,EAAE;UACvC,MAAMiE,WAAW,GAAG,IAAI,CAACnE,mBAAmB,CAACE,SAAS,CAAC,IAAI,CAACF,mBAAmB,CAACE,SAAS,CAACkE,MAAM,GAAG,CAAC,CAAC;UACrG,IAAID,WAAW,IAAIA,WAAW,CAACE,EAAE,KAAK,IAAI,CAACH,yBAAyB,EAAE;YACpE,MAAMI,WAAW,GAAG,IAAI,CAACC,kBAAkB,CAAC7E,OAAO,CAAC;YACpD,IAAI,CAACyE,WAAW,CAACK,SAAS,EAAE;cAC1BL,WAAW,CAACK,SAAS,GAAG,EAAE;;YAE5BL,WAAW,CAACK,SAAS,CAACC,IAAI,CAAC,IAAI3H,oBAAoB,CAAC;cAClDuH,EAAE,EAAE,EAAE;cACNK,YAAY,EAAEJ,WAAW;cACzBK,UAAU,EAAE,OAAO;cACnBC,SAAS,EAAE9G,QAAQ,CAACM,GAAG;aACxB,CAAC,CAAC;;;OAGR,MAAM;QACL,MAAMkG,WAAW,GAAG,IAAI,CAACC,kBAAkB,CAAC7E,OAAO,CAAC;QACpDsE,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEK,WAAW,CAAC;QAExD,IAAIA,WAAW,IAAI,IAAI,CAAC9D,iBAAiB,IAAI,IAAI,CAAC0D,yBAAyB,EAAE;UAC3E;UACA,IAAIH,UAAU,EAAE;YACd,IAAI,CAACc,uBAAuB,EAAE;YAC9B;;UAGF;UACA,IAAI,IAAI,CAAC7E,mBAAmB,EAAEE,SAAS,EAAE;YACvC,MAAM4E,gBAAgB,GAAG,IAAI,CAAC9E,mBAAmB,CAACE,SAAS,CAAC6E,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACX,EAAE,KAAK,IAAI,CAACH,yBAAyB,CAAC;YAC9G,IAAIY,gBAAgB,EAAE;cACpB;cACA,IAAI,CAACA,gBAAgB,CAACN,SAAS,EAAE;gBAC/BM,gBAAgB,CAACN,SAAS,GAAG,EAAE;;cAGjC;cACA,IAAIM,gBAAgB,CAACN,SAAS,CAACJ,MAAM,KAAK,CAAC,EAAE;gBAC3CU,gBAAgB,CAACN,SAAS,CAACC,IAAI,CAAC,IAAI3H,oBAAoB,CAAC;kBACvDuH,EAAE,EAAE,EAAE;kBACNK,YAAY,EAAEJ,WAAW;kBACzBK,UAAU,EAAE,WAAW;kBACvBC,SAAS,EAAE9G,QAAQ,CAACM,GAAG;iBACxB,CAAC,CAAC;eACJ,MAAM;gBACL;gBACA,MAAM6G,YAAY,GAAGH,gBAAgB,CAACN,SAAS,CAACM,gBAAgB,CAACN,SAAS,CAACJ,MAAM,GAAG,CAAC,CAAC;gBACtFa,YAAY,CAACP,YAAY,GAAG,CAACO,YAAY,CAACP,YAAY,IAAI,EAAE,IAAIJ,WAAW;;cAG7E;cACA,IAAIQ,gBAAgB,CAACI,SAAS,EAAE;gBAC9BJ,gBAAgB,CAACI,SAAS,GAAG,KAAK;gBAClC;gBACCJ,gBAAwB,CAACK,WAAW,GAAG,IAAI;;cAG9C;cACAC,UAAU,CAAC,MAAM,IAAI,CAAC/B,cAAc,EAAE,EAAE,EAAE,CAAC;;;;;IAKrD,CAAC,CACF;EACH;EAEA;;;EAGQwB,uBAAuBA,CAAA;IAC7B,IAAI,IAAI,CAACX,yBAAyB,IAAI,IAAI,CAAClE,mBAAmB,EAAEE,SAAS,EAAE;MACzE,MAAM4E,gBAAgB,GAAG,IAAI,CAAC9E,mBAAmB,CAACE,SAAS,CAAC6E,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACX,EAAE,KAAK,IAAI,CAACH,yBAAyB,CAAC;MAC9G,IAAIY,gBAAgB,EAAE;QACpB;QACCA,gBAAwB,CAACK,WAAW,GAAG,KAAK;QAC7CL,gBAAgB,CAACI,SAAS,GAAG,KAAK;QAClC,IAAI,CAAC1E,iBAAiB,GAAG,KAAK;QAC9B,IAAI,CAAC0D,yBAAyB,GAAGf,SAAS;QAC1C,IAAI,CAAChD,gBAAgB,GAAG,KAAK;QAE7B,IAAI,CAACkD,cAAc,EAAE;;;EAG3B;EAEA;;;EAGQkB,kBAAkBA,CAAC7E,OAAY;IACrC,OAAOA,OAAO,CAACoE,OAAO,GAAG,GAAG,GAAGpE,OAAO,CAACA,OAAO;EAChD;EAEA;;;EAGA2F,kBAAkBA,CAAC3F,OAAY;IAC7B,OAAQA,OAAe,CAACyF,WAAW,KAAK,IAAI;EAC9C;EAEA;;;;EAIAjC,sBAAsBA,CAACjD,SAAkB;IACvC,IAAI,CAACE,gBAAgB,GAAG,IAAI;IAE5B;IACA6D,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEhE,SAAS,IAAI,YAAY,CAAC;IAE3E,IAAI,CAACJ,gBAAgB,CAACK,SAAS,CAACD,SAAS,CAAC,CAAC8C,SAAS,CAAC;MACnDuC,IAAI,EAAGvF,aAAa,IAAI;QACtB;QACA,IAAIwF,OAAO,GAAGC,KAAK,CAACC,OAAO,CAAC1F,aAAa,CAAC,GAAGA,aAAa,GAAG,CAACA,aAAa,CAAC;QAC5EwF,OAAO,GAAGA,OAAO,CAACG,OAAO,EAAE;QAC3B1B,OAAO,CAACC,GAAG,CAACsB,OAAO,CAAC;QAEpB;QACAA,OAAO,CAACI,OAAO,CAACC,CAAC,IAAG;UAClB,IAAIA,CAAC,CAAC1F,SAAS,IAAIsF,KAAK,CAACC,OAAO,CAACG,CAAC,CAAC1F,SAAS,CAAC,EAAE;YAC7C0F,CAAC,CAAC1F,SAAS,GAAG0F,CAAC,CAAC1F,SAAS,CAACwF,OAAO,EAAE;YACnC;YACAE,CAAC,CAAC1F,SAAS,CAACyF,OAAO,CAACE,OAAO,IAAG;cAC5B,IAAIA,OAAO,CAACrB,SAAS,IAAIqB,OAAO,CAACrB,SAAS,CAACJ,MAAM,GAAG,CAAC,EAAE;gBACrD,IAAI,CAAC7D,sBAAsB,CAACsF,OAAO,CAACxB,EAAE,IAAI,EAAE,CAAC,GAAGwB,OAAO,CAACrB,SAAS,CAACJ,MAAM,GAAG,CAAC;;YAEhF,CAAC,CAAC;;QAEN,CAAC,CAAC;QAEF,IAAI,CAACrE,aAAa,GAAGwF,OAAO;QAE5B;QACA,IAAI,CAAC9D,cAAc,GAAG,IAAI,CAAC1B,aAAa,CAACqE,MAAM,KAAK,CAAC,IACnD,IAAI,CAACrE,aAAa,CAAC+F,KAAK,CAACF,CAAC,IAAI,CAACA,CAAC,CAAC1F,SAAS,IAAI0F,CAAC,CAAC1F,SAAS,CAACkE,MAAM,KAAK,CAAC,CAAC;QAEzE,IAAI,IAAI,CAACrE,aAAa,CAACqE,MAAM,GAAG,CAAC,EAAE;UACjC;UACA,MAAM2B,iBAAiB,GAAG9F,SAAS,GAC/B,IAAI,CAACF,aAAa,CAACgF,IAAI,CAACa,CAAC,IAAIA,CAAC,CAAC3F,SAAS,KAAKA,SAAS,CAAC,GACvD,IAAI,CAACF,aAAa,CAAC,CAAC,CAAC;UAAW,IAAIgG,iBAAiB,EAAE;YACzD,IAAI,CAAC/F,mBAAmB,GAAG+F,iBAAiB;YAE5C;YACA,IAAIA,iBAAiB,CAAC7F,SAAS,IAAI6F,iBAAiB,CAAC7F,SAAS,CAACkE,MAAM,GAAG,CAAC,EAAE;cACzE,IAAI,CAAC3C,cAAc,GAAG,KAAK;;YAG7B;YACA,IAAI,IAAI,CAACzB,mBAAmB,CAACC,SAAS,IAAI,CAAC,IAAI,CAACc,aAAa,EAAE;cAC7D,IAAI,CAACA,aAAa,GAAG,IAAI,CAACf,mBAAmB,CAACC,SAAS;cACvD,IAAI,CAACH,SAAS,CAACG,SAAS,GAAG,IAAI,CAACD,mBAAmB,CAACC,SAAS;;WAEhE,MAAM,IAAIA,SAAS,EAAE;YACpB;YACA;YACA,IAAI,CAACD,mBAAmB,GAAG,IAAInD,wBAAwB,CAAC;cACtDoD,SAAS,EAAEA,SAAS;cACpBC,SAAS,EAAE;aACZ,CAAC;YACF,IAAI,CAACH,aAAa,CAAC0E,IAAI,CAAC,IAAI,CAACzE,mBAAmB,CAAC;;SAEpD,MAAM,IAAIC,SAAS,EAAE;UACpB;UACA,IAAI,CAACD,mBAAmB,GAAG,IAAInD,wBAAwB,CAAC;YACtDoD,SAAS,EAAEA,SAAS;YACpBC,SAAS,EAAE;WACZ,CAAC;UACF,IAAI,CAACH,aAAa,CAAC0E,IAAI,CAAC,IAAI,CAACzE,mBAAmB,CAAC;UACjD,IAAI,CAACyB,cAAc,GAAG,IAAI;SAC3B,MAAM;UACL;UACA,IAAI,CAACA,cAAc,GAAG,IAAI;;QAG5B,IAAI,CAACtB,gBAAgB,GAAG,KAAK;QAC7BiF,UAAU,CAAC,MAAM,IAAI,CAAC/B,cAAc,EAAE,EAAE,GAAG,CAAC;MAC9C,CAAC;MACD2C,KAAK,EAAGA,KAAK,IAAI;QACfhC,OAAO,CAACgC,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;QAC3D,IAAI,CAACtG,OAAO,CAACsG,KAAK,CAAC,+BAA+B,CAAC;QACnD,IAAI,CAAC7F,gBAAgB,GAAG,KAAK;MAC/B;KACD,CAAC;EACJ;EAEA;;;EAGAyC,cAAcA,CAAA;IACZ,IAAI,CAACjD,gBAAgB,CAACsG,MAAM,EAAE,CAAClD,SAAS,CAAC;MACvCuC,IAAI,EAAG7E,UAAU,IAAI;QACnB,IAAI,CAACA,UAAU,GAAGA,UAAU;QAC5B,IAAI,IAAI,CAACA,UAAU,CAAC2D,MAAM,GAAG,CAAC,EAAE;UAC9B,IAAI,CAAC1D,iBAAiB,GAAG,IAAI,CAACD,UAAU,CAAC,CAAC,CAAC,CAACyF,KAAK;UACjD,IAAI,CAACC,sBAAsB,CAAC,IAAI,CAACzF,iBAAiB,CAAC;;MAEvD,CAAC;MACDsF,KAAK,EAAGA,KAAK,IAAI;QACfhC,OAAO,CAACgC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD;KACD,CAAC;EACJ;EAEA;;;EAGAG,sBAAsBA,CAACC,aAAqB;IAC1C,IAAI,CAACA,aAAa,EAAE;IAEpB,IAAI,CAAC9G,eAAe,CAAC+G,iBAAiB,CAACD,aAAa,CAAC,CAACrD,SAAS,CAAC;MAC9DuC,IAAI,EAAGgB,MAAM,IAAI;QACf,IAAI,CAAC3F,eAAe,GAAG2F,MAAM;QAC7B;QACA,IAAI,CAACnF,qBAAqB,GAAG,CAAC,GAAG,IAAI,CAACR,eAAe,CAAC;MACxD,CAAC;MACDqF,KAAK,EAAGA,KAAK,IAAI;QACfhC,OAAO,CAACgC,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC7D;KACD,CAAC;EACJ;EAEA;;;EAGAO,mBAAmBA,CAACC,UAAe;IACjC,IAAI,CAAC1G,SAAS,CAAC2G,QAAQ,GAAGD,UAAU,CAACE,IAAI;IACzC,IAAIF,UAAU,CAACG,YAAY,EAAE;MAC3B,IAAI,CAACC,WAAW,EAAE;;EAEtB;EAEA;;;;;EAIKC,aAAaA,CAACH,IAAY;IAC7B,OAAO,CAAC,CAACA,IAAI,IAAIA,IAAI,CAACI,QAAQ,CAAC,QAAQ,CAAC;EAC1C;EAEAC,oBAAoBA,CAACL,IAAY;IAC/B,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IACpB,OAAOA,IAAI,CAACM,OAAO,CAAC,oBAAoB,EAAE,EAAE,CAAC;EAC/C;EAEAC,iBAAiBA,CAACP,IAAY;IAC5B,IAAI,CAACA,IAAI,IAAI,CAAC,IAAI,CAACG,aAAa,CAACH,IAAI,CAAC,EAAE,OAAO,EAAE;IACjD,MAAMQ,KAAK,GAAGR,IAAI,CAACQ,KAAK,CAAC,qBAAqB,CAAC;IAC/C,OAAOA,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,EAAE,GAAG,EAAE;EACrC;EAEAC,gBAAgBA,CAACV,IAAY;IAC3B,IAAI,CAACA,IAAI,EAAE,OAAO,WAAW;IAC7B,MAAMW,UAAU,GAAGX,IAAI,CAACQ,KAAK,CAAC,cAAc,CAAC;IAC7C,OAAOG,UAAU,GAAGA,UAAU,CAAC,CAAC,CAAC,GAAG,WAAW;EACjD;EAEAC,mBAAmBA,CAACZ,IAAY;IAC9B,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IACpB,MAAMa,YAAY,GAAGb,IAAI,CAACQ,KAAK,CAAC,iBAAiB,CAAC;IAClD,OAAOK,YAAY,GAAGA,YAAY,CAAC,CAAC,CAAC,CAACJ,IAAI,EAAE,GAAG,EAAE;EACnD;EAEAK,cAAcA,CAACd,IAAY;IACzB,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IACpB,MAAMe,OAAO,GAAGf,IAAI,CAACQ,KAAK,CAAC,YAAY,CAAC;IACxC,OAAOO,OAAO,GAAGA,OAAO,CAAC,CAAC,CAAC,CAACN,IAAI,EAAE,GAAG,EAAE;EACzC;EAEAO,cAAcA,CAAChB,IAAY;IACzB,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IACpB,MAAMiB,OAAO,GAAGjB,IAAI,CAACQ,KAAK,CAAC,YAAY,CAAC;IACxC,OAAOS,OAAO,GAAGA,OAAO,CAAC,CAAC,CAAC,CAACR,IAAI,EAAE,GAAG,EAAE;EACzC;EAEAS,gBAAgBA,CAAClB,IAAY;IAC3B,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IACpB,MAAMmB,KAAK,GAAGnB,IAAI,CAACoB,KAAK,CAAC,SAAS,CAAC;IACnC,OAAOD,KAAK,CAACzD,MAAM,GAAG,CAAC,GAAGyD,KAAK,CAAC,CAAC,CAAC,CAACV,IAAI,EAAE,GAAGT,IAAI;EAClD;EAEA;;;EAGAqB,oBAAoBA,CAACC,KAAU;IAC7B,IAAI,CAACA,KAAK,EAAE;IACZ,IAAI,CAAClI,SAAS,CAACG,SAAS,GAAG+H,KAAK,CAAC/H,SAAS;IAC1C,IAAI,CAACiB,sBAAsB,GAAG,KAAK;EACrC;EAEA+G,YAAYA,CAACC,MAAW;IACtB,IAAI,CAACA,MAAM,EAAE;IACb,IAAI,CAACpI,SAAS,CAAC2G,QAAQ,GAAGyB,MAAM,CAACA,MAAM;IACvC,IAAI,CAAC7G,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACiC,iBAAiB,EAAE;EAC1B;EAEA;;;EAGQX,0BAA0BA,CAAA;IAChC,IAAI;MACF,MAAMwF,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,IAAI,CAAC1G,yBAAyB,CAAC;MACvE,IAAIwG,UAAU,EAAE;QACd,MAAMG,WAAW,GAAGC,QAAQ,CAACJ,UAAU,EAAE,EAAE,CAAC;QAC5C;QACA,IAAI,CAACK,KAAK,CAACF,WAAW,CAAC,IAAIA,WAAW,IAAI,IAAI,CAACxG,oBAAoB,IAAIwG,WAAW,IAAI,IAAI,CAACvG,oBAAoB,EAAE;UAC/G,IAAI,CAACH,iBAAiB,GAAG0G,WAAW;UACpC,IAAI,CAACrG,cAAc,GAAGqG,WAAW,CAAC,CAAC;SACpC,MAAM;UACL;UACA,IAAI,CAAC1G,iBAAiB,GAAG,IAAI,CAACF,2BAA2B;UACzD;UACA,IAAI,CAAC+G,qBAAqB,CAAC,IAAI,CAAC/G,2BAA2B,CAAC;;OAE/D,MAAM;QACL;QACA,IAAI,CAACE,iBAAiB,GAAG,IAAI,CAACF,2BAA2B;QACzD;QACA,IAAI,CAAC+G,qBAAqB,CAAC,IAAI,CAAC/G,2BAA2B,CAAC;;MAG9D;MACA,IAAI,CAACkC,gBAAgB,EAAE;KACxB,CAAC,OAAOoC,KAAK,EAAE;MACdhC,OAAO,CAACgC,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,IAAI,CAACpE,iBAAiB,GAAG,IAAI,CAACF,2BAA2B;MACzD,IAAI,CAAC+G,qBAAqB,CAAC,IAAI,CAAC/G,2BAA2B,CAAC;;EAEhE;EAEA;;;;EAIQ+G,qBAAqBA,CAACC,KAAa;IACzC,IAAI;MACFN,YAAY,CAACO,OAAO,CAAC,IAAI,CAAChH,yBAAyB,EAAE+G,KAAK,CAACE,QAAQ,EAAE,CAAC;KACvE,CAAC,OAAO5C,KAAK,EAAE;MACdhC,OAAO,CAACgC,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;;EAE/D;EAEA;;;EAGA3C,cAAcA,CAAA;IACZ,IAAI;MACF,IAAI,IAAI,CAACwF,aAAa,EAAE;QACtBzD,UAAU,CAAC,MAAK;UACd,IAAI,CAACyD,aAAa,CAACC,aAAa,CAACC,SAAS,GACxC,IAAI,CAACF,aAAa,CAACC,aAAa,CAACE,YAAY;QACjD,CAAC,EAAE,GAAG,CAAC;;KAEV,CAAC,OAAOC,GAAG,EAAE;MACZjF,OAAO,CAACgC,KAAK,CAAC,4BAA4B,EAAEiD,GAAG,CAAC;;EAEpD;EAEA;;;EAGA3F,iBAAiBA,CAAA;IACf,IAAI,IAAI,CAAC4F,SAAS,IAAI,IAAI,CAACA,SAAS,CAACJ,aAAa,EAAE;MAClD,MAAMK,OAAO,GAAG,IAAI,CAACD,SAAS,CAACJ,aAAa;MAC5CK,OAAO,CAACC,KAAK,CAACC,MAAM,GAAG,MAAM;MAC7BF,OAAO,CAACC,KAAK,CAACC,MAAM,GAAG7K,IAAI,CAAC8K,GAAG,CAACH,OAAO,CAACH,YAAY,EAAE,GAAG,CAAC,GAAG,IAAI;;EAErE;EAEA;;;EAGMpC,WAAWA,CAAA;IAAA,IAAA2C,KAAA;IAAA,OAAAC,iBAAA;MACf,IAAI,CAACD,KAAI,CAACzJ,SAAS,CAAC2G,QAAQ,EAAEU,IAAI,EAAE,EAAE;MACtC,IAAI,CAACoC,KAAI,CAACzJ,SAAS,CAACG,SAAS,IAAIsJ,KAAI,CAACxI,aAAa,EAAE;QACnDwI,KAAI,CAACzJ,SAAS,CAACG,SAAS,GAAGsJ,KAAI,CAACxI,aAAa;;MAG/C;MACA,MAAM0I,eAAe,GAAGF,KAAI,CAACzJ,SAAS,CAAC2G,QAAQ;MAC/C,MAAMxG,SAAS,GAAGsJ,KAAI,CAACzJ,SAAS,CAACG,SAAS,IAAIsJ,KAAI,CAACxI,aAAa;MAEhE;MACA,IAAIwI,KAAI,CAACvJ,mBAAmB,CAACC,SAAS,KAAKA,SAAS,EAAE;QACpD,MAAM8F,iBAAiB,GAAGwD,KAAI,CAACxJ,aAAa,CAACgF,IAAI,CAACa,CAAC,IAAIA,CAAC,CAAC3F,SAAS,KAAKA,SAAS,CAAC;QAEjF,IAAI8F,iBAAiB,EAAE;UACrBwD,KAAI,CAACvJ,mBAAmB,GAAG+F,iBAAiB;SAC7C,MAAM;UACLwD,KAAI,CAACvJ,mBAAmB,GAAG,IAAInD,wBAAwB,CAAC;YACtDoD,SAAS,EAAEA,SAAS;YACpBC,SAAS,EAAE;WACZ,CAAC;UAEFqJ,KAAI,CAACxJ,aAAa,CAAC0E,IAAI,CAAC8E,KAAI,CAACvJ,mBAAmB,CAAC;;;MAIrD;MACA,IAAI,CAACuJ,KAAI,CAACvJ,mBAAmB,CAACE,SAAS,EAAE;QACvCqJ,KAAI,CAACvJ,mBAAmB,CAACE,SAAS,GAAG,EAAE;;MAGzC;MACA,MAAMwJ,WAAW,GAAG,IAAI9M,mBAAmB,CAAC;QAC1CyH,EAAE,EAAE,OAAO,GAAGlG,IAAI,CAACC,GAAG,EAAE;QACxBqI,QAAQ,EAAEgD,eAAe;QACzBjF,SAAS,EAAE,EAAE;QACbI,SAAS,EAAE9G,QAAQ,CAACM,GAAG;OACxB,CAAC;MACFsL,WAAW,CAACxE,SAAS,GAAG,IAAI;MAE5B;MACAqE,KAAI,CAACvJ,mBAAmB,CAACE,SAAS,CAACuE,IAAI,CAACiF,WAAW,CAAC;MAEpD;MACAH,KAAI,CAAC9H,cAAc,GAAG,KAAK;MAE3B;MACA8H,KAAI,CAACzJ,SAAS,CAAC2G,QAAQ,GAAG,EAAE;MAC5B8C,KAAI,CAACjG,iBAAiB,EAAE;MAExB;MACAiG,KAAI,CAACpJ,gBAAgB,GAAG,IAAI;MAE5B;MACAiF,UAAU,CAAC,MAAMmE,KAAI,CAAClG,cAAc,EAAE,EAAE,GAAG,CAAC;MAE5CW,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEhE,SAAS,CAAC;MAEnD;MACAsJ,KAAI,CAACrF,yBAAyB,GAAGwF,WAAW,CAACrF,EAAE;MAC/CkF,KAAI,CAAC/I,iBAAiB,GAAG,IAAI;MAE7B;MACA,MAAMmJ,UAAU,GAAG,IAAIhN,mBAAmB,CAAC;QACzC8J,QAAQ,EAAEgD,eAAe;QACzBxJ,SAAS,EAAEA;OACZ,CAAC;MAEFsJ,KAAI,CAAC1J,gBAAgB,CAAC+J,gBAAgB,CAACD,UAAU,CAAC,CAAC5G,SAAS,CAAC;QAC3DuC,IAAI,EAAGuE,QAAQ,IAAI;UACjB,IAAI,CAACA,QAAQ,EAAE;YACb7F,OAAO,CAACgC,KAAK,CAAC,gDAAgD,CAAC;YAC/DuD,KAAI,CAAC7J,OAAO,CAACsG,KAAK,CAAC,uCAAuC,CAAC;YAC3DuD,KAAI,CAACpJ,gBAAgB,GAAG,KAAK;YAC7B;;UAGF;UACA,IAAIoJ,KAAI,CAACvJ,mBAAmB,CAACE,SAAS,EAAE;YACtC,MAAM4J,SAAS,GAAGP,KAAI,CAACvJ,mBAAmB,CAACE,SAAS,CAAC6J,SAAS,CAAC/E,CAAC,IAAIA,CAAC,CAACX,EAAE,KAAKqF,WAAW,CAACrF,EAAE,CAAC;YAC5F,IAAIyF,SAAS,KAAK,CAAC,CAAC,EAAE;cACpB;cACAP,KAAI,CAACvJ,mBAAmB,CAACE,SAAS,CAAC4J,SAAS,CAAC,GAAGD,QAAQ;cAExD;cACA,IAAIA,QAAQ,CAACxF,EAAE,EAAE;gBACfkF,KAAI,CAAChJ,sBAAsB,CAACsJ,QAAQ,CAACxF,EAAE,CAAC,GAAG,CAACwF,QAAQ,CAACrF,SAAS,IAAI,EAAE,EAAEJ,MAAM,GAAG,CAAC;;aAEnF,MAAM;cACL;cACAmF,KAAI,CAACvJ,mBAAmB,CAACE,SAAS,CAACuE,IAAI,CAACoF,QAAQ,CAAC;cACjD,IAAIA,QAAQ,CAACxF,EAAE,EAAE;gBACfkF,KAAI,CAAChJ,sBAAsB,CAACsJ,QAAQ,CAACxF,EAAE,CAAC,GAAG,CAACwF,QAAQ,CAACrF,SAAS,IAAI,EAAE,EAAEJ,MAAM,GAAG,CAAC;;;;UAKtF;UACAmF,KAAI,CAACpJ,gBAAgB,GAAG,KAAK;UAC7BoJ,KAAI,CAAClG,cAAc,EAAE;QACvB,CAAC;QACD2C,KAAK,EAAGA,KAAK,IAAI;UACfhC,OAAO,CAACgC,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;UACpDuD,KAAI,CAAC7J,OAAO,CAACsG,KAAK,CAAC,wBAAwB,CAAC;UAE5C;UACA,IAAIuD,KAAI,CAACvJ,mBAAmB,CAACE,SAAS,EAAE;YACtC,MAAM4J,SAAS,GAAGP,KAAI,CAACvJ,mBAAmB,CAACE,SAAS,CAAC6J,SAAS,CAAC/E,CAAC,IAAIA,CAAC,CAACX,EAAE,KAAKqF,WAAW,CAACrF,EAAE,CAAC;YAC5F,IAAIyF,SAAS,KAAK,CAAC,CAAC,EAAE;cACpBP,KAAI,CAACvJ,mBAAmB,CAACE,SAAS,CAAC8J,MAAM,CAACF,SAAS,EAAE,CAAC,CAAC;;;UAI3D;UACAP,KAAI,CAACzJ,SAAS,CAAC2G,QAAQ,GAAGgD,eAAe;UACzCF,KAAI,CAACjG,iBAAiB,EAAE;UAExBiG,KAAI,CAACpJ,gBAAgB,GAAG,KAAK;QAC/B;OACD,CAAC;IAAC;EACL;EAEA;;;EAGA8J,kBAAkBA,CAACpE,OAA4B;IAC7C,IAAI,CAACA,OAAO,IAAI,CAACA,OAAO,CAACxB,EAAE,EAAE;IAE7B;IACAwB,OAAO,CAACX,SAAS,GAAG,IAAI;IACxB,MAAMjF,SAAS,GAAG,IAAI,CAACD,mBAAmB,EAAEC,SAAS;IAErD,IAAI,CAACJ,gBAAgB,CAACqK,mBAAmB,CAACrE,OAAO,CAACxB,EAAE,EAAEpE,SAAS,CAAC,CAAC8C,SAAS,CAAC;MACzEuC,IAAI,EAAGuE,QAAQ,IAAI;QACjB;QACAhE,OAAO,CAACX,SAAS,GAAG,KAAK;QAEzB;QACA,IAAI,CAACW,OAAO,CAACrB,SAAS,EAAE;UACtBqB,OAAO,CAACrB,SAAS,GAAG,EAAE;;QAGxB;QACA,IAAIqF,QAAQ,CAACnF,YAAY,EAAE;UACzB,MAAMyF,WAAW,GAAG,IAAIrN,oBAAoB,CAAC;YAC3CuH,EAAE,EAAEwF,QAAQ,CAACxF,EAAE,IAAI,EAAE;YACrBK,YAAY,EAAEmF,QAAQ,CAACnF,YAAY;YACnCC,UAAU,EAAE,aAAa;YACzBC,SAAS,EAAE9G,QAAQ,CAACM,GAAG;WACxB,CAAC;UACFyH,OAAO,CAACrB,SAAS,CAACC,IAAI,CAAC0F,WAAW,CAAC;UAEnC;UACA,MAAMC,SAAS,GAAGvE,OAAO,CAACxB,EAAE,IAAI,EAAE;UAClC,IAAI,CAAC9D,sBAAsB,CAAC6J,SAAS,CAAC,GAAGvE,OAAO,CAACrB,SAAS,CAACJ,MAAM,GAAG,CAAC;;QAGvE,IAAI,CAACf,cAAc,EAAE;MACvB,CAAC;MACD2C,KAAK,EAAGA,KAAK,IAAI;QACfhC,OAAO,CAACgC,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpD,IAAI,CAACtG,OAAO,CAACsG,KAAK,CAAC,+BAA+B,CAAC;QAEnD;QACAH,OAAO,CAACX,SAAS,GAAG,KAAK;MAC3B;KACD,CAAC;EACJ;EAEA;;;EAGAmF,WAAWA,CAACxE,OAA4B;IACtC,IAAI,CAACA,OAAO,EAAE;IAEd;IACA,MAAMyE,cAAc,GAAGzE,OAAO,CAACY,QAAQ;IAEvC;IACAZ,OAAO,CAAC0E,WAAW,GAAG,KAAK;IAE3B;IACA,IAAI,CAACzK,SAAS,CAAC2G,QAAQ,GAAG6D,cAAc;IACxC,IAAI,CAAC1D,WAAW,EAAE;EACpB;EAEA;;;EAGA4D,cAAcA,CAAA;IACZ,IAAI,CAAChJ,WAAW,GAAG,IAAI;IACvB;IACA,IAAI,CAAC9B,OAAO,CAAC+K,IAAI,CAAC,mBAAmB,CAAC;EACxC;EAEAC,aAAaA,CAAA;IACX,IAAI,CAAClJ,WAAW,GAAG,KAAK;IACxB;IACA,IAAI,CAAC9B,OAAO,CAAC+K,IAAI,CAAC,mBAAmB,CAAC;EACxC;EAEA;;;EAGAE,YAAYA,CAACjE,IAAY;IACvB;IACA,IAAI,CAAChH,OAAO,CAAC+K,IAAI,CAAC,wBAAwB,CAAC;EAC7C;EAEAG,UAAUA,CAAA;IACR;IACA,IAAI,CAAClL,OAAO,CAAC+K,IAAI,CAAC,wBAAwB,CAAC;EAC7C;EAEA;EACAI,aAAaA,CAAClH,KAAU;IACtB,IAAIA,KAAK,CAACmH,GAAG,KAAK,OAAO,IAAI,CAACnH,KAAK,CAACoH,QAAQ,EAAE;MAC5CpH,KAAK,CAACqH,cAAc,EAAE;MACtB,IAAI,CAACpE,WAAW,EAAE;;EAEtB;EAEAqE,OAAOA,CAACtH,KAAU;IAChB,IAAI,CAACL,iBAAiB,EAAE;EAC1B;EAEA4H,YAAYA,CAAA;IACV,IAAI,IAAI,CAACrC,aAAa,EAAE;MACtB,MAAMM,OAAO,GAAG,IAAI,CAACN,aAAa,CAACC,aAAa;MAChD,IAAI,CAAClI,gBAAgB,GAAGuI,OAAO,CAACH,YAAY,GAAGG,OAAO,CAACJ,SAAS,GAAGI,OAAO,CAACgC,YAAY,GAAG,GAAG;;EAEjG;EAEA;;;EAGQvH,gBAAgBA,CAAA;IACtB,IAAI,IAAI,CAACxB,wBAAwB,IAAI,IAAI,CAACpB,kBAAkB,EAAE;MAC5D;MACA,MAAMoK,WAAW,GAAGC,MAAM,CAACC,UAAU;MACrC;MACA,MAAMC,aAAa,GAAG,EAAE;MACxB,MAAMC,oBAAoB,GAAI,IAAI,CAAC5J,iBAAiB,GAAGwJ,WAAW,GAAI,GAAG;MACzE,IAAI,CAACjJ,qBAAqB,GAAG3D,IAAI,CAACiN,GAAG,CAACD,oBAAoB,EAAED,aAAa,CAAC;MAC1E,IAAI,CAACrJ,oBAAoB,GAAG,GAAG,GAAG,IAAI,CAACC,qBAAqB;KAC7D,MAAM;MACL;MACA,IAAI,CAACD,oBAAoB,GAAG,GAAG;MAC/B,IAAI,CAACC,qBAAqB,GAAG,CAAC;;EAElC;EAEA;;;;;EAKAuJ,0BAA0BA,CAACC,sBAA8B,EAAEC,UAAA,GAAqB,EAAE;IAChF5H,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAE,IAAI,CAAC7B,wBAAwB,CAAC;IAE7F;IACA,IAAIuJ,sBAAsB,IAAIA,sBAAsB,CAACvH,MAAM,GAAG,CAAC,EAAE;MAC/DJ,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE0H,sBAAsB,CAAC;MAE/E;MACA,IAAI,CAACrL,iBAAiB,GAAGsL,UAAU;MAEnC;MACA,IAAI,CAACvL,aAAa,GAAGsL,sBAAsB,CAACE,GAAG,CAACC,IAAI,KAAK;QACvD5F,KAAK,EAAE4F,IAAI,CAAC5F,KAAK,IAAI,UAAU;QAC/B6F,GAAG,EAAED,IAAI,CAACC,GAAG,IAAI,EAAE;QACnBC,MAAM,EAAEF,IAAI,CAACC,GAAG;QAChBE,WAAW,EAAEH,IAAI,CAACG,WAAW,IAAI;OAClC,CAAC,CAAC;MAEH;MACA,IAAI,CAAC7J,wBAAwB,GAAG,IAAI;MAEpC;MACA,IAAI,IAAI,CAACpB,kBAAkB,EAAE;QAC3B,IAAI,CAACA,kBAAkB,GAAG,KAAK;;KAElC,MAAM;MACL;MACA,IAAI,CAACoB,wBAAwB,GAAG,CAAC,IAAI,CAACA,wBAAwB;;IAGhE;IACA,IAAI,CAACwB,gBAAgB,EAAE;IAEvB;IACAwB,UAAU,CAAC,MAAK;MACd;MACAiG,MAAM,CAACa,aAAa,CAAC,IAAIC,KAAK,CAAC,QAAQ,CAAC,CAAC;IAC3C,CAAC,EAAE,EAAE,CAAC;EACR;EAEA;EACAC,kBAAkBA,CAAA;IAChB,IAAI,CAACpL,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAkB;IAElD;IACA,IAAI,IAAI,CAACA,kBAAkB,IAAI,IAAI,CAACoB,wBAAwB,EAAE;MAC5D,IAAI,CAACA,wBAAwB,GAAG,KAAK;;IAGvC;IACA,IAAI,CAACwB,gBAAgB,EAAE;IAEvB;IACAwB,UAAU,CAAC,MAAK;MACd;MACAiG,MAAM,CAACa,aAAa,CAAC,IAAIC,KAAK,CAAC,QAAQ,CAAC,CAAC;IAC3C,CAAC,EAAE,EAAE,CAAC;EACR;EAEA;;;EAGAE,cAAcA,CAAC1I,KAAU;IACvB;IACA,MAAM2I,SAAS,GAAG3I,KAAK,CAAC4I,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAClC,IAAID,SAAS,IAAIA,SAAS,GAAG,CAAC,EAAE;MAC9B,MAAMlB,WAAW,GAAGC,MAAM,CAACC,UAAU;MACrC,IAAI,CAAC1J,iBAAiB,GAAGpD,IAAI,CAACgO,KAAK,CAAEF,SAAS,GAAG,GAAG,GAAIlB,WAAW,CAAC;MAEpE;MACA,IAAI,CAACxJ,iBAAiB,GAAGpD,IAAI,CAACiN,GAAG,CAAC,IAAI,CAAC3J,oBAAoB,EACzDtD,IAAI,CAAC8K,GAAG,CAAC,IAAI,CAAC1H,iBAAiB,EAAE,IAAI,CAACG,oBAAoB,CAAC,CAAC;MAE9D;MACA,IAAI,IAAI,CAACH,iBAAiB,GAAG,IAAI,CAACI,kBAAkB,EAAE;QACpD,IAAI,CAACI,wBAAwB,GAAG,KAAK;QACrC,IAAI,CAACpB,kBAAkB,GAAG,KAAK;QAC/B,IAAI,CAAC4C,gBAAgB,EAAE;OACxB,MAAM;QACL;QACA,IAAI,CAAC3B,cAAc,GAAG,IAAI,CAACL,iBAAiB;QAC5C;QACA,IAAI,CAAC6G,qBAAqB,CAAC,IAAI,CAAC7G,iBAAiB,CAAC;;;IAItD,IAAI,CAACC,UAAU,GAAG,KAAK;EACzB;EAEA4K,mBAAmBA,CAAC9I,KAAU;IAC5B,IAAI,CAAC9B,UAAU,GAAG,IAAI;IAEtB;IACA,MAAMyK,SAAS,GAAG3I,KAAK,CAAC4I,KAAK,CAAC,CAAC,CAAC;IAChC,IAAID,SAAS,IAAIA,SAAS,GAAG,CAAC,EAAE;MAC9B,MAAMlB,WAAW,GAAGC,MAAM,CAACC,UAAU;MACrC,MAAMoB,QAAQ,GAAGlO,IAAI,CAACgO,KAAK,CAAEF,SAAS,GAAG,GAAG,GAAIlB,WAAW,CAAC;MAE5D;MACA,IAAIsB,QAAQ,GAAG,IAAI,CAAC1K,kBAAkB,EAAE;QACtC;QACA;MAAA;;EAGN;EAEA2K,mBAAmBA,CAAChJ,KAAU;IAC5B,IAAI,IAAI,CAACvB,wBAAwB,IAAI,IAAI,CAACpB,kBAAkB,EAAE;MAC5D;MACA,IAAI,CAACoB,wBAAwB,GAAG,KAAK;MACrC,IAAI,CAACpB,kBAAkB,GAAG,KAAK;KAChC,MAAM;MACL;MACA,IAAI,IAAI,CAACX,aAAa,CAAC+D,MAAM,GAAG,CAAC,EAAE;QACjC,IAAI,CAAChC,wBAAwB,GAAG,IAAI;OACrC,MAAM;QACL,IAAI,CAACpB,kBAAkB,GAAG,IAAI;;MAGhC;MACA,IAAI,CAACY,iBAAiB,GAAG,IAAI,CAACK,cAAc;;IAG9C,IAAI,CAAC2B,gBAAgB,EAAE;EACzB;EAEAgJ,eAAeA,CAACC,SAAiB;IAC/B,IAAI,CAACnM,iBAAiB,GAAGmM,SAAS;IAClC,IAAI,CAAC1G,sBAAsB,CAAC0G,SAAS,CAAC;EACxC;EACAC,WAAWA,CAAC9E,KAAU;IACpB,IAAI,CAACjH,aAAa,GAAGiH,KAAK,CAAC/H,SAAS,IAAI+H,KAAK;IAC7C,IAAI,CAAClI,SAAS,CAACG,SAAS,GAAG,IAAI,CAACc,aAAa;IAE7C;IACA,IAAI,CAAC3B,MAAM,CAAC2N,QAAQ,CAAC,CAAC,aAAa,EAAE,IAAI,CAAChM,aAAa,CAAC,CAAC;IAEzD;IACA,IAAI,IAAI,CAACC,kBAAkB,EAAE;MAC3B,IAAI,CAACA,kBAAkB,GAAG,KAAK;MAC/B,IAAI,CAACmB,qBAAqB,GAAG,CAAC;MAC9B,IAAI,CAACD,oBAAoB,GAAG,GAAG;;EAEnC;EACA8K,kBAAkBA,CAAA;IAChB,IAAI,CAACjM,aAAa,GAAG,EAAE;IACvB,IAAI,CAACjB,SAAS,CAACG,SAAS,GAAG,EAAE;IAE7B;IACA,IAAI,CAACb,MAAM,CAAC2N,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;IAErC;IACA,IAAI,CAAC7J,sBAAsB,EAAE;EAC/B;EACA;EACA+J,WAAWA,CAACC,OAA2B;IACrC,IAAIA,OAAO,EAAE;MACXC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACH,OAAO,CAAC;MACtC,IAAI,CAACxN,OAAO,CAAC4N,OAAO,CAAC,6BAA6B,CAAC;;EAEvD;EAEAC,cAAcA,CAACL,OAAe;IAC5B,IAAI,CAACD,WAAW,CAACC,OAAO,CAAC;EAC3B;EAEAM,eAAeA,CAACN,OAAe;IAC7B,IAAI,CAACD,WAAW,CAACC,OAAO,CAAC;EAC3B;EAEAO,gBAAgBA,CAACP,OAAe;IAC9B,IAAI,CAACD,WAAW,CAACC,OAAO,CAAC;EAC3B;EAEA;EACAQ,uBAAuBA,CAACtD,SAAiB;IACvC,OAAO,IAAI,CAAC7J,sBAAsB,CAAC6J,SAAS,CAAC,IAAI,CAAC;EACpD;EAEAuD,kBAAkBA,CAAC9H,OAA4B;IAC7C,IAAI,CAACA,OAAO,IAAI,CAACA,OAAO,CAACrB,SAAS,IAAIqB,OAAO,CAACrB,SAAS,CAACJ,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;IAEjF,MAAMgG,SAAS,GAAGvE,OAAO,CAACxB,EAAE,IAAI,EAAE;IAClC,MAAMuJ,YAAY,GAAG,IAAI,CAACF,uBAAuB,CAACtD,SAAS,CAAC;IAC5D,MAAMP,QAAQ,GAAGhE,OAAO,CAACrB,SAAS,CAACoJ,YAAY,CAAC;IAEhD,OAAO/D,QAAQ,IAAI,IAAI;EACzB;EAEAgE,oBAAoBA,CAAChI,OAA4B;IAC/C,IAAI,CAACA,OAAO,IAAI,CAACA,OAAO,CAACrB,SAAS,IAAIqB,OAAO,CAACrB,SAAS,CAACJ,MAAM,IAAI,CAAC,EAAE;IAErE,MAAMgG,SAAS,GAAGvE,OAAO,CAACxB,EAAE,IAAI,EAAE;IAClC,MAAMuJ,YAAY,GAAG,IAAI,CAACF,uBAAuB,CAACtD,SAAS,CAAC;IAE5D,IAAIwD,YAAY,GAAG,CAAC,EAAE;MACpB,IAAI,CAACrN,sBAAsB,CAAC6J,SAAS,CAAC,GAAGwD,YAAY,GAAG,CAAC;;EAE7D;EAEAE,YAAYA,CAACjI,OAA4B;IACvC,IAAI,CAACA,OAAO,IAAI,CAACA,OAAO,CAACrB,SAAS,IAAIqB,OAAO,CAACrB,SAAS,CAACJ,MAAM,IAAI,CAAC,EAAE;IAErE,MAAMgG,SAAS,GAAGvE,OAAO,CAACxB,EAAE,IAAI,EAAE;IAClC,MAAMuJ,YAAY,GAAG,IAAI,CAACF,uBAAuB,CAACtD,SAAS,CAAC;IAE5D,IAAIwD,YAAY,GAAG/H,OAAO,CAACrB,SAAS,CAACJ,MAAM,GAAG,CAAC,EAAE;MAC/C,IAAI,CAAC7D,sBAAsB,CAAC6J,SAAS,CAAC,GAAGwD,YAAY,GAAG,CAAC;;EAE7D;EAEAG,qBAAqBA,CAAClI,OAA4B;IAChD,IAAI,CAACA,OAAO,IAAI,CAACA,OAAO,CAACrB,SAAS,IAAIqB,OAAO,CAACrB,SAAS,CAACJ,MAAM,IAAI,CAAC,EAAE,OAAO,EAAE;IAE9E,MAAMgG,SAAS,GAAGvE,OAAO,CAACxB,EAAE,IAAI,EAAE;IAClC,MAAMuJ,YAAY,GAAG,IAAI,CAACF,uBAAuB,CAACtD,SAAS,CAAC;IAE5D,OAAO,GAAGwD,YAAY,GAAG,CAAC,IAAI/H,OAAO,CAACrB,SAAS,CAACJ,MAAM,EAAE;EAC1D;EAEA4J,uBAAuBA,CAACC,KAAa;IACnC;IACA,IAAI,CAACvO,OAAO,CAAC+K,IAAI,CAAC,uCAAuC,CAAC;EAC5D;EAEAyD,mBAAmBA,CAAChB,OAAe;IACjC;IACA,IAAI,CAACxN,OAAO,CAAC+K,IAAI,CAAC,qCAAqC,CAAC;EAC1D;EAEA0D,SAASA,CAACjB,OAAe;IACvB;IACA,IAAI,CAACxN,OAAO,CAAC+K,IAAI,CAAC,iCAAiC,CAAC;EACtD;EAEA;;;;EAIA2D,aAAaA,CAACvE,QAAa;IACzB,IAAIA,QAAQ,IAAIA,QAAQ,CAAC8B,sBAAsB,IAAI9B,QAAQ,CAAC8B,sBAAsB,CAACvH,MAAM,GAAG,CAAC,EAAE;MAC7FJ,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAE4F,QAAQ,CAAC8B,sBAAsB,CAAC;MAElG;MACA,MAAMC,UAAU,GAAG/B,QAAQ,CAAClF,UAAU,IAAI,YAAY;MAEtD;MACA,IAAI,CAAC+G,0BAA0B,CAAC7B,QAAQ,CAAC8B,sBAAsB,EAAEC,UAAU,CAAC;KAC7E,MAAM;MACL5H,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;MAC7D;MACA,IAAI,CAACyH,0BAA0B,CAAC,EAAE,EAAE,YAAY,CAAC;;EAErD;EAEA;;;;;EAKA2C,qBAAqBA,CAACxE,QAAa;IACjC,OAAOA,QAAQ,IACRA,QAAQ,CAAC8B,sBAAsB,IAC/BnG,KAAK,CAACC,OAAO,CAACoE,QAAQ,CAAC8B,sBAAsB,CAAC,IAC9C9B,QAAQ,CAAC8B,sBAAsB,CAACvH,MAAM,GAAG,CAAC;EACnD;EAEA;;;;;EAKAkK,cAAcA,CAACzE,QAAa;IAC1B,IAAI,IAAI,CAACwE,qBAAqB,CAACxE,QAAQ,CAAC,EAAE;MACxC,OAAOA,QAAQ,CAAC8B,sBAAsB,CAACvH,MAAM;;IAE/C,OAAO,CAAC;EACV;;;;;;;;;;;;;;;;;;;;;;;;;;;cA3hCCtI,SAAS;QAAAyS,IAAA,GAAC,WAAW;MAAA;;cACrBzS,SAAS;QAAAyS,IAAA,GAAC,eAAe;MAAA;;cACzBzS,SAAS;QAAAyS,IAAA,GAAC,cAAc;MAAA;;cACxBzS,SAAS;QAAAyS,IAAA,GAAC,qBAAqB;MAAA;;cAC/BzS,SAAS;QAAAyS,IAAA,GAAC,iBAAiB;MAAA;;cAyH3BvS,YAAY;QAAAuS,IAAA,GAAC,eAAe,EAAE,CAAC,QAAQ,CAAC;MAAA;;;;AA9H9BrP,kBAAkB,GAAAH,UAAA,EArB9BlD,SAAS,CAAC;EACT2S,QAAQ,EAAE,gBAAgB;EAC1BvP,UAAU,EAAE,IAAI;EAChBwP,OAAO,EAAE,CACPvS,YAAY,EACZC,WAAW,EACXY,kBAAkB,EAClBG,cAAc,EACdO,cAAc,EACdN,cAAc,EACdC,aAAa,EACbC,eAAe,EACfC,aAAa,EACbI,yBAAyB,EACzBC,qBAAqB,EACrBC,kBAAkB,CACnB;EACD8Q,SAAS,EAAE,CAACnR,cAAc,CAAC;EAC3BoR,QAAA,EAAAC,oBAA0C;;CAE3C,CAAC,C,EACW1P,kBAAkB,CA6hC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}