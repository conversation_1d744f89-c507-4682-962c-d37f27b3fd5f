{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Pipe, NgModule } from '@angular/core';\nimport { sum, isNumberFinite, toDecimal, isNil } from 'ng-zorro-antd/core/util';\nimport * as i1 from '@angular/platform-browser';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzAggregatePipe {\n  transform(value, method) {\n    if (!Array.isArray(value)) {\n      return value;\n    }\n    if (value.length === 0) {\n      return undefined;\n    }\n    switch (method) {\n      case 'sum':\n        return sum(value);\n      case 'avg':\n        return sum(value) / value.length;\n      case 'max':\n        return Math.max(...value);\n      case 'min':\n        return Math.min(...value);\n      default:\n        throw Error(`Invalid Pipe Arguments: Aggregate pipe doesn't support this type`);\n    }\n  }\n  static {\n    this.ɵfac = function NzAggregatePipe_Factory(t) {\n      return new (t || NzAggregatePipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"nzAggregate\",\n      type: NzAggregatePipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzAggregatePipe, [{\n    type: Pipe,\n    args: [{\n      name: 'nzAggregate',\n      standalone: true\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzBytesPipe {\n  static {\n    this.formats = {\n      B: {\n        max: 1024\n      },\n      kB: {\n        max: Math.pow(1024, 2),\n        prev: 'B'\n      },\n      KB: {\n        max: Math.pow(1024, 2),\n        prev: 'B'\n      },\n      MB: {\n        max: Math.pow(1024, 3),\n        prev: 'kB'\n      },\n      GB: {\n        max: Math.pow(1024, 4),\n        prev: 'MB'\n      },\n      TB: {\n        max: Number.MAX_SAFE_INTEGER,\n        prev: 'GB'\n      }\n    };\n  }\n  transform(input, decimal = 0, from = 'B', to) {\n    if (!(isNumberFinite(input) && isNumberFinite(decimal) && decimal % 1 === 0 && decimal >= 0)) {\n      return input;\n    }\n    let bytes = input;\n    let unit = from;\n    while (unit !== 'B') {\n      bytes *= 1024;\n      unit = NzBytesPipe.formats[unit].prev;\n    }\n    if (to) {\n      const format = NzBytesPipe.formats[to];\n      const result = toDecimal(NzBytesPipe.calculateResult(format, bytes), decimal);\n      return NzBytesPipe.formatResult(result, to);\n    }\n    for (const key in NzBytesPipe.formats) {\n      if (NzBytesPipe.formats.hasOwnProperty(key)) {\n        const format = NzBytesPipe.formats[key];\n        if (bytes < format.max) {\n          const result = toDecimal(NzBytesPipe.calculateResult(format, bytes), decimal);\n          return NzBytesPipe.formatResult(result, key);\n        }\n      }\n    }\n  }\n  static formatResult(result, unit) {\n    return `${result} ${unit}`;\n  }\n  static calculateResult(format, bytes) {\n    const prev = format.prev ? NzBytesPipe.formats[format.prev] : undefined;\n    return prev ? bytes / prev.max : bytes;\n  }\n  static {\n    this.ɵfac = function NzBytesPipe_Factory(t) {\n      return new (t || NzBytesPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"nzBytes\",\n      type: NzBytesPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzBytesPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'nzBytes',\n      standalone: true\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzToCssUnitPipe {\n  transform(value, defaultUnit = 'px') {\n    return typeof value === 'number' ? `${value}${defaultUnit}` : value;\n  }\n  static {\n    this.ɵfac = function NzToCssUnitPipe_Factory(t) {\n      return new (t || NzToCssUnitPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"nzToCssUnit\",\n      type: NzToCssUnitPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzToCssUnitPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'nzToCssUnit',\n      standalone: true\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzEllipsisPipe {\n  transform(value, length, suffix = '') {\n    if (typeof value !== 'string') {\n      return value;\n    }\n    const len = typeof length === 'undefined' ? value.length : length;\n    if (value.length <= len) {\n      return value;\n    }\n    return value.substring(0, len) + suffix;\n  }\n  static {\n    this.ɵfac = function NzEllipsisPipe_Factory(t) {\n      return new (t || NzEllipsisPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"nzEllipsis\",\n      type: NzEllipsisPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzEllipsisPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'nzEllipsis',\n      standalone: true\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/**\n * @deprecated v17.0.0 - Use Nullish coalescing operator (??) instead of `NzSafeNullPipe`.\n */\nclass NzSafeNullPipe {\n  transform(value, replace = '') {\n    if (isNil(value)) {\n      return replace;\n    }\n    return value;\n  }\n  static {\n    this.ɵfac = function NzSafeNullPipe_Factory(t) {\n      return new (t || NzSafeNullPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"nzSafeNull\",\n      type: NzSafeNullPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSafeNullPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'nzSafeNull',\n      standalone: true\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzSanitizerPipe {\n  constructor(sanitizer) {\n    this.sanitizer = sanitizer;\n  }\n  transform(value, type = 'html') {\n    switch (type) {\n      case 'html':\n        return this.sanitizer.bypassSecurityTrustHtml(value);\n      case 'style':\n        return this.sanitizer.bypassSecurityTrustStyle(value);\n      case 'url':\n        return this.sanitizer.bypassSecurityTrustUrl(value);\n      case 'resourceUrl':\n        return this.sanitizer.bypassSecurityTrustResourceUrl(value);\n      default:\n        throw new Error(`Invalid safe type specified`);\n    }\n  }\n  static {\n    this.ɵfac = function NzSanitizerPipe_Factory(t) {\n      return new (t || NzSanitizerPipe)(i0.ɵɵdirectiveInject(i1.DomSanitizer, 16));\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"nzSanitizer\",\n      type: NzSanitizerPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSanitizerPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'nzSanitizer',\n      standalone: true\n    }]\n  }], () => [{\n    type: i1.DomSanitizer\n  }], null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTrimPipe {\n  // TODO(chensimeng) trimEnd, trimStart\n  transform(text) {\n    return text.trim();\n  }\n  static {\n    this.ɵfac = function NzTrimPipe_Factory(t) {\n      return new (t || NzTrimPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"nzTrim\",\n      type: NzTrimPipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTrimPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'nzTrim',\n      standalone: true\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst pipes = [NzToCssUnitPipe, NzSafeNullPipe, NzSanitizerPipe, NzTrimPipe, NzBytesPipe, NzAggregatePipe, NzEllipsisPipe];\nclass NzPipesModule {\n  static {\n    this.ɵfac = function NzPipesModule_Factory(t) {\n      return new (t || NzPipesModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzPipesModule,\n      imports: [NzToCssUnitPipe, NzSafeNullPipe, NzSanitizerPipe, NzTrimPipe, NzBytesPipe, NzAggregatePipe, NzEllipsisPipe],\n      exports: [NzToCssUnitPipe, NzSafeNullPipe, NzSanitizerPipe, NzTrimPipe, NzBytesPipe, NzAggregatePipe, NzEllipsisPipe]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzPipesModule, [{\n    type: NgModule,\n    args: [{\n      imports: [pipes],\n      exports: [pipes]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzAggregatePipe, NzBytesPipe, NzEllipsisPipe, NzPipesModule, NzSafeNullPipe, NzSanitizerPipe, NzToCssUnitPipe, NzTrimPipe };", "map": {"version": 3, "names": ["i0", "<PERSON><PERSON>", "NgModule", "sum", "isNumberFinite", "toDecimal", "isNil", "i1", "NzAggregatePipe", "transform", "value", "method", "Array", "isArray", "length", "undefined", "Math", "max", "min", "Error", "ɵfac", "NzAggregatePipe_Factory", "t", "ɵpipe", "ɵɵdefinePipe", "name", "type", "pure", "standalone", "ngDevMode", "ɵsetClassMetadata", "args", "NzBytesPipe", "formats", "B", "kB", "pow", "prev", "KB", "MB", "GB", "TB", "Number", "MAX_SAFE_INTEGER", "input", "decimal", "from", "to", "bytes", "unit", "format", "result", "calculateResult", "formatResult", "key", "hasOwnProperty", "NzBytesPipe_Factory", "NzToCssUnitPipe", "defaultUnit", "NzToCssUnitPipe_Factory", "NzEllipsisPipe", "suffix", "len", "substring", "NzEllipsisPipe_Factory", "NzSafeNullPipe", "replace", "NzSafeNullPipe_Factory", "NzSanitizerPipe", "constructor", "sanitizer", "bypassSecurityTrustHtml", "bypassSecurityTrustStyle", "bypassSecurityTrustUrl", "bypassSecurityTrustResourceUrl", "NzSanitizerPipe_Factory", "ɵɵdirectiveInject", "Dom<PERSON><PERSON><PERSON>zer", "NzTrimPipe", "text", "trim", "NzTrimPipe_Factory", "pipes", "NzPipesModule", "NzPipesModule_Factory", "ɵmod", "ɵɵdefineNgModule", "imports", "exports", "ɵinj", "ɵɵdefineInjector"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-pipes.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Pi<PERSON>, NgModule } from '@angular/core';\nimport { sum, isNumberFinite, toDecimal, isNil } from 'ng-zorro-antd/core/util';\nimport * as i1 from '@angular/platform-browser';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzAggregatePipe {\n    transform(value, method) {\n        if (!Array.isArray(value)) {\n            return value;\n        }\n        if (value.length === 0) {\n            return undefined;\n        }\n        switch (method) {\n            case 'sum':\n                return sum(value);\n            case 'avg':\n                return sum(value) / value.length;\n            case 'max':\n                return Math.max(...value);\n            case 'min':\n                return Math.min(...value);\n            default:\n                throw Error(`Invalid Pipe Arguments: Aggregate pipe doesn't support this type`);\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzAggregatePipe, deps: [], target: i0.ɵɵFactoryTarget.Pipe }); }\n    static { this.ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: \"14.0.0\", version: \"17.3.8\", ngImport: i0, type: NzAggregatePipe, isStandalone: true, name: \"nzAggregate\" }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzAggregatePipe, decorators: [{\n            type: Pipe,\n            args: [{\n                    name: 'nzAggregate',\n                    standalone: true\n                }]\n        }] });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzBytesPipe {\n    static { this.formats = {\n        B: { max: 1024 },\n        kB: { max: Math.pow(1024, 2), prev: 'B' },\n        KB: { max: Math.pow(1024, 2), prev: 'B' },\n        MB: { max: Math.pow(1024, 3), prev: 'kB' },\n        GB: { max: Math.pow(1024, 4), prev: 'MB' },\n        TB: { max: Number.MAX_SAFE_INTEGER, prev: 'GB' }\n    }; }\n    transform(input, decimal = 0, from = 'B', to) {\n        if (!(isNumberFinite(input) && isNumberFinite(decimal) && decimal % 1 === 0 && decimal >= 0)) {\n            return input;\n        }\n        let bytes = input;\n        let unit = from;\n        while (unit !== 'B') {\n            bytes *= 1024;\n            unit = NzBytesPipe.formats[unit].prev;\n        }\n        if (to) {\n            const format = NzBytesPipe.formats[to];\n            const result = toDecimal(NzBytesPipe.calculateResult(format, bytes), decimal);\n            return NzBytesPipe.formatResult(result, to);\n        }\n        for (const key in NzBytesPipe.formats) {\n            if (NzBytesPipe.formats.hasOwnProperty(key)) {\n                const format = NzBytesPipe.formats[key];\n                if (bytes < format.max) {\n                    const result = toDecimal(NzBytesPipe.calculateResult(format, bytes), decimal);\n                    return NzBytesPipe.formatResult(result, key);\n                }\n            }\n        }\n    }\n    static formatResult(result, unit) {\n        return `${result} ${unit}`;\n    }\n    static calculateResult(format, bytes) {\n        const prev = format.prev ? NzBytesPipe.formats[format.prev] : undefined;\n        return prev ? bytes / prev.max : bytes;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzBytesPipe, deps: [], target: i0.ɵɵFactoryTarget.Pipe }); }\n    static { this.ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: \"14.0.0\", version: \"17.3.8\", ngImport: i0, type: NzBytesPipe, isStandalone: true, name: \"nzBytes\" }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzBytesPipe, decorators: [{\n            type: Pipe,\n            args: [{\n                    name: 'nzBytes',\n                    standalone: true\n                }]\n        }] });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzToCssUnitPipe {\n    transform(value, defaultUnit = 'px') {\n        return typeof value === 'number' ? `${value}${defaultUnit}` : value;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzToCssUnitPipe, deps: [], target: i0.ɵɵFactoryTarget.Pipe }); }\n    static { this.ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: \"14.0.0\", version: \"17.3.8\", ngImport: i0, type: NzToCssUnitPipe, isStandalone: true, name: \"nzToCssUnit\" }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzToCssUnitPipe, decorators: [{\n            type: Pipe,\n            args: [{\n                    name: 'nzToCssUnit',\n                    standalone: true\n                }]\n        }] });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzEllipsisPipe {\n    transform(value, length, suffix = '') {\n        if (typeof value !== 'string') {\n            return value;\n        }\n        const len = typeof length === 'undefined' ? value.length : length;\n        if (value.length <= len) {\n            return value;\n        }\n        return value.substring(0, len) + suffix;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzEllipsisPipe, deps: [], target: i0.ɵɵFactoryTarget.Pipe }); }\n    static { this.ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: \"14.0.0\", version: \"17.3.8\", ngImport: i0, type: NzEllipsisPipe, isStandalone: true, name: \"nzEllipsis\" }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzEllipsisPipe, decorators: [{\n            type: Pipe,\n            args: [{\n                    name: 'nzEllipsis',\n                    standalone: true\n                }]\n        }] });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/**\n * @deprecated v17.0.0 - Use Nullish coalescing operator (??) instead of `NzSafeNullPipe`.\n */\nclass NzSafeNullPipe {\n    transform(value, replace = '') {\n        if (isNil(value)) {\n            return replace;\n        }\n        return value;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzSafeNullPipe, deps: [], target: i0.ɵɵFactoryTarget.Pipe }); }\n    static { this.ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: \"14.0.0\", version: \"17.3.8\", ngImport: i0, type: NzSafeNullPipe, isStandalone: true, name: \"nzSafeNull\" }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzSafeNullPipe, decorators: [{\n            type: Pipe,\n            args: [{\n                    name: 'nzSafeNull',\n                    standalone: true\n                }]\n        }] });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzSanitizerPipe {\n    constructor(sanitizer) {\n        this.sanitizer = sanitizer;\n    }\n    transform(value, type = 'html') {\n        switch (type) {\n            case 'html':\n                return this.sanitizer.bypassSecurityTrustHtml(value);\n            case 'style':\n                return this.sanitizer.bypassSecurityTrustStyle(value);\n            case 'url':\n                return this.sanitizer.bypassSecurityTrustUrl(value);\n            case 'resourceUrl':\n                return this.sanitizer.bypassSecurityTrustResourceUrl(value);\n            default:\n                throw new Error(`Invalid safe type specified`);\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzSanitizerPipe, deps: [{ token: i1.DomSanitizer }], target: i0.ɵɵFactoryTarget.Pipe }); }\n    static { this.ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: \"14.0.0\", version: \"17.3.8\", ngImport: i0, type: NzSanitizerPipe, isStandalone: true, name: \"nzSanitizer\" }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzSanitizerPipe, decorators: [{\n            type: Pipe,\n            args: [{\n                    name: 'nzSanitizer',\n                    standalone: true\n                }]\n        }], ctorParameters: () => [{ type: i1.DomSanitizer }] });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTrimPipe {\n    // TODO(chensimeng) trimEnd, trimStart\n    transform(text) {\n        return text.trim();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzTrimPipe, deps: [], target: i0.ɵɵFactoryTarget.Pipe }); }\n    static { this.ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: \"14.0.0\", version: \"17.3.8\", ngImport: i0, type: NzTrimPipe, isStandalone: true, name: \"nzTrim\" }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzTrimPipe, decorators: [{\n            type: Pipe,\n            args: [{\n                    name: 'nzTrim',\n                    standalone: true\n                }]\n        }] });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst pipes = [\n    NzToCssUnitPipe,\n    NzSafeNullPipe,\n    NzSanitizerPipe,\n    NzTrimPipe,\n    NzBytesPipe,\n    NzAggregatePipe,\n    NzEllipsisPipe\n];\nclass NzPipesModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzPipesModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.3.8\", ngImport: i0, type: NzPipesModule, imports: [NzToCssUnitPipe,\n            NzSafeNullPipe,\n            NzSanitizerPipe,\n            NzTrimPipe,\n            NzBytesPipe,\n            NzAggregatePipe,\n            NzEllipsisPipe], exports: [NzToCssUnitPipe,\n            NzSafeNullPipe,\n            NzSanitizerPipe,\n            NzTrimPipe,\n            NzBytesPipe,\n            NzAggregatePipe,\n            NzEllipsisPipe] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzPipesModule }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzPipesModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [pipes],\n                    exports: [pipes]\n                }]\n        }] });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzAggregatePipe, NzBytesPipe, NzEllipsisPipe, NzPipesModule, NzSafeNullPipe, NzSanitizerPipe, NzToCssUnitPipe, NzTrimPipe };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,IAAI,EAAEC,QAAQ,QAAQ,eAAe;AAC9C,SAASC,GAAG,EAAEC,cAAc,EAAEC,SAAS,EAAEC,KAAK,QAAQ,yBAAyB;AAC/E,OAAO,KAAKC,EAAE,MAAM,2BAA2B;;AAE/C;AACA;AACA;AACA;AACA,MAAMC,eAAe,CAAC;EAClBC,SAASA,CAACC,KAAK,EAAEC,MAAM,EAAE;IACrB,IAAI,CAACC,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,EAAE;MACvB,OAAOA,KAAK;IAChB;IACA,IAAIA,KAAK,CAACI,MAAM,KAAK,CAAC,EAAE;MACpB,OAAOC,SAAS;IACpB;IACA,QAAQJ,MAAM;MACV,KAAK,KAAK;QACN,OAAOR,GAAG,CAACO,KAAK,CAAC;MACrB,KAAK,KAAK;QACN,OAAOP,GAAG,CAACO,KAAK,CAAC,GAAGA,KAAK,CAACI,MAAM;MACpC,KAAK,KAAK;QACN,OAAOE,IAAI,CAACC,GAAG,CAAC,GAAGP,KAAK,CAAC;MAC7B,KAAK,KAAK;QACN,OAAOM,IAAI,CAACE,GAAG,CAAC,GAAGR,KAAK,CAAC;MAC7B;QACI,MAAMS,KAAK,CAAE,kEAAiE,CAAC;IACvF;EACJ;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,wBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFd,eAAe;IAAA,CAA8C;EAAE;EACzK;IAAS,IAAI,CAACe,KAAK,kBAD6EvB,EAAE,CAAAwB,YAAA;MAAAC,IAAA;MAAAC,IAAA,EACMlB,eAAe;MAAAmB,IAAA;MAAAC,UAAA;IAAA,EAA4C;EAAE;AACzK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoG7B,EAAE,CAAA8B,iBAAA,CAGXtB,eAAe,EAAc,CAAC;IAC7GkB,IAAI,EAAEzB,IAAI;IACV8B,IAAI,EAAE,CAAC;MACCN,IAAI,EAAE,aAAa;MACnBG,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA,MAAMI,WAAW,CAAC;EACd;IAAS,IAAI,CAACC,OAAO,GAAG;MACpBC,CAAC,EAAE;QAAEjB,GAAG,EAAE;MAAK,CAAC;MAChBkB,EAAE,EAAE;QAAElB,GAAG,EAAED,IAAI,CAACoB,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;QAAEC,IAAI,EAAE;MAAI,CAAC;MACzCC,EAAE,EAAE;QAAErB,GAAG,EAAED,IAAI,CAACoB,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;QAAEC,IAAI,EAAE;MAAI,CAAC;MACzCE,EAAE,EAAE;QAAEtB,GAAG,EAAED,IAAI,CAACoB,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;QAAEC,IAAI,EAAE;MAAK,CAAC;MAC1CG,EAAE,EAAE;QAAEvB,GAAG,EAAED,IAAI,CAACoB,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;QAAEC,IAAI,EAAE;MAAK,CAAC;MAC1CI,EAAE,EAAE;QAAExB,GAAG,EAAEyB,MAAM,CAACC,gBAAgB;QAAEN,IAAI,EAAE;MAAK;IACnD,CAAC;EAAE;EACH5B,SAASA,CAACmC,KAAK,EAAEC,OAAO,GAAG,CAAC,EAAEC,IAAI,GAAG,GAAG,EAAEC,EAAE,EAAE;IAC1C,IAAI,EAAE3C,cAAc,CAACwC,KAAK,CAAC,IAAIxC,cAAc,CAACyC,OAAO,CAAC,IAAIA,OAAO,GAAG,CAAC,KAAK,CAAC,IAAIA,OAAO,IAAI,CAAC,CAAC,EAAE;MAC1F,OAAOD,KAAK;IAChB;IACA,IAAII,KAAK,GAAGJ,KAAK;IACjB,IAAIK,IAAI,GAAGH,IAAI;IACf,OAAOG,IAAI,KAAK,GAAG,EAAE;MACjBD,KAAK,IAAI,IAAI;MACbC,IAAI,GAAGjB,WAAW,CAACC,OAAO,CAACgB,IAAI,CAAC,CAACZ,IAAI;IACzC;IACA,IAAIU,EAAE,EAAE;MACJ,MAAMG,MAAM,GAAGlB,WAAW,CAACC,OAAO,CAACc,EAAE,CAAC;MACtC,MAAMI,MAAM,GAAG9C,SAAS,CAAC2B,WAAW,CAACoB,eAAe,CAACF,MAAM,EAAEF,KAAK,CAAC,EAAEH,OAAO,CAAC;MAC7E,OAAOb,WAAW,CAACqB,YAAY,CAACF,MAAM,EAAEJ,EAAE,CAAC;IAC/C;IACA,KAAK,MAAMO,GAAG,IAAItB,WAAW,CAACC,OAAO,EAAE;MACnC,IAAID,WAAW,CAACC,OAAO,CAACsB,cAAc,CAACD,GAAG,CAAC,EAAE;QACzC,MAAMJ,MAAM,GAAGlB,WAAW,CAACC,OAAO,CAACqB,GAAG,CAAC;QACvC,IAAIN,KAAK,GAAGE,MAAM,CAACjC,GAAG,EAAE;UACpB,MAAMkC,MAAM,GAAG9C,SAAS,CAAC2B,WAAW,CAACoB,eAAe,CAACF,MAAM,EAAEF,KAAK,CAAC,EAAEH,OAAO,CAAC;UAC7E,OAAOb,WAAW,CAACqB,YAAY,CAACF,MAAM,EAAEG,GAAG,CAAC;QAChD;MACJ;IACJ;EACJ;EACA,OAAOD,YAAYA,CAACF,MAAM,EAAEF,IAAI,EAAE;IAC9B,OAAQ,GAAEE,MAAO,IAAGF,IAAK,EAAC;EAC9B;EACA,OAAOG,eAAeA,CAACF,MAAM,EAAEF,KAAK,EAAE;IAClC,MAAMX,IAAI,GAAGa,MAAM,CAACb,IAAI,GAAGL,WAAW,CAACC,OAAO,CAACiB,MAAM,CAACb,IAAI,CAAC,GAAGtB,SAAS;IACvE,OAAOsB,IAAI,GAAGW,KAAK,GAAGX,IAAI,CAACpB,GAAG,GAAG+B,KAAK;EAC1C;EACA;IAAS,IAAI,CAAC5B,IAAI,YAAAoC,oBAAAlC,CAAA;MAAA,YAAAA,CAAA,IAAwFU,WAAW;IAAA,CAA8C;EAAE;EACrK;IAAS,IAAI,CAACT,KAAK,kBAzD6EvB,EAAE,CAAAwB,YAAA;MAAAC,IAAA;MAAAC,IAAA,EAyDMM,WAAW;MAAAL,IAAA;MAAAC,UAAA;IAAA,EAAwC;EAAE;AACjK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA3DoG7B,EAAE,CAAA8B,iBAAA,CA2DXE,WAAW,EAAc,CAAC;IACzGN,IAAI,EAAEzB,IAAI;IACV8B,IAAI,EAAE,CAAC;MACCN,IAAI,EAAE,SAAS;MACfG,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA,MAAM6B,eAAe,CAAC;EAClBhD,SAASA,CAACC,KAAK,EAAEgD,WAAW,GAAG,IAAI,EAAE;IACjC,OAAO,OAAOhD,KAAK,KAAK,QAAQ,GAAI,GAAEA,KAAM,GAAEgD,WAAY,EAAC,GAAGhD,KAAK;EACvE;EACA;IAAS,IAAI,CAACU,IAAI,YAAAuC,wBAAArC,CAAA;MAAA,YAAAA,CAAA,IAAwFmC,eAAe;IAAA,CAA8C;EAAE;EACzK;IAAS,IAAI,CAAClC,KAAK,kBA5E6EvB,EAAE,CAAAwB,YAAA;MAAAC,IAAA;MAAAC,IAAA,EA4EM+B,eAAe;MAAA9B,IAAA;MAAAC,UAAA;IAAA,EAA4C;EAAE;AACzK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA9EoG7B,EAAE,CAAA8B,iBAAA,CA8EX2B,eAAe,EAAc,CAAC;IAC7G/B,IAAI,EAAEzB,IAAI;IACV8B,IAAI,EAAE,CAAC;MACCN,IAAI,EAAE,aAAa;MACnBG,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA,MAAMgC,cAAc,CAAC;EACjBnD,SAASA,CAACC,KAAK,EAAEI,MAAM,EAAE+C,MAAM,GAAG,EAAE,EAAE;IAClC,IAAI,OAAOnD,KAAK,KAAK,QAAQ,EAAE;MAC3B,OAAOA,KAAK;IAChB;IACA,MAAMoD,GAAG,GAAG,OAAOhD,MAAM,KAAK,WAAW,GAAGJ,KAAK,CAACI,MAAM,GAAGA,MAAM;IACjE,IAAIJ,KAAK,CAACI,MAAM,IAAIgD,GAAG,EAAE;MACrB,OAAOpD,KAAK;IAChB;IACA,OAAOA,KAAK,CAACqD,SAAS,CAAC,CAAC,EAAED,GAAG,CAAC,GAAGD,MAAM;EAC3C;EACA;IAAS,IAAI,CAACzC,IAAI,YAAA4C,uBAAA1C,CAAA;MAAA,YAAAA,CAAA,IAAwFsC,cAAc;IAAA,CAA8C;EAAE;EACxK;IAAS,IAAI,CAACrC,KAAK,kBAtG6EvB,EAAE,CAAAwB,YAAA;MAAAC,IAAA;MAAAC,IAAA,EAsGMkC,cAAc;MAAAjC,IAAA;MAAAC,UAAA;IAAA,EAA2C;EAAE;AACvK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAxGoG7B,EAAE,CAAA8B,iBAAA,CAwGX8B,cAAc,EAAc,CAAC;IAC5GlC,IAAI,EAAEzB,IAAI;IACV8B,IAAI,EAAE,CAAC;MACCN,IAAI,EAAE,YAAY;MAClBG,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMqC,cAAc,CAAC;EACjBxD,SAASA,CAACC,KAAK,EAAEwD,OAAO,GAAG,EAAE,EAAE;IAC3B,IAAI5D,KAAK,CAACI,KAAK,CAAC,EAAE;MACd,OAAOwD,OAAO;IAClB;IACA,OAAOxD,KAAK;EAChB;EACA;IAAS,IAAI,CAACU,IAAI,YAAA+C,uBAAA7C,CAAA;MAAA,YAAAA,CAAA,IAAwF2C,cAAc;IAAA,CAA8C;EAAE;EACxK;IAAS,IAAI,CAAC1C,KAAK,kBA/H6EvB,EAAE,CAAAwB,YAAA;MAAAC,IAAA;MAAAC,IAAA,EA+HMuC,cAAc;MAAAtC,IAAA;MAAAC,UAAA;IAAA,EAA2C;EAAE;AACvK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAjIoG7B,EAAE,CAAA8B,iBAAA,CAiIXmC,cAAc,EAAc,CAAC;IAC5GvC,IAAI,EAAEzB,IAAI;IACV8B,IAAI,EAAE,CAAC;MACCN,IAAI,EAAE,YAAY;MAClBG,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA,MAAMwC,eAAe,CAAC;EAClBC,WAAWA,CAACC,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;EAC9B;EACA7D,SAASA,CAACC,KAAK,EAAEgB,IAAI,GAAG,MAAM,EAAE;IAC5B,QAAQA,IAAI;MACR,KAAK,MAAM;QACP,OAAO,IAAI,CAAC4C,SAAS,CAACC,uBAAuB,CAAC7D,KAAK,CAAC;MACxD,KAAK,OAAO;QACR,OAAO,IAAI,CAAC4D,SAAS,CAACE,wBAAwB,CAAC9D,KAAK,CAAC;MACzD,KAAK,KAAK;QACN,OAAO,IAAI,CAAC4D,SAAS,CAACG,sBAAsB,CAAC/D,KAAK,CAAC;MACvD,KAAK,aAAa;QACd,OAAO,IAAI,CAAC4D,SAAS,CAACI,8BAA8B,CAAChE,KAAK,CAAC;MAC/D;QACI,MAAM,IAAIS,KAAK,CAAE,6BAA4B,CAAC;IACtD;EACJ;EACA;IAAS,IAAI,CAACC,IAAI,YAAAuD,wBAAArD,CAAA;MAAA,YAAAA,CAAA,IAAwF8C,eAAe,EA/JzBpE,EAAE,CAAA4E,iBAAA,CA+JyCrE,EAAE,CAACsE,YAAY;IAAA,CAAuC;EAAE;EACnM;IAAS,IAAI,CAACtD,KAAK,kBAhK6EvB,EAAE,CAAAwB,YAAA;MAAAC,IAAA;MAAAC,IAAA,EAgKM0C,eAAe;MAAAzC,IAAA;MAAAC,UAAA;IAAA,EAA4C;EAAE;AACzK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAlKoG7B,EAAE,CAAA8B,iBAAA,CAkKXsC,eAAe,EAAc,CAAC;IAC7G1C,IAAI,EAAEzB,IAAI;IACV8B,IAAI,EAAE,CAAC;MACCN,IAAI,EAAE,aAAa;MACnBG,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEF,IAAI,EAAEnB,EAAE,CAACsE;EAAa,CAAC,CAAC;AAAA;;AAE7D;AACA;AACA;AACA;AACA,MAAMC,UAAU,CAAC;EACb;EACArE,SAASA,CAACsE,IAAI,EAAE;IACZ,OAAOA,IAAI,CAACC,IAAI,CAAC,CAAC;EACtB;EACA;IAAS,IAAI,CAAC5D,IAAI,YAAA6D,mBAAA3D,CAAA;MAAA,YAAAA,CAAA,IAAwFwD,UAAU;IAAA,CAA8C;EAAE;EACpK;IAAS,IAAI,CAACvD,KAAK,kBApL6EvB,EAAE,CAAAwB,YAAA;MAAAC,IAAA;MAAAC,IAAA,EAoLMoD,UAAU;MAAAnD,IAAA;MAAAC,UAAA;IAAA,EAAuC;EAAE;AAC/J;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAtLoG7B,EAAE,CAAA8B,iBAAA,CAsLXgD,UAAU,EAAc,CAAC;IACxGpD,IAAI,EAAEzB,IAAI;IACV8B,IAAI,EAAE,CAAC;MACCN,IAAI,EAAE,QAAQ;MACdG,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA,MAAMsD,KAAK,GAAG,CACVzB,eAAe,EACfQ,cAAc,EACdG,eAAe,EACfU,UAAU,EACV9C,WAAW,EACXxB,eAAe,EACfoD,cAAc,CACjB;AACD,MAAMuB,aAAa,CAAC;EAChB;IAAS,IAAI,CAAC/D,IAAI,YAAAgE,sBAAA9D,CAAA;MAAA,YAAAA,CAAA,IAAwF6D,aAAa;IAAA,CAAkD;EAAE;EAC3K;IAAS,IAAI,CAACE,IAAI,kBA7M8ErF,EAAE,CAAAsF,gBAAA;MAAA5D,IAAA,EA6MSyD,aAAa;MAAAI,OAAA,GAAY9B,eAAe,EAC3IQ,cAAc,EACdG,eAAe,EACfU,UAAU,EACV9C,WAAW,EACXxB,eAAe,EACfoD,cAAc;MAAA4B,OAAA,GAAa/B,eAAe,EAC1CQ,cAAc,EACdG,eAAe,EACfU,UAAU,EACV9C,WAAW,EACXxB,eAAe,EACfoD,cAAc;IAAA,EAAI;EAAE;EAC5B;IAAS,IAAI,CAAC6B,IAAI,kBA1N8EzF,EAAE,CAAA0F,gBAAA,IA0NyB;EAAE;AACjI;AACA;EAAA,QAAA7D,SAAA,oBAAAA,SAAA,KA5NoG7B,EAAE,CAAA8B,iBAAA,CA4NXqD,aAAa,EAAc,CAAC;IAC3GzD,IAAI,EAAExB,QAAQ;IACd6B,IAAI,EAAE,CAAC;MACCwD,OAAO,EAAE,CAACL,KAAK,CAAC;MAChBM,OAAO,EAAE,CAACN,KAAK;IACnB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAAS1E,eAAe,EAAEwB,WAAW,EAAE4B,cAAc,EAAEuB,aAAa,EAAElB,cAAc,EAAEG,eAAe,EAAEX,eAAe,EAAEqB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}