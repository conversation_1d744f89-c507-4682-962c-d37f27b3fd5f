{"ast": null, "code": "const levels = ['error', 'warn', 'log', 'info'];\nlet level = 'warn';\nfunction debug(method) {\n  if (level) {\n    if (levels.indexOf(method) <= levels.indexOf(level)) {\n      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        args[_key - 1] = arguments[_key];\n      }\n      console[method](...args); // eslint-disable-line no-console\n    }\n  }\n}\nfunction namespace(ns) {\n  return levels.reduce((logger, method) => {\n    logger[method] = debug.bind(console, method, ns);\n    return logger;\n  }, {});\n}\nnamespace.level = newLevel => {\n  level = newLevel;\n};\ndebug.level = namespace.level;\nexport default namespace;", "map": {"version": 3, "names": ["levels", "level", "debug", "method", "indexOf", "_len", "arguments", "length", "args", "Array", "_key", "console", "namespace", "ns", "reduce", "logger", "bind", "newLevel"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/quill/core/logger.js"], "sourcesContent": ["const levels = ['error', 'warn', 'log', 'info'];\nlet level = 'warn';\nfunction debug(method) {\n  if (level) {\n    if (levels.indexOf(method) <= levels.indexOf(level)) {\n      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        args[_key - 1] = arguments[_key];\n      }\n      console[method](...args); // eslint-disable-line no-console\n    }\n  }\n}\nfunction namespace(ns) {\n  return levels.reduce((logger, method) => {\n    logger[method] = debug.bind(console, method, ns);\n    return logger;\n  }, {});\n}\nnamespace.level = newLevel => {\n  level = newLevel;\n};\ndebug.level = namespace.level;\nexport default namespace;\n"], "mappings": "AAAA,MAAMA,MAAM,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC;AAC/C,IAAIC,KAAK,GAAG,MAAM;AAClB,SAASC,KAAKA,CAACC,MAAM,EAAE;EACrB,IAAIF,KAAK,EAAE;IACT,IAAID,MAAM,CAACI,OAAO,CAACD,MAAM,CAAC,IAAIH,MAAM,CAACI,OAAO,CAACH,KAAK,CAAC,EAAE;MACnD,KAAK,IAAII,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;QAC1GF,IAAI,CAACE,IAAI,GAAG,CAAC,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;MAClC;MACAC,OAAO,CAACR,MAAM,CAAC,CAAC,GAAGK,IAAI,CAAC,CAAC,CAAC;IAC5B;EACF;AACF;AACA,SAASI,SAASA,CAACC,EAAE,EAAE;EACrB,OAAOb,MAAM,CAACc,MAAM,CAAC,CAACC,MAAM,EAAEZ,MAAM,KAAK;IACvCY,MAAM,CAACZ,MAAM,CAAC,GAAGD,KAAK,CAACc,IAAI,CAACL,OAAO,EAAER,MAAM,EAAEU,EAAE,CAAC;IAChD,OAAOE,MAAM;EACf,CAAC,EAAE,CAAC,CAAC,CAAC;AACR;AACAH,SAAS,CAACX,KAAK,GAAGgB,QAAQ,IAAI;EAC5BhB,KAAK,GAAGgB,QAAQ;AAClB,CAAC;AACDf,KAAK,CAACD,KAAK,GAAGW,SAAS,CAACX,KAAK;AAC7B,eAAeW,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}