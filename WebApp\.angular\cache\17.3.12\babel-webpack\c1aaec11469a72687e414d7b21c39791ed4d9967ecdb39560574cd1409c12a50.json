{"ast": null, "code": "import DateTime, { friendlyDateTime } from \"./datetime.js\";\nimport Duration from \"./duration.js\";\nimport Settings from \"./settings.js\";\nimport { InvalidArgumentError, InvalidIntervalError } from \"./errors.js\";\nimport Invalid from \"./impl/invalid.js\";\nimport Formatter from \"./impl/formatter.js\";\nimport * as Formats from \"./impl/formats.js\";\nconst INVALID = \"Invalid Interval\";\n\n// checks if the start is equal to or before the end\nfunction validateStartEnd(start, end) {\n  if (!start || !start.isValid) {\n    return Interval.invalid(\"missing or invalid start\");\n  } else if (!end || !end.isValid) {\n    return Interval.invalid(\"missing or invalid end\");\n  } else if (end < start) {\n    return Interval.invalid(\"end before start\", `The end of an interval must be after its start, but you had start=${start.toISO()} and end=${end.toISO()}`);\n  } else {\n    return null;\n  }\n}\n\n/**\n * An Interval object represents a half-open interval of time, where each endpoint is a {@link DateTime}. Conceptually, it's a container for those two endpoints, accompanied by methods for creating, parsing, interrogating, comparing, transforming, and formatting them.\n *\n * Here is a brief overview of the most commonly used methods and getters in Interval:\n *\n * * **Creation** To create an Interval, use {@link Interval.fromDateTimes}, {@link Interval.after}, {@link Interval.before}, or {@link Interval.fromISO}.\n * * **Accessors** Use {@link Interval#start} and {@link Interval#end} to get the start and end.\n * * **Interrogation** To analyze the Interval, use {@link Interval#count}, {@link Interval#length}, {@link Interval#hasSame}, {@link Interval#contains}, {@link Interval#isAfter}, or {@link Interval#isBefore}.\n * * **Transformation** To create other Intervals out of this one, use {@link Interval#set}, {@link Interval#splitAt}, {@link Interval#splitBy}, {@link Interval#divideEqually}, {@link Interval.merge}, {@link Interval.xor}, {@link Interval#union}, {@link Interval#intersection}, or {@link Interval#difference}.\n * * **Comparison** To compare this Interval to another one, use {@link Interval#equals}, {@link Interval#overlaps}, {@link Interval#abutsStart}, {@link Interval#abutsEnd}, {@link Interval#engulfs}\n * * **Output** To convert the Interval into other representations, see {@link Interval#toString}, {@link Interval#toLocaleString}, {@link Interval#toISO}, {@link Interval#toISODate}, {@link Interval#toISOTime}, {@link Interval#toFormat}, and {@link Interval#toDuration}.\n */\nexport default class Interval {\n  /**\n   * @private\n   */\n  constructor(config) {\n    /**\n     * @access private\n     */\n    this.s = config.start;\n    /**\n     * @access private\n     */\n    this.e = config.end;\n    /**\n     * @access private\n     */\n    this.invalid = config.invalid || null;\n    /**\n     * @access private\n     */\n    this.isLuxonInterval = true;\n  }\n\n  /**\n   * Create an invalid Interval.\n   * @param {string} reason - simple string of why this Interval is invalid. Should not contain parameters or anything else data-dependent\n   * @param {string} [explanation=null] - longer explanation, may include parameters and other useful debugging information\n   * @return {Interval}\n   */\n  static invalid(reason, explanation = null) {\n    if (!reason) {\n      throw new InvalidArgumentError(\"need to specify a reason the Interval is invalid\");\n    }\n    const invalid = reason instanceof Invalid ? reason : new Invalid(reason, explanation);\n    if (Settings.throwOnInvalid) {\n      throw new InvalidIntervalError(invalid);\n    } else {\n      return new Interval({\n        invalid\n      });\n    }\n  }\n\n  /**\n   * Create an Interval from a start DateTime and an end DateTime. Inclusive of the start but not the end.\n   * @param {DateTime|Date|Object} start\n   * @param {DateTime|Date|Object} end\n   * @return {Interval}\n   */\n  static fromDateTimes(start, end) {\n    const builtStart = friendlyDateTime(start),\n      builtEnd = friendlyDateTime(end);\n    const validateError = validateStartEnd(builtStart, builtEnd);\n    if (validateError == null) {\n      return new Interval({\n        start: builtStart,\n        end: builtEnd\n      });\n    } else {\n      return validateError;\n    }\n  }\n\n  /**\n   * Create an Interval from a start DateTime and a Duration to extend to.\n   * @param {DateTime|Date|Object} start\n   * @param {Duration|Object|number} duration - the length of the Interval.\n   * @return {Interval}\n   */\n  static after(start, duration) {\n    const dur = Duration.fromDurationLike(duration),\n      dt = friendlyDateTime(start);\n    return Interval.fromDateTimes(dt, dt.plus(dur));\n  }\n\n  /**\n   * Create an Interval from an end DateTime and a Duration to extend backwards to.\n   * @param {DateTime|Date|Object} end\n   * @param {Duration|Object|number} duration - the length of the Interval.\n   * @return {Interval}\n   */\n  static before(end, duration) {\n    const dur = Duration.fromDurationLike(duration),\n      dt = friendlyDateTime(end);\n    return Interval.fromDateTimes(dt.minus(dur), dt);\n  }\n\n  /**\n   * Create an Interval from an ISO 8601 string.\n   * Accepts `<start>/<end>`, `<start>/<duration>`, and `<duration>/<end>` formats.\n   * @param {string} text - the ISO string to parse\n   * @param {Object} [opts] - options to pass {@link DateTime#fromISO} and optionally {@link Duration#fromISO}\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Time_intervals\n   * @return {Interval}\n   */\n  static fromISO(text, opts) {\n    const [s, e] = (text || \"\").split(\"/\", 2);\n    if (s && e) {\n      let start, startIsValid;\n      try {\n        start = DateTime.fromISO(s, opts);\n        startIsValid = start.isValid;\n      } catch (e) {\n        startIsValid = false;\n      }\n      let end, endIsValid;\n      try {\n        end = DateTime.fromISO(e, opts);\n        endIsValid = end.isValid;\n      } catch (e) {\n        endIsValid = false;\n      }\n      if (startIsValid && endIsValid) {\n        return Interval.fromDateTimes(start, end);\n      }\n      if (startIsValid) {\n        const dur = Duration.fromISO(e, opts);\n        if (dur.isValid) {\n          return Interval.after(start, dur);\n        }\n      } else if (endIsValid) {\n        const dur = Duration.fromISO(s, opts);\n        if (dur.isValid) {\n          return Interval.before(end, dur);\n        }\n      }\n    }\n    return Interval.invalid(\"unparsable\", `the input \"${text}\" can't be parsed as ISO 8601`);\n  }\n\n  /**\n   * Check if an object is an Interval. Works across context boundaries\n   * @param {object} o\n   * @return {boolean}\n   */\n  static isInterval(o) {\n    return o && o.isLuxonInterval || false;\n  }\n\n  /**\n   * Returns the start of the Interval\n   * @type {DateTime}\n   */\n  get start() {\n    return this.isValid ? this.s : null;\n  }\n\n  /**\n   * Returns the end of the Interval\n   * @type {DateTime}\n   */\n  get end() {\n    return this.isValid ? this.e : null;\n  }\n\n  /**\n   * Returns whether this Interval's end is at least its start, meaning that the Interval isn't 'backwards'.\n   * @type {boolean}\n   */\n  get isValid() {\n    return this.invalidReason === null;\n  }\n\n  /**\n   * Returns an error code if this Interval is invalid, or null if the Interval is valid\n   * @type {string}\n   */\n  get invalidReason() {\n    return this.invalid ? this.invalid.reason : null;\n  }\n\n  /**\n   * Returns an explanation of why this Interval became invalid, or null if the Interval is valid\n   * @type {string}\n   */\n  get invalidExplanation() {\n    return this.invalid ? this.invalid.explanation : null;\n  }\n\n  /**\n   * Returns the length of the Interval in the specified unit.\n   * @param {string} unit - the unit (such as 'hours' or 'days') to return the length in.\n   * @return {number}\n   */\n  length(unit = \"milliseconds\") {\n    return this.isValid ? this.toDuration(...[unit]).get(unit) : NaN;\n  }\n\n  /**\n   * Returns the count of minutes, hours, days, months, or years included in the Interval, even in part.\n   * Unlike {@link Interval#length} this counts sections of the calendar, not periods of time, e.g. specifying 'day'\n   * asks 'what dates are included in this interval?', not 'how many days long is this interval?'\n   * @param {string} [unit='milliseconds'] - the unit of time to count.\n   * @param {Object} opts - options\n   * @param {boolean} [opts.useLocaleWeeks=false] - If true, use weeks based on the locale, i.e. use the locale-dependent start of the week; this operation will always use the locale of the start DateTime\n   * @return {number}\n   */\n  count(unit = \"milliseconds\", opts) {\n    if (!this.isValid) return NaN;\n    const start = this.start.startOf(unit, opts);\n    let end;\n    if (opts?.useLocaleWeeks) {\n      end = this.end.reconfigure({\n        locale: start.locale\n      });\n    } else {\n      end = this.end;\n    }\n    end = end.startOf(unit, opts);\n    return Math.floor(end.diff(start, unit).get(unit)) + (end.valueOf() !== this.end.valueOf());\n  }\n\n  /**\n   * Returns whether this Interval's start and end are both in the same unit of time\n   * @param {string} unit - the unit of time to check sameness on\n   * @return {boolean}\n   */\n  hasSame(unit) {\n    return this.isValid ? this.isEmpty() || this.e.minus(1).hasSame(this.s, unit) : false;\n  }\n\n  /**\n   * Return whether this Interval has the same start and end DateTimes.\n   * @return {boolean}\n   */\n  isEmpty() {\n    return this.s.valueOf() === this.e.valueOf();\n  }\n\n  /**\n   * Return whether this Interval's start is after the specified DateTime.\n   * @param {DateTime} dateTime\n   * @return {boolean}\n   */\n  isAfter(dateTime) {\n    if (!this.isValid) return false;\n    return this.s > dateTime;\n  }\n\n  /**\n   * Return whether this Interval's end is before the specified DateTime.\n   * @param {DateTime} dateTime\n   * @return {boolean}\n   */\n  isBefore(dateTime) {\n    if (!this.isValid) return false;\n    return this.e <= dateTime;\n  }\n\n  /**\n   * Return whether this Interval contains the specified DateTime.\n   * @param {DateTime} dateTime\n   * @return {boolean}\n   */\n  contains(dateTime) {\n    if (!this.isValid) return false;\n    return this.s <= dateTime && this.e > dateTime;\n  }\n\n  /**\n   * \"Sets\" the start and/or end dates. Returns a newly-constructed Interval.\n   * @param {Object} values - the values to set\n   * @param {DateTime} values.start - the starting DateTime\n   * @param {DateTime} values.end - the ending DateTime\n   * @return {Interval}\n   */\n  set({\n    start,\n    end\n  } = {}) {\n    if (!this.isValid) return this;\n    return Interval.fromDateTimes(start || this.s, end || this.e);\n  }\n\n  /**\n   * Split this Interval at each of the specified DateTimes\n   * @param {...DateTime} dateTimes - the unit of time to count.\n   * @return {Array}\n   */\n  splitAt(...dateTimes) {\n    if (!this.isValid) return [];\n    const sorted = dateTimes.map(friendlyDateTime).filter(d => this.contains(d)).sort((a, b) => a.toMillis() - b.toMillis()),\n      results = [];\n    let {\n        s\n      } = this,\n      i = 0;\n    while (s < this.e) {\n      const added = sorted[i] || this.e,\n        next = +added > +this.e ? this.e : added;\n      results.push(Interval.fromDateTimes(s, next));\n      s = next;\n      i += 1;\n    }\n    return results;\n  }\n\n  /**\n   * Split this Interval into smaller Intervals, each of the specified length.\n   * Left over time is grouped into a smaller interval\n   * @param {Duration|Object|number} duration - The length of each resulting interval.\n   * @return {Array}\n   */\n  splitBy(duration) {\n    const dur = Duration.fromDurationLike(duration);\n    if (!this.isValid || !dur.isValid || dur.as(\"milliseconds\") === 0) {\n      return [];\n    }\n    let {\n        s\n      } = this,\n      idx = 1,\n      next;\n    const results = [];\n    while (s < this.e) {\n      const added = this.start.plus(dur.mapUnits(x => x * idx));\n      next = +added > +this.e ? this.e : added;\n      results.push(Interval.fromDateTimes(s, next));\n      s = next;\n      idx += 1;\n    }\n    return results;\n  }\n\n  /**\n   * Split this Interval into the specified number of smaller intervals.\n   * @param {number} numberOfParts - The number of Intervals to divide the Interval into.\n   * @return {Array}\n   */\n  divideEqually(numberOfParts) {\n    if (!this.isValid) return [];\n    return this.splitBy(this.length() / numberOfParts).slice(0, numberOfParts);\n  }\n\n  /**\n   * Return whether this Interval overlaps with the specified Interval\n   * @param {Interval} other\n   * @return {boolean}\n   */\n  overlaps(other) {\n    return this.e > other.s && this.s < other.e;\n  }\n\n  /**\n   * Return whether this Interval's end is adjacent to the specified Interval's start.\n   * @param {Interval} other\n   * @return {boolean}\n   */\n  abutsStart(other) {\n    if (!this.isValid) return false;\n    return +this.e === +other.s;\n  }\n\n  /**\n   * Return whether this Interval's start is adjacent to the specified Interval's end.\n   * @param {Interval} other\n   * @return {boolean}\n   */\n  abutsEnd(other) {\n    if (!this.isValid) return false;\n    return +other.e === +this.s;\n  }\n\n  /**\n   * Returns true if this Interval fully contains the specified Interval, specifically if the intersect (of this Interval and the other Interval) is equal to the other Interval; false otherwise.\n   * @param {Interval} other\n   * @return {boolean}\n   */\n  engulfs(other) {\n    if (!this.isValid) return false;\n    return this.s <= other.s && this.e >= other.e;\n  }\n\n  /**\n   * Return whether this Interval has the same start and end as the specified Interval.\n   * @param {Interval} other\n   * @return {boolean}\n   */\n  equals(other) {\n    if (!this.isValid || !other.isValid) {\n      return false;\n    }\n    return this.s.equals(other.s) && this.e.equals(other.e);\n  }\n\n  /**\n   * Return an Interval representing the intersection of this Interval and the specified Interval.\n   * Specifically, the resulting Interval has the maximum start time and the minimum end time of the two Intervals.\n   * Returns null if the intersection is empty, meaning, the intervals don't intersect.\n   * @param {Interval} other\n   * @return {Interval}\n   */\n  intersection(other) {\n    if (!this.isValid) return this;\n    const s = this.s > other.s ? this.s : other.s,\n      e = this.e < other.e ? this.e : other.e;\n    if (s >= e) {\n      return null;\n    } else {\n      return Interval.fromDateTimes(s, e);\n    }\n  }\n\n  /**\n   * Return an Interval representing the union of this Interval and the specified Interval.\n   * Specifically, the resulting Interval has the minimum start time and the maximum end time of the two Intervals.\n   * @param {Interval} other\n   * @return {Interval}\n   */\n  union(other) {\n    if (!this.isValid) return this;\n    const s = this.s < other.s ? this.s : other.s,\n      e = this.e > other.e ? this.e : other.e;\n    return Interval.fromDateTimes(s, e);\n  }\n\n  /**\n   * Merge an array of Intervals into a equivalent minimal set of Intervals.\n   * Combines overlapping and adjacent Intervals.\n   * @param {Array} intervals\n   * @return {Array}\n   */\n  static merge(intervals) {\n    const [found, final] = intervals.sort((a, b) => a.s - b.s).reduce(([sofar, current], item) => {\n      if (!current) {\n        return [sofar, item];\n      } else if (current.overlaps(item) || current.abutsStart(item)) {\n        return [sofar, current.union(item)];\n      } else {\n        return [sofar.concat([current]), item];\n      }\n    }, [[], null]);\n    if (final) {\n      found.push(final);\n    }\n    return found;\n  }\n\n  /**\n   * Return an array of Intervals representing the spans of time that only appear in one of the specified Intervals.\n   * @param {Array} intervals\n   * @return {Array}\n   */\n  static xor(intervals) {\n    let start = null,\n      currentCount = 0;\n    const results = [],\n      ends = intervals.map(i => [{\n        time: i.s,\n        type: \"s\"\n      }, {\n        time: i.e,\n        type: \"e\"\n      }]),\n      flattened = Array.prototype.concat(...ends),\n      arr = flattened.sort((a, b) => a.time - b.time);\n    for (const i of arr) {\n      currentCount += i.type === \"s\" ? 1 : -1;\n      if (currentCount === 1) {\n        start = i.time;\n      } else {\n        if (start && +start !== +i.time) {\n          results.push(Interval.fromDateTimes(start, i.time));\n        }\n        start = null;\n      }\n    }\n    return Interval.merge(results);\n  }\n\n  /**\n   * Return an Interval representing the span of time in this Interval that doesn't overlap with any of the specified Intervals.\n   * @param {...Interval} intervals\n   * @return {Array}\n   */\n  difference(...intervals) {\n    return Interval.xor([this].concat(intervals)).map(i => this.intersection(i)).filter(i => i && !i.isEmpty());\n  }\n\n  /**\n   * Returns a string representation of this Interval appropriate for debugging.\n   * @return {string}\n   */\n  toString() {\n    if (!this.isValid) return INVALID;\n    return `[${this.s.toISO()} – ${this.e.toISO()})`;\n  }\n\n  /**\n   * Returns a string representation of this Interval appropriate for the REPL.\n   * @return {string}\n   */\n  [Symbol.for(\"nodejs.util.inspect.custom\")]() {\n    if (this.isValid) {\n      return `Interval { start: ${this.s.toISO()}, end: ${this.e.toISO()} }`;\n    } else {\n      return `Interval { Invalid, reason: ${this.invalidReason} }`;\n    }\n  }\n\n  /**\n   * Returns a localized string representing this Interval. Accepts the same options as the\n   * Intl.DateTimeFormat constructor and any presets defined by Luxon, such as\n   * {@link DateTime.DATE_FULL} or {@link DateTime.TIME_SIMPLE}. The exact behavior of this method\n   * is browser-specific, but in general it will return an appropriate representation of the\n   * Interval in the assigned locale. Defaults to the system's locale if no locale has been\n   * specified.\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/DateTimeFormat\n   * @param {Object} [formatOpts=DateTime.DATE_SHORT] - Either a DateTime preset or\n   * Intl.DateTimeFormat constructor options.\n   * @param {Object} opts - Options to override the configuration of the start DateTime.\n   * @example Interval.fromISO('2022-11-07T09:00Z/2022-11-08T09:00Z').toLocaleString(); //=> 11/7/2022 – 11/8/2022\n   * @example Interval.fromISO('2022-11-07T09:00Z/2022-11-08T09:00Z').toLocaleString(DateTime.DATE_FULL); //=> November 7 – 8, 2022\n   * @example Interval.fromISO('2022-11-07T09:00Z/2022-11-08T09:00Z').toLocaleString(DateTime.DATE_FULL, { locale: 'fr-FR' }); //=> 7–8 novembre 2022\n   * @example Interval.fromISO('2022-11-07T17:00Z/2022-11-07T19:00Z').toLocaleString(DateTime.TIME_SIMPLE); //=> 6:00 – 8:00 PM\n   * @example Interval.fromISO('2022-11-07T17:00Z/2022-11-07T19:00Z').toLocaleString({ weekday: 'short', month: 'short', day: '2-digit', hour: '2-digit', minute: '2-digit' }); //=> Mon, Nov 07, 6:00 – 8:00 p\n   * @return {string}\n   */\n  toLocaleString(formatOpts = Formats.DATE_SHORT, opts = {}) {\n    return this.isValid ? Formatter.create(this.s.loc.clone(opts), formatOpts).formatInterval(this) : INVALID;\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this Interval.\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Time_intervals\n   * @param {Object} opts - The same options as {@link DateTime#toISO}\n   * @return {string}\n   */\n  toISO(opts) {\n    if (!this.isValid) return INVALID;\n    return `${this.s.toISO(opts)}/${this.e.toISO(opts)}`;\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of date of this Interval.\n   * The time components are ignored.\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Time_intervals\n   * @return {string}\n   */\n  toISODate() {\n    if (!this.isValid) return INVALID;\n    return `${this.s.toISODate()}/${this.e.toISODate()}`;\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of time of this Interval.\n   * The date components are ignored.\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Time_intervals\n   * @param {Object} opts - The same options as {@link DateTime#toISO}\n   * @return {string}\n   */\n  toISOTime(opts) {\n    if (!this.isValid) return INVALID;\n    return `${this.s.toISOTime(opts)}/${this.e.toISOTime(opts)}`;\n  }\n\n  /**\n   * Returns a string representation of this Interval formatted according to the specified format\n   * string. **You may not want this.** See {@link Interval#toLocaleString} for a more flexible\n   * formatting tool.\n   * @param {string} dateFormat - The format string. This string formats the start and end time.\n   * See {@link DateTime#toFormat} for details.\n   * @param {Object} opts - Options.\n   * @param {string} [opts.separator =  ' – '] - A separator to place between the start and end\n   * representations.\n   * @return {string}\n   */\n  toFormat(dateFormat, {\n    separator = \" – \"\n  } = {}) {\n    if (!this.isValid) return INVALID;\n    return `${this.s.toFormat(dateFormat)}${separator}${this.e.toFormat(dateFormat)}`;\n  }\n\n  /**\n   * Return a Duration representing the time spanned by this interval.\n   * @param {string|string[]} [unit=['milliseconds']] - the unit or units (such as 'hours' or 'days') to include in the duration.\n   * @param {Object} opts - options that affect the creation of the Duration\n   * @param {string} [opts.conversionAccuracy='casual'] - the conversion system to use\n   * @example Interval.fromDateTimes(dt1, dt2).toDuration().toObject() //=> { milliseconds: 88489257 }\n   * @example Interval.fromDateTimes(dt1, dt2).toDuration('days').toObject() //=> { days: 1.0241812152777778 }\n   * @example Interval.fromDateTimes(dt1, dt2).toDuration(['hours', 'minutes']).toObject() //=> { hours: 24, minutes: 34.82095 }\n   * @example Interval.fromDateTimes(dt1, dt2).toDuration(['hours', 'minutes', 'seconds']).toObject() //=> { hours: 24, minutes: 34, seconds: 49.257 }\n   * @example Interval.fromDateTimes(dt1, dt2).toDuration('seconds').toObject() //=> { seconds: 88489.257 }\n   * @return {Duration}\n   */\n  toDuration(unit, opts) {\n    if (!this.isValid) {\n      return Duration.invalid(this.invalidReason);\n    }\n    return this.e.diff(this.s, unit, opts);\n  }\n\n  /**\n   * Run mapFn on the interval start and end, returning a new Interval from the resulting DateTimes\n   * @param {function} mapFn\n   * @return {Interval}\n   * @example Interval.fromDateTimes(dt1, dt2).mapEndpoints(endpoint => endpoint.toUTC())\n   * @example Interval.fromDateTimes(dt1, dt2).mapEndpoints(endpoint => endpoint.plus({ hours: 2 }))\n   */\n  mapEndpoints(mapFn) {\n    return Interval.fromDateTimes(mapFn(this.s), mapFn(this.e));\n  }\n}", "map": {"version": 3, "names": ["DateTime", "friendlyDateTime", "Duration", "Settings", "InvalidArgumentError", "InvalidIntervalError", "Invalid", "<PERSON><PERSON><PERSON>", "Formats", "INVALID", "validateStartEnd", "start", "end", "<PERSON><PERSON><PERSON><PERSON>", "Interval", "invalid", "toISO", "constructor", "config", "s", "e", "isLuxonInterval", "reason", "explanation", "throwOnInvalid", "fromDateTimes", "builtStart", "builtEnd", "validateError", "after", "duration", "dur", "fromDurationLike", "dt", "plus", "before", "minus", "fromISO", "text", "opts", "split", "startIsValid", "endIsValid", "isInterval", "o", "invalidReason", "invalidExplanation", "length", "unit", "toDuration", "get", "NaN", "count", "startOf", "useLocaleWeeks", "reconfigure", "locale", "Math", "floor", "diff", "valueOf", "<PERSON><PERSON><PERSON>", "isEmpty", "isAfter", "dateTime", "isBefore", "contains", "set", "splitAt", "dateTimes", "sorted", "map", "filter", "d", "sort", "a", "b", "<PERSON><PERSON><PERSON><PERSON>", "results", "i", "added", "next", "push", "splitBy", "as", "idx", "mapUnits", "x", "divideEqually", "numberOfParts", "slice", "overlaps", "other", "abutsStart", "abutsEnd", "engulfs", "equals", "intersection", "union", "merge", "intervals", "found", "final", "reduce", "sofar", "current", "item", "concat", "xor", "currentCount", "ends", "time", "type", "flattened", "Array", "prototype", "arr", "difference", "toString", "Symbol", "for", "toLocaleString", "formatOpts", "DATE_SHORT", "create", "loc", "clone", "formatInterval", "toISODate", "toISOTime", "toFormat", "dateFormat", "separator", "mapEndpoints", "mapFn"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/luxon/src/interval.js"], "sourcesContent": ["import DateTime, { friendlyDateTime } from \"./datetime.js\";\nimport Duration from \"./duration.js\";\nimport Settings from \"./settings.js\";\nimport { InvalidArgumentError, InvalidIntervalError } from \"./errors.js\";\nimport Invalid from \"./impl/invalid.js\";\nimport Formatter from \"./impl/formatter.js\";\nimport * as Formats from \"./impl/formats.js\";\n\nconst INVALID = \"Invalid Interval\";\n\n// checks if the start is equal to or before the end\nfunction validateStartEnd(start, end) {\n  if (!start || !start.isValid) {\n    return Interval.invalid(\"missing or invalid start\");\n  } else if (!end || !end.isValid) {\n    return Interval.invalid(\"missing or invalid end\");\n  } else if (end < start) {\n    return Interval.invalid(\n      \"end before start\",\n      `The end of an interval must be after its start, but you had start=${start.toISO()} and end=${end.toISO()}`\n    );\n  } else {\n    return null;\n  }\n}\n\n/**\n * An Interval object represents a half-open interval of time, where each endpoint is a {@link DateTime}. Conceptually, it's a container for those two endpoints, accompanied by methods for creating, parsing, interrogating, comparing, transforming, and formatting them.\n *\n * Here is a brief overview of the most commonly used methods and getters in Interval:\n *\n * * **Creation** To create an Interval, use {@link Interval.fromDateTimes}, {@link Interval.after}, {@link Interval.before}, or {@link Interval.fromISO}.\n * * **Accessors** Use {@link Interval#start} and {@link Interval#end} to get the start and end.\n * * **Interrogation** To analyze the Interval, use {@link Interval#count}, {@link Interval#length}, {@link Interval#hasSame}, {@link Interval#contains}, {@link Interval#isAfter}, or {@link Interval#isBefore}.\n * * **Transformation** To create other Intervals out of this one, use {@link Interval#set}, {@link Interval#splitAt}, {@link Interval#splitBy}, {@link Interval#divideEqually}, {@link Interval.merge}, {@link Interval.xor}, {@link Interval#union}, {@link Interval#intersection}, or {@link Interval#difference}.\n * * **Comparison** To compare this Interval to another one, use {@link Interval#equals}, {@link Interval#overlaps}, {@link Interval#abutsStart}, {@link Interval#abutsEnd}, {@link Interval#engulfs}\n * * **Output** To convert the Interval into other representations, see {@link Interval#toString}, {@link Interval#toLocaleString}, {@link Interval#toISO}, {@link Interval#toISODate}, {@link Interval#toISOTime}, {@link Interval#toFormat}, and {@link Interval#toDuration}.\n */\nexport default class Interval {\n  /**\n   * @private\n   */\n  constructor(config) {\n    /**\n     * @access private\n     */\n    this.s = config.start;\n    /**\n     * @access private\n     */\n    this.e = config.end;\n    /**\n     * @access private\n     */\n    this.invalid = config.invalid || null;\n    /**\n     * @access private\n     */\n    this.isLuxonInterval = true;\n  }\n\n  /**\n   * Create an invalid Interval.\n   * @param {string} reason - simple string of why this Interval is invalid. Should not contain parameters or anything else data-dependent\n   * @param {string} [explanation=null] - longer explanation, may include parameters and other useful debugging information\n   * @return {Interval}\n   */\n  static invalid(reason, explanation = null) {\n    if (!reason) {\n      throw new InvalidArgumentError(\"need to specify a reason the Interval is invalid\");\n    }\n\n    const invalid = reason instanceof Invalid ? reason : new Invalid(reason, explanation);\n\n    if (Settings.throwOnInvalid) {\n      throw new InvalidIntervalError(invalid);\n    } else {\n      return new Interval({ invalid });\n    }\n  }\n\n  /**\n   * Create an Interval from a start DateTime and an end DateTime. Inclusive of the start but not the end.\n   * @param {DateTime|Date|Object} start\n   * @param {DateTime|Date|Object} end\n   * @return {Interval}\n   */\n  static fromDateTimes(start, end) {\n    const builtStart = friendlyDateTime(start),\n      builtEnd = friendlyDateTime(end);\n\n    const validateError = validateStartEnd(builtStart, builtEnd);\n\n    if (validateError == null) {\n      return new Interval({\n        start: builtStart,\n        end: builtEnd,\n      });\n    } else {\n      return validateError;\n    }\n  }\n\n  /**\n   * Create an Interval from a start DateTime and a Duration to extend to.\n   * @param {DateTime|Date|Object} start\n   * @param {Duration|Object|number} duration - the length of the Interval.\n   * @return {Interval}\n   */\n  static after(start, duration) {\n    const dur = Duration.fromDurationLike(duration),\n      dt = friendlyDateTime(start);\n    return Interval.fromDateTimes(dt, dt.plus(dur));\n  }\n\n  /**\n   * Create an Interval from an end DateTime and a Duration to extend backwards to.\n   * @param {DateTime|Date|Object} end\n   * @param {Duration|Object|number} duration - the length of the Interval.\n   * @return {Interval}\n   */\n  static before(end, duration) {\n    const dur = Duration.fromDurationLike(duration),\n      dt = friendlyDateTime(end);\n    return Interval.fromDateTimes(dt.minus(dur), dt);\n  }\n\n  /**\n   * Create an Interval from an ISO 8601 string.\n   * Accepts `<start>/<end>`, `<start>/<duration>`, and `<duration>/<end>` formats.\n   * @param {string} text - the ISO string to parse\n   * @param {Object} [opts] - options to pass {@link DateTime#fromISO} and optionally {@link Duration#fromISO}\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Time_intervals\n   * @return {Interval}\n   */\n  static fromISO(text, opts) {\n    const [s, e] = (text || \"\").split(\"/\", 2);\n    if (s && e) {\n      let start, startIsValid;\n      try {\n        start = DateTime.fromISO(s, opts);\n        startIsValid = start.isValid;\n      } catch (e) {\n        startIsValid = false;\n      }\n\n      let end, endIsValid;\n      try {\n        end = DateTime.fromISO(e, opts);\n        endIsValid = end.isValid;\n      } catch (e) {\n        endIsValid = false;\n      }\n\n      if (startIsValid && endIsValid) {\n        return Interval.fromDateTimes(start, end);\n      }\n\n      if (startIsValid) {\n        const dur = Duration.fromISO(e, opts);\n        if (dur.isValid) {\n          return Interval.after(start, dur);\n        }\n      } else if (endIsValid) {\n        const dur = Duration.fromISO(s, opts);\n        if (dur.isValid) {\n          return Interval.before(end, dur);\n        }\n      }\n    }\n    return Interval.invalid(\"unparsable\", `the input \"${text}\" can't be parsed as ISO 8601`);\n  }\n\n  /**\n   * Check if an object is an Interval. Works across context boundaries\n   * @param {object} o\n   * @return {boolean}\n   */\n  static isInterval(o) {\n    return (o && o.isLuxonInterval) || false;\n  }\n\n  /**\n   * Returns the start of the Interval\n   * @type {DateTime}\n   */\n  get start() {\n    return this.isValid ? this.s : null;\n  }\n\n  /**\n   * Returns the end of the Interval\n   * @type {DateTime}\n   */\n  get end() {\n    return this.isValid ? this.e : null;\n  }\n\n  /**\n   * Returns whether this Interval's end is at least its start, meaning that the Interval isn't 'backwards'.\n   * @type {boolean}\n   */\n  get isValid() {\n    return this.invalidReason === null;\n  }\n\n  /**\n   * Returns an error code if this Interval is invalid, or null if the Interval is valid\n   * @type {string}\n   */\n  get invalidReason() {\n    return this.invalid ? this.invalid.reason : null;\n  }\n\n  /**\n   * Returns an explanation of why this Interval became invalid, or null if the Interval is valid\n   * @type {string}\n   */\n  get invalidExplanation() {\n    return this.invalid ? this.invalid.explanation : null;\n  }\n\n  /**\n   * Returns the length of the Interval in the specified unit.\n   * @param {string} unit - the unit (such as 'hours' or 'days') to return the length in.\n   * @return {number}\n   */\n  length(unit = \"milliseconds\") {\n    return this.isValid ? this.toDuration(...[unit]).get(unit) : NaN;\n  }\n\n  /**\n   * Returns the count of minutes, hours, days, months, or years included in the Interval, even in part.\n   * Unlike {@link Interval#length} this counts sections of the calendar, not periods of time, e.g. specifying 'day'\n   * asks 'what dates are included in this interval?', not 'how many days long is this interval?'\n   * @param {string} [unit='milliseconds'] - the unit of time to count.\n   * @param {Object} opts - options\n   * @param {boolean} [opts.useLocaleWeeks=false] - If true, use weeks based on the locale, i.e. use the locale-dependent start of the week; this operation will always use the locale of the start DateTime\n   * @return {number}\n   */\n  count(unit = \"milliseconds\", opts) {\n    if (!this.isValid) return NaN;\n    const start = this.start.startOf(unit, opts);\n    let end;\n    if (opts?.useLocaleWeeks) {\n      end = this.end.reconfigure({ locale: start.locale });\n    } else {\n      end = this.end;\n    }\n    end = end.startOf(unit, opts);\n    return Math.floor(end.diff(start, unit).get(unit)) + (end.valueOf() !== this.end.valueOf());\n  }\n\n  /**\n   * Returns whether this Interval's start and end are both in the same unit of time\n   * @param {string} unit - the unit of time to check sameness on\n   * @return {boolean}\n   */\n  hasSame(unit) {\n    return this.isValid ? this.isEmpty() || this.e.minus(1).hasSame(this.s, unit) : false;\n  }\n\n  /**\n   * Return whether this Interval has the same start and end DateTimes.\n   * @return {boolean}\n   */\n  isEmpty() {\n    return this.s.valueOf() === this.e.valueOf();\n  }\n\n  /**\n   * Return whether this Interval's start is after the specified DateTime.\n   * @param {DateTime} dateTime\n   * @return {boolean}\n   */\n  isAfter(dateTime) {\n    if (!this.isValid) return false;\n    return this.s > dateTime;\n  }\n\n  /**\n   * Return whether this Interval's end is before the specified DateTime.\n   * @param {DateTime} dateTime\n   * @return {boolean}\n   */\n  isBefore(dateTime) {\n    if (!this.isValid) return false;\n    return this.e <= dateTime;\n  }\n\n  /**\n   * Return whether this Interval contains the specified DateTime.\n   * @param {DateTime} dateTime\n   * @return {boolean}\n   */\n  contains(dateTime) {\n    if (!this.isValid) return false;\n    return this.s <= dateTime && this.e > dateTime;\n  }\n\n  /**\n   * \"Sets\" the start and/or end dates. Returns a newly-constructed Interval.\n   * @param {Object} values - the values to set\n   * @param {DateTime} values.start - the starting DateTime\n   * @param {DateTime} values.end - the ending DateTime\n   * @return {Interval}\n   */\n  set({ start, end } = {}) {\n    if (!this.isValid) return this;\n    return Interval.fromDateTimes(start || this.s, end || this.e);\n  }\n\n  /**\n   * Split this Interval at each of the specified DateTimes\n   * @param {...DateTime} dateTimes - the unit of time to count.\n   * @return {Array}\n   */\n  splitAt(...dateTimes) {\n    if (!this.isValid) return [];\n    const sorted = dateTimes\n        .map(friendlyDateTime)\n        .filter((d) => this.contains(d))\n        .sort((a, b) => a.toMillis() - b.toMillis()),\n      results = [];\n    let { s } = this,\n      i = 0;\n\n    while (s < this.e) {\n      const added = sorted[i] || this.e,\n        next = +added > +this.e ? this.e : added;\n      results.push(Interval.fromDateTimes(s, next));\n      s = next;\n      i += 1;\n    }\n\n    return results;\n  }\n\n  /**\n   * Split this Interval into smaller Intervals, each of the specified length.\n   * Left over time is grouped into a smaller interval\n   * @param {Duration|Object|number} duration - The length of each resulting interval.\n   * @return {Array}\n   */\n  splitBy(duration) {\n    const dur = Duration.fromDurationLike(duration);\n\n    if (!this.isValid || !dur.isValid || dur.as(\"milliseconds\") === 0) {\n      return [];\n    }\n\n    let { s } = this,\n      idx = 1,\n      next;\n\n    const results = [];\n    while (s < this.e) {\n      const added = this.start.plus(dur.mapUnits((x) => x * idx));\n      next = +added > +this.e ? this.e : added;\n      results.push(Interval.fromDateTimes(s, next));\n      s = next;\n      idx += 1;\n    }\n\n    return results;\n  }\n\n  /**\n   * Split this Interval into the specified number of smaller intervals.\n   * @param {number} numberOfParts - The number of Intervals to divide the Interval into.\n   * @return {Array}\n   */\n  divideEqually(numberOfParts) {\n    if (!this.isValid) return [];\n    return this.splitBy(this.length() / numberOfParts).slice(0, numberOfParts);\n  }\n\n  /**\n   * Return whether this Interval overlaps with the specified Interval\n   * @param {Interval} other\n   * @return {boolean}\n   */\n  overlaps(other) {\n    return this.e > other.s && this.s < other.e;\n  }\n\n  /**\n   * Return whether this Interval's end is adjacent to the specified Interval's start.\n   * @param {Interval} other\n   * @return {boolean}\n   */\n  abutsStart(other) {\n    if (!this.isValid) return false;\n    return +this.e === +other.s;\n  }\n\n  /**\n   * Return whether this Interval's start is adjacent to the specified Interval's end.\n   * @param {Interval} other\n   * @return {boolean}\n   */\n  abutsEnd(other) {\n    if (!this.isValid) return false;\n    return +other.e === +this.s;\n  }\n\n  /**\n   * Returns true if this Interval fully contains the specified Interval, specifically if the intersect (of this Interval and the other Interval) is equal to the other Interval; false otherwise.\n   * @param {Interval} other\n   * @return {boolean}\n   */\n  engulfs(other) {\n    if (!this.isValid) return false;\n    return this.s <= other.s && this.e >= other.e;\n  }\n\n  /**\n   * Return whether this Interval has the same start and end as the specified Interval.\n   * @param {Interval} other\n   * @return {boolean}\n   */\n  equals(other) {\n    if (!this.isValid || !other.isValid) {\n      return false;\n    }\n\n    return this.s.equals(other.s) && this.e.equals(other.e);\n  }\n\n  /**\n   * Return an Interval representing the intersection of this Interval and the specified Interval.\n   * Specifically, the resulting Interval has the maximum start time and the minimum end time of the two Intervals.\n   * Returns null if the intersection is empty, meaning, the intervals don't intersect.\n   * @param {Interval} other\n   * @return {Interval}\n   */\n  intersection(other) {\n    if (!this.isValid) return this;\n    const s = this.s > other.s ? this.s : other.s,\n      e = this.e < other.e ? this.e : other.e;\n\n    if (s >= e) {\n      return null;\n    } else {\n      return Interval.fromDateTimes(s, e);\n    }\n  }\n\n  /**\n   * Return an Interval representing the union of this Interval and the specified Interval.\n   * Specifically, the resulting Interval has the minimum start time and the maximum end time of the two Intervals.\n   * @param {Interval} other\n   * @return {Interval}\n   */\n  union(other) {\n    if (!this.isValid) return this;\n    const s = this.s < other.s ? this.s : other.s,\n      e = this.e > other.e ? this.e : other.e;\n    return Interval.fromDateTimes(s, e);\n  }\n\n  /**\n   * Merge an array of Intervals into a equivalent minimal set of Intervals.\n   * Combines overlapping and adjacent Intervals.\n   * @param {Array} intervals\n   * @return {Array}\n   */\n  static merge(intervals) {\n    const [found, final] = intervals\n      .sort((a, b) => a.s - b.s)\n      .reduce(\n        ([sofar, current], item) => {\n          if (!current) {\n            return [sofar, item];\n          } else if (current.overlaps(item) || current.abutsStart(item)) {\n            return [sofar, current.union(item)];\n          } else {\n            return [sofar.concat([current]), item];\n          }\n        },\n        [[], null]\n      );\n    if (final) {\n      found.push(final);\n    }\n    return found;\n  }\n\n  /**\n   * Return an array of Intervals representing the spans of time that only appear in one of the specified Intervals.\n   * @param {Array} intervals\n   * @return {Array}\n   */\n  static xor(intervals) {\n    let start = null,\n      currentCount = 0;\n    const results = [],\n      ends = intervals.map((i) => [\n        { time: i.s, type: \"s\" },\n        { time: i.e, type: \"e\" },\n      ]),\n      flattened = Array.prototype.concat(...ends),\n      arr = flattened.sort((a, b) => a.time - b.time);\n\n    for (const i of arr) {\n      currentCount += i.type === \"s\" ? 1 : -1;\n\n      if (currentCount === 1) {\n        start = i.time;\n      } else {\n        if (start && +start !== +i.time) {\n          results.push(Interval.fromDateTimes(start, i.time));\n        }\n\n        start = null;\n      }\n    }\n\n    return Interval.merge(results);\n  }\n\n  /**\n   * Return an Interval representing the span of time in this Interval that doesn't overlap with any of the specified Intervals.\n   * @param {...Interval} intervals\n   * @return {Array}\n   */\n  difference(...intervals) {\n    return Interval.xor([this].concat(intervals))\n      .map((i) => this.intersection(i))\n      .filter((i) => i && !i.isEmpty());\n  }\n\n  /**\n   * Returns a string representation of this Interval appropriate for debugging.\n   * @return {string}\n   */\n  toString() {\n    if (!this.isValid) return INVALID;\n    return `[${this.s.toISO()} – ${this.e.toISO()})`;\n  }\n\n  /**\n   * Returns a string representation of this Interval appropriate for the REPL.\n   * @return {string}\n   */\n  [Symbol.for(\"nodejs.util.inspect.custom\")]() {\n    if (this.isValid) {\n      return `Interval { start: ${this.s.toISO()}, end: ${this.e.toISO()} }`;\n    } else {\n      return `Interval { Invalid, reason: ${this.invalidReason} }`;\n    }\n  }\n\n  /**\n   * Returns a localized string representing this Interval. Accepts the same options as the\n   * Intl.DateTimeFormat constructor and any presets defined by Luxon, such as\n   * {@link DateTime.DATE_FULL} or {@link DateTime.TIME_SIMPLE}. The exact behavior of this method\n   * is browser-specific, but in general it will return an appropriate representation of the\n   * Interval in the assigned locale. Defaults to the system's locale if no locale has been\n   * specified.\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/DateTimeFormat\n   * @param {Object} [formatOpts=DateTime.DATE_SHORT] - Either a DateTime preset or\n   * Intl.DateTimeFormat constructor options.\n   * @param {Object} opts - Options to override the configuration of the start DateTime.\n   * @example Interval.fromISO('2022-11-07T09:00Z/2022-11-08T09:00Z').toLocaleString(); //=> 11/7/2022 – 11/8/2022\n   * @example Interval.fromISO('2022-11-07T09:00Z/2022-11-08T09:00Z').toLocaleString(DateTime.DATE_FULL); //=> November 7 – 8, 2022\n   * @example Interval.fromISO('2022-11-07T09:00Z/2022-11-08T09:00Z').toLocaleString(DateTime.DATE_FULL, { locale: 'fr-FR' }); //=> 7–8 novembre 2022\n   * @example Interval.fromISO('2022-11-07T17:00Z/2022-11-07T19:00Z').toLocaleString(DateTime.TIME_SIMPLE); //=> 6:00 – 8:00 PM\n   * @example Interval.fromISO('2022-11-07T17:00Z/2022-11-07T19:00Z').toLocaleString({ weekday: 'short', month: 'short', day: '2-digit', hour: '2-digit', minute: '2-digit' }); //=> Mon, Nov 07, 6:00 – 8:00 p\n   * @return {string}\n   */\n  toLocaleString(formatOpts = Formats.DATE_SHORT, opts = {}) {\n    return this.isValid\n      ? Formatter.create(this.s.loc.clone(opts), formatOpts).formatInterval(this)\n      : INVALID;\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this Interval.\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Time_intervals\n   * @param {Object} opts - The same options as {@link DateTime#toISO}\n   * @return {string}\n   */\n  toISO(opts) {\n    if (!this.isValid) return INVALID;\n    return `${this.s.toISO(opts)}/${this.e.toISO(opts)}`;\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of date of this Interval.\n   * The time components are ignored.\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Time_intervals\n   * @return {string}\n   */\n  toISODate() {\n    if (!this.isValid) return INVALID;\n    return `${this.s.toISODate()}/${this.e.toISODate()}`;\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of time of this Interval.\n   * The date components are ignored.\n   * @see https://en.wikipedia.org/wiki/ISO_8601#Time_intervals\n   * @param {Object} opts - The same options as {@link DateTime#toISO}\n   * @return {string}\n   */\n  toISOTime(opts) {\n    if (!this.isValid) return INVALID;\n    return `${this.s.toISOTime(opts)}/${this.e.toISOTime(opts)}`;\n  }\n\n  /**\n   * Returns a string representation of this Interval formatted according to the specified format\n   * string. **You may not want this.** See {@link Interval#toLocaleString} for a more flexible\n   * formatting tool.\n   * @param {string} dateFormat - The format string. This string formats the start and end time.\n   * See {@link DateTime#toFormat} for details.\n   * @param {Object} opts - Options.\n   * @param {string} [opts.separator =  ' – '] - A separator to place between the start and end\n   * representations.\n   * @return {string}\n   */\n  toFormat(dateFormat, { separator = \" – \" } = {}) {\n    if (!this.isValid) return INVALID;\n    return `${this.s.toFormat(dateFormat)}${separator}${this.e.toFormat(dateFormat)}`;\n  }\n\n  /**\n   * Return a Duration representing the time spanned by this interval.\n   * @param {string|string[]} [unit=['milliseconds']] - the unit or units (such as 'hours' or 'days') to include in the duration.\n   * @param {Object} opts - options that affect the creation of the Duration\n   * @param {string} [opts.conversionAccuracy='casual'] - the conversion system to use\n   * @example Interval.fromDateTimes(dt1, dt2).toDuration().toObject() //=> { milliseconds: 88489257 }\n   * @example Interval.fromDateTimes(dt1, dt2).toDuration('days').toObject() //=> { days: 1.0241812152777778 }\n   * @example Interval.fromDateTimes(dt1, dt2).toDuration(['hours', 'minutes']).toObject() //=> { hours: 24, minutes: 34.82095 }\n   * @example Interval.fromDateTimes(dt1, dt2).toDuration(['hours', 'minutes', 'seconds']).toObject() //=> { hours: 24, minutes: 34, seconds: 49.257 }\n   * @example Interval.fromDateTimes(dt1, dt2).toDuration('seconds').toObject() //=> { seconds: 88489.257 }\n   * @return {Duration}\n   */\n  toDuration(unit, opts) {\n    if (!this.isValid) {\n      return Duration.invalid(this.invalidReason);\n    }\n    return this.e.diff(this.s, unit, opts);\n  }\n\n  /**\n   * Run mapFn on the interval start and end, returning a new Interval from the resulting DateTimes\n   * @param {function} mapFn\n   * @return {Interval}\n   * @example Interval.fromDateTimes(dt1, dt2).mapEndpoints(endpoint => endpoint.toUTC())\n   * @example Interval.fromDateTimes(dt1, dt2).mapEndpoints(endpoint => endpoint.plus({ hours: 2 }))\n   */\n  mapEndpoints(mapFn) {\n    return Interval.fromDateTimes(mapFn(this.s), mapFn(this.e));\n  }\n}\n"], "mappings": "AAAA,OAAOA,QAAQ,IAAIC,gBAAgB,QAAQ,eAAe;AAC1D,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,QAAQ,MAAM,eAAe;AACpC,SAASC,oBAAoB,EAAEC,oBAAoB,QAAQ,aAAa;AACxE,OAAOC,OAAO,MAAM,mBAAmB;AACvC,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,OAAO,KAAKC,OAAO,MAAM,mBAAmB;AAE5C,MAAMC,OAAO,GAAG,kBAAkB;;AAElC;AACA,SAASC,gBAAgBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACpC,IAAI,CAACD,KAAK,IAAI,CAACA,KAAK,CAACE,OAAO,EAAE;IAC5B,OAAOC,QAAQ,CAACC,OAAO,CAAC,0BAA0B,CAAC;EACrD,CAAC,MAAM,IAAI,CAACH,GAAG,IAAI,CAACA,GAAG,CAACC,OAAO,EAAE;IAC/B,OAAOC,QAAQ,CAACC,OAAO,CAAC,wBAAwB,CAAC;EACnD,CAAC,MAAM,IAAIH,GAAG,GAAGD,KAAK,EAAE;IACtB,OAAOG,QAAQ,CAACC,OAAO,CACrB,kBAAkB,EACjB,qEAAoEJ,KAAK,CAACK,KAAK,CAAC,CAAE,YAAWJ,GAAG,CAACI,KAAK,CAAC,CAAE,EAC5G,CAAC;EACH,CAAC,MAAM;IACL,OAAO,IAAI;EACb;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,MAAMF,QAAQ,CAAC;EAC5B;AACF;AACA;EACEG,WAAWA,CAACC,MAAM,EAAE;IAClB;AACJ;AACA;IACI,IAAI,CAACC,CAAC,GAAGD,MAAM,CAACP,KAAK;IACrB;AACJ;AACA;IACI,IAAI,CAACS,CAAC,GAAGF,MAAM,CAACN,GAAG;IACnB;AACJ;AACA;IACI,IAAI,CAACG,OAAO,GAAGG,MAAM,CAACH,OAAO,IAAI,IAAI;IACrC;AACJ;AACA;IACI,IAAI,CAACM,eAAe,GAAG,IAAI;EAC7B;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,OAAON,OAAOA,CAACO,MAAM,EAAEC,WAAW,GAAG,IAAI,EAAE;IACzC,IAAI,CAACD,MAAM,EAAE;MACX,MAAM,IAAIlB,oBAAoB,CAAC,kDAAkD,CAAC;IACpF;IAEA,MAAMW,OAAO,GAAGO,MAAM,YAAYhB,OAAO,GAAGgB,MAAM,GAAG,IAAIhB,OAAO,CAACgB,MAAM,EAAEC,WAAW,CAAC;IAErF,IAAIpB,QAAQ,CAACqB,cAAc,EAAE;MAC3B,MAAM,IAAInB,oBAAoB,CAACU,OAAO,CAAC;IACzC,CAAC,MAAM;MACL,OAAO,IAAID,QAAQ,CAAC;QAAEC;MAAQ,CAAC,CAAC;IAClC;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,OAAOU,aAAaA,CAACd,KAAK,EAAEC,GAAG,EAAE;IAC/B,MAAMc,UAAU,GAAGzB,gBAAgB,CAACU,KAAK,CAAC;MACxCgB,QAAQ,GAAG1B,gBAAgB,CAACW,GAAG,CAAC;IAElC,MAAMgB,aAAa,GAAGlB,gBAAgB,CAACgB,UAAU,EAAEC,QAAQ,CAAC;IAE5D,IAAIC,aAAa,IAAI,IAAI,EAAE;MACzB,OAAO,IAAId,QAAQ,CAAC;QAClBH,KAAK,EAAEe,UAAU;QACjBd,GAAG,EAAEe;MACP,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,OAAOC,aAAa;IACtB;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,OAAOC,KAAKA,CAAClB,KAAK,EAAEmB,QAAQ,EAAE;IAC5B,MAAMC,GAAG,GAAG7B,QAAQ,CAAC8B,gBAAgB,CAACF,QAAQ,CAAC;MAC7CG,EAAE,GAAGhC,gBAAgB,CAACU,KAAK,CAAC;IAC9B,OAAOG,QAAQ,CAACW,aAAa,CAACQ,EAAE,EAAEA,EAAE,CAACC,IAAI,CAACH,GAAG,CAAC,CAAC;EACjD;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,OAAOI,MAAMA,CAACvB,GAAG,EAAEkB,QAAQ,EAAE;IAC3B,MAAMC,GAAG,GAAG7B,QAAQ,CAAC8B,gBAAgB,CAACF,QAAQ,CAAC;MAC7CG,EAAE,GAAGhC,gBAAgB,CAACW,GAAG,CAAC;IAC5B,OAAOE,QAAQ,CAACW,aAAa,CAACQ,EAAE,CAACG,KAAK,CAACL,GAAG,CAAC,EAAEE,EAAE,CAAC;EAClD;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOI,OAAOA,CAACC,IAAI,EAAEC,IAAI,EAAE;IACzB,MAAM,CAACpB,CAAC,EAAEC,CAAC,CAAC,GAAG,CAACkB,IAAI,IAAI,EAAE,EAAEE,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;IACzC,IAAIrB,CAAC,IAAIC,CAAC,EAAE;MACV,IAAIT,KAAK,EAAE8B,YAAY;MACvB,IAAI;QACF9B,KAAK,GAAGX,QAAQ,CAACqC,OAAO,CAAClB,CAAC,EAAEoB,IAAI,CAAC;QACjCE,YAAY,GAAG9B,KAAK,CAACE,OAAO;MAC9B,CAAC,CAAC,OAAOO,CAAC,EAAE;QACVqB,YAAY,GAAG,KAAK;MACtB;MAEA,IAAI7B,GAAG,EAAE8B,UAAU;MACnB,IAAI;QACF9B,GAAG,GAAGZ,QAAQ,CAACqC,OAAO,CAACjB,CAAC,EAAEmB,IAAI,CAAC;QAC/BG,UAAU,GAAG9B,GAAG,CAACC,OAAO;MAC1B,CAAC,CAAC,OAAOO,CAAC,EAAE;QACVsB,UAAU,GAAG,KAAK;MACpB;MAEA,IAAID,YAAY,IAAIC,UAAU,EAAE;QAC9B,OAAO5B,QAAQ,CAACW,aAAa,CAACd,KAAK,EAAEC,GAAG,CAAC;MAC3C;MAEA,IAAI6B,YAAY,EAAE;QAChB,MAAMV,GAAG,GAAG7B,QAAQ,CAACmC,OAAO,CAACjB,CAAC,EAAEmB,IAAI,CAAC;QACrC,IAAIR,GAAG,CAAClB,OAAO,EAAE;UACf,OAAOC,QAAQ,CAACe,KAAK,CAAClB,KAAK,EAAEoB,GAAG,CAAC;QACnC;MACF,CAAC,MAAM,IAAIW,UAAU,EAAE;QACrB,MAAMX,GAAG,GAAG7B,QAAQ,CAACmC,OAAO,CAAClB,CAAC,EAAEoB,IAAI,CAAC;QACrC,IAAIR,GAAG,CAAClB,OAAO,EAAE;UACf,OAAOC,QAAQ,CAACqB,MAAM,CAACvB,GAAG,EAAEmB,GAAG,CAAC;QAClC;MACF;IACF;IACA,OAAOjB,QAAQ,CAACC,OAAO,CAAC,YAAY,EAAG,cAAauB,IAAK,+BAA8B,CAAC;EAC1F;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAOK,UAAUA,CAACC,CAAC,EAAE;IACnB,OAAQA,CAAC,IAAIA,CAAC,CAACvB,eAAe,IAAK,KAAK;EAC1C;;EAEA;AACF;AACA;AACA;EACE,IAAIV,KAAKA,CAAA,EAAG;IACV,OAAO,IAAI,CAACE,OAAO,GAAG,IAAI,CAACM,CAAC,GAAG,IAAI;EACrC;;EAEA;AACF;AACA;AACA;EACE,IAAIP,GAAGA,CAAA,EAAG;IACR,OAAO,IAAI,CAACC,OAAO,GAAG,IAAI,CAACO,CAAC,GAAG,IAAI;EACrC;;EAEA;AACF;AACA;AACA;EACE,IAAIP,OAAOA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACgC,aAAa,KAAK,IAAI;EACpC;;EAEA;AACF;AACA;AACA;EACE,IAAIA,aAAaA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAC9B,OAAO,GAAG,IAAI,CAACA,OAAO,CAACO,MAAM,GAAG,IAAI;EAClD;;EAEA;AACF;AACA;AACA;EACE,IAAIwB,kBAAkBA,CAAA,EAAG;IACvB,OAAO,IAAI,CAAC/B,OAAO,GAAG,IAAI,CAACA,OAAO,CAACQ,WAAW,GAAG,IAAI;EACvD;;EAEA;AACF;AACA;AACA;AACA;EACEwB,MAAMA,CAACC,IAAI,GAAG,cAAc,EAAE;IAC5B,OAAO,IAAI,CAACnC,OAAO,GAAG,IAAI,CAACoC,UAAU,CAAC,GAAG,CAACD,IAAI,CAAC,CAAC,CAACE,GAAG,CAACF,IAAI,CAAC,GAAGG,GAAG;EAClE;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,KAAKA,CAACJ,IAAI,GAAG,cAAc,EAAET,IAAI,EAAE;IACjC,IAAI,CAAC,IAAI,CAAC1B,OAAO,EAAE,OAAOsC,GAAG;IAC7B,MAAMxC,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC0C,OAAO,CAACL,IAAI,EAAET,IAAI,CAAC;IAC5C,IAAI3B,GAAG;IACP,IAAI2B,IAAI,EAAEe,cAAc,EAAE;MACxB1C,GAAG,GAAG,IAAI,CAACA,GAAG,CAAC2C,WAAW,CAAC;QAAEC,MAAM,EAAE7C,KAAK,CAAC6C;MAAO,CAAC,CAAC;IACtD,CAAC,MAAM;MACL5C,GAAG,GAAG,IAAI,CAACA,GAAG;IAChB;IACAA,GAAG,GAAGA,GAAG,CAACyC,OAAO,CAACL,IAAI,EAAET,IAAI,CAAC;IAC7B,OAAOkB,IAAI,CAACC,KAAK,CAAC9C,GAAG,CAAC+C,IAAI,CAAChD,KAAK,EAAEqC,IAAI,CAAC,CAACE,GAAG,CAACF,IAAI,CAAC,CAAC,IAAIpC,GAAG,CAACgD,OAAO,CAAC,CAAC,KAAK,IAAI,CAAChD,GAAG,CAACgD,OAAO,CAAC,CAAC,CAAC;EAC7F;;EAEA;AACF;AACA;AACA;AACA;EACEC,OAAOA,CAACb,IAAI,EAAE;IACZ,OAAO,IAAI,CAACnC,OAAO,GAAG,IAAI,CAACiD,OAAO,CAAC,CAAC,IAAI,IAAI,CAAC1C,CAAC,CAACgB,KAAK,CAAC,CAAC,CAAC,CAACyB,OAAO,CAAC,IAAI,CAAC1C,CAAC,EAAE6B,IAAI,CAAC,GAAG,KAAK;EACvF;;EAEA;AACF;AACA;AACA;EACEc,OAAOA,CAAA,EAAG;IACR,OAAO,IAAI,CAAC3C,CAAC,CAACyC,OAAO,CAAC,CAAC,KAAK,IAAI,CAACxC,CAAC,CAACwC,OAAO,CAAC,CAAC;EAC9C;;EAEA;AACF;AACA;AACA;AACA;EACEG,OAAOA,CAACC,QAAQ,EAAE;IAChB,IAAI,CAAC,IAAI,CAACnD,OAAO,EAAE,OAAO,KAAK;IAC/B,OAAO,IAAI,CAACM,CAAC,GAAG6C,QAAQ;EAC1B;;EAEA;AACF;AACA;AACA;AACA;EACEC,QAAQA,CAACD,QAAQ,EAAE;IACjB,IAAI,CAAC,IAAI,CAACnD,OAAO,EAAE,OAAO,KAAK;IAC/B,OAAO,IAAI,CAACO,CAAC,IAAI4C,QAAQ;EAC3B;;EAEA;AACF;AACA;AACA;AACA;EACEE,QAAQA,CAACF,QAAQ,EAAE;IACjB,IAAI,CAAC,IAAI,CAACnD,OAAO,EAAE,OAAO,KAAK;IAC/B,OAAO,IAAI,CAACM,CAAC,IAAI6C,QAAQ,IAAI,IAAI,CAAC5C,CAAC,GAAG4C,QAAQ;EAChD;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACEG,GAAGA,CAAC;IAAExD,KAAK;IAAEC;EAAI,CAAC,GAAG,CAAC,CAAC,EAAE;IACvB,IAAI,CAAC,IAAI,CAACC,OAAO,EAAE,OAAO,IAAI;IAC9B,OAAOC,QAAQ,CAACW,aAAa,CAACd,KAAK,IAAI,IAAI,CAACQ,CAAC,EAAEP,GAAG,IAAI,IAAI,CAACQ,CAAC,CAAC;EAC/D;;EAEA;AACF;AACA;AACA;AACA;EACEgD,OAAOA,CAAC,GAAGC,SAAS,EAAE;IACpB,IAAI,CAAC,IAAI,CAACxD,OAAO,EAAE,OAAO,EAAE;IAC5B,MAAMyD,MAAM,GAAGD,SAAS,CACnBE,GAAG,CAACtE,gBAAgB,CAAC,CACrBuE,MAAM,CAAEC,CAAC,IAAK,IAAI,CAACP,QAAQ,CAACO,CAAC,CAAC,CAAC,CAC/BC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACE,QAAQ,CAAC,CAAC,GAAGD,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAC;MAC9CC,OAAO,GAAG,EAAE;IACd,IAAI;QAAE3D;MAAE,CAAC,GAAG,IAAI;MACd4D,CAAC,GAAG,CAAC;IAEP,OAAO5D,CAAC,GAAG,IAAI,CAACC,CAAC,EAAE;MACjB,MAAM4D,KAAK,GAAGV,MAAM,CAACS,CAAC,CAAC,IAAI,IAAI,CAAC3D,CAAC;QAC/B6D,IAAI,GAAG,CAACD,KAAK,GAAG,CAAC,IAAI,CAAC5D,CAAC,GAAG,IAAI,CAACA,CAAC,GAAG4D,KAAK;MAC1CF,OAAO,CAACI,IAAI,CAACpE,QAAQ,CAACW,aAAa,CAACN,CAAC,EAAE8D,IAAI,CAAC,CAAC;MAC7C9D,CAAC,GAAG8D,IAAI;MACRF,CAAC,IAAI,CAAC;IACR;IAEA,OAAOD,OAAO;EAChB;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEK,OAAOA,CAACrD,QAAQ,EAAE;IAChB,MAAMC,GAAG,GAAG7B,QAAQ,CAAC8B,gBAAgB,CAACF,QAAQ,CAAC;IAE/C,IAAI,CAAC,IAAI,CAACjB,OAAO,IAAI,CAACkB,GAAG,CAAClB,OAAO,IAAIkB,GAAG,CAACqD,EAAE,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;MACjE,OAAO,EAAE;IACX;IAEA,IAAI;QAAEjE;MAAE,CAAC,GAAG,IAAI;MACdkE,GAAG,GAAG,CAAC;MACPJ,IAAI;IAEN,MAAMH,OAAO,GAAG,EAAE;IAClB,OAAO3D,CAAC,GAAG,IAAI,CAACC,CAAC,EAAE;MACjB,MAAM4D,KAAK,GAAG,IAAI,CAACrE,KAAK,CAACuB,IAAI,CAACH,GAAG,CAACuD,QAAQ,CAAEC,CAAC,IAAKA,CAAC,GAAGF,GAAG,CAAC,CAAC;MAC3DJ,IAAI,GAAG,CAACD,KAAK,GAAG,CAAC,IAAI,CAAC5D,CAAC,GAAG,IAAI,CAACA,CAAC,GAAG4D,KAAK;MACxCF,OAAO,CAACI,IAAI,CAACpE,QAAQ,CAACW,aAAa,CAACN,CAAC,EAAE8D,IAAI,CAAC,CAAC;MAC7C9D,CAAC,GAAG8D,IAAI;MACRI,GAAG,IAAI,CAAC;IACV;IAEA,OAAOP,OAAO;EAChB;;EAEA;AACF;AACA;AACA;AACA;EACEU,aAAaA,CAACC,aAAa,EAAE;IAC3B,IAAI,CAAC,IAAI,CAAC5E,OAAO,EAAE,OAAO,EAAE;IAC5B,OAAO,IAAI,CAACsE,OAAO,CAAC,IAAI,CAACpC,MAAM,CAAC,CAAC,GAAG0C,aAAa,CAAC,CAACC,KAAK,CAAC,CAAC,EAAED,aAAa,CAAC;EAC5E;;EAEA;AACF;AACA;AACA;AACA;EACEE,QAAQA,CAACC,KAAK,EAAE;IACd,OAAO,IAAI,CAACxE,CAAC,GAAGwE,KAAK,CAACzE,CAAC,IAAI,IAAI,CAACA,CAAC,GAAGyE,KAAK,CAACxE,CAAC;EAC7C;;EAEA;AACF;AACA;AACA;AACA;EACEyE,UAAUA,CAACD,KAAK,EAAE;IAChB,IAAI,CAAC,IAAI,CAAC/E,OAAO,EAAE,OAAO,KAAK;IAC/B,OAAO,CAAC,IAAI,CAACO,CAAC,KAAK,CAACwE,KAAK,CAACzE,CAAC;EAC7B;;EAEA;AACF;AACA;AACA;AACA;EACE2E,QAAQA,CAACF,KAAK,EAAE;IACd,IAAI,CAAC,IAAI,CAAC/E,OAAO,EAAE,OAAO,KAAK;IAC/B,OAAO,CAAC+E,KAAK,CAACxE,CAAC,KAAK,CAAC,IAAI,CAACD,CAAC;EAC7B;;EAEA;AACF;AACA;AACA;AACA;EACE4E,OAAOA,CAACH,KAAK,EAAE;IACb,IAAI,CAAC,IAAI,CAAC/E,OAAO,EAAE,OAAO,KAAK;IAC/B,OAAO,IAAI,CAACM,CAAC,IAAIyE,KAAK,CAACzE,CAAC,IAAI,IAAI,CAACC,CAAC,IAAIwE,KAAK,CAACxE,CAAC;EAC/C;;EAEA;AACF;AACA;AACA;AACA;EACE4E,MAAMA,CAACJ,KAAK,EAAE;IACZ,IAAI,CAAC,IAAI,CAAC/E,OAAO,IAAI,CAAC+E,KAAK,CAAC/E,OAAO,EAAE;MACnC,OAAO,KAAK;IACd;IAEA,OAAO,IAAI,CAACM,CAAC,CAAC6E,MAAM,CAACJ,KAAK,CAACzE,CAAC,CAAC,IAAI,IAAI,CAACC,CAAC,CAAC4E,MAAM,CAACJ,KAAK,CAACxE,CAAC,CAAC;EACzD;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE6E,YAAYA,CAACL,KAAK,EAAE;IAClB,IAAI,CAAC,IAAI,CAAC/E,OAAO,EAAE,OAAO,IAAI;IAC9B,MAAMM,CAAC,GAAG,IAAI,CAACA,CAAC,GAAGyE,KAAK,CAACzE,CAAC,GAAG,IAAI,CAACA,CAAC,GAAGyE,KAAK,CAACzE,CAAC;MAC3CC,CAAC,GAAG,IAAI,CAACA,CAAC,GAAGwE,KAAK,CAACxE,CAAC,GAAG,IAAI,CAACA,CAAC,GAAGwE,KAAK,CAACxE,CAAC;IAEzC,IAAID,CAAC,IAAIC,CAAC,EAAE;MACV,OAAO,IAAI;IACb,CAAC,MAAM;MACL,OAAON,QAAQ,CAACW,aAAa,CAACN,CAAC,EAAEC,CAAC,CAAC;IACrC;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE8E,KAAKA,CAACN,KAAK,EAAE;IACX,IAAI,CAAC,IAAI,CAAC/E,OAAO,EAAE,OAAO,IAAI;IAC9B,MAAMM,CAAC,GAAG,IAAI,CAACA,CAAC,GAAGyE,KAAK,CAACzE,CAAC,GAAG,IAAI,CAACA,CAAC,GAAGyE,KAAK,CAACzE,CAAC;MAC3CC,CAAC,GAAG,IAAI,CAACA,CAAC,GAAGwE,KAAK,CAACxE,CAAC,GAAG,IAAI,CAACA,CAAC,GAAGwE,KAAK,CAACxE,CAAC;IACzC,OAAON,QAAQ,CAACW,aAAa,CAACN,CAAC,EAAEC,CAAC,CAAC;EACrC;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,OAAO+E,KAAKA,CAACC,SAAS,EAAE;IACtB,MAAM,CAACC,KAAK,EAAEC,KAAK,CAAC,GAAGF,SAAS,CAC7B1B,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACxD,CAAC,GAAGyD,CAAC,CAACzD,CAAC,CAAC,CACzBoF,MAAM,CACL,CAAC,CAACC,KAAK,EAAEC,OAAO,CAAC,EAAEC,IAAI,KAAK;MAC1B,IAAI,CAACD,OAAO,EAAE;QACZ,OAAO,CAACD,KAAK,EAAEE,IAAI,CAAC;MACtB,CAAC,MAAM,IAAID,OAAO,CAACd,QAAQ,CAACe,IAAI,CAAC,IAAID,OAAO,CAACZ,UAAU,CAACa,IAAI,CAAC,EAAE;QAC7D,OAAO,CAACF,KAAK,EAAEC,OAAO,CAACP,KAAK,CAACQ,IAAI,CAAC,CAAC;MACrC,CAAC,MAAM;QACL,OAAO,CAACF,KAAK,CAACG,MAAM,CAAC,CAACF,OAAO,CAAC,CAAC,EAAEC,IAAI,CAAC;MACxC;IACF,CAAC,EACD,CAAC,EAAE,EAAE,IAAI,CACX,CAAC;IACH,IAAIJ,KAAK,EAAE;MACTD,KAAK,CAACnB,IAAI,CAACoB,KAAK,CAAC;IACnB;IACA,OAAOD,KAAK;EACd;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAOO,GAAGA,CAACR,SAAS,EAAE;IACpB,IAAIzF,KAAK,GAAG,IAAI;MACdkG,YAAY,GAAG,CAAC;IAClB,MAAM/B,OAAO,GAAG,EAAE;MAChBgC,IAAI,GAAGV,SAAS,CAAC7B,GAAG,CAAEQ,CAAC,IAAK,CAC1B;QAAEgC,IAAI,EAAEhC,CAAC,CAAC5D,CAAC;QAAE6F,IAAI,EAAE;MAAI,CAAC,EACxB;QAAED,IAAI,EAAEhC,CAAC,CAAC3D,CAAC;QAAE4F,IAAI,EAAE;MAAI,CAAC,CACzB,CAAC;MACFC,SAAS,GAAGC,KAAK,CAACC,SAAS,CAACR,MAAM,CAAC,GAAGG,IAAI,CAAC;MAC3CM,GAAG,GAAGH,SAAS,CAACvC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACoC,IAAI,GAAGnC,CAAC,CAACmC,IAAI,CAAC;IAEjD,KAAK,MAAMhC,CAAC,IAAIqC,GAAG,EAAE;MACnBP,YAAY,IAAI9B,CAAC,CAACiC,IAAI,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;MAEvC,IAAIH,YAAY,KAAK,CAAC,EAAE;QACtBlG,KAAK,GAAGoE,CAAC,CAACgC,IAAI;MAChB,CAAC,MAAM;QACL,IAAIpG,KAAK,IAAI,CAACA,KAAK,KAAK,CAACoE,CAAC,CAACgC,IAAI,EAAE;UAC/BjC,OAAO,CAACI,IAAI,CAACpE,QAAQ,CAACW,aAAa,CAACd,KAAK,EAAEoE,CAAC,CAACgC,IAAI,CAAC,CAAC;QACrD;QAEApG,KAAK,GAAG,IAAI;MACd;IACF;IAEA,OAAOG,QAAQ,CAACqF,KAAK,CAACrB,OAAO,CAAC;EAChC;;EAEA;AACF;AACA;AACA;AACA;EACEuC,UAAUA,CAAC,GAAGjB,SAAS,EAAE;IACvB,OAAOtF,QAAQ,CAAC8F,GAAG,CAAC,CAAC,IAAI,CAAC,CAACD,MAAM,CAACP,SAAS,CAAC,CAAC,CAC1C7B,GAAG,CAAEQ,CAAC,IAAK,IAAI,CAACkB,YAAY,CAAClB,CAAC,CAAC,CAAC,CAChCP,MAAM,CAAEO,CAAC,IAAKA,CAAC,IAAI,CAACA,CAAC,CAACjB,OAAO,CAAC,CAAC,CAAC;EACrC;;EAEA;AACF;AACA;AACA;EACEwD,QAAQA,CAAA,EAAG;IACT,IAAI,CAAC,IAAI,CAACzG,OAAO,EAAE,OAAOJ,OAAO;IACjC,OAAQ,IAAG,IAAI,CAACU,CAAC,CAACH,KAAK,CAAC,CAAE,MAAK,IAAI,CAACI,CAAC,CAACJ,KAAK,CAAC,CAAE,GAAE;EAClD;;EAEA;AACF;AACA;AACA;EACE,CAACuG,MAAM,CAACC,GAAG,CAAC,4BAA4B,CAAC,IAAI;IAC3C,IAAI,IAAI,CAAC3G,OAAO,EAAE;MAChB,OAAQ,qBAAoB,IAAI,CAACM,CAAC,CAACH,KAAK,CAAC,CAAE,UAAS,IAAI,CAACI,CAAC,CAACJ,KAAK,CAAC,CAAE,IAAG;IACxE,CAAC,MAAM;MACL,OAAQ,+BAA8B,IAAI,CAAC6B,aAAc,IAAG;IAC9D;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE4E,cAAcA,CAACC,UAAU,GAAGlH,OAAO,CAACmH,UAAU,EAAEpF,IAAI,GAAG,CAAC,CAAC,EAAE;IACzD,OAAO,IAAI,CAAC1B,OAAO,GACfN,SAAS,CAACqH,MAAM,CAAC,IAAI,CAACzG,CAAC,CAAC0G,GAAG,CAACC,KAAK,CAACvF,IAAI,CAAC,EAAEmF,UAAU,CAAC,CAACK,cAAc,CAAC,IAAI,CAAC,GACzEtH,OAAO;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEO,KAAKA,CAACuB,IAAI,EAAE;IACV,IAAI,CAAC,IAAI,CAAC1B,OAAO,EAAE,OAAOJ,OAAO;IACjC,OAAQ,GAAE,IAAI,CAACU,CAAC,CAACH,KAAK,CAACuB,IAAI,CAAE,IAAG,IAAI,CAACnB,CAAC,CAACJ,KAAK,CAACuB,IAAI,CAAE,EAAC;EACtD;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEyF,SAASA,CAAA,EAAG;IACV,IAAI,CAAC,IAAI,CAACnH,OAAO,EAAE,OAAOJ,OAAO;IACjC,OAAQ,GAAE,IAAI,CAACU,CAAC,CAAC6G,SAAS,CAAC,CAAE,IAAG,IAAI,CAAC5G,CAAC,CAAC4G,SAAS,CAAC,CAAE,EAAC;EACtD;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACEC,SAASA,CAAC1F,IAAI,EAAE;IACd,IAAI,CAAC,IAAI,CAAC1B,OAAO,EAAE,OAAOJ,OAAO;IACjC,OAAQ,GAAE,IAAI,CAACU,CAAC,CAAC8G,SAAS,CAAC1F,IAAI,CAAE,IAAG,IAAI,CAACnB,CAAC,CAAC6G,SAAS,CAAC1F,IAAI,CAAE,EAAC;EAC9D;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE2F,QAAQA,CAACC,UAAU,EAAE;IAAEC,SAAS,GAAG;EAAM,CAAC,GAAG,CAAC,CAAC,EAAE;IAC/C,IAAI,CAAC,IAAI,CAACvH,OAAO,EAAE,OAAOJ,OAAO;IACjC,OAAQ,GAAE,IAAI,CAACU,CAAC,CAAC+G,QAAQ,CAACC,UAAU,CAAE,GAAEC,SAAU,GAAE,IAAI,CAAChH,CAAC,CAAC8G,QAAQ,CAACC,UAAU,CAAE,EAAC;EACnF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACElF,UAAUA,CAACD,IAAI,EAAET,IAAI,EAAE;IACrB,IAAI,CAAC,IAAI,CAAC1B,OAAO,EAAE;MACjB,OAAOX,QAAQ,CAACa,OAAO,CAAC,IAAI,CAAC8B,aAAa,CAAC;IAC7C;IACA,OAAO,IAAI,CAACzB,CAAC,CAACuC,IAAI,CAAC,IAAI,CAACxC,CAAC,EAAE6B,IAAI,EAAET,IAAI,CAAC;EACxC;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE8F,YAAYA,CAACC,KAAK,EAAE;IAClB,OAAOxH,QAAQ,CAACW,aAAa,CAAC6G,KAAK,CAAC,IAAI,CAACnH,CAAC,CAAC,EAAEmH,KAAK,CAAC,IAAI,CAAClH,CAAC,CAAC,CAAC;EAC7D;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}