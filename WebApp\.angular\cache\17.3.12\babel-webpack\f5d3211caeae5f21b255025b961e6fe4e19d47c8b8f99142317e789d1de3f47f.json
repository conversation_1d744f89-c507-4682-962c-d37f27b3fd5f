{"ast": null, "code": "import { EmbedBlot } from 'parchment';\nclass Break extends EmbedBlot {\n  static value() {\n    return undefined;\n  }\n  optimize() {\n    if (this.prev || this.next) {\n      this.remove();\n    }\n  }\n  length() {\n    return 0;\n  }\n  value() {\n    return '';\n  }\n}\nBreak.blotName = 'break';\nBreak.tagName = 'BR';\nexport default Break;", "map": {"version": 3, "names": ["EmbedBlot", "Break", "value", "undefined", "optimize", "prev", "next", "remove", "length", "blotName", "tagName"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/quill/blots/break.js"], "sourcesContent": ["import { EmbedBlot } from 'parchment';\nclass Break extends EmbedBlot {\n  static value() {\n    return undefined;\n  }\n  optimize() {\n    if (this.prev || this.next) {\n      this.remove();\n    }\n  }\n  length() {\n    return 0;\n  }\n  value() {\n    return '';\n  }\n}\nBreak.blotName = 'break';\nBreak.tagName = 'BR';\nexport default Break;\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,WAAW;AACrC,MAAMC,KAAK,SAASD,SAAS,CAAC;EAC5B,OAAOE,KAAKA,CAAA,EAAG;IACb,OAAOC,SAAS;EAClB;EACAC,QAAQA,CAAA,EAAG;IACT,IAAI,IAAI,CAACC,IAAI,IAAI,IAAI,CAACC,IAAI,EAAE;MAC1B,IAAI,CAACC,MAAM,CAAC,CAAC;IACf;EACF;EACAC,MAAMA,CAAA,EAAG;IACP,OAAO,CAAC;EACV;EACAN,KAAKA,CAAA,EAAG;IACN,OAAO,EAAE;EACX;AACF;AACAD,KAAK,CAACQ,QAAQ,GAAG,OAAO;AACxBR,KAAK,CAACS,OAAO,GAAG,IAAI;AACpB,eAAeT,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}