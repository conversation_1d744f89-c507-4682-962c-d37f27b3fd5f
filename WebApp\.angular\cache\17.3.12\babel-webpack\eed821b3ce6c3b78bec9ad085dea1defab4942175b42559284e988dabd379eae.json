{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./add-or-edit-agents.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"C:/Users/<USER>/source/ai-hub/WebApp/src/app/workspaces/agents/add-or-edit-agents/add-or-edit-agents.component.ts.css?ngResource!=!C:\\\\Users\\\\<USER>\\\\source\\\\ai-hub\\\\WebApp\\\\node_modules\\\\@ngtools\\\\webpack\\\\src\\\\loaders\\\\inline-resource.js?data=CiAgICAgIDo6bmctZGVlcCAuYW50LXNlbGVjdC1tdWx0aXBsZSAuYW50LXNlbGVjdC1zZWxlY3Rpb24taXRlbXsKICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS1zZWNvbmRhcnktcHVycGxlKSAhaW1wb3J0YW50OwogICAgICB9CiAgICA%3D!C:/Users/<USER>/source/ai-hub/WebApp/src/app/workspaces/agents/add-or-edit-agents/add-or-edit-agents.component.ts\";\nimport { Component, ViewChild } from '@angular/core';\nimport { Router, RouterLink } from '@angular/router';\nimport { FormsModule } from '@angular/forms';\nimport { CommonModule } from '@angular/common';\nimport { NzAutocompleteModule } from 'ng-zorro-antd/auto-complete';\nimport { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';\nimport { NzInputModule } from 'ng-zorro-antd/input';\nimport { NzRadioModule } from 'ng-zorro-antd/radio';\nimport { NzSelectModule } from 'ng-zorro-antd/select';\nimport { AgentDefinitionServiceProxy, AiServiceProxy, ModelDetailsServiceProxy, PluginServiceProxy } from '../../../../shared/service-proxies/service-proxies';\nimport { ServiceProxyModule } from '../../../../shared/service-proxies/service-proxy.module';\nimport { finalize } from 'rxjs/operators';\nimport { RemoveProviderPrefixPipe } from \"../../../../shared/pipes/remove-provider-prefix.pipe\";\nlet AddOrEditAgentsComponent = class AddOrEditAgentsComponent {\n  constructor(agentService, router, aiService, modelDetailsService, _pluginService) {\n    this.agentService = agentService;\n    this.router = router;\n    this.aiService = aiService;\n    this.modelDetailsService = modelDetailsService;\n    this._pluginService = _pluginService;\n    // Renamed to singular for consistency\n    this.agents = [];\n    this.isEditing = false;\n    this.currentAgent = {\n      agentName: '',\n      instructions: '',\n      userInstructions: '',\n      modelName: '',\n      tools: [],\n      workspace: ''\n    };\n    this.modelSearchQuery = '';\n    this.filteredModels = [];\n    this.models = [];\n    this.agentName = '';\n    this.workspaceName = '';\n    this.plugins = [];\n    this.selectedPlugins = [];\n    this.routerParts = [];\n    // Agent description for AI generation\n    this.isGeneratingInstructions = false;\n    this.isInstructionsDisabled = true; // Initially disable instructions field\n    // Test agent properties\n    this.testQuestion = '';\n    this.testResponse = '';\n    this.testError = '';\n    this.isTestingAgent = false;\n    this.testSuccessful = true; // Always enabled as testing is optional\n    // Chat sidebar properties\n    this.showTestResults = false;\n    // Chat properties\n    this.chatMessages = [];\n    this.chatInput = '';\n  }\n  /**\n   * Scroll to the bottom of the chat container when new messages are added\n   */\n  ngAfterViewChecked() {\n    this.scrollToBottom();\n  }\n  /**\n   * Scroll to the bottom of the sidebar container\n   */\n  scrollToBottom() {\n    try {\n      if (this.sidebarContainer && this.showTestResults) {\n        setTimeout(() => {\n          if (this.sidebarContainer && this.sidebarContainer.nativeElement) {\n            this.sidebarContainer.nativeElement.scrollTop = this.sidebarContainer.nativeElement.scrollHeight;\n            console.log('Scrolled to bottom, height:', this.sidebarContainer.nativeElement.scrollHeight);\n          }\n        }, 200);\n      }\n    } catch (err) {\n      console.error('Error scrolling to bottom:', err);\n    }\n  }\n  ngOnInit() {\n    // Extract workspace from URL\n    this.routerParts = this.router.url.split('/');\n    console.log(this.routerParts);\n    if (this.routerParts[2] == 'agents') {\n      if (this.routerParts[3] == 'new') {\n        this.agentName = 'new';\n        this.isEditing = false;\n        this.workspaceName = \"Default\";\n      } else {\n        this.agentName = decodeURIComponent(this.routerParts[3]);\n        this.isEditing = true;\n        this.loadAgent(this.agentName); // Load existing agent data\n      }\n      // if (this.routerParts.length >= 4) {\n      //   this.workspaceName = decodeURIComponent(this.routerParts[3]);\n      // }\n      // else {\n      // }\n      // this.workspaceName = decodeURIComponent(this.routerParts[3]);\n      // this.isEditing = false; // Explicitly set for new agent\n    } else {\n      if (this.routerParts[4] == 'new') {\n        this.agentName = 'new';\n        this.isEditing = false;\n        this.workspaceName = decodeURIComponent(this.routerParts[2]);\n      } else {\n        this.agentName = decodeURIComponent(this.routerParts[4]);\n        this.isEditing = true;\n        this.loadAgent(this.agentName); // Load existing agent data\n      }\n    }\n    console.log('Workspace Name: ', this.agentName);\n    this.loadModels();\n    this.loadPlugins();\n    // // Check if we're editing based on route params\n    // if (!(this.workspaceName == 'new')) {\n    //   this.isEditing = true;\n    //   this.loadAgent(this.workspaceName); // Load existing agent data\n    // } else {\n    // }\n    // Load models and plugins\n  }\n  loadAgent(agentName) {\n    // Fetch agent details for editing\n    this.agentService.getByAgentName(agentName).subscribe(agent => {\n      this.currentAgent = {\n        ...agent\n      };\n      this.modelSearchQuery = this.currentAgent.modelName || '';\n      this.selectedPlugins = [...(this.currentAgent.tools || [])];\n      this.workspaceName = this.currentAgent.workspace || 'Default';\n      // For editing existing agents, we still require testing before saving\n      // to ensure any changes don't break the agent\n      this.testSuccessful = false;\n    });\n  }\n  loadModels() {\n    this.modelDetailsService.getAllActiveModel().subscribe(models => {\n      this.filteredModels = models;\n      this.models = models;\n    });\n  }\n  loadPlugins() {\n    this._pluginService.getAllPluginNames().subscribe(res => {\n      this.plugins = [...res.message];\n    });\n  }\n  onChange(event) {\n    const query = event.target.value.toLowerCase();\n    this.filteredModels = this.models.filter(m => m.modelName.toLowerCase().includes(query));\n  }\n  updateModel(modelName) {\n    this.currentAgent.modelName = modelName;\n    // Also update the search box to show only the model name (without provider) for user display\n    this.modelSearchQuery = this.removeProviderPrefix(modelName);\n  }\n  /**\n   * Remove provider prefix for display (e.g., azure_gpt-4o-mini -> gpt-4o-mini)\n   */\n  removeProviderPrefix(value) {\n    if (!value) return '';\n    const parts = value.split('_');\n    return parts.slice(1).join('_');\n  }\n  /**\n   * Check if the agent can generate instructions\n   * @returns boolean indicating if the agent has the required fields to generate instructions\n   */\n  canGenerateInstructions() {\n    return this.currentAgent.agentName?.trim() !== '' && this.currentAgent.modelName?.trim() !== '';\n  }\n  /**\n   * Generate agent instructions using AI\n   */\n  generateInstructions() {\n    this.isGeneratingInstructions = true;\n    // Prepare the prompt for the instruction generator agent\n    const prompt = `Generate detailed instructions for an AI agent with the following description:\n\nAgent Name: ${this.currentAgent.agentName || 'Unnamed Agent'}\nDescription: ${this.currentAgent.userInstructions || 'No description provided'}\nSelected Plugins: ${this.selectedPlugins.join(', ')}\n\nThe instructions should be comprehensive and clearly define the agent's purpose, capabilities, and limitations.\nFormat the instructions in a clear, professional manner that will guide the AI in responding appropriately to user queries.\n`;\n    // Call the instruction generator agent\n    this.aiService.callAgent('InstructionGeneratorAgent', prompt).pipe(finalize(() => {\n      this.isGeneratingInstructions = false;\n    })).subscribe({\n      next: response => {\n        if (response && response.message) {\n          // Update the instructions field with the generated content\n          this.currentAgent.instructions = response.message;\n          // Enable the instructions field for editing\n          this.isInstructionsDisabled = false;\n          console.log('Instructions generated successfully');\n        } else {\n          console.error('Failed to generate instructions: Empty response');\n        }\n      },\n      error: error => {\n        console.error('Error generating instructions:', error);\n        // If there's an error with the instruction generator agent, try a fallback approach\n        this.generateFallbackInstructions();\n      }\n    });\n  }\n  /**\n   * Generate fallback instructions if the instruction generator agent fails\n   */\n  generateFallbackInstructions() {\n    // Create a basic template based on the agent description\n    this.currentAgent.instructions = `You are ${this.currentAgent.agentName || 'an AI assistant'}.\n\nPurpose: ${this.currentAgent.userInstructions || 'No description provided'}\n\n${this.selectedPlugins.length > 0 ? 'You have access to the following plugins: ' + this.selectedPlugins.join(', ') : ''}\n\nWhen responding to user queries:\n1. Be helpful, accurate, and concise\n2. Stay within your defined purpose and capabilities\n3. If you're unsure about something, acknowledge your limitations\n4. Provide clear, well-structured responses\n`;\n    // Enable the instructions field for editing\n    this.isInstructionsDisabled = false;\n  }\n  saveAgent() {\n    this.currentAgent.tools = this.selectedPlugins;\n    this.currentAgent.workspace = this.workspaceName;\n    console.log(this.workspaceName);\n    console.log(this.currentAgent);\n    this.agentService.createOrUpdate(this.currentAgent).subscribe(res => {\n      console.log(res);\n      if (this.routerParts[2] == 'agents') {\n        this.router.navigate(['settings', 'agents']);\n      } else {\n        this.router.navigate(['workspaces', this.workspaceName, 'agents']);\n      }\n    });\n  }\n  /**\n   * Check if the agent has required fields filled\n   * @returns boolean indicating if the agent has required fields\n   */\n  hasRequiredFields() {\n    // Check if all required fields are filled\n    return this.currentAgent.agentName?.trim() !== '' && this.currentAgent.instructions?.trim() !== '' && this.currentAgent.modelName?.trim() !== '' && this.workspaceName?.trim() !== '' && this.currentAgent.userInstructions?.trim() !== '';\n  }\n  /**\n   * Open the chat interface for testing the agent\n   */\n  testAgent() {\n    console.log(\"Opening chat interface\");\n    // Make sure we have the required fields\n    if (!this.hasRequiredFields()) {\n      console.error(\"Missing required fields\");\n      return;\n    }\n    // Clear previous chat messages when starting a new test\n    this.chatMessages = [];\n    // Add a welcome message\n    const welcomeMessage = {\n      sender: 'agent',\n      content: `Hello! I'm your ${this.currentAgent.agentName || 'AI'} agent. How can I help you today?`,\n      timestamp: new Date()\n    };\n    this.chatMessages.push(welcomeMessage);\n    // Show the chat sidebar with a slight delay to ensure smooth animation\n    setTimeout(() => {\n      this.showTestResults = true;\n      // Force scroll to bottom after sidebar is shown\n      setTimeout(() => {\n        this.scrollToBottom();\n        // Try one more time after a longer delay to ensure content is fully rendered\n        setTimeout(() => {\n          this.scrollToBottom();\n        }, 500);\n      }, 300);\n    }, 50);\n  }\n  /**\n   * Send a message in the chat\n   */\n  sendChatMessage() {\n    if (!this.chatInput.trim()) {\n      return;\n    }\n    console.log(\"Sending message:\", this.chatInput);\n    // Add user message to chat\n    const userMessage = {\n      sender: 'user',\n      content: this.chatInput.trim(),\n      timestamp: new Date()\n    };\n    this.chatMessages.push(userMessage);\n    // Store the question\n    const question = this.chatInput.trim();\n    // Clear input field\n    this.chatInput = '';\n    // Add loading message\n    const loadingMessage = {\n      sender: 'loading',\n      content: '',\n      timestamp: new Date()\n    };\n    this.chatMessages.push(loadingMessage);\n    // Force scroll to bottom with multiple attempts to ensure it works\n    setTimeout(() => {\n      this.scrollToBottom();\n      // Try again after a short delay\n      setTimeout(() => {\n        this.scrollToBottom();\n      }, 300);\n    }, 100);\n    // Set testing state\n    this.isTestingAgent = true;\n    // Make sure we have an agent name\n    if (!this.currentAgent.agentName) {\n      this.chatMessages.pop(); // Remove loading message\n      const errorMessage = {\n        sender: 'agent',\n        content: 'Error: Agent name is required',\n        timestamp: new Date()\n      };\n      this.chatMessages.push(errorMessage);\n      this.isTestingAgent = false;\n      return;\n    }\n    // Call the agent\n    console.log(\"Calling agent:\", this.currentAgent.agentName);\n    this.agentService.testAgent(question, this.currentAgent).subscribe({\n      next: response => {\n        // Remove loading message\n        this.chatMessages.pop();\n        // Add agent response\n        const agentMessage = {\n          sender: 'agent',\n          content: response.message || 'No response from agent',\n          timestamp: new Date()\n        };\n        this.chatMessages.push(agentMessage);\n        // Update test state\n        this.isTestingAgent = false;\n        this.testSuccessful = true;\n        // Scroll to bottom with multiple attempts\n        this.scrollToBottom();\n        setTimeout(() => {\n          this.scrollToBottom();\n        }, 300);\n      },\n      error: error => {\n        // Remove loading message\n        this.chatMessages.pop();\n        // Add error message\n        const errorMessage = {\n          sender: 'agent',\n          content: 'Error: ' + (error.message || 'Unknown error occurred'),\n          timestamp: new Date()\n        };\n        this.chatMessages.push(errorMessage);\n        // Update test state\n        this.isTestingAgent = false;\n        // Scroll to bottom with multiple attempts\n        this.scrollToBottom();\n        setTimeout(() => {\n          this.scrollToBottom();\n        }, 300);\n      }\n    });\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: AgentDefinitionServiceProxy\n    }, {\n      type: Router\n    }, {\n      type: AiServiceProxy\n    }, {\n      type: ModelDetailsServiceProxy\n    }, {\n      type: PluginServiceProxy\n    }];\n  }\n  static {\n    this.propDecorators = {\n      sidebarContainer: [{\n        type: ViewChild,\n        args: ['sidebarContainer']\n      }]\n    };\n  }\n};\nAddOrEditAgentsComponent = __decorate([Component({\n  selector: 'app-add-or-edit-agent',\n  standalone: true,\n  imports: [FormsModule, CommonModule, NzInputModule, NzAutocompleteModule, NzSelectModule, NzRadioModule, NzBreadCrumbModule, ServiceProxyModule, RouterLink, RemoveProviderPrefixPipe],\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], AddOrEditAgentsComponent);\nexport { AddOrEditAgentsComponent };", "map": {"version": 3, "names": ["Component", "ViewChild", "Router", "RouterLink", "FormsModule", "CommonModule", "NzAutocompleteModule", "NzBreadCrumbModule", "NzInputModule", "NzRadioModule", "NzSelectModule", "AgentDefinitionServiceProxy", "AiServiceProxy", "ModelDetailsServiceProxy", "PluginServiceProxy", "ServiceProxyModule", "finalize", "RemoveProviderPrefixPipe", "AddOrEditAgentsComponent", "constructor", "agentService", "router", "aiService", "modelDetailsService", "_pluginService", "agents", "isEditing", "currentAgent", "<PERSON><PERSON><PERSON>", "instructions", "userInstructions", "modelName", "tools", "workspace", "modelSearchQuery", "filteredModels", "models", "workspaceName", "plugins", "selected<PERSON><PERSON><PERSON>", "routerParts", "isGeneratingInstructions", "isInstructionsDisabled", "testQuestion", "testResponse", "testError", "isTestingAgent", "testSuccessful", "showTestResults", "chatMessages", "chatInput", "ngAfterViewChecked", "scrollToBottom", "sidebarContainer", "setTimeout", "nativeElement", "scrollTop", "scrollHeight", "console", "log", "err", "error", "ngOnInit", "url", "split", "decodeURIComponent", "loadAgent", "loadModels", "loadPlugins", "getByAgentName", "subscribe", "agent", "getAllActiveModel", "getAllPluginNames", "res", "message", "onChange", "event", "query", "target", "value", "toLowerCase", "filter", "m", "includes", "updateModel", "removeProviderPrefix", "parts", "slice", "join", "canGenerateInstructions", "trim", "generateInstructions", "prompt", "callAgent", "pipe", "next", "response", "generateFallbackInstructions", "length", "saveAgent", "createOrUpdate", "navigate", "hasRequiredFields", "testAgent", "welcomeMessage", "sender", "content", "timestamp", "Date", "push", "sendChatMessage", "userMessage", "question", "loadingMessage", "pop", "errorMessage", "agentMessage", "args", "__decorate", "selector", "standalone", "imports", "template", "__NG_CLI_RESOURCE__0"], "sources": ["C:\\Users\\<USER>\\source\\ai-hub\\WebApp\\src\\app\\workspaces\\agents\\add-or-edit-agents\\add-or-edit-agents.component.ts"], "sourcesContent": ["import { Component, ViewChild, ElementRef, AfterViewChecked } from '@angular/core';\r\nimport { Router, RouterLink } from '@angular/router';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NzAutocompleteModule } from 'ng-zorro-antd/auto-complete';\r\nimport { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';\r\nimport { NzInputModule } from 'ng-zorro-antd/input';\r\nimport { NzRadioModule } from 'ng-zorro-antd/radio';\r\nimport { NzSelectModule } from 'ng-zorro-antd/select';\r\nimport {\r\n  AgentDefinitionServiceProxy,\r\n  AiServiceProxy,\r\n  ModelDetailsServiceProxy,\r\n  PluginServiceProxy,\r\n  ResponseMessage\r\n} from '../../../../shared/service-proxies/service-proxies';\r\nimport { ServiceProxyModule } from '../../../../shared/service-proxies/service-proxy.module';\r\nimport { finalize } from 'rxjs/operators';\r\nimport { RemoveProviderPrefixPipe } from \"../../../../shared/pipes/remove-provider-prefix.pipe\";\r\n\r\n// Define interface for chat messages\r\ninterface ChatMessage {\r\n  sender: 'user' | 'agent' | 'loading';\r\n  content: string;\r\n  timestamp: Date;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-add-or-edit-agent',\r\n  standalone: true,\r\n  imports: [\r\n    FormsModule,\r\n    CommonModule,\r\n    NzInputModule,\r\n    NzAutocompleteModule,\r\n    NzSelectModule,\r\n    NzRadioModule,\r\n    NzBreadCrumbModule,\r\n    ServiceProxyModule,\r\n    RouterLink,\r\n    RemoveProviderPrefixPipe\r\n  ],\r\n  templateUrl: './add-or-edit-agents.component.html', // Updated to match selector,\r\n  styles: [\r\n    `\r\n      ::ng-deep .ant-select-multiple .ant-select-selection-item{\r\n        background-color: var(--secondary-purple) !important;\r\n      }\r\n    `,\r\n  ],\r\n})\r\nexport class AddOrEditAgentsComponent implements AfterViewChecked {\r\n  @ViewChild('sidebarContainer') private sidebarContainer!: ElementRef;\r\n\r\n  // Renamed to singular for consistency\r\n  agents: any[] = [];\r\n  isEditing: boolean = false;\r\n  currentAgent: any = {\r\n    agentName: '',\r\n    instructions: '',\r\n    userInstructions: '',\r\n    modelName: '',\r\n    tools: [],\r\n    workspace: '',\r\n  };\r\n  modelSearchQuery: string = '';\r\n  filteredModels: any[] = [];\r\n  models: any[] = [];\r\n  agentName: string = '';\r\n  workspaceName: string = '';\r\n  plugins: any[] = [];\r\n  selectedPlugins: string[] = [];\r\n  routerParts: string[] = [];\r\n\r\n  // Agent description for AI generation\r\n  isGeneratingInstructions: boolean = false;\r\n  isInstructionsDisabled: boolean = true; // Initially disable instructions field\r\n\r\n  // Test agent properties\r\n  testQuestion: string = '';\r\n  testResponse: string = '';\r\n  testError: string = '';\r\n  isTestingAgent: boolean = false;\r\n  testSuccessful: boolean = true; // Always enabled as testing is optional\r\n\r\n  // Chat sidebar properties\r\n  showTestResults: boolean = false;\r\n\r\n  // Chat properties\r\n  chatMessages: ChatMessage[] = [];\r\n  chatInput: string = '';\r\n\r\n  constructor(\r\n    private agentService: AgentDefinitionServiceProxy,\r\n    private router: Router,\r\n    private aiService: AiServiceProxy,\r\n    private modelDetailsService: ModelDetailsServiceProxy,\r\n    private _pluginService: PluginServiceProxy\r\n  ) { }\r\n\r\n  /**\r\n   * Scroll to the bottom of the chat container when new messages are added\r\n   */\r\n  ngAfterViewChecked() {\r\n    this.scrollToBottom();\r\n  }\r\n\r\n  /**\r\n   * Scroll to the bottom of the sidebar container\r\n   */\r\n  scrollToBottom(): void {\r\n    try {\r\n      if (this.sidebarContainer && this.showTestResults) {\r\n        setTimeout(() => {\r\n          if (this.sidebarContainer && this.sidebarContainer.nativeElement) {\r\n            this.sidebarContainer.nativeElement.scrollTop = this.sidebarContainer.nativeElement.scrollHeight;\r\n            console.log('Scrolled to bottom, height:', this.sidebarContainer.nativeElement.scrollHeight);\r\n          }\r\n        }, 200);\r\n      }\r\n    } catch (err) {\r\n      console.error('Error scrolling to bottom:', err);\r\n    }\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    // Extract workspace from URL\r\n    this.routerParts = this.router.url.split('/');\r\n    console.log(this.routerParts);\r\n\r\n    if (this.routerParts[2] == 'agents') {\r\n      if (this.routerParts[3] == 'new') {\r\n        this.agentName = 'new';\r\n        this.isEditing = false;\r\n        this.workspaceName = \"Default\"\r\n      } else {\r\n        this.agentName = decodeURIComponent(this.routerParts[3]);\r\n        this.isEditing = true;\r\n        this.loadAgent(this.agentName); // Load existing agent data\r\n      }\r\n      // if (this.routerParts.length >= 4) {\r\n      //   this.workspaceName = decodeURIComponent(this.routerParts[3]);\r\n      // }\r\n      // else {\r\n      // }\r\n      // this.workspaceName = decodeURIComponent(this.routerParts[3]);\r\n      // this.isEditing = false; // Explicitly set for new agent\r\n    } else {\r\n      if (this.routerParts[4] == 'new') {\r\n        this.agentName = 'new';\r\n        this.isEditing = false;\r\n        this.workspaceName = decodeURIComponent(this.routerParts[2]);\r\n      } else {\r\n        this.agentName = decodeURIComponent(this.routerParts[4]);\r\n        this.isEditing = true;\r\n        this.loadAgent(this.agentName); // Load existing agent data\r\n      }\r\n    }\r\n    console.log(\r\n      'Workspace Name: ',\r\n      this.agentName\r\n    );\r\n    this.loadModels();\r\n    this.loadPlugins();\r\n\r\n    // // Check if we're editing based on route params\r\n\r\n    // if (!(this.workspaceName == 'new')) {\r\n    //   this.isEditing = true;\r\n    //   this.loadAgent(this.workspaceName); // Load existing agent data\r\n    // } else {\r\n    // }\r\n\r\n    // Load models and plugins\r\n\r\n  }\r\n\r\n  loadAgent(agentName: string) {\r\n    // Fetch agent details for editing\r\n    this.agentService.getByAgentName(agentName).subscribe((agent: any) => {\r\n      this.currentAgent = { ...agent };\r\n      this.modelSearchQuery = this.currentAgent.modelName || '';\r\n      this.selectedPlugins = [...(this.currentAgent.tools || [])];\r\n      this.workspaceName = this.currentAgent.workspace || 'Default';\r\n\r\n      // For editing existing agents, we still require testing before saving\r\n      // to ensure any changes don't break the agent\r\n      this.testSuccessful = false;\r\n    });\r\n  }\r\n\r\n  loadModels() {\r\n    this.modelDetailsService.getAllActiveModel().subscribe((models: any) => {\r\n      this.filteredModels = models;\r\n      this.models = models;\r\n    });\r\n  }\r\n\r\n  loadPlugins() {\r\n    this._pluginService.getAllPluginNames().subscribe((res: any) => {\r\n      this.plugins = [...res.message];\r\n    });\r\n  }\r\n\r\n  onChange(event: any) {\r\n    const query = event.target.value.toLowerCase();\r\n    this.filteredModels = this.models.filter((m: any) =>\r\n      m.modelName.toLowerCase().includes(query)\r\n    );\r\n  }\r\n\r\n  updateModel(modelName: string) {\r\n    this.currentAgent.modelName = modelName;\r\n    // Also update the search box to show only the model name (without provider) for user display\r\n    this.modelSearchQuery = this.removeProviderPrefix(modelName);\r\n  }\r\n\r\n  /**\r\n   * Remove provider prefix for display (e.g., azure_gpt-4o-mini -> gpt-4o-mini)\r\n   */\r\n  removeProviderPrefix(value: string): string {\r\n    if (!value) return '';\r\n    const parts = value.split('_');\r\n    return parts.slice(1).join('_');\r\n  }\r\n\r\n  /**\r\n   * Check if the agent can generate instructions\r\n   * @returns boolean indicating if the agent has the required fields to generate instructions\r\n   */\r\n  canGenerateInstructions(): boolean {\r\n    return (\r\n      this.currentAgent.agentName?.trim() !== '' &&\r\n      this.currentAgent.modelName?.trim() !== ''\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Generate agent instructions using AI\r\n   */\r\n  generateInstructions() {\r\n    this.isGeneratingInstructions = true;\r\n\r\n    // Prepare the prompt for the instruction generator agent\r\n    const prompt = `Generate detailed instructions for an AI agent with the following description:\r\n\r\nAgent Name: ${this.currentAgent.agentName || 'Unnamed Agent'}\r\nDescription: ${this.currentAgent.userInstructions || 'No description provided'}\r\nSelected Plugins: ${this.selectedPlugins.join(', ')}\r\n\r\nThe instructions should be comprehensive and clearly define the agent's purpose, capabilities, and limitations.\r\nFormat the instructions in a clear, professional manner that will guide the AI in responding appropriately to user queries.\r\n`;\r\n\r\n    // Call the instruction generator agent\r\n    this.aiService.callAgent('InstructionGeneratorAgent', prompt)\r\n      .pipe(\r\n        finalize(() => {\r\n          this.isGeneratingInstructions = false;\r\n        })\r\n      )\r\n      .subscribe({\r\n        next: (response: ResponseMessage) => {\r\n          if (response && response.message) {\r\n            // Update the instructions field with the generated content\r\n            this.currentAgent.instructions = response.message;\r\n            // Enable the instructions field for editing\r\n            this.isInstructionsDisabled = false;\r\n            console.log('Instructions generated successfully');\r\n          } else {\r\n            console.error('Failed to generate instructions: Empty response');\r\n          }\r\n        },\r\n        error: (error) => {\r\n          console.error('Error generating instructions:', error);\r\n          // If there's an error with the instruction generator agent, try a fallback approach\r\n          this.generateFallbackInstructions();\r\n        }\r\n      });\r\n  }\r\n\r\n  /**\r\n   * Generate fallback instructions if the instruction generator agent fails\r\n   */\r\n  private generateFallbackInstructions() {\r\n    // Create a basic template based on the agent description\r\n    this.currentAgent.instructions = `You are ${this.currentAgent.agentName || 'an AI assistant'}.\r\n\r\nPurpose: ${this.currentAgent.userInstructions || 'No description provided'}\r\n\r\n${this.selectedPlugins.length > 0 ? 'You have access to the following plugins: ' + this.selectedPlugins.join(', ') : ''}\r\n\r\nWhen responding to user queries:\r\n1. Be helpful, accurate, and concise\r\n2. Stay within your defined purpose and capabilities\r\n3. If you're unsure about something, acknowledge your limitations\r\n4. Provide clear, well-structured responses\r\n`;\r\n    // Enable the instructions field for editing\r\n    this.isInstructionsDisabled = false;\r\n  }\r\n\r\n  saveAgent() {\r\n    this.currentAgent.tools = this.selectedPlugins;\r\n    this.currentAgent.workspace = this.workspaceName;\r\n    console.log(this.workspaceName);\r\n\r\n    console.log(this.currentAgent);\r\n    this.agentService\r\n      .createOrUpdate(this.currentAgent)\r\n      .subscribe((res: any) => {\r\n        console.log(res);\r\n        if (this.routerParts[2] == 'agents') {\r\n          this.router.navigate(['settings', 'agents']);\r\n        }\r\n        else {\r\n          this.router.navigate(['workspaces', this.workspaceName, 'agents']);\r\n        }\r\n      });\r\n  }\r\n\r\n  /**\r\n   * Check if the agent has required fields filled\r\n   * @returns boolean indicating if the agent has required fields\r\n   */\r\n  hasRequiredFields(): boolean {\r\n    // Check if all required fields are filled\r\n    return (\r\n      this.currentAgent.agentName?.trim() !== '' &&\r\n      this.currentAgent.instructions?.trim() !== '' &&\r\n      this.currentAgent.modelName?.trim() !== '' &&\r\n      this.workspaceName?.trim() !== '' &&\r\n      this.currentAgent.userInstructions?.trim() !== ''\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Open the chat interface for testing the agent\r\n   */\r\n  testAgent(): void {\r\n    console.log(\"Opening chat interface\");\r\n\r\n    // Make sure we have the required fields\r\n    if (!this.hasRequiredFields()) {\r\n      console.error(\"Missing required fields\");\r\n      return;\r\n    }\r\n\r\n    // Clear previous chat messages when starting a new test\r\n    this.chatMessages = [];\r\n\r\n    // Add a welcome message\r\n    const welcomeMessage: ChatMessage = {\r\n      sender: 'agent',\r\n      content: `Hello! I'm your ${this.currentAgent.agentName || 'AI'} agent. How can I help you today?`,\r\n      timestamp: new Date()\r\n    };\r\n    this.chatMessages.push(welcomeMessage);\r\n\r\n    // Show the chat sidebar with a slight delay to ensure smooth animation\r\n    setTimeout(() => {\r\n      this.showTestResults = true;\r\n\r\n      // Force scroll to bottom after sidebar is shown\r\n      setTimeout(() => {\r\n        this.scrollToBottom();\r\n        // Try one more time after a longer delay to ensure content is fully rendered\r\n        setTimeout(() => {\r\n          this.scrollToBottom();\r\n        }, 500);\r\n      }, 300);\r\n    }, 50);\r\n  }\r\n\r\n  /**\r\n   * Send a message in the chat\r\n   */\r\n  sendChatMessage(): void {\r\n    if (!this.chatInput.trim()) {\r\n      return;\r\n    }\r\n\r\n    console.log(\"Sending message:\", this.chatInput);\r\n\r\n    // Add user message to chat\r\n    const userMessage: ChatMessage = {\r\n      sender: 'user',\r\n      content: this.chatInput.trim(),\r\n      timestamp: new Date()\r\n    };\r\n    this.chatMessages.push(userMessage);\r\n\r\n    // Store the question\r\n    const question = this.chatInput.trim();\r\n\r\n    // Clear input field\r\n    this.chatInput = '';\r\n\r\n    // Add loading message\r\n    const loadingMessage: ChatMessage = {\r\n      sender: 'loading',\r\n      content: '',\r\n      timestamp: new Date()\r\n    };\r\n    this.chatMessages.push(loadingMessage);\r\n\r\n    // Force scroll to bottom with multiple attempts to ensure it works\r\n    setTimeout(() => {\r\n      this.scrollToBottom();\r\n      // Try again after a short delay\r\n      setTimeout(() => {\r\n        this.scrollToBottom();\r\n      }, 300);\r\n    }, 100);\r\n\r\n    // Set testing state\r\n    this.isTestingAgent = true;\r\n\r\n    // Make sure we have an agent name\r\n    if (!this.currentAgent.agentName) {\r\n      this.chatMessages.pop(); // Remove loading message\r\n      const errorMessage: ChatMessage = {\r\n        sender: 'agent',\r\n        content: 'Error: Agent name is required',\r\n        timestamp: new Date()\r\n      };\r\n      this.chatMessages.push(errorMessage);\r\n      this.isTestingAgent = false;\r\n      return;\r\n    }\r\n\r\n    // Call the agent\r\n    console.log(\"Calling agent:\", this.currentAgent.agentName);\r\n    this.agentService.testAgent(question, this.currentAgent)\r\n      .subscribe({\r\n        next: (response: ResponseMessage) => {\r\n          // Remove loading message\r\n          this.chatMessages.pop();\r\n\r\n          // Add agent response\r\n          const agentMessage: ChatMessage = {\r\n            sender: 'agent',\r\n            content: response.message || 'No response from agent',\r\n            timestamp: new Date()\r\n          };\r\n          this.chatMessages.push(agentMessage);\r\n\r\n          // Update test state\r\n          this.isTestingAgent = false;\r\n          this.testSuccessful = true;\r\n\r\n          // Scroll to bottom with multiple attempts\r\n          this.scrollToBottom();\r\n          setTimeout(() => {\r\n            this.scrollToBottom();\r\n          }, 300);\r\n        },\r\n        error: (error: any) => {\r\n          // Remove loading message\r\n          this.chatMessages.pop();\r\n\r\n          // Add error message\r\n          const errorMessage: ChatMessage = {\r\n            sender: 'agent',\r\n            content: 'Error: ' + (error.message || 'Unknown error occurred'),\r\n            timestamp: new Date()\r\n          };\r\n          this.chatMessages.push(errorMessage);\r\n\r\n          // Update test state\r\n          this.isTestingAgent = false;\r\n\r\n          // Scroll to bottom with multiple attempts\r\n          this.scrollToBottom();\r\n          setTimeout(() => {\r\n            this.scrollToBottom();\r\n          }, 300);\r\n        }\r\n      });\r\n  }\r\n}\r\n"], "mappings": ";;;AAAA,SAASA,SAAS,EAAEC,SAAS,QAAsC,eAAe;AAClF,SAASC,MAAM,EAAEC,UAAU,QAAQ,iBAAiB;AACpD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,oBAAoB,QAAQ,6BAA6B;AAClE,SAASC,kBAAkB,QAAQ,0BAA0B;AAC7D,SAASC,aAAa,QAAQ,qBAAqB;AACnD,SAASC,aAAa,QAAQ,qBAAqB;AACnD,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SACEC,2BAA2B,EAC3BC,cAAc,EACdC,wBAAwB,EACxBC,kBAAkB,QAEb,oDAAoD;AAC3D,SAASC,kBAAkB,QAAQ,yDAAyD;AAC5F,SAASC,QAAQ,QAAQ,gBAAgB;AACzC,SAASC,wBAAwB,QAAQ,sDAAsD;AAiCxF,IAAMC,wBAAwB,GAA9B,MAAMA,wBAAwB;EAyCnCC,YACUC,YAAyC,EACzCC,MAAc,EACdC,SAAyB,EACzBC,mBAA6C,EAC7CC,cAAkC;IAJlC,KAAAJ,YAAY,GAAZA,YAAY;IACZ,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,cAAc,GAAdA,cAAc;IA3CxB;IACA,KAAAC,MAAM,GAAU,EAAE;IAClB,KAAAC,SAAS,GAAY,KAAK;IAC1B,KAAAC,YAAY,GAAQ;MAClBC,SAAS,EAAE,EAAE;MACbC,YAAY,EAAE,EAAE;MAChBC,gBAAgB,EAAE,EAAE;MACpBC,SAAS,EAAE,EAAE;MACbC,KAAK,EAAE,EAAE;MACTC,SAAS,EAAE;KACZ;IACD,KAAAC,gBAAgB,GAAW,EAAE;IAC7B,KAAAC,cAAc,GAAU,EAAE;IAC1B,KAAAC,MAAM,GAAU,EAAE;IAClB,KAAAR,SAAS,GAAW,EAAE;IACtB,KAAAS,aAAa,GAAW,EAAE;IAC1B,KAAAC,OAAO,GAAU,EAAE;IACnB,KAAAC,eAAe,GAAa,EAAE;IAC9B,KAAAC,WAAW,GAAa,EAAE;IAE1B;IACA,KAAAC,wBAAwB,GAAY,KAAK;IACzC,KAAAC,sBAAsB,GAAY,IAAI,CAAC,CAAC;IAExC;IACA,KAAAC,YAAY,GAAW,EAAE;IACzB,KAAAC,YAAY,GAAW,EAAE;IACzB,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAC,cAAc,GAAY,KAAK;IAC/B,KAAAC,cAAc,GAAY,IAAI,CAAC,CAAC;IAEhC;IACA,KAAAC,eAAe,GAAY,KAAK;IAEhC;IACA,KAAAC,YAAY,GAAkB,EAAE;IAChC,KAAAC,SAAS,GAAW,EAAE;EAQlB;EAEJ;;;EAGAC,kBAAkBA,CAAA;IAChB,IAAI,CAACC,cAAc,EAAE;EACvB;EAEA;;;EAGAA,cAAcA,CAAA;IACZ,IAAI;MACF,IAAI,IAAI,CAACC,gBAAgB,IAAI,IAAI,CAACL,eAAe,EAAE;QACjDM,UAAU,CAAC,MAAK;UACd,IAAI,IAAI,CAACD,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAACE,aAAa,EAAE;YAChE,IAAI,CAACF,gBAAgB,CAACE,aAAa,CAACC,SAAS,GAAG,IAAI,CAACH,gBAAgB,CAACE,aAAa,CAACE,YAAY;YAChGC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAACN,gBAAgB,CAACE,aAAa,CAACE,YAAY,CAAC;;QAEhG,CAAC,EAAE,GAAG,CAAC;;KAEV,CAAC,OAAOG,GAAG,EAAE;MACZF,OAAO,CAACG,KAAK,CAAC,4BAA4B,EAAED,GAAG,CAAC;;EAEpD;EAEAE,QAAQA,CAAA;IACN;IACA,IAAI,CAACtB,WAAW,GAAG,IAAI,CAACnB,MAAM,CAAC0C,GAAG,CAACC,KAAK,CAAC,GAAG,CAAC;IAC7CN,OAAO,CAACC,GAAG,CAAC,IAAI,CAACnB,WAAW,CAAC;IAE7B,IAAI,IAAI,CAACA,WAAW,CAAC,CAAC,CAAC,IAAI,QAAQ,EAAE;MACnC,IAAI,IAAI,CAACA,WAAW,CAAC,CAAC,CAAC,IAAI,KAAK,EAAE;QAChC,IAAI,CAACZ,SAAS,GAAG,KAAK;QACtB,IAAI,CAACF,SAAS,GAAG,KAAK;QACtB,IAAI,CAACW,aAAa,GAAG,SAAS;OAC/B,MAAM;QACL,IAAI,CAACT,SAAS,GAAGqC,kBAAkB,CAAC,IAAI,CAACzB,WAAW,CAAC,CAAC,CAAC,CAAC;QACxD,IAAI,CAACd,SAAS,GAAG,IAAI;QACrB,IAAI,CAACwC,SAAS,CAAC,IAAI,CAACtC,SAAS,CAAC,CAAC,CAAC;;MAElC;MACA;MACA;MACA;MACA;MACA;MACA;KACD,MAAM;MACL,IAAI,IAAI,CAACY,WAAW,CAAC,CAAC,CAAC,IAAI,KAAK,EAAE;QAChC,IAAI,CAACZ,SAAS,GAAG,KAAK;QACtB,IAAI,CAACF,SAAS,GAAG,KAAK;QACtB,IAAI,CAACW,aAAa,GAAG4B,kBAAkB,CAAC,IAAI,CAACzB,WAAW,CAAC,CAAC,CAAC,CAAC;OAC7D,MAAM;QACL,IAAI,CAACZ,SAAS,GAAGqC,kBAAkB,CAAC,IAAI,CAACzB,WAAW,CAAC,CAAC,CAAC,CAAC;QACxD,IAAI,CAACd,SAAS,GAAG,IAAI;QACrB,IAAI,CAACwC,SAAS,CAAC,IAAI,CAACtC,SAAS,CAAC,CAAC,CAAC;;;IAGpC8B,OAAO,CAACC,GAAG,CACT,kBAAkB,EAClB,IAAI,CAAC/B,SAAS,CACf;IACD,IAAI,CAACuC,UAAU,EAAE;IACjB,IAAI,CAACC,WAAW,EAAE;IAElB;IAEA;IACA;IACA;IACA;IACA;IAEA;EAEF;EAEAF,SAASA,CAACtC,SAAiB;IACzB;IACA,IAAI,CAACR,YAAY,CAACiD,cAAc,CAACzC,SAAS,CAAC,CAAC0C,SAAS,CAAEC,KAAU,IAAI;MACnE,IAAI,CAAC5C,YAAY,GAAG;QAAE,GAAG4C;MAAK,CAAE;MAChC,IAAI,CAACrC,gBAAgB,GAAG,IAAI,CAACP,YAAY,CAACI,SAAS,IAAI,EAAE;MACzD,IAAI,CAACQ,eAAe,GAAG,CAAC,IAAI,IAAI,CAACZ,YAAY,CAACK,KAAK,IAAI,EAAE,CAAC,CAAC;MAC3D,IAAI,CAACK,aAAa,GAAG,IAAI,CAACV,YAAY,CAACM,SAAS,IAAI,SAAS;MAE7D;MACA;MACA,IAAI,CAACc,cAAc,GAAG,KAAK;IAC7B,CAAC,CAAC;EACJ;EAEAoB,UAAUA,CAAA;IACR,IAAI,CAAC5C,mBAAmB,CAACiD,iBAAiB,EAAE,CAACF,SAAS,CAAElC,MAAW,IAAI;MACrE,IAAI,CAACD,cAAc,GAAGC,MAAM;MAC5B,IAAI,CAACA,MAAM,GAAGA,MAAM;IACtB,CAAC,CAAC;EACJ;EAEAgC,WAAWA,CAAA;IACT,IAAI,CAAC5C,cAAc,CAACiD,iBAAiB,EAAE,CAACH,SAAS,CAAEI,GAAQ,IAAI;MAC7D,IAAI,CAACpC,OAAO,GAAG,CAAC,GAAGoC,GAAG,CAACC,OAAO,CAAC;IACjC,CAAC,CAAC;EACJ;EAEAC,QAAQA,CAACC,KAAU;IACjB,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAACC,WAAW,EAAE;IAC9C,IAAI,CAAC9C,cAAc,GAAG,IAAI,CAACC,MAAM,CAAC8C,MAAM,CAAEC,CAAM,IAC9CA,CAAC,CAACpD,SAAS,CAACkD,WAAW,EAAE,CAACG,QAAQ,CAACN,KAAK,CAAC,CAC1C;EACH;EAEAO,WAAWA,CAACtD,SAAiB;IAC3B,IAAI,CAACJ,YAAY,CAACI,SAAS,GAAGA,SAAS;IACvC;IACA,IAAI,CAACG,gBAAgB,GAAG,IAAI,CAACoD,oBAAoB,CAACvD,SAAS,CAAC;EAC9D;EAEA;;;EAGAuD,oBAAoBA,CAACN,KAAa;IAChC,IAAI,CAACA,KAAK,EAAE,OAAO,EAAE;IACrB,MAAMO,KAAK,GAAGP,KAAK,CAAChB,KAAK,CAAC,GAAG,CAAC;IAC9B,OAAOuB,KAAK,CAACC,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;EACjC;EAEA;;;;EAIAC,uBAAuBA,CAAA;IACrB,OACE,IAAI,CAAC/D,YAAY,CAACC,SAAS,EAAE+D,IAAI,EAAE,KAAK,EAAE,IAC1C,IAAI,CAAChE,YAAY,CAACI,SAAS,EAAE4D,IAAI,EAAE,KAAK,EAAE;EAE9C;EAEA;;;EAGAC,oBAAoBA,CAAA;IAClB,IAAI,CAACnD,wBAAwB,GAAG,IAAI;IAEpC;IACA,MAAMoD,MAAM,GAAG;;cAEL,IAAI,CAAClE,YAAY,CAACC,SAAS,IAAI,eAAe;eAC7C,IAAI,CAACD,YAAY,CAACG,gBAAgB,IAAI,yBAAyB;oBAC1D,IAAI,CAACS,eAAe,CAACkD,IAAI,CAAC,IAAI,CAAC;;;;CAIlD;IAEG;IACA,IAAI,CAACnE,SAAS,CAACwE,SAAS,CAAC,2BAA2B,EAAED,MAAM,CAAC,CAC1DE,IAAI,CACH/E,QAAQ,CAAC,MAAK;MACZ,IAAI,CAACyB,wBAAwB,GAAG,KAAK;IACvC,CAAC,CAAC,CACH,CACA6B,SAAS,CAAC;MACT0B,IAAI,EAAGC,QAAyB,IAAI;QAClC,IAAIA,QAAQ,IAAIA,QAAQ,CAACtB,OAAO,EAAE;UAChC;UACA,IAAI,CAAChD,YAAY,CAACE,YAAY,GAAGoE,QAAQ,CAACtB,OAAO;UACjD;UACA,IAAI,CAACjC,sBAAsB,GAAG,KAAK;UACnCgB,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;SACnD,MAAM;UACLD,OAAO,CAACG,KAAK,CAAC,iDAAiD,CAAC;;MAEpE,CAAC;MACDA,KAAK,EAAGA,KAAK,IAAI;QACfH,OAAO,CAACG,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD;QACA,IAAI,CAACqC,4BAA4B,EAAE;MACrC;KACD,CAAC;EACN;EAEA;;;EAGQA,4BAA4BA,CAAA;IAClC;IACA,IAAI,CAACvE,YAAY,CAACE,YAAY,GAAG,WAAW,IAAI,CAACF,YAAY,CAACC,SAAS,IAAI,iBAAiB;;WAErF,IAAI,CAACD,YAAY,CAACG,gBAAgB,IAAI,yBAAyB;;EAExE,IAAI,CAACS,eAAe,CAAC4D,MAAM,GAAG,CAAC,GAAG,4CAA4C,GAAG,IAAI,CAAC5D,eAAe,CAACkD,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;;;;;;;CAOtH;IACG;IACA,IAAI,CAAC/C,sBAAsB,GAAG,KAAK;EACrC;EAEA0D,SAASA,CAAA;IACP,IAAI,CAACzE,YAAY,CAACK,KAAK,GAAG,IAAI,CAACO,eAAe;IAC9C,IAAI,CAACZ,YAAY,CAACM,SAAS,GAAG,IAAI,CAACI,aAAa;IAChDqB,OAAO,CAACC,GAAG,CAAC,IAAI,CAACtB,aAAa,CAAC;IAE/BqB,OAAO,CAACC,GAAG,CAAC,IAAI,CAAChC,YAAY,CAAC;IAC9B,IAAI,CAACP,YAAY,CACdiF,cAAc,CAAC,IAAI,CAAC1E,YAAY,CAAC,CACjC2C,SAAS,CAAEI,GAAQ,IAAI;MACtBhB,OAAO,CAACC,GAAG,CAACe,GAAG,CAAC;MAChB,IAAI,IAAI,CAAClC,WAAW,CAAC,CAAC,CAAC,IAAI,QAAQ,EAAE;QACnC,IAAI,CAACnB,MAAM,CAACiF,QAAQ,CAAC,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;OAC7C,MACI;QACH,IAAI,CAACjF,MAAM,CAACiF,QAAQ,CAAC,CAAC,YAAY,EAAE,IAAI,CAACjE,aAAa,EAAE,QAAQ,CAAC,CAAC;;IAEtE,CAAC,CAAC;EACN;EAEA;;;;EAIAkE,iBAAiBA,CAAA;IACf;IACA,OACE,IAAI,CAAC5E,YAAY,CAACC,SAAS,EAAE+D,IAAI,EAAE,KAAK,EAAE,IAC1C,IAAI,CAAChE,YAAY,CAACE,YAAY,EAAE8D,IAAI,EAAE,KAAK,EAAE,IAC7C,IAAI,CAAChE,YAAY,CAACI,SAAS,EAAE4D,IAAI,EAAE,KAAK,EAAE,IAC1C,IAAI,CAACtD,aAAa,EAAEsD,IAAI,EAAE,KAAK,EAAE,IACjC,IAAI,CAAChE,YAAY,CAACG,gBAAgB,EAAE6D,IAAI,EAAE,KAAK,EAAE;EAErD;EAEA;;;EAGAa,SAASA,CAAA;IACP9C,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IAErC;IACA,IAAI,CAAC,IAAI,CAAC4C,iBAAiB,EAAE,EAAE;MAC7B7C,OAAO,CAACG,KAAK,CAAC,yBAAyB,CAAC;MACxC;;IAGF;IACA,IAAI,CAACZ,YAAY,GAAG,EAAE;IAEtB;IACA,MAAMwD,cAAc,GAAgB;MAClCC,MAAM,EAAE,OAAO;MACfC,OAAO,EAAE,mBAAmB,IAAI,CAAChF,YAAY,CAACC,SAAS,IAAI,IAAI,mCAAmC;MAClGgF,SAAS,EAAE,IAAIC,IAAI;KACpB;IACD,IAAI,CAAC5D,YAAY,CAAC6D,IAAI,CAACL,cAAc,CAAC;IAEtC;IACAnD,UAAU,CAAC,MAAK;MACd,IAAI,CAACN,eAAe,GAAG,IAAI;MAE3B;MACAM,UAAU,CAAC,MAAK;QACd,IAAI,CAACF,cAAc,EAAE;QACrB;QACAE,UAAU,CAAC,MAAK;UACd,IAAI,CAACF,cAAc,EAAE;QACvB,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,EAAE,EAAE,CAAC;EACR;EAEA;;;EAGA2D,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAAC7D,SAAS,CAACyC,IAAI,EAAE,EAAE;MAC1B;;IAGFjC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAACT,SAAS,CAAC;IAE/C;IACA,MAAM8D,WAAW,GAAgB;MAC/BN,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE,IAAI,CAACzD,SAAS,CAACyC,IAAI,EAAE;MAC9BiB,SAAS,EAAE,IAAIC,IAAI;KACpB;IACD,IAAI,CAAC5D,YAAY,CAAC6D,IAAI,CAACE,WAAW,CAAC;IAEnC;IACA,MAAMC,QAAQ,GAAG,IAAI,CAAC/D,SAAS,CAACyC,IAAI,EAAE;IAEtC;IACA,IAAI,CAACzC,SAAS,GAAG,EAAE;IAEnB;IACA,MAAMgE,cAAc,GAAgB;MAClCR,MAAM,EAAE,SAAS;MACjBC,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE,IAAIC,IAAI;KACpB;IACD,IAAI,CAAC5D,YAAY,CAAC6D,IAAI,CAACI,cAAc,CAAC;IAEtC;IACA5D,UAAU,CAAC,MAAK;MACd,IAAI,CAACF,cAAc,EAAE;MACrB;MACAE,UAAU,CAAC,MAAK;QACd,IAAI,CAACF,cAAc,EAAE;MACvB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,EAAE,GAAG,CAAC;IAEP;IACA,IAAI,CAACN,cAAc,GAAG,IAAI;IAE1B;IACA,IAAI,CAAC,IAAI,CAACnB,YAAY,CAACC,SAAS,EAAE;MAChC,IAAI,CAACqB,YAAY,CAACkE,GAAG,EAAE,CAAC,CAAC;MACzB,MAAMC,YAAY,GAAgB;QAChCV,MAAM,EAAE,OAAO;QACfC,OAAO,EAAE,+BAA+B;QACxCC,SAAS,EAAE,IAAIC,IAAI;OACpB;MACD,IAAI,CAAC5D,YAAY,CAAC6D,IAAI,CAACM,YAAY,CAAC;MACpC,IAAI,CAACtE,cAAc,GAAG,KAAK;MAC3B;;IAGF;IACAY,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAChC,YAAY,CAACC,SAAS,CAAC;IAC1D,IAAI,CAACR,YAAY,CAACoF,SAAS,CAACS,QAAQ,EAAE,IAAI,CAACtF,YAAY,CAAC,CACrD2C,SAAS,CAAC;MACT0B,IAAI,EAAGC,QAAyB,IAAI;QAClC;QACA,IAAI,CAAChD,YAAY,CAACkE,GAAG,EAAE;QAEvB;QACA,MAAME,YAAY,GAAgB;UAChCX,MAAM,EAAE,OAAO;UACfC,OAAO,EAAEV,QAAQ,CAACtB,OAAO,IAAI,wBAAwB;UACrDiC,SAAS,EAAE,IAAIC,IAAI;SACpB;QACD,IAAI,CAAC5D,YAAY,CAAC6D,IAAI,CAACO,YAAY,CAAC;QAEpC;QACA,IAAI,CAACvE,cAAc,GAAG,KAAK;QAC3B,IAAI,CAACC,cAAc,GAAG,IAAI;QAE1B;QACA,IAAI,CAACK,cAAc,EAAE;QACrBE,UAAU,CAAC,MAAK;UACd,IAAI,CAACF,cAAc,EAAE;QACvB,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MACDS,KAAK,EAAGA,KAAU,IAAI;QACpB;QACA,IAAI,CAACZ,YAAY,CAACkE,GAAG,EAAE;QAEvB;QACA,MAAMC,YAAY,GAAgB;UAChCV,MAAM,EAAE,OAAO;UACfC,OAAO,EAAE,SAAS,IAAI9C,KAAK,CAACc,OAAO,IAAI,wBAAwB,CAAC;UAChEiC,SAAS,EAAE,IAAIC,IAAI;SACpB;QACD,IAAI,CAAC5D,YAAY,CAAC6D,IAAI,CAACM,YAAY,CAAC;QAEpC;QACA,IAAI,CAACtE,cAAc,GAAG,KAAK;QAE3B;QACA,IAAI,CAACM,cAAc,EAAE;QACrBE,UAAU,CAAC,MAAK;UACd,IAAI,CAACF,cAAc,EAAE;QACvB,CAAC,EAAE,GAAG,CAAC;MACT;KACD,CAAC;EACN;;;;;;;;;;;;;;;;;cA3aCnD,SAAS;QAAAqH,IAAA,GAAC,kBAAkB;MAAA;;;;AADlBpG,wBAAwB,GAAAqG,UAAA,EAxBpCvH,SAAS,CAAC;EACTwH,QAAQ,EAAE,uBAAuB;EACjCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPtH,WAAW,EACXC,YAAY,EACZG,aAAa,EACbF,oBAAoB,EACpBI,cAAc,EACdD,aAAa,EACbF,kBAAkB,EAClBQ,kBAAkB,EAClBZ,UAAU,EACVc,wBAAwB,CACzB;EACD0G,QAAA,EAAAC,oBAAkD;;CAQnD,CAAC,C,EACW1G,wBAAwB,CA6apC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}