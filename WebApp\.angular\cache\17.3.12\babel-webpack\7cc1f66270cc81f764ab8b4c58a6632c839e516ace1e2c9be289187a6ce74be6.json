{"ast": null, "code": "import { createErrorClass } from './createErrorClass';\nexport const SequenceError = createErrorClass(_super => function SequenceErrorImpl(message) {\n  _super(this);\n  this.name = 'SequenceError';\n  this.message = message;\n});", "map": {"version": 3, "names": ["createErrorClass", "SequenceError", "_super", "SequenceErrorImpl", "message", "name"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/rxjs/dist/esm/internal/util/SequenceError.js"], "sourcesContent": ["import { createErrorClass } from './createErrorClass';\nexport const SequenceError = createErrorClass((_super) => function SequenceErrorImpl(message) {\n    _super(this);\n    this.name = 'SequenceError';\n    this.message = message;\n});\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,oBAAoB;AACrD,OAAO,MAAMC,aAAa,GAAGD,gBAAgB,CAAEE,MAAM,IAAK,SAASC,iBAAiBA,CAACC,OAAO,EAAE;EAC1FF,MAAM,CAAC,IAAI,CAAC;EACZ,IAAI,CAACG,IAAI,GAAG,eAAe;EAC3B,IAAI,CAACD,OAAO,GAAGA,OAAO;AAC1B,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}