{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nlet TogglingService = class TogglingService {\n  constructor() {\n    this.isNavbarOpen = true;\n  }\n  ngOnInit() {\n    const storedValue = localStorage.getItem('isNavbarOpen');\n    if (storedValue === 'true') {\n      this.isNavbarOpen = true;\n    } else if (storedValue === 'false') {\n      this.isNavbarOpen = false;\n    } else {\n      this.isNavbarOpen = true; // Default value if not set in localStorage\n    }\n    console.log('isNavbarOpen', this.isNavbarOpen);\n  }\n  /**\n   * Toggles the sidebar between states\n   * @param action Optional action to specify the toggle behavior:\n   *   - 'toggle': Cycle through states (collapsed -> narrow -> expanded -> collapsed)\n   *   - 'collapse': Collapse the sidebar\n   *   - 'expand': Expand the sidebar to full width\n   *   - 'narrow': Set the sidebar to narrow width\n   */\n  toggleNavbar(action) {\n    // If no action is specified, use the default toggle behavior\n    if (!action) {\n      action = this.isNavbarOpen ? 'collapse' : 'narrow';\n    }\n    // Update the isNavbarOpen state based on the action\n    if (action === 'collapse') {\n      this.isNavbarOpen = false;\n    } else {\n      this.isNavbarOpen = true;\n    }\n    localStorage.setItem('isNavbarOpen', this.isNavbarOpen.toString());\n    // Emit an event that the app component can listen to\n    const toggleEvent = new CustomEvent('sidebar-toggle', {\n      detail: {\n        action: action,\n        isOpen: this.isNavbarOpen\n      }\n    });\n    window.dispatchEvent(toggleEvent);\n    // Trigger a resize event to ensure the split layout updates\n    setTimeout(() => {\n      window.dispatchEvent(new Event('resize'));\n    }, 10);\n  }\n  /**\n   * Collapses the sidebar to its minimum width\n   */\n  collapseSidebar() {\n    this.toggleNavbar('collapse');\n  }\n  /**\n   * Expands the sidebar to its maximum width\n   */\n  expandSidebar() {\n    this.toggleNavbar('expand');\n  }\n  /**\n   * Sets the sidebar to its narrow width\n   */\n  narrowSidebar() {\n    this.toggleNavbar('narrow');\n  }\n  /**\n   * Cycles through the sidebar states\n   */\n  cycleSidebarState() {\n    this.toggleNavbar('toggle');\n  }\n  static {\n    this.ctorParameters = () => [];\n  }\n};\nTogglingService = __decorate([Injectable({\n  providedIn: 'root'\n})], TogglingService);\nexport { TogglingService };", "map": {"version": 3, "names": ["Injectable", "TogglingService", "constructor", "isNavbarOpen", "ngOnInit", "storedValue", "localStorage", "getItem", "console", "log", "toggle<PERSON><PERSON><PERSON>", "action", "setItem", "toString", "toggleEvent", "CustomEvent", "detail", "isOpen", "window", "dispatchEvent", "setTimeout", "Event", "collapseSidebar", "expandSidebar", "narrowSidebar", "cycleSidebarState", "__decorate", "providedIn"], "sources": ["C:\\Users\\<USER>\\source\\ai-hub\\WebApp\\src\\app\\toggling.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class TogglingService {\r\n\r\n  isNavbarOpen = true;\r\n\r\n  constructor() { }\r\n\r\n  ngOnInit() {\r\n    const storedValue = localStorage.getItem('isNavbarOpen');\r\n    if (storedValue === 'true') {\r\n      this.isNavbarOpen = true;\r\n    } else if (storedValue === 'false') {\r\n      this.isNavbarOpen = false;\r\n    } else {\r\n      this.isNavbarOpen = true; // Default value if not set in localStorage\r\n    }\r\n    console.log('isNavbarOpen', this.isNavbarOpen);\r\n  }\r\n\r\n  /**\r\n   * Toggles the sidebar between states\r\n   * @param action Optional action to specify the toggle behavior:\r\n   *   - 'toggle': Cycle through states (collapsed -> narrow -> expanded -> collapsed)\r\n   *   - 'collapse': Collapse the sidebar\r\n   *   - 'expand': Expand the sidebar to full width\r\n   *   - 'narrow': Set the sidebar to narrow width\r\n   */\r\n  toggleNavbar(action?: 'toggle' | 'collapse' | 'expand' | 'narrow') {\r\n    // If no action is specified, use the default toggle behavior\r\n    if (!action) {\r\n      action = this.isNavbarOpen ? 'collapse' : 'narrow';\r\n    }\r\n\r\n    // Update the isNavbarOpen state based on the action\r\n    if (action === 'collapse') {\r\n      this.isNavbarOpen = false;\r\n    } else {\r\n      this.isNavbarOpen = true;\r\n    }\r\n\r\n    localStorage.setItem('isNavbarOpen', this.isNavbarOpen.toString());\r\n\r\n    // Emit an event that the app component can listen to\r\n    const toggleEvent = new CustomEvent('sidebar-toggle', {\r\n      detail: {\r\n        action: action,\r\n        isOpen: this.isNavbarOpen\r\n      }\r\n    });\r\n    window.dispatchEvent(toggleEvent);\r\n\r\n    // Trigger a resize event to ensure the split layout updates\r\n    setTimeout(() => {\r\n      window.dispatchEvent(new Event('resize'));\r\n    }, 10);\r\n  }\r\n\r\n  /**\r\n   * Collapses the sidebar to its minimum width\r\n   */\r\n  collapseSidebar() {\r\n    this.toggleNavbar('collapse');\r\n  }\r\n\r\n  /**\r\n   * Expands the sidebar to its maximum width\r\n   */\r\n  expandSidebar() {\r\n    this.toggleNavbar('expand');\r\n  }\r\n\r\n  /**\r\n   * Sets the sidebar to its narrow width\r\n   */\r\n  narrowSidebar() {\r\n    this.toggleNavbar('narrow');\r\n  }\r\n\r\n  /**\r\n   * Cycles through the sidebar states\r\n   */\r\n  cycleSidebarState() {\r\n    this.toggleNavbar('toggle');\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,eAAe;AAKnC,IAAMC,eAAe,GAArB,MAAMA,eAAe;EAI1BC,YAAA;IAFA,KAAAC,YAAY,GAAG,IAAI;EAEH;EAEhBC,QAAQA,CAAA;IACN,MAAMC,WAAW,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;IACxD,IAAIF,WAAW,KAAK,MAAM,EAAE;MAC1B,IAAI,CAACF,YAAY,GAAG,IAAI;KACzB,MAAM,IAAIE,WAAW,KAAK,OAAO,EAAE;MAClC,IAAI,CAACF,YAAY,GAAG,KAAK;KAC1B,MAAM;MACL,IAAI,CAACA,YAAY,GAAG,IAAI,CAAC,CAAC;;IAE5BK,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE,IAAI,CAACN,YAAY,CAAC;EAChD;EAEA;;;;;;;;EAQAO,YAAYA,CAACC,MAAoD;IAC/D;IACA,IAAI,CAACA,MAAM,EAAE;MACXA,MAAM,GAAG,IAAI,CAACR,YAAY,GAAG,UAAU,GAAG,QAAQ;;IAGpD;IACA,IAAIQ,MAAM,KAAK,UAAU,EAAE;MACzB,IAAI,CAACR,YAAY,GAAG,KAAK;KAC1B,MAAM;MACL,IAAI,CAACA,YAAY,GAAG,IAAI;;IAG1BG,YAAY,CAACM,OAAO,CAAC,cAAc,EAAE,IAAI,CAACT,YAAY,CAACU,QAAQ,EAAE,CAAC;IAElE;IACA,MAAMC,WAAW,GAAG,IAAIC,WAAW,CAAC,gBAAgB,EAAE;MACpDC,MAAM,EAAE;QACNL,MAAM,EAAEA,MAAM;QACdM,MAAM,EAAE,IAAI,CAACd;;KAEhB,CAAC;IACFe,MAAM,CAACC,aAAa,CAACL,WAAW,CAAC;IAEjC;IACAM,UAAU,CAAC,MAAK;MACdF,MAAM,CAACC,aAAa,CAAC,IAAIE,KAAK,CAAC,QAAQ,CAAC,CAAC;IAC3C,CAAC,EAAE,EAAE,CAAC;EACR;EAEA;;;EAGAC,eAAeA,CAAA;IACb,IAAI,CAACZ,YAAY,CAAC,UAAU,CAAC;EAC/B;EAEA;;;EAGAa,aAAaA,CAAA;IACX,IAAI,CAACb,YAAY,CAAC,QAAQ,CAAC;EAC7B;EAEA;;;EAGAc,aAAaA,CAAA;IACX,IAAI,CAACd,YAAY,CAAC,QAAQ,CAAC;EAC7B;EAEA;;;EAGAe,iBAAiBA,CAAA;IACf,IAAI,CAACf,YAAY,CAAC,QAAQ,CAAC;EAC7B;;;;;AAlFWT,eAAe,GAAAyB,UAAA,EAH3B1B,UAAU,CAAC;EACV2B,UAAU,EAAE;CACb,CAAC,C,EACW1B,eAAe,CAmF3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}