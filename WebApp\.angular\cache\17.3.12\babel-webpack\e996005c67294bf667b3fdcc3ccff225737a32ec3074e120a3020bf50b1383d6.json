{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/source/ai-hub/WebApp/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\nimport { HandshakeProtocol } from \"./HandshakeProtocol\";\nimport { AbortError } from \"./Errors\";\nimport { MessageType } from \"./IHubProtocol\";\nimport { LogLevel } from \"./ILogger\";\nimport { Subject } from \"./Subject\";\nimport { Arg, getErrorString, Platform } from \"./Utils\";\nimport { MessageBuffer } from \"./MessageBuffer\";\nconst DEFAULT_TIMEOUT_IN_MS = 30 * 1000;\nconst DEFAULT_PING_INTERVAL_IN_MS = 15 * 1000;\nconst DEFAULT_STATEFUL_RECONNECT_BUFFER_SIZE = 100000;\n/** Describes the current state of the {@link HubConnection} to the server. */\nexport var HubConnectionState;\n(function (HubConnectionState) {\n  /** The hub connection is disconnected. */\n  HubConnectionState[\"Disconnected\"] = \"Disconnected\";\n  /** The hub connection is connecting. */\n  HubConnectionState[\"Connecting\"] = \"Connecting\";\n  /** The hub connection is connected. */\n  HubConnectionState[\"Connected\"] = \"Connected\";\n  /** The hub connection is disconnecting. */\n  HubConnectionState[\"Disconnecting\"] = \"Disconnecting\";\n  /** The hub connection is reconnecting. */\n  HubConnectionState[\"Reconnecting\"] = \"Reconnecting\";\n})(HubConnectionState || (HubConnectionState = {}));\n/** Represents a connection to a SignalR Hub. */\nexport class HubConnection {\n  /** @internal */\n  // Using a public static factory method means we can have a private constructor and an _internal_\n  // create method that can be used by HubConnectionBuilder. An \"internal\" constructor would just\n  // be stripped away and the '.d.ts' file would have no constructor, which is interpreted as a\n  // public parameter-less constructor.\n  static create(connection, logger, protocol, reconnectPolicy, serverTimeoutInMilliseconds, keepAliveIntervalInMilliseconds, statefulReconnectBufferSize) {\n    return new HubConnection(connection, logger, protocol, reconnectPolicy, serverTimeoutInMilliseconds, keepAliveIntervalInMilliseconds, statefulReconnectBufferSize);\n  }\n  constructor(connection, logger, protocol, reconnectPolicy, serverTimeoutInMilliseconds, keepAliveIntervalInMilliseconds, statefulReconnectBufferSize) {\n    this._nextKeepAlive = 0;\n    this._freezeEventListener = () => {\n      this._logger.log(LogLevel.Warning, \"The page is being frozen, this will likely lead to the connection being closed and messages being lost. For more information see the docs at https://learn.microsoft.com/aspnet/core/signalr/javascript-client#bsleep\");\n    };\n    Arg.isRequired(connection, \"connection\");\n    Arg.isRequired(logger, \"logger\");\n    Arg.isRequired(protocol, \"protocol\");\n    this.serverTimeoutInMilliseconds = serverTimeoutInMilliseconds !== null && serverTimeoutInMilliseconds !== void 0 ? serverTimeoutInMilliseconds : DEFAULT_TIMEOUT_IN_MS;\n    this.keepAliveIntervalInMilliseconds = keepAliveIntervalInMilliseconds !== null && keepAliveIntervalInMilliseconds !== void 0 ? keepAliveIntervalInMilliseconds : DEFAULT_PING_INTERVAL_IN_MS;\n    this._statefulReconnectBufferSize = statefulReconnectBufferSize !== null && statefulReconnectBufferSize !== void 0 ? statefulReconnectBufferSize : DEFAULT_STATEFUL_RECONNECT_BUFFER_SIZE;\n    this._logger = logger;\n    this._protocol = protocol;\n    this.connection = connection;\n    this._reconnectPolicy = reconnectPolicy;\n    this._handshakeProtocol = new HandshakeProtocol();\n    this.connection.onreceive = data => this._processIncomingData(data);\n    this.connection.onclose = error => this._connectionClosed(error);\n    this._callbacks = {};\n    this._methods = {};\n    this._closedCallbacks = [];\n    this._reconnectingCallbacks = [];\n    this._reconnectedCallbacks = [];\n    this._invocationId = 0;\n    this._receivedHandshakeResponse = false;\n    this._connectionState = HubConnectionState.Disconnected;\n    this._connectionStarted = false;\n    this._cachedPingMessage = this._protocol.writeMessage({\n      type: MessageType.Ping\n    });\n  }\n  /** Indicates the state of the {@link HubConnection} to the server. */\n  get state() {\n    return this._connectionState;\n  }\n  /** Represents the connection id of the {@link HubConnection} on the server. The connection id will be null when the connection is either\r\n   *  in the disconnected state or if the negotiation step was skipped.\r\n   */\n  get connectionId() {\n    return this.connection ? this.connection.connectionId || null : null;\n  }\n  /** Indicates the url of the {@link HubConnection} to the server. */\n  get baseUrl() {\n    return this.connection.baseUrl || \"\";\n  }\n  /**\r\n   * Sets a new url for the HubConnection. Note that the url can only be changed when the connection is in either the Disconnected or\r\n   * Reconnecting states.\r\n   * @param {string} url The url to connect to.\r\n   */\n  set baseUrl(url) {\n    if (this._connectionState !== HubConnectionState.Disconnected && this._connectionState !== HubConnectionState.Reconnecting) {\n      throw new Error(\"The HubConnection must be in the Disconnected or Reconnecting state to change the url.\");\n    }\n    if (!url) {\n      throw new Error(\"The HubConnection url must be a valid url.\");\n    }\n    this.connection.baseUrl = url;\n  }\n  /** Starts the connection.\r\n   *\r\n   * @returns {Promise<void>} A Promise that resolves when the connection has been successfully established, or rejects with an error.\r\n   */\n  start() {\n    this._startPromise = this._startWithStateTransitions();\n    return this._startPromise;\n  }\n  _startWithStateTransitions() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (_this._connectionState !== HubConnectionState.Disconnected) {\n        return Promise.reject(new Error(\"Cannot start a HubConnection that is not in the 'Disconnected' state.\"));\n      }\n      _this._connectionState = HubConnectionState.Connecting;\n      _this._logger.log(LogLevel.Debug, \"Starting HubConnection.\");\n      try {\n        yield _this._startInternal();\n        if (Platform.isBrowser) {\n          // Log when the browser freezes the tab so users know why their connection unexpectedly stopped working\n          window.document.addEventListener(\"freeze\", _this._freezeEventListener);\n        }\n        _this._connectionState = HubConnectionState.Connected;\n        _this._connectionStarted = true;\n        _this._logger.log(LogLevel.Debug, \"HubConnection connected successfully.\");\n      } catch (e) {\n        _this._connectionState = HubConnectionState.Disconnected;\n        _this._logger.log(LogLevel.Debug, `HubConnection failed to start successfully because of error '${e}'.`);\n        return Promise.reject(e);\n      }\n    })();\n  }\n  _startInternal() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      _this2._stopDuringStartError = undefined;\n      _this2._receivedHandshakeResponse = false;\n      // Set up the promise before any connection is (re)started otherwise it could race with received messages\n      const handshakePromise = new Promise((resolve, reject) => {\n        _this2._handshakeResolver = resolve;\n        _this2._handshakeRejecter = reject;\n      });\n      yield _this2.connection.start(_this2._protocol.transferFormat);\n      try {\n        let version = _this2._protocol.version;\n        if (!_this2.connection.features.reconnect) {\n          // Stateful Reconnect starts with HubProtocol version 2, newer clients connecting to older servers will fail to connect due to\n          // the handshake only supporting version 1, so we will try to send version 1 during the handshake to keep old servers working.\n          version = 1;\n        }\n        const handshakeRequest = {\n          protocol: _this2._protocol.name,\n          version\n        };\n        _this2._logger.log(LogLevel.Debug, \"Sending handshake request.\");\n        yield _this2._sendMessage(_this2._handshakeProtocol.writeHandshakeRequest(handshakeRequest));\n        _this2._logger.log(LogLevel.Information, `Using HubProtocol '${_this2._protocol.name}'.`);\n        // defensively cleanup timeout in case we receive a message from the server before we finish start\n        _this2._cleanupTimeout();\n        _this2._resetTimeoutPeriod();\n        _this2._resetKeepAliveInterval();\n        yield handshakePromise;\n        // It's important to check the stopDuringStartError instead of just relying on the handshakePromise\n        // being rejected on close, because this continuation can run after both the handshake completed successfully\n        // and the connection was closed.\n        if (_this2._stopDuringStartError) {\n          // It's important to throw instead of returning a rejected promise, because we don't want to allow any state\n          // transitions to occur between now and the calling code observing the exceptions. Returning a rejected promise\n          // will cause the calling continuation to get scheduled to run later.\n          // eslint-disable-next-line @typescript-eslint/no-throw-literal\n          throw _this2._stopDuringStartError;\n        }\n        const useStatefulReconnect = _this2.connection.features.reconnect || false;\n        if (useStatefulReconnect) {\n          _this2._messageBuffer = new MessageBuffer(_this2._protocol, _this2.connection, _this2._statefulReconnectBufferSize);\n          _this2.connection.features.disconnected = _this2._messageBuffer._disconnected.bind(_this2._messageBuffer);\n          _this2.connection.features.resend = () => {\n            if (_this2._messageBuffer) {\n              return _this2._messageBuffer._resend();\n            }\n          };\n        }\n        if (!_this2.connection.features.inherentKeepAlive) {\n          yield _this2._sendMessage(_this2._cachedPingMessage);\n        }\n      } catch (e) {\n        _this2._logger.log(LogLevel.Debug, `Hub handshake failed with error '${e}' during start(). Stopping HubConnection.`);\n        _this2._cleanupTimeout();\n        _this2._cleanupPingTimer();\n        // HttpConnection.stop() should not complete until after the onclose callback is invoked.\n        // This will transition the HubConnection to the disconnected state before HttpConnection.stop() completes.\n        yield _this2.connection.stop(e);\n        throw e;\n      }\n    })();\n  }\n  /** Stops the connection.\r\n   *\r\n   * @returns {Promise<void>} A Promise that resolves when the connection has been successfully terminated, or rejects with an error.\r\n   */\n  stop() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      // Capture the start promise before the connection might be restarted in an onclose callback.\n      const startPromise = _this3._startPromise;\n      _this3.connection.features.reconnect = false;\n      _this3._stopPromise = _this3._stopInternal();\n      yield _this3._stopPromise;\n      try {\n        // Awaiting undefined continues immediately\n        yield startPromise;\n      } catch (e) {\n        // This exception is returned to the user as a rejected Promise from the start method.\n      }\n    })();\n  }\n  _stopInternal(error) {\n    if (this._connectionState === HubConnectionState.Disconnected) {\n      this._logger.log(LogLevel.Debug, `Call to HubConnection.stop(${error}) ignored because it is already in the disconnected state.`);\n      return Promise.resolve();\n    }\n    if (this._connectionState === HubConnectionState.Disconnecting) {\n      this._logger.log(LogLevel.Debug, `Call to HttpConnection.stop(${error}) ignored because the connection is already in the disconnecting state.`);\n      return this._stopPromise;\n    }\n    const state = this._connectionState;\n    this._connectionState = HubConnectionState.Disconnecting;\n    this._logger.log(LogLevel.Debug, \"Stopping HubConnection.\");\n    if (this._reconnectDelayHandle) {\n      // We're in a reconnect delay which means the underlying connection is currently already stopped.\n      // Just clear the handle to stop the reconnect loop (which no one is waiting on thankfully) and\n      // fire the onclose callbacks.\n      this._logger.log(LogLevel.Debug, \"Connection stopped during reconnect delay. Done reconnecting.\");\n      clearTimeout(this._reconnectDelayHandle);\n      this._reconnectDelayHandle = undefined;\n      this._completeClose();\n      return Promise.resolve();\n    }\n    if (state === HubConnectionState.Connected) {\n      // eslint-disable-next-line @typescript-eslint/no-floating-promises\n      this._sendCloseMessage();\n    }\n    this._cleanupTimeout();\n    this._cleanupPingTimer();\n    this._stopDuringStartError = error || new AbortError(\"The connection was stopped before the hub handshake could complete.\");\n    // HttpConnection.stop() should not complete until after either HttpConnection.start() fails\n    // or the onclose callback is invoked. The onclose callback will transition the HubConnection\n    // to the disconnected state if need be before HttpConnection.stop() completes.\n    return this.connection.stop(error);\n  }\n  _sendCloseMessage() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        yield _this4._sendWithProtocol(_this4._createCloseMessage());\n      } catch {\n        // Ignore, this is a best effort attempt to let the server know the client closed gracefully.\n      }\n    })();\n  }\n  /** Invokes a streaming hub method on the server using the specified name and arguments.\r\n   *\r\n   * @typeparam T The type of the items returned by the server.\r\n   * @param {string} methodName The name of the server method to invoke.\r\n   * @param {any[]} args The arguments used to invoke the server method.\r\n   * @returns {IStreamResult<T>} An object that yields results from the server as they are received.\r\n   */\n  stream(methodName, ...args) {\n    const [streams, streamIds] = this._replaceStreamingParams(args);\n    const invocationDescriptor = this._createStreamInvocation(methodName, args, streamIds);\n    // eslint-disable-next-line prefer-const\n    let promiseQueue;\n    const subject = new Subject();\n    subject.cancelCallback = () => {\n      const cancelInvocation = this._createCancelInvocation(invocationDescriptor.invocationId);\n      delete this._callbacks[invocationDescriptor.invocationId];\n      return promiseQueue.then(() => {\n        return this._sendWithProtocol(cancelInvocation);\n      });\n    };\n    this._callbacks[invocationDescriptor.invocationId] = (invocationEvent, error) => {\n      if (error) {\n        subject.error(error);\n        return;\n      } else if (invocationEvent) {\n        // invocationEvent will not be null when an error is not passed to the callback\n        if (invocationEvent.type === MessageType.Completion) {\n          if (invocationEvent.error) {\n            subject.error(new Error(invocationEvent.error));\n          } else {\n            subject.complete();\n          }\n        } else {\n          subject.next(invocationEvent.item);\n        }\n      }\n    };\n    promiseQueue = this._sendWithProtocol(invocationDescriptor).catch(e => {\n      subject.error(e);\n      delete this._callbacks[invocationDescriptor.invocationId];\n    });\n    this._launchStreams(streams, promiseQueue);\n    return subject;\n  }\n  _sendMessage(message) {\n    this._resetKeepAliveInterval();\n    return this.connection.send(message);\n  }\n  /**\r\n   * Sends a js object to the server.\r\n   * @param message The js object to serialize and send.\r\n   */\n  _sendWithProtocol(message) {\n    if (this._messageBuffer) {\n      return this._messageBuffer._send(message);\n    } else {\n      return this._sendMessage(this._protocol.writeMessage(message));\n    }\n  }\n  /** Invokes a hub method on the server using the specified name and arguments. Does not wait for a response from the receiver.\r\n   *\r\n   * The Promise returned by this method resolves when the client has sent the invocation to the server. The server may still\r\n   * be processing the invocation.\r\n   *\r\n   * @param {string} methodName The name of the server method to invoke.\r\n   * @param {any[]} args The arguments used to invoke the server method.\r\n   * @returns {Promise<void>} A Promise that resolves when the invocation has been successfully sent, or rejects with an error.\r\n   */\n  send(methodName, ...args) {\n    const [streams, streamIds] = this._replaceStreamingParams(args);\n    const sendPromise = this._sendWithProtocol(this._createInvocation(methodName, args, true, streamIds));\n    this._launchStreams(streams, sendPromise);\n    return sendPromise;\n  }\n  /** Invokes a hub method on the server using the specified name and arguments.\r\n   *\r\n   * The Promise returned by this method resolves when the server indicates it has finished invoking the method. When the promise\r\n   * resolves, the server has finished invoking the method. If the server method returns a result, it is produced as the result of\r\n   * resolving the Promise.\r\n   *\r\n   * @typeparam T The expected return type.\r\n   * @param {string} methodName The name of the server method to invoke.\r\n   * @param {any[]} args The arguments used to invoke the server method.\r\n   * @returns {Promise<T>} A Promise that resolves with the result of the server method (if any), or rejects with an error.\r\n   */\n  invoke(methodName, ...args) {\n    const [streams, streamIds] = this._replaceStreamingParams(args);\n    const invocationDescriptor = this._createInvocation(methodName, args, false, streamIds);\n    const p = new Promise((resolve, reject) => {\n      // invocationId will always have a value for a non-blocking invocation\n      this._callbacks[invocationDescriptor.invocationId] = (invocationEvent, error) => {\n        if (error) {\n          reject(error);\n          return;\n        } else if (invocationEvent) {\n          // invocationEvent will not be null when an error is not passed to the callback\n          if (invocationEvent.type === MessageType.Completion) {\n            if (invocationEvent.error) {\n              reject(new Error(invocationEvent.error));\n            } else {\n              resolve(invocationEvent.result);\n            }\n          } else {\n            reject(new Error(`Unexpected message type: ${invocationEvent.type}`));\n          }\n        }\n      };\n      const promiseQueue = this._sendWithProtocol(invocationDescriptor).catch(e => {\n        reject(e);\n        // invocationId will always have a value for a non-blocking invocation\n        delete this._callbacks[invocationDescriptor.invocationId];\n      });\n      this._launchStreams(streams, promiseQueue);\n    });\n    return p;\n  }\n  on(methodName, newMethod) {\n    if (!methodName || !newMethod) {\n      return;\n    }\n    methodName = methodName.toLowerCase();\n    if (!this._methods[methodName]) {\n      this._methods[methodName] = [];\n    }\n    // Preventing adding the same handler multiple times.\n    if (this._methods[methodName].indexOf(newMethod) !== -1) {\n      return;\n    }\n    this._methods[methodName].push(newMethod);\n  }\n  off(methodName, method) {\n    if (!methodName) {\n      return;\n    }\n    methodName = methodName.toLowerCase();\n    const handlers = this._methods[methodName];\n    if (!handlers) {\n      return;\n    }\n    if (method) {\n      const removeIdx = handlers.indexOf(method);\n      if (removeIdx !== -1) {\n        handlers.splice(removeIdx, 1);\n        if (handlers.length === 0) {\n          delete this._methods[methodName];\n        }\n      }\n    } else {\n      delete this._methods[methodName];\n    }\n  }\n  /** Registers a handler that will be invoked when the connection is closed.\r\n   *\r\n   * @param {Function} callback The handler that will be invoked when the connection is closed. Optionally receives a single argument containing the error that caused the connection to close (if any).\r\n   */\n  onclose(callback) {\n    if (callback) {\n      this._closedCallbacks.push(callback);\n    }\n  }\n  /** Registers a handler that will be invoked when the connection starts reconnecting.\r\n   *\r\n   * @param {Function} callback The handler that will be invoked when the connection starts reconnecting. Optionally receives a single argument containing the error that caused the connection to start reconnecting (if any).\r\n   */\n  onreconnecting(callback) {\n    if (callback) {\n      this._reconnectingCallbacks.push(callback);\n    }\n  }\n  /** Registers a handler that will be invoked when the connection successfully reconnects.\r\n   *\r\n   * @param {Function} callback The handler that will be invoked when the connection successfully reconnects.\r\n   */\n  onreconnected(callback) {\n    if (callback) {\n      this._reconnectedCallbacks.push(callback);\n    }\n  }\n  _processIncomingData(data) {\n    this._cleanupTimeout();\n    if (!this._receivedHandshakeResponse) {\n      data = this._processHandshakeResponse(data);\n      this._receivedHandshakeResponse = true;\n    }\n    // Data may have all been read when processing handshake response\n    if (data) {\n      // Parse the messages\n      const messages = this._protocol.parseMessages(data, this._logger);\n      for (const message of messages) {\n        if (this._messageBuffer && !this._messageBuffer._shouldProcessMessage(message)) {\n          // Don't process the message, we are either waiting for a SequenceMessage or received a duplicate message\n          continue;\n        }\n        switch (message.type) {\n          case MessageType.Invocation:\n            this._invokeClientMethod(message).catch(e => {\n              this._logger.log(LogLevel.Error, `Invoke client method threw error: ${getErrorString(e)}`);\n            });\n            break;\n          case MessageType.StreamItem:\n          case MessageType.Completion:\n            {\n              const callback = this._callbacks[message.invocationId];\n              if (callback) {\n                if (message.type === MessageType.Completion) {\n                  delete this._callbacks[message.invocationId];\n                }\n                try {\n                  callback(message);\n                } catch (e) {\n                  this._logger.log(LogLevel.Error, `Stream callback threw error: ${getErrorString(e)}`);\n                }\n              }\n              break;\n            }\n          case MessageType.Ping:\n            // Don't care about pings\n            break;\n          case MessageType.Close:\n            {\n              this._logger.log(LogLevel.Information, \"Close message received from server.\");\n              const error = message.error ? new Error(\"Server returned an error on close: \" + message.error) : undefined;\n              if (message.allowReconnect === true) {\n                // It feels wrong not to await connection.stop() here, but processIncomingData is called as part of an onreceive callback which is not async,\n                // this is already the behavior for serverTimeout(), and HttpConnection.Stop() should catch and log all possible exceptions.\n                // eslint-disable-next-line @typescript-eslint/no-floating-promises\n                this.connection.stop(error);\n              } else {\n                // We cannot await stopInternal() here, but subsequent calls to stop() will await this if stopInternal() is still ongoing.\n                this._stopPromise = this._stopInternal(error);\n              }\n              break;\n            }\n          case MessageType.Ack:\n            if (this._messageBuffer) {\n              this._messageBuffer._ack(message);\n            }\n            break;\n          case MessageType.Sequence:\n            if (this._messageBuffer) {\n              this._messageBuffer._resetSequence(message);\n            }\n            break;\n          default:\n            this._logger.log(LogLevel.Warning, `Invalid message type: ${message.type}.`);\n            break;\n        }\n      }\n    }\n    this._resetTimeoutPeriod();\n  }\n  _processHandshakeResponse(data) {\n    let responseMessage;\n    let remainingData;\n    try {\n      [remainingData, responseMessage] = this._handshakeProtocol.parseHandshakeResponse(data);\n    } catch (e) {\n      const message = \"Error parsing handshake response: \" + e;\n      this._logger.log(LogLevel.Error, message);\n      const error = new Error(message);\n      this._handshakeRejecter(error);\n      throw error;\n    }\n    if (responseMessage.error) {\n      const message = \"Server returned handshake error: \" + responseMessage.error;\n      this._logger.log(LogLevel.Error, message);\n      const error = new Error(message);\n      this._handshakeRejecter(error);\n      throw error;\n    } else {\n      this._logger.log(LogLevel.Debug, \"Server handshake complete.\");\n    }\n    this._handshakeResolver();\n    return remainingData;\n  }\n  _resetKeepAliveInterval() {\n    if (this.connection.features.inherentKeepAlive) {\n      return;\n    }\n    // Set the time we want the next keep alive to be sent\n    // Timer will be setup on next message receive\n    this._nextKeepAlive = new Date().getTime() + this.keepAliveIntervalInMilliseconds;\n    this._cleanupPingTimer();\n  }\n  _resetTimeoutPeriod() {\n    var _this5 = this;\n    if (!this.connection.features || !this.connection.features.inherentKeepAlive) {\n      // Set the timeout timer\n      this._timeoutHandle = setTimeout(() => this.serverTimeout(), this.serverTimeoutInMilliseconds);\n      // Set keepAlive timer if there isn't one\n      if (this._pingServerHandle === undefined) {\n        let nextPing = this._nextKeepAlive - new Date().getTime();\n        if (nextPing < 0) {\n          nextPing = 0;\n        }\n        // The timer needs to be set from a networking callback to avoid Chrome timer throttling from causing timers to run once a minute\n        this._pingServerHandle = setTimeout( /*#__PURE__*/_asyncToGenerator(function* () {\n          if (_this5._connectionState === HubConnectionState.Connected) {\n            try {\n              yield _this5._sendMessage(_this5._cachedPingMessage);\n            } catch {\n              // We don't care about the error. It should be seen elsewhere in the client.\n              // The connection is probably in a bad or closed state now, cleanup the timer so it stops triggering\n              _this5._cleanupPingTimer();\n            }\n          }\n        }), nextPing);\n      }\n    }\n  }\n  // eslint-disable-next-line @typescript-eslint/naming-convention\n  serverTimeout() {\n    // The server hasn't talked to us in a while. It doesn't like us anymore ... :(\n    // Terminate the connection, but we don't need to wait on the promise. This could trigger reconnecting.\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    this.connection.stop(new Error(\"Server timeout elapsed without receiving a message from the server.\"));\n  }\n  _invokeClientMethod(invocationMessage) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      const methodName = invocationMessage.target.toLowerCase();\n      const methods = _this6._methods[methodName];\n      if (!methods) {\n        _this6._logger.log(LogLevel.Warning, `No client method with the name '${methodName}' found.`);\n        // No handlers provided by client but the server is expecting a response still, so we send an error\n        if (invocationMessage.invocationId) {\n          _this6._logger.log(LogLevel.Warning, `No result given for '${methodName}' method and invocation ID '${invocationMessage.invocationId}'.`);\n          yield _this6._sendWithProtocol(_this6._createCompletionMessage(invocationMessage.invocationId, \"Client didn't provide a result.\", null));\n        }\n        return;\n      }\n      // Avoid issues with handlers removing themselves thus modifying the list while iterating through it\n      const methodsCopy = methods.slice();\n      // Server expects a response\n      const expectsResponse = invocationMessage.invocationId ? true : false;\n      // We preserve the last result or exception but still call all handlers\n      let res;\n      let exception;\n      let completionMessage;\n      for (const m of methodsCopy) {\n        try {\n          const prevRes = res;\n          res = yield m.apply(_this6, invocationMessage.arguments);\n          if (expectsResponse && res && prevRes) {\n            _this6._logger.log(LogLevel.Error, `Multiple results provided for '${methodName}'. Sending error to server.`);\n            completionMessage = _this6._createCompletionMessage(invocationMessage.invocationId, `Client provided multiple results.`, null);\n          }\n          // Ignore exception if we got a result after, the exception will be logged\n          exception = undefined;\n        } catch (e) {\n          exception = e;\n          _this6._logger.log(LogLevel.Error, `A callback for the method '${methodName}' threw error '${e}'.`);\n        }\n      }\n      if (completionMessage) {\n        yield _this6._sendWithProtocol(completionMessage);\n      } else if (expectsResponse) {\n        // If there is an exception that means either no result was given or a handler after a result threw\n        if (exception) {\n          completionMessage = _this6._createCompletionMessage(invocationMessage.invocationId, `${exception}`, null);\n        } else if (res !== undefined) {\n          completionMessage = _this6._createCompletionMessage(invocationMessage.invocationId, null, res);\n        } else {\n          _this6._logger.log(LogLevel.Warning, `No result given for '${methodName}' method and invocation ID '${invocationMessage.invocationId}'.`);\n          // Client didn't provide a result or throw from a handler, server expects a response so we send an error\n          completionMessage = _this6._createCompletionMessage(invocationMessage.invocationId, \"Client didn't provide a result.\", null);\n        }\n        yield _this6._sendWithProtocol(completionMessage);\n      } else {\n        if (res) {\n          _this6._logger.log(LogLevel.Error, `Result given for '${methodName}' method but server is not expecting a result.`);\n        }\n      }\n    })();\n  }\n  _connectionClosed(error) {\n    this._logger.log(LogLevel.Debug, `HubConnection.connectionClosed(${error}) called while in state ${this._connectionState}.`);\n    // Triggering this.handshakeRejecter is insufficient because it could already be resolved without the continuation having run yet.\n    this._stopDuringStartError = this._stopDuringStartError || error || new AbortError(\"The underlying connection was closed before the hub handshake could complete.\");\n    // If the handshake is in progress, start will be waiting for the handshake promise, so we complete it.\n    // If it has already completed, this should just noop.\n    if (this._handshakeResolver) {\n      this._handshakeResolver();\n    }\n    this._cancelCallbacksWithError(error || new Error(\"Invocation canceled due to the underlying connection being closed.\"));\n    this._cleanupTimeout();\n    this._cleanupPingTimer();\n    if (this._connectionState === HubConnectionState.Disconnecting) {\n      this._completeClose(error);\n    } else if (this._connectionState === HubConnectionState.Connected && this._reconnectPolicy) {\n      // eslint-disable-next-line @typescript-eslint/no-floating-promises\n      this._reconnect(error);\n    } else if (this._connectionState === HubConnectionState.Connected) {\n      this._completeClose(error);\n    }\n    // If none of the above if conditions were true were called the HubConnection must be in either:\n    // 1. The Connecting state in which case the handshakeResolver will complete it and stopDuringStartError will fail it.\n    // 2. The Reconnecting state in which case the handshakeResolver will complete it and stopDuringStartError will fail the current reconnect attempt\n    //    and potentially continue the reconnect() loop.\n    // 3. The Disconnected state in which case we're already done.\n  }\n  _completeClose(error) {\n    if (this._connectionStarted) {\n      this._connectionState = HubConnectionState.Disconnected;\n      this._connectionStarted = false;\n      if (this._messageBuffer) {\n        this._messageBuffer._dispose(error !== null && error !== void 0 ? error : new Error(\"Connection closed.\"));\n        this._messageBuffer = undefined;\n      }\n      if (Platform.isBrowser) {\n        window.document.removeEventListener(\"freeze\", this._freezeEventListener);\n      }\n      try {\n        this._closedCallbacks.forEach(c => c.apply(this, [error]));\n      } catch (e) {\n        this._logger.log(LogLevel.Error, `An onclose callback called with error '${error}' threw error '${e}'.`);\n      }\n    }\n  }\n  _reconnect(error) {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      const reconnectStartTime = Date.now();\n      let previousReconnectAttempts = 0;\n      let retryError = error !== undefined ? error : new Error(\"Attempting to reconnect due to a unknown error.\");\n      let nextRetryDelay = _this7._getNextRetryDelay(previousReconnectAttempts++, 0, retryError);\n      if (nextRetryDelay === null) {\n        _this7._logger.log(LogLevel.Debug, \"Connection not reconnecting because the IRetryPolicy returned null on the first reconnect attempt.\");\n        _this7._completeClose(error);\n        return;\n      }\n      _this7._connectionState = HubConnectionState.Reconnecting;\n      if (error) {\n        _this7._logger.log(LogLevel.Information, `Connection reconnecting because of error '${error}'.`);\n      } else {\n        _this7._logger.log(LogLevel.Information, \"Connection reconnecting.\");\n      }\n      if (_this7._reconnectingCallbacks.length !== 0) {\n        try {\n          _this7._reconnectingCallbacks.forEach(c => c.apply(_this7, [error]));\n        } catch (e) {\n          _this7._logger.log(LogLevel.Error, `An onreconnecting callback called with error '${error}' threw error '${e}'.`);\n        }\n        // Exit early if an onreconnecting callback called connection.stop().\n        if (_this7._connectionState !== HubConnectionState.Reconnecting) {\n          _this7._logger.log(LogLevel.Debug, \"Connection left the reconnecting state in onreconnecting callback. Done reconnecting.\");\n          return;\n        }\n      }\n      while (nextRetryDelay !== null) {\n        _this7._logger.log(LogLevel.Information, `Reconnect attempt number ${previousReconnectAttempts} will start in ${nextRetryDelay} ms.`);\n        yield new Promise(resolve => {\n          _this7._reconnectDelayHandle = setTimeout(resolve, nextRetryDelay);\n        });\n        _this7._reconnectDelayHandle = undefined;\n        if (_this7._connectionState !== HubConnectionState.Reconnecting) {\n          _this7._logger.log(LogLevel.Debug, \"Connection left the reconnecting state during reconnect delay. Done reconnecting.\");\n          return;\n        }\n        try {\n          yield _this7._startInternal();\n          _this7._connectionState = HubConnectionState.Connected;\n          _this7._logger.log(LogLevel.Information, \"HubConnection reconnected successfully.\");\n          if (_this7._reconnectedCallbacks.length !== 0) {\n            try {\n              _this7._reconnectedCallbacks.forEach(c => c.apply(_this7, [_this7.connection.connectionId]));\n            } catch (e) {\n              _this7._logger.log(LogLevel.Error, `An onreconnected callback called with connectionId '${_this7.connection.connectionId}; threw error '${e}'.`);\n            }\n          }\n          return;\n        } catch (e) {\n          _this7._logger.log(LogLevel.Information, `Reconnect attempt failed because of error '${e}'.`);\n          if (_this7._connectionState !== HubConnectionState.Reconnecting) {\n            _this7._logger.log(LogLevel.Debug, `Connection moved to the '${_this7._connectionState}' from the reconnecting state during reconnect attempt. Done reconnecting.`);\n            // The TypeScript compiler thinks that connectionState must be Connected here. The TypeScript compiler is wrong.\n            if (_this7._connectionState === HubConnectionState.Disconnecting) {\n              _this7._completeClose();\n            }\n            return;\n          }\n          retryError = e instanceof Error ? e : new Error(e.toString());\n          nextRetryDelay = _this7._getNextRetryDelay(previousReconnectAttempts++, Date.now() - reconnectStartTime, retryError);\n        }\n      }\n      _this7._logger.log(LogLevel.Information, `Reconnect retries have been exhausted after ${Date.now() - reconnectStartTime} ms and ${previousReconnectAttempts} failed attempts. Connection disconnecting.`);\n      _this7._completeClose();\n    })();\n  }\n  _getNextRetryDelay(previousRetryCount, elapsedMilliseconds, retryReason) {\n    try {\n      return this._reconnectPolicy.nextRetryDelayInMilliseconds({\n        elapsedMilliseconds,\n        previousRetryCount,\n        retryReason\n      });\n    } catch (e) {\n      this._logger.log(LogLevel.Error, `IRetryPolicy.nextRetryDelayInMilliseconds(${previousRetryCount}, ${elapsedMilliseconds}) threw error '${e}'.`);\n      return null;\n    }\n  }\n  _cancelCallbacksWithError(error) {\n    const callbacks = this._callbacks;\n    this._callbacks = {};\n    Object.keys(callbacks).forEach(key => {\n      const callback = callbacks[key];\n      try {\n        callback(null, error);\n      } catch (e) {\n        this._logger.log(LogLevel.Error, `Stream 'error' callback called with '${error}' threw error: ${getErrorString(e)}`);\n      }\n    });\n  }\n  _cleanupPingTimer() {\n    if (this._pingServerHandle) {\n      clearTimeout(this._pingServerHandle);\n      this._pingServerHandle = undefined;\n    }\n  }\n  _cleanupTimeout() {\n    if (this._timeoutHandle) {\n      clearTimeout(this._timeoutHandle);\n    }\n  }\n  _createInvocation(methodName, args, nonblocking, streamIds) {\n    if (nonblocking) {\n      if (streamIds.length !== 0) {\n        return {\n          arguments: args,\n          streamIds,\n          target: methodName,\n          type: MessageType.Invocation\n        };\n      } else {\n        return {\n          arguments: args,\n          target: methodName,\n          type: MessageType.Invocation\n        };\n      }\n    } else {\n      const invocationId = this._invocationId;\n      this._invocationId++;\n      if (streamIds.length !== 0) {\n        return {\n          arguments: args,\n          invocationId: invocationId.toString(),\n          streamIds,\n          target: methodName,\n          type: MessageType.Invocation\n        };\n      } else {\n        return {\n          arguments: args,\n          invocationId: invocationId.toString(),\n          target: methodName,\n          type: MessageType.Invocation\n        };\n      }\n    }\n  }\n  _launchStreams(streams, promiseQueue) {\n    if (streams.length === 0) {\n      return;\n    }\n    // Synchronize stream data so they arrive in-order on the server\n    if (!promiseQueue) {\n      promiseQueue = Promise.resolve();\n    }\n    // We want to iterate over the keys, since the keys are the stream ids\n    // eslint-disable-next-line guard-for-in\n    for (const streamId in streams) {\n      streams[streamId].subscribe({\n        complete: () => {\n          promiseQueue = promiseQueue.then(() => this._sendWithProtocol(this._createCompletionMessage(streamId)));\n        },\n        error: err => {\n          let message;\n          if (err instanceof Error) {\n            message = err.message;\n          } else if (err && err.toString) {\n            message = err.toString();\n          } else {\n            message = \"Unknown error\";\n          }\n          promiseQueue = promiseQueue.then(() => this._sendWithProtocol(this._createCompletionMessage(streamId, message)));\n        },\n        next: item => {\n          promiseQueue = promiseQueue.then(() => this._sendWithProtocol(this._createStreamItemMessage(streamId, item)));\n        }\n      });\n    }\n  }\n  _replaceStreamingParams(args) {\n    const streams = [];\n    const streamIds = [];\n    for (let i = 0; i < args.length; i++) {\n      const argument = args[i];\n      if (this._isObservable(argument)) {\n        const streamId = this._invocationId;\n        this._invocationId++;\n        // Store the stream for later use\n        streams[streamId] = argument;\n        streamIds.push(streamId.toString());\n        // remove stream from args\n        args.splice(i, 1);\n      }\n    }\n    return [streams, streamIds];\n  }\n  _isObservable(arg) {\n    // This allows other stream implementations to just work (like rxjs)\n    return arg && arg.subscribe && typeof arg.subscribe === \"function\";\n  }\n  _createStreamInvocation(methodName, args, streamIds) {\n    const invocationId = this._invocationId;\n    this._invocationId++;\n    if (streamIds.length !== 0) {\n      return {\n        arguments: args,\n        invocationId: invocationId.toString(),\n        streamIds,\n        target: methodName,\n        type: MessageType.StreamInvocation\n      };\n    } else {\n      return {\n        arguments: args,\n        invocationId: invocationId.toString(),\n        target: methodName,\n        type: MessageType.StreamInvocation\n      };\n    }\n  }\n  _createCancelInvocation(id) {\n    return {\n      invocationId: id,\n      type: MessageType.CancelInvocation\n    };\n  }\n  _createStreamItemMessage(id, item) {\n    return {\n      invocationId: id,\n      item,\n      type: MessageType.StreamItem\n    };\n  }\n  _createCompletionMessage(id, error, result) {\n    if (error) {\n      return {\n        error,\n        invocationId: id,\n        type: MessageType.Completion\n      };\n    }\n    return {\n      invocationId: id,\n      result,\n      type: MessageType.Completion\n    };\n  }\n  _createCloseMessage() {\n    return {\n      type: MessageType.Close\n    };\n  }\n}", "map": {"version": 3, "names": ["HandshakeProtocol", "AbortError", "MessageType", "LogLevel", "Subject", "Arg", "getErrorString", "Platform", "MessageBuffer", "DEFAULT_TIMEOUT_IN_MS", "DEFAULT_PING_INTERVAL_IN_MS", "DEFAULT_STATEFUL_RECONNECT_BUFFER_SIZE", "HubConnectionState", "HubConnection", "create", "connection", "logger", "protocol", "reconnectPolicy", "serverTimeoutInMilliseconds", "keepAliveIntervalInMilliseconds", "statefulReconnectBufferSize", "constructor", "_nextKeepAlive", "_freezeEventListener", "_logger", "log", "Warning", "isRequired", "_statefulReconnectBufferSize", "_protocol", "_reconnectPolicy", "_handshakeProtocol", "onreceive", "data", "_processIncomingData", "onclose", "error", "_connectionClosed", "_callbacks", "_methods", "_closedCallbacks", "_reconnectingCallbacks", "_reconnectedCallbacks", "_invocationId", "_receivedHandshakeResponse", "_connectionState", "Disconnected", "_connectionStarted", "_cachedPingMessage", "writeMessage", "type", "<PERSON>", "state", "connectionId", "baseUrl", "url", "Reconnecting", "Error", "start", "_startPromise", "_startWithStateTransitions", "_this", "_asyncToGenerator", "Promise", "reject", "Connecting", "Debug", "_startInternal", "<PERSON><PERSON><PERSON><PERSON>", "window", "document", "addEventListener", "Connected", "e", "_this2", "_stopDuringStartError", "undefined", "handshakePromise", "resolve", "_handshakeResolver", "_handshake<PERSON><PERSON><PERSON><PERSON>", "transferFormat", "version", "features", "reconnect", "handshakeRequest", "name", "_sendMessage", "writeHandshakeRequest", "Information", "_cleanupTimeout", "_resetTimeoutPeriod", "_resetKeepAliveInterval", "useStatefulReconnect", "_messageBuffer", "disconnected", "_disconnected", "bind", "resend", "_resend", "inherentKeepAlive", "_cleanupPingTimer", "stop", "_this3", "startPromise", "_stopPromise", "_stopInternal", "Disconnecting", "_reconnectDelayHandle", "clearTimeout", "_completeClose", "_sendCloseMessage", "_this4", "_sendWithProtocol", "_createCloseMessage", "stream", "methodName", "args", "streams", "streamIds", "_replaceStreamingParams", "invocationDescriptor", "_createStreamInvocation", "promiseQueue", "subject", "cancelCallback", "cancelInvocation", "_createCancelInvocation", "invocationId", "then", "invocationEvent", "Completion", "complete", "next", "item", "catch", "_launchStreams", "message", "send", "_send", "sendPromise", "_createInvocation", "invoke", "p", "result", "on", "newMethod", "toLowerCase", "indexOf", "push", "off", "method", "handlers", "removeIdx", "splice", "length", "callback", "onreconnecting", "onreconnected", "_processHandshakeResponse", "messages", "parseMessages", "_shouldProcessMessage", "Invocation", "_invokeClientMethod", "StreamItem", "Close", "allowReconnect", "Ack", "_ack", "Sequence", "_resetSequence", "responseMessage", "remainingData", "parseHandshakeResponse", "Date", "getTime", "_this5", "_timeoutHandle", "setTimeout", "serverTimeout", "_pingServerHandle", "nextPing", "invocationMessage", "_this6", "target", "methods", "_createCompletionMessage", "methodsCopy", "slice", "expectsResponse", "res", "exception", "completionMessage", "m", "prevRes", "apply", "arguments", "_cancelCallbacksWithError", "_reconnect", "_dispose", "removeEventListener", "for<PERSON>ach", "c", "_this7", "reconnectStartTime", "now", "previousReconnectAttempts", "retryError", "nextRetryDelay", "_getNextRetryDelay", "toString", "previousRetryCount", "elapsedMilliseconds", "retryReason", "nextRetryDelayInMilliseconds", "callbacks", "Object", "keys", "key", "nonblocking", "streamId", "subscribe", "err", "_createStreamItemMessage", "i", "argument", "_isObservable", "arg", "StreamInvocation", "id", "CancelInvocation"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/@microsoft/signalr/dist/esm/HubConnection.js"], "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\nimport { HandshakeProtocol } from \"./HandshakeProtocol\";\r\nimport { AbortError } from \"./Errors\";\r\nimport { MessageType } from \"./IHubProtocol\";\r\nimport { LogLevel } from \"./ILogger\";\r\nimport { Subject } from \"./Subject\";\r\nimport { Arg, getErrorString, Platform } from \"./Utils\";\r\nimport { MessageBuffer } from \"./MessageBuffer\";\r\nconst DEFAULT_TIMEOUT_IN_MS = 30 * 1000;\r\nconst DEFAULT_PING_INTERVAL_IN_MS = 15 * 1000;\r\nconst DEFAULT_STATEFUL_RECONNECT_BUFFER_SIZE = 100000;\r\n/** Describes the current state of the {@link HubConnection} to the server. */\r\nexport var HubConnectionState;\r\n(function (HubConnectionState) {\r\n    /** The hub connection is disconnected. */\r\n    HubConnectionState[\"Disconnected\"] = \"Disconnected\";\r\n    /** The hub connection is connecting. */\r\n    HubConnectionState[\"Connecting\"] = \"Connecting\";\r\n    /** The hub connection is connected. */\r\n    HubConnectionState[\"Connected\"] = \"Connected\";\r\n    /** The hub connection is disconnecting. */\r\n    HubConnectionState[\"Disconnecting\"] = \"Disconnecting\";\r\n    /** The hub connection is reconnecting. */\r\n    HubConnectionState[\"Reconnecting\"] = \"Reconnecting\";\r\n})(HubConnectionState || (HubConnectionState = {}));\r\n/** Represents a connection to a SignalR Hub. */\r\nexport class HubConnection {\r\n    /** @internal */\r\n    // Using a public static factory method means we can have a private constructor and an _internal_\r\n    // create method that can be used by HubConnectionBuilder. An \"internal\" constructor would just\r\n    // be stripped away and the '.d.ts' file would have no constructor, which is interpreted as a\r\n    // public parameter-less constructor.\r\n    static create(connection, logger, protocol, reconnectPolicy, serverTimeoutInMilliseconds, keepAliveIntervalInMilliseconds, statefulReconnectBufferSize) {\r\n        return new HubConnection(connection, logger, protocol, reconnectPolicy, serverTimeoutInMilliseconds, keepAliveIntervalInMilliseconds, statefulReconnectBufferSize);\r\n    }\r\n    constructor(connection, logger, protocol, reconnectPolicy, serverTimeoutInMilliseconds, keepAliveIntervalInMilliseconds, statefulReconnectBufferSize) {\r\n        this._nextKeepAlive = 0;\r\n        this._freezeEventListener = () => {\r\n            this._logger.log(LogLevel.Warning, \"The page is being frozen, this will likely lead to the connection being closed and messages being lost. For more information see the docs at https://learn.microsoft.com/aspnet/core/signalr/javascript-client#bsleep\");\r\n        };\r\n        Arg.isRequired(connection, \"connection\");\r\n        Arg.isRequired(logger, \"logger\");\r\n        Arg.isRequired(protocol, \"protocol\");\r\n        this.serverTimeoutInMilliseconds = serverTimeoutInMilliseconds !== null && serverTimeoutInMilliseconds !== void 0 ? serverTimeoutInMilliseconds : DEFAULT_TIMEOUT_IN_MS;\r\n        this.keepAliveIntervalInMilliseconds = keepAliveIntervalInMilliseconds !== null && keepAliveIntervalInMilliseconds !== void 0 ? keepAliveIntervalInMilliseconds : DEFAULT_PING_INTERVAL_IN_MS;\r\n        this._statefulReconnectBufferSize = statefulReconnectBufferSize !== null && statefulReconnectBufferSize !== void 0 ? statefulReconnectBufferSize : DEFAULT_STATEFUL_RECONNECT_BUFFER_SIZE;\r\n        this._logger = logger;\r\n        this._protocol = protocol;\r\n        this.connection = connection;\r\n        this._reconnectPolicy = reconnectPolicy;\r\n        this._handshakeProtocol = new HandshakeProtocol();\r\n        this.connection.onreceive = (data) => this._processIncomingData(data);\r\n        this.connection.onclose = (error) => this._connectionClosed(error);\r\n        this._callbacks = {};\r\n        this._methods = {};\r\n        this._closedCallbacks = [];\r\n        this._reconnectingCallbacks = [];\r\n        this._reconnectedCallbacks = [];\r\n        this._invocationId = 0;\r\n        this._receivedHandshakeResponse = false;\r\n        this._connectionState = HubConnectionState.Disconnected;\r\n        this._connectionStarted = false;\r\n        this._cachedPingMessage = this._protocol.writeMessage({ type: MessageType.Ping });\r\n    }\r\n    /** Indicates the state of the {@link HubConnection} to the server. */\r\n    get state() {\r\n        return this._connectionState;\r\n    }\r\n    /** Represents the connection id of the {@link HubConnection} on the server. The connection id will be null when the connection is either\r\n     *  in the disconnected state or if the negotiation step was skipped.\r\n     */\r\n    get connectionId() {\r\n        return this.connection ? (this.connection.connectionId || null) : null;\r\n    }\r\n    /** Indicates the url of the {@link HubConnection} to the server. */\r\n    get baseUrl() {\r\n        return this.connection.baseUrl || \"\";\r\n    }\r\n    /**\r\n     * Sets a new url for the HubConnection. Note that the url can only be changed when the connection is in either the Disconnected or\r\n     * Reconnecting states.\r\n     * @param {string} url The url to connect to.\r\n     */\r\n    set baseUrl(url) {\r\n        if (this._connectionState !== HubConnectionState.Disconnected && this._connectionState !== HubConnectionState.Reconnecting) {\r\n            throw new Error(\"The HubConnection must be in the Disconnected or Reconnecting state to change the url.\");\r\n        }\r\n        if (!url) {\r\n            throw new Error(\"The HubConnection url must be a valid url.\");\r\n        }\r\n        this.connection.baseUrl = url;\r\n    }\r\n    /** Starts the connection.\r\n     *\r\n     * @returns {Promise<void>} A Promise that resolves when the connection has been successfully established, or rejects with an error.\r\n     */\r\n    start() {\r\n        this._startPromise = this._startWithStateTransitions();\r\n        return this._startPromise;\r\n    }\r\n    async _startWithStateTransitions() {\r\n        if (this._connectionState !== HubConnectionState.Disconnected) {\r\n            return Promise.reject(new Error(\"Cannot start a HubConnection that is not in the 'Disconnected' state.\"));\r\n        }\r\n        this._connectionState = HubConnectionState.Connecting;\r\n        this._logger.log(LogLevel.Debug, \"Starting HubConnection.\");\r\n        try {\r\n            await this._startInternal();\r\n            if (Platform.isBrowser) {\r\n                // Log when the browser freezes the tab so users know why their connection unexpectedly stopped working\r\n                window.document.addEventListener(\"freeze\", this._freezeEventListener);\r\n            }\r\n            this._connectionState = HubConnectionState.Connected;\r\n            this._connectionStarted = true;\r\n            this._logger.log(LogLevel.Debug, \"HubConnection connected successfully.\");\r\n        }\r\n        catch (e) {\r\n            this._connectionState = HubConnectionState.Disconnected;\r\n            this._logger.log(LogLevel.Debug, `HubConnection failed to start successfully because of error '${e}'.`);\r\n            return Promise.reject(e);\r\n        }\r\n    }\r\n    async _startInternal() {\r\n        this._stopDuringStartError = undefined;\r\n        this._receivedHandshakeResponse = false;\r\n        // Set up the promise before any connection is (re)started otherwise it could race with received messages\r\n        const handshakePromise = new Promise((resolve, reject) => {\r\n            this._handshakeResolver = resolve;\r\n            this._handshakeRejecter = reject;\r\n        });\r\n        await this.connection.start(this._protocol.transferFormat);\r\n        try {\r\n            let version = this._protocol.version;\r\n            if (!this.connection.features.reconnect) {\r\n                // Stateful Reconnect starts with HubProtocol version 2, newer clients connecting to older servers will fail to connect due to\r\n                // the handshake only supporting version 1, so we will try to send version 1 during the handshake to keep old servers working.\r\n                version = 1;\r\n            }\r\n            const handshakeRequest = {\r\n                protocol: this._protocol.name,\r\n                version,\r\n            };\r\n            this._logger.log(LogLevel.Debug, \"Sending handshake request.\");\r\n            await this._sendMessage(this._handshakeProtocol.writeHandshakeRequest(handshakeRequest));\r\n            this._logger.log(LogLevel.Information, `Using HubProtocol '${this._protocol.name}'.`);\r\n            // defensively cleanup timeout in case we receive a message from the server before we finish start\r\n            this._cleanupTimeout();\r\n            this._resetTimeoutPeriod();\r\n            this._resetKeepAliveInterval();\r\n            await handshakePromise;\r\n            // It's important to check the stopDuringStartError instead of just relying on the handshakePromise\r\n            // being rejected on close, because this continuation can run after both the handshake completed successfully\r\n            // and the connection was closed.\r\n            if (this._stopDuringStartError) {\r\n                // It's important to throw instead of returning a rejected promise, because we don't want to allow any state\r\n                // transitions to occur between now and the calling code observing the exceptions. Returning a rejected promise\r\n                // will cause the calling continuation to get scheduled to run later.\r\n                // eslint-disable-next-line @typescript-eslint/no-throw-literal\r\n                throw this._stopDuringStartError;\r\n            }\r\n            const useStatefulReconnect = this.connection.features.reconnect || false;\r\n            if (useStatefulReconnect) {\r\n                this._messageBuffer = new MessageBuffer(this._protocol, this.connection, this._statefulReconnectBufferSize);\r\n                this.connection.features.disconnected = this._messageBuffer._disconnected.bind(this._messageBuffer);\r\n                this.connection.features.resend = () => {\r\n                    if (this._messageBuffer) {\r\n                        return this._messageBuffer._resend();\r\n                    }\r\n                };\r\n            }\r\n            if (!this.connection.features.inherentKeepAlive) {\r\n                await this._sendMessage(this._cachedPingMessage);\r\n            }\r\n        }\r\n        catch (e) {\r\n            this._logger.log(LogLevel.Debug, `Hub handshake failed with error '${e}' during start(). Stopping HubConnection.`);\r\n            this._cleanupTimeout();\r\n            this._cleanupPingTimer();\r\n            // HttpConnection.stop() should not complete until after the onclose callback is invoked.\r\n            // This will transition the HubConnection to the disconnected state before HttpConnection.stop() completes.\r\n            await this.connection.stop(e);\r\n            throw e;\r\n        }\r\n    }\r\n    /** Stops the connection.\r\n     *\r\n     * @returns {Promise<void>} A Promise that resolves when the connection has been successfully terminated, or rejects with an error.\r\n     */\r\n    async stop() {\r\n        // Capture the start promise before the connection might be restarted in an onclose callback.\r\n        const startPromise = this._startPromise;\r\n        this.connection.features.reconnect = false;\r\n        this._stopPromise = this._stopInternal();\r\n        await this._stopPromise;\r\n        try {\r\n            // Awaiting undefined continues immediately\r\n            await startPromise;\r\n        }\r\n        catch (e) {\r\n            // This exception is returned to the user as a rejected Promise from the start method.\r\n        }\r\n    }\r\n    _stopInternal(error) {\r\n        if (this._connectionState === HubConnectionState.Disconnected) {\r\n            this._logger.log(LogLevel.Debug, `Call to HubConnection.stop(${error}) ignored because it is already in the disconnected state.`);\r\n            return Promise.resolve();\r\n        }\r\n        if (this._connectionState === HubConnectionState.Disconnecting) {\r\n            this._logger.log(LogLevel.Debug, `Call to HttpConnection.stop(${error}) ignored because the connection is already in the disconnecting state.`);\r\n            return this._stopPromise;\r\n        }\r\n        const state = this._connectionState;\r\n        this._connectionState = HubConnectionState.Disconnecting;\r\n        this._logger.log(LogLevel.Debug, \"Stopping HubConnection.\");\r\n        if (this._reconnectDelayHandle) {\r\n            // We're in a reconnect delay which means the underlying connection is currently already stopped.\r\n            // Just clear the handle to stop the reconnect loop (which no one is waiting on thankfully) and\r\n            // fire the onclose callbacks.\r\n            this._logger.log(LogLevel.Debug, \"Connection stopped during reconnect delay. Done reconnecting.\");\r\n            clearTimeout(this._reconnectDelayHandle);\r\n            this._reconnectDelayHandle = undefined;\r\n            this._completeClose();\r\n            return Promise.resolve();\r\n        }\r\n        if (state === HubConnectionState.Connected) {\r\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n            this._sendCloseMessage();\r\n        }\r\n        this._cleanupTimeout();\r\n        this._cleanupPingTimer();\r\n        this._stopDuringStartError = error || new AbortError(\"The connection was stopped before the hub handshake could complete.\");\r\n        // HttpConnection.stop() should not complete until after either HttpConnection.start() fails\r\n        // or the onclose callback is invoked. The onclose callback will transition the HubConnection\r\n        // to the disconnected state if need be before HttpConnection.stop() completes.\r\n        return this.connection.stop(error);\r\n    }\r\n    async _sendCloseMessage() {\r\n        try {\r\n            await this._sendWithProtocol(this._createCloseMessage());\r\n        }\r\n        catch {\r\n            // Ignore, this is a best effort attempt to let the server know the client closed gracefully.\r\n        }\r\n    }\r\n    /** Invokes a streaming hub method on the server using the specified name and arguments.\r\n     *\r\n     * @typeparam T The type of the items returned by the server.\r\n     * @param {string} methodName The name of the server method to invoke.\r\n     * @param {any[]} args The arguments used to invoke the server method.\r\n     * @returns {IStreamResult<T>} An object that yields results from the server as they are received.\r\n     */\r\n    stream(methodName, ...args) {\r\n        const [streams, streamIds] = this._replaceStreamingParams(args);\r\n        const invocationDescriptor = this._createStreamInvocation(methodName, args, streamIds);\r\n        // eslint-disable-next-line prefer-const\r\n        let promiseQueue;\r\n        const subject = new Subject();\r\n        subject.cancelCallback = () => {\r\n            const cancelInvocation = this._createCancelInvocation(invocationDescriptor.invocationId);\r\n            delete this._callbacks[invocationDescriptor.invocationId];\r\n            return promiseQueue.then(() => {\r\n                return this._sendWithProtocol(cancelInvocation);\r\n            });\r\n        };\r\n        this._callbacks[invocationDescriptor.invocationId] = (invocationEvent, error) => {\r\n            if (error) {\r\n                subject.error(error);\r\n                return;\r\n            }\r\n            else if (invocationEvent) {\r\n                // invocationEvent will not be null when an error is not passed to the callback\r\n                if (invocationEvent.type === MessageType.Completion) {\r\n                    if (invocationEvent.error) {\r\n                        subject.error(new Error(invocationEvent.error));\r\n                    }\r\n                    else {\r\n                        subject.complete();\r\n                    }\r\n                }\r\n                else {\r\n                    subject.next((invocationEvent.item));\r\n                }\r\n            }\r\n        };\r\n        promiseQueue = this._sendWithProtocol(invocationDescriptor)\r\n            .catch((e) => {\r\n            subject.error(e);\r\n            delete this._callbacks[invocationDescriptor.invocationId];\r\n        });\r\n        this._launchStreams(streams, promiseQueue);\r\n        return subject;\r\n    }\r\n    _sendMessage(message) {\r\n        this._resetKeepAliveInterval();\r\n        return this.connection.send(message);\r\n    }\r\n    /**\r\n     * Sends a js object to the server.\r\n     * @param message The js object to serialize and send.\r\n     */\r\n    _sendWithProtocol(message) {\r\n        if (this._messageBuffer) {\r\n            return this._messageBuffer._send(message);\r\n        }\r\n        else {\r\n            return this._sendMessage(this._protocol.writeMessage(message));\r\n        }\r\n    }\r\n    /** Invokes a hub method on the server using the specified name and arguments. Does not wait for a response from the receiver.\r\n     *\r\n     * The Promise returned by this method resolves when the client has sent the invocation to the server. The server may still\r\n     * be processing the invocation.\r\n     *\r\n     * @param {string} methodName The name of the server method to invoke.\r\n     * @param {any[]} args The arguments used to invoke the server method.\r\n     * @returns {Promise<void>} A Promise that resolves when the invocation has been successfully sent, or rejects with an error.\r\n     */\r\n    send(methodName, ...args) {\r\n        const [streams, streamIds] = this._replaceStreamingParams(args);\r\n        const sendPromise = this._sendWithProtocol(this._createInvocation(methodName, args, true, streamIds));\r\n        this._launchStreams(streams, sendPromise);\r\n        return sendPromise;\r\n    }\r\n    /** Invokes a hub method on the server using the specified name and arguments.\r\n     *\r\n     * The Promise returned by this method resolves when the server indicates it has finished invoking the method. When the promise\r\n     * resolves, the server has finished invoking the method. If the server method returns a result, it is produced as the result of\r\n     * resolving the Promise.\r\n     *\r\n     * @typeparam T The expected return type.\r\n     * @param {string} methodName The name of the server method to invoke.\r\n     * @param {any[]} args The arguments used to invoke the server method.\r\n     * @returns {Promise<T>} A Promise that resolves with the result of the server method (if any), or rejects with an error.\r\n     */\r\n    invoke(methodName, ...args) {\r\n        const [streams, streamIds] = this._replaceStreamingParams(args);\r\n        const invocationDescriptor = this._createInvocation(methodName, args, false, streamIds);\r\n        const p = new Promise((resolve, reject) => {\r\n            // invocationId will always have a value for a non-blocking invocation\r\n            this._callbacks[invocationDescriptor.invocationId] = (invocationEvent, error) => {\r\n                if (error) {\r\n                    reject(error);\r\n                    return;\r\n                }\r\n                else if (invocationEvent) {\r\n                    // invocationEvent will not be null when an error is not passed to the callback\r\n                    if (invocationEvent.type === MessageType.Completion) {\r\n                        if (invocationEvent.error) {\r\n                            reject(new Error(invocationEvent.error));\r\n                        }\r\n                        else {\r\n                            resolve(invocationEvent.result);\r\n                        }\r\n                    }\r\n                    else {\r\n                        reject(new Error(`Unexpected message type: ${invocationEvent.type}`));\r\n                    }\r\n                }\r\n            };\r\n            const promiseQueue = this._sendWithProtocol(invocationDescriptor)\r\n                .catch((e) => {\r\n                reject(e);\r\n                // invocationId will always have a value for a non-blocking invocation\r\n                delete this._callbacks[invocationDescriptor.invocationId];\r\n            });\r\n            this._launchStreams(streams, promiseQueue);\r\n        });\r\n        return p;\r\n    }\r\n    on(methodName, newMethod) {\r\n        if (!methodName || !newMethod) {\r\n            return;\r\n        }\r\n        methodName = methodName.toLowerCase();\r\n        if (!this._methods[methodName]) {\r\n            this._methods[methodName] = [];\r\n        }\r\n        // Preventing adding the same handler multiple times.\r\n        if (this._methods[methodName].indexOf(newMethod) !== -1) {\r\n            return;\r\n        }\r\n        this._methods[methodName].push(newMethod);\r\n    }\r\n    off(methodName, method) {\r\n        if (!methodName) {\r\n            return;\r\n        }\r\n        methodName = methodName.toLowerCase();\r\n        const handlers = this._methods[methodName];\r\n        if (!handlers) {\r\n            return;\r\n        }\r\n        if (method) {\r\n            const removeIdx = handlers.indexOf(method);\r\n            if (removeIdx !== -1) {\r\n                handlers.splice(removeIdx, 1);\r\n                if (handlers.length === 0) {\r\n                    delete this._methods[methodName];\r\n                }\r\n            }\r\n        }\r\n        else {\r\n            delete this._methods[methodName];\r\n        }\r\n    }\r\n    /** Registers a handler that will be invoked when the connection is closed.\r\n     *\r\n     * @param {Function} callback The handler that will be invoked when the connection is closed. Optionally receives a single argument containing the error that caused the connection to close (if any).\r\n     */\r\n    onclose(callback) {\r\n        if (callback) {\r\n            this._closedCallbacks.push(callback);\r\n        }\r\n    }\r\n    /** Registers a handler that will be invoked when the connection starts reconnecting.\r\n     *\r\n     * @param {Function} callback The handler that will be invoked when the connection starts reconnecting. Optionally receives a single argument containing the error that caused the connection to start reconnecting (if any).\r\n     */\r\n    onreconnecting(callback) {\r\n        if (callback) {\r\n            this._reconnectingCallbacks.push(callback);\r\n        }\r\n    }\r\n    /** Registers a handler that will be invoked when the connection successfully reconnects.\r\n     *\r\n     * @param {Function} callback The handler that will be invoked when the connection successfully reconnects.\r\n     */\r\n    onreconnected(callback) {\r\n        if (callback) {\r\n            this._reconnectedCallbacks.push(callback);\r\n        }\r\n    }\r\n    _processIncomingData(data) {\r\n        this._cleanupTimeout();\r\n        if (!this._receivedHandshakeResponse) {\r\n            data = this._processHandshakeResponse(data);\r\n            this._receivedHandshakeResponse = true;\r\n        }\r\n        // Data may have all been read when processing handshake response\r\n        if (data) {\r\n            // Parse the messages\r\n            const messages = this._protocol.parseMessages(data, this._logger);\r\n            for (const message of messages) {\r\n                if (this._messageBuffer && !this._messageBuffer._shouldProcessMessage(message)) {\r\n                    // Don't process the message, we are either waiting for a SequenceMessage or received a duplicate message\r\n                    continue;\r\n                }\r\n                switch (message.type) {\r\n                    case MessageType.Invocation:\r\n                        this._invokeClientMethod(message)\r\n                            .catch((e) => {\r\n                            this._logger.log(LogLevel.Error, `Invoke client method threw error: ${getErrorString(e)}`);\r\n                        });\r\n                        break;\r\n                    case MessageType.StreamItem:\r\n                    case MessageType.Completion: {\r\n                        const callback = this._callbacks[message.invocationId];\r\n                        if (callback) {\r\n                            if (message.type === MessageType.Completion) {\r\n                                delete this._callbacks[message.invocationId];\r\n                            }\r\n                            try {\r\n                                callback(message);\r\n                            }\r\n                            catch (e) {\r\n                                this._logger.log(LogLevel.Error, `Stream callback threw error: ${getErrorString(e)}`);\r\n                            }\r\n                        }\r\n                        break;\r\n                    }\r\n                    case MessageType.Ping:\r\n                        // Don't care about pings\r\n                        break;\r\n                    case MessageType.Close: {\r\n                        this._logger.log(LogLevel.Information, \"Close message received from server.\");\r\n                        const error = message.error ? new Error(\"Server returned an error on close: \" + message.error) : undefined;\r\n                        if (message.allowReconnect === true) {\r\n                            // It feels wrong not to await connection.stop() here, but processIncomingData is called as part of an onreceive callback which is not async,\r\n                            // this is already the behavior for serverTimeout(), and HttpConnection.Stop() should catch and log all possible exceptions.\r\n                            // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n                            this.connection.stop(error);\r\n                        }\r\n                        else {\r\n                            // We cannot await stopInternal() here, but subsequent calls to stop() will await this if stopInternal() is still ongoing.\r\n                            this._stopPromise = this._stopInternal(error);\r\n                        }\r\n                        break;\r\n                    }\r\n                    case MessageType.Ack:\r\n                        if (this._messageBuffer) {\r\n                            this._messageBuffer._ack(message);\r\n                        }\r\n                        break;\r\n                    case MessageType.Sequence:\r\n                        if (this._messageBuffer) {\r\n                            this._messageBuffer._resetSequence(message);\r\n                        }\r\n                        break;\r\n                    default:\r\n                        this._logger.log(LogLevel.Warning, `Invalid message type: ${message.type}.`);\r\n                        break;\r\n                }\r\n            }\r\n        }\r\n        this._resetTimeoutPeriod();\r\n    }\r\n    _processHandshakeResponse(data) {\r\n        let responseMessage;\r\n        let remainingData;\r\n        try {\r\n            [remainingData, responseMessage] = this._handshakeProtocol.parseHandshakeResponse(data);\r\n        }\r\n        catch (e) {\r\n            const message = \"Error parsing handshake response: \" + e;\r\n            this._logger.log(LogLevel.Error, message);\r\n            const error = new Error(message);\r\n            this._handshakeRejecter(error);\r\n            throw error;\r\n        }\r\n        if (responseMessage.error) {\r\n            const message = \"Server returned handshake error: \" + responseMessage.error;\r\n            this._logger.log(LogLevel.Error, message);\r\n            const error = new Error(message);\r\n            this._handshakeRejecter(error);\r\n            throw error;\r\n        }\r\n        else {\r\n            this._logger.log(LogLevel.Debug, \"Server handshake complete.\");\r\n        }\r\n        this._handshakeResolver();\r\n        return remainingData;\r\n    }\r\n    _resetKeepAliveInterval() {\r\n        if (this.connection.features.inherentKeepAlive) {\r\n            return;\r\n        }\r\n        // Set the time we want the next keep alive to be sent\r\n        // Timer will be setup on next message receive\r\n        this._nextKeepAlive = new Date().getTime() + this.keepAliveIntervalInMilliseconds;\r\n        this._cleanupPingTimer();\r\n    }\r\n    _resetTimeoutPeriod() {\r\n        if (!this.connection.features || !this.connection.features.inherentKeepAlive) {\r\n            // Set the timeout timer\r\n            this._timeoutHandle = setTimeout(() => this.serverTimeout(), this.serverTimeoutInMilliseconds);\r\n            // Set keepAlive timer if there isn't one\r\n            if (this._pingServerHandle === undefined) {\r\n                let nextPing = this._nextKeepAlive - new Date().getTime();\r\n                if (nextPing < 0) {\r\n                    nextPing = 0;\r\n                }\r\n                // The timer needs to be set from a networking callback to avoid Chrome timer throttling from causing timers to run once a minute\r\n                this._pingServerHandle = setTimeout(async () => {\r\n                    if (this._connectionState === HubConnectionState.Connected) {\r\n                        try {\r\n                            await this._sendMessage(this._cachedPingMessage);\r\n                        }\r\n                        catch {\r\n                            // We don't care about the error. It should be seen elsewhere in the client.\r\n                            // The connection is probably in a bad or closed state now, cleanup the timer so it stops triggering\r\n                            this._cleanupPingTimer();\r\n                        }\r\n                    }\r\n                }, nextPing);\r\n            }\r\n        }\r\n    }\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    serverTimeout() {\r\n        // The server hasn't talked to us in a while. It doesn't like us anymore ... :(\r\n        // Terminate the connection, but we don't need to wait on the promise. This could trigger reconnecting.\r\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n        this.connection.stop(new Error(\"Server timeout elapsed without receiving a message from the server.\"));\r\n    }\r\n    async _invokeClientMethod(invocationMessage) {\r\n        const methodName = invocationMessage.target.toLowerCase();\r\n        const methods = this._methods[methodName];\r\n        if (!methods) {\r\n            this._logger.log(LogLevel.Warning, `No client method with the name '${methodName}' found.`);\r\n            // No handlers provided by client but the server is expecting a response still, so we send an error\r\n            if (invocationMessage.invocationId) {\r\n                this._logger.log(LogLevel.Warning, `No result given for '${methodName}' method and invocation ID '${invocationMessage.invocationId}'.`);\r\n                await this._sendWithProtocol(this._createCompletionMessage(invocationMessage.invocationId, \"Client didn't provide a result.\", null));\r\n            }\r\n            return;\r\n        }\r\n        // Avoid issues with handlers removing themselves thus modifying the list while iterating through it\r\n        const methodsCopy = methods.slice();\r\n        // Server expects a response\r\n        const expectsResponse = invocationMessage.invocationId ? true : false;\r\n        // We preserve the last result or exception but still call all handlers\r\n        let res;\r\n        let exception;\r\n        let completionMessage;\r\n        for (const m of methodsCopy) {\r\n            try {\r\n                const prevRes = res;\r\n                res = await m.apply(this, invocationMessage.arguments);\r\n                if (expectsResponse && res && prevRes) {\r\n                    this._logger.log(LogLevel.Error, `Multiple results provided for '${methodName}'. Sending error to server.`);\r\n                    completionMessage = this._createCompletionMessage(invocationMessage.invocationId, `Client provided multiple results.`, null);\r\n                }\r\n                // Ignore exception if we got a result after, the exception will be logged\r\n                exception = undefined;\r\n            }\r\n            catch (e) {\r\n                exception = e;\r\n                this._logger.log(LogLevel.Error, `A callback for the method '${methodName}' threw error '${e}'.`);\r\n            }\r\n        }\r\n        if (completionMessage) {\r\n            await this._sendWithProtocol(completionMessage);\r\n        }\r\n        else if (expectsResponse) {\r\n            // If there is an exception that means either no result was given or a handler after a result threw\r\n            if (exception) {\r\n                completionMessage = this._createCompletionMessage(invocationMessage.invocationId, `${exception}`, null);\r\n            }\r\n            else if (res !== undefined) {\r\n                completionMessage = this._createCompletionMessage(invocationMessage.invocationId, null, res);\r\n            }\r\n            else {\r\n                this._logger.log(LogLevel.Warning, `No result given for '${methodName}' method and invocation ID '${invocationMessage.invocationId}'.`);\r\n                // Client didn't provide a result or throw from a handler, server expects a response so we send an error\r\n                completionMessage = this._createCompletionMessage(invocationMessage.invocationId, \"Client didn't provide a result.\", null);\r\n            }\r\n            await this._sendWithProtocol(completionMessage);\r\n        }\r\n        else {\r\n            if (res) {\r\n                this._logger.log(LogLevel.Error, `Result given for '${methodName}' method but server is not expecting a result.`);\r\n            }\r\n        }\r\n    }\r\n    _connectionClosed(error) {\r\n        this._logger.log(LogLevel.Debug, `HubConnection.connectionClosed(${error}) called while in state ${this._connectionState}.`);\r\n        // Triggering this.handshakeRejecter is insufficient because it could already be resolved without the continuation having run yet.\r\n        this._stopDuringStartError = this._stopDuringStartError || error || new AbortError(\"The underlying connection was closed before the hub handshake could complete.\");\r\n        // If the handshake is in progress, start will be waiting for the handshake promise, so we complete it.\r\n        // If it has already completed, this should just noop.\r\n        if (this._handshakeResolver) {\r\n            this._handshakeResolver();\r\n        }\r\n        this._cancelCallbacksWithError(error || new Error(\"Invocation canceled due to the underlying connection being closed.\"));\r\n        this._cleanupTimeout();\r\n        this._cleanupPingTimer();\r\n        if (this._connectionState === HubConnectionState.Disconnecting) {\r\n            this._completeClose(error);\r\n        }\r\n        else if (this._connectionState === HubConnectionState.Connected && this._reconnectPolicy) {\r\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n            this._reconnect(error);\r\n        }\r\n        else if (this._connectionState === HubConnectionState.Connected) {\r\n            this._completeClose(error);\r\n        }\r\n        // If none of the above if conditions were true were called the HubConnection must be in either:\r\n        // 1. The Connecting state in which case the handshakeResolver will complete it and stopDuringStartError will fail it.\r\n        // 2. The Reconnecting state in which case the handshakeResolver will complete it and stopDuringStartError will fail the current reconnect attempt\r\n        //    and potentially continue the reconnect() loop.\r\n        // 3. The Disconnected state in which case we're already done.\r\n    }\r\n    _completeClose(error) {\r\n        if (this._connectionStarted) {\r\n            this._connectionState = HubConnectionState.Disconnected;\r\n            this._connectionStarted = false;\r\n            if (this._messageBuffer) {\r\n                this._messageBuffer._dispose(error !== null && error !== void 0 ? error : new Error(\"Connection closed.\"));\r\n                this._messageBuffer = undefined;\r\n            }\r\n            if (Platform.isBrowser) {\r\n                window.document.removeEventListener(\"freeze\", this._freezeEventListener);\r\n            }\r\n            try {\r\n                this._closedCallbacks.forEach((c) => c.apply(this, [error]));\r\n            }\r\n            catch (e) {\r\n                this._logger.log(LogLevel.Error, `An onclose callback called with error '${error}' threw error '${e}'.`);\r\n            }\r\n        }\r\n    }\r\n    async _reconnect(error) {\r\n        const reconnectStartTime = Date.now();\r\n        let previousReconnectAttempts = 0;\r\n        let retryError = error !== undefined ? error : new Error(\"Attempting to reconnect due to a unknown error.\");\r\n        let nextRetryDelay = this._getNextRetryDelay(previousReconnectAttempts++, 0, retryError);\r\n        if (nextRetryDelay === null) {\r\n            this._logger.log(LogLevel.Debug, \"Connection not reconnecting because the IRetryPolicy returned null on the first reconnect attempt.\");\r\n            this._completeClose(error);\r\n            return;\r\n        }\r\n        this._connectionState = HubConnectionState.Reconnecting;\r\n        if (error) {\r\n            this._logger.log(LogLevel.Information, `Connection reconnecting because of error '${error}'.`);\r\n        }\r\n        else {\r\n            this._logger.log(LogLevel.Information, \"Connection reconnecting.\");\r\n        }\r\n        if (this._reconnectingCallbacks.length !== 0) {\r\n            try {\r\n                this._reconnectingCallbacks.forEach((c) => c.apply(this, [error]));\r\n            }\r\n            catch (e) {\r\n                this._logger.log(LogLevel.Error, `An onreconnecting callback called with error '${error}' threw error '${e}'.`);\r\n            }\r\n            // Exit early if an onreconnecting callback called connection.stop().\r\n            if (this._connectionState !== HubConnectionState.Reconnecting) {\r\n                this._logger.log(LogLevel.Debug, \"Connection left the reconnecting state in onreconnecting callback. Done reconnecting.\");\r\n                return;\r\n            }\r\n        }\r\n        while (nextRetryDelay !== null) {\r\n            this._logger.log(LogLevel.Information, `Reconnect attempt number ${previousReconnectAttempts} will start in ${nextRetryDelay} ms.`);\r\n            await new Promise((resolve) => {\r\n                this._reconnectDelayHandle = setTimeout(resolve, nextRetryDelay);\r\n            });\r\n            this._reconnectDelayHandle = undefined;\r\n            if (this._connectionState !== HubConnectionState.Reconnecting) {\r\n                this._logger.log(LogLevel.Debug, \"Connection left the reconnecting state during reconnect delay. Done reconnecting.\");\r\n                return;\r\n            }\r\n            try {\r\n                await this._startInternal();\r\n                this._connectionState = HubConnectionState.Connected;\r\n                this._logger.log(LogLevel.Information, \"HubConnection reconnected successfully.\");\r\n                if (this._reconnectedCallbacks.length !== 0) {\r\n                    try {\r\n                        this._reconnectedCallbacks.forEach((c) => c.apply(this, [this.connection.connectionId]));\r\n                    }\r\n                    catch (e) {\r\n                        this._logger.log(LogLevel.Error, `An onreconnected callback called with connectionId '${this.connection.connectionId}; threw error '${e}'.`);\r\n                    }\r\n                }\r\n                return;\r\n            }\r\n            catch (e) {\r\n                this._logger.log(LogLevel.Information, `Reconnect attempt failed because of error '${e}'.`);\r\n                if (this._connectionState !== HubConnectionState.Reconnecting) {\r\n                    this._logger.log(LogLevel.Debug, `Connection moved to the '${this._connectionState}' from the reconnecting state during reconnect attempt. Done reconnecting.`);\r\n                    // The TypeScript compiler thinks that connectionState must be Connected here. The TypeScript compiler is wrong.\r\n                    if (this._connectionState === HubConnectionState.Disconnecting) {\r\n                        this._completeClose();\r\n                    }\r\n                    return;\r\n                }\r\n                retryError = e instanceof Error ? e : new Error(e.toString());\r\n                nextRetryDelay = this._getNextRetryDelay(previousReconnectAttempts++, Date.now() - reconnectStartTime, retryError);\r\n            }\r\n        }\r\n        this._logger.log(LogLevel.Information, `Reconnect retries have been exhausted after ${Date.now() - reconnectStartTime} ms and ${previousReconnectAttempts} failed attempts. Connection disconnecting.`);\r\n        this._completeClose();\r\n    }\r\n    _getNextRetryDelay(previousRetryCount, elapsedMilliseconds, retryReason) {\r\n        try {\r\n            return this._reconnectPolicy.nextRetryDelayInMilliseconds({\r\n                elapsedMilliseconds,\r\n                previousRetryCount,\r\n                retryReason,\r\n            });\r\n        }\r\n        catch (e) {\r\n            this._logger.log(LogLevel.Error, `IRetryPolicy.nextRetryDelayInMilliseconds(${previousRetryCount}, ${elapsedMilliseconds}) threw error '${e}'.`);\r\n            return null;\r\n        }\r\n    }\r\n    _cancelCallbacksWithError(error) {\r\n        const callbacks = this._callbacks;\r\n        this._callbacks = {};\r\n        Object.keys(callbacks)\r\n            .forEach((key) => {\r\n            const callback = callbacks[key];\r\n            try {\r\n                callback(null, error);\r\n            }\r\n            catch (e) {\r\n                this._logger.log(LogLevel.Error, `Stream 'error' callback called with '${error}' threw error: ${getErrorString(e)}`);\r\n            }\r\n        });\r\n    }\r\n    _cleanupPingTimer() {\r\n        if (this._pingServerHandle) {\r\n            clearTimeout(this._pingServerHandle);\r\n            this._pingServerHandle = undefined;\r\n        }\r\n    }\r\n    _cleanupTimeout() {\r\n        if (this._timeoutHandle) {\r\n            clearTimeout(this._timeoutHandle);\r\n        }\r\n    }\r\n    _createInvocation(methodName, args, nonblocking, streamIds) {\r\n        if (nonblocking) {\r\n            if (streamIds.length !== 0) {\r\n                return {\r\n                    arguments: args,\r\n                    streamIds,\r\n                    target: methodName,\r\n                    type: MessageType.Invocation,\r\n                };\r\n            }\r\n            else {\r\n                return {\r\n                    arguments: args,\r\n                    target: methodName,\r\n                    type: MessageType.Invocation,\r\n                };\r\n            }\r\n        }\r\n        else {\r\n            const invocationId = this._invocationId;\r\n            this._invocationId++;\r\n            if (streamIds.length !== 0) {\r\n                return {\r\n                    arguments: args,\r\n                    invocationId: invocationId.toString(),\r\n                    streamIds,\r\n                    target: methodName,\r\n                    type: MessageType.Invocation,\r\n                };\r\n            }\r\n            else {\r\n                return {\r\n                    arguments: args,\r\n                    invocationId: invocationId.toString(),\r\n                    target: methodName,\r\n                    type: MessageType.Invocation,\r\n                };\r\n            }\r\n        }\r\n    }\r\n    _launchStreams(streams, promiseQueue) {\r\n        if (streams.length === 0) {\r\n            return;\r\n        }\r\n        // Synchronize stream data so they arrive in-order on the server\r\n        if (!promiseQueue) {\r\n            promiseQueue = Promise.resolve();\r\n        }\r\n        // We want to iterate over the keys, since the keys are the stream ids\r\n        // eslint-disable-next-line guard-for-in\r\n        for (const streamId in streams) {\r\n            streams[streamId].subscribe({\r\n                complete: () => {\r\n                    promiseQueue = promiseQueue.then(() => this._sendWithProtocol(this._createCompletionMessage(streamId)));\r\n                },\r\n                error: (err) => {\r\n                    let message;\r\n                    if (err instanceof Error) {\r\n                        message = err.message;\r\n                    }\r\n                    else if (err && err.toString) {\r\n                        message = err.toString();\r\n                    }\r\n                    else {\r\n                        message = \"Unknown error\";\r\n                    }\r\n                    promiseQueue = promiseQueue.then(() => this._sendWithProtocol(this._createCompletionMessage(streamId, message)));\r\n                },\r\n                next: (item) => {\r\n                    promiseQueue = promiseQueue.then(() => this._sendWithProtocol(this._createStreamItemMessage(streamId, item)));\r\n                },\r\n            });\r\n        }\r\n    }\r\n    _replaceStreamingParams(args) {\r\n        const streams = [];\r\n        const streamIds = [];\r\n        for (let i = 0; i < args.length; i++) {\r\n            const argument = args[i];\r\n            if (this._isObservable(argument)) {\r\n                const streamId = this._invocationId;\r\n                this._invocationId++;\r\n                // Store the stream for later use\r\n                streams[streamId] = argument;\r\n                streamIds.push(streamId.toString());\r\n                // remove stream from args\r\n                args.splice(i, 1);\r\n            }\r\n        }\r\n        return [streams, streamIds];\r\n    }\r\n    _isObservable(arg) {\r\n        // This allows other stream implementations to just work (like rxjs)\r\n        return arg && arg.subscribe && typeof arg.subscribe === \"function\";\r\n    }\r\n    _createStreamInvocation(methodName, args, streamIds) {\r\n        const invocationId = this._invocationId;\r\n        this._invocationId++;\r\n        if (streamIds.length !== 0) {\r\n            return {\r\n                arguments: args,\r\n                invocationId: invocationId.toString(),\r\n                streamIds,\r\n                target: methodName,\r\n                type: MessageType.StreamInvocation,\r\n            };\r\n        }\r\n        else {\r\n            return {\r\n                arguments: args,\r\n                invocationId: invocationId.toString(),\r\n                target: methodName,\r\n                type: MessageType.StreamInvocation,\r\n            };\r\n        }\r\n    }\r\n    _createCancelInvocation(id) {\r\n        return {\r\n            invocationId: id,\r\n            type: MessageType.CancelInvocation,\r\n        };\r\n    }\r\n    _createStreamItemMessage(id, item) {\r\n        return {\r\n            invocationId: id,\r\n            item,\r\n            type: MessageType.StreamItem,\r\n        };\r\n    }\r\n    _createCompletionMessage(id, error, result) {\r\n        if (error) {\r\n            return {\r\n                error,\r\n                invocationId: id,\r\n                type: MessageType.Completion,\r\n            };\r\n        }\r\n        return {\r\n            invocationId: id,\r\n            result,\r\n            type: MessageType.Completion,\r\n        };\r\n    }\r\n    _createCloseMessage() {\r\n        return { type: MessageType.Close };\r\n    }\r\n}\r\n"], "mappings": ";AAAA;AACA;AACA,SAASA,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,UAAU,QAAQ,UAAU;AACrC,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,QAAQ,QAAQ,WAAW;AACpC,SAASC,OAAO,QAAQ,WAAW;AACnC,SAASC,GAAG,EAAEC,cAAc,EAAEC,QAAQ,QAAQ,SAAS;AACvD,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,MAAMC,qBAAqB,GAAG,EAAE,GAAG,IAAI;AACvC,MAAMC,2BAA2B,GAAG,EAAE,GAAG,IAAI;AAC7C,MAAMC,sCAAsC,GAAG,MAAM;AACrD;AACA,OAAO,IAAIC,kBAAkB;AAC7B,CAAC,UAAUA,kBAAkB,EAAE;EAC3B;EACAA,kBAAkB,CAAC,cAAc,CAAC,GAAG,cAAc;EACnD;EACAA,kBAAkB,CAAC,YAAY,CAAC,GAAG,YAAY;EAC/C;EACAA,kBAAkB,CAAC,WAAW,CAAC,GAAG,WAAW;EAC7C;EACAA,kBAAkB,CAAC,eAAe,CAAC,GAAG,eAAe;EACrD;EACAA,kBAAkB,CAAC,cAAc,CAAC,GAAG,cAAc;AACvD,CAAC,EAAEA,kBAAkB,KAAKA,kBAAkB,GAAG,CAAC,CAAC,CAAC,CAAC;AACnD;AACA,OAAO,MAAMC,aAAa,CAAC;EACvB;EACA;EACA;EACA;EACA;EACA,OAAOC,MAAMA,CAACC,UAAU,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,2BAA2B,EAAEC,+BAA+B,EAAEC,2BAA2B,EAAE;IACpJ,OAAO,IAAIR,aAAa,CAACE,UAAU,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,2BAA2B,EAAEC,+BAA+B,EAAEC,2BAA2B,CAAC;EACtK;EACAC,WAAWA,CAACP,UAAU,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,2BAA2B,EAAEC,+BAA+B,EAAEC,2BAA2B,EAAE;IAClJ,IAAI,CAACE,cAAc,GAAG,CAAC;IACvB,IAAI,CAACC,oBAAoB,GAAG,MAAM;MAC9B,IAAI,CAACC,OAAO,CAACC,GAAG,CAACvB,QAAQ,CAACwB,OAAO,EAAE,uNAAuN,CAAC;IAC/P,CAAC;IACDtB,GAAG,CAACuB,UAAU,CAACb,UAAU,EAAE,YAAY,CAAC;IACxCV,GAAG,CAACuB,UAAU,CAACZ,MAAM,EAAE,QAAQ,CAAC;IAChCX,GAAG,CAACuB,UAAU,CAACX,QAAQ,EAAE,UAAU,CAAC;IACpC,IAAI,CAACE,2BAA2B,GAAGA,2BAA2B,KAAK,IAAI,IAAIA,2BAA2B,KAAK,KAAK,CAAC,GAAGA,2BAA2B,GAAGV,qBAAqB;IACvK,IAAI,CAACW,+BAA+B,GAAGA,+BAA+B,KAAK,IAAI,IAAIA,+BAA+B,KAAK,KAAK,CAAC,GAAGA,+BAA+B,GAAGV,2BAA2B;IAC7L,IAAI,CAACmB,4BAA4B,GAAGR,2BAA2B,KAAK,IAAI,IAAIA,2BAA2B,KAAK,KAAK,CAAC,GAAGA,2BAA2B,GAAGV,sCAAsC;IACzL,IAAI,CAACc,OAAO,GAAGT,MAAM;IACrB,IAAI,CAACc,SAAS,GAAGb,QAAQ;IACzB,IAAI,CAACF,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACgB,gBAAgB,GAAGb,eAAe;IACvC,IAAI,CAACc,kBAAkB,GAAG,IAAIhC,iBAAiB,CAAC,CAAC;IACjD,IAAI,CAACe,UAAU,CAACkB,SAAS,GAAIC,IAAI,IAAK,IAAI,CAACC,oBAAoB,CAACD,IAAI,CAAC;IACrE,IAAI,CAACnB,UAAU,CAACqB,OAAO,GAAIC,KAAK,IAAK,IAAI,CAACC,iBAAiB,CAACD,KAAK,CAAC;IAClE,IAAI,CAACE,UAAU,GAAG,CAAC,CAAC;IACpB,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAC;IAClB,IAAI,CAACC,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACC,sBAAsB,GAAG,EAAE;IAChC,IAAI,CAACC,qBAAqB,GAAG,EAAE;IAC/B,IAAI,CAACC,aAAa,GAAG,CAAC;IACtB,IAAI,CAACC,0BAA0B,GAAG,KAAK;IACvC,IAAI,CAACC,gBAAgB,GAAGlC,kBAAkB,CAACmC,YAAY;IACvD,IAAI,CAACC,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACC,kBAAkB,GAAG,IAAI,CAACnB,SAAS,CAACoB,YAAY,CAAC;MAAEC,IAAI,EAAEjD,WAAW,CAACkD;IAAK,CAAC,CAAC;EACrF;EACA;EACA,IAAIC,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACP,gBAAgB;EAChC;EACA;AACJ;AACA;EACI,IAAIQ,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACvC,UAAU,GAAI,IAAI,CAACA,UAAU,CAACuC,YAAY,IAAI,IAAI,GAAI,IAAI;EAC1E;EACA;EACA,IAAIC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACxC,UAAU,CAACwC,OAAO,IAAI,EAAE;EACxC;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIA,OAAOA,CAACC,GAAG,EAAE;IACb,IAAI,IAAI,CAACV,gBAAgB,KAAKlC,kBAAkB,CAACmC,YAAY,IAAI,IAAI,CAACD,gBAAgB,KAAKlC,kBAAkB,CAAC6C,YAAY,EAAE;MACxH,MAAM,IAAIC,KAAK,CAAC,wFAAwF,CAAC;IAC7G;IACA,IAAI,CAACF,GAAG,EAAE;MACN,MAAM,IAAIE,KAAK,CAAC,4CAA4C,CAAC;IACjE;IACA,IAAI,CAAC3C,UAAU,CAACwC,OAAO,GAAGC,GAAG;EACjC;EACA;AACJ;AACA;AACA;EACIG,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACC,aAAa,GAAG,IAAI,CAACC,0BAA0B,CAAC,CAAC;IACtD,OAAO,IAAI,CAACD,aAAa;EAC7B;EACMC,0BAA0BA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAC/B,IAAID,KAAI,CAAChB,gBAAgB,KAAKlC,kBAAkB,CAACmC,YAAY,EAAE;QAC3D,OAAOiB,OAAO,CAACC,MAAM,CAAC,IAAIP,KAAK,CAAC,uEAAuE,CAAC,CAAC;MAC7G;MACAI,KAAI,CAAChB,gBAAgB,GAAGlC,kBAAkB,CAACsD,UAAU;MACrDJ,KAAI,CAACrC,OAAO,CAACC,GAAG,CAACvB,QAAQ,CAACgE,KAAK,EAAE,yBAAyB,CAAC;MAC3D,IAAI;QACA,MAAML,KAAI,CAACM,cAAc,CAAC,CAAC;QAC3B,IAAI7D,QAAQ,CAAC8D,SAAS,EAAE;UACpB;UACAC,MAAM,CAACC,QAAQ,CAACC,gBAAgB,CAAC,QAAQ,EAAEV,KAAI,CAACtC,oBAAoB,CAAC;QACzE;QACAsC,KAAI,CAAChB,gBAAgB,GAAGlC,kBAAkB,CAAC6D,SAAS;QACpDX,KAAI,CAACd,kBAAkB,GAAG,IAAI;QAC9Bc,KAAI,CAACrC,OAAO,CAACC,GAAG,CAACvB,QAAQ,CAACgE,KAAK,EAAE,uCAAuC,CAAC;MAC7E,CAAC,CACD,OAAOO,CAAC,EAAE;QACNZ,KAAI,CAAChB,gBAAgB,GAAGlC,kBAAkB,CAACmC,YAAY;QACvDe,KAAI,CAACrC,OAAO,CAACC,GAAG,CAACvB,QAAQ,CAACgE,KAAK,EAAG,gEAA+DO,CAAE,IAAG,CAAC;QACvG,OAAOV,OAAO,CAACC,MAAM,CAACS,CAAC,CAAC;MAC5B;IAAC;EACL;EACMN,cAAcA,CAAA,EAAG;IAAA,IAAAO,MAAA;IAAA,OAAAZ,iBAAA;MACnBY,MAAI,CAACC,qBAAqB,GAAGC,SAAS;MACtCF,MAAI,CAAC9B,0BAA0B,GAAG,KAAK;MACvC;MACA,MAAMiC,gBAAgB,GAAG,IAAId,OAAO,CAAC,CAACe,OAAO,EAAEd,MAAM,KAAK;QACtDU,MAAI,CAACK,kBAAkB,GAAGD,OAAO;QACjCJ,MAAI,CAACM,kBAAkB,GAAGhB,MAAM;MACpC,CAAC,CAAC;MACF,MAAMU,MAAI,CAAC5D,UAAU,CAAC4C,KAAK,CAACgB,MAAI,CAAC7C,SAAS,CAACoD,cAAc,CAAC;MAC1D,IAAI;QACA,IAAIC,OAAO,GAAGR,MAAI,CAAC7C,SAAS,CAACqD,OAAO;QACpC,IAAI,CAACR,MAAI,CAAC5D,UAAU,CAACqE,QAAQ,CAACC,SAAS,EAAE;UACrC;UACA;UACAF,OAAO,GAAG,CAAC;QACf;QACA,MAAMG,gBAAgB,GAAG;UACrBrE,QAAQ,EAAE0D,MAAI,CAAC7C,SAAS,CAACyD,IAAI;UAC7BJ;QACJ,CAAC;QACDR,MAAI,CAAClD,OAAO,CAACC,GAAG,CAACvB,QAAQ,CAACgE,KAAK,EAAE,4BAA4B,CAAC;QAC9D,MAAMQ,MAAI,CAACa,YAAY,CAACb,MAAI,CAAC3C,kBAAkB,CAACyD,qBAAqB,CAACH,gBAAgB,CAAC,CAAC;QACxFX,MAAI,CAAClD,OAAO,CAACC,GAAG,CAACvB,QAAQ,CAACuF,WAAW,EAAG,sBAAqBf,MAAI,CAAC7C,SAAS,CAACyD,IAAK,IAAG,CAAC;QACrF;QACAZ,MAAI,CAACgB,eAAe,CAAC,CAAC;QACtBhB,MAAI,CAACiB,mBAAmB,CAAC,CAAC;QAC1BjB,MAAI,CAACkB,uBAAuB,CAAC,CAAC;QAC9B,MAAMf,gBAAgB;QACtB;QACA;QACA;QACA,IAAIH,MAAI,CAACC,qBAAqB,EAAE;UAC5B;UACA;UACA;UACA;UACA,MAAMD,MAAI,CAACC,qBAAqB;QACpC;QACA,MAAMkB,oBAAoB,GAAGnB,MAAI,CAAC5D,UAAU,CAACqE,QAAQ,CAACC,SAAS,IAAI,KAAK;QACxE,IAAIS,oBAAoB,EAAE;UACtBnB,MAAI,CAACoB,cAAc,GAAG,IAAIvF,aAAa,CAACmE,MAAI,CAAC7C,SAAS,EAAE6C,MAAI,CAAC5D,UAAU,EAAE4D,MAAI,CAAC9C,4BAA4B,CAAC;UAC3G8C,MAAI,CAAC5D,UAAU,CAACqE,QAAQ,CAACY,YAAY,GAAGrB,MAAI,CAACoB,cAAc,CAACE,aAAa,CAACC,IAAI,CAACvB,MAAI,CAACoB,cAAc,CAAC;UACnGpB,MAAI,CAAC5D,UAAU,CAACqE,QAAQ,CAACe,MAAM,GAAG,MAAM;YACpC,IAAIxB,MAAI,CAACoB,cAAc,EAAE;cACrB,OAAOpB,MAAI,CAACoB,cAAc,CAACK,OAAO,CAAC,CAAC;YACxC;UACJ,CAAC;QACL;QACA,IAAI,CAACzB,MAAI,CAAC5D,UAAU,CAACqE,QAAQ,CAACiB,iBAAiB,EAAE;UAC7C,MAAM1B,MAAI,CAACa,YAAY,CAACb,MAAI,CAAC1B,kBAAkB,CAAC;QACpD;MACJ,CAAC,CACD,OAAOyB,CAAC,EAAE;QACNC,MAAI,CAAClD,OAAO,CAACC,GAAG,CAACvB,QAAQ,CAACgE,KAAK,EAAG,oCAAmCO,CAAE,2CAA0C,CAAC;QAClHC,MAAI,CAACgB,eAAe,CAAC,CAAC;QACtBhB,MAAI,CAAC2B,iBAAiB,CAAC,CAAC;QACxB;QACA;QACA,MAAM3B,MAAI,CAAC5D,UAAU,CAACwF,IAAI,CAAC7B,CAAC,CAAC;QAC7B,MAAMA,CAAC;MACX;IAAC;EACL;EACA;AACJ;AACA;AACA;EACU6B,IAAIA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAzC,iBAAA;MACT;MACA,MAAM0C,YAAY,GAAGD,MAAI,CAAC5C,aAAa;MACvC4C,MAAI,CAACzF,UAAU,CAACqE,QAAQ,CAACC,SAAS,GAAG,KAAK;MAC1CmB,MAAI,CAACE,YAAY,GAAGF,MAAI,CAACG,aAAa,CAAC,CAAC;MACxC,MAAMH,MAAI,CAACE,YAAY;MACvB,IAAI;QACA;QACA,MAAMD,YAAY;MACtB,CAAC,CACD,OAAO/B,CAAC,EAAE;QACN;MAAA;IACH;EACL;EACAiC,aAAaA,CAACtE,KAAK,EAAE;IACjB,IAAI,IAAI,CAACS,gBAAgB,KAAKlC,kBAAkB,CAACmC,YAAY,EAAE;MAC3D,IAAI,CAACtB,OAAO,CAACC,GAAG,CAACvB,QAAQ,CAACgE,KAAK,EAAG,8BAA6B9B,KAAM,4DAA2D,CAAC;MACjI,OAAO2B,OAAO,CAACe,OAAO,CAAC,CAAC;IAC5B;IACA,IAAI,IAAI,CAACjC,gBAAgB,KAAKlC,kBAAkB,CAACgG,aAAa,EAAE;MAC5D,IAAI,CAACnF,OAAO,CAACC,GAAG,CAACvB,QAAQ,CAACgE,KAAK,EAAG,+BAA8B9B,KAAM,yEAAwE,CAAC;MAC/I,OAAO,IAAI,CAACqE,YAAY;IAC5B;IACA,MAAMrD,KAAK,GAAG,IAAI,CAACP,gBAAgB;IACnC,IAAI,CAACA,gBAAgB,GAAGlC,kBAAkB,CAACgG,aAAa;IACxD,IAAI,CAACnF,OAAO,CAACC,GAAG,CAACvB,QAAQ,CAACgE,KAAK,EAAE,yBAAyB,CAAC;IAC3D,IAAI,IAAI,CAAC0C,qBAAqB,EAAE;MAC5B;MACA;MACA;MACA,IAAI,CAACpF,OAAO,CAACC,GAAG,CAACvB,QAAQ,CAACgE,KAAK,EAAE,+DAA+D,CAAC;MACjG2C,YAAY,CAAC,IAAI,CAACD,qBAAqB,CAAC;MACxC,IAAI,CAACA,qBAAqB,GAAGhC,SAAS;MACtC,IAAI,CAACkC,cAAc,CAAC,CAAC;MACrB,OAAO/C,OAAO,CAACe,OAAO,CAAC,CAAC;IAC5B;IACA,IAAI1B,KAAK,KAAKzC,kBAAkB,CAAC6D,SAAS,EAAE;MACxC;MACA,IAAI,CAACuC,iBAAiB,CAAC,CAAC;IAC5B;IACA,IAAI,CAACrB,eAAe,CAAC,CAAC;IACtB,IAAI,CAACW,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAAC1B,qBAAqB,GAAGvC,KAAK,IAAI,IAAIpC,UAAU,CAAC,qEAAqE,CAAC;IAC3H;IACA;IACA;IACA,OAAO,IAAI,CAACc,UAAU,CAACwF,IAAI,CAAClE,KAAK,CAAC;EACtC;EACM2E,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAlD,iBAAA;MACtB,IAAI;QACA,MAAMkD,MAAI,CAACC,iBAAiB,CAACD,MAAI,CAACE,mBAAmB,CAAC,CAAC,CAAC;MAC5D,CAAC,CACD,MAAM;QACF;MAAA;IACH;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,MAAMA,CAACC,UAAU,EAAE,GAAGC,IAAI,EAAE;IACxB,MAAM,CAACC,OAAO,EAAEC,SAAS,CAAC,GAAG,IAAI,CAACC,uBAAuB,CAACH,IAAI,CAAC;IAC/D,MAAMI,oBAAoB,GAAG,IAAI,CAACC,uBAAuB,CAACN,UAAU,EAAEC,IAAI,EAAEE,SAAS,CAAC;IACtF;IACA,IAAII,YAAY;IAChB,MAAMC,OAAO,GAAG,IAAIzH,OAAO,CAAC,CAAC;IAC7ByH,OAAO,CAACC,cAAc,GAAG,MAAM;MAC3B,MAAMC,gBAAgB,GAAG,IAAI,CAACC,uBAAuB,CAACN,oBAAoB,CAACO,YAAY,CAAC;MACxF,OAAO,IAAI,CAAC1F,UAAU,CAACmF,oBAAoB,CAACO,YAAY,CAAC;MACzD,OAAOL,YAAY,CAACM,IAAI,CAAC,MAAM;QAC3B,OAAO,IAAI,CAAChB,iBAAiB,CAACa,gBAAgB,CAAC;MACnD,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAACxF,UAAU,CAACmF,oBAAoB,CAACO,YAAY,CAAC,GAAG,CAACE,eAAe,EAAE9F,KAAK,KAAK;MAC7E,IAAIA,KAAK,EAAE;QACPwF,OAAO,CAACxF,KAAK,CAACA,KAAK,CAAC;QACpB;MACJ,CAAC,MACI,IAAI8F,eAAe,EAAE;QACtB;QACA,IAAIA,eAAe,CAAChF,IAAI,KAAKjD,WAAW,CAACkI,UAAU,EAAE;UACjD,IAAID,eAAe,CAAC9F,KAAK,EAAE;YACvBwF,OAAO,CAACxF,KAAK,CAAC,IAAIqB,KAAK,CAACyE,eAAe,CAAC9F,KAAK,CAAC,CAAC;UACnD,CAAC,MACI;YACDwF,OAAO,CAACQ,QAAQ,CAAC,CAAC;UACtB;QACJ,CAAC,MACI;UACDR,OAAO,CAACS,IAAI,CAAEH,eAAe,CAACI,IAAK,CAAC;QACxC;MACJ;IACJ,CAAC;IACDX,YAAY,GAAG,IAAI,CAACV,iBAAiB,CAACQ,oBAAoB,CAAC,CACtDc,KAAK,CAAE9D,CAAC,IAAK;MACdmD,OAAO,CAACxF,KAAK,CAACqC,CAAC,CAAC;MAChB,OAAO,IAAI,CAACnC,UAAU,CAACmF,oBAAoB,CAACO,YAAY,CAAC;IAC7D,CAAC,CAAC;IACF,IAAI,CAACQ,cAAc,CAAClB,OAAO,EAAEK,YAAY,CAAC;IAC1C,OAAOC,OAAO;EAClB;EACArC,YAAYA,CAACkD,OAAO,EAAE;IAClB,IAAI,CAAC7C,uBAAuB,CAAC,CAAC;IAC9B,OAAO,IAAI,CAAC9E,UAAU,CAAC4H,IAAI,CAACD,OAAO,CAAC;EACxC;EACA;AACJ;AACA;AACA;EACIxB,iBAAiBA,CAACwB,OAAO,EAAE;IACvB,IAAI,IAAI,CAAC3C,cAAc,EAAE;MACrB,OAAO,IAAI,CAACA,cAAc,CAAC6C,KAAK,CAACF,OAAO,CAAC;IAC7C,CAAC,MACI;MACD,OAAO,IAAI,CAAClD,YAAY,CAAC,IAAI,CAAC1D,SAAS,CAACoB,YAAY,CAACwF,OAAO,CAAC,CAAC;IAClE;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,IAAIA,CAACtB,UAAU,EAAE,GAAGC,IAAI,EAAE;IACtB,MAAM,CAACC,OAAO,EAAEC,SAAS,CAAC,GAAG,IAAI,CAACC,uBAAuB,CAACH,IAAI,CAAC;IAC/D,MAAMuB,WAAW,GAAG,IAAI,CAAC3B,iBAAiB,CAAC,IAAI,CAAC4B,iBAAiB,CAACzB,UAAU,EAAEC,IAAI,EAAE,IAAI,EAAEE,SAAS,CAAC,CAAC;IACrG,IAAI,CAACiB,cAAc,CAAClB,OAAO,EAAEsB,WAAW,CAAC;IACzC,OAAOA,WAAW;EACtB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIE,MAAMA,CAAC1B,UAAU,EAAE,GAAGC,IAAI,EAAE;IACxB,MAAM,CAACC,OAAO,EAAEC,SAAS,CAAC,GAAG,IAAI,CAACC,uBAAuB,CAACH,IAAI,CAAC;IAC/D,MAAMI,oBAAoB,GAAG,IAAI,CAACoB,iBAAiB,CAACzB,UAAU,EAAEC,IAAI,EAAE,KAAK,EAAEE,SAAS,CAAC;IACvF,MAAMwB,CAAC,GAAG,IAAIhF,OAAO,CAAC,CAACe,OAAO,EAAEd,MAAM,KAAK;MACvC;MACA,IAAI,CAAC1B,UAAU,CAACmF,oBAAoB,CAACO,YAAY,CAAC,GAAG,CAACE,eAAe,EAAE9F,KAAK,KAAK;QAC7E,IAAIA,KAAK,EAAE;UACP4B,MAAM,CAAC5B,KAAK,CAAC;UACb;QACJ,CAAC,MACI,IAAI8F,eAAe,EAAE;UACtB;UACA,IAAIA,eAAe,CAAChF,IAAI,KAAKjD,WAAW,CAACkI,UAAU,EAAE;YACjD,IAAID,eAAe,CAAC9F,KAAK,EAAE;cACvB4B,MAAM,CAAC,IAAIP,KAAK,CAACyE,eAAe,CAAC9F,KAAK,CAAC,CAAC;YAC5C,CAAC,MACI;cACD0C,OAAO,CAACoD,eAAe,CAACc,MAAM,CAAC;YACnC;UACJ,CAAC,MACI;YACDhF,MAAM,CAAC,IAAIP,KAAK,CAAE,4BAA2ByE,eAAe,CAAChF,IAAK,EAAC,CAAC,CAAC;UACzE;QACJ;MACJ,CAAC;MACD,MAAMyE,YAAY,GAAG,IAAI,CAACV,iBAAiB,CAACQ,oBAAoB,CAAC,CAC5Dc,KAAK,CAAE9D,CAAC,IAAK;QACdT,MAAM,CAACS,CAAC,CAAC;QACT;QACA,OAAO,IAAI,CAACnC,UAAU,CAACmF,oBAAoB,CAACO,YAAY,CAAC;MAC7D,CAAC,CAAC;MACF,IAAI,CAACQ,cAAc,CAAClB,OAAO,EAAEK,YAAY,CAAC;IAC9C,CAAC,CAAC;IACF,OAAOoB,CAAC;EACZ;EACAE,EAAEA,CAAC7B,UAAU,EAAE8B,SAAS,EAAE;IACtB,IAAI,CAAC9B,UAAU,IAAI,CAAC8B,SAAS,EAAE;MAC3B;IACJ;IACA9B,UAAU,GAAGA,UAAU,CAAC+B,WAAW,CAAC,CAAC;IACrC,IAAI,CAAC,IAAI,CAAC5G,QAAQ,CAAC6E,UAAU,CAAC,EAAE;MAC5B,IAAI,CAAC7E,QAAQ,CAAC6E,UAAU,CAAC,GAAG,EAAE;IAClC;IACA;IACA,IAAI,IAAI,CAAC7E,QAAQ,CAAC6E,UAAU,CAAC,CAACgC,OAAO,CAACF,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE;MACrD;IACJ;IACA,IAAI,CAAC3G,QAAQ,CAAC6E,UAAU,CAAC,CAACiC,IAAI,CAACH,SAAS,CAAC;EAC7C;EACAI,GAAGA,CAAClC,UAAU,EAAEmC,MAAM,EAAE;IACpB,IAAI,CAACnC,UAAU,EAAE;MACb;IACJ;IACAA,UAAU,GAAGA,UAAU,CAAC+B,WAAW,CAAC,CAAC;IACrC,MAAMK,QAAQ,GAAG,IAAI,CAACjH,QAAQ,CAAC6E,UAAU,CAAC;IAC1C,IAAI,CAACoC,QAAQ,EAAE;MACX;IACJ;IACA,IAAID,MAAM,EAAE;MACR,MAAME,SAAS,GAAGD,QAAQ,CAACJ,OAAO,CAACG,MAAM,CAAC;MAC1C,IAAIE,SAAS,KAAK,CAAC,CAAC,EAAE;QAClBD,QAAQ,CAACE,MAAM,CAACD,SAAS,EAAE,CAAC,CAAC;QAC7B,IAAID,QAAQ,CAACG,MAAM,KAAK,CAAC,EAAE;UACvB,OAAO,IAAI,CAACpH,QAAQ,CAAC6E,UAAU,CAAC;QACpC;MACJ;IACJ,CAAC,MACI;MACD,OAAO,IAAI,CAAC7E,QAAQ,CAAC6E,UAAU,CAAC;IACpC;EACJ;EACA;AACJ;AACA;AACA;EACIjF,OAAOA,CAACyH,QAAQ,EAAE;IACd,IAAIA,QAAQ,EAAE;MACV,IAAI,CAACpH,gBAAgB,CAAC6G,IAAI,CAACO,QAAQ,CAAC;IACxC;EACJ;EACA;AACJ;AACA;AACA;EACIC,cAAcA,CAACD,QAAQ,EAAE;IACrB,IAAIA,QAAQ,EAAE;MACV,IAAI,CAACnH,sBAAsB,CAAC4G,IAAI,CAACO,QAAQ,CAAC;IAC9C;EACJ;EACA;AACJ;AACA;AACA;EACIE,aAAaA,CAACF,QAAQ,EAAE;IACpB,IAAIA,QAAQ,EAAE;MACV,IAAI,CAAClH,qBAAqB,CAAC2G,IAAI,CAACO,QAAQ,CAAC;IAC7C;EACJ;EACA1H,oBAAoBA,CAACD,IAAI,EAAE;IACvB,IAAI,CAACyD,eAAe,CAAC,CAAC;IACtB,IAAI,CAAC,IAAI,CAAC9C,0BAA0B,EAAE;MAClCX,IAAI,GAAG,IAAI,CAAC8H,yBAAyB,CAAC9H,IAAI,CAAC;MAC3C,IAAI,CAACW,0BAA0B,GAAG,IAAI;IAC1C;IACA;IACA,IAAIX,IAAI,EAAE;MACN;MACA,MAAM+H,QAAQ,GAAG,IAAI,CAACnI,SAAS,CAACoI,aAAa,CAAChI,IAAI,EAAE,IAAI,CAACT,OAAO,CAAC;MACjE,KAAK,MAAMiH,OAAO,IAAIuB,QAAQ,EAAE;QAC5B,IAAI,IAAI,CAAClE,cAAc,IAAI,CAAC,IAAI,CAACA,cAAc,CAACoE,qBAAqB,CAACzB,OAAO,CAAC,EAAE;UAC5E;UACA;QACJ;QACA,QAAQA,OAAO,CAACvF,IAAI;UAChB,KAAKjD,WAAW,CAACkK,UAAU;YACvB,IAAI,CAACC,mBAAmB,CAAC3B,OAAO,CAAC,CAC5BF,KAAK,CAAE9D,CAAC,IAAK;cACd,IAAI,CAACjD,OAAO,CAACC,GAAG,CAACvB,QAAQ,CAACuD,KAAK,EAAG,qCAAoCpD,cAAc,CAACoE,CAAC,CAAE,EAAC,CAAC;YAC9F,CAAC,CAAC;YACF;UACJ,KAAKxE,WAAW,CAACoK,UAAU;UAC3B,KAAKpK,WAAW,CAACkI,UAAU;YAAE;cACzB,MAAMyB,QAAQ,GAAG,IAAI,CAACtH,UAAU,CAACmG,OAAO,CAACT,YAAY,CAAC;cACtD,IAAI4B,QAAQ,EAAE;gBACV,IAAInB,OAAO,CAACvF,IAAI,KAAKjD,WAAW,CAACkI,UAAU,EAAE;kBACzC,OAAO,IAAI,CAAC7F,UAAU,CAACmG,OAAO,CAACT,YAAY,CAAC;gBAChD;gBACA,IAAI;kBACA4B,QAAQ,CAACnB,OAAO,CAAC;gBACrB,CAAC,CACD,OAAOhE,CAAC,EAAE;kBACN,IAAI,CAACjD,OAAO,CAACC,GAAG,CAACvB,QAAQ,CAACuD,KAAK,EAAG,gCAA+BpD,cAAc,CAACoE,CAAC,CAAE,EAAC,CAAC;gBACzF;cACJ;cACA;YACJ;UACA,KAAKxE,WAAW,CAACkD,IAAI;YACjB;YACA;UACJ,KAAKlD,WAAW,CAACqK,KAAK;YAAE;cACpB,IAAI,CAAC9I,OAAO,CAACC,GAAG,CAACvB,QAAQ,CAACuF,WAAW,EAAE,qCAAqC,CAAC;cAC7E,MAAMrD,KAAK,GAAGqG,OAAO,CAACrG,KAAK,GAAG,IAAIqB,KAAK,CAAC,qCAAqC,GAAGgF,OAAO,CAACrG,KAAK,CAAC,GAAGwC,SAAS;cAC1G,IAAI6D,OAAO,CAAC8B,cAAc,KAAK,IAAI,EAAE;gBACjC;gBACA;gBACA;gBACA,IAAI,CAACzJ,UAAU,CAACwF,IAAI,CAAClE,KAAK,CAAC;cAC/B,CAAC,MACI;gBACD;gBACA,IAAI,CAACqE,YAAY,GAAG,IAAI,CAACC,aAAa,CAACtE,KAAK,CAAC;cACjD;cACA;YACJ;UACA,KAAKnC,WAAW,CAACuK,GAAG;YAChB,IAAI,IAAI,CAAC1E,cAAc,EAAE;cACrB,IAAI,CAACA,cAAc,CAAC2E,IAAI,CAAChC,OAAO,CAAC;YACrC;YACA;UACJ,KAAKxI,WAAW,CAACyK,QAAQ;YACrB,IAAI,IAAI,CAAC5E,cAAc,EAAE;cACrB,IAAI,CAACA,cAAc,CAAC6E,cAAc,CAAClC,OAAO,CAAC;YAC/C;YACA;UACJ;YACI,IAAI,CAACjH,OAAO,CAACC,GAAG,CAACvB,QAAQ,CAACwB,OAAO,EAAG,yBAAwB+G,OAAO,CAACvF,IAAK,GAAE,CAAC;YAC5E;QACR;MACJ;IACJ;IACA,IAAI,CAACyC,mBAAmB,CAAC,CAAC;EAC9B;EACAoE,yBAAyBA,CAAC9H,IAAI,EAAE;IAC5B,IAAI2I,eAAe;IACnB,IAAIC,aAAa;IACjB,IAAI;MACA,CAACA,aAAa,EAAED,eAAe,CAAC,GAAG,IAAI,CAAC7I,kBAAkB,CAAC+I,sBAAsB,CAAC7I,IAAI,CAAC;IAC3F,CAAC,CACD,OAAOwC,CAAC,EAAE;MACN,MAAMgE,OAAO,GAAG,oCAAoC,GAAGhE,CAAC;MACxD,IAAI,CAACjD,OAAO,CAACC,GAAG,CAACvB,QAAQ,CAACuD,KAAK,EAAEgF,OAAO,CAAC;MACzC,MAAMrG,KAAK,GAAG,IAAIqB,KAAK,CAACgF,OAAO,CAAC;MAChC,IAAI,CAACzD,kBAAkB,CAAC5C,KAAK,CAAC;MAC9B,MAAMA,KAAK;IACf;IACA,IAAIwI,eAAe,CAACxI,KAAK,EAAE;MACvB,MAAMqG,OAAO,GAAG,mCAAmC,GAAGmC,eAAe,CAACxI,KAAK;MAC3E,IAAI,CAACZ,OAAO,CAACC,GAAG,CAACvB,QAAQ,CAACuD,KAAK,EAAEgF,OAAO,CAAC;MACzC,MAAMrG,KAAK,GAAG,IAAIqB,KAAK,CAACgF,OAAO,CAAC;MAChC,IAAI,CAACzD,kBAAkB,CAAC5C,KAAK,CAAC;MAC9B,MAAMA,KAAK;IACf,CAAC,MACI;MACD,IAAI,CAACZ,OAAO,CAACC,GAAG,CAACvB,QAAQ,CAACgE,KAAK,EAAE,4BAA4B,CAAC;IAClE;IACA,IAAI,CAACa,kBAAkB,CAAC,CAAC;IACzB,OAAO8F,aAAa;EACxB;EACAjF,uBAAuBA,CAAA,EAAG;IACtB,IAAI,IAAI,CAAC9E,UAAU,CAACqE,QAAQ,CAACiB,iBAAiB,EAAE;MAC5C;IACJ;IACA;IACA;IACA,IAAI,CAAC9E,cAAc,GAAG,IAAIyJ,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC7J,+BAA+B;IACjF,IAAI,CAACkF,iBAAiB,CAAC,CAAC;EAC5B;EACAV,mBAAmBA,CAAA,EAAG;IAAA,IAAAsF,MAAA;IAClB,IAAI,CAAC,IAAI,CAACnK,UAAU,CAACqE,QAAQ,IAAI,CAAC,IAAI,CAACrE,UAAU,CAACqE,QAAQ,CAACiB,iBAAiB,EAAE;MAC1E;MACA,IAAI,CAAC8E,cAAc,GAAGC,UAAU,CAAC,MAAM,IAAI,CAACC,aAAa,CAAC,CAAC,EAAE,IAAI,CAAClK,2BAA2B,CAAC;MAC9F;MACA,IAAI,IAAI,CAACmK,iBAAiB,KAAKzG,SAAS,EAAE;QACtC,IAAI0G,QAAQ,GAAG,IAAI,CAAChK,cAAc,GAAG,IAAIyJ,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;QACzD,IAAIM,QAAQ,GAAG,CAAC,EAAE;UACdA,QAAQ,GAAG,CAAC;QAChB;QACA;QACA,IAAI,CAACD,iBAAiB,GAAGF,UAAU,eAAArH,iBAAA,CAAC,aAAY;UAC5C,IAAImH,MAAI,CAACpI,gBAAgB,KAAKlC,kBAAkB,CAAC6D,SAAS,EAAE;YACxD,IAAI;cACA,MAAMyG,MAAI,CAAC1F,YAAY,CAAC0F,MAAI,CAACjI,kBAAkB,CAAC;YACpD,CAAC,CACD,MAAM;cACF;cACA;cACAiI,MAAI,CAAC5E,iBAAiB,CAAC,CAAC;YAC5B;UACJ;QACJ,CAAC,GAAEiF,QAAQ,CAAC;MAChB;IACJ;EACJ;EACA;EACAF,aAAaA,CAAA,EAAG;IACZ;IACA;IACA;IACA,IAAI,CAACtK,UAAU,CAACwF,IAAI,CAAC,IAAI7C,KAAK,CAAC,qEAAqE,CAAC,CAAC;EAC1G;EACM2G,mBAAmBA,CAACmB,iBAAiB,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAA1H,iBAAA;MACzC,MAAMsD,UAAU,GAAGmE,iBAAiB,CAACE,MAAM,CAACtC,WAAW,CAAC,CAAC;MACzD,MAAMuC,OAAO,GAAGF,MAAI,CAACjJ,QAAQ,CAAC6E,UAAU,CAAC;MACzC,IAAI,CAACsE,OAAO,EAAE;QACVF,MAAI,CAAChK,OAAO,CAACC,GAAG,CAACvB,QAAQ,CAACwB,OAAO,EAAG,mCAAkC0F,UAAW,UAAS,CAAC;QAC3F;QACA,IAAImE,iBAAiB,CAACvD,YAAY,EAAE;UAChCwD,MAAI,CAAChK,OAAO,CAACC,GAAG,CAACvB,QAAQ,CAACwB,OAAO,EAAG,wBAAuB0F,UAAW,+BAA8BmE,iBAAiB,CAACvD,YAAa,IAAG,CAAC;UACvI,MAAMwD,MAAI,CAACvE,iBAAiB,CAACuE,MAAI,CAACG,wBAAwB,CAACJ,iBAAiB,CAACvD,YAAY,EAAE,iCAAiC,EAAE,IAAI,CAAC,CAAC;QACxI;QACA;MACJ;MACA;MACA,MAAM4D,WAAW,GAAGF,OAAO,CAACG,KAAK,CAAC,CAAC;MACnC;MACA,MAAMC,eAAe,GAAGP,iBAAiB,CAACvD,YAAY,GAAG,IAAI,GAAG,KAAK;MACrE;MACA,IAAI+D,GAAG;MACP,IAAIC,SAAS;MACb,IAAIC,iBAAiB;MACrB,KAAK,MAAMC,CAAC,IAAIN,WAAW,EAAE;QACzB,IAAI;UACA,MAAMO,OAAO,GAAGJ,GAAG;UACnBA,GAAG,SAASG,CAAC,CAACE,KAAK,CAACZ,MAAI,EAAED,iBAAiB,CAACc,SAAS,CAAC;UACtD,IAAIP,eAAe,IAAIC,GAAG,IAAII,OAAO,EAAE;YACnCX,MAAI,CAAChK,OAAO,CAACC,GAAG,CAACvB,QAAQ,CAACuD,KAAK,EAAG,kCAAiC2D,UAAW,6BAA4B,CAAC;YAC3G6E,iBAAiB,GAAGT,MAAI,CAACG,wBAAwB,CAACJ,iBAAiB,CAACvD,YAAY,EAAG,mCAAkC,EAAE,IAAI,CAAC;UAChI;UACA;UACAgE,SAAS,GAAGpH,SAAS;QACzB,CAAC,CACD,OAAOH,CAAC,EAAE;UACNuH,SAAS,GAAGvH,CAAC;UACb+G,MAAI,CAAChK,OAAO,CAACC,GAAG,CAACvB,QAAQ,CAACuD,KAAK,EAAG,8BAA6B2D,UAAW,kBAAiB3C,CAAE,IAAG,CAAC;QACrG;MACJ;MACA,IAAIwH,iBAAiB,EAAE;QACnB,MAAMT,MAAI,CAACvE,iBAAiB,CAACgF,iBAAiB,CAAC;MACnD,CAAC,MACI,IAAIH,eAAe,EAAE;QACtB;QACA,IAAIE,SAAS,EAAE;UACXC,iBAAiB,GAAGT,MAAI,CAACG,wBAAwB,CAACJ,iBAAiB,CAACvD,YAAY,EAAG,GAAEgE,SAAU,EAAC,EAAE,IAAI,CAAC;QAC3G,CAAC,MACI,IAAID,GAAG,KAAKnH,SAAS,EAAE;UACxBqH,iBAAiB,GAAGT,MAAI,CAACG,wBAAwB,CAACJ,iBAAiB,CAACvD,YAAY,EAAE,IAAI,EAAE+D,GAAG,CAAC;QAChG,CAAC,MACI;UACDP,MAAI,CAAChK,OAAO,CAACC,GAAG,CAACvB,QAAQ,CAACwB,OAAO,EAAG,wBAAuB0F,UAAW,+BAA8BmE,iBAAiB,CAACvD,YAAa,IAAG,CAAC;UACvI;UACAiE,iBAAiB,GAAGT,MAAI,CAACG,wBAAwB,CAACJ,iBAAiB,CAACvD,YAAY,EAAE,iCAAiC,EAAE,IAAI,CAAC;QAC9H;QACA,MAAMwD,MAAI,CAACvE,iBAAiB,CAACgF,iBAAiB,CAAC;MACnD,CAAC,MACI;QACD,IAAIF,GAAG,EAAE;UACLP,MAAI,CAAChK,OAAO,CAACC,GAAG,CAACvB,QAAQ,CAACuD,KAAK,EAAG,qBAAoB2D,UAAW,gDAA+C,CAAC;QACrH;MACJ;IAAC;EACL;EACA/E,iBAAiBA,CAACD,KAAK,EAAE;IACrB,IAAI,CAACZ,OAAO,CAACC,GAAG,CAACvB,QAAQ,CAACgE,KAAK,EAAG,kCAAiC9B,KAAM,2BAA0B,IAAI,CAACS,gBAAiB,GAAE,CAAC;IAC5H;IACA,IAAI,CAAC8B,qBAAqB,GAAG,IAAI,CAACA,qBAAqB,IAAIvC,KAAK,IAAI,IAAIpC,UAAU,CAAC,+EAA+E,CAAC;IACnK;IACA;IACA,IAAI,IAAI,CAAC+E,kBAAkB,EAAE;MACzB,IAAI,CAACA,kBAAkB,CAAC,CAAC;IAC7B;IACA,IAAI,CAACuH,yBAAyB,CAAClK,KAAK,IAAI,IAAIqB,KAAK,CAAC,oEAAoE,CAAC,CAAC;IACxH,IAAI,CAACiC,eAAe,CAAC,CAAC;IACtB,IAAI,CAACW,iBAAiB,CAAC,CAAC;IACxB,IAAI,IAAI,CAACxD,gBAAgB,KAAKlC,kBAAkB,CAACgG,aAAa,EAAE;MAC5D,IAAI,CAACG,cAAc,CAAC1E,KAAK,CAAC;IAC9B,CAAC,MACI,IAAI,IAAI,CAACS,gBAAgB,KAAKlC,kBAAkB,CAAC6D,SAAS,IAAI,IAAI,CAAC1C,gBAAgB,EAAE;MACtF;MACA,IAAI,CAACyK,UAAU,CAACnK,KAAK,CAAC;IAC1B,CAAC,MACI,IAAI,IAAI,CAACS,gBAAgB,KAAKlC,kBAAkB,CAAC6D,SAAS,EAAE;MAC7D,IAAI,CAACsC,cAAc,CAAC1E,KAAK,CAAC;IAC9B;IACA;IACA;IACA;IACA;IACA;EACJ;EACA0E,cAAcA,CAAC1E,KAAK,EAAE;IAClB,IAAI,IAAI,CAACW,kBAAkB,EAAE;MACzB,IAAI,CAACF,gBAAgB,GAAGlC,kBAAkB,CAACmC,YAAY;MACvD,IAAI,CAACC,kBAAkB,GAAG,KAAK;MAC/B,IAAI,IAAI,CAAC+C,cAAc,EAAE;QACrB,IAAI,CAACA,cAAc,CAAC0G,QAAQ,CAACpK,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAG,IAAIqB,KAAK,CAAC,oBAAoB,CAAC,CAAC;QAC1G,IAAI,CAACqC,cAAc,GAAGlB,SAAS;MACnC;MACA,IAAItE,QAAQ,CAAC8D,SAAS,EAAE;QACpBC,MAAM,CAACC,QAAQ,CAACmI,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAClL,oBAAoB,CAAC;MAC5E;MACA,IAAI;QACA,IAAI,CAACiB,gBAAgB,CAACkK,OAAO,CAAEC,CAAC,IAAKA,CAAC,CAACP,KAAK,CAAC,IAAI,EAAE,CAAChK,KAAK,CAAC,CAAC,CAAC;MAChE,CAAC,CACD,OAAOqC,CAAC,EAAE;QACN,IAAI,CAACjD,OAAO,CAACC,GAAG,CAACvB,QAAQ,CAACuD,KAAK,EAAG,0CAAyCrB,KAAM,kBAAiBqC,CAAE,IAAG,CAAC;MAC5G;IACJ;EACJ;EACM8H,UAAUA,CAACnK,KAAK,EAAE;IAAA,IAAAwK,MAAA;IAAA,OAAA9I,iBAAA;MACpB,MAAM+I,kBAAkB,GAAG9B,IAAI,CAAC+B,GAAG,CAAC,CAAC;MACrC,IAAIC,yBAAyB,GAAG,CAAC;MACjC,IAAIC,UAAU,GAAG5K,KAAK,KAAKwC,SAAS,GAAGxC,KAAK,GAAG,IAAIqB,KAAK,CAAC,iDAAiD,CAAC;MAC3G,IAAIwJ,cAAc,GAAGL,MAAI,CAACM,kBAAkB,CAACH,yBAAyB,EAAE,EAAE,CAAC,EAAEC,UAAU,CAAC;MACxF,IAAIC,cAAc,KAAK,IAAI,EAAE;QACzBL,MAAI,CAACpL,OAAO,CAACC,GAAG,CAACvB,QAAQ,CAACgE,KAAK,EAAE,oGAAoG,CAAC;QACtI0I,MAAI,CAAC9F,cAAc,CAAC1E,KAAK,CAAC;QAC1B;MACJ;MACAwK,MAAI,CAAC/J,gBAAgB,GAAGlC,kBAAkB,CAAC6C,YAAY;MACvD,IAAIpB,KAAK,EAAE;QACPwK,MAAI,CAACpL,OAAO,CAACC,GAAG,CAACvB,QAAQ,CAACuF,WAAW,EAAG,6CAA4CrD,KAAM,IAAG,CAAC;MAClG,CAAC,MACI;QACDwK,MAAI,CAACpL,OAAO,CAACC,GAAG,CAACvB,QAAQ,CAACuF,WAAW,EAAE,0BAA0B,CAAC;MACtE;MACA,IAAImH,MAAI,CAACnK,sBAAsB,CAACkH,MAAM,KAAK,CAAC,EAAE;QAC1C,IAAI;UACAiD,MAAI,CAACnK,sBAAsB,CAACiK,OAAO,CAAEC,CAAC,IAAKA,CAAC,CAACP,KAAK,CAACQ,MAAI,EAAE,CAACxK,KAAK,CAAC,CAAC,CAAC;QACtE,CAAC,CACD,OAAOqC,CAAC,EAAE;UACNmI,MAAI,CAACpL,OAAO,CAACC,GAAG,CAACvB,QAAQ,CAACuD,KAAK,EAAG,iDAAgDrB,KAAM,kBAAiBqC,CAAE,IAAG,CAAC;QACnH;QACA;QACA,IAAImI,MAAI,CAAC/J,gBAAgB,KAAKlC,kBAAkB,CAAC6C,YAAY,EAAE;UAC3DoJ,MAAI,CAACpL,OAAO,CAACC,GAAG,CAACvB,QAAQ,CAACgE,KAAK,EAAE,uFAAuF,CAAC;UACzH;QACJ;MACJ;MACA,OAAO+I,cAAc,KAAK,IAAI,EAAE;QAC5BL,MAAI,CAACpL,OAAO,CAACC,GAAG,CAACvB,QAAQ,CAACuF,WAAW,EAAG,4BAA2BsH,yBAA0B,kBAAiBE,cAAe,MAAK,CAAC;QACnI,MAAM,IAAIlJ,OAAO,CAAEe,OAAO,IAAK;UAC3B8H,MAAI,CAAChG,qBAAqB,GAAGuE,UAAU,CAACrG,OAAO,EAAEmI,cAAc,CAAC;QACpE,CAAC,CAAC;QACFL,MAAI,CAAChG,qBAAqB,GAAGhC,SAAS;QACtC,IAAIgI,MAAI,CAAC/J,gBAAgB,KAAKlC,kBAAkB,CAAC6C,YAAY,EAAE;UAC3DoJ,MAAI,CAACpL,OAAO,CAACC,GAAG,CAACvB,QAAQ,CAACgE,KAAK,EAAE,mFAAmF,CAAC;UACrH;QACJ;QACA,IAAI;UACA,MAAM0I,MAAI,CAACzI,cAAc,CAAC,CAAC;UAC3ByI,MAAI,CAAC/J,gBAAgB,GAAGlC,kBAAkB,CAAC6D,SAAS;UACpDoI,MAAI,CAACpL,OAAO,CAACC,GAAG,CAACvB,QAAQ,CAACuF,WAAW,EAAE,yCAAyC,CAAC;UACjF,IAAImH,MAAI,CAAClK,qBAAqB,CAACiH,MAAM,KAAK,CAAC,EAAE;YACzC,IAAI;cACAiD,MAAI,CAAClK,qBAAqB,CAACgK,OAAO,CAAEC,CAAC,IAAKA,CAAC,CAACP,KAAK,CAACQ,MAAI,EAAE,CAACA,MAAI,CAAC9L,UAAU,CAACuC,YAAY,CAAC,CAAC,CAAC;YAC5F,CAAC,CACD,OAAOoB,CAAC,EAAE;cACNmI,MAAI,CAACpL,OAAO,CAACC,GAAG,CAACvB,QAAQ,CAACuD,KAAK,EAAG,uDAAsDmJ,MAAI,CAAC9L,UAAU,CAACuC,YAAa,kBAAiBoB,CAAE,IAAG,CAAC;YAChJ;UACJ;UACA;QACJ,CAAC,CACD,OAAOA,CAAC,EAAE;UACNmI,MAAI,CAACpL,OAAO,CAACC,GAAG,CAACvB,QAAQ,CAACuF,WAAW,EAAG,8CAA6ChB,CAAE,IAAG,CAAC;UAC3F,IAAImI,MAAI,CAAC/J,gBAAgB,KAAKlC,kBAAkB,CAAC6C,YAAY,EAAE;YAC3DoJ,MAAI,CAACpL,OAAO,CAACC,GAAG,CAACvB,QAAQ,CAACgE,KAAK,EAAG,4BAA2B0I,MAAI,CAAC/J,gBAAiB,4EAA2E,CAAC;YAC/J;YACA,IAAI+J,MAAI,CAAC/J,gBAAgB,KAAKlC,kBAAkB,CAACgG,aAAa,EAAE;cAC5DiG,MAAI,CAAC9F,cAAc,CAAC,CAAC;YACzB;YACA;UACJ;UACAkG,UAAU,GAAGvI,CAAC,YAAYhB,KAAK,GAAGgB,CAAC,GAAG,IAAIhB,KAAK,CAACgB,CAAC,CAAC0I,QAAQ,CAAC,CAAC,CAAC;UAC7DF,cAAc,GAAGL,MAAI,CAACM,kBAAkB,CAACH,yBAAyB,EAAE,EAAEhC,IAAI,CAAC+B,GAAG,CAAC,CAAC,GAAGD,kBAAkB,EAAEG,UAAU,CAAC;QACtH;MACJ;MACAJ,MAAI,CAACpL,OAAO,CAACC,GAAG,CAACvB,QAAQ,CAACuF,WAAW,EAAG,+CAA8CsF,IAAI,CAAC+B,GAAG,CAAC,CAAC,GAAGD,kBAAmB,WAAUE,yBAA0B,6CAA4C,CAAC;MACvMH,MAAI,CAAC9F,cAAc,CAAC,CAAC;IAAC;EAC1B;EACAoG,kBAAkBA,CAACE,kBAAkB,EAAEC,mBAAmB,EAAEC,WAAW,EAAE;IACrE,IAAI;MACA,OAAO,IAAI,CAACxL,gBAAgB,CAACyL,4BAA4B,CAAC;QACtDF,mBAAmB;QACnBD,kBAAkB;QAClBE;MACJ,CAAC,CAAC;IACN,CAAC,CACD,OAAO7I,CAAC,EAAE;MACN,IAAI,CAACjD,OAAO,CAACC,GAAG,CAACvB,QAAQ,CAACuD,KAAK,EAAG,6CAA4C2J,kBAAmB,KAAIC,mBAAoB,kBAAiB5I,CAAE,IAAG,CAAC;MAChJ,OAAO,IAAI;IACf;EACJ;EACA6H,yBAAyBA,CAAClK,KAAK,EAAE;IAC7B,MAAMoL,SAAS,GAAG,IAAI,CAAClL,UAAU;IACjC,IAAI,CAACA,UAAU,GAAG,CAAC,CAAC;IACpBmL,MAAM,CAACC,IAAI,CAACF,SAAS,CAAC,CACjBd,OAAO,CAAEiB,GAAG,IAAK;MAClB,MAAM/D,QAAQ,GAAG4D,SAAS,CAACG,GAAG,CAAC;MAC/B,IAAI;QACA/D,QAAQ,CAAC,IAAI,EAAExH,KAAK,CAAC;MACzB,CAAC,CACD,OAAOqC,CAAC,EAAE;QACN,IAAI,CAACjD,OAAO,CAACC,GAAG,CAACvB,QAAQ,CAACuD,KAAK,EAAG,wCAAuCrB,KAAM,kBAAiB/B,cAAc,CAACoE,CAAC,CAAE,EAAC,CAAC;MACxH;IACJ,CAAC,CAAC;EACN;EACA4B,iBAAiBA,CAAA,EAAG;IAChB,IAAI,IAAI,CAACgF,iBAAiB,EAAE;MACxBxE,YAAY,CAAC,IAAI,CAACwE,iBAAiB,CAAC;MACpC,IAAI,CAACA,iBAAiB,GAAGzG,SAAS;IACtC;EACJ;EACAc,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACwF,cAAc,EAAE;MACrBrE,YAAY,CAAC,IAAI,CAACqE,cAAc,CAAC;IACrC;EACJ;EACArC,iBAAiBA,CAACzB,UAAU,EAAEC,IAAI,EAAEuG,WAAW,EAAErG,SAAS,EAAE;IACxD,IAAIqG,WAAW,EAAE;MACb,IAAIrG,SAAS,CAACoC,MAAM,KAAK,CAAC,EAAE;QACxB,OAAO;UACH0C,SAAS,EAAEhF,IAAI;UACfE,SAAS;UACTkE,MAAM,EAAErE,UAAU;UAClBlE,IAAI,EAAEjD,WAAW,CAACkK;QACtB,CAAC;MACL,CAAC,MACI;QACD,OAAO;UACHkC,SAAS,EAAEhF,IAAI;UACfoE,MAAM,EAAErE,UAAU;UAClBlE,IAAI,EAAEjD,WAAW,CAACkK;QACtB,CAAC;MACL;IACJ,CAAC,MACI;MACD,MAAMnC,YAAY,GAAG,IAAI,CAACrF,aAAa;MACvC,IAAI,CAACA,aAAa,EAAE;MACpB,IAAI4E,SAAS,CAACoC,MAAM,KAAK,CAAC,EAAE;QACxB,OAAO;UACH0C,SAAS,EAAEhF,IAAI;UACfW,YAAY,EAAEA,YAAY,CAACmF,QAAQ,CAAC,CAAC;UACrC5F,SAAS;UACTkE,MAAM,EAAErE,UAAU;UAClBlE,IAAI,EAAEjD,WAAW,CAACkK;QACtB,CAAC;MACL,CAAC,MACI;QACD,OAAO;UACHkC,SAAS,EAAEhF,IAAI;UACfW,YAAY,EAAEA,YAAY,CAACmF,QAAQ,CAAC,CAAC;UACrC1B,MAAM,EAAErE,UAAU;UAClBlE,IAAI,EAAEjD,WAAW,CAACkK;QACtB,CAAC;MACL;IACJ;EACJ;EACA3B,cAAcA,CAAClB,OAAO,EAAEK,YAAY,EAAE;IAClC,IAAIL,OAAO,CAACqC,MAAM,KAAK,CAAC,EAAE;MACtB;IACJ;IACA;IACA,IAAI,CAAChC,YAAY,EAAE;MACfA,YAAY,GAAG5D,OAAO,CAACe,OAAO,CAAC,CAAC;IACpC;IACA;IACA;IACA,KAAK,MAAM+I,QAAQ,IAAIvG,OAAO,EAAE;MAC5BA,OAAO,CAACuG,QAAQ,CAAC,CAACC,SAAS,CAAC;QACxB1F,QAAQ,EAAEA,CAAA,KAAM;UACZT,YAAY,GAAGA,YAAY,CAACM,IAAI,CAAC,MAAM,IAAI,CAAChB,iBAAiB,CAAC,IAAI,CAAC0E,wBAAwB,CAACkC,QAAQ,CAAC,CAAC,CAAC;QAC3G,CAAC;QACDzL,KAAK,EAAG2L,GAAG,IAAK;UACZ,IAAItF,OAAO;UACX,IAAIsF,GAAG,YAAYtK,KAAK,EAAE;YACtBgF,OAAO,GAAGsF,GAAG,CAACtF,OAAO;UACzB,CAAC,MACI,IAAIsF,GAAG,IAAIA,GAAG,CAACZ,QAAQ,EAAE;YAC1B1E,OAAO,GAAGsF,GAAG,CAACZ,QAAQ,CAAC,CAAC;UAC5B,CAAC,MACI;YACD1E,OAAO,GAAG,eAAe;UAC7B;UACAd,YAAY,GAAGA,YAAY,CAACM,IAAI,CAAC,MAAM,IAAI,CAAChB,iBAAiB,CAAC,IAAI,CAAC0E,wBAAwB,CAACkC,QAAQ,EAAEpF,OAAO,CAAC,CAAC,CAAC;QACpH,CAAC;QACDJ,IAAI,EAAGC,IAAI,IAAK;UACZX,YAAY,GAAGA,YAAY,CAACM,IAAI,CAAC,MAAM,IAAI,CAAChB,iBAAiB,CAAC,IAAI,CAAC+G,wBAAwB,CAACH,QAAQ,EAAEvF,IAAI,CAAC,CAAC,CAAC;QACjH;MACJ,CAAC,CAAC;IACN;EACJ;EACAd,uBAAuBA,CAACH,IAAI,EAAE;IAC1B,MAAMC,OAAO,GAAG,EAAE;IAClB,MAAMC,SAAS,GAAG,EAAE;IACpB,KAAK,IAAI0G,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5G,IAAI,CAACsC,MAAM,EAAEsE,CAAC,EAAE,EAAE;MAClC,MAAMC,QAAQ,GAAG7G,IAAI,CAAC4G,CAAC,CAAC;MACxB,IAAI,IAAI,CAACE,aAAa,CAACD,QAAQ,CAAC,EAAE;QAC9B,MAAML,QAAQ,GAAG,IAAI,CAAClL,aAAa;QACnC,IAAI,CAACA,aAAa,EAAE;QACpB;QACA2E,OAAO,CAACuG,QAAQ,CAAC,GAAGK,QAAQ;QAC5B3G,SAAS,CAAC8B,IAAI,CAACwE,QAAQ,CAACV,QAAQ,CAAC,CAAC,CAAC;QACnC;QACA9F,IAAI,CAACqC,MAAM,CAACuE,CAAC,EAAE,CAAC,CAAC;MACrB;IACJ;IACA,OAAO,CAAC3G,OAAO,EAAEC,SAAS,CAAC;EAC/B;EACA4G,aAAaA,CAACC,GAAG,EAAE;IACf;IACA,OAAOA,GAAG,IAAIA,GAAG,CAACN,SAAS,IAAI,OAAOM,GAAG,CAACN,SAAS,KAAK,UAAU;EACtE;EACApG,uBAAuBA,CAACN,UAAU,EAAEC,IAAI,EAAEE,SAAS,EAAE;IACjD,MAAMS,YAAY,GAAG,IAAI,CAACrF,aAAa;IACvC,IAAI,CAACA,aAAa,EAAE;IACpB,IAAI4E,SAAS,CAACoC,MAAM,KAAK,CAAC,EAAE;MACxB,OAAO;QACH0C,SAAS,EAAEhF,IAAI;QACfW,YAAY,EAAEA,YAAY,CAACmF,QAAQ,CAAC,CAAC;QACrC5F,SAAS;QACTkE,MAAM,EAAErE,UAAU;QAClBlE,IAAI,EAAEjD,WAAW,CAACoO;MACtB,CAAC;IACL,CAAC,MACI;MACD,OAAO;QACHhC,SAAS,EAAEhF,IAAI;QACfW,YAAY,EAAEA,YAAY,CAACmF,QAAQ,CAAC,CAAC;QACrC1B,MAAM,EAAErE,UAAU;QAClBlE,IAAI,EAAEjD,WAAW,CAACoO;MACtB,CAAC;IACL;EACJ;EACAtG,uBAAuBA,CAACuG,EAAE,EAAE;IACxB,OAAO;MACHtG,YAAY,EAAEsG,EAAE;MAChBpL,IAAI,EAAEjD,WAAW,CAACsO;IACtB,CAAC;EACL;EACAP,wBAAwBA,CAACM,EAAE,EAAEhG,IAAI,EAAE;IAC/B,OAAO;MACHN,YAAY,EAAEsG,EAAE;MAChBhG,IAAI;MACJpF,IAAI,EAAEjD,WAAW,CAACoK;IACtB,CAAC;EACL;EACAsB,wBAAwBA,CAAC2C,EAAE,EAAElM,KAAK,EAAE4G,MAAM,EAAE;IACxC,IAAI5G,KAAK,EAAE;MACP,OAAO;QACHA,KAAK;QACL4F,YAAY,EAAEsG,EAAE;QAChBpL,IAAI,EAAEjD,WAAW,CAACkI;MACtB,CAAC;IACL;IACA,OAAO;MACHH,YAAY,EAAEsG,EAAE;MAChBtF,MAAM;MACN9F,IAAI,EAAEjD,WAAW,CAACkI;IACtB,CAAC;EACL;EACAjB,mBAAmBA,CAAA,EAAG;IAClB,OAAO;MAAEhE,IAAI,EAAEjD,WAAW,CAACqK;IAAM,CAAC;EACtC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}