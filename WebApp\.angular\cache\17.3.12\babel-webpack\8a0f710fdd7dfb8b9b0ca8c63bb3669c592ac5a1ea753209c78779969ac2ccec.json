{"ast": null, "code": "class Module {\n  static DEFAULTS = {};\n  constructor(quill) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    this.quill = quill;\n    this.options = options;\n  }\n}\nexport default Module;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "DEFAULTS", "constructor", "quill", "options", "arguments", "length", "undefined"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/quill/core/module.js"], "sourcesContent": ["class Module {\n  static DEFAULTS = {};\n  constructor(quill) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    this.quill = quill;\n    this.options = options;\n  }\n}\nexport default Module;\n"], "mappings": "AAAA,MAAMA,MAAM,CAAC;EACX,OAAOC,QAAQ,GAAG,CAAC,CAAC;EACpBC,WAAWA,CAACC,KAAK,EAAE;IACjB,IAAIC,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpF,IAAI,CAACF,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,OAAO,GAAGA,OAAO;EACxB;AACF;AACA,eAAeJ,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}