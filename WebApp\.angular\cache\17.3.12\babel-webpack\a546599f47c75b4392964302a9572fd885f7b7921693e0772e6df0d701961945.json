{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/source/ai-hub/WebApp/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./sidebar.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./sidebar.component.css?ngResource\";\nimport { Component, inject } from '@angular/core';\nimport { TogglingService } from '../../toggling.service';\nimport { ChatListService } from '../../services/chat-list.service';\nimport { ThemeService } from '../../../shared/services/theam.service';\nimport { NavigationEnd, Router, RouterLink, ActivatedRoute } from '@angular/router';\nimport { ChatServiceProxy, ChatRequestDto } from '../../../shared/service-proxies/service-proxies';\nimport { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { AuthService } from '../../../shared/services/auth.service';\nimport { NzButtonModule } from 'ng-zorro-antd/button';\nimport { NzPopoverModule } from 'ng-zorro-antd/popover';\nimport { NzMessageService } from 'ng-zorro-antd/message';\nimport { BehaviorSubject } from 'rxjs';\nimport { filter } from 'rxjs/operators';\nimport { NotesService } from '../../MyNotesProjects/services/notes.service';\nimport { DailyInsightsSidebarComponent } from '../@rightSideComponents/daily-insights-sidebar/daily-insights-sidebar.component';\nimport { RightsidebarchatComponent } from '../@leftSideComponents/rightsidebarchat/rightsidebarchat.component';\nimport { SettingsidebarcomponentComponent } from '../@leftSideComponents/settingsidebarcomponent/settingsidebarcomponent.component';\nimport { NotesSidebarComponent } from '../@leftSideComponents/notes-sidebar/notes-sidebar.component';\nlet SidebarComponent = class SidebarComponent {\n  constructor(_chatService, authService, nzMessageService) {\n    this._chatService = _chatService;\n    this.authService = authService;\n    this.nzMessageService = nzMessageService;\n    this.togglingService = inject(TogglingService);\n    this.chatListService = inject(ChatListService);\n    this.router = inject(Router);\n    this.notesService = inject(NotesService);\n    this.activatedRoute = inject(ActivatedRoute);\n    this.themeService = inject(ThemeService);\n    this.groupedChats = {\n      History: []\n    };\n    this.filteredGroupedChats = {\n      ...this.groupedChats\n    };\n    this.isAllChatsOpen = true;\n    this.activeTab = 'all';\n    this.panelVisible = true;\n    // Add this property to track if in workspace mode\n    this.isWorkspaceMode = false;\n    // Notes-related properties\n    this.favoriteNotes = [];\n    this.recentNotes = [];\n    this.notesByCategory = [];\n    this.isNotesSubmenuOpen = false;\n    this.recentNotesSource = new BehaviorSubject(5); // Default limit\n    // Add this property to track if in admin mode\n    this.isSettingsMode = false;\n    // Property to track current admin tab\n    this.activeAdminTab = 'prompt-library';\n    // Add this property to track if in DailyInsight mode\n    this.isDailyInsightMode = false;\n    // Add this property to track if in Notes mode\n    this.isNotesMode = false;\n    // Properties for chat-sidebar component\n    this.userInput = new ChatRequestDto();\n    this.selectedAgent = '';\n    this.originalOrder = (a, b) => {\n      const order = ['History'];\n      return order.indexOf(a.key) - order.indexOf(b.key);\n    };\n    this.hasMoreMessages = true;\n    this.originalChatList = [];\n    this.pinnedChats = [];\n    this.favoriteChats = [];\n    this.archivedChats = [];\n    this.notes = [];\n    this.counter = 1;\n    this.hasWorkspaces = false;\n    this.workspaceName = '';\n    this.tabConfig = {\n      all: {\n        title: 'All Chats',\n        chats: () => this.groupedChats,\n        isGrouped: true,\n        hasMore: true\n      },\n      'pinned-history': {\n        title: 'Pinned Chats',\n        chats: () => this.groupChatsByDate(this.pinnedChats),\n        isGrouped: true\n      },\n      favorite: {\n        title: 'Favorite Chats',\n        chats: () => this.groupChatsByDate(this.favoriteChats),\n        isGrouped: true\n      },\n      archive: {\n        title: 'Archive Chats',\n        chats: () => this.groupChatsByDate(this.archivedChats),\n        isGrouped: true\n      },\n      notes: {\n        title: 'My Notes',\n        chats: () => [],\n        isGrouped: false\n      },\n      history: {\n        title: 'Chat History',\n        chats: () => [],\n        isGrouped: false\n      }\n    };\n    this.isDarkMode = false;\n    this.isLoading = false;\n    this.isLoadingMore = false;\n    this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(event => {\n      // Get the current URL\n      const url = event.url;\n      // Update mode flags\n      this.isDailyInsightMode = url === '/' || url === '';\n      this.isWorkspaceMode = url.includes('/workspaces') && !url.includes('/chat');\n      this.isSettingsMode = url.includes('/settings');\n      this.isNotesMode = url.includes('/notes');\n      // Update active admin tab when in settings\n      if (this.isSettingsMode) {\n        const urlParts = url.split('/');\n        // Get the last segment of the URL\n        this.activeAdminTab = urlParts[urlParts.length - 1];\n      } else {\n        // Reset activeAdminTab when not in settings\n        this.activeAdminTab = '';\n      }\n      // Update workspace name and reset counter when navigating between workspaces\n      const oldWorkspaceName = this.workspaceName;\n      if (url.includes('workspaces')) {\n        this.workspaceName = url.split('/')[2];\n        // Only reset counter if workspace changed\n        if (oldWorkspaceName !== this.workspaceName) {\n          this.counter = 1;\n        }\n      } else {\n        if (this.workspaceName !== '') {\n          // Only reset if we're changing from a workspace to non-workspace\n          this.workspaceName = '';\n          this.counter = 1;\n        }\n      }\n      if (this.workspaceName?.length > 0) {\n        this.hasWorkspaces = true;\n      }\n      // Store the current tab before data reloads\n      const previousTab = this.activeTab;\n      // Reload data when navigating between different sections or workspaces\n      const needsReload = oldWorkspaceName !== this.workspaceName ||\n      // Workspace changed\n      url.includes('/chat') && !event.urlAfterRedirects.includes('/chat') ||\n      // Navigated to/from chat\n      this.isDailyInsightMode !== (url === '/' || url === ''); // Daily insight mode changed\n      if (needsReload) {\n        console.log('Navigation requires data reload');\n        // Only load chat lists if we're not in daily insight mode\n        if (!this.isDailyInsightMode) {\n          console.log('Not in daily insight mode, loading chat lists');\n          this.loadChatList();\n          // Only load data for the active tab\n          if (this.activeTab === 'pinned-history') {\n            this.loadPinnedChats();\n          } else if (this.activeTab === 'favorite') {\n            this.loadFavoriteChats();\n          } else if (this.activeTab === 'archive') {\n            this.loadArchivedChats();\n          }\n        } else {\n          console.log('In daily insight mode, skipping chat list loading');\n        }\n      }\n      // Restore the previous tab\n      if (previousTab !== 'all') {\n        this.activeTab = previousTab;\n        // Set the appropriate filtered list based on active tab\n        switch (this.activeTab) {\n          case 'pinned-history':\n            this.filteredGroupedChats = this.groupChatsByDate(this.pinnedChats);\n            break;\n          case 'favorite':\n            this.filteredGroupedChats = this.groupChatsByDate(this.favoriteChats);\n            break;\n          case 'archive':\n            this.filteredGroupedChats = this.groupChatsByDate(this.archivedChats);\n            break;\n          default:\n            // For 'all' tab or other tabs, keep default behavior\n            break;\n        }\n      }\n    });\n    this.isDarkMode = document.body.classList.contains('dark-mode');\n  }\n  ngOnInit() {\n    // Initialize based on current URL\n    const currentUrl = this.router.url;\n    this.isSettingsMode = currentUrl.includes('/settings');\n    this.isWorkspaceMode = currentUrl.includes('/workspaces') && !currentUrl.includes('/chat');\n    this.isDailyInsightMode = currentUrl === '/' || currentUrl === '';\n    this.isNotesMode = currentUrl.includes('/notes');\n    // Extract workspace name from URL if available\n    if (currentUrl.includes('workspaces')) {\n      this.workspaceName = currentUrl.split('/')[2];\n      if (this.workspaceName?.length > 0) {\n        this.hasWorkspaces = true;\n      }\n    }\n    // Extract active admin tab from URL if in settings mode\n    if (this.isSettingsMode) {\n      const urlParts = currentUrl.split('/');\n      if (urlParts.length > 2) {\n        this.activeAdminTab = urlParts[2];\n      }\n    }\n    // Load chat list data immediately on initialization\n    console.log('Initializing sidebar component, loading chat list data');\n    this.loadChatList();\n    // Load other data based on active tab\n    if (this.activeTab === 'pinned-history') {\n      this.loadPinnedChats();\n    } else if (this.activeTab === 'favorite') {\n      this.loadFavoriteChats();\n    } else if (this.activeTab === 'archive') {\n      this.loadArchivedChats();\n    }\n  }\n  loadChatList() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      // Show loading indicator if we're on the all chats tab\n      if (_this.activeTab === 'all') {\n        _this.isLoading = true;\n      }\n      try {\n        console.log('Loading chat list for workspace:', _this.workspaceName);\n        // Make sure counter is initialized\n        if (!_this.counter || _this.counter < 1) {\n          _this.counter = 1;\n        }\n        let res = yield _this._chatService.list(_this.workspaceName, _this.counter, 15).toPromise();\n        if (res && res.messages) {\n          console.log('Chat list loaded successfully:', res.messages.length, 'messages');\n          _this.chatListService.chatList = res.messages;\n          _this.originalChatList = res.messages;\n          _this.hasMoreMessages = res.hasMoreMessages;\n          _this.chatListService.groupChatsByDate();\n          _this.groupedChats = _this.chatListService.groupedChats;\n          // Only update filteredGroupedChats if we're on the 'all' tab\n          // This prevents losing our filtered view when clicking on a chat\n          if (_this.activeTab === 'all') {\n            _this.filteredGroupedChats = {\n              ..._this.groupedChats\n            };\n          } else {\n            // For other tabs, maintain the existing filtered list by refreshing it\n            switch (_this.activeTab) {\n              case 'pinned-history':\n                _this.filteredGroupedChats = _this.groupChatsByDate(_this.pinnedChats);\n                break;\n              case 'favorite':\n                _this.filteredGroupedChats = _this.groupChatsByDate(_this.favoriteChats);\n                break;\n              case 'archive':\n                _this.filteredGroupedChats = _this.groupChatsByDate(_this.archivedChats);\n                break;\n            }\n          }\n          _this.counter++;\n        } else {\n          console.warn('Chat list response was empty or invalid:', res);\n          // Initialize with empty data\n          _this.initializeEmptyChats();\n        }\n      } catch (err) {\n        console.error('Error loading chat list:', err);\n        // Initialize with empty data\n        _this.initializeEmptyChats();\n      } finally {\n        // Always make sure to turn off loading indicator\n        _this.isLoading = false;\n      }\n    })();\n  }\n  // Helper method to initialize empty chat lists\n  initializeEmptyChats() {\n    this.chatListService.chatList = [];\n    this.originalChatList = [];\n    this.hasMoreMessages = false;\n    this.chatListService.groupedChats = {\n      Today: [],\n      Yesterday: [],\n      'Last 7 Days': [],\n      'Last 30 Days': [],\n      Older: []\n    };\n    this.groupedChats = this.chatListService.groupedChats;\n    this.filteredGroupedChats = {\n      ...this.groupedChats\n    };\n  }\n  loadMoreChatList() {\n    // Show loading indicator for \"load more\"\n    this.isLoadingMore = true;\n    this._chatService.list(this.workspaceName, this.counter, 15).subscribe({\n      next: res => {\n        this.counter++;\n        if (res) {\n          this.chatListService.chatList.push(...res.messages);\n          this.originalChatList.push(...res.messages);\n          this.hasMoreMessages = res.hasMoreMessages;\n          this.chatListService.groupChatsByDate();\n          this.groupedChats = this.chatListService.groupedChats;\n          // Only update filteredGroupedChats if we're on the 'all' tab\n          if (this.activeTab === 'all') {\n            this.filteredGroupedChats = {\n              ...this.groupedChats\n            };\n          }\n        }\n        this.isLoadingMore = false;\n      },\n      error: err => {\n        console.error('Error loading more chats:', err);\n        this.isLoadingMore = false;\n      }\n    });\n  }\n  loadPinnedChats() {\n    // Show loading indicator if we're on the pinned tab\n    if (this.activeTab === 'pinned-history') {\n      this.isLoading = true;\n    }\n    this._chatService.pinned(this.workspaceName).subscribe({\n      next: res => {\n        if (res && Array.isArray(res)) {\n          this.pinnedChats = res;\n          if (this.activeTab === 'pinned-history') {\n            this.filteredGroupedChats = this.groupChatsByDate(this.pinnedChats);\n            this.isLoading = false;\n          }\n        } else {\n          this.pinnedChats = [];\n          if (this.activeTab === 'pinned-history') {\n            this.filteredGroupedChats = {};\n            this.isLoading = false;\n          }\n        }\n      },\n      error: err => {\n        console.error('Error loading pinned chats:', err);\n        this.pinnedChats = [];\n        if (this.activeTab === 'pinned-history') {\n          this.filteredGroupedChats = {};\n          this.isLoading = false;\n        }\n      }\n    });\n  }\n  loadFavoriteChats() {\n    // Show loading indicator if we're on the favorites tab\n    if (this.activeTab === 'favorite') {\n      this.isLoading = true;\n    }\n    this._chatService.favorites(this.workspaceName).subscribe({\n      next: res => {\n        if (res && Array.isArray(res)) {\n          this.favoriteChats = res;\n          if (this.activeTab === 'favorite') {\n            this.filteredGroupedChats = this.groupChatsByDate(this.favoriteChats);\n            this.isLoading = false;\n          }\n        } else {\n          this.favoriteChats = [];\n          if (this.activeTab === 'favorite') {\n            this.filteredGroupedChats = {};\n            this.isLoading = false;\n          }\n        }\n      },\n      error: err => {\n        console.error('Error loading favorite chats:', err);\n        this.favoriteChats = [];\n        if (this.activeTab === 'favorite') {\n          this.filteredGroupedChats = {};\n          this.isLoading = false;\n        }\n      }\n    });\n  }\n  loadArchivedChats() {\n    // Show loading indicator if we're on the archive tab\n    if (this.activeTab === 'archive') {\n      this.isLoading = true;\n    }\n    this._chatService.archived(this.workspaceName).subscribe({\n      next: res => {\n        if (res && Array.isArray(res)) {\n          this.archivedChats = res;\n          if (this.activeTab === 'archive') {\n            this.filteredGroupedChats = this.groupChatsByDate(this.archivedChats);\n            this.isLoading = false;\n          }\n        } else {\n          this.archivedChats = [];\n          if (this.activeTab === 'archive') {\n            this.filteredGroupedChats = {};\n            this.isLoading = false;\n          }\n        }\n      },\n      error: err => {\n        console.error('Error loading archived chats:', err);\n        this.archivedChats = [];\n        if (this.activeTab === 'archive') {\n          this.filteredGroupedChats = {};\n          this.isLoading = false;\n        }\n      }\n    });\n  }\n  toggleTab(tab) {\n    // If we're already on this tab, don't do anything\n    if (this.activeTab === tab) {\n      return;\n    }\n    // Update the activeTab\n    this.activeTab = tab;\n    this.isAllChatsOpen = true;\n    // Ensure we're in chat mode (not settings or workspace mode)\n    this.isSettingsMode = false;\n    this.isWorkspaceMode = false;\n    this.isDailyInsightMode = false;\n    // Apply changes based on the selected tab\n    switch (tab) {\n      case 'pinned-history':\n        // Check if we already have pinned chats data\n        if (!this.pinnedChats || this.pinnedChats.length === 0) {\n          // Only load if we don't have data\n          this.loadPinnedChats();\n        } else {\n          // Use existing data\n          this.filteredGroupedChats = this.groupChatsByDate(this.pinnedChats);\n        }\n        break;\n      case 'favorite':\n        // Check if we already have favorite chats data\n        if (!this.favoriteChats || this.favoriteChats.length === 0) {\n          // Only load if we don't have data\n          this.loadFavoriteChats();\n        } else {\n          // Use existing data\n          this.filteredGroupedChats = this.groupChatsByDate(this.favoriteChats);\n        }\n        break;\n      case 'archive':\n        // Check if we already have archived chats data\n        if (!this.archivedChats || this.archivedChats.length === 0) {\n          // Only load if we don't have data\n          this.loadArchivedChats();\n        } else {\n          // Use existing data\n          this.filteredGroupedChats = this.groupChatsByDate(this.archivedChats);\n        }\n        break;\n      case 'all':\n      default:\n        this.isNotesSubmenuOpen = false;\n        this.filteredGroupedChats = this.groupedChats;\n        break;\n    }\n  }\n  toggleNotesSubmenu() {\n    this.isNotesSubmenuOpen = !this.isNotesSubmenuOpen;\n  }\n  trackByNoteCategory(index, category) {\n    return category.key;\n  }\n  trackByChatId(index, chat) {\n    return chat.id;\n  }\n  /**\n   * Handles the agent selected event from the chat-sidebar component\n   * @param agent The selected agent\n   */\n  onAgentSelected(agent) {\n    if (!agent) return;\n    // Set the selected agent\n    this.selectedAgent = agent.agentName;\n    // Store the agent in the user input\n    this.userInput.selectedAgentName = agent.agentName;\n    // Add the agent mention to the message if the input is empty\n    if (!this.userInput.message || this.userInput.message.trim() === '') {\n      this.userInput.message = `@${agent.agentName} `;\n    }\n  }\n  filterChats(event) {\n    event.stopPropagation();\n    const searchTerm = event.target.value.toLowerCase();\n    if (!searchTerm) {\n      this.filteredGroupedChats = {\n        ...this.groupedChats\n      };\n      return;\n    }\n    this.filteredGroupedChats = {};\n    Object.keys(this.groupedChats).forEach(group => {\n      this.filteredGroupedChats[group] = this.groupedChats[group].filter(chat => chat.title.toLowerCase().includes(searchTerm));\n    });\n  }\n  toggleAllChats(event) {\n    event.stopPropagation();\n    this.isAllChatsOpen = !this.isAllChatsOpen;\n  }\n  addNewChats(event) {\n    event.stopPropagation();\n    this.isAllChatsOpen = true;\n    this.chatListService.chatId = 0; // Reset chatId to 0 for new chat\n    if (this.workspaceName) {\n      // If workspaceName is available, navigate to the chat within that workspace\n      this.router.navigate(['workspaces', this.workspaceName, 'chat']);\n    } else {\n      // If no workspace, navigate to chat\n      this.router.navigate(['/chat']);\n    }\n  }\n  toggleChat(event, chat) {\n    // Set the chat ID and navigate to the chat\n    this.chatListService.chatId = chat.id;\n    // Navigate based on workspace context\n    if (this.workspaceName) {\n      this.router.navigate(['workspaces', this.workspaceName, 'chat', chat.id]);\n    } else {\n      this.router.navigate(['/chat', chat.id]);\n    }\n    event.stopPropagation();\n    chat.isToggled = !chat.isToggled;\n  }\n  openSidebar() {\n    this.togglingService.isNavbarOpen = true;\n  }\n  addToPinnedChat(chat) {\n    this.chatId = chat.id;\n    this._chatService.pin(this.chatId, !chat.isPinned).subscribe(res => {\n      if (res.isPinned) {\n        this.nzMessageService.success('Chat pinned successfully!');\n        this.pinnedChats = [...this.pinnedChats, res];\n      } else {\n        this.nzMessageService.success('Chat unpinned successfully!');\n        this.pinnedChats = this.pinnedChats.filter(c => c.id !== res.id);\n      }\n      const index = this.chatListService.chatList.findIndex(c => c.id === res.id);\n      if (index !== -1) {\n        this.chatListService.chatList[index] = res;\n        this.chatListService.groupChatsByDate();\n        this.groupedChats = this.chatListService.groupedChats;\n        this.filteredGroupedChats = {\n          ...this.groupedChats\n        };\n      }\n    });\n  }\n  addToFavChat(chat) {\n    this.chatId = chat.id;\n    this._chatService.favorite(this.chatId, !chat.isFavorite).subscribe(res => {\n      if (res.isFavorite) {\n        this.nzMessageService.success('Chat favorited successfully!');\n        this.favoriteChats = [...this.favoriteChats, res];\n      } else {\n        this.nzMessageService.success('Chat unfavorited successfully!');\n        this.favoriteChats = this.favoriteChats.filter(c => c.id !== res.id);\n      }\n      const index = this.chatListService.chatList.findIndex(c => c.id === res.id);\n      if (index !== -1) {\n        this.chatListService.chatList[index] = res;\n        this.chatListService.groupChatsByDate();\n        this.groupedChats = this.chatListService.groupedChats;\n        this.filteredGroupedChats = {\n          ...this.groupedChats\n        };\n      }\n    });\n  }\n  addToArchiveChat(chat) {\n    this.chatId = chat.id;\n    this._chatService.archive(this.chatId, !chat.isArchived).subscribe(res => {\n      if (res.isArchived) {\n        this.nzMessageService.success('Chat archived successfully!');\n        this.archivedChats = [...this.archivedChats, res];\n        this.chatListService.chatList = this.chatListService.chatList.filter(c => c.id !== res.id);\n      } else {\n        this.nzMessageService.success('Chat unarchived successfully!');\n        this.archivedChats = this.archivedChats.filter(c => c.id !== res.id);\n        this.chatListService.chatList.push(res);\n      }\n      this.chatListService.groupChatsByDate();\n      this.groupedChats = this.chatListService.groupedChats;\n      this.filteredGroupedChats = {\n        ...this.groupedChats\n      };\n    });\n  }\n  getCurrentTabChats() {\n    if (this.activeTab === 'all') {\n      return this.groupedChats;\n    }\n    const chats = this.filteredGroupedChats;\n    if (this.tabConfig[this.activeTab].isGrouped) {\n      return Object.keys(chats).length > 0 ? chats : {\n        'No Chats': []\n      };\n    }\n    return chats;\n  }\n  getCurrentTabTitle() {\n    return this.tabConfig[this.activeTab].title;\n  }\n  isCurrentTabGrouped() {\n    return this.tabConfig[this.activeTab].isGrouped;\n  }\n  hasMoreForCurrentTab() {\n    return this.tabConfig[this.activeTab].hasMore && this.hasMoreMessages;\n  }\n  toggleTheme() {\n    this.isDarkMode = !this.isDarkMode;\n    if (this.isDarkMode) {\n      document.body.classList.add('dark-mode');\n    } else {\n      document.body.classList.remove('dark-mode');\n    }\n  }\n  groupChatsByDate(chats) {\n    // Return early if the chats array is null, undefined, or empty\n    if (!chats || chats.length === 0) {\n      return {};\n    }\n    const grouped = {};\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(today.getDate() - 1);\n    const sevenDaysAgo = new Date(today);\n    sevenDaysAgo.setDate(today.getDate() - 7);\n    const thirtyDaysAgo = new Date(today);\n    thirtyDaysAgo.setDate(today.getDate() - 30);\n    chats.forEach(chat => {\n      if (!chat || chat.createdDate === undefined) {\n        return;\n      }\n      try {\n        let date;\n        if (typeof chat.createdDate === 'string') {\n          date = new Date(chat.createdDate);\n        } else if (chat.createdDate && typeof chat.createdDate === 'object' && chat.createdDate.toString) {\n          date = new Date(chat.createdDate.toString());\n        } else {\n          return;\n        }\n        if (isNaN(date.getTime())) {\n          return;\n        }\n        let groupKey;\n        if (this.isSameDay(date, today)) {\n          groupKey = 'Today';\n        } else if (this.isSameDay(date, yesterday)) {\n          groupKey = 'Yesterday';\n        } else if (date >= sevenDaysAgo) {\n          groupKey = 'Previous 7 Days';\n        } else if (date >= thirtyDaysAgo) {\n          groupKey = 'Previous 30 Days';\n        } else {\n          groupKey = date.toLocaleDateString('en-US', {\n            month: 'long',\n            year: 'numeric'\n          });\n        }\n        if (!grouped[groupKey]) {\n          grouped[groupKey] = [];\n        }\n        grouped[groupKey].push(chat);\n      } catch (error) {\n        console.error('Error processing chat date:', error);\n      }\n    });\n    // Sort chats within each group\n    for (const key in grouped) {\n      grouped[key].sort((a, b) => {\n        try {\n          const dateA = new Date(a.createdDate.toString()).getTime();\n          const dateB = new Date(b.createdDate.toString()).getTime();\n          return dateB - dateA;\n        } catch (error) {\n          return 0;\n        }\n      });\n    }\n    return grouped;\n  }\n  isSameDay(date1, date2) {\n    return date1.getFullYear() === date2.getFullYear() && date1.getMonth() === date2.getMonth() && date1.getDate() === date2.getDate();\n  }\n  // Method to navigate to Daily Insight\n  navigateToDailyInsight(event) {\n    event.stopPropagation();\n    // Reset chatId to 0\n    this.chatListService.chatId = 0;\n    // Toggle/hide the sidebar\n    // if (this.togglingService.isNavbarOpen) {\n    // this.togglingService.toggleNavbar();\n    // }\n    if (!this.togglingService.isNavbarOpen) {\n      this.togglingService.toggleNavbar();\n    }\n    // Navigate to root/daily insight\n    this.router.navigate(['/']);\n  }\n  // Update navigateToSettings to handle event and toggle sidebar\n  navigateToSettings(event, section) {\n    event.stopPropagation();\n    // Set mode flags\n    this.isSettingsMode = true;\n    this.isWorkspaceMode = false;\n    this.isDailyInsightMode = false;\n    // Update the active tab\n    this.activeAdminTab = section;\n    // Make sure the sidebar is open\n    if (!this.togglingService.isNavbarOpen) {\n      this.togglingService.toggleNavbar();\n    }\n    // Navigate to the settings section\n    this.router.navigate(['/settings', section]);\n  }\n  // Method to handle navigation from the chat icon in narrow sidebar\n  navigateToChatPage(event) {\n    event.stopPropagation();\n    // Switch from admin mode to chat mode if in admin mode\n    this.isSettingsMode = false;\n    this.isDailyInsightMode = false;\n    if (!this.togglingService.isNavbarOpen) {\n      this.togglingService.toggleNavbar();\n    }\n    // Open the sidebar and select 'all' chat tab\n    this.openSidebar();\n    this.activeTab = 'all';\n    // Reset chatId to 0 for new chat\n    this.chatListService.chatId = 0;\n    // Navigate based on workspace context (same logic as addNewChats)\n    if (this.workspaceName) {\n      // If workspaceName is available, navigate to the chat within that workspace\n      this.router.navigate(['workspaces', this.workspaceName, 'chat']);\n    } else {\n      // If no workspace, navigate to chat\n      this.router.navigate(['/chat']);\n    }\n  }\n  // Method to handle workspace navigation and toggle sidebar\n  navigateToWorkspace(event) {\n    event.stopPropagation();\n    // Reset chatId to 0\n    this.chatListService.chatId = 0;\n    // Toggle/hide the sidebar\n    if (this.togglingService.isNavbarOpen) {\n      this.togglingService.toggleNavbar();\n    }\n    // Navigate to workspaces\n    this.router.navigate(['/workspaces']);\n  }\n  // Daily insight mode methods moved to DailyInsightsSidebarComponent\n  // Method to navigate to Notes\n  navigateToNotes(event) {\n    event.stopPropagation();\n    // Reset chatId to 0\n    this.chatListService.chatId = 0;\n    // Set mode flags\n    this.isSettingsMode = false;\n    this.isWorkspaceMode = false;\n    this.isDailyInsightMode = false;\n    this.isNotesMode = true;\n    // Open the sidebar if it's closed, keep it open if already open\n    if (!this.togglingService.isNavbarOpen) {\n      this.togglingService.toggleNavbar();\n    }\n    // Navigate to notes\n    this.router.navigate(['/notes']);\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: ChatServiceProxy\n    }, {\n      type: AuthService\n    }, {\n      type: NzMessageService\n    }];\n  }\n};\nSidebarComponent = __decorate([Component({\n  selector: 'app-sidebar',\n  standalone: true,\n  imports: [ServiceProxyModule, CommonModule, FormsModule, RouterLink, NzButtonModule, NzPopoverModule, DailyInsightsSidebarComponent, RightsidebarchatComponent, SettingsidebarcomponentComponent, NotesSidebarComponent],\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], SidebarComponent);\nexport { SidebarComponent };", "map": {"version": 3, "names": ["Component", "inject", "TogglingService", "ChatListService", "ThemeService", "NavigationEnd", "Router", "RouterLink", "ActivatedRoute", "ChatServiceProxy", "ChatRequestDto", "ServiceProxyModule", "CommonModule", "FormsModule", "AuthService", "NzButtonModule", "NzPopoverModule", "NzMessageService", "BehaviorSubject", "filter", "NotesService", "DailyInsightsSidebarComponent", "RightsidebarchatComponent", "SettingsidebarcomponentComponent", "NotesSidebarComponent", "SidebarComponent", "constructor", "_chatService", "authService", "nzMessageService", "togglingService", "chatListService", "router", "notesService", "activatedRoute", "themeService", "groupedChats", "History", "filteredGroupedChats", "isAllChatsOpen", "activeTab", "panelVisible", "isWorkspaceMode", "favoriteNotes", "recentNotes", "notesByCategory", "isNotesSubmenuOpen", "recentNotesSource", "isSettingsMode", "activeAdminTab", "isDailyInsightMode", "isNotesMode", "userInput", "selectedAgent", "originalOrder", "a", "b", "order", "indexOf", "key", "hasMoreMessages", "originalChatList", "pinnedChats", "favoriteChats", "archivedChats", "notes", "counter", "hasWorkspaces", "workspaceName", "tabConfig", "all", "title", "chats", "isGrouped", "hasMore", "groupChatsByDate", "favorite", "archive", "history", "isDarkMode", "isLoading", "isLoadingMore", "events", "pipe", "event", "subscribe", "url", "includes", "urlParts", "split", "length", "oldWorkspaceName", "previousTab", "needsReload", "urlAfterRedirects", "console", "log", "loadChatList", "loadPinnedChats", "loadFavoriteChats", "loadArchivedChats", "document", "body", "classList", "contains", "ngOnInit", "currentUrl", "_this", "_asyncToGenerator", "res", "list", "to<PERSON>romise", "messages", "chatList", "warn", "initializeEmptyChats", "err", "error", "Today", "Yesterday", "Older", "loadMoreChatList", "next", "push", "pinned", "Array", "isArray", "favorites", "archived", "toggleTab", "tab", "toggleNotesSubmenu", "trackByNoteCategory", "index", "category", "trackByChatId", "chat", "id", "onAgentSelected", "agent", "<PERSON><PERSON><PERSON>", "selectedAgentName", "message", "trim", "filterChats", "stopPropagation", "searchTerm", "target", "value", "toLowerCase", "Object", "keys", "for<PERSON>ach", "group", "toggleAllChats", "addNewChats", "chatId", "navigate", "toggleChat", "isToggled", "openSidebar", "isNavbarOpen", "addToPinnedChat", "pin", "isPinned", "success", "c", "findIndex", "addToFavChat", "isFavorite", "addToArchiveChat", "isArchived", "getCurrentTabChats", "getCurrentTabTitle", "isCurrentTabGrouped", "hasMoreForCurrentTab", "toggleTheme", "add", "remove", "grouped", "today", "Date", "yesterday", "setDate", "getDate", "sevenDaysAgo", "thirtyDaysAgo", "createdDate", "undefined", "date", "toString", "isNaN", "getTime", "groupKey", "isSameDay", "toLocaleDateString", "month", "year", "sort", "dateA", "dateB", "date1", "date2", "getFullYear", "getMonth", "navigateToDailyInsight", "toggle<PERSON><PERSON><PERSON>", "navigateToSettings", "section", "navigateToChatPage", "navigateToWorkspace", "navigateToNotes", "__decorate", "selector", "standalone", "imports", "template", "__NG_CLI_RESOURCE__0"], "sources": ["C:\\Users\\<USER>\\source\\ai-hub\\WebApp\\src\\app\\components\\sidebar\\sidebar.component.ts"], "sourcesContent": ["import { Component, inject } from '@angular/core';\nimport { KeyValue } from '@angular/common';\nimport { TogglingService } from '../../toggling.service';\nimport { ChatListService } from '../../services/chat-list.service';\nimport { ThemeService } from '../../../shared/services/theam.service';\nimport {\n  NavigationEnd,\n  Router,\n  RouterLink,\n  RouterLinkActive,\n  ActivatedRoute,\n} from '@angular/router';\nimport {\n  ChatServiceProxy,\n  ChatRequestDto,\n} from '../../../shared/service-proxies/service-proxies';\nimport { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { AuthService } from '../../../shared/services/auth.service';\nimport { NzButtonModule } from 'ng-zorro-antd/button';\nimport { NzPopoverModule } from 'ng-zorro-antd/popover';\nimport { NzMessageService } from 'ng-zorro-antd/message';\nimport { Subscription, BehaviorSubject } from 'rxjs';\nimport { filter } from 'rxjs/operators';\nimport {\n  Note,\n  NotesService,\n} from '../../MyNotesProjects/services/notes.service';\n\nimport { DailyInsightsSidebarComponent } from '../@rightSideComponents/daily-insights-sidebar/daily-insights-sidebar.component';\nimport { RightsidebarchatComponent } from '../@leftSideComponents/rightsidebarchat/rightsidebarchat.component';\nimport { SettingsidebarcomponentComponent } from '../@leftSideComponents/settingsidebarcomponent/settingsidebarcomponent.component';\nimport { NotesSidebarComponent } from '../@leftSideComponents/notes-sidebar/notes-sidebar.component';\n@Component({\n  selector: 'app-sidebar',\n  standalone: true,\n  imports: [\n    ServiceProxyModule,\n    CommonModule,\n    FormsModule,\n    RouterLink,\n    NzButtonModule,\n    NzPopoverModule,\n    DailyInsightsSidebarComponent,\n    RightsidebarchatComponent,\n    SettingsidebarcomponentComponent,\n    NotesSidebarComponent,\n  ],\n  templateUrl: './sidebar.component.html',\n  styleUrls: ['./sidebar.component.css'],\n})\nexport class SidebarComponent {\n  togglingService = inject(TogglingService);\n  chatListService = inject(ChatListService);\n  router = inject(Router);\n  notesService = inject(NotesService);\n  activatedRoute = inject(ActivatedRoute);\n  themeService = inject(ThemeService);\n  groupedChats: { [key: string]: any[] } = { History: [] };\n  chatId: any;\n  filteredGroupedChats: { [key: string]: any[] } = { ...this.groupedChats };\n  isAllChatsOpen = true;\n  activeTab:\n    | 'all'\n    | 'pinned-history'\n    | 'favorite'\n    | 'archive'\n    | 'notes'\n    | 'plugins'\n    | 'history' = 'all';\n  panelVisible = true;\n\n  // Add this property to track if in workspace mode\n  isWorkspaceMode = false;\n\n  // Notes-related properties\n  favoriteNotes: Note[] = [];\n  recentNotes: Note[] = [];\n  notesByCategory: { key: string; value: any[] }[] = [];\n  isNotesSubmenuOpen = false;\n  private favoriteNotesSubscription!: Subscription;\n  private recentNotesSource = new BehaviorSubject<number>(5); // Default limit\n  private recentNotesSubscription!: Subscription;\n\n  // Add this property to track if in admin mode\n  isSettingsMode = false;\n  // Property to track current admin tab\n  activeAdminTab = 'prompt-library';\n\n  // Add this property to track if in DailyInsight mode\n  isDailyInsightMode = false;\n\n  // Add this property to track if in Notes mode\n  isNotesMode = false;\n\n  // Properties for chat-sidebar component\n  userInput: ChatRequestDto = new ChatRequestDto();\n  selectedAgent: string = '';\n\n  originalOrder = (\n    a: KeyValue<string, any[]>,\n    b: KeyValue<string, any[]>\n  ): number => {\n    const order = ['History'];\n    return order.indexOf(a.key) - order.indexOf(b.key);\n  };\n  hasMoreMessages = true;\n  originalChatList: any[] = [];\n  pinnedChats: any[] = [];\n  favoriteChats: any[] = [];\n  archivedChats: any[] = [];\n  notes: any[] = [];\n  counter = 1;\n  hasWorkspaces = false;\n  workspaceName: string = '';\n\n  tabConfig: {\n    [key: string]: {\n      title: string;\n      chats: any;\n      isGrouped: boolean;\n      hasMore?: boolean;\n    };\n  } = {\n    all: {\n      title: 'All Chats',\n      chats: () => this.groupedChats,\n      isGrouped: true,\n      hasMore: true,\n    },\n    'pinned-history': {\n      title: 'Pinned Chats',\n      chats: () => this.groupChatsByDate(this.pinnedChats),\n      isGrouped: true,\n    },\n    favorite: {\n      title: 'Favorite Chats',\n      chats: () => this.groupChatsByDate(this.favoriteChats),\n      isGrouped: true,\n    },\n    archive: {\n      title: 'Archive Chats',\n      chats: () => this.groupChatsByDate(this.archivedChats),\n      isGrouped: true,\n    },\n    notes: { title: 'My Notes', chats: () => [], isGrouped: false },\n    history: { title: 'Chat History', chats: () => [], isGrouped: false },\n  };\n\n  isDarkMode: boolean = false;\n  isLoading: boolean = false;\n  isLoadingMore: boolean = false;\n\n  constructor(\n    private _chatService: ChatServiceProxy,\n    public authService: AuthService,\n    private nzMessageService: NzMessageService\n  ) {\n    this.router.events\n      .pipe(filter((event) => event instanceof NavigationEnd))\n      .subscribe((event: any) => {\n        // Get the current URL\n        const url = event.url;\n\n        // Update mode flags\n        this.isDailyInsightMode = url === '/' || url === '';\n        this.isWorkspaceMode =\n          url.includes('/workspaces') && !url.includes('/chat');\n        this.isSettingsMode = url.includes('/settings');\n        this.isNotesMode = url.includes('/notes');\n\n        // Update active admin tab when in settings\n        if (this.isSettingsMode) {\n          const urlParts = url.split('/');\n          // Get the last segment of the URL\n          this.activeAdminTab = urlParts[urlParts.length - 1];\n        } else {\n          // Reset activeAdminTab when not in settings\n          this.activeAdminTab = '';\n        }\n\n        // Update workspace name and reset counter when navigating between workspaces\n        const oldWorkspaceName = this.workspaceName;\n        if (url.includes('workspaces')) {\n          this.workspaceName = url.split('/')[2];\n          // Only reset counter if workspace changed\n          if (oldWorkspaceName !== this.workspaceName) {\n            this.counter = 1;\n          }\n        } else {\n          if (this.workspaceName !== '') {\n            // Only reset if we're changing from a workspace to non-workspace\n            this.workspaceName = '';\n            this.counter = 1;\n          }\n        }\n\n        if (this.workspaceName?.length > 0) {\n          this.hasWorkspaces = true;\n        }\n\n        // Store the current tab before data reloads\n        const previousTab = this.activeTab;\n\n        // Reload data when navigating between different sections or workspaces\n        const needsReload =\n          oldWorkspaceName !== this.workspaceName || // Workspace changed\n          (url.includes('/chat') &&\n            !event.urlAfterRedirects.includes('/chat')) || // Navigated to/from chat\n          this.isDailyInsightMode !== (url === '/' || url === ''); // Daily insight mode changed\n\n        if (needsReload) {\n          console.log('Navigation requires data reload');\n\n          // Only load chat lists if we're not in daily insight mode\n          if (!this.isDailyInsightMode) {\n            console.log('Not in daily insight mode, loading chat lists');\n            this.loadChatList();\n\n            // Only load data for the active tab\n            if (this.activeTab === 'pinned-history') {\n              this.loadPinnedChats();\n            } else if (this.activeTab === 'favorite') {\n              this.loadFavoriteChats();\n            } else if (this.activeTab === 'archive') {\n              this.loadArchivedChats();\n            }\n          } else {\n            console.log('In daily insight mode, skipping chat list loading');\n          }\n        }\n\n        // Restore the previous tab\n        if (previousTab !== 'all') {\n          this.activeTab = previousTab;\n\n          // Set the appropriate filtered list based on active tab\n          switch (this.activeTab) {\n            case 'pinned-history':\n              this.filteredGroupedChats = this.groupChatsByDate(\n                this.pinnedChats\n              );\n              break;\n            case 'favorite':\n              this.filteredGroupedChats = this.groupChatsByDate(\n                this.favoriteChats\n              );\n              break;\n            case 'archive':\n              this.filteredGroupedChats = this.groupChatsByDate(\n                this.archivedChats\n              );\n              break;\n            default:\n              // For 'all' tab or other tabs, keep default behavior\n              break;\n          }\n        }\n      });\n    this.isDarkMode = document.body.classList.contains('dark-mode');\n  }\n\n\n\n  ngOnInit(): void {\n    // Initialize based on current URL\n    const currentUrl = this.router.url;\n    this.isSettingsMode = currentUrl.includes('/settings');\n    this.isWorkspaceMode =\n      currentUrl.includes('/workspaces') && !currentUrl.includes('/chat');\n    this.isDailyInsightMode = currentUrl === '/' || currentUrl === '';\n    this.isNotesMode = currentUrl.includes('/notes');\n\n    // Extract workspace name from URL if available\n    if (currentUrl.includes('workspaces')) {\n      this.workspaceName = currentUrl.split('/')[2];\n      if (this.workspaceName?.length > 0) {\n        this.hasWorkspaces = true;\n      }\n    }\n\n    // Extract active admin tab from URL if in settings mode\n    if (this.isSettingsMode) {\n      const urlParts = currentUrl.split('/');\n      if (urlParts.length > 2) {\n        this.activeAdminTab = urlParts[2];\n      }\n    }\n\n    // Load chat list data immediately on initialization\n    console.log('Initializing sidebar component, loading chat list data');\n    this.loadChatList();\n\n    // Load other data based on active tab\n    if (this.activeTab === 'pinned-history') {\n      this.loadPinnedChats();\n    } else if (this.activeTab === 'favorite') {\n      this.loadFavoriteChats();\n    } else if (this.activeTab === 'archive') {\n      this.loadArchivedChats();\n    }\n  }\n\n  async loadChatList() {\n    // Show loading indicator if we're on the all chats tab\n    if (this.activeTab === 'all') {\n      this.isLoading = true;\n    }\n\n    try {\n      console.log('Loading chat list for workspace:', this.workspaceName);\n\n      // Make sure counter is initialized\n      if (!this.counter || this.counter < 1) {\n        this.counter = 1;\n      }\n\n      let res: any = await this._chatService\n        .list(this.workspaceName, this.counter, 15)\n        .toPromise();\n\n      if (res && res.messages) {\n        console.log(\n          'Chat list loaded successfully:',\n          res.messages.length,\n          'messages'\n        );\n        this.chatListService.chatList = res.messages;\n        this.originalChatList = res.messages;\n        this.hasMoreMessages = res.hasMoreMessages;\n        this.chatListService.groupChatsByDate();\n        this.groupedChats = this.chatListService.groupedChats;\n\n        // Only update filteredGroupedChats if we're on the 'all' tab\n        // This prevents losing our filtered view when clicking on a chat\n        if (this.activeTab === 'all') {\n          this.filteredGroupedChats = { ...this.groupedChats };\n        } else {\n          // For other tabs, maintain the existing filtered list by refreshing it\n          switch (this.activeTab) {\n            case 'pinned-history':\n              this.filteredGroupedChats = this.groupChatsByDate(\n                this.pinnedChats\n              );\n              break;\n            case 'favorite':\n              this.filteredGroupedChats = this.groupChatsByDate(\n                this.favoriteChats\n              );\n              break;\n            case 'archive':\n              this.filteredGroupedChats = this.groupChatsByDate(\n                this.archivedChats\n              );\n              break;\n          }\n        }\n\n        this.counter++;\n      } else {\n        console.warn('Chat list response was empty or invalid:', res);\n        // Initialize with empty data\n        this.initializeEmptyChats();\n      }\n    } catch (err) {\n      console.error('Error loading chat list:', err);\n      // Initialize with empty data\n      this.initializeEmptyChats();\n    } finally {\n      // Always make sure to turn off loading indicator\n      this.isLoading = false;\n    }\n  }\n\n  // Helper method to initialize empty chat lists\n  private initializeEmptyChats() {\n    this.chatListService.chatList = [];\n    this.originalChatList = [];\n    this.hasMoreMessages = false;\n    this.chatListService.groupedChats = {\n      Today: [],\n      Yesterday: [],\n      'Last 7 Days': [],\n      'Last 30 Days': [],\n      Older: [],\n    };\n    this.groupedChats = this.chatListService.groupedChats;\n    this.filteredGroupedChats = { ...this.groupedChats };\n  }\n\n  loadMoreChatList() {\n    // Show loading indicator for \"load more\"\n    this.isLoadingMore = true;\n    this._chatService.list(this.workspaceName, this.counter, 15).subscribe({\n      next: (res: any) => {\n        this.counter++;\n        if (res) {\n          this.chatListService.chatList.push(...res.messages);\n          this.originalChatList.push(...res.messages);\n          this.hasMoreMessages = res.hasMoreMessages;\n          this.chatListService.groupChatsByDate();\n          this.groupedChats = this.chatListService.groupedChats;\n\n          // Only update filteredGroupedChats if we're on the 'all' tab\n          if (this.activeTab === 'all') {\n            this.filteredGroupedChats = { ...this.groupedChats };\n          }\n        }\n        this.isLoadingMore = false;\n      },\n      error: (err) => {\n        console.error('Error loading more chats:', err);\n        this.isLoadingMore = false;\n      },\n    });\n  }\n\n  loadPinnedChats() {\n    // Show loading indicator if we're on the pinned tab\n    if (this.activeTab === 'pinned-history') {\n      this.isLoading = true;\n    }\n\n    this._chatService.pinned(this.workspaceName).subscribe({\n      next: (res: any) => {\n        if (res && Array.isArray(res)) {\n          this.pinnedChats = res;\n          if (this.activeTab === 'pinned-history') {\n            this.filteredGroupedChats = this.groupChatsByDate(this.pinnedChats);\n            this.isLoading = false;\n          }\n        } else {\n          this.pinnedChats = [];\n          if (this.activeTab === 'pinned-history') {\n            this.filteredGroupedChats = {};\n            this.isLoading = false;\n          }\n        }\n      },\n      error: (err) => {\n        console.error('Error loading pinned chats:', err);\n        this.pinnedChats = [];\n        if (this.activeTab === 'pinned-history') {\n          this.filteredGroupedChats = {};\n          this.isLoading = false;\n        }\n      },\n    });\n  }\n\n  loadFavoriteChats() {\n    // Show loading indicator if we're on the favorites tab\n    if (this.activeTab === 'favorite') {\n      this.isLoading = true;\n    }\n\n    this._chatService.favorites(this.workspaceName).subscribe({\n      next: (res: any) => {\n        if (res && Array.isArray(res)) {\n          this.favoriteChats = res;\n          if (this.activeTab === 'favorite') {\n            this.filteredGroupedChats = this.groupChatsByDate(\n              this.favoriteChats\n            );\n            this.isLoading = false;\n          }\n        } else {\n          this.favoriteChats = [];\n          if (this.activeTab === 'favorite') {\n            this.filteredGroupedChats = {};\n            this.isLoading = false;\n          }\n        }\n      },\n      error: (err) => {\n        console.error('Error loading favorite chats:', err);\n        this.favoriteChats = [];\n        if (this.activeTab === 'favorite') {\n          this.filteredGroupedChats = {};\n          this.isLoading = false;\n        }\n      },\n    });\n  }\n\n  loadArchivedChats() {\n    // Show loading indicator if we're on the archive tab\n    if (this.activeTab === 'archive') {\n      this.isLoading = true;\n    }\n\n    this._chatService.archived(this.workspaceName).subscribe({\n      next: (res: any) => {\n        if (res && Array.isArray(res)) {\n          this.archivedChats = res;\n          if (this.activeTab === 'archive') {\n            this.filteredGroupedChats = this.groupChatsByDate(\n              this.archivedChats\n            );\n            this.isLoading = false;\n          }\n        } else {\n          this.archivedChats = [];\n          if (this.activeTab === 'archive') {\n            this.filteredGroupedChats = {};\n            this.isLoading = false;\n          }\n        }\n      },\n      error: (err) => {\n        console.error('Error loading archived chats:', err);\n        this.archivedChats = [];\n        if (this.activeTab === 'archive') {\n          this.filteredGroupedChats = {};\n          this.isLoading = false;\n        }\n      },\n    });\n  }\n\n  toggleTab(tab: any) {\n    // If we're already on this tab, don't do anything\n    if (this.activeTab === tab) {\n      return;\n    }\n\n    // Update the activeTab\n    this.activeTab = tab;\n    this.isAllChatsOpen = true;\n\n    // Ensure we're in chat mode (not settings or workspace mode)\n    this.isSettingsMode = false;\n    this.isWorkspaceMode = false;\n    this.isDailyInsightMode = false;\n\n    // Apply changes based on the selected tab\n    switch (tab) {\n      case 'pinned-history':\n        // Check if we already have pinned chats data\n        if (!this.pinnedChats || this.pinnedChats.length === 0) {\n          // Only load if we don't have data\n          this.loadPinnedChats();\n        } else {\n          // Use existing data\n          this.filteredGroupedChats = this.groupChatsByDate(this.pinnedChats);\n        }\n        break;\n      case 'favorite':\n        // Check if we already have favorite chats data\n        if (!this.favoriteChats || this.favoriteChats.length === 0) {\n          // Only load if we don't have data\n          this.loadFavoriteChats();\n        } else {\n          // Use existing data\n          this.filteredGroupedChats = this.groupChatsByDate(this.favoriteChats);\n        }\n        break;\n      case 'archive':\n        // Check if we already have archived chats data\n        if (!this.archivedChats || this.archivedChats.length === 0) {\n          // Only load if we don't have data\n          this.loadArchivedChats();\n        } else {\n          // Use existing data\n          this.filteredGroupedChats = this.groupChatsByDate(this.archivedChats);\n        }\n        break;\n      case 'all':\n      default:\n        this.isNotesSubmenuOpen = false;\n        this.filteredGroupedChats = this.groupedChats;\n        break;\n    }\n  }\n\n  toggleNotesSubmenu() {\n    this.isNotesSubmenuOpen = !this.isNotesSubmenuOpen;\n  }\n\n  trackByNoteCategory(\n    index: number,\n    category: { key: string; value: any[] }\n  ): string {\n    return category.key;\n  }\n\n  trackByChatId(index: number, chat: any): string {\n    return chat.id;\n  }\n\n  /**\n   * Handles the agent selected event from the chat-sidebar component\n   * @param agent The selected agent\n   */\n  onAgentSelected(agent: any) {\n    if (!agent) return;\n\n    // Set the selected agent\n    this.selectedAgent = agent.agentName;\n\n    // Store the agent in the user input\n    (this.userInput as any).selectedAgentName = agent.agentName;\n\n    // Add the agent mention to the message if the input is empty\n    if (!this.userInput.message || this.userInput.message.trim() === '') {\n      this.userInput.message = `@${agent.agentName} `;\n    }\n  }\n\n  filterChats(event: Event) {\n    event.stopPropagation();\n    const searchTerm = (event.target as HTMLInputElement).value.toLowerCase();\n\n    if (!searchTerm) {\n      this.filteredGroupedChats = { ...this.groupedChats };\n      return;\n    }\n\n    this.filteredGroupedChats = {};\n    Object.keys(this.groupedChats).forEach((group) => {\n      this.filteredGroupedChats[group] = this.groupedChats[group].filter(\n        (chat: any) => chat.title.toLowerCase().includes(searchTerm)\n      );\n    });\n  }\n\n  toggleAllChats(event: Event) {\n    event.stopPropagation();\n    this.isAllChatsOpen = !this.isAllChatsOpen;\n  }\n\n  addNewChats(event: Event) {\n    event.stopPropagation();\n    this.isAllChatsOpen = true;\n    this.chatListService.chatId = 0; // Reset chatId to 0 for new chat\n\n    if (this.workspaceName) {\n      // If workspaceName is available, navigate to the chat within that workspace\n      this.router.navigate(['workspaces', this.workspaceName, 'chat']);\n    } else {\n      // If no workspace, navigate to chat\n      this.router.navigate(['/chat']);\n    }\n  }\n\n  toggleChat(\n    event: Event,\n    chat: {\n      id: number;\n      title: string;\n      lastMessage: string;\n      isToggled: boolean;\n      chats: any;\n    }\n  ) {\n    // Set the chat ID and navigate to the chat\n    this.chatListService.chatId = chat.id;\n\n    // Navigate based on workspace context\n    if (this.workspaceName) {\n      this.router.navigate(['workspaces', this.workspaceName, 'chat', chat.id]);\n    } else {\n      this.router.navigate(['/chat', chat.id]);\n    }\n\n    event.stopPropagation();\n    chat.isToggled = !chat.isToggled;\n  }\n\n  openSidebar() {\n    this.togglingService.isNavbarOpen = true;\n  }\n\n  addToPinnedChat(chat: any) {\n    this.chatId = chat.id;\n    this._chatService.pin(this.chatId, !chat.isPinned).subscribe((res) => {\n      if (res.isPinned) {\n        this.nzMessageService.success('Chat pinned successfully!');\n        this.pinnedChats = [...this.pinnedChats, res];\n      } else {\n        this.nzMessageService.success('Chat unpinned successfully!');\n        this.pinnedChats = this.pinnedChats.filter((c) => c.id !== res.id);\n      }\n      const index = this.chatListService.chatList.findIndex(\n        (c: any) => c.id === res.id\n      );\n      if (index !== -1) {\n        this.chatListService.chatList[index] = res;\n        this.chatListService.groupChatsByDate();\n        this.groupedChats = this.chatListService.groupedChats;\n        this.filteredGroupedChats = { ...this.groupedChats };\n      }\n    });\n  }\n\n  addToFavChat(chat: any) {\n    this.chatId = chat.id;\n    this._chatService\n      .favorite(this.chatId, !chat.isFavorite)\n      .subscribe((res) => {\n        if (res.isFavorite) {\n          this.nzMessageService.success('Chat favorited successfully!');\n          this.favoriteChats = [...this.favoriteChats, res];\n        } else {\n          this.nzMessageService.success('Chat unfavorited successfully!');\n          this.favoriteChats = this.favoriteChats.filter(\n            (c) => c.id !== res.id\n          );\n        }\n        const index = this.chatListService.chatList.findIndex(\n          (c: any) => c.id === res.id\n        );\n        if (index !== -1) {\n          this.chatListService.chatList[index] = res;\n          this.chatListService.groupChatsByDate();\n          this.groupedChats = this.chatListService.groupedChats;\n          this.filteredGroupedChats = { ...this.groupedChats };\n        }\n      });\n  }\n\n  addToArchiveChat(chat: any) {\n    this.chatId = chat.id;\n    this._chatService\n      .archive(this.chatId, !chat.isArchived)\n      .subscribe((res) => {\n        if (res.isArchived) {\n          this.nzMessageService.success('Chat archived successfully!');\n          this.archivedChats = [...this.archivedChats, res];\n          this.chatListService.chatList = this.chatListService.chatList.filter(\n            (c: any) => c.id !== res.id\n          );\n        } else {\n          this.nzMessageService.success('Chat unarchived successfully!');\n          this.archivedChats = this.archivedChats.filter(\n            (c) => c.id !== res.id\n          );\n          this.chatListService.chatList.push(res);\n        }\n        this.chatListService.groupChatsByDate();\n        this.groupedChats = this.chatListService.groupedChats;\n        this.filteredGroupedChats = { ...this.groupedChats };\n      });\n  }\n\n  getCurrentTabChats() {\n    if (this.activeTab === 'all') {\n      return this.groupedChats;\n    }\n\n    const chats = this.filteredGroupedChats;\n    if (this.tabConfig[this.activeTab].isGrouped) {\n      return Object.keys(chats).length > 0 ? chats : { 'No Chats': [] };\n    }\n    return chats;\n  }\n\n  getCurrentTabTitle() {\n    return this.tabConfig[this.activeTab].title;\n  }\n\n  isCurrentTabGrouped() {\n    return this.tabConfig[this.activeTab].isGrouped;\n  }\n\n  hasMoreForCurrentTab() {\n    return this.tabConfig[this.activeTab].hasMore && this.hasMoreMessages;\n  }\n\n  toggleTheme() {\n    this.isDarkMode = !this.isDarkMode;\n    if (this.isDarkMode) {\n      document.body.classList.add('dark-mode');\n    } else {\n      document.body.classList.remove('dark-mode');\n    }\n  }\n\n  private groupChatsByDate(chats: any[]): { [key: string]: any[] } {\n    // Return early if the chats array is null, undefined, or empty\n    if (!chats || chats.length === 0) {\n      return {};\n    }\n\n    const grouped: { [key: string]: any[] } = {};\n    const today = new Date();\n    const yesterday = new Date(today);\n    yesterday.setDate(today.getDate() - 1);\n    const sevenDaysAgo = new Date(today);\n    sevenDaysAgo.setDate(today.getDate() - 7);\n    const thirtyDaysAgo = new Date(today);\n    thirtyDaysAgo.setDate(today.getDate() - 30);\n\n    chats.forEach((chat) => {\n      if (!chat || chat.createdDate === undefined) {\n        return;\n      }\n\n      try {\n        let date: Date;\n        if (typeof chat.createdDate === 'string') {\n          date = new Date(chat.createdDate);\n        } else if (\n          chat.createdDate &&\n          typeof chat.createdDate === 'object' &&\n          chat.createdDate.toString\n        ) {\n          date = new Date(chat.createdDate.toString());\n        } else {\n          return;\n        }\n\n        if (isNaN(date.getTime())) {\n          return;\n        }\n\n        let groupKey:\n          | 'Today'\n          | 'Yesterday'\n          | 'Previous 7 Days'\n          | 'Previous 30 Days'\n          | string;\n\n        if (this.isSameDay(date, today)) {\n          groupKey = 'Today';\n        } else if (this.isSameDay(date, yesterday)) {\n          groupKey = 'Yesterday';\n        } else if (date >= sevenDaysAgo) {\n          groupKey = 'Previous 7 Days';\n        } else if (date >= thirtyDaysAgo) {\n          groupKey = 'Previous 30 Days';\n        } else {\n          groupKey = date.toLocaleDateString('en-US', {\n            month: 'long',\n            year: 'numeric',\n          });\n        }\n\n        if (!grouped[groupKey]) {\n          grouped[groupKey] = [];\n        }\n        grouped[groupKey].push(chat);\n      } catch (error) {\n        console.error('Error processing chat date:', error);\n      }\n    });\n\n    // Sort chats within each group\n    for (const key in grouped) {\n      grouped[key].sort((a, b) => {\n        try {\n          const dateA = new Date(a.createdDate.toString()).getTime();\n          const dateB = new Date(b.createdDate.toString()).getTime();\n          return dateB - dateA;\n        } catch (error) {\n          return 0;\n        }\n      });\n    }\n\n    return grouped;\n  }\n\n  private isSameDay(date1: Date, date2: Date): boolean {\n    return (\n      date1.getFullYear() === date2.getFullYear() &&\n      date1.getMonth() === date2.getMonth() &&\n      date1.getDate() === date2.getDate()\n    );\n  }\n\n  // Method to navigate to Daily Insight\n  navigateToDailyInsight(event: Event) {\n    event.stopPropagation();\n\n    // Reset chatId to 0\n    this.chatListService.chatId = 0;\n\n    // Toggle/hide the sidebar\n    // if (this.togglingService.isNavbarOpen) {\n    // this.togglingService.toggleNavbar();\n    // }\n  if (!this.togglingService.isNavbarOpen) {\n      this.togglingService.toggleNavbar();\n    }\n    // Navigate to root/daily insight\n    this.router.navigate(['/']);\n  }\n\n  // Update navigateToSettings to handle event and toggle sidebar\n  navigateToSettings(event: Event, section: string) {\n    event.stopPropagation();\n\n    // Set mode flags\n    this.isSettingsMode = true;\n    this.isWorkspaceMode = false;\n    this.isDailyInsightMode = false;\n\n    // Update the active tab\n    this.activeAdminTab = section;\n\n    // Make sure the sidebar is open\n    if (!this.togglingService.isNavbarOpen) {\n      this.togglingService.toggleNavbar();\n    }\n\n    // Navigate to the settings section\n    this.router.navigate(['/settings', section]);\n  }\n\n  // Method to handle navigation from the chat icon in narrow sidebar\n  navigateToChatPage(event: Event) {\n    event.stopPropagation();\n\n    // Switch from admin mode to chat mode if in admin mode\n    this.isSettingsMode = false;\n    this.isDailyInsightMode = false;\n\n    if (!this.togglingService.isNavbarOpen) {\n      this.togglingService.toggleNavbar();\n    }\n\n    // Open the sidebar and select 'all' chat tab\n    this.openSidebar();\n    this.activeTab = 'all';\n\n    // Reset chatId to 0 for new chat\n    this.chatListService.chatId = 0;\n\n    // Navigate based on workspace context (same logic as addNewChats)\n    if (this.workspaceName) {\n      // If workspaceName is available, navigate to the chat within that workspace\n      this.router.navigate(['workspaces', this.workspaceName, 'chat']);\n    } else {\n      // If no workspace, navigate to chat\n      this.router.navigate(['/chat']);\n    }\n  }\n\n  // Method to handle workspace navigation and toggle sidebar\n  navigateToWorkspace(event: Event) {\n    event.stopPropagation();\n\n    // Reset chatId to 0\n    this.chatListService.chatId = 0;\n\n    // Toggle/hide the sidebar\n    if (this.togglingService.isNavbarOpen) {\n      this.togglingService.toggleNavbar();\n    }\n\n    // Navigate to workspaces\n    this.router.navigate(['/workspaces']);\n  }\n\n  // Daily insight mode methods moved to DailyInsightsSidebarComponent\n\n  // Method to navigate to Notes\n  navigateToNotes(event: Event) {\n    event.stopPropagation();\n\n    // Reset chatId to 0\n    this.chatListService.chatId = 0;\n\n    // Set mode flags\n    this.isSettingsMode = false;\n    this.isWorkspaceMode = false;\n    this.isDailyInsightMode = false;\n    this.isNotesMode = true;\n\n    // Open the sidebar if it's closed, keep it open if already open\n    if (!this.togglingService.isNavbarOpen) {\n      this.togglingService.toggleNavbar();\n    }\n\n    // Navigate to notes\n    this.router.navigate(['/notes']);\n  }\n\n  // refreshDailyInsights method moved to DailyInsightsSidebarComponent\n  // isSettingsTabActive method moved to SettingsidebarcomponentComponent\n}\n"], "mappings": ";;;;AAAA,SAASA,SAAS,EAAEC,MAAM,QAAQ,eAAe;AAEjD,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,eAAe,QAAQ,kCAAkC;AAClE,SAASC,YAAY,QAAQ,wCAAwC;AACrE,SACEC,aAAa,EACbC,MAAM,EACNC,UAAU,EAEVC,cAAc,QACT,iBAAiB;AACxB,SACEC,gBAAgB,EAChBC,cAAc,QACT,iDAAiD;AACxD,SAASC,kBAAkB,QAAQ,sDAAsD;AACzF,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,WAAW,QAAQ,uCAAuC;AACnE,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,eAAe,QAAQ,uBAAuB;AACvD,SAASC,gBAAgB,QAAQ,uBAAuB;AACxD,SAAuBC,eAAe,QAAQ,MAAM;AACpD,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAEEC,YAAY,QACP,8CAA8C;AAErD,SAASC,6BAA6B,QAAQ,iFAAiF;AAC/H,SAASC,yBAAyB,QAAQ,oEAAoE;AAC9G,SAASC,gCAAgC,QAAQ,kFAAkF;AACnI,SAASC,qBAAqB,QAAQ,8DAA8D;AAmB7F,IAAMC,gBAAgB,GAAtB,MAAMA,gBAAgB;EAsG3BC,YACUC,YAA8B,EAC/BC,WAAwB,EACvBC,gBAAkC;IAFlC,KAAAF,YAAY,GAAZA,YAAY;IACb,KAAAC,WAAW,GAAXA,WAAW;IACV,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAxG1B,KAAAC,eAAe,GAAG7B,MAAM,CAACC,eAAe,CAAC;IACzC,KAAA6B,eAAe,GAAG9B,MAAM,CAACE,eAAe,CAAC;IACzC,KAAA6B,MAAM,GAAG/B,MAAM,CAACK,MAAM,CAAC;IACvB,KAAA2B,YAAY,GAAGhC,MAAM,CAACmB,YAAY,CAAC;IACnC,KAAAc,cAAc,GAAGjC,MAAM,CAACO,cAAc,CAAC;IACvC,KAAA2B,YAAY,GAAGlC,MAAM,CAACG,YAAY,CAAC;IACnC,KAAAgC,YAAY,GAA6B;MAAEC,OAAO,EAAE;IAAE,CAAE;IAExD,KAAAC,oBAAoB,GAA6B;MAAE,GAAG,IAAI,CAACF;IAAY,CAAE;IACzE,KAAAG,cAAc,GAAG,IAAI;IACrB,KAAAC,SAAS,GAOO,KAAK;IACrB,KAAAC,YAAY,GAAG,IAAI;IAEnB;IACA,KAAAC,eAAe,GAAG,KAAK;IAEvB;IACA,KAAAC,aAAa,GAAW,EAAE;IAC1B,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,eAAe,GAAoC,EAAE;IACrD,KAAAC,kBAAkB,GAAG,KAAK;IAElB,KAAAC,iBAAiB,GAAG,IAAI7B,eAAe,CAAS,CAAC,CAAC,CAAC,CAAC;IAG5D;IACA,KAAA8B,cAAc,GAAG,KAAK;IACtB;IACA,KAAAC,cAAc,GAAG,gBAAgB;IAEjC;IACA,KAAAC,kBAAkB,GAAG,KAAK;IAE1B;IACA,KAAAC,WAAW,GAAG,KAAK;IAEnB;IACA,KAAAC,SAAS,GAAmB,IAAI1C,cAAc,EAAE;IAChD,KAAA2C,aAAa,GAAW,EAAE;IAE1B,KAAAC,aAAa,GAAG,CACdC,CAA0B,EAC1BC,CAA0B,KAChB;MACV,MAAMC,KAAK,GAAG,CAAC,SAAS,CAAC;MACzB,OAAOA,KAAK,CAACC,OAAO,CAACH,CAAC,CAACI,GAAG,CAAC,GAAGF,KAAK,CAACC,OAAO,CAACF,CAAC,CAACG,GAAG,CAAC;IACpD,CAAC;IACD,KAAAC,eAAe,GAAG,IAAI;IACtB,KAAAC,gBAAgB,GAAU,EAAE;IAC5B,KAAAC,WAAW,GAAU,EAAE;IACvB,KAAAC,aAAa,GAAU,EAAE;IACzB,KAAAC,aAAa,GAAU,EAAE;IACzB,KAAAC,KAAK,GAAU,EAAE;IACjB,KAAAC,OAAO,GAAG,CAAC;IACX,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,aAAa,GAAW,EAAE;IAE1B,KAAAC,SAAS,GAOL;MACFC,GAAG,EAAE;QACHC,KAAK,EAAE,WAAW;QAClBC,KAAK,EAAEA,CAAA,KAAM,IAAI,CAACpC,YAAY;QAC9BqC,SAAS,EAAE,IAAI;QACfC,OAAO,EAAE;OACV;MACD,gBAAgB,EAAE;QAChBH,KAAK,EAAE,cAAc;QACrBC,KAAK,EAAEA,CAAA,KAAM,IAAI,CAACG,gBAAgB,CAAC,IAAI,CAACb,WAAW,CAAC;QACpDW,SAAS,EAAE;OACZ;MACDG,QAAQ,EAAE;QACRL,KAAK,EAAE,gBAAgB;QACvBC,KAAK,EAAEA,CAAA,KAAM,IAAI,CAACG,gBAAgB,CAAC,IAAI,CAACZ,aAAa,CAAC;QACtDU,SAAS,EAAE;OACZ;MACDI,OAAO,EAAE;QACPN,KAAK,EAAE,eAAe;QACtBC,KAAK,EAAEA,CAAA,KAAM,IAAI,CAACG,gBAAgB,CAAC,IAAI,CAACX,aAAa,CAAC;QACtDS,SAAS,EAAE;OACZ;MACDR,KAAK,EAAE;QAAEM,KAAK,EAAE,UAAU;QAAEC,KAAK,EAAEA,CAAA,KAAM,EAAE;QAAEC,SAAS,EAAE;MAAK,CAAE;MAC/DK,OAAO,EAAE;QAAEP,KAAK,EAAE,cAAc;QAAEC,KAAK,EAAEA,CAAA,KAAM,EAAE;QAAEC,SAAS,EAAE;MAAK;KACpE;IAED,KAAAM,UAAU,GAAY,KAAK;IAC3B,KAAAC,SAAS,GAAY,KAAK;IAC1B,KAAAC,aAAa,GAAY,KAAK;IAO5B,IAAI,CAACjD,MAAM,CAACkD,MAAM,CACfC,IAAI,CAAChE,MAAM,CAAEiE,KAAK,IAAKA,KAAK,YAAY/E,aAAa,CAAC,CAAC,CACvDgF,SAAS,CAAED,KAAU,IAAI;MACxB;MACA,MAAME,GAAG,GAAGF,KAAK,CAACE,GAAG;MAErB;MACA,IAAI,CAACpC,kBAAkB,GAAGoC,GAAG,KAAK,GAAG,IAAIA,GAAG,KAAK,EAAE;MACnD,IAAI,CAAC5C,eAAe,GAClB4C,GAAG,CAACC,QAAQ,CAAC,aAAa,CAAC,IAAI,CAACD,GAAG,CAACC,QAAQ,CAAC,OAAO,CAAC;MACvD,IAAI,CAACvC,cAAc,GAAGsC,GAAG,CAACC,QAAQ,CAAC,WAAW,CAAC;MAC/C,IAAI,CAACpC,WAAW,GAAGmC,GAAG,CAACC,QAAQ,CAAC,QAAQ,CAAC;MAEzC;MACA,IAAI,IAAI,CAACvC,cAAc,EAAE;QACvB,MAAMwC,QAAQ,GAAGF,GAAG,CAACG,KAAK,CAAC,GAAG,CAAC;QAC/B;QACA,IAAI,CAACxC,cAAc,GAAGuC,QAAQ,CAACA,QAAQ,CAACE,MAAM,GAAG,CAAC,CAAC;OACpD,MAAM;QACL;QACA,IAAI,CAACzC,cAAc,GAAG,EAAE;;MAG1B;MACA,MAAM0C,gBAAgB,GAAG,IAAI,CAACvB,aAAa;MAC3C,IAAIkB,GAAG,CAACC,QAAQ,CAAC,YAAY,CAAC,EAAE;QAC9B,IAAI,CAACnB,aAAa,GAAGkB,GAAG,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACtC;QACA,IAAIE,gBAAgB,KAAK,IAAI,CAACvB,aAAa,EAAE;UAC3C,IAAI,CAACF,OAAO,GAAG,CAAC;;OAEnB,MAAM;QACL,IAAI,IAAI,CAACE,aAAa,KAAK,EAAE,EAAE;UAC7B;UACA,IAAI,CAACA,aAAa,GAAG,EAAE;UACvB,IAAI,CAACF,OAAO,GAAG,CAAC;;;MAIpB,IAAI,IAAI,CAACE,aAAa,EAAEsB,MAAM,GAAG,CAAC,EAAE;QAClC,IAAI,CAACvB,aAAa,GAAG,IAAI;;MAG3B;MACA,MAAMyB,WAAW,GAAG,IAAI,CAACpD,SAAS;MAElC;MACA,MAAMqD,WAAW,GACfF,gBAAgB,KAAK,IAAI,CAACvB,aAAa;MAAI;MAC1CkB,GAAG,CAACC,QAAQ,CAAC,OAAO,CAAC,IACpB,CAACH,KAAK,CAACU,iBAAiB,CAACP,QAAQ,CAAC,OAAO,CAAE;MAAI;MACjD,IAAI,CAACrC,kBAAkB,MAAMoC,GAAG,KAAK,GAAG,IAAIA,GAAG,KAAK,EAAE,CAAC,CAAC,CAAC;MAE3D,IAAIO,WAAW,EAAE;QACfE,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAE9C;QACA,IAAI,CAAC,IAAI,CAAC9C,kBAAkB,EAAE;UAC5B6C,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;UAC5D,IAAI,CAACC,YAAY,EAAE;UAEnB;UACA,IAAI,IAAI,CAACzD,SAAS,KAAK,gBAAgB,EAAE;YACvC,IAAI,CAAC0D,eAAe,EAAE;WACvB,MAAM,IAAI,IAAI,CAAC1D,SAAS,KAAK,UAAU,EAAE;YACxC,IAAI,CAAC2D,iBAAiB,EAAE;WACzB,MAAM,IAAI,IAAI,CAAC3D,SAAS,KAAK,SAAS,EAAE;YACvC,IAAI,CAAC4D,iBAAiB,EAAE;;SAE3B,MAAM;UACLL,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;;;MAIpE;MACA,IAAIJ,WAAW,KAAK,KAAK,EAAE;QACzB,IAAI,CAACpD,SAAS,GAAGoD,WAAW;QAE5B;QACA,QAAQ,IAAI,CAACpD,SAAS;UACpB,KAAK,gBAAgB;YACnB,IAAI,CAACF,oBAAoB,GAAG,IAAI,CAACqC,gBAAgB,CAC/C,IAAI,CAACb,WAAW,CACjB;YACD;UACF,KAAK,UAAU;YACb,IAAI,CAACxB,oBAAoB,GAAG,IAAI,CAACqC,gBAAgB,CAC/C,IAAI,CAACZ,aAAa,CACnB;YACD;UACF,KAAK,SAAS;YACZ,IAAI,CAACzB,oBAAoB,GAAG,IAAI,CAACqC,gBAAgB,CAC/C,IAAI,CAACX,aAAa,CACnB;YACD;UACF;YACE;YACA;;;IAGR,CAAC,CAAC;IACJ,IAAI,CAACe,UAAU,GAAGsB,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,QAAQ,CAAC,WAAW,CAAC;EACjE;EAIAC,QAAQA,CAAA;IACN;IACA,MAAMC,UAAU,GAAG,IAAI,CAAC1E,MAAM,CAACsD,GAAG;IAClC,IAAI,CAACtC,cAAc,GAAG0D,UAAU,CAACnB,QAAQ,CAAC,WAAW,CAAC;IACtD,IAAI,CAAC7C,eAAe,GAClBgE,UAAU,CAACnB,QAAQ,CAAC,aAAa,CAAC,IAAI,CAACmB,UAAU,CAACnB,QAAQ,CAAC,OAAO,CAAC;IACrE,IAAI,CAACrC,kBAAkB,GAAGwD,UAAU,KAAK,GAAG,IAAIA,UAAU,KAAK,EAAE;IACjE,IAAI,CAACvD,WAAW,GAAGuD,UAAU,CAACnB,QAAQ,CAAC,QAAQ,CAAC;IAEhD;IACA,IAAImB,UAAU,CAACnB,QAAQ,CAAC,YAAY,CAAC,EAAE;MACrC,IAAI,CAACnB,aAAa,GAAGsC,UAAU,CAACjB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC7C,IAAI,IAAI,CAACrB,aAAa,EAAEsB,MAAM,GAAG,CAAC,EAAE;QAClC,IAAI,CAACvB,aAAa,GAAG,IAAI;;;IAI7B;IACA,IAAI,IAAI,CAACnB,cAAc,EAAE;MACvB,MAAMwC,QAAQ,GAAGkB,UAAU,CAACjB,KAAK,CAAC,GAAG,CAAC;MACtC,IAAID,QAAQ,CAACE,MAAM,GAAG,CAAC,EAAE;QACvB,IAAI,CAACzC,cAAc,GAAGuC,QAAQ,CAAC,CAAC,CAAC;;;IAIrC;IACAO,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;IACrE,IAAI,CAACC,YAAY,EAAE;IAEnB;IACA,IAAI,IAAI,CAACzD,SAAS,KAAK,gBAAgB,EAAE;MACvC,IAAI,CAAC0D,eAAe,EAAE;KACvB,MAAM,IAAI,IAAI,CAAC1D,SAAS,KAAK,UAAU,EAAE;MACxC,IAAI,CAAC2D,iBAAiB,EAAE;KACzB,MAAM,IAAI,IAAI,CAAC3D,SAAS,KAAK,SAAS,EAAE;MACvC,IAAI,CAAC4D,iBAAiB,EAAE;;EAE5B;EAEMH,YAAYA,CAAA;IAAA,IAAAU,KAAA;IAAA,OAAAC,iBAAA;MAChB;MACA,IAAID,KAAI,CAACnE,SAAS,KAAK,KAAK,EAAE;QAC5BmE,KAAI,CAAC3B,SAAS,GAAG,IAAI;;MAGvB,IAAI;QACFe,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEW,KAAI,CAACvC,aAAa,CAAC;QAEnE;QACA,IAAI,CAACuC,KAAI,CAACzC,OAAO,IAAIyC,KAAI,CAACzC,OAAO,GAAG,CAAC,EAAE;UACrCyC,KAAI,CAACzC,OAAO,GAAG,CAAC;;QAGlB,IAAI2C,GAAG,SAAcF,KAAI,CAAChF,YAAY,CACnCmF,IAAI,CAACH,KAAI,CAACvC,aAAa,EAAEuC,KAAI,CAACzC,OAAO,EAAE,EAAE,CAAC,CAC1C6C,SAAS,EAAE;QAEd,IAAIF,GAAG,IAAIA,GAAG,CAACG,QAAQ,EAAE;UACvBjB,OAAO,CAACC,GAAG,CACT,gCAAgC,EAChCa,GAAG,CAACG,QAAQ,CAACtB,MAAM,EACnB,UAAU,CACX;UACDiB,KAAI,CAAC5E,eAAe,CAACkF,QAAQ,GAAGJ,GAAG,CAACG,QAAQ;UAC5CL,KAAI,CAAC9C,gBAAgB,GAAGgD,GAAG,CAACG,QAAQ;UACpCL,KAAI,CAAC/C,eAAe,GAAGiD,GAAG,CAACjD,eAAe;UAC1C+C,KAAI,CAAC5E,eAAe,CAAC4C,gBAAgB,EAAE;UACvCgC,KAAI,CAACvE,YAAY,GAAGuE,KAAI,CAAC5E,eAAe,CAACK,YAAY;UAErD;UACA;UACA,IAAIuE,KAAI,CAACnE,SAAS,KAAK,KAAK,EAAE;YAC5BmE,KAAI,CAACrE,oBAAoB,GAAG;cAAE,GAAGqE,KAAI,CAACvE;YAAY,CAAE;WACrD,MAAM;YACL;YACA,QAAQuE,KAAI,CAACnE,SAAS;cACpB,KAAK,gBAAgB;gBACnBmE,KAAI,CAACrE,oBAAoB,GAAGqE,KAAI,CAAChC,gBAAgB,CAC/CgC,KAAI,CAAC7C,WAAW,CACjB;gBACD;cACF,KAAK,UAAU;gBACb6C,KAAI,CAACrE,oBAAoB,GAAGqE,KAAI,CAAChC,gBAAgB,CAC/CgC,KAAI,CAAC5C,aAAa,CACnB;gBACD;cACF,KAAK,SAAS;gBACZ4C,KAAI,CAACrE,oBAAoB,GAAGqE,KAAI,CAAChC,gBAAgB,CAC/CgC,KAAI,CAAC3C,aAAa,CACnB;gBACD;;;UAIN2C,KAAI,CAACzC,OAAO,EAAE;SACf,MAAM;UACL6B,OAAO,CAACmB,IAAI,CAAC,0CAA0C,EAAEL,GAAG,CAAC;UAC7D;UACAF,KAAI,CAACQ,oBAAoB,EAAE;;OAE9B,CAAC,OAAOC,GAAG,EAAE;QACZrB,OAAO,CAACsB,KAAK,CAAC,0BAA0B,EAAED,GAAG,CAAC;QAC9C;QACAT,KAAI,CAACQ,oBAAoB,EAAE;OAC5B,SAAS;QACR;QACAR,KAAI,CAAC3B,SAAS,GAAG,KAAK;;IACvB;EACH;EAEA;EACQmC,oBAAoBA,CAAA;IAC1B,IAAI,CAACpF,eAAe,CAACkF,QAAQ,GAAG,EAAE;IAClC,IAAI,CAACpD,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACD,eAAe,GAAG,KAAK;IAC5B,IAAI,CAAC7B,eAAe,CAACK,YAAY,GAAG;MAClCkF,KAAK,EAAE,EAAE;MACTC,SAAS,EAAE,EAAE;MACb,aAAa,EAAE,EAAE;MACjB,cAAc,EAAE,EAAE;MAClBC,KAAK,EAAE;KACR;IACD,IAAI,CAACpF,YAAY,GAAG,IAAI,CAACL,eAAe,CAACK,YAAY;IACrD,IAAI,CAACE,oBAAoB,GAAG;MAAE,GAAG,IAAI,CAACF;IAAY,CAAE;EACtD;EAEAqF,gBAAgBA,CAAA;IACd;IACA,IAAI,CAACxC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACtD,YAAY,CAACmF,IAAI,CAAC,IAAI,CAAC1C,aAAa,EAAE,IAAI,CAACF,OAAO,EAAE,EAAE,CAAC,CAACmB,SAAS,CAAC;MACrEqC,IAAI,EAAGb,GAAQ,IAAI;QACjB,IAAI,CAAC3C,OAAO,EAAE;QACd,IAAI2C,GAAG,EAAE;UACP,IAAI,CAAC9E,eAAe,CAACkF,QAAQ,CAACU,IAAI,CAAC,GAAGd,GAAG,CAACG,QAAQ,CAAC;UACnD,IAAI,CAACnD,gBAAgB,CAAC8D,IAAI,CAAC,GAAGd,GAAG,CAACG,QAAQ,CAAC;UAC3C,IAAI,CAACpD,eAAe,GAAGiD,GAAG,CAACjD,eAAe;UAC1C,IAAI,CAAC7B,eAAe,CAAC4C,gBAAgB,EAAE;UACvC,IAAI,CAACvC,YAAY,GAAG,IAAI,CAACL,eAAe,CAACK,YAAY;UAErD;UACA,IAAI,IAAI,CAACI,SAAS,KAAK,KAAK,EAAE;YAC5B,IAAI,CAACF,oBAAoB,GAAG;cAAE,GAAG,IAAI,CAACF;YAAY,CAAE;;;QAGxD,IAAI,CAAC6C,aAAa,GAAG,KAAK;MAC5B,CAAC;MACDoC,KAAK,EAAGD,GAAG,IAAI;QACbrB,OAAO,CAACsB,KAAK,CAAC,2BAA2B,EAAED,GAAG,CAAC;QAC/C,IAAI,CAACnC,aAAa,GAAG,KAAK;MAC5B;KACD,CAAC;EACJ;EAEAiB,eAAeA,CAAA;IACb;IACA,IAAI,IAAI,CAAC1D,SAAS,KAAK,gBAAgB,EAAE;MACvC,IAAI,CAACwC,SAAS,GAAG,IAAI;;IAGvB,IAAI,CAACrD,YAAY,CAACiG,MAAM,CAAC,IAAI,CAACxD,aAAa,CAAC,CAACiB,SAAS,CAAC;MACrDqC,IAAI,EAAGb,GAAQ,IAAI;QACjB,IAAIA,GAAG,IAAIgB,KAAK,CAACC,OAAO,CAACjB,GAAG,CAAC,EAAE;UAC7B,IAAI,CAAC/C,WAAW,GAAG+C,GAAG;UACtB,IAAI,IAAI,CAACrE,SAAS,KAAK,gBAAgB,EAAE;YACvC,IAAI,CAACF,oBAAoB,GAAG,IAAI,CAACqC,gBAAgB,CAAC,IAAI,CAACb,WAAW,CAAC;YACnE,IAAI,CAACkB,SAAS,GAAG,KAAK;;SAEzB,MAAM;UACL,IAAI,CAAClB,WAAW,GAAG,EAAE;UACrB,IAAI,IAAI,CAACtB,SAAS,KAAK,gBAAgB,EAAE;YACvC,IAAI,CAACF,oBAAoB,GAAG,EAAE;YAC9B,IAAI,CAAC0C,SAAS,GAAG,KAAK;;;MAG5B,CAAC;MACDqC,KAAK,EAAGD,GAAG,IAAI;QACbrB,OAAO,CAACsB,KAAK,CAAC,6BAA6B,EAAED,GAAG,CAAC;QACjD,IAAI,CAACtD,WAAW,GAAG,EAAE;QACrB,IAAI,IAAI,CAACtB,SAAS,KAAK,gBAAgB,EAAE;UACvC,IAAI,CAACF,oBAAoB,GAAG,EAAE;UAC9B,IAAI,CAAC0C,SAAS,GAAG,KAAK;;MAE1B;KACD,CAAC;EACJ;EAEAmB,iBAAiBA,CAAA;IACf;IACA,IAAI,IAAI,CAAC3D,SAAS,KAAK,UAAU,EAAE;MACjC,IAAI,CAACwC,SAAS,GAAG,IAAI;;IAGvB,IAAI,CAACrD,YAAY,CAACoG,SAAS,CAAC,IAAI,CAAC3D,aAAa,CAAC,CAACiB,SAAS,CAAC;MACxDqC,IAAI,EAAGb,GAAQ,IAAI;QACjB,IAAIA,GAAG,IAAIgB,KAAK,CAACC,OAAO,CAACjB,GAAG,CAAC,EAAE;UAC7B,IAAI,CAAC9C,aAAa,GAAG8C,GAAG;UACxB,IAAI,IAAI,CAACrE,SAAS,KAAK,UAAU,EAAE;YACjC,IAAI,CAACF,oBAAoB,GAAG,IAAI,CAACqC,gBAAgB,CAC/C,IAAI,CAACZ,aAAa,CACnB;YACD,IAAI,CAACiB,SAAS,GAAG,KAAK;;SAEzB,MAAM;UACL,IAAI,CAACjB,aAAa,GAAG,EAAE;UACvB,IAAI,IAAI,CAACvB,SAAS,KAAK,UAAU,EAAE;YACjC,IAAI,CAACF,oBAAoB,GAAG,EAAE;YAC9B,IAAI,CAAC0C,SAAS,GAAG,KAAK;;;MAG5B,CAAC;MACDqC,KAAK,EAAGD,GAAG,IAAI;QACbrB,OAAO,CAACsB,KAAK,CAAC,+BAA+B,EAAED,GAAG,CAAC;QACnD,IAAI,CAACrD,aAAa,GAAG,EAAE;QACvB,IAAI,IAAI,CAACvB,SAAS,KAAK,UAAU,EAAE;UACjC,IAAI,CAACF,oBAAoB,GAAG,EAAE;UAC9B,IAAI,CAAC0C,SAAS,GAAG,KAAK;;MAE1B;KACD,CAAC;EACJ;EAEAoB,iBAAiBA,CAAA;IACf;IACA,IAAI,IAAI,CAAC5D,SAAS,KAAK,SAAS,EAAE;MAChC,IAAI,CAACwC,SAAS,GAAG,IAAI;;IAGvB,IAAI,CAACrD,YAAY,CAACqG,QAAQ,CAAC,IAAI,CAAC5D,aAAa,CAAC,CAACiB,SAAS,CAAC;MACvDqC,IAAI,EAAGb,GAAQ,IAAI;QACjB,IAAIA,GAAG,IAAIgB,KAAK,CAACC,OAAO,CAACjB,GAAG,CAAC,EAAE;UAC7B,IAAI,CAAC7C,aAAa,GAAG6C,GAAG;UACxB,IAAI,IAAI,CAACrE,SAAS,KAAK,SAAS,EAAE;YAChC,IAAI,CAACF,oBAAoB,GAAG,IAAI,CAACqC,gBAAgB,CAC/C,IAAI,CAACX,aAAa,CACnB;YACD,IAAI,CAACgB,SAAS,GAAG,KAAK;;SAEzB,MAAM;UACL,IAAI,CAAChB,aAAa,GAAG,EAAE;UACvB,IAAI,IAAI,CAACxB,SAAS,KAAK,SAAS,EAAE;YAChC,IAAI,CAACF,oBAAoB,GAAG,EAAE;YAC9B,IAAI,CAAC0C,SAAS,GAAG,KAAK;;;MAG5B,CAAC;MACDqC,KAAK,EAAGD,GAAG,IAAI;QACbrB,OAAO,CAACsB,KAAK,CAAC,+BAA+B,EAAED,GAAG,CAAC;QACnD,IAAI,CAACpD,aAAa,GAAG,EAAE;QACvB,IAAI,IAAI,CAACxB,SAAS,KAAK,SAAS,EAAE;UAChC,IAAI,CAACF,oBAAoB,GAAG,EAAE;UAC9B,IAAI,CAAC0C,SAAS,GAAG,KAAK;;MAE1B;KACD,CAAC;EACJ;EAEAiD,SAASA,CAACC,GAAQ;IAChB;IACA,IAAI,IAAI,CAAC1F,SAAS,KAAK0F,GAAG,EAAE;MAC1B;;IAGF;IACA,IAAI,CAAC1F,SAAS,GAAG0F,GAAG;IACpB,IAAI,CAAC3F,cAAc,GAAG,IAAI;IAE1B;IACA,IAAI,CAACS,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACN,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACQ,kBAAkB,GAAG,KAAK;IAE/B;IACA,QAAQgF,GAAG;MACT,KAAK,gBAAgB;QACnB;QACA,IAAI,CAAC,IAAI,CAACpE,WAAW,IAAI,IAAI,CAACA,WAAW,CAAC4B,MAAM,KAAK,CAAC,EAAE;UACtD;UACA,IAAI,CAACQ,eAAe,EAAE;SACvB,MAAM;UACL;UACA,IAAI,CAAC5D,oBAAoB,GAAG,IAAI,CAACqC,gBAAgB,CAAC,IAAI,CAACb,WAAW,CAAC;;QAErE;MACF,KAAK,UAAU;QACb;QACA,IAAI,CAAC,IAAI,CAACC,aAAa,IAAI,IAAI,CAACA,aAAa,CAAC2B,MAAM,KAAK,CAAC,EAAE;UAC1D;UACA,IAAI,CAACS,iBAAiB,EAAE;SACzB,MAAM;UACL;UACA,IAAI,CAAC7D,oBAAoB,GAAG,IAAI,CAACqC,gBAAgB,CAAC,IAAI,CAACZ,aAAa,CAAC;;QAEvE;MACF,KAAK,SAAS;QACZ;QACA,IAAI,CAAC,IAAI,CAACC,aAAa,IAAI,IAAI,CAACA,aAAa,CAAC0B,MAAM,KAAK,CAAC,EAAE;UAC1D;UACA,IAAI,CAACU,iBAAiB,EAAE;SACzB,MAAM;UACL;UACA,IAAI,CAAC9D,oBAAoB,GAAG,IAAI,CAACqC,gBAAgB,CAAC,IAAI,CAACX,aAAa,CAAC;;QAEvE;MACF,KAAK,KAAK;MACV;QACE,IAAI,CAAClB,kBAAkB,GAAG,KAAK;QAC/B,IAAI,CAACR,oBAAoB,GAAG,IAAI,CAACF,YAAY;QAC7C;;EAEN;EAEA+F,kBAAkBA,CAAA;IAChB,IAAI,CAACrF,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAkB;EACpD;EAEAsF,mBAAmBA,CACjBC,KAAa,EACbC,QAAuC;IAEvC,OAAOA,QAAQ,CAAC3E,GAAG;EACrB;EAEA4E,aAAaA,CAACF,KAAa,EAAEG,IAAS;IACpC,OAAOA,IAAI,CAACC,EAAE;EAChB;EAEA;;;;EAIAC,eAAeA,CAACC,KAAU;IACxB,IAAI,CAACA,KAAK,EAAE;IAEZ;IACA,IAAI,CAACtF,aAAa,GAAGsF,KAAK,CAACC,SAAS;IAEpC;IACC,IAAI,CAACxF,SAAiB,CAACyF,iBAAiB,GAAGF,KAAK,CAACC,SAAS;IAE3D;IACA,IAAI,CAAC,IAAI,CAACxF,SAAS,CAAC0F,OAAO,IAAI,IAAI,CAAC1F,SAAS,CAAC0F,OAAO,CAACC,IAAI,EAAE,KAAK,EAAE,EAAE;MACnE,IAAI,CAAC3F,SAAS,CAAC0F,OAAO,GAAG,IAAIH,KAAK,CAACC,SAAS,GAAG;;EAEnD;EAEAI,WAAWA,CAAC5D,KAAY;IACtBA,KAAK,CAAC6D,eAAe,EAAE;IACvB,MAAMC,UAAU,GAAI9D,KAAK,CAAC+D,MAA2B,CAACC,KAAK,CAACC,WAAW,EAAE;IAEzE,IAAI,CAACH,UAAU,EAAE;MACf,IAAI,CAAC5G,oBAAoB,GAAG;QAAE,GAAG,IAAI,CAACF;MAAY,CAAE;MACpD;;IAGF,IAAI,CAACE,oBAAoB,GAAG,EAAE;IAC9BgH,MAAM,CAACC,IAAI,CAAC,IAAI,CAACnH,YAAY,CAAC,CAACoH,OAAO,CAAEC,KAAK,IAAI;MAC/C,IAAI,CAACnH,oBAAoB,CAACmH,KAAK,CAAC,GAAG,IAAI,CAACrH,YAAY,CAACqH,KAAK,CAAC,CAACtI,MAAM,CAC/DqH,IAAS,IAAKA,IAAI,CAACjE,KAAK,CAAC8E,WAAW,EAAE,CAAC9D,QAAQ,CAAC2D,UAAU,CAAC,CAC7D;IACH,CAAC,CAAC;EACJ;EAEAQ,cAAcA,CAACtE,KAAY;IACzBA,KAAK,CAAC6D,eAAe,EAAE;IACvB,IAAI,CAAC1G,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;EAC5C;EAEAoH,WAAWA,CAACvE,KAAY;IACtBA,KAAK,CAAC6D,eAAe,EAAE;IACvB,IAAI,CAAC1G,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACR,eAAe,CAAC6H,MAAM,GAAG,CAAC,CAAC,CAAC;IAEjC,IAAI,IAAI,CAACxF,aAAa,EAAE;MACtB;MACA,IAAI,CAACpC,MAAM,CAAC6H,QAAQ,CAAC,CAAC,YAAY,EAAE,IAAI,CAACzF,aAAa,EAAE,MAAM,CAAC,CAAC;KACjE,MAAM;MACL;MACA,IAAI,CAACpC,MAAM,CAAC6H,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;;EAEnC;EAEAC,UAAUA,CACR1E,KAAY,EACZoD,IAMC;IAED;IACA,IAAI,CAACzG,eAAe,CAAC6H,MAAM,GAAGpB,IAAI,CAACC,EAAE;IAErC;IACA,IAAI,IAAI,CAACrE,aAAa,EAAE;MACtB,IAAI,CAACpC,MAAM,CAAC6H,QAAQ,CAAC,CAAC,YAAY,EAAE,IAAI,CAACzF,aAAa,EAAE,MAAM,EAAEoE,IAAI,CAACC,EAAE,CAAC,CAAC;KAC1E,MAAM;MACL,IAAI,CAACzG,MAAM,CAAC6H,QAAQ,CAAC,CAAC,OAAO,EAAErB,IAAI,CAACC,EAAE,CAAC,CAAC;;IAG1CrD,KAAK,CAAC6D,eAAe,EAAE;IACvBT,IAAI,CAACuB,SAAS,GAAG,CAACvB,IAAI,CAACuB,SAAS;EAClC;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAClI,eAAe,CAACmI,YAAY,GAAG,IAAI;EAC1C;EAEAC,eAAeA,CAAC1B,IAAS;IACvB,IAAI,CAACoB,MAAM,GAAGpB,IAAI,CAACC,EAAE;IACrB,IAAI,CAAC9G,YAAY,CAACwI,GAAG,CAAC,IAAI,CAACP,MAAM,EAAE,CAACpB,IAAI,CAAC4B,QAAQ,CAAC,CAAC/E,SAAS,CAAEwB,GAAG,IAAI;MACnE,IAAIA,GAAG,CAACuD,QAAQ,EAAE;QAChB,IAAI,CAACvI,gBAAgB,CAACwI,OAAO,CAAC,2BAA2B,CAAC;QAC1D,IAAI,CAACvG,WAAW,GAAG,CAAC,GAAG,IAAI,CAACA,WAAW,EAAE+C,GAAG,CAAC;OAC9C,MAAM;QACL,IAAI,CAAChF,gBAAgB,CAACwI,OAAO,CAAC,6BAA6B,CAAC;QAC5D,IAAI,CAACvG,WAAW,GAAG,IAAI,CAACA,WAAW,CAAC3C,MAAM,CAAEmJ,CAAC,IAAKA,CAAC,CAAC7B,EAAE,KAAK5B,GAAG,CAAC4B,EAAE,CAAC;;MAEpE,MAAMJ,KAAK,GAAG,IAAI,CAACtG,eAAe,CAACkF,QAAQ,CAACsD,SAAS,CAClDD,CAAM,IAAKA,CAAC,CAAC7B,EAAE,KAAK5B,GAAG,CAAC4B,EAAE,CAC5B;MACD,IAAIJ,KAAK,KAAK,CAAC,CAAC,EAAE;QAChB,IAAI,CAACtG,eAAe,CAACkF,QAAQ,CAACoB,KAAK,CAAC,GAAGxB,GAAG;QAC1C,IAAI,CAAC9E,eAAe,CAAC4C,gBAAgB,EAAE;QACvC,IAAI,CAACvC,YAAY,GAAG,IAAI,CAACL,eAAe,CAACK,YAAY;QACrD,IAAI,CAACE,oBAAoB,GAAG;UAAE,GAAG,IAAI,CAACF;QAAY,CAAE;;IAExD,CAAC,CAAC;EACJ;EAEAoI,YAAYA,CAAChC,IAAS;IACpB,IAAI,CAACoB,MAAM,GAAGpB,IAAI,CAACC,EAAE;IACrB,IAAI,CAAC9G,YAAY,CACdiD,QAAQ,CAAC,IAAI,CAACgF,MAAM,EAAE,CAACpB,IAAI,CAACiC,UAAU,CAAC,CACvCpF,SAAS,CAAEwB,GAAG,IAAI;MACjB,IAAIA,GAAG,CAAC4D,UAAU,EAAE;QAClB,IAAI,CAAC5I,gBAAgB,CAACwI,OAAO,CAAC,8BAA8B,CAAC;QAC7D,IAAI,CAACtG,aAAa,GAAG,CAAC,GAAG,IAAI,CAACA,aAAa,EAAE8C,GAAG,CAAC;OAClD,MAAM;QACL,IAAI,CAAChF,gBAAgB,CAACwI,OAAO,CAAC,gCAAgC,CAAC;QAC/D,IAAI,CAACtG,aAAa,GAAG,IAAI,CAACA,aAAa,CAAC5C,MAAM,CAC3CmJ,CAAC,IAAKA,CAAC,CAAC7B,EAAE,KAAK5B,GAAG,CAAC4B,EAAE,CACvB;;MAEH,MAAMJ,KAAK,GAAG,IAAI,CAACtG,eAAe,CAACkF,QAAQ,CAACsD,SAAS,CAClDD,CAAM,IAAKA,CAAC,CAAC7B,EAAE,KAAK5B,GAAG,CAAC4B,EAAE,CAC5B;MACD,IAAIJ,KAAK,KAAK,CAAC,CAAC,EAAE;QAChB,IAAI,CAACtG,eAAe,CAACkF,QAAQ,CAACoB,KAAK,CAAC,GAAGxB,GAAG;QAC1C,IAAI,CAAC9E,eAAe,CAAC4C,gBAAgB,EAAE;QACvC,IAAI,CAACvC,YAAY,GAAG,IAAI,CAACL,eAAe,CAACK,YAAY;QACrD,IAAI,CAACE,oBAAoB,GAAG;UAAE,GAAG,IAAI,CAACF;QAAY,CAAE;;IAExD,CAAC,CAAC;EACN;EAEAsI,gBAAgBA,CAAClC,IAAS;IACxB,IAAI,CAACoB,MAAM,GAAGpB,IAAI,CAACC,EAAE;IACrB,IAAI,CAAC9G,YAAY,CACdkD,OAAO,CAAC,IAAI,CAAC+E,MAAM,EAAE,CAACpB,IAAI,CAACmC,UAAU,CAAC,CACtCtF,SAAS,CAAEwB,GAAG,IAAI;MACjB,IAAIA,GAAG,CAAC8D,UAAU,EAAE;QAClB,IAAI,CAAC9I,gBAAgB,CAACwI,OAAO,CAAC,6BAA6B,CAAC;QAC5D,IAAI,CAACrG,aAAa,GAAG,CAAC,GAAG,IAAI,CAACA,aAAa,EAAE6C,GAAG,CAAC;QACjD,IAAI,CAAC9E,eAAe,CAACkF,QAAQ,GAAG,IAAI,CAAClF,eAAe,CAACkF,QAAQ,CAAC9F,MAAM,CACjEmJ,CAAM,IAAKA,CAAC,CAAC7B,EAAE,KAAK5B,GAAG,CAAC4B,EAAE,CAC5B;OACF,MAAM;QACL,IAAI,CAAC5G,gBAAgB,CAACwI,OAAO,CAAC,+BAA+B,CAAC;QAC9D,IAAI,CAACrG,aAAa,GAAG,IAAI,CAACA,aAAa,CAAC7C,MAAM,CAC3CmJ,CAAC,IAAKA,CAAC,CAAC7B,EAAE,KAAK5B,GAAG,CAAC4B,EAAE,CACvB;QACD,IAAI,CAAC1G,eAAe,CAACkF,QAAQ,CAACU,IAAI,CAACd,GAAG,CAAC;;MAEzC,IAAI,CAAC9E,eAAe,CAAC4C,gBAAgB,EAAE;MACvC,IAAI,CAACvC,YAAY,GAAG,IAAI,CAACL,eAAe,CAACK,YAAY;MACrD,IAAI,CAACE,oBAAoB,GAAG;QAAE,GAAG,IAAI,CAACF;MAAY,CAAE;IACtD,CAAC,CAAC;EACN;EAEAwI,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACpI,SAAS,KAAK,KAAK,EAAE;MAC5B,OAAO,IAAI,CAACJ,YAAY;;IAG1B,MAAMoC,KAAK,GAAG,IAAI,CAAClC,oBAAoB;IACvC,IAAI,IAAI,CAAC+B,SAAS,CAAC,IAAI,CAAC7B,SAAS,CAAC,CAACiC,SAAS,EAAE;MAC5C,OAAO6E,MAAM,CAACC,IAAI,CAAC/E,KAAK,CAAC,CAACkB,MAAM,GAAG,CAAC,GAAGlB,KAAK,GAAG;QAAE,UAAU,EAAE;MAAE,CAAE;;IAEnE,OAAOA,KAAK;EACd;EAEAqG,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAACxG,SAAS,CAAC,IAAI,CAAC7B,SAAS,CAAC,CAAC+B,KAAK;EAC7C;EAEAuG,mBAAmBA,CAAA;IACjB,OAAO,IAAI,CAACzG,SAAS,CAAC,IAAI,CAAC7B,SAAS,CAAC,CAACiC,SAAS;EACjD;EAEAsG,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAAC1G,SAAS,CAAC,IAAI,CAAC7B,SAAS,CAAC,CAACkC,OAAO,IAAI,IAAI,CAACd,eAAe;EACvE;EAEAoH,WAAWA,CAAA;IACT,IAAI,CAACjG,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;IAClC,IAAI,IAAI,CAACA,UAAU,EAAE;MACnBsB,QAAQ,CAACC,IAAI,CAACC,SAAS,CAAC0E,GAAG,CAAC,WAAW,CAAC;KACzC,MAAM;MACL5E,QAAQ,CAACC,IAAI,CAACC,SAAS,CAAC2E,MAAM,CAAC,WAAW,CAAC;;EAE/C;EAEQvG,gBAAgBA,CAACH,KAAY;IACnC;IACA,IAAI,CAACA,KAAK,IAAIA,KAAK,CAACkB,MAAM,KAAK,CAAC,EAAE;MAChC,OAAO,EAAE;;IAGX,MAAMyF,OAAO,GAA6B,EAAE;IAC5C,MAAMC,KAAK,GAAG,IAAIC,IAAI,EAAE;IACxB,MAAMC,SAAS,GAAG,IAAID,IAAI,CAACD,KAAK,CAAC;IACjCE,SAAS,CAACC,OAAO,CAACH,KAAK,CAACI,OAAO,EAAE,GAAG,CAAC,CAAC;IACtC,MAAMC,YAAY,GAAG,IAAIJ,IAAI,CAACD,KAAK,CAAC;IACpCK,YAAY,CAACF,OAAO,CAACH,KAAK,CAACI,OAAO,EAAE,GAAG,CAAC,CAAC;IACzC,MAAME,aAAa,GAAG,IAAIL,IAAI,CAACD,KAAK,CAAC;IACrCM,aAAa,CAACH,OAAO,CAACH,KAAK,CAACI,OAAO,EAAE,GAAG,EAAE,CAAC;IAE3ChH,KAAK,CAACgF,OAAO,CAAEhB,IAAI,IAAI;MACrB,IAAI,CAACA,IAAI,IAAIA,IAAI,CAACmD,WAAW,KAAKC,SAAS,EAAE;QAC3C;;MAGF,IAAI;QACF,IAAIC,IAAU;QACd,IAAI,OAAOrD,IAAI,CAACmD,WAAW,KAAK,QAAQ,EAAE;UACxCE,IAAI,GAAG,IAAIR,IAAI,CAAC7C,IAAI,CAACmD,WAAW,CAAC;SAClC,MAAM,IACLnD,IAAI,CAACmD,WAAW,IAChB,OAAOnD,IAAI,CAACmD,WAAW,KAAK,QAAQ,IACpCnD,IAAI,CAACmD,WAAW,CAACG,QAAQ,EACzB;UACAD,IAAI,GAAG,IAAIR,IAAI,CAAC7C,IAAI,CAACmD,WAAW,CAACG,QAAQ,EAAE,CAAC;SAC7C,MAAM;UACL;;QAGF,IAAIC,KAAK,CAACF,IAAI,CAACG,OAAO,EAAE,CAAC,EAAE;UACzB;;QAGF,IAAIC,QAKM;QAEV,IAAI,IAAI,CAACC,SAAS,CAACL,IAAI,EAAET,KAAK,CAAC,EAAE;UAC/Ba,QAAQ,GAAG,OAAO;SACnB,MAAM,IAAI,IAAI,CAACC,SAAS,CAACL,IAAI,EAAEP,SAAS,CAAC,EAAE;UAC1CW,QAAQ,GAAG,WAAW;SACvB,MAAM,IAAIJ,IAAI,IAAIJ,YAAY,EAAE;UAC/BQ,QAAQ,GAAG,iBAAiB;SAC7B,MAAM,IAAIJ,IAAI,IAAIH,aAAa,EAAE;UAChCO,QAAQ,GAAG,kBAAkB;SAC9B,MAAM;UACLA,QAAQ,GAAGJ,IAAI,CAACM,kBAAkB,CAAC,OAAO,EAAE;YAC1CC,KAAK,EAAE,MAAM;YACbC,IAAI,EAAE;WACP,CAAC;;QAGJ,IAAI,CAAClB,OAAO,CAACc,QAAQ,CAAC,EAAE;UACtBd,OAAO,CAACc,QAAQ,CAAC,GAAG,EAAE;;QAExBd,OAAO,CAACc,QAAQ,CAAC,CAACtE,IAAI,CAACa,IAAI,CAAC;OAC7B,CAAC,OAAOnB,KAAK,EAAE;QACdtB,OAAO,CAACsB,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;;IAEvD,CAAC,CAAC;IAEF;IACA,KAAK,MAAM1D,GAAG,IAAIwH,OAAO,EAAE;MACzBA,OAAO,CAACxH,GAAG,CAAC,CAAC2I,IAAI,CAAC,CAAC/I,CAAC,EAAEC,CAAC,KAAI;QACzB,IAAI;UACF,MAAM+I,KAAK,GAAG,IAAIlB,IAAI,CAAC9H,CAAC,CAACoI,WAAW,CAACG,QAAQ,EAAE,CAAC,CAACE,OAAO,EAAE;UAC1D,MAAMQ,KAAK,GAAG,IAAInB,IAAI,CAAC7H,CAAC,CAACmI,WAAW,CAACG,QAAQ,EAAE,CAAC,CAACE,OAAO,EAAE;UAC1D,OAAOQ,KAAK,GAAGD,KAAK;SACrB,CAAC,OAAOlF,KAAK,EAAE;UACd,OAAO,CAAC;;MAEZ,CAAC,CAAC;;IAGJ,OAAO8D,OAAO;EAChB;EAEQe,SAASA,CAACO,KAAW,EAAEC,KAAW;IACxC,OACED,KAAK,CAACE,WAAW,EAAE,KAAKD,KAAK,CAACC,WAAW,EAAE,IAC3CF,KAAK,CAACG,QAAQ,EAAE,KAAKF,KAAK,CAACE,QAAQ,EAAE,IACrCH,KAAK,CAACjB,OAAO,EAAE,KAAKkB,KAAK,CAAClB,OAAO,EAAE;EAEvC;EAEA;EACAqB,sBAAsBA,CAACzH,KAAY;IACjCA,KAAK,CAAC6D,eAAe,EAAE;IAEvB;IACA,IAAI,CAAClH,eAAe,CAAC6H,MAAM,GAAG,CAAC;IAE/B;IACA;IACA;IACA;IACF,IAAI,CAAC,IAAI,CAAC9H,eAAe,CAACmI,YAAY,EAAE;MACpC,IAAI,CAACnI,eAAe,CAACgL,YAAY,EAAE;;IAErC;IACA,IAAI,CAAC9K,MAAM,CAAC6H,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;EAC7B;EAEA;EACAkD,kBAAkBA,CAAC3H,KAAY,EAAE4H,OAAe;IAC9C5H,KAAK,CAAC6D,eAAe,EAAE;IAEvB;IACA,IAAI,CAACjG,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACN,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACQ,kBAAkB,GAAG,KAAK;IAE/B;IACA,IAAI,CAACD,cAAc,GAAG+J,OAAO;IAE7B;IACA,IAAI,CAAC,IAAI,CAAClL,eAAe,CAACmI,YAAY,EAAE;MACtC,IAAI,CAACnI,eAAe,CAACgL,YAAY,EAAE;;IAGrC;IACA,IAAI,CAAC9K,MAAM,CAAC6H,QAAQ,CAAC,CAAC,WAAW,EAAEmD,OAAO,CAAC,CAAC;EAC9C;EAEA;EACAC,kBAAkBA,CAAC7H,KAAY;IAC7BA,KAAK,CAAC6D,eAAe,EAAE;IAEvB;IACA,IAAI,CAACjG,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACE,kBAAkB,GAAG,KAAK;IAE/B,IAAI,CAAC,IAAI,CAACpB,eAAe,CAACmI,YAAY,EAAE;MACtC,IAAI,CAACnI,eAAe,CAACgL,YAAY,EAAE;;IAGrC;IACA,IAAI,CAAC9C,WAAW,EAAE;IAClB,IAAI,CAACxH,SAAS,GAAG,KAAK;IAEtB;IACA,IAAI,CAACT,eAAe,CAAC6H,MAAM,GAAG,CAAC;IAE/B;IACA,IAAI,IAAI,CAACxF,aAAa,EAAE;MACtB;MACA,IAAI,CAACpC,MAAM,CAAC6H,QAAQ,CAAC,CAAC,YAAY,EAAE,IAAI,CAACzF,aAAa,EAAE,MAAM,CAAC,CAAC;KACjE,MAAM;MACL;MACA,IAAI,CAACpC,MAAM,CAAC6H,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;;EAEnC;EAEA;EACAqD,mBAAmBA,CAAC9H,KAAY;IAC9BA,KAAK,CAAC6D,eAAe,EAAE;IAEvB;IACA,IAAI,CAAClH,eAAe,CAAC6H,MAAM,GAAG,CAAC;IAE/B;IACA,IAAI,IAAI,CAAC9H,eAAe,CAACmI,YAAY,EAAE;MACrC,IAAI,CAACnI,eAAe,CAACgL,YAAY,EAAE;;IAGrC;IACA,IAAI,CAAC9K,MAAM,CAAC6H,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;EACvC;EAEA;EAEA;EACAsD,eAAeA,CAAC/H,KAAY;IAC1BA,KAAK,CAAC6D,eAAe,EAAE;IAEvB;IACA,IAAI,CAAClH,eAAe,CAAC6H,MAAM,GAAG,CAAC;IAE/B;IACA,IAAI,CAAC5G,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACN,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACQ,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACC,WAAW,GAAG,IAAI;IAEvB;IACA,IAAI,CAAC,IAAI,CAACrB,eAAe,CAACmI,YAAY,EAAE;MACtC,IAAI,CAACnI,eAAe,CAACgL,YAAY,EAAE;;IAGrC;IACA,IAAI,CAAC9K,MAAM,CAAC6H,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;;;;;;;;;;;AA95BWpI,gBAAgB,GAAA2L,UAAA,EAlB5BpN,SAAS,CAAC;EACTqN,QAAQ,EAAE,aAAa;EACvBC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACP5M,kBAAkB,EAClBC,YAAY,EACZC,WAAW,EACXN,UAAU,EACVQ,cAAc,EACdC,eAAe,EACfK,6BAA6B,EAC7BC,yBAAyB,EACzBC,gCAAgC,EAChCC,qBAAqB,CACtB;EACDgM,QAAA,EAAAC,oBAAuC;;CAExC,CAAC,C,EACWhM,gBAAgB,CAk6B5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}