<!-- MS Teams Style Chat Container -->
<div class="teams-chat-container h-full w-full" [ngClass]="{
    'bg-[#2b2b33] text-white': themeService.isDarkMode(),
    'bg-[var(--background-white)] text-[var(--text-dark)]': !themeService.isDarkMode()
  }">
  <!-- Header Section -->
  <div class="chat-header px-4 py-3 border-b" [ngClass]="{
      'border-[#404040]': themeService.isDarkMode(),
      'border-[var(--hover-blue-gray)]': !themeService.isDarkMode()
    }">
    <div class="flex items-center justify-between">
      <div class="flex items-center gap-2">
        <i class="ri-arrow-down-s-line text-sm" [ngClass]="{
            'text-gray-400': themeService.isDarkMode(),
            'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
          }"></i>
        <h2 class="text-sm font-medium" [ngClass]="{
            'text-white': themeService.isDarkMode(),
            'text-[var(--text-dark)]': !themeService.isDarkMode()
          }">
          Agents
        </h2>
      </div>

    </div>
  </div>
  <!-- Chat List Container -->
  <div class="chat-list-container flex-1 overflow-y-auto px-2 py-2">
    <!-- Loading State -->
    <div *ngIf="isLoadingAgents || isLoadingWorkspaces" class="flex items-center justify-center py-8">
      <div class="flex items-center gap-2">
        <div class="w-4 h-4 border-2 border-[var(--primary-purple)] border-t-transparent rounded-full animate-spin">
        </div>
        <span class="text-sm" [ngClass]="{
            'text-gray-300': themeService.isDarkMode(),
            'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
          }">
          Loading...
        </span>
      </div>
    </div>
    <!-- Agents Accordion Section -->
    <div class="accordion-section mb-4">
      <!-- Agents Header -->
      <div (click)="toggleAccordionSection('agents')"
        class="accordion-header flex items-center justify-between px-3 py-2 rounded-lg cursor-pointer transition-all duration-200"
        [ngClass]="{
          'hover:bg-[#3a3a45] hover:bg-opacity-60': themeService.isDarkMode(),
          'hover:bg-[var(--background-light-gray)]': !themeService.isDarkMode()
        }">
        <div class="flex items-center gap-2">
          <i class="ri-robot-line text-sm" [ngClass]="{
              'text-gray-400': themeService.isDarkMode(),
              'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
            }"></i>
          <h3 class="text-sm font-medium" [ngClass]="{
              'text-white': themeService.isDarkMode(),
              'text-[var(--text-dark)]': !themeService.isDarkMode()
            }">
            Agents
          </h3>
          <span class="text-xs px-1.5 py-0.5 rounded-full" [ngClass]="{
              'bg-[#3a3a45] text-gray-300': themeService.isDarkMode(),
              'bg-[var(--background-light-gray)] text-[var(--text-medium-gray)]': !themeService.isDarkMode()
            }">
            {{ agents.length }}
          </span>
        </div>
        <!-- Chevron Icon -->
        <div class="transition-transform duration-200" [ngClass]="{
            'rotate-180': accordionSections.agents.isExpanded,
            'rotate-0': !accordionSections.agents.isExpanded
          }">
          <i class="ri-arrow-down-s-line text-sm" [ngClass]="{
              'text-gray-400': themeService.isDarkMode(),
              'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
            }"></i>
        </div>
      </div>
      <!-- Agents Content -->
      <div class="accordion-content overflow-hidden transition-all duration-300 ease-in-out" [ngClass]="{
          'max-h-96 opacity-100 mt-2': accordionSections.agents.isExpanded,
          'max-h-0 opacity-0 mt-0': !accordionSections.agents.isExpanded
        }">
        <!-- Agents List -->
        <div *ngIf="!isLoadingAgents && !agentsError && agents.length > 0">
          @for (agentName of agents; track $index) {
          <div (click)="navigateToAgentChat(agentName)"
            class="chat-item flex items-center gap-3 px-3 py-2 rounded-lg cursor-pointer transition-all duration-150 mb-1"
            [ngClass]="{
                'hover:bg-[#3a3a45] hover:bg-opacity-60': themeService.isDarkMode(),
                'hover:bg-[var(--background-light-gray)]': !themeService.isDarkMode()
              }">

            <!-- Agent Name and Type -->
            <div class="flex-1 min-w-0">
              <div class="text-sm font-medium truncate" [ngClass]="{
                    'text-white': themeService.isDarkMode(),
                    'text-[var(--text-dark)]': !themeService.isDarkMode()
                  }">
                {{ formatAgentName(agentName) }}
              </div>
            </div>

          </div>
          }
        </div>
        <!-- Error State for Agents -->
        <div *ngIf="agentsError" class="px-3 py-2">
          <div class="text-red-500 text-sm">{{ agentsError }}</div>
        </div>
      </div>
    </div>
    <!-- Workspaces Accordion Section -->
    <div class="accordion-section">
      <!-- Workspaces Header -->
      <div (click)="toggleAccordionSection('workspaces')"
        class="accordion-header flex items-center justify-between px-3 py-2 rounded-lg cursor-pointer transition-all duration-200"
        [ngClass]="{
          'hover:bg-[#3a3a45] hover:bg-opacity-60': themeService.isDarkMode(),
          'hover:bg-[var(--background-light-gray)]': !themeService.isDarkMode()
        }">
        <div class="flex items-center gap-2">
          <i class="ri-building-line text-sm" [ngClass]="{
              'text-gray-400': themeService.isDarkMode(),
              'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
            }"></i>
          <h3 class="text-sm font-medium" [ngClass]="{
              'text-white': themeService.isDarkMode(),
              'text-[var(--text-dark)]': !themeService.isDarkMode()
            }">
            Workspaces
          </h3>
          <span class="text-xs px-1.5 py-0.5 rounded-full" [ngClass]="{
              'bg-[#3a3a45] text-gray-300': themeService.isDarkMode(),
              'bg-[var(--background-light-gray)] text-[var(--text-medium-gray)]': !themeService.isDarkMode()
            }">
            {{ workspaces.length }}
          </span>
        </div>
        <!-- Chevron Icon -->
        <div class="transition-transform duration-200" [ngClass]="{
            'rotate-180': accordionSections.workspaces.isExpanded,
            'rotate-0': !accordionSections.workspaces.isExpanded
          }">
          <i class="ri-arrow-down-s-line text-sm" [ngClass]="{
              'text-gray-400': themeService.isDarkMode(),
              'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
            }"></i>
        </div>
      </div>

      <!-- Workspaces Content -->
      <div class="accordion-content overflow-hidden transition-all duration-300 ease-in-out" [ngClass]="{
          'max-h-96 opacity-100 mt-2': accordionSections.workspaces.isExpanded,
          'max-h-0 opacity-0 mt-0': !accordionSections.workspaces.isExpanded
        }">

        <!-- Workspaces List -->
        <div *ngIf="!isLoadingWorkspaces && !workspacesError && workspaces.length > 0">
          @for (workspace of workspaces; track workspace.id) {
          <div (click)="navigateToWorkspace(workspace)"
            class="chat-item flex items-center gap-3 px-3 py-2 rounded-lg cursor-pointer transition-all duration-150 mb-1"
            [ngClass]="{
                'hover:bg-[#3a3a45] hover:bg-opacity-60': themeService.isDarkMode(),
                'hover:bg-[var(--background-light-gray)]': !themeService.isDarkMode()
              }">
            <!-- Workspace Details -->
            <div class="flex-1 min-w-0">
              <div class="text-sm font-medium truncate" [ngClass]="{
                    'text-white': themeService.isDarkMode(),
                    'text-[var(--text-dark)]': !themeService.isDarkMode()
                  }">
                {{ workspace.title }}
              </div>
            </div>

          </div>
          }
        </div>
      </div>
    </div>
  </div>
</div>
