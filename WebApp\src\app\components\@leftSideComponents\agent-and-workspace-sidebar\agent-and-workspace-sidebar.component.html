<!-- Agent and Workspace Sidebar Container -->
<div class="h-full flex flex-col bg-[var(--background-white)] text-[var(--text-dark)]" [ngClass]="{
    'bg-[#2b2b33] text-white': themeService.isDarkMode(),
    'bg-white text-[var(--text-dark)]': !themeService.isDarkMode()
  }">

  <!-- Header Section -->
  <div class="px-[var(--padding-small)] py-[var(--padding-small)] border-b border-[var(--hover-blue-gray)] flex justify-between flex-col gap-2" [ngClass]="{
      'border-[#3a3a45]': themeService.isDarkMode(),
      'border-[var(--border-light)]': !themeService.isDarkMode()
    }">
    <div class="flex items-center justify-between w-full">
      <div class="flex flex-col">
        <div class="flex items-center gap-1.5">
          <span class="font-bold text-[var(--text-dark)] text-lg" [ngClass]="{
              'text-white': themeService.isDarkMode(),
              'text-[var(--text-dark)]': !themeService.isDarkMode()
            }">
            Chats
          </span>
        </div>
      </div>

    </div>
  </div>
  <!-- Content Section -->
  <div class="flex-1 overflow-y-auto">
    <!-- Loading State -->
    <div *ngIf="isLoadingAgents || isLoadingWorkspaces" class="flex items-center justify-center py-8">
      <div class="flex items-center gap-2">
        <div class="w-4 h-4 border-2 border-[var(--primary-purple)] border-t-transparent rounded-full animate-spin">
        </div>
        <span class="text-sm" [ngClass]="{
            'text-gray-300': themeService.isDarkMode(),
            'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
          }">
          Loading...
        </span>
      </div>
    </div>

    <!-- Accordion Content -->
    <div class="flex-1 px-[var(--padding-small)] overflow-y-auto">
      <div class="py-[var(--padding-small)]">
    <!-- Agents Accordion Section -->
    <div class="accordion-section mb-4">
      <!-- Agents Header -->
      <div (click)="toggleAccordionSection('agents')"
        class="accordion-header flex items-center justify-between px-3 py-2 rounded-lg cursor-pointer transition-all duration-200"
        [ngClass]="{
          'hover:bg-[#3a3a45] hover:bg-opacity-60': themeService.isDarkMode(),
          'hover:bg-[var(--background-light-gray)]': !themeService.isDarkMode()
        }">
        <div class="flex items-center gap-2">
          <i class="ri-robot-line text-lg" [ngClass]="{
              'text-gray-400': themeService.isDarkMode(),
              'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
            }"></i>
          <h3 class="text-sm font-medium" [ngClass]="{
              'text-white': themeService.isDarkMode(),
              'text-[var(--text-dark)]': !themeService.isDarkMode()
            }">
            Agents
          </h3>
          <span class="text-xs px-1.5 py-0.5 rounded-full" [ngClass]="{
              'bg-[#3a3a45] text-gray-300': themeService.isDarkMode(),
              'bg-[var(--background-light-gray)] text-[var(--text-medium-gray)]': !themeService.isDarkMode()
            }">
            {{ agents.length }}
          </span>
        </div>
        <!-- Chevron Icon -->
        <div class="transition-transform duration-200" [ngClass]="{
            'rotate-180': accordionSections.agents.isExpanded,
            'rotate-0': !accordionSections.agents.isExpanded
          }">
          <i class="ri-arrow-down-s-line text-sm transition-colors duration-200" [ngClass]="{
              'text-[#ACACBE]': themeService.isDarkMode(),
              'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
            }"></i>
        </div>
      </div>
      <!-- Agents Content -->
      <div class="accordion-content overflow-hidden transition-all duration-300 ease-in-out" [ngClass]="{
          'max-h-96 opacity-100 mt-2': accordionSections.agents.isExpanded,
          'max-h-0 opacity-0 mt-0': !accordionSections.agents.isExpanded
        }">
        <!-- Agents List -->
        <div *ngIf="!isLoadingAgents && !agentsError && agents.length > 0" class="space-y-1">
          @for (agentName of agents; track $index) {
          <div (click)="navigateToAgentChat(agentName)"
            class="flex items-center gap-3 py-2 px-3 rounded-md cursor-pointer transition-all relative group"
            [ngClass]="{
              'hover:bg-[var(--primary-purple)]': true,
              'hover:text-white': true
            }">

            <!-- Active indicator bar -->
            <div class="absolute left-0 top-0 bottom-0 w-1.5 rounded-r-full transition-all opacity-0"></div>

            <i class="ri-robot-line text-sm transition-colors duration-200" [ngClass]="{
                'text-[#ACACBE]': themeService.isDarkMode(),
                'text-[var(--primary-purple)]': !themeService.isDarkMode(),
                'group-hover:text-white': themeService.isDarkMode(),
                'group-hover:text-black': !themeService.isDarkMode()
              }"></i>

            <span class="font-medium text-sm transition-colors duration-200 truncate flex-1 min-w-0" [ngClass]="{
                'text-[#ACACBE]': themeService.isDarkMode(),
                'text-[var(--text-dark)]': !themeService.isDarkMode(),
                'group-hover:text-white': themeService.isDarkMode(),
                'group-hover:text-black': !themeService.isDarkMode()
              }">
              {{ formatAgentName(agentName) }}
            </span>
          </div>
          }
        </div>
        <!-- Error State for Agents -->
        <div *ngIf="agentsError" class="px-3 py-2">
          <div class="text-red-500 text-sm">{{ agentsError }}</div>
        </div>
      </div>
    </div>
    <!-- Workspaces Accordion Section -->
    <div class="accordion-section">
      <!-- Workspaces Header -->
      <div (click)="toggleAccordionSection('workspaces')"
        class="accordion-header flex items-center justify-between px-3 py-2 rounded-lg cursor-pointer transition-all duration-200"
        [ngClass]="{
          'hover:bg-[#3a3a45] hover:bg-opacity-60': themeService.isDarkMode(),
          'hover:bg-[var(--background-light-gray)]': !themeService.isDarkMode()
        }">
        <div class="flex items-center gap-2">
          <i class="ri-building-line text-lg" [ngClass]="{
              'text-gray-400': themeService.isDarkMode(),
              'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
            }"></i>
          <h3 class="text-sm font-medium" [ngClass]="{
              'text-white': themeService.isDarkMode(),
              'text-[var(--text-dark)]': !themeService.isDarkMode()
            }">
            Workspaces
          </h3>
          <span class="text-xs px-1.5 py-0.5 rounded-full" [ngClass]="{
              'bg-[#3a3a45] text-gray-300': themeService.isDarkMode(),
              'bg-[var(--background-light-gray)] text-[var(--text-medium-gray)]': !themeService.isDarkMode()
            }">
            {{ workspaces.length }}
          </span>
        </div>
        <!-- Chevron Icon -->
        <div class="transition-transform duration-200" [ngClass]="{
            'rotate-180': accordionSections.workspaces.isExpanded,
            'rotate-0': !accordionSections.workspaces.isExpanded
          }">
          <i class="ri-arrow-down-s-line text-sm" [ngClass]="{
              'text-gray-400': themeService.isDarkMode(),
              'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
            }"></i>
        </div>
      </div>

      <!-- Workspaces Content -->
      <div class="accordion-content overflow-hidden transition-all duration-300 ease-in-out" [ngClass]="{
          'max-h-96 opacity-100 mt-2': accordionSections.workspaces.isExpanded,
          'max-h-0 opacity-0 mt-0': !accordionSections.workspaces.isExpanded
        }">

        <!-- Workspaces List -->
        <div *ngIf="!isLoadingWorkspaces && !workspacesError && workspaces.length > 0" class="space-y-1">
          @for (workspace of workspaces; track workspace.id) {
          <div (click)="navigateToWorkspace(workspace)"
            class="flex items-center gap-3 py-2 px-3 rounded-md cursor-pointer transition-all relative group"
            [ngClass]="{
              'hover:bg-[var(--primary-purple)]': true,
              'hover:text-white': true
            }">

            <!-- Active indicator bar -->
            <div class="absolute left-0 top-0 bottom-0 w-1.5 rounded-r-full transition-all opacity-0"></div>

            <i class="ri-building-line text-sm transition-colors duration-200" [ngClass]="{
                'text-[#ACACBE]': themeService.isDarkMode(),
                'text-[var(--primary-purple)]': !themeService.isDarkMode(),
                'group-hover:text-white': themeService.isDarkMode(),
                'group-hover:text-black': !themeService.isDarkMode()
              }"></i>

            <span class="font-medium text-sm transition-colors duration-200 truncate flex-1 min-w-0" [ngClass]="{
                'text-[#ACACBE]': themeService.isDarkMode(),
                'text-[var(--text-dark)]': !themeService.isDarkMode(),
                'group-hover:text-white': themeService.isDarkMode(),
                'group-hover:text-black': !themeService.isDarkMode()
              }">
              {{ workspace.title }}
            </span>
          </div>
          }
        </div>

        <!-- Error State for Workspaces -->
        <div *ngIf="workspacesError" class="px-3 py-2">
          <div class="text-red-500 text-sm">{{ workspacesError }}</div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div *ngIf="!isLoadingAgents && !isLoadingWorkspaces && agents.length === 0 && workspaces.length === 0"
      class="flex flex-col items-center justify-center py-12 text-center">
      <span class="text-[var(--text-medium-gray)]">No chats available.</span>
      <i class="ri-emotion-sad-line text-[var(--text-medium-gray)] text-4xl mt-2"></i>
    </div>

      </div>
    </div>
  </div>
</div>
