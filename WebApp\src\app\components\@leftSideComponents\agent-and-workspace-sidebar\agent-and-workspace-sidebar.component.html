<!-- MS Teams Style Chat Container -->
<div class="teams-chat-container h-full w-full" [ngClass]="{
    'bg-[#2b2b33] text-white': themeService.isDarkMode(),
    'bg-[var(--background-white)] text-[var(--text-dark)]': !themeService.isDarkMode()
  }">

  <!-- Header Section -->
  <div class="chat-header px-4 py-3 border-b" [ngClass]="{
      'border-[#404040]': themeService.isDarkMode(),
      'border-[var(--hover-blue-gray)]': !themeService.isDarkMode()
    }">
    <div class="flex items-center justify-between">
      <div class="flex items-center gap-2">
        <i class="ri-arrow-down-s-line text-sm" [ngClass]="{
            'text-gray-400': themeService.isDarkMode(),
            'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
          }"></i>
        <h2 class="text-sm font-medium" [ngClass]="{
            'text-white': themeService.isDarkMode(),
            'text-[var(--text-dark)]': !themeService.isDarkMode()
          }">
          Agents
        </h2>
      </div>
    
    </div>
  </div>

  <!-- Chat List Container -->
  <div class="chat-list-container flex-1 overflow-y-auto px-2 py-2">

    <!-- Loading State -->
    <div *ngIf="isLoadingAgents || isLoadingWorkspaces" class="flex items-center justify-center py-8">
      <div class="flex items-center gap-2">
        <div class="w-4 h-4 border-2 border-[var(--primary-purple)] border-t-transparent rounded-full animate-spin"></div>
        <span class="text-sm" [ngClass]="{
            'text-gray-300': themeService.isDarkMode(),
            'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
          }">
          Loading...
        </span>
      </div>
    </div>

    <!-- Combined Agents and Workspaces List -->
    <div *ngIf="!isLoadingAgents && !isLoadingWorkspaces">

      <!-- Agents List -->
      @for (agentName of agents; track $index) {
        <div (click)="navigateToAgentChat(agentName)"
          class="chat-item flex items-center gap-3 px-3 py-2 rounded-lg cursor-pointer transition-all duration-150 mb-1"
          [ngClass]="{
            'hover:bg-[#3a3a45] hover:bg-opacity-60': themeService.isDarkMode(),
            'hover:bg-[var(--background-light-gray)]': !themeService.isDarkMode()
          }">

          <!-- Agent Avatar -->
          <div class="chat-avatar w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0"
            [ngClass]="{
              'bg-gradient-to-br from-blue-500 to-purple-600': getAgentAvatarColor(agentName) === 'blue',
              'bg-gradient-to-br from-green-500 to-teal-600': getAgentAvatarColor(agentName) === 'green',
              'bg-gradient-to-br from-orange-500 to-red-600': getAgentAvatarColor(agentName) === 'orange',
              'bg-gradient-to-br from-purple-500 to-pink-600': getAgentAvatarColor(agentName) === 'purple',
              'bg-gradient-to-br from-indigo-500 to-blue-600': getAgentAvatarColor(agentName) === 'indigo'
            }">
            <span class="text-white text-sm font-medium">
              {{ getAgentInitials(agentName) }}
            </span>
          </div>

          <!-- Agent Name and Type -->
          <div class="flex-1 min-w-0">
            <div class="text-sm font-medium truncate" [ngClass]="{
                'text-white': themeService.isDarkMode(),
                'text-[var(--text-dark)]': !themeService.isDarkMode()
              }">
              {{ formatAgentName(agentName) }}
            </div>
            <div class="text-xs truncate" [ngClass]="{
                'text-gray-400': themeService.isDarkMode(),
                'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
              }">
              AI Agent
            </div>
          </div>

          <!-- Agent Status Indicator -->
          <div class="flex items-center">
            <div class="w-2 h-2 rounded-full bg-green-500"></div>
          </div>
        </div>
      }

      <!-- Workspaces List (shown after agents) -->
      @for (workspace of workspaces; track workspace.id) {
        <div (click)="navigateToWorkspace(workspace)"
          class="chat-item flex items-center gap-3 px-3 py-2 rounded-lg cursor-pointer transition-all duration-150 mb-1"
          [ngClass]="{
            'hover:bg-[#3a3a45] hover:bg-opacity-60': themeService.isDarkMode(),
            'hover:bg-[var(--background-light-gray)]': !themeService.isDarkMode()
          }">

          <!-- Workspace Avatar -->
          <div class="chat-avatar w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0"
            [ngClass]="{
              'bg-gradient-to-br from-cyan-500 to-blue-600': getWorkspaceAvatarColor(workspace.title) === 'cyan',
              'bg-gradient-to-br from-emerald-500 to-green-600': getWorkspaceAvatarColor(workspace.title) === 'emerald',
              'bg-gradient-to-br from-amber-500 to-orange-600': getWorkspaceAvatarColor(workspace.title) === 'amber',
              'bg-gradient-to-br from-rose-500 to-pink-600': getWorkspaceAvatarColor(workspace.title) === 'rose',
              'bg-gradient-to-br from-violet-500 to-purple-600': getWorkspaceAvatarColor(workspace.title) === 'violet'
            }">
            <span class="text-white text-sm font-medium">
              {{ getWorkspaceInitials(workspace.title) }}
            </span>
          </div>

          <!-- Workspace Details -->
          <div class="flex-1 min-w-0">
            <div class="text-sm font-medium truncate" [ngClass]="{
                'text-white': themeService.isDarkMode(),
                'text-[var(--text-dark)]': !themeService.isDarkMode()
              }">
              {{ workspace.title }}
            </div>
            <div class="text-xs truncate" [ngClass]="{
                'text-gray-400': themeService.isDarkMode(),
                'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
              }">
              {{ getWorkspaceDisplayInfo(workspace) }}
            </div>
          </div>

          <!-- Workspace Status/Info -->
          <div class="flex items-center gap-1">
            <!-- Default Workspace Indicator -->
            <div *ngIf="isDefaultWorkspace(workspace)"
              class="w-2 h-2 rounded-full bg-blue-500"
              [title]="'Default Workspace'">
            </div>
            <!-- Member Count -->
            <span class="text-xs px-1.5 py-0.5 rounded-full" [ngClass]="{
                'bg-[#3a3a45] text-gray-300': themeService.isDarkMode(),
                'bg-[var(--background-light-gray)] text-[var(--text-medium-gray)]': !themeService.isDarkMode()
              }">
              {{ getWorkspaceMemberCount(workspace) }}
            </span>
          </div>
        </div>
      }
    </div>

    <!-- Empty State -->
    <div *ngIf="!isLoadingAgents && !isLoadingWorkspaces && agents.length === 0 && workspaces.length === 0"
      class="flex flex-col items-center justify-center py-12 text-center">
      <div class="w-12 h-12 rounded-full flex items-center justify-center mb-4" [ngClass]="{
          'bg-[#3a3a45]': themeService.isDarkMode(),
          'bg-[var(--background-light-gray)]': !themeService.isDarkMode()
        }">
        <i class="ri-chat-1-line text-xl" [ngClass]="{
            'text-gray-400': themeService.isDarkMode(),
            'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
          }"></i>
      </div>
      <p class="text-sm" [ngClass]="{
          'text-gray-400': themeService.isDarkMode(),
          'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
        }">
        No chats available
      </p>
    </div>

    <!-- Error States -->
    <div *ngIf="agentsError || workspacesError" class="px-3 py-2">
      <div *ngIf="agentsError" class="text-red-500 text-sm mb-2">
        {{ agentsError }}
      </div>
      <div *ngIf="workspacesError" class="text-red-500 text-sm">
        {{ workspacesError }}
      </div>
    </div>

  </div>
</div>
