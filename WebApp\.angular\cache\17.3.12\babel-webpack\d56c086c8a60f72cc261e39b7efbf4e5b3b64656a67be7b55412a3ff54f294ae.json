{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./document-details.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./document-details.component.css?ngResource\";\nimport { Component, Output, EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { NotesService } from '../services/notes.service';\nimport { DomSanitizer } from '@angular/platform-browser';\nlet DocumentDetailsComponent = class DocumentDetailsComponent {\n  constructor(route, notesService, sanitizer, router) {\n    this.route = route;\n    this.notesService = notesService;\n    this.sanitizer = sanitizer;\n    this.router = router;\n    this.note = null;\n    this.formattedContent = '';\n    this.favoriteToggled = new EventEmitter();\n    this.recentNoteUpdated = new EventEmitter();\n    this.isLoading = true;\n  }\n  ngOnInit() {\n    this.route.paramMap.subscribe(params => {\n      const id = params.get('id');\n      if (id) {\n        this.documentId = +id;\n        this.fetchDocumentDetails();\n      }\n    });\n  }\n  fetchDocumentDetails() {\n    this.notesService.getNoteById(this.documentId).subscribe({\n      next: doc => {\n        this.document = doc;\n        this.isLoading = false;\n        if (doc.content) {\n          this.formatContent(doc.content);\n        }\n        // Call updateRecentlyOpened after the note is loaded\n        this.notesService.updateRecentlyOpened(this.document.id).subscribe({\n          next: updatedNote => {\n            console.log('Recently opened updated:', updatedNote);\n            this.note = updatedNote;\n            // Emit event to trigger recent notes reload\n            this.recentNoteUpdated.emit();\n          },\n          error: error => {\n            console.error('Error updating recently opened:', error);\n          }\n        });\n      },\n      error: error => {\n        console.error('Error fetching document:', error);\n        this.isLoading = false;\n      }\n    });\n  }\n  toggleFavorite() {\n    if (this.note?.id) {\n      this.notesService.toggleFavorite(this.note.id).subscribe({\n        next: updatedNote => {\n          console.log(updatedNote);\n          if (this.note) {\n            this.note.isFavourite = updatedNote.isFavourite;\n            this.favoriteToggled.emit(this.note);\n          }\n        },\n        error: error => {\n          console.error('Error toggling favorite:', error);\n        }\n      });\n    }\n  }\n  formatContent(content) {\n    try {\n      const editorData = JSON.parse(content);\n      let html = '';\n      editorData.blocks.forEach(block => {\n        switch (block.type) {\n          case 'paragraph':\n            html += `<p class=\"mb-4\">${block.data.text}</p>`;\n            break;\n          case 'list':\n            html += '<ul class=\"list-disc pl-6 mb-4\">';\n            block.data.items?.forEach(item => {\n              html += `<li class=\"mb-2\">${item.content}</li>`;\n            });\n            html += '</ul>';\n            break;\n          case 'link':\n            // Format link block\n            html += `\n              <div class=\"mb-4 p-4 border rounded-lg border-gray-200 dark:border-gray-700\">\n                <a href=\"${block.data.link}\" target=\"_blank\" rel=\"noopener noreferrer\"\n                   class=\"flex items-start no-underline\">\n                  ${block.data.meta?.image?.url ? `\n                    <div class=\"flex-shrink-0 mr-4\">\n                      <img src=\"${block.data.meta.image.url}\" alt=\"\"\n                           class=\"w-16 h-16 object-cover rounded\">\n                    </div>\n                  ` : ''}\n                  <div class=\"flex-grow\">\n                    <h3 class=\"text-lg font-semibold text-blue-600 dark:text-blue-400 mb-1\">\n                      ${block.data.meta?.title || block.data.link}\n                    </h3>\n                    ${block.data.meta?.description ? `\n                      <p class=\"text-gray-600 dark:text-gray-300 text-sm line-clamp-2\">\n                        ${block.data.meta.description}\n                      </p>\n                    ` : ''}\n                    <span class=\"text-gray-500 dark:text-gray-400 text-xs\">\n                      ${block.data.link ? new URL(block.data.link).hostname : ''}\n                    </span>\n                  </div>\n                </a>\n              </div>\n            `;\n            break;\n          // Add more cases for other block types as needed\n        }\n      });\n      this.formattedContent = this.sanitizer.bypassSecurityTrustHtml(html);\n    } catch (e) {\n      console.error('Error parsing content:', e);\n      this.formattedContent = 'Error displaying content';\n    }\n  }\n  editDocument(id) {\n    this.router.navigate(['editor', id]);\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: ActivatedRoute\n    }, {\n      type: NotesService\n    }, {\n      type: DomSanitizer\n    }, {\n      type: Router\n    }];\n  }\n  static {\n    this.propDecorators = {\n      favoriteToggled: [{\n        type: Output\n      }],\n      recentNoteUpdated: [{\n        type: Output\n      }]\n    };\n  }\n};\nDocumentDetailsComponent = __decorate([Component({\n  selector: 'app-document-details',\n  standalone: true,\n  imports: [CommonModule],\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], DocumentDetailsComponent);\nexport { DocumentDetailsComponent };", "map": {"version": 3, "names": ["Component", "Output", "EventEmitter", "CommonModule", "ActivatedRoute", "Router", "NotesService", "Dom<PERSON><PERSON><PERSON>zer", "DocumentDetailsComponent", "constructor", "route", "notesService", "sanitizer", "router", "note", "formattedContent", "favoriteToggled", "recentNoteUpdated", "isLoading", "ngOnInit", "paramMap", "subscribe", "params", "id", "get", "documentId", "fetchDocumentDetails", "getNoteById", "next", "doc", "document", "content", "formatContent", "updateRecentlyOpened", "updatedNote", "console", "log", "emit", "error", "toggleFavorite", "isFavourite", "editorData", "JSON", "parse", "html", "blocks", "for<PERSON>ach", "block", "type", "data", "text", "items", "item", "link", "meta", "image", "url", "title", "description", "URL", "hostname", "bypassSecurityTrustHtml", "e", "editDocument", "navigate", "__decorate", "selector", "standalone", "imports", "template", "__NG_CLI_RESOURCE__0"], "sources": ["C:\\Users\\<USER>\\source\\ai-hub\\WebApp\\src\\app\\MyNotesProjects\\document-details\\document-details.component.ts"], "sourcesContent": ["import { Component, OnInit, Output, EventEmitter } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { NotesService, Note } from '../services/notes.service';\r\nimport { DomSanitizer, SafeHtml } from '@angular/platform-browser';\r\n\r\ninterface EditorBlock {\r\n  id: string;\r\n  type: string;\r\n  data: {\r\n    text?: string;\r\n    style?: string;\r\n    items?: Array<{\r\n      content: string;\r\n      items: any[];\r\n    }>;\r\n    link?: string;\r\n    meta?: {\r\n      image?: {\r\n        url?: string;\r\n      };\r\n      title?: string;\r\n      description?: string;\r\n    };\r\n  };\r\n}\r\n\r\ninterface EditorData {\r\n  time: number;\r\n  blocks: EditorBlock[];\r\n  version: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-document-details',\r\n  standalone: true,\r\n  imports: [CommonModule],\r\n  templateUrl: './document-details.component.html',\r\n  styleUrls: ['./document-details.component.css']\r\n})\r\nexport class DocumentDetailsComponent implements OnInit {\r\n  note: Note | null = null;\r\n  formattedContent: SafeHtml = '';\r\n  @Output() favoriteToggled = new EventEmitter<Note>();\r\n  @Output() recentNoteUpdated = new EventEmitter<any>();\r\n  documentId!: number;\r\n  document!: any; // Define a proper interface if available\r\n  isLoading: boolean = true;\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private notesService: NotesService,\r\n    private sanitizer: DomSanitizer,\r\n    private router: Router\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n    this.route.paramMap.subscribe(params => {\r\n      const id = params.get('id');\r\n      if (id) {\r\n        this.documentId = +id;\r\n        this.fetchDocumentDetails();\r\n      }\r\n    });\r\n  }\r\n\r\n  fetchDocumentDetails(): void {\r\n    this.notesService.getNoteById(this.documentId).subscribe({\r\n      next: (doc) => {\r\n        this.document = doc;\r\n        this.isLoading = false;\r\n        if (doc.content) {\r\n          this.formatContent(doc.content);\r\n        }\r\n\r\n        // Call updateRecentlyOpened after the note is loaded\r\n        this.notesService.updateRecentlyOpened(this.document.id).subscribe({\r\n          next: (updatedNote) => {\r\n            console.log('Recently opened updated:', updatedNote);\r\n            this.note = updatedNote;\r\n            // Emit event to trigger recent notes reload\r\n            this.recentNoteUpdated.emit();\r\n          },\r\n          error: (error) => {\r\n            console.error('Error updating recently opened:', error);\r\n          }\r\n        });\r\n      },\r\n      error: (error) => {\r\n        console.error('Error fetching document:', error);\r\n        this.isLoading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  toggleFavorite() {\r\n    if (this.note?.id) {\r\n      this.notesService.toggleFavorite(this.note.id).subscribe({\r\n        next: (updatedNote) => {\r\n          console.log(updatedNote);\r\n          if (this.note) {\r\n            this.note.isFavourite = updatedNote.isFavourite;\r\n            this.favoriteToggled.emit(this.note);\r\n          }\r\n        },\r\n        error: (error) => {\r\n          console.error('Error toggling favorite:', error);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  private formatContent(content: string) {\r\n    try {\r\n      const editorData: EditorData = JSON.parse(content);\r\n      let html = '';\r\n\r\n      editorData.blocks.forEach(block => {\r\n        switch (block.type) {\r\n          case 'paragraph':\r\n            html += `<p class=\"mb-4\">${block.data.text}</p>`;\r\n            break;\r\n          case 'list':\r\n            html += '<ul class=\"list-disc pl-6 mb-4\">';\r\n            block.data.items?.forEach(item => {\r\n              html += `<li class=\"mb-2\">${item.content}</li>`;\r\n            });\r\n            html += '</ul>';\r\n            break;\r\n          case 'link':\r\n            // Format link block\r\n            html += `\r\n              <div class=\"mb-4 p-4 border rounded-lg border-gray-200 dark:border-gray-700\">\r\n                <a href=\"${block.data.link}\" target=\"_blank\" rel=\"noopener noreferrer\"\r\n                   class=\"flex items-start no-underline\">\r\n                  ${block.data.meta?.image?.url ? `\r\n                    <div class=\"flex-shrink-0 mr-4\">\r\n                      <img src=\"${block.data.meta.image.url}\" alt=\"\"\r\n                           class=\"w-16 h-16 object-cover rounded\">\r\n                    </div>\r\n                  ` : ''}\r\n                  <div class=\"flex-grow\">\r\n                    <h3 class=\"text-lg font-semibold text-blue-600 dark:text-blue-400 mb-1\">\r\n                      ${block.data.meta?.title || block.data.link}\r\n                    </h3>\r\n                    ${block.data.meta?.description ? `\r\n                      <p class=\"text-gray-600 dark:text-gray-300 text-sm line-clamp-2\">\r\n                        ${block.data.meta.description}\r\n                      </p>\r\n                    ` : ''}\r\n                    <span class=\"text-gray-500 dark:text-gray-400 text-xs\">\r\n                      ${block.data.link ? new URL(block.data.link).hostname : ''}\r\n                    </span>\r\n                  </div>\r\n                </a>\r\n              </div>\r\n            `;\r\n            break;\r\n          // Add more cases for other block types as needed\r\n        }\r\n      });\r\n\r\n      this.formattedContent = this.sanitizer.bypassSecurityTrustHtml(html);\r\n    } catch (e) {\r\n      console.error('Error parsing content:', e);\r\n      this.formattedContent = 'Error displaying content';\r\n    }\r\n  }\r\n\r\n\r\n  editDocument(id: number) {\r\n    this.router.navigate(['editor', id]);\r\n  }\r\n\r\n}\r\n"], "mappings": ";;;AAAA,SAASA,SAAS,EAAUC,MAAM,EAAEC,YAAY,QAAQ,eAAe;AACvE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,EAAEC,MAAM,QAAQ,iBAAiB;AACxD,SAASC,YAAY,QAAc,2BAA2B;AAC9D,SAASC,YAAY,QAAkB,2BAA2B;AAoC3D,IAAMC,wBAAwB,GAA9B,MAAMA,wBAAwB;EASnCC,YACUC,KAAqB,EACrBC,YAA0B,EAC1BC,SAAuB,EACvBC,MAAc;IAHd,KAAAH,KAAK,GAALA,KAAK;IACL,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,MAAM,GAANA,MAAM;IAZhB,KAAAC,IAAI,GAAgB,IAAI;IACxB,KAAAC,gBAAgB,GAAa,EAAE;IACrB,KAAAC,eAAe,GAAG,IAAId,YAAY,EAAQ;IAC1C,KAAAe,iBAAiB,GAAG,IAAIf,YAAY,EAAO;IAGrD,KAAAgB,SAAS,GAAY,IAAI;EAOrB;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACT,KAAK,CAACU,QAAQ,CAACC,SAAS,CAACC,MAAM,IAAG;MACrC,MAAMC,EAAE,GAAGD,MAAM,CAACE,GAAG,CAAC,IAAI,CAAC;MAC3B,IAAID,EAAE,EAAE;QACN,IAAI,CAACE,UAAU,GAAG,CAACF,EAAE;QACrB,IAAI,CAACG,oBAAoB,EAAE;;IAE/B,CAAC,CAAC;EACJ;EAEAA,oBAAoBA,CAAA;IAClB,IAAI,CAACf,YAAY,CAACgB,WAAW,CAAC,IAAI,CAACF,UAAU,CAAC,CAACJ,SAAS,CAAC;MACvDO,IAAI,EAAGC,GAAG,IAAI;QACZ,IAAI,CAACC,QAAQ,GAAGD,GAAG;QACnB,IAAI,CAACX,SAAS,GAAG,KAAK;QACtB,IAAIW,GAAG,CAACE,OAAO,EAAE;UACf,IAAI,CAACC,aAAa,CAACH,GAAG,CAACE,OAAO,CAAC;;QAGjC;QACA,IAAI,CAACpB,YAAY,CAACsB,oBAAoB,CAAC,IAAI,CAACH,QAAQ,CAACP,EAAE,CAAC,CAACF,SAAS,CAAC;UACjEO,IAAI,EAAGM,WAAW,IAAI;YACpBC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEF,WAAW,CAAC;YACpD,IAAI,CAACpB,IAAI,GAAGoB,WAAW;YACvB;YACA,IAAI,CAACjB,iBAAiB,CAACoB,IAAI,EAAE;UAC/B,CAAC;UACDC,KAAK,EAAGA,KAAK,IAAI;YACfH,OAAO,CAACG,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;UACzD;SACD,CAAC;MACJ,CAAC;MACDA,KAAK,EAAGA,KAAK,IAAI;QACfH,OAAO,CAACG,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,IAAI,CAACpB,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EAEAqB,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACzB,IAAI,EAAES,EAAE,EAAE;MACjB,IAAI,CAACZ,YAAY,CAAC4B,cAAc,CAAC,IAAI,CAACzB,IAAI,CAACS,EAAE,CAAC,CAACF,SAAS,CAAC;QACvDO,IAAI,EAAGM,WAAW,IAAI;UACpBC,OAAO,CAACC,GAAG,CAACF,WAAW,CAAC;UACxB,IAAI,IAAI,CAACpB,IAAI,EAAE;YACb,IAAI,CAACA,IAAI,CAAC0B,WAAW,GAAGN,WAAW,CAACM,WAAW;YAC/C,IAAI,CAACxB,eAAe,CAACqB,IAAI,CAAC,IAAI,CAACvB,IAAI,CAAC;;QAExC,CAAC;QACDwB,KAAK,EAAGA,KAAK,IAAI;UACfH,OAAO,CAACG,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAClD;OACD,CAAC;;EAEN;EAEQN,aAAaA,CAACD,OAAe;IACnC,IAAI;MACF,MAAMU,UAAU,GAAeC,IAAI,CAACC,KAAK,CAACZ,OAAO,CAAC;MAClD,IAAIa,IAAI,GAAG,EAAE;MAEbH,UAAU,CAACI,MAAM,CAACC,OAAO,CAACC,KAAK,IAAG;QAChC,QAAQA,KAAK,CAACC,IAAI;UAChB,KAAK,WAAW;YACdJ,IAAI,IAAI,mBAAmBG,KAAK,CAACE,IAAI,CAACC,IAAI,MAAM;YAChD;UACF,KAAK,MAAM;YACTN,IAAI,IAAI,kCAAkC;YAC1CG,KAAK,CAACE,IAAI,CAACE,KAAK,EAAEL,OAAO,CAACM,IAAI,IAAG;cAC/BR,IAAI,IAAI,oBAAoBQ,IAAI,CAACrB,OAAO,OAAO;YACjD,CAAC,CAAC;YACFa,IAAI,IAAI,OAAO;YACf;UACF,KAAK,MAAM;YACT;YACAA,IAAI,IAAI;;2BAEOG,KAAK,CAACE,IAAI,CAACI,IAAI;;oBAEtBN,KAAK,CAACE,IAAI,CAACK,IAAI,EAAEC,KAAK,EAAEC,GAAG,GAAG;;kCAEhBT,KAAK,CAACE,IAAI,CAACK,IAAI,CAACC,KAAK,CAACC,GAAG;;;mBAGxC,GAAG,EAAE;;;wBAGAT,KAAK,CAACE,IAAI,CAACK,IAAI,EAAEG,KAAK,IAAIV,KAAK,CAACE,IAAI,CAACI,IAAI;;sBAE3CN,KAAK,CAACE,IAAI,CAACK,IAAI,EAAEI,WAAW,GAAG;;0BAE3BX,KAAK,CAACE,IAAI,CAACK,IAAI,CAACI,WAAW;;qBAEhC,GAAG,EAAE;;wBAEFX,KAAK,CAACE,IAAI,CAACI,IAAI,GAAG,IAAIM,GAAG,CAACZ,KAAK,CAACE,IAAI,CAACI,IAAI,CAAC,CAACO,QAAQ,GAAG,EAAE;;;;;aAKnE;YACD;UACF;;MAEJ,CAAC,CAAC;MAEF,IAAI,CAAC7C,gBAAgB,GAAG,IAAI,CAACH,SAAS,CAACiD,uBAAuB,CAACjB,IAAI,CAAC;KACrE,CAAC,OAAOkB,CAAC,EAAE;MACV3B,OAAO,CAACG,KAAK,CAAC,wBAAwB,EAAEwB,CAAC,CAAC;MAC1C,IAAI,CAAC/C,gBAAgB,GAAG,0BAA0B;;EAEtD;EAGAgD,YAAYA,CAACxC,EAAU;IACrB,IAAI,CAACV,MAAM,CAACmD,QAAQ,CAAC,CAAC,QAAQ,EAAEzC,EAAE,CAAC,CAAC;EACtC;;;;;;;;;;;;;;;cAjICtB;MAAM;;cACNA;MAAM;;;;AAJIO,wBAAwB,GAAAyD,UAAA,EAPpCjE,SAAS,CAAC;EACTkE,QAAQ,EAAE,sBAAsB;EAChCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACjE,YAAY,CAAC;EACvBkE,QAAA,EAAAC,oBAAgD;;CAEjD,CAAC,C,EACW9D,wBAAwB,CAsIpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}