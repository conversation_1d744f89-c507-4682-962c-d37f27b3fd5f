{"ast": null, "code": "import { NgTemplateOutlet } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Optional, NgModule } from '@angular/core';\nimport * as i4 from 'ng-zorro-antd/core/outlet';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport * as i2 from 'ng-zorro-antd/dropdown';\nimport { NzDropDownModule } from 'ng-zorro-antd/dropdown';\nimport * as i3 from 'ng-zorro-antd/icon';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport { __decorate } from 'tslib';\nimport { Router, ActivatedRoute, NavigationEnd, PRIMARY_OUTLET } from '@angular/router';\nimport { Subject } from 'rxjs';\nimport { takeUntil, filter, startWith } from 'rxjs/operators';\nimport { PREFIX } from 'ng-zorro-antd/core/logger';\nimport { InputBoolean } from 'ng-zorro-antd/core/util';\nimport * as i1 from '@angular/cdk/bidi';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst _c0 = [\"*\"];\nfunction NzBreadCrumbItemComponent_Conditional_0_ng_template_1_Template(rf, ctx) {}\nfunction NzBreadCrumbItemComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 1);\n    i0.ɵɵtemplate(1, NzBreadCrumbItemComponent_Conditional_0_ng_template_1_Template, 0, 0, \"ng-template\", 2);\n    i0.ɵɵelement(2, \"span\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    const noMenuTpl_r2 = i0.ɵɵreference(4);\n    i0.ɵɵproperty(\"nzDropdownMenu\", ctx_r0.nzOverlay);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", noMenuTpl_r2);\n  }\n}\nfunction NzBreadCrumbItemComponent_Conditional_1_ng_template_0_Template(rf, ctx) {}\nfunction NzBreadCrumbItemComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzBreadCrumbItemComponent_Conditional_1_ng_template_0_Template, 0, 0, \"ng-template\", 2);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const noMenuTpl_r2 = i0.ɵɵreference(4);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", noMenuTpl_r2);\n  }\n}\nfunction NzBreadCrumbItemComponent_Conditional_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.nzBreadCrumbComponent.nzSeparator, \" \");\n  }\n}\nfunction NzBreadCrumbItemComponent_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nz-breadcrumb-separator\");\n    i0.ɵɵtemplate(1, NzBreadCrumbItemComponent_Conditional_2_ng_container_1_Template, 2, 1, \"ng-container\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx_r0.nzBreadCrumbComponent.nzSeparator);\n  }\n}\nfunction NzBreadCrumbItemComponent_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 5);\n    i0.ɵɵprojection(1);\n    i0.ɵɵelementEnd();\n  }\n}\nconst _forTrack0 = ($index, $item) => $item.url;\nfunction NzBreadCrumbComponent_Conditional_1_For_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nz-breadcrumb-item\")(1, \"a\", 0);\n    i0.ɵɵlistener(\"click\", function NzBreadCrumbComponent_Conditional_1_For_1_Template_a_click_1_listener($event) {\n      const breadcrumb_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.navigate(breadcrumb_r2.url, $event));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const breadcrumb_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"href\", breadcrumb_r2.url, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(breadcrumb_r2.label);\n  }\n}\nfunction NzBreadCrumbComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵrepeaterCreate(0, NzBreadCrumbComponent_Conditional_1_For_1_Template, 3, 2, \"nz-breadcrumb-item\", null, _forTrack0);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵrepeater(ctx_r2.breadcrumbs);\n  }\n}\nclass NzBreadCrumbSeparatorComponent {\n  static {\n    this.ɵfac = function NzBreadCrumbSeparatorComponent_Factory(t) {\n      return new (t || NzBreadCrumbSeparatorComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzBreadCrumbSeparatorComponent,\n      selectors: [[\"nz-breadcrumb-separator\"]],\n      hostAttrs: [1, \"ant-breadcrumb-separator\"],\n      exportAs: [\"nzBreadcrumbSeparator\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function NzBreadCrumbSeparatorComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzBreadCrumbSeparatorComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-breadcrumb-separator',\n      exportAs: 'nzBreadcrumbSeparator',\n      standalone: true,\n      template: `<ng-content></ng-content>`,\n      host: {\n        class: 'ant-breadcrumb-separator'\n      }\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/**\n * https://angular.io/errors/NG3003\n * An intermediate interface for {@link NzBreadCrumbComponent} & {@link NzBreadCrumbItemComponent}\n */\nclass NzBreadcrumb {}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzBreadCrumbItemComponent {\n  constructor(nzBreadCrumbComponent) {\n    this.nzBreadCrumbComponent = nzBreadCrumbComponent;\n  }\n  static {\n    this.ɵfac = function NzBreadCrumbItemComponent_Factory(t) {\n      return new (t || NzBreadCrumbItemComponent)(i0.ɵɵdirectiveInject(NzBreadcrumb));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzBreadCrumbItemComponent,\n      selectors: [[\"nz-breadcrumb-item\"]],\n      inputs: {\n        nzOverlay: \"nzOverlay\"\n      },\n      exportAs: [\"nzBreadcrumbItem\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 5,\n      vars: 2,\n      consts: [[\"noMenuTpl\", \"\"], [\"nz-dropdown\", \"\", 1, \"ant-breadcrumb-overlay-link\", 3, \"nzDropdownMenu\"], [3, \"ngTemplateOutlet\"], [\"nz-icon\", \"\", \"nzType\", \"down\"], [4, \"nzStringTemplateOutlet\"], [1, \"ant-breadcrumb-link\"]],\n      template: function NzBreadCrumbItemComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, NzBreadCrumbItemComponent_Conditional_0_Template, 3, 2, \"span\", 1)(1, NzBreadCrumbItemComponent_Conditional_1_Template, 1, 1)(2, NzBreadCrumbItemComponent_Conditional_2_Template, 2, 1, \"nz-breadcrumb-separator\")(3, NzBreadCrumbItemComponent_ng_template_3_Template, 2, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(0, !!ctx.nzOverlay ? 0 : 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(2, ctx.nzBreadCrumbComponent.nzSeparator ? 2 : -1);\n        }\n      },\n      dependencies: [NgTemplateOutlet, NzBreadCrumbSeparatorComponent, NzDropDownModule, i2.NzDropDownDirective, NzIconModule, i3.NzIconDirective, NzOutletModule, i4.NzStringTemplateOutletDirective],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzBreadCrumbItemComponent, [{\n    type: Component,\n    args: [{\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      selector: 'nz-breadcrumb-item',\n      exportAs: 'nzBreadcrumbItem',\n      preserveWhitespaces: false,\n      standalone: true,\n      imports: [NgTemplateOutlet, NzBreadCrumbSeparatorComponent, NzDropDownModule, NzIconModule, NzOutletModule],\n      template: `\n    @if (!!nzOverlay) {\n      <span class=\"ant-breadcrumb-overlay-link\" nz-dropdown [nzDropdownMenu]=\"nzOverlay\">\n        <ng-template [ngTemplateOutlet]=\"noMenuTpl\"></ng-template>\n        <span nz-icon nzType=\"down\"></span>\n      </span>\n    } @else {\n      <ng-template [ngTemplateOutlet]=\"noMenuTpl\" />\n    }\n\n    @if (nzBreadCrumbComponent.nzSeparator) {\n      <nz-breadcrumb-separator>\n        <ng-container *nzStringTemplateOutlet=\"nzBreadCrumbComponent.nzSeparator\">\n          {{ nzBreadCrumbComponent.nzSeparator }}\n        </ng-container>\n      </nz-breadcrumb-separator>\n    }\n\n    <ng-template #noMenuTpl>\n      <span class=\"ant-breadcrumb-link\">\n        <ng-content />\n      </span>\n    </ng-template>\n  `\n    }]\n  }], () => [{\n    type: NzBreadcrumb\n  }], {\n    nzOverlay: [{\n      type: Input\n    }]\n  });\n})();\nclass NzBreadCrumbComponent {\n  constructor(injector, cdr, elementRef, renderer, directionality) {\n    this.injector = injector;\n    this.cdr = cdr;\n    this.elementRef = elementRef;\n    this.renderer = renderer;\n    this.directionality = directionality;\n    this.nzAutoGenerate = false;\n    this.nzSeparator = '/';\n    this.nzRouteLabel = 'breadcrumb';\n    this.nzRouteLabelFn = label => label;\n    this.breadcrumbs = [];\n    this.dir = 'ltr';\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {\n    if (this.nzAutoGenerate) {\n      this.registerRouterChange();\n    }\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.prepareComponentForRtl();\n      this.cdr.detectChanges();\n    });\n    this.dir = this.directionality.value;\n    this.prepareComponentForRtl();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  navigate(url, e) {\n    e.preventDefault();\n    this.injector.get(Router).navigateByUrl(url);\n  }\n  registerRouterChange() {\n    try {\n      const router = this.injector.get(Router);\n      const activatedRoute = this.injector.get(ActivatedRoute);\n      router.events.pipe(filter(e => e instanceof NavigationEnd), takeUntil(this.destroy$), startWith(true) // trigger initial render\n      ).subscribe(() => {\n        this.breadcrumbs = this.getBreadcrumbs(activatedRoute.root);\n        this.cdr.markForCheck();\n      });\n    } catch (e) {\n      throw new Error(`${PREFIX} You should import RouterModule if you want to use 'NzAutoGenerate'.`);\n    }\n  }\n  getBreadcrumbs(route, url = '', breadcrumbs = []) {\n    const children = route.children;\n    // If there's no sub root, then stop the recurse and returns the generated breadcrumbs.\n    if (children.length === 0) {\n      return breadcrumbs;\n    }\n    for (const child of children) {\n      if (child.outlet === PRIMARY_OUTLET) {\n        // Only parse components in primary router-outlet (in another word, router-outlet without a specific name).\n        // Parse this layer and generate a breadcrumb item.\n        const routeUrl = child.snapshot.url.map(segment => segment.path).filter(path => path).join('/');\n        // Do not change nextUrl if routeUrl is falsy. This happens when it's a route lazy loading other modules.\n        const nextUrl = routeUrl ? `${url}/${routeUrl}` : url;\n        const breadcrumbLabel = this.nzRouteLabelFn(child.snapshot.data[this.nzRouteLabel]);\n        // If have data, go to generate a breadcrumb for it.\n        if (routeUrl && breadcrumbLabel) {\n          const breadcrumb = {\n            label: breadcrumbLabel,\n            params: child.snapshot.params,\n            url: nextUrl\n          };\n          breadcrumbs.push(breadcrumb);\n        }\n        return this.getBreadcrumbs(child, nextUrl, breadcrumbs);\n      }\n    }\n    return breadcrumbs;\n  }\n  prepareComponentForRtl() {\n    if (this.dir === 'rtl') {\n      this.renderer.addClass(this.elementRef.nativeElement, 'ant-breadcrumb-rtl');\n    } else {\n      this.renderer.removeClass(this.elementRef.nativeElement, 'ant-breadcrumb-rtl');\n    }\n  }\n  static {\n    this.ɵfac = function NzBreadCrumbComponent_Factory(t) {\n      return new (t || NzBreadCrumbComponent)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i1.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzBreadCrumbComponent,\n      selectors: [[\"nz-breadcrumb\"]],\n      hostAttrs: [1, \"ant-breadcrumb\"],\n      inputs: {\n        nzAutoGenerate: \"nzAutoGenerate\",\n        nzSeparator: \"nzSeparator\",\n        nzRouteLabel: \"nzRouteLabel\",\n        nzRouteLabelFn: \"nzRouteLabelFn\"\n      },\n      exportAs: [\"nzBreadcrumb\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: NzBreadcrumb,\n        useExisting: NzBreadCrumbComponent\n      }]), i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 2,\n      vars: 1,\n      consts: [[3, \"click\"]],\n      template: function NzBreadCrumbComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n          i0.ɵɵtemplate(1, NzBreadCrumbComponent_Conditional_1_Template, 2, 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(1, ctx.nzAutoGenerate && ctx.breadcrumbs.length ? 1 : -1);\n        }\n      },\n      dependencies: [NzBreadCrumbItemComponent],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([InputBoolean()], NzBreadCrumbComponent.prototype, \"nzAutoGenerate\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzBreadCrumbComponent, [{\n    type: Component,\n    args: [{\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      selector: 'nz-breadcrumb',\n      exportAs: 'nzBreadcrumb',\n      preserveWhitespaces: false,\n      providers: [{\n        provide: NzBreadcrumb,\n        useExisting: NzBreadCrumbComponent\n      }],\n      standalone: true,\n      imports: [NzBreadCrumbItemComponent],\n      template: `\n    <ng-content />\n    @if (nzAutoGenerate && breadcrumbs.length) {\n      @for (breadcrumb of breadcrumbs; track breadcrumb.url) {\n        <nz-breadcrumb-item>\n          <a [attr.href]=\"breadcrumb.url\" (click)=\"navigate(breadcrumb.url, $event)\">{{ breadcrumb.label }}</a>\n        </nz-breadcrumb-item>\n      }\n    }\n  `,\n      host: {\n        class: 'ant-breadcrumb'\n      }\n    }]\n  }], () => [{\n    type: i0.Injector\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i1.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    nzAutoGenerate: [{\n      type: Input\n    }],\n    nzSeparator: [{\n      type: Input\n    }],\n    nzRouteLabel: [{\n      type: Input\n    }],\n    nzRouteLabelFn: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzBreadCrumbModule {\n  static {\n    this.ɵfac = function NzBreadCrumbModule_Factory(t) {\n      return new (t || NzBreadCrumbModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzBreadCrumbModule,\n      imports: [NzBreadCrumbComponent, NzBreadCrumbItemComponent, NzBreadCrumbSeparatorComponent],\n      exports: [NzBreadCrumbComponent, NzBreadCrumbItemComponent, NzBreadCrumbSeparatorComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [NzBreadCrumbComponent, NzBreadCrumbItemComponent]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzBreadCrumbModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzBreadCrumbComponent, NzBreadCrumbItemComponent, NzBreadCrumbSeparatorComponent],\n      exports: [NzBreadCrumbComponent, NzBreadCrumbItemComponent, NzBreadCrumbSeparatorComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzBreadCrumbComponent, NzBreadCrumbItemComponent, NzBreadCrumbModule, NzBreadCrumbSeparatorComponent };", "map": {"version": 3, "names": ["NgTemplateOutlet", "i0", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Input", "Optional", "NgModule", "i4", "NzOutletModule", "i2", "NzDropDownModule", "i3", "NzIconModule", "__decorate", "Router", "ActivatedRoute", "NavigationEnd", "PRIMARY_OUTLET", "Subject", "takeUntil", "filter", "startWith", "PREFIX", "InputBoolean", "i1", "_c0", "NzBreadCrumbItemComponent_Conditional_0_ng_template_1_Template", "rf", "ctx", "NzBreadCrumbItemComponent_Conditional_0_Template", "ɵɵelementStart", "ɵɵtemplate", "ɵɵelement", "ɵɵelementEnd", "ctx_r0", "ɵɵnextContext", "noMenuTpl_r2", "ɵɵreference", "ɵɵproperty", "nzOverlay", "ɵɵadvance", "NzBreadCrumbItemComponent_Conditional_1_ng_template_0_Template", "NzBreadCrumbItemComponent_Conditional_1_Template", "NzBreadCrumbItemComponent_Conditional_2_ng_container_1_Template", "ɵɵelementContainerStart", "ɵɵtext", "ɵɵelementContainerEnd", "ɵɵtextInterpolate1", "nzBreadCrumbComponent", "nzSeparator", "NzBreadCrumbItemComponent_Conditional_2_Template", "NzBreadCrumbItemComponent_ng_template_3_Template", "ɵɵprojection", "_forTrack0", "$index", "$item", "url", "NzBreadCrumbComponent_Conditional_1_For_1_Template", "_r1", "ɵɵgetCurrentView", "ɵɵlistener", "NzBreadCrumbComponent_Conditional_1_For_1_Template_a_click_1_listener", "$event", "breadcrumb_r2", "ɵɵrestoreView", "$implicit", "ctx_r2", "ɵɵresetView", "navigate", "ɵɵattribute", "ɵɵsanitizeUrl", "ɵɵtextInterpolate", "label", "NzBreadCrumbComponent_Conditional_1_Template", "ɵɵrepeaterCreate", "ɵɵrepeater", "breadcrumbs", "NzBreadCrumbSeparatorComponent", "ɵfac", "NzBreadCrumbSeparatorComponent_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "exportAs", "standalone", "features", "ɵɵStandaloneFeature", "ngContentSelectors", "decls", "vars", "template", "NzBreadCrumbSeparatorComponent_Template", "ɵɵprojectionDef", "encapsulation", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "host", "class", "NzBreadcrumb", "NzBreadCrumbItemComponent", "constructor", "NzBreadCrumbItemComponent_Factory", "ɵɵdirectiveInject", "inputs", "consts", "NzBreadCrumbItemComponent_Template", "ɵɵtemplateRefExtractor", "ɵɵconditional", "dependencies", "NzDropDownDirective", "NzIconDirective", "NzStringTemplateOutletDirective", "changeDetection", "OnPush", "None", "preserveWhitespaces", "imports", "NzBreadCrumbComponent", "injector", "cdr", "elementRef", "renderer", "directionality", "nzAutoGenerate", "nzRouteLabel", "nzRouteLabelFn", "dir", "destroy$", "ngOnInit", "registerRouterChange", "change", "pipe", "subscribe", "direction", "prepareComponentForRtl", "detectChanges", "value", "ngOnDestroy", "next", "complete", "e", "preventDefault", "get", "navigateByUrl", "router", "activatedRoute", "events", "getBreadcrumbs", "root", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Error", "route", "children", "length", "child", "outlet", "routeUrl", "snapshot", "map", "segment", "path", "join", "nextUrl", "breadcrumbLabel", "data", "breadcrumb", "params", "push", "addClass", "nativeElement", "removeClass", "NzBreadCrumbComponent_Factory", "Injector", "ChangeDetectorRef", "ElementRef", "Renderer2", "Directionality", "ɵɵProvidersFeature", "provide", "useExisting", "NzBreadCrumbComponent_Template", "prototype", "providers", "decorators", "NzBreadCrumbModule", "NzBreadCrumbModule_Factory", "ɵmod", "ɵɵdefineNgModule", "exports", "ɵinj", "ɵɵdefineInjector"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-breadcrumb.mjs"], "sourcesContent": ["import { NgTemplateOutlet } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Optional, NgModule } from '@angular/core';\nimport * as i4 from 'ng-zorro-antd/core/outlet';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport * as i2 from 'ng-zorro-antd/dropdown';\nimport { NzDropDownModule } from 'ng-zorro-antd/dropdown';\nimport * as i3 from 'ng-zorro-antd/icon';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport { __decorate } from 'tslib';\nimport { Router, ActivatedRoute, NavigationEnd, PRIMARY_OUTLET } from '@angular/router';\nimport { Subject } from 'rxjs';\nimport { takeUntil, filter, startWith } from 'rxjs/operators';\nimport { PREFIX } from 'ng-zorro-antd/core/logger';\nimport { InputBoolean } from 'ng-zorro-antd/core/util';\nimport * as i1 from '@angular/cdk/bidi';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzBreadCrumbSeparatorComponent {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzBreadCrumbSeparatorComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzBreadCrumbSeparatorComponent, isStandalone: true, selector: \"nz-breadcrumb-separator\", host: { classAttribute: \"ant-breadcrumb-separator\" }, exportAs: [\"nzBreadcrumbSeparator\"], ngImport: i0, template: `<ng-content></ng-content>`, isInline: true }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzBreadCrumbSeparatorComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'nz-breadcrumb-separator',\n                    exportAs: 'nzBreadcrumbSeparator',\n                    standalone: true,\n                    template: `<ng-content></ng-content>`,\n                    host: {\n                        class: 'ant-breadcrumb-separator'\n                    }\n                }]\n        }] });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/**\n * https://angular.io/errors/NG3003\n * An intermediate interface for {@link NzBreadCrumbComponent} & {@link NzBreadCrumbItemComponent}\n */\nclass NzBreadcrumb {\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzBreadCrumbItemComponent {\n    constructor(nzBreadCrumbComponent) {\n        this.nzBreadCrumbComponent = nzBreadCrumbComponent;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzBreadCrumbItemComponent, deps: [{ token: NzBreadcrumb }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"17.3.8\", type: NzBreadCrumbItemComponent, isStandalone: true, selector: \"nz-breadcrumb-item\", inputs: { nzOverlay: \"nzOverlay\" }, exportAs: [\"nzBreadcrumbItem\"], ngImport: i0, template: `\n    @if (!!nzOverlay) {\n      <span class=\"ant-breadcrumb-overlay-link\" nz-dropdown [nzDropdownMenu]=\"nzOverlay\">\n        <ng-template [ngTemplateOutlet]=\"noMenuTpl\"></ng-template>\n        <span nz-icon nzType=\"down\"></span>\n      </span>\n    } @else {\n      <ng-template [ngTemplateOutlet]=\"noMenuTpl\" />\n    }\n\n    @if (nzBreadCrumbComponent.nzSeparator) {\n      <nz-breadcrumb-separator>\n        <ng-container *nzStringTemplateOutlet=\"nzBreadCrumbComponent.nzSeparator\">\n          {{ nzBreadCrumbComponent.nzSeparator }}\n        </ng-container>\n      </nz-breadcrumb-separator>\n    }\n\n    <ng-template #noMenuTpl>\n      <span class=\"ant-breadcrumb-link\">\n        <ng-content />\n      </span>\n    </ng-template>\n  `, isInline: true, dependencies: [{ kind: \"directive\", type: NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"component\", type: NzBreadCrumbSeparatorComponent, selector: \"nz-breadcrumb-separator\", exportAs: [\"nzBreadcrumbSeparator\"] }, { kind: \"ngmodule\", type: NzDropDownModule }, { kind: \"directive\", type: i2.NzDropDownDirective, selector: \"[nz-dropdown]\", inputs: [\"nzDropdownMenu\", \"nzTrigger\", \"nzMatchWidthElement\", \"nzBackdrop\", \"nzClickHide\", \"nzDisabled\", \"nzVisible\", \"nzOverlayClassName\", \"nzOverlayStyle\", \"nzPlacement\"], outputs: [\"nzVisibleChange\"], exportAs: [\"nzDropdown\"] }, { kind: \"ngmodule\", type: NzIconModule }, { kind: \"directive\", type: i3.NzIconDirective, selector: \"[nz-icon]\", inputs: [\"nzSpin\", \"nzRotate\", \"nzType\", \"nzTheme\", \"nzTwotoneColor\", \"nzIconfont\"], exportAs: [\"nzIcon\"] }, { kind: \"ngmodule\", type: NzOutletModule }, { kind: \"directive\", type: i4.NzStringTemplateOutletDirective, selector: \"[nzStringTemplateOutlet]\", inputs: [\"nzStringTemplateOutletContext\", \"nzStringTemplateOutlet\"], exportAs: [\"nzStringTemplateOutlet\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzBreadCrumbItemComponent, decorators: [{\n            type: Component,\n            args: [{\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    encapsulation: ViewEncapsulation.None,\n                    selector: 'nz-breadcrumb-item',\n                    exportAs: 'nzBreadcrumbItem',\n                    preserveWhitespaces: false,\n                    standalone: true,\n                    imports: [NgTemplateOutlet, NzBreadCrumbSeparatorComponent, NzDropDownModule, NzIconModule, NzOutletModule],\n                    template: `\n    @if (!!nzOverlay) {\n      <span class=\"ant-breadcrumb-overlay-link\" nz-dropdown [nzDropdownMenu]=\"nzOverlay\">\n        <ng-template [ngTemplateOutlet]=\"noMenuTpl\"></ng-template>\n        <span nz-icon nzType=\"down\"></span>\n      </span>\n    } @else {\n      <ng-template [ngTemplateOutlet]=\"noMenuTpl\" />\n    }\n\n    @if (nzBreadCrumbComponent.nzSeparator) {\n      <nz-breadcrumb-separator>\n        <ng-container *nzStringTemplateOutlet=\"nzBreadCrumbComponent.nzSeparator\">\n          {{ nzBreadCrumbComponent.nzSeparator }}\n        </ng-container>\n      </nz-breadcrumb-separator>\n    }\n\n    <ng-template #noMenuTpl>\n      <span class=\"ant-breadcrumb-link\">\n        <ng-content />\n      </span>\n    </ng-template>\n  `\n                }]\n        }], ctorParameters: () => [{ type: NzBreadcrumb }], propDecorators: { nzOverlay: [{\n                type: Input\n            }] } });\n\nclass NzBreadCrumbComponent {\n    constructor(injector, cdr, elementRef, renderer, directionality) {\n        this.injector = injector;\n        this.cdr = cdr;\n        this.elementRef = elementRef;\n        this.renderer = renderer;\n        this.directionality = directionality;\n        this.nzAutoGenerate = false;\n        this.nzSeparator = '/';\n        this.nzRouteLabel = 'breadcrumb';\n        this.nzRouteLabelFn = label => label;\n        this.breadcrumbs = [];\n        this.dir = 'ltr';\n        this.destroy$ = new Subject();\n    }\n    ngOnInit() {\n        if (this.nzAutoGenerate) {\n            this.registerRouterChange();\n        }\n        this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe((direction) => {\n            this.dir = direction;\n            this.prepareComponentForRtl();\n            this.cdr.detectChanges();\n        });\n        this.dir = this.directionality.value;\n        this.prepareComponentForRtl();\n    }\n    ngOnDestroy() {\n        this.destroy$.next();\n        this.destroy$.complete();\n    }\n    navigate(url, e) {\n        e.preventDefault();\n        this.injector.get(Router).navigateByUrl(url);\n    }\n    registerRouterChange() {\n        try {\n            const router = this.injector.get(Router);\n            const activatedRoute = this.injector.get(ActivatedRoute);\n            router.events\n                .pipe(filter(e => e instanceof NavigationEnd), takeUntil(this.destroy$), startWith(true) // trigger initial render\n            )\n                .subscribe(() => {\n                this.breadcrumbs = this.getBreadcrumbs(activatedRoute.root);\n                this.cdr.markForCheck();\n            });\n        }\n        catch (e) {\n            throw new Error(`${PREFIX} You should import RouterModule if you want to use 'NzAutoGenerate'.`);\n        }\n    }\n    getBreadcrumbs(route, url = '', breadcrumbs = []) {\n        const children = route.children;\n        // If there's no sub root, then stop the recurse and returns the generated breadcrumbs.\n        if (children.length === 0) {\n            return breadcrumbs;\n        }\n        for (const child of children) {\n            if (child.outlet === PRIMARY_OUTLET) {\n                // Only parse components in primary router-outlet (in another word, router-outlet without a specific name).\n                // Parse this layer and generate a breadcrumb item.\n                const routeUrl = child.snapshot.url\n                    .map(segment => segment.path)\n                    .filter(path => path)\n                    .join('/');\n                // Do not change nextUrl if routeUrl is falsy. This happens when it's a route lazy loading other modules.\n                const nextUrl = routeUrl ? `${url}/${routeUrl}` : url;\n                const breadcrumbLabel = this.nzRouteLabelFn(child.snapshot.data[this.nzRouteLabel]);\n                // If have data, go to generate a breadcrumb for it.\n                if (routeUrl && breadcrumbLabel) {\n                    const breadcrumb = {\n                        label: breadcrumbLabel,\n                        params: child.snapshot.params,\n                        url: nextUrl\n                    };\n                    breadcrumbs.push(breadcrumb);\n                }\n                return this.getBreadcrumbs(child, nextUrl, breadcrumbs);\n            }\n        }\n        return breadcrumbs;\n    }\n    prepareComponentForRtl() {\n        if (this.dir === 'rtl') {\n            this.renderer.addClass(this.elementRef.nativeElement, 'ant-breadcrumb-rtl');\n        }\n        else {\n            this.renderer.removeClass(this.elementRef.nativeElement, 'ant-breadcrumb-rtl');\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzBreadCrumbComponent, deps: [{ token: i0.Injector }, { token: i0.ChangeDetectorRef }, { token: i0.ElementRef }, { token: i0.Renderer2 }, { token: i1.Directionality, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"17.3.8\", type: NzBreadCrumbComponent, isStandalone: true, selector: \"nz-breadcrumb\", inputs: { nzAutoGenerate: \"nzAutoGenerate\", nzSeparator: \"nzSeparator\", nzRouteLabel: \"nzRouteLabel\", nzRouteLabelFn: \"nzRouteLabelFn\" }, host: { classAttribute: \"ant-breadcrumb\" }, providers: [{ provide: NzBreadcrumb, useExisting: NzBreadCrumbComponent }], exportAs: [\"nzBreadcrumb\"], ngImport: i0, template: `\n    <ng-content />\n    @if (nzAutoGenerate && breadcrumbs.length) {\n      @for (breadcrumb of breadcrumbs; track breadcrumb.url) {\n        <nz-breadcrumb-item>\n          <a [attr.href]=\"breadcrumb.url\" (click)=\"navigate(breadcrumb.url, $event)\">{{ breadcrumb.label }}</a>\n        </nz-breadcrumb-item>\n      }\n    }\n  `, isInline: true, dependencies: [{ kind: \"component\", type: NzBreadCrumbItemComponent, selector: \"nz-breadcrumb-item\", inputs: [\"nzOverlay\"], exportAs: [\"nzBreadcrumbItem\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\n__decorate([\n    InputBoolean()\n], NzBreadCrumbComponent.prototype, \"nzAutoGenerate\", void 0);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzBreadCrumbComponent, decorators: [{\n            type: Component,\n            args: [{\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    encapsulation: ViewEncapsulation.None,\n                    selector: 'nz-breadcrumb',\n                    exportAs: 'nzBreadcrumb',\n                    preserveWhitespaces: false,\n                    providers: [{ provide: NzBreadcrumb, useExisting: NzBreadCrumbComponent }],\n                    standalone: true,\n                    imports: [NzBreadCrumbItemComponent],\n                    template: `\n    <ng-content />\n    @if (nzAutoGenerate && breadcrumbs.length) {\n      @for (breadcrumb of breadcrumbs; track breadcrumb.url) {\n        <nz-breadcrumb-item>\n          <a [attr.href]=\"breadcrumb.url\" (click)=\"navigate(breadcrumb.url, $event)\">{{ breadcrumb.label }}</a>\n        </nz-breadcrumb-item>\n      }\n    }\n  `,\n                    host: {\n                        class: 'ant-breadcrumb'\n                    }\n                }]\n        }], ctorParameters: () => [{ type: i0.Injector }, { type: i0.ChangeDetectorRef }, { type: i0.ElementRef }, { type: i0.Renderer2 }, { type: i1.Directionality, decorators: [{\n                    type: Optional\n                }] }], propDecorators: { nzAutoGenerate: [{\n                type: Input\n            }], nzSeparator: [{\n                type: Input\n            }], nzRouteLabel: [{\n                type: Input\n            }], nzRouteLabelFn: [{\n                type: Input\n            }] } });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzBreadCrumbModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzBreadCrumbModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.3.8\", ngImport: i0, type: NzBreadCrumbModule, imports: [NzBreadCrumbComponent, NzBreadCrumbItemComponent, NzBreadCrumbSeparatorComponent], exports: [NzBreadCrumbComponent, NzBreadCrumbItemComponent, NzBreadCrumbSeparatorComponent] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzBreadCrumbModule, imports: [NzBreadCrumbComponent, NzBreadCrumbItemComponent] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzBreadCrumbModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [NzBreadCrumbComponent, NzBreadCrumbItemComponent, NzBreadCrumbSeparatorComponent],\n                    exports: [NzBreadCrumbComponent, NzBreadCrumbItemComponent, NzBreadCrumbSeparatorComponent]\n                }]\n        }] });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzBreadCrumbComponent, NzBreadCrumbItemComponent, NzBreadCrumbModule, NzBreadCrumbSeparatorComponent };\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,iBAAiB;AAClD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,eAAe;AAChH,OAAO,KAAKC,EAAE,MAAM,2BAA2B;AAC/C,SAASC,cAAc,QAAQ,2BAA2B;AAC1D,OAAO,KAAKC,EAAE,MAAM,wBAAwB;AAC5C,SAASC,gBAAgB,QAAQ,wBAAwB;AACzD,OAAO,KAAKC,EAAE,MAAM,oBAAoB;AACxC,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,UAAU,QAAQ,OAAO;AAClC,SAASC,MAAM,EAAEC,cAAc,EAAEC,aAAa,EAAEC,cAAc,QAAQ,iBAAiB;AACvF,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,EAAEC,MAAM,EAAEC,SAAS,QAAQ,gBAAgB;AAC7D,SAASC,MAAM,QAAQ,2BAA2B;AAClD,SAASC,YAAY,QAAQ,yBAAyB;AACtD,OAAO,KAAKC,EAAE,MAAM,mBAAmB;;AAEvC;AACA;AACA;AACA;AAHA,MAAAC,GAAA;AAAA,SAAAC,+DAAAC,EAAA,EAAAC,GAAA;AAAA,SAAAC,iDAAAF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAKoG3B,EAAE,CAAA8B,cAAA,aAsCd,CAAC;IAtCW9B,EAAE,CAAA+B,UAAA,IAAAL,8DAAA,wBAuCnD,CAAC;IAvCgD1B,EAAE,CAAAgC,SAAA,aAwC5D,CAAC;IAxCyDhC,EAAE,CAAAiC,YAAA,CAyC1F,CAAC;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAO,MAAA,GAzCuFlC,EAAE,CAAAmC,aAAA;IAAA,MAAAC,YAAA,GAAFpC,EAAE,CAAAqC,WAAA;IAAFrC,EAAE,CAAAsC,UAAA,mBAAAJ,MAAA,CAAAK,SAsCf,CAAC;IAtCYvC,EAAE,CAAAwC,SAAA,CAuCpD,CAAC;IAvCiDxC,EAAE,CAAAsC,UAAA,qBAAAF,YAuCpD,CAAC;EAAA;AAAA;AAAA,SAAAK,+DAAAd,EAAA,EAAAC,GAAA;AAAA,SAAAc,iDAAAf,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAvCiD3B,EAAE,CAAA+B,UAAA,IAAAU,8DAAA,wBA2CnD,CAAC;EAAA;EAAA,IAAAd,EAAA;IA3CgD3B,EAAE,CAAAmC,aAAA;IAAA,MAAAC,YAAA,GAAFpC,EAAE,CAAAqC,WAAA;IAAFrC,EAAE,CAAAsC,UAAA,qBAAAF,YA2CtD,CAAC;EAAA;AAAA;AAAA,SAAAO,gEAAAhB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA3CmD3B,EAAE,CAAA4C,uBAAA,EAgDrB,CAAC;IAhDkB5C,EAAE,CAAA6C,MAAA,EAkD/F,CAAC;IAlD4F7C,EAAE,CAAA8C,qBAAA;EAAA;EAAA,IAAAnB,EAAA;IAAA,MAAAO,MAAA,GAAFlC,EAAE,CAAAmC,aAAA;IAAFnC,EAAE,CAAAwC,SAAA,CAkD/F,CAAC;IAlD4FxC,EAAE,CAAA+C,kBAAA,MAAAb,MAAA,CAAAc,qBAAA,CAAAC,WAAA,KAkD/F,CAAC;EAAA;AAAA;AAAA,SAAAC,iDAAAvB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlD4F3B,EAAE,CAAA8B,cAAA,6BA+CxE,CAAC;IA/CqE9B,EAAE,CAAA+B,UAAA,IAAAY,+DAAA,yBAgDrB,CAAC;IAhDkB3C,EAAE,CAAAiC,YAAA,CAmDvE,CAAC;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAO,MAAA,GAnDoElC,EAAE,CAAAmC,aAAA;IAAFnC,EAAE,CAAAwC,SAAA,CAgDvB,CAAC;IAhDoBxC,EAAE,CAAAsC,UAAA,2BAAAJ,MAAA,CAAAc,qBAAA,CAAAC,WAgDvB,CAAC;EAAA;AAAA;AAAA,SAAAE,iDAAAxB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAhDoB3B,EAAE,CAAA8B,cAAA,aAuD/D,CAAC;IAvD4D9B,EAAE,CAAAoD,YAAA,EAwDjF,CAAC;IAxD8EpD,EAAE,CAAAiC,YAAA,CAyD1F,CAAC;EAAA;AAAA;AAAA,MAAAoB,UAAA,GAAAA,CAAAC,MAAA,EAAAC,KAAA,KAAAA,KAAA,CAAAC,GAAA;AAAA,SAAAC,mDAAA9B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA+B,GAAA,GAzDuF1D,EAAE,CAAA2D,gBAAA;IAAF3D,EAAE,CAAA8B,cAAA,wBAmM3E,CAAC,UACwD,CAAC;IApMe9B,EAAE,CAAA4D,UAAA,mBAAAC,sEAAAC,MAAA;MAAA,MAAAC,aAAA,GAAF/D,EAAE,CAAAgE,aAAA,CAAAN,GAAA,EAAAO,SAAA;MAAA,MAAAC,MAAA,GAAFlE,EAAE,CAAAmC,aAAA;MAAA,OAAFnC,EAAE,CAAAmE,WAAA,CAoMnDD,MAAA,CAAAE,QAAA,CAAAL,aAAA,CAAAP,GAAA,EAAAM,MAA+B,CAAC;IAAA,EAAC;IApMgB9D,EAAE,CAAA6C,MAAA,EAoMI,CAAC;IApMP7C,EAAE,CAAAiC,YAAA,CAoMQ,CAAC,CACnF,CAAC;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAoC,aAAA,GAAAnC,GAAA,CAAAqC,SAAA;IArMuEjE,EAAE,CAAAwC,SAAA,CAoM9D,CAAC;IApM2DxC,EAAE,CAAAqE,WAAA,SAAAN,aAAA,CAAAP,GAAA,EAAFxD,EAAE,CAAAsE,aAAA;IAAFtE,EAAE,CAAAwC,SAAA,CAoMI,CAAC;IApMPxC,EAAE,CAAAuE,iBAAA,CAAAR,aAAA,CAAAS,KAoMI,CAAC;EAAA;AAAA;AAAA,SAAAC,6CAAA9C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IApMP3B,EAAE,CAAA0E,gBAAA,IAAAjB,kDAAA,oCAAAJ,UAsMhG,CAAC;EAAA;EAAA,IAAA1B,EAAA;IAAA,MAAAuC,MAAA,GAtM6FlE,EAAE,CAAAmC,aAAA;IAAFnC,EAAE,CAAA2E,UAAA,CAAAT,MAAA,CAAAU,WAsMhG,CAAC;EAAA;AAAA;AAvMP,MAAMC,8BAA8B,CAAC;EACjC;IAAS,IAAI,CAACC,IAAI,YAAAC,uCAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFH,8BAA8B;IAAA,CAAmD;EAAE;EAC7L;IAAS,IAAI,CAACI,IAAI,kBAD8EjF,EAAE,CAAAkF,iBAAA;MAAAC,IAAA,EACJN,8BAA8B;MAAAO,SAAA;MAAAC,SAAA;MAAAC,QAAA;MAAAC,UAAA;MAAAC,QAAA,GAD5BxF,EAAE,CAAAyF,mBAAA;MAAAC,kBAAA,EAAAjE,GAAA;MAAAkE,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,wCAAAnE,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF3B,EAAE,CAAA+F,eAAA;UAAF/F,EAAE,CAAAoD,YAAA,EACiO,CAAC;QAAA;MAAA;MAAA4C,aAAA;IAAA,EAAoB;EAAE;AAC9V;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoGjG,EAAE,CAAAkG,iBAAA,CAGXrB,8BAA8B,EAAc,CAAC;IAC5HM,IAAI,EAAElF,SAAS;IACfkG,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,yBAAyB;MACnCd,QAAQ,EAAE,uBAAuB;MACjCC,UAAU,EAAE,IAAI;MAChBM,QAAQ,EAAG,2BAA0B;MACrCQ,IAAI,EAAE;QACFC,KAAK,EAAE;MACX;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,CAAC;;AAGnB;AACA;AACA;AACA;AACA,MAAMC,yBAAyB,CAAC;EAC5BC,WAAWA,CAACzD,qBAAqB,EAAE;IAC/B,IAAI,CAACA,qBAAqB,GAAGA,qBAAqB;EACtD;EACA;IAAS,IAAI,CAAC8B,IAAI,YAAA4B,kCAAA1B,CAAA;MAAA,YAAAA,CAAA,IAAwFwB,yBAAyB,EAnCnCxG,EAAE,CAAA2G,iBAAA,CAmCmDJ,YAAY;IAAA,CAA4C;EAAE;EAC/M;IAAS,IAAI,CAACtB,IAAI,kBApC8EjF,EAAE,CAAAkF,iBAAA;MAAAC,IAAA,EAoCJqB,yBAAyB;MAAApB,SAAA;MAAAwB,MAAA;QAAArE,SAAA;MAAA;MAAA+C,QAAA;MAAAC,UAAA;MAAAC,QAAA,GApCvBxF,EAAE,CAAAyF,mBAAA;MAAAC,kBAAA,EAAAjE,GAAA;MAAAkE,KAAA;MAAAC,IAAA;MAAAiB,MAAA;MAAAhB,QAAA,WAAAiB,mCAAAnF,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF3B,EAAE,CAAA+F,eAAA;UAAF/F,EAAE,CAAA+B,UAAA,IAAAF,gDAAA,iBAqChF,CAAC,IAAAa,gDAAA,MAKX,CAAC,IAAAQ,gDAAA,iCAI+B,CAAC,IAAAC,gDAAA,gCA9CuDnD,EAAE,CAAA+G,sBAsD3E,CAAC;QAAA;QAAA,IAAApF,EAAA;UAtDwE3B,EAAE,CAAAgH,aAAA,MAAApF,GAAA,CAAAW,SAAA,QA4ClG,CAAC;UA5C+FvC,EAAE,CAAAwC,SAAA,EAoDlG,CAAC;UApD+FxC,EAAE,CAAAgH,aAAA,IAAApF,GAAA,CAAAoB,qBAAA,CAAAC,WAAA,SAoDlG,CAAC;QAAA;MAAA;MAAAgE,YAAA,GAO0DlH,gBAAgB,EAAoJ8E,8BAA8B,EAAwGnE,gBAAgB,EAA+BD,EAAE,CAACyG,mBAAmB,EAAgStG,YAAY,EAA+BD,EAAE,CAACwG,eAAe,EAAgK3G,cAAc,EAA+BD,EAAE,CAAC6G,+BAA+B;MAAApB,aAAA;MAAAqB,eAAA;IAAA,EAAqP;EAAE;AACjvC;AACA;EAAA,QAAApB,SAAA,oBAAAA,SAAA,KA7DoGjG,EAAE,CAAAkG,iBAAA,CA6DXM,yBAAyB,EAAc,CAAC;IACvHrB,IAAI,EAAElF,SAAS;IACfkG,IAAI,EAAE,CAAC;MACCkB,eAAe,EAAEnH,uBAAuB,CAACoH,MAAM;MAC/CtB,aAAa,EAAE7F,iBAAiB,CAACoH,IAAI;MACrCnB,QAAQ,EAAE,oBAAoB;MAC9Bd,QAAQ,EAAE,kBAAkB;MAC5BkC,mBAAmB,EAAE,KAAK;MAC1BjC,UAAU,EAAE,IAAI;MAChBkC,OAAO,EAAE,CAAC1H,gBAAgB,EAAE8E,8BAA8B,EAAEnE,gBAAgB,EAAEE,YAAY,EAAEJ,cAAc,CAAC;MAC3GqF,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACgB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEV,IAAI,EAAEoB;EAAa,CAAC,CAAC,EAAkB;IAAEhE,SAAS,EAAE,CAAC;MAC1E4C,IAAI,EAAE/E;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMsH,qBAAqB,CAAC;EACxBjB,WAAWA,CAACkB,QAAQ,EAAEC,GAAG,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,cAAc,EAAE;IAC7D,IAAI,CAACJ,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B,IAAI,CAAC/E,WAAW,GAAG,GAAG;IACtB,IAAI,CAACgF,YAAY,GAAG,YAAY;IAChC,IAAI,CAACC,cAAc,GAAG1D,KAAK,IAAIA,KAAK;IACpC,IAAI,CAACI,WAAW,GAAG,EAAE;IACrB,IAAI,CAACuD,GAAG,GAAG,KAAK;IAChB,IAAI,CAACC,QAAQ,GAAG,IAAIlH,OAAO,CAAC,CAAC;EACjC;EACAmH,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACL,cAAc,EAAE;MACrB,IAAI,CAACM,oBAAoB,CAAC,CAAC;IAC/B;IACA,IAAI,CAACP,cAAc,CAACQ,MAAM,EAAEC,IAAI,CAACrH,SAAS,CAAC,IAAI,CAACiH,QAAQ,CAAC,CAAC,CAACK,SAAS,CAAEC,SAAS,IAAK;MAChF,IAAI,CAACP,GAAG,GAAGO,SAAS;MACpB,IAAI,CAACC,sBAAsB,CAAC,CAAC;MAC7B,IAAI,CAACf,GAAG,CAACgB,aAAa,CAAC,CAAC;IAC5B,CAAC,CAAC;IACF,IAAI,CAACT,GAAG,GAAG,IAAI,CAACJ,cAAc,CAACc,KAAK;IACpC,IAAI,CAACF,sBAAsB,CAAC,CAAC;EACjC;EACAG,WAAWA,CAAA,EAAG;IACV,IAAI,CAACV,QAAQ,CAACW,IAAI,CAAC,CAAC;IACpB,IAAI,CAACX,QAAQ,CAACY,QAAQ,CAAC,CAAC;EAC5B;EACA5E,QAAQA,CAACZ,GAAG,EAAEyF,CAAC,EAAE;IACbA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAACvB,QAAQ,CAACwB,GAAG,CAACrI,MAAM,CAAC,CAACsI,aAAa,CAAC5F,GAAG,CAAC;EAChD;EACA8E,oBAAoBA,CAAA,EAAG;IACnB,IAAI;MACA,MAAMe,MAAM,GAAG,IAAI,CAAC1B,QAAQ,CAACwB,GAAG,CAACrI,MAAM,CAAC;MACxC,MAAMwI,cAAc,GAAG,IAAI,CAAC3B,QAAQ,CAACwB,GAAG,CAACpI,cAAc,CAAC;MACxDsI,MAAM,CAACE,MAAM,CACRf,IAAI,CAACpH,MAAM,CAAC6H,CAAC,IAAIA,CAAC,YAAYjI,aAAa,CAAC,EAAEG,SAAS,CAAC,IAAI,CAACiH,QAAQ,CAAC,EAAE/G,SAAS,CAAC,IAAI,CAAC,CAAC;MAC7F,CAAC,CACIoH,SAAS,CAAC,MAAM;QACjB,IAAI,CAAC7D,WAAW,GAAG,IAAI,CAAC4E,cAAc,CAACF,cAAc,CAACG,IAAI,CAAC;QAC3D,IAAI,CAAC7B,GAAG,CAAC8B,YAAY,CAAC,CAAC;MAC3B,CAAC,CAAC;IACN,CAAC,CACD,OAAOT,CAAC,EAAE;MACN,MAAM,IAAIU,KAAK,CAAE,GAAErI,MAAO,sEAAqE,CAAC;IACpG;EACJ;EACAkI,cAAcA,CAACI,KAAK,EAAEpG,GAAG,GAAG,EAAE,EAAEoB,WAAW,GAAG,EAAE,EAAE;IAC9C,MAAMiF,QAAQ,GAAGD,KAAK,CAACC,QAAQ;IAC/B;IACA,IAAIA,QAAQ,CAACC,MAAM,KAAK,CAAC,EAAE;MACvB,OAAOlF,WAAW;IACtB;IACA,KAAK,MAAMmF,KAAK,IAAIF,QAAQ,EAAE;MAC1B,IAAIE,KAAK,CAACC,MAAM,KAAK/I,cAAc,EAAE;QACjC;QACA;QACA,MAAMgJ,QAAQ,GAAGF,KAAK,CAACG,QAAQ,CAAC1G,GAAG,CAC9B2G,GAAG,CAACC,OAAO,IAAIA,OAAO,CAACC,IAAI,CAAC,CAC5BjJ,MAAM,CAACiJ,IAAI,IAAIA,IAAI,CAAC,CACpBC,IAAI,CAAC,GAAG,CAAC;QACd;QACA,MAAMC,OAAO,GAAGN,QAAQ,GAAI,GAAEzG,GAAI,IAAGyG,QAAS,EAAC,GAAGzG,GAAG;QACrD,MAAMgH,eAAe,GAAG,IAAI,CAACtC,cAAc,CAAC6B,KAAK,CAACG,QAAQ,CAACO,IAAI,CAAC,IAAI,CAACxC,YAAY,CAAC,CAAC;QACnF;QACA,IAAIgC,QAAQ,IAAIO,eAAe,EAAE;UAC7B,MAAME,UAAU,GAAG;YACflG,KAAK,EAAEgG,eAAe;YACtBG,MAAM,EAAEZ,KAAK,CAACG,QAAQ,CAACS,MAAM;YAC7BnH,GAAG,EAAE+G;UACT,CAAC;UACD3F,WAAW,CAACgG,IAAI,CAACF,UAAU,CAAC;QAChC;QACA,OAAO,IAAI,CAAClB,cAAc,CAACO,KAAK,EAAEQ,OAAO,EAAE3F,WAAW,CAAC;MAC3D;IACJ;IACA,OAAOA,WAAW;EACtB;EACA+D,sBAAsBA,CAAA,EAAG;IACrB,IAAI,IAAI,CAACR,GAAG,KAAK,KAAK,EAAE;MACpB,IAAI,CAACL,QAAQ,CAAC+C,QAAQ,CAAC,IAAI,CAAChD,UAAU,CAACiD,aAAa,EAAE,oBAAoB,CAAC;IAC/E,CAAC,MACI;MACD,IAAI,CAAChD,QAAQ,CAACiD,WAAW,CAAC,IAAI,CAAClD,UAAU,CAACiD,aAAa,EAAE,oBAAoB,CAAC;IAClF;EACJ;EACA;IAAS,IAAI,CAAChG,IAAI,YAAAkG,8BAAAhG,CAAA;MAAA,YAAAA,CAAA,IAAwF0C,qBAAqB,EA9L/B1H,EAAE,CAAA2G,iBAAA,CA8L+C3G,EAAE,CAACiL,QAAQ,GA9L5DjL,EAAE,CAAA2G,iBAAA,CA8LuE3G,EAAE,CAACkL,iBAAiB,GA9L7FlL,EAAE,CAAA2G,iBAAA,CA8LwG3G,EAAE,CAACmL,UAAU,GA9LvHnL,EAAE,CAAA2G,iBAAA,CA8LkI3G,EAAE,CAACoL,SAAS,GA9LhJpL,EAAE,CAAA2G,iBAAA,CA8L2JnF,EAAE,CAAC6J,cAAc;IAAA,CAA4D;EAAE;EAC5U;IAAS,IAAI,CAACpG,IAAI,kBA/L8EjF,EAAE,CAAAkF,iBAAA;MAAAC,IAAA,EA+LJuC,qBAAqB;MAAAtC,SAAA;MAAAC,SAAA;MAAAuB,MAAA;QAAAoB,cAAA;QAAA/E,WAAA;QAAAgF,YAAA;QAAAC,cAAA;MAAA;MAAA5C,QAAA;MAAAC,UAAA;MAAAC,QAAA,GA/LnBxF,EAAE,CAAAsL,kBAAA,CA+LmQ,CAAC;QAAEC,OAAO,EAAEhF,YAAY;QAAEiF,WAAW,EAAE9D;MAAsB,CAAC,CAAC,GA/LpU1H,EAAE,CAAAyF,mBAAA;MAAAC,kBAAA,EAAAjE,GAAA;MAAAkE,KAAA;MAAAC,IAAA;MAAAiB,MAAA;MAAAhB,QAAA,WAAA4F,+BAAA9J,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF3B,EAAE,CAAA+F,eAAA;UAAF/F,EAAE,CAAAoD,YAAA,EAgMrF,CAAC;UAhMkFpD,EAAE,CAAA+B,UAAA,IAAA0C,4CAAA,MAiMvD,CAAC;QAAA;QAAA,IAAA9C,EAAA;UAjMoD3B,EAAE,CAAAwC,SAAA,CAuMlG,CAAC;UAvM+FxC,EAAE,CAAAgH,aAAA,IAAApF,GAAA,CAAAoG,cAAA,IAAApG,GAAA,CAAAgD,WAAA,CAAAkF,MAAA,SAuMlG,CAAC;QAAA;MAAA;MAAA7C,YAAA,GAC0DT,yBAAyB;MAAAR,aAAA;MAAAqB,eAAA;IAAA,EAA2L;EAAE;AACrR;AACAxG,UAAU,CAAC,CACPU,YAAY,CAAC,CAAC,CACjB,EAAEmG,qBAAqB,CAACgE,SAAS,EAAE,gBAAgB,EAAE,KAAK,CAAC,CAAC;AAC7D;EAAA,QAAAzF,SAAA,oBAAAA,SAAA,KA7MoGjG,EAAE,CAAAkG,iBAAA,CA6MXwB,qBAAqB,EAAc,CAAC;IACnHvC,IAAI,EAAElF,SAAS;IACfkG,IAAI,EAAE,CAAC;MACCkB,eAAe,EAAEnH,uBAAuB,CAACoH,MAAM;MAC/CtB,aAAa,EAAE7F,iBAAiB,CAACoH,IAAI;MACrCnB,QAAQ,EAAE,eAAe;MACzBd,QAAQ,EAAE,cAAc;MACxBkC,mBAAmB,EAAE,KAAK;MAC1BmE,SAAS,EAAE,CAAC;QAAEJ,OAAO,EAAEhF,YAAY;QAAEiF,WAAW,EAAE9D;MAAsB,CAAC,CAAC;MAC1EnC,UAAU,EAAE,IAAI;MAChBkC,OAAO,EAAE,CAACjB,yBAAyB,CAAC;MACpCX,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiBQ,IAAI,EAAE;QACFC,KAAK,EAAE;MACX;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEnB,IAAI,EAAEnF,EAAE,CAACiL;EAAS,CAAC,EAAE;IAAE9F,IAAI,EAAEnF,EAAE,CAACkL;EAAkB,CAAC,EAAE;IAAE/F,IAAI,EAAEnF,EAAE,CAACmL;EAAW,CAAC,EAAE;IAAEhG,IAAI,EAAEnF,EAAE,CAACoL;EAAU,CAAC,EAAE;IAAEjG,IAAI,EAAE3D,EAAE,CAAC6J,cAAc;IAAEO,UAAU,EAAE,CAAC;MAC/JzG,IAAI,EAAE9E;IACV,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAE2H,cAAc,EAAE,CAAC;MAC1C7C,IAAI,EAAE/E;IACV,CAAC,CAAC;IAAE6C,WAAW,EAAE,CAAC;MACdkC,IAAI,EAAE/E;IACV,CAAC,CAAC;IAAE6H,YAAY,EAAE,CAAC;MACf9C,IAAI,EAAE/E;IACV,CAAC,CAAC;IAAE8H,cAAc,EAAE,CAAC;MACjB/C,IAAI,EAAE/E;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAMyL,kBAAkB,CAAC;EACrB;IAAS,IAAI,CAAC/G,IAAI,YAAAgH,2BAAA9G,CAAA;MAAA,YAAAA,CAAA,IAAwF6G,kBAAkB;IAAA,CAAkD;EAAE;EAChL;IAAS,IAAI,CAACE,IAAI,kBAxP8E/L,EAAE,CAAAgM,gBAAA;MAAA7G,IAAA,EAwPS0G,kBAAkB;MAAApE,OAAA,GAAYC,qBAAqB,EAAElB,yBAAyB,EAAE3B,8BAA8B;MAAAoH,OAAA,GAAavE,qBAAqB,EAAElB,yBAAyB,EAAE3B,8BAA8B;IAAA,EAAI;EAAE;EAC5T;IAAS,IAAI,CAACqH,IAAI,kBAzP8ElM,EAAE,CAAAmM,gBAAA;MAAA1E,OAAA,GAyPuCC,qBAAqB,EAAElB,yBAAyB;IAAA,EAAI;EAAE;AACnM;AACA;EAAA,QAAAP,SAAA,oBAAAA,SAAA,KA3PoGjG,EAAE,CAAAkG,iBAAA,CA2PX2F,kBAAkB,EAAc,CAAC;IAChH1G,IAAI,EAAE7E,QAAQ;IACd6F,IAAI,EAAE,CAAC;MACCsB,OAAO,EAAE,CAACC,qBAAqB,EAAElB,yBAAyB,EAAE3B,8BAA8B,CAAC;MAC3FoH,OAAO,EAAE,CAACvE,qBAAqB,EAAElB,yBAAyB,EAAE3B,8BAA8B;IAC9F,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAAS6C,qBAAqB,EAAElB,yBAAyB,EAAEqF,kBAAkB,EAAEhH,8BAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}