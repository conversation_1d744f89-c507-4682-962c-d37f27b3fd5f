{"ast": null, "code": "import { TemplateRef } from '@angular/core';\nimport { coerceBooleanProperty, _isNumberValue, coerceCssPixelValue } from '@angular/cdk/coercion';\nimport { warn } from 'ng-zorro-antd/core/logger';\nimport { Subject, isObservable, from, of } from 'rxjs';\nimport { take } from 'rxjs/operators';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction toArray(value) {\n  let ret;\n  if (value == null) {\n    ret = [];\n  } else if (!Array.isArray(value)) {\n    ret = [value];\n  } else {\n    ret = value;\n  }\n  return ret;\n}\nfunction arraysEqual(array1, array2) {\n  if (!array1 || !array2 || array1.length !== array2.length) {\n    return false;\n  }\n  const len = array1.length;\n  for (let i = 0; i < len; i++) {\n    if (array1[i] !== array2[i]) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction shallowCopyArray(source) {\n  return source.slice();\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction isNotNil(value) {\n  return typeof value !== 'undefined' && value !== null;\n}\nfunction isNil(value) {\n  return typeof value === 'undefined' || value === null;\n}\n/**\n * Examine if two objects are shallowly equaled.\n */\nfunction shallowEqual(objA, objB) {\n  if (objA === objB) {\n    return true;\n  }\n  if (typeof objA !== 'object' || !objA || typeof objB !== 'object' || !objB) {\n    return false;\n  }\n  const keysA = Object.keys(objA);\n  const keysB = Object.keys(objB);\n  if (keysA.length !== keysB.length) {\n    return false;\n  }\n  const bHasOwnProperty = Object.prototype.hasOwnProperty.bind(objB);\n  // eslint-disable-next-line @typescript-eslint/prefer-for-of\n  for (let idx = 0; idx < keysA.length; idx++) {\n    const key = keysA[idx];\n    if (!bHasOwnProperty(key)) {\n      return false;\n    }\n    if (objA[key] !== objB[key]) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction isNonEmptyString(value) {\n  return typeof value === 'string' && value !== '';\n}\nfunction isTemplateRef(value) {\n  return value instanceof TemplateRef;\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction toBoolean(value) {\n  return coerceBooleanProperty(value);\n}\nfunction toNumber(value, fallbackValue = 0) {\n  return _isNumberValue(value) ? Number(value) : fallbackValue;\n}\nfunction toCssPixel(value) {\n  return coerceCssPixelValue(value);\n}\n// eslint-disable  no-invalid-this\n/**\n * Get the function-property type's value\n */\nfunction valueFunctionProp(prop, ...args) {\n  return typeof prop === 'function' ? prop(...args) : prop;\n}\nfunction propDecoratorFactory(name, fallback) {\n  function propDecorator(target, propName, originalDescriptor) {\n    const privatePropName = `$$__zorroPropDecorator__${propName}`;\n    if (Object.prototype.hasOwnProperty.call(target, privatePropName)) {\n      warn(`The prop \"${privatePropName}\" is already exist, it will be overrided by ${name} decorator.`);\n    }\n    Object.defineProperty(target, privatePropName, {\n      configurable: true,\n      writable: true\n    });\n    return {\n      get() {\n        return originalDescriptor && originalDescriptor.get ? originalDescriptor.get.bind(this)() : this[privatePropName];\n      },\n      set(value) {\n        if (originalDescriptor && originalDescriptor.set) {\n          originalDescriptor.set.bind(this)(fallback(value));\n        }\n        this[privatePropName] = fallback(value);\n      }\n    };\n  }\n  return propDecorator;\n}\n/**\n * Input decorator that handle a prop to do get/set automatically with toBoolean\n *\n * Why not using @InputBoolean alone without @Input? AOT needs @Input to be visible\n *\n * @howToUse\n * ```\n * @Input() @InputBoolean() visible: boolean = false;\n *\n * // Act as below:\n * // @Input()\n * // get visible() { return this.__visible; }\n * // set visible(value) { this.__visible = value; }\n * // __visible = false;\n * ```\n */\nfunction InputBoolean() {\n  return propDecoratorFactory('InputBoolean', toBoolean);\n}\nfunction InputCssPixel() {\n  return propDecoratorFactory('InputCssPixel', toCssPixel);\n}\nfunction InputNumber(fallbackValue) {\n  return propDecoratorFactory('InputNumber', value => toNumber(value, fallbackValue));\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/**\n * Silent an event by stopping and preventing it.\n */\nfunction silentEvent(e) {\n  e.stopPropagation();\n  e.preventDefault();\n}\nfunction getElementOffset(elem) {\n  if (!elem.getClientRects().length) {\n    return {\n      top: 0,\n      left: 0\n    };\n  }\n  const rect = elem.getBoundingClientRect();\n  const win = elem.ownerDocument.defaultView;\n  return {\n    top: rect.top + win.pageYOffset,\n    left: rect.left + win.pageXOffset\n  };\n}\n/**\n * Investigate if an event is a `TouchEvent`.\n */\nfunction isTouchEvent(event) {\n  return event.type.startsWith('touch');\n}\nfunction getEventPosition(event) {\n  return isTouchEvent(event) ? event.touches[0] || event.changedTouches[0] : event;\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction getRegExp(prefix) {\n  const prefixArray = Array.isArray(prefix) ? prefix : [prefix];\n  let prefixToken = prefixArray.join('').replace(/(\\$|\\^)/g, '\\\\$1');\n  if (prefixArray.length > 1) {\n    prefixToken = `[${prefixToken}]`;\n  }\n  return new RegExp(`(\\\\s|^)(${prefixToken})[^\\\\s]*`, 'g');\n}\nfunction getMentions(value, prefix = '@') {\n  if (typeof value !== 'string') {\n    return [];\n  }\n  const regex = getRegExp(prefix);\n  const mentions = value.match(regex);\n  return mentions !== null ? mentions.map(e => e.trim()) : [];\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/**\n * Much like lodash.\n */\nfunction padStart(toPad, length, element) {\n  if (toPad.length > length) {\n    return toPad;\n  }\n  const joined = `${getRepeatedElement(length, element)}${toPad}`;\n  return joined.slice(joined.length - length, joined.length);\n}\nfunction padEnd(toPad, length, element) {\n  const joined = `${toPad}${getRepeatedElement(length, element)}`;\n  return joined.slice(0, length);\n}\nfunction getRepeatedElement(length, element) {\n  return Array(length).fill(element).join('');\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction isPromise(obj) {\n  return !!obj && typeof obj.then === 'function' && typeof obj.catch === 'function';\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction getPercent(min, max, value) {\n  return (value - min) / (max - min) * 100;\n}\nfunction getPrecision(num) {\n  const numStr = num.toString();\n  const dotIndex = numStr.indexOf('.');\n  return dotIndex >= 0 ? numStr.length - dotIndex - 1 : 0;\n}\nfunction ensureNumberInRange(num, min, max) {\n  if (isNaN(num) || num < min) {\n    return min;\n  } else if (num > max) {\n    return max;\n  } else {\n    return num;\n  }\n}\nfunction isNumberFinite(value) {\n  return typeof value === 'number' && isFinite(value);\n}\nfunction toDecimal(value, decimal) {\n  return Math.round(value * Math.pow(10, decimal)) / Math.pow(10, decimal);\n}\nfunction sum(input, initial = 0) {\n  return input.reduce((previous, current) => previous + current, initial);\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction scrollIntoView(node) {\n  const nodeAsAny = node;\n  if (nodeAsAny.scrollIntoViewIfNeeded) {\n    /* eslint-disable-next-line @typescript-eslint/dot-notation */\n    nodeAsAny.scrollIntoViewIfNeeded(false);\n    return;\n  }\n  if (node.scrollIntoView) {\n    node.scrollIntoView(false);\n    return;\n  }\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n// from https://github.com/component/textarea-caret-position\n// We'll copy the properties below into the mirror div.\n// Note that some browsers, such as Firefox, do not concatenate properties\n// into their shorthand (e.g. padding-top, padding-bottom etc. -> padding),\n// so we have to list every single property explicitly.\nconst properties = ['direction',\n// RTL support\n'boxSizing', 'width',\n// on Chrome and IE, exclude the scrollbar, so the mirror div wraps exactly as the textarea does\n'height', 'overflowX', 'overflowY',\n// copy the scrollbar for IE\n'borderTopWidth', 'borderRightWidth', 'borderBottomWidth', 'borderLeftWidth', 'borderStyle', 'paddingTop', 'paddingRight', 'paddingBottom', 'paddingLeft',\n// https://developer.mozilla.org/en-US/docs/Web/CSS/font\n'fontStyle', 'fontVariant', 'fontWeight', 'fontStretch', 'fontSize', 'fontSizeAdjust', 'lineHeight', 'fontFamily', 'textAlign', 'textTransform', 'textIndent', 'textDecoration',\n// might not make a difference, but better be safe\n'letterSpacing', 'wordSpacing', 'tabSize', 'MozTabSize'];\nconst isBrowser = typeof window !== 'undefined';\nconst isFirefox = isBrowser && window.mozInnerScreenX != null;\nconst _parseInt = str => parseInt(str, 10);\nfunction getCaretCoordinates(element, position, options) {\n  if (!isBrowser) {\n    throw new Error('textarea-caret-position#getCaretCoordinates should only be called in a browser');\n  }\n  const debug = options && options.debug || false;\n  if (debug) {\n    const el = document.querySelector('#input-textarea-caret-position-mirror-div');\n    if (el) {\n      el.parentNode.removeChild(el);\n    }\n  }\n  // The mirror div will replicate the textarea's style\n  const div = document.createElement('div');\n  div.id = 'input-textarea-caret-position-mirror-div';\n  document.body.appendChild(div);\n  const style = div.style;\n  const computed = window.getComputedStyle ? window.getComputedStyle(element) : element.currentStyle; // currentStyle for IE < 9\n  const isInput = element.nodeName === 'INPUT';\n  // Default textarea styles\n  style.whiteSpace = 'pre-wrap';\n  if (!isInput) {\n    style.wordWrap = 'break-word'; // only for textarea-s\n  }\n  // Position off-screen\n  style.position = 'absolute'; // required to return coordinates properly\n  if (!debug) {\n    style.visibility = 'hidden';\n  } // not 'display: none' because we want rendering\n  // Transfer the element's properties to the div\n  properties.forEach(prop => {\n    if (isInput && prop === 'lineHeight') {\n      // Special case for <input>s because text is rendered centered and line height may be != height\n      style.lineHeight = computed.height;\n    } else {\n      // @ts-ignore\n      style[prop] = computed[prop];\n    }\n  });\n  if (isFirefox) {\n    // Firefox lies about the overflow property for textareas: https://bugzilla.mozilla.org/show_bug.cgi?id=984275\n    if (element.scrollHeight > _parseInt(computed.height)) {\n      style.overflowY = 'scroll';\n    }\n  } else {\n    style.overflow = 'hidden'; // for Chrome to not render a scrollbar; IE keeps overflowY = 'scroll'\n  }\n  div.textContent = element.value.substring(0, position);\n  // The second special handling for input type=\"text\" vs textarea:\n  // spaces need to be replaced with non-breaking spaces - http://stackoverflow.com/a/13402035/1269037\n  if (isInput) {\n    div.textContent = div.textContent.replace(/\\s/g, '\\u00a0');\n  }\n  const span = document.createElement('span');\n  // Wrapping must be replicated *exactly*, including when a long word gets\n  // onto the next line, with whitespace at the end of the line before (#7).\n  // The  *only* reliable way to do that is to copy the *entire* rest of the\n  // textarea's content into the <span> created at the caret position.\n  // For inputs, just '.' would be enough, but no need to bother.\n  span.textContent = element.value.substring(position) || '.'; // || because a completely empty faux span doesn't render at all\n  div.appendChild(span);\n  const coordinates = {\n    top: span.offsetTop + _parseInt(computed.borderTopWidth),\n    left: span.offsetLeft + _parseInt(computed.borderLeftWidth),\n    height: _parseInt(computed.lineHeight)\n  };\n  if (debug) {\n    span.style.backgroundColor = '#eee';\n    createDebugEle(element, coordinates);\n  } else {\n    document.body.removeChild(div);\n  }\n  return coordinates;\n}\nfunction createDebugEle(element, coordinates) {\n  const fontSize = getComputedStyle(element).getPropertyValue('font-size');\n  const rect = document.querySelector('#DEBUG') || document.createElement('div');\n  document.body.appendChild(rect);\n  rect.id = 'DEBUG';\n  rect.style.position = 'absolute';\n  rect.style.backgroundColor = 'red';\n  rect.style.height = fontSize;\n  rect.style.width = '1px';\n  rect.style.top = `${element.getBoundingClientRect().top - element.scrollTop + window.pageYOffset + coordinates.top}px`;\n  rect.style.left = `${element.getBoundingClientRect().left - element.scrollLeft + window.pageXOffset + coordinates.left}px`;\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction isStyleSupport(styleName) {\n  if (typeof window !== 'undefined' && window.document && window.document.documentElement) {\n    const styleNameList = Array.isArray(styleName) ? styleName : [styleName];\n    const {\n      documentElement\n    } = window.document;\n    return styleNameList.some(name => name in documentElement.style);\n  }\n  return false;\n}\nfunction getStyleAsText(styles) {\n  if (!styles) {\n    return '';\n  }\n  return Object.keys(styles).map(key => {\n    const val = styles[key];\n    return `${key}:${typeof val === 'string' ? val : `${val}px`}`;\n  }).join(';');\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n// We only handle element & text node.\nconst ELEMENT_NODE = 1;\nconst TEXT_NODE = 3;\nconst COMMENT_NODE = 8;\nlet ellipsisContainer;\nconst wrapperStyle = {\n  padding: '0',\n  margin: '0',\n  display: 'inline',\n  lineHeight: 'inherit'\n};\nfunction pxToNumber(value) {\n  if (!value) {\n    return 0;\n  }\n  const match = value.match(/^\\d*(\\.\\d*)?/);\n  return match ? Number(match[0]) : 0;\n}\nfunction styleToString(style) {\n  // There are some different behavior between Firefox & Chrome.\n  // We have to handle this ourself.\n  const styleNames = Array.prototype.slice.apply(style);\n  return styleNames.map(name => `${name}: ${style.getPropertyValue(name)};`).join('');\n}\nfunction mergeChildren(children) {\n  const childList = [];\n  children.forEach(child => {\n    const prevChild = childList[childList.length - 1];\n    if (prevChild && child.nodeType === TEXT_NODE && prevChild.nodeType === TEXT_NODE) {\n      prevChild.data += child.data;\n    } else {\n      childList.push(child);\n    }\n  });\n  return childList;\n}\nfunction measure(originEle, rows, contentNodes, fixedContent, ellipsisStr, suffixStr = '') {\n  if (!ellipsisContainer) {\n    ellipsisContainer = document.createElement('div');\n    ellipsisContainer.setAttribute('aria-hidden', 'true');\n    document.body.appendChild(ellipsisContainer);\n  }\n  // Get origin style\n  const originStyle = window.getComputedStyle(originEle);\n  const originCSS = styleToString(originStyle);\n  const lineHeight = pxToNumber(originStyle.lineHeight);\n  const maxHeight = Math.round(lineHeight * (rows + 1) + pxToNumber(originStyle.paddingTop) + pxToNumber(originStyle.paddingBottom));\n  // Set shadow\n  ellipsisContainer.setAttribute('style', originCSS);\n  ellipsisContainer.style.position = 'fixed';\n  ellipsisContainer.style.left = '0';\n  ellipsisContainer.style.height = 'auto';\n  ellipsisContainer.style.minHeight = 'auto';\n  ellipsisContainer.style.maxHeight = 'auto';\n  ellipsisContainer.style.top = '-999999px';\n  ellipsisContainer.style.zIndex = '-1000';\n  // clean up css overflow\n  ellipsisContainer.style.textOverflow = 'clip';\n  ellipsisContainer.style.whiteSpace = 'normal';\n  ellipsisContainer.style.webkitLineClamp = 'none';\n  const contentList = mergeChildren(contentNodes);\n  const container = document.createElement('div');\n  const contentContainer = document.createElement('span');\n  const suffixContainer = document.createTextNode(suffixStr);\n  const fixedContainer = document.createElement('span');\n  // Add styles in container\n  Object.assign(container.style, wrapperStyle);\n  Object.assign(contentContainer.style, wrapperStyle);\n  Object.assign(fixedContainer.style, wrapperStyle);\n  contentList.forEach(n => {\n    contentContainer.appendChild(n);\n  });\n  contentContainer.appendChild(suffixContainer);\n  fixedContent.forEach(node => {\n    fixedContainer.appendChild(node.cloneNode(true));\n  });\n  container.appendChild(contentContainer);\n  container.appendChild(fixedContainer);\n  // Render in the fake container\n  ellipsisContainer.appendChild(container);\n  // Check if ellipsis in measure div is height enough for content\n  function inRange() {\n    return ellipsisContainer.offsetHeight < maxHeight;\n  }\n  if (inRange()) {\n    const text = ellipsisContainer.innerHTML;\n    ellipsisContainer.removeChild(container);\n    return {\n      contentNodes,\n      text,\n      ellipsis: false\n    };\n  }\n  // We should clone the childNode since they're controlled by React and we can't reuse it without warning\n  const childNodes = Array.prototype.slice.apply(ellipsisContainer.childNodes[0].childNodes[0].cloneNode(true).childNodes).filter(({\n    nodeType\n  }) => nodeType !== COMMENT_NODE);\n  const fixedNodes = Array.prototype.slice.apply(ellipsisContainer.childNodes[0].childNodes[1].cloneNode(true).childNodes);\n  ellipsisContainer.removeChild(container);\n  // ========================= Find match ellipsis content =========================\n  ellipsisContainer.innerHTML = '';\n  // Create origin content holder\n  const ellipsisContentHolder = document.createElement('span');\n  ellipsisContainer.appendChild(ellipsisContentHolder);\n  const ellipsisTextNode = document.createTextNode(ellipsisStr + suffixStr);\n  ellipsisContentHolder.appendChild(ellipsisTextNode);\n  fixedNodes.forEach(childNode => {\n    ellipsisContainer.appendChild(childNode);\n  });\n  // Append before fixed nodes\n  function appendChildNode(node) {\n    ellipsisContentHolder.insertBefore(node, ellipsisTextNode);\n  }\n  // Get maximum text\n  function measureText(textNode, fullText, startLoc = 0, endLoc = fullText.length, lastSuccessLoc = 0) {\n    const midLoc = Math.floor((startLoc + endLoc) / 2);\n    textNode.textContent = fullText.slice(0, midLoc);\n    if (startLoc >= endLoc - 1) {\n      // Loop when step is small\n      for (let step = endLoc; step >= startLoc; step -= 1) {\n        const currentStepText = fullText.slice(0, step);\n        textNode.textContent = currentStepText;\n        if (inRange() || !currentStepText) {\n          return step === fullText.length ? {\n            finished: false,\n            node: document.createTextNode(fullText)\n          } : {\n            finished: true,\n            node: document.createTextNode(currentStepText)\n          };\n        }\n      }\n    }\n    if (inRange()) {\n      return measureText(textNode, fullText, midLoc, endLoc, midLoc);\n    } else {\n      return measureText(textNode, fullText, startLoc, midLoc, lastSuccessLoc);\n    }\n  }\n  function measureNode(childNode, index) {\n    const type = childNode.nodeType;\n    if (type === ELEMENT_NODE) {\n      // We don't split element, it will keep if whole element can be displayed.\n      // appendChildNode(childNode);\n      if (inRange()) {\n        return {\n          finished: false,\n          node: contentList[index]\n        };\n      }\n      // Clean up if can not pull in\n      ellipsisContentHolder.removeChild(childNode);\n      return {\n        finished: true,\n        node: null\n      };\n    } else if (type === TEXT_NODE) {\n      const fullText = childNode.textContent || '';\n      const textNode = document.createTextNode(fullText);\n      appendChildNode(textNode);\n      return measureText(textNode, fullText);\n    }\n    // Not handle other type of content\n    // PS: This code should not be attached after react 16\n    return {\n      finished: false,\n      node: null\n    };\n  }\n  const ellipsisNodes = [];\n  childNodes.some((childNode, index) => {\n    const {\n      finished,\n      node\n    } = measureNode(childNode, index);\n    if (node) {\n      ellipsisNodes.push(node);\n    }\n    return finished;\n  });\n  const result = {\n    contentNodes: ellipsisNodes,\n    text: ellipsisContainer.innerHTML,\n    ellipsis: true\n  };\n  while (ellipsisContainer.firstChild) {\n    ellipsisContainer.removeChild(ellipsisContainer.firstChild);\n  }\n  return result;\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nlet scrollbarVerticalSize;\nlet scrollbarHorizontalSize;\n// Measure scrollbar width for padding body during modal show/hide\nconst scrollbarMeasure = {\n  position: 'absolute',\n  top: '-9999px',\n  width: '50px',\n  height: '50px'\n};\nfunction measureScrollbar(direction = 'vertical', prefix = 'ant') {\n  if (typeof document === 'undefined' || typeof window === 'undefined') {\n    return 0;\n  }\n  const isVertical = direction === 'vertical';\n  if (isVertical && scrollbarVerticalSize) {\n    return scrollbarVerticalSize;\n  } else if (!isVertical && scrollbarHorizontalSize) {\n    return scrollbarHorizontalSize;\n  }\n  const scrollDiv = document.createElement('div');\n  Object.keys(scrollbarMeasure).forEach(scrollProp => {\n    // @ts-ignore\n    scrollDiv.style[scrollProp] = scrollbarMeasure[scrollProp];\n  });\n  // apply hide scrollbar className ahead\n  scrollDiv.className = `${prefix}-hide-scrollbar scroll-div-append-to-body`;\n  // Append related overflow style\n  if (isVertical) {\n    scrollDiv.style.overflowY = 'scroll';\n  } else {\n    scrollDiv.style.overflowX = 'scroll';\n  }\n  document.body.appendChild(scrollDiv);\n  let size = 0;\n  if (isVertical) {\n    size = scrollDiv.offsetWidth - scrollDiv.clientWidth;\n    scrollbarVerticalSize = size;\n  } else {\n    size = scrollDiv.offsetHeight - scrollDiv.clientHeight;\n    scrollbarHorizontalSize = size;\n  }\n  document.body.removeChild(scrollDiv);\n  return size;\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction ensureInBounds(value, boundValue) {\n  return value ? value < boundValue ? value : boundValue : boundValue;\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction inNextTick() {\n  const timer = new Subject();\n  Promise.resolve().then(() => timer.next());\n  return timer.pipe(take(1));\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction wrapIntoObservable(value) {\n  if (isObservable(value)) {\n    return value;\n  }\n  if (isPromise(value)) {\n    // Use `Promise.resolve()` to wrap promise-like instances.\n    return from(Promise.resolve(value));\n  }\n  return of(value);\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/**\n * Sync from rc-util [https://github.com/react-component/util]\n */\nfunction canUseDom() {\n  return !!(typeof window !== 'undefined' && window.document && window.document.createElement);\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/**\n * Sync from rc-util [https://github.com/react-component/util]\n */\nconst MARK_KEY = `rc-util-key`;\nfunction getMark({\n  mark\n} = {}) {\n  if (mark) {\n    return mark.startsWith('data-') ? mark : `data-${mark}`;\n  }\n  return MARK_KEY;\n}\nfunction getContainer(option) {\n  if (option.attachTo) {\n    return option.attachTo;\n  }\n  const head = document.querySelector('head');\n  return head || document.body;\n}\nfunction injectCSS(css, options = {}) {\n  if (!canUseDom()) {\n    return null;\n  }\n  const styleNode = document.createElement('style');\n  if (options.cspNonce) {\n    styleNode.nonce = options.cspNonce;\n  }\n  styleNode.innerHTML = css;\n  const container = getContainer(options);\n  const {\n    firstChild\n  } = container;\n  if (options.prepend && container.prepend) {\n    // Use `prepend` first\n    container.prepend(styleNode);\n  } else if (options.prepend && firstChild) {\n    // Fallback to `insertBefore` like IE not support `prepend`\n    container.insertBefore(styleNode, firstChild);\n  } else {\n    container.appendChild(styleNode);\n  }\n  return styleNode;\n}\nconst containerCache = new Map();\nfunction findExistNode(key, option = {}) {\n  const container = getContainer(option);\n  return Array.from(containerCache.get(container)?.children || []).find(node => node.tagName === 'STYLE' && node.getAttribute(getMark(option)) === key);\n}\nfunction removeCSS(key, option = {}) {\n  const existNode = findExistNode(key, option);\n  existNode?.parentNode?.removeChild(existNode);\n}\nfunction updateCSS(css, key, options = {}) {\n  const container = getContainer(options);\n  // Get real parent\n  if (!containerCache.has(container)) {\n    const placeholderStyle = injectCSS('', options);\n    // @ts-ignore\n    const {\n      parentNode\n    } = placeholderStyle;\n    containerCache.set(container, parentNode);\n    parentNode.removeChild(placeholderStyle);\n  }\n  const existNode = findExistNode(key, options);\n  if (existNode) {\n    if (options.cspNonce && existNode.nonce !== options.cspNonce) {\n      existNode.nonce = options.cspNonce;\n    }\n    if (existNode.innerHTML !== css) {\n      existNode.innerHTML = css;\n    }\n    return existNode;\n  }\n  const newNode = injectCSS(css, options);\n  newNode?.setAttribute(getMark(options), key);\n  return newNode;\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction getStatusClassNames(prefixCls, status, hasFeedback) {\n  return {\n    [`${prefixCls}-status-success`]: status === 'success',\n    [`${prefixCls}-status-warning`]: status === 'warning',\n    [`${prefixCls}-status-error`]: status === 'error',\n    [`${prefixCls}-status-validating`]: status === 'validating',\n    [`${prefixCls}-has-feedback`]: hasFeedback\n  };\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { InputBoolean, InputCssPixel, InputNumber, arraysEqual, canUseDom, createDebugEle, ensureInBounds, ensureNumberInRange, getCaretCoordinates, getElementOffset, getEventPosition, getMentions, getPercent, getPrecision, getRegExp, getRepeatedElement, getStatusClassNames, getStyleAsText, inNextTick, injectCSS, isNil, isNonEmptyString, isNotNil, isNumberFinite, isPromise, isStyleSupport, isTemplateRef, isTouchEvent, measure, measureScrollbar, padEnd, padStart, properties, pxToNumber, removeCSS, scrollIntoView, shallowCopyArray, shallowEqual, silentEvent, sum, toArray, toBoolean, toCssPixel, toDecimal, toNumber, updateCSS, valueFunctionProp, wrapIntoObservable };", "map": {"version": 3, "names": ["TemplateRef", "coerceBooleanProperty", "_isNumberValue", "coerceCssPixelValue", "warn", "Subject", "isObservable", "from", "of", "take", "toArray", "value", "ret", "Array", "isArray", "arraysEqual", "array1", "array2", "length", "len", "i", "shallowCopyArray", "source", "slice", "isNotNil", "isNil", "shallowEqual", "objA", "objB", "keysA", "Object", "keys", "keysB", "bHasOwnProperty", "prototype", "hasOwnProperty", "bind", "idx", "key", "isNonEmptyString", "isTemplateRef", "toBoolean", "toNumber", "fallback<PERSON><PERSON><PERSON>", "Number", "toCssPixel", "valueFunctionProp", "prop", "args", "propDecoratorFactory", "name", "fallback", "propDecorator", "target", "propName", "originalDescriptor", "privatePropName", "call", "defineProperty", "configurable", "writable", "get", "set", "InputBoolean", "InputCssPixel", "InputNumber", "silentEvent", "e", "stopPropagation", "preventDefault", "getElementOffset", "elem", "getClientRects", "top", "left", "rect", "getBoundingClientRect", "win", "ownerDocument", "defaultView", "pageYOffset", "pageXOffset", "isTouchEvent", "event", "type", "startsWith", "getEventPosition", "touches", "changedTouches", "getRegExp", "prefix", "prefixArray", "prefixToken", "join", "replace", "RegExp", "getMentions", "regex", "mentions", "match", "map", "trim", "padStart", "toPad", "element", "joined", "getRepeatedElement", "padEnd", "fill", "isPromise", "obj", "then", "catch", "getPercent", "min", "max", "getPrecision", "num", "numStr", "toString", "dotIndex", "indexOf", "ensureNumberInRange", "isNaN", "isNumberFinite", "isFinite", "toDecimal", "decimal", "Math", "round", "pow", "sum", "input", "initial", "reduce", "previous", "current", "scrollIntoView", "node", "nodeAsAny", "scrollIntoViewIfNeeded", "properties", "<PERSON><PERSON><PERSON><PERSON>", "window", "isFirefox", "mozInnerScreenX", "_parseInt", "str", "parseInt", "getCaretCoordinates", "position", "options", "Error", "debug", "el", "document", "querySelector", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "div", "createElement", "id", "body", "append<PERSON><PERSON><PERSON>", "style", "computed", "getComputedStyle", "currentStyle", "isInput", "nodeName", "whiteSpace", "wordWrap", "visibility", "for<PERSON>ach", "lineHeight", "height", "scrollHeight", "overflowY", "overflow", "textContent", "substring", "span", "coordinates", "offsetTop", "borderTopWidth", "offsetLeft", "borderLeftWidth", "backgroundColor", "createDebugEle", "fontSize", "getPropertyValue", "width", "scrollTop", "scrollLeft", "isStyleSupport", "styleName", "documentElement", "styleNameList", "some", "getStyleAsText", "styles", "val", "ELEMENT_NODE", "TEXT_NODE", "COMMENT_NODE", "ellip<PERSON><PERSON><PERSON><PERSON>", "wrapperStyle", "padding", "margin", "display", "pxToNumber", "styleToString", "styleNames", "apply", "mergeChildren", "children", "childList", "child", "prev<PERSON><PERSON><PERSON>", "nodeType", "data", "push", "measure", "originEle", "rows", "contentNodes", "fixedContent", "ellipsisStr", "suffixStr", "setAttribute", "originStyle", "originCSS", "maxHeight", "paddingTop", "paddingBottom", "minHeight", "zIndex", "textOverflow", "webkitLineClamp", "contentList", "container", "contentContainer", "suffixContainer", "createTextNode", "fixedContainer", "assign", "n", "cloneNode", "inRange", "offsetHeight", "text", "innerHTML", "ellipsis", "childNodes", "filter", "fixedNodes", "ellipsisContentHolder", "ellipsisTextNode", "childNode", "appendChildNode", "insertBefore", "measureText", "textNode", "fullText", "startLoc", "endLoc", "lastSuccessLoc", "midLoc", "floor", "step", "currentStepText", "finished", "measureNode", "index", "ellipsisNodes", "result", "<PERSON><PERSON><PERSON><PERSON>", "scrollbarVerticalSize", "scrollbarHorizontalSize", "scrollbarMeasure", "measureScrollbar", "direction", "isVertical", "scrollDiv", "scrollProp", "className", "overflowX", "size", "offsetWidth", "clientWidth", "clientHeight", "ensureInBounds", "boundValue", "inNextTick", "timer", "Promise", "resolve", "next", "pipe", "wrapIntoObservable", "canUseDom", "MARK_KEY", "getMark", "mark", "getContainer", "option", "attachTo", "head", "injectCSS", "css", "styleNode", "cspNonce", "nonce", "prepend", "containerCache", "Map", "findExistNode", "find", "tagName", "getAttribute", "removeCSS", "existNode", "updateCSS", "has", "placeholder<PERSON><PERSON><PERSON>", "newNode", "getStatusClassNames", "prefixCls", "status", "hasFeedback"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-core-util.mjs"], "sourcesContent": ["import { TemplateRef } from '@angular/core';\nimport { coerceBooleanProperty, _isNumberValue, coerceCssPixelValue } from '@angular/cdk/coercion';\nimport { warn } from 'ng-zorro-antd/core/logger';\nimport { Subject, isObservable, from, of } from 'rxjs';\nimport { take } from 'rxjs/operators';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction toArray(value) {\n    let ret;\n    if (value == null) {\n        ret = [];\n    }\n    else if (!Array.isArray(value)) {\n        ret = [value];\n    }\n    else {\n        ret = value;\n    }\n    return ret;\n}\nfunction arraysEqual(array1, array2) {\n    if (!array1 || !array2 || array1.length !== array2.length) {\n        return false;\n    }\n    const len = array1.length;\n    for (let i = 0; i < len; i++) {\n        if (array1[i] !== array2[i]) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction shallowCopyArray(source) {\n    return source.slice();\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction isNotNil(value) {\n    return typeof value !== 'undefined' && value !== null;\n}\nfunction isNil(value) {\n    return typeof value === 'undefined' || value === null;\n}\n/**\n * Examine if two objects are shallowly equaled.\n */\nfunction shallowEqual(objA, objB) {\n    if (objA === objB) {\n        return true;\n    }\n    if (typeof objA !== 'object' || !objA || typeof objB !== 'object' || !objB) {\n        return false;\n    }\n    const keysA = Object.keys(objA);\n    const keysB = Object.keys(objB);\n    if (keysA.length !== keysB.length) {\n        return false;\n    }\n    const bHasOwnProperty = Object.prototype.hasOwnProperty.bind(objB);\n    // eslint-disable-next-line @typescript-eslint/prefer-for-of\n    for (let idx = 0; idx < keysA.length; idx++) {\n        const key = keysA[idx];\n        if (!bHasOwnProperty(key)) {\n            return false;\n        }\n        if (objA[key] !== objB[key]) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction isNonEmptyString(value) {\n    return typeof value === 'string' && value !== '';\n}\nfunction isTemplateRef(value) {\n    return value instanceof TemplateRef;\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction toBoolean(value) {\n    return coerceBooleanProperty(value);\n}\nfunction toNumber(value, fallbackValue = 0) {\n    return _isNumberValue(value) ? Number(value) : fallbackValue;\n}\nfunction toCssPixel(value) {\n    return coerceCssPixelValue(value);\n}\n// eslint-disable  no-invalid-this\n/**\n * Get the function-property type's value\n */\nfunction valueFunctionProp(prop, ...args) {\n    return typeof prop === 'function' ? prop(...args) : prop;\n}\nfunction propDecoratorFactory(name, fallback) {\n    function propDecorator(target, propName, originalDescriptor) {\n        const privatePropName = `$$__zorroPropDecorator__${propName}`;\n        if (Object.prototype.hasOwnProperty.call(target, privatePropName)) {\n            warn(`The prop \"${privatePropName}\" is already exist, it will be overrided by ${name} decorator.`);\n        }\n        Object.defineProperty(target, privatePropName, {\n            configurable: true,\n            writable: true\n        });\n        return {\n            get() {\n                return originalDescriptor && originalDescriptor.get\n                    ? originalDescriptor.get.bind(this)()\n                    : this[privatePropName];\n            },\n            set(value) {\n                if (originalDescriptor && originalDescriptor.set) {\n                    originalDescriptor.set.bind(this)(fallback(value));\n                }\n                this[privatePropName] = fallback(value);\n            }\n        };\n    }\n    return propDecorator;\n}\n/**\n * Input decorator that handle a prop to do get/set automatically with toBoolean\n *\n * Why not using @InputBoolean alone without @Input? AOT needs @Input to be visible\n *\n * @howToUse\n * ```\n * @Input() @InputBoolean() visible: boolean = false;\n *\n * // Act as below:\n * // @Input()\n * // get visible() { return this.__visible; }\n * // set visible(value) { this.__visible = value; }\n * // __visible = false;\n * ```\n */\nfunction InputBoolean() {\n    return propDecoratorFactory('InputBoolean', toBoolean);\n}\nfunction InputCssPixel() {\n    return propDecoratorFactory('InputCssPixel', toCssPixel);\n}\nfunction InputNumber(fallbackValue) {\n    return propDecoratorFactory('InputNumber', (value) => toNumber(value, fallbackValue));\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/**\n * Silent an event by stopping and preventing it.\n */\nfunction silentEvent(e) {\n    e.stopPropagation();\n    e.preventDefault();\n}\nfunction getElementOffset(elem) {\n    if (!elem.getClientRects().length) {\n        return { top: 0, left: 0 };\n    }\n    const rect = elem.getBoundingClientRect();\n    const win = elem.ownerDocument.defaultView;\n    return {\n        top: rect.top + win.pageYOffset,\n        left: rect.left + win.pageXOffset\n    };\n}\n/**\n * Investigate if an event is a `TouchEvent`.\n */\nfunction isTouchEvent(event) {\n    return event.type.startsWith('touch');\n}\nfunction getEventPosition(event) {\n    return isTouchEvent(event) ? event.touches[0] || event.changedTouches[0] : event;\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction getRegExp(prefix) {\n    const prefixArray = Array.isArray(prefix) ? prefix : [prefix];\n    let prefixToken = prefixArray.join('').replace(/(\\$|\\^)/g, '\\\\$1');\n    if (prefixArray.length > 1) {\n        prefixToken = `[${prefixToken}]`;\n    }\n    return new RegExp(`(\\\\s|^)(${prefixToken})[^\\\\s]*`, 'g');\n}\nfunction getMentions(value, prefix = '@') {\n    if (typeof value !== 'string') {\n        return [];\n    }\n    const regex = getRegExp(prefix);\n    const mentions = value.match(regex);\n    return mentions !== null ? mentions.map(e => e.trim()) : [];\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/**\n * Much like lodash.\n */\nfunction padStart(toPad, length, element) {\n    if (toPad.length > length) {\n        return toPad;\n    }\n    const joined = `${getRepeatedElement(length, element)}${toPad}`;\n    return joined.slice(joined.length - length, joined.length);\n}\nfunction padEnd(toPad, length, element) {\n    const joined = `${toPad}${getRepeatedElement(length, element)}`;\n    return joined.slice(0, length);\n}\nfunction getRepeatedElement(length, element) {\n    return Array(length).fill(element).join('');\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction isPromise(obj) {\n    return !!obj && typeof obj.then === 'function' && typeof obj.catch === 'function';\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction getPercent(min, max, value) {\n    return ((value - min) / (max - min)) * 100;\n}\nfunction getPrecision(num) {\n    const numStr = num.toString();\n    const dotIndex = numStr.indexOf('.');\n    return dotIndex >= 0 ? numStr.length - dotIndex - 1 : 0;\n}\nfunction ensureNumberInRange(num, min, max) {\n    if (isNaN(num) || num < min) {\n        return min;\n    }\n    else if (num > max) {\n        return max;\n    }\n    else {\n        return num;\n    }\n}\nfunction isNumberFinite(value) {\n    return typeof value === 'number' && isFinite(value);\n}\nfunction toDecimal(value, decimal) {\n    return Math.round(value * Math.pow(10, decimal)) / Math.pow(10, decimal);\n}\nfunction sum(input, initial = 0) {\n    return input.reduce((previous, current) => previous + current, initial);\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction scrollIntoView(node) {\n    const nodeAsAny = node;\n    if (nodeAsAny.scrollIntoViewIfNeeded) {\n        /* eslint-disable-next-line @typescript-eslint/dot-notation */\n        nodeAsAny.scrollIntoViewIfNeeded(false);\n        return;\n    }\n    if (node.scrollIntoView) {\n        node.scrollIntoView(false);\n        return;\n    }\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n// from https://github.com/component/textarea-caret-position\n// We'll copy the properties below into the mirror div.\n// Note that some browsers, such as Firefox, do not concatenate properties\n// into their shorthand (e.g. padding-top, padding-bottom etc. -> padding),\n// so we have to list every single property explicitly.\nconst properties = [\n    'direction', // RTL support\n    'boxSizing',\n    'width', // on Chrome and IE, exclude the scrollbar, so the mirror div wraps exactly as the textarea does\n    'height',\n    'overflowX',\n    'overflowY', // copy the scrollbar for IE\n    'borderTopWidth',\n    'borderRightWidth',\n    'borderBottomWidth',\n    'borderLeftWidth',\n    'borderStyle',\n    'paddingTop',\n    'paddingRight',\n    'paddingBottom',\n    'paddingLeft',\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/font\n    'fontStyle',\n    'fontVariant',\n    'fontWeight',\n    'fontStretch',\n    'fontSize',\n    'fontSizeAdjust',\n    'lineHeight',\n    'fontFamily',\n    'textAlign',\n    'textTransform',\n    'textIndent',\n    'textDecoration', // might not make a difference, but better be safe\n    'letterSpacing',\n    'wordSpacing',\n    'tabSize',\n    'MozTabSize'\n];\nconst isBrowser = typeof window !== 'undefined';\nconst isFirefox = isBrowser && window.mozInnerScreenX != null;\nconst _parseInt = (str) => parseInt(str, 10);\nfunction getCaretCoordinates(element, position, options) {\n    if (!isBrowser) {\n        throw new Error('textarea-caret-position#getCaretCoordinates should only be called in a browser');\n    }\n    const debug = (options && options.debug) || false;\n    if (debug) {\n        const el = document.querySelector('#input-textarea-caret-position-mirror-div');\n        if (el) {\n            el.parentNode.removeChild(el);\n        }\n    }\n    // The mirror div will replicate the textarea's style\n    const div = document.createElement('div');\n    div.id = 'input-textarea-caret-position-mirror-div';\n    document.body.appendChild(div);\n    const style = div.style;\n    const computed = window.getComputedStyle ? window.getComputedStyle(element) : element.currentStyle; // currentStyle for IE < 9\n    const isInput = element.nodeName === 'INPUT';\n    // Default textarea styles\n    style.whiteSpace = 'pre-wrap';\n    if (!isInput) {\n        style.wordWrap = 'break-word'; // only for textarea-s\n    }\n    // Position off-screen\n    style.position = 'absolute'; // required to return coordinates properly\n    if (!debug) {\n        style.visibility = 'hidden';\n    } // not 'display: none' because we want rendering\n    // Transfer the element's properties to the div\n    properties.forEach((prop) => {\n        if (isInput && prop === 'lineHeight') {\n            // Special case for <input>s because text is rendered centered and line height may be != height\n            style.lineHeight = computed.height;\n        }\n        else {\n            // @ts-ignore\n            style[prop] = computed[prop];\n        }\n    });\n    if (isFirefox) {\n        // Firefox lies about the overflow property for textareas: https://bugzilla.mozilla.org/show_bug.cgi?id=984275\n        if (element.scrollHeight > _parseInt(computed.height)) {\n            style.overflowY = 'scroll';\n        }\n    }\n    else {\n        style.overflow = 'hidden'; // for Chrome to not render a scrollbar; IE keeps overflowY = 'scroll'\n    }\n    div.textContent = element.value.substring(0, position);\n    // The second special handling for input type=\"text\" vs textarea:\n    // spaces need to be replaced with non-breaking spaces - http://stackoverflow.com/a/13402035/1269037\n    if (isInput) {\n        div.textContent = div.textContent.replace(/\\s/g, '\\u00a0');\n    }\n    const span = document.createElement('span');\n    // Wrapping must be replicated *exactly*, including when a long word gets\n    // onto the next line, with whitespace at the end of the line before (#7).\n    // The  *only* reliable way to do that is to copy the *entire* rest of the\n    // textarea's content into the <span> created at the caret position.\n    // For inputs, just '.' would be enough, but no need to bother.\n    span.textContent = element.value.substring(position) || '.'; // || because a completely empty faux span doesn't render at all\n    div.appendChild(span);\n    const coordinates = {\n        top: span.offsetTop + _parseInt(computed.borderTopWidth),\n        left: span.offsetLeft + _parseInt(computed.borderLeftWidth),\n        height: _parseInt(computed.lineHeight)\n    };\n    if (debug) {\n        span.style.backgroundColor = '#eee';\n        createDebugEle(element, coordinates);\n    }\n    else {\n        document.body.removeChild(div);\n    }\n    return coordinates;\n}\nfunction createDebugEle(element, coordinates) {\n    const fontSize = getComputedStyle(element).getPropertyValue('font-size');\n    const rect = document.querySelector('#DEBUG') || document.createElement('div');\n    document.body.appendChild(rect);\n    rect.id = 'DEBUG';\n    rect.style.position = 'absolute';\n    rect.style.backgroundColor = 'red';\n    rect.style.height = fontSize;\n    rect.style.width = '1px';\n    rect.style.top = `${element.getBoundingClientRect().top - element.scrollTop + window.pageYOffset + coordinates.top}px`;\n    rect.style.left = `${element.getBoundingClientRect().left - element.scrollLeft + window.pageXOffset + coordinates.left}px`;\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction isStyleSupport(styleName) {\n    if (typeof window !== 'undefined' && window.document && window.document.documentElement) {\n        const styleNameList = Array.isArray(styleName) ? styleName : [styleName];\n        const { documentElement } = window.document;\n        return styleNameList.some(name => name in documentElement.style);\n    }\n    return false;\n}\nfunction getStyleAsText(styles) {\n    if (!styles) {\n        return '';\n    }\n    return Object.keys(styles)\n        .map(key => {\n        const val = styles[key];\n        return `${key}:${typeof val === 'string' ? val : `${val}px`}`;\n    })\n        .join(';');\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n// We only handle element & text node.\nconst ELEMENT_NODE = 1;\nconst TEXT_NODE = 3;\nconst COMMENT_NODE = 8;\nlet ellipsisContainer;\nconst wrapperStyle = {\n    padding: '0',\n    margin: '0',\n    display: 'inline',\n    lineHeight: 'inherit'\n};\nfunction pxToNumber(value) {\n    if (!value) {\n        return 0;\n    }\n    const match = value.match(/^\\d*(\\.\\d*)?/);\n    return match ? Number(match[0]) : 0;\n}\nfunction styleToString(style) {\n    // There are some different behavior between Firefox & Chrome.\n    // We have to handle this ourself.\n    const styleNames = Array.prototype.slice.apply(style);\n    return styleNames.map(name => `${name}: ${style.getPropertyValue(name)};`).join('');\n}\nfunction mergeChildren(children) {\n    const childList = [];\n    children.forEach((child) => {\n        const prevChild = childList[childList.length - 1];\n        if (prevChild && child.nodeType === TEXT_NODE && prevChild.nodeType === TEXT_NODE) {\n            prevChild.data += child.data;\n        }\n        else {\n            childList.push(child);\n        }\n    });\n    return childList;\n}\nfunction measure(originEle, rows, contentNodes, fixedContent, ellipsisStr, suffixStr = '') {\n    if (!ellipsisContainer) {\n        ellipsisContainer = document.createElement('div');\n        ellipsisContainer.setAttribute('aria-hidden', 'true');\n        document.body.appendChild(ellipsisContainer);\n    }\n    // Get origin style\n    const originStyle = window.getComputedStyle(originEle);\n    const originCSS = styleToString(originStyle);\n    const lineHeight = pxToNumber(originStyle.lineHeight);\n    const maxHeight = Math.round(lineHeight * (rows + 1) + pxToNumber(originStyle.paddingTop) + pxToNumber(originStyle.paddingBottom));\n    // Set shadow\n    ellipsisContainer.setAttribute('style', originCSS);\n    ellipsisContainer.style.position = 'fixed';\n    ellipsisContainer.style.left = '0';\n    ellipsisContainer.style.height = 'auto';\n    ellipsisContainer.style.minHeight = 'auto';\n    ellipsisContainer.style.maxHeight = 'auto';\n    ellipsisContainer.style.top = '-999999px';\n    ellipsisContainer.style.zIndex = '-1000';\n    // clean up css overflow\n    ellipsisContainer.style.textOverflow = 'clip';\n    ellipsisContainer.style.whiteSpace = 'normal';\n    ellipsisContainer.style.webkitLineClamp = 'none';\n    const contentList = mergeChildren(contentNodes);\n    const container = document.createElement('div');\n    const contentContainer = document.createElement('span');\n    const suffixContainer = document.createTextNode(suffixStr);\n    const fixedContainer = document.createElement('span');\n    // Add styles in container\n    Object.assign(container.style, wrapperStyle);\n    Object.assign(contentContainer.style, wrapperStyle);\n    Object.assign(fixedContainer.style, wrapperStyle);\n    contentList.forEach(n => {\n        contentContainer.appendChild(n);\n    });\n    contentContainer.appendChild(suffixContainer);\n    fixedContent.forEach(node => {\n        fixedContainer.appendChild(node.cloneNode(true));\n    });\n    container.appendChild(contentContainer);\n    container.appendChild(fixedContainer);\n    // Render in the fake container\n    ellipsisContainer.appendChild(container);\n    // Check if ellipsis in measure div is height enough for content\n    function inRange() {\n        return ellipsisContainer.offsetHeight < maxHeight;\n    }\n    if (inRange()) {\n        const text = ellipsisContainer.innerHTML;\n        ellipsisContainer.removeChild(container);\n        return { contentNodes, text, ellipsis: false };\n    }\n    // We should clone the childNode since they're controlled by React and we can't reuse it without warning\n    const childNodes = Array.prototype.slice\n        .apply(ellipsisContainer.childNodes[0].childNodes[0].cloneNode(true).childNodes)\n        .filter(({ nodeType }) => nodeType !== COMMENT_NODE);\n    const fixedNodes = Array.prototype.slice.apply(ellipsisContainer.childNodes[0].childNodes[1].cloneNode(true).childNodes);\n    ellipsisContainer.removeChild(container);\n    // ========================= Find match ellipsis content =========================\n    ellipsisContainer.innerHTML = '';\n    // Create origin content holder\n    const ellipsisContentHolder = document.createElement('span');\n    ellipsisContainer.appendChild(ellipsisContentHolder);\n    const ellipsisTextNode = document.createTextNode(ellipsisStr + suffixStr);\n    ellipsisContentHolder.appendChild(ellipsisTextNode);\n    fixedNodes.forEach(childNode => {\n        ellipsisContainer.appendChild(childNode);\n    });\n    // Append before fixed nodes\n    function appendChildNode(node) {\n        ellipsisContentHolder.insertBefore(node, ellipsisTextNode);\n    }\n    // Get maximum text\n    function measureText(textNode, fullText, startLoc = 0, endLoc = fullText.length, lastSuccessLoc = 0) {\n        const midLoc = Math.floor((startLoc + endLoc) / 2);\n        textNode.textContent = fullText.slice(0, midLoc);\n        if (startLoc >= endLoc - 1) {\n            // Loop when step is small\n            for (let step = endLoc; step >= startLoc; step -= 1) {\n                const currentStepText = fullText.slice(0, step);\n                textNode.textContent = currentStepText;\n                if (inRange() || !currentStepText) {\n                    return step === fullText.length\n                        ? {\n                            finished: false,\n                            node: document.createTextNode(fullText)\n                        }\n                        : {\n                            finished: true,\n                            node: document.createTextNode(currentStepText)\n                        };\n                }\n            }\n        }\n        if (inRange()) {\n            return measureText(textNode, fullText, midLoc, endLoc, midLoc);\n        }\n        else {\n            return measureText(textNode, fullText, startLoc, midLoc, lastSuccessLoc);\n        }\n    }\n    function measureNode(childNode, index) {\n        const type = childNode.nodeType;\n        if (type === ELEMENT_NODE) {\n            // We don't split element, it will keep if whole element can be displayed.\n            // appendChildNode(childNode);\n            if (inRange()) {\n                return {\n                    finished: false,\n                    node: contentList[index]\n                };\n            }\n            // Clean up if can not pull in\n            ellipsisContentHolder.removeChild(childNode);\n            return {\n                finished: true,\n                node: null\n            };\n        }\n        else if (type === TEXT_NODE) {\n            const fullText = childNode.textContent || '';\n            const textNode = document.createTextNode(fullText);\n            appendChildNode(textNode);\n            return measureText(textNode, fullText);\n        }\n        // Not handle other type of content\n        // PS: This code should not be attached after react 16\n        return {\n            finished: false,\n            node: null\n        };\n    }\n    const ellipsisNodes = [];\n    childNodes.some((childNode, index) => {\n        const { finished, node } = measureNode(childNode, index);\n        if (node) {\n            ellipsisNodes.push(node);\n        }\n        return finished;\n    });\n    const result = {\n        contentNodes: ellipsisNodes,\n        text: ellipsisContainer.innerHTML,\n        ellipsis: true\n    };\n    while (ellipsisContainer.firstChild) {\n        ellipsisContainer.removeChild(ellipsisContainer.firstChild);\n    }\n    return result;\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nlet scrollbarVerticalSize;\nlet scrollbarHorizontalSize;\n// Measure scrollbar width for padding body during modal show/hide\nconst scrollbarMeasure = {\n    position: 'absolute',\n    top: '-9999px',\n    width: '50px',\n    height: '50px'\n};\nfunction measureScrollbar(direction = 'vertical', prefix = 'ant') {\n    if (typeof document === 'undefined' || typeof window === 'undefined') {\n        return 0;\n    }\n    const isVertical = direction === 'vertical';\n    if (isVertical && scrollbarVerticalSize) {\n        return scrollbarVerticalSize;\n    }\n    else if (!isVertical && scrollbarHorizontalSize) {\n        return scrollbarHorizontalSize;\n    }\n    const scrollDiv = document.createElement('div');\n    Object.keys(scrollbarMeasure).forEach(scrollProp => {\n        // @ts-ignore\n        scrollDiv.style[scrollProp] = scrollbarMeasure[scrollProp];\n    });\n    // apply hide scrollbar className ahead\n    scrollDiv.className = `${prefix}-hide-scrollbar scroll-div-append-to-body`;\n    // Append related overflow style\n    if (isVertical) {\n        scrollDiv.style.overflowY = 'scroll';\n    }\n    else {\n        scrollDiv.style.overflowX = 'scroll';\n    }\n    document.body.appendChild(scrollDiv);\n    let size = 0;\n    if (isVertical) {\n        size = scrollDiv.offsetWidth - scrollDiv.clientWidth;\n        scrollbarVerticalSize = size;\n    }\n    else {\n        size = scrollDiv.offsetHeight - scrollDiv.clientHeight;\n        scrollbarHorizontalSize = size;\n    }\n    document.body.removeChild(scrollDiv);\n    return size;\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction ensureInBounds(value, boundValue) {\n    return value ? (value < boundValue ? value : boundValue) : boundValue;\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction inNextTick() {\n    const timer = new Subject();\n    Promise.resolve().then(() => timer.next());\n    return timer.pipe(take(1));\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction wrapIntoObservable(value) {\n    if (isObservable(value)) {\n        return value;\n    }\n    if (isPromise(value)) {\n        // Use `Promise.resolve()` to wrap promise-like instances.\n        return from(Promise.resolve(value));\n    }\n    return of(value);\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/**\n * Sync from rc-util [https://github.com/react-component/util]\n */\nfunction canUseDom() {\n    return !!(typeof window !== 'undefined' && window.document && window.document.createElement);\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/**\n * Sync from rc-util [https://github.com/react-component/util]\n */\nconst MARK_KEY = `rc-util-key`;\nfunction getMark({ mark } = {}) {\n    if (mark) {\n        return mark.startsWith('data-') ? mark : `data-${mark}`;\n    }\n    return MARK_KEY;\n}\nfunction getContainer(option) {\n    if (option.attachTo) {\n        return option.attachTo;\n    }\n    const head = document.querySelector('head');\n    return head || document.body;\n}\nfunction injectCSS(css, options = {}) {\n    if (!canUseDom()) {\n        return null;\n    }\n    const styleNode = document.createElement('style');\n    if (options.cspNonce) {\n        styleNode.nonce = options.cspNonce;\n    }\n    styleNode.innerHTML = css;\n    const container = getContainer(options);\n    const { firstChild } = container;\n    if (options.prepend && container.prepend) {\n        // Use `prepend` first\n        container.prepend(styleNode);\n    }\n    else if (options.prepend && firstChild) {\n        // Fallback to `insertBefore` like IE not support `prepend`\n        container.insertBefore(styleNode, firstChild);\n    }\n    else {\n        container.appendChild(styleNode);\n    }\n    return styleNode;\n}\nconst containerCache = new Map();\nfunction findExistNode(key, option = {}) {\n    const container = getContainer(option);\n    return Array.from(containerCache.get(container)?.children || []).find(node => node.tagName === 'STYLE' && node.getAttribute(getMark(option)) === key);\n}\nfunction removeCSS(key, option = {}) {\n    const existNode = findExistNode(key, option);\n    existNode?.parentNode?.removeChild(existNode);\n}\nfunction updateCSS(css, key, options = {}) {\n    const container = getContainer(options);\n    // Get real parent\n    if (!containerCache.has(container)) {\n        const placeholderStyle = injectCSS('', options);\n        // @ts-ignore\n        const { parentNode } = placeholderStyle;\n        containerCache.set(container, parentNode);\n        parentNode.removeChild(placeholderStyle);\n    }\n    const existNode = findExistNode(key, options);\n    if (existNode) {\n        if (options.cspNonce && existNode.nonce !== options.cspNonce) {\n            existNode.nonce = options.cspNonce;\n        }\n        if (existNode.innerHTML !== css) {\n            existNode.innerHTML = css;\n        }\n        return existNode;\n    }\n    const newNode = injectCSS(css, options);\n    newNode?.setAttribute(getMark(options), key);\n    return newNode;\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction getStatusClassNames(prefixCls, status, hasFeedback) {\n    return {\n        [`${prefixCls}-status-success`]: status === 'success',\n        [`${prefixCls}-status-warning`]: status === 'warning',\n        [`${prefixCls}-status-error`]: status === 'error',\n        [`${prefixCls}-status-validating`]: status === 'validating',\n        [`${prefixCls}-has-feedback`]: hasFeedback\n    };\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { InputBoolean, InputCssPixel, InputNumber, arraysEqual, canUseDom, createDebugEle, ensureInBounds, ensureNumberInRange, getCaretCoordinates, getElementOffset, getEventPosition, getMentions, getPercent, getPrecision, getRegExp, getRepeatedElement, getStatusClassNames, getStyleAsText, inNextTick, injectCSS, isNil, isNonEmptyString, isNotNil, isNumberFinite, isPromise, isStyleSupport, isTemplateRef, isTouchEvent, measure, measureScrollbar, padEnd, padStart, properties, pxToNumber, removeCSS, scrollIntoView, shallowCopyArray, shallowEqual, silentEvent, sum, toArray, toBoolean, toCssPixel, toDecimal, toNumber, updateCSS, valueFunctionProp, wrapIntoObservable };\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,eAAe;AAC3C,SAASC,qBAAqB,EAAEC,cAAc,EAAEC,mBAAmB,QAAQ,uBAAuB;AAClG,SAASC,IAAI,QAAQ,2BAA2B;AAChD,SAASC,OAAO,EAAEC,YAAY,EAAEC,IAAI,EAAEC,EAAE,QAAQ,MAAM;AACtD,SAASC,IAAI,QAAQ,gBAAgB;;AAErC;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAACC,KAAK,EAAE;EACpB,IAAIC,GAAG;EACP,IAAID,KAAK,IAAI,IAAI,EAAE;IACfC,GAAG,GAAG,EAAE;EACZ,CAAC,MACI,IAAI,CAACC,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,EAAE;IAC5BC,GAAG,GAAG,CAACD,KAAK,CAAC;EACjB,CAAC,MACI;IACDC,GAAG,GAAGD,KAAK;EACf;EACA,OAAOC,GAAG;AACd;AACA,SAASG,WAAWA,CAACC,MAAM,EAAEC,MAAM,EAAE;EACjC,IAAI,CAACD,MAAM,IAAI,CAACC,MAAM,IAAID,MAAM,CAACE,MAAM,KAAKD,MAAM,CAACC,MAAM,EAAE;IACvD,OAAO,KAAK;EAChB;EACA,MAAMC,GAAG,GAAGH,MAAM,CAACE,MAAM;EACzB,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,GAAG,EAAEC,CAAC,EAAE,EAAE;IAC1B,IAAIJ,MAAM,CAACI,CAAC,CAAC,KAAKH,MAAM,CAACG,CAAC,CAAC,EAAE;MACzB,OAAO,KAAK;IAChB;EACJ;EACA,OAAO,IAAI;AACf;AACA,SAASC,gBAAgBA,CAACC,MAAM,EAAE;EAC9B,OAAOA,MAAM,CAACC,KAAK,CAAC,CAAC;AACzB;;AAEA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACb,KAAK,EAAE;EACrB,OAAO,OAAOA,KAAK,KAAK,WAAW,IAAIA,KAAK,KAAK,IAAI;AACzD;AACA,SAASc,KAAKA,CAACd,KAAK,EAAE;EAClB,OAAO,OAAOA,KAAK,KAAK,WAAW,IAAIA,KAAK,KAAK,IAAI;AACzD;AACA;AACA;AACA;AACA,SAASe,YAAYA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAC9B,IAAID,IAAI,KAAKC,IAAI,EAAE;IACf,OAAO,IAAI;EACf;EACA,IAAI,OAAOD,IAAI,KAAK,QAAQ,IAAI,CAACA,IAAI,IAAI,OAAOC,IAAI,KAAK,QAAQ,IAAI,CAACA,IAAI,EAAE;IACxE,OAAO,KAAK;EAChB;EACA,MAAMC,KAAK,GAAGC,MAAM,CAACC,IAAI,CAACJ,IAAI,CAAC;EAC/B,MAAMK,KAAK,GAAGF,MAAM,CAACC,IAAI,CAACH,IAAI,CAAC;EAC/B,IAAIC,KAAK,CAACX,MAAM,KAAKc,KAAK,CAACd,MAAM,EAAE;IAC/B,OAAO,KAAK;EAChB;EACA,MAAMe,eAAe,GAAGH,MAAM,CAACI,SAAS,CAACC,cAAc,CAACC,IAAI,CAACR,IAAI,CAAC;EAClE;EACA,KAAK,IAAIS,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGR,KAAK,CAACX,MAAM,EAAEmB,GAAG,EAAE,EAAE;IACzC,MAAMC,GAAG,GAAGT,KAAK,CAACQ,GAAG,CAAC;IACtB,IAAI,CAACJ,eAAe,CAACK,GAAG,CAAC,EAAE;MACvB,OAAO,KAAK;IAChB;IACA,IAAIX,IAAI,CAACW,GAAG,CAAC,KAAKV,IAAI,CAACU,GAAG,CAAC,EAAE;MACzB,OAAO,KAAK;IAChB;EACJ;EACA,OAAO,IAAI;AACf;AACA,SAASC,gBAAgBA,CAAC5B,KAAK,EAAE;EAC7B,OAAO,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,EAAE;AACpD;AACA,SAAS6B,aAAaA,CAAC7B,KAAK,EAAE;EAC1B,OAAOA,KAAK,YAAYX,WAAW;AACvC;;AAEA;AACA;AACA;AACA;AACA,SAASyC,SAASA,CAAC9B,KAAK,EAAE;EACtB,OAAOV,qBAAqB,CAACU,KAAK,CAAC;AACvC;AACA,SAAS+B,QAAQA,CAAC/B,KAAK,EAAEgC,aAAa,GAAG,CAAC,EAAE;EACxC,OAAOzC,cAAc,CAACS,KAAK,CAAC,GAAGiC,MAAM,CAACjC,KAAK,CAAC,GAAGgC,aAAa;AAChE;AACA,SAASE,UAAUA,CAAClC,KAAK,EAAE;EACvB,OAAOR,mBAAmB,CAACQ,KAAK,CAAC;AACrC;AACA;AACA;AACA;AACA;AACA,SAASmC,iBAAiBA,CAACC,IAAI,EAAE,GAAGC,IAAI,EAAE;EACtC,OAAO,OAAOD,IAAI,KAAK,UAAU,GAAGA,IAAI,CAAC,GAAGC,IAAI,CAAC,GAAGD,IAAI;AAC5D;AACA,SAASE,oBAAoBA,CAACC,IAAI,EAAEC,QAAQ,EAAE;EAC1C,SAASC,aAAaA,CAACC,MAAM,EAAEC,QAAQ,EAAEC,kBAAkB,EAAE;IACzD,MAAMC,eAAe,GAAI,2BAA0BF,QAAS,EAAC;IAC7D,IAAIxB,MAAM,CAACI,SAAS,CAACC,cAAc,CAACsB,IAAI,CAACJ,MAAM,EAAEG,eAAe,CAAC,EAAE;MAC/DpD,IAAI,CAAE,aAAYoD,eAAgB,+CAA8CN,IAAK,aAAY,CAAC;IACtG;IACApB,MAAM,CAAC4B,cAAc,CAACL,MAAM,EAAEG,eAAe,EAAE;MAC3CG,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE;IACd,CAAC,CAAC;IACF,OAAO;MACHC,GAAGA,CAAA,EAAG;QACF,OAAON,kBAAkB,IAAIA,kBAAkB,CAACM,GAAG,GAC7CN,kBAAkB,CAACM,GAAG,CAACzB,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GACnC,IAAI,CAACoB,eAAe,CAAC;MAC/B,CAAC;MACDM,GAAGA,CAACnD,KAAK,EAAE;QACP,IAAI4C,kBAAkB,IAAIA,kBAAkB,CAACO,GAAG,EAAE;UAC9CP,kBAAkB,CAACO,GAAG,CAAC1B,IAAI,CAAC,IAAI,CAAC,CAACe,QAAQ,CAACxC,KAAK,CAAC,CAAC;QACtD;QACA,IAAI,CAAC6C,eAAe,CAAC,GAAGL,QAAQ,CAACxC,KAAK,CAAC;MAC3C;IACJ,CAAC;EACL;EACA,OAAOyC,aAAa;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASW,YAAYA,CAAA,EAAG;EACpB,OAAOd,oBAAoB,CAAC,cAAc,EAAER,SAAS,CAAC;AAC1D;AACA,SAASuB,aAAaA,CAAA,EAAG;EACrB,OAAOf,oBAAoB,CAAC,eAAe,EAAEJ,UAAU,CAAC;AAC5D;AACA,SAASoB,WAAWA,CAACtB,aAAa,EAAE;EAChC,OAAOM,oBAAoB,CAAC,aAAa,EAAGtC,KAAK,IAAK+B,QAAQ,CAAC/B,KAAK,EAAEgC,aAAa,CAAC,CAAC;AACzF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASuB,WAAWA,CAACC,CAAC,EAAE;EACpBA,CAAC,CAACC,eAAe,CAAC,CAAC;EACnBD,CAAC,CAACE,cAAc,CAAC,CAAC;AACtB;AACA,SAASC,gBAAgBA,CAACC,IAAI,EAAE;EAC5B,IAAI,CAACA,IAAI,CAACC,cAAc,CAAC,CAAC,CAACtD,MAAM,EAAE;IAC/B,OAAO;MAAEuD,GAAG,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAE,CAAC;EAC9B;EACA,MAAMC,IAAI,GAAGJ,IAAI,CAACK,qBAAqB,CAAC,CAAC;EACzC,MAAMC,GAAG,GAAGN,IAAI,CAACO,aAAa,CAACC,WAAW;EAC1C,OAAO;IACHN,GAAG,EAAEE,IAAI,CAACF,GAAG,GAAGI,GAAG,CAACG,WAAW;IAC/BN,IAAI,EAAEC,IAAI,CAACD,IAAI,GAAGG,GAAG,CAACI;EAC1B,CAAC;AACL;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,KAAK,EAAE;EACzB,OAAOA,KAAK,CAACC,IAAI,CAACC,UAAU,CAAC,OAAO,CAAC;AACzC;AACA,SAASC,gBAAgBA,CAACH,KAAK,EAAE;EAC7B,OAAOD,YAAY,CAACC,KAAK,CAAC,GAAGA,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC,IAAIJ,KAAK,CAACK,cAAc,CAAC,CAAC,CAAC,GAAGL,KAAK;AACpF;;AAEA;AACA;AACA;AACA;AACA,SAASM,SAASA,CAACC,MAAM,EAAE;EACvB,MAAMC,WAAW,GAAG9E,KAAK,CAACC,OAAO,CAAC4E,MAAM,CAAC,GAAGA,MAAM,GAAG,CAACA,MAAM,CAAC;EAC7D,IAAIE,WAAW,GAAGD,WAAW,CAACE,IAAI,CAAC,EAAE,CAAC,CAACC,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC;EAClE,IAAIH,WAAW,CAACzE,MAAM,GAAG,CAAC,EAAE;IACxB0E,WAAW,GAAI,IAAGA,WAAY,GAAE;EACpC;EACA,OAAO,IAAIG,MAAM,CAAE,WAAUH,WAAY,UAAS,EAAE,GAAG,CAAC;AAC5D;AACA,SAASI,WAAWA,CAACrF,KAAK,EAAE+E,MAAM,GAAG,GAAG,EAAE;EACtC,IAAI,OAAO/E,KAAK,KAAK,QAAQ,EAAE;IAC3B,OAAO,EAAE;EACb;EACA,MAAMsF,KAAK,GAAGR,SAAS,CAACC,MAAM,CAAC;EAC/B,MAAMQ,QAAQ,GAAGvF,KAAK,CAACwF,KAAK,CAACF,KAAK,CAAC;EACnC,OAAOC,QAAQ,KAAK,IAAI,GAAGA,QAAQ,CAACE,GAAG,CAACjC,CAAC,IAAIA,CAAC,CAACkC,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE;AAC/D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACC,KAAK,EAAErF,MAAM,EAAEsF,OAAO,EAAE;EACtC,IAAID,KAAK,CAACrF,MAAM,GAAGA,MAAM,EAAE;IACvB,OAAOqF,KAAK;EAChB;EACA,MAAME,MAAM,GAAI,GAAEC,kBAAkB,CAACxF,MAAM,EAAEsF,OAAO,CAAE,GAAED,KAAM,EAAC;EAC/D,OAAOE,MAAM,CAAClF,KAAK,CAACkF,MAAM,CAACvF,MAAM,GAAGA,MAAM,EAAEuF,MAAM,CAACvF,MAAM,CAAC;AAC9D;AACA,SAASyF,MAAMA,CAACJ,KAAK,EAAErF,MAAM,EAAEsF,OAAO,EAAE;EACpC,MAAMC,MAAM,GAAI,GAAEF,KAAM,GAAEG,kBAAkB,CAACxF,MAAM,EAAEsF,OAAO,CAAE,EAAC;EAC/D,OAAOC,MAAM,CAAClF,KAAK,CAAC,CAAC,EAAEL,MAAM,CAAC;AAClC;AACA,SAASwF,kBAAkBA,CAACxF,MAAM,EAAEsF,OAAO,EAAE;EACzC,OAAO3F,KAAK,CAACK,MAAM,CAAC,CAAC0F,IAAI,CAACJ,OAAO,CAAC,CAACX,IAAI,CAAC,EAAE,CAAC;AAC/C;;AAEA;AACA;AACA;AACA;AACA,SAASgB,SAASA,CAACC,GAAG,EAAE;EACpB,OAAO,CAAC,CAACA,GAAG,IAAI,OAAOA,GAAG,CAACC,IAAI,KAAK,UAAU,IAAI,OAAOD,GAAG,CAACE,KAAK,KAAK,UAAU;AACrF;;AAEA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,GAAG,EAAEC,GAAG,EAAExG,KAAK,EAAE;EACjC,OAAQ,CAACA,KAAK,GAAGuG,GAAG,KAAKC,GAAG,GAAGD,GAAG,CAAC,GAAI,GAAG;AAC9C;AACA,SAASE,YAAYA,CAACC,GAAG,EAAE;EACvB,MAAMC,MAAM,GAAGD,GAAG,CAACE,QAAQ,CAAC,CAAC;EAC7B,MAAMC,QAAQ,GAAGF,MAAM,CAACG,OAAO,CAAC,GAAG,CAAC;EACpC,OAAOD,QAAQ,IAAI,CAAC,GAAGF,MAAM,CAACpG,MAAM,GAAGsG,QAAQ,GAAG,CAAC,GAAG,CAAC;AAC3D;AACA,SAASE,mBAAmBA,CAACL,GAAG,EAAEH,GAAG,EAAEC,GAAG,EAAE;EACxC,IAAIQ,KAAK,CAACN,GAAG,CAAC,IAAIA,GAAG,GAAGH,GAAG,EAAE;IACzB,OAAOA,GAAG;EACd,CAAC,MACI,IAAIG,GAAG,GAAGF,GAAG,EAAE;IAChB,OAAOA,GAAG;EACd,CAAC,MACI;IACD,OAAOE,GAAG;EACd;AACJ;AACA,SAASO,cAAcA,CAACjH,KAAK,EAAE;EAC3B,OAAO,OAAOA,KAAK,KAAK,QAAQ,IAAIkH,QAAQ,CAAClH,KAAK,CAAC;AACvD;AACA,SAASmH,SAASA,CAACnH,KAAK,EAAEoH,OAAO,EAAE;EAC/B,OAAOC,IAAI,CAACC,KAAK,CAACtH,KAAK,GAAGqH,IAAI,CAACE,GAAG,CAAC,EAAE,EAAEH,OAAO,CAAC,CAAC,GAAGC,IAAI,CAACE,GAAG,CAAC,EAAE,EAAEH,OAAO,CAAC;AAC5E;AACA,SAASI,GAAGA,CAACC,KAAK,EAAEC,OAAO,GAAG,CAAC,EAAE;EAC7B,OAAOD,KAAK,CAACE,MAAM,CAAC,CAACC,QAAQ,EAAEC,OAAO,KAAKD,QAAQ,GAAGC,OAAO,EAAEH,OAAO,CAAC;AAC3E;;AAEA;AACA;AACA;AACA;AACA,SAASI,cAAcA,CAACC,IAAI,EAAE;EAC1B,MAAMC,SAAS,GAAGD,IAAI;EACtB,IAAIC,SAAS,CAACC,sBAAsB,EAAE;IAClC;IACAD,SAAS,CAACC,sBAAsB,CAAC,KAAK,CAAC;IACvC;EACJ;EACA,IAAIF,IAAI,CAACD,cAAc,EAAE;IACrBC,IAAI,CAACD,cAAc,CAAC,KAAK,CAAC;IAC1B;EACJ;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,UAAU,GAAG,CACf,WAAW;AAAE;AACb,WAAW,EACX,OAAO;AAAE;AACT,QAAQ,EACR,WAAW,EACX,WAAW;AAAE;AACb,gBAAgB,EAChB,kBAAkB,EAClB,mBAAmB,EACnB,iBAAiB,EACjB,aAAa,EACb,YAAY,EACZ,cAAc,EACd,eAAe,EACf,aAAa;AACb;AACA,WAAW,EACX,aAAa,EACb,YAAY,EACZ,aAAa,EACb,UAAU,EACV,gBAAgB,EAChB,YAAY,EACZ,YAAY,EACZ,WAAW,EACX,eAAe,EACf,YAAY,EACZ,gBAAgB;AAAE;AAClB,eAAe,EACf,aAAa,EACb,SAAS,EACT,YAAY,CACf;AACD,MAAMC,SAAS,GAAG,OAAOC,MAAM,KAAK,WAAW;AAC/C,MAAMC,SAAS,GAAGF,SAAS,IAAIC,MAAM,CAACE,eAAe,IAAI,IAAI;AAC7D,MAAMC,SAAS,GAAIC,GAAG,IAAKC,QAAQ,CAACD,GAAG,EAAE,EAAE,CAAC;AAC5C,SAASE,mBAAmBA,CAAC7C,OAAO,EAAE8C,QAAQ,EAAEC,OAAO,EAAE;EACrD,IAAI,CAACT,SAAS,EAAE;IACZ,MAAM,IAAIU,KAAK,CAAC,gFAAgF,CAAC;EACrG;EACA,MAAMC,KAAK,GAAIF,OAAO,IAAIA,OAAO,CAACE,KAAK,IAAK,KAAK;EACjD,IAAIA,KAAK,EAAE;IACP,MAAMC,EAAE,GAAGC,QAAQ,CAACC,aAAa,CAAC,2CAA2C,CAAC;IAC9E,IAAIF,EAAE,EAAE;MACJA,EAAE,CAACG,UAAU,CAACC,WAAW,CAACJ,EAAE,CAAC;IACjC;EACJ;EACA;EACA,MAAMK,GAAG,GAAGJ,QAAQ,CAACK,aAAa,CAAC,KAAK,CAAC;EACzCD,GAAG,CAACE,EAAE,GAAG,0CAA0C;EACnDN,QAAQ,CAACO,IAAI,CAACC,WAAW,CAACJ,GAAG,CAAC;EAC9B,MAAMK,KAAK,GAAGL,GAAG,CAACK,KAAK;EACvB,MAAMC,QAAQ,GAAGtB,MAAM,CAACuB,gBAAgB,GAAGvB,MAAM,CAACuB,gBAAgB,CAAC9D,OAAO,CAAC,GAAGA,OAAO,CAAC+D,YAAY,CAAC,CAAC;EACpG,MAAMC,OAAO,GAAGhE,OAAO,CAACiE,QAAQ,KAAK,OAAO;EAC5C;EACAL,KAAK,CAACM,UAAU,GAAG,UAAU;EAC7B,IAAI,CAACF,OAAO,EAAE;IACVJ,KAAK,CAACO,QAAQ,GAAG,YAAY,CAAC,CAAC;EACnC;EACA;EACAP,KAAK,CAACd,QAAQ,GAAG,UAAU,CAAC,CAAC;EAC7B,IAAI,CAACG,KAAK,EAAE;IACRW,KAAK,CAACQ,UAAU,GAAG,QAAQ;EAC/B,CAAC,CAAC;EACF;EACA/B,UAAU,CAACgC,OAAO,CAAE9H,IAAI,IAAK;IACzB,IAAIyH,OAAO,IAAIzH,IAAI,KAAK,YAAY,EAAE;MAClC;MACAqH,KAAK,CAACU,UAAU,GAAGT,QAAQ,CAACU,MAAM;IACtC,CAAC,MACI;MACD;MACAX,KAAK,CAACrH,IAAI,CAAC,GAAGsH,QAAQ,CAACtH,IAAI,CAAC;IAChC;EACJ,CAAC,CAAC;EACF,IAAIiG,SAAS,EAAE;IACX;IACA,IAAIxC,OAAO,CAACwE,YAAY,GAAG9B,SAAS,CAACmB,QAAQ,CAACU,MAAM,CAAC,EAAE;MACnDX,KAAK,CAACa,SAAS,GAAG,QAAQ;IAC9B;EACJ,CAAC,MACI;IACDb,KAAK,CAACc,QAAQ,GAAG,QAAQ,CAAC,CAAC;EAC/B;EACAnB,GAAG,CAACoB,WAAW,GAAG3E,OAAO,CAAC7F,KAAK,CAACyK,SAAS,CAAC,CAAC,EAAE9B,QAAQ,CAAC;EACtD;EACA;EACA,IAAIkB,OAAO,EAAE;IACTT,GAAG,CAACoB,WAAW,GAAGpB,GAAG,CAACoB,WAAW,CAACrF,OAAO,CAAC,KAAK,EAAE,QAAQ,CAAC;EAC9D;EACA,MAAMuF,IAAI,GAAG1B,QAAQ,CAACK,aAAa,CAAC,MAAM,CAAC;EAC3C;EACA;EACA;EACA;EACA;EACAqB,IAAI,CAACF,WAAW,GAAG3E,OAAO,CAAC7F,KAAK,CAACyK,SAAS,CAAC9B,QAAQ,CAAC,IAAI,GAAG,CAAC,CAAC;EAC7DS,GAAG,CAACI,WAAW,CAACkB,IAAI,CAAC;EACrB,MAAMC,WAAW,GAAG;IAChB7G,GAAG,EAAE4G,IAAI,CAACE,SAAS,GAAGrC,SAAS,CAACmB,QAAQ,CAACmB,cAAc,CAAC;IACxD9G,IAAI,EAAE2G,IAAI,CAACI,UAAU,GAAGvC,SAAS,CAACmB,QAAQ,CAACqB,eAAe,CAAC;IAC3DX,MAAM,EAAE7B,SAAS,CAACmB,QAAQ,CAACS,UAAU;EACzC,CAAC;EACD,IAAIrB,KAAK,EAAE;IACP4B,IAAI,CAACjB,KAAK,CAACuB,eAAe,GAAG,MAAM;IACnCC,cAAc,CAACpF,OAAO,EAAE8E,WAAW,CAAC;EACxC,CAAC,MACI;IACD3B,QAAQ,CAACO,IAAI,CAACJ,WAAW,CAACC,GAAG,CAAC;EAClC;EACA,OAAOuB,WAAW;AACtB;AACA,SAASM,cAAcA,CAACpF,OAAO,EAAE8E,WAAW,EAAE;EAC1C,MAAMO,QAAQ,GAAGvB,gBAAgB,CAAC9D,OAAO,CAAC,CAACsF,gBAAgB,CAAC,WAAW,CAAC;EACxE,MAAMnH,IAAI,GAAGgF,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC,IAAID,QAAQ,CAACK,aAAa,CAAC,KAAK,CAAC;EAC9EL,QAAQ,CAACO,IAAI,CAACC,WAAW,CAACxF,IAAI,CAAC;EAC/BA,IAAI,CAACsF,EAAE,GAAG,OAAO;EACjBtF,IAAI,CAACyF,KAAK,CAACd,QAAQ,GAAG,UAAU;EAChC3E,IAAI,CAACyF,KAAK,CAACuB,eAAe,GAAG,KAAK;EAClChH,IAAI,CAACyF,KAAK,CAACW,MAAM,GAAGc,QAAQ;EAC5BlH,IAAI,CAACyF,KAAK,CAAC2B,KAAK,GAAG,KAAK;EACxBpH,IAAI,CAACyF,KAAK,CAAC3F,GAAG,GAAI,GAAE+B,OAAO,CAAC5B,qBAAqB,CAAC,CAAC,CAACH,GAAG,GAAG+B,OAAO,CAACwF,SAAS,GAAGjD,MAAM,CAAC/D,WAAW,GAAGsG,WAAW,CAAC7G,GAAI,IAAG;EACtHE,IAAI,CAACyF,KAAK,CAAC1F,IAAI,GAAI,GAAE8B,OAAO,CAAC5B,qBAAqB,CAAC,CAAC,CAACF,IAAI,GAAG8B,OAAO,CAACyF,UAAU,GAAGlD,MAAM,CAAC9D,WAAW,GAAGqG,WAAW,CAAC5G,IAAK,IAAG;AAC9H;;AAEA;AACA;AACA;AACA;AACA,SAASwH,cAAcA,CAACC,SAAS,EAAE;EAC/B,IAAI,OAAOpD,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACY,QAAQ,IAAIZ,MAAM,CAACY,QAAQ,CAACyC,eAAe,EAAE;IACrF,MAAMC,aAAa,GAAGxL,KAAK,CAACC,OAAO,CAACqL,SAAS,CAAC,GAAGA,SAAS,GAAG,CAACA,SAAS,CAAC;IACxE,MAAM;MAAEC;IAAgB,CAAC,GAAGrD,MAAM,CAACY,QAAQ;IAC3C,OAAO0C,aAAa,CAACC,IAAI,CAACpJ,IAAI,IAAIA,IAAI,IAAIkJ,eAAe,CAAChC,KAAK,CAAC;EACpE;EACA,OAAO,KAAK;AAChB;AACA,SAASmC,cAAcA,CAACC,MAAM,EAAE;EAC5B,IAAI,CAACA,MAAM,EAAE;IACT,OAAO,EAAE;EACb;EACA,OAAO1K,MAAM,CAACC,IAAI,CAACyK,MAAM,CAAC,CACrBpG,GAAG,CAAC9D,GAAG,IAAI;IACZ,MAAMmK,GAAG,GAAGD,MAAM,CAAClK,GAAG,CAAC;IACvB,OAAQ,GAAEA,GAAI,IAAG,OAAOmK,GAAG,KAAK,QAAQ,GAAGA,GAAG,GAAI,GAAEA,GAAI,IAAI,EAAC;EACjE,CAAC,CAAC,CACG5G,IAAI,CAAC,GAAG,CAAC;AAClB;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAM6G,YAAY,GAAG,CAAC;AACtB,MAAMC,SAAS,GAAG,CAAC;AACnB,MAAMC,YAAY,GAAG,CAAC;AACtB,IAAIC,iBAAiB;AACrB,MAAMC,YAAY,GAAG;EACjBC,OAAO,EAAE,GAAG;EACZC,MAAM,EAAE,GAAG;EACXC,OAAO,EAAE,QAAQ;EACjBnC,UAAU,EAAE;AAChB,CAAC;AACD,SAASoC,UAAUA,CAACvM,KAAK,EAAE;EACvB,IAAI,CAACA,KAAK,EAAE;IACR,OAAO,CAAC;EACZ;EACA,MAAMwF,KAAK,GAAGxF,KAAK,CAACwF,KAAK,CAAC,cAAc,CAAC;EACzC,OAAOA,KAAK,GAAGvD,MAAM,CAACuD,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;AACvC;AACA,SAASgH,aAAaA,CAAC/C,KAAK,EAAE;EAC1B;EACA;EACA,MAAMgD,UAAU,GAAGvM,KAAK,CAACqB,SAAS,CAACX,KAAK,CAAC8L,KAAK,CAACjD,KAAK,CAAC;EACrD,OAAOgD,UAAU,CAAChH,GAAG,CAAClD,IAAI,IAAK,GAAEA,IAAK,KAAIkH,KAAK,CAAC0B,gBAAgB,CAAC5I,IAAI,CAAE,GAAE,CAAC,CAAC2C,IAAI,CAAC,EAAE,CAAC;AACvF;AACA,SAASyH,aAAaA,CAACC,QAAQ,EAAE;EAC7B,MAAMC,SAAS,GAAG,EAAE;EACpBD,QAAQ,CAAC1C,OAAO,CAAE4C,KAAK,IAAK;IACxB,MAAMC,SAAS,GAAGF,SAAS,CAACA,SAAS,CAACtM,MAAM,GAAG,CAAC,CAAC;IACjD,IAAIwM,SAAS,IAAID,KAAK,CAACE,QAAQ,KAAKhB,SAAS,IAAIe,SAAS,CAACC,QAAQ,KAAKhB,SAAS,EAAE;MAC/Ee,SAAS,CAACE,IAAI,IAAIH,KAAK,CAACG,IAAI;IAChC,CAAC,MACI;MACDJ,SAAS,CAACK,IAAI,CAACJ,KAAK,CAAC;IACzB;EACJ,CAAC,CAAC;EACF,OAAOD,SAAS;AACpB;AACA,SAASM,OAAOA,CAACC,SAAS,EAAEC,IAAI,EAAEC,YAAY,EAAEC,YAAY,EAAEC,WAAW,EAAEC,SAAS,GAAG,EAAE,EAAE;EACvF,IAAI,CAACvB,iBAAiB,EAAE;IACpBA,iBAAiB,GAAGlD,QAAQ,CAACK,aAAa,CAAC,KAAK,CAAC;IACjD6C,iBAAiB,CAACwB,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;IACrD1E,QAAQ,CAACO,IAAI,CAACC,WAAW,CAAC0C,iBAAiB,CAAC;EAChD;EACA;EACA,MAAMyB,WAAW,GAAGvF,MAAM,CAACuB,gBAAgB,CAACyD,SAAS,CAAC;EACtD,MAAMQ,SAAS,GAAGpB,aAAa,CAACmB,WAAW,CAAC;EAC5C,MAAMxD,UAAU,GAAGoC,UAAU,CAACoB,WAAW,CAACxD,UAAU,CAAC;EACrD,MAAM0D,SAAS,GAAGxG,IAAI,CAACC,KAAK,CAAC6C,UAAU,IAAIkD,IAAI,GAAG,CAAC,CAAC,GAAGd,UAAU,CAACoB,WAAW,CAACG,UAAU,CAAC,GAAGvB,UAAU,CAACoB,WAAW,CAACI,aAAa,CAAC,CAAC;EAClI;EACA7B,iBAAiB,CAACwB,YAAY,CAAC,OAAO,EAAEE,SAAS,CAAC;EAClD1B,iBAAiB,CAACzC,KAAK,CAACd,QAAQ,GAAG,OAAO;EAC1CuD,iBAAiB,CAACzC,KAAK,CAAC1F,IAAI,GAAG,GAAG;EAClCmI,iBAAiB,CAACzC,KAAK,CAACW,MAAM,GAAG,MAAM;EACvC8B,iBAAiB,CAACzC,KAAK,CAACuE,SAAS,GAAG,MAAM;EAC1C9B,iBAAiB,CAACzC,KAAK,CAACoE,SAAS,GAAG,MAAM;EAC1C3B,iBAAiB,CAACzC,KAAK,CAAC3F,GAAG,GAAG,WAAW;EACzCoI,iBAAiB,CAACzC,KAAK,CAACwE,MAAM,GAAG,OAAO;EACxC;EACA/B,iBAAiB,CAACzC,KAAK,CAACyE,YAAY,GAAG,MAAM;EAC7ChC,iBAAiB,CAACzC,KAAK,CAACM,UAAU,GAAG,QAAQ;EAC7CmC,iBAAiB,CAACzC,KAAK,CAAC0E,eAAe,GAAG,MAAM;EAChD,MAAMC,WAAW,GAAGzB,aAAa,CAACW,YAAY,CAAC;EAC/C,MAAMe,SAAS,GAAGrF,QAAQ,CAACK,aAAa,CAAC,KAAK,CAAC;EAC/C,MAAMiF,gBAAgB,GAAGtF,QAAQ,CAACK,aAAa,CAAC,MAAM,CAAC;EACvD,MAAMkF,eAAe,GAAGvF,QAAQ,CAACwF,cAAc,CAACf,SAAS,CAAC;EAC1D,MAAMgB,cAAc,GAAGzF,QAAQ,CAACK,aAAa,CAAC,MAAM,CAAC;EACrD;EACAlI,MAAM,CAACuN,MAAM,CAACL,SAAS,CAAC5E,KAAK,EAAE0C,YAAY,CAAC;EAC5ChL,MAAM,CAACuN,MAAM,CAACJ,gBAAgB,CAAC7E,KAAK,EAAE0C,YAAY,CAAC;EACnDhL,MAAM,CAACuN,MAAM,CAACD,cAAc,CAAChF,KAAK,EAAE0C,YAAY,CAAC;EACjDiC,WAAW,CAAClE,OAAO,CAACyE,CAAC,IAAI;IACrBL,gBAAgB,CAAC9E,WAAW,CAACmF,CAAC,CAAC;EACnC,CAAC,CAAC;EACFL,gBAAgB,CAAC9E,WAAW,CAAC+E,eAAe,CAAC;EAC7ChB,YAAY,CAACrD,OAAO,CAACnC,IAAI,IAAI;IACzB0G,cAAc,CAACjF,WAAW,CAACzB,IAAI,CAAC6G,SAAS,CAAC,IAAI,CAAC,CAAC;EACpD,CAAC,CAAC;EACFP,SAAS,CAAC7E,WAAW,CAAC8E,gBAAgB,CAAC;EACvCD,SAAS,CAAC7E,WAAW,CAACiF,cAAc,CAAC;EACrC;EACAvC,iBAAiB,CAAC1C,WAAW,CAAC6E,SAAS,CAAC;EACxC;EACA,SAASQ,OAAOA,CAAA,EAAG;IACf,OAAO3C,iBAAiB,CAAC4C,YAAY,GAAGjB,SAAS;EACrD;EACA,IAAIgB,OAAO,CAAC,CAAC,EAAE;IACX,MAAME,IAAI,GAAG7C,iBAAiB,CAAC8C,SAAS;IACxC9C,iBAAiB,CAAC/C,WAAW,CAACkF,SAAS,CAAC;IACxC,OAAO;MAAEf,YAAY;MAAEyB,IAAI;MAAEE,QAAQ,EAAE;IAAM,CAAC;EAClD;EACA;EACA,MAAMC,UAAU,GAAGhP,KAAK,CAACqB,SAAS,CAACX,KAAK,CACnC8L,KAAK,CAACR,iBAAiB,CAACgD,UAAU,CAAC,CAAC,CAAC,CAACA,UAAU,CAAC,CAAC,CAAC,CAACN,SAAS,CAAC,IAAI,CAAC,CAACM,UAAU,CAAC,CAC/EC,MAAM,CAAC,CAAC;IAAEnC;EAAS,CAAC,KAAKA,QAAQ,KAAKf,YAAY,CAAC;EACxD,MAAMmD,UAAU,GAAGlP,KAAK,CAACqB,SAAS,CAACX,KAAK,CAAC8L,KAAK,CAACR,iBAAiB,CAACgD,UAAU,CAAC,CAAC,CAAC,CAACA,UAAU,CAAC,CAAC,CAAC,CAACN,SAAS,CAAC,IAAI,CAAC,CAACM,UAAU,CAAC;EACxHhD,iBAAiB,CAAC/C,WAAW,CAACkF,SAAS,CAAC;EACxC;EACAnC,iBAAiB,CAAC8C,SAAS,GAAG,EAAE;EAChC;EACA,MAAMK,qBAAqB,GAAGrG,QAAQ,CAACK,aAAa,CAAC,MAAM,CAAC;EAC5D6C,iBAAiB,CAAC1C,WAAW,CAAC6F,qBAAqB,CAAC;EACpD,MAAMC,gBAAgB,GAAGtG,QAAQ,CAACwF,cAAc,CAAChB,WAAW,GAAGC,SAAS,CAAC;EACzE4B,qBAAqB,CAAC7F,WAAW,CAAC8F,gBAAgB,CAAC;EACnDF,UAAU,CAAClF,OAAO,CAACqF,SAAS,IAAI;IAC5BrD,iBAAiB,CAAC1C,WAAW,CAAC+F,SAAS,CAAC;EAC5C,CAAC,CAAC;EACF;EACA,SAASC,eAAeA,CAACzH,IAAI,EAAE;IAC3BsH,qBAAqB,CAACI,YAAY,CAAC1H,IAAI,EAAEuH,gBAAgB,CAAC;EAC9D;EACA;EACA,SAASI,WAAWA,CAACC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,GAAG,CAAC,EAAEC,MAAM,GAAGF,QAAQ,CAACrP,MAAM,EAAEwP,cAAc,GAAG,CAAC,EAAE;IACjG,MAAMC,MAAM,GAAG3I,IAAI,CAAC4I,KAAK,CAAC,CAACJ,QAAQ,GAAGC,MAAM,IAAI,CAAC,CAAC;IAClDH,QAAQ,CAACnF,WAAW,GAAGoF,QAAQ,CAAChP,KAAK,CAAC,CAAC,EAAEoP,MAAM,CAAC;IAChD,IAAIH,QAAQ,IAAIC,MAAM,GAAG,CAAC,EAAE;MACxB;MACA,KAAK,IAAII,IAAI,GAAGJ,MAAM,EAAEI,IAAI,IAAIL,QAAQ,EAAEK,IAAI,IAAI,CAAC,EAAE;QACjD,MAAMC,eAAe,GAAGP,QAAQ,CAAChP,KAAK,CAAC,CAAC,EAAEsP,IAAI,CAAC;QAC/CP,QAAQ,CAACnF,WAAW,GAAG2F,eAAe;QACtC,IAAItB,OAAO,CAAC,CAAC,IAAI,CAACsB,eAAe,EAAE;UAC/B,OAAOD,IAAI,KAAKN,QAAQ,CAACrP,MAAM,GACzB;YACE6P,QAAQ,EAAE,KAAK;YACfrI,IAAI,EAAEiB,QAAQ,CAACwF,cAAc,CAACoB,QAAQ;UAC1C,CAAC,GACC;YACEQ,QAAQ,EAAE,IAAI;YACdrI,IAAI,EAAEiB,QAAQ,CAACwF,cAAc,CAAC2B,eAAe;UACjD,CAAC;QACT;MACJ;IACJ;IACA,IAAItB,OAAO,CAAC,CAAC,EAAE;MACX,OAAOa,WAAW,CAACC,QAAQ,EAAEC,QAAQ,EAAEI,MAAM,EAAEF,MAAM,EAAEE,MAAM,CAAC;IAClE,CAAC,MACI;MACD,OAAON,WAAW,CAACC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAEG,MAAM,EAAED,cAAc,CAAC;IAC5E;EACJ;EACA,SAASM,WAAWA,CAACd,SAAS,EAAEe,KAAK,EAAE;IACnC,MAAM7L,IAAI,GAAG8K,SAAS,CAACvC,QAAQ;IAC/B,IAAIvI,IAAI,KAAKsH,YAAY,EAAE;MACvB;MACA;MACA,IAAI8C,OAAO,CAAC,CAAC,EAAE;QACX,OAAO;UACHuB,QAAQ,EAAE,KAAK;UACfrI,IAAI,EAAEqG,WAAW,CAACkC,KAAK;QAC3B,CAAC;MACL;MACA;MACAjB,qBAAqB,CAAClG,WAAW,CAACoG,SAAS,CAAC;MAC5C,OAAO;QACHa,QAAQ,EAAE,IAAI;QACdrI,IAAI,EAAE;MACV,CAAC;IACL,CAAC,MACI,IAAItD,IAAI,KAAKuH,SAAS,EAAE;MACzB,MAAM4D,QAAQ,GAAGL,SAAS,CAAC/E,WAAW,IAAI,EAAE;MAC5C,MAAMmF,QAAQ,GAAG3G,QAAQ,CAACwF,cAAc,CAACoB,QAAQ,CAAC;MAClDJ,eAAe,CAACG,QAAQ,CAAC;MACzB,OAAOD,WAAW,CAACC,QAAQ,EAAEC,QAAQ,CAAC;IAC1C;IACA;IACA;IACA,OAAO;MACHQ,QAAQ,EAAE,KAAK;MACfrI,IAAI,EAAE;IACV,CAAC;EACL;EACA,MAAMwI,aAAa,GAAG,EAAE;EACxBrB,UAAU,CAACvD,IAAI,CAAC,CAAC4D,SAAS,EAAEe,KAAK,KAAK;IAClC,MAAM;MAAEF,QAAQ;MAAErI;IAAK,CAAC,GAAGsI,WAAW,CAACd,SAAS,EAAEe,KAAK,CAAC;IACxD,IAAIvI,IAAI,EAAE;MACNwI,aAAa,CAACrD,IAAI,CAACnF,IAAI,CAAC;IAC5B;IACA,OAAOqI,QAAQ;EACnB,CAAC,CAAC;EACF,MAAMI,MAAM,GAAG;IACXlD,YAAY,EAAEiD,aAAa;IAC3BxB,IAAI,EAAE7C,iBAAiB,CAAC8C,SAAS;IACjCC,QAAQ,EAAE;EACd,CAAC;EACD,OAAO/C,iBAAiB,CAACuE,UAAU,EAAE;IACjCvE,iBAAiB,CAAC/C,WAAW,CAAC+C,iBAAiB,CAACuE,UAAU,CAAC;EAC/D;EACA,OAAOD,MAAM;AACjB;;AAEA;AACA;AACA;AACA;AACA,IAAIE,qBAAqB;AACzB,IAAIC,uBAAuB;AAC3B;AACA,MAAMC,gBAAgB,GAAG;EACrBjI,QAAQ,EAAE,UAAU;EACpB7E,GAAG,EAAE,SAAS;EACdsH,KAAK,EAAE,MAAM;EACbhB,MAAM,EAAE;AACZ,CAAC;AACD,SAASyG,gBAAgBA,CAACC,SAAS,GAAG,UAAU,EAAE/L,MAAM,GAAG,KAAK,EAAE;EAC9D,IAAI,OAAOiE,QAAQ,KAAK,WAAW,IAAI,OAAOZ,MAAM,KAAK,WAAW,EAAE;IAClE,OAAO,CAAC;EACZ;EACA,MAAM2I,UAAU,GAAGD,SAAS,KAAK,UAAU;EAC3C,IAAIC,UAAU,IAAIL,qBAAqB,EAAE;IACrC,OAAOA,qBAAqB;EAChC,CAAC,MACI,IAAI,CAACK,UAAU,IAAIJ,uBAAuB,EAAE;IAC7C,OAAOA,uBAAuB;EAClC;EACA,MAAMK,SAAS,GAAGhI,QAAQ,CAACK,aAAa,CAAC,KAAK,CAAC;EAC/ClI,MAAM,CAACC,IAAI,CAACwP,gBAAgB,CAAC,CAAC1G,OAAO,CAAC+G,UAAU,IAAI;IAChD;IACAD,SAAS,CAACvH,KAAK,CAACwH,UAAU,CAAC,GAAGL,gBAAgB,CAACK,UAAU,CAAC;EAC9D,CAAC,CAAC;EACF;EACAD,SAAS,CAACE,SAAS,GAAI,GAAEnM,MAAO,2CAA0C;EAC1E;EACA,IAAIgM,UAAU,EAAE;IACZC,SAAS,CAACvH,KAAK,CAACa,SAAS,GAAG,QAAQ;EACxC,CAAC,MACI;IACD0G,SAAS,CAACvH,KAAK,CAAC0H,SAAS,GAAG,QAAQ;EACxC;EACAnI,QAAQ,CAACO,IAAI,CAACC,WAAW,CAACwH,SAAS,CAAC;EACpC,IAAII,IAAI,GAAG,CAAC;EACZ,IAAIL,UAAU,EAAE;IACZK,IAAI,GAAGJ,SAAS,CAACK,WAAW,GAAGL,SAAS,CAACM,WAAW;IACpDZ,qBAAqB,GAAGU,IAAI;EAChC,CAAC,MACI;IACDA,IAAI,GAAGJ,SAAS,CAAClC,YAAY,GAAGkC,SAAS,CAACO,YAAY;IACtDZ,uBAAuB,GAAGS,IAAI;EAClC;EACApI,QAAQ,CAACO,IAAI,CAACJ,WAAW,CAAC6H,SAAS,CAAC;EACpC,OAAOI,IAAI;AACf;;AAEA;AACA;AACA;AACA;AACA,SAASI,cAAcA,CAACxR,KAAK,EAAEyR,UAAU,EAAE;EACvC,OAAOzR,KAAK,GAAIA,KAAK,GAAGyR,UAAU,GAAGzR,KAAK,GAAGyR,UAAU,GAAIA,UAAU;AACzE;;AAEA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAAA,EAAG;EAClB,MAAMC,KAAK,GAAG,IAAIjS,OAAO,CAAC,CAAC;EAC3BkS,OAAO,CAACC,OAAO,CAAC,CAAC,CAACzL,IAAI,CAAC,MAAMuL,KAAK,CAACG,IAAI,CAAC,CAAC,CAAC;EAC1C,OAAOH,KAAK,CAACI,IAAI,CAACjS,IAAI,CAAC,CAAC,CAAC,CAAC;AAC9B;;AAEA;AACA;AACA;AACA;AACA,SAASkS,kBAAkBA,CAAChS,KAAK,EAAE;EAC/B,IAAIL,YAAY,CAACK,KAAK,CAAC,EAAE;IACrB,OAAOA,KAAK;EAChB;EACA,IAAIkG,SAAS,CAAClG,KAAK,CAAC,EAAE;IAClB;IACA,OAAOJ,IAAI,CAACgS,OAAO,CAACC,OAAO,CAAC7R,KAAK,CAAC,CAAC;EACvC;EACA,OAAOH,EAAE,CAACG,KAAK,CAAC;AACpB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASiS,SAASA,CAAA,EAAG;EACjB,OAAO,CAAC,EAAE,OAAO7J,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACY,QAAQ,IAAIZ,MAAM,CAACY,QAAQ,CAACK,aAAa,CAAC;AAChG;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM6I,QAAQ,GAAI,aAAY;AAC9B,SAASC,OAAOA,CAAC;EAAEC;AAAK,CAAC,GAAG,CAAC,CAAC,EAAE;EAC5B,IAAIA,IAAI,EAAE;IACN,OAAOA,IAAI,CAAC1N,UAAU,CAAC,OAAO,CAAC,GAAG0N,IAAI,GAAI,QAAOA,IAAK,EAAC;EAC3D;EACA,OAAOF,QAAQ;AACnB;AACA,SAASG,YAAYA,CAACC,MAAM,EAAE;EAC1B,IAAIA,MAAM,CAACC,QAAQ,EAAE;IACjB,OAAOD,MAAM,CAACC,QAAQ;EAC1B;EACA,MAAMC,IAAI,GAAGxJ,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;EAC3C,OAAOuJ,IAAI,IAAIxJ,QAAQ,CAACO,IAAI;AAChC;AACA,SAASkJ,SAASA,CAACC,GAAG,EAAE9J,OAAO,GAAG,CAAC,CAAC,EAAE;EAClC,IAAI,CAACqJ,SAAS,CAAC,CAAC,EAAE;IACd,OAAO,IAAI;EACf;EACA,MAAMU,SAAS,GAAG3J,QAAQ,CAACK,aAAa,CAAC,OAAO,CAAC;EACjD,IAAIT,OAAO,CAACgK,QAAQ,EAAE;IAClBD,SAAS,CAACE,KAAK,GAAGjK,OAAO,CAACgK,QAAQ;EACtC;EACAD,SAAS,CAAC3D,SAAS,GAAG0D,GAAG;EACzB,MAAMrE,SAAS,GAAGgE,YAAY,CAACzJ,OAAO,CAAC;EACvC,MAAM;IAAE6H;EAAW,CAAC,GAAGpC,SAAS;EAChC,IAAIzF,OAAO,CAACkK,OAAO,IAAIzE,SAAS,CAACyE,OAAO,EAAE;IACtC;IACAzE,SAAS,CAACyE,OAAO,CAACH,SAAS,CAAC;EAChC,CAAC,MACI,IAAI/J,OAAO,CAACkK,OAAO,IAAIrC,UAAU,EAAE;IACpC;IACApC,SAAS,CAACoB,YAAY,CAACkD,SAAS,EAAElC,UAAU,CAAC;EACjD,CAAC,MACI;IACDpC,SAAS,CAAC7E,WAAW,CAACmJ,SAAS,CAAC;EACpC;EACA,OAAOA,SAAS;AACpB;AACA,MAAMI,cAAc,GAAG,IAAIC,GAAG,CAAC,CAAC;AAChC,SAASC,aAAaA,CAACtR,GAAG,EAAE2Q,MAAM,GAAG,CAAC,CAAC,EAAE;EACrC,MAAMjE,SAAS,GAAGgE,YAAY,CAACC,MAAM,CAAC;EACtC,OAAOpS,KAAK,CAACN,IAAI,CAACmT,cAAc,CAAC7P,GAAG,CAACmL,SAAS,CAAC,EAAEzB,QAAQ,IAAI,EAAE,CAAC,CAACsG,IAAI,CAACnL,IAAI,IAAIA,IAAI,CAACoL,OAAO,KAAK,OAAO,IAAIpL,IAAI,CAACqL,YAAY,CAACjB,OAAO,CAACG,MAAM,CAAC,CAAC,KAAK3Q,GAAG,CAAC;AACzJ;AACA,SAAS0R,SAASA,CAAC1R,GAAG,EAAE2Q,MAAM,GAAG,CAAC,CAAC,EAAE;EACjC,MAAMgB,SAAS,GAAGL,aAAa,CAACtR,GAAG,EAAE2Q,MAAM,CAAC;EAC5CgB,SAAS,EAAEpK,UAAU,EAAEC,WAAW,CAACmK,SAAS,CAAC;AACjD;AACA,SAASC,SAASA,CAACb,GAAG,EAAE/Q,GAAG,EAAEiH,OAAO,GAAG,CAAC,CAAC,EAAE;EACvC,MAAMyF,SAAS,GAAGgE,YAAY,CAACzJ,OAAO,CAAC;EACvC;EACA,IAAI,CAACmK,cAAc,CAACS,GAAG,CAACnF,SAAS,CAAC,EAAE;IAChC,MAAMoF,gBAAgB,GAAGhB,SAAS,CAAC,EAAE,EAAE7J,OAAO,CAAC;IAC/C;IACA,MAAM;MAAEM;IAAW,CAAC,GAAGuK,gBAAgB;IACvCV,cAAc,CAAC5P,GAAG,CAACkL,SAAS,EAAEnF,UAAU,CAAC;IACzCA,UAAU,CAACC,WAAW,CAACsK,gBAAgB,CAAC;EAC5C;EACA,MAAMH,SAAS,GAAGL,aAAa,CAACtR,GAAG,EAAEiH,OAAO,CAAC;EAC7C,IAAI0K,SAAS,EAAE;IACX,IAAI1K,OAAO,CAACgK,QAAQ,IAAIU,SAAS,CAACT,KAAK,KAAKjK,OAAO,CAACgK,QAAQ,EAAE;MAC1DU,SAAS,CAACT,KAAK,GAAGjK,OAAO,CAACgK,QAAQ;IACtC;IACA,IAAIU,SAAS,CAACtE,SAAS,KAAK0D,GAAG,EAAE;MAC7BY,SAAS,CAACtE,SAAS,GAAG0D,GAAG;IAC7B;IACA,OAAOY,SAAS;EACpB;EACA,MAAMI,OAAO,GAAGjB,SAAS,CAACC,GAAG,EAAE9J,OAAO,CAAC;EACvC8K,OAAO,EAAEhG,YAAY,CAACyE,OAAO,CAACvJ,OAAO,CAAC,EAAEjH,GAAG,CAAC;EAC5C,OAAO+R,OAAO;AAClB;;AAEA;AACA;AACA;AACA;AACA,SAASC,mBAAmBA,CAACC,SAAS,EAAEC,MAAM,EAAEC,WAAW,EAAE;EACzD,OAAO;IACH,CAAE,GAAEF,SAAU,iBAAgB,GAAGC,MAAM,KAAK,SAAS;IACrD,CAAE,GAAED,SAAU,iBAAgB,GAAGC,MAAM,KAAK,SAAS;IACrD,CAAE,GAAED,SAAU,eAAc,GAAGC,MAAM,KAAK,OAAO;IACjD,CAAE,GAAED,SAAU,oBAAmB,GAAGC,MAAM,KAAK,YAAY;IAC3D,CAAE,GAAED,SAAU,eAAc,GAAGE;EACnC,CAAC;AACL;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAAS1Q,YAAY,EAAEC,aAAa,EAAEC,WAAW,EAAElD,WAAW,EAAE6R,SAAS,EAAEhH,cAAc,EAAEuG,cAAc,EAAEzK,mBAAmB,EAAE2B,mBAAmB,EAAE/E,gBAAgB,EAAEgB,gBAAgB,EAAEU,WAAW,EAAEiB,UAAU,EAAEG,YAAY,EAAE3B,SAAS,EAAEiB,kBAAkB,EAAE4N,mBAAmB,EAAE/H,cAAc,EAAE8F,UAAU,EAAEe,SAAS,EAAE3R,KAAK,EAAEc,gBAAgB,EAAEf,QAAQ,EAAEoG,cAAc,EAAEf,SAAS,EAAEqF,cAAc,EAAE1J,aAAa,EAAE0C,YAAY,EAAE4I,OAAO,EAAE0D,gBAAgB,EAAE7K,MAAM,EAAEL,QAAQ,EAAEuC,UAAU,EAAEqE,UAAU,EAAE8G,SAAS,EAAEvL,cAAc,EAAEpH,gBAAgB,EAAEK,YAAY,EAAEwC,WAAW,EAAEiE,GAAG,EAAEzH,OAAO,EAAE+B,SAAS,EAAEI,UAAU,EAAEiF,SAAS,EAAEpF,QAAQ,EAAEwR,SAAS,EAAEpR,iBAAiB,EAAE6P,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}