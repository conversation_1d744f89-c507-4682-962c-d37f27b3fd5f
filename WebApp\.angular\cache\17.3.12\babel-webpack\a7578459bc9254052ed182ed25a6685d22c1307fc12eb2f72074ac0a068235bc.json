{"ast": null, "code": "/**\n * marked v15.0.7 - a markdown parser\n * Copyright (c) 2011-2025, <PERSON>. (MIT Licensed)\n * https://github.com/markedjs/marked\n */\n\n/**\n * DO NOT EDIT THIS FILE\n * The code in this file is generated from files in ./src/\n */\n\n/**\n * Gets the original marked default options.\n */\nfunction _getDefaults() {\n  return {\n    async: false,\n    breaks: false,\n    extensions: null,\n    gfm: true,\n    hooks: null,\n    pedantic: false,\n    renderer: null,\n    silent: false,\n    tokenizer: null,\n    walkTokens: null\n  };\n}\nlet _defaults = _getDefaults();\nfunction changeDefaults(newDefaults) {\n  _defaults = newDefaults;\n}\nconst noopTest = {\n  exec: () => null\n};\nfunction edit(regex, opt = '') {\n  let source = typeof regex === 'string' ? regex : regex.source;\n  const obj = {\n    replace: (name, val) => {\n      let valSource = typeof val === 'string' ? val : val.source;\n      valSource = valSource.replace(other.caret, '$1');\n      source = source.replace(name, valSource);\n      return obj;\n    },\n    getRegex: () => {\n      return new RegExp(source, opt);\n    }\n  };\n  return obj;\n}\nconst other = {\n  codeRemoveIndent: /^(?: {1,4}| {0,3}\\t)/gm,\n  outputLinkReplace: /\\\\([\\[\\]])/g,\n  indentCodeCompensation: /^(\\s+)(?:```)/,\n  beginningSpace: /^\\s+/,\n  endingHash: /#$/,\n  startingSpaceChar: /^ /,\n  endingSpaceChar: / $/,\n  nonSpaceChar: /[^ ]/,\n  newLineCharGlobal: /\\n/g,\n  tabCharGlobal: /\\t/g,\n  multipleSpaceGlobal: /\\s+/g,\n  blankLine: /^[ \\t]*$/,\n  doubleBlankLine: /\\n[ \\t]*\\n[ \\t]*$/,\n  blockquoteStart: /^ {0,3}>/,\n  blockquoteSetextReplace: /\\n {0,3}((?:=+|-+) *)(?=\\n|$)/g,\n  blockquoteSetextReplace2: /^ {0,3}>[ \\t]?/gm,\n  listReplaceTabs: /^\\t+/,\n  listReplaceNesting: /^ {1,4}(?=( {4})*[^ ])/g,\n  listIsTask: /^\\[[ xX]\\] /,\n  listReplaceTask: /^\\[[ xX]\\] +/,\n  anyLine: /\\n.*\\n/,\n  hrefBrackets: /^<(.*)>$/,\n  tableDelimiter: /[:|]/,\n  tableAlignChars: /^\\||\\| *$/g,\n  tableRowBlankLine: /\\n[ \\t]*$/,\n  tableAlignRight: /^ *-+: *$/,\n  tableAlignCenter: /^ *:-+: *$/,\n  tableAlignLeft: /^ *:-+ *$/,\n  startATag: /^<a /i,\n  endATag: /^<\\/a>/i,\n  startPreScriptTag: /^<(pre|code|kbd|script)(\\s|>)/i,\n  endPreScriptTag: /^<\\/(pre|code|kbd|script)(\\s|>)/i,\n  startAngleBracket: /^</,\n  endAngleBracket: />$/,\n  pedanticHrefTitle: /^([^'\"]*[^\\s])\\s+(['\"])(.*)\\2/,\n  unicodeAlphaNumeric: /[\\p{L}\\p{N}]/u,\n  escapeTest: /[&<>\"']/,\n  escapeReplace: /[&<>\"']/g,\n  escapeTestNoEncode: /[<>\"']|&(?!(#\\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\\w+);)/,\n  escapeReplaceNoEncode: /[<>\"']|&(?!(#\\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\\w+);)/g,\n  unescapeTest: /&(#(?:\\d+)|(?:#x[0-9A-Fa-f]+)|(?:\\w+));?/ig,\n  caret: /(^|[^\\[])\\^/g,\n  percentDecode: /%25/g,\n  findPipe: /\\|/g,\n  splitPipe: / \\|/,\n  slashPipe: /\\\\\\|/g,\n  carriageReturn: /\\r\\n|\\r/g,\n  spaceLine: /^ +$/gm,\n  notSpaceStart: /^\\S*/,\n  endingNewline: /\\n$/,\n  listItemRegex: bull => new RegExp(`^( {0,3}${bull})((?:[\\t ][^\\\\n]*)?(?:\\\\n|$))`),\n  nextBulletRegex: indent => new RegExp(`^ {0,${Math.min(3, indent - 1)}}(?:[*+-]|\\\\d{1,9}[.)])((?:[ \\t][^\\\\n]*)?(?:\\\\n|$))`),\n  hrRegex: indent => new RegExp(`^ {0,${Math.min(3, indent - 1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\\\* *){3,})(?:\\\\n+|$)`),\n  fencesBeginRegex: indent => new RegExp(`^ {0,${Math.min(3, indent - 1)}}(?:\\`\\`\\`|~~~)`),\n  headingBeginRegex: indent => new RegExp(`^ {0,${Math.min(3, indent - 1)}}#`),\n  htmlBeginRegex: indent => new RegExp(`^ {0,${Math.min(3, indent - 1)}}<(?:[a-z].*>|!--)`, 'i')\n};\n/**\n * Block-Level Grammar\n */\nconst newline = /^(?:[ \\t]*(?:\\n|$))+/;\nconst blockCode = /^((?: {4}| {0,3}\\t)[^\\n]+(?:\\n(?:[ \\t]*(?:\\n|$))*)?)+/;\nconst fences = /^ {0,3}(`{3,}(?=[^`\\n]*(?:\\n|$))|~{3,})([^\\n]*)(?:\\n|$)(?:|([\\s\\S]*?)(?:\\n|$))(?: {0,3}\\1[~`]* *(?=\\n|$)|$)/;\nconst hr = /^ {0,3}((?:-[\\t ]*){3,}|(?:_[ \\t]*){3,}|(?:\\*[ \\t]*){3,})(?:\\n+|$)/;\nconst heading = /^ {0,3}(#{1,6})(?=\\s|$)(.*)(?:\\n+|$)/;\nconst bullet = /(?:[*+-]|\\d{1,9}[.)])/;\nconst lheadingCore = /^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\\n(?!\\s*?\\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\\n {0,3}(=+|-+) *(?:\\n+|$)/;\nconst lheading = edit(lheadingCore).replace(/bull/g, bullet) // lists can interrupt\n.replace(/blockCode/g, /(?: {4}| {0,3}\\t)/) // indented code blocks can interrupt\n.replace(/fences/g, / {0,3}(?:`{3,}|~{3,})/) // fenced code blocks can interrupt\n.replace(/blockquote/g, / {0,3}>/) // blockquote can interrupt\n.replace(/heading/g, / {0,3}#{1,6}/) // ATX heading can interrupt\n.replace(/html/g, / {0,3}<[^\\n>]+>\\n/) // block html can interrupt\n.replace(/\\|table/g, '') // table not in commonmark\n.getRegex();\nconst lheadingGfm = edit(lheadingCore).replace(/bull/g, bullet) // lists can interrupt\n.replace(/blockCode/g, /(?: {4}| {0,3}\\t)/) // indented code blocks can interrupt\n.replace(/fences/g, / {0,3}(?:`{3,}|~{3,})/) // fenced code blocks can interrupt\n.replace(/blockquote/g, / {0,3}>/) // blockquote can interrupt\n.replace(/heading/g, / {0,3}#{1,6}/) // ATX heading can interrupt\n.replace(/html/g, / {0,3}<[^\\n>]+>\\n/) // block html can interrupt\n.replace(/table/g, / {0,3}\\|?(?:[:\\- ]*\\|)+[\\:\\- ]*\\n/) // table can interrupt\n.getRegex();\nconst _paragraph = /^([^\\n]+(?:\\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\\n)[^\\n]+)*)/;\nconst blockText = /^[^\\n]+/;\nconst _blockLabel = /(?!\\s*\\])(?:\\\\.|[^\\[\\]\\\\])+/;\nconst def = edit(/^ {0,3}\\[(label)\\]: *(?:\\n[ \\t]*)?([^<\\s][^\\s]*|<.*?>)(?:(?: +(?:\\n[ \\t]*)?| *\\n[ \\t]*)(title))? *(?:\\n+|$)/).replace('label', _blockLabel).replace('title', /(?:\"(?:\\\\\"?|[^\"\\\\])*\"|'[^'\\n]*(?:\\n[^'\\n]+)*\\n?'|\\([^()]*\\))/).getRegex();\nconst list = edit(/^( {0,3}bull)([ \\t][^\\n]+?)?(?:\\n|$)/).replace(/bull/g, bullet).getRegex();\nconst _tag = 'address|article|aside|base|basefont|blockquote|body|caption' + '|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption' + '|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe' + '|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option' + '|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title' + '|tr|track|ul';\nconst _comment = /<!--(?:-?>|[\\s\\S]*?(?:-->|$))/;\nconst html = edit('^ {0,3}(?:' // optional indentation\n+ '<(script|pre|style|textarea)[\\\\s>][\\\\s\\\\S]*?(?:</\\\\1>[^\\\\n]*\\\\n+|$)' // (1)\n+ '|comment[^\\\\n]*(\\\\n+|$)' // (2)\n+ '|<\\\\?[\\\\s\\\\S]*?(?:\\\\?>\\\\n*|$)' // (3)\n+ '|<![A-Z][\\\\s\\\\S]*?(?:>\\\\n*|$)' // (4)\n+ '|<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?(?:\\\\]\\\\]>\\\\n*|$)' // (5)\n+ '|</?(tag)(?: +|\\\\n|/?>)[\\\\s\\\\S]*?(?:(?:\\\\n[ \\t]*)+\\\\n|$)' // (6)\n+ '|<(?!script|pre|style|textarea)([a-z][\\\\w-]*)(?:attribute)*? */?>(?=[ \\\\t]*(?:\\\\n|$))[\\\\s\\\\S]*?(?:(?:\\\\n[ \\t]*)+\\\\n|$)' // (7) open tag\n+ '|</(?!script|pre|style|textarea)[a-z][\\\\w-]*\\\\s*>(?=[ \\\\t]*(?:\\\\n|$))[\\\\s\\\\S]*?(?:(?:\\\\n[ \\t]*)+\\\\n|$)' // (7) closing tag\n+ ')', 'i').replace('comment', _comment).replace('tag', _tag).replace('attribute', / +[a-zA-Z:_][\\w.:-]*(?: *= *\"[^\"\\n]*\"| *= *'[^'\\n]*'| *= *[^\\s\"'=<>`]+)?/).getRegex();\nconst paragraph = edit(_paragraph).replace('hr', hr).replace('heading', ' {0,3}#{1,6}(?:\\\\s|$)').replace('|lheading', '') // setext headings don't interrupt commonmark paragraphs\n.replace('|table', '').replace('blockquote', ' {0,3}>').replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n').replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n.replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)').replace('tag', _tag) // pars can be interrupted by type (6) html blocks\n.getRegex();\nconst blockquote = edit(/^( {0,3}> ?(paragraph|[^\\n]*)(?:\\n|$))+/).replace('paragraph', paragraph).getRegex();\n/**\n * Normal Block Grammar\n */\nconst blockNormal = {\n  blockquote,\n  code: blockCode,\n  def,\n  fences,\n  heading,\n  hr,\n  html,\n  lheading,\n  list,\n  newline,\n  paragraph,\n  table: noopTest,\n  text: blockText\n};\n/**\n * GFM Block Grammar\n */\nconst gfmTable = edit('^ *([^\\\\n ].*)\\\\n' // Header\n+ ' {0,3}((?:\\\\| *)?:?-+:? *(?:\\\\| *:?-+:? *)*(?:\\\\| *)?)' // Align\n+ '(?:\\\\n((?:(?! *\\\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\\\n|$))*)\\\\n*|$)') // Cells\n.replace('hr', hr).replace('heading', ' {0,3}#{1,6}(?:\\\\s|$)').replace('blockquote', ' {0,3}>').replace('code', '(?: {4}| {0,3}\\t)[^\\\\n]').replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n').replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n.replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)').replace('tag', _tag) // tables can be interrupted by type (6) html blocks\n.getRegex();\nconst blockGfm = {\n  ...blockNormal,\n  lheading: lheadingGfm,\n  table: gfmTable,\n  paragraph: edit(_paragraph).replace('hr', hr).replace('heading', ' {0,3}#{1,6}(?:\\\\s|$)').replace('|lheading', '') // setext headings don't interrupt commonmark paragraphs\n  .replace('table', gfmTable) // interrupt paragraphs with table\n  .replace('blockquote', ' {0,3}>').replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n').replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n  .replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)').replace('tag', _tag) // pars can be interrupted by type (6) html blocks\n  .getRegex()\n};\n/**\n * Pedantic grammar (original John Gruber's loose markdown specification)\n */\nconst blockPedantic = {\n  ...blockNormal,\n  html: edit('^ *(?:comment *(?:\\\\n|\\\\s*$)' + '|<(tag)[\\\\s\\\\S]+?</\\\\1> *(?:\\\\n{2,}|\\\\s*$)' // closed tag\n  + '|<tag(?:\"[^\"]*\"|\\'[^\\']*\\'|\\\\s[^\\'\"/>\\\\s]*)*?/?> *(?:\\\\n{2,}|\\\\s*$))').replace('comment', _comment).replace(/tag/g, '(?!(?:' + 'a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub' + '|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)' + '\\\\b)\\\\w+(?!:|[^\\\\w\\\\s@]*@)\\\\b').getRegex(),\n  def: /^ *\\[([^\\]]+)\\]: *<?([^\\s>]+)>?(?: +([\"(][^\\n]+[\")]))? *(?:\\n+|$)/,\n  heading: /^(#{1,6})(.*)(?:\\n+|$)/,\n  fences: noopTest,\n  // fences not supported\n  lheading: /^(.+?)\\n {0,3}(=+|-+) *(?:\\n+|$)/,\n  paragraph: edit(_paragraph).replace('hr', hr).replace('heading', ' *#{1,6} *[^\\n]').replace('lheading', lheading).replace('|table', '').replace('blockquote', ' {0,3}>').replace('|fences', '').replace('|list', '').replace('|html', '').replace('|tag', '').getRegex()\n};\n/**\n * Inline-Level Grammar\n */\nconst escape$1 = /^\\\\([!\"#$%&'()*+,\\-./:;<=>?@\\[\\]\\\\^_`{|}~])/;\nconst inlineCode = /^(`+)([^`]|[^`][\\s\\S]*?[^`])\\1(?!`)/;\nconst br = /^( {2,}|\\\\)\\n(?!\\s*$)/;\nconst inlineText = /^(`+|[^`])(?:(?= {2,}\\n)|[\\s\\S]*?(?:(?=[\\\\<!\\[`*_]|\\b_|$)|[^ ](?= {2,}\\n)))/;\n// list of unicode punctuation marks, plus any missing characters from CommonMark spec\nconst _punctuation = /[\\p{P}\\p{S}]/u;\nconst _punctuationOrSpace = /[\\s\\p{P}\\p{S}]/u;\nconst _notPunctuationOrSpace = /[^\\s\\p{P}\\p{S}]/u;\nconst punctuation = edit(/^((?![*_])punctSpace)/, 'u').replace(/punctSpace/g, _punctuationOrSpace).getRegex();\n// GFM allows ~ inside strong and em for strikethrough\nconst _punctuationGfmStrongEm = /(?!~)[\\p{P}\\p{S}]/u;\nconst _punctuationOrSpaceGfmStrongEm = /(?!~)[\\s\\p{P}\\p{S}]/u;\nconst _notPunctuationOrSpaceGfmStrongEm = /(?:[^\\s\\p{P}\\p{S}]|~)/u;\n// sequences em should skip over [title](link), `code`, <html>\nconst blockSkip = /\\[[^[\\]]*?\\]\\((?:\\\\.|[^\\\\\\(\\)]|\\((?:\\\\.|[^\\\\\\(\\)])*\\))*\\)|`[^`]*?`|<[^<>]*?>/g;\nconst emStrongLDelimCore = /^(?:\\*+(?:((?!\\*)punct)|[^\\s*]))|^_+(?:((?!_)punct)|([^\\s_]))/;\nconst emStrongLDelim = edit(emStrongLDelimCore, 'u').replace(/punct/g, _punctuation).getRegex();\nconst emStrongLDelimGfm = edit(emStrongLDelimCore, 'u').replace(/punct/g, _punctuationGfmStrongEm).getRegex();\nconst emStrongRDelimAstCore = '^[^_*]*?__[^_*]*?\\\\*[^_*]*?(?=__)' // Skip orphan inside strong\n+ '|[^*]+(?=[^*])' // Consume to delim\n+ '|(?!\\\\*)punct(\\\\*+)(?=[\\\\s]|$)' // (1) #*** can only be a Right Delimiter\n+ '|notPunctSpace(\\\\*+)(?!\\\\*)(?=punctSpace|$)' // (2) a***#, a*** can only be a Right Delimiter\n+ '|(?!\\\\*)punctSpace(\\\\*+)(?=notPunctSpace)' // (3) #***a, ***a can only be Left Delimiter\n+ '|[\\\\s](\\\\*+)(?!\\\\*)(?=punct)' // (4) ***# can only be Left Delimiter\n+ '|(?!\\\\*)punct(\\\\*+)(?!\\\\*)(?=punct)' // (5) #***# can be either Left or Right Delimiter\n+ '|notPunctSpace(\\\\*+)(?=notPunctSpace)'; // (6) a***a can be either Left or Right Delimiter\nconst emStrongRDelimAst = edit(emStrongRDelimAstCore, 'gu').replace(/notPunctSpace/g, _notPunctuationOrSpace).replace(/punctSpace/g, _punctuationOrSpace).replace(/punct/g, _punctuation).getRegex();\nconst emStrongRDelimAstGfm = edit(emStrongRDelimAstCore, 'gu').replace(/notPunctSpace/g, _notPunctuationOrSpaceGfmStrongEm).replace(/punctSpace/g, _punctuationOrSpaceGfmStrongEm).replace(/punct/g, _punctuationGfmStrongEm).getRegex();\n// (6) Not allowed for _\nconst emStrongRDelimUnd = edit('^[^_*]*?\\\\*\\\\*[^_*]*?_[^_*]*?(?=\\\\*\\\\*)' // Skip orphan inside strong\n+ '|[^_]+(?=[^_])' // Consume to delim\n+ '|(?!_)punct(_+)(?=[\\\\s]|$)' // (1) #___ can only be a Right Delimiter\n+ '|notPunctSpace(_+)(?!_)(?=punctSpace|$)' // (2) a___#, a___ can only be a Right Delimiter\n+ '|(?!_)punctSpace(_+)(?=notPunctSpace)' // (3) #___a, ___a can only be Left Delimiter\n+ '|[\\\\s](_+)(?!_)(?=punct)' // (4) ___# can only be Left Delimiter\n+ '|(?!_)punct(_+)(?!_)(?=punct)', 'gu') // (5) #___# can be either Left or Right Delimiter\n.replace(/notPunctSpace/g, _notPunctuationOrSpace).replace(/punctSpace/g, _punctuationOrSpace).replace(/punct/g, _punctuation).getRegex();\nconst anyPunctuation = edit(/\\\\(punct)/, 'gu').replace(/punct/g, _punctuation).getRegex();\nconst autolink = edit(/^<(scheme:[^\\s\\x00-\\x1f<>]*|email)>/).replace('scheme', /[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace('email', /[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex();\nconst _inlineComment = edit(_comment).replace('(?:-->|$)', '-->').getRegex();\nconst tag = edit('^comment' + '|^</[a-zA-Z][\\\\w:-]*\\\\s*>' // self-closing tag\n+ '|^<[a-zA-Z][\\\\w-]*(?:attribute)*?\\\\s*/?>' // open tag\n+ '|^<\\\\?[\\\\s\\\\S]*?\\\\?>' // processing instruction, e.g. <?php ?>\n+ '|^<![a-zA-Z]+\\\\s[\\\\s\\\\S]*?>' // declaration, e.g. <!DOCTYPE html>\n+ '|^<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?\\\\]\\\\]>') // CDATA section\n.replace('comment', _inlineComment).replace('attribute', /\\s+[a-zA-Z:_][\\w.:-]*(?:\\s*=\\s*\"[^\"]*\"|\\s*=\\s*'[^']*'|\\s*=\\s*[^\\s\"'=<>`]+)?/).getRegex();\nconst _inlineLabel = /(?:\\[(?:\\\\.|[^\\[\\]\\\\])*\\]|\\\\.|`[^`]*`|[^\\[\\]\\\\`])*?/;\nconst link = edit(/^!?\\[(label)\\]\\(\\s*(href)(?:\\s+(title))?\\s*\\)/).replace('label', _inlineLabel).replace('href', /<(?:\\\\.|[^\\n<>\\\\])+>|[^\\s\\x00-\\x1f]*/).replace('title', /\"(?:\\\\\"?|[^\"\\\\])*\"|'(?:\\\\'?|[^'\\\\])*'|\\((?:\\\\\\)?|[^)\\\\])*\\)/).getRegex();\nconst reflink = edit(/^!?\\[(label)\\]\\[(ref)\\]/).replace('label', _inlineLabel).replace('ref', _blockLabel).getRegex();\nconst nolink = edit(/^!?\\[(ref)\\](?:\\[\\])?/).replace('ref', _blockLabel).getRegex();\nconst reflinkSearch = edit('reflink|nolink(?!\\\\()', 'g').replace('reflink', reflink).replace('nolink', nolink).getRegex();\n/**\n * Normal Inline Grammar\n */\nconst inlineNormal = {\n  _backpedal: noopTest,\n  // only used for GFM url\n  anyPunctuation,\n  autolink,\n  blockSkip,\n  br,\n  code: inlineCode,\n  del: noopTest,\n  emStrongLDelim,\n  emStrongRDelimAst,\n  emStrongRDelimUnd,\n  escape: escape$1,\n  link,\n  nolink,\n  punctuation,\n  reflink,\n  reflinkSearch,\n  tag,\n  text: inlineText,\n  url: noopTest\n};\n/**\n * Pedantic Inline Grammar\n */\nconst inlinePedantic = {\n  ...inlineNormal,\n  link: edit(/^!?\\[(label)\\]\\((.*?)\\)/).replace('label', _inlineLabel).getRegex(),\n  reflink: edit(/^!?\\[(label)\\]\\s*\\[([^\\]]*)\\]/).replace('label', _inlineLabel).getRegex()\n};\n/**\n * GFM Inline Grammar\n */\nconst inlineGfm = {\n  ...inlineNormal,\n  emStrongRDelimAst: emStrongRDelimAstGfm,\n  emStrongLDelim: emStrongLDelimGfm,\n  url: edit(/^((?:ftp|https?):\\/\\/|www\\.)(?:[a-zA-Z0-9\\-]+\\.?)+[^\\s<]*|^email/, 'i').replace('email', /[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),\n  _backpedal: /(?:[^?!.,:;*_'\"~()&]+|\\([^)]*\\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'\"~)]+(?!$))+/,\n  del: /^(~~?)(?=[^\\s~])((?:\\\\.|[^\\\\])*?(?:\\\\.|[^\\s~\\\\]))\\1(?=[^~]|$)/,\n  text: /^([`~]+|[^`~])(?:(?= {2,}\\n)|(?=[a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-]+@)|[\\s\\S]*?(?:(?=[\\\\<!\\[`*~_]|\\b_|https?:\\/\\/|ftp:\\/\\/|www\\.|$)|[^ ](?= {2,}\\n)|[^a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-](?=[a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-]+@)))/\n};\n/**\n * GFM + Line Breaks Inline Grammar\n */\nconst inlineBreaks = {\n  ...inlineGfm,\n  br: edit(br).replace('{2,}', '*').getRegex(),\n  text: edit(inlineGfm.text).replace('\\\\b_', '\\\\b_| {2,}\\\\n').replace(/\\{2,\\}/g, '*').getRegex()\n};\n/**\n * exports\n */\nconst block = {\n  normal: blockNormal,\n  gfm: blockGfm,\n  pedantic: blockPedantic\n};\nconst inline = {\n  normal: inlineNormal,\n  gfm: inlineGfm,\n  breaks: inlineBreaks,\n  pedantic: inlinePedantic\n};\n\n/**\n * Helpers\n */\nconst escapeReplacements = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;',\n  \"'\": '&#39;'\n};\nconst getEscapeReplacement = ch => escapeReplacements[ch];\nfunction escape(html, encode) {\n  if (encode) {\n    if (other.escapeTest.test(html)) {\n      return html.replace(other.escapeReplace, getEscapeReplacement);\n    }\n  } else {\n    if (other.escapeTestNoEncode.test(html)) {\n      return html.replace(other.escapeReplaceNoEncode, getEscapeReplacement);\n    }\n  }\n  return html;\n}\nfunction cleanUrl(href) {\n  try {\n    href = encodeURI(href).replace(other.percentDecode, '%');\n  } catch {\n    return null;\n  }\n  return href;\n}\nfunction splitCells(tableRow, count) {\n  // ensure that every cell-delimiting pipe has a space\n  // before it to distinguish it from an escaped pipe\n  const row = tableRow.replace(other.findPipe, (match, offset, str) => {\n      let escaped = false;\n      let curr = offset;\n      while (--curr >= 0 && str[curr] === '\\\\') escaped = !escaped;\n      if (escaped) {\n        // odd number of slashes means | is escaped\n        // so we leave it alone\n        return '|';\n      } else {\n        // add space before unescaped |\n        return ' |';\n      }\n    }),\n    cells = row.split(other.splitPipe);\n  let i = 0;\n  // First/last cell in a row cannot be empty if it has no leading/trailing pipe\n  if (!cells[0].trim()) {\n    cells.shift();\n  }\n  if (cells.length > 0 && !cells.at(-1)?.trim()) {\n    cells.pop();\n  }\n  if (count) {\n    if (cells.length > count) {\n      cells.splice(count);\n    } else {\n      while (cells.length < count) cells.push('');\n    }\n  }\n  for (; i < cells.length; i++) {\n    // leading or trailing whitespace is ignored per the gfm spec\n    cells[i] = cells[i].trim().replace(other.slashPipe, '|');\n  }\n  return cells;\n}\n/**\n * Remove trailing 'c's. Equivalent to str.replace(/c*$/, '').\n * /c*$/ is vulnerable to REDOS.\n *\n * @param str\n * @param c\n * @param invert Remove suffix of non-c chars instead. Default falsey.\n */\nfunction rtrim(str, c, invert) {\n  const l = str.length;\n  if (l === 0) {\n    return '';\n  }\n  // Length of suffix matching the invert condition.\n  let suffLen = 0;\n  // Step left until we fail to match the invert condition.\n  while (suffLen < l) {\n    const currChar = str.charAt(l - suffLen - 1);\n    if (currChar === c && true) {\n      suffLen++;\n    } else {\n      break;\n    }\n  }\n  return str.slice(0, l - suffLen);\n}\nfunction findClosingBracket(str, b) {\n  if (str.indexOf(b[1]) === -1) {\n    return -1;\n  }\n  let level = 0;\n  for (let i = 0; i < str.length; i++) {\n    if (str[i] === '\\\\') {\n      i++;\n    } else if (str[i] === b[0]) {\n      level++;\n    } else if (str[i] === b[1]) {\n      level--;\n      if (level < 0) {\n        return i;\n      }\n    }\n  }\n  return -1;\n}\nfunction outputLink(cap, link, raw, lexer, rules) {\n  const href = link.href;\n  const title = link.title || null;\n  const text = cap[1].replace(rules.other.outputLinkReplace, '$1');\n  if (cap[0].charAt(0) !== '!') {\n    lexer.state.inLink = true;\n    const token = {\n      type: 'link',\n      raw,\n      href,\n      title,\n      text,\n      tokens: lexer.inlineTokens(text)\n    };\n    lexer.state.inLink = false;\n    return token;\n  }\n  return {\n    type: 'image',\n    raw,\n    href,\n    title,\n    text\n  };\n}\nfunction indentCodeCompensation(raw, text, rules) {\n  const matchIndentToCode = raw.match(rules.other.indentCodeCompensation);\n  if (matchIndentToCode === null) {\n    return text;\n  }\n  const indentToCode = matchIndentToCode[1];\n  return text.split('\\n').map(node => {\n    const matchIndentInNode = node.match(rules.other.beginningSpace);\n    if (matchIndentInNode === null) {\n      return node;\n    }\n    const [indentInNode] = matchIndentInNode;\n    if (indentInNode.length >= indentToCode.length) {\n      return node.slice(indentToCode.length);\n    }\n    return node;\n  }).join('\\n');\n}\n/**\n * Tokenizer\n */\nclass _Tokenizer {\n  options;\n  rules; // set by the lexer\n  lexer; // set by the lexer\n  constructor(options) {\n    this.options = options || _defaults;\n  }\n  space(src) {\n    const cap = this.rules.block.newline.exec(src);\n    if (cap && cap[0].length > 0) {\n      return {\n        type: 'space',\n        raw: cap[0]\n      };\n    }\n  }\n  code(src) {\n    const cap = this.rules.block.code.exec(src);\n    if (cap) {\n      const text = cap[0].replace(this.rules.other.codeRemoveIndent, '');\n      return {\n        type: 'code',\n        raw: cap[0],\n        codeBlockStyle: 'indented',\n        text: !this.options.pedantic ? rtrim(text, '\\n') : text\n      };\n    }\n  }\n  fences(src) {\n    const cap = this.rules.block.fences.exec(src);\n    if (cap) {\n      const raw = cap[0];\n      const text = indentCodeCompensation(raw, cap[3] || '', this.rules);\n      return {\n        type: 'code',\n        raw,\n        lang: cap[2] ? cap[2].trim().replace(this.rules.inline.anyPunctuation, '$1') : cap[2],\n        text\n      };\n    }\n  }\n  heading(src) {\n    const cap = this.rules.block.heading.exec(src);\n    if (cap) {\n      let text = cap[2].trim();\n      // remove trailing #s\n      if (this.rules.other.endingHash.test(text)) {\n        const trimmed = rtrim(text, '#');\n        if (this.options.pedantic) {\n          text = trimmed.trim();\n        } else if (!trimmed || this.rules.other.endingSpaceChar.test(trimmed)) {\n          // CommonMark requires space before trailing #s\n          text = trimmed.trim();\n        }\n      }\n      return {\n        type: 'heading',\n        raw: cap[0],\n        depth: cap[1].length,\n        text,\n        tokens: this.lexer.inline(text)\n      };\n    }\n  }\n  hr(src) {\n    const cap = this.rules.block.hr.exec(src);\n    if (cap) {\n      return {\n        type: 'hr',\n        raw: rtrim(cap[0], '\\n')\n      };\n    }\n  }\n  blockquote(src) {\n    const cap = this.rules.block.blockquote.exec(src);\n    if (cap) {\n      let lines = rtrim(cap[0], '\\n').split('\\n');\n      let raw = '';\n      let text = '';\n      const tokens = [];\n      while (lines.length > 0) {\n        let inBlockquote = false;\n        const currentLines = [];\n        let i;\n        for (i = 0; i < lines.length; i++) {\n          // get lines up to a continuation\n          if (this.rules.other.blockquoteStart.test(lines[i])) {\n            currentLines.push(lines[i]);\n            inBlockquote = true;\n          } else if (!inBlockquote) {\n            currentLines.push(lines[i]);\n          } else {\n            break;\n          }\n        }\n        lines = lines.slice(i);\n        const currentRaw = currentLines.join('\\n');\n        const currentText = currentRaw\n        // precede setext continuation with 4 spaces so it isn't a setext\n        .replace(this.rules.other.blockquoteSetextReplace, '\\n    $1').replace(this.rules.other.blockquoteSetextReplace2, '');\n        raw = raw ? `${raw}\\n${currentRaw}` : currentRaw;\n        text = text ? `${text}\\n${currentText}` : currentText;\n        // parse blockquote lines as top level tokens\n        // merge paragraphs if this is a continuation\n        const top = this.lexer.state.top;\n        this.lexer.state.top = true;\n        this.lexer.blockTokens(currentText, tokens, true);\n        this.lexer.state.top = top;\n        // if there is no continuation then we are done\n        if (lines.length === 0) {\n          break;\n        }\n        const lastToken = tokens.at(-1);\n        if (lastToken?.type === 'code') {\n          // blockquote continuation cannot be preceded by a code block\n          break;\n        } else if (lastToken?.type === 'blockquote') {\n          // include continuation in nested blockquote\n          const oldToken = lastToken;\n          const newText = oldToken.raw + '\\n' + lines.join('\\n');\n          const newToken = this.blockquote(newText);\n          tokens[tokens.length - 1] = newToken;\n          raw = raw.substring(0, raw.length - oldToken.raw.length) + newToken.raw;\n          text = text.substring(0, text.length - oldToken.text.length) + newToken.text;\n          break;\n        } else if (lastToken?.type === 'list') {\n          // include continuation in nested list\n          const oldToken = lastToken;\n          const newText = oldToken.raw + '\\n' + lines.join('\\n');\n          const newToken = this.list(newText);\n          tokens[tokens.length - 1] = newToken;\n          raw = raw.substring(0, raw.length - lastToken.raw.length) + newToken.raw;\n          text = text.substring(0, text.length - oldToken.raw.length) + newToken.raw;\n          lines = newText.substring(tokens.at(-1).raw.length).split('\\n');\n          continue;\n        }\n      }\n      return {\n        type: 'blockquote',\n        raw,\n        tokens,\n        text\n      };\n    }\n  }\n  list(src) {\n    let cap = this.rules.block.list.exec(src);\n    if (cap) {\n      let bull = cap[1].trim();\n      const isordered = bull.length > 1;\n      const list = {\n        type: 'list',\n        raw: '',\n        ordered: isordered,\n        start: isordered ? +bull.slice(0, -1) : '',\n        loose: false,\n        items: []\n      };\n      bull = isordered ? `\\\\d{1,9}\\\\${bull.slice(-1)}` : `\\\\${bull}`;\n      if (this.options.pedantic) {\n        bull = isordered ? bull : '[*+-]';\n      }\n      // Get next list item\n      const itemRegex = this.rules.other.listItemRegex(bull);\n      let endsWithBlankLine = false;\n      // Check if current bullet point can start a new List Item\n      while (src) {\n        let endEarly = false;\n        let raw = '';\n        let itemContents = '';\n        if (!(cap = itemRegex.exec(src))) {\n          break;\n        }\n        if (this.rules.block.hr.test(src)) {\n          // End list if bullet was actually HR (possibly move into itemRegex?)\n          break;\n        }\n        raw = cap[0];\n        src = src.substring(raw.length);\n        let line = cap[2].split('\\n', 1)[0].replace(this.rules.other.listReplaceTabs, t => ' '.repeat(3 * t.length));\n        let nextLine = src.split('\\n', 1)[0];\n        let blankLine = !line.trim();\n        let indent = 0;\n        if (this.options.pedantic) {\n          indent = 2;\n          itemContents = line.trimStart();\n        } else if (blankLine) {\n          indent = cap[1].length + 1;\n        } else {\n          indent = cap[2].search(this.rules.other.nonSpaceChar); // Find first non-space char\n          indent = indent > 4 ? 1 : indent; // Treat indented code blocks (> 4 spaces) as having only 1 indent\n          itemContents = line.slice(indent);\n          indent += cap[1].length;\n        }\n        if (blankLine && this.rules.other.blankLine.test(nextLine)) {\n          // Items begin with at most one blank line\n          raw += nextLine + '\\n';\n          src = src.substring(nextLine.length + 1);\n          endEarly = true;\n        }\n        if (!endEarly) {\n          const nextBulletRegex = this.rules.other.nextBulletRegex(indent);\n          const hrRegex = this.rules.other.hrRegex(indent);\n          const fencesBeginRegex = this.rules.other.fencesBeginRegex(indent);\n          const headingBeginRegex = this.rules.other.headingBeginRegex(indent);\n          const htmlBeginRegex = this.rules.other.htmlBeginRegex(indent);\n          // Check if following lines should be included in List Item\n          while (src) {\n            const rawLine = src.split('\\n', 1)[0];\n            let nextLineWithoutTabs;\n            nextLine = rawLine;\n            // Re-align to follow commonmark nesting rules\n            if (this.options.pedantic) {\n              nextLine = nextLine.replace(this.rules.other.listReplaceNesting, '  ');\n              nextLineWithoutTabs = nextLine;\n            } else {\n              nextLineWithoutTabs = nextLine.replace(this.rules.other.tabCharGlobal, '    ');\n            }\n            // End list item if found code fences\n            if (fencesBeginRegex.test(nextLine)) {\n              break;\n            }\n            // End list item if found start of new heading\n            if (headingBeginRegex.test(nextLine)) {\n              break;\n            }\n            // End list item if found start of html block\n            if (htmlBeginRegex.test(nextLine)) {\n              break;\n            }\n            // End list item if found start of new bullet\n            if (nextBulletRegex.test(nextLine)) {\n              break;\n            }\n            // Horizontal rule found\n            if (hrRegex.test(nextLine)) {\n              break;\n            }\n            if (nextLineWithoutTabs.search(this.rules.other.nonSpaceChar) >= indent || !nextLine.trim()) {\n              // Dedent if possible\n              itemContents += '\\n' + nextLineWithoutTabs.slice(indent);\n            } else {\n              // not enough indentation\n              if (blankLine) {\n                break;\n              }\n              // paragraph continuation unless last line was a different block level element\n              if (line.replace(this.rules.other.tabCharGlobal, '    ').search(this.rules.other.nonSpaceChar) >= 4) {\n                // indented code block\n                break;\n              }\n              if (fencesBeginRegex.test(line)) {\n                break;\n              }\n              if (headingBeginRegex.test(line)) {\n                break;\n              }\n              if (hrRegex.test(line)) {\n                break;\n              }\n              itemContents += '\\n' + nextLine;\n            }\n            if (!blankLine && !nextLine.trim()) {\n              // Check if current line is blank\n              blankLine = true;\n            }\n            raw += rawLine + '\\n';\n            src = src.substring(rawLine.length + 1);\n            line = nextLineWithoutTabs.slice(indent);\n          }\n        }\n        if (!list.loose) {\n          // If the previous item ended with a blank line, the list is loose\n          if (endsWithBlankLine) {\n            list.loose = true;\n          } else if (this.rules.other.doubleBlankLine.test(raw)) {\n            endsWithBlankLine = true;\n          }\n        }\n        let istask = null;\n        let ischecked;\n        // Check for task list items\n        if (this.options.gfm) {\n          istask = this.rules.other.listIsTask.exec(itemContents);\n          if (istask) {\n            ischecked = istask[0] !== '[ ] ';\n            itemContents = itemContents.replace(this.rules.other.listReplaceTask, '');\n          }\n        }\n        list.items.push({\n          type: 'list_item',\n          raw,\n          task: !!istask,\n          checked: ischecked,\n          loose: false,\n          text: itemContents,\n          tokens: []\n        });\n        list.raw += raw;\n      }\n      // Do not consume newlines at end of final item. Alternatively, make itemRegex *start* with any newlines to simplify/speed up endsWithBlankLine logic\n      const lastItem = list.items.at(-1);\n      if (lastItem) {\n        lastItem.raw = lastItem.raw.trimEnd();\n        lastItem.text = lastItem.text.trimEnd();\n      } else {\n        // not a list since there were no items\n        return;\n      }\n      list.raw = list.raw.trimEnd();\n      // Item child tokens handled here at end because we needed to have the final item to trim it first\n      for (let i = 0; i < list.items.length; i++) {\n        this.lexer.state.top = false;\n        list.items[i].tokens = this.lexer.blockTokens(list.items[i].text, []);\n        if (!list.loose) {\n          // Check if list should be loose\n          const spacers = list.items[i].tokens.filter(t => t.type === 'space');\n          const hasMultipleLineBreaks = spacers.length > 0 && spacers.some(t => this.rules.other.anyLine.test(t.raw));\n          list.loose = hasMultipleLineBreaks;\n        }\n      }\n      // Set all items to loose if list is loose\n      if (list.loose) {\n        for (let i = 0; i < list.items.length; i++) {\n          list.items[i].loose = true;\n        }\n      }\n      return list;\n    }\n  }\n  html(src) {\n    const cap = this.rules.block.html.exec(src);\n    if (cap) {\n      const token = {\n        type: 'html',\n        block: true,\n        raw: cap[0],\n        pre: cap[1] === 'pre' || cap[1] === 'script' || cap[1] === 'style',\n        text: cap[0]\n      };\n      return token;\n    }\n  }\n  def(src) {\n    const cap = this.rules.block.def.exec(src);\n    if (cap) {\n      const tag = cap[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal, ' ');\n      const href = cap[2] ? cap[2].replace(this.rules.other.hrefBrackets, '$1').replace(this.rules.inline.anyPunctuation, '$1') : '';\n      const title = cap[3] ? cap[3].substring(1, cap[3].length - 1).replace(this.rules.inline.anyPunctuation, '$1') : cap[3];\n      return {\n        type: 'def',\n        tag,\n        raw: cap[0],\n        href,\n        title\n      };\n    }\n  }\n  table(src) {\n    const cap = this.rules.block.table.exec(src);\n    if (!cap) {\n      return;\n    }\n    if (!this.rules.other.tableDelimiter.test(cap[2])) {\n      // delimiter row must have a pipe (|) or colon (:) otherwise it is a setext heading\n      return;\n    }\n    const headers = splitCells(cap[1]);\n    const aligns = cap[2].replace(this.rules.other.tableAlignChars, '').split('|');\n    const rows = cap[3]?.trim() ? cap[3].replace(this.rules.other.tableRowBlankLine, '').split('\\n') : [];\n    const item = {\n      type: 'table',\n      raw: cap[0],\n      header: [],\n      align: [],\n      rows: []\n    };\n    if (headers.length !== aligns.length) {\n      // header and align columns must be equal, rows can be different.\n      return;\n    }\n    for (const align of aligns) {\n      if (this.rules.other.tableAlignRight.test(align)) {\n        item.align.push('right');\n      } else if (this.rules.other.tableAlignCenter.test(align)) {\n        item.align.push('center');\n      } else if (this.rules.other.tableAlignLeft.test(align)) {\n        item.align.push('left');\n      } else {\n        item.align.push(null);\n      }\n    }\n    for (let i = 0; i < headers.length; i++) {\n      item.header.push({\n        text: headers[i],\n        tokens: this.lexer.inline(headers[i]),\n        header: true,\n        align: item.align[i]\n      });\n    }\n    for (const row of rows) {\n      item.rows.push(splitCells(row, item.header.length).map((cell, i) => {\n        return {\n          text: cell,\n          tokens: this.lexer.inline(cell),\n          header: false,\n          align: item.align[i]\n        };\n      }));\n    }\n    return item;\n  }\n  lheading(src) {\n    const cap = this.rules.block.lheading.exec(src);\n    if (cap) {\n      return {\n        type: 'heading',\n        raw: cap[0],\n        depth: cap[2].charAt(0) === '=' ? 1 : 2,\n        text: cap[1],\n        tokens: this.lexer.inline(cap[1])\n      };\n    }\n  }\n  paragraph(src) {\n    const cap = this.rules.block.paragraph.exec(src);\n    if (cap) {\n      const text = cap[1].charAt(cap[1].length - 1) === '\\n' ? cap[1].slice(0, -1) : cap[1];\n      return {\n        type: 'paragraph',\n        raw: cap[0],\n        text,\n        tokens: this.lexer.inline(text)\n      };\n    }\n  }\n  text(src) {\n    const cap = this.rules.block.text.exec(src);\n    if (cap) {\n      return {\n        type: 'text',\n        raw: cap[0],\n        text: cap[0],\n        tokens: this.lexer.inline(cap[0])\n      };\n    }\n  }\n  escape(src) {\n    const cap = this.rules.inline.escape.exec(src);\n    if (cap) {\n      return {\n        type: 'escape',\n        raw: cap[0],\n        text: cap[1]\n      };\n    }\n  }\n  tag(src) {\n    const cap = this.rules.inline.tag.exec(src);\n    if (cap) {\n      if (!this.lexer.state.inLink && this.rules.other.startATag.test(cap[0])) {\n        this.lexer.state.inLink = true;\n      } else if (this.lexer.state.inLink && this.rules.other.endATag.test(cap[0])) {\n        this.lexer.state.inLink = false;\n      }\n      if (!this.lexer.state.inRawBlock && this.rules.other.startPreScriptTag.test(cap[0])) {\n        this.lexer.state.inRawBlock = true;\n      } else if (this.lexer.state.inRawBlock && this.rules.other.endPreScriptTag.test(cap[0])) {\n        this.lexer.state.inRawBlock = false;\n      }\n      return {\n        type: 'html',\n        raw: cap[0],\n        inLink: this.lexer.state.inLink,\n        inRawBlock: this.lexer.state.inRawBlock,\n        block: false,\n        text: cap[0]\n      };\n    }\n  }\n  link(src) {\n    const cap = this.rules.inline.link.exec(src);\n    if (cap) {\n      const trimmedUrl = cap[2].trim();\n      if (!this.options.pedantic && this.rules.other.startAngleBracket.test(trimmedUrl)) {\n        // commonmark requires matching angle brackets\n        if (!this.rules.other.endAngleBracket.test(trimmedUrl)) {\n          return;\n        }\n        // ending angle bracket cannot be escaped\n        const rtrimSlash = rtrim(trimmedUrl.slice(0, -1), '\\\\');\n        if ((trimmedUrl.length - rtrimSlash.length) % 2 === 0) {\n          return;\n        }\n      } else {\n        // find closing parenthesis\n        const lastParenIndex = findClosingBracket(cap[2], '()');\n        if (lastParenIndex > -1) {\n          const start = cap[0].indexOf('!') === 0 ? 5 : 4;\n          const linkLen = start + cap[1].length + lastParenIndex;\n          cap[2] = cap[2].substring(0, lastParenIndex);\n          cap[0] = cap[0].substring(0, linkLen).trim();\n          cap[3] = '';\n        }\n      }\n      let href = cap[2];\n      let title = '';\n      if (this.options.pedantic) {\n        // split pedantic href and title\n        const link = this.rules.other.pedanticHrefTitle.exec(href);\n        if (link) {\n          href = link[1];\n          title = link[3];\n        }\n      } else {\n        title = cap[3] ? cap[3].slice(1, -1) : '';\n      }\n      href = href.trim();\n      if (this.rules.other.startAngleBracket.test(href)) {\n        if (this.options.pedantic && !this.rules.other.endAngleBracket.test(trimmedUrl)) {\n          // pedantic allows starting angle bracket without ending angle bracket\n          href = href.slice(1);\n        } else {\n          href = href.slice(1, -1);\n        }\n      }\n      return outputLink(cap, {\n        href: href ? href.replace(this.rules.inline.anyPunctuation, '$1') : href,\n        title: title ? title.replace(this.rules.inline.anyPunctuation, '$1') : title\n      }, cap[0], this.lexer, this.rules);\n    }\n  }\n  reflink(src, links) {\n    let cap;\n    if ((cap = this.rules.inline.reflink.exec(src)) || (cap = this.rules.inline.nolink.exec(src))) {\n      const linkString = (cap[2] || cap[1]).replace(this.rules.other.multipleSpaceGlobal, ' ');\n      const link = links[linkString.toLowerCase()];\n      if (!link) {\n        const text = cap[0].charAt(0);\n        return {\n          type: 'text',\n          raw: text,\n          text\n        };\n      }\n      return outputLink(cap, link, cap[0], this.lexer, this.rules);\n    }\n  }\n  emStrong(src, maskedSrc, prevChar = '') {\n    let match = this.rules.inline.emStrongLDelim.exec(src);\n    if (!match) return;\n    // _ can't be between two alphanumerics. \\p{L}\\p{N} includes non-english alphabet/numbers as well\n    if (match[3] && prevChar.match(this.rules.other.unicodeAlphaNumeric)) return;\n    const nextChar = match[1] || match[2] || '';\n    if (!nextChar || !prevChar || this.rules.inline.punctuation.exec(prevChar)) {\n      // unicode Regex counts emoji as 1 char; spread into array for proper count (used multiple times below)\n      const lLength = [...match[0]].length - 1;\n      let rDelim,\n        rLength,\n        delimTotal = lLength,\n        midDelimTotal = 0;\n      const endReg = match[0][0] === '*' ? this.rules.inline.emStrongRDelimAst : this.rules.inline.emStrongRDelimUnd;\n      endReg.lastIndex = 0;\n      // Clip maskedSrc to same section of string as src (move to lexer?)\n      maskedSrc = maskedSrc.slice(-1 * src.length + lLength);\n      while ((match = endReg.exec(maskedSrc)) != null) {\n        rDelim = match[1] || match[2] || match[3] || match[4] || match[5] || match[6];\n        if (!rDelim) continue; // skip single * in __abc*abc__\n        rLength = [...rDelim].length;\n        if (match[3] || match[4]) {\n          // found another Left Delim\n          delimTotal += rLength;\n          continue;\n        } else if (match[5] || match[6]) {\n          // either Left or Right Delim\n          if (lLength % 3 && !((lLength + rLength) % 3)) {\n            midDelimTotal += rLength;\n            continue; // CommonMark Emphasis Rules 9-10\n          }\n        }\n        delimTotal -= rLength;\n        if (delimTotal > 0) continue; // Haven't found enough closing delimiters\n        // Remove extra characters. *a*** -> *a*\n        rLength = Math.min(rLength, rLength + delimTotal + midDelimTotal);\n        // char length can be >1 for unicode characters;\n        const lastCharLength = [...match[0]][0].length;\n        const raw = src.slice(0, lLength + match.index + lastCharLength + rLength);\n        // Create `em` if smallest delimiter has odd char count. *a***\n        if (Math.min(lLength, rLength) % 2) {\n          const text = raw.slice(1, -1);\n          return {\n            type: 'em',\n            raw,\n            text,\n            tokens: this.lexer.inlineTokens(text)\n          };\n        }\n        // Create 'strong' if smallest delimiter has even char count. **a***\n        const text = raw.slice(2, -2);\n        return {\n          type: 'strong',\n          raw,\n          text,\n          tokens: this.lexer.inlineTokens(text)\n        };\n      }\n    }\n  }\n  codespan(src) {\n    const cap = this.rules.inline.code.exec(src);\n    if (cap) {\n      let text = cap[2].replace(this.rules.other.newLineCharGlobal, ' ');\n      const hasNonSpaceChars = this.rules.other.nonSpaceChar.test(text);\n      const hasSpaceCharsOnBothEnds = this.rules.other.startingSpaceChar.test(text) && this.rules.other.endingSpaceChar.test(text);\n      if (hasNonSpaceChars && hasSpaceCharsOnBothEnds) {\n        text = text.substring(1, text.length - 1);\n      }\n      return {\n        type: 'codespan',\n        raw: cap[0],\n        text\n      };\n    }\n  }\n  br(src) {\n    const cap = this.rules.inline.br.exec(src);\n    if (cap) {\n      return {\n        type: 'br',\n        raw: cap[0]\n      };\n    }\n  }\n  del(src) {\n    const cap = this.rules.inline.del.exec(src);\n    if (cap) {\n      return {\n        type: 'del',\n        raw: cap[0],\n        text: cap[2],\n        tokens: this.lexer.inlineTokens(cap[2])\n      };\n    }\n  }\n  autolink(src) {\n    const cap = this.rules.inline.autolink.exec(src);\n    if (cap) {\n      let text, href;\n      if (cap[2] === '@') {\n        text = cap[1];\n        href = 'mailto:' + text;\n      } else {\n        text = cap[1];\n        href = text;\n      }\n      return {\n        type: 'link',\n        raw: cap[0],\n        text,\n        href,\n        tokens: [{\n          type: 'text',\n          raw: text,\n          text\n        }]\n      };\n    }\n  }\n  url(src) {\n    let cap;\n    if (cap = this.rules.inline.url.exec(src)) {\n      let text, href;\n      if (cap[2] === '@') {\n        text = cap[0];\n        href = 'mailto:' + text;\n      } else {\n        // do extended autolink path validation\n        let prevCapZero;\n        do {\n          prevCapZero = cap[0];\n          cap[0] = this.rules.inline._backpedal.exec(cap[0])?.[0] ?? '';\n        } while (prevCapZero !== cap[0]);\n        text = cap[0];\n        if (cap[1] === 'www.') {\n          href = 'http://' + cap[0];\n        } else {\n          href = cap[0];\n        }\n      }\n      return {\n        type: 'link',\n        raw: cap[0],\n        text,\n        href,\n        tokens: [{\n          type: 'text',\n          raw: text,\n          text\n        }]\n      };\n    }\n  }\n  inlineText(src) {\n    const cap = this.rules.inline.text.exec(src);\n    if (cap) {\n      const escaped = this.lexer.state.inRawBlock;\n      return {\n        type: 'text',\n        raw: cap[0],\n        text: cap[0],\n        escaped\n      };\n    }\n  }\n}\n\n/**\n * Block Lexer\n */\nclass _Lexer {\n  tokens;\n  options;\n  state;\n  tokenizer;\n  inlineQueue;\n  constructor(options) {\n    // TokenList cannot be created in one go\n    this.tokens = [];\n    this.tokens.links = Object.create(null);\n    this.options = options || _defaults;\n    this.options.tokenizer = this.options.tokenizer || new _Tokenizer();\n    this.tokenizer = this.options.tokenizer;\n    this.tokenizer.options = this.options;\n    this.tokenizer.lexer = this;\n    this.inlineQueue = [];\n    this.state = {\n      inLink: false,\n      inRawBlock: false,\n      top: true\n    };\n    const rules = {\n      other,\n      block: block.normal,\n      inline: inline.normal\n    };\n    if (this.options.pedantic) {\n      rules.block = block.pedantic;\n      rules.inline = inline.pedantic;\n    } else if (this.options.gfm) {\n      rules.block = block.gfm;\n      if (this.options.breaks) {\n        rules.inline = inline.breaks;\n      } else {\n        rules.inline = inline.gfm;\n      }\n    }\n    this.tokenizer.rules = rules;\n  }\n  /**\n   * Expose Rules\n   */\n  static get rules() {\n    return {\n      block,\n      inline\n    };\n  }\n  /**\n   * Static Lex Method\n   */\n  static lex(src, options) {\n    const lexer = new _Lexer(options);\n    return lexer.lex(src);\n  }\n  /**\n   * Static Lex Inline Method\n   */\n  static lexInline(src, options) {\n    const lexer = new _Lexer(options);\n    return lexer.inlineTokens(src);\n  }\n  /**\n   * Preprocessing\n   */\n  lex(src) {\n    src = src.replace(other.carriageReturn, '\\n');\n    this.blockTokens(src, this.tokens);\n    for (let i = 0; i < this.inlineQueue.length; i++) {\n      const next = this.inlineQueue[i];\n      this.inlineTokens(next.src, next.tokens);\n    }\n    this.inlineQueue = [];\n    return this.tokens;\n  }\n  blockTokens(src, tokens = [], lastParagraphClipped = false) {\n    if (this.options.pedantic) {\n      src = src.replace(other.tabCharGlobal, '    ').replace(other.spaceLine, '');\n    }\n    while (src) {\n      let token;\n      if (this.options.extensions?.block?.some(extTokenizer => {\n        if (token = extTokenizer.call({\n          lexer: this\n        }, src, tokens)) {\n          src = src.substring(token.raw.length);\n          tokens.push(token);\n          return true;\n        }\n        return false;\n      })) {\n        continue;\n      }\n      // newline\n      if (token = this.tokenizer.space(src)) {\n        src = src.substring(token.raw.length);\n        const lastToken = tokens.at(-1);\n        if (token.raw.length === 1 && lastToken !== undefined) {\n          // if there's a single \\n as a spacer, it's terminating the last line,\n          // so move it there so that we don't get unnecessary paragraph tags\n          lastToken.raw += '\\n';\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n      // code\n      if (token = this.tokenizer.code(src)) {\n        src = src.substring(token.raw.length);\n        const lastToken = tokens.at(-1);\n        // An indented code block cannot interrupt a paragraph.\n        if (lastToken?.type === 'paragraph' || lastToken?.type === 'text') {\n          lastToken.raw += '\\n' + token.raw;\n          lastToken.text += '\\n' + token.text;\n          this.inlineQueue.at(-1).src = lastToken.text;\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n      // fences\n      if (token = this.tokenizer.fences(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      // heading\n      if (token = this.tokenizer.heading(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      // hr\n      if (token = this.tokenizer.hr(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      // blockquote\n      if (token = this.tokenizer.blockquote(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      // list\n      if (token = this.tokenizer.list(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      // html\n      if (token = this.tokenizer.html(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      // def\n      if (token = this.tokenizer.def(src)) {\n        src = src.substring(token.raw.length);\n        const lastToken = tokens.at(-1);\n        if (lastToken?.type === 'paragraph' || lastToken?.type === 'text') {\n          lastToken.raw += '\\n' + token.raw;\n          lastToken.text += '\\n' + token.raw;\n          this.inlineQueue.at(-1).src = lastToken.text;\n        } else if (!this.tokens.links[token.tag]) {\n          this.tokens.links[token.tag] = {\n            href: token.href,\n            title: token.title\n          };\n        }\n        continue;\n      }\n      // table (gfm)\n      if (token = this.tokenizer.table(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      // lheading\n      if (token = this.tokenizer.lheading(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      // top-level paragraph\n      // prevent paragraph consuming extensions by clipping 'src' to extension start\n      let cutSrc = src;\n      if (this.options.extensions?.startBlock) {\n        let startIndex = Infinity;\n        const tempSrc = src.slice(1);\n        let tempStart;\n        this.options.extensions.startBlock.forEach(getStartIndex => {\n          tempStart = getStartIndex.call({\n            lexer: this\n          }, tempSrc);\n          if (typeof tempStart === 'number' && tempStart >= 0) {\n            startIndex = Math.min(startIndex, tempStart);\n          }\n        });\n        if (startIndex < Infinity && startIndex >= 0) {\n          cutSrc = src.substring(0, startIndex + 1);\n        }\n      }\n      if (this.state.top && (token = this.tokenizer.paragraph(cutSrc))) {\n        const lastToken = tokens.at(-1);\n        if (lastParagraphClipped && lastToken?.type === 'paragraph') {\n          lastToken.raw += '\\n' + token.raw;\n          lastToken.text += '\\n' + token.text;\n          this.inlineQueue.pop();\n          this.inlineQueue.at(-1).src = lastToken.text;\n        } else {\n          tokens.push(token);\n        }\n        lastParagraphClipped = cutSrc.length !== src.length;\n        src = src.substring(token.raw.length);\n        continue;\n      }\n      // text\n      if (token = this.tokenizer.text(src)) {\n        src = src.substring(token.raw.length);\n        const lastToken = tokens.at(-1);\n        if (lastToken?.type === 'text') {\n          lastToken.raw += '\\n' + token.raw;\n          lastToken.text += '\\n' + token.text;\n          this.inlineQueue.pop();\n          this.inlineQueue.at(-1).src = lastToken.text;\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n      if (src) {\n        const errMsg = 'Infinite loop on byte: ' + src.charCodeAt(0);\n        if (this.options.silent) {\n          console.error(errMsg);\n          break;\n        } else {\n          throw new Error(errMsg);\n        }\n      }\n    }\n    this.state.top = true;\n    return tokens;\n  }\n  inline(src, tokens = []) {\n    this.inlineQueue.push({\n      src,\n      tokens\n    });\n    return tokens;\n  }\n  /**\n   * Lexing/Compiling\n   */\n  inlineTokens(src, tokens = []) {\n    // String with links masked to avoid interference with em and strong\n    let maskedSrc = src;\n    let match = null;\n    // Mask out reflinks\n    if (this.tokens.links) {\n      const links = Object.keys(this.tokens.links);\n      if (links.length > 0) {\n        while ((match = this.tokenizer.rules.inline.reflinkSearch.exec(maskedSrc)) != null) {\n          if (links.includes(match[0].slice(match[0].lastIndexOf('[') + 1, -1))) {\n            maskedSrc = maskedSrc.slice(0, match.index) + '[' + 'a'.repeat(match[0].length - 2) + ']' + maskedSrc.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex);\n          }\n        }\n      }\n    }\n    // Mask out other blocks\n    while ((match = this.tokenizer.rules.inline.blockSkip.exec(maskedSrc)) != null) {\n      maskedSrc = maskedSrc.slice(0, match.index) + '[' + 'a'.repeat(match[0].length - 2) + ']' + maskedSrc.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);\n    }\n    // Mask out escaped characters\n    while ((match = this.tokenizer.rules.inline.anyPunctuation.exec(maskedSrc)) != null) {\n      maskedSrc = maskedSrc.slice(0, match.index) + '++' + maskedSrc.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);\n    }\n    let keepPrevChar = false;\n    let prevChar = '';\n    while (src) {\n      if (!keepPrevChar) {\n        prevChar = '';\n      }\n      keepPrevChar = false;\n      let token;\n      // extensions\n      if (this.options.extensions?.inline?.some(extTokenizer => {\n        if (token = extTokenizer.call({\n          lexer: this\n        }, src, tokens)) {\n          src = src.substring(token.raw.length);\n          tokens.push(token);\n          return true;\n        }\n        return false;\n      })) {\n        continue;\n      }\n      // escape\n      if (token = this.tokenizer.escape(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      // tag\n      if (token = this.tokenizer.tag(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      // link\n      if (token = this.tokenizer.link(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      // reflink, nolink\n      if (token = this.tokenizer.reflink(src, this.tokens.links)) {\n        src = src.substring(token.raw.length);\n        const lastToken = tokens.at(-1);\n        if (token.type === 'text' && lastToken?.type === 'text') {\n          lastToken.raw += token.raw;\n          lastToken.text += token.text;\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n      // em & strong\n      if (token = this.tokenizer.emStrong(src, maskedSrc, prevChar)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      // code\n      if (token = this.tokenizer.codespan(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      // br\n      if (token = this.tokenizer.br(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      // del (gfm)\n      if (token = this.tokenizer.del(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      // autolink\n      if (token = this.tokenizer.autolink(src)) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      // url (gfm)\n      if (!this.state.inLink && (token = this.tokenizer.url(src))) {\n        src = src.substring(token.raw.length);\n        tokens.push(token);\n        continue;\n      }\n      // text\n      // prevent inlineText consuming extensions by clipping 'src' to extension start\n      let cutSrc = src;\n      if (this.options.extensions?.startInline) {\n        let startIndex = Infinity;\n        const tempSrc = src.slice(1);\n        let tempStart;\n        this.options.extensions.startInline.forEach(getStartIndex => {\n          tempStart = getStartIndex.call({\n            lexer: this\n          }, tempSrc);\n          if (typeof tempStart === 'number' && tempStart >= 0) {\n            startIndex = Math.min(startIndex, tempStart);\n          }\n        });\n        if (startIndex < Infinity && startIndex >= 0) {\n          cutSrc = src.substring(0, startIndex + 1);\n        }\n      }\n      if (token = this.tokenizer.inlineText(cutSrc)) {\n        src = src.substring(token.raw.length);\n        if (token.raw.slice(-1) !== '_') {\n          // Track prevChar before string of ____ started\n          prevChar = token.raw.slice(-1);\n        }\n        keepPrevChar = true;\n        const lastToken = tokens.at(-1);\n        if (lastToken?.type === 'text') {\n          lastToken.raw += token.raw;\n          lastToken.text += token.text;\n        } else {\n          tokens.push(token);\n        }\n        continue;\n      }\n      if (src) {\n        const errMsg = 'Infinite loop on byte: ' + src.charCodeAt(0);\n        if (this.options.silent) {\n          console.error(errMsg);\n          break;\n        } else {\n          throw new Error(errMsg);\n        }\n      }\n    }\n    return tokens;\n  }\n}\n\n/**\n * Renderer\n */\nclass _Renderer {\n  options;\n  parser; // set by the parser\n  constructor(options) {\n    this.options = options || _defaults;\n  }\n  space(token) {\n    return '';\n  }\n  code({\n    text,\n    lang,\n    escaped\n  }) {\n    const langString = (lang || '').match(other.notSpaceStart)?.[0];\n    const code = text.replace(other.endingNewline, '') + '\\n';\n    if (!langString) {\n      return '<pre><code>' + (escaped ? code : escape(code, true)) + '</code></pre>\\n';\n    }\n    return '<pre><code class=\"language-' + escape(langString) + '\">' + (escaped ? code : escape(code, true)) + '</code></pre>\\n';\n  }\n  blockquote({\n    tokens\n  }) {\n    const body = this.parser.parse(tokens);\n    return `<blockquote>\\n${body}</blockquote>\\n`;\n  }\n  html({\n    text\n  }) {\n    return text;\n  }\n  heading({\n    tokens,\n    depth\n  }) {\n    return `<h${depth}>${this.parser.parseInline(tokens)}</h${depth}>\\n`;\n  }\n  hr(token) {\n    return '<hr>\\n';\n  }\n  list(token) {\n    const ordered = token.ordered;\n    const start = token.start;\n    let body = '';\n    for (let j = 0; j < token.items.length; j++) {\n      const item = token.items[j];\n      body += this.listitem(item);\n    }\n    const type = ordered ? 'ol' : 'ul';\n    const startAttr = ordered && start !== 1 ? ' start=\"' + start + '\"' : '';\n    return '<' + type + startAttr + '>\\n' + body + '</' + type + '>\\n';\n  }\n  listitem(item) {\n    let itemBody = '';\n    if (item.task) {\n      const checkbox = this.checkbox({\n        checked: !!item.checked\n      });\n      if (item.loose) {\n        if (item.tokens[0]?.type === 'paragraph') {\n          item.tokens[0].text = checkbox + ' ' + item.tokens[0].text;\n          if (item.tokens[0].tokens && item.tokens[0].tokens.length > 0 && item.tokens[0].tokens[0].type === 'text') {\n            item.tokens[0].tokens[0].text = checkbox + ' ' + escape(item.tokens[0].tokens[0].text);\n            item.tokens[0].tokens[0].escaped = true;\n          }\n        } else {\n          item.tokens.unshift({\n            type: 'text',\n            raw: checkbox + ' ',\n            text: checkbox + ' ',\n            escaped: true\n          });\n        }\n      } else {\n        itemBody += checkbox + ' ';\n      }\n    }\n    itemBody += this.parser.parse(item.tokens, !!item.loose);\n    return `<li>${itemBody}</li>\\n`;\n  }\n  checkbox({\n    checked\n  }) {\n    return '<input ' + (checked ? 'checked=\"\" ' : '') + 'disabled=\"\" type=\"checkbox\">';\n  }\n  paragraph({\n    tokens\n  }) {\n    return `<p>${this.parser.parseInline(tokens)}</p>\\n`;\n  }\n  table(token) {\n    let header = '';\n    // header\n    let cell = '';\n    for (let j = 0; j < token.header.length; j++) {\n      cell += this.tablecell(token.header[j]);\n    }\n    header += this.tablerow({\n      text: cell\n    });\n    let body = '';\n    for (let j = 0; j < token.rows.length; j++) {\n      const row = token.rows[j];\n      cell = '';\n      for (let k = 0; k < row.length; k++) {\n        cell += this.tablecell(row[k]);\n      }\n      body += this.tablerow({\n        text: cell\n      });\n    }\n    if (body) body = `<tbody>${body}</tbody>`;\n    return '<table>\\n' + '<thead>\\n' + header + '</thead>\\n' + body + '</table>\\n';\n  }\n  tablerow({\n    text\n  }) {\n    return `<tr>\\n${text}</tr>\\n`;\n  }\n  tablecell(token) {\n    const content = this.parser.parseInline(token.tokens);\n    const type = token.header ? 'th' : 'td';\n    const tag = token.align ? `<${type} align=\"${token.align}\">` : `<${type}>`;\n    return tag + content + `</${type}>\\n`;\n  }\n  /**\n   * span level renderer\n   */\n  strong({\n    tokens\n  }) {\n    return `<strong>${this.parser.parseInline(tokens)}</strong>`;\n  }\n  em({\n    tokens\n  }) {\n    return `<em>${this.parser.parseInline(tokens)}</em>`;\n  }\n  codespan({\n    text\n  }) {\n    return `<code>${escape(text, true)}</code>`;\n  }\n  br(token) {\n    return '<br>';\n  }\n  del({\n    tokens\n  }) {\n    return `<del>${this.parser.parseInline(tokens)}</del>`;\n  }\n  link({\n    href,\n    title,\n    tokens\n  }) {\n    const text = this.parser.parseInline(tokens);\n    const cleanHref = cleanUrl(href);\n    if (cleanHref === null) {\n      return text;\n    }\n    href = cleanHref;\n    let out = '<a href=\"' + href + '\"';\n    if (title) {\n      out += ' title=\"' + escape(title) + '\"';\n    }\n    out += '>' + text + '</a>';\n    return out;\n  }\n  image({\n    href,\n    title,\n    text\n  }) {\n    const cleanHref = cleanUrl(href);\n    if (cleanHref === null) {\n      return escape(text);\n    }\n    href = cleanHref;\n    let out = `<img src=\"${href}\" alt=\"${text}\"`;\n    if (title) {\n      out += ` title=\"${escape(title)}\"`;\n    }\n    out += '>';\n    return out;\n  }\n  text(token) {\n    return 'tokens' in token && token.tokens ? this.parser.parseInline(token.tokens) : 'escaped' in token && token.escaped ? token.text : escape(token.text);\n  }\n}\n\n/**\n * TextRenderer\n * returns only the textual part of the token\n */\nclass _TextRenderer {\n  // no need for block level renderers\n  strong({\n    text\n  }) {\n    return text;\n  }\n  em({\n    text\n  }) {\n    return text;\n  }\n  codespan({\n    text\n  }) {\n    return text;\n  }\n  del({\n    text\n  }) {\n    return text;\n  }\n  html({\n    text\n  }) {\n    return text;\n  }\n  text({\n    text\n  }) {\n    return text;\n  }\n  link({\n    text\n  }) {\n    return '' + text;\n  }\n  image({\n    text\n  }) {\n    return '' + text;\n  }\n  br() {\n    return '';\n  }\n}\n\n/**\n * Parsing & Compiling\n */\nclass _Parser {\n  options;\n  renderer;\n  textRenderer;\n  constructor(options) {\n    this.options = options || _defaults;\n    this.options.renderer = this.options.renderer || new _Renderer();\n    this.renderer = this.options.renderer;\n    this.renderer.options = this.options;\n    this.renderer.parser = this;\n    this.textRenderer = new _TextRenderer();\n  }\n  /**\n   * Static Parse Method\n   */\n  static parse(tokens, options) {\n    const parser = new _Parser(options);\n    return parser.parse(tokens);\n  }\n  /**\n   * Static Parse Inline Method\n   */\n  static parseInline(tokens, options) {\n    const parser = new _Parser(options);\n    return parser.parseInline(tokens);\n  }\n  /**\n   * Parse Loop\n   */\n  parse(tokens, top = true) {\n    let out = '';\n    for (let i = 0; i < tokens.length; i++) {\n      const anyToken = tokens[i];\n      // Run any renderer extensions\n      if (this.options.extensions?.renderers?.[anyToken.type]) {\n        const genericToken = anyToken;\n        const ret = this.options.extensions.renderers[genericToken.type].call({\n          parser: this\n        }, genericToken);\n        if (ret !== false || !['space', 'hr', 'heading', 'code', 'table', 'blockquote', 'list', 'html', 'paragraph', 'text'].includes(genericToken.type)) {\n          out += ret || '';\n          continue;\n        }\n      }\n      const token = anyToken;\n      switch (token.type) {\n        case 'space':\n          {\n            out += this.renderer.space(token);\n            continue;\n          }\n        case 'hr':\n          {\n            out += this.renderer.hr(token);\n            continue;\n          }\n        case 'heading':\n          {\n            out += this.renderer.heading(token);\n            continue;\n          }\n        case 'code':\n          {\n            out += this.renderer.code(token);\n            continue;\n          }\n        case 'table':\n          {\n            out += this.renderer.table(token);\n            continue;\n          }\n        case 'blockquote':\n          {\n            out += this.renderer.blockquote(token);\n            continue;\n          }\n        case 'list':\n          {\n            out += this.renderer.list(token);\n            continue;\n          }\n        case 'html':\n          {\n            out += this.renderer.html(token);\n            continue;\n          }\n        case 'paragraph':\n          {\n            out += this.renderer.paragraph(token);\n            continue;\n          }\n        case 'text':\n          {\n            let textToken = token;\n            let body = this.renderer.text(textToken);\n            while (i + 1 < tokens.length && tokens[i + 1].type === 'text') {\n              textToken = tokens[++i];\n              body += '\\n' + this.renderer.text(textToken);\n            }\n            if (top) {\n              out += this.renderer.paragraph({\n                type: 'paragraph',\n                raw: body,\n                text: body,\n                tokens: [{\n                  type: 'text',\n                  raw: body,\n                  text: body,\n                  escaped: true\n                }]\n              });\n            } else {\n              out += body;\n            }\n            continue;\n          }\n        default:\n          {\n            const errMsg = 'Token with \"' + token.type + '\" type was not found.';\n            if (this.options.silent) {\n              console.error(errMsg);\n              return '';\n            } else {\n              throw new Error(errMsg);\n            }\n          }\n      }\n    }\n    return out;\n  }\n  /**\n   * Parse Inline Tokens\n   */\n  parseInline(tokens, renderer = this.renderer) {\n    let out = '';\n    for (let i = 0; i < tokens.length; i++) {\n      const anyToken = tokens[i];\n      // Run any renderer extensions\n      if (this.options.extensions?.renderers?.[anyToken.type]) {\n        const ret = this.options.extensions.renderers[anyToken.type].call({\n          parser: this\n        }, anyToken);\n        if (ret !== false || !['escape', 'html', 'link', 'image', 'strong', 'em', 'codespan', 'br', 'del', 'text'].includes(anyToken.type)) {\n          out += ret || '';\n          continue;\n        }\n      }\n      const token = anyToken;\n      switch (token.type) {\n        case 'escape':\n          {\n            out += renderer.text(token);\n            break;\n          }\n        case 'html':\n          {\n            out += renderer.html(token);\n            break;\n          }\n        case 'link':\n          {\n            out += renderer.link(token);\n            break;\n          }\n        case 'image':\n          {\n            out += renderer.image(token);\n            break;\n          }\n        case 'strong':\n          {\n            out += renderer.strong(token);\n            break;\n          }\n        case 'em':\n          {\n            out += renderer.em(token);\n            break;\n          }\n        case 'codespan':\n          {\n            out += renderer.codespan(token);\n            break;\n          }\n        case 'br':\n          {\n            out += renderer.br(token);\n            break;\n          }\n        case 'del':\n          {\n            out += renderer.del(token);\n            break;\n          }\n        case 'text':\n          {\n            out += renderer.text(token);\n            break;\n          }\n        default:\n          {\n            const errMsg = 'Token with \"' + token.type + '\" type was not found.';\n            if (this.options.silent) {\n              console.error(errMsg);\n              return '';\n            } else {\n              throw new Error(errMsg);\n            }\n          }\n      }\n    }\n    return out;\n  }\n}\nclass _Hooks {\n  options;\n  block;\n  constructor(options) {\n    this.options = options || _defaults;\n  }\n  static passThroughHooks = new Set(['preprocess', 'postprocess', 'processAllTokens']);\n  /**\n   * Process markdown before marked\n   */\n  preprocess(markdown) {\n    return markdown;\n  }\n  /**\n   * Process HTML after marked is finished\n   */\n  postprocess(html) {\n    return html;\n  }\n  /**\n   * Process all tokens before walk tokens\n   */\n  processAllTokens(tokens) {\n    return tokens;\n  }\n  /**\n   * Provide function to tokenize markdown\n   */\n  provideLexer() {\n    return this.block ? _Lexer.lex : _Lexer.lexInline;\n  }\n  /**\n   * Provide function to parse tokens\n   */\n  provideParser() {\n    return this.block ? _Parser.parse : _Parser.parseInline;\n  }\n}\nclass Marked {\n  defaults = _getDefaults();\n  options = this.setOptions;\n  parse = this.parseMarkdown(true);\n  parseInline = this.parseMarkdown(false);\n  Parser = _Parser;\n  Renderer = _Renderer;\n  TextRenderer = _TextRenderer;\n  Lexer = _Lexer;\n  Tokenizer = _Tokenizer;\n  Hooks = _Hooks;\n  constructor(...args) {\n    this.use(...args);\n  }\n  /**\n   * Run callback for every token\n   */\n  walkTokens(tokens, callback) {\n    let values = [];\n    for (const token of tokens) {\n      values = values.concat(callback.call(this, token));\n      switch (token.type) {\n        case 'table':\n          {\n            const tableToken = token;\n            for (const cell of tableToken.header) {\n              values = values.concat(this.walkTokens(cell.tokens, callback));\n            }\n            for (const row of tableToken.rows) {\n              for (const cell of row) {\n                values = values.concat(this.walkTokens(cell.tokens, callback));\n              }\n            }\n            break;\n          }\n        case 'list':\n          {\n            const listToken = token;\n            values = values.concat(this.walkTokens(listToken.items, callback));\n            break;\n          }\n        default:\n          {\n            const genericToken = token;\n            if (this.defaults.extensions?.childTokens?.[genericToken.type]) {\n              this.defaults.extensions.childTokens[genericToken.type].forEach(childTokens => {\n                const tokens = genericToken[childTokens].flat(Infinity);\n                values = values.concat(this.walkTokens(tokens, callback));\n              });\n            } else if (genericToken.tokens) {\n              values = values.concat(this.walkTokens(genericToken.tokens, callback));\n            }\n          }\n      }\n    }\n    return values;\n  }\n  use(...args) {\n    const extensions = this.defaults.extensions || {\n      renderers: {},\n      childTokens: {}\n    };\n    args.forEach(pack => {\n      // copy options to new object\n      const opts = {\n        ...pack\n      };\n      // set async to true if it was set to true before\n      opts.async = this.defaults.async || opts.async || false;\n      // ==-- Parse \"addon\" extensions --== //\n      if (pack.extensions) {\n        pack.extensions.forEach(ext => {\n          if (!ext.name) {\n            throw new Error('extension name required');\n          }\n          if ('renderer' in ext) {\n            // Renderer extensions\n            const prevRenderer = extensions.renderers[ext.name];\n            if (prevRenderer) {\n              // Replace extension with func to run new extension but fall back if false\n              extensions.renderers[ext.name] = function (...args) {\n                let ret = ext.renderer.apply(this, args);\n                if (ret === false) {\n                  ret = prevRenderer.apply(this, args);\n                }\n                return ret;\n              };\n            } else {\n              extensions.renderers[ext.name] = ext.renderer;\n            }\n          }\n          if ('tokenizer' in ext) {\n            // Tokenizer Extensions\n            if (!ext.level || ext.level !== 'block' && ext.level !== 'inline') {\n              throw new Error(\"extension level must be 'block' or 'inline'\");\n            }\n            const extLevel = extensions[ext.level];\n            if (extLevel) {\n              extLevel.unshift(ext.tokenizer);\n            } else {\n              extensions[ext.level] = [ext.tokenizer];\n            }\n            if (ext.start) {\n              // Function to check for start of token\n              if (ext.level === 'block') {\n                if (extensions.startBlock) {\n                  extensions.startBlock.push(ext.start);\n                } else {\n                  extensions.startBlock = [ext.start];\n                }\n              } else if (ext.level === 'inline') {\n                if (extensions.startInline) {\n                  extensions.startInline.push(ext.start);\n                } else {\n                  extensions.startInline = [ext.start];\n                }\n              }\n            }\n          }\n          if ('childTokens' in ext && ext.childTokens) {\n            // Child tokens to be visited by walkTokens\n            extensions.childTokens[ext.name] = ext.childTokens;\n          }\n        });\n        opts.extensions = extensions;\n      }\n      // ==-- Parse \"overwrite\" extensions --== //\n      if (pack.renderer) {\n        const renderer = this.defaults.renderer || new _Renderer(this.defaults);\n        for (const prop in pack.renderer) {\n          if (!(prop in renderer)) {\n            throw new Error(`renderer '${prop}' does not exist`);\n          }\n          if (['options', 'parser'].includes(prop)) {\n            // ignore options property\n            continue;\n          }\n          const rendererProp = prop;\n          const rendererFunc = pack.renderer[rendererProp];\n          const prevRenderer = renderer[rendererProp];\n          // Replace renderer with func to run extension, but fall back if false\n          renderer[rendererProp] = (...args) => {\n            let ret = rendererFunc.apply(renderer, args);\n            if (ret === false) {\n              ret = prevRenderer.apply(renderer, args);\n            }\n            return ret || '';\n          };\n        }\n        opts.renderer = renderer;\n      }\n      if (pack.tokenizer) {\n        const tokenizer = this.defaults.tokenizer || new _Tokenizer(this.defaults);\n        for (const prop in pack.tokenizer) {\n          if (!(prop in tokenizer)) {\n            throw new Error(`tokenizer '${prop}' does not exist`);\n          }\n          if (['options', 'rules', 'lexer'].includes(prop)) {\n            // ignore options, rules, and lexer properties\n            continue;\n          }\n          const tokenizerProp = prop;\n          const tokenizerFunc = pack.tokenizer[tokenizerProp];\n          const prevTokenizer = tokenizer[tokenizerProp];\n          // Replace tokenizer with func to run extension, but fall back if false\n          // @ts-expect-error cannot type tokenizer function dynamically\n          tokenizer[tokenizerProp] = (...args) => {\n            let ret = tokenizerFunc.apply(tokenizer, args);\n            if (ret === false) {\n              ret = prevTokenizer.apply(tokenizer, args);\n            }\n            return ret;\n          };\n        }\n        opts.tokenizer = tokenizer;\n      }\n      // ==-- Parse Hooks extensions --== //\n      if (pack.hooks) {\n        const hooks = this.defaults.hooks || new _Hooks();\n        for (const prop in pack.hooks) {\n          if (!(prop in hooks)) {\n            throw new Error(`hook '${prop}' does not exist`);\n          }\n          if (['options', 'block'].includes(prop)) {\n            // ignore options and block properties\n            continue;\n          }\n          const hooksProp = prop;\n          const hooksFunc = pack.hooks[hooksProp];\n          const prevHook = hooks[hooksProp];\n          if (_Hooks.passThroughHooks.has(prop)) {\n            // @ts-expect-error cannot type hook function dynamically\n            hooks[hooksProp] = arg => {\n              if (this.defaults.async) {\n                return Promise.resolve(hooksFunc.call(hooks, arg)).then(ret => {\n                  return prevHook.call(hooks, ret);\n                });\n              }\n              const ret = hooksFunc.call(hooks, arg);\n              return prevHook.call(hooks, ret);\n            };\n          } else {\n            // @ts-expect-error cannot type hook function dynamically\n            hooks[hooksProp] = (...args) => {\n              let ret = hooksFunc.apply(hooks, args);\n              if (ret === false) {\n                ret = prevHook.apply(hooks, args);\n              }\n              return ret;\n            };\n          }\n        }\n        opts.hooks = hooks;\n      }\n      // ==-- Parse WalkTokens extensions --== //\n      if (pack.walkTokens) {\n        const walkTokens = this.defaults.walkTokens;\n        const packWalktokens = pack.walkTokens;\n        opts.walkTokens = function (token) {\n          let values = [];\n          values.push(packWalktokens.call(this, token));\n          if (walkTokens) {\n            values = values.concat(walkTokens.call(this, token));\n          }\n          return values;\n        };\n      }\n      this.defaults = {\n        ...this.defaults,\n        ...opts\n      };\n    });\n    return this;\n  }\n  setOptions(opt) {\n    this.defaults = {\n      ...this.defaults,\n      ...opt\n    };\n    return this;\n  }\n  lexer(src, options) {\n    return _Lexer.lex(src, options ?? this.defaults);\n  }\n  parser(tokens, options) {\n    return _Parser.parse(tokens, options ?? this.defaults);\n  }\n  parseMarkdown(blockType) {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    const parse = (src, options) => {\n      const origOpt = {\n        ...options\n      };\n      const opt = {\n        ...this.defaults,\n        ...origOpt\n      };\n      const throwError = this.onError(!!opt.silent, !!opt.async);\n      // throw error if an extension set async to true but parse was called with async: false\n      if (this.defaults.async === true && origOpt.async === false) {\n        return throwError(new Error('marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise.'));\n      }\n      // throw error in case of non string input\n      if (typeof src === 'undefined' || src === null) {\n        return throwError(new Error('marked(): input parameter is undefined or null'));\n      }\n      if (typeof src !== 'string') {\n        return throwError(new Error('marked(): input parameter is of type ' + Object.prototype.toString.call(src) + ', string expected'));\n      }\n      if (opt.hooks) {\n        opt.hooks.options = opt;\n        opt.hooks.block = blockType;\n      }\n      const lexer = opt.hooks ? opt.hooks.provideLexer() : blockType ? _Lexer.lex : _Lexer.lexInline;\n      const parser = opt.hooks ? opt.hooks.provideParser() : blockType ? _Parser.parse : _Parser.parseInline;\n      if (opt.async) {\n        return Promise.resolve(opt.hooks ? opt.hooks.preprocess(src) : src).then(src => lexer(src, opt)).then(tokens => opt.hooks ? opt.hooks.processAllTokens(tokens) : tokens).then(tokens => opt.walkTokens ? Promise.all(this.walkTokens(tokens, opt.walkTokens)).then(() => tokens) : tokens).then(tokens => parser(tokens, opt)).then(html => opt.hooks ? opt.hooks.postprocess(html) : html).catch(throwError);\n      }\n      try {\n        if (opt.hooks) {\n          src = opt.hooks.preprocess(src);\n        }\n        let tokens = lexer(src, opt);\n        if (opt.hooks) {\n          tokens = opt.hooks.processAllTokens(tokens);\n        }\n        if (opt.walkTokens) {\n          this.walkTokens(tokens, opt.walkTokens);\n        }\n        let html = parser(tokens, opt);\n        if (opt.hooks) {\n          html = opt.hooks.postprocess(html);\n        }\n        return html;\n      } catch (e) {\n        return throwError(e);\n      }\n    };\n    return parse;\n  }\n  onError(silent, async) {\n    return e => {\n      e.message += '\\nPlease report this to https://github.com/markedjs/marked.';\n      if (silent) {\n        const msg = '<p>An error occurred:</p><pre>' + escape(e.message + '', true) + '</pre>';\n        if (async) {\n          return Promise.resolve(msg);\n        }\n        return msg;\n      }\n      if (async) {\n        return Promise.reject(e);\n      }\n      throw e;\n    };\n  }\n}\nconst markedInstance = new Marked();\nfunction marked(src, opt) {\n  return markedInstance.parse(src, opt);\n}\n/**\n * Sets the default options.\n *\n * @param options Hash of options\n */\nmarked.options = marked.setOptions = function (options) {\n  markedInstance.setOptions(options);\n  marked.defaults = markedInstance.defaults;\n  changeDefaults(marked.defaults);\n  return marked;\n};\n/**\n * Gets the original marked default options.\n */\nmarked.getDefaults = _getDefaults;\nmarked.defaults = _defaults;\n/**\n * Use Extension\n */\nmarked.use = function (...args) {\n  markedInstance.use(...args);\n  marked.defaults = markedInstance.defaults;\n  changeDefaults(marked.defaults);\n  return marked;\n};\n/**\n * Run callback for every token\n */\nmarked.walkTokens = function (tokens, callback) {\n  return markedInstance.walkTokens(tokens, callback);\n};\n/**\n * Compiles markdown to HTML without enclosing `p` tag.\n *\n * @param src String of markdown source to be compiled\n * @param options Hash of options\n * @return String of compiled HTML\n */\nmarked.parseInline = markedInstance.parseInline;\n/**\n * Expose\n */\nmarked.Parser = _Parser;\nmarked.parser = _Parser.parse;\nmarked.Renderer = _Renderer;\nmarked.TextRenderer = _TextRenderer;\nmarked.Lexer = _Lexer;\nmarked.lexer = _Lexer.lex;\nmarked.Tokenizer = _Tokenizer;\nmarked.Hooks = _Hooks;\nmarked.parse = marked;\nconst options = marked.options;\nconst setOptions = marked.setOptions;\nconst use = marked.use;\nconst walkTokens = marked.walkTokens;\nconst parseInline = marked.parseInline;\nconst parse = marked;\nconst parser = _Parser.parse;\nconst lexer = _Lexer.lex;\nexport { _Hooks as Hooks, _Lexer as Lexer, Marked, _Parser as Parser, _Renderer as Renderer, _TextRenderer as TextRenderer, _Tokenizer as Tokenizer, _defaults as defaults, _getDefaults as getDefaults, lexer, marked, options, parse, parseInline, parser, setOptions, use, walkTokens };", "map": {"version": 3, "names": ["_getDefaults", "async", "breaks", "extensions", "gfm", "hooks", "pedantic", "renderer", "silent", "tokenizer", "walkTokens", "_defaults", "changeDefaults", "newDefaults", "noopTest", "exec", "edit", "regex", "opt", "source", "obj", "replace", "name", "val", "valSource", "other", "caret", "getRegex", "RegExp", "codeRemoveIndent", "outputLinkReplace", "indentCodeCompensation", "beginningSpace", "endingHash", "startingSpaceChar", "endingSpaceChar", "nonSpaceChar", "newLineCharGlobal", "tabCharGlobal", "multipleSpaceGlobal", "blankLine", "doubleBlankLine", "blockquoteStart", "blockquoteSetextReplace", "blockquoteSetextReplace2", "listReplaceTabs", "listReplaceNesting", "listIsTask", "listReplaceTask", "anyLine", "hrefBrackets", "tableDelimiter", "tableAlignChars", "tableRowBlankLine", "tableAlignRight", "tableAlignCenter", "tableAlignLeft", "startATag", "endATag", "startPreScriptTag", "endPreScriptTag", "startAngleBracket", "endAngleBracket", "pedanticHrefTitle", "unicodeAlphaNumeric", "escapeTest", "escapeReplace", "escapeTestNoEncode", "escapeReplaceNoEncode", "unescapeTest", "percentDecode", "find<PERSON>ipe", "splitPipe", "slashPipe", "carriageReturn", "spaceLine", "notSpaceStart", "endingNewline", "listItemRegex", "bull", "nextBulletRegex", "indent", "Math", "min", "hrRegex", "fencesBeginRegex", "headingBeginRegex", "htmlBeginRegex", "newline", "blockCode", "fences", "hr", "heading", "bullet", "lheadingCore", "lheading", "lheadingGfm", "_paragraph", "blockText", "_blockLabel", "def", "list", "_tag", "_comment", "html", "paragraph", "blockquote", "blockNormal", "code", "table", "text", "gfmTable", "blockGfm", "blockPedantic", "escape$1", "inlineCode", "br", "inlineText", "_punctuation", "_punctuationOrSpace", "_notPunctuationOrSpace", "punctuation", "_punctuationGfmStrongEm", "_punctuationOrSpaceGfmStrongEm", "_notPunctuationOrSpaceGfmStrongEm", "blockSkip", "emStrongLDelimCore", "em<PERSON><PERSON>g<PERSON><PERSON><PERSON>", "emStrongLDelimGfm", "emStrongRDelimAstCore", "emStrongRDelim<PERSON>t", "emStrongRDelimAstGfm", "emStrongRDelimUnd", "anyPunctuation", "autolink", "_inlineComment", "tag", "_inlineLabel", "link", "reflink", "nolink", "reflinkSearch", "inlineNormal", "_backpedal", "del", "escape", "url", "inlinePedantic", "inlineGfm", "inlineBreaks", "block", "normal", "inline", "escapeReplacements", "getEscapeReplacement", "ch", "encode", "test", "cleanUrl", "href", "encodeURI", "splitCells", "tableRow", "count", "row", "match", "offset", "str", "escaped", "curr", "cells", "split", "i", "trim", "shift", "length", "at", "pop", "splice", "push", "rtrim", "c", "invert", "l", "suffLen", "curr<PERSON>har", "char<PERSON>t", "slice", "findClosingBracket", "b", "indexOf", "level", "outputLink", "cap", "raw", "lexer", "rules", "title", "state", "inLink", "token", "type", "tokens", "inlineTokens", "matchIndentToCode", "indentToCode", "map", "node", "matchIndentInNode", "indentInNode", "join", "_Tokenizer", "options", "constructor", "space", "src", "codeBlockStyle", "lang", "trimmed", "depth", "lines", "inBlockquote", "currentLines", "currentRaw", "currentText", "top", "blockTokens", "lastToken", "oldToken", "newText", "newToken", "substring", "isordered", "ordered", "start", "loose", "items", "itemRegex", "endsWithBlankLine", "endEarly", "itemContents", "line", "t", "repeat", "nextLine", "trimStart", "search", "rawLine", "nextLineWithoutTabs", "istask", "ischecked", "task", "checked", "lastItem", "trimEnd", "spacers", "filter", "hasMultipleLineBreaks", "some", "pre", "toLowerCase", "headers", "aligns", "rows", "item", "header", "align", "cell", "inRawBlock", "trimmedUrl", "rtrimSlash", "lastParenIndex", "linkLen", "links", "linkString", "emStrong", "maskedSrc", "prevChar", "nextChar", "l<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "r<PERSON><PERSON><PERSON>", "delimTotal", "midDelimTotal", "endReg", "lastIndex", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "index", "codespan", "hasNonSpaceChars", "hasSpaceCharsOnBothEnds", "prevCapZero", "_<PERSON>er", "inlineQueue", "Object", "create", "lex", "lexInline", "next", "lastParagraphClipped", "extTokenizer", "call", "undefined", "cutSrc", "startBlock", "startIndex", "Infinity", "tempSrc", "tempStart", "for<PERSON>ach", "getStartIndex", "errMsg", "charCodeAt", "console", "error", "Error", "keys", "includes", "lastIndexOf", "keepPrevChar", "startInline", "_Renderer", "parser", "langString", "body", "parse", "parseInline", "j", "listitem", "startAttr", "itemBody", "checkbox", "unshift", "tablecell", "tablerow", "k", "content", "strong", "em", "cleanHref", "out", "image", "_<PERSON><PERSON><PERSON><PERSON>", "_<PERSON><PERSON>r", "<PERSON><PERSON><PERSON><PERSON>", "anyToken", "renderers", "genericToken", "ret", "textToken", "_Hooks", "passThroughHooks", "Set", "preprocess", "markdown", "postprocess", "processAllTokens", "provideLexer", "<PERSON><PERSON><PERSON><PERSON>", "Marked", "defaults", "setOptions", "parseMarkdown", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Tokenizer", "<PERSON>s", "args", "use", "callback", "values", "concat", "tableToken", "listToken", "childTokens", "flat", "pack", "opts", "ext", "prev<PERSON><PERSON><PERSON>", "apply", "extLevel", "prop", "rendererProp", "rendererFunc", "tokenizerProp", "tokenizerFunc", "prevTokenizer", "hooksProp", "hooksFunc", "prevHook", "has", "arg", "Promise", "resolve", "then", "packWalktokens", "blockType", "origOpt", "throwError", "onError", "prototype", "toString", "all", "catch", "e", "message", "msg", "reject", "markedInstance", "marked", "getDefaults"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/marked/lib/marked.esm.js"], "sourcesContent": ["/**\n * marked v15.0.7 - a markdown parser\n * Copyright (c) 2011-2025, <PERSON>. (MIT Licensed)\n * https://github.com/markedjs/marked\n */\n\n/**\n * DO NOT EDIT THIS FILE\n * The code in this file is generated from files in ./src/\n */\n\n/**\n * Gets the original marked default options.\n */\nfunction _getDefaults() {\n    return {\n        async: false,\n        breaks: false,\n        extensions: null,\n        gfm: true,\n        hooks: null,\n        pedantic: false,\n        renderer: null,\n        silent: false,\n        tokenizer: null,\n        walkTokens: null,\n    };\n}\nlet _defaults = _getDefaults();\nfunction changeDefaults(newDefaults) {\n    _defaults = newDefaults;\n}\n\nconst noopTest = { exec: () => null };\nfunction edit(regex, opt = '') {\n    let source = typeof regex === 'string' ? regex : regex.source;\n    const obj = {\n        replace: (name, val) => {\n            let valSource = typeof val === 'string' ? val : val.source;\n            valSource = valSource.replace(other.caret, '$1');\n            source = source.replace(name, valSource);\n            return obj;\n        },\n        getRegex: () => {\n            return new RegExp(source, opt);\n        },\n    };\n    return obj;\n}\nconst other = {\n    codeRemoveIndent: /^(?: {1,4}| {0,3}\\t)/gm,\n    outputLinkReplace: /\\\\([\\[\\]])/g,\n    indentCodeCompensation: /^(\\s+)(?:```)/,\n    beginningSpace: /^\\s+/,\n    endingHash: /#$/,\n    startingSpaceChar: /^ /,\n    endingSpaceChar: / $/,\n    nonSpaceChar: /[^ ]/,\n    newLineCharGlobal: /\\n/g,\n    tabCharGlobal: /\\t/g,\n    multipleSpaceGlobal: /\\s+/g,\n    blankLine: /^[ \\t]*$/,\n    doubleBlankLine: /\\n[ \\t]*\\n[ \\t]*$/,\n    blockquoteStart: /^ {0,3}>/,\n    blockquoteSetextReplace: /\\n {0,3}((?:=+|-+) *)(?=\\n|$)/g,\n    blockquoteSetextReplace2: /^ {0,3}>[ \\t]?/gm,\n    listReplaceTabs: /^\\t+/,\n    listReplaceNesting: /^ {1,4}(?=( {4})*[^ ])/g,\n    listIsTask: /^\\[[ xX]\\] /,\n    listReplaceTask: /^\\[[ xX]\\] +/,\n    anyLine: /\\n.*\\n/,\n    hrefBrackets: /^<(.*)>$/,\n    tableDelimiter: /[:|]/,\n    tableAlignChars: /^\\||\\| *$/g,\n    tableRowBlankLine: /\\n[ \\t]*$/,\n    tableAlignRight: /^ *-+: *$/,\n    tableAlignCenter: /^ *:-+: *$/,\n    tableAlignLeft: /^ *:-+ *$/,\n    startATag: /^<a /i,\n    endATag: /^<\\/a>/i,\n    startPreScriptTag: /^<(pre|code|kbd|script)(\\s|>)/i,\n    endPreScriptTag: /^<\\/(pre|code|kbd|script)(\\s|>)/i,\n    startAngleBracket: /^</,\n    endAngleBracket: />$/,\n    pedanticHrefTitle: /^([^'\"]*[^\\s])\\s+(['\"])(.*)\\2/,\n    unicodeAlphaNumeric: /[\\p{L}\\p{N}]/u,\n    escapeTest: /[&<>\"']/,\n    escapeReplace: /[&<>\"']/g,\n    escapeTestNoEncode: /[<>\"']|&(?!(#\\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\\w+);)/,\n    escapeReplaceNoEncode: /[<>\"']|&(?!(#\\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\\w+);)/g,\n    unescapeTest: /&(#(?:\\d+)|(?:#x[0-9A-Fa-f]+)|(?:\\w+));?/ig,\n    caret: /(^|[^\\[])\\^/g,\n    percentDecode: /%25/g,\n    findPipe: /\\|/g,\n    splitPipe: / \\|/,\n    slashPipe: /\\\\\\|/g,\n    carriageReturn: /\\r\\n|\\r/g,\n    spaceLine: /^ +$/gm,\n    notSpaceStart: /^\\S*/,\n    endingNewline: /\\n$/,\n    listItemRegex: (bull) => new RegExp(`^( {0,3}${bull})((?:[\\t ][^\\\\n]*)?(?:\\\\n|$))`),\n    nextBulletRegex: (indent) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}(?:[*+-]|\\\\d{1,9}[.)])((?:[ \\t][^\\\\n]*)?(?:\\\\n|$))`),\n    hrRegex: (indent) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\\\* *){3,})(?:\\\\n+|$)`),\n    fencesBeginRegex: (indent) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}(?:\\`\\`\\`|~~~)`),\n    headingBeginRegex: (indent) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}#`),\n    htmlBeginRegex: (indent) => new RegExp(`^ {0,${Math.min(3, indent - 1)}}<(?:[a-z].*>|!--)`, 'i'),\n};\n/**\n * Block-Level Grammar\n */\nconst newline = /^(?:[ \\t]*(?:\\n|$))+/;\nconst blockCode = /^((?: {4}| {0,3}\\t)[^\\n]+(?:\\n(?:[ \\t]*(?:\\n|$))*)?)+/;\nconst fences = /^ {0,3}(`{3,}(?=[^`\\n]*(?:\\n|$))|~{3,})([^\\n]*)(?:\\n|$)(?:|([\\s\\S]*?)(?:\\n|$))(?: {0,3}\\1[~`]* *(?=\\n|$)|$)/;\nconst hr = /^ {0,3}((?:-[\\t ]*){3,}|(?:_[ \\t]*){3,}|(?:\\*[ \\t]*){3,})(?:\\n+|$)/;\nconst heading = /^ {0,3}(#{1,6})(?=\\s|$)(.*)(?:\\n+|$)/;\nconst bullet = /(?:[*+-]|\\d{1,9}[.)])/;\nconst lheadingCore = /^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\\n(?!\\s*?\\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\\n {0,3}(=+|-+) *(?:\\n+|$)/;\nconst lheading = edit(lheadingCore)\n    .replace(/bull/g, bullet) // lists can interrupt\n    .replace(/blockCode/g, /(?: {4}| {0,3}\\t)/) // indented code blocks can interrupt\n    .replace(/fences/g, / {0,3}(?:`{3,}|~{3,})/) // fenced code blocks can interrupt\n    .replace(/blockquote/g, / {0,3}>/) // blockquote can interrupt\n    .replace(/heading/g, / {0,3}#{1,6}/) // ATX heading can interrupt\n    .replace(/html/g, / {0,3}<[^\\n>]+>\\n/) // block html can interrupt\n    .replace(/\\|table/g, '') // table not in commonmark\n    .getRegex();\nconst lheadingGfm = edit(lheadingCore)\n    .replace(/bull/g, bullet) // lists can interrupt\n    .replace(/blockCode/g, /(?: {4}| {0,3}\\t)/) // indented code blocks can interrupt\n    .replace(/fences/g, / {0,3}(?:`{3,}|~{3,})/) // fenced code blocks can interrupt\n    .replace(/blockquote/g, / {0,3}>/) // blockquote can interrupt\n    .replace(/heading/g, / {0,3}#{1,6}/) // ATX heading can interrupt\n    .replace(/html/g, / {0,3}<[^\\n>]+>\\n/) // block html can interrupt\n    .replace(/table/g, / {0,3}\\|?(?:[:\\- ]*\\|)+[\\:\\- ]*\\n/) // table can interrupt\n    .getRegex();\nconst _paragraph = /^([^\\n]+(?:\\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\\n)[^\\n]+)*)/;\nconst blockText = /^[^\\n]+/;\nconst _blockLabel = /(?!\\s*\\])(?:\\\\.|[^\\[\\]\\\\])+/;\nconst def = edit(/^ {0,3}\\[(label)\\]: *(?:\\n[ \\t]*)?([^<\\s][^\\s]*|<.*?>)(?:(?: +(?:\\n[ \\t]*)?| *\\n[ \\t]*)(title))? *(?:\\n+|$)/)\n    .replace('label', _blockLabel)\n    .replace('title', /(?:\"(?:\\\\\"?|[^\"\\\\])*\"|'[^'\\n]*(?:\\n[^'\\n]+)*\\n?'|\\([^()]*\\))/)\n    .getRegex();\nconst list = edit(/^( {0,3}bull)([ \\t][^\\n]+?)?(?:\\n|$)/)\n    .replace(/bull/g, bullet)\n    .getRegex();\nconst _tag = 'address|article|aside|base|basefont|blockquote|body|caption'\n    + '|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption'\n    + '|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe'\n    + '|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option'\n    + '|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title'\n    + '|tr|track|ul';\nconst _comment = /<!--(?:-?>|[\\s\\S]*?(?:-->|$))/;\nconst html = edit('^ {0,3}(?:' // optional indentation\n    + '<(script|pre|style|textarea)[\\\\s>][\\\\s\\\\S]*?(?:</\\\\1>[^\\\\n]*\\\\n+|$)' // (1)\n    + '|comment[^\\\\n]*(\\\\n+|$)' // (2)\n    + '|<\\\\?[\\\\s\\\\S]*?(?:\\\\?>\\\\n*|$)' // (3)\n    + '|<![A-Z][\\\\s\\\\S]*?(?:>\\\\n*|$)' // (4)\n    + '|<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?(?:\\\\]\\\\]>\\\\n*|$)' // (5)\n    + '|</?(tag)(?: +|\\\\n|/?>)[\\\\s\\\\S]*?(?:(?:\\\\n[ \\t]*)+\\\\n|$)' // (6)\n    + '|<(?!script|pre|style|textarea)([a-z][\\\\w-]*)(?:attribute)*? */?>(?=[ \\\\t]*(?:\\\\n|$))[\\\\s\\\\S]*?(?:(?:\\\\n[ \\t]*)+\\\\n|$)' // (7) open tag\n    + '|</(?!script|pre|style|textarea)[a-z][\\\\w-]*\\\\s*>(?=[ \\\\t]*(?:\\\\n|$))[\\\\s\\\\S]*?(?:(?:\\\\n[ \\t]*)+\\\\n|$)' // (7) closing tag\n    + ')', 'i')\n    .replace('comment', _comment)\n    .replace('tag', _tag)\n    .replace('attribute', / +[a-zA-Z:_][\\w.:-]*(?: *= *\"[^\"\\n]*\"| *= *'[^'\\n]*'| *= *[^\\s\"'=<>`]+)?/)\n    .getRegex();\nconst paragraph = edit(_paragraph)\n    .replace('hr', hr)\n    .replace('heading', ' {0,3}#{1,6}(?:\\\\s|$)')\n    .replace('|lheading', '') // setext headings don't interrupt commonmark paragraphs\n    .replace('|table', '')\n    .replace('blockquote', ' {0,3}>')\n    .replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n')\n    .replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n    .replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)')\n    .replace('tag', _tag) // pars can be interrupted by type (6) html blocks\n    .getRegex();\nconst blockquote = edit(/^( {0,3}> ?(paragraph|[^\\n]*)(?:\\n|$))+/)\n    .replace('paragraph', paragraph)\n    .getRegex();\n/**\n * Normal Block Grammar\n */\nconst blockNormal = {\n    blockquote,\n    code: blockCode,\n    def,\n    fences,\n    heading,\n    hr,\n    html,\n    lheading,\n    list,\n    newline,\n    paragraph,\n    table: noopTest,\n    text: blockText,\n};\n/**\n * GFM Block Grammar\n */\nconst gfmTable = edit('^ *([^\\\\n ].*)\\\\n' // Header\n    + ' {0,3}((?:\\\\| *)?:?-+:? *(?:\\\\| *:?-+:? *)*(?:\\\\| *)?)' // Align\n    + '(?:\\\\n((?:(?! *\\\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\\\n|$))*)\\\\n*|$)') // Cells\n    .replace('hr', hr)\n    .replace('heading', ' {0,3}#{1,6}(?:\\\\s|$)')\n    .replace('blockquote', ' {0,3}>')\n    .replace('code', '(?: {4}| {0,3}\\t)[^\\\\n]')\n    .replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n')\n    .replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n    .replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)')\n    .replace('tag', _tag) // tables can be interrupted by type (6) html blocks\n    .getRegex();\nconst blockGfm = {\n    ...blockNormal,\n    lheading: lheadingGfm,\n    table: gfmTable,\n    paragraph: edit(_paragraph)\n        .replace('hr', hr)\n        .replace('heading', ' {0,3}#{1,6}(?:\\\\s|$)')\n        .replace('|lheading', '') // setext headings don't interrupt commonmark paragraphs\n        .replace('table', gfmTable) // interrupt paragraphs with table\n        .replace('blockquote', ' {0,3}>')\n        .replace('fences', ' {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n')\n        .replace('list', ' {0,3}(?:[*+-]|1[.)]) ') // only lists starting from 1 can interrupt\n        .replace('html', '</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)')\n        .replace('tag', _tag) // pars can be interrupted by type (6) html blocks\n        .getRegex(),\n};\n/**\n * Pedantic grammar (original John Gruber's loose markdown specification)\n */\nconst blockPedantic = {\n    ...blockNormal,\n    html: edit('^ *(?:comment *(?:\\\\n|\\\\s*$)'\n        + '|<(tag)[\\\\s\\\\S]+?</\\\\1> *(?:\\\\n{2,}|\\\\s*$)' // closed tag\n        + '|<tag(?:\"[^\"]*\"|\\'[^\\']*\\'|\\\\s[^\\'\"/>\\\\s]*)*?/?> *(?:\\\\n{2,}|\\\\s*$))')\n        .replace('comment', _comment)\n        .replace(/tag/g, '(?!(?:'\n        + 'a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub'\n        + '|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)'\n        + '\\\\b)\\\\w+(?!:|[^\\\\w\\\\s@]*@)\\\\b')\n        .getRegex(),\n    def: /^ *\\[([^\\]]+)\\]: *<?([^\\s>]+)>?(?: +([\"(][^\\n]+[\")]))? *(?:\\n+|$)/,\n    heading: /^(#{1,6})(.*)(?:\\n+|$)/,\n    fences: noopTest, // fences not supported\n    lheading: /^(.+?)\\n {0,3}(=+|-+) *(?:\\n+|$)/,\n    paragraph: edit(_paragraph)\n        .replace('hr', hr)\n        .replace('heading', ' *#{1,6} *[^\\n]')\n        .replace('lheading', lheading)\n        .replace('|table', '')\n        .replace('blockquote', ' {0,3}>')\n        .replace('|fences', '')\n        .replace('|list', '')\n        .replace('|html', '')\n        .replace('|tag', '')\n        .getRegex(),\n};\n/**\n * Inline-Level Grammar\n */\nconst escape$1 = /^\\\\([!\"#$%&'()*+,\\-./:;<=>?@\\[\\]\\\\^_`{|}~])/;\nconst inlineCode = /^(`+)([^`]|[^`][\\s\\S]*?[^`])\\1(?!`)/;\nconst br = /^( {2,}|\\\\)\\n(?!\\s*$)/;\nconst inlineText = /^(`+|[^`])(?:(?= {2,}\\n)|[\\s\\S]*?(?:(?=[\\\\<!\\[`*_]|\\b_|$)|[^ ](?= {2,}\\n)))/;\n// list of unicode punctuation marks, plus any missing characters from CommonMark spec\nconst _punctuation = /[\\p{P}\\p{S}]/u;\nconst _punctuationOrSpace = /[\\s\\p{P}\\p{S}]/u;\nconst _notPunctuationOrSpace = /[^\\s\\p{P}\\p{S}]/u;\nconst punctuation = edit(/^((?![*_])punctSpace)/, 'u')\n    .replace(/punctSpace/g, _punctuationOrSpace).getRegex();\n// GFM allows ~ inside strong and em for strikethrough\nconst _punctuationGfmStrongEm = /(?!~)[\\p{P}\\p{S}]/u;\nconst _punctuationOrSpaceGfmStrongEm = /(?!~)[\\s\\p{P}\\p{S}]/u;\nconst _notPunctuationOrSpaceGfmStrongEm = /(?:[^\\s\\p{P}\\p{S}]|~)/u;\n// sequences em should skip over [title](link), `code`, <html>\nconst blockSkip = /\\[[^[\\]]*?\\]\\((?:\\\\.|[^\\\\\\(\\)]|\\((?:\\\\.|[^\\\\\\(\\)])*\\))*\\)|`[^`]*?`|<[^<>]*?>/g;\nconst emStrongLDelimCore = /^(?:\\*+(?:((?!\\*)punct)|[^\\s*]))|^_+(?:((?!_)punct)|([^\\s_]))/;\nconst emStrongLDelim = edit(emStrongLDelimCore, 'u')\n    .replace(/punct/g, _punctuation)\n    .getRegex();\nconst emStrongLDelimGfm = edit(emStrongLDelimCore, 'u')\n    .replace(/punct/g, _punctuationGfmStrongEm)\n    .getRegex();\nconst emStrongRDelimAstCore = '^[^_*]*?__[^_*]*?\\\\*[^_*]*?(?=__)' // Skip orphan inside strong\n    + '|[^*]+(?=[^*])' // Consume to delim\n    + '|(?!\\\\*)punct(\\\\*+)(?=[\\\\s]|$)' // (1) #*** can only be a Right Delimiter\n    + '|notPunctSpace(\\\\*+)(?!\\\\*)(?=punctSpace|$)' // (2) a***#, a*** can only be a Right Delimiter\n    + '|(?!\\\\*)punctSpace(\\\\*+)(?=notPunctSpace)' // (3) #***a, ***a can only be Left Delimiter\n    + '|[\\\\s](\\\\*+)(?!\\\\*)(?=punct)' // (4) ***# can only be Left Delimiter\n    + '|(?!\\\\*)punct(\\\\*+)(?!\\\\*)(?=punct)' // (5) #***# can be either Left or Right Delimiter\n    + '|notPunctSpace(\\\\*+)(?=notPunctSpace)'; // (6) a***a can be either Left or Right Delimiter\nconst emStrongRDelimAst = edit(emStrongRDelimAstCore, 'gu')\n    .replace(/notPunctSpace/g, _notPunctuationOrSpace)\n    .replace(/punctSpace/g, _punctuationOrSpace)\n    .replace(/punct/g, _punctuation)\n    .getRegex();\nconst emStrongRDelimAstGfm = edit(emStrongRDelimAstCore, 'gu')\n    .replace(/notPunctSpace/g, _notPunctuationOrSpaceGfmStrongEm)\n    .replace(/punctSpace/g, _punctuationOrSpaceGfmStrongEm)\n    .replace(/punct/g, _punctuationGfmStrongEm)\n    .getRegex();\n// (6) Not allowed for _\nconst emStrongRDelimUnd = edit('^[^_*]*?\\\\*\\\\*[^_*]*?_[^_*]*?(?=\\\\*\\\\*)' // Skip orphan inside strong\n    + '|[^_]+(?=[^_])' // Consume to delim\n    + '|(?!_)punct(_+)(?=[\\\\s]|$)' // (1) #___ can only be a Right Delimiter\n    + '|notPunctSpace(_+)(?!_)(?=punctSpace|$)' // (2) a___#, a___ can only be a Right Delimiter\n    + '|(?!_)punctSpace(_+)(?=notPunctSpace)' // (3) #___a, ___a can only be Left Delimiter\n    + '|[\\\\s](_+)(?!_)(?=punct)' // (4) ___# can only be Left Delimiter\n    + '|(?!_)punct(_+)(?!_)(?=punct)', 'gu') // (5) #___# can be either Left or Right Delimiter\n    .replace(/notPunctSpace/g, _notPunctuationOrSpace)\n    .replace(/punctSpace/g, _punctuationOrSpace)\n    .replace(/punct/g, _punctuation)\n    .getRegex();\nconst anyPunctuation = edit(/\\\\(punct)/, 'gu')\n    .replace(/punct/g, _punctuation)\n    .getRegex();\nconst autolink = edit(/^<(scheme:[^\\s\\x00-\\x1f<>]*|email)>/)\n    .replace('scheme', /[a-zA-Z][a-zA-Z0-9+.-]{1,31}/)\n    .replace('email', /[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/)\n    .getRegex();\nconst _inlineComment = edit(_comment).replace('(?:-->|$)', '-->').getRegex();\nconst tag = edit('^comment'\n    + '|^</[a-zA-Z][\\\\w:-]*\\\\s*>' // self-closing tag\n    + '|^<[a-zA-Z][\\\\w-]*(?:attribute)*?\\\\s*/?>' // open tag\n    + '|^<\\\\?[\\\\s\\\\S]*?\\\\?>' // processing instruction, e.g. <?php ?>\n    + '|^<![a-zA-Z]+\\\\s[\\\\s\\\\S]*?>' // declaration, e.g. <!DOCTYPE html>\n    + '|^<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?\\\\]\\\\]>') // CDATA section\n    .replace('comment', _inlineComment)\n    .replace('attribute', /\\s+[a-zA-Z:_][\\w.:-]*(?:\\s*=\\s*\"[^\"]*\"|\\s*=\\s*'[^']*'|\\s*=\\s*[^\\s\"'=<>`]+)?/)\n    .getRegex();\nconst _inlineLabel = /(?:\\[(?:\\\\.|[^\\[\\]\\\\])*\\]|\\\\.|`[^`]*`|[^\\[\\]\\\\`])*?/;\nconst link = edit(/^!?\\[(label)\\]\\(\\s*(href)(?:\\s+(title))?\\s*\\)/)\n    .replace('label', _inlineLabel)\n    .replace('href', /<(?:\\\\.|[^\\n<>\\\\])+>|[^\\s\\x00-\\x1f]*/)\n    .replace('title', /\"(?:\\\\\"?|[^\"\\\\])*\"|'(?:\\\\'?|[^'\\\\])*'|\\((?:\\\\\\)?|[^)\\\\])*\\)/)\n    .getRegex();\nconst reflink = edit(/^!?\\[(label)\\]\\[(ref)\\]/)\n    .replace('label', _inlineLabel)\n    .replace('ref', _blockLabel)\n    .getRegex();\nconst nolink = edit(/^!?\\[(ref)\\](?:\\[\\])?/)\n    .replace('ref', _blockLabel)\n    .getRegex();\nconst reflinkSearch = edit('reflink|nolink(?!\\\\()', 'g')\n    .replace('reflink', reflink)\n    .replace('nolink', nolink)\n    .getRegex();\n/**\n * Normal Inline Grammar\n */\nconst inlineNormal = {\n    _backpedal: noopTest, // only used for GFM url\n    anyPunctuation,\n    autolink,\n    blockSkip,\n    br,\n    code: inlineCode,\n    del: noopTest,\n    emStrongLDelim,\n    emStrongRDelimAst,\n    emStrongRDelimUnd,\n    escape: escape$1,\n    link,\n    nolink,\n    punctuation,\n    reflink,\n    reflinkSearch,\n    tag,\n    text: inlineText,\n    url: noopTest,\n};\n/**\n * Pedantic Inline Grammar\n */\nconst inlinePedantic = {\n    ...inlineNormal,\n    link: edit(/^!?\\[(label)\\]\\((.*?)\\)/)\n        .replace('label', _inlineLabel)\n        .getRegex(),\n    reflink: edit(/^!?\\[(label)\\]\\s*\\[([^\\]]*)\\]/)\n        .replace('label', _inlineLabel)\n        .getRegex(),\n};\n/**\n * GFM Inline Grammar\n */\nconst inlineGfm = {\n    ...inlineNormal,\n    emStrongRDelimAst: emStrongRDelimAstGfm,\n    emStrongLDelim: emStrongLDelimGfm,\n    url: edit(/^((?:ftp|https?):\\/\\/|www\\.)(?:[a-zA-Z0-9\\-]+\\.?)+[^\\s<]*|^email/, 'i')\n        .replace('email', /[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/)\n        .getRegex(),\n    _backpedal: /(?:[^?!.,:;*_'\"~()&]+|\\([^)]*\\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'\"~)]+(?!$))+/,\n    del: /^(~~?)(?=[^\\s~])((?:\\\\.|[^\\\\])*?(?:\\\\.|[^\\s~\\\\]))\\1(?=[^~]|$)/,\n    text: /^([`~]+|[^`~])(?:(?= {2,}\\n)|(?=[a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-]+@)|[\\s\\S]*?(?:(?=[\\\\<!\\[`*~_]|\\b_|https?:\\/\\/|ftp:\\/\\/|www\\.|$)|[^ ](?= {2,}\\n)|[^a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-](?=[a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-]+@)))/,\n};\n/**\n * GFM + Line Breaks Inline Grammar\n */\nconst inlineBreaks = {\n    ...inlineGfm,\n    br: edit(br).replace('{2,}', '*').getRegex(),\n    text: edit(inlineGfm.text)\n        .replace('\\\\b_', '\\\\b_| {2,}\\\\n')\n        .replace(/\\{2,\\}/g, '*')\n        .getRegex(),\n};\n/**\n * exports\n */\nconst block = {\n    normal: blockNormal,\n    gfm: blockGfm,\n    pedantic: blockPedantic,\n};\nconst inline = {\n    normal: inlineNormal,\n    gfm: inlineGfm,\n    breaks: inlineBreaks,\n    pedantic: inlinePedantic,\n};\n\n/**\n * Helpers\n */\nconst escapeReplacements = {\n    '&': '&amp;',\n    '<': '&lt;',\n    '>': '&gt;',\n    '\"': '&quot;',\n    \"'\": '&#39;',\n};\nconst getEscapeReplacement = (ch) => escapeReplacements[ch];\nfunction escape(html, encode) {\n    if (encode) {\n        if (other.escapeTest.test(html)) {\n            return html.replace(other.escapeReplace, getEscapeReplacement);\n        }\n    }\n    else {\n        if (other.escapeTestNoEncode.test(html)) {\n            return html.replace(other.escapeReplaceNoEncode, getEscapeReplacement);\n        }\n    }\n    return html;\n}\nfunction cleanUrl(href) {\n    try {\n        href = encodeURI(href).replace(other.percentDecode, '%');\n    }\n    catch {\n        return null;\n    }\n    return href;\n}\nfunction splitCells(tableRow, count) {\n    // ensure that every cell-delimiting pipe has a space\n    // before it to distinguish it from an escaped pipe\n    const row = tableRow.replace(other.findPipe, (match, offset, str) => {\n        let escaped = false;\n        let curr = offset;\n        while (--curr >= 0 && str[curr] === '\\\\')\n            escaped = !escaped;\n        if (escaped) {\n            // odd number of slashes means | is escaped\n            // so we leave it alone\n            return '|';\n        }\n        else {\n            // add space before unescaped |\n            return ' |';\n        }\n    }), cells = row.split(other.splitPipe);\n    let i = 0;\n    // First/last cell in a row cannot be empty if it has no leading/trailing pipe\n    if (!cells[0].trim()) {\n        cells.shift();\n    }\n    if (cells.length > 0 && !cells.at(-1)?.trim()) {\n        cells.pop();\n    }\n    if (count) {\n        if (cells.length > count) {\n            cells.splice(count);\n        }\n        else {\n            while (cells.length < count)\n                cells.push('');\n        }\n    }\n    for (; i < cells.length; i++) {\n        // leading or trailing whitespace is ignored per the gfm spec\n        cells[i] = cells[i].trim().replace(other.slashPipe, '|');\n    }\n    return cells;\n}\n/**\n * Remove trailing 'c's. Equivalent to str.replace(/c*$/, '').\n * /c*$/ is vulnerable to REDOS.\n *\n * @param str\n * @param c\n * @param invert Remove suffix of non-c chars instead. Default falsey.\n */\nfunction rtrim(str, c, invert) {\n    const l = str.length;\n    if (l === 0) {\n        return '';\n    }\n    // Length of suffix matching the invert condition.\n    let suffLen = 0;\n    // Step left until we fail to match the invert condition.\n    while (suffLen < l) {\n        const currChar = str.charAt(l - suffLen - 1);\n        if (currChar === c && true) {\n            suffLen++;\n        }\n        else {\n            break;\n        }\n    }\n    return str.slice(0, l - suffLen);\n}\nfunction findClosingBracket(str, b) {\n    if (str.indexOf(b[1]) === -1) {\n        return -1;\n    }\n    let level = 0;\n    for (let i = 0; i < str.length; i++) {\n        if (str[i] === '\\\\') {\n            i++;\n        }\n        else if (str[i] === b[0]) {\n            level++;\n        }\n        else if (str[i] === b[1]) {\n            level--;\n            if (level < 0) {\n                return i;\n            }\n        }\n    }\n    return -1;\n}\n\nfunction outputLink(cap, link, raw, lexer, rules) {\n    const href = link.href;\n    const title = link.title || null;\n    const text = cap[1].replace(rules.other.outputLinkReplace, '$1');\n    if (cap[0].charAt(0) !== '!') {\n        lexer.state.inLink = true;\n        const token = {\n            type: 'link',\n            raw,\n            href,\n            title,\n            text,\n            tokens: lexer.inlineTokens(text),\n        };\n        lexer.state.inLink = false;\n        return token;\n    }\n    return {\n        type: 'image',\n        raw,\n        href,\n        title,\n        text,\n    };\n}\nfunction indentCodeCompensation(raw, text, rules) {\n    const matchIndentToCode = raw.match(rules.other.indentCodeCompensation);\n    if (matchIndentToCode === null) {\n        return text;\n    }\n    const indentToCode = matchIndentToCode[1];\n    return text\n        .split('\\n')\n        .map(node => {\n        const matchIndentInNode = node.match(rules.other.beginningSpace);\n        if (matchIndentInNode === null) {\n            return node;\n        }\n        const [indentInNode] = matchIndentInNode;\n        if (indentInNode.length >= indentToCode.length) {\n            return node.slice(indentToCode.length);\n        }\n        return node;\n    })\n        .join('\\n');\n}\n/**\n * Tokenizer\n */\nclass _Tokenizer {\n    options;\n    rules; // set by the lexer\n    lexer; // set by the lexer\n    constructor(options) {\n        this.options = options || _defaults;\n    }\n    space(src) {\n        const cap = this.rules.block.newline.exec(src);\n        if (cap && cap[0].length > 0) {\n            return {\n                type: 'space',\n                raw: cap[0],\n            };\n        }\n    }\n    code(src) {\n        const cap = this.rules.block.code.exec(src);\n        if (cap) {\n            const text = cap[0].replace(this.rules.other.codeRemoveIndent, '');\n            return {\n                type: 'code',\n                raw: cap[0],\n                codeBlockStyle: 'indented',\n                text: !this.options.pedantic\n                    ? rtrim(text, '\\n')\n                    : text,\n            };\n        }\n    }\n    fences(src) {\n        const cap = this.rules.block.fences.exec(src);\n        if (cap) {\n            const raw = cap[0];\n            const text = indentCodeCompensation(raw, cap[3] || '', this.rules);\n            return {\n                type: 'code',\n                raw,\n                lang: cap[2] ? cap[2].trim().replace(this.rules.inline.anyPunctuation, '$1') : cap[2],\n                text,\n            };\n        }\n    }\n    heading(src) {\n        const cap = this.rules.block.heading.exec(src);\n        if (cap) {\n            let text = cap[2].trim();\n            // remove trailing #s\n            if (this.rules.other.endingHash.test(text)) {\n                const trimmed = rtrim(text, '#');\n                if (this.options.pedantic) {\n                    text = trimmed.trim();\n                }\n                else if (!trimmed || this.rules.other.endingSpaceChar.test(trimmed)) {\n                    // CommonMark requires space before trailing #s\n                    text = trimmed.trim();\n                }\n            }\n            return {\n                type: 'heading',\n                raw: cap[0],\n                depth: cap[1].length,\n                text,\n                tokens: this.lexer.inline(text),\n            };\n        }\n    }\n    hr(src) {\n        const cap = this.rules.block.hr.exec(src);\n        if (cap) {\n            return {\n                type: 'hr',\n                raw: rtrim(cap[0], '\\n'),\n            };\n        }\n    }\n    blockquote(src) {\n        const cap = this.rules.block.blockquote.exec(src);\n        if (cap) {\n            let lines = rtrim(cap[0], '\\n').split('\\n');\n            let raw = '';\n            let text = '';\n            const tokens = [];\n            while (lines.length > 0) {\n                let inBlockquote = false;\n                const currentLines = [];\n                let i;\n                for (i = 0; i < lines.length; i++) {\n                    // get lines up to a continuation\n                    if (this.rules.other.blockquoteStart.test(lines[i])) {\n                        currentLines.push(lines[i]);\n                        inBlockquote = true;\n                    }\n                    else if (!inBlockquote) {\n                        currentLines.push(lines[i]);\n                    }\n                    else {\n                        break;\n                    }\n                }\n                lines = lines.slice(i);\n                const currentRaw = currentLines.join('\\n');\n                const currentText = currentRaw\n                    // precede setext continuation with 4 spaces so it isn't a setext\n                    .replace(this.rules.other.blockquoteSetextReplace, '\\n    $1')\n                    .replace(this.rules.other.blockquoteSetextReplace2, '');\n                raw = raw ? `${raw}\\n${currentRaw}` : currentRaw;\n                text = text ? `${text}\\n${currentText}` : currentText;\n                // parse blockquote lines as top level tokens\n                // merge paragraphs if this is a continuation\n                const top = this.lexer.state.top;\n                this.lexer.state.top = true;\n                this.lexer.blockTokens(currentText, tokens, true);\n                this.lexer.state.top = top;\n                // if there is no continuation then we are done\n                if (lines.length === 0) {\n                    break;\n                }\n                const lastToken = tokens.at(-1);\n                if (lastToken?.type === 'code') {\n                    // blockquote continuation cannot be preceded by a code block\n                    break;\n                }\n                else if (lastToken?.type === 'blockquote') {\n                    // include continuation in nested blockquote\n                    const oldToken = lastToken;\n                    const newText = oldToken.raw + '\\n' + lines.join('\\n');\n                    const newToken = this.blockquote(newText);\n                    tokens[tokens.length - 1] = newToken;\n                    raw = raw.substring(0, raw.length - oldToken.raw.length) + newToken.raw;\n                    text = text.substring(0, text.length - oldToken.text.length) + newToken.text;\n                    break;\n                }\n                else if (lastToken?.type === 'list') {\n                    // include continuation in nested list\n                    const oldToken = lastToken;\n                    const newText = oldToken.raw + '\\n' + lines.join('\\n');\n                    const newToken = this.list(newText);\n                    tokens[tokens.length - 1] = newToken;\n                    raw = raw.substring(0, raw.length - lastToken.raw.length) + newToken.raw;\n                    text = text.substring(0, text.length - oldToken.raw.length) + newToken.raw;\n                    lines = newText.substring(tokens.at(-1).raw.length).split('\\n');\n                    continue;\n                }\n            }\n            return {\n                type: 'blockquote',\n                raw,\n                tokens,\n                text,\n            };\n        }\n    }\n    list(src) {\n        let cap = this.rules.block.list.exec(src);\n        if (cap) {\n            let bull = cap[1].trim();\n            const isordered = bull.length > 1;\n            const list = {\n                type: 'list',\n                raw: '',\n                ordered: isordered,\n                start: isordered ? +bull.slice(0, -1) : '',\n                loose: false,\n                items: [],\n            };\n            bull = isordered ? `\\\\d{1,9}\\\\${bull.slice(-1)}` : `\\\\${bull}`;\n            if (this.options.pedantic) {\n                bull = isordered ? bull : '[*+-]';\n            }\n            // Get next list item\n            const itemRegex = this.rules.other.listItemRegex(bull);\n            let endsWithBlankLine = false;\n            // Check if current bullet point can start a new List Item\n            while (src) {\n                let endEarly = false;\n                let raw = '';\n                let itemContents = '';\n                if (!(cap = itemRegex.exec(src))) {\n                    break;\n                }\n                if (this.rules.block.hr.test(src)) { // End list if bullet was actually HR (possibly move into itemRegex?)\n                    break;\n                }\n                raw = cap[0];\n                src = src.substring(raw.length);\n                let line = cap[2].split('\\n', 1)[0].replace(this.rules.other.listReplaceTabs, (t) => ' '.repeat(3 * t.length));\n                let nextLine = src.split('\\n', 1)[0];\n                let blankLine = !line.trim();\n                let indent = 0;\n                if (this.options.pedantic) {\n                    indent = 2;\n                    itemContents = line.trimStart();\n                }\n                else if (blankLine) {\n                    indent = cap[1].length + 1;\n                }\n                else {\n                    indent = cap[2].search(this.rules.other.nonSpaceChar); // Find first non-space char\n                    indent = indent > 4 ? 1 : indent; // Treat indented code blocks (> 4 spaces) as having only 1 indent\n                    itemContents = line.slice(indent);\n                    indent += cap[1].length;\n                }\n                if (blankLine && this.rules.other.blankLine.test(nextLine)) { // Items begin with at most one blank line\n                    raw += nextLine + '\\n';\n                    src = src.substring(nextLine.length + 1);\n                    endEarly = true;\n                }\n                if (!endEarly) {\n                    const nextBulletRegex = this.rules.other.nextBulletRegex(indent);\n                    const hrRegex = this.rules.other.hrRegex(indent);\n                    const fencesBeginRegex = this.rules.other.fencesBeginRegex(indent);\n                    const headingBeginRegex = this.rules.other.headingBeginRegex(indent);\n                    const htmlBeginRegex = this.rules.other.htmlBeginRegex(indent);\n                    // Check if following lines should be included in List Item\n                    while (src) {\n                        const rawLine = src.split('\\n', 1)[0];\n                        let nextLineWithoutTabs;\n                        nextLine = rawLine;\n                        // Re-align to follow commonmark nesting rules\n                        if (this.options.pedantic) {\n                            nextLine = nextLine.replace(this.rules.other.listReplaceNesting, '  ');\n                            nextLineWithoutTabs = nextLine;\n                        }\n                        else {\n                            nextLineWithoutTabs = nextLine.replace(this.rules.other.tabCharGlobal, '    ');\n                        }\n                        // End list item if found code fences\n                        if (fencesBeginRegex.test(nextLine)) {\n                            break;\n                        }\n                        // End list item if found start of new heading\n                        if (headingBeginRegex.test(nextLine)) {\n                            break;\n                        }\n                        // End list item if found start of html block\n                        if (htmlBeginRegex.test(nextLine)) {\n                            break;\n                        }\n                        // End list item if found start of new bullet\n                        if (nextBulletRegex.test(nextLine)) {\n                            break;\n                        }\n                        // Horizontal rule found\n                        if (hrRegex.test(nextLine)) {\n                            break;\n                        }\n                        if (nextLineWithoutTabs.search(this.rules.other.nonSpaceChar) >= indent || !nextLine.trim()) { // Dedent if possible\n                            itemContents += '\\n' + nextLineWithoutTabs.slice(indent);\n                        }\n                        else {\n                            // not enough indentation\n                            if (blankLine) {\n                                break;\n                            }\n                            // paragraph continuation unless last line was a different block level element\n                            if (line.replace(this.rules.other.tabCharGlobal, '    ').search(this.rules.other.nonSpaceChar) >= 4) { // indented code block\n                                break;\n                            }\n                            if (fencesBeginRegex.test(line)) {\n                                break;\n                            }\n                            if (headingBeginRegex.test(line)) {\n                                break;\n                            }\n                            if (hrRegex.test(line)) {\n                                break;\n                            }\n                            itemContents += '\\n' + nextLine;\n                        }\n                        if (!blankLine && !nextLine.trim()) { // Check if current line is blank\n                            blankLine = true;\n                        }\n                        raw += rawLine + '\\n';\n                        src = src.substring(rawLine.length + 1);\n                        line = nextLineWithoutTabs.slice(indent);\n                    }\n                }\n                if (!list.loose) {\n                    // If the previous item ended with a blank line, the list is loose\n                    if (endsWithBlankLine) {\n                        list.loose = true;\n                    }\n                    else if (this.rules.other.doubleBlankLine.test(raw)) {\n                        endsWithBlankLine = true;\n                    }\n                }\n                let istask = null;\n                let ischecked;\n                // Check for task list items\n                if (this.options.gfm) {\n                    istask = this.rules.other.listIsTask.exec(itemContents);\n                    if (istask) {\n                        ischecked = istask[0] !== '[ ] ';\n                        itemContents = itemContents.replace(this.rules.other.listReplaceTask, '');\n                    }\n                }\n                list.items.push({\n                    type: 'list_item',\n                    raw,\n                    task: !!istask,\n                    checked: ischecked,\n                    loose: false,\n                    text: itemContents,\n                    tokens: [],\n                });\n                list.raw += raw;\n            }\n            // Do not consume newlines at end of final item. Alternatively, make itemRegex *start* with any newlines to simplify/speed up endsWithBlankLine logic\n            const lastItem = list.items.at(-1);\n            if (lastItem) {\n                lastItem.raw = lastItem.raw.trimEnd();\n                lastItem.text = lastItem.text.trimEnd();\n            }\n            else {\n                // not a list since there were no items\n                return;\n            }\n            list.raw = list.raw.trimEnd();\n            // Item child tokens handled here at end because we needed to have the final item to trim it first\n            for (let i = 0; i < list.items.length; i++) {\n                this.lexer.state.top = false;\n                list.items[i].tokens = this.lexer.blockTokens(list.items[i].text, []);\n                if (!list.loose) {\n                    // Check if list should be loose\n                    const spacers = list.items[i].tokens.filter(t => t.type === 'space');\n                    const hasMultipleLineBreaks = spacers.length > 0 && spacers.some(t => this.rules.other.anyLine.test(t.raw));\n                    list.loose = hasMultipleLineBreaks;\n                }\n            }\n            // Set all items to loose if list is loose\n            if (list.loose) {\n                for (let i = 0; i < list.items.length; i++) {\n                    list.items[i].loose = true;\n                }\n            }\n            return list;\n        }\n    }\n    html(src) {\n        const cap = this.rules.block.html.exec(src);\n        if (cap) {\n            const token = {\n                type: 'html',\n                block: true,\n                raw: cap[0],\n                pre: cap[1] === 'pre' || cap[1] === 'script' || cap[1] === 'style',\n                text: cap[0],\n            };\n            return token;\n        }\n    }\n    def(src) {\n        const cap = this.rules.block.def.exec(src);\n        if (cap) {\n            const tag = cap[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal, ' ');\n            const href = cap[2] ? cap[2].replace(this.rules.other.hrefBrackets, '$1').replace(this.rules.inline.anyPunctuation, '$1') : '';\n            const title = cap[3] ? cap[3].substring(1, cap[3].length - 1).replace(this.rules.inline.anyPunctuation, '$1') : cap[3];\n            return {\n                type: 'def',\n                tag,\n                raw: cap[0],\n                href,\n                title,\n            };\n        }\n    }\n    table(src) {\n        const cap = this.rules.block.table.exec(src);\n        if (!cap) {\n            return;\n        }\n        if (!this.rules.other.tableDelimiter.test(cap[2])) {\n            // delimiter row must have a pipe (|) or colon (:) otherwise it is a setext heading\n            return;\n        }\n        const headers = splitCells(cap[1]);\n        const aligns = cap[2].replace(this.rules.other.tableAlignChars, '').split('|');\n        const rows = cap[3]?.trim() ? cap[3].replace(this.rules.other.tableRowBlankLine, '').split('\\n') : [];\n        const item = {\n            type: 'table',\n            raw: cap[0],\n            header: [],\n            align: [],\n            rows: [],\n        };\n        if (headers.length !== aligns.length) {\n            // header and align columns must be equal, rows can be different.\n            return;\n        }\n        for (const align of aligns) {\n            if (this.rules.other.tableAlignRight.test(align)) {\n                item.align.push('right');\n            }\n            else if (this.rules.other.tableAlignCenter.test(align)) {\n                item.align.push('center');\n            }\n            else if (this.rules.other.tableAlignLeft.test(align)) {\n                item.align.push('left');\n            }\n            else {\n                item.align.push(null);\n            }\n        }\n        for (let i = 0; i < headers.length; i++) {\n            item.header.push({\n                text: headers[i],\n                tokens: this.lexer.inline(headers[i]),\n                header: true,\n                align: item.align[i],\n            });\n        }\n        for (const row of rows) {\n            item.rows.push(splitCells(row, item.header.length).map((cell, i) => {\n                return {\n                    text: cell,\n                    tokens: this.lexer.inline(cell),\n                    header: false,\n                    align: item.align[i],\n                };\n            }));\n        }\n        return item;\n    }\n    lheading(src) {\n        const cap = this.rules.block.lheading.exec(src);\n        if (cap) {\n            return {\n                type: 'heading',\n                raw: cap[0],\n                depth: cap[2].charAt(0) === '=' ? 1 : 2,\n                text: cap[1],\n                tokens: this.lexer.inline(cap[1]),\n            };\n        }\n    }\n    paragraph(src) {\n        const cap = this.rules.block.paragraph.exec(src);\n        if (cap) {\n            const text = cap[1].charAt(cap[1].length - 1) === '\\n'\n                ? cap[1].slice(0, -1)\n                : cap[1];\n            return {\n                type: 'paragraph',\n                raw: cap[0],\n                text,\n                tokens: this.lexer.inline(text),\n            };\n        }\n    }\n    text(src) {\n        const cap = this.rules.block.text.exec(src);\n        if (cap) {\n            return {\n                type: 'text',\n                raw: cap[0],\n                text: cap[0],\n                tokens: this.lexer.inline(cap[0]),\n            };\n        }\n    }\n    escape(src) {\n        const cap = this.rules.inline.escape.exec(src);\n        if (cap) {\n            return {\n                type: 'escape',\n                raw: cap[0],\n                text: cap[1],\n            };\n        }\n    }\n    tag(src) {\n        const cap = this.rules.inline.tag.exec(src);\n        if (cap) {\n            if (!this.lexer.state.inLink && this.rules.other.startATag.test(cap[0])) {\n                this.lexer.state.inLink = true;\n            }\n            else if (this.lexer.state.inLink && this.rules.other.endATag.test(cap[0])) {\n                this.lexer.state.inLink = false;\n            }\n            if (!this.lexer.state.inRawBlock && this.rules.other.startPreScriptTag.test(cap[0])) {\n                this.lexer.state.inRawBlock = true;\n            }\n            else if (this.lexer.state.inRawBlock && this.rules.other.endPreScriptTag.test(cap[0])) {\n                this.lexer.state.inRawBlock = false;\n            }\n            return {\n                type: 'html',\n                raw: cap[0],\n                inLink: this.lexer.state.inLink,\n                inRawBlock: this.lexer.state.inRawBlock,\n                block: false,\n                text: cap[0],\n            };\n        }\n    }\n    link(src) {\n        const cap = this.rules.inline.link.exec(src);\n        if (cap) {\n            const trimmedUrl = cap[2].trim();\n            if (!this.options.pedantic && this.rules.other.startAngleBracket.test(trimmedUrl)) {\n                // commonmark requires matching angle brackets\n                if (!(this.rules.other.endAngleBracket.test(trimmedUrl))) {\n                    return;\n                }\n                // ending angle bracket cannot be escaped\n                const rtrimSlash = rtrim(trimmedUrl.slice(0, -1), '\\\\');\n                if ((trimmedUrl.length - rtrimSlash.length) % 2 === 0) {\n                    return;\n                }\n            }\n            else {\n                // find closing parenthesis\n                const lastParenIndex = findClosingBracket(cap[2], '()');\n                if (lastParenIndex > -1) {\n                    const start = cap[0].indexOf('!') === 0 ? 5 : 4;\n                    const linkLen = start + cap[1].length + lastParenIndex;\n                    cap[2] = cap[2].substring(0, lastParenIndex);\n                    cap[0] = cap[0].substring(0, linkLen).trim();\n                    cap[3] = '';\n                }\n            }\n            let href = cap[2];\n            let title = '';\n            if (this.options.pedantic) {\n                // split pedantic href and title\n                const link = this.rules.other.pedanticHrefTitle.exec(href);\n                if (link) {\n                    href = link[1];\n                    title = link[3];\n                }\n            }\n            else {\n                title = cap[3] ? cap[3].slice(1, -1) : '';\n            }\n            href = href.trim();\n            if (this.rules.other.startAngleBracket.test(href)) {\n                if (this.options.pedantic && !(this.rules.other.endAngleBracket.test(trimmedUrl))) {\n                    // pedantic allows starting angle bracket without ending angle bracket\n                    href = href.slice(1);\n                }\n                else {\n                    href = href.slice(1, -1);\n                }\n            }\n            return outputLink(cap, {\n                href: href ? href.replace(this.rules.inline.anyPunctuation, '$1') : href,\n                title: title ? title.replace(this.rules.inline.anyPunctuation, '$1') : title,\n            }, cap[0], this.lexer, this.rules);\n        }\n    }\n    reflink(src, links) {\n        let cap;\n        if ((cap = this.rules.inline.reflink.exec(src))\n            || (cap = this.rules.inline.nolink.exec(src))) {\n            const linkString = (cap[2] || cap[1]).replace(this.rules.other.multipleSpaceGlobal, ' ');\n            const link = links[linkString.toLowerCase()];\n            if (!link) {\n                const text = cap[0].charAt(0);\n                return {\n                    type: 'text',\n                    raw: text,\n                    text,\n                };\n            }\n            return outputLink(cap, link, cap[0], this.lexer, this.rules);\n        }\n    }\n    emStrong(src, maskedSrc, prevChar = '') {\n        let match = this.rules.inline.emStrongLDelim.exec(src);\n        if (!match)\n            return;\n        // _ can't be between two alphanumerics. \\p{L}\\p{N} includes non-english alphabet/numbers as well\n        if (match[3] && prevChar.match(this.rules.other.unicodeAlphaNumeric))\n            return;\n        const nextChar = match[1] || match[2] || '';\n        if (!nextChar || !prevChar || this.rules.inline.punctuation.exec(prevChar)) {\n            // unicode Regex counts emoji as 1 char; spread into array for proper count (used multiple times below)\n            const lLength = [...match[0]].length - 1;\n            let rDelim, rLength, delimTotal = lLength, midDelimTotal = 0;\n            const endReg = match[0][0] === '*' ? this.rules.inline.emStrongRDelimAst : this.rules.inline.emStrongRDelimUnd;\n            endReg.lastIndex = 0;\n            // Clip maskedSrc to same section of string as src (move to lexer?)\n            maskedSrc = maskedSrc.slice(-1 * src.length + lLength);\n            while ((match = endReg.exec(maskedSrc)) != null) {\n                rDelim = match[1] || match[2] || match[3] || match[4] || match[5] || match[6];\n                if (!rDelim)\n                    continue; // skip single * in __abc*abc__\n                rLength = [...rDelim].length;\n                if (match[3] || match[4]) { // found another Left Delim\n                    delimTotal += rLength;\n                    continue;\n                }\n                else if (match[5] || match[6]) { // either Left or Right Delim\n                    if (lLength % 3 && !((lLength + rLength) % 3)) {\n                        midDelimTotal += rLength;\n                        continue; // CommonMark Emphasis Rules 9-10\n                    }\n                }\n                delimTotal -= rLength;\n                if (delimTotal > 0)\n                    continue; // Haven't found enough closing delimiters\n                // Remove extra characters. *a*** -> *a*\n                rLength = Math.min(rLength, rLength + delimTotal + midDelimTotal);\n                // char length can be >1 for unicode characters;\n                const lastCharLength = [...match[0]][0].length;\n                const raw = src.slice(0, lLength + match.index + lastCharLength + rLength);\n                // Create `em` if smallest delimiter has odd char count. *a***\n                if (Math.min(lLength, rLength) % 2) {\n                    const text = raw.slice(1, -1);\n                    return {\n                        type: 'em',\n                        raw,\n                        text,\n                        tokens: this.lexer.inlineTokens(text),\n                    };\n                }\n                // Create 'strong' if smallest delimiter has even char count. **a***\n                const text = raw.slice(2, -2);\n                return {\n                    type: 'strong',\n                    raw,\n                    text,\n                    tokens: this.lexer.inlineTokens(text),\n                };\n            }\n        }\n    }\n    codespan(src) {\n        const cap = this.rules.inline.code.exec(src);\n        if (cap) {\n            let text = cap[2].replace(this.rules.other.newLineCharGlobal, ' ');\n            const hasNonSpaceChars = this.rules.other.nonSpaceChar.test(text);\n            const hasSpaceCharsOnBothEnds = this.rules.other.startingSpaceChar.test(text) && this.rules.other.endingSpaceChar.test(text);\n            if (hasNonSpaceChars && hasSpaceCharsOnBothEnds) {\n                text = text.substring(1, text.length - 1);\n            }\n            return {\n                type: 'codespan',\n                raw: cap[0],\n                text,\n            };\n        }\n    }\n    br(src) {\n        const cap = this.rules.inline.br.exec(src);\n        if (cap) {\n            return {\n                type: 'br',\n                raw: cap[0],\n            };\n        }\n    }\n    del(src) {\n        const cap = this.rules.inline.del.exec(src);\n        if (cap) {\n            return {\n                type: 'del',\n                raw: cap[0],\n                text: cap[2],\n                tokens: this.lexer.inlineTokens(cap[2]),\n            };\n        }\n    }\n    autolink(src) {\n        const cap = this.rules.inline.autolink.exec(src);\n        if (cap) {\n            let text, href;\n            if (cap[2] === '@') {\n                text = cap[1];\n                href = 'mailto:' + text;\n            }\n            else {\n                text = cap[1];\n                href = text;\n            }\n            return {\n                type: 'link',\n                raw: cap[0],\n                text,\n                href,\n                tokens: [\n                    {\n                        type: 'text',\n                        raw: text,\n                        text,\n                    },\n                ],\n            };\n        }\n    }\n    url(src) {\n        let cap;\n        if (cap = this.rules.inline.url.exec(src)) {\n            let text, href;\n            if (cap[2] === '@') {\n                text = cap[0];\n                href = 'mailto:' + text;\n            }\n            else {\n                // do extended autolink path validation\n                let prevCapZero;\n                do {\n                    prevCapZero = cap[0];\n                    cap[0] = this.rules.inline._backpedal.exec(cap[0])?.[0] ?? '';\n                } while (prevCapZero !== cap[0]);\n                text = cap[0];\n                if (cap[1] === 'www.') {\n                    href = 'http://' + cap[0];\n                }\n                else {\n                    href = cap[0];\n                }\n            }\n            return {\n                type: 'link',\n                raw: cap[0],\n                text,\n                href,\n                tokens: [\n                    {\n                        type: 'text',\n                        raw: text,\n                        text,\n                    },\n                ],\n            };\n        }\n    }\n    inlineText(src) {\n        const cap = this.rules.inline.text.exec(src);\n        if (cap) {\n            const escaped = this.lexer.state.inRawBlock;\n            return {\n                type: 'text',\n                raw: cap[0],\n                text: cap[0],\n                escaped,\n            };\n        }\n    }\n}\n\n/**\n * Block Lexer\n */\nclass _Lexer {\n    tokens;\n    options;\n    state;\n    tokenizer;\n    inlineQueue;\n    constructor(options) {\n        // TokenList cannot be created in one go\n        this.tokens = [];\n        this.tokens.links = Object.create(null);\n        this.options = options || _defaults;\n        this.options.tokenizer = this.options.tokenizer || new _Tokenizer();\n        this.tokenizer = this.options.tokenizer;\n        this.tokenizer.options = this.options;\n        this.tokenizer.lexer = this;\n        this.inlineQueue = [];\n        this.state = {\n            inLink: false,\n            inRawBlock: false,\n            top: true,\n        };\n        const rules = {\n            other,\n            block: block.normal,\n            inline: inline.normal,\n        };\n        if (this.options.pedantic) {\n            rules.block = block.pedantic;\n            rules.inline = inline.pedantic;\n        }\n        else if (this.options.gfm) {\n            rules.block = block.gfm;\n            if (this.options.breaks) {\n                rules.inline = inline.breaks;\n            }\n            else {\n                rules.inline = inline.gfm;\n            }\n        }\n        this.tokenizer.rules = rules;\n    }\n    /**\n     * Expose Rules\n     */\n    static get rules() {\n        return {\n            block,\n            inline,\n        };\n    }\n    /**\n     * Static Lex Method\n     */\n    static lex(src, options) {\n        const lexer = new _Lexer(options);\n        return lexer.lex(src);\n    }\n    /**\n     * Static Lex Inline Method\n     */\n    static lexInline(src, options) {\n        const lexer = new _Lexer(options);\n        return lexer.inlineTokens(src);\n    }\n    /**\n     * Preprocessing\n     */\n    lex(src) {\n        src = src.replace(other.carriageReturn, '\\n');\n        this.blockTokens(src, this.tokens);\n        for (let i = 0; i < this.inlineQueue.length; i++) {\n            const next = this.inlineQueue[i];\n            this.inlineTokens(next.src, next.tokens);\n        }\n        this.inlineQueue = [];\n        return this.tokens;\n    }\n    blockTokens(src, tokens = [], lastParagraphClipped = false) {\n        if (this.options.pedantic) {\n            src = src.replace(other.tabCharGlobal, '    ').replace(other.spaceLine, '');\n        }\n        while (src) {\n            let token;\n            if (this.options.extensions?.block?.some((extTokenizer) => {\n                if (token = extTokenizer.call({ lexer: this }, src, tokens)) {\n                    src = src.substring(token.raw.length);\n                    tokens.push(token);\n                    return true;\n                }\n                return false;\n            })) {\n                continue;\n            }\n            // newline\n            if (token = this.tokenizer.space(src)) {\n                src = src.substring(token.raw.length);\n                const lastToken = tokens.at(-1);\n                if (token.raw.length === 1 && lastToken !== undefined) {\n                    // if there's a single \\n as a spacer, it's terminating the last line,\n                    // so move it there so that we don't get unnecessary paragraph tags\n                    lastToken.raw += '\\n';\n                }\n                else {\n                    tokens.push(token);\n                }\n                continue;\n            }\n            // code\n            if (token = this.tokenizer.code(src)) {\n                src = src.substring(token.raw.length);\n                const lastToken = tokens.at(-1);\n                // An indented code block cannot interrupt a paragraph.\n                if (lastToken?.type === 'paragraph' || lastToken?.type === 'text') {\n                    lastToken.raw += '\\n' + token.raw;\n                    lastToken.text += '\\n' + token.text;\n                    this.inlineQueue.at(-1).src = lastToken.text;\n                }\n                else {\n                    tokens.push(token);\n                }\n                continue;\n            }\n            // fences\n            if (token = this.tokenizer.fences(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // heading\n            if (token = this.tokenizer.heading(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // hr\n            if (token = this.tokenizer.hr(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // blockquote\n            if (token = this.tokenizer.blockquote(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // list\n            if (token = this.tokenizer.list(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // html\n            if (token = this.tokenizer.html(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // def\n            if (token = this.tokenizer.def(src)) {\n                src = src.substring(token.raw.length);\n                const lastToken = tokens.at(-1);\n                if (lastToken?.type === 'paragraph' || lastToken?.type === 'text') {\n                    lastToken.raw += '\\n' + token.raw;\n                    lastToken.text += '\\n' + token.raw;\n                    this.inlineQueue.at(-1).src = lastToken.text;\n                }\n                else if (!this.tokens.links[token.tag]) {\n                    this.tokens.links[token.tag] = {\n                        href: token.href,\n                        title: token.title,\n                    };\n                }\n                continue;\n            }\n            // table (gfm)\n            if (token = this.tokenizer.table(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // lheading\n            if (token = this.tokenizer.lheading(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // top-level paragraph\n            // prevent paragraph consuming extensions by clipping 'src' to extension start\n            let cutSrc = src;\n            if (this.options.extensions?.startBlock) {\n                let startIndex = Infinity;\n                const tempSrc = src.slice(1);\n                let tempStart;\n                this.options.extensions.startBlock.forEach((getStartIndex) => {\n                    tempStart = getStartIndex.call({ lexer: this }, tempSrc);\n                    if (typeof tempStart === 'number' && tempStart >= 0) {\n                        startIndex = Math.min(startIndex, tempStart);\n                    }\n                });\n                if (startIndex < Infinity && startIndex >= 0) {\n                    cutSrc = src.substring(0, startIndex + 1);\n                }\n            }\n            if (this.state.top && (token = this.tokenizer.paragraph(cutSrc))) {\n                const lastToken = tokens.at(-1);\n                if (lastParagraphClipped && lastToken?.type === 'paragraph') {\n                    lastToken.raw += '\\n' + token.raw;\n                    lastToken.text += '\\n' + token.text;\n                    this.inlineQueue.pop();\n                    this.inlineQueue.at(-1).src = lastToken.text;\n                }\n                else {\n                    tokens.push(token);\n                }\n                lastParagraphClipped = cutSrc.length !== src.length;\n                src = src.substring(token.raw.length);\n                continue;\n            }\n            // text\n            if (token = this.tokenizer.text(src)) {\n                src = src.substring(token.raw.length);\n                const lastToken = tokens.at(-1);\n                if (lastToken?.type === 'text') {\n                    lastToken.raw += '\\n' + token.raw;\n                    lastToken.text += '\\n' + token.text;\n                    this.inlineQueue.pop();\n                    this.inlineQueue.at(-1).src = lastToken.text;\n                }\n                else {\n                    tokens.push(token);\n                }\n                continue;\n            }\n            if (src) {\n                const errMsg = 'Infinite loop on byte: ' + src.charCodeAt(0);\n                if (this.options.silent) {\n                    console.error(errMsg);\n                    break;\n                }\n                else {\n                    throw new Error(errMsg);\n                }\n            }\n        }\n        this.state.top = true;\n        return tokens;\n    }\n    inline(src, tokens = []) {\n        this.inlineQueue.push({ src, tokens });\n        return tokens;\n    }\n    /**\n     * Lexing/Compiling\n     */\n    inlineTokens(src, tokens = []) {\n        // String with links masked to avoid interference with em and strong\n        let maskedSrc = src;\n        let match = null;\n        // Mask out reflinks\n        if (this.tokens.links) {\n            const links = Object.keys(this.tokens.links);\n            if (links.length > 0) {\n                while ((match = this.tokenizer.rules.inline.reflinkSearch.exec(maskedSrc)) != null) {\n                    if (links.includes(match[0].slice(match[0].lastIndexOf('[') + 1, -1))) {\n                        maskedSrc = maskedSrc.slice(0, match.index)\n                            + '[' + 'a'.repeat(match[0].length - 2) + ']'\n                            + maskedSrc.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex);\n                    }\n                }\n            }\n        }\n        // Mask out other blocks\n        while ((match = this.tokenizer.rules.inline.blockSkip.exec(maskedSrc)) != null) {\n            maskedSrc = maskedSrc.slice(0, match.index) + '[' + 'a'.repeat(match[0].length - 2) + ']' + maskedSrc.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);\n        }\n        // Mask out escaped characters\n        while ((match = this.tokenizer.rules.inline.anyPunctuation.exec(maskedSrc)) != null) {\n            maskedSrc = maskedSrc.slice(0, match.index) + '++' + maskedSrc.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);\n        }\n        let keepPrevChar = false;\n        let prevChar = '';\n        while (src) {\n            if (!keepPrevChar) {\n                prevChar = '';\n            }\n            keepPrevChar = false;\n            let token;\n            // extensions\n            if (this.options.extensions?.inline?.some((extTokenizer) => {\n                if (token = extTokenizer.call({ lexer: this }, src, tokens)) {\n                    src = src.substring(token.raw.length);\n                    tokens.push(token);\n                    return true;\n                }\n                return false;\n            })) {\n                continue;\n            }\n            // escape\n            if (token = this.tokenizer.escape(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // tag\n            if (token = this.tokenizer.tag(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // link\n            if (token = this.tokenizer.link(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // reflink, nolink\n            if (token = this.tokenizer.reflink(src, this.tokens.links)) {\n                src = src.substring(token.raw.length);\n                const lastToken = tokens.at(-1);\n                if (token.type === 'text' && lastToken?.type === 'text') {\n                    lastToken.raw += token.raw;\n                    lastToken.text += token.text;\n                }\n                else {\n                    tokens.push(token);\n                }\n                continue;\n            }\n            // em & strong\n            if (token = this.tokenizer.emStrong(src, maskedSrc, prevChar)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // code\n            if (token = this.tokenizer.codespan(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // br\n            if (token = this.tokenizer.br(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // del (gfm)\n            if (token = this.tokenizer.del(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // autolink\n            if (token = this.tokenizer.autolink(src)) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // url (gfm)\n            if (!this.state.inLink && (token = this.tokenizer.url(src))) {\n                src = src.substring(token.raw.length);\n                tokens.push(token);\n                continue;\n            }\n            // text\n            // prevent inlineText consuming extensions by clipping 'src' to extension start\n            let cutSrc = src;\n            if (this.options.extensions?.startInline) {\n                let startIndex = Infinity;\n                const tempSrc = src.slice(1);\n                let tempStart;\n                this.options.extensions.startInline.forEach((getStartIndex) => {\n                    tempStart = getStartIndex.call({ lexer: this }, tempSrc);\n                    if (typeof tempStart === 'number' && tempStart >= 0) {\n                        startIndex = Math.min(startIndex, tempStart);\n                    }\n                });\n                if (startIndex < Infinity && startIndex >= 0) {\n                    cutSrc = src.substring(0, startIndex + 1);\n                }\n            }\n            if (token = this.tokenizer.inlineText(cutSrc)) {\n                src = src.substring(token.raw.length);\n                if (token.raw.slice(-1) !== '_') { // Track prevChar before string of ____ started\n                    prevChar = token.raw.slice(-1);\n                }\n                keepPrevChar = true;\n                const lastToken = tokens.at(-1);\n                if (lastToken?.type === 'text') {\n                    lastToken.raw += token.raw;\n                    lastToken.text += token.text;\n                }\n                else {\n                    tokens.push(token);\n                }\n                continue;\n            }\n            if (src) {\n                const errMsg = 'Infinite loop on byte: ' + src.charCodeAt(0);\n                if (this.options.silent) {\n                    console.error(errMsg);\n                    break;\n                }\n                else {\n                    throw new Error(errMsg);\n                }\n            }\n        }\n        return tokens;\n    }\n}\n\n/**\n * Renderer\n */\nclass _Renderer {\n    options;\n    parser; // set by the parser\n    constructor(options) {\n        this.options = options || _defaults;\n    }\n    space(token) {\n        return '';\n    }\n    code({ text, lang, escaped }) {\n        const langString = (lang || '').match(other.notSpaceStart)?.[0];\n        const code = text.replace(other.endingNewline, '') + '\\n';\n        if (!langString) {\n            return '<pre><code>'\n                + (escaped ? code : escape(code, true))\n                + '</code></pre>\\n';\n        }\n        return '<pre><code class=\"language-'\n            + escape(langString)\n            + '\">'\n            + (escaped ? code : escape(code, true))\n            + '</code></pre>\\n';\n    }\n    blockquote({ tokens }) {\n        const body = this.parser.parse(tokens);\n        return `<blockquote>\\n${body}</blockquote>\\n`;\n    }\n    html({ text }) {\n        return text;\n    }\n    heading({ tokens, depth }) {\n        return `<h${depth}>${this.parser.parseInline(tokens)}</h${depth}>\\n`;\n    }\n    hr(token) {\n        return '<hr>\\n';\n    }\n    list(token) {\n        const ordered = token.ordered;\n        const start = token.start;\n        let body = '';\n        for (let j = 0; j < token.items.length; j++) {\n            const item = token.items[j];\n            body += this.listitem(item);\n        }\n        const type = ordered ? 'ol' : 'ul';\n        const startAttr = (ordered && start !== 1) ? (' start=\"' + start + '\"') : '';\n        return '<' + type + startAttr + '>\\n' + body + '</' + type + '>\\n';\n    }\n    listitem(item) {\n        let itemBody = '';\n        if (item.task) {\n            const checkbox = this.checkbox({ checked: !!item.checked });\n            if (item.loose) {\n                if (item.tokens[0]?.type === 'paragraph') {\n                    item.tokens[0].text = checkbox + ' ' + item.tokens[0].text;\n                    if (item.tokens[0].tokens && item.tokens[0].tokens.length > 0 && item.tokens[0].tokens[0].type === 'text') {\n                        item.tokens[0].tokens[0].text = checkbox + ' ' + escape(item.tokens[0].tokens[0].text);\n                        item.tokens[0].tokens[0].escaped = true;\n                    }\n                }\n                else {\n                    item.tokens.unshift({\n                        type: 'text',\n                        raw: checkbox + ' ',\n                        text: checkbox + ' ',\n                        escaped: true,\n                    });\n                }\n            }\n            else {\n                itemBody += checkbox + ' ';\n            }\n        }\n        itemBody += this.parser.parse(item.tokens, !!item.loose);\n        return `<li>${itemBody}</li>\\n`;\n    }\n    checkbox({ checked }) {\n        return '<input '\n            + (checked ? 'checked=\"\" ' : '')\n            + 'disabled=\"\" type=\"checkbox\">';\n    }\n    paragraph({ tokens }) {\n        return `<p>${this.parser.parseInline(tokens)}</p>\\n`;\n    }\n    table(token) {\n        let header = '';\n        // header\n        let cell = '';\n        for (let j = 0; j < token.header.length; j++) {\n            cell += this.tablecell(token.header[j]);\n        }\n        header += this.tablerow({ text: cell });\n        let body = '';\n        for (let j = 0; j < token.rows.length; j++) {\n            const row = token.rows[j];\n            cell = '';\n            for (let k = 0; k < row.length; k++) {\n                cell += this.tablecell(row[k]);\n            }\n            body += this.tablerow({ text: cell });\n        }\n        if (body)\n            body = `<tbody>${body}</tbody>`;\n        return '<table>\\n'\n            + '<thead>\\n'\n            + header\n            + '</thead>\\n'\n            + body\n            + '</table>\\n';\n    }\n    tablerow({ text }) {\n        return `<tr>\\n${text}</tr>\\n`;\n    }\n    tablecell(token) {\n        const content = this.parser.parseInline(token.tokens);\n        const type = token.header ? 'th' : 'td';\n        const tag = token.align\n            ? `<${type} align=\"${token.align}\">`\n            : `<${type}>`;\n        return tag + content + `</${type}>\\n`;\n    }\n    /**\n     * span level renderer\n     */\n    strong({ tokens }) {\n        return `<strong>${this.parser.parseInline(tokens)}</strong>`;\n    }\n    em({ tokens }) {\n        return `<em>${this.parser.parseInline(tokens)}</em>`;\n    }\n    codespan({ text }) {\n        return `<code>${escape(text, true)}</code>`;\n    }\n    br(token) {\n        return '<br>';\n    }\n    del({ tokens }) {\n        return `<del>${this.parser.parseInline(tokens)}</del>`;\n    }\n    link({ href, title, tokens }) {\n        const text = this.parser.parseInline(tokens);\n        const cleanHref = cleanUrl(href);\n        if (cleanHref === null) {\n            return text;\n        }\n        href = cleanHref;\n        let out = '<a href=\"' + href + '\"';\n        if (title) {\n            out += ' title=\"' + (escape(title)) + '\"';\n        }\n        out += '>' + text + '</a>';\n        return out;\n    }\n    image({ href, title, text }) {\n        const cleanHref = cleanUrl(href);\n        if (cleanHref === null) {\n            return escape(text);\n        }\n        href = cleanHref;\n        let out = `<img src=\"${href}\" alt=\"${text}\"`;\n        if (title) {\n            out += ` title=\"${escape(title)}\"`;\n        }\n        out += '>';\n        return out;\n    }\n    text(token) {\n        return 'tokens' in token && token.tokens\n            ? this.parser.parseInline(token.tokens)\n            : ('escaped' in token && token.escaped ? token.text : escape(token.text));\n    }\n}\n\n/**\n * TextRenderer\n * returns only the textual part of the token\n */\nclass _TextRenderer {\n    // no need for block level renderers\n    strong({ text }) {\n        return text;\n    }\n    em({ text }) {\n        return text;\n    }\n    codespan({ text }) {\n        return text;\n    }\n    del({ text }) {\n        return text;\n    }\n    html({ text }) {\n        return text;\n    }\n    text({ text }) {\n        return text;\n    }\n    link({ text }) {\n        return '' + text;\n    }\n    image({ text }) {\n        return '' + text;\n    }\n    br() {\n        return '';\n    }\n}\n\n/**\n * Parsing & Compiling\n */\nclass _Parser {\n    options;\n    renderer;\n    textRenderer;\n    constructor(options) {\n        this.options = options || _defaults;\n        this.options.renderer = this.options.renderer || new _Renderer();\n        this.renderer = this.options.renderer;\n        this.renderer.options = this.options;\n        this.renderer.parser = this;\n        this.textRenderer = new _TextRenderer();\n    }\n    /**\n     * Static Parse Method\n     */\n    static parse(tokens, options) {\n        const parser = new _Parser(options);\n        return parser.parse(tokens);\n    }\n    /**\n     * Static Parse Inline Method\n     */\n    static parseInline(tokens, options) {\n        const parser = new _Parser(options);\n        return parser.parseInline(tokens);\n    }\n    /**\n     * Parse Loop\n     */\n    parse(tokens, top = true) {\n        let out = '';\n        for (let i = 0; i < tokens.length; i++) {\n            const anyToken = tokens[i];\n            // Run any renderer extensions\n            if (this.options.extensions?.renderers?.[anyToken.type]) {\n                const genericToken = anyToken;\n                const ret = this.options.extensions.renderers[genericToken.type].call({ parser: this }, genericToken);\n                if (ret !== false || !['space', 'hr', 'heading', 'code', 'table', 'blockquote', 'list', 'html', 'paragraph', 'text'].includes(genericToken.type)) {\n                    out += ret || '';\n                    continue;\n                }\n            }\n            const token = anyToken;\n            switch (token.type) {\n                case 'space': {\n                    out += this.renderer.space(token);\n                    continue;\n                }\n                case 'hr': {\n                    out += this.renderer.hr(token);\n                    continue;\n                }\n                case 'heading': {\n                    out += this.renderer.heading(token);\n                    continue;\n                }\n                case 'code': {\n                    out += this.renderer.code(token);\n                    continue;\n                }\n                case 'table': {\n                    out += this.renderer.table(token);\n                    continue;\n                }\n                case 'blockquote': {\n                    out += this.renderer.blockquote(token);\n                    continue;\n                }\n                case 'list': {\n                    out += this.renderer.list(token);\n                    continue;\n                }\n                case 'html': {\n                    out += this.renderer.html(token);\n                    continue;\n                }\n                case 'paragraph': {\n                    out += this.renderer.paragraph(token);\n                    continue;\n                }\n                case 'text': {\n                    let textToken = token;\n                    let body = this.renderer.text(textToken);\n                    while (i + 1 < tokens.length && tokens[i + 1].type === 'text') {\n                        textToken = tokens[++i];\n                        body += '\\n' + this.renderer.text(textToken);\n                    }\n                    if (top) {\n                        out += this.renderer.paragraph({\n                            type: 'paragraph',\n                            raw: body,\n                            text: body,\n                            tokens: [{ type: 'text', raw: body, text: body, escaped: true }],\n                        });\n                    }\n                    else {\n                        out += body;\n                    }\n                    continue;\n                }\n                default: {\n                    const errMsg = 'Token with \"' + token.type + '\" type was not found.';\n                    if (this.options.silent) {\n                        console.error(errMsg);\n                        return '';\n                    }\n                    else {\n                        throw new Error(errMsg);\n                    }\n                }\n            }\n        }\n        return out;\n    }\n    /**\n     * Parse Inline Tokens\n     */\n    parseInline(tokens, renderer = this.renderer) {\n        let out = '';\n        for (let i = 0; i < tokens.length; i++) {\n            const anyToken = tokens[i];\n            // Run any renderer extensions\n            if (this.options.extensions?.renderers?.[anyToken.type]) {\n                const ret = this.options.extensions.renderers[anyToken.type].call({ parser: this }, anyToken);\n                if (ret !== false || !['escape', 'html', 'link', 'image', 'strong', 'em', 'codespan', 'br', 'del', 'text'].includes(anyToken.type)) {\n                    out += ret || '';\n                    continue;\n                }\n            }\n            const token = anyToken;\n            switch (token.type) {\n                case 'escape': {\n                    out += renderer.text(token);\n                    break;\n                }\n                case 'html': {\n                    out += renderer.html(token);\n                    break;\n                }\n                case 'link': {\n                    out += renderer.link(token);\n                    break;\n                }\n                case 'image': {\n                    out += renderer.image(token);\n                    break;\n                }\n                case 'strong': {\n                    out += renderer.strong(token);\n                    break;\n                }\n                case 'em': {\n                    out += renderer.em(token);\n                    break;\n                }\n                case 'codespan': {\n                    out += renderer.codespan(token);\n                    break;\n                }\n                case 'br': {\n                    out += renderer.br(token);\n                    break;\n                }\n                case 'del': {\n                    out += renderer.del(token);\n                    break;\n                }\n                case 'text': {\n                    out += renderer.text(token);\n                    break;\n                }\n                default: {\n                    const errMsg = 'Token with \"' + token.type + '\" type was not found.';\n                    if (this.options.silent) {\n                        console.error(errMsg);\n                        return '';\n                    }\n                    else {\n                        throw new Error(errMsg);\n                    }\n                }\n            }\n        }\n        return out;\n    }\n}\n\nclass _Hooks {\n    options;\n    block;\n    constructor(options) {\n        this.options = options || _defaults;\n    }\n    static passThroughHooks = new Set([\n        'preprocess',\n        'postprocess',\n        'processAllTokens',\n    ]);\n    /**\n     * Process markdown before marked\n     */\n    preprocess(markdown) {\n        return markdown;\n    }\n    /**\n     * Process HTML after marked is finished\n     */\n    postprocess(html) {\n        return html;\n    }\n    /**\n     * Process all tokens before walk tokens\n     */\n    processAllTokens(tokens) {\n        return tokens;\n    }\n    /**\n     * Provide function to tokenize markdown\n     */\n    provideLexer() {\n        return this.block ? _Lexer.lex : _Lexer.lexInline;\n    }\n    /**\n     * Provide function to parse tokens\n     */\n    provideParser() {\n        return this.block ? _Parser.parse : _Parser.parseInline;\n    }\n}\n\nclass Marked {\n    defaults = _getDefaults();\n    options = this.setOptions;\n    parse = this.parseMarkdown(true);\n    parseInline = this.parseMarkdown(false);\n    Parser = _Parser;\n    Renderer = _Renderer;\n    TextRenderer = _TextRenderer;\n    Lexer = _Lexer;\n    Tokenizer = _Tokenizer;\n    Hooks = _Hooks;\n    constructor(...args) {\n        this.use(...args);\n    }\n    /**\n     * Run callback for every token\n     */\n    walkTokens(tokens, callback) {\n        let values = [];\n        for (const token of tokens) {\n            values = values.concat(callback.call(this, token));\n            switch (token.type) {\n                case 'table': {\n                    const tableToken = token;\n                    for (const cell of tableToken.header) {\n                        values = values.concat(this.walkTokens(cell.tokens, callback));\n                    }\n                    for (const row of tableToken.rows) {\n                        for (const cell of row) {\n                            values = values.concat(this.walkTokens(cell.tokens, callback));\n                        }\n                    }\n                    break;\n                }\n                case 'list': {\n                    const listToken = token;\n                    values = values.concat(this.walkTokens(listToken.items, callback));\n                    break;\n                }\n                default: {\n                    const genericToken = token;\n                    if (this.defaults.extensions?.childTokens?.[genericToken.type]) {\n                        this.defaults.extensions.childTokens[genericToken.type].forEach((childTokens) => {\n                            const tokens = genericToken[childTokens].flat(Infinity);\n                            values = values.concat(this.walkTokens(tokens, callback));\n                        });\n                    }\n                    else if (genericToken.tokens) {\n                        values = values.concat(this.walkTokens(genericToken.tokens, callback));\n                    }\n                }\n            }\n        }\n        return values;\n    }\n    use(...args) {\n        const extensions = this.defaults.extensions || { renderers: {}, childTokens: {} };\n        args.forEach((pack) => {\n            // copy options to new object\n            const opts = { ...pack };\n            // set async to true if it was set to true before\n            opts.async = this.defaults.async || opts.async || false;\n            // ==-- Parse \"addon\" extensions --== //\n            if (pack.extensions) {\n                pack.extensions.forEach((ext) => {\n                    if (!ext.name) {\n                        throw new Error('extension name required');\n                    }\n                    if ('renderer' in ext) { // Renderer extensions\n                        const prevRenderer = extensions.renderers[ext.name];\n                        if (prevRenderer) {\n                            // Replace extension with func to run new extension but fall back if false\n                            extensions.renderers[ext.name] = function (...args) {\n                                let ret = ext.renderer.apply(this, args);\n                                if (ret === false) {\n                                    ret = prevRenderer.apply(this, args);\n                                }\n                                return ret;\n                            };\n                        }\n                        else {\n                            extensions.renderers[ext.name] = ext.renderer;\n                        }\n                    }\n                    if ('tokenizer' in ext) { // Tokenizer Extensions\n                        if (!ext.level || (ext.level !== 'block' && ext.level !== 'inline')) {\n                            throw new Error(\"extension level must be 'block' or 'inline'\");\n                        }\n                        const extLevel = extensions[ext.level];\n                        if (extLevel) {\n                            extLevel.unshift(ext.tokenizer);\n                        }\n                        else {\n                            extensions[ext.level] = [ext.tokenizer];\n                        }\n                        if (ext.start) { // Function to check for start of token\n                            if (ext.level === 'block') {\n                                if (extensions.startBlock) {\n                                    extensions.startBlock.push(ext.start);\n                                }\n                                else {\n                                    extensions.startBlock = [ext.start];\n                                }\n                            }\n                            else if (ext.level === 'inline') {\n                                if (extensions.startInline) {\n                                    extensions.startInline.push(ext.start);\n                                }\n                                else {\n                                    extensions.startInline = [ext.start];\n                                }\n                            }\n                        }\n                    }\n                    if ('childTokens' in ext && ext.childTokens) { // Child tokens to be visited by walkTokens\n                        extensions.childTokens[ext.name] = ext.childTokens;\n                    }\n                });\n                opts.extensions = extensions;\n            }\n            // ==-- Parse \"overwrite\" extensions --== //\n            if (pack.renderer) {\n                const renderer = this.defaults.renderer || new _Renderer(this.defaults);\n                for (const prop in pack.renderer) {\n                    if (!(prop in renderer)) {\n                        throw new Error(`renderer '${prop}' does not exist`);\n                    }\n                    if (['options', 'parser'].includes(prop)) {\n                        // ignore options property\n                        continue;\n                    }\n                    const rendererProp = prop;\n                    const rendererFunc = pack.renderer[rendererProp];\n                    const prevRenderer = renderer[rendererProp];\n                    // Replace renderer with func to run extension, but fall back if false\n                    renderer[rendererProp] = (...args) => {\n                        let ret = rendererFunc.apply(renderer, args);\n                        if (ret === false) {\n                            ret = prevRenderer.apply(renderer, args);\n                        }\n                        return ret || '';\n                    };\n                }\n                opts.renderer = renderer;\n            }\n            if (pack.tokenizer) {\n                const tokenizer = this.defaults.tokenizer || new _Tokenizer(this.defaults);\n                for (const prop in pack.tokenizer) {\n                    if (!(prop in tokenizer)) {\n                        throw new Error(`tokenizer '${prop}' does not exist`);\n                    }\n                    if (['options', 'rules', 'lexer'].includes(prop)) {\n                        // ignore options, rules, and lexer properties\n                        continue;\n                    }\n                    const tokenizerProp = prop;\n                    const tokenizerFunc = pack.tokenizer[tokenizerProp];\n                    const prevTokenizer = tokenizer[tokenizerProp];\n                    // Replace tokenizer with func to run extension, but fall back if false\n                    // @ts-expect-error cannot type tokenizer function dynamically\n                    tokenizer[tokenizerProp] = (...args) => {\n                        let ret = tokenizerFunc.apply(tokenizer, args);\n                        if (ret === false) {\n                            ret = prevTokenizer.apply(tokenizer, args);\n                        }\n                        return ret;\n                    };\n                }\n                opts.tokenizer = tokenizer;\n            }\n            // ==-- Parse Hooks extensions --== //\n            if (pack.hooks) {\n                const hooks = this.defaults.hooks || new _Hooks();\n                for (const prop in pack.hooks) {\n                    if (!(prop in hooks)) {\n                        throw new Error(`hook '${prop}' does not exist`);\n                    }\n                    if (['options', 'block'].includes(prop)) {\n                        // ignore options and block properties\n                        continue;\n                    }\n                    const hooksProp = prop;\n                    const hooksFunc = pack.hooks[hooksProp];\n                    const prevHook = hooks[hooksProp];\n                    if (_Hooks.passThroughHooks.has(prop)) {\n                        // @ts-expect-error cannot type hook function dynamically\n                        hooks[hooksProp] = (arg) => {\n                            if (this.defaults.async) {\n                                return Promise.resolve(hooksFunc.call(hooks, arg)).then(ret => {\n                                    return prevHook.call(hooks, ret);\n                                });\n                            }\n                            const ret = hooksFunc.call(hooks, arg);\n                            return prevHook.call(hooks, ret);\n                        };\n                    }\n                    else {\n                        // @ts-expect-error cannot type hook function dynamically\n                        hooks[hooksProp] = (...args) => {\n                            let ret = hooksFunc.apply(hooks, args);\n                            if (ret === false) {\n                                ret = prevHook.apply(hooks, args);\n                            }\n                            return ret;\n                        };\n                    }\n                }\n                opts.hooks = hooks;\n            }\n            // ==-- Parse WalkTokens extensions --== //\n            if (pack.walkTokens) {\n                const walkTokens = this.defaults.walkTokens;\n                const packWalktokens = pack.walkTokens;\n                opts.walkTokens = function (token) {\n                    let values = [];\n                    values.push(packWalktokens.call(this, token));\n                    if (walkTokens) {\n                        values = values.concat(walkTokens.call(this, token));\n                    }\n                    return values;\n                };\n            }\n            this.defaults = { ...this.defaults, ...opts };\n        });\n        return this;\n    }\n    setOptions(opt) {\n        this.defaults = { ...this.defaults, ...opt };\n        return this;\n    }\n    lexer(src, options) {\n        return _Lexer.lex(src, options ?? this.defaults);\n    }\n    parser(tokens, options) {\n        return _Parser.parse(tokens, options ?? this.defaults);\n    }\n    parseMarkdown(blockType) {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        const parse = (src, options) => {\n            const origOpt = { ...options };\n            const opt = { ...this.defaults, ...origOpt };\n            const throwError = this.onError(!!opt.silent, !!opt.async);\n            // throw error if an extension set async to true but parse was called with async: false\n            if (this.defaults.async === true && origOpt.async === false) {\n                return throwError(new Error('marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise.'));\n            }\n            // throw error in case of non string input\n            if (typeof src === 'undefined' || src === null) {\n                return throwError(new Error('marked(): input parameter is undefined or null'));\n            }\n            if (typeof src !== 'string') {\n                return throwError(new Error('marked(): input parameter is of type '\n                    + Object.prototype.toString.call(src) + ', string expected'));\n            }\n            if (opt.hooks) {\n                opt.hooks.options = opt;\n                opt.hooks.block = blockType;\n            }\n            const lexer = opt.hooks ? opt.hooks.provideLexer() : (blockType ? _Lexer.lex : _Lexer.lexInline);\n            const parser = opt.hooks ? opt.hooks.provideParser() : (blockType ? _Parser.parse : _Parser.parseInline);\n            if (opt.async) {\n                return Promise.resolve(opt.hooks ? opt.hooks.preprocess(src) : src)\n                    .then(src => lexer(src, opt))\n                    .then(tokens => opt.hooks ? opt.hooks.processAllTokens(tokens) : tokens)\n                    .then(tokens => opt.walkTokens ? Promise.all(this.walkTokens(tokens, opt.walkTokens)).then(() => tokens) : tokens)\n                    .then(tokens => parser(tokens, opt))\n                    .then(html => opt.hooks ? opt.hooks.postprocess(html) : html)\n                    .catch(throwError);\n            }\n            try {\n                if (opt.hooks) {\n                    src = opt.hooks.preprocess(src);\n                }\n                let tokens = lexer(src, opt);\n                if (opt.hooks) {\n                    tokens = opt.hooks.processAllTokens(tokens);\n                }\n                if (opt.walkTokens) {\n                    this.walkTokens(tokens, opt.walkTokens);\n                }\n                let html = parser(tokens, opt);\n                if (opt.hooks) {\n                    html = opt.hooks.postprocess(html);\n                }\n                return html;\n            }\n            catch (e) {\n                return throwError(e);\n            }\n        };\n        return parse;\n    }\n    onError(silent, async) {\n        return (e) => {\n            e.message += '\\nPlease report this to https://github.com/markedjs/marked.';\n            if (silent) {\n                const msg = '<p>An error occurred:</p><pre>'\n                    + escape(e.message + '', true)\n                    + '</pre>';\n                if (async) {\n                    return Promise.resolve(msg);\n                }\n                return msg;\n            }\n            if (async) {\n                return Promise.reject(e);\n            }\n            throw e;\n        };\n    }\n}\n\nconst markedInstance = new Marked();\nfunction marked(src, opt) {\n    return markedInstance.parse(src, opt);\n}\n/**\n * Sets the default options.\n *\n * @param options Hash of options\n */\nmarked.options =\n    marked.setOptions = function (options) {\n        markedInstance.setOptions(options);\n        marked.defaults = markedInstance.defaults;\n        changeDefaults(marked.defaults);\n        return marked;\n    };\n/**\n * Gets the original marked default options.\n */\nmarked.getDefaults = _getDefaults;\nmarked.defaults = _defaults;\n/**\n * Use Extension\n */\nmarked.use = function (...args) {\n    markedInstance.use(...args);\n    marked.defaults = markedInstance.defaults;\n    changeDefaults(marked.defaults);\n    return marked;\n};\n/**\n * Run callback for every token\n */\nmarked.walkTokens = function (tokens, callback) {\n    return markedInstance.walkTokens(tokens, callback);\n};\n/**\n * Compiles markdown to HTML without enclosing `p` tag.\n *\n * @param src String of markdown source to be compiled\n * @param options Hash of options\n * @return String of compiled HTML\n */\nmarked.parseInline = markedInstance.parseInline;\n/**\n * Expose\n */\nmarked.Parser = _Parser;\nmarked.parser = _Parser.parse;\nmarked.Renderer = _Renderer;\nmarked.TextRenderer = _TextRenderer;\nmarked.Lexer = _Lexer;\nmarked.lexer = _Lexer.lex;\nmarked.Tokenizer = _Tokenizer;\nmarked.Hooks = _Hooks;\nmarked.parse = marked;\nconst options = marked.options;\nconst setOptions = marked.setOptions;\nconst use = marked.use;\nconst walkTokens = marked.walkTokens;\nconst parseInline = marked.parseInline;\nconst parse = marked;\nconst parser = _Parser.parse;\nconst lexer = _Lexer.lex;\n\nexport { _Hooks as Hooks, _Lexer as Lexer, Marked, _Parser as Parser, _Renderer as Renderer, _TextRenderer as TextRenderer, _Tokenizer as Tokenizer, _defaults as defaults, _getDefaults as getDefaults, lexer, marked, options, parse, parseInline, parser, setOptions, use, walkTokens };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,SAASA,YAAYA,CAAA,EAAG;EACpB,OAAO;IACHC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,UAAU,EAAE,IAAI;IAChBC,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE,IAAI;IACXC,QAAQ,EAAE,KAAK;IACfC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE,KAAK;IACbC,SAAS,EAAE,IAAI;IACfC,UAAU,EAAE;EAChB,CAAC;AACL;AACA,IAAIC,SAAS,GAAGX,YAAY,CAAC,CAAC;AAC9B,SAASY,cAAcA,CAACC,WAAW,EAAE;EACjCF,SAAS,GAAGE,WAAW;AAC3B;AAEA,MAAMC,QAAQ,GAAG;EAAEC,IAAI,EAAEA,CAAA,KAAM;AAAK,CAAC;AACrC,SAASC,IAAIA,CAACC,KAAK,EAAEC,GAAG,GAAG,EAAE,EAAE;EAC3B,IAAIC,MAAM,GAAG,OAAOF,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGA,KAAK,CAACE,MAAM;EAC7D,MAAMC,GAAG,GAAG;IACRC,OAAO,EAAEA,CAACC,IAAI,EAAEC,GAAG,KAAK;MACpB,IAAIC,SAAS,GAAG,OAAOD,GAAG,KAAK,QAAQ,GAAGA,GAAG,GAAGA,GAAG,CAACJ,MAAM;MAC1DK,SAAS,GAAGA,SAAS,CAACH,OAAO,CAACI,KAAK,CAACC,KAAK,EAAE,IAAI,CAAC;MAChDP,MAAM,GAAGA,MAAM,CAACE,OAAO,CAACC,IAAI,EAAEE,SAAS,CAAC;MACxC,OAAOJ,GAAG;IACd,CAAC;IACDO,QAAQ,EAAEA,CAAA,KAAM;MACZ,OAAO,IAAIC,MAAM,CAACT,MAAM,EAAED,GAAG,CAAC;IAClC;EACJ,CAAC;EACD,OAAOE,GAAG;AACd;AACA,MAAMK,KAAK,GAAG;EACVI,gBAAgB,EAAE,wBAAwB;EAC1CC,iBAAiB,EAAE,aAAa;EAChCC,sBAAsB,EAAE,eAAe;EACvCC,cAAc,EAAE,MAAM;EACtBC,UAAU,EAAE,IAAI;EAChBC,iBAAiB,EAAE,IAAI;EACvBC,eAAe,EAAE,IAAI;EACrBC,YAAY,EAAE,MAAM;EACpBC,iBAAiB,EAAE,KAAK;EACxBC,aAAa,EAAE,KAAK;EACpBC,mBAAmB,EAAE,MAAM;EAC3BC,SAAS,EAAE,UAAU;EACrBC,eAAe,EAAE,mBAAmB;EACpCC,eAAe,EAAE,UAAU;EAC3BC,uBAAuB,EAAE,gCAAgC;EACzDC,wBAAwB,EAAE,kBAAkB;EAC5CC,eAAe,EAAE,MAAM;EACvBC,kBAAkB,EAAE,yBAAyB;EAC7CC,UAAU,EAAE,aAAa;EACzBC,eAAe,EAAE,cAAc;EAC/BC,OAAO,EAAE,QAAQ;EACjBC,YAAY,EAAE,UAAU;EACxBC,cAAc,EAAE,MAAM;EACtBC,eAAe,EAAE,YAAY;EAC7BC,iBAAiB,EAAE,WAAW;EAC9BC,eAAe,EAAE,WAAW;EAC5BC,gBAAgB,EAAE,YAAY;EAC9BC,cAAc,EAAE,WAAW;EAC3BC,SAAS,EAAE,OAAO;EAClBC,OAAO,EAAE,SAAS;EAClBC,iBAAiB,EAAE,gCAAgC;EACnDC,eAAe,EAAE,kCAAkC;EACnDC,iBAAiB,EAAE,IAAI;EACvBC,eAAe,EAAE,IAAI;EACrBC,iBAAiB,EAAE,+BAA+B;EAClDC,mBAAmB,EAAE,eAAe;EACpCC,UAAU,EAAE,SAAS;EACrBC,aAAa,EAAE,UAAU;EACzBC,kBAAkB,EAAE,mDAAmD;EACvEC,qBAAqB,EAAE,oDAAoD;EAC3EC,YAAY,EAAE,4CAA4C;EAC1D3C,KAAK,EAAE,cAAc;EACrB4C,aAAa,EAAE,MAAM;EACrBC,QAAQ,EAAE,KAAK;EACfC,SAAS,EAAE,KAAK;EAChBC,SAAS,EAAE,OAAO;EAClBC,cAAc,EAAE,UAAU;EAC1BC,SAAS,EAAE,QAAQ;EACnBC,aAAa,EAAE,MAAM;EACrBC,aAAa,EAAE,KAAK;EACpBC,aAAa,EAAGC,IAAI,IAAK,IAAInD,MAAM,CAAE,WAAUmD,IAAK,+BAA8B,CAAC;EACnFC,eAAe,EAAGC,MAAM,IAAK,IAAIrD,MAAM,CAAE,QAAOsD,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEF,MAAM,GAAG,CAAC,CAAE,qDAAoD,CAAC;EAC7HG,OAAO,EAAGH,MAAM,IAAK,IAAIrD,MAAM,CAAE,QAAOsD,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEF,MAAM,GAAG,CAAC,CAAE,oDAAmD,CAAC;EACpHI,gBAAgB,EAAGJ,MAAM,IAAK,IAAIrD,MAAM,CAAE,QAAOsD,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEF,MAAM,GAAG,CAAC,CAAE,iBAAgB,CAAC;EAC1FK,iBAAiB,EAAGL,MAAM,IAAK,IAAIrD,MAAM,CAAE,QAAOsD,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEF,MAAM,GAAG,CAAC,CAAE,IAAG,CAAC;EAC9EM,cAAc,EAAGN,MAAM,IAAK,IAAIrD,MAAM,CAAE,QAAOsD,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEF,MAAM,GAAG,CAAC,CAAE,oBAAmB,EAAE,GAAG;AACnG,CAAC;AACD;AACA;AACA;AACA,MAAMO,OAAO,GAAG,sBAAsB;AACtC,MAAMC,SAAS,GAAG,uDAAuD;AACzE,MAAMC,MAAM,GAAG,6GAA6G;AAC5H,MAAMC,EAAE,GAAG,oEAAoE;AAC/E,MAAMC,OAAO,GAAG,sCAAsC;AACtD,MAAMC,MAAM,GAAG,uBAAuB;AACtC,MAAMC,YAAY,GAAG,gKAAgK;AACrL,MAAMC,QAAQ,GAAG/E,IAAI,CAAC8E,YAAY,CAAC,CAC9BzE,OAAO,CAAC,OAAO,EAAEwE,MAAM,CAAC,CAAC;AAAA,CACzBxE,OAAO,CAAC,YAAY,EAAE,mBAAmB,CAAC,CAAC;AAAA,CAC3CA,OAAO,CAAC,SAAS,EAAE,uBAAuB,CAAC,CAAC;AAAA,CAC5CA,OAAO,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;AAAA,CAClCA,OAAO,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;AAAA,CACpCA,OAAO,CAAC,OAAO,EAAE,mBAAmB,CAAC,CAAC;AAAA,CACtCA,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;AAAA,CACxBM,QAAQ,CAAC,CAAC;AACf,MAAMqE,WAAW,GAAGhF,IAAI,CAAC8E,YAAY,CAAC,CACjCzE,OAAO,CAAC,OAAO,EAAEwE,MAAM,CAAC,CAAC;AAAA,CACzBxE,OAAO,CAAC,YAAY,EAAE,mBAAmB,CAAC,CAAC;AAAA,CAC3CA,OAAO,CAAC,SAAS,EAAE,uBAAuB,CAAC,CAAC;AAAA,CAC5CA,OAAO,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;AAAA,CAClCA,OAAO,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;AAAA,CACpCA,OAAO,CAAC,OAAO,EAAE,mBAAmB,CAAC,CAAC;AAAA,CACtCA,OAAO,CAAC,QAAQ,EAAE,mCAAmC,CAAC,CAAC;AAAA,CACvDM,QAAQ,CAAC,CAAC;AACf,MAAMsE,UAAU,GAAG,sFAAsF;AACzG,MAAMC,SAAS,GAAG,SAAS;AAC3B,MAAMC,WAAW,GAAG,6BAA6B;AACjD,MAAMC,GAAG,GAAGpF,IAAI,CAAC,6GAA6G,CAAC,CAC1HK,OAAO,CAAC,OAAO,EAAE8E,WAAW,CAAC,CAC7B9E,OAAO,CAAC,OAAO,EAAE,8DAA8D,CAAC,CAChFM,QAAQ,CAAC,CAAC;AACf,MAAM0E,IAAI,GAAGrF,IAAI,CAAC,sCAAsC,CAAC,CACpDK,OAAO,CAAC,OAAO,EAAEwE,MAAM,CAAC,CACxBlE,QAAQ,CAAC,CAAC;AACf,MAAM2E,IAAI,GAAG,6DAA6D,GACpE,0EAA0E,GAC1E,sEAAsE,GACtE,yEAAyE,GACzE,qEAAqE,GACrE,cAAc;AACpB,MAAMC,QAAQ,GAAG,+BAA+B;AAChD,MAAMC,IAAI,GAAGxF,IAAI,CAAC,YAAY,CAAC;AAAA,EACzB,qEAAqE,CAAC;AAAA,EACtE,yBAAyB,CAAC;AAAA,EAC1B,+BAA+B,CAAC;AAAA,EAChC,+BAA+B,CAAC;AAAA,EAChC,2CAA2C,CAAC;AAAA,EAC5C,0DAA0D,CAAC;AAAA,EAC3D,wHAAwH,CAAC;AAAA,EACzH,wGAAwG,CAAC;AAAA,EACzG,GAAG,EAAE,GAAG,CAAC,CACVK,OAAO,CAAC,SAAS,EAAEkF,QAAQ,CAAC,CAC5BlF,OAAO,CAAC,KAAK,EAAEiF,IAAI,CAAC,CACpBjF,OAAO,CAAC,WAAW,EAAE,0EAA0E,CAAC,CAChGM,QAAQ,CAAC,CAAC;AACf,MAAM8E,SAAS,GAAGzF,IAAI,CAACiF,UAAU,CAAC,CAC7B5E,OAAO,CAAC,IAAI,EAAEsE,EAAE,CAAC,CACjBtE,OAAO,CAAC,SAAS,EAAE,uBAAuB,CAAC,CAC3CA,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;AAAA,CACzBA,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CACrBA,OAAO,CAAC,YAAY,EAAE,SAAS,CAAC,CAChCA,OAAO,CAAC,QAAQ,EAAE,gDAAgD,CAAC,CACnEA,OAAO,CAAC,MAAM,EAAE,wBAAwB,CAAC,CAAC;AAAA,CAC1CA,OAAO,CAAC,MAAM,EAAE,6DAA6D,CAAC,CAC9EA,OAAO,CAAC,KAAK,EAAEiF,IAAI,CAAC,CAAC;AAAA,CACrB3E,QAAQ,CAAC,CAAC;AACf,MAAM+E,UAAU,GAAG1F,IAAI,CAAC,yCAAyC,CAAC,CAC7DK,OAAO,CAAC,WAAW,EAAEoF,SAAS,CAAC,CAC/B9E,QAAQ,CAAC,CAAC;AACf;AACA;AACA;AACA,MAAMgF,WAAW,GAAG;EAChBD,UAAU;EACVE,IAAI,EAAEnB,SAAS;EACfW,GAAG;EACHV,MAAM;EACNE,OAAO;EACPD,EAAE;EACFa,IAAI;EACJT,QAAQ;EACRM,IAAI;EACJb,OAAO;EACPiB,SAAS;EACTI,KAAK,EAAE/F,QAAQ;EACfgG,IAAI,EAAEZ;AACV,CAAC;AACD;AACA;AACA;AACA,MAAMa,QAAQ,GAAG/F,IAAI,CAAC,mBAAmB,CAAC;AAAA,EACpC,wDAAwD,CAAC;AAAA,EACzD,sFAAsF,CAAC,CAAC;AAAA,CACzFK,OAAO,CAAC,IAAI,EAAEsE,EAAE,CAAC,CACjBtE,OAAO,CAAC,SAAS,EAAE,uBAAuB,CAAC,CAC3CA,OAAO,CAAC,YAAY,EAAE,SAAS,CAAC,CAChCA,OAAO,CAAC,MAAM,EAAE,yBAAyB,CAAC,CAC1CA,OAAO,CAAC,QAAQ,EAAE,gDAAgD,CAAC,CACnEA,OAAO,CAAC,MAAM,EAAE,wBAAwB,CAAC,CAAC;AAAA,CAC1CA,OAAO,CAAC,MAAM,EAAE,6DAA6D,CAAC,CAC9EA,OAAO,CAAC,KAAK,EAAEiF,IAAI,CAAC,CAAC;AAAA,CACrB3E,QAAQ,CAAC,CAAC;AACf,MAAMqF,QAAQ,GAAG;EACb,GAAGL,WAAW;EACdZ,QAAQ,EAAEC,WAAW;EACrBa,KAAK,EAAEE,QAAQ;EACfN,SAAS,EAAEzF,IAAI,CAACiF,UAAU,CAAC,CACtB5E,OAAO,CAAC,IAAI,EAAEsE,EAAE,CAAC,CACjBtE,OAAO,CAAC,SAAS,EAAE,uBAAuB,CAAC,CAC3CA,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;EAAA,CACzBA,OAAO,CAAC,OAAO,EAAE0F,QAAQ,CAAC,CAAC;EAAA,CAC3B1F,OAAO,CAAC,YAAY,EAAE,SAAS,CAAC,CAChCA,OAAO,CAAC,QAAQ,EAAE,gDAAgD,CAAC,CACnEA,OAAO,CAAC,MAAM,EAAE,wBAAwB,CAAC,CAAC;EAAA,CAC1CA,OAAO,CAAC,MAAM,EAAE,6DAA6D,CAAC,CAC9EA,OAAO,CAAC,KAAK,EAAEiF,IAAI,CAAC,CAAC;EAAA,CACrB3E,QAAQ,CAAC;AAClB,CAAC;AACD;AACA;AACA;AACA,MAAMsF,aAAa,GAAG;EAClB,GAAGN,WAAW;EACdH,IAAI,EAAExF,IAAI,CAAC,8BAA8B,GACnC,4CAA4C,CAAC;EAAA,EAC7C,sEAAsE,CAAC,CACxEK,OAAO,CAAC,SAAS,EAAEkF,QAAQ,CAAC,CAC5BlF,OAAO,CAAC,MAAM,EAAE,QAAQ,GACvB,qEAAqE,GACrE,6DAA6D,GAC7D,+BAA+B,CAAC,CACjCM,QAAQ,CAAC,CAAC;EACfyE,GAAG,EAAE,mEAAmE;EACxER,OAAO,EAAE,wBAAwB;EACjCF,MAAM,EAAE5E,QAAQ;EAAE;EAClBiF,QAAQ,EAAE,kCAAkC;EAC5CU,SAAS,EAAEzF,IAAI,CAACiF,UAAU,CAAC,CACtB5E,OAAO,CAAC,IAAI,EAAEsE,EAAE,CAAC,CACjBtE,OAAO,CAAC,SAAS,EAAE,iBAAiB,CAAC,CACrCA,OAAO,CAAC,UAAU,EAAE0E,QAAQ,CAAC,CAC7B1E,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CACrBA,OAAO,CAAC,YAAY,EAAE,SAAS,CAAC,CAChCA,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CACtBA,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CACpBA,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CACpBA,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CACnBM,QAAQ,CAAC;AAClB,CAAC;AACD;AACA;AACA;AACA,MAAMuF,QAAQ,GAAG,6CAA6C;AAC9D,MAAMC,UAAU,GAAG,qCAAqC;AACxD,MAAMC,EAAE,GAAG,uBAAuB;AAClC,MAAMC,UAAU,GAAG,6EAA6E;AAChG;AACA,MAAMC,YAAY,GAAG,eAAe;AACpC,MAAMC,mBAAmB,GAAG,iBAAiB;AAC7C,MAAMC,sBAAsB,GAAG,kBAAkB;AACjD,MAAMC,WAAW,GAAGzG,IAAI,CAAC,uBAAuB,EAAE,GAAG,CAAC,CACjDK,OAAO,CAAC,aAAa,EAAEkG,mBAAmB,CAAC,CAAC5F,QAAQ,CAAC,CAAC;AAC3D;AACA,MAAM+F,uBAAuB,GAAG,oBAAoB;AACpD,MAAMC,8BAA8B,GAAG,sBAAsB;AAC7D,MAAMC,iCAAiC,GAAG,wBAAwB;AAClE;AACA,MAAMC,SAAS,GAAG,+EAA+E;AACjG,MAAMC,kBAAkB,GAAG,+DAA+D;AAC1F,MAAMC,cAAc,GAAG/G,IAAI,CAAC8G,kBAAkB,EAAE,GAAG,CAAC,CAC/CzG,OAAO,CAAC,QAAQ,EAAEiG,YAAY,CAAC,CAC/B3F,QAAQ,CAAC,CAAC;AACf,MAAMqG,iBAAiB,GAAGhH,IAAI,CAAC8G,kBAAkB,EAAE,GAAG,CAAC,CAClDzG,OAAO,CAAC,QAAQ,EAAEqG,uBAAuB,CAAC,CAC1C/F,QAAQ,CAAC,CAAC;AACf,MAAMsG,qBAAqB,GAAG,mCAAmC,CAAC;AAAA,EAC5D,gBAAgB,CAAC;AAAA,EACjB,gCAAgC,CAAC;AAAA,EACjC,6CAA6C,CAAC;AAAA,EAC9C,2CAA2C,CAAC;AAAA,EAC5C,8BAA8B,CAAC;AAAA,EAC/B,qCAAqC,CAAC;AAAA,EACtC,uCAAuC,CAAC,CAAC;AAC/C,MAAMC,iBAAiB,GAAGlH,IAAI,CAACiH,qBAAqB,EAAE,IAAI,CAAC,CACtD5G,OAAO,CAAC,gBAAgB,EAAEmG,sBAAsB,CAAC,CACjDnG,OAAO,CAAC,aAAa,EAAEkG,mBAAmB,CAAC,CAC3ClG,OAAO,CAAC,QAAQ,EAAEiG,YAAY,CAAC,CAC/B3F,QAAQ,CAAC,CAAC;AACf,MAAMwG,oBAAoB,GAAGnH,IAAI,CAACiH,qBAAqB,EAAE,IAAI,CAAC,CACzD5G,OAAO,CAAC,gBAAgB,EAAEuG,iCAAiC,CAAC,CAC5DvG,OAAO,CAAC,aAAa,EAAEsG,8BAA8B,CAAC,CACtDtG,OAAO,CAAC,QAAQ,EAAEqG,uBAAuB,CAAC,CAC1C/F,QAAQ,CAAC,CAAC;AACf;AACA,MAAMyG,iBAAiB,GAAGpH,IAAI,CAAC,yCAAyC,CAAC;AAAA,EACnE,gBAAgB,CAAC;AAAA,EACjB,4BAA4B,CAAC;AAAA,EAC7B,yCAAyC,CAAC;AAAA,EAC1C,uCAAuC,CAAC;AAAA,EACxC,0BAA0B,CAAC;AAAA,EAC3B,+BAA+B,EAAE,IAAI,CAAC,CAAC;AAAA,CACxCK,OAAO,CAAC,gBAAgB,EAAEmG,sBAAsB,CAAC,CACjDnG,OAAO,CAAC,aAAa,EAAEkG,mBAAmB,CAAC,CAC3ClG,OAAO,CAAC,QAAQ,EAAEiG,YAAY,CAAC,CAC/B3F,QAAQ,CAAC,CAAC;AACf,MAAM0G,cAAc,GAAGrH,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CACzCK,OAAO,CAAC,QAAQ,EAAEiG,YAAY,CAAC,CAC/B3F,QAAQ,CAAC,CAAC;AACf,MAAM2G,QAAQ,GAAGtH,IAAI,CAAC,qCAAqC,CAAC,CACvDK,OAAO,CAAC,QAAQ,EAAE,8BAA8B,CAAC,CACjDA,OAAO,CAAC,OAAO,EAAE,8IAA8I,CAAC,CAChKM,QAAQ,CAAC,CAAC;AACf,MAAM4G,cAAc,GAAGvH,IAAI,CAACuF,QAAQ,CAAC,CAAClF,OAAO,CAAC,WAAW,EAAE,KAAK,CAAC,CAACM,QAAQ,CAAC,CAAC;AAC5E,MAAM6G,GAAG,GAAGxH,IAAI,CAAC,UAAU,GACrB,2BAA2B,CAAC;AAAA,EAC5B,0CAA0C,CAAC;AAAA,EAC3C,sBAAsB,CAAC;AAAA,EACvB,6BAA6B,CAAC;AAAA,EAC9B,kCAAkC,CAAC,CAAC;AAAA,CACrCK,OAAO,CAAC,SAAS,EAAEkH,cAAc,CAAC,CAClClH,OAAO,CAAC,WAAW,EAAE,6EAA6E,CAAC,CACnGM,QAAQ,CAAC,CAAC;AACf,MAAM8G,YAAY,GAAG,qDAAqD;AAC1E,MAAMC,IAAI,GAAG1H,IAAI,CAAC,+CAA+C,CAAC,CAC7DK,OAAO,CAAC,OAAO,EAAEoH,YAAY,CAAC,CAC9BpH,OAAO,CAAC,MAAM,EAAE,sCAAsC,CAAC,CACvDA,OAAO,CAAC,OAAO,EAAE,6DAA6D,CAAC,CAC/EM,QAAQ,CAAC,CAAC;AACf,MAAMgH,OAAO,GAAG3H,IAAI,CAAC,yBAAyB,CAAC,CAC1CK,OAAO,CAAC,OAAO,EAAEoH,YAAY,CAAC,CAC9BpH,OAAO,CAAC,KAAK,EAAE8E,WAAW,CAAC,CAC3BxE,QAAQ,CAAC,CAAC;AACf,MAAMiH,MAAM,GAAG5H,IAAI,CAAC,uBAAuB,CAAC,CACvCK,OAAO,CAAC,KAAK,EAAE8E,WAAW,CAAC,CAC3BxE,QAAQ,CAAC,CAAC;AACf,MAAMkH,aAAa,GAAG7H,IAAI,CAAC,uBAAuB,EAAE,GAAG,CAAC,CACnDK,OAAO,CAAC,SAAS,EAAEsH,OAAO,CAAC,CAC3BtH,OAAO,CAAC,QAAQ,EAAEuH,MAAM,CAAC,CACzBjH,QAAQ,CAAC,CAAC;AACf;AACA;AACA;AACA,MAAMmH,YAAY,GAAG;EACjBC,UAAU,EAAEjI,QAAQ;EAAE;EACtBuH,cAAc;EACdC,QAAQ;EACRT,SAAS;EACTT,EAAE;EACFR,IAAI,EAAEO,UAAU;EAChB6B,GAAG,EAAElI,QAAQ;EACbiH,cAAc;EACdG,iBAAiB;EACjBE,iBAAiB;EACjBa,MAAM,EAAE/B,QAAQ;EAChBwB,IAAI;EACJE,MAAM;EACNnB,WAAW;EACXkB,OAAO;EACPE,aAAa;EACbL,GAAG;EACH1B,IAAI,EAAEO,UAAU;EAChB6B,GAAG,EAAEpI;AACT,CAAC;AACD;AACA;AACA;AACA,MAAMqI,cAAc,GAAG;EACnB,GAAGL,YAAY;EACfJ,IAAI,EAAE1H,IAAI,CAAC,yBAAyB,CAAC,CAChCK,OAAO,CAAC,OAAO,EAAEoH,YAAY,CAAC,CAC9B9G,QAAQ,CAAC,CAAC;EACfgH,OAAO,EAAE3H,IAAI,CAAC,+BAA+B,CAAC,CACzCK,OAAO,CAAC,OAAO,EAAEoH,YAAY,CAAC,CAC9B9G,QAAQ,CAAC;AAClB,CAAC;AACD;AACA;AACA;AACA,MAAMyH,SAAS,GAAG;EACd,GAAGN,YAAY;EACfZ,iBAAiB,EAAEC,oBAAoB;EACvCJ,cAAc,EAAEC,iBAAiB;EACjCkB,GAAG,EAAElI,IAAI,CAAC,kEAAkE,EAAE,GAAG,CAAC,CAC7EK,OAAO,CAAC,OAAO,EAAE,2EAA2E,CAAC,CAC7FM,QAAQ,CAAC,CAAC;EACfoH,UAAU,EAAE,4EAA4E;EACxFC,GAAG,EAAE,+DAA+D;EACpElC,IAAI,EAAE;AACV,CAAC;AACD;AACA;AACA;AACA,MAAMuC,YAAY,GAAG;EACjB,GAAGD,SAAS;EACZhC,EAAE,EAAEpG,IAAI,CAACoG,EAAE,CAAC,CAAC/F,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACM,QAAQ,CAAC,CAAC;EAC5CmF,IAAI,EAAE9F,IAAI,CAACoI,SAAS,CAACtC,IAAI,CAAC,CACrBzF,OAAO,CAAC,MAAM,EAAE,eAAe,CAAC,CAChCA,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CACvBM,QAAQ,CAAC;AAClB,CAAC;AACD;AACA;AACA;AACA,MAAM2H,KAAK,GAAG;EACVC,MAAM,EAAE5C,WAAW;EACnBvG,GAAG,EAAE4G,QAAQ;EACb1G,QAAQ,EAAE2G;AACd,CAAC;AACD,MAAMuC,MAAM,GAAG;EACXD,MAAM,EAAET,YAAY;EACpB1I,GAAG,EAAEgJ,SAAS;EACdlJ,MAAM,EAAEmJ,YAAY;EACpB/I,QAAQ,EAAE6I;AACd,CAAC;;AAED;AACA;AACA;AACA,MAAMM,kBAAkB,GAAG;EACvB,GAAG,EAAE,OAAO;EACZ,GAAG,EAAE,MAAM;EACX,GAAG,EAAE,MAAM;EACX,GAAG,EAAE,QAAQ;EACb,GAAG,EAAE;AACT,CAAC;AACD,MAAMC,oBAAoB,GAAIC,EAAE,IAAKF,kBAAkB,CAACE,EAAE,CAAC;AAC3D,SAASV,MAAMA,CAACzC,IAAI,EAAEoD,MAAM,EAAE;EAC1B,IAAIA,MAAM,EAAE;IACR,IAAInI,KAAK,CAACwC,UAAU,CAAC4F,IAAI,CAACrD,IAAI,CAAC,EAAE;MAC7B,OAAOA,IAAI,CAACnF,OAAO,CAACI,KAAK,CAACyC,aAAa,EAAEwF,oBAAoB,CAAC;IAClE;EACJ,CAAC,MACI;IACD,IAAIjI,KAAK,CAAC0C,kBAAkB,CAAC0F,IAAI,CAACrD,IAAI,CAAC,EAAE;MACrC,OAAOA,IAAI,CAACnF,OAAO,CAACI,KAAK,CAAC2C,qBAAqB,EAAEsF,oBAAoB,CAAC;IAC1E;EACJ;EACA,OAAOlD,IAAI;AACf;AACA,SAASsD,QAAQA,CAACC,IAAI,EAAE;EACpB,IAAI;IACAA,IAAI,GAAGC,SAAS,CAACD,IAAI,CAAC,CAAC1I,OAAO,CAACI,KAAK,CAAC6C,aAAa,EAAE,GAAG,CAAC;EAC5D,CAAC,CACD,MAAM;IACF,OAAO,IAAI;EACf;EACA,OAAOyF,IAAI;AACf;AACA,SAASE,UAAUA,CAACC,QAAQ,EAAEC,KAAK,EAAE;EACjC;EACA;EACA,MAAMC,GAAG,GAAGF,QAAQ,CAAC7I,OAAO,CAACI,KAAK,CAAC8C,QAAQ,EAAE,CAAC8F,KAAK,EAAEC,MAAM,EAAEC,GAAG,KAAK;MACjE,IAAIC,OAAO,GAAG,KAAK;MACnB,IAAIC,IAAI,GAAGH,MAAM;MACjB,OAAO,EAAEG,IAAI,IAAI,CAAC,IAAIF,GAAG,CAACE,IAAI,CAAC,KAAK,IAAI,EACpCD,OAAO,GAAG,CAACA,OAAO;MACtB,IAAIA,OAAO,EAAE;QACT;QACA;QACA,OAAO,GAAG;MACd,CAAC,MACI;QACD;QACA,OAAO,IAAI;MACf;IACJ,CAAC,CAAC;IAAEE,KAAK,GAAGN,GAAG,CAACO,KAAK,CAAClJ,KAAK,CAAC+C,SAAS,CAAC;EACtC,IAAIoG,CAAC,GAAG,CAAC;EACT;EACA,IAAI,CAACF,KAAK,CAAC,CAAC,CAAC,CAACG,IAAI,CAAC,CAAC,EAAE;IAClBH,KAAK,CAACI,KAAK,CAAC,CAAC;EACjB;EACA,IAAIJ,KAAK,CAACK,MAAM,GAAG,CAAC,IAAI,CAACL,KAAK,CAACM,EAAE,CAAC,CAAC,CAAC,CAAC,EAAEH,IAAI,CAAC,CAAC,EAAE;IAC3CH,KAAK,CAACO,GAAG,CAAC,CAAC;EACf;EACA,IAAId,KAAK,EAAE;IACP,IAAIO,KAAK,CAACK,MAAM,GAAGZ,KAAK,EAAE;MACtBO,KAAK,CAACQ,MAAM,CAACf,KAAK,CAAC;IACvB,CAAC,MACI;MACD,OAAOO,KAAK,CAACK,MAAM,GAAGZ,KAAK,EACvBO,KAAK,CAACS,IAAI,CAAC,EAAE,CAAC;IACtB;EACJ;EACA,OAAOP,CAAC,GAAGF,KAAK,CAACK,MAAM,EAAEH,CAAC,EAAE,EAAE;IAC1B;IACAF,KAAK,CAACE,CAAC,CAAC,GAAGF,KAAK,CAACE,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAACxJ,OAAO,CAACI,KAAK,CAACgD,SAAS,EAAE,GAAG,CAAC;EAC5D;EACA,OAAOiG,KAAK;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASU,KAAKA,CAACb,GAAG,EAAEc,CAAC,EAAEC,MAAM,EAAE;EAC3B,MAAMC,CAAC,GAAGhB,GAAG,CAACQ,MAAM;EACpB,IAAIQ,CAAC,KAAK,CAAC,EAAE;IACT,OAAO,EAAE;EACb;EACA;EACA,IAAIC,OAAO,GAAG,CAAC;EACf;EACA,OAAOA,OAAO,GAAGD,CAAC,EAAE;IAChB,MAAME,QAAQ,GAAGlB,GAAG,CAACmB,MAAM,CAACH,CAAC,GAAGC,OAAO,GAAG,CAAC,CAAC;IAC5C,IAAIC,QAAQ,KAAKJ,CAAC,IAAI,IAAI,EAAE;MACxBG,OAAO,EAAE;IACb,CAAC,MACI;MACD;IACJ;EACJ;EACA,OAAOjB,GAAG,CAACoB,KAAK,CAAC,CAAC,EAAEJ,CAAC,GAAGC,OAAO,CAAC;AACpC;AACA,SAASI,kBAAkBA,CAACrB,GAAG,EAAEsB,CAAC,EAAE;EAChC,IAAItB,GAAG,CAACuB,OAAO,CAACD,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;IAC1B,OAAO,CAAC,CAAC;EACb;EACA,IAAIE,KAAK,GAAG,CAAC;EACb,KAAK,IAAInB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,GAAG,CAACQ,MAAM,EAAEH,CAAC,EAAE,EAAE;IACjC,IAAIL,GAAG,CAACK,CAAC,CAAC,KAAK,IAAI,EAAE;MACjBA,CAAC,EAAE;IACP,CAAC,MACI,IAAIL,GAAG,CAACK,CAAC,CAAC,KAAKiB,CAAC,CAAC,CAAC,CAAC,EAAE;MACtBE,KAAK,EAAE;IACX,CAAC,MACI,IAAIxB,GAAG,CAACK,CAAC,CAAC,KAAKiB,CAAC,CAAC,CAAC,CAAC,EAAE;MACtBE,KAAK,EAAE;MACP,IAAIA,KAAK,GAAG,CAAC,EAAE;QACX,OAAOnB,CAAC;MACZ;IACJ;EACJ;EACA,OAAO,CAAC,CAAC;AACb;AAEA,SAASoB,UAAUA,CAACC,GAAG,EAAEvD,IAAI,EAAEwD,GAAG,EAAEC,KAAK,EAAEC,KAAK,EAAE;EAC9C,MAAMrC,IAAI,GAAGrB,IAAI,CAACqB,IAAI;EACtB,MAAMsC,KAAK,GAAG3D,IAAI,CAAC2D,KAAK,IAAI,IAAI;EAChC,MAAMvF,IAAI,GAAGmF,GAAG,CAAC,CAAC,CAAC,CAAC5K,OAAO,CAAC+K,KAAK,CAAC3K,KAAK,CAACK,iBAAiB,EAAE,IAAI,CAAC;EAChE,IAAImK,GAAG,CAAC,CAAC,CAAC,CAACP,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IAC1BS,KAAK,CAACG,KAAK,CAACC,MAAM,GAAG,IAAI;IACzB,MAAMC,KAAK,GAAG;MACVC,IAAI,EAAE,MAAM;MACZP,GAAG;MACHnC,IAAI;MACJsC,KAAK;MACLvF,IAAI;MACJ4F,MAAM,EAAEP,KAAK,CAACQ,YAAY,CAAC7F,IAAI;IACnC,CAAC;IACDqF,KAAK,CAACG,KAAK,CAACC,MAAM,GAAG,KAAK;IAC1B,OAAOC,KAAK;EAChB;EACA,OAAO;IACHC,IAAI,EAAE,OAAO;IACbP,GAAG;IACHnC,IAAI;IACJsC,KAAK;IACLvF;EACJ,CAAC;AACL;AACA,SAAS/E,sBAAsBA,CAACmK,GAAG,EAAEpF,IAAI,EAAEsF,KAAK,EAAE;EAC9C,MAAMQ,iBAAiB,GAAGV,GAAG,CAAC7B,KAAK,CAAC+B,KAAK,CAAC3K,KAAK,CAACM,sBAAsB,CAAC;EACvE,IAAI6K,iBAAiB,KAAK,IAAI,EAAE;IAC5B,OAAO9F,IAAI;EACf;EACA,MAAM+F,YAAY,GAAGD,iBAAiB,CAAC,CAAC,CAAC;EACzC,OAAO9F,IAAI,CACN6D,KAAK,CAAC,IAAI,CAAC,CACXmC,GAAG,CAACC,IAAI,IAAI;IACb,MAAMC,iBAAiB,GAAGD,IAAI,CAAC1C,KAAK,CAAC+B,KAAK,CAAC3K,KAAK,CAACO,cAAc,CAAC;IAChE,IAAIgL,iBAAiB,KAAK,IAAI,EAAE;MAC5B,OAAOD,IAAI;IACf;IACA,MAAM,CAACE,YAAY,CAAC,GAAGD,iBAAiB;IACxC,IAAIC,YAAY,CAAClC,MAAM,IAAI8B,YAAY,CAAC9B,MAAM,EAAE;MAC5C,OAAOgC,IAAI,CAACpB,KAAK,CAACkB,YAAY,CAAC9B,MAAM,CAAC;IAC1C;IACA,OAAOgC,IAAI;EACf,CAAC,CAAC,CACGG,IAAI,CAAC,IAAI,CAAC;AACnB;AACA;AACA;AACA;AACA,MAAMC,UAAU,CAAC;EACbC,OAAO;EACPhB,KAAK,CAAC,CAAC;EACPD,KAAK,CAAC,CAAC;EACPkB,WAAWA,CAACD,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO,IAAIzM,SAAS;EACvC;EACA2M,KAAKA,CAACC,GAAG,EAAE;IACP,MAAMtB,GAAG,GAAG,IAAI,CAACG,KAAK,CAAC9C,KAAK,CAAC9D,OAAO,CAACzE,IAAI,CAACwM,GAAG,CAAC;IAC9C,IAAItB,GAAG,IAAIA,GAAG,CAAC,CAAC,CAAC,CAAClB,MAAM,GAAG,CAAC,EAAE;MAC1B,OAAO;QACH0B,IAAI,EAAE,OAAO;QACbP,GAAG,EAAED,GAAG,CAAC,CAAC;MACd,CAAC;IACL;EACJ;EACArF,IAAIA,CAAC2G,GAAG,EAAE;IACN,MAAMtB,GAAG,GAAG,IAAI,CAACG,KAAK,CAAC9C,KAAK,CAAC1C,IAAI,CAAC7F,IAAI,CAACwM,GAAG,CAAC;IAC3C,IAAItB,GAAG,EAAE;MACL,MAAMnF,IAAI,GAAGmF,GAAG,CAAC,CAAC,CAAC,CAAC5K,OAAO,CAAC,IAAI,CAAC+K,KAAK,CAAC3K,KAAK,CAACI,gBAAgB,EAAE,EAAE,CAAC;MAClE,OAAO;QACH4K,IAAI,EAAE,MAAM;QACZP,GAAG,EAAED,GAAG,CAAC,CAAC,CAAC;QACXuB,cAAc,EAAE,UAAU;QAC1B1G,IAAI,EAAE,CAAC,IAAI,CAACsG,OAAO,CAAC9M,QAAQ,GACtB8K,KAAK,CAACtE,IAAI,EAAE,IAAI,CAAC,GACjBA;MACV,CAAC;IACL;EACJ;EACApB,MAAMA,CAAC6H,GAAG,EAAE;IACR,MAAMtB,GAAG,GAAG,IAAI,CAACG,KAAK,CAAC9C,KAAK,CAAC5D,MAAM,CAAC3E,IAAI,CAACwM,GAAG,CAAC;IAC7C,IAAItB,GAAG,EAAE;MACL,MAAMC,GAAG,GAAGD,GAAG,CAAC,CAAC,CAAC;MAClB,MAAMnF,IAAI,GAAG/E,sBAAsB,CAACmK,GAAG,EAAED,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE,IAAI,CAACG,KAAK,CAAC;MAClE,OAAO;QACHK,IAAI,EAAE,MAAM;QACZP,GAAG;QACHuB,IAAI,EAAExB,GAAG,CAAC,CAAC,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC,CAACpB,IAAI,CAAC,CAAC,CAACxJ,OAAO,CAAC,IAAI,CAAC+K,KAAK,CAAC5C,MAAM,CAACnB,cAAc,EAAE,IAAI,CAAC,GAAG4D,GAAG,CAAC,CAAC,CAAC;QACrFnF;MACJ,CAAC;IACL;EACJ;EACAlB,OAAOA,CAAC2H,GAAG,EAAE;IACT,MAAMtB,GAAG,GAAG,IAAI,CAACG,KAAK,CAAC9C,KAAK,CAAC1D,OAAO,CAAC7E,IAAI,CAACwM,GAAG,CAAC;IAC9C,IAAItB,GAAG,EAAE;MACL,IAAInF,IAAI,GAAGmF,GAAG,CAAC,CAAC,CAAC,CAACpB,IAAI,CAAC,CAAC;MACxB;MACA,IAAI,IAAI,CAACuB,KAAK,CAAC3K,KAAK,CAACQ,UAAU,CAAC4H,IAAI,CAAC/C,IAAI,CAAC,EAAE;QACxC,MAAM4G,OAAO,GAAGtC,KAAK,CAACtE,IAAI,EAAE,GAAG,CAAC;QAChC,IAAI,IAAI,CAACsG,OAAO,CAAC9M,QAAQ,EAAE;UACvBwG,IAAI,GAAG4G,OAAO,CAAC7C,IAAI,CAAC,CAAC;QACzB,CAAC,MACI,IAAI,CAAC6C,OAAO,IAAI,IAAI,CAACtB,KAAK,CAAC3K,KAAK,CAACU,eAAe,CAAC0H,IAAI,CAAC6D,OAAO,CAAC,EAAE;UACjE;UACA5G,IAAI,GAAG4G,OAAO,CAAC7C,IAAI,CAAC,CAAC;QACzB;MACJ;MACA,OAAO;QACH4B,IAAI,EAAE,SAAS;QACfP,GAAG,EAAED,GAAG,CAAC,CAAC,CAAC;QACX0B,KAAK,EAAE1B,GAAG,CAAC,CAAC,CAAC,CAAClB,MAAM;QACpBjE,IAAI;QACJ4F,MAAM,EAAE,IAAI,CAACP,KAAK,CAAC3C,MAAM,CAAC1C,IAAI;MAClC,CAAC;IACL;EACJ;EACAnB,EAAEA,CAAC4H,GAAG,EAAE;IACJ,MAAMtB,GAAG,GAAG,IAAI,CAACG,KAAK,CAAC9C,KAAK,CAAC3D,EAAE,CAAC5E,IAAI,CAACwM,GAAG,CAAC;IACzC,IAAItB,GAAG,EAAE;MACL,OAAO;QACHQ,IAAI,EAAE,IAAI;QACVP,GAAG,EAAEd,KAAK,CAACa,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI;MAC3B,CAAC;IACL;EACJ;EACAvF,UAAUA,CAAC6G,GAAG,EAAE;IACZ,MAAMtB,GAAG,GAAG,IAAI,CAACG,KAAK,CAAC9C,KAAK,CAAC5C,UAAU,CAAC3F,IAAI,CAACwM,GAAG,CAAC;IACjD,IAAItB,GAAG,EAAE;MACL,IAAI2B,KAAK,GAAGxC,KAAK,CAACa,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAACtB,KAAK,CAAC,IAAI,CAAC;MAC3C,IAAIuB,GAAG,GAAG,EAAE;MACZ,IAAIpF,IAAI,GAAG,EAAE;MACb,MAAM4F,MAAM,GAAG,EAAE;MACjB,OAAOkB,KAAK,CAAC7C,MAAM,GAAG,CAAC,EAAE;QACrB,IAAI8C,YAAY,GAAG,KAAK;QACxB,MAAMC,YAAY,GAAG,EAAE;QACvB,IAAIlD,CAAC;QACL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgD,KAAK,CAAC7C,MAAM,EAAEH,CAAC,EAAE,EAAE;UAC/B;UACA,IAAI,IAAI,CAACwB,KAAK,CAAC3K,KAAK,CAACiB,eAAe,CAACmH,IAAI,CAAC+D,KAAK,CAAChD,CAAC,CAAC,CAAC,EAAE;YACjDkD,YAAY,CAAC3C,IAAI,CAACyC,KAAK,CAAChD,CAAC,CAAC,CAAC;YAC3BiD,YAAY,GAAG,IAAI;UACvB,CAAC,MACI,IAAI,CAACA,YAAY,EAAE;YACpBC,YAAY,CAAC3C,IAAI,CAACyC,KAAK,CAAChD,CAAC,CAAC,CAAC;UAC/B,CAAC,MACI;YACD;UACJ;QACJ;QACAgD,KAAK,GAAGA,KAAK,CAACjC,KAAK,CAACf,CAAC,CAAC;QACtB,MAAMmD,UAAU,GAAGD,YAAY,CAACZ,IAAI,CAAC,IAAI,CAAC;QAC1C,MAAMc,WAAW,GAAGD;QAChB;QAAA,CACC1M,OAAO,CAAC,IAAI,CAAC+K,KAAK,CAAC3K,KAAK,CAACkB,uBAAuB,EAAE,UAAU,CAAC,CAC7DtB,OAAO,CAAC,IAAI,CAAC+K,KAAK,CAAC3K,KAAK,CAACmB,wBAAwB,EAAE,EAAE,CAAC;QAC3DsJ,GAAG,GAAGA,GAAG,GAAI,GAAEA,GAAI,KAAI6B,UAAW,EAAC,GAAGA,UAAU;QAChDjH,IAAI,GAAGA,IAAI,GAAI,GAAEA,IAAK,KAAIkH,WAAY,EAAC,GAAGA,WAAW;QACrD;QACA;QACA,MAAMC,GAAG,GAAG,IAAI,CAAC9B,KAAK,CAACG,KAAK,CAAC2B,GAAG;QAChC,IAAI,CAAC9B,KAAK,CAACG,KAAK,CAAC2B,GAAG,GAAG,IAAI;QAC3B,IAAI,CAAC9B,KAAK,CAAC+B,WAAW,CAACF,WAAW,EAAEtB,MAAM,EAAE,IAAI,CAAC;QACjD,IAAI,CAACP,KAAK,CAACG,KAAK,CAAC2B,GAAG,GAAGA,GAAG;QAC1B;QACA,IAAIL,KAAK,CAAC7C,MAAM,KAAK,CAAC,EAAE;UACpB;QACJ;QACA,MAAMoD,SAAS,GAAGzB,MAAM,CAAC1B,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAImD,SAAS,EAAE1B,IAAI,KAAK,MAAM,EAAE;UAC5B;UACA;QACJ,CAAC,MACI,IAAI0B,SAAS,EAAE1B,IAAI,KAAK,YAAY,EAAE;UACvC;UACA,MAAM2B,QAAQ,GAAGD,SAAS;UAC1B,MAAME,OAAO,GAAGD,QAAQ,CAAClC,GAAG,GAAG,IAAI,GAAG0B,KAAK,CAACV,IAAI,CAAC,IAAI,CAAC;UACtD,MAAMoB,QAAQ,GAAG,IAAI,CAAC5H,UAAU,CAAC2H,OAAO,CAAC;UACzC3B,MAAM,CAACA,MAAM,CAAC3B,MAAM,GAAG,CAAC,CAAC,GAAGuD,QAAQ;UACpCpC,GAAG,GAAGA,GAAG,CAACqC,SAAS,CAAC,CAAC,EAAErC,GAAG,CAACnB,MAAM,GAAGqD,QAAQ,CAAClC,GAAG,CAACnB,MAAM,CAAC,GAAGuD,QAAQ,CAACpC,GAAG;UACvEpF,IAAI,GAAGA,IAAI,CAACyH,SAAS,CAAC,CAAC,EAAEzH,IAAI,CAACiE,MAAM,GAAGqD,QAAQ,CAACtH,IAAI,CAACiE,MAAM,CAAC,GAAGuD,QAAQ,CAACxH,IAAI;UAC5E;QACJ,CAAC,MACI,IAAIqH,SAAS,EAAE1B,IAAI,KAAK,MAAM,EAAE;UACjC;UACA,MAAM2B,QAAQ,GAAGD,SAAS;UAC1B,MAAME,OAAO,GAAGD,QAAQ,CAAClC,GAAG,GAAG,IAAI,GAAG0B,KAAK,CAACV,IAAI,CAAC,IAAI,CAAC;UACtD,MAAMoB,QAAQ,GAAG,IAAI,CAACjI,IAAI,CAACgI,OAAO,CAAC;UACnC3B,MAAM,CAACA,MAAM,CAAC3B,MAAM,GAAG,CAAC,CAAC,GAAGuD,QAAQ;UACpCpC,GAAG,GAAGA,GAAG,CAACqC,SAAS,CAAC,CAAC,EAAErC,GAAG,CAACnB,MAAM,GAAGoD,SAAS,CAACjC,GAAG,CAACnB,MAAM,CAAC,GAAGuD,QAAQ,CAACpC,GAAG;UACxEpF,IAAI,GAAGA,IAAI,CAACyH,SAAS,CAAC,CAAC,EAAEzH,IAAI,CAACiE,MAAM,GAAGqD,QAAQ,CAAClC,GAAG,CAACnB,MAAM,CAAC,GAAGuD,QAAQ,CAACpC,GAAG;UAC1E0B,KAAK,GAAGS,OAAO,CAACE,SAAS,CAAC7B,MAAM,CAAC1B,EAAE,CAAC,CAAC,CAAC,CAAC,CAACkB,GAAG,CAACnB,MAAM,CAAC,CAACJ,KAAK,CAAC,IAAI,CAAC;UAC/D;QACJ;MACJ;MACA,OAAO;QACH8B,IAAI,EAAE,YAAY;QAClBP,GAAG;QACHQ,MAAM;QACN5F;MACJ,CAAC;IACL;EACJ;EACAT,IAAIA,CAACkH,GAAG,EAAE;IACN,IAAItB,GAAG,GAAG,IAAI,CAACG,KAAK,CAAC9C,KAAK,CAACjD,IAAI,CAACtF,IAAI,CAACwM,GAAG,CAAC;IACzC,IAAItB,GAAG,EAAE;MACL,IAAIlH,IAAI,GAAGkH,GAAG,CAAC,CAAC,CAAC,CAACpB,IAAI,CAAC,CAAC;MACxB,MAAM2D,SAAS,GAAGzJ,IAAI,CAACgG,MAAM,GAAG,CAAC;MACjC,MAAM1E,IAAI,GAAG;QACToG,IAAI,EAAE,MAAM;QACZP,GAAG,EAAE,EAAE;QACPuC,OAAO,EAAED,SAAS;QAClBE,KAAK,EAAEF,SAAS,GAAG,CAACzJ,IAAI,CAAC4G,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE;QAC1CgD,KAAK,EAAE,KAAK;QACZC,KAAK,EAAE;MACX,CAAC;MACD7J,IAAI,GAAGyJ,SAAS,GAAI,aAAYzJ,IAAI,CAAC4G,KAAK,CAAC,CAAC,CAAC,CAAE,EAAC,GAAI,KAAI5G,IAAK,EAAC;MAC9D,IAAI,IAAI,CAACqI,OAAO,CAAC9M,QAAQ,EAAE;QACvByE,IAAI,GAAGyJ,SAAS,GAAGzJ,IAAI,GAAG,OAAO;MACrC;MACA;MACA,MAAM8J,SAAS,GAAG,IAAI,CAACzC,KAAK,CAAC3K,KAAK,CAACqD,aAAa,CAACC,IAAI,CAAC;MACtD,IAAI+J,iBAAiB,GAAG,KAAK;MAC7B;MACA,OAAOvB,GAAG,EAAE;QACR,IAAIwB,QAAQ,GAAG,KAAK;QACpB,IAAI7C,GAAG,GAAG,EAAE;QACZ,IAAI8C,YAAY,GAAG,EAAE;QACrB,IAAI,EAAE/C,GAAG,GAAG4C,SAAS,CAAC9N,IAAI,CAACwM,GAAG,CAAC,CAAC,EAAE;UAC9B;QACJ;QACA,IAAI,IAAI,CAACnB,KAAK,CAAC9C,KAAK,CAAC3D,EAAE,CAACkE,IAAI,CAAC0D,GAAG,CAAC,EAAE;UAAE;UACjC;QACJ;QACArB,GAAG,GAAGD,GAAG,CAAC,CAAC,CAAC;QACZsB,GAAG,GAAGA,GAAG,CAACgB,SAAS,CAACrC,GAAG,CAACnB,MAAM,CAAC;QAC/B,IAAIkE,IAAI,GAAGhD,GAAG,CAAC,CAAC,CAAC,CAACtB,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAACtJ,OAAO,CAAC,IAAI,CAAC+K,KAAK,CAAC3K,KAAK,CAACoB,eAAe,EAAGqM,CAAC,IAAK,GAAG,CAACC,MAAM,CAAC,CAAC,GAAGD,CAAC,CAACnE,MAAM,CAAC,CAAC;QAC9G,IAAIqE,QAAQ,GAAG7B,GAAG,CAAC5C,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC,IAAInI,SAAS,GAAG,CAACyM,IAAI,CAACpE,IAAI,CAAC,CAAC;QAC5B,IAAI5F,MAAM,GAAG,CAAC;QACd,IAAI,IAAI,CAACmI,OAAO,CAAC9M,QAAQ,EAAE;UACvB2E,MAAM,GAAG,CAAC;UACV+J,YAAY,GAAGC,IAAI,CAACI,SAAS,CAAC,CAAC;QACnC,CAAC,MACI,IAAI7M,SAAS,EAAE;UAChByC,MAAM,GAAGgH,GAAG,CAAC,CAAC,CAAC,CAAClB,MAAM,GAAG,CAAC;QAC9B,CAAC,MACI;UACD9F,MAAM,GAAGgH,GAAG,CAAC,CAAC,CAAC,CAACqD,MAAM,CAAC,IAAI,CAAClD,KAAK,CAAC3K,KAAK,CAACW,YAAY,CAAC,CAAC,CAAC;UACvD6C,MAAM,GAAGA,MAAM,GAAG,CAAC,GAAG,CAAC,GAAGA,MAAM,CAAC,CAAC;UAClC+J,YAAY,GAAGC,IAAI,CAACtD,KAAK,CAAC1G,MAAM,CAAC;UACjCA,MAAM,IAAIgH,GAAG,CAAC,CAAC,CAAC,CAAClB,MAAM;QAC3B;QACA,IAAIvI,SAAS,IAAI,IAAI,CAAC4J,KAAK,CAAC3K,KAAK,CAACe,SAAS,CAACqH,IAAI,CAACuF,QAAQ,CAAC,EAAE;UAAE;UAC1DlD,GAAG,IAAIkD,QAAQ,GAAG,IAAI;UACtB7B,GAAG,GAAGA,GAAG,CAACgB,SAAS,CAACa,QAAQ,CAACrE,MAAM,GAAG,CAAC,CAAC;UACxCgE,QAAQ,GAAG,IAAI;QACnB;QACA,IAAI,CAACA,QAAQ,EAAE;UACX,MAAM/J,eAAe,GAAG,IAAI,CAACoH,KAAK,CAAC3K,KAAK,CAACuD,eAAe,CAACC,MAAM,CAAC;UAChE,MAAMG,OAAO,GAAG,IAAI,CAACgH,KAAK,CAAC3K,KAAK,CAAC2D,OAAO,CAACH,MAAM,CAAC;UAChD,MAAMI,gBAAgB,GAAG,IAAI,CAAC+G,KAAK,CAAC3K,KAAK,CAAC4D,gBAAgB,CAACJ,MAAM,CAAC;UAClE,MAAMK,iBAAiB,GAAG,IAAI,CAAC8G,KAAK,CAAC3K,KAAK,CAAC6D,iBAAiB,CAACL,MAAM,CAAC;UACpE,MAAMM,cAAc,GAAG,IAAI,CAAC6G,KAAK,CAAC3K,KAAK,CAAC8D,cAAc,CAACN,MAAM,CAAC;UAC9D;UACA,OAAOsI,GAAG,EAAE;YACR,MAAMgC,OAAO,GAAGhC,GAAG,CAAC5C,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACrC,IAAI6E,mBAAmB;YACvBJ,QAAQ,GAAGG,OAAO;YAClB;YACA,IAAI,IAAI,CAACnC,OAAO,CAAC9M,QAAQ,EAAE;cACvB8O,QAAQ,GAAGA,QAAQ,CAAC/N,OAAO,CAAC,IAAI,CAAC+K,KAAK,CAAC3K,KAAK,CAACqB,kBAAkB,EAAE,IAAI,CAAC;cACtE0M,mBAAmB,GAAGJ,QAAQ;YAClC,CAAC,MACI;cACDI,mBAAmB,GAAGJ,QAAQ,CAAC/N,OAAO,CAAC,IAAI,CAAC+K,KAAK,CAAC3K,KAAK,CAACa,aAAa,EAAE,MAAM,CAAC;YAClF;YACA;YACA,IAAI+C,gBAAgB,CAACwE,IAAI,CAACuF,QAAQ,CAAC,EAAE;cACjC;YACJ;YACA;YACA,IAAI9J,iBAAiB,CAACuE,IAAI,CAACuF,QAAQ,CAAC,EAAE;cAClC;YACJ;YACA;YACA,IAAI7J,cAAc,CAACsE,IAAI,CAACuF,QAAQ,CAAC,EAAE;cAC/B;YACJ;YACA;YACA,IAAIpK,eAAe,CAAC6E,IAAI,CAACuF,QAAQ,CAAC,EAAE;cAChC;YACJ;YACA;YACA,IAAIhK,OAAO,CAACyE,IAAI,CAACuF,QAAQ,CAAC,EAAE;cACxB;YACJ;YACA,IAAII,mBAAmB,CAACF,MAAM,CAAC,IAAI,CAAClD,KAAK,CAAC3K,KAAK,CAACW,YAAY,CAAC,IAAI6C,MAAM,IAAI,CAACmK,QAAQ,CAACvE,IAAI,CAAC,CAAC,EAAE;cAAE;cAC3FmE,YAAY,IAAI,IAAI,GAAGQ,mBAAmB,CAAC7D,KAAK,CAAC1G,MAAM,CAAC;YAC5D,CAAC,MACI;cACD;cACA,IAAIzC,SAAS,EAAE;gBACX;cACJ;cACA;cACA,IAAIyM,IAAI,CAAC5N,OAAO,CAAC,IAAI,CAAC+K,KAAK,CAAC3K,KAAK,CAACa,aAAa,EAAE,MAAM,CAAC,CAACgN,MAAM,CAAC,IAAI,CAAClD,KAAK,CAAC3K,KAAK,CAACW,YAAY,CAAC,IAAI,CAAC,EAAE;gBAAE;gBACnG;cACJ;cACA,IAAIiD,gBAAgB,CAACwE,IAAI,CAACoF,IAAI,CAAC,EAAE;gBAC7B;cACJ;cACA,IAAI3J,iBAAiB,CAACuE,IAAI,CAACoF,IAAI,CAAC,EAAE;gBAC9B;cACJ;cACA,IAAI7J,OAAO,CAACyE,IAAI,CAACoF,IAAI,CAAC,EAAE;gBACpB;cACJ;cACAD,YAAY,IAAI,IAAI,GAAGI,QAAQ;YACnC;YACA,IAAI,CAAC5M,SAAS,IAAI,CAAC4M,QAAQ,CAACvE,IAAI,CAAC,CAAC,EAAE;cAAE;cAClCrI,SAAS,GAAG,IAAI;YACpB;YACA0J,GAAG,IAAIqD,OAAO,GAAG,IAAI;YACrBhC,GAAG,GAAGA,GAAG,CAACgB,SAAS,CAACgB,OAAO,CAACxE,MAAM,GAAG,CAAC,CAAC;YACvCkE,IAAI,GAAGO,mBAAmB,CAAC7D,KAAK,CAAC1G,MAAM,CAAC;UAC5C;QACJ;QACA,IAAI,CAACoB,IAAI,CAACsI,KAAK,EAAE;UACb;UACA,IAAIG,iBAAiB,EAAE;YACnBzI,IAAI,CAACsI,KAAK,GAAG,IAAI;UACrB,CAAC,MACI,IAAI,IAAI,CAACvC,KAAK,CAAC3K,KAAK,CAACgB,eAAe,CAACoH,IAAI,CAACqC,GAAG,CAAC,EAAE;YACjD4C,iBAAiB,GAAG,IAAI;UAC5B;QACJ;QACA,IAAIW,MAAM,GAAG,IAAI;QACjB,IAAIC,SAAS;QACb;QACA,IAAI,IAAI,CAACtC,OAAO,CAAChN,GAAG,EAAE;UAClBqP,MAAM,GAAG,IAAI,CAACrD,KAAK,CAAC3K,KAAK,CAACsB,UAAU,CAAChC,IAAI,CAACiO,YAAY,CAAC;UACvD,IAAIS,MAAM,EAAE;YACRC,SAAS,GAAGD,MAAM,CAAC,CAAC,CAAC,KAAK,MAAM;YAChCT,YAAY,GAAGA,YAAY,CAAC3N,OAAO,CAAC,IAAI,CAAC+K,KAAK,CAAC3K,KAAK,CAACuB,eAAe,EAAE,EAAE,CAAC;UAC7E;QACJ;QACAqD,IAAI,CAACuI,KAAK,CAACzD,IAAI,CAAC;UACZsB,IAAI,EAAE,WAAW;UACjBP,GAAG;UACHyD,IAAI,EAAE,CAAC,CAACF,MAAM;UACdG,OAAO,EAAEF,SAAS;UAClBf,KAAK,EAAE,KAAK;UACZ7H,IAAI,EAAEkI,YAAY;UAClBtC,MAAM,EAAE;QACZ,CAAC,CAAC;QACFrG,IAAI,CAAC6F,GAAG,IAAIA,GAAG;MACnB;MACA;MACA,MAAM2D,QAAQ,GAAGxJ,IAAI,CAACuI,KAAK,CAAC5D,EAAE,CAAC,CAAC,CAAC,CAAC;MAClC,IAAI6E,QAAQ,EAAE;QACVA,QAAQ,CAAC3D,GAAG,GAAG2D,QAAQ,CAAC3D,GAAG,CAAC4D,OAAO,CAAC,CAAC;QACrCD,QAAQ,CAAC/I,IAAI,GAAG+I,QAAQ,CAAC/I,IAAI,CAACgJ,OAAO,CAAC,CAAC;MAC3C,CAAC,MACI;QACD;QACA;MACJ;MACAzJ,IAAI,CAAC6F,GAAG,GAAG7F,IAAI,CAAC6F,GAAG,CAAC4D,OAAO,CAAC,CAAC;MAC7B;MACA,KAAK,IAAIlF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvE,IAAI,CAACuI,KAAK,CAAC7D,MAAM,EAAEH,CAAC,EAAE,EAAE;QACxC,IAAI,CAACuB,KAAK,CAACG,KAAK,CAAC2B,GAAG,GAAG,KAAK;QAC5B5H,IAAI,CAACuI,KAAK,CAAChE,CAAC,CAAC,CAAC8B,MAAM,GAAG,IAAI,CAACP,KAAK,CAAC+B,WAAW,CAAC7H,IAAI,CAACuI,KAAK,CAAChE,CAAC,CAAC,CAAC9D,IAAI,EAAE,EAAE,CAAC;QACrE,IAAI,CAACT,IAAI,CAACsI,KAAK,EAAE;UACb;UACA,MAAMoB,OAAO,GAAG1J,IAAI,CAACuI,KAAK,CAAChE,CAAC,CAAC,CAAC8B,MAAM,CAACsD,MAAM,CAACd,CAAC,IAAIA,CAAC,CAACzC,IAAI,KAAK,OAAO,CAAC;UACpE,MAAMwD,qBAAqB,GAAGF,OAAO,CAAChF,MAAM,GAAG,CAAC,IAAIgF,OAAO,CAACG,IAAI,CAAChB,CAAC,IAAI,IAAI,CAAC9C,KAAK,CAAC3K,KAAK,CAACwB,OAAO,CAAC4G,IAAI,CAACqF,CAAC,CAAChD,GAAG,CAAC,CAAC;UAC3G7F,IAAI,CAACsI,KAAK,GAAGsB,qBAAqB;QACtC;MACJ;MACA;MACA,IAAI5J,IAAI,CAACsI,KAAK,EAAE;QACZ,KAAK,IAAI/D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvE,IAAI,CAACuI,KAAK,CAAC7D,MAAM,EAAEH,CAAC,EAAE,EAAE;UACxCvE,IAAI,CAACuI,KAAK,CAAChE,CAAC,CAAC,CAAC+D,KAAK,GAAG,IAAI;QAC9B;MACJ;MACA,OAAOtI,IAAI;IACf;EACJ;EACAG,IAAIA,CAAC+G,GAAG,EAAE;IACN,MAAMtB,GAAG,GAAG,IAAI,CAACG,KAAK,CAAC9C,KAAK,CAAC9C,IAAI,CAACzF,IAAI,CAACwM,GAAG,CAAC;IAC3C,IAAItB,GAAG,EAAE;MACL,MAAMO,KAAK,GAAG;QACVC,IAAI,EAAE,MAAM;QACZnD,KAAK,EAAE,IAAI;QACX4C,GAAG,EAAED,GAAG,CAAC,CAAC,CAAC;QACXkE,GAAG,EAAElE,GAAG,CAAC,CAAC,CAAC,KAAK,KAAK,IAAIA,GAAG,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAIA,GAAG,CAAC,CAAC,CAAC,KAAK,OAAO;QAClEnF,IAAI,EAAEmF,GAAG,CAAC,CAAC;MACf,CAAC;MACD,OAAOO,KAAK;IAChB;EACJ;EACApG,GAAGA,CAACmH,GAAG,EAAE;IACL,MAAMtB,GAAG,GAAG,IAAI,CAACG,KAAK,CAAC9C,KAAK,CAAClD,GAAG,CAACrF,IAAI,CAACwM,GAAG,CAAC;IAC1C,IAAItB,GAAG,EAAE;MACL,MAAMzD,GAAG,GAAGyD,GAAG,CAAC,CAAC,CAAC,CAACmE,WAAW,CAAC,CAAC,CAAC/O,OAAO,CAAC,IAAI,CAAC+K,KAAK,CAAC3K,KAAK,CAACc,mBAAmB,EAAE,GAAG,CAAC;MACnF,MAAMwH,IAAI,GAAGkC,GAAG,CAAC,CAAC,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC,CAAC5K,OAAO,CAAC,IAAI,CAAC+K,KAAK,CAAC3K,KAAK,CAACyB,YAAY,EAAE,IAAI,CAAC,CAAC7B,OAAO,CAAC,IAAI,CAAC+K,KAAK,CAAC5C,MAAM,CAACnB,cAAc,EAAE,IAAI,CAAC,GAAG,EAAE;MAC9H,MAAMgE,KAAK,GAAGJ,GAAG,CAAC,CAAC,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC,CAACsC,SAAS,CAAC,CAAC,EAAEtC,GAAG,CAAC,CAAC,CAAC,CAAClB,MAAM,GAAG,CAAC,CAAC,CAAC1J,OAAO,CAAC,IAAI,CAAC+K,KAAK,CAAC5C,MAAM,CAACnB,cAAc,EAAE,IAAI,CAAC,GAAG4D,GAAG,CAAC,CAAC,CAAC;MACtH,OAAO;QACHQ,IAAI,EAAE,KAAK;QACXjE,GAAG;QACH0D,GAAG,EAAED,GAAG,CAAC,CAAC,CAAC;QACXlC,IAAI;QACJsC;MACJ,CAAC;IACL;EACJ;EACAxF,KAAKA,CAAC0G,GAAG,EAAE;IACP,MAAMtB,GAAG,GAAG,IAAI,CAACG,KAAK,CAAC9C,KAAK,CAACzC,KAAK,CAAC9F,IAAI,CAACwM,GAAG,CAAC;IAC5C,IAAI,CAACtB,GAAG,EAAE;MACN;IACJ;IACA,IAAI,CAAC,IAAI,CAACG,KAAK,CAAC3K,KAAK,CAAC0B,cAAc,CAAC0G,IAAI,CAACoC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;MAC/C;MACA;IACJ;IACA,MAAMoE,OAAO,GAAGpG,UAAU,CAACgC,GAAG,CAAC,CAAC,CAAC,CAAC;IAClC,MAAMqE,MAAM,GAAGrE,GAAG,CAAC,CAAC,CAAC,CAAC5K,OAAO,CAAC,IAAI,CAAC+K,KAAK,CAAC3K,KAAK,CAAC2B,eAAe,EAAE,EAAE,CAAC,CAACuH,KAAK,CAAC,GAAG,CAAC;IAC9E,MAAM4F,IAAI,GAAGtE,GAAG,CAAC,CAAC,CAAC,EAAEpB,IAAI,CAAC,CAAC,GAAGoB,GAAG,CAAC,CAAC,CAAC,CAAC5K,OAAO,CAAC,IAAI,CAAC+K,KAAK,CAAC3K,KAAK,CAAC4B,iBAAiB,EAAE,EAAE,CAAC,CAACsH,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE;IACrG,MAAM6F,IAAI,GAAG;MACT/D,IAAI,EAAE,OAAO;MACbP,GAAG,EAAED,GAAG,CAAC,CAAC,CAAC;MACXwE,MAAM,EAAE,EAAE;MACVC,KAAK,EAAE,EAAE;MACTH,IAAI,EAAE;IACV,CAAC;IACD,IAAIF,OAAO,CAACtF,MAAM,KAAKuF,MAAM,CAACvF,MAAM,EAAE;MAClC;MACA;IACJ;IACA,KAAK,MAAM2F,KAAK,IAAIJ,MAAM,EAAE;MACxB,IAAI,IAAI,CAAClE,KAAK,CAAC3K,KAAK,CAAC6B,eAAe,CAACuG,IAAI,CAAC6G,KAAK,CAAC,EAAE;QAC9CF,IAAI,CAACE,KAAK,CAACvF,IAAI,CAAC,OAAO,CAAC;MAC5B,CAAC,MACI,IAAI,IAAI,CAACiB,KAAK,CAAC3K,KAAK,CAAC8B,gBAAgB,CAACsG,IAAI,CAAC6G,KAAK,CAAC,EAAE;QACpDF,IAAI,CAACE,KAAK,CAACvF,IAAI,CAAC,QAAQ,CAAC;MAC7B,CAAC,MACI,IAAI,IAAI,CAACiB,KAAK,CAAC3K,KAAK,CAAC+B,cAAc,CAACqG,IAAI,CAAC6G,KAAK,CAAC,EAAE;QAClDF,IAAI,CAACE,KAAK,CAACvF,IAAI,CAAC,MAAM,CAAC;MAC3B,CAAC,MACI;QACDqF,IAAI,CAACE,KAAK,CAACvF,IAAI,CAAC,IAAI,CAAC;MACzB;IACJ;IACA,KAAK,IAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyF,OAAO,CAACtF,MAAM,EAAEH,CAAC,EAAE,EAAE;MACrC4F,IAAI,CAACC,MAAM,CAACtF,IAAI,CAAC;QACbrE,IAAI,EAAEuJ,OAAO,CAACzF,CAAC,CAAC;QAChB8B,MAAM,EAAE,IAAI,CAACP,KAAK,CAAC3C,MAAM,CAAC6G,OAAO,CAACzF,CAAC,CAAC,CAAC;QACrC6F,MAAM,EAAE,IAAI;QACZC,KAAK,EAAEF,IAAI,CAACE,KAAK,CAAC9F,CAAC;MACvB,CAAC,CAAC;IACN;IACA,KAAK,MAAMR,GAAG,IAAImG,IAAI,EAAE;MACpBC,IAAI,CAACD,IAAI,CAACpF,IAAI,CAAClB,UAAU,CAACG,GAAG,EAAEoG,IAAI,CAACC,MAAM,CAAC1F,MAAM,CAAC,CAAC+B,GAAG,CAAC,CAAC6D,IAAI,EAAE/F,CAAC,KAAK;QAChE,OAAO;UACH9D,IAAI,EAAE6J,IAAI;UACVjE,MAAM,EAAE,IAAI,CAACP,KAAK,CAAC3C,MAAM,CAACmH,IAAI,CAAC;UAC/BF,MAAM,EAAE,KAAK;UACbC,KAAK,EAAEF,IAAI,CAACE,KAAK,CAAC9F,CAAC;QACvB,CAAC;MACL,CAAC,CAAC,CAAC;IACP;IACA,OAAO4F,IAAI;EACf;EACAzK,QAAQA,CAACwH,GAAG,EAAE;IACV,MAAMtB,GAAG,GAAG,IAAI,CAACG,KAAK,CAAC9C,KAAK,CAACvD,QAAQ,CAAChF,IAAI,CAACwM,GAAG,CAAC;IAC/C,IAAItB,GAAG,EAAE;MACL,OAAO;QACHQ,IAAI,EAAE,SAAS;QACfP,GAAG,EAAED,GAAG,CAAC,CAAC,CAAC;QACX0B,KAAK,EAAE1B,GAAG,CAAC,CAAC,CAAC,CAACP,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC;QACvC5E,IAAI,EAAEmF,GAAG,CAAC,CAAC,CAAC;QACZS,MAAM,EAAE,IAAI,CAACP,KAAK,CAAC3C,MAAM,CAACyC,GAAG,CAAC,CAAC,CAAC;MACpC,CAAC;IACL;EACJ;EACAxF,SAASA,CAAC8G,GAAG,EAAE;IACX,MAAMtB,GAAG,GAAG,IAAI,CAACG,KAAK,CAAC9C,KAAK,CAAC7C,SAAS,CAAC1F,IAAI,CAACwM,GAAG,CAAC;IAChD,IAAItB,GAAG,EAAE;MACL,MAAMnF,IAAI,GAAGmF,GAAG,CAAC,CAAC,CAAC,CAACP,MAAM,CAACO,GAAG,CAAC,CAAC,CAAC,CAAClB,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,GAChDkB,GAAG,CAAC,CAAC,CAAC,CAACN,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GACnBM,GAAG,CAAC,CAAC,CAAC;MACZ,OAAO;QACHQ,IAAI,EAAE,WAAW;QACjBP,GAAG,EAAED,GAAG,CAAC,CAAC,CAAC;QACXnF,IAAI;QACJ4F,MAAM,EAAE,IAAI,CAACP,KAAK,CAAC3C,MAAM,CAAC1C,IAAI;MAClC,CAAC;IACL;EACJ;EACAA,IAAIA,CAACyG,GAAG,EAAE;IACN,MAAMtB,GAAG,GAAG,IAAI,CAACG,KAAK,CAAC9C,KAAK,CAACxC,IAAI,CAAC/F,IAAI,CAACwM,GAAG,CAAC;IAC3C,IAAItB,GAAG,EAAE;MACL,OAAO;QACHQ,IAAI,EAAE,MAAM;QACZP,GAAG,EAAED,GAAG,CAAC,CAAC,CAAC;QACXnF,IAAI,EAAEmF,GAAG,CAAC,CAAC,CAAC;QACZS,MAAM,EAAE,IAAI,CAACP,KAAK,CAAC3C,MAAM,CAACyC,GAAG,CAAC,CAAC,CAAC;MACpC,CAAC;IACL;EACJ;EACAhD,MAAMA,CAACsE,GAAG,EAAE;IACR,MAAMtB,GAAG,GAAG,IAAI,CAACG,KAAK,CAAC5C,MAAM,CAACP,MAAM,CAAClI,IAAI,CAACwM,GAAG,CAAC;IAC9C,IAAItB,GAAG,EAAE;MACL,OAAO;QACHQ,IAAI,EAAE,QAAQ;QACdP,GAAG,EAAED,GAAG,CAAC,CAAC,CAAC;QACXnF,IAAI,EAAEmF,GAAG,CAAC,CAAC;MACf,CAAC;IACL;EACJ;EACAzD,GAAGA,CAAC+E,GAAG,EAAE;IACL,MAAMtB,GAAG,GAAG,IAAI,CAACG,KAAK,CAAC5C,MAAM,CAAChB,GAAG,CAACzH,IAAI,CAACwM,GAAG,CAAC;IAC3C,IAAItB,GAAG,EAAE;MACL,IAAI,CAAC,IAAI,CAACE,KAAK,CAACG,KAAK,CAACC,MAAM,IAAI,IAAI,CAACH,KAAK,CAAC3K,KAAK,CAACgC,SAAS,CAACoG,IAAI,CAACoC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;QACrE,IAAI,CAACE,KAAK,CAACG,KAAK,CAACC,MAAM,GAAG,IAAI;MAClC,CAAC,MACI,IAAI,IAAI,CAACJ,KAAK,CAACG,KAAK,CAACC,MAAM,IAAI,IAAI,CAACH,KAAK,CAAC3K,KAAK,CAACiC,OAAO,CAACmG,IAAI,CAACoC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;QACvE,IAAI,CAACE,KAAK,CAACG,KAAK,CAACC,MAAM,GAAG,KAAK;MACnC;MACA,IAAI,CAAC,IAAI,CAACJ,KAAK,CAACG,KAAK,CAACsE,UAAU,IAAI,IAAI,CAACxE,KAAK,CAAC3K,KAAK,CAACkC,iBAAiB,CAACkG,IAAI,CAACoC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;QACjF,IAAI,CAACE,KAAK,CAACG,KAAK,CAACsE,UAAU,GAAG,IAAI;MACtC,CAAC,MACI,IAAI,IAAI,CAACzE,KAAK,CAACG,KAAK,CAACsE,UAAU,IAAI,IAAI,CAACxE,KAAK,CAAC3K,KAAK,CAACmC,eAAe,CAACiG,IAAI,CAACoC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;QACnF,IAAI,CAACE,KAAK,CAACG,KAAK,CAACsE,UAAU,GAAG,KAAK;MACvC;MACA,OAAO;QACHnE,IAAI,EAAE,MAAM;QACZP,GAAG,EAAED,GAAG,CAAC,CAAC,CAAC;QACXM,MAAM,EAAE,IAAI,CAACJ,KAAK,CAACG,KAAK,CAACC,MAAM;QAC/BqE,UAAU,EAAE,IAAI,CAACzE,KAAK,CAACG,KAAK,CAACsE,UAAU;QACvCtH,KAAK,EAAE,KAAK;QACZxC,IAAI,EAAEmF,GAAG,CAAC,CAAC;MACf,CAAC;IACL;EACJ;EACAvD,IAAIA,CAAC6E,GAAG,EAAE;IACN,MAAMtB,GAAG,GAAG,IAAI,CAACG,KAAK,CAAC5C,MAAM,CAACd,IAAI,CAAC3H,IAAI,CAACwM,GAAG,CAAC;IAC5C,IAAItB,GAAG,EAAE;MACL,MAAM4E,UAAU,GAAG5E,GAAG,CAAC,CAAC,CAAC,CAACpB,IAAI,CAAC,CAAC;MAChC,IAAI,CAAC,IAAI,CAACuC,OAAO,CAAC9M,QAAQ,IAAI,IAAI,CAAC8L,KAAK,CAAC3K,KAAK,CAACoC,iBAAiB,CAACgG,IAAI,CAACgH,UAAU,CAAC,EAAE;QAC/E;QACA,IAAI,CAAE,IAAI,CAACzE,KAAK,CAAC3K,KAAK,CAACqC,eAAe,CAAC+F,IAAI,CAACgH,UAAU,CAAE,EAAE;UACtD;QACJ;QACA;QACA,MAAMC,UAAU,GAAG1F,KAAK,CAACyF,UAAU,CAAClF,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;QACvD,IAAI,CAACkF,UAAU,CAAC9F,MAAM,GAAG+F,UAAU,CAAC/F,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;UACnD;QACJ;MACJ,CAAC,MACI;QACD;QACA,MAAMgG,cAAc,GAAGnF,kBAAkB,CAACK,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;QACvD,IAAI8E,cAAc,GAAG,CAAC,CAAC,EAAE;UACrB,MAAMrC,KAAK,GAAGzC,GAAG,CAAC,CAAC,CAAC,CAACH,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;UAC/C,MAAMkF,OAAO,GAAGtC,KAAK,GAAGzC,GAAG,CAAC,CAAC,CAAC,CAAClB,MAAM,GAAGgG,cAAc;UACtD9E,GAAG,CAAC,CAAC,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC,CAACsC,SAAS,CAAC,CAAC,EAAEwC,cAAc,CAAC;UAC5C9E,GAAG,CAAC,CAAC,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC,CAACsC,SAAS,CAAC,CAAC,EAAEyC,OAAO,CAAC,CAACnG,IAAI,CAAC,CAAC;UAC5CoB,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE;QACf;MACJ;MACA,IAAIlC,IAAI,GAAGkC,GAAG,CAAC,CAAC,CAAC;MACjB,IAAII,KAAK,GAAG,EAAE;MACd,IAAI,IAAI,CAACe,OAAO,CAAC9M,QAAQ,EAAE;QACvB;QACA,MAAMoI,IAAI,GAAG,IAAI,CAAC0D,KAAK,CAAC3K,KAAK,CAACsC,iBAAiB,CAAChD,IAAI,CAACgJ,IAAI,CAAC;QAC1D,IAAIrB,IAAI,EAAE;UACNqB,IAAI,GAAGrB,IAAI,CAAC,CAAC,CAAC;UACd2D,KAAK,GAAG3D,IAAI,CAAC,CAAC,CAAC;QACnB;MACJ,CAAC,MACI;QACD2D,KAAK,GAAGJ,GAAG,CAAC,CAAC,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC,CAACN,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE;MAC7C;MACA5B,IAAI,GAAGA,IAAI,CAACc,IAAI,CAAC,CAAC;MAClB,IAAI,IAAI,CAACuB,KAAK,CAAC3K,KAAK,CAACoC,iBAAiB,CAACgG,IAAI,CAACE,IAAI,CAAC,EAAE;QAC/C,IAAI,IAAI,CAACqD,OAAO,CAAC9M,QAAQ,IAAI,CAAE,IAAI,CAAC8L,KAAK,CAAC3K,KAAK,CAACqC,eAAe,CAAC+F,IAAI,CAACgH,UAAU,CAAE,EAAE;UAC/E;UACA9G,IAAI,GAAGA,IAAI,CAAC4B,KAAK,CAAC,CAAC,CAAC;QACxB,CAAC,MACI;UACD5B,IAAI,GAAGA,IAAI,CAAC4B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC5B;MACJ;MACA,OAAOK,UAAU,CAACC,GAAG,EAAE;QACnBlC,IAAI,EAAEA,IAAI,GAAGA,IAAI,CAAC1I,OAAO,CAAC,IAAI,CAAC+K,KAAK,CAAC5C,MAAM,CAACnB,cAAc,EAAE,IAAI,CAAC,GAAG0B,IAAI;QACxEsC,KAAK,EAAEA,KAAK,GAAGA,KAAK,CAAChL,OAAO,CAAC,IAAI,CAAC+K,KAAK,CAAC5C,MAAM,CAACnB,cAAc,EAAE,IAAI,CAAC,GAAGgE;MAC3E,CAAC,EAAEJ,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAACE,KAAK,EAAE,IAAI,CAACC,KAAK,CAAC;IACtC;EACJ;EACAzD,OAAOA,CAAC4E,GAAG,EAAE0D,KAAK,EAAE;IAChB,IAAIhF,GAAG;IACP,IAAI,CAACA,GAAG,GAAG,IAAI,CAACG,KAAK,CAAC5C,MAAM,CAACb,OAAO,CAAC5H,IAAI,CAACwM,GAAG,CAAC,MACtCtB,GAAG,GAAG,IAAI,CAACG,KAAK,CAAC5C,MAAM,CAACZ,MAAM,CAAC7H,IAAI,CAACwM,GAAG,CAAC,CAAC,EAAE;MAC/C,MAAM2D,UAAU,GAAG,CAACjF,GAAG,CAAC,CAAC,CAAC,IAAIA,GAAG,CAAC,CAAC,CAAC,EAAE5K,OAAO,CAAC,IAAI,CAAC+K,KAAK,CAAC3K,KAAK,CAACc,mBAAmB,EAAE,GAAG,CAAC;MACxF,MAAMmG,IAAI,GAAGuI,KAAK,CAACC,UAAU,CAACd,WAAW,CAAC,CAAC,CAAC;MAC5C,IAAI,CAAC1H,IAAI,EAAE;QACP,MAAM5B,IAAI,GAAGmF,GAAG,CAAC,CAAC,CAAC,CAACP,MAAM,CAAC,CAAC,CAAC;QAC7B,OAAO;UACHe,IAAI,EAAE,MAAM;UACZP,GAAG,EAAEpF,IAAI;UACTA;QACJ,CAAC;MACL;MACA,OAAOkF,UAAU,CAACC,GAAG,EAAEvD,IAAI,EAAEuD,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAACE,KAAK,EAAE,IAAI,CAACC,KAAK,CAAC;IAChE;EACJ;EACA+E,QAAQA,CAAC5D,GAAG,EAAE6D,SAAS,EAAEC,QAAQ,GAAG,EAAE,EAAE;IACpC,IAAIhH,KAAK,GAAG,IAAI,CAAC+B,KAAK,CAAC5C,MAAM,CAACzB,cAAc,CAAChH,IAAI,CAACwM,GAAG,CAAC;IACtD,IAAI,CAAClD,KAAK,EACN;IACJ;IACA,IAAIA,KAAK,CAAC,CAAC,CAAC,IAAIgH,QAAQ,CAAChH,KAAK,CAAC,IAAI,CAAC+B,KAAK,CAAC3K,KAAK,CAACuC,mBAAmB,CAAC,EAChE;IACJ,MAAMsN,QAAQ,GAAGjH,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE;IAC3C,IAAI,CAACiH,QAAQ,IAAI,CAACD,QAAQ,IAAI,IAAI,CAACjF,KAAK,CAAC5C,MAAM,CAAC/B,WAAW,CAAC1G,IAAI,CAACsQ,QAAQ,CAAC,EAAE;MACxE;MACA,MAAME,OAAO,GAAG,CAAC,GAAGlH,KAAK,CAAC,CAAC,CAAC,CAAC,CAACU,MAAM,GAAG,CAAC;MACxC,IAAIyG,MAAM;QAAEC,OAAO;QAAEC,UAAU,GAAGH,OAAO;QAAEI,aAAa,GAAG,CAAC;MAC5D,MAAMC,MAAM,GAAGvH,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC+B,KAAK,CAAC5C,MAAM,CAACtB,iBAAiB,GAAG,IAAI,CAACkE,KAAK,CAAC5C,MAAM,CAACpB,iBAAiB;MAC9GwJ,MAAM,CAACC,SAAS,GAAG,CAAC;MACpB;MACAT,SAAS,GAAGA,SAAS,CAACzF,KAAK,CAAC,CAAC,CAAC,GAAG4B,GAAG,CAACxC,MAAM,GAAGwG,OAAO,CAAC;MACtD,OAAO,CAAClH,KAAK,GAAGuH,MAAM,CAAC7Q,IAAI,CAACqQ,SAAS,CAAC,KAAK,IAAI,EAAE;QAC7CI,MAAM,GAAGnH,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC;QAC7E,IAAI,CAACmH,MAAM,EACP,SAAS,CAAC;QACdC,OAAO,GAAG,CAAC,GAAGD,MAAM,CAAC,CAACzG,MAAM;QAC5B,IAAIV,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,EAAE;UAAE;UACxBqH,UAAU,IAAID,OAAO;UACrB;QACJ,CAAC,MACI,IAAIpH,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,EAAE;UAAE;UAC7B,IAAIkH,OAAO,GAAG,CAAC,IAAI,EAAE,CAACA,OAAO,GAAGE,OAAO,IAAI,CAAC,CAAC,EAAE;YAC3CE,aAAa,IAAIF,OAAO;YACxB,SAAS,CAAC;UACd;QACJ;QACAC,UAAU,IAAID,OAAO;QACrB,IAAIC,UAAU,GAAG,CAAC,EACd,SAAS,CAAC;QACd;QACAD,OAAO,GAAGvM,IAAI,CAACC,GAAG,CAACsM,OAAO,EAAEA,OAAO,GAAGC,UAAU,GAAGC,aAAa,CAAC;QACjE;QACA,MAAMG,cAAc,GAAG,CAAC,GAAGzH,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACU,MAAM;QAC9C,MAAMmB,GAAG,GAAGqB,GAAG,CAAC5B,KAAK,CAAC,CAAC,EAAE4F,OAAO,GAAGlH,KAAK,CAAC0H,KAAK,GAAGD,cAAc,GAAGL,OAAO,CAAC;QAC1E;QACA,IAAIvM,IAAI,CAACC,GAAG,CAACoM,OAAO,EAAEE,OAAO,CAAC,GAAG,CAAC,EAAE;UAChC,MAAM3K,IAAI,GAAGoF,GAAG,CAACP,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAC7B,OAAO;YACHc,IAAI,EAAE,IAAI;YACVP,GAAG;YACHpF,IAAI;YACJ4F,MAAM,EAAE,IAAI,CAACP,KAAK,CAACQ,YAAY,CAAC7F,IAAI;UACxC,CAAC;QACL;QACA;QACA,MAAMA,IAAI,GAAGoF,GAAG,CAACP,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC7B,OAAO;UACHc,IAAI,EAAE,QAAQ;UACdP,GAAG;UACHpF,IAAI;UACJ4F,MAAM,EAAE,IAAI,CAACP,KAAK,CAACQ,YAAY,CAAC7F,IAAI;QACxC,CAAC;MACL;IACJ;EACJ;EACAkL,QAAQA,CAACzE,GAAG,EAAE;IACV,MAAMtB,GAAG,GAAG,IAAI,CAACG,KAAK,CAAC5C,MAAM,CAAC5C,IAAI,CAAC7F,IAAI,CAACwM,GAAG,CAAC;IAC5C,IAAItB,GAAG,EAAE;MACL,IAAInF,IAAI,GAAGmF,GAAG,CAAC,CAAC,CAAC,CAAC5K,OAAO,CAAC,IAAI,CAAC+K,KAAK,CAAC3K,KAAK,CAACY,iBAAiB,EAAE,GAAG,CAAC;MAClE,MAAM4P,gBAAgB,GAAG,IAAI,CAAC7F,KAAK,CAAC3K,KAAK,CAACW,YAAY,CAACyH,IAAI,CAAC/C,IAAI,CAAC;MACjE,MAAMoL,uBAAuB,GAAG,IAAI,CAAC9F,KAAK,CAAC3K,KAAK,CAACS,iBAAiB,CAAC2H,IAAI,CAAC/C,IAAI,CAAC,IAAI,IAAI,CAACsF,KAAK,CAAC3K,KAAK,CAACU,eAAe,CAAC0H,IAAI,CAAC/C,IAAI,CAAC;MAC5H,IAAImL,gBAAgB,IAAIC,uBAAuB,EAAE;QAC7CpL,IAAI,GAAGA,IAAI,CAACyH,SAAS,CAAC,CAAC,EAAEzH,IAAI,CAACiE,MAAM,GAAG,CAAC,CAAC;MAC7C;MACA,OAAO;QACH0B,IAAI,EAAE,UAAU;QAChBP,GAAG,EAAED,GAAG,CAAC,CAAC,CAAC;QACXnF;MACJ,CAAC;IACL;EACJ;EACAM,EAAEA,CAACmG,GAAG,EAAE;IACJ,MAAMtB,GAAG,GAAG,IAAI,CAACG,KAAK,CAAC5C,MAAM,CAACpC,EAAE,CAACrG,IAAI,CAACwM,GAAG,CAAC;IAC1C,IAAItB,GAAG,EAAE;MACL,OAAO;QACHQ,IAAI,EAAE,IAAI;QACVP,GAAG,EAAED,GAAG,CAAC,CAAC;MACd,CAAC;IACL;EACJ;EACAjD,GAAGA,CAACuE,GAAG,EAAE;IACL,MAAMtB,GAAG,GAAG,IAAI,CAACG,KAAK,CAAC5C,MAAM,CAACR,GAAG,CAACjI,IAAI,CAACwM,GAAG,CAAC;IAC3C,IAAItB,GAAG,EAAE;MACL,OAAO;QACHQ,IAAI,EAAE,KAAK;QACXP,GAAG,EAAED,GAAG,CAAC,CAAC,CAAC;QACXnF,IAAI,EAAEmF,GAAG,CAAC,CAAC,CAAC;QACZS,MAAM,EAAE,IAAI,CAACP,KAAK,CAACQ,YAAY,CAACV,GAAG,CAAC,CAAC,CAAC;MAC1C,CAAC;IACL;EACJ;EACA3D,QAAQA,CAACiF,GAAG,EAAE;IACV,MAAMtB,GAAG,GAAG,IAAI,CAACG,KAAK,CAAC5C,MAAM,CAAClB,QAAQ,CAACvH,IAAI,CAACwM,GAAG,CAAC;IAChD,IAAItB,GAAG,EAAE;MACL,IAAInF,IAAI,EAAEiD,IAAI;MACd,IAAIkC,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QAChBnF,IAAI,GAAGmF,GAAG,CAAC,CAAC,CAAC;QACblC,IAAI,GAAG,SAAS,GAAGjD,IAAI;MAC3B,CAAC,MACI;QACDA,IAAI,GAAGmF,GAAG,CAAC,CAAC,CAAC;QACblC,IAAI,GAAGjD,IAAI;MACf;MACA,OAAO;QACH2F,IAAI,EAAE,MAAM;QACZP,GAAG,EAAED,GAAG,CAAC,CAAC,CAAC;QACXnF,IAAI;QACJiD,IAAI;QACJ2C,MAAM,EAAE,CACJ;UACID,IAAI,EAAE,MAAM;UACZP,GAAG,EAAEpF,IAAI;UACTA;QACJ,CAAC;MAET,CAAC;IACL;EACJ;EACAoC,GAAGA,CAACqE,GAAG,EAAE;IACL,IAAItB,GAAG;IACP,IAAIA,GAAG,GAAG,IAAI,CAACG,KAAK,CAAC5C,MAAM,CAACN,GAAG,CAACnI,IAAI,CAACwM,GAAG,CAAC,EAAE;MACvC,IAAIzG,IAAI,EAAEiD,IAAI;MACd,IAAIkC,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QAChBnF,IAAI,GAAGmF,GAAG,CAAC,CAAC,CAAC;QACblC,IAAI,GAAG,SAAS,GAAGjD,IAAI;MAC3B,CAAC,MACI;QACD;QACA,IAAIqL,WAAW;QACf,GAAG;UACCA,WAAW,GAAGlG,GAAG,CAAC,CAAC,CAAC;UACpBA,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAACG,KAAK,CAAC5C,MAAM,CAACT,UAAU,CAAChI,IAAI,CAACkL,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE;QACjE,CAAC,QAAQkG,WAAW,KAAKlG,GAAG,CAAC,CAAC,CAAC;QAC/BnF,IAAI,GAAGmF,GAAG,CAAC,CAAC,CAAC;QACb,IAAIA,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,EAAE;UACnBlC,IAAI,GAAG,SAAS,GAAGkC,GAAG,CAAC,CAAC,CAAC;QAC7B,CAAC,MACI;UACDlC,IAAI,GAAGkC,GAAG,CAAC,CAAC,CAAC;QACjB;MACJ;MACA,OAAO;QACHQ,IAAI,EAAE,MAAM;QACZP,GAAG,EAAED,GAAG,CAAC,CAAC,CAAC;QACXnF,IAAI;QACJiD,IAAI;QACJ2C,MAAM,EAAE,CACJ;UACID,IAAI,EAAE,MAAM;UACZP,GAAG,EAAEpF,IAAI;UACTA;QACJ,CAAC;MAET,CAAC;IACL;EACJ;EACAO,UAAUA,CAACkG,GAAG,EAAE;IACZ,MAAMtB,GAAG,GAAG,IAAI,CAACG,KAAK,CAAC5C,MAAM,CAAC1C,IAAI,CAAC/F,IAAI,CAACwM,GAAG,CAAC;IAC5C,IAAItB,GAAG,EAAE;MACL,MAAMzB,OAAO,GAAG,IAAI,CAAC2B,KAAK,CAACG,KAAK,CAACsE,UAAU;MAC3C,OAAO;QACHnE,IAAI,EAAE,MAAM;QACZP,GAAG,EAAED,GAAG,CAAC,CAAC,CAAC;QACXnF,IAAI,EAAEmF,GAAG,CAAC,CAAC,CAAC;QACZzB;MACJ,CAAC;IACL;EACJ;AACJ;;AAEA;AACA;AACA;AACA,MAAM4H,MAAM,CAAC;EACT1F,MAAM;EACNU,OAAO;EACPd,KAAK;EACL7L,SAAS;EACT4R,WAAW;EACXhF,WAAWA,CAACD,OAAO,EAAE;IACjB;IACA,IAAI,CAACV,MAAM,GAAG,EAAE;IAChB,IAAI,CAACA,MAAM,CAACuE,KAAK,GAAGqB,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IACvC,IAAI,CAACnF,OAAO,GAAGA,OAAO,IAAIzM,SAAS;IACnC,IAAI,CAACyM,OAAO,CAAC3M,SAAS,GAAG,IAAI,CAAC2M,OAAO,CAAC3M,SAAS,IAAI,IAAI0M,UAAU,CAAC,CAAC;IACnE,IAAI,CAAC1M,SAAS,GAAG,IAAI,CAAC2M,OAAO,CAAC3M,SAAS;IACvC,IAAI,CAACA,SAAS,CAAC2M,OAAO,GAAG,IAAI,CAACA,OAAO;IACrC,IAAI,CAAC3M,SAAS,CAAC0L,KAAK,GAAG,IAAI;IAC3B,IAAI,CAACkG,WAAW,GAAG,EAAE;IACrB,IAAI,CAAC/F,KAAK,GAAG;MACTC,MAAM,EAAE,KAAK;MACbqE,UAAU,EAAE,KAAK;MACjB3C,GAAG,EAAE;IACT,CAAC;IACD,MAAM7B,KAAK,GAAG;MACV3K,KAAK;MACL6H,KAAK,EAAEA,KAAK,CAACC,MAAM;MACnBC,MAAM,EAAEA,MAAM,CAACD;IACnB,CAAC;IACD,IAAI,IAAI,CAAC6D,OAAO,CAAC9M,QAAQ,EAAE;MACvB8L,KAAK,CAAC9C,KAAK,GAAGA,KAAK,CAAChJ,QAAQ;MAC5B8L,KAAK,CAAC5C,MAAM,GAAGA,MAAM,CAAClJ,QAAQ;IAClC,CAAC,MACI,IAAI,IAAI,CAAC8M,OAAO,CAAChN,GAAG,EAAE;MACvBgM,KAAK,CAAC9C,KAAK,GAAGA,KAAK,CAAClJ,GAAG;MACvB,IAAI,IAAI,CAACgN,OAAO,CAAClN,MAAM,EAAE;QACrBkM,KAAK,CAAC5C,MAAM,GAAGA,MAAM,CAACtJ,MAAM;MAChC,CAAC,MACI;QACDkM,KAAK,CAAC5C,MAAM,GAAGA,MAAM,CAACpJ,GAAG;MAC7B;IACJ;IACA,IAAI,CAACK,SAAS,CAAC2L,KAAK,GAAGA,KAAK;EAChC;EACA;AACJ;AACA;EACI,WAAWA,KAAKA,CAAA,EAAG;IACf,OAAO;MACH9C,KAAK;MACLE;IACJ,CAAC;EACL;EACA;AACJ;AACA;EACI,OAAOgJ,GAAGA,CAACjF,GAAG,EAAEH,OAAO,EAAE;IACrB,MAAMjB,KAAK,GAAG,IAAIiG,MAAM,CAAChF,OAAO,CAAC;IACjC,OAAOjB,KAAK,CAACqG,GAAG,CAACjF,GAAG,CAAC;EACzB;EACA;AACJ;AACA;EACI,OAAOkF,SAASA,CAAClF,GAAG,EAAEH,OAAO,EAAE;IAC3B,MAAMjB,KAAK,GAAG,IAAIiG,MAAM,CAAChF,OAAO,CAAC;IACjC,OAAOjB,KAAK,CAACQ,YAAY,CAACY,GAAG,CAAC;EAClC;EACA;AACJ;AACA;EACIiF,GAAGA,CAACjF,GAAG,EAAE;IACLA,GAAG,GAAGA,GAAG,CAAClM,OAAO,CAACI,KAAK,CAACiD,cAAc,EAAE,IAAI,CAAC;IAC7C,IAAI,CAACwJ,WAAW,CAACX,GAAG,EAAE,IAAI,CAACb,MAAM,CAAC;IAClC,KAAK,IAAI9B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACyH,WAAW,CAACtH,MAAM,EAAEH,CAAC,EAAE,EAAE;MAC9C,MAAM8H,IAAI,GAAG,IAAI,CAACL,WAAW,CAACzH,CAAC,CAAC;MAChC,IAAI,CAAC+B,YAAY,CAAC+F,IAAI,CAACnF,GAAG,EAAEmF,IAAI,CAAChG,MAAM,CAAC;IAC5C;IACA,IAAI,CAAC2F,WAAW,GAAG,EAAE;IACrB,OAAO,IAAI,CAAC3F,MAAM;EACtB;EACAwB,WAAWA,CAACX,GAAG,EAAEb,MAAM,GAAG,EAAE,EAAEiG,oBAAoB,GAAG,KAAK,EAAE;IACxD,IAAI,IAAI,CAACvF,OAAO,CAAC9M,QAAQ,EAAE;MACvBiN,GAAG,GAAGA,GAAG,CAAClM,OAAO,CAACI,KAAK,CAACa,aAAa,EAAE,MAAM,CAAC,CAACjB,OAAO,CAACI,KAAK,CAACkD,SAAS,EAAE,EAAE,CAAC;IAC/E;IACA,OAAO4I,GAAG,EAAE;MACR,IAAIf,KAAK;MACT,IAAI,IAAI,CAACY,OAAO,CAACjN,UAAU,EAAEmJ,KAAK,EAAE4G,IAAI,CAAE0C,YAAY,IAAK;QACvD,IAAIpG,KAAK,GAAGoG,YAAY,CAACC,IAAI,CAAC;UAAE1G,KAAK,EAAE;QAAK,CAAC,EAAEoB,GAAG,EAAEb,MAAM,CAAC,EAAE;UACzDa,GAAG,GAAGA,GAAG,CAACgB,SAAS,CAAC/B,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;UACrC2B,MAAM,CAACvB,IAAI,CAACqB,KAAK,CAAC;UAClB,OAAO,IAAI;QACf;QACA,OAAO,KAAK;MAChB,CAAC,CAAC,EAAE;QACA;MACJ;MACA;MACA,IAAIA,KAAK,GAAG,IAAI,CAAC/L,SAAS,CAAC6M,KAAK,CAACC,GAAG,CAAC,EAAE;QACnCA,GAAG,GAAGA,GAAG,CAACgB,SAAS,CAAC/B,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;QACrC,MAAMoD,SAAS,GAAGzB,MAAM,CAAC1B,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAIwB,KAAK,CAACN,GAAG,CAACnB,MAAM,KAAK,CAAC,IAAIoD,SAAS,KAAK2E,SAAS,EAAE;UACnD;UACA;UACA3E,SAAS,CAACjC,GAAG,IAAI,IAAI;QACzB,CAAC,MACI;UACDQ,MAAM,CAACvB,IAAI,CAACqB,KAAK,CAAC;QACtB;QACA;MACJ;MACA;MACA,IAAIA,KAAK,GAAG,IAAI,CAAC/L,SAAS,CAACmG,IAAI,CAAC2G,GAAG,CAAC,EAAE;QAClCA,GAAG,GAAGA,GAAG,CAACgB,SAAS,CAAC/B,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;QACrC,MAAMoD,SAAS,GAAGzB,MAAM,CAAC1B,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/B;QACA,IAAImD,SAAS,EAAE1B,IAAI,KAAK,WAAW,IAAI0B,SAAS,EAAE1B,IAAI,KAAK,MAAM,EAAE;UAC/D0B,SAAS,CAACjC,GAAG,IAAI,IAAI,GAAGM,KAAK,CAACN,GAAG;UACjCiC,SAAS,CAACrH,IAAI,IAAI,IAAI,GAAG0F,KAAK,CAAC1F,IAAI;UACnC,IAAI,CAACuL,WAAW,CAACrH,EAAE,CAAC,CAAC,CAAC,CAAC,CAACuC,GAAG,GAAGY,SAAS,CAACrH,IAAI;QAChD,CAAC,MACI;UACD4F,MAAM,CAACvB,IAAI,CAACqB,KAAK,CAAC;QACtB;QACA;MACJ;MACA;MACA,IAAIA,KAAK,GAAG,IAAI,CAAC/L,SAAS,CAACiF,MAAM,CAAC6H,GAAG,CAAC,EAAE;QACpCA,GAAG,GAAGA,GAAG,CAACgB,SAAS,CAAC/B,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;QACrC2B,MAAM,CAACvB,IAAI,CAACqB,KAAK,CAAC;QAClB;MACJ;MACA;MACA,IAAIA,KAAK,GAAG,IAAI,CAAC/L,SAAS,CAACmF,OAAO,CAAC2H,GAAG,CAAC,EAAE;QACrCA,GAAG,GAAGA,GAAG,CAACgB,SAAS,CAAC/B,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;QACrC2B,MAAM,CAACvB,IAAI,CAACqB,KAAK,CAAC;QAClB;MACJ;MACA;MACA,IAAIA,KAAK,GAAG,IAAI,CAAC/L,SAAS,CAACkF,EAAE,CAAC4H,GAAG,CAAC,EAAE;QAChCA,GAAG,GAAGA,GAAG,CAACgB,SAAS,CAAC/B,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;QACrC2B,MAAM,CAACvB,IAAI,CAACqB,KAAK,CAAC;QAClB;MACJ;MACA;MACA,IAAIA,KAAK,GAAG,IAAI,CAAC/L,SAAS,CAACiG,UAAU,CAAC6G,GAAG,CAAC,EAAE;QACxCA,GAAG,GAAGA,GAAG,CAACgB,SAAS,CAAC/B,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;QACrC2B,MAAM,CAACvB,IAAI,CAACqB,KAAK,CAAC;QAClB;MACJ;MACA;MACA,IAAIA,KAAK,GAAG,IAAI,CAAC/L,SAAS,CAAC4F,IAAI,CAACkH,GAAG,CAAC,EAAE;QAClCA,GAAG,GAAGA,GAAG,CAACgB,SAAS,CAAC/B,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;QACrC2B,MAAM,CAACvB,IAAI,CAACqB,KAAK,CAAC;QAClB;MACJ;MACA;MACA,IAAIA,KAAK,GAAG,IAAI,CAAC/L,SAAS,CAAC+F,IAAI,CAAC+G,GAAG,CAAC,EAAE;QAClCA,GAAG,GAAGA,GAAG,CAACgB,SAAS,CAAC/B,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;QACrC2B,MAAM,CAACvB,IAAI,CAACqB,KAAK,CAAC;QAClB;MACJ;MACA;MACA,IAAIA,KAAK,GAAG,IAAI,CAAC/L,SAAS,CAAC2F,GAAG,CAACmH,GAAG,CAAC,EAAE;QACjCA,GAAG,GAAGA,GAAG,CAACgB,SAAS,CAAC/B,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;QACrC,MAAMoD,SAAS,GAAGzB,MAAM,CAAC1B,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAImD,SAAS,EAAE1B,IAAI,KAAK,WAAW,IAAI0B,SAAS,EAAE1B,IAAI,KAAK,MAAM,EAAE;UAC/D0B,SAAS,CAACjC,GAAG,IAAI,IAAI,GAAGM,KAAK,CAACN,GAAG;UACjCiC,SAAS,CAACrH,IAAI,IAAI,IAAI,GAAG0F,KAAK,CAACN,GAAG;UAClC,IAAI,CAACmG,WAAW,CAACrH,EAAE,CAAC,CAAC,CAAC,CAAC,CAACuC,GAAG,GAAGY,SAAS,CAACrH,IAAI;QAChD,CAAC,MACI,IAAI,CAAC,IAAI,CAAC4F,MAAM,CAACuE,KAAK,CAACzE,KAAK,CAAChE,GAAG,CAAC,EAAE;UACpC,IAAI,CAACkE,MAAM,CAACuE,KAAK,CAACzE,KAAK,CAAChE,GAAG,CAAC,GAAG;YAC3BuB,IAAI,EAAEyC,KAAK,CAACzC,IAAI;YAChBsC,KAAK,EAAEG,KAAK,CAACH;UACjB,CAAC;QACL;QACA;MACJ;MACA;MACA,IAAIG,KAAK,GAAG,IAAI,CAAC/L,SAAS,CAACoG,KAAK,CAAC0G,GAAG,CAAC,EAAE;QACnCA,GAAG,GAAGA,GAAG,CAACgB,SAAS,CAAC/B,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;QACrC2B,MAAM,CAACvB,IAAI,CAACqB,KAAK,CAAC;QAClB;MACJ;MACA;MACA,IAAIA,KAAK,GAAG,IAAI,CAAC/L,SAAS,CAACsF,QAAQ,CAACwH,GAAG,CAAC,EAAE;QACtCA,GAAG,GAAGA,GAAG,CAACgB,SAAS,CAAC/B,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;QACrC2B,MAAM,CAACvB,IAAI,CAACqB,KAAK,CAAC;QAClB;MACJ;MACA;MACA;MACA,IAAIuG,MAAM,GAAGxF,GAAG;MAChB,IAAI,IAAI,CAACH,OAAO,CAACjN,UAAU,EAAE6S,UAAU,EAAE;QACrC,IAAIC,UAAU,GAAGC,QAAQ;QACzB,MAAMC,OAAO,GAAG5F,GAAG,CAAC5B,KAAK,CAAC,CAAC,CAAC;QAC5B,IAAIyH,SAAS;QACb,IAAI,CAAChG,OAAO,CAACjN,UAAU,CAAC6S,UAAU,CAACK,OAAO,CAAEC,aAAa,IAAK;UAC1DF,SAAS,GAAGE,aAAa,CAACT,IAAI,CAAC;YAAE1G,KAAK,EAAE;UAAK,CAAC,EAAEgH,OAAO,CAAC;UACxD,IAAI,OAAOC,SAAS,KAAK,QAAQ,IAAIA,SAAS,IAAI,CAAC,EAAE;YACjDH,UAAU,GAAG/N,IAAI,CAACC,GAAG,CAAC8N,UAAU,EAAEG,SAAS,CAAC;UAChD;QACJ,CAAC,CAAC;QACF,IAAIH,UAAU,GAAGC,QAAQ,IAAID,UAAU,IAAI,CAAC,EAAE;UAC1CF,MAAM,GAAGxF,GAAG,CAACgB,SAAS,CAAC,CAAC,EAAE0E,UAAU,GAAG,CAAC,CAAC;QAC7C;MACJ;MACA,IAAI,IAAI,CAAC3G,KAAK,CAAC2B,GAAG,KAAKzB,KAAK,GAAG,IAAI,CAAC/L,SAAS,CAACgG,SAAS,CAACsM,MAAM,CAAC,CAAC,EAAE;QAC9D,MAAM5E,SAAS,GAAGzB,MAAM,CAAC1B,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAI2H,oBAAoB,IAAIxE,SAAS,EAAE1B,IAAI,KAAK,WAAW,EAAE;UACzD0B,SAAS,CAACjC,GAAG,IAAI,IAAI,GAAGM,KAAK,CAACN,GAAG;UACjCiC,SAAS,CAACrH,IAAI,IAAI,IAAI,GAAG0F,KAAK,CAAC1F,IAAI;UACnC,IAAI,CAACuL,WAAW,CAACpH,GAAG,CAAC,CAAC;UACtB,IAAI,CAACoH,WAAW,CAACrH,EAAE,CAAC,CAAC,CAAC,CAAC,CAACuC,GAAG,GAAGY,SAAS,CAACrH,IAAI;QAChD,CAAC,MACI;UACD4F,MAAM,CAACvB,IAAI,CAACqB,KAAK,CAAC;QACtB;QACAmG,oBAAoB,GAAGI,MAAM,CAAChI,MAAM,KAAKwC,GAAG,CAACxC,MAAM;QACnDwC,GAAG,GAAGA,GAAG,CAACgB,SAAS,CAAC/B,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;QACrC;MACJ;MACA;MACA,IAAIyB,KAAK,GAAG,IAAI,CAAC/L,SAAS,CAACqG,IAAI,CAACyG,GAAG,CAAC,EAAE;QAClCA,GAAG,GAAGA,GAAG,CAACgB,SAAS,CAAC/B,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;QACrC,MAAMoD,SAAS,GAAGzB,MAAM,CAAC1B,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAImD,SAAS,EAAE1B,IAAI,KAAK,MAAM,EAAE;UAC5B0B,SAAS,CAACjC,GAAG,IAAI,IAAI,GAAGM,KAAK,CAACN,GAAG;UACjCiC,SAAS,CAACrH,IAAI,IAAI,IAAI,GAAG0F,KAAK,CAAC1F,IAAI;UACnC,IAAI,CAACuL,WAAW,CAACpH,GAAG,CAAC,CAAC;UACtB,IAAI,CAACoH,WAAW,CAACrH,EAAE,CAAC,CAAC,CAAC,CAAC,CAACuC,GAAG,GAAGY,SAAS,CAACrH,IAAI;QAChD,CAAC,MACI;UACD4F,MAAM,CAACvB,IAAI,CAACqB,KAAK,CAAC;QACtB;QACA;MACJ;MACA,IAAIe,GAAG,EAAE;QACL,MAAMgG,MAAM,GAAG,yBAAyB,GAAGhG,GAAG,CAACiG,UAAU,CAAC,CAAC,CAAC;QAC5D,IAAI,IAAI,CAACpG,OAAO,CAAC5M,MAAM,EAAE;UACrBiT,OAAO,CAACC,KAAK,CAACH,MAAM,CAAC;UACrB;QACJ,CAAC,MACI;UACD,MAAM,IAAII,KAAK,CAACJ,MAAM,CAAC;QAC3B;MACJ;IACJ;IACA,IAAI,CAACjH,KAAK,CAAC2B,GAAG,GAAG,IAAI;IACrB,OAAOvB,MAAM;EACjB;EACAlD,MAAMA,CAAC+D,GAAG,EAAEb,MAAM,GAAG,EAAE,EAAE;IACrB,IAAI,CAAC2F,WAAW,CAAClH,IAAI,CAAC;MAAEoC,GAAG;MAAEb;IAAO,CAAC,CAAC;IACtC,OAAOA,MAAM;EACjB;EACA;AACJ;AACA;EACIC,YAAYA,CAACY,GAAG,EAAEb,MAAM,GAAG,EAAE,EAAE;IAC3B;IACA,IAAI0E,SAAS,GAAG7D,GAAG;IACnB,IAAIlD,KAAK,GAAG,IAAI;IAChB;IACA,IAAI,IAAI,CAACqC,MAAM,CAACuE,KAAK,EAAE;MACnB,MAAMA,KAAK,GAAGqB,MAAM,CAACsB,IAAI,CAAC,IAAI,CAAClH,MAAM,CAACuE,KAAK,CAAC;MAC5C,IAAIA,KAAK,CAAClG,MAAM,GAAG,CAAC,EAAE;QAClB,OAAO,CAACV,KAAK,GAAG,IAAI,CAAC5J,SAAS,CAAC2L,KAAK,CAAC5C,MAAM,CAACX,aAAa,CAAC9H,IAAI,CAACqQ,SAAS,CAAC,KAAK,IAAI,EAAE;UAChF,IAAIH,KAAK,CAAC4C,QAAQ,CAACxJ,KAAK,CAAC,CAAC,CAAC,CAACsB,KAAK,CAACtB,KAAK,CAAC,CAAC,CAAC,CAACyJ,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;YACnE1C,SAAS,GAAGA,SAAS,CAACzF,KAAK,CAAC,CAAC,EAAEtB,KAAK,CAAC0H,KAAK,CAAC,GACrC,GAAG,GAAG,GAAG,CAAC5C,MAAM,CAAC9E,KAAK,CAAC,CAAC,CAAC,CAACU,MAAM,GAAG,CAAC,CAAC,GAAG,GAAG,GAC3CqG,SAAS,CAACzF,KAAK,CAAC,IAAI,CAAClL,SAAS,CAAC2L,KAAK,CAAC5C,MAAM,CAACX,aAAa,CAACgJ,SAAS,CAAC;UAC9E;QACJ;MACJ;IACJ;IACA;IACA,OAAO,CAACxH,KAAK,GAAG,IAAI,CAAC5J,SAAS,CAAC2L,KAAK,CAAC5C,MAAM,CAAC3B,SAAS,CAAC9G,IAAI,CAACqQ,SAAS,CAAC,KAAK,IAAI,EAAE;MAC5EA,SAAS,GAAGA,SAAS,CAACzF,KAAK,CAAC,CAAC,EAAEtB,KAAK,CAAC0H,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC5C,MAAM,CAAC9E,KAAK,CAAC,CAAC,CAAC,CAACU,MAAM,GAAG,CAAC,CAAC,GAAG,GAAG,GAAGqG,SAAS,CAACzF,KAAK,CAAC,IAAI,CAAClL,SAAS,CAAC2L,KAAK,CAAC5C,MAAM,CAAC3B,SAAS,CAACgK,SAAS,CAAC;IAChK;IACA;IACA,OAAO,CAACxH,KAAK,GAAG,IAAI,CAAC5J,SAAS,CAAC2L,KAAK,CAAC5C,MAAM,CAACnB,cAAc,CAACtH,IAAI,CAACqQ,SAAS,CAAC,KAAK,IAAI,EAAE;MACjFA,SAAS,GAAGA,SAAS,CAACzF,KAAK,CAAC,CAAC,EAAEtB,KAAK,CAAC0H,KAAK,CAAC,GAAG,IAAI,GAAGX,SAAS,CAACzF,KAAK,CAAC,IAAI,CAAClL,SAAS,CAAC2L,KAAK,CAAC5C,MAAM,CAACnB,cAAc,CAACwJ,SAAS,CAAC;IAC9H;IACA,IAAIkC,YAAY,GAAG,KAAK;IACxB,IAAI1C,QAAQ,GAAG,EAAE;IACjB,OAAO9D,GAAG,EAAE;MACR,IAAI,CAACwG,YAAY,EAAE;QACf1C,QAAQ,GAAG,EAAE;MACjB;MACA0C,YAAY,GAAG,KAAK;MACpB,IAAIvH,KAAK;MACT;MACA,IAAI,IAAI,CAACY,OAAO,CAACjN,UAAU,EAAEqJ,MAAM,EAAE0G,IAAI,CAAE0C,YAAY,IAAK;QACxD,IAAIpG,KAAK,GAAGoG,YAAY,CAACC,IAAI,CAAC;UAAE1G,KAAK,EAAE;QAAK,CAAC,EAAEoB,GAAG,EAAEb,MAAM,CAAC,EAAE;UACzDa,GAAG,GAAGA,GAAG,CAACgB,SAAS,CAAC/B,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;UACrC2B,MAAM,CAACvB,IAAI,CAACqB,KAAK,CAAC;UAClB,OAAO,IAAI;QACf;QACA,OAAO,KAAK;MAChB,CAAC,CAAC,EAAE;QACA;MACJ;MACA;MACA,IAAIA,KAAK,GAAG,IAAI,CAAC/L,SAAS,CAACwI,MAAM,CAACsE,GAAG,CAAC,EAAE;QACpCA,GAAG,GAAGA,GAAG,CAACgB,SAAS,CAAC/B,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;QACrC2B,MAAM,CAACvB,IAAI,CAACqB,KAAK,CAAC;QAClB;MACJ;MACA;MACA,IAAIA,KAAK,GAAG,IAAI,CAAC/L,SAAS,CAAC+H,GAAG,CAAC+E,GAAG,CAAC,EAAE;QACjCA,GAAG,GAAGA,GAAG,CAACgB,SAAS,CAAC/B,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;QACrC2B,MAAM,CAACvB,IAAI,CAACqB,KAAK,CAAC;QAClB;MACJ;MACA;MACA,IAAIA,KAAK,GAAG,IAAI,CAAC/L,SAAS,CAACiI,IAAI,CAAC6E,GAAG,CAAC,EAAE;QAClCA,GAAG,GAAGA,GAAG,CAACgB,SAAS,CAAC/B,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;QACrC2B,MAAM,CAACvB,IAAI,CAACqB,KAAK,CAAC;QAClB;MACJ;MACA;MACA,IAAIA,KAAK,GAAG,IAAI,CAAC/L,SAAS,CAACkI,OAAO,CAAC4E,GAAG,EAAE,IAAI,CAACb,MAAM,CAACuE,KAAK,CAAC,EAAE;QACxD1D,GAAG,GAAGA,GAAG,CAACgB,SAAS,CAAC/B,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;QACrC,MAAMoD,SAAS,GAAGzB,MAAM,CAAC1B,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAIwB,KAAK,CAACC,IAAI,KAAK,MAAM,IAAI0B,SAAS,EAAE1B,IAAI,KAAK,MAAM,EAAE;UACrD0B,SAAS,CAACjC,GAAG,IAAIM,KAAK,CAACN,GAAG;UAC1BiC,SAAS,CAACrH,IAAI,IAAI0F,KAAK,CAAC1F,IAAI;QAChC,CAAC,MACI;UACD4F,MAAM,CAACvB,IAAI,CAACqB,KAAK,CAAC;QACtB;QACA;MACJ;MACA;MACA,IAAIA,KAAK,GAAG,IAAI,CAAC/L,SAAS,CAAC0Q,QAAQ,CAAC5D,GAAG,EAAE6D,SAAS,EAAEC,QAAQ,CAAC,EAAE;QAC3D9D,GAAG,GAAGA,GAAG,CAACgB,SAAS,CAAC/B,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;QACrC2B,MAAM,CAACvB,IAAI,CAACqB,KAAK,CAAC;QAClB;MACJ;MACA;MACA,IAAIA,KAAK,GAAG,IAAI,CAAC/L,SAAS,CAACuR,QAAQ,CAACzE,GAAG,CAAC,EAAE;QACtCA,GAAG,GAAGA,GAAG,CAACgB,SAAS,CAAC/B,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;QACrC2B,MAAM,CAACvB,IAAI,CAACqB,KAAK,CAAC;QAClB;MACJ;MACA;MACA,IAAIA,KAAK,GAAG,IAAI,CAAC/L,SAAS,CAAC2G,EAAE,CAACmG,GAAG,CAAC,EAAE;QAChCA,GAAG,GAAGA,GAAG,CAACgB,SAAS,CAAC/B,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;QACrC2B,MAAM,CAACvB,IAAI,CAACqB,KAAK,CAAC;QAClB;MACJ;MACA;MACA,IAAIA,KAAK,GAAG,IAAI,CAAC/L,SAAS,CAACuI,GAAG,CAACuE,GAAG,CAAC,EAAE;QACjCA,GAAG,GAAGA,GAAG,CAACgB,SAAS,CAAC/B,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;QACrC2B,MAAM,CAACvB,IAAI,CAACqB,KAAK,CAAC;QAClB;MACJ;MACA;MACA,IAAIA,KAAK,GAAG,IAAI,CAAC/L,SAAS,CAAC6H,QAAQ,CAACiF,GAAG,CAAC,EAAE;QACtCA,GAAG,GAAGA,GAAG,CAACgB,SAAS,CAAC/B,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;QACrC2B,MAAM,CAACvB,IAAI,CAACqB,KAAK,CAAC;QAClB;MACJ;MACA;MACA,IAAI,CAAC,IAAI,CAACF,KAAK,CAACC,MAAM,KAAKC,KAAK,GAAG,IAAI,CAAC/L,SAAS,CAACyI,GAAG,CAACqE,GAAG,CAAC,CAAC,EAAE;QACzDA,GAAG,GAAGA,GAAG,CAACgB,SAAS,CAAC/B,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;QACrC2B,MAAM,CAACvB,IAAI,CAACqB,KAAK,CAAC;QAClB;MACJ;MACA;MACA;MACA,IAAIuG,MAAM,GAAGxF,GAAG;MAChB,IAAI,IAAI,CAACH,OAAO,CAACjN,UAAU,EAAE6T,WAAW,EAAE;QACtC,IAAIf,UAAU,GAAGC,QAAQ;QACzB,MAAMC,OAAO,GAAG5F,GAAG,CAAC5B,KAAK,CAAC,CAAC,CAAC;QAC5B,IAAIyH,SAAS;QACb,IAAI,CAAChG,OAAO,CAACjN,UAAU,CAAC6T,WAAW,CAACX,OAAO,CAAEC,aAAa,IAAK;UAC3DF,SAAS,GAAGE,aAAa,CAACT,IAAI,CAAC;YAAE1G,KAAK,EAAE;UAAK,CAAC,EAAEgH,OAAO,CAAC;UACxD,IAAI,OAAOC,SAAS,KAAK,QAAQ,IAAIA,SAAS,IAAI,CAAC,EAAE;YACjDH,UAAU,GAAG/N,IAAI,CAACC,GAAG,CAAC8N,UAAU,EAAEG,SAAS,CAAC;UAChD;QACJ,CAAC,CAAC;QACF,IAAIH,UAAU,GAAGC,QAAQ,IAAID,UAAU,IAAI,CAAC,EAAE;UAC1CF,MAAM,GAAGxF,GAAG,CAACgB,SAAS,CAAC,CAAC,EAAE0E,UAAU,GAAG,CAAC,CAAC;QAC7C;MACJ;MACA,IAAIzG,KAAK,GAAG,IAAI,CAAC/L,SAAS,CAAC4G,UAAU,CAAC0L,MAAM,CAAC,EAAE;QAC3CxF,GAAG,GAAGA,GAAG,CAACgB,SAAS,CAAC/B,KAAK,CAACN,GAAG,CAACnB,MAAM,CAAC;QACrC,IAAIyB,KAAK,CAACN,GAAG,CAACP,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;UAAE;UAC/B0F,QAAQ,GAAG7E,KAAK,CAACN,GAAG,CAACP,KAAK,CAAC,CAAC,CAAC,CAAC;QAClC;QACAoI,YAAY,GAAG,IAAI;QACnB,MAAM5F,SAAS,GAAGzB,MAAM,CAAC1B,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAImD,SAAS,EAAE1B,IAAI,KAAK,MAAM,EAAE;UAC5B0B,SAAS,CAACjC,GAAG,IAAIM,KAAK,CAACN,GAAG;UAC1BiC,SAAS,CAACrH,IAAI,IAAI0F,KAAK,CAAC1F,IAAI;QAChC,CAAC,MACI;UACD4F,MAAM,CAACvB,IAAI,CAACqB,KAAK,CAAC;QACtB;QACA;MACJ;MACA,IAAIe,GAAG,EAAE;QACL,MAAMgG,MAAM,GAAG,yBAAyB,GAAGhG,GAAG,CAACiG,UAAU,CAAC,CAAC,CAAC;QAC5D,IAAI,IAAI,CAACpG,OAAO,CAAC5M,MAAM,EAAE;UACrBiT,OAAO,CAACC,KAAK,CAACH,MAAM,CAAC;UACrB;QACJ,CAAC,MACI;UACD,MAAM,IAAII,KAAK,CAACJ,MAAM,CAAC;QAC3B;MACJ;IACJ;IACA,OAAO7G,MAAM;EACjB;AACJ;;AAEA;AACA;AACA;AACA,MAAMuH,SAAS,CAAC;EACZ7G,OAAO;EACP8G,MAAM,CAAC,CAAC;EACR7G,WAAWA,CAACD,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO,IAAIzM,SAAS;EACvC;EACA2M,KAAKA,CAACd,KAAK,EAAE;IACT,OAAO,EAAE;EACb;EACA5F,IAAIA,CAAC;IAAEE,IAAI;IAAE2G,IAAI;IAAEjD;EAAQ,CAAC,EAAE;IAC1B,MAAM2J,UAAU,GAAG,CAAC1G,IAAI,IAAI,EAAE,EAAEpD,KAAK,CAAC5I,KAAK,CAACmD,aAAa,CAAC,GAAG,CAAC,CAAC;IAC/D,MAAMgC,IAAI,GAAGE,IAAI,CAACzF,OAAO,CAACI,KAAK,CAACoD,aAAa,EAAE,EAAE,CAAC,GAAG,IAAI;IACzD,IAAI,CAACsP,UAAU,EAAE;MACb,OAAO,aAAa,IACb3J,OAAO,GAAG5D,IAAI,GAAGqC,MAAM,CAACrC,IAAI,EAAE,IAAI,CAAC,CAAC,GACrC,iBAAiB;IAC3B;IACA,OAAO,6BAA6B,GAC9BqC,MAAM,CAACkL,UAAU,CAAC,GAClB,IAAI,IACH3J,OAAO,GAAG5D,IAAI,GAAGqC,MAAM,CAACrC,IAAI,EAAE,IAAI,CAAC,CAAC,GACrC,iBAAiB;EAC3B;EACAF,UAAUA,CAAC;IAAEgG;EAAO,CAAC,EAAE;IACnB,MAAM0H,IAAI,GAAG,IAAI,CAACF,MAAM,CAACG,KAAK,CAAC3H,MAAM,CAAC;IACtC,OAAQ,iBAAgB0H,IAAK,iBAAgB;EACjD;EACA5N,IAAIA,CAAC;IAAEM;EAAK,CAAC,EAAE;IACX,OAAOA,IAAI;EACf;EACAlB,OAAOA,CAAC;IAAE8G,MAAM;IAAEiB;EAAM,CAAC,EAAE;IACvB,OAAQ,KAAIA,KAAM,IAAG,IAAI,CAACuG,MAAM,CAACI,WAAW,CAAC5H,MAAM,CAAE,MAAKiB,KAAM,KAAI;EACxE;EACAhI,EAAEA,CAAC6G,KAAK,EAAE;IACN,OAAO,QAAQ;EACnB;EACAnG,IAAIA,CAACmG,KAAK,EAAE;IACR,MAAMiC,OAAO,GAAGjC,KAAK,CAACiC,OAAO;IAC7B,MAAMC,KAAK,GAAGlC,KAAK,CAACkC,KAAK;IACzB,IAAI0F,IAAI,GAAG,EAAE;IACb,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/H,KAAK,CAACoC,KAAK,CAAC7D,MAAM,EAAEwJ,CAAC,EAAE,EAAE;MACzC,MAAM/D,IAAI,GAAGhE,KAAK,CAACoC,KAAK,CAAC2F,CAAC,CAAC;MAC3BH,IAAI,IAAI,IAAI,CAACI,QAAQ,CAAChE,IAAI,CAAC;IAC/B;IACA,MAAM/D,IAAI,GAAGgC,OAAO,GAAG,IAAI,GAAG,IAAI;IAClC,MAAMgG,SAAS,GAAIhG,OAAO,IAAIC,KAAK,KAAK,CAAC,GAAK,UAAU,GAAGA,KAAK,GAAG,GAAG,GAAI,EAAE;IAC5E,OAAO,GAAG,GAAGjC,IAAI,GAAGgI,SAAS,GAAG,KAAK,GAAGL,IAAI,GAAG,IAAI,GAAG3H,IAAI,GAAG,KAAK;EACtE;EACA+H,QAAQA,CAAChE,IAAI,EAAE;IACX,IAAIkE,QAAQ,GAAG,EAAE;IACjB,IAAIlE,IAAI,CAACb,IAAI,EAAE;MACX,MAAMgF,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC;QAAE/E,OAAO,EAAE,CAAC,CAACY,IAAI,CAACZ;MAAQ,CAAC,CAAC;MAC3D,IAAIY,IAAI,CAAC7B,KAAK,EAAE;QACZ,IAAI6B,IAAI,CAAC9D,MAAM,CAAC,CAAC,CAAC,EAAED,IAAI,KAAK,WAAW,EAAE;UACtC+D,IAAI,CAAC9D,MAAM,CAAC,CAAC,CAAC,CAAC5F,IAAI,GAAG6N,QAAQ,GAAG,GAAG,GAAGnE,IAAI,CAAC9D,MAAM,CAAC,CAAC,CAAC,CAAC5F,IAAI;UAC1D,IAAI0J,IAAI,CAAC9D,MAAM,CAAC,CAAC,CAAC,CAACA,MAAM,IAAI8D,IAAI,CAAC9D,MAAM,CAAC,CAAC,CAAC,CAACA,MAAM,CAAC3B,MAAM,GAAG,CAAC,IAAIyF,IAAI,CAAC9D,MAAM,CAAC,CAAC,CAAC,CAACA,MAAM,CAAC,CAAC,CAAC,CAACD,IAAI,KAAK,MAAM,EAAE;YACvG+D,IAAI,CAAC9D,MAAM,CAAC,CAAC,CAAC,CAACA,MAAM,CAAC,CAAC,CAAC,CAAC5F,IAAI,GAAG6N,QAAQ,GAAG,GAAG,GAAG1L,MAAM,CAACuH,IAAI,CAAC9D,MAAM,CAAC,CAAC,CAAC,CAACA,MAAM,CAAC,CAAC,CAAC,CAAC5F,IAAI,CAAC;YACtF0J,IAAI,CAAC9D,MAAM,CAAC,CAAC,CAAC,CAACA,MAAM,CAAC,CAAC,CAAC,CAAClC,OAAO,GAAG,IAAI;UAC3C;QACJ,CAAC,MACI;UACDgG,IAAI,CAAC9D,MAAM,CAACkI,OAAO,CAAC;YAChBnI,IAAI,EAAE,MAAM;YACZP,GAAG,EAAEyI,QAAQ,GAAG,GAAG;YACnB7N,IAAI,EAAE6N,QAAQ,GAAG,GAAG;YACpBnK,OAAO,EAAE;UACb,CAAC,CAAC;QACN;MACJ,CAAC,MACI;QACDkK,QAAQ,IAAIC,QAAQ,GAAG,GAAG;MAC9B;IACJ;IACAD,QAAQ,IAAI,IAAI,CAACR,MAAM,CAACG,KAAK,CAAC7D,IAAI,CAAC9D,MAAM,EAAE,CAAC,CAAC8D,IAAI,CAAC7B,KAAK,CAAC;IACxD,OAAQ,OAAM+F,QAAS,SAAQ;EACnC;EACAC,QAAQA,CAAC;IAAE/E;EAAQ,CAAC,EAAE;IAClB,OAAO,SAAS,IACTA,OAAO,GAAG,aAAa,GAAG,EAAE,CAAC,GAC9B,8BAA8B;EACxC;EACAnJ,SAASA,CAAC;IAAEiG;EAAO,CAAC,EAAE;IAClB,OAAQ,MAAK,IAAI,CAACwH,MAAM,CAACI,WAAW,CAAC5H,MAAM,CAAE,QAAO;EACxD;EACA7F,KAAKA,CAAC2F,KAAK,EAAE;IACT,IAAIiE,MAAM,GAAG,EAAE;IACf;IACA,IAAIE,IAAI,GAAG,EAAE;IACb,KAAK,IAAI4D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/H,KAAK,CAACiE,MAAM,CAAC1F,MAAM,EAAEwJ,CAAC,EAAE,EAAE;MAC1C5D,IAAI,IAAI,IAAI,CAACkE,SAAS,CAACrI,KAAK,CAACiE,MAAM,CAAC8D,CAAC,CAAC,CAAC;IAC3C;IACA9D,MAAM,IAAI,IAAI,CAACqE,QAAQ,CAAC;MAAEhO,IAAI,EAAE6J;IAAK,CAAC,CAAC;IACvC,IAAIyD,IAAI,GAAG,EAAE;IACb,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/H,KAAK,CAAC+D,IAAI,CAACxF,MAAM,EAAEwJ,CAAC,EAAE,EAAE;MACxC,MAAMnK,GAAG,GAAGoC,KAAK,CAAC+D,IAAI,CAACgE,CAAC,CAAC;MACzB5D,IAAI,GAAG,EAAE;MACT,KAAK,IAAIoE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3K,GAAG,CAACW,MAAM,EAAEgK,CAAC,EAAE,EAAE;QACjCpE,IAAI,IAAI,IAAI,CAACkE,SAAS,CAACzK,GAAG,CAAC2K,CAAC,CAAC,CAAC;MAClC;MACAX,IAAI,IAAI,IAAI,CAACU,QAAQ,CAAC;QAAEhO,IAAI,EAAE6J;MAAK,CAAC,CAAC;IACzC;IACA,IAAIyD,IAAI,EACJA,IAAI,GAAI,UAASA,IAAK,UAAS;IACnC,OAAO,WAAW,GACZ,WAAW,GACX3D,MAAM,GACN,YAAY,GACZ2D,IAAI,GACJ,YAAY;EACtB;EACAU,QAAQA,CAAC;IAAEhO;EAAK,CAAC,EAAE;IACf,OAAQ,SAAQA,IAAK,SAAQ;EACjC;EACA+N,SAASA,CAACrI,KAAK,EAAE;IACb,MAAMwI,OAAO,GAAG,IAAI,CAACd,MAAM,CAACI,WAAW,CAAC9H,KAAK,CAACE,MAAM,CAAC;IACrD,MAAMD,IAAI,GAAGD,KAAK,CAACiE,MAAM,GAAG,IAAI,GAAG,IAAI;IACvC,MAAMjI,GAAG,GAAGgE,KAAK,CAACkE,KAAK,GAChB,IAAGjE,IAAK,WAAUD,KAAK,CAACkE,KAAM,IAAG,GACjC,IAAGjE,IAAK,GAAE;IACjB,OAAOjE,GAAG,GAAGwM,OAAO,GAAI,KAAIvI,IAAK,KAAI;EACzC;EACA;AACJ;AACA;EACIwI,MAAMA,CAAC;IAAEvI;EAAO,CAAC,EAAE;IACf,OAAQ,WAAU,IAAI,CAACwH,MAAM,CAACI,WAAW,CAAC5H,MAAM,CAAE,WAAU;EAChE;EACAwI,EAAEA,CAAC;IAAExI;EAAO,CAAC,EAAE;IACX,OAAQ,OAAM,IAAI,CAACwH,MAAM,CAACI,WAAW,CAAC5H,MAAM,CAAE,OAAM;EACxD;EACAsF,QAAQA,CAAC;IAAElL;EAAK,CAAC,EAAE;IACf,OAAQ,SAAQmC,MAAM,CAACnC,IAAI,EAAE,IAAI,CAAE,SAAQ;EAC/C;EACAM,EAAEA,CAACoF,KAAK,EAAE;IACN,OAAO,MAAM;EACjB;EACAxD,GAAGA,CAAC;IAAE0D;EAAO,CAAC,EAAE;IACZ,OAAQ,QAAO,IAAI,CAACwH,MAAM,CAACI,WAAW,CAAC5H,MAAM,CAAE,QAAO;EAC1D;EACAhE,IAAIA,CAAC;IAAEqB,IAAI;IAAEsC,KAAK;IAAEK;EAAO,CAAC,EAAE;IAC1B,MAAM5F,IAAI,GAAG,IAAI,CAACoN,MAAM,CAACI,WAAW,CAAC5H,MAAM,CAAC;IAC5C,MAAMyI,SAAS,GAAGrL,QAAQ,CAACC,IAAI,CAAC;IAChC,IAAIoL,SAAS,KAAK,IAAI,EAAE;MACpB,OAAOrO,IAAI;IACf;IACAiD,IAAI,GAAGoL,SAAS;IAChB,IAAIC,GAAG,GAAG,WAAW,GAAGrL,IAAI,GAAG,GAAG;IAClC,IAAIsC,KAAK,EAAE;MACP+I,GAAG,IAAI,UAAU,GAAInM,MAAM,CAACoD,KAAK,CAAE,GAAG,GAAG;IAC7C;IACA+I,GAAG,IAAI,GAAG,GAAGtO,IAAI,GAAG,MAAM;IAC1B,OAAOsO,GAAG;EACd;EACAC,KAAKA,CAAC;IAAEtL,IAAI;IAAEsC,KAAK;IAAEvF;EAAK,CAAC,EAAE;IACzB,MAAMqO,SAAS,GAAGrL,QAAQ,CAACC,IAAI,CAAC;IAChC,IAAIoL,SAAS,KAAK,IAAI,EAAE;MACpB,OAAOlM,MAAM,CAACnC,IAAI,CAAC;IACvB;IACAiD,IAAI,GAAGoL,SAAS;IAChB,IAAIC,GAAG,GAAI,aAAYrL,IAAK,UAASjD,IAAK,GAAE;IAC5C,IAAIuF,KAAK,EAAE;MACP+I,GAAG,IAAK,WAAUnM,MAAM,CAACoD,KAAK,CAAE,GAAE;IACtC;IACA+I,GAAG,IAAI,GAAG;IACV,OAAOA,GAAG;EACd;EACAtO,IAAIA,CAAC0F,KAAK,EAAE;IACR,OAAO,QAAQ,IAAIA,KAAK,IAAIA,KAAK,CAACE,MAAM,GAClC,IAAI,CAACwH,MAAM,CAACI,WAAW,CAAC9H,KAAK,CAACE,MAAM,CAAC,GACpC,SAAS,IAAIF,KAAK,IAAIA,KAAK,CAAChC,OAAO,GAAGgC,KAAK,CAAC1F,IAAI,GAAGmC,MAAM,CAACuD,KAAK,CAAC1F,IAAI,CAAE;EACjF;AACJ;;AAEA;AACA;AACA;AACA;AACA,MAAMwO,aAAa,CAAC;EAChB;EACAL,MAAMA,CAAC;IAAEnO;EAAK,CAAC,EAAE;IACb,OAAOA,IAAI;EACf;EACAoO,EAAEA,CAAC;IAAEpO;EAAK,CAAC,EAAE;IACT,OAAOA,IAAI;EACf;EACAkL,QAAQA,CAAC;IAAElL;EAAK,CAAC,EAAE;IACf,OAAOA,IAAI;EACf;EACAkC,GAAGA,CAAC;IAAElC;EAAK,CAAC,EAAE;IACV,OAAOA,IAAI;EACf;EACAN,IAAIA,CAAC;IAAEM;EAAK,CAAC,EAAE;IACX,OAAOA,IAAI;EACf;EACAA,IAAIA,CAAC;IAAEA;EAAK,CAAC,EAAE;IACX,OAAOA,IAAI;EACf;EACA4B,IAAIA,CAAC;IAAE5B;EAAK,CAAC,EAAE;IACX,OAAO,EAAE,GAAGA,IAAI;EACpB;EACAuO,KAAKA,CAAC;IAAEvO;EAAK,CAAC,EAAE;IACZ,OAAO,EAAE,GAAGA,IAAI;EACpB;EACAM,EAAEA,CAAA,EAAG;IACD,OAAO,EAAE;EACb;AACJ;;AAEA;AACA;AACA;AACA,MAAMmO,OAAO,CAAC;EACVnI,OAAO;EACP7M,QAAQ;EACRiV,YAAY;EACZnI,WAAWA,CAACD,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO,IAAIzM,SAAS;IACnC,IAAI,CAACyM,OAAO,CAAC7M,QAAQ,GAAG,IAAI,CAAC6M,OAAO,CAAC7M,QAAQ,IAAI,IAAI0T,SAAS,CAAC,CAAC;IAChE,IAAI,CAAC1T,QAAQ,GAAG,IAAI,CAAC6M,OAAO,CAAC7M,QAAQ;IACrC,IAAI,CAACA,QAAQ,CAAC6M,OAAO,GAAG,IAAI,CAACA,OAAO;IACpC,IAAI,CAAC7M,QAAQ,CAAC2T,MAAM,GAAG,IAAI;IAC3B,IAAI,CAACsB,YAAY,GAAG,IAAIF,aAAa,CAAC,CAAC;EAC3C;EACA;AACJ;AACA;EACI,OAAOjB,KAAKA,CAAC3H,MAAM,EAAEU,OAAO,EAAE;IAC1B,MAAM8G,MAAM,GAAG,IAAIqB,OAAO,CAACnI,OAAO,CAAC;IACnC,OAAO8G,MAAM,CAACG,KAAK,CAAC3H,MAAM,CAAC;EAC/B;EACA;AACJ;AACA;EACI,OAAO4H,WAAWA,CAAC5H,MAAM,EAAEU,OAAO,EAAE;IAChC,MAAM8G,MAAM,GAAG,IAAIqB,OAAO,CAACnI,OAAO,CAAC;IACnC,OAAO8G,MAAM,CAACI,WAAW,CAAC5H,MAAM,CAAC;EACrC;EACA;AACJ;AACA;EACI2H,KAAKA,CAAC3H,MAAM,EAAEuB,GAAG,GAAG,IAAI,EAAE;IACtB,IAAImH,GAAG,GAAG,EAAE;IACZ,KAAK,IAAIxK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8B,MAAM,CAAC3B,MAAM,EAAEH,CAAC,EAAE,EAAE;MACpC,MAAM6K,QAAQ,GAAG/I,MAAM,CAAC9B,CAAC,CAAC;MAC1B;MACA,IAAI,IAAI,CAACwC,OAAO,CAACjN,UAAU,EAAEuV,SAAS,GAAGD,QAAQ,CAAChJ,IAAI,CAAC,EAAE;QACrD,MAAMkJ,YAAY,GAAGF,QAAQ;QAC7B,MAAMG,GAAG,GAAG,IAAI,CAACxI,OAAO,CAACjN,UAAU,CAACuV,SAAS,CAACC,YAAY,CAAClJ,IAAI,CAAC,CAACoG,IAAI,CAAC;UAAEqB,MAAM,EAAE;QAAK,CAAC,EAAEyB,YAAY,CAAC;QACrG,IAAIC,GAAG,KAAK,KAAK,IAAI,CAAC,CAAC,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC/B,QAAQ,CAAC8B,YAAY,CAAClJ,IAAI,CAAC,EAAE;UAC9I2I,GAAG,IAAIQ,GAAG,IAAI,EAAE;UAChB;QACJ;MACJ;MACA,MAAMpJ,KAAK,GAAGiJ,QAAQ;MACtB,QAAQjJ,KAAK,CAACC,IAAI;QACd,KAAK,OAAO;UAAE;YACV2I,GAAG,IAAI,IAAI,CAAC7U,QAAQ,CAAC+M,KAAK,CAACd,KAAK,CAAC;YACjC;UACJ;QACA,KAAK,IAAI;UAAE;YACP4I,GAAG,IAAI,IAAI,CAAC7U,QAAQ,CAACoF,EAAE,CAAC6G,KAAK,CAAC;YAC9B;UACJ;QACA,KAAK,SAAS;UAAE;YACZ4I,GAAG,IAAI,IAAI,CAAC7U,QAAQ,CAACqF,OAAO,CAAC4G,KAAK,CAAC;YACnC;UACJ;QACA,KAAK,MAAM;UAAE;YACT4I,GAAG,IAAI,IAAI,CAAC7U,QAAQ,CAACqG,IAAI,CAAC4F,KAAK,CAAC;YAChC;UACJ;QACA,KAAK,OAAO;UAAE;YACV4I,GAAG,IAAI,IAAI,CAAC7U,QAAQ,CAACsG,KAAK,CAAC2F,KAAK,CAAC;YACjC;UACJ;QACA,KAAK,YAAY;UAAE;YACf4I,GAAG,IAAI,IAAI,CAAC7U,QAAQ,CAACmG,UAAU,CAAC8F,KAAK,CAAC;YACtC;UACJ;QACA,KAAK,MAAM;UAAE;YACT4I,GAAG,IAAI,IAAI,CAAC7U,QAAQ,CAAC8F,IAAI,CAACmG,KAAK,CAAC;YAChC;UACJ;QACA,KAAK,MAAM;UAAE;YACT4I,GAAG,IAAI,IAAI,CAAC7U,QAAQ,CAACiG,IAAI,CAACgG,KAAK,CAAC;YAChC;UACJ;QACA,KAAK,WAAW;UAAE;YACd4I,GAAG,IAAI,IAAI,CAAC7U,QAAQ,CAACkG,SAAS,CAAC+F,KAAK,CAAC;YACrC;UACJ;QACA,KAAK,MAAM;UAAE;YACT,IAAIqJ,SAAS,GAAGrJ,KAAK;YACrB,IAAI4H,IAAI,GAAG,IAAI,CAAC7T,QAAQ,CAACuG,IAAI,CAAC+O,SAAS,CAAC;YACxC,OAAOjL,CAAC,GAAG,CAAC,GAAG8B,MAAM,CAAC3B,MAAM,IAAI2B,MAAM,CAAC9B,CAAC,GAAG,CAAC,CAAC,CAAC6B,IAAI,KAAK,MAAM,EAAE;cAC3DoJ,SAAS,GAAGnJ,MAAM,CAAC,EAAE9B,CAAC,CAAC;cACvBwJ,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC7T,QAAQ,CAACuG,IAAI,CAAC+O,SAAS,CAAC;YAChD;YACA,IAAI5H,GAAG,EAAE;cACLmH,GAAG,IAAI,IAAI,CAAC7U,QAAQ,CAACkG,SAAS,CAAC;gBAC3BgG,IAAI,EAAE,WAAW;gBACjBP,GAAG,EAAEkI,IAAI;gBACTtN,IAAI,EAAEsN,IAAI;gBACV1H,MAAM,EAAE,CAAC;kBAAED,IAAI,EAAE,MAAM;kBAAEP,GAAG,EAAEkI,IAAI;kBAAEtN,IAAI,EAAEsN,IAAI;kBAAE5J,OAAO,EAAE;gBAAK,CAAC;cACnE,CAAC,CAAC;YACN,CAAC,MACI;cACD4K,GAAG,IAAIhB,IAAI;YACf;YACA;UACJ;QACA;UAAS;YACL,MAAMb,MAAM,GAAG,cAAc,GAAG/G,KAAK,CAACC,IAAI,GAAG,uBAAuB;YACpE,IAAI,IAAI,CAACW,OAAO,CAAC5M,MAAM,EAAE;cACrBiT,OAAO,CAACC,KAAK,CAACH,MAAM,CAAC;cACrB,OAAO,EAAE;YACb,CAAC,MACI;cACD,MAAM,IAAII,KAAK,CAACJ,MAAM,CAAC;YAC3B;UACJ;MACJ;IACJ;IACA,OAAO6B,GAAG;EACd;EACA;AACJ;AACA;EACId,WAAWA,CAAC5H,MAAM,EAAEnM,QAAQ,GAAG,IAAI,CAACA,QAAQ,EAAE;IAC1C,IAAI6U,GAAG,GAAG,EAAE;IACZ,KAAK,IAAIxK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8B,MAAM,CAAC3B,MAAM,EAAEH,CAAC,EAAE,EAAE;MACpC,MAAM6K,QAAQ,GAAG/I,MAAM,CAAC9B,CAAC,CAAC;MAC1B;MACA,IAAI,IAAI,CAACwC,OAAO,CAACjN,UAAU,EAAEuV,SAAS,GAAGD,QAAQ,CAAChJ,IAAI,CAAC,EAAE;QACrD,MAAMmJ,GAAG,GAAG,IAAI,CAACxI,OAAO,CAACjN,UAAU,CAACuV,SAAS,CAACD,QAAQ,CAAChJ,IAAI,CAAC,CAACoG,IAAI,CAAC;UAAEqB,MAAM,EAAE;QAAK,CAAC,EAAEuB,QAAQ,CAAC;QAC7F,IAAIG,GAAG,KAAK,KAAK,IAAI,CAAC,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC/B,QAAQ,CAAC4B,QAAQ,CAAChJ,IAAI,CAAC,EAAE;UAChI2I,GAAG,IAAIQ,GAAG,IAAI,EAAE;UAChB;QACJ;MACJ;MACA,MAAMpJ,KAAK,GAAGiJ,QAAQ;MACtB,QAAQjJ,KAAK,CAACC,IAAI;QACd,KAAK,QAAQ;UAAE;YACX2I,GAAG,IAAI7U,QAAQ,CAACuG,IAAI,CAAC0F,KAAK,CAAC;YAC3B;UACJ;QACA,KAAK,MAAM;UAAE;YACT4I,GAAG,IAAI7U,QAAQ,CAACiG,IAAI,CAACgG,KAAK,CAAC;YAC3B;UACJ;QACA,KAAK,MAAM;UAAE;YACT4I,GAAG,IAAI7U,QAAQ,CAACmI,IAAI,CAAC8D,KAAK,CAAC;YAC3B;UACJ;QACA,KAAK,OAAO;UAAE;YACV4I,GAAG,IAAI7U,QAAQ,CAAC8U,KAAK,CAAC7I,KAAK,CAAC;YAC5B;UACJ;QACA,KAAK,QAAQ;UAAE;YACX4I,GAAG,IAAI7U,QAAQ,CAAC0U,MAAM,CAACzI,KAAK,CAAC;YAC7B;UACJ;QACA,KAAK,IAAI;UAAE;YACP4I,GAAG,IAAI7U,QAAQ,CAAC2U,EAAE,CAAC1I,KAAK,CAAC;YACzB;UACJ;QACA,KAAK,UAAU;UAAE;YACb4I,GAAG,IAAI7U,QAAQ,CAACyR,QAAQ,CAACxF,KAAK,CAAC;YAC/B;UACJ;QACA,KAAK,IAAI;UAAE;YACP4I,GAAG,IAAI7U,QAAQ,CAAC6G,EAAE,CAACoF,KAAK,CAAC;YACzB;UACJ;QACA,KAAK,KAAK;UAAE;YACR4I,GAAG,IAAI7U,QAAQ,CAACyI,GAAG,CAACwD,KAAK,CAAC;YAC1B;UACJ;QACA,KAAK,MAAM;UAAE;YACT4I,GAAG,IAAI7U,QAAQ,CAACuG,IAAI,CAAC0F,KAAK,CAAC;YAC3B;UACJ;QACA;UAAS;YACL,MAAM+G,MAAM,GAAG,cAAc,GAAG/G,KAAK,CAACC,IAAI,GAAG,uBAAuB;YACpE,IAAI,IAAI,CAACW,OAAO,CAAC5M,MAAM,EAAE;cACrBiT,OAAO,CAACC,KAAK,CAACH,MAAM,CAAC;cACrB,OAAO,EAAE;YACb,CAAC,MACI;cACD,MAAM,IAAII,KAAK,CAACJ,MAAM,CAAC;YAC3B;UACJ;MACJ;IACJ;IACA,OAAO6B,GAAG;EACd;AACJ;AAEA,MAAMU,MAAM,CAAC;EACT1I,OAAO;EACP9D,KAAK;EACL+D,WAAWA,CAACD,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO,IAAIzM,SAAS;EACvC;EACA,OAAOoV,gBAAgB,GAAG,IAAIC,GAAG,CAAC,CAC9B,YAAY,EACZ,aAAa,EACb,kBAAkB,CACrB,CAAC;EACF;AACJ;AACA;EACIC,UAAUA,CAACC,QAAQ,EAAE;IACjB,OAAOA,QAAQ;EACnB;EACA;AACJ;AACA;EACIC,WAAWA,CAAC3P,IAAI,EAAE;IACd,OAAOA,IAAI;EACf;EACA;AACJ;AACA;EACI4P,gBAAgBA,CAAC1J,MAAM,EAAE;IACrB,OAAOA,MAAM;EACjB;EACA;AACJ;AACA;EACI2J,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC/M,KAAK,GAAG8I,MAAM,CAACI,GAAG,GAAGJ,MAAM,CAACK,SAAS;EACrD;EACA;AACJ;AACA;EACI6D,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAChN,KAAK,GAAGiM,OAAO,CAAClB,KAAK,GAAGkB,OAAO,CAACjB,WAAW;EAC3D;AACJ;AAEA,MAAMiC,MAAM,CAAC;EACTC,QAAQ,GAAGxW,YAAY,CAAC,CAAC;EACzBoN,OAAO,GAAG,IAAI,CAACqJ,UAAU;EACzBpC,KAAK,GAAG,IAAI,CAACqC,aAAa,CAAC,IAAI,CAAC;EAChCpC,WAAW,GAAG,IAAI,CAACoC,aAAa,CAAC,KAAK,CAAC;EACvCC,MAAM,GAAGpB,OAAO;EAChBqB,QAAQ,GAAG3C,SAAS;EACpB4C,YAAY,GAAGvB,aAAa;EAC5BwB,KAAK,GAAG1E,MAAM;EACd2E,SAAS,GAAG5J,UAAU;EACtB6J,KAAK,GAAGlB,MAAM;EACdzI,WAAWA,CAAC,GAAG4J,IAAI,EAAE;IACjB,IAAI,CAACC,GAAG,CAAC,GAAGD,IAAI,CAAC;EACrB;EACA;AACJ;AACA;EACIvW,UAAUA,CAACgM,MAAM,EAAEyK,QAAQ,EAAE;IACzB,IAAIC,MAAM,GAAG,EAAE;IACf,KAAK,MAAM5K,KAAK,IAAIE,MAAM,EAAE;MACxB0K,MAAM,GAAGA,MAAM,CAACC,MAAM,CAACF,QAAQ,CAACtE,IAAI,CAAC,IAAI,EAAErG,KAAK,CAAC,CAAC;MAClD,QAAQA,KAAK,CAACC,IAAI;QACd,KAAK,OAAO;UAAE;YACV,MAAM6K,UAAU,GAAG9K,KAAK;YACxB,KAAK,MAAMmE,IAAI,IAAI2G,UAAU,CAAC7G,MAAM,EAAE;cAClC2G,MAAM,GAAGA,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC3W,UAAU,CAACiQ,IAAI,CAACjE,MAAM,EAAEyK,QAAQ,CAAC,CAAC;YAClE;YACA,KAAK,MAAM/M,GAAG,IAAIkN,UAAU,CAAC/G,IAAI,EAAE;cAC/B,KAAK,MAAMI,IAAI,IAAIvG,GAAG,EAAE;gBACpBgN,MAAM,GAAGA,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC3W,UAAU,CAACiQ,IAAI,CAACjE,MAAM,EAAEyK,QAAQ,CAAC,CAAC;cAClE;YACJ;YACA;UACJ;QACA,KAAK,MAAM;UAAE;YACT,MAAMI,SAAS,GAAG/K,KAAK;YACvB4K,MAAM,GAAGA,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC3W,UAAU,CAAC6W,SAAS,CAAC3I,KAAK,EAAEuI,QAAQ,CAAC,CAAC;YAClE;UACJ;QACA;UAAS;YACL,MAAMxB,YAAY,GAAGnJ,KAAK;YAC1B,IAAI,IAAI,CAACgK,QAAQ,CAACrW,UAAU,EAAEqX,WAAW,GAAG7B,YAAY,CAAClJ,IAAI,CAAC,EAAE;cAC5D,IAAI,CAAC+J,QAAQ,CAACrW,UAAU,CAACqX,WAAW,CAAC7B,YAAY,CAAClJ,IAAI,CAAC,CAAC4G,OAAO,CAAEmE,WAAW,IAAK;gBAC7E,MAAM9K,MAAM,GAAGiJ,YAAY,CAAC6B,WAAW,CAAC,CAACC,IAAI,CAACvE,QAAQ,CAAC;gBACvDkE,MAAM,GAAGA,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC3W,UAAU,CAACgM,MAAM,EAAEyK,QAAQ,CAAC,CAAC;cAC7D,CAAC,CAAC;YACN,CAAC,MACI,IAAIxB,YAAY,CAACjJ,MAAM,EAAE;cAC1B0K,MAAM,GAAGA,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC3W,UAAU,CAACiV,YAAY,CAACjJ,MAAM,EAAEyK,QAAQ,CAAC,CAAC;YAC1E;UACJ;MACJ;IACJ;IACA,OAAOC,MAAM;EACjB;EACAF,GAAGA,CAAC,GAAGD,IAAI,EAAE;IACT,MAAM9W,UAAU,GAAG,IAAI,CAACqW,QAAQ,CAACrW,UAAU,IAAI;MAAEuV,SAAS,EAAE,CAAC,CAAC;MAAE8B,WAAW,EAAE,CAAC;IAAE,CAAC;IACjFP,IAAI,CAAC5D,OAAO,CAAEqE,IAAI,IAAK;MACnB;MACA,MAAMC,IAAI,GAAG;QAAE,GAAGD;MAAK,CAAC;MACxB;MACAC,IAAI,CAAC1X,KAAK,GAAG,IAAI,CAACuW,QAAQ,CAACvW,KAAK,IAAI0X,IAAI,CAAC1X,KAAK,IAAI,KAAK;MACvD;MACA,IAAIyX,IAAI,CAACvX,UAAU,EAAE;QACjBuX,IAAI,CAACvX,UAAU,CAACkT,OAAO,CAAEuE,GAAG,IAAK;UAC7B,IAAI,CAACA,GAAG,CAACtW,IAAI,EAAE;YACX,MAAM,IAAIqS,KAAK,CAAC,yBAAyB,CAAC;UAC9C;UACA,IAAI,UAAU,IAAIiE,GAAG,EAAE;YAAE;YACrB,MAAMC,YAAY,GAAG1X,UAAU,CAACuV,SAAS,CAACkC,GAAG,CAACtW,IAAI,CAAC;YACnD,IAAIuW,YAAY,EAAE;cACd;cACA1X,UAAU,CAACuV,SAAS,CAACkC,GAAG,CAACtW,IAAI,CAAC,GAAG,UAAU,GAAG2V,IAAI,EAAE;gBAChD,IAAIrB,GAAG,GAAGgC,GAAG,CAACrX,QAAQ,CAACuX,KAAK,CAAC,IAAI,EAAEb,IAAI,CAAC;gBACxC,IAAIrB,GAAG,KAAK,KAAK,EAAE;kBACfA,GAAG,GAAGiC,YAAY,CAACC,KAAK,CAAC,IAAI,EAAEb,IAAI,CAAC;gBACxC;gBACA,OAAOrB,GAAG;cACd,CAAC;YACL,CAAC,MACI;cACDzV,UAAU,CAACuV,SAAS,CAACkC,GAAG,CAACtW,IAAI,CAAC,GAAGsW,GAAG,CAACrX,QAAQ;YACjD;UACJ;UACA,IAAI,WAAW,IAAIqX,GAAG,EAAE;YAAE;YACtB,IAAI,CAACA,GAAG,CAAC7L,KAAK,IAAK6L,GAAG,CAAC7L,KAAK,KAAK,OAAO,IAAI6L,GAAG,CAAC7L,KAAK,KAAK,QAAS,EAAE;cACjE,MAAM,IAAI4H,KAAK,CAAC,6CAA6C,CAAC;YAClE;YACA,MAAMoE,QAAQ,GAAG5X,UAAU,CAACyX,GAAG,CAAC7L,KAAK,CAAC;YACtC,IAAIgM,QAAQ,EAAE;cACVA,QAAQ,CAACnD,OAAO,CAACgD,GAAG,CAACnX,SAAS,CAAC;YACnC,CAAC,MACI;cACDN,UAAU,CAACyX,GAAG,CAAC7L,KAAK,CAAC,GAAG,CAAC6L,GAAG,CAACnX,SAAS,CAAC;YAC3C;YACA,IAAImX,GAAG,CAAClJ,KAAK,EAAE;cAAE;cACb,IAAIkJ,GAAG,CAAC7L,KAAK,KAAK,OAAO,EAAE;gBACvB,IAAI5L,UAAU,CAAC6S,UAAU,EAAE;kBACvB7S,UAAU,CAAC6S,UAAU,CAAC7H,IAAI,CAACyM,GAAG,CAAClJ,KAAK,CAAC;gBACzC,CAAC,MACI;kBACDvO,UAAU,CAAC6S,UAAU,GAAG,CAAC4E,GAAG,CAAClJ,KAAK,CAAC;gBACvC;cACJ,CAAC,MACI,IAAIkJ,GAAG,CAAC7L,KAAK,KAAK,QAAQ,EAAE;gBAC7B,IAAI5L,UAAU,CAAC6T,WAAW,EAAE;kBACxB7T,UAAU,CAAC6T,WAAW,CAAC7I,IAAI,CAACyM,GAAG,CAAClJ,KAAK,CAAC;gBAC1C,CAAC,MACI;kBACDvO,UAAU,CAAC6T,WAAW,GAAG,CAAC4D,GAAG,CAAClJ,KAAK,CAAC;gBACxC;cACJ;YACJ;UACJ;UACA,IAAI,aAAa,IAAIkJ,GAAG,IAAIA,GAAG,CAACJ,WAAW,EAAE;YAAE;YAC3CrX,UAAU,CAACqX,WAAW,CAACI,GAAG,CAACtW,IAAI,CAAC,GAAGsW,GAAG,CAACJ,WAAW;UACtD;QACJ,CAAC,CAAC;QACFG,IAAI,CAACxX,UAAU,GAAGA,UAAU;MAChC;MACA;MACA,IAAIuX,IAAI,CAACnX,QAAQ,EAAE;QACf,MAAMA,QAAQ,GAAG,IAAI,CAACiW,QAAQ,CAACjW,QAAQ,IAAI,IAAI0T,SAAS,CAAC,IAAI,CAACuC,QAAQ,CAAC;QACvE,KAAK,MAAMwB,IAAI,IAAIN,IAAI,CAACnX,QAAQ,EAAE;UAC9B,IAAI,EAAEyX,IAAI,IAAIzX,QAAQ,CAAC,EAAE;YACrB,MAAM,IAAIoT,KAAK,CAAE,aAAYqE,IAAK,kBAAiB,CAAC;UACxD;UACA,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAACnE,QAAQ,CAACmE,IAAI,CAAC,EAAE;YACtC;YACA;UACJ;UACA,MAAMC,YAAY,GAAGD,IAAI;UACzB,MAAME,YAAY,GAAGR,IAAI,CAACnX,QAAQ,CAAC0X,YAAY,CAAC;UAChD,MAAMJ,YAAY,GAAGtX,QAAQ,CAAC0X,YAAY,CAAC;UAC3C;UACA1X,QAAQ,CAAC0X,YAAY,CAAC,GAAG,CAAC,GAAGhB,IAAI,KAAK;YAClC,IAAIrB,GAAG,GAAGsC,YAAY,CAACJ,KAAK,CAACvX,QAAQ,EAAE0W,IAAI,CAAC;YAC5C,IAAIrB,GAAG,KAAK,KAAK,EAAE;cACfA,GAAG,GAAGiC,YAAY,CAACC,KAAK,CAACvX,QAAQ,EAAE0W,IAAI,CAAC;YAC5C;YACA,OAAOrB,GAAG,IAAI,EAAE;UACpB,CAAC;QACL;QACA+B,IAAI,CAACpX,QAAQ,GAAGA,QAAQ;MAC5B;MACA,IAAImX,IAAI,CAACjX,SAAS,EAAE;QAChB,MAAMA,SAAS,GAAG,IAAI,CAAC+V,QAAQ,CAAC/V,SAAS,IAAI,IAAI0M,UAAU,CAAC,IAAI,CAACqJ,QAAQ,CAAC;QAC1E,KAAK,MAAMwB,IAAI,IAAIN,IAAI,CAACjX,SAAS,EAAE;UAC/B,IAAI,EAAEuX,IAAI,IAAIvX,SAAS,CAAC,EAAE;YACtB,MAAM,IAAIkT,KAAK,CAAE,cAAaqE,IAAK,kBAAiB,CAAC;UACzD;UACA,IAAI,CAAC,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC,CAACnE,QAAQ,CAACmE,IAAI,CAAC,EAAE;YAC9C;YACA;UACJ;UACA,MAAMG,aAAa,GAAGH,IAAI;UAC1B,MAAMI,aAAa,GAAGV,IAAI,CAACjX,SAAS,CAAC0X,aAAa,CAAC;UACnD,MAAME,aAAa,GAAG5X,SAAS,CAAC0X,aAAa,CAAC;UAC9C;UACA;UACA1X,SAAS,CAAC0X,aAAa,CAAC,GAAG,CAAC,GAAGlB,IAAI,KAAK;YACpC,IAAIrB,GAAG,GAAGwC,aAAa,CAACN,KAAK,CAACrX,SAAS,EAAEwW,IAAI,CAAC;YAC9C,IAAIrB,GAAG,KAAK,KAAK,EAAE;cACfA,GAAG,GAAGyC,aAAa,CAACP,KAAK,CAACrX,SAAS,EAAEwW,IAAI,CAAC;YAC9C;YACA,OAAOrB,GAAG;UACd,CAAC;QACL;QACA+B,IAAI,CAAClX,SAAS,GAAGA,SAAS;MAC9B;MACA;MACA,IAAIiX,IAAI,CAACrX,KAAK,EAAE;QACZ,MAAMA,KAAK,GAAG,IAAI,CAACmW,QAAQ,CAACnW,KAAK,IAAI,IAAIyV,MAAM,CAAC,CAAC;QACjD,KAAK,MAAMkC,IAAI,IAAIN,IAAI,CAACrX,KAAK,EAAE;UAC3B,IAAI,EAAE2X,IAAI,IAAI3X,KAAK,CAAC,EAAE;YAClB,MAAM,IAAIsT,KAAK,CAAE,SAAQqE,IAAK,kBAAiB,CAAC;UACpD;UACA,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAACnE,QAAQ,CAACmE,IAAI,CAAC,EAAE;YACrC;YACA;UACJ;UACA,MAAMM,SAAS,GAAGN,IAAI;UACtB,MAAMO,SAAS,GAAGb,IAAI,CAACrX,KAAK,CAACiY,SAAS,CAAC;UACvC,MAAME,QAAQ,GAAGnY,KAAK,CAACiY,SAAS,CAAC;UACjC,IAAIxC,MAAM,CAACC,gBAAgB,CAAC0C,GAAG,CAACT,IAAI,CAAC,EAAE;YACnC;YACA3X,KAAK,CAACiY,SAAS,CAAC,GAAII,GAAG,IAAK;cACxB,IAAI,IAAI,CAAClC,QAAQ,CAACvW,KAAK,EAAE;gBACrB,OAAO0Y,OAAO,CAACC,OAAO,CAACL,SAAS,CAAC1F,IAAI,CAACxS,KAAK,EAAEqY,GAAG,CAAC,CAAC,CAACG,IAAI,CAACjD,GAAG,IAAI;kBAC3D,OAAO4C,QAAQ,CAAC3F,IAAI,CAACxS,KAAK,EAAEuV,GAAG,CAAC;gBACpC,CAAC,CAAC;cACN;cACA,MAAMA,GAAG,GAAG2C,SAAS,CAAC1F,IAAI,CAACxS,KAAK,EAAEqY,GAAG,CAAC;cACtC,OAAOF,QAAQ,CAAC3F,IAAI,CAACxS,KAAK,EAAEuV,GAAG,CAAC;YACpC,CAAC;UACL,CAAC,MACI;YACD;YACAvV,KAAK,CAACiY,SAAS,CAAC,GAAG,CAAC,GAAGrB,IAAI,KAAK;cAC5B,IAAIrB,GAAG,GAAG2C,SAAS,CAACT,KAAK,CAACzX,KAAK,EAAE4W,IAAI,CAAC;cACtC,IAAIrB,GAAG,KAAK,KAAK,EAAE;gBACfA,GAAG,GAAG4C,QAAQ,CAACV,KAAK,CAACzX,KAAK,EAAE4W,IAAI,CAAC;cACrC;cACA,OAAOrB,GAAG;YACd,CAAC;UACL;QACJ;QACA+B,IAAI,CAACtX,KAAK,GAAGA,KAAK;MACtB;MACA;MACA,IAAIqX,IAAI,CAAChX,UAAU,EAAE;QACjB,MAAMA,UAAU,GAAG,IAAI,CAAC8V,QAAQ,CAAC9V,UAAU;QAC3C,MAAMoY,cAAc,GAAGpB,IAAI,CAAChX,UAAU;QACtCiX,IAAI,CAACjX,UAAU,GAAG,UAAU8L,KAAK,EAAE;UAC/B,IAAI4K,MAAM,GAAG,EAAE;UACfA,MAAM,CAACjM,IAAI,CAAC2N,cAAc,CAACjG,IAAI,CAAC,IAAI,EAAErG,KAAK,CAAC,CAAC;UAC7C,IAAI9L,UAAU,EAAE;YACZ0W,MAAM,GAAGA,MAAM,CAACC,MAAM,CAAC3W,UAAU,CAACmS,IAAI,CAAC,IAAI,EAAErG,KAAK,CAAC,CAAC;UACxD;UACA,OAAO4K,MAAM;QACjB,CAAC;MACL;MACA,IAAI,CAACZ,QAAQ,GAAG;QAAE,GAAG,IAAI,CAACA,QAAQ;QAAE,GAAGmB;MAAK,CAAC;IACjD,CAAC,CAAC;IACF,OAAO,IAAI;EACf;EACAlB,UAAUA,CAACvV,GAAG,EAAE;IACZ,IAAI,CAACsV,QAAQ,GAAG;MAAE,GAAG,IAAI,CAACA,QAAQ;MAAE,GAAGtV;IAAI,CAAC;IAC5C,OAAO,IAAI;EACf;EACAiL,KAAKA,CAACoB,GAAG,EAAEH,OAAO,EAAE;IAChB,OAAOgF,MAAM,CAACI,GAAG,CAACjF,GAAG,EAAEH,OAAO,IAAI,IAAI,CAACoJ,QAAQ,CAAC;EACpD;EACAtC,MAAMA,CAACxH,MAAM,EAAEU,OAAO,EAAE;IACpB,OAAOmI,OAAO,CAAClB,KAAK,CAAC3H,MAAM,EAAEU,OAAO,IAAI,IAAI,CAACoJ,QAAQ,CAAC;EAC1D;EACAE,aAAaA,CAACqC,SAAS,EAAE;IACrB;IACA,MAAM1E,KAAK,GAAGA,CAAC9G,GAAG,EAAEH,OAAO,KAAK;MAC5B,MAAM4L,OAAO,GAAG;QAAE,GAAG5L;MAAQ,CAAC;MAC9B,MAAMlM,GAAG,GAAG;QAAE,GAAG,IAAI,CAACsV,QAAQ;QAAE,GAAGwC;MAAQ,CAAC;MAC5C,MAAMC,UAAU,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC,CAAChY,GAAG,CAACV,MAAM,EAAE,CAAC,CAACU,GAAG,CAACjB,KAAK,CAAC;MAC1D;MACA,IAAI,IAAI,CAACuW,QAAQ,CAACvW,KAAK,KAAK,IAAI,IAAI+Y,OAAO,CAAC/Y,KAAK,KAAK,KAAK,EAAE;QACzD,OAAOgZ,UAAU,CAAC,IAAItF,KAAK,CAAC,oIAAoI,CAAC,CAAC;MACtK;MACA;MACA,IAAI,OAAOpG,GAAG,KAAK,WAAW,IAAIA,GAAG,KAAK,IAAI,EAAE;QAC5C,OAAO0L,UAAU,CAAC,IAAItF,KAAK,CAAC,gDAAgD,CAAC,CAAC;MAClF;MACA,IAAI,OAAOpG,GAAG,KAAK,QAAQ,EAAE;QACzB,OAAO0L,UAAU,CAAC,IAAItF,KAAK,CAAC,uCAAuC,GAC7DrB,MAAM,CAAC6G,SAAS,CAACC,QAAQ,CAACvG,IAAI,CAACtF,GAAG,CAAC,GAAG,mBAAmB,CAAC,CAAC;MACrE;MACA,IAAIrM,GAAG,CAACb,KAAK,EAAE;QACXa,GAAG,CAACb,KAAK,CAAC+M,OAAO,GAAGlM,GAAG;QACvBA,GAAG,CAACb,KAAK,CAACiJ,KAAK,GAAGyP,SAAS;MAC/B;MACA,MAAM5M,KAAK,GAAGjL,GAAG,CAACb,KAAK,GAAGa,GAAG,CAACb,KAAK,CAACgW,YAAY,CAAC,CAAC,GAAI0C,SAAS,GAAG3G,MAAM,CAACI,GAAG,GAAGJ,MAAM,CAACK,SAAU;MAChG,MAAMyB,MAAM,GAAGhT,GAAG,CAACb,KAAK,GAAGa,GAAG,CAACb,KAAK,CAACiW,aAAa,CAAC,CAAC,GAAIyC,SAAS,GAAGxD,OAAO,CAAClB,KAAK,GAAGkB,OAAO,CAACjB,WAAY;MACxG,IAAIpT,GAAG,CAACjB,KAAK,EAAE;QACX,OAAO0Y,OAAO,CAACC,OAAO,CAAC1X,GAAG,CAACb,KAAK,GAAGa,GAAG,CAACb,KAAK,CAAC4V,UAAU,CAAC1I,GAAG,CAAC,GAAGA,GAAG,CAAC,CAC9DsL,IAAI,CAACtL,GAAG,IAAIpB,KAAK,CAACoB,GAAG,EAAErM,GAAG,CAAC,CAAC,CAC5B2X,IAAI,CAACnM,MAAM,IAAIxL,GAAG,CAACb,KAAK,GAAGa,GAAG,CAACb,KAAK,CAAC+V,gBAAgB,CAAC1J,MAAM,CAAC,GAAGA,MAAM,CAAC,CACvEmM,IAAI,CAACnM,MAAM,IAAIxL,GAAG,CAACR,UAAU,GAAGiY,OAAO,CAACU,GAAG,CAAC,IAAI,CAAC3Y,UAAU,CAACgM,MAAM,EAAExL,GAAG,CAACR,UAAU,CAAC,CAAC,CAACmY,IAAI,CAAC,MAAMnM,MAAM,CAAC,GAAGA,MAAM,CAAC,CACjHmM,IAAI,CAACnM,MAAM,IAAIwH,MAAM,CAACxH,MAAM,EAAExL,GAAG,CAAC,CAAC,CACnC2X,IAAI,CAACrS,IAAI,IAAItF,GAAG,CAACb,KAAK,GAAGa,GAAG,CAACb,KAAK,CAAC8V,WAAW,CAAC3P,IAAI,CAAC,GAAGA,IAAI,CAAC,CAC5D8S,KAAK,CAACL,UAAU,CAAC;MAC1B;MACA,IAAI;QACA,IAAI/X,GAAG,CAACb,KAAK,EAAE;UACXkN,GAAG,GAAGrM,GAAG,CAACb,KAAK,CAAC4V,UAAU,CAAC1I,GAAG,CAAC;QACnC;QACA,IAAIb,MAAM,GAAGP,KAAK,CAACoB,GAAG,EAAErM,GAAG,CAAC;QAC5B,IAAIA,GAAG,CAACb,KAAK,EAAE;UACXqM,MAAM,GAAGxL,GAAG,CAACb,KAAK,CAAC+V,gBAAgB,CAAC1J,MAAM,CAAC;QAC/C;QACA,IAAIxL,GAAG,CAACR,UAAU,EAAE;UAChB,IAAI,CAACA,UAAU,CAACgM,MAAM,EAAExL,GAAG,CAACR,UAAU,CAAC;QAC3C;QACA,IAAI8F,IAAI,GAAG0N,MAAM,CAACxH,MAAM,EAAExL,GAAG,CAAC;QAC9B,IAAIA,GAAG,CAACb,KAAK,EAAE;UACXmG,IAAI,GAAGtF,GAAG,CAACb,KAAK,CAAC8V,WAAW,CAAC3P,IAAI,CAAC;QACtC;QACA,OAAOA,IAAI;MACf,CAAC,CACD,OAAO+S,CAAC,EAAE;QACN,OAAON,UAAU,CAACM,CAAC,CAAC;MACxB;IACJ,CAAC;IACD,OAAOlF,KAAK;EAChB;EACA6E,OAAOA,CAAC1Y,MAAM,EAAEP,KAAK,EAAE;IACnB,OAAQsZ,CAAC,IAAK;MACVA,CAAC,CAACC,OAAO,IAAI,6DAA6D;MAC1E,IAAIhZ,MAAM,EAAE;QACR,MAAMiZ,GAAG,GAAG,gCAAgC,GACtCxQ,MAAM,CAACsQ,CAAC,CAACC,OAAO,GAAG,EAAE,EAAE,IAAI,CAAC,GAC5B,QAAQ;QACd,IAAIvZ,KAAK,EAAE;UACP,OAAO0Y,OAAO,CAACC,OAAO,CAACa,GAAG,CAAC;QAC/B;QACA,OAAOA,GAAG;MACd;MACA,IAAIxZ,KAAK,EAAE;QACP,OAAO0Y,OAAO,CAACe,MAAM,CAACH,CAAC,CAAC;MAC5B;MACA,MAAMA,CAAC;IACX,CAAC;EACL;AACJ;AAEA,MAAMI,cAAc,GAAG,IAAIpD,MAAM,CAAC,CAAC;AACnC,SAASqD,MAAMA,CAACrM,GAAG,EAAErM,GAAG,EAAE;EACtB,OAAOyY,cAAc,CAACtF,KAAK,CAAC9G,GAAG,EAAErM,GAAG,CAAC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA0Y,MAAM,CAACxM,OAAO,GACVwM,MAAM,CAACnD,UAAU,GAAG,UAAUrJ,OAAO,EAAE;EACnCuM,cAAc,CAAClD,UAAU,CAACrJ,OAAO,CAAC;EAClCwM,MAAM,CAACpD,QAAQ,GAAGmD,cAAc,CAACnD,QAAQ;EACzC5V,cAAc,CAACgZ,MAAM,CAACpD,QAAQ,CAAC;EAC/B,OAAOoD,MAAM;AACjB,CAAC;AACL;AACA;AACA;AACAA,MAAM,CAACC,WAAW,GAAG7Z,YAAY;AACjC4Z,MAAM,CAACpD,QAAQ,GAAG7V,SAAS;AAC3B;AACA;AACA;AACAiZ,MAAM,CAAC1C,GAAG,GAAG,UAAU,GAAGD,IAAI,EAAE;EAC5B0C,cAAc,CAACzC,GAAG,CAAC,GAAGD,IAAI,CAAC;EAC3B2C,MAAM,CAACpD,QAAQ,GAAGmD,cAAc,CAACnD,QAAQ;EACzC5V,cAAc,CAACgZ,MAAM,CAACpD,QAAQ,CAAC;EAC/B,OAAOoD,MAAM;AACjB,CAAC;AACD;AACA;AACA;AACAA,MAAM,CAAClZ,UAAU,GAAG,UAAUgM,MAAM,EAAEyK,QAAQ,EAAE;EAC5C,OAAOwC,cAAc,CAACjZ,UAAU,CAACgM,MAAM,EAAEyK,QAAQ,CAAC;AACtD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACAyC,MAAM,CAACtF,WAAW,GAAGqF,cAAc,CAACrF,WAAW;AAC/C;AACA;AACA;AACAsF,MAAM,CAACjD,MAAM,GAAGpB,OAAO;AACvBqE,MAAM,CAAC1F,MAAM,GAAGqB,OAAO,CAAClB,KAAK;AAC7BuF,MAAM,CAAChD,QAAQ,GAAG3C,SAAS;AAC3B2F,MAAM,CAAC/C,YAAY,GAAGvB,aAAa;AACnCsE,MAAM,CAAC9C,KAAK,GAAG1E,MAAM;AACrBwH,MAAM,CAACzN,KAAK,GAAGiG,MAAM,CAACI,GAAG;AACzBoH,MAAM,CAAC7C,SAAS,GAAG5J,UAAU;AAC7ByM,MAAM,CAAC5C,KAAK,GAAGlB,MAAM;AACrB8D,MAAM,CAACvF,KAAK,GAAGuF,MAAM;AACrB,MAAMxM,OAAO,GAAGwM,MAAM,CAACxM,OAAO;AAC9B,MAAMqJ,UAAU,GAAGmD,MAAM,CAACnD,UAAU;AACpC,MAAMS,GAAG,GAAG0C,MAAM,CAAC1C,GAAG;AACtB,MAAMxW,UAAU,GAAGkZ,MAAM,CAAClZ,UAAU;AACpC,MAAM4T,WAAW,GAAGsF,MAAM,CAACtF,WAAW;AACtC,MAAMD,KAAK,GAAGuF,MAAM;AACpB,MAAM1F,MAAM,GAAGqB,OAAO,CAAClB,KAAK;AAC5B,MAAMlI,KAAK,GAAGiG,MAAM,CAACI,GAAG;AAExB,SAASsD,MAAM,IAAIkB,KAAK,EAAE5E,MAAM,IAAI0E,KAAK,EAAEP,MAAM,EAAEhB,OAAO,IAAIoB,MAAM,EAAE1C,SAAS,IAAI2C,QAAQ,EAAEtB,aAAa,IAAIuB,YAAY,EAAE1J,UAAU,IAAI4J,SAAS,EAAEpW,SAAS,IAAI6V,QAAQ,EAAExW,YAAY,IAAI6Z,WAAW,EAAE1N,KAAK,EAAEyN,MAAM,EAAExM,OAAO,EAAEiH,KAAK,EAAEC,WAAW,EAAEJ,MAAM,EAAEuC,UAAU,EAAES,GAAG,EAAExW,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}