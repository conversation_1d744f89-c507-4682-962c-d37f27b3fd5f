{"ast": null, "code": "import { __decorate } from 'tslib';\nimport { NgClass, NgTemplateOutlet } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Input, Directive, Optional, Self, ContentChildren, isDevMode, ContentChild, NgModule } from '@angular/core';\nimport { Subject, merge, EMPTY } from 'rxjs';\nimport { distinctUntilChanged, takeUntil, filter, startWith, switchMap, mergeMap, map } from 'rxjs/operators';\nimport * as i3 from 'ng-zorro-antd/core/form';\nimport { NzFormItemFeedbackIconComponent, NzFormNoStatusService, NzFormPatchModule } from 'ng-zorro-antd/core/form';\nimport { getStatusClassNames, InputBoolean, isNotNil } from 'ng-zorro-antd/core/util';\nimport * as i2 from 'ng-zorro-antd/core/outlet';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport * as i1 from 'ng-zorro-antd/icon';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport * as i1$1 from '@angular/forms';\nimport * as i2$1 from '@angular/cdk/bidi';\nimport * as i1$2 from '@angular/cdk/a11y';\nimport * as i1$3 from '@angular/cdk/platform';\nimport * as i2$2 from 'ng-zorro-antd/core/services';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst _c0 = [\"nz-input-group-slot\", \"\"];\nconst _c1 = [\"*\"];\nfunction NzInputGroupSlotComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 0);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"nzType\", ctx_r0.icon);\n  }\n}\nfunction NzInputGroupSlotComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.template);\n  }\n}\nfunction NzInputGroupComponent_Conditional_0_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 3);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"icon\", ctx_r0.nzAddOnBeforeIcon)(\"template\", ctx_r0.nzAddOnBefore);\n  }\n}\nfunction NzInputGroupComponent_Conditional_0_Conditional_2_ng_template_1_Template(rf, ctx) {}\nfunction NzInputGroupComponent_Conditional_0_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 5);\n    i0.ɵɵtemplate(1, NzInputGroupComponent_Conditional_0_Conditional_2_ng_template_1_Template, 0, 0, \"ng-template\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    const affixTemplate_r2 = i0.ɵɵreference(3);\n    i0.ɵɵclassProp(\"ant-input-affix-wrapper-disabled\", ctx_r0.disabled)(\"ant-input-affix-wrapper-sm\", ctx_r0.isSmall)(\"ant-input-affix-wrapper-lg\", ctx_r0.isLarge)(\"ant-input-affix-wrapper-focused\", ctx_r0.focused);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.affixInGroupStatusCls);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", affixTemplate_r2);\n  }\n}\nfunction NzInputGroupComponent_Conditional_0_Conditional_3_ng_template_0_Template(rf, ctx) {}\nfunction NzInputGroupComponent_Conditional_0_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzInputGroupComponent_Conditional_0_Conditional_3_ng_template_0_Template, 0, 0, \"ng-template\", 6);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(2);\n    const contentTemplate_r3 = i0.ɵɵreference(5);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", contentTemplate_r3);\n  }\n}\nfunction NzInputGroupComponent_Conditional_0_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 3);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"icon\", ctx_r0.nzAddOnAfterIcon)(\"template\", ctx_r0.nzAddOnAfter);\n  }\n}\nfunction NzInputGroupComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 2);\n    i0.ɵɵtemplate(1, NzInputGroupComponent_Conditional_0_Conditional_1_Template, 1, 2, \"span\", 3)(2, NzInputGroupComponent_Conditional_0_Conditional_2_Template, 2, 10, \"span\", 4)(3, NzInputGroupComponent_Conditional_0_Conditional_3_Template, 1, 1)(4, NzInputGroupComponent_Conditional_0_Conditional_4_Template, 1, 2, \"span\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(1, ctx_r0.nzAddOnBefore || ctx_r0.nzAddOnBeforeIcon ? 1 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(2, ctx_r0.isAffix || ctx_r0.hasFeedback ? 2 : 3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵconditional(4, ctx_r0.nzAddOnAfter || ctx_r0.nzAddOnAfterIcon ? 4 : -1);\n  }\n}\nfunction NzInputGroupComponent_Conditional_1_Conditional_0_ng_template_0_Template(rf, ctx) {}\nfunction NzInputGroupComponent_Conditional_1_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzInputGroupComponent_Conditional_1_Conditional_0_ng_template_0_Template, 0, 0, \"ng-template\", 6);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(2);\n    const affixTemplate_r2 = i0.ɵɵreference(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", affixTemplate_r2);\n  }\n}\nfunction NzInputGroupComponent_Conditional_1_Conditional_1_ng_template_0_Template(rf, ctx) {}\nfunction NzInputGroupComponent_Conditional_1_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzInputGroupComponent_Conditional_1_Conditional_1_ng_template_0_Template, 0, 0, \"ng-template\", 6);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(2);\n    const contentTemplate_r3 = i0.ɵɵreference(5);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", contentTemplate_r3);\n  }\n}\nfunction NzInputGroupComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzInputGroupComponent_Conditional_1_Conditional_0_Template, 1, 1, null, 6)(1, NzInputGroupComponent_Conditional_1_Conditional_1_Template, 1, 1);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵconditional(0, ctx_r0.isAffix ? 0 : 1);\n  }\n}\nfunction NzInputGroupComponent_ng_template_2_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"icon\", ctx_r0.nzPrefixIcon)(\"template\", ctx_r0.nzPrefix);\n  }\n}\nfunction NzInputGroupComponent_ng_template_2_ng_template_1_Template(rf, ctx) {}\nfunction NzInputGroupComponent_ng_template_2_Conditional_2_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-form-item-feedback-icon\", 9);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"status\", ctx_r0.status);\n  }\n}\nfunction NzInputGroupComponent_ng_template_2_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 8);\n    i0.ɵɵtemplate(1, NzInputGroupComponent_ng_template_2_Conditional_2_Conditional_1_Template, 1, 1, \"nz-form-item-feedback-icon\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"icon\", ctx_r0.nzSuffixIcon)(\"template\", ctx_r0.nzSuffix);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(1, ctx_r0.isFeedback ? 1 : -1);\n  }\n}\nfunction NzInputGroupComponent_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzInputGroupComponent_ng_template_2_Conditional_0_Template, 1, 2, \"span\", 7)(1, NzInputGroupComponent_ng_template_2_ng_template_1_Template, 0, 0, \"ng-template\", 6)(2, NzInputGroupComponent_ng_template_2_Conditional_2_Template, 2, 3, \"span\", 8);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    const contentTemplate_r3 = i0.ɵɵreference(5);\n    i0.ɵɵconditional(0, ctx_r0.nzPrefix || ctx_r0.nzPrefixIcon ? 0 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", contentTemplate_r3);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(2, ctx_r0.nzSuffix || ctx_r0.nzSuffixIcon || ctx_r0.isFeedback ? 2 : -1);\n  }\n}\nfunction NzInputGroupComponent_ng_template_4_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 10);\n    i0.ɵɵelement(1, \"nz-form-item-feedback-icon\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"status\", ctx_r0.status);\n  }\n}\nfunction NzInputGroupComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n    i0.ɵɵtemplate(1, NzInputGroupComponent_ng_template_4_Conditional_1_Template, 2, 1, \"span\", 10);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(1, !ctx_r0.isAddOn && !ctx_r0.isAffix && ctx_r0.isFeedback ? 1 : -1);\n  }\n}\nconst _c2 = [[[\"textarea\", \"nz-input\", \"\"]]];\nconst _c3 = [\"textarea[nz-input]\"];\nclass NzInputGroupSlotComponent {\n  constructor() {\n    this.icon = null;\n    this.type = null;\n    this.template = null;\n  }\n  static {\n    this.ɵfac = function NzInputGroupSlotComponent_Factory(t) {\n      return new (t || NzInputGroupSlotComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzInputGroupSlotComponent,\n      selectors: [[\"\", \"nz-input-group-slot\", \"\"]],\n      hostVars: 6,\n      hostBindings: function NzInputGroupSlotComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-input-group-addon\", ctx.type === \"addon\")(\"ant-input-prefix\", ctx.type === \"prefix\")(\"ant-input-suffix\", ctx.type === \"suffix\");\n        }\n      },\n      inputs: {\n        icon: \"icon\",\n        type: \"type\",\n        template: \"template\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      attrs: _c0,\n      ngContentSelectors: _c1,\n      decls: 3,\n      vars: 2,\n      consts: [[\"nz-icon\", \"\", 3, \"nzType\"], [4, \"nzStringTemplateOutlet\"]],\n      template: function NzInputGroupSlotComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, NzInputGroupSlotComponent_Conditional_0_Template, 1, 1, \"span\", 0)(1, NzInputGroupSlotComponent_ng_container_1_Template, 2, 1, \"ng-container\", 1);\n          i0.ɵɵprojection(2);\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(0, ctx.icon ? 0 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx.template);\n        }\n      },\n      dependencies: [NzIconModule, i1.NzIconDirective, NzOutletModule, i2.NzStringTemplateOutletDirective],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzInputGroupSlotComponent, [{\n    type: Component,\n    args: [{\n      selector: '[nz-input-group-slot]',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    @if (icon) {\n      <span nz-icon [nzType]=\"icon\"></span>\n    }\n    <ng-container *nzStringTemplateOutlet=\"template\">{{ template }}</ng-container>\n    <ng-content></ng-content>\n  `,\n      host: {\n        '[class.ant-input-group-addon]': `type === 'addon'`,\n        '[class.ant-input-prefix]': `type === 'prefix'`,\n        '[class.ant-input-suffix]': `type === 'suffix'`\n      },\n      imports: [NzIconModule, NzOutletModule],\n      standalone: true\n    }]\n  }], null, {\n    icon: [{\n      type: Input\n    }],\n    type: [{\n      type: Input\n    }],\n    template: [{\n      type: Input\n    }]\n  });\n})();\nclass NzInputDirective {\n  get disabled() {\n    if (this.ngControl && this.ngControl.disabled !== null) {\n      return this.ngControl.disabled;\n    }\n    return this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = value != null && `${value}` !== 'false';\n  }\n  constructor(ngControl, renderer, elementRef, hostView, directionality, nzFormStatusService, nzFormNoStatusService) {\n    this.ngControl = ngControl;\n    this.renderer = renderer;\n    this.elementRef = elementRef;\n    this.hostView = hostView;\n    this.directionality = directionality;\n    this.nzFormStatusService = nzFormStatusService;\n    this.nzFormNoStatusService = nzFormNoStatusService;\n    this.nzBorderless = false;\n    this.nzSize = 'default';\n    this.nzStepperless = true;\n    this.nzStatus = '';\n    this._disabled = false;\n    this.disabled$ = new Subject();\n    this.dir = 'ltr';\n    // status\n    this.prefixCls = 'ant-input';\n    this.status = '';\n    this.statusCls = {};\n    this.hasFeedback = false;\n    this.feedbackRef = null;\n    this.components = [];\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {\n    this.nzFormStatusService?.formStatusChanges.pipe(distinctUntilChanged((pre, cur) => {\n      return pre.status === cur.status && pre.hasFeedback === cur.hasFeedback;\n    }), takeUntil(this.destroy$)).subscribe(({\n      status,\n      hasFeedback\n    }) => {\n      this.setStatusStyles(status, hasFeedback);\n    });\n    if (this.ngControl) {\n      this.ngControl.statusChanges?.pipe(filter(() => this.ngControl.disabled !== null), takeUntil(this.destroy$)).subscribe(() => {\n        this.disabled$.next(this.ngControl.disabled);\n      });\n    }\n    this.dir = this.directionality.value;\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n    });\n  }\n  ngOnChanges(changes) {\n    const {\n      disabled,\n      nzStatus\n    } = changes;\n    if (disabled) {\n      this.disabled$.next(this.disabled);\n    }\n    if (nzStatus) {\n      this.setStatusStyles(this.nzStatus, this.hasFeedback);\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  setStatusStyles(status, hasFeedback) {\n    // set inner status\n    this.status = status;\n    this.hasFeedback = hasFeedback;\n    this.renderFeedbackIcon();\n    // render status if nzStatus is set\n    this.statusCls = getStatusClassNames(this.prefixCls, status, hasFeedback);\n    Object.keys(this.statusCls).forEach(status => {\n      if (this.statusCls[status]) {\n        this.renderer.addClass(this.elementRef.nativeElement, status);\n      } else {\n        this.renderer.removeClass(this.elementRef.nativeElement, status);\n      }\n    });\n  }\n  renderFeedbackIcon() {\n    if (!this.status || !this.hasFeedback || !!this.nzFormNoStatusService) {\n      // remove feedback\n      this.hostView.clear();\n      this.feedbackRef = null;\n      return;\n    }\n    this.feedbackRef = this.feedbackRef || this.hostView.createComponent(NzFormItemFeedbackIconComponent);\n    this.feedbackRef.location.nativeElement.classList.add('ant-input-suffix');\n    this.feedbackRef.instance.status = this.status;\n    this.feedbackRef.instance.updateIcon();\n  }\n  static {\n    this.ɵfac = function NzInputDirective_Factory(t) {\n      return new (t || NzInputDirective)(i0.ɵɵdirectiveInject(i1$1.NgControl, 10), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i2$1.Directionality, 8), i0.ɵɵdirectiveInject(i3.NzFormStatusService, 8), i0.ɵɵdirectiveInject(i3.NzFormNoStatusService, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzInputDirective,\n      selectors: [[\"input\", \"nz-input\", \"\"], [\"textarea\", \"nz-input\", \"\"]],\n      hostAttrs: [1, \"ant-input\"],\n      hostVars: 13,\n      hostBindings: function NzInputDirective_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"disabled\", ctx.disabled || null);\n          i0.ɵɵclassProp(\"ant-input-disabled\", ctx.disabled)(\"ant-input-borderless\", ctx.nzBorderless)(\"ant-input-lg\", ctx.nzSize === \"large\")(\"ant-input-sm\", ctx.nzSize === \"small\")(\"ant-input-rtl\", ctx.dir === \"rtl\")(\"ant-input-stepperless\", ctx.nzStepperless);\n        }\n      },\n      inputs: {\n        nzBorderless: \"nzBorderless\",\n        nzSize: \"nzSize\",\n        nzStepperless: \"nzStepperless\",\n        nzStatus: \"nzStatus\",\n        disabled: \"disabled\"\n      },\n      exportAs: [\"nzInput\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n__decorate([InputBoolean()], NzInputDirective.prototype, \"nzBorderless\", void 0);\n__decorate([InputBoolean()], NzInputDirective.prototype, \"nzStepperless\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzInputDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'input[nz-input],textarea[nz-input]',\n      exportAs: 'nzInput',\n      host: {\n        class: 'ant-input',\n        '[class.ant-input-disabled]': 'disabled',\n        '[class.ant-input-borderless]': 'nzBorderless',\n        '[class.ant-input-lg]': `nzSize === 'large'`,\n        '[class.ant-input-sm]': `nzSize === 'small'`,\n        '[attr.disabled]': 'disabled || null',\n        '[class.ant-input-rtl]': `dir=== 'rtl'`,\n        '[class.ant-input-stepperless]': `nzStepperless`\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: i1$1.NgControl,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Self\n    }]\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: i2$1.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i3.NzFormStatusService,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i3.NzFormNoStatusService,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    nzBorderless: [{\n      type: Input\n    }],\n    nzSize: [{\n      type: Input\n    }],\n    nzStepperless: [{\n      type: Input\n    }],\n    nzStatus: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }]\n  });\n})();\nclass NzInputGroupWhitSuffixOrPrefixDirective {\n  constructor(elementRef) {\n    this.elementRef = elementRef;\n  }\n  static {\n    this.ɵfac = function NzInputGroupWhitSuffixOrPrefixDirective_Factory(t) {\n      return new (t || NzInputGroupWhitSuffixOrPrefixDirective)(i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzInputGroupWhitSuffixOrPrefixDirective,\n      selectors: [[\"nz-input-group\", \"nzSuffix\", \"\"], [\"nz-input-group\", \"nzPrefix\", \"\"]],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzInputGroupWhitSuffixOrPrefixDirective, [{\n    type: Directive,\n    args: [{\n      selector: `nz-input-group[nzSuffix], nz-input-group[nzPrefix]`,\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }], null);\n})();\nclass NzInputGroupComponent {\n  constructor(focusMonitor, elementRef, renderer, cdr, directionality, nzFormStatusService, nzFormNoStatusService) {\n    this.focusMonitor = focusMonitor;\n    this.elementRef = elementRef;\n    this.renderer = renderer;\n    this.cdr = cdr;\n    this.directionality = directionality;\n    this.nzFormStatusService = nzFormStatusService;\n    this.nzFormNoStatusService = nzFormNoStatusService;\n    this.nzAddOnBeforeIcon = null;\n    this.nzAddOnAfterIcon = null;\n    this.nzPrefixIcon = null;\n    this.nzSuffixIcon = null;\n    this.nzStatus = '';\n    this.nzSize = 'default';\n    this.nzSearch = false;\n    this.nzCompact = false;\n    this.isLarge = false;\n    this.isSmall = false;\n    this.isAffix = false;\n    this.isAddOn = false;\n    this.isFeedback = false;\n    this.focused = false;\n    this.disabled = false;\n    this.dir = 'ltr';\n    // status\n    this.prefixCls = 'ant-input';\n    this.affixStatusCls = {};\n    this.groupStatusCls = {};\n    this.affixInGroupStatusCls = {};\n    this.status = '';\n    this.hasFeedback = false;\n    this.destroy$ = new Subject();\n  }\n  updateChildrenInputSize() {\n    if (this.listOfNzInputDirective) {\n      this.listOfNzInputDirective.forEach(item => item.nzSize = this.nzSize);\n    }\n  }\n  ngOnInit() {\n    this.nzFormStatusService?.formStatusChanges.pipe(distinctUntilChanged((pre, cur) => {\n      return pre.status === cur.status && pre.hasFeedback === cur.hasFeedback;\n    }), takeUntil(this.destroy$)).subscribe(({\n      status,\n      hasFeedback\n    }) => {\n      this.setStatusStyles(status, hasFeedback);\n    });\n    this.focusMonitor.monitor(this.elementRef, true).pipe(takeUntil(this.destroy$)).subscribe(focusOrigin => {\n      this.focused = !!focusOrigin;\n      this.cdr.markForCheck();\n    });\n    this.dir = this.directionality.value;\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n    });\n  }\n  ngAfterContentInit() {\n    this.updateChildrenInputSize();\n    const listOfInputChange$ = this.listOfNzInputDirective.changes.pipe(startWith(this.listOfNzInputDirective));\n    listOfInputChange$.pipe(switchMap(list => merge(...[listOfInputChange$, ...list.map(input => input.disabled$)])), mergeMap(() => listOfInputChange$), map(list => list.some(input => input.disabled)), takeUntil(this.destroy$)).subscribe(disabled => {\n      this.disabled = disabled;\n      this.cdr.markForCheck();\n    });\n  }\n  ngOnChanges(changes) {\n    const {\n      nzSize,\n      nzSuffix,\n      nzPrefix,\n      nzPrefixIcon,\n      nzSuffixIcon,\n      nzAddOnAfter,\n      nzAddOnBefore,\n      nzAddOnAfterIcon,\n      nzAddOnBeforeIcon,\n      nzStatus\n    } = changes;\n    if (nzSize) {\n      this.updateChildrenInputSize();\n      this.isLarge = this.nzSize === 'large';\n      this.isSmall = this.nzSize === 'small';\n    }\n    if (nzSuffix || nzPrefix || nzPrefixIcon || nzSuffixIcon) {\n      this.isAffix = !!(this.nzSuffix || this.nzPrefix || this.nzPrefixIcon || this.nzSuffixIcon);\n    }\n    if (nzAddOnAfter || nzAddOnBefore || nzAddOnAfterIcon || nzAddOnBeforeIcon) {\n      this.isAddOn = !!(this.nzAddOnAfter || this.nzAddOnBefore || this.nzAddOnAfterIcon || this.nzAddOnBeforeIcon);\n      this.nzFormNoStatusService?.noFormStatus?.next(this.isAddOn);\n    }\n    if (nzStatus) {\n      this.setStatusStyles(this.nzStatus, this.hasFeedback);\n    }\n  }\n  ngOnDestroy() {\n    this.focusMonitor.stopMonitoring(this.elementRef);\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  setStatusStyles(status, hasFeedback) {\n    // set inner status\n    this.status = status;\n    this.hasFeedback = hasFeedback;\n    this.isFeedback = !!status && hasFeedback;\n    const baseAffix = !!(this.nzSuffix || this.nzPrefix || this.nzPrefixIcon || this.nzSuffixIcon);\n    this.isAffix = baseAffix || !this.isAddOn && hasFeedback;\n    this.affixInGroupStatusCls = this.isAffix || this.isFeedback ? this.affixStatusCls = getStatusClassNames(`${this.prefixCls}-affix-wrapper`, status, hasFeedback) : {};\n    this.cdr.markForCheck();\n    // render status if nzStatus is set\n    this.affixStatusCls = getStatusClassNames(`${this.prefixCls}-affix-wrapper`, this.isAddOn ? '' : status, this.isAddOn ? false : hasFeedback);\n    this.groupStatusCls = getStatusClassNames(`${this.prefixCls}-group-wrapper`, this.isAddOn ? status : '', this.isAddOn ? hasFeedback : false);\n    const statusCls = {\n      ...this.affixStatusCls,\n      ...this.groupStatusCls\n    };\n    Object.keys(statusCls).forEach(status => {\n      if (statusCls[status]) {\n        this.renderer.addClass(this.elementRef.nativeElement, status);\n      } else {\n        this.renderer.removeClass(this.elementRef.nativeElement, status);\n      }\n    });\n  }\n  static {\n    this.ɵfac = function NzInputGroupComponent_Factory(t) {\n      return new (t || NzInputGroupComponent)(i0.ɵɵdirectiveInject(i1$2.FocusMonitor), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2$1.Directionality, 8), i0.ɵɵdirectiveInject(i3.NzFormStatusService, 8), i0.ɵɵdirectiveInject(i3.NzFormNoStatusService, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzInputGroupComponent,\n      selectors: [[\"nz-input-group\"]],\n      contentQueries: function NzInputGroupComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, NzInputDirective, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfNzInputDirective = _t);\n        }\n      },\n      hostVars: 40,\n      hostBindings: function NzInputGroupComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-input-group-compact\", ctx.nzCompact)(\"ant-input-search-enter-button\", ctx.nzSearch)(\"ant-input-search\", ctx.nzSearch)(\"ant-input-search-rtl\", ctx.dir === \"rtl\")(\"ant-input-search-sm\", ctx.nzSearch && ctx.isSmall)(\"ant-input-search-large\", ctx.nzSearch && ctx.isLarge)(\"ant-input-group-wrapper\", ctx.isAddOn)(\"ant-input-group-wrapper-rtl\", ctx.dir === \"rtl\")(\"ant-input-group-wrapper-lg\", ctx.isAddOn && ctx.isLarge)(\"ant-input-group-wrapper-sm\", ctx.isAddOn && ctx.isSmall)(\"ant-input-affix-wrapper\", ctx.isAffix && !ctx.isAddOn)(\"ant-input-affix-wrapper-rtl\", ctx.dir === \"rtl\")(\"ant-input-affix-wrapper-focused\", ctx.isAffix && ctx.focused)(\"ant-input-affix-wrapper-disabled\", ctx.isAffix && ctx.disabled)(\"ant-input-affix-wrapper-lg\", ctx.isAffix && !ctx.isAddOn && ctx.isLarge)(\"ant-input-affix-wrapper-sm\", ctx.isAffix && !ctx.isAddOn && ctx.isSmall)(\"ant-input-group\", !ctx.isAffix && !ctx.isAddOn)(\"ant-input-group-rtl\", ctx.dir === \"rtl\")(\"ant-input-group-lg\", !ctx.isAffix && !ctx.isAddOn && ctx.isLarge)(\"ant-input-group-sm\", !ctx.isAffix && !ctx.isAddOn && ctx.isSmall);\n        }\n      },\n      inputs: {\n        nzAddOnBeforeIcon: \"nzAddOnBeforeIcon\",\n        nzAddOnAfterIcon: \"nzAddOnAfterIcon\",\n        nzPrefixIcon: \"nzPrefixIcon\",\n        nzSuffixIcon: \"nzSuffixIcon\",\n        nzAddOnBefore: \"nzAddOnBefore\",\n        nzAddOnAfter: \"nzAddOnAfter\",\n        nzPrefix: \"nzPrefix\",\n        nzStatus: \"nzStatus\",\n        nzSuffix: \"nzSuffix\",\n        nzSize: \"nzSize\",\n        nzSearch: \"nzSearch\",\n        nzCompact: \"nzCompact\"\n      },\n      exportAs: [\"nzInputGroup\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([NzFormNoStatusService]), i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c1,\n      decls: 6,\n      vars: 1,\n      consts: [[\"affixTemplate\", \"\"], [\"contentTemplate\", \"\"], [1, \"ant-input-wrapper\", \"ant-input-group\"], [\"nz-input-group-slot\", \"\", \"type\", \"addon\", 3, \"icon\", \"template\"], [1, \"ant-input-affix-wrapper\", 3, \"ant-input-affix-wrapper-disabled\", \"ant-input-affix-wrapper-sm\", \"ant-input-affix-wrapper-lg\", \"ant-input-affix-wrapper-focused\", \"ngClass\"], [1, \"ant-input-affix-wrapper\", 3, \"ngClass\"], [3, \"ngTemplateOutlet\"], [\"nz-input-group-slot\", \"\", \"type\", \"prefix\", 3, \"icon\", \"template\"], [\"nz-input-group-slot\", \"\", \"type\", \"suffix\", 3, \"icon\", \"template\"], [3, \"status\"], [\"nz-input-group-slot\", \"\", \"type\", \"suffix\"]],\n      template: function NzInputGroupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, NzInputGroupComponent_Conditional_0_Template, 5, 3, \"span\", 2)(1, NzInputGroupComponent_Conditional_1_Template, 2, 1)(2, NzInputGroupComponent_ng_template_2_Template, 3, 3, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(4, NzInputGroupComponent_ng_template_4_Template, 2, 1, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(0, ctx.isAddOn ? 0 : 1);\n        }\n      },\n      dependencies: [NzInputGroupSlotComponent, NgClass, NgTemplateOutlet, NzFormPatchModule, i3.NzFormItemFeedbackIconComponent],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([InputBoolean()], NzInputGroupComponent.prototype, \"nzSearch\", void 0);\n__decorate([InputBoolean()], NzInputGroupComponent.prototype, \"nzCompact\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzInputGroupComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-input-group',\n      exportAs: 'nzInputGroup',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [NzFormNoStatusService],\n      template: `\n    @if (isAddOn) {\n      <span class=\"ant-input-wrapper ant-input-group\">\n        @if (nzAddOnBefore || nzAddOnBeforeIcon) {\n          <span nz-input-group-slot type=\"addon\" [icon]=\"nzAddOnBeforeIcon\" [template]=\"nzAddOnBefore\"></span>\n        }\n\n        @if (isAffix || hasFeedback) {\n          <span\n            class=\"ant-input-affix-wrapper\"\n            [class.ant-input-affix-wrapper-disabled]=\"disabled\"\n            [class.ant-input-affix-wrapper-sm]=\"isSmall\"\n            [class.ant-input-affix-wrapper-lg]=\"isLarge\"\n            [class.ant-input-affix-wrapper-focused]=\"focused\"\n            [ngClass]=\"affixInGroupStatusCls\"\n          >\n            <ng-template [ngTemplateOutlet]=\"affixTemplate\"></ng-template>\n          </span>\n        } @else {\n          <ng-template [ngTemplateOutlet]=\"contentTemplate\" />\n        }\n        @if (nzAddOnAfter || nzAddOnAfterIcon) {\n          <span nz-input-group-slot type=\"addon\" [icon]=\"nzAddOnAfterIcon\" [template]=\"nzAddOnAfter\"></span>\n        }\n      </span>\n    } @else {\n      @if (isAffix) {\n        <ng-template [ngTemplateOutlet]=\"affixTemplate\" />\n      } @else {\n        <ng-template [ngTemplateOutlet]=\"contentTemplate\" />\n      }\n    }\n\n    <!-- affix template -->\n    <ng-template #affixTemplate>\n      @if (nzPrefix || nzPrefixIcon) {\n        <span nz-input-group-slot type=\"prefix\" [icon]=\"nzPrefixIcon\" [template]=\"nzPrefix\"></span>\n      }\n      <ng-template [ngTemplateOutlet]=\"contentTemplate\" />\n      @if (nzSuffix || nzSuffixIcon || isFeedback) {\n        <span nz-input-group-slot type=\"suffix\" [icon]=\"nzSuffixIcon\" [template]=\"nzSuffix\">\n          @if (isFeedback) {\n            <nz-form-item-feedback-icon [status]=\"status\" />\n          }\n        </span>\n      }\n    </ng-template>\n\n    <!-- content template -->\n    <ng-template #contentTemplate>\n      <ng-content></ng-content>\n      @if (!isAddOn && !isAffix && isFeedback) {\n        <span nz-input-group-slot type=\"suffix\">\n          <nz-form-item-feedback-icon [status]=\"status\" />\n        </span>\n      }\n    </ng-template>\n  `,\n      host: {\n        '[class.ant-input-group-compact]': `nzCompact`,\n        '[class.ant-input-search-enter-button]': `nzSearch`,\n        '[class.ant-input-search]': `nzSearch`,\n        '[class.ant-input-search-rtl]': `dir === 'rtl'`,\n        '[class.ant-input-search-sm]': `nzSearch && isSmall`,\n        '[class.ant-input-search-large]': `nzSearch && isLarge`,\n        '[class.ant-input-group-wrapper]': `isAddOn`,\n        '[class.ant-input-group-wrapper-rtl]': `dir === 'rtl'`,\n        '[class.ant-input-group-wrapper-lg]': `isAddOn && isLarge`,\n        '[class.ant-input-group-wrapper-sm]': `isAddOn && isSmall`,\n        '[class.ant-input-affix-wrapper]': `isAffix && !isAddOn`,\n        '[class.ant-input-affix-wrapper-rtl]': `dir === 'rtl'`,\n        '[class.ant-input-affix-wrapper-focused]': `isAffix && focused`,\n        '[class.ant-input-affix-wrapper-disabled]': `isAffix && disabled`,\n        '[class.ant-input-affix-wrapper-lg]': `isAffix && !isAddOn && isLarge`,\n        '[class.ant-input-affix-wrapper-sm]': `isAffix && !isAddOn && isSmall`,\n        '[class.ant-input-group]': `!isAffix && !isAddOn`,\n        '[class.ant-input-group-rtl]': `dir === 'rtl'`,\n        '[class.ant-input-group-lg]': `!isAffix && !isAddOn && isLarge`,\n        '[class.ant-input-group-sm]': `!isAffix && !isAddOn && isSmall`\n      },\n      imports: [NzInputGroupSlotComponent, NgClass, NgTemplateOutlet, NzFormPatchModule],\n      standalone: true\n    }]\n  }], () => [{\n    type: i1$2.FocusMonitor\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i2$1.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i3.NzFormStatusService,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i3.NzFormNoStatusService,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    listOfNzInputDirective: [{\n      type: ContentChildren,\n      args: [NzInputDirective]\n    }],\n    nzAddOnBeforeIcon: [{\n      type: Input\n    }],\n    nzAddOnAfterIcon: [{\n      type: Input\n    }],\n    nzPrefixIcon: [{\n      type: Input\n    }],\n    nzSuffixIcon: [{\n      type: Input\n    }],\n    nzAddOnBefore: [{\n      type: Input\n    }],\n    nzAddOnAfter: [{\n      type: Input\n    }],\n    nzPrefix: [{\n      type: Input\n    }],\n    nzStatus: [{\n      type: Input\n    }],\n    nzSuffix: [{\n      type: Input\n    }],\n    nzSize: [{\n      type: Input\n    }],\n    nzSearch: [{\n      type: Input\n    }],\n    nzCompact: [{\n      type: Input\n    }]\n  });\n})();\nclass NzAutosizeDirective {\n  set nzAutosize(value) {\n    const isAutoSizeType = data => typeof data !== 'string' && typeof data !== 'boolean' && (!!data.maxRows || !!data.minRows);\n    if (typeof value === 'string' || value === true) {\n      this.autosize = true;\n    } else if (isAutoSizeType(value)) {\n      this.autosize = true;\n      this.minRows = value.minRows;\n      this.maxRows = value.maxRows;\n      this.maxHeight = this.setMaxHeight();\n      this.minHeight = this.setMinHeight();\n    }\n  }\n  resizeToFitContent(force = false) {\n    this.cacheTextareaLineHeight();\n    // If we haven't determined the line-height yet, we know we're still hidden and there's no point\n    // in checking the height of the textarea.\n    if (!this.cachedLineHeight) {\n      return;\n    }\n    const textarea = this.el;\n    const value = textarea.value;\n    // Only resize if the value or minRows have changed since these calculations can be expensive.\n    if (!force && this.minRows === this.previousMinRows && value === this.previousValue) {\n      return;\n    }\n    const placeholderText = textarea.placeholder;\n    // Reset the textarea height to auto in order to shrink back to its default size.\n    // Also temporarily force overflow:hidden, so scroll bars do not interfere with calculations.\n    // Long placeholders that are wider than the textarea width may lead to a bigger scrollHeight\n    // value. To ensure that the scrollHeight is not bigger than the content, the placeholders\n    // need to be removed temporarily.\n    textarea.classList.add('nz-textarea-autosize-measuring');\n    textarea.placeholder = '';\n    let height = Math.round((textarea.scrollHeight - this.inputGap) / this.cachedLineHeight) * this.cachedLineHeight + this.inputGap;\n    if (this.maxHeight !== null && height > this.maxHeight) {\n      height = this.maxHeight;\n    }\n    if (this.minHeight !== null && height < this.minHeight) {\n      height = this.minHeight;\n    }\n    // Use the scrollHeight to know how large the textarea *would* be if fit its entire value.\n    textarea.style.height = `${height}px`;\n    textarea.classList.remove('nz-textarea-autosize-measuring');\n    textarea.placeholder = placeholderText;\n    // On Firefox resizing the textarea will prevent it from scrolling to the caret position.\n    // We need to re-set the selection in order for it to scroll to the proper position.\n    if (typeof requestAnimationFrame !== 'undefined') {\n      this.ngZone.runOutsideAngular(() => requestAnimationFrame(() => {\n        const {\n          selectionStart,\n          selectionEnd\n        } = textarea;\n        // IE will throw an \"Unspecified error\" if we try to set the selection range after the\n        // element has been removed from the DOM. Assert that the directive hasn't been destroyed\n        // between the time we requested the animation frame and when it was executed.\n        // Also note that we have to assert that the textarea is focused before we set the\n        // selection range. Setting the selection range on a non-focused textarea will cause\n        // it to receive focus on IE and Edge.\n        if (!this.destroy$.isStopped && document.activeElement === textarea) {\n          textarea.setSelectionRange(selectionStart, selectionEnd);\n        }\n      }));\n    }\n    this.previousValue = value;\n    this.previousMinRows = this.minRows;\n  }\n  cacheTextareaLineHeight() {\n    if (this.cachedLineHeight >= 0 || !this.el.parentNode) {\n      return;\n    }\n    // Use a clone element because we have to override some styles.\n    const textareaClone = this.el.cloneNode(false);\n    textareaClone.rows = 1;\n    // Use `position: absolute` so that this doesn't cause a browser layout and use\n    // `visibility: hidden` so that nothing is rendered. Clear any other styles that\n    // would affect the height.\n    textareaClone.style.position = 'absolute';\n    textareaClone.style.visibility = 'hidden';\n    textareaClone.style.border = 'none';\n    textareaClone.style.padding = '0';\n    textareaClone.style.height = '';\n    textareaClone.style.minHeight = '';\n    textareaClone.style.maxHeight = '';\n    // In Firefox it happens that textarea elements are always bigger than the specified amount\n    // of rows. This is because Firefox tries to add extra space for the horizontal scrollbar.\n    // As a workaround that removes the extra space for the scrollbar, we can just set overflow\n    // to hidden. This ensures that there is no invalid calculation of the line height.\n    // See Firefox bug report: https://bugzilla.mozilla.org/show_bug.cgi?id=33654\n    textareaClone.style.overflow = 'hidden';\n    this.el.parentNode.appendChild(textareaClone);\n    this.cachedLineHeight = textareaClone.clientHeight - this.inputGap;\n    this.el.parentNode.removeChild(textareaClone);\n    // Min and max heights have to be re-calculated if the cached line height changes\n    this.maxHeight = this.setMaxHeight();\n    this.minHeight = this.setMinHeight();\n  }\n  setMinHeight() {\n    const minHeight = this.minRows && this.cachedLineHeight ? this.minRows * this.cachedLineHeight + this.inputGap : null;\n    if (minHeight !== null) {\n      this.el.style.minHeight = `${minHeight}px`;\n    }\n    return minHeight;\n  }\n  setMaxHeight() {\n    const maxHeight = this.maxRows && this.cachedLineHeight ? this.maxRows * this.cachedLineHeight + this.inputGap : null;\n    if (maxHeight !== null) {\n      this.el.style.maxHeight = `${maxHeight}px`;\n    }\n    return maxHeight;\n  }\n  noopInputHandler() {\n    // no-op handler that ensures we're running change detection on input events.\n  }\n  constructor(elementRef, ngZone, platform, resizeService) {\n    this.elementRef = elementRef;\n    this.ngZone = ngZone;\n    this.platform = platform;\n    this.resizeService = resizeService;\n    this.autosize = false;\n    this.el = this.elementRef.nativeElement;\n    this.maxHeight = null;\n    this.minHeight = null;\n    this.destroy$ = new Subject();\n    this.inputGap = 10;\n  }\n  ngAfterViewInit() {\n    if (this.autosize && this.platform.isBrowser) {\n      this.resizeToFitContent();\n      this.resizeService.subscribe().pipe(takeUntil(this.destroy$)).subscribe(() => this.resizeToFitContent(true));\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next(true);\n    this.destroy$.complete();\n  }\n  ngDoCheck() {\n    if (this.autosize && this.platform.isBrowser) {\n      this.resizeToFitContent();\n    }\n  }\n  static {\n    this.ɵfac = function NzAutosizeDirective_Factory(t) {\n      return new (t || NzAutosizeDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1$3.Platform), i0.ɵɵdirectiveInject(i2$2.NzResizeService));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzAutosizeDirective,\n      selectors: [[\"textarea\", \"nzAutosize\", \"\"]],\n      hostAttrs: [\"rows\", \"1\"],\n      hostBindings: function NzAutosizeDirective_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"input\", function NzAutosizeDirective_input_HostBindingHandler() {\n            return ctx.noopInputHandler();\n          });\n        }\n      },\n      inputs: {\n        nzAutosize: \"nzAutosize\"\n      },\n      exportAs: [\"nzAutosize\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzAutosizeDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'textarea[nzAutosize]',\n      exportAs: 'nzAutosize',\n      host: {\n        // Textarea elements that have the directive applied should have a single row by default.\n        // Browsers normally show two rows by default and therefore this limits the minRows binding.\n        rows: '1',\n        '(input)': 'noopInputHandler()'\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.NgZone\n  }, {\n    type: i1$3.Platform\n  }, {\n    type: i2$2.NzResizeService\n  }], {\n    nzAutosize: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTextareaCountComponent {\n  constructor(renderer, elementRef) {\n    this.renderer = renderer;\n    this.elementRef = elementRef;\n    this.nzMaxCharacterCount = 0;\n    this.nzComputeCharacterCount = v => v.length;\n    this.nzFormatter = (c, m) => `${c}${m > 0 ? `/${m}` : ``}`;\n    this.configChange$ = new Subject();\n    this.destroy$ = new Subject();\n  }\n  ngAfterContentInit() {\n    if (!this.nzInputDirective && isDevMode()) {\n      throw new Error('[nz-textarea-count]: Could not find matching textarea[nz-input] child.');\n    }\n    if (this.nzInputDirective.ngControl) {\n      const valueChanges = this.nzInputDirective.ngControl.valueChanges || EMPTY;\n      merge(valueChanges, this.configChange$).pipe(takeUntil(this.destroy$), map(() => this.nzInputDirective.ngControl.value), startWith(this.nzInputDirective.ngControl.value)).subscribe(value => {\n        this.setDataCount(value);\n      });\n    }\n  }\n  setDataCount(value) {\n    const inputValue = isNotNil(value) ? String(value) : '';\n    const currentCount = this.nzComputeCharacterCount(inputValue);\n    const dataCount = this.nzFormatter(currentCount, this.nzMaxCharacterCount);\n    this.renderer.setAttribute(this.elementRef.nativeElement, 'data-count', dataCount);\n  }\n  ngOnDestroy() {\n    this.configChange$.complete();\n    this.destroy$.next(true);\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzTextareaCountComponent_Factory(t) {\n      return new (t || NzTextareaCountComponent)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzTextareaCountComponent,\n      selectors: [[\"nz-textarea-count\"]],\n      contentQueries: function NzTextareaCountComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, NzInputDirective, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.nzInputDirective = _t.first);\n        }\n      },\n      hostAttrs: [1, \"ant-input-textarea-show-count\"],\n      inputs: {\n        nzMaxCharacterCount: \"nzMaxCharacterCount\",\n        nzComputeCharacterCount: \"nzComputeCharacterCount\",\n        nzFormatter: \"nzFormatter\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c3,\n      decls: 1,\n      vars: 0,\n      template: function NzTextareaCountComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c2);\n          i0.ɵɵprojection(0);\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTextareaCountComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-textarea-count',\n      template: ` <ng-content select=\"textarea[nz-input]\"></ng-content> `,\n      host: {\n        class: 'ant-input-textarea-show-count'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.Renderer2\n  }, {\n    type: i0.ElementRef\n  }], {\n    nzInputDirective: [{\n      type: ContentChild,\n      args: [NzInputDirective, {\n        static: true\n      }]\n    }],\n    nzMaxCharacterCount: [{\n      type: Input\n    }],\n    nzComputeCharacterCount: [{\n      type: Input\n    }],\n    nzFormatter: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzInputModule {\n  static {\n    this.ɵfac = function NzInputModule_Factory(t) {\n      return new (t || NzInputModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzInputModule,\n      imports: [NzTextareaCountComponent, NzInputDirective, NzInputGroupComponent, NzAutosizeDirective, NzInputGroupSlotComponent, NzInputGroupWhitSuffixOrPrefixDirective],\n      exports: [NzTextareaCountComponent, NzInputDirective, NzInputGroupComponent, NzAutosizeDirective, NzInputGroupWhitSuffixOrPrefixDirective]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [NzInputGroupComponent, NzInputGroupSlotComponent]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzInputModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzTextareaCountComponent, NzInputDirective, NzInputGroupComponent, NzAutosizeDirective, NzInputGroupSlotComponent, NzInputGroupWhitSuffixOrPrefixDirective],\n      exports: [NzTextareaCountComponent, NzInputDirective, NzInputGroupComponent, NzAutosizeDirective, NzInputGroupWhitSuffixOrPrefixDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzAutosizeDirective, NzInputDirective, NzInputGroupComponent, NzInputGroupSlotComponent, NzInputGroupWhitSuffixOrPrefixDirective, NzInputModule, NzTextareaCountComponent };", "map": {"version": 3, "names": ["__decorate", "Ng<PERSON><PERSON>", "NgTemplateOutlet", "i0", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Input", "Directive", "Optional", "Self", "ContentChildren", "isDevMode", "ContentChild", "NgModule", "Subject", "merge", "EMPTY", "distinctUntilChanged", "takeUntil", "filter", "startWith", "switchMap", "mergeMap", "map", "i3", "NzFormItemFeedbackIconComponent", "NzFormNoStatusService", "NzFormPatchModule", "getStatusClassNames", "InputBoolean", "isNotNil", "i2", "NzOutletModule", "i1", "NzIconModule", "i1$1", "i2$1", "i1$2", "i1$3", "i2$2", "_c0", "_c1", "NzInputGroupSlotComponent_Conditional_0_Template", "rf", "ctx", "ɵɵelement", "ctx_r0", "ɵɵnextContext", "ɵɵproperty", "icon", "NzInputGroupSlotComponent_ng_container_1_Template", "ɵɵelementContainerStart", "ɵɵtext", "ɵɵelementContainerEnd", "ɵɵadvance", "ɵɵtextInterpolate", "template", "NzInputGroupComponent_Conditional_0_Conditional_1_Template", "nzAddOnBeforeIcon", "nzAddOnBefore", "NzInputGroupComponent_Conditional_0_Conditional_2_ng_template_1_Template", "NzInputGroupComponent_Conditional_0_Conditional_2_Template", "ɵɵelementStart", "ɵɵtemplate", "ɵɵelementEnd", "affixTemplate_r2", "ɵɵreference", "ɵɵclassProp", "disabled", "isSmall", "is<PERSON>arge", "focused", "affixInGroupStatusCls", "NzInputGroupComponent_Conditional_0_Conditional_3_ng_template_0_Template", "NzInputGroupComponent_Conditional_0_Conditional_3_Template", "contentTemplate_r3", "NzInputGroupComponent_Conditional_0_Conditional_4_Template", "nzAddOnAfterIcon", "nzAddOnAfter", "NzInputGroupComponent_Conditional_0_Template", "ɵɵconditional", "isAffix", "hasFeedback", "NzInputGroupComponent_Conditional_1_Conditional_0_ng_template_0_Template", "NzInputGroupComponent_Conditional_1_Conditional_0_Template", "NzInputGroupComponent_Conditional_1_Conditional_1_ng_template_0_Template", "NzInputGroupComponent_Conditional_1_Conditional_1_Template", "NzInputGroupComponent_Conditional_1_Template", "NzInputGroupComponent_ng_template_2_Conditional_0_Template", "nzPrefixIcon", "nzPrefix", "NzInputGroupComponent_ng_template_2_ng_template_1_Template", "NzInputGroupComponent_ng_template_2_Conditional_2_Conditional_1_Template", "status", "NzInputGroupComponent_ng_template_2_Conditional_2_Template", "nzSuffixIcon", "nzSuffix", "isFeedback", "NzInputGroupComponent_ng_template_2_Template", "NzInputGroupComponent_ng_template_4_Conditional_1_Template", "NzInputGroupComponent_ng_template_4_Template", "ɵɵprojection", "isAddOn", "_c2", "_c3", "NzInputGroupSlotComponent", "constructor", "type", "ɵfac", "NzInputGroupSlotComponent_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "selectors", "hostVars", "hostBindings", "NzInputGroupSlotComponent_HostBindings", "inputs", "standalone", "features", "ɵɵStandaloneFeature", "attrs", "ngContentSelectors", "decls", "vars", "consts", "NzInputGroupSlotComponent_Template", "ɵɵprojectionDef", "dependencies", "NzIconDirective", "NzStringTemplateOutletDirective", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "preserveWhitespaces", "None", "OnPush", "host", "imports", "NzInputDirective", "ngControl", "_disabled", "value", "renderer", "elementRef", "<PERSON><PERSON><PERSON><PERSON>", "directionality", "nzFormStatusService", "nzFormNoStatusService", "nzBorderless", "nzSize", "nzStepperless", "nzStatus", "disabled$", "dir", "prefixCls", "statusCls", "feedbackRef", "components", "destroy$", "ngOnInit", "formStatusChanges", "pipe", "pre", "cur", "subscribe", "setStatusStyles", "statusChanges", "next", "change", "direction", "ngOnChanges", "changes", "ngOnDestroy", "complete", "renderFeedbackIcon", "Object", "keys", "for<PERSON>ach", "addClass", "nativeElement", "removeClass", "clear", "createComponent", "location", "classList", "add", "instance", "updateIcon", "NzInputDirective_Factory", "ɵɵdirectiveInject", "NgControl", "Renderer2", "ElementRef", "ViewContainerRef", "Directionality", "NzFormStatusService", "ɵdir", "ɵɵdefineDirective", "hostAttrs", "NzInputDirective_HostBindings", "ɵɵattribute", "exportAs", "ɵɵNgOnChangesFeature", "prototype", "class", "decorators", "NzInputGroupWhitSuffixOrPrefixDirective", "NzInputGroupWhitSuffixOrPrefixDirective_Factory", "NzInputGroupComponent", "focusMonitor", "cdr", "nzSearch", "nzCompact", "affixStatusCls", "groupStatusCls", "updateChildrenInputSize", "listOfNzInputDirective", "item", "monitor", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ngAfterContentInit", "listOfInputChange$", "list", "input", "some", "noFormStatus", "stopMonitoring", "baseAffix", "NzInputGroupComponent_Factory", "FocusMonitor", "ChangeDetectorRef", "contentQueries", "NzInputGroupComponent_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "NzInputGroupComponent_HostBindings", "ɵɵProvidersFeature", "NzInputGroupComponent_Template", "ɵɵtemplateRefExtractor", "providers", "NzAutosizeDirective", "nzAutosize", "isAutoSizeType", "data", "maxRows", "minRows", "autosize", "maxHeight", "setMaxHeight", "minHeight", "setMinHeight", "resizeToFitContent", "force", "cacheTextareaLineHeight", "cachedLineHeight", "textarea", "el", "previousMinRows", "previousValue", "placeholderText", "placeholder", "height", "Math", "round", "scrollHeight", "inputGap", "style", "remove", "requestAnimationFrame", "ngZone", "runOutsideAngular", "selectionStart", "selectionEnd", "isStopped", "document", "activeElement", "setSelectionRange", "parentNode", "textareaClone", "cloneNode", "rows", "position", "visibility", "border", "padding", "overflow", "append<PERSON><PERSON><PERSON>", "clientHeight", "<PERSON><PERSON><PERSON><PERSON>", "noopInputHandler", "platform", "resizeService", "ngAfterViewInit", "<PERSON><PERSON><PERSON><PERSON>", "ngDoCheck", "NzAutosizeDirective_Factory", "NgZone", "Platform", "NzResizeService", "NzAutosizeDirective_HostBindings", "ɵɵlistener", "NzAutosizeDirective_input_HostBindingHandler", "NzTextareaCountComponent", "nzMaxCharacterCount", "nzComputeCharacterCount", "v", "length", "nzFormatter", "c", "m", "configChange$", "nzInputDirective", "Error", "valueChanges", "setDataCount", "inputValue", "String", "currentCount", "dataCount", "setAttribute", "NzTextareaCountComponent_Factory", "NzTextareaCountComponent_ContentQueries", "first", "NzTextareaCountComponent_Template", "static", "NzInputModule", "NzInputModule_Factory", "ɵmod", "ɵɵdefineNgModule", "exports", "ɵinj", "ɵɵdefineInjector"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-input.mjs"], "sourcesContent": ["import { __decorate } from 'tslib';\nimport { NgClass, NgTemplateOutlet } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Input, Directive, Optional, Self, ContentChildren, isDevMode, ContentChild, NgModule } from '@angular/core';\nimport { Subject, merge, EMPTY } from 'rxjs';\nimport { distinctUntilChanged, takeUntil, filter, startWith, switchMap, mergeMap, map } from 'rxjs/operators';\nimport * as i3 from 'ng-zorro-antd/core/form';\nimport { NzFormItemFeedbackIconComponent, NzFormNoStatusService, NzFormPatchModule } from 'ng-zorro-antd/core/form';\nimport { getStatusClassNames, InputBoolean, isNotNil } from 'ng-zorro-antd/core/util';\nimport * as i2 from 'ng-zorro-antd/core/outlet';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport * as i1 from 'ng-zorro-antd/icon';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport * as i1$1 from '@angular/forms';\nimport * as i2$1 from '@angular/cdk/bidi';\nimport * as i1$2 from '@angular/cdk/a11y';\nimport * as i1$3 from '@angular/cdk/platform';\nimport * as i2$2 from 'ng-zorro-antd/core/services';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzInputGroupSlotComponent {\n    constructor() {\n        this.icon = null;\n        this.type = null;\n        this.template = null;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzInputGroupSlotComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"17.3.8\", type: NzInputGroupSlotComponent, isStandalone: true, selector: \"[nz-input-group-slot]\", inputs: { icon: \"icon\", type: \"type\", template: \"template\" }, host: { properties: { \"class.ant-input-group-addon\": \"type === 'addon'\", \"class.ant-input-prefix\": \"type === 'prefix'\", \"class.ant-input-suffix\": \"type === 'suffix'\" } }, ngImport: i0, template: `\n    @if (icon) {\n      <span nz-icon [nzType]=\"icon\"></span>\n    }\n    <ng-container *nzStringTemplateOutlet=\"template\">{{ template }}</ng-container>\n    <ng-content></ng-content>\n  `, isInline: true, dependencies: [{ kind: \"ngmodule\", type: NzIconModule }, { kind: \"directive\", type: i1.NzIconDirective, selector: \"[nz-icon]\", inputs: [\"nzSpin\", \"nzRotate\", \"nzType\", \"nzTheme\", \"nzTwotoneColor\", \"nzIconfont\"], exportAs: [\"nzIcon\"] }, { kind: \"ngmodule\", type: NzOutletModule }, { kind: \"directive\", type: i2.NzStringTemplateOutletDirective, selector: \"[nzStringTemplateOutlet]\", inputs: [\"nzStringTemplateOutletContext\", \"nzStringTemplateOutlet\"], exportAs: [\"nzStringTemplateOutlet\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzInputGroupSlotComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: '[nz-input-group-slot]',\n                    preserveWhitespaces: false,\n                    encapsulation: ViewEncapsulation.None,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    template: `\n    @if (icon) {\n      <span nz-icon [nzType]=\"icon\"></span>\n    }\n    <ng-container *nzStringTemplateOutlet=\"template\">{{ template }}</ng-container>\n    <ng-content></ng-content>\n  `,\n                    host: {\n                        '[class.ant-input-group-addon]': `type === 'addon'`,\n                        '[class.ant-input-prefix]': `type === 'prefix'`,\n                        '[class.ant-input-suffix]': `type === 'suffix'`\n                    },\n                    imports: [NzIconModule, NzOutletModule],\n                    standalone: true\n                }]\n        }], propDecorators: { icon: [{\n                type: Input\n            }], type: [{\n                type: Input\n            }], template: [{\n                type: Input\n            }] } });\n\nclass NzInputDirective {\n    get disabled() {\n        if (this.ngControl && this.ngControl.disabled !== null) {\n            return this.ngControl.disabled;\n        }\n        return this._disabled;\n    }\n    set disabled(value) {\n        this._disabled = value != null && `${value}` !== 'false';\n    }\n    constructor(ngControl, renderer, elementRef, hostView, directionality, nzFormStatusService, nzFormNoStatusService) {\n        this.ngControl = ngControl;\n        this.renderer = renderer;\n        this.elementRef = elementRef;\n        this.hostView = hostView;\n        this.directionality = directionality;\n        this.nzFormStatusService = nzFormStatusService;\n        this.nzFormNoStatusService = nzFormNoStatusService;\n        this.nzBorderless = false;\n        this.nzSize = 'default';\n        this.nzStepperless = true;\n        this.nzStatus = '';\n        this._disabled = false;\n        this.disabled$ = new Subject();\n        this.dir = 'ltr';\n        // status\n        this.prefixCls = 'ant-input';\n        this.status = '';\n        this.statusCls = {};\n        this.hasFeedback = false;\n        this.feedbackRef = null;\n        this.components = [];\n        this.destroy$ = new Subject();\n    }\n    ngOnInit() {\n        this.nzFormStatusService?.formStatusChanges\n            .pipe(distinctUntilChanged((pre, cur) => {\n            return pre.status === cur.status && pre.hasFeedback === cur.hasFeedback;\n        }), takeUntil(this.destroy$))\n            .subscribe(({ status, hasFeedback }) => {\n            this.setStatusStyles(status, hasFeedback);\n        });\n        if (this.ngControl) {\n            this.ngControl.statusChanges\n                ?.pipe(filter(() => this.ngControl.disabled !== null), takeUntil(this.destroy$))\n                .subscribe(() => {\n                this.disabled$.next(this.ngControl.disabled);\n            });\n        }\n        this.dir = this.directionality.value;\n        this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe((direction) => {\n            this.dir = direction;\n        });\n    }\n    ngOnChanges(changes) {\n        const { disabled, nzStatus } = changes;\n        if (disabled) {\n            this.disabled$.next(this.disabled);\n        }\n        if (nzStatus) {\n            this.setStatusStyles(this.nzStatus, this.hasFeedback);\n        }\n    }\n    ngOnDestroy() {\n        this.destroy$.next();\n        this.destroy$.complete();\n    }\n    setStatusStyles(status, hasFeedback) {\n        // set inner status\n        this.status = status;\n        this.hasFeedback = hasFeedback;\n        this.renderFeedbackIcon();\n        // render status if nzStatus is set\n        this.statusCls = getStatusClassNames(this.prefixCls, status, hasFeedback);\n        Object.keys(this.statusCls).forEach(status => {\n            if (this.statusCls[status]) {\n                this.renderer.addClass(this.elementRef.nativeElement, status);\n            }\n            else {\n                this.renderer.removeClass(this.elementRef.nativeElement, status);\n            }\n        });\n    }\n    renderFeedbackIcon() {\n        if (!this.status || !this.hasFeedback || !!this.nzFormNoStatusService) {\n            // remove feedback\n            this.hostView.clear();\n            this.feedbackRef = null;\n            return;\n        }\n        this.feedbackRef = this.feedbackRef || this.hostView.createComponent(NzFormItemFeedbackIconComponent);\n        this.feedbackRef.location.nativeElement.classList.add('ant-input-suffix');\n        this.feedbackRef.instance.status = this.status;\n        this.feedbackRef.instance.updateIcon();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzInputDirective, deps: [{ token: i1$1.NgControl, optional: true, self: true }, { token: i0.Renderer2 }, { token: i0.ElementRef }, { token: i0.ViewContainerRef }, { token: i2$1.Directionality, optional: true }, { token: i3.NzFormStatusService, optional: true }, { token: i3.NzFormNoStatusService, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzInputDirective, isStandalone: true, selector: \"input[nz-input],textarea[nz-input]\", inputs: { nzBorderless: \"nzBorderless\", nzSize: \"nzSize\", nzStepperless: \"nzStepperless\", nzStatus: \"nzStatus\", disabled: \"disabled\" }, host: { properties: { \"class.ant-input-disabled\": \"disabled\", \"class.ant-input-borderless\": \"nzBorderless\", \"class.ant-input-lg\": \"nzSize === 'large'\", \"class.ant-input-sm\": \"nzSize === 'small'\", \"attr.disabled\": \"disabled || null\", \"class.ant-input-rtl\": \"dir=== 'rtl'\", \"class.ant-input-stepperless\": \"nzStepperless\" }, classAttribute: \"ant-input\" }, exportAs: [\"nzInput\"], usesOnChanges: true, ngImport: i0 }); }\n}\n__decorate([\n    InputBoolean()\n], NzInputDirective.prototype, \"nzBorderless\", void 0);\n__decorate([\n    InputBoolean()\n], NzInputDirective.prototype, \"nzStepperless\", void 0);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzInputDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'input[nz-input],textarea[nz-input]',\n                    exportAs: 'nzInput',\n                    host: {\n                        class: 'ant-input',\n                        '[class.ant-input-disabled]': 'disabled',\n                        '[class.ant-input-borderless]': 'nzBorderless',\n                        '[class.ant-input-lg]': `nzSize === 'large'`,\n                        '[class.ant-input-sm]': `nzSize === 'small'`,\n                        '[attr.disabled]': 'disabled || null',\n                        '[class.ant-input-rtl]': `dir=== 'rtl'`,\n                        '[class.ant-input-stepperless]': `nzStepperless`\n                    },\n                    standalone: true\n                }]\n        }], ctorParameters: () => [{ type: i1$1.NgControl, decorators: [{\n                    type: Optional\n                }, {\n                    type: Self\n                }] }, { type: i0.Renderer2 }, { type: i0.ElementRef }, { type: i0.ViewContainerRef }, { type: i2$1.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i3.NzFormStatusService, decorators: [{\n                    type: Optional\n                }] }, { type: i3.NzFormNoStatusService, decorators: [{\n                    type: Optional\n                }] }], propDecorators: { nzBorderless: [{\n                type: Input\n            }], nzSize: [{\n                type: Input\n            }], nzStepperless: [{\n                type: Input\n            }], nzStatus: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }] } });\n\nclass NzInputGroupWhitSuffixOrPrefixDirective {\n    constructor(elementRef) {\n        this.elementRef = elementRef;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzInputGroupWhitSuffixOrPrefixDirective, deps: [{ token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzInputGroupWhitSuffixOrPrefixDirective, isStandalone: true, selector: \"nz-input-group[nzSuffix], nz-input-group[nzPrefix]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzInputGroupWhitSuffixOrPrefixDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `nz-input-group[nzSuffix], nz-input-group[nzPrefix]`,\n                    standalone: true\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }] });\nclass NzInputGroupComponent {\n    constructor(focusMonitor, elementRef, renderer, cdr, directionality, nzFormStatusService, nzFormNoStatusService) {\n        this.focusMonitor = focusMonitor;\n        this.elementRef = elementRef;\n        this.renderer = renderer;\n        this.cdr = cdr;\n        this.directionality = directionality;\n        this.nzFormStatusService = nzFormStatusService;\n        this.nzFormNoStatusService = nzFormNoStatusService;\n        this.nzAddOnBeforeIcon = null;\n        this.nzAddOnAfterIcon = null;\n        this.nzPrefixIcon = null;\n        this.nzSuffixIcon = null;\n        this.nzStatus = '';\n        this.nzSize = 'default';\n        this.nzSearch = false;\n        this.nzCompact = false;\n        this.isLarge = false;\n        this.isSmall = false;\n        this.isAffix = false;\n        this.isAddOn = false;\n        this.isFeedback = false;\n        this.focused = false;\n        this.disabled = false;\n        this.dir = 'ltr';\n        // status\n        this.prefixCls = 'ant-input';\n        this.affixStatusCls = {};\n        this.groupStatusCls = {};\n        this.affixInGroupStatusCls = {};\n        this.status = '';\n        this.hasFeedback = false;\n        this.destroy$ = new Subject();\n    }\n    updateChildrenInputSize() {\n        if (this.listOfNzInputDirective) {\n            this.listOfNzInputDirective.forEach(item => (item.nzSize = this.nzSize));\n        }\n    }\n    ngOnInit() {\n        this.nzFormStatusService?.formStatusChanges\n            .pipe(distinctUntilChanged((pre, cur) => {\n            return pre.status === cur.status && pre.hasFeedback === cur.hasFeedback;\n        }), takeUntil(this.destroy$))\n            .subscribe(({ status, hasFeedback }) => {\n            this.setStatusStyles(status, hasFeedback);\n        });\n        this.focusMonitor\n            .monitor(this.elementRef, true)\n            .pipe(takeUntil(this.destroy$))\n            .subscribe(focusOrigin => {\n            this.focused = !!focusOrigin;\n            this.cdr.markForCheck();\n        });\n        this.dir = this.directionality.value;\n        this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe((direction) => {\n            this.dir = direction;\n        });\n    }\n    ngAfterContentInit() {\n        this.updateChildrenInputSize();\n        const listOfInputChange$ = this.listOfNzInputDirective.changes.pipe(startWith(this.listOfNzInputDirective));\n        listOfInputChange$\n            .pipe(switchMap(list => merge(...[listOfInputChange$, ...list.map((input) => input.disabled$)])), mergeMap(() => listOfInputChange$), map(list => list.some((input) => input.disabled)), takeUntil(this.destroy$))\n            .subscribe(disabled => {\n            this.disabled = disabled;\n            this.cdr.markForCheck();\n        });\n    }\n    ngOnChanges(changes) {\n        const { nzSize, nzSuffix, nzPrefix, nzPrefixIcon, nzSuffixIcon, nzAddOnAfter, nzAddOnBefore, nzAddOnAfterIcon, nzAddOnBeforeIcon, nzStatus } = changes;\n        if (nzSize) {\n            this.updateChildrenInputSize();\n            this.isLarge = this.nzSize === 'large';\n            this.isSmall = this.nzSize === 'small';\n        }\n        if (nzSuffix || nzPrefix || nzPrefixIcon || nzSuffixIcon) {\n            this.isAffix = !!(this.nzSuffix || this.nzPrefix || this.nzPrefixIcon || this.nzSuffixIcon);\n        }\n        if (nzAddOnAfter || nzAddOnBefore || nzAddOnAfterIcon || nzAddOnBeforeIcon) {\n            this.isAddOn = !!(this.nzAddOnAfter || this.nzAddOnBefore || this.nzAddOnAfterIcon || this.nzAddOnBeforeIcon);\n            this.nzFormNoStatusService?.noFormStatus?.next(this.isAddOn);\n        }\n        if (nzStatus) {\n            this.setStatusStyles(this.nzStatus, this.hasFeedback);\n        }\n    }\n    ngOnDestroy() {\n        this.focusMonitor.stopMonitoring(this.elementRef);\n        this.destroy$.next();\n        this.destroy$.complete();\n    }\n    setStatusStyles(status, hasFeedback) {\n        // set inner status\n        this.status = status;\n        this.hasFeedback = hasFeedback;\n        this.isFeedback = !!status && hasFeedback;\n        const baseAffix = !!(this.nzSuffix || this.nzPrefix || this.nzPrefixIcon || this.nzSuffixIcon);\n        this.isAffix = baseAffix || (!this.isAddOn && hasFeedback);\n        this.affixInGroupStatusCls =\n            this.isAffix || this.isFeedback\n                ? (this.affixStatusCls = getStatusClassNames(`${this.prefixCls}-affix-wrapper`, status, hasFeedback))\n                : {};\n        this.cdr.markForCheck();\n        // render status if nzStatus is set\n        this.affixStatusCls = getStatusClassNames(`${this.prefixCls}-affix-wrapper`, this.isAddOn ? '' : status, this.isAddOn ? false : hasFeedback);\n        this.groupStatusCls = getStatusClassNames(`${this.prefixCls}-group-wrapper`, this.isAddOn ? status : '', this.isAddOn ? hasFeedback : false);\n        const statusCls = {\n            ...this.affixStatusCls,\n            ...this.groupStatusCls\n        };\n        Object.keys(statusCls).forEach(status => {\n            if (statusCls[status]) {\n                this.renderer.addClass(this.elementRef.nativeElement, status);\n            }\n            else {\n                this.renderer.removeClass(this.elementRef.nativeElement, status);\n            }\n        });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzInputGroupComponent, deps: [{ token: i1$2.FocusMonitor }, { token: i0.ElementRef }, { token: i0.Renderer2 }, { token: i0.ChangeDetectorRef }, { token: i2$1.Directionality, optional: true }, { token: i3.NzFormStatusService, optional: true }, { token: i3.NzFormNoStatusService, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"17.3.8\", type: NzInputGroupComponent, isStandalone: true, selector: \"nz-input-group\", inputs: { nzAddOnBeforeIcon: \"nzAddOnBeforeIcon\", nzAddOnAfterIcon: \"nzAddOnAfterIcon\", nzPrefixIcon: \"nzPrefixIcon\", nzSuffixIcon: \"nzSuffixIcon\", nzAddOnBefore: \"nzAddOnBefore\", nzAddOnAfter: \"nzAddOnAfter\", nzPrefix: \"nzPrefix\", nzStatus: \"nzStatus\", nzSuffix: \"nzSuffix\", nzSize: \"nzSize\", nzSearch: \"nzSearch\", nzCompact: \"nzCompact\" }, host: { properties: { \"class.ant-input-group-compact\": \"nzCompact\", \"class.ant-input-search-enter-button\": \"nzSearch\", \"class.ant-input-search\": \"nzSearch\", \"class.ant-input-search-rtl\": \"dir === 'rtl'\", \"class.ant-input-search-sm\": \"nzSearch && isSmall\", \"class.ant-input-search-large\": \"nzSearch && isLarge\", \"class.ant-input-group-wrapper\": \"isAddOn\", \"class.ant-input-group-wrapper-rtl\": \"dir === 'rtl'\", \"class.ant-input-group-wrapper-lg\": \"isAddOn && isLarge\", \"class.ant-input-group-wrapper-sm\": \"isAddOn && isSmall\", \"class.ant-input-affix-wrapper\": \"isAffix && !isAddOn\", \"class.ant-input-affix-wrapper-rtl\": \"dir === 'rtl'\", \"class.ant-input-affix-wrapper-focused\": \"isAffix && focused\", \"class.ant-input-affix-wrapper-disabled\": \"isAffix && disabled\", \"class.ant-input-affix-wrapper-lg\": \"isAffix && !isAddOn && isLarge\", \"class.ant-input-affix-wrapper-sm\": \"isAffix && !isAddOn && isSmall\", \"class.ant-input-group\": \"!isAffix && !isAddOn\", \"class.ant-input-group-rtl\": \"dir === 'rtl'\", \"class.ant-input-group-lg\": \"!isAffix && !isAddOn && isLarge\", \"class.ant-input-group-sm\": \"!isAffix && !isAddOn && isSmall\" } }, providers: [NzFormNoStatusService], queries: [{ propertyName: \"listOfNzInputDirective\", predicate: NzInputDirective }], exportAs: [\"nzInputGroup\"], usesOnChanges: true, ngImport: i0, template: `\n    @if (isAddOn) {\n      <span class=\"ant-input-wrapper ant-input-group\">\n        @if (nzAddOnBefore || nzAddOnBeforeIcon) {\n          <span nz-input-group-slot type=\"addon\" [icon]=\"nzAddOnBeforeIcon\" [template]=\"nzAddOnBefore\"></span>\n        }\n\n        @if (isAffix || hasFeedback) {\n          <span\n            class=\"ant-input-affix-wrapper\"\n            [class.ant-input-affix-wrapper-disabled]=\"disabled\"\n            [class.ant-input-affix-wrapper-sm]=\"isSmall\"\n            [class.ant-input-affix-wrapper-lg]=\"isLarge\"\n            [class.ant-input-affix-wrapper-focused]=\"focused\"\n            [ngClass]=\"affixInGroupStatusCls\"\n          >\n            <ng-template [ngTemplateOutlet]=\"affixTemplate\"></ng-template>\n          </span>\n        } @else {\n          <ng-template [ngTemplateOutlet]=\"contentTemplate\" />\n        }\n        @if (nzAddOnAfter || nzAddOnAfterIcon) {\n          <span nz-input-group-slot type=\"addon\" [icon]=\"nzAddOnAfterIcon\" [template]=\"nzAddOnAfter\"></span>\n        }\n      </span>\n    } @else {\n      @if (isAffix) {\n        <ng-template [ngTemplateOutlet]=\"affixTemplate\" />\n      } @else {\n        <ng-template [ngTemplateOutlet]=\"contentTemplate\" />\n      }\n    }\n\n    <!-- affix template -->\n    <ng-template #affixTemplate>\n      @if (nzPrefix || nzPrefixIcon) {\n        <span nz-input-group-slot type=\"prefix\" [icon]=\"nzPrefixIcon\" [template]=\"nzPrefix\"></span>\n      }\n      <ng-template [ngTemplateOutlet]=\"contentTemplate\" />\n      @if (nzSuffix || nzSuffixIcon || isFeedback) {\n        <span nz-input-group-slot type=\"suffix\" [icon]=\"nzSuffixIcon\" [template]=\"nzSuffix\">\n          @if (isFeedback) {\n            <nz-form-item-feedback-icon [status]=\"status\" />\n          }\n        </span>\n      }\n    </ng-template>\n\n    <!-- content template -->\n    <ng-template #contentTemplate>\n      <ng-content></ng-content>\n      @if (!isAddOn && !isAffix && isFeedback) {\n        <span nz-input-group-slot type=\"suffix\">\n          <nz-form-item-feedback-icon [status]=\"status\" />\n        </span>\n      }\n    </ng-template>\n  `, isInline: true, dependencies: [{ kind: \"component\", type: NzInputGroupSlotComponent, selector: \"[nz-input-group-slot]\", inputs: [\"icon\", \"type\", \"template\"] }, { kind: \"directive\", type: NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"ngmodule\", type: NzFormPatchModule }, { kind: \"component\", type: i3.NzFormItemFeedbackIconComponent, selector: \"nz-form-item-feedback-icon\", inputs: [\"status\"], exportAs: [\"nzFormFeedbackIcon\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\n__decorate([\n    InputBoolean()\n], NzInputGroupComponent.prototype, \"nzSearch\", void 0);\n__decorate([\n    InputBoolean()\n], NzInputGroupComponent.prototype, \"nzCompact\", void 0);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzInputGroupComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'nz-input-group',\n                    exportAs: 'nzInputGroup',\n                    preserveWhitespaces: false,\n                    encapsulation: ViewEncapsulation.None,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    providers: [NzFormNoStatusService],\n                    template: `\n    @if (isAddOn) {\n      <span class=\"ant-input-wrapper ant-input-group\">\n        @if (nzAddOnBefore || nzAddOnBeforeIcon) {\n          <span nz-input-group-slot type=\"addon\" [icon]=\"nzAddOnBeforeIcon\" [template]=\"nzAddOnBefore\"></span>\n        }\n\n        @if (isAffix || hasFeedback) {\n          <span\n            class=\"ant-input-affix-wrapper\"\n            [class.ant-input-affix-wrapper-disabled]=\"disabled\"\n            [class.ant-input-affix-wrapper-sm]=\"isSmall\"\n            [class.ant-input-affix-wrapper-lg]=\"isLarge\"\n            [class.ant-input-affix-wrapper-focused]=\"focused\"\n            [ngClass]=\"affixInGroupStatusCls\"\n          >\n            <ng-template [ngTemplateOutlet]=\"affixTemplate\"></ng-template>\n          </span>\n        } @else {\n          <ng-template [ngTemplateOutlet]=\"contentTemplate\" />\n        }\n        @if (nzAddOnAfter || nzAddOnAfterIcon) {\n          <span nz-input-group-slot type=\"addon\" [icon]=\"nzAddOnAfterIcon\" [template]=\"nzAddOnAfter\"></span>\n        }\n      </span>\n    } @else {\n      @if (isAffix) {\n        <ng-template [ngTemplateOutlet]=\"affixTemplate\" />\n      } @else {\n        <ng-template [ngTemplateOutlet]=\"contentTemplate\" />\n      }\n    }\n\n    <!-- affix template -->\n    <ng-template #affixTemplate>\n      @if (nzPrefix || nzPrefixIcon) {\n        <span nz-input-group-slot type=\"prefix\" [icon]=\"nzPrefixIcon\" [template]=\"nzPrefix\"></span>\n      }\n      <ng-template [ngTemplateOutlet]=\"contentTemplate\" />\n      @if (nzSuffix || nzSuffixIcon || isFeedback) {\n        <span nz-input-group-slot type=\"suffix\" [icon]=\"nzSuffixIcon\" [template]=\"nzSuffix\">\n          @if (isFeedback) {\n            <nz-form-item-feedback-icon [status]=\"status\" />\n          }\n        </span>\n      }\n    </ng-template>\n\n    <!-- content template -->\n    <ng-template #contentTemplate>\n      <ng-content></ng-content>\n      @if (!isAddOn && !isAffix && isFeedback) {\n        <span nz-input-group-slot type=\"suffix\">\n          <nz-form-item-feedback-icon [status]=\"status\" />\n        </span>\n      }\n    </ng-template>\n  `,\n                    host: {\n                        '[class.ant-input-group-compact]': `nzCompact`,\n                        '[class.ant-input-search-enter-button]': `nzSearch`,\n                        '[class.ant-input-search]': `nzSearch`,\n                        '[class.ant-input-search-rtl]': `dir === 'rtl'`,\n                        '[class.ant-input-search-sm]': `nzSearch && isSmall`,\n                        '[class.ant-input-search-large]': `nzSearch && isLarge`,\n                        '[class.ant-input-group-wrapper]': `isAddOn`,\n                        '[class.ant-input-group-wrapper-rtl]': `dir === 'rtl'`,\n                        '[class.ant-input-group-wrapper-lg]': `isAddOn && isLarge`,\n                        '[class.ant-input-group-wrapper-sm]': `isAddOn && isSmall`,\n                        '[class.ant-input-affix-wrapper]': `isAffix && !isAddOn`,\n                        '[class.ant-input-affix-wrapper-rtl]': `dir === 'rtl'`,\n                        '[class.ant-input-affix-wrapper-focused]': `isAffix && focused`,\n                        '[class.ant-input-affix-wrapper-disabled]': `isAffix && disabled`,\n                        '[class.ant-input-affix-wrapper-lg]': `isAffix && !isAddOn && isLarge`,\n                        '[class.ant-input-affix-wrapper-sm]': `isAffix && !isAddOn && isSmall`,\n                        '[class.ant-input-group]': `!isAffix && !isAddOn`,\n                        '[class.ant-input-group-rtl]': `dir === 'rtl'`,\n                        '[class.ant-input-group-lg]': `!isAffix && !isAddOn && isLarge`,\n                        '[class.ant-input-group-sm]': `!isAffix && !isAddOn && isSmall`\n                    },\n                    imports: [NzInputGroupSlotComponent, NgClass, NgTemplateOutlet, NzFormPatchModule],\n                    standalone: true\n                }]\n        }], ctorParameters: () => [{ type: i1$2.FocusMonitor }, { type: i0.ElementRef }, { type: i0.Renderer2 }, { type: i0.ChangeDetectorRef }, { type: i2$1.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i3.NzFormStatusService, decorators: [{\n                    type: Optional\n                }] }, { type: i3.NzFormNoStatusService, decorators: [{\n                    type: Optional\n                }] }], propDecorators: { listOfNzInputDirective: [{\n                type: ContentChildren,\n                args: [NzInputDirective]\n            }], nzAddOnBeforeIcon: [{\n                type: Input\n            }], nzAddOnAfterIcon: [{\n                type: Input\n            }], nzPrefixIcon: [{\n                type: Input\n            }], nzSuffixIcon: [{\n                type: Input\n            }], nzAddOnBefore: [{\n                type: Input\n            }], nzAddOnAfter: [{\n                type: Input\n            }], nzPrefix: [{\n                type: Input\n            }], nzStatus: [{\n                type: Input\n            }], nzSuffix: [{\n                type: Input\n            }], nzSize: [{\n                type: Input\n            }], nzSearch: [{\n                type: Input\n            }], nzCompact: [{\n                type: Input\n            }] } });\n\nclass NzAutosizeDirective {\n    set nzAutosize(value) {\n        const isAutoSizeType = (data) => typeof data !== 'string' && typeof data !== 'boolean' && (!!data.maxRows || !!data.minRows);\n        if (typeof value === 'string' || value === true) {\n            this.autosize = true;\n        }\n        else if (isAutoSizeType(value)) {\n            this.autosize = true;\n            this.minRows = value.minRows;\n            this.maxRows = value.maxRows;\n            this.maxHeight = this.setMaxHeight();\n            this.minHeight = this.setMinHeight();\n        }\n    }\n    resizeToFitContent(force = false) {\n        this.cacheTextareaLineHeight();\n        // If we haven't determined the line-height yet, we know we're still hidden and there's no point\n        // in checking the height of the textarea.\n        if (!this.cachedLineHeight) {\n            return;\n        }\n        const textarea = this.el;\n        const value = textarea.value;\n        // Only resize if the value or minRows have changed since these calculations can be expensive.\n        if (!force && this.minRows === this.previousMinRows && value === this.previousValue) {\n            return;\n        }\n        const placeholderText = textarea.placeholder;\n        // Reset the textarea height to auto in order to shrink back to its default size.\n        // Also temporarily force overflow:hidden, so scroll bars do not interfere with calculations.\n        // Long placeholders that are wider than the textarea width may lead to a bigger scrollHeight\n        // value. To ensure that the scrollHeight is not bigger than the content, the placeholders\n        // need to be removed temporarily.\n        textarea.classList.add('nz-textarea-autosize-measuring');\n        textarea.placeholder = '';\n        let height = Math.round((textarea.scrollHeight - this.inputGap) / this.cachedLineHeight) * this.cachedLineHeight +\n            this.inputGap;\n        if (this.maxHeight !== null && height > this.maxHeight) {\n            height = this.maxHeight;\n        }\n        if (this.minHeight !== null && height < this.minHeight) {\n            height = this.minHeight;\n        }\n        // Use the scrollHeight to know how large the textarea *would* be if fit its entire value.\n        textarea.style.height = `${height}px`;\n        textarea.classList.remove('nz-textarea-autosize-measuring');\n        textarea.placeholder = placeholderText;\n        // On Firefox resizing the textarea will prevent it from scrolling to the caret position.\n        // We need to re-set the selection in order for it to scroll to the proper position.\n        if (typeof requestAnimationFrame !== 'undefined') {\n            this.ngZone.runOutsideAngular(() => requestAnimationFrame(() => {\n                const { selectionStart, selectionEnd } = textarea;\n                // IE will throw an \"Unspecified error\" if we try to set the selection range after the\n                // element has been removed from the DOM. Assert that the directive hasn't been destroyed\n                // between the time we requested the animation frame and when it was executed.\n                // Also note that we have to assert that the textarea is focused before we set the\n                // selection range. Setting the selection range on a non-focused textarea will cause\n                // it to receive focus on IE and Edge.\n                if (!this.destroy$.isStopped && document.activeElement === textarea) {\n                    textarea.setSelectionRange(selectionStart, selectionEnd);\n                }\n            }));\n        }\n        this.previousValue = value;\n        this.previousMinRows = this.minRows;\n    }\n    cacheTextareaLineHeight() {\n        if (this.cachedLineHeight >= 0 || !this.el.parentNode) {\n            return;\n        }\n        // Use a clone element because we have to override some styles.\n        const textareaClone = this.el.cloneNode(false);\n        textareaClone.rows = 1;\n        // Use `position: absolute` so that this doesn't cause a browser layout and use\n        // `visibility: hidden` so that nothing is rendered. Clear any other styles that\n        // would affect the height.\n        textareaClone.style.position = 'absolute';\n        textareaClone.style.visibility = 'hidden';\n        textareaClone.style.border = 'none';\n        textareaClone.style.padding = '0';\n        textareaClone.style.height = '';\n        textareaClone.style.minHeight = '';\n        textareaClone.style.maxHeight = '';\n        // In Firefox it happens that textarea elements are always bigger than the specified amount\n        // of rows. This is because Firefox tries to add extra space for the horizontal scrollbar.\n        // As a workaround that removes the extra space for the scrollbar, we can just set overflow\n        // to hidden. This ensures that there is no invalid calculation of the line height.\n        // See Firefox bug report: https://bugzilla.mozilla.org/show_bug.cgi?id=33654\n        textareaClone.style.overflow = 'hidden';\n        this.el.parentNode.appendChild(textareaClone);\n        this.cachedLineHeight = textareaClone.clientHeight - this.inputGap;\n        this.el.parentNode.removeChild(textareaClone);\n        // Min and max heights have to be re-calculated if the cached line height changes\n        this.maxHeight = this.setMaxHeight();\n        this.minHeight = this.setMinHeight();\n    }\n    setMinHeight() {\n        const minHeight = this.minRows && this.cachedLineHeight ? this.minRows * this.cachedLineHeight + this.inputGap : null;\n        if (minHeight !== null) {\n            this.el.style.minHeight = `${minHeight}px`;\n        }\n        return minHeight;\n    }\n    setMaxHeight() {\n        const maxHeight = this.maxRows && this.cachedLineHeight ? this.maxRows * this.cachedLineHeight + this.inputGap : null;\n        if (maxHeight !== null) {\n            this.el.style.maxHeight = `${maxHeight}px`;\n        }\n        return maxHeight;\n    }\n    noopInputHandler() {\n        // no-op handler that ensures we're running change detection on input events.\n    }\n    constructor(elementRef, ngZone, platform, resizeService) {\n        this.elementRef = elementRef;\n        this.ngZone = ngZone;\n        this.platform = platform;\n        this.resizeService = resizeService;\n        this.autosize = false;\n        this.el = this.elementRef.nativeElement;\n        this.maxHeight = null;\n        this.minHeight = null;\n        this.destroy$ = new Subject();\n        this.inputGap = 10;\n    }\n    ngAfterViewInit() {\n        if (this.autosize && this.platform.isBrowser) {\n            this.resizeToFitContent();\n            this.resizeService\n                .subscribe()\n                .pipe(takeUntil(this.destroy$))\n                .subscribe(() => this.resizeToFitContent(true));\n        }\n    }\n    ngOnDestroy() {\n        this.destroy$.next(true);\n        this.destroy$.complete();\n    }\n    ngDoCheck() {\n        if (this.autosize && this.platform.isBrowser) {\n            this.resizeToFitContent();\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzAutosizeDirective, deps: [{ token: i0.ElementRef }, { token: i0.NgZone }, { token: i1$3.Platform }, { token: i2$2.NzResizeService }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzAutosizeDirective, isStandalone: true, selector: \"textarea[nzAutosize]\", inputs: { nzAutosize: \"nzAutosize\" }, host: { attributes: { \"rows\": \"1\" }, listeners: { \"input\": \"noopInputHandler()\" } }, exportAs: [\"nzAutosize\"], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzAutosizeDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'textarea[nzAutosize]',\n                    exportAs: 'nzAutosize',\n                    host: {\n                        // Textarea elements that have the directive applied should have a single row by default.\n                        // Browsers normally show two rows by default and therefore this limits the minRows binding.\n                        rows: '1',\n                        '(input)': 'noopInputHandler()'\n                    },\n                    standalone: true\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: i0.NgZone }, { type: i1$3.Platform }, { type: i2$2.NzResizeService }], propDecorators: { nzAutosize: [{\n                type: Input\n            }] } });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTextareaCountComponent {\n    constructor(renderer, elementRef) {\n        this.renderer = renderer;\n        this.elementRef = elementRef;\n        this.nzMaxCharacterCount = 0;\n        this.nzComputeCharacterCount = v => v.length;\n        this.nzFormatter = (c, m) => `${c}${m > 0 ? `/${m}` : ``}`;\n        this.configChange$ = new Subject();\n        this.destroy$ = new Subject();\n    }\n    ngAfterContentInit() {\n        if (!this.nzInputDirective && isDevMode()) {\n            throw new Error('[nz-textarea-count]: Could not find matching textarea[nz-input] child.');\n        }\n        if (this.nzInputDirective.ngControl) {\n            const valueChanges = this.nzInputDirective.ngControl.valueChanges || EMPTY;\n            merge(valueChanges, this.configChange$)\n                .pipe(takeUntil(this.destroy$), map(() => this.nzInputDirective.ngControl.value), startWith(this.nzInputDirective.ngControl.value))\n                .subscribe(value => {\n                this.setDataCount(value);\n            });\n        }\n    }\n    setDataCount(value) {\n        const inputValue = isNotNil(value) ? String(value) : '';\n        const currentCount = this.nzComputeCharacterCount(inputValue);\n        const dataCount = this.nzFormatter(currentCount, this.nzMaxCharacterCount);\n        this.renderer.setAttribute(this.elementRef.nativeElement, 'data-count', dataCount);\n    }\n    ngOnDestroy() {\n        this.configChange$.complete();\n        this.destroy$.next(true);\n        this.destroy$.complete();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzTextareaCountComponent, deps: [{ token: i0.Renderer2 }, { token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzTextareaCountComponent, isStandalone: true, selector: \"nz-textarea-count\", inputs: { nzMaxCharacterCount: \"nzMaxCharacterCount\", nzComputeCharacterCount: \"nzComputeCharacterCount\", nzFormatter: \"nzFormatter\" }, host: { classAttribute: \"ant-input-textarea-show-count\" }, queries: [{ propertyName: \"nzInputDirective\", first: true, predicate: NzInputDirective, descendants: true, static: true }], ngImport: i0, template: ` <ng-content select=\"textarea[nz-input]\"></ng-content> `, isInline: true, changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzTextareaCountComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'nz-textarea-count',\n                    template: ` <ng-content select=\"textarea[nz-input]\"></ng-content> `,\n                    host: {\n                        class: 'ant-input-textarea-show-count'\n                    },\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    standalone: true\n                }]\n        }], ctorParameters: () => [{ type: i0.Renderer2 }, { type: i0.ElementRef }], propDecorators: { nzInputDirective: [{\n                type: ContentChild,\n                args: [NzInputDirective, { static: true }]\n            }], nzMaxCharacterCount: [{\n                type: Input\n            }], nzComputeCharacterCount: [{\n                type: Input\n            }], nzFormatter: [{\n                type: Input\n            }] } });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzInputModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzInputModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.3.8\", ngImport: i0, type: NzInputModule, imports: [NzTextareaCountComponent,\n            NzInputDirective,\n            NzInputGroupComponent,\n            NzAutosizeDirective,\n            NzInputGroupSlotComponent,\n            NzInputGroupWhitSuffixOrPrefixDirective], exports: [NzTextareaCountComponent,\n            NzInputDirective,\n            NzInputGroupComponent,\n            NzAutosizeDirective,\n            NzInputGroupWhitSuffixOrPrefixDirective] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzInputModule, imports: [NzInputGroupComponent,\n            NzInputGroupSlotComponent] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzInputModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        NzTextareaCountComponent,\n                        NzInputDirective,\n                        NzInputGroupComponent,\n                        NzAutosizeDirective,\n                        NzInputGroupSlotComponent,\n                        NzInputGroupWhitSuffixOrPrefixDirective\n                    ],\n                    exports: [\n                        NzTextareaCountComponent,\n                        NzInputDirective,\n                        NzInputGroupComponent,\n                        NzAutosizeDirective,\n                        NzInputGroupWhitSuffixOrPrefixDirective\n                    ]\n                }]\n        }] });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzAutosizeDirective, NzInputDirective, NzInputGroupComponent, NzInputGroupSlotComponent, NzInputGroupWhitSuffixOrPrefixDirective, NzInputModule, NzTextareaCountComponent };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,OAAO,EAAEC,gBAAgB,QAAQ,iBAAiB;AAC3D,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,eAAe,EAAEC,SAAS,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,eAAe;AAC3K,SAASC,OAAO,EAAEC,KAAK,EAAEC,KAAK,QAAQ,MAAM;AAC5C,SAASC,oBAAoB,EAAEC,SAAS,EAAEC,MAAM,EAAEC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,GAAG,QAAQ,gBAAgB;AAC7G,OAAO,KAAKC,EAAE,MAAM,yBAAyB;AAC7C,SAASC,+BAA+B,EAAEC,qBAAqB,EAAEC,iBAAiB,QAAQ,yBAAyB;AACnH,SAASC,mBAAmB,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,yBAAyB;AACrF,OAAO,KAAKC,EAAE,MAAM,2BAA2B;AAC/C,SAASC,cAAc,QAAQ,2BAA2B;AAC1D,OAAO,KAAKC,EAAE,MAAM,oBAAoB;AACxC,SAASC,YAAY,QAAQ,oBAAoB;AACjD,OAAO,KAAKC,IAAI,MAAM,gBAAgB;AACtC,OAAO,KAAKC,IAAI,MAAM,mBAAmB;AACzC,OAAO,KAAKC,IAAI,MAAM,mBAAmB;AACzC,OAAO,KAAKC,IAAI,MAAM,uBAAuB;AAC7C,OAAO,KAAKC,IAAI,MAAM,6BAA6B;;AAEnD;AACA;AACA;AACA;AAHA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,iDAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAUoGzC,EAAE,CAAA2C,SAAA,aAG5D,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,MAAA,GAHyD5C,EAAE,CAAA6C,aAAA;IAAF7C,EAAE,CAAA8C,UAAA,WAAAF,MAAA,CAAAG,IAGpE,CAAC;EAAA;AAAA;AAAA,SAAAC,kDAAAP,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAHiEzC,EAAE,CAAAiD,uBAAA,EAKlD,CAAC;IAL+CjD,EAAE,CAAAkD,MAAA,EAKpC,CAAC;IALiClD,EAAE,CAAAmD,qBAAA;EAAA;EAAA,IAAAV,EAAA;IAAA,MAAAG,MAAA,GAAF5C,EAAE,CAAA6C,aAAA;IAAF7C,EAAE,CAAAoD,SAAA,CAKpC,CAAC;IALiCpD,EAAE,CAAAqD,iBAAA,CAAAT,MAAA,CAAAU,QAKpC,CAAC;EAAA;AAAA;AAAA,SAAAC,2DAAAd,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IALiCzC,EAAE,CAAA2C,SAAA,aAiUO,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,MAAA,GAjUV5C,EAAE,CAAA6C,aAAA;IAAF7C,EAAE,CAAA8C,UAAA,SAAAF,MAAA,CAAAY,iBAiU5B,CAAC,aAAAZ,MAAA,CAAAa,aAA0B,CAAC;EAAA;AAAA;AAAA,SAAAC,yEAAAjB,EAAA,EAAAC,GAAA;AAAA,SAAAiB,2DAAAlB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAjUFzC,EAAE,CAAA4D,cAAA,aA4U5F,CAAC;IA5UyF5D,EAAE,CAAA6D,UAAA,IAAAH,wEAAA,wBA6U3C,CAAC;IA7UwC1D,EAAE,CAAA8D,YAAA,CA8UtF,CAAC;EAAA;EAAA,IAAArB,EAAA;IAAA,MAAAG,MAAA,GA9UmF5C,EAAE,CAAA6C,aAAA;IAAA,MAAAkB,gBAAA,GAAF/D,EAAE,CAAAgE,WAAA;IAAFhE,EAAE,CAAAiE,WAAA,qCAAArB,MAAA,CAAAsB,QAuUxC,CAAC,+BAAAtB,MAAA,CAAAuB,OACR,CAAC,+BAAAvB,MAAA,CAAAwB,OACD,CAAC,oCAAAxB,MAAA,CAAAyB,OACI,CAAC;IA1UuCrE,EAAE,CAAA8C,UAAA,YAAAF,MAAA,CAAA0B,qBA2U1D,CAAC;IA3UuDtE,EAAE,CAAAoD,SAAA,CA6U5C,CAAC;IA7UyCpD,EAAE,CAAA8C,UAAA,qBAAAiB,gBA6U5C,CAAC;EAAA;AAAA;AAAA,SAAAQ,yEAAA9B,EAAA,EAAAC,GAAA;AAAA,SAAA8B,2DAAA/B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7UyCzC,EAAE,CAAA6D,UAAA,IAAAU,wEAAA,wBAgVzC,CAAC;EAAA;EAAA,IAAA9B,EAAA;IAhVsCzC,EAAE,CAAA6C,aAAA;IAAA,MAAA4B,kBAAA,GAAFzE,EAAE,CAAAgE,WAAA;IAAFhE,EAAE,CAAA8C,UAAA,qBAAA2B,kBAgV5C,CAAC;EAAA;AAAA;AAAA,SAAAC,2DAAAjC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAhVyCzC,EAAE,CAAA2C,SAAA,aAmVK,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,MAAA,GAnVR5C,EAAE,CAAA6C,aAAA;IAAF7C,EAAE,CAAA8C,UAAA,SAAAF,MAAA,CAAA+B,gBAmV7B,CAAC,aAAA/B,MAAA,CAAAgC,YAAyB,CAAC;EAAA;AAAA;AAAA,SAAAC,6CAAApC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAnVAzC,EAAE,CAAA4D,cAAA,aA+TjD,CAAC;IA/T8C5D,EAAE,CAAA6D,UAAA,IAAAN,0DAAA,iBAgUrD,CAAC,IAAAI,0DAAA,kBAIb,CAAC,IAAAa,0DAAA,MAWtB,CAAC,IAAAE,0DAAA,iBAG8B,CAAC;IAlVoD1E,EAAE,CAAA8D,YAAA,CAqV1F,CAAC;EAAA;EAAA,IAAArB,EAAA;IAAA,MAAAG,MAAA,GArVuF5C,EAAE,CAAA6C,aAAA;IAAF7C,EAAE,CAAAoD,SAAA,CAkU9F,CAAC;IAlU2FpD,EAAE,CAAA8E,aAAA,IAAAlC,MAAA,CAAAa,aAAA,IAAAb,MAAA,CAAAY,iBAAA,SAkU9F,CAAC;IAlU2FxD,EAAE,CAAAoD,SAAA,CAiV9F,CAAC;IAjV2FpD,EAAE,CAAA8E,aAAA,IAAAlC,MAAA,CAAAmC,OAAA,IAAAnC,MAAA,CAAAoC,WAAA,QAiV9F,CAAC;IAjV2FhF,EAAE,CAAAoD,SAAA,EAoV9F,CAAC;IApV2FpD,EAAE,CAAA8E,aAAA,IAAAlC,MAAA,CAAAgC,YAAA,IAAAhC,MAAA,CAAA+B,gBAAA,SAoV9F,CAAC;EAAA;AAAA;AAAA,SAAAM,yEAAAxC,EAAA,EAAAC,GAAA;AAAA,SAAAwC,2DAAAzC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IApV2FzC,EAAE,CAAA6D,UAAA,IAAAoB,wEAAA,wBAwV7C,CAAC;EAAA;EAAA,IAAAxC,EAAA;IAxV0CzC,EAAE,CAAA6C,aAAA;IAAA,MAAAkB,gBAAA,GAAF/D,EAAE,CAAAgE,WAAA;IAAFhE,EAAE,CAAA8C,UAAA,qBAAAiB,gBAwVhD,CAAC;EAAA;AAAA;AAAA,SAAAoB,yEAAA1C,EAAA,EAAAC,GAAA;AAAA,SAAA0C,2DAAA3C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAxV6CzC,EAAE,CAAA6D,UAAA,IAAAsB,wEAAA,wBA0V3C,CAAC;EAAA;EAAA,IAAA1C,EAAA;IA1VwCzC,EAAE,CAAA6C,aAAA;IAAA,MAAA4B,kBAAA,GAAFzE,EAAE,CAAAgE,WAAA;IAAFhE,EAAE,CAAA8C,UAAA,qBAAA2B,kBA0V9C,CAAC;EAAA;AAAA;AAAA,SAAAY,6CAAA5C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA1V2CzC,EAAE,CAAA6D,UAAA,IAAAqB,0DAAA,eAuVlF,CAAC,IAAAE,0DAAA,MAEP,CAAC;EAAA;EAAA,IAAA3C,EAAA;IAAA,MAAAG,MAAA,GAzVqF5C,EAAE,CAAA6C,aAAA;IAAF7C,EAAE,CAAA8E,aAAA,IAAAlC,MAAA,CAAAmC,OAAA,QA2VhG,CAAC;EAAA;AAAA;AAAA,SAAAO,2DAAA7C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA3V6FzC,EAAE,CAAA2C,SAAA,aAiWJ,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,MAAA,GAjWC5C,EAAE,CAAA6C,aAAA;IAAF7C,EAAE,CAAA8C,UAAA,SAAAF,MAAA,CAAA2C,YAiWlC,CAAC,aAAA3C,MAAA,CAAA4C,QAAqB,CAAC;EAAA;AAAA;AAAA,SAAAC,2DAAAhD,EAAA,EAAAC,GAAA;AAAA,SAAAgD,yEAAAjD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAjWSzC,EAAE,CAAA2C,SAAA,mCAuW3C,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,MAAA,GAvWwC5C,EAAE,CAAA6C,aAAA;IAAF7C,EAAE,CAAA8C,UAAA,WAAAF,MAAA,CAAA+C,MAuW9C,CAAC;EAAA;AAAA;AAAA,SAAAC,2DAAAnD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAvW2CzC,EAAE,CAAA4D,cAAA,aAqWX,CAAC;IArWQ5D,EAAE,CAAA6D,UAAA,IAAA6B,wEAAA,uCAsW3E,CAAC;IAtWwE1F,EAAE,CAAA8D,YAAA,CAyWxF,CAAC;EAAA;EAAA,IAAArB,EAAA;IAAA,MAAAG,MAAA,GAzWqF5C,EAAE,CAAA6C,aAAA;IAAF7C,EAAE,CAAA8C,UAAA,SAAAF,MAAA,CAAAiD,YAqWlC,CAAC,aAAAjD,MAAA,CAAAkD,QAAqB,CAAC;IArWS9F,EAAE,CAAAoD,SAAA,CAwW5F,CAAC;IAxWyFpD,EAAE,CAAA8E,aAAA,IAAAlC,MAAA,CAAAmD,UAAA,SAwW5F,CAAC;EAAA;AAAA;AAAA,SAAAC,6CAAAvD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAxWyFzC,EAAE,CAAA6D,UAAA,IAAAyB,0DAAA,iBAgWjE,CAAC,IAAAG,0DAAA,wBAGmB,CAAC,IAAAG,0DAAA,iBACP,CAAC;EAAA;EAAA,IAAAnD,EAAA;IAAA,MAAAG,MAAA,GApWgD5C,EAAE,CAAA6C,aAAA;IAAA,MAAA4B,kBAAA,GAAFzE,EAAE,CAAAgE,WAAA;IAAFhE,EAAE,CAAA8E,aAAA,IAAAlC,MAAA,CAAA4C,QAAA,IAAA5C,MAAA,CAAA2C,YAAA,SAkWhG,CAAC;IAlW6FvF,EAAE,CAAAoD,SAAA,CAmWhD,CAAC;IAnW6CpD,EAAE,CAAA8C,UAAA,qBAAA2B,kBAmWhD,CAAC;IAnW6CzE,EAAE,CAAAoD,SAAA,CA0WhG,CAAC;IA1W6FpD,EAAE,CAAA8E,aAAA,IAAAlC,MAAA,CAAAkD,QAAA,IAAAlD,MAAA,CAAAiD,YAAA,IAAAjD,MAAA,CAAAmD,UAAA,SA0WhG,CAAC;EAAA;AAAA;AAAA,SAAAE,2DAAAxD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA1W6FzC,EAAE,CAAA4D,cAAA,cAiXvD,CAAC;IAjXoD5D,EAAE,CAAA2C,SAAA,mCAkX7C,CAAC;IAlX0C3C,EAAE,CAAA8D,YAAA,CAmXxF,CAAC;EAAA;EAAA,IAAArB,EAAA;IAAA,MAAAG,MAAA,GAnXqF5C,EAAE,CAAA6C,aAAA;IAAF7C,EAAE,CAAAoD,SAAA,CAkXhD,CAAC;IAlX6CpD,EAAE,CAAA8C,UAAA,WAAAF,MAAA,CAAA+C,MAkXhD,CAAC;EAAA;AAAA;AAAA,SAAAO,6CAAAzD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlX6CzC,EAAE,CAAAmG,YAAA,EA+WxE,CAAC;IA/WqEnG,EAAE,CAAA6D,UAAA,IAAAoC,0DAAA,kBAgXvD,CAAC;EAAA;EAAA,IAAAxD,EAAA;IAAA,MAAAG,MAAA,GAhXoD5C,EAAE,CAAA6C,aAAA;IAAF7C,EAAE,CAAAoD,SAAA,CAoXhG,CAAC;IApX6FpD,EAAE,CAAA8E,aAAA,KAAAlC,MAAA,CAAAwD,OAAA,KAAAxD,MAAA,CAAAmC,OAAA,IAAAnC,MAAA,CAAAmD,UAAA,SAoXhG,CAAC;EAAA;AAAA;AAAA,MAAAM,GAAA;AAAA,MAAAC,GAAA;AA1XP,MAAMC,yBAAyB,CAAC;EAC5BC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACzD,IAAI,GAAG,IAAI;IAChB,IAAI,CAAC0D,IAAI,GAAG,IAAI;IAChB,IAAI,CAACnD,QAAQ,GAAG,IAAI;EACxB;EACA;IAAS,IAAI,CAACoD,IAAI,YAAAC,kCAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFL,yBAAyB;IAAA,CAAmD;EAAE;EACxL;IAAS,IAAI,CAACM,IAAI,kBAD8E7G,EAAE,CAAA8G,iBAAA;MAAAL,IAAA,EACJF,yBAAyB;MAAAQ,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,uCAAAzE,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADvBzC,EAAE,CAAAiE,WAAA,0BAAAvB,GAAA,CAAA+D,IAAA,KACK,OAAe,CAAC,qBAAA/D,GAAA,CAAA+D,IAAA,KAAhB,QAAe,CAAC,qBAAA/D,GAAA,CAAA+D,IAAA,KAAhB,QAAe,CAAC;QAAA;MAAA;MAAAU,MAAA;QAAApE,IAAA;QAAA0D,IAAA;QAAAnD,QAAA;MAAA;MAAA8D,UAAA;MAAAC,QAAA,GADvBrH,EAAE,CAAAsH,mBAAA;MAAAC,KAAA,EAAAjF,GAAA;MAAAkF,kBAAA,EAAAjF,GAAA;MAAAkF,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAArE,QAAA,WAAAsE,mCAAAnF,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFzC,EAAE,CAAA6H,eAAA;UAAF7H,EAAE,CAAA6D,UAAA,IAAArB,gDAAA,iBAEvF,CAAC,IAAAQ,iDAAA,yBAGoC,CAAC;UAL+ChD,EAAE,CAAAmG,YAAA,EAM1E,CAAC;QAAA;QAAA,IAAA1D,EAAA;UANuEzC,EAAE,CAAA8E,aAAA,IAAApC,GAAA,CAAAK,IAAA,SAIlG,CAAC;UAJ+F/C,EAAE,CAAAoD,SAAA,CAKpD,CAAC;UALiDpD,EAAE,CAAA8C,UAAA,2BAAAJ,GAAA,CAAAY,QAKpD,CAAC;QAAA;MAAA;MAAAwE,YAAA,GAEW9F,YAAY,EAA+BD,EAAE,CAACgG,eAAe,EAAgKjG,cAAc,EAA+BD,EAAE,CAACmG,+BAA+B;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAAqP;EAAE;AACjmB;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAToGnI,EAAE,CAAAoI,iBAAA,CASX7B,yBAAyB,EAAc,CAAC;IACvHE,IAAI,EAAExG,SAAS;IACfoI,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,uBAAuB;MACjCC,mBAAmB,EAAE,KAAK;MAC1BN,aAAa,EAAE/H,iBAAiB,CAACsI,IAAI;MACrCN,eAAe,EAAE/H,uBAAuB,CAACsI,MAAM;MAC/CnF,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA,GAAG;MACiBoF,IAAI,EAAE;QACF,+BAA+B,EAAG,kBAAiB;QACnD,0BAA0B,EAAG,mBAAkB;QAC/C,0BAA0B,EAAG;MACjC,CAAC;MACDC,OAAO,EAAE,CAAC3G,YAAY,EAAEF,cAAc,CAAC;MACvCsF,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,QAAkB;IAAErE,IAAI,EAAE,CAAC;MACrB0D,IAAI,EAAErG;IACV,CAAC,CAAC;IAAEqG,IAAI,EAAE,CAAC;MACPA,IAAI,EAAErG;IACV,CAAC,CAAC;IAAEkD,QAAQ,EAAE,CAAC;MACXmD,IAAI,EAAErG;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMwI,gBAAgB,CAAC;EACnB,IAAI1E,QAAQA,CAAA,EAAG;IACX,IAAI,IAAI,CAAC2E,SAAS,IAAI,IAAI,CAACA,SAAS,CAAC3E,QAAQ,KAAK,IAAI,EAAE;MACpD,OAAO,IAAI,CAAC2E,SAAS,CAAC3E,QAAQ;IAClC;IACA,OAAO,IAAI,CAAC4E,SAAS;EACzB;EACA,IAAI5E,QAAQA,CAAC6E,KAAK,EAAE;IAChB,IAAI,CAACD,SAAS,GAAGC,KAAK,IAAI,IAAI,IAAK,GAAEA,KAAM,EAAC,KAAK,OAAO;EAC5D;EACAvC,WAAWA,CAACqC,SAAS,EAAEG,QAAQ,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,cAAc,EAAEC,mBAAmB,EAAEC,qBAAqB,EAAE;IAC/G,IAAI,CAACR,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACG,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACC,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,MAAM,GAAG,SAAS;IACvB,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACX,SAAS,GAAG,KAAK;IACtB,IAAI,CAACY,SAAS,GAAG,IAAI9I,OAAO,CAAC,CAAC;IAC9B,IAAI,CAAC+I,GAAG,GAAG,KAAK;IAChB;IACA,IAAI,CAACC,SAAS,GAAG,WAAW;IAC5B,IAAI,CAACjE,MAAM,GAAG,EAAE;IAChB,IAAI,CAACkE,SAAS,GAAG,CAAC,CAAC;IACnB,IAAI,CAAC7E,WAAW,GAAG,KAAK;IACxB,IAAI,CAAC8E,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,QAAQ,GAAG,IAAIpJ,OAAO,CAAC,CAAC;EACjC;EACAqJ,QAAQA,CAAA,EAAG;IACP,IAAI,CAACb,mBAAmB,EAAEc,iBAAiB,CACtCC,IAAI,CAACpJ,oBAAoB,CAAC,CAACqJ,GAAG,EAAEC,GAAG,KAAK;MACzC,OAAOD,GAAG,CAACzE,MAAM,KAAK0E,GAAG,CAAC1E,MAAM,IAAIyE,GAAG,CAACpF,WAAW,KAAKqF,GAAG,CAACrF,WAAW;IAC3E,CAAC,CAAC,EAAEhE,SAAS,CAAC,IAAI,CAACgJ,QAAQ,CAAC,CAAC,CACxBM,SAAS,CAAC,CAAC;MAAE3E,MAAM;MAAEX;IAAY,CAAC,KAAK;MACxC,IAAI,CAACuF,eAAe,CAAC5E,MAAM,EAAEX,WAAW,CAAC;IAC7C,CAAC,CAAC;IACF,IAAI,IAAI,CAAC6D,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAAC2B,aAAa,EACtBL,IAAI,CAAClJ,MAAM,CAAC,MAAM,IAAI,CAAC4H,SAAS,CAAC3E,QAAQ,KAAK,IAAI,CAAC,EAAElD,SAAS,CAAC,IAAI,CAACgJ,QAAQ,CAAC,CAAC,CAC/EM,SAAS,CAAC,MAAM;QACjB,IAAI,CAACZ,SAAS,CAACe,IAAI,CAAC,IAAI,CAAC5B,SAAS,CAAC3E,QAAQ,CAAC;MAChD,CAAC,CAAC;IACN;IACA,IAAI,CAACyF,GAAG,GAAG,IAAI,CAACR,cAAc,CAACJ,KAAK;IACpC,IAAI,CAACI,cAAc,CAACuB,MAAM,EAAEP,IAAI,CAACnJ,SAAS,CAAC,IAAI,CAACgJ,QAAQ,CAAC,CAAC,CAACM,SAAS,CAAEK,SAAS,IAAK;MAChF,IAAI,CAAChB,GAAG,GAAGgB,SAAS;IACxB,CAAC,CAAC;EACN;EACAC,WAAWA,CAACC,OAAO,EAAE;IACjB,MAAM;MAAE3G,QAAQ;MAAEuF;IAAS,CAAC,GAAGoB,OAAO;IACtC,IAAI3G,QAAQ,EAAE;MACV,IAAI,CAACwF,SAAS,CAACe,IAAI,CAAC,IAAI,CAACvG,QAAQ,CAAC;IACtC;IACA,IAAIuF,QAAQ,EAAE;MACV,IAAI,CAACc,eAAe,CAAC,IAAI,CAACd,QAAQ,EAAE,IAAI,CAACzE,WAAW,CAAC;IACzD;EACJ;EACA8F,WAAWA,CAAA,EAAG;IACV,IAAI,CAACd,QAAQ,CAACS,IAAI,CAAC,CAAC;IACpB,IAAI,CAACT,QAAQ,CAACe,QAAQ,CAAC,CAAC;EAC5B;EACAR,eAAeA,CAAC5E,MAAM,EAAEX,WAAW,EAAE;IACjC;IACA,IAAI,CAACW,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACX,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACgG,kBAAkB,CAAC,CAAC;IACzB;IACA,IAAI,CAACnB,SAAS,GAAGnI,mBAAmB,CAAC,IAAI,CAACkI,SAAS,EAAEjE,MAAM,EAAEX,WAAW,CAAC;IACzEiG,MAAM,CAACC,IAAI,CAAC,IAAI,CAACrB,SAAS,CAAC,CAACsB,OAAO,CAACxF,MAAM,IAAI;MAC1C,IAAI,IAAI,CAACkE,SAAS,CAAClE,MAAM,CAAC,EAAE;QACxB,IAAI,CAACqD,QAAQ,CAACoC,QAAQ,CAAC,IAAI,CAACnC,UAAU,CAACoC,aAAa,EAAE1F,MAAM,CAAC;MACjE,CAAC,MACI;QACD,IAAI,CAACqD,QAAQ,CAACsC,WAAW,CAAC,IAAI,CAACrC,UAAU,CAACoC,aAAa,EAAE1F,MAAM,CAAC;MACpE;IACJ,CAAC,CAAC;EACN;EACAqF,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAACrF,MAAM,IAAI,CAAC,IAAI,CAACX,WAAW,IAAI,CAAC,CAAC,IAAI,CAACqE,qBAAqB,EAAE;MACnE;MACA,IAAI,CAACH,QAAQ,CAACqC,KAAK,CAAC,CAAC;MACrB,IAAI,CAACzB,WAAW,GAAG,IAAI;MACvB;IACJ;IACA,IAAI,CAACA,WAAW,GAAG,IAAI,CAACA,WAAW,IAAI,IAAI,CAACZ,QAAQ,CAACsC,eAAe,CAACjK,+BAA+B,CAAC;IACrG,IAAI,CAACuI,WAAW,CAAC2B,QAAQ,CAACJ,aAAa,CAACK,SAAS,CAACC,GAAG,CAAC,kBAAkB,CAAC;IACzE,IAAI,CAAC7B,WAAW,CAAC8B,QAAQ,CAACjG,MAAM,GAAG,IAAI,CAACA,MAAM;IAC9C,IAAI,CAACmE,WAAW,CAAC8B,QAAQ,CAACC,UAAU,CAAC,CAAC;EAC1C;EACA;IAAS,IAAI,CAACnF,IAAI,YAAAoF,yBAAAlF,CAAA;MAAA,YAAAA,CAAA,IAAwFgC,gBAAgB,EAtI1B5I,EAAE,CAAA+L,iBAAA,CAsI0C9J,IAAI,CAAC+J,SAAS,OAtI1DhM,EAAE,CAAA+L,iBAAA,CAsIiG/L,EAAE,CAACiM,SAAS,GAtI/GjM,EAAE,CAAA+L,iBAAA,CAsI0H/L,EAAE,CAACkM,UAAU,GAtIzIlM,EAAE,CAAA+L,iBAAA,CAsIoJ/L,EAAE,CAACmM,gBAAgB,GAtIzKnM,EAAE,CAAA+L,iBAAA,CAsIoL7J,IAAI,CAACkK,cAAc,MAtIzMpM,EAAE,CAAA+L,iBAAA,CAsIoOzK,EAAE,CAAC+K,mBAAmB,MAtI5PrM,EAAE,CAAA+L,iBAAA,CAsIuRzK,EAAE,CAACE,qBAAqB;IAAA,CAA4D;EAAE;EAC/c;IAAS,IAAI,CAAC8K,IAAI,kBAvI8EtM,EAAE,CAAAuM,iBAAA;MAAA9F,IAAA,EAuIJmC,gBAAgB;MAAA7B,SAAA;MAAAyF,SAAA;MAAAxF,QAAA;MAAAC,YAAA,WAAAwF,8BAAAhK,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAvIdzC,EAAE,CAAA0M,WAAA,aAAAhK,GAAA,CAAAwB,QAAA,IAuIQ,IAAI;UAvIdlE,EAAE,CAAAiE,WAAA,uBAAAvB,GAAA,CAAAwB,QAuIW,CAAC,yBAAAxB,GAAA,CAAA4G,YAAD,CAAC,iBAAA5G,GAAA,CAAA6G,MAAA,KAAL,OAAI,CAAC,iBAAA7G,GAAA,CAAA6G,MAAA,KAAL,OAAI,CAAC,kBAAA7G,GAAA,CAAAiH,GAAA,KAAT,KAAQ,CAAC,0BAAAjH,GAAA,CAAA8G,aAAD,CAAC;QAAA;MAAA;MAAArC,MAAA;QAAAmC,YAAA;QAAAC,MAAA;QAAAC,aAAA;QAAAC,QAAA;QAAAvF,QAAA;MAAA;MAAAyI,QAAA;MAAAvF,UAAA;MAAAC,QAAA,GAvIdrH,EAAE,CAAA4M,oBAAA;IAAA,EAuIsnB;EAAE;AAC9tB;AACA/M,UAAU,CAAC,CACP8B,YAAY,CAAC,CAAC,CACjB,EAAEiH,gBAAgB,CAACiE,SAAS,EAAE,cAAc,EAAE,KAAK,CAAC,CAAC;AACtDhN,UAAU,CAAC,CACP8B,YAAY,CAAC,CAAC,CACjB,EAAEiH,gBAAgB,CAACiE,SAAS,EAAE,eAAe,EAAE,KAAK,CAAC,CAAC;AACvD;EAAA,QAAA1E,SAAA,oBAAAA,SAAA,KA/IoGnI,EAAE,CAAAoI,iBAAA,CA+IXQ,gBAAgB,EAAc,CAAC;IAC9GnC,IAAI,EAAEpG,SAAS;IACfgI,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oCAAoC;MAC9CqE,QAAQ,EAAE,SAAS;MACnBjE,IAAI,EAAE;QACFoE,KAAK,EAAE,WAAW;QAClB,4BAA4B,EAAE,UAAU;QACxC,8BAA8B,EAAE,cAAc;QAC9C,sBAAsB,EAAG,oBAAmB;QAC5C,sBAAsB,EAAG,oBAAmB;QAC5C,iBAAiB,EAAE,kBAAkB;QACrC,uBAAuB,EAAG,cAAa;QACvC,+BAA+B,EAAG;MACtC,CAAC;MACD1F,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEX,IAAI,EAAExE,IAAI,CAAC+J,SAAS;IAAEe,UAAU,EAAE,CAAC;MACpDtG,IAAI,EAAEnG;IACV,CAAC,EAAE;MACCmG,IAAI,EAAElG;IACV,CAAC;EAAE,CAAC,EAAE;IAAEkG,IAAI,EAAEzG,EAAE,CAACiM;EAAU,CAAC,EAAE;IAAExF,IAAI,EAAEzG,EAAE,CAACkM;EAAW,CAAC,EAAE;IAAEzF,IAAI,EAAEzG,EAAE,CAACmM;EAAiB,CAAC,EAAE;IAAE1F,IAAI,EAAEvE,IAAI,CAACkK,cAAc;IAAEW,UAAU,EAAE,CAAC;MAC5HtG,IAAI,EAAEnG;IACV,CAAC;EAAE,CAAC,EAAE;IAAEmG,IAAI,EAAEnF,EAAE,CAAC+K,mBAAmB;IAAEU,UAAU,EAAE,CAAC;MAC/CtG,IAAI,EAAEnG;IACV,CAAC;EAAE,CAAC,EAAE;IAAEmG,IAAI,EAAEnF,EAAE,CAACE,qBAAqB;IAAEuL,UAAU,EAAE,CAAC;MACjDtG,IAAI,EAAEnG;IACV,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEgJ,YAAY,EAAE,CAAC;MACxC7C,IAAI,EAAErG;IACV,CAAC,CAAC;IAAEmJ,MAAM,EAAE,CAAC;MACT9C,IAAI,EAAErG;IACV,CAAC,CAAC;IAAEoJ,aAAa,EAAE,CAAC;MAChB/C,IAAI,EAAErG;IACV,CAAC,CAAC;IAAEqJ,QAAQ,EAAE,CAAC;MACXhD,IAAI,EAAErG;IACV,CAAC,CAAC;IAAE8D,QAAQ,EAAE,CAAC;MACXuC,IAAI,EAAErG;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM4M,uCAAuC,CAAC;EAC1CxG,WAAWA,CAACyC,UAAU,EAAE;IACpB,IAAI,CAACA,UAAU,GAAGA,UAAU;EAChC;EACA;IAAS,IAAI,CAACvC,IAAI,YAAAuG,gDAAArG,CAAA;MAAA,YAAAA,CAAA,IAAwFoG,uCAAuC,EA1LjDhN,EAAE,CAAA+L,iBAAA,CA0LiE/L,EAAE,CAACkM,UAAU;IAAA,CAA4C;EAAE;EAC9N;IAAS,IAAI,CAACI,IAAI,kBA3L8EtM,EAAE,CAAAuM,iBAAA;MAAA9F,IAAA,EA2LJuG,uCAAuC;MAAAjG,SAAA;MAAAK,UAAA;IAAA,EAAqG;EAAE;AAChP;AACA;EAAA,QAAAe,SAAA,oBAAAA,SAAA,KA7LoGnI,EAAE,CAAAoI,iBAAA,CA6LX4E,uCAAuC,EAAc,CAAC;IACrIvG,IAAI,EAAEpG,SAAS;IACfgI,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAG,oDAAmD;MAC9DlB,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEX,IAAI,EAAEzG,EAAE,CAACkM;EAAW,CAAC,CAAC;AAAA;AAC3D,MAAMgB,qBAAqB,CAAC;EACxB1G,WAAWA,CAAC2G,YAAY,EAAElE,UAAU,EAAED,QAAQ,EAAEoE,GAAG,EAAEjE,cAAc,EAAEC,mBAAmB,EAAEC,qBAAqB,EAAE;IAC7G,IAAI,CAAC8D,YAAY,GAAGA,YAAY;IAChC,IAAI,CAAClE,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACD,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACoE,GAAG,GAAGA,GAAG;IACd,IAAI,CAACjE,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACC,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAAC7F,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACmB,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACY,YAAY,GAAG,IAAI;IACxB,IAAI,CAACM,YAAY,GAAG,IAAI;IACxB,IAAI,CAAC4D,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACF,MAAM,GAAG,SAAS;IACvB,IAAI,CAAC8D,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAAClJ,OAAO,GAAG,KAAK;IACpB,IAAI,CAACD,OAAO,GAAG,KAAK;IACpB,IAAI,CAACY,OAAO,GAAG,KAAK;IACpB,IAAI,CAACqB,OAAO,GAAG,KAAK;IACpB,IAAI,CAACL,UAAU,GAAG,KAAK;IACvB,IAAI,CAAC1B,OAAO,GAAG,KAAK;IACpB,IAAI,CAACH,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACyF,GAAG,GAAG,KAAK;IAChB;IACA,IAAI,CAACC,SAAS,GAAG,WAAW;IAC5B,IAAI,CAAC2D,cAAc,GAAG,CAAC,CAAC;IACxB,IAAI,CAACC,cAAc,GAAG,CAAC,CAAC;IACxB,IAAI,CAAClJ,qBAAqB,GAAG,CAAC,CAAC;IAC/B,IAAI,CAACqB,MAAM,GAAG,EAAE;IAChB,IAAI,CAACX,WAAW,GAAG,KAAK;IACxB,IAAI,CAACgF,QAAQ,GAAG,IAAIpJ,OAAO,CAAC,CAAC;EACjC;EACA6M,uBAAuBA,CAAA,EAAG;IACtB,IAAI,IAAI,CAACC,sBAAsB,EAAE;MAC7B,IAAI,CAACA,sBAAsB,CAACvC,OAAO,CAACwC,IAAI,IAAKA,IAAI,CAACpE,MAAM,GAAG,IAAI,CAACA,MAAO,CAAC;IAC5E;EACJ;EACAU,QAAQA,CAAA,EAAG;IACP,IAAI,CAACb,mBAAmB,EAAEc,iBAAiB,CACtCC,IAAI,CAACpJ,oBAAoB,CAAC,CAACqJ,GAAG,EAAEC,GAAG,KAAK;MACzC,OAAOD,GAAG,CAACzE,MAAM,KAAK0E,GAAG,CAAC1E,MAAM,IAAIyE,GAAG,CAACpF,WAAW,KAAKqF,GAAG,CAACrF,WAAW;IAC3E,CAAC,CAAC,EAAEhE,SAAS,CAAC,IAAI,CAACgJ,QAAQ,CAAC,CAAC,CACxBM,SAAS,CAAC,CAAC;MAAE3E,MAAM;MAAEX;IAAY,CAAC,KAAK;MACxC,IAAI,CAACuF,eAAe,CAAC5E,MAAM,EAAEX,WAAW,CAAC;IAC7C,CAAC,CAAC;IACF,IAAI,CAACmI,YAAY,CACZS,OAAO,CAAC,IAAI,CAAC3E,UAAU,EAAE,IAAI,CAAC,CAC9BkB,IAAI,CAACnJ,SAAS,CAAC,IAAI,CAACgJ,QAAQ,CAAC,CAAC,CAC9BM,SAAS,CAACuD,WAAW,IAAI;MAC1B,IAAI,CAACxJ,OAAO,GAAG,CAAC,CAACwJ,WAAW;MAC5B,IAAI,CAACT,GAAG,CAACU,YAAY,CAAC,CAAC;IAC3B,CAAC,CAAC;IACF,IAAI,CAACnE,GAAG,GAAG,IAAI,CAACR,cAAc,CAACJ,KAAK;IACpC,IAAI,CAACI,cAAc,CAACuB,MAAM,EAAEP,IAAI,CAACnJ,SAAS,CAAC,IAAI,CAACgJ,QAAQ,CAAC,CAAC,CAACM,SAAS,CAAEK,SAAS,IAAK;MAChF,IAAI,CAAChB,GAAG,GAAGgB,SAAS;IACxB,CAAC,CAAC;EACN;EACAoD,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACN,uBAAuB,CAAC,CAAC;IAC9B,MAAMO,kBAAkB,GAAG,IAAI,CAACN,sBAAsB,CAAC7C,OAAO,CAACV,IAAI,CAACjJ,SAAS,CAAC,IAAI,CAACwM,sBAAsB,CAAC,CAAC;IAC3GM,kBAAkB,CACb7D,IAAI,CAAChJ,SAAS,CAAC8M,IAAI,IAAIpN,KAAK,CAAC,GAAG,CAACmN,kBAAkB,EAAE,GAAGC,IAAI,CAAC5M,GAAG,CAAE6M,KAAK,IAAKA,KAAK,CAACxE,SAAS,CAAC,CAAC,CAAC,CAAC,EAAEtI,QAAQ,CAAC,MAAM4M,kBAAkB,CAAC,EAAE3M,GAAG,CAAC4M,IAAI,IAAIA,IAAI,CAACE,IAAI,CAAED,KAAK,IAAKA,KAAK,CAAChK,QAAQ,CAAC,CAAC,EAAElD,SAAS,CAAC,IAAI,CAACgJ,QAAQ,CAAC,CAAC,CACjNM,SAAS,CAACpG,QAAQ,IAAI;MACvB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;MACxB,IAAI,CAACkJ,GAAG,CAACU,YAAY,CAAC,CAAC;IAC3B,CAAC,CAAC;EACN;EACAlD,WAAWA,CAACC,OAAO,EAAE;IACjB,MAAM;MAAEtB,MAAM;MAAEzD,QAAQ;MAAEN,QAAQ;MAAED,YAAY;MAAEM,YAAY;MAAEjB,YAAY;MAAEnB,aAAa;MAAEkB,gBAAgB;MAAEnB,iBAAiB;MAAEiG;IAAS,CAAC,GAAGoB,OAAO;IACtJ,IAAItB,MAAM,EAAE;MACR,IAAI,CAACkE,uBAAuB,CAAC,CAAC;MAC9B,IAAI,CAACrJ,OAAO,GAAG,IAAI,CAACmF,MAAM,KAAK,OAAO;MACtC,IAAI,CAACpF,OAAO,GAAG,IAAI,CAACoF,MAAM,KAAK,OAAO;IAC1C;IACA,IAAIzD,QAAQ,IAAIN,QAAQ,IAAID,YAAY,IAAIM,YAAY,EAAE;MACtD,IAAI,CAACd,OAAO,GAAG,CAAC,EAAE,IAAI,CAACe,QAAQ,IAAI,IAAI,CAACN,QAAQ,IAAI,IAAI,CAACD,YAAY,IAAI,IAAI,CAACM,YAAY,CAAC;IAC/F;IACA,IAAIjB,YAAY,IAAInB,aAAa,IAAIkB,gBAAgB,IAAInB,iBAAiB,EAAE;MACxE,IAAI,CAAC4C,OAAO,GAAG,CAAC,EAAE,IAAI,CAACxB,YAAY,IAAI,IAAI,CAACnB,aAAa,IAAI,IAAI,CAACkB,gBAAgB,IAAI,IAAI,CAACnB,iBAAiB,CAAC;MAC7G,IAAI,CAAC6F,qBAAqB,EAAE+E,YAAY,EAAE3D,IAAI,CAAC,IAAI,CAACrE,OAAO,CAAC;IAChE;IACA,IAAIqD,QAAQ,EAAE;MACV,IAAI,CAACc,eAAe,CAAC,IAAI,CAACd,QAAQ,EAAE,IAAI,CAACzE,WAAW,CAAC;IACzD;EACJ;EACA8F,WAAWA,CAAA,EAAG;IACV,IAAI,CAACqC,YAAY,CAACkB,cAAc,CAAC,IAAI,CAACpF,UAAU,CAAC;IACjD,IAAI,CAACe,QAAQ,CAACS,IAAI,CAAC,CAAC;IACpB,IAAI,CAACT,QAAQ,CAACe,QAAQ,CAAC,CAAC;EAC5B;EACAR,eAAeA,CAAC5E,MAAM,EAAEX,WAAW,EAAE;IACjC;IACA,IAAI,CAACW,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACX,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACe,UAAU,GAAG,CAAC,CAACJ,MAAM,IAAIX,WAAW;IACzC,MAAMsJ,SAAS,GAAG,CAAC,EAAE,IAAI,CAACxI,QAAQ,IAAI,IAAI,CAACN,QAAQ,IAAI,IAAI,CAACD,YAAY,IAAI,IAAI,CAACM,YAAY,CAAC;IAC9F,IAAI,CAACd,OAAO,GAAGuJ,SAAS,IAAK,CAAC,IAAI,CAAClI,OAAO,IAAIpB,WAAY;IAC1D,IAAI,CAACV,qBAAqB,GACtB,IAAI,CAACS,OAAO,IAAI,IAAI,CAACgB,UAAU,GACxB,IAAI,CAACwH,cAAc,GAAG7L,mBAAmB,CAAE,GAAE,IAAI,CAACkI,SAAU,gBAAe,EAAEjE,MAAM,EAAEX,WAAW,CAAC,GAClG,CAAC,CAAC;IACZ,IAAI,CAACoI,GAAG,CAACU,YAAY,CAAC,CAAC;IACvB;IACA,IAAI,CAACP,cAAc,GAAG7L,mBAAmB,CAAE,GAAE,IAAI,CAACkI,SAAU,gBAAe,EAAE,IAAI,CAACxD,OAAO,GAAG,EAAE,GAAGT,MAAM,EAAE,IAAI,CAACS,OAAO,GAAG,KAAK,GAAGpB,WAAW,CAAC;IAC5I,IAAI,CAACwI,cAAc,GAAG9L,mBAAmB,CAAE,GAAE,IAAI,CAACkI,SAAU,gBAAe,EAAE,IAAI,CAACxD,OAAO,GAAGT,MAAM,GAAG,EAAE,EAAE,IAAI,CAACS,OAAO,GAAGpB,WAAW,GAAG,KAAK,CAAC;IAC5I,MAAM6E,SAAS,GAAG;MACd,GAAG,IAAI,CAAC0D,cAAc;MACtB,GAAG,IAAI,CAACC;IACZ,CAAC;IACDvC,MAAM,CAACC,IAAI,CAACrB,SAAS,CAAC,CAACsB,OAAO,CAACxF,MAAM,IAAI;MACrC,IAAIkE,SAAS,CAAClE,MAAM,CAAC,EAAE;QACnB,IAAI,CAACqD,QAAQ,CAACoC,QAAQ,CAAC,IAAI,CAACnC,UAAU,CAACoC,aAAa,EAAE1F,MAAM,CAAC;MACjE,CAAC,MACI;QACD,IAAI,CAACqD,QAAQ,CAACsC,WAAW,CAAC,IAAI,CAACrC,UAAU,CAACoC,aAAa,EAAE1F,MAAM,CAAC;MACpE;IACJ,CAAC,CAAC;EACN;EACA;IAAS,IAAI,CAACe,IAAI,YAAA6H,8BAAA3H,CAAA;MAAA,YAAAA,CAAA,IAAwFsG,qBAAqB,EA5T/BlN,EAAE,CAAA+L,iBAAA,CA4T+C5J,IAAI,CAACqM,YAAY,GA5TlExO,EAAE,CAAA+L,iBAAA,CA4T6E/L,EAAE,CAACkM,UAAU,GA5T5FlM,EAAE,CAAA+L,iBAAA,CA4TuG/L,EAAE,CAACiM,SAAS,GA5TrHjM,EAAE,CAAA+L,iBAAA,CA4TgI/L,EAAE,CAACyO,iBAAiB,GA5TtJzO,EAAE,CAAA+L,iBAAA,CA4TiK7J,IAAI,CAACkK,cAAc,MA5TtLpM,EAAE,CAAA+L,iBAAA,CA4TiNzK,EAAE,CAAC+K,mBAAmB,MA5TzOrM,EAAE,CAAA+L,iBAAA,CA4ToQzK,EAAE,CAACE,qBAAqB;IAAA,CAA4D;EAAE;EAC5b;IAAS,IAAI,CAACqF,IAAI,kBA7T8E7G,EAAE,CAAA8G,iBAAA;MAAAL,IAAA,EA6TJyG,qBAAqB;MAAAnG,SAAA;MAAA2H,cAAA,WAAAC,qCAAAlM,EAAA,EAAAC,GAAA,EAAAkM,QAAA;QAAA,IAAAnM,EAAA;UA7TnBzC,EAAE,CAAA6O,cAAA,CAAAD,QAAA,EA6TomDhG,gBAAgB;QAAA;QAAA,IAAAnG,EAAA;UAAA,IAAAqM,EAAA;UA7TtnD9O,EAAE,CAAA+O,cAAA,CAAAD,EAAA,GAAF9O,EAAE,CAAAgP,WAAA,QAAAtM,GAAA,CAAAgL,sBAAA,GAAAoB,EAAA;QAAA;MAAA;MAAA9H,QAAA;MAAAC,YAAA,WAAAgI,mCAAAxM,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFzC,EAAE,CAAAiE,WAAA,4BAAAvB,GAAA,CAAA4K,SA6TgB,CAAC,kCAAA5K,GAAA,CAAA2K,QAAD,CAAC,qBAAA3K,GAAA,CAAA2K,QAAD,CAAC,yBAAA3K,GAAA,CAAAiH,GAAA,KAAb,KAAY,CAAC,wBAAAjH,GAAA,CAAA2K,QAAA,IAAA3K,GAAA,CAAAyB,OAAD,CAAC,2BAAAzB,GAAA,CAAA2K,QAAA,IAAA3K,GAAA,CAAA0B,OAAD,CAAC,4BAAA1B,GAAA,CAAA0D,OAAD,CAAC,gCAAA1D,GAAA,CAAAiH,GAAA,KAAb,KAAY,CAAC,+BAAAjH,GAAA,CAAA0D,OAAA,IAAA1D,GAAA,CAAA0B,OAAD,CAAC,+BAAA1B,GAAA,CAAA0D,OAAA,IAAA1D,GAAA,CAAAyB,OAAD,CAAC,4BAAAzB,GAAA,CAAAqC,OAAA,KAAArC,GAAA,CAAA0D,OAAD,CAAC,gCAAA1D,GAAA,CAAAiH,GAAA,KAAb,KAAY,CAAC,oCAAAjH,GAAA,CAAAqC,OAAA,IAAArC,GAAA,CAAA2B,OAAD,CAAC,qCAAA3B,GAAA,CAAAqC,OAAA,IAAArC,GAAA,CAAAwB,QAAD,CAAC,+BAAAxB,GAAA,CAAAqC,OAAA,KAAArC,GAAA,CAAA0D,OAAA,IAAA1D,GAAA,CAAA0B,OAAD,CAAC,+BAAA1B,GAAA,CAAAqC,OAAA,KAAArC,GAAA,CAAA0D,OAAA,IAAA1D,GAAA,CAAAyB,OAAD,CAAC,qBAAAzB,GAAA,CAAAqC,OAAA,KAAArC,GAAA,CAAA0D,OAAD,CAAC,wBAAA1D,GAAA,CAAAiH,GAAA,KAAb,KAAY,CAAC,wBAAAjH,GAAA,CAAAqC,OAAA,KAAArC,GAAA,CAAA0D,OAAA,IAAA1D,GAAA,CAAA0B,OAAD,CAAC,wBAAA1B,GAAA,CAAAqC,OAAA,KAAArC,GAAA,CAAA0D,OAAA,IAAA1D,GAAA,CAAAyB,OAAD,CAAC;QAAA;MAAA;MAAAgD,MAAA;QAAA3D,iBAAA;QAAAmB,gBAAA;QAAAY,YAAA;QAAAM,YAAA;QAAApC,aAAA;QAAAmB,YAAA;QAAAY,QAAA;QAAAiE,QAAA;QAAA3D,QAAA;QAAAyD,MAAA;QAAA8D,QAAA;QAAAC,SAAA;MAAA;MAAAX,QAAA;MAAAvF,UAAA;MAAAC,QAAA,GA7TnBrH,EAAE,CAAAkP,kBAAA,CA6T4gD,CAAC1N,qBAAqB,CAAC,GA7TriDxB,EAAE,CAAA4M,oBAAA,EAAF5M,EAAE,CAAAsH,mBAAA;MAAAE,kBAAA,EAAAjF,GAAA;MAAAkF,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAArE,QAAA,WAAA6L,+BAAA1M,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFzC,EAAE,CAAA6H,eAAA;UAAF7H,EAAE,CAAA6D,UAAA,IAAAgB,4CAAA,iBA8TpF,CAAC,IAAAQ,4CAAA,MAwBP,CAAC,IAAAW,4CAAA,gCAtVuFhG,EAAE,CAAAoP,sBA+VvE,CAAC,IAAAlJ,4CAAA,gCA/VoElG,EAAE,CAAAoP,sBA8WrE,CAAC;QAAA;QAAA,IAAA3M,EAAA;UA9WkEzC,EAAE,CAAA8E,aAAA,IAAApC,GAAA,CAAA0D,OAAA,QA4VlG,CAAC;QAAA;MAAA;MAAA0B,YAAA,GA0B0DvB,yBAAyB,EAAwGzG,OAAO,EAAoFC,gBAAgB,EAAmJ0B,iBAAiB,EAA+BH,EAAE,CAACC,+BAA+B;MAAA0G,aAAA;MAAAC,eAAA;IAAA,EAAkM;EAAE;AACptB;AACArI,UAAU,CAAC,CACP8B,YAAY,CAAC,CAAC,CACjB,EAAEuL,qBAAqB,CAACL,SAAS,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;AACvDhN,UAAU,CAAC,CACP8B,YAAY,CAAC,CAAC,CACjB,EAAEuL,qBAAqB,CAACL,SAAS,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC;AACxD;EAAA,QAAA1E,SAAA,oBAAAA,SAAA,KA9XoGnI,EAAE,CAAAoI,iBAAA,CA8XX8E,qBAAqB,EAAc,CAAC;IACnHzG,IAAI,EAAExG,SAAS;IACfoI,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,gBAAgB;MAC1BqE,QAAQ,EAAE,cAAc;MACxBpE,mBAAmB,EAAE,KAAK;MAC1BN,aAAa,EAAE/H,iBAAiB,CAACsI,IAAI;MACrCN,eAAe,EAAE/H,uBAAuB,CAACsI,MAAM;MAC/C4G,SAAS,EAAE,CAAC7N,qBAAqB,CAAC;MAClC8B,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiBoF,IAAI,EAAE;QACF,iCAAiC,EAAG,WAAU;QAC9C,uCAAuC,EAAG,UAAS;QACnD,0BAA0B,EAAG,UAAS;QACtC,8BAA8B,EAAG,eAAc;QAC/C,6BAA6B,EAAG,qBAAoB;QACpD,gCAAgC,EAAG,qBAAoB;QACvD,iCAAiC,EAAG,SAAQ;QAC5C,qCAAqC,EAAG,eAAc;QACtD,oCAAoC,EAAG,oBAAmB;QAC1D,oCAAoC,EAAG,oBAAmB;QAC1D,iCAAiC,EAAG,qBAAoB;QACxD,qCAAqC,EAAG,eAAc;QACtD,yCAAyC,EAAG,oBAAmB;QAC/D,0CAA0C,EAAG,qBAAoB;QACjE,oCAAoC,EAAG,gCAA+B;QACtE,oCAAoC,EAAG,gCAA+B;QACtE,yBAAyB,EAAG,sBAAqB;QACjD,6BAA6B,EAAG,eAAc;QAC9C,4BAA4B,EAAG,iCAAgC;QAC/D,4BAA4B,EAAG;MACnC,CAAC;MACDC,OAAO,EAAE,CAACpC,yBAAyB,EAAEzG,OAAO,EAAEC,gBAAgB,EAAE0B,iBAAiB,CAAC;MAClF2F,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEX,IAAI,EAAEtE,IAAI,CAACqM;EAAa,CAAC,EAAE;IAAE/H,IAAI,EAAEzG,EAAE,CAACkM;EAAW,CAAC,EAAE;IAAEzF,IAAI,EAAEzG,EAAE,CAACiM;EAAU,CAAC,EAAE;IAAExF,IAAI,EAAEzG,EAAE,CAACyO;EAAkB,CAAC,EAAE;IAAEhI,IAAI,EAAEvE,IAAI,CAACkK,cAAc;IAAEW,UAAU,EAAE,CAAC;MACvKtG,IAAI,EAAEnG;IACV,CAAC;EAAE,CAAC,EAAE;IAAEmG,IAAI,EAAEnF,EAAE,CAAC+K,mBAAmB;IAAEU,UAAU,EAAE,CAAC;MAC/CtG,IAAI,EAAEnG;IACV,CAAC;EAAE,CAAC,EAAE;IAAEmG,IAAI,EAAEnF,EAAE,CAACE,qBAAqB;IAAEuL,UAAU,EAAE,CAAC;MACjDtG,IAAI,EAAEnG;IACV,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEoN,sBAAsB,EAAE,CAAC;MAClDjH,IAAI,EAAEjG,eAAe;MACrB6H,IAAI,EAAE,CAACO,gBAAgB;IAC3B,CAAC,CAAC;IAAEpF,iBAAiB,EAAE,CAAC;MACpBiD,IAAI,EAAErG;IACV,CAAC,CAAC;IAAEuE,gBAAgB,EAAE,CAAC;MACnB8B,IAAI,EAAErG;IACV,CAAC,CAAC;IAAEmF,YAAY,EAAE,CAAC;MACfkB,IAAI,EAAErG;IACV,CAAC,CAAC;IAAEyF,YAAY,EAAE,CAAC;MACfY,IAAI,EAAErG;IACV,CAAC,CAAC;IAAEqD,aAAa,EAAE,CAAC;MAChBgD,IAAI,EAAErG;IACV,CAAC,CAAC;IAAEwE,YAAY,EAAE,CAAC;MACf6B,IAAI,EAAErG;IACV,CAAC,CAAC;IAAEoF,QAAQ,EAAE,CAAC;MACXiB,IAAI,EAAErG;IACV,CAAC,CAAC;IAAEqJ,QAAQ,EAAE,CAAC;MACXhD,IAAI,EAAErG;IACV,CAAC,CAAC;IAAE0F,QAAQ,EAAE,CAAC;MACXW,IAAI,EAAErG;IACV,CAAC,CAAC;IAAEmJ,MAAM,EAAE,CAAC;MACT9C,IAAI,EAAErG;IACV,CAAC,CAAC;IAAEiN,QAAQ,EAAE,CAAC;MACX5G,IAAI,EAAErG;IACV,CAAC,CAAC;IAAEkN,SAAS,EAAE,CAAC;MACZ7G,IAAI,EAAErG;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMkP,mBAAmB,CAAC;EACtB,IAAIC,UAAUA,CAACxG,KAAK,EAAE;IAClB,MAAMyG,cAAc,GAAIC,IAAI,IAAK,OAAOA,IAAI,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,SAAS,KAAK,CAAC,CAACA,IAAI,CAACC,OAAO,IAAI,CAAC,CAACD,IAAI,CAACE,OAAO,CAAC;IAC5H,IAAI,OAAO5G,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE;MAC7C,IAAI,CAAC6G,QAAQ,GAAG,IAAI;IACxB,CAAC,MACI,IAAIJ,cAAc,CAACzG,KAAK,CAAC,EAAE;MAC5B,IAAI,CAAC6G,QAAQ,GAAG,IAAI;MACpB,IAAI,CAACD,OAAO,GAAG5G,KAAK,CAAC4G,OAAO;MAC5B,IAAI,CAACD,OAAO,GAAG3G,KAAK,CAAC2G,OAAO;MAC5B,IAAI,CAACG,SAAS,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;MACpC,IAAI,CAACC,SAAS,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IACxC;EACJ;EACAC,kBAAkBA,CAACC,KAAK,GAAG,KAAK,EAAE;IAC9B,IAAI,CAACC,uBAAuB,CAAC,CAAC;IAC9B;IACA;IACA,IAAI,CAAC,IAAI,CAACC,gBAAgB,EAAE;MACxB;IACJ;IACA,MAAMC,QAAQ,GAAG,IAAI,CAACC,EAAE;IACxB,MAAMvH,KAAK,GAAGsH,QAAQ,CAACtH,KAAK;IAC5B;IACA,IAAI,CAACmH,KAAK,IAAI,IAAI,CAACP,OAAO,KAAK,IAAI,CAACY,eAAe,IAAIxH,KAAK,KAAK,IAAI,CAACyH,aAAa,EAAE;MACjF;IACJ;IACA,MAAMC,eAAe,GAAGJ,QAAQ,CAACK,WAAW;IAC5C;IACA;IACA;IACA;IACA;IACAL,QAAQ,CAAC3E,SAAS,CAACC,GAAG,CAAC,gCAAgC,CAAC;IACxD0E,QAAQ,CAACK,WAAW,GAAG,EAAE;IACzB,IAAIC,MAAM,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACR,QAAQ,CAACS,YAAY,GAAG,IAAI,CAACC,QAAQ,IAAI,IAAI,CAACX,gBAAgB,CAAC,GAAG,IAAI,CAACA,gBAAgB,GAC5G,IAAI,CAACW,QAAQ;IACjB,IAAI,IAAI,CAAClB,SAAS,KAAK,IAAI,IAAIc,MAAM,GAAG,IAAI,CAACd,SAAS,EAAE;MACpDc,MAAM,GAAG,IAAI,CAACd,SAAS;IAC3B;IACA,IAAI,IAAI,CAACE,SAAS,KAAK,IAAI,IAAIY,MAAM,GAAG,IAAI,CAACZ,SAAS,EAAE;MACpDY,MAAM,GAAG,IAAI,CAACZ,SAAS;IAC3B;IACA;IACAM,QAAQ,CAACW,KAAK,CAACL,MAAM,GAAI,GAAEA,MAAO,IAAG;IACrCN,QAAQ,CAAC3E,SAAS,CAACuF,MAAM,CAAC,gCAAgC,CAAC;IAC3DZ,QAAQ,CAACK,WAAW,GAAGD,eAAe;IACtC;IACA;IACA,IAAI,OAAOS,qBAAqB,KAAK,WAAW,EAAE;MAC9C,IAAI,CAACC,MAAM,CAACC,iBAAiB,CAAC,MAAMF,qBAAqB,CAAC,MAAM;QAC5D,MAAM;UAAEG,cAAc;UAAEC;QAAa,CAAC,GAAGjB,QAAQ;QACjD;QACA;QACA;QACA;QACA;QACA;QACA,IAAI,CAAC,IAAI,CAACrG,QAAQ,CAACuH,SAAS,IAAIC,QAAQ,CAACC,aAAa,KAAKpB,QAAQ,EAAE;UACjEA,QAAQ,CAACqB,iBAAiB,CAACL,cAAc,EAAEC,YAAY,CAAC;QAC5D;MACJ,CAAC,CAAC,CAAC;IACP;IACA,IAAI,CAACd,aAAa,GAAGzH,KAAK;IAC1B,IAAI,CAACwH,eAAe,GAAG,IAAI,CAACZ,OAAO;EACvC;EACAQ,uBAAuBA,CAAA,EAAG;IACtB,IAAI,IAAI,CAACC,gBAAgB,IAAI,CAAC,IAAI,CAAC,IAAI,CAACE,EAAE,CAACqB,UAAU,EAAE;MACnD;IACJ;IACA;IACA,MAAMC,aAAa,GAAG,IAAI,CAACtB,EAAE,CAACuB,SAAS,CAAC,KAAK,CAAC;IAC9CD,aAAa,CAACE,IAAI,GAAG,CAAC;IACtB;IACA;IACA;IACAF,aAAa,CAACZ,KAAK,CAACe,QAAQ,GAAG,UAAU;IACzCH,aAAa,CAACZ,KAAK,CAACgB,UAAU,GAAG,QAAQ;IACzCJ,aAAa,CAACZ,KAAK,CAACiB,MAAM,GAAG,MAAM;IACnCL,aAAa,CAACZ,KAAK,CAACkB,OAAO,GAAG,GAAG;IACjCN,aAAa,CAACZ,KAAK,CAACL,MAAM,GAAG,EAAE;IAC/BiB,aAAa,CAACZ,KAAK,CAACjB,SAAS,GAAG,EAAE;IAClC6B,aAAa,CAACZ,KAAK,CAACnB,SAAS,GAAG,EAAE;IAClC;IACA;IACA;IACA;IACA;IACA+B,aAAa,CAACZ,KAAK,CAACmB,QAAQ,GAAG,QAAQ;IACvC,IAAI,CAAC7B,EAAE,CAACqB,UAAU,CAACS,WAAW,CAACR,aAAa,CAAC;IAC7C,IAAI,CAACxB,gBAAgB,GAAGwB,aAAa,CAACS,YAAY,GAAG,IAAI,CAACtB,QAAQ;IAClE,IAAI,CAACT,EAAE,CAACqB,UAAU,CAACW,WAAW,CAACV,aAAa,CAAC;IAC7C;IACA,IAAI,CAAC/B,SAAS,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IACpC,IAAI,CAACC,SAAS,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;EACxC;EACAA,YAAYA,CAAA,EAAG;IACX,MAAMD,SAAS,GAAG,IAAI,CAACJ,OAAO,IAAI,IAAI,CAACS,gBAAgB,GAAG,IAAI,CAACT,OAAO,GAAG,IAAI,CAACS,gBAAgB,GAAG,IAAI,CAACW,QAAQ,GAAG,IAAI;IACrH,IAAIhB,SAAS,KAAK,IAAI,EAAE;MACpB,IAAI,CAACO,EAAE,CAACU,KAAK,CAACjB,SAAS,GAAI,GAAEA,SAAU,IAAG;IAC9C;IACA,OAAOA,SAAS;EACpB;EACAD,YAAYA,CAAA,EAAG;IACX,MAAMD,SAAS,GAAG,IAAI,CAACH,OAAO,IAAI,IAAI,CAACU,gBAAgB,GAAG,IAAI,CAACV,OAAO,GAAG,IAAI,CAACU,gBAAgB,GAAG,IAAI,CAACW,QAAQ,GAAG,IAAI;IACrH,IAAIlB,SAAS,KAAK,IAAI,EAAE;MACpB,IAAI,CAACS,EAAE,CAACU,KAAK,CAACnB,SAAS,GAAI,GAAEA,SAAU,IAAG;IAC9C;IACA,OAAOA,SAAS;EACpB;EACA0C,gBAAgBA,CAAA,EAAG;IACf;EAAA;EAEJ/L,WAAWA,CAACyC,UAAU,EAAEkI,MAAM,EAAEqB,QAAQ,EAAEC,aAAa,EAAE;IACrD,IAAI,CAACxJ,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACkI,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACqB,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAAC7C,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACU,EAAE,GAAG,IAAI,CAACrH,UAAU,CAACoC,aAAa;IACvC,IAAI,CAACwE,SAAS,GAAG,IAAI;IACrB,IAAI,CAACE,SAAS,GAAG,IAAI;IACrB,IAAI,CAAC/F,QAAQ,GAAG,IAAIpJ,OAAO,CAAC,CAAC;IAC7B,IAAI,CAACmQ,QAAQ,GAAG,EAAE;EACtB;EACA2B,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAAC9C,QAAQ,IAAI,IAAI,CAAC4C,QAAQ,CAACG,SAAS,EAAE;MAC1C,IAAI,CAAC1C,kBAAkB,CAAC,CAAC;MACzB,IAAI,CAACwC,aAAa,CACbnI,SAAS,CAAC,CAAC,CACXH,IAAI,CAACnJ,SAAS,CAAC,IAAI,CAACgJ,QAAQ,CAAC,CAAC,CAC9BM,SAAS,CAAC,MAAM,IAAI,CAAC2F,kBAAkB,CAAC,IAAI,CAAC,CAAC;IACvD;EACJ;EACAnF,WAAWA,CAAA,EAAG;IACV,IAAI,CAACd,QAAQ,CAACS,IAAI,CAAC,IAAI,CAAC;IACxB,IAAI,CAACT,QAAQ,CAACe,QAAQ,CAAC,CAAC;EAC5B;EACA6H,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAAChD,QAAQ,IAAI,IAAI,CAAC4C,QAAQ,CAACG,SAAS,EAAE;MAC1C,IAAI,CAAC1C,kBAAkB,CAAC,CAAC;IAC7B;EACJ;EACA;IAAS,IAAI,CAACvJ,IAAI,YAAAmM,4BAAAjM,CAAA;MAAA,YAAAA,CAAA,IAAwF0I,mBAAmB,EA5oB7BtP,EAAE,CAAA+L,iBAAA,CA4oB6C/L,EAAE,CAACkM,UAAU,GA5oB5DlM,EAAE,CAAA+L,iBAAA,CA4oBuE/L,EAAE,CAAC8S,MAAM,GA5oBlF9S,EAAE,CAAA+L,iBAAA,CA4oB6F3J,IAAI,CAAC2Q,QAAQ,GA5oB5G/S,EAAE,CAAA+L,iBAAA,CA4oBuH1J,IAAI,CAAC2Q,eAAe;IAAA,CAA4C;EAAE;EAC3R;IAAS,IAAI,CAAC1G,IAAI,kBA7oB8EtM,EAAE,CAAAuM,iBAAA;MAAA9F,IAAA,EA6oBJ6I,mBAAmB;MAAAvI,SAAA;MAAAyF,SAAA,WAA4H,GAAG;MAAAvF,YAAA,WAAAgM,iCAAAxQ,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA7oBhJzC,EAAE,CAAAkT,UAAA,mBAAAC,6CAAA;YAAA,OA6oBJzQ,GAAA,CAAA6P,gBAAA,CAAiB,CAAC;UAAA,EAAC;QAAA;MAAA;MAAApL,MAAA;QAAAoI,UAAA;MAAA;MAAA5C,QAAA;MAAAvF,UAAA;IAAA,EAA4N;EAAE;AACnV;AACA;EAAA,QAAAe,SAAA,oBAAAA,SAAA,KA/oBoGnI,EAAE,CAAAoI,iBAAA,CA+oBXkH,mBAAmB,EAAc,CAAC;IACjH7I,IAAI,EAAEpG,SAAS;IACfgI,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sBAAsB;MAChCqE,QAAQ,EAAE,YAAY;MACtBjE,IAAI,EAAE;QACF;QACA;QACAoJ,IAAI,EAAE,GAAG;QACT,SAAS,EAAE;MACf,CAAC;MACD1K,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEX,IAAI,EAAEzG,EAAE,CAACkM;EAAW,CAAC,EAAE;IAAEzF,IAAI,EAAEzG,EAAE,CAAC8S;EAAO,CAAC,EAAE;IAAErM,IAAI,EAAErE,IAAI,CAAC2Q;EAAS,CAAC,EAAE;IAAEtM,IAAI,EAAEpE,IAAI,CAAC2Q;EAAgB,CAAC,CAAC,EAAkB;IAAEzD,UAAU,EAAE,CAAC;MAC1J9I,IAAI,EAAErG;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAMgT,wBAAwB,CAAC;EAC3B5M,WAAWA,CAACwC,QAAQ,EAAEC,UAAU,EAAE;IAC9B,IAAI,CAACD,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACoK,mBAAmB,GAAG,CAAC;IAC5B,IAAI,CAACC,uBAAuB,GAAGC,CAAC,IAAIA,CAAC,CAACC,MAAM;IAC5C,IAAI,CAACC,WAAW,GAAG,CAACC,CAAC,EAAEC,CAAC,KAAM,GAAED,CAAE,GAAEC,CAAC,GAAG,CAAC,GAAI,IAAGA,CAAE,EAAC,GAAI,EAAE,EAAC;IAC1D,IAAI,CAACC,aAAa,GAAG,IAAIhT,OAAO,CAAC,CAAC;IAClC,IAAI,CAACoJ,QAAQ,GAAG,IAAIpJ,OAAO,CAAC,CAAC;EACjC;EACAmN,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAAC8F,gBAAgB,IAAIpT,SAAS,CAAC,CAAC,EAAE;MACvC,MAAM,IAAIqT,KAAK,CAAC,wEAAwE,CAAC;IAC7F;IACA,IAAI,IAAI,CAACD,gBAAgB,CAAChL,SAAS,EAAE;MACjC,MAAMkL,YAAY,GAAG,IAAI,CAACF,gBAAgB,CAAChL,SAAS,CAACkL,YAAY,IAAIjT,KAAK;MAC1ED,KAAK,CAACkT,YAAY,EAAE,IAAI,CAACH,aAAa,CAAC,CAClCzJ,IAAI,CAACnJ,SAAS,CAAC,IAAI,CAACgJ,QAAQ,CAAC,EAAE3I,GAAG,CAAC,MAAM,IAAI,CAACwS,gBAAgB,CAAChL,SAAS,CAACE,KAAK,CAAC,EAAE7H,SAAS,CAAC,IAAI,CAAC2S,gBAAgB,CAAChL,SAAS,CAACE,KAAK,CAAC,CAAC,CAClIuB,SAAS,CAACvB,KAAK,IAAI;QACpB,IAAI,CAACiL,YAAY,CAACjL,KAAK,CAAC;MAC5B,CAAC,CAAC;IACN;EACJ;EACAiL,YAAYA,CAACjL,KAAK,EAAE;IAChB,MAAMkL,UAAU,GAAGrS,QAAQ,CAACmH,KAAK,CAAC,GAAGmL,MAAM,CAACnL,KAAK,CAAC,GAAG,EAAE;IACvD,MAAMoL,YAAY,GAAG,IAAI,CAACb,uBAAuB,CAACW,UAAU,CAAC;IAC7D,MAAMG,SAAS,GAAG,IAAI,CAACX,WAAW,CAACU,YAAY,EAAE,IAAI,CAACd,mBAAmB,CAAC;IAC1E,IAAI,CAACrK,QAAQ,CAACqL,YAAY,CAAC,IAAI,CAACpL,UAAU,CAACoC,aAAa,EAAE,YAAY,EAAE+I,SAAS,CAAC;EACtF;EACAtJ,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC8I,aAAa,CAAC7I,QAAQ,CAAC,CAAC;IAC7B,IAAI,CAACf,QAAQ,CAACS,IAAI,CAAC,IAAI,CAAC;IACxB,IAAI,CAACT,QAAQ,CAACe,QAAQ,CAAC,CAAC;EAC5B;EACA;IAAS,IAAI,CAACrE,IAAI,YAAA4N,iCAAA1N,CAAA;MAAA,YAAAA,CAAA,IAAwFwM,wBAAwB,EAtsBlCpT,EAAE,CAAA+L,iBAAA,CAssBkD/L,EAAE,CAACiM,SAAS,GAtsBhEjM,EAAE,CAAA+L,iBAAA,CAssB2E/L,EAAE,CAACkM,UAAU;IAAA,CAA4C;EAAE;EACxO;IAAS,IAAI,CAACrF,IAAI,kBAvsB8E7G,EAAE,CAAA8G,iBAAA;MAAAL,IAAA,EAusBJ2M,wBAAwB;MAAArM,SAAA;MAAA2H,cAAA,WAAA6F,wCAAA9R,EAAA,EAAAC,GAAA,EAAAkM,QAAA;QAAA,IAAAnM,EAAA;UAvsBtBzC,EAAE,CAAA6O,cAAA,CAAAD,QAAA,EAusBkVhG,gBAAgB;QAAA;QAAA,IAAAnG,EAAA;UAAA,IAAAqM,EAAA;UAvsBpW9O,EAAE,CAAA+O,cAAA,CAAAD,EAAA,GAAF9O,EAAE,CAAAgP,WAAA,QAAAtM,GAAA,CAAAmR,gBAAA,GAAA/E,EAAA,CAAA0F,KAAA;QAAA;MAAA;MAAAhI,SAAA;MAAArF,MAAA;QAAAkM,mBAAA;QAAAC,uBAAA;QAAAG,WAAA;MAAA;MAAArM,UAAA;MAAAC,QAAA,GAAFrH,EAAE,CAAAsH,mBAAA;MAAAE,kBAAA,EAAAlB,GAAA;MAAAmB,KAAA;MAAAC,IAAA;MAAApE,QAAA,WAAAmR,kCAAAhS,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFzC,EAAE,CAAA6H,eAAA,CAAAxB,GAAA;UAAFrG,EAAE,CAAAmG,YAAA,EAusBsd,CAAC;QAAA;MAAA;MAAA8B,aAAA;MAAAC,eAAA;IAAA,EAAyE;EAAE;AACxoB;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAzsBoGnI,EAAE,CAAAoI,iBAAA,CAysBXgL,wBAAwB,EAAc,CAAC;IACtH3M,IAAI,EAAExG,SAAS;IACfoI,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,mBAAmB;MAC7BhF,QAAQ,EAAG,yDAAwD;MACnEoF,IAAI,EAAE;QACFoE,KAAK,EAAE;MACX,CAAC;MACD5E,eAAe,EAAE/H,uBAAuB,CAACsI,MAAM;MAC/CrB,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEX,IAAI,EAAEzG,EAAE,CAACiM;EAAU,CAAC,EAAE;IAAExF,IAAI,EAAEzG,EAAE,CAACkM;EAAW,CAAC,CAAC,EAAkB;IAAE2H,gBAAgB,EAAE,CAAC;MAC1GpN,IAAI,EAAE/F,YAAY;MAClB2H,IAAI,EAAE,CAACO,gBAAgB,EAAE;QAAE8L,MAAM,EAAE;MAAK,CAAC;IAC7C,CAAC,CAAC;IAAErB,mBAAmB,EAAE,CAAC;MACtB5M,IAAI,EAAErG;IACV,CAAC,CAAC;IAAEkT,uBAAuB,EAAE,CAAC;MAC1B7M,IAAI,EAAErG;IACV,CAAC,CAAC;IAAEqT,WAAW,EAAE,CAAC;MACdhN,IAAI,EAAErG;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAMuU,aAAa,CAAC;EAChB;IAAS,IAAI,CAACjO,IAAI,YAAAkO,sBAAAhO,CAAA;MAAA,YAAAA,CAAA,IAAwF+N,aAAa;IAAA,CAAkD;EAAE;EAC3K;IAAS,IAAI,CAACE,IAAI,kBAruB8E7U,EAAE,CAAA8U,gBAAA;MAAArO,IAAA,EAquBSkO,aAAa;MAAAhM,OAAA,GAAYyK,wBAAwB,EACpJxK,gBAAgB,EAChBsE,qBAAqB,EACrBoC,mBAAmB,EACnB/I,yBAAyB,EACzByG,uCAAuC;MAAA+H,OAAA,GAAa3B,wBAAwB,EAC5ExK,gBAAgB,EAChBsE,qBAAqB,EACrBoC,mBAAmB,EACnBtC,uCAAuC;IAAA,EAAI;EAAE;EACrD;IAAS,IAAI,CAACgI,IAAI,kBA/uB8EhV,EAAE,CAAAiV,gBAAA;MAAAtM,OAAA,GA+uBkCuE,qBAAqB,EACjJ3G,yBAAyB;IAAA,EAAI;EAAE;AAC3C;AACA;EAAA,QAAA4B,SAAA,oBAAAA,SAAA,KAlvBoGnI,EAAE,CAAAoI,iBAAA,CAkvBXuM,aAAa,EAAc,CAAC;IAC3GlO,IAAI,EAAE9F,QAAQ;IACd0H,IAAI,EAAE,CAAC;MACCM,OAAO,EAAE,CACLyK,wBAAwB,EACxBxK,gBAAgB,EAChBsE,qBAAqB,EACrBoC,mBAAmB,EACnB/I,yBAAyB,EACzByG,uCAAuC,CAC1C;MACD+H,OAAO,EAAE,CACL3B,wBAAwB,EACxBxK,gBAAgB,EAChBsE,qBAAqB,EACrBoC,mBAAmB,EACnBtC,uCAAuC;IAE/C,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASsC,mBAAmB,EAAE1G,gBAAgB,EAAEsE,qBAAqB,EAAE3G,yBAAyB,EAAEyG,uCAAuC,EAAE2H,aAAa,EAAEvB,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}