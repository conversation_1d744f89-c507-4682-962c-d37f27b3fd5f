{"ast": null, "code": "import { isDevMode } from '@angular/core';\nimport { environment } from 'ng-zorro-antd/core/environments';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst record = {};\nconst PREFIX = '[NG-ZORRO]:';\nfunction notRecorded(...args) {\n  const asRecord = args.reduce((acc, c) => acc + c.toString(), '');\n  if (record[asRecord]) {\n    return false;\n  } else {\n    record[asRecord] = true;\n    return true;\n  }\n}\nfunction consoleCommonBehavior(consoleFunc, ...args) {\n  if (environment.isTestMode || isDevMode() && notRecorded(...args)) {\n    consoleFunc(...args);\n  }\n}\n// Warning should only be printed in dev mode and only once.\nconst warn = (...args) => consoleCommonBehavior((...arg) => console.warn(PREFIX, ...arg), ...args);\n// eslint-disable-next-line @typescript-eslint/explicit-function-return-type\nconst warnDeprecation = (...args) => {\n  if (!environment.isTestMode) {\n    const stack = new Error().stack;\n    return consoleCommonBehavior((...arg) => console.warn(PREFIX, 'deprecated:', ...arg, stack), ...args);\n  } else {\n    return () => {};\n  }\n};\n// Log should only be printed in dev mode.\nconst log = (...args) => {\n  if (isDevMode()) {\n    console.log(PREFIX, ...args);\n  }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { PREFIX, log, warn, warnDeprecation };", "map": {"version": 3, "names": ["isDevMode", "environment", "record", "PREFIX", "notRecorded", "args", "asRecord", "reduce", "acc", "c", "toString", "consoleCommonBehavior", "consoleFunc", "isTestMode", "warn", "arg", "console", "warnDeprecation", "stack", "Error", "log"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-core-logger.mjs"], "sourcesContent": ["import { isDevMode } from '@angular/core';\nimport { environment } from 'ng-zorro-antd/core/environments';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst record = {};\nconst PREFIX = '[NG-ZORRO]:';\nfunction notRecorded(...args) {\n    const asRecord = args.reduce((acc, c) => acc + c.toString(), '');\n    if (record[asRecord]) {\n        return false;\n    }\n    else {\n        record[asRecord] = true;\n        return true;\n    }\n}\nfunction consoleCommonBehavior(consoleFunc, ...args) {\n    if (environment.isTestMode || (isDevMode() && notRecorded(...args))) {\n        consoleFunc(...args);\n    }\n}\n// Warning should only be printed in dev mode and only once.\nconst warn = (...args) => consoleCommonBehavior((...arg) => console.warn(PREFIX, ...arg), ...args);\n// eslint-disable-next-line @typescript-eslint/explicit-function-return-type\nconst warnDeprecation = (...args) => {\n    if (!environment.isTestMode) {\n        const stack = new Error().stack;\n        return consoleCommonBehavior((...arg) => console.warn(PREFIX, 'deprecated:', ...arg, stack), ...args);\n    }\n    else {\n        return () => { };\n    }\n};\n// Log should only be printed in dev mode.\nconst log = (...args) => {\n    if (isDevMode()) {\n        console.log(PREFIX, ...args);\n    }\n};\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { PREFIX, log, warn, warnDeprecation };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,eAAe;AACzC,SAASC,WAAW,QAAQ,iCAAiC;;AAE7D;AACA;AACA;AACA;AACA,MAAMC,MAAM,GAAG,CAAC,CAAC;AACjB,MAAMC,MAAM,GAAG,aAAa;AAC5B,SAASC,WAAWA,CAAC,GAAGC,IAAI,EAAE;EAC1B,MAAMC,QAAQ,GAAGD,IAAI,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,GAAGC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC;EAChE,IAAIR,MAAM,CAACI,QAAQ,CAAC,EAAE;IAClB,OAAO,KAAK;EAChB,CAAC,MACI;IACDJ,MAAM,CAACI,QAAQ,CAAC,GAAG,IAAI;IACvB,OAAO,IAAI;EACf;AACJ;AACA,SAASK,qBAAqBA,CAACC,WAAW,EAAE,GAAGP,IAAI,EAAE;EACjD,IAAIJ,WAAW,CAACY,UAAU,IAAKb,SAAS,CAAC,CAAC,IAAII,WAAW,CAAC,GAAGC,IAAI,CAAE,EAAE;IACjEO,WAAW,CAAC,GAAGP,IAAI,CAAC;EACxB;AACJ;AACA;AACA,MAAMS,IAAI,GAAGA,CAAC,GAAGT,IAAI,KAAKM,qBAAqB,CAAC,CAAC,GAAGI,GAAG,KAAKC,OAAO,CAACF,IAAI,CAACX,MAAM,EAAE,GAAGY,GAAG,CAAC,EAAE,GAAGV,IAAI,CAAC;AAClG;AACA,MAAMY,eAAe,GAAGA,CAAC,GAAGZ,IAAI,KAAK;EACjC,IAAI,CAACJ,WAAW,CAACY,UAAU,EAAE;IACzB,MAAMK,KAAK,GAAG,IAAIC,KAAK,CAAC,CAAC,CAACD,KAAK;IAC/B,OAAOP,qBAAqB,CAAC,CAAC,GAAGI,GAAG,KAAKC,OAAO,CAACF,IAAI,CAACX,MAAM,EAAE,aAAa,EAAE,GAAGY,GAAG,EAAEG,KAAK,CAAC,EAAE,GAAGb,IAAI,CAAC;EACzG,CAAC,MACI;IACD,OAAO,MAAM,CAAE,CAAC;EACpB;AACJ,CAAC;AACD;AACA,MAAMe,GAAG,GAAGA,CAAC,GAAGf,IAAI,KAAK;EACrB,IAAIL,SAAS,CAAC,CAAC,EAAE;IACbgB,OAAO,CAACI,GAAG,CAACjB,MAAM,EAAE,GAAGE,IAAI,CAAC;EAChC;AACJ,CAAC;;AAED;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASF,MAAM,EAAEiB,GAAG,EAAEN,IAAI,EAAEG,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}