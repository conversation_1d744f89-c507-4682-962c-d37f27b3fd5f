{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { <PERSON><PERSON> } from '@angular/core';\nlet RemoveProviderPrefixPipe = class RemoveProviderPrefixPipe {\n  transform(value) {\n    if (!value) return '';\n    const parts = value.split('_');\n    return parts.slice(1).join('_');\n  }\n};\nRemoveProviderPrefixPipe = __decorate([Pipe({\n  name: 'removeProviderPrefix',\n  standalone: true\n})], RemoveProviderPrefixPipe);\nexport { RemoveProviderPrefixPipe };", "map": {"version": 3, "names": ["<PERSON><PERSON>", "RemoveProviderPrefixPipe", "transform", "value", "parts", "split", "slice", "join", "__decorate", "name", "standalone"], "sources": ["C:\\Users\\<USER>\\source\\ai-hub\\WebApp\\src\\shared\\pipes\\remove-provider-prefix.pipe.ts"], "sourcesContent": ["import { Pipe, PipeTransform } from '@angular/core';\r\n\r\n@Pipe({\r\n  name: 'removeProviderPrefix',\r\n  standalone: true,\r\n})\r\nexport class RemoveProviderPrefixPipe implements PipeTransform {\r\n  transform(value: string): string {\r\n    if (!value) return '';\r\n    const parts = value.split('_');\r\n    return parts.slice(1).join('_');\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,IAAI,QAAuB,eAAe;AAM5C,IAAMC,wBAAwB,GAA9B,MAAMA,wBAAwB;EACnCC,SAASA,CAACC,KAAa;IACrB,IAAI,CAACA,KAAK,EAAE,OAAO,EAAE;IACrB,MAAMC,KAAK,GAAGD,KAAK,CAACE,KAAK,CAAC,GAAG,CAAC;IAC9B,OAAOD,KAAK,CAACE,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;EACjC;CACD;AANYN,wBAAwB,GAAAO,UAAA,EAJpCR,IAAI,CAAC;EACJS,IAAI,EAAE,sBAAsB;EAC5BC,UAAU,EAAE;CACb,CAAC,C,EACWT,wBAAwB,CAMpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}