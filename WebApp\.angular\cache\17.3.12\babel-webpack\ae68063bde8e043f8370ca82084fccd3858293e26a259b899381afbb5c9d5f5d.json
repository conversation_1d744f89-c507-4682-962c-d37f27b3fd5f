{"ast": null, "code": "import Duration from \"./duration.js\";\nimport Interval from \"./interval.js\";\nimport Settings from \"./settings.js\";\nimport Info from \"./info.js\";\nimport Formatter from \"./impl/formatter.js\";\nimport FixedOffsetZone from \"./zones/fixedOffsetZone.js\";\nimport Locale from \"./impl/locale.js\";\nimport { isUndefined, maybeArray, isDate, isNumber, bestBy, daysInMonth, daysInYear, isLeapYear, weeksInWeekYear, normalizeObject, roundTo, objToLocalTS, padStart } from \"./impl/util.js\";\nimport { normalizeZone } from \"./impl/zoneUtil.js\";\nimport diff from \"./impl/diff.js\";\nimport { parseRFC2822Date, parseISODate, parseHTTPDate, parseSQL } from \"./impl/regexParser.js\";\nimport { parseFromTokens, explainFromTokens, formatOptsToTokens, expandMacroTokens, TokenParser } from \"./impl/tokenParser.js\";\nimport { gregorianToWeek, weekToGregorian, gregorianToOrdinal, ordinalToGregorian, hasInvalidGregorianData, hasInvalidWeekData, hasInvalidOrdinalData, hasInvalidTimeData, usesLocalWeekValues, isoWeekdayToLocal } from \"./impl/conversions.js\";\nimport * as Formats from \"./impl/formats.js\";\nimport { InvalidArgumentError, ConflictingSpecificationError, InvalidUnitError, InvalidDateTimeError } from \"./errors.js\";\nimport Invalid from \"./impl/invalid.js\";\nconst INVALID = \"Invalid DateTime\";\nconst MAX_DATE = 8.64e15;\nfunction unsupportedZone(zone) {\n  return new Invalid(\"unsupported zone\", `the zone \"${zone.name}\" is not supported`);\n}\n\n// we cache week data on the DT object and this intermediates the cache\n/**\n * @param {DateTime} dt\n */\nfunction possiblyCachedWeekData(dt) {\n  if (dt.weekData === null) {\n    dt.weekData = gregorianToWeek(dt.c);\n  }\n  return dt.weekData;\n}\n\n/**\n * @param {DateTime} dt\n */\nfunction possiblyCachedLocalWeekData(dt) {\n  if (dt.localWeekData === null) {\n    dt.localWeekData = gregorianToWeek(dt.c, dt.loc.getMinDaysInFirstWeek(), dt.loc.getStartOfWeek());\n  }\n  return dt.localWeekData;\n}\n\n// clone really means, \"make a new object with these modifications\". all \"setters\" really use this\n// to create a new object while only changing some of the properties\nfunction clone(inst, alts) {\n  const current = {\n    ts: inst.ts,\n    zone: inst.zone,\n    c: inst.c,\n    o: inst.o,\n    loc: inst.loc,\n    invalid: inst.invalid\n  };\n  return new DateTime({\n    ...current,\n    ...alts,\n    old: current\n  });\n}\n\n// find the right offset a given local time. The o input is our guess, which determines which\n// offset we'll pick in ambiguous cases (e.g. there are two 3 AMs b/c Fallback DST)\nfunction fixOffset(localTS, o, tz) {\n  // Our UTC time is just a guess because our offset is just a guess\n  let utcGuess = localTS - o * 60 * 1000;\n\n  // Test whether the zone matches the offset for this ts\n  const o2 = tz.offset(utcGuess);\n\n  // If so, offset didn't change and we're done\n  if (o === o2) {\n    return [utcGuess, o];\n  }\n\n  // If not, change the ts by the difference in the offset\n  utcGuess -= (o2 - o) * 60 * 1000;\n\n  // If that gives us the local time we want, we're done\n  const o3 = tz.offset(utcGuess);\n  if (o2 === o3) {\n    return [utcGuess, o2];\n  }\n\n  // If it's different, we're in a hole time. The offset has changed, but the we don't adjust the time\n  return [localTS - Math.min(o2, o3) * 60 * 1000, Math.max(o2, o3)];\n}\n\n// convert an epoch timestamp into a calendar object with the given offset\nfunction tsToObj(ts, offset) {\n  ts += offset * 60 * 1000;\n  const d = new Date(ts);\n  return {\n    year: d.getUTCFullYear(),\n    month: d.getUTCMonth() + 1,\n    day: d.getUTCDate(),\n    hour: d.getUTCHours(),\n    minute: d.getUTCMinutes(),\n    second: d.getUTCSeconds(),\n    millisecond: d.getUTCMilliseconds()\n  };\n}\n\n// convert a calendar object to a epoch timestamp\nfunction objToTS(obj, offset, zone) {\n  return fixOffset(objToLocalTS(obj), offset, zone);\n}\n\n// create a new DT instance by adding a duration, adjusting for DSTs\nfunction adjustTime(inst, dur) {\n  const oPre = inst.o,\n    year = inst.c.year + Math.trunc(dur.years),\n    month = inst.c.month + Math.trunc(dur.months) + Math.trunc(dur.quarters) * 3,\n    c = {\n      ...inst.c,\n      year,\n      month,\n      day: Math.min(inst.c.day, daysInMonth(year, month)) + Math.trunc(dur.days) + Math.trunc(dur.weeks) * 7\n    },\n    millisToAdd = Duration.fromObject({\n      years: dur.years - Math.trunc(dur.years),\n      quarters: dur.quarters - Math.trunc(dur.quarters),\n      months: dur.months - Math.trunc(dur.months),\n      weeks: dur.weeks - Math.trunc(dur.weeks),\n      days: dur.days - Math.trunc(dur.days),\n      hours: dur.hours,\n      minutes: dur.minutes,\n      seconds: dur.seconds,\n      milliseconds: dur.milliseconds\n    }).as(\"milliseconds\"),\n    localTS = objToLocalTS(c);\n  let [ts, o] = fixOffset(localTS, oPre, inst.zone);\n  if (millisToAdd !== 0) {\n    ts += millisToAdd;\n    // that could have changed the offset by going over a DST, but we want to keep the ts the same\n    o = inst.zone.offset(ts);\n  }\n  return {\n    ts,\n    o\n  };\n}\n\n// helper useful in turning the results of parsing into real dates\n// by handling the zone options\nfunction parseDataToDateTime(parsed, parsedZone, opts, format, text, specificOffset) {\n  const {\n    setZone,\n    zone\n  } = opts;\n  if (parsed && Object.keys(parsed).length !== 0 || parsedZone) {\n    const interpretationZone = parsedZone || zone,\n      inst = DateTime.fromObject(parsed, {\n        ...opts,\n        zone: interpretationZone,\n        specificOffset\n      });\n    return setZone ? inst : inst.setZone(zone);\n  } else {\n    return DateTime.invalid(new Invalid(\"unparsable\", `the input \"${text}\" can't be parsed as ${format}`));\n  }\n}\n\n// if you want to output a technical format (e.g. RFC 2822), this helper\n// helps handle the details\nfunction toTechFormat(dt, format, allowZ = true) {\n  return dt.isValid ? Formatter.create(Locale.create(\"en-US\"), {\n    allowZ,\n    forceSimple: true\n  }).formatDateTimeFromString(dt, format) : null;\n}\nfunction toISODate(o, extended) {\n  const longFormat = o.c.year > 9999 || o.c.year < 0;\n  let c = \"\";\n  if (longFormat && o.c.year >= 0) c += \"+\";\n  c += padStart(o.c.year, longFormat ? 6 : 4);\n  if (extended) {\n    c += \"-\";\n    c += padStart(o.c.month);\n    c += \"-\";\n    c += padStart(o.c.day);\n  } else {\n    c += padStart(o.c.month);\n    c += padStart(o.c.day);\n  }\n  return c;\n}\nfunction toISOTime(o, extended, suppressSeconds, suppressMilliseconds, includeOffset, extendedZone) {\n  let c = padStart(o.c.hour);\n  if (extended) {\n    c += \":\";\n    c += padStart(o.c.minute);\n    if (o.c.millisecond !== 0 || o.c.second !== 0 || !suppressSeconds) {\n      c += \":\";\n    }\n  } else {\n    c += padStart(o.c.minute);\n  }\n  if (o.c.millisecond !== 0 || o.c.second !== 0 || !suppressSeconds) {\n    c += padStart(o.c.second);\n    if (o.c.millisecond !== 0 || !suppressMilliseconds) {\n      c += \".\";\n      c += padStart(o.c.millisecond, 3);\n    }\n  }\n  if (includeOffset) {\n    if (o.isOffsetFixed && o.offset === 0 && !extendedZone) {\n      c += \"Z\";\n    } else if (o.o < 0) {\n      c += \"-\";\n      c += padStart(Math.trunc(-o.o / 60));\n      c += \":\";\n      c += padStart(Math.trunc(-o.o % 60));\n    } else {\n      c += \"+\";\n      c += padStart(Math.trunc(o.o / 60));\n      c += \":\";\n      c += padStart(Math.trunc(o.o % 60));\n    }\n  }\n  if (extendedZone) {\n    c += \"[\" + o.zone.ianaName + \"]\";\n  }\n  return c;\n}\n\n// defaults for unspecified units in the supported calendars\nconst defaultUnitValues = {\n    month: 1,\n    day: 1,\n    hour: 0,\n    minute: 0,\n    second: 0,\n    millisecond: 0\n  },\n  defaultWeekUnitValues = {\n    weekNumber: 1,\n    weekday: 1,\n    hour: 0,\n    minute: 0,\n    second: 0,\n    millisecond: 0\n  },\n  defaultOrdinalUnitValues = {\n    ordinal: 1,\n    hour: 0,\n    minute: 0,\n    second: 0,\n    millisecond: 0\n  };\n\n// Units in the supported calendars, sorted by bigness\nconst orderedUnits = [\"year\", \"month\", \"day\", \"hour\", \"minute\", \"second\", \"millisecond\"],\n  orderedWeekUnits = [\"weekYear\", \"weekNumber\", \"weekday\", \"hour\", \"minute\", \"second\", \"millisecond\"],\n  orderedOrdinalUnits = [\"year\", \"ordinal\", \"hour\", \"minute\", \"second\", \"millisecond\"];\n\n// standardize case and plurality in units\nfunction normalizeUnit(unit) {\n  const normalized = {\n    year: \"year\",\n    years: \"year\",\n    month: \"month\",\n    months: \"month\",\n    day: \"day\",\n    days: \"day\",\n    hour: \"hour\",\n    hours: \"hour\",\n    minute: \"minute\",\n    minutes: \"minute\",\n    quarter: \"quarter\",\n    quarters: \"quarter\",\n    second: \"second\",\n    seconds: \"second\",\n    millisecond: \"millisecond\",\n    milliseconds: \"millisecond\",\n    weekday: \"weekday\",\n    weekdays: \"weekday\",\n    weeknumber: \"weekNumber\",\n    weeksnumber: \"weekNumber\",\n    weeknumbers: \"weekNumber\",\n    weekyear: \"weekYear\",\n    weekyears: \"weekYear\",\n    ordinal: \"ordinal\"\n  }[unit.toLowerCase()];\n  if (!normalized) throw new InvalidUnitError(unit);\n  return normalized;\n}\nfunction normalizeUnitWithLocalWeeks(unit) {\n  switch (unit.toLowerCase()) {\n    case \"localweekday\":\n    case \"localweekdays\":\n      return \"localWeekday\";\n    case \"localweeknumber\":\n    case \"localweeknumbers\":\n      return \"localWeekNumber\";\n    case \"localweekyear\":\n    case \"localweekyears\":\n      return \"localWeekYear\";\n    default:\n      return normalizeUnit(unit);\n  }\n}\n\n// cache offsets for zones based on the current timestamp when this function is\n// first called. When we are handling a datetime from components like (year,\n// month, day, hour) in a time zone, we need a guess about what the timezone\n// offset is so that we can convert into a UTC timestamp. One way is to find the\n// offset of now in the zone. The actual date may have a different offset (for\n// example, if we handle a date in June while we're in December in a zone that\n// observes DST), but we can check and adjust that.\n//\n// When handling many dates, calculating the offset for now every time is\n// expensive. It's just a guess, so we can cache the offset to use even if we\n// are right on a time change boundary (we'll just correct in the other\n// direction). Using a timestamp from first read is a slight optimization for\n// handling dates close to the current date, since those dates will usually be\n// in the same offset (we could set the timestamp statically, instead). We use a\n// single timestamp for all zones to make things a bit more predictable.\n//\n// This is safe for quickDT (used by local() and utc()) because we don't fill in\n// higher-order units from tsNow (as we do in fromObject, this requires that\n// offset is calculated from tsNow).\nfunction guessOffsetForZone(zone) {\n  if (!zoneOffsetGuessCache[zone]) {\n    if (zoneOffsetTs === undefined) {\n      zoneOffsetTs = Settings.now();\n    }\n    zoneOffsetGuessCache[zone] = zone.offset(zoneOffsetTs);\n  }\n  return zoneOffsetGuessCache[zone];\n}\n\n// this is a dumbed down version of fromObject() that runs about 60% faster\n// but doesn't do any validation, makes a bunch of assumptions about what units\n// are present, and so on.\nfunction quickDT(obj, opts) {\n  const zone = normalizeZone(opts.zone, Settings.defaultZone);\n  if (!zone.isValid) {\n    return DateTime.invalid(unsupportedZone(zone));\n  }\n  const loc = Locale.fromObject(opts);\n  let ts, o;\n\n  // assume we have the higher-order units\n  if (!isUndefined(obj.year)) {\n    for (const u of orderedUnits) {\n      if (isUndefined(obj[u])) {\n        obj[u] = defaultUnitValues[u];\n      }\n    }\n    const invalid = hasInvalidGregorianData(obj) || hasInvalidTimeData(obj);\n    if (invalid) {\n      return DateTime.invalid(invalid);\n    }\n    const offsetProvis = guessOffsetForZone(zone);\n    [ts, o] = objToTS(obj, offsetProvis, zone);\n  } else {\n    ts = Settings.now();\n  }\n  return new DateTime({\n    ts,\n    zone,\n    loc,\n    o\n  });\n}\nfunction diffRelative(start, end, opts) {\n  const round = isUndefined(opts.round) ? true : opts.round,\n    format = (c, unit) => {\n      c = roundTo(c, round || opts.calendary ? 0 : 2, true);\n      const formatter = end.loc.clone(opts).relFormatter(opts);\n      return formatter.format(c, unit);\n    },\n    differ = unit => {\n      if (opts.calendary) {\n        if (!end.hasSame(start, unit)) {\n          return end.startOf(unit).diff(start.startOf(unit), unit).get(unit);\n        } else return 0;\n      } else {\n        return end.diff(start, unit).get(unit);\n      }\n    };\n  if (opts.unit) {\n    return format(differ(opts.unit), opts.unit);\n  }\n  for (const unit of opts.units) {\n    const count = differ(unit);\n    if (Math.abs(count) >= 1) {\n      return format(count, unit);\n    }\n  }\n  return format(start > end ? -0 : 0, opts.units[opts.units.length - 1]);\n}\nfunction lastOpts(argList) {\n  let opts = {},\n    args;\n  if (argList.length > 0 && typeof argList[argList.length - 1] === \"object\") {\n    opts = argList[argList.length - 1];\n    args = Array.from(argList).slice(0, argList.length - 1);\n  } else {\n    args = Array.from(argList);\n  }\n  return [opts, args];\n}\n\n/**\n * Timestamp to use for cached zone offset guesses (exposed for test)\n */\nlet zoneOffsetTs;\n/**\n * Cache for zone offset guesses (exposed for test).\n *\n * This optimizes quickDT via guessOffsetForZone to avoid repeated calls of\n * zone.offset().\n */\nlet zoneOffsetGuessCache = {};\n\n/**\n * A DateTime is an immutable data structure representing a specific date and time and accompanying methods. It contains class and instance methods for creating, parsing, interrogating, transforming, and formatting them.\n *\n * A DateTime comprises of:\n * * A timestamp. Each DateTime instance refers to a specific millisecond of the Unix epoch.\n * * A time zone. Each instance is considered in the context of a specific zone (by default the local system's zone).\n * * Configuration properties that effect how output strings are formatted, such as `locale`, `numberingSystem`, and `outputCalendar`.\n *\n * Here is a brief overview of the most commonly used functionality it provides:\n *\n * * **Creation**: To create a DateTime from its components, use one of its factory class methods: {@link DateTime.local}, {@link DateTime.utc}, and (most flexibly) {@link DateTime.fromObject}. To create one from a standard string format, use {@link DateTime.fromISO}, {@link DateTime.fromHTTP}, and {@link DateTime.fromRFC2822}. To create one from a custom string format, use {@link DateTime.fromFormat}. To create one from a native JS date, use {@link DateTime.fromJSDate}.\n * * **Gregorian calendar and time**: To examine the Gregorian properties of a DateTime individually (i.e as opposed to collectively through {@link DateTime#toObject}), use the {@link DateTime#year}, {@link DateTime#month},\n * {@link DateTime#day}, {@link DateTime#hour}, {@link DateTime#minute}, {@link DateTime#second}, {@link DateTime#millisecond} accessors.\n * * **Week calendar**: For ISO week calendar attributes, see the {@link DateTime#weekYear}, {@link DateTime#weekNumber}, and {@link DateTime#weekday} accessors.\n * * **Configuration** See the {@link DateTime#locale} and {@link DateTime#numberingSystem} accessors.\n * * **Transformation**: To transform the DateTime into other DateTimes, use {@link DateTime#set}, {@link DateTime#reconfigure}, {@link DateTime#setZone}, {@link DateTime#setLocale}, {@link DateTime.plus}, {@link DateTime#minus}, {@link DateTime#endOf}, {@link DateTime#startOf}, {@link DateTime#toUTC}, and {@link DateTime#toLocal}.\n * * **Output**: To convert the DateTime to other representations, use the {@link DateTime#toRelative}, {@link DateTime#toRelativeCalendar}, {@link DateTime#toJSON}, {@link DateTime#toISO}, {@link DateTime#toHTTP}, {@link DateTime#toObject}, {@link DateTime#toRFC2822}, {@link DateTime#toString}, {@link DateTime#toLocaleString}, {@link DateTime#toFormat}, {@link DateTime#toMillis} and {@link DateTime#toJSDate}.\n *\n * There's plenty others documented below. In addition, for more information on subtler topics like internationalization, time zones, alternative calendars, validity, and so on, see the external documentation.\n */\nexport default class DateTime {\n  /**\n   * @access private\n   */\n  constructor(config) {\n    const zone = config.zone || Settings.defaultZone;\n    let invalid = config.invalid || (Number.isNaN(config.ts) ? new Invalid(\"invalid input\") : null) || (!zone.isValid ? unsupportedZone(zone) : null);\n    /**\n     * @access private\n     */\n    this.ts = isUndefined(config.ts) ? Settings.now() : config.ts;\n    let c = null,\n      o = null;\n    if (!invalid) {\n      const unchanged = config.old && config.old.ts === this.ts && config.old.zone.equals(zone);\n      if (unchanged) {\n        [c, o] = [config.old.c, config.old.o];\n      } else {\n        // If an offset has been passed and we have not been called from\n        // clone(), we can trust it and avoid the offset calculation.\n        const ot = isNumber(config.o) && !config.old ? config.o : zone.offset(this.ts);\n        c = tsToObj(this.ts, ot);\n        invalid = Number.isNaN(c.year) ? new Invalid(\"invalid input\") : null;\n        c = invalid ? null : c;\n        o = invalid ? null : ot;\n      }\n    }\n\n    /**\n     * @access private\n     */\n    this._zone = zone;\n    /**\n     * @access private\n     */\n    this.loc = config.loc || Locale.create();\n    /**\n     * @access private\n     */\n    this.invalid = invalid;\n    /**\n     * @access private\n     */\n    this.weekData = null;\n    /**\n     * @access private\n     */\n    this.localWeekData = null;\n    /**\n     * @access private\n     */\n    this.c = c;\n    /**\n     * @access private\n     */\n    this.o = o;\n    /**\n     * @access private\n     */\n    this.isLuxonDateTime = true;\n  }\n\n  // CONSTRUCT\n\n  /**\n   * Create a DateTime for the current instant, in the system's time zone.\n   *\n   * Use Settings to override these default values if needed.\n   * @example DateTime.now().toISO() //~> now in the ISO format\n   * @return {DateTime}\n   */\n  static now() {\n    return new DateTime({});\n  }\n\n  /**\n   * Create a local DateTime\n   * @param {number} [year] - The calendar year. If omitted (as in, call `local()` with no arguments), the current time will be used\n   * @param {number} [month=1] - The month, 1-indexed\n   * @param {number} [day=1] - The day of the month, 1-indexed\n   * @param {number} [hour=0] - The hour of the day, in 24-hour time\n   * @param {number} [minute=0] - The minute of the hour, meaning a number between 0 and 59\n   * @param {number} [second=0] - The second of the minute, meaning a number between 0 and 59\n   * @param {number} [millisecond=0] - The millisecond of the second, meaning a number between 0 and 999\n   * @example DateTime.local()                                  //~> now\n   * @example DateTime.local({ zone: \"America/New_York\" })      //~> now, in US east coast time\n   * @example DateTime.local(2017)                              //~> 2017-01-01T00:00:00\n   * @example DateTime.local(2017, 3)                           //~> 2017-03-01T00:00:00\n   * @example DateTime.local(2017, 3, 12, { locale: \"fr\" })     //~> 2017-03-12T00:00:00, with a French locale\n   * @example DateTime.local(2017, 3, 12, 5)                    //~> 2017-03-12T05:00:00\n   * @example DateTime.local(2017, 3, 12, 5, { zone: \"utc\" })   //~> 2017-03-12T05:00:00, in UTC\n   * @example DateTime.local(2017, 3, 12, 5, 45)                //~> 2017-03-12T05:45:00\n   * @example DateTime.local(2017, 3, 12, 5, 45, 10)            //~> 2017-03-12T05:45:10\n   * @example DateTime.local(2017, 3, 12, 5, 45, 10, 765)       //~> 2017-03-12T05:45:10.765\n   * @return {DateTime}\n   */\n  static local() {\n    const [opts, args] = lastOpts(arguments),\n      [year, month, day, hour, minute, second, millisecond] = args;\n    return quickDT({\n      year,\n      month,\n      day,\n      hour,\n      minute,\n      second,\n      millisecond\n    }, opts);\n  }\n\n  /**\n   * Create a DateTime in UTC\n   * @param {number} [year] - The calendar year. If omitted (as in, call `utc()` with no arguments), the current time will be used\n   * @param {number} [month=1] - The month, 1-indexed\n   * @param {number} [day=1] - The day of the month\n   * @param {number} [hour=0] - The hour of the day, in 24-hour time\n   * @param {number} [minute=0] - The minute of the hour, meaning a number between 0 and 59\n   * @param {number} [second=0] - The second of the minute, meaning a number between 0 and 59\n   * @param {number} [millisecond=0] - The millisecond of the second, meaning a number between 0 and 999\n   * @param {Object} options - configuration options for the DateTime\n   * @param {string} [options.locale] - a locale to set on the resulting DateTime instance\n   * @param {string} [options.outputCalendar] - the output calendar to set on the resulting DateTime instance\n   * @param {string} [options.numberingSystem] - the numbering system to set on the resulting DateTime instance\n   * @param {string} [options.weekSettings] - the week settings to set on the resulting DateTime instance\n   * @example DateTime.utc()                                              //~> now\n   * @example DateTime.utc(2017)                                          //~> 2017-01-01T00:00:00Z\n   * @example DateTime.utc(2017, 3)                                       //~> 2017-03-01T00:00:00Z\n   * @example DateTime.utc(2017, 3, 12)                                   //~> 2017-03-12T00:00:00Z\n   * @example DateTime.utc(2017, 3, 12, 5)                                //~> 2017-03-12T05:00:00Z\n   * @example DateTime.utc(2017, 3, 12, 5, 45)                            //~> 2017-03-12T05:45:00Z\n   * @example DateTime.utc(2017, 3, 12, 5, 45, { locale: \"fr\" })          //~> 2017-03-12T05:45:00Z with a French locale\n   * @example DateTime.utc(2017, 3, 12, 5, 45, 10)                        //~> 2017-03-12T05:45:10Z\n   * @example DateTime.utc(2017, 3, 12, 5, 45, 10, 765, { locale: \"fr\" }) //~> 2017-03-12T05:45:10.765Z with a French locale\n   * @return {DateTime}\n   */\n  static utc() {\n    const [opts, args] = lastOpts(arguments),\n      [year, month, day, hour, minute, second, millisecond] = args;\n    opts.zone = FixedOffsetZone.utcInstance;\n    return quickDT({\n      year,\n      month,\n      day,\n      hour,\n      minute,\n      second,\n      millisecond\n    }, opts);\n  }\n\n  /**\n   * Create a DateTime from a JavaScript Date object. Uses the default zone.\n   * @param {Date} date - a JavaScript Date object\n   * @param {Object} options - configuration options for the DateTime\n   * @param {string|Zone} [options.zone='local'] - the zone to place the DateTime into\n   * @return {DateTime}\n   */\n  static fromJSDate(date, options = {}) {\n    const ts = isDate(date) ? date.valueOf() : NaN;\n    if (Number.isNaN(ts)) {\n      return DateTime.invalid(\"invalid input\");\n    }\n    const zoneToUse = normalizeZone(options.zone, Settings.defaultZone);\n    if (!zoneToUse.isValid) {\n      return DateTime.invalid(unsupportedZone(zoneToUse));\n    }\n    return new DateTime({\n      ts: ts,\n      zone: zoneToUse,\n      loc: Locale.fromObject(options)\n    });\n  }\n\n  /**\n   * Create a DateTime from a number of milliseconds since the epoch (meaning since 1 January 1970 00:00:00 UTC). Uses the default zone.\n   * @param {number} milliseconds - a number of milliseconds since 1970 UTC\n   * @param {Object} options - configuration options for the DateTime\n   * @param {string|Zone} [options.zone='local'] - the zone to place the DateTime into\n   * @param {string} [options.locale] - a locale to set on the resulting DateTime instance\n   * @param {string} options.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @param {string} options.numberingSystem - the numbering system to set on the resulting DateTime instance\n   * @param {string} options.weekSettings - the week settings to set on the resulting DateTime instance\n   * @return {DateTime}\n   */\n  static fromMillis(milliseconds, options = {}) {\n    if (!isNumber(milliseconds)) {\n      throw new InvalidArgumentError(`fromMillis requires a numerical input, but received a ${typeof milliseconds} with value ${milliseconds}`);\n    } else if (milliseconds < -MAX_DATE || milliseconds > MAX_DATE) {\n      // this isn't perfect because we can still end up out of range because of additional shifting, but it's a start\n      return DateTime.invalid(\"Timestamp out of range\");\n    } else {\n      return new DateTime({\n        ts: milliseconds,\n        zone: normalizeZone(options.zone, Settings.defaultZone),\n        loc: Locale.fromObject(options)\n      });\n    }\n  }\n\n  /**\n   * Create a DateTime from a number of seconds since the epoch (meaning since 1 January 1970 00:00:00 UTC). Uses the default zone.\n   * @param {number} seconds - a number of seconds since 1970 UTC\n   * @param {Object} options - configuration options for the DateTime\n   * @param {string|Zone} [options.zone='local'] - the zone to place the DateTime into\n   * @param {string} [options.locale] - a locale to set on the resulting DateTime instance\n   * @param {string} options.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @param {string} options.numberingSystem - the numbering system to set on the resulting DateTime instance\n   * @param {string} options.weekSettings - the week settings to set on the resulting DateTime instance\n   * @return {DateTime}\n   */\n  static fromSeconds(seconds, options = {}) {\n    if (!isNumber(seconds)) {\n      throw new InvalidArgumentError(\"fromSeconds requires a numerical input\");\n    } else {\n      return new DateTime({\n        ts: seconds * 1000,\n        zone: normalizeZone(options.zone, Settings.defaultZone),\n        loc: Locale.fromObject(options)\n      });\n    }\n  }\n\n  /**\n   * Create a DateTime from a JavaScript object with keys like 'year' and 'hour' with reasonable defaults.\n   * @param {Object} obj - the object to create the DateTime from\n   * @param {number} obj.year - a year, such as 1987\n   * @param {number} obj.month - a month, 1-12\n   * @param {number} obj.day - a day of the month, 1-31, depending on the month\n   * @param {number} obj.ordinal - day of the year, 1-365 or 366\n   * @param {number} obj.weekYear - an ISO week year\n   * @param {number} obj.weekNumber - an ISO week number, between 1 and 52 or 53, depending on the year\n   * @param {number} obj.weekday - an ISO weekday, 1-7, where 1 is Monday and 7 is Sunday\n   * @param {number} obj.localWeekYear - a week year, according to the locale\n   * @param {number} obj.localWeekNumber - a week number, between 1 and 52 or 53, depending on the year, according to the locale\n   * @param {number} obj.localWeekday - a weekday, 1-7, where 1 is the first and 7 is the last day of the week, according to the locale\n   * @param {number} obj.hour - hour of the day, 0-23\n   * @param {number} obj.minute - minute of the hour, 0-59\n   * @param {number} obj.second - second of the minute, 0-59\n   * @param {number} obj.millisecond - millisecond of the second, 0-999\n   * @param {Object} opts - options for creating this DateTime\n   * @param {string|Zone} [opts.zone='local'] - interpret the numbers in the context of a particular zone. Can take any value taken as the first argument to setZone()\n   * @param {string} [opts.locale='system\\'s locale'] - a locale to set on the resulting DateTime instance\n   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @param {string} opts.numberingSystem - the numbering system to set on the resulting DateTime instance\n   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance\n   * @example DateTime.fromObject({ year: 1982, month: 5, day: 25}).toISODate() //=> '1982-05-25'\n   * @example DateTime.fromObject({ year: 1982 }).toISODate() //=> '1982-01-01'\n   * @example DateTime.fromObject({ hour: 10, minute: 26, second: 6 }) //~> today at 10:26:06\n   * @example DateTime.fromObject({ hour: 10, minute: 26, second: 6 }, { zone: 'utc' }),\n   * @example DateTime.fromObject({ hour: 10, minute: 26, second: 6 }, { zone: 'local' })\n   * @example DateTime.fromObject({ hour: 10, minute: 26, second: 6 }, { zone: 'America/New_York' })\n   * @example DateTime.fromObject({ weekYear: 2016, weekNumber: 2, weekday: 3 }).toISODate() //=> '2016-01-13'\n   * @example DateTime.fromObject({ localWeekYear: 2022, localWeekNumber: 1, localWeekday: 1 }, { locale: \"en-US\" }).toISODate() //=> '2021-12-26'\n   * @return {DateTime}\n   */\n  static fromObject(obj, opts = {}) {\n    obj = obj || {};\n    const zoneToUse = normalizeZone(opts.zone, Settings.defaultZone);\n    if (!zoneToUse.isValid) {\n      return DateTime.invalid(unsupportedZone(zoneToUse));\n    }\n    const loc = Locale.fromObject(opts);\n    const normalized = normalizeObject(obj, normalizeUnitWithLocalWeeks);\n    const {\n      minDaysInFirstWeek,\n      startOfWeek\n    } = usesLocalWeekValues(normalized, loc);\n    const tsNow = Settings.now(),\n      offsetProvis = !isUndefined(opts.specificOffset) ? opts.specificOffset : zoneToUse.offset(tsNow),\n      containsOrdinal = !isUndefined(normalized.ordinal),\n      containsGregorYear = !isUndefined(normalized.year),\n      containsGregorMD = !isUndefined(normalized.month) || !isUndefined(normalized.day),\n      containsGregor = containsGregorYear || containsGregorMD,\n      definiteWeekDef = normalized.weekYear || normalized.weekNumber;\n\n    // cases:\n    // just a weekday -> this week's instance of that weekday, no worries\n    // (gregorian data or ordinal) + (weekYear or weekNumber) -> error\n    // (gregorian month or day) + ordinal -> error\n    // otherwise just use weeks or ordinals or gregorian, depending on what's specified\n\n    if ((containsGregor || containsOrdinal) && definiteWeekDef) {\n      throw new ConflictingSpecificationError(\"Can't mix weekYear/weekNumber units with year/month/day or ordinals\");\n    }\n    if (containsGregorMD && containsOrdinal) {\n      throw new ConflictingSpecificationError(\"Can't mix ordinal dates with month/day\");\n    }\n    const useWeekData = definiteWeekDef || normalized.weekday && !containsGregor;\n\n    // configure ourselves to deal with gregorian dates or week stuff\n    let units,\n      defaultValues,\n      objNow = tsToObj(tsNow, offsetProvis);\n    if (useWeekData) {\n      units = orderedWeekUnits;\n      defaultValues = defaultWeekUnitValues;\n      objNow = gregorianToWeek(objNow, minDaysInFirstWeek, startOfWeek);\n    } else if (containsOrdinal) {\n      units = orderedOrdinalUnits;\n      defaultValues = defaultOrdinalUnitValues;\n      objNow = gregorianToOrdinal(objNow);\n    } else {\n      units = orderedUnits;\n      defaultValues = defaultUnitValues;\n    }\n\n    // set default values for missing stuff\n    let foundFirst = false;\n    for (const u of units) {\n      const v = normalized[u];\n      if (!isUndefined(v)) {\n        foundFirst = true;\n      } else if (foundFirst) {\n        normalized[u] = defaultValues[u];\n      } else {\n        normalized[u] = objNow[u];\n      }\n    }\n\n    // make sure the values we have are in range\n    const higherOrderInvalid = useWeekData ? hasInvalidWeekData(normalized, minDaysInFirstWeek, startOfWeek) : containsOrdinal ? hasInvalidOrdinalData(normalized) : hasInvalidGregorianData(normalized),\n      invalid = higherOrderInvalid || hasInvalidTimeData(normalized);\n    if (invalid) {\n      return DateTime.invalid(invalid);\n    }\n\n    // compute the actual time\n    const gregorian = useWeekData ? weekToGregorian(normalized, minDaysInFirstWeek, startOfWeek) : containsOrdinal ? ordinalToGregorian(normalized) : normalized,\n      [tsFinal, offsetFinal] = objToTS(gregorian, offsetProvis, zoneToUse),\n      inst = new DateTime({\n        ts: tsFinal,\n        zone: zoneToUse,\n        o: offsetFinal,\n        loc\n      });\n\n    // gregorian data + weekday serves only to validate\n    if (normalized.weekday && containsGregor && obj.weekday !== inst.weekday) {\n      return DateTime.invalid(\"mismatched weekday\", `you can't specify both a weekday of ${normalized.weekday} and a date of ${inst.toISO()}`);\n    }\n    if (!inst.isValid) {\n      return DateTime.invalid(inst.invalid);\n    }\n    return inst;\n  }\n\n  /**\n   * Create a DateTime from an ISO 8601 string\n   * @param {string} text - the ISO string\n   * @param {Object} opts - options to affect the creation\n   * @param {string|Zone} [opts.zone='local'] - use this zone if no offset is specified in the input string itself. Will also convert the time to this zone\n   * @param {boolean} [opts.setZone=false] - override the zone with a fixed-offset zone specified in the string itself, if it specifies one\n   * @param {string} [opts.locale='system's locale'] - a locale to set on the resulting DateTime instance\n   * @param {string} [opts.outputCalendar] - the output calendar to set on the resulting DateTime instance\n   * @param {string} [opts.numberingSystem] - the numbering system to set on the resulting DateTime instance\n   * @param {string} [opts.weekSettings] - the week settings to set on the resulting DateTime instance\n   * @example DateTime.fromISO('2016-05-25T09:08:34.123')\n   * @example DateTime.fromISO('2016-05-25T09:08:34.123+06:00')\n   * @example DateTime.fromISO('2016-05-25T09:08:34.123+06:00', {setZone: true})\n   * @example DateTime.fromISO('2016-05-25T09:08:34.123', {zone: 'utc'})\n   * @example DateTime.fromISO('2016-W05-4')\n   * @return {DateTime}\n   */\n  static fromISO(text, opts = {}) {\n    const [vals, parsedZone] = parseISODate(text);\n    return parseDataToDateTime(vals, parsedZone, opts, \"ISO 8601\", text);\n  }\n\n  /**\n   * Create a DateTime from an RFC 2822 string\n   * @param {string} text - the RFC 2822 string\n   * @param {Object} opts - options to affect the creation\n   * @param {string|Zone} [opts.zone='local'] - convert the time to this zone. Since the offset is always specified in the string itself, this has no effect on the interpretation of string, merely the zone the resulting DateTime is expressed in.\n   * @param {boolean} [opts.setZone=false] - override the zone with a fixed-offset zone specified in the string itself, if it specifies one\n   * @param {string} [opts.locale='system's locale'] - a locale to set on the resulting DateTime instance\n   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @param {string} opts.numberingSystem - the numbering system to set on the resulting DateTime instance\n   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance\n   * @example DateTime.fromRFC2822('25 Nov 2016 13:23:12 GMT')\n   * @example DateTime.fromRFC2822('Fri, 25 Nov 2016 13:23:12 +0600')\n   * @example DateTime.fromRFC2822('25 Nov 2016 13:23 Z')\n   * @return {DateTime}\n   */\n  static fromRFC2822(text, opts = {}) {\n    const [vals, parsedZone] = parseRFC2822Date(text);\n    return parseDataToDateTime(vals, parsedZone, opts, \"RFC 2822\", text);\n  }\n\n  /**\n   * Create a DateTime from an HTTP header date\n   * @see https://www.w3.org/Protocols/rfc2616/rfc2616-sec3.html#sec3.3.1\n   * @param {string} text - the HTTP header date\n   * @param {Object} opts - options to affect the creation\n   * @param {string|Zone} [opts.zone='local'] - convert the time to this zone. Since HTTP dates are always in UTC, this has no effect on the interpretation of string, merely the zone the resulting DateTime is expressed in.\n   * @param {boolean} [opts.setZone=false] - override the zone with the fixed-offset zone specified in the string. For HTTP dates, this is always UTC, so this option is equivalent to setting the `zone` option to 'utc', but this option is included for consistency with similar methods.\n   * @param {string} [opts.locale='system's locale'] - a locale to set on the resulting DateTime instance\n   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @param {string} opts.numberingSystem - the numbering system to set on the resulting DateTime instance\n   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance\n   * @example DateTime.fromHTTP('Sun, 06 Nov 1994 08:49:37 GMT')\n   * @example DateTime.fromHTTP('Sunday, 06-Nov-94 08:49:37 GMT')\n   * @example DateTime.fromHTTP('Sun Nov  6 08:49:37 1994')\n   * @return {DateTime}\n   */\n  static fromHTTP(text, opts = {}) {\n    const [vals, parsedZone] = parseHTTPDate(text);\n    return parseDataToDateTime(vals, parsedZone, opts, \"HTTP\", opts);\n  }\n\n  /**\n   * Create a DateTime from an input string and format string.\n   * Defaults to en-US if no locale has been specified, regardless of the system's locale. For a table of tokens and their interpretations, see [here](https://moment.github.io/luxon/#/parsing?id=table-of-tokens).\n   * @param {string} text - the string to parse\n   * @param {string} fmt - the format the string is expected to be in (see the link below for the formats)\n   * @param {Object} opts - options to affect the creation\n   * @param {string|Zone} [opts.zone='local'] - use this zone if no offset is specified in the input string itself. Will also convert the DateTime to this zone\n   * @param {boolean} [opts.setZone=false] - override the zone with a zone specified in the string itself, if it specifies one\n   * @param {string} [opts.locale='en-US'] - a locale string to use when parsing. Will also set the DateTime to this locale\n   * @param {string} opts.numberingSystem - the numbering system to use when parsing. Will also set the resulting DateTime to this numbering system\n   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance\n   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @return {DateTime}\n   */\n  static fromFormat(text, fmt, opts = {}) {\n    if (isUndefined(text) || isUndefined(fmt)) {\n      throw new InvalidArgumentError(\"fromFormat requires an input string and a format\");\n    }\n    const {\n        locale = null,\n        numberingSystem = null\n      } = opts,\n      localeToUse = Locale.fromOpts({\n        locale,\n        numberingSystem,\n        defaultToEN: true\n      }),\n      [vals, parsedZone, specificOffset, invalid] = parseFromTokens(localeToUse, text, fmt);\n    if (invalid) {\n      return DateTime.invalid(invalid);\n    } else {\n      return parseDataToDateTime(vals, parsedZone, opts, `format ${fmt}`, text, specificOffset);\n    }\n  }\n\n  /**\n   * @deprecated use fromFormat instead\n   */\n  static fromString(text, fmt, opts = {}) {\n    return DateTime.fromFormat(text, fmt, opts);\n  }\n\n  /**\n   * Create a DateTime from a SQL date, time, or datetime\n   * Defaults to en-US if no locale has been specified, regardless of the system's locale\n   * @param {string} text - the string to parse\n   * @param {Object} opts - options to affect the creation\n   * @param {string|Zone} [opts.zone='local'] - use this zone if no offset is specified in the input string itself. Will also convert the DateTime to this zone\n   * @param {boolean} [opts.setZone=false] - override the zone with a zone specified in the string itself, if it specifies one\n   * @param {string} [opts.locale='en-US'] - a locale string to use when parsing. Will also set the DateTime to this locale\n   * @param {string} opts.numberingSystem - the numbering system to use when parsing. Will also set the resulting DateTime to this numbering system\n   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance\n   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @example DateTime.fromSQL('2017-05-15')\n   * @example DateTime.fromSQL('2017-05-15 09:12:34')\n   * @example DateTime.fromSQL('2017-05-15 09:12:34.342')\n   * @example DateTime.fromSQL('2017-05-15 09:12:34.342+06:00')\n   * @example DateTime.fromSQL('2017-05-15 09:12:34.342 America/Los_Angeles')\n   * @example DateTime.fromSQL('2017-05-15 09:12:34.342 America/Los_Angeles', { setZone: true })\n   * @example DateTime.fromSQL('2017-05-15 09:12:34.342', { zone: 'America/Los_Angeles' })\n   * @example DateTime.fromSQL('09:12:34.342')\n   * @return {DateTime}\n   */\n  static fromSQL(text, opts = {}) {\n    const [vals, parsedZone] = parseSQL(text);\n    return parseDataToDateTime(vals, parsedZone, opts, \"SQL\", text);\n  }\n\n  /**\n   * Create an invalid DateTime.\n   * @param {string} reason - simple string of why this DateTime is invalid. Should not contain parameters or anything else data-dependent.\n   * @param {string} [explanation=null] - longer explanation, may include parameters and other useful debugging information\n   * @return {DateTime}\n   */\n  static invalid(reason, explanation = null) {\n    if (!reason) {\n      throw new InvalidArgumentError(\"need to specify a reason the DateTime is invalid\");\n    }\n    const invalid = reason instanceof Invalid ? reason : new Invalid(reason, explanation);\n    if (Settings.throwOnInvalid) {\n      throw new InvalidDateTimeError(invalid);\n    } else {\n      return new DateTime({\n        invalid\n      });\n    }\n  }\n\n  /**\n   * Check if an object is an instance of DateTime. Works across context boundaries\n   * @param {object} o\n   * @return {boolean}\n   */\n  static isDateTime(o) {\n    return o && o.isLuxonDateTime || false;\n  }\n\n  /**\n   * Produce the format string for a set of options\n   * @param formatOpts\n   * @param localeOpts\n   * @returns {string}\n   */\n  static parseFormatForOpts(formatOpts, localeOpts = {}) {\n    const tokenList = formatOptsToTokens(formatOpts, Locale.fromObject(localeOpts));\n    return !tokenList ? null : tokenList.map(t => t ? t.val : null).join(\"\");\n  }\n\n  /**\n   * Produce the the fully expanded format token for the locale\n   * Does NOT quote characters, so quoted tokens will not round trip correctly\n   * @param fmt\n   * @param localeOpts\n   * @returns {string}\n   */\n  static expandFormat(fmt, localeOpts = {}) {\n    const expanded = expandMacroTokens(Formatter.parseFormat(fmt), Locale.fromObject(localeOpts));\n    return expanded.map(t => t.val).join(\"\");\n  }\n  static resetCache() {\n    zoneOffsetTs = undefined;\n    zoneOffsetGuessCache = {};\n  }\n\n  // INFO\n\n  /**\n   * Get the value of unit.\n   * @param {string} unit - a unit such as 'minute' or 'day'\n   * @example DateTime.local(2017, 7, 4).get('month'); //=> 7\n   * @example DateTime.local(2017, 7, 4).get('day'); //=> 4\n   * @return {number}\n   */\n  get(unit) {\n    return this[unit];\n  }\n\n  /**\n   * Returns whether the DateTime is valid. Invalid DateTimes occur when:\n   * * The DateTime was created from invalid calendar information, such as the 13th month or February 30\n   * * The DateTime was created by an operation on another invalid date\n   * @type {boolean}\n   */\n  get isValid() {\n    return this.invalid === null;\n  }\n\n  /**\n   * Returns an error code if this DateTime is invalid, or null if the DateTime is valid\n   * @type {string}\n   */\n  get invalidReason() {\n    return this.invalid ? this.invalid.reason : null;\n  }\n\n  /**\n   * Returns an explanation of why this DateTime became invalid, or null if the DateTime is valid\n   * @type {string}\n   */\n  get invalidExplanation() {\n    return this.invalid ? this.invalid.explanation : null;\n  }\n\n  /**\n   * Get the locale of a DateTime, such 'en-GB'. The locale is used when formatting the DateTime\n   *\n   * @type {string}\n   */\n  get locale() {\n    return this.isValid ? this.loc.locale : null;\n  }\n\n  /**\n   * Get the numbering system of a DateTime, such 'beng'. The numbering system is used when formatting the DateTime\n   *\n   * @type {string}\n   */\n  get numberingSystem() {\n    return this.isValid ? this.loc.numberingSystem : null;\n  }\n\n  /**\n   * Get the output calendar of a DateTime, such 'islamic'. The output calendar is used when formatting the DateTime\n   *\n   * @type {string}\n   */\n  get outputCalendar() {\n    return this.isValid ? this.loc.outputCalendar : null;\n  }\n\n  /**\n   * Get the time zone associated with this DateTime.\n   * @type {Zone}\n   */\n  get zone() {\n    return this._zone;\n  }\n\n  /**\n   * Get the name of the time zone.\n   * @type {string}\n   */\n  get zoneName() {\n    return this.isValid ? this.zone.name : null;\n  }\n\n  /**\n   * Get the year\n   * @example DateTime.local(2017, 5, 25).year //=> 2017\n   * @type {number}\n   */\n  get year() {\n    return this.isValid ? this.c.year : NaN;\n  }\n\n  /**\n   * Get the quarter\n   * @example DateTime.local(2017, 5, 25).quarter //=> 2\n   * @type {number}\n   */\n  get quarter() {\n    return this.isValid ? Math.ceil(this.c.month / 3) : NaN;\n  }\n\n  /**\n   * Get the month (1-12).\n   * @example DateTime.local(2017, 5, 25).month //=> 5\n   * @type {number}\n   */\n  get month() {\n    return this.isValid ? this.c.month : NaN;\n  }\n\n  /**\n   * Get the day of the month (1-30ish).\n   * @example DateTime.local(2017, 5, 25).day //=> 25\n   * @type {number}\n   */\n  get day() {\n    return this.isValid ? this.c.day : NaN;\n  }\n\n  /**\n   * Get the hour of the day (0-23).\n   * @example DateTime.local(2017, 5, 25, 9).hour //=> 9\n   * @type {number}\n   */\n  get hour() {\n    return this.isValid ? this.c.hour : NaN;\n  }\n\n  /**\n   * Get the minute of the hour (0-59).\n   * @example DateTime.local(2017, 5, 25, 9, 30).minute //=> 30\n   * @type {number}\n   */\n  get minute() {\n    return this.isValid ? this.c.minute : NaN;\n  }\n\n  /**\n   * Get the second of the minute (0-59).\n   * @example DateTime.local(2017, 5, 25, 9, 30, 52).second //=> 52\n   * @type {number}\n   */\n  get second() {\n    return this.isValid ? this.c.second : NaN;\n  }\n\n  /**\n   * Get the millisecond of the second (0-999).\n   * @example DateTime.local(2017, 5, 25, 9, 30, 52, 654).millisecond //=> 654\n   * @type {number}\n   */\n  get millisecond() {\n    return this.isValid ? this.c.millisecond : NaN;\n  }\n\n  /**\n   * Get the week year\n   * @see https://en.wikipedia.org/wiki/ISO_week_date\n   * @example DateTime.local(2014, 12, 31).weekYear //=> 2015\n   * @type {number}\n   */\n  get weekYear() {\n    return this.isValid ? possiblyCachedWeekData(this).weekYear : NaN;\n  }\n\n  /**\n   * Get the week number of the week year (1-52ish).\n   * @see https://en.wikipedia.org/wiki/ISO_week_date\n   * @example DateTime.local(2017, 5, 25).weekNumber //=> 21\n   * @type {number}\n   */\n  get weekNumber() {\n    return this.isValid ? possiblyCachedWeekData(this).weekNumber : NaN;\n  }\n\n  /**\n   * Get the day of the week.\n   * 1 is Monday and 7 is Sunday\n   * @see https://en.wikipedia.org/wiki/ISO_week_date\n   * @example DateTime.local(2014, 11, 31).weekday //=> 4\n   * @type {number}\n   */\n  get weekday() {\n    return this.isValid ? possiblyCachedWeekData(this).weekday : NaN;\n  }\n\n  /**\n   * Returns true if this date is on a weekend according to the locale, false otherwise\n   * @returns {boolean}\n   */\n  get isWeekend() {\n    return this.isValid && this.loc.getWeekendDays().includes(this.weekday);\n  }\n\n  /**\n   * Get the day of the week according to the locale.\n   * 1 is the first day of the week and 7 is the last day of the week.\n   * If the locale assigns Sunday as the first day of the week, then a date which is a Sunday will return 1,\n   * @returns {number}\n   */\n  get localWeekday() {\n    return this.isValid ? possiblyCachedLocalWeekData(this).weekday : NaN;\n  }\n\n  /**\n   * Get the week number of the week year according to the locale. Different locales assign week numbers differently,\n   * because the week can start on different days of the week (see localWeekday) and because a different number of days\n   * is required for a week to count as the first week of a year.\n   * @returns {number}\n   */\n  get localWeekNumber() {\n    return this.isValid ? possiblyCachedLocalWeekData(this).weekNumber : NaN;\n  }\n\n  /**\n   * Get the week year according to the locale. Different locales assign week numbers (and therefor week years)\n   * differently, see localWeekNumber.\n   * @returns {number}\n   */\n  get localWeekYear() {\n    return this.isValid ? possiblyCachedLocalWeekData(this).weekYear : NaN;\n  }\n\n  /**\n   * Get the ordinal (meaning the day of the year)\n   * @example DateTime.local(2017, 5, 25).ordinal //=> 145\n   * @type {number|DateTime}\n   */\n  get ordinal() {\n    return this.isValid ? gregorianToOrdinal(this.c).ordinal : NaN;\n  }\n\n  /**\n   * Get the human readable short month name, such as 'Oct'.\n   * Defaults to the system's locale if no locale has been specified\n   * @example DateTime.local(2017, 10, 30).monthShort //=> Oct\n   * @type {string}\n   */\n  get monthShort() {\n    return this.isValid ? Info.months(\"short\", {\n      locObj: this.loc\n    })[this.month - 1] : null;\n  }\n\n  /**\n   * Get the human readable long month name, such as 'October'.\n   * Defaults to the system's locale if no locale has been specified\n   * @example DateTime.local(2017, 10, 30).monthLong //=> October\n   * @type {string}\n   */\n  get monthLong() {\n    return this.isValid ? Info.months(\"long\", {\n      locObj: this.loc\n    })[this.month - 1] : null;\n  }\n\n  /**\n   * Get the human readable short weekday, such as 'Mon'.\n   * Defaults to the system's locale if no locale has been specified\n   * @example DateTime.local(2017, 10, 30).weekdayShort //=> Mon\n   * @type {string}\n   */\n  get weekdayShort() {\n    return this.isValid ? Info.weekdays(\"short\", {\n      locObj: this.loc\n    })[this.weekday - 1] : null;\n  }\n\n  /**\n   * Get the human readable long weekday, such as 'Monday'.\n   * Defaults to the system's locale if no locale has been specified\n   * @example DateTime.local(2017, 10, 30).weekdayLong //=> Monday\n   * @type {string}\n   */\n  get weekdayLong() {\n    return this.isValid ? Info.weekdays(\"long\", {\n      locObj: this.loc\n    })[this.weekday - 1] : null;\n  }\n\n  /**\n   * Get the UTC offset of this DateTime in minutes\n   * @example DateTime.now().offset //=> -240\n   * @example DateTime.utc().offset //=> 0\n   * @type {number}\n   */\n  get offset() {\n    return this.isValid ? +this.o : NaN;\n  }\n\n  /**\n   * Get the short human name for the zone's current offset, for example \"EST\" or \"EDT\".\n   * Defaults to the system's locale if no locale has been specified\n   * @type {string}\n   */\n  get offsetNameShort() {\n    if (this.isValid) {\n      return this.zone.offsetName(this.ts, {\n        format: \"short\",\n        locale: this.locale\n      });\n    } else {\n      return null;\n    }\n  }\n\n  /**\n   * Get the long human name for the zone's current offset, for example \"Eastern Standard Time\" or \"Eastern Daylight Time\".\n   * Defaults to the system's locale if no locale has been specified\n   * @type {string}\n   */\n  get offsetNameLong() {\n    if (this.isValid) {\n      return this.zone.offsetName(this.ts, {\n        format: \"long\",\n        locale: this.locale\n      });\n    } else {\n      return null;\n    }\n  }\n\n  /**\n   * Get whether this zone's offset ever changes, as in a DST.\n   * @type {boolean}\n   */\n  get isOffsetFixed() {\n    return this.isValid ? this.zone.isUniversal : null;\n  }\n\n  /**\n   * Get whether the DateTime is in a DST.\n   * @type {boolean}\n   */\n  get isInDST() {\n    if (this.isOffsetFixed) {\n      return false;\n    } else {\n      return this.offset > this.set({\n        month: 1,\n        day: 1\n      }).offset || this.offset > this.set({\n        month: 5\n      }).offset;\n    }\n  }\n\n  /**\n   * Get those DateTimes which have the same local time as this DateTime, but a different offset from UTC\n   * in this DateTime's zone. During DST changes local time can be ambiguous, for example\n   * `2023-10-29T02:30:00` in `Europe/Berlin` can have offset `+01:00` or `+02:00`.\n   * This method will return both possible DateTimes if this DateTime's local time is ambiguous.\n   * @returns {DateTime[]}\n   */\n  getPossibleOffsets() {\n    if (!this.isValid || this.isOffsetFixed) {\n      return [this];\n    }\n    const dayMs = 86400000;\n    const minuteMs = 60000;\n    const localTS = objToLocalTS(this.c);\n    const oEarlier = this.zone.offset(localTS - dayMs);\n    const oLater = this.zone.offset(localTS + dayMs);\n    const o1 = this.zone.offset(localTS - oEarlier * minuteMs);\n    const o2 = this.zone.offset(localTS - oLater * minuteMs);\n    if (o1 === o2) {\n      return [this];\n    }\n    const ts1 = localTS - o1 * minuteMs;\n    const ts2 = localTS - o2 * minuteMs;\n    const c1 = tsToObj(ts1, o1);\n    const c2 = tsToObj(ts2, o2);\n    if (c1.hour === c2.hour && c1.minute === c2.minute && c1.second === c2.second && c1.millisecond === c2.millisecond) {\n      return [clone(this, {\n        ts: ts1\n      }), clone(this, {\n        ts: ts2\n      })];\n    }\n    return [this];\n  }\n\n  /**\n   * Returns true if this DateTime is in a leap year, false otherwise\n   * @example DateTime.local(2016).isInLeapYear //=> true\n   * @example DateTime.local(2013).isInLeapYear //=> false\n   * @type {boolean}\n   */\n  get isInLeapYear() {\n    return isLeapYear(this.year);\n  }\n\n  /**\n   * Returns the number of days in this DateTime's month\n   * @example DateTime.local(2016, 2).daysInMonth //=> 29\n   * @example DateTime.local(2016, 3).daysInMonth //=> 31\n   * @type {number}\n   */\n  get daysInMonth() {\n    return daysInMonth(this.year, this.month);\n  }\n\n  /**\n   * Returns the number of days in this DateTime's year\n   * @example DateTime.local(2016).daysInYear //=> 366\n   * @example DateTime.local(2013).daysInYear //=> 365\n   * @type {number}\n   */\n  get daysInYear() {\n    return this.isValid ? daysInYear(this.year) : NaN;\n  }\n\n  /**\n   * Returns the number of weeks in this DateTime's year\n   * @see https://en.wikipedia.org/wiki/ISO_week_date\n   * @example DateTime.local(2004).weeksInWeekYear //=> 53\n   * @example DateTime.local(2013).weeksInWeekYear //=> 52\n   * @type {number}\n   */\n  get weeksInWeekYear() {\n    return this.isValid ? weeksInWeekYear(this.weekYear) : NaN;\n  }\n\n  /**\n   * Returns the number of weeks in this DateTime's local week year\n   * @example DateTime.local(2020, 6, {locale: 'en-US'}).weeksInLocalWeekYear //=> 52\n   * @example DateTime.local(2020, 6, {locale: 'de-DE'}).weeksInLocalWeekYear //=> 53\n   * @type {number}\n   */\n  get weeksInLocalWeekYear() {\n    return this.isValid ? weeksInWeekYear(this.localWeekYear, this.loc.getMinDaysInFirstWeek(), this.loc.getStartOfWeek()) : NaN;\n  }\n\n  /**\n   * Returns the resolved Intl options for this DateTime.\n   * This is useful in understanding the behavior of formatting methods\n   * @param {Object} opts - the same options as toLocaleString\n   * @return {Object}\n   */\n  resolvedLocaleOptions(opts = {}) {\n    const {\n      locale,\n      numberingSystem,\n      calendar\n    } = Formatter.create(this.loc.clone(opts), opts).resolvedOptions(this);\n    return {\n      locale,\n      numberingSystem,\n      outputCalendar: calendar\n    };\n  }\n\n  // TRANSFORM\n\n  /**\n   * \"Set\" the DateTime's zone to UTC. Returns a newly-constructed DateTime.\n   *\n   * Equivalent to {@link DateTime#setZone}('utc')\n   * @param {number} [offset=0] - optionally, an offset from UTC in minutes\n   * @param {Object} [opts={}] - options to pass to `setZone()`\n   * @return {DateTime}\n   */\n  toUTC(offset = 0, opts = {}) {\n    return this.setZone(FixedOffsetZone.instance(offset), opts);\n  }\n\n  /**\n   * \"Set\" the DateTime's zone to the host's local zone. Returns a newly-constructed DateTime.\n   *\n   * Equivalent to `setZone('local')`\n   * @return {DateTime}\n   */\n  toLocal() {\n    return this.setZone(Settings.defaultZone);\n  }\n\n  /**\n   * \"Set\" the DateTime's zone to specified zone. Returns a newly-constructed DateTime.\n   *\n   * By default, the setter keeps the underlying time the same (as in, the same timestamp), but the new instance will report different local times and consider DSTs when making computations, as with {@link DateTime#plus}. You may wish to use {@link DateTime#toLocal} and {@link DateTime#toUTC} which provide simple convenience wrappers for commonly used zones.\n   * @param {string|Zone} [zone='local'] - a zone identifier. As a string, that can be any IANA zone supported by the host environment, or a fixed-offset name of the form 'UTC+3', or the strings 'local' or 'utc'. You may also supply an instance of a {@link DateTime#Zone} class.\n   * @param {Object} opts - options\n   * @param {boolean} [opts.keepLocalTime=false] - If true, adjust the underlying time so that the local time stays the same, but in the target zone. You should rarely need this.\n   * @return {DateTime}\n   */\n  setZone(zone, {\n    keepLocalTime = false,\n    keepCalendarTime = false\n  } = {}) {\n    zone = normalizeZone(zone, Settings.defaultZone);\n    if (zone.equals(this.zone)) {\n      return this;\n    } else if (!zone.isValid) {\n      return DateTime.invalid(unsupportedZone(zone));\n    } else {\n      let newTS = this.ts;\n      if (keepLocalTime || keepCalendarTime) {\n        const offsetGuess = zone.offset(this.ts);\n        const asObj = this.toObject();\n        [newTS] = objToTS(asObj, offsetGuess, zone);\n      }\n      return clone(this, {\n        ts: newTS,\n        zone\n      });\n    }\n  }\n\n  /**\n   * \"Set\" the locale, numberingSystem, or outputCalendar. Returns a newly-constructed DateTime.\n   * @param {Object} properties - the properties to set\n   * @example DateTime.local(2017, 5, 25).reconfigure({ locale: 'en-GB' })\n   * @return {DateTime}\n   */\n  reconfigure({\n    locale,\n    numberingSystem,\n    outputCalendar\n  } = {}) {\n    const loc = this.loc.clone({\n      locale,\n      numberingSystem,\n      outputCalendar\n    });\n    return clone(this, {\n      loc\n    });\n  }\n\n  /**\n   * \"Set\" the locale. Returns a newly-constructed DateTime.\n   * Just a convenient alias for reconfigure({ locale })\n   * @example DateTime.local(2017, 5, 25).setLocale('en-GB')\n   * @return {DateTime}\n   */\n  setLocale(locale) {\n    return this.reconfigure({\n      locale\n    });\n  }\n\n  /**\n   * \"Set\" the values of specified units. Returns a newly-constructed DateTime.\n   * You can only set units with this method; for \"setting\" metadata, see {@link DateTime#reconfigure} and {@link DateTime#setZone}.\n   *\n   * This method also supports setting locale-based week units, i.e. `localWeekday`, `localWeekNumber` and `localWeekYear`.\n   * They cannot be mixed with ISO-week units like `weekday`.\n   * @param {Object} values - a mapping of units to numbers\n   * @example dt.set({ year: 2017 })\n   * @example dt.set({ hour: 8, minute: 30 })\n   * @example dt.set({ weekday: 5 })\n   * @example dt.set({ year: 2005, ordinal: 234 })\n   * @return {DateTime}\n   */\n  set(values) {\n    if (!this.isValid) return this;\n    const normalized = normalizeObject(values, normalizeUnitWithLocalWeeks);\n    const {\n      minDaysInFirstWeek,\n      startOfWeek\n    } = usesLocalWeekValues(normalized, this.loc);\n    const settingWeekStuff = !isUndefined(normalized.weekYear) || !isUndefined(normalized.weekNumber) || !isUndefined(normalized.weekday),\n      containsOrdinal = !isUndefined(normalized.ordinal),\n      containsGregorYear = !isUndefined(normalized.year),\n      containsGregorMD = !isUndefined(normalized.month) || !isUndefined(normalized.day),\n      containsGregor = containsGregorYear || containsGregorMD,\n      definiteWeekDef = normalized.weekYear || normalized.weekNumber;\n    if ((containsGregor || containsOrdinal) && definiteWeekDef) {\n      throw new ConflictingSpecificationError(\"Can't mix weekYear/weekNumber units with year/month/day or ordinals\");\n    }\n    if (containsGregorMD && containsOrdinal) {\n      throw new ConflictingSpecificationError(\"Can't mix ordinal dates with month/day\");\n    }\n    let mixed;\n    if (settingWeekStuff) {\n      mixed = weekToGregorian({\n        ...gregorianToWeek(this.c, minDaysInFirstWeek, startOfWeek),\n        ...normalized\n      }, minDaysInFirstWeek, startOfWeek);\n    } else if (!isUndefined(normalized.ordinal)) {\n      mixed = ordinalToGregorian({\n        ...gregorianToOrdinal(this.c),\n        ...normalized\n      });\n    } else {\n      mixed = {\n        ...this.toObject(),\n        ...normalized\n      };\n\n      // if we didn't set the day but we ended up on an overflow date,\n      // use the last day of the right month\n      if (isUndefined(normalized.day)) {\n        mixed.day = Math.min(daysInMonth(mixed.year, mixed.month), mixed.day);\n      }\n    }\n    const [ts, o] = objToTS(mixed, this.o, this.zone);\n    return clone(this, {\n      ts,\n      o\n    });\n  }\n\n  /**\n   * Add a period of time to this DateTime and return the resulting DateTime\n   *\n   * Adding hours, minutes, seconds, or milliseconds increases the timestamp by the right number of milliseconds. Adding days, months, or years shifts the calendar, accounting for DSTs and leap years along the way. Thus, `dt.plus({ hours: 24 })` may result in a different time than `dt.plus({ days: 1 })` if there's a DST shift in between.\n   * @param {Duration|Object|number} duration - The amount to add. Either a Luxon Duration, a number of milliseconds, the object argument to Duration.fromObject()\n   * @example DateTime.now().plus(123) //~> in 123 milliseconds\n   * @example DateTime.now().plus({ minutes: 15 }) //~> in 15 minutes\n   * @example DateTime.now().plus({ days: 1 }) //~> this time tomorrow\n   * @example DateTime.now().plus({ days: -1 }) //~> this time yesterday\n   * @example DateTime.now().plus({ hours: 3, minutes: 13 }) //~> in 3 hr, 13 min\n   * @example DateTime.now().plus(Duration.fromObject({ hours: 3, minutes: 13 })) //~> in 3 hr, 13 min\n   * @return {DateTime}\n   */\n  plus(duration) {\n    if (!this.isValid) return this;\n    const dur = Duration.fromDurationLike(duration);\n    return clone(this, adjustTime(this, dur));\n  }\n\n  /**\n   * Subtract a period of time to this DateTime and return the resulting DateTime\n   * See {@link DateTime#plus}\n   * @param {Duration|Object|number} duration - The amount to subtract. Either a Luxon Duration, a number of milliseconds, the object argument to Duration.fromObject()\n   @return {DateTime}\n   */\n  minus(duration) {\n    if (!this.isValid) return this;\n    const dur = Duration.fromDurationLike(duration).negate();\n    return clone(this, adjustTime(this, dur));\n  }\n\n  /**\n   * \"Set\" this DateTime to the beginning of a unit of time.\n   * @param {string} unit - The unit to go to the beginning of. Can be 'year', 'quarter', 'month', 'week', 'day', 'hour', 'minute', 'second', or 'millisecond'.\n   * @param {Object} opts - options\n   * @param {boolean} [opts.useLocaleWeeks=false] - If true, use weeks based on the locale, i.e. use the locale-dependent start of the week\n   * @example DateTime.local(2014, 3, 3).startOf('month').toISODate(); //=> '2014-03-01'\n   * @example DateTime.local(2014, 3, 3).startOf('year').toISODate(); //=> '2014-01-01'\n   * @example DateTime.local(2014, 3, 3).startOf('week').toISODate(); //=> '2014-03-03', weeks always start on Mondays\n   * @example DateTime.local(2014, 3, 3, 5, 30).startOf('day').toISOTime(); //=> '00:00.000-05:00'\n   * @example DateTime.local(2014, 3, 3, 5, 30).startOf('hour').toISOTime(); //=> '05:00:00.000-05:00'\n   * @return {DateTime}\n   */\n  startOf(unit, {\n    useLocaleWeeks = false\n  } = {}) {\n    if (!this.isValid) return this;\n    const o = {},\n      normalizedUnit = Duration.normalizeUnit(unit);\n    switch (normalizedUnit) {\n      case \"years\":\n        o.month = 1;\n      // falls through\n      case \"quarters\":\n      case \"months\":\n        o.day = 1;\n      // falls through\n      case \"weeks\":\n      case \"days\":\n        o.hour = 0;\n      // falls through\n      case \"hours\":\n        o.minute = 0;\n      // falls through\n      case \"minutes\":\n        o.second = 0;\n      // falls through\n      case \"seconds\":\n        o.millisecond = 0;\n        break;\n      case \"milliseconds\":\n        break;\n      // no default, invalid units throw in normalizeUnit()\n    }\n    if (normalizedUnit === \"weeks\") {\n      if (useLocaleWeeks) {\n        const startOfWeek = this.loc.getStartOfWeek();\n        const {\n          weekday\n        } = this;\n        if (weekday < startOfWeek) {\n          o.weekNumber = this.weekNumber - 1;\n        }\n        o.weekday = startOfWeek;\n      } else {\n        o.weekday = 1;\n      }\n    }\n    if (normalizedUnit === \"quarters\") {\n      const q = Math.ceil(this.month / 3);\n      o.month = (q - 1) * 3 + 1;\n    }\n    return this.set(o);\n  }\n\n  /**\n   * \"Set\" this DateTime to the end (meaning the last millisecond) of a unit of time\n   * @param {string} unit - The unit to go to the end of. Can be 'year', 'quarter', 'month', 'week', 'day', 'hour', 'minute', 'second', or 'millisecond'.\n   * @param {Object} opts - options\n   * @param {boolean} [opts.useLocaleWeeks=false] - If true, use weeks based on the locale, i.e. use the locale-dependent start of the week\n   * @example DateTime.local(2014, 3, 3).endOf('month').toISO(); //=> '2014-03-31T23:59:59.999-05:00'\n   * @example DateTime.local(2014, 3, 3).endOf('year').toISO(); //=> '2014-12-31T23:59:59.999-05:00'\n   * @example DateTime.local(2014, 3, 3).endOf('week').toISO(); // => '2014-03-09T23:59:59.999-05:00', weeks start on Mondays\n   * @example DateTime.local(2014, 3, 3, 5, 30).endOf('day').toISO(); //=> '2014-03-03T23:59:59.999-05:00'\n   * @example DateTime.local(2014, 3, 3, 5, 30).endOf('hour').toISO(); //=> '2014-03-03T05:59:59.999-05:00'\n   * @return {DateTime}\n   */\n  endOf(unit, opts) {\n    return this.isValid ? this.plus({\n      [unit]: 1\n    }).startOf(unit, opts).minus(1) : this;\n  }\n\n  // OUTPUT\n\n  /**\n   * Returns a string representation of this DateTime formatted according to the specified format string.\n   * **You may not want this.** See {@link DateTime#toLocaleString} for a more flexible formatting tool. For a table of tokens and their interpretations, see [here](https://moment.github.io/luxon/#/formatting?id=table-of-tokens).\n   * Defaults to en-US if no locale has been specified, regardless of the system's locale.\n   * @param {string} fmt - the format string\n   * @param {Object} opts - opts to override the configuration options on this DateTime\n   * @example DateTime.now().toFormat('yyyy LLL dd') //=> '2017 Apr 22'\n   * @example DateTime.now().setLocale('fr').toFormat('yyyy LLL dd') //=> '2017 avr. 22'\n   * @example DateTime.now().toFormat('yyyy LLL dd', { locale: \"fr\" }) //=> '2017 avr. 22'\n   * @example DateTime.now().toFormat(\"HH 'hours and' mm 'minutes'\") //=> '20 hours and 55 minutes'\n   * @return {string}\n   */\n  toFormat(fmt, opts = {}) {\n    return this.isValid ? Formatter.create(this.loc.redefaultToEN(opts)).formatDateTimeFromString(this, fmt) : INVALID;\n  }\n\n  /**\n   * Returns a localized string representing this date. Accepts the same options as the Intl.DateTimeFormat constructor and any presets defined by Luxon, such as `DateTime.DATE_FULL` or `DateTime.TIME_SIMPLE`.\n   * The exact behavior of this method is browser-specific, but in general it will return an appropriate representation\n   * of the DateTime in the assigned locale.\n   * Defaults to the system's locale if no locale has been specified\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/DateTimeFormat\n   * @param formatOpts {Object} - Intl.DateTimeFormat constructor options and configuration options\n   * @param {Object} opts - opts to override the configuration options on this DateTime\n   * @example DateTime.now().toLocaleString(); //=> 4/20/2017\n   * @example DateTime.now().setLocale('en-gb').toLocaleString(); //=> '20/04/2017'\n   * @example DateTime.now().toLocaleString(DateTime.DATE_FULL); //=> 'April 20, 2017'\n   * @example DateTime.now().toLocaleString(DateTime.DATE_FULL, { locale: 'fr' }); //=> '28 août 2022'\n   * @example DateTime.now().toLocaleString(DateTime.TIME_SIMPLE); //=> '11:32 AM'\n   * @example DateTime.now().toLocaleString(DateTime.DATETIME_SHORT); //=> '4/20/2017, 11:32 AM'\n   * @example DateTime.now().toLocaleString({ weekday: 'long', month: 'long', day: '2-digit' }); //=> 'Thursday, April 20'\n   * @example DateTime.now().toLocaleString({ weekday: 'short', month: 'short', day: '2-digit', hour: '2-digit', minute: '2-digit' }); //=> 'Thu, Apr 20, 11:27 AM'\n   * @example DateTime.now().toLocaleString({ hour: '2-digit', minute: '2-digit', hourCycle: 'h23' }); //=> '11:32'\n   * @return {string}\n   */\n  toLocaleString(formatOpts = Formats.DATE_SHORT, opts = {}) {\n    return this.isValid ? Formatter.create(this.loc.clone(opts), formatOpts).formatDateTime(this) : INVALID;\n  }\n\n  /**\n   * Returns an array of format \"parts\", meaning individual tokens along with metadata. This is allows callers to post-process individual sections of the formatted output.\n   * Defaults to the system's locale if no locale has been specified\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/DateTimeFormat/formatToParts\n   * @param opts {Object} - Intl.DateTimeFormat constructor options, same as `toLocaleString`.\n   * @example DateTime.now().toLocaleParts(); //=> [\n   *                                   //=>   { type: 'day', value: '25' },\n   *                                   //=>   { type: 'literal', value: '/' },\n   *                                   //=>   { type: 'month', value: '05' },\n   *                                   //=>   { type: 'literal', value: '/' },\n   *                                   //=>   { type: 'year', value: '1982' }\n   *                                   //=> ]\n   */\n  toLocaleParts(opts = {}) {\n    return this.isValid ? Formatter.create(this.loc.clone(opts), opts).formatDateTimeParts(this) : [];\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this DateTime\n   * @param {Object} opts - options\n   * @param {boolean} [opts.suppressMilliseconds=false] - exclude milliseconds from the format if they're 0\n   * @param {boolean} [opts.suppressSeconds=false] - exclude seconds from the format if they're 0\n   * @param {boolean} [opts.includeOffset=true] - include the offset, such as 'Z' or '-04:00'\n   * @param {boolean} [opts.extendedZone=false] - add the time zone format extension\n   * @param {string} [opts.format='extended'] - choose between the basic and extended format\n   * @example DateTime.utc(1983, 5, 25).toISO() //=> '1982-05-25T00:00:00.000Z'\n   * @example DateTime.now().toISO() //=> '2017-04-22T20:47:05.335-04:00'\n   * @example DateTime.now().toISO({ includeOffset: false }) //=> '2017-04-22T20:47:05.335'\n   * @example DateTime.now().toISO({ format: 'basic' }) //=> '20170422T204705.335-0400'\n   * @return {string}\n   */\n  toISO({\n    format = \"extended\",\n    suppressSeconds = false,\n    suppressMilliseconds = false,\n    includeOffset = true,\n    extendedZone = false\n  } = {}) {\n    if (!this.isValid) {\n      return null;\n    }\n    const ext = format === \"extended\";\n    let c = toISODate(this, ext);\n    c += \"T\";\n    c += toISOTime(this, ext, suppressSeconds, suppressMilliseconds, includeOffset, extendedZone);\n    return c;\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this DateTime's date component\n   * @param {Object} opts - options\n   * @param {string} [opts.format='extended'] - choose between the basic and extended format\n   * @example DateTime.utc(1982, 5, 25).toISODate() //=> '1982-05-25'\n   * @example DateTime.utc(1982, 5, 25).toISODate({ format: 'basic' }) //=> '19820525'\n   * @return {string}\n   */\n  toISODate({\n    format = \"extended\"\n  } = {}) {\n    if (!this.isValid) {\n      return null;\n    }\n    return toISODate(this, format === \"extended\");\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this DateTime's week date\n   * @example DateTime.utc(1982, 5, 25).toISOWeekDate() //=> '1982-W21-2'\n   * @return {string}\n   */\n  toISOWeekDate() {\n    return toTechFormat(this, \"kkkk-'W'WW-c\");\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this DateTime's time component\n   * @param {Object} opts - options\n   * @param {boolean} [opts.suppressMilliseconds=false] - exclude milliseconds from the format if they're 0\n   * @param {boolean} [opts.suppressSeconds=false] - exclude seconds from the format if they're 0\n   * @param {boolean} [opts.includeOffset=true] - include the offset, such as 'Z' or '-04:00'\n   * @param {boolean} [opts.extendedZone=true] - add the time zone format extension\n   * @param {boolean} [opts.includePrefix=false] - include the `T` prefix\n   * @param {string} [opts.format='extended'] - choose between the basic and extended format\n   * @example DateTime.utc().set({ hour: 7, minute: 34 }).toISOTime() //=> '07:34:19.361Z'\n   * @example DateTime.utc().set({ hour: 7, minute: 34, seconds: 0, milliseconds: 0 }).toISOTime({ suppressSeconds: true }) //=> '07:34Z'\n   * @example DateTime.utc().set({ hour: 7, minute: 34 }).toISOTime({ format: 'basic' }) //=> '073419.361Z'\n   * @example DateTime.utc().set({ hour: 7, minute: 34 }).toISOTime({ includePrefix: true }) //=> 'T07:34:19.361Z'\n   * @return {string}\n   */\n  toISOTime({\n    suppressMilliseconds = false,\n    suppressSeconds = false,\n    includeOffset = true,\n    includePrefix = false,\n    extendedZone = false,\n    format = \"extended\"\n  } = {}) {\n    if (!this.isValid) {\n      return null;\n    }\n    let c = includePrefix ? \"T\" : \"\";\n    return c + toISOTime(this, format === \"extended\", suppressSeconds, suppressMilliseconds, includeOffset, extendedZone);\n  }\n\n  /**\n   * Returns an RFC 2822-compatible string representation of this DateTime\n   * @example DateTime.utc(2014, 7, 13).toRFC2822() //=> 'Sun, 13 Jul 2014 00:00:00 +0000'\n   * @example DateTime.local(2014, 7, 13).toRFC2822() //=> 'Sun, 13 Jul 2014 00:00:00 -0400'\n   * @return {string}\n   */\n  toRFC2822() {\n    return toTechFormat(this, \"EEE, dd LLL yyyy HH:mm:ss ZZZ\", false);\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for use in HTTP headers. The output is always expressed in GMT.\n   * Specifically, the string conforms to RFC 1123.\n   * @see https://www.w3.org/Protocols/rfc2616/rfc2616-sec3.html#sec3.3.1\n   * @example DateTime.utc(2014, 7, 13).toHTTP() //=> 'Sun, 13 Jul 2014 00:00:00 GMT'\n   * @example DateTime.utc(2014, 7, 13, 19).toHTTP() //=> 'Sun, 13 Jul 2014 19:00:00 GMT'\n   * @return {string}\n   */\n  toHTTP() {\n    return toTechFormat(this.toUTC(), \"EEE, dd LLL yyyy HH:mm:ss 'GMT'\");\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for use in SQL Date\n   * @example DateTime.utc(2014, 7, 13).toSQLDate() //=> '2014-07-13'\n   * @return {string}\n   */\n  toSQLDate() {\n    if (!this.isValid) {\n      return null;\n    }\n    return toISODate(this, true);\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for use in SQL Time\n   * @param {Object} opts - options\n   * @param {boolean} [opts.includeZone=false] - include the zone, such as 'America/New_York'. Overrides includeOffset.\n   * @param {boolean} [opts.includeOffset=true] - include the offset, such as 'Z' or '-04:00'\n   * @param {boolean} [opts.includeOffsetSpace=true] - include the space between the time and the offset, such as '05:15:16.345 -04:00'\n   * @example DateTime.utc().toSQL() //=> '05:15:16.345'\n   * @example DateTime.now().toSQL() //=> '05:15:16.345 -04:00'\n   * @example DateTime.now().toSQL({ includeOffset: false }) //=> '05:15:16.345'\n   * @example DateTime.now().toSQL({ includeZone: false }) //=> '05:15:16.345 America/New_York'\n   * @return {string}\n   */\n  toSQLTime({\n    includeOffset = true,\n    includeZone = false,\n    includeOffsetSpace = true\n  } = {}) {\n    let fmt = \"HH:mm:ss.SSS\";\n    if (includeZone || includeOffset) {\n      if (includeOffsetSpace) {\n        fmt += \" \";\n      }\n      if (includeZone) {\n        fmt += \"z\";\n      } else if (includeOffset) {\n        fmt += \"ZZ\";\n      }\n    }\n    return toTechFormat(this, fmt, true);\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for use in SQL DateTime\n   * @param {Object} opts - options\n   * @param {boolean} [opts.includeZone=false] - include the zone, such as 'America/New_York'. Overrides includeOffset.\n   * @param {boolean} [opts.includeOffset=true] - include the offset, such as 'Z' or '-04:00'\n   * @param {boolean} [opts.includeOffsetSpace=true] - include the space between the time and the offset, such as '05:15:16.345 -04:00'\n   * @example DateTime.utc(2014, 7, 13).toSQL() //=> '2014-07-13 00:00:00.000 Z'\n   * @example DateTime.local(2014, 7, 13).toSQL() //=> '2014-07-13 00:00:00.000 -04:00'\n   * @example DateTime.local(2014, 7, 13).toSQL({ includeOffset: false }) //=> '2014-07-13 00:00:00.000'\n   * @example DateTime.local(2014, 7, 13).toSQL({ includeZone: true }) //=> '2014-07-13 00:00:00.000 America/New_York'\n   * @return {string}\n   */\n  toSQL(opts = {}) {\n    if (!this.isValid) {\n      return null;\n    }\n    return `${this.toSQLDate()} ${this.toSQLTime(opts)}`;\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for debugging\n   * @return {string}\n   */\n  toString() {\n    return this.isValid ? this.toISO() : INVALID;\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for the REPL.\n   * @return {string}\n   */\n  [Symbol.for(\"nodejs.util.inspect.custom\")]() {\n    if (this.isValid) {\n      return `DateTime { ts: ${this.toISO()}, zone: ${this.zone.name}, locale: ${this.locale} }`;\n    } else {\n      return `DateTime { Invalid, reason: ${this.invalidReason} }`;\n    }\n  }\n\n  /**\n   * Returns the epoch milliseconds of this DateTime. Alias of {@link DateTime#toMillis}\n   * @return {number}\n   */\n  valueOf() {\n    return this.toMillis();\n  }\n\n  /**\n   * Returns the epoch milliseconds of this DateTime.\n   * @return {number}\n   */\n  toMillis() {\n    return this.isValid ? this.ts : NaN;\n  }\n\n  /**\n   * Returns the epoch seconds of this DateTime.\n   * @return {number}\n   */\n  toSeconds() {\n    return this.isValid ? this.ts / 1000 : NaN;\n  }\n\n  /**\n   * Returns the epoch seconds (as a whole number) of this DateTime.\n   * @return {number}\n   */\n  toUnixInteger() {\n    return this.isValid ? Math.floor(this.ts / 1000) : NaN;\n  }\n\n  /**\n   * Returns an ISO 8601 representation of this DateTime appropriate for use in JSON.\n   * @return {string}\n   */\n  toJSON() {\n    return this.toISO();\n  }\n\n  /**\n   * Returns a BSON serializable equivalent to this DateTime.\n   * @return {Date}\n   */\n  toBSON() {\n    return this.toJSDate();\n  }\n\n  /**\n   * Returns a JavaScript object with this DateTime's year, month, day, and so on.\n   * @param opts - options for generating the object\n   * @param {boolean} [opts.includeConfig=false] - include configuration attributes in the output\n   * @example DateTime.now().toObject() //=> { year: 2017, month: 4, day: 22, hour: 20, minute: 49, second: 42, millisecond: 268 }\n   * @return {Object}\n   */\n  toObject(opts = {}) {\n    if (!this.isValid) return {};\n    const base = {\n      ...this.c\n    };\n    if (opts.includeConfig) {\n      base.outputCalendar = this.outputCalendar;\n      base.numberingSystem = this.loc.numberingSystem;\n      base.locale = this.loc.locale;\n    }\n    return base;\n  }\n\n  /**\n   * Returns a JavaScript Date equivalent to this DateTime.\n   * @return {Date}\n   */\n  toJSDate() {\n    return new Date(this.isValid ? this.ts : NaN);\n  }\n\n  // COMPARE\n\n  /**\n   * Return the difference between two DateTimes as a Duration.\n   * @param {DateTime} otherDateTime - the DateTime to compare this one to\n   * @param {string|string[]} [unit=['milliseconds']] - the unit or array of units (such as 'hours' or 'days') to include in the duration.\n   * @param {Object} opts - options that affect the creation of the Duration\n   * @param {string} [opts.conversionAccuracy='casual'] - the conversion system to use\n   * @example\n   * var i1 = DateTime.fromISO('1982-05-25T09:45'),\n   *     i2 = DateTime.fromISO('1983-10-14T10:30');\n   * i2.diff(i1).toObject() //=> { milliseconds: 43807500000 }\n   * i2.diff(i1, 'hours').toObject() //=> { hours: 12168.75 }\n   * i2.diff(i1, ['months', 'days']).toObject() //=> { months: 16, days: 19.03125 }\n   * i2.diff(i1, ['months', 'days', 'hours']).toObject() //=> { months: 16, days: 19, hours: 0.75 }\n   * @return {Duration}\n   */\n  diff(otherDateTime, unit = \"milliseconds\", opts = {}) {\n    if (!this.isValid || !otherDateTime.isValid) {\n      return Duration.invalid(\"created by diffing an invalid DateTime\");\n    }\n    const durOpts = {\n      locale: this.locale,\n      numberingSystem: this.numberingSystem,\n      ...opts\n    };\n    const units = maybeArray(unit).map(Duration.normalizeUnit),\n      otherIsLater = otherDateTime.valueOf() > this.valueOf(),\n      earlier = otherIsLater ? this : otherDateTime,\n      later = otherIsLater ? otherDateTime : this,\n      diffed = diff(earlier, later, units, durOpts);\n    return otherIsLater ? diffed.negate() : diffed;\n  }\n\n  /**\n   * Return the difference between this DateTime and right now.\n   * See {@link DateTime#diff}\n   * @param {string|string[]} [unit=['milliseconds']] - the unit or units units (such as 'hours' or 'days') to include in the duration\n   * @param {Object} opts - options that affect the creation of the Duration\n   * @param {string} [opts.conversionAccuracy='casual'] - the conversion system to use\n   * @return {Duration}\n   */\n  diffNow(unit = \"milliseconds\", opts = {}) {\n    return this.diff(DateTime.now(), unit, opts);\n  }\n\n  /**\n   * Return an Interval spanning between this DateTime and another DateTime\n   * @param {DateTime} otherDateTime - the other end point of the Interval\n   * @return {Interval}\n   */\n  until(otherDateTime) {\n    return this.isValid ? Interval.fromDateTimes(this, otherDateTime) : this;\n  }\n\n  /**\n   * Return whether this DateTime is in the same unit of time as another DateTime.\n   * Higher-order units must also be identical for this function to return `true`.\n   * Note that time zones are **ignored** in this comparison, which compares the **local** calendar time. Use {@link DateTime#setZone} to convert one of the dates if needed.\n   * @param {DateTime} otherDateTime - the other DateTime\n   * @param {string} unit - the unit of time to check sameness on\n   * @param {Object} opts - options\n   * @param {boolean} [opts.useLocaleWeeks=false] - If true, use weeks based on the locale, i.e. use the locale-dependent start of the week; only the locale of this DateTime is used\n   * @example DateTime.now().hasSame(otherDT, 'day'); //~> true if otherDT is in the same current calendar day\n   * @return {boolean}\n   */\n  hasSame(otherDateTime, unit, opts) {\n    if (!this.isValid) return false;\n    const inputMs = otherDateTime.valueOf();\n    const adjustedToZone = this.setZone(otherDateTime.zone, {\n      keepLocalTime: true\n    });\n    return adjustedToZone.startOf(unit, opts) <= inputMs && inputMs <= adjustedToZone.endOf(unit, opts);\n  }\n\n  /**\n   * Equality check\n   * Two DateTimes are equal if and only if they represent the same millisecond, have the same zone and location, and are both valid.\n   * To compare just the millisecond values, use `+dt1 === +dt2`.\n   * @param {DateTime} other - the other DateTime\n   * @return {boolean}\n   */\n  equals(other) {\n    return this.isValid && other.isValid && this.valueOf() === other.valueOf() && this.zone.equals(other.zone) && this.loc.equals(other.loc);\n  }\n\n  /**\n   * Returns a string representation of a this time relative to now, such as \"in two days\". Can only internationalize if your\n   * platform supports Intl.RelativeTimeFormat. Rounds down by default.\n   * @param {Object} options - options that affect the output\n   * @param {DateTime} [options.base=DateTime.now()] - the DateTime to use as the basis to which this time is compared. Defaults to now.\n   * @param {string} [options.style=\"long\"] - the style of units, must be \"long\", \"short\", or \"narrow\"\n   * @param {string|string[]} options.unit - use a specific unit or array of units; if omitted, or an array, the method will pick the best unit. Use an array or one of \"years\", \"quarters\", \"months\", \"weeks\", \"days\", \"hours\", \"minutes\", or \"seconds\"\n   * @param {boolean} [options.round=true] - whether to round the numbers in the output.\n   * @param {number} [options.padding=0] - padding in milliseconds. This allows you to round up the result if it fits inside the threshold. Don't use in combination with {round: false} because the decimal output will include the padding.\n   * @param {string} options.locale - override the locale of this DateTime\n   * @param {string} options.numberingSystem - override the numberingSystem of this DateTime. The Intl system may choose not to honor this\n   * @example DateTime.now().plus({ days: 1 }).toRelative() //=> \"in 1 day\"\n   * @example DateTime.now().setLocale(\"es\").toRelative({ days: 1 }) //=> \"dentro de 1 día\"\n   * @example DateTime.now().plus({ days: 1 }).toRelative({ locale: \"fr\" }) //=> \"dans 23 heures\"\n   * @example DateTime.now().minus({ days: 2 }).toRelative() //=> \"2 days ago\"\n   * @example DateTime.now().minus({ days: 2 }).toRelative({ unit: \"hours\" }) //=> \"48 hours ago\"\n   * @example DateTime.now().minus({ hours: 36 }).toRelative({ round: false }) //=> \"1.5 days ago\"\n   */\n  toRelative(options = {}) {\n    if (!this.isValid) return null;\n    const base = options.base || DateTime.fromObject({}, {\n        zone: this.zone\n      }),\n      padding = options.padding ? this < base ? -options.padding : options.padding : 0;\n    let units = [\"years\", \"months\", \"days\", \"hours\", \"minutes\", \"seconds\"];\n    let unit = options.unit;\n    if (Array.isArray(options.unit)) {\n      units = options.unit;\n      unit = undefined;\n    }\n    return diffRelative(base, this.plus(padding), {\n      ...options,\n      numeric: \"always\",\n      units,\n      unit\n    });\n  }\n\n  /**\n   * Returns a string representation of this date relative to today, such as \"yesterday\" or \"next month\".\n   * Only internationalizes on platforms that supports Intl.RelativeTimeFormat.\n   * @param {Object} options - options that affect the output\n   * @param {DateTime} [options.base=DateTime.now()] - the DateTime to use as the basis to which this time is compared. Defaults to now.\n   * @param {string} options.locale - override the locale of this DateTime\n   * @param {string} options.unit - use a specific unit; if omitted, the method will pick the unit. Use one of \"years\", \"quarters\", \"months\", \"weeks\", or \"days\"\n   * @param {string} options.numberingSystem - override the numberingSystem of this DateTime. The Intl system may choose not to honor this\n   * @example DateTime.now().plus({ days: 1 }).toRelativeCalendar() //=> \"tomorrow\"\n   * @example DateTime.now().setLocale(\"es\").plus({ days: 1 }).toRelative() //=> \"\"mañana\"\n   * @example DateTime.now().plus({ days: 1 }).toRelativeCalendar({ locale: \"fr\" }) //=> \"demain\"\n   * @example DateTime.now().minus({ days: 2 }).toRelativeCalendar() //=> \"2 days ago\"\n   */\n  toRelativeCalendar(options = {}) {\n    if (!this.isValid) return null;\n    return diffRelative(options.base || DateTime.fromObject({}, {\n      zone: this.zone\n    }), this, {\n      ...options,\n      numeric: \"auto\",\n      units: [\"years\", \"months\", \"days\"],\n      calendary: true\n    });\n  }\n\n  /**\n   * Return the min of several date times\n   * @param {...DateTime} dateTimes - the DateTimes from which to choose the minimum\n   * @return {DateTime} the min DateTime, or undefined if called with no argument\n   */\n  static min(...dateTimes) {\n    if (!dateTimes.every(DateTime.isDateTime)) {\n      throw new InvalidArgumentError(\"min requires all arguments be DateTimes\");\n    }\n    return bestBy(dateTimes, i => i.valueOf(), Math.min);\n  }\n\n  /**\n   * Return the max of several date times\n   * @param {...DateTime} dateTimes - the DateTimes from which to choose the maximum\n   * @return {DateTime} the max DateTime, or undefined if called with no argument\n   */\n  static max(...dateTimes) {\n    if (!dateTimes.every(DateTime.isDateTime)) {\n      throw new InvalidArgumentError(\"max requires all arguments be DateTimes\");\n    }\n    return bestBy(dateTimes, i => i.valueOf(), Math.max);\n  }\n\n  // MISC\n\n  /**\n   * Explain how a string would be parsed by fromFormat()\n   * @param {string} text - the string to parse\n   * @param {string} fmt - the format the string is expected to be in (see description)\n   * @param {Object} options - options taken by fromFormat()\n   * @return {Object}\n   */\n  static fromFormatExplain(text, fmt, options = {}) {\n    const {\n        locale = null,\n        numberingSystem = null\n      } = options,\n      localeToUse = Locale.fromOpts({\n        locale,\n        numberingSystem,\n        defaultToEN: true\n      });\n    return explainFromTokens(localeToUse, text, fmt);\n  }\n\n  /**\n   * @deprecated use fromFormatExplain instead\n   */\n  static fromStringExplain(text, fmt, options = {}) {\n    return DateTime.fromFormatExplain(text, fmt, options);\n  }\n\n  /**\n   * Build a parser for `fmt` using the given locale. This parser can be passed\n   * to {@link DateTime.fromFormatParser} to a parse a date in this format. This\n   * can be used to optimize cases where many dates need to be parsed in a\n   * specific format.\n   *\n   * @param {String} fmt - the format the string is expected to be in (see\n   * description)\n   * @param {Object} options - options used to set locale and numberingSystem\n   * for parser\n   * @returns {TokenParser} - opaque object to be used\n   */\n  static buildFormatParser(fmt, options = {}) {\n    const {\n        locale = null,\n        numberingSystem = null\n      } = options,\n      localeToUse = Locale.fromOpts({\n        locale,\n        numberingSystem,\n        defaultToEN: true\n      });\n    return new TokenParser(localeToUse, fmt);\n  }\n\n  /**\n   * Create a DateTime from an input string and format parser.\n   *\n   * The format parser must have been created with the same locale as this call.\n   *\n   * @param {String} text - the string to parse\n   * @param {TokenParser} formatParser - parser from {@link DateTime.buildFormatParser}\n   * @param {Object} opts - options taken by fromFormat()\n   * @returns {DateTime}\n   */\n  static fromFormatParser(text, formatParser, opts = {}) {\n    if (isUndefined(text) || isUndefined(formatParser)) {\n      throw new InvalidArgumentError(\"fromFormatParser requires an input string and a format parser\");\n    }\n    const {\n        locale = null,\n        numberingSystem = null\n      } = opts,\n      localeToUse = Locale.fromOpts({\n        locale,\n        numberingSystem,\n        defaultToEN: true\n      });\n    if (!localeToUse.equals(formatParser.locale)) {\n      throw new InvalidArgumentError(`fromFormatParser called with a locale of ${localeToUse}, ` + `but the format parser was created for ${formatParser.locale}`);\n    }\n    const {\n      result,\n      zone,\n      specificOffset,\n      invalidReason\n    } = formatParser.explainFromTokens(text);\n    if (invalidReason) {\n      return DateTime.invalid(invalidReason);\n    } else {\n      return parseDataToDateTime(result, zone, opts, `format ${formatParser.format}`, text, specificOffset);\n    }\n  }\n\n  // FORMAT PRESETS\n\n  /**\n   * {@link DateTime#toLocaleString} format like 10/14/1983\n   * @type {Object}\n   */\n  static get DATE_SHORT() {\n    return Formats.DATE_SHORT;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Oct 14, 1983'\n   * @type {Object}\n   */\n  static get DATE_MED() {\n    return Formats.DATE_MED;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Fri, Oct 14, 1983'\n   * @type {Object}\n   */\n  static get DATE_MED_WITH_WEEKDAY() {\n    return Formats.DATE_MED_WITH_WEEKDAY;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'October 14, 1983'\n   * @type {Object}\n   */\n  static get DATE_FULL() {\n    return Formats.DATE_FULL;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Tuesday, October 14, 1983'\n   * @type {Object}\n   */\n  static get DATE_HUGE() {\n    return Formats.DATE_HUGE;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get TIME_SIMPLE() {\n    return Formats.TIME_SIMPLE;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get TIME_WITH_SECONDS() {\n    return Formats.TIME_WITH_SECONDS;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23 AM EDT'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get TIME_WITH_SHORT_OFFSET() {\n    return Formats.TIME_WITH_SHORT_OFFSET;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23 AM Eastern Daylight Time'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get TIME_WITH_LONG_OFFSET() {\n    return Formats.TIME_WITH_LONG_OFFSET;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30', always 24-hour.\n   * @type {Object}\n   */\n  static get TIME_24_SIMPLE() {\n    return Formats.TIME_24_SIMPLE;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23', always 24-hour.\n   * @type {Object}\n   */\n  static get TIME_24_WITH_SECONDS() {\n    return Formats.TIME_24_WITH_SECONDS;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23 EDT', always 24-hour.\n   * @type {Object}\n   */\n  static get TIME_24_WITH_SHORT_OFFSET() {\n    return Formats.TIME_24_WITH_SHORT_OFFSET;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23 Eastern Daylight Time', always 24-hour.\n   * @type {Object}\n   */\n  static get TIME_24_WITH_LONG_OFFSET() {\n    return Formats.TIME_24_WITH_LONG_OFFSET;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '10/14/1983, 9:30 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_SHORT() {\n    return Formats.DATETIME_SHORT;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '10/14/1983, 9:30:33 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_SHORT_WITH_SECONDS() {\n    return Formats.DATETIME_SHORT_WITH_SECONDS;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Oct 14, 1983, 9:30 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_MED() {\n    return Formats.DATETIME_MED;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Oct 14, 1983, 9:30:33 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_MED_WITH_SECONDS() {\n    return Formats.DATETIME_MED_WITH_SECONDS;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Fri, 14 Oct 1983, 9:30 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_MED_WITH_WEEKDAY() {\n    return Formats.DATETIME_MED_WITH_WEEKDAY;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'October 14, 1983, 9:30 AM EDT'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_FULL() {\n    return Formats.DATETIME_FULL;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'October 14, 1983, 9:30:33 AM EDT'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_FULL_WITH_SECONDS() {\n    return Formats.DATETIME_FULL_WITH_SECONDS;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Friday, October 14, 1983, 9:30 AM Eastern Daylight Time'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_HUGE() {\n    return Formats.DATETIME_HUGE;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Friday, October 14, 1983, 9:30:33 AM Eastern Daylight Time'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_HUGE_WITH_SECONDS() {\n    return Formats.DATETIME_HUGE_WITH_SECONDS;\n  }\n}\n\n/**\n * @private\n */\nexport function friendlyDateTime(dateTimeish) {\n  if (DateTime.isDateTime(dateTimeish)) {\n    return dateTimeish;\n  } else if (dateTimeish && dateTimeish.valueOf && isNumber(dateTimeish.valueOf())) {\n    return DateTime.fromJSDate(dateTimeish);\n  } else if (dateTimeish && typeof dateTimeish === \"object\") {\n    return DateTime.fromObject(dateTimeish);\n  } else {\n    throw new InvalidArgumentError(`Unknown datetime argument: ${dateTimeish}, of type ${typeof dateTimeish}`);\n  }\n}", "map": {"version": 3, "names": ["Duration", "Interval", "Settings", "Info", "<PERSON><PERSON><PERSON>", "FixedOffsetZone", "Locale", "isUndefined", "maybeA<PERSON>y", "isDate", "isNumber", "bestBy", "daysInMonth", "daysInYear", "isLeapYear", "weeksInWeekYear", "normalizeObject", "roundTo", "objToLocalTS", "padStart", "normalizeZone", "diff", "parseRFC2822Date", "parseISODate", "parseHTTPDate", "parseSQL", "parseFromTokens", "explainFromTokens", "formatOptsToTokens", "expandMacroTokens", "Token<PERSON><PERSON><PERSON>", "gregorianToWeek", "weekT<PERSON><PERSON><PERSON><PERSON><PERSON>", "gregorianToOrdinal", "ordinalToGregorian", "hasInvalidGregorianData", "hasInvalidWeekData", "hasInvalidOrdinalData", "hasInvalidTimeData", "usesLocalWeekValues", "isoWeekdayToLocal", "Formats", "InvalidArgumentError", "ConflictingSpecificationError", "InvalidUnitError", "InvalidDateTimeError", "Invalid", "INVALID", "MAX_DATE", "unsupportedZone", "zone", "name", "possiblyCachedWeekData", "dt", "weekData", "c", "possiblyCachedLocalWeekData", "localWeekData", "loc", "getMinDaysInFirstWeek", "getStartOfWeek", "clone", "inst", "alts", "current", "ts", "o", "invalid", "DateTime", "old", "fixOffset", "localTS", "tz", "ut<PERSON><PERSON><PERSON><PERSON>", "o2", "offset", "o3", "Math", "min", "max", "tsToObj", "d", "Date", "year", "getUTCFullYear", "month", "getUTCMonth", "day", "getUTCDate", "hour", "getUTCHours", "minute", "getUTCMinutes", "second", "getUTCSeconds", "millisecond", "getUTCMilliseconds", "objToTS", "obj", "adjustTime", "dur", "oPre", "trunc", "years", "months", "quarters", "days", "weeks", "millisToAdd", "fromObject", "hours", "minutes", "seconds", "milliseconds", "as", "parseDataToDateTime", "parsed", "parsedZone", "opts", "format", "text", "specificOffset", "setZone", "Object", "keys", "length", "interpretationZone", "toTechFormat", "allowZ", "<PERSON><PERSON><PERSON><PERSON>", "create", "forceSimple", "formatDateTimeFromString", "toISODate", "extended", "longFormat", "toISOTime", "suppressSeconds", "suppressMilliseconds", "includeOffset", "extendedZone", "isOffsetFixed", "<PERSON><PERSON><PERSON><PERSON>", "defaultUnitValues", "defaultWeekUnitValues", "weekNumber", "weekday", "defaultOrdinalUnitValues", "ordinal", "orderedUnits", "orderedWeekUnits", "orderedOrdinalUnits", "normalizeUnit", "unit", "normalized", "quarter", "weekdays", "weeknumber", "weeksnumber", "weeknumbers", "weekyear", "weekyears", "toLowerCase", "normalizeUnitWithLocalWeeks", "guessOffsetForZone", "zoneOffsetGuessCache", "zoneOffsetTs", "undefined", "now", "quickDT", "defaultZone", "u", "<PERSON><PERSON><PERSON><PERSON>", "diffRelative", "start", "end", "round", "calendary", "formatter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "differ", "<PERSON><PERSON><PERSON>", "startOf", "get", "units", "count", "abs", "lastOpts", "argList", "args", "Array", "from", "slice", "constructor", "config", "Number", "isNaN", "unchanged", "equals", "ot", "_zone", "isLuxonDateTime", "local", "arguments", "utc", "utcInstance", "fromJSDate", "date", "options", "valueOf", "NaN", "zoneToUse", "fromMillis", "fromSeconds", "minDaysInFirstWeek", "startOfWeek", "tsNow", "containsOrdinal", "containsGregorYear", "containsGregorMD", "<PERSON><PERSON><PERSON><PERSON>", "definiteWeekDef", "weekYear", "useWeekData", "defaultValues", "objNow", "<PERSON><PERSON><PERSON><PERSON>", "v", "higherOrderInvalid", "gregorian", "tsFinal", "offsetFinal", "toISO", "fromISO", "vals", "fromRFC2822", "fromHTTP", "fromFormat", "fmt", "locale", "numberingSystem", "localeToUse", "fromOpts", "defaultToEN", "fromString", "fromSQL", "reason", "explanation", "throwOnInvalid", "isDateTime", "parseFormatForOpts", "formatOpts", "localeOpts", "tokenList", "map", "t", "val", "join", "expandFormat", "expanded", "parseFormat", "resetCache", "invalidReason", "invalidExplanation", "outputCalendar", "zoneName", "ceil", "isWeekend", "getWeekendDays", "includes", "localWeekday", "localWeekNumber", "localWeekYear", "monthShort", "locObj", "monthLong", "weekdayShort", "weekdayLong", "offsetNameShort", "offsetName", "offsetNameLong", "isUniversal", "isInDST", "set", "getPossibleOffsets", "dayMs", "minuteMs", "oEarlier", "oLater", "o1", "ts1", "ts2", "c1", "c2", "isInLeapYear", "weeksInLocalWeekYear", "resolvedLocaleOptions", "calendar", "resolvedOptions", "toUTC", "instance", "toLocal", "keepLocalTime", "keepCalendarTime", "newTS", "offsetGuess", "as<PERSON>bj", "toObject", "reconfigure", "setLocale", "values", "settingWeekStuff", "mixed", "plus", "duration", "fromDurationLike", "minus", "negate", "useLocaleWeeks", "normalizedUnit", "q", "endOf", "toFormat", "redefaultToEN", "toLocaleString", "DATE_SHORT", "formatDateTime", "toLocaleParts", "formatDateTimeParts", "ext", "toISOWeekDate", "includePrefix", "toRFC2822", "toHTTP", "toSQLDate", "toSQLTime", "includeZone", "includeOffsetSpace", "toSQL", "toString", "Symbol", "for", "<PERSON><PERSON><PERSON><PERSON>", "to<PERSON><PERSON><PERSON><PERSON>", "toUnixInteger", "floor", "toJSON", "toBSON", "toJSDate", "base", "includeConfig", "otherDateTime", "durOpts", "otherIsLater", "earlier", "later", "diffed", "diffNow", "until", "fromDateTimes", "inputMs", "adjustedToZone", "other", "toRelative", "padding", "isArray", "numeric", "toRelativeCalendar", "dateTimes", "every", "i", "fromFormatExplain", "fromStringExplain", "buildFormatParser", "fromFormatParser", "format<PERSON><PERSON>er", "result", "DATE_MED", "DATE_MED_WITH_WEEKDAY", "DATE_FULL", "DATE_HUGE", "TIME_SIMPLE", "TIME_WITH_SECONDS", "TIME_WITH_SHORT_OFFSET", "TIME_WITH_LONG_OFFSET", "TIME_24_SIMPLE", "TIME_24_WITH_SECONDS", "TIME_24_WITH_SHORT_OFFSET", "TIME_24_WITH_LONG_OFFSET", "DATETIME_SHORT", "DATETIME_SHORT_WITH_SECONDS", "DATETIME_MED", "DATETIME_MED_WITH_SECONDS", "DATETIME_MED_WITH_WEEKDAY", "DATETIME_FULL", "DATETIME_FULL_WITH_SECONDS", "DATETIME_HUGE", "DATETIME_HUGE_WITH_SECONDS", "friendlyDateTime", "dateTimeish"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/luxon/src/datetime.js"], "sourcesContent": ["import Duration from \"./duration.js\";\nimport Interval from \"./interval.js\";\nimport Settings from \"./settings.js\";\nimport Info from \"./info.js\";\nimport Formatter from \"./impl/formatter.js\";\nimport FixedOffsetZone from \"./zones/fixedOffsetZone.js\";\nimport Locale from \"./impl/locale.js\";\nimport {\n  isUndefined,\n  maybeArray,\n  isDate,\n  isNumber,\n  bestBy,\n  daysInMonth,\n  daysInYear,\n  isLeapYear,\n  weeksInWeekYear,\n  normalizeObject,\n  roundTo,\n  objToLocalTS,\n  padStart,\n} from \"./impl/util.js\";\nimport { normalizeZone } from \"./impl/zoneUtil.js\";\nimport diff from \"./impl/diff.js\";\nimport { parseRFC2822Date, parseISODate, parseHTTPDate, parseSQL } from \"./impl/regexParser.js\";\nimport {\n  parseFromTokens,\n  explainFromTokens,\n  formatOptsToTokens,\n  expandMacroTokens,\n  TokenParser,\n} from \"./impl/tokenParser.js\";\nimport {\n  gregorianToWeek,\n  weekToGregorian,\n  gregorianToOrdinal,\n  ordinalToGregorian,\n  hasInvalidGregorianData,\n  hasInvalidWeekData,\n  hasInvalidOrdinalData,\n  hasInvalidTimeData,\n  usesLocalWeekValues,\n  isoWeekdayToLocal,\n} from \"./impl/conversions.js\";\nimport * as Formats from \"./impl/formats.js\";\nimport {\n  InvalidArgumentError,\n  ConflictingSpecificationError,\n  InvalidUnitError,\n  InvalidDateTimeError,\n} from \"./errors.js\";\nimport Invalid from \"./impl/invalid.js\";\n\nconst INVALID = \"Invalid DateTime\";\nconst MAX_DATE = 8.64e15;\n\nfunction unsupportedZone(zone) {\n  return new Invalid(\"unsupported zone\", `the zone \"${zone.name}\" is not supported`);\n}\n\n// we cache week data on the DT object and this intermediates the cache\n/**\n * @param {DateTime} dt\n */\nfunction possiblyCachedWeekData(dt) {\n  if (dt.weekData === null) {\n    dt.weekData = gregorianToWeek(dt.c);\n  }\n  return dt.weekData;\n}\n\n/**\n * @param {DateTime} dt\n */\nfunction possiblyCachedLocalWeekData(dt) {\n  if (dt.localWeekData === null) {\n    dt.localWeekData = gregorianToWeek(\n      dt.c,\n      dt.loc.getMinDaysInFirstWeek(),\n      dt.loc.getStartOfWeek()\n    );\n  }\n  return dt.localWeekData;\n}\n\n// clone really means, \"make a new object with these modifications\". all \"setters\" really use this\n// to create a new object while only changing some of the properties\nfunction clone(inst, alts) {\n  const current = {\n    ts: inst.ts,\n    zone: inst.zone,\n    c: inst.c,\n    o: inst.o,\n    loc: inst.loc,\n    invalid: inst.invalid,\n  };\n  return new DateTime({ ...current, ...alts, old: current });\n}\n\n// find the right offset a given local time. The o input is our guess, which determines which\n// offset we'll pick in ambiguous cases (e.g. there are two 3 AMs b/c Fallback DST)\nfunction fixOffset(localTS, o, tz) {\n  // Our UTC time is just a guess because our offset is just a guess\n  let utcGuess = localTS - o * 60 * 1000;\n\n  // Test whether the zone matches the offset for this ts\n  const o2 = tz.offset(utcGuess);\n\n  // If so, offset didn't change and we're done\n  if (o === o2) {\n    return [utcGuess, o];\n  }\n\n  // If not, change the ts by the difference in the offset\n  utcGuess -= (o2 - o) * 60 * 1000;\n\n  // If that gives us the local time we want, we're done\n  const o3 = tz.offset(utcGuess);\n  if (o2 === o3) {\n    return [utcGuess, o2];\n  }\n\n  // If it's different, we're in a hole time. The offset has changed, but the we don't adjust the time\n  return [localTS - Math.min(o2, o3) * 60 * 1000, Math.max(o2, o3)];\n}\n\n// convert an epoch timestamp into a calendar object with the given offset\nfunction tsToObj(ts, offset) {\n  ts += offset * 60 * 1000;\n\n  const d = new Date(ts);\n\n  return {\n    year: d.getUTCFullYear(),\n    month: d.getUTCMonth() + 1,\n    day: d.getUTCDate(),\n    hour: d.getUTCHours(),\n    minute: d.getUTCMinutes(),\n    second: d.getUTCSeconds(),\n    millisecond: d.getUTCMilliseconds(),\n  };\n}\n\n// convert a calendar object to a epoch timestamp\nfunction objToTS(obj, offset, zone) {\n  return fixOffset(objToLocalTS(obj), offset, zone);\n}\n\n// create a new DT instance by adding a duration, adjusting for DSTs\nfunction adjustTime(inst, dur) {\n  const oPre = inst.o,\n    year = inst.c.year + Math.trunc(dur.years),\n    month = inst.c.month + Math.trunc(dur.months) + Math.trunc(dur.quarters) * 3,\n    c = {\n      ...inst.c,\n      year,\n      month,\n      day:\n        Math.min(inst.c.day, daysInMonth(year, month)) +\n        Math.trunc(dur.days) +\n        Math.trunc(dur.weeks) * 7,\n    },\n    millisToAdd = Duration.fromObject({\n      years: dur.years - Math.trunc(dur.years),\n      quarters: dur.quarters - Math.trunc(dur.quarters),\n      months: dur.months - Math.trunc(dur.months),\n      weeks: dur.weeks - Math.trunc(dur.weeks),\n      days: dur.days - Math.trunc(dur.days),\n      hours: dur.hours,\n      minutes: dur.minutes,\n      seconds: dur.seconds,\n      milliseconds: dur.milliseconds,\n    }).as(\"milliseconds\"),\n    localTS = objToLocalTS(c);\n\n  let [ts, o] = fixOffset(localTS, oPre, inst.zone);\n\n  if (millisToAdd !== 0) {\n    ts += millisToAdd;\n    // that could have changed the offset by going over a DST, but we want to keep the ts the same\n    o = inst.zone.offset(ts);\n  }\n\n  return { ts, o };\n}\n\n// helper useful in turning the results of parsing into real dates\n// by handling the zone options\nfunction parseDataToDateTime(parsed, parsedZone, opts, format, text, specificOffset) {\n  const { setZone, zone } = opts;\n  if ((parsed && Object.keys(parsed).length !== 0) || parsedZone) {\n    const interpretationZone = parsedZone || zone,\n      inst = DateTime.fromObject(parsed, {\n        ...opts,\n        zone: interpretationZone,\n        specificOffset,\n      });\n    return setZone ? inst : inst.setZone(zone);\n  } else {\n    return DateTime.invalid(\n      new Invalid(\"unparsable\", `the input \"${text}\" can't be parsed as ${format}`)\n    );\n  }\n}\n\n// if you want to output a technical format (e.g. RFC 2822), this helper\n// helps handle the details\nfunction toTechFormat(dt, format, allowZ = true) {\n  return dt.isValid\n    ? Formatter.create(Locale.create(\"en-US\"), {\n        allowZ,\n        forceSimple: true,\n      }).formatDateTimeFromString(dt, format)\n    : null;\n}\n\nfunction toISODate(o, extended) {\n  const longFormat = o.c.year > 9999 || o.c.year < 0;\n  let c = \"\";\n  if (longFormat && o.c.year >= 0) c += \"+\";\n  c += padStart(o.c.year, longFormat ? 6 : 4);\n\n  if (extended) {\n    c += \"-\";\n    c += padStart(o.c.month);\n    c += \"-\";\n    c += padStart(o.c.day);\n  } else {\n    c += padStart(o.c.month);\n    c += padStart(o.c.day);\n  }\n  return c;\n}\n\nfunction toISOTime(\n  o,\n  extended,\n  suppressSeconds,\n  suppressMilliseconds,\n  includeOffset,\n  extendedZone\n) {\n  let c = padStart(o.c.hour);\n  if (extended) {\n    c += \":\";\n    c += padStart(o.c.minute);\n    if (o.c.millisecond !== 0 || o.c.second !== 0 || !suppressSeconds) {\n      c += \":\";\n    }\n  } else {\n    c += padStart(o.c.minute);\n  }\n\n  if (o.c.millisecond !== 0 || o.c.second !== 0 || !suppressSeconds) {\n    c += padStart(o.c.second);\n\n    if (o.c.millisecond !== 0 || !suppressMilliseconds) {\n      c += \".\";\n      c += padStart(o.c.millisecond, 3);\n    }\n  }\n\n  if (includeOffset) {\n    if (o.isOffsetFixed && o.offset === 0 && !extendedZone) {\n      c += \"Z\";\n    } else if (o.o < 0) {\n      c += \"-\";\n      c += padStart(Math.trunc(-o.o / 60));\n      c += \":\";\n      c += padStart(Math.trunc(-o.o % 60));\n    } else {\n      c += \"+\";\n      c += padStart(Math.trunc(o.o / 60));\n      c += \":\";\n      c += padStart(Math.trunc(o.o % 60));\n    }\n  }\n\n  if (extendedZone) {\n    c += \"[\" + o.zone.ianaName + \"]\";\n  }\n  return c;\n}\n\n// defaults for unspecified units in the supported calendars\nconst defaultUnitValues = {\n    month: 1,\n    day: 1,\n    hour: 0,\n    minute: 0,\n    second: 0,\n    millisecond: 0,\n  },\n  defaultWeekUnitValues = {\n    weekNumber: 1,\n    weekday: 1,\n    hour: 0,\n    minute: 0,\n    second: 0,\n    millisecond: 0,\n  },\n  defaultOrdinalUnitValues = {\n    ordinal: 1,\n    hour: 0,\n    minute: 0,\n    second: 0,\n    millisecond: 0,\n  };\n\n// Units in the supported calendars, sorted by bigness\nconst orderedUnits = [\"year\", \"month\", \"day\", \"hour\", \"minute\", \"second\", \"millisecond\"],\n  orderedWeekUnits = [\n    \"weekYear\",\n    \"weekNumber\",\n    \"weekday\",\n    \"hour\",\n    \"minute\",\n    \"second\",\n    \"millisecond\",\n  ],\n  orderedOrdinalUnits = [\"year\", \"ordinal\", \"hour\", \"minute\", \"second\", \"millisecond\"];\n\n// standardize case and plurality in units\nfunction normalizeUnit(unit) {\n  const normalized = {\n    year: \"year\",\n    years: \"year\",\n    month: \"month\",\n    months: \"month\",\n    day: \"day\",\n    days: \"day\",\n    hour: \"hour\",\n    hours: \"hour\",\n    minute: \"minute\",\n    minutes: \"minute\",\n    quarter: \"quarter\",\n    quarters: \"quarter\",\n    second: \"second\",\n    seconds: \"second\",\n    millisecond: \"millisecond\",\n    milliseconds: \"millisecond\",\n    weekday: \"weekday\",\n    weekdays: \"weekday\",\n    weeknumber: \"weekNumber\",\n    weeksnumber: \"weekNumber\",\n    weeknumbers: \"weekNumber\",\n    weekyear: \"weekYear\",\n    weekyears: \"weekYear\",\n    ordinal: \"ordinal\",\n  }[unit.toLowerCase()];\n\n  if (!normalized) throw new InvalidUnitError(unit);\n\n  return normalized;\n}\n\nfunction normalizeUnitWithLocalWeeks(unit) {\n  switch (unit.toLowerCase()) {\n    case \"localweekday\":\n    case \"localweekdays\":\n      return \"localWeekday\";\n    case \"localweeknumber\":\n    case \"localweeknumbers\":\n      return \"localWeekNumber\";\n    case \"localweekyear\":\n    case \"localweekyears\":\n      return \"localWeekYear\";\n    default:\n      return normalizeUnit(unit);\n  }\n}\n\n// cache offsets for zones based on the current timestamp when this function is\n// first called. When we are handling a datetime from components like (year,\n// month, day, hour) in a time zone, we need a guess about what the timezone\n// offset is so that we can convert into a UTC timestamp. One way is to find the\n// offset of now in the zone. The actual date may have a different offset (for\n// example, if we handle a date in June while we're in December in a zone that\n// observes DST), but we can check and adjust that.\n//\n// When handling many dates, calculating the offset for now every time is\n// expensive. It's just a guess, so we can cache the offset to use even if we\n// are right on a time change boundary (we'll just correct in the other\n// direction). Using a timestamp from first read is a slight optimization for\n// handling dates close to the current date, since those dates will usually be\n// in the same offset (we could set the timestamp statically, instead). We use a\n// single timestamp for all zones to make things a bit more predictable.\n//\n// This is safe for quickDT (used by local() and utc()) because we don't fill in\n// higher-order units from tsNow (as we do in fromObject, this requires that\n// offset is calculated from tsNow).\nfunction guessOffsetForZone(zone) {\n  if (!zoneOffsetGuessCache[zone]) {\n    if (zoneOffsetTs === undefined) {\n      zoneOffsetTs = Settings.now();\n    }\n\n    zoneOffsetGuessCache[zone] = zone.offset(zoneOffsetTs);\n  }\n  return zoneOffsetGuessCache[zone];\n}\n\n// this is a dumbed down version of fromObject() that runs about 60% faster\n// but doesn't do any validation, makes a bunch of assumptions about what units\n// are present, and so on.\nfunction quickDT(obj, opts) {\n  const zone = normalizeZone(opts.zone, Settings.defaultZone);\n  if (!zone.isValid) {\n    return DateTime.invalid(unsupportedZone(zone));\n  }\n\n  const loc = Locale.fromObject(opts);\n\n  let ts, o;\n\n  // assume we have the higher-order units\n  if (!isUndefined(obj.year)) {\n    for (const u of orderedUnits) {\n      if (isUndefined(obj[u])) {\n        obj[u] = defaultUnitValues[u];\n      }\n    }\n\n    const invalid = hasInvalidGregorianData(obj) || hasInvalidTimeData(obj);\n    if (invalid) {\n      return DateTime.invalid(invalid);\n    }\n\n    const offsetProvis = guessOffsetForZone(zone);\n    [ts, o] = objToTS(obj, offsetProvis, zone);\n  } else {\n    ts = Settings.now();\n  }\n\n  return new DateTime({ ts, zone, loc, o });\n}\n\nfunction diffRelative(start, end, opts) {\n  const round = isUndefined(opts.round) ? true : opts.round,\n    format = (c, unit) => {\n      c = roundTo(c, round || opts.calendary ? 0 : 2, true);\n      const formatter = end.loc.clone(opts).relFormatter(opts);\n      return formatter.format(c, unit);\n    },\n    differ = (unit) => {\n      if (opts.calendary) {\n        if (!end.hasSame(start, unit)) {\n          return end.startOf(unit).diff(start.startOf(unit), unit).get(unit);\n        } else return 0;\n      } else {\n        return end.diff(start, unit).get(unit);\n      }\n    };\n\n  if (opts.unit) {\n    return format(differ(opts.unit), opts.unit);\n  }\n\n  for (const unit of opts.units) {\n    const count = differ(unit);\n    if (Math.abs(count) >= 1) {\n      return format(count, unit);\n    }\n  }\n  return format(start > end ? -0 : 0, opts.units[opts.units.length - 1]);\n}\n\nfunction lastOpts(argList) {\n  let opts = {},\n    args;\n  if (argList.length > 0 && typeof argList[argList.length - 1] === \"object\") {\n    opts = argList[argList.length - 1];\n    args = Array.from(argList).slice(0, argList.length - 1);\n  } else {\n    args = Array.from(argList);\n  }\n  return [opts, args];\n}\n\n/**\n * Timestamp to use for cached zone offset guesses (exposed for test)\n */\nlet zoneOffsetTs;\n/**\n * Cache for zone offset guesses (exposed for test).\n *\n * This optimizes quickDT via guessOffsetForZone to avoid repeated calls of\n * zone.offset().\n */\nlet zoneOffsetGuessCache = {};\n\n/**\n * A DateTime is an immutable data structure representing a specific date and time and accompanying methods. It contains class and instance methods for creating, parsing, interrogating, transforming, and formatting them.\n *\n * A DateTime comprises of:\n * * A timestamp. Each DateTime instance refers to a specific millisecond of the Unix epoch.\n * * A time zone. Each instance is considered in the context of a specific zone (by default the local system's zone).\n * * Configuration properties that effect how output strings are formatted, such as `locale`, `numberingSystem`, and `outputCalendar`.\n *\n * Here is a brief overview of the most commonly used functionality it provides:\n *\n * * **Creation**: To create a DateTime from its components, use one of its factory class methods: {@link DateTime.local}, {@link DateTime.utc}, and (most flexibly) {@link DateTime.fromObject}. To create one from a standard string format, use {@link DateTime.fromISO}, {@link DateTime.fromHTTP}, and {@link DateTime.fromRFC2822}. To create one from a custom string format, use {@link DateTime.fromFormat}. To create one from a native JS date, use {@link DateTime.fromJSDate}.\n * * **Gregorian calendar and time**: To examine the Gregorian properties of a DateTime individually (i.e as opposed to collectively through {@link DateTime#toObject}), use the {@link DateTime#year}, {@link DateTime#month},\n * {@link DateTime#day}, {@link DateTime#hour}, {@link DateTime#minute}, {@link DateTime#second}, {@link DateTime#millisecond} accessors.\n * * **Week calendar**: For ISO week calendar attributes, see the {@link DateTime#weekYear}, {@link DateTime#weekNumber}, and {@link DateTime#weekday} accessors.\n * * **Configuration** See the {@link DateTime#locale} and {@link DateTime#numberingSystem} accessors.\n * * **Transformation**: To transform the DateTime into other DateTimes, use {@link DateTime#set}, {@link DateTime#reconfigure}, {@link DateTime#setZone}, {@link DateTime#setLocale}, {@link DateTime.plus}, {@link DateTime#minus}, {@link DateTime#endOf}, {@link DateTime#startOf}, {@link DateTime#toUTC}, and {@link DateTime#toLocal}.\n * * **Output**: To convert the DateTime to other representations, use the {@link DateTime#toRelative}, {@link DateTime#toRelativeCalendar}, {@link DateTime#toJSON}, {@link DateTime#toISO}, {@link DateTime#toHTTP}, {@link DateTime#toObject}, {@link DateTime#toRFC2822}, {@link DateTime#toString}, {@link DateTime#toLocaleString}, {@link DateTime#toFormat}, {@link DateTime#toMillis} and {@link DateTime#toJSDate}.\n *\n * There's plenty others documented below. In addition, for more information on subtler topics like internationalization, time zones, alternative calendars, validity, and so on, see the external documentation.\n */\nexport default class DateTime {\n  /**\n   * @access private\n   */\n  constructor(config) {\n    const zone = config.zone || Settings.defaultZone;\n\n    let invalid =\n      config.invalid ||\n      (Number.isNaN(config.ts) ? new Invalid(\"invalid input\") : null) ||\n      (!zone.isValid ? unsupportedZone(zone) : null);\n    /**\n     * @access private\n     */\n    this.ts = isUndefined(config.ts) ? Settings.now() : config.ts;\n\n    let c = null,\n      o = null;\n    if (!invalid) {\n      const unchanged = config.old && config.old.ts === this.ts && config.old.zone.equals(zone);\n\n      if (unchanged) {\n        [c, o] = [config.old.c, config.old.o];\n      } else {\n        // If an offset has been passed and we have not been called from\n        // clone(), we can trust it and avoid the offset calculation.\n        const ot = isNumber(config.o) && !config.old ? config.o : zone.offset(this.ts);\n        c = tsToObj(this.ts, ot);\n        invalid = Number.isNaN(c.year) ? new Invalid(\"invalid input\") : null;\n        c = invalid ? null : c;\n        o = invalid ? null : ot;\n      }\n    }\n\n    /**\n     * @access private\n     */\n    this._zone = zone;\n    /**\n     * @access private\n     */\n    this.loc = config.loc || Locale.create();\n    /**\n     * @access private\n     */\n    this.invalid = invalid;\n    /**\n     * @access private\n     */\n    this.weekData = null;\n    /**\n     * @access private\n     */\n    this.localWeekData = null;\n    /**\n     * @access private\n     */\n    this.c = c;\n    /**\n     * @access private\n     */\n    this.o = o;\n    /**\n     * @access private\n     */\n    this.isLuxonDateTime = true;\n  }\n\n  // CONSTRUCT\n\n  /**\n   * Create a DateTime for the current instant, in the system's time zone.\n   *\n   * Use Settings to override these default values if needed.\n   * @example DateTime.now().toISO() //~> now in the ISO format\n   * @return {DateTime}\n   */\n  static now() {\n    return new DateTime({});\n  }\n\n  /**\n   * Create a local DateTime\n   * @param {number} [year] - The calendar year. If omitted (as in, call `local()` with no arguments), the current time will be used\n   * @param {number} [month=1] - The month, 1-indexed\n   * @param {number} [day=1] - The day of the month, 1-indexed\n   * @param {number} [hour=0] - The hour of the day, in 24-hour time\n   * @param {number} [minute=0] - The minute of the hour, meaning a number between 0 and 59\n   * @param {number} [second=0] - The second of the minute, meaning a number between 0 and 59\n   * @param {number} [millisecond=0] - The millisecond of the second, meaning a number between 0 and 999\n   * @example DateTime.local()                                  //~> now\n   * @example DateTime.local({ zone: \"America/New_York\" })      //~> now, in US east coast time\n   * @example DateTime.local(2017)                              //~> 2017-01-01T00:00:00\n   * @example DateTime.local(2017, 3)                           //~> 2017-03-01T00:00:00\n   * @example DateTime.local(2017, 3, 12, { locale: \"fr\" })     //~> 2017-03-12T00:00:00, with a French locale\n   * @example DateTime.local(2017, 3, 12, 5)                    //~> 2017-03-12T05:00:00\n   * @example DateTime.local(2017, 3, 12, 5, { zone: \"utc\" })   //~> 2017-03-12T05:00:00, in UTC\n   * @example DateTime.local(2017, 3, 12, 5, 45)                //~> 2017-03-12T05:45:00\n   * @example DateTime.local(2017, 3, 12, 5, 45, 10)            //~> 2017-03-12T05:45:10\n   * @example DateTime.local(2017, 3, 12, 5, 45, 10, 765)       //~> 2017-03-12T05:45:10.765\n   * @return {DateTime}\n   */\n  static local() {\n    const [opts, args] = lastOpts(arguments),\n      [year, month, day, hour, minute, second, millisecond] = args;\n    return quickDT({ year, month, day, hour, minute, second, millisecond }, opts);\n  }\n\n  /**\n   * Create a DateTime in UTC\n   * @param {number} [year] - The calendar year. If omitted (as in, call `utc()` with no arguments), the current time will be used\n   * @param {number} [month=1] - The month, 1-indexed\n   * @param {number} [day=1] - The day of the month\n   * @param {number} [hour=0] - The hour of the day, in 24-hour time\n   * @param {number} [minute=0] - The minute of the hour, meaning a number between 0 and 59\n   * @param {number} [second=0] - The second of the minute, meaning a number between 0 and 59\n   * @param {number} [millisecond=0] - The millisecond of the second, meaning a number between 0 and 999\n   * @param {Object} options - configuration options for the DateTime\n   * @param {string} [options.locale] - a locale to set on the resulting DateTime instance\n   * @param {string} [options.outputCalendar] - the output calendar to set on the resulting DateTime instance\n   * @param {string} [options.numberingSystem] - the numbering system to set on the resulting DateTime instance\n   * @param {string} [options.weekSettings] - the week settings to set on the resulting DateTime instance\n   * @example DateTime.utc()                                              //~> now\n   * @example DateTime.utc(2017)                                          //~> 2017-01-01T00:00:00Z\n   * @example DateTime.utc(2017, 3)                                       //~> 2017-03-01T00:00:00Z\n   * @example DateTime.utc(2017, 3, 12)                                   //~> 2017-03-12T00:00:00Z\n   * @example DateTime.utc(2017, 3, 12, 5)                                //~> 2017-03-12T05:00:00Z\n   * @example DateTime.utc(2017, 3, 12, 5, 45)                            //~> 2017-03-12T05:45:00Z\n   * @example DateTime.utc(2017, 3, 12, 5, 45, { locale: \"fr\" })          //~> 2017-03-12T05:45:00Z with a French locale\n   * @example DateTime.utc(2017, 3, 12, 5, 45, 10)                        //~> 2017-03-12T05:45:10Z\n   * @example DateTime.utc(2017, 3, 12, 5, 45, 10, 765, { locale: \"fr\" }) //~> 2017-03-12T05:45:10.765Z with a French locale\n   * @return {DateTime}\n   */\n  static utc() {\n    const [opts, args] = lastOpts(arguments),\n      [year, month, day, hour, minute, second, millisecond] = args;\n\n    opts.zone = FixedOffsetZone.utcInstance;\n    return quickDT({ year, month, day, hour, minute, second, millisecond }, opts);\n  }\n\n  /**\n   * Create a DateTime from a JavaScript Date object. Uses the default zone.\n   * @param {Date} date - a JavaScript Date object\n   * @param {Object} options - configuration options for the DateTime\n   * @param {string|Zone} [options.zone='local'] - the zone to place the DateTime into\n   * @return {DateTime}\n   */\n  static fromJSDate(date, options = {}) {\n    const ts = isDate(date) ? date.valueOf() : NaN;\n    if (Number.isNaN(ts)) {\n      return DateTime.invalid(\"invalid input\");\n    }\n\n    const zoneToUse = normalizeZone(options.zone, Settings.defaultZone);\n    if (!zoneToUse.isValid) {\n      return DateTime.invalid(unsupportedZone(zoneToUse));\n    }\n\n    return new DateTime({\n      ts: ts,\n      zone: zoneToUse,\n      loc: Locale.fromObject(options),\n    });\n  }\n\n  /**\n   * Create a DateTime from a number of milliseconds since the epoch (meaning since 1 January 1970 00:00:00 UTC). Uses the default zone.\n   * @param {number} milliseconds - a number of milliseconds since 1970 UTC\n   * @param {Object} options - configuration options for the DateTime\n   * @param {string|Zone} [options.zone='local'] - the zone to place the DateTime into\n   * @param {string} [options.locale] - a locale to set on the resulting DateTime instance\n   * @param {string} options.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @param {string} options.numberingSystem - the numbering system to set on the resulting DateTime instance\n   * @param {string} options.weekSettings - the week settings to set on the resulting DateTime instance\n   * @return {DateTime}\n   */\n  static fromMillis(milliseconds, options = {}) {\n    if (!isNumber(milliseconds)) {\n      throw new InvalidArgumentError(\n        `fromMillis requires a numerical input, but received a ${typeof milliseconds} with value ${milliseconds}`\n      );\n    } else if (milliseconds < -MAX_DATE || milliseconds > MAX_DATE) {\n      // this isn't perfect because we can still end up out of range because of additional shifting, but it's a start\n      return DateTime.invalid(\"Timestamp out of range\");\n    } else {\n      return new DateTime({\n        ts: milliseconds,\n        zone: normalizeZone(options.zone, Settings.defaultZone),\n        loc: Locale.fromObject(options),\n      });\n    }\n  }\n\n  /**\n   * Create a DateTime from a number of seconds since the epoch (meaning since 1 January 1970 00:00:00 UTC). Uses the default zone.\n   * @param {number} seconds - a number of seconds since 1970 UTC\n   * @param {Object} options - configuration options for the DateTime\n   * @param {string|Zone} [options.zone='local'] - the zone to place the DateTime into\n   * @param {string} [options.locale] - a locale to set on the resulting DateTime instance\n   * @param {string} options.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @param {string} options.numberingSystem - the numbering system to set on the resulting DateTime instance\n   * @param {string} options.weekSettings - the week settings to set on the resulting DateTime instance\n   * @return {DateTime}\n   */\n  static fromSeconds(seconds, options = {}) {\n    if (!isNumber(seconds)) {\n      throw new InvalidArgumentError(\"fromSeconds requires a numerical input\");\n    } else {\n      return new DateTime({\n        ts: seconds * 1000,\n        zone: normalizeZone(options.zone, Settings.defaultZone),\n        loc: Locale.fromObject(options),\n      });\n    }\n  }\n\n  /**\n   * Create a DateTime from a JavaScript object with keys like 'year' and 'hour' with reasonable defaults.\n   * @param {Object} obj - the object to create the DateTime from\n   * @param {number} obj.year - a year, such as 1987\n   * @param {number} obj.month - a month, 1-12\n   * @param {number} obj.day - a day of the month, 1-31, depending on the month\n   * @param {number} obj.ordinal - day of the year, 1-365 or 366\n   * @param {number} obj.weekYear - an ISO week year\n   * @param {number} obj.weekNumber - an ISO week number, between 1 and 52 or 53, depending on the year\n   * @param {number} obj.weekday - an ISO weekday, 1-7, where 1 is Monday and 7 is Sunday\n   * @param {number} obj.localWeekYear - a week year, according to the locale\n   * @param {number} obj.localWeekNumber - a week number, between 1 and 52 or 53, depending on the year, according to the locale\n   * @param {number} obj.localWeekday - a weekday, 1-7, where 1 is the first and 7 is the last day of the week, according to the locale\n   * @param {number} obj.hour - hour of the day, 0-23\n   * @param {number} obj.minute - minute of the hour, 0-59\n   * @param {number} obj.second - second of the minute, 0-59\n   * @param {number} obj.millisecond - millisecond of the second, 0-999\n   * @param {Object} opts - options for creating this DateTime\n   * @param {string|Zone} [opts.zone='local'] - interpret the numbers in the context of a particular zone. Can take any value taken as the first argument to setZone()\n   * @param {string} [opts.locale='system\\'s locale'] - a locale to set on the resulting DateTime instance\n   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @param {string} opts.numberingSystem - the numbering system to set on the resulting DateTime instance\n   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance\n   * @example DateTime.fromObject({ year: 1982, month: 5, day: 25}).toISODate() //=> '1982-05-25'\n   * @example DateTime.fromObject({ year: 1982 }).toISODate() //=> '1982-01-01'\n   * @example DateTime.fromObject({ hour: 10, minute: 26, second: 6 }) //~> today at 10:26:06\n   * @example DateTime.fromObject({ hour: 10, minute: 26, second: 6 }, { zone: 'utc' }),\n   * @example DateTime.fromObject({ hour: 10, minute: 26, second: 6 }, { zone: 'local' })\n   * @example DateTime.fromObject({ hour: 10, minute: 26, second: 6 }, { zone: 'America/New_York' })\n   * @example DateTime.fromObject({ weekYear: 2016, weekNumber: 2, weekday: 3 }).toISODate() //=> '2016-01-13'\n   * @example DateTime.fromObject({ localWeekYear: 2022, localWeekNumber: 1, localWeekday: 1 }, { locale: \"en-US\" }).toISODate() //=> '2021-12-26'\n   * @return {DateTime}\n   */\n  static fromObject(obj, opts = {}) {\n    obj = obj || {};\n    const zoneToUse = normalizeZone(opts.zone, Settings.defaultZone);\n    if (!zoneToUse.isValid) {\n      return DateTime.invalid(unsupportedZone(zoneToUse));\n    }\n\n    const loc = Locale.fromObject(opts);\n    const normalized = normalizeObject(obj, normalizeUnitWithLocalWeeks);\n    const { minDaysInFirstWeek, startOfWeek } = usesLocalWeekValues(normalized, loc);\n\n    const tsNow = Settings.now(),\n      offsetProvis = !isUndefined(opts.specificOffset)\n        ? opts.specificOffset\n        : zoneToUse.offset(tsNow),\n      containsOrdinal = !isUndefined(normalized.ordinal),\n      containsGregorYear = !isUndefined(normalized.year),\n      containsGregorMD = !isUndefined(normalized.month) || !isUndefined(normalized.day),\n      containsGregor = containsGregorYear || containsGregorMD,\n      definiteWeekDef = normalized.weekYear || normalized.weekNumber;\n\n    // cases:\n    // just a weekday -> this week's instance of that weekday, no worries\n    // (gregorian data or ordinal) + (weekYear or weekNumber) -> error\n    // (gregorian month or day) + ordinal -> error\n    // otherwise just use weeks or ordinals or gregorian, depending on what's specified\n\n    if ((containsGregor || containsOrdinal) && definiteWeekDef) {\n      throw new ConflictingSpecificationError(\n        \"Can't mix weekYear/weekNumber units with year/month/day or ordinals\"\n      );\n    }\n\n    if (containsGregorMD && containsOrdinal) {\n      throw new ConflictingSpecificationError(\"Can't mix ordinal dates with month/day\");\n    }\n\n    const useWeekData = definiteWeekDef || (normalized.weekday && !containsGregor);\n\n    // configure ourselves to deal with gregorian dates or week stuff\n    let units,\n      defaultValues,\n      objNow = tsToObj(tsNow, offsetProvis);\n    if (useWeekData) {\n      units = orderedWeekUnits;\n      defaultValues = defaultWeekUnitValues;\n      objNow = gregorianToWeek(objNow, minDaysInFirstWeek, startOfWeek);\n    } else if (containsOrdinal) {\n      units = orderedOrdinalUnits;\n      defaultValues = defaultOrdinalUnitValues;\n      objNow = gregorianToOrdinal(objNow);\n    } else {\n      units = orderedUnits;\n      defaultValues = defaultUnitValues;\n    }\n\n    // set default values for missing stuff\n    let foundFirst = false;\n    for (const u of units) {\n      const v = normalized[u];\n      if (!isUndefined(v)) {\n        foundFirst = true;\n      } else if (foundFirst) {\n        normalized[u] = defaultValues[u];\n      } else {\n        normalized[u] = objNow[u];\n      }\n    }\n\n    // make sure the values we have are in range\n    const higherOrderInvalid = useWeekData\n        ? hasInvalidWeekData(normalized, minDaysInFirstWeek, startOfWeek)\n        : containsOrdinal\n        ? hasInvalidOrdinalData(normalized)\n        : hasInvalidGregorianData(normalized),\n      invalid = higherOrderInvalid || hasInvalidTimeData(normalized);\n\n    if (invalid) {\n      return DateTime.invalid(invalid);\n    }\n\n    // compute the actual time\n    const gregorian = useWeekData\n        ? weekToGregorian(normalized, minDaysInFirstWeek, startOfWeek)\n        : containsOrdinal\n        ? ordinalToGregorian(normalized)\n        : normalized,\n      [tsFinal, offsetFinal] = objToTS(gregorian, offsetProvis, zoneToUse),\n      inst = new DateTime({\n        ts: tsFinal,\n        zone: zoneToUse,\n        o: offsetFinal,\n        loc,\n      });\n\n    // gregorian data + weekday serves only to validate\n    if (normalized.weekday && containsGregor && obj.weekday !== inst.weekday) {\n      return DateTime.invalid(\n        \"mismatched weekday\",\n        `you can't specify both a weekday of ${normalized.weekday} and a date of ${inst.toISO()}`\n      );\n    }\n\n    if (!inst.isValid) {\n      return DateTime.invalid(inst.invalid);\n    }\n\n    return inst;\n  }\n\n  /**\n   * Create a DateTime from an ISO 8601 string\n   * @param {string} text - the ISO string\n   * @param {Object} opts - options to affect the creation\n   * @param {string|Zone} [opts.zone='local'] - use this zone if no offset is specified in the input string itself. Will also convert the time to this zone\n   * @param {boolean} [opts.setZone=false] - override the zone with a fixed-offset zone specified in the string itself, if it specifies one\n   * @param {string} [opts.locale='system's locale'] - a locale to set on the resulting DateTime instance\n   * @param {string} [opts.outputCalendar] - the output calendar to set on the resulting DateTime instance\n   * @param {string} [opts.numberingSystem] - the numbering system to set on the resulting DateTime instance\n   * @param {string} [opts.weekSettings] - the week settings to set on the resulting DateTime instance\n   * @example DateTime.fromISO('2016-05-25T09:08:34.123')\n   * @example DateTime.fromISO('2016-05-25T09:08:34.123+06:00')\n   * @example DateTime.fromISO('2016-05-25T09:08:34.123+06:00', {setZone: true})\n   * @example DateTime.fromISO('2016-05-25T09:08:34.123', {zone: 'utc'})\n   * @example DateTime.fromISO('2016-W05-4')\n   * @return {DateTime}\n   */\n  static fromISO(text, opts = {}) {\n    const [vals, parsedZone] = parseISODate(text);\n    return parseDataToDateTime(vals, parsedZone, opts, \"ISO 8601\", text);\n  }\n\n  /**\n   * Create a DateTime from an RFC 2822 string\n   * @param {string} text - the RFC 2822 string\n   * @param {Object} opts - options to affect the creation\n   * @param {string|Zone} [opts.zone='local'] - convert the time to this zone. Since the offset is always specified in the string itself, this has no effect on the interpretation of string, merely the zone the resulting DateTime is expressed in.\n   * @param {boolean} [opts.setZone=false] - override the zone with a fixed-offset zone specified in the string itself, if it specifies one\n   * @param {string} [opts.locale='system's locale'] - a locale to set on the resulting DateTime instance\n   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @param {string} opts.numberingSystem - the numbering system to set on the resulting DateTime instance\n   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance\n   * @example DateTime.fromRFC2822('25 Nov 2016 13:23:12 GMT')\n   * @example DateTime.fromRFC2822('Fri, 25 Nov 2016 13:23:12 +0600')\n   * @example DateTime.fromRFC2822('25 Nov 2016 13:23 Z')\n   * @return {DateTime}\n   */\n  static fromRFC2822(text, opts = {}) {\n    const [vals, parsedZone] = parseRFC2822Date(text);\n    return parseDataToDateTime(vals, parsedZone, opts, \"RFC 2822\", text);\n  }\n\n  /**\n   * Create a DateTime from an HTTP header date\n   * @see https://www.w3.org/Protocols/rfc2616/rfc2616-sec3.html#sec3.3.1\n   * @param {string} text - the HTTP header date\n   * @param {Object} opts - options to affect the creation\n   * @param {string|Zone} [opts.zone='local'] - convert the time to this zone. Since HTTP dates are always in UTC, this has no effect on the interpretation of string, merely the zone the resulting DateTime is expressed in.\n   * @param {boolean} [opts.setZone=false] - override the zone with the fixed-offset zone specified in the string. For HTTP dates, this is always UTC, so this option is equivalent to setting the `zone` option to 'utc', but this option is included for consistency with similar methods.\n   * @param {string} [opts.locale='system's locale'] - a locale to set on the resulting DateTime instance\n   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @param {string} opts.numberingSystem - the numbering system to set on the resulting DateTime instance\n   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance\n   * @example DateTime.fromHTTP('Sun, 06 Nov 1994 08:49:37 GMT')\n   * @example DateTime.fromHTTP('Sunday, 06-Nov-94 08:49:37 GMT')\n   * @example DateTime.fromHTTP('Sun Nov  6 08:49:37 1994')\n   * @return {DateTime}\n   */\n  static fromHTTP(text, opts = {}) {\n    const [vals, parsedZone] = parseHTTPDate(text);\n    return parseDataToDateTime(vals, parsedZone, opts, \"HTTP\", opts);\n  }\n\n  /**\n   * Create a DateTime from an input string and format string.\n   * Defaults to en-US if no locale has been specified, regardless of the system's locale. For a table of tokens and their interpretations, see [here](https://moment.github.io/luxon/#/parsing?id=table-of-tokens).\n   * @param {string} text - the string to parse\n   * @param {string} fmt - the format the string is expected to be in (see the link below for the formats)\n   * @param {Object} opts - options to affect the creation\n   * @param {string|Zone} [opts.zone='local'] - use this zone if no offset is specified in the input string itself. Will also convert the DateTime to this zone\n   * @param {boolean} [opts.setZone=false] - override the zone with a zone specified in the string itself, if it specifies one\n   * @param {string} [opts.locale='en-US'] - a locale string to use when parsing. Will also set the DateTime to this locale\n   * @param {string} opts.numberingSystem - the numbering system to use when parsing. Will also set the resulting DateTime to this numbering system\n   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance\n   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @return {DateTime}\n   */\n  static fromFormat(text, fmt, opts = {}) {\n    if (isUndefined(text) || isUndefined(fmt)) {\n      throw new InvalidArgumentError(\"fromFormat requires an input string and a format\");\n    }\n\n    const { locale = null, numberingSystem = null } = opts,\n      localeToUse = Locale.fromOpts({\n        locale,\n        numberingSystem,\n        defaultToEN: true,\n      }),\n      [vals, parsedZone, specificOffset, invalid] = parseFromTokens(localeToUse, text, fmt);\n    if (invalid) {\n      return DateTime.invalid(invalid);\n    } else {\n      return parseDataToDateTime(vals, parsedZone, opts, `format ${fmt}`, text, specificOffset);\n    }\n  }\n\n  /**\n   * @deprecated use fromFormat instead\n   */\n  static fromString(text, fmt, opts = {}) {\n    return DateTime.fromFormat(text, fmt, opts);\n  }\n\n  /**\n   * Create a DateTime from a SQL date, time, or datetime\n   * Defaults to en-US if no locale has been specified, regardless of the system's locale\n   * @param {string} text - the string to parse\n   * @param {Object} opts - options to affect the creation\n   * @param {string|Zone} [opts.zone='local'] - use this zone if no offset is specified in the input string itself. Will also convert the DateTime to this zone\n   * @param {boolean} [opts.setZone=false] - override the zone with a zone specified in the string itself, if it specifies one\n   * @param {string} [opts.locale='en-US'] - a locale string to use when parsing. Will also set the DateTime to this locale\n   * @param {string} opts.numberingSystem - the numbering system to use when parsing. Will also set the resulting DateTime to this numbering system\n   * @param {string} opts.weekSettings - the week settings to set on the resulting DateTime instance\n   * @param {string} opts.outputCalendar - the output calendar to set on the resulting DateTime instance\n   * @example DateTime.fromSQL('2017-05-15')\n   * @example DateTime.fromSQL('2017-05-15 09:12:34')\n   * @example DateTime.fromSQL('2017-05-15 09:12:34.342')\n   * @example DateTime.fromSQL('2017-05-15 09:12:34.342+06:00')\n   * @example DateTime.fromSQL('2017-05-15 09:12:34.342 America/Los_Angeles')\n   * @example DateTime.fromSQL('2017-05-15 09:12:34.342 America/Los_Angeles', { setZone: true })\n   * @example DateTime.fromSQL('2017-05-15 09:12:34.342', { zone: 'America/Los_Angeles' })\n   * @example DateTime.fromSQL('09:12:34.342')\n   * @return {DateTime}\n   */\n  static fromSQL(text, opts = {}) {\n    const [vals, parsedZone] = parseSQL(text);\n    return parseDataToDateTime(vals, parsedZone, opts, \"SQL\", text);\n  }\n\n  /**\n   * Create an invalid DateTime.\n   * @param {string} reason - simple string of why this DateTime is invalid. Should not contain parameters or anything else data-dependent.\n   * @param {string} [explanation=null] - longer explanation, may include parameters and other useful debugging information\n   * @return {DateTime}\n   */\n  static invalid(reason, explanation = null) {\n    if (!reason) {\n      throw new InvalidArgumentError(\"need to specify a reason the DateTime is invalid\");\n    }\n\n    const invalid = reason instanceof Invalid ? reason : new Invalid(reason, explanation);\n\n    if (Settings.throwOnInvalid) {\n      throw new InvalidDateTimeError(invalid);\n    } else {\n      return new DateTime({ invalid });\n    }\n  }\n\n  /**\n   * Check if an object is an instance of DateTime. Works across context boundaries\n   * @param {object} o\n   * @return {boolean}\n   */\n  static isDateTime(o) {\n    return (o && o.isLuxonDateTime) || false;\n  }\n\n  /**\n   * Produce the format string for a set of options\n   * @param formatOpts\n   * @param localeOpts\n   * @returns {string}\n   */\n  static parseFormatForOpts(formatOpts, localeOpts = {}) {\n    const tokenList = formatOptsToTokens(formatOpts, Locale.fromObject(localeOpts));\n    return !tokenList ? null : tokenList.map((t) => (t ? t.val : null)).join(\"\");\n  }\n\n  /**\n   * Produce the the fully expanded format token for the locale\n   * Does NOT quote characters, so quoted tokens will not round trip correctly\n   * @param fmt\n   * @param localeOpts\n   * @returns {string}\n   */\n  static expandFormat(fmt, localeOpts = {}) {\n    const expanded = expandMacroTokens(Formatter.parseFormat(fmt), Locale.fromObject(localeOpts));\n    return expanded.map((t) => t.val).join(\"\");\n  }\n\n  static resetCache() {\n    zoneOffsetTs = undefined;\n    zoneOffsetGuessCache = {};\n  }\n\n  // INFO\n\n  /**\n   * Get the value of unit.\n   * @param {string} unit - a unit such as 'minute' or 'day'\n   * @example DateTime.local(2017, 7, 4).get('month'); //=> 7\n   * @example DateTime.local(2017, 7, 4).get('day'); //=> 4\n   * @return {number}\n   */\n  get(unit) {\n    return this[unit];\n  }\n\n  /**\n   * Returns whether the DateTime is valid. Invalid DateTimes occur when:\n   * * The DateTime was created from invalid calendar information, such as the 13th month or February 30\n   * * The DateTime was created by an operation on another invalid date\n   * @type {boolean}\n   */\n  get isValid() {\n    return this.invalid === null;\n  }\n\n  /**\n   * Returns an error code if this DateTime is invalid, or null if the DateTime is valid\n   * @type {string}\n   */\n  get invalidReason() {\n    return this.invalid ? this.invalid.reason : null;\n  }\n\n  /**\n   * Returns an explanation of why this DateTime became invalid, or null if the DateTime is valid\n   * @type {string}\n   */\n  get invalidExplanation() {\n    return this.invalid ? this.invalid.explanation : null;\n  }\n\n  /**\n   * Get the locale of a DateTime, such 'en-GB'. The locale is used when formatting the DateTime\n   *\n   * @type {string}\n   */\n  get locale() {\n    return this.isValid ? this.loc.locale : null;\n  }\n\n  /**\n   * Get the numbering system of a DateTime, such 'beng'. The numbering system is used when formatting the DateTime\n   *\n   * @type {string}\n   */\n  get numberingSystem() {\n    return this.isValid ? this.loc.numberingSystem : null;\n  }\n\n  /**\n   * Get the output calendar of a DateTime, such 'islamic'. The output calendar is used when formatting the DateTime\n   *\n   * @type {string}\n   */\n  get outputCalendar() {\n    return this.isValid ? this.loc.outputCalendar : null;\n  }\n\n  /**\n   * Get the time zone associated with this DateTime.\n   * @type {Zone}\n   */\n  get zone() {\n    return this._zone;\n  }\n\n  /**\n   * Get the name of the time zone.\n   * @type {string}\n   */\n  get zoneName() {\n    return this.isValid ? this.zone.name : null;\n  }\n\n  /**\n   * Get the year\n   * @example DateTime.local(2017, 5, 25).year //=> 2017\n   * @type {number}\n   */\n  get year() {\n    return this.isValid ? this.c.year : NaN;\n  }\n\n  /**\n   * Get the quarter\n   * @example DateTime.local(2017, 5, 25).quarter //=> 2\n   * @type {number}\n   */\n  get quarter() {\n    return this.isValid ? Math.ceil(this.c.month / 3) : NaN;\n  }\n\n  /**\n   * Get the month (1-12).\n   * @example DateTime.local(2017, 5, 25).month //=> 5\n   * @type {number}\n   */\n  get month() {\n    return this.isValid ? this.c.month : NaN;\n  }\n\n  /**\n   * Get the day of the month (1-30ish).\n   * @example DateTime.local(2017, 5, 25).day //=> 25\n   * @type {number}\n   */\n  get day() {\n    return this.isValid ? this.c.day : NaN;\n  }\n\n  /**\n   * Get the hour of the day (0-23).\n   * @example DateTime.local(2017, 5, 25, 9).hour //=> 9\n   * @type {number}\n   */\n  get hour() {\n    return this.isValid ? this.c.hour : NaN;\n  }\n\n  /**\n   * Get the minute of the hour (0-59).\n   * @example DateTime.local(2017, 5, 25, 9, 30).minute //=> 30\n   * @type {number}\n   */\n  get minute() {\n    return this.isValid ? this.c.minute : NaN;\n  }\n\n  /**\n   * Get the second of the minute (0-59).\n   * @example DateTime.local(2017, 5, 25, 9, 30, 52).second //=> 52\n   * @type {number}\n   */\n  get second() {\n    return this.isValid ? this.c.second : NaN;\n  }\n\n  /**\n   * Get the millisecond of the second (0-999).\n   * @example DateTime.local(2017, 5, 25, 9, 30, 52, 654).millisecond //=> 654\n   * @type {number}\n   */\n  get millisecond() {\n    return this.isValid ? this.c.millisecond : NaN;\n  }\n\n  /**\n   * Get the week year\n   * @see https://en.wikipedia.org/wiki/ISO_week_date\n   * @example DateTime.local(2014, 12, 31).weekYear //=> 2015\n   * @type {number}\n   */\n  get weekYear() {\n    return this.isValid ? possiblyCachedWeekData(this).weekYear : NaN;\n  }\n\n  /**\n   * Get the week number of the week year (1-52ish).\n   * @see https://en.wikipedia.org/wiki/ISO_week_date\n   * @example DateTime.local(2017, 5, 25).weekNumber //=> 21\n   * @type {number}\n   */\n  get weekNumber() {\n    return this.isValid ? possiblyCachedWeekData(this).weekNumber : NaN;\n  }\n\n  /**\n   * Get the day of the week.\n   * 1 is Monday and 7 is Sunday\n   * @see https://en.wikipedia.org/wiki/ISO_week_date\n   * @example DateTime.local(2014, 11, 31).weekday //=> 4\n   * @type {number}\n   */\n  get weekday() {\n    return this.isValid ? possiblyCachedWeekData(this).weekday : NaN;\n  }\n\n  /**\n   * Returns true if this date is on a weekend according to the locale, false otherwise\n   * @returns {boolean}\n   */\n  get isWeekend() {\n    return this.isValid && this.loc.getWeekendDays().includes(this.weekday);\n  }\n\n  /**\n   * Get the day of the week according to the locale.\n   * 1 is the first day of the week and 7 is the last day of the week.\n   * If the locale assigns Sunday as the first day of the week, then a date which is a Sunday will return 1,\n   * @returns {number}\n   */\n  get localWeekday() {\n    return this.isValid ? possiblyCachedLocalWeekData(this).weekday : NaN;\n  }\n\n  /**\n   * Get the week number of the week year according to the locale. Different locales assign week numbers differently,\n   * because the week can start on different days of the week (see localWeekday) and because a different number of days\n   * is required for a week to count as the first week of a year.\n   * @returns {number}\n   */\n  get localWeekNumber() {\n    return this.isValid ? possiblyCachedLocalWeekData(this).weekNumber : NaN;\n  }\n\n  /**\n   * Get the week year according to the locale. Different locales assign week numbers (and therefor week years)\n   * differently, see localWeekNumber.\n   * @returns {number}\n   */\n  get localWeekYear() {\n    return this.isValid ? possiblyCachedLocalWeekData(this).weekYear : NaN;\n  }\n\n  /**\n   * Get the ordinal (meaning the day of the year)\n   * @example DateTime.local(2017, 5, 25).ordinal //=> 145\n   * @type {number|DateTime}\n   */\n  get ordinal() {\n    return this.isValid ? gregorianToOrdinal(this.c).ordinal : NaN;\n  }\n\n  /**\n   * Get the human readable short month name, such as 'Oct'.\n   * Defaults to the system's locale if no locale has been specified\n   * @example DateTime.local(2017, 10, 30).monthShort //=> Oct\n   * @type {string}\n   */\n  get monthShort() {\n    return this.isValid ? Info.months(\"short\", { locObj: this.loc })[this.month - 1] : null;\n  }\n\n  /**\n   * Get the human readable long month name, such as 'October'.\n   * Defaults to the system's locale if no locale has been specified\n   * @example DateTime.local(2017, 10, 30).monthLong //=> October\n   * @type {string}\n   */\n  get monthLong() {\n    return this.isValid ? Info.months(\"long\", { locObj: this.loc })[this.month - 1] : null;\n  }\n\n  /**\n   * Get the human readable short weekday, such as 'Mon'.\n   * Defaults to the system's locale if no locale has been specified\n   * @example DateTime.local(2017, 10, 30).weekdayShort //=> Mon\n   * @type {string}\n   */\n  get weekdayShort() {\n    return this.isValid ? Info.weekdays(\"short\", { locObj: this.loc })[this.weekday - 1] : null;\n  }\n\n  /**\n   * Get the human readable long weekday, such as 'Monday'.\n   * Defaults to the system's locale if no locale has been specified\n   * @example DateTime.local(2017, 10, 30).weekdayLong //=> Monday\n   * @type {string}\n   */\n  get weekdayLong() {\n    return this.isValid ? Info.weekdays(\"long\", { locObj: this.loc })[this.weekday - 1] : null;\n  }\n\n  /**\n   * Get the UTC offset of this DateTime in minutes\n   * @example DateTime.now().offset //=> -240\n   * @example DateTime.utc().offset //=> 0\n   * @type {number}\n   */\n  get offset() {\n    return this.isValid ? +this.o : NaN;\n  }\n\n  /**\n   * Get the short human name for the zone's current offset, for example \"EST\" or \"EDT\".\n   * Defaults to the system's locale if no locale has been specified\n   * @type {string}\n   */\n  get offsetNameShort() {\n    if (this.isValid) {\n      return this.zone.offsetName(this.ts, {\n        format: \"short\",\n        locale: this.locale,\n      });\n    } else {\n      return null;\n    }\n  }\n\n  /**\n   * Get the long human name for the zone's current offset, for example \"Eastern Standard Time\" or \"Eastern Daylight Time\".\n   * Defaults to the system's locale if no locale has been specified\n   * @type {string}\n   */\n  get offsetNameLong() {\n    if (this.isValid) {\n      return this.zone.offsetName(this.ts, {\n        format: \"long\",\n        locale: this.locale,\n      });\n    } else {\n      return null;\n    }\n  }\n\n  /**\n   * Get whether this zone's offset ever changes, as in a DST.\n   * @type {boolean}\n   */\n  get isOffsetFixed() {\n    return this.isValid ? this.zone.isUniversal : null;\n  }\n\n  /**\n   * Get whether the DateTime is in a DST.\n   * @type {boolean}\n   */\n  get isInDST() {\n    if (this.isOffsetFixed) {\n      return false;\n    } else {\n      return (\n        this.offset > this.set({ month: 1, day: 1 }).offset ||\n        this.offset > this.set({ month: 5 }).offset\n      );\n    }\n  }\n\n  /**\n   * Get those DateTimes which have the same local time as this DateTime, but a different offset from UTC\n   * in this DateTime's zone. During DST changes local time can be ambiguous, for example\n   * `2023-10-29T02:30:00` in `Europe/Berlin` can have offset `+01:00` or `+02:00`.\n   * This method will return both possible DateTimes if this DateTime's local time is ambiguous.\n   * @returns {DateTime[]}\n   */\n  getPossibleOffsets() {\n    if (!this.isValid || this.isOffsetFixed) {\n      return [this];\n    }\n    const dayMs = 86400000;\n    const minuteMs = 60000;\n    const localTS = objToLocalTS(this.c);\n    const oEarlier = this.zone.offset(localTS - dayMs);\n    const oLater = this.zone.offset(localTS + dayMs);\n\n    const o1 = this.zone.offset(localTS - oEarlier * minuteMs);\n    const o2 = this.zone.offset(localTS - oLater * minuteMs);\n    if (o1 === o2) {\n      return [this];\n    }\n    const ts1 = localTS - o1 * minuteMs;\n    const ts2 = localTS - o2 * minuteMs;\n    const c1 = tsToObj(ts1, o1);\n    const c2 = tsToObj(ts2, o2);\n    if (\n      c1.hour === c2.hour &&\n      c1.minute === c2.minute &&\n      c1.second === c2.second &&\n      c1.millisecond === c2.millisecond\n    ) {\n      return [clone(this, { ts: ts1 }), clone(this, { ts: ts2 })];\n    }\n    return [this];\n  }\n\n  /**\n   * Returns true if this DateTime is in a leap year, false otherwise\n   * @example DateTime.local(2016).isInLeapYear //=> true\n   * @example DateTime.local(2013).isInLeapYear //=> false\n   * @type {boolean}\n   */\n  get isInLeapYear() {\n    return isLeapYear(this.year);\n  }\n\n  /**\n   * Returns the number of days in this DateTime's month\n   * @example DateTime.local(2016, 2).daysInMonth //=> 29\n   * @example DateTime.local(2016, 3).daysInMonth //=> 31\n   * @type {number}\n   */\n  get daysInMonth() {\n    return daysInMonth(this.year, this.month);\n  }\n\n  /**\n   * Returns the number of days in this DateTime's year\n   * @example DateTime.local(2016).daysInYear //=> 366\n   * @example DateTime.local(2013).daysInYear //=> 365\n   * @type {number}\n   */\n  get daysInYear() {\n    return this.isValid ? daysInYear(this.year) : NaN;\n  }\n\n  /**\n   * Returns the number of weeks in this DateTime's year\n   * @see https://en.wikipedia.org/wiki/ISO_week_date\n   * @example DateTime.local(2004).weeksInWeekYear //=> 53\n   * @example DateTime.local(2013).weeksInWeekYear //=> 52\n   * @type {number}\n   */\n  get weeksInWeekYear() {\n    return this.isValid ? weeksInWeekYear(this.weekYear) : NaN;\n  }\n\n  /**\n   * Returns the number of weeks in this DateTime's local week year\n   * @example DateTime.local(2020, 6, {locale: 'en-US'}).weeksInLocalWeekYear //=> 52\n   * @example DateTime.local(2020, 6, {locale: 'de-DE'}).weeksInLocalWeekYear //=> 53\n   * @type {number}\n   */\n  get weeksInLocalWeekYear() {\n    return this.isValid\n      ? weeksInWeekYear(\n          this.localWeekYear,\n          this.loc.getMinDaysInFirstWeek(),\n          this.loc.getStartOfWeek()\n        )\n      : NaN;\n  }\n\n  /**\n   * Returns the resolved Intl options for this DateTime.\n   * This is useful in understanding the behavior of formatting methods\n   * @param {Object} opts - the same options as toLocaleString\n   * @return {Object}\n   */\n  resolvedLocaleOptions(opts = {}) {\n    const { locale, numberingSystem, calendar } = Formatter.create(\n      this.loc.clone(opts),\n      opts\n    ).resolvedOptions(this);\n    return { locale, numberingSystem, outputCalendar: calendar };\n  }\n\n  // TRANSFORM\n\n  /**\n   * \"Set\" the DateTime's zone to UTC. Returns a newly-constructed DateTime.\n   *\n   * Equivalent to {@link DateTime#setZone}('utc')\n   * @param {number} [offset=0] - optionally, an offset from UTC in minutes\n   * @param {Object} [opts={}] - options to pass to `setZone()`\n   * @return {DateTime}\n   */\n  toUTC(offset = 0, opts = {}) {\n    return this.setZone(FixedOffsetZone.instance(offset), opts);\n  }\n\n  /**\n   * \"Set\" the DateTime's zone to the host's local zone. Returns a newly-constructed DateTime.\n   *\n   * Equivalent to `setZone('local')`\n   * @return {DateTime}\n   */\n  toLocal() {\n    return this.setZone(Settings.defaultZone);\n  }\n\n  /**\n   * \"Set\" the DateTime's zone to specified zone. Returns a newly-constructed DateTime.\n   *\n   * By default, the setter keeps the underlying time the same (as in, the same timestamp), but the new instance will report different local times and consider DSTs when making computations, as with {@link DateTime#plus}. You may wish to use {@link DateTime#toLocal} and {@link DateTime#toUTC} which provide simple convenience wrappers for commonly used zones.\n   * @param {string|Zone} [zone='local'] - a zone identifier. As a string, that can be any IANA zone supported by the host environment, or a fixed-offset name of the form 'UTC+3', or the strings 'local' or 'utc'. You may also supply an instance of a {@link DateTime#Zone} class.\n   * @param {Object} opts - options\n   * @param {boolean} [opts.keepLocalTime=false] - If true, adjust the underlying time so that the local time stays the same, but in the target zone. You should rarely need this.\n   * @return {DateTime}\n   */\n  setZone(zone, { keepLocalTime = false, keepCalendarTime = false } = {}) {\n    zone = normalizeZone(zone, Settings.defaultZone);\n    if (zone.equals(this.zone)) {\n      return this;\n    } else if (!zone.isValid) {\n      return DateTime.invalid(unsupportedZone(zone));\n    } else {\n      let newTS = this.ts;\n      if (keepLocalTime || keepCalendarTime) {\n        const offsetGuess = zone.offset(this.ts);\n        const asObj = this.toObject();\n        [newTS] = objToTS(asObj, offsetGuess, zone);\n      }\n      return clone(this, { ts: newTS, zone });\n    }\n  }\n\n  /**\n   * \"Set\" the locale, numberingSystem, or outputCalendar. Returns a newly-constructed DateTime.\n   * @param {Object} properties - the properties to set\n   * @example DateTime.local(2017, 5, 25).reconfigure({ locale: 'en-GB' })\n   * @return {DateTime}\n   */\n  reconfigure({ locale, numberingSystem, outputCalendar } = {}) {\n    const loc = this.loc.clone({ locale, numberingSystem, outputCalendar });\n    return clone(this, { loc });\n  }\n\n  /**\n   * \"Set\" the locale. Returns a newly-constructed DateTime.\n   * Just a convenient alias for reconfigure({ locale })\n   * @example DateTime.local(2017, 5, 25).setLocale('en-GB')\n   * @return {DateTime}\n   */\n  setLocale(locale) {\n    return this.reconfigure({ locale });\n  }\n\n  /**\n   * \"Set\" the values of specified units. Returns a newly-constructed DateTime.\n   * You can only set units with this method; for \"setting\" metadata, see {@link DateTime#reconfigure} and {@link DateTime#setZone}.\n   *\n   * This method also supports setting locale-based week units, i.e. `localWeekday`, `localWeekNumber` and `localWeekYear`.\n   * They cannot be mixed with ISO-week units like `weekday`.\n   * @param {Object} values - a mapping of units to numbers\n   * @example dt.set({ year: 2017 })\n   * @example dt.set({ hour: 8, minute: 30 })\n   * @example dt.set({ weekday: 5 })\n   * @example dt.set({ year: 2005, ordinal: 234 })\n   * @return {DateTime}\n   */\n  set(values) {\n    if (!this.isValid) return this;\n\n    const normalized = normalizeObject(values, normalizeUnitWithLocalWeeks);\n    const { minDaysInFirstWeek, startOfWeek } = usesLocalWeekValues(normalized, this.loc);\n\n    const settingWeekStuff =\n        !isUndefined(normalized.weekYear) ||\n        !isUndefined(normalized.weekNumber) ||\n        !isUndefined(normalized.weekday),\n      containsOrdinal = !isUndefined(normalized.ordinal),\n      containsGregorYear = !isUndefined(normalized.year),\n      containsGregorMD = !isUndefined(normalized.month) || !isUndefined(normalized.day),\n      containsGregor = containsGregorYear || containsGregorMD,\n      definiteWeekDef = normalized.weekYear || normalized.weekNumber;\n\n    if ((containsGregor || containsOrdinal) && definiteWeekDef) {\n      throw new ConflictingSpecificationError(\n        \"Can't mix weekYear/weekNumber units with year/month/day or ordinals\"\n      );\n    }\n\n    if (containsGregorMD && containsOrdinal) {\n      throw new ConflictingSpecificationError(\"Can't mix ordinal dates with month/day\");\n    }\n\n    let mixed;\n    if (settingWeekStuff) {\n      mixed = weekToGregorian(\n        { ...gregorianToWeek(this.c, minDaysInFirstWeek, startOfWeek), ...normalized },\n        minDaysInFirstWeek,\n        startOfWeek\n      );\n    } else if (!isUndefined(normalized.ordinal)) {\n      mixed = ordinalToGregorian({ ...gregorianToOrdinal(this.c), ...normalized });\n    } else {\n      mixed = { ...this.toObject(), ...normalized };\n\n      // if we didn't set the day but we ended up on an overflow date,\n      // use the last day of the right month\n      if (isUndefined(normalized.day)) {\n        mixed.day = Math.min(daysInMonth(mixed.year, mixed.month), mixed.day);\n      }\n    }\n\n    const [ts, o] = objToTS(mixed, this.o, this.zone);\n    return clone(this, { ts, o });\n  }\n\n  /**\n   * Add a period of time to this DateTime and return the resulting DateTime\n   *\n   * Adding hours, minutes, seconds, or milliseconds increases the timestamp by the right number of milliseconds. Adding days, months, or years shifts the calendar, accounting for DSTs and leap years along the way. Thus, `dt.plus({ hours: 24 })` may result in a different time than `dt.plus({ days: 1 })` if there's a DST shift in between.\n   * @param {Duration|Object|number} duration - The amount to add. Either a Luxon Duration, a number of milliseconds, the object argument to Duration.fromObject()\n   * @example DateTime.now().plus(123) //~> in 123 milliseconds\n   * @example DateTime.now().plus({ minutes: 15 }) //~> in 15 minutes\n   * @example DateTime.now().plus({ days: 1 }) //~> this time tomorrow\n   * @example DateTime.now().plus({ days: -1 }) //~> this time yesterday\n   * @example DateTime.now().plus({ hours: 3, minutes: 13 }) //~> in 3 hr, 13 min\n   * @example DateTime.now().plus(Duration.fromObject({ hours: 3, minutes: 13 })) //~> in 3 hr, 13 min\n   * @return {DateTime}\n   */\n  plus(duration) {\n    if (!this.isValid) return this;\n    const dur = Duration.fromDurationLike(duration);\n    return clone(this, adjustTime(this, dur));\n  }\n\n  /**\n   * Subtract a period of time to this DateTime and return the resulting DateTime\n   * See {@link DateTime#plus}\n   * @param {Duration|Object|number} duration - The amount to subtract. Either a Luxon Duration, a number of milliseconds, the object argument to Duration.fromObject()\n   @return {DateTime}\n   */\n  minus(duration) {\n    if (!this.isValid) return this;\n    const dur = Duration.fromDurationLike(duration).negate();\n    return clone(this, adjustTime(this, dur));\n  }\n\n  /**\n   * \"Set\" this DateTime to the beginning of a unit of time.\n   * @param {string} unit - The unit to go to the beginning of. Can be 'year', 'quarter', 'month', 'week', 'day', 'hour', 'minute', 'second', or 'millisecond'.\n   * @param {Object} opts - options\n   * @param {boolean} [opts.useLocaleWeeks=false] - If true, use weeks based on the locale, i.e. use the locale-dependent start of the week\n   * @example DateTime.local(2014, 3, 3).startOf('month').toISODate(); //=> '2014-03-01'\n   * @example DateTime.local(2014, 3, 3).startOf('year').toISODate(); //=> '2014-01-01'\n   * @example DateTime.local(2014, 3, 3).startOf('week').toISODate(); //=> '2014-03-03', weeks always start on Mondays\n   * @example DateTime.local(2014, 3, 3, 5, 30).startOf('day').toISOTime(); //=> '00:00.000-05:00'\n   * @example DateTime.local(2014, 3, 3, 5, 30).startOf('hour').toISOTime(); //=> '05:00:00.000-05:00'\n   * @return {DateTime}\n   */\n  startOf(unit, { useLocaleWeeks = false } = {}) {\n    if (!this.isValid) return this;\n\n    const o = {},\n      normalizedUnit = Duration.normalizeUnit(unit);\n    switch (normalizedUnit) {\n      case \"years\":\n        o.month = 1;\n      // falls through\n      case \"quarters\":\n      case \"months\":\n        o.day = 1;\n      // falls through\n      case \"weeks\":\n      case \"days\":\n        o.hour = 0;\n      // falls through\n      case \"hours\":\n        o.minute = 0;\n      // falls through\n      case \"minutes\":\n        o.second = 0;\n      // falls through\n      case \"seconds\":\n        o.millisecond = 0;\n        break;\n      case \"milliseconds\":\n        break;\n      // no default, invalid units throw in normalizeUnit()\n    }\n\n    if (normalizedUnit === \"weeks\") {\n      if (useLocaleWeeks) {\n        const startOfWeek = this.loc.getStartOfWeek();\n        const { weekday } = this;\n        if (weekday < startOfWeek) {\n          o.weekNumber = this.weekNumber - 1;\n        }\n        o.weekday = startOfWeek;\n      } else {\n        o.weekday = 1;\n      }\n    }\n\n    if (normalizedUnit === \"quarters\") {\n      const q = Math.ceil(this.month / 3);\n      o.month = (q - 1) * 3 + 1;\n    }\n\n    return this.set(o);\n  }\n\n  /**\n   * \"Set\" this DateTime to the end (meaning the last millisecond) of a unit of time\n   * @param {string} unit - The unit to go to the end of. Can be 'year', 'quarter', 'month', 'week', 'day', 'hour', 'minute', 'second', or 'millisecond'.\n   * @param {Object} opts - options\n   * @param {boolean} [opts.useLocaleWeeks=false] - If true, use weeks based on the locale, i.e. use the locale-dependent start of the week\n   * @example DateTime.local(2014, 3, 3).endOf('month').toISO(); //=> '2014-03-31T23:59:59.999-05:00'\n   * @example DateTime.local(2014, 3, 3).endOf('year').toISO(); //=> '2014-12-31T23:59:59.999-05:00'\n   * @example DateTime.local(2014, 3, 3).endOf('week').toISO(); // => '2014-03-09T23:59:59.999-05:00', weeks start on Mondays\n   * @example DateTime.local(2014, 3, 3, 5, 30).endOf('day').toISO(); //=> '2014-03-03T23:59:59.999-05:00'\n   * @example DateTime.local(2014, 3, 3, 5, 30).endOf('hour').toISO(); //=> '2014-03-03T05:59:59.999-05:00'\n   * @return {DateTime}\n   */\n  endOf(unit, opts) {\n    return this.isValid\n      ? this.plus({ [unit]: 1 })\n          .startOf(unit, opts)\n          .minus(1)\n      : this;\n  }\n\n  // OUTPUT\n\n  /**\n   * Returns a string representation of this DateTime formatted according to the specified format string.\n   * **You may not want this.** See {@link DateTime#toLocaleString} for a more flexible formatting tool. For a table of tokens and their interpretations, see [here](https://moment.github.io/luxon/#/formatting?id=table-of-tokens).\n   * Defaults to en-US if no locale has been specified, regardless of the system's locale.\n   * @param {string} fmt - the format string\n   * @param {Object} opts - opts to override the configuration options on this DateTime\n   * @example DateTime.now().toFormat('yyyy LLL dd') //=> '2017 Apr 22'\n   * @example DateTime.now().setLocale('fr').toFormat('yyyy LLL dd') //=> '2017 avr. 22'\n   * @example DateTime.now().toFormat('yyyy LLL dd', { locale: \"fr\" }) //=> '2017 avr. 22'\n   * @example DateTime.now().toFormat(\"HH 'hours and' mm 'minutes'\") //=> '20 hours and 55 minutes'\n   * @return {string}\n   */\n  toFormat(fmt, opts = {}) {\n    return this.isValid\n      ? Formatter.create(this.loc.redefaultToEN(opts)).formatDateTimeFromString(this, fmt)\n      : INVALID;\n  }\n\n  /**\n   * Returns a localized string representing this date. Accepts the same options as the Intl.DateTimeFormat constructor and any presets defined by Luxon, such as `DateTime.DATE_FULL` or `DateTime.TIME_SIMPLE`.\n   * The exact behavior of this method is browser-specific, but in general it will return an appropriate representation\n   * of the DateTime in the assigned locale.\n   * Defaults to the system's locale if no locale has been specified\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/DateTimeFormat\n   * @param formatOpts {Object} - Intl.DateTimeFormat constructor options and configuration options\n   * @param {Object} opts - opts to override the configuration options on this DateTime\n   * @example DateTime.now().toLocaleString(); //=> 4/20/2017\n   * @example DateTime.now().setLocale('en-gb').toLocaleString(); //=> '20/04/2017'\n   * @example DateTime.now().toLocaleString(DateTime.DATE_FULL); //=> 'April 20, 2017'\n   * @example DateTime.now().toLocaleString(DateTime.DATE_FULL, { locale: 'fr' }); //=> '28 août 2022'\n   * @example DateTime.now().toLocaleString(DateTime.TIME_SIMPLE); //=> '11:32 AM'\n   * @example DateTime.now().toLocaleString(DateTime.DATETIME_SHORT); //=> '4/20/2017, 11:32 AM'\n   * @example DateTime.now().toLocaleString({ weekday: 'long', month: 'long', day: '2-digit' }); //=> 'Thursday, April 20'\n   * @example DateTime.now().toLocaleString({ weekday: 'short', month: 'short', day: '2-digit', hour: '2-digit', minute: '2-digit' }); //=> 'Thu, Apr 20, 11:27 AM'\n   * @example DateTime.now().toLocaleString({ hour: '2-digit', minute: '2-digit', hourCycle: 'h23' }); //=> '11:32'\n   * @return {string}\n   */\n  toLocaleString(formatOpts = Formats.DATE_SHORT, opts = {}) {\n    return this.isValid\n      ? Formatter.create(this.loc.clone(opts), formatOpts).formatDateTime(this)\n      : INVALID;\n  }\n\n  /**\n   * Returns an array of format \"parts\", meaning individual tokens along with metadata. This is allows callers to post-process individual sections of the formatted output.\n   * Defaults to the system's locale if no locale has been specified\n   * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/DateTimeFormat/formatToParts\n   * @param opts {Object} - Intl.DateTimeFormat constructor options, same as `toLocaleString`.\n   * @example DateTime.now().toLocaleParts(); //=> [\n   *                                   //=>   { type: 'day', value: '25' },\n   *                                   //=>   { type: 'literal', value: '/' },\n   *                                   //=>   { type: 'month', value: '05' },\n   *                                   //=>   { type: 'literal', value: '/' },\n   *                                   //=>   { type: 'year', value: '1982' }\n   *                                   //=> ]\n   */\n  toLocaleParts(opts = {}) {\n    return this.isValid\n      ? Formatter.create(this.loc.clone(opts), opts).formatDateTimeParts(this)\n      : [];\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this DateTime\n   * @param {Object} opts - options\n   * @param {boolean} [opts.suppressMilliseconds=false] - exclude milliseconds from the format if they're 0\n   * @param {boolean} [opts.suppressSeconds=false] - exclude seconds from the format if they're 0\n   * @param {boolean} [opts.includeOffset=true] - include the offset, such as 'Z' or '-04:00'\n   * @param {boolean} [opts.extendedZone=false] - add the time zone format extension\n   * @param {string} [opts.format='extended'] - choose between the basic and extended format\n   * @example DateTime.utc(1983, 5, 25).toISO() //=> '1982-05-25T00:00:00.000Z'\n   * @example DateTime.now().toISO() //=> '2017-04-22T20:47:05.335-04:00'\n   * @example DateTime.now().toISO({ includeOffset: false }) //=> '2017-04-22T20:47:05.335'\n   * @example DateTime.now().toISO({ format: 'basic' }) //=> '20170422T204705.335-0400'\n   * @return {string}\n   */\n  toISO({\n    format = \"extended\",\n    suppressSeconds = false,\n    suppressMilliseconds = false,\n    includeOffset = true,\n    extendedZone = false,\n  } = {}) {\n    if (!this.isValid) {\n      return null;\n    }\n\n    const ext = format === \"extended\";\n\n    let c = toISODate(this, ext);\n    c += \"T\";\n    c += toISOTime(this, ext, suppressSeconds, suppressMilliseconds, includeOffset, extendedZone);\n    return c;\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this DateTime's date component\n   * @param {Object} opts - options\n   * @param {string} [opts.format='extended'] - choose between the basic and extended format\n   * @example DateTime.utc(1982, 5, 25).toISODate() //=> '1982-05-25'\n   * @example DateTime.utc(1982, 5, 25).toISODate({ format: 'basic' }) //=> '19820525'\n   * @return {string}\n   */\n  toISODate({ format = \"extended\" } = {}) {\n    if (!this.isValid) {\n      return null;\n    }\n\n    return toISODate(this, format === \"extended\");\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this DateTime's week date\n   * @example DateTime.utc(1982, 5, 25).toISOWeekDate() //=> '1982-W21-2'\n   * @return {string}\n   */\n  toISOWeekDate() {\n    return toTechFormat(this, \"kkkk-'W'WW-c\");\n  }\n\n  /**\n   * Returns an ISO 8601-compliant string representation of this DateTime's time component\n   * @param {Object} opts - options\n   * @param {boolean} [opts.suppressMilliseconds=false] - exclude milliseconds from the format if they're 0\n   * @param {boolean} [opts.suppressSeconds=false] - exclude seconds from the format if they're 0\n   * @param {boolean} [opts.includeOffset=true] - include the offset, such as 'Z' or '-04:00'\n   * @param {boolean} [opts.extendedZone=true] - add the time zone format extension\n   * @param {boolean} [opts.includePrefix=false] - include the `T` prefix\n   * @param {string} [opts.format='extended'] - choose between the basic and extended format\n   * @example DateTime.utc().set({ hour: 7, minute: 34 }).toISOTime() //=> '07:34:19.361Z'\n   * @example DateTime.utc().set({ hour: 7, minute: 34, seconds: 0, milliseconds: 0 }).toISOTime({ suppressSeconds: true }) //=> '07:34Z'\n   * @example DateTime.utc().set({ hour: 7, minute: 34 }).toISOTime({ format: 'basic' }) //=> '073419.361Z'\n   * @example DateTime.utc().set({ hour: 7, minute: 34 }).toISOTime({ includePrefix: true }) //=> 'T07:34:19.361Z'\n   * @return {string}\n   */\n  toISOTime({\n    suppressMilliseconds = false,\n    suppressSeconds = false,\n    includeOffset = true,\n    includePrefix = false,\n    extendedZone = false,\n    format = \"extended\",\n  } = {}) {\n    if (!this.isValid) {\n      return null;\n    }\n\n    let c = includePrefix ? \"T\" : \"\";\n    return (\n      c +\n      toISOTime(\n        this,\n        format === \"extended\",\n        suppressSeconds,\n        suppressMilliseconds,\n        includeOffset,\n        extendedZone\n      )\n    );\n  }\n\n  /**\n   * Returns an RFC 2822-compatible string representation of this DateTime\n   * @example DateTime.utc(2014, 7, 13).toRFC2822() //=> 'Sun, 13 Jul 2014 00:00:00 +0000'\n   * @example DateTime.local(2014, 7, 13).toRFC2822() //=> 'Sun, 13 Jul 2014 00:00:00 -0400'\n   * @return {string}\n   */\n  toRFC2822() {\n    return toTechFormat(this, \"EEE, dd LLL yyyy HH:mm:ss ZZZ\", false);\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for use in HTTP headers. The output is always expressed in GMT.\n   * Specifically, the string conforms to RFC 1123.\n   * @see https://www.w3.org/Protocols/rfc2616/rfc2616-sec3.html#sec3.3.1\n   * @example DateTime.utc(2014, 7, 13).toHTTP() //=> 'Sun, 13 Jul 2014 00:00:00 GMT'\n   * @example DateTime.utc(2014, 7, 13, 19).toHTTP() //=> 'Sun, 13 Jul 2014 19:00:00 GMT'\n   * @return {string}\n   */\n  toHTTP() {\n    return toTechFormat(this.toUTC(), \"EEE, dd LLL yyyy HH:mm:ss 'GMT'\");\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for use in SQL Date\n   * @example DateTime.utc(2014, 7, 13).toSQLDate() //=> '2014-07-13'\n   * @return {string}\n   */\n  toSQLDate() {\n    if (!this.isValid) {\n      return null;\n    }\n    return toISODate(this, true);\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for use in SQL Time\n   * @param {Object} opts - options\n   * @param {boolean} [opts.includeZone=false] - include the zone, such as 'America/New_York'. Overrides includeOffset.\n   * @param {boolean} [opts.includeOffset=true] - include the offset, such as 'Z' or '-04:00'\n   * @param {boolean} [opts.includeOffsetSpace=true] - include the space between the time and the offset, such as '05:15:16.345 -04:00'\n   * @example DateTime.utc().toSQL() //=> '05:15:16.345'\n   * @example DateTime.now().toSQL() //=> '05:15:16.345 -04:00'\n   * @example DateTime.now().toSQL({ includeOffset: false }) //=> '05:15:16.345'\n   * @example DateTime.now().toSQL({ includeZone: false }) //=> '05:15:16.345 America/New_York'\n   * @return {string}\n   */\n  toSQLTime({ includeOffset = true, includeZone = false, includeOffsetSpace = true } = {}) {\n    let fmt = \"HH:mm:ss.SSS\";\n\n    if (includeZone || includeOffset) {\n      if (includeOffsetSpace) {\n        fmt += \" \";\n      }\n      if (includeZone) {\n        fmt += \"z\";\n      } else if (includeOffset) {\n        fmt += \"ZZ\";\n      }\n    }\n\n    return toTechFormat(this, fmt, true);\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for use in SQL DateTime\n   * @param {Object} opts - options\n   * @param {boolean} [opts.includeZone=false] - include the zone, such as 'America/New_York'. Overrides includeOffset.\n   * @param {boolean} [opts.includeOffset=true] - include the offset, such as 'Z' or '-04:00'\n   * @param {boolean} [opts.includeOffsetSpace=true] - include the space between the time and the offset, such as '05:15:16.345 -04:00'\n   * @example DateTime.utc(2014, 7, 13).toSQL() //=> '2014-07-13 00:00:00.000 Z'\n   * @example DateTime.local(2014, 7, 13).toSQL() //=> '2014-07-13 00:00:00.000 -04:00'\n   * @example DateTime.local(2014, 7, 13).toSQL({ includeOffset: false }) //=> '2014-07-13 00:00:00.000'\n   * @example DateTime.local(2014, 7, 13).toSQL({ includeZone: true }) //=> '2014-07-13 00:00:00.000 America/New_York'\n   * @return {string}\n   */\n  toSQL(opts = {}) {\n    if (!this.isValid) {\n      return null;\n    }\n\n    return `${this.toSQLDate()} ${this.toSQLTime(opts)}`;\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for debugging\n   * @return {string}\n   */\n  toString() {\n    return this.isValid ? this.toISO() : INVALID;\n  }\n\n  /**\n   * Returns a string representation of this DateTime appropriate for the REPL.\n   * @return {string}\n   */\n  [Symbol.for(\"nodejs.util.inspect.custom\")]() {\n    if (this.isValid) {\n      return `DateTime { ts: ${this.toISO()}, zone: ${this.zone.name}, locale: ${this.locale} }`;\n    } else {\n      return `DateTime { Invalid, reason: ${this.invalidReason} }`;\n    }\n  }\n\n  /**\n   * Returns the epoch milliseconds of this DateTime. Alias of {@link DateTime#toMillis}\n   * @return {number}\n   */\n  valueOf() {\n    return this.toMillis();\n  }\n\n  /**\n   * Returns the epoch milliseconds of this DateTime.\n   * @return {number}\n   */\n  toMillis() {\n    return this.isValid ? this.ts : NaN;\n  }\n\n  /**\n   * Returns the epoch seconds of this DateTime.\n   * @return {number}\n   */\n  toSeconds() {\n    return this.isValid ? this.ts / 1000 : NaN;\n  }\n\n  /**\n   * Returns the epoch seconds (as a whole number) of this DateTime.\n   * @return {number}\n   */\n  toUnixInteger() {\n    return this.isValid ? Math.floor(this.ts / 1000) : NaN;\n  }\n\n  /**\n   * Returns an ISO 8601 representation of this DateTime appropriate for use in JSON.\n   * @return {string}\n   */\n  toJSON() {\n    return this.toISO();\n  }\n\n  /**\n   * Returns a BSON serializable equivalent to this DateTime.\n   * @return {Date}\n   */\n  toBSON() {\n    return this.toJSDate();\n  }\n\n  /**\n   * Returns a JavaScript object with this DateTime's year, month, day, and so on.\n   * @param opts - options for generating the object\n   * @param {boolean} [opts.includeConfig=false] - include configuration attributes in the output\n   * @example DateTime.now().toObject() //=> { year: 2017, month: 4, day: 22, hour: 20, minute: 49, second: 42, millisecond: 268 }\n   * @return {Object}\n   */\n  toObject(opts = {}) {\n    if (!this.isValid) return {};\n\n    const base = { ...this.c };\n\n    if (opts.includeConfig) {\n      base.outputCalendar = this.outputCalendar;\n      base.numberingSystem = this.loc.numberingSystem;\n      base.locale = this.loc.locale;\n    }\n    return base;\n  }\n\n  /**\n   * Returns a JavaScript Date equivalent to this DateTime.\n   * @return {Date}\n   */\n  toJSDate() {\n    return new Date(this.isValid ? this.ts : NaN);\n  }\n\n  // COMPARE\n\n  /**\n   * Return the difference between two DateTimes as a Duration.\n   * @param {DateTime} otherDateTime - the DateTime to compare this one to\n   * @param {string|string[]} [unit=['milliseconds']] - the unit or array of units (such as 'hours' or 'days') to include in the duration.\n   * @param {Object} opts - options that affect the creation of the Duration\n   * @param {string} [opts.conversionAccuracy='casual'] - the conversion system to use\n   * @example\n   * var i1 = DateTime.fromISO('1982-05-25T09:45'),\n   *     i2 = DateTime.fromISO('1983-10-14T10:30');\n   * i2.diff(i1).toObject() //=> { milliseconds: 43807500000 }\n   * i2.diff(i1, 'hours').toObject() //=> { hours: 12168.75 }\n   * i2.diff(i1, ['months', 'days']).toObject() //=> { months: 16, days: 19.03125 }\n   * i2.diff(i1, ['months', 'days', 'hours']).toObject() //=> { months: 16, days: 19, hours: 0.75 }\n   * @return {Duration}\n   */\n  diff(otherDateTime, unit = \"milliseconds\", opts = {}) {\n    if (!this.isValid || !otherDateTime.isValid) {\n      return Duration.invalid(\"created by diffing an invalid DateTime\");\n    }\n\n    const durOpts = { locale: this.locale, numberingSystem: this.numberingSystem, ...opts };\n\n    const units = maybeArray(unit).map(Duration.normalizeUnit),\n      otherIsLater = otherDateTime.valueOf() > this.valueOf(),\n      earlier = otherIsLater ? this : otherDateTime,\n      later = otherIsLater ? otherDateTime : this,\n      diffed = diff(earlier, later, units, durOpts);\n\n    return otherIsLater ? diffed.negate() : diffed;\n  }\n\n  /**\n   * Return the difference between this DateTime and right now.\n   * See {@link DateTime#diff}\n   * @param {string|string[]} [unit=['milliseconds']] - the unit or units units (such as 'hours' or 'days') to include in the duration\n   * @param {Object} opts - options that affect the creation of the Duration\n   * @param {string} [opts.conversionAccuracy='casual'] - the conversion system to use\n   * @return {Duration}\n   */\n  diffNow(unit = \"milliseconds\", opts = {}) {\n    return this.diff(DateTime.now(), unit, opts);\n  }\n\n  /**\n   * Return an Interval spanning between this DateTime and another DateTime\n   * @param {DateTime} otherDateTime - the other end point of the Interval\n   * @return {Interval}\n   */\n  until(otherDateTime) {\n    return this.isValid ? Interval.fromDateTimes(this, otherDateTime) : this;\n  }\n\n  /**\n   * Return whether this DateTime is in the same unit of time as another DateTime.\n   * Higher-order units must also be identical for this function to return `true`.\n   * Note that time zones are **ignored** in this comparison, which compares the **local** calendar time. Use {@link DateTime#setZone} to convert one of the dates if needed.\n   * @param {DateTime} otherDateTime - the other DateTime\n   * @param {string} unit - the unit of time to check sameness on\n   * @param {Object} opts - options\n   * @param {boolean} [opts.useLocaleWeeks=false] - If true, use weeks based on the locale, i.e. use the locale-dependent start of the week; only the locale of this DateTime is used\n   * @example DateTime.now().hasSame(otherDT, 'day'); //~> true if otherDT is in the same current calendar day\n   * @return {boolean}\n   */\n  hasSame(otherDateTime, unit, opts) {\n    if (!this.isValid) return false;\n\n    const inputMs = otherDateTime.valueOf();\n    const adjustedToZone = this.setZone(otherDateTime.zone, { keepLocalTime: true });\n    return (\n      adjustedToZone.startOf(unit, opts) <= inputMs && inputMs <= adjustedToZone.endOf(unit, opts)\n    );\n  }\n\n  /**\n   * Equality check\n   * Two DateTimes are equal if and only if they represent the same millisecond, have the same zone and location, and are both valid.\n   * To compare just the millisecond values, use `+dt1 === +dt2`.\n   * @param {DateTime} other - the other DateTime\n   * @return {boolean}\n   */\n  equals(other) {\n    return (\n      this.isValid &&\n      other.isValid &&\n      this.valueOf() === other.valueOf() &&\n      this.zone.equals(other.zone) &&\n      this.loc.equals(other.loc)\n    );\n  }\n\n  /**\n   * Returns a string representation of a this time relative to now, such as \"in two days\". Can only internationalize if your\n   * platform supports Intl.RelativeTimeFormat. Rounds down by default.\n   * @param {Object} options - options that affect the output\n   * @param {DateTime} [options.base=DateTime.now()] - the DateTime to use as the basis to which this time is compared. Defaults to now.\n   * @param {string} [options.style=\"long\"] - the style of units, must be \"long\", \"short\", or \"narrow\"\n   * @param {string|string[]} options.unit - use a specific unit or array of units; if omitted, or an array, the method will pick the best unit. Use an array or one of \"years\", \"quarters\", \"months\", \"weeks\", \"days\", \"hours\", \"minutes\", or \"seconds\"\n   * @param {boolean} [options.round=true] - whether to round the numbers in the output.\n   * @param {number} [options.padding=0] - padding in milliseconds. This allows you to round up the result if it fits inside the threshold. Don't use in combination with {round: false} because the decimal output will include the padding.\n   * @param {string} options.locale - override the locale of this DateTime\n   * @param {string} options.numberingSystem - override the numberingSystem of this DateTime. The Intl system may choose not to honor this\n   * @example DateTime.now().plus({ days: 1 }).toRelative() //=> \"in 1 day\"\n   * @example DateTime.now().setLocale(\"es\").toRelative({ days: 1 }) //=> \"dentro de 1 día\"\n   * @example DateTime.now().plus({ days: 1 }).toRelative({ locale: \"fr\" }) //=> \"dans 23 heures\"\n   * @example DateTime.now().minus({ days: 2 }).toRelative() //=> \"2 days ago\"\n   * @example DateTime.now().minus({ days: 2 }).toRelative({ unit: \"hours\" }) //=> \"48 hours ago\"\n   * @example DateTime.now().minus({ hours: 36 }).toRelative({ round: false }) //=> \"1.5 days ago\"\n   */\n  toRelative(options = {}) {\n    if (!this.isValid) return null;\n    const base = options.base || DateTime.fromObject({}, { zone: this.zone }),\n      padding = options.padding ? (this < base ? -options.padding : options.padding) : 0;\n    let units = [\"years\", \"months\", \"days\", \"hours\", \"minutes\", \"seconds\"];\n    let unit = options.unit;\n    if (Array.isArray(options.unit)) {\n      units = options.unit;\n      unit = undefined;\n    }\n    return diffRelative(base, this.plus(padding), {\n      ...options,\n      numeric: \"always\",\n      units,\n      unit,\n    });\n  }\n\n  /**\n   * Returns a string representation of this date relative to today, such as \"yesterday\" or \"next month\".\n   * Only internationalizes on platforms that supports Intl.RelativeTimeFormat.\n   * @param {Object} options - options that affect the output\n   * @param {DateTime} [options.base=DateTime.now()] - the DateTime to use as the basis to which this time is compared. Defaults to now.\n   * @param {string} options.locale - override the locale of this DateTime\n   * @param {string} options.unit - use a specific unit; if omitted, the method will pick the unit. Use one of \"years\", \"quarters\", \"months\", \"weeks\", or \"days\"\n   * @param {string} options.numberingSystem - override the numberingSystem of this DateTime. The Intl system may choose not to honor this\n   * @example DateTime.now().plus({ days: 1 }).toRelativeCalendar() //=> \"tomorrow\"\n   * @example DateTime.now().setLocale(\"es\").plus({ days: 1 }).toRelative() //=> \"\"mañana\"\n   * @example DateTime.now().plus({ days: 1 }).toRelativeCalendar({ locale: \"fr\" }) //=> \"demain\"\n   * @example DateTime.now().minus({ days: 2 }).toRelativeCalendar() //=> \"2 days ago\"\n   */\n  toRelativeCalendar(options = {}) {\n    if (!this.isValid) return null;\n\n    return diffRelative(options.base || DateTime.fromObject({}, { zone: this.zone }), this, {\n      ...options,\n      numeric: \"auto\",\n      units: [\"years\", \"months\", \"days\"],\n      calendary: true,\n    });\n  }\n\n  /**\n   * Return the min of several date times\n   * @param {...DateTime} dateTimes - the DateTimes from which to choose the minimum\n   * @return {DateTime} the min DateTime, or undefined if called with no argument\n   */\n  static min(...dateTimes) {\n    if (!dateTimes.every(DateTime.isDateTime)) {\n      throw new InvalidArgumentError(\"min requires all arguments be DateTimes\");\n    }\n    return bestBy(dateTimes, (i) => i.valueOf(), Math.min);\n  }\n\n  /**\n   * Return the max of several date times\n   * @param {...DateTime} dateTimes - the DateTimes from which to choose the maximum\n   * @return {DateTime} the max DateTime, or undefined if called with no argument\n   */\n  static max(...dateTimes) {\n    if (!dateTimes.every(DateTime.isDateTime)) {\n      throw new InvalidArgumentError(\"max requires all arguments be DateTimes\");\n    }\n    return bestBy(dateTimes, (i) => i.valueOf(), Math.max);\n  }\n\n  // MISC\n\n  /**\n   * Explain how a string would be parsed by fromFormat()\n   * @param {string} text - the string to parse\n   * @param {string} fmt - the format the string is expected to be in (see description)\n   * @param {Object} options - options taken by fromFormat()\n   * @return {Object}\n   */\n  static fromFormatExplain(text, fmt, options = {}) {\n    const { locale = null, numberingSystem = null } = options,\n      localeToUse = Locale.fromOpts({\n        locale,\n        numberingSystem,\n        defaultToEN: true,\n      });\n    return explainFromTokens(localeToUse, text, fmt);\n  }\n\n  /**\n   * @deprecated use fromFormatExplain instead\n   */\n  static fromStringExplain(text, fmt, options = {}) {\n    return DateTime.fromFormatExplain(text, fmt, options);\n  }\n\n  /**\n   * Build a parser for `fmt` using the given locale. This parser can be passed\n   * to {@link DateTime.fromFormatParser} to a parse a date in this format. This\n   * can be used to optimize cases where many dates need to be parsed in a\n   * specific format.\n   *\n   * @param {String} fmt - the format the string is expected to be in (see\n   * description)\n   * @param {Object} options - options used to set locale and numberingSystem\n   * for parser\n   * @returns {TokenParser} - opaque object to be used\n   */\n  static buildFormatParser(fmt, options = {}) {\n    const { locale = null, numberingSystem = null } = options,\n      localeToUse = Locale.fromOpts({\n        locale,\n        numberingSystem,\n        defaultToEN: true,\n      });\n    return new TokenParser(localeToUse, fmt);\n  }\n\n  /**\n   * Create a DateTime from an input string and format parser.\n   *\n   * The format parser must have been created with the same locale as this call.\n   *\n   * @param {String} text - the string to parse\n   * @param {TokenParser} formatParser - parser from {@link DateTime.buildFormatParser}\n   * @param {Object} opts - options taken by fromFormat()\n   * @returns {DateTime}\n   */\n  static fromFormatParser(text, formatParser, opts = {}) {\n    if (isUndefined(text) || isUndefined(formatParser)) {\n      throw new InvalidArgumentError(\n        \"fromFormatParser requires an input string and a format parser\"\n      );\n    }\n    const { locale = null, numberingSystem = null } = opts,\n      localeToUse = Locale.fromOpts({\n        locale,\n        numberingSystem,\n        defaultToEN: true,\n      });\n\n    if (!localeToUse.equals(formatParser.locale)) {\n      throw new InvalidArgumentError(\n        `fromFormatParser called with a locale of ${localeToUse}, ` +\n          `but the format parser was created for ${formatParser.locale}`\n      );\n    }\n\n    const { result, zone, specificOffset, invalidReason } = formatParser.explainFromTokens(text);\n\n    if (invalidReason) {\n      return DateTime.invalid(invalidReason);\n    } else {\n      return parseDataToDateTime(\n        result,\n        zone,\n        opts,\n        `format ${formatParser.format}`,\n        text,\n        specificOffset\n      );\n    }\n  }\n\n  // FORMAT PRESETS\n\n  /**\n   * {@link DateTime#toLocaleString} format like 10/14/1983\n   * @type {Object}\n   */\n  static get DATE_SHORT() {\n    return Formats.DATE_SHORT;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Oct 14, 1983'\n   * @type {Object}\n   */\n  static get DATE_MED() {\n    return Formats.DATE_MED;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Fri, Oct 14, 1983'\n   * @type {Object}\n   */\n  static get DATE_MED_WITH_WEEKDAY() {\n    return Formats.DATE_MED_WITH_WEEKDAY;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'October 14, 1983'\n   * @type {Object}\n   */\n  static get DATE_FULL() {\n    return Formats.DATE_FULL;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Tuesday, October 14, 1983'\n   * @type {Object}\n   */\n  static get DATE_HUGE() {\n    return Formats.DATE_HUGE;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get TIME_SIMPLE() {\n    return Formats.TIME_SIMPLE;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get TIME_WITH_SECONDS() {\n    return Formats.TIME_WITH_SECONDS;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23 AM EDT'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get TIME_WITH_SHORT_OFFSET() {\n    return Formats.TIME_WITH_SHORT_OFFSET;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23 AM Eastern Daylight Time'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get TIME_WITH_LONG_OFFSET() {\n    return Formats.TIME_WITH_LONG_OFFSET;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30', always 24-hour.\n   * @type {Object}\n   */\n  static get TIME_24_SIMPLE() {\n    return Formats.TIME_24_SIMPLE;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23', always 24-hour.\n   * @type {Object}\n   */\n  static get TIME_24_WITH_SECONDS() {\n    return Formats.TIME_24_WITH_SECONDS;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23 EDT', always 24-hour.\n   * @type {Object}\n   */\n  static get TIME_24_WITH_SHORT_OFFSET() {\n    return Formats.TIME_24_WITH_SHORT_OFFSET;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '09:30:23 Eastern Daylight Time', always 24-hour.\n   * @type {Object}\n   */\n  static get TIME_24_WITH_LONG_OFFSET() {\n    return Formats.TIME_24_WITH_LONG_OFFSET;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '10/14/1983, 9:30 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_SHORT() {\n    return Formats.DATETIME_SHORT;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like '10/14/1983, 9:30:33 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_SHORT_WITH_SECONDS() {\n    return Formats.DATETIME_SHORT_WITH_SECONDS;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Oct 14, 1983, 9:30 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_MED() {\n    return Formats.DATETIME_MED;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Oct 14, 1983, 9:30:33 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_MED_WITH_SECONDS() {\n    return Formats.DATETIME_MED_WITH_SECONDS;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Fri, 14 Oct 1983, 9:30 AM'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_MED_WITH_WEEKDAY() {\n    return Formats.DATETIME_MED_WITH_WEEKDAY;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'October 14, 1983, 9:30 AM EDT'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_FULL() {\n    return Formats.DATETIME_FULL;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'October 14, 1983, 9:30:33 AM EDT'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_FULL_WITH_SECONDS() {\n    return Formats.DATETIME_FULL_WITH_SECONDS;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Friday, October 14, 1983, 9:30 AM Eastern Daylight Time'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_HUGE() {\n    return Formats.DATETIME_HUGE;\n  }\n\n  /**\n   * {@link DateTime#toLocaleString} format like 'Friday, October 14, 1983, 9:30:33 AM Eastern Daylight Time'. Only 12-hour if the locale is.\n   * @type {Object}\n   */\n  static get DATETIME_HUGE_WITH_SECONDS() {\n    return Formats.DATETIME_HUGE_WITH_SECONDS;\n  }\n}\n\n/**\n * @private\n */\nexport function friendlyDateTime(dateTimeish) {\n  if (DateTime.isDateTime(dateTimeish)) {\n    return dateTimeish;\n  } else if (dateTimeish && dateTimeish.valueOf && isNumber(dateTimeish.valueOf())) {\n    return DateTime.fromJSDate(dateTimeish);\n  } else if (dateTimeish && typeof dateTimeish === \"object\") {\n    return DateTime.fromObject(dateTimeish);\n  } else {\n    throw new InvalidArgumentError(\n      `Unknown datetime argument: ${dateTimeish}, of type ${typeof dateTimeish}`\n    );\n  }\n}\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,eAAe;AACpC,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,IAAI,MAAM,WAAW;AAC5B,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,OAAOC,eAAe,MAAM,4BAA4B;AACxD,OAAOC,MAAM,MAAM,kBAAkB;AACrC,SACEC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,MAAM,EACNC,WAAW,EACXC,UAAU,EACVC,UAAU,EACVC,eAAe,EACfC,eAAe,EACfC,OAAO,EACPC,YAAY,EACZC,QAAQ,QACH,gBAAgB;AACvB,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,IAAI,MAAM,gBAAgB;AACjC,SAASC,gBAAgB,EAAEC,YAAY,EAAEC,aAAa,EAAEC,QAAQ,QAAQ,uBAAuB;AAC/F,SACEC,eAAe,EACfC,iBAAiB,EACjBC,kBAAkB,EAClBC,iBAAiB,EACjBC,WAAW,QACN,uBAAuB;AAC9B,SACEC,eAAe,EACfC,eAAe,EACfC,kBAAkB,EAClBC,kBAAkB,EAClBC,uBAAuB,EACvBC,kBAAkB,EAClBC,qBAAqB,EACrBC,kBAAkB,EAClBC,mBAAmB,EACnBC,iBAAiB,QACZ,uBAAuB;AAC9B,OAAO,KAAKC,OAAO,MAAM,mBAAmB;AAC5C,SACEC,oBAAoB,EACpBC,6BAA6B,EAC7BC,gBAAgB,EAChBC,oBAAoB,QACf,aAAa;AACpB,OAAOC,OAAO,MAAM,mBAAmB;AAEvC,MAAMC,OAAO,GAAG,kBAAkB;AAClC,MAAMC,QAAQ,GAAG,OAAO;AAExB,SAASC,eAAeA,CAACC,IAAI,EAAE;EAC7B,OAAO,IAAIJ,OAAO,CAAC,kBAAkB,EAAG,aAAYI,IAAI,CAACC,IAAK,oBAAmB,CAAC;AACpF;;AAEA;AACA;AACA;AACA;AACA,SAASC,sBAAsBA,CAACC,EAAE,EAAE;EAClC,IAAIA,EAAE,CAACC,QAAQ,KAAK,IAAI,EAAE;IACxBD,EAAE,CAACC,QAAQ,GAAGvB,eAAe,CAACsB,EAAE,CAACE,CAAC,CAAC;EACrC;EACA,OAAOF,EAAE,CAACC,QAAQ;AACpB;;AAEA;AACA;AACA;AACA,SAASE,2BAA2BA,CAACH,EAAE,EAAE;EACvC,IAAIA,EAAE,CAACI,aAAa,KAAK,IAAI,EAAE;IAC7BJ,EAAE,CAACI,aAAa,GAAG1B,eAAe,CAChCsB,EAAE,CAACE,CAAC,EACJF,EAAE,CAACK,GAAG,CAACC,qBAAqB,CAAC,CAAC,EAC9BN,EAAE,CAACK,GAAG,CAACE,cAAc,CAAC,CACxB,CAAC;EACH;EACA,OAAOP,EAAE,CAACI,aAAa;AACzB;;AAEA;AACA;AACA,SAASI,KAAKA,CAACC,IAAI,EAAEC,IAAI,EAAE;EACzB,MAAMC,OAAO,GAAG;IACdC,EAAE,EAAEH,IAAI,CAACG,EAAE;IACXf,IAAI,EAAEY,IAAI,CAACZ,IAAI;IACfK,CAAC,EAAEO,IAAI,CAACP,CAAC;IACTW,CAAC,EAAEJ,IAAI,CAACI,CAAC;IACTR,GAAG,EAAEI,IAAI,CAACJ,GAAG;IACbS,OAAO,EAAEL,IAAI,CAACK;EAChB,CAAC;EACD,OAAO,IAAIC,QAAQ,CAAC;IAAE,GAAGJ,OAAO;IAAE,GAAGD,IAAI;IAAEM,GAAG,EAAEL;EAAQ,CAAC,CAAC;AAC5D;;AAEA;AACA;AACA,SAASM,SAASA,CAACC,OAAO,EAAEL,CAAC,EAAEM,EAAE,EAAE;EACjC;EACA,IAAIC,QAAQ,GAAGF,OAAO,GAAGL,CAAC,GAAG,EAAE,GAAG,IAAI;;EAEtC;EACA,MAAMQ,EAAE,GAAGF,EAAE,CAACG,MAAM,CAACF,QAAQ,CAAC;;EAE9B;EACA,IAAIP,CAAC,KAAKQ,EAAE,EAAE;IACZ,OAAO,CAACD,QAAQ,EAAEP,CAAC,CAAC;EACtB;;EAEA;EACAO,QAAQ,IAAI,CAACC,EAAE,GAAGR,CAAC,IAAI,EAAE,GAAG,IAAI;;EAEhC;EACA,MAAMU,EAAE,GAAGJ,EAAE,CAACG,MAAM,CAACF,QAAQ,CAAC;EAC9B,IAAIC,EAAE,KAAKE,EAAE,EAAE;IACb,OAAO,CAACH,QAAQ,EAAEC,EAAE,CAAC;EACvB;;EAEA;EACA,OAAO,CAACH,OAAO,GAAGM,IAAI,CAACC,GAAG,CAACJ,EAAE,EAAEE,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,EAAEC,IAAI,CAACE,GAAG,CAACL,EAAE,EAAEE,EAAE,CAAC,CAAC;AACnE;;AAEA;AACA,SAASI,OAAOA,CAACf,EAAE,EAAEU,MAAM,EAAE;EAC3BV,EAAE,IAAIU,MAAM,GAAG,EAAE,GAAG,IAAI;EAExB,MAAMM,CAAC,GAAG,IAAIC,IAAI,CAACjB,EAAE,CAAC;EAEtB,OAAO;IACLkB,IAAI,EAAEF,CAAC,CAACG,cAAc,CAAC,CAAC;IACxBC,KAAK,EAAEJ,CAAC,CAACK,WAAW,CAAC,CAAC,GAAG,CAAC;IAC1BC,GAAG,EAAEN,CAAC,CAACO,UAAU,CAAC,CAAC;IACnBC,IAAI,EAAER,CAAC,CAACS,WAAW,CAAC,CAAC;IACrBC,MAAM,EAAEV,CAAC,CAACW,aAAa,CAAC,CAAC;IACzBC,MAAM,EAAEZ,CAAC,CAACa,aAAa,CAAC,CAAC;IACzBC,WAAW,EAAEd,CAAC,CAACe,kBAAkB,CAAC;EACpC,CAAC;AACH;;AAEA;AACA,SAASC,OAAOA,CAACC,GAAG,EAAEvB,MAAM,EAAEzB,IAAI,EAAE;EAClC,OAAOoB,SAAS,CAACpD,YAAY,CAACgF,GAAG,CAAC,EAAEvB,MAAM,EAAEzB,IAAI,CAAC;AACnD;;AAEA;AACA,SAASiD,UAAUA,CAACrC,IAAI,EAAEsC,GAAG,EAAE;EAC7B,MAAMC,IAAI,GAAGvC,IAAI,CAACI,CAAC;IACjBiB,IAAI,GAAGrB,IAAI,CAACP,CAAC,CAAC4B,IAAI,GAAGN,IAAI,CAACyB,KAAK,CAACF,GAAG,CAACG,KAAK,CAAC;IAC1ClB,KAAK,GAAGvB,IAAI,CAACP,CAAC,CAAC8B,KAAK,GAAGR,IAAI,CAACyB,KAAK,CAACF,GAAG,CAACI,MAAM,CAAC,GAAG3B,IAAI,CAACyB,KAAK,CAACF,GAAG,CAACK,QAAQ,CAAC,GAAG,CAAC;IAC5ElD,CAAC,GAAG;MACF,GAAGO,IAAI,CAACP,CAAC;MACT4B,IAAI;MACJE,KAAK;MACLE,GAAG,EACDV,IAAI,CAACC,GAAG,CAAChB,IAAI,CAACP,CAAC,CAACgC,GAAG,EAAE3E,WAAW,CAACuE,IAAI,EAAEE,KAAK,CAAC,CAAC,GAC9CR,IAAI,CAACyB,KAAK,CAACF,GAAG,CAACM,IAAI,CAAC,GACpB7B,IAAI,CAACyB,KAAK,CAACF,GAAG,CAACO,KAAK,CAAC,GAAG;IAC5B,CAAC;IACDC,WAAW,GAAG5G,QAAQ,CAAC6G,UAAU,CAAC;MAChCN,KAAK,EAAEH,GAAG,CAACG,KAAK,GAAG1B,IAAI,CAACyB,KAAK,CAACF,GAAG,CAACG,KAAK,CAAC;MACxCE,QAAQ,EAAEL,GAAG,CAACK,QAAQ,GAAG5B,IAAI,CAACyB,KAAK,CAACF,GAAG,CAACK,QAAQ,CAAC;MACjDD,MAAM,EAAEJ,GAAG,CAACI,MAAM,GAAG3B,IAAI,CAACyB,KAAK,CAACF,GAAG,CAACI,MAAM,CAAC;MAC3CG,KAAK,EAAEP,GAAG,CAACO,KAAK,GAAG9B,IAAI,CAACyB,KAAK,CAACF,GAAG,CAACO,KAAK,CAAC;MACxCD,IAAI,EAAEN,GAAG,CAACM,IAAI,GAAG7B,IAAI,CAACyB,KAAK,CAACF,GAAG,CAACM,IAAI,CAAC;MACrCI,KAAK,EAAEV,GAAG,CAACU,KAAK;MAChBC,OAAO,EAAEX,GAAG,CAACW,OAAO;MACpBC,OAAO,EAAEZ,GAAG,CAACY,OAAO;MACpBC,YAAY,EAAEb,GAAG,CAACa;IACpB,CAAC,CAAC,CAACC,EAAE,CAAC,cAAc,CAAC;IACrB3C,OAAO,GAAGrD,YAAY,CAACqC,CAAC,CAAC;EAE3B,IAAI,CAACU,EAAE,EAAEC,CAAC,CAAC,GAAGI,SAAS,CAACC,OAAO,EAAE8B,IAAI,EAAEvC,IAAI,CAACZ,IAAI,CAAC;EAEjD,IAAI0D,WAAW,KAAK,CAAC,EAAE;IACrB3C,EAAE,IAAI2C,WAAW;IACjB;IACA1C,CAAC,GAAGJ,IAAI,CAACZ,IAAI,CAACyB,MAAM,CAACV,EAAE,CAAC;EAC1B;EAEA,OAAO;IAAEA,EAAE;IAAEC;EAAE,CAAC;AAClB;;AAEA;AACA;AACA,SAASiD,mBAAmBA,CAACC,MAAM,EAAEC,UAAU,EAAEC,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAEC,cAAc,EAAE;EACnF,MAAM;IAAEC,OAAO;IAAExE;EAAK,CAAC,GAAGoE,IAAI;EAC9B,IAAKF,MAAM,IAAIO,MAAM,CAACC,IAAI,CAACR,MAAM,CAAC,CAACS,MAAM,KAAK,CAAC,IAAKR,UAAU,EAAE;IAC9D,MAAMS,kBAAkB,GAAGT,UAAU,IAAInE,IAAI;MAC3CY,IAAI,GAAGM,QAAQ,CAACyC,UAAU,CAACO,MAAM,EAAE;QACjC,GAAGE,IAAI;QACPpE,IAAI,EAAE4E,kBAAkB;QACxBL;MACF,CAAC,CAAC;IACJ,OAAOC,OAAO,GAAG5D,IAAI,GAAGA,IAAI,CAAC4D,OAAO,CAACxE,IAAI,CAAC;EAC5C,CAAC,MAAM;IACL,OAAOkB,QAAQ,CAACD,OAAO,CACrB,IAAIrB,OAAO,CAAC,YAAY,EAAG,cAAa0E,IAAK,wBAAuBD,MAAO,EAAC,CAC9E,CAAC;EACH;AACF;;AAEA;AACA;AACA,SAASQ,YAAYA,CAAC1E,EAAE,EAAEkE,MAAM,EAAES,MAAM,GAAG,IAAI,EAAE;EAC/C,OAAO3E,EAAE,CAAC4E,OAAO,GACb7H,SAAS,CAAC8H,MAAM,CAAC5H,MAAM,CAAC4H,MAAM,CAAC,OAAO,CAAC,EAAE;IACvCF,MAAM;IACNG,WAAW,EAAE;EACf,CAAC,CAAC,CAACC,wBAAwB,CAAC/E,EAAE,EAAEkE,MAAM,CAAC,GACvC,IAAI;AACV;AAEA,SAASc,SAASA,CAACnE,CAAC,EAAEoE,QAAQ,EAAE;EAC9B,MAAMC,UAAU,GAAGrE,CAAC,CAACX,CAAC,CAAC4B,IAAI,GAAG,IAAI,IAAIjB,CAAC,CAACX,CAAC,CAAC4B,IAAI,GAAG,CAAC;EAClD,IAAI5B,CAAC,GAAG,EAAE;EACV,IAAIgF,UAAU,IAAIrE,CAAC,CAACX,CAAC,CAAC4B,IAAI,IAAI,CAAC,EAAE5B,CAAC,IAAI,GAAG;EACzCA,CAAC,IAAIpC,QAAQ,CAAC+C,CAAC,CAACX,CAAC,CAAC4B,IAAI,EAAEoD,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC;EAE3C,IAAID,QAAQ,EAAE;IACZ/E,CAAC,IAAI,GAAG;IACRA,CAAC,IAAIpC,QAAQ,CAAC+C,CAAC,CAACX,CAAC,CAAC8B,KAAK,CAAC;IACxB9B,CAAC,IAAI,GAAG;IACRA,CAAC,IAAIpC,QAAQ,CAAC+C,CAAC,CAACX,CAAC,CAACgC,GAAG,CAAC;EACxB,CAAC,MAAM;IACLhC,CAAC,IAAIpC,QAAQ,CAAC+C,CAAC,CAACX,CAAC,CAAC8B,KAAK,CAAC;IACxB9B,CAAC,IAAIpC,QAAQ,CAAC+C,CAAC,CAACX,CAAC,CAACgC,GAAG,CAAC;EACxB;EACA,OAAOhC,CAAC;AACV;AAEA,SAASiF,SAASA,CAChBtE,CAAC,EACDoE,QAAQ,EACRG,eAAe,EACfC,oBAAoB,EACpBC,aAAa,EACbC,YAAY,EACZ;EACA,IAAIrF,CAAC,GAAGpC,QAAQ,CAAC+C,CAAC,CAACX,CAAC,CAACkC,IAAI,CAAC;EAC1B,IAAI6C,QAAQ,EAAE;IACZ/E,CAAC,IAAI,GAAG;IACRA,CAAC,IAAIpC,QAAQ,CAAC+C,CAAC,CAACX,CAAC,CAACoC,MAAM,CAAC;IACzB,IAAIzB,CAAC,CAACX,CAAC,CAACwC,WAAW,KAAK,CAAC,IAAI7B,CAAC,CAACX,CAAC,CAACsC,MAAM,KAAK,CAAC,IAAI,CAAC4C,eAAe,EAAE;MACjElF,CAAC,IAAI,GAAG;IACV;EACF,CAAC,MAAM;IACLA,CAAC,IAAIpC,QAAQ,CAAC+C,CAAC,CAACX,CAAC,CAACoC,MAAM,CAAC;EAC3B;EAEA,IAAIzB,CAAC,CAACX,CAAC,CAACwC,WAAW,KAAK,CAAC,IAAI7B,CAAC,CAACX,CAAC,CAACsC,MAAM,KAAK,CAAC,IAAI,CAAC4C,eAAe,EAAE;IACjElF,CAAC,IAAIpC,QAAQ,CAAC+C,CAAC,CAACX,CAAC,CAACsC,MAAM,CAAC;IAEzB,IAAI3B,CAAC,CAACX,CAAC,CAACwC,WAAW,KAAK,CAAC,IAAI,CAAC2C,oBAAoB,EAAE;MAClDnF,CAAC,IAAI,GAAG;MACRA,CAAC,IAAIpC,QAAQ,CAAC+C,CAAC,CAACX,CAAC,CAACwC,WAAW,EAAE,CAAC,CAAC;IACnC;EACF;EAEA,IAAI4C,aAAa,EAAE;IACjB,IAAIzE,CAAC,CAAC2E,aAAa,IAAI3E,CAAC,CAACS,MAAM,KAAK,CAAC,IAAI,CAACiE,YAAY,EAAE;MACtDrF,CAAC,IAAI,GAAG;IACV,CAAC,MAAM,IAAIW,CAAC,CAACA,CAAC,GAAG,CAAC,EAAE;MAClBX,CAAC,IAAI,GAAG;MACRA,CAAC,IAAIpC,QAAQ,CAAC0D,IAAI,CAACyB,KAAK,CAAC,CAACpC,CAAC,CAACA,CAAC,GAAG,EAAE,CAAC,CAAC;MACpCX,CAAC,IAAI,GAAG;MACRA,CAAC,IAAIpC,QAAQ,CAAC0D,IAAI,CAACyB,KAAK,CAAC,CAACpC,CAAC,CAACA,CAAC,GAAG,EAAE,CAAC,CAAC;IACtC,CAAC,MAAM;MACLX,CAAC,IAAI,GAAG;MACRA,CAAC,IAAIpC,QAAQ,CAAC0D,IAAI,CAACyB,KAAK,CAACpC,CAAC,CAACA,CAAC,GAAG,EAAE,CAAC,CAAC;MACnCX,CAAC,IAAI,GAAG;MACRA,CAAC,IAAIpC,QAAQ,CAAC0D,IAAI,CAACyB,KAAK,CAACpC,CAAC,CAACA,CAAC,GAAG,EAAE,CAAC,CAAC;IACrC;EACF;EAEA,IAAI0E,YAAY,EAAE;IAChBrF,CAAC,IAAI,GAAG,GAAGW,CAAC,CAAChB,IAAI,CAAC4F,QAAQ,GAAG,GAAG;EAClC;EACA,OAAOvF,CAAC;AACV;;AAEA;AACA,MAAMwF,iBAAiB,GAAG;IACtB1D,KAAK,EAAE,CAAC;IACRE,GAAG,EAAE,CAAC;IACNE,IAAI,EAAE,CAAC;IACPE,MAAM,EAAE,CAAC;IACTE,MAAM,EAAE,CAAC;IACTE,WAAW,EAAE;EACf,CAAC;EACDiD,qBAAqB,GAAG;IACtBC,UAAU,EAAE,CAAC;IACbC,OAAO,EAAE,CAAC;IACVzD,IAAI,EAAE,CAAC;IACPE,MAAM,EAAE,CAAC;IACTE,MAAM,EAAE,CAAC;IACTE,WAAW,EAAE;EACf,CAAC;EACDoD,wBAAwB,GAAG;IACzBC,OAAO,EAAE,CAAC;IACV3D,IAAI,EAAE,CAAC;IACPE,MAAM,EAAE,CAAC;IACTE,MAAM,EAAE,CAAC;IACTE,WAAW,EAAE;EACf,CAAC;;AAEH;AACA,MAAMsD,YAAY,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,aAAa,CAAC;EACtFC,gBAAgB,GAAG,CACjB,UAAU,EACV,YAAY,EACZ,SAAS,EACT,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,aAAa,CACd;EACDC,mBAAmB,GAAG,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,aAAa,CAAC;;AAEtF;AACA,SAASC,aAAaA,CAACC,IAAI,EAAE;EAC3B,MAAMC,UAAU,GAAG;IACjBvE,IAAI,EAAE,MAAM;IACZoB,KAAK,EAAE,MAAM;IACblB,KAAK,EAAE,OAAO;IACdmB,MAAM,EAAE,OAAO;IACfjB,GAAG,EAAE,KAAK;IACVmB,IAAI,EAAE,KAAK;IACXjB,IAAI,EAAE,MAAM;IACZqB,KAAK,EAAE,MAAM;IACbnB,MAAM,EAAE,QAAQ;IAChBoB,OAAO,EAAE,QAAQ;IACjB4C,OAAO,EAAE,SAAS;IAClBlD,QAAQ,EAAE,SAAS;IACnBZ,MAAM,EAAE,QAAQ;IAChBmB,OAAO,EAAE,QAAQ;IACjBjB,WAAW,EAAE,aAAa;IAC1BkB,YAAY,EAAE,aAAa;IAC3BiC,OAAO,EAAE,SAAS;IAClBU,QAAQ,EAAE,SAAS;IACnBC,UAAU,EAAE,YAAY;IACxBC,WAAW,EAAE,YAAY;IACzBC,WAAW,EAAE,YAAY;IACzBC,QAAQ,EAAE,UAAU;IACpBC,SAAS,EAAE,UAAU;IACrBb,OAAO,EAAE;EACX,CAAC,CAACK,IAAI,CAACS,WAAW,CAAC,CAAC,CAAC;EAErB,IAAI,CAACR,UAAU,EAAE,MAAM,IAAI9G,gBAAgB,CAAC6G,IAAI,CAAC;EAEjD,OAAOC,UAAU;AACnB;AAEA,SAASS,2BAA2BA,CAACV,IAAI,EAAE;EACzC,QAAQA,IAAI,CAACS,WAAW,CAAC,CAAC;IACxB,KAAK,cAAc;IACnB,KAAK,eAAe;MAClB,OAAO,cAAc;IACvB,KAAK,iBAAiB;IACtB,KAAK,kBAAkB;MACrB,OAAO,iBAAiB;IAC1B,KAAK,eAAe;IACpB,KAAK,gBAAgB;MACnB,OAAO,eAAe;IACxB;MACE,OAAOV,aAAa,CAACC,IAAI,CAAC;EAC9B;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASW,kBAAkBA,CAAClH,IAAI,EAAE;EAChC,IAAI,CAACmH,oBAAoB,CAACnH,IAAI,CAAC,EAAE;IAC/B,IAAIoH,YAAY,KAAKC,SAAS,EAAE;MAC9BD,YAAY,GAAGpK,QAAQ,CAACsK,GAAG,CAAC,CAAC;IAC/B;IAEAH,oBAAoB,CAACnH,IAAI,CAAC,GAAGA,IAAI,CAACyB,MAAM,CAAC2F,YAAY,CAAC;EACxD;EACA,OAAOD,oBAAoB,CAACnH,IAAI,CAAC;AACnC;;AAEA;AACA;AACA;AACA,SAASuH,OAAOA,CAACvE,GAAG,EAAEoB,IAAI,EAAE;EAC1B,MAAMpE,IAAI,GAAG9B,aAAa,CAACkG,IAAI,CAACpE,IAAI,EAAEhD,QAAQ,CAACwK,WAAW,CAAC;EAC3D,IAAI,CAACxH,IAAI,CAAC+E,OAAO,EAAE;IACjB,OAAO7D,QAAQ,CAACD,OAAO,CAAClB,eAAe,CAACC,IAAI,CAAC,CAAC;EAChD;EAEA,MAAMQ,GAAG,GAAGpD,MAAM,CAACuG,UAAU,CAACS,IAAI,CAAC;EAEnC,IAAIrD,EAAE,EAAEC,CAAC;;EAET;EACA,IAAI,CAAC3D,WAAW,CAAC2F,GAAG,CAACf,IAAI,CAAC,EAAE;IAC1B,KAAK,MAAMwF,CAAC,IAAItB,YAAY,EAAE;MAC5B,IAAI9I,WAAW,CAAC2F,GAAG,CAACyE,CAAC,CAAC,CAAC,EAAE;QACvBzE,GAAG,CAACyE,CAAC,CAAC,GAAG5B,iBAAiB,CAAC4B,CAAC,CAAC;MAC/B;IACF;IAEA,MAAMxG,OAAO,GAAGhC,uBAAuB,CAAC+D,GAAG,CAAC,IAAI5D,kBAAkB,CAAC4D,GAAG,CAAC;IACvE,IAAI/B,OAAO,EAAE;MACX,OAAOC,QAAQ,CAACD,OAAO,CAACA,OAAO,CAAC;IAClC;IAEA,MAAMyG,YAAY,GAAGR,kBAAkB,CAAClH,IAAI,CAAC;IAC7C,CAACe,EAAE,EAAEC,CAAC,CAAC,GAAG+B,OAAO,CAACC,GAAG,EAAE0E,YAAY,EAAE1H,IAAI,CAAC;EAC5C,CAAC,MAAM;IACLe,EAAE,GAAG/D,QAAQ,CAACsK,GAAG,CAAC,CAAC;EACrB;EAEA,OAAO,IAAIpG,QAAQ,CAAC;IAAEH,EAAE;IAAEf,IAAI;IAAEQ,GAAG;IAAEQ;EAAE,CAAC,CAAC;AAC3C;AAEA,SAAS2G,YAAYA,CAACC,KAAK,EAAEC,GAAG,EAAEzD,IAAI,EAAE;EACtC,MAAM0D,KAAK,GAAGzK,WAAW,CAAC+G,IAAI,CAAC0D,KAAK,CAAC,GAAG,IAAI,GAAG1D,IAAI,CAAC0D,KAAK;IACvDzD,MAAM,GAAGA,CAAChE,CAAC,EAAEkG,IAAI,KAAK;MACpBlG,CAAC,GAAGtC,OAAO,CAACsC,CAAC,EAAEyH,KAAK,IAAI1D,IAAI,CAAC2D,SAAS,GAAG,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC;MACrD,MAAMC,SAAS,GAAGH,GAAG,CAACrH,GAAG,CAACG,KAAK,CAACyD,IAAI,CAAC,CAAC6D,YAAY,CAAC7D,IAAI,CAAC;MACxD,OAAO4D,SAAS,CAAC3D,MAAM,CAAChE,CAAC,EAAEkG,IAAI,CAAC;IAClC,CAAC;IACD2B,MAAM,GAAI3B,IAAI,IAAK;MACjB,IAAInC,IAAI,CAAC2D,SAAS,EAAE;QAClB,IAAI,CAACF,GAAG,CAACM,OAAO,CAACP,KAAK,EAAErB,IAAI,CAAC,EAAE;UAC7B,OAAOsB,GAAG,CAACO,OAAO,CAAC7B,IAAI,CAAC,CAACpI,IAAI,CAACyJ,KAAK,CAACQ,OAAO,CAAC7B,IAAI,CAAC,EAAEA,IAAI,CAAC,CAAC8B,GAAG,CAAC9B,IAAI,CAAC;QACpE,CAAC,MAAM,OAAO,CAAC;MACjB,CAAC,MAAM;QACL,OAAOsB,GAAG,CAAC1J,IAAI,CAACyJ,KAAK,EAAErB,IAAI,CAAC,CAAC8B,GAAG,CAAC9B,IAAI,CAAC;MACxC;IACF,CAAC;EAEH,IAAInC,IAAI,CAACmC,IAAI,EAAE;IACb,OAAOlC,MAAM,CAAC6D,MAAM,CAAC9D,IAAI,CAACmC,IAAI,CAAC,EAAEnC,IAAI,CAACmC,IAAI,CAAC;EAC7C;EAEA,KAAK,MAAMA,IAAI,IAAInC,IAAI,CAACkE,KAAK,EAAE;IAC7B,MAAMC,KAAK,GAAGL,MAAM,CAAC3B,IAAI,CAAC;IAC1B,IAAI5E,IAAI,CAAC6G,GAAG,CAACD,KAAK,CAAC,IAAI,CAAC,EAAE;MACxB,OAAOlE,MAAM,CAACkE,KAAK,EAAEhC,IAAI,CAAC;IAC5B;EACF;EACA,OAAOlC,MAAM,CAACuD,KAAK,GAAGC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,EAAEzD,IAAI,CAACkE,KAAK,CAAClE,IAAI,CAACkE,KAAK,CAAC3D,MAAM,GAAG,CAAC,CAAC,CAAC;AACxE;AAEA,SAAS8D,QAAQA,CAACC,OAAO,EAAE;EACzB,IAAItE,IAAI,GAAG,CAAC,CAAC;IACXuE,IAAI;EACN,IAAID,OAAO,CAAC/D,MAAM,GAAG,CAAC,IAAI,OAAO+D,OAAO,CAACA,OAAO,CAAC/D,MAAM,GAAG,CAAC,CAAC,KAAK,QAAQ,EAAE;IACzEP,IAAI,GAAGsE,OAAO,CAACA,OAAO,CAAC/D,MAAM,GAAG,CAAC,CAAC;IAClCgE,IAAI,GAAGC,KAAK,CAACC,IAAI,CAACH,OAAO,CAAC,CAACI,KAAK,CAAC,CAAC,EAAEJ,OAAO,CAAC/D,MAAM,GAAG,CAAC,CAAC;EACzD,CAAC,MAAM;IACLgE,IAAI,GAAGC,KAAK,CAACC,IAAI,CAACH,OAAO,CAAC;EAC5B;EACA,OAAO,CAACtE,IAAI,EAAEuE,IAAI,CAAC;AACrB;;AAEA;AACA;AACA;AACA,IAAIvB,YAAY;AAChB;AACA;AACA;AACA;AACA;AACA;AACA,IAAID,oBAAoB,GAAG,CAAC,CAAC;;AAE7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,MAAMjG,QAAQ,CAAC;EAC5B;AACF;AACA;EACE6H,WAAWA,CAACC,MAAM,EAAE;IAClB,MAAMhJ,IAAI,GAAGgJ,MAAM,CAAChJ,IAAI,IAAIhD,QAAQ,CAACwK,WAAW;IAEhD,IAAIvG,OAAO,GACT+H,MAAM,CAAC/H,OAAO,KACbgI,MAAM,CAACC,KAAK,CAACF,MAAM,CAACjI,EAAE,CAAC,GAAG,IAAInB,OAAO,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC,KAC9D,CAACI,IAAI,CAAC+E,OAAO,GAAGhF,eAAe,CAACC,IAAI,CAAC,GAAG,IAAI,CAAC;IAChD;AACJ;AACA;IACI,IAAI,CAACe,EAAE,GAAG1D,WAAW,CAAC2L,MAAM,CAACjI,EAAE,CAAC,GAAG/D,QAAQ,CAACsK,GAAG,CAAC,CAAC,GAAG0B,MAAM,CAACjI,EAAE;IAE7D,IAAIV,CAAC,GAAG,IAAI;MACVW,CAAC,GAAG,IAAI;IACV,IAAI,CAACC,OAAO,EAAE;MACZ,MAAMkI,SAAS,GAAGH,MAAM,CAAC7H,GAAG,IAAI6H,MAAM,CAAC7H,GAAG,CAACJ,EAAE,KAAK,IAAI,CAACA,EAAE,IAAIiI,MAAM,CAAC7H,GAAG,CAACnB,IAAI,CAACoJ,MAAM,CAACpJ,IAAI,CAAC;MAEzF,IAAImJ,SAAS,EAAE;QACb,CAAC9I,CAAC,EAAEW,CAAC,CAAC,GAAG,CAACgI,MAAM,CAAC7H,GAAG,CAACd,CAAC,EAAE2I,MAAM,CAAC7H,GAAG,CAACH,CAAC,CAAC;MACvC,CAAC,MAAM;QACL;QACA;QACA,MAAMqI,EAAE,GAAG7L,QAAQ,CAACwL,MAAM,CAAChI,CAAC,CAAC,IAAI,CAACgI,MAAM,CAAC7H,GAAG,GAAG6H,MAAM,CAAChI,CAAC,GAAGhB,IAAI,CAACyB,MAAM,CAAC,IAAI,CAACV,EAAE,CAAC;QAC9EV,CAAC,GAAGyB,OAAO,CAAC,IAAI,CAACf,EAAE,EAAEsI,EAAE,CAAC;QACxBpI,OAAO,GAAGgI,MAAM,CAACC,KAAK,CAAC7I,CAAC,CAAC4B,IAAI,CAAC,GAAG,IAAIrC,OAAO,CAAC,eAAe,CAAC,GAAG,IAAI;QACpES,CAAC,GAAGY,OAAO,GAAG,IAAI,GAAGZ,CAAC;QACtBW,CAAC,GAAGC,OAAO,GAAG,IAAI,GAAGoI,EAAE;MACzB;IACF;;IAEA;AACJ;AACA;IACI,IAAI,CAACC,KAAK,GAAGtJ,IAAI;IACjB;AACJ;AACA;IACI,IAAI,CAACQ,GAAG,GAAGwI,MAAM,CAACxI,GAAG,IAAIpD,MAAM,CAAC4H,MAAM,CAAC,CAAC;IACxC;AACJ;AACA;IACI,IAAI,CAAC/D,OAAO,GAAGA,OAAO;IACtB;AACJ;AACA;IACI,IAAI,CAACb,QAAQ,GAAG,IAAI;IACpB;AACJ;AACA;IACI,IAAI,CAACG,aAAa,GAAG,IAAI;IACzB;AACJ;AACA;IACI,IAAI,CAACF,CAAC,GAAGA,CAAC;IACV;AACJ;AACA;IACI,IAAI,CAACW,CAAC,GAAGA,CAAC;IACV;AACJ;AACA;IACI,IAAI,CAACuI,eAAe,GAAG,IAAI;EAC7B;;EAEA;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,OAAOjC,GAAGA,CAAA,EAAG;IACX,OAAO,IAAIpG,QAAQ,CAAC,CAAC,CAAC,CAAC;EACzB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOsI,KAAKA,CAAA,EAAG;IACb,MAAM,CAACpF,IAAI,EAAEuE,IAAI,CAAC,GAAGF,QAAQ,CAACgB,SAAS,CAAC;MACtC,CAACxH,IAAI,EAAEE,KAAK,EAAEE,GAAG,EAAEE,IAAI,EAAEE,MAAM,EAAEE,MAAM,EAAEE,WAAW,CAAC,GAAG8F,IAAI;IAC9D,OAAOpB,OAAO,CAAC;MAAEtF,IAAI;MAAEE,KAAK;MAAEE,GAAG;MAAEE,IAAI;MAAEE,MAAM;MAAEE,MAAM;MAAEE;IAAY,CAAC,EAAEuB,IAAI,CAAC;EAC/E;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOsF,GAAGA,CAAA,EAAG;IACX,MAAM,CAACtF,IAAI,EAAEuE,IAAI,CAAC,GAAGF,QAAQ,CAACgB,SAAS,CAAC;MACtC,CAACxH,IAAI,EAAEE,KAAK,EAAEE,GAAG,EAAEE,IAAI,EAAEE,MAAM,EAAEE,MAAM,EAAEE,WAAW,CAAC,GAAG8F,IAAI;IAE9DvE,IAAI,CAACpE,IAAI,GAAG7C,eAAe,CAACwM,WAAW;IACvC,OAAOpC,OAAO,CAAC;MAAEtF,IAAI;MAAEE,KAAK;MAAEE,GAAG;MAAEE,IAAI;MAAEE,MAAM;MAAEE,MAAM;MAAEE;IAAY,CAAC,EAAEuB,IAAI,CAAC;EAC/E;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,OAAOwF,UAAUA,CAACC,IAAI,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACpC,MAAM/I,EAAE,GAAGxD,MAAM,CAACsM,IAAI,CAAC,GAAGA,IAAI,CAACE,OAAO,CAAC,CAAC,GAAGC,GAAG;IAC9C,IAAIf,MAAM,CAACC,KAAK,CAACnI,EAAE,CAAC,EAAE;MACpB,OAAOG,QAAQ,CAACD,OAAO,CAAC,eAAe,CAAC;IAC1C;IAEA,MAAMgJ,SAAS,GAAG/L,aAAa,CAAC4L,OAAO,CAAC9J,IAAI,EAAEhD,QAAQ,CAACwK,WAAW,CAAC;IACnE,IAAI,CAACyC,SAAS,CAAClF,OAAO,EAAE;MACtB,OAAO7D,QAAQ,CAACD,OAAO,CAAClB,eAAe,CAACkK,SAAS,CAAC,CAAC;IACrD;IAEA,OAAO,IAAI/I,QAAQ,CAAC;MAClBH,EAAE,EAAEA,EAAE;MACNf,IAAI,EAAEiK,SAAS;MACfzJ,GAAG,EAAEpD,MAAM,CAACuG,UAAU,CAACmG,OAAO;IAChC,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOI,UAAUA,CAACnG,YAAY,EAAE+F,OAAO,GAAG,CAAC,CAAC,EAAE;IAC5C,IAAI,CAACtM,QAAQ,CAACuG,YAAY,CAAC,EAAE;MAC3B,MAAM,IAAIvE,oBAAoB,CAC3B,yDAAwD,OAAOuE,YAAa,eAAcA,YAAa,EAC1G,CAAC;IACH,CAAC,MAAM,IAAIA,YAAY,GAAG,CAACjE,QAAQ,IAAIiE,YAAY,GAAGjE,QAAQ,EAAE;MAC9D;MACA,OAAOoB,QAAQ,CAACD,OAAO,CAAC,wBAAwB,CAAC;IACnD,CAAC,MAAM;MACL,OAAO,IAAIC,QAAQ,CAAC;QAClBH,EAAE,EAAEgD,YAAY;QAChB/D,IAAI,EAAE9B,aAAa,CAAC4L,OAAO,CAAC9J,IAAI,EAAEhD,QAAQ,CAACwK,WAAW,CAAC;QACvDhH,GAAG,EAAEpD,MAAM,CAACuG,UAAU,CAACmG,OAAO;MAChC,CAAC,CAAC;IACJ;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOK,WAAWA,CAACrG,OAAO,EAAEgG,OAAO,GAAG,CAAC,CAAC,EAAE;IACxC,IAAI,CAACtM,QAAQ,CAACsG,OAAO,CAAC,EAAE;MACtB,MAAM,IAAItE,oBAAoB,CAAC,wCAAwC,CAAC;IAC1E,CAAC,MAAM;MACL,OAAO,IAAI0B,QAAQ,CAAC;QAClBH,EAAE,EAAE+C,OAAO,GAAG,IAAI;QAClB9D,IAAI,EAAE9B,aAAa,CAAC4L,OAAO,CAAC9J,IAAI,EAAEhD,QAAQ,CAACwK,WAAW,CAAC;QACvDhH,GAAG,EAAEpD,MAAM,CAACuG,UAAU,CAACmG,OAAO;MAChC,CAAC,CAAC;IACJ;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOnG,UAAUA,CAACX,GAAG,EAAEoB,IAAI,GAAG,CAAC,CAAC,EAAE;IAChCpB,GAAG,GAAGA,GAAG,IAAI,CAAC,CAAC;IACf,MAAMiH,SAAS,GAAG/L,aAAa,CAACkG,IAAI,CAACpE,IAAI,EAAEhD,QAAQ,CAACwK,WAAW,CAAC;IAChE,IAAI,CAACyC,SAAS,CAAClF,OAAO,EAAE;MACtB,OAAO7D,QAAQ,CAACD,OAAO,CAAClB,eAAe,CAACkK,SAAS,CAAC,CAAC;IACrD;IAEA,MAAMzJ,GAAG,GAAGpD,MAAM,CAACuG,UAAU,CAACS,IAAI,CAAC;IACnC,MAAMoC,UAAU,GAAG1I,eAAe,CAACkF,GAAG,EAAEiE,2BAA2B,CAAC;IACpE,MAAM;MAAEmD,kBAAkB;MAAEC;IAAY,CAAC,GAAGhL,mBAAmB,CAACmH,UAAU,EAAEhG,GAAG,CAAC;IAEhF,MAAM8J,KAAK,GAAGtN,QAAQ,CAACsK,GAAG,CAAC,CAAC;MAC1BI,YAAY,GAAG,CAACrK,WAAW,CAAC+G,IAAI,CAACG,cAAc,CAAC,GAC5CH,IAAI,CAACG,cAAc,GACnB0F,SAAS,CAACxI,MAAM,CAAC6I,KAAK,CAAC;MAC3BC,eAAe,GAAG,CAAClN,WAAW,CAACmJ,UAAU,CAACN,OAAO,CAAC;MAClDsE,kBAAkB,GAAG,CAACnN,WAAW,CAACmJ,UAAU,CAACvE,IAAI,CAAC;MAClDwI,gBAAgB,GAAG,CAACpN,WAAW,CAACmJ,UAAU,CAACrE,KAAK,CAAC,IAAI,CAAC9E,WAAW,CAACmJ,UAAU,CAACnE,GAAG,CAAC;MACjFqI,cAAc,GAAGF,kBAAkB,IAAIC,gBAAgB;MACvDE,eAAe,GAAGnE,UAAU,CAACoE,QAAQ,IAAIpE,UAAU,CAACT,UAAU;;IAEhE;IACA;IACA;IACA;IACA;;IAEA,IAAI,CAAC2E,cAAc,IAAIH,eAAe,KAAKI,eAAe,EAAE;MAC1D,MAAM,IAAIlL,6BAA6B,CACrC,qEACF,CAAC;IACH;IAEA,IAAIgL,gBAAgB,IAAIF,eAAe,EAAE;MACvC,MAAM,IAAI9K,6BAA6B,CAAC,wCAAwC,CAAC;IACnF;IAEA,MAAMoL,WAAW,GAAGF,eAAe,IAAKnE,UAAU,CAACR,OAAO,IAAI,CAAC0E,cAAe;;IAE9E;IACA,IAAIpC,KAAK;MACPwC,aAAa;MACbC,MAAM,GAAGjJ,OAAO,CAACwI,KAAK,EAAE5C,YAAY,CAAC;IACvC,IAAImD,WAAW,EAAE;MACfvC,KAAK,GAAGlC,gBAAgB;MACxB0E,aAAa,GAAGhF,qBAAqB;MACrCiF,MAAM,GAAGlM,eAAe,CAACkM,MAAM,EAAEX,kBAAkB,EAAEC,WAAW,CAAC;IACnE,CAAC,MAAM,IAAIE,eAAe,EAAE;MAC1BjC,KAAK,GAAGjC,mBAAmB;MAC3ByE,aAAa,GAAG7E,wBAAwB;MACxC8E,MAAM,GAAGhM,kBAAkB,CAACgM,MAAM,CAAC;IACrC,CAAC,MAAM;MACLzC,KAAK,GAAGnC,YAAY;MACpB2E,aAAa,GAAGjF,iBAAiB;IACnC;;IAEA;IACA,IAAImF,UAAU,GAAG,KAAK;IACtB,KAAK,MAAMvD,CAAC,IAAIa,KAAK,EAAE;MACrB,MAAM2C,CAAC,GAAGzE,UAAU,CAACiB,CAAC,CAAC;MACvB,IAAI,CAACpK,WAAW,CAAC4N,CAAC,CAAC,EAAE;QACnBD,UAAU,GAAG,IAAI;MACnB,CAAC,MAAM,IAAIA,UAAU,EAAE;QACrBxE,UAAU,CAACiB,CAAC,CAAC,GAAGqD,aAAa,CAACrD,CAAC,CAAC;MAClC,CAAC,MAAM;QACLjB,UAAU,CAACiB,CAAC,CAAC,GAAGsD,MAAM,CAACtD,CAAC,CAAC;MAC3B;IACF;;IAEA;IACA,MAAMyD,kBAAkB,GAAGL,WAAW,GAChC3L,kBAAkB,CAACsH,UAAU,EAAE4D,kBAAkB,EAAEC,WAAW,CAAC,GAC/DE,eAAe,GACfpL,qBAAqB,CAACqH,UAAU,CAAC,GACjCvH,uBAAuB,CAACuH,UAAU,CAAC;MACvCvF,OAAO,GAAGiK,kBAAkB,IAAI9L,kBAAkB,CAACoH,UAAU,CAAC;IAEhE,IAAIvF,OAAO,EAAE;MACX,OAAOC,QAAQ,CAACD,OAAO,CAACA,OAAO,CAAC;IAClC;;IAEA;IACA,MAAMkK,SAAS,GAAGN,WAAW,GACvB/L,eAAe,CAAC0H,UAAU,EAAE4D,kBAAkB,EAAEC,WAAW,CAAC,GAC5DE,eAAe,GACfvL,kBAAkB,CAACwH,UAAU,CAAC,GAC9BA,UAAU;MACd,CAAC4E,OAAO,EAAEC,WAAW,CAAC,GAAGtI,OAAO,CAACoI,SAAS,EAAEzD,YAAY,EAAEuC,SAAS,CAAC;MACpErJ,IAAI,GAAG,IAAIM,QAAQ,CAAC;QAClBH,EAAE,EAAEqK,OAAO;QACXpL,IAAI,EAAEiK,SAAS;QACfjJ,CAAC,EAAEqK,WAAW;QACd7K;MACF,CAAC,CAAC;;IAEJ;IACA,IAAIgG,UAAU,CAACR,OAAO,IAAI0E,cAAc,IAAI1H,GAAG,CAACgD,OAAO,KAAKpF,IAAI,CAACoF,OAAO,EAAE;MACxE,OAAO9E,QAAQ,CAACD,OAAO,CACrB,oBAAoB,EACnB,uCAAsCuF,UAAU,CAACR,OAAQ,kBAAiBpF,IAAI,CAAC0K,KAAK,CAAC,CAAE,EAC1F,CAAC;IACH;IAEA,IAAI,CAAC1K,IAAI,CAACmE,OAAO,EAAE;MACjB,OAAO7D,QAAQ,CAACD,OAAO,CAACL,IAAI,CAACK,OAAO,CAAC;IACvC;IAEA,OAAOL,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAO2K,OAAOA,CAACjH,IAAI,EAAEF,IAAI,GAAG,CAAC,CAAC,EAAE;IAC9B,MAAM,CAACoH,IAAI,EAAErH,UAAU,CAAC,GAAG9F,YAAY,CAACiG,IAAI,CAAC;IAC7C,OAAOL,mBAAmB,CAACuH,IAAI,EAAErH,UAAU,EAAEC,IAAI,EAAE,UAAU,EAAEE,IAAI,CAAC;EACtE;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOmH,WAAWA,CAACnH,IAAI,EAAEF,IAAI,GAAG,CAAC,CAAC,EAAE;IAClC,MAAM,CAACoH,IAAI,EAAErH,UAAU,CAAC,GAAG/F,gBAAgB,CAACkG,IAAI,CAAC;IACjD,OAAOL,mBAAmB,CAACuH,IAAI,EAAErH,UAAU,EAAEC,IAAI,EAAE,UAAU,EAAEE,IAAI,CAAC;EACtE;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOoH,QAAQA,CAACpH,IAAI,EAAEF,IAAI,GAAG,CAAC,CAAC,EAAE;IAC/B,MAAM,CAACoH,IAAI,EAAErH,UAAU,CAAC,GAAG7F,aAAa,CAACgG,IAAI,CAAC;IAC9C,OAAOL,mBAAmB,CAACuH,IAAI,EAAErH,UAAU,EAAEC,IAAI,EAAE,MAAM,EAAEA,IAAI,CAAC;EAClE;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOuH,UAAUA,CAACrH,IAAI,EAAEsH,GAAG,EAAExH,IAAI,GAAG,CAAC,CAAC,EAAE;IACtC,IAAI/G,WAAW,CAACiH,IAAI,CAAC,IAAIjH,WAAW,CAACuO,GAAG,CAAC,EAAE;MACzC,MAAM,IAAIpM,oBAAoB,CAAC,kDAAkD,CAAC;IACpF;IAEA,MAAM;QAAEqM,MAAM,GAAG,IAAI;QAAEC,eAAe,GAAG;MAAK,CAAC,GAAG1H,IAAI;MACpD2H,WAAW,GAAG3O,MAAM,CAAC4O,QAAQ,CAAC;QAC5BH,MAAM;QACNC,eAAe;QACfG,WAAW,EAAE;MACf,CAAC,CAAC;MACF,CAACT,IAAI,EAAErH,UAAU,EAAEI,cAAc,EAAEtD,OAAO,CAAC,GAAGzC,eAAe,CAACuN,WAAW,EAAEzH,IAAI,EAAEsH,GAAG,CAAC;IACvF,IAAI3K,OAAO,EAAE;MACX,OAAOC,QAAQ,CAACD,OAAO,CAACA,OAAO,CAAC;IAClC,CAAC,MAAM;MACL,OAAOgD,mBAAmB,CAACuH,IAAI,EAAErH,UAAU,EAAEC,IAAI,EAAG,UAASwH,GAAI,EAAC,EAAEtH,IAAI,EAAEC,cAAc,CAAC;IAC3F;EACF;;EAEA;AACF;AACA;EACE,OAAO2H,UAAUA,CAAC5H,IAAI,EAAEsH,GAAG,EAAExH,IAAI,GAAG,CAAC,CAAC,EAAE;IACtC,OAAOlD,QAAQ,CAACyK,UAAU,CAACrH,IAAI,EAAEsH,GAAG,EAAExH,IAAI,CAAC;EAC7C;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAO+H,OAAOA,CAAC7H,IAAI,EAAEF,IAAI,GAAG,CAAC,CAAC,EAAE;IAC9B,MAAM,CAACoH,IAAI,EAAErH,UAAU,CAAC,GAAG5F,QAAQ,CAAC+F,IAAI,CAAC;IACzC,OAAOL,mBAAmB,CAACuH,IAAI,EAAErH,UAAU,EAAEC,IAAI,EAAE,KAAK,EAAEE,IAAI,CAAC;EACjE;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,OAAOrD,OAAOA,CAACmL,MAAM,EAAEC,WAAW,GAAG,IAAI,EAAE;IACzC,IAAI,CAACD,MAAM,EAAE;MACX,MAAM,IAAI5M,oBAAoB,CAAC,kDAAkD,CAAC;IACpF;IAEA,MAAMyB,OAAO,GAAGmL,MAAM,YAAYxM,OAAO,GAAGwM,MAAM,GAAG,IAAIxM,OAAO,CAACwM,MAAM,EAAEC,WAAW,CAAC;IAErF,IAAIrP,QAAQ,CAACsP,cAAc,EAAE;MAC3B,MAAM,IAAI3M,oBAAoB,CAACsB,OAAO,CAAC;IACzC,CAAC,MAAM;MACL,OAAO,IAAIC,QAAQ,CAAC;QAAED;MAAQ,CAAC,CAAC;IAClC;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAOsL,UAAUA,CAACvL,CAAC,EAAE;IACnB,OAAQA,CAAC,IAAIA,CAAC,CAACuI,eAAe,IAAK,KAAK;EAC1C;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,OAAOiD,kBAAkBA,CAACC,UAAU,EAAEC,UAAU,GAAG,CAAC,CAAC,EAAE;IACrD,MAAMC,SAAS,GAAGjO,kBAAkB,CAAC+N,UAAU,EAAErP,MAAM,CAACuG,UAAU,CAAC+I,UAAU,CAAC,CAAC;IAC/E,OAAO,CAACC,SAAS,GAAG,IAAI,GAAGA,SAAS,CAACC,GAAG,CAAEC,CAAC,IAAMA,CAAC,GAAGA,CAAC,CAACC,GAAG,GAAG,IAAK,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;EAC9E;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,OAAOC,YAAYA,CAACpB,GAAG,EAAEc,UAAU,GAAG,CAAC,CAAC,EAAE;IACxC,MAAMO,QAAQ,GAAGtO,iBAAiB,CAACzB,SAAS,CAACgQ,WAAW,CAACtB,GAAG,CAAC,EAAExO,MAAM,CAACuG,UAAU,CAAC+I,UAAU,CAAC,CAAC;IAC7F,OAAOO,QAAQ,CAACL,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACC,GAAG,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;EAC5C;EAEA,OAAOI,UAAUA,CAAA,EAAG;IAClB/F,YAAY,GAAGC,SAAS;IACxBF,oBAAoB,GAAG,CAAC,CAAC;EAC3B;;EAEA;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACEkB,GAAGA,CAAC9B,IAAI,EAAE;IACR,OAAO,IAAI,CAACA,IAAI,CAAC;EACnB;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAIxB,OAAOA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC9D,OAAO,KAAK,IAAI;EAC9B;;EAEA;AACF;AACA;AACA;EACE,IAAImM,aAAaA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACnM,OAAO,GAAG,IAAI,CAACA,OAAO,CAACmL,MAAM,GAAG,IAAI;EAClD;;EAEA;AACF;AACA;AACA;EACE,IAAIiB,kBAAkBA,CAAA,EAAG;IACvB,OAAO,IAAI,CAACpM,OAAO,GAAG,IAAI,CAACA,OAAO,CAACoL,WAAW,GAAG,IAAI;EACvD;;EAEA;AACF;AACA;AACA;AACA;EACE,IAAIR,MAAMA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC9G,OAAO,GAAG,IAAI,CAACvE,GAAG,CAACqL,MAAM,GAAG,IAAI;EAC9C;;EAEA;AACF;AACA;AACA;AACA;EACE,IAAIC,eAAeA,CAAA,EAAG;IACpB,OAAO,IAAI,CAAC/G,OAAO,GAAG,IAAI,CAACvE,GAAG,CAACsL,eAAe,GAAG,IAAI;EACvD;;EAEA;AACF;AACA;AACA;AACA;EACE,IAAIwB,cAAcA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACvI,OAAO,GAAG,IAAI,CAACvE,GAAG,CAAC8M,cAAc,GAAG,IAAI;EACtD;;EAEA;AACF;AACA;AACA;EACE,IAAItN,IAAIA,CAAA,EAAG;IACT,OAAO,IAAI,CAACsJ,KAAK;EACnB;;EAEA;AACF;AACA;AACA;EACE,IAAIiE,QAAQA,CAAA,EAAG;IACb,OAAO,IAAI,CAACxI,OAAO,GAAG,IAAI,CAAC/E,IAAI,CAACC,IAAI,GAAG,IAAI;EAC7C;;EAEA;AACF;AACA;AACA;AACA;EACE,IAAIgC,IAAIA,CAAA,EAAG;IACT,OAAO,IAAI,CAAC8C,OAAO,GAAG,IAAI,CAAC1E,CAAC,CAAC4B,IAAI,GAAG+H,GAAG;EACzC;;EAEA;AACF;AACA;AACA;AACA;EACE,IAAIvD,OAAOA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC1B,OAAO,GAAGpD,IAAI,CAAC6L,IAAI,CAAC,IAAI,CAACnN,CAAC,CAAC8B,KAAK,GAAG,CAAC,CAAC,GAAG6H,GAAG;EACzD;;EAEA;AACF;AACA;AACA;AACA;EACE,IAAI7H,KAAKA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC4C,OAAO,GAAG,IAAI,CAAC1E,CAAC,CAAC8B,KAAK,GAAG6H,GAAG;EAC1C;;EAEA;AACF;AACA;AACA;AACA;EACE,IAAI3H,GAAGA,CAAA,EAAG;IACR,OAAO,IAAI,CAAC0C,OAAO,GAAG,IAAI,CAAC1E,CAAC,CAACgC,GAAG,GAAG2H,GAAG;EACxC;;EAEA;AACF;AACA;AACA;AACA;EACE,IAAIzH,IAAIA,CAAA,EAAG;IACT,OAAO,IAAI,CAACwC,OAAO,GAAG,IAAI,CAAC1E,CAAC,CAACkC,IAAI,GAAGyH,GAAG;EACzC;;EAEA;AACF;AACA;AACA;AACA;EACE,IAAIvH,MAAMA,CAAA,EAAG;IACX,OAAO,IAAI,CAACsC,OAAO,GAAG,IAAI,CAAC1E,CAAC,CAACoC,MAAM,GAAGuH,GAAG;EAC3C;;EAEA;AACF;AACA;AACA;AACA;EACE,IAAIrH,MAAMA,CAAA,EAAG;IACX,OAAO,IAAI,CAACoC,OAAO,GAAG,IAAI,CAAC1E,CAAC,CAACsC,MAAM,GAAGqH,GAAG;EAC3C;;EAEA;AACF;AACA;AACA;AACA;EACE,IAAInH,WAAWA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACkC,OAAO,GAAG,IAAI,CAAC1E,CAAC,CAACwC,WAAW,GAAGmH,GAAG;EAChD;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAIY,QAAQA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC7F,OAAO,GAAG7E,sBAAsB,CAAC,IAAI,CAAC,CAAC0K,QAAQ,GAAGZ,GAAG;EACnE;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAIjE,UAAUA,CAAA,EAAG;IACf,OAAO,IAAI,CAAChB,OAAO,GAAG7E,sBAAsB,CAAC,IAAI,CAAC,CAAC6F,UAAU,GAAGiE,GAAG;EACrE;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,IAAIhE,OAAOA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACjB,OAAO,GAAG7E,sBAAsB,CAAC,IAAI,CAAC,CAAC8F,OAAO,GAAGgE,GAAG;EAClE;;EAEA;AACF;AACA;AACA;EACE,IAAIyD,SAASA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC1I,OAAO,IAAI,IAAI,CAACvE,GAAG,CAACkN,cAAc,CAAC,CAAC,CAACC,QAAQ,CAAC,IAAI,CAAC3H,OAAO,CAAC;EACzE;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAI4H,YAAYA,CAAA,EAAG;IACjB,OAAO,IAAI,CAAC7I,OAAO,GAAGzE,2BAA2B,CAAC,IAAI,CAAC,CAAC0F,OAAO,GAAGgE,GAAG;EACvE;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAI6D,eAAeA,CAAA,EAAG;IACpB,OAAO,IAAI,CAAC9I,OAAO,GAAGzE,2BAA2B,CAAC,IAAI,CAAC,CAACyF,UAAU,GAAGiE,GAAG;EAC1E;;EAEA;AACF;AACA;AACA;AACA;EACE,IAAI8D,aAAaA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAC/I,OAAO,GAAGzE,2BAA2B,CAAC,IAAI,CAAC,CAACsK,QAAQ,GAAGZ,GAAG;EACxE;;EAEA;AACF;AACA;AACA;AACA;EACE,IAAI9D,OAAOA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACnB,OAAO,GAAGhG,kBAAkB,CAAC,IAAI,CAACsB,CAAC,CAAC,CAAC6F,OAAO,GAAG8D,GAAG;EAChE;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAI+D,UAAUA,CAAA,EAAG;IACf,OAAO,IAAI,CAAChJ,OAAO,GAAG9H,IAAI,CAACqG,MAAM,CAAC,OAAO,EAAE;MAAE0K,MAAM,EAAE,IAAI,CAACxN;IAAI,CAAC,CAAC,CAAC,IAAI,CAAC2B,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI;EACzF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAI8L,SAASA,CAAA,EAAG;IACd,OAAO,IAAI,CAAClJ,OAAO,GAAG9H,IAAI,CAACqG,MAAM,CAAC,MAAM,EAAE;MAAE0K,MAAM,EAAE,IAAI,CAACxN;IAAI,CAAC,CAAC,CAAC,IAAI,CAAC2B,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI;EACxF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAI+L,YAAYA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACnJ,OAAO,GAAG9H,IAAI,CAACyJ,QAAQ,CAAC,OAAO,EAAE;MAAEsH,MAAM,EAAE,IAAI,CAACxN;IAAI,CAAC,CAAC,CAAC,IAAI,CAACwF,OAAO,GAAG,CAAC,CAAC,GAAG,IAAI;EAC7F;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAImI,WAAWA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACpJ,OAAO,GAAG9H,IAAI,CAACyJ,QAAQ,CAAC,MAAM,EAAE;MAAEsH,MAAM,EAAE,IAAI,CAACxN;IAAI,CAAC,CAAC,CAAC,IAAI,CAACwF,OAAO,GAAG,CAAC,CAAC,GAAG,IAAI;EAC5F;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAIvE,MAAMA,CAAA,EAAG;IACX,OAAO,IAAI,CAACsD,OAAO,GAAG,CAAC,IAAI,CAAC/D,CAAC,GAAGgJ,GAAG;EACrC;;EAEA;AACF;AACA;AACA;AACA;EACE,IAAIoE,eAAeA,CAAA,EAAG;IACpB,IAAI,IAAI,CAACrJ,OAAO,EAAE;MAChB,OAAO,IAAI,CAAC/E,IAAI,CAACqO,UAAU,CAAC,IAAI,CAACtN,EAAE,EAAE;QACnCsD,MAAM,EAAE,OAAO;QACfwH,MAAM,EAAE,IAAI,CAACA;MACf,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,OAAO,IAAI;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,IAAIyC,cAAcA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACvJ,OAAO,EAAE;MAChB,OAAO,IAAI,CAAC/E,IAAI,CAACqO,UAAU,CAAC,IAAI,CAACtN,EAAE,EAAE;QACnCsD,MAAM,EAAE,MAAM;QACdwH,MAAM,EAAE,IAAI,CAACA;MACf,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,OAAO,IAAI;IACb;EACF;;EAEA;AACF;AACA;AACA;EACE,IAAIlG,aAAaA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACZ,OAAO,GAAG,IAAI,CAAC/E,IAAI,CAACuO,WAAW,GAAG,IAAI;EACpD;;EAEA;AACF;AACA;AACA;EACE,IAAIC,OAAOA,CAAA,EAAG;IACZ,IAAI,IAAI,CAAC7I,aAAa,EAAE;MACtB,OAAO,KAAK;IACd,CAAC,MAAM;MACL,OACE,IAAI,CAAClE,MAAM,GAAG,IAAI,CAACgN,GAAG,CAAC;QAAEtM,KAAK,EAAE,CAAC;QAAEE,GAAG,EAAE;MAAE,CAAC,CAAC,CAACZ,MAAM,IACnD,IAAI,CAACA,MAAM,GAAG,IAAI,CAACgN,GAAG,CAAC;QAAEtM,KAAK,EAAE;MAAE,CAAC,CAAC,CAACV,MAAM;IAE/C;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACEiN,kBAAkBA,CAAA,EAAG;IACnB,IAAI,CAAC,IAAI,CAAC3J,OAAO,IAAI,IAAI,CAACY,aAAa,EAAE;MACvC,OAAO,CAAC,IAAI,CAAC;IACf;IACA,MAAMgJ,KAAK,GAAG,QAAQ;IACtB,MAAMC,QAAQ,GAAG,KAAK;IACtB,MAAMvN,OAAO,GAAGrD,YAAY,CAAC,IAAI,CAACqC,CAAC,CAAC;IACpC,MAAMwO,QAAQ,GAAG,IAAI,CAAC7O,IAAI,CAACyB,MAAM,CAACJ,OAAO,GAAGsN,KAAK,CAAC;IAClD,MAAMG,MAAM,GAAG,IAAI,CAAC9O,IAAI,CAACyB,MAAM,CAACJ,OAAO,GAAGsN,KAAK,CAAC;IAEhD,MAAMI,EAAE,GAAG,IAAI,CAAC/O,IAAI,CAACyB,MAAM,CAACJ,OAAO,GAAGwN,QAAQ,GAAGD,QAAQ,CAAC;IAC1D,MAAMpN,EAAE,GAAG,IAAI,CAACxB,IAAI,CAACyB,MAAM,CAACJ,OAAO,GAAGyN,MAAM,GAAGF,QAAQ,CAAC;IACxD,IAAIG,EAAE,KAAKvN,EAAE,EAAE;MACb,OAAO,CAAC,IAAI,CAAC;IACf;IACA,MAAMwN,GAAG,GAAG3N,OAAO,GAAG0N,EAAE,GAAGH,QAAQ;IACnC,MAAMK,GAAG,GAAG5N,OAAO,GAAGG,EAAE,GAAGoN,QAAQ;IACnC,MAAMM,EAAE,GAAGpN,OAAO,CAACkN,GAAG,EAAED,EAAE,CAAC;IAC3B,MAAMI,EAAE,GAAGrN,OAAO,CAACmN,GAAG,EAAEzN,EAAE,CAAC;IAC3B,IACE0N,EAAE,CAAC3M,IAAI,KAAK4M,EAAE,CAAC5M,IAAI,IACnB2M,EAAE,CAACzM,MAAM,KAAK0M,EAAE,CAAC1M,MAAM,IACvByM,EAAE,CAACvM,MAAM,KAAKwM,EAAE,CAACxM,MAAM,IACvBuM,EAAE,CAACrM,WAAW,KAAKsM,EAAE,CAACtM,WAAW,EACjC;MACA,OAAO,CAAClC,KAAK,CAAC,IAAI,EAAE;QAAEI,EAAE,EAAEiO;MAAI,CAAC,CAAC,EAAErO,KAAK,CAAC,IAAI,EAAE;QAAEI,EAAE,EAAEkO;MAAI,CAAC,CAAC,CAAC;IAC7D;IACA,OAAO,CAAC,IAAI,CAAC;EACf;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAIG,YAAYA,CAAA,EAAG;IACjB,OAAOxR,UAAU,CAAC,IAAI,CAACqE,IAAI,CAAC;EAC9B;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAIvE,WAAWA,CAAA,EAAG;IAChB,OAAOA,WAAW,CAAC,IAAI,CAACuE,IAAI,EAAE,IAAI,CAACE,KAAK,CAAC;EAC3C;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAIxE,UAAUA,CAAA,EAAG;IACf,OAAO,IAAI,CAACoH,OAAO,GAAGpH,UAAU,CAAC,IAAI,CAACsE,IAAI,CAAC,GAAG+H,GAAG;EACnD;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,IAAInM,eAAeA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACkH,OAAO,GAAGlH,eAAe,CAAC,IAAI,CAAC+M,QAAQ,CAAC,GAAGZ,GAAG;EAC5D;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAIqF,oBAAoBA,CAAA,EAAG;IACzB,OAAO,IAAI,CAACtK,OAAO,GACflH,eAAe,CACb,IAAI,CAACiQ,aAAa,EAClB,IAAI,CAACtN,GAAG,CAACC,qBAAqB,CAAC,CAAC,EAChC,IAAI,CAACD,GAAG,CAACE,cAAc,CAAC,CAC1B,CAAC,GACDsJ,GAAG;EACT;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEsF,qBAAqBA,CAAClL,IAAI,GAAG,CAAC,CAAC,EAAE;IAC/B,MAAM;MAAEyH,MAAM;MAAEC,eAAe;MAAEyD;IAAS,CAAC,GAAGrS,SAAS,CAAC8H,MAAM,CAC5D,IAAI,CAACxE,GAAG,CAACG,KAAK,CAACyD,IAAI,CAAC,EACpBA,IACF,CAAC,CAACoL,eAAe,CAAC,IAAI,CAAC;IACvB,OAAO;MAAE3D,MAAM;MAAEC,eAAe;MAAEwB,cAAc,EAAEiC;IAAS,CAAC;EAC9D;;EAEA;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEE,KAAKA,CAAChO,MAAM,GAAG,CAAC,EAAE2C,IAAI,GAAG,CAAC,CAAC,EAAE;IAC3B,OAAO,IAAI,CAACI,OAAO,CAACrH,eAAe,CAACuS,QAAQ,CAACjO,MAAM,CAAC,EAAE2C,IAAI,CAAC;EAC7D;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEuL,OAAOA,CAAA,EAAG;IACR,OAAO,IAAI,CAACnL,OAAO,CAACxH,QAAQ,CAACwK,WAAW,CAAC;EAC3C;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEhD,OAAOA,CAACxE,IAAI,EAAE;IAAE4P,aAAa,GAAG,KAAK;IAAEC,gBAAgB,GAAG;EAAM,CAAC,GAAG,CAAC,CAAC,EAAE;IACtE7P,IAAI,GAAG9B,aAAa,CAAC8B,IAAI,EAAEhD,QAAQ,CAACwK,WAAW,CAAC;IAChD,IAAIxH,IAAI,CAACoJ,MAAM,CAAC,IAAI,CAACpJ,IAAI,CAAC,EAAE;MAC1B,OAAO,IAAI;IACb,CAAC,MAAM,IAAI,CAACA,IAAI,CAAC+E,OAAO,EAAE;MACxB,OAAO7D,QAAQ,CAACD,OAAO,CAAClB,eAAe,CAACC,IAAI,CAAC,CAAC;IAChD,CAAC,MAAM;MACL,IAAI8P,KAAK,GAAG,IAAI,CAAC/O,EAAE;MACnB,IAAI6O,aAAa,IAAIC,gBAAgB,EAAE;QACrC,MAAME,WAAW,GAAG/P,IAAI,CAACyB,MAAM,CAAC,IAAI,CAACV,EAAE,CAAC;QACxC,MAAMiP,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;QAC7B,CAACH,KAAK,CAAC,GAAG/M,OAAO,CAACiN,KAAK,EAAED,WAAW,EAAE/P,IAAI,CAAC;MAC7C;MACA,OAAOW,KAAK,CAAC,IAAI,EAAE;QAAEI,EAAE,EAAE+O,KAAK;QAAE9P;MAAK,CAAC,CAAC;IACzC;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEkQ,WAAWA,CAAC;IAAErE,MAAM;IAAEC,eAAe;IAAEwB;EAAe,CAAC,GAAG,CAAC,CAAC,EAAE;IAC5D,MAAM9M,GAAG,GAAG,IAAI,CAACA,GAAG,CAACG,KAAK,CAAC;MAAEkL,MAAM;MAAEC,eAAe;MAAEwB;IAAe,CAAC,CAAC;IACvE,OAAO3M,KAAK,CAAC,IAAI,EAAE;MAAEH;IAAI,CAAC,CAAC;EAC7B;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE2P,SAASA,CAACtE,MAAM,EAAE;IAChB,OAAO,IAAI,CAACqE,WAAW,CAAC;MAAErE;IAAO,CAAC,CAAC;EACrC;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE4C,GAAGA,CAAC2B,MAAM,EAAE;IACV,IAAI,CAAC,IAAI,CAACrL,OAAO,EAAE,OAAO,IAAI;IAE9B,MAAMyB,UAAU,GAAG1I,eAAe,CAACsS,MAAM,EAAEnJ,2BAA2B,CAAC;IACvE,MAAM;MAAEmD,kBAAkB;MAAEC;IAAY,CAAC,GAAGhL,mBAAmB,CAACmH,UAAU,EAAE,IAAI,CAAChG,GAAG,CAAC;IAErF,MAAM6P,gBAAgB,GAClB,CAAChT,WAAW,CAACmJ,UAAU,CAACoE,QAAQ,CAAC,IACjC,CAACvN,WAAW,CAACmJ,UAAU,CAACT,UAAU,CAAC,IACnC,CAAC1I,WAAW,CAACmJ,UAAU,CAACR,OAAO,CAAC;MAClCuE,eAAe,GAAG,CAAClN,WAAW,CAACmJ,UAAU,CAACN,OAAO,CAAC;MAClDsE,kBAAkB,GAAG,CAACnN,WAAW,CAACmJ,UAAU,CAACvE,IAAI,CAAC;MAClDwI,gBAAgB,GAAG,CAACpN,WAAW,CAACmJ,UAAU,CAACrE,KAAK,CAAC,IAAI,CAAC9E,WAAW,CAACmJ,UAAU,CAACnE,GAAG,CAAC;MACjFqI,cAAc,GAAGF,kBAAkB,IAAIC,gBAAgB;MACvDE,eAAe,GAAGnE,UAAU,CAACoE,QAAQ,IAAIpE,UAAU,CAACT,UAAU;IAEhE,IAAI,CAAC2E,cAAc,IAAIH,eAAe,KAAKI,eAAe,EAAE;MAC1D,MAAM,IAAIlL,6BAA6B,CACrC,qEACF,CAAC;IACH;IAEA,IAAIgL,gBAAgB,IAAIF,eAAe,EAAE;MACvC,MAAM,IAAI9K,6BAA6B,CAAC,wCAAwC,CAAC;IACnF;IAEA,IAAI6Q,KAAK;IACT,IAAID,gBAAgB,EAAE;MACpBC,KAAK,GAAGxR,eAAe,CACrB;QAAE,GAAGD,eAAe,CAAC,IAAI,CAACwB,CAAC,EAAE+J,kBAAkB,EAAEC,WAAW,CAAC;QAAE,GAAG7D;MAAW,CAAC,EAC9E4D,kBAAkB,EAClBC,WACF,CAAC;IACH,CAAC,MAAM,IAAI,CAAChN,WAAW,CAACmJ,UAAU,CAACN,OAAO,CAAC,EAAE;MAC3CoK,KAAK,GAAGtR,kBAAkB,CAAC;QAAE,GAAGD,kBAAkB,CAAC,IAAI,CAACsB,CAAC,CAAC;QAAE,GAAGmG;MAAW,CAAC,CAAC;IAC9E,CAAC,MAAM;MACL8J,KAAK,GAAG;QAAE,GAAG,IAAI,CAACL,QAAQ,CAAC,CAAC;QAAE,GAAGzJ;MAAW,CAAC;;MAE7C;MACA;MACA,IAAInJ,WAAW,CAACmJ,UAAU,CAACnE,GAAG,CAAC,EAAE;QAC/BiO,KAAK,CAACjO,GAAG,GAAGV,IAAI,CAACC,GAAG,CAAClE,WAAW,CAAC4S,KAAK,CAACrO,IAAI,EAAEqO,KAAK,CAACnO,KAAK,CAAC,EAAEmO,KAAK,CAACjO,GAAG,CAAC;MACvE;IACF;IAEA,MAAM,CAACtB,EAAE,EAAEC,CAAC,CAAC,GAAG+B,OAAO,CAACuN,KAAK,EAAE,IAAI,CAACtP,CAAC,EAAE,IAAI,CAAChB,IAAI,CAAC;IACjD,OAAOW,KAAK,CAAC,IAAI,EAAE;MAAEI,EAAE;MAAEC;IAAE,CAAC,CAAC;EAC/B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEuP,IAAIA,CAACC,QAAQ,EAAE;IACb,IAAI,CAAC,IAAI,CAACzL,OAAO,EAAE,OAAO,IAAI;IAC9B,MAAM7B,GAAG,GAAGpG,QAAQ,CAAC2T,gBAAgB,CAACD,QAAQ,CAAC;IAC/C,OAAO7P,KAAK,CAAC,IAAI,EAAEsC,UAAU,CAAC,IAAI,EAAEC,GAAG,CAAC,CAAC;EAC3C;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEwN,KAAKA,CAACF,QAAQ,EAAE;IACd,IAAI,CAAC,IAAI,CAACzL,OAAO,EAAE,OAAO,IAAI;IAC9B,MAAM7B,GAAG,GAAGpG,QAAQ,CAAC2T,gBAAgB,CAACD,QAAQ,CAAC,CAACG,MAAM,CAAC,CAAC;IACxD,OAAOhQ,KAAK,CAAC,IAAI,EAAEsC,UAAU,CAAC,IAAI,EAAEC,GAAG,CAAC,CAAC;EAC3C;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEkF,OAAOA,CAAC7B,IAAI,EAAE;IAAEqK,cAAc,GAAG;EAAM,CAAC,GAAG,CAAC,CAAC,EAAE;IAC7C,IAAI,CAAC,IAAI,CAAC7L,OAAO,EAAE,OAAO,IAAI;IAE9B,MAAM/D,CAAC,GAAG,CAAC,CAAC;MACV6P,cAAc,GAAG/T,QAAQ,CAACwJ,aAAa,CAACC,IAAI,CAAC;IAC/C,QAAQsK,cAAc;MACpB,KAAK,OAAO;QACV7P,CAAC,CAACmB,KAAK,GAAG,CAAC;MACb;MACA,KAAK,UAAU;MACf,KAAK,QAAQ;QACXnB,CAAC,CAACqB,GAAG,GAAG,CAAC;MACX;MACA,KAAK,OAAO;MACZ,KAAK,MAAM;QACTrB,CAAC,CAACuB,IAAI,GAAG,CAAC;MACZ;MACA,KAAK,OAAO;QACVvB,CAAC,CAACyB,MAAM,GAAG,CAAC;MACd;MACA,KAAK,SAAS;QACZzB,CAAC,CAAC2B,MAAM,GAAG,CAAC;MACd;MACA,KAAK,SAAS;QACZ3B,CAAC,CAAC6B,WAAW,GAAG,CAAC;QACjB;MACF,KAAK,cAAc;QACjB;MACF;IACF;IAEA,IAAIgO,cAAc,KAAK,OAAO,EAAE;MAC9B,IAAID,cAAc,EAAE;QAClB,MAAMvG,WAAW,GAAG,IAAI,CAAC7J,GAAG,CAACE,cAAc,CAAC,CAAC;QAC7C,MAAM;UAAEsF;QAAQ,CAAC,GAAG,IAAI;QACxB,IAAIA,OAAO,GAAGqE,WAAW,EAAE;UACzBrJ,CAAC,CAAC+E,UAAU,GAAG,IAAI,CAACA,UAAU,GAAG,CAAC;QACpC;QACA/E,CAAC,CAACgF,OAAO,GAAGqE,WAAW;MACzB,CAAC,MAAM;QACLrJ,CAAC,CAACgF,OAAO,GAAG,CAAC;MACf;IACF;IAEA,IAAI6K,cAAc,KAAK,UAAU,EAAE;MACjC,MAAMC,CAAC,GAAGnP,IAAI,CAAC6L,IAAI,CAAC,IAAI,CAACrL,KAAK,GAAG,CAAC,CAAC;MACnCnB,CAAC,CAACmB,KAAK,GAAG,CAAC2O,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC;IAC3B;IAEA,OAAO,IAAI,CAACrC,GAAG,CAACzN,CAAC,CAAC;EACpB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE+P,KAAKA,CAACxK,IAAI,EAAEnC,IAAI,EAAE;IAChB,OAAO,IAAI,CAACW,OAAO,GACf,IAAI,CAACwL,IAAI,CAAC;MAAE,CAAChK,IAAI,GAAG;IAAE,CAAC,CAAC,CACrB6B,OAAO,CAAC7B,IAAI,EAAEnC,IAAI,CAAC,CACnBsM,KAAK,CAAC,CAAC,CAAC,GACX,IAAI;EACV;;EAEA;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEM,QAAQA,CAACpF,GAAG,EAAExH,IAAI,GAAG,CAAC,CAAC,EAAE;IACvB,OAAO,IAAI,CAACW,OAAO,GACf7H,SAAS,CAAC8H,MAAM,CAAC,IAAI,CAACxE,GAAG,CAACyQ,aAAa,CAAC7M,IAAI,CAAC,CAAC,CAACc,wBAAwB,CAAC,IAAI,EAAE0G,GAAG,CAAC,GAClF/L,OAAO;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEqR,cAAcA,CAACzE,UAAU,GAAGlN,OAAO,CAAC4R,UAAU,EAAE/M,IAAI,GAAG,CAAC,CAAC,EAAE;IACzD,OAAO,IAAI,CAACW,OAAO,GACf7H,SAAS,CAAC8H,MAAM,CAAC,IAAI,CAACxE,GAAG,CAACG,KAAK,CAACyD,IAAI,CAAC,EAAEqI,UAAU,CAAC,CAAC2E,cAAc,CAAC,IAAI,CAAC,GACvEvR,OAAO;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEwR,aAAaA,CAACjN,IAAI,GAAG,CAAC,CAAC,EAAE;IACvB,OAAO,IAAI,CAACW,OAAO,GACf7H,SAAS,CAAC8H,MAAM,CAAC,IAAI,CAACxE,GAAG,CAACG,KAAK,CAACyD,IAAI,CAAC,EAAEA,IAAI,CAAC,CAACkN,mBAAmB,CAAC,IAAI,CAAC,GACtE,EAAE;EACR;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEhG,KAAKA,CAAC;IACJjH,MAAM,GAAG,UAAU;IACnBkB,eAAe,GAAG,KAAK;IACvBC,oBAAoB,GAAG,KAAK;IAC5BC,aAAa,GAAG,IAAI;IACpBC,YAAY,GAAG;EACjB,CAAC,GAAG,CAAC,CAAC,EAAE;IACN,IAAI,CAAC,IAAI,CAACX,OAAO,EAAE;MACjB,OAAO,IAAI;IACb;IAEA,MAAMwM,GAAG,GAAGlN,MAAM,KAAK,UAAU;IAEjC,IAAIhE,CAAC,GAAG8E,SAAS,CAAC,IAAI,EAAEoM,GAAG,CAAC;IAC5BlR,CAAC,IAAI,GAAG;IACRA,CAAC,IAAIiF,SAAS,CAAC,IAAI,EAAEiM,GAAG,EAAEhM,eAAe,EAAEC,oBAAoB,EAAEC,aAAa,EAAEC,YAAY,CAAC;IAC7F,OAAOrF,CAAC;EACV;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE8E,SAASA,CAAC;IAAEd,MAAM,GAAG;EAAW,CAAC,GAAG,CAAC,CAAC,EAAE;IACtC,IAAI,CAAC,IAAI,CAACU,OAAO,EAAE;MACjB,OAAO,IAAI;IACb;IAEA,OAAOI,SAAS,CAAC,IAAI,EAAEd,MAAM,KAAK,UAAU,CAAC;EAC/C;;EAEA;AACF;AACA;AACA;AACA;EACEmN,aAAaA,CAAA,EAAG;IACd,OAAO3M,YAAY,CAAC,IAAI,EAAE,cAAc,CAAC;EAC3C;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACES,SAASA,CAAC;IACRE,oBAAoB,GAAG,KAAK;IAC5BD,eAAe,GAAG,KAAK;IACvBE,aAAa,GAAG,IAAI;IACpBgM,aAAa,GAAG,KAAK;IACrB/L,YAAY,GAAG,KAAK;IACpBrB,MAAM,GAAG;EACX,CAAC,GAAG,CAAC,CAAC,EAAE;IACN,IAAI,CAAC,IAAI,CAACU,OAAO,EAAE;MACjB,OAAO,IAAI;IACb;IAEA,IAAI1E,CAAC,GAAGoR,aAAa,GAAG,GAAG,GAAG,EAAE;IAChC,OACEpR,CAAC,GACDiF,SAAS,CACP,IAAI,EACJjB,MAAM,KAAK,UAAU,EACrBkB,eAAe,EACfC,oBAAoB,EACpBC,aAAa,EACbC,YACF,CAAC;EAEL;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEgM,SAASA,CAAA,EAAG;IACV,OAAO7M,YAAY,CAAC,IAAI,EAAE,+BAA+B,EAAE,KAAK,CAAC;EACnE;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE8M,MAAMA,CAAA,EAAG;IACP,OAAO9M,YAAY,CAAC,IAAI,CAAC4K,KAAK,CAAC,CAAC,EAAE,iCAAiC,CAAC;EACtE;;EAEA;AACF;AACA;AACA;AACA;EACEmC,SAASA,CAAA,EAAG;IACV,IAAI,CAAC,IAAI,CAAC7M,OAAO,EAAE;MACjB,OAAO,IAAI;IACb;IACA,OAAOI,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC;EAC9B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE0M,SAASA,CAAC;IAAEpM,aAAa,GAAG,IAAI;IAAEqM,WAAW,GAAG,KAAK;IAAEC,kBAAkB,GAAG;EAAK,CAAC,GAAG,CAAC,CAAC,EAAE;IACvF,IAAInG,GAAG,GAAG,cAAc;IAExB,IAAIkG,WAAW,IAAIrM,aAAa,EAAE;MAChC,IAAIsM,kBAAkB,EAAE;QACtBnG,GAAG,IAAI,GAAG;MACZ;MACA,IAAIkG,WAAW,EAAE;QACflG,GAAG,IAAI,GAAG;MACZ,CAAC,MAAM,IAAInG,aAAa,EAAE;QACxBmG,GAAG,IAAI,IAAI;MACb;IACF;IAEA,OAAO/G,YAAY,CAAC,IAAI,EAAE+G,GAAG,EAAE,IAAI,CAAC;EACtC;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEoG,KAAKA,CAAC5N,IAAI,GAAG,CAAC,CAAC,EAAE;IACf,IAAI,CAAC,IAAI,CAACW,OAAO,EAAE;MACjB,OAAO,IAAI;IACb;IAEA,OAAQ,GAAE,IAAI,CAAC6M,SAAS,CAAC,CAAE,IAAG,IAAI,CAACC,SAAS,CAACzN,IAAI,CAAE,EAAC;EACtD;;EAEA;AACF;AACA;AACA;EACE6N,QAAQA,CAAA,EAAG;IACT,OAAO,IAAI,CAAClN,OAAO,GAAG,IAAI,CAACuG,KAAK,CAAC,CAAC,GAAGzL,OAAO;EAC9C;;EAEA;AACF;AACA;AACA;EACE,CAACqS,MAAM,CAACC,GAAG,CAAC,4BAA4B,CAAC,IAAI;IAC3C,IAAI,IAAI,CAACpN,OAAO,EAAE;MAChB,OAAQ,kBAAiB,IAAI,CAACuG,KAAK,CAAC,CAAE,WAAU,IAAI,CAACtL,IAAI,CAACC,IAAK,aAAY,IAAI,CAAC4L,MAAO,IAAG;IAC5F,CAAC,MAAM;MACL,OAAQ,+BAA8B,IAAI,CAACuB,aAAc,IAAG;IAC9D;EACF;;EAEA;AACF;AACA;AACA;EACErD,OAAOA,CAAA,EAAG;IACR,OAAO,IAAI,CAACqI,QAAQ,CAAC,CAAC;EACxB;;EAEA;AACF;AACA;AACA;EACEA,QAAQA,CAAA,EAAG;IACT,OAAO,IAAI,CAACrN,OAAO,GAAG,IAAI,CAAChE,EAAE,GAAGiJ,GAAG;EACrC;;EAEA;AACF;AACA;AACA;EACEqI,SAASA,CAAA,EAAG;IACV,OAAO,IAAI,CAACtN,OAAO,GAAG,IAAI,CAAChE,EAAE,GAAG,IAAI,GAAGiJ,GAAG;EAC5C;;EAEA;AACF;AACA;AACA;EACEsI,aAAaA,CAAA,EAAG;IACd,OAAO,IAAI,CAACvN,OAAO,GAAGpD,IAAI,CAAC4Q,KAAK,CAAC,IAAI,CAACxR,EAAE,GAAG,IAAI,CAAC,GAAGiJ,GAAG;EACxD;;EAEA;AACF;AACA;AACA;EACEwI,MAAMA,CAAA,EAAG;IACP,OAAO,IAAI,CAAClH,KAAK,CAAC,CAAC;EACrB;;EAEA;AACF;AACA;AACA;EACEmH,MAAMA,CAAA,EAAG;IACP,OAAO,IAAI,CAACC,QAAQ,CAAC,CAAC;EACxB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACEzC,QAAQA,CAAC7L,IAAI,GAAG,CAAC,CAAC,EAAE;IAClB,IAAI,CAAC,IAAI,CAACW,OAAO,EAAE,OAAO,CAAC,CAAC;IAE5B,MAAM4N,IAAI,GAAG;MAAE,GAAG,IAAI,CAACtS;IAAE,CAAC;IAE1B,IAAI+D,IAAI,CAACwO,aAAa,EAAE;MACtBD,IAAI,CAACrF,cAAc,GAAG,IAAI,CAACA,cAAc;MACzCqF,IAAI,CAAC7G,eAAe,GAAG,IAAI,CAACtL,GAAG,CAACsL,eAAe;MAC/C6G,IAAI,CAAC9G,MAAM,GAAG,IAAI,CAACrL,GAAG,CAACqL,MAAM;IAC/B;IACA,OAAO8G,IAAI;EACb;;EAEA;AACF;AACA;AACA;EACED,QAAQA,CAAA,EAAG;IACT,OAAO,IAAI1Q,IAAI,CAAC,IAAI,CAAC+C,OAAO,GAAG,IAAI,CAAChE,EAAE,GAAGiJ,GAAG,CAAC;EAC/C;;EAEA;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE7L,IAAIA,CAAC0U,aAAa,EAAEtM,IAAI,GAAG,cAAc,EAAEnC,IAAI,GAAG,CAAC,CAAC,EAAE;IACpD,IAAI,CAAC,IAAI,CAACW,OAAO,IAAI,CAAC8N,aAAa,CAAC9N,OAAO,EAAE;MAC3C,OAAOjI,QAAQ,CAACmE,OAAO,CAAC,wCAAwC,CAAC;IACnE;IAEA,MAAM6R,OAAO,GAAG;MAAEjH,MAAM,EAAE,IAAI,CAACA,MAAM;MAAEC,eAAe,EAAE,IAAI,CAACA,eAAe;MAAE,GAAG1H;IAAK,CAAC;IAEvF,MAAMkE,KAAK,GAAGhL,UAAU,CAACiJ,IAAI,CAAC,CAACqG,GAAG,CAAC9P,QAAQ,CAACwJ,aAAa,CAAC;MACxDyM,YAAY,GAAGF,aAAa,CAAC9I,OAAO,CAAC,CAAC,GAAG,IAAI,CAACA,OAAO,CAAC,CAAC;MACvDiJ,OAAO,GAAGD,YAAY,GAAG,IAAI,GAAGF,aAAa;MAC7CI,KAAK,GAAGF,YAAY,GAAGF,aAAa,GAAG,IAAI;MAC3CK,MAAM,GAAG/U,IAAI,CAAC6U,OAAO,EAAEC,KAAK,EAAE3K,KAAK,EAAEwK,OAAO,CAAC;IAE/C,OAAOC,YAAY,GAAGG,MAAM,CAACvC,MAAM,CAAC,CAAC,GAAGuC,MAAM;EAChD;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,OAAOA,CAAC5M,IAAI,GAAG,cAAc,EAAEnC,IAAI,GAAG,CAAC,CAAC,EAAE;IACxC,OAAO,IAAI,CAACjG,IAAI,CAAC+C,QAAQ,CAACoG,GAAG,CAAC,CAAC,EAAEf,IAAI,EAAEnC,IAAI,CAAC;EAC9C;;EAEA;AACF;AACA;AACA;AACA;EACEgP,KAAKA,CAACP,aAAa,EAAE;IACnB,OAAO,IAAI,CAAC9N,OAAO,GAAGhI,QAAQ,CAACsW,aAAa,CAAC,IAAI,EAAER,aAAa,CAAC,GAAG,IAAI;EAC1E;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE1K,OAAOA,CAAC0K,aAAa,EAAEtM,IAAI,EAAEnC,IAAI,EAAE;IACjC,IAAI,CAAC,IAAI,CAACW,OAAO,EAAE,OAAO,KAAK;IAE/B,MAAMuO,OAAO,GAAGT,aAAa,CAAC9I,OAAO,CAAC,CAAC;IACvC,MAAMwJ,cAAc,GAAG,IAAI,CAAC/O,OAAO,CAACqO,aAAa,CAAC7S,IAAI,EAAE;MAAE4P,aAAa,EAAE;IAAK,CAAC,CAAC;IAChF,OACE2D,cAAc,CAACnL,OAAO,CAAC7B,IAAI,EAAEnC,IAAI,CAAC,IAAIkP,OAAO,IAAIA,OAAO,IAAIC,cAAc,CAACxC,KAAK,CAACxK,IAAI,EAAEnC,IAAI,CAAC;EAEhG;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACEgF,MAAMA,CAACoK,KAAK,EAAE;IACZ,OACE,IAAI,CAACzO,OAAO,IACZyO,KAAK,CAACzO,OAAO,IACb,IAAI,CAACgF,OAAO,CAAC,CAAC,KAAKyJ,KAAK,CAACzJ,OAAO,CAAC,CAAC,IAClC,IAAI,CAAC/J,IAAI,CAACoJ,MAAM,CAACoK,KAAK,CAACxT,IAAI,CAAC,IAC5B,IAAI,CAACQ,GAAG,CAAC4I,MAAM,CAACoK,KAAK,CAAChT,GAAG,CAAC;EAE9B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEiT,UAAUA,CAAC3J,OAAO,GAAG,CAAC,CAAC,EAAE;IACvB,IAAI,CAAC,IAAI,CAAC/E,OAAO,EAAE,OAAO,IAAI;IAC9B,MAAM4N,IAAI,GAAG7I,OAAO,CAAC6I,IAAI,IAAIzR,QAAQ,CAACyC,UAAU,CAAC,CAAC,CAAC,EAAE;QAAE3D,IAAI,EAAE,IAAI,CAACA;MAAK,CAAC,CAAC;MACvE0T,OAAO,GAAG5J,OAAO,CAAC4J,OAAO,GAAI,IAAI,GAAGf,IAAI,GAAG,CAAC7I,OAAO,CAAC4J,OAAO,GAAG5J,OAAO,CAAC4J,OAAO,GAAI,CAAC;IACpF,IAAIpL,KAAK,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC;IACtE,IAAI/B,IAAI,GAAGuD,OAAO,CAACvD,IAAI;IACvB,IAAIqC,KAAK,CAAC+K,OAAO,CAAC7J,OAAO,CAACvD,IAAI,CAAC,EAAE;MAC/B+B,KAAK,GAAGwB,OAAO,CAACvD,IAAI;MACpBA,IAAI,GAAGc,SAAS;IAClB;IACA,OAAOM,YAAY,CAACgL,IAAI,EAAE,IAAI,CAACpC,IAAI,CAACmD,OAAO,CAAC,EAAE;MAC5C,GAAG5J,OAAO;MACV8J,OAAO,EAAE,QAAQ;MACjBtL,KAAK;MACL/B;IACF,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEsN,kBAAkBA,CAAC/J,OAAO,GAAG,CAAC,CAAC,EAAE;IAC/B,IAAI,CAAC,IAAI,CAAC/E,OAAO,EAAE,OAAO,IAAI;IAE9B,OAAO4C,YAAY,CAACmC,OAAO,CAAC6I,IAAI,IAAIzR,QAAQ,CAACyC,UAAU,CAAC,CAAC,CAAC,EAAE;MAAE3D,IAAI,EAAE,IAAI,CAACA;IAAK,CAAC,CAAC,EAAE,IAAI,EAAE;MACtF,GAAG8J,OAAO;MACV8J,OAAO,EAAE,MAAM;MACftL,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC;MAClCP,SAAS,EAAE;IACb,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAOnG,GAAGA,CAAC,GAAGkS,SAAS,EAAE;IACvB,IAAI,CAACA,SAAS,CAACC,KAAK,CAAC7S,QAAQ,CAACqL,UAAU,CAAC,EAAE;MACzC,MAAM,IAAI/M,oBAAoB,CAAC,yCAAyC,CAAC;IAC3E;IACA,OAAO/B,MAAM,CAACqW,SAAS,EAAGE,CAAC,IAAKA,CAAC,CAACjK,OAAO,CAAC,CAAC,EAAEpI,IAAI,CAACC,GAAG,CAAC;EACxD;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAOC,GAAGA,CAAC,GAAGiS,SAAS,EAAE;IACvB,IAAI,CAACA,SAAS,CAACC,KAAK,CAAC7S,QAAQ,CAACqL,UAAU,CAAC,EAAE;MACzC,MAAM,IAAI/M,oBAAoB,CAAC,yCAAyC,CAAC;IAC3E;IACA,OAAO/B,MAAM,CAACqW,SAAS,EAAGE,CAAC,IAAKA,CAAC,CAACjK,OAAO,CAAC,CAAC,EAAEpI,IAAI,CAACE,GAAG,CAAC;EACxD;;EAEA;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,OAAOoS,iBAAiBA,CAAC3P,IAAI,EAAEsH,GAAG,EAAE9B,OAAO,GAAG,CAAC,CAAC,EAAE;IAChD,MAAM;QAAE+B,MAAM,GAAG,IAAI;QAAEC,eAAe,GAAG;MAAK,CAAC,GAAGhC,OAAO;MACvDiC,WAAW,GAAG3O,MAAM,CAAC4O,QAAQ,CAAC;QAC5BH,MAAM;QACNC,eAAe;QACfG,WAAW,EAAE;MACf,CAAC,CAAC;IACJ,OAAOxN,iBAAiB,CAACsN,WAAW,EAAEzH,IAAI,EAAEsH,GAAG,CAAC;EAClD;;EAEA;AACF;AACA;EACE,OAAOsI,iBAAiBA,CAAC5P,IAAI,EAAEsH,GAAG,EAAE9B,OAAO,GAAG,CAAC,CAAC,EAAE;IAChD,OAAO5I,QAAQ,CAAC+S,iBAAiB,CAAC3P,IAAI,EAAEsH,GAAG,EAAE9B,OAAO,CAAC;EACvD;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOqK,iBAAiBA,CAACvI,GAAG,EAAE9B,OAAO,GAAG,CAAC,CAAC,EAAE;IAC1C,MAAM;QAAE+B,MAAM,GAAG,IAAI;QAAEC,eAAe,GAAG;MAAK,CAAC,GAAGhC,OAAO;MACvDiC,WAAW,GAAG3O,MAAM,CAAC4O,QAAQ,CAAC;QAC5BH,MAAM;QACNC,eAAe;QACfG,WAAW,EAAE;MACf,CAAC,CAAC;IACJ,OAAO,IAAIrN,WAAW,CAACmN,WAAW,EAAEH,GAAG,CAAC;EAC1C;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAOwI,gBAAgBA,CAAC9P,IAAI,EAAE+P,YAAY,EAAEjQ,IAAI,GAAG,CAAC,CAAC,EAAE;IACrD,IAAI/G,WAAW,CAACiH,IAAI,CAAC,IAAIjH,WAAW,CAACgX,YAAY,CAAC,EAAE;MAClD,MAAM,IAAI7U,oBAAoB,CAC5B,+DACF,CAAC;IACH;IACA,MAAM;QAAEqM,MAAM,GAAG,IAAI;QAAEC,eAAe,GAAG;MAAK,CAAC,GAAG1H,IAAI;MACpD2H,WAAW,GAAG3O,MAAM,CAAC4O,QAAQ,CAAC;QAC5BH,MAAM;QACNC,eAAe;QACfG,WAAW,EAAE;MACf,CAAC,CAAC;IAEJ,IAAI,CAACF,WAAW,CAAC3C,MAAM,CAACiL,YAAY,CAACxI,MAAM,CAAC,EAAE;MAC5C,MAAM,IAAIrM,oBAAoB,CAC3B,4CAA2CuM,WAAY,IAAG,GACxD,yCAAwCsI,YAAY,CAACxI,MAAO,EACjE,CAAC;IACH;IAEA,MAAM;MAAEyI,MAAM;MAAEtU,IAAI;MAAEuE,cAAc;MAAE6I;IAAc,CAAC,GAAGiH,YAAY,CAAC5V,iBAAiB,CAAC6F,IAAI,CAAC;IAE5F,IAAI8I,aAAa,EAAE;MACjB,OAAOlM,QAAQ,CAACD,OAAO,CAACmM,aAAa,CAAC;IACxC,CAAC,MAAM;MACL,OAAOnJ,mBAAmB,CACxBqQ,MAAM,EACNtU,IAAI,EACJoE,IAAI,EACH,UAASiQ,YAAY,CAAChQ,MAAO,EAAC,EAC/BC,IAAI,EACJC,cACF,CAAC;IACH;EACF;;EAEA;;EAEA;AACF;AACA;AACA;EACE,WAAW4M,UAAUA,CAAA,EAAG;IACtB,OAAO5R,OAAO,CAAC4R,UAAU;EAC3B;;EAEA;AACF;AACA;AACA;EACE,WAAWoD,QAAQA,CAAA,EAAG;IACpB,OAAOhV,OAAO,CAACgV,QAAQ;EACzB;;EAEA;AACF;AACA;AACA;EACE,WAAWC,qBAAqBA,CAAA,EAAG;IACjC,OAAOjV,OAAO,CAACiV,qBAAqB;EACtC;;EAEA;AACF;AACA;AACA;EACE,WAAWC,SAASA,CAAA,EAAG;IACrB,OAAOlV,OAAO,CAACkV,SAAS;EAC1B;;EAEA;AACF;AACA;AACA;EACE,WAAWC,SAASA,CAAA,EAAG;IACrB,OAAOnV,OAAO,CAACmV,SAAS;EAC1B;;EAEA;AACF;AACA;AACA;EACE,WAAWC,WAAWA,CAAA,EAAG;IACvB,OAAOpV,OAAO,CAACoV,WAAW;EAC5B;;EAEA;AACF;AACA;AACA;EACE,WAAWC,iBAAiBA,CAAA,EAAG;IAC7B,OAAOrV,OAAO,CAACqV,iBAAiB;EAClC;;EAEA;AACF;AACA;AACA;EACE,WAAWC,sBAAsBA,CAAA,EAAG;IAClC,OAAOtV,OAAO,CAACsV,sBAAsB;EACvC;;EAEA;AACF;AACA;AACA;EACE,WAAWC,qBAAqBA,CAAA,EAAG;IACjC,OAAOvV,OAAO,CAACuV,qBAAqB;EACtC;;EAEA;AACF;AACA;AACA;EACE,WAAWC,cAAcA,CAAA,EAAG;IAC1B,OAAOxV,OAAO,CAACwV,cAAc;EAC/B;;EAEA;AACF;AACA;AACA;EACE,WAAWC,oBAAoBA,CAAA,EAAG;IAChC,OAAOzV,OAAO,CAACyV,oBAAoB;EACrC;;EAEA;AACF;AACA;AACA;EACE,WAAWC,yBAAyBA,CAAA,EAAG;IACrC,OAAO1V,OAAO,CAAC0V,yBAAyB;EAC1C;;EAEA;AACF;AACA;AACA;EACE,WAAWC,wBAAwBA,CAAA,EAAG;IACpC,OAAO3V,OAAO,CAAC2V,wBAAwB;EACzC;;EAEA;AACF;AACA;AACA;EACE,WAAWC,cAAcA,CAAA,EAAG;IAC1B,OAAO5V,OAAO,CAAC4V,cAAc;EAC/B;;EAEA;AACF;AACA;AACA;EACE,WAAWC,2BAA2BA,CAAA,EAAG;IACvC,OAAO7V,OAAO,CAAC6V,2BAA2B;EAC5C;;EAEA;AACF;AACA;AACA;EACE,WAAWC,YAAYA,CAAA,EAAG;IACxB,OAAO9V,OAAO,CAAC8V,YAAY;EAC7B;;EAEA;AACF;AACA;AACA;EACE,WAAWC,yBAAyBA,CAAA,EAAG;IACrC,OAAO/V,OAAO,CAAC+V,yBAAyB;EAC1C;;EAEA;AACF;AACA;AACA;EACE,WAAWC,yBAAyBA,CAAA,EAAG;IACrC,OAAOhW,OAAO,CAACgW,yBAAyB;EAC1C;;EAEA;AACF;AACA;AACA;EACE,WAAWC,aAAaA,CAAA,EAAG;IACzB,OAAOjW,OAAO,CAACiW,aAAa;EAC9B;;EAEA;AACF;AACA;AACA;EACE,WAAWC,0BAA0BA,CAAA,EAAG;IACtC,OAAOlW,OAAO,CAACkW,0BAA0B;EAC3C;;EAEA;AACF;AACA;AACA;EACE,WAAWC,aAAaA,CAAA,EAAG;IACzB,OAAOnW,OAAO,CAACmW,aAAa;EAC9B;;EAEA;AACF;AACA;AACA;EACE,WAAWC,0BAA0BA,CAAA,EAAG;IACtC,OAAOpW,OAAO,CAACoW,0BAA0B;EAC3C;AACF;;AAEA;AACA;AACA;AACA,OAAO,SAASC,gBAAgBA,CAACC,WAAW,EAAE;EAC5C,IAAI3U,QAAQ,CAACqL,UAAU,CAACsJ,WAAW,CAAC,EAAE;IACpC,OAAOA,WAAW;EACpB,CAAC,MAAM,IAAIA,WAAW,IAAIA,WAAW,CAAC9L,OAAO,IAAIvM,QAAQ,CAACqY,WAAW,CAAC9L,OAAO,CAAC,CAAC,CAAC,EAAE;IAChF,OAAO7I,QAAQ,CAAC0I,UAAU,CAACiM,WAAW,CAAC;EACzC,CAAC,MAAM,IAAIA,WAAW,IAAI,OAAOA,WAAW,KAAK,QAAQ,EAAE;IACzD,OAAO3U,QAAQ,CAACyC,UAAU,CAACkS,WAAW,CAAC;EACzC,CAAC,MAAM;IACL,MAAM,IAAIrW,oBAAoB,CAC3B,8BAA6BqW,WAAY,aAAY,OAAOA,WAAY,EAC3E,CAAC;EACH;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}