{"ast": null, "code": "import { ComponentPortal } from '@angular/cdk/portal';\nimport * as i0 from '@angular/core';\nimport { Directive, EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, NgModule, Injectable } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { filter, take, takeUntil } from 'rxjs/operators';\nimport * as i1 from 'ng-zorro-antd/core/config';\nimport { NgClass, NgSwitch, NgSwitchCase, NgForOf } from '@angular/common';\nimport { toCssPixel } from 'ng-zorro-antd/core/util';\nimport { moveUpMotion } from 'ng-zorro-antd/core/animation';\nimport * as i2 from 'ng-zorro-antd/core/outlet';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport * as i1$1 from 'ng-zorro-antd/icon';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport * as i1$2 from 'ng-zorro-antd/core/services';\nimport * as i2$1 from '@angular/cdk/overlay';\nfunction NzMessageComponent_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 10);\n  }\n}\nfunction NzMessageComponent_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 11);\n  }\n}\nfunction NzMessageComponent_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 12);\n  }\n}\nfunction NzMessageComponent_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 13);\n  }\n}\nfunction NzMessageComponent_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 14);\n  }\n}\nfunction NzMessageComponent_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"span\", 15);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r0.instance.content, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction NzMessageContainerComponent_nz_message_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nz-message\", 2);\n    i0.ɵɵlistener(\"destroyed\", function NzMessageContainerComponent_nz_message_1_Template_nz_message_destroyed_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.remove($event.id, $event.userAction));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const instance_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"instance\", instance_r3);\n  }\n}\nlet globalCounter = 0;\nclass NzMNService {\n  constructor(nzSingletonService, overlay, injector) {\n    this.nzSingletonService = nzSingletonService;\n    this.overlay = overlay;\n    this.injector = injector;\n  }\n  remove(id) {\n    if (this.container) {\n      if (id) {\n        this.container.remove(id);\n      } else {\n        this.container.removeAll();\n      }\n    }\n  }\n  getInstanceId() {\n    return `${this.componentPrefix}-${globalCounter++}`;\n  }\n  withContainer(ctor) {\n    let containerInstance = this.nzSingletonService.getSingletonWithKey(this.componentPrefix);\n    if (containerInstance) {\n      return containerInstance;\n    }\n    const overlayRef = this.overlay.create({\n      hasBackdrop: false,\n      scrollStrategy: this.overlay.scrollStrategies.noop(),\n      positionStrategy: this.overlay.position().global()\n    });\n    const componentPortal = new ComponentPortal(ctor, null, this.injector);\n    const componentRef = overlayRef.attach(componentPortal);\n    const overlayWrapper = overlayRef.hostElement;\n    overlayWrapper.style.zIndex = '1010';\n    if (!containerInstance) {\n      this.container = containerInstance = componentRef.instance;\n      this.nzSingletonService.registerSingletonWithKey(this.componentPrefix, containerInstance);\n      this.container.afterAllInstancesRemoved.subscribe(() => {\n        this.container = undefined;\n        this.nzSingletonService.unregisterSingletonWithKey(this.componentPrefix);\n        overlayRef.dispose();\n      });\n    }\n    return containerInstance;\n  }\n}\nclass NzMNContainerComponent {\n  constructor(cdr, nzConfigService) {\n    this.cdr = cdr;\n    this.nzConfigService = nzConfigService;\n    this.instances = [];\n    this._afterAllInstancesRemoved = new Subject();\n    this.afterAllInstancesRemoved = this._afterAllInstancesRemoved.asObservable();\n    this.destroy$ = new Subject();\n    this.updateConfig();\n  }\n  ngOnInit() {\n    this.subscribeConfigChange();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  create(data) {\n    const instance = this.onCreate(data);\n    if (this.instances.length >= this.config.nzMaxStack) {\n      this.instances = this.instances.slice(1);\n    }\n    this.instances = [...this.instances, instance];\n    this.readyInstances();\n    return instance;\n  }\n  remove(id, userAction = false) {\n    this.instances.map((instance, index) => ({\n      index,\n      instance\n    })).filter(({\n      instance\n    }) => instance.messageId === id).forEach(({\n      index,\n      instance\n    }) => {\n      this.instances.splice(index, 1);\n      this.instances = [...this.instances];\n      this.onRemove(instance, userAction);\n      this.readyInstances();\n    });\n    if (!this.instances.length) {\n      this.onAllInstancesRemoved();\n    }\n  }\n  removeAll() {\n    this.instances.forEach(i => this.onRemove(i, false));\n    this.instances = [];\n    this.readyInstances();\n    this.onAllInstancesRemoved();\n  }\n  onCreate(instance) {\n    instance.options = this.mergeOptions(instance.options);\n    instance.onClose = new Subject();\n    return instance;\n  }\n  onRemove(instance, userAction) {\n    instance.onClose.next(userAction);\n    instance.onClose.complete();\n  }\n  onAllInstancesRemoved() {\n    this._afterAllInstancesRemoved.next();\n    this._afterAllInstancesRemoved.complete();\n  }\n  readyInstances() {\n    this.cdr.detectChanges();\n  }\n  mergeOptions(options) {\n    const {\n      nzDuration,\n      nzAnimate,\n      nzPauseOnHover\n    } = this.config;\n    return {\n      nzDuration,\n      nzAnimate,\n      nzPauseOnHover,\n      ...options\n    };\n  }\n  static {\n    this.ɵfac = function NzMNContainerComponent_Factory(t) {\n      return new (t || NzMNContainerComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.NzConfigService));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzMNContainerComponent\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzMNContainerComponent, [{\n    type: Directive\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1.NzConfigService\n  }], null);\n})();\nclass NzMNComponent {\n  constructor(cdr) {\n    this.cdr = cdr;\n    this.destroyed = new EventEmitter();\n    this.animationStateChanged = new Subject();\n    this.userAction = false;\n  }\n  ngOnInit() {\n    this.options = this.instance.options;\n    if (this.options.nzAnimate) {\n      this.instance.state = 'enter';\n      this.animationStateChanged.pipe(filter(event => event.phaseName === 'done' && event.toState === 'leave'), take(1)).subscribe(() => {\n        clearTimeout(this.closeTimer);\n        this.destroyed.next({\n          id: this.instance.messageId,\n          userAction: this.userAction\n        });\n      });\n    }\n    this.autoClose = this.options.nzDuration > 0;\n    if (this.autoClose) {\n      this.initErase();\n      this.startEraseTimeout();\n    }\n  }\n  ngOnDestroy() {\n    if (this.autoClose) {\n      this.clearEraseTimeout();\n    }\n    this.animationStateChanged.complete();\n  }\n  onEnter() {\n    if (this.autoClose && this.options.nzPauseOnHover) {\n      this.clearEraseTimeout();\n      this.updateTTL();\n    }\n  }\n  onLeave() {\n    if (this.autoClose && this.options.nzPauseOnHover) {\n      this.startEraseTimeout();\n    }\n  }\n  destroy(userAction = false) {\n    this.userAction = userAction;\n    if (this.options.nzAnimate) {\n      this.instance.state = 'leave';\n      this.cdr.detectChanges();\n      this.closeTimer = setTimeout(() => {\n        this.closeTimer = undefined;\n        this.destroyed.next({\n          id: this.instance.messageId,\n          userAction\n        });\n      }, 200);\n    } else {\n      this.destroyed.next({\n        id: this.instance.messageId,\n        userAction\n      });\n    }\n  }\n  initErase() {\n    this.eraseTTL = this.options.nzDuration;\n    this.eraseTimingStart = Date.now();\n  }\n  updateTTL() {\n    if (this.autoClose) {\n      this.eraseTTL -= Date.now() - this.eraseTimingStart;\n    }\n  }\n  startEraseTimeout() {\n    if (this.eraseTTL > 0) {\n      this.clearEraseTimeout();\n      this.eraseTimer = setTimeout(() => this.destroy(), this.eraseTTL);\n      this.eraseTimingStart = Date.now();\n    } else {\n      this.destroy();\n    }\n  }\n  clearEraseTimeout() {\n    if (this.eraseTimer !== null) {\n      clearTimeout(this.eraseTimer);\n      this.eraseTimer = undefined;\n    }\n  }\n  static {\n    this.ɵfac = function NzMNComponent_Factory(t) {\n      return new (t || NzMNComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzMNComponent\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzMNComponent, [{\n    type: Directive\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }], null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzMessageComponent extends NzMNComponent {\n  constructor(cdr) {\n    super(cdr);\n    this.destroyed = new EventEmitter();\n  }\n  static {\n    this.ɵfac = function NzMessageComponent_Factory(t) {\n      return new (t || NzMessageComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzMessageComponent,\n      selectors: [[\"nz-message\"]],\n      inputs: {\n        instance: \"instance\"\n      },\n      outputs: {\n        destroyed: \"destroyed\"\n      },\n      exportAs: [\"nzMessage\"],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 10,\n      vars: 9,\n      consts: [[1, \"ant-message-notice\", 3, \"mouseenter\", \"mouseleave\"], [1, \"ant-message-notice-content\"], [1, \"ant-message-custom-content\", 3, \"ngClass\"], [3, \"ngSwitch\"], [\"nz-icon\", \"\", \"nzType\", \"check-circle\", 4, \"ngSwitchCase\"], [\"nz-icon\", \"\", \"nzType\", \"info-circle\", 4, \"ngSwitchCase\"], [\"nz-icon\", \"\", \"nzType\", \"exclamation-circle\", 4, \"ngSwitchCase\"], [\"nz-icon\", \"\", \"nzType\", \"close-circle\", 4, \"ngSwitchCase\"], [\"nz-icon\", \"\", \"nzType\", \"loading\", 4, \"ngSwitchCase\"], [4, \"nzStringTemplateOutlet\"], [\"nz-icon\", \"\", \"nzType\", \"check-circle\"], [\"nz-icon\", \"\", \"nzType\", \"info-circle\"], [\"nz-icon\", \"\", \"nzType\", \"exclamation-circle\"], [\"nz-icon\", \"\", \"nzType\", \"close-circle\"], [\"nz-icon\", \"\", \"nzType\", \"loading\"], [3, \"innerHTML\"]],\n      template: function NzMessageComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵlistener(\"@moveUpMotion.done\", function NzMessageComponent_Template_div_animation_moveUpMotion_done_0_listener($event) {\n            return ctx.animationStateChanged.next($event);\n          })(\"mouseenter\", function NzMessageComponent_Template_div_mouseenter_0_listener() {\n            return ctx.onEnter();\n          })(\"mouseleave\", function NzMessageComponent_Template_div_mouseleave_0_listener() {\n            return ctx.onLeave();\n          });\n          i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelementContainerStart(3, 3);\n          i0.ɵɵtemplate(4, NzMessageComponent_span_4_Template, 1, 0, \"span\", 4)(5, NzMessageComponent_span_5_Template, 1, 0, \"span\", 5)(6, NzMessageComponent_span_6_Template, 1, 0, \"span\", 6)(7, NzMessageComponent_span_7_Template, 1, 0, \"span\", 7)(8, NzMessageComponent_span_8_Template, 1, 0, \"span\", 8);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵtemplate(9, NzMessageComponent_ng_container_9_Template, 2, 1, \"ng-container\", 9);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"@moveUpMotion\", ctx.instance.state);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", \"ant-message-\" + ctx.instance.type);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngSwitch\", ctx.instance.type);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngSwitchCase\", \"success\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngSwitchCase\", \"info\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngSwitchCase\", \"warning\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngSwitchCase\", \"error\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngSwitchCase\", \"loading\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx.instance.content);\n        }\n      },\n      dependencies: [NgClass, NgSwitch, NgSwitchCase, NzIconModule, i1$1.NzIconDirective, NzOutletModule, i2.NzStringTemplateOutletDirective],\n      encapsulation: 2,\n      data: {\n        animation: [moveUpMotion]\n      },\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzMessageComponent, [{\n    type: Component,\n    args: [{\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      selector: 'nz-message',\n      exportAs: 'nzMessage',\n      preserveWhitespaces: false,\n      animations: [moveUpMotion],\n      template: `\n    <div\n      class=\"ant-message-notice\"\n      [@moveUpMotion]=\"instance.state\"\n      (@moveUpMotion.done)=\"animationStateChanged.next($event)\"\n      (mouseenter)=\"onEnter()\"\n      (mouseleave)=\"onLeave()\"\n    >\n      <div class=\"ant-message-notice-content\">\n        <div class=\"ant-message-custom-content\" [ngClass]=\"'ant-message-' + instance.type\">\n          <ng-container [ngSwitch]=\"instance.type\">\n            <span *ngSwitchCase=\"'success'\" nz-icon nzType=\"check-circle\"></span>\n            <span *ngSwitchCase=\"'info'\" nz-icon nzType=\"info-circle\"></span>\n            <span *ngSwitchCase=\"'warning'\" nz-icon nzType=\"exclamation-circle\"></span>\n            <span *ngSwitchCase=\"'error'\" nz-icon nzType=\"close-circle\"></span>\n            <span *ngSwitchCase=\"'loading'\" nz-icon nzType=\"loading\"></span>\n          </ng-container>\n          <ng-container *nzStringTemplateOutlet=\"instance.content\">\n            <span [innerHTML]=\"instance.content\"></span>\n          </ng-container>\n        </div>\n      </div>\n    </div>\n  `,\n      imports: [NgClass, NgSwitch, NgSwitchCase, NzIconModule, NzOutletModule],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }], {\n    instance: [{\n      type: Input\n    }],\n    destroyed: [{\n      type: Output\n    }]\n  });\n})();\nconst NZ_CONFIG_COMPONENT_NAME = 'message';\nconst NZ_MESSAGE_DEFAULT_CONFIG = {\n  nzAnimate: true,\n  nzDuration: 3000,\n  nzMaxStack: 7,\n  nzPauseOnHover: true,\n  nzTop: 24,\n  nzDirection: 'ltr'\n};\nclass NzMessageContainerComponent extends NzMNContainerComponent {\n  constructor(cdr, nzConfigService) {\n    super(cdr, nzConfigService);\n    this.dir = 'ltr';\n    const config = this.nzConfigService.getConfigForComponent(NZ_CONFIG_COMPONENT_NAME);\n    this.dir = config?.nzDirection || 'ltr';\n  }\n  subscribeConfigChange() {\n    this.nzConfigService.getConfigChangeEventForComponent(NZ_CONFIG_COMPONENT_NAME).pipe(takeUntil(this.destroy$)).subscribe(() => {\n      this.updateConfig();\n      const config = this.nzConfigService.getConfigForComponent(NZ_CONFIG_COMPONENT_NAME);\n      if (config) {\n        const {\n          nzDirection\n        } = config;\n        this.dir = nzDirection || this.dir;\n      }\n    });\n  }\n  updateConfig() {\n    this.config = {\n      ...NZ_MESSAGE_DEFAULT_CONFIG,\n      ...this.config,\n      ...this.nzConfigService.getConfigForComponent(NZ_CONFIG_COMPONENT_NAME)\n    };\n    this.top = toCssPixel(this.config.nzTop);\n    this.cdr.markForCheck();\n  }\n  static {\n    this.ɵfac = function NzMessageContainerComponent_Factory(t) {\n      return new (t || NzMessageContainerComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.NzConfigService));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzMessageContainerComponent,\n      selectors: [[\"nz-message-container\"]],\n      exportAs: [\"nzMessageContainer\"],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 5,\n      consts: [[1, \"ant-message\"], [3, \"instance\", \"destroyed\", 4, \"ngFor\", \"ngForOf\"], [3, \"destroyed\", \"instance\"]],\n      template: function NzMessageContainerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, NzMessageContainerComponent_nz_message_1_Template, 1, 1, \"nz-message\", 1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"top\", ctx.top);\n          i0.ɵɵclassProp(\"ant-message-rtl\", ctx.dir === \"rtl\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.instances);\n        }\n      },\n      dependencies: [NzMessageComponent, NgForOf],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzMessageContainerComponent, [{\n    type: Component,\n    args: [{\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      selector: 'nz-message-container',\n      exportAs: 'nzMessageContainer',\n      preserveWhitespaces: false,\n      template: `\n    <div class=\"ant-message\" [class.ant-message-rtl]=\"dir === 'rtl'\" [style.top]=\"top\">\n      <nz-message\n        *ngFor=\"let instance of instances\"\n        [instance]=\"instance\"\n        (destroyed)=\"remove($event.id, $event.userAction)\"\n      ></nz-message>\n    </div>\n  `,\n      imports: [NzMessageComponent, NgForOf],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1.NzConfigService\n  }], null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzMessageModule {\n  static {\n    this.ɵfac = function NzMessageModule_Factory(t) {\n      return new (t || NzMessageModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzMessageModule,\n      imports: [NzMessageContainerComponent, NzMessageComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [NzMessageContainerComponent, NzMessageComponent]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzMessageModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzMessageContainerComponent, NzMessageComponent]\n    }]\n  }], null, null);\n})();\nclass NzMessageService extends NzMNService {\n  constructor(nzSingletonService, overlay, injector) {\n    super(nzSingletonService, overlay, injector);\n    this.componentPrefix = 'message-';\n  }\n  success(content, options) {\n    return this.createInstance({\n      type: 'success',\n      content\n    }, options);\n  }\n  error(content, options) {\n    return this.createInstance({\n      type: 'error',\n      content\n    }, options);\n  }\n  info(content, options) {\n    return this.createInstance({\n      type: 'info',\n      content\n    }, options);\n  }\n  warning(content, options) {\n    return this.createInstance({\n      type: 'warning',\n      content\n    }, options);\n  }\n  loading(content, options) {\n    return this.createInstance({\n      type: 'loading',\n      content\n    }, options);\n  }\n  create(type, content, options) {\n    return this.createInstance({\n      type,\n      content\n    }, options);\n  }\n  createInstance(message, options) {\n    this.container = this.withContainer(NzMessageContainerComponent);\n    return this.container.create({\n      ...message,\n      ...{\n        createdAt: new Date(),\n        messageId: this.getInstanceId(),\n        options\n      }\n    });\n  }\n  static {\n    this.ɵfac = function NzMessageService_Factory(t) {\n      return new (t || NzMessageService)(i0.ɵɵinject(i1$2.NzSingletonService), i0.ɵɵinject(i2$1.Overlay), i0.ɵɵinject(i0.Injector));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NzMessageService,\n      factory: NzMessageService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzMessageService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i1$2.NzSingletonService\n  }, {\n    type: i2$1.Overlay\n  }, {\n    type: i0.Injector\n  }], null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzMNComponent, NzMNContainerComponent, NzMNService, NzMessageComponent, NzMessageContainerComponent, NzMessageModule, NzMessageService };", "map": {"version": 3, "names": ["ComponentPortal", "i0", "Directive", "EventEmitter", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Input", "Output", "NgModule", "Injectable", "Subject", "filter", "take", "takeUntil", "i1", "Ng<PERSON><PERSON>", "NgSwitch", "NgSwitchCase", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "toCssPixel", "moveUpMotion", "i2", "NzOutletModule", "i1$1", "NzIconModule", "i1$2", "i2$1", "NzMessageComponent_span_4_Template", "rf", "ctx", "ɵɵelement", "NzMessageComponent_span_5_Template", "NzMessageComponent_span_6_Template", "NzMessageComponent_span_7_Template", "NzMessageComponent_span_8_Template", "NzMessageComponent_ng_container_9_Template", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "ctx_r0", "ɵɵnextContext", "ɵɵadvance", "ɵɵproperty", "instance", "content", "ɵɵsanitizeHtml", "NzMessageContainerComponent_nz_message_1_Template", "_r1", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "NzMessageContainerComponent_nz_message_1_Template_nz_message_destroyed_0_listener", "$event", "ɵɵrestoreView", "ctx_r1", "ɵɵresetView", "remove", "id", "userAction", "ɵɵelementEnd", "instance_r3", "$implicit", "globalCounter", "NzMNService", "constructor", "nzSingletonService", "overlay", "injector", "container", "removeAll", "getInstanceId", "componentPrefix", "<PERSON><PERSON><PERSON><PERSON>", "ctor", "containerInstance", "getSingletonWithKey", "overlayRef", "create", "hasBackdrop", "scrollStrategy", "scrollStrategies", "noop", "positionStrategy", "position", "global", "componentPortal", "componentRef", "attach", "overlayWrapper", "hostElement", "style", "zIndex", "registerSingletonWithKey", "afterAllInstancesRemoved", "subscribe", "undefined", "unregisterSingletonWithKey", "dispose", "NzMNContainerComponent", "cdr", "nzConfigService", "instances", "_afterAllInstancesRemoved", "asObservable", "destroy$", "updateConfig", "ngOnInit", "subscribeConfigChange", "ngOnDestroy", "next", "complete", "data", "onCreate", "length", "config", "nzMaxStack", "slice", "readyInstances", "map", "index", "messageId", "for<PERSON>ach", "splice", "onRemove", "onAllInstancesRemoved", "i", "options", "mergeOptions", "onClose", "detectChanges", "nzDuration", "nzAnimate", "nzPauseOnHover", "ɵfac", "NzMNContainerComponent_Factory", "t", "ɵɵdirectiveInject", "ChangeDetectorRef", "NzConfigService", "ɵdir", "ɵɵdefineDirective", "type", "ngDevMode", "ɵsetClassMetadata", "NzMNComponent", "destroyed", "animationStateChanged", "state", "pipe", "event", "phaseName", "toState", "clearTimeout", "closeTimer", "autoClose", "initErase", "startEraseTimeout", "clearEraseTimeout", "onEnter", "updateTTL", "onLeave", "destroy", "setTimeout", "eraseTTL", "eraseTimingStart", "Date", "now", "eraseTimer", "NzMNComponent_Factory", "NzMessageComponent", "NzMessageComponent_Factory", "ɵcmp", "ɵɵdefineComponent", "selectors", "inputs", "outputs", "exportAs", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "NzMessageComponent_Template", "NzMessageComponent_Template_div_animation_moveUpMotion_done_0_listener", "NzMessageComponent_Template_div_mouseenter_0_listener", "NzMessageComponent_Template_div_mouseleave_0_listener", "ɵɵtemplate", "dependencies", "NzIconDirective", "NzStringTemplateOutletDirective", "encapsulation", "animation", "changeDetection", "args", "OnPush", "None", "selector", "preserveWhitespaces", "animations", "imports", "NZ_CONFIG_COMPONENT_NAME", "NZ_MESSAGE_DEFAULT_CONFIG", "nzTop", "nzDirection", "NzMessageContainerComponent", "dir", "getConfigForComponent", "getConfigChangeEventForComponent", "top", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "NzMessageContainerComponent_Factory", "NzMessageContainerComponent_Template", "ɵɵstyleProp", "ɵɵclassProp", "NzMessageModule", "NzMessageModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "NzMessageService", "success", "createInstance", "error", "info", "warning", "loading", "message", "createdAt", "NzMessageService_Factory", "ɵɵinject", "NzSingletonService", "Overlay", "Injector", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-message.mjs"], "sourcesContent": ["import { ComponentPortal } from '@angular/cdk/portal';\nimport * as i0 from '@angular/core';\nimport { Directive, EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, NgModule, Injectable } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { filter, take, takeUntil } from 'rxjs/operators';\nimport * as i1 from 'ng-zorro-antd/core/config';\nimport { NgClass, NgSwitch, NgSwitchCase, NgForOf } from '@angular/common';\nimport { toCssPixel } from 'ng-zorro-antd/core/util';\nimport { moveUpMotion } from 'ng-zorro-antd/core/animation';\nimport * as i2 from 'ng-zorro-antd/core/outlet';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport * as i1$1 from 'ng-zorro-antd/icon';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport * as i1$2 from 'ng-zorro-antd/core/services';\nimport * as i2$1 from '@angular/cdk/overlay';\n\nlet globalCounter = 0;\nclass NzMNService {\n    constructor(nzSingletonService, overlay, injector) {\n        this.nzSingletonService = nzSingletonService;\n        this.overlay = overlay;\n        this.injector = injector;\n    }\n    remove(id) {\n        if (this.container) {\n            if (id) {\n                this.container.remove(id);\n            }\n            else {\n                this.container.removeAll();\n            }\n        }\n    }\n    getInstanceId() {\n        return `${this.componentPrefix}-${globalCounter++}`;\n    }\n    withContainer(ctor) {\n        let containerInstance = this.nzSingletonService.getSingletonWithKey(this.componentPrefix);\n        if (containerInstance) {\n            return containerInstance;\n        }\n        const overlayRef = this.overlay.create({\n            hasBackdrop: false,\n            scrollStrategy: this.overlay.scrollStrategies.noop(),\n            positionStrategy: this.overlay.position().global()\n        });\n        const componentPortal = new ComponentPortal(ctor, null, this.injector);\n        const componentRef = overlayRef.attach(componentPortal);\n        const overlayWrapper = overlayRef.hostElement;\n        overlayWrapper.style.zIndex = '1010';\n        if (!containerInstance) {\n            this.container = containerInstance = componentRef.instance;\n            this.nzSingletonService.registerSingletonWithKey(this.componentPrefix, containerInstance);\n            this.container.afterAllInstancesRemoved.subscribe(() => {\n                this.container = undefined;\n                this.nzSingletonService.unregisterSingletonWithKey(this.componentPrefix);\n                overlayRef.dispose();\n            });\n        }\n        return containerInstance;\n    }\n}\nclass NzMNContainerComponent {\n    constructor(cdr, nzConfigService) {\n        this.cdr = cdr;\n        this.nzConfigService = nzConfigService;\n        this.instances = [];\n        this._afterAllInstancesRemoved = new Subject();\n        this.afterAllInstancesRemoved = this._afterAllInstancesRemoved.asObservable();\n        this.destroy$ = new Subject();\n        this.updateConfig();\n    }\n    ngOnInit() {\n        this.subscribeConfigChange();\n    }\n    ngOnDestroy() {\n        this.destroy$.next();\n        this.destroy$.complete();\n    }\n    create(data) {\n        const instance = this.onCreate(data);\n        if (this.instances.length >= this.config.nzMaxStack) {\n            this.instances = this.instances.slice(1);\n        }\n        this.instances = [...this.instances, instance];\n        this.readyInstances();\n        return instance;\n    }\n    remove(id, userAction = false) {\n        this.instances\n            .map((instance, index) => ({ index, instance }))\n            .filter(({ instance }) => instance.messageId === id)\n            .forEach(({ index, instance }) => {\n            this.instances.splice(index, 1);\n            this.instances = [...this.instances];\n            this.onRemove(instance, userAction);\n            this.readyInstances();\n        });\n        if (!this.instances.length) {\n            this.onAllInstancesRemoved();\n        }\n    }\n    removeAll() {\n        this.instances.forEach(i => this.onRemove(i, false));\n        this.instances = [];\n        this.readyInstances();\n        this.onAllInstancesRemoved();\n    }\n    onCreate(instance) {\n        instance.options = this.mergeOptions(instance.options);\n        instance.onClose = new Subject();\n        return instance;\n    }\n    onRemove(instance, userAction) {\n        instance.onClose.next(userAction);\n        instance.onClose.complete();\n    }\n    onAllInstancesRemoved() {\n        this._afterAllInstancesRemoved.next();\n        this._afterAllInstancesRemoved.complete();\n    }\n    readyInstances() {\n        this.cdr.detectChanges();\n    }\n    mergeOptions(options) {\n        const { nzDuration, nzAnimate, nzPauseOnHover } = this.config;\n        return { nzDuration, nzAnimate, nzPauseOnHover, ...options };\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzMNContainerComponent, deps: [{ token: i0.ChangeDetectorRef }, { token: i1.NzConfigService }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzMNContainerComponent, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzMNContainerComponent, decorators: [{\n            type: Directive\n        }], ctorParameters: () => [{ type: i0.ChangeDetectorRef }, { type: i1.NzConfigService }] });\nclass NzMNComponent {\n    constructor(cdr) {\n        this.cdr = cdr;\n        this.destroyed = new EventEmitter();\n        this.animationStateChanged = new Subject();\n        this.userAction = false;\n    }\n    ngOnInit() {\n        this.options = this.instance.options;\n        if (this.options.nzAnimate) {\n            this.instance.state = 'enter';\n            this.animationStateChanged\n                .pipe(filter(event => event.phaseName === 'done' && event.toState === 'leave'), take(1))\n                .subscribe(() => {\n                clearTimeout(this.closeTimer);\n                this.destroyed.next({ id: this.instance.messageId, userAction: this.userAction });\n            });\n        }\n        this.autoClose = this.options.nzDuration > 0;\n        if (this.autoClose) {\n            this.initErase();\n            this.startEraseTimeout();\n        }\n    }\n    ngOnDestroy() {\n        if (this.autoClose) {\n            this.clearEraseTimeout();\n        }\n        this.animationStateChanged.complete();\n    }\n    onEnter() {\n        if (this.autoClose && this.options.nzPauseOnHover) {\n            this.clearEraseTimeout();\n            this.updateTTL();\n        }\n    }\n    onLeave() {\n        if (this.autoClose && this.options.nzPauseOnHover) {\n            this.startEraseTimeout();\n        }\n    }\n    destroy(userAction = false) {\n        this.userAction = userAction;\n        if (this.options.nzAnimate) {\n            this.instance.state = 'leave';\n            this.cdr.detectChanges();\n            this.closeTimer = setTimeout(() => {\n                this.closeTimer = undefined;\n                this.destroyed.next({ id: this.instance.messageId, userAction });\n            }, 200);\n        }\n        else {\n            this.destroyed.next({ id: this.instance.messageId, userAction });\n        }\n    }\n    initErase() {\n        this.eraseTTL = this.options.nzDuration;\n        this.eraseTimingStart = Date.now();\n    }\n    updateTTL() {\n        if (this.autoClose) {\n            this.eraseTTL -= Date.now() - this.eraseTimingStart;\n        }\n    }\n    startEraseTimeout() {\n        if (this.eraseTTL > 0) {\n            this.clearEraseTimeout();\n            this.eraseTimer = setTimeout(() => this.destroy(), this.eraseTTL);\n            this.eraseTimingStart = Date.now();\n        }\n        else {\n            this.destroy();\n        }\n    }\n    clearEraseTimeout() {\n        if (this.eraseTimer !== null) {\n            clearTimeout(this.eraseTimer);\n            this.eraseTimer = undefined;\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzMNComponent, deps: [{ token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzMNComponent, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzMNComponent, decorators: [{\n            type: Directive\n        }], ctorParameters: () => [{ type: i0.ChangeDetectorRef }] });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzMessageComponent extends NzMNComponent {\n    constructor(cdr) {\n        super(cdr);\n        this.destroyed = new EventEmitter();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzMessageComponent, deps: [{ token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzMessageComponent, isStandalone: true, selector: \"nz-message\", inputs: { instance: \"instance\" }, outputs: { destroyed: \"destroyed\" }, exportAs: [\"nzMessage\"], usesInheritance: true, ngImport: i0, template: `\n    <div\n      class=\"ant-message-notice\"\n      [@moveUpMotion]=\"instance.state\"\n      (@moveUpMotion.done)=\"animationStateChanged.next($event)\"\n      (mouseenter)=\"onEnter()\"\n      (mouseleave)=\"onLeave()\"\n    >\n      <div class=\"ant-message-notice-content\">\n        <div class=\"ant-message-custom-content\" [ngClass]=\"'ant-message-' + instance.type\">\n          <ng-container [ngSwitch]=\"instance.type\">\n            <span *ngSwitchCase=\"'success'\" nz-icon nzType=\"check-circle\"></span>\n            <span *ngSwitchCase=\"'info'\" nz-icon nzType=\"info-circle\"></span>\n            <span *ngSwitchCase=\"'warning'\" nz-icon nzType=\"exclamation-circle\"></span>\n            <span *ngSwitchCase=\"'error'\" nz-icon nzType=\"close-circle\"></span>\n            <span *ngSwitchCase=\"'loading'\" nz-icon nzType=\"loading\"></span>\n          </ng-container>\n          <ng-container *nzStringTemplateOutlet=\"instance.content\">\n            <span [innerHTML]=\"instance.content\"></span>\n          </ng-container>\n        </div>\n      </div>\n    </div>\n  `, isInline: true, dependencies: [{ kind: \"directive\", type: NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: NgSwitch, selector: \"[ngSwitch]\", inputs: [\"ngSwitch\"] }, { kind: \"directive\", type: NgSwitchCase, selector: \"[ngSwitchCase]\", inputs: [\"ngSwitchCase\"] }, { kind: \"ngmodule\", type: NzIconModule }, { kind: \"directive\", type: i1$1.NzIconDirective, selector: \"[nz-icon]\", inputs: [\"nzSpin\", \"nzRotate\", \"nzType\", \"nzTheme\", \"nzTwotoneColor\", \"nzIconfont\"], exportAs: [\"nzIcon\"] }, { kind: \"ngmodule\", type: NzOutletModule }, { kind: \"directive\", type: i2.NzStringTemplateOutletDirective, selector: \"[nzStringTemplateOutlet]\", inputs: [\"nzStringTemplateOutletContext\", \"nzStringTemplateOutlet\"], exportAs: [\"nzStringTemplateOutlet\"] }], animations: [moveUpMotion], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzMessageComponent, decorators: [{\n            type: Component,\n            args: [{\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    encapsulation: ViewEncapsulation.None,\n                    selector: 'nz-message',\n                    exportAs: 'nzMessage',\n                    preserveWhitespaces: false,\n                    animations: [moveUpMotion],\n                    template: `\n    <div\n      class=\"ant-message-notice\"\n      [@moveUpMotion]=\"instance.state\"\n      (@moveUpMotion.done)=\"animationStateChanged.next($event)\"\n      (mouseenter)=\"onEnter()\"\n      (mouseleave)=\"onLeave()\"\n    >\n      <div class=\"ant-message-notice-content\">\n        <div class=\"ant-message-custom-content\" [ngClass]=\"'ant-message-' + instance.type\">\n          <ng-container [ngSwitch]=\"instance.type\">\n            <span *ngSwitchCase=\"'success'\" nz-icon nzType=\"check-circle\"></span>\n            <span *ngSwitchCase=\"'info'\" nz-icon nzType=\"info-circle\"></span>\n            <span *ngSwitchCase=\"'warning'\" nz-icon nzType=\"exclamation-circle\"></span>\n            <span *ngSwitchCase=\"'error'\" nz-icon nzType=\"close-circle\"></span>\n            <span *ngSwitchCase=\"'loading'\" nz-icon nzType=\"loading\"></span>\n          </ng-container>\n          <ng-container *nzStringTemplateOutlet=\"instance.content\">\n            <span [innerHTML]=\"instance.content\"></span>\n          </ng-container>\n        </div>\n      </div>\n    </div>\n  `,\n                    imports: [NgClass, NgSwitch, NgSwitchCase, NzIconModule, NzOutletModule],\n                    standalone: true\n                }]\n        }], ctorParameters: () => [{ type: i0.ChangeDetectorRef }], propDecorators: { instance: [{\n                type: Input\n            }], destroyed: [{\n                type: Output\n            }] } });\n\nconst NZ_CONFIG_COMPONENT_NAME = 'message';\nconst NZ_MESSAGE_DEFAULT_CONFIG = {\n    nzAnimate: true,\n    nzDuration: 3000,\n    nzMaxStack: 7,\n    nzPauseOnHover: true,\n    nzTop: 24,\n    nzDirection: 'ltr'\n};\nclass NzMessageContainerComponent extends NzMNContainerComponent {\n    constructor(cdr, nzConfigService) {\n        super(cdr, nzConfigService);\n        this.dir = 'ltr';\n        const config = this.nzConfigService.getConfigForComponent(NZ_CONFIG_COMPONENT_NAME);\n        this.dir = config?.nzDirection || 'ltr';\n    }\n    subscribeConfigChange() {\n        this.nzConfigService\n            .getConfigChangeEventForComponent(NZ_CONFIG_COMPONENT_NAME)\n            .pipe(takeUntil(this.destroy$))\n            .subscribe(() => {\n            this.updateConfig();\n            const config = this.nzConfigService.getConfigForComponent(NZ_CONFIG_COMPONENT_NAME);\n            if (config) {\n                const { nzDirection } = config;\n                this.dir = nzDirection || this.dir;\n            }\n        });\n    }\n    updateConfig() {\n        this.config = {\n            ...NZ_MESSAGE_DEFAULT_CONFIG,\n            ...this.config,\n            ...this.nzConfigService.getConfigForComponent(NZ_CONFIG_COMPONENT_NAME)\n        };\n        this.top = toCssPixel(this.config.nzTop);\n        this.cdr.markForCheck();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzMessageContainerComponent, deps: [{ token: i0.ChangeDetectorRef }, { token: i1.NzConfigService }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzMessageContainerComponent, isStandalone: true, selector: \"nz-message-container\", exportAs: [\"nzMessageContainer\"], usesInheritance: true, ngImport: i0, template: `\n    <div class=\"ant-message\" [class.ant-message-rtl]=\"dir === 'rtl'\" [style.top]=\"top\">\n      <nz-message\n        *ngFor=\"let instance of instances\"\n        [instance]=\"instance\"\n        (destroyed)=\"remove($event.id, $event.userAction)\"\n      ></nz-message>\n    </div>\n  `, isInline: true, dependencies: [{ kind: \"component\", type: NzMessageComponent, selector: \"nz-message\", inputs: [\"instance\"], outputs: [\"destroyed\"], exportAs: [\"nzMessage\"] }, { kind: \"directive\", type: NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzMessageContainerComponent, decorators: [{\n            type: Component,\n            args: [{\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    encapsulation: ViewEncapsulation.None,\n                    selector: 'nz-message-container',\n                    exportAs: 'nzMessageContainer',\n                    preserveWhitespaces: false,\n                    template: `\n    <div class=\"ant-message\" [class.ant-message-rtl]=\"dir === 'rtl'\" [style.top]=\"top\">\n      <nz-message\n        *ngFor=\"let instance of instances\"\n        [instance]=\"instance\"\n        (destroyed)=\"remove($event.id, $event.userAction)\"\n      ></nz-message>\n    </div>\n  `,\n                    imports: [NzMessageComponent, NgForOf],\n                    standalone: true\n                }]\n        }], ctorParameters: () => [{ type: i0.ChangeDetectorRef }, { type: i1.NzConfigService }] });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzMessageModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzMessageModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.3.8\", ngImport: i0, type: NzMessageModule, imports: [NzMessageContainerComponent, NzMessageComponent] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzMessageModule, imports: [NzMessageContainerComponent, NzMessageComponent] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzMessageModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [NzMessageContainerComponent, NzMessageComponent]\n                }]\n        }] });\n\nclass NzMessageService extends NzMNService {\n    constructor(nzSingletonService, overlay, injector) {\n        super(nzSingletonService, overlay, injector);\n        this.componentPrefix = 'message-';\n    }\n    success(content, options) {\n        return this.createInstance({ type: 'success', content }, options);\n    }\n    error(content, options) {\n        return this.createInstance({ type: 'error', content }, options);\n    }\n    info(content, options) {\n        return this.createInstance({ type: 'info', content }, options);\n    }\n    warning(content, options) {\n        return this.createInstance({ type: 'warning', content }, options);\n    }\n    loading(content, options) {\n        return this.createInstance({ type: 'loading', content }, options);\n    }\n    create(type, content, options) {\n        return this.createInstance({ type, content }, options);\n    }\n    createInstance(message, options) {\n        this.container = this.withContainer(NzMessageContainerComponent);\n        return this.container.create({\n            ...message,\n            ...{\n                createdAt: new Date(),\n                messageId: this.getInstanceId(),\n                options\n            }\n        });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzMessageService, deps: [{ token: i1$2.NzSingletonService }, { token: i2$1.Overlay }, { token: i0.Injector }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzMessageService, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzMessageService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root'\n                }]\n        }], ctorParameters: () => [{ type: i1$2.NzSingletonService }, { type: i2$1.Overlay }, { type: i0.Injector }] });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzMNComponent, NzMNContainerComponent, NzMNService, NzMessageComponent, NzMessageContainerComponent, NzMessageModule, NzMessageService };\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,qBAAqB;AACrD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,YAAY,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,eAAe;AACnJ,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,MAAM,EAAEC,IAAI,EAAEC,SAAS,QAAQ,gBAAgB;AACxD,OAAO,KAAKC,EAAE,MAAM,2BAA2B;AAC/C,SAASC,OAAO,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,OAAO,QAAQ,iBAAiB;AAC1E,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,OAAO,KAAKC,EAAE,MAAM,2BAA2B;AAC/C,SAASC,cAAc,QAAQ,2BAA2B;AAC1D,OAAO,KAAKC,IAAI,MAAM,oBAAoB;AAC1C,SAASC,YAAY,QAAQ,oBAAoB;AACjD,OAAO,KAAKC,IAAI,MAAM,6BAA6B;AACnD,OAAO,KAAKC,IAAI,MAAM,sBAAsB;AAAC,SAAAC,mCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAkHuD5B,EAAE,CAAA8B,SAAA,cAkHtB,CAAC;EAAA;AAAA;AAAA,SAAAC,mCAAAH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlHmB5B,EAAE,CAAA8B,SAAA,cAmH1B,CAAC;EAAA;AAAA;AAAA,SAAAE,mCAAAJ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAnHuB5B,EAAE,CAAA8B,SAAA,cAoHhB,CAAC;EAAA;AAAA;AAAA,SAAAG,mCAAAL,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IApHa5B,EAAE,CAAA8B,SAAA,cAqHxB,CAAC;EAAA;AAAA;AAAA,SAAAI,mCAAAN,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArHqB5B,EAAE,CAAA8B,SAAA,cAsH3B,CAAC;EAAA;AAAA;AAAA,SAAAK,2CAAAP,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAtHwB5B,EAAE,CAAAoC,uBAAA,EAwHpC,CAAC;IAxHiCpC,EAAE,CAAA8B,SAAA,cAyH/C,CAAC;IAzH4C9B,EAAE,CAAAqC,qBAAA;EAAA;EAAA,IAAAT,EAAA;IAAA,MAAAU,MAAA,GAAFtC,EAAE,CAAAuC,aAAA;IAAFvC,EAAE,CAAAwC,SAAA,CAyHvD,CAAC;IAzHoDxC,EAAE,CAAAyC,UAAA,cAAAH,MAAA,CAAAI,QAAA,CAAAC,OAAA,EAAF3C,EAAE,CAAA4C,cAyHvD,CAAC;EAAA;AAAA;AAAA,SAAAC,kDAAAjB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAkB,GAAA,GAzHoD9C,EAAE,CAAA+C,gBAAA;IAAF/C,EAAE,CAAAgD,cAAA,mBAuNhG,CAAC;IAvN6FhD,EAAE,CAAAiD,UAAA,uBAAAC,kFAAAC,MAAA;MAAFnD,EAAE,CAAAoD,aAAA,CAAAN,GAAA;MAAA,MAAAO,MAAA,GAAFrD,EAAE,CAAAuC,aAAA;MAAA,OAAFvC,EAAE,CAAAsD,WAAA,CAsNjFD,MAAA,CAAAE,MAAA,CAAAJ,MAAA,CAAAK,EAAA,EAAAL,MAAA,CAAAM,UAAmC,CAAC;IAAA,EAAC;IAtN0CzD,EAAE,CAAA0D,YAAA,CAuNnF,CAAC;EAAA;EAAA,IAAA9B,EAAA;IAAA,MAAA+B,WAAA,GAAA9B,GAAA,CAAA+B,SAAA;IAvNgF5D,EAAE,CAAAyC,UAAA,aAAAkB,WAqN1E,CAAC;EAAA;AAAA;AArU7B,IAAIE,aAAa,GAAG,CAAC;AACrB,MAAMC,WAAW,CAAC;EACdC,WAAWA,CAACC,kBAAkB,EAAEC,OAAO,EAAEC,QAAQ,EAAE;IAC/C,IAAI,CAACF,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EAC5B;EACAX,MAAMA,CAACC,EAAE,EAAE;IACP,IAAI,IAAI,CAACW,SAAS,EAAE;MAChB,IAAIX,EAAE,EAAE;QACJ,IAAI,CAACW,SAAS,CAACZ,MAAM,CAACC,EAAE,CAAC;MAC7B,CAAC,MACI;QACD,IAAI,CAACW,SAAS,CAACC,SAAS,CAAC,CAAC;MAC9B;IACJ;EACJ;EACAC,aAAaA,CAAA,EAAG;IACZ,OAAQ,GAAE,IAAI,CAACC,eAAgB,IAAGT,aAAa,EAAG,EAAC;EACvD;EACAU,aAAaA,CAACC,IAAI,EAAE;IAChB,IAAIC,iBAAiB,GAAG,IAAI,CAACT,kBAAkB,CAACU,mBAAmB,CAAC,IAAI,CAACJ,eAAe,CAAC;IACzF,IAAIG,iBAAiB,EAAE;MACnB,OAAOA,iBAAiB;IAC5B;IACA,MAAME,UAAU,GAAG,IAAI,CAACV,OAAO,CAACW,MAAM,CAAC;MACnCC,WAAW,EAAE,KAAK;MAClBC,cAAc,EAAE,IAAI,CAACb,OAAO,CAACc,gBAAgB,CAACC,IAAI,CAAC,CAAC;MACpDC,gBAAgB,EAAE,IAAI,CAAChB,OAAO,CAACiB,QAAQ,CAAC,CAAC,CAACC,MAAM,CAAC;IACrD,CAAC,CAAC;IACF,MAAMC,eAAe,GAAG,IAAIrF,eAAe,CAACyE,IAAI,EAAE,IAAI,EAAE,IAAI,CAACN,QAAQ,CAAC;IACtE,MAAMmB,YAAY,GAAGV,UAAU,CAACW,MAAM,CAACF,eAAe,CAAC;IACvD,MAAMG,cAAc,GAAGZ,UAAU,CAACa,WAAW;IAC7CD,cAAc,CAACE,KAAK,CAACC,MAAM,GAAG,MAAM;IACpC,IAAI,CAACjB,iBAAiB,EAAE;MACpB,IAAI,CAACN,SAAS,GAAGM,iBAAiB,GAAGY,YAAY,CAAC3C,QAAQ;MAC1D,IAAI,CAACsB,kBAAkB,CAAC2B,wBAAwB,CAAC,IAAI,CAACrB,eAAe,EAAEG,iBAAiB,CAAC;MACzF,IAAI,CAACN,SAAS,CAACyB,wBAAwB,CAACC,SAAS,CAAC,MAAM;QACpD,IAAI,CAAC1B,SAAS,GAAG2B,SAAS;QAC1B,IAAI,CAAC9B,kBAAkB,CAAC+B,0BAA0B,CAAC,IAAI,CAACzB,eAAe,CAAC;QACxEK,UAAU,CAACqB,OAAO,CAAC,CAAC;MACxB,CAAC,CAAC;IACN;IACA,OAAOvB,iBAAiB;EAC5B;AACJ;AACA,MAAMwB,sBAAsB,CAAC;EACzBlC,WAAWA,CAACmC,GAAG,EAAEC,eAAe,EAAE;IAC9B,IAAI,CAACD,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,yBAAyB,GAAG,IAAI3F,OAAO,CAAC,CAAC;IAC9C,IAAI,CAACkF,wBAAwB,GAAG,IAAI,CAACS,yBAAyB,CAACC,YAAY,CAAC,CAAC;IAC7E,IAAI,CAACC,QAAQ,GAAG,IAAI7F,OAAO,CAAC,CAAC;IAC7B,IAAI,CAAC8F,YAAY,CAAC,CAAC;EACvB;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,qBAAqB,CAAC,CAAC;EAChC;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACJ,QAAQ,CAACK,IAAI,CAAC,CAAC;IACpB,IAAI,CAACL,QAAQ,CAACM,QAAQ,CAAC,CAAC;EAC5B;EACAjC,MAAMA,CAACkC,IAAI,EAAE;IACT,MAAMpE,QAAQ,GAAG,IAAI,CAACqE,QAAQ,CAACD,IAAI,CAAC;IACpC,IAAI,IAAI,CAACV,SAAS,CAACY,MAAM,IAAI,IAAI,CAACC,MAAM,CAACC,UAAU,EAAE;MACjD,IAAI,CAACd,SAAS,GAAG,IAAI,CAACA,SAAS,CAACe,KAAK,CAAC,CAAC,CAAC;IAC5C;IACA,IAAI,CAACf,SAAS,GAAG,CAAC,GAAG,IAAI,CAACA,SAAS,EAAE1D,QAAQ,CAAC;IAC9C,IAAI,CAAC0E,cAAc,CAAC,CAAC;IACrB,OAAO1E,QAAQ;EACnB;EACAa,MAAMA,CAACC,EAAE,EAAEC,UAAU,GAAG,KAAK,EAAE;IAC3B,IAAI,CAAC2C,SAAS,CACTiB,GAAG,CAAC,CAAC3E,QAAQ,EAAE4E,KAAK,MAAM;MAAEA,KAAK;MAAE5E;IAAS,CAAC,CAAC,CAAC,CAC/C/B,MAAM,CAAC,CAAC;MAAE+B;IAAS,CAAC,KAAKA,QAAQ,CAAC6E,SAAS,KAAK/D,EAAE,CAAC,CACnDgE,OAAO,CAAC,CAAC;MAAEF,KAAK;MAAE5E;IAAS,CAAC,KAAK;MAClC,IAAI,CAAC0D,SAAS,CAACqB,MAAM,CAACH,KAAK,EAAE,CAAC,CAAC;MAC/B,IAAI,CAAClB,SAAS,GAAG,CAAC,GAAG,IAAI,CAACA,SAAS,CAAC;MACpC,IAAI,CAACsB,QAAQ,CAAChF,QAAQ,EAAEe,UAAU,CAAC;MACnC,IAAI,CAAC2D,cAAc,CAAC,CAAC;IACzB,CAAC,CAAC;IACF,IAAI,CAAC,IAAI,CAAChB,SAAS,CAACY,MAAM,EAAE;MACxB,IAAI,CAACW,qBAAqB,CAAC,CAAC;IAChC;EACJ;EACAvD,SAASA,CAAA,EAAG;IACR,IAAI,CAACgC,SAAS,CAACoB,OAAO,CAACI,CAAC,IAAI,IAAI,CAACF,QAAQ,CAACE,CAAC,EAAE,KAAK,CAAC,CAAC;IACpD,IAAI,CAACxB,SAAS,GAAG,EAAE;IACnB,IAAI,CAACgB,cAAc,CAAC,CAAC;IACrB,IAAI,CAACO,qBAAqB,CAAC,CAAC;EAChC;EACAZ,QAAQA,CAACrE,QAAQ,EAAE;IACfA,QAAQ,CAACmF,OAAO,GAAG,IAAI,CAACC,YAAY,CAACpF,QAAQ,CAACmF,OAAO,CAAC;IACtDnF,QAAQ,CAACqF,OAAO,GAAG,IAAIrH,OAAO,CAAC,CAAC;IAChC,OAAOgC,QAAQ;EACnB;EACAgF,QAAQA,CAAChF,QAAQ,EAAEe,UAAU,EAAE;IAC3Bf,QAAQ,CAACqF,OAAO,CAACnB,IAAI,CAACnD,UAAU,CAAC;IACjCf,QAAQ,CAACqF,OAAO,CAAClB,QAAQ,CAAC,CAAC;EAC/B;EACAc,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAACtB,yBAAyB,CAACO,IAAI,CAAC,CAAC;IACrC,IAAI,CAACP,yBAAyB,CAACQ,QAAQ,CAAC,CAAC;EAC7C;EACAO,cAAcA,CAAA,EAAG;IACb,IAAI,CAAClB,GAAG,CAAC8B,aAAa,CAAC,CAAC;EAC5B;EACAF,YAAYA,CAACD,OAAO,EAAE;IAClB,MAAM;MAAEI,UAAU;MAAEC,SAAS;MAAEC;IAAe,CAAC,GAAG,IAAI,CAAClB,MAAM;IAC7D,OAAO;MAAEgB,UAAU;MAAEC,SAAS;MAAEC,cAAc;MAAE,GAAGN;IAAQ,CAAC;EAChE;EACA;IAAS,IAAI,CAACO,IAAI,YAAAC,+BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFrC,sBAAsB,EAAhCjG,EAAE,CAAAuI,iBAAA,CAAgDvI,EAAE,CAACwI,iBAAiB,GAAtExI,EAAE,CAAAuI,iBAAA,CAAiFzH,EAAE,CAAC2H,eAAe;IAAA,CAA4C;EAAE;EACnP;IAAS,IAAI,CAACC,IAAI,kBAD8E1I,EAAE,CAAA2I,iBAAA;MAAAC,IAAA,EACJ3C;IAAsB,EAAiB;EAAE;AAC3I;AACA;EAAA,QAAA4C,SAAA,oBAAAA,SAAA,KAHoG7I,EAAE,CAAA8I,iBAAA,CAGX7C,sBAAsB,EAAc,CAAC;IACpH2C,IAAI,EAAE3I;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE2I,IAAI,EAAE5I,EAAE,CAACwI;EAAkB,CAAC,EAAE;IAAEI,IAAI,EAAE9H,EAAE,CAAC2H;EAAgB,CAAC,CAAC;AAAA;AAChG,MAAMM,aAAa,CAAC;EAChBhF,WAAWA,CAACmC,GAAG,EAAE;IACb,IAAI,CAACA,GAAG,GAAGA,GAAG;IACd,IAAI,CAAC8C,SAAS,GAAG,IAAI9I,YAAY,CAAC,CAAC;IACnC,IAAI,CAAC+I,qBAAqB,GAAG,IAAIvI,OAAO,CAAC,CAAC;IAC1C,IAAI,CAAC+C,UAAU,GAAG,KAAK;EAC3B;EACAgD,QAAQA,CAAA,EAAG;IACP,IAAI,CAACoB,OAAO,GAAG,IAAI,CAACnF,QAAQ,CAACmF,OAAO;IACpC,IAAI,IAAI,CAACA,OAAO,CAACK,SAAS,EAAE;MACxB,IAAI,CAACxF,QAAQ,CAACwG,KAAK,GAAG,OAAO;MAC7B,IAAI,CAACD,qBAAqB,CACrBE,IAAI,CAACxI,MAAM,CAACyI,KAAK,IAAIA,KAAK,CAACC,SAAS,KAAK,MAAM,IAAID,KAAK,CAACE,OAAO,KAAK,OAAO,CAAC,EAAE1I,IAAI,CAAC,CAAC,CAAC,CAAC,CACvFiF,SAAS,CAAC,MAAM;QACjB0D,YAAY,CAAC,IAAI,CAACC,UAAU,CAAC;QAC7B,IAAI,CAACR,SAAS,CAACpC,IAAI,CAAC;UAAEpD,EAAE,EAAE,IAAI,CAACd,QAAQ,CAAC6E,SAAS;UAAE9D,UAAU,EAAE,IAAI,CAACA;QAAW,CAAC,CAAC;MACrF,CAAC,CAAC;IACN;IACA,IAAI,CAACgG,SAAS,GAAG,IAAI,CAAC5B,OAAO,CAACI,UAAU,GAAG,CAAC;IAC5C,IAAI,IAAI,CAACwB,SAAS,EAAE;MAChB,IAAI,CAACC,SAAS,CAAC,CAAC;MAChB,IAAI,CAACC,iBAAiB,CAAC,CAAC;IAC5B;EACJ;EACAhD,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAAC8C,SAAS,EAAE;MAChB,IAAI,CAACG,iBAAiB,CAAC,CAAC;IAC5B;IACA,IAAI,CAACX,qBAAqB,CAACpC,QAAQ,CAAC,CAAC;EACzC;EACAgD,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACJ,SAAS,IAAI,IAAI,CAAC5B,OAAO,CAACM,cAAc,EAAE;MAC/C,IAAI,CAACyB,iBAAiB,CAAC,CAAC;MACxB,IAAI,CAACE,SAAS,CAAC,CAAC;IACpB;EACJ;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACN,SAAS,IAAI,IAAI,CAAC5B,OAAO,CAACM,cAAc,EAAE;MAC/C,IAAI,CAACwB,iBAAiB,CAAC,CAAC;IAC5B;EACJ;EACAK,OAAOA,CAACvG,UAAU,GAAG,KAAK,EAAE;IACxB,IAAI,CAACA,UAAU,GAAGA,UAAU;IAC5B,IAAI,IAAI,CAACoE,OAAO,CAACK,SAAS,EAAE;MACxB,IAAI,CAACxF,QAAQ,CAACwG,KAAK,GAAG,OAAO;MAC7B,IAAI,CAAChD,GAAG,CAAC8B,aAAa,CAAC,CAAC;MACxB,IAAI,CAACwB,UAAU,GAAGS,UAAU,CAAC,MAAM;QAC/B,IAAI,CAACT,UAAU,GAAG1D,SAAS;QAC3B,IAAI,CAACkD,SAAS,CAACpC,IAAI,CAAC;UAAEpD,EAAE,EAAE,IAAI,CAACd,QAAQ,CAAC6E,SAAS;UAAE9D;QAAW,CAAC,CAAC;MACpE,CAAC,EAAE,GAAG,CAAC;IACX,CAAC,MACI;MACD,IAAI,CAACuF,SAAS,CAACpC,IAAI,CAAC;QAAEpD,EAAE,EAAE,IAAI,CAACd,QAAQ,CAAC6E,SAAS;QAAE9D;MAAW,CAAC,CAAC;IACpE;EACJ;EACAiG,SAASA,CAAA,EAAG;IACR,IAAI,CAACQ,QAAQ,GAAG,IAAI,CAACrC,OAAO,CAACI,UAAU;IACvC,IAAI,CAACkC,gBAAgB,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;EACtC;EACAP,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAACL,SAAS,EAAE;MAChB,IAAI,CAACS,QAAQ,IAAIE,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAACF,gBAAgB;IACvD;EACJ;EACAR,iBAAiBA,CAAA,EAAG;IAChB,IAAI,IAAI,CAACO,QAAQ,GAAG,CAAC,EAAE;MACnB,IAAI,CAACN,iBAAiB,CAAC,CAAC;MACxB,IAAI,CAACU,UAAU,GAAGL,UAAU,CAAC,MAAM,IAAI,CAACD,OAAO,CAAC,CAAC,EAAE,IAAI,CAACE,QAAQ,CAAC;MACjE,IAAI,CAACC,gBAAgB,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;IACtC,CAAC,MACI;MACD,IAAI,CAACL,OAAO,CAAC,CAAC;IAClB;EACJ;EACAJ,iBAAiBA,CAAA,EAAG;IAChB,IAAI,IAAI,CAACU,UAAU,KAAK,IAAI,EAAE;MAC1Bf,YAAY,CAAC,IAAI,CAACe,UAAU,CAAC;MAC7B,IAAI,CAACA,UAAU,GAAGxE,SAAS;IAC/B;EACJ;EACA;IAAS,IAAI,CAACsC,IAAI,YAAAmC,sBAAAjC,CAAA;MAAA,YAAAA,CAAA,IAAwFS,aAAa,EAtFvB/I,EAAE,CAAAuI,iBAAA,CAsFuCvI,EAAE,CAACwI,iBAAiB;IAAA,CAA4C;EAAE;EAC3M;IAAS,IAAI,CAACE,IAAI,kBAvF8E1I,EAAE,CAAA2I,iBAAA;MAAAC,IAAA,EAuFJG;IAAa,EAAiB;EAAE;AAClI;AACA;EAAA,QAAAF,SAAA,oBAAAA,SAAA,KAzFoG7I,EAAE,CAAA8I,iBAAA,CAyFXC,aAAa,EAAc,CAAC;IAC3GH,IAAI,EAAE3I;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE2I,IAAI,EAAE5I,EAAE,CAACwI;EAAkB,CAAC,CAAC;AAAA;;AAElE;AACA;AACA;AACA;AACA,MAAMgC,kBAAkB,SAASzB,aAAa,CAAC;EAC3ChF,WAAWA,CAACmC,GAAG,EAAE;IACb,KAAK,CAACA,GAAG,CAAC;IACV,IAAI,CAAC8C,SAAS,GAAG,IAAI9I,YAAY,CAAC,CAAC;EACvC;EACA;IAAS,IAAI,CAACkI,IAAI,YAAAqC,2BAAAnC,CAAA;MAAA,YAAAA,CAAA,IAAwFkC,kBAAkB,EAtG5BxK,EAAE,CAAAuI,iBAAA,CAsG4CvI,EAAE,CAACwI,iBAAiB;IAAA,CAA4C;EAAE;EAChN;IAAS,IAAI,CAACkC,IAAI,kBAvG8E1K,EAAE,CAAA2K,iBAAA;MAAA/B,IAAA,EAuGJ4B,kBAAkB;MAAAI,SAAA;MAAAC,MAAA;QAAAnI,QAAA;MAAA;MAAAoI,OAAA;QAAA9B,SAAA;MAAA;MAAA+B,QAAA;MAAAC,UAAA;MAAAC,QAAA,GAvGhBjL,EAAE,CAAAkL,0BAAA,EAAFlL,EAAE,CAAAmL,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAA5J,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF5B,EAAE,CAAAgD,cAAA,YA8GlG,CAAC;UA9G+FhD,EAAE,CAAAiD,UAAA,gCAAAwI,uEAAAtI,MAAA;YAAA,OA2G1EtB,GAAA,CAAAoH,qBAAA,CAAArC,IAAA,CAAAzD,MAAiC,CAAC;UAAA,EAAC,wBAAAuI,sDAAA;YAAA,OAC3C7J,GAAA,CAAAgI,OAAA,CAAQ,CAAC;UAAA,EAAC,wBAAA8B,sDAAA;YAAA,OACV9J,GAAA,CAAAkI,OAAA,CAAQ,CAAC;UAAA,EAAC;UA7GsE/J,EAAE,CAAAgD,cAAA,YA+GzD,CAAC,YAC4C,CAAC;UAhHShD,EAAE,CAAAoC,uBAAA,KAiHpD,CAAC;UAjHiDpC,EAAE,CAAA4L,UAAA,IAAAjK,kCAAA,iBAkH7B,CAAC,IAAAI,kCAAA,iBACL,CAAC,IAAAC,kCAAA,iBACS,CAAC,IAAAC,kCAAA,iBACT,CAAC,IAAAC,kCAAA,iBACJ,CAAC;UAtH+BlC,EAAE,CAAAqC,qBAAA;UAAFrC,EAAE,CAAA4L,UAAA,IAAAzJ,0CAAA,yBAwHpC,CAAC;UAxHiCnC,EAAE,CAAA0D,YAAA,CA2HzF,CAAC,CACH,CAAC,CACH,CAAC;QAAA;QAAA,IAAA9B,EAAA;UA7H0F5B,EAAE,CAAAyC,UAAA,kBAAAZ,GAAA,CAAAa,QAAA,CAAAwG,KA0GjE,CAAC;UA1G8DlJ,EAAE,CAAAwC,SAAA,EAgHb,CAAC;UAhHUxC,EAAE,CAAAyC,UAAA,6BAAAZ,GAAA,CAAAa,QAAA,CAAAkG,IAgHb,CAAC;UAhHU5I,EAAE,CAAAwC,SAAA,CAiHrD,CAAC;UAjHkDxC,EAAE,CAAAyC,UAAA,aAAAZ,GAAA,CAAAa,QAAA,CAAAkG,IAiHrD,CAAC;UAjHkD5I,EAAE,CAAAwC,SAAA,CAkH7D,CAAC;UAlH0DxC,EAAE,CAAAyC,UAAA,0BAkH7D,CAAC;UAlH0DzC,EAAE,CAAAwC,SAAA,CAmHhE,CAAC;UAnH6DxC,EAAE,CAAAyC,UAAA,uBAmHhE,CAAC;UAnH6DzC,EAAE,CAAAwC,SAAA,CAoH7D,CAAC;UApH0DxC,EAAE,CAAAyC,UAAA,0BAoH7D,CAAC;UApH0DzC,EAAE,CAAAwC,SAAA,CAqH/D,CAAC;UArH4DxC,EAAE,CAAAyC,UAAA,wBAqH/D,CAAC;UArH4DzC,EAAE,CAAAwC,SAAA,CAsH7D,CAAC;UAtH0DxC,EAAE,CAAAyC,UAAA,0BAsH7D,CAAC;UAtH0DzC,EAAE,CAAAwC,SAAA,CAwHtC,CAAC;UAxHmCxC,EAAE,CAAAyC,UAAA,2BAAAZ,GAAA,CAAAa,QAAA,CAAAC,OAwHtC,CAAC;QAAA;MAAA;MAAAkJ,YAAA,GAMF9K,OAAO,EAAoFC,QAAQ,EAA6EC,YAAY,EAAoFO,YAAY,EAA+BD,IAAI,CAACuK,eAAe,EAAgKxK,cAAc,EAA+BD,EAAE,CAAC0K,+BAA+B;MAAAC,aAAA;MAAAlF,IAAA;QAAAmF,SAAA,EAAkK,CAAC7K,YAAY;MAAC;MAAA8K,eAAA;IAAA,EAAiG;EAAE;AACh5B;AACA;EAAA,QAAArD,SAAA,oBAAAA,SAAA,KAhIoG7I,EAAE,CAAA8I,iBAAA,CAgIX0B,kBAAkB,EAAc,CAAC;IAChH5B,IAAI,EAAEzI,SAAS;IACfgM,IAAI,EAAE,CAAC;MACCD,eAAe,EAAE9L,uBAAuB,CAACgM,MAAM;MAC/CJ,aAAa,EAAE3L,iBAAiB,CAACgM,IAAI;MACrCC,QAAQ,EAAE,YAAY;MACtBvB,QAAQ,EAAE,WAAW;MACrBwB,mBAAmB,EAAE,KAAK;MAC1BC,UAAU,EAAE,CAACpL,YAAY,CAAC;MAC1BmK,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiBkB,OAAO,EAAE,CAAC1L,OAAO,EAAEC,QAAQ,EAAEC,YAAY,EAAEO,YAAY,EAAEF,cAAc,CAAC;MACxE0J,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEpC,IAAI,EAAE5I,EAAE,CAACwI;EAAkB,CAAC,CAAC,EAAkB;IAAE9F,QAAQ,EAAE,CAAC;MACjFkG,IAAI,EAAEtI;IACV,CAAC,CAAC;IAAE0I,SAAS,EAAE,CAAC;MACZJ,IAAI,EAAErI;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMmM,wBAAwB,GAAG,SAAS;AAC1C,MAAMC,yBAAyB,GAAG;EAC9BzE,SAAS,EAAE,IAAI;EACfD,UAAU,EAAE,IAAI;EAChBf,UAAU,EAAE,CAAC;EACbiB,cAAc,EAAE,IAAI;EACpByE,KAAK,EAAE,EAAE;EACTC,WAAW,EAAE;AACjB,CAAC;AACD,MAAMC,2BAA2B,SAAS7G,sBAAsB,CAAC;EAC7DlC,WAAWA,CAACmC,GAAG,EAAEC,eAAe,EAAE;IAC9B,KAAK,CAACD,GAAG,EAAEC,eAAe,CAAC;IAC3B,IAAI,CAAC4G,GAAG,GAAG,KAAK;IAChB,MAAM9F,MAAM,GAAG,IAAI,CAACd,eAAe,CAAC6G,qBAAqB,CAACN,wBAAwB,CAAC;IACnF,IAAI,CAACK,GAAG,GAAG9F,MAAM,EAAE4F,WAAW,IAAI,KAAK;EAC3C;EACAnG,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAACP,eAAe,CACf8G,gCAAgC,CAACP,wBAAwB,CAAC,CAC1DvD,IAAI,CAACtI,SAAS,CAAC,IAAI,CAAC0F,QAAQ,CAAC,CAAC,CAC9BV,SAAS,CAAC,MAAM;MACjB,IAAI,CAACW,YAAY,CAAC,CAAC;MACnB,MAAMS,MAAM,GAAG,IAAI,CAACd,eAAe,CAAC6G,qBAAqB,CAACN,wBAAwB,CAAC;MACnF,IAAIzF,MAAM,EAAE;QACR,MAAM;UAAE4F;QAAY,CAAC,GAAG5F,MAAM;QAC9B,IAAI,CAAC8F,GAAG,GAAGF,WAAW,IAAI,IAAI,CAACE,GAAG;MACtC;IACJ,CAAC,CAAC;EACN;EACAvG,YAAYA,CAAA,EAAG;IACX,IAAI,CAACS,MAAM,GAAG;MACV,GAAG0F,yBAAyB;MAC5B,GAAG,IAAI,CAAC1F,MAAM;MACd,GAAG,IAAI,CAACd,eAAe,CAAC6G,qBAAqB,CAACN,wBAAwB;IAC1E,CAAC;IACD,IAAI,CAACQ,GAAG,GAAG/L,UAAU,CAAC,IAAI,CAAC8F,MAAM,CAAC2F,KAAK,CAAC;IACxC,IAAI,CAAC1G,GAAG,CAACiH,YAAY,CAAC,CAAC;EAC3B;EACA;IAAS,IAAI,CAAC/E,IAAI,YAAAgF,oCAAA9E,CAAA;MAAA,YAAAA,CAAA,IAAwFwE,2BAA2B,EAhNrC9M,EAAE,CAAAuI,iBAAA,CAgNqDvI,EAAE,CAACwI,iBAAiB,GAhN3ExI,EAAE,CAAAuI,iBAAA,CAgNsFzH,EAAE,CAAC2H,eAAe;IAAA,CAA4C;EAAE;EACxP;IAAS,IAAI,CAACiC,IAAI,kBAjN8E1K,EAAE,CAAA2K,iBAAA;MAAA/B,IAAA,EAiNJkE,2BAA2B;MAAAlC,SAAA;MAAAG,QAAA;MAAAC,UAAA;MAAAC,QAAA,GAjNzBjL,EAAE,CAAAkL,0BAAA,EAAFlL,EAAE,CAAAmL,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA8B,qCAAAzL,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF5B,EAAE,CAAAgD,cAAA,YAkNhB,CAAC;UAlNahD,EAAE,CAAA4L,UAAA,IAAA/I,iDAAA,uBAuNhG,CAAC;UAvN6F7C,EAAE,CAAA0D,YAAA,CAwN7F,CAAC;QAAA;QAAA,IAAA9B,EAAA;UAxN0F5B,EAAE,CAAAsN,WAAA,QAAAzL,GAAA,CAAAqL,GAkNjB,CAAC;UAlNclN,EAAE,CAAAuN,WAAA,oBAAA1L,GAAA,CAAAkL,GAAA,UAkNnC,CAAC;UAlNgC/M,EAAE,CAAAwC,SAAA,CAoN9D,CAAC;UApN2DxC,EAAE,CAAAyC,UAAA,YAAAZ,GAAA,CAAAuE,SAoN9D,CAAC;QAAA;MAAA;MAAAyF,YAAA,GAKsBrB,kBAAkB,EAA8HtJ,OAAO;MAAA8K,aAAA;MAAAE,eAAA;IAAA,EAAwL;EAAE;AAChZ;AACA;EAAA,QAAArD,SAAA,oBAAAA,SAAA,KA3NoG7I,EAAE,CAAA8I,iBAAA,CA2NXgE,2BAA2B,EAAc,CAAC;IACzHlE,IAAI,EAAEzI,SAAS;IACfgM,IAAI,EAAE,CAAC;MACCD,eAAe,EAAE9L,uBAAuB,CAACgM,MAAM;MAC/CJ,aAAa,EAAE3L,iBAAiB,CAACgM,IAAI;MACrCC,QAAQ,EAAE,sBAAsB;MAChCvB,QAAQ,EAAE,oBAAoB;MAC9BwB,mBAAmB,EAAE,KAAK;MAC1BhB,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiBkB,OAAO,EAAE,CAACjC,kBAAkB,EAAEtJ,OAAO,CAAC;MACtC8J,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEpC,IAAI,EAAE5I,EAAE,CAACwI;EAAkB,CAAC,EAAE;IAAEI,IAAI,EAAE9H,EAAE,CAAC2H;EAAgB,CAAC,CAAC;AAAA;;AAEhG;AACA;AACA;AACA;AACA,MAAM+E,eAAe,CAAC;EAClB;IAAS,IAAI,CAACpF,IAAI,YAAAqF,wBAAAnF,CAAA;MAAA,YAAAA,CAAA,IAAwFkF,eAAe;IAAA,CAAkD;EAAE;EAC7K;IAAS,IAAI,CAACE,IAAI,kBAvP8E1N,EAAE,CAAA2N,gBAAA;MAAA/E,IAAA,EAuPS4E,eAAe;MAAAf,OAAA,GAAYK,2BAA2B,EAAEtC,kBAAkB;IAAA,EAAI;EAAE;EAC3L;IAAS,IAAI,CAACoD,IAAI,kBAxP8E5N,EAAE,CAAA6N,gBAAA;MAAApB,OAAA,GAwPoCK,2BAA2B,EAAEtC,kBAAkB;IAAA,EAAI;EAAE;AAC/L;AACA;EAAA,QAAA3B,SAAA,oBAAAA,SAAA,KA1PoG7I,EAAE,CAAA8I,iBAAA,CA0PX0E,eAAe,EAAc,CAAC;IAC7G5E,IAAI,EAAEpI,QAAQ;IACd2L,IAAI,EAAE,CAAC;MACCM,OAAO,EAAE,CAACK,2BAA2B,EAAEtC,kBAAkB;IAC7D,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMsD,gBAAgB,SAAShK,WAAW,CAAC;EACvCC,WAAWA,CAACC,kBAAkB,EAAEC,OAAO,EAAEC,QAAQ,EAAE;IAC/C,KAAK,CAACF,kBAAkB,EAAEC,OAAO,EAAEC,QAAQ,CAAC;IAC5C,IAAI,CAACI,eAAe,GAAG,UAAU;EACrC;EACAyJ,OAAOA,CAACpL,OAAO,EAAEkF,OAAO,EAAE;IACtB,OAAO,IAAI,CAACmG,cAAc,CAAC;MAAEpF,IAAI,EAAE,SAAS;MAAEjG;IAAQ,CAAC,EAAEkF,OAAO,CAAC;EACrE;EACAoG,KAAKA,CAACtL,OAAO,EAAEkF,OAAO,EAAE;IACpB,OAAO,IAAI,CAACmG,cAAc,CAAC;MAAEpF,IAAI,EAAE,OAAO;MAAEjG;IAAQ,CAAC,EAAEkF,OAAO,CAAC;EACnE;EACAqG,IAAIA,CAACvL,OAAO,EAAEkF,OAAO,EAAE;IACnB,OAAO,IAAI,CAACmG,cAAc,CAAC;MAAEpF,IAAI,EAAE,MAAM;MAAEjG;IAAQ,CAAC,EAAEkF,OAAO,CAAC;EAClE;EACAsG,OAAOA,CAACxL,OAAO,EAAEkF,OAAO,EAAE;IACtB,OAAO,IAAI,CAACmG,cAAc,CAAC;MAAEpF,IAAI,EAAE,SAAS;MAAEjG;IAAQ,CAAC,EAAEkF,OAAO,CAAC;EACrE;EACAuG,OAAOA,CAACzL,OAAO,EAAEkF,OAAO,EAAE;IACtB,OAAO,IAAI,CAACmG,cAAc,CAAC;MAAEpF,IAAI,EAAE,SAAS;MAAEjG;IAAQ,CAAC,EAAEkF,OAAO,CAAC;EACrE;EACAjD,MAAMA,CAACgE,IAAI,EAAEjG,OAAO,EAAEkF,OAAO,EAAE;IAC3B,OAAO,IAAI,CAACmG,cAAc,CAAC;MAAEpF,IAAI;MAAEjG;IAAQ,CAAC,EAAEkF,OAAO,CAAC;EAC1D;EACAmG,cAAcA,CAACK,OAAO,EAAExG,OAAO,EAAE;IAC7B,IAAI,CAAC1D,SAAS,GAAG,IAAI,CAACI,aAAa,CAACuI,2BAA2B,CAAC;IAChE,OAAO,IAAI,CAAC3I,SAAS,CAACS,MAAM,CAAC;MACzB,GAAGyJ,OAAO;MACV,GAAG;QACCC,SAAS,EAAE,IAAIlE,IAAI,CAAC,CAAC;QACrB7C,SAAS,EAAE,IAAI,CAAClD,aAAa,CAAC,CAAC;QAC/BwD;MACJ;IACJ,CAAC,CAAC;EACN;EACA;IAAS,IAAI,CAACO,IAAI,YAAAmG,yBAAAjG,CAAA;MAAA,YAAAA,CAAA,IAAwFwF,gBAAgB,EAnS1B9N,EAAE,CAAAwO,QAAA,CAmS0C/M,IAAI,CAACgN,kBAAkB,GAnSnEzO,EAAE,CAAAwO,QAAA,CAmS8E9M,IAAI,CAACgN,OAAO,GAnS5F1O,EAAE,CAAAwO,QAAA,CAmSuGxO,EAAE,CAAC2O,QAAQ;IAAA,CAA6C;EAAE;EACnQ;IAAS,IAAI,CAACC,KAAK,kBApS6E5O,EAAE,CAAA6O,kBAAA;MAAAC,KAAA,EAoSYhB,gBAAgB;MAAAiB,OAAA,EAAhBjB,gBAAgB,CAAA1F,IAAA;MAAA4G,UAAA,EAAc;IAAM,EAAG;EAAE;AAC3J;AACA;EAAA,QAAAnG,SAAA,oBAAAA,SAAA,KAtSoG7I,EAAE,CAAA8I,iBAAA,CAsSXgF,gBAAgB,EAAc,CAAC;IAC9GlF,IAAI,EAAEnI,UAAU;IAChB0L,IAAI,EAAE,CAAC;MACC6C,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEpG,IAAI,EAAEnH,IAAI,CAACgN;EAAmB,CAAC,EAAE;IAAE7F,IAAI,EAAElH,IAAI,CAACgN;EAAQ,CAAC,EAAE;IAAE9F,IAAI,EAAE5I,EAAE,CAAC2O;EAAS,CAAC,CAAC;AAAA;;AAEpH;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAAS5F,aAAa,EAAE9C,sBAAsB,EAAEnC,WAAW,EAAE0G,kBAAkB,EAAEsC,2BAA2B,EAAEU,eAAe,EAAEM,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}