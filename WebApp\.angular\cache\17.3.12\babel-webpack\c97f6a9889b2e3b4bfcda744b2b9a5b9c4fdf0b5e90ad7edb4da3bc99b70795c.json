{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./add-or-edit-prompt-library.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./add-or-edit-prompt-library.component.css?ngResource\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NzModalModule } from 'ng-zorro-antd/modal';\nimport { NzFormModule } from 'ng-zorro-antd/form';\nimport { NzInputModule } from 'ng-zorro-antd/input';\nimport { NzSwitchModule } from 'ng-zorro-antd/switch';\nimport { NzButtonModule } from 'ng-zorro-antd/button';\nimport { NzSelectModule } from 'ng-zorro-antd/select';\nimport { NzModalRef } from 'ng-zorro-antd/modal';\nimport { WorkspaceServiceProxy } from '../../../../shared/service-proxies/service-proxies';\nimport { AuthService } from '../../../../shared/services/auth.service';\nimport { Router } from '@angular/router';\nlet AddOrEditPromptLibraryComponent = class AddOrEditPromptLibraryComponent {\n  constructor(modalRef, workspaceService, authService, router) {\n    this.modalRef = modalRef;\n    this.workspaceService = workspaceService;\n    this.authService = authService;\n    this.router = router;\n    this.promptData = {\n      id: 0,\n      prompt: '',\n      shortMessage: '',\n      workspaceName: ''\n    };\n    this.workspaces = [];\n    this.workspaceName = '';\n  }\n  ngOnInit() {\n    let router = this.router.url.split('/');\n    if (router[1] === 'workspaces') {\n      this.workspaceName = router[2];\n      this.workspaceName = decodeURIComponent(this.workspaceName);\n    } else {\n      this.workspaceName = undefined;\n    }\n    const data = this.modalRef.getConfig().nzData;\n    if (data && data.promptData) {\n      this.promptData = {\n        ...data.promptData\n      };\n    }\n    // If we're in a workspace context, set the workspace name for the prompt\n    if (this.workspaceName) {\n      this.promptData.workspaceName = this.workspaceName;\n    }\n    this.loadWorkspaces();\n  }\n  loadWorkspaces() {\n    if (this.authService.isAdmin()) {\n      this.workspaceService.getAll().subscribe(res => {\n        this.workspaces = res;\n      });\n    } else {\n      this.workspaceService.getWorkspacesByUserEmail().subscribe(res => {\n        this.workspaces = res;\n      });\n    }\n  }\n  onSubmit() {\n    if (this.promptData.prompt) {\n      // Ensure workspace name is set if we're in a workspace context\n      if (this.workspaceName && !this.promptData.workspaceName) {\n        this.promptData.workspaceName = this.workspaceName;\n      }\n      this.modalRef.close(this.promptData);\n    }\n  }\n  onCancel() {\n    this.modalRef.close();\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: NzModalRef\n    }, {\n      type: WorkspaceServiceProxy\n    }, {\n      type: AuthService\n    }, {\n      type: Router\n    }];\n  }\n};\nAddOrEditPromptLibraryComponent = __decorate([Component({\n  selector: 'app-add-or-edit-prompt-library',\n  standalone: true,\n  imports: [CommonModule, FormsModule, NzModalModule, NzFormModule, NzInputModule, NzSwitchModule, NzButtonModule, NzSelectModule],\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], AddOrEditPromptLibraryComponent);\nexport { AddOrEditPromptLibraryComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "FormsModule", "NzModalModule", "NzFormModule", "NzInputModule", "NzSwitchModule", "NzButtonModule", "NzSelectModule", "NzModalRef", "WorkspaceServiceProxy", "AuthService", "Router", "AddOrEditPromptLibraryComponent", "constructor", "modalRef", "workspaceService", "authService", "router", "promptData", "id", "prompt", "shortMessage", "workspaceName", "workspaces", "ngOnInit", "url", "split", "decodeURIComponent", "undefined", "data", "getConfig", "nzData", "loadWorkspaces", "isAdmin", "getAll", "subscribe", "res", "getWorkspacesByUserEmail", "onSubmit", "close", "onCancel", "__decorate", "selector", "standalone", "imports", "template", "__NG_CLI_RESOURCE__0"], "sources": ["C:\\Users\\<USER>\\source\\ai-hub\\WebApp\\src\\app\\admin\\prompts-library\\add-or-edit-prompt-library\\add-or-edit-prompt-library.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, NgForm } from '@angular/forms';\nimport { NzModalModule } from 'ng-zorro-antd/modal';\nimport { NzFormModule } from 'ng-zorro-antd/form';\nimport { NzInputModule } from 'ng-zorro-antd/input';\nimport { NzSwitchModule } from 'ng-zorro-antd/switch';\nimport { NzButtonModule } from 'ng-zorro-antd/button';\nimport { NzSelectModule } from 'ng-zorro-antd/select';\nimport { NzModalRef } from 'ng-zorro-antd/modal';\nimport { WorkspaceServiceProxy } from '../../../../shared/service-proxies/service-proxies';\nimport { AuthService } from '../../../../shared/services/auth.service';\nimport { Router } from '@angular/router';\n@Component({\n  selector: 'app-add-or-edit-prompt-library',\n  standalone: true,\n  imports: [\n    CommonModule,\n    FormsModule,\n    NzModalModule,\n    NzFormModule,\n    NzInputModule,\n    NzSwitchModule,\n    NzButtonModule,\n    NzSelectModule\n  ],\n  templateUrl: './add-or-edit-prompt-library.component.html',\n  styleUrls: ['./add-or-edit-prompt-library.component.css']\n})\nexport class AddOrEditPromptLibraryComponent implements OnInit {\n  promptData: any = {\n    id: 0,\n    prompt: '',\n    shortMessage: '',\n    workspaceName: '',\n  };\n  workspaces: any[] = [];\n  workspaceName: string | undefined = '';\n  constructor(\n    private modalRef: NzModalRef,\n    private workspaceService: WorkspaceServiceProxy,\n    public authService: AuthService,\n    private router: Router\n  ) { }\n\n  ngOnInit() {\n    let router = this.router.url.split('/');\n    if (router[1] === 'workspaces') {\n      this.workspaceName = router[2];\n      this.workspaceName = decodeURIComponent(this.workspaceName);\n    } else {\n      this.workspaceName = undefined;\n    }\n\n    const data = this.modalRef.getConfig().nzData;\n    if (data && data.promptData) {\n      this.promptData = { ...data.promptData };\n    }\n\n    // If we're in a workspace context, set the workspace name for the prompt\n    if (this.workspaceName) {\n      this.promptData.workspaceName = this.workspaceName;\n    }\n\n    this.loadWorkspaces();\n  }\n\n  loadWorkspaces() {\n    if (this.authService.isAdmin()) {\n      this.workspaceService.getAll().subscribe((res: any) => {\n        this.workspaces = res;\n      });\n    } else {\n      this.workspaceService.getWorkspacesByUserEmail().subscribe((res: any) => {\n        this.workspaces = res;\n      });\n    }\n  }\n\n  onSubmit() {\n    if (this.promptData.prompt) {\n      // Ensure workspace name is set if we're in a workspace context\n      if (this.workspaceName && !this.promptData.workspaceName) {\n        this.promptData.workspaceName = this.workspaceName;\n      }\n      this.modalRef.close(this.promptData);\n    }\n  }\n\n  onCancel() {\n    this.modalRef.close();\n  }\n}\n"], "mappings": ";;;AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAgB,gBAAgB;AACpD,SAASC,aAAa,QAAQ,qBAAqB;AACnD,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,aAAa,QAAQ,qBAAqB;AACnD,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,UAAU,QAAQ,qBAAqB;AAChD,SAASC,qBAAqB,QAAQ,oDAAoD;AAC1F,SAASC,WAAW,QAAQ,0CAA0C;AACtE,SAASC,MAAM,QAAQ,iBAAiB;AAiBjC,IAAMC,+BAA+B,GAArC,MAAMA,+BAA+B;EAS1CC,YACUC,QAAoB,EACpBC,gBAAuC,EACxCC,WAAwB,EACvBC,MAAc;IAHd,KAAAH,QAAQ,GAARA,QAAQ;IACR,KAAAC,gBAAgB,GAAhBA,gBAAgB;IACjB,KAAAC,WAAW,GAAXA,WAAW;IACV,KAAAC,MAAM,GAANA,MAAM;IAZhB,KAAAC,UAAU,GAAQ;MAChBC,EAAE,EAAE,CAAC;MACLC,MAAM,EAAE,EAAE;MACVC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE;KAChB;IACD,KAAAC,UAAU,GAAU,EAAE;IACtB,KAAAD,aAAa,GAAuB,EAAE;EAMlC;EAEJE,QAAQA,CAAA;IACN,IAAIP,MAAM,GAAG,IAAI,CAACA,MAAM,CAACQ,GAAG,CAACC,KAAK,CAAC,GAAG,CAAC;IACvC,IAAIT,MAAM,CAAC,CAAC,CAAC,KAAK,YAAY,EAAE;MAC9B,IAAI,CAACK,aAAa,GAAGL,MAAM,CAAC,CAAC,CAAC;MAC9B,IAAI,CAACK,aAAa,GAAGK,kBAAkB,CAAC,IAAI,CAACL,aAAa,CAAC;KAC5D,MAAM;MACL,IAAI,CAACA,aAAa,GAAGM,SAAS;;IAGhC,MAAMC,IAAI,GAAG,IAAI,CAACf,QAAQ,CAACgB,SAAS,EAAE,CAACC,MAAM;IAC7C,IAAIF,IAAI,IAAIA,IAAI,CAACX,UAAU,EAAE;MAC3B,IAAI,CAACA,UAAU,GAAG;QAAE,GAAGW,IAAI,CAACX;MAAU,CAAE;;IAG1C;IACA,IAAI,IAAI,CAACI,aAAa,EAAE;MACtB,IAAI,CAACJ,UAAU,CAACI,aAAa,GAAG,IAAI,CAACA,aAAa;;IAGpD,IAAI,CAACU,cAAc,EAAE;EACvB;EAEAA,cAAcA,CAAA;IACZ,IAAI,IAAI,CAAChB,WAAW,CAACiB,OAAO,EAAE,EAAE;MAC9B,IAAI,CAAClB,gBAAgB,CAACmB,MAAM,EAAE,CAACC,SAAS,CAAEC,GAAQ,IAAI;QACpD,IAAI,CAACb,UAAU,GAAGa,GAAG;MACvB,CAAC,CAAC;KACH,MAAM;MACL,IAAI,CAACrB,gBAAgB,CAACsB,wBAAwB,EAAE,CAACF,SAAS,CAAEC,GAAQ,IAAI;QACtE,IAAI,CAACb,UAAU,GAAGa,GAAG;MACvB,CAAC,CAAC;;EAEN;EAEAE,QAAQA,CAAA;IACN,IAAI,IAAI,CAACpB,UAAU,CAACE,MAAM,EAAE;MAC1B;MACA,IAAI,IAAI,CAACE,aAAa,IAAI,CAAC,IAAI,CAACJ,UAAU,CAACI,aAAa,EAAE;QACxD,IAAI,CAACJ,UAAU,CAACI,aAAa,GAAG,IAAI,CAACA,aAAa;;MAEpD,IAAI,CAACR,QAAQ,CAACyB,KAAK,CAAC,IAAI,CAACrB,UAAU,CAAC;;EAExC;EAEAsB,QAAQA,CAAA;IACN,IAAI,CAAC1B,QAAQ,CAACyB,KAAK,EAAE;EACvB;;;;;;;;;;;;;AA9DW3B,+BAA+B,GAAA6B,UAAA,EAhB3C1C,SAAS,CAAC;EACT2C,QAAQ,EAAE,gCAAgC;EAC1CC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACP5C,YAAY,EACZC,WAAW,EACXC,aAAa,EACbC,YAAY,EACZC,aAAa,EACbC,cAAc,EACdC,cAAc,EACdC,cAAc,CACf;EACDsC,QAAA,EAAAC,oBAA0D;;CAE3D,CAAC,C,EACWlC,+BAA+B,CA+D3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}