{"ast": null, "code": "import { rgbToHsv, rgbToHex, inputToRGB } from '@ctrl/tinycolor';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst statusColors = ['success', 'processing', 'error', 'default', 'warning'];\nconst presetColors = ['pink', 'red', 'yellow', 'orange', 'cyan', 'green', 'blue', 'purple', 'geekblue', 'magenta', 'volcano', 'gold', 'lime'];\nfunction isPresetColor(color) {\n  return presetColors.indexOf(color) !== -1;\n}\nfunction isStatusColor(color) {\n  return statusColors.indexOf(color) !== -1;\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/**\n * Sync from @ant-design/colors(https://github.com/ant-design/ant-design-colors)\n */\nconst hueStep = 2; // 色相阶梯\nconst saturationStep = 0.16; // 饱和度阶梯，浅色部分\nconst saturationStep2 = 0.05; // 饱和度阶梯，深色部分\nconst brightnessStep1 = 0.05; // 亮度阶梯，浅色部分\nconst brightnessStep2 = 0.15; // 亮度阶梯，深色部分\nconst lightColorCount = 5; // 浅色数量，主色上\nconst darkColorCount = 4; // 深色数量，主色下\n// 暗色主题颜色映射关系表\nconst darkColorMap = [{\n  index: 7,\n  opacity: 0.15\n}, {\n  index: 6,\n  opacity: 0.25\n}, {\n  index: 5,\n  opacity: 0.3\n}, {\n  index: 5,\n  opacity: 0.45\n}, {\n  index: 5,\n  opacity: 0.65\n}, {\n  index: 5,\n  opacity: 0.85\n}, {\n  index: 4,\n  opacity: 0.9\n}, {\n  index: 3,\n  opacity: 0.95\n}, {\n  index: 2,\n  opacity: 0.97\n}, {\n  index: 1,\n  opacity: 0.98\n}];\n// Wrapper function ported from TinyColor.prototype.toHsv\n// Keep it here because of `hsv.h * 360`\nfunction toHsv({\n  r,\n  g,\n  b\n}) {\n  const hsv = rgbToHsv(r, g, b);\n  return {\n    h: hsv.h * 360,\n    s: hsv.s,\n    v: hsv.v\n  };\n}\n// Wrapper function ported from TinyColor.prototype.toHexString\n// Keep it here because of the prefix `#`\nfunction toHex({\n  r,\n  g,\n  b\n}) {\n  return `#${rgbToHex(r, g, b, false)}`;\n}\n// Wrapper function ported from TinyColor.prototype.mix, not treeshakable.\n// Amount in range [0, 1]\n// Assume color1 & color2 has no alpha, since the following src code did so.\nfunction mix(rgb1, rgb2, amount) {\n  const p = amount / 100;\n  const rgb = {\n    r: (rgb2.r - rgb1.r) * p + rgb1.r,\n    g: (rgb2.g - rgb1.g) * p + rgb1.g,\n    b: (rgb2.b - rgb1.b) * p + rgb1.b\n  };\n  return rgb;\n}\nfunction getHue(hsv, i, light) {\n  let hue;\n  // 根据色相不同，色相转向不同\n  if (Math.round(hsv.h) >= 60 && Math.round(hsv.h) <= 240) {\n    hue = light ? Math.round(hsv.h) - hueStep * i : Math.round(hsv.h) + hueStep * i;\n  } else {\n    hue = light ? Math.round(hsv.h) + hueStep * i : Math.round(hsv.h) - hueStep * i;\n  }\n  if (hue < 0) {\n    hue += 360;\n  } else if (hue >= 360) {\n    hue -= 360;\n  }\n  return hue;\n}\nfunction getSaturation(hsv, i, light) {\n  // grey color don't change saturation\n  if (hsv.h === 0 && hsv.s === 0) {\n    return hsv.s;\n  }\n  let saturation;\n  if (light) {\n    saturation = hsv.s - saturationStep * i;\n  } else if (i === darkColorCount) {\n    saturation = hsv.s + saturationStep;\n  } else {\n    saturation = hsv.s + saturationStep2 * i;\n  }\n  // 边界值修正\n  if (saturation > 1) {\n    saturation = 1;\n  }\n  // 第一格的 s 限制在 0.06-0.1 之间\n  if (light && i === lightColorCount && saturation > 0.1) {\n    saturation = 0.1;\n  }\n  if (saturation < 0.06) {\n    saturation = 0.06;\n  }\n  return Number(saturation.toFixed(2));\n}\nfunction getValue(hsv, i, light) {\n  let value;\n  if (light) {\n    value = hsv.v + brightnessStep1 * i;\n  } else {\n    value = hsv.v - brightnessStep2 * i;\n  }\n  if (value > 1) {\n    value = 1;\n  }\n  return Number(value.toFixed(2));\n}\nfunction generate(color, opts = {}) {\n  const patterns = [];\n  const pColor = inputToRGB(color);\n  for (let i = lightColorCount; i > 0; i -= 1) {\n    const hsv = toHsv(pColor);\n    const colorString = toHex(inputToRGB({\n      h: getHue(hsv, i, true),\n      s: getSaturation(hsv, i, true),\n      v: getValue(hsv, i, true)\n    }));\n    patterns.push(colorString);\n  }\n  patterns.push(toHex(pColor));\n  for (let i = 1; i <= darkColorCount; i += 1) {\n    const hsv = toHsv(pColor);\n    const colorString = toHex(inputToRGB({\n      h: getHue(hsv, i),\n      s: getSaturation(hsv, i),\n      v: getValue(hsv, i)\n    }));\n    patterns.push(colorString);\n  }\n  // dark theme patterns\n  if (opts.theme === 'dark') {\n    return darkColorMap.map(({\n      index,\n      opacity\n    }) => {\n      const darkColorString = toHex(mix(inputToRGB(opts.backgroundColor || '#141414'), inputToRGB(patterns[index]), opacity * 100));\n      return darkColorString;\n    });\n  }\n  return patterns;\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { generate, isPresetColor, isStatusColor, presetColors, statusColors };", "map": {"version": 3, "names": ["rgbToHsv", "rgbToHex", "inputToRGB", "statusColors", "presetColors", "isPresetColor", "color", "indexOf", "isStatusColor", "hueStep", "saturationStep", "saturationStep2", "brightnessStep1", "brightnessStep2", "lightColorCount", "darkColorCount", "darkColorMap", "index", "opacity", "toHsv", "r", "g", "b", "hsv", "h", "s", "v", "toHex", "mix", "rgb1", "rgb2", "amount", "p", "rgb", "getHue", "i", "light", "hue", "Math", "round", "getSaturation", "saturation", "Number", "toFixed", "getValue", "value", "generate", "opts", "patterns", "pColor", "colorString", "push", "theme", "map", "darkColorString", "backgroundColor"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-core-color.mjs"], "sourcesContent": ["import { rgbToHsv, rgbToHex, inputToRGB } from '@ctrl/tinycolor';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst statusColors = ['success', 'processing', 'error', 'default', 'warning'];\nconst presetColors = [\n    'pink',\n    'red',\n    'yellow',\n    'orange',\n    'cyan',\n    'green',\n    'blue',\n    'purple',\n    'geekblue',\n    'magenta',\n    'volcano',\n    'gold',\n    'lime'\n];\nfunction isPresetColor(color) {\n    return presetColors.indexOf(color) !== -1;\n}\nfunction isStatusColor(color) {\n    return statusColors.indexOf(color) !== -1;\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/**\n * Sync from @ant-design/colors(https://github.com/ant-design/ant-design-colors)\n */\nconst hueStep = 2; // 色相阶梯\nconst saturationStep = 0.16; // 饱和度阶梯，浅色部分\nconst saturationStep2 = 0.05; // 饱和度阶梯，深色部分\nconst brightnessStep1 = 0.05; // 亮度阶梯，浅色部分\nconst brightnessStep2 = 0.15; // 亮度阶梯，深色部分\nconst lightColorCount = 5; // 浅色数量，主色上\nconst darkColorCount = 4; // 深色数量，主色下\n// 暗色主题颜色映射关系表\nconst darkColorMap = [\n    { index: 7, opacity: 0.15 },\n    { index: 6, opacity: 0.25 },\n    { index: 5, opacity: 0.3 },\n    { index: 5, opacity: 0.45 },\n    { index: 5, opacity: 0.65 },\n    { index: 5, opacity: 0.85 },\n    { index: 4, opacity: 0.9 },\n    { index: 3, opacity: 0.95 },\n    { index: 2, opacity: 0.97 },\n    { index: 1, opacity: 0.98 }\n];\n// Wrapper function ported from TinyColor.prototype.toHsv\n// Keep it here because of `hsv.h * 360`\nfunction toHsv({ r, g, b }) {\n    const hsv = rgbToHsv(r, g, b);\n    return { h: hsv.h * 360, s: hsv.s, v: hsv.v };\n}\n// Wrapper function ported from TinyColor.prototype.toHexString\n// Keep it here because of the prefix `#`\nfunction toHex({ r, g, b }) {\n    return `#${rgbToHex(r, g, b, false)}`;\n}\n// Wrapper function ported from TinyColor.prototype.mix, not treeshakable.\n// Amount in range [0, 1]\n// Assume color1 & color2 has no alpha, since the following src code did so.\nfunction mix(rgb1, rgb2, amount) {\n    const p = amount / 100;\n    const rgb = {\n        r: (rgb2.r - rgb1.r) * p + rgb1.r,\n        g: (rgb2.g - rgb1.g) * p + rgb1.g,\n        b: (rgb2.b - rgb1.b) * p + rgb1.b\n    };\n    return rgb;\n}\nfunction getHue(hsv, i, light) {\n    let hue;\n    // 根据色相不同，色相转向不同\n    if (Math.round(hsv.h) >= 60 && Math.round(hsv.h) <= 240) {\n        hue = light ? Math.round(hsv.h) - hueStep * i : Math.round(hsv.h) + hueStep * i;\n    }\n    else {\n        hue = light ? Math.round(hsv.h) + hueStep * i : Math.round(hsv.h) - hueStep * i;\n    }\n    if (hue < 0) {\n        hue += 360;\n    }\n    else if (hue >= 360) {\n        hue -= 360;\n    }\n    return hue;\n}\nfunction getSaturation(hsv, i, light) {\n    // grey color don't change saturation\n    if (hsv.h === 0 && hsv.s === 0) {\n        return hsv.s;\n    }\n    let saturation;\n    if (light) {\n        saturation = hsv.s - saturationStep * i;\n    }\n    else if (i === darkColorCount) {\n        saturation = hsv.s + saturationStep;\n    }\n    else {\n        saturation = hsv.s + saturationStep2 * i;\n    }\n    // 边界值修正\n    if (saturation > 1) {\n        saturation = 1;\n    }\n    // 第一格的 s 限制在 0.06-0.1 之间\n    if (light && i === lightColorCount && saturation > 0.1) {\n        saturation = 0.1;\n    }\n    if (saturation < 0.06) {\n        saturation = 0.06;\n    }\n    return Number(saturation.toFixed(2));\n}\nfunction getValue(hsv, i, light) {\n    let value;\n    if (light) {\n        value = hsv.v + brightnessStep1 * i;\n    }\n    else {\n        value = hsv.v - brightnessStep2 * i;\n    }\n    if (value > 1) {\n        value = 1;\n    }\n    return Number(value.toFixed(2));\n}\nfunction generate(color, opts = {}) {\n    const patterns = [];\n    const pColor = inputToRGB(color);\n    for (let i = lightColorCount; i > 0; i -= 1) {\n        const hsv = toHsv(pColor);\n        const colorString = toHex(inputToRGB({\n            h: getHue(hsv, i, true),\n            s: getSaturation(hsv, i, true),\n            v: getValue(hsv, i, true)\n        }));\n        patterns.push(colorString);\n    }\n    patterns.push(toHex(pColor));\n    for (let i = 1; i <= darkColorCount; i += 1) {\n        const hsv = toHsv(pColor);\n        const colorString = toHex(inputToRGB({\n            h: getHue(hsv, i),\n            s: getSaturation(hsv, i),\n            v: getValue(hsv, i)\n        }));\n        patterns.push(colorString);\n    }\n    // dark theme patterns\n    if (opts.theme === 'dark') {\n        return darkColorMap.map(({ index, opacity }) => {\n            const darkColorString = toHex(mix(inputToRGB(opts.backgroundColor || '#141414'), inputToRGB(patterns[index]), opacity * 100));\n            return darkColorString;\n        });\n    }\n    return patterns;\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { generate, isPresetColor, isStatusColor, presetColors, statusColors };\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,iBAAiB;;AAEhE;AACA;AACA;AACA;AACA,MAAMC,YAAY,GAAG,CAAC,SAAS,EAAE,YAAY,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC;AAC7E,MAAMC,YAAY,GAAG,CACjB,MAAM,EACN,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,OAAO,EACP,MAAM,EACN,QAAQ,EACR,UAAU,EACV,SAAS,EACT,SAAS,EACT,MAAM,EACN,MAAM,CACT;AACD,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC1B,OAAOF,YAAY,CAACG,OAAO,CAACD,KAAK,CAAC,KAAK,CAAC,CAAC;AAC7C;AACA,SAASE,aAAaA,CAACF,KAAK,EAAE;EAC1B,OAAOH,YAAY,CAACI,OAAO,CAACD,KAAK,CAAC,KAAK,CAAC,CAAC;AAC7C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,OAAO,GAAG,CAAC,CAAC,CAAC;AACnB,MAAMC,cAAc,GAAG,IAAI,CAAC,CAAC;AAC7B,MAAMC,eAAe,GAAG,IAAI,CAAC,CAAC;AAC9B,MAAMC,eAAe,GAAG,IAAI,CAAC,CAAC;AAC9B,MAAMC,eAAe,GAAG,IAAI,CAAC,CAAC;AAC9B,MAAMC,eAAe,GAAG,CAAC,CAAC,CAAC;AAC3B,MAAMC,cAAc,GAAG,CAAC,CAAC,CAAC;AAC1B;AACA,MAAMC,YAAY,GAAG,CACjB;EAAEC,KAAK,EAAE,CAAC;EAAEC,OAAO,EAAE;AAAK,CAAC,EAC3B;EAAED,KAAK,EAAE,CAAC;EAAEC,OAAO,EAAE;AAAK,CAAC,EAC3B;EAAED,KAAK,EAAE,CAAC;EAAEC,OAAO,EAAE;AAAI,CAAC,EAC1B;EAAED,KAAK,EAAE,CAAC;EAAEC,OAAO,EAAE;AAAK,CAAC,EAC3B;EAAED,KAAK,EAAE,CAAC;EAAEC,OAAO,EAAE;AAAK,CAAC,EAC3B;EAAED,KAAK,EAAE,CAAC;EAAEC,OAAO,EAAE;AAAK,CAAC,EAC3B;EAAED,KAAK,EAAE,CAAC;EAAEC,OAAO,EAAE;AAAI,CAAC,EAC1B;EAAED,KAAK,EAAE,CAAC;EAAEC,OAAO,EAAE;AAAK,CAAC,EAC3B;EAAED,KAAK,EAAE,CAAC;EAAEC,OAAO,EAAE;AAAK,CAAC,EAC3B;EAAED,KAAK,EAAE,CAAC;EAAEC,OAAO,EAAE;AAAK,CAAC,CAC9B;AACD;AACA;AACA,SAASC,KAAKA,CAAC;EAAEC,CAAC;EAAEC,CAAC;EAAEC;AAAE,CAAC,EAAE;EACxB,MAAMC,GAAG,GAAGvB,QAAQ,CAACoB,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;EAC7B,OAAO;IAAEE,CAAC,EAAED,GAAG,CAACC,CAAC,GAAG,GAAG;IAAEC,CAAC,EAAEF,GAAG,CAACE,CAAC;IAAEC,CAAC,EAAEH,GAAG,CAACG;EAAE,CAAC;AACjD;AACA;AACA;AACA,SAASC,KAAKA,CAAC;EAAEP,CAAC;EAAEC,CAAC;EAAEC;AAAE,CAAC,EAAE;EACxB,OAAQ,IAAGrB,QAAQ,CAACmB,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE,KAAK,CAAE,EAAC;AACzC;AACA;AACA;AACA;AACA,SAASM,GAAGA,CAACC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAE;EAC7B,MAAMC,CAAC,GAAGD,MAAM,GAAG,GAAG;EACtB,MAAME,GAAG,GAAG;IACRb,CAAC,EAAE,CAACU,IAAI,CAACV,CAAC,GAAGS,IAAI,CAACT,CAAC,IAAIY,CAAC,GAAGH,IAAI,CAACT,CAAC;IACjCC,CAAC,EAAE,CAACS,IAAI,CAACT,CAAC,GAAGQ,IAAI,CAACR,CAAC,IAAIW,CAAC,GAAGH,IAAI,CAACR,CAAC;IACjCC,CAAC,EAAE,CAACQ,IAAI,CAACR,CAAC,GAAGO,IAAI,CAACP,CAAC,IAAIU,CAAC,GAAGH,IAAI,CAACP;EACpC,CAAC;EACD,OAAOW,GAAG;AACd;AACA,SAASC,MAAMA,CAACX,GAAG,EAAEY,CAAC,EAAEC,KAAK,EAAE;EAC3B,IAAIC,GAAG;EACP;EACA,IAAIC,IAAI,CAACC,KAAK,CAAChB,GAAG,CAACC,CAAC,CAAC,IAAI,EAAE,IAAIc,IAAI,CAACC,KAAK,CAAChB,GAAG,CAACC,CAAC,CAAC,IAAI,GAAG,EAAE;IACrDa,GAAG,GAAGD,KAAK,GAAGE,IAAI,CAACC,KAAK,CAAChB,GAAG,CAACC,CAAC,CAAC,GAAGf,OAAO,GAAG0B,CAAC,GAAGG,IAAI,CAACC,KAAK,CAAChB,GAAG,CAACC,CAAC,CAAC,GAAGf,OAAO,GAAG0B,CAAC;EACnF,CAAC,MACI;IACDE,GAAG,GAAGD,KAAK,GAAGE,IAAI,CAACC,KAAK,CAAChB,GAAG,CAACC,CAAC,CAAC,GAAGf,OAAO,GAAG0B,CAAC,GAAGG,IAAI,CAACC,KAAK,CAAChB,GAAG,CAACC,CAAC,CAAC,GAAGf,OAAO,GAAG0B,CAAC;EACnF;EACA,IAAIE,GAAG,GAAG,CAAC,EAAE;IACTA,GAAG,IAAI,GAAG;EACd,CAAC,MACI,IAAIA,GAAG,IAAI,GAAG,EAAE;IACjBA,GAAG,IAAI,GAAG;EACd;EACA,OAAOA,GAAG;AACd;AACA,SAASG,aAAaA,CAACjB,GAAG,EAAEY,CAAC,EAAEC,KAAK,EAAE;EAClC;EACA,IAAIb,GAAG,CAACC,CAAC,KAAK,CAAC,IAAID,GAAG,CAACE,CAAC,KAAK,CAAC,EAAE;IAC5B,OAAOF,GAAG,CAACE,CAAC;EAChB;EACA,IAAIgB,UAAU;EACd,IAAIL,KAAK,EAAE;IACPK,UAAU,GAAGlB,GAAG,CAACE,CAAC,GAAGf,cAAc,GAAGyB,CAAC;EAC3C,CAAC,MACI,IAAIA,CAAC,KAAKpB,cAAc,EAAE;IAC3B0B,UAAU,GAAGlB,GAAG,CAACE,CAAC,GAAGf,cAAc;EACvC,CAAC,MACI;IACD+B,UAAU,GAAGlB,GAAG,CAACE,CAAC,GAAGd,eAAe,GAAGwB,CAAC;EAC5C;EACA;EACA,IAAIM,UAAU,GAAG,CAAC,EAAE;IAChBA,UAAU,GAAG,CAAC;EAClB;EACA;EACA,IAAIL,KAAK,IAAID,CAAC,KAAKrB,eAAe,IAAI2B,UAAU,GAAG,GAAG,EAAE;IACpDA,UAAU,GAAG,GAAG;EACpB;EACA,IAAIA,UAAU,GAAG,IAAI,EAAE;IACnBA,UAAU,GAAG,IAAI;EACrB;EACA,OAAOC,MAAM,CAACD,UAAU,CAACE,OAAO,CAAC,CAAC,CAAC,CAAC;AACxC;AACA,SAASC,QAAQA,CAACrB,GAAG,EAAEY,CAAC,EAAEC,KAAK,EAAE;EAC7B,IAAIS,KAAK;EACT,IAAIT,KAAK,EAAE;IACPS,KAAK,GAAGtB,GAAG,CAACG,CAAC,GAAGd,eAAe,GAAGuB,CAAC;EACvC,CAAC,MACI;IACDU,KAAK,GAAGtB,GAAG,CAACG,CAAC,GAAGb,eAAe,GAAGsB,CAAC;EACvC;EACA,IAAIU,KAAK,GAAG,CAAC,EAAE;IACXA,KAAK,GAAG,CAAC;EACb;EACA,OAAOH,MAAM,CAACG,KAAK,CAACF,OAAO,CAAC,CAAC,CAAC,CAAC;AACnC;AACA,SAASG,QAAQA,CAACxC,KAAK,EAAEyC,IAAI,GAAG,CAAC,CAAC,EAAE;EAChC,MAAMC,QAAQ,GAAG,EAAE;EACnB,MAAMC,MAAM,GAAG/C,UAAU,CAACI,KAAK,CAAC;EAChC,KAAK,IAAI6B,CAAC,GAAGrB,eAAe,EAAEqB,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;IACzC,MAAMZ,GAAG,GAAGJ,KAAK,CAAC8B,MAAM,CAAC;IACzB,MAAMC,WAAW,GAAGvB,KAAK,CAACzB,UAAU,CAAC;MACjCsB,CAAC,EAAEU,MAAM,CAACX,GAAG,EAAEY,CAAC,EAAE,IAAI,CAAC;MACvBV,CAAC,EAAEe,aAAa,CAACjB,GAAG,EAAEY,CAAC,EAAE,IAAI,CAAC;MAC9BT,CAAC,EAAEkB,QAAQ,CAACrB,GAAG,EAAEY,CAAC,EAAE,IAAI;IAC5B,CAAC,CAAC,CAAC;IACHa,QAAQ,CAACG,IAAI,CAACD,WAAW,CAAC;EAC9B;EACAF,QAAQ,CAACG,IAAI,CAACxB,KAAK,CAACsB,MAAM,CAAC,CAAC;EAC5B,KAAK,IAAId,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIpB,cAAc,EAAEoB,CAAC,IAAI,CAAC,EAAE;IACzC,MAAMZ,GAAG,GAAGJ,KAAK,CAAC8B,MAAM,CAAC;IACzB,MAAMC,WAAW,GAAGvB,KAAK,CAACzB,UAAU,CAAC;MACjCsB,CAAC,EAAEU,MAAM,CAACX,GAAG,EAAEY,CAAC,CAAC;MACjBV,CAAC,EAAEe,aAAa,CAACjB,GAAG,EAAEY,CAAC,CAAC;MACxBT,CAAC,EAAEkB,QAAQ,CAACrB,GAAG,EAAEY,CAAC;IACtB,CAAC,CAAC,CAAC;IACHa,QAAQ,CAACG,IAAI,CAACD,WAAW,CAAC;EAC9B;EACA;EACA,IAAIH,IAAI,CAACK,KAAK,KAAK,MAAM,EAAE;IACvB,OAAOpC,YAAY,CAACqC,GAAG,CAAC,CAAC;MAAEpC,KAAK;MAAEC;IAAQ,CAAC,KAAK;MAC5C,MAAMoC,eAAe,GAAG3B,KAAK,CAACC,GAAG,CAAC1B,UAAU,CAAC6C,IAAI,CAACQ,eAAe,IAAI,SAAS,CAAC,EAAErD,UAAU,CAAC8C,QAAQ,CAAC/B,KAAK,CAAC,CAAC,EAAEC,OAAO,GAAG,GAAG,CAAC,CAAC;MAC7H,OAAOoC,eAAe;IAC1B,CAAC,CAAC;EACN;EACA,OAAON,QAAQ;AACnB;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASF,QAAQ,EAAEzC,aAAa,EAAEG,aAAa,EAAEJ,YAAY,EAAED,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}