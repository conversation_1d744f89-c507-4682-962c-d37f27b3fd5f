{"ast": null, "code": "(function () {\n  \"use strict\";\n\n  try {\n    if (typeof document < \"u\") {\n      var t = document.createElement(\"style\");\n      t.appendChild(document.createTextNode(\".cdx-quote-icon svg{transform:rotate(180deg)}.cdx-quote{margin:0}.cdx-quote__text{min-height:158px;margin-bottom:10px}.cdx-quote [contentEditable=true][data-placeholder]:before{position:absolute;content:attr(data-placeholder);color:#707684;font-weight:400;opacity:0}.cdx-quote [contentEditable=true][data-placeholder]:empty:before{opacity:1}.cdx-quote [contentEditable=true][data-placeholder]:empty:focus:before{opacity:0}.cdx-quote-settings{display:flex}.cdx-quote-settings .cdx-settings-button{width:50%}\")), document.head.appendChild(t);\n    }\n  } catch (e) {\n    console.error(\"vite-plugin-css-injected-by-js\", e);\n  }\n})();\nconst De = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M18 7L6 7\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M18 17H6\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M16 12L8 12\"/></svg>',\n  He = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M17 7L5 7\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M17 17H5\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M13 12L5 12\"/></svg>',\n  Re = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10 10.8182L9 10.8182C8.80222 10.8182 8.60888 10.7649 8.44443 10.665C8.27998 10.5651 8.15181 10.4231 8.07612 10.257C8.00043 10.0909 7.98063 9.90808 8.01922 9.73174C8.0578 9.55539 8.15304 9.39341 8.29289 9.26627C8.43275 9.13913 8.61093 9.05255 8.80491 9.01747C8.99889 8.98239 9.19996 9.00039 9.38268 9.0692C9.56541 9.13801 9.72159 9.25453 9.83147 9.40403C9.94135 9.55353 10 9.72929 10 9.90909L10 12.1818C10 12.664 9.78929 13.1265 9.41421 13.4675C9.03914 13.8084 8.53043 14 8 14\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M16 10.8182L15 10.8182C14.8022 10.8182 14.6089 10.7649 14.4444 10.665C14.28 10.5651 14.1518 10.4231 14.0761 10.257C14.0004 10.0909 13.9806 9.90808 14.0192 9.73174C14.0578 9.55539 14.153 9.39341 14.2929 9.26627C14.4327 9.13913 14.6109 9.05255 14.8049 9.01747C14.9989 8.98239 15.2 9.00039 15.3827 9.0692C15.5654 9.13801 15.7216 9.25453 15.8315 9.40403C15.9414 9.55353 16 9.72929 16 9.90909L16 12.1818C16 12.664 15.7893 13.1265 15.4142 13.4675C15.0391 13.8084 14.5304 14 14 14\"/></svg>';\nvar b = typeof globalThis < \"u\" ? globalThis : typeof window < \"u\" ? window : typeof global < \"u\" ? global : typeof self < \"u\" ? self : {};\nfunction Fe(e) {\n  if (e.__esModule) return e;\n  var t = e.default;\n  if (typeof t == \"function\") {\n    var n = function r() {\n      return this instanceof r ? Reflect.construct(t, arguments, this.constructor) : t.apply(this, arguments);\n    };\n    n.prototype = t.prototype;\n  } else n = {};\n  return Object.defineProperty(n, \"__esModule\", {\n    value: !0\n  }), Object.keys(e).forEach(function (r) {\n    var i = Object.getOwnPropertyDescriptor(e, r);\n    Object.defineProperty(n, r, i.get ? i : {\n      enumerable: !0,\n      get: function () {\n        return e[r];\n      }\n    });\n  }), n;\n}\nvar v = {},\n  P = {},\n  j = {};\nObject.defineProperty(j, \"__esModule\", {\n  value: !0\n});\nj.allInputsSelector = We;\nfunction We() {\n  var e = [\"text\", \"password\", \"email\", \"number\", \"search\", \"tel\", \"url\"];\n  return \"[contenteditable=true], textarea, input:not([type]), \" + e.map(function (t) {\n    return 'input[type=\"'.concat(t, '\"]');\n  }).join(\", \");\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.allInputsSelector = void 0;\n  var t = j;\n  Object.defineProperty(e, \"allInputsSelector\", {\n    enumerable: !0,\n    get: function () {\n      return t.allInputsSelector;\n    }\n  });\n})(P);\nvar c = {},\n  T = {};\nObject.defineProperty(T, \"__esModule\", {\n  value: !0\n});\nT.isNativeInput = Ue;\nfunction Ue(e) {\n  var t = [\"INPUT\", \"TEXTAREA\"];\n  return e && e.tagName ? t.includes(e.tagName) : !1;\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.isNativeInput = void 0;\n  var t = T;\n  Object.defineProperty(e, \"isNativeInput\", {\n    enumerable: !0,\n    get: function () {\n      return t.isNativeInput;\n    }\n  });\n})(c);\nvar ie = {},\n  C = {};\nObject.defineProperty(C, \"__esModule\", {\n  value: !0\n});\nC.append = qe;\nfunction qe(e, t) {\n  Array.isArray(t) ? t.forEach(function (n) {\n    e.appendChild(n);\n  }) : e.appendChild(t);\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.append = void 0;\n  var t = C;\n  Object.defineProperty(e, \"append\", {\n    enumerable: !0,\n    get: function () {\n      return t.append;\n    }\n  });\n})(ie);\nvar L = {},\n  S = {};\nObject.defineProperty(S, \"__esModule\", {\n  value: !0\n});\nS.blockElements = ze;\nfunction ze() {\n  return [\"address\", \"article\", \"aside\", \"blockquote\", \"canvas\", \"div\", \"dl\", \"dt\", \"fieldset\", \"figcaption\", \"figure\", \"footer\", \"form\", \"h1\", \"h2\", \"h3\", \"h4\", \"h5\", \"h6\", \"header\", \"hgroup\", \"hr\", \"li\", \"main\", \"nav\", \"noscript\", \"ol\", \"output\", \"p\", \"pre\", \"ruby\", \"section\", \"table\", \"tbody\", \"thead\", \"tr\", \"tfoot\", \"ul\", \"video\"];\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.blockElements = void 0;\n  var t = S;\n  Object.defineProperty(e, \"blockElements\", {\n    enumerable: !0,\n    get: function () {\n      return t.blockElements;\n    }\n  });\n})(L);\nvar ae = {},\n  M = {};\nObject.defineProperty(M, \"__esModule\", {\n  value: !0\n});\nM.calculateBaseline = Ge;\nfunction Ge(e) {\n  var t = window.getComputedStyle(e),\n    n = parseFloat(t.fontSize),\n    r = parseFloat(t.lineHeight) || n * 1.2,\n    i = parseFloat(t.paddingTop),\n    a = parseFloat(t.borderTopWidth),\n    l = parseFloat(t.marginTop),\n    u = n * 0.8,\n    d = (r - n) / 2,\n    s = l + a + i + d + u;\n  return s;\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.calculateBaseline = void 0;\n  var t = M;\n  Object.defineProperty(e, \"calculateBaseline\", {\n    enumerable: !0,\n    get: function () {\n      return t.calculateBaseline;\n    }\n  });\n})(ae);\nvar le = {},\n  k = {},\n  w = {},\n  N = {};\nObject.defineProperty(N, \"__esModule\", {\n  value: !0\n});\nN.isContentEditable = Ke;\nfunction Ke(e) {\n  return e.contentEditable === \"true\";\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.isContentEditable = void 0;\n  var t = N;\n  Object.defineProperty(e, \"isContentEditable\", {\n    enumerable: !0,\n    get: function () {\n      return t.isContentEditable;\n    }\n  });\n})(w);\nObject.defineProperty(k, \"__esModule\", {\n  value: !0\n});\nk.canSetCaret = Qe;\nvar Xe = c,\n  Ye = w;\nfunction Qe(e) {\n  var t = !0;\n  if ((0, Xe.isNativeInput)(e)) switch (e.type) {\n    case \"file\":\n    case \"checkbox\":\n    case \"radio\":\n    case \"hidden\":\n    case \"submit\":\n    case \"button\":\n    case \"image\":\n    case \"reset\":\n      t = !1;\n      break;\n  } else t = (0, Ye.isContentEditable)(e);\n  return t;\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.canSetCaret = void 0;\n  var t = k;\n  Object.defineProperty(e, \"canSetCaret\", {\n    enumerable: !0,\n    get: function () {\n      return t.canSetCaret;\n    }\n  });\n})(le);\nvar y = {},\n  I = {};\nfunction Ve(e, t, n) {\n  const r = n.value !== void 0 ? \"value\" : \"get\",\n    i = n[r],\n    a = `#${t}Cache`;\n  if (n[r] = function (...l) {\n    return this[a] === void 0 && (this[a] = i.apply(this, l)), this[a];\n  }, r === \"get\" && n.set) {\n    const l = n.set;\n    n.set = function (u) {\n      delete e[a], l.apply(this, u);\n    };\n  }\n  return n;\n}\nfunction ue() {\n  const e = {\n      win: !1,\n      mac: !1,\n      x11: !1,\n      linux: !1\n    },\n    t = Object.keys(e).find(n => window.navigator.appVersion.toLowerCase().indexOf(n) !== -1);\n  return t !== void 0 && (e[t] = !0), e;\n}\nfunction A(e) {\n  return e != null && e !== \"\" && (typeof e != \"object\" || Object.keys(e).length > 0);\n}\nfunction Ze(e) {\n  return !A(e);\n}\nconst Je = () => typeof window < \"u\" && window.navigator !== null && A(window.navigator.platform) && (/iP(ad|hone|od)/.test(window.navigator.platform) || window.navigator.platform === \"MacIntel\" && window.navigator.maxTouchPoints > 1);\nfunction xe(e) {\n  const t = ue();\n  return e = e.replace(/shift/gi, \"⇧\").replace(/backspace/gi, \"⌫\").replace(/enter/gi, \"⏎\").replace(/up/gi, \"↑\").replace(/left/gi, \"→\").replace(/down/gi, \"↓\").replace(/right/gi, \"←\").replace(/escape/gi, \"⎋\").replace(/insert/gi, \"Ins\").replace(/delete/gi, \"␡\").replace(/\\+/gi, \"+\"), t.mac ? e = e.replace(/ctrl|cmd/gi, \"⌘\").replace(/alt/gi, \"⌥\") : e = e.replace(/cmd/gi, \"Ctrl\").replace(/windows/gi, \"WIN\"), e;\n}\nfunction et(e) {\n  return e[0].toUpperCase() + e.slice(1);\n}\nfunction tt(e) {\n  const t = document.createElement(\"div\");\n  t.style.position = \"absolute\", t.style.left = \"-999px\", t.style.bottom = \"-999px\", t.innerHTML = e, document.body.appendChild(t);\n  const n = window.getSelection(),\n    r = document.createRange();\n  if (r.selectNode(t), n === null) throw new Error(\"Cannot copy text to clipboard\");\n  n.removeAllRanges(), n.addRange(r), document.execCommand(\"copy\"), document.body.removeChild(t);\n}\nfunction nt(e, t, n) {\n  let r;\n  return (...i) => {\n    const a = this,\n      l = () => {\n        r = void 0, n !== !0 && e.apply(a, i);\n      },\n      u = n === !0 && r !== void 0;\n    window.clearTimeout(r), r = window.setTimeout(l, t), u && e.apply(a, i);\n  };\n}\nfunction o(e) {\n  return Object.prototype.toString.call(e).match(/\\s([a-zA-Z]+)/)[1].toLowerCase();\n}\nfunction rt(e) {\n  return o(e) === \"boolean\";\n}\nfunction oe(e) {\n  return o(e) === \"function\" || o(e) === \"asyncfunction\";\n}\nfunction it(e) {\n  return oe(e) && /^\\s*class\\s+/.test(e.toString());\n}\nfunction at(e) {\n  return o(e) === \"number\";\n}\nfunction g(e) {\n  return o(e) === \"object\";\n}\nfunction lt(e) {\n  return Promise.resolve(e) === e;\n}\nfunction ut(e) {\n  return o(e) === \"string\";\n}\nfunction ot(e) {\n  return o(e) === \"undefined\";\n}\nfunction O(e, ...t) {\n  if (!t.length) return e;\n  const n = t.shift();\n  if (g(e) && g(n)) for (const r in n) g(n[r]) ? (e[r] === void 0 && Object.assign(e, {\n    [r]: {}\n  }), O(e[r], n[r])) : Object.assign(e, {\n    [r]: n[r]\n  });\n  return O(e, ...t);\n}\nfunction st(e, t, n) {\n  const r = `«${t}» is deprecated and will be removed in the next major release. Please use the «${n}» instead.`;\n  e && console.warn(r);\n}\nfunction ct(e) {\n  try {\n    return new URL(e).href;\n  } catch {}\n  return e.substring(0, 2) === \"//\" ? window.location.protocol + e : window.location.origin + e;\n}\nfunction dt(e) {\n  return e > 47 && e < 58 || e === 32 || e === 13 || e === 229 || e > 64 && e < 91 || e > 95 && e < 112 || e > 185 && e < 193 || e > 218 && e < 223;\n}\nconst ft = {\n    BACKSPACE: 8,\n    TAB: 9,\n    ENTER: 13,\n    SHIFT: 16,\n    CTRL: 17,\n    ALT: 18,\n    ESC: 27,\n    SPACE: 32,\n    LEFT: 37,\n    UP: 38,\n    DOWN: 40,\n    RIGHT: 39,\n    DELETE: 46,\n    META: 91,\n    SLASH: 191\n  },\n  pt = {\n    LEFT: 0,\n    WHEEL: 1,\n    RIGHT: 2,\n    BACKWARD: 3,\n    FORWARD: 4\n  };\nclass vt {\n  constructor() {\n    this.completed = Promise.resolve();\n  }\n  /**\n   * Add new promise to queue\n   * @param operation - promise should be added to queue\n   */\n  add(t) {\n    return new Promise((n, r) => {\n      this.completed = this.completed.then(t).then(n).catch(r);\n    });\n  }\n}\nfunction gt(e, t, n = void 0) {\n  let r,\n    i,\n    a,\n    l = null,\n    u = 0;\n  n || (n = {});\n  const d = function () {\n    u = n.leading === !1 ? 0 : Date.now(), l = null, a = e.apply(r, i), l === null && (r = i = null);\n  };\n  return function () {\n    const s = Date.now();\n    !u && n.leading === !1 && (u = s);\n    const f = t - (s - u);\n    return r = this, i = arguments, f <= 0 || f > t ? (l && (clearTimeout(l), l = null), u = s, a = e.apply(r, i), l === null && (r = i = null)) : !l && n.trailing !== !1 && (l = setTimeout(d, f)), a;\n  };\n}\nconst mt = /* @__PURE__ */Object.freeze( /* @__PURE__ */Object.defineProperty({\n    __proto__: null,\n    PromiseQueue: vt,\n    beautifyShortcut: xe,\n    cacheable: Ve,\n    capitalize: et,\n    copyTextToClipboard: tt,\n    debounce: nt,\n    deepMerge: O,\n    deprecationAssert: st,\n    getUserOS: ue,\n    getValidUrl: ct,\n    isBoolean: rt,\n    isClass: it,\n    isEmpty: Ze,\n    isFunction: oe,\n    isIosDevice: Je,\n    isNumber: at,\n    isObject: g,\n    isPrintableKey: dt,\n    isPromise: lt,\n    isString: ut,\n    isUndefined: ot,\n    keyCodes: ft,\n    mouseButtons: pt,\n    notEmpty: A,\n    throttle: gt,\n    typeOf: o\n  }, Symbol.toStringTag, {\n    value: \"Module\"\n  })),\n  $ = /* @__PURE__ */Fe(mt);\nObject.defineProperty(I, \"__esModule\", {\n  value: !0\n});\nI.containsOnlyInlineElements = _t;\nvar bt = $,\n  yt = L;\nfunction _t(e) {\n  var t;\n  (0, bt.isString)(e) ? (t = document.createElement(\"div\"), t.innerHTML = e) : t = e;\n  var n = function (r) {\n    return !(0, yt.blockElements)().includes(r.tagName.toLowerCase()) && Array.from(r.children).every(n);\n  };\n  return Array.from(t.children).every(n);\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.containsOnlyInlineElements = void 0;\n  var t = I;\n  Object.defineProperty(e, \"containsOnlyInlineElements\", {\n    enumerable: !0,\n    get: function () {\n      return t.containsOnlyInlineElements;\n    }\n  });\n})(y);\nvar se = {},\n  B = {},\n  _ = {},\n  D = {};\nObject.defineProperty(D, \"__esModule\", {\n  value: !0\n});\nD.make = ht;\nfunction ht(e, t, n) {\n  var r;\n  t === void 0 && (t = null), n === void 0 && (n = {});\n  var i = document.createElement(e);\n  if (Array.isArray(t)) {\n    var a = t.filter(function (u) {\n      return u !== void 0;\n    });\n    (r = i.classList).add.apply(r, a);\n  } else t !== null && i.classList.add(t);\n  for (var l in n) Object.prototype.hasOwnProperty.call(n, l) && (i[l] = n[l]);\n  return i;\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.make = void 0;\n  var t = D;\n  Object.defineProperty(e, \"make\", {\n    enumerable: !0,\n    get: function () {\n      return t.make;\n    }\n  });\n})(_);\nObject.defineProperty(B, \"__esModule\", {\n  value: !0\n});\nB.fragmentToString = Ot;\nvar Et = _;\nfunction Ot(e) {\n  var t = (0, Et.make)(\"div\");\n  return t.appendChild(e), t.innerHTML;\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.fragmentToString = void 0;\n  var t = B;\n  Object.defineProperty(e, \"fragmentToString\", {\n    enumerable: !0,\n    get: function () {\n      return t.fragmentToString;\n    }\n  });\n})(se);\nvar ce = {},\n  H = {};\nObject.defineProperty(H, \"__esModule\", {\n  value: !0\n});\nH.getContentLength = jt;\nvar Pt = c;\nfunction jt(e) {\n  var t, n;\n  return (0, Pt.isNativeInput)(e) ? e.value.length : e.nodeType === Node.TEXT_NODE ? e.length : (n = (t = e.textContent) === null || t === void 0 ? void 0 : t.length) !== null && n !== void 0 ? n : 0;\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.getContentLength = void 0;\n  var t = H;\n  Object.defineProperty(e, \"getContentLength\", {\n    enumerable: !0,\n    get: function () {\n      return t.getContentLength;\n    }\n  });\n})(ce);\nvar R = {},\n  F = {},\n  re = b && b.__spreadArray || function (e, t, n) {\n    if (n || arguments.length === 2) for (var r = 0, i = t.length, a; r < i; r++) (a || !(r in t)) && (a || (a = Array.prototype.slice.call(t, 0, r)), a[r] = t[r]);\n    return e.concat(a || Array.prototype.slice.call(t));\n  };\nObject.defineProperty(F, \"__esModule\", {\n  value: !0\n});\nF.getDeepestBlockElements = de;\nvar Tt = y;\nfunction de(e) {\n  return (0, Tt.containsOnlyInlineElements)(e) ? [e] : Array.from(e.children).reduce(function (t, n) {\n    return re(re([], t, !0), de(n), !0);\n  }, []);\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.getDeepestBlockElements = void 0;\n  var t = F;\n  Object.defineProperty(e, \"getDeepestBlockElements\", {\n    enumerable: !0,\n    get: function () {\n      return t.getDeepestBlockElements;\n    }\n  });\n})(R);\nvar fe = {},\n  W = {},\n  h = {},\n  U = {};\nObject.defineProperty(U, \"__esModule\", {\n  value: !0\n});\nU.isLineBreakTag = Ct;\nfunction Ct(e) {\n  return [\"BR\", \"WBR\"].includes(e.tagName);\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.isLineBreakTag = void 0;\n  var t = U;\n  Object.defineProperty(e, \"isLineBreakTag\", {\n    enumerable: !0,\n    get: function () {\n      return t.isLineBreakTag;\n    }\n  });\n})(h);\nvar E = {},\n  q = {};\nObject.defineProperty(q, \"__esModule\", {\n  value: !0\n});\nq.isSingleTag = Lt;\nfunction Lt(e) {\n  return [\"AREA\", \"BASE\", \"BR\", \"COL\", \"COMMAND\", \"EMBED\", \"HR\", \"IMG\", \"INPUT\", \"KEYGEN\", \"LINK\", \"META\", \"PARAM\", \"SOURCE\", \"TRACK\", \"WBR\"].includes(e.tagName);\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.isSingleTag = void 0;\n  var t = q;\n  Object.defineProperty(e, \"isSingleTag\", {\n    enumerable: !0,\n    get: function () {\n      return t.isSingleTag;\n    }\n  });\n})(E);\nObject.defineProperty(W, \"__esModule\", {\n  value: !0\n});\nW.getDeepestNode = pe;\nvar St = c,\n  Mt = h,\n  kt = E;\nfunction pe(e, t) {\n  t === void 0 && (t = !1);\n  var n = t ? \"lastChild\" : \"firstChild\",\n    r = t ? \"previousSibling\" : \"nextSibling\";\n  if (e.nodeType === Node.ELEMENT_NODE && e[n]) {\n    var i = e[n];\n    if ((0, kt.isSingleTag)(i) && !(0, St.isNativeInput)(i) && !(0, Mt.isLineBreakTag)(i)) if (i[r]) i = i[r];else if (i.parentNode !== null && i.parentNode[r]) i = i.parentNode[r];else return i.parentNode;\n    return pe(i, t);\n  }\n  return e;\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.getDeepestNode = void 0;\n  var t = W;\n  Object.defineProperty(e, \"getDeepestNode\", {\n    enumerable: !0,\n    get: function () {\n      return t.getDeepestNode;\n    }\n  });\n})(fe);\nvar ve = {},\n  z = {},\n  p = b && b.__spreadArray || function (e, t, n) {\n    if (n || arguments.length === 2) for (var r = 0, i = t.length, a; r < i; r++) (a || !(r in t)) && (a || (a = Array.prototype.slice.call(t, 0, r)), a[r] = t[r]);\n    return e.concat(a || Array.prototype.slice.call(t));\n  };\nObject.defineProperty(z, \"__esModule\", {\n  value: !0\n});\nz.findAllInputs = $t;\nvar wt = y,\n  Nt = R,\n  It = P,\n  At = c;\nfunction $t(e) {\n  return Array.from(e.querySelectorAll((0, It.allInputsSelector)())).reduce(function (t, n) {\n    return (0, At.isNativeInput)(n) || (0, wt.containsOnlyInlineElements)(n) ? p(p([], t, !0), [n], !1) : p(p([], t, !0), (0, Nt.getDeepestBlockElements)(n), !0);\n  }, []);\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.findAllInputs = void 0;\n  var t = z;\n  Object.defineProperty(e, \"findAllInputs\", {\n    enumerable: !0,\n    get: function () {\n      return t.findAllInputs;\n    }\n  });\n})(ve);\nvar ge = {},\n  G = {};\nObject.defineProperty(G, \"__esModule\", {\n  value: !0\n});\nG.isCollapsedWhitespaces = Bt;\nfunction Bt(e) {\n  return !/[^\\t\\n\\r ]/.test(e);\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.isCollapsedWhitespaces = void 0;\n  var t = G;\n  Object.defineProperty(e, \"isCollapsedWhitespaces\", {\n    enumerable: !0,\n    get: function () {\n      return t.isCollapsedWhitespaces;\n    }\n  });\n})(ge);\nvar K = {},\n  X = {};\nObject.defineProperty(X, \"__esModule\", {\n  value: !0\n});\nX.isElement = Ht;\nvar Dt = $;\nfunction Ht(e) {\n  return (0, Dt.isNumber)(e) ? !1 : !!e && !!e.nodeType && e.nodeType === Node.ELEMENT_NODE;\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.isElement = void 0;\n  var t = X;\n  Object.defineProperty(e, \"isElement\", {\n    enumerable: !0,\n    get: function () {\n      return t.isElement;\n    }\n  });\n})(K);\nvar me = {},\n  Y = {},\n  Q = {},\n  V = {};\nObject.defineProperty(V, \"__esModule\", {\n  value: !0\n});\nV.isLeaf = Rt;\nfunction Rt(e) {\n  return e === null ? !1 : e.childNodes.length === 0;\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.isLeaf = void 0;\n  var t = V;\n  Object.defineProperty(e, \"isLeaf\", {\n    enumerable: !0,\n    get: function () {\n      return t.isLeaf;\n    }\n  });\n})(Q);\nvar Z = {},\n  J = {};\nObject.defineProperty(J, \"__esModule\", {\n  value: !0\n});\nJ.isNodeEmpty = zt;\nvar Ft = h,\n  Wt = K,\n  Ut = c,\n  qt = E;\nfunction zt(e, t) {\n  var n = \"\";\n  return (0, qt.isSingleTag)(e) && !(0, Ft.isLineBreakTag)(e) ? !1 : ((0, Wt.isElement)(e) && (0, Ut.isNativeInput)(e) ? n = e.value : e.textContent !== null && (n = e.textContent.replace(\"​\", \"\")), t !== void 0 && (n = n.replace(new RegExp(t, \"g\"), \"\")), n.trim().length === 0);\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.isNodeEmpty = void 0;\n  var t = J;\n  Object.defineProperty(e, \"isNodeEmpty\", {\n    enumerable: !0,\n    get: function () {\n      return t.isNodeEmpty;\n    }\n  });\n})(Z);\nObject.defineProperty(Y, \"__esModule\", {\n  value: !0\n});\nY.isEmpty = Xt;\nvar Gt = Q,\n  Kt = Z;\nfunction Xt(e, t) {\n  e.normalize();\n  for (var n = [e]; n.length > 0;) {\n    var r = n.shift();\n    if (r) {\n      if (e = r, (0, Gt.isLeaf)(e) && !(0, Kt.isNodeEmpty)(e, t)) return !1;\n      n.push.apply(n, Array.from(e.childNodes));\n    }\n  }\n  return !0;\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.isEmpty = void 0;\n  var t = Y;\n  Object.defineProperty(e, \"isEmpty\", {\n    enumerable: !0,\n    get: function () {\n      return t.isEmpty;\n    }\n  });\n})(me);\nvar be = {},\n  x = {};\nObject.defineProperty(x, \"__esModule\", {\n  value: !0\n});\nx.isFragment = Qt;\nvar Yt = $;\nfunction Qt(e) {\n  return (0, Yt.isNumber)(e) ? !1 : !!e && !!e.nodeType && e.nodeType === Node.DOCUMENT_FRAGMENT_NODE;\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.isFragment = void 0;\n  var t = x;\n  Object.defineProperty(e, \"isFragment\", {\n    enumerable: !0,\n    get: function () {\n      return t.isFragment;\n    }\n  });\n})(be);\nvar ye = {},\n  ee = {};\nObject.defineProperty(ee, \"__esModule\", {\n  value: !0\n});\nee.isHTMLString = Zt;\nvar Vt = _;\nfunction Zt(e) {\n  var t = (0, Vt.make)(\"div\");\n  return t.innerHTML = e, t.childElementCount > 0;\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.isHTMLString = void 0;\n  var t = ee;\n  Object.defineProperty(e, \"isHTMLString\", {\n    enumerable: !0,\n    get: function () {\n      return t.isHTMLString;\n    }\n  });\n})(ye);\nvar _e = {},\n  te = {};\nObject.defineProperty(te, \"__esModule\", {\n  value: !0\n});\nte.offset = Jt;\nfunction Jt(e) {\n  var t = e.getBoundingClientRect(),\n    n = window.pageXOffset || document.documentElement.scrollLeft,\n    r = window.pageYOffset || document.documentElement.scrollTop,\n    i = t.top + r,\n    a = t.left + n;\n  return {\n    top: i,\n    left: a,\n    bottom: i + t.height,\n    right: a + t.width\n  };\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.offset = void 0;\n  var t = te;\n  Object.defineProperty(e, \"offset\", {\n    enumerable: !0,\n    get: function () {\n      return t.offset;\n    }\n  });\n})(_e);\nvar he = {},\n  ne = {};\nObject.defineProperty(ne, \"__esModule\", {\n  value: !0\n});\nne.prepend = xt;\nfunction xt(e, t) {\n  Array.isArray(t) ? (t = t.reverse(), t.forEach(function (n) {\n    return e.prepend(n);\n  })) : e.prepend(t);\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.prepend = void 0;\n  var t = ne;\n  Object.defineProperty(e, \"prepend\", {\n    enumerable: !0,\n    get: function () {\n      return t.prepend;\n    }\n  });\n})(he);\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.prepend = e.offset = e.make = e.isLineBreakTag = e.isSingleTag = e.isNodeEmpty = e.isLeaf = e.isHTMLString = e.isFragment = e.isEmpty = e.isElement = e.isContentEditable = e.isCollapsedWhitespaces = e.findAllInputs = e.isNativeInput = e.allInputsSelector = e.getDeepestNode = e.getDeepestBlockElements = e.getContentLength = e.fragmentToString = e.containsOnlyInlineElements = e.canSetCaret = e.calculateBaseline = e.blockElements = e.append = void 0;\n  var t = P;\n  Object.defineProperty(e, \"allInputsSelector\", {\n    enumerable: !0,\n    get: function () {\n      return t.allInputsSelector;\n    }\n  });\n  var n = c;\n  Object.defineProperty(e, \"isNativeInput\", {\n    enumerable: !0,\n    get: function () {\n      return n.isNativeInput;\n    }\n  });\n  var r = ie;\n  Object.defineProperty(e, \"append\", {\n    enumerable: !0,\n    get: function () {\n      return r.append;\n    }\n  });\n  var i = L;\n  Object.defineProperty(e, \"blockElements\", {\n    enumerable: !0,\n    get: function () {\n      return i.blockElements;\n    }\n  });\n  var a = ae;\n  Object.defineProperty(e, \"calculateBaseline\", {\n    enumerable: !0,\n    get: function () {\n      return a.calculateBaseline;\n    }\n  });\n  var l = le;\n  Object.defineProperty(e, \"canSetCaret\", {\n    enumerable: !0,\n    get: function () {\n      return l.canSetCaret;\n    }\n  });\n  var u = y;\n  Object.defineProperty(e, \"containsOnlyInlineElements\", {\n    enumerable: !0,\n    get: function () {\n      return u.containsOnlyInlineElements;\n    }\n  });\n  var d = se;\n  Object.defineProperty(e, \"fragmentToString\", {\n    enumerable: !0,\n    get: function () {\n      return d.fragmentToString;\n    }\n  });\n  var s = ce;\n  Object.defineProperty(e, \"getContentLength\", {\n    enumerable: !0,\n    get: function () {\n      return s.getContentLength;\n    }\n  });\n  var f = R;\n  Object.defineProperty(e, \"getDeepestBlockElements\", {\n    enumerable: !0,\n    get: function () {\n      return f.getDeepestBlockElements;\n    }\n  });\n  var Oe = fe;\n  Object.defineProperty(e, \"getDeepestNode\", {\n    enumerable: !0,\n    get: function () {\n      return Oe.getDeepestNode;\n    }\n  });\n  var Pe = ve;\n  Object.defineProperty(e, \"findAllInputs\", {\n    enumerable: !0,\n    get: function () {\n      return Pe.findAllInputs;\n    }\n  });\n  var je = ge;\n  Object.defineProperty(e, \"isCollapsedWhitespaces\", {\n    enumerable: !0,\n    get: function () {\n      return je.isCollapsedWhitespaces;\n    }\n  });\n  var Te = w;\n  Object.defineProperty(e, \"isContentEditable\", {\n    enumerable: !0,\n    get: function () {\n      return Te.isContentEditable;\n    }\n  });\n  var Ce = K;\n  Object.defineProperty(e, \"isElement\", {\n    enumerable: !0,\n    get: function () {\n      return Ce.isElement;\n    }\n  });\n  var Le = me;\n  Object.defineProperty(e, \"isEmpty\", {\n    enumerable: !0,\n    get: function () {\n      return Le.isEmpty;\n    }\n  });\n  var Se = be;\n  Object.defineProperty(e, \"isFragment\", {\n    enumerable: !0,\n    get: function () {\n      return Se.isFragment;\n    }\n  });\n  var Me = ye;\n  Object.defineProperty(e, \"isHTMLString\", {\n    enumerable: !0,\n    get: function () {\n      return Me.isHTMLString;\n    }\n  });\n  var ke = Q;\n  Object.defineProperty(e, \"isLeaf\", {\n    enumerable: !0,\n    get: function () {\n      return ke.isLeaf;\n    }\n  });\n  var we = Z;\n  Object.defineProperty(e, \"isNodeEmpty\", {\n    enumerable: !0,\n    get: function () {\n      return we.isNodeEmpty;\n    }\n  });\n  var Ne = h;\n  Object.defineProperty(e, \"isLineBreakTag\", {\n    enumerable: !0,\n    get: function () {\n      return Ne.isLineBreakTag;\n    }\n  });\n  var Ie = E;\n  Object.defineProperty(e, \"isSingleTag\", {\n    enumerable: !0,\n    get: function () {\n      return Ie.isSingleTag;\n    }\n  });\n  var Ae = _;\n  Object.defineProperty(e, \"make\", {\n    enumerable: !0,\n    get: function () {\n      return Ae.make;\n    }\n  });\n  var $e = _e;\n  Object.defineProperty(e, \"offset\", {\n    enumerable: !0,\n    get: function () {\n      return $e.offset;\n    }\n  });\n  var Be = he;\n  Object.defineProperty(e, \"prepend\", {\n    enumerable: !0,\n    get: function () {\n      return Be.prepend;\n    }\n  });\n})(v);\nvar Ee = /* @__PURE__ */(e => (e.Left = \"left\", e.Center = \"center\", e))(Ee || {});\nclass m {\n  /**\n   * Render plugin`s main Element and fill it with saved data\n   * @param params - Quote Tool constructor params\n   * @param params.data - previously saved data\n   * @param params.config - user config for Tool\n   * @param params.api - editor.js api\n   * @param params.readOnly - read only mode flag\n   */\n  constructor({\n    data: t,\n    config: n,\n    api: r,\n    readOnly: i,\n    block: a\n  }) {\n    const {\n      DEFAULT_ALIGNMENT: l\n    } = m;\n    this.api = r, this.readOnly = i, this.quotePlaceholder = r.i18n.t((n == null ? void 0 : n.quotePlaceholder) ?? m.DEFAULT_QUOTE_PLACEHOLDER), this.captionPlaceholder = r.i18n.t((n == null ? void 0 : n.captionPlaceholder) ?? m.DEFAULT_CAPTION_PLACEHOLDER), this.data = {\n      text: t.text || \"\",\n      caption: t.caption || \"\",\n      alignment: Object.values(Ee).includes(t.alignment) ? t.alignment : (n == null ? void 0 : n.defaultAlignment) ?? l\n    }, this.css = {\n      baseClass: this.api.styles.block,\n      wrapper: \"cdx-quote\",\n      text: \"cdx-quote__text\",\n      input: this.api.styles.input,\n      caption: \"cdx-quote__caption\"\n    }, this.block = a;\n  }\n  /**\n   * Notify core that read-only mode is supported\n   * @returns true\n   */\n  static get isReadOnlySupported() {\n    return !0;\n  }\n  /**\n   * Get Tool toolbox settings\n   * icon - Tool icon's SVG\n   * title - title to show in toolbox\n   * @returns icon and title of the toolbox\n   */\n  static get toolbox() {\n    return {\n      icon: Re,\n      title: \"Quote\"\n    };\n  }\n  /**\n   * Empty Quote is not empty Block\n   * @returns true\n   */\n  static get contentless() {\n    return !0;\n  }\n  /**\n   * Allow to press Enter inside the Quote\n   * @returns true\n   */\n  static get enableLineBreaks() {\n    return !0;\n  }\n  /**\n   * Default placeholder for quote text\n   * @returns 'Enter a quote'\n   */\n  static get DEFAULT_QUOTE_PLACEHOLDER() {\n    return \"Enter a quote\";\n  }\n  /**\n   * Default placeholder for quote caption\n   * @returns 'Enter a caption'\n   */\n  static get DEFAULT_CAPTION_PLACEHOLDER() {\n    return \"Enter a caption\";\n  }\n  /**\n   * Default quote alignment\n   * @returns Alignment.Left\n   */\n  static get DEFAULT_ALIGNMENT() {\n    return \"left\";\n  }\n  /**\n   * Allow Quote to be converted to/from other blocks\n   * @returns conversion config object\n   */\n  static get conversionConfig() {\n    return {\n      /**\n       * To create Quote data from string, simple fill 'text' property\n       */\n      import: \"text\",\n      /**\n       * To create string from Quote data, concatenate text and caption\n       * @param quoteData - Quote data object\n       * @returns string\n       */\n      export: function (t) {\n        return t.caption ? `${t.text} — ${t.caption}` : t.text;\n      }\n    };\n  }\n  /**\n   * Tool`s styles\n   * @returns CSS classes names\n   */\n  get CSS() {\n    return {\n      baseClass: this.api.styles.block,\n      wrapper: \"cdx-quote\",\n      text: \"cdx-quote__text\",\n      input: this.api.styles.input,\n      caption: \"cdx-quote__caption\"\n    };\n  }\n  /**\n   * Tool`s settings properties\n   * @returns settings properties\n   */\n  get settings() {\n    return [{\n      name: \"left\",\n      icon: He\n    }, {\n      name: \"center\",\n      icon: De\n    }];\n  }\n  /**\n   * Create Quote Tool container with inputs\n   * @returns blockquote DOM element - Quote Tool container\n   */\n  render() {\n    const t = v.make(\"blockquote\", [this.css.baseClass, this.css.wrapper]),\n      n = v.make(\"div\", [this.css.input, this.css.text], {\n        contentEditable: !this.readOnly,\n        innerHTML: this.data.text\n      }),\n      r = v.make(\"div\", [this.css.input, this.css.caption], {\n        contentEditable: !this.readOnly,\n        innerHTML: this.data.caption\n      });\n    return n.dataset.placeholder = this.quotePlaceholder, r.dataset.placeholder = this.captionPlaceholder, t.appendChild(n), t.appendChild(r), t;\n  }\n  /**\n   * Extract Quote data from Quote Tool element\n   * @param quoteElement - Quote DOM element to save\n   * @returns Quote data object\n   */\n  save(t) {\n    const n = t.querySelector(`.${this.css.text}`),\n      r = t.querySelector(`.${this.css.caption}`);\n    return Object.assign(this.data, {\n      text: (n == null ? void 0 : n.innerHTML) ?? \"\",\n      caption: (r == null ? void 0 : r.innerHTML) ?? \"\"\n    });\n  }\n  /**\n   * Sanitizer rules\n   * @returns sanitizer rules\n   */\n  static get sanitize() {\n    return {\n      text: {\n        br: !0\n      },\n      caption: {\n        br: !0\n      },\n      alignment: {}\n    };\n  }\n  /**\n   * Create wrapper for Tool`s settings buttons:\n   * 1. Left alignment\n   * 2. Center alignment\n   * @returns settings menu\n   */\n  renderSettings() {\n    const t = n => n && n[0].toUpperCase() + n.slice(1);\n    return this.settings.map(n => ({\n      icon: n.icon,\n      label: this.api.i18n.t(`Align ${t(n.name)}`),\n      onActivate: () => this._toggleTune(n.name),\n      isActive: this.data.alignment === n.name,\n      closeOnActivate: !0\n    }));\n  }\n  /**\n   * Toggle quote`s alignment\n   * @param tune - alignment\n   */\n  _toggleTune(t) {\n    this.data.alignment = t, this.block.dispatchChange();\n  }\n}\nexport { m as default };", "map": {"version": 3, "names": ["document", "t", "createElement", "append<PERSON><PERSON><PERSON>", "createTextNode", "head", "e", "console", "error", "De", "He", "Re", "b", "globalThis", "window", "global", "self", "Fe", "__esModule", "default", "n", "r", "Reflect", "construct", "arguments", "constructor", "apply", "prototype", "Object", "defineProperty", "value", "keys", "for<PERSON>ach", "i", "getOwnPropertyDescriptor", "get", "enumerable", "v", "P", "j", "allInputsSelector", "We", "map", "concat", "join", "c", "T", "isNativeInput", "Ue", "tagName", "includes", "ie", "C", "append", "qe", "Array", "isArray", "L", "S", "blockElements", "ze", "ae", "M", "calculateBaseline", "Ge", "getComputedStyle", "parseFloat", "fontSize", "lineHeight", "paddingTop", "a", "borderTopWidth", "l", "marginTop", "u", "d", "s", "le", "k", "w", "N", "isContentEditable", "<PERSON>", "contentEditable", "canSetCaret", "Qe", "Xe", "Ye", "type", "y", "I", "Ve", "set", "ue", "win", "mac", "x11", "linux", "find", "navigator", "appVersion", "toLowerCase", "indexOf", "A", "length", "Ze", "Je", "platform", "test", "maxTouchPoints", "xe", "replace", "et", "toUpperCase", "slice", "tt", "style", "position", "left", "bottom", "innerHTML", "body", "getSelection", "createRange", "selectNode", "Error", "removeAllRanges", "addRange", "execCommand", "<PERSON><PERSON><PERSON><PERSON>", "nt", "clearTimeout", "setTimeout", "o", "toString", "call", "match", "rt", "oe", "it", "at", "g", "lt", "Promise", "resolve", "ut", "ot", "O", "shift", "assign", "st", "warn", "ct", "URL", "href", "substring", "location", "protocol", "origin", "dt", "ft", "BACKSPACE", "TAB", "ENTER", "SHIFT", "CTRL", "ALT", "ESC", "SPACE", "LEFT", "UP", "DOWN", "RIGHT", "DELETE", "META", "SLASH", "pt", "WHEEL", "BACKWARD", "FORWARD", "vt", "completed", "add", "then", "catch", "gt", "leading", "Date", "now", "f", "trailing", "mt", "freeze", "__proto__", "PromiseQueue", "beautifyShortcut", "cacheable", "capitalize", "copyTextToClipboard", "debounce", "deepMerge", "deprecationAssert", "getUserOS", "getValidUrl", "isBoolean", "isClass", "isEmpty", "isFunction", "isIosDevice", "isNumber", "isObject", "isPrintableKey", "isPromise", "isString", "isUndefined", "keyCodes", "mouseButtons", "notEmpty", "throttle", "typeOf", "Symbol", "toStringTag", "$", "containsOnlyInlineElements", "_t", "bt", "yt", "from", "children", "every", "se", "B", "_", "D", "make", "ht", "filter", "classList", "hasOwnProperty", "fragmentToString", "<PERSON>t", "Et", "ce", "H", "getContentLength", "jt", "Pt", "nodeType", "Node", "TEXT_NODE", "textContent", "R", "F", "re", "__spread<PERSON><PERSON>y", "getDeepestBlockElements", "de", "Tt", "reduce", "fe", "W", "h", "U", "isLineBreakTag", "Ct", "E", "q", "isSingleTag", "Lt", "getDeepestNode", "pe", "St", "Mt", "kt", "ELEMENT_NODE", "parentNode", "ve", "z", "p", "findAllInputs", "$t", "wt", "Nt", "It", "At", "querySelectorAll", "ge", "G", "isCollapsedWhitespaces", "Bt", "K", "X", "isElement", "Ht", "Dt", "me", "Y", "Q", "V", "<PERSON><PERSON><PERSON><PERSON>", "Rt", "childNodes", "Z", "J", "isNodeEmpty", "zt", "Ft", "Wt", "Ut", "qt", "RegExp", "trim", "Xt", "Gt", "Kt", "normalize", "push", "be", "x", "isFragment", "Qt", "Yt", "DOCUMENT_FRAGMENT_NODE", "ye", "ee", "isHTMLString", "Zt", "Vt", "childElementCount", "_e", "te", "offset", "Jt", "getBoundingClientRect", "pageXOffset", "documentElement", "scrollLeft", "pageYOffset", "scrollTop", "top", "height", "right", "width", "he", "ne", "prepend", "xt", "reverse", "Oe", "Pe", "je", "Te", "Ce", "Le", "Se", "Me", "ke", "we", "Ne", "Ie", "Ae", "$e", "Be", "Ee", "Left", "Center", "m", "data", "config", "api", "readOnly", "block", "DEFAULT_ALIGNMENT", "quotePlaceholder", "i18n", "DEFAULT_QUOTE_PLACEHOLDER", "captionPlaceholder", "DEFAULT_CAPTION_PLACEHOLDER", "text", "caption", "alignment", "values", "defaultAlignment", "css", "baseClass", "styles", "wrapper", "input", "isReadOnlySupported", "toolbox", "icon", "title", "contentless", "enableLineBreaks", "conversionConfig", "import", "export", "CSS", "settings", "name", "render", "dataset", "placeholder", "save", "querySelector", "sanitize", "br", "renderSettings", "label", "onActivate", "_toggleTune", "isActive", "closeOnActivate", "dispatchChange"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/@editorjs/quote/dist/quote.mjs"], "sourcesContent": ["(function(){\"use strict\";try{if(typeof document<\"u\"){var t=document.createElement(\"style\");t.appendChild(document.createTextNode(\".cdx-quote-icon svg{transform:rotate(180deg)}.cdx-quote{margin:0}.cdx-quote__text{min-height:158px;margin-bottom:10px}.cdx-quote [contentEditable=true][data-placeholder]:before{position:absolute;content:attr(data-placeholder);color:#707684;font-weight:400;opacity:0}.cdx-quote [contentEditable=true][data-placeholder]:empty:before{opacity:1}.cdx-quote [contentEditable=true][data-placeholder]:empty:focus:before{opacity:0}.cdx-quote-settings{display:flex}.cdx-quote-settings .cdx-settings-button{width:50%}\")),document.head.appendChild(t)}}catch(e){console.error(\"vite-plugin-css-injected-by-js\",e)}})();\nconst De = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M18 7L6 7\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M18 17H6\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M16 12L8 12\"/></svg>', He = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M17 7L5 7\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M17 17H5\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M13 12L5 12\"/></svg>', Re = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10 10.8182L9 10.8182C8.80222 10.8182 8.60888 10.7649 8.44443 10.665C8.27998 10.5651 8.15181 10.4231 8.07612 10.257C8.00043 10.0909 7.98063 9.90808 8.01922 9.73174C8.0578 9.55539 8.15304 9.39341 8.29289 9.26627C8.43275 9.13913 8.61093 9.05255 8.80491 9.01747C8.99889 8.98239 9.19996 9.00039 9.38268 9.0692C9.56541 9.13801 9.72159 9.25453 9.83147 9.40403C9.94135 9.55353 10 9.72929 10 9.90909L10 12.1818C10 12.664 9.78929 13.1265 9.41421 13.4675C9.03914 13.8084 8.53043 14 8 14\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M16 10.8182L15 10.8182C14.8022 10.8182 14.6089 10.7649 14.4444 10.665C14.28 10.5651 14.1518 10.4231 14.0761 10.257C14.0004 10.0909 13.9806 9.90808 14.0192 9.73174C14.0578 9.55539 14.153 9.39341 14.2929 9.26627C14.4327 9.13913 14.6109 9.05255 14.8049 9.01747C14.9989 8.98239 15.2 9.00039 15.3827 9.0692C15.5654 9.13801 15.7216 9.25453 15.8315 9.40403C15.9414 9.55353 16 9.72929 16 9.90909L16 12.1818C16 12.664 15.7893 13.1265 15.4142 13.4675C15.0391 13.8084 14.5304 14 14 14\"/></svg>';\nvar b = typeof globalThis < \"u\" ? globalThis : typeof window < \"u\" ? window : typeof global < \"u\" ? global : typeof self < \"u\" ? self : {};\nfunction Fe(e) {\n  if (e.__esModule)\n    return e;\n  var t = e.default;\n  if (typeof t == \"function\") {\n    var n = function r() {\n      return this instanceof r ? Reflect.construct(t, arguments, this.constructor) : t.apply(this, arguments);\n    };\n    n.prototype = t.prototype;\n  } else\n    n = {};\n  return Object.defineProperty(n, \"__esModule\", { value: !0 }), Object.keys(e).forEach(function(r) {\n    var i = Object.getOwnPropertyDescriptor(e, r);\n    Object.defineProperty(n, r, i.get ? i : {\n      enumerable: !0,\n      get: function() {\n        return e[r];\n      }\n    });\n  }), n;\n}\nvar v = {}, P = {}, j = {};\nObject.defineProperty(j, \"__esModule\", { value: !0 });\nj.allInputsSelector = We;\nfunction We() {\n  var e = [\"text\", \"password\", \"email\", \"number\", \"search\", \"tel\", \"url\"];\n  return \"[contenteditable=true], textarea, input:not([type]), \" + e.map(function(t) {\n    return 'input[type=\"'.concat(t, '\"]');\n  }).join(\", \");\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.allInputsSelector = void 0;\n  var t = j;\n  Object.defineProperty(e, \"allInputsSelector\", { enumerable: !0, get: function() {\n    return t.allInputsSelector;\n  } });\n})(P);\nvar c = {}, T = {};\nObject.defineProperty(T, \"__esModule\", { value: !0 });\nT.isNativeInput = Ue;\nfunction Ue(e) {\n  var t = [\n    \"INPUT\",\n    \"TEXTAREA\"\n  ];\n  return e && e.tagName ? t.includes(e.tagName) : !1;\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.isNativeInput = void 0;\n  var t = T;\n  Object.defineProperty(e, \"isNativeInput\", { enumerable: !0, get: function() {\n    return t.isNativeInput;\n  } });\n})(c);\nvar ie = {}, C = {};\nObject.defineProperty(C, \"__esModule\", { value: !0 });\nC.append = qe;\nfunction qe(e, t) {\n  Array.isArray(t) ? t.forEach(function(n) {\n    e.appendChild(n);\n  }) : e.appendChild(t);\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.append = void 0;\n  var t = C;\n  Object.defineProperty(e, \"append\", { enumerable: !0, get: function() {\n    return t.append;\n  } });\n})(ie);\nvar L = {}, S = {};\nObject.defineProperty(S, \"__esModule\", { value: !0 });\nS.blockElements = ze;\nfunction ze() {\n  return [\n    \"address\",\n    \"article\",\n    \"aside\",\n    \"blockquote\",\n    \"canvas\",\n    \"div\",\n    \"dl\",\n    \"dt\",\n    \"fieldset\",\n    \"figcaption\",\n    \"figure\",\n    \"footer\",\n    \"form\",\n    \"h1\",\n    \"h2\",\n    \"h3\",\n    \"h4\",\n    \"h5\",\n    \"h6\",\n    \"header\",\n    \"hgroup\",\n    \"hr\",\n    \"li\",\n    \"main\",\n    \"nav\",\n    \"noscript\",\n    \"ol\",\n    \"output\",\n    \"p\",\n    \"pre\",\n    \"ruby\",\n    \"section\",\n    \"table\",\n    \"tbody\",\n    \"thead\",\n    \"tr\",\n    \"tfoot\",\n    \"ul\",\n    \"video\"\n  ];\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.blockElements = void 0;\n  var t = S;\n  Object.defineProperty(e, \"blockElements\", { enumerable: !0, get: function() {\n    return t.blockElements;\n  } });\n})(L);\nvar ae = {}, M = {};\nObject.defineProperty(M, \"__esModule\", { value: !0 });\nM.calculateBaseline = Ge;\nfunction Ge(e) {\n  var t = window.getComputedStyle(e), n = parseFloat(t.fontSize), r = parseFloat(t.lineHeight) || n * 1.2, i = parseFloat(t.paddingTop), a = parseFloat(t.borderTopWidth), l = parseFloat(t.marginTop), u = n * 0.8, d = (r - n) / 2, s = l + a + i + d + u;\n  return s;\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.calculateBaseline = void 0;\n  var t = M;\n  Object.defineProperty(e, \"calculateBaseline\", { enumerable: !0, get: function() {\n    return t.calculateBaseline;\n  } });\n})(ae);\nvar le = {}, k = {}, w = {}, N = {};\nObject.defineProperty(N, \"__esModule\", { value: !0 });\nN.isContentEditable = Ke;\nfunction Ke(e) {\n  return e.contentEditable === \"true\";\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.isContentEditable = void 0;\n  var t = N;\n  Object.defineProperty(e, \"isContentEditable\", { enumerable: !0, get: function() {\n    return t.isContentEditable;\n  } });\n})(w);\nObject.defineProperty(k, \"__esModule\", { value: !0 });\nk.canSetCaret = Qe;\nvar Xe = c, Ye = w;\nfunction Qe(e) {\n  var t = !0;\n  if ((0, Xe.isNativeInput)(e))\n    switch (e.type) {\n      case \"file\":\n      case \"checkbox\":\n      case \"radio\":\n      case \"hidden\":\n      case \"submit\":\n      case \"button\":\n      case \"image\":\n      case \"reset\":\n        t = !1;\n        break;\n    }\n  else\n    t = (0, Ye.isContentEditable)(e);\n  return t;\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.canSetCaret = void 0;\n  var t = k;\n  Object.defineProperty(e, \"canSetCaret\", { enumerable: !0, get: function() {\n    return t.canSetCaret;\n  } });\n})(le);\nvar y = {}, I = {};\nfunction Ve(e, t, n) {\n  const r = n.value !== void 0 ? \"value\" : \"get\", i = n[r], a = `#${t}Cache`;\n  if (n[r] = function(...l) {\n    return this[a] === void 0 && (this[a] = i.apply(this, l)), this[a];\n  }, r === \"get\" && n.set) {\n    const l = n.set;\n    n.set = function(u) {\n      delete e[a], l.apply(this, u);\n    };\n  }\n  return n;\n}\nfunction ue() {\n  const e = {\n    win: !1,\n    mac: !1,\n    x11: !1,\n    linux: !1\n  }, t = Object.keys(e).find((n) => window.navigator.appVersion.toLowerCase().indexOf(n) !== -1);\n  return t !== void 0 && (e[t] = !0), e;\n}\nfunction A(e) {\n  return e != null && e !== \"\" && (typeof e != \"object\" || Object.keys(e).length > 0);\n}\nfunction Ze(e) {\n  return !A(e);\n}\nconst Je = () => typeof window < \"u\" && window.navigator !== null && A(window.navigator.platform) && (/iP(ad|hone|od)/.test(window.navigator.platform) || window.navigator.platform === \"MacIntel\" && window.navigator.maxTouchPoints > 1);\nfunction xe(e) {\n  const t = ue();\n  return e = e.replace(/shift/gi, \"⇧\").replace(/backspace/gi, \"⌫\").replace(/enter/gi, \"⏎\").replace(/up/gi, \"↑\").replace(/left/gi, \"→\").replace(/down/gi, \"↓\").replace(/right/gi, \"←\").replace(/escape/gi, \"⎋\").replace(/insert/gi, \"Ins\").replace(/delete/gi, \"␡\").replace(/\\+/gi, \"+\"), t.mac ? e = e.replace(/ctrl|cmd/gi, \"⌘\").replace(/alt/gi, \"⌥\") : e = e.replace(/cmd/gi, \"Ctrl\").replace(/windows/gi, \"WIN\"), e;\n}\nfunction et(e) {\n  return e[0].toUpperCase() + e.slice(1);\n}\nfunction tt(e) {\n  const t = document.createElement(\"div\");\n  t.style.position = \"absolute\", t.style.left = \"-999px\", t.style.bottom = \"-999px\", t.innerHTML = e, document.body.appendChild(t);\n  const n = window.getSelection(), r = document.createRange();\n  if (r.selectNode(t), n === null)\n    throw new Error(\"Cannot copy text to clipboard\");\n  n.removeAllRanges(), n.addRange(r), document.execCommand(\"copy\"), document.body.removeChild(t);\n}\nfunction nt(e, t, n) {\n  let r;\n  return (...i) => {\n    const a = this, l = () => {\n      r = void 0, n !== !0 && e.apply(a, i);\n    }, u = n === !0 && r !== void 0;\n    window.clearTimeout(r), r = window.setTimeout(l, t), u && e.apply(a, i);\n  };\n}\nfunction o(e) {\n  return Object.prototype.toString.call(e).match(/\\s([a-zA-Z]+)/)[1].toLowerCase();\n}\nfunction rt(e) {\n  return o(e) === \"boolean\";\n}\nfunction oe(e) {\n  return o(e) === \"function\" || o(e) === \"asyncfunction\";\n}\nfunction it(e) {\n  return oe(e) && /^\\s*class\\s+/.test(e.toString());\n}\nfunction at(e) {\n  return o(e) === \"number\";\n}\nfunction g(e) {\n  return o(e) === \"object\";\n}\nfunction lt(e) {\n  return Promise.resolve(e) === e;\n}\nfunction ut(e) {\n  return o(e) === \"string\";\n}\nfunction ot(e) {\n  return o(e) === \"undefined\";\n}\nfunction O(e, ...t) {\n  if (!t.length)\n    return e;\n  const n = t.shift();\n  if (g(e) && g(n))\n    for (const r in n)\n      g(n[r]) ? (e[r] === void 0 && Object.assign(e, { [r]: {} }), O(e[r], n[r])) : Object.assign(e, { [r]: n[r] });\n  return O(e, ...t);\n}\nfunction st(e, t, n) {\n  const r = `«${t}» is deprecated and will be removed in the next major release. Please use the «${n}» instead.`;\n  e && console.warn(r);\n}\nfunction ct(e) {\n  try {\n    return new URL(e).href;\n  } catch {\n  }\n  return e.substring(0, 2) === \"//\" ? window.location.protocol + e : window.location.origin + e;\n}\nfunction dt(e) {\n  return e > 47 && e < 58 || e === 32 || e === 13 || e === 229 || e > 64 && e < 91 || e > 95 && e < 112 || e > 185 && e < 193 || e > 218 && e < 223;\n}\nconst ft = {\n  BACKSPACE: 8,\n  TAB: 9,\n  ENTER: 13,\n  SHIFT: 16,\n  CTRL: 17,\n  ALT: 18,\n  ESC: 27,\n  SPACE: 32,\n  LEFT: 37,\n  UP: 38,\n  DOWN: 40,\n  RIGHT: 39,\n  DELETE: 46,\n  META: 91,\n  SLASH: 191\n}, pt = {\n  LEFT: 0,\n  WHEEL: 1,\n  RIGHT: 2,\n  BACKWARD: 3,\n  FORWARD: 4\n};\nclass vt {\n  constructor() {\n    this.completed = Promise.resolve();\n  }\n  /**\n   * Add new promise to queue\n   * @param operation - promise should be added to queue\n   */\n  add(t) {\n    return new Promise((n, r) => {\n      this.completed = this.completed.then(t).then(n).catch(r);\n    });\n  }\n}\nfunction gt(e, t, n = void 0) {\n  let r, i, a, l = null, u = 0;\n  n || (n = {});\n  const d = function() {\n    u = n.leading === !1 ? 0 : Date.now(), l = null, a = e.apply(r, i), l === null && (r = i = null);\n  };\n  return function() {\n    const s = Date.now();\n    !u && n.leading === !1 && (u = s);\n    const f = t - (s - u);\n    return r = this, i = arguments, f <= 0 || f > t ? (l && (clearTimeout(l), l = null), u = s, a = e.apply(r, i), l === null && (r = i = null)) : !l && n.trailing !== !1 && (l = setTimeout(d, f)), a;\n  };\n}\nconst mt = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({\n  __proto__: null,\n  PromiseQueue: vt,\n  beautifyShortcut: xe,\n  cacheable: Ve,\n  capitalize: et,\n  copyTextToClipboard: tt,\n  debounce: nt,\n  deepMerge: O,\n  deprecationAssert: st,\n  getUserOS: ue,\n  getValidUrl: ct,\n  isBoolean: rt,\n  isClass: it,\n  isEmpty: Ze,\n  isFunction: oe,\n  isIosDevice: Je,\n  isNumber: at,\n  isObject: g,\n  isPrintableKey: dt,\n  isPromise: lt,\n  isString: ut,\n  isUndefined: ot,\n  keyCodes: ft,\n  mouseButtons: pt,\n  notEmpty: A,\n  throttle: gt,\n  typeOf: o\n}, Symbol.toStringTag, { value: \"Module\" })), $ = /* @__PURE__ */ Fe(mt);\nObject.defineProperty(I, \"__esModule\", { value: !0 });\nI.containsOnlyInlineElements = _t;\nvar bt = $, yt = L;\nfunction _t(e) {\n  var t;\n  (0, bt.isString)(e) ? (t = document.createElement(\"div\"), t.innerHTML = e) : t = e;\n  var n = function(r) {\n    return !(0, yt.blockElements)().includes(r.tagName.toLowerCase()) && Array.from(r.children).every(n);\n  };\n  return Array.from(t.children).every(n);\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.containsOnlyInlineElements = void 0;\n  var t = I;\n  Object.defineProperty(e, \"containsOnlyInlineElements\", { enumerable: !0, get: function() {\n    return t.containsOnlyInlineElements;\n  } });\n})(y);\nvar se = {}, B = {}, _ = {}, D = {};\nObject.defineProperty(D, \"__esModule\", { value: !0 });\nD.make = ht;\nfunction ht(e, t, n) {\n  var r;\n  t === void 0 && (t = null), n === void 0 && (n = {});\n  var i = document.createElement(e);\n  if (Array.isArray(t)) {\n    var a = t.filter(function(u) {\n      return u !== void 0;\n    });\n    (r = i.classList).add.apply(r, a);\n  } else\n    t !== null && i.classList.add(t);\n  for (var l in n)\n    Object.prototype.hasOwnProperty.call(n, l) && (i[l] = n[l]);\n  return i;\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.make = void 0;\n  var t = D;\n  Object.defineProperty(e, \"make\", { enumerable: !0, get: function() {\n    return t.make;\n  } });\n})(_);\nObject.defineProperty(B, \"__esModule\", { value: !0 });\nB.fragmentToString = Ot;\nvar Et = _;\nfunction Ot(e) {\n  var t = (0, Et.make)(\"div\");\n  return t.appendChild(e), t.innerHTML;\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.fragmentToString = void 0;\n  var t = B;\n  Object.defineProperty(e, \"fragmentToString\", { enumerable: !0, get: function() {\n    return t.fragmentToString;\n  } });\n})(se);\nvar ce = {}, H = {};\nObject.defineProperty(H, \"__esModule\", { value: !0 });\nH.getContentLength = jt;\nvar Pt = c;\nfunction jt(e) {\n  var t, n;\n  return (0, Pt.isNativeInput)(e) ? e.value.length : e.nodeType === Node.TEXT_NODE ? e.length : (n = (t = e.textContent) === null || t === void 0 ? void 0 : t.length) !== null && n !== void 0 ? n : 0;\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.getContentLength = void 0;\n  var t = H;\n  Object.defineProperty(e, \"getContentLength\", { enumerable: !0, get: function() {\n    return t.getContentLength;\n  } });\n})(ce);\nvar R = {}, F = {}, re = b && b.__spreadArray || function(e, t, n) {\n  if (n || arguments.length === 2)\n    for (var r = 0, i = t.length, a; r < i; r++)\n      (a || !(r in t)) && (a || (a = Array.prototype.slice.call(t, 0, r)), a[r] = t[r]);\n  return e.concat(a || Array.prototype.slice.call(t));\n};\nObject.defineProperty(F, \"__esModule\", { value: !0 });\nF.getDeepestBlockElements = de;\nvar Tt = y;\nfunction de(e) {\n  return (0, Tt.containsOnlyInlineElements)(e) ? [e] : Array.from(e.children).reduce(function(t, n) {\n    return re(re([], t, !0), de(n), !0);\n  }, []);\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.getDeepestBlockElements = void 0;\n  var t = F;\n  Object.defineProperty(e, \"getDeepestBlockElements\", { enumerable: !0, get: function() {\n    return t.getDeepestBlockElements;\n  } });\n})(R);\nvar fe = {}, W = {}, h = {}, U = {};\nObject.defineProperty(U, \"__esModule\", { value: !0 });\nU.isLineBreakTag = Ct;\nfunction Ct(e) {\n  return [\n    \"BR\",\n    \"WBR\"\n  ].includes(e.tagName);\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.isLineBreakTag = void 0;\n  var t = U;\n  Object.defineProperty(e, \"isLineBreakTag\", { enumerable: !0, get: function() {\n    return t.isLineBreakTag;\n  } });\n})(h);\nvar E = {}, q = {};\nObject.defineProperty(q, \"__esModule\", { value: !0 });\nq.isSingleTag = Lt;\nfunction Lt(e) {\n  return [\n    \"AREA\",\n    \"BASE\",\n    \"BR\",\n    \"COL\",\n    \"COMMAND\",\n    \"EMBED\",\n    \"HR\",\n    \"IMG\",\n    \"INPUT\",\n    \"KEYGEN\",\n    \"LINK\",\n    \"META\",\n    \"PARAM\",\n    \"SOURCE\",\n    \"TRACK\",\n    \"WBR\"\n  ].includes(e.tagName);\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.isSingleTag = void 0;\n  var t = q;\n  Object.defineProperty(e, \"isSingleTag\", { enumerable: !0, get: function() {\n    return t.isSingleTag;\n  } });\n})(E);\nObject.defineProperty(W, \"__esModule\", { value: !0 });\nW.getDeepestNode = pe;\nvar St = c, Mt = h, kt = E;\nfunction pe(e, t) {\n  t === void 0 && (t = !1);\n  var n = t ? \"lastChild\" : \"firstChild\", r = t ? \"previousSibling\" : \"nextSibling\";\n  if (e.nodeType === Node.ELEMENT_NODE && e[n]) {\n    var i = e[n];\n    if ((0, kt.isSingleTag)(i) && !(0, St.isNativeInput)(i) && !(0, Mt.isLineBreakTag)(i))\n      if (i[r])\n        i = i[r];\n      else if (i.parentNode !== null && i.parentNode[r])\n        i = i.parentNode[r];\n      else\n        return i.parentNode;\n    return pe(i, t);\n  }\n  return e;\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.getDeepestNode = void 0;\n  var t = W;\n  Object.defineProperty(e, \"getDeepestNode\", { enumerable: !0, get: function() {\n    return t.getDeepestNode;\n  } });\n})(fe);\nvar ve = {}, z = {}, p = b && b.__spreadArray || function(e, t, n) {\n  if (n || arguments.length === 2)\n    for (var r = 0, i = t.length, a; r < i; r++)\n      (a || !(r in t)) && (a || (a = Array.prototype.slice.call(t, 0, r)), a[r] = t[r]);\n  return e.concat(a || Array.prototype.slice.call(t));\n};\nObject.defineProperty(z, \"__esModule\", { value: !0 });\nz.findAllInputs = $t;\nvar wt = y, Nt = R, It = P, At = c;\nfunction $t(e) {\n  return Array.from(e.querySelectorAll((0, It.allInputsSelector)())).reduce(function(t, n) {\n    return (0, At.isNativeInput)(n) || (0, wt.containsOnlyInlineElements)(n) ? p(p([], t, !0), [n], !1) : p(p([], t, !0), (0, Nt.getDeepestBlockElements)(n), !0);\n  }, []);\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.findAllInputs = void 0;\n  var t = z;\n  Object.defineProperty(e, \"findAllInputs\", { enumerable: !0, get: function() {\n    return t.findAllInputs;\n  } });\n})(ve);\nvar ge = {}, G = {};\nObject.defineProperty(G, \"__esModule\", { value: !0 });\nG.isCollapsedWhitespaces = Bt;\nfunction Bt(e) {\n  return !/[^\\t\\n\\r ]/.test(e);\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.isCollapsedWhitespaces = void 0;\n  var t = G;\n  Object.defineProperty(e, \"isCollapsedWhitespaces\", { enumerable: !0, get: function() {\n    return t.isCollapsedWhitespaces;\n  } });\n})(ge);\nvar K = {}, X = {};\nObject.defineProperty(X, \"__esModule\", { value: !0 });\nX.isElement = Ht;\nvar Dt = $;\nfunction Ht(e) {\n  return (0, Dt.isNumber)(e) ? !1 : !!e && !!e.nodeType && e.nodeType === Node.ELEMENT_NODE;\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.isElement = void 0;\n  var t = X;\n  Object.defineProperty(e, \"isElement\", { enumerable: !0, get: function() {\n    return t.isElement;\n  } });\n})(K);\nvar me = {}, Y = {}, Q = {}, V = {};\nObject.defineProperty(V, \"__esModule\", { value: !0 });\nV.isLeaf = Rt;\nfunction Rt(e) {\n  return e === null ? !1 : e.childNodes.length === 0;\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.isLeaf = void 0;\n  var t = V;\n  Object.defineProperty(e, \"isLeaf\", { enumerable: !0, get: function() {\n    return t.isLeaf;\n  } });\n})(Q);\nvar Z = {}, J = {};\nObject.defineProperty(J, \"__esModule\", { value: !0 });\nJ.isNodeEmpty = zt;\nvar Ft = h, Wt = K, Ut = c, qt = E;\nfunction zt(e, t) {\n  var n = \"\";\n  return (0, qt.isSingleTag)(e) && !(0, Ft.isLineBreakTag)(e) ? !1 : ((0, Wt.isElement)(e) && (0, Ut.isNativeInput)(e) ? n = e.value : e.textContent !== null && (n = e.textContent.replace(\"​\", \"\")), t !== void 0 && (n = n.replace(new RegExp(t, \"g\"), \"\")), n.trim().length === 0);\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.isNodeEmpty = void 0;\n  var t = J;\n  Object.defineProperty(e, \"isNodeEmpty\", { enumerable: !0, get: function() {\n    return t.isNodeEmpty;\n  } });\n})(Z);\nObject.defineProperty(Y, \"__esModule\", { value: !0 });\nY.isEmpty = Xt;\nvar Gt = Q, Kt = Z;\nfunction Xt(e, t) {\n  e.normalize();\n  for (var n = [e]; n.length > 0; ) {\n    var r = n.shift();\n    if (r) {\n      if (e = r, (0, Gt.isLeaf)(e) && !(0, Kt.isNodeEmpty)(e, t))\n        return !1;\n      n.push.apply(n, Array.from(e.childNodes));\n    }\n  }\n  return !0;\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.isEmpty = void 0;\n  var t = Y;\n  Object.defineProperty(e, \"isEmpty\", { enumerable: !0, get: function() {\n    return t.isEmpty;\n  } });\n})(me);\nvar be = {}, x = {};\nObject.defineProperty(x, \"__esModule\", { value: !0 });\nx.isFragment = Qt;\nvar Yt = $;\nfunction Qt(e) {\n  return (0, Yt.isNumber)(e) ? !1 : !!e && !!e.nodeType && e.nodeType === Node.DOCUMENT_FRAGMENT_NODE;\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.isFragment = void 0;\n  var t = x;\n  Object.defineProperty(e, \"isFragment\", { enumerable: !0, get: function() {\n    return t.isFragment;\n  } });\n})(be);\nvar ye = {}, ee = {};\nObject.defineProperty(ee, \"__esModule\", { value: !0 });\nee.isHTMLString = Zt;\nvar Vt = _;\nfunction Zt(e) {\n  var t = (0, Vt.make)(\"div\");\n  return t.innerHTML = e, t.childElementCount > 0;\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.isHTMLString = void 0;\n  var t = ee;\n  Object.defineProperty(e, \"isHTMLString\", { enumerable: !0, get: function() {\n    return t.isHTMLString;\n  } });\n})(ye);\nvar _e = {}, te = {};\nObject.defineProperty(te, \"__esModule\", { value: !0 });\nte.offset = Jt;\nfunction Jt(e) {\n  var t = e.getBoundingClientRect(), n = window.pageXOffset || document.documentElement.scrollLeft, r = window.pageYOffset || document.documentElement.scrollTop, i = t.top + r, a = t.left + n;\n  return {\n    top: i,\n    left: a,\n    bottom: i + t.height,\n    right: a + t.width\n  };\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.offset = void 0;\n  var t = te;\n  Object.defineProperty(e, \"offset\", { enumerable: !0, get: function() {\n    return t.offset;\n  } });\n})(_e);\nvar he = {}, ne = {};\nObject.defineProperty(ne, \"__esModule\", { value: !0 });\nne.prepend = xt;\nfunction xt(e, t) {\n  Array.isArray(t) ? (t = t.reverse(), t.forEach(function(n) {\n    return e.prepend(n);\n  })) : e.prepend(t);\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.prepend = void 0;\n  var t = ne;\n  Object.defineProperty(e, \"prepend\", { enumerable: !0, get: function() {\n    return t.prepend;\n  } });\n})(he);\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.prepend = e.offset = e.make = e.isLineBreakTag = e.isSingleTag = e.isNodeEmpty = e.isLeaf = e.isHTMLString = e.isFragment = e.isEmpty = e.isElement = e.isContentEditable = e.isCollapsedWhitespaces = e.findAllInputs = e.isNativeInput = e.allInputsSelector = e.getDeepestNode = e.getDeepestBlockElements = e.getContentLength = e.fragmentToString = e.containsOnlyInlineElements = e.canSetCaret = e.calculateBaseline = e.blockElements = e.append = void 0;\n  var t = P;\n  Object.defineProperty(e, \"allInputsSelector\", { enumerable: !0, get: function() {\n    return t.allInputsSelector;\n  } });\n  var n = c;\n  Object.defineProperty(e, \"isNativeInput\", { enumerable: !0, get: function() {\n    return n.isNativeInput;\n  } });\n  var r = ie;\n  Object.defineProperty(e, \"append\", { enumerable: !0, get: function() {\n    return r.append;\n  } });\n  var i = L;\n  Object.defineProperty(e, \"blockElements\", { enumerable: !0, get: function() {\n    return i.blockElements;\n  } });\n  var a = ae;\n  Object.defineProperty(e, \"calculateBaseline\", { enumerable: !0, get: function() {\n    return a.calculateBaseline;\n  } });\n  var l = le;\n  Object.defineProperty(e, \"canSetCaret\", { enumerable: !0, get: function() {\n    return l.canSetCaret;\n  } });\n  var u = y;\n  Object.defineProperty(e, \"containsOnlyInlineElements\", { enumerable: !0, get: function() {\n    return u.containsOnlyInlineElements;\n  } });\n  var d = se;\n  Object.defineProperty(e, \"fragmentToString\", { enumerable: !0, get: function() {\n    return d.fragmentToString;\n  } });\n  var s = ce;\n  Object.defineProperty(e, \"getContentLength\", { enumerable: !0, get: function() {\n    return s.getContentLength;\n  } });\n  var f = R;\n  Object.defineProperty(e, \"getDeepestBlockElements\", { enumerable: !0, get: function() {\n    return f.getDeepestBlockElements;\n  } });\n  var Oe = fe;\n  Object.defineProperty(e, \"getDeepestNode\", { enumerable: !0, get: function() {\n    return Oe.getDeepestNode;\n  } });\n  var Pe = ve;\n  Object.defineProperty(e, \"findAllInputs\", { enumerable: !0, get: function() {\n    return Pe.findAllInputs;\n  } });\n  var je = ge;\n  Object.defineProperty(e, \"isCollapsedWhitespaces\", { enumerable: !0, get: function() {\n    return je.isCollapsedWhitespaces;\n  } });\n  var Te = w;\n  Object.defineProperty(e, \"isContentEditable\", { enumerable: !0, get: function() {\n    return Te.isContentEditable;\n  } });\n  var Ce = K;\n  Object.defineProperty(e, \"isElement\", { enumerable: !0, get: function() {\n    return Ce.isElement;\n  } });\n  var Le = me;\n  Object.defineProperty(e, \"isEmpty\", { enumerable: !0, get: function() {\n    return Le.isEmpty;\n  } });\n  var Se = be;\n  Object.defineProperty(e, \"isFragment\", { enumerable: !0, get: function() {\n    return Se.isFragment;\n  } });\n  var Me = ye;\n  Object.defineProperty(e, \"isHTMLString\", { enumerable: !0, get: function() {\n    return Me.isHTMLString;\n  } });\n  var ke = Q;\n  Object.defineProperty(e, \"isLeaf\", { enumerable: !0, get: function() {\n    return ke.isLeaf;\n  } });\n  var we = Z;\n  Object.defineProperty(e, \"isNodeEmpty\", { enumerable: !0, get: function() {\n    return we.isNodeEmpty;\n  } });\n  var Ne = h;\n  Object.defineProperty(e, \"isLineBreakTag\", { enumerable: !0, get: function() {\n    return Ne.isLineBreakTag;\n  } });\n  var Ie = E;\n  Object.defineProperty(e, \"isSingleTag\", { enumerable: !0, get: function() {\n    return Ie.isSingleTag;\n  } });\n  var Ae = _;\n  Object.defineProperty(e, \"make\", { enumerable: !0, get: function() {\n    return Ae.make;\n  } });\n  var $e = _e;\n  Object.defineProperty(e, \"offset\", { enumerable: !0, get: function() {\n    return $e.offset;\n  } });\n  var Be = he;\n  Object.defineProperty(e, \"prepend\", { enumerable: !0, get: function() {\n    return Be.prepend;\n  } });\n})(v);\nvar Ee = /* @__PURE__ */ ((e) => (e.Left = \"left\", e.Center = \"center\", e))(Ee || {});\nclass m {\n  /**\n   * Render plugin`s main Element and fill it with saved data\n   * @param params - Quote Tool constructor params\n   * @param params.data - previously saved data\n   * @param params.config - user config for Tool\n   * @param params.api - editor.js api\n   * @param params.readOnly - read only mode flag\n   */\n  constructor({ data: t, config: n, api: r, readOnly: i, block: a }) {\n    const { DEFAULT_ALIGNMENT: l } = m;\n    this.api = r, this.readOnly = i, this.quotePlaceholder = r.i18n.t((n == null ? void 0 : n.quotePlaceholder) ?? m.DEFAULT_QUOTE_PLACEHOLDER), this.captionPlaceholder = r.i18n.t((n == null ? void 0 : n.captionPlaceholder) ?? m.DEFAULT_CAPTION_PLACEHOLDER), this.data = {\n      text: t.text || \"\",\n      caption: t.caption || \"\",\n      alignment: Object.values(Ee).includes(t.alignment) ? t.alignment : (n == null ? void 0 : n.defaultAlignment) ?? l\n    }, this.css = {\n      baseClass: this.api.styles.block,\n      wrapper: \"cdx-quote\",\n      text: \"cdx-quote__text\",\n      input: this.api.styles.input,\n      caption: \"cdx-quote__caption\"\n    }, this.block = a;\n  }\n  /**\n   * Notify core that read-only mode is supported\n   * @returns true\n   */\n  static get isReadOnlySupported() {\n    return !0;\n  }\n  /**\n   * Get Tool toolbox settings\n   * icon - Tool icon's SVG\n   * title - title to show in toolbox\n   * @returns icon and title of the toolbox\n   */\n  static get toolbox() {\n    return {\n      icon: Re,\n      title: \"Quote\"\n    };\n  }\n  /**\n   * Empty Quote is not empty Block\n   * @returns true\n   */\n  static get contentless() {\n    return !0;\n  }\n  /**\n   * Allow to press Enter inside the Quote\n   * @returns true\n   */\n  static get enableLineBreaks() {\n    return !0;\n  }\n  /**\n   * Default placeholder for quote text\n   * @returns 'Enter a quote'\n   */\n  static get DEFAULT_QUOTE_PLACEHOLDER() {\n    return \"Enter a quote\";\n  }\n  /**\n   * Default placeholder for quote caption\n   * @returns 'Enter a caption'\n   */\n  static get DEFAULT_CAPTION_PLACEHOLDER() {\n    return \"Enter a caption\";\n  }\n  /**\n   * Default quote alignment\n   * @returns Alignment.Left\n   */\n  static get DEFAULT_ALIGNMENT() {\n    return \"left\";\n  }\n  /**\n   * Allow Quote to be converted to/from other blocks\n   * @returns conversion config object\n   */\n  static get conversionConfig() {\n    return {\n      /**\n       * To create Quote data from string, simple fill 'text' property\n       */\n      import: \"text\",\n      /**\n       * To create string from Quote data, concatenate text and caption\n       * @param quoteData - Quote data object\n       * @returns string\n       */\n      export: function(t) {\n        return t.caption ? `${t.text} — ${t.caption}` : t.text;\n      }\n    };\n  }\n  /**\n   * Tool`s styles\n   * @returns CSS classes names\n   */\n  get CSS() {\n    return {\n      baseClass: this.api.styles.block,\n      wrapper: \"cdx-quote\",\n      text: \"cdx-quote__text\",\n      input: this.api.styles.input,\n      caption: \"cdx-quote__caption\"\n    };\n  }\n  /**\n   * Tool`s settings properties\n   * @returns settings properties\n   */\n  get settings() {\n    return [\n      {\n        name: \"left\",\n        icon: He\n      },\n      {\n        name: \"center\",\n        icon: De\n      }\n    ];\n  }\n  /**\n   * Create Quote Tool container with inputs\n   * @returns blockquote DOM element - Quote Tool container\n   */\n  render() {\n    const t = v.make(\"blockquote\", [\n      this.css.baseClass,\n      this.css.wrapper\n    ]), n = v.make(\"div\", [this.css.input, this.css.text], {\n      contentEditable: !this.readOnly,\n      innerHTML: this.data.text\n    }), r = v.make(\"div\", [this.css.input, this.css.caption], {\n      contentEditable: !this.readOnly,\n      innerHTML: this.data.caption\n    });\n    return n.dataset.placeholder = this.quotePlaceholder, r.dataset.placeholder = this.captionPlaceholder, t.appendChild(n), t.appendChild(r), t;\n  }\n  /**\n   * Extract Quote data from Quote Tool element\n   * @param quoteElement - Quote DOM element to save\n   * @returns Quote data object\n   */\n  save(t) {\n    const n = t.querySelector(`.${this.css.text}`), r = t.querySelector(`.${this.css.caption}`);\n    return Object.assign(this.data, {\n      text: (n == null ? void 0 : n.innerHTML) ?? \"\",\n      caption: (r == null ? void 0 : r.innerHTML) ?? \"\"\n    });\n  }\n  /**\n   * Sanitizer rules\n   * @returns sanitizer rules\n   */\n  static get sanitize() {\n    return {\n      text: {\n        br: !0\n      },\n      caption: {\n        br: !0\n      },\n      alignment: {}\n    };\n  }\n  /**\n   * Create wrapper for Tool`s settings buttons:\n   * 1. Left alignment\n   * 2. Center alignment\n   * @returns settings menu\n   */\n  renderSettings() {\n    const t = (n) => n && n[0].toUpperCase() + n.slice(1);\n    return this.settings.map((n) => ({\n      icon: n.icon,\n      label: this.api.i18n.t(`Align ${t(n.name)}`),\n      onActivate: () => this._toggleTune(n.name),\n      isActive: this.data.alignment === n.name,\n      closeOnActivate: !0\n    }));\n  }\n  /**\n   * Toggle quote`s alignment\n   * @param tune - alignment\n   */\n  _toggleTune(t) {\n    this.data.alignment = t, this.block.dispatchChange();\n  }\n}\nexport {\n  m as default\n};\n"], "mappings": "AAAA,CAAC,YAAU;EAAC,YAAY;;EAAC,IAAG;IAAC,IAAG,OAAOA,QAAQ,GAAC,GAAG,EAAC;MAAC,IAAIC,CAAC,GAACD,QAAQ,CAACE,aAAa,CAAC,OAAO,CAAC;MAACD,CAAC,CAACE,WAAW,CAACH,QAAQ,CAACI,cAAc,CAAC,4fAA4f,CAAC,CAAC,EAACJ,QAAQ,CAACK,IAAI,CAACF,WAAW,CAACF,CAAC,CAAC;IAAA;EAAC,CAAC,QAAMK,CAAC,EAAC;IAACC,OAAO,CAACC,KAAK,CAAC,gCAAgC,EAACF,CAAC,CAAC;EAAA;AAAC,CAAC,EAAE,CAAC;AAC7tB,MAAMG,EAAE,GAAG,iWAAiW;EAAEC,EAAE,GAAG,iWAAiW;EAAEC,EAAE,GAAG,guCAAguC;AAC37D,IAAIC,CAAC,GAAG,OAAOC,UAAU,GAAG,GAAG,GAAGA,UAAU,GAAG,OAAOC,MAAM,GAAG,GAAG,GAAGA,MAAM,GAAG,OAAOC,MAAM,GAAG,GAAG,GAAGA,MAAM,GAAG,OAAOC,IAAI,GAAG,GAAG,GAAGA,IAAI,GAAG,CAAC,CAAC;AAC1I,SAASC,EAAEA,CAACX,CAAC,EAAE;EACb,IAAIA,CAAC,CAACY,UAAU,EACd,OAAOZ,CAAC;EACV,IAAIL,CAAC,GAAGK,CAAC,CAACa,OAAO;EACjB,IAAI,OAAOlB,CAAC,IAAI,UAAU,EAAE;IAC1B,IAAImB,CAAC,GAAG,SAASC,CAACA,CAAA,EAAG;MACnB,OAAO,IAAI,YAAYA,CAAC,GAAGC,OAAO,CAACC,SAAS,CAACtB,CAAC,EAAEuB,SAAS,EAAE,IAAI,CAACC,WAAW,CAAC,GAAGxB,CAAC,CAACyB,KAAK,CAAC,IAAI,EAAEF,SAAS,CAAC;IACzG,CAAC;IACDJ,CAAC,CAACO,SAAS,GAAG1B,CAAC,CAAC0B,SAAS;EAC3B,CAAC,MACCP,CAAC,GAAG,CAAC,CAAC;EACR,OAAOQ,MAAM,CAACC,cAAc,CAACT,CAAC,EAAE,YAAY,EAAE;IAAEU,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAEF,MAAM,CAACG,IAAI,CAACzB,CAAC,CAAC,CAAC0B,OAAO,CAAC,UAASX,CAAC,EAAE;IAC/F,IAAIY,CAAC,GAAGL,MAAM,CAACM,wBAAwB,CAAC5B,CAAC,EAAEe,CAAC,CAAC;IAC7CO,MAAM,CAACC,cAAc,CAACT,CAAC,EAAEC,CAAC,EAAEY,CAAC,CAACE,GAAG,GAAGF,CAAC,GAAG;MACtCG,UAAU,EAAE,CAAC,CAAC;MACdD,GAAG,EAAE,SAAAA,CAAA,EAAW;QACd,OAAO7B,CAAC,CAACe,CAAC,CAAC;MACb;IACF,CAAC,CAAC;EACJ,CAAC,CAAC,EAAED,CAAC;AACP;AACA,IAAIiB,CAAC,GAAG,CAAC,CAAC;EAAEC,CAAC,GAAG,CAAC,CAAC;EAAEC,CAAC,GAAG,CAAC,CAAC;AAC1BX,MAAM,CAACC,cAAc,CAACU,CAAC,EAAE,YAAY,EAAE;EAAET,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACrDS,CAAC,CAACC,iBAAiB,GAAGC,EAAE;AACxB,SAASA,EAAEA,CAAA,EAAG;EACZ,IAAInC,CAAC,GAAG,CAAC,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,CAAC;EACvE,OAAO,uDAAuD,GAAGA,CAAC,CAACoC,GAAG,CAAC,UAASzC,CAAC,EAAE;IACjF,OAAO,cAAc,CAAC0C,MAAM,CAAC1C,CAAC,EAAE,IAAI,CAAC;EACvC,CAAC,CAAC,CAAC2C,IAAI,CAAC,IAAI,CAAC;AACf;AACA,CAAC,UAAStC,CAAC,EAAE;EACXsB,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,YAAY,EAAE;IAAEwB,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAExB,CAAC,CAACkC,iBAAiB,GAAG,KAAK,CAAC;EACnF,IAAIvC,CAAC,GAAGsC,CAAC;EACTX,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,mBAAmB,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAC9E,OAAOlC,CAAC,CAACuC,iBAAiB;IAC5B;EAAE,CAAC,CAAC;AACN,CAAC,EAAEF,CAAC,CAAC;AACL,IAAIO,CAAC,GAAG,CAAC,CAAC;EAAEC,CAAC,GAAG,CAAC,CAAC;AAClBlB,MAAM,CAACC,cAAc,CAACiB,CAAC,EAAE,YAAY,EAAE;EAAEhB,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACrDgB,CAAC,CAACC,aAAa,GAAGC,EAAE;AACpB,SAASA,EAAEA,CAAC1C,CAAC,EAAE;EACb,IAAIL,CAAC,GAAG,CACN,OAAO,EACP,UAAU,CACX;EACD,OAAOK,CAAC,IAAIA,CAAC,CAAC2C,OAAO,GAAGhD,CAAC,CAACiD,QAAQ,CAAC5C,CAAC,CAAC2C,OAAO,CAAC,GAAG,CAAC,CAAC;AACpD;AACA,CAAC,UAAS3C,CAAC,EAAE;EACXsB,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,YAAY,EAAE;IAAEwB,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAExB,CAAC,CAACyC,aAAa,GAAG,KAAK,CAAC;EAC/E,IAAI9C,CAAC,GAAG6C,CAAC;EACTlB,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,eAAe,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAC1E,OAAOlC,CAAC,CAAC8C,aAAa;IACxB;EAAE,CAAC,CAAC;AACN,CAAC,EAAEF,CAAC,CAAC;AACL,IAAIM,EAAE,GAAG,CAAC,CAAC;EAAEC,CAAC,GAAG,CAAC,CAAC;AACnBxB,MAAM,CAACC,cAAc,CAACuB,CAAC,EAAE,YAAY,EAAE;EAAEtB,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACrDsB,CAAC,CAACC,MAAM,GAAGC,EAAE;AACb,SAASA,EAAEA,CAAChD,CAAC,EAAEL,CAAC,EAAE;EAChBsD,KAAK,CAACC,OAAO,CAACvD,CAAC,CAAC,GAAGA,CAAC,CAAC+B,OAAO,CAAC,UAASZ,CAAC,EAAE;IACvCd,CAAC,CAACH,WAAW,CAACiB,CAAC,CAAC;EAClB,CAAC,CAAC,GAAGd,CAAC,CAACH,WAAW,CAACF,CAAC,CAAC;AACvB;AACA,CAAC,UAASK,CAAC,EAAE;EACXsB,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,YAAY,EAAE;IAAEwB,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAExB,CAAC,CAAC+C,MAAM,GAAG,KAAK,CAAC;EACxE,IAAIpD,CAAC,GAAGmD,CAAC;EACTxB,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,QAAQ,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACnE,OAAOlC,CAAC,CAACoD,MAAM;IACjB;EAAE,CAAC,CAAC;AACN,CAAC,EAAEF,EAAE,CAAC;AACN,IAAIM,CAAC,GAAG,CAAC,CAAC;EAAEC,CAAC,GAAG,CAAC,CAAC;AAClB9B,MAAM,CAACC,cAAc,CAAC6B,CAAC,EAAE,YAAY,EAAE;EAAE5B,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACrD4B,CAAC,CAACC,aAAa,GAAGC,EAAE;AACpB,SAASA,EAAEA,CAAA,EAAG;EACZ,OAAO,CACL,SAAS,EACT,SAAS,EACT,OAAO,EACP,YAAY,EACZ,QAAQ,EACR,KAAK,EACL,IAAI,EACJ,IAAI,EACJ,UAAU,EACV,YAAY,EACZ,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,QAAQ,EACR,QAAQ,EACR,IAAI,EACJ,IAAI,EACJ,MAAM,EACN,KAAK,EACL,UAAU,EACV,IAAI,EACJ,QAAQ,EACR,GAAG,EACH,KAAK,EACL,MAAM,EACN,SAAS,EACT,OAAO,EACP,OAAO,EACP,OAAO,EACP,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,OAAO,CACR;AACH;AACA,CAAC,UAAStD,CAAC,EAAE;EACXsB,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,YAAY,EAAE;IAAEwB,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAExB,CAAC,CAACqD,aAAa,GAAG,KAAK,CAAC;EAC/E,IAAI1D,CAAC,GAAGyD,CAAC;EACT9B,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,eAAe,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAC1E,OAAOlC,CAAC,CAAC0D,aAAa;IACxB;EAAE,CAAC,CAAC;AACN,CAAC,EAAEF,CAAC,CAAC;AACL,IAAII,EAAE,GAAG,CAAC,CAAC;EAAEC,CAAC,GAAG,CAAC,CAAC;AACnBlC,MAAM,CAACC,cAAc,CAACiC,CAAC,EAAE,YAAY,EAAE;EAAEhC,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACrDgC,CAAC,CAACC,iBAAiB,GAAGC,EAAE;AACxB,SAASA,EAAEA,CAAC1D,CAAC,EAAE;EACb,IAAIL,CAAC,GAAGa,MAAM,CAACmD,gBAAgB,CAAC3D,CAAC,CAAC;IAAEc,CAAC,GAAG8C,UAAU,CAACjE,CAAC,CAACkE,QAAQ,CAAC;IAAE9C,CAAC,GAAG6C,UAAU,CAACjE,CAAC,CAACmE,UAAU,CAAC,IAAIhD,CAAC,GAAG,GAAG;IAAEa,CAAC,GAAGiC,UAAU,CAACjE,CAAC,CAACoE,UAAU,CAAC;IAAEC,CAAC,GAAGJ,UAAU,CAACjE,CAAC,CAACsE,cAAc,CAAC;IAAEC,CAAC,GAAGN,UAAU,CAACjE,CAAC,CAACwE,SAAS,CAAC;IAAEC,CAAC,GAAGtD,CAAC,GAAG,GAAG;IAAEuD,CAAC,GAAG,CAACtD,CAAC,GAAGD,CAAC,IAAI,CAAC;IAAEwD,CAAC,GAAGJ,CAAC,GAAGF,CAAC,GAAGrC,CAAC,GAAG0C,CAAC,GAAGD,CAAC;EACzP,OAAOE,CAAC;AACV;AACA,CAAC,UAAStE,CAAC,EAAE;EACXsB,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,YAAY,EAAE;IAAEwB,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAExB,CAAC,CAACyD,iBAAiB,GAAG,KAAK,CAAC;EACnF,IAAI9D,CAAC,GAAG6D,CAAC;EACTlC,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,mBAAmB,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAC9E,OAAOlC,CAAC,CAAC8D,iBAAiB;IAC5B;EAAE,CAAC,CAAC;AACN,CAAC,EAAEF,EAAE,CAAC;AACN,IAAIgB,EAAE,GAAG,CAAC,CAAC;EAAEC,CAAC,GAAG,CAAC,CAAC;EAAEC,CAAC,GAAG,CAAC,CAAC;EAAEC,CAAC,GAAG,CAAC,CAAC;AACnCpD,MAAM,CAACC,cAAc,CAACmD,CAAC,EAAE,YAAY,EAAE;EAAElD,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACrDkD,CAAC,CAACC,iBAAiB,GAAGC,EAAE;AACxB,SAASA,EAAEA,CAAC5E,CAAC,EAAE;EACb,OAAOA,CAAC,CAAC6E,eAAe,KAAK,MAAM;AACrC;AACA,CAAC,UAAS7E,CAAC,EAAE;EACXsB,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,YAAY,EAAE;IAAEwB,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAExB,CAAC,CAAC2E,iBAAiB,GAAG,KAAK,CAAC;EACnF,IAAIhF,CAAC,GAAG+E,CAAC;EACTpD,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,mBAAmB,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAC9E,OAAOlC,CAAC,CAACgF,iBAAiB;IAC5B;EAAE,CAAC,CAAC;AACN,CAAC,EAAEF,CAAC,CAAC;AACLnD,MAAM,CAACC,cAAc,CAACiD,CAAC,EAAE,YAAY,EAAE;EAAEhD,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACrDgD,CAAC,CAACM,WAAW,GAAGC,EAAE;AAClB,IAAIC,EAAE,GAAGzC,CAAC;EAAE0C,EAAE,GAAGR,CAAC;AAClB,SAASM,EAAEA,CAAC/E,CAAC,EAAE;EACb,IAAIL,CAAC,GAAG,CAAC,CAAC;EACV,IAAI,CAAC,CAAC,EAAEqF,EAAE,CAACvC,aAAa,EAAEzC,CAAC,CAAC,EAC1B,QAAQA,CAAC,CAACkF,IAAI;IACZ,KAAK,MAAM;IACX,KAAK,UAAU;IACf,KAAK,OAAO;IACZ,KAAK,QAAQ;IACb,KAAK,QAAQ;IACb,KAAK,QAAQ;IACb,KAAK,OAAO;IACZ,KAAK,OAAO;MACVvF,CAAC,GAAG,CAAC,CAAC;MACN;EACJ,CAAC,MAEDA,CAAC,GAAG,CAAC,CAAC,EAAEsF,EAAE,CAACN,iBAAiB,EAAE3E,CAAC,CAAC;EAClC,OAAOL,CAAC;AACV;AACA,CAAC,UAASK,CAAC,EAAE;EACXsB,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,YAAY,EAAE;IAAEwB,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAExB,CAAC,CAAC8E,WAAW,GAAG,KAAK,CAAC;EAC7E,IAAInF,CAAC,GAAG6E,CAAC;EACTlD,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,aAAa,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACxE,OAAOlC,CAAC,CAACmF,WAAW;IACtB;EAAE,CAAC,CAAC;AACN,CAAC,EAAEP,EAAE,CAAC;AACN,IAAIY,CAAC,GAAG,CAAC,CAAC;EAAEC,CAAC,GAAG,CAAC,CAAC;AAClB,SAASC,EAAEA,CAACrF,CAAC,EAAEL,CAAC,EAAEmB,CAAC,EAAE;EACnB,MAAMC,CAAC,GAAGD,CAAC,CAACU,KAAK,KAAK,KAAK,CAAC,GAAG,OAAO,GAAG,KAAK;IAAEG,CAAC,GAAGb,CAAC,CAACC,CAAC,CAAC;IAAEiD,CAAC,GAAI,IAAGrE,CAAE,OAAM;EAC1E,IAAImB,CAAC,CAACC,CAAC,CAAC,GAAG,UAAS,GAAGmD,CAAC,EAAE;IACxB,OAAO,IAAI,CAACF,CAAC,CAAC,KAAK,KAAK,CAAC,KAAK,IAAI,CAACA,CAAC,CAAC,GAAGrC,CAAC,CAACP,KAAK,CAAC,IAAI,EAAE8C,CAAC,CAAC,CAAC,EAAE,IAAI,CAACF,CAAC,CAAC;EACpE,CAAC,EAAEjD,CAAC,KAAK,KAAK,IAAID,CAAC,CAACwE,GAAG,EAAE;IACvB,MAAMpB,CAAC,GAAGpD,CAAC,CAACwE,GAAG;IACfxE,CAAC,CAACwE,GAAG,GAAG,UAASlB,CAAC,EAAE;MAClB,OAAOpE,CAAC,CAACgE,CAAC,CAAC,EAAEE,CAAC,CAAC9C,KAAK,CAAC,IAAI,EAAEgD,CAAC,CAAC;IAC/B,CAAC;EACH;EACA,OAAOtD,CAAC;AACV;AACA,SAASyE,EAAEA,CAAA,EAAG;EACZ,MAAMvF,CAAC,GAAG;MACRwF,GAAG,EAAE,CAAC,CAAC;MACPC,GAAG,EAAE,CAAC,CAAC;MACPC,GAAG,EAAE,CAAC,CAAC;MACPC,KAAK,EAAE,CAAC;IACV,CAAC;IAAEhG,CAAC,GAAG2B,MAAM,CAACG,IAAI,CAACzB,CAAC,CAAC,CAAC4F,IAAI,CAAE9E,CAAC,IAAKN,MAAM,CAACqF,SAAS,CAACC,UAAU,CAACC,WAAW,CAAC,CAAC,CAACC,OAAO,CAAClF,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;EAC9F,OAAOnB,CAAC,KAAK,KAAK,CAAC,KAAKK,CAAC,CAACL,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAEK,CAAC;AACvC;AACA,SAASiG,CAACA,CAACjG,CAAC,EAAE;EACZ,OAAOA,CAAC,IAAI,IAAI,IAAIA,CAAC,KAAK,EAAE,KAAK,OAAOA,CAAC,IAAI,QAAQ,IAAIsB,MAAM,CAACG,IAAI,CAACzB,CAAC,CAAC,CAACkG,MAAM,GAAG,CAAC,CAAC;AACrF;AACA,SAASC,EAAEA,CAACnG,CAAC,EAAE;EACb,OAAO,CAACiG,CAAC,CAACjG,CAAC,CAAC;AACd;AACA,MAAMoG,EAAE,GAAGA,CAAA,KAAM,OAAO5F,MAAM,GAAG,GAAG,IAAIA,MAAM,CAACqF,SAAS,KAAK,IAAI,IAAII,CAAC,CAACzF,MAAM,CAACqF,SAAS,CAACQ,QAAQ,CAAC,KAAK,gBAAgB,CAACC,IAAI,CAAC9F,MAAM,CAACqF,SAAS,CAACQ,QAAQ,CAAC,IAAI7F,MAAM,CAACqF,SAAS,CAACQ,QAAQ,KAAK,UAAU,IAAI7F,MAAM,CAACqF,SAAS,CAACU,cAAc,GAAG,CAAC,CAAC;AAC1O,SAASC,EAAEA,CAACxG,CAAC,EAAE;EACb,MAAML,CAAC,GAAG4F,EAAE,CAAC,CAAC;EACd,OAAOvF,CAAC,GAAGA,CAAC,CAACyG,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,aAAa,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAACA,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE9G,CAAC,CAAC8F,GAAG,GAAGzF,CAAC,GAAGA,CAAC,CAACyG,OAAO,CAAC,YAAY,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,GAAGzG,CAAC,GAAGA,CAAC,CAACyG,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC,CAACA,OAAO,CAAC,WAAW,EAAE,KAAK,CAAC,EAAEzG,CAAC;AACvZ;AACA,SAAS0G,EAAEA,CAAC1G,CAAC,EAAE;EACb,OAAOA,CAAC,CAAC,CAAC,CAAC,CAAC2G,WAAW,CAAC,CAAC,GAAG3G,CAAC,CAAC4G,KAAK,CAAC,CAAC,CAAC;AACxC;AACA,SAASC,EAAEA,CAAC7G,CAAC,EAAE;EACb,MAAML,CAAC,GAAGD,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC;EACvCD,CAAC,CAACmH,KAAK,CAACC,QAAQ,GAAG,UAAU,EAAEpH,CAAC,CAACmH,KAAK,CAACE,IAAI,GAAG,QAAQ,EAAErH,CAAC,CAACmH,KAAK,CAACG,MAAM,GAAG,QAAQ,EAAEtH,CAAC,CAACuH,SAAS,GAAGlH,CAAC,EAAEN,QAAQ,CAACyH,IAAI,CAACtH,WAAW,CAACF,CAAC,CAAC;EAChI,MAAMmB,CAAC,GAAGN,MAAM,CAAC4G,YAAY,CAAC,CAAC;IAAErG,CAAC,GAAGrB,QAAQ,CAAC2H,WAAW,CAAC,CAAC;EAC3D,IAAItG,CAAC,CAACuG,UAAU,CAAC3H,CAAC,CAAC,EAAEmB,CAAC,KAAK,IAAI,EAC7B,MAAM,IAAIyG,KAAK,CAAC,+BAA+B,CAAC;EAClDzG,CAAC,CAAC0G,eAAe,CAAC,CAAC,EAAE1G,CAAC,CAAC2G,QAAQ,CAAC1G,CAAC,CAAC,EAAErB,QAAQ,CAACgI,WAAW,CAAC,MAAM,CAAC,EAAEhI,QAAQ,CAACyH,IAAI,CAACQ,WAAW,CAAChI,CAAC,CAAC;AAChG;AACA,SAASiI,EAAEA,CAAC5H,CAAC,EAAEL,CAAC,EAAEmB,CAAC,EAAE;EACnB,IAAIC,CAAC;EACL,OAAO,CAAC,GAAGY,CAAC,KAAK;IACf,MAAMqC,CAAC,GAAG,IAAI;MAAEE,CAAC,GAAGA,CAAA,KAAM;QACxBnD,CAAC,GAAG,KAAK,CAAC,EAAED,CAAC,KAAK,CAAC,CAAC,IAAId,CAAC,CAACoB,KAAK,CAAC4C,CAAC,EAAErC,CAAC,CAAC;MACvC,CAAC;MAAEyC,CAAC,GAAGtD,CAAC,KAAK,CAAC,CAAC,IAAIC,CAAC,KAAK,KAAK,CAAC;IAC/BP,MAAM,CAACqH,YAAY,CAAC9G,CAAC,CAAC,EAAEA,CAAC,GAAGP,MAAM,CAACsH,UAAU,CAAC5D,CAAC,EAAEvE,CAAC,CAAC,EAAEyE,CAAC,IAAIpE,CAAC,CAACoB,KAAK,CAAC4C,CAAC,EAAErC,CAAC,CAAC;EACzE,CAAC;AACH;AACA,SAASoG,CAACA,CAAC/H,CAAC,EAAE;EACZ,OAAOsB,MAAM,CAACD,SAAS,CAAC2G,QAAQ,CAACC,IAAI,CAACjI,CAAC,CAAC,CAACkI,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAACnC,WAAW,CAAC,CAAC;AAClF;AACA,SAASoC,EAAEA,CAACnI,CAAC,EAAE;EACb,OAAO+H,CAAC,CAAC/H,CAAC,CAAC,KAAK,SAAS;AAC3B;AACA,SAASoI,EAAEA,CAACpI,CAAC,EAAE;EACb,OAAO+H,CAAC,CAAC/H,CAAC,CAAC,KAAK,UAAU,IAAI+H,CAAC,CAAC/H,CAAC,CAAC,KAAK,eAAe;AACxD;AACA,SAASqI,EAAEA,CAACrI,CAAC,EAAE;EACb,OAAOoI,EAAE,CAACpI,CAAC,CAAC,IAAI,cAAc,CAACsG,IAAI,CAACtG,CAAC,CAACgI,QAAQ,CAAC,CAAC,CAAC;AACnD;AACA,SAASM,EAAEA,CAACtI,CAAC,EAAE;EACb,OAAO+H,CAAC,CAAC/H,CAAC,CAAC,KAAK,QAAQ;AAC1B;AACA,SAASuI,CAACA,CAACvI,CAAC,EAAE;EACZ,OAAO+H,CAAC,CAAC/H,CAAC,CAAC,KAAK,QAAQ;AAC1B;AACA,SAASwI,EAAEA,CAACxI,CAAC,EAAE;EACb,OAAOyI,OAAO,CAACC,OAAO,CAAC1I,CAAC,CAAC,KAAKA,CAAC;AACjC;AACA,SAAS2I,EAAEA,CAAC3I,CAAC,EAAE;EACb,OAAO+H,CAAC,CAAC/H,CAAC,CAAC,KAAK,QAAQ;AAC1B;AACA,SAAS4I,EAAEA,CAAC5I,CAAC,EAAE;EACb,OAAO+H,CAAC,CAAC/H,CAAC,CAAC,KAAK,WAAW;AAC7B;AACA,SAAS6I,CAACA,CAAC7I,CAAC,EAAE,GAAGL,CAAC,EAAE;EAClB,IAAI,CAACA,CAAC,CAACuG,MAAM,EACX,OAAOlG,CAAC;EACV,MAAMc,CAAC,GAAGnB,CAAC,CAACmJ,KAAK,CAAC,CAAC;EACnB,IAAIP,CAAC,CAACvI,CAAC,CAAC,IAAIuI,CAAC,CAACzH,CAAC,CAAC,EACd,KAAK,MAAMC,CAAC,IAAID,CAAC,EACfyH,CAAC,CAACzH,CAAC,CAACC,CAAC,CAAC,CAAC,IAAIf,CAAC,CAACe,CAAC,CAAC,KAAK,KAAK,CAAC,IAAIO,MAAM,CAACyH,MAAM,CAAC/I,CAAC,EAAE;IAAE,CAACe,CAAC,GAAG,CAAC;EAAE,CAAC,CAAC,EAAE8H,CAAC,CAAC7I,CAAC,CAACe,CAAC,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,CAAC,IAAIO,MAAM,CAACyH,MAAM,CAAC/I,CAAC,EAAE;IAAE,CAACe,CAAC,GAAGD,CAAC,CAACC,CAAC;EAAE,CAAC,CAAC;EACjH,OAAO8H,CAAC,CAAC7I,CAAC,EAAE,GAAGL,CAAC,CAAC;AACnB;AACA,SAASqJ,EAAEA,CAAChJ,CAAC,EAAEL,CAAC,EAAEmB,CAAC,EAAE;EACnB,MAAMC,CAAC,GAAI,IAAGpB,CAAE,kFAAiFmB,CAAE,YAAW;EAC9Gd,CAAC,IAAIC,OAAO,CAACgJ,IAAI,CAAClI,CAAC,CAAC;AACtB;AACA,SAASmI,EAAEA,CAAClJ,CAAC,EAAE;EACb,IAAI;IACF,OAAO,IAAImJ,GAAG,CAACnJ,CAAC,CAAC,CAACoJ,IAAI;EACxB,CAAC,CAAC,MAAM,CACR;EACA,OAAOpJ,CAAC,CAACqJ,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,IAAI,GAAG7I,MAAM,CAAC8I,QAAQ,CAACC,QAAQ,GAAGvJ,CAAC,GAAGQ,MAAM,CAAC8I,QAAQ,CAACE,MAAM,GAAGxJ,CAAC;AAC/F;AACA,SAASyJ,EAAEA,CAACzJ,CAAC,EAAE;EACb,OAAOA,CAAC,GAAG,EAAE,IAAIA,CAAC,GAAG,EAAE,IAAIA,CAAC,KAAK,EAAE,IAAIA,CAAC,KAAK,EAAE,IAAIA,CAAC,KAAK,GAAG,IAAIA,CAAC,GAAG,EAAE,IAAIA,CAAC,GAAG,EAAE,IAAIA,CAAC,GAAG,EAAE,IAAIA,CAAC,GAAG,GAAG,IAAIA,CAAC,GAAG,GAAG,IAAIA,CAAC,GAAG,GAAG,IAAIA,CAAC,GAAG,GAAG,IAAIA,CAAC,GAAG,GAAG;AACnJ;AACA,MAAM0J,EAAE,GAAG;IACTC,SAAS,EAAE,CAAC;IACZC,GAAG,EAAE,CAAC;IACNC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,EAAE;IACTC,IAAI,EAAE,EAAE;IACRC,GAAG,EAAE,EAAE;IACPC,GAAG,EAAE,EAAE;IACPC,KAAK,EAAE,EAAE;IACTC,IAAI,EAAE,EAAE;IACRC,EAAE,EAAE,EAAE;IACNC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE;EACT,CAAC;EAAEC,EAAE,GAAG;IACNP,IAAI,EAAE,CAAC;IACPQ,KAAK,EAAE,CAAC;IACRL,KAAK,EAAE,CAAC;IACRM,QAAQ,EAAE,CAAC;IACXC,OAAO,EAAE;EACX,CAAC;AACD,MAAMC,EAAE,CAAC;EACP3J,WAAWA,CAAA,EAAG;IACZ,IAAI,CAAC4J,SAAS,GAAGtC,OAAO,CAACC,OAAO,CAAC,CAAC;EACpC;EACA;AACF;AACA;AACA;EACEsC,GAAGA,CAACrL,CAAC,EAAE;IACL,OAAO,IAAI8I,OAAO,CAAC,CAAC3H,CAAC,EAAEC,CAAC,KAAK;MAC3B,IAAI,CAACgK,SAAS,GAAG,IAAI,CAACA,SAAS,CAACE,IAAI,CAACtL,CAAC,CAAC,CAACsL,IAAI,CAACnK,CAAC,CAAC,CAACoK,KAAK,CAACnK,CAAC,CAAC;IAC1D,CAAC,CAAC;EACJ;AACF;AACA,SAASoK,EAAEA,CAACnL,CAAC,EAAEL,CAAC,EAAEmB,CAAC,GAAG,KAAK,CAAC,EAAE;EAC5B,IAAIC,CAAC;IAAEY,CAAC;IAAEqC,CAAC;IAAEE,CAAC,GAAG,IAAI;IAAEE,CAAC,GAAG,CAAC;EAC5BtD,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC,CAAC;EACb,MAAMuD,CAAC,GAAG,SAAAA,CAAA,EAAW;IACnBD,CAAC,GAAGtD,CAAC,CAACsK,OAAO,KAAK,CAAC,CAAC,GAAG,CAAC,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEpH,CAAC,GAAG,IAAI,EAAEF,CAAC,GAAGhE,CAAC,CAACoB,KAAK,CAACL,CAAC,EAAEY,CAAC,CAAC,EAAEuC,CAAC,KAAK,IAAI,KAAKnD,CAAC,GAAGY,CAAC,GAAG,IAAI,CAAC;EAClG,CAAC;EACD,OAAO,YAAW;IAChB,MAAM2C,CAAC,GAAG+G,IAAI,CAACC,GAAG,CAAC,CAAC;IACpB,CAAClH,CAAC,IAAItD,CAAC,CAACsK,OAAO,KAAK,CAAC,CAAC,KAAKhH,CAAC,GAAGE,CAAC,CAAC;IACjC,MAAMiH,CAAC,GAAG5L,CAAC,IAAI2E,CAAC,GAAGF,CAAC,CAAC;IACrB,OAAOrD,CAAC,GAAG,IAAI,EAAEY,CAAC,GAAGT,SAAS,EAAEqK,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAG5L,CAAC,IAAIuE,CAAC,KAAK2D,YAAY,CAAC3D,CAAC,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC,EAAEE,CAAC,GAAGE,CAAC,EAAEN,CAAC,GAAGhE,CAAC,CAACoB,KAAK,CAACL,CAAC,EAAEY,CAAC,CAAC,EAAEuC,CAAC,KAAK,IAAI,KAAKnD,CAAC,GAAGY,CAAC,GAAG,IAAI,CAAC,IAAI,CAACuC,CAAC,IAAIpD,CAAC,CAAC0K,QAAQ,KAAK,CAAC,CAAC,KAAKtH,CAAC,GAAG4D,UAAU,CAACzD,CAAC,EAAEkH,CAAC,CAAC,CAAC,EAAEvH,CAAC;EACrM,CAAC;AACH;AACA,MAAMyH,EAAE,GAAG,eAAgBnK,MAAM,CAACoK,MAAM,EAAC,eAAgBpK,MAAM,CAACC,cAAc,CAAC;IAC7EoK,SAAS,EAAE,IAAI;IACfC,YAAY,EAAEd,EAAE;IAChBe,gBAAgB,EAAErF,EAAE;IACpBsF,SAAS,EAAEzG,EAAE;IACb0G,UAAU,EAAErF,EAAE;IACdsF,mBAAmB,EAAEnF,EAAE;IACvBoF,QAAQ,EAAErE,EAAE;IACZsE,SAAS,EAAErD,CAAC;IACZsD,iBAAiB,EAAEnD,EAAE;IACrBoD,SAAS,EAAE7G,EAAE;IACb8G,WAAW,EAAEnD,EAAE;IACfoD,SAAS,EAAEnE,EAAE;IACboE,OAAO,EAAElE,EAAE;IACXmE,OAAO,EAAErG,EAAE;IACXsG,UAAU,EAAErE,EAAE;IACdsE,WAAW,EAAEtG,EAAE;IACfuG,QAAQ,EAAErE,EAAE;IACZsE,QAAQ,EAAErE,CAAC;IACXsE,cAAc,EAAEpD,EAAE;IAClBqD,SAAS,EAAEtE,EAAE;IACbuE,QAAQ,EAAEpE,EAAE;IACZqE,WAAW,EAAEpE,EAAE;IACfqE,QAAQ,EAAEvD,EAAE;IACZwD,YAAY,EAAExC,EAAE;IAChByC,QAAQ,EAAElH,CAAC;IACXmH,QAAQ,EAAEjC,EAAE;IACZkC,MAAM,EAAEtF;EACV,CAAC,EAAEuF,MAAM,CAACC,WAAW,EAAE;IAAE/L,KAAK,EAAE;EAAS,CAAC,CAAC,CAAC;EAAEgM,CAAC,GAAG,eAAgB7M,EAAE,CAAC8K,EAAE,CAAC;AACxEnK,MAAM,CAACC,cAAc,CAAC6D,CAAC,EAAE,YAAY,EAAE;EAAE5D,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACrD4D,CAAC,CAACqI,0BAA0B,GAAGC,EAAE;AACjC,IAAIC,EAAE,GAAGH,CAAC;EAAEI,EAAE,GAAGzK,CAAC;AAClB,SAASuK,EAAEA,CAAC1N,CAAC,EAAE;EACb,IAAIL,CAAC;EACL,CAAC,CAAC,EAAEgO,EAAE,CAACZ,QAAQ,EAAE/M,CAAC,CAAC,IAAIL,CAAC,GAAGD,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC,EAAED,CAAC,CAACuH,SAAS,GAAGlH,CAAC,IAAIL,CAAC,GAAGK,CAAC;EAClF,IAAIc,CAAC,GAAG,SAAAA,CAASC,CAAC,EAAE;IAClB,OAAO,CAAC,CAAC,CAAC,EAAE6M,EAAE,CAACvK,aAAa,EAAE,CAAC,CAACT,QAAQ,CAAC7B,CAAC,CAAC4B,OAAO,CAACoD,WAAW,CAAC,CAAC,CAAC,IAAI9C,KAAK,CAAC4K,IAAI,CAAC9M,CAAC,CAAC+M,QAAQ,CAAC,CAACC,KAAK,CAACjN,CAAC,CAAC;EACtG,CAAC;EACD,OAAOmC,KAAK,CAAC4K,IAAI,CAAClO,CAAC,CAACmO,QAAQ,CAAC,CAACC,KAAK,CAACjN,CAAC,CAAC;AACxC;AACA,CAAC,UAASd,CAAC,EAAE;EACXsB,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,YAAY,EAAE;IAAEwB,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAExB,CAAC,CAACyN,0BAA0B,GAAG,KAAK,CAAC;EAC5F,IAAI9N,CAAC,GAAGyF,CAAC;EACT9D,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,4BAA4B,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACvF,OAAOlC,CAAC,CAAC8N,0BAA0B;IACrC;EAAE,CAAC,CAAC;AACN,CAAC,EAAEtI,CAAC,CAAC;AACL,IAAI6I,EAAE,GAAG,CAAC,CAAC;EAAEC,CAAC,GAAG,CAAC,CAAC;EAAEC,CAAC,GAAG,CAAC,CAAC;EAAEC,CAAC,GAAG,CAAC,CAAC;AACnC7M,MAAM,CAACC,cAAc,CAAC4M,CAAC,EAAE,YAAY,EAAE;EAAE3M,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACrD2M,CAAC,CAACC,IAAI,GAAGC,EAAE;AACX,SAASA,EAAEA,CAACrO,CAAC,EAAEL,CAAC,EAAEmB,CAAC,EAAE;EACnB,IAAIC,CAAC;EACLpB,CAAC,KAAK,KAAK,CAAC,KAAKA,CAAC,GAAG,IAAI,CAAC,EAAEmB,CAAC,KAAK,KAAK,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC,CAAC;EACpD,IAAIa,CAAC,GAAGjC,QAAQ,CAACE,aAAa,CAACI,CAAC,CAAC;EACjC,IAAIiD,KAAK,CAACC,OAAO,CAACvD,CAAC,CAAC,EAAE;IACpB,IAAIqE,CAAC,GAAGrE,CAAC,CAAC2O,MAAM,CAAC,UAASlK,CAAC,EAAE;MAC3B,OAAOA,CAAC,KAAK,KAAK,CAAC;IACrB,CAAC,CAAC;IACF,CAACrD,CAAC,GAAGY,CAAC,CAAC4M,SAAS,EAAEvD,GAAG,CAAC5J,KAAK,CAACL,CAAC,EAAEiD,CAAC,CAAC;EACnC,CAAC,MACCrE,CAAC,KAAK,IAAI,IAAIgC,CAAC,CAAC4M,SAAS,CAACvD,GAAG,CAACrL,CAAC,CAAC;EAClC,KAAK,IAAIuE,CAAC,IAAIpD,CAAC,EACbQ,MAAM,CAACD,SAAS,CAACmN,cAAc,CAACvG,IAAI,CAACnH,CAAC,EAAEoD,CAAC,CAAC,KAAKvC,CAAC,CAACuC,CAAC,CAAC,GAAGpD,CAAC,CAACoD,CAAC,CAAC,CAAC;EAC7D,OAAOvC,CAAC;AACV;AACA,CAAC,UAAS3B,CAAC,EAAE;EACXsB,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,YAAY,EAAE;IAAEwB,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAExB,CAAC,CAACoO,IAAI,GAAG,KAAK,CAAC;EACtE,IAAIzO,CAAC,GAAGwO,CAAC;EACT7M,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,MAAM,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACjE,OAAOlC,CAAC,CAACyO,IAAI;IACf;EAAE,CAAC,CAAC;AACN,CAAC,EAAEF,CAAC,CAAC;AACL5M,MAAM,CAACC,cAAc,CAAC0M,CAAC,EAAE,YAAY,EAAE;EAAEzM,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACrDyM,CAAC,CAACQ,gBAAgB,GAAGC,EAAE;AACvB,IAAIC,EAAE,GAAGT,CAAC;AACV,SAASQ,EAAEA,CAAC1O,CAAC,EAAE;EACb,IAAIL,CAAC,GAAG,CAAC,CAAC,EAAEgP,EAAE,CAACP,IAAI,EAAE,KAAK,CAAC;EAC3B,OAAOzO,CAAC,CAACE,WAAW,CAACG,CAAC,CAAC,EAAEL,CAAC,CAACuH,SAAS;AACtC;AACA,CAAC,UAASlH,CAAC,EAAE;EACXsB,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,YAAY,EAAE;IAAEwB,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAExB,CAAC,CAACyO,gBAAgB,GAAG,KAAK,CAAC;EAClF,IAAI9O,CAAC,GAAGsO,CAAC;EACT3M,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,kBAAkB,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAC7E,OAAOlC,CAAC,CAAC8O,gBAAgB;IAC3B;EAAE,CAAC,CAAC;AACN,CAAC,EAAET,EAAE,CAAC;AACN,IAAIY,EAAE,GAAG,CAAC,CAAC;EAAEC,CAAC,GAAG,CAAC,CAAC;AACnBvN,MAAM,CAACC,cAAc,CAACsN,CAAC,EAAE,YAAY,EAAE;EAAErN,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACrDqN,CAAC,CAACC,gBAAgB,GAAGC,EAAE;AACvB,IAAIC,EAAE,GAAGzM,CAAC;AACV,SAASwM,EAAEA,CAAC/O,CAAC,EAAE;EACb,IAAIL,CAAC,EAAEmB,CAAC;EACR,OAAO,CAAC,CAAC,EAAEkO,EAAE,CAACvM,aAAa,EAAEzC,CAAC,CAAC,GAAGA,CAAC,CAACwB,KAAK,CAAC0E,MAAM,GAAGlG,CAAC,CAACiP,QAAQ,KAAKC,IAAI,CAACC,SAAS,GAAGnP,CAAC,CAACkG,MAAM,GAAG,CAACpF,CAAC,GAAG,CAACnB,CAAC,GAAGK,CAAC,CAACoP,WAAW,MAAM,IAAI,IAAIzP,CAAC,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,CAAC,CAACuG,MAAM,MAAM,IAAI,IAAIpF,CAAC,KAAK,KAAK,CAAC,GAAGA,CAAC,GAAG,CAAC;AACvM;AACA,CAAC,UAASd,CAAC,EAAE;EACXsB,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,YAAY,EAAE;IAAEwB,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAExB,CAAC,CAAC8O,gBAAgB,GAAG,KAAK,CAAC;EAClF,IAAInP,CAAC,GAAGkP,CAAC;EACTvN,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,kBAAkB,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAC7E,OAAOlC,CAAC,CAACmP,gBAAgB;IAC3B;EAAE,CAAC,CAAC;AACN,CAAC,EAAEF,EAAE,CAAC;AACN,IAAIS,CAAC,GAAG,CAAC,CAAC;EAAEC,CAAC,GAAG,CAAC,CAAC;EAAEC,EAAE,GAAGjP,CAAC,IAAIA,CAAC,CAACkP,aAAa,IAAI,UAASxP,CAAC,EAAEL,CAAC,EAAEmB,CAAC,EAAE;IACjE,IAAIA,CAAC,IAAII,SAAS,CAACgF,MAAM,KAAK,CAAC,EAC7B,KAAK,IAAInF,CAAC,GAAG,CAAC,EAAEY,CAAC,GAAGhC,CAAC,CAACuG,MAAM,EAAElC,CAAC,EAAEjD,CAAC,GAAGY,CAAC,EAAEZ,CAAC,EAAE,EACzC,CAACiD,CAAC,IAAI,EAAEjD,CAAC,IAAIpB,CAAC,CAAC,MAAMqE,CAAC,KAAKA,CAAC,GAAGf,KAAK,CAAC5B,SAAS,CAACuF,KAAK,CAACqB,IAAI,CAACtI,CAAC,EAAE,CAAC,EAAEoB,CAAC,CAAC,CAAC,EAAEiD,CAAC,CAACjD,CAAC,CAAC,GAAGpB,CAAC,CAACoB,CAAC,CAAC,CAAC;IACrF,OAAOf,CAAC,CAACqC,MAAM,CAAC2B,CAAC,IAAIf,KAAK,CAAC5B,SAAS,CAACuF,KAAK,CAACqB,IAAI,CAACtI,CAAC,CAAC,CAAC;EACrD,CAAC;AACD2B,MAAM,CAACC,cAAc,CAAC+N,CAAC,EAAE,YAAY,EAAE;EAAE9N,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACrD8N,CAAC,CAACG,uBAAuB,GAAGC,EAAE;AAC9B,IAAIC,EAAE,GAAGxK,CAAC;AACV,SAASuK,EAAEA,CAAC1P,CAAC,EAAE;EACb,OAAO,CAAC,CAAC,EAAE2P,EAAE,CAAClC,0BAA0B,EAAEzN,CAAC,CAAC,GAAG,CAACA,CAAC,CAAC,GAAGiD,KAAK,CAAC4K,IAAI,CAAC7N,CAAC,CAAC8N,QAAQ,CAAC,CAAC8B,MAAM,CAAC,UAASjQ,CAAC,EAAEmB,CAAC,EAAE;IAChG,OAAOyO,EAAE,CAACA,EAAE,CAAC,EAAE,EAAE5P,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE+P,EAAE,CAAC5O,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACrC,CAAC,EAAE,EAAE,CAAC;AACR;AACA,CAAC,UAASd,CAAC,EAAE;EACXsB,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,YAAY,EAAE;IAAEwB,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAExB,CAAC,CAACyP,uBAAuB,GAAG,KAAK,CAAC;EACzF,IAAI9P,CAAC,GAAG2P,CAAC;EACThO,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,yBAAyB,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACpF,OAAOlC,CAAC,CAAC8P,uBAAuB;IAClC;EAAE,CAAC,CAAC;AACN,CAAC,EAAEJ,CAAC,CAAC;AACL,IAAIQ,EAAE,GAAG,CAAC,CAAC;EAAEC,CAAC,GAAG,CAAC,CAAC;EAAEC,CAAC,GAAG,CAAC,CAAC;EAAEC,CAAC,GAAG,CAAC,CAAC;AACnC1O,MAAM,CAACC,cAAc,CAACyO,CAAC,EAAE,YAAY,EAAE;EAAExO,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACrDwO,CAAC,CAACC,cAAc,GAAGC,EAAE;AACrB,SAASA,EAAEA,CAAClQ,CAAC,EAAE;EACb,OAAO,CACL,IAAI,EACJ,KAAK,CACN,CAAC4C,QAAQ,CAAC5C,CAAC,CAAC2C,OAAO,CAAC;AACvB;AACA,CAAC,UAAS3C,CAAC,EAAE;EACXsB,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,YAAY,EAAE;IAAEwB,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAExB,CAAC,CAACiQ,cAAc,GAAG,KAAK,CAAC;EAChF,IAAItQ,CAAC,GAAGqQ,CAAC;EACT1O,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,gBAAgB,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAC3E,OAAOlC,CAAC,CAACsQ,cAAc;IACzB;EAAE,CAAC,CAAC;AACN,CAAC,EAAEF,CAAC,CAAC;AACL,IAAII,CAAC,GAAG,CAAC,CAAC;EAAEC,CAAC,GAAG,CAAC,CAAC;AAClB9O,MAAM,CAACC,cAAc,CAAC6O,CAAC,EAAE,YAAY,EAAE;EAAE5O,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACrD4O,CAAC,CAACC,WAAW,GAAGC,EAAE;AAClB,SAASA,EAAEA,CAACtQ,CAAC,EAAE;EACb,OAAO,CACL,MAAM,EACN,MAAM,EACN,IAAI,EACJ,KAAK,EACL,SAAS,EACT,OAAO,EACP,IAAI,EACJ,KAAK,EACL,OAAO,EACP,QAAQ,EACR,MAAM,EACN,MAAM,EACN,OAAO,EACP,QAAQ,EACR,OAAO,EACP,KAAK,CACN,CAAC4C,QAAQ,CAAC5C,CAAC,CAAC2C,OAAO,CAAC;AACvB;AACA,CAAC,UAAS3C,CAAC,EAAE;EACXsB,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,YAAY,EAAE;IAAEwB,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAExB,CAAC,CAACqQ,WAAW,GAAG,KAAK,CAAC;EAC7E,IAAI1Q,CAAC,GAAGyQ,CAAC;EACT9O,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,aAAa,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACxE,OAAOlC,CAAC,CAAC0Q,WAAW;IACtB;EAAE,CAAC,CAAC;AACN,CAAC,EAAEF,CAAC,CAAC;AACL7O,MAAM,CAACC,cAAc,CAACuO,CAAC,EAAE,YAAY,EAAE;EAAEtO,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACrDsO,CAAC,CAACS,cAAc,GAAGC,EAAE;AACrB,IAAIC,EAAE,GAAGlO,CAAC;EAAEmO,EAAE,GAAGX,CAAC;EAAEY,EAAE,GAAGR,CAAC;AAC1B,SAASK,EAAEA,CAACxQ,CAAC,EAAEL,CAAC,EAAE;EAChBA,CAAC,KAAK,KAAK,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC,CAAC;EACxB,IAAImB,CAAC,GAAGnB,CAAC,GAAG,WAAW,GAAG,YAAY;IAAEoB,CAAC,GAAGpB,CAAC,GAAG,iBAAiB,GAAG,aAAa;EACjF,IAAIK,CAAC,CAACiP,QAAQ,KAAKC,IAAI,CAAC0B,YAAY,IAAI5Q,CAAC,CAACc,CAAC,CAAC,EAAE;IAC5C,IAAIa,CAAC,GAAG3B,CAAC,CAACc,CAAC,CAAC;IACZ,IAAI,CAAC,CAAC,EAAE6P,EAAE,CAACN,WAAW,EAAE1O,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE8O,EAAE,CAAChO,aAAa,EAAEd,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE+O,EAAE,CAACT,cAAc,EAAEtO,CAAC,CAAC,EACnF,IAAIA,CAAC,CAACZ,CAAC,CAAC,EACNY,CAAC,GAAGA,CAAC,CAACZ,CAAC,CAAC,CAAC,KACN,IAAIY,CAAC,CAACkP,UAAU,KAAK,IAAI,IAAIlP,CAAC,CAACkP,UAAU,CAAC9P,CAAC,CAAC,EAC/CY,CAAC,GAAGA,CAAC,CAACkP,UAAU,CAAC9P,CAAC,CAAC,CAAC,KAEpB,OAAOY,CAAC,CAACkP,UAAU;IACvB,OAAOL,EAAE,CAAC7O,CAAC,EAAEhC,CAAC,CAAC;EACjB;EACA,OAAOK,CAAC;AACV;AACA,CAAC,UAASA,CAAC,EAAE;EACXsB,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,YAAY,EAAE;IAAEwB,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAExB,CAAC,CAACuQ,cAAc,GAAG,KAAK,CAAC;EAChF,IAAI5Q,CAAC,GAAGmQ,CAAC;EACTxO,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,gBAAgB,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAC3E,OAAOlC,CAAC,CAAC4Q,cAAc;IACzB;EAAE,CAAC,CAAC;AACN,CAAC,EAAEV,EAAE,CAAC;AACN,IAAIiB,EAAE,GAAG,CAAC,CAAC;EAAEC,CAAC,GAAG,CAAC,CAAC;EAAEC,CAAC,GAAG1Q,CAAC,IAAIA,CAAC,CAACkP,aAAa,IAAI,UAASxP,CAAC,EAAEL,CAAC,EAAEmB,CAAC,EAAE;IACjE,IAAIA,CAAC,IAAII,SAAS,CAACgF,MAAM,KAAK,CAAC,EAC7B,KAAK,IAAInF,CAAC,GAAG,CAAC,EAAEY,CAAC,GAAGhC,CAAC,CAACuG,MAAM,EAAElC,CAAC,EAAEjD,CAAC,GAAGY,CAAC,EAAEZ,CAAC,EAAE,EACzC,CAACiD,CAAC,IAAI,EAAEjD,CAAC,IAAIpB,CAAC,CAAC,MAAMqE,CAAC,KAAKA,CAAC,GAAGf,KAAK,CAAC5B,SAAS,CAACuF,KAAK,CAACqB,IAAI,CAACtI,CAAC,EAAE,CAAC,EAAEoB,CAAC,CAAC,CAAC,EAAEiD,CAAC,CAACjD,CAAC,CAAC,GAAGpB,CAAC,CAACoB,CAAC,CAAC,CAAC;IACrF,OAAOf,CAAC,CAACqC,MAAM,CAAC2B,CAAC,IAAIf,KAAK,CAAC5B,SAAS,CAACuF,KAAK,CAACqB,IAAI,CAACtI,CAAC,CAAC,CAAC;EACrD,CAAC;AACD2B,MAAM,CAACC,cAAc,CAACwP,CAAC,EAAE,YAAY,EAAE;EAAEvP,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACrDuP,CAAC,CAACE,aAAa,GAAGC,EAAE;AACpB,IAAIC,EAAE,GAAGhM,CAAC;EAAEiM,EAAE,GAAG/B,CAAC;EAAEgC,EAAE,GAAGrP,CAAC;EAAEsP,EAAE,GAAG/O,CAAC;AAClC,SAAS2O,EAAEA,CAAClR,CAAC,EAAE;EACb,OAAOiD,KAAK,CAAC4K,IAAI,CAAC7N,CAAC,CAACuR,gBAAgB,CAAC,CAAC,CAAC,EAAEF,EAAE,CAACnP,iBAAiB,EAAE,CAAC,CAAC,CAAC,CAAC0N,MAAM,CAAC,UAASjQ,CAAC,EAAEmB,CAAC,EAAE;IACvF,OAAO,CAAC,CAAC,EAAEwQ,EAAE,CAAC7O,aAAa,EAAE3B,CAAC,CAAC,IAAI,CAAC,CAAC,EAAEqQ,EAAE,CAAC1D,0BAA0B,EAAE3M,CAAC,CAAC,GAAGkQ,CAAC,CAACA,CAAC,CAAC,EAAE,EAAErR,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAACmB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGkQ,CAAC,CAACA,CAAC,CAAC,EAAE,EAAErR,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAEyR,EAAE,CAAC3B,uBAAuB,EAAE3O,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAC/J,CAAC,EAAE,EAAE,CAAC;AACR;AACA,CAAC,UAASd,CAAC,EAAE;EACXsB,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,YAAY,EAAE;IAAEwB,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAExB,CAAC,CAACiR,aAAa,GAAG,KAAK,CAAC;EAC/E,IAAItR,CAAC,GAAGoR,CAAC;EACTzP,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,eAAe,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAC1E,OAAOlC,CAAC,CAACsR,aAAa;IACxB;EAAE,CAAC,CAAC;AACN,CAAC,EAAEH,EAAE,CAAC;AACN,IAAIU,EAAE,GAAG,CAAC,CAAC;EAAEC,CAAC,GAAG,CAAC,CAAC;AACnBnQ,MAAM,CAACC,cAAc,CAACkQ,CAAC,EAAE,YAAY,EAAE;EAAEjQ,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACrDiQ,CAAC,CAACC,sBAAsB,GAAGC,EAAE;AAC7B,SAASA,EAAEA,CAAC3R,CAAC,EAAE;EACb,OAAO,CAAC,YAAY,CAACsG,IAAI,CAACtG,CAAC,CAAC;AAC9B;AACA,CAAC,UAASA,CAAC,EAAE;EACXsB,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,YAAY,EAAE;IAAEwB,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAExB,CAAC,CAAC0R,sBAAsB,GAAG,KAAK,CAAC;EACxF,IAAI/R,CAAC,GAAG8R,CAAC;EACTnQ,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,wBAAwB,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACnF,OAAOlC,CAAC,CAAC+R,sBAAsB;IACjC;EAAE,CAAC,CAAC;AACN,CAAC,EAAEF,EAAE,CAAC;AACN,IAAII,CAAC,GAAG,CAAC,CAAC;EAAEC,CAAC,GAAG,CAAC,CAAC;AAClBvQ,MAAM,CAACC,cAAc,CAACsQ,CAAC,EAAE,YAAY,EAAE;EAAErQ,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACrDqQ,CAAC,CAACC,SAAS,GAAGC,EAAE;AAChB,IAAIC,EAAE,GAAGxE,CAAC;AACV,SAASuE,EAAEA,CAAC/R,CAAC,EAAE;EACb,OAAO,CAAC,CAAC,EAAEgS,EAAE,CAACrF,QAAQ,EAAE3M,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAACA,CAAC,IAAI,CAAC,CAACA,CAAC,CAACiP,QAAQ,IAAIjP,CAAC,CAACiP,QAAQ,KAAKC,IAAI,CAAC0B,YAAY;AAC3F;AACA,CAAC,UAAS5Q,CAAC,EAAE;EACXsB,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,YAAY,EAAE;IAAEwB,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAExB,CAAC,CAAC8R,SAAS,GAAG,KAAK,CAAC;EAC3E,IAAInS,CAAC,GAAGkS,CAAC;EACTvQ,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,WAAW,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACtE,OAAOlC,CAAC,CAACmS,SAAS;IACpB;EAAE,CAAC,CAAC;AACN,CAAC,EAAEF,CAAC,CAAC;AACL,IAAIK,EAAE,GAAG,CAAC,CAAC;EAAEC,CAAC,GAAG,CAAC,CAAC;EAAEC,CAAC,GAAG,CAAC,CAAC;EAAEC,CAAC,GAAG,CAAC,CAAC;AACnC9Q,MAAM,CAACC,cAAc,CAAC6Q,CAAC,EAAE,YAAY,EAAE;EAAE5Q,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACrD4Q,CAAC,CAACC,MAAM,GAAGC,EAAE;AACb,SAASA,EAAEA,CAACtS,CAAC,EAAE;EACb,OAAOA,CAAC,KAAK,IAAI,GAAG,CAAC,CAAC,GAAGA,CAAC,CAACuS,UAAU,CAACrM,MAAM,KAAK,CAAC;AACpD;AACA,CAAC,UAASlG,CAAC,EAAE;EACXsB,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,YAAY,EAAE;IAAEwB,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAExB,CAAC,CAACqS,MAAM,GAAG,KAAK,CAAC;EACxE,IAAI1S,CAAC,GAAGyS,CAAC;EACT9Q,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,QAAQ,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACnE,OAAOlC,CAAC,CAAC0S,MAAM;IACjB;EAAE,CAAC,CAAC;AACN,CAAC,EAAEF,CAAC,CAAC;AACL,IAAIK,CAAC,GAAG,CAAC,CAAC;EAAEC,CAAC,GAAG,CAAC,CAAC;AAClBnR,MAAM,CAACC,cAAc,CAACkR,CAAC,EAAE,YAAY,EAAE;EAAEjR,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACrDiR,CAAC,CAACC,WAAW,GAAGC,EAAE;AAClB,IAAIC,EAAE,GAAG7C,CAAC;EAAE8C,EAAE,GAAGjB,CAAC;EAAEkB,EAAE,GAAGvQ,CAAC;EAAEwQ,EAAE,GAAG5C,CAAC;AAClC,SAASwC,EAAEA,CAAC3S,CAAC,EAAEL,CAAC,EAAE;EAChB,IAAImB,CAAC,GAAG,EAAE;EACV,OAAO,CAAC,CAAC,EAAEiS,EAAE,CAAC1C,WAAW,EAAErQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE4S,EAAE,CAAC3C,cAAc,EAAEjQ,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE6S,EAAE,CAACf,SAAS,EAAE9R,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE8S,EAAE,CAACrQ,aAAa,EAAEzC,CAAC,CAAC,GAAGc,CAAC,GAAGd,CAAC,CAACwB,KAAK,GAAGxB,CAAC,CAACoP,WAAW,KAAK,IAAI,KAAKtO,CAAC,GAAGd,CAAC,CAACoP,WAAW,CAAC3I,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,EAAE9G,CAAC,KAAK,KAAK,CAAC,KAAKmB,CAAC,GAAGA,CAAC,CAAC2F,OAAO,CAAC,IAAIuM,MAAM,CAACrT,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,EAAEmB,CAAC,CAACmS,IAAI,CAAC,CAAC,CAAC/M,MAAM,KAAK,CAAC,CAAC;AACtR;AACA,CAAC,UAASlG,CAAC,EAAE;EACXsB,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,YAAY,EAAE;IAAEwB,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAExB,CAAC,CAAC0S,WAAW,GAAG,KAAK,CAAC;EAC7E,IAAI/S,CAAC,GAAG8S,CAAC;EACTnR,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,aAAa,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACxE,OAAOlC,CAAC,CAAC+S,WAAW;IACtB;EAAE,CAAC,CAAC;AACN,CAAC,EAAEF,CAAC,CAAC;AACLlR,MAAM,CAACC,cAAc,CAAC2Q,CAAC,EAAE,YAAY,EAAE;EAAE1Q,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACrD0Q,CAAC,CAAC1F,OAAO,GAAG0G,EAAE;AACd,IAAIC,EAAE,GAAGhB,CAAC;EAAEiB,EAAE,GAAGZ,CAAC;AAClB,SAASU,EAAEA,CAAClT,CAAC,EAAEL,CAAC,EAAE;EAChBK,CAAC,CAACqT,SAAS,CAAC,CAAC;EACb,KAAK,IAAIvS,CAAC,GAAG,CAACd,CAAC,CAAC,EAAEc,CAAC,CAACoF,MAAM,GAAG,CAAC,GAAI;IAChC,IAAInF,CAAC,GAAGD,CAAC,CAACgI,KAAK,CAAC,CAAC;IACjB,IAAI/H,CAAC,EAAE;MACL,IAAIf,CAAC,GAAGe,CAAC,EAAE,CAAC,CAAC,EAAEoS,EAAE,CAACd,MAAM,EAAErS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAEoT,EAAE,CAACV,WAAW,EAAE1S,CAAC,EAAEL,CAAC,CAAC,EACxD,OAAO,CAAC,CAAC;MACXmB,CAAC,CAACwS,IAAI,CAAClS,KAAK,CAACN,CAAC,EAAEmC,KAAK,CAAC4K,IAAI,CAAC7N,CAAC,CAACuS,UAAU,CAAC,CAAC;IAC3C;EACF;EACA,OAAO,CAAC,CAAC;AACX;AACA,CAAC,UAASvS,CAAC,EAAE;EACXsB,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,YAAY,EAAE;IAAEwB,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAExB,CAAC,CAACwM,OAAO,GAAG,KAAK,CAAC;EACzE,IAAI7M,CAAC,GAAGuS,CAAC;EACT5Q,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,SAAS,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACpE,OAAOlC,CAAC,CAAC6M,OAAO;IAClB;EAAE,CAAC,CAAC;AACN,CAAC,EAAEyF,EAAE,CAAC;AACN,IAAIsB,EAAE,GAAG,CAAC,CAAC;EAAEC,CAAC,GAAG,CAAC,CAAC;AACnBlS,MAAM,CAACC,cAAc,CAACiS,CAAC,EAAE,YAAY,EAAE;EAAEhS,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACrDgS,CAAC,CAACC,UAAU,GAAGC,EAAE;AACjB,IAAIC,EAAE,GAAGnG,CAAC;AACV,SAASkG,EAAEA,CAAC1T,CAAC,EAAE;EACb,OAAO,CAAC,CAAC,EAAE2T,EAAE,CAAChH,QAAQ,EAAE3M,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAACA,CAAC,IAAI,CAAC,CAACA,CAAC,CAACiP,QAAQ,IAAIjP,CAAC,CAACiP,QAAQ,KAAKC,IAAI,CAAC0E,sBAAsB;AACrG;AACA,CAAC,UAAS5T,CAAC,EAAE;EACXsB,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,YAAY,EAAE;IAAEwB,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAExB,CAAC,CAACyT,UAAU,GAAG,KAAK,CAAC;EAC5E,IAAI9T,CAAC,GAAG6T,CAAC;EACTlS,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,YAAY,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACvE,OAAOlC,CAAC,CAAC8T,UAAU;IACrB;EAAE,CAAC,CAAC;AACN,CAAC,EAAEF,EAAE,CAAC;AACN,IAAIM,EAAE,GAAG,CAAC,CAAC;EAAEC,EAAE,GAAG,CAAC,CAAC;AACpBxS,MAAM,CAACC,cAAc,CAACuS,EAAE,EAAE,YAAY,EAAE;EAAEtS,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACtDsS,EAAE,CAACC,YAAY,GAAGC,EAAE;AACpB,IAAIC,EAAE,GAAG/F,CAAC;AACV,SAAS8F,EAAEA,CAAChU,CAAC,EAAE;EACb,IAAIL,CAAC,GAAG,CAAC,CAAC,EAAEsU,EAAE,CAAC7F,IAAI,EAAE,KAAK,CAAC;EAC3B,OAAOzO,CAAC,CAACuH,SAAS,GAAGlH,CAAC,EAAEL,CAAC,CAACuU,iBAAiB,GAAG,CAAC;AACjD;AACA,CAAC,UAASlU,CAAC,EAAE;EACXsB,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,YAAY,EAAE;IAAEwB,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAExB,CAAC,CAAC+T,YAAY,GAAG,KAAK,CAAC;EAC9E,IAAIpU,CAAC,GAAGmU,EAAE;EACVxS,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,cAAc,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACzE,OAAOlC,CAAC,CAACoU,YAAY;IACvB;EAAE,CAAC,CAAC;AACN,CAAC,EAAEF,EAAE,CAAC;AACN,IAAIM,EAAE,GAAG,CAAC,CAAC;EAAEC,EAAE,GAAG,CAAC,CAAC;AACpB9S,MAAM,CAACC,cAAc,CAAC6S,EAAE,EAAE,YAAY,EAAE;EAAE5S,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACtD4S,EAAE,CAACC,MAAM,GAAGC,EAAE;AACd,SAASA,EAAEA,CAACtU,CAAC,EAAE;EACb,IAAIL,CAAC,GAAGK,CAAC,CAACuU,qBAAqB,CAAC,CAAC;IAAEzT,CAAC,GAAGN,MAAM,CAACgU,WAAW,IAAI9U,QAAQ,CAAC+U,eAAe,CAACC,UAAU;IAAE3T,CAAC,GAAGP,MAAM,CAACmU,WAAW,IAAIjV,QAAQ,CAAC+U,eAAe,CAACG,SAAS;IAAEjT,CAAC,GAAGhC,CAAC,CAACkV,GAAG,GAAG9T,CAAC;IAAEiD,CAAC,GAAGrE,CAAC,CAACqH,IAAI,GAAGlG,CAAC;EAC7L,OAAO;IACL+T,GAAG,EAAElT,CAAC;IACNqF,IAAI,EAAEhD,CAAC;IACPiD,MAAM,EAAEtF,CAAC,GAAGhC,CAAC,CAACmV,MAAM;IACpBC,KAAK,EAAE/Q,CAAC,GAAGrE,CAAC,CAACqV;EACf,CAAC;AACH;AACA,CAAC,UAAShV,CAAC,EAAE;EACXsB,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,YAAY,EAAE;IAAEwB,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAExB,CAAC,CAACqU,MAAM,GAAG,KAAK,CAAC;EACxE,IAAI1U,CAAC,GAAGyU,EAAE;EACV9S,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,QAAQ,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACnE,OAAOlC,CAAC,CAAC0U,MAAM;IACjB;EAAE,CAAC,CAAC;AACN,CAAC,EAAEF,EAAE,CAAC;AACN,IAAIc,EAAE,GAAG,CAAC,CAAC;EAAEC,EAAE,GAAG,CAAC,CAAC;AACpB5T,MAAM,CAACC,cAAc,CAAC2T,EAAE,EAAE,YAAY,EAAE;EAAE1T,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACtD0T,EAAE,CAACC,OAAO,GAAGC,EAAE;AACf,SAASA,EAAEA,CAACpV,CAAC,EAAEL,CAAC,EAAE;EAChBsD,KAAK,CAACC,OAAO,CAACvD,CAAC,CAAC,IAAIA,CAAC,GAAGA,CAAC,CAAC0V,OAAO,CAAC,CAAC,EAAE1V,CAAC,CAAC+B,OAAO,CAAC,UAASZ,CAAC,EAAE;IACzD,OAAOd,CAAC,CAACmV,OAAO,CAACrU,CAAC,CAAC;EACrB,CAAC,CAAC,IAAId,CAAC,CAACmV,OAAO,CAACxV,CAAC,CAAC;AACpB;AACA,CAAC,UAASK,CAAC,EAAE;EACXsB,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,YAAY,EAAE;IAAEwB,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAExB,CAAC,CAACmV,OAAO,GAAG,KAAK,CAAC;EACzE,IAAIxV,CAAC,GAAGuV,EAAE;EACV5T,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,SAAS,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACpE,OAAOlC,CAAC,CAACwV,OAAO;IAClB;EAAE,CAAC,CAAC;AACN,CAAC,EAAEF,EAAE,CAAC;AACN,CAAC,UAASjV,CAAC,EAAE;EACXsB,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,YAAY,EAAE;IAAEwB,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAExB,CAAC,CAACmV,OAAO,GAAGnV,CAAC,CAACqU,MAAM,GAAGrU,CAAC,CAACoO,IAAI,GAAGpO,CAAC,CAACiQ,cAAc,GAAGjQ,CAAC,CAACqQ,WAAW,GAAGrQ,CAAC,CAAC0S,WAAW,GAAG1S,CAAC,CAACqS,MAAM,GAAGrS,CAAC,CAAC+T,YAAY,GAAG/T,CAAC,CAACyT,UAAU,GAAGzT,CAAC,CAACwM,OAAO,GAAGxM,CAAC,CAAC8R,SAAS,GAAG9R,CAAC,CAAC2E,iBAAiB,GAAG3E,CAAC,CAAC0R,sBAAsB,GAAG1R,CAAC,CAACiR,aAAa,GAAGjR,CAAC,CAACyC,aAAa,GAAGzC,CAAC,CAACkC,iBAAiB,GAAGlC,CAAC,CAACuQ,cAAc,GAAGvQ,CAAC,CAACyP,uBAAuB,GAAGzP,CAAC,CAAC8O,gBAAgB,GAAG9O,CAAC,CAACyO,gBAAgB,GAAGzO,CAAC,CAACyN,0BAA0B,GAAGzN,CAAC,CAAC8E,WAAW,GAAG9E,CAAC,CAACyD,iBAAiB,GAAGzD,CAAC,CAACqD,aAAa,GAAGrD,CAAC,CAAC+C,MAAM,GAAG,KAAK,CAAC;EAC3f,IAAIpD,CAAC,GAAGqC,CAAC;EACTV,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,mBAAmB,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAC9E,OAAOlC,CAAC,CAACuC,iBAAiB;IAC5B;EAAE,CAAC,CAAC;EACJ,IAAIpB,CAAC,GAAGyB,CAAC;EACTjB,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,eAAe,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAC1E,OAAOf,CAAC,CAAC2B,aAAa;IACxB;EAAE,CAAC,CAAC;EACJ,IAAI1B,CAAC,GAAG8B,EAAE;EACVvB,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,QAAQ,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACnE,OAAOd,CAAC,CAACgC,MAAM;IACjB;EAAE,CAAC,CAAC;EACJ,IAAIpB,CAAC,GAAGwB,CAAC;EACT7B,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,eAAe,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAC1E,OAAOF,CAAC,CAAC0B,aAAa;IACxB;EAAE,CAAC,CAAC;EACJ,IAAIW,CAAC,GAAGT,EAAE;EACVjC,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,mBAAmB,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAC9E,OAAOmC,CAAC,CAACP,iBAAiB;IAC5B;EAAE,CAAC,CAAC;EACJ,IAAIS,CAAC,GAAGK,EAAE;EACVjD,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,aAAa,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACxE,OAAOqC,CAAC,CAACY,WAAW;IACtB;EAAE,CAAC,CAAC;EACJ,IAAIV,CAAC,GAAGe,CAAC;EACT7D,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,4BAA4B,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACvF,OAAOuC,CAAC,CAACqJ,0BAA0B;IACrC;EAAE,CAAC,CAAC;EACJ,IAAIpJ,CAAC,GAAG2J,EAAE;EACV1M,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,kBAAkB,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAC7E,OAAOwC,CAAC,CAACoK,gBAAgB;IAC3B;EAAE,CAAC,CAAC;EACJ,IAAInK,CAAC,GAAGsK,EAAE;EACVtN,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,kBAAkB,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAC7E,OAAOyC,CAAC,CAACwK,gBAAgB;IAC3B;EAAE,CAAC,CAAC;EACJ,IAAIvD,CAAC,GAAG8D,CAAC;EACT/N,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,yBAAyB,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACpF,OAAO0J,CAAC,CAACkE,uBAAuB;IAClC;EAAE,CAAC,CAAC;EACJ,IAAI6F,EAAE,GAAGzF,EAAE;EACXvO,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,gBAAgB,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAC3E,OAAOyT,EAAE,CAAC/E,cAAc;IAC1B;EAAE,CAAC,CAAC;EACJ,IAAIgF,EAAE,GAAGzE,EAAE;EACXxP,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,eAAe,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAC1E,OAAO0T,EAAE,CAACtE,aAAa;IACzB;EAAE,CAAC,CAAC;EACJ,IAAIuE,EAAE,GAAGhE,EAAE;EACXlQ,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,wBAAwB,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACnF,OAAO2T,EAAE,CAAC9D,sBAAsB;IAClC;EAAE,CAAC,CAAC;EACJ,IAAI+D,EAAE,GAAGhR,CAAC;EACVnD,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,mBAAmB,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAC9E,OAAO4T,EAAE,CAAC9Q,iBAAiB;IAC7B;EAAE,CAAC,CAAC;EACJ,IAAI+Q,EAAE,GAAG9D,CAAC;EACVtQ,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,WAAW,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACtE,OAAO6T,EAAE,CAAC5D,SAAS;IACrB;EAAE,CAAC,CAAC;EACJ,IAAI6D,EAAE,GAAG1D,EAAE;EACX3Q,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,SAAS,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACpE,OAAO8T,EAAE,CAACnJ,OAAO;IACnB;EAAE,CAAC,CAAC;EACJ,IAAIoJ,EAAE,GAAGrC,EAAE;EACXjS,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,YAAY,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACvE,OAAO+T,EAAE,CAACnC,UAAU;IACtB;EAAE,CAAC,CAAC;EACJ,IAAIoC,EAAE,GAAGhC,EAAE;EACXvS,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,cAAc,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACzE,OAAOgU,EAAE,CAAC9B,YAAY;IACxB;EAAE,CAAC,CAAC;EACJ,IAAI+B,EAAE,GAAG3D,CAAC;EACV7Q,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,QAAQ,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACnE,OAAOiU,EAAE,CAACzD,MAAM;IAClB;EAAE,CAAC,CAAC;EACJ,IAAI0D,EAAE,GAAGvD,CAAC;EACVlR,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,aAAa,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACxE,OAAOkU,EAAE,CAACrD,WAAW;IACvB;EAAE,CAAC,CAAC;EACJ,IAAIsD,EAAE,GAAGjG,CAAC;EACVzO,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,gBAAgB,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAC3E,OAAOmU,EAAE,CAAC/F,cAAc;IAC1B;EAAE,CAAC,CAAC;EACJ,IAAIgG,EAAE,GAAG9F,CAAC;EACV7O,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,aAAa,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACxE,OAAOoU,EAAE,CAAC5F,WAAW;IACvB;EAAE,CAAC,CAAC;EACJ,IAAI6F,EAAE,GAAGhI,CAAC;EACV5M,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,MAAM,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACjE,OAAOqU,EAAE,CAAC9H,IAAI;IAChB;EAAE,CAAC,CAAC;EACJ,IAAI+H,EAAE,GAAGhC,EAAE;EACX7S,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,QAAQ,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACnE,OAAOsU,EAAE,CAAC9B,MAAM;IAClB;EAAE,CAAC,CAAC;EACJ,IAAI+B,EAAE,GAAGnB,EAAE;EACX3T,MAAM,CAACC,cAAc,CAACvB,CAAC,EAAE,SAAS,EAAE;IAAE8B,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACpE,OAAOuU,EAAE,CAACjB,OAAO;IACnB;EAAE,CAAC,CAAC;AACN,CAAC,EAAEpT,CAAC,CAAC;AACL,IAAIsU,EAAE,GAAG,eAAgB,CAAErW,CAAC,KAAMA,CAAC,CAACsW,IAAI,GAAG,MAAM,EAAEtW,CAAC,CAACuW,MAAM,GAAG,QAAQ,EAAEvW,CAAC,CAAC,EAAEqW,EAAE,IAAI,CAAC,CAAC,CAAC;AACrF,MAAMG,CAAC,CAAC;EACN;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACErV,WAAWA,CAAC;IAAEsV,IAAI,EAAE9W,CAAC;IAAE+W,MAAM,EAAE5V,CAAC;IAAE6V,GAAG,EAAE5V,CAAC;IAAE6V,QAAQ,EAAEjV,CAAC;IAAEkV,KAAK,EAAE7S;EAAE,CAAC,EAAE;IACjE,MAAM;MAAE8S,iBAAiB,EAAE5S;IAAE,CAAC,GAAGsS,CAAC;IAClC,IAAI,CAACG,GAAG,GAAG5V,CAAC,EAAE,IAAI,CAAC6V,QAAQ,GAAGjV,CAAC,EAAE,IAAI,CAACoV,gBAAgB,GAAGhW,CAAC,CAACiW,IAAI,CAACrX,CAAC,CAAC,CAACmB,CAAC,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,CAAC,CAACiW,gBAAgB,KAAKP,CAAC,CAACS,yBAAyB,CAAC,EAAE,IAAI,CAACC,kBAAkB,GAAGnW,CAAC,CAACiW,IAAI,CAACrX,CAAC,CAAC,CAACmB,CAAC,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,CAAC,CAACoW,kBAAkB,KAAKV,CAAC,CAACW,2BAA2B,CAAC,EAAE,IAAI,CAACV,IAAI,GAAG;MACzQW,IAAI,EAAEzX,CAAC,CAACyX,IAAI,IAAI,EAAE;MAClBC,OAAO,EAAE1X,CAAC,CAAC0X,OAAO,IAAI,EAAE;MACxBC,SAAS,EAAEhW,MAAM,CAACiW,MAAM,CAAClB,EAAE,CAAC,CAACzT,QAAQ,CAACjD,CAAC,CAAC2X,SAAS,CAAC,GAAG3X,CAAC,CAAC2X,SAAS,GAAG,CAACxW,CAAC,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,CAAC,CAAC0W,gBAAgB,KAAKtT;IAClH,CAAC,EAAE,IAAI,CAACuT,GAAG,GAAG;MACZC,SAAS,EAAE,IAAI,CAACf,GAAG,CAACgB,MAAM,CAACd,KAAK;MAChCe,OAAO,EAAE,WAAW;MACpBR,IAAI,EAAE,iBAAiB;MACvBS,KAAK,EAAE,IAAI,CAAClB,GAAG,CAACgB,MAAM,CAACE,KAAK;MAC5BR,OAAO,EAAE;IACX,CAAC,EAAE,IAAI,CAACR,KAAK,GAAG7S,CAAC;EACnB;EACA;AACF;AACA;AACA;EACE,WAAW8T,mBAAmBA,CAAA,EAAG;IAC/B,OAAO,CAAC,CAAC;EACX;EACA;AACF;AACA;AACA;AACA;AACA;EACE,WAAWC,OAAOA,CAAA,EAAG;IACnB,OAAO;MACLC,IAAI,EAAE3X,EAAE;MACR4X,KAAK,EAAE;IACT,CAAC;EACH;EACA;AACF;AACA;AACA;EACE,WAAWC,WAAWA,CAAA,EAAG;IACvB,OAAO,CAAC,CAAC;EACX;EACA;AACF;AACA;AACA;EACE,WAAWC,gBAAgBA,CAAA,EAAG;IAC5B,OAAO,CAAC,CAAC;EACX;EACA;AACF;AACA;AACA;EACE,WAAWlB,yBAAyBA,CAAA,EAAG;IACrC,OAAO,eAAe;EACxB;EACA;AACF;AACA;AACA;EACE,WAAWE,2BAA2BA,CAAA,EAAG;IACvC,OAAO,iBAAiB;EAC1B;EACA;AACF;AACA;AACA;EACE,WAAWL,iBAAiBA,CAAA,EAAG;IAC7B,OAAO,MAAM;EACf;EACA;AACF;AACA;AACA;EACE,WAAWsB,gBAAgBA,CAAA,EAAG;IAC5B,OAAO;MACL;AACN;AACA;MACMC,MAAM,EAAE,MAAM;MACd;AACN;AACA;AACA;AACA;MACMC,MAAM,EAAE,SAAAA,CAAS3Y,CAAC,EAAE;QAClB,OAAOA,CAAC,CAAC0X,OAAO,GAAI,GAAE1X,CAAC,CAACyX,IAAK,MAAKzX,CAAC,CAAC0X,OAAQ,EAAC,GAAG1X,CAAC,CAACyX,IAAI;MACxD;IACF,CAAC;EACH;EACA;AACF;AACA;AACA;EACE,IAAImB,GAAGA,CAAA,EAAG;IACR,OAAO;MACLb,SAAS,EAAE,IAAI,CAACf,GAAG,CAACgB,MAAM,CAACd,KAAK;MAChCe,OAAO,EAAE,WAAW;MACpBR,IAAI,EAAE,iBAAiB;MACvBS,KAAK,EAAE,IAAI,CAAClB,GAAG,CAACgB,MAAM,CAACE,KAAK;MAC5BR,OAAO,EAAE;IACX,CAAC;EACH;EACA;AACF;AACA;AACA;EACE,IAAImB,QAAQA,CAAA,EAAG;IACb,OAAO,CACL;MACEC,IAAI,EAAE,MAAM;MACZT,IAAI,EAAE5X;IACR,CAAC,EACD;MACEqY,IAAI,EAAE,QAAQ;MACdT,IAAI,EAAE7X;IACR,CAAC,CACF;EACH;EACA;AACF;AACA;AACA;EACEuY,MAAMA,CAAA,EAAG;IACP,MAAM/Y,CAAC,GAAGoC,CAAC,CAACqM,IAAI,CAAC,YAAY,EAAE,CAC7B,IAAI,CAACqJ,GAAG,CAACC,SAAS,EAClB,IAAI,CAACD,GAAG,CAACG,OAAO,CACjB,CAAC;MAAE9W,CAAC,GAAGiB,CAAC,CAACqM,IAAI,CAAC,KAAK,EAAE,CAAC,IAAI,CAACqJ,GAAG,CAACI,KAAK,EAAE,IAAI,CAACJ,GAAG,CAACL,IAAI,CAAC,EAAE;QACrDvS,eAAe,EAAE,CAAC,IAAI,CAAC+R,QAAQ;QAC/B1P,SAAS,EAAE,IAAI,CAACuP,IAAI,CAACW;MACvB,CAAC,CAAC;MAAErW,CAAC,GAAGgB,CAAC,CAACqM,IAAI,CAAC,KAAK,EAAE,CAAC,IAAI,CAACqJ,GAAG,CAACI,KAAK,EAAE,IAAI,CAACJ,GAAG,CAACJ,OAAO,CAAC,EAAE;QACxDxS,eAAe,EAAE,CAAC,IAAI,CAAC+R,QAAQ;QAC/B1P,SAAS,EAAE,IAAI,CAACuP,IAAI,CAACY;MACvB,CAAC,CAAC;IACF,OAAOvW,CAAC,CAAC6X,OAAO,CAACC,WAAW,GAAG,IAAI,CAAC7B,gBAAgB,EAAEhW,CAAC,CAAC4X,OAAO,CAACC,WAAW,GAAG,IAAI,CAAC1B,kBAAkB,EAAEvX,CAAC,CAACE,WAAW,CAACiB,CAAC,CAAC,EAAEnB,CAAC,CAACE,WAAW,CAACkB,CAAC,CAAC,EAAEpB,CAAC;EAC9I;EACA;AACF;AACA;AACA;AACA;EACEkZ,IAAIA,CAAClZ,CAAC,EAAE;IACN,MAAMmB,CAAC,GAAGnB,CAAC,CAACmZ,aAAa,CAAE,IAAG,IAAI,CAACrB,GAAG,CAACL,IAAK,EAAC,CAAC;MAAErW,CAAC,GAAGpB,CAAC,CAACmZ,aAAa,CAAE,IAAG,IAAI,CAACrB,GAAG,CAACJ,OAAQ,EAAC,CAAC;IAC3F,OAAO/V,MAAM,CAACyH,MAAM,CAAC,IAAI,CAAC0N,IAAI,EAAE;MAC9BW,IAAI,EAAE,CAACtW,CAAC,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,CAAC,CAACoG,SAAS,KAAK,EAAE;MAC9CmQ,OAAO,EAAE,CAACtW,CAAC,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,CAAC,CAACmG,SAAS,KAAK;IACjD,CAAC,CAAC;EACJ;EACA;AACF;AACA;AACA;EACE,WAAW6R,QAAQA,CAAA,EAAG;IACpB,OAAO;MACL3B,IAAI,EAAE;QACJ4B,EAAE,EAAE,CAAC;MACP,CAAC;MACD3B,OAAO,EAAE;QACP2B,EAAE,EAAE,CAAC;MACP,CAAC;MACD1B,SAAS,EAAE,CAAC;IACd,CAAC;EACH;EACA;AACF;AACA;AACA;AACA;AACA;EACE2B,cAAcA,CAAA,EAAG;IACf,MAAMtZ,CAAC,GAAImB,CAAC,IAAKA,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,CAAC6F,WAAW,CAAC,CAAC,GAAG7F,CAAC,CAAC8F,KAAK,CAAC,CAAC,CAAC;IACrD,OAAO,IAAI,CAAC4R,QAAQ,CAACpW,GAAG,CAAEtB,CAAC,KAAM;MAC/BkX,IAAI,EAAElX,CAAC,CAACkX,IAAI;MACZkB,KAAK,EAAE,IAAI,CAACvC,GAAG,CAACK,IAAI,CAACrX,CAAC,CAAE,SAAQA,CAAC,CAACmB,CAAC,CAAC2X,IAAI,CAAE,EAAC,CAAC;MAC5CU,UAAU,EAAEA,CAAA,KAAM,IAAI,CAACC,WAAW,CAACtY,CAAC,CAAC2X,IAAI,CAAC;MAC1CY,QAAQ,EAAE,IAAI,CAAC5C,IAAI,CAACa,SAAS,KAAKxW,CAAC,CAAC2X,IAAI;MACxCa,eAAe,EAAE,CAAC;IACpB,CAAC,CAAC,CAAC;EACL;EACA;AACF;AACA;AACA;EACEF,WAAWA,CAACzZ,CAAC,EAAE;IACb,IAAI,CAAC8W,IAAI,CAACa,SAAS,GAAG3X,CAAC,EAAE,IAAI,CAACkX,KAAK,CAAC0C,cAAc,CAAC,CAAC;EACtD;AACF;AACA,SACE/C,CAAC,IAAI3V,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}