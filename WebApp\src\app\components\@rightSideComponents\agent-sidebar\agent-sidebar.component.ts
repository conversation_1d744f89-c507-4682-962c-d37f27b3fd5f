import { Component, Input, Output, EventEmitter, inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RemoveProviderPrefixPipe } from '../../../../shared/pipes/remove-provider-prefix.pipe';
import { ThemeService } from '../../../../shared/services/theam.service';

@Component({
  selector: 'app-agent-sidebar',
  standalone: true,
  imports: [
    CommonModule,
    RemoveProviderPrefixPipe
  ],
  templateUrl: './agent-sidebar.component.html',
  styleUrls: ['./agent-sidebar.component.css']
})
export class AgentSidebarComponent implements OnInit {
  // Inject ThemeService
  themeService = inject(ThemeService);

  // Input properties
  @Input() isAgentSidebarOpen: boolean = false;
  @Input() agentSidebarTitle: string = 'Available Agents';
  @Input() workspaceAgents: any[] = [];

  // Output events
  @Output() onClose = new EventEmitter<void>();
  @Output() onSelectAgent = new EventEmitter<any>();

  // Accordion state management
  accordionSections = {
    agents: {
      isExpanded: true,
      title: 'Available Agents',
      icon: 'ri-robot-line'
    }
  };

  /**
   * Getter for showSidebar to match source-references component property name
   * This allows for consistent template usage
   */
  get showSidebar(): boolean {
    return this.isAgentSidebarOpen;
  }

  /**
   * Handles the sidebar close event
   */
  closeSidebar(): void {
    this.onClose.emit();
  }

  /**
   * Alternative method name to match source-references component
   */
  onCloseSidebar(): void {
    this.closeSidebar();
  }

  /**
   * Handles agent selection
   * @param agent The selected agent
   */
  selectAgent(agent: any): void {
    if (!agent) return;
    this.onSelectAgent.emit(agent);
  }
}
