{"ast": null, "code": "const numberingSystems = {\n  arab: \"[\\u0660-\\u0669]\",\n  arabext: \"[\\u06F0-\\u06F9]\",\n  bali: \"[\\u1B50-\\u1B59]\",\n  beng: \"[\\u09E6-\\u09EF]\",\n  deva: \"[\\u0966-\\u096F]\",\n  fullwide: \"[\\uFF10-\\uFF19]\",\n  gujr: \"[\\u0AE6-\\u0AEF]\",\n  hanidec: \"[〇|一|二|三|四|五|六|七|八|九]\",\n  khmr: \"[\\u17E0-\\u17E9]\",\n  knda: \"[\\u0CE6-\\u0CEF]\",\n  laoo: \"[\\u0ED0-\\u0ED9]\",\n  limb: \"[\\u1946-\\u194F]\",\n  mlym: \"[\\u0D66-\\u0D6F]\",\n  mong: \"[\\u1810-\\u1819]\",\n  mymr: \"[\\u1040-\\u1049]\",\n  orya: \"[\\u0B66-\\u0B6F]\",\n  tamldec: \"[\\u0BE6-\\u0BEF]\",\n  telu: \"[\\u0C66-\\u0C6F]\",\n  thai: \"[\\u0E50-\\u0E59]\",\n  tibt: \"[\\u0F20-\\u0F29]\",\n  latn: \"\\\\d\"\n};\nconst numberingSystemsUTF16 = {\n  arab: [1632, 1641],\n  arabext: [1776, 1785],\n  bali: [6992, 7001],\n  beng: [2534, 2543],\n  deva: [2406, 2415],\n  fullwide: [65296, 65303],\n  gujr: [2790, 2799],\n  khmr: [6112, 6121],\n  knda: [3302, 3311],\n  laoo: [3792, 3801],\n  limb: [6470, 6479],\n  mlym: [3430, 3439],\n  mong: [6160, 6169],\n  mymr: [4160, 4169],\n  orya: [2918, 2927],\n  tamldec: [3046, 3055],\n  telu: [3174, 3183],\n  thai: [3664, 3673],\n  tibt: [3872, 3881]\n};\nconst hanidecChars = numberingSystems.hanidec.replace(/[\\[|\\]]/g, \"\").split(\"\");\nexport function parseDigits(str) {\n  let value = parseInt(str, 10);\n  if (isNaN(value)) {\n    value = \"\";\n    for (let i = 0; i < str.length; i++) {\n      const code = str.charCodeAt(i);\n      if (str[i].search(numberingSystems.hanidec) !== -1) {\n        value += hanidecChars.indexOf(str[i]);\n      } else {\n        for (const key in numberingSystemsUTF16) {\n          const [min, max] = numberingSystemsUTF16[key];\n          if (code >= min && code <= max) {\n            value += code - min;\n          }\n        }\n      }\n    }\n    return parseInt(value, 10);\n  } else {\n    return value;\n  }\n}\n\n// cache of {numberingSystem: {append: regex}}\nlet digitRegexCache = {};\nexport function resetDigitRegexCache() {\n  digitRegexCache = {};\n}\nexport function digitRegex({\n  numberingSystem\n}, append = \"\") {\n  const ns = numberingSystem || \"latn\";\n  if (!digitRegexCache[ns]) {\n    digitRegexCache[ns] = {};\n  }\n  if (!digitRegexCache[ns][append]) {\n    digitRegexCache[ns][append] = new RegExp(`${numberingSystems[ns]}${append}`);\n  }\n  return digitRegexCache[ns][append];\n}", "map": {"version": 3, "names": ["numberingSystems", "arab", "arabext", "bali", "beng", "deva", "fullwide", "gujr", "hanidec", "khmr", "knda", "laoo", "limb", "mlym", "mong", "mymr", "orya", "tamldec", "telu", "thai", "tibt", "latn", "numberingSystemsUTF16", "hanidecChars", "replace", "split", "parseDigits", "str", "value", "parseInt", "isNaN", "i", "length", "code", "charCodeAt", "search", "indexOf", "key", "min", "max", "digitRegexCache", "resetDigitRegexCache", "digitRegex", "numberingSystem", "append", "ns", "RegExp"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/luxon/src/impl/digits.js"], "sourcesContent": ["const numberingSystems = {\n  arab: \"[\\u0660-\\u0669]\",\n  arabext: \"[\\u06F0-\\u06F9]\",\n  bali: \"[\\u1B50-\\u1B59]\",\n  beng: \"[\\u09E6-\\u09EF]\",\n  deva: \"[\\u0966-\\u096F]\",\n  fullwide: \"[\\uFF10-\\uFF19]\",\n  gujr: \"[\\u0AE6-\\u0AEF]\",\n  hanidec: \"[〇|一|二|三|四|五|六|七|八|九]\",\n  khmr: \"[\\u17E0-\\u17E9]\",\n  knda: \"[\\u0CE6-\\u0CEF]\",\n  laoo: \"[\\u0ED0-\\u0ED9]\",\n  limb: \"[\\u1946-\\u194F]\",\n  mlym: \"[\\u0D66-\\u0D6F]\",\n  mong: \"[\\u1810-\\u1819]\",\n  mymr: \"[\\u1040-\\u1049]\",\n  orya: \"[\\u0B66-\\u0B6F]\",\n  tamldec: \"[\\u0BE6-\\u0BEF]\",\n  telu: \"[\\u0C66-\\u0C6F]\",\n  thai: \"[\\u0E50-\\u0E59]\",\n  tibt: \"[\\u0F20-\\u0F29]\",\n  latn: \"\\\\d\",\n};\n\nconst numberingSystemsUTF16 = {\n  arab: [1632, 1641],\n  arabext: [1776, 1785],\n  bali: [6992, 7001],\n  beng: [2534, 2543],\n  deva: [2406, 2415],\n  fullwide: [65296, 65303],\n  gujr: [2790, 2799],\n  khmr: [6112, 6121],\n  knda: [3302, 3311],\n  laoo: [3792, 3801],\n  limb: [6470, 6479],\n  mlym: [3430, 3439],\n  mong: [6160, 6169],\n  mymr: [4160, 4169],\n  orya: [2918, 2927],\n  tamldec: [3046, 3055],\n  telu: [3174, 3183],\n  thai: [3664, 3673],\n  tibt: [3872, 3881],\n};\n\nconst hanidecChars = numberingSystems.hanidec.replace(/[\\[|\\]]/g, \"\").split(\"\");\n\nexport function parseDigits(str) {\n  let value = parseInt(str, 10);\n  if (isNaN(value)) {\n    value = \"\";\n    for (let i = 0; i < str.length; i++) {\n      const code = str.charCodeAt(i);\n\n      if (str[i].search(numberingSystems.hanidec) !== -1) {\n        value += hanidecChars.indexOf(str[i]);\n      } else {\n        for (const key in numberingSystemsUTF16) {\n          const [min, max] = numberingSystemsUTF16[key];\n          if (code >= min && code <= max) {\n            value += code - min;\n          }\n        }\n      }\n    }\n    return parseInt(value, 10);\n  } else {\n    return value;\n  }\n}\n\n// cache of {numberingSystem: {append: regex}}\nlet digitRegexCache = {};\nexport function resetDigitRegexCache() {\n  digitRegexCache = {};\n}\n\nexport function digitRegex({ numberingSystem }, append = \"\") {\n  const ns = numberingSystem || \"latn\";\n\n  if (!digitRegexCache[ns]) {\n    digitRegexCache[ns] = {};\n  }\n  if (!digitRegexCache[ns][append]) {\n    digitRegexCache[ns][append] = new RegExp(`${numberingSystems[ns]}${append}`);\n  }\n\n  return digitRegexCache[ns][append];\n}\n"], "mappings": "AAAA,MAAMA,gBAAgB,GAAG;EACvBC,IAAI,EAAE,iBAAiB;EACvBC,OAAO,EAAE,iBAAiB;EAC1BC,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE,iBAAiB;EACvBC,QAAQ,EAAE,iBAAiB;EAC3BC,IAAI,EAAE,iBAAiB;EACvBC,OAAO,EAAE,uBAAuB;EAChCC,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE,iBAAiB;EACvBC,OAAO,EAAE,iBAAiB;EAC1BC,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE;AACR,CAAC;AAED,MAAMC,qBAAqB,GAAG;EAC5BrB,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EAClBC,OAAO,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EACrBC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EAClBC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EAClBC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EAClBC,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EACxBC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EAClBE,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EAClBC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EAClBC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EAClBC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EAClBC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EAClBC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EAClBC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EAClBC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EAClBC,OAAO,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EACrBC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EAClBC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EAClBC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI;AACnB,CAAC;AAED,MAAMG,YAAY,GAAGvB,gBAAgB,CAACQ,OAAO,CAACgB,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAACC,KAAK,CAAC,EAAE,CAAC;AAE/E,OAAO,SAASC,WAAWA,CAACC,GAAG,EAAE;EAC/B,IAAIC,KAAK,GAAGC,QAAQ,CAACF,GAAG,EAAE,EAAE,CAAC;EAC7B,IAAIG,KAAK,CAACF,KAAK,CAAC,EAAE;IAChBA,KAAK,GAAG,EAAE;IACV,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,GAAG,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;MACnC,MAAME,IAAI,GAAGN,GAAG,CAACO,UAAU,CAACH,CAAC,CAAC;MAE9B,IAAIJ,GAAG,CAACI,CAAC,CAAC,CAACI,MAAM,CAACnC,gBAAgB,CAACQ,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;QAClDoB,KAAK,IAAIL,YAAY,CAACa,OAAO,CAACT,GAAG,CAACI,CAAC,CAAC,CAAC;MACvC,CAAC,MAAM;QACL,KAAK,MAAMM,GAAG,IAAIf,qBAAqB,EAAE;UACvC,MAAM,CAACgB,GAAG,EAAEC,GAAG,CAAC,GAAGjB,qBAAqB,CAACe,GAAG,CAAC;UAC7C,IAAIJ,IAAI,IAAIK,GAAG,IAAIL,IAAI,IAAIM,GAAG,EAAE;YAC9BX,KAAK,IAAIK,IAAI,GAAGK,GAAG;UACrB;QACF;MACF;IACF;IACA,OAAOT,QAAQ,CAACD,KAAK,EAAE,EAAE,CAAC;EAC5B,CAAC,MAAM;IACL,OAAOA,KAAK;EACd;AACF;;AAEA;AACA,IAAIY,eAAe,GAAG,CAAC,CAAC;AACxB,OAAO,SAASC,oBAAoBA,CAAA,EAAG;EACrCD,eAAe,GAAG,CAAC,CAAC;AACtB;AAEA,OAAO,SAASE,UAAUA,CAAC;EAAEC;AAAgB,CAAC,EAAEC,MAAM,GAAG,EAAE,EAAE;EAC3D,MAAMC,EAAE,GAAGF,eAAe,IAAI,MAAM;EAEpC,IAAI,CAACH,eAAe,CAACK,EAAE,CAAC,EAAE;IACxBL,eAAe,CAACK,EAAE,CAAC,GAAG,CAAC,CAAC;EAC1B;EACA,IAAI,CAACL,eAAe,CAACK,EAAE,CAAC,CAACD,MAAM,CAAC,EAAE;IAChCJ,eAAe,CAACK,EAAE,CAAC,CAACD,MAAM,CAAC,GAAG,IAAIE,MAAM,CAAE,GAAE9C,gBAAgB,CAAC6C,EAAE,CAAE,GAAED,MAAO,EAAC,CAAC;EAC9E;EAEA,OAAOJ,eAAe,CAACK,EAAE,CAAC,CAACD,MAAM,CAAC;AACpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}