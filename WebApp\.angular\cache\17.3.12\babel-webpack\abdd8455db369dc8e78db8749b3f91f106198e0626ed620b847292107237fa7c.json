{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Injectable, signal } from '@angular/core';\nlet ThemeService = class ThemeService {\n  constructor() {\n    this.THEME_KEY = 'theme';\n    this.isDarkMode = signal(false);\n    this.loadTheme();\n  }\n  toggleTheme() {\n    this.isDarkMode.update(dark => !dark);\n    this.applyTheme();\n    this.saveTheme();\n  }\n  loadTheme() {\n    const savedTheme = localStorage.getItem(this.THEME_KEY);\n    if (savedTheme) {\n      this.isDarkMode.set(savedTheme === 'dark');\n      this.applyTheme();\n    } else {\n      // Check system preference\n      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n      this.isDarkMode.set(prefersDark);\n      this.applyTheme();\n    }\n  }\n  saveTheme() {\n    localStorage.setItem(this.THEME_KEY, this.isDarkMode() ? 'dark' : 'light');\n  }\n  applyTheme() {\n    if (this.isDarkMode()) {\n      document.documentElement.classList.add('dark');\n    } else {\n      document.documentElement.classList.remove('dark');\n    }\n  }\n  static {\n    this.ctorParameters = () => [];\n  }\n};\nThemeService = __decorate([Injectable({\n  providedIn: 'root'\n})], ThemeService);\nexport { ThemeService };", "map": {"version": 3, "names": ["Injectable", "signal", "ThemeService", "constructor", "THEME_KEY", "isDarkMode", "loadTheme", "toggleTheme", "update", "dark", "applyTheme", "saveTheme", "savedTheme", "localStorage", "getItem", "set", "prefersDark", "window", "matchMedia", "matches", "setItem", "document", "documentElement", "classList", "add", "remove", "__decorate", "providedIn"], "sources": ["C:\\Users\\<USER>\\source\\ai-hub\\WebApp\\src\\shared\\services\\theam.service.ts"], "sourcesContent": ["import { Injectable, signal } from '@angular/core';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ThemeService {\r\n  private readonly THEME_KEY = 'theme';\r\n  isDarkMode = signal<boolean>(false);\r\n\r\n  constructor() {\r\n    this.loadTheme();\r\n  }\r\n\r\n  toggleTheme() {\r\n    this.isDarkMode.update(dark => !dark);\r\n    this.applyTheme();\r\n    this.saveTheme();\r\n  }\r\n\r\n  private loadTheme() {\r\n    const savedTheme = localStorage.getItem(this.THEME_KEY);\r\n    if (savedTheme) {\r\n      this.isDarkMode.set(savedTheme === 'dark');\r\n      this.applyTheme();\r\n    } else {\r\n      // Check system preference\r\n      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\r\n      this.isDarkMode.set(prefersDark);\r\n      this.applyTheme();\r\n    }\r\n  }\r\n\r\n  private saveTheme() {\r\n    localStorage.setItem(this.THEME_KEY, this.isDarkMode() ? 'dark' : 'light');\r\n  }\r\n\r\n  private applyTheme() {\r\n    if (this.isDarkMode()) {\r\n      document.documentElement.classList.add('dark');\r\n    } else {\r\n      document.documentElement.classList.remove('dark');\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,UAAU,EAAEC,MAAM,QAAQ,eAAe;AAK3C,IAAMC,YAAY,GAAlB,MAAMA,YAAY;EAIvBC,YAAA;IAHiB,KAAAC,SAAS,GAAG,OAAO;IACpC,KAAAC,UAAU,GAAGJ,MAAM,CAAU,KAAK,CAAC;IAGjC,IAAI,CAACK,SAAS,EAAE;EAClB;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACF,UAAU,CAACG,MAAM,CAACC,IAAI,IAAI,CAACA,IAAI,CAAC;IACrC,IAAI,CAACC,UAAU,EAAE;IACjB,IAAI,CAACC,SAAS,EAAE;EAClB;EAEQL,SAASA,CAAA;IACf,MAAMM,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,IAAI,CAACV,SAAS,CAAC;IACvD,IAAIQ,UAAU,EAAE;MACd,IAAI,CAACP,UAAU,CAACU,GAAG,CAACH,UAAU,KAAK,MAAM,CAAC;MAC1C,IAAI,CAACF,UAAU,EAAE;KAClB,MAAM;MACL;MACA,MAAMM,WAAW,GAAGC,MAAM,CAACC,UAAU,CAAC,8BAA8B,CAAC,CAACC,OAAO;MAC7E,IAAI,CAACd,UAAU,CAACU,GAAG,CAACC,WAAW,CAAC;MAChC,IAAI,CAACN,UAAU,EAAE;;EAErB;EAEQC,SAASA,CAAA;IACfE,YAAY,CAACO,OAAO,CAAC,IAAI,CAAChB,SAAS,EAAE,IAAI,CAACC,UAAU,EAAE,GAAG,MAAM,GAAG,OAAO,CAAC;EAC5E;EAEQK,UAAUA,CAAA;IAChB,IAAI,IAAI,CAACL,UAAU,EAAE,EAAE;MACrBgB,QAAQ,CAACC,eAAe,CAACC,SAAS,CAACC,GAAG,CAAC,MAAM,CAAC;KAC/C,MAAM;MACLH,QAAQ,CAACC,eAAe,CAACC,SAAS,CAACE,MAAM,CAAC,MAAM,CAAC;;EAErD;;;;;AArCWvB,YAAY,GAAAwB,UAAA,EAHxB1B,UAAU,CAAC;EACV2B,UAAU,EAAE;CACb,CAAC,C,EACWzB,YAAY,CAsCxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}