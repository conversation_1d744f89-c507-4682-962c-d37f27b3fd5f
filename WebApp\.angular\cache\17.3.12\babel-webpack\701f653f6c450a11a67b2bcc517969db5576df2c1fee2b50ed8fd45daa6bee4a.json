{"ast": null, "code": "class Theme {\n  static DEFAULTS = {\n    modules: {}\n  };\n  static themes = {\n    default: Theme\n  };\n  modules = {};\n  constructor(quill, options) {\n    this.quill = quill;\n    this.options = options;\n  }\n  init() {\n    Object.keys(this.options.modules).forEach(name => {\n      if (this.modules[name] == null) {\n        this.addModule(name);\n      }\n    });\n  }\n  addModule(name) {\n    // @ts-expect-error\n    const ModuleClass = this.quill.constructor.import(`modules/${name}`);\n    this.modules[name] = new ModuleClass(this.quill, this.options.modules[name] || {});\n    return this.modules[name];\n  }\n}\nexport default Theme;", "map": {"version": 3, "names": ["Theme", "DEFAULTS", "modules", "themes", "default", "constructor", "quill", "options", "init", "Object", "keys", "for<PERSON>ach", "name", "addModule", "ModuleClass", "import"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/quill/core/theme.js"], "sourcesContent": ["class Theme {\n  static DEFAULTS = {\n    modules: {}\n  };\n  static themes = {\n    default: Theme\n  };\n  modules = {};\n  constructor(quill, options) {\n    this.quill = quill;\n    this.options = options;\n  }\n  init() {\n    Object.keys(this.options.modules).forEach(name => {\n      if (this.modules[name] == null) {\n        this.addModule(name);\n      }\n    });\n  }\n  addModule(name) {\n    // @ts-expect-error\n    const ModuleClass = this.quill.constructor.import(`modules/${name}`);\n    this.modules[name] = new ModuleClass(this.quill, this.options.modules[name] || {});\n    return this.modules[name];\n  }\n}\nexport default Theme;\n"], "mappings": "AAAA,MAAMA,KAAK,CAAC;EACV,OAAOC,QAAQ,GAAG;IAChBC,OAAO,EAAE,CAAC;EACZ,CAAC;EACD,OAAOC,MAAM,GAAG;IACdC,OAAO,EAAEJ;EACX,CAAC;EACDE,OAAO,GAAG,CAAC,CAAC;EACZG,WAAWA,CAACC,KAAK,EAAEC,OAAO,EAAE;IAC1B,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,OAAO,GAAGA,OAAO;EACxB;EACAC,IAAIA,CAAA,EAAG;IACLC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACH,OAAO,CAACL,OAAO,CAAC,CAACS,OAAO,CAACC,IAAI,IAAI;MAChD,IAAI,IAAI,CAACV,OAAO,CAACU,IAAI,CAAC,IAAI,IAAI,EAAE;QAC9B,IAAI,CAACC,SAAS,CAACD,IAAI,CAAC;MACtB;IACF,CAAC,CAAC;EACJ;EACAC,SAASA,CAACD,IAAI,EAAE;IACd;IACA,MAAME,WAAW,GAAG,IAAI,CAACR,KAAK,CAACD,WAAW,CAACU,MAAM,CAAE,WAAUH,IAAK,EAAC,CAAC;IACpE,IAAI,CAACV,OAAO,CAACU,IAAI,CAAC,GAAG,IAAIE,WAAW,CAAC,IAAI,CAACR,KAAK,EAAE,IAAI,CAACC,OAAO,CAACL,OAAO,CAACU,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAClF,OAAO,IAAI,CAACV,OAAO,CAACU,IAAI,CAAC;EAC3B;AACF;AACA,eAAeZ,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}