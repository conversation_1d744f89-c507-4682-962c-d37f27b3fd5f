{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/source/ai-hub/WebApp/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { TestBed } from '@angular/core/testing';\nimport { DocumentDetailsComponent } from './document-details.component';\nimport { ActivatedRoute } from '@angular/router';\nimport { NotesService } from '../services/notes.service';\nimport { of } from 'rxjs';\nimport { MarkdownModule } from 'ngx-markdown';\ndescribe('DocumentDetailsComponent', () => {\n  let component;\n  let fixture;\n  const mockNotesService = {\n    getNoteById: jasmine.createSpy('getNoteById').and.returnValue(of({\n      id: 1,\n      title: 'Test Note',\n      content: '# Test Content'\n    }))\n  };\n  const mockActivatedRoute = {\n    params: of({\n      id: '1'\n    })\n  };\n  beforeEach( /*#__PURE__*/_asyncToGenerator(function* () {\n    yield TestBed.configureTestingModule({\n      imports: [DocumentDetailsComponent, MarkdownModule.forRoot()],\n      providers: [{\n        provide: NotesService,\n        useValue: mockNotesService\n      }, {\n        provide: ActivatedRoute,\n        useValue: mockActivatedRoute\n      }]\n    }).compileComponents();\n    fixture = TestBed.createComponent(DocumentDetailsComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  }));\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n  it('should load note details on init', () => {\n    expect(mockNotesService.getNoteById).toHaveBeenCalledWith('1');\n  });\n});", "map": {"version": 3, "names": ["TestBed", "DocumentDetailsComponent", "ActivatedRoute", "NotesService", "of", "MarkdownModule", "describe", "component", "fixture", "mockNotesService", "getNoteById", "jasmine", "createSpy", "and", "returnValue", "id", "title", "content", "mockActivatedRoute", "params", "beforeEach", "_asyncToGenerator", "configureTestingModule", "imports", "forRoot", "providers", "provide", "useValue", "compileComponents", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy", "toHaveBeenCalledWith"], "sources": ["C:\\Users\\<USER>\\source\\ai-hub\\WebApp\\src\\app\\MyNotesProjects\\document-details\\document-details.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed } from '@angular/core/testing';\r\nimport { DocumentDetailsComponent } from './document-details.component';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { NotesService } from '../services/notes.service';\r\nimport { of } from 'rxjs';\r\nimport { MarkdownModule } from 'ngx-markdown';\r\n\r\ndescribe('DocumentDetailsComponent', () => {\r\n  let component: DocumentDetailsComponent;\r\n  let fixture: ComponentFixture<DocumentDetailsComponent>;\r\n\r\n  const mockNotesService = {\r\n    getNoteById: jasmine.createSpy('getNoteById').and.returnValue(of({\r\n      id: 1,\r\n      title: 'Test Note',\r\n      content: '# Test Content'\r\n    }))\r\n  };\r\n\r\n  const mockActivatedRoute = {\r\n    params: of({ id: '1' })\r\n  };\r\n\r\n  beforeEach(async () => {\r\n    await TestBed.configureTestingModule({\r\n      imports: [DocumentDetailsComponent, MarkdownModule.forRoot()],\r\n      providers: [\r\n        { provide: NotesService, useValue: mockNotesService },\r\n        { provide: ActivatedRoute, useValue: mockActivatedRoute }\r\n      ]\r\n    })\r\n    .compileComponents();\r\n\r\n    fixture = TestBed.createComponent(DocumentDetailsComponent);\r\n    component = fixture.componentInstance;\r\n    fixture.detectChanges();\r\n  });\r\n\r\n  it('should create', () => {\r\n    expect(component).toBeTruthy();\r\n  });\r\n\r\n  it('should load note details on init', () => {\r\n    expect(mockNotesService.getNoteById).toHaveBeenCalledWith('1');\r\n  });\r\n});\r\n"], "mappings": ";AAAA,SAA2BA,OAAO,QAAQ,uBAAuB;AACjE,SAASC,wBAAwB,QAAQ,8BAA8B;AACvE,SAASC,cAAc,QAAQ,iBAAiB;AAChD,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,EAAE,QAAQ,MAAM;AACzB,SAASC,cAAc,QAAQ,cAAc;AAE7CC,QAAQ,CAAC,0BAA0B,EAAE,MAAK;EACxC,IAAIC,SAAmC;EACvC,IAAIC,OAAmD;EAEvD,MAAMC,gBAAgB,GAAG;IACvBC,WAAW,EAAEC,OAAO,CAACC,SAAS,CAAC,aAAa,CAAC,CAACC,GAAG,CAACC,WAAW,CAACV,EAAE,CAAC;MAC/DW,EAAE,EAAE,CAAC;MACLC,KAAK,EAAE,WAAW;MAClBC,OAAO,EAAE;KACV,CAAC;GACH;EAED,MAAMC,kBAAkB,GAAG;IACzBC,MAAM,EAAEf,EAAE,CAAC;MAAEW,EAAE,EAAE;IAAG,CAAE;GACvB;EAEDK,UAAU,eAAAC,iBAAA,CAAC,aAAW;IACpB,MAAMrB,OAAO,CAACsB,sBAAsB,CAAC;MACnCC,OAAO,EAAE,CAACtB,wBAAwB,EAAEI,cAAc,CAACmB,OAAO,EAAE,CAAC;MAC7DC,SAAS,EAAE,CACT;QAAEC,OAAO,EAAEvB,YAAY;QAAEwB,QAAQ,EAAElB;MAAgB,CAAE,EACrD;QAAEiB,OAAO,EAAExB,cAAc;QAAEyB,QAAQ,EAAET;MAAkB,CAAE;KAE5D,CAAC,CACDU,iBAAiB,EAAE;IAEpBpB,OAAO,GAAGR,OAAO,CAAC6B,eAAe,CAAC5B,wBAAwB,CAAC;IAC3DM,SAAS,GAAGC,OAAO,CAACsB,iBAAiB;IACrCtB,OAAO,CAACuB,aAAa,EAAE;EACzB,CAAC,EAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAAC1B,SAAS,CAAC,CAAC2B,UAAU,EAAE;EAChC,CAAC,CAAC;EAEFF,EAAE,CAAC,kCAAkC,EAAE,MAAK;IAC1CC,MAAM,CAACxB,gBAAgB,CAACC,WAAW,CAAC,CAACyB,oBAAoB,CAAC,GAAG,CAAC;EAChE,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}