{"ast": null, "code": "(function () {\n  \"use strict\";\n\n  try {\n    if (typeof document < \"u\") {\n      var e = document.createElement(\"style\");\n      e.appendChild(document.createTextNode(`.cdx-warning{position:relative}@media all and (min-width: 736px){.cdx-warning{padding-left:36px}}.cdx-warning [contentEditable=true][data-placeholder]:before{position:absolute;content:attr(data-placeholder);color:#707684;font-weight:400;opacity:0}.cdx-warning [contentEditable=true][data-placeholder]:empty:before{opacity:1}.cdx-warning [contentEditable=true][data-placeholder]:empty:focus:before{opacity:0}.cdx-warning:before{content:\"\";background-image:url(\"data:image/svg+xml,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Crect x='5' y='5' width='14' height='14' rx='4' stroke='black' stroke-width='2'/%3E%3Cline x1='12' y1='9' x2='12' y2='12' stroke='black' stroke-width='2' stroke-linecap='round'/%3E%3Cpath d='M12 15.02V15.01' stroke='black' stroke-width='2' stroke-linecap='round'/%3E%3C/svg%3E\");width:24px;height:24px;background-size:24px 24px;position:absolute;margin-top:8px;left:0}@media all and (max-width: 735px){.cdx-warning:before{display:none}}.cdx-warning__message{min-height:85px}.cdx-warning__title{margin-bottom:6px}`)), document.head.appendChild(e);\n    }\n  } catch (t) {\n    console.error(\"vite-plugin-css-injected-by-js\", t);\n  }\n})();\nconst l = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><rect width=\"14\" height=\"14\" x=\"5\" y=\"5\" stroke=\"currentColor\" stroke-width=\"2\" rx=\"4\"/><line x1=\"12\" x2=\"12\" y1=\"9\" y2=\"12\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M12 15.02V15.01\"/></svg>';\nclass i {\n  /**\n   * Notify core that read-only mode is supported\n   */\n  static get isReadOnlySupported() {\n    return !0;\n  }\n  /**\n   * Get Toolbox settings\n   *\n   * @public\n   * @returns {ToolboxConfig} An object containing Tool's icon and title.\n   */\n  static get toolbox() {\n    return {\n      icon: l,\n      title: \"Warning\"\n    };\n  }\n  /**\n   * Allow to press Enter inside the Warning\n   *\n   * @public\n   * @returns {boolean}\n   */\n  static get enableLineBreaks() {\n    return !0;\n  }\n  /**\n   * Default placeholder for warning title\n   *\n   * @public\n   * @returns {string}\n   */\n  static get DEFAULT_TITLE_PLACEHOLDER() {\n    return \"Title\";\n  }\n  /**\n   * Default placeholder for warning message\n   *\n   * @public\n   * @returns {string}\n   */\n  static get DEFAULT_MESSAGE_PLACEHOLDER() {\n    return \"Message\";\n  }\n  /**\n   * Warning Tool`s styles\n   *\n   * @returns {WarningCSS} An object containing Tool`s CSS classnames.\n   */\n  get CSS() {\n    return {\n      baseClass: this.api.styles.block,\n      wrapper: \"cdx-warning\",\n      title: \"cdx-warning__title\",\n      input: this.api.styles.input,\n      message: \"cdx-warning__message\"\n    };\n  }\n  /**\n   * Render plugin`s main Element and fill it with saved data\n   *\n   * @param {object} params — constructor params\n   * @param {WarningData} params.data — previously saved data\n   * @param {WarningConfig} params.config — user config for Tool\n   * @param {API} params.api - Editor.js API\n   * @param {boolean} params.readOnly - read-only mode flag\n   */\n  constructor({\n    data: e,\n    config: t,\n    api: s,\n    readOnly: r\n  }) {\n    this.api = s, this.readOnly = r, this.titlePlaceholder = (t == null ? void 0 : t.titlePlaceholder) || i.DEFAULT_TITLE_PLACEHOLDER, this.messagePlaceholder = (t == null ? void 0 : t.messagePlaceholder) || i.DEFAULT_MESSAGE_PLACEHOLDER, this.data = {\n      title: e.title || \"\",\n      message: e.message || \"\"\n    };\n  }\n  /**\n   * Create Warning Tool container with inputs\n   *\n   * @returns {Element} Html element of Warning Tool.\n   */\n  render() {\n    const e = this._make(\"div\", [this.CSS.baseClass, this.CSS.wrapper]),\n      t = this._make(\"div\", [this.CSS.input, this.CSS.title], {\n        contentEditable: !this.readOnly,\n        innerHTML: this.data.title\n      }),\n      s = this._make(\"div\", [this.CSS.input, this.CSS.message], {\n        contentEditable: !this.readOnly,\n        innerHTML: this.data.message\n      });\n    return t.dataset.placeholder = this.titlePlaceholder, s.dataset.placeholder = this.messagePlaceholder, e.appendChild(t), e.appendChild(s), e;\n  }\n  /**\n   * Extract Warning data from Warning Tool element\n   *\n   * @param {HTMLDivElement} warningElement - element to save\n   * @returns {WarningData} Warning Tool`s data.\n   */\n  save(e) {\n    const t = e.querySelector(`.${this.CSS.title}`),\n      s = e.querySelector(`.${this.CSS.message}`);\n    return Object.assign(this.data, {\n      title: (t == null ? void 0 : t.innerHTML) ?? \"\",\n      message: (s == null ? void 0 : s.innerHTML) ?? \"\"\n    });\n  }\n  /**\n   * Helper for making Elements with attributes\n   *\n   * @param  {string} tagName           - new Element tag name\n   * @param  {Array|string} classNames  - list or name of CSS classname(s)\n   * @param  {object} attributes        - any attributes\n   * @returns {Element} Html element of {tagName}.\n   */\n  _make(e, t = null, s = {}) {\n    const r = document.createElement(e);\n    Array.isArray(t) ? r.classList.add(...t) : t && r.classList.add(t);\n    for (const a in s) r[a] = s[a];\n    return r;\n  }\n  /**\n   * Sanitizer config for Warning Tool saved data\n   *\n   */\n  static get sanitize() {\n    return {\n      title: {},\n      message: {}\n    };\n  }\n}\nexport { i as default };", "map": {"version": 3, "names": ["document", "e", "createElement", "append<PERSON><PERSON><PERSON>", "createTextNode", "head", "t", "console", "error", "l", "i", "isReadOnlySupported", "toolbox", "icon", "title", "enableLineBreaks", "DEFAULT_TITLE_PLACEHOLDER", "DEFAULT_MESSAGE_PLACEHOLDER", "CSS", "baseClass", "api", "styles", "block", "wrapper", "input", "message", "constructor", "data", "config", "s", "readOnly", "r", "titlePlaceholder", "messagePlaceholder", "render", "_make", "contentEditable", "innerHTML", "dataset", "placeholder", "save", "querySelector", "Object", "assign", "Array", "isArray", "classList", "add", "a", "sanitize", "default"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/@editorjs/warning/dist/warning.mjs"], "sourcesContent": ["(function(){\"use strict\";try{if(typeof document<\"u\"){var e=document.createElement(\"style\");e.appendChild(document.createTextNode(`.cdx-warning{position:relative}@media all and (min-width: 736px){.cdx-warning{padding-left:36px}}.cdx-warning [contentEditable=true][data-placeholder]:before{position:absolute;content:attr(data-placeholder);color:#707684;font-weight:400;opacity:0}.cdx-warning [contentEditable=true][data-placeholder]:empty:before{opacity:1}.cdx-warning [contentEditable=true][data-placeholder]:empty:focus:before{opacity:0}.cdx-warning:before{content:\"\";background-image:url(\"data:image/svg+xml,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Crect x='5' y='5' width='14' height='14' rx='4' stroke='black' stroke-width='2'/%3E%3Cline x1='12' y1='9' x2='12' y2='12' stroke='black' stroke-width='2' stroke-linecap='round'/%3E%3Cpath d='M12 15.02V15.01' stroke='black' stroke-width='2' stroke-linecap='round'/%3E%3C/svg%3E\");width:24px;height:24px;background-size:24px 24px;position:absolute;margin-top:8px;left:0}@media all and (max-width: 735px){.cdx-warning:before{display:none}}.cdx-warning__message{min-height:85px}.cdx-warning__title{margin-bottom:6px}`)),document.head.appendChild(e)}}catch(t){console.error(\"vite-plugin-css-injected-by-js\",t)}})();\nconst l = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><rect width=\"14\" height=\"14\" x=\"5\" y=\"5\" stroke=\"currentColor\" stroke-width=\"2\" rx=\"4\"/><line x1=\"12\" x2=\"12\" y1=\"9\" y2=\"12\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M12 15.02V15.01\"/></svg>';\nclass i {\n  /**\n   * Notify core that read-only mode is supported\n   */\n  static get isReadOnlySupported() {\n    return !0;\n  }\n  /**\n   * Get Toolbox settings\n   *\n   * @public\n   * @returns {ToolboxConfig} An object containing Tool's icon and title.\n   */\n  static get toolbox() {\n    return {\n      icon: l,\n      title: \"Warning\"\n    };\n  }\n  /**\n   * Allow to press Enter inside the Warning\n   *\n   * @public\n   * @returns {boolean}\n   */\n  static get enableLineBreaks() {\n    return !0;\n  }\n  /**\n   * Default placeholder for warning title\n   *\n   * @public\n   * @returns {string}\n   */\n  static get DEFAULT_TITLE_PLACEHOLDER() {\n    return \"Title\";\n  }\n  /**\n   * Default placeholder for warning message\n   *\n   * @public\n   * @returns {string}\n   */\n  static get DEFAULT_MESSAGE_PLACEHOLDER() {\n    return \"Message\";\n  }\n  /**\n   * Warning Tool`s styles\n   *\n   * @returns {WarningCSS} An object containing Tool`s CSS classnames.\n   */\n  get CSS() {\n    return {\n      baseClass: this.api.styles.block,\n      wrapper: \"cdx-warning\",\n      title: \"cdx-warning__title\",\n      input: this.api.styles.input,\n      message: \"cdx-warning__message\"\n    };\n  }\n  /**\n   * Render plugin`s main Element and fill it with saved data\n   *\n   * @param {object} params — constructor params\n   * @param {WarningData} params.data — previously saved data\n   * @param {WarningConfig} params.config — user config for Tool\n   * @param {API} params.api - Editor.js API\n   * @param {boolean} params.readOnly - read-only mode flag\n   */\n  constructor({ data: e, config: t, api: s, readOnly: r }) {\n    this.api = s, this.readOnly = r, this.titlePlaceholder = (t == null ? void 0 : t.titlePlaceholder) || i.DEFAULT_TITLE_PLACEHOLDER, this.messagePlaceholder = (t == null ? void 0 : t.messagePlaceholder) || i.DEFAULT_MESSAGE_PLACEHOLDER, this.data = {\n      title: e.title || \"\",\n      message: e.message || \"\"\n    };\n  }\n  /**\n   * Create Warning Tool container with inputs\n   *\n   * @returns {Element} Html element of Warning Tool.\n   */\n  render() {\n    const e = this._make(\"div\", [this.CSS.baseClass, this.CSS.wrapper]), t = this._make(\"div\", [this.CSS.input, this.CSS.title], {\n      contentEditable: !this.readOnly,\n      innerHTML: this.data.title\n    }), s = this._make(\"div\", [this.CSS.input, this.CSS.message], {\n      contentEditable: !this.readOnly,\n      innerHTML: this.data.message\n    });\n    return t.dataset.placeholder = this.titlePlaceholder, s.dataset.placeholder = this.messagePlaceholder, e.appendChild(t), e.appendChild(s), e;\n  }\n  /**\n   * Extract Warning data from Warning Tool element\n   *\n   * @param {HTMLDivElement} warningElement - element to save\n   * @returns {WarningData} Warning Tool`s data.\n   */\n  save(e) {\n    const t = e.querySelector(`.${this.CSS.title}`), s = e.querySelector(`.${this.CSS.message}`);\n    return Object.assign(this.data, {\n      title: (t == null ? void 0 : t.innerHTML) ?? \"\",\n      message: (s == null ? void 0 : s.innerHTML) ?? \"\"\n    });\n  }\n  /**\n   * Helper for making Elements with attributes\n   *\n   * @param  {string} tagName           - new Element tag name\n   * @param  {Array|string} classNames  - list or name of CSS classname(s)\n   * @param  {object} attributes        - any attributes\n   * @returns {Element} Html element of {tagName}.\n   */\n  _make(e, t = null, s = {}) {\n    const r = document.createElement(e);\n    Array.isArray(t) ? r.classList.add(...t) : t && r.classList.add(t);\n    for (const a in s)\n      r[a] = s[a];\n    return r;\n  }\n  /**\n   * Sanitizer config for Warning Tool saved data\n   *\n   */\n  static get sanitize() {\n    return {\n      title: {},\n      message: {}\n    };\n  }\n}\nexport {\n  i as default\n};\n"], "mappings": "AAAA,CAAC,YAAU;EAAC,YAAY;;EAAC,IAAG;IAAC,IAAG,OAAOA,QAAQ,GAAC,GAAG,EAAC;MAAC,IAAIC,CAAC,GAACD,QAAQ,CAACE,aAAa,CAAC,OAAO,CAAC;MAACD,CAAC,CAACE,WAAW,CAACH,QAAQ,CAACI,cAAc,CAAE,skCAAqkC,CAAC,CAAC,EAACJ,QAAQ,CAACK,IAAI,CAACF,WAAW,CAACF,CAAC,CAAC;IAAA;EAAC,CAAC,QAAMK,CAAC,EAAC;IAACC,OAAO,CAACC,KAAK,CAAC,gCAAgC,EAACF,CAAC,CAAC;EAAA;AAAC,CAAC,EAAE,CAAC;AACvyC,MAAMG,CAAC,GAAG,4XAA4X;AACtY,MAAMC,CAAC,CAAC;EACN;AACF;AACA;EACE,WAAWC,mBAAmBA,CAAA,EAAG;IAC/B,OAAO,CAAC,CAAC;EACX;EACA;AACF;AACA;AACA;AACA;AACA;EACE,WAAWC,OAAOA,CAAA,EAAG;IACnB,OAAO;MACLC,IAAI,EAAEJ,CAAC;MACPK,KAAK,EAAE;IACT,CAAC;EACH;EACA;AACF;AACA;AACA;AACA;AACA;EACE,WAAWC,gBAAgBA,CAAA,EAAG;IAC5B,OAAO,CAAC,CAAC;EACX;EACA;AACF;AACA;AACA;AACA;AACA;EACE,WAAWC,yBAAyBA,CAAA,EAAG;IACrC,OAAO,OAAO;EAChB;EACA;AACF;AACA;AACA;AACA;AACA;EACE,WAAWC,2BAA2BA,CAAA,EAAG;IACvC,OAAO,SAAS;EAClB;EACA;AACF;AACA;AACA;AACA;EACE,IAAIC,GAAGA,CAAA,EAAG;IACR,OAAO;MACLC,SAAS,EAAE,IAAI,CAACC,GAAG,CAACC,MAAM,CAACC,KAAK;MAChCC,OAAO,EAAE,aAAa;MACtBT,KAAK,EAAE,oBAAoB;MAC3BU,KAAK,EAAE,IAAI,CAACJ,GAAG,CAACC,MAAM,CAACG,KAAK;MAC5BC,OAAO,EAAE;IACX,CAAC;EACH;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,WAAWA,CAAC;IAAEC,IAAI,EAAE1B,CAAC;IAAE2B,MAAM,EAAEtB,CAAC;IAAEc,GAAG,EAAES,CAAC;IAAEC,QAAQ,EAAEC;EAAE,CAAC,EAAE;IACvD,IAAI,CAACX,GAAG,GAAGS,CAAC,EAAE,IAAI,CAACC,QAAQ,GAAGC,CAAC,EAAE,IAAI,CAACC,gBAAgB,GAAG,CAAC1B,CAAC,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,CAAC,CAAC0B,gBAAgB,KAAKtB,CAAC,CAACM,yBAAyB,EAAE,IAAI,CAACiB,kBAAkB,GAAG,CAAC3B,CAAC,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,CAAC,CAAC2B,kBAAkB,KAAKvB,CAAC,CAACO,2BAA2B,EAAE,IAAI,CAACU,IAAI,GAAG;MACrPb,KAAK,EAAEb,CAAC,CAACa,KAAK,IAAI,EAAE;MACpBW,OAAO,EAAExB,CAAC,CAACwB,OAAO,IAAI;IACxB,CAAC;EACH;EACA;AACF;AACA;AACA;AACA;EACES,MAAMA,CAAA,EAAG;IACP,MAAMjC,CAAC,GAAG,IAAI,CAACkC,KAAK,CAAC,KAAK,EAAE,CAAC,IAAI,CAACjB,GAAG,CAACC,SAAS,EAAE,IAAI,CAACD,GAAG,CAACK,OAAO,CAAC,CAAC;MAAEjB,CAAC,GAAG,IAAI,CAAC6B,KAAK,CAAC,KAAK,EAAE,CAAC,IAAI,CAACjB,GAAG,CAACM,KAAK,EAAE,IAAI,CAACN,GAAG,CAACJ,KAAK,CAAC,EAAE;QAC3HsB,eAAe,EAAE,CAAC,IAAI,CAACN,QAAQ;QAC/BO,SAAS,EAAE,IAAI,CAACV,IAAI,CAACb;MACvB,CAAC,CAAC;MAAEe,CAAC,GAAG,IAAI,CAACM,KAAK,CAAC,KAAK,EAAE,CAAC,IAAI,CAACjB,GAAG,CAACM,KAAK,EAAE,IAAI,CAACN,GAAG,CAACO,OAAO,CAAC,EAAE;QAC5DW,eAAe,EAAE,CAAC,IAAI,CAACN,QAAQ;QAC/BO,SAAS,EAAE,IAAI,CAACV,IAAI,CAACF;MACvB,CAAC,CAAC;IACF,OAAOnB,CAAC,CAACgC,OAAO,CAACC,WAAW,GAAG,IAAI,CAACP,gBAAgB,EAAEH,CAAC,CAACS,OAAO,CAACC,WAAW,GAAG,IAAI,CAACN,kBAAkB,EAAEhC,CAAC,CAACE,WAAW,CAACG,CAAC,CAAC,EAAEL,CAAC,CAACE,WAAW,CAAC0B,CAAC,CAAC,EAAE5B,CAAC;EAC9I;EACA;AACF;AACA;AACA;AACA;AACA;EACEuC,IAAIA,CAACvC,CAAC,EAAE;IACN,MAAMK,CAAC,GAAGL,CAAC,CAACwC,aAAa,CAAE,IAAG,IAAI,CAACvB,GAAG,CAACJ,KAAM,EAAC,CAAC;MAAEe,CAAC,GAAG5B,CAAC,CAACwC,aAAa,CAAE,IAAG,IAAI,CAACvB,GAAG,CAACO,OAAQ,EAAC,CAAC;IAC5F,OAAOiB,MAAM,CAACC,MAAM,CAAC,IAAI,CAAChB,IAAI,EAAE;MAC9Bb,KAAK,EAAE,CAACR,CAAC,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,CAAC,CAAC+B,SAAS,KAAK,EAAE;MAC/CZ,OAAO,EAAE,CAACI,CAAC,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,CAAC,CAACQ,SAAS,KAAK;IACjD,CAAC,CAAC;EACJ;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEF,KAAKA,CAAClC,CAAC,EAAEK,CAAC,GAAG,IAAI,EAAEuB,CAAC,GAAG,CAAC,CAAC,EAAE;IACzB,MAAME,CAAC,GAAG/B,QAAQ,CAACE,aAAa,CAACD,CAAC,CAAC;IACnC2C,KAAK,CAACC,OAAO,CAACvC,CAAC,CAAC,GAAGyB,CAAC,CAACe,SAAS,CAACC,GAAG,CAAC,GAAGzC,CAAC,CAAC,GAAGA,CAAC,IAAIyB,CAAC,CAACe,SAAS,CAACC,GAAG,CAACzC,CAAC,CAAC;IAClE,KAAK,MAAM0C,CAAC,IAAInB,CAAC,EACfE,CAAC,CAACiB,CAAC,CAAC,GAAGnB,CAAC,CAACmB,CAAC,CAAC;IACb,OAAOjB,CAAC;EACV;EACA;AACF;AACA;AACA;EACE,WAAWkB,QAAQA,CAAA,EAAG;IACpB,OAAO;MACLnC,KAAK,EAAE,CAAC,CAAC;MACTW,OAAO,EAAE,CAAC;IACZ,CAAC;EACH;AACF;AACA,SACEf,CAAC,IAAIwC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}