{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./chat-model.component.html?ngResource\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { ChatModelServiceProxy } from '../../../shared/service-proxies/service-proxies';\nimport { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';\nlet ChatModelComponent = class ChatModelComponent {\n  constructor(chatModelService) {\n    this.chatModelService = chatModelService;\n    this.models = [];\n    this.currentModel = {\n      id: '',\n      modelId: '',\n      apIkey: '',\n      endpoint: '',\n      provider: ''\n    };\n    this.isEditing = false;\n    this.showForm = false;\n  }\n  ngOnInit() {\n    this.loadChatModels();\n  }\n  loadChatModels() {\n    this.chatModelService.getAll().subscribe(result => {\n      this.models = result;\n    });\n  }\n  addNewModel() {\n    this.isEditing = false;\n    this.currentModel = {\n      modelId: '',\n      apIkey: '',\n      endpoint: '',\n      provider: ''\n    };\n    this.showForm = true;\n  }\n  editModel(model) {\n    this.isEditing = true;\n    this.currentModel = {\n      ...model\n    };\n    this.showForm = true;\n  }\n  saveModel() {\n    this.chatModelService.createOrUpdate(this.currentModel).subscribe(res => {\n      console.log(res);\n      if (res) {\n        if (this.isEditing) {\n          this.models = this.models.map(m => m.id === this.currentModel.id ? this.currentModel : m);\n        } else {\n          this.models.push(res);\n        }\n        this.showForm = false;\n        this.resetForm();\n      }\n    });\n  }\n  deleteModel(modelId) {\n    if (confirm('Are you sure you want to delete this model?')) {\n      this.chatModelService.delete(modelId).subscribe(res => {\n        if (res) {\n          this.models = this.models.filter(model => model.modelId !== modelId);\n        }\n      });\n    }\n  }\n  resetForm() {\n    this.currentModel = {\n      id: '',\n      modelId: '',\n      apIkey: '',\n      endpoint: '',\n      provider: ''\n    };\n    this.showForm = false;\n  }\n  hideApiKey(apiKey) {\n    if (!apiKey) return '';\n    return apiKey.substring(0, 8) + '...' + apiKey.substring(apiKey.length - 4);\n  }\n  generateUniqueId() {\n    return Date.now().toString(36) + Math.random().toString(36).substring(2);\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: ChatModelServiceProxy\n    }];\n  }\n};\nChatModelComponent = __decorate([Component({\n  selector: 'app-chat-model',\n  standalone: true,\n  imports: [CommonModule, FormsModule, ServiceProxyModule],\n  template: __NG_CLI_RESOURCE__0\n})], ChatModelComponent);\nexport { ChatModelComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "FormsModule", "ChatModelServiceProxy", "ServiceProxyModule", "ChatModelComponent", "constructor", "chatModelService", "models", "currentModel", "id", "modelId", "apIkey", "endpoint", "provider", "isEditing", "showForm", "ngOnInit", "loadChatModels", "getAll", "subscribe", "result", "addNewModel", "editModel", "model", "saveModel", "createOrUpdate", "res", "console", "log", "map", "m", "push", "resetForm", "deleteModel", "confirm", "delete", "filter", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "substring", "length", "generateUniqueId", "Date", "now", "toString", "Math", "random", "__decorate", "selector", "standalone", "imports", "template", "__NG_CLI_RESOURCE__0"], "sources": ["C:\\Users\\<USER>\\source\\ai-hub\\WebApp\\src\\app\\ai-settings\\chat-model\\chat-model.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { ChatModelServiceProxy } from '../../../shared/service-proxies/service-proxies';\r\nimport { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';\r\n\r\n@Component({\r\n  selector: 'app-chat-model',\r\n  standalone: true,\r\n  imports: [CommonModule, FormsModule, ServiceProxyModule],\r\n  templateUrl: './chat-model.component.html',\r\n})\r\nexport class ChatModelComponent implements OnInit {\r\n  models: any[] = [];\r\n  currentModel: any = {\r\n    id: '',\r\n    modelId: '',\r\n    apIkey: '',\r\n    endpoint: '',\r\n    provider: ''\r\n  };\r\n  isEditing = false;\r\n  showForm = false;\r\n\r\n  constructor(private chatModelService: ChatModelServiceProxy) {}\r\n\r\n  ngOnInit(): void {\r\n    this.loadChatModels();\r\n  }\r\n\r\n  loadChatModels() {\r\n    this.chatModelService.getAll().subscribe((result) => {\r\n      this.models = result;\r\n    });\r\n  }\r\n\r\n  addNewModel(): void {\r\n    this.isEditing = false;\r\n    this.currentModel = {\r\n      modelId: '',\r\n      apIkey: '',\r\n      endpoint: '',\r\n      provider: ''\r\n    };\r\n    this.showForm = true;\r\n  }\r\n\r\n  editModel(model: any): void {\r\n    this.isEditing = true;\r\n    this.currentModel = { ...model };\r\n    this.showForm = true;\r\n  }\r\n\r\n  saveModel(): void {\r\n    this.chatModelService.createOrUpdate(this.currentModel).subscribe((res: any) => {\r\n      console.log(res);\r\n\r\n      if (res) {\r\n        if (this.isEditing) {\r\n          this.models = this.models.map(m => m.id === this.currentModel.id ? this.currentModel : m);\r\n        } else {\r\n          this.models.push(res);\r\n        }\r\n        this.showForm = false;\r\n        this.resetForm();\r\n      }\r\n    });\r\n  }\r\n\r\n  deleteModel(modelId: string): void {\r\n    if (confirm('Are you sure you want to delete this model?')) {\r\n      this.chatModelService.delete(modelId).subscribe((res: any) => {\r\n        if (res) {\r\n          this.models = this.models.filter(model => model.modelId !== modelId);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  resetForm(): void {\r\n    this.currentModel = {\r\n      id: '',\r\n      modelId: '',\r\n      apIkey: '',\r\n      endpoint: '',\r\n      provider: ''\r\n    };\r\n    this.showForm = false;\r\n  }\r\n\r\n  hideApiKey(apiKey: string): string {\r\n    if (!apiKey) return '';\r\n    return apiKey.substring(0, 8) + '...' + apiKey.substring(apiKey.length - 4);\r\n  }\r\n\r\n  generateUniqueId(): string {\r\n    return Date.now().toString(36) + Math.random().toString(36).substring(2);\r\n  }\r\n}\r\n"], "mappings": ";;AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,qBAAqB,QAAQ,iDAAiD;AACvF,SAASC,kBAAkB,QAAQ,sDAAsD;AAQlF,IAAMC,kBAAkB,GAAxB,MAAMA,kBAAkB;EAY7BC,YAAoBC,gBAAuC;IAAvC,KAAAA,gBAAgB,GAAhBA,gBAAgB;IAXpC,KAAAC,MAAM,GAAU,EAAE;IAClB,KAAAC,YAAY,GAAQ;MAClBC,EAAE,EAAE,EAAE;MACNC,OAAO,EAAE,EAAE;MACXC,MAAM,EAAE,EAAE;MACVC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE;KACX;IACD,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,QAAQ,GAAG,KAAK;EAE8C;EAE9DC,QAAQA,CAAA;IACN,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAA,cAAcA,CAAA;IACZ,IAAI,CAACX,gBAAgB,CAACY,MAAM,EAAE,CAACC,SAAS,CAAEC,MAAM,IAAI;MAClD,IAAI,CAACb,MAAM,GAAGa,MAAM;IACtB,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACP,SAAS,GAAG,KAAK;IACtB,IAAI,CAACN,YAAY,GAAG;MAClBE,OAAO,EAAE,EAAE;MACXC,MAAM,EAAE,EAAE;MACVC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE;KACX;IACD,IAAI,CAACE,QAAQ,GAAG,IAAI;EACtB;EAEAO,SAASA,CAACC,KAAU;IAClB,IAAI,CAACT,SAAS,GAAG,IAAI;IACrB,IAAI,CAACN,YAAY,GAAG;MAAE,GAAGe;IAAK,CAAE;IAChC,IAAI,CAACR,QAAQ,GAAG,IAAI;EACtB;EAEAS,SAASA,CAAA;IACP,IAAI,CAAClB,gBAAgB,CAACmB,cAAc,CAAC,IAAI,CAACjB,YAAY,CAAC,CAACW,SAAS,CAAEO,GAAQ,IAAI;MAC7EC,OAAO,CAACC,GAAG,CAACF,GAAG,CAAC;MAEhB,IAAIA,GAAG,EAAE;QACP,IAAI,IAAI,CAACZ,SAAS,EAAE;UAClB,IAAI,CAACP,MAAM,GAAG,IAAI,CAACA,MAAM,CAACsB,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACrB,EAAE,KAAK,IAAI,CAACD,YAAY,CAACC,EAAE,GAAG,IAAI,CAACD,YAAY,GAAGsB,CAAC,CAAC;SAC1F,MAAM;UACL,IAAI,CAACvB,MAAM,CAACwB,IAAI,CAACL,GAAG,CAAC;;QAEvB,IAAI,CAACX,QAAQ,GAAG,KAAK;QACrB,IAAI,CAACiB,SAAS,EAAE;;IAEpB,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAACvB,OAAe;IACzB,IAAIwB,OAAO,CAAC,6CAA6C,CAAC,EAAE;MAC1D,IAAI,CAAC5B,gBAAgB,CAAC6B,MAAM,CAACzB,OAAO,CAAC,CAACS,SAAS,CAAEO,GAAQ,IAAI;QAC3D,IAAIA,GAAG,EAAE;UACP,IAAI,CAACnB,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC6B,MAAM,CAACb,KAAK,IAAIA,KAAK,CAACb,OAAO,KAAKA,OAAO,CAAC;;MAExE,CAAC,CAAC;;EAEN;EAEAsB,SAASA,CAAA;IACP,IAAI,CAACxB,YAAY,GAAG;MAClBC,EAAE,EAAE,EAAE;MACNC,OAAO,EAAE,EAAE;MACXC,MAAM,EAAE,EAAE;MACVC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE;KACX;IACD,IAAI,CAACE,QAAQ,GAAG,KAAK;EACvB;EAEAsB,UAAUA,CAACC,MAAc;IACvB,IAAI,CAACA,MAAM,EAAE,OAAO,EAAE;IACtB,OAAOA,MAAM,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,GAAGD,MAAM,CAACC,SAAS,CAACD,MAAM,CAACE,MAAM,GAAG,CAAC,CAAC;EAC7E;EAEAC,gBAAgBA,CAAA;IACd,OAAOC,IAAI,CAACC,GAAG,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,GAAGC,IAAI,CAACC,MAAM,EAAE,CAACF,QAAQ,CAAC,EAAE,CAAC,CAACL,SAAS,CAAC,CAAC,CAAC;EAC1E;;;;;;;AArFWnC,kBAAkB,GAAA2C,UAAA,EAN9BhD,SAAS,CAAC;EACTiD,QAAQ,EAAE,gBAAgB;EAC1BC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAAClD,YAAY,EAAEC,WAAW,EAAEE,kBAAkB,CAAC;EACxDgD,QAAA,EAAAC;CACD,CAAC,C,EACWhD,kBAAkB,CAsF9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}