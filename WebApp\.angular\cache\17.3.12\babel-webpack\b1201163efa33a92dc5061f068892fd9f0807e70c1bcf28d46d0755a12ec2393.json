{"ast": null, "code": "import { trigger, state, style, transition, animate, query, stagger } from '@angular/animations';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass AnimationDuration {\n  static {\n    this.SLOW = '0.3s';\n  } // Modal\n  static {\n    this.BASE = '0.2s';\n  }\n  static {\n    this.FAST = '0.1s';\n  } // Tooltip\n}\nclass AnimationCurves {\n  static {\n    this.EASE_BASE_OUT = 'cubic-bezier(0.7, 0.3, 0.1, 1)';\n  }\n  static {\n    this.EASE_BASE_IN = 'cubic-bezier(0.9, 0, 0.3, 0.7)';\n  }\n  static {\n    this.EASE_OUT = 'cubic-bezier(0.215, 0.61, 0.355, 1)';\n  }\n  static {\n    this.EASE_IN = 'cubic-bezier(0.55, 0.055, 0.675, 0.19)';\n  }\n  static {\n    this.EASE_IN_OUT = 'cubic-bezier(0.645, 0.045, 0.355, 1)';\n  }\n  static {\n    this.EASE_OUT_BACK = 'cubic-bezier(0.12, 0.4, 0.29, 1.46)';\n  }\n  static {\n    this.EASE_IN_BACK = 'cubic-bezier(0.71, -0.46, 0.88, 0.6)';\n  }\n  static {\n    this.EASE_IN_OUT_BACK = 'cubic-bezier(0.71, -0.46, 0.29, 1.46)';\n  }\n  static {\n    this.EASE_OUT_CIRC = 'cubic-bezier(0.08, 0.82, 0.17, 1)';\n  }\n  static {\n    this.EASE_IN_CIRC = 'cubic-bezier(0.6, 0.04, 0.98, 0.34)';\n  }\n  static {\n    this.EASE_IN_OUT_CIRC = 'cubic-bezier(0.78, 0.14, 0.15, 0.86)';\n  }\n  static {\n    this.EASE_OUT_QUINT = 'cubic-bezier(0.23, 1, 0.32, 1)';\n  }\n  static {\n    this.EASE_IN_QUINT = 'cubic-bezier(0.755, 0.05, 0.855, 0.06)';\n  }\n  static {\n    this.EASE_IN_OUT_QUINT = 'cubic-bezier(0.86, 0, 0.07, 1)';\n  }\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst collapseMotion = trigger('collapseMotion', [state('expanded', style({\n  height: '*'\n})), state('collapsed', style({\n  height: 0,\n  overflow: 'hidden'\n})), state('hidden', style({\n  height: 0,\n  overflow: 'hidden',\n  borderTopWidth: '0'\n})), transition('expanded => collapsed', animate(`150ms ${AnimationCurves.EASE_IN_OUT}`)), transition('expanded => hidden', animate(`150ms ${AnimationCurves.EASE_IN_OUT}`)), transition('collapsed => expanded', animate(`150ms ${AnimationCurves.EASE_IN_OUT}`)), transition('hidden => expanded', animate(`150ms ${AnimationCurves.EASE_IN_OUT}`))]);\nconst treeCollapseMotion = trigger('treeCollapseMotion', [transition('* => *', [query('nz-tree-node:leave,nz-tree-builtin-node:leave', [style({\n  overflow: 'hidden'\n}), stagger(0, [animate(`150ms ${AnimationCurves.EASE_IN_OUT}`, style({\n  height: 0,\n  opacity: 0,\n  'padding-bottom': 0\n}))])], {\n  optional: true\n}), query('nz-tree-node:enter,nz-tree-builtin-node:enter', [style({\n  overflow: 'hidden',\n  height: 0,\n  opacity: 0,\n  'padding-bottom': 0\n}), stagger(0, [animate(`150ms ${AnimationCurves.EASE_IN_OUT}`, style({\n  overflow: 'hidden',\n  height: '*',\n  opacity: '*',\n  'padding-bottom': '*'\n}))])], {\n  optional: true\n})])]);\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst drawerMaskMotion = trigger('drawerMaskMotion', [transition(':enter', [style({\n  opacity: 0\n}), animate(`${AnimationDuration.SLOW}`, style({\n  opacity: 1\n}))]), transition(':leave', [style({\n  opacity: 1\n}), animate(`${AnimationDuration.SLOW}`, style({\n  opacity: 0\n}))])]);\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst fadeMotion = trigger('fadeMotion', [transition('* => enter', [style({\n  opacity: 0\n}), animate(`${AnimationDuration.BASE}`, style({\n  opacity: 1\n}))]), transition('* => leave, :leave', [style({\n  opacity: 1\n}), animate(`${AnimationDuration.BASE}`, style({\n  opacity: 0\n}))])]);\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst helpMotion = trigger('helpMotion', [transition(':enter', [style({\n  opacity: 0,\n  transform: 'translateY(-5px)'\n}), animate(`${AnimationDuration.SLOW} ${AnimationCurves.EASE_IN_OUT}`, style({\n  opacity: 1,\n  transform: 'translateY(0)'\n}))]), transition(':leave', [style({\n  opacity: 1,\n  transform: 'translateY(0)'\n}), animate(`${AnimationDuration.SLOW} ${AnimationCurves.EASE_IN_OUT}`, style({\n  opacity: 0,\n  transform: 'translateY(-5px)'\n}))])]);\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst moveUpMotion = trigger('moveUpMotion', [transition('* => enter', [style({\n  transformOrigin: '0 0',\n  transform: 'translateY(-100%)',\n  opacity: 0\n}), animate(`${AnimationDuration.BASE}`, style({\n  transformOrigin: '0 0',\n  transform: 'translateY(0%)',\n  opacity: 1\n}))]), transition('* => leave', [style({\n  transformOrigin: '0 0',\n  transform: 'translateY(0%)',\n  opacity: 1\n}), animate(`${AnimationDuration.BASE}`, style({\n  transformOrigin: '0 0',\n  transform: 'translateY(-100%)',\n  opacity: 0\n}))])]);\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst notificationMotion = trigger('notificationMotion', [state('enterRight', style({\n  opacity: 1,\n  transform: 'translateX(0)'\n})), transition('* => enterRight', [style({\n  opacity: 0,\n  transform: 'translateX(5%)'\n}), animate('100ms linear')]), state('enterLeft', style({\n  opacity: 1,\n  transform: 'translateX(0)'\n})), transition('* => enterLeft', [style({\n  opacity: 0,\n  transform: 'translateX(-5%)'\n}), animate('100ms linear')]), state('enterTop', style({\n  opacity: 1,\n  transform: 'translateY(0)'\n})), transition('* => enterTop', [style({\n  opacity: 0,\n  transform: 'translateY(-5%)'\n}), animate('100ms linear')]), state('enterBottom', style({\n  opacity: 1,\n  transform: 'translateY(0)'\n})), transition('* => enterBottom', [style({\n  opacity: 0,\n  transform: 'translateY(5%)'\n}), animate('100ms linear')]), state('leave', style({\n  opacity: 0,\n  transform: 'scaleY(0.8)',\n  transformOrigin: '0% 0%'\n})), transition('* => leave', [style({\n  opacity: 1,\n  transform: 'scaleY(1)',\n  transformOrigin: '0% 0%'\n}), animate('100ms linear')])]);\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst ANIMATION_TRANSITION_IN = `${AnimationDuration.BASE} ${AnimationCurves.EASE_OUT_QUINT}`;\nconst ANIMATION_TRANSITION_OUT = `${AnimationDuration.BASE} ${AnimationCurves.EASE_IN_QUINT}`;\nconst slideMotion = trigger('slideMotion', [state('void', style({\n  opacity: 0,\n  transform: 'scaleY(0.8)'\n})), state('enter', style({\n  opacity: 1,\n  transform: 'scaleY(1)'\n})), transition('void => *', [animate(ANIMATION_TRANSITION_IN)]), transition('* => void', [animate(ANIMATION_TRANSITION_OUT)])]);\nconst slideAlertMotion = trigger('slideAlertMotion', [transition(':leave', [style({\n  opacity: 1,\n  transform: 'scaleY(1)',\n  transformOrigin: '0% 0%'\n}), animate(`${AnimationDuration.SLOW} ${AnimationCurves.EASE_IN_OUT_CIRC}`, style({\n  opacity: 0,\n  transform: 'scaleY(0)',\n  transformOrigin: '0% 0%'\n}))])]);\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst tabSwitchMotion = trigger('tabSwitchMotion', [state('leave', style({\n  display: 'none'\n})), transition('* => enter', [style({\n  display: 'block',\n  opacity: 0\n}), animate(AnimationDuration.SLOW)]), transition('* => leave, :leave', [style({\n  position: 'absolute',\n  top: 0,\n  left: 0,\n  width: '100%'\n}), animate(AnimationDuration.SLOW, style({\n  opacity: 0\n})), style({\n  display: 'none'\n})])]);\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst thumbMotion = trigger('thumbMotion', [state('from', style({\n  transform: 'translateX({{ transform }}px)',\n  width: '{{ width }}px'\n}), {\n  params: {\n    transform: 0,\n    width: 0\n  }\n}), state('to', style({\n  transform: 'translateX({{ transform }}px)',\n  width: '{{ width }}px'\n}), {\n  params: {\n    transform: 100,\n    width: 0\n  }\n}), transition('from => to', animate(`300ms ${AnimationCurves.EASE_IN_OUT}`))]);\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst zoomBigMotion = trigger('zoomBigMotion', [transition('void => active', [style({\n  opacity: 0,\n  transform: 'scale(0.8)'\n}), animate(`${AnimationDuration.BASE} ${AnimationCurves.EASE_OUT_CIRC}`, style({\n  opacity: 1,\n  transform: 'scale(1)'\n}))]), transition('active => void', [style({\n  opacity: 1,\n  transform: 'scale(1)'\n}), animate(`${AnimationDuration.BASE} ${AnimationCurves.EASE_IN_OUT_CIRC}`, style({\n  opacity: 0,\n  transform: 'scale(0.8)'\n}))])]);\nconst zoomBadgeMotion = trigger('zoomBadgeMotion', [transition(':enter', [style({\n  opacity: 0,\n  transform: 'scale(0) translate(50%, -50%)'\n}), animate(`${AnimationDuration.SLOW} ${AnimationCurves.EASE_OUT_BACK}`, style({\n  opacity: 1,\n  transform: 'scale(1) translate(50%, -50%)'\n}))]), transition(':leave', [style({\n  opacity: 1,\n  transform: 'scale(1) translate(50%, -50%)'\n}), animate(`${AnimationDuration.SLOW} ${AnimationCurves.EASE_IN_BACK}`, style({\n  opacity: 0,\n  transform: 'scale(0) translate(50%, -50%)'\n}))])]);\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AnimationCurves, AnimationDuration, collapseMotion, drawerMaskMotion, fadeMotion, helpMotion, moveUpMotion, notificationMotion, slideAlertMotion, slideMotion, tabSwitchMotion, thumbMotion, treeCollapseMotion, zoomBadgeMotion, zoomBigMotion };", "map": {"version": 3, "names": ["trigger", "state", "style", "transition", "animate", "query", "stagger", "AnimationDuration", "SLOW", "BASE", "FAST", "AnimationCurves", "EASE_BASE_OUT", "EASE_BASE_IN", "EASE_OUT", "EASE_IN", "EASE_IN_OUT", "EASE_OUT_BACK", "EASE_IN_BACK", "EASE_IN_OUT_BACK", "EASE_OUT_CIRC", "EASE_IN_CIRC", "EASE_IN_OUT_CIRC", "EASE_OUT_QUINT", "EASE_IN_QUINT", "EASE_IN_OUT_QUINT", "collapseMotion", "height", "overflow", "borderTopWidth", "treeCollapseMotion", "opacity", "optional", "drawerMaskMotion", "fadeMotion", "helpMotion", "transform", "moveUpMotion", "transform<PERSON><PERSON>in", "notificationMotion", "ANIMATION_TRANSITION_IN", "ANIMATION_TRANSITION_OUT", "slideMotion", "slideAlertMotion", "tabSwitchMotion", "display", "position", "top", "left", "width", "thumbMotion", "params", "zoomBigMotion", "zoomBadgeMotion"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-core-animation.mjs"], "sourcesContent": ["import { trigger, state, style, transition, animate, query, stagger } from '@angular/animations';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass AnimationDuration {\n    static { this.SLOW = '0.3s'; } // Modal\n    static { this.BASE = '0.2s'; }\n    static { this.FAST = '0.1s'; } // Tooltip\n}\nclass AnimationCurves {\n    static { this.EASE_BASE_OUT = 'cubic-bezier(0.7, 0.3, 0.1, 1)'; }\n    static { this.EASE_BASE_IN = 'cubic-bezier(0.9, 0, 0.3, 0.7)'; }\n    static { this.EASE_OUT = 'cubic-bezier(0.215, 0.61, 0.355, 1)'; }\n    static { this.EASE_IN = 'cubic-bezier(0.55, 0.055, 0.675, 0.19)'; }\n    static { this.EASE_IN_OUT = 'cubic-bezier(0.645, 0.045, 0.355, 1)'; }\n    static { this.EASE_OUT_BACK = 'cubic-bezier(0.12, 0.4, 0.29, 1.46)'; }\n    static { this.EASE_IN_BACK = 'cubic-bezier(0.71, -0.46, 0.88, 0.6)'; }\n    static { this.EASE_IN_OUT_BACK = 'cubic-bezier(0.71, -0.46, 0.29, 1.46)'; }\n    static { this.EASE_OUT_CIRC = 'cubic-bezier(0.08, 0.82, 0.17, 1)'; }\n    static { this.EASE_IN_CIRC = 'cubic-bezier(0.6, 0.04, 0.98, 0.34)'; }\n    static { this.EASE_IN_OUT_CIRC = 'cubic-bezier(0.78, 0.14, 0.15, 0.86)'; }\n    static { this.EASE_OUT_QUINT = 'cubic-bezier(0.23, 1, 0.32, 1)'; }\n    static { this.EASE_IN_QUINT = 'cubic-bezier(0.755, 0.05, 0.855, 0.06)'; }\n    static { this.EASE_IN_OUT_QUINT = 'cubic-bezier(0.86, 0, 0.07, 1)'; }\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst collapseMotion = trigger('collapseMotion', [\n    state('expanded', style({ height: '*' })),\n    state('collapsed', style({ height: 0, overflow: 'hidden' })),\n    state('hidden', style({ height: 0, overflow: 'hidden', borderTopWidth: '0' })),\n    transition('expanded => collapsed', animate(`150ms ${AnimationCurves.EASE_IN_OUT}`)),\n    transition('expanded => hidden', animate(`150ms ${AnimationCurves.EASE_IN_OUT}`)),\n    transition('collapsed => expanded', animate(`150ms ${AnimationCurves.EASE_IN_OUT}`)),\n    transition('hidden => expanded', animate(`150ms ${AnimationCurves.EASE_IN_OUT}`))\n]);\nconst treeCollapseMotion = trigger('treeCollapseMotion', [\n    transition('* => *', [\n        query('nz-tree-node:leave,nz-tree-builtin-node:leave', [\n            style({ overflow: 'hidden' }),\n            stagger(0, [\n                animate(`150ms ${AnimationCurves.EASE_IN_OUT}`, style({ height: 0, opacity: 0, 'padding-bottom': 0 }))\n            ])\n        ], {\n            optional: true\n        }),\n        query('nz-tree-node:enter,nz-tree-builtin-node:enter', [\n            style({ overflow: 'hidden', height: 0, opacity: 0, 'padding-bottom': 0 }),\n            stagger(0, [\n                animate(`150ms ${AnimationCurves.EASE_IN_OUT}`, style({ overflow: 'hidden', height: '*', opacity: '*', 'padding-bottom': '*' }))\n            ])\n        ], {\n            optional: true\n        })\n    ])\n]);\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst drawerMaskMotion = trigger('drawerMaskMotion', [\n    transition(':enter', [style({ opacity: 0 }), animate(`${AnimationDuration.SLOW}`, style({ opacity: 1 }))]),\n    transition(':leave', [style({ opacity: 1 }), animate(`${AnimationDuration.SLOW}`, style({ opacity: 0 }))])\n]);\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst fadeMotion = trigger('fadeMotion', [\n    transition('* => enter', [style({ opacity: 0 }), animate(`${AnimationDuration.BASE}`, style({ opacity: 1 }))]),\n    transition('* => leave, :leave', [style({ opacity: 1 }), animate(`${AnimationDuration.BASE}`, style({ opacity: 0 }))])\n]);\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst helpMotion = trigger('helpMotion', [\n    transition(':enter', [\n        style({\n            opacity: 0,\n            transform: 'translateY(-5px)'\n        }),\n        animate(`${AnimationDuration.SLOW} ${AnimationCurves.EASE_IN_OUT}`, style({\n            opacity: 1,\n            transform: 'translateY(0)'\n        }))\n    ]),\n    transition(':leave', [\n        style({\n            opacity: 1,\n            transform: 'translateY(0)'\n        }),\n        animate(`${AnimationDuration.SLOW} ${AnimationCurves.EASE_IN_OUT}`, style({\n            opacity: 0,\n            transform: 'translateY(-5px)'\n        }))\n    ])\n]);\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst moveUpMotion = trigger('moveUpMotion', [\n    transition('* => enter', [\n        style({\n            transformOrigin: '0 0',\n            transform: 'translateY(-100%)',\n            opacity: 0\n        }),\n        animate(`${AnimationDuration.BASE}`, style({\n            transformOrigin: '0 0',\n            transform: 'translateY(0%)',\n            opacity: 1\n        }))\n    ]),\n    transition('* => leave', [\n        style({\n            transformOrigin: '0 0',\n            transform: 'translateY(0%)',\n            opacity: 1\n        }),\n        animate(`${AnimationDuration.BASE}`, style({\n            transformOrigin: '0 0',\n            transform: 'translateY(-100%)',\n            opacity: 0\n        }))\n    ])\n]);\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst notificationMotion = trigger('notificationMotion', [\n    state('enterRight', style({ opacity: 1, transform: 'translateX(0)' })),\n    transition('* => enterRight', [style({ opacity: 0, transform: 'translateX(5%)' }), animate('100ms linear')]),\n    state('enterLeft', style({ opacity: 1, transform: 'translateX(0)' })),\n    transition('* => enterLeft', [style({ opacity: 0, transform: 'translateX(-5%)' }), animate('100ms linear')]),\n    state('enterTop', style({ opacity: 1, transform: 'translateY(0)' })),\n    transition('* => enterTop', [style({ opacity: 0, transform: 'translateY(-5%)' }), animate('100ms linear')]),\n    state('enterBottom', style({ opacity: 1, transform: 'translateY(0)' })),\n    transition('* => enterBottom', [style({ opacity: 0, transform: 'translateY(5%)' }), animate('100ms linear')]),\n    state('leave', style({\n        opacity: 0,\n        transform: 'scaleY(0.8)',\n        transformOrigin: '0% 0%'\n    })),\n    transition('* => leave', [\n        style({\n            opacity: 1,\n            transform: 'scaleY(1)',\n            transformOrigin: '0% 0%'\n        }),\n        animate('100ms linear')\n    ])\n]);\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst ANIMATION_TRANSITION_IN = `${AnimationDuration.BASE} ${AnimationCurves.EASE_OUT_QUINT}`;\nconst ANIMATION_TRANSITION_OUT = `${AnimationDuration.BASE} ${AnimationCurves.EASE_IN_QUINT}`;\nconst slideMotion = trigger('slideMotion', [\n    state('void', style({\n        opacity: 0,\n        transform: 'scaleY(0.8)'\n    })),\n    state('enter', style({\n        opacity: 1,\n        transform: 'scaleY(1)'\n    })),\n    transition('void => *', [animate(ANIMATION_TRANSITION_IN)]),\n    transition('* => void', [animate(ANIMATION_TRANSITION_OUT)])\n]);\nconst slideAlertMotion = trigger('slideAlertMotion', [\n    transition(':leave', [\n        style({ opacity: 1, transform: 'scaleY(1)', transformOrigin: '0% 0%' }),\n        animate(`${AnimationDuration.SLOW} ${AnimationCurves.EASE_IN_OUT_CIRC}`, style({\n            opacity: 0,\n            transform: 'scaleY(0)',\n            transformOrigin: '0% 0%'\n        }))\n    ])\n]);\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst tabSwitchMotion = trigger('tabSwitchMotion', [\n    state('leave', style({\n        display: 'none'\n    })),\n    transition('* => enter', [\n        style({\n            display: 'block',\n            opacity: 0\n        }),\n        animate(AnimationDuration.SLOW)\n    ]),\n    transition('* => leave, :leave', [\n        style({\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            width: '100%'\n        }),\n        animate(AnimationDuration.SLOW, style({\n            opacity: 0\n        })),\n        style({\n            display: 'none'\n        })\n    ])\n]);\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst thumbMotion = trigger('thumbMotion', [\n    state('from', style({ transform: 'translateX({{ transform }}px)', width: '{{ width }}px' }), {\n        params: { transform: 0, width: 0 }\n    }),\n    state('to', style({ transform: 'translateX({{ transform }}px)', width: '{{ width }}px' }), {\n        params: { transform: 100, width: 0 }\n    }),\n    transition('from => to', animate(`300ms ${AnimationCurves.EASE_IN_OUT}`))\n]);\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst zoomBigMotion = trigger('zoomBigMotion', [\n    transition('void => active', [\n        style({ opacity: 0, transform: 'scale(0.8)' }),\n        animate(`${AnimationDuration.BASE} ${AnimationCurves.EASE_OUT_CIRC}`, style({\n            opacity: 1,\n            transform: 'scale(1)'\n        }))\n    ]),\n    transition('active => void', [\n        style({ opacity: 1, transform: 'scale(1)' }),\n        animate(`${AnimationDuration.BASE} ${AnimationCurves.EASE_IN_OUT_CIRC}`, style({\n            opacity: 0,\n            transform: 'scale(0.8)'\n        }))\n    ])\n]);\nconst zoomBadgeMotion = trigger('zoomBadgeMotion', [\n    transition(':enter', [\n        style({ opacity: 0, transform: 'scale(0) translate(50%, -50%)' }),\n        animate(`${AnimationDuration.SLOW} ${AnimationCurves.EASE_OUT_BACK}`, style({\n            opacity: 1,\n            transform: 'scale(1) translate(50%, -50%)'\n        }))\n    ]),\n    transition(':leave', [\n        style({ opacity: 1, transform: 'scale(1) translate(50%, -50%)' }),\n        animate(`${AnimationDuration.SLOW} ${AnimationCurves.EASE_IN_BACK}`, style({\n            opacity: 0,\n            transform: 'scale(0) translate(50%, -50%)'\n        }))\n    ])\n]);\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AnimationCurves, AnimationDuration, collapseMotion, drawerMaskMotion, fadeMotion, helpMotion, moveUpMotion, notificationMotion, slideAlertMotion, slideMotion, tabSwitchMotion, thumbMotion, treeCollapseMotion, zoomBadgeMotion, zoomBigMotion };\n"], "mappings": "AAAA,SAASA,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,OAAO,EAAEC,KAAK,EAAEC,OAAO,QAAQ,qBAAqB;;AAEhG;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,CAAC;EACpB;IAAS,IAAI,CAACC,IAAI,GAAG,MAAM;EAAE,CAAC,CAAC;EAC/B;IAAS,IAAI,CAACC,IAAI,GAAG,MAAM;EAAE;EAC7B;IAAS,IAAI,CAACC,IAAI,GAAG,MAAM;EAAE,CAAC,CAAC;AACnC;AACA,MAAMC,eAAe,CAAC;EAClB;IAAS,IAAI,CAACC,aAAa,GAAG,gCAAgC;EAAE;EAChE;IAAS,IAAI,CAACC,YAAY,GAAG,gCAAgC;EAAE;EAC/D;IAAS,IAAI,CAACC,QAAQ,GAAG,qCAAqC;EAAE;EAChE;IAAS,IAAI,CAACC,OAAO,GAAG,wCAAwC;EAAE;EAClE;IAAS,IAAI,CAACC,WAAW,GAAG,sCAAsC;EAAE;EACpE;IAAS,IAAI,CAACC,aAAa,GAAG,qCAAqC;EAAE;EACrE;IAAS,IAAI,CAACC,YAAY,GAAG,sCAAsC;EAAE;EACrE;IAAS,IAAI,CAACC,gBAAgB,GAAG,uCAAuC;EAAE;EAC1E;IAAS,IAAI,CAACC,aAAa,GAAG,mCAAmC;EAAE;EACnE;IAAS,IAAI,CAACC,YAAY,GAAG,qCAAqC;EAAE;EACpE;IAAS,IAAI,CAACC,gBAAgB,GAAG,sCAAsC;EAAE;EACzE;IAAS,IAAI,CAACC,cAAc,GAAG,gCAAgC;EAAE;EACjE;IAAS,IAAI,CAACC,aAAa,GAAG,wCAAwC;EAAE;EACxE;IAAS,IAAI,CAACC,iBAAiB,GAAG,gCAAgC;EAAE;AACxE;;AAEA;AACA;AACA;AACA;AACA,MAAMC,cAAc,GAAG1B,OAAO,CAAC,gBAAgB,EAAE,CAC7CC,KAAK,CAAC,UAAU,EAAEC,KAAK,CAAC;EAAEyB,MAAM,EAAE;AAAI,CAAC,CAAC,CAAC,EACzC1B,KAAK,CAAC,WAAW,EAAEC,KAAK,CAAC;EAAEyB,MAAM,EAAE,CAAC;EAAEC,QAAQ,EAAE;AAAS,CAAC,CAAC,CAAC,EAC5D3B,KAAK,CAAC,QAAQ,EAAEC,KAAK,CAAC;EAAEyB,MAAM,EAAE,CAAC;EAAEC,QAAQ,EAAE,QAAQ;EAAEC,cAAc,EAAE;AAAI,CAAC,CAAC,CAAC,EAC9E1B,UAAU,CAAC,uBAAuB,EAAEC,OAAO,CAAE,SAAQO,eAAe,CAACK,WAAY,EAAC,CAAC,CAAC,EACpFb,UAAU,CAAC,oBAAoB,EAAEC,OAAO,CAAE,SAAQO,eAAe,CAACK,WAAY,EAAC,CAAC,CAAC,EACjFb,UAAU,CAAC,uBAAuB,EAAEC,OAAO,CAAE,SAAQO,eAAe,CAACK,WAAY,EAAC,CAAC,CAAC,EACpFb,UAAU,CAAC,oBAAoB,EAAEC,OAAO,CAAE,SAAQO,eAAe,CAACK,WAAY,EAAC,CAAC,CAAC,CACpF,CAAC;AACF,MAAMc,kBAAkB,GAAG9B,OAAO,CAAC,oBAAoB,EAAE,CACrDG,UAAU,CAAC,QAAQ,EAAE,CACjBE,KAAK,CAAC,+CAA+C,EAAE,CACnDH,KAAK,CAAC;EAAE0B,QAAQ,EAAE;AAAS,CAAC,CAAC,EAC7BtB,OAAO,CAAC,CAAC,EAAE,CACPF,OAAO,CAAE,SAAQO,eAAe,CAACK,WAAY,EAAC,EAAEd,KAAK,CAAC;EAAEyB,MAAM,EAAE,CAAC;EAAEI,OAAO,EAAE,CAAC;EAAE,gBAAgB,EAAE;AAAE,CAAC,CAAC,CAAC,CACzG,CAAC,CACL,EAAE;EACCC,QAAQ,EAAE;AACd,CAAC,CAAC,EACF3B,KAAK,CAAC,+CAA+C,EAAE,CACnDH,KAAK,CAAC;EAAE0B,QAAQ,EAAE,QAAQ;EAAED,MAAM,EAAE,CAAC;EAAEI,OAAO,EAAE,CAAC;EAAE,gBAAgB,EAAE;AAAE,CAAC,CAAC,EACzEzB,OAAO,CAAC,CAAC,EAAE,CACPF,OAAO,CAAE,SAAQO,eAAe,CAACK,WAAY,EAAC,EAAEd,KAAK,CAAC;EAAE0B,QAAQ,EAAE,QAAQ;EAAED,MAAM,EAAE,GAAG;EAAEI,OAAO,EAAE,GAAG;EAAE,gBAAgB,EAAE;AAAI,CAAC,CAAC,CAAC,CACnI,CAAC,CACL,EAAE;EACCC,QAAQ,EAAE;AACd,CAAC,CAAC,CACL,CAAC,CACL,CAAC;;AAEF;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,GAAGjC,OAAO,CAAC,kBAAkB,EAAE,CACjDG,UAAU,CAAC,QAAQ,EAAE,CAACD,KAAK,CAAC;EAAE6B,OAAO,EAAE;AAAE,CAAC,CAAC,EAAE3B,OAAO,CAAE,GAAEG,iBAAiB,CAACC,IAAK,EAAC,EAAEN,KAAK,CAAC;EAAE6B,OAAO,EAAE;AAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAC1G5B,UAAU,CAAC,QAAQ,EAAE,CAACD,KAAK,CAAC;EAAE6B,OAAO,EAAE;AAAE,CAAC,CAAC,EAAE3B,OAAO,CAAE,GAAEG,iBAAiB,CAACC,IAAK,EAAC,EAAEN,KAAK,CAAC;EAAE6B,OAAO,EAAE;AAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAC7G,CAAC;;AAEF;AACA;AACA;AACA;AACA,MAAMG,UAAU,GAAGlC,OAAO,CAAC,YAAY,EAAE,CACrCG,UAAU,CAAC,YAAY,EAAE,CAACD,KAAK,CAAC;EAAE6B,OAAO,EAAE;AAAE,CAAC,CAAC,EAAE3B,OAAO,CAAE,GAAEG,iBAAiB,CAACE,IAAK,EAAC,EAAEP,KAAK,CAAC;EAAE6B,OAAO,EAAE;AAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAC9G5B,UAAU,CAAC,oBAAoB,EAAE,CAACD,KAAK,CAAC;EAAE6B,OAAO,EAAE;AAAE,CAAC,CAAC,EAAE3B,OAAO,CAAE,GAAEG,iBAAiB,CAACE,IAAK,EAAC,EAAEP,KAAK,CAAC;EAAE6B,OAAO,EAAE;AAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CACzH,CAAC;;AAEF;AACA;AACA;AACA;AACA,MAAMI,UAAU,GAAGnC,OAAO,CAAC,YAAY,EAAE,CACrCG,UAAU,CAAC,QAAQ,EAAE,CACjBD,KAAK,CAAC;EACF6B,OAAO,EAAE,CAAC;EACVK,SAAS,EAAE;AACf,CAAC,CAAC,EACFhC,OAAO,CAAE,GAAEG,iBAAiB,CAACC,IAAK,IAAGG,eAAe,CAACK,WAAY,EAAC,EAAEd,KAAK,CAAC;EACtE6B,OAAO,EAAE,CAAC;EACVK,SAAS,EAAE;AACf,CAAC,CAAC,CAAC,CACN,CAAC,EACFjC,UAAU,CAAC,QAAQ,EAAE,CACjBD,KAAK,CAAC;EACF6B,OAAO,EAAE,CAAC;EACVK,SAAS,EAAE;AACf,CAAC,CAAC,EACFhC,OAAO,CAAE,GAAEG,iBAAiB,CAACC,IAAK,IAAGG,eAAe,CAACK,WAAY,EAAC,EAAEd,KAAK,CAAC;EACtE6B,OAAO,EAAE,CAAC;EACVK,SAAS,EAAE;AACf,CAAC,CAAC,CAAC,CACN,CAAC,CACL,CAAC;;AAEF;AACA;AACA;AACA;AACA,MAAMC,YAAY,GAAGrC,OAAO,CAAC,cAAc,EAAE,CACzCG,UAAU,CAAC,YAAY,EAAE,CACrBD,KAAK,CAAC;EACFoC,eAAe,EAAE,KAAK;EACtBF,SAAS,EAAE,mBAAmB;EAC9BL,OAAO,EAAE;AACb,CAAC,CAAC,EACF3B,OAAO,CAAE,GAAEG,iBAAiB,CAACE,IAAK,EAAC,EAAEP,KAAK,CAAC;EACvCoC,eAAe,EAAE,KAAK;EACtBF,SAAS,EAAE,gBAAgB;EAC3BL,OAAO,EAAE;AACb,CAAC,CAAC,CAAC,CACN,CAAC,EACF5B,UAAU,CAAC,YAAY,EAAE,CACrBD,KAAK,CAAC;EACFoC,eAAe,EAAE,KAAK;EACtBF,SAAS,EAAE,gBAAgB;EAC3BL,OAAO,EAAE;AACb,CAAC,CAAC,EACF3B,OAAO,CAAE,GAAEG,iBAAiB,CAACE,IAAK,EAAC,EAAEP,KAAK,CAAC;EACvCoC,eAAe,EAAE,KAAK;EACtBF,SAAS,EAAE,mBAAmB;EAC9BL,OAAO,EAAE;AACb,CAAC,CAAC,CAAC,CACN,CAAC,CACL,CAAC;;AAEF;AACA;AACA;AACA;AACA,MAAMQ,kBAAkB,GAAGvC,OAAO,CAAC,oBAAoB,EAAE,CACrDC,KAAK,CAAC,YAAY,EAAEC,KAAK,CAAC;EAAE6B,OAAO,EAAE,CAAC;EAAEK,SAAS,EAAE;AAAgB,CAAC,CAAC,CAAC,EACtEjC,UAAU,CAAC,iBAAiB,EAAE,CAACD,KAAK,CAAC;EAAE6B,OAAO,EAAE,CAAC;EAAEK,SAAS,EAAE;AAAiB,CAAC,CAAC,EAAEhC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,EAC5GH,KAAK,CAAC,WAAW,EAAEC,KAAK,CAAC;EAAE6B,OAAO,EAAE,CAAC;EAAEK,SAAS,EAAE;AAAgB,CAAC,CAAC,CAAC,EACrEjC,UAAU,CAAC,gBAAgB,EAAE,CAACD,KAAK,CAAC;EAAE6B,OAAO,EAAE,CAAC;EAAEK,SAAS,EAAE;AAAkB,CAAC,CAAC,EAAEhC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,EAC5GH,KAAK,CAAC,UAAU,EAAEC,KAAK,CAAC;EAAE6B,OAAO,EAAE,CAAC;EAAEK,SAAS,EAAE;AAAgB,CAAC,CAAC,CAAC,EACpEjC,UAAU,CAAC,eAAe,EAAE,CAACD,KAAK,CAAC;EAAE6B,OAAO,EAAE,CAAC;EAAEK,SAAS,EAAE;AAAkB,CAAC,CAAC,EAAEhC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,EAC3GH,KAAK,CAAC,aAAa,EAAEC,KAAK,CAAC;EAAE6B,OAAO,EAAE,CAAC;EAAEK,SAAS,EAAE;AAAgB,CAAC,CAAC,CAAC,EACvEjC,UAAU,CAAC,kBAAkB,EAAE,CAACD,KAAK,CAAC;EAAE6B,OAAO,EAAE,CAAC;EAAEK,SAAS,EAAE;AAAiB,CAAC,CAAC,EAAEhC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,EAC7GH,KAAK,CAAC,OAAO,EAAEC,KAAK,CAAC;EACjB6B,OAAO,EAAE,CAAC;EACVK,SAAS,EAAE,aAAa;EACxBE,eAAe,EAAE;AACrB,CAAC,CAAC,CAAC,EACHnC,UAAU,CAAC,YAAY,EAAE,CACrBD,KAAK,CAAC;EACF6B,OAAO,EAAE,CAAC;EACVK,SAAS,EAAE,WAAW;EACtBE,eAAe,EAAE;AACrB,CAAC,CAAC,EACFlC,OAAO,CAAC,cAAc,CAAC,CAC1B,CAAC,CACL,CAAC;;AAEF;AACA;AACA;AACA;AACA,MAAMoC,uBAAuB,GAAI,GAAEjC,iBAAiB,CAACE,IAAK,IAAGE,eAAe,CAACY,cAAe,EAAC;AAC7F,MAAMkB,wBAAwB,GAAI,GAAElC,iBAAiB,CAACE,IAAK,IAAGE,eAAe,CAACa,aAAc,EAAC;AAC7F,MAAMkB,WAAW,GAAG1C,OAAO,CAAC,aAAa,EAAE,CACvCC,KAAK,CAAC,MAAM,EAAEC,KAAK,CAAC;EAChB6B,OAAO,EAAE,CAAC;EACVK,SAAS,EAAE;AACf,CAAC,CAAC,CAAC,EACHnC,KAAK,CAAC,OAAO,EAAEC,KAAK,CAAC;EACjB6B,OAAO,EAAE,CAAC;EACVK,SAAS,EAAE;AACf,CAAC,CAAC,CAAC,EACHjC,UAAU,CAAC,WAAW,EAAE,CAACC,OAAO,CAACoC,uBAAuB,CAAC,CAAC,CAAC,EAC3DrC,UAAU,CAAC,WAAW,EAAE,CAACC,OAAO,CAACqC,wBAAwB,CAAC,CAAC,CAAC,CAC/D,CAAC;AACF,MAAME,gBAAgB,GAAG3C,OAAO,CAAC,kBAAkB,EAAE,CACjDG,UAAU,CAAC,QAAQ,EAAE,CACjBD,KAAK,CAAC;EAAE6B,OAAO,EAAE,CAAC;EAAEK,SAAS,EAAE,WAAW;EAAEE,eAAe,EAAE;AAAQ,CAAC,CAAC,EACvElC,OAAO,CAAE,GAAEG,iBAAiB,CAACC,IAAK,IAAGG,eAAe,CAACW,gBAAiB,EAAC,EAAEpB,KAAK,CAAC;EAC3E6B,OAAO,EAAE,CAAC;EACVK,SAAS,EAAE,WAAW;EACtBE,eAAe,EAAE;AACrB,CAAC,CAAC,CAAC,CACN,CAAC,CACL,CAAC;;AAEF;AACA;AACA;AACA;AACA,MAAMM,eAAe,GAAG5C,OAAO,CAAC,iBAAiB,EAAE,CAC/CC,KAAK,CAAC,OAAO,EAAEC,KAAK,CAAC;EACjB2C,OAAO,EAAE;AACb,CAAC,CAAC,CAAC,EACH1C,UAAU,CAAC,YAAY,EAAE,CACrBD,KAAK,CAAC;EACF2C,OAAO,EAAE,OAAO;EAChBd,OAAO,EAAE;AACb,CAAC,CAAC,EACF3B,OAAO,CAACG,iBAAiB,CAACC,IAAI,CAAC,CAClC,CAAC,EACFL,UAAU,CAAC,oBAAoB,EAAE,CAC7BD,KAAK,CAAC;EACF4C,QAAQ,EAAE,UAAU;EACpBC,GAAG,EAAE,CAAC;EACNC,IAAI,EAAE,CAAC;EACPC,KAAK,EAAE;AACX,CAAC,CAAC,EACF7C,OAAO,CAACG,iBAAiB,CAACC,IAAI,EAAEN,KAAK,CAAC;EAClC6B,OAAO,EAAE;AACb,CAAC,CAAC,CAAC,EACH7B,KAAK,CAAC;EACF2C,OAAO,EAAE;AACb,CAAC,CAAC,CACL,CAAC,CACL,CAAC;;AAEF;AACA;AACA;AACA;AACA,MAAMK,WAAW,GAAGlD,OAAO,CAAC,aAAa,EAAE,CACvCC,KAAK,CAAC,MAAM,EAAEC,KAAK,CAAC;EAAEkC,SAAS,EAAE,+BAA+B;EAAEa,KAAK,EAAE;AAAgB,CAAC,CAAC,EAAE;EACzFE,MAAM,EAAE;IAAEf,SAAS,EAAE,CAAC;IAAEa,KAAK,EAAE;EAAE;AACrC,CAAC,CAAC,EACFhD,KAAK,CAAC,IAAI,EAAEC,KAAK,CAAC;EAAEkC,SAAS,EAAE,+BAA+B;EAAEa,KAAK,EAAE;AAAgB,CAAC,CAAC,EAAE;EACvFE,MAAM,EAAE;IAAEf,SAAS,EAAE,GAAG;IAAEa,KAAK,EAAE;EAAE;AACvC,CAAC,CAAC,EACF9C,UAAU,CAAC,YAAY,EAAEC,OAAO,CAAE,SAAQO,eAAe,CAACK,WAAY,EAAC,CAAC,CAAC,CAC5E,CAAC;;AAEF;AACA;AACA;AACA;AACA,MAAMoC,aAAa,GAAGpD,OAAO,CAAC,eAAe,EAAE,CAC3CG,UAAU,CAAC,gBAAgB,EAAE,CACzBD,KAAK,CAAC;EAAE6B,OAAO,EAAE,CAAC;EAAEK,SAAS,EAAE;AAAa,CAAC,CAAC,EAC9ChC,OAAO,CAAE,GAAEG,iBAAiB,CAACE,IAAK,IAAGE,eAAe,CAACS,aAAc,EAAC,EAAElB,KAAK,CAAC;EACxE6B,OAAO,EAAE,CAAC;EACVK,SAAS,EAAE;AACf,CAAC,CAAC,CAAC,CACN,CAAC,EACFjC,UAAU,CAAC,gBAAgB,EAAE,CACzBD,KAAK,CAAC;EAAE6B,OAAO,EAAE,CAAC;EAAEK,SAAS,EAAE;AAAW,CAAC,CAAC,EAC5ChC,OAAO,CAAE,GAAEG,iBAAiB,CAACE,IAAK,IAAGE,eAAe,CAACW,gBAAiB,EAAC,EAAEpB,KAAK,CAAC;EAC3E6B,OAAO,EAAE,CAAC;EACVK,SAAS,EAAE;AACf,CAAC,CAAC,CAAC,CACN,CAAC,CACL,CAAC;AACF,MAAMiB,eAAe,GAAGrD,OAAO,CAAC,iBAAiB,EAAE,CAC/CG,UAAU,CAAC,QAAQ,EAAE,CACjBD,KAAK,CAAC;EAAE6B,OAAO,EAAE,CAAC;EAAEK,SAAS,EAAE;AAAgC,CAAC,CAAC,EACjEhC,OAAO,CAAE,GAAEG,iBAAiB,CAACC,IAAK,IAAGG,eAAe,CAACM,aAAc,EAAC,EAAEf,KAAK,CAAC;EACxE6B,OAAO,EAAE,CAAC;EACVK,SAAS,EAAE;AACf,CAAC,CAAC,CAAC,CACN,CAAC,EACFjC,UAAU,CAAC,QAAQ,EAAE,CACjBD,KAAK,CAAC;EAAE6B,OAAO,EAAE,CAAC;EAAEK,SAAS,EAAE;AAAgC,CAAC,CAAC,EACjEhC,OAAO,CAAE,GAAEG,iBAAiB,CAACC,IAAK,IAAGG,eAAe,CAACO,YAAa,EAAC,EAAEhB,KAAK,CAAC;EACvE6B,OAAO,EAAE,CAAC;EACVK,SAAS,EAAE;AACf,CAAC,CAAC,CAAC,CACN,CAAC,CACL,CAAC;;AAEF;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASzB,eAAe,EAAEJ,iBAAiB,EAAEmB,cAAc,EAAEO,gBAAgB,EAAEC,UAAU,EAAEC,UAAU,EAAEE,YAAY,EAAEE,kBAAkB,EAAEI,gBAAgB,EAAED,WAAW,EAAEE,eAAe,EAAEM,WAAW,EAAEpB,kBAAkB,EAAEuB,eAAe,EAAED,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}