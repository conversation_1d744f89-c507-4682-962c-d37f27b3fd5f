{"ast": null, "code": "import Delta from 'quill-delta';\nimport Module from '../core/module.js';\nimport Quill from '../core/quill.js';\nimport { deleteRange } from './keyboard.js';\nconst INSERT_TYPES = ['insertText', 'insertReplacementText'];\nclass Input extends Module {\n  constructor(quill, options) {\n    super(quill, options);\n    quill.root.addEventListener('beforeinput', event => {\n      this.handleBeforeInput(event);\n    });\n\n    // Gboard with English input on Android triggers `compositionstart` sometimes even\n    // users are not going to type anything.\n    if (!/Android/i.test(navigator.userAgent)) {\n      quill.on(Quill.events.COMPOSITION_BEFORE_START, () => {\n        this.handleCompositionStart();\n      });\n    }\n  }\n  deleteRange(range) {\n    deleteRange({\n      range,\n      quill: this.quill\n    });\n  }\n  replaceText(range) {\n    let text = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';\n    if (range.length === 0) return false;\n    if (text) {\n      // Follow the native behavior that inherits the formats of the first character\n      const formats = this.quill.getFormat(range.index, 1);\n      this.deleteRange(range);\n      this.quill.updateContents(new Delta().retain(range.index).insert(text, formats), Quill.sources.USER);\n    } else {\n      this.deleteRange(range);\n    }\n    this.quill.setSelection(range.index + text.length, 0, Quill.sources.SILENT);\n    return true;\n  }\n  handleBeforeInput(event) {\n    if (this.quill.composition.isComposing || event.defaultPrevented || !INSERT_TYPES.includes(event.inputType)) {\n      return;\n    }\n    const staticRange = event.getTargetRanges ? event.getTargetRanges()[0] : null;\n    if (!staticRange || staticRange.collapsed === true) {\n      return;\n    }\n    const text = getPlainTextFromInputEvent(event);\n    if (text == null) {\n      return;\n    }\n    const normalized = this.quill.selection.normalizeNative(staticRange);\n    const range = normalized ? this.quill.selection.normalizedToRange(normalized) : null;\n    if (range && this.replaceText(range, text)) {\n      event.preventDefault();\n    }\n  }\n  handleCompositionStart() {\n    const range = this.quill.getSelection();\n    if (range) {\n      this.replaceText(range);\n    }\n  }\n}\nfunction getPlainTextFromInputEvent(event) {\n  // When `inputType` is \"insertText\":\n  // - `event.data` should be string (Safari uses `event.dataTransfer`).\n  // - `event.dataTransfer` should be null.\n  // When `inputType` is \"insertReplacementText\":\n  // - `event.data` should be null.\n  // - `event.dataTransfer` should contain \"text/plain\" data.\n\n  if (typeof event.data === 'string') {\n    return event.data;\n  }\n  if (event.dataTransfer?.types.includes('text/plain')) {\n    return event.dataTransfer.getData('text/plain');\n  }\n  return null;\n}\nexport default Input;", "map": {"version": 3, "names": ["Delta", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "deleteRange", "INSERT_TYPES", "Input", "constructor", "quill", "options", "root", "addEventListener", "event", "handleBeforeInput", "test", "navigator", "userAgent", "on", "events", "COMPOSITION_BEFORE_START", "handleCompositionStart", "range", "replaceText", "text", "arguments", "length", "undefined", "formats", "getFormat", "index", "updateContents", "retain", "insert", "sources", "USER", "setSelection", "SILENT", "composition", "isComposing", "defaultPrevented", "includes", "inputType", "staticRange", "getTargetRanges", "collapsed", "getPlainTextFromInputEvent", "normalized", "selection", "normalizeNative", "normalizedToRange", "preventDefault", "getSelection", "data", "dataTransfer", "types", "getData"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/quill/modules/input.js"], "sourcesContent": ["import Delta from 'quill-delta';\nimport Module from '../core/module.js';\nimport Quill from '../core/quill.js';\nimport { deleteRange } from './keyboard.js';\nconst INSERT_TYPES = ['insertText', 'insertReplacementText'];\nclass Input extends Module {\n  constructor(quill, options) {\n    super(quill, options);\n    quill.root.addEventListener('beforeinput', event => {\n      this.handleBeforeInput(event);\n    });\n\n    // Gboard with English input on Android triggers `compositionstart` sometimes even\n    // users are not going to type anything.\n    if (!/Android/i.test(navigator.userAgent)) {\n      quill.on(Quill.events.COMPOSITION_BEFORE_START, () => {\n        this.handleCompositionStart();\n      });\n    }\n  }\n  deleteRange(range) {\n    deleteRange({\n      range,\n      quill: this.quill\n    });\n  }\n  replaceText(range) {\n    let text = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';\n    if (range.length === 0) return false;\n    if (text) {\n      // Follow the native behavior that inherits the formats of the first character\n      const formats = this.quill.getFormat(range.index, 1);\n      this.deleteRange(range);\n      this.quill.updateContents(new Delta().retain(range.index).insert(text, formats), Quill.sources.USER);\n    } else {\n      this.deleteRange(range);\n    }\n    this.quill.setSelection(range.index + text.length, 0, Quill.sources.SILENT);\n    return true;\n  }\n  handleBeforeInput(event) {\n    if (this.quill.composition.isComposing || event.defaultPrevented || !INSERT_TYPES.includes(event.inputType)) {\n      return;\n    }\n    const staticRange = event.getTargetRanges ? event.getTargetRanges()[0] : null;\n    if (!staticRange || staticRange.collapsed === true) {\n      return;\n    }\n    const text = getPlainTextFromInputEvent(event);\n    if (text == null) {\n      return;\n    }\n    const normalized = this.quill.selection.normalizeNative(staticRange);\n    const range = normalized ? this.quill.selection.normalizedToRange(normalized) : null;\n    if (range && this.replaceText(range, text)) {\n      event.preventDefault();\n    }\n  }\n  handleCompositionStart() {\n    const range = this.quill.getSelection();\n    if (range) {\n      this.replaceText(range);\n    }\n  }\n}\nfunction getPlainTextFromInputEvent(event) {\n  // When `inputType` is \"insertText\":\n  // - `event.data` should be string (Safari uses `event.dataTransfer`).\n  // - `event.dataTransfer` should be null.\n  // When `inputType` is \"insertReplacementText\":\n  // - `event.data` should be null.\n  // - `event.dataTransfer` should contain \"text/plain\" data.\n\n  if (typeof event.data === 'string') {\n    return event.data;\n  }\n  if (event.dataTransfer?.types.includes('text/plain')) {\n    return event.dataTransfer.getData('text/plain');\n  }\n  return null;\n}\nexport default Input;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,aAAa;AAC/B,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,KAAK,MAAM,kBAAkB;AACpC,SAASC,WAAW,QAAQ,eAAe;AAC3C,MAAMC,YAAY,GAAG,CAAC,YAAY,EAAE,uBAAuB,CAAC;AAC5D,MAAMC,KAAK,SAASJ,MAAM,CAAC;EACzBK,WAAWA,CAACC,KAAK,EAAEC,OAAO,EAAE;IAC1B,KAAK,CAACD,KAAK,EAAEC,OAAO,CAAC;IACrBD,KAAK,CAACE,IAAI,CAACC,gBAAgB,CAAC,aAAa,EAAEC,KAAK,IAAI;MAClD,IAAI,CAACC,iBAAiB,CAACD,KAAK,CAAC;IAC/B,CAAC,CAAC;;IAEF;IACA;IACA,IAAI,CAAC,UAAU,CAACE,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC,EAAE;MACzCR,KAAK,CAACS,EAAE,CAACd,KAAK,CAACe,MAAM,CAACC,wBAAwB,EAAE,MAAM;QACpD,IAAI,CAACC,sBAAsB,CAAC,CAAC;MAC/B,CAAC,CAAC;IACJ;EACF;EACAhB,WAAWA,CAACiB,KAAK,EAAE;IACjBjB,WAAW,CAAC;MACViB,KAAK;MACLb,KAAK,EAAE,IAAI,CAACA;IACd,CAAC,CAAC;EACJ;EACAc,WAAWA,CAACD,KAAK,EAAE;IACjB,IAAIE,IAAI,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;IACjF,IAAIH,KAAK,CAACI,MAAM,KAAK,CAAC,EAAE,OAAO,KAAK;IACpC,IAAIF,IAAI,EAAE;MACR;MACA,MAAMI,OAAO,GAAG,IAAI,CAACnB,KAAK,CAACoB,SAAS,CAACP,KAAK,CAACQ,KAAK,EAAE,CAAC,CAAC;MACpD,IAAI,CAACzB,WAAW,CAACiB,KAAK,CAAC;MACvB,IAAI,CAACb,KAAK,CAACsB,cAAc,CAAC,IAAI7B,KAAK,CAAC,CAAC,CAAC8B,MAAM,CAACV,KAAK,CAACQ,KAAK,CAAC,CAACG,MAAM,CAACT,IAAI,EAAEI,OAAO,CAAC,EAAExB,KAAK,CAAC8B,OAAO,CAACC,IAAI,CAAC;IACtG,CAAC,MAAM;MACL,IAAI,CAAC9B,WAAW,CAACiB,KAAK,CAAC;IACzB;IACA,IAAI,CAACb,KAAK,CAAC2B,YAAY,CAACd,KAAK,CAACQ,KAAK,GAAGN,IAAI,CAACE,MAAM,EAAE,CAAC,EAAEtB,KAAK,CAAC8B,OAAO,CAACG,MAAM,CAAC;IAC3E,OAAO,IAAI;EACb;EACAvB,iBAAiBA,CAACD,KAAK,EAAE;IACvB,IAAI,IAAI,CAACJ,KAAK,CAAC6B,WAAW,CAACC,WAAW,IAAI1B,KAAK,CAAC2B,gBAAgB,IAAI,CAAClC,YAAY,CAACmC,QAAQ,CAAC5B,KAAK,CAAC6B,SAAS,CAAC,EAAE;MAC3G;IACF;IACA,MAAMC,WAAW,GAAG9B,KAAK,CAAC+B,eAAe,GAAG/B,KAAK,CAAC+B,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;IAC7E,IAAI,CAACD,WAAW,IAAIA,WAAW,CAACE,SAAS,KAAK,IAAI,EAAE;MAClD;IACF;IACA,MAAMrB,IAAI,GAAGsB,0BAA0B,CAACjC,KAAK,CAAC;IAC9C,IAAIW,IAAI,IAAI,IAAI,EAAE;MAChB;IACF;IACA,MAAMuB,UAAU,GAAG,IAAI,CAACtC,KAAK,CAACuC,SAAS,CAACC,eAAe,CAACN,WAAW,CAAC;IACpE,MAAMrB,KAAK,GAAGyB,UAAU,GAAG,IAAI,CAACtC,KAAK,CAACuC,SAAS,CAACE,iBAAiB,CAACH,UAAU,CAAC,GAAG,IAAI;IACpF,IAAIzB,KAAK,IAAI,IAAI,CAACC,WAAW,CAACD,KAAK,EAAEE,IAAI,CAAC,EAAE;MAC1CX,KAAK,CAACsC,cAAc,CAAC,CAAC;IACxB;EACF;EACA9B,sBAAsBA,CAAA,EAAG;IACvB,MAAMC,KAAK,GAAG,IAAI,CAACb,KAAK,CAAC2C,YAAY,CAAC,CAAC;IACvC,IAAI9B,KAAK,EAAE;MACT,IAAI,CAACC,WAAW,CAACD,KAAK,CAAC;IACzB;EACF;AACF;AACA,SAASwB,0BAA0BA,CAACjC,KAAK,EAAE;EACzC;EACA;EACA;EACA;EACA;EACA;;EAEA,IAAI,OAAOA,KAAK,CAACwC,IAAI,KAAK,QAAQ,EAAE;IAClC,OAAOxC,KAAK,CAACwC,IAAI;EACnB;EACA,IAAIxC,KAAK,CAACyC,YAAY,EAAEC,KAAK,CAACd,QAAQ,CAAC,YAAY,CAAC,EAAE;IACpD,OAAO5B,KAAK,CAACyC,YAAY,CAACE,OAAO,CAAC,YAAY,CAAC;EACjD;EACA,OAAO,IAAI;AACb;AACA,eAAejD,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}