{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./agent-sidebar.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./agent-sidebar.component.css?ngResource\";\nimport { Component, Input, Output, EventEmitter, inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RemoveProviderPrefixPipe } from '../../../../shared/pipes/remove-provider-prefix.pipe';\nimport { ThemeService } from '../../../../shared/services/theam.service';\nlet AgentSidebarComponent = class AgentSidebarComponent {\n  constructor() {\n    // Inject ThemeService\n    this.themeService = inject(ThemeService);\n    this.isAgentSidebarOpen = false;\n    this.agentSidebarTitle = 'Available Agents';\n    this.workspaceAgents = [];\n    this.onClose = new EventEmitter();\n    this.onSelectAgent = new EventEmitter();\n  }\n  /**\n   * Getter for showSidebar to match source-references component property name\n   * This allows for consistent template usage\n   */\n  get showSidebar() {\n    return this.isAgentSidebarOpen;\n  }\n  /**\n   * Handles the sidebar close event\n   */\n  closeSidebar() {\n    this.onClose.emit();\n  }\n  /**\n   * Alternative method name to match source-references component\n   */\n  onCloseSidebar() {\n    this.closeSidebar();\n  }\n  /**\n   * Handles agent selection\n   * @param agent The selected agent\n   */\n  selectAgent(agent) {\n    if (!agent) return;\n    this.onSelectAgent.emit(agent);\n  }\n  static {\n    this.propDecorators = {\n      isAgentSidebarOpen: [{\n        type: Input\n      }],\n      agentSidebarTitle: [{\n        type: Input\n      }],\n      workspaceAgents: [{\n        type: Input\n      }],\n      onClose: [{\n        type: Output\n      }],\n      onSelectAgent: [{\n        type: Output\n      }]\n    };\n  }\n};\nAgentSidebarComponent = __decorate([Component({\n  selector: 'app-agent-sidebar',\n  standalone: true,\n  imports: [CommonModule, RemoveProviderPrefixPipe],\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], AgentSidebarComponent);\nexport { AgentSidebarComponent };", "map": {"version": 3, "names": ["Component", "Input", "Output", "EventEmitter", "inject", "CommonModule", "RemoveProviderPrefixPipe", "ThemeService", "AgentSidebarComponent", "constructor", "themeService", "isAgentSidebarOpen", "agentSidebarTitle", "workspaceAgents", "onClose", "onSelectAgent", "showSidebar", "closeSidebar", "emit", "onCloseSidebar", "selectAgent", "agent", "__decorate", "selector", "standalone", "imports", "template", "__NG_CLI_RESOURCE__0"], "sources": ["C:\\Users\\<USER>\\source\\ai-hub\\WebApp\\src\\app\\components\\@rightSideComponents\\agent-sidebar\\agent-sidebar.component.ts"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RemoveProviderPrefixPipe } from '../../../../shared/pipes/remove-provider-prefix.pipe';\nimport { ThemeService } from '../../../../shared/services/theam.service';\n\n@Component({\n  selector: 'app-agent-sidebar',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RemoveProviderPrefixPipe\n  ],\n  templateUrl: './agent-sidebar.component.html',\n  styleUrls: ['./agent-sidebar.component.css']\n})\nexport class AgentSidebarComponent {\n  // Inject ThemeService\n  themeService = inject(ThemeService);\n\n  // Input properties\n  @Input() isAgentSidebarOpen: boolean = false;\n  @Input() agentSidebarTitle: string = 'Available Agents';\n  @Input() workspaceAgents: any[] = [];\n\n  // Output events\n  @Output() onClose = new EventEmitter<void>();\n  @Output() onSelectAgent = new EventEmitter<any>();\n\n  /**\n   * Getter for showSidebar to match source-references component property name\n   * This allows for consistent template usage\n   */\n  get showSidebar(): boolean {\n    return this.isAgentSidebarOpen;\n  }\n\n  /**\n   * Handles the sidebar close event\n   */\n  closeSidebar(): void {\n    this.onClose.emit();\n  }\n\n  /**\n   * Alternative method name to match source-references component\n   */\n  onCloseSidebar(): void {\n    this.closeSidebar();\n  }\n\n  /**\n   * Handles agent selection\n   * @param agent The selected agent\n   */\n  selectAgent(agent: any): void {\n    if (!agent) return;\n    this.onSelectAgent.emit(agent);\n  }\n}\n"], "mappings": ";;;AAAA,SAASA,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,YAAY,EAAEC,MAAM,QAAQ,eAAe;AAC9E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,wBAAwB,QAAQ,sDAAsD;AAC/F,SAASC,YAAY,QAAQ,2CAA2C;AAYjE,IAAMC,qBAAqB,GAA3B,MAAMA,qBAAqB;EAA3BC,YAAA;IACL;IACA,KAAAC,YAAY,GAAGN,MAAM,CAACG,YAAY,CAAC;IAG1B,KAAAI,kBAAkB,GAAY,KAAK;IACnC,KAAAC,iBAAiB,GAAW,kBAAkB;IAC9C,KAAAC,eAAe,GAAU,EAAE;IAG1B,KAAAC,OAAO,GAAG,IAAIX,YAAY,EAAQ;IAClC,KAAAY,aAAa,GAAG,IAAIZ,YAAY,EAAO;EAgCnD;EA9BE;;;;EAIA,IAAIa,WAAWA,CAAA;IACb,OAAO,IAAI,CAACL,kBAAkB;EAChC;EAEA;;;EAGAM,YAAYA,CAAA;IACV,IAAI,CAACH,OAAO,CAACI,IAAI,EAAE;EACrB;EAEA;;;EAGAC,cAAcA,CAAA;IACZ,IAAI,CAACF,YAAY,EAAE;EACrB;EAEA;;;;EAIAG,WAAWA,CAACC,KAAU;IACpB,IAAI,CAACA,KAAK,EAAE;IACZ,IAAI,CAACN,aAAa,CAACG,IAAI,CAACG,KAAK,CAAC;EAChC;;;;cArCCpB;MAAK;;cACLA;MAAK;;cACLA;MAAK;;cAGLC;MAAM;;cACNA;MAAM;;;;AAXIM,qBAAqB,GAAAc,UAAA,EAVjCtB,SAAS,CAAC;EACTuB,QAAQ,EAAE,mBAAmB;EAC7BC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPpB,YAAY,EACZC,wBAAwB,CACzB;EACDoB,QAAA,EAAAC,oBAA6C;;CAE9C,CAAC,C,EACWnB,qBAAqB,CA2CjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}