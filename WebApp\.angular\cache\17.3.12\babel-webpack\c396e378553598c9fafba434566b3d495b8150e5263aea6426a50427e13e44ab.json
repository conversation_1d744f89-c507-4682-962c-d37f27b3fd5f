{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./view-workspace.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./view-workspace.component.css?ngResource\";\nimport { CommonModule } from '@angular/common';\nimport { Component, ViewChild } from '@angular/core';\nimport { Router, RouterLink, RouterLinkActive, RouterOutlet } from '@angular/router';\nimport { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';\nimport { AuthService } from '../../../shared/services/auth.service';\nlet ViewWorkspaceComponent = class ViewWorkspaceComponent {\n  constructor(router, authService) {\n    this.router = router;\n    this.authService = authService;\n    this.showScrollButton = false;\n    this.isSidebarOpen = true;\n    this.workspaceName = this.router.url.split('/')[2];\n    this.tabName = this.router.url.split('/')[3];\n    // Initialize the scroll handler\n    this.scrollHandler = () => {\n      const scrollableContent = this.mainContent?.nativeElement.querySelector('.overflow-auto');\n      if (scrollableContent) {\n        this.showScrollButton = scrollableContent.scrollTop > 300;\n      }\n    };\n  }\n  toggleSidebar() {\n    this.isSidebarOpen = !this.isSidebarOpen;\n  }\n  ngOnInit() {\n    //Called after the constructor, initializing input properties, and the first call to ngOnChanges.\n    //Add 'implements OnInit' to the class.\n    this.workspaceName.replace(/[^a-zA-Z0-9 ]/g, '');\n    console.log(this.workspaceName);\n  }\n  get isAdmin() {\n    return this.authService.isAdmin();\n  }\n  removeSpecialChars(str) {\n    return decodeURIComponent(str).replace(/[^a-zA-Z0-9 ]/g, '');\n  }\n  ngDoCheck() {\n    //Called every time that the input properties of a component or a directive are checked. Use it to extend change detection by performing a custom check.\n    //Add 'implements DoCheck' to the class.\n    this.tabName = this.router.url.split('/')[3];\n  }\n  ngAfterViewInit() {\n    setTimeout(() => {\n      const scrollableContent = this.mainContent?.nativeElement.querySelector('.overflow-auto');\n      if (scrollableContent) {\n        // Add scroll event listener\n        scrollableContent.addEventListener('scroll', this.scrollHandler);\n        // Initial check for scroll position\n        this.scrollHandler();\n      }\n    });\n  }\n  ngOnDestroy() {\n    const scrollableContent = this.mainContent?.nativeElement.querySelector('.overflow-auto');\n    if (scrollableContent) {\n      scrollableContent.removeEventListener('scroll', this.scrollHandler);\n    }\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: Router\n    }, {\n      type: AuthService\n    }];\n  }\n  static {\n    this.propDecorators = {\n      mainContent: [{\n        type: ViewChild,\n        args: ['mainContent']\n      }]\n    };\n  }\n};\nViewWorkspaceComponent = __decorate([Component({\n  selector: 'app-view-workspace',\n  standalone: true,\n  imports: [CommonModule, NzBreadCrumbModule, RouterLink, RouterOutlet, RouterLinkActive],\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], ViewWorkspaceComponent);\nexport { ViewWorkspaceComponent };", "map": {"version": 3, "names": ["CommonModule", "Component", "ViewChild", "Router", "RouterLink", "RouterLinkActive", "RouterOutlet", "NzBreadCrumbModule", "AuthService", "ViewWorkspaceComponent", "constructor", "router", "authService", "showScrollButton", "isSidebarOpen", "workspaceName", "url", "split", "tabName", "<PERSON><PERSON><PERSON><PERSON>", "scrollableContent", "mainContent", "nativeElement", "querySelector", "scrollTop", "toggleSidebar", "ngOnInit", "replace", "console", "log", "isAdmin", "removeSpecialChars", "str", "decodeURIComponent", "ngDoCheck", "ngAfterViewInit", "setTimeout", "addEventListener", "ngOnDestroy", "removeEventListener", "args", "__decorate", "selector", "standalone", "imports", "template", "__NG_CLI_RESOURCE__0"], "sources": ["C:\\Users\\<USER>\\source\\ai-hub\\WebApp\\src\\app\\workspaces\\view-workspace\\view-workspace.component.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\r\nimport { Component, ViewChild, ElementRef, AfterViewInit, OnDestroy } from '@angular/core';\r\nimport {\r\n  Router,\r\n  RouterLink,\r\n  RouterLinkActive,\r\n  RouterOutlet,\r\n} from '@angular/router';\r\nimport { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';\r\nimport { ThemeToggleComponent } from '../../components/theme-toogle.component';\r\nimport { AuthService } from '../../../shared/services/auth.service';\r\n\r\n@Component({\r\n  selector: 'app-view-workspace',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    NzBreadCrumbModule,\r\n    RouterLink,\r\n    RouterOutlet,\r\n    RouterLinkActive,\r\n  ],\r\n  templateUrl: './view-workspace.component.html',\r\n  styleUrl: './view-workspace.component.css',\r\n})\r\nexport class ViewWorkspaceComponent implements AfterViewInit, OnDestroy {\r\n  @ViewChild('mainContent') mainContent!: ElementRef;\r\n  showScrollButton = false;\r\n  isSidebarOpen = true;\r\n  workspaceName = this.router.url.split('/')[2];\r\n  tabName = this.router.url.split('/')[3];\r\n  private scrollHandler: () => void;\r\n\r\n  constructor(private router: Router, public authService: AuthService) {\r\n    // Initialize the scroll handler\r\n    this.scrollHandler = () => {\r\n      const scrollableContent = this.mainContent?.nativeElement.querySelector('.overflow-auto');\r\n      if (scrollableContent) {\r\n        this.showScrollButton = scrollableContent.scrollTop > 300;\r\n      }\r\n    };\r\n  }\r\n\r\n  toggleSidebar() {\r\n    this.isSidebarOpen = !this.isSidebarOpen;\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    //Called after the constructor, initializing input properties, and the first call to ngOnChanges.\r\n    //Add 'implements OnInit' to the class.\r\n    this.workspaceName.replace(/[^a-zA-Z0-9 ]/g, '');\r\n    console.log(this.workspaceName);\r\n  }\r\n\r\n  get isAdmin() {\r\n    return this.authService.isAdmin();\r\n  }\r\n\r\n  removeSpecialChars(str: string) {\r\n    return decodeURIComponent(str).replace(/[^a-zA-Z0-9 ]/g, '')\r\n  }\r\n\r\n  ngDoCheck(): void {\r\n    //Called every time that the input properties of a component or a directive are checked. Use it to extend change detection by performing a custom check.\r\n    //Add 'implements DoCheck' to the class.\r\n    this.tabName = this.router.url.split('/')[3];\r\n  }\r\n\r\n  ngAfterViewInit() {\r\n    setTimeout(() => {\r\n      const scrollableContent = this.mainContent?.nativeElement.querySelector('.overflow-auto');\r\n      if (scrollableContent) {\r\n        // Add scroll event listener\r\n        scrollableContent.addEventListener('scroll', this.scrollHandler);\r\n\r\n        // Initial check for scroll position\r\n        this.scrollHandler();\r\n      }\r\n    });\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    const scrollableContent = this.mainContent?.nativeElement.querySelector('.overflow-auto');\r\n    if (scrollableContent) {\r\n      scrollableContent.removeEventListener('scroll', this.scrollHandler);\r\n    }\r\n  }\r\n\r\n\r\n}\r\n"], "mappings": ";;;AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,SAAS,EAAEC,SAAS,QAA8C,eAAe;AAC1F,SACEC,MAAM,EACNC,UAAU,EACVC,gBAAgB,EAChBC,YAAY,QACP,iBAAiB;AACxB,SAASC,kBAAkB,QAAQ,0BAA0B;AAE7D,SAASC,WAAW,QAAQ,uCAAuC;AAe5D,IAAMC,sBAAsB,GAA5B,MAAMA,sBAAsB;EAQjCC,YAAoBC,MAAc,EAASC,WAAwB;IAA/C,KAAAD,MAAM,GAANA,MAAM;IAAiB,KAAAC,WAAW,GAAXA,WAAW;IANtD,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,aAAa,GAAG,IAAI;IACpB,KAAAC,aAAa,GAAG,IAAI,CAACJ,MAAM,CAACK,GAAG,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC7C,KAAAC,OAAO,GAAG,IAAI,CAACP,MAAM,CAACK,GAAG,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAIrC;IACA,IAAI,CAACE,aAAa,GAAG,MAAK;MACxB,MAAMC,iBAAiB,GAAG,IAAI,CAACC,WAAW,EAAEC,aAAa,CAACC,aAAa,CAAC,gBAAgB,CAAC;MACzF,IAAIH,iBAAiB,EAAE;QACrB,IAAI,CAACP,gBAAgB,GAAGO,iBAAiB,CAACI,SAAS,GAAG,GAAG;;IAE7D,CAAC;EACH;EAEAC,aAAaA,CAAA;IACX,IAAI,CAACX,aAAa,GAAG,CAAC,IAAI,CAACA,aAAa;EAC1C;EAEAY,QAAQA,CAAA;IACN;IACA;IACA,IAAI,CAACX,aAAa,CAACY,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC;IAChDC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACd,aAAa,CAAC;EACjC;EAEA,IAAIe,OAAOA,CAAA;IACT,OAAO,IAAI,CAAClB,WAAW,CAACkB,OAAO,EAAE;EACnC;EAEAC,kBAAkBA,CAACC,GAAW;IAC5B,OAAOC,kBAAkB,CAACD,GAAG,CAAC,CAACL,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC;EAC9D;EAEAO,SAASA,CAAA;IACP;IACA;IACA,IAAI,CAAChB,OAAO,GAAG,IAAI,CAACP,MAAM,CAACK,GAAG,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC9C;EAEAkB,eAAeA,CAAA;IACbC,UAAU,CAAC,MAAK;MACd,MAAMhB,iBAAiB,GAAG,IAAI,CAACC,WAAW,EAAEC,aAAa,CAACC,aAAa,CAAC,gBAAgB,CAAC;MACzF,IAAIH,iBAAiB,EAAE;QACrB;QACAA,iBAAiB,CAACiB,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAClB,aAAa,CAAC;QAEhE;QACA,IAAI,CAACA,aAAa,EAAE;;IAExB,CAAC,CAAC;EACJ;EAEAmB,WAAWA,CAAA;IACT,MAAMlB,iBAAiB,GAAG,IAAI,CAACC,WAAW,EAAEC,aAAa,CAACC,aAAa,CAAC,gBAAgB,CAAC;IACzF,IAAIH,iBAAiB,EAAE;MACrBA,iBAAiB,CAACmB,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACpB,aAAa,CAAC;;EAEvE;;;;;;;;;;;cA5DCjB,SAAS;QAAAsC,IAAA,GAAC,aAAa;MAAA;;;;AADb/B,sBAAsB,GAAAgC,UAAA,EAblCxC,SAAS,CAAC;EACTyC,QAAQ,EAAE,oBAAoB;EAC9BC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACP5C,YAAY,EACZO,kBAAkB,EAClBH,UAAU,EACVE,YAAY,EACZD,gBAAgB,CACjB;EACDwC,QAAA,EAAAC,oBAA8C;;CAE/C,CAAC,C,EACWrC,sBAAsB,CAgElC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}