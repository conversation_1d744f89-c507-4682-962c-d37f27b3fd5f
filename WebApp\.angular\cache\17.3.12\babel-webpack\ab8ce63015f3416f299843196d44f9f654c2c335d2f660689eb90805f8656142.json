{"ast": null, "code": "import Inline from '../blots/inline.js';\nclass Link extends Inline {\n  static blotName = 'link';\n  static tagName = 'A';\n  static SANITIZED_URL = 'about:blank';\n  static PROTOCOL_WHITELIST = ['http', 'https', 'mailto', 'tel', 'sms'];\n  static create(value) {\n    const node = super.create(value);\n    node.setAttribute('href', this.sanitize(value));\n    node.setAttribute('rel', 'noopener noreferrer');\n    node.setAttribute('target', '_blank');\n    return node;\n  }\n  static formats(domNode) {\n    return domNode.getAttribute('href');\n  }\n  static sanitize(url) {\n    return sanitize(url, this.PROTOCOL_WHITELIST) ? url : this.SANITIZED_URL;\n  }\n  format(name, value) {\n    if (name !== this.statics.blotName || !value) {\n      super.format(name, value);\n    } else {\n      // @ts-expect-error\n      this.domNode.setAttribute('href', this.constructor.sanitize(value));\n    }\n  }\n}\nfunction sanitize(url, protocols) {\n  const anchor = document.createElement('a');\n  anchor.href = url;\n  const protocol = anchor.href.slice(0, anchor.href.indexOf(':'));\n  return protocols.indexOf(protocol) > -1;\n}\nexport { Link as default, sanitize };", "map": {"version": 3, "names": ["Inline", "Link", "blotName", "tagName", "SANITIZED_URL", "PROTOCOL_WHITELIST", "create", "value", "node", "setAttribute", "sanitize", "formats", "domNode", "getAttribute", "url", "format", "name", "statics", "constructor", "protocols", "anchor", "document", "createElement", "href", "protocol", "slice", "indexOf", "default"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/quill/formats/link.js"], "sourcesContent": ["import Inline from '../blots/inline.js';\nclass Link extends Inline {\n  static blotName = 'link';\n  static tagName = 'A';\n  static SANITIZED_URL = 'about:blank';\n  static PROTOCOL_WHITELIST = ['http', 'https', 'mailto', 'tel', 'sms'];\n  static create(value) {\n    const node = super.create(value);\n    node.setAttribute('href', this.sanitize(value));\n    node.setAttribute('rel', 'noopener noreferrer');\n    node.setAttribute('target', '_blank');\n    return node;\n  }\n  static formats(domNode) {\n    return domNode.getAttribute('href');\n  }\n  static sanitize(url) {\n    return sanitize(url, this.PROTOCOL_WHITELIST) ? url : this.SANITIZED_URL;\n  }\n  format(name, value) {\n    if (name !== this.statics.blotName || !value) {\n      super.format(name, value);\n    } else {\n      // @ts-expect-error\n      this.domNode.setAttribute('href', this.constructor.sanitize(value));\n    }\n  }\n}\nfunction sanitize(url, protocols) {\n  const anchor = document.createElement('a');\n  anchor.href = url;\n  const protocol = anchor.href.slice(0, anchor.href.indexOf(':'));\n  return protocols.indexOf(protocol) > -1;\n}\nexport { Link as default, sanitize };\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,MAAMC,IAAI,SAASD,MAAM,CAAC;EACxB,OAAOE,QAAQ,GAAG,MAAM;EACxB,OAAOC,OAAO,GAAG,GAAG;EACpB,OAAOC,aAAa,GAAG,aAAa;EACpC,OAAOC,kBAAkB,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,CAAC;EACrE,OAAOC,MAAMA,CAACC,KAAK,EAAE;IACnB,MAAMC,IAAI,GAAG,KAAK,CAACF,MAAM,CAACC,KAAK,CAAC;IAChCC,IAAI,CAACC,YAAY,CAAC,MAAM,EAAE,IAAI,CAACC,QAAQ,CAACH,KAAK,CAAC,CAAC;IAC/CC,IAAI,CAACC,YAAY,CAAC,KAAK,EAAE,qBAAqB,CAAC;IAC/CD,IAAI,CAACC,YAAY,CAAC,QAAQ,EAAE,QAAQ,CAAC;IACrC,OAAOD,IAAI;EACb;EACA,OAAOG,OAAOA,CAACC,OAAO,EAAE;IACtB,OAAOA,OAAO,CAACC,YAAY,CAAC,MAAM,CAAC;EACrC;EACA,OAAOH,QAAQA,CAACI,GAAG,EAAE;IACnB,OAAOJ,QAAQ,CAACI,GAAG,EAAE,IAAI,CAACT,kBAAkB,CAAC,GAAGS,GAAG,GAAG,IAAI,CAACV,aAAa;EAC1E;EACAW,MAAMA,CAACC,IAAI,EAAET,KAAK,EAAE;IAClB,IAAIS,IAAI,KAAK,IAAI,CAACC,OAAO,CAACf,QAAQ,IAAI,CAACK,KAAK,EAAE;MAC5C,KAAK,CAACQ,MAAM,CAACC,IAAI,EAAET,KAAK,CAAC;IAC3B,CAAC,MAAM;MACL;MACA,IAAI,CAACK,OAAO,CAACH,YAAY,CAAC,MAAM,EAAE,IAAI,CAACS,WAAW,CAACR,QAAQ,CAACH,KAAK,CAAC,CAAC;IACrE;EACF;AACF;AACA,SAASG,QAAQA,CAACI,GAAG,EAAEK,SAAS,EAAE;EAChC,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;EAC1CF,MAAM,CAACG,IAAI,GAAGT,GAAG;EACjB,MAAMU,QAAQ,GAAGJ,MAAM,CAACG,IAAI,CAACE,KAAK,CAAC,CAAC,EAAEL,MAAM,CAACG,IAAI,CAACG,OAAO,CAAC,GAAG,CAAC,CAAC;EAC/D,OAAOP,SAAS,CAACO,OAAO,CAACF,QAAQ,CAAC,GAAG,CAAC,CAAC;AACzC;AACA,SAASvB,IAAI,IAAI0B,OAAO,EAAEjB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}