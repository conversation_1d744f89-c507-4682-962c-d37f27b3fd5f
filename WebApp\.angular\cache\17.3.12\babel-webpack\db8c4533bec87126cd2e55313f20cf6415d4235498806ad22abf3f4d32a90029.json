{"ast": null, "code": "import { cloneDeep, isEqual, merge } from 'lodash-es';\nimport { LeafBlot, EmbedBlot, Scope, ParentBlot } from 'parchment';\nimport Delta, { AttributeMap, Op } from 'quill-delta';\nimport Block, { BlockEmbed, bubbleFormats } from '../blots/block.js';\nimport Break from '../blots/break.js';\nimport CursorBlot from '../blots/cursor.js';\nimport TextBlot, { escapeText } from '../blots/text.js';\nimport { Range } from './selection.js';\nconst ASCII = /^[ -~]*$/;\nclass Editor {\n  constructor(scroll) {\n    this.scroll = scroll;\n    this.delta = this.getDelta();\n  }\n  applyDelta(delta) {\n    this.scroll.update();\n    let scrollLength = this.scroll.length();\n    this.scroll.batchStart();\n    const normalizedDelta = normalizeDelta(delta);\n    const deleteDelta = new Delta();\n    const normalizedOps = splitOpLines(normalizedDelta.ops.slice());\n    normalizedOps.reduce((index, op) => {\n      const length = Op.length(op);\n      let attributes = op.attributes || {};\n      let isImplicitNewlinePrepended = false;\n      let isImplicitNewlineAppended = false;\n      if (op.insert != null) {\n        deleteDelta.retain(length);\n        if (typeof op.insert === 'string') {\n          const text = op.insert;\n          isImplicitNewlineAppended = !text.endsWith('\\n') && (scrollLength <= index || !!this.scroll.descendant(BlockEmbed, index)[0]);\n          this.scroll.insertAt(index, text);\n          const [line, offset] = this.scroll.line(index);\n          let formats = merge({}, bubbleFormats(line));\n          if (line instanceof Block) {\n            const [leaf] = line.descendant(LeafBlot, offset);\n            if (leaf) {\n              formats = merge(formats, bubbleFormats(leaf));\n            }\n          }\n          attributes = AttributeMap.diff(formats, attributes) || {};\n        } else if (typeof op.insert === 'object') {\n          const key = Object.keys(op.insert)[0]; // There should only be one key\n          if (key == null) return index;\n          const isInlineEmbed = this.scroll.query(key, Scope.INLINE) != null;\n          if (isInlineEmbed) {\n            if (scrollLength <= index || !!this.scroll.descendant(BlockEmbed, index)[0]) {\n              isImplicitNewlineAppended = true;\n            }\n          } else if (index > 0) {\n            const [leaf, offset] = this.scroll.descendant(LeafBlot, index - 1);\n            if (leaf instanceof TextBlot) {\n              const text = leaf.value();\n              if (text[offset] !== '\\n') {\n                isImplicitNewlinePrepended = true;\n              }\n            } else if (leaf instanceof EmbedBlot && leaf.statics.scope === Scope.INLINE_BLOT) {\n              isImplicitNewlinePrepended = true;\n            }\n          }\n          this.scroll.insertAt(index, key, op.insert[key]);\n          if (isInlineEmbed) {\n            const [leaf] = this.scroll.descendant(LeafBlot, index);\n            if (leaf) {\n              const formats = merge({}, bubbleFormats(leaf));\n              attributes = AttributeMap.diff(formats, attributes) || {};\n            }\n          }\n        }\n        scrollLength += length;\n      } else {\n        deleteDelta.push(op);\n        if (op.retain !== null && typeof op.retain === 'object') {\n          const key = Object.keys(op.retain)[0];\n          if (key == null) return index;\n          this.scroll.updateEmbedAt(index, key, op.retain[key]);\n        }\n      }\n      Object.keys(attributes).forEach(name => {\n        this.scroll.formatAt(index, length, name, attributes[name]);\n      });\n      const prependedLength = isImplicitNewlinePrepended ? 1 : 0;\n      const addedLength = isImplicitNewlineAppended ? 1 : 0;\n      scrollLength += prependedLength + addedLength;\n      deleteDelta.retain(prependedLength);\n      deleteDelta.delete(addedLength);\n      return index + length + prependedLength + addedLength;\n    }, 0);\n    deleteDelta.reduce((index, op) => {\n      if (typeof op.delete === 'number') {\n        this.scroll.deleteAt(index, op.delete);\n        return index;\n      }\n      return index + Op.length(op);\n    }, 0);\n    this.scroll.batchEnd();\n    this.scroll.optimize();\n    return this.update(normalizedDelta);\n  }\n  deleteText(index, length) {\n    this.scroll.deleteAt(index, length);\n    return this.update(new Delta().retain(index).delete(length));\n  }\n  formatLine(index, length) {\n    let formats = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    this.scroll.update();\n    Object.keys(formats).forEach(format => {\n      this.scroll.lines(index, Math.max(length, 1)).forEach(line => {\n        line.format(format, formats[format]);\n      });\n    });\n    this.scroll.optimize();\n    const delta = new Delta().retain(index).retain(length, cloneDeep(formats));\n    return this.update(delta);\n  }\n  formatText(index, length) {\n    let formats = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    Object.keys(formats).forEach(format => {\n      this.scroll.formatAt(index, length, format, formats[format]);\n    });\n    const delta = new Delta().retain(index).retain(length, cloneDeep(formats));\n    return this.update(delta);\n  }\n  getContents(index, length) {\n    return this.delta.slice(index, index + length);\n  }\n  getDelta() {\n    return this.scroll.lines().reduce((delta, line) => {\n      return delta.concat(line.delta());\n    }, new Delta());\n  }\n  getFormat(index) {\n    let length = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n    let lines = [];\n    let leaves = [];\n    if (length === 0) {\n      this.scroll.path(index).forEach(path => {\n        const [blot] = path;\n        if (blot instanceof Block) {\n          lines.push(blot);\n        } else if (blot instanceof LeafBlot) {\n          leaves.push(blot);\n        }\n      });\n    } else {\n      lines = this.scroll.lines(index, length);\n      leaves = this.scroll.descendants(LeafBlot, index, length);\n    }\n    const [lineFormats, leafFormats] = [lines, leaves].map(blots => {\n      const blot = blots.shift();\n      if (blot == null) return {};\n      let formats = bubbleFormats(blot);\n      while (Object.keys(formats).length > 0) {\n        const blot = blots.shift();\n        if (blot == null) return formats;\n        formats = combineFormats(bubbleFormats(blot), formats);\n      }\n      return formats;\n    });\n    return {\n      ...lineFormats,\n      ...leafFormats\n    };\n  }\n  getHTML(index, length) {\n    const [line, lineOffset] = this.scroll.line(index);\n    if (line) {\n      const lineLength = line.length();\n      const isWithinLine = line.length() >= lineOffset + length;\n      if (isWithinLine && !(lineOffset === 0 && length === lineLength)) {\n        return convertHTML(line, lineOffset, length, true);\n      }\n      return convertHTML(this.scroll, index, length, true);\n    }\n    return '';\n  }\n  getText(index, length) {\n    return this.getContents(index, length).filter(op => typeof op.insert === 'string').map(op => op.insert).join('');\n  }\n  insertContents(index, contents) {\n    const normalizedDelta = normalizeDelta(contents);\n    const change = new Delta().retain(index).concat(normalizedDelta);\n    this.scroll.insertContents(index, normalizedDelta);\n    return this.update(change);\n  }\n  insertEmbed(index, embed, value) {\n    this.scroll.insertAt(index, embed, value);\n    return this.update(new Delta().retain(index).insert({\n      [embed]: value\n    }));\n  }\n  insertText(index, text) {\n    let formats = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    text = text.replace(/\\r\\n/g, '\\n').replace(/\\r/g, '\\n');\n    this.scroll.insertAt(index, text);\n    Object.keys(formats).forEach(format => {\n      this.scroll.formatAt(index, text.length, format, formats[format]);\n    });\n    return this.update(new Delta().retain(index).insert(text, cloneDeep(formats)));\n  }\n  isBlank() {\n    if (this.scroll.children.length === 0) return true;\n    if (this.scroll.children.length > 1) return false;\n    const blot = this.scroll.children.head;\n    if (blot?.statics.blotName !== Block.blotName) return false;\n    const block = blot;\n    if (block.children.length > 1) return false;\n    return block.children.head instanceof Break;\n  }\n  removeFormat(index, length) {\n    const text = this.getText(index, length);\n    const [line, offset] = this.scroll.line(index + length);\n    let suffixLength = 0;\n    let suffix = new Delta();\n    if (line != null) {\n      suffixLength = line.length() - offset;\n      suffix = line.delta().slice(offset, offset + suffixLength - 1).insert('\\n');\n    }\n    const contents = this.getContents(index, length + suffixLength);\n    const diff = contents.diff(new Delta().insert(text).concat(suffix));\n    const delta = new Delta().retain(index).concat(diff);\n    return this.applyDelta(delta);\n  }\n  update(change) {\n    let mutations = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n    let selectionInfo = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : undefined;\n    const oldDelta = this.delta;\n    if (mutations.length === 1 && mutations[0].type === 'characterData' &&\n    // @ts-expect-error Fix me later\n    mutations[0].target.data.match(ASCII) && this.scroll.find(mutations[0].target)) {\n      // Optimization for character changes\n      const textBlot = this.scroll.find(mutations[0].target);\n      const formats = bubbleFormats(textBlot);\n      const index = textBlot.offset(this.scroll);\n      // @ts-expect-error Fix me later\n      const oldValue = mutations[0].oldValue.replace(CursorBlot.CONTENTS, '');\n      const oldText = new Delta().insert(oldValue);\n      // @ts-expect-error\n      const newText = new Delta().insert(textBlot.value());\n      const relativeSelectionInfo = selectionInfo && {\n        oldRange: shiftRange(selectionInfo.oldRange, -index),\n        newRange: shiftRange(selectionInfo.newRange, -index)\n      };\n      const diffDelta = new Delta().retain(index).concat(oldText.diff(newText, relativeSelectionInfo));\n      change = diffDelta.reduce((delta, op) => {\n        if (op.insert) {\n          return delta.insert(op.insert, formats);\n        }\n        return delta.push(op);\n      }, new Delta());\n      this.delta = oldDelta.compose(change);\n    } else {\n      this.delta = this.getDelta();\n      if (!change || !isEqual(oldDelta.compose(change), this.delta)) {\n        change = oldDelta.diff(this.delta, selectionInfo);\n      }\n    }\n    return change;\n  }\n}\nfunction convertListHTML(items, lastIndent, types) {\n  if (items.length === 0) {\n    const [endTag] = getListType(types.pop());\n    if (lastIndent <= 0) {\n      return `</li></${endTag}>`;\n    }\n    return `</li></${endTag}>${convertListHTML([], lastIndent - 1, types)}`;\n  }\n  const [{\n    child,\n    offset,\n    length,\n    indent,\n    type\n  }, ...rest] = items;\n  const [tag, attribute] = getListType(type);\n  if (indent > lastIndent) {\n    types.push(type);\n    if (indent === lastIndent + 1) {\n      return `<${tag}><li${attribute}>${convertHTML(child, offset, length)}${convertListHTML(rest, indent, types)}`;\n    }\n    return `<${tag}><li>${convertListHTML(items, lastIndent + 1, types)}`;\n  }\n  const previousType = types[types.length - 1];\n  if (indent === lastIndent && type === previousType) {\n    return `</li><li${attribute}>${convertHTML(child, offset, length)}${convertListHTML(rest, indent, types)}`;\n  }\n  const [endTag] = getListType(types.pop());\n  return `</li></${endTag}>${convertListHTML(items, lastIndent - 1, types)}`;\n}\nfunction convertHTML(blot, index, length) {\n  let isRoot = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n  if ('html' in blot && typeof blot.html === 'function') {\n    return blot.html(index, length);\n  }\n  if (blot instanceof TextBlot) {\n    const escapedText = escapeText(blot.value().slice(index, index + length));\n    return escapedText.replaceAll(' ', '&nbsp;');\n  }\n  if (blot instanceof ParentBlot) {\n    // TODO fix API\n    if (blot.statics.blotName === 'list-container') {\n      const items = [];\n      blot.children.forEachAt(index, length, (child, offset, childLength) => {\n        const formats = 'formats' in child && typeof child.formats === 'function' ? child.formats() : {};\n        items.push({\n          child,\n          offset,\n          length: childLength,\n          indent: formats.indent || 0,\n          type: formats.list\n        });\n      });\n      return convertListHTML(items, -1, []);\n    }\n    const parts = [];\n    blot.children.forEachAt(index, length, (child, offset, childLength) => {\n      parts.push(convertHTML(child, offset, childLength));\n    });\n    if (isRoot || blot.statics.blotName === 'list') {\n      return parts.join('');\n    }\n    const {\n      outerHTML,\n      innerHTML\n    } = blot.domNode;\n    const [start, end] = outerHTML.split(`>${innerHTML}<`);\n    // TODO cleanup\n    if (start === '<table') {\n      return `<table style=\"border: 1px solid #000;\">${parts.join('')}<${end}`;\n    }\n    return `${start}>${parts.join('')}<${end}`;\n  }\n  return blot.domNode instanceof Element ? blot.domNode.outerHTML : '';\n}\nfunction combineFormats(formats, combined) {\n  return Object.keys(combined).reduce((merged, name) => {\n    if (formats[name] == null) return merged;\n    const combinedValue = combined[name];\n    if (combinedValue === formats[name]) {\n      merged[name] = combinedValue;\n    } else if (Array.isArray(combinedValue)) {\n      if (combinedValue.indexOf(formats[name]) < 0) {\n        merged[name] = combinedValue.concat([formats[name]]);\n      } else {\n        // If style already exists, don't add to an array, but don't lose other styles\n        merged[name] = combinedValue;\n      }\n    } else {\n      merged[name] = [combinedValue, formats[name]];\n    }\n    return merged;\n  }, {});\n}\nfunction getListType(type) {\n  const tag = type === 'ordered' ? 'ol' : 'ul';\n  switch (type) {\n    case 'checked':\n      return [tag, ' data-list=\"checked\"'];\n    case 'unchecked':\n      return [tag, ' data-list=\"unchecked\"'];\n    default:\n      return [tag, ''];\n  }\n}\nfunction normalizeDelta(delta) {\n  return delta.reduce((normalizedDelta, op) => {\n    if (typeof op.insert === 'string') {\n      const text = op.insert.replace(/\\r\\n/g, '\\n').replace(/\\r/g, '\\n');\n      return normalizedDelta.insert(text, op.attributes);\n    }\n    return normalizedDelta.push(op);\n  }, new Delta());\n}\nfunction shiftRange(_ref, amount) {\n  let {\n    index,\n    length\n  } = _ref;\n  return new Range(index + amount, length);\n}\nfunction splitOpLines(ops) {\n  const split = [];\n  ops.forEach(op => {\n    if (typeof op.insert === 'string') {\n      const lines = op.insert.split('\\n');\n      lines.forEach((line, index) => {\n        if (index) split.push({\n          insert: '\\n',\n          attributes: op.attributes\n        });\n        if (line) split.push({\n          insert: line,\n          attributes: op.attributes\n        });\n      });\n    } else {\n      split.push(op);\n    }\n  });\n  return split;\n}\nexport default Editor;", "map": {"version": 3, "names": ["cloneDeep", "isEqual", "merge", "LeafBlot", "EmbedBlot", "<PERSON><PERSON>", "ParentBlot", "Delta", "AttributeMap", "Op", "Block", "BlockEmbed", "bubbleFormats", "Break", "CursorBlot", "TextBlot", "escapeText", "Range", "ASCII", "Editor", "constructor", "scroll", "delta", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "update", "<PERSON><PERSON><PERSON><PERSON>", "length", "batchStart", "normalizedDelta", "normalizeDel<PERSON>", "deleteDelta", "normalizedOps", "splitOpLines", "ops", "slice", "reduce", "index", "op", "attributes", "isImplicitNewlinePrepended", "isImplicitNewlineAppended", "insert", "retain", "text", "endsWith", "descendant", "insertAt", "line", "offset", "formats", "leaf", "diff", "key", "Object", "keys", "isInlineEmbed", "query", "INLINE", "value", "statics", "scope", "INLINE_BLOT", "push", "updateEmbedAt", "for<PERSON>ach", "name", "formatAt", "prependedLength", "<PERSON><PERSON><PERSON><PERSON>", "delete", "deleteAt", "batchEnd", "optimize", "deleteText", "formatLine", "arguments", "undefined", "format", "lines", "Math", "max", "formatText", "getContents", "concat", "getFormat", "leaves", "path", "blot", "descendants", "lineFormats", "leafFormats", "map", "blots", "shift", "combineFormats", "getHTML", "lineOffset", "lineLength", "isWithinLine", "convertHTML", "getText", "filter", "join", "insertContents", "contents", "change", "insertEmbed", "embed", "insertText", "replace", "isBlank", "children", "head", "blotName", "block", "removeFormat", "suffixLength", "suffix", "mutations", "selectionInfo", "<PERSON><PERSON><PERSON><PERSON>", "type", "target", "data", "match", "find", "textBlot", "oldValue", "CONTENTS", "oldText", "newText", "relativeSelectionInfo", "oldRange", "shiftRange", "newRange", "diff<PERSON><PERSON><PERSON>", "compose", "convertListHTML", "items", "lastIndent", "types", "endTag", "getListType", "pop", "child", "indent", "rest", "tag", "attribute", "previousType", "isRoot", "html", "escapedText", "replaceAll", "forEachAt", "<PERSON><PERSON><PERSON><PERSON>", "list", "parts", "outerHTML", "innerHTML", "domNode", "start", "end", "split", "Element", "combined", "merged", "combinedValue", "Array", "isArray", "indexOf", "_ref", "amount"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/quill/core/editor.js"], "sourcesContent": ["import { cloneDeep, isEqual, merge } from 'lodash-es';\nimport { LeafBlot, EmbedBlot, Scope, ParentBlot } from 'parchment';\nimport Delta, { AttributeMap, Op } from 'quill-delta';\nimport Block, { BlockEmbed, bubbleFormats } from '../blots/block.js';\nimport Break from '../blots/break.js';\nimport CursorBlot from '../blots/cursor.js';\nimport TextBlot, { escapeText } from '../blots/text.js';\nimport { Range } from './selection.js';\nconst ASCII = /^[ -~]*$/;\nclass Editor {\n  constructor(scroll) {\n    this.scroll = scroll;\n    this.delta = this.getDelta();\n  }\n  applyDelta(delta) {\n    this.scroll.update();\n    let scrollLength = this.scroll.length();\n    this.scroll.batchStart();\n    const normalizedDelta = normalizeDelta(delta);\n    const deleteDelta = new Delta();\n    const normalizedOps = splitOpLines(normalizedDelta.ops.slice());\n    normalizedOps.reduce((index, op) => {\n      const length = Op.length(op);\n      let attributes = op.attributes || {};\n      let isImplicitNewlinePrepended = false;\n      let isImplicitNewlineAppended = false;\n      if (op.insert != null) {\n        deleteDelta.retain(length);\n        if (typeof op.insert === 'string') {\n          const text = op.insert;\n          isImplicitNewlineAppended = !text.endsWith('\\n') && (scrollLength <= index || !!this.scroll.descendant(BlockEmbed, index)[0]);\n          this.scroll.insertAt(index, text);\n          const [line, offset] = this.scroll.line(index);\n          let formats = merge({}, bubbleFormats(line));\n          if (line instanceof Block) {\n            const [leaf] = line.descendant(LeafBlot, offset);\n            if (leaf) {\n              formats = merge(formats, bubbleFormats(leaf));\n            }\n          }\n          attributes = AttributeMap.diff(formats, attributes) || {};\n        } else if (typeof op.insert === 'object') {\n          const key = Object.keys(op.insert)[0]; // There should only be one key\n          if (key == null) return index;\n          const isInlineEmbed = this.scroll.query(key, Scope.INLINE) != null;\n          if (isInlineEmbed) {\n            if (scrollLength <= index || !!this.scroll.descendant(BlockEmbed, index)[0]) {\n              isImplicitNewlineAppended = true;\n            }\n          } else if (index > 0) {\n            const [leaf, offset] = this.scroll.descendant(LeafBlot, index - 1);\n            if (leaf instanceof TextBlot) {\n              const text = leaf.value();\n              if (text[offset] !== '\\n') {\n                isImplicitNewlinePrepended = true;\n              }\n            } else if (leaf instanceof EmbedBlot && leaf.statics.scope === Scope.INLINE_BLOT) {\n              isImplicitNewlinePrepended = true;\n            }\n          }\n          this.scroll.insertAt(index, key, op.insert[key]);\n          if (isInlineEmbed) {\n            const [leaf] = this.scroll.descendant(LeafBlot, index);\n            if (leaf) {\n              const formats = merge({}, bubbleFormats(leaf));\n              attributes = AttributeMap.diff(formats, attributes) || {};\n            }\n          }\n        }\n        scrollLength += length;\n      } else {\n        deleteDelta.push(op);\n        if (op.retain !== null && typeof op.retain === 'object') {\n          const key = Object.keys(op.retain)[0];\n          if (key == null) return index;\n          this.scroll.updateEmbedAt(index, key, op.retain[key]);\n        }\n      }\n      Object.keys(attributes).forEach(name => {\n        this.scroll.formatAt(index, length, name, attributes[name]);\n      });\n      const prependedLength = isImplicitNewlinePrepended ? 1 : 0;\n      const addedLength = isImplicitNewlineAppended ? 1 : 0;\n      scrollLength += prependedLength + addedLength;\n      deleteDelta.retain(prependedLength);\n      deleteDelta.delete(addedLength);\n      return index + length + prependedLength + addedLength;\n    }, 0);\n    deleteDelta.reduce((index, op) => {\n      if (typeof op.delete === 'number') {\n        this.scroll.deleteAt(index, op.delete);\n        return index;\n      }\n      return index + Op.length(op);\n    }, 0);\n    this.scroll.batchEnd();\n    this.scroll.optimize();\n    return this.update(normalizedDelta);\n  }\n  deleteText(index, length) {\n    this.scroll.deleteAt(index, length);\n    return this.update(new Delta().retain(index).delete(length));\n  }\n  formatLine(index, length) {\n    let formats = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    this.scroll.update();\n    Object.keys(formats).forEach(format => {\n      this.scroll.lines(index, Math.max(length, 1)).forEach(line => {\n        line.format(format, formats[format]);\n      });\n    });\n    this.scroll.optimize();\n    const delta = new Delta().retain(index).retain(length, cloneDeep(formats));\n    return this.update(delta);\n  }\n  formatText(index, length) {\n    let formats = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    Object.keys(formats).forEach(format => {\n      this.scroll.formatAt(index, length, format, formats[format]);\n    });\n    const delta = new Delta().retain(index).retain(length, cloneDeep(formats));\n    return this.update(delta);\n  }\n  getContents(index, length) {\n    return this.delta.slice(index, index + length);\n  }\n  getDelta() {\n    return this.scroll.lines().reduce((delta, line) => {\n      return delta.concat(line.delta());\n    }, new Delta());\n  }\n  getFormat(index) {\n    let length = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n    let lines = [];\n    let leaves = [];\n    if (length === 0) {\n      this.scroll.path(index).forEach(path => {\n        const [blot] = path;\n        if (blot instanceof Block) {\n          lines.push(blot);\n        } else if (blot instanceof LeafBlot) {\n          leaves.push(blot);\n        }\n      });\n    } else {\n      lines = this.scroll.lines(index, length);\n      leaves = this.scroll.descendants(LeafBlot, index, length);\n    }\n    const [lineFormats, leafFormats] = [lines, leaves].map(blots => {\n      const blot = blots.shift();\n      if (blot == null) return {};\n      let formats = bubbleFormats(blot);\n      while (Object.keys(formats).length > 0) {\n        const blot = blots.shift();\n        if (blot == null) return formats;\n        formats = combineFormats(bubbleFormats(blot), formats);\n      }\n      return formats;\n    });\n    return {\n      ...lineFormats,\n      ...leafFormats\n    };\n  }\n  getHTML(index, length) {\n    const [line, lineOffset] = this.scroll.line(index);\n    if (line) {\n      const lineLength = line.length();\n      const isWithinLine = line.length() >= lineOffset + length;\n      if (isWithinLine && !(lineOffset === 0 && length === lineLength)) {\n        return convertHTML(line, lineOffset, length, true);\n      }\n      return convertHTML(this.scroll, index, length, true);\n    }\n    return '';\n  }\n  getText(index, length) {\n    return this.getContents(index, length).filter(op => typeof op.insert === 'string').map(op => op.insert).join('');\n  }\n  insertContents(index, contents) {\n    const normalizedDelta = normalizeDelta(contents);\n    const change = new Delta().retain(index).concat(normalizedDelta);\n    this.scroll.insertContents(index, normalizedDelta);\n    return this.update(change);\n  }\n  insertEmbed(index, embed, value) {\n    this.scroll.insertAt(index, embed, value);\n    return this.update(new Delta().retain(index).insert({\n      [embed]: value\n    }));\n  }\n  insertText(index, text) {\n    let formats = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    text = text.replace(/\\r\\n/g, '\\n').replace(/\\r/g, '\\n');\n    this.scroll.insertAt(index, text);\n    Object.keys(formats).forEach(format => {\n      this.scroll.formatAt(index, text.length, format, formats[format]);\n    });\n    return this.update(new Delta().retain(index).insert(text, cloneDeep(formats)));\n  }\n  isBlank() {\n    if (this.scroll.children.length === 0) return true;\n    if (this.scroll.children.length > 1) return false;\n    const blot = this.scroll.children.head;\n    if (blot?.statics.blotName !== Block.blotName) return false;\n    const block = blot;\n    if (block.children.length > 1) return false;\n    return block.children.head instanceof Break;\n  }\n  removeFormat(index, length) {\n    const text = this.getText(index, length);\n    const [line, offset] = this.scroll.line(index + length);\n    let suffixLength = 0;\n    let suffix = new Delta();\n    if (line != null) {\n      suffixLength = line.length() - offset;\n      suffix = line.delta().slice(offset, offset + suffixLength - 1).insert('\\n');\n    }\n    const contents = this.getContents(index, length + suffixLength);\n    const diff = contents.diff(new Delta().insert(text).concat(suffix));\n    const delta = new Delta().retain(index).concat(diff);\n    return this.applyDelta(delta);\n  }\n  update(change) {\n    let mutations = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n    let selectionInfo = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : undefined;\n    const oldDelta = this.delta;\n    if (mutations.length === 1 && mutations[0].type === 'characterData' &&\n    // @ts-expect-error Fix me later\n    mutations[0].target.data.match(ASCII) && this.scroll.find(mutations[0].target)) {\n      // Optimization for character changes\n      const textBlot = this.scroll.find(mutations[0].target);\n      const formats = bubbleFormats(textBlot);\n      const index = textBlot.offset(this.scroll);\n      // @ts-expect-error Fix me later\n      const oldValue = mutations[0].oldValue.replace(CursorBlot.CONTENTS, '');\n      const oldText = new Delta().insert(oldValue);\n      // @ts-expect-error\n      const newText = new Delta().insert(textBlot.value());\n      const relativeSelectionInfo = selectionInfo && {\n        oldRange: shiftRange(selectionInfo.oldRange, -index),\n        newRange: shiftRange(selectionInfo.newRange, -index)\n      };\n      const diffDelta = new Delta().retain(index).concat(oldText.diff(newText, relativeSelectionInfo));\n      change = diffDelta.reduce((delta, op) => {\n        if (op.insert) {\n          return delta.insert(op.insert, formats);\n        }\n        return delta.push(op);\n      }, new Delta());\n      this.delta = oldDelta.compose(change);\n    } else {\n      this.delta = this.getDelta();\n      if (!change || !isEqual(oldDelta.compose(change), this.delta)) {\n        change = oldDelta.diff(this.delta, selectionInfo);\n      }\n    }\n    return change;\n  }\n}\nfunction convertListHTML(items, lastIndent, types) {\n  if (items.length === 0) {\n    const [endTag] = getListType(types.pop());\n    if (lastIndent <= 0) {\n      return `</li></${endTag}>`;\n    }\n    return `</li></${endTag}>${convertListHTML([], lastIndent - 1, types)}`;\n  }\n  const [{\n    child,\n    offset,\n    length,\n    indent,\n    type\n  }, ...rest] = items;\n  const [tag, attribute] = getListType(type);\n  if (indent > lastIndent) {\n    types.push(type);\n    if (indent === lastIndent + 1) {\n      return `<${tag}><li${attribute}>${convertHTML(child, offset, length)}${convertListHTML(rest, indent, types)}`;\n    }\n    return `<${tag}><li>${convertListHTML(items, lastIndent + 1, types)}`;\n  }\n  const previousType = types[types.length - 1];\n  if (indent === lastIndent && type === previousType) {\n    return `</li><li${attribute}>${convertHTML(child, offset, length)}${convertListHTML(rest, indent, types)}`;\n  }\n  const [endTag] = getListType(types.pop());\n  return `</li></${endTag}>${convertListHTML(items, lastIndent - 1, types)}`;\n}\nfunction convertHTML(blot, index, length) {\n  let isRoot = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n  if ('html' in blot && typeof blot.html === 'function') {\n    return blot.html(index, length);\n  }\n  if (blot instanceof TextBlot) {\n    const escapedText = escapeText(blot.value().slice(index, index + length));\n    return escapedText.replaceAll(' ', '&nbsp;');\n  }\n  if (blot instanceof ParentBlot) {\n    // TODO fix API\n    if (blot.statics.blotName === 'list-container') {\n      const items = [];\n      blot.children.forEachAt(index, length, (child, offset, childLength) => {\n        const formats = 'formats' in child && typeof child.formats === 'function' ? child.formats() : {};\n        items.push({\n          child,\n          offset,\n          length: childLength,\n          indent: formats.indent || 0,\n          type: formats.list\n        });\n      });\n      return convertListHTML(items, -1, []);\n    }\n    const parts = [];\n    blot.children.forEachAt(index, length, (child, offset, childLength) => {\n      parts.push(convertHTML(child, offset, childLength));\n    });\n    if (isRoot || blot.statics.blotName === 'list') {\n      return parts.join('');\n    }\n    const {\n      outerHTML,\n      innerHTML\n    } = blot.domNode;\n    const [start, end] = outerHTML.split(`>${innerHTML}<`);\n    // TODO cleanup\n    if (start === '<table') {\n      return `<table style=\"border: 1px solid #000;\">${parts.join('')}<${end}`;\n    }\n    return `${start}>${parts.join('')}<${end}`;\n  }\n  return blot.domNode instanceof Element ? blot.domNode.outerHTML : '';\n}\nfunction combineFormats(formats, combined) {\n  return Object.keys(combined).reduce((merged, name) => {\n    if (formats[name] == null) return merged;\n    const combinedValue = combined[name];\n    if (combinedValue === formats[name]) {\n      merged[name] = combinedValue;\n    } else if (Array.isArray(combinedValue)) {\n      if (combinedValue.indexOf(formats[name]) < 0) {\n        merged[name] = combinedValue.concat([formats[name]]);\n      } else {\n        // If style already exists, don't add to an array, but don't lose other styles\n        merged[name] = combinedValue;\n      }\n    } else {\n      merged[name] = [combinedValue, formats[name]];\n    }\n    return merged;\n  }, {});\n}\nfunction getListType(type) {\n  const tag = type === 'ordered' ? 'ol' : 'ul';\n  switch (type) {\n    case 'checked':\n      return [tag, ' data-list=\"checked\"'];\n    case 'unchecked':\n      return [tag, ' data-list=\"unchecked\"'];\n    default:\n      return [tag, ''];\n  }\n}\nfunction normalizeDelta(delta) {\n  return delta.reduce((normalizedDelta, op) => {\n    if (typeof op.insert === 'string') {\n      const text = op.insert.replace(/\\r\\n/g, '\\n').replace(/\\r/g, '\\n');\n      return normalizedDelta.insert(text, op.attributes);\n    }\n    return normalizedDelta.push(op);\n  }, new Delta());\n}\nfunction shiftRange(_ref, amount) {\n  let {\n    index,\n    length\n  } = _ref;\n  return new Range(index + amount, length);\n}\nfunction splitOpLines(ops) {\n  const split = [];\n  ops.forEach(op => {\n    if (typeof op.insert === 'string') {\n      const lines = op.insert.split('\\n');\n      lines.forEach((line, index) => {\n        if (index) split.push({\n          insert: '\\n',\n          attributes: op.attributes\n        });\n        if (line) split.push({\n          insert: line,\n          attributes: op.attributes\n        });\n      });\n    } else {\n      split.push(op);\n    }\n  });\n  return split;\n}\nexport default Editor;\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,OAAO,EAAEC,KAAK,QAAQ,WAAW;AACrD,SAASC,QAAQ,EAAEC,SAAS,EAAEC,KAAK,EAAEC,UAAU,QAAQ,WAAW;AAClE,OAAOC,KAAK,IAAIC,YAAY,EAAEC,EAAE,QAAQ,aAAa;AACrD,OAAOC,KAAK,IAAIC,UAAU,EAAEC,aAAa,QAAQ,mBAAmB;AACpE,OAAOC,KAAK,MAAM,mBAAmB;AACrC,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,QAAQ,IAAIC,UAAU,QAAQ,kBAAkB;AACvD,SAASC,KAAK,QAAQ,gBAAgB;AACtC,MAAMC,KAAK,GAAG,UAAU;AACxB,MAAMC,MAAM,CAAC;EACXC,WAAWA,CAACC,MAAM,EAAE;IAClB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;EAC9B;EACAC,UAAUA,CAACF,KAAK,EAAE;IAChB,IAAI,CAACD,MAAM,CAACI,MAAM,CAAC,CAAC;IACpB,IAAIC,YAAY,GAAG,IAAI,CAACL,MAAM,CAACM,MAAM,CAAC,CAAC;IACvC,IAAI,CAACN,MAAM,CAACO,UAAU,CAAC,CAAC;IACxB,MAAMC,eAAe,GAAGC,cAAc,CAACR,KAAK,CAAC;IAC7C,MAAMS,WAAW,GAAG,IAAIxB,KAAK,CAAC,CAAC;IAC/B,MAAMyB,aAAa,GAAGC,YAAY,CAACJ,eAAe,CAACK,GAAG,CAACC,KAAK,CAAC,CAAC,CAAC;IAC/DH,aAAa,CAACI,MAAM,CAAC,CAACC,KAAK,EAAEC,EAAE,KAAK;MAClC,MAAMX,MAAM,GAAGlB,EAAE,CAACkB,MAAM,CAACW,EAAE,CAAC;MAC5B,IAAIC,UAAU,GAAGD,EAAE,CAACC,UAAU,IAAI,CAAC,CAAC;MACpC,IAAIC,0BAA0B,GAAG,KAAK;MACtC,IAAIC,yBAAyB,GAAG,KAAK;MACrC,IAAIH,EAAE,CAACI,MAAM,IAAI,IAAI,EAAE;QACrBX,WAAW,CAACY,MAAM,CAAChB,MAAM,CAAC;QAC1B,IAAI,OAAOW,EAAE,CAACI,MAAM,KAAK,QAAQ,EAAE;UACjC,MAAME,IAAI,GAAGN,EAAE,CAACI,MAAM;UACtBD,yBAAyB,GAAG,CAACG,IAAI,CAACC,QAAQ,CAAC,IAAI,CAAC,KAAKnB,YAAY,IAAIW,KAAK,IAAI,CAAC,CAAC,IAAI,CAAChB,MAAM,CAACyB,UAAU,CAACnC,UAAU,EAAE0B,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7H,IAAI,CAAChB,MAAM,CAAC0B,QAAQ,CAACV,KAAK,EAAEO,IAAI,CAAC;UACjC,MAAM,CAACI,IAAI,EAAEC,MAAM,CAAC,GAAG,IAAI,CAAC5B,MAAM,CAAC2B,IAAI,CAACX,KAAK,CAAC;UAC9C,IAAIa,OAAO,GAAGhD,KAAK,CAAC,CAAC,CAAC,EAAEU,aAAa,CAACoC,IAAI,CAAC,CAAC;UAC5C,IAAIA,IAAI,YAAYtC,KAAK,EAAE;YACzB,MAAM,CAACyC,IAAI,CAAC,GAAGH,IAAI,CAACF,UAAU,CAAC3C,QAAQ,EAAE8C,MAAM,CAAC;YAChD,IAAIE,IAAI,EAAE;cACRD,OAAO,GAAGhD,KAAK,CAACgD,OAAO,EAAEtC,aAAa,CAACuC,IAAI,CAAC,CAAC;YAC/C;UACF;UACAZ,UAAU,GAAG/B,YAAY,CAAC4C,IAAI,CAACF,OAAO,EAAEX,UAAU,CAAC,IAAI,CAAC,CAAC;QAC3D,CAAC,MAAM,IAAI,OAAOD,EAAE,CAACI,MAAM,KAAK,QAAQ,EAAE;UACxC,MAAMW,GAAG,GAAGC,MAAM,CAACC,IAAI,CAACjB,EAAE,CAACI,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvC,IAAIW,GAAG,IAAI,IAAI,EAAE,OAAOhB,KAAK;UAC7B,MAAMmB,aAAa,GAAG,IAAI,CAACnC,MAAM,CAACoC,KAAK,CAACJ,GAAG,EAAEhD,KAAK,CAACqD,MAAM,CAAC,IAAI,IAAI;UAClE,IAAIF,aAAa,EAAE;YACjB,IAAI9B,YAAY,IAAIW,KAAK,IAAI,CAAC,CAAC,IAAI,CAAChB,MAAM,CAACyB,UAAU,CAACnC,UAAU,EAAE0B,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;cAC3EI,yBAAyB,GAAG,IAAI;YAClC;UACF,CAAC,MAAM,IAAIJ,KAAK,GAAG,CAAC,EAAE;YACpB,MAAM,CAACc,IAAI,EAAEF,MAAM,CAAC,GAAG,IAAI,CAAC5B,MAAM,CAACyB,UAAU,CAAC3C,QAAQ,EAAEkC,KAAK,GAAG,CAAC,CAAC;YAClE,IAAIc,IAAI,YAAYpC,QAAQ,EAAE;cAC5B,MAAM6B,IAAI,GAAGO,IAAI,CAACQ,KAAK,CAAC,CAAC;cACzB,IAAIf,IAAI,CAACK,MAAM,CAAC,KAAK,IAAI,EAAE;gBACzBT,0BAA0B,GAAG,IAAI;cACnC;YACF,CAAC,MAAM,IAAIW,IAAI,YAAY/C,SAAS,IAAI+C,IAAI,CAACS,OAAO,CAACC,KAAK,KAAKxD,KAAK,CAACyD,WAAW,EAAE;cAChFtB,0BAA0B,GAAG,IAAI;YACnC;UACF;UACA,IAAI,CAACnB,MAAM,CAAC0B,QAAQ,CAACV,KAAK,EAAEgB,GAAG,EAAEf,EAAE,CAACI,MAAM,CAACW,GAAG,CAAC,CAAC;UAChD,IAAIG,aAAa,EAAE;YACjB,MAAM,CAACL,IAAI,CAAC,GAAG,IAAI,CAAC9B,MAAM,CAACyB,UAAU,CAAC3C,QAAQ,EAAEkC,KAAK,CAAC;YACtD,IAAIc,IAAI,EAAE;cACR,MAAMD,OAAO,GAAGhD,KAAK,CAAC,CAAC,CAAC,EAAEU,aAAa,CAACuC,IAAI,CAAC,CAAC;cAC9CZ,UAAU,GAAG/B,YAAY,CAAC4C,IAAI,CAACF,OAAO,EAAEX,UAAU,CAAC,IAAI,CAAC,CAAC;YAC3D;UACF;QACF;QACAb,YAAY,IAAIC,MAAM;MACxB,CAAC,MAAM;QACLI,WAAW,CAACgC,IAAI,CAACzB,EAAE,CAAC;QACpB,IAAIA,EAAE,CAACK,MAAM,KAAK,IAAI,IAAI,OAAOL,EAAE,CAACK,MAAM,KAAK,QAAQ,EAAE;UACvD,MAAMU,GAAG,GAAGC,MAAM,CAACC,IAAI,CAACjB,EAAE,CAACK,MAAM,CAAC,CAAC,CAAC,CAAC;UACrC,IAAIU,GAAG,IAAI,IAAI,EAAE,OAAOhB,KAAK;UAC7B,IAAI,CAAChB,MAAM,CAAC2C,aAAa,CAAC3B,KAAK,EAAEgB,GAAG,EAAEf,EAAE,CAACK,MAAM,CAACU,GAAG,CAAC,CAAC;QACvD;MACF;MACAC,MAAM,CAACC,IAAI,CAAChB,UAAU,CAAC,CAAC0B,OAAO,CAACC,IAAI,IAAI;QACtC,IAAI,CAAC7C,MAAM,CAAC8C,QAAQ,CAAC9B,KAAK,EAAEV,MAAM,EAAEuC,IAAI,EAAE3B,UAAU,CAAC2B,IAAI,CAAC,CAAC;MAC7D,CAAC,CAAC;MACF,MAAME,eAAe,GAAG5B,0BAA0B,GAAG,CAAC,GAAG,CAAC;MAC1D,MAAM6B,WAAW,GAAG5B,yBAAyB,GAAG,CAAC,GAAG,CAAC;MACrDf,YAAY,IAAI0C,eAAe,GAAGC,WAAW;MAC7CtC,WAAW,CAACY,MAAM,CAACyB,eAAe,CAAC;MACnCrC,WAAW,CAACuC,MAAM,CAACD,WAAW,CAAC;MAC/B,OAAOhC,KAAK,GAAGV,MAAM,GAAGyC,eAAe,GAAGC,WAAW;IACvD,CAAC,EAAE,CAAC,CAAC;IACLtC,WAAW,CAACK,MAAM,CAAC,CAACC,KAAK,EAAEC,EAAE,KAAK;MAChC,IAAI,OAAOA,EAAE,CAACgC,MAAM,KAAK,QAAQ,EAAE;QACjC,IAAI,CAACjD,MAAM,CAACkD,QAAQ,CAAClC,KAAK,EAAEC,EAAE,CAACgC,MAAM,CAAC;QACtC,OAAOjC,KAAK;MACd;MACA,OAAOA,KAAK,GAAG5B,EAAE,CAACkB,MAAM,CAACW,EAAE,CAAC;IAC9B,CAAC,EAAE,CAAC,CAAC;IACL,IAAI,CAACjB,MAAM,CAACmD,QAAQ,CAAC,CAAC;IACtB,IAAI,CAACnD,MAAM,CAACoD,QAAQ,CAAC,CAAC;IACtB,OAAO,IAAI,CAAChD,MAAM,CAACI,eAAe,CAAC;EACrC;EACA6C,UAAUA,CAACrC,KAAK,EAAEV,MAAM,EAAE;IACxB,IAAI,CAACN,MAAM,CAACkD,QAAQ,CAAClC,KAAK,EAAEV,MAAM,CAAC;IACnC,OAAO,IAAI,CAACF,MAAM,CAAC,IAAIlB,KAAK,CAAC,CAAC,CAACoC,MAAM,CAACN,KAAK,CAAC,CAACiC,MAAM,CAAC3C,MAAM,CAAC,CAAC;EAC9D;EACAgD,UAAUA,CAACtC,KAAK,EAAEV,MAAM,EAAE;IACxB,IAAIuB,OAAO,GAAG0B,SAAS,CAACjD,MAAM,GAAG,CAAC,IAAIiD,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpF,IAAI,CAACvD,MAAM,CAACI,MAAM,CAAC,CAAC;IACpB6B,MAAM,CAACC,IAAI,CAACL,OAAO,CAAC,CAACe,OAAO,CAACa,MAAM,IAAI;MACrC,IAAI,CAACzD,MAAM,CAAC0D,KAAK,CAAC1C,KAAK,EAAE2C,IAAI,CAACC,GAAG,CAACtD,MAAM,EAAE,CAAC,CAAC,CAAC,CAACsC,OAAO,CAACjB,IAAI,IAAI;QAC5DA,IAAI,CAAC8B,MAAM,CAACA,MAAM,EAAE5B,OAAO,CAAC4B,MAAM,CAAC,CAAC;MACtC,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAI,CAACzD,MAAM,CAACoD,QAAQ,CAAC,CAAC;IACtB,MAAMnD,KAAK,GAAG,IAAIf,KAAK,CAAC,CAAC,CAACoC,MAAM,CAACN,KAAK,CAAC,CAACM,MAAM,CAAChB,MAAM,EAAE3B,SAAS,CAACkD,OAAO,CAAC,CAAC;IAC1E,OAAO,IAAI,CAACzB,MAAM,CAACH,KAAK,CAAC;EAC3B;EACA4D,UAAUA,CAAC7C,KAAK,EAAEV,MAAM,EAAE;IACxB,IAAIuB,OAAO,GAAG0B,SAAS,CAACjD,MAAM,GAAG,CAAC,IAAIiD,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpFtB,MAAM,CAACC,IAAI,CAACL,OAAO,CAAC,CAACe,OAAO,CAACa,MAAM,IAAI;MACrC,IAAI,CAACzD,MAAM,CAAC8C,QAAQ,CAAC9B,KAAK,EAAEV,MAAM,EAAEmD,MAAM,EAAE5B,OAAO,CAAC4B,MAAM,CAAC,CAAC;IAC9D,CAAC,CAAC;IACF,MAAMxD,KAAK,GAAG,IAAIf,KAAK,CAAC,CAAC,CAACoC,MAAM,CAACN,KAAK,CAAC,CAACM,MAAM,CAAChB,MAAM,EAAE3B,SAAS,CAACkD,OAAO,CAAC,CAAC;IAC1E,OAAO,IAAI,CAACzB,MAAM,CAACH,KAAK,CAAC;EAC3B;EACA6D,WAAWA,CAAC9C,KAAK,EAAEV,MAAM,EAAE;IACzB,OAAO,IAAI,CAACL,KAAK,CAACa,KAAK,CAACE,KAAK,EAAEA,KAAK,GAAGV,MAAM,CAAC;EAChD;EACAJ,QAAQA,CAAA,EAAG;IACT,OAAO,IAAI,CAACF,MAAM,CAAC0D,KAAK,CAAC,CAAC,CAAC3C,MAAM,CAAC,CAACd,KAAK,EAAE0B,IAAI,KAAK;MACjD,OAAO1B,KAAK,CAAC8D,MAAM,CAACpC,IAAI,CAAC1B,KAAK,CAAC,CAAC,CAAC;IACnC,CAAC,EAAE,IAAIf,KAAK,CAAC,CAAC,CAAC;EACjB;EACA8E,SAASA,CAAChD,KAAK,EAAE;IACf,IAAIV,MAAM,GAAGiD,SAAS,CAACjD,MAAM,GAAG,CAAC,IAAIiD,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IAClF,IAAIG,KAAK,GAAG,EAAE;IACd,IAAIO,MAAM,GAAG,EAAE;IACf,IAAI3D,MAAM,KAAK,CAAC,EAAE;MAChB,IAAI,CAACN,MAAM,CAACkE,IAAI,CAAClD,KAAK,CAAC,CAAC4B,OAAO,CAACsB,IAAI,IAAI;QACtC,MAAM,CAACC,IAAI,CAAC,GAAGD,IAAI;QACnB,IAAIC,IAAI,YAAY9E,KAAK,EAAE;UACzBqE,KAAK,CAAChB,IAAI,CAACyB,IAAI,CAAC;QAClB,CAAC,MAAM,IAAIA,IAAI,YAAYrF,QAAQ,EAAE;UACnCmF,MAAM,CAACvB,IAAI,CAACyB,IAAI,CAAC;QACnB;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACLT,KAAK,GAAG,IAAI,CAAC1D,MAAM,CAAC0D,KAAK,CAAC1C,KAAK,EAAEV,MAAM,CAAC;MACxC2D,MAAM,GAAG,IAAI,CAACjE,MAAM,CAACoE,WAAW,CAACtF,QAAQ,EAAEkC,KAAK,EAAEV,MAAM,CAAC;IAC3D;IACA,MAAM,CAAC+D,WAAW,EAAEC,WAAW,CAAC,GAAG,CAACZ,KAAK,EAAEO,MAAM,CAAC,CAACM,GAAG,CAACC,KAAK,IAAI;MAC9D,MAAML,IAAI,GAAGK,KAAK,CAACC,KAAK,CAAC,CAAC;MAC1B,IAAIN,IAAI,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;MAC3B,IAAItC,OAAO,GAAGtC,aAAa,CAAC4E,IAAI,CAAC;MACjC,OAAOlC,MAAM,CAACC,IAAI,CAACL,OAAO,CAAC,CAACvB,MAAM,GAAG,CAAC,EAAE;QACtC,MAAM6D,IAAI,GAAGK,KAAK,CAACC,KAAK,CAAC,CAAC;QAC1B,IAAIN,IAAI,IAAI,IAAI,EAAE,OAAOtC,OAAO;QAChCA,OAAO,GAAG6C,cAAc,CAACnF,aAAa,CAAC4E,IAAI,CAAC,EAAEtC,OAAO,CAAC;MACxD;MACA,OAAOA,OAAO;IAChB,CAAC,CAAC;IACF,OAAO;MACL,GAAGwC,WAAW;MACd,GAAGC;IACL,CAAC;EACH;EACAK,OAAOA,CAAC3D,KAAK,EAAEV,MAAM,EAAE;IACrB,MAAM,CAACqB,IAAI,EAAEiD,UAAU,CAAC,GAAG,IAAI,CAAC5E,MAAM,CAAC2B,IAAI,CAACX,KAAK,CAAC;IAClD,IAAIW,IAAI,EAAE;MACR,MAAMkD,UAAU,GAAGlD,IAAI,CAACrB,MAAM,CAAC,CAAC;MAChC,MAAMwE,YAAY,GAAGnD,IAAI,CAACrB,MAAM,CAAC,CAAC,IAAIsE,UAAU,GAAGtE,MAAM;MACzD,IAAIwE,YAAY,IAAI,EAAEF,UAAU,KAAK,CAAC,IAAItE,MAAM,KAAKuE,UAAU,CAAC,EAAE;QAChE,OAAOE,WAAW,CAACpD,IAAI,EAAEiD,UAAU,EAAEtE,MAAM,EAAE,IAAI,CAAC;MACpD;MACA,OAAOyE,WAAW,CAAC,IAAI,CAAC/E,MAAM,EAAEgB,KAAK,EAAEV,MAAM,EAAE,IAAI,CAAC;IACtD;IACA,OAAO,EAAE;EACX;EACA0E,OAAOA,CAAChE,KAAK,EAAEV,MAAM,EAAE;IACrB,OAAO,IAAI,CAACwD,WAAW,CAAC9C,KAAK,EAAEV,MAAM,CAAC,CAAC2E,MAAM,CAAChE,EAAE,IAAI,OAAOA,EAAE,CAACI,MAAM,KAAK,QAAQ,CAAC,CAACkD,GAAG,CAACtD,EAAE,IAAIA,EAAE,CAACI,MAAM,CAAC,CAAC6D,IAAI,CAAC,EAAE,CAAC;EAClH;EACAC,cAAcA,CAACnE,KAAK,EAAEoE,QAAQ,EAAE;IAC9B,MAAM5E,eAAe,GAAGC,cAAc,CAAC2E,QAAQ,CAAC;IAChD,MAAMC,MAAM,GAAG,IAAInG,KAAK,CAAC,CAAC,CAACoC,MAAM,CAACN,KAAK,CAAC,CAAC+C,MAAM,CAACvD,eAAe,CAAC;IAChE,IAAI,CAACR,MAAM,CAACmF,cAAc,CAACnE,KAAK,EAAER,eAAe,CAAC;IAClD,OAAO,IAAI,CAACJ,MAAM,CAACiF,MAAM,CAAC;EAC5B;EACAC,WAAWA,CAACtE,KAAK,EAAEuE,KAAK,EAAEjD,KAAK,EAAE;IAC/B,IAAI,CAACtC,MAAM,CAAC0B,QAAQ,CAACV,KAAK,EAAEuE,KAAK,EAAEjD,KAAK,CAAC;IACzC,OAAO,IAAI,CAAClC,MAAM,CAAC,IAAIlB,KAAK,CAAC,CAAC,CAACoC,MAAM,CAACN,KAAK,CAAC,CAACK,MAAM,CAAC;MAClD,CAACkE,KAAK,GAAGjD;IACX,CAAC,CAAC,CAAC;EACL;EACAkD,UAAUA,CAACxE,KAAK,EAAEO,IAAI,EAAE;IACtB,IAAIM,OAAO,GAAG0B,SAAS,CAACjD,MAAM,GAAG,CAAC,IAAIiD,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpFhC,IAAI,GAAGA,IAAI,CAACkE,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC;IACvD,IAAI,CAACzF,MAAM,CAAC0B,QAAQ,CAACV,KAAK,EAAEO,IAAI,CAAC;IACjCU,MAAM,CAACC,IAAI,CAACL,OAAO,CAAC,CAACe,OAAO,CAACa,MAAM,IAAI;MACrC,IAAI,CAACzD,MAAM,CAAC8C,QAAQ,CAAC9B,KAAK,EAAEO,IAAI,CAACjB,MAAM,EAAEmD,MAAM,EAAE5B,OAAO,CAAC4B,MAAM,CAAC,CAAC;IACnE,CAAC,CAAC;IACF,OAAO,IAAI,CAACrD,MAAM,CAAC,IAAIlB,KAAK,CAAC,CAAC,CAACoC,MAAM,CAACN,KAAK,CAAC,CAACK,MAAM,CAACE,IAAI,EAAE5C,SAAS,CAACkD,OAAO,CAAC,CAAC,CAAC;EAChF;EACA6D,OAAOA,CAAA,EAAG;IACR,IAAI,IAAI,CAAC1F,MAAM,CAAC2F,QAAQ,CAACrF,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;IAClD,IAAI,IAAI,CAACN,MAAM,CAAC2F,QAAQ,CAACrF,MAAM,GAAG,CAAC,EAAE,OAAO,KAAK;IACjD,MAAM6D,IAAI,GAAG,IAAI,CAACnE,MAAM,CAAC2F,QAAQ,CAACC,IAAI;IACtC,IAAIzB,IAAI,EAAE5B,OAAO,CAACsD,QAAQ,KAAKxG,KAAK,CAACwG,QAAQ,EAAE,OAAO,KAAK;IAC3D,MAAMC,KAAK,GAAG3B,IAAI;IAClB,IAAI2B,KAAK,CAACH,QAAQ,CAACrF,MAAM,GAAG,CAAC,EAAE,OAAO,KAAK;IAC3C,OAAOwF,KAAK,CAACH,QAAQ,CAACC,IAAI,YAAYpG,KAAK;EAC7C;EACAuG,YAAYA,CAAC/E,KAAK,EAAEV,MAAM,EAAE;IAC1B,MAAMiB,IAAI,GAAG,IAAI,CAACyD,OAAO,CAAChE,KAAK,EAAEV,MAAM,CAAC;IACxC,MAAM,CAACqB,IAAI,EAAEC,MAAM,CAAC,GAAG,IAAI,CAAC5B,MAAM,CAAC2B,IAAI,CAACX,KAAK,GAAGV,MAAM,CAAC;IACvD,IAAI0F,YAAY,GAAG,CAAC;IACpB,IAAIC,MAAM,GAAG,IAAI/G,KAAK,CAAC,CAAC;IACxB,IAAIyC,IAAI,IAAI,IAAI,EAAE;MAChBqE,YAAY,GAAGrE,IAAI,CAACrB,MAAM,CAAC,CAAC,GAAGsB,MAAM;MACrCqE,MAAM,GAAGtE,IAAI,CAAC1B,KAAK,CAAC,CAAC,CAACa,KAAK,CAACc,MAAM,EAAEA,MAAM,GAAGoE,YAAY,GAAG,CAAC,CAAC,CAAC3E,MAAM,CAAC,IAAI,CAAC;IAC7E;IACA,MAAM+D,QAAQ,GAAG,IAAI,CAACtB,WAAW,CAAC9C,KAAK,EAAEV,MAAM,GAAG0F,YAAY,CAAC;IAC/D,MAAMjE,IAAI,GAAGqD,QAAQ,CAACrD,IAAI,CAAC,IAAI7C,KAAK,CAAC,CAAC,CAACmC,MAAM,CAACE,IAAI,CAAC,CAACwC,MAAM,CAACkC,MAAM,CAAC,CAAC;IACnE,MAAMhG,KAAK,GAAG,IAAIf,KAAK,CAAC,CAAC,CAACoC,MAAM,CAACN,KAAK,CAAC,CAAC+C,MAAM,CAAChC,IAAI,CAAC;IACpD,OAAO,IAAI,CAAC5B,UAAU,CAACF,KAAK,CAAC;EAC/B;EACAG,MAAMA,CAACiF,MAAM,EAAE;IACb,IAAIa,SAAS,GAAG3C,SAAS,CAACjD,MAAM,GAAG,CAAC,IAAIiD,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;IACtF,IAAI4C,aAAa,GAAG5C,SAAS,CAACjD,MAAM,GAAG,CAAC,IAAIiD,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAGC,SAAS;IACjG,MAAM4C,QAAQ,GAAG,IAAI,CAACnG,KAAK;IAC3B,IAAIiG,SAAS,CAAC5F,MAAM,KAAK,CAAC,IAAI4F,SAAS,CAAC,CAAC,CAAC,CAACG,IAAI,KAAK,eAAe;IACnE;IACAH,SAAS,CAAC,CAAC,CAAC,CAACI,MAAM,CAACC,IAAI,CAACC,KAAK,CAAC3G,KAAK,CAAC,IAAI,IAAI,CAACG,MAAM,CAACyG,IAAI,CAACP,SAAS,CAAC,CAAC,CAAC,CAACI,MAAM,CAAC,EAAE;MAC9E;MACA,MAAMI,QAAQ,GAAG,IAAI,CAAC1G,MAAM,CAACyG,IAAI,CAACP,SAAS,CAAC,CAAC,CAAC,CAACI,MAAM,CAAC;MACtD,MAAMzE,OAAO,GAAGtC,aAAa,CAACmH,QAAQ,CAAC;MACvC,MAAM1F,KAAK,GAAG0F,QAAQ,CAAC9E,MAAM,CAAC,IAAI,CAAC5B,MAAM,CAAC;MAC1C;MACA,MAAM2G,QAAQ,GAAGT,SAAS,CAAC,CAAC,CAAC,CAACS,QAAQ,CAAClB,OAAO,CAAChG,UAAU,CAACmH,QAAQ,EAAE,EAAE,CAAC;MACvE,MAAMC,OAAO,GAAG,IAAI3H,KAAK,CAAC,CAAC,CAACmC,MAAM,CAACsF,QAAQ,CAAC;MAC5C;MACA,MAAMG,OAAO,GAAG,IAAI5H,KAAK,CAAC,CAAC,CAACmC,MAAM,CAACqF,QAAQ,CAACpE,KAAK,CAAC,CAAC,CAAC;MACpD,MAAMyE,qBAAqB,GAAGZ,aAAa,IAAI;QAC7Ca,QAAQ,EAAEC,UAAU,CAACd,aAAa,CAACa,QAAQ,EAAE,CAAChG,KAAK,CAAC;QACpDkG,QAAQ,EAAED,UAAU,CAACd,aAAa,CAACe,QAAQ,EAAE,CAAClG,KAAK;MACrD,CAAC;MACD,MAAMmG,SAAS,GAAG,IAAIjI,KAAK,CAAC,CAAC,CAACoC,MAAM,CAACN,KAAK,CAAC,CAAC+C,MAAM,CAAC8C,OAAO,CAAC9E,IAAI,CAAC+E,OAAO,EAAEC,qBAAqB,CAAC,CAAC;MAChG1B,MAAM,GAAG8B,SAAS,CAACpG,MAAM,CAAC,CAACd,KAAK,EAAEgB,EAAE,KAAK;QACvC,IAAIA,EAAE,CAACI,MAAM,EAAE;UACb,OAAOpB,KAAK,CAACoB,MAAM,CAACJ,EAAE,CAACI,MAAM,EAAEQ,OAAO,CAAC;QACzC;QACA,OAAO5B,KAAK,CAACyC,IAAI,CAACzB,EAAE,CAAC;MACvB,CAAC,EAAE,IAAI/B,KAAK,CAAC,CAAC,CAAC;MACf,IAAI,CAACe,KAAK,GAAGmG,QAAQ,CAACgB,OAAO,CAAC/B,MAAM,CAAC;IACvC,CAAC,MAAM;MACL,IAAI,CAACpF,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC5B,IAAI,CAACmF,MAAM,IAAI,CAACzG,OAAO,CAACwH,QAAQ,CAACgB,OAAO,CAAC/B,MAAM,CAAC,EAAE,IAAI,CAACpF,KAAK,CAAC,EAAE;QAC7DoF,MAAM,GAAGe,QAAQ,CAACrE,IAAI,CAAC,IAAI,CAAC9B,KAAK,EAAEkG,aAAa,CAAC;MACnD;IACF;IACA,OAAOd,MAAM;EACf;AACF;AACA,SAASgC,eAAeA,CAACC,KAAK,EAAEC,UAAU,EAAEC,KAAK,EAAE;EACjD,IAAIF,KAAK,CAAChH,MAAM,KAAK,CAAC,EAAE;IACtB,MAAM,CAACmH,MAAM,CAAC,GAAGC,WAAW,CAACF,KAAK,CAACG,GAAG,CAAC,CAAC,CAAC;IACzC,IAAIJ,UAAU,IAAI,CAAC,EAAE;MACnB,OAAQ,UAASE,MAAO,GAAE;IAC5B;IACA,OAAQ,UAASA,MAAO,IAAGJ,eAAe,CAAC,EAAE,EAAEE,UAAU,GAAG,CAAC,EAAEC,KAAK,CAAE,EAAC;EACzE;EACA,MAAM,CAAC;IACLI,KAAK;IACLhG,MAAM;IACNtB,MAAM;IACNuH,MAAM;IACNxB;EACF,CAAC,EAAE,GAAGyB,IAAI,CAAC,GAAGR,KAAK;EACnB,MAAM,CAACS,GAAG,EAAEC,SAAS,CAAC,GAAGN,WAAW,CAACrB,IAAI,CAAC;EAC1C,IAAIwB,MAAM,GAAGN,UAAU,EAAE;IACvBC,KAAK,CAAC9E,IAAI,CAAC2D,IAAI,CAAC;IAChB,IAAIwB,MAAM,KAAKN,UAAU,GAAG,CAAC,EAAE;MAC7B,OAAQ,IAAGQ,GAAI,OAAMC,SAAU,IAAGjD,WAAW,CAAC6C,KAAK,EAAEhG,MAAM,EAAEtB,MAAM,CAAE,GAAE+G,eAAe,CAACS,IAAI,EAAED,MAAM,EAAEL,KAAK,CAAE,EAAC;IAC/G;IACA,OAAQ,IAAGO,GAAI,QAAOV,eAAe,CAACC,KAAK,EAAEC,UAAU,GAAG,CAAC,EAAEC,KAAK,CAAE,EAAC;EACvE;EACA,MAAMS,YAAY,GAAGT,KAAK,CAACA,KAAK,CAAClH,MAAM,GAAG,CAAC,CAAC;EAC5C,IAAIuH,MAAM,KAAKN,UAAU,IAAIlB,IAAI,KAAK4B,YAAY,EAAE;IAClD,OAAQ,WAAUD,SAAU,IAAGjD,WAAW,CAAC6C,KAAK,EAAEhG,MAAM,EAAEtB,MAAM,CAAE,GAAE+G,eAAe,CAACS,IAAI,EAAED,MAAM,EAAEL,KAAK,CAAE,EAAC;EAC5G;EACA,MAAM,CAACC,MAAM,CAAC,GAAGC,WAAW,CAACF,KAAK,CAACG,GAAG,CAAC,CAAC,CAAC;EACzC,OAAQ,UAASF,MAAO,IAAGJ,eAAe,CAACC,KAAK,EAAEC,UAAU,GAAG,CAAC,EAAEC,KAAK,CAAE,EAAC;AAC5E;AACA,SAASzC,WAAWA,CAACZ,IAAI,EAAEnD,KAAK,EAAEV,MAAM,EAAE;EACxC,IAAI4H,MAAM,GAAG3E,SAAS,CAACjD,MAAM,GAAG,CAAC,IAAIiD,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;EACtF,IAAI,MAAM,IAAIY,IAAI,IAAI,OAAOA,IAAI,CAACgE,IAAI,KAAK,UAAU,EAAE;IACrD,OAAOhE,IAAI,CAACgE,IAAI,CAACnH,KAAK,EAAEV,MAAM,CAAC;EACjC;EACA,IAAI6D,IAAI,YAAYzE,QAAQ,EAAE;IAC5B,MAAM0I,WAAW,GAAGzI,UAAU,CAACwE,IAAI,CAAC7B,KAAK,CAAC,CAAC,CAACxB,KAAK,CAACE,KAAK,EAAEA,KAAK,GAAGV,MAAM,CAAC,CAAC;IACzE,OAAO8H,WAAW,CAACC,UAAU,CAAC,GAAG,EAAE,QAAQ,CAAC;EAC9C;EACA,IAAIlE,IAAI,YAAYlF,UAAU,EAAE;IAC9B;IACA,IAAIkF,IAAI,CAAC5B,OAAO,CAACsD,QAAQ,KAAK,gBAAgB,EAAE;MAC9C,MAAMyB,KAAK,GAAG,EAAE;MAChBnD,IAAI,CAACwB,QAAQ,CAAC2C,SAAS,CAACtH,KAAK,EAAEV,MAAM,EAAE,CAACsH,KAAK,EAAEhG,MAAM,EAAE2G,WAAW,KAAK;QACrE,MAAM1G,OAAO,GAAG,SAAS,IAAI+F,KAAK,IAAI,OAAOA,KAAK,CAAC/F,OAAO,KAAK,UAAU,GAAG+F,KAAK,CAAC/F,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;QAChGyF,KAAK,CAAC5E,IAAI,CAAC;UACTkF,KAAK;UACLhG,MAAM;UACNtB,MAAM,EAAEiI,WAAW;UACnBV,MAAM,EAAEhG,OAAO,CAACgG,MAAM,IAAI,CAAC;UAC3BxB,IAAI,EAAExE,OAAO,CAAC2G;QAChB,CAAC,CAAC;MACJ,CAAC,CAAC;MACF,OAAOnB,eAAe,CAACC,KAAK,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;IACvC;IACA,MAAMmB,KAAK,GAAG,EAAE;IAChBtE,IAAI,CAACwB,QAAQ,CAAC2C,SAAS,CAACtH,KAAK,EAAEV,MAAM,EAAE,CAACsH,KAAK,EAAEhG,MAAM,EAAE2G,WAAW,KAAK;MACrEE,KAAK,CAAC/F,IAAI,CAACqC,WAAW,CAAC6C,KAAK,EAAEhG,MAAM,EAAE2G,WAAW,CAAC,CAAC;IACrD,CAAC,CAAC;IACF,IAAIL,MAAM,IAAI/D,IAAI,CAAC5B,OAAO,CAACsD,QAAQ,KAAK,MAAM,EAAE;MAC9C,OAAO4C,KAAK,CAACvD,IAAI,CAAC,EAAE,CAAC;IACvB;IACA,MAAM;MACJwD,SAAS;MACTC;IACF,CAAC,GAAGxE,IAAI,CAACyE,OAAO;IAChB,MAAM,CAACC,KAAK,EAAEC,GAAG,CAAC,GAAGJ,SAAS,CAACK,KAAK,CAAE,IAAGJ,SAAU,GAAE,CAAC;IACtD;IACA,IAAIE,KAAK,KAAK,QAAQ,EAAE;MACtB,OAAQ,0CAAyCJ,KAAK,CAACvD,IAAI,CAAC,EAAE,CAAE,IAAG4D,GAAI,EAAC;IAC1E;IACA,OAAQ,GAAED,KAAM,IAAGJ,KAAK,CAACvD,IAAI,CAAC,EAAE,CAAE,IAAG4D,GAAI,EAAC;EAC5C;EACA,OAAO3E,IAAI,CAACyE,OAAO,YAAYI,OAAO,GAAG7E,IAAI,CAACyE,OAAO,CAACF,SAAS,GAAG,EAAE;AACtE;AACA,SAAShE,cAAcA,CAAC7C,OAAO,EAAEoH,QAAQ,EAAE;EACzC,OAAOhH,MAAM,CAACC,IAAI,CAAC+G,QAAQ,CAAC,CAAClI,MAAM,CAAC,CAACmI,MAAM,EAAErG,IAAI,KAAK;IACpD,IAAIhB,OAAO,CAACgB,IAAI,CAAC,IAAI,IAAI,EAAE,OAAOqG,MAAM;IACxC,MAAMC,aAAa,GAAGF,QAAQ,CAACpG,IAAI,CAAC;IACpC,IAAIsG,aAAa,KAAKtH,OAAO,CAACgB,IAAI,CAAC,EAAE;MACnCqG,MAAM,CAACrG,IAAI,CAAC,GAAGsG,aAAa;IAC9B,CAAC,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACF,aAAa,CAAC,EAAE;MACvC,IAAIA,aAAa,CAACG,OAAO,CAACzH,OAAO,CAACgB,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE;QAC5CqG,MAAM,CAACrG,IAAI,CAAC,GAAGsG,aAAa,CAACpF,MAAM,CAAC,CAAClC,OAAO,CAACgB,IAAI,CAAC,CAAC,CAAC;MACtD,CAAC,MAAM;QACL;QACAqG,MAAM,CAACrG,IAAI,CAAC,GAAGsG,aAAa;MAC9B;IACF,CAAC,MAAM;MACLD,MAAM,CAACrG,IAAI,CAAC,GAAG,CAACsG,aAAa,EAAEtH,OAAO,CAACgB,IAAI,CAAC,CAAC;IAC/C;IACA,OAAOqG,MAAM;EACf,CAAC,EAAE,CAAC,CAAC,CAAC;AACR;AACA,SAASxB,WAAWA,CAACrB,IAAI,EAAE;EACzB,MAAM0B,GAAG,GAAG1B,IAAI,KAAK,SAAS,GAAG,IAAI,GAAG,IAAI;EAC5C,QAAQA,IAAI;IACV,KAAK,SAAS;MACZ,OAAO,CAAC0B,GAAG,EAAE,sBAAsB,CAAC;IACtC,KAAK,WAAW;MACd,OAAO,CAACA,GAAG,EAAE,wBAAwB,CAAC;IACxC;MACE,OAAO,CAACA,GAAG,EAAE,EAAE,CAAC;EACpB;AACF;AACA,SAAStH,cAAcA,CAACR,KAAK,EAAE;EAC7B,OAAOA,KAAK,CAACc,MAAM,CAAC,CAACP,eAAe,EAAES,EAAE,KAAK;IAC3C,IAAI,OAAOA,EAAE,CAACI,MAAM,KAAK,QAAQ,EAAE;MACjC,MAAME,IAAI,GAAGN,EAAE,CAACI,MAAM,CAACoE,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC;MAClE,OAAOjF,eAAe,CAACa,MAAM,CAACE,IAAI,EAAEN,EAAE,CAACC,UAAU,CAAC;IACpD;IACA,OAAOV,eAAe,CAACkC,IAAI,CAACzB,EAAE,CAAC;EACjC,CAAC,EAAE,IAAI/B,KAAK,CAAC,CAAC,CAAC;AACjB;AACA,SAAS+H,UAAUA,CAACsC,IAAI,EAAEC,MAAM,EAAE;EAChC,IAAI;IACFxI,KAAK;IACLV;EACF,CAAC,GAAGiJ,IAAI;EACR,OAAO,IAAI3J,KAAK,CAACoB,KAAK,GAAGwI,MAAM,EAAElJ,MAAM,CAAC;AAC1C;AACA,SAASM,YAAYA,CAACC,GAAG,EAAE;EACzB,MAAMkI,KAAK,GAAG,EAAE;EAChBlI,GAAG,CAAC+B,OAAO,CAAC3B,EAAE,IAAI;IAChB,IAAI,OAAOA,EAAE,CAACI,MAAM,KAAK,QAAQ,EAAE;MACjC,MAAMqC,KAAK,GAAGzC,EAAE,CAACI,MAAM,CAAC0H,KAAK,CAAC,IAAI,CAAC;MACnCrF,KAAK,CAACd,OAAO,CAAC,CAACjB,IAAI,EAAEX,KAAK,KAAK;QAC7B,IAAIA,KAAK,EAAE+H,KAAK,CAACrG,IAAI,CAAC;UACpBrB,MAAM,EAAE,IAAI;UACZH,UAAU,EAAED,EAAE,CAACC;QACjB,CAAC,CAAC;QACF,IAAIS,IAAI,EAAEoH,KAAK,CAACrG,IAAI,CAAC;UACnBrB,MAAM,EAAEM,IAAI;UACZT,UAAU,EAAED,EAAE,CAACC;QACjB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,MAAM;MACL6H,KAAK,CAACrG,IAAI,CAACzB,EAAE,CAAC;IAChB;EACF,CAAC,CAAC;EACF,OAAO8H,KAAK;AACd;AACA,eAAejJ,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}