{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./add-or-edit-embedding.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./add-or-edit-embedding.component.css?ngResource\";\nimport { CommonModule } from '@angular/common';\nimport { Component, EventEmitter, Output } from '@angular/core';\nimport { FormsModule } from '@angular/forms';\nimport { NzModalModule, NzModalRef } from 'ng-zorro-antd/modal';\nimport { EmbeddingConfigServiceProxy, EmbeddingConfiguration } from '../../../../shared/service-proxies/service-proxies';\nlet AddOrEditEmbeddingComponent = class AddOrEditEmbeddingComponent {\n  constructor(_embeddingService, modal) {\n    this._embeddingService = _embeddingService;\n    this.modal = modal;\n    this.isUpdating = false;\n    this.apiConfig = new EmbeddingConfiguration();\n    this.save = new EventEmitter();\n    this.cancel = new EventEmitter();\n  }\n  ngOnInit() {\n    // Get the data passed from the modal\n    const modalData = this.modal.getConfig().nzData;\n    if (modalData) {\n      this.isUpdating = modalData.isUpdating;\n      this.apiConfig = modalData.apiConfig;\n      // Set isActive to false for new configurations\n      if (!this.isUpdating) {\n        this.apiConfig.isActive = false;\n      }\n      console.log('Modal Data:', modalData);\n    }\n  }\n  onSubmit() {\n    const config = new EmbeddingConfiguration({\n      ...this.apiConfig,\n      isActive: Boolean(this.apiConfig.isActive)\n    });\n    if (this.isUpdating) {\n      this._embeddingService.createEmbeddingConfig(config).subscribe(res => {\n        console.log(res);\n        this.save.emit(res);\n      }, error => {\n        console.error('Failed to update embedding configuration:', error);\n      });\n    } else {\n      this._embeddingService.createEmbeddingConfig(config).subscribe(res => {\n        console.log(res);\n        this.save.emit(res);\n      }, error => {\n        console.error('Failed to create embedding configuration:', error);\n      });\n    }\n  }\n  onCancel() {\n    this.cancel.emit();\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: EmbeddingConfigServiceProxy\n    }, {\n      type: NzModalRef\n    }];\n  }\n  static {\n    this.propDecorators = {\n      save: [{\n        type: Output\n      }],\n      cancel: [{\n        type: Output\n      }]\n    };\n  }\n};\nAddOrEditEmbeddingComponent = __decorate([Component({\n  selector: 'app-add-or-edit-embedding',\n  standalone: true,\n  imports: [CommonModule, FormsModule, NzModalModule],\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], AddOrEditEmbeddingComponent);\nexport { AddOrEditEmbeddingComponent };", "map": {"version": 3, "names": ["CommonModule", "Component", "EventEmitter", "Output", "FormsModule", "NzModalModule", "NzModalRef", "EmbeddingConfigServiceProxy", "EmbeddingConfiguration", "AddOrEditEmbeddingComponent", "constructor", "_embeddingService", "modal", "isUpdating", "apiConfig", "save", "cancel", "ngOnInit", "modalData", "getConfig", "nzData", "isActive", "console", "log", "onSubmit", "config", "Boolean", "createEmbeddingConfig", "subscribe", "res", "emit", "error", "onCancel", "__decorate", "selector", "standalone", "imports", "template", "__NG_CLI_RESOURCE__0"], "sources": ["C:\\Users\\<USER>\\source\\ai-hub\\WebApp\\src\\app\\admin\\embedding\\add-or-edit-embedding\\add-or-edit-embedding.component.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\r\nimport { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { NzModalModule, NzModalRef } from 'ng-zorro-antd/modal';\r\nimport { EmbeddingConfigServiceProxy, EmbeddingConfiguration } from '../../../../shared/service-proxies/service-proxies';\r\nimport { DateTime } from 'luxon';\r\n\r\n@Component({\r\n  selector: 'app-add-or-edit-embedding',\r\n  standalone: true,\r\n  imports: [CommonModule, FormsModule, NzModalModule],\r\n  templateUrl: './add-or-edit-embedding.component.html',\r\n  styleUrl: './add-or-edit-embedding.component.css'\r\n})\r\nexport class AddOrEditEmbeddingComponent implements OnInit {\r\n  isUpdating = false;\r\n  apiConfig: EmbeddingConfiguration = new EmbeddingConfiguration();\r\n  @Output() save = new EventEmitter<any>();\r\n  @Output() cancel = new EventEmitter<void>();\r\n\r\n  constructor(\r\n    private _embeddingService: EmbeddingConfigServiceProxy,\r\n    private modal: NzModalRef\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n    // Get the data passed from the modal\r\n    const modalData = this.modal.getConfig().nzData;\r\n    if (modalData) {\r\n      this.isUpdating = modalData.isUpdating;\r\n      this.apiConfig = modalData.apiConfig;\r\n      // Set isActive to false for new configurations\r\n      if (!this.isUpdating) {\r\n        this.apiConfig.isActive = false;\r\n      }\r\n      console.log('Modal Data:', modalData);\r\n    }\r\n  }\r\n\r\n  onSubmit() {\r\n    const config = new EmbeddingConfiguration({\r\n      ...this.apiConfig,\r\n      isActive: Boolean(this.apiConfig.isActive)\r\n    });\r\n\r\n    if (this.isUpdating) {\r\n      this._embeddingService.createEmbeddingConfig(config).subscribe((res: any) => {\r\n        console.log(res);\r\n        this.save.emit(res);\r\n      }, (error: any) => {\r\n        console.error('Failed to update embedding configuration:', error);\r\n      });\r\n    } else {\r\n      this._embeddingService.createEmbeddingConfig(config).subscribe((res: any) => {\r\n        console.log(res);\r\n        this.save.emit(res);\r\n      }, (error: any) => {\r\n        console.error('Failed to create embedding configuration:', error);\r\n      });\r\n    }\r\n  }\r\n\r\n  onCancel() {\r\n    this.cancel.emit();\r\n  }\r\n}\r\n"], "mappings": ";;;AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,SAAS,EAAEC,YAAY,EAASC,MAAM,QAAgB,eAAe;AAC9E,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,aAAa,EAAEC,UAAU,QAAQ,qBAAqB;AAC/D,SAASC,2BAA2B,EAAEC,sBAAsB,QAAQ,oDAAoD;AAUjH,IAAMC,2BAA2B,GAAjC,MAAMA,2BAA2B;EAMtCC,YACUC,iBAA8C,EAC9CC,KAAiB;IADjB,KAAAD,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,KAAK,GAALA,KAAK;IAPf,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,SAAS,GAA2B,IAAIN,sBAAsB,EAAE;IACtD,KAAAO,IAAI,GAAG,IAAIb,YAAY,EAAO;IAC9B,KAAAc,MAAM,GAAG,IAAId,YAAY,EAAQ;EAKvC;EAEJe,QAAQA,CAAA;IACN;IACA,MAAMC,SAAS,GAAG,IAAI,CAACN,KAAK,CAACO,SAAS,EAAE,CAACC,MAAM;IAC/C,IAAIF,SAAS,EAAE;MACb,IAAI,CAACL,UAAU,GAAGK,SAAS,CAACL,UAAU;MACtC,IAAI,CAACC,SAAS,GAAGI,SAAS,CAACJ,SAAS;MACpC;MACA,IAAI,CAAC,IAAI,CAACD,UAAU,EAAE;QACpB,IAAI,CAACC,SAAS,CAACO,QAAQ,GAAG,KAAK;;MAEjCC,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEL,SAAS,CAAC;;EAEzC;EAEAM,QAAQA,CAAA;IACN,MAAMC,MAAM,GAAG,IAAIjB,sBAAsB,CAAC;MACxC,GAAG,IAAI,CAACM,SAAS;MACjBO,QAAQ,EAAEK,OAAO,CAAC,IAAI,CAACZ,SAAS,CAACO,QAAQ;KAC1C,CAAC;IAEF,IAAI,IAAI,CAACR,UAAU,EAAE;MACnB,IAAI,CAACF,iBAAiB,CAACgB,qBAAqB,CAACF,MAAM,CAAC,CAACG,SAAS,CAAEC,GAAQ,IAAI;QAC1EP,OAAO,CAACC,GAAG,CAACM,GAAG,CAAC;QAChB,IAAI,CAACd,IAAI,CAACe,IAAI,CAACD,GAAG,CAAC;MACrB,CAAC,EAAGE,KAAU,IAAI;QAChBT,OAAO,CAACS,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACnE,CAAC,CAAC;KACH,MAAM;MACL,IAAI,CAACpB,iBAAiB,CAACgB,qBAAqB,CAACF,MAAM,CAAC,CAACG,SAAS,CAAEC,GAAQ,IAAI;QAC1EP,OAAO,CAACC,GAAG,CAACM,GAAG,CAAC;QAChB,IAAI,CAACd,IAAI,CAACe,IAAI,CAACD,GAAG,CAAC;MACrB,CAAC,EAAGE,KAAU,IAAI;QAChBT,OAAO,CAACS,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACnE,CAAC,CAAC;;EAEN;EAEAC,QAAQA,CAAA;IACN,IAAI,CAAChB,MAAM,CAACc,IAAI,EAAE;EACpB;;;;;;;;;;;cA/CC3B;MAAM;;cACNA;MAAM;;;;AAJIM,2BAA2B,GAAAwB,UAAA,EAPvChC,SAAS,CAAC;EACTiC,QAAQ,EAAE,2BAA2B;EACrCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACpC,YAAY,EAAEI,WAAW,EAAEC,aAAa,CAAC;EACnDgC,QAAA,EAAAC,oBAAqD;;CAEtD,CAAC,C,EACW7B,2BAA2B,CAmDvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}