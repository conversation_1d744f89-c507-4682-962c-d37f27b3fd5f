{"ast": null, "code": "import { createErrorClass } from './createErrorClass';\nexport const ObjectUnsubscribedError = createErrorClass(_super => function ObjectUnsubscribedErrorImpl() {\n  _super(this);\n  this.name = 'ObjectUnsubscribedError';\n  this.message = 'object unsubscribed';\n});", "map": {"version": 3, "names": ["createErrorClass", "ObjectUnsubscribedError", "_super", "ObjectUnsubscribedErrorImpl", "name", "message"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/rxjs/dist/esm/internal/util/ObjectUnsubscribedError.js"], "sourcesContent": ["import { createErrorClass } from './createErrorClass';\nexport const ObjectUnsubscribedError = createErrorClass((_super) => function ObjectUnsubscribedErrorImpl() {\n    _super(this);\n    this.name = 'ObjectUnsubscribedError';\n    this.message = 'object unsubscribed';\n});\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,oBAAoB;AACrD,OAAO,MAAMC,uBAAuB,GAAGD,gBAAgB,CAAEE,MAAM,IAAK,SAASC,2BAA2BA,CAAA,EAAG;EACvGD,MAAM,CAAC,IAAI,CAAC;EACZ,IAAI,CAACE,IAAI,GAAG,yBAAyB;EACrC,IAAI,CAACC,OAAO,GAAG,qBAAqB;AACxC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}