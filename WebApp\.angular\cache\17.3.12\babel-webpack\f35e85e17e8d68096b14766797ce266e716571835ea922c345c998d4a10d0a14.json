{"ast": null, "code": "import { ParentBlot } from 'parchment';\nimport Module from '../core/module.js';\nimport Quill from '../core/quill.js';\nconst isMac = /Mac/i.test(navigator.platform);\n\n// Export for testing\nexport const TTL_FOR_VALID_SELECTION_CHANGE = 100;\n\n// A loose check to determine if the shortcut can move the caret before a UI node:\n// <ANY_PARENT>[CARET]<div class=\"ql-ui\"></div>[CONTENT]</ANY_PARENT>\nconst canMoveCaretBeforeUINode = event => {\n  if (event.key === 'ArrowLeft' || event.key === 'ArrowRight' ||\n  // RTL scripts or moving from the end of the previous line\n  event.key === 'ArrowUp' || event.key === 'ArrowDown' || event.key === 'Home') {\n    return true;\n  }\n  if (isMac && event.key === 'a' && event.ctrlKey === true) {\n    return true;\n  }\n  return false;\n};\nclass UINode extends Module {\n  isListening = false;\n  selectionChangeDeadline = 0;\n  constructor(quill, options) {\n    super(quill, options);\n    this.handleArrowKeys();\n    this.handleNavigationShortcuts();\n  }\n  handleArrowKeys() {\n    this.quill.keyboard.addBinding({\n      key: ['ArrowLeft', 'ArrowRight'],\n      offset: 0,\n      shiftKey: null,\n      handler(range, _ref) {\n        let {\n          line,\n          event\n        } = _ref;\n        if (!(line instanceof ParentBlot) || !line.uiNode) {\n          return true;\n        }\n        const isRTL = getComputedStyle(line.domNode)['direction'] === 'rtl';\n        if (isRTL && event.key !== 'ArrowRight' || !isRTL && event.key !== 'ArrowLeft') {\n          return true;\n        }\n        this.quill.setSelection(range.index - 1, range.length + (event.shiftKey ? 1 : 0), Quill.sources.USER);\n        return false;\n      }\n    });\n  }\n  handleNavigationShortcuts() {\n    this.quill.root.addEventListener('keydown', event => {\n      if (!event.defaultPrevented && canMoveCaretBeforeUINode(event)) {\n        this.ensureListeningToSelectionChange();\n      }\n    });\n  }\n\n  /**\n   * We only listen to the `selectionchange` event when\n   * there is an intention of moving the caret to the beginning using shortcuts.\n   * This is primarily implemented to prevent infinite loops, as we are changing\n   * the selection within the handler of a `selectionchange` event.\n   */\n  ensureListeningToSelectionChange() {\n    this.selectionChangeDeadline = Date.now() + TTL_FOR_VALID_SELECTION_CHANGE;\n    if (this.isListening) return;\n    this.isListening = true;\n    const listener = () => {\n      this.isListening = false;\n      if (Date.now() <= this.selectionChangeDeadline) {\n        this.handleSelectionChange();\n      }\n    };\n    document.addEventListener('selectionchange', listener, {\n      once: true\n    });\n  }\n  handleSelectionChange() {\n    const selection = document.getSelection();\n    if (!selection) return;\n    const range = selection.getRangeAt(0);\n    if (range.collapsed !== true || range.startOffset !== 0) return;\n    const line = this.quill.scroll.find(range.startContainer);\n    if (!(line instanceof ParentBlot) || !line.uiNode) return;\n    const newRange = document.createRange();\n    newRange.setStartAfter(line.uiNode);\n    newRange.setEndAfter(line.uiNode);\n    selection.removeAllRanges();\n    selection.addRange(newRange);\n  }\n}\nexport default UINode;", "map": {"version": 3, "names": ["ParentBlot", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "isMac", "test", "navigator", "platform", "TTL_FOR_VALID_SELECTION_CHANGE", "canMoveCaretBeforeUINode", "event", "key", "ctrl<PERSON>ey", "UINode", "isListening", "selectionChangeDeadline", "constructor", "quill", "options", "handleArrowKeys", "handleNavigationShortcuts", "keyboard", "addBinding", "offset", "shift<PERSON>ey", "handler", "range", "_ref", "line", "uiNode", "isRTL", "getComputedStyle", "domNode", "setSelection", "index", "length", "sources", "USER", "root", "addEventListener", "defaultPrevented", "ensureListeningToSelectionChange", "Date", "now", "listener", "handleSelectionChange", "document", "once", "selection", "getSelection", "getRangeAt", "collapsed", "startOffset", "scroll", "find", "startContainer", "newRange", "createRange", "setStartAfter", "setEndAfter", "removeAllRanges", "addRange"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/quill/modules/uiNode.js"], "sourcesContent": ["import { ParentBlot } from 'parchment';\nimport Module from '../core/module.js';\nimport Quill from '../core/quill.js';\nconst isMac = /Mac/i.test(navigator.platform);\n\n// Export for testing\nexport const TTL_FOR_VALID_SELECTION_CHANGE = 100;\n\n// A loose check to determine if the shortcut can move the caret before a UI node:\n// <ANY_PARENT>[CARET]<div class=\"ql-ui\"></div>[CONTENT]</ANY_PARENT>\nconst canMoveCaretBeforeUINode = event => {\n  if (event.key === 'ArrowLeft' || event.key === 'ArrowRight' ||\n  // RTL scripts or moving from the end of the previous line\n  event.key === 'ArrowUp' || event.key === 'ArrowDown' || event.key === 'Home') {\n    return true;\n  }\n  if (isMac && event.key === 'a' && event.ctrlKey === true) {\n    return true;\n  }\n  return false;\n};\nclass UINode extends Module {\n  isListening = false;\n  selectionChangeDeadline = 0;\n  constructor(quill, options) {\n    super(quill, options);\n    this.handleArrowKeys();\n    this.handleNavigationShortcuts();\n  }\n  handleArrowKeys() {\n    this.quill.keyboard.addBinding({\n      key: ['ArrowLeft', 'ArrowRight'],\n      offset: 0,\n      shiftKey: null,\n      handler(range, _ref) {\n        let {\n          line,\n          event\n        } = _ref;\n        if (!(line instanceof ParentBlot) || !line.uiNode) {\n          return true;\n        }\n        const isRTL = getComputedStyle(line.domNode)['direction'] === 'rtl';\n        if (isRTL && event.key !== 'ArrowRight' || !isRTL && event.key !== 'ArrowLeft') {\n          return true;\n        }\n        this.quill.setSelection(range.index - 1, range.length + (event.shiftKey ? 1 : 0), Quill.sources.USER);\n        return false;\n      }\n    });\n  }\n  handleNavigationShortcuts() {\n    this.quill.root.addEventListener('keydown', event => {\n      if (!event.defaultPrevented && canMoveCaretBeforeUINode(event)) {\n        this.ensureListeningToSelectionChange();\n      }\n    });\n  }\n\n  /**\n   * We only listen to the `selectionchange` event when\n   * there is an intention of moving the caret to the beginning using shortcuts.\n   * This is primarily implemented to prevent infinite loops, as we are changing\n   * the selection within the handler of a `selectionchange` event.\n   */\n  ensureListeningToSelectionChange() {\n    this.selectionChangeDeadline = Date.now() + TTL_FOR_VALID_SELECTION_CHANGE;\n    if (this.isListening) return;\n    this.isListening = true;\n    const listener = () => {\n      this.isListening = false;\n      if (Date.now() <= this.selectionChangeDeadline) {\n        this.handleSelectionChange();\n      }\n    };\n    document.addEventListener('selectionchange', listener, {\n      once: true\n    });\n  }\n  handleSelectionChange() {\n    const selection = document.getSelection();\n    if (!selection) return;\n    const range = selection.getRangeAt(0);\n    if (range.collapsed !== true || range.startOffset !== 0) return;\n    const line = this.quill.scroll.find(range.startContainer);\n    if (!(line instanceof ParentBlot) || !line.uiNode) return;\n    const newRange = document.createRange();\n    newRange.setStartAfter(line.uiNode);\n    newRange.setEndAfter(line.uiNode);\n    selection.removeAllRanges();\n    selection.addRange(newRange);\n  }\n}\nexport default UINode;\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,WAAW;AACtC,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,KAAK,MAAM,kBAAkB;AACpC,MAAMC,KAAK,GAAG,MAAM,CAACC,IAAI,CAACC,SAAS,CAACC,QAAQ,CAAC;;AAE7C;AACA,OAAO,MAAMC,8BAA8B,GAAG,GAAG;;AAEjD;AACA;AACA,MAAMC,wBAAwB,GAAGC,KAAK,IAAI;EACxC,IAAIA,KAAK,CAACC,GAAG,KAAK,WAAW,IAAID,KAAK,CAACC,GAAG,KAAK,YAAY;EAC3D;EACAD,KAAK,CAACC,GAAG,KAAK,SAAS,IAAID,KAAK,CAACC,GAAG,KAAK,WAAW,IAAID,KAAK,CAACC,GAAG,KAAK,MAAM,EAAE;IAC5E,OAAO,IAAI;EACb;EACA,IAAIP,KAAK,IAAIM,KAAK,CAACC,GAAG,KAAK,GAAG,IAAID,KAAK,CAACE,OAAO,KAAK,IAAI,EAAE;IACxD,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd,CAAC;AACD,MAAMC,MAAM,SAASX,MAAM,CAAC;EAC1BY,WAAW,GAAG,KAAK;EACnBC,uBAAuB,GAAG,CAAC;EAC3BC,WAAWA,CAACC,KAAK,EAAEC,OAAO,EAAE;IAC1B,KAAK,CAACD,KAAK,EAAEC,OAAO,CAAC;IACrB,IAAI,CAACC,eAAe,CAAC,CAAC;IACtB,IAAI,CAACC,yBAAyB,CAAC,CAAC;EAClC;EACAD,eAAeA,CAAA,EAAG;IAChB,IAAI,CAACF,KAAK,CAACI,QAAQ,CAACC,UAAU,CAAC;MAC7BX,GAAG,EAAE,CAAC,WAAW,EAAE,YAAY,CAAC;MAChCY,MAAM,EAAE,CAAC;MACTC,QAAQ,EAAE,IAAI;MACdC,OAAOA,CAACC,KAAK,EAAEC,IAAI,EAAE;QACnB,IAAI;UACFC,IAAI;UACJlB;QACF,CAAC,GAAGiB,IAAI;QACR,IAAI,EAAEC,IAAI,YAAY3B,UAAU,CAAC,IAAI,CAAC2B,IAAI,CAACC,MAAM,EAAE;UACjD,OAAO,IAAI;QACb;QACA,MAAMC,KAAK,GAAGC,gBAAgB,CAACH,IAAI,CAACI,OAAO,CAAC,CAAC,WAAW,CAAC,KAAK,KAAK;QACnE,IAAIF,KAAK,IAAIpB,KAAK,CAACC,GAAG,KAAK,YAAY,IAAI,CAACmB,KAAK,IAAIpB,KAAK,CAACC,GAAG,KAAK,WAAW,EAAE;UAC9E,OAAO,IAAI;QACb;QACA,IAAI,CAACM,KAAK,CAACgB,YAAY,CAACP,KAAK,CAACQ,KAAK,GAAG,CAAC,EAAER,KAAK,CAACS,MAAM,IAAIzB,KAAK,CAACc,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC,EAAErB,KAAK,CAACiC,OAAO,CAACC,IAAI,CAAC;QACrG,OAAO,KAAK;MACd;IACF,CAAC,CAAC;EACJ;EACAjB,yBAAyBA,CAAA,EAAG;IAC1B,IAAI,CAACH,KAAK,CAACqB,IAAI,CAACC,gBAAgB,CAAC,SAAS,EAAE7B,KAAK,IAAI;MACnD,IAAI,CAACA,KAAK,CAAC8B,gBAAgB,IAAI/B,wBAAwB,CAACC,KAAK,CAAC,EAAE;QAC9D,IAAI,CAAC+B,gCAAgC,CAAC,CAAC;MACzC;IACF,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEA,gCAAgCA,CAAA,EAAG;IACjC,IAAI,CAAC1B,uBAAuB,GAAG2B,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGnC,8BAA8B;IAC1E,IAAI,IAAI,CAACM,WAAW,EAAE;IACtB,IAAI,CAACA,WAAW,GAAG,IAAI;IACvB,MAAM8B,QAAQ,GAAGA,CAAA,KAAM;MACrB,IAAI,CAAC9B,WAAW,GAAG,KAAK;MACxB,IAAI4B,IAAI,CAACC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC5B,uBAAuB,EAAE;QAC9C,IAAI,CAAC8B,qBAAqB,CAAC,CAAC;MAC9B;IACF,CAAC;IACDC,QAAQ,CAACP,gBAAgB,CAAC,iBAAiB,EAAEK,QAAQ,EAAE;MACrDG,IAAI,EAAE;IACR,CAAC,CAAC;EACJ;EACAF,qBAAqBA,CAAA,EAAG;IACtB,MAAMG,SAAS,GAAGF,QAAQ,CAACG,YAAY,CAAC,CAAC;IACzC,IAAI,CAACD,SAAS,EAAE;IAChB,MAAMtB,KAAK,GAAGsB,SAAS,CAACE,UAAU,CAAC,CAAC,CAAC;IACrC,IAAIxB,KAAK,CAACyB,SAAS,KAAK,IAAI,IAAIzB,KAAK,CAAC0B,WAAW,KAAK,CAAC,EAAE;IACzD,MAAMxB,IAAI,GAAG,IAAI,CAACX,KAAK,CAACoC,MAAM,CAACC,IAAI,CAAC5B,KAAK,CAAC6B,cAAc,CAAC;IACzD,IAAI,EAAE3B,IAAI,YAAY3B,UAAU,CAAC,IAAI,CAAC2B,IAAI,CAACC,MAAM,EAAE;IACnD,MAAM2B,QAAQ,GAAGV,QAAQ,CAACW,WAAW,CAAC,CAAC;IACvCD,QAAQ,CAACE,aAAa,CAAC9B,IAAI,CAACC,MAAM,CAAC;IACnC2B,QAAQ,CAACG,WAAW,CAAC/B,IAAI,CAACC,MAAM,CAAC;IACjCmB,SAAS,CAACY,eAAe,CAAC,CAAC;IAC3BZ,SAAS,CAACa,QAAQ,CAACL,QAAQ,CAAC;EAC9B;AACF;AACA,eAAe3C,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}