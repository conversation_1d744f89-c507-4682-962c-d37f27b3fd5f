{"ast": null, "code": "// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\nexport { AbortError, HttpError, TimeoutError } from \"./Errors\";\nexport { HttpClient, HttpResponse } from \"./HttpClient\";\nexport { DefaultHttpClient } from \"./DefaultHttpClient\";\nexport { HubConnection, HubConnectionState } from \"./HubConnection\";\nexport { HubConnectionBuilder } from \"./HubConnectionBuilder\";\nexport { MessageType } from \"./IHubProtocol\";\nexport { LogLevel } from \"./ILogger\";\nexport { HttpTransportType, TransferFormat } from \"./ITransport\";\nexport { NullLogger } from \"./Loggers\";\nexport { JsonHubProtocol } from \"./JsonHubProtocol\";\nexport { Subject } from \"./Subject\";\nexport { VERSION } from \"./Utils\";", "map": {"version": 3, "names": ["AbortError", "HttpError", "TimeoutError", "HttpClient", "HttpResponse", "DefaultHttpClient", "HubConnection", "HubConnectionState", "HubConnectionBuilder", "MessageType", "LogLevel", "HttpTransportType", "TransferFormat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "JsonHubProtocol", "Subject", "VERSION"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/@microsoft/signalr/dist/esm/index.js"], "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\nexport { AbortError, HttpError, TimeoutError } from \"./Errors\";\r\nexport { HttpClient, HttpResponse } from \"./HttpClient\";\r\nexport { DefaultHttpClient } from \"./DefaultHttpClient\";\r\nexport { HubConnection, HubConnectionState } from \"./HubConnection\";\r\nexport { HubConnectionBuilder } from \"./HubConnectionBuilder\";\r\nexport { MessageType } from \"./IHubProtocol\";\r\nexport { LogLevel } from \"./ILogger\";\r\nexport { HttpTransportType, TransferFormat } from \"./ITransport\";\r\nexport { NullLogger } from \"./Loggers\";\r\nexport { JsonHubProtocol } from \"./JsonHubProtocol\";\r\nexport { Subject } from \"./Subject\";\r\nexport { VERSION } from \"./Utils\";\r\n"], "mappings": "AAAA;AACA;AACA,SAASA,UAAU,EAAEC,SAAS,EAAEC,YAAY,QAAQ,UAAU;AAC9D,SAASC,UAAU,EAAEC,YAAY,QAAQ,cAAc;AACvD,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,aAAa,EAAEC,kBAAkB,QAAQ,iBAAiB;AACnE,SAASC,oBAAoB,QAAQ,wBAAwB;AAC7D,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,QAAQ,QAAQ,WAAW;AACpC,SAASC,iBAAiB,EAAEC,cAAc,QAAQ,cAAc;AAChE,SAASC,UAAU,QAAQ,WAAW;AACtC,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,OAAO,QAAQ,WAAW;AACnC,SAASC,OAAO,QAAQ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}