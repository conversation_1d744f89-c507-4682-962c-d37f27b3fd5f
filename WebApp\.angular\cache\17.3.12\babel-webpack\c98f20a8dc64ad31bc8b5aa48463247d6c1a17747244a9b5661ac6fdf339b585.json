{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/source/ai-hub/WebApp/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { TestBed } from '@angular/core/testing';\nimport { DocsComponent } from './docs.component';\ndescribe('DocsComponent', () => {\n  let component;\n  let fixture;\n  beforeEach( /*#__PURE__*/_asyncToGenerator(function* () {\n    yield TestBed.configureTestingModule({\n      imports: [DocsComponent]\n    }).compileComponents();\n    fixture = TestBed.createComponent(DocsComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  }));\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "DocsComponent", "describe", "component", "fixture", "beforeEach", "_asyncToGenerator", "configureTestingModule", "imports", "compileComponents", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["C:\\Users\\<USER>\\source\\ai-hub\\WebApp\\src\\app\\MyNotesProjects\\docs\\docs.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed } from '@angular/core/testing';\r\n\r\nimport { DocsComponent } from './docs.component';\r\n\r\ndescribe('DocsComponent', () => {\r\n  let component: DocsComponent;\r\n  let fixture: ComponentFixture<DocsComponent>;\r\n\r\n  beforeEach(async () => {\r\n    await TestBed.configureTestingModule({\r\n      imports: [DocsComponent]\r\n    })\r\n    .compileComponents();\r\n\r\n    fixture = TestBed.createComponent(DocsComponent);\r\n    component = fixture.componentInstance;\r\n    fixture.detectChanges();\r\n  });\r\n\r\n  it('should create', () => {\r\n    expect(component).toBeTruthy();\r\n  });\r\n});\r\n"], "mappings": ";AAAA,SAA2BA,OAAO,QAAQ,uBAAuB;AAEjE,SAASC,aAAa,QAAQ,kBAAkB;AAEhDC,QAAQ,CAAC,eAAe,EAAE,MAAK;EAC7B,IAAIC,SAAwB;EAC5B,IAAIC,OAAwC;EAE5CC,UAAU,eAAAC,iBAAA,CAAC,aAAW;IACpB,MAAMN,OAAO,CAACO,sBAAsB,CAAC;MACnCC,OAAO,EAAE,CAACP,aAAa;KACxB,CAAC,CACDQ,iBAAiB,EAAE;IAEpBL,OAAO,GAAGJ,OAAO,CAACU,eAAe,CAACT,aAAa,CAAC;IAChDE,SAAS,GAAGC,OAAO,CAACO,iBAAiB;IACrCP,OAAO,CAACQ,aAAa,EAAE;EACzB,CAAC,EAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACX,SAAS,CAAC,CAACY,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}