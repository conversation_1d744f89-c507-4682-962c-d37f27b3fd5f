{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/source/ai-hub/WebApp/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./document-list.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./document-list.component.css?ngResource\";\nimport { Component, ViewChild } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NotesService } from '../services/notes.service';\nimport { MarkdownModule } from 'ngx-markdown';\nimport { ActivatedRoute, Router } from '@angular/router';\nlet DocumentListComponent = class DocumentListComponent {\n  constructor(notesService, router, route) {\n    this.notesService = notesService;\n    this.router = router;\n    this.route = route;\n    this.isToogled = false;\n    this.wordCount = 0;\n    this.markdownContent = '';\n    this.isSaving = false;\n    this.isLoading = false;\n    this.isGenerating = false;\n    this.generateModalVisible = false;\n    this.aiPrompt = '';\n    this.contentType = 'article';\n    this.useCurrentContent = false;\n    this.chatMessages = [{\n      text: \"Hello! How can I help you today?\",\n      isUser: false,\n      timestamp: new Date()\n    }];\n    this.selectedOption = 'article';\n    this.documents = [];\n    // Add title property\n    this.noteTitle = '';\n    this.currentNoteId = null;\n    this.isDocsOpen = false; // Set default to false to hide docs section\n    this.showDeleteModal = false;\n    this.noteToDelete = null;\n    this.sessionId = '';\n    this.hasContent = false;\n    this.isjournal = false;\n    // Pagination properties\n    this.currentPage = 1;\n    this.totalPages = 1;\n    this.pageSize = 15;\n  }\n  ngOnInit() {\n    this.sessionId = crypto.randomUUID();\n    const currentUrl = this.router.url;\n    if (currentUrl.includes('journal')) {\n      this.isjournal = true;\n      console.log('Journal detected in URL');\n    }\n    this.loadNotes();\n  }\n  createNewDocument() {\n    this.router.navigate(['/editor']);\n  }\n  loadNotes() {\n    this.isLoading = true;\n    this.notesService.getAllNotes().subscribe({\n      next: notes => {\n        this.documents = notes;\n        this.totalPages = Math.ceil(this.documents.length / this.pageSize);\n        this.isLoading = false;\n      },\n      error: error => {\n        console.error('Error loading notes:', error);\n        this.isLoading = false;\n      }\n    });\n  }\n  get paginatedDocuments() {\n    const startIndex = (this.currentPage - 1) * this.pageSize;\n    const endIndex = startIndex + this.pageSize;\n    return this.documents.slice(startIndex, endIndex);\n  }\n  deleteDocument(id, event) {\n    event.stopPropagation();\n    this.noteToDelete = id;\n    this.showDeleteModal = true;\n  }\n  editDocument(doc, event) {\n    event.stopPropagation();\n    this.router.navigate(['editor', doc.id]);\n  }\n  viewDocument(doc, event) {\n    event.stopPropagation();\n    this.router.navigate(['document', doc.id]);\n  }\n  toggleDocs() {\n    this.isDocsOpen = !this.isDocsOpen;\n  }\n  confirmDelete() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (_this.noteToDelete) {\n        try {\n          yield _this.notesService.deleteNote(_this.noteToDelete).toPromise();\n          _this.loadNotes();\n        } catch (error) {\n          console.error('Error deleting document:', error);\n        } finally {\n          _this.showDeleteModal = false;\n          _this.noteToDelete = null;\n        }\n      }\n    })();\n  }\n  // Pagination methods\n  goToPage(page) {\n    if (page >= 1 && page <= this.totalPages) {\n      this.currentPage = page;\n    }\n  }\n  nextPage() {\n    if (this.currentPage < this.totalPages) {\n      this.currentPage++;\n    }\n  }\n  previousPage() {\n    if (this.currentPage > 1) {\n      this.currentPage--;\n    }\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: NotesService\n    }, {\n      type: Router\n    }, {\n      type: ActivatedRoute\n    }];\n  }\n  static {\n    this.propDecorators = {\n      chatContainer: [{\n        type: ViewChild,\n        args: ['chatContainer']\n      }],\n      chatInput: [{\n        type: ViewChild,\n        args: ['chatInput']\n      }],\n      editorElement: [{\n        type: ViewChild,\n        args: ['editor']\n      }]\n    };\n  }\n};\nDocumentListComponent = __decorate([Component({\n  selector: 'app-document-list',\n  imports: [CommonModule, FormsModule, MarkdownModule],\n  template: __NG_CLI_RESOURCE__0,\n  standalone: true,\n  styles: [__NG_CLI_RESOURCE__1]\n})], DocumentListComponent);\nexport { DocumentListComponent };", "map": {"version": 3, "names": ["Component", "ViewChild", "CommonModule", "FormsModule", "NotesService", "MarkdownModule", "ActivatedRoute", "Router", "DocumentListComponent", "constructor", "notesService", "router", "route", "isToogled", "wordCount", "markdownContent", "isSaving", "isLoading", "isGenerating", "generateModalVisible", "aiPrompt", "contentType", "useCurrentContent", "chatMessages", "text", "isUser", "timestamp", "Date", "selectedOption", "documents", "noteTitle", "currentNoteId", "isDocsOpen", "showDeleteModal", "noteToDelete", "sessionId", "<PERSON><PERSON><PERSON><PERSON>", "isjournal", "currentPage", "totalPages", "pageSize", "ngOnInit", "crypto", "randomUUID", "currentUrl", "url", "includes", "console", "log", "loadNotes", "createNewDocument", "navigate", "getAllNotes", "subscribe", "next", "notes", "Math", "ceil", "length", "error", "paginatedDocuments", "startIndex", "endIndex", "slice", "deleteDocument", "id", "event", "stopPropagation", "editDocument", "doc", "viewDocument", "toggleDocs", "confirmDelete", "_this", "_asyncToGenerator", "deleteNote", "to<PERSON>romise", "goToPage", "page", "nextPage", "previousPage", "args", "__decorate", "selector", "imports", "template", "__NG_CLI_RESOURCE__0", "standalone"], "sources": ["C:\\Users\\<USER>\\source\\ai-hub\\WebApp\\src\\app\\MyNotesProjects\\document-list\\document-list.component.ts"], "sourcesContent": ["import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { NotesService, Note } from '../services/notes.service';\r\nimport { MarkdownModule } from 'ngx-markdown';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\n\r\ninterface ChatMessage {\r\n  text: string;\r\n  isUser: boolean;\r\n  timestamp: Date;\r\n  hasCodeBlock?: boolean;\r\n  codeLanguage?: string;\r\n  code?: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-document-list',\r\n  imports: [CommonModule, FormsModule, MarkdownModule],\r\n  templateUrl: './document-list.component.html',\r\n  styleUrl: './document-list.component.css',\r\n  standalone: true,\r\n})\r\nexport class DocumentListComponent implements OnInit {\r\n  @ViewChild('chatContainer') private chatContainer!: ElementRef;\r\n  @ViewChild('chatInput') private chatInput!: ElementRef;\r\n  @ViewChild('editor') private editorElement!: ElementRef;\r\n\r\n  isToogled = false;\r\n  wordCount = 0;\r\n  markdownContent = '';\r\n  isSaving = false;\r\n  isLoading = false;\r\n  isGenerating = false;\r\n  generateModalVisible = false;\r\n  aiPrompt = '';\r\n  contentType = 'article';\r\n  useCurrentContent = false;\r\n  chatMessages: ChatMessage[] = [\r\n    {\r\n      text: \"Hello! How can I help you today?\",\r\n      isUser: false,\r\n      timestamp: new Date(),\r\n    }\r\n  ];\r\n  selectedOption = 'article';\r\n  documents: Note[] = [];\r\n  // Add title property\r\n  noteTitle: string = '';\r\n  currentNoteId: number | null = null;\r\n\r\n  isDocsOpen = false;  // Set default to false to hide docs section\r\n  showDeleteModal = false;\r\n  noteToDelete: number | null = null;\r\n  sessionId: string = '';\r\n  hasContent = false;\r\n  isjournal = false;\r\n\r\n  // Pagination properties\r\n  currentPage = 1;\r\n  totalPages = 1;\r\n  pageSize = 15;\r\n\r\n  constructor(private notesService: NotesService, private router: Router, private route: ActivatedRoute) { }\r\n\r\n  ngOnInit() {\r\n    this.sessionId = crypto.randomUUID();\r\n    const currentUrl = this.router.url;\r\n    if (currentUrl.includes('journal')) {\r\n      this.isjournal = true;\r\n      console.log('Journal detected in URL');\r\n    }\r\n    this.loadNotes();\r\n  }\r\n\r\n  createNewDocument() {\r\n    this.router.navigate(['/editor']);\r\n  }\r\n\r\n  private loadNotes() {\r\n    this.isLoading = true;\r\n    this.notesService.getAllNotes().subscribe({\r\n      next: (notes) => {\r\n        this.documents = notes;\r\n        this.totalPages = Math.ceil(this.documents.length / this.pageSize);\r\n        this.isLoading = false;\r\n      },\r\n      error: (error) => {\r\n        console.error('Error loading notes:', error);\r\n        this.isLoading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  get paginatedDocuments(): Note[] {\r\n    const startIndex = (this.currentPage - 1) * this.pageSize;\r\n    const endIndex = startIndex + this.pageSize;\r\n    return this.documents.slice(startIndex, endIndex);\r\n  }\r\n\r\n  deleteDocument(id: number, event: MouseEvent) {\r\n    event.stopPropagation();\r\n    this.noteToDelete = id;\r\n    this.showDeleteModal = true;\r\n  }\r\n\r\n  editDocument(doc: Note, event: MouseEvent) {\r\n    event.stopPropagation();\r\n    this.router.navigate(['editor', doc.id]);\r\n  }\r\n\r\n  viewDocument(doc: Note, event: MouseEvent) {\r\n    event.stopPropagation();\r\n    this.router.navigate(['document', doc.id]);\r\n  }\r\n\r\n  toggleDocs() {\r\n    this.isDocsOpen = !this.isDocsOpen;\r\n  }\r\n\r\n  async confirmDelete() {\r\n    if (this.noteToDelete) {\r\n      try {\r\n        await this.notesService.deleteNote(this.noteToDelete).toPromise();\r\n        this.loadNotes();\r\n      } catch (error) {\r\n        console.error('Error deleting document:', error);\r\n      } finally {\r\n        this.showDeleteModal = false;\r\n        this.noteToDelete = null;\r\n      }\r\n    }\r\n  }\r\n\r\n  // Pagination methods\r\n  goToPage(page: number) {\r\n    if (page >= 1 && page <= this.totalPages) {\r\n      this.currentPage = page;\r\n    }\r\n  }\r\n\r\n  nextPage() {\r\n    if (this.currentPage < this.totalPages) {\r\n      this.currentPage++;\r\n    }\r\n  }\r\n\r\n  previousPage() {\r\n    if (this.currentPage > 1) {\r\n      this.currentPage--;\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;;;AAAA,SAASA,SAAS,EAAUC,SAAS,QAAoB,eAAe;AACxE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,QAAc,2BAA2B;AAC9D,SAASC,cAAc,QAAQ,cAAc;AAC7C,SAASC,cAAc,EAAEC,MAAM,QAAQ,iBAAiB;AAkBjD,IAAMC,qBAAqB,GAA3B,MAAMA,qBAAqB;EAwChCC,YAAoBC,YAA0B,EAAUC,MAAc,EAAUC,KAAqB;IAAjF,KAAAF,YAAY,GAAZA,YAAY;IAAwB,KAAAC,MAAM,GAANA,MAAM;IAAkB,KAAAC,KAAK,GAALA,KAAK;IAnCrF,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,eAAe,GAAG,EAAE;IACpB,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,oBAAoB,GAAG,KAAK;IAC5B,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,WAAW,GAAG,SAAS;IACvB,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,YAAY,GAAkB,CAC5B;MACEC,IAAI,EAAE,kCAAkC;MACxCC,MAAM,EAAE,KAAK;MACbC,SAAS,EAAE,IAAIC,IAAI;KACpB,CACF;IACD,KAAAC,cAAc,GAAG,SAAS;IAC1B,KAAAC,SAAS,GAAW,EAAE;IACtB;IACA,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAC,aAAa,GAAkB,IAAI;IAEnC,KAAAC,UAAU,GAAG,KAAK,CAAC,CAAE;IACrB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,YAAY,GAAkB,IAAI;IAClC,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,SAAS,GAAG,KAAK;IAEjB;IACA,KAAAC,WAAW,GAAG,CAAC;IACf,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,QAAQ,GAAG,EAAE;EAE4F;EAEzGC,QAAQA,CAAA;IACN,IAAI,CAACN,SAAS,GAAGO,MAAM,CAACC,UAAU,EAAE;IACpC,MAAMC,UAAU,GAAG,IAAI,CAACjC,MAAM,CAACkC,GAAG;IAClC,IAAID,UAAU,CAACE,QAAQ,CAAC,SAAS,CAAC,EAAE;MAClC,IAAI,CAACT,SAAS,GAAG,IAAI;MACrBU,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;;IAExC,IAAI,CAACC,SAAS,EAAE;EAClB;EAEAC,iBAAiBA,CAAA;IACf,IAAI,CAACvC,MAAM,CAACwC,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;EACnC;EAEQF,SAASA,CAAA;IACf,IAAI,CAAChC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACP,YAAY,CAAC0C,WAAW,EAAE,CAACC,SAAS,CAAC;MACxCC,IAAI,EAAGC,KAAK,IAAI;QACd,IAAI,CAAC1B,SAAS,GAAG0B,KAAK;QACtB,IAAI,CAAChB,UAAU,GAAGiB,IAAI,CAACC,IAAI,CAAC,IAAI,CAAC5B,SAAS,CAAC6B,MAAM,GAAG,IAAI,CAAClB,QAAQ,CAAC;QAClE,IAAI,CAACvB,SAAS,GAAG,KAAK;MACxB,CAAC;MACD0C,KAAK,EAAGA,KAAK,IAAI;QACfZ,OAAO,CAACY,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,IAAI,CAAC1C,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EAEA,IAAI2C,kBAAkBA,CAAA;IACpB,MAAMC,UAAU,GAAG,CAAC,IAAI,CAACvB,WAAW,GAAG,CAAC,IAAI,IAAI,CAACE,QAAQ;IACzD,MAAMsB,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAACrB,QAAQ;IAC3C,OAAO,IAAI,CAACX,SAAS,CAACkC,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;EACnD;EAEAE,cAAcA,CAACC,EAAU,EAAEC,KAAiB;IAC1CA,KAAK,CAACC,eAAe,EAAE;IACvB,IAAI,CAACjC,YAAY,GAAG+B,EAAE;IACtB,IAAI,CAAChC,eAAe,GAAG,IAAI;EAC7B;EAEAmC,YAAYA,CAACC,GAAS,EAAEH,KAAiB;IACvCA,KAAK,CAACC,eAAe,EAAE;IACvB,IAAI,CAACxD,MAAM,CAACwC,QAAQ,CAAC,CAAC,QAAQ,EAAEkB,GAAG,CAACJ,EAAE,CAAC,CAAC;EAC1C;EAEAK,YAAYA,CAACD,GAAS,EAAEH,KAAiB;IACvCA,KAAK,CAACC,eAAe,EAAE;IACvB,IAAI,CAACxD,MAAM,CAACwC,QAAQ,CAAC,CAAC,UAAU,EAAEkB,GAAG,CAACJ,EAAE,CAAC,CAAC;EAC5C;EAEAM,UAAUA,CAAA;IACR,IAAI,CAACvC,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;EACpC;EAEMwC,aAAaA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACjB,IAAID,KAAI,CAACvC,YAAY,EAAE;QACrB,IAAI;UACF,MAAMuC,KAAI,CAAC/D,YAAY,CAACiE,UAAU,CAACF,KAAI,CAACvC,YAAY,CAAC,CAAC0C,SAAS,EAAE;UACjEH,KAAI,CAACxB,SAAS,EAAE;SACjB,CAAC,OAAOU,KAAK,EAAE;UACdZ,OAAO,CAACY,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;SACjD,SAAS;UACRc,KAAI,CAACxC,eAAe,GAAG,KAAK;UAC5BwC,KAAI,CAACvC,YAAY,GAAG,IAAI;;;IAE3B;EACH;EAEA;EACA2C,QAAQA,CAACC,IAAY;IACnB,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,IAAI,CAACvC,UAAU,EAAE;MACxC,IAAI,CAACD,WAAW,GAAGwC,IAAI;;EAE3B;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACzC,WAAW,GAAG,IAAI,CAACC,UAAU,EAAE;MACtC,IAAI,CAACD,WAAW,EAAE;;EAEtB;EAEA0C,YAAYA,CAAA;IACV,IAAI,IAAI,CAAC1C,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;;EAEtB;;;;;;;;;;;;;cA/HCrC,SAAS;QAAAgF,IAAA,GAAC,eAAe;MAAA;;cACzBhF,SAAS;QAAAgF,IAAA,GAAC,WAAW;MAAA;;cACrBhF,SAAS;QAAAgF,IAAA,GAAC,QAAQ;MAAA;;;;AAHRzE,qBAAqB,GAAA0E,UAAA,EAPjClF,SAAS,CAAC;EACTmF,QAAQ,EAAE,mBAAmB;EAC7BC,OAAO,EAAE,CAAClF,YAAY,EAAEC,WAAW,EAAEE,cAAc,CAAC;EACpDgF,QAAA,EAAAC,oBAA6C;EAE7CC,UAAU,EAAE,IAAI;;CACjB,CAAC,C,EACW/E,qBAAqB,CAiIjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}