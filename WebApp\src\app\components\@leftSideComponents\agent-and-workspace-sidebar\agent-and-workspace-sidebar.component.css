/* MS Teams Style Chat Container */
.teams-chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Chat Header Styles */
.chat-header {
  flex-shrink: 0;
  background: inherit;
}

.chat-header h2 {
  font-size: 14px;
  font-weight: 600;
  letter-spacing: 0.2px;
}

/* Chat List Container */
.chat-list-container {
  flex: 1;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
}

/* Custom scrollbar for webkit browsers */
.chat-list-container::-webkit-scrollbar {
  width: 6px;
}

.chat-list-container::-webkit-scrollbar-track {
  background: transparent;
}

.chat-list-container::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

.chat-list-container::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Chat Item Styles - MS Teams inspired */
.chat-item {
  position: relative;
  border-radius: 6px;
  margin: 0 2px;
  user-select: none;
}

.chat-item:hover {
  background-color: rgba(255, 255, 255, 0.08) !important;
}

.chat-item:active {
  background-color: rgba(255, 255, 255, 0.12) !important;
}

/* Chat Avatar Styles */
.chat-avatar {
  position: relative;
  overflow: hidden;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.chat-avatar span {
  font-weight: 600;
  font-size: 12px;
  line-height: 1;
}

/* Text Styles */
.chat-item .text-sm {
  font-size: 13px;
  line-height: 1.4;
  font-weight: 400;
}

.chat-item .font-medium {
  font-weight: 600;
}

/* Truncation */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Loading Animation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Smooth transitions */
.chat-item,
.chat-avatar,
.teams-chat-container * {
  transition: background-color 0.15s ease, color 0.15s ease, transform 0.15s ease;
}

/* Focus states for accessibility */
.chat-item:focus {
  outline: 2px solid #0078d4;
  outline-offset: -2px;
}

/* Hover effects for buttons */
.chat-header button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Empty state styling */
.teams-chat-container .flex.flex-col.items-center {
  opacity: 0.7;
}

/* Error state styling */
.text-red-500 {
  color: #ff6b6b;
  font-size: 12px;
  padding: 8px 12px;
  background: rgba(255, 107, 107, 0.1);
  border-radius: 4px;
  margin: 4px 0;
}

/* Dark theme specific adjustments */
:host-context(.dark) .chat-item:hover {
  background-color: rgba(255, 255, 255, 0.06) !important;
}

:host-context(.dark) .chat-header button:hover {
  background-color: rgba(255, 255, 255, 0.08);
}

/* Light theme specific adjustments */
:host-context(.light) .chat-item:hover {
  background-color: rgba(0, 0, 0, 0.04) !important;
}

:host-context(.light) .chat-header button:hover {
  background-color: rgba(0, 0, 0, 0.06);
}

/* Responsive Design */
@media (max-width: 768px) {
  .chat-header {
    padding: 12px 16px;
  }

  .chat-list-container {
    padding: 8px 12px;
  }

  .chat-item {
    padding: 8px 12px;
    margin-bottom: 2px;
  }

  .chat-avatar {
    width: 28px;
    height: 28px;
  }

  .chat-avatar span {
    font-size: 11px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .chat-item {
    border: 1px solid currentColor;
  }

  .chat-avatar {
    border: 2px solid currentColor;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .chat-item,
  .chat-avatar,
  .teams-chat-container * {
    transition: none;
  }

  .animate-spin {
    animation: none;
  }
}
