/* Agent and Workspace Container Styles */
.agent-workspace-container {
  overflow-y: auto;
  scrollbar-width: thin;
}

/* Custom scrollbar for webkit browsers */
.agent-workspace-container::-webkit-scrollbar {
  width: 6px;
}

.agent-workspace-container::-webkit-scrollbar-track {
  background: transparent;
}

.agent-workspace-container::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

.agent-workspace-container::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Simple List Item Hover Effects */
.agent-list-item,
.workspace-list-item {
  transition: background-color 0.2s ease-in-out;
}

.agent-list-item:hover,
.workspace-list-item:hover {
  /* Background color changes are handled by ngClass in template */
}

/* Text Truncation */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Loading Animation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Smooth transitions for theme changes */
.agent-list-container * {
  transition: background-color 0.2s ease, color 0.2s ease, border-color 0.2s ease;
}

/* Accordion Styles */
.accordion-container {
  /* Container for all accordion sections */
}

.accordion-section {
  /* Individual accordion section */
}

.accordion-header {
  /* Accordion header styling */
  user-select: none;
}

.accordion-header:hover {
  /* Hover effect handled by ngClass in template */
}

.accordion-content {
  /* Accordion content area */
  overflow-y: auto;
  scrollbar-width: thin;
}

/* Custom scrollbar for accordion content */
.accordion-content::-webkit-scrollbar {
  width: 4px;
}

.accordion-content::-webkit-scrollbar-track {
  background: transparent;
}

.accordion-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
}

.accordion-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Smooth transitions for accordion */
.accordion-content {
  transition: max-height 0.3s ease-in-out, opacity 0.3s ease-in-out, margin-top 0.3s ease-in-out;
}

/* Chevron rotation animation */
.accordion-header i {
  transition: transform 0.2s ease-in-out;
}

/* Card hover effects */
.agent-card,
.workspace-card {
  transition: all 0.2s ease-in-out;
}

.agent-card:hover,
.workspace-card:hover {
  transform: translateY(-1px);
}

/* Text truncation for long content */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

/* Responsive Design */
@media (max-width: 768px) {
  .agent-workspace-container {
    padding: 0.75rem;
  }

  .agent-list-item,
  .workspace-list-item {
    padding: 0.5rem 0.75rem;
  }

  .accordion-header {
    padding: 0.5rem 0.75rem;
  }

  .agent-card,
  .workspace-card {
    padding: 0.75rem;
  }
}
