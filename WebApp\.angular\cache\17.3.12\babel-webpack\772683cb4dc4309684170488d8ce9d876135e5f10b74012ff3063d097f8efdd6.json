{"ast": null, "code": "import { __decorate } from 'tslib';\nimport { LEFT_ARROW, RIGHT_ARROW, SPACE, ENTER } from '@angular/cdk/keycodes';\nimport { NgIf } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { forwardRef, Component, ChangeDetectionStrategy, ViewEncapsulation, Optional, ViewChild, Input, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { Subject, fromEvent } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport * as i1 from 'ng-zorro-antd/core/config';\nimport { WithConfig } from 'ng-zorro-antd/core/config';\nimport * as i6 from 'ng-zorro-antd/core/outlet';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport { InputBoolean } from 'ng-zorro-antd/core/util';\nimport * as i4 from 'ng-zorro-antd/core/wave';\nimport { NzWaveModule } from 'ng-zorro-antd/core/wave';\nimport * as i5 from 'ng-zorro-antd/icon';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport * as i2 from '@angular/cdk/a11y';\nimport * as i3 from '@angular/cdk/bidi';\nconst _c0 = [\"switchElement\"];\nfunction NzSwitchComponent_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 8);\n  }\n}\nfunction NzSwitchComponent_ng_container_5_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.nzCheckedChildren);\n  }\n}\nfunction NzSwitchComponent_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NzSwitchComponent_ng_container_5_ng_container_1_Template, 2, 1, \"ng-container\", 9);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx_r0.nzCheckedChildren);\n  }\n}\nfunction NzSwitchComponent_ng_template_6_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.nzUnCheckedChildren);\n  }\n}\nfunction NzSwitchComponent_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzSwitchComponent_ng_template_6_ng_container_0_Template, 2, 1, \"ng-container\", 9);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx_r0.nzUnCheckedChildren);\n  }\n}\nconst NZ_CONFIG_MODULE_NAME = 'switch';\nclass NzSwitchComponent {\n  updateValue(value) {\n    if (this.isChecked !== value) {\n      this.isChecked = value;\n      this.onChange(this.isChecked);\n    }\n  }\n  focus() {\n    this.focusMonitor.focusVia(this.switchElement.nativeElement, 'keyboard');\n  }\n  blur() {\n    this.switchElement.nativeElement.blur();\n  }\n  constructor(nzConfigService, host, ngZone, cdr, focusMonitor, directionality) {\n    this.nzConfigService = nzConfigService;\n    this.host = host;\n    this.ngZone = ngZone;\n    this.cdr = cdr;\n    this.focusMonitor = focusMonitor;\n    this.directionality = directionality;\n    this._nzModuleName = NZ_CONFIG_MODULE_NAME;\n    this.isChecked = false;\n    this.onChange = () => {};\n    this.onTouched = () => {};\n    this.nzLoading = false;\n    this.nzDisabled = false;\n    this.nzControl = false;\n    this.nzCheckedChildren = null;\n    this.nzUnCheckedChildren = null;\n    this.nzSize = 'default';\n    this.nzId = null;\n    this.dir = 'ltr';\n    this.destroy$ = new Subject();\n    this.isNzDisableFirstChange = true;\n  }\n  ngOnInit() {\n    this.directionality.change.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n    this.dir = this.directionality.value;\n    this.ngZone.runOutsideAngular(() => {\n      fromEvent(this.host.nativeElement, 'click').pipe(takeUntil(this.destroy$)).subscribe(event => {\n        event.preventDefault();\n        if (this.nzControl || this.nzDisabled || this.nzLoading) {\n          return;\n        }\n        this.ngZone.run(() => {\n          this.updateValue(!this.isChecked);\n          this.cdr.markForCheck();\n        });\n      });\n      fromEvent(this.switchElement.nativeElement, 'keydown').pipe(takeUntil(this.destroy$)).subscribe(event => {\n        if (this.nzControl || this.nzDisabled || this.nzLoading) {\n          return;\n        }\n        const {\n          keyCode\n        } = event;\n        if (keyCode !== LEFT_ARROW && keyCode !== RIGHT_ARROW && keyCode !== SPACE && keyCode !== ENTER) {\n          return;\n        }\n        event.preventDefault();\n        this.ngZone.run(() => {\n          if (keyCode === LEFT_ARROW) {\n            this.updateValue(false);\n          } else if (keyCode === RIGHT_ARROW) {\n            this.updateValue(true);\n          } else if (keyCode === SPACE || keyCode === ENTER) {\n            this.updateValue(!this.isChecked);\n          }\n          this.cdr.markForCheck();\n        });\n      });\n    });\n  }\n  ngAfterViewInit() {\n    this.focusMonitor.monitor(this.switchElement.nativeElement, true).pipe(takeUntil(this.destroy$)).subscribe(focusOrigin => {\n      if (!focusOrigin) {\n        /** https://github.com/angular/angular/issues/17793 **/\n        Promise.resolve().then(() => this.onTouched());\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.focusMonitor.stopMonitoring(this.switchElement.nativeElement);\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  writeValue(value) {\n    this.isChecked = value;\n    this.cdr.markForCheck();\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(disabled) {\n    this.nzDisabled = this.isNzDisableFirstChange && this.nzDisabled || disabled;\n    this.isNzDisableFirstChange = false;\n    this.cdr.markForCheck();\n  }\n  static {\n    this.ɵfac = function NzSwitchComponent_Factory(t) {\n      return new (t || NzSwitchComponent)(i0.ɵɵdirectiveInject(i1.NzConfigService), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.FocusMonitor), i0.ɵɵdirectiveInject(i3.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzSwitchComponent,\n      selectors: [[\"nz-switch\"]],\n      viewQuery: function NzSwitchComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.switchElement = _t.first);\n        }\n      },\n      inputs: {\n        nzLoading: \"nzLoading\",\n        nzDisabled: \"nzDisabled\",\n        nzControl: \"nzControl\",\n        nzCheckedChildren: \"nzCheckedChildren\",\n        nzUnCheckedChildren: \"nzUnCheckedChildren\",\n        nzSize: \"nzSize\",\n        nzId: \"nzId\"\n      },\n      exportAs: [\"nzSwitch\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => NzSwitchComponent),\n        multi: true\n      }]), i0.ɵɵStandaloneFeature],\n      decls: 9,\n      vars: 16,\n      consts: [[\"switchElement\", \"\"], [\"uncheckTemplate\", \"\"], [\"nz-wave\", \"\", \"type\", \"button\", 1, \"ant-switch\", 3, \"disabled\", \"nzWaveExtraNode\"], [1, \"ant-switch-handle\"], [\"nz-icon\", \"\", \"nzType\", \"loading\", \"class\", \"ant-switch-loading-icon\", 4, \"ngIf\"], [1, \"ant-switch-inner\"], [4, \"ngIf\", \"ngIfElse\"], [1, \"ant-click-animating-node\"], [\"nz-icon\", \"\", \"nzType\", \"loading\", 1, \"ant-switch-loading-icon\"], [4, \"nzStringTemplateOutlet\"]],\n      template: function NzSwitchComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"button\", 2, 0)(2, \"span\", 3);\n          i0.ɵɵtemplate(3, NzSwitchComponent_span_3_Template, 1, 0, \"span\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"span\", 5);\n          i0.ɵɵtemplate(5, NzSwitchComponent_ng_container_5_Template, 2, 1, \"ng-container\", 6)(6, NzSwitchComponent_ng_template_6_Template, 1, 1, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(8, \"div\", 7);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          const uncheckTemplate_r2 = i0.ɵɵreference(7);\n          i0.ɵɵclassProp(\"ant-switch-checked\", ctx.isChecked)(\"ant-switch-loading\", ctx.nzLoading)(\"ant-switch-disabled\", ctx.nzDisabled)(\"ant-switch-small\", ctx.nzSize === \"small\")(\"ant-switch-rtl\", ctx.dir === \"rtl\");\n          i0.ɵɵproperty(\"disabled\", ctx.nzDisabled)(\"nzWaveExtraNode\", true);\n          i0.ɵɵattribute(\"id\", ctx.nzId);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.nzLoading);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isChecked)(\"ngIfElse\", uncheckTemplate_r2);\n        }\n      },\n      dependencies: [NzWaveModule, i4.NzWaveDirective, NzIconModule, i5.NzIconDirective, NgIf, NzOutletModule, i6.NzStringTemplateOutletDirective],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([InputBoolean()], NzSwitchComponent.prototype, \"nzLoading\", void 0);\n__decorate([InputBoolean()], NzSwitchComponent.prototype, \"nzDisabled\", void 0);\n__decorate([InputBoolean()], NzSwitchComponent.prototype, \"nzControl\", void 0);\n__decorate([WithConfig()], NzSwitchComponent.prototype, \"nzSize\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSwitchComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-switch',\n      exportAs: 'nzSwitch',\n      preserveWhitespaces: false,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => NzSwitchComponent),\n        multi: true\n      }],\n      template: `\n    <button\n      nz-wave\n      type=\"button\"\n      class=\"ant-switch\"\n      #switchElement\n      [attr.id]=\"nzId\"\n      [disabled]=\"nzDisabled\"\n      [class.ant-switch-checked]=\"isChecked\"\n      [class.ant-switch-loading]=\"nzLoading\"\n      [class.ant-switch-disabled]=\"nzDisabled\"\n      [class.ant-switch-small]=\"nzSize === 'small'\"\n      [class.ant-switch-rtl]=\"dir === 'rtl'\"\n      [nzWaveExtraNode]=\"true\"\n    >\n      <span class=\"ant-switch-handle\">\n        <span *ngIf=\"nzLoading\" nz-icon nzType=\"loading\" class=\"ant-switch-loading-icon\"></span>\n      </span>\n      <span class=\"ant-switch-inner\">\n        <ng-container *ngIf=\"isChecked; else uncheckTemplate\">\n          <ng-container *nzStringTemplateOutlet=\"nzCheckedChildren\">{{ nzCheckedChildren }}</ng-container>\n        </ng-container>\n        <ng-template #uncheckTemplate>\n          <ng-container *nzStringTemplateOutlet=\"nzUnCheckedChildren\">{{ nzUnCheckedChildren }}</ng-container>\n        </ng-template>\n      </span>\n      <div class=\"ant-click-animating-node\"></div>\n    </button>\n  `,\n      imports: [NzWaveModule, NzIconModule, NgIf, NzOutletModule],\n      standalone: true\n    }]\n  }], () => [{\n    type: i1.NzConfigService\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.NgZone\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i2.FocusMonitor\n  }, {\n    type: i3.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    switchElement: [{\n      type: ViewChild,\n      args: ['switchElement', {\n        static: true\n      }]\n    }],\n    nzLoading: [{\n      type: Input\n    }],\n    nzDisabled: [{\n      type: Input\n    }],\n    nzControl: [{\n      type: Input\n    }],\n    nzCheckedChildren: [{\n      type: Input\n    }],\n    nzUnCheckedChildren: [{\n      type: Input\n    }],\n    nzSize: [{\n      type: Input\n    }],\n    nzId: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzSwitchModule {\n  static {\n    this.ɵfac = function NzSwitchModule_Factory(t) {\n      return new (t || NzSwitchModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzSwitchModule,\n      imports: [NzSwitchComponent],\n      exports: [NzSwitchComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [NzSwitchComponent]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSwitchModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzSwitchComponent],\n      exports: [NzSwitchComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzSwitchComponent, NzSwitchModule };", "map": {"version": 3, "names": ["__decorate", "LEFT_ARROW", "RIGHT_ARROW", "SPACE", "ENTER", "NgIf", "i0", "forwardRef", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Optional", "ViewChild", "Input", "NgModule", "NG_VALUE_ACCESSOR", "Subject", "fromEvent", "takeUntil", "i1", "WithConfig", "i6", "NzOutletModule", "InputBoolean", "i4", "NzWaveModule", "i5", "NzIconModule", "i2", "i3", "_c0", "NzSwitchComponent_span_3_Template", "rf", "ctx", "ɵɵelement", "NzSwitchComponent_ng_container_5_ng_container_1_Template", "ɵɵelementContainerStart", "ɵɵtext", "ɵɵelementContainerEnd", "ctx_r0", "ɵɵnextContext", "ɵɵadvance", "ɵɵtextInterpolate", "nzCheckedChildren", "NzSwitchComponent_ng_container_5_Template", "ɵɵtemplate", "ɵɵproperty", "NzSwitchComponent_ng_template_6_ng_container_0_Template", "nzUnCheckedChildren", "NzSwitchComponent_ng_template_6_Template", "NZ_CONFIG_MODULE_NAME", "NzSwitchComponent", "updateValue", "value", "isChecked", "onChange", "focus", "focusMonitor", "focusVia", "switchElement", "nativeElement", "blur", "constructor", "nzConfigService", "host", "ngZone", "cdr", "directionality", "_nzModuleName", "onTouched", "nzLoading", "nzDisabled", "nzControl", "nzSize", "nzId", "dir", "destroy$", "isNzDisableFirstChange", "ngOnInit", "change", "pipe", "subscribe", "direction", "detectChanges", "runOutsideAngular", "event", "preventDefault", "run", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "keyCode", "ngAfterViewInit", "monitor", "<PERSON><PERSON><PERSON><PERSON>", "Promise", "resolve", "then", "ngOnDestroy", "stopMonitoring", "next", "complete", "writeValue", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "disabled", "ɵfac", "NzSwitchComponent_Factory", "t", "ɵɵdirectiveInject", "NzConfigService", "ElementRef", "NgZone", "ChangeDetectorRef", "FocusMonitor", "Directionality", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "viewQuery", "NzSwitchComponent_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "inputs", "exportAs", "standalone", "features", "ɵɵProvidersFeature", "provide", "useExisting", "multi", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "NzSwitchComponent_Template", "ɵɵelementStart", "ɵɵelementEnd", "ɵɵtemplateRefExtractor", "uncheckTemplate_r2", "ɵɵreference", "ɵɵclassProp", "ɵɵattribute", "dependencies", "NzWaveDirective", "NzIconDirective", "NzStringTemplateOutletDirective", "encapsulation", "changeDetection", "prototype", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "preserveWhitespaces", "OnPush", "None", "providers", "imports", "decorators", "static", "NzSwitchModule", "NzSwitchModule_Factory", "ɵmod", "ɵɵdefineNgModule", "exports", "ɵinj", "ɵɵdefineInjector"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-switch.mjs"], "sourcesContent": ["import { __decorate } from 'tslib';\nimport { LEFT_ARROW, RIGHT_ARROW, SPACE, ENTER } from '@angular/cdk/keycodes';\nimport { NgIf } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { forwardRef, Component, ChangeDetectionStrategy, ViewEncapsulation, Optional, ViewChild, Input, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { Subject, fromEvent } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport * as i1 from 'ng-zorro-antd/core/config';\nimport { WithConfig } from 'ng-zorro-antd/core/config';\nimport * as i6 from 'ng-zorro-antd/core/outlet';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport { InputBoolean } from 'ng-zorro-antd/core/util';\nimport * as i4 from 'ng-zorro-antd/core/wave';\nimport { NzWaveModule } from 'ng-zorro-antd/core/wave';\nimport * as i5 from 'ng-zorro-antd/icon';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport * as i2 from '@angular/cdk/a11y';\nimport * as i3 from '@angular/cdk/bidi';\n\nconst NZ_CONFIG_MODULE_NAME = 'switch';\nclass NzSwitchComponent {\n    updateValue(value) {\n        if (this.isChecked !== value) {\n            this.isChecked = value;\n            this.onChange(this.isChecked);\n        }\n    }\n    focus() {\n        this.focusMonitor.focusVia(this.switchElement.nativeElement, 'keyboard');\n    }\n    blur() {\n        this.switchElement.nativeElement.blur();\n    }\n    constructor(nzConfigService, host, ngZone, cdr, focusMonitor, directionality) {\n        this.nzConfigService = nzConfigService;\n        this.host = host;\n        this.ngZone = ngZone;\n        this.cdr = cdr;\n        this.focusMonitor = focusMonitor;\n        this.directionality = directionality;\n        this._nzModuleName = NZ_CONFIG_MODULE_NAME;\n        this.isChecked = false;\n        this.onChange = () => { };\n        this.onTouched = () => { };\n        this.nzLoading = false;\n        this.nzDisabled = false;\n        this.nzControl = false;\n        this.nzCheckedChildren = null;\n        this.nzUnCheckedChildren = null;\n        this.nzSize = 'default';\n        this.nzId = null;\n        this.dir = 'ltr';\n        this.destroy$ = new Subject();\n        this.isNzDisableFirstChange = true;\n    }\n    ngOnInit() {\n        this.directionality.change.pipe(takeUntil(this.destroy$)).subscribe((direction) => {\n            this.dir = direction;\n            this.cdr.detectChanges();\n        });\n        this.dir = this.directionality.value;\n        this.ngZone.runOutsideAngular(() => {\n            fromEvent(this.host.nativeElement, 'click')\n                .pipe(takeUntil(this.destroy$))\n                .subscribe(event => {\n                event.preventDefault();\n                if (this.nzControl || this.nzDisabled || this.nzLoading) {\n                    return;\n                }\n                this.ngZone.run(() => {\n                    this.updateValue(!this.isChecked);\n                    this.cdr.markForCheck();\n                });\n            });\n            fromEvent(this.switchElement.nativeElement, 'keydown')\n                .pipe(takeUntil(this.destroy$))\n                .subscribe(event => {\n                if (this.nzControl || this.nzDisabled || this.nzLoading) {\n                    return;\n                }\n                const { keyCode } = event;\n                if (keyCode !== LEFT_ARROW && keyCode !== RIGHT_ARROW && keyCode !== SPACE && keyCode !== ENTER) {\n                    return;\n                }\n                event.preventDefault();\n                this.ngZone.run(() => {\n                    if (keyCode === LEFT_ARROW) {\n                        this.updateValue(false);\n                    }\n                    else if (keyCode === RIGHT_ARROW) {\n                        this.updateValue(true);\n                    }\n                    else if (keyCode === SPACE || keyCode === ENTER) {\n                        this.updateValue(!this.isChecked);\n                    }\n                    this.cdr.markForCheck();\n                });\n            });\n        });\n    }\n    ngAfterViewInit() {\n        this.focusMonitor\n            .monitor(this.switchElement.nativeElement, true)\n            .pipe(takeUntil(this.destroy$))\n            .subscribe(focusOrigin => {\n            if (!focusOrigin) {\n                /** https://github.com/angular/angular/issues/17793 **/\n                Promise.resolve().then(() => this.onTouched());\n            }\n        });\n    }\n    ngOnDestroy() {\n        this.focusMonitor.stopMonitoring(this.switchElement.nativeElement);\n        this.destroy$.next();\n        this.destroy$.complete();\n    }\n    writeValue(value) {\n        this.isChecked = value;\n        this.cdr.markForCheck();\n    }\n    registerOnChange(fn) {\n        this.onChange = fn;\n    }\n    registerOnTouched(fn) {\n        this.onTouched = fn;\n    }\n    setDisabledState(disabled) {\n        this.nzDisabled = (this.isNzDisableFirstChange && this.nzDisabled) || disabled;\n        this.isNzDisableFirstChange = false;\n        this.cdr.markForCheck();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzSwitchComponent, deps: [{ token: i1.NzConfigService }, { token: i0.ElementRef }, { token: i0.NgZone }, { token: i0.ChangeDetectorRef }, { token: i2.FocusMonitor }, { token: i3.Directionality, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzSwitchComponent, isStandalone: true, selector: \"nz-switch\", inputs: { nzLoading: \"nzLoading\", nzDisabled: \"nzDisabled\", nzControl: \"nzControl\", nzCheckedChildren: \"nzCheckedChildren\", nzUnCheckedChildren: \"nzUnCheckedChildren\", nzSize: \"nzSize\", nzId: \"nzId\" }, providers: [\n            {\n                provide: NG_VALUE_ACCESSOR,\n                useExisting: forwardRef(() => NzSwitchComponent),\n                multi: true\n            }\n        ], viewQueries: [{ propertyName: \"switchElement\", first: true, predicate: [\"switchElement\"], descendants: true, static: true }], exportAs: [\"nzSwitch\"], ngImport: i0, template: `\n    <button\n      nz-wave\n      type=\"button\"\n      class=\"ant-switch\"\n      #switchElement\n      [attr.id]=\"nzId\"\n      [disabled]=\"nzDisabled\"\n      [class.ant-switch-checked]=\"isChecked\"\n      [class.ant-switch-loading]=\"nzLoading\"\n      [class.ant-switch-disabled]=\"nzDisabled\"\n      [class.ant-switch-small]=\"nzSize === 'small'\"\n      [class.ant-switch-rtl]=\"dir === 'rtl'\"\n      [nzWaveExtraNode]=\"true\"\n    >\n      <span class=\"ant-switch-handle\">\n        <span *ngIf=\"nzLoading\" nz-icon nzType=\"loading\" class=\"ant-switch-loading-icon\"></span>\n      </span>\n      <span class=\"ant-switch-inner\">\n        <ng-container *ngIf=\"isChecked; else uncheckTemplate\">\n          <ng-container *nzStringTemplateOutlet=\"nzCheckedChildren\">{{ nzCheckedChildren }}</ng-container>\n        </ng-container>\n        <ng-template #uncheckTemplate>\n          <ng-container *nzStringTemplateOutlet=\"nzUnCheckedChildren\">{{ nzUnCheckedChildren }}</ng-container>\n        </ng-template>\n      </span>\n      <div class=\"ant-click-animating-node\"></div>\n    </button>\n  `, isInline: true, dependencies: [{ kind: \"ngmodule\", type: NzWaveModule }, { kind: \"directive\", type: i4.NzWaveDirective, selector: \"[nz-wave],button[nz-button]:not([nzType=\\\"link\\\"]):not([nzType=\\\"text\\\"])\", inputs: [\"nzWaveExtraNode\"], exportAs: [\"nzWave\"] }, { kind: \"ngmodule\", type: NzIconModule }, { kind: \"directive\", type: i5.NzIconDirective, selector: \"[nz-icon]\", inputs: [\"nzSpin\", \"nzRotate\", \"nzType\", \"nzTheme\", \"nzTwotoneColor\", \"nzIconfont\"], exportAs: [\"nzIcon\"] }, { kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"ngmodule\", type: NzOutletModule }, { kind: \"directive\", type: i6.NzStringTemplateOutletDirective, selector: \"[nzStringTemplateOutlet]\", inputs: [\"nzStringTemplateOutletContext\", \"nzStringTemplateOutlet\"], exportAs: [\"nzStringTemplateOutlet\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\n__decorate([\n    InputBoolean()\n], NzSwitchComponent.prototype, \"nzLoading\", void 0);\n__decorate([\n    InputBoolean()\n], NzSwitchComponent.prototype, \"nzDisabled\", void 0);\n__decorate([\n    InputBoolean()\n], NzSwitchComponent.prototype, \"nzControl\", void 0);\n__decorate([\n    WithConfig()\n], NzSwitchComponent.prototype, \"nzSize\", void 0);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzSwitchComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'nz-switch',\n                    exportAs: 'nzSwitch',\n                    preserveWhitespaces: false,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    encapsulation: ViewEncapsulation.None,\n                    providers: [\n                        {\n                            provide: NG_VALUE_ACCESSOR,\n                            useExisting: forwardRef(() => NzSwitchComponent),\n                            multi: true\n                        }\n                    ],\n                    template: `\n    <button\n      nz-wave\n      type=\"button\"\n      class=\"ant-switch\"\n      #switchElement\n      [attr.id]=\"nzId\"\n      [disabled]=\"nzDisabled\"\n      [class.ant-switch-checked]=\"isChecked\"\n      [class.ant-switch-loading]=\"nzLoading\"\n      [class.ant-switch-disabled]=\"nzDisabled\"\n      [class.ant-switch-small]=\"nzSize === 'small'\"\n      [class.ant-switch-rtl]=\"dir === 'rtl'\"\n      [nzWaveExtraNode]=\"true\"\n    >\n      <span class=\"ant-switch-handle\">\n        <span *ngIf=\"nzLoading\" nz-icon nzType=\"loading\" class=\"ant-switch-loading-icon\"></span>\n      </span>\n      <span class=\"ant-switch-inner\">\n        <ng-container *ngIf=\"isChecked; else uncheckTemplate\">\n          <ng-container *nzStringTemplateOutlet=\"nzCheckedChildren\">{{ nzCheckedChildren }}</ng-container>\n        </ng-container>\n        <ng-template #uncheckTemplate>\n          <ng-container *nzStringTemplateOutlet=\"nzUnCheckedChildren\">{{ nzUnCheckedChildren }}</ng-container>\n        </ng-template>\n      </span>\n      <div class=\"ant-click-animating-node\"></div>\n    </button>\n  `,\n                    imports: [NzWaveModule, NzIconModule, NgIf, NzOutletModule],\n                    standalone: true\n                }]\n        }], ctorParameters: () => [{ type: i1.NzConfigService }, { type: i0.ElementRef }, { type: i0.NgZone }, { type: i0.ChangeDetectorRef }, { type: i2.FocusMonitor }, { type: i3.Directionality, decorators: [{\n                    type: Optional\n                }] }], propDecorators: { switchElement: [{\n                type: ViewChild,\n                args: ['switchElement', { static: true }]\n            }], nzLoading: [{\n                type: Input\n            }], nzDisabled: [{\n                type: Input\n            }], nzControl: [{\n                type: Input\n            }], nzCheckedChildren: [{\n                type: Input\n            }], nzUnCheckedChildren: [{\n                type: Input\n            }], nzSize: [{\n                type: Input\n            }], nzId: [{\n                type: Input\n            }] } });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzSwitchModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzSwitchModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.3.8\", ngImport: i0, type: NzSwitchModule, imports: [NzSwitchComponent], exports: [NzSwitchComponent] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzSwitchModule, imports: [NzSwitchComponent] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzSwitchModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [NzSwitchComponent],\n                    exports: [NzSwitchComponent]\n                }]\n        }] });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzSwitchComponent, NzSwitchModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,UAAU,EAAEC,WAAW,EAAEC,KAAK,EAAEC,KAAK,QAAQ,uBAAuB;AAC7E,SAASC,IAAI,QAAQ,iBAAiB;AACtC,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AACvI,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD,SAASC,OAAO,EAAEC,SAAS,QAAQ,MAAM;AACzC,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,OAAO,KAAKC,EAAE,MAAM,2BAA2B;AAC/C,SAASC,UAAU,QAAQ,2BAA2B;AACtD,OAAO,KAAKC,EAAE,MAAM,2BAA2B;AAC/C,SAASC,cAAc,QAAQ,2BAA2B;AAC1D,SAASC,YAAY,QAAQ,yBAAyB;AACtD,OAAO,KAAKC,EAAE,MAAM,yBAAyB;AAC7C,SAASC,YAAY,QAAQ,yBAAyB;AACtD,OAAO,KAAKC,EAAE,MAAM,oBAAoB;AACxC,SAASC,YAAY,QAAQ,oBAAoB;AACjD,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AAAC,MAAAC,GAAA;AAAA,SAAAC,kCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAkH4D1B,EAAE,CAAA4B,SAAA,aAuBP,CAAC;EAAA;AAAA;AAAA,SAAAC,yDAAAH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAvBI1B,EAAE,CAAA8B,uBAAA,EA2BnC,CAAC;IA3BgC9B,EAAE,CAAA+B,MAAA,EA2BZ,CAAC;IA3BS/B,EAAE,CAAAgC,qBAAA;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAO,MAAA,GAAFjC,EAAE,CAAAkC,aAAA;IAAFlC,EAAE,CAAAmC,SAAA,CA2BZ,CAAC;IA3BSnC,EAAE,CAAAoC,iBAAA,CAAAH,MAAA,CAAAI,iBA2BZ,CAAC;EAAA;AAAA;AAAA,SAAAC,0CAAAZ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA3BS1B,EAAE,CAAA8B,uBAAA,EA0BzC,CAAC;IA1BsC9B,EAAE,CAAAuC,UAAA,IAAAV,wDAAA,yBA2BnC,CAAC;IA3BgC7B,EAAE,CAAAgC,qBAAA;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAO,MAAA,GAAFjC,EAAE,CAAAkC,aAAA;IAAFlC,EAAE,CAAAmC,SAAA,CA2BrC,CAAC;IA3BkCnC,EAAE,CAAAwC,UAAA,2BAAAP,MAAA,CAAAI,iBA2BrC,CAAC;EAAA;AAAA;AAAA,SAAAI,wDAAAf,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA3BkC1B,EAAE,CAAA8B,uBAAA,EA8BjC,CAAC;IA9B8B9B,EAAE,CAAA+B,MAAA,EA8BR,CAAC;IA9BK/B,EAAE,CAAAgC,qBAAA;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAO,MAAA,GAAFjC,EAAE,CAAAkC,aAAA;IAAFlC,EAAE,CAAAmC,SAAA,CA8BR,CAAC;IA9BKnC,EAAE,CAAAoC,iBAAA,CAAAH,MAAA,CAAAS,mBA8BR,CAAC;EAAA;AAAA;AAAA,SAAAC,yCAAAjB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9BK1B,EAAE,CAAAuC,UAAA,IAAAE,uDAAA,yBA8BjC,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAO,MAAA,GA9B8BjC,EAAE,CAAAkC,aAAA;IAAFlC,EAAE,CAAAwC,UAAA,2BAAAP,MAAA,CAAAS,mBA8BnC,CAAC;EAAA;AAAA;AA9IpE,MAAME,qBAAqB,GAAG,QAAQ;AACtC,MAAMC,iBAAiB,CAAC;EACpBC,WAAWA,CAACC,KAAK,EAAE;IACf,IAAI,IAAI,CAACC,SAAS,KAAKD,KAAK,EAAE;MAC1B,IAAI,CAACC,SAAS,GAAGD,KAAK;MACtB,IAAI,CAACE,QAAQ,CAAC,IAAI,CAACD,SAAS,CAAC;IACjC;EACJ;EACAE,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACC,YAAY,CAACC,QAAQ,CAAC,IAAI,CAACC,aAAa,CAACC,aAAa,EAAE,UAAU,CAAC;EAC5E;EACAC,IAAIA,CAAA,EAAG;IACH,IAAI,CAACF,aAAa,CAACC,aAAa,CAACC,IAAI,CAAC,CAAC;EAC3C;EACAC,WAAWA,CAACC,eAAe,EAAEC,IAAI,EAAEC,MAAM,EAAEC,GAAG,EAAET,YAAY,EAAEU,cAAc,EAAE;IAC1E,IAAI,CAACJ,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACT,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACU,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,aAAa,GAAGlB,qBAAqB;IAC1C,IAAI,CAACI,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,QAAQ,GAAG,MAAM,CAAE,CAAC;IACzB,IAAI,CAACc,SAAS,GAAG,MAAM,CAAE,CAAC;IAC1B,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAAC7B,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACK,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACyB,MAAM,GAAG,SAAS;IACvB,IAAI,CAACC,IAAI,GAAG,IAAI;IAChB,IAAI,CAACC,GAAG,GAAG,KAAK;IAChB,IAAI,CAACC,QAAQ,GAAG,IAAI5D,OAAO,CAAC,CAAC;IAC7B,IAAI,CAAC6D,sBAAsB,GAAG,IAAI;EACtC;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACX,cAAc,CAACY,MAAM,CAACC,IAAI,CAAC9D,SAAS,CAAC,IAAI,CAAC0D,QAAQ,CAAC,CAAC,CAACK,SAAS,CAAEC,SAAS,IAAK;MAC/E,IAAI,CAACP,GAAG,GAAGO,SAAS;MACpB,IAAI,CAAChB,GAAG,CAACiB,aAAa,CAAC,CAAC;IAC5B,CAAC,CAAC;IACF,IAAI,CAACR,GAAG,GAAG,IAAI,CAACR,cAAc,CAACd,KAAK;IACpC,IAAI,CAACY,MAAM,CAACmB,iBAAiB,CAAC,MAAM;MAChCnE,SAAS,CAAC,IAAI,CAAC+C,IAAI,CAACJ,aAAa,EAAE,OAAO,CAAC,CACtCoB,IAAI,CAAC9D,SAAS,CAAC,IAAI,CAAC0D,QAAQ,CAAC,CAAC,CAC9BK,SAAS,CAACI,KAAK,IAAI;QACpBA,KAAK,CAACC,cAAc,CAAC,CAAC;QACtB,IAAI,IAAI,CAACd,SAAS,IAAI,IAAI,CAACD,UAAU,IAAI,IAAI,CAACD,SAAS,EAAE;UACrD;QACJ;QACA,IAAI,CAACL,MAAM,CAACsB,GAAG,CAAC,MAAM;UAClB,IAAI,CAACnC,WAAW,CAAC,CAAC,IAAI,CAACE,SAAS,CAAC;UACjC,IAAI,CAACY,GAAG,CAACsB,YAAY,CAAC,CAAC;QAC3B,CAAC,CAAC;MACN,CAAC,CAAC;MACFvE,SAAS,CAAC,IAAI,CAAC0C,aAAa,CAACC,aAAa,EAAE,SAAS,CAAC,CACjDoB,IAAI,CAAC9D,SAAS,CAAC,IAAI,CAAC0D,QAAQ,CAAC,CAAC,CAC9BK,SAAS,CAACI,KAAK,IAAI;QACpB,IAAI,IAAI,CAACb,SAAS,IAAI,IAAI,CAACD,UAAU,IAAI,IAAI,CAACD,SAAS,EAAE;UACrD;QACJ;QACA,MAAM;UAAEmB;QAAQ,CAAC,GAAGJ,KAAK;QACzB,IAAII,OAAO,KAAKxF,UAAU,IAAIwF,OAAO,KAAKvF,WAAW,IAAIuF,OAAO,KAAKtF,KAAK,IAAIsF,OAAO,KAAKrF,KAAK,EAAE;UAC7F;QACJ;QACAiF,KAAK,CAACC,cAAc,CAAC,CAAC;QACtB,IAAI,CAACrB,MAAM,CAACsB,GAAG,CAAC,MAAM;UAClB,IAAIE,OAAO,KAAKxF,UAAU,EAAE;YACxB,IAAI,CAACmD,WAAW,CAAC,KAAK,CAAC;UAC3B,CAAC,MACI,IAAIqC,OAAO,KAAKvF,WAAW,EAAE;YAC9B,IAAI,CAACkD,WAAW,CAAC,IAAI,CAAC;UAC1B,CAAC,MACI,IAAIqC,OAAO,KAAKtF,KAAK,IAAIsF,OAAO,KAAKrF,KAAK,EAAE;YAC7C,IAAI,CAACgD,WAAW,CAAC,CAAC,IAAI,CAACE,SAAS,CAAC;UACrC;UACA,IAAI,CAACY,GAAG,CAACsB,YAAY,CAAC,CAAC;QAC3B,CAAC,CAAC;MACN,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACAE,eAAeA,CAAA,EAAG;IACd,IAAI,CAACjC,YAAY,CACZkC,OAAO,CAAC,IAAI,CAAChC,aAAa,CAACC,aAAa,EAAE,IAAI,CAAC,CAC/CoB,IAAI,CAAC9D,SAAS,CAAC,IAAI,CAAC0D,QAAQ,CAAC,CAAC,CAC9BK,SAAS,CAACW,WAAW,IAAI;MAC1B,IAAI,CAACA,WAAW,EAAE;QACd;QACAC,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM,IAAI,CAAC1B,SAAS,CAAC,CAAC,CAAC;MAClD;IACJ,CAAC,CAAC;EACN;EACA2B,WAAWA,CAAA,EAAG;IACV,IAAI,CAACvC,YAAY,CAACwC,cAAc,CAAC,IAAI,CAACtC,aAAa,CAACC,aAAa,CAAC;IAClE,IAAI,CAACgB,QAAQ,CAACsB,IAAI,CAAC,CAAC;IACpB,IAAI,CAACtB,QAAQ,CAACuB,QAAQ,CAAC,CAAC;EAC5B;EACAC,UAAUA,CAAC/C,KAAK,EAAE;IACd,IAAI,CAACC,SAAS,GAAGD,KAAK;IACtB,IAAI,CAACa,GAAG,CAACsB,YAAY,CAAC,CAAC;EAC3B;EACAa,gBAAgBA,CAACC,EAAE,EAAE;IACjB,IAAI,CAAC/C,QAAQ,GAAG+C,EAAE;EACtB;EACAC,iBAAiBA,CAACD,EAAE,EAAE;IAClB,IAAI,CAACjC,SAAS,GAAGiC,EAAE;EACvB;EACAE,gBAAgBA,CAACC,QAAQ,EAAE;IACvB,IAAI,CAAClC,UAAU,GAAI,IAAI,CAACM,sBAAsB,IAAI,IAAI,CAACN,UAAU,IAAKkC,QAAQ;IAC9E,IAAI,CAAC5B,sBAAsB,GAAG,KAAK;IACnC,IAAI,CAACX,GAAG,CAACsB,YAAY,CAAC,CAAC;EAC3B;EACA;IAAS,IAAI,CAACkB,IAAI,YAAAC,0BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFzD,iBAAiB,EAA3B7C,EAAE,CAAAuG,iBAAA,CAA2C1F,EAAE,CAAC2F,eAAe,GAA/DxG,EAAE,CAAAuG,iBAAA,CAA0EvG,EAAE,CAACyG,UAAU,GAAzFzG,EAAE,CAAAuG,iBAAA,CAAoGvG,EAAE,CAAC0G,MAAM,GAA/G1G,EAAE,CAAAuG,iBAAA,CAA0HvG,EAAE,CAAC2G,iBAAiB,GAAhJ3G,EAAE,CAAAuG,iBAAA,CAA2JjF,EAAE,CAACsF,YAAY,GAA5K5G,EAAE,CAAAuG,iBAAA,CAAuLhF,EAAE,CAACsF,cAAc;IAAA,CAA4D;EAAE;EACxW;IAAS,IAAI,CAACC,IAAI,kBAD8E9G,EAAE,CAAA+G,iBAAA;MAAAC,IAAA,EACJnE,iBAAiB;MAAAoE,SAAA;MAAAC,SAAA,WAAAC,wBAAAzF,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADf1B,EAAE,CAAAoH,WAAA,CAAA5F,GAAA;QAAA;QAAA,IAAAE,EAAA;UAAA,IAAA2F,EAAA;UAAFrH,EAAE,CAAAsH,cAAA,CAAAD,EAAA,GAAFrH,EAAE,CAAAuH,WAAA,QAAA5F,GAAA,CAAA0B,aAAA,GAAAgE,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAC,MAAA;QAAAzD,SAAA;QAAAC,UAAA;QAAAC,SAAA;QAAA7B,iBAAA;QAAAK,mBAAA;QAAAyB,MAAA;QAAAC,IAAA;MAAA;MAAAsD,QAAA;MAAAC,UAAA;MAAAC,QAAA,GAAF5H,EAAE,CAAA6H,kBAAA,CAC+Q,CACzW;QACIC,OAAO,EAAErH,iBAAiB;QAC1BsH,WAAW,EAAE9H,UAAU,CAAC,MAAM4C,iBAAiB,CAAC;QAChDmF,KAAK,EAAE;MACX,CAAC,CACJ,GAP2FhI,EAAE,CAAAiI,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAA5G,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF1B,EAAE,CAAAuI,cAAA,kBAqBlG,CAAC,aACgC,CAAC;UAtB8DvI,EAAE,CAAAuC,UAAA,IAAAd,iCAAA,iBAuBd,CAAC;UAvBWzB,EAAE,CAAAwI,YAAA,CAwB1F,CAAC;UAxBuFxI,EAAE,CAAAuI,cAAA,aAyBlE,CAAC;UAzB+DvI,EAAE,CAAAuC,UAAA,IAAAD,yCAAA,yBA0BzC,CAAC,IAAAK,wCAAA,gCA1BsC3C,EAAE,CAAAyI,sBA6BjE,CAAC;UA7B8DzI,EAAE,CAAAwI,YAAA,CAgC1F,CAAC;UAhCuFxI,EAAE,CAAA4B,SAAA,YAiCrD,CAAC;UAjCkD5B,EAAE,CAAAwI,YAAA,CAkC1F,CAAC;QAAA;QAAA,IAAA9G,EAAA;UAAA,MAAAgH,kBAAA,GAlCuF1I,EAAE,CAAA2I,WAAA;UAAF3I,EAAE,CAAA4I,WAAA,uBAAAjH,GAAA,CAAAqB,SAe3D,CAAC,uBAAArB,GAAA,CAAAqC,SACD,CAAC,wBAAArC,GAAA,CAAAsC,UACC,CAAC,qBAAAtC,GAAA,CAAAwC,MAAA,YACI,CAAC,mBAAAxC,GAAA,CAAA0C,GAAA,UACR,CAAC;UAnBwDrE,EAAE,CAAAwC,UAAA,aAAAb,GAAA,CAAAsC,UAc1E,CAAC,wBAMA,CAAC;UApBsEjE,EAAE,CAAA6I,WAAA,OAAAlH,GAAA,CAAAyC,IAAA;UAAFpE,EAAE,CAAAmC,SAAA,EAuBzE,CAAC;UAvBsEnC,EAAE,CAAAwC,UAAA,SAAAb,GAAA,CAAAqC,SAuBzE,CAAC;UAvBsEhE,EAAE,CAAAmC,SAAA,EA0B/D,CAAC;UA1B4DnC,EAAE,CAAAwC,UAAA,SAAAb,GAAA,CAAAqB,SA0B/D,CAAC,aAAA0F,kBAAmB,CAAC;QAAA;MAAA;MAAAI,YAAA,GASE3H,YAAY,EAA+BD,EAAE,CAAC6H,eAAe,EAAwK1H,YAAY,EAA+BD,EAAE,CAAC4H,eAAe,EAAiKjJ,IAAI,EAA4FiB,cAAc,EAA+BD,EAAE,CAACkI,+BAA+B;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAAqP;EAAE;AACv6B;AACAzJ,UAAU,CAAC,CACPuB,YAAY,CAAC,CAAC,CACjB,EAAE4B,iBAAiB,CAACuG,SAAS,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC;AACpD1J,UAAU,CAAC,CACPuB,YAAY,CAAC,CAAC,CACjB,EAAE4B,iBAAiB,CAACuG,SAAS,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC;AACrD1J,UAAU,CAAC,CACPuB,YAAY,CAAC,CAAC,CACjB,EAAE4B,iBAAiB,CAACuG,SAAS,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC;AACpD1J,UAAU,CAAC,CACPoB,UAAU,CAAC,CAAC,CACf,EAAE+B,iBAAiB,CAACuG,SAAS,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;AACjD;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAjDoGrJ,EAAE,CAAAsJ,iBAAA,CAiDXzG,iBAAiB,EAAc,CAAC;IAC/GmE,IAAI,EAAE9G,SAAS;IACfqJ,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,WAAW;MACrB9B,QAAQ,EAAE,UAAU;MACpB+B,mBAAmB,EAAE,KAAK;MAC1BN,eAAe,EAAEhJ,uBAAuB,CAACuJ,MAAM;MAC/CR,aAAa,EAAE9I,iBAAiB,CAACuJ,IAAI;MACrCC,SAAS,EAAE,CACP;QACI9B,OAAO,EAAErH,iBAAiB;QAC1BsH,WAAW,EAAE9H,UAAU,CAAC,MAAM4C,iBAAiB,CAAC;QAChDmF,KAAK,EAAE;MACX,CAAC,CACJ;MACDK,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiBwB,OAAO,EAAE,CAAC1I,YAAY,EAAEE,YAAY,EAAEtB,IAAI,EAAEiB,cAAc,CAAC;MAC3D2G,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEX,IAAI,EAAEnG,EAAE,CAAC2F;EAAgB,CAAC,EAAE;IAAEQ,IAAI,EAAEhH,EAAE,CAACyG;EAAW,CAAC,EAAE;IAAEO,IAAI,EAAEhH,EAAE,CAAC0G;EAAO,CAAC,EAAE;IAAEM,IAAI,EAAEhH,EAAE,CAAC2G;EAAkB,CAAC,EAAE;IAAEK,IAAI,EAAE1F,EAAE,CAACsF;EAAa,CAAC,EAAE;IAAEI,IAAI,EAAEzF,EAAE,CAACsF,cAAc;IAAEiD,UAAU,EAAE,CAAC;MAC9L9C,IAAI,EAAE3G;IACV,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEgD,aAAa,EAAE,CAAC;MACzC2D,IAAI,EAAE1G,SAAS;MACfiJ,IAAI,EAAE,CAAC,eAAe,EAAE;QAAEQ,MAAM,EAAE;MAAK,CAAC;IAC5C,CAAC,CAAC;IAAE/F,SAAS,EAAE,CAAC;MACZgD,IAAI,EAAEzG;IACV,CAAC,CAAC;IAAE0D,UAAU,EAAE,CAAC;MACb+C,IAAI,EAAEzG;IACV,CAAC,CAAC;IAAE2D,SAAS,EAAE,CAAC;MACZ8C,IAAI,EAAEzG;IACV,CAAC,CAAC;IAAE8B,iBAAiB,EAAE,CAAC;MACpB2E,IAAI,EAAEzG;IACV,CAAC,CAAC;IAAEmC,mBAAmB,EAAE,CAAC;MACtBsE,IAAI,EAAEzG;IACV,CAAC,CAAC;IAAE4D,MAAM,EAAE,CAAC;MACT6C,IAAI,EAAEzG;IACV,CAAC,CAAC;IAAE6D,IAAI,EAAE,CAAC;MACP4C,IAAI,EAAEzG;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAMyJ,cAAc,CAAC;EACjB;IAAS,IAAI,CAAC5D,IAAI,YAAA6D,uBAAA3D,CAAA;MAAA,YAAAA,CAAA,IAAwF0D,cAAc;IAAA,CAAkD;EAAE;EAC5K;IAAS,IAAI,CAACE,IAAI,kBA3H8ElK,EAAE,CAAAmK,gBAAA;MAAAnD,IAAA,EA2HSgD,cAAc;MAAAH,OAAA,GAAYhH,iBAAiB;MAAAuH,OAAA,GAAavH,iBAAiB;IAAA,EAAI;EAAE;EAC1L;IAAS,IAAI,CAACwH,IAAI,kBA5H8ErK,EAAE,CAAAsK,gBAAA;MAAAT,OAAA,GA4HmChH,iBAAiB;IAAA,EAAI;EAAE;AAChK;AACA;EAAA,QAAAwG,SAAA,oBAAAA,SAAA,KA9HoGrJ,EAAE,CAAAsJ,iBAAA,CA8HXU,cAAc,EAAc,CAAC;IAC5GhD,IAAI,EAAExG,QAAQ;IACd+I,IAAI,EAAE,CAAC;MACCM,OAAO,EAAE,CAAChH,iBAAiB,CAAC;MAC5BuH,OAAO,EAAE,CAACvH,iBAAiB;IAC/B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASA,iBAAiB,EAAEmH,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}