{"ast": null, "code": "(function () {\n  \"use strict\";\n\n  try {\n    if (typeof document < \"u\") {\n      var e = document.createElement(\"style\");\n      e.appendChild(document.createTextNode('.cdx-checklist{gap:6px;display:flex;flex-direction:column}.cdx-checklist__item{display:flex;box-sizing:content-box;align-items:flex-start}.cdx-checklist__item-text{outline:none;flex-grow:1;line-height:1.57em}.cdx-checklist__item-checkbox{width:22px;height:22px;display:flex;align-items:center;margin-right:8px;margin-top:calc(.785em - 11px);cursor:pointer}.cdx-checklist__item-checkbox svg{opacity:0;height:20px;width:20px;position:absolute;left:-1px;top:-1px;max-height:20px}@media (hover: hover){.cdx-checklist__item-checkbox:not(.cdx-checklist__item-checkbox--no-hover):hover .cdx-checklist__item-checkbox-check svg{opacity:1}}.cdx-checklist__item-checkbox-check{cursor:pointer;display:inline-block;flex-shrink:0;position:relative;width:20px;height:20px;box-sizing:border-box;margin-left:0;border-radius:5px;border:1px solid #C9C9C9;background:#fff}.cdx-checklist__item-checkbox-check:before{content:\"\";position:absolute;top:0;right:0;bottom:0;left:0;border-radius:100%;background-color:#369fff;visibility:hidden;pointer-events:none;transform:scale(1);transition:transform .4s ease-out,opacity .4s}@media (hover: hover){.cdx-checklist__item--checked .cdx-checklist__item-checkbox:not(.cdx-checklist__item--checked .cdx-checklist__item-checkbox--no-hover):hover .cdx-checklist__item-checkbox-check{background:#0059AB;border-color:#0059ab}}.cdx-checklist__item--checked .cdx-checklist__item-checkbox-check{background:#369FFF;border-color:#369fff}.cdx-checklist__item--checked .cdx-checklist__item-checkbox-check svg{opacity:1}.cdx-checklist__item--checked .cdx-checklist__item-checkbox-check svg path{stroke:#fff}.cdx-checklist__item--checked .cdx-checklist__item-checkbox-check:before{opacity:0;visibility:visible;transform:scale(2.5)}')), document.head.appendChild(e);\n    }\n  } catch (c) {\n    console.error(\"vite-plugin-css-injected-by-js\", c);\n  }\n})();\nconst k = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M7 12L10.4884 15.8372C10.5677 15.9245 10.705 15.9245 10.7844 15.8372L17 9\"/></svg>',\n  g = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M9.2 12L11.0586 13.8586C11.1367 13.9367 11.2633 13.9367 11.3414 13.8586L14.7 10.5\"/><rect width=\"14\" height=\"14\" x=\"5\" y=\"5\" stroke=\"currentColor\" stroke-width=\"2\" rx=\"4\"/></svg>';\nfunction d() {\n  const s = document.activeElement,\n    t = window.getSelection().getRangeAt(0),\n    n = t.cloneRange();\n  return n.selectNodeContents(s), n.setStart(t.endContainer, t.endOffset), n.extractContents();\n}\nfunction C(s) {\n  const e = document.createElement(\"div\");\n  return e.appendChild(s), e.innerHTML;\n}\nfunction c(s, e = null, t = {}) {\n  const n = document.createElement(s);\n  Array.isArray(e) ? n.classList.add(...e) : e && n.classList.add(e);\n  for (const i in t) n[i] = t[i];\n  return n;\n}\nfunction m(s) {\n  return s.innerHTML.replace(\"<br>\", \" \").trim();\n}\nfunction p(s, e = !1, t = void 0) {\n  const n = document.createRange(),\n    i = window.getSelection();\n  n.selectNodeContents(s), t !== void 0 && (n.setStart(s, t), n.setEnd(s, t)), n.collapse(e), i.removeAllRanges(), i.addRange(n);\n}\nElement.prototype.matches || (Element.prototype.matches = Element.prototype.msMatchesSelector || Element.prototype.webkitMatchesSelector);\nElement.prototype.closest || (Element.prototype.closest = function (s) {\n  let e = this;\n  if (!document.documentElement.contains(e)) return null;\n  do {\n    if (e.matches(s)) return e;\n    e = e.parentElement || e.parentNode;\n  } while (e !== null && e.nodeType === 1);\n  return null;\n});\nclass f {\n  /**\n   * Notify core that read-only mode is supported\n   *\n   * @returns {boolean}\n   */\n  static get isReadOnlySupported() {\n    return !0;\n  }\n  /**\n   * Allow to use native Enter behaviour\n   *\n   * @returns {boolean}\n   * @public\n   */\n  static get enableLineBreaks() {\n    return !0;\n  }\n  /**\n   * Get Tool toolbox settings\n   * icon - Tool icon's SVG\n   * title - title to show in toolbox\n   *\n   * @returns {{icon: string, title: string}}\n   */\n  static get toolbox() {\n    return {\n      icon: g,\n      title: \"Checklist\"\n    };\n  }\n  /**\n   * Allow Checkbox Tool to be converted to/from other block\n   *\n   * @returns {{export: Function, import: Function}}\n   */\n  static get conversionConfig() {\n    return {\n      /**\n       * To create exported string from the checkbox, concatenate items by dot-symbol.\n       *\n       * @param {ChecklistData} data - checklist data to create a string from that\n       * @returns {string}\n       */\n      export: e => e.items.map(({\n        text: t\n      }) => t).join(\". \"),\n      /**\n       * To create a checklist from other block's string, just put it at the first item\n       *\n       * @param {string} string - string to create list tool data from that\n       * @returns {ChecklistData}\n       */\n      import: e => ({\n        items: [{\n          text: e,\n          checked: !1\n        }]\n      })\n    };\n  }\n  /**\n   * Render plugin`s main Element and fill it with saved data\n   *\n   * @param {object} options - block constructor options\n   * @param {ChecklistData} options.data - previously saved data\n   * @param {object} options.config - user config for Tool\n   * @param {object} options.api - Editor.js API\n   * @param {boolean} options.readOnly - read only mode flag\n   */\n  constructor({\n    data: e,\n    config: t,\n    api: n,\n    readOnly: i\n  }) {\n    this._elements = {\n      wrapper: null,\n      items: []\n    }, this.readOnly = i, this.api = n, this.data = e || {};\n  }\n  /**\n   * Returns checklist tag with items\n   *\n   * @returns {Element}\n   */\n  render() {\n    return this._elements.wrapper = c(\"div\", [this.CSS.baseBlock, this.CSS.wrapper]), this.data.items || (this.data.items = [{\n      text: \"\",\n      checked: !1\n    }]), this.data.items.forEach(e => {\n      const t = this.createChecklistItem(e);\n      this._elements.wrapper.appendChild(t);\n    }), this.readOnly ? this._elements.wrapper : (this._elements.wrapper.addEventListener(\"keydown\", e => {\n      const [t, n] = [13, 8];\n      switch (e.keyCode) {\n        case t:\n          this.enterPressed(e);\n          break;\n        case n:\n          this.backspace(e);\n          break;\n      }\n    }, !1), this._elements.wrapper.addEventListener(\"click\", e => {\n      this.toggleCheckbox(e);\n    }), this._elements.wrapper);\n  }\n  /**\n   * Return Checklist data\n   *\n   * @returns {ChecklistData}\n   */\n  save() {\n    let e = this.items.map(t => {\n      const n = this.getItemInput(t);\n      return {\n        text: m(n),\n        checked: t.classList.contains(this.CSS.itemChecked)\n      };\n    });\n    return e = e.filter(t => t.text.trim().length !== 0), {\n      items: e\n    };\n  }\n  /**\n   * Validate data: check if Checklist has items\n   *\n   * @param {ChecklistData} savedData — data received after saving\n   * @returns {boolean} false if saved data is not correct, otherwise true\n   * @public\n   */\n  validate(e) {\n    return !!e.items.length;\n  }\n  /**\n   * Toggle checklist item state\n   *\n   * @param {MouseEvent} event - click\n   * @returns {void}\n   */\n  toggleCheckbox(e) {\n    const t = e.target.closest(`.${this.CSS.item}`),\n      n = t.querySelector(`.${this.CSS.checkboxContainer}`);\n    n.contains(e.target) && (t.classList.toggle(this.CSS.itemChecked), n.classList.add(this.CSS.noHover), n.addEventListener(\"mouseleave\", () => this.removeSpecialHoverBehavior(n), {\n      once: !0\n    }));\n  }\n  /**\n   * Create Checklist items\n   *\n   * @param {ChecklistItem} item - data.item\n   * @returns {Element} checkListItem - new element of checklist\n   */\n  createChecklistItem(e = {}) {\n    const t = c(\"div\", this.CSS.item),\n      n = c(\"span\", this.CSS.checkbox),\n      i = c(\"div\", this.CSS.checkboxContainer),\n      o = c(\"div\", this.CSS.textField, {\n        innerHTML: e.text ? e.text : \"\",\n        contentEditable: !this.readOnly\n      });\n    return e.checked && t.classList.add(this.CSS.itemChecked), n.innerHTML = k, i.appendChild(n), t.appendChild(i), t.appendChild(o), t;\n  }\n  /**\n   * Append new elements to the list by pressing Enter\n   *\n   * @param {KeyboardEvent} event - keyboard event\n   */\n  enterPressed(e) {\n    e.preventDefault();\n    const t = this.items,\n      n = document.activeElement.closest(`.${this.CSS.item}`);\n    if (t.indexOf(n) === t.length - 1 && m(this.getItemInput(n)).length === 0) {\n      const u = this.api.blocks.getCurrentBlockIndex();\n      n.remove(), this.api.blocks.insert(), this.api.caret.setToBlock(u + 1);\n      return;\n    }\n    const a = d(),\n      l = C(a),\n      r = this.createChecklistItem({\n        text: l,\n        checked: !1\n      });\n    this._elements.wrapper.insertBefore(r, n.nextSibling), p(this.getItemInput(r), !0);\n  }\n  /**\n   * Handle backspace\n   *\n   * @param {KeyboardEvent} event - keyboard event\n   */\n  backspace(e) {\n    const t = e.target.closest(`.${this.CSS.item}`),\n      n = this.items.indexOf(t),\n      i = this.items[n - 1];\n    if (!i || !(window.getSelection().focusOffset === 0)) return;\n    e.preventDefault();\n    const l = d(),\n      r = this.getItemInput(i),\n      h = r.childNodes.length;\n    r.appendChild(l), p(r, void 0, h), t.remove();\n  }\n  /**\n   * Styles\n   *\n   * @private\n   * @returns {object<string>}\n   */\n  get CSS() {\n    return {\n      baseBlock: this.api.styles.block,\n      wrapper: \"cdx-checklist\",\n      item: \"cdx-checklist__item\",\n      itemChecked: \"cdx-checklist__item--checked\",\n      noHover: \"cdx-checklist__item-checkbox--no-hover\",\n      checkbox: \"cdx-checklist__item-checkbox-check\",\n      textField: \"cdx-checklist__item-text\",\n      checkboxContainer: \"cdx-checklist__item-checkbox\"\n    };\n  }\n  /**\n   * Return all items elements\n   *\n   * @returns {Element[]}\n   */\n  get items() {\n    return Array.from(this._elements.wrapper.querySelectorAll(`.${this.CSS.item}`));\n  }\n  /**\n   * Removes class responsible for special hover behavior on an item\n   * \n   * @private\n   * @param {Element} el - item wrapper\n   * @returns {Element}\n   */\n  removeSpecialHoverBehavior(e) {\n    e.classList.remove(this.CSS.noHover);\n  }\n  /**\n   * Find and return item's content editable element\n   *\n   * @private\n   * @param {Element} el - item wrapper\n   * @returns {Element}\n   */\n  getItemInput(e) {\n    return e.querySelector(`.${this.CSS.textField}`);\n  }\n}\nexport { f as default };", "map": {"version": 3, "names": ["document", "e", "createElement", "append<PERSON><PERSON><PERSON>", "createTextNode", "head", "c", "console", "error", "k", "g", "d", "s", "activeElement", "t", "window", "getSelection", "getRangeAt", "n", "cloneRange", "selectNodeContents", "setStart", "endContainer", "endOffset", "extractContents", "C", "innerHTML", "Array", "isArray", "classList", "add", "i", "m", "replace", "trim", "p", "createRange", "setEnd", "collapse", "removeAllRanges", "addRange", "Element", "prototype", "matches", "msMatchesSelector", "webkitMatchesSelector", "closest", "documentElement", "contains", "parentElement", "parentNode", "nodeType", "f", "isReadOnlySupported", "enableLineBreaks", "toolbox", "icon", "title", "conversionConfig", "export", "items", "map", "text", "join", "import", "checked", "constructor", "data", "config", "api", "readOnly", "_elements", "wrapper", "render", "CSS", "baseBlock", "for<PERSON>ach", "createChecklistItem", "addEventListener", "keyCode", "enterPressed", "backspace", "toggleCheckbox", "save", "getItemInput", "itemChecked", "filter", "length", "validate", "target", "item", "querySelector", "checkboxContainer", "toggle", "noHover", "removeSpecialHoverBehavior", "once", "checkbox", "o", "textField", "contentEditable", "preventDefault", "indexOf", "u", "blocks", "getCurrentBlockIndex", "remove", "insert", "caret", "setT<PERSON><PERSON><PERSON>", "a", "l", "r", "insertBefore", "nextS<PERSON>ling", "focusOffset", "h", "childNodes", "styles", "block", "from", "querySelectorAll", "default"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/@editorjs/checklist/dist/checklist.mjs"], "sourcesContent": ["(function(){\"use strict\";try{if(typeof document<\"u\"){var e=document.createElement(\"style\");e.appendChild(document.createTextNode('.cdx-checklist{gap:6px;display:flex;flex-direction:column}.cdx-checklist__item{display:flex;box-sizing:content-box;align-items:flex-start}.cdx-checklist__item-text{outline:none;flex-grow:1;line-height:1.57em}.cdx-checklist__item-checkbox{width:22px;height:22px;display:flex;align-items:center;margin-right:8px;margin-top:calc(.785em - 11px);cursor:pointer}.cdx-checklist__item-checkbox svg{opacity:0;height:20px;width:20px;position:absolute;left:-1px;top:-1px;max-height:20px}@media (hover: hover){.cdx-checklist__item-checkbox:not(.cdx-checklist__item-checkbox--no-hover):hover .cdx-checklist__item-checkbox-check svg{opacity:1}}.cdx-checklist__item-checkbox-check{cursor:pointer;display:inline-block;flex-shrink:0;position:relative;width:20px;height:20px;box-sizing:border-box;margin-left:0;border-radius:5px;border:1px solid #C9C9C9;background:#fff}.cdx-checklist__item-checkbox-check:before{content:\"\";position:absolute;top:0;right:0;bottom:0;left:0;border-radius:100%;background-color:#369fff;visibility:hidden;pointer-events:none;transform:scale(1);transition:transform .4s ease-out,opacity .4s}@media (hover: hover){.cdx-checklist__item--checked .cdx-checklist__item-checkbox:not(.cdx-checklist__item--checked .cdx-checklist__item-checkbox--no-hover):hover .cdx-checklist__item-checkbox-check{background:#0059AB;border-color:#0059ab}}.cdx-checklist__item--checked .cdx-checklist__item-checkbox-check{background:#369FFF;border-color:#369fff}.cdx-checklist__item--checked .cdx-checklist__item-checkbox-check svg{opacity:1}.cdx-checklist__item--checked .cdx-checklist__item-checkbox-check svg path{stroke:#fff}.cdx-checklist__item--checked .cdx-checklist__item-checkbox-check:before{opacity:0;visibility:visible;transform:scale(2.5)}')),document.head.appendChild(e)}}catch(c){console.error(\"vite-plugin-css-injected-by-js\",c)}})();\nconst k = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M7 12L10.4884 15.8372C10.5677 15.9245 10.705 15.9245 10.7844 15.8372L17 9\"/></svg>', g = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M9.2 12L11.0586 13.8586C11.1367 13.9367 11.2633 13.9367 11.3414 13.8586L14.7 10.5\"/><rect width=\"14\" height=\"14\" x=\"5\" y=\"5\" stroke=\"currentColor\" stroke-width=\"2\" rx=\"4\"/></svg>';\nfunction d() {\n  const s = document.activeElement, t = window.getSelection().getRangeAt(0), n = t.cloneRange();\n  return n.selectNodeContents(s), n.setStart(t.endContainer, t.endOffset), n.extractContents();\n}\nfunction C(s) {\n  const e = document.createElement(\"div\");\n  return e.appendChild(s), e.innerHTML;\n}\nfunction c(s, e = null, t = {}) {\n  const n = document.createElement(s);\n  Array.isArray(e) ? n.classList.add(...e) : e && n.classList.add(e);\n  for (const i in t)\n    n[i] = t[i];\n  return n;\n}\nfunction m(s) {\n  return s.innerHTML.replace(\"<br>\", \" \").trim();\n}\nfunction p(s, e = !1, t = void 0) {\n  const n = document.createRange(), i = window.getSelection();\n  n.selectNodeContents(s), t !== void 0 && (n.setStart(s, t), n.setEnd(s, t)), n.collapse(e), i.removeAllRanges(), i.addRange(n);\n}\nElement.prototype.matches || (Element.prototype.matches = Element.prototype.msMatchesSelector || Element.prototype.webkitMatchesSelector);\nElement.prototype.closest || (Element.prototype.closest = function(s) {\n  let e = this;\n  if (!document.documentElement.contains(e))\n    return null;\n  do {\n    if (e.matches(s))\n      return e;\n    e = e.parentElement || e.parentNode;\n  } while (e !== null && e.nodeType === 1);\n  return null;\n});\nclass f {\n  /**\n   * Notify core that read-only mode is supported\n   *\n   * @returns {boolean}\n   */\n  static get isReadOnlySupported() {\n    return !0;\n  }\n  /**\n   * Allow to use native Enter behaviour\n   *\n   * @returns {boolean}\n   * @public\n   */\n  static get enableLineBreaks() {\n    return !0;\n  }\n  /**\n   * Get Tool toolbox settings\n   * icon - Tool icon's SVG\n   * title - title to show in toolbox\n   *\n   * @returns {{icon: string, title: string}}\n   */\n  static get toolbox() {\n    return {\n      icon: g,\n      title: \"Checklist\"\n    };\n  }\n  /**\n   * Allow Checkbox Tool to be converted to/from other block\n   *\n   * @returns {{export: Function, import: Function}}\n   */\n  static get conversionConfig() {\n    return {\n      /**\n       * To create exported string from the checkbox, concatenate items by dot-symbol.\n       *\n       * @param {ChecklistData} data - checklist data to create a string from that\n       * @returns {string}\n       */\n      export: (e) => e.items.map(({ text: t }) => t).join(\". \"),\n      /**\n       * To create a checklist from other block's string, just put it at the first item\n       *\n       * @param {string} string - string to create list tool data from that\n       * @returns {ChecklistData}\n       */\n      import: (e) => ({\n        items: [\n          {\n            text: e,\n            checked: !1\n          }\n        ]\n      })\n    };\n  }\n  /**\n   * Render plugin`s main Element and fill it with saved data\n   *\n   * @param {object} options - block constructor options\n   * @param {ChecklistData} options.data - previously saved data\n   * @param {object} options.config - user config for Tool\n   * @param {object} options.api - Editor.js API\n   * @param {boolean} options.readOnly - read only mode flag\n   */\n  constructor({ data: e, config: t, api: n, readOnly: i }) {\n    this._elements = {\n      wrapper: null,\n      items: []\n    }, this.readOnly = i, this.api = n, this.data = e || {};\n  }\n  /**\n   * Returns checklist tag with items\n   *\n   * @returns {Element}\n   */\n  render() {\n    return this._elements.wrapper = c(\"div\", [this.CSS.baseBlock, this.CSS.wrapper]), this.data.items || (this.data.items = [\n      {\n        text: \"\",\n        checked: !1\n      }\n    ]), this.data.items.forEach((e) => {\n      const t = this.createChecklistItem(e);\n      this._elements.wrapper.appendChild(t);\n    }), this.readOnly ? this._elements.wrapper : (this._elements.wrapper.addEventListener(\"keydown\", (e) => {\n      const [t, n] = [13, 8];\n      switch (e.keyCode) {\n        case t:\n          this.enterPressed(e);\n          break;\n        case n:\n          this.backspace(e);\n          break;\n      }\n    }, !1), this._elements.wrapper.addEventListener(\"click\", (e) => {\n      this.toggleCheckbox(e);\n    }), this._elements.wrapper);\n  }\n  /**\n   * Return Checklist data\n   *\n   * @returns {ChecklistData}\n   */\n  save() {\n    let e = this.items.map((t) => {\n      const n = this.getItemInput(t);\n      return {\n        text: m(n),\n        checked: t.classList.contains(this.CSS.itemChecked)\n      };\n    });\n    return e = e.filter((t) => t.text.trim().length !== 0), {\n      items: e\n    };\n  }\n  /**\n   * Validate data: check if Checklist has items\n   *\n   * @param {ChecklistData} savedData — data received after saving\n   * @returns {boolean} false if saved data is not correct, otherwise true\n   * @public\n   */\n  validate(e) {\n    return !!e.items.length;\n  }\n  /**\n   * Toggle checklist item state\n   *\n   * @param {MouseEvent} event - click\n   * @returns {void}\n   */\n  toggleCheckbox(e) {\n    const t = e.target.closest(`.${this.CSS.item}`), n = t.querySelector(`.${this.CSS.checkboxContainer}`);\n    n.contains(e.target) && (t.classList.toggle(this.CSS.itemChecked), n.classList.add(this.CSS.noHover), n.addEventListener(\"mouseleave\", () => this.removeSpecialHoverBehavior(n), { once: !0 }));\n  }\n  /**\n   * Create Checklist items\n   *\n   * @param {ChecklistItem} item - data.item\n   * @returns {Element} checkListItem - new element of checklist\n   */\n  createChecklistItem(e = {}) {\n    const t = c(\"div\", this.CSS.item), n = c(\"span\", this.CSS.checkbox), i = c(\"div\", this.CSS.checkboxContainer), o = c(\"div\", this.CSS.textField, {\n      innerHTML: e.text ? e.text : \"\",\n      contentEditable: !this.readOnly\n    });\n    return e.checked && t.classList.add(this.CSS.itemChecked), n.innerHTML = k, i.appendChild(n), t.appendChild(i), t.appendChild(o), t;\n  }\n  /**\n   * Append new elements to the list by pressing Enter\n   *\n   * @param {KeyboardEvent} event - keyboard event\n   */\n  enterPressed(e) {\n    e.preventDefault();\n    const t = this.items, n = document.activeElement.closest(`.${this.CSS.item}`);\n    if (t.indexOf(n) === t.length - 1 && m(this.getItemInput(n)).length === 0) {\n      const u = this.api.blocks.getCurrentBlockIndex();\n      n.remove(), this.api.blocks.insert(), this.api.caret.setToBlock(u + 1);\n      return;\n    }\n    const a = d(), l = C(a), r = this.createChecklistItem({\n      text: l,\n      checked: !1\n    });\n    this._elements.wrapper.insertBefore(r, n.nextSibling), p(this.getItemInput(r), !0);\n  }\n  /**\n   * Handle backspace\n   *\n   * @param {KeyboardEvent} event - keyboard event\n   */\n  backspace(e) {\n    const t = e.target.closest(`.${this.CSS.item}`), n = this.items.indexOf(t), i = this.items[n - 1];\n    if (!i || !(window.getSelection().focusOffset === 0))\n      return;\n    e.preventDefault();\n    const l = d(), r = this.getItemInput(i), h = r.childNodes.length;\n    r.appendChild(l), p(r, void 0, h), t.remove();\n  }\n  /**\n   * Styles\n   *\n   * @private\n   * @returns {object<string>}\n   */\n  get CSS() {\n    return {\n      baseBlock: this.api.styles.block,\n      wrapper: \"cdx-checklist\",\n      item: \"cdx-checklist__item\",\n      itemChecked: \"cdx-checklist__item--checked\",\n      noHover: \"cdx-checklist__item-checkbox--no-hover\",\n      checkbox: \"cdx-checklist__item-checkbox-check\",\n      textField: \"cdx-checklist__item-text\",\n      checkboxContainer: \"cdx-checklist__item-checkbox\"\n    };\n  }\n  /**\n   * Return all items elements\n   *\n   * @returns {Element[]}\n   */\n  get items() {\n    return Array.from(this._elements.wrapper.querySelectorAll(`.${this.CSS.item}`));\n  }\n  /**\n   * Removes class responsible for special hover behavior on an item\n   * \n   * @private\n   * @param {Element} el - item wrapper\n   * @returns {Element}\n   */\n  removeSpecialHoverBehavior(e) {\n    e.classList.remove(this.CSS.noHover);\n  }\n  /**\n   * Find and return item's content editable element\n   *\n   * @private\n   * @param {Element} el - item wrapper\n   * @returns {Element}\n   */\n  getItemInput(e) {\n    return e.querySelector(`.${this.CSS.textField}`);\n  }\n}\nexport {\n  f as default\n};\n"], "mappings": "AAAA,CAAC,YAAU;EAAC,YAAY;;EAAC,IAAG;IAAC,IAAG,OAAOA,QAAQ,GAAC,GAAG,EAAC;MAAC,IAAIC,CAAC,GAACD,QAAQ,CAACE,aAAa,CAAC,OAAO,CAAC;MAACD,CAAC,CAACE,WAAW,CAACH,QAAQ,CAACI,cAAc,CAAC,2sDAA2sD,CAAC,CAAC,EAACJ,QAAQ,CAACK,IAAI,CAACF,WAAW,CAACF,CAAC,CAAC;IAAA;EAAC,CAAC,QAAMK,CAAC,EAAC;IAACC,OAAO,CAACC,KAAK,CAAC,gCAAgC,EAACF,CAAC,CAAC;EAAA;AAAC,CAAC,EAAE,CAAC;AAC56D,MAAMG,CAAC,GAAG,0PAA0P;EAAEC,CAAC,GAAG,0VAA0V;AACpmB,SAASC,CAACA,CAAA,EAAG;EACX,MAAMC,CAAC,GAAGZ,QAAQ,CAACa,aAAa;IAAEC,CAAC,GAAGC,MAAM,CAACC,YAAY,CAAC,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC;IAAEC,CAAC,GAAGJ,CAAC,CAACK,UAAU,CAAC,CAAC;EAC7F,OAAOD,CAAC,CAACE,kBAAkB,CAACR,CAAC,CAAC,EAAEM,CAAC,CAACG,QAAQ,CAACP,CAAC,CAACQ,YAAY,EAAER,CAAC,CAACS,SAAS,CAAC,EAAEL,CAAC,CAACM,eAAe,CAAC,CAAC;AAC9F;AACA,SAASC,CAACA,CAACb,CAAC,EAAE;EACZ,MAAMX,CAAC,GAAGD,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC;EACvC,OAAOD,CAAC,CAACE,WAAW,CAACS,CAAC,CAAC,EAAEX,CAAC,CAACyB,SAAS;AACtC;AACA,SAASpB,CAACA,CAACM,CAAC,EAAEX,CAAC,GAAG,IAAI,EAAEa,CAAC,GAAG,CAAC,CAAC,EAAE;EAC9B,MAAMI,CAAC,GAAGlB,QAAQ,CAACE,aAAa,CAACU,CAAC,CAAC;EACnCe,KAAK,CAACC,OAAO,CAAC3B,CAAC,CAAC,GAAGiB,CAAC,CAACW,SAAS,CAACC,GAAG,CAAC,GAAG7B,CAAC,CAAC,GAAGA,CAAC,IAAIiB,CAAC,CAACW,SAAS,CAACC,GAAG,CAAC7B,CAAC,CAAC;EAClE,KAAK,MAAM8B,CAAC,IAAIjB,CAAC,EACfI,CAAC,CAACa,CAAC,CAAC,GAAGjB,CAAC,CAACiB,CAAC,CAAC;EACb,OAAOb,CAAC;AACV;AACA,SAASc,CAACA,CAACpB,CAAC,EAAE;EACZ,OAAOA,CAAC,CAACc,SAAS,CAACO,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACC,IAAI,CAAC,CAAC;AAChD;AACA,SAASC,CAACA,CAACvB,CAAC,EAAEX,CAAC,GAAG,CAAC,CAAC,EAAEa,CAAC,GAAG,KAAK,CAAC,EAAE;EAChC,MAAMI,CAAC,GAAGlB,QAAQ,CAACoC,WAAW,CAAC,CAAC;IAAEL,CAAC,GAAGhB,MAAM,CAACC,YAAY,CAAC,CAAC;EAC3DE,CAAC,CAACE,kBAAkB,CAACR,CAAC,CAAC,EAAEE,CAAC,KAAK,KAAK,CAAC,KAAKI,CAAC,CAACG,QAAQ,CAACT,CAAC,EAAEE,CAAC,CAAC,EAAEI,CAAC,CAACmB,MAAM,CAACzB,CAAC,EAAEE,CAAC,CAAC,CAAC,EAAEI,CAAC,CAACoB,QAAQ,CAACrC,CAAC,CAAC,EAAE8B,CAAC,CAACQ,eAAe,CAAC,CAAC,EAAER,CAAC,CAACS,QAAQ,CAACtB,CAAC,CAAC;AAChI;AACAuB,OAAO,CAACC,SAAS,CAACC,OAAO,KAAKF,OAAO,CAACC,SAAS,CAACC,OAAO,GAAGF,OAAO,CAACC,SAAS,CAACE,iBAAiB,IAAIH,OAAO,CAACC,SAAS,CAACG,qBAAqB,CAAC;AACzIJ,OAAO,CAACC,SAAS,CAACI,OAAO,KAAKL,OAAO,CAACC,SAAS,CAACI,OAAO,GAAG,UAASlC,CAAC,EAAE;EACpE,IAAIX,CAAC,GAAG,IAAI;EACZ,IAAI,CAACD,QAAQ,CAAC+C,eAAe,CAACC,QAAQ,CAAC/C,CAAC,CAAC,EACvC,OAAO,IAAI;EACb,GAAG;IACD,IAAIA,CAAC,CAAC0C,OAAO,CAAC/B,CAAC,CAAC,EACd,OAAOX,CAAC;IACVA,CAAC,GAAGA,CAAC,CAACgD,aAAa,IAAIhD,CAAC,CAACiD,UAAU;EACrC,CAAC,QAAQjD,CAAC,KAAK,IAAI,IAAIA,CAAC,CAACkD,QAAQ,KAAK,CAAC;EACvC,OAAO,IAAI;AACb,CAAC,CAAC;AACF,MAAMC,CAAC,CAAC;EACN;AACF;AACA;AACA;AACA;EACE,WAAWC,mBAAmBA,CAAA,EAAG;IAC/B,OAAO,CAAC,CAAC;EACX;EACA;AACF;AACA;AACA;AACA;AACA;EACE,WAAWC,gBAAgBA,CAAA,EAAG;IAC5B,OAAO,CAAC,CAAC;EACX;EACA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,WAAWC,OAAOA,CAAA,EAAG;IACnB,OAAO;MACLC,IAAI,EAAE9C,CAAC;MACP+C,KAAK,EAAE;IACT,CAAC;EACH;EACA;AACF;AACA;AACA;AACA;EACE,WAAWC,gBAAgBA,CAAA,EAAG;IAC5B,OAAO;MACL;AACN;AACA;AACA;AACA;AACA;MACMC,MAAM,EAAG1D,CAAC,IAAKA,CAAC,CAAC2D,KAAK,CAACC,GAAG,CAAC,CAAC;QAAEC,IAAI,EAAEhD;MAAE,CAAC,KAAKA,CAAC,CAAC,CAACiD,IAAI,CAAC,IAAI,CAAC;MACzD;AACN;AACA;AACA;AACA;AACA;MACMC,MAAM,EAAG/D,CAAC,KAAM;QACd2D,KAAK,EAAE,CACL;UACEE,IAAI,EAAE7D,CAAC;UACPgE,OAAO,EAAE,CAAC;QACZ,CAAC;MAEL,CAAC;IACH,CAAC;EACH;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,WAAWA,CAAC;IAAEC,IAAI,EAAElE,CAAC;IAAEmE,MAAM,EAAEtD,CAAC;IAAEuD,GAAG,EAAEnD,CAAC;IAAEoD,QAAQ,EAAEvC;EAAE,CAAC,EAAE;IACvD,IAAI,CAACwC,SAAS,GAAG;MACfC,OAAO,EAAE,IAAI;MACbZ,KAAK,EAAE;IACT,CAAC,EAAE,IAAI,CAACU,QAAQ,GAAGvC,CAAC,EAAE,IAAI,CAACsC,GAAG,GAAGnD,CAAC,EAAE,IAAI,CAACiD,IAAI,GAAGlE,CAAC,IAAI,CAAC,CAAC;EACzD;EACA;AACF;AACA;AACA;AACA;EACEwE,MAAMA,CAAA,EAAG;IACP,OAAO,IAAI,CAACF,SAAS,CAACC,OAAO,GAAGlE,CAAC,CAAC,KAAK,EAAE,CAAC,IAAI,CAACoE,GAAG,CAACC,SAAS,EAAE,IAAI,CAACD,GAAG,CAACF,OAAO,CAAC,CAAC,EAAE,IAAI,CAACL,IAAI,CAACP,KAAK,KAAK,IAAI,CAACO,IAAI,CAACP,KAAK,GAAG,CACtH;MACEE,IAAI,EAAE,EAAE;MACRG,OAAO,EAAE,CAAC;IACZ,CAAC,CACF,CAAC,EAAE,IAAI,CAACE,IAAI,CAACP,KAAK,CAACgB,OAAO,CAAE3E,CAAC,IAAK;MACjC,MAAMa,CAAC,GAAG,IAAI,CAAC+D,mBAAmB,CAAC5E,CAAC,CAAC;MACrC,IAAI,CAACsE,SAAS,CAACC,OAAO,CAACrE,WAAW,CAACW,CAAC,CAAC;IACvC,CAAC,CAAC,EAAE,IAAI,CAACwD,QAAQ,GAAG,IAAI,CAACC,SAAS,CAACC,OAAO,IAAI,IAAI,CAACD,SAAS,CAACC,OAAO,CAACM,gBAAgB,CAAC,SAAS,EAAG7E,CAAC,IAAK;MACtG,MAAM,CAACa,CAAC,EAAEI,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;MACtB,QAAQjB,CAAC,CAAC8E,OAAO;QACf,KAAKjE,CAAC;UACJ,IAAI,CAACkE,YAAY,CAAC/E,CAAC,CAAC;UACpB;QACF,KAAKiB,CAAC;UACJ,IAAI,CAAC+D,SAAS,CAAChF,CAAC,CAAC;UACjB;MACJ;IACF,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAACsE,SAAS,CAACC,OAAO,CAACM,gBAAgB,CAAC,OAAO,EAAG7E,CAAC,IAAK;MAC9D,IAAI,CAACiF,cAAc,CAACjF,CAAC,CAAC;IACxB,CAAC,CAAC,EAAE,IAAI,CAACsE,SAAS,CAACC,OAAO,CAAC;EAC7B;EACA;AACF;AACA;AACA;AACA;EACEW,IAAIA,CAAA,EAAG;IACL,IAAIlF,CAAC,GAAG,IAAI,CAAC2D,KAAK,CAACC,GAAG,CAAE/C,CAAC,IAAK;MAC5B,MAAMI,CAAC,GAAG,IAAI,CAACkE,YAAY,CAACtE,CAAC,CAAC;MAC9B,OAAO;QACLgD,IAAI,EAAE9B,CAAC,CAACd,CAAC,CAAC;QACV+C,OAAO,EAAEnD,CAAC,CAACe,SAAS,CAACmB,QAAQ,CAAC,IAAI,CAAC0B,GAAG,CAACW,WAAW;MACpD,CAAC;IACH,CAAC,CAAC;IACF,OAAOpF,CAAC,GAAGA,CAAC,CAACqF,MAAM,CAAExE,CAAC,IAAKA,CAAC,CAACgD,IAAI,CAAC5B,IAAI,CAAC,CAAC,CAACqD,MAAM,KAAK,CAAC,CAAC,EAAE;MACtD3B,KAAK,EAAE3D;IACT,CAAC;EACH;EACA;AACF;AACA;AACA;AACA;AACA;AACA;EACEuF,QAAQA,CAACvF,CAAC,EAAE;IACV,OAAO,CAAC,CAACA,CAAC,CAAC2D,KAAK,CAAC2B,MAAM;EACzB;EACA;AACF;AACA;AACA;AACA;AACA;EACEL,cAAcA,CAACjF,CAAC,EAAE;IAChB,MAAMa,CAAC,GAAGb,CAAC,CAACwF,MAAM,CAAC3C,OAAO,CAAE,IAAG,IAAI,CAAC4B,GAAG,CAACgB,IAAK,EAAC,CAAC;MAAExE,CAAC,GAAGJ,CAAC,CAAC6E,aAAa,CAAE,IAAG,IAAI,CAACjB,GAAG,CAACkB,iBAAkB,EAAC,CAAC;IACtG1E,CAAC,CAAC8B,QAAQ,CAAC/C,CAAC,CAACwF,MAAM,CAAC,KAAK3E,CAAC,CAACe,SAAS,CAACgE,MAAM,CAAC,IAAI,CAACnB,GAAG,CAACW,WAAW,CAAC,EAAEnE,CAAC,CAACW,SAAS,CAACC,GAAG,CAAC,IAAI,CAAC4C,GAAG,CAACoB,OAAO,CAAC,EAAE5E,CAAC,CAAC4D,gBAAgB,CAAC,YAAY,EAAE,MAAM,IAAI,CAACiB,0BAA0B,CAAC7E,CAAC,CAAC,EAAE;MAAE8E,IAAI,EAAE,CAAC;IAAE,CAAC,CAAC,CAAC;EACjM;EACA;AACF;AACA;AACA;AACA;AACA;EACEnB,mBAAmBA,CAAC5E,CAAC,GAAG,CAAC,CAAC,EAAE;IAC1B,MAAMa,CAAC,GAAGR,CAAC,CAAC,KAAK,EAAE,IAAI,CAACoE,GAAG,CAACgB,IAAI,CAAC;MAAExE,CAAC,GAAGZ,CAAC,CAAC,MAAM,EAAE,IAAI,CAACoE,GAAG,CAACuB,QAAQ,CAAC;MAAElE,CAAC,GAAGzB,CAAC,CAAC,KAAK,EAAE,IAAI,CAACoE,GAAG,CAACkB,iBAAiB,CAAC;MAAEM,CAAC,GAAG5F,CAAC,CAAC,KAAK,EAAE,IAAI,CAACoE,GAAG,CAACyB,SAAS,EAAE;QAC9IzE,SAAS,EAAEzB,CAAC,CAAC6D,IAAI,GAAG7D,CAAC,CAAC6D,IAAI,GAAG,EAAE;QAC/BsC,eAAe,EAAE,CAAC,IAAI,CAAC9B;MACzB,CAAC,CAAC;IACF,OAAOrE,CAAC,CAACgE,OAAO,IAAInD,CAAC,CAACe,SAAS,CAACC,GAAG,CAAC,IAAI,CAAC4C,GAAG,CAACW,WAAW,CAAC,EAAEnE,CAAC,CAACQ,SAAS,GAAGjB,CAAC,EAAEsB,CAAC,CAAC5B,WAAW,CAACe,CAAC,CAAC,EAAEJ,CAAC,CAACX,WAAW,CAAC4B,CAAC,CAAC,EAAEjB,CAAC,CAACX,WAAW,CAAC+F,CAAC,CAAC,EAAEpF,CAAC;EACrI;EACA;AACF;AACA;AACA;AACA;EACEkE,YAAYA,CAAC/E,CAAC,EAAE;IACdA,CAAC,CAACoG,cAAc,CAAC,CAAC;IAClB,MAAMvF,CAAC,GAAG,IAAI,CAAC8C,KAAK;MAAE1C,CAAC,GAAGlB,QAAQ,CAACa,aAAa,CAACiC,OAAO,CAAE,IAAG,IAAI,CAAC4B,GAAG,CAACgB,IAAK,EAAC,CAAC;IAC7E,IAAI5E,CAAC,CAACwF,OAAO,CAACpF,CAAC,CAAC,KAAKJ,CAAC,CAACyE,MAAM,GAAG,CAAC,IAAIvD,CAAC,CAAC,IAAI,CAACoD,YAAY,CAAClE,CAAC,CAAC,CAAC,CAACqE,MAAM,KAAK,CAAC,EAAE;MACzE,MAAMgB,CAAC,GAAG,IAAI,CAAClC,GAAG,CAACmC,MAAM,CAACC,oBAAoB,CAAC,CAAC;MAChDvF,CAAC,CAACwF,MAAM,CAAC,CAAC,EAAE,IAAI,CAACrC,GAAG,CAACmC,MAAM,CAACG,MAAM,CAAC,CAAC,EAAE,IAAI,CAACtC,GAAG,CAACuC,KAAK,CAACC,UAAU,CAACN,CAAC,GAAG,CAAC,CAAC;MACtE;IACF;IACA,MAAMO,CAAC,GAAGnG,CAAC,CAAC,CAAC;MAAEoG,CAAC,GAAGtF,CAAC,CAACqF,CAAC,CAAC;MAAEE,CAAC,GAAG,IAAI,CAACnC,mBAAmB,CAAC;QACpDf,IAAI,EAAEiD,CAAC;QACP9C,OAAO,EAAE,CAAC;MACZ,CAAC,CAAC;IACF,IAAI,CAACM,SAAS,CAACC,OAAO,CAACyC,YAAY,CAACD,CAAC,EAAE9F,CAAC,CAACgG,WAAW,CAAC,EAAE/E,CAAC,CAAC,IAAI,CAACiD,YAAY,CAAC4B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACpF;EACA;AACF;AACA;AACA;AACA;EACE/B,SAASA,CAAChF,CAAC,EAAE;IACX,MAAMa,CAAC,GAAGb,CAAC,CAACwF,MAAM,CAAC3C,OAAO,CAAE,IAAG,IAAI,CAAC4B,GAAG,CAACgB,IAAK,EAAC,CAAC;MAAExE,CAAC,GAAG,IAAI,CAAC0C,KAAK,CAAC0C,OAAO,CAACxF,CAAC,CAAC;MAAEiB,CAAC,GAAG,IAAI,CAAC6B,KAAK,CAAC1C,CAAC,GAAG,CAAC,CAAC;IACjG,IAAI,CAACa,CAAC,IAAI,EAAEhB,MAAM,CAACC,YAAY,CAAC,CAAC,CAACmG,WAAW,KAAK,CAAC,CAAC,EAClD;IACFlH,CAAC,CAACoG,cAAc,CAAC,CAAC;IAClB,MAAMU,CAAC,GAAGpG,CAAC,CAAC,CAAC;MAAEqG,CAAC,GAAG,IAAI,CAAC5B,YAAY,CAACrD,CAAC,CAAC;MAAEqF,CAAC,GAAGJ,CAAC,CAACK,UAAU,CAAC9B,MAAM;IAChEyB,CAAC,CAAC7G,WAAW,CAAC4G,CAAC,CAAC,EAAE5E,CAAC,CAAC6E,CAAC,EAAE,KAAK,CAAC,EAAEI,CAAC,CAAC,EAAEtG,CAAC,CAAC4F,MAAM,CAAC,CAAC;EAC/C;EACA;AACF;AACA;AACA;AACA;AACA;EACE,IAAIhC,GAAGA,CAAA,EAAG;IACR,OAAO;MACLC,SAAS,EAAE,IAAI,CAACN,GAAG,CAACiD,MAAM,CAACC,KAAK;MAChC/C,OAAO,EAAE,eAAe;MACxBkB,IAAI,EAAE,qBAAqB;MAC3BL,WAAW,EAAE,8BAA8B;MAC3CS,OAAO,EAAE,wCAAwC;MACjDG,QAAQ,EAAE,oCAAoC;MAC9CE,SAAS,EAAE,0BAA0B;MACrCP,iBAAiB,EAAE;IACrB,CAAC;EACH;EACA;AACF;AACA;AACA;AACA;EACE,IAAIhC,KAAKA,CAAA,EAAG;IACV,OAAOjC,KAAK,CAAC6F,IAAI,CAAC,IAAI,CAACjD,SAAS,CAACC,OAAO,CAACiD,gBAAgB,CAAE,IAAG,IAAI,CAAC/C,GAAG,CAACgB,IAAK,EAAC,CAAC,CAAC;EACjF;EACA;AACF;AACA;AACA;AACA;AACA;AACA;EACEK,0BAA0BA,CAAC9F,CAAC,EAAE;IAC5BA,CAAC,CAAC4B,SAAS,CAAC6E,MAAM,CAAC,IAAI,CAAChC,GAAG,CAACoB,OAAO,CAAC;EACtC;EACA;AACF;AACA;AACA;AACA;AACA;AACA;EACEV,YAAYA,CAACnF,CAAC,EAAE;IACd,OAAOA,CAAC,CAAC0F,aAAa,CAAE,IAAG,IAAI,CAACjB,GAAG,CAACyB,SAAU,EAAC,CAAC;EAClD;AACF;AACA,SACE/C,CAAC,IAAIsE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}