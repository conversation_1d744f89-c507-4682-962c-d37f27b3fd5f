{"ast": null, "code": "import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>orO<PERSON> } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Directive, Input, Component, ChangeDetectionStrategy, ViewEncapsulation, NgModule } from '@angular/core';\nimport { InputBoolean, toCssPixel } from 'ng-zorro-antd/core/util';\nimport { __decorate } from 'tslib';\nconst _c0 = [\"nzType\", \"button\"];\nconst _c1 = [\"nzType\", \"avatar\"];\nconst _c2 = [\"nzType\", \"input\"];\nconst _c3 = [\"nzType\", \"image\"];\nconst _c4 = [\"*\"];\nfunction NzSkeletonComponent_ng_container_0_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵelement(1, \"nz-skeleton-element\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzSize\", ctx_r0.avatar.size || \"default\")(\"nzShape\", ctx_r0.avatar.shape || \"circle\");\n  }\n}\nfunction NzSkeletonComponent_ng_container_0_h3_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"h3\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r0.toCSSUnit(ctx_r0.title.width));\n  }\n}\nfunction NzSkeletonComponent_ng_container_0_ul_4_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\");\n  }\n  if (rf & 2) {\n    const i_r2 = ctx.index;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵstyleProp(\"width\", ctx_r0.toCSSUnit(ctx_r0.widthList[i_r2]));\n  }\n}\nfunction NzSkeletonComponent_ng_container_0_ul_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 8);\n    i0.ɵɵtemplate(1, NzSkeletonComponent_ng_container_0_ul_4_li_1_Template, 1, 2, \"li\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.rowsList);\n  }\n}\nfunction NzSkeletonComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NzSkeletonComponent_ng_container_0_div_1_Template, 2, 2, \"div\", 1);\n    i0.ɵɵelementStart(2, \"div\", 2);\n    i0.ɵɵtemplate(3, NzSkeletonComponent_ng_container_0_h3_3_Template, 1, 2, \"h3\", 3)(4, NzSkeletonComponent_ng_container_0_ul_4_Template, 2, 1, \"ul\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !!ctx_r0.nzAvatar);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !!ctx_r0.nzTitle);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !!ctx_r0.nzParagraph);\n  }\n}\nfunction NzSkeletonComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵprojection(1);\n    i0.ɵɵelementContainerEnd();\n  }\n}\nclass NzSkeletonElementDirective {\n  constructor() {\n    this.nzActive = false;\n    this.nzBlock = false;\n  }\n  static {\n    this.ɵfac = function NzSkeletonElementDirective_Factory(t) {\n      return new (t || NzSkeletonElementDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzSkeletonElementDirective,\n      selectors: [[\"nz-skeleton-element\"]],\n      hostAttrs: [1, \"ant-skeleton\", \"ant-skeleton-element\"],\n      hostVars: 4,\n      hostBindings: function NzSkeletonElementDirective_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-skeleton-active\", ctx.nzActive)(\"ant-skeleton-block\", ctx.nzBlock);\n        }\n      },\n      inputs: {\n        nzActive: \"nzActive\",\n        nzType: \"nzType\",\n        nzBlock: \"nzBlock\"\n      },\n      standalone: true\n    });\n  }\n}\n__decorate([InputBoolean()], NzSkeletonElementDirective.prototype, \"nzBlock\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSkeletonElementDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'nz-skeleton-element',\n      host: {\n        class: 'ant-skeleton ant-skeleton-element',\n        '[class.ant-skeleton-active]': 'nzActive',\n        '[class.ant-skeleton-block]': 'nzBlock'\n      },\n      standalone: true\n    }]\n  }], () => [], {\n    nzActive: [{\n      type: Input\n    }],\n    nzType: [{\n      type: Input\n    }],\n    nzBlock: [{\n      type: Input\n    }]\n  });\n})();\nclass NzSkeletonElementButtonComponent {\n  constructor() {\n    this.nzShape = 'default';\n    this.nzSize = 'default';\n  }\n  static {\n    this.ɵfac = function NzSkeletonElementButtonComponent_Factory(t) {\n      return new (t || NzSkeletonElementButtonComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzSkeletonElementButtonComponent,\n      selectors: [[\"nz-skeleton-element\", \"nzType\", \"button\"]],\n      inputs: {\n        nzShape: \"nzShape\",\n        nzSize: \"nzSize\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      attrs: _c0,\n      decls: 1,\n      vars: 10,\n      consts: [[1, \"ant-skeleton-button\"]],\n      template: function NzSkeletonElementButtonComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"span\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-skeleton-button-square\", ctx.nzShape === \"square\")(\"ant-skeleton-button-round\", ctx.nzShape === \"round\")(\"ant-skeleton-button-circle\", ctx.nzShape === \"circle\")(\"ant-skeleton-button-lg\", ctx.nzSize === \"large\")(\"ant-skeleton-button-sm\", ctx.nzSize === \"small\");\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSkeletonElementButtonComponent, [{\n    type: Component,\n    args: [{\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      selector: 'nz-skeleton-element[nzType=\"button\"]',\n      template: `\n    <span\n      class=\"ant-skeleton-button\"\n      [class.ant-skeleton-button-square]=\"nzShape === 'square'\"\n      [class.ant-skeleton-button-round]=\"nzShape === 'round'\"\n      [class.ant-skeleton-button-circle]=\"nzShape === 'circle'\"\n      [class.ant-skeleton-button-lg]=\"nzSize === 'large'\"\n      [class.ant-skeleton-button-sm]=\"nzSize === 'small'\"\n    ></span>\n  `,\n      standalone: true\n    }]\n  }], null, {\n    nzShape: [{\n      type: Input\n    }],\n    nzSize: [{\n      type: Input\n    }]\n  });\n})();\nclass NzSkeletonElementAvatarComponent {\n  constructor() {\n    this.nzShape = 'circle';\n    this.nzSize = 'default';\n    this.styleMap = {};\n  }\n  ngOnChanges(changes) {\n    if (changes.nzSize && typeof this.nzSize === 'number') {\n      const sideLength = `${this.nzSize}px`;\n      this.styleMap = {\n        width: sideLength,\n        height: sideLength,\n        'line-height': sideLength\n      };\n    } else {\n      this.styleMap = {};\n    }\n  }\n  static {\n    this.ɵfac = function NzSkeletonElementAvatarComponent_Factory(t) {\n      return new (t || NzSkeletonElementAvatarComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzSkeletonElementAvatarComponent,\n      selectors: [[\"nz-skeleton-element\", \"nzType\", \"avatar\"]],\n      inputs: {\n        nzShape: \"nzShape\",\n        nzSize: \"nzSize\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      attrs: _c1,\n      decls: 1,\n      vars: 9,\n      consts: [[1, \"ant-skeleton-avatar\", 3, \"ngStyle\"]],\n      template: function NzSkeletonElementAvatarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"span\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-skeleton-avatar-square\", ctx.nzShape === \"square\")(\"ant-skeleton-avatar-circle\", ctx.nzShape === \"circle\")(\"ant-skeleton-avatar-lg\", ctx.nzSize === \"large\")(\"ant-skeleton-avatar-sm\", ctx.nzSize === \"small\");\n          i0.ɵɵproperty(\"ngStyle\", ctx.styleMap);\n        }\n      },\n      dependencies: [NgStyle],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSkeletonElementAvatarComponent, [{\n    type: Component,\n    args: [{\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      selector: 'nz-skeleton-element[nzType=\"avatar\"]',\n      template: `\n    <span\n      class=\"ant-skeleton-avatar\"\n      [class.ant-skeleton-avatar-square]=\"nzShape === 'square'\"\n      [class.ant-skeleton-avatar-circle]=\"nzShape === 'circle'\"\n      [class.ant-skeleton-avatar-lg]=\"nzSize === 'large'\"\n      [class.ant-skeleton-avatar-sm]=\"nzSize === 'small'\"\n      [ngStyle]=\"styleMap\"\n    ></span>\n  `,\n      imports: [NgStyle],\n      standalone: true\n    }]\n  }], null, {\n    nzShape: [{\n      type: Input\n    }],\n    nzSize: [{\n      type: Input\n    }]\n  });\n})();\nclass NzSkeletonElementInputComponent {\n  constructor() {\n    this.nzSize = 'default';\n  }\n  static {\n    this.ɵfac = function NzSkeletonElementInputComponent_Factory(t) {\n      return new (t || NzSkeletonElementInputComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzSkeletonElementInputComponent,\n      selectors: [[\"nz-skeleton-element\", \"nzType\", \"input\"]],\n      inputs: {\n        nzSize: \"nzSize\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      attrs: _c2,\n      decls: 1,\n      vars: 4,\n      consts: [[1, \"ant-skeleton-input\"]],\n      template: function NzSkeletonElementInputComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"span\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-skeleton-input-lg\", ctx.nzSize === \"large\")(\"ant-skeleton-input-sm\", ctx.nzSize === \"small\");\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSkeletonElementInputComponent, [{\n    type: Component,\n    args: [{\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      selector: 'nz-skeleton-element[nzType=\"input\"]',\n      template: `\n    <span\n      class=\"ant-skeleton-input\"\n      [class.ant-skeleton-input-lg]=\"nzSize === 'large'\"\n      [class.ant-skeleton-input-sm]=\"nzSize === 'small'\"\n    ></span>\n  `,\n      standalone: true\n    }]\n  }], null, {\n    nzSize: [{\n      type: Input\n    }]\n  });\n})();\nclass NzSkeletonElementImageComponent {\n  static {\n    this.ɵfac = function NzSkeletonElementImageComponent_Factory(t) {\n      return new (t || NzSkeletonElementImageComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzSkeletonElementImageComponent,\n      selectors: [[\"nz-skeleton-element\", \"nzType\", \"image\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      attrs: _c3,\n      decls: 3,\n      vars: 0,\n      consts: [[1, \"ant-skeleton-image\"], [\"viewBox\", \"0 0 1098 1024\", \"xmlns\", \"http://www.w3.org/2000/svg\", 1, \"ant-skeleton-image-svg\"], [\"d\", \"M365.714286 329.142857q0 45.714286-32.036571 77.677714t-77.677714 32.036571-77.677714-32.036571-32.036571-77.677714 32.036571-77.677714 77.677714-32.036571 77.677714 32.036571 32.036571 77.677714zM950.857143 548.571429l0 256-804.571429 0 0-109.714286 182.857143-182.857143 91.428571 91.428571 292.571429-292.571429zM1005.714286 146.285714l-914.285714 0q-7.460571 0-12.873143 5.412571t-5.412571 12.873143l0 694.857143q0 7.460571 5.412571 12.873143t12.873143 5.412571l914.285714 0q7.460571 0 12.873143-5.412571t5.412571-12.873143l0-694.857143q0-7.460571-5.412571-12.873143t-12.873143-5.412571zM1097.142857 164.571429l0 694.857143q0 37.741714-26.843429 64.585143t-64.585143 26.843429l-914.285714 0q-37.741714 0-64.585143-26.843429t-26.843429-64.585143l0-694.857143q0-37.741714 26.843429-64.585143t64.585143-26.843429l914.285714 0q37.741714 0 64.585143 26.843429t26.843429 64.585143z\", 1, \"ant-skeleton-image-path\"]],\n      template: function NzSkeletonElementImageComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"span\", 0);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(1, \"svg\", 1);\n          i0.ɵɵelement(2, \"path\", 2);\n          i0.ɵɵelementEnd()();\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSkeletonElementImageComponent, [{\n    type: Component,\n    args: [{\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      selector: 'nz-skeleton-element[nzType=\"image\"]',\n      template: `\n    <span class=\"ant-skeleton-image\">\n      <svg class=\"ant-skeleton-image-svg\" viewBox=\"0 0 1098 1024\" xmlns=\"http://www.w3.org/2000/svg\">\n        <path\n          d=\"M365.714286 329.142857q0 45.714286-32.036571 77.677714t-77.677714 32.036571-77.677714-32.036571-32.036571-77.677714 32.036571-77.677714 77.677714-32.036571 77.677714 32.036571 32.036571 77.677714zM950.857143 548.571429l0 256-804.571429 0 0-109.714286 182.857143-182.857143 91.428571 91.428571 292.571429-292.571429zM1005.714286 146.285714l-914.285714 0q-7.460571 0-12.873143 5.412571t-5.412571 12.873143l0 694.857143q0 7.460571 5.412571 12.873143t12.873143 5.412571l914.285714 0q7.460571 0 12.873143-5.412571t5.412571-12.873143l0-694.857143q0-7.460571-5.412571-12.873143t-12.873143-5.412571zM1097.142857 164.571429l0 694.857143q0 37.741714-26.843429 64.585143t-64.585143 26.843429l-914.285714 0q-37.741714 0-64.585143-26.843429t-26.843429-64.585143l0-694.857143q0-37.741714 26.843429-64.585143t64.585143-26.843429l914.285714 0q37.741714 0 64.585143 26.843429t26.843429 64.585143z\"\n          class=\"ant-skeleton-image-path\"\n        />\n      </svg>\n    </span>\n  `,\n      standalone: true\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzSkeletonComponent {\n  constructor(cdr) {\n    this.cdr = cdr;\n    this.nzActive = false;\n    this.nzLoading = true;\n    this.nzRound = false;\n    this.nzTitle = true;\n    this.nzAvatar = false;\n    this.nzParagraph = true;\n    this.rowsList = [];\n    this.widthList = [];\n  }\n  toCSSUnit(value = '') {\n    return toCssPixel(value);\n  }\n  getTitleProps() {\n    const hasAvatar = !!this.nzAvatar;\n    const hasParagraph = !!this.nzParagraph;\n    let width = '';\n    if (!hasAvatar && hasParagraph) {\n      width = '38%';\n    } else if (hasAvatar && hasParagraph) {\n      width = '50%';\n    }\n    return {\n      width,\n      ...this.getProps(this.nzTitle)\n    };\n  }\n  getAvatarProps() {\n    const shape = !!this.nzTitle && !this.nzParagraph ? 'square' : 'circle';\n    const size = 'large';\n    return {\n      shape,\n      size,\n      ...this.getProps(this.nzAvatar)\n    };\n  }\n  getParagraphProps() {\n    const hasAvatar = !!this.nzAvatar;\n    const hasTitle = !!this.nzTitle;\n    const basicProps = {};\n    // Width\n    if (!hasAvatar || !hasTitle) {\n      basicProps.width = '61%';\n    }\n    // Rows\n    if (!hasAvatar && hasTitle) {\n      basicProps.rows = 3;\n    } else {\n      basicProps.rows = 2;\n    }\n    return {\n      ...basicProps,\n      ...this.getProps(this.nzParagraph)\n    };\n  }\n  getProps(prop) {\n    return prop && typeof prop === 'object' ? prop : {};\n  }\n  getWidthList() {\n    const {\n      width,\n      rows\n    } = this.paragraph;\n    let widthList = [];\n    if (width && Array.isArray(width)) {\n      widthList = width;\n    } else if (width && !Array.isArray(width)) {\n      widthList = [];\n      widthList[rows - 1] = width;\n    }\n    return widthList;\n  }\n  updateProps() {\n    this.title = this.getTitleProps();\n    this.avatar = this.getAvatarProps();\n    this.paragraph = this.getParagraphProps();\n    this.rowsList = [...Array(this.paragraph.rows)];\n    this.widthList = this.getWidthList();\n    this.cdr.markForCheck();\n  }\n  ngOnInit() {\n    this.updateProps();\n  }\n  ngOnChanges(changes) {\n    if (changes.nzTitle || changes.nzAvatar || changes.nzParagraph) {\n      this.updateProps();\n    }\n  }\n  static {\n    this.ɵfac = function NzSkeletonComponent_Factory(t) {\n      return new (t || NzSkeletonComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzSkeletonComponent,\n      selectors: [[\"nz-skeleton\"]],\n      hostAttrs: [1, \"ant-skeleton\"],\n      hostVars: 6,\n      hostBindings: function NzSkeletonComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-skeleton-with-avatar\", !!ctx.nzAvatar)(\"ant-skeleton-active\", ctx.nzActive)(\"ant-skeleton-round\", !!ctx.nzRound);\n        }\n      },\n      inputs: {\n        nzActive: \"nzActive\",\n        nzLoading: \"nzLoading\",\n        nzRound: \"nzRound\",\n        nzTitle: \"nzTitle\",\n        nzAvatar: \"nzAvatar\",\n        nzParagraph: \"nzParagraph\"\n      },\n      exportAs: [\"nzSkeleton\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c4,\n      decls: 2,\n      vars: 2,\n      consts: [[4, \"ngIf\"], [\"class\", \"ant-skeleton-header\", 4, \"ngIf\"], [1, \"ant-skeleton-content\"], [\"class\", \"ant-skeleton-title\", 3, \"width\", 4, \"ngIf\"], [\"class\", \"ant-skeleton-paragraph\", 4, \"ngIf\"], [1, \"ant-skeleton-header\"], [\"nzType\", \"avatar\", 3, \"nzSize\", \"nzShape\"], [1, \"ant-skeleton-title\"], [1, \"ant-skeleton-paragraph\"], [3, \"width\", 4, \"ngFor\", \"ngForOf\"]],\n      template: function NzSkeletonComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, NzSkeletonComponent_ng_container_0_Template, 5, 3, \"ng-container\", 0)(1, NzSkeletonComponent_ng_container_1_Template, 2, 0, \"ng-container\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.nzLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.nzLoading);\n        }\n      },\n      dependencies: [NzSkeletonElementDirective, NzSkeletonElementAvatarComponent, NgIf, NgForOf],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSkeletonComponent, [{\n    type: Component,\n    args: [{\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      selector: 'nz-skeleton',\n      exportAs: 'nzSkeleton',\n      host: {\n        class: 'ant-skeleton',\n        '[class.ant-skeleton-with-avatar]': '!!nzAvatar',\n        '[class.ant-skeleton-active]': 'nzActive',\n        '[class.ant-skeleton-round]': '!!nzRound'\n      },\n      template: `\n    <ng-container *ngIf=\"nzLoading\">\n      <div class=\"ant-skeleton-header\" *ngIf=\"!!nzAvatar\">\n        <nz-skeleton-element\n          nzType=\"avatar\"\n          [nzSize]=\"avatar.size || 'default'\"\n          [nzShape]=\"avatar.shape || 'circle'\"\n        ></nz-skeleton-element>\n      </div>\n      <div class=\"ant-skeleton-content\">\n        <h3 *ngIf=\"!!nzTitle\" class=\"ant-skeleton-title\" [style.width]=\"toCSSUnit(title.width)\"></h3>\n        <ul *ngIf=\"!!nzParagraph\" class=\"ant-skeleton-paragraph\">\n          <li *ngFor=\"let row of rowsList; let i = index\" [style.width]=\"toCSSUnit(widthList[i])\"></li>\n        </ul>\n      </div>\n    </ng-container>\n    <ng-container *ngIf=\"!nzLoading\">\n      <ng-content></ng-content>\n    </ng-container>\n  `,\n      imports: [NzSkeletonElementDirective, NzSkeletonElementAvatarComponent, NgIf, NgForOf],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }], {\n    nzActive: [{\n      type: Input\n    }],\n    nzLoading: [{\n      type: Input\n    }],\n    nzRound: [{\n      type: Input\n    }],\n    nzTitle: [{\n      type: Input\n    }],\n    nzAvatar: [{\n      type: Input\n    }],\n    nzParagraph: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzSkeletonModule {\n  static {\n    this.ɵfac = function NzSkeletonModule_Factory(t) {\n      return new (t || NzSkeletonModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzSkeletonModule,\n      imports: [NzSkeletonElementDirective, NzSkeletonComponent, NzSkeletonElementButtonComponent, NzSkeletonElementAvatarComponent, NzSkeletonElementImageComponent, NzSkeletonElementInputComponent],\n      exports: [NzSkeletonElementDirective, NzSkeletonComponent, NzSkeletonElementButtonComponent, NzSkeletonElementAvatarComponent, NzSkeletonElementImageComponent, NzSkeletonElementInputComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSkeletonModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzSkeletonElementDirective, NzSkeletonComponent, NzSkeletonElementButtonComponent, NzSkeletonElementAvatarComponent, NzSkeletonElementImageComponent, NzSkeletonElementInputComponent],\n      exports: [NzSkeletonElementDirective, NzSkeletonComponent, NzSkeletonElementButtonComponent, NzSkeletonElementAvatarComponent, NzSkeletonElementImageComponent, NzSkeletonElementInputComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzSkeletonComponent, NzSkeletonElementAvatarComponent, NzSkeletonElementButtonComponent, NzSkeletonElementDirective, NzSkeletonElementImageComponent, NzSkeletonElementInputComponent, NzSkeletonModule };", "map": {"version": 3, "names": ["NgStyle", "NgIf", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i0", "Directive", "Input", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "NgModule", "InputBoolean", "toCssPixel", "__decorate", "_c0", "_c1", "_c2", "_c3", "_c4", "NzSkeletonComponent_ng_container_0_div_1_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ctx_r0", "ɵɵnextContext", "ɵɵadvance", "ɵɵproperty", "avatar", "size", "shape", "NzSkeletonComponent_ng_container_0_h3_3_Template", "ɵɵstyleProp", "toCSSUnit", "title", "width", "NzSkeletonComponent_ng_container_0_ul_4_li_1_Template", "i_r2", "index", "widthList", "NzSkeletonComponent_ng_container_0_ul_4_Template", "ɵɵtemplate", "rowsList", "NzSkeletonComponent_ng_container_0_Template", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "nzAvatar", "nzTitle", "nzParagraph", "NzSkeletonComponent_ng_container_1_Template", "ɵɵprojection", "NzSkeletonElementDirective", "constructor", "nzActive", "nzBlock", "ɵfac", "NzSkeletonElementDirective_Factory", "t", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostAttrs", "hostVars", "hostBindings", "NzSkeletonElementDirective_HostBindings", "ɵɵclassProp", "inputs", "nzType", "standalone", "prototype", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "host", "class", "NzSkeletonElementButtonComponent", "nzShape", "nzSize", "NzSkeletonElementButtonComponent_Factory", "ɵcmp", "ɵɵdefineComponent", "features", "ɵɵStandaloneFeature", "attrs", "decls", "vars", "consts", "template", "NzSkeletonElementButtonComponent_Template", "encapsulation", "changeDetection", "OnPush", "NzSkeletonElementAvatarComponent", "styleMap", "ngOnChanges", "changes", "sideLength", "height", "NzSkeletonElementAvatarComponent_Factory", "ɵɵNgOnChangesFeature", "NzSkeletonElementAvatarComponent_Template", "dependencies", "imports", "NzSkeletonElementInputComponent", "NzSkeletonElementInputComponent_Factory", "NzSkeletonElementInputComponent_Template", "NzSkeletonElementImageComponent", "NzSkeletonElementImageComponent_Factory", "NzSkeletonElementImageComponent_Template", "ɵɵnamespaceSVG", "NzSkeletonComponent", "cdr", "nzLoading", "nzRound", "value", "getTitleProps", "has<PERSON><PERSON><PERSON>", "hasParagraph", "getProps", "getAvatarProps", "getParagraphProps", "hasTitle", "basicProps", "rows", "prop", "getWidthList", "paragraph", "Array", "isArray", "updateProps", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ngOnInit", "NzSkeletonComponent_Factory", "ɵɵdirectiveInject", "ChangeDetectorRef", "NzSkeletonComponent_HostBindings", "exportAs", "ngContentSelectors", "NzSkeletonComponent_Template", "ɵɵprojectionDef", "None", "NzSkeletonModule", "NzSkeletonModule_Factory", "ɵmod", "ɵɵdefineNgModule", "exports", "ɵinj", "ɵɵdefineInjector"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-skeleton.mjs"], "sourcesContent": ["import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgForOf } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Directive, Input, Component, ChangeDetectionStrategy, ViewEncapsulation, NgModule } from '@angular/core';\nimport { InputBoolean, toCssPixel } from 'ng-zorro-antd/core/util';\nimport { __decorate } from 'tslib';\n\nclass NzSkeletonElementDirective {\n    constructor() {\n        this.nzActive = false;\n        this.nzBlock = false;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzSkeletonElementDirective, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzSkeletonElementDirective, isStandalone: true, selector: \"nz-skeleton-element\", inputs: { nzActive: \"nzActive\", nzType: \"nzType\", nzBlock: \"nzBlock\" }, host: { properties: { \"class.ant-skeleton-active\": \"nzActive\", \"class.ant-skeleton-block\": \"nzBlock\" }, classAttribute: \"ant-skeleton ant-skeleton-element\" }, ngImport: i0 }); }\n}\n__decorate([\n    InputBoolean()\n], NzSkeletonElementDirective.prototype, \"nzBlock\", void 0);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzSkeletonElementDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'nz-skeleton-element',\n                    host: {\n                        class: 'ant-skeleton ant-skeleton-element',\n                        '[class.ant-skeleton-active]': 'nzActive',\n                        '[class.ant-skeleton-block]': 'nzBlock'\n                    },\n                    standalone: true\n                }]\n        }], ctorParameters: () => [], propDecorators: { nzActive: [{\n                type: Input\n            }], nzType: [{\n                type: Input\n            }], nzBlock: [{\n                type: Input\n            }] } });\nclass NzSkeletonElementButtonComponent {\n    constructor() {\n        this.nzShape = 'default';\n        this.nzSize = 'default';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzSkeletonElementButtonComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzSkeletonElementButtonComponent, isStandalone: true, selector: \"nz-skeleton-element[nzType=\\\"button\\\"]\", inputs: { nzShape: \"nzShape\", nzSize: \"nzSize\" }, ngImport: i0, template: `\n    <span\n      class=\"ant-skeleton-button\"\n      [class.ant-skeleton-button-square]=\"nzShape === 'square'\"\n      [class.ant-skeleton-button-round]=\"nzShape === 'round'\"\n      [class.ant-skeleton-button-circle]=\"nzShape === 'circle'\"\n      [class.ant-skeleton-button-lg]=\"nzSize === 'large'\"\n      [class.ant-skeleton-button-sm]=\"nzSize === 'small'\"\n    ></span>\n  `, isInline: true, changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzSkeletonElementButtonComponent, decorators: [{\n            type: Component,\n            args: [{\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    selector: 'nz-skeleton-element[nzType=\"button\"]',\n                    template: `\n    <span\n      class=\"ant-skeleton-button\"\n      [class.ant-skeleton-button-square]=\"nzShape === 'square'\"\n      [class.ant-skeleton-button-round]=\"nzShape === 'round'\"\n      [class.ant-skeleton-button-circle]=\"nzShape === 'circle'\"\n      [class.ant-skeleton-button-lg]=\"nzSize === 'large'\"\n      [class.ant-skeleton-button-sm]=\"nzSize === 'small'\"\n    ></span>\n  `,\n                    standalone: true\n                }]\n        }], propDecorators: { nzShape: [{\n                type: Input\n            }], nzSize: [{\n                type: Input\n            }] } });\nclass NzSkeletonElementAvatarComponent {\n    constructor() {\n        this.nzShape = 'circle';\n        this.nzSize = 'default';\n        this.styleMap = {};\n    }\n    ngOnChanges(changes) {\n        if (changes.nzSize && typeof this.nzSize === 'number') {\n            const sideLength = `${this.nzSize}px`;\n            this.styleMap = { width: sideLength, height: sideLength, 'line-height': sideLength };\n        }\n        else {\n            this.styleMap = {};\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzSkeletonElementAvatarComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzSkeletonElementAvatarComponent, isStandalone: true, selector: \"nz-skeleton-element[nzType=\\\"avatar\\\"]\", inputs: { nzShape: \"nzShape\", nzSize: \"nzSize\" }, usesOnChanges: true, ngImport: i0, template: `\n    <span\n      class=\"ant-skeleton-avatar\"\n      [class.ant-skeleton-avatar-square]=\"nzShape === 'square'\"\n      [class.ant-skeleton-avatar-circle]=\"nzShape === 'circle'\"\n      [class.ant-skeleton-avatar-lg]=\"nzSize === 'large'\"\n      [class.ant-skeleton-avatar-sm]=\"nzSize === 'small'\"\n      [ngStyle]=\"styleMap\"\n    ></span>\n  `, isInline: true, dependencies: [{ kind: \"directive\", type: NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzSkeletonElementAvatarComponent, decorators: [{\n            type: Component,\n            args: [{\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    selector: 'nz-skeleton-element[nzType=\"avatar\"]',\n                    template: `\n    <span\n      class=\"ant-skeleton-avatar\"\n      [class.ant-skeleton-avatar-square]=\"nzShape === 'square'\"\n      [class.ant-skeleton-avatar-circle]=\"nzShape === 'circle'\"\n      [class.ant-skeleton-avatar-lg]=\"nzSize === 'large'\"\n      [class.ant-skeleton-avatar-sm]=\"nzSize === 'small'\"\n      [ngStyle]=\"styleMap\"\n    ></span>\n  `,\n                    imports: [NgStyle],\n                    standalone: true\n                }]\n        }], propDecorators: { nzShape: [{\n                type: Input\n            }], nzSize: [{\n                type: Input\n            }] } });\nclass NzSkeletonElementInputComponent {\n    constructor() {\n        this.nzSize = 'default';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzSkeletonElementInputComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzSkeletonElementInputComponent, isStandalone: true, selector: \"nz-skeleton-element[nzType=\\\"input\\\"]\", inputs: { nzSize: \"nzSize\" }, ngImport: i0, template: `\n    <span\n      class=\"ant-skeleton-input\"\n      [class.ant-skeleton-input-lg]=\"nzSize === 'large'\"\n      [class.ant-skeleton-input-sm]=\"nzSize === 'small'\"\n    ></span>\n  `, isInline: true, changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzSkeletonElementInputComponent, decorators: [{\n            type: Component,\n            args: [{\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    selector: 'nz-skeleton-element[nzType=\"input\"]',\n                    template: `\n    <span\n      class=\"ant-skeleton-input\"\n      [class.ant-skeleton-input-lg]=\"nzSize === 'large'\"\n      [class.ant-skeleton-input-sm]=\"nzSize === 'small'\"\n    ></span>\n  `,\n                    standalone: true\n                }]\n        }], propDecorators: { nzSize: [{\n                type: Input\n            }] } });\nclass NzSkeletonElementImageComponent {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzSkeletonElementImageComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzSkeletonElementImageComponent, isStandalone: true, selector: \"nz-skeleton-element[nzType=\\\"image\\\"]\", ngImport: i0, template: `\n    <span class=\"ant-skeleton-image\">\n      <svg class=\"ant-skeleton-image-svg\" viewBox=\"0 0 1098 1024\" xmlns=\"http://www.w3.org/2000/svg\">\n        <path\n          d=\"M365.714286 329.142857q0 45.714286-32.036571 77.677714t-77.677714 32.036571-77.677714-32.036571-32.036571-77.677714 32.036571-77.677714 77.677714-32.036571 77.677714 32.036571 32.036571 77.677714zM950.857143 548.571429l0 256-804.571429 0 0-109.714286 182.857143-182.857143 91.428571 91.428571 292.571429-292.571429zM1005.714286 146.285714l-914.285714 0q-7.460571 0-12.873143 5.412571t-5.412571 12.873143l0 694.857143q0 7.460571 5.412571 12.873143t12.873143 5.412571l914.285714 0q7.460571 0 12.873143-5.412571t5.412571-12.873143l0-694.857143q0-7.460571-5.412571-12.873143t-12.873143-5.412571zM1097.142857 164.571429l0 694.857143q0 37.741714-26.843429 64.585143t-64.585143 26.843429l-914.285714 0q-37.741714 0-64.585143-26.843429t-26.843429-64.585143l0-694.857143q0-37.741714 26.843429-64.585143t64.585143-26.843429l914.285714 0q37.741714 0 64.585143 26.843429t26.843429 64.585143z\"\n          class=\"ant-skeleton-image-path\"\n        />\n      </svg>\n    </span>\n  `, isInline: true, changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzSkeletonElementImageComponent, decorators: [{\n            type: Component,\n            args: [{\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    selector: 'nz-skeleton-element[nzType=\"image\"]',\n                    template: `\n    <span class=\"ant-skeleton-image\">\n      <svg class=\"ant-skeleton-image-svg\" viewBox=\"0 0 1098 1024\" xmlns=\"http://www.w3.org/2000/svg\">\n        <path\n          d=\"M365.714286 329.142857q0 45.714286-32.036571 77.677714t-77.677714 32.036571-77.677714-32.036571-32.036571-77.677714 32.036571-77.677714 77.677714-32.036571 77.677714 32.036571 32.036571 77.677714zM950.857143 548.571429l0 256-804.571429 0 0-109.714286 182.857143-182.857143 91.428571 91.428571 292.571429-292.571429zM1005.714286 146.285714l-914.285714 0q-7.460571 0-12.873143 5.412571t-5.412571 12.873143l0 694.857143q0 7.460571 5.412571 12.873143t12.873143 5.412571l914.285714 0q7.460571 0 12.873143-5.412571t5.412571-12.873143l0-694.857143q0-7.460571-5.412571-12.873143t-12.873143-5.412571zM1097.142857 164.571429l0 694.857143q0 37.741714-26.843429 64.585143t-64.585143 26.843429l-914.285714 0q-37.741714 0-64.585143-26.843429t-26.843429-64.585143l0-694.857143q0-37.741714 26.843429-64.585143t64.585143-26.843429l914.285714 0q37.741714 0 64.585143 26.843429t26.843429 64.585143z\"\n          class=\"ant-skeleton-image-path\"\n        />\n      </svg>\n    </span>\n  `,\n                    standalone: true\n                }]\n        }] });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzSkeletonComponent {\n    constructor(cdr) {\n        this.cdr = cdr;\n        this.nzActive = false;\n        this.nzLoading = true;\n        this.nzRound = false;\n        this.nzTitle = true;\n        this.nzAvatar = false;\n        this.nzParagraph = true;\n        this.rowsList = [];\n        this.widthList = [];\n    }\n    toCSSUnit(value = '') {\n        return toCssPixel(value);\n    }\n    getTitleProps() {\n        const hasAvatar = !!this.nzAvatar;\n        const hasParagraph = !!this.nzParagraph;\n        let width = '';\n        if (!hasAvatar && hasParagraph) {\n            width = '38%';\n        }\n        else if (hasAvatar && hasParagraph) {\n            width = '50%';\n        }\n        return { width, ...this.getProps(this.nzTitle) };\n    }\n    getAvatarProps() {\n        const shape = !!this.nzTitle && !this.nzParagraph ? 'square' : 'circle';\n        const size = 'large';\n        return { shape, size, ...this.getProps(this.nzAvatar) };\n    }\n    getParagraphProps() {\n        const hasAvatar = !!this.nzAvatar;\n        const hasTitle = !!this.nzTitle;\n        const basicProps = {};\n        // Width\n        if (!hasAvatar || !hasTitle) {\n            basicProps.width = '61%';\n        }\n        // Rows\n        if (!hasAvatar && hasTitle) {\n            basicProps.rows = 3;\n        }\n        else {\n            basicProps.rows = 2;\n        }\n        return { ...basicProps, ...this.getProps(this.nzParagraph) };\n    }\n    getProps(prop) {\n        return prop && typeof prop === 'object' ? prop : {};\n    }\n    getWidthList() {\n        const { width, rows } = this.paragraph;\n        let widthList = [];\n        if (width && Array.isArray(width)) {\n            widthList = width;\n        }\n        else if (width && !Array.isArray(width)) {\n            widthList = [];\n            widthList[rows - 1] = width;\n        }\n        return widthList;\n    }\n    updateProps() {\n        this.title = this.getTitleProps();\n        this.avatar = this.getAvatarProps();\n        this.paragraph = this.getParagraphProps();\n        this.rowsList = [...Array(this.paragraph.rows)];\n        this.widthList = this.getWidthList();\n        this.cdr.markForCheck();\n    }\n    ngOnInit() {\n        this.updateProps();\n    }\n    ngOnChanges(changes) {\n        if (changes.nzTitle || changes.nzAvatar || changes.nzParagraph) {\n            this.updateProps();\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzSkeletonComponent, deps: [{ token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzSkeletonComponent, isStandalone: true, selector: \"nz-skeleton\", inputs: { nzActive: \"nzActive\", nzLoading: \"nzLoading\", nzRound: \"nzRound\", nzTitle: \"nzTitle\", nzAvatar: \"nzAvatar\", nzParagraph: \"nzParagraph\" }, host: { properties: { \"class.ant-skeleton-with-avatar\": \"!!nzAvatar\", \"class.ant-skeleton-active\": \"nzActive\", \"class.ant-skeleton-round\": \"!!nzRound\" }, classAttribute: \"ant-skeleton\" }, exportAs: [\"nzSkeleton\"], usesOnChanges: true, ngImport: i0, template: `\n    <ng-container *ngIf=\"nzLoading\">\n      <div class=\"ant-skeleton-header\" *ngIf=\"!!nzAvatar\">\n        <nz-skeleton-element\n          nzType=\"avatar\"\n          [nzSize]=\"avatar.size || 'default'\"\n          [nzShape]=\"avatar.shape || 'circle'\"\n        ></nz-skeleton-element>\n      </div>\n      <div class=\"ant-skeleton-content\">\n        <h3 *ngIf=\"!!nzTitle\" class=\"ant-skeleton-title\" [style.width]=\"toCSSUnit(title.width)\"></h3>\n        <ul *ngIf=\"!!nzParagraph\" class=\"ant-skeleton-paragraph\">\n          <li *ngFor=\"let row of rowsList; let i = index\" [style.width]=\"toCSSUnit(widthList[i])\"></li>\n        </ul>\n      </div>\n    </ng-container>\n    <ng-container *ngIf=\"!nzLoading\">\n      <ng-content></ng-content>\n    </ng-container>\n  `, isInline: true, dependencies: [{ kind: \"directive\", type: NzSkeletonElementDirective, selector: \"nz-skeleton-element\", inputs: [\"nzActive\", \"nzType\", \"nzBlock\"] }, { kind: \"component\", type: NzSkeletonElementAvatarComponent, selector: \"nz-skeleton-element[nzType=\\\"avatar\\\"]\", inputs: [\"nzShape\", \"nzSize\"] }, { kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzSkeletonComponent, decorators: [{\n            type: Component,\n            args: [{\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    encapsulation: ViewEncapsulation.None,\n                    selector: 'nz-skeleton',\n                    exportAs: 'nzSkeleton',\n                    host: {\n                        class: 'ant-skeleton',\n                        '[class.ant-skeleton-with-avatar]': '!!nzAvatar',\n                        '[class.ant-skeleton-active]': 'nzActive',\n                        '[class.ant-skeleton-round]': '!!nzRound'\n                    },\n                    template: `\n    <ng-container *ngIf=\"nzLoading\">\n      <div class=\"ant-skeleton-header\" *ngIf=\"!!nzAvatar\">\n        <nz-skeleton-element\n          nzType=\"avatar\"\n          [nzSize]=\"avatar.size || 'default'\"\n          [nzShape]=\"avatar.shape || 'circle'\"\n        ></nz-skeleton-element>\n      </div>\n      <div class=\"ant-skeleton-content\">\n        <h3 *ngIf=\"!!nzTitle\" class=\"ant-skeleton-title\" [style.width]=\"toCSSUnit(title.width)\"></h3>\n        <ul *ngIf=\"!!nzParagraph\" class=\"ant-skeleton-paragraph\">\n          <li *ngFor=\"let row of rowsList; let i = index\" [style.width]=\"toCSSUnit(widthList[i])\"></li>\n        </ul>\n      </div>\n    </ng-container>\n    <ng-container *ngIf=\"!nzLoading\">\n      <ng-content></ng-content>\n    </ng-container>\n  `,\n                    imports: [NzSkeletonElementDirective, NzSkeletonElementAvatarComponent, NgIf, NgForOf],\n                    standalone: true\n                }]\n        }], ctorParameters: () => [{ type: i0.ChangeDetectorRef }], propDecorators: { nzActive: [{\n                type: Input\n            }], nzLoading: [{\n                type: Input\n            }], nzRound: [{\n                type: Input\n            }], nzTitle: [{\n                type: Input\n            }], nzAvatar: [{\n                type: Input\n            }], nzParagraph: [{\n                type: Input\n            }] } });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzSkeletonModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzSkeletonModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.3.8\", ngImport: i0, type: NzSkeletonModule, imports: [NzSkeletonElementDirective,\n            NzSkeletonComponent,\n            NzSkeletonElementButtonComponent,\n            NzSkeletonElementAvatarComponent,\n            NzSkeletonElementImageComponent,\n            NzSkeletonElementInputComponent], exports: [NzSkeletonElementDirective,\n            NzSkeletonComponent,\n            NzSkeletonElementButtonComponent,\n            NzSkeletonElementAvatarComponent,\n            NzSkeletonElementImageComponent,\n            NzSkeletonElementInputComponent] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzSkeletonModule }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzSkeletonModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        NzSkeletonElementDirective,\n                        NzSkeletonComponent,\n                        NzSkeletonElementButtonComponent,\n                        NzSkeletonElementAvatarComponent,\n                        NzSkeletonElementImageComponent,\n                        NzSkeletonElementInputComponent\n                    ],\n                    exports: [\n                        NzSkeletonElementDirective,\n                        NzSkeletonComponent,\n                        NzSkeletonElementButtonComponent,\n                        NzSkeletonElementAvatarComponent,\n                        NzSkeletonElementImageComponent,\n                        NzSkeletonElementInputComponent\n                    ]\n                }]\n        }] });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzSkeletonComponent, NzSkeletonElementAvatarComponent, NzSkeletonElementButtonComponent, NzSkeletonElementDirective, NzSkeletonElementImageComponent, NzSkeletonElementInputComponent, NzSkeletonModule };\n"], "mappings": "AAAA,SAASA,OAAO,EAAEC,IAAI,EAAEC,OAAO,QAAQ,iBAAiB;AACxD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,KAAK,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,QAAQ,QAAQ,eAAe;AACjH,SAASC,YAAY,EAAEC,UAAU,QAAQ,yBAAyB;AAClE,SAASC,UAAU,QAAQ,OAAO;AAAC,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,kDAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAOiEhB,EAAE,CAAAkB,cAAA,YAsQ7C,CAAC;IAtQ0ClB,EAAE,CAAAmB,SAAA,4BA2QxE,CAAC;IA3QqEnB,EAAE,CAAAoB,YAAA,CA4Q3F,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,MAAA,GA5QwFrB,EAAE,CAAAsB,aAAA;IAAFtB,EAAE,CAAAuB,SAAA,CAyQ1D,CAAC;IAzQuDvB,EAAE,CAAAwB,UAAA,WAAAH,MAAA,CAAAI,MAAA,CAAAC,IAAA,aAyQ1D,CAAC,YAAAL,MAAA,CAAAI,MAAA,CAAAE,KAAA,YACA,CAAC;EAAA;AAAA;AAAA,SAAAC,iDAAAZ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA1QsDhB,EAAE,CAAAmB,SAAA,WA8QF,CAAC;EAAA;EAAA,IAAAH,EAAA;IAAA,MAAAK,MAAA,GA9QDrB,EAAE,CAAAsB,aAAA;IAAFtB,EAAE,CAAA6B,WAAA,UAAAR,MAAA,CAAAS,SAAA,CAAAT,MAAA,CAAAU,KAAA,CAAAC,KAAA,CA8QR,CAAC;EAAA;AAAA;AAAA,SAAAC,sDAAAjB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9QKhB,EAAE,CAAAmB,SAAA,QAgRA,CAAC;EAAA;EAAA,IAAAH,EAAA;IAAA,MAAAkB,IAAA,GAAAjB,GAAA,CAAAkB,KAAA;IAAA,MAAAd,MAAA,GAhRHrB,EAAE,CAAAsB,aAAA;IAAFtB,EAAE,CAAA6B,WAAA,UAAAR,MAAA,CAAAS,SAAA,CAAAT,MAAA,CAAAe,SAAA,CAAAF,IAAA,EAgRN,CAAC;EAAA;AAAA;AAAA,SAAAG,iDAAArB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAhRGhB,EAAE,CAAAkB,cAAA,WA+QtC,CAAC;IA/QmClB,EAAE,CAAAsC,UAAA,IAAAL,qDAAA,eAgRL,CAAC;IAhREjC,EAAE,CAAAoB,YAAA,CAiR1F,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,MAAA,GAjRuFrB,EAAE,CAAAsB,aAAA;IAAFtB,EAAE,CAAAuB,SAAA,CAgR5D,CAAC;IAhRyDvB,EAAE,CAAAwB,UAAA,YAAAH,MAAA,CAAAkB,QAgR5D,CAAC;EAAA;AAAA;AAAA,SAAAC,4CAAAxB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAhRyDhB,EAAE,CAAAyC,uBAAA,EAqQnE,CAAC;IArQgEzC,EAAE,CAAAsC,UAAA,IAAAvB,iDAAA,gBAsQ7C,CAAC;IAtQ0Cf,EAAE,CAAAkB,cAAA,YA6Q/D,CAAC;IA7Q4DlB,EAAE,CAAAsC,UAAA,IAAAV,gDAAA,eA8QP,CAAC,IAAAS,gDAAA,eAChC,CAAC;IA/QmCrC,EAAE,CAAAoB,YAAA,CAkR3F,CAAC;IAlRwFpB,EAAE,CAAA0C,qBAAA;EAAA;EAAA,IAAA1B,EAAA;IAAA,MAAAK,MAAA,GAAFrB,EAAE,CAAAsB,aAAA;IAAFtB,EAAE,CAAAuB,SAAA,CAsQ/C,CAAC;IAtQ4CvB,EAAE,CAAAwB,UAAA,WAAAH,MAAA,CAAAsB,QAsQ/C,CAAC;IAtQ4C3C,EAAE,CAAAuB,SAAA,EA8Q3E,CAAC;IA9QwEvB,EAAE,CAAAwB,UAAA,WAAAH,MAAA,CAAAuB,OA8Q3E,CAAC;IA9QwE5C,EAAE,CAAAuB,SAAA,CA+QvE,CAAC;IA/QoEvB,EAAE,CAAAwB,UAAA,WAAAH,MAAA,CAAAwB,WA+QvE,CAAC;EAAA;AAAA;AAAA,SAAAC,4CAAA9B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/QoEhB,EAAE,CAAAyC,uBAAA,EAoRlE,CAAC;IApR+DzC,EAAE,CAAA+C,YAAA,EAqRxE,CAAC;IArRqE/C,EAAE,CAAA0C,qBAAA;EAAA;AAAA;AALtG,MAAMM,0BAA0B,CAAC;EAC7BC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,OAAO,GAAG,KAAK;EACxB;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,mCAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFN,0BAA0B;IAAA,CAAmD;EAAE;EACzL;IAAS,IAAI,CAACO,IAAI,kBAD8EvD,EAAE,CAAAwD,iBAAA;MAAAC,IAAA,EACJT,0BAA0B;MAAAU,SAAA;MAAAC,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,wCAAA9C,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADxBhB,EAAE,CAAA+D,WAAA,wBAAA9C,GAAA,CAAAiC,QACqB,CAAC,uBAAAjC,GAAA,CAAAkC,OAAD,CAAC;QAAA;MAAA;MAAAa,MAAA;QAAAd,QAAA;QAAAe,MAAA;QAAAd,OAAA;MAAA;MAAAe,UAAA;IAAA,EAA6S;EAAE;AAC3a;AACAzD,UAAU,CAAC,CACPF,YAAY,CAAC,CAAC,CACjB,EAAEyC,0BAA0B,CAACmB,SAAS,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;AAC3D;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KANoGpE,EAAE,CAAAqE,iBAAA,CAMXrB,0BAA0B,EAAc,CAAC;IACxHS,IAAI,EAAExD,SAAS;IACfqE,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,qBAAqB;MAC/BC,IAAI,EAAE;QACFC,KAAK,EAAE,mCAAmC;QAC1C,6BAA6B,EAAE,UAAU;QACzC,4BAA4B,EAAE;MAClC,CAAC;MACDP,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEhB,QAAQ,EAAE,CAAC;MACnDO,IAAI,EAAEvD;IACV,CAAC,CAAC;IAAE+D,MAAM,EAAE,CAAC;MACTR,IAAI,EAAEvD;IACV,CAAC,CAAC;IAAEiD,OAAO,EAAE,CAAC;MACVM,IAAI,EAAEvD;IACV,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMwE,gCAAgC,CAAC;EACnCzB,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC0B,OAAO,GAAG,SAAS;IACxB,IAAI,CAACC,MAAM,GAAG,SAAS;EAC3B;EACA;IAAS,IAAI,CAACxB,IAAI,YAAAyB,yCAAAvB,CAAA;MAAA,YAAAA,CAAA,IAAwFoB,gCAAgC;IAAA,CAAmD;EAAE;EAC/L;IAAS,IAAI,CAACI,IAAI,kBA9B8E9E,EAAE,CAAA+E,iBAAA;MAAAtB,IAAA,EA8BJiB,gCAAgC;MAAAhB,SAAA;MAAAM,MAAA;QAAAW,OAAA;QAAAC,MAAA;MAAA;MAAAV,UAAA;MAAAc,QAAA,GA9B9BhF,EAAE,CAAAiF,mBAAA;MAAAC,KAAA,EAAAxE,GAAA;MAAAyE,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0CAAAvE,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFhB,EAAE,CAAAmB,SAAA,aAsC3F,CAAC;QAAA;QAAA,IAAAH,EAAA;UAtCwFhB,EAAE,CAAA+D,WAAA,+BAAA9C,GAAA,CAAA0D,OAAA,aAiCxC,CAAC,8BAAA1D,GAAA,CAAA0D,OAAA,YACH,CAAC,+BAAA1D,GAAA,CAAA0D,OAAA,aACC,CAAC,2BAAA1D,GAAA,CAAA2D,MAAA,YACP,CAAC,2BAAA3D,GAAA,CAAA2D,MAAA,YACD,CAAC;QAAA;MAAA;MAAAY,aAAA;MAAAC,eAAA;IAAA,EAEiB;EAAE;AAC5E;AACA;EAAA,QAAArB,SAAA,oBAAAA,SAAA,KAzCoGpE,EAAE,CAAAqE,iBAAA,CAyCXK,gCAAgC,EAAc,CAAC;IAC9HjB,IAAI,EAAEtD,SAAS;IACfmE,IAAI,EAAE,CAAC;MACCmB,eAAe,EAAErF,uBAAuB,CAACsF,MAAM;MAC/CnB,QAAQ,EAAE,sCAAsC;MAChDe,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiBpB,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,QAAkB;IAAES,OAAO,EAAE,CAAC;MACxBlB,IAAI,EAAEvD;IACV,CAAC,CAAC;IAAE0E,MAAM,EAAE,CAAC;MACTnB,IAAI,EAAEvD;IACV,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMyF,gCAAgC,CAAC;EACnC1C,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC0B,OAAO,GAAG,QAAQ;IACvB,IAAI,CAACC,MAAM,GAAG,SAAS;IACvB,IAAI,CAACgB,QAAQ,GAAG,CAAC,CAAC;EACtB;EACAC,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAIA,OAAO,CAAClB,MAAM,IAAI,OAAO,IAAI,CAACA,MAAM,KAAK,QAAQ,EAAE;MACnD,MAAMmB,UAAU,GAAI,GAAE,IAAI,CAACnB,MAAO,IAAG;MACrC,IAAI,CAACgB,QAAQ,GAAG;QAAE5D,KAAK,EAAE+D,UAAU;QAAEC,MAAM,EAAED,UAAU;QAAE,aAAa,EAAEA;MAAW,CAAC;IACxF,CAAC,MACI;MACD,IAAI,CAACH,QAAQ,GAAG,CAAC,CAAC;IACtB;EACJ;EACA;IAAS,IAAI,CAACxC,IAAI,YAAA6C,yCAAA3C,CAAA;MAAA,YAAAA,CAAA,IAAwFqC,gCAAgC;IAAA,CAAmD;EAAE;EAC/L;IAAS,IAAI,CAACb,IAAI,kBA/E8E9E,EAAE,CAAA+E,iBAAA;MAAAtB,IAAA,EA+EJkC,gCAAgC;MAAAjC,SAAA;MAAAM,MAAA;QAAAW,OAAA;QAAAC,MAAA;MAAA;MAAAV,UAAA;MAAAc,QAAA,GA/E9BhF,EAAE,CAAAkG,oBAAA,EAAFlG,EAAE,CAAAiF,mBAAA;MAAAC,KAAA,EAAAvE,GAAA;MAAAwE,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAa,0CAAAnF,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFhB,EAAE,CAAAmB,SAAA,aAuF3F,CAAC;QAAA;QAAA,IAAAH,EAAA;UAvFwFhB,EAAE,CAAA+D,WAAA,+BAAA9C,GAAA,CAAA0D,OAAA,aAkFxC,CAAC,+BAAA1D,GAAA,CAAA0D,OAAA,aACD,CAAC,2BAAA1D,GAAA,CAAA2D,MAAA,YACP,CAAC,2BAAA3D,GAAA,CAAA2D,MAAA,YACD,CAAC;UArF2C5E,EAAE,CAAAwB,UAAA,YAAAP,GAAA,CAAA2E,QAsF7E,CAAC;QAAA;MAAA;MAAAQ,YAAA,GAEqCvG,OAAO;MAAA2F,aAAA;MAAAC,eAAA;IAAA,EAAsG;EAAE;AAC9K;AACA;EAAA,QAAArB,SAAA,oBAAAA,SAAA,KA1FoGpE,EAAE,CAAAqE,iBAAA,CA0FXsB,gCAAgC,EAAc,CAAC;IAC9HlC,IAAI,EAAEtD,SAAS;IACfmE,IAAI,EAAE,CAAC;MACCmB,eAAe,EAAErF,uBAAuB,CAACsF,MAAM;MAC/CnB,QAAQ,EAAE,sCAAsC;MAChDe,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiBe,OAAO,EAAE,CAACxG,OAAO,CAAC;MAClBqE,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,QAAkB;IAAES,OAAO,EAAE,CAAC;MACxBlB,IAAI,EAAEvD;IACV,CAAC,CAAC;IAAE0E,MAAM,EAAE,CAAC;MACTnB,IAAI,EAAEvD;IACV,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMoG,+BAA+B,CAAC;EAClCrD,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC2B,MAAM,GAAG,SAAS;EAC3B;EACA;IAAS,IAAI,CAACxB,IAAI,YAAAmD,wCAAAjD,CAAA;MAAA,YAAAA,CAAA,IAAwFgD,+BAA+B;IAAA,CAAmD;EAAE;EAC9L;IAAS,IAAI,CAACxB,IAAI,kBAtH8E9E,EAAE,CAAA+E,iBAAA;MAAAtB,IAAA,EAsHJ6C,+BAA+B;MAAA5C,SAAA;MAAAM,MAAA;QAAAY,MAAA;MAAA;MAAAV,UAAA;MAAAc,QAAA,GAtH7BhF,EAAE,CAAAiF,mBAAA;MAAAC,KAAA,EAAAtE,GAAA;MAAAuE,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAkB,yCAAAxF,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFhB,EAAE,CAAAmB,SAAA,aA2H3F,CAAC;QAAA;QAAA,IAAAH,EAAA;UA3HwFhB,EAAE,CAAA+D,WAAA,0BAAA9C,GAAA,CAAA2D,MAAA,YAyH/C,CAAC,0BAAA3D,GAAA,CAAA2D,MAAA,YACD,CAAC;QAAA;MAAA;MAAAY,aAAA;MAAAC,eAAA;IAAA,EAEkB;EAAE;AAC5E;AACA;EAAA,QAAArB,SAAA,oBAAAA,SAAA,KA9HoGpE,EAAE,CAAAqE,iBAAA,CA8HXiC,+BAA+B,EAAc,CAAC;IAC7H7C,IAAI,EAAEtD,SAAS;IACfmE,IAAI,EAAE,CAAC;MACCmB,eAAe,EAAErF,uBAAuB,CAACsF,MAAM;MAC/CnB,QAAQ,EAAE,qCAAqC;MAC/Ce,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA,GAAG;MACiBpB,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEU,MAAM,EAAE,CAAC;MACvBnB,IAAI,EAAEvD;IACV,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMuG,+BAA+B,CAAC;EAClC;IAAS,IAAI,CAACrD,IAAI,YAAAsD,wCAAApD,CAAA;MAAA,YAAAA,CAAA,IAAwFmD,+BAA+B;IAAA,CAAmD;EAAE;EAC9L;IAAS,IAAI,CAAC3B,IAAI,kBAjJ8E9E,EAAE,CAAA+E,iBAAA;MAAAtB,IAAA,EAiJJgD,+BAA+B;MAAA/C,SAAA;MAAAQ,UAAA;MAAAc,QAAA,GAjJ7BhF,EAAE,CAAAiF,mBAAA;MAAAC,KAAA,EAAArE,GAAA;MAAAsE,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAqB,yCAAA3F,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFhB,EAAE,CAAAkB,cAAA,aAkJlE,CAAC;UAlJ+DlB,EAAE,CAAA4G,cAAA;UAAF5G,EAAE,CAAAkB,cAAA,YAmJF,CAAC;UAnJDlB,EAAE,CAAAmB,SAAA,aAuJ7F,CAAC;UAvJ0FnB,EAAE,CAAAoB,YAAA,CAwJ3F,CAAC,CACF,CAAC;QAAA;MAAA;MAAAoE,aAAA;MAAAC,eAAA;IAAA,EAC+D;EAAE;AAC5E;AACA;EAAA,QAAArB,SAAA,oBAAAA,SAAA,KA5JoGpE,EAAE,CAAAqE,iBAAA,CA4JXoC,+BAA+B,EAAc,CAAC;IAC7HhD,IAAI,EAAEtD,SAAS;IACfmE,IAAI,EAAE,CAAC;MACCmB,eAAe,EAAErF,uBAAuB,CAACsF,MAAM;MAC/CnB,QAAQ,EAAE,qCAAqC;MAC/Ce,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiBpB,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA,MAAM2C,mBAAmB,CAAC;EACtB5D,WAAWA,CAAC6D,GAAG,EAAE;IACb,IAAI,CAACA,GAAG,GAAGA,GAAG;IACd,IAAI,CAAC5D,QAAQ,GAAG,KAAK;IACrB,IAAI,CAAC6D,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACpE,OAAO,GAAG,IAAI;IACnB,IAAI,CAACD,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACE,WAAW,GAAG,IAAI;IACvB,IAAI,CAACN,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACH,SAAS,GAAG,EAAE;EACvB;EACAN,SAASA,CAACmF,KAAK,GAAG,EAAE,EAAE;IAClB,OAAOzG,UAAU,CAACyG,KAAK,CAAC;EAC5B;EACAC,aAAaA,CAAA,EAAG;IACZ,MAAMC,SAAS,GAAG,CAAC,CAAC,IAAI,CAACxE,QAAQ;IACjC,MAAMyE,YAAY,GAAG,CAAC,CAAC,IAAI,CAACvE,WAAW;IACvC,IAAIb,KAAK,GAAG,EAAE;IACd,IAAI,CAACmF,SAAS,IAAIC,YAAY,EAAE;MAC5BpF,KAAK,GAAG,KAAK;IACjB,CAAC,MACI,IAAImF,SAAS,IAAIC,YAAY,EAAE;MAChCpF,KAAK,GAAG,KAAK;IACjB;IACA,OAAO;MAAEA,KAAK;MAAE,GAAG,IAAI,CAACqF,QAAQ,CAAC,IAAI,CAACzE,OAAO;IAAE,CAAC;EACpD;EACA0E,cAAcA,CAAA,EAAG;IACb,MAAM3F,KAAK,GAAG,CAAC,CAAC,IAAI,CAACiB,OAAO,IAAI,CAAC,IAAI,CAACC,WAAW,GAAG,QAAQ,GAAG,QAAQ;IACvE,MAAMnB,IAAI,GAAG,OAAO;IACpB,OAAO;MAAEC,KAAK;MAAED,IAAI;MAAE,GAAG,IAAI,CAAC2F,QAAQ,CAAC,IAAI,CAAC1E,QAAQ;IAAE,CAAC;EAC3D;EACA4E,iBAAiBA,CAAA,EAAG;IAChB,MAAMJ,SAAS,GAAG,CAAC,CAAC,IAAI,CAACxE,QAAQ;IACjC,MAAM6E,QAAQ,GAAG,CAAC,CAAC,IAAI,CAAC5E,OAAO;IAC/B,MAAM6E,UAAU,GAAG,CAAC,CAAC;IACrB;IACA,IAAI,CAACN,SAAS,IAAI,CAACK,QAAQ,EAAE;MACzBC,UAAU,CAACzF,KAAK,GAAG,KAAK;IAC5B;IACA;IACA,IAAI,CAACmF,SAAS,IAAIK,QAAQ,EAAE;MACxBC,UAAU,CAACC,IAAI,GAAG,CAAC;IACvB,CAAC,MACI;MACDD,UAAU,CAACC,IAAI,GAAG,CAAC;IACvB;IACA,OAAO;MAAE,GAAGD,UAAU;MAAE,GAAG,IAAI,CAACJ,QAAQ,CAAC,IAAI,CAACxE,WAAW;IAAE,CAAC;EAChE;EACAwE,QAAQA,CAACM,IAAI,EAAE;IACX,OAAOA,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,GAAGA,IAAI,GAAG,CAAC,CAAC;EACvD;EACAC,YAAYA,CAAA,EAAG;IACX,MAAM;MAAE5F,KAAK;MAAE0F;IAAK,CAAC,GAAG,IAAI,CAACG,SAAS;IACtC,IAAIzF,SAAS,GAAG,EAAE;IAClB,IAAIJ,KAAK,IAAI8F,KAAK,CAACC,OAAO,CAAC/F,KAAK,CAAC,EAAE;MAC/BI,SAAS,GAAGJ,KAAK;IACrB,CAAC,MACI,IAAIA,KAAK,IAAI,CAAC8F,KAAK,CAACC,OAAO,CAAC/F,KAAK,CAAC,EAAE;MACrCI,SAAS,GAAG,EAAE;MACdA,SAAS,CAACsF,IAAI,GAAG,CAAC,CAAC,GAAG1F,KAAK;IAC/B;IACA,OAAOI,SAAS;EACpB;EACA4F,WAAWA,CAAA,EAAG;IACV,IAAI,CAACjG,KAAK,GAAG,IAAI,CAACmF,aAAa,CAAC,CAAC;IACjC,IAAI,CAACzF,MAAM,GAAG,IAAI,CAAC6F,cAAc,CAAC,CAAC;IACnC,IAAI,CAACO,SAAS,GAAG,IAAI,CAACN,iBAAiB,CAAC,CAAC;IACzC,IAAI,CAAChF,QAAQ,GAAG,CAAC,GAAGuF,KAAK,CAAC,IAAI,CAACD,SAAS,CAACH,IAAI,CAAC,CAAC;IAC/C,IAAI,CAACtF,SAAS,GAAG,IAAI,CAACwF,YAAY,CAAC,CAAC;IACpC,IAAI,CAACd,GAAG,CAACmB,YAAY,CAAC,CAAC;EAC3B;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACF,WAAW,CAAC,CAAC;EACtB;EACAnC,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAIA,OAAO,CAAClD,OAAO,IAAIkD,OAAO,CAACnD,QAAQ,IAAImD,OAAO,CAACjD,WAAW,EAAE;MAC5D,IAAI,CAACmF,WAAW,CAAC,CAAC;IACtB;EACJ;EACA;IAAS,IAAI,CAAC5E,IAAI,YAAA+E,4BAAA7E,CAAA;MAAA,YAAAA,CAAA,IAAwFuD,mBAAmB,EAnQ7B7G,EAAE,CAAAoI,iBAAA,CAmQ6CpI,EAAE,CAACqI,iBAAiB;IAAA,CAA4C;EAAE;EACjN;IAAS,IAAI,CAACvD,IAAI,kBApQ8E9E,EAAE,CAAA+E,iBAAA;MAAAtB,IAAA,EAoQJoD,mBAAmB;MAAAnD,SAAA;MAAAC,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAyE,iCAAAtH,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UApQjBhB,EAAE,CAAA+D,WAAA,+BAAA9C,GAAA,CAAA0B,QAoQc,CAAC,wBAAA1B,GAAA,CAAAiC,QAAD,CAAC,yBAAAjC,GAAA,CAAA+F,OAAD,CAAC;QAAA;MAAA;MAAAhD,MAAA;QAAAd,QAAA;QAAA6D,SAAA;QAAAC,OAAA;QAAApE,OAAA;QAAAD,QAAA;QAAAE,WAAA;MAAA;MAAA0F,QAAA;MAAArE,UAAA;MAAAc,QAAA,GApQjBhF,EAAE,CAAAkG,oBAAA,EAAFlG,EAAE,CAAAiF,mBAAA;MAAAuD,kBAAA,EAAA1H,GAAA;MAAAqE,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAmD,6BAAAzH,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFhB,EAAE,CAAA0I,eAAA;UAAF1I,EAAE,CAAAsC,UAAA,IAAAE,2CAAA,yBAqQnE,CAAC,IAAAM,2CAAA,yBAeA,CAAC;QAAA;QAAA,IAAA9B,EAAA;UApR+DhB,EAAE,CAAAwB,UAAA,SAAAP,GAAA,CAAA8F,SAqQrE,CAAC;UArQkE/G,EAAE,CAAAuB,SAAA,CAoRpE,CAAC;UApRiEvB,EAAE,CAAAwB,UAAA,UAAAP,GAAA,CAAA8F,SAoRpE,CAAC;QAAA;MAAA;MAAAX,YAAA,GAG4BpD,0BAA0B,EAA2G2C,gCAAgC,EAAkH7F,IAAI,EAA6FC,OAAO;MAAAyF,aAAA;MAAAC,eAAA;IAAA,EAAwL;EAAE;AACxnB;AACA;EAAA,QAAArB,SAAA,oBAAAA,SAAA,KAzRoGpE,EAAE,CAAAqE,iBAAA,CAyRXwC,mBAAmB,EAAc,CAAC;IACjHpD,IAAI,EAAEtD,SAAS;IACfmE,IAAI,EAAE,CAAC;MACCmB,eAAe,EAAErF,uBAAuB,CAACsF,MAAM;MAC/CF,aAAa,EAAEnF,iBAAiB,CAACsI,IAAI;MACrCpE,QAAQ,EAAE,aAAa;MACvBgE,QAAQ,EAAE,YAAY;MACtB/D,IAAI,EAAE;QACFC,KAAK,EAAE,cAAc;QACrB,kCAAkC,EAAE,YAAY;QAChD,6BAA6B,EAAE,UAAU;QACzC,4BAA4B,EAAE;MAClC,CAAC;MACDa,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiBe,OAAO,EAAE,CAACrD,0BAA0B,EAAE2C,gCAAgC,EAAE7F,IAAI,EAAEC,OAAO,CAAC;MACtFmE,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAET,IAAI,EAAEzD,EAAE,CAACqI;EAAkB,CAAC,CAAC,EAAkB;IAAEnF,QAAQ,EAAE,CAAC;MACjFO,IAAI,EAAEvD;IACV,CAAC,CAAC;IAAE6G,SAAS,EAAE,CAAC;MACZtD,IAAI,EAAEvD;IACV,CAAC,CAAC;IAAE8G,OAAO,EAAE,CAAC;MACVvD,IAAI,EAAEvD;IACV,CAAC,CAAC;IAAE0C,OAAO,EAAE,CAAC;MACVa,IAAI,EAAEvD;IACV,CAAC,CAAC;IAAEyC,QAAQ,EAAE,CAAC;MACXc,IAAI,EAAEvD;IACV,CAAC,CAAC;IAAE2C,WAAW,EAAE,CAAC;MACdY,IAAI,EAAEvD;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAM0I,gBAAgB,CAAC;EACnB;IAAS,IAAI,CAACxF,IAAI,YAAAyF,yBAAAvF,CAAA;MAAA,YAAAA,CAAA,IAAwFsF,gBAAgB;IAAA,CAAkD;EAAE;EAC9K;IAAS,IAAI,CAACE,IAAI,kBAjV8E9I,EAAE,CAAA+I,gBAAA;MAAAtF,IAAA,EAiVSmF,gBAAgB;MAAAvC,OAAA,GAAYrD,0BAA0B,EACzJ6D,mBAAmB,EACnBnC,gCAAgC,EAChCiB,gCAAgC,EAChCc,+BAA+B,EAC/BH,+BAA+B;MAAA0C,OAAA,GAAahG,0BAA0B,EACtE6D,mBAAmB,EACnBnC,gCAAgC,EAChCiB,gCAAgC,EAChCc,+BAA+B,EAC/BH,+BAA+B;IAAA,EAAI;EAAE;EAC7C;IAAS,IAAI,CAAC2C,IAAI,kBA5V8EjJ,EAAE,CAAAkJ,gBAAA,IA4V4B;EAAE;AACpI;AACA;EAAA,QAAA9E,SAAA,oBAAAA,SAAA,KA9VoGpE,EAAE,CAAAqE,iBAAA,CA8VXuE,gBAAgB,EAAc,CAAC;IAC9GnF,IAAI,EAAEnD,QAAQ;IACdgE,IAAI,EAAE,CAAC;MACC+B,OAAO,EAAE,CACLrD,0BAA0B,EAC1B6D,mBAAmB,EACnBnC,gCAAgC,EAChCiB,gCAAgC,EAChCc,+BAA+B,EAC/BH,+BAA+B,CAClC;MACD0C,OAAO,EAAE,CACLhG,0BAA0B,EAC1B6D,mBAAmB,EACnBnC,gCAAgC,EAChCiB,gCAAgC,EAChCc,+BAA+B,EAC/BH,+BAA+B;IAEvC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASO,mBAAmB,EAAElB,gCAAgC,EAAEjB,gCAAgC,EAAE1B,0BAA0B,EAAEyD,+BAA+B,EAAEH,+BAA+B,EAAEsC,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}