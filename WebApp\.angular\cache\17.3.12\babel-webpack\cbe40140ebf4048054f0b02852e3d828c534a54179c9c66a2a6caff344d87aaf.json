{"ast": null, "code": "import { Leaf<PERSON><PERSON>, Scope } from 'parchment';\nimport { cloneDeep, isEqual } from 'lodash-es';\nimport Emitter from './emitter.js';\nimport logger from './logger.js';\nconst debug = logger('quill:selection');\nexport class Range {\n  constructor(index) {\n    let length = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n    this.index = index;\n    this.length = length;\n  }\n}\nclass Selection {\n  constructor(scroll, emitter) {\n    this.emitter = emitter;\n    this.scroll = scroll;\n    this.composing = false;\n    this.mouseDown = false;\n    this.root = this.scroll.domNode;\n    // @ts-expect-error\n    this.cursor = this.scroll.create('cursor', this);\n    // savedRange is last non-null range\n    this.savedRange = new Range(0, 0);\n    this.lastRange = this.savedRange;\n    this.lastNative = null;\n    this.handleComposition();\n    this.handleDragging();\n    this.emitter.listenDOM('selectionchange', document, () => {\n      if (!this.mouseDown && !this.composing) {\n        setTimeout(this.update.bind(this, Emitter.sources.USER), 1);\n      }\n    });\n    this.emitter.on(Emitter.events.SCROLL_BEFORE_UPDATE, () => {\n      if (!this.hasFocus()) return;\n      const native = this.getNativeRange();\n      if (native == null) return;\n      if (native.start.node === this.cursor.textNode) return; // cursor.restore() will handle\n      this.emitter.once(Emitter.events.SCROLL_UPDATE, (source, mutations) => {\n        try {\n          if (this.root.contains(native.start.node) && this.root.contains(native.end.node)) {\n            this.setNativeRange(native.start.node, native.start.offset, native.end.node, native.end.offset);\n          }\n          const triggeredByTyping = mutations.some(mutation => mutation.type === 'characterData' || mutation.type === 'childList' || mutation.type === 'attributes' && mutation.target === this.root);\n          this.update(triggeredByTyping ? Emitter.sources.SILENT : source);\n        } catch (ignored) {\n          // ignore\n        }\n      });\n    });\n    this.emitter.on(Emitter.events.SCROLL_OPTIMIZE, (mutations, context) => {\n      if (context.range) {\n        const {\n          startNode,\n          startOffset,\n          endNode,\n          endOffset\n        } = context.range;\n        this.setNativeRange(startNode, startOffset, endNode, endOffset);\n        this.update(Emitter.sources.SILENT);\n      }\n    });\n    this.update(Emitter.sources.SILENT);\n  }\n  handleComposition() {\n    this.emitter.on(Emitter.events.COMPOSITION_BEFORE_START, () => {\n      this.composing = true;\n    });\n    this.emitter.on(Emitter.events.COMPOSITION_END, () => {\n      this.composing = false;\n      if (this.cursor.parent) {\n        const range = this.cursor.restore();\n        if (!range) return;\n        setTimeout(() => {\n          this.setNativeRange(range.startNode, range.startOffset, range.endNode, range.endOffset);\n        }, 1);\n      }\n    });\n  }\n  handleDragging() {\n    this.emitter.listenDOM('mousedown', document.body, () => {\n      this.mouseDown = true;\n    });\n    this.emitter.listenDOM('mouseup', document.body, () => {\n      this.mouseDown = false;\n      this.update(Emitter.sources.USER);\n    });\n  }\n  focus() {\n    if (this.hasFocus()) return;\n    this.root.focus({\n      preventScroll: true\n    });\n    this.setRange(this.savedRange);\n  }\n  format(format, value) {\n    this.scroll.update();\n    const nativeRange = this.getNativeRange();\n    if (nativeRange == null || !nativeRange.native.collapsed || this.scroll.query(format, Scope.BLOCK)) return;\n    if (nativeRange.start.node !== this.cursor.textNode) {\n      const blot = this.scroll.find(nativeRange.start.node, false);\n      if (blot == null) return;\n      // TODO Give blot ability to not split\n      if (blot instanceof LeafBlot) {\n        const after = blot.split(nativeRange.start.offset);\n        blot.parent.insertBefore(this.cursor, after);\n      } else {\n        // @ts-expect-error TODO: nativeRange.start.node doesn't seem to match function signature\n        blot.insertBefore(this.cursor, nativeRange.start.node); // Should never happen\n      }\n      this.cursor.attach();\n    }\n    this.cursor.format(format, value);\n    this.scroll.optimize();\n    this.setNativeRange(this.cursor.textNode, this.cursor.textNode.data.length);\n    this.update();\n  }\n  getBounds(index) {\n    let length = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n    const scrollLength = this.scroll.length();\n    index = Math.min(index, scrollLength - 1);\n    length = Math.min(index + length, scrollLength - 1) - index;\n    let node;\n    let [leaf, offset] = this.scroll.leaf(index);\n    if (leaf == null) return null;\n    if (length > 0 && offset === leaf.length()) {\n      const [next] = this.scroll.leaf(index + 1);\n      if (next) {\n        const [line] = this.scroll.line(index);\n        const [nextLine] = this.scroll.line(index + 1);\n        if (line === nextLine) {\n          leaf = next;\n          offset = 0;\n        }\n      }\n    }\n    [node, offset] = leaf.position(offset, true);\n    const range = document.createRange();\n    if (length > 0) {\n      range.setStart(node, offset);\n      [leaf, offset] = this.scroll.leaf(index + length);\n      if (leaf == null) return null;\n      [node, offset] = leaf.position(offset, true);\n      range.setEnd(node, offset);\n      return range.getBoundingClientRect();\n    }\n    let side = 'left';\n    let rect;\n    if (node instanceof Text) {\n      // Return null if the text node is empty because it is\n      // not able to get a useful client rect:\n      // https://github.com/w3c/csswg-drafts/issues/2514.\n      // Empty text nodes are most likely caused by TextBlot#optimize()\n      // not getting called when editor content changes.\n      if (!node.data.length) {\n        return null;\n      }\n      if (offset < node.data.length) {\n        range.setStart(node, offset);\n        range.setEnd(node, offset + 1);\n      } else {\n        range.setStart(node, offset - 1);\n        range.setEnd(node, offset);\n        side = 'right';\n      }\n      rect = range.getBoundingClientRect();\n    } else {\n      if (!(leaf.domNode instanceof Element)) return null;\n      rect = leaf.domNode.getBoundingClientRect();\n      if (offset > 0) side = 'right';\n    }\n    return {\n      bottom: rect.top + rect.height,\n      height: rect.height,\n      left: rect[side],\n      right: rect[side],\n      top: rect.top,\n      width: 0\n    };\n  }\n  getNativeRange() {\n    const selection = document.getSelection();\n    if (selection == null || selection.rangeCount <= 0) return null;\n    const nativeRange = selection.getRangeAt(0);\n    if (nativeRange == null) return null;\n    const range = this.normalizeNative(nativeRange);\n    debug.info('getNativeRange', range);\n    return range;\n  }\n  getRange() {\n    const root = this.scroll.domNode;\n    if ('isConnected' in root && !root.isConnected) {\n      // document.getSelection() forces layout on Blink, so we trend to\n      // not calling it.\n      return [null, null];\n    }\n    const normalized = this.getNativeRange();\n    if (normalized == null) return [null, null];\n    const range = this.normalizedToRange(normalized);\n    return [range, normalized];\n  }\n  hasFocus() {\n    return document.activeElement === this.root || document.activeElement != null && contains(this.root, document.activeElement);\n  }\n  normalizedToRange(range) {\n    const positions = [[range.start.node, range.start.offset]];\n    if (!range.native.collapsed) {\n      positions.push([range.end.node, range.end.offset]);\n    }\n    const indexes = positions.map(position => {\n      const [node, offset] = position;\n      const blot = this.scroll.find(node, true);\n      // @ts-expect-error Fix me later\n      const index = blot.offset(this.scroll);\n      if (offset === 0) {\n        return index;\n      }\n      if (blot instanceof LeafBlot) {\n        return index + blot.index(node, offset);\n      }\n      // @ts-expect-error Fix me later\n      return index + blot.length();\n    });\n    const end = Math.min(Math.max(...indexes), this.scroll.length() - 1);\n    const start = Math.min(end, ...indexes);\n    return new Range(start, end - start);\n  }\n  normalizeNative(nativeRange) {\n    if (!contains(this.root, nativeRange.startContainer) || !nativeRange.collapsed && !contains(this.root, nativeRange.endContainer)) {\n      return null;\n    }\n    const range = {\n      start: {\n        node: nativeRange.startContainer,\n        offset: nativeRange.startOffset\n      },\n      end: {\n        node: nativeRange.endContainer,\n        offset: nativeRange.endOffset\n      },\n      native: nativeRange\n    };\n    [range.start, range.end].forEach(position => {\n      let {\n        node,\n        offset\n      } = position;\n      while (!(node instanceof Text) && node.childNodes.length > 0) {\n        if (node.childNodes.length > offset) {\n          node = node.childNodes[offset];\n          offset = 0;\n        } else if (node.childNodes.length === offset) {\n          // @ts-expect-error Fix me later\n          node = node.lastChild;\n          if (node instanceof Text) {\n            offset = node.data.length;\n          } else if (node.childNodes.length > 0) {\n            // Container case\n            offset = node.childNodes.length;\n          } else {\n            // Embed case\n            offset = node.childNodes.length + 1;\n          }\n        } else {\n          break;\n        }\n      }\n      position.node = node;\n      position.offset = offset;\n    });\n    return range;\n  }\n  rangeToNative(range) {\n    const scrollLength = this.scroll.length();\n    const getPosition = (index, inclusive) => {\n      index = Math.min(scrollLength - 1, index);\n      const [leaf, leafOffset] = this.scroll.leaf(index);\n      return leaf ? leaf.position(leafOffset, inclusive) : [null, -1];\n    };\n    return [...getPosition(range.index, false), ...getPosition(range.index + range.length, true)];\n  }\n  setNativeRange(startNode, startOffset) {\n    let endNode = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : startNode;\n    let endOffset = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : startOffset;\n    let force = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : false;\n    debug.info('setNativeRange', startNode, startOffset, endNode, endOffset);\n    if (startNode != null && (this.root.parentNode == null || startNode.parentNode == null ||\n    // @ts-expect-error Fix me later\n    endNode.parentNode == null)) {\n      return;\n    }\n    const selection = document.getSelection();\n    if (selection == null) return;\n    if (startNode != null) {\n      if (!this.hasFocus()) this.root.focus({\n        preventScroll: true\n      });\n      const {\n        native\n      } = this.getNativeRange() || {};\n      if (native == null || force || startNode !== native.startContainer || startOffset !== native.startOffset || endNode !== native.endContainer || endOffset !== native.endOffset) {\n        if (startNode instanceof Element && startNode.tagName === 'BR') {\n          // @ts-expect-error Fix me later\n          startOffset = Array.from(startNode.parentNode.childNodes).indexOf(startNode);\n          startNode = startNode.parentNode;\n        }\n        if (endNode instanceof Element && endNode.tagName === 'BR') {\n          // @ts-expect-error Fix me later\n          endOffset = Array.from(endNode.parentNode.childNodes).indexOf(endNode);\n          endNode = endNode.parentNode;\n        }\n        const range = document.createRange();\n        // @ts-expect-error Fix me later\n        range.setStart(startNode, startOffset);\n        // @ts-expect-error Fix me later\n        range.setEnd(endNode, endOffset);\n        selection.removeAllRanges();\n        selection.addRange(range);\n      }\n    } else {\n      selection.removeAllRanges();\n      this.root.blur();\n    }\n  }\n  setRange(range) {\n    let force = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    let source = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : Emitter.sources.API;\n    if (typeof force === 'string') {\n      source = force;\n      force = false;\n    }\n    debug.info('setRange', range);\n    if (range != null) {\n      const args = this.rangeToNative(range);\n      this.setNativeRange(...args, force);\n    } else {\n      this.setNativeRange(null);\n    }\n    this.update(source);\n  }\n  update() {\n    let source = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : Emitter.sources.USER;\n    const oldRange = this.lastRange;\n    const [lastRange, nativeRange] = this.getRange();\n    this.lastRange = lastRange;\n    this.lastNative = nativeRange;\n    if (this.lastRange != null) {\n      this.savedRange = this.lastRange;\n    }\n    if (!isEqual(oldRange, this.lastRange)) {\n      if (!this.composing && nativeRange != null && nativeRange.native.collapsed && nativeRange.start.node !== this.cursor.textNode) {\n        const range = this.cursor.restore();\n        if (range) {\n          this.setNativeRange(range.startNode, range.startOffset, range.endNode, range.endOffset);\n        }\n      }\n      const args = [Emitter.events.SELECTION_CHANGE, cloneDeep(this.lastRange), cloneDeep(oldRange), source];\n      this.emitter.emit(Emitter.events.EDITOR_CHANGE, ...args);\n      if (source !== Emitter.sources.SILENT) {\n        this.emitter.emit(...args);\n      }\n    }\n  }\n}\nfunction contains(parent, descendant) {\n  try {\n    // Firefox inserts inaccessible nodes around video elements\n    descendant.parentNode; // eslint-disable-line @typescript-eslint/no-unused-expressions\n  } catch (e) {\n    return false;\n  }\n  return parent.contains(descendant);\n}\nexport default Selection;", "map": {"version": 3, "names": ["LeafBlot", "<PERSON><PERSON>", "cloneDeep", "isEqual", "Emitter", "logger", "debug", "Range", "constructor", "index", "length", "arguments", "undefined", "Selection", "scroll", "emitter", "composing", "mouseDown", "root", "domNode", "cursor", "create", "savedRange", "<PERSON><PERSON><PERSON><PERSON>", "lastNative", "handleComposition", "handleDragging", "listenDOM", "document", "setTimeout", "update", "bind", "sources", "USER", "on", "events", "SCROLL_BEFORE_UPDATE", "hasFocus", "native", "getNativeRange", "start", "node", "textNode", "once", "SCROLL_UPDATE", "source", "mutations", "contains", "end", "setNativeRange", "offset", "triggeredByTyping", "some", "mutation", "type", "target", "SILENT", "ignored", "SCROLL_OPTIMIZE", "context", "range", "startNode", "startOffset", "endNode", "endOffset", "COMPOSITION_BEFORE_START", "COMPOSITION_END", "parent", "restore", "body", "focus", "preventScroll", "setRang<PERSON>", "format", "value", "nativeRange", "collapsed", "query", "BLOCK", "blot", "find", "after", "split", "insertBefore", "attach", "optimize", "data", "getBounds", "<PERSON><PERSON><PERSON><PERSON>", "Math", "min", "leaf", "next", "line", "nextLine", "position", "createRange", "setStart", "setEnd", "getBoundingClientRect", "side", "rect", "Text", "Element", "bottom", "top", "height", "left", "right", "width", "selection", "getSelection", "rangeCount", "getRangeAt", "normalizeNative", "info", "getRange", "isConnected", "normalized", "normalizedToRange", "activeElement", "positions", "push", "indexes", "map", "max", "startContainer", "endContainer", "for<PERSON>ach", "childNodes", "<PERSON><PERSON><PERSON><PERSON>", "rangeToNative", "getPosition", "inclusive", "leafOffset", "force", "parentNode", "tagName", "Array", "from", "indexOf", "removeAllRanges", "addRange", "blur", "API", "args", "oldRange", "SELECTION_CHANGE", "emit", "EDITOR_CHANGE", "descendant", "e"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/quill/core/selection.js"], "sourcesContent": ["import { Leaf<PERSON><PERSON>, Scope } from 'parchment';\nimport { cloneDeep, isEqual } from 'lodash-es';\nimport Emitter from './emitter.js';\nimport logger from './logger.js';\nconst debug = logger('quill:selection');\nexport class Range {\n  constructor(index) {\n    let length = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n    this.index = index;\n    this.length = length;\n  }\n}\nclass Selection {\n  constructor(scroll, emitter) {\n    this.emitter = emitter;\n    this.scroll = scroll;\n    this.composing = false;\n    this.mouseDown = false;\n    this.root = this.scroll.domNode;\n    // @ts-expect-error\n    this.cursor = this.scroll.create('cursor', this);\n    // savedRange is last non-null range\n    this.savedRange = new Range(0, 0);\n    this.lastRange = this.savedRange;\n    this.lastNative = null;\n    this.handleComposition();\n    this.handleDragging();\n    this.emitter.listenDOM('selectionchange', document, () => {\n      if (!this.mouseDown && !this.composing) {\n        setTimeout(this.update.bind(this, Emitter.sources.USER), 1);\n      }\n    });\n    this.emitter.on(Emitter.events.SCROLL_BEFORE_UPDATE, () => {\n      if (!this.hasFocus()) return;\n      const native = this.getNativeRange();\n      if (native == null) return;\n      if (native.start.node === this.cursor.textNode) return; // cursor.restore() will handle\n      this.emitter.once(Emitter.events.SCROLL_UPDATE, (source, mutations) => {\n        try {\n          if (this.root.contains(native.start.node) && this.root.contains(native.end.node)) {\n            this.setNativeRange(native.start.node, native.start.offset, native.end.node, native.end.offset);\n          }\n          const triggeredByTyping = mutations.some(mutation => mutation.type === 'characterData' || mutation.type === 'childList' || mutation.type === 'attributes' && mutation.target === this.root);\n          this.update(triggeredByTyping ? Emitter.sources.SILENT : source);\n        } catch (ignored) {\n          // ignore\n        }\n      });\n    });\n    this.emitter.on(Emitter.events.SCROLL_OPTIMIZE, (mutations, context) => {\n      if (context.range) {\n        const {\n          startNode,\n          startOffset,\n          endNode,\n          endOffset\n        } = context.range;\n        this.setNativeRange(startNode, startOffset, endNode, endOffset);\n        this.update(Emitter.sources.SILENT);\n      }\n    });\n    this.update(Emitter.sources.SILENT);\n  }\n  handleComposition() {\n    this.emitter.on(Emitter.events.COMPOSITION_BEFORE_START, () => {\n      this.composing = true;\n    });\n    this.emitter.on(Emitter.events.COMPOSITION_END, () => {\n      this.composing = false;\n      if (this.cursor.parent) {\n        const range = this.cursor.restore();\n        if (!range) return;\n        setTimeout(() => {\n          this.setNativeRange(range.startNode, range.startOffset, range.endNode, range.endOffset);\n        }, 1);\n      }\n    });\n  }\n  handleDragging() {\n    this.emitter.listenDOM('mousedown', document.body, () => {\n      this.mouseDown = true;\n    });\n    this.emitter.listenDOM('mouseup', document.body, () => {\n      this.mouseDown = false;\n      this.update(Emitter.sources.USER);\n    });\n  }\n  focus() {\n    if (this.hasFocus()) return;\n    this.root.focus({\n      preventScroll: true\n    });\n    this.setRange(this.savedRange);\n  }\n  format(format, value) {\n    this.scroll.update();\n    const nativeRange = this.getNativeRange();\n    if (nativeRange == null || !nativeRange.native.collapsed || this.scroll.query(format, Scope.BLOCK)) return;\n    if (nativeRange.start.node !== this.cursor.textNode) {\n      const blot = this.scroll.find(nativeRange.start.node, false);\n      if (blot == null) return;\n      // TODO Give blot ability to not split\n      if (blot instanceof LeafBlot) {\n        const after = blot.split(nativeRange.start.offset);\n        blot.parent.insertBefore(this.cursor, after);\n      } else {\n        // @ts-expect-error TODO: nativeRange.start.node doesn't seem to match function signature\n        blot.insertBefore(this.cursor, nativeRange.start.node); // Should never happen\n      }\n      this.cursor.attach();\n    }\n    this.cursor.format(format, value);\n    this.scroll.optimize();\n    this.setNativeRange(this.cursor.textNode, this.cursor.textNode.data.length);\n    this.update();\n  }\n  getBounds(index) {\n    let length = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n    const scrollLength = this.scroll.length();\n    index = Math.min(index, scrollLength - 1);\n    length = Math.min(index + length, scrollLength - 1) - index;\n    let node;\n    let [leaf, offset] = this.scroll.leaf(index);\n    if (leaf == null) return null;\n    if (length > 0 && offset === leaf.length()) {\n      const [next] = this.scroll.leaf(index + 1);\n      if (next) {\n        const [line] = this.scroll.line(index);\n        const [nextLine] = this.scroll.line(index + 1);\n        if (line === nextLine) {\n          leaf = next;\n          offset = 0;\n        }\n      }\n    }\n    [node, offset] = leaf.position(offset, true);\n    const range = document.createRange();\n    if (length > 0) {\n      range.setStart(node, offset);\n      [leaf, offset] = this.scroll.leaf(index + length);\n      if (leaf == null) return null;\n      [node, offset] = leaf.position(offset, true);\n      range.setEnd(node, offset);\n      return range.getBoundingClientRect();\n    }\n    let side = 'left';\n    let rect;\n    if (node instanceof Text) {\n      // Return null if the text node is empty because it is\n      // not able to get a useful client rect:\n      // https://github.com/w3c/csswg-drafts/issues/2514.\n      // Empty text nodes are most likely caused by TextBlot#optimize()\n      // not getting called when editor content changes.\n      if (!node.data.length) {\n        return null;\n      }\n      if (offset < node.data.length) {\n        range.setStart(node, offset);\n        range.setEnd(node, offset + 1);\n      } else {\n        range.setStart(node, offset - 1);\n        range.setEnd(node, offset);\n        side = 'right';\n      }\n      rect = range.getBoundingClientRect();\n    } else {\n      if (!(leaf.domNode instanceof Element)) return null;\n      rect = leaf.domNode.getBoundingClientRect();\n      if (offset > 0) side = 'right';\n    }\n    return {\n      bottom: rect.top + rect.height,\n      height: rect.height,\n      left: rect[side],\n      right: rect[side],\n      top: rect.top,\n      width: 0\n    };\n  }\n  getNativeRange() {\n    const selection = document.getSelection();\n    if (selection == null || selection.rangeCount <= 0) return null;\n    const nativeRange = selection.getRangeAt(0);\n    if (nativeRange == null) return null;\n    const range = this.normalizeNative(nativeRange);\n    debug.info('getNativeRange', range);\n    return range;\n  }\n  getRange() {\n    const root = this.scroll.domNode;\n    if ('isConnected' in root && !root.isConnected) {\n      // document.getSelection() forces layout on Blink, so we trend to\n      // not calling it.\n      return [null, null];\n    }\n    const normalized = this.getNativeRange();\n    if (normalized == null) return [null, null];\n    const range = this.normalizedToRange(normalized);\n    return [range, normalized];\n  }\n  hasFocus() {\n    return document.activeElement === this.root || document.activeElement != null && contains(this.root, document.activeElement);\n  }\n  normalizedToRange(range) {\n    const positions = [[range.start.node, range.start.offset]];\n    if (!range.native.collapsed) {\n      positions.push([range.end.node, range.end.offset]);\n    }\n    const indexes = positions.map(position => {\n      const [node, offset] = position;\n      const blot = this.scroll.find(node, true);\n      // @ts-expect-error Fix me later\n      const index = blot.offset(this.scroll);\n      if (offset === 0) {\n        return index;\n      }\n      if (blot instanceof LeafBlot) {\n        return index + blot.index(node, offset);\n      }\n      // @ts-expect-error Fix me later\n      return index + blot.length();\n    });\n    const end = Math.min(Math.max(...indexes), this.scroll.length() - 1);\n    const start = Math.min(end, ...indexes);\n    return new Range(start, end - start);\n  }\n  normalizeNative(nativeRange) {\n    if (!contains(this.root, nativeRange.startContainer) || !nativeRange.collapsed && !contains(this.root, nativeRange.endContainer)) {\n      return null;\n    }\n    const range = {\n      start: {\n        node: nativeRange.startContainer,\n        offset: nativeRange.startOffset\n      },\n      end: {\n        node: nativeRange.endContainer,\n        offset: nativeRange.endOffset\n      },\n      native: nativeRange\n    };\n    [range.start, range.end].forEach(position => {\n      let {\n        node,\n        offset\n      } = position;\n      while (!(node instanceof Text) && node.childNodes.length > 0) {\n        if (node.childNodes.length > offset) {\n          node = node.childNodes[offset];\n          offset = 0;\n        } else if (node.childNodes.length === offset) {\n          // @ts-expect-error Fix me later\n          node = node.lastChild;\n          if (node instanceof Text) {\n            offset = node.data.length;\n          } else if (node.childNodes.length > 0) {\n            // Container case\n            offset = node.childNodes.length;\n          } else {\n            // Embed case\n            offset = node.childNodes.length + 1;\n          }\n        } else {\n          break;\n        }\n      }\n      position.node = node;\n      position.offset = offset;\n    });\n    return range;\n  }\n  rangeToNative(range) {\n    const scrollLength = this.scroll.length();\n    const getPosition = (index, inclusive) => {\n      index = Math.min(scrollLength - 1, index);\n      const [leaf, leafOffset] = this.scroll.leaf(index);\n      return leaf ? leaf.position(leafOffset, inclusive) : [null, -1];\n    };\n    return [...getPosition(range.index, false), ...getPosition(range.index + range.length, true)];\n  }\n  setNativeRange(startNode, startOffset) {\n    let endNode = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : startNode;\n    let endOffset = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : startOffset;\n    let force = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : false;\n    debug.info('setNativeRange', startNode, startOffset, endNode, endOffset);\n    if (startNode != null && (this.root.parentNode == null || startNode.parentNode == null ||\n    // @ts-expect-error Fix me later\n    endNode.parentNode == null)) {\n      return;\n    }\n    const selection = document.getSelection();\n    if (selection == null) return;\n    if (startNode != null) {\n      if (!this.hasFocus()) this.root.focus({\n        preventScroll: true\n      });\n      const {\n        native\n      } = this.getNativeRange() || {};\n      if (native == null || force || startNode !== native.startContainer || startOffset !== native.startOffset || endNode !== native.endContainer || endOffset !== native.endOffset) {\n        if (startNode instanceof Element && startNode.tagName === 'BR') {\n          // @ts-expect-error Fix me later\n          startOffset = Array.from(startNode.parentNode.childNodes).indexOf(startNode);\n          startNode = startNode.parentNode;\n        }\n        if (endNode instanceof Element && endNode.tagName === 'BR') {\n          // @ts-expect-error Fix me later\n          endOffset = Array.from(endNode.parentNode.childNodes).indexOf(endNode);\n          endNode = endNode.parentNode;\n        }\n        const range = document.createRange();\n        // @ts-expect-error Fix me later\n        range.setStart(startNode, startOffset);\n        // @ts-expect-error Fix me later\n        range.setEnd(endNode, endOffset);\n        selection.removeAllRanges();\n        selection.addRange(range);\n      }\n    } else {\n      selection.removeAllRanges();\n      this.root.blur();\n    }\n  }\n  setRange(range) {\n    let force = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    let source = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : Emitter.sources.API;\n    if (typeof force === 'string') {\n      source = force;\n      force = false;\n    }\n    debug.info('setRange', range);\n    if (range != null) {\n      const args = this.rangeToNative(range);\n      this.setNativeRange(...args, force);\n    } else {\n      this.setNativeRange(null);\n    }\n    this.update(source);\n  }\n  update() {\n    let source = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : Emitter.sources.USER;\n    const oldRange = this.lastRange;\n    const [lastRange, nativeRange] = this.getRange();\n    this.lastRange = lastRange;\n    this.lastNative = nativeRange;\n    if (this.lastRange != null) {\n      this.savedRange = this.lastRange;\n    }\n    if (!isEqual(oldRange, this.lastRange)) {\n      if (!this.composing && nativeRange != null && nativeRange.native.collapsed && nativeRange.start.node !== this.cursor.textNode) {\n        const range = this.cursor.restore();\n        if (range) {\n          this.setNativeRange(range.startNode, range.startOffset, range.endNode, range.endOffset);\n        }\n      }\n      const args = [Emitter.events.SELECTION_CHANGE, cloneDeep(this.lastRange), cloneDeep(oldRange), source];\n      this.emitter.emit(Emitter.events.EDITOR_CHANGE, ...args);\n      if (source !== Emitter.sources.SILENT) {\n        this.emitter.emit(...args);\n      }\n    }\n  }\n}\nfunction contains(parent, descendant) {\n  try {\n    // Firefox inserts inaccessible nodes around video elements\n    descendant.parentNode; // eslint-disable-line @typescript-eslint/no-unused-expressions\n  } catch (e) {\n    return false;\n  }\n  return parent.contains(descendant);\n}\nexport default Selection;\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,KAAK,QAAQ,WAAW;AAC3C,SAASC,SAAS,EAAEC,OAAO,QAAQ,WAAW;AAC9C,OAAOC,OAAO,MAAM,cAAc;AAClC,OAAOC,MAAM,MAAM,aAAa;AAChC,MAAMC,KAAK,GAAGD,MAAM,CAAC,iBAAiB,CAAC;AACvC,OAAO,MAAME,KAAK,CAAC;EACjBC,WAAWA,CAACC,KAAK,EAAE;IACjB,IAAIC,MAAM,GAAGC,SAAS,CAACD,MAAM,GAAG,CAAC,IAAIC,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IAClF,IAAI,CAACF,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,MAAM,GAAGA,MAAM;EACtB;AACF;AACA,MAAMG,SAAS,CAAC;EACdL,WAAWA,CAACM,MAAM,EAAEC,OAAO,EAAE;IAC3B,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACE,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,IAAI,GAAG,IAAI,CAACJ,MAAM,CAACK,OAAO;IAC/B;IACA,IAAI,CAACC,MAAM,GAAG,IAAI,CAACN,MAAM,CAACO,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC;IAChD;IACA,IAAI,CAACC,UAAU,GAAG,IAAIf,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IACjC,IAAI,CAACgB,SAAS,GAAG,IAAI,CAACD,UAAU;IAChC,IAAI,CAACE,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAACC,cAAc,CAAC,CAAC;IACrB,IAAI,CAACX,OAAO,CAACY,SAAS,CAAC,iBAAiB,EAAEC,QAAQ,EAAE,MAAM;MACxD,IAAI,CAAC,IAAI,CAACX,SAAS,IAAI,CAAC,IAAI,CAACD,SAAS,EAAE;QACtCa,UAAU,CAAC,IAAI,CAACC,MAAM,CAACC,IAAI,CAAC,IAAI,EAAE3B,OAAO,CAAC4B,OAAO,CAACC,IAAI,CAAC,EAAE,CAAC,CAAC;MAC7D;IACF,CAAC,CAAC;IACF,IAAI,CAAClB,OAAO,CAACmB,EAAE,CAAC9B,OAAO,CAAC+B,MAAM,CAACC,oBAAoB,EAAE,MAAM;MACzD,IAAI,CAAC,IAAI,CAACC,QAAQ,CAAC,CAAC,EAAE;MACtB,MAAMC,MAAM,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACpC,IAAID,MAAM,IAAI,IAAI,EAAE;MACpB,IAAIA,MAAM,CAACE,KAAK,CAACC,IAAI,KAAK,IAAI,CAACrB,MAAM,CAACsB,QAAQ,EAAE,OAAO,CAAC;MACxD,IAAI,CAAC3B,OAAO,CAAC4B,IAAI,CAACvC,OAAO,CAAC+B,MAAM,CAACS,aAAa,EAAE,CAACC,MAAM,EAAEC,SAAS,KAAK;QACrE,IAAI;UACF,IAAI,IAAI,CAAC5B,IAAI,CAAC6B,QAAQ,CAACT,MAAM,CAACE,KAAK,CAACC,IAAI,CAAC,IAAI,IAAI,CAACvB,IAAI,CAAC6B,QAAQ,CAACT,MAAM,CAACU,GAAG,CAACP,IAAI,CAAC,EAAE;YAChF,IAAI,CAACQ,cAAc,CAACX,MAAM,CAACE,KAAK,CAACC,IAAI,EAAEH,MAAM,CAACE,KAAK,CAACU,MAAM,EAAEZ,MAAM,CAACU,GAAG,CAACP,IAAI,EAAEH,MAAM,CAACU,GAAG,CAACE,MAAM,CAAC;UACjG;UACA,MAAMC,iBAAiB,GAAGL,SAAS,CAACM,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAACC,IAAI,KAAK,eAAe,IAAID,QAAQ,CAACC,IAAI,KAAK,WAAW,IAAID,QAAQ,CAACC,IAAI,KAAK,YAAY,IAAID,QAAQ,CAACE,MAAM,KAAK,IAAI,CAACrC,IAAI,CAAC;UAC3L,IAAI,CAACY,MAAM,CAACqB,iBAAiB,GAAG/C,OAAO,CAAC4B,OAAO,CAACwB,MAAM,GAAGX,MAAM,CAAC;QAClE,CAAC,CAAC,OAAOY,OAAO,EAAE;UAChB;QAAA;MAEJ,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAI,CAAC1C,OAAO,CAACmB,EAAE,CAAC9B,OAAO,CAAC+B,MAAM,CAACuB,eAAe,EAAE,CAACZ,SAAS,EAAEa,OAAO,KAAK;MACtE,IAAIA,OAAO,CAACC,KAAK,EAAE;QACjB,MAAM;UACJC,SAAS;UACTC,WAAW;UACXC,OAAO;UACPC;QACF,CAAC,GAAGL,OAAO,CAACC,KAAK;QACjB,IAAI,CAACX,cAAc,CAACY,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAEC,SAAS,CAAC;QAC/D,IAAI,CAAClC,MAAM,CAAC1B,OAAO,CAAC4B,OAAO,CAACwB,MAAM,CAAC;MACrC;IACF,CAAC,CAAC;IACF,IAAI,CAAC1B,MAAM,CAAC1B,OAAO,CAAC4B,OAAO,CAACwB,MAAM,CAAC;EACrC;EACA/B,iBAAiBA,CAAA,EAAG;IAClB,IAAI,CAACV,OAAO,CAACmB,EAAE,CAAC9B,OAAO,CAAC+B,MAAM,CAAC8B,wBAAwB,EAAE,MAAM;MAC7D,IAAI,CAACjD,SAAS,GAAG,IAAI;IACvB,CAAC,CAAC;IACF,IAAI,CAACD,OAAO,CAACmB,EAAE,CAAC9B,OAAO,CAAC+B,MAAM,CAAC+B,eAAe,EAAE,MAAM;MACpD,IAAI,CAAClD,SAAS,GAAG,KAAK;MACtB,IAAI,IAAI,CAACI,MAAM,CAAC+C,MAAM,EAAE;QACtB,MAAMP,KAAK,GAAG,IAAI,CAACxC,MAAM,CAACgD,OAAO,CAAC,CAAC;QACnC,IAAI,CAACR,KAAK,EAAE;QACZ/B,UAAU,CAAC,MAAM;UACf,IAAI,CAACoB,cAAc,CAACW,KAAK,CAACC,SAAS,EAAED,KAAK,CAACE,WAAW,EAAEF,KAAK,CAACG,OAAO,EAAEH,KAAK,CAACI,SAAS,CAAC;QACzF,CAAC,EAAE,CAAC,CAAC;MACP;IACF,CAAC,CAAC;EACJ;EACAtC,cAAcA,CAAA,EAAG;IACf,IAAI,CAACX,OAAO,CAACY,SAAS,CAAC,WAAW,EAAEC,QAAQ,CAACyC,IAAI,EAAE,MAAM;MACvD,IAAI,CAACpD,SAAS,GAAG,IAAI;IACvB,CAAC,CAAC;IACF,IAAI,CAACF,OAAO,CAACY,SAAS,CAAC,SAAS,EAAEC,QAAQ,CAACyC,IAAI,EAAE,MAAM;MACrD,IAAI,CAACpD,SAAS,GAAG,KAAK;MACtB,IAAI,CAACa,MAAM,CAAC1B,OAAO,CAAC4B,OAAO,CAACC,IAAI,CAAC;IACnC,CAAC,CAAC;EACJ;EACAqC,KAAKA,CAAA,EAAG;IACN,IAAI,IAAI,CAACjC,QAAQ,CAAC,CAAC,EAAE;IACrB,IAAI,CAACnB,IAAI,CAACoD,KAAK,CAAC;MACdC,aAAa,EAAE;IACjB,CAAC,CAAC;IACF,IAAI,CAACC,QAAQ,CAAC,IAAI,CAAClD,UAAU,CAAC;EAChC;EACAmD,MAAMA,CAACA,MAAM,EAAEC,KAAK,EAAE;IACpB,IAAI,CAAC5D,MAAM,CAACgB,MAAM,CAAC,CAAC;IACpB,MAAM6C,WAAW,GAAG,IAAI,CAACpC,cAAc,CAAC,CAAC;IACzC,IAAIoC,WAAW,IAAI,IAAI,IAAI,CAACA,WAAW,CAACrC,MAAM,CAACsC,SAAS,IAAI,IAAI,CAAC9D,MAAM,CAAC+D,KAAK,CAACJ,MAAM,EAAExE,KAAK,CAAC6E,KAAK,CAAC,EAAE;IACpG,IAAIH,WAAW,CAACnC,KAAK,CAACC,IAAI,KAAK,IAAI,CAACrB,MAAM,CAACsB,QAAQ,EAAE;MACnD,MAAMqC,IAAI,GAAG,IAAI,CAACjE,MAAM,CAACkE,IAAI,CAACL,WAAW,CAACnC,KAAK,CAACC,IAAI,EAAE,KAAK,CAAC;MAC5D,IAAIsC,IAAI,IAAI,IAAI,EAAE;MAClB;MACA,IAAIA,IAAI,YAAY/E,QAAQ,EAAE;QAC5B,MAAMiF,KAAK,GAAGF,IAAI,CAACG,KAAK,CAACP,WAAW,CAACnC,KAAK,CAACU,MAAM,CAAC;QAClD6B,IAAI,CAACZ,MAAM,CAACgB,YAAY,CAAC,IAAI,CAAC/D,MAAM,EAAE6D,KAAK,CAAC;MAC9C,CAAC,MAAM;QACL;QACAF,IAAI,CAACI,YAAY,CAAC,IAAI,CAAC/D,MAAM,EAAEuD,WAAW,CAACnC,KAAK,CAACC,IAAI,CAAC,CAAC,CAAC;MAC1D;MACA,IAAI,CAACrB,MAAM,CAACgE,MAAM,CAAC,CAAC;IACtB;IACA,IAAI,CAAChE,MAAM,CAACqD,MAAM,CAACA,MAAM,EAAEC,KAAK,CAAC;IACjC,IAAI,CAAC5D,MAAM,CAACuE,QAAQ,CAAC,CAAC;IACtB,IAAI,CAACpC,cAAc,CAAC,IAAI,CAAC7B,MAAM,CAACsB,QAAQ,EAAE,IAAI,CAACtB,MAAM,CAACsB,QAAQ,CAAC4C,IAAI,CAAC5E,MAAM,CAAC;IAC3E,IAAI,CAACoB,MAAM,CAAC,CAAC;EACf;EACAyD,SAASA,CAAC9E,KAAK,EAAE;IACf,IAAIC,MAAM,GAAGC,SAAS,CAACD,MAAM,GAAG,CAAC,IAAIC,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IAClF,MAAM6E,YAAY,GAAG,IAAI,CAAC1E,MAAM,CAACJ,MAAM,CAAC,CAAC;IACzCD,KAAK,GAAGgF,IAAI,CAACC,GAAG,CAACjF,KAAK,EAAE+E,YAAY,GAAG,CAAC,CAAC;IACzC9E,MAAM,GAAG+E,IAAI,CAACC,GAAG,CAACjF,KAAK,GAAGC,MAAM,EAAE8E,YAAY,GAAG,CAAC,CAAC,GAAG/E,KAAK;IAC3D,IAAIgC,IAAI;IACR,IAAI,CAACkD,IAAI,EAAEzC,MAAM,CAAC,GAAG,IAAI,CAACpC,MAAM,CAAC6E,IAAI,CAAClF,KAAK,CAAC;IAC5C,IAAIkF,IAAI,IAAI,IAAI,EAAE,OAAO,IAAI;IAC7B,IAAIjF,MAAM,GAAG,CAAC,IAAIwC,MAAM,KAAKyC,IAAI,CAACjF,MAAM,CAAC,CAAC,EAAE;MAC1C,MAAM,CAACkF,IAAI,CAAC,GAAG,IAAI,CAAC9E,MAAM,CAAC6E,IAAI,CAAClF,KAAK,GAAG,CAAC,CAAC;MAC1C,IAAImF,IAAI,EAAE;QACR,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAAC/E,MAAM,CAAC+E,IAAI,CAACpF,KAAK,CAAC;QACtC,MAAM,CAACqF,QAAQ,CAAC,GAAG,IAAI,CAAChF,MAAM,CAAC+E,IAAI,CAACpF,KAAK,GAAG,CAAC,CAAC;QAC9C,IAAIoF,IAAI,KAAKC,QAAQ,EAAE;UACrBH,IAAI,GAAGC,IAAI;UACX1C,MAAM,GAAG,CAAC;QACZ;MACF;IACF;IACA,CAACT,IAAI,EAAES,MAAM,CAAC,GAAGyC,IAAI,CAACI,QAAQ,CAAC7C,MAAM,EAAE,IAAI,CAAC;IAC5C,MAAMU,KAAK,GAAGhC,QAAQ,CAACoE,WAAW,CAAC,CAAC;IACpC,IAAItF,MAAM,GAAG,CAAC,EAAE;MACdkD,KAAK,CAACqC,QAAQ,CAACxD,IAAI,EAAES,MAAM,CAAC;MAC5B,CAACyC,IAAI,EAAEzC,MAAM,CAAC,GAAG,IAAI,CAACpC,MAAM,CAAC6E,IAAI,CAAClF,KAAK,GAAGC,MAAM,CAAC;MACjD,IAAIiF,IAAI,IAAI,IAAI,EAAE,OAAO,IAAI;MAC7B,CAAClD,IAAI,EAAES,MAAM,CAAC,GAAGyC,IAAI,CAACI,QAAQ,CAAC7C,MAAM,EAAE,IAAI,CAAC;MAC5CU,KAAK,CAACsC,MAAM,CAACzD,IAAI,EAAES,MAAM,CAAC;MAC1B,OAAOU,KAAK,CAACuC,qBAAqB,CAAC,CAAC;IACtC;IACA,IAAIC,IAAI,GAAG,MAAM;IACjB,IAAIC,IAAI;IACR,IAAI5D,IAAI,YAAY6D,IAAI,EAAE;MACxB;MACA;MACA;MACA;MACA;MACA,IAAI,CAAC7D,IAAI,CAAC6C,IAAI,CAAC5E,MAAM,EAAE;QACrB,OAAO,IAAI;MACb;MACA,IAAIwC,MAAM,GAAGT,IAAI,CAAC6C,IAAI,CAAC5E,MAAM,EAAE;QAC7BkD,KAAK,CAACqC,QAAQ,CAACxD,IAAI,EAAES,MAAM,CAAC;QAC5BU,KAAK,CAACsC,MAAM,CAACzD,IAAI,EAAES,MAAM,GAAG,CAAC,CAAC;MAChC,CAAC,MAAM;QACLU,KAAK,CAACqC,QAAQ,CAACxD,IAAI,EAAES,MAAM,GAAG,CAAC,CAAC;QAChCU,KAAK,CAACsC,MAAM,CAACzD,IAAI,EAAES,MAAM,CAAC;QAC1BkD,IAAI,GAAG,OAAO;MAChB;MACAC,IAAI,GAAGzC,KAAK,CAACuC,qBAAqB,CAAC,CAAC;IACtC,CAAC,MAAM;MACL,IAAI,EAAER,IAAI,CAACxE,OAAO,YAAYoF,OAAO,CAAC,EAAE,OAAO,IAAI;MACnDF,IAAI,GAAGV,IAAI,CAACxE,OAAO,CAACgF,qBAAqB,CAAC,CAAC;MAC3C,IAAIjD,MAAM,GAAG,CAAC,EAAEkD,IAAI,GAAG,OAAO;IAChC;IACA,OAAO;MACLI,MAAM,EAAEH,IAAI,CAACI,GAAG,GAAGJ,IAAI,CAACK,MAAM;MAC9BA,MAAM,EAAEL,IAAI,CAACK,MAAM;MACnBC,IAAI,EAAEN,IAAI,CAACD,IAAI,CAAC;MAChBQ,KAAK,EAAEP,IAAI,CAACD,IAAI,CAAC;MACjBK,GAAG,EAAEJ,IAAI,CAACI,GAAG;MACbI,KAAK,EAAE;IACT,CAAC;EACH;EACAtE,cAAcA,CAAA,EAAG;IACf,MAAMuE,SAAS,GAAGlF,QAAQ,CAACmF,YAAY,CAAC,CAAC;IACzC,IAAID,SAAS,IAAI,IAAI,IAAIA,SAAS,CAACE,UAAU,IAAI,CAAC,EAAE,OAAO,IAAI;IAC/D,MAAMrC,WAAW,GAAGmC,SAAS,CAACG,UAAU,CAAC,CAAC,CAAC;IAC3C,IAAItC,WAAW,IAAI,IAAI,EAAE,OAAO,IAAI;IACpC,MAAMf,KAAK,GAAG,IAAI,CAACsD,eAAe,CAACvC,WAAW,CAAC;IAC/CrE,KAAK,CAAC6G,IAAI,CAAC,gBAAgB,EAAEvD,KAAK,CAAC;IACnC,OAAOA,KAAK;EACd;EACAwD,QAAQA,CAAA,EAAG;IACT,MAAMlG,IAAI,GAAG,IAAI,CAACJ,MAAM,CAACK,OAAO;IAChC,IAAI,aAAa,IAAID,IAAI,IAAI,CAACA,IAAI,CAACmG,WAAW,EAAE;MAC9C;MACA;MACA,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC;IACrB;IACA,MAAMC,UAAU,GAAG,IAAI,CAAC/E,cAAc,CAAC,CAAC;IACxC,IAAI+E,UAAU,IAAI,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC;IAC3C,MAAM1D,KAAK,GAAG,IAAI,CAAC2D,iBAAiB,CAACD,UAAU,CAAC;IAChD,OAAO,CAAC1D,KAAK,EAAE0D,UAAU,CAAC;EAC5B;EACAjF,QAAQA,CAAA,EAAG;IACT,OAAOT,QAAQ,CAAC4F,aAAa,KAAK,IAAI,CAACtG,IAAI,IAAIU,QAAQ,CAAC4F,aAAa,IAAI,IAAI,IAAIzE,QAAQ,CAAC,IAAI,CAAC7B,IAAI,EAAEU,QAAQ,CAAC4F,aAAa,CAAC;EAC9H;EACAD,iBAAiBA,CAAC3D,KAAK,EAAE;IACvB,MAAM6D,SAAS,GAAG,CAAC,CAAC7D,KAAK,CAACpB,KAAK,CAACC,IAAI,EAAEmB,KAAK,CAACpB,KAAK,CAACU,MAAM,CAAC,CAAC;IAC1D,IAAI,CAACU,KAAK,CAACtB,MAAM,CAACsC,SAAS,EAAE;MAC3B6C,SAAS,CAACC,IAAI,CAAC,CAAC9D,KAAK,CAACZ,GAAG,CAACP,IAAI,EAAEmB,KAAK,CAACZ,GAAG,CAACE,MAAM,CAAC,CAAC;IACpD;IACA,MAAMyE,OAAO,GAAGF,SAAS,CAACG,GAAG,CAAC7B,QAAQ,IAAI;MACxC,MAAM,CAACtD,IAAI,EAAES,MAAM,CAAC,GAAG6C,QAAQ;MAC/B,MAAMhB,IAAI,GAAG,IAAI,CAACjE,MAAM,CAACkE,IAAI,CAACvC,IAAI,EAAE,IAAI,CAAC;MACzC;MACA,MAAMhC,KAAK,GAAGsE,IAAI,CAAC7B,MAAM,CAAC,IAAI,CAACpC,MAAM,CAAC;MACtC,IAAIoC,MAAM,KAAK,CAAC,EAAE;QAChB,OAAOzC,KAAK;MACd;MACA,IAAIsE,IAAI,YAAY/E,QAAQ,EAAE;QAC5B,OAAOS,KAAK,GAAGsE,IAAI,CAACtE,KAAK,CAACgC,IAAI,EAAES,MAAM,CAAC;MACzC;MACA;MACA,OAAOzC,KAAK,GAAGsE,IAAI,CAACrE,MAAM,CAAC,CAAC;IAC9B,CAAC,CAAC;IACF,MAAMsC,GAAG,GAAGyC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACoC,GAAG,CAAC,GAAGF,OAAO,CAAC,EAAE,IAAI,CAAC7G,MAAM,CAACJ,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;IACpE,MAAM8B,KAAK,GAAGiD,IAAI,CAACC,GAAG,CAAC1C,GAAG,EAAE,GAAG2E,OAAO,CAAC;IACvC,OAAO,IAAIpH,KAAK,CAACiC,KAAK,EAAEQ,GAAG,GAAGR,KAAK,CAAC;EACtC;EACA0E,eAAeA,CAACvC,WAAW,EAAE;IAC3B,IAAI,CAAC5B,QAAQ,CAAC,IAAI,CAAC7B,IAAI,EAAEyD,WAAW,CAACmD,cAAc,CAAC,IAAI,CAACnD,WAAW,CAACC,SAAS,IAAI,CAAC7B,QAAQ,CAAC,IAAI,CAAC7B,IAAI,EAAEyD,WAAW,CAACoD,YAAY,CAAC,EAAE;MAChI,OAAO,IAAI;IACb;IACA,MAAMnE,KAAK,GAAG;MACZpB,KAAK,EAAE;QACLC,IAAI,EAAEkC,WAAW,CAACmD,cAAc;QAChC5E,MAAM,EAAEyB,WAAW,CAACb;MACtB,CAAC;MACDd,GAAG,EAAE;QACHP,IAAI,EAAEkC,WAAW,CAACoD,YAAY;QAC9B7E,MAAM,EAAEyB,WAAW,CAACX;MACtB,CAAC;MACD1B,MAAM,EAAEqC;IACV,CAAC;IACD,CAACf,KAAK,CAACpB,KAAK,EAAEoB,KAAK,CAACZ,GAAG,CAAC,CAACgF,OAAO,CAACjC,QAAQ,IAAI;MAC3C,IAAI;QACFtD,IAAI;QACJS;MACF,CAAC,GAAG6C,QAAQ;MACZ,OAAO,EAAEtD,IAAI,YAAY6D,IAAI,CAAC,IAAI7D,IAAI,CAACwF,UAAU,CAACvH,MAAM,GAAG,CAAC,EAAE;QAC5D,IAAI+B,IAAI,CAACwF,UAAU,CAACvH,MAAM,GAAGwC,MAAM,EAAE;UACnCT,IAAI,GAAGA,IAAI,CAACwF,UAAU,CAAC/E,MAAM,CAAC;UAC9BA,MAAM,GAAG,CAAC;QACZ,CAAC,MAAM,IAAIT,IAAI,CAACwF,UAAU,CAACvH,MAAM,KAAKwC,MAAM,EAAE;UAC5C;UACAT,IAAI,GAAGA,IAAI,CAACyF,SAAS;UACrB,IAAIzF,IAAI,YAAY6D,IAAI,EAAE;YACxBpD,MAAM,GAAGT,IAAI,CAAC6C,IAAI,CAAC5E,MAAM;UAC3B,CAAC,MAAM,IAAI+B,IAAI,CAACwF,UAAU,CAACvH,MAAM,GAAG,CAAC,EAAE;YACrC;YACAwC,MAAM,GAAGT,IAAI,CAACwF,UAAU,CAACvH,MAAM;UACjC,CAAC,MAAM;YACL;YACAwC,MAAM,GAAGT,IAAI,CAACwF,UAAU,CAACvH,MAAM,GAAG,CAAC;UACrC;QACF,CAAC,MAAM;UACL;QACF;MACF;MACAqF,QAAQ,CAACtD,IAAI,GAAGA,IAAI;MACpBsD,QAAQ,CAAC7C,MAAM,GAAGA,MAAM;IAC1B,CAAC,CAAC;IACF,OAAOU,KAAK;EACd;EACAuE,aAAaA,CAACvE,KAAK,EAAE;IACnB,MAAM4B,YAAY,GAAG,IAAI,CAAC1E,MAAM,CAACJ,MAAM,CAAC,CAAC;IACzC,MAAM0H,WAAW,GAAGA,CAAC3H,KAAK,EAAE4H,SAAS,KAAK;MACxC5H,KAAK,GAAGgF,IAAI,CAACC,GAAG,CAACF,YAAY,GAAG,CAAC,EAAE/E,KAAK,CAAC;MACzC,MAAM,CAACkF,IAAI,EAAE2C,UAAU,CAAC,GAAG,IAAI,CAACxH,MAAM,CAAC6E,IAAI,CAAClF,KAAK,CAAC;MAClD,OAAOkF,IAAI,GAAGA,IAAI,CAACI,QAAQ,CAACuC,UAAU,EAAED,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;IACjE,CAAC;IACD,OAAO,CAAC,GAAGD,WAAW,CAACxE,KAAK,CAACnD,KAAK,EAAE,KAAK,CAAC,EAAE,GAAG2H,WAAW,CAACxE,KAAK,CAACnD,KAAK,GAAGmD,KAAK,CAAClD,MAAM,EAAE,IAAI,CAAC,CAAC;EAC/F;EACAuC,cAAcA,CAACY,SAAS,EAAEC,WAAW,EAAE;IACrC,IAAIC,OAAO,GAAGpD,SAAS,CAACD,MAAM,GAAG,CAAC,IAAIC,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAGkD,SAAS;IAC3F,IAAIG,SAAS,GAAGrD,SAAS,CAACD,MAAM,GAAG,CAAC,IAAIC,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAGmD,WAAW;IAC/F,IAAIyE,KAAK,GAAG5H,SAAS,CAACD,MAAM,GAAG,CAAC,IAAIC,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;IACrFL,KAAK,CAAC6G,IAAI,CAAC,gBAAgB,EAAEtD,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAEC,SAAS,CAAC;IACxE,IAAIH,SAAS,IAAI,IAAI,KAAK,IAAI,CAAC3C,IAAI,CAACsH,UAAU,IAAI,IAAI,IAAI3E,SAAS,CAAC2E,UAAU,IAAI,IAAI;IACtF;IACAzE,OAAO,CAACyE,UAAU,IAAI,IAAI,CAAC,EAAE;MAC3B;IACF;IACA,MAAM1B,SAAS,GAAGlF,QAAQ,CAACmF,YAAY,CAAC,CAAC;IACzC,IAAID,SAAS,IAAI,IAAI,EAAE;IACvB,IAAIjD,SAAS,IAAI,IAAI,EAAE;MACrB,IAAI,CAAC,IAAI,CAACxB,QAAQ,CAAC,CAAC,EAAE,IAAI,CAACnB,IAAI,CAACoD,KAAK,CAAC;QACpCC,aAAa,EAAE;MACjB,CAAC,CAAC;MACF,MAAM;QACJjC;MACF,CAAC,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC;MAC/B,IAAID,MAAM,IAAI,IAAI,IAAIiG,KAAK,IAAI1E,SAAS,KAAKvB,MAAM,CAACwF,cAAc,IAAIhE,WAAW,KAAKxB,MAAM,CAACwB,WAAW,IAAIC,OAAO,KAAKzB,MAAM,CAACyF,YAAY,IAAI/D,SAAS,KAAK1B,MAAM,CAAC0B,SAAS,EAAE;QAC7K,IAAIH,SAAS,YAAY0C,OAAO,IAAI1C,SAAS,CAAC4E,OAAO,KAAK,IAAI,EAAE;UAC9D;UACA3E,WAAW,GAAG4E,KAAK,CAACC,IAAI,CAAC9E,SAAS,CAAC2E,UAAU,CAACP,UAAU,CAAC,CAACW,OAAO,CAAC/E,SAAS,CAAC;UAC5EA,SAAS,GAAGA,SAAS,CAAC2E,UAAU;QAClC;QACA,IAAIzE,OAAO,YAAYwC,OAAO,IAAIxC,OAAO,CAAC0E,OAAO,KAAK,IAAI,EAAE;UAC1D;UACAzE,SAAS,GAAG0E,KAAK,CAACC,IAAI,CAAC5E,OAAO,CAACyE,UAAU,CAACP,UAAU,CAAC,CAACW,OAAO,CAAC7E,OAAO,CAAC;UACtEA,OAAO,GAAGA,OAAO,CAACyE,UAAU;QAC9B;QACA,MAAM5E,KAAK,GAAGhC,QAAQ,CAACoE,WAAW,CAAC,CAAC;QACpC;QACApC,KAAK,CAACqC,QAAQ,CAACpC,SAAS,EAAEC,WAAW,CAAC;QACtC;QACAF,KAAK,CAACsC,MAAM,CAACnC,OAAO,EAAEC,SAAS,CAAC;QAChC8C,SAAS,CAAC+B,eAAe,CAAC,CAAC;QAC3B/B,SAAS,CAACgC,QAAQ,CAAClF,KAAK,CAAC;MAC3B;IACF,CAAC,MAAM;MACLkD,SAAS,CAAC+B,eAAe,CAAC,CAAC;MAC3B,IAAI,CAAC3H,IAAI,CAAC6H,IAAI,CAAC,CAAC;IAClB;EACF;EACAvE,QAAQA,CAACZ,KAAK,EAAE;IACd,IAAI2E,KAAK,GAAG5H,SAAS,CAACD,MAAM,GAAG,CAAC,IAAIC,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;IACrF,IAAIkC,MAAM,GAAGlC,SAAS,CAACD,MAAM,GAAG,CAAC,IAAIC,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAGP,OAAO,CAAC4B,OAAO,CAACgH,GAAG;IACpG,IAAI,OAAOT,KAAK,KAAK,QAAQ,EAAE;MAC7B1F,MAAM,GAAG0F,KAAK;MACdA,KAAK,GAAG,KAAK;IACf;IACAjI,KAAK,CAAC6G,IAAI,CAAC,UAAU,EAAEvD,KAAK,CAAC;IAC7B,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,MAAMqF,IAAI,GAAG,IAAI,CAACd,aAAa,CAACvE,KAAK,CAAC;MACtC,IAAI,CAACX,cAAc,CAAC,GAAGgG,IAAI,EAAEV,KAAK,CAAC;IACrC,CAAC,MAAM;MACL,IAAI,CAACtF,cAAc,CAAC,IAAI,CAAC;IAC3B;IACA,IAAI,CAACnB,MAAM,CAACe,MAAM,CAAC;EACrB;EACAf,MAAMA,CAAA,EAAG;IACP,IAAIe,MAAM,GAAGlC,SAAS,CAACD,MAAM,GAAG,CAAC,IAAIC,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAGP,OAAO,CAAC4B,OAAO,CAACC,IAAI;IACrG,MAAMiH,QAAQ,GAAG,IAAI,CAAC3H,SAAS;IAC/B,MAAM,CAACA,SAAS,EAAEoD,WAAW,CAAC,GAAG,IAAI,CAACyC,QAAQ,CAAC,CAAC;IAChD,IAAI,CAAC7F,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,UAAU,GAAGmD,WAAW;IAC7B,IAAI,IAAI,CAACpD,SAAS,IAAI,IAAI,EAAE;MAC1B,IAAI,CAACD,UAAU,GAAG,IAAI,CAACC,SAAS;IAClC;IACA,IAAI,CAACpB,OAAO,CAAC+I,QAAQ,EAAE,IAAI,CAAC3H,SAAS,CAAC,EAAE;MACtC,IAAI,CAAC,IAAI,CAACP,SAAS,IAAI2D,WAAW,IAAI,IAAI,IAAIA,WAAW,CAACrC,MAAM,CAACsC,SAAS,IAAID,WAAW,CAACnC,KAAK,CAACC,IAAI,KAAK,IAAI,CAACrB,MAAM,CAACsB,QAAQ,EAAE;QAC7H,MAAMkB,KAAK,GAAG,IAAI,CAACxC,MAAM,CAACgD,OAAO,CAAC,CAAC;QACnC,IAAIR,KAAK,EAAE;UACT,IAAI,CAACX,cAAc,CAACW,KAAK,CAACC,SAAS,EAAED,KAAK,CAACE,WAAW,EAAEF,KAAK,CAACG,OAAO,EAAEH,KAAK,CAACI,SAAS,CAAC;QACzF;MACF;MACA,MAAMiF,IAAI,GAAG,CAAC7I,OAAO,CAAC+B,MAAM,CAACgH,gBAAgB,EAAEjJ,SAAS,CAAC,IAAI,CAACqB,SAAS,CAAC,EAAErB,SAAS,CAACgJ,QAAQ,CAAC,EAAErG,MAAM,CAAC;MACtG,IAAI,CAAC9B,OAAO,CAACqI,IAAI,CAAChJ,OAAO,CAAC+B,MAAM,CAACkH,aAAa,EAAE,GAAGJ,IAAI,CAAC;MACxD,IAAIpG,MAAM,KAAKzC,OAAO,CAAC4B,OAAO,CAACwB,MAAM,EAAE;QACrC,IAAI,CAACzC,OAAO,CAACqI,IAAI,CAAC,GAAGH,IAAI,CAAC;MAC5B;IACF;EACF;AACF;AACA,SAASlG,QAAQA,CAACoB,MAAM,EAAEmF,UAAU,EAAE;EACpC,IAAI;IACF;IACAA,UAAU,CAACd,UAAU,CAAC,CAAC;EACzB,CAAC,CAAC,OAAOe,CAAC,EAAE;IACV,OAAO,KAAK;EACd;EACA,OAAOpF,MAAM,CAACpB,QAAQ,CAACuG,UAAU,CAAC;AACpC;AACA,eAAezI,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}