{"ast": null, "code": "import { Platform } from '@angular/cdk/platform';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, makeEnvironmentProviders, PLATFORM_ID, CSP_NONCE, Directive, Optional, Inject, Input, NgModule } from '@angular/core';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzWaveRenderer {\n  get waveAttributeName() {\n    return this.insertExtraNode ? 'ant-click-animating' : 'ant-click-animating-without-extra-node';\n  }\n  constructor(triggerElement, ngZone, insertExtraNode, platformId, cspNonce) {\n    this.triggerElement = triggerElement;\n    this.ngZone = ngZone;\n    this.insertExtraNode = insertExtraNode;\n    this.platformId = platformId;\n    this.cspNonce = cspNonce;\n    this.waveTransitionDuration = 400;\n    this.styleForPseudo = null;\n    this.extraNode = null;\n    this.lastTime = 0;\n    this.onClick = event => {\n      if (!this.triggerElement || !this.triggerElement.getAttribute || this.triggerElement.getAttribute('disabled') || event.target.tagName === 'INPUT' || this.triggerElement.className.indexOf('disabled') >= 0) {\n        return;\n      }\n      this.fadeOutWave();\n    };\n    this.platform = new Platform(this.platformId);\n    this.clickHandler = this.onClick.bind(this);\n    this.bindTriggerEvent();\n  }\n  bindTriggerEvent() {\n    if (this.platform.isBrowser) {\n      this.ngZone.runOutsideAngular(() => {\n        this.removeTriggerEvent();\n        if (this.triggerElement) {\n          this.triggerElement.addEventListener('click', this.clickHandler, true);\n        }\n      });\n    }\n  }\n  removeTriggerEvent() {\n    if (this.triggerElement) {\n      this.triggerElement.removeEventListener('click', this.clickHandler, true);\n    }\n  }\n  removeStyleAndExtraNode() {\n    if (this.styleForPseudo && document.body.contains(this.styleForPseudo)) {\n      document.body.removeChild(this.styleForPseudo);\n      this.styleForPseudo = null;\n    }\n    if (this.insertExtraNode && this.triggerElement.contains(this.extraNode)) {\n      this.triggerElement.removeChild(this.extraNode);\n    }\n  }\n  destroy() {\n    this.removeTriggerEvent();\n    this.removeStyleAndExtraNode();\n  }\n  fadeOutWave() {\n    const node = this.triggerElement;\n    const waveColor = this.getWaveColor(node);\n    node.setAttribute(this.waveAttributeName, 'true');\n    if (Date.now() < this.lastTime + this.waveTransitionDuration) {\n      return;\n    }\n    if (this.isValidColor(waveColor)) {\n      if (!this.styleForPseudo) {\n        this.styleForPseudo = document.createElement('style');\n        if (this.cspNonce) {\n          this.styleForPseudo.nonce = this.cspNonce;\n        }\n      }\n      this.styleForPseudo.innerHTML = `\n      [ant-click-animating-without-extra-node='true']::after, .ant-click-animating-node {\n        --antd-wave-shadow-color: ${waveColor};\n      }`;\n      document.body.appendChild(this.styleForPseudo);\n    }\n    if (this.insertExtraNode) {\n      if (!this.extraNode) {\n        this.extraNode = document.createElement('div');\n      }\n      this.extraNode.className = 'ant-click-animating-node';\n      node.appendChild(this.extraNode);\n    }\n    this.lastTime = Date.now();\n    this.runTimeoutOutsideZone(() => {\n      node.removeAttribute(this.waveAttributeName);\n      this.removeStyleAndExtraNode();\n    }, this.waveTransitionDuration);\n  }\n  isValidColor(color) {\n    return !!color && color !== '#ffffff' && color !== 'rgb(255, 255, 255)' && this.isNotGrey(color) && !/rgba\\(\\d*, \\d*, \\d*, 0\\)/.test(color) && color !== 'transparent';\n  }\n  isNotGrey(color) {\n    const match = color.match(/rgba?\\((\\d*), (\\d*), (\\d*)(, [\\.\\d]*)?\\)/);\n    if (match && match[1] && match[2] && match[3]) {\n      return !(match[1] === match[2] && match[2] === match[3]);\n    }\n    return true;\n  }\n  getWaveColor(node) {\n    const nodeStyle = getComputedStyle(node);\n    return nodeStyle.getPropertyValue('border-top-color') ||\n    // Firefox Compatible\n    nodeStyle.getPropertyValue('border-color') || nodeStyle.getPropertyValue('background-color');\n  }\n  runTimeoutOutsideZone(fn, delay) {\n    this.ngZone.runOutsideAngular(() => setTimeout(fn, delay));\n  }\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst NZ_WAVE_GLOBAL_DEFAULT_CONFIG = {\n  disabled: false\n};\nconst NZ_WAVE_GLOBAL_CONFIG = new InjectionToken('nz-wave-global-options');\nfunction provideNzWave(config) {\n  return makeEnvironmentProviders([{\n    provide: NZ_WAVE_GLOBAL_CONFIG,\n    useValue: config\n  }]);\n}\nclass NzWaveDirective {\n  get disabled() {\n    return this.waveDisabled;\n  }\n  get rendererRef() {\n    return this.waveRenderer;\n  }\n  constructor(ngZone, elementRef, config, animationType, platformId, cspNonce) {\n    this.ngZone = ngZone;\n    this.elementRef = elementRef;\n    this.config = config;\n    this.animationType = animationType;\n    this.platformId = platformId;\n    this.cspNonce = cspNonce;\n    this.nzWaveExtraNode = false;\n    this.waveDisabled = false;\n    this.waveDisabled = this.isConfigDisabled();\n  }\n  isConfigDisabled() {\n    let disabled = false;\n    if (this.config && typeof this.config.disabled === 'boolean') {\n      disabled = this.config.disabled;\n    }\n    if (this.animationType === 'NoopAnimations') {\n      disabled = true;\n    }\n    return disabled;\n  }\n  ngOnDestroy() {\n    if (this.waveRenderer) {\n      this.waveRenderer.destroy();\n    }\n  }\n  ngOnInit() {\n    this.renderWaveIfEnabled();\n  }\n  renderWaveIfEnabled() {\n    if (!this.waveDisabled && this.elementRef.nativeElement) {\n      this.waveRenderer = new NzWaveRenderer(this.elementRef.nativeElement, this.ngZone, this.nzWaveExtraNode, this.platformId, this.cspNonce);\n    }\n  }\n  disable() {\n    this.waveDisabled = true;\n    if (this.waveRenderer) {\n      this.waveRenderer.removeTriggerEvent();\n      this.waveRenderer.removeStyleAndExtraNode();\n    }\n  }\n  enable() {\n    // config priority\n    this.waveDisabled = this.isConfigDisabled() || false;\n    if (this.waveRenderer) {\n      this.waveRenderer.bindTriggerEvent();\n    }\n  }\n  static {\n    this.ɵfac = function NzWaveDirective_Factory(t) {\n      return new (t || NzWaveDirective)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(NZ_WAVE_GLOBAL_CONFIG, 8), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(CSP_NONCE, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzWaveDirective,\n      selectors: [[\"\", \"nz-wave\", \"\"], [\"button\", \"nz-button\", \"\", 3, \"nzType\", \"link\", 3, \"nzType\", \"text\"]],\n      inputs: {\n        nzWaveExtraNode: \"nzWaveExtraNode\"\n      },\n      exportAs: [\"nzWave\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzWaveDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[nz-wave],button[nz-button]:not([nzType=\"link\"]):not([nzType=\"text\"])',\n      exportAs: 'nzWave',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [NZ_WAVE_GLOBAL_CONFIG]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [ANIMATION_MODULE_TYPE]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [CSP_NONCE]\n    }]\n  }], {\n    nzWaveExtraNode: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzWaveModule {\n  static {\n    this.ɵfac = function NzWaveModule_Factory(t) {\n      return new (t || NzWaveModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzWaveModule,\n      imports: [NzWaveDirective],\n      exports: [NzWaveDirective]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [provideNzWave(NZ_WAVE_GLOBAL_DEFAULT_CONFIG)]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzWaveModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzWaveDirective],\n      exports: [NzWaveDirective],\n      providers: [provideNzWave(NZ_WAVE_GLOBAL_DEFAULT_CONFIG)]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NZ_WAVE_GLOBAL_CONFIG, NZ_WAVE_GLOBAL_DEFAULT_CONFIG, NzWaveDirective, NzWaveModule, NzWaveRenderer, provideNzWave };", "map": {"version": 3, "names": ["Platform", "i0", "InjectionToken", "makeEnvironmentProviders", "PLATFORM_ID", "CSP_NONCE", "Directive", "Optional", "Inject", "Input", "NgModule", "ANIMATION_MODULE_TYPE", "NzWaveRenderer", "waveAttributeName", "insertExtraNode", "constructor", "triggerElement", "ngZone", "platformId", "cspNonce", "waveTransitionDuration", "style<PERSON>or<PERSON><PERSON><PERSON>", "extraNode", "lastTime", "onClick", "event", "getAttribute", "target", "tagName", "className", "indexOf", "fadeOutWave", "platform", "clickHandler", "bind", "bindTriggerEvent", "<PERSON><PERSON><PERSON><PERSON>", "runOutsideAngular", "removeTriggerEvent", "addEventListener", "removeEventListener", "removeStyleAndExtraNode", "document", "body", "contains", "<PERSON><PERSON><PERSON><PERSON>", "destroy", "node", "waveColor", "getWaveColor", "setAttribute", "Date", "now", "isValidColor", "createElement", "nonce", "innerHTML", "append<PERSON><PERSON><PERSON>", "runTimeoutOutsideZone", "removeAttribute", "color", "isNotGrey", "test", "match", "nodeStyle", "getComputedStyle", "getPropertyValue", "fn", "delay", "setTimeout", "NZ_WAVE_GLOBAL_DEFAULT_CONFIG", "disabled", "NZ_WAVE_GLOBAL_CONFIG", "provideNzWave", "config", "provide", "useValue", "NzWaveDirective", "waveDisabled", "rendererRef", "<PERSON><PERSON><PERSON><PERSON>", "elementRef", "animationType", "nzWaveExtraNode", "isConfigDisabled", "ngOnDestroy", "ngOnInit", "renderWaveIfEnabled", "nativeElement", "disable", "enable", "ɵfac", "NzWaveDirective_Factory", "t", "ɵɵdirectiveInject", "NgZone", "ElementRef", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "inputs", "exportAs", "standalone", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "undefined", "decorators", "NzWaveModule", "NzWaveModule_Factory", "ɵmod", "ɵɵdefineNgModule", "imports", "exports", "ɵinj", "ɵɵdefineInjector", "providers"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-core-wave.mjs"], "sourcesContent": ["import { Platform } from '@angular/cdk/platform';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, makeEnvironmentProviders, PLATFORM_ID, CSP_NONCE, Directive, Optional, Inject, Input, NgModule } from '@angular/core';\nimport { ANIMATION_MODULE_TYPE } from '@angular/platform-browser/animations';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzWaveRenderer {\n    get waveAttributeName() {\n        return this.insertExtraNode ? 'ant-click-animating' : 'ant-click-animating-without-extra-node';\n    }\n    constructor(triggerElement, ngZone, insertExtraNode, platformId, cspNonce) {\n        this.triggerElement = triggerElement;\n        this.ngZone = ngZone;\n        this.insertExtraNode = insertExtraNode;\n        this.platformId = platformId;\n        this.cspNonce = cspNonce;\n        this.waveTransitionDuration = 400;\n        this.styleForPseudo = null;\n        this.extraNode = null;\n        this.lastTime = 0;\n        this.onClick = (event) => {\n            if (!this.triggerElement ||\n                !this.triggerElement.getAttribute ||\n                this.triggerElement.getAttribute('disabled') ||\n                event.target.tagName === 'INPUT' ||\n                this.triggerElement.className.indexOf('disabled') >= 0) {\n                return;\n            }\n            this.fadeOutWave();\n        };\n        this.platform = new Platform(this.platformId);\n        this.clickHandler = this.onClick.bind(this);\n        this.bindTriggerEvent();\n    }\n    bindTriggerEvent() {\n        if (this.platform.isBrowser) {\n            this.ngZone.runOutsideAngular(() => {\n                this.removeTriggerEvent();\n                if (this.triggerElement) {\n                    this.triggerElement.addEventListener('click', this.clickHandler, true);\n                }\n            });\n        }\n    }\n    removeTriggerEvent() {\n        if (this.triggerElement) {\n            this.triggerElement.removeEventListener('click', this.clickHandler, true);\n        }\n    }\n    removeStyleAndExtraNode() {\n        if (this.styleForPseudo && document.body.contains(this.styleForPseudo)) {\n            document.body.removeChild(this.styleForPseudo);\n            this.styleForPseudo = null;\n        }\n        if (this.insertExtraNode && this.triggerElement.contains(this.extraNode)) {\n            this.triggerElement.removeChild(this.extraNode);\n        }\n    }\n    destroy() {\n        this.removeTriggerEvent();\n        this.removeStyleAndExtraNode();\n    }\n    fadeOutWave() {\n        const node = this.triggerElement;\n        const waveColor = this.getWaveColor(node);\n        node.setAttribute(this.waveAttributeName, 'true');\n        if (Date.now() < this.lastTime + this.waveTransitionDuration) {\n            return;\n        }\n        if (this.isValidColor(waveColor)) {\n            if (!this.styleForPseudo) {\n                this.styleForPseudo = document.createElement('style');\n                if (this.cspNonce) {\n                    this.styleForPseudo.nonce = this.cspNonce;\n                }\n            }\n            this.styleForPseudo.innerHTML = `\n      [ant-click-animating-without-extra-node='true']::after, .ant-click-animating-node {\n        --antd-wave-shadow-color: ${waveColor};\n      }`;\n            document.body.appendChild(this.styleForPseudo);\n        }\n        if (this.insertExtraNode) {\n            if (!this.extraNode) {\n                this.extraNode = document.createElement('div');\n            }\n            this.extraNode.className = 'ant-click-animating-node';\n            node.appendChild(this.extraNode);\n        }\n        this.lastTime = Date.now();\n        this.runTimeoutOutsideZone(() => {\n            node.removeAttribute(this.waveAttributeName);\n            this.removeStyleAndExtraNode();\n        }, this.waveTransitionDuration);\n    }\n    isValidColor(color) {\n        return (!!color &&\n            color !== '#ffffff' &&\n            color !== 'rgb(255, 255, 255)' &&\n            this.isNotGrey(color) &&\n            !/rgba\\(\\d*, \\d*, \\d*, 0\\)/.test(color) &&\n            color !== 'transparent');\n    }\n    isNotGrey(color) {\n        const match = color.match(/rgba?\\((\\d*), (\\d*), (\\d*)(, [\\.\\d]*)?\\)/);\n        if (match && match[1] && match[2] && match[3]) {\n            return !(match[1] === match[2] && match[2] === match[3]);\n        }\n        return true;\n    }\n    getWaveColor(node) {\n        const nodeStyle = getComputedStyle(node);\n        return (nodeStyle.getPropertyValue('border-top-color') || // Firefox Compatible\n            nodeStyle.getPropertyValue('border-color') ||\n            nodeStyle.getPropertyValue('background-color'));\n    }\n    runTimeoutOutsideZone(fn, delay) {\n        this.ngZone.runOutsideAngular(() => setTimeout(fn, delay));\n    }\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst NZ_WAVE_GLOBAL_DEFAULT_CONFIG = {\n    disabled: false\n};\nconst NZ_WAVE_GLOBAL_CONFIG = new InjectionToken('nz-wave-global-options');\nfunction provideNzWave(config) {\n    return makeEnvironmentProviders([{ provide: NZ_WAVE_GLOBAL_CONFIG, useValue: config }]);\n}\nclass NzWaveDirective {\n    get disabled() {\n        return this.waveDisabled;\n    }\n    get rendererRef() {\n        return this.waveRenderer;\n    }\n    constructor(ngZone, elementRef, config, animationType, platformId, cspNonce) {\n        this.ngZone = ngZone;\n        this.elementRef = elementRef;\n        this.config = config;\n        this.animationType = animationType;\n        this.platformId = platformId;\n        this.cspNonce = cspNonce;\n        this.nzWaveExtraNode = false;\n        this.waveDisabled = false;\n        this.waveDisabled = this.isConfigDisabled();\n    }\n    isConfigDisabled() {\n        let disabled = false;\n        if (this.config && typeof this.config.disabled === 'boolean') {\n            disabled = this.config.disabled;\n        }\n        if (this.animationType === 'NoopAnimations') {\n            disabled = true;\n        }\n        return disabled;\n    }\n    ngOnDestroy() {\n        if (this.waveRenderer) {\n            this.waveRenderer.destroy();\n        }\n    }\n    ngOnInit() {\n        this.renderWaveIfEnabled();\n    }\n    renderWaveIfEnabled() {\n        if (!this.waveDisabled && this.elementRef.nativeElement) {\n            this.waveRenderer = new NzWaveRenderer(this.elementRef.nativeElement, this.ngZone, this.nzWaveExtraNode, this.platformId, this.cspNonce);\n        }\n    }\n    disable() {\n        this.waveDisabled = true;\n        if (this.waveRenderer) {\n            this.waveRenderer.removeTriggerEvent();\n            this.waveRenderer.removeStyleAndExtraNode();\n        }\n    }\n    enable() {\n        // config priority\n        this.waveDisabled = this.isConfigDisabled() || false;\n        if (this.waveRenderer) {\n            this.waveRenderer.bindTriggerEvent();\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzWaveDirective, deps: [{ token: i0.NgZone }, { token: i0.ElementRef }, { token: NZ_WAVE_GLOBAL_CONFIG, optional: true }, { token: ANIMATION_MODULE_TYPE, optional: true }, { token: PLATFORM_ID }, { token: CSP_NONCE, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzWaveDirective, isStandalone: true, selector: \"[nz-wave],button[nz-button]:not([nzType=\\\"link\\\"]):not([nzType=\\\"text\\\"])\", inputs: { nzWaveExtraNode: \"nzWaveExtraNode\" }, exportAs: [\"nzWave\"], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzWaveDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[nz-wave],button[nz-button]:not([nzType=\"link\"]):not([nzType=\"text\"])',\n                    exportAs: 'nzWave',\n                    standalone: true\n                }]\n        }], ctorParameters: () => [{ type: i0.NgZone }, { type: i0.ElementRef }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [NZ_WAVE_GLOBAL_CONFIG]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [CSP_NONCE]\n                }] }], propDecorators: { nzWaveExtraNode: [{\n                type: Input\n            }] } });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzWaveModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzWaveModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.3.8\", ngImport: i0, type: NzWaveModule, imports: [NzWaveDirective], exports: [NzWaveDirective] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzWaveModule, providers: [provideNzWave(NZ_WAVE_GLOBAL_DEFAULT_CONFIG)] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzWaveModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [NzWaveDirective],\n                    exports: [NzWaveDirective],\n                    providers: [provideNzWave(NZ_WAVE_GLOBAL_DEFAULT_CONFIG)]\n                }]\n        }] });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NZ_WAVE_GLOBAL_CONFIG, NZ_WAVE_GLOBAL_DEFAULT_CONFIG, NzWaveDirective, NzWaveModule, NzWaveRenderer, provideNzWave };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,uBAAuB;AAChD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,wBAAwB,EAAEC,WAAW,EAAEC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AAC9I,SAASC,qBAAqB,QAAQ,sCAAsC;;AAE5E;AACA;AACA;AACA;AACA,MAAMC,cAAc,CAAC;EACjB,IAAIC,iBAAiBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACC,eAAe,GAAG,qBAAqB,GAAG,wCAAwC;EAClG;EACAC,WAAWA,CAACC,cAAc,EAAEC,MAAM,EAAEH,eAAe,EAAEI,UAAU,EAAEC,QAAQ,EAAE;IACvE,IAAI,CAACH,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACH,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACI,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,sBAAsB,GAAG,GAAG;IACjC,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACC,OAAO,GAAIC,KAAK,IAAK;MACtB,IAAI,CAAC,IAAI,CAACT,cAAc,IACpB,CAAC,IAAI,CAACA,cAAc,CAACU,YAAY,IACjC,IAAI,CAACV,cAAc,CAACU,YAAY,CAAC,UAAU,CAAC,IAC5CD,KAAK,CAACE,MAAM,CAACC,OAAO,KAAK,OAAO,IAChC,IAAI,CAACZ,cAAc,CAACa,SAAS,CAACC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;QACxD;MACJ;MACA,IAAI,CAACC,WAAW,CAAC,CAAC;IACtB,CAAC;IACD,IAAI,CAACC,QAAQ,GAAG,IAAIhC,QAAQ,CAAC,IAAI,CAACkB,UAAU,CAAC;IAC7C,IAAI,CAACe,YAAY,GAAG,IAAI,CAACT,OAAO,CAACU,IAAI,CAAC,IAAI,CAAC;IAC3C,IAAI,CAACC,gBAAgB,CAAC,CAAC;EAC3B;EACAA,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAACH,QAAQ,CAACI,SAAS,EAAE;MACzB,IAAI,CAACnB,MAAM,CAACoB,iBAAiB,CAAC,MAAM;QAChC,IAAI,CAACC,kBAAkB,CAAC,CAAC;QACzB,IAAI,IAAI,CAACtB,cAAc,EAAE;UACrB,IAAI,CAACA,cAAc,CAACuB,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACN,YAAY,EAAE,IAAI,CAAC;QAC1E;MACJ,CAAC,CAAC;IACN;EACJ;EACAK,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAACtB,cAAc,EAAE;MACrB,IAAI,CAACA,cAAc,CAACwB,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACP,YAAY,EAAE,IAAI,CAAC;IAC7E;EACJ;EACAQ,uBAAuBA,CAAA,EAAG;IACtB,IAAI,IAAI,CAACpB,cAAc,IAAIqB,QAAQ,CAACC,IAAI,CAACC,QAAQ,CAAC,IAAI,CAACvB,cAAc,CAAC,EAAE;MACpEqB,QAAQ,CAACC,IAAI,CAACE,WAAW,CAAC,IAAI,CAACxB,cAAc,CAAC;MAC9C,IAAI,CAACA,cAAc,GAAG,IAAI;IAC9B;IACA,IAAI,IAAI,CAACP,eAAe,IAAI,IAAI,CAACE,cAAc,CAAC4B,QAAQ,CAAC,IAAI,CAACtB,SAAS,CAAC,EAAE;MACtE,IAAI,CAACN,cAAc,CAAC6B,WAAW,CAAC,IAAI,CAACvB,SAAS,CAAC;IACnD;EACJ;EACAwB,OAAOA,CAAA,EAAG;IACN,IAAI,CAACR,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAACG,uBAAuB,CAAC,CAAC;EAClC;EACAV,WAAWA,CAAA,EAAG;IACV,MAAMgB,IAAI,GAAG,IAAI,CAAC/B,cAAc;IAChC,MAAMgC,SAAS,GAAG,IAAI,CAACC,YAAY,CAACF,IAAI,CAAC;IACzCA,IAAI,CAACG,YAAY,CAAC,IAAI,CAACrC,iBAAiB,EAAE,MAAM,CAAC;IACjD,IAAIsC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC7B,QAAQ,GAAG,IAAI,CAACH,sBAAsB,EAAE;MAC1D;IACJ;IACA,IAAI,IAAI,CAACiC,YAAY,CAACL,SAAS,CAAC,EAAE;MAC9B,IAAI,CAAC,IAAI,CAAC3B,cAAc,EAAE;QACtB,IAAI,CAACA,cAAc,GAAGqB,QAAQ,CAACY,aAAa,CAAC,OAAO,CAAC;QACrD,IAAI,IAAI,CAACnC,QAAQ,EAAE;UACf,IAAI,CAACE,cAAc,CAACkC,KAAK,GAAG,IAAI,CAACpC,QAAQ;QAC7C;MACJ;MACA,IAAI,CAACE,cAAc,CAACmC,SAAS,GAAI;AAC7C;AACA,oCAAoCR,SAAU;AAC9C,QAAQ;MACIN,QAAQ,CAACC,IAAI,CAACc,WAAW,CAAC,IAAI,CAACpC,cAAc,CAAC;IAClD;IACA,IAAI,IAAI,CAACP,eAAe,EAAE;MACtB,IAAI,CAAC,IAAI,CAACQ,SAAS,EAAE;QACjB,IAAI,CAACA,SAAS,GAAGoB,QAAQ,CAACY,aAAa,CAAC,KAAK,CAAC;MAClD;MACA,IAAI,CAAChC,SAAS,CAACO,SAAS,GAAG,0BAA0B;MACrDkB,IAAI,CAACU,WAAW,CAAC,IAAI,CAACnC,SAAS,CAAC;IACpC;IACA,IAAI,CAACC,QAAQ,GAAG4B,IAAI,CAACC,GAAG,CAAC,CAAC;IAC1B,IAAI,CAACM,qBAAqB,CAAC,MAAM;MAC7BX,IAAI,CAACY,eAAe,CAAC,IAAI,CAAC9C,iBAAiB,CAAC;MAC5C,IAAI,CAAC4B,uBAAuB,CAAC,CAAC;IAClC,CAAC,EAAE,IAAI,CAACrB,sBAAsB,CAAC;EACnC;EACAiC,YAAYA,CAACO,KAAK,EAAE;IAChB,OAAQ,CAAC,CAACA,KAAK,IACXA,KAAK,KAAK,SAAS,IACnBA,KAAK,KAAK,oBAAoB,IAC9B,IAAI,CAACC,SAAS,CAACD,KAAK,CAAC,IACrB,CAAC,0BAA0B,CAACE,IAAI,CAACF,KAAK,CAAC,IACvCA,KAAK,KAAK,aAAa;EAC/B;EACAC,SAASA,CAACD,KAAK,EAAE;IACb,MAAMG,KAAK,GAAGH,KAAK,CAACG,KAAK,CAAC,0CAA0C,CAAC;IACrE,IAAIA,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,EAAE;MAC3C,OAAO,EAAEA,KAAK,CAAC,CAAC,CAAC,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,KAAKA,KAAK,CAAC,CAAC,CAAC,CAAC;IAC5D;IACA,OAAO,IAAI;EACf;EACAd,YAAYA,CAACF,IAAI,EAAE;IACf,MAAMiB,SAAS,GAAGC,gBAAgB,CAAClB,IAAI,CAAC;IACxC,OAAQiB,SAAS,CAACE,gBAAgB,CAAC,kBAAkB,CAAC;IAAI;IACtDF,SAAS,CAACE,gBAAgB,CAAC,cAAc,CAAC,IAC1CF,SAAS,CAACE,gBAAgB,CAAC,kBAAkB,CAAC;EACtD;EACAR,qBAAqBA,CAACS,EAAE,EAAEC,KAAK,EAAE;IAC7B,IAAI,CAACnD,MAAM,CAACoB,iBAAiB,CAAC,MAAMgC,UAAU,CAACF,EAAE,EAAEC,KAAK,CAAC,CAAC;EAC9D;AACJ;;AAEA;AACA;AACA;AACA;AACA,MAAME,6BAA6B,GAAG;EAClCC,QAAQ,EAAE;AACd,CAAC;AACD,MAAMC,qBAAqB,GAAG,IAAItE,cAAc,CAAC,wBAAwB,CAAC;AAC1E,SAASuE,aAAaA,CAACC,MAAM,EAAE;EAC3B,OAAOvE,wBAAwB,CAAC,CAAC;IAAEwE,OAAO,EAAEH,qBAAqB;IAAEI,QAAQ,EAAEF;EAAO,CAAC,CAAC,CAAC;AAC3F;AACA,MAAMG,eAAe,CAAC;EAClB,IAAIN,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACO,YAAY;EAC5B;EACA,IAAIC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,YAAY;EAC5B;EACAjE,WAAWA,CAACE,MAAM,EAAEgE,UAAU,EAAEP,MAAM,EAAEQ,aAAa,EAAEhE,UAAU,EAAEC,QAAQ,EAAE;IACzE,IAAI,CAACF,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACgE,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACP,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACQ,aAAa,GAAGA,aAAa;IAClC,IAAI,CAAChE,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACgE,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACL,YAAY,GAAG,KAAK;IACzB,IAAI,CAACA,YAAY,GAAG,IAAI,CAACM,gBAAgB,CAAC,CAAC;EAC/C;EACAA,gBAAgBA,CAAA,EAAG;IACf,IAAIb,QAAQ,GAAG,KAAK;IACpB,IAAI,IAAI,CAACG,MAAM,IAAI,OAAO,IAAI,CAACA,MAAM,CAACH,QAAQ,KAAK,SAAS,EAAE;MAC1DA,QAAQ,GAAG,IAAI,CAACG,MAAM,CAACH,QAAQ;IACnC;IACA,IAAI,IAAI,CAACW,aAAa,KAAK,gBAAgB,EAAE;MACzCX,QAAQ,GAAG,IAAI;IACnB;IACA,OAAOA,QAAQ;EACnB;EACAc,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACL,YAAY,EAAE;MACnB,IAAI,CAACA,YAAY,CAAClC,OAAO,CAAC,CAAC;IAC/B;EACJ;EACAwC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,mBAAmB,CAAC,CAAC;EAC9B;EACAA,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAAC,IAAI,CAACT,YAAY,IAAI,IAAI,CAACG,UAAU,CAACO,aAAa,EAAE;MACrD,IAAI,CAACR,YAAY,GAAG,IAAIpE,cAAc,CAAC,IAAI,CAACqE,UAAU,CAACO,aAAa,EAAE,IAAI,CAACvE,MAAM,EAAE,IAAI,CAACkE,eAAe,EAAE,IAAI,CAACjE,UAAU,EAAE,IAAI,CAACC,QAAQ,CAAC;IAC5I;EACJ;EACAsE,OAAOA,CAAA,EAAG;IACN,IAAI,CAACX,YAAY,GAAG,IAAI;IACxB,IAAI,IAAI,CAACE,YAAY,EAAE;MACnB,IAAI,CAACA,YAAY,CAAC1C,kBAAkB,CAAC,CAAC;MACtC,IAAI,CAAC0C,YAAY,CAACvC,uBAAuB,CAAC,CAAC;IAC/C;EACJ;EACAiD,MAAMA,CAAA,EAAG;IACL;IACA,IAAI,CAACZ,YAAY,GAAG,IAAI,CAACM,gBAAgB,CAAC,CAAC,IAAI,KAAK;IACpD,IAAI,IAAI,CAACJ,YAAY,EAAE;MACnB,IAAI,CAACA,YAAY,CAAC7C,gBAAgB,CAAC,CAAC;IACxC;EACJ;EACA;IAAS,IAAI,CAACwD,IAAI,YAAAC,wBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFhB,eAAe,EAAzB5E,EAAE,CAAA6F,iBAAA,CAAyC7F,EAAE,CAAC8F,MAAM,GAApD9F,EAAE,CAAA6F,iBAAA,CAA+D7F,EAAE,CAAC+F,UAAU,GAA9E/F,EAAE,CAAA6F,iBAAA,CAAyFtB,qBAAqB,MAAhHvE,EAAE,CAAA6F,iBAAA,CAA2InF,qBAAqB,MAAlKV,EAAE,CAAA6F,iBAAA,CAA6L1F,WAAW,GAA1MH,EAAE,CAAA6F,iBAAA,CAAqNzF,SAAS;IAAA,CAA4D;EAAE;EAC9X;IAAS,IAAI,CAAC4F,IAAI,kBAD8EhG,EAAE,CAAAiG,iBAAA;MAAAC,IAAA,EACJtB,eAAe;MAAAuB,SAAA;MAAAC,MAAA;QAAAlB,eAAA;MAAA;MAAAmB,QAAA;MAAAC,UAAA;IAAA,EAAkM;EAAE;AACrT;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoGvG,EAAE,CAAAwG,iBAAA,CAGX5B,eAAe,EAAc,CAAC;IAC7GsB,IAAI,EAAE7F,SAAS;IACfoG,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,uEAAuE;MACjFL,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEJ,IAAI,EAAElG,EAAE,CAAC8F;EAAO,CAAC,EAAE;IAAEI,IAAI,EAAElG,EAAE,CAAC+F;EAAW,CAAC,EAAE;IAAEG,IAAI,EAAES,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC7FV,IAAI,EAAE5F;IACV,CAAC,EAAE;MACC4F,IAAI,EAAE3F,MAAM;MACZkG,IAAI,EAAE,CAAClC,qBAAqB;IAChC,CAAC;EAAE,CAAC,EAAE;IAAE2B,IAAI,EAAES,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClCV,IAAI,EAAE5F;IACV,CAAC,EAAE;MACC4F,IAAI,EAAE3F,MAAM;MACZkG,IAAI,EAAE,CAAC/F,qBAAqB;IAChC,CAAC;EAAE,CAAC,EAAE;IAAEwF,IAAI,EAAES,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClCV,IAAI,EAAE3F,MAAM;MACZkG,IAAI,EAAE,CAACtG,WAAW;IACtB,CAAC;EAAE,CAAC,EAAE;IAAE+F,IAAI,EAAES,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClCV,IAAI,EAAE5F;IACV,CAAC,EAAE;MACC4F,IAAI,EAAE3F,MAAM;MACZkG,IAAI,EAAE,CAACrG,SAAS;IACpB,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAE8E,eAAe,EAAE,CAAC;MAC3CgB,IAAI,EAAE1F;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAMqG,YAAY,CAAC;EACf;IAAS,IAAI,CAACnB,IAAI,YAAAoB,qBAAAlB,CAAA;MAAA,YAAAA,CAAA,IAAwFiB,YAAY;IAAA,CAAkD;EAAE;EAC1K;IAAS,IAAI,CAACE,IAAI,kBAtC8E/G,EAAE,CAAAgH,gBAAA;MAAAd,IAAA,EAsCSW,YAAY;MAAAI,OAAA,GAAYrC,eAAe;MAAAsC,OAAA,GAAatC,eAAe;IAAA,EAAI;EAAE;EACpL;IAAS,IAAI,CAACuC,IAAI,kBAvC8EnH,EAAE,CAAAoH,gBAAA;MAAAC,SAAA,EAuCkC,CAAC7C,aAAa,CAACH,6BAA6B,CAAC;IAAC,EAAG;EAAE;AAC3L;AACA;EAAA,QAAAkC,SAAA,oBAAAA,SAAA,KAzCoGvG,EAAE,CAAAwG,iBAAA,CAyCXK,YAAY,EAAc,CAAC;IAC1GX,IAAI,EAAEzF,QAAQ;IACdgG,IAAI,EAAE,CAAC;MACCQ,OAAO,EAAE,CAACrC,eAAe,CAAC;MAC1BsC,OAAO,EAAE,CAACtC,eAAe,CAAC;MAC1ByC,SAAS,EAAE,CAAC7C,aAAa,CAACH,6BAA6B,CAAC;IAC5D,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASE,qBAAqB,EAAEF,6BAA6B,EAAEO,eAAe,EAAEiC,YAAY,EAAElG,cAAc,EAAE6D,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}