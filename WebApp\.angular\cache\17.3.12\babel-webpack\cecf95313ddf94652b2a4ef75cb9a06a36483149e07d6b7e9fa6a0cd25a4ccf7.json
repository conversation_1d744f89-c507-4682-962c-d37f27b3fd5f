{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./file.component.html?ngResource\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterLink } from '@angular/router';\nimport { FormsModule } from '@angular/forms';\nimport { FileServiceProxy } from '../../../../../shared/service-proxies/service-proxies';\nlet FileComponent = class FileComponent {\n  constructor(fileService) {\n    this.fileService = fileService;\n    this.files = [];\n    this.loading = true;\n    this.searchTerm = '';\n    this.selectedFile = null;\n    this.fileToDelete = null;\n    this.sampleFiles = [];\n    this.aiPrompt = '';\n    this.syncingAI = false;\n    this.showRegenerateForm = false;\n  }\n  ngOnInit() {\n    // Load files when component initializes\n    this.loadFiles();\n  }\n  loadFiles() {\n    this.fileService.getAllFiles('File').subscribe({\n      next: res => {\n        this.sampleFiles = res;\n        this.files = this.sampleFiles;\n        this.loading = false;\n      },\n      error: err => {\n        console.error('Error loading files:', err);\n        this.loading = false;\n      }\n    });\n    // Simulate loading data from a service\n  }\n  searchFiles() {\n    if (!this.searchTerm.trim()) {\n      this.files = this.sampleFiles;\n      return;\n    }\n    const term = this.searchTerm.toLowerCase();\n    this.files = this.sampleFiles.filter(file => (file.fileName?.toLowerCase() || '').includes(term) || file.description && file.description.toLowerCase().includes(term));\n  }\n  getShortDescription(description) {\n    if (!description) return '';\n    // Extract first 150 characters for preview\n    return description.length > 150 ? description.substring(0, 150) + '...' : description;\n  }\n  selectFile(file) {\n    this.selectedFile = file;\n    this.aiPrompt = ''; // Reset prompt when selecting a file\n    this.showRegenerateForm = false; // Hide regenerate form initially\n  }\n  closeFileDetail() {\n    this.selectedFile = null;\n    this.aiPrompt = ''; // Reset prompt when closing detail\n    this.showRegenerateForm = false;\n  }\n  toggleRegenerateForm() {\n    this.showRegenerateForm = !this.showRegenerateForm;\n    if (!this.showRegenerateForm) {\n      this.aiPrompt = ''; // Clear prompt when hiding the form\n    }\n  }\n  downloadFile(file) {\n    if (!file || !file.fileName) {\n      console.error('Invalid file or file name');\n      return;\n    }\n    // Create a link element to trigger the download\n    // Use the API endpoint directly\n    const baseUrl = location.origin; // Get the base URL from the current location\n    const downloadUrl = `${baseUrl}/api/File/Getfile/${encodeURIComponent(file.fileName)}`;\n    // Create a temporary anchor element to trigger the download\n    const link = document.createElement('a');\n    link.href = downloadUrl;\n    link.download = file.fileName || 'download';\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  }\n  confirmDelete(file) {\n    this.fileToDelete = file;\n  }\n  deleteFile(file) {\n    if (!file || !file.fileName) {\n      console.error('Invalid file or file name');\n      return;\n    }\n    console.log('Deleting file:', file.fileName);\n    // Call the service to delete the file - pass an array with the filename\n    this.fileService.deleteFile([file.fileName]).subscribe({\n      next: res => {\n        console.log('File deleted:', res);\n        this.files = this.files.filter(f => f !== file);\n        this.fileToDelete = null;\n        this.loadFiles();\n      },\n      error: err => {\n        console.error('Error deleting file:', err);\n      }\n    });\n  }\n  cancelDelete() {\n    this.fileToDelete = null;\n  }\n  syncWithAI(file) {\n    if (!file || !file.fileName || !this.aiPrompt.trim()) {\n      console.error('Invalid file, file name, or empty prompt');\n      return;\n    }\n    this.syncingAI = true;\n    this.fileService.syncWithAI(file.fileName, this.aiPrompt).subscribe({\n      next: response => {\n        console.log('AI sync successful:', response);\n        // Reload the file to get the updated AI analysis\n        if (file.fileName) {\n          this.loadFileDetails(file.fileName);\n        }\n        this.aiPrompt = ''; // Clear the prompt after successful sync\n        this.syncingAI = false;\n      },\n      error: err => {\n        console.error('Error syncing with AI:', err);\n        this.syncingAI = false;\n      }\n    });\n  }\n  loadFileDetails(fileName) {\n    this.fileService.getFileDto(fileName).subscribe({\n      next: fileDetails => {\n        // Update the selected file with the latest details\n        this.selectedFile = fileDetails;\n        // Also update the file in the files array\n        const index = this.files.findIndex(f => f.fileName === fileName);\n        if (index !== -1) {\n          this.files[index] = fileDetails;\n        }\n      },\n      error: err => {\n        console.error('Error loading file details:', err);\n      }\n    });\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: FileServiceProxy\n    }];\n  }\n};\nFileComponent = __decorate([Component({\n  selector: 'app-file',\n  standalone: true,\n  imports: [CommonModule, RouterLink, FormsModule],\n  template: __NG_CLI_RESOURCE__0\n})], FileComponent);\nexport { FileComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "RouterLink", "FormsModule", "FileServiceProxy", "FileComponent", "constructor", "fileService", "files", "loading", "searchTerm", "selectedFile", "fileToDelete", "sampleFiles", "aiPrompt", "syncingAI", "showRegenerateForm", "ngOnInit", "loadFiles", "getAllFiles", "subscribe", "next", "res", "error", "err", "console", "searchFiles", "trim", "term", "toLowerCase", "filter", "file", "fileName", "includes", "description", "getShortDescription", "length", "substring", "selectFile", "closeFileDetail", "toggleRegenerateForm", "downloadFile", "baseUrl", "location", "origin", "downloadUrl", "encodeURIComponent", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "confirmDelete", "deleteFile", "log", "f", "cancelDelete", "syncWithAI", "response", "loadFileDetails", "getFileDto", "fileDetails", "index", "findIndex", "__decorate", "selector", "standalone", "imports", "template", "__NG_CLI_RESOURCE__0"], "sources": ["C:\\Users\\<USER>\\source\\ai-hub\\WebApp\\src\\app\\admin\\settings\\Files\\file\\file.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { RouterLink } from '@angular/router';\r\nimport { FormsModule } from '@angular/forms';\r\nimport {\r\n  FileDto,\r\n  FileServiceProxy,\r\n} from '../../../../../shared/service-proxies/service-proxies';\r\n\r\n@Component({\r\n  selector: 'app-file',\r\n  standalone: true,\r\n  imports: [CommonModule, RouterLink, FormsModule],\r\n  templateUrl: './file.component.html',\r\n})\r\nexport class FileComponent implements OnInit {\r\n  files: FileDto[] = [];\r\n  loading: boolean = true;\r\n  searchTerm: string = '';\r\n  selectedFile: FileDto | null = null;\r\n  fileToDelete: FileDto | null = null;\r\n  sampleFiles: FileDto[] = [];\r\n  aiPrompt: string = '';\r\n  syncingAI: boolean = false;\r\n  showRegenerateForm: boolean = false;\r\n\r\n  constructor(private fileService: FileServiceProxy) {}\r\n\r\n  ngOnInit(): void {\r\n    // Load files when component initializes\r\n    this.loadFiles();\r\n  }\r\n\r\n  loadFiles(): void {\r\n    this.fileService.getAllFiles('File').subscribe({\r\n      next: (res) => {\r\n        this.sampleFiles = res;\r\n        this.files = this.sampleFiles;\r\n\r\n        this.loading = false;\r\n      },\r\n      error: (err) => {\r\n        console.error('Error loading files:', err);\r\n        this.loading = false;\r\n      },\r\n    });\r\n    // Simulate loading data from a service\r\n  }\r\n\r\n  searchFiles(): void {\r\n    if (!this.searchTerm.trim()) {\r\n      this.files = this.sampleFiles;\r\n      return;\r\n    }\r\n    const term = this.searchTerm.toLowerCase();\r\n    this.files = this.sampleFiles.filter(\r\n      (file) =>\r\n        (file.fileName?.toLowerCase() || '').includes(term) ||\r\n        (file.description && file.description.toLowerCase().includes(term))\r\n    );\r\n  }\r\n\r\n  getShortDescription(description: string): string {\r\n    if (!description) return '';\r\n\r\n    // Extract first 150 characters for preview\r\n    return description.length > 150\r\n      ? description.substring(0, 150) + '...'\r\n      : description;\r\n  }\r\n\r\n  selectFile(file: FileDto): void {\r\n    this.selectedFile = file;\r\n    this.aiPrompt = ''; // Reset prompt when selecting a file\r\n    this.showRegenerateForm = false; // Hide regenerate form initially\r\n  }\r\n\r\n  closeFileDetail(): void {\r\n    this.selectedFile = null;\r\n    this.aiPrompt = ''; // Reset prompt when closing detail\r\n    this.showRegenerateForm = false;\r\n  }\r\n\r\n  toggleRegenerateForm(): void {\r\n    this.showRegenerateForm = !this.showRegenerateForm;\r\n    if (!this.showRegenerateForm) {\r\n      this.aiPrompt = ''; // Clear prompt when hiding the form\r\n    }\r\n  }\r\n\r\n  downloadFile(file: FileDto): void {\r\n    if (!file || !file.fileName) {\r\n      console.error('Invalid file or file name');\r\n      return;\r\n    }\r\n\r\n    // Create a link element to trigger the download\r\n    // Use the API endpoint directly\r\n    const baseUrl = location.origin; // Get the base URL from the current location\r\n    const downloadUrl = `${baseUrl}/api/File/Getfile/${encodeURIComponent(\r\n      file.fileName\r\n    )}`;\r\n\r\n    // Create a temporary anchor element to trigger the download\r\n    const link = document.createElement('a');\r\n    link.href = downloadUrl;\r\n    link.download = file.fileName || 'download';\r\n    document.body.appendChild(link);\r\n    link.click();\r\n    document.body.removeChild(link);\r\n  }\r\n\r\n  confirmDelete(file: FileDto): void {\r\n    this.fileToDelete = file;\r\n  }\r\n\r\n  deleteFile(file: FileDto): void {\r\n    if (!file || !file.fileName) {\r\n      console.error('Invalid file or file name');\r\n      return;\r\n    }\r\n\r\n    console.log('Deleting file:', file.fileName);\r\n\r\n    // Call the service to delete the file - pass an array with the filename\r\n    this.fileService.deleteFile([file.fileName]).subscribe({\r\n      next: (res) => {\r\n        console.log('File deleted:', res);\r\n        this.files = this.files.filter((f) => f !== file);\r\n        this.fileToDelete = null;\r\n        this.loadFiles();\r\n      },\r\n      error: (err) => {\r\n        console.error('Error deleting file:', err);\r\n      },\r\n    });\r\n  }\r\n\r\n  cancelDelete(): void {\r\n    this.fileToDelete = null;\r\n  }\r\n\r\n  syncWithAI(file: FileDto): void {\r\n    if (!file || !file.fileName || !this.aiPrompt.trim()) {\r\n      console.error('Invalid file, file name, or empty prompt');\r\n      return;\r\n    }\r\n\r\n    this.syncingAI = true;\r\n\r\n    this.fileService.syncWithAI(file.fileName, this.aiPrompt).subscribe({\r\n      next: (response) => {\r\n        console.log('AI sync successful:', response);\r\n        // Reload the file to get the updated AI analysis\r\n        if (file.fileName) {\r\n          this.loadFileDetails(file.fileName);\r\n        }\r\n        this.aiPrompt = ''; // Clear the prompt after successful sync\r\n        this.syncingAI = false;\r\n      },\r\n      error: (err) => {\r\n        console.error('Error syncing with AI:', err);\r\n        this.syncingAI = false;\r\n      },\r\n    });\r\n  }\r\n\r\n  loadFileDetails(fileName: string): void {\r\n    this.fileService.getFileDto(fileName).subscribe({\r\n      next: (fileDetails) => {\r\n        // Update the selected file with the latest details\r\n        this.selectedFile = fileDetails;\r\n\r\n        // Also update the file in the files array\r\n        const index = this.files.findIndex((f) => f.fileName === fileName);\r\n        if (index !== -1) {\r\n          this.files[index] = fileDetails;\r\n        }\r\n      },\r\n      error: (err) => {\r\n        console.error('Error loading file details:', err);\r\n      },\r\n    });\r\n  }\r\n}\r\n"], "mappings": ";;AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAEEC,gBAAgB,QACX,uDAAuD;AAQvD,IAAMC,aAAa,GAAnB,MAAMA,aAAa;EAWxBC,YAAoBC,WAA6B;IAA7B,KAAAA,WAAW,GAAXA,WAAW;IAV/B,KAAAC,KAAK,GAAc,EAAE;IACrB,KAAAC,OAAO,GAAY,IAAI;IACvB,KAAAC,UAAU,GAAW,EAAE;IACvB,KAAAC,YAAY,GAAmB,IAAI;IACnC,KAAAC,YAAY,GAAmB,IAAI;IACnC,KAAAC,WAAW,GAAc,EAAE;IAC3B,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,SAAS,GAAY,KAAK;IAC1B,KAAAC,kBAAkB,GAAY,KAAK;EAEiB;EAEpDC,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,SAAS,EAAE;EAClB;EAEAA,SAASA,CAAA;IACP,IAAI,CAACX,WAAW,CAACY,WAAW,CAAC,MAAM,CAAC,CAACC,SAAS,CAAC;MAC7CC,IAAI,EAAGC,GAAG,IAAI;QACZ,IAAI,CAACT,WAAW,GAAGS,GAAG;QACtB,IAAI,CAACd,KAAK,GAAG,IAAI,CAACK,WAAW;QAE7B,IAAI,CAACJ,OAAO,GAAG,KAAK;MACtB,CAAC;MACDc,KAAK,EAAGC,GAAG,IAAI;QACbC,OAAO,CAACF,KAAK,CAAC,sBAAsB,EAAEC,GAAG,CAAC;QAC1C,IAAI,CAACf,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;IACF;EACF;EAEAiB,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAAChB,UAAU,CAACiB,IAAI,EAAE,EAAE;MAC3B,IAAI,CAACnB,KAAK,GAAG,IAAI,CAACK,WAAW;MAC7B;;IAEF,MAAMe,IAAI,GAAG,IAAI,CAAClB,UAAU,CAACmB,WAAW,EAAE;IAC1C,IAAI,CAACrB,KAAK,GAAG,IAAI,CAACK,WAAW,CAACiB,MAAM,CACjCC,IAAI,IACH,CAACA,IAAI,CAACC,QAAQ,EAAEH,WAAW,EAAE,IAAI,EAAE,EAAEI,QAAQ,CAACL,IAAI,CAAC,IAClDG,IAAI,CAACG,WAAW,IAAIH,IAAI,CAACG,WAAW,CAACL,WAAW,EAAE,CAACI,QAAQ,CAACL,IAAI,CAAE,CACtE;EACH;EAEAO,mBAAmBA,CAACD,WAAmB;IACrC,IAAI,CAACA,WAAW,EAAE,OAAO,EAAE;IAE3B;IACA,OAAOA,WAAW,CAACE,MAAM,GAAG,GAAG,GAC3BF,WAAW,CAACG,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK,GACrCH,WAAW;EACjB;EAEAI,UAAUA,CAACP,IAAa;IACtB,IAAI,CAACpB,YAAY,GAAGoB,IAAI;IACxB,IAAI,CAACjB,QAAQ,GAAG,EAAE,CAAC,CAAC;IACpB,IAAI,CAACE,kBAAkB,GAAG,KAAK,CAAC,CAAC;EACnC;EAEAuB,eAAeA,CAAA;IACb,IAAI,CAAC5B,YAAY,GAAG,IAAI;IACxB,IAAI,CAACG,QAAQ,GAAG,EAAE,CAAC,CAAC;IACpB,IAAI,CAACE,kBAAkB,GAAG,KAAK;EACjC;EAEAwB,oBAAoBA,CAAA;IAClB,IAAI,CAACxB,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAkB;IAClD,IAAI,CAAC,IAAI,CAACA,kBAAkB,EAAE;MAC5B,IAAI,CAACF,QAAQ,GAAG,EAAE,CAAC,CAAC;;EAExB;EAEA2B,YAAYA,CAACV,IAAa;IACxB,IAAI,CAACA,IAAI,IAAI,CAACA,IAAI,CAACC,QAAQ,EAAE;MAC3BP,OAAO,CAACF,KAAK,CAAC,2BAA2B,CAAC;MAC1C;;IAGF;IACA;IACA,MAAMmB,OAAO,GAAGC,QAAQ,CAACC,MAAM,CAAC,CAAC;IACjC,MAAMC,WAAW,GAAG,GAAGH,OAAO,qBAAqBI,kBAAkB,CACnEf,IAAI,CAACC,QAAQ,CACd,EAAE;IAEH;IACA,MAAMe,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAGL,WAAW;IACvBE,IAAI,CAACI,QAAQ,GAAGpB,IAAI,CAACC,QAAQ,IAAI,UAAU;IAC3CgB,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;IAC/BA,IAAI,CAACO,KAAK,EAAE;IACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;EACjC;EAEAS,aAAaA,CAACzB,IAAa;IACzB,IAAI,CAACnB,YAAY,GAAGmB,IAAI;EAC1B;EAEA0B,UAAUA,CAAC1B,IAAa;IACtB,IAAI,CAACA,IAAI,IAAI,CAACA,IAAI,CAACC,QAAQ,EAAE;MAC3BP,OAAO,CAACF,KAAK,CAAC,2BAA2B,CAAC;MAC1C;;IAGFE,OAAO,CAACiC,GAAG,CAAC,gBAAgB,EAAE3B,IAAI,CAACC,QAAQ,CAAC;IAE5C;IACA,IAAI,CAACzB,WAAW,CAACkD,UAAU,CAAC,CAAC1B,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACZ,SAAS,CAAC;MACrDC,IAAI,EAAGC,GAAG,IAAI;QACZG,OAAO,CAACiC,GAAG,CAAC,eAAe,EAAEpC,GAAG,CAAC;QACjC,IAAI,CAACd,KAAK,GAAG,IAAI,CAACA,KAAK,CAACsB,MAAM,CAAE6B,CAAC,IAAKA,CAAC,KAAK5B,IAAI,CAAC;QACjD,IAAI,CAACnB,YAAY,GAAG,IAAI;QACxB,IAAI,CAACM,SAAS,EAAE;MAClB,CAAC;MACDK,KAAK,EAAGC,GAAG,IAAI;QACbC,OAAO,CAACF,KAAK,CAAC,sBAAsB,EAAEC,GAAG,CAAC;MAC5C;KACD,CAAC;EACJ;EAEAoC,YAAYA,CAAA;IACV,IAAI,CAAChD,YAAY,GAAG,IAAI;EAC1B;EAEAiD,UAAUA,CAAC9B,IAAa;IACtB,IAAI,CAACA,IAAI,IAAI,CAACA,IAAI,CAACC,QAAQ,IAAI,CAAC,IAAI,CAAClB,QAAQ,CAACa,IAAI,EAAE,EAAE;MACpDF,OAAO,CAACF,KAAK,CAAC,0CAA0C,CAAC;MACzD;;IAGF,IAAI,CAACR,SAAS,GAAG,IAAI;IAErB,IAAI,CAACR,WAAW,CAACsD,UAAU,CAAC9B,IAAI,CAACC,QAAQ,EAAE,IAAI,CAAClB,QAAQ,CAAC,CAACM,SAAS,CAAC;MAClEC,IAAI,EAAGyC,QAAQ,IAAI;QACjBrC,OAAO,CAACiC,GAAG,CAAC,qBAAqB,EAAEI,QAAQ,CAAC;QAC5C;QACA,IAAI/B,IAAI,CAACC,QAAQ,EAAE;UACjB,IAAI,CAAC+B,eAAe,CAAChC,IAAI,CAACC,QAAQ,CAAC;;QAErC,IAAI,CAAClB,QAAQ,GAAG,EAAE,CAAC,CAAC;QACpB,IAAI,CAACC,SAAS,GAAG,KAAK;MACxB,CAAC;MACDQ,KAAK,EAAGC,GAAG,IAAI;QACbC,OAAO,CAACF,KAAK,CAAC,wBAAwB,EAAEC,GAAG,CAAC;QAC5C,IAAI,CAACT,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EAEAgD,eAAeA,CAAC/B,QAAgB;IAC9B,IAAI,CAACzB,WAAW,CAACyD,UAAU,CAAChC,QAAQ,CAAC,CAACZ,SAAS,CAAC;MAC9CC,IAAI,EAAG4C,WAAW,IAAI;QACpB;QACA,IAAI,CAACtD,YAAY,GAAGsD,WAAW;QAE/B;QACA,MAAMC,KAAK,GAAG,IAAI,CAAC1D,KAAK,CAAC2D,SAAS,CAAER,CAAC,IAAKA,CAAC,CAAC3B,QAAQ,KAAKA,QAAQ,CAAC;QAClE,IAAIkC,KAAK,KAAK,CAAC,CAAC,EAAE;UAChB,IAAI,CAAC1D,KAAK,CAAC0D,KAAK,CAAC,GAAGD,WAAW;;MAEnC,CAAC;MACD1C,KAAK,EAAGC,GAAG,IAAI;QACbC,OAAO,CAACF,KAAK,CAAC,6BAA6B,EAAEC,GAAG,CAAC;MACnD;KACD,CAAC;EACJ;;;;;;;AAxKWnB,aAAa,GAAA+D,UAAA,EANzBpE,SAAS,CAAC;EACTqE,QAAQ,EAAE,UAAU;EACpBC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACtE,YAAY,EAAEC,UAAU,EAAEC,WAAW,CAAC;EAChDqE,QAAA,EAAAC;CACD,CAAC,C,EACWpE,aAAa,CAyKzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}