{"ast": null, "code": "// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\n/** A logger that does nothing when log messages are sent to it. */\nexport class NullLogger {\n  constructor() {}\n  /** @inheritDoc */\n  // eslint-disable-next-line\n  log(_logLevel, _message) {}\n}\n/** The singleton instance of the {@link @microsoft/signalr.NullLogger}. */\nNullLogger.instance = new NullLogger();", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "log", "_logLevel", "_message", "instance"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/@microsoft/signalr/dist/esm/Loggers.js"], "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n/** A logger that does nothing when log messages are sent to it. */\r\nexport class NullLogger {\r\n    constructor() { }\r\n    /** @inheritDoc */\r\n    // eslint-disable-next-line\r\n    log(_logLevel, _message) {\r\n    }\r\n}\r\n/** The singleton instance of the {@link @microsoft/signalr.NullLogger}. */\r\nNullLogger.instance = new NullLogger();\r\n"], "mappings": "AAAA;AACA;AACA;AACA,OAAO,MAAMA,UAAU,CAAC;EACpBC,WAAWA,CAAA,EAAG,CAAE;EAChB;EACA;EACAC,GAAGA,CAACC,SAAS,EAAEC,QAAQ,EAAE,CACzB;AACJ;AACA;AACAJ,UAAU,CAACK,QAAQ,GAAG,IAAIL,UAAU,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}