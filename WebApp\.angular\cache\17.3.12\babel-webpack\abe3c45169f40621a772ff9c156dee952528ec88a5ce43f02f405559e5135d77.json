{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/source/ai-hub/WebApp/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/**\n * @license Angular v17.3.12\n * (c) 2010-2024 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { ɵDeferBlockState, ɵtriggerResourceLoading, ɵrenderDeferBlockState, ɵCONTAINER_HEADER_OFFSET, ɵgetDeferBlocks, ɵDeferBlockBehavior, InjectionToken, inject as inject$1, ɵNoopNgZone, NgZone, ɵEffectScheduler, ApplicationRef, getDebugNode, RendererFactory2, ɵPendingTasks, ɵstringify, ɵReflectionCapabilities, Directive, Component, Pipe, NgModule, ɵgetAsyncClassMetadataFn, ɵgenerateStandaloneInDeclarationsError, ɵUSE_RUNTIME_DEPS_TRACKER_FOR_JIT, ɵdepsTracker, ɵgetInjectableDef, resolveForwardRef, ɵNG_COMP_DEF, ɵisComponentDefPendingResolution, ɵresolveComponentResources, ɵRender3NgModuleRef, ApplicationInitStatus, LOCALE_ID, ɵDEFAULT_LOCALE_ID, ɵsetLocaleId, ɵRender3ComponentFactory, ɵcompileComponent, ɵNG_DIR_DEF, ɵcompileDirective, ɵNG_PIPE_DEF, ɵcompilePipe, ɵNG_MOD_DEF, ɵtransitiveScopesFor, ɵpatchComponentDefWithScope, ɵNG_INJ_DEF, ɵcompileNgModuleDefs, ɵclearResolutionOfComponentResourcesQueue, ɵrestoreComponentResolutionQueue, provideZoneChangeDetection, Compiler, ɵDEFER_BLOCK_CONFIG, COMPILER_OPTIONS, Injector, ɵisEnvironmentProviders, ɵNgModuleFactory, ModuleWithComponentFactories, ɵconvertToBitFlags, InjectFlags, ɵsetAllowDuplicateNgModuleIdsForTest, ɵresetCompiledComponents, ɵsetUnknownElementStrictMode, ɵsetUnknownPropertyStrictMode, ɵgetUnknownElementStrictMode, ɵgetUnknownPropertyStrictMode, runInInjectionContext, EnvironmentInjector, ɵChangeDetectionScheduler, ɵflushModuleScopingQueueAsMuchAsPossible } from '@angular/core';\nexport { ɵDeferBlockBehavior as DeferBlockBehavior, ɵDeferBlockState as DeferBlockState } from '@angular/core';\nimport { Subscription } from 'rxjs';\nimport { first } from 'rxjs/operators';\nimport { ResourceLoader } from '@angular/compiler';\n\n/**\n * Wraps a test function in an asynchronous test zone. The test will automatically\n * complete when all asynchronous calls within this zone are done. Can be used\n * to wrap an {@link inject} call.\n *\n * Example:\n *\n * ```\n * it('...', waitForAsync(inject([AClass], (object) => {\n *   object.doSomething.then(() => {\n *     expect(...);\n *   })\n * })));\n * ```\n *\n * @publicApi\n */\nfunction waitForAsync(fn) {\n  const _Zone = typeof Zone !== 'undefined' ? Zone : null;\n  if (!_Zone) {\n    return function () {\n      return Promise.reject('Zone is needed for the waitForAsync() test helper but could not be found. ' + 'Please make sure that your environment includes zone.js');\n    };\n  }\n  const asyncTest = _Zone && _Zone[_Zone.__symbol__('asyncTest')];\n  if (typeof asyncTest === 'function') {\n    return asyncTest(fn);\n  }\n  return function () {\n    return Promise.reject('zone-testing.js is needed for the async() test helper but could not be found. ' + 'Please make sure that your environment includes zone.js/testing');\n  };\n}\n/**\n * @deprecated use `waitForAsync()`, (expected removal in v12)\n * @see {@link waitForAsync}\n * @publicApi\n * */\nfunction async(fn) {\n  return waitForAsync(fn);\n}\n\n/**\n * Represents an individual defer block for testing purposes.\n *\n * @publicApi\n * @developerPreview\n */\nclass DeferBlockFixture {\n  /** @nodoc */\n  constructor(block, componentFixture) {\n    this.block = block;\n    this.componentFixture = componentFixture;\n  }\n  /**\n   * Renders the specified state of the defer fixture.\n   * @param state the defer state to render\n   */\n  render(state) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (!hasStateTemplate(state, _this.block)) {\n        const stateAsString = getDeferBlockStateNameFromEnum(state);\n        throw new Error(`Tried to render this defer block in the \\`${stateAsString}\\` state, ` + `but there was no @${stateAsString.toLowerCase()} block defined in a template.`);\n      }\n      if (state === ɵDeferBlockState.Complete) {\n        yield ɵtriggerResourceLoading(_this.block.tDetails, _this.block.lView, _this.block.tNode);\n      }\n      // If the `render` method is used explicitly - skip timer-based scheduling for\n      // `@placeholder` and `@loading` blocks and render them immediately.\n      const skipTimerScheduling = true;\n      ɵrenderDeferBlockState(state, _this.block.tNode, _this.block.lContainer, skipTimerScheduling);\n      _this.componentFixture.detectChanges();\n    })();\n  }\n  /**\n   * Retrieves all nested child defer block fixtures\n   * in a given defer block.\n   */\n  getDeferBlocks() {\n    const deferBlocks = [];\n    // An LContainer that represents a defer block has at most 1 view, which is\n    // located right after an LContainer header. Get a hold of that view and inspect\n    // it for nested defer blocks.\n    const deferBlockFixtures = [];\n    if (this.block.lContainer.length >= ɵCONTAINER_HEADER_OFFSET) {\n      const lView = this.block.lContainer[ɵCONTAINER_HEADER_OFFSET];\n      ɵgetDeferBlocks(lView, deferBlocks);\n      for (const block of deferBlocks) {\n        deferBlockFixtures.push(new DeferBlockFixture(block, this.componentFixture));\n      }\n    }\n    return Promise.resolve(deferBlockFixtures);\n  }\n}\nfunction hasStateTemplate(state, block) {\n  switch (state) {\n    case ɵDeferBlockState.Placeholder:\n      return block.tDetails.placeholderTmplIndex !== null;\n    case ɵDeferBlockState.Loading:\n      return block.tDetails.loadingTmplIndex !== null;\n    case ɵDeferBlockState.Error:\n      return block.tDetails.errorTmplIndex !== null;\n    case ɵDeferBlockState.Complete:\n      return true;\n    default:\n      return false;\n  }\n}\nfunction getDeferBlockStateNameFromEnum(state) {\n  switch (state) {\n    case ɵDeferBlockState.Placeholder:\n      return 'Placeholder';\n    case ɵDeferBlockState.Loading:\n      return 'Loading';\n    case ɵDeferBlockState.Error:\n      return 'Error';\n    default:\n      return 'Main';\n  }\n}\n\n/** Whether test modules should be torn down by default. */\nconst TEARDOWN_TESTING_MODULE_ON_DESTROY_DEFAULT = true;\n/** Whether unknown elements in templates should throw by default. */\nconst THROW_ON_UNKNOWN_ELEMENTS_DEFAULT = false;\n/** Whether unknown properties in templates should throw by default. */\nconst THROW_ON_UNKNOWN_PROPERTIES_DEFAULT = false;\n/** Whether defer blocks should use manual triggering or play through normally. */\nconst DEFER_BLOCK_DEFAULT_BEHAVIOR = ɵDeferBlockBehavior.Playthrough;\n/**\n * An abstract class for inserting the root test component element in a platform independent way.\n *\n * @publicApi\n */\nclass TestComponentRenderer {\n  insertRootElement(rootElementId) {}\n  removeAllRootElements() {}\n}\n/**\n * @publicApi\n */\nconst ComponentFixtureAutoDetect = new InjectionToken('ComponentFixtureAutoDetect');\n/**\n * TODO(atscott): Make public API once we have decided if we want this error and how we want devs to\n * disable it.\n */\nconst AllowDetectChangesAndAcknowledgeItCanHideApplicationBugs = new InjectionToken('AllowDetectChangesAndAcknowledgeItCanHideApplicationBugs');\n/**\n * @publicApi\n */\nconst ComponentFixtureNoNgZone = new InjectionToken('ComponentFixtureNoNgZone');\n\n/**\n * Fixture for debugging and testing a component.\n *\n * @publicApi\n */\nclass ComponentFixture {\n  /** @nodoc */\n  constructor(componentRef) {\n    this.componentRef = componentRef;\n    this._isDestroyed = false;\n    /** @internal */\n    this._noZoneOptionIsSet = inject$1(ComponentFixtureNoNgZone, {\n      optional: true\n    });\n    /** @internal */\n    this._ngZone = this._noZoneOptionIsSet ? new ɵNoopNgZone() : inject$1(NgZone);\n    /** @internal */\n    this._effectRunner = inject$1(ɵEffectScheduler);\n    // Inject ApplicationRef to ensure NgZone stableness causes after render hooks to run\n    // This will likely happen as a result of fixture.detectChanges because it calls ngZone.run\n    // This is a crazy way of doing things but hey, it's the world we live in.\n    // The zoneless scheduler should instead do this more imperatively by attaching\n    // the `ComponentRef` to `ApplicationRef` and calling `appRef.tick` as the `detectChanges`\n    // behavior.\n    /** @internal */\n    this._appRef = inject$1(ApplicationRef);\n    /** @internal */\n    this._testAppRef = this._appRef;\n    // TODO(atscott): Remove this from public API\n    this.ngZone = this._noZoneOptionIsSet ? null : this._ngZone;\n    this.changeDetectorRef = componentRef.changeDetectorRef;\n    this.elementRef = componentRef.location;\n    this.debugElement = getDebugNode(this.elementRef.nativeElement);\n    this.componentInstance = componentRef.instance;\n    this.nativeElement = this.elementRef.nativeElement;\n    this.componentRef = componentRef;\n  }\n  /**\n   * Do a change detection run to make sure there were no changes.\n   */\n  checkNoChanges() {\n    this.changeDetectorRef.checkNoChanges();\n  }\n  /**\n   * Retrieves all defer block fixtures in the component fixture.\n   *\n   * @developerPreview\n   */\n  getDeferBlocks() {\n    const deferBlocks = [];\n    const lView = this.componentRef.hostView['_lView'];\n    ɵgetDeferBlocks(lView, deferBlocks);\n    const deferBlockFixtures = [];\n    for (const block of deferBlocks) {\n      deferBlockFixtures.push(new DeferBlockFixture(block, this));\n    }\n    return Promise.resolve(deferBlockFixtures);\n  }\n  _getRenderer() {\n    if (this._renderer === undefined) {\n      this._renderer = this.componentRef.injector.get(RendererFactory2, null);\n    }\n    return this._renderer;\n  }\n  /**\n   * Get a promise that resolves when the ui state is stable following animations.\n   */\n  whenRenderingDone() {\n    const renderer = this._getRenderer();\n    if (renderer && renderer.whenRenderingDone) {\n      return renderer.whenRenderingDone();\n    }\n    return this.whenStable();\n  }\n  /**\n   * Trigger component destruction.\n   */\n  destroy() {\n    if (!this._isDestroyed) {\n      this.componentRef.destroy();\n      this._isDestroyed = true;\n    }\n  }\n}\n/**\n * ComponentFixture behavior that actually attaches the component to the application to ensure\n * behaviors between fixture and application do not diverge. `detectChanges` is disabled by default\n * (instead, tests should wait for the scheduler to detect changes), `whenStable` is directly the\n * `ApplicationRef.isStable`, and `autoDetectChanges` cannot be disabled.\n */\nclass ScheduledComponentFixture extends ComponentFixture {\n  constructor() {\n    super(...arguments);\n    this.disableDetectChangesError = inject$1(AllowDetectChangesAndAcknowledgeItCanHideApplicationBugs, {\n      optional: true\n    }) ?? false;\n    this.pendingTasks = inject$1(ɵPendingTasks);\n  }\n  initialize() {\n    this._appRef.attachView(this.componentRef.hostView);\n  }\n  detectChanges(checkNoChanges = true) {\n    if (!this.disableDetectChangesError) {\n      throw new Error('Do not use `detectChanges` directly when using zoneless change detection.' + ' Instead, wait for the next render or `fixture.whenStable`.');\n    } else if (!checkNoChanges) {\n      throw new Error('Cannot disable `checkNoChanges` in this configuration. ' + 'Use `fixture.componentRef.hostView.changeDetectorRef.detectChanges()` instead.');\n    }\n    this._effectRunner.flush();\n    this._appRef.tick();\n    this._effectRunner.flush();\n  }\n  isStable() {\n    return !this.pendingTasks.hasPendingTasks.value;\n  }\n  whenStable() {\n    if (this.isStable()) {\n      return Promise.resolve(false);\n    }\n    return this._appRef.isStable.pipe(first(stable => stable)).toPromise().then(() => true);\n  }\n  autoDetectChanges(autoDetect) {\n    throw new Error('Cannot call autoDetectChanges when using change detection scheduling.');\n  }\n}\n/**\n * ComponentFixture behavior that attempts to act as a \"mini application\".\n */\nclass PseudoApplicationComponentFixture extends ComponentFixture {\n  constructor() {\n    super(...arguments);\n    this._subscriptions = new Subscription();\n    this._autoDetect = inject$1(ComponentFixtureAutoDetect, {\n      optional: true\n    }) ?? false;\n    this._isStable = true;\n    this._promise = null;\n    this._resolve = null;\n    this.afterTickSubscription = undefined;\n    this.beforeRenderSubscription = undefined;\n  }\n  initialize() {\n    // Create subscriptions outside the NgZone so that the callbacks run outside\n    // of NgZone.\n    this._ngZone.runOutsideAngular(() => {\n      this._subscriptions.add(this._ngZone.onUnstable.subscribe({\n        next: () => {\n          this._isStable = false;\n        }\n      }));\n      this._subscriptions.add(this._ngZone.onMicrotaskEmpty.subscribe({\n        next: () => {\n          if (this._autoDetect) {\n            // Do a change detection run with checkNoChanges set to true to check\n            // there are no changes on the second run.\n            this.detectChanges(true);\n          }\n        }\n      }));\n      this._subscriptions.add(this._ngZone.onStable.subscribe({\n        next: () => {\n          this._isStable = true;\n          // Check whether there is a pending whenStable() completer to resolve.\n          if (this._promise !== null) {\n            // If so check whether there are no pending macrotasks before resolving.\n            // Do this check in the next tick so that ngZone gets a chance to update the state\n            // of pending macrotasks.\n            queueMicrotask(() => {\n              if (!this._ngZone.hasPendingMacrotasks) {\n                if (this._promise !== null) {\n                  this._resolve(true);\n                  this._resolve = null;\n                  this._promise = null;\n                }\n              }\n            });\n          }\n        }\n      }));\n      this._subscriptions.add(this._ngZone.onError.subscribe({\n        next: error => {\n          throw error;\n        }\n      }));\n    });\n  }\n  detectChanges(checkNoChanges = true) {\n    this._effectRunner.flush();\n    // Run the change detection inside the NgZone so that any async tasks as part of the change\n    // detection are captured by the zone and can be waited for in isStable.\n    this._ngZone.run(() => {\n      this.changeDetectorRef.detectChanges();\n      if (checkNoChanges) {\n        this.checkNoChanges();\n      }\n    });\n    // Run any effects that were created/dirtied during change detection. Such effects might become\n    // dirty in response to input signals changing.\n    this._effectRunner.flush();\n  }\n  isStable() {\n    return this._isStable && !this._ngZone.hasPendingMacrotasks;\n  }\n  whenStable() {\n    if (this.isStable()) {\n      return Promise.resolve(false);\n    } else if (this._promise !== null) {\n      return this._promise;\n    } else {\n      this._promise = new Promise(res => {\n        this._resolve = res;\n      });\n      return this._promise;\n    }\n  }\n  autoDetectChanges(autoDetect = true) {\n    if (this._noZoneOptionIsSet) {\n      throw new Error('Cannot call autoDetectChanges when ComponentFixtureNoNgZone is set.');\n    }\n    this._autoDetect = autoDetect;\n    this.detectChanges();\n  }\n  destroy() {\n    this._subscriptions.unsubscribe();\n    super.destroy();\n  }\n}\nconst _Zone = typeof Zone !== 'undefined' ? Zone : null;\nconst fakeAsyncTestModule = _Zone && _Zone[_Zone.__symbol__('fakeAsyncTest')];\nconst fakeAsyncTestModuleNotLoadedErrorMessage = `zone-testing.js is needed for the fakeAsync() test helper but could not be found.\n        Please make sure that your environment includes zone.js/testing`;\n/**\n * Clears out the shared fake async zone for a test.\n * To be called in a global `beforeEach`.\n *\n * @publicApi\n */\nfunction resetFakeAsyncZone() {\n  if (fakeAsyncTestModule) {\n    return fakeAsyncTestModule.resetFakeAsyncZone();\n  }\n  throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\nfunction resetFakeAsyncZoneIfExists() {\n  if (fakeAsyncTestModule) {\n    fakeAsyncTestModule.resetFakeAsyncZone();\n  }\n}\n/**\n * Wraps a function to be executed in the `fakeAsync` zone:\n * - Microtasks are manually executed by calling `flushMicrotasks()`.\n * - Timers are synchronous; `tick()` simulates the asynchronous passage of time.\n *\n * If there are any pending timers at the end of the function, an exception is thrown.\n *\n * Can be used to wrap `inject()` calls.\n *\n * @param fn The function that you want to wrap in the `fakeAsync` zone.\n *\n * @usageNotes\n * ### Example\n *\n * {@example core/testing/ts/fake_async.ts region='basic'}\n *\n *\n * @returns The function wrapped to be executed in the `fakeAsync` zone.\n * Any arguments passed when calling this returned function will be passed through to the `fn`\n * function in the parameters when it is called.\n *\n * @publicApi\n */\nfunction fakeAsync(fn) {\n  if (fakeAsyncTestModule) {\n    return fakeAsyncTestModule.fakeAsync(fn);\n  }\n  throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\n/**\n * Simulates the asynchronous passage of time for the timers in the `fakeAsync` zone.\n *\n * The microtasks queue is drained at the very start of this function and after any timer callback\n * has been executed.\n *\n * @param millis The number of milliseconds to advance the virtual timer.\n * @param tickOptions The options to pass to the `tick()` function.\n *\n * @usageNotes\n *\n * The `tick()` option is a flag called `processNewMacroTasksSynchronously`,\n * which determines whether or not to invoke new macroTasks.\n *\n * If you provide a `tickOptions` object, but do not specify a\n * `processNewMacroTasksSynchronously` property (`tick(100, {})`),\n * then `processNewMacroTasksSynchronously` defaults to true.\n *\n * If you omit the `tickOptions` parameter (`tick(100))`), then\n * `tickOptions` defaults to `{processNewMacroTasksSynchronously: true}`.\n *\n * ### Example\n *\n * {@example core/testing/ts/fake_async.ts region='basic'}\n *\n * The following example includes a nested timeout (new macroTask), and\n * the `tickOptions` parameter is allowed to default. In this case,\n * `processNewMacroTasksSynchronously` defaults to true, and the nested\n * function is executed on each tick.\n *\n * ```\n * it ('test with nested setTimeout', fakeAsync(() => {\n *   let nestedTimeoutInvoked = false;\n *   function funcWithNestedTimeout() {\n *     setTimeout(() => {\n *       nestedTimeoutInvoked = true;\n *     });\n *   };\n *   setTimeout(funcWithNestedTimeout);\n *   tick();\n *   expect(nestedTimeoutInvoked).toBe(true);\n * }));\n * ```\n *\n * In the following case, `processNewMacroTasksSynchronously` is explicitly\n * set to false, so the nested timeout function is not invoked.\n *\n * ```\n * it ('test with nested setTimeout', fakeAsync(() => {\n *   let nestedTimeoutInvoked = false;\n *   function funcWithNestedTimeout() {\n *     setTimeout(() => {\n *       nestedTimeoutInvoked = true;\n *     });\n *   };\n *   setTimeout(funcWithNestedTimeout);\n *   tick(0, {processNewMacroTasksSynchronously: false});\n *   expect(nestedTimeoutInvoked).toBe(false);\n * }));\n * ```\n *\n *\n * @publicApi\n */\nfunction tick(millis = 0, tickOptions = {\n  processNewMacroTasksSynchronously: true\n}) {\n  if (fakeAsyncTestModule) {\n    return fakeAsyncTestModule.tick(millis, tickOptions);\n  }\n  throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\n/**\n * Flushes any pending microtasks and simulates the asynchronous passage of time for the timers in\n * the `fakeAsync` zone by\n * draining the macrotask queue until it is empty.\n *\n * @param maxTurns The maximum number of times the scheduler attempts to clear its queue before\n *     throwing an error.\n * @returns The simulated time elapsed, in milliseconds.\n *\n * @publicApi\n */\nfunction flush(maxTurns) {\n  if (fakeAsyncTestModule) {\n    return fakeAsyncTestModule.flush(maxTurns);\n  }\n  throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\n/**\n * Discard all remaining periodic tasks.\n *\n * @publicApi\n */\nfunction discardPeriodicTasks() {\n  if (fakeAsyncTestModule) {\n    return fakeAsyncTestModule.discardPeriodicTasks();\n  }\n  throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\n/**\n * Flush any pending microtasks.\n *\n * @publicApi\n */\nfunction flushMicrotasks() {\n  if (fakeAsyncTestModule) {\n    return fakeAsyncTestModule.flushMicrotasks();\n  }\n  throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\nlet _nextReferenceId = 0;\nclass MetadataOverrider {\n  constructor() {\n    this._references = new Map();\n  }\n  /**\n   * Creates a new instance for the given metadata class\n   * based on an old instance and overrides.\n   */\n  overrideMetadata(metadataClass, oldMetadata, override) {\n    const props = {};\n    if (oldMetadata) {\n      _valueProps(oldMetadata).forEach(prop => props[prop] = oldMetadata[prop]);\n    }\n    if (override.set) {\n      if (override.remove || override.add) {\n        throw new Error(`Cannot set and add/remove ${ɵstringify(metadataClass)} at the same time!`);\n      }\n      setMetadata(props, override.set);\n    }\n    if (override.remove) {\n      removeMetadata(props, override.remove, this._references);\n    }\n    if (override.add) {\n      addMetadata(props, override.add);\n    }\n    return new metadataClass(props);\n  }\n}\nfunction removeMetadata(metadata, remove, references) {\n  const removeObjects = new Set();\n  for (const prop in remove) {\n    const removeValue = remove[prop];\n    if (Array.isArray(removeValue)) {\n      removeValue.forEach(value => {\n        removeObjects.add(_propHashKey(prop, value, references));\n      });\n    } else {\n      removeObjects.add(_propHashKey(prop, removeValue, references));\n    }\n  }\n  for (const prop in metadata) {\n    const propValue = metadata[prop];\n    if (Array.isArray(propValue)) {\n      metadata[prop] = propValue.filter(value => !removeObjects.has(_propHashKey(prop, value, references)));\n    } else {\n      if (removeObjects.has(_propHashKey(prop, propValue, references))) {\n        metadata[prop] = undefined;\n      }\n    }\n  }\n}\nfunction addMetadata(metadata, add) {\n  for (const prop in add) {\n    const addValue = add[prop];\n    const propValue = metadata[prop];\n    if (propValue != null && Array.isArray(propValue)) {\n      metadata[prop] = propValue.concat(addValue);\n    } else {\n      metadata[prop] = addValue;\n    }\n  }\n}\nfunction setMetadata(metadata, set) {\n  for (const prop in set) {\n    metadata[prop] = set[prop];\n  }\n}\nfunction _propHashKey(propName, propValue, references) {\n  let nextObjectId = 0;\n  const objectIds = new Map();\n  const replacer = (key, value) => {\n    if (value !== null && typeof value === 'object') {\n      if (objectIds.has(value)) {\n        return objectIds.get(value);\n      }\n      // Record an id for this object such that any later references use the object's id instead\n      // of the object itself, in order to break cyclic pointers in objects.\n      objectIds.set(value, `ɵobj#${nextObjectId++}`);\n      // The first time an object is seen the object itself is serialized.\n      return value;\n    } else if (typeof value === 'function') {\n      value = _serializeReference(value, references);\n    }\n    return value;\n  };\n  return `${propName}:${JSON.stringify(propValue, replacer)}`;\n}\nfunction _serializeReference(ref, references) {\n  let id = references.get(ref);\n  if (!id) {\n    id = `${ɵstringify(ref)}${_nextReferenceId++}`;\n    references.set(ref, id);\n  }\n  return id;\n}\nfunction _valueProps(obj) {\n  const props = [];\n  // regular public props\n  Object.keys(obj).forEach(prop => {\n    if (!prop.startsWith('_')) {\n      props.push(prop);\n    }\n  });\n  // getters\n  let proto = obj;\n  while (proto = Object.getPrototypeOf(proto)) {\n    Object.keys(proto).forEach(protoProp => {\n      const desc = Object.getOwnPropertyDescriptor(proto, protoProp);\n      if (!protoProp.startsWith('_') && desc && 'get' in desc) {\n        props.push(protoProp);\n      }\n    });\n  }\n  return props;\n}\nconst reflection = new ɵReflectionCapabilities();\n/**\n * Allows to override ivy metadata for tests (via the `TestBed`).\n */\nclass OverrideResolver {\n  constructor() {\n    this.overrides = new Map();\n    this.resolved = new Map();\n  }\n  addOverride(type, override) {\n    const overrides = this.overrides.get(type) || [];\n    overrides.push(override);\n    this.overrides.set(type, overrides);\n    this.resolved.delete(type);\n  }\n  setOverrides(overrides) {\n    this.overrides.clear();\n    overrides.forEach(([type, override]) => {\n      this.addOverride(type, override);\n    });\n  }\n  getAnnotation(type) {\n    const annotations = reflection.annotations(type);\n    // Try to find the nearest known Type annotation and make sure that this annotation is an\n    // instance of the type we are looking for, so we can use it for resolution. Note: there might\n    // be multiple known annotations found due to the fact that Components can extend Directives (so\n    // both Directive and Component annotations would be present), so we always check if the known\n    // annotation has the right type.\n    for (let i = annotations.length - 1; i >= 0; i--) {\n      const annotation = annotations[i];\n      const isKnownType = annotation instanceof Directive || annotation instanceof Component || annotation instanceof Pipe || annotation instanceof NgModule;\n      if (isKnownType) {\n        return annotation instanceof this.type ? annotation : null;\n      }\n    }\n    return null;\n  }\n  resolve(type) {\n    let resolved = this.resolved.get(type) || null;\n    if (!resolved) {\n      resolved = this.getAnnotation(type);\n      if (resolved) {\n        const overrides = this.overrides.get(type);\n        if (overrides) {\n          const overrider = new MetadataOverrider();\n          overrides.forEach(override => {\n            resolved = overrider.overrideMetadata(this.type, resolved, override);\n          });\n        }\n      }\n      this.resolved.set(type, resolved);\n    }\n    return resolved;\n  }\n}\nclass DirectiveResolver extends OverrideResolver {\n  get type() {\n    return Directive;\n  }\n}\nclass ComponentResolver extends OverrideResolver {\n  get type() {\n    return Component;\n  }\n}\nclass PipeResolver extends OverrideResolver {\n  get type() {\n    return Pipe;\n  }\n}\nclass NgModuleResolver extends OverrideResolver {\n  get type() {\n    return NgModule;\n  }\n}\nvar TestingModuleOverride;\n(function (TestingModuleOverride) {\n  TestingModuleOverride[TestingModuleOverride[\"DECLARATION\"] = 0] = \"DECLARATION\";\n  TestingModuleOverride[TestingModuleOverride[\"OVERRIDE_TEMPLATE\"] = 1] = \"OVERRIDE_TEMPLATE\";\n})(TestingModuleOverride || (TestingModuleOverride = {}));\nfunction isTestingModuleOverride(value) {\n  return value === TestingModuleOverride.DECLARATION || value === TestingModuleOverride.OVERRIDE_TEMPLATE;\n}\nfunction assertNoStandaloneComponents(types, resolver, location) {\n  types.forEach(type => {\n    if (!ɵgetAsyncClassMetadataFn(type)) {\n      const component = resolver.resolve(type);\n      if (component && component.standalone) {\n        throw new Error(ɵgenerateStandaloneInDeclarationsError(type, location));\n      }\n    }\n  });\n}\nclass TestBedCompiler {\n  constructor(platform, additionalModuleTypes) {\n    this.platform = platform;\n    this.additionalModuleTypes = additionalModuleTypes;\n    this.originalComponentResolutionQueue = null;\n    // Testing module configuration\n    this.declarations = [];\n    this.imports = [];\n    this.providers = [];\n    this.schemas = [];\n    // Queues of components/directives/pipes that should be recompiled.\n    this.pendingComponents = new Set();\n    this.pendingDirectives = new Set();\n    this.pendingPipes = new Set();\n    // Set of components with async metadata, i.e. components with `@defer` blocks\n    // in their templates.\n    this.componentsWithAsyncMetadata = new Set();\n    // Keep track of all components and directives, so we can patch Providers onto defs later.\n    this.seenComponents = new Set();\n    this.seenDirectives = new Set();\n    // Keep track of overridden modules, so that we can collect all affected ones in the module tree.\n    this.overriddenModules = new Set();\n    // Store resolved styles for Components that have template overrides present and `styleUrls`\n    // defined at the same time.\n    this.existingComponentStyles = new Map();\n    this.resolvers = initResolvers();\n    // Map of component type to an NgModule that declares it.\n    //\n    // There are a couple special cases:\n    // - for standalone components, the module scope value is `null`\n    // - when a component is declared in `TestBed.configureTestingModule()` call or\n    //   a component's template is overridden via `TestBed.overrideTemplateUsingTestingModule()`.\n    //   we use a special value from the `TestingModuleOverride` enum.\n    this.componentToModuleScope = new Map();\n    // Map that keeps initial version of component/directive/pipe defs in case\n    // we compile a Type again, thus overriding respective static fields. This is\n    // required to make sure we restore defs to their initial states between test runs.\n    // Note: one class may have multiple defs (for example: ɵmod and ɵinj in case of an\n    // NgModule), store all of them in a map.\n    this.initialNgDefs = new Map();\n    // Array that keeps cleanup operations for initial versions of component/directive/pipe/module\n    // defs in case TestBed makes changes to the originals.\n    this.defCleanupOps = [];\n    this._injector = null;\n    this.compilerProviders = null;\n    this.providerOverrides = [];\n    this.rootProviderOverrides = [];\n    // Overrides for injectables with `{providedIn: SomeModule}` need to be tracked and added to that\n    // module's provider list.\n    this.providerOverridesByModule = new Map();\n    this.providerOverridesByToken = new Map();\n    this.scopesWithOverriddenProviders = new Set();\n    this.testModuleRef = null;\n    this.deferBlockBehavior = DEFER_BLOCK_DEFAULT_BEHAVIOR;\n    class DynamicTestModule {}\n    this.testModuleType = DynamicTestModule;\n  }\n  setCompilerProviders(providers) {\n    this.compilerProviders = providers;\n    this._injector = null;\n  }\n  configureTestingModule(moduleDef) {\n    // Enqueue any compilation tasks for the directly declared component.\n    if (moduleDef.declarations !== undefined) {\n      // Verify that there are no standalone components\n      assertNoStandaloneComponents(moduleDef.declarations, this.resolvers.component, '\"TestBed.configureTestingModule\" call');\n      this.queueTypeArray(moduleDef.declarations, TestingModuleOverride.DECLARATION);\n      this.declarations.push(...moduleDef.declarations);\n    }\n    // Enqueue any compilation tasks for imported modules.\n    if (moduleDef.imports !== undefined) {\n      this.queueTypesFromModulesArray(moduleDef.imports);\n      this.imports.push(...moduleDef.imports);\n    }\n    if (moduleDef.providers !== undefined) {\n      this.providers.push(...moduleDef.providers);\n    }\n    if (moduleDef.schemas !== undefined) {\n      this.schemas.push(...moduleDef.schemas);\n    }\n    this.deferBlockBehavior = moduleDef.deferBlockBehavior ?? DEFER_BLOCK_DEFAULT_BEHAVIOR;\n  }\n  overrideModule(ngModule, override) {\n    if (ɵUSE_RUNTIME_DEPS_TRACKER_FOR_JIT) {\n      ɵdepsTracker.clearScopeCacheFor(ngModule);\n    }\n    this.overriddenModules.add(ngModule);\n    // Compile the module right away.\n    this.resolvers.module.addOverride(ngModule, override);\n    const metadata = this.resolvers.module.resolve(ngModule);\n    if (metadata === null) {\n      throw invalidTypeError(ngModule.name, 'NgModule');\n    }\n    this.recompileNgModule(ngModule, metadata);\n    // At this point, the module has a valid module def (ɵmod), but the override may have introduced\n    // new declarations or imported modules. Ingest any possible new types and add them to the\n    // current queue.\n    this.queueTypesFromModulesArray([ngModule]);\n  }\n  overrideComponent(component, override) {\n    this.verifyNoStandaloneFlagOverrides(component, override);\n    this.resolvers.component.addOverride(component, override);\n    this.pendingComponents.add(component);\n    // If this is a component with async metadata (i.e. a component with a `@defer` block\n    // in a template) - store it for future processing.\n    this.maybeRegisterComponentWithAsyncMetadata(component);\n  }\n  overrideDirective(directive, override) {\n    this.verifyNoStandaloneFlagOverrides(directive, override);\n    this.resolvers.directive.addOverride(directive, override);\n    this.pendingDirectives.add(directive);\n  }\n  overridePipe(pipe, override) {\n    this.verifyNoStandaloneFlagOverrides(pipe, override);\n    this.resolvers.pipe.addOverride(pipe, override);\n    this.pendingPipes.add(pipe);\n  }\n  verifyNoStandaloneFlagOverrides(type, override) {\n    if (override.add?.hasOwnProperty('standalone') || override.set?.hasOwnProperty('standalone') || override.remove?.hasOwnProperty('standalone')) {\n      throw new Error(`An override for the ${type.name} class has the \\`standalone\\` flag. ` + `Changing the \\`standalone\\` flag via TestBed overrides is not supported.`);\n    }\n  }\n  overrideProvider(token, provider) {\n    let providerDef;\n    if (provider.useFactory !== undefined) {\n      providerDef = {\n        provide: token,\n        useFactory: provider.useFactory,\n        deps: provider.deps || [],\n        multi: provider.multi\n      };\n    } else if (provider.useValue !== undefined) {\n      providerDef = {\n        provide: token,\n        useValue: provider.useValue,\n        multi: provider.multi\n      };\n    } else {\n      providerDef = {\n        provide: token\n      };\n    }\n    const injectableDef = typeof token !== 'string' ? ɵgetInjectableDef(token) : null;\n    const providedIn = injectableDef === null ? null : resolveForwardRef(injectableDef.providedIn);\n    const overridesBucket = providedIn === 'root' ? this.rootProviderOverrides : this.providerOverrides;\n    overridesBucket.push(providerDef);\n    // Keep overrides grouped by token as well for fast lookups using token\n    this.providerOverridesByToken.set(token, providerDef);\n    if (injectableDef !== null && providedIn !== null && typeof providedIn !== 'string') {\n      const existingOverrides = this.providerOverridesByModule.get(providedIn);\n      if (existingOverrides !== undefined) {\n        existingOverrides.push(providerDef);\n      } else {\n        this.providerOverridesByModule.set(providedIn, [providerDef]);\n      }\n    }\n  }\n  overrideTemplateUsingTestingModule(type, template) {\n    const def = type[ɵNG_COMP_DEF];\n    const hasStyleUrls = () => {\n      const metadata = this.resolvers.component.resolve(type);\n      return !!metadata.styleUrl || !!metadata.styleUrls?.length;\n    };\n    const overrideStyleUrls = !!def && !ɵisComponentDefPendingResolution(type) && hasStyleUrls();\n    // In Ivy, compiling a component does not require knowing the module providing the\n    // component's scope, so overrideTemplateUsingTestingModule can be implemented purely via\n    // overrideComponent. Important: overriding template requires full Component re-compilation,\n    // which may fail in case styleUrls are also present (thus Component is considered as required\n    // resolution). In order to avoid this, we preemptively set styleUrls to an empty array,\n    // preserve current styles available on Component def and restore styles back once compilation\n    // is complete.\n    const override = overrideStyleUrls ? {\n      template,\n      styles: [],\n      styleUrls: [],\n      styleUrl: undefined\n    } : {\n      template\n    };\n    this.overrideComponent(type, {\n      set: override\n    });\n    if (overrideStyleUrls && def.styles && def.styles.length > 0) {\n      this.existingComponentStyles.set(type, def.styles);\n    }\n    // Set the component's scope to be the testing module.\n    this.componentToModuleScope.set(type, TestingModuleOverride.OVERRIDE_TEMPLATE);\n  }\n  resolvePendingComponentsWithAsyncMetadata() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (_this2.componentsWithAsyncMetadata.size === 0) return;\n      const promises = [];\n      for (const component of _this2.componentsWithAsyncMetadata) {\n        const asyncMetadataFn = ɵgetAsyncClassMetadataFn(component);\n        if (asyncMetadataFn) {\n          promises.push(asyncMetadataFn());\n        }\n      }\n      _this2.componentsWithAsyncMetadata.clear();\n      const resolvedDeps = yield Promise.all(promises);\n      const flatResolvedDeps = resolvedDeps.flat(2);\n      _this2.queueTypesFromModulesArray(flatResolvedDeps);\n      // Loaded standalone components might contain imports of NgModules\n      // with providers, make sure we override providers there too.\n      for (const component of flatResolvedDeps) {\n        _this2.applyProviderOverridesInScope(component);\n      }\n    })();\n  }\n  compileComponents() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      _this3.clearComponentResolutionQueue();\n      // Wait for all async metadata for components that were\n      // overridden, we need resolved metadata to perform an override\n      // and re-compile a component.\n      yield _this3.resolvePendingComponentsWithAsyncMetadata();\n      // Verify that there were no standalone components present in the `declarations` field\n      // during the `TestBed.configureTestingModule` call. We perform this check here in addition\n      // to the logic in the `configureTestingModule` function, since at this point we have\n      // all async metadata resolved.\n      assertNoStandaloneComponents(_this3.declarations, _this3.resolvers.component, '\"TestBed.configureTestingModule\" call');\n      // Run compilers for all queued types.\n      let needsAsyncResources = _this3.compileTypesSync();\n      // compileComponents() should not be async unless it needs to be.\n      if (needsAsyncResources) {\n        let resourceLoader;\n        let resolver = url => {\n          if (!resourceLoader) {\n            resourceLoader = _this3.injector.get(ResourceLoader);\n          }\n          return Promise.resolve(resourceLoader.get(url));\n        };\n        yield ɵresolveComponentResources(resolver);\n      }\n    })();\n  }\n  finalize() {\n    // One last compile\n    this.compileTypesSync();\n    // Create the testing module itself.\n    this.compileTestModule();\n    this.applyTransitiveScopes();\n    this.applyProviderOverrides();\n    // Patch previously stored `styles` Component values (taken from ɵcmp), in case these\n    // Components have `styleUrls` fields defined and template override was requested.\n    this.patchComponentsWithExistingStyles();\n    // Clear the componentToModuleScope map, so that future compilations don't reset the scope of\n    // every component.\n    this.componentToModuleScope.clear();\n    const parentInjector = this.platform.injector;\n    this.testModuleRef = new ɵRender3NgModuleRef(this.testModuleType, parentInjector, []);\n    // ApplicationInitStatus.runInitializers() is marked @internal to core.\n    // Cast it to any before accessing it.\n    this.testModuleRef.injector.get(ApplicationInitStatus).runInitializers();\n    // Set locale ID after running app initializers, since locale information might be updated while\n    // running initializers. This is also consistent with the execution order while bootstrapping an\n    // app (see `packages/core/src/application_ref.ts` file).\n    const localeId = this.testModuleRef.injector.get(LOCALE_ID, ɵDEFAULT_LOCALE_ID);\n    ɵsetLocaleId(localeId);\n    return this.testModuleRef;\n  }\n  /**\n   * @internal\n   */\n  _compileNgModuleSync(moduleType) {\n    this.queueTypesFromModulesArray([moduleType]);\n    this.compileTypesSync();\n    this.applyProviderOverrides();\n    this.applyProviderOverridesInScope(moduleType);\n    this.applyTransitiveScopes();\n  }\n  /**\n   * @internal\n   */\n  _compileNgModuleAsync(moduleType) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      _this4.queueTypesFromModulesArray([moduleType]);\n      yield _this4.compileComponents();\n      _this4.applyProviderOverrides();\n      _this4.applyProviderOverridesInScope(moduleType);\n      _this4.applyTransitiveScopes();\n    })();\n  }\n  /**\n   * @internal\n   */\n  _getModuleResolver() {\n    return this.resolvers.module;\n  }\n  /**\n   * @internal\n   */\n  _getComponentFactories(moduleType) {\n    return maybeUnwrapFn(moduleType.ɵmod.declarations).reduce((factories, declaration) => {\n      const componentDef = declaration.ɵcmp;\n      componentDef && factories.push(new ɵRender3ComponentFactory(componentDef, this.testModuleRef));\n      return factories;\n    }, []);\n  }\n  compileTypesSync() {\n    // Compile all queued components, directives, pipes.\n    let needsAsyncResources = false;\n    this.pendingComponents.forEach(declaration => {\n      if (ɵgetAsyncClassMetadataFn(declaration)) {\n        throw new Error(`Component '${declaration.name}' has unresolved metadata. ` + `Please call \\`await TestBed.compileComponents()\\` before running this test.`);\n      }\n      needsAsyncResources = needsAsyncResources || ɵisComponentDefPendingResolution(declaration);\n      const metadata = this.resolvers.component.resolve(declaration);\n      if (metadata === null) {\n        throw invalidTypeError(declaration.name, 'Component');\n      }\n      this.maybeStoreNgDef(ɵNG_COMP_DEF, declaration);\n      if (ɵUSE_RUNTIME_DEPS_TRACKER_FOR_JIT) {\n        ɵdepsTracker.clearScopeCacheFor(declaration);\n      }\n      ɵcompileComponent(declaration, metadata);\n    });\n    this.pendingComponents.clear();\n    this.pendingDirectives.forEach(declaration => {\n      const metadata = this.resolvers.directive.resolve(declaration);\n      if (metadata === null) {\n        throw invalidTypeError(declaration.name, 'Directive');\n      }\n      this.maybeStoreNgDef(ɵNG_DIR_DEF, declaration);\n      ɵcompileDirective(declaration, metadata);\n    });\n    this.pendingDirectives.clear();\n    this.pendingPipes.forEach(declaration => {\n      const metadata = this.resolvers.pipe.resolve(declaration);\n      if (metadata === null) {\n        throw invalidTypeError(declaration.name, 'Pipe');\n      }\n      this.maybeStoreNgDef(ɵNG_PIPE_DEF, declaration);\n      ɵcompilePipe(declaration, metadata);\n    });\n    this.pendingPipes.clear();\n    return needsAsyncResources;\n  }\n  applyTransitiveScopes() {\n    if (this.overriddenModules.size > 0) {\n      // Module overrides (via `TestBed.overrideModule`) might affect scopes that were previously\n      // calculated and stored in `transitiveCompileScopes`. If module overrides are present,\n      // collect all affected modules and reset scopes to force their re-calculation.\n      const testingModuleDef = this.testModuleType[ɵNG_MOD_DEF];\n      const affectedModules = this.collectModulesAffectedByOverrides(testingModuleDef.imports);\n      if (affectedModules.size > 0) {\n        affectedModules.forEach(moduleType => {\n          if (!ɵUSE_RUNTIME_DEPS_TRACKER_FOR_JIT) {\n            this.storeFieldOfDefOnType(moduleType, ɵNG_MOD_DEF, 'transitiveCompileScopes');\n            moduleType[ɵNG_MOD_DEF].transitiveCompileScopes = null;\n          } else {\n            ɵdepsTracker.clearScopeCacheFor(moduleType);\n          }\n        });\n      }\n    }\n    const moduleToScope = new Map();\n    const getScopeOfModule = moduleType => {\n      if (!moduleToScope.has(moduleType)) {\n        const isTestingModule = isTestingModuleOverride(moduleType);\n        const realType = isTestingModule ? this.testModuleType : moduleType;\n        moduleToScope.set(moduleType, ɵtransitiveScopesFor(realType));\n      }\n      return moduleToScope.get(moduleType);\n    };\n    this.componentToModuleScope.forEach((moduleType, componentType) => {\n      if (moduleType !== null) {\n        const moduleScope = getScopeOfModule(moduleType);\n        this.storeFieldOfDefOnType(componentType, ɵNG_COMP_DEF, 'directiveDefs');\n        this.storeFieldOfDefOnType(componentType, ɵNG_COMP_DEF, 'pipeDefs');\n        ɵpatchComponentDefWithScope(getComponentDef(componentType), moduleScope);\n      }\n      // `tView` that is stored on component def contains information about directives and pipes\n      // that are in the scope of this component. Patching component scope will cause `tView` to be\n      // changed. Store original `tView` before patching scope, so the `tView` (including scope\n      // information) is restored back to its previous/original state before running next test.\n      // Resetting `tView` is also needed for cases when we apply provider overrides and those\n      // providers are defined on component's level, in which case they may end up included into\n      // `tView.blueprint`.\n      this.storeFieldOfDefOnType(componentType, ɵNG_COMP_DEF, 'tView');\n    });\n    this.componentToModuleScope.clear();\n  }\n  applyProviderOverrides() {\n    const maybeApplyOverrides = field => type => {\n      const resolver = field === ɵNG_COMP_DEF ? this.resolvers.component : this.resolvers.directive;\n      const metadata = resolver.resolve(type);\n      if (this.hasProviderOverrides(metadata.providers)) {\n        this.patchDefWithProviderOverrides(type, field);\n      }\n    };\n    this.seenComponents.forEach(maybeApplyOverrides(ɵNG_COMP_DEF));\n    this.seenDirectives.forEach(maybeApplyOverrides(ɵNG_DIR_DEF));\n    this.seenComponents.clear();\n    this.seenDirectives.clear();\n  }\n  /**\n   * Applies provider overrides to a given type (either an NgModule or a standalone component)\n   * and all imported NgModules and standalone components recursively.\n   */\n  applyProviderOverridesInScope(type) {\n    const hasScope = isStandaloneComponent(type) || isNgModule(type);\n    // The function can be re-entered recursively while inspecting dependencies\n    // of an NgModule or a standalone component. Exit early if we come across a\n    // type that can not have a scope (directive or pipe) or the type is already\n    // processed earlier.\n    if (!hasScope || this.scopesWithOverriddenProviders.has(type)) {\n      return;\n    }\n    this.scopesWithOverriddenProviders.add(type);\n    // NOTE: the line below triggers JIT compilation of the module injector,\n    // which also invokes verification of the NgModule semantics, which produces\n    // detailed error messages. The fact that the code relies on this line being\n    // present here is suspicious and should be refactored in a way that the line\n    // below can be moved (for ex. after an early exit check below).\n    const injectorDef = type[ɵNG_INJ_DEF];\n    // No provider overrides, exit early.\n    if (this.providerOverridesByToken.size === 0) return;\n    if (isStandaloneComponent(type)) {\n      // Visit all component dependencies and override providers there.\n      const def = getComponentDef(type);\n      const dependencies = maybeUnwrapFn(def.dependencies ?? []);\n      for (const dependency of dependencies) {\n        this.applyProviderOverridesInScope(dependency);\n      }\n    } else {\n      const providers = [...injectorDef.providers, ...(this.providerOverridesByModule.get(type) || [])];\n      if (this.hasProviderOverrides(providers)) {\n        this.maybeStoreNgDef(ɵNG_INJ_DEF, type);\n        this.storeFieldOfDefOnType(type, ɵNG_INJ_DEF, 'providers');\n        injectorDef.providers = this.getOverriddenProviders(providers);\n      }\n      // Apply provider overrides to imported modules recursively\n      const moduleDef = type[ɵNG_MOD_DEF];\n      const imports = maybeUnwrapFn(moduleDef.imports);\n      for (const importedModule of imports) {\n        this.applyProviderOverridesInScope(importedModule);\n      }\n      // Also override the providers on any ModuleWithProviders imports since those don't appear in\n      // the moduleDef.\n      for (const importedModule of flatten(injectorDef.imports)) {\n        if (isModuleWithProviders(importedModule)) {\n          this.defCleanupOps.push({\n            object: importedModule,\n            fieldName: 'providers',\n            originalValue: importedModule.providers\n          });\n          importedModule.providers = this.getOverriddenProviders(importedModule.providers);\n        }\n      }\n    }\n  }\n  patchComponentsWithExistingStyles() {\n    this.existingComponentStyles.forEach((styles, type) => type[ɵNG_COMP_DEF].styles = styles);\n    this.existingComponentStyles.clear();\n  }\n  queueTypeArray(arr, moduleType) {\n    for (const value of arr) {\n      if (Array.isArray(value)) {\n        this.queueTypeArray(value, moduleType);\n      } else {\n        this.queueType(value, moduleType);\n      }\n    }\n  }\n  recompileNgModule(ngModule, metadata) {\n    // Cache the initial ngModuleDef as it will be overwritten.\n    this.maybeStoreNgDef(ɵNG_MOD_DEF, ngModule);\n    this.maybeStoreNgDef(ɵNG_INJ_DEF, ngModule);\n    ɵcompileNgModuleDefs(ngModule, metadata);\n  }\n  maybeRegisterComponentWithAsyncMetadata(type) {\n    const asyncMetadataFn = ɵgetAsyncClassMetadataFn(type);\n    if (asyncMetadataFn) {\n      this.componentsWithAsyncMetadata.add(type);\n    }\n  }\n  queueType(type, moduleType) {\n    // If this is a component with async metadata (i.e. a component with a `@defer` block\n    // in a template) - store it for future processing.\n    this.maybeRegisterComponentWithAsyncMetadata(type);\n    const component = this.resolvers.component.resolve(type);\n    if (component) {\n      // Check whether a give Type has respective NG def (ɵcmp) and compile if def is\n      // missing. That might happen in case a class without any Angular decorators extends another\n      // class where Component/Directive/Pipe decorator is defined.\n      if (ɵisComponentDefPendingResolution(type) || !type.hasOwnProperty(ɵNG_COMP_DEF)) {\n        this.pendingComponents.add(type);\n      }\n      this.seenComponents.add(type);\n      // Keep track of the module which declares this component, so later the component's scope\n      // can be set correctly. If the component has already been recorded here, then one of several\n      // cases is true:\n      // * the module containing the component was imported multiple times (common).\n      // * the component is declared in multiple modules (which is an error).\n      // * the component was in 'declarations' of the testing module, and also in an imported module\n      //   in which case the module scope will be TestingModuleOverride.DECLARATION.\n      // * overrideTemplateUsingTestingModule was called for the component in which case the module\n      //   scope will be TestingModuleOverride.OVERRIDE_TEMPLATE.\n      //\n      // If the component was previously in the testing module's 'declarations' (meaning the\n      // current value is TestingModuleOverride.DECLARATION), then `moduleType` is the component's\n      // real module, which was imported. This pattern is understood to mean that the component\n      // should use its original scope, but that the testing module should also contain the\n      // component in its scope.\n      if (!this.componentToModuleScope.has(type) || this.componentToModuleScope.get(type) === TestingModuleOverride.DECLARATION) {\n        this.componentToModuleScope.set(type, moduleType);\n      }\n      return;\n    }\n    const directive = this.resolvers.directive.resolve(type);\n    if (directive) {\n      if (!type.hasOwnProperty(ɵNG_DIR_DEF)) {\n        this.pendingDirectives.add(type);\n      }\n      this.seenDirectives.add(type);\n      return;\n    }\n    const pipe = this.resolvers.pipe.resolve(type);\n    if (pipe && !type.hasOwnProperty(ɵNG_PIPE_DEF)) {\n      this.pendingPipes.add(type);\n      return;\n    }\n  }\n  queueTypesFromModulesArray(arr) {\n    // Because we may encounter the same NgModule or a standalone Component while processing\n    // the dependencies of an NgModule or a standalone Component, we cache them in this set so we\n    // can skip ones that have already been seen encountered. In some test setups, this caching\n    // resulted in 10X runtime improvement.\n    const processedDefs = new Set();\n    const queueTypesFromModulesArrayRecur = arr => {\n      for (const value of arr) {\n        if (Array.isArray(value)) {\n          queueTypesFromModulesArrayRecur(value);\n        } else if (hasNgModuleDef(value)) {\n          const def = value.ɵmod;\n          if (processedDefs.has(def)) {\n            continue;\n          }\n          processedDefs.add(def);\n          // Look through declarations, imports, and exports, and queue\n          // everything found there.\n          this.queueTypeArray(maybeUnwrapFn(def.declarations), value);\n          queueTypesFromModulesArrayRecur(maybeUnwrapFn(def.imports));\n          queueTypesFromModulesArrayRecur(maybeUnwrapFn(def.exports));\n        } else if (isModuleWithProviders(value)) {\n          queueTypesFromModulesArrayRecur([value.ngModule]);\n        } else if (isStandaloneComponent(value)) {\n          this.queueType(value, null);\n          const def = getComponentDef(value);\n          if (processedDefs.has(def)) {\n            continue;\n          }\n          processedDefs.add(def);\n          const dependencies = maybeUnwrapFn(def.dependencies ?? []);\n          dependencies.forEach(dependency => {\n            // Note: in AOT, the `dependencies` might also contain regular\n            // (NgModule-based) Component, Directive and Pipes, so we handle\n            // them separately and proceed with recursive process for standalone\n            // Components and NgModules only.\n            if (isStandaloneComponent(dependency) || hasNgModuleDef(dependency)) {\n              queueTypesFromModulesArrayRecur([dependency]);\n            } else {\n              this.queueType(dependency, null);\n            }\n          });\n        }\n      }\n    };\n    queueTypesFromModulesArrayRecur(arr);\n  }\n  // When module overrides (via `TestBed.overrideModule`) are present, it might affect all modules\n  // that import (even transitively) an overridden one. For all affected modules we need to\n  // recalculate their scopes for a given test run and restore original scopes at the end. The goal\n  // of this function is to collect all affected modules in a set for further processing. Example:\n  // if we have the following module hierarchy: A -> B -> C (where `->` means `imports`) and module\n  // `C` is overridden, we consider `A` and `B` as affected, since their scopes might become\n  // invalidated with the override.\n  collectModulesAffectedByOverrides(arr) {\n    const seenModules = new Set();\n    const affectedModules = new Set();\n    const calcAffectedModulesRecur = (arr, path) => {\n      for (const value of arr) {\n        if (Array.isArray(value)) {\n          // If the value is an array, just flatten it (by invoking this function recursively),\n          // keeping \"path\" the same.\n          calcAffectedModulesRecur(value, path);\n        } else if (hasNgModuleDef(value)) {\n          if (seenModules.has(value)) {\n            // If we've seen this module before and it's included into \"affected modules\" list, mark\n            // the whole path that leads to that module as affected, but do not descend into its\n            // imports, since we already examined them before.\n            if (affectedModules.has(value)) {\n              path.forEach(item => affectedModules.add(item));\n            }\n            continue;\n          }\n          seenModules.add(value);\n          if (this.overriddenModules.has(value)) {\n            path.forEach(item => affectedModules.add(item));\n          }\n          // Examine module imports recursively to look for overridden modules.\n          const moduleDef = value[ɵNG_MOD_DEF];\n          calcAffectedModulesRecur(maybeUnwrapFn(moduleDef.imports), path.concat(value));\n        }\n      }\n    };\n    calcAffectedModulesRecur(arr, []);\n    return affectedModules;\n  }\n  /**\n   * Preserve an original def (such as ɵmod, ɵinj, etc) before applying an override.\n   * Note: one class may have multiple defs (for example: ɵmod and ɵinj in case of\n   * an NgModule). If there is a def in a set already, don't override it, since\n   * an original one should be restored at the end of a test.\n   */\n  maybeStoreNgDef(prop, type) {\n    if (!this.initialNgDefs.has(type)) {\n      this.initialNgDefs.set(type, new Map());\n    }\n    const currentDefs = this.initialNgDefs.get(type);\n    if (!currentDefs.has(prop)) {\n      const currentDef = Object.getOwnPropertyDescriptor(type, prop);\n      currentDefs.set(prop, currentDef);\n    }\n  }\n  storeFieldOfDefOnType(type, defField, fieldName) {\n    const def = type[defField];\n    const originalValue = def[fieldName];\n    this.defCleanupOps.push({\n      object: def,\n      fieldName,\n      originalValue\n    });\n  }\n  /**\n   * Clears current components resolution queue, but stores the state of the queue, so we can\n   * restore it later. Clearing the queue is required before we try to compile components (via\n   * `TestBed.compileComponents`), so that component defs are in sync with the resolution queue.\n   */\n  clearComponentResolutionQueue() {\n    if (this.originalComponentResolutionQueue === null) {\n      this.originalComponentResolutionQueue = new Map();\n    }\n    ɵclearResolutionOfComponentResourcesQueue().forEach((value, key) => this.originalComponentResolutionQueue.set(key, value));\n  }\n  /*\n   * Restores component resolution queue to the previously saved state. This operation is performed\n   * as a part of restoring the state after completion of the current set of tests (that might\n   * potentially mutate the state).\n   */\n  restoreComponentResolutionQueue() {\n    if (this.originalComponentResolutionQueue !== null) {\n      ɵrestoreComponentResolutionQueue(this.originalComponentResolutionQueue);\n      this.originalComponentResolutionQueue = null;\n    }\n  }\n  restoreOriginalState() {\n    // Process cleanup ops in reverse order so the field's original value is restored correctly (in\n    // case there were multiple overrides for the same field).\n    forEachRight(this.defCleanupOps, op => {\n      op.object[op.fieldName] = op.originalValue;\n    });\n    // Restore initial component/directive/pipe defs\n    this.initialNgDefs.forEach((defs, type) => {\n      if (ɵUSE_RUNTIME_DEPS_TRACKER_FOR_JIT) {\n        ɵdepsTracker.clearScopeCacheFor(type);\n      }\n      defs.forEach((descriptor, prop) => {\n        if (!descriptor) {\n          // Delete operations are generally undesirable since they have performance\n          // implications on objects they were applied to. In this particular case, situations\n          // where this code is invoked should be quite rare to cause any noticeable impact,\n          // since it's applied only to some test cases (for example when class with no\n          // annotations extends some @Component) when we need to clear 'ɵcmp' field on a given\n          // class to restore its original state (before applying overrides and running tests).\n          delete type[prop];\n        } else {\n          Object.defineProperty(type, prop, descriptor);\n        }\n      });\n    });\n    this.initialNgDefs.clear();\n    this.scopesWithOverriddenProviders.clear();\n    this.restoreComponentResolutionQueue();\n    // Restore the locale ID to the default value, this shouldn't be necessary but we never know\n    ɵsetLocaleId(ɵDEFAULT_LOCALE_ID);\n  }\n  compileTestModule() {\n    class RootScopeModule {}\n    ɵcompileNgModuleDefs(RootScopeModule, {\n      providers: [...this.rootProviderOverrides]\n    });\n    const providers = [provideZoneChangeDetection(), {\n      provide: Compiler,\n      useFactory: () => new R3TestCompiler(this)\n    }, {\n      provide: ɵDEFER_BLOCK_CONFIG,\n      useValue: {\n        behavior: this.deferBlockBehavior\n      }\n    }, ...this.providers, ...this.providerOverrides];\n    const imports = [RootScopeModule, this.additionalModuleTypes, this.imports || []];\n    // clang-format off\n    ɵcompileNgModuleDefs(this.testModuleType, {\n      declarations: this.declarations,\n      imports,\n      schemas: this.schemas,\n      providers\n    }, /* allowDuplicateDeclarationsInRoot */true);\n    // clang-format on\n    this.applyProviderOverridesInScope(this.testModuleType);\n  }\n  get injector() {\n    if (this._injector !== null) {\n      return this._injector;\n    }\n    const providers = [];\n    const compilerOptions = this.platform.injector.get(COMPILER_OPTIONS);\n    compilerOptions.forEach(opts => {\n      if (opts.providers) {\n        providers.push(opts.providers);\n      }\n    });\n    if (this.compilerProviders !== null) {\n      providers.push(...this.compilerProviders);\n    }\n    this._injector = Injector.create({\n      providers,\n      parent: this.platform.injector\n    });\n    return this._injector;\n  }\n  // get overrides for a specific provider (if any)\n  getSingleProviderOverrides(provider) {\n    const token = getProviderToken(provider);\n    return this.providerOverridesByToken.get(token) || null;\n  }\n  getProviderOverrides(providers) {\n    if (!providers || !providers.length || this.providerOverridesByToken.size === 0) return [];\n    // There are two flattening operations here. The inner flattenProviders() operates on the\n    // metadata's providers and applies a mapping function which retrieves overrides for each\n    // incoming provider. The outer flatten() then flattens the produced overrides array. If this is\n    // not done, the array can contain other empty arrays (e.g. `[[], []]`) which leak into the\n    // providers array and contaminate any error messages that might be generated.\n    return flatten(flattenProviders(providers, provider => this.getSingleProviderOverrides(provider) || []));\n  }\n  getOverriddenProviders(providers) {\n    if (!providers || !providers.length || this.providerOverridesByToken.size === 0) return [];\n    const flattenedProviders = flattenProviders(providers);\n    const overrides = this.getProviderOverrides(flattenedProviders);\n    const overriddenProviders = [...flattenedProviders, ...overrides];\n    const final = [];\n    const seenOverriddenProviders = new Set();\n    // We iterate through the list of providers in reverse order to make sure provider overrides\n    // take precedence over the values defined in provider list. We also filter out all providers\n    // that have overrides, keeping overridden values only. This is needed, since presence of a\n    // provider with `ngOnDestroy` hook will cause this hook to be registered and invoked later.\n    forEachRight(overriddenProviders, provider => {\n      const token = getProviderToken(provider);\n      if (this.providerOverridesByToken.has(token)) {\n        if (!seenOverriddenProviders.has(token)) {\n          seenOverriddenProviders.add(token);\n          // Treat all overridden providers as `{multi: false}` (even if it's a multi-provider) to\n          // make sure that provided override takes highest precedence and is not combined with\n          // other instances of the same multi provider.\n          final.unshift({\n            ...provider,\n            multi: false\n          });\n        }\n      } else {\n        final.unshift(provider);\n      }\n    });\n    return final;\n  }\n  hasProviderOverrides(providers) {\n    return this.getProviderOverrides(providers).length > 0;\n  }\n  patchDefWithProviderOverrides(declaration, field) {\n    const def = declaration[field];\n    if (def && def.providersResolver) {\n      this.maybeStoreNgDef(field, declaration);\n      const resolver = def.providersResolver;\n      const processProvidersFn = providers => this.getOverriddenProviders(providers);\n      this.storeFieldOfDefOnType(declaration, field, 'providersResolver');\n      def.providersResolver = ngDef => resolver(ngDef, processProvidersFn);\n    }\n  }\n}\nfunction initResolvers() {\n  return {\n    module: new NgModuleResolver(),\n    component: new ComponentResolver(),\n    directive: new DirectiveResolver(),\n    pipe: new PipeResolver()\n  };\n}\nfunction isStandaloneComponent(value) {\n  const def = getComponentDef(value);\n  return !!def?.standalone;\n}\nfunction getComponentDef(value) {\n  return value.ɵcmp ?? null;\n}\nfunction hasNgModuleDef(value) {\n  return value.hasOwnProperty('ɵmod');\n}\nfunction isNgModule(value) {\n  return hasNgModuleDef(value);\n}\nfunction maybeUnwrapFn(maybeFn) {\n  return maybeFn instanceof Function ? maybeFn() : maybeFn;\n}\nfunction flatten(values) {\n  const out = [];\n  values.forEach(value => {\n    if (Array.isArray(value)) {\n      out.push(...flatten(value));\n    } else {\n      out.push(value);\n    }\n  });\n  return out;\n}\nfunction identityFn(value) {\n  return value;\n}\nfunction flattenProviders(providers, mapFn = identityFn) {\n  const out = [];\n  for (let provider of providers) {\n    if (ɵisEnvironmentProviders(provider)) {\n      provider = provider.ɵproviders;\n    }\n    if (Array.isArray(provider)) {\n      out.push(...flattenProviders(provider, mapFn));\n    } else {\n      out.push(mapFn(provider));\n    }\n  }\n  return out;\n}\nfunction getProviderField(provider, field) {\n  return provider && typeof provider === 'object' && provider[field];\n}\nfunction getProviderToken(provider) {\n  return getProviderField(provider, 'provide') || provider;\n}\nfunction isModuleWithProviders(value) {\n  return value.hasOwnProperty('ngModule');\n}\nfunction forEachRight(values, fn) {\n  for (let idx = values.length - 1; idx >= 0; idx--) {\n    fn(values[idx], idx);\n  }\n}\nfunction invalidTypeError(name, expectedType) {\n  return new Error(`${name} class doesn't have @${expectedType} decorator or is missing metadata.`);\n}\nclass R3TestCompiler {\n  constructor(testBed) {\n    this.testBed = testBed;\n  }\n  compileModuleSync(moduleType) {\n    this.testBed._compileNgModuleSync(moduleType);\n    return new ɵNgModuleFactory(moduleType);\n  }\n  compileModuleAsync(moduleType) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      yield _this5.testBed._compileNgModuleAsync(moduleType);\n      return new ɵNgModuleFactory(moduleType);\n    })();\n  }\n  compileModuleAndAllComponentsSync(moduleType) {\n    const ngModuleFactory = this.compileModuleSync(moduleType);\n    const componentFactories = this.testBed._getComponentFactories(moduleType);\n    return new ModuleWithComponentFactories(ngModuleFactory, componentFactories);\n  }\n  compileModuleAndAllComponentsAsync(moduleType) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      const ngModuleFactory = yield _this6.compileModuleAsync(moduleType);\n      const componentFactories = _this6.testBed._getComponentFactories(moduleType);\n      return new ModuleWithComponentFactories(ngModuleFactory, componentFactories);\n    })();\n  }\n  clearCache() {}\n  clearCacheFor(type) {}\n  getModuleId(moduleType) {\n    const meta = this.testBed._getModuleResolver().resolve(moduleType);\n    return meta && meta.id || undefined;\n  }\n}\n\n// The formatter and CI disagree on how this import statement should be formatted. Both try to keep\nlet _nextRootElementId = 0;\n/**\n * Returns a singleton of the `TestBed` class.\n *\n * @publicApi\n */\nfunction getTestBed() {\n  return TestBedImpl.INSTANCE;\n}\n/**\n * @description\n * Configures and initializes environment for unit testing and provides methods for\n * creating components and services in unit tests.\n *\n * TestBed is the primary api for writing unit tests for Angular applications and libraries.\n */\nclass TestBedImpl {\n  constructor() {\n    /**\n     * Defer block behavior option that specifies whether defer blocks will be triggered manually\n     * or set to play through.\n     */\n    this._instanceDeferBlockBehavior = DEFER_BLOCK_DEFAULT_BEHAVIOR;\n    // Properties\n    this.platform = null;\n    this.ngModule = null;\n    this._compiler = null;\n    this._testModuleRef = null;\n    this._activeFixtures = [];\n    /**\n     * Internal-only flag to indicate whether a module\n     * scoping queue has been checked and flushed already.\n     * @nodoc\n     */\n    this.globalCompilationChecked = false;\n  }\n  static {\n    this._INSTANCE = null;\n  }\n  static get INSTANCE() {\n    return TestBedImpl._INSTANCE = TestBedImpl._INSTANCE || new TestBedImpl();\n  }\n  /**\n   * Initialize the environment for testing with a compiler factory, a PlatformRef, and an\n   * angular module. These are common to every test in the suite.\n   *\n   * This may only be called once, to set up the common providers for the current test\n   * suite on the current platform. If you absolutely need to change the providers,\n   * first use `resetTestEnvironment`.\n   *\n   * Test modules and platforms for individual platforms are available from\n   * '@angular/<platform_name>/testing'.\n   *\n   * @publicApi\n   */\n  static initTestEnvironment(ngModule, platform, options) {\n    const testBed = TestBedImpl.INSTANCE;\n    testBed.initTestEnvironment(ngModule, platform, options);\n    return testBed;\n  }\n  /**\n   * Reset the providers for the test injector.\n   *\n   * @publicApi\n   */\n  static resetTestEnvironment() {\n    TestBedImpl.INSTANCE.resetTestEnvironment();\n  }\n  static configureCompiler(config) {\n    return TestBedImpl.INSTANCE.configureCompiler(config);\n  }\n  /**\n   * Allows overriding default providers, directives, pipes, modules of the test injector,\n   * which are defined in test_injector.js\n   */\n  static configureTestingModule(moduleDef) {\n    return TestBedImpl.INSTANCE.configureTestingModule(moduleDef);\n  }\n  /**\n   * Compile components with a `templateUrl` for the test's NgModule.\n   * It is necessary to call this function\n   * as fetching urls is asynchronous.\n   */\n  static compileComponents() {\n    return TestBedImpl.INSTANCE.compileComponents();\n  }\n  static overrideModule(ngModule, override) {\n    return TestBedImpl.INSTANCE.overrideModule(ngModule, override);\n  }\n  static overrideComponent(component, override) {\n    return TestBedImpl.INSTANCE.overrideComponent(component, override);\n  }\n  static overrideDirective(directive, override) {\n    return TestBedImpl.INSTANCE.overrideDirective(directive, override);\n  }\n  static overridePipe(pipe, override) {\n    return TestBedImpl.INSTANCE.overridePipe(pipe, override);\n  }\n  static overrideTemplate(component, template) {\n    return TestBedImpl.INSTANCE.overrideTemplate(component, template);\n  }\n  /**\n   * Overrides the template of the given component, compiling the template\n   * in the context of the TestingModule.\n   *\n   * Note: This works for JIT and AOTed components as well.\n   */\n  static overrideTemplateUsingTestingModule(component, template) {\n    return TestBedImpl.INSTANCE.overrideTemplateUsingTestingModule(component, template);\n  }\n  static overrideProvider(token, provider) {\n    return TestBedImpl.INSTANCE.overrideProvider(token, provider);\n  }\n  static inject(token, notFoundValue, flags) {\n    return TestBedImpl.INSTANCE.inject(token, notFoundValue, ɵconvertToBitFlags(flags));\n  }\n  /** @deprecated from v9.0.0 use TestBed.inject */\n  static get(token, notFoundValue = Injector.THROW_IF_NOT_FOUND, flags = InjectFlags.Default) {\n    return TestBedImpl.INSTANCE.inject(token, notFoundValue, flags);\n  }\n  /**\n   * Runs the given function in the `EnvironmentInjector` context of `TestBed`.\n   *\n   * @see {@link EnvironmentInjector#runInContext}\n   */\n  static runInInjectionContext(fn) {\n    return TestBedImpl.INSTANCE.runInInjectionContext(fn);\n  }\n  static createComponent(component) {\n    return TestBedImpl.INSTANCE.createComponent(component);\n  }\n  static resetTestingModule() {\n    return TestBedImpl.INSTANCE.resetTestingModule();\n  }\n  static execute(tokens, fn, context) {\n    return TestBedImpl.INSTANCE.execute(tokens, fn, context);\n  }\n  static get platform() {\n    return TestBedImpl.INSTANCE.platform;\n  }\n  static get ngModule() {\n    return TestBedImpl.INSTANCE.ngModule;\n  }\n  static flushEffects() {\n    return TestBedImpl.INSTANCE.flushEffects();\n  }\n  /**\n   * Initialize the environment for testing with a compiler factory, a PlatformRef, and an\n   * angular module. These are common to every test in the suite.\n   *\n   * This may only be called once, to set up the common providers for the current test\n   * suite on the current platform. If you absolutely need to change the providers,\n   * first use `resetTestEnvironment`.\n   *\n   * Test modules and platforms for individual platforms are available from\n   * '@angular/<platform_name>/testing'.\n   *\n   * @publicApi\n   */\n  initTestEnvironment(ngModule, platform, options) {\n    if (this.platform || this.ngModule) {\n      throw new Error('Cannot set base providers because it has already been called');\n    }\n    TestBedImpl._environmentTeardownOptions = options?.teardown;\n    TestBedImpl._environmentErrorOnUnknownElementsOption = options?.errorOnUnknownElements;\n    TestBedImpl._environmentErrorOnUnknownPropertiesOption = options?.errorOnUnknownProperties;\n    this.platform = platform;\n    this.ngModule = ngModule;\n    this._compiler = new TestBedCompiler(this.platform, this.ngModule);\n    // TestBed does not have an API which can reliably detect the start of a test, and thus could be\n    // used to track the state of the NgModule registry and reset it correctly. Instead, when we\n    // know we're in a testing scenario, we disable the check for duplicate NgModule registration\n    // completely.\n    ɵsetAllowDuplicateNgModuleIdsForTest(true);\n  }\n  /**\n   * Reset the providers for the test injector.\n   *\n   * @publicApi\n   */\n  resetTestEnvironment() {\n    this.resetTestingModule();\n    this._compiler = null;\n    this.platform = null;\n    this.ngModule = null;\n    TestBedImpl._environmentTeardownOptions = undefined;\n    ɵsetAllowDuplicateNgModuleIdsForTest(false);\n  }\n  resetTestingModule() {\n    this.checkGlobalCompilationFinished();\n    ɵresetCompiledComponents();\n    if (this._compiler !== null) {\n      this.compiler.restoreOriginalState();\n    }\n    this._compiler = new TestBedCompiler(this.platform, this.ngModule);\n    // Restore the previous value of the \"error on unknown elements\" option\n    ɵsetUnknownElementStrictMode(this._previousErrorOnUnknownElementsOption ?? THROW_ON_UNKNOWN_ELEMENTS_DEFAULT);\n    // Restore the previous value of the \"error on unknown properties\" option\n    ɵsetUnknownPropertyStrictMode(this._previousErrorOnUnknownPropertiesOption ?? THROW_ON_UNKNOWN_PROPERTIES_DEFAULT);\n    // We have to chain a couple of try/finally blocks, because each step can\n    // throw errors and we don't want it to interrupt the next step and we also\n    // want an error to be thrown at the end.\n    try {\n      this.destroyActiveFixtures();\n    } finally {\n      try {\n        if (this.shouldTearDownTestingModule()) {\n          this.tearDownTestingModule();\n        }\n      } finally {\n        this._testModuleRef = null;\n        this._instanceTeardownOptions = undefined;\n        this._instanceErrorOnUnknownElementsOption = undefined;\n        this._instanceErrorOnUnknownPropertiesOption = undefined;\n        this._instanceDeferBlockBehavior = DEFER_BLOCK_DEFAULT_BEHAVIOR;\n      }\n    }\n    return this;\n  }\n  configureCompiler(config) {\n    if (config.useJit != null) {\n      throw new Error('JIT compiler is not configurable via TestBed APIs.');\n    }\n    if (config.providers !== undefined) {\n      this.compiler.setCompilerProviders(config.providers);\n    }\n    return this;\n  }\n  configureTestingModule(moduleDef) {\n    this.assertNotInstantiated('TestBed.configureTestingModule', 'configure the test module');\n    // Trigger module scoping queue flush before executing other TestBed operations in a test.\n    // This is needed for the first test invocation to ensure that globally declared modules have\n    // their components scoped properly. See the `checkGlobalCompilationFinished` function\n    // description for additional info.\n    this.checkGlobalCompilationFinished();\n    // Always re-assign the options, even if they're undefined.\n    // This ensures that we don't carry them between tests.\n    this._instanceTeardownOptions = moduleDef.teardown;\n    this._instanceErrorOnUnknownElementsOption = moduleDef.errorOnUnknownElements;\n    this._instanceErrorOnUnknownPropertiesOption = moduleDef.errorOnUnknownProperties;\n    this._instanceDeferBlockBehavior = moduleDef.deferBlockBehavior ?? DEFER_BLOCK_DEFAULT_BEHAVIOR;\n    // Store the current value of the strict mode option,\n    // so we can restore it later\n    this._previousErrorOnUnknownElementsOption = ɵgetUnknownElementStrictMode();\n    ɵsetUnknownElementStrictMode(this.shouldThrowErrorOnUnknownElements());\n    this._previousErrorOnUnknownPropertiesOption = ɵgetUnknownPropertyStrictMode();\n    ɵsetUnknownPropertyStrictMode(this.shouldThrowErrorOnUnknownProperties());\n    this.compiler.configureTestingModule(moduleDef);\n    return this;\n  }\n  compileComponents() {\n    return this.compiler.compileComponents();\n  }\n  inject(token, notFoundValue, flags) {\n    if (token === TestBed) {\n      return this;\n    }\n    const UNDEFINED = {};\n    const result = this.testModuleRef.injector.get(token, UNDEFINED, ɵconvertToBitFlags(flags));\n    return result === UNDEFINED ? this.compiler.injector.get(token, notFoundValue, flags) : result;\n  }\n  /** @deprecated from v9.0.0 use TestBed.inject */\n  get(token, notFoundValue = Injector.THROW_IF_NOT_FOUND, flags = InjectFlags.Default) {\n    return this.inject(token, notFoundValue, flags);\n  }\n  runInInjectionContext(fn) {\n    return runInInjectionContext(this.inject(EnvironmentInjector), fn);\n  }\n  execute(tokens, fn, context) {\n    const params = tokens.map(t => this.inject(t));\n    return fn.apply(context, params);\n  }\n  overrideModule(ngModule, override) {\n    this.assertNotInstantiated('overrideModule', 'override module metadata');\n    this.compiler.overrideModule(ngModule, override);\n    return this;\n  }\n  overrideComponent(component, override) {\n    this.assertNotInstantiated('overrideComponent', 'override component metadata');\n    this.compiler.overrideComponent(component, override);\n    return this;\n  }\n  overrideTemplateUsingTestingModule(component, template) {\n    this.assertNotInstantiated('TestBed.overrideTemplateUsingTestingModule', 'Cannot override template when the test module has already been instantiated');\n    this.compiler.overrideTemplateUsingTestingModule(component, template);\n    return this;\n  }\n  overrideDirective(directive, override) {\n    this.assertNotInstantiated('overrideDirective', 'override directive metadata');\n    this.compiler.overrideDirective(directive, override);\n    return this;\n  }\n  overridePipe(pipe, override) {\n    this.assertNotInstantiated('overridePipe', 'override pipe metadata');\n    this.compiler.overridePipe(pipe, override);\n    return this;\n  }\n  /**\n   * Overwrites all providers for the given token with the given provider definition.\n   */\n  overrideProvider(token, provider) {\n    this.assertNotInstantiated('overrideProvider', 'override provider');\n    this.compiler.overrideProvider(token, provider);\n    return this;\n  }\n  overrideTemplate(component, template) {\n    return this.overrideComponent(component, {\n      set: {\n        template,\n        templateUrl: null\n      }\n    });\n  }\n  createComponent(type) {\n    const testComponentRenderer = this.inject(TestComponentRenderer);\n    const rootElId = `root${_nextRootElementId++}`;\n    testComponentRenderer.insertRootElement(rootElId);\n    if (ɵgetAsyncClassMetadataFn(type)) {\n      throw new Error(`Component '${type.name}' has unresolved metadata. ` + `Please call \\`await TestBed.compileComponents()\\` before running this test.`);\n    }\n    const componentDef = type.ɵcmp;\n    if (!componentDef) {\n      throw new Error(`It looks like '${ɵstringify(type)}' has not been compiled.`);\n    }\n    const componentFactory = new ɵRender3ComponentFactory(componentDef);\n    const initComponent = () => {\n      const componentRef = componentFactory.create(Injector.NULL, [], `#${rootElId}`, this.testModuleRef);\n      return this.runInInjectionContext(() => {\n        const hasScheduler = this.inject(ɵChangeDetectionScheduler, null) !== null;\n        const fixture = hasScheduler ? new ScheduledComponentFixture(componentRef) : new PseudoApplicationComponentFixture(componentRef);\n        fixture.initialize();\n        return fixture;\n      });\n    };\n    const noNgZone = this.inject(ComponentFixtureNoNgZone, false);\n    const ngZone = noNgZone ? null : this.inject(NgZone, null);\n    const fixture = ngZone ? ngZone.run(initComponent) : initComponent();\n    this._activeFixtures.push(fixture);\n    return fixture;\n  }\n  /**\n   * @internal strip this from published d.ts files due to\n   * https://github.com/microsoft/TypeScript/issues/36216\n   */\n  get compiler() {\n    if (this._compiler === null) {\n      throw new Error(`Need to call TestBed.initTestEnvironment() first`);\n    }\n    return this._compiler;\n  }\n  /**\n   * @internal strip this from published d.ts files due to\n   * https://github.com/microsoft/TypeScript/issues/36216\n   */\n  get testModuleRef() {\n    if (this._testModuleRef === null) {\n      this._testModuleRef = this.compiler.finalize();\n    }\n    return this._testModuleRef;\n  }\n  assertNotInstantiated(methodName, methodDescription) {\n    if (this._testModuleRef !== null) {\n      throw new Error(`Cannot ${methodDescription} when the test module has already been instantiated. ` + `Make sure you are not using \\`inject\\` before \\`${methodName}\\`.`);\n    }\n  }\n  /**\n   * Check whether the module scoping queue should be flushed, and flush it if needed.\n   *\n   * When the TestBed is reset, it clears the JIT module compilation queue, cancelling any\n   * in-progress module compilation. This creates a potential hazard - the very first time the\n   * TestBed is initialized (or if it's reset without being initialized), there may be pending\n   * compilations of modules declared in global scope. These compilations should be finished.\n   *\n   * To ensure that globally declared modules have their components scoped properly, this function\n   * is called whenever TestBed is initialized or reset. The _first_ time that this happens, prior\n   * to any other operations, the scoping queue is flushed.\n   */\n  checkGlobalCompilationFinished() {\n    // Checking _testNgModuleRef is null should not be necessary, but is left in as an additional\n    // guard that compilations queued in tests (after instantiation) are never flushed accidentally.\n    if (!this.globalCompilationChecked && this._testModuleRef === null) {\n      ɵflushModuleScopingQueueAsMuchAsPossible();\n    }\n    this.globalCompilationChecked = true;\n  }\n  destroyActiveFixtures() {\n    let errorCount = 0;\n    this._activeFixtures.forEach(fixture => {\n      try {\n        fixture.destroy();\n      } catch (e) {\n        errorCount++;\n        console.error('Error during cleanup of component', {\n          component: fixture.componentInstance,\n          stacktrace: e\n        });\n      }\n    });\n    this._activeFixtures = [];\n    if (errorCount > 0 && this.shouldRethrowTeardownErrors()) {\n      throw Error(`${errorCount} ${errorCount === 1 ? 'component' : 'components'} ` + `threw errors during cleanup`);\n    }\n  }\n  shouldRethrowTeardownErrors() {\n    const instanceOptions = this._instanceTeardownOptions;\n    const environmentOptions = TestBedImpl._environmentTeardownOptions;\n    // If the new teardown behavior hasn't been configured, preserve the old behavior.\n    if (!instanceOptions && !environmentOptions) {\n      return TEARDOWN_TESTING_MODULE_ON_DESTROY_DEFAULT;\n    }\n    // Otherwise use the configured behavior or default to rethrowing.\n    return instanceOptions?.rethrowErrors ?? environmentOptions?.rethrowErrors ?? this.shouldTearDownTestingModule();\n  }\n  shouldThrowErrorOnUnknownElements() {\n    // Check if a configuration has been provided to throw when an unknown element is found\n    return this._instanceErrorOnUnknownElementsOption ?? TestBedImpl._environmentErrorOnUnknownElementsOption ?? THROW_ON_UNKNOWN_ELEMENTS_DEFAULT;\n  }\n  shouldThrowErrorOnUnknownProperties() {\n    // Check if a configuration has been provided to throw when an unknown property is found\n    return this._instanceErrorOnUnknownPropertiesOption ?? TestBedImpl._environmentErrorOnUnknownPropertiesOption ?? THROW_ON_UNKNOWN_PROPERTIES_DEFAULT;\n  }\n  shouldTearDownTestingModule() {\n    return this._instanceTeardownOptions?.destroyAfterEach ?? TestBedImpl._environmentTeardownOptions?.destroyAfterEach ?? TEARDOWN_TESTING_MODULE_ON_DESTROY_DEFAULT;\n  }\n  getDeferBlockBehavior() {\n    return this._instanceDeferBlockBehavior;\n  }\n  tearDownTestingModule() {\n    // If the module ref has already been destroyed, we won't be able to get a test renderer.\n    if (this._testModuleRef === null) {\n      return;\n    }\n    // Resolve the renderer ahead of time, because we want to remove the root elements as the very\n    // last step, but the injector will be destroyed as a part of the module ref destruction.\n    const testRenderer = this.inject(TestComponentRenderer);\n    try {\n      this._testModuleRef.destroy();\n    } catch (e) {\n      if (this.shouldRethrowTeardownErrors()) {\n        throw e;\n      } else {\n        console.error('Error during cleanup of a testing module', {\n          component: this._testModuleRef.instance,\n          stacktrace: e\n        });\n      }\n    } finally {\n      testRenderer.removeAllRootElements?.();\n    }\n  }\n  /**\n   * Execute any pending effects.\n   *\n   * @developerPreview\n   */\n  flushEffects() {\n    this.inject(ɵEffectScheduler).flush();\n  }\n}\n/**\n * @description\n * Configures and initializes environment for unit testing and provides methods for\n * creating components and services in unit tests.\n *\n * `TestBed` is the primary api for writing unit tests for Angular applications and libraries.\n *\n * @publicApi\n */\nconst TestBed = TestBedImpl;\n/**\n * Allows injecting dependencies in `beforeEach()` and `it()`. Note: this function\n * (imported from the `@angular/core/testing` package) can **only** be used to inject dependencies\n * in tests. To inject dependencies in your application code, use the [`inject`](api/core/inject)\n * function from the `@angular/core` package instead.\n *\n * Example:\n *\n * ```\n * beforeEach(inject([Dependency, AClass], (dep, object) => {\n *   // some code that uses `dep` and `object`\n *   // ...\n * }));\n *\n * it('...', inject([AClass], (object) => {\n *   object.doSomething();\n *   expect(...);\n * })\n * ```\n *\n * @publicApi\n */\nfunction inject(tokens, fn) {\n  const testBed = TestBedImpl.INSTANCE;\n  // Not using an arrow function to preserve context passed from call site\n  return function () {\n    return testBed.execute(tokens, fn, this);\n  };\n}\n/**\n * @publicApi\n */\nclass InjectSetupWrapper {\n  constructor(_moduleDef) {\n    this._moduleDef = _moduleDef;\n  }\n  _addModule() {\n    const moduleDef = this._moduleDef();\n    if (moduleDef) {\n      TestBedImpl.configureTestingModule(moduleDef);\n    }\n  }\n  inject(tokens, fn) {\n    const self = this;\n    // Not using an arrow function to preserve context passed from call site\n    return function () {\n      self._addModule();\n      return inject(tokens, fn).call(this);\n    };\n  }\n}\nfunction withModule(moduleDef, fn) {\n  if (fn) {\n    // Not using an arrow function to preserve context passed from call site\n    return function () {\n      const testBed = TestBedImpl.INSTANCE;\n      if (moduleDef) {\n        testBed.configureTestingModule(moduleDef);\n      }\n      return fn.apply(this);\n    };\n  }\n  return new InjectSetupWrapper(() => moduleDef);\n}\n\n/**\n * Public Test Library for unit testing Angular applications. Assumes that you are running\n * with Jasmine, Mocha, or a similar framework which exports a beforeEach function and\n * allows tests to be asynchronous by either returning a promise or using a 'done' parameter.\n */\n// Reset the test providers and the fake async zone before each test.\n// We keep a guard because somehow this file can make it into a bundle and be executed\n// beforeEach is only defined when executing the tests\nglobalThis.beforeEach?.(getCleanupHook(false));\n// We provide both a `beforeEach` and `afterEach`, because the updated behavior for\n// tearing down the module is supposed to run after the test so that we can associate\n// teardown errors with the correct test.\n// We keep a guard because somehow this file can make it into a bundle and be executed\n// afterEach is only defined when executing the tests\nglobalThis.afterEach?.(getCleanupHook(true));\nfunction getCleanupHook(expectedTeardownValue) {\n  return () => {\n    const testBed = TestBedImpl.INSTANCE;\n    if (testBed.shouldTearDownTestingModule() === expectedTeardownValue) {\n      testBed.resetTestingModule();\n      resetFakeAsyncZoneIfExists();\n    }\n  };\n}\n/**\n * This API should be removed. But doing so seems to break `google3` and so it requires a bit of\n * investigation.\n *\n * A work around is to mark it as `@codeGenApi` for now and investigate later.\n *\n * @codeGenApi\n */\n// TODO(iminar): Remove this code in a safe way.\nconst __core_private_testing_placeholder__ = '';\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the core/testing package.\n */\n\n/// <reference types=\"jasmine\" />\n// This file only reexports content of the `src` folder. Keep it that way.\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ComponentFixture, ComponentFixtureAutoDetect, ComponentFixtureNoNgZone, DeferBlockFixture, InjectSetupWrapper, TestBed, TestComponentRenderer, __core_private_testing_placeholder__, async, discardPeriodicTasks, fakeAsync, flush, flushMicrotasks, getTestBed, inject, resetFakeAsyncZone, tick, waitForAsync, withModule, MetadataOverrider as ɵMetadataOverrider };", "map": {"version": 3, "names": ["ɵDeferBlockState", "ɵtriggerResourceLoading", "ɵrenderDeferBlockState", "ɵCONTAINER_HEADER_OFFSET", "ɵgetDeferBlocks", "ɵDeferBlockBehavior", "InjectionToken", "inject", "inject$1", "ɵNoopNgZone", "NgZone", "ɵEffectScheduler", "ApplicationRef", "getDebugNode", "RendererFactory2", "ɵPendingTasks", "ɵstringify", "ɵReflectionCapabilities", "Directive", "Component", "<PERSON><PERSON>", "NgModule", "ɵgetAsyncClassMetadataFn", "ɵgenerateStandaloneInDeclarationsError", "ɵUSE_RUNTIME_DEPS_TRACKER_FOR_JIT", "ɵdepsTracker", "ɵgetInjectableDef", "resolveForwardRef", "ɵNG_COMP_DEF", "ɵisComponentDefPendingResolution", "ɵresolveComponentResources", "ɵRender3NgModuleRef", "ApplicationInitStatus", "LOCALE_ID", "ɵDEFAULT_LOCALE_ID", "ɵsetLocaleId", "ɵRender3ComponentFactory", "ɵcompileComponent", "ɵNG_DIR_DEF", "ɵcompileDirective", "ɵNG_PIPE_DEF", "ɵcompilePipe", "ɵNG_MOD_DEF", "ɵtransitiveScopesFor", "ɵpatchComponentDefWithScope", "ɵNG_INJ_DEF", "ɵcompileNgModuleDefs", "ɵclearResolutionOfComponentResourcesQueue", "ɵrestoreComponentResolutionQueue", "provideZoneChangeDetection", "Compiler", "ɵDEFER_BLOCK_CONFIG", "COMPILER_OPTIONS", "Injector", "ɵisEnvironmentProviders", "ɵNgModuleFactory", "ModuleWithComponentFactories", "ɵconvertToBitFlags", "InjectFlags", "ɵsetAllowDuplicateNgModuleIdsForTest", "ɵresetCompiledComponents", "ɵsetUnknownElementStrictMode", "ɵsetUnknownPropertyStrictMode", "ɵgetUnknownElementStrictMode", "ɵgetUnknownPropertyStrictMode", "runInInjectionContext", "EnvironmentInjector", "ɵChangeDetectionScheduler", "ɵflushModuleScopingQueueAsMuchAsPossible", "DeferBlockBehavior", "DeferBlockState", "Subscription", "first", "Resource<PERSON><PERSON>der", "waitForAsync", "fn", "_Zone", "Zone", "Promise", "reject", "asyncTest", "__symbol__", "async", "DeferBlockFixture", "constructor", "block", "componentFixture", "render", "state", "_this", "_asyncToGenerator", "hasStateTemplate", "stateAsString", "getDeferBlockStateNameFromEnum", "Error", "toLowerCase", "Complete", "tDetails", "lView", "tNode", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>uling", "lContainer", "detectChanges", "getDeferBlocks", "deferBlocks", "deferBlockFixtures", "length", "push", "resolve", "Placeholder", "placeholderTmplIndex", "Loading", "loadingTmplIndex", "errorTmplIndex", "TEARDOWN_TESTING_MODULE_ON_DESTROY_DEFAULT", "THROW_ON_UNKNOWN_ELEMENTS_DEFAULT", "THROW_ON_UNKNOWN_PROPERTIES_DEFAULT", "DEFER_BLOCK_DEFAULT_BEHAVIOR", "Playthrough", "TestComponent<PERSON><PERSON><PERSON>", "insertRootElement", "rootElementId", "removeAllRootElements", "ComponentFixtureAutoDetect", "AllowDetectChangesAndAcknowledgeItCanHideApplicationBugs", "ComponentFixtureNoNgZone", "ComponentFixture", "componentRef", "_isDestroyed", "_noZoneOptionIsSet", "optional", "_ngZone", "_effectRunner", "_appRef", "_testAppRef", "ngZone", "changeDetectorRef", "elementRef", "location", "debugElement", "nativeElement", "componentInstance", "instance", "checkNoChanges", "<PERSON><PERSON><PERSON><PERSON>", "_get<PERSON><PERSON><PERSON>", "_renderer", "undefined", "injector", "get", "whenRenderingDone", "renderer", "whenStable", "destroy", "ScheduledComponentFixture", "arguments", "disableDetectChangesError", "pendingTasks", "initialize", "attachView", "flush", "tick", "isStable", "hasPendingTasks", "value", "pipe", "stable", "to<PERSON>romise", "then", "autoDetectChanges", "autoDetect", "PseudoApplicationComponentFixture", "_subscriptions", "_autoDetect", "_isStable", "_promise", "_resolve", "afterTickSubscription", "beforeRenderSubscription", "runOutsideAngular", "add", "onUnstable", "subscribe", "next", "onMicrotaskEmpty", "onStable", "queueMicrotask", "hasPendingMacrotasks", "onError", "error", "run", "res", "unsubscribe", "fakeAsyncTestModule", "fakeAsyncTestModuleNotLoadedErrorMessage", "resetFakeAsyncZone", "resetFakeAsyncZoneIfExists", "fakeAsync", "millis", "tickOptions", "processNewMacroTasksSynchronously", "maxTurns", "discardPeriodicTasks", "flushMicrotasks", "_nextReferenceId", "MetadataOverrider", "_references", "Map", "overrideMetadata", "metadataClass", "oldMetadata", "override", "props", "_valueProps", "for<PERSON>ach", "prop", "set", "remove", "setMetadata", "removeMetadata", "addMetadata", "metadata", "references", "removeObjects", "Set", "removeValue", "Array", "isArray", "_propH<PERSON><PERSON><PERSON>", "propValue", "filter", "has", "addValue", "concat", "propName", "nextObjectId", "objectIds", "replacer", "key", "_serializeReference", "JSON", "stringify", "ref", "id", "obj", "Object", "keys", "startsWith", "proto", "getPrototypeOf", "protoProp", "desc", "getOwnPropertyDescriptor", "reflection", "OverrideResolver", "overrides", "resolved", "addOverride", "type", "delete", "setOverrides", "clear", "getAnnotation", "annotations", "i", "annotation", "isKnownType", "overrider", "DirectiveResolver", "ComponentResolver", "PipeResolver", "NgModuleResolver", "TestingModuleOverride", "isTestingModuleOverride", "DECLARATION", "OVERRIDE_TEMPLATE", "assertNoStandaloneComponents", "types", "resolver", "component", "standalone", "TestBedCompiler", "platform", "additionalModuleTypes", "originalComponentResolutionQueue", "declarations", "imports", "providers", "schemas", "pendingComponents", "pendingDirectives", "pending<PERSON><PERSON>es", "componentsWithAsyncMetadata", "seenComponents", "seenDirectives", "overriddenModules", "existingComponentStyles", "resolvers", "initResolvers", "componentToModuleScope", "initialNgDefs", "defCleanupOps", "_injector", "compilerProviders", "providerOverrides", "rootProviderOverrides", "providerOverridesByModule", "providerOverridesByToken", "scopesWithOverriddenProviders", "testModuleRef", "deferBlock<PERSON><PERSON><PERSON>or", "DynamicTestModule", "testModuleType", "setCompilerProviders", "configureTestingModule", "moduleDef", "queueTypeArray", "queueTypesFromModulesArray", "overrideModule", "ngModule", "clearScopeCacheFor", "module", "invalidTypeError", "name", "recompileNgModule", "overrideComponent", "verifyNoStandaloneFlagOverrides", "maybeRegisterComponentWithAsyncMetadata", "overrideDirective", "directive", "overridePipe", "hasOwnProperty", "override<PERSON><PERSON><PERSON>", "token", "provider", "providerDef", "useFactory", "provide", "deps", "multi", "useValue", "injectableDef", "providedIn", "overridesBucket", "existingOverrides", "overrideTemplateUsingTestingModule", "template", "def", "hasStyleUrls", "styleUrl", "styleUrls", "overrideStyleUrls", "styles", "resolvePendingComponentsWithAsyncMetadata", "_this2", "size", "promises", "asyncMetadataFn", "resolvedDeps", "all", "flatResolvedDeps", "flat", "applyProviderOverridesInScope", "compileComponents", "_this3", "clearComponentResolutionQueue", "needsAsyncResources", "compileTypesSync", "resourceLoader", "url", "finalize", "compileTestModule", "applyTransitiveScopes", "applyProviderOverrides", "patchComponentsWithExistingStyles", "parentInjector", "runInitializers", "localeId", "_compileNgModuleSync", "moduleType", "_compileNgModuleAsync", "_this4", "_getModuleResolver", "_getComponentFactories", "maybeUnwrapFn", "ɵmod", "reduce", "factories", "declaration", "componentDef", "ɵcmp", "maybeStoreNgDef", "testingModuleDef", "affectedModules", "collectModulesAffectedByOverrides", "storeFieldOfDefOnType", "transitiveCompileScopes", "moduleToScope", "getScopeOfModule", "isTestingModule", "realType", "componentType", "moduleScope", "getComponentDef", "maybeApplyOverrides", "field", "hasProviderOverrides", "patchDefWithProviderOverrides", "hasScope", "isStandaloneComponent", "isNgModule", "injectorDef", "dependencies", "dependency", "getOverriddenProviders", "importedModule", "flatten", "isModuleWithProviders", "object", "fieldName", "originalValue", "arr", "queueType", "processedDefs", "queueTypesFromModulesArrayRecur", "hasNgModuleDef", "exports", "seenModules", "calcAffectedModulesRecur", "path", "item", "currentDefs", "currentDef", "defField", "restoreComponentResolutionQueue", "restoreOriginalState", "forEachRight", "op", "defs", "descriptor", "defineProperty", "RootScopeModule", "R3TestCompiler", "behavior", "compilerOptions", "opts", "create", "parent", "getSingleProviderOverrides", "getProviderToken", "getProviderOverrides", "flattenProviders", "flattenedProviders", "overriddenProviders", "final", "seenOverriddenProviders", "unshift", "providersResolver", "processProvidersFn", "ngDef", "maybeFn", "Function", "values", "out", "identityFn", "mapFn", "ɵproviders", "getProviderField", "idx", "expectedType", "testBed", "compileModuleSync", "compileModuleAsync", "_this5", "compileModuleAndAllComponentsSync", "ngModuleFactory", "componentFactories", "compileModuleAndAllComponentsAsync", "_this6", "clearCache", "clearCacheFor", "getModuleId", "meta", "_nextRootElementId", "getTestBed", "TestBedImpl", "INSTANCE", "_instanceDeferBlockBehavior", "_compiler", "_testModuleRef", "_activeFixtures", "globalCompilationChecked", "_INSTANCE", "initTestEnvironment", "options", "resetTestEnvironment", "configureCompiler", "config", "overrideTemplate", "notFoundValue", "flags", "THROW_IF_NOT_FOUND", "<PERSON><PERSON><PERSON>", "createComponent", "resetTestingModule", "execute", "tokens", "context", "flushEffects", "_environmentTeardownOptions", "teardown", "_environmentErrorOnUnknownElementsOption", "errorOnUnknownElements", "_environmentErrorOnUnknownPropertiesOption", "errorOnUnknownProperties", "checkGlobalCompilationFinished", "compiler", "_previousErrorOnUnknownElementsOption", "_previousErrorOnUnknownPropertiesOption", "destroyActiveFixtures", "shouldTearDownTestingModule", "tearDownTestingModule", "_instanceTeardownOptions", "_instanceErrorOnUnknownElementsOption", "_instanceErrorOnUnknownPropertiesOption", "useJit", "assertNotInstantiated", "shouldThrowErrorOnUnknownElements", "shouldThrowErrorOnUnknownProperties", "TestBed", "UNDEFINED", "result", "params", "map", "t", "apply", "templateUrl", "testComponent<PERSON><PERSON><PERSON>", "rootElId", "componentFactory", "initComponent", "NULL", "hasScheduler", "fixture", "noNgZone", "methodName", "methodDescription", "errorCount", "e", "console", "stacktrace", "shouldRethrowTeardownErrors", "instanceOptions", "environmentOptions", "rethrowErrors", "destroyAfterEach", "getDeferBlockBehavior", "<PERSON><PERSON><PERSON><PERSON>", "InjectSetupWrapper", "_moduleDef", "_addModule", "self", "call", "withModule", "globalThis", "beforeEach", "getCleanupHook", "after<PERSON>ach", "expectedTeardownValue", "__core_private_testing_placeholder__", "ɵMetadataOverrider"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/@angular/core/fesm2022/testing.mjs"], "sourcesContent": ["/**\n * @license Angular v17.3.12\n * (c) 2010-2024 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { ɵDeferBlockState, ɵtriggerResourceLoading, ɵrenderDeferBlockState, ɵCONTAINER_HEADER_OFFSET, ɵgetDeferBlocks, ɵDeferBlockBehavior, InjectionToken, inject as inject$1, ɵNoopNgZone, NgZone, ɵEffectScheduler, ApplicationRef, getDebugNode, RendererFactory2, ɵPendingTasks, ɵstringify, ɵReflectionCapabilities, Directive, Component, Pipe, NgModule, ɵgetAsyncClassMetadataFn, ɵgenerateStandaloneInDeclarationsError, ɵUSE_RUNTIME_DEPS_TRACKER_FOR_JIT, ɵdepsTracker, ɵgetInjectableDef, resolveForwardRef, ɵNG_COMP_DEF, ɵisComponentDefPendingResolution, ɵresolveComponentResources, ɵRender3NgModuleRef, ApplicationInitStatus, LOCALE_ID, ɵDEFAULT_LOCALE_ID, ɵsetLocaleId, ɵRender3ComponentFactory, ɵcompileComponent, ɵNG_DIR_DEF, ɵcompileDirective, ɵNG_PIPE_DEF, ɵcompilePipe, ɵNG_MOD_DEF, ɵtransitiveScopesFor, ɵpatchComponentDefWithScope, ɵNG_INJ_DEF, ɵcompileNgModuleDefs, ɵclearResolutionOfComponentResourcesQueue, ɵrestoreComponentResolutionQueue, provideZoneChangeDetection, Compiler, ɵDEFER_BLOCK_CONFIG, COMPILER_OPTIONS, Injector, ɵisEnvironmentProviders, ɵNgModuleFactory, ModuleWithComponentFactories, ɵconvertToBitFlags, InjectFlags, ɵsetAllowDuplicateNgModuleIdsForTest, ɵresetCompiledComponents, ɵsetUnknownElementStrictMode, ɵsetUnknownPropertyStrictMode, ɵgetUnknownElementStrictMode, ɵgetUnknownPropertyStrictMode, runInInjection<PERSON>ontext, EnvironmentInjector, ɵChangeDetectionScheduler, ɵflushModuleScopingQueueAsMuchAsPossible } from '@angular/core';\nexport { ɵDeferBlockBehavior as DeferBlockBehavior, ɵDeferBlockState as DeferBlockState } from '@angular/core';\nimport { Subscription } from 'rxjs';\nimport { first } from 'rxjs/operators';\nimport { ResourceLoader } from '@angular/compiler';\n\n/**\n * Wraps a test function in an asynchronous test zone. The test will automatically\n * complete when all asynchronous calls within this zone are done. Can be used\n * to wrap an {@link inject} call.\n *\n * Example:\n *\n * ```\n * it('...', waitForAsync(inject([AClass], (object) => {\n *   object.doSomething.then(() => {\n *     expect(...);\n *   })\n * })));\n * ```\n *\n * @publicApi\n */\nfunction waitForAsync(fn) {\n    const _Zone = typeof Zone !== 'undefined' ? Zone : null;\n    if (!_Zone) {\n        return function () {\n            return Promise.reject('Zone is needed for the waitForAsync() test helper but could not be found. ' +\n                'Please make sure that your environment includes zone.js');\n        };\n    }\n    const asyncTest = _Zone && _Zone[_Zone.__symbol__('asyncTest')];\n    if (typeof asyncTest === 'function') {\n        return asyncTest(fn);\n    }\n    return function () {\n        return Promise.reject('zone-testing.js is needed for the async() test helper but could not be found. ' +\n            'Please make sure that your environment includes zone.js/testing');\n    };\n}\n/**\n * @deprecated use `waitForAsync()`, (expected removal in v12)\n * @see {@link waitForAsync}\n * @publicApi\n * */\nfunction async(fn) {\n    return waitForAsync(fn);\n}\n\n/**\n * Represents an individual defer block for testing purposes.\n *\n * @publicApi\n * @developerPreview\n */\nclass DeferBlockFixture {\n    /** @nodoc */\n    constructor(block, componentFixture) {\n        this.block = block;\n        this.componentFixture = componentFixture;\n    }\n    /**\n     * Renders the specified state of the defer fixture.\n     * @param state the defer state to render\n     */\n    async render(state) {\n        if (!hasStateTemplate(state, this.block)) {\n            const stateAsString = getDeferBlockStateNameFromEnum(state);\n            throw new Error(`Tried to render this defer block in the \\`${stateAsString}\\` state, ` +\n                `but there was no @${stateAsString.toLowerCase()} block defined in a template.`);\n        }\n        if (state === ɵDeferBlockState.Complete) {\n            await ɵtriggerResourceLoading(this.block.tDetails, this.block.lView, this.block.tNode);\n        }\n        // If the `render` method is used explicitly - skip timer-based scheduling for\n        // `@placeholder` and `@loading` blocks and render them immediately.\n        const skipTimerScheduling = true;\n        ɵrenderDeferBlockState(state, this.block.tNode, this.block.lContainer, skipTimerScheduling);\n        this.componentFixture.detectChanges();\n    }\n    /**\n     * Retrieves all nested child defer block fixtures\n     * in a given defer block.\n     */\n    getDeferBlocks() {\n        const deferBlocks = [];\n        // An LContainer that represents a defer block has at most 1 view, which is\n        // located right after an LContainer header. Get a hold of that view and inspect\n        // it for nested defer blocks.\n        const deferBlockFixtures = [];\n        if (this.block.lContainer.length >= ɵCONTAINER_HEADER_OFFSET) {\n            const lView = this.block.lContainer[ɵCONTAINER_HEADER_OFFSET];\n            ɵgetDeferBlocks(lView, deferBlocks);\n            for (const block of deferBlocks) {\n                deferBlockFixtures.push(new DeferBlockFixture(block, this.componentFixture));\n            }\n        }\n        return Promise.resolve(deferBlockFixtures);\n    }\n}\nfunction hasStateTemplate(state, block) {\n    switch (state) {\n        case ɵDeferBlockState.Placeholder:\n            return block.tDetails.placeholderTmplIndex !== null;\n        case ɵDeferBlockState.Loading:\n            return block.tDetails.loadingTmplIndex !== null;\n        case ɵDeferBlockState.Error:\n            return block.tDetails.errorTmplIndex !== null;\n        case ɵDeferBlockState.Complete:\n            return true;\n        default:\n            return false;\n    }\n}\nfunction getDeferBlockStateNameFromEnum(state) {\n    switch (state) {\n        case ɵDeferBlockState.Placeholder:\n            return 'Placeholder';\n        case ɵDeferBlockState.Loading:\n            return 'Loading';\n        case ɵDeferBlockState.Error:\n            return 'Error';\n        default:\n            return 'Main';\n    }\n}\n\n/** Whether test modules should be torn down by default. */\nconst TEARDOWN_TESTING_MODULE_ON_DESTROY_DEFAULT = true;\n/** Whether unknown elements in templates should throw by default. */\nconst THROW_ON_UNKNOWN_ELEMENTS_DEFAULT = false;\n/** Whether unknown properties in templates should throw by default. */\nconst THROW_ON_UNKNOWN_PROPERTIES_DEFAULT = false;\n/** Whether defer blocks should use manual triggering or play through normally. */\nconst DEFER_BLOCK_DEFAULT_BEHAVIOR = ɵDeferBlockBehavior.Playthrough;\n/**\n * An abstract class for inserting the root test component element in a platform independent way.\n *\n * @publicApi\n */\nclass TestComponentRenderer {\n    insertRootElement(rootElementId) { }\n    removeAllRootElements() { }\n}\n/**\n * @publicApi\n */\nconst ComponentFixtureAutoDetect = new InjectionToken('ComponentFixtureAutoDetect');\n/**\n * TODO(atscott): Make public API once we have decided if we want this error and how we want devs to\n * disable it.\n */\nconst AllowDetectChangesAndAcknowledgeItCanHideApplicationBugs = new InjectionToken('AllowDetectChangesAndAcknowledgeItCanHideApplicationBugs');\n/**\n * @publicApi\n */\nconst ComponentFixtureNoNgZone = new InjectionToken('ComponentFixtureNoNgZone');\n\n/**\n * Fixture for debugging and testing a component.\n *\n * @publicApi\n */\nclass ComponentFixture {\n    /** @nodoc */\n    constructor(componentRef) {\n        this.componentRef = componentRef;\n        this._isDestroyed = false;\n        /** @internal */\n        this._noZoneOptionIsSet = inject$1(ComponentFixtureNoNgZone, { optional: true });\n        /** @internal */\n        this._ngZone = this._noZoneOptionIsSet ? new ɵNoopNgZone() : inject$1(NgZone);\n        /** @internal */\n        this._effectRunner = inject$1(ɵEffectScheduler);\n        // Inject ApplicationRef to ensure NgZone stableness causes after render hooks to run\n        // This will likely happen as a result of fixture.detectChanges because it calls ngZone.run\n        // This is a crazy way of doing things but hey, it's the world we live in.\n        // The zoneless scheduler should instead do this more imperatively by attaching\n        // the `ComponentRef` to `ApplicationRef` and calling `appRef.tick` as the `detectChanges`\n        // behavior.\n        /** @internal */\n        this._appRef = inject$1(ApplicationRef);\n        /** @internal */\n        this._testAppRef = this._appRef;\n        // TODO(atscott): Remove this from public API\n        this.ngZone = this._noZoneOptionIsSet ? null : this._ngZone;\n        this.changeDetectorRef = componentRef.changeDetectorRef;\n        this.elementRef = componentRef.location;\n        this.debugElement = getDebugNode(this.elementRef.nativeElement);\n        this.componentInstance = componentRef.instance;\n        this.nativeElement = this.elementRef.nativeElement;\n        this.componentRef = componentRef;\n    }\n    /**\n     * Do a change detection run to make sure there were no changes.\n     */\n    checkNoChanges() {\n        this.changeDetectorRef.checkNoChanges();\n    }\n    /**\n     * Retrieves all defer block fixtures in the component fixture.\n     *\n     * @developerPreview\n     */\n    getDeferBlocks() {\n        const deferBlocks = [];\n        const lView = this.componentRef.hostView['_lView'];\n        ɵgetDeferBlocks(lView, deferBlocks);\n        const deferBlockFixtures = [];\n        for (const block of deferBlocks) {\n            deferBlockFixtures.push(new DeferBlockFixture(block, this));\n        }\n        return Promise.resolve(deferBlockFixtures);\n    }\n    _getRenderer() {\n        if (this._renderer === undefined) {\n            this._renderer = this.componentRef.injector.get(RendererFactory2, null);\n        }\n        return this._renderer;\n    }\n    /**\n     * Get a promise that resolves when the ui state is stable following animations.\n     */\n    whenRenderingDone() {\n        const renderer = this._getRenderer();\n        if (renderer && renderer.whenRenderingDone) {\n            return renderer.whenRenderingDone();\n        }\n        return this.whenStable();\n    }\n    /**\n     * Trigger component destruction.\n     */\n    destroy() {\n        if (!this._isDestroyed) {\n            this.componentRef.destroy();\n            this._isDestroyed = true;\n        }\n    }\n}\n/**\n * ComponentFixture behavior that actually attaches the component to the application to ensure\n * behaviors between fixture and application do not diverge. `detectChanges` is disabled by default\n * (instead, tests should wait for the scheduler to detect changes), `whenStable` is directly the\n * `ApplicationRef.isStable`, and `autoDetectChanges` cannot be disabled.\n */\nclass ScheduledComponentFixture extends ComponentFixture {\n    constructor() {\n        super(...arguments);\n        this.disableDetectChangesError = inject$1(AllowDetectChangesAndAcknowledgeItCanHideApplicationBugs, { optional: true }) ?? false;\n        this.pendingTasks = inject$1(ɵPendingTasks);\n    }\n    initialize() {\n        this._appRef.attachView(this.componentRef.hostView);\n    }\n    detectChanges(checkNoChanges = true) {\n        if (!this.disableDetectChangesError) {\n            throw new Error('Do not use `detectChanges` directly when using zoneless change detection.' +\n                ' Instead, wait for the next render or `fixture.whenStable`.');\n        }\n        else if (!checkNoChanges) {\n            throw new Error('Cannot disable `checkNoChanges` in this configuration. ' +\n                'Use `fixture.componentRef.hostView.changeDetectorRef.detectChanges()` instead.');\n        }\n        this._effectRunner.flush();\n        this._appRef.tick();\n        this._effectRunner.flush();\n    }\n    isStable() {\n        return !this.pendingTasks.hasPendingTasks.value;\n    }\n    whenStable() {\n        if (this.isStable()) {\n            return Promise.resolve(false);\n        }\n        return this._appRef.isStable.pipe(first((stable) => stable)).toPromise().then(() => true);\n    }\n    autoDetectChanges(autoDetect) {\n        throw new Error('Cannot call autoDetectChanges when using change detection scheduling.');\n    }\n}\n/**\n * ComponentFixture behavior that attempts to act as a \"mini application\".\n */\nclass PseudoApplicationComponentFixture extends ComponentFixture {\n    constructor() {\n        super(...arguments);\n        this._subscriptions = new Subscription();\n        this._autoDetect = inject$1(ComponentFixtureAutoDetect, { optional: true }) ?? false;\n        this._isStable = true;\n        this._promise = null;\n        this._resolve = null;\n        this.afterTickSubscription = undefined;\n        this.beforeRenderSubscription = undefined;\n    }\n    initialize() {\n        // Create subscriptions outside the NgZone so that the callbacks run outside\n        // of NgZone.\n        this._ngZone.runOutsideAngular(() => {\n            this._subscriptions.add(this._ngZone.onUnstable.subscribe({\n                next: () => {\n                    this._isStable = false;\n                },\n            }));\n            this._subscriptions.add(this._ngZone.onMicrotaskEmpty.subscribe({\n                next: () => {\n                    if (this._autoDetect) {\n                        // Do a change detection run with checkNoChanges set to true to check\n                        // there are no changes on the second run.\n                        this.detectChanges(true);\n                    }\n                },\n            }));\n            this._subscriptions.add(this._ngZone.onStable.subscribe({\n                next: () => {\n                    this._isStable = true;\n                    // Check whether there is a pending whenStable() completer to resolve.\n                    if (this._promise !== null) {\n                        // If so check whether there are no pending macrotasks before resolving.\n                        // Do this check in the next tick so that ngZone gets a chance to update the state\n                        // of pending macrotasks.\n                        queueMicrotask(() => {\n                            if (!this._ngZone.hasPendingMacrotasks) {\n                                if (this._promise !== null) {\n                                    this._resolve(true);\n                                    this._resolve = null;\n                                    this._promise = null;\n                                }\n                            }\n                        });\n                    }\n                },\n            }));\n            this._subscriptions.add(this._ngZone.onError.subscribe({\n                next: (error) => {\n                    throw error;\n                },\n            }));\n        });\n    }\n    detectChanges(checkNoChanges = true) {\n        this._effectRunner.flush();\n        // Run the change detection inside the NgZone so that any async tasks as part of the change\n        // detection are captured by the zone and can be waited for in isStable.\n        this._ngZone.run(() => {\n            this.changeDetectorRef.detectChanges();\n            if (checkNoChanges) {\n                this.checkNoChanges();\n            }\n        });\n        // Run any effects that were created/dirtied during change detection. Such effects might become\n        // dirty in response to input signals changing.\n        this._effectRunner.flush();\n    }\n    isStable() {\n        return this._isStable && !this._ngZone.hasPendingMacrotasks;\n    }\n    whenStable() {\n        if (this.isStable()) {\n            return Promise.resolve(false);\n        }\n        else if (this._promise !== null) {\n            return this._promise;\n        }\n        else {\n            this._promise = new Promise((res) => {\n                this._resolve = res;\n            });\n            return this._promise;\n        }\n    }\n    autoDetectChanges(autoDetect = true) {\n        if (this._noZoneOptionIsSet) {\n            throw new Error('Cannot call autoDetectChanges when ComponentFixtureNoNgZone is set.');\n        }\n        this._autoDetect = autoDetect;\n        this.detectChanges();\n    }\n    destroy() {\n        this._subscriptions.unsubscribe();\n        super.destroy();\n    }\n}\n\nconst _Zone = typeof Zone !== 'undefined' ? Zone : null;\nconst fakeAsyncTestModule = _Zone && _Zone[_Zone.__symbol__('fakeAsyncTest')];\nconst fakeAsyncTestModuleNotLoadedErrorMessage = `zone-testing.js is needed for the fakeAsync() test helper but could not be found.\n        Please make sure that your environment includes zone.js/testing`;\n/**\n * Clears out the shared fake async zone for a test.\n * To be called in a global `beforeEach`.\n *\n * @publicApi\n */\nfunction resetFakeAsyncZone() {\n    if (fakeAsyncTestModule) {\n        return fakeAsyncTestModule.resetFakeAsyncZone();\n    }\n    throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\nfunction resetFakeAsyncZoneIfExists() {\n    if (fakeAsyncTestModule) {\n        fakeAsyncTestModule.resetFakeAsyncZone();\n    }\n}\n/**\n * Wraps a function to be executed in the `fakeAsync` zone:\n * - Microtasks are manually executed by calling `flushMicrotasks()`.\n * - Timers are synchronous; `tick()` simulates the asynchronous passage of time.\n *\n * If there are any pending timers at the end of the function, an exception is thrown.\n *\n * Can be used to wrap `inject()` calls.\n *\n * @param fn The function that you want to wrap in the `fakeAsync` zone.\n *\n * @usageNotes\n * ### Example\n *\n * {@example core/testing/ts/fake_async.ts region='basic'}\n *\n *\n * @returns The function wrapped to be executed in the `fakeAsync` zone.\n * Any arguments passed when calling this returned function will be passed through to the `fn`\n * function in the parameters when it is called.\n *\n * @publicApi\n */\nfunction fakeAsync(fn) {\n    if (fakeAsyncTestModule) {\n        return fakeAsyncTestModule.fakeAsync(fn);\n    }\n    throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\n/**\n * Simulates the asynchronous passage of time for the timers in the `fakeAsync` zone.\n *\n * The microtasks queue is drained at the very start of this function and after any timer callback\n * has been executed.\n *\n * @param millis The number of milliseconds to advance the virtual timer.\n * @param tickOptions The options to pass to the `tick()` function.\n *\n * @usageNotes\n *\n * The `tick()` option is a flag called `processNewMacroTasksSynchronously`,\n * which determines whether or not to invoke new macroTasks.\n *\n * If you provide a `tickOptions` object, but do not specify a\n * `processNewMacroTasksSynchronously` property (`tick(100, {})`),\n * then `processNewMacroTasksSynchronously` defaults to true.\n *\n * If you omit the `tickOptions` parameter (`tick(100))`), then\n * `tickOptions` defaults to `{processNewMacroTasksSynchronously: true}`.\n *\n * ### Example\n *\n * {@example core/testing/ts/fake_async.ts region='basic'}\n *\n * The following example includes a nested timeout (new macroTask), and\n * the `tickOptions` parameter is allowed to default. In this case,\n * `processNewMacroTasksSynchronously` defaults to true, and the nested\n * function is executed on each tick.\n *\n * ```\n * it ('test with nested setTimeout', fakeAsync(() => {\n *   let nestedTimeoutInvoked = false;\n *   function funcWithNestedTimeout() {\n *     setTimeout(() => {\n *       nestedTimeoutInvoked = true;\n *     });\n *   };\n *   setTimeout(funcWithNestedTimeout);\n *   tick();\n *   expect(nestedTimeoutInvoked).toBe(true);\n * }));\n * ```\n *\n * In the following case, `processNewMacroTasksSynchronously` is explicitly\n * set to false, so the nested timeout function is not invoked.\n *\n * ```\n * it ('test with nested setTimeout', fakeAsync(() => {\n *   let nestedTimeoutInvoked = false;\n *   function funcWithNestedTimeout() {\n *     setTimeout(() => {\n *       nestedTimeoutInvoked = true;\n *     });\n *   };\n *   setTimeout(funcWithNestedTimeout);\n *   tick(0, {processNewMacroTasksSynchronously: false});\n *   expect(nestedTimeoutInvoked).toBe(false);\n * }));\n * ```\n *\n *\n * @publicApi\n */\nfunction tick(millis = 0, tickOptions = {\n    processNewMacroTasksSynchronously: true\n}) {\n    if (fakeAsyncTestModule) {\n        return fakeAsyncTestModule.tick(millis, tickOptions);\n    }\n    throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\n/**\n * Flushes any pending microtasks and simulates the asynchronous passage of time for the timers in\n * the `fakeAsync` zone by\n * draining the macrotask queue until it is empty.\n *\n * @param maxTurns The maximum number of times the scheduler attempts to clear its queue before\n *     throwing an error.\n * @returns The simulated time elapsed, in milliseconds.\n *\n * @publicApi\n */\nfunction flush(maxTurns) {\n    if (fakeAsyncTestModule) {\n        return fakeAsyncTestModule.flush(maxTurns);\n    }\n    throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\n/**\n * Discard all remaining periodic tasks.\n *\n * @publicApi\n */\nfunction discardPeriodicTasks() {\n    if (fakeAsyncTestModule) {\n        return fakeAsyncTestModule.discardPeriodicTasks();\n    }\n    throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\n/**\n * Flush any pending microtasks.\n *\n * @publicApi\n */\nfunction flushMicrotasks() {\n    if (fakeAsyncTestModule) {\n        return fakeAsyncTestModule.flushMicrotasks();\n    }\n    throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\n\nlet _nextReferenceId = 0;\nclass MetadataOverrider {\n    constructor() {\n        this._references = new Map();\n    }\n    /**\n     * Creates a new instance for the given metadata class\n     * based on an old instance and overrides.\n     */\n    overrideMetadata(metadataClass, oldMetadata, override) {\n        const props = {};\n        if (oldMetadata) {\n            _valueProps(oldMetadata).forEach((prop) => props[prop] = oldMetadata[prop]);\n        }\n        if (override.set) {\n            if (override.remove || override.add) {\n                throw new Error(`Cannot set and add/remove ${ɵstringify(metadataClass)} at the same time!`);\n            }\n            setMetadata(props, override.set);\n        }\n        if (override.remove) {\n            removeMetadata(props, override.remove, this._references);\n        }\n        if (override.add) {\n            addMetadata(props, override.add);\n        }\n        return new metadataClass(props);\n    }\n}\nfunction removeMetadata(metadata, remove, references) {\n    const removeObjects = new Set();\n    for (const prop in remove) {\n        const removeValue = remove[prop];\n        if (Array.isArray(removeValue)) {\n            removeValue.forEach((value) => {\n                removeObjects.add(_propHashKey(prop, value, references));\n            });\n        }\n        else {\n            removeObjects.add(_propHashKey(prop, removeValue, references));\n        }\n    }\n    for (const prop in metadata) {\n        const propValue = metadata[prop];\n        if (Array.isArray(propValue)) {\n            metadata[prop] = propValue.filter((value) => !removeObjects.has(_propHashKey(prop, value, references)));\n        }\n        else {\n            if (removeObjects.has(_propHashKey(prop, propValue, references))) {\n                metadata[prop] = undefined;\n            }\n        }\n    }\n}\nfunction addMetadata(metadata, add) {\n    for (const prop in add) {\n        const addValue = add[prop];\n        const propValue = metadata[prop];\n        if (propValue != null && Array.isArray(propValue)) {\n            metadata[prop] = propValue.concat(addValue);\n        }\n        else {\n            metadata[prop] = addValue;\n        }\n    }\n}\nfunction setMetadata(metadata, set) {\n    for (const prop in set) {\n        metadata[prop] = set[prop];\n    }\n}\nfunction _propHashKey(propName, propValue, references) {\n    let nextObjectId = 0;\n    const objectIds = new Map();\n    const replacer = (key, value) => {\n        if (value !== null && typeof value === 'object') {\n            if (objectIds.has(value)) {\n                return objectIds.get(value);\n            }\n            // Record an id for this object such that any later references use the object's id instead\n            // of the object itself, in order to break cyclic pointers in objects.\n            objectIds.set(value, `ɵobj#${nextObjectId++}`);\n            // The first time an object is seen the object itself is serialized.\n            return value;\n        }\n        else if (typeof value === 'function') {\n            value = _serializeReference(value, references);\n        }\n        return value;\n    };\n    return `${propName}:${JSON.stringify(propValue, replacer)}`;\n}\nfunction _serializeReference(ref, references) {\n    let id = references.get(ref);\n    if (!id) {\n        id = `${ɵstringify(ref)}${_nextReferenceId++}`;\n        references.set(ref, id);\n    }\n    return id;\n}\nfunction _valueProps(obj) {\n    const props = [];\n    // regular public props\n    Object.keys(obj).forEach((prop) => {\n        if (!prop.startsWith('_')) {\n            props.push(prop);\n        }\n    });\n    // getters\n    let proto = obj;\n    while (proto = Object.getPrototypeOf(proto)) {\n        Object.keys(proto).forEach((protoProp) => {\n            const desc = Object.getOwnPropertyDescriptor(proto, protoProp);\n            if (!protoProp.startsWith('_') && desc && 'get' in desc) {\n                props.push(protoProp);\n            }\n        });\n    }\n    return props;\n}\n\nconst reflection = new ɵReflectionCapabilities();\n/**\n * Allows to override ivy metadata for tests (via the `TestBed`).\n */\nclass OverrideResolver {\n    constructor() {\n        this.overrides = new Map();\n        this.resolved = new Map();\n    }\n    addOverride(type, override) {\n        const overrides = this.overrides.get(type) || [];\n        overrides.push(override);\n        this.overrides.set(type, overrides);\n        this.resolved.delete(type);\n    }\n    setOverrides(overrides) {\n        this.overrides.clear();\n        overrides.forEach(([type, override]) => {\n            this.addOverride(type, override);\n        });\n    }\n    getAnnotation(type) {\n        const annotations = reflection.annotations(type);\n        // Try to find the nearest known Type annotation and make sure that this annotation is an\n        // instance of the type we are looking for, so we can use it for resolution. Note: there might\n        // be multiple known annotations found due to the fact that Components can extend Directives (so\n        // both Directive and Component annotations would be present), so we always check if the known\n        // annotation has the right type.\n        for (let i = annotations.length - 1; i >= 0; i--) {\n            const annotation = annotations[i];\n            const isKnownType = annotation instanceof Directive || annotation instanceof Component ||\n                annotation instanceof Pipe || annotation instanceof NgModule;\n            if (isKnownType) {\n                return annotation instanceof this.type ? annotation : null;\n            }\n        }\n        return null;\n    }\n    resolve(type) {\n        let resolved = this.resolved.get(type) || null;\n        if (!resolved) {\n            resolved = this.getAnnotation(type);\n            if (resolved) {\n                const overrides = this.overrides.get(type);\n                if (overrides) {\n                    const overrider = new MetadataOverrider();\n                    overrides.forEach(override => {\n                        resolved = overrider.overrideMetadata(this.type, resolved, override);\n                    });\n                }\n            }\n            this.resolved.set(type, resolved);\n        }\n        return resolved;\n    }\n}\nclass DirectiveResolver extends OverrideResolver {\n    get type() {\n        return Directive;\n    }\n}\nclass ComponentResolver extends OverrideResolver {\n    get type() {\n        return Component;\n    }\n}\nclass PipeResolver extends OverrideResolver {\n    get type() {\n        return Pipe;\n    }\n}\nclass NgModuleResolver extends OverrideResolver {\n    get type() {\n        return NgModule;\n    }\n}\n\nvar TestingModuleOverride;\n(function (TestingModuleOverride) {\n    TestingModuleOverride[TestingModuleOverride[\"DECLARATION\"] = 0] = \"DECLARATION\";\n    TestingModuleOverride[TestingModuleOverride[\"OVERRIDE_TEMPLATE\"] = 1] = \"OVERRIDE_TEMPLATE\";\n})(TestingModuleOverride || (TestingModuleOverride = {}));\nfunction isTestingModuleOverride(value) {\n    return value === TestingModuleOverride.DECLARATION ||\n        value === TestingModuleOverride.OVERRIDE_TEMPLATE;\n}\nfunction assertNoStandaloneComponents(types, resolver, location) {\n    types.forEach(type => {\n        if (!ɵgetAsyncClassMetadataFn(type)) {\n            const component = resolver.resolve(type);\n            if (component && component.standalone) {\n                throw new Error(ɵgenerateStandaloneInDeclarationsError(type, location));\n            }\n        }\n    });\n}\nclass TestBedCompiler {\n    constructor(platform, additionalModuleTypes) {\n        this.platform = platform;\n        this.additionalModuleTypes = additionalModuleTypes;\n        this.originalComponentResolutionQueue = null;\n        // Testing module configuration\n        this.declarations = [];\n        this.imports = [];\n        this.providers = [];\n        this.schemas = [];\n        // Queues of components/directives/pipes that should be recompiled.\n        this.pendingComponents = new Set();\n        this.pendingDirectives = new Set();\n        this.pendingPipes = new Set();\n        // Set of components with async metadata, i.e. components with `@defer` blocks\n        // in their templates.\n        this.componentsWithAsyncMetadata = new Set();\n        // Keep track of all components and directives, so we can patch Providers onto defs later.\n        this.seenComponents = new Set();\n        this.seenDirectives = new Set();\n        // Keep track of overridden modules, so that we can collect all affected ones in the module tree.\n        this.overriddenModules = new Set();\n        // Store resolved styles for Components that have template overrides present and `styleUrls`\n        // defined at the same time.\n        this.existingComponentStyles = new Map();\n        this.resolvers = initResolvers();\n        // Map of component type to an NgModule that declares it.\n        //\n        // There are a couple special cases:\n        // - for standalone components, the module scope value is `null`\n        // - when a component is declared in `TestBed.configureTestingModule()` call or\n        //   a component's template is overridden via `TestBed.overrideTemplateUsingTestingModule()`.\n        //   we use a special value from the `TestingModuleOverride` enum.\n        this.componentToModuleScope = new Map();\n        // Map that keeps initial version of component/directive/pipe defs in case\n        // we compile a Type again, thus overriding respective static fields. This is\n        // required to make sure we restore defs to their initial states between test runs.\n        // Note: one class may have multiple defs (for example: ɵmod and ɵinj in case of an\n        // NgModule), store all of them in a map.\n        this.initialNgDefs = new Map();\n        // Array that keeps cleanup operations for initial versions of component/directive/pipe/module\n        // defs in case TestBed makes changes to the originals.\n        this.defCleanupOps = [];\n        this._injector = null;\n        this.compilerProviders = null;\n        this.providerOverrides = [];\n        this.rootProviderOverrides = [];\n        // Overrides for injectables with `{providedIn: SomeModule}` need to be tracked and added to that\n        // module's provider list.\n        this.providerOverridesByModule = new Map();\n        this.providerOverridesByToken = new Map();\n        this.scopesWithOverriddenProviders = new Set();\n        this.testModuleRef = null;\n        this.deferBlockBehavior = DEFER_BLOCK_DEFAULT_BEHAVIOR;\n        class DynamicTestModule {\n        }\n        this.testModuleType = DynamicTestModule;\n    }\n    setCompilerProviders(providers) {\n        this.compilerProviders = providers;\n        this._injector = null;\n    }\n    configureTestingModule(moduleDef) {\n        // Enqueue any compilation tasks for the directly declared component.\n        if (moduleDef.declarations !== undefined) {\n            // Verify that there are no standalone components\n            assertNoStandaloneComponents(moduleDef.declarations, this.resolvers.component, '\"TestBed.configureTestingModule\" call');\n            this.queueTypeArray(moduleDef.declarations, TestingModuleOverride.DECLARATION);\n            this.declarations.push(...moduleDef.declarations);\n        }\n        // Enqueue any compilation tasks for imported modules.\n        if (moduleDef.imports !== undefined) {\n            this.queueTypesFromModulesArray(moduleDef.imports);\n            this.imports.push(...moduleDef.imports);\n        }\n        if (moduleDef.providers !== undefined) {\n            this.providers.push(...moduleDef.providers);\n        }\n        if (moduleDef.schemas !== undefined) {\n            this.schemas.push(...moduleDef.schemas);\n        }\n        this.deferBlockBehavior = moduleDef.deferBlockBehavior ?? DEFER_BLOCK_DEFAULT_BEHAVIOR;\n    }\n    overrideModule(ngModule, override) {\n        if (ɵUSE_RUNTIME_DEPS_TRACKER_FOR_JIT) {\n            ɵdepsTracker.clearScopeCacheFor(ngModule);\n        }\n        this.overriddenModules.add(ngModule);\n        // Compile the module right away.\n        this.resolvers.module.addOverride(ngModule, override);\n        const metadata = this.resolvers.module.resolve(ngModule);\n        if (metadata === null) {\n            throw invalidTypeError(ngModule.name, 'NgModule');\n        }\n        this.recompileNgModule(ngModule, metadata);\n        // At this point, the module has a valid module def (ɵmod), but the override may have introduced\n        // new declarations or imported modules. Ingest any possible new types and add them to the\n        // current queue.\n        this.queueTypesFromModulesArray([ngModule]);\n    }\n    overrideComponent(component, override) {\n        this.verifyNoStandaloneFlagOverrides(component, override);\n        this.resolvers.component.addOverride(component, override);\n        this.pendingComponents.add(component);\n        // If this is a component with async metadata (i.e. a component with a `@defer` block\n        // in a template) - store it for future processing.\n        this.maybeRegisterComponentWithAsyncMetadata(component);\n    }\n    overrideDirective(directive, override) {\n        this.verifyNoStandaloneFlagOverrides(directive, override);\n        this.resolvers.directive.addOverride(directive, override);\n        this.pendingDirectives.add(directive);\n    }\n    overridePipe(pipe, override) {\n        this.verifyNoStandaloneFlagOverrides(pipe, override);\n        this.resolvers.pipe.addOverride(pipe, override);\n        this.pendingPipes.add(pipe);\n    }\n    verifyNoStandaloneFlagOverrides(type, override) {\n        if (override.add?.hasOwnProperty('standalone') || override.set?.hasOwnProperty('standalone') ||\n            override.remove?.hasOwnProperty('standalone')) {\n            throw new Error(`An override for the ${type.name} class has the \\`standalone\\` flag. ` +\n                `Changing the \\`standalone\\` flag via TestBed overrides is not supported.`);\n        }\n    }\n    overrideProvider(token, provider) {\n        let providerDef;\n        if (provider.useFactory !== undefined) {\n            providerDef = {\n                provide: token,\n                useFactory: provider.useFactory,\n                deps: provider.deps || [],\n                multi: provider.multi\n            };\n        }\n        else if (provider.useValue !== undefined) {\n            providerDef = { provide: token, useValue: provider.useValue, multi: provider.multi };\n        }\n        else {\n            providerDef = { provide: token };\n        }\n        const injectableDef = typeof token !== 'string' ? ɵgetInjectableDef(token) : null;\n        const providedIn = injectableDef === null ? null : resolveForwardRef(injectableDef.providedIn);\n        const overridesBucket = providedIn === 'root' ? this.rootProviderOverrides : this.providerOverrides;\n        overridesBucket.push(providerDef);\n        // Keep overrides grouped by token as well for fast lookups using token\n        this.providerOverridesByToken.set(token, providerDef);\n        if (injectableDef !== null && providedIn !== null && typeof providedIn !== 'string') {\n            const existingOverrides = this.providerOverridesByModule.get(providedIn);\n            if (existingOverrides !== undefined) {\n                existingOverrides.push(providerDef);\n            }\n            else {\n                this.providerOverridesByModule.set(providedIn, [providerDef]);\n            }\n        }\n    }\n    overrideTemplateUsingTestingModule(type, template) {\n        const def = type[ɵNG_COMP_DEF];\n        const hasStyleUrls = () => {\n            const metadata = this.resolvers.component.resolve(type);\n            return !!metadata.styleUrl || !!metadata.styleUrls?.length;\n        };\n        const overrideStyleUrls = !!def && !ɵisComponentDefPendingResolution(type) && hasStyleUrls();\n        // In Ivy, compiling a component does not require knowing the module providing the\n        // component's scope, so overrideTemplateUsingTestingModule can be implemented purely via\n        // overrideComponent. Important: overriding template requires full Component re-compilation,\n        // which may fail in case styleUrls are also present (thus Component is considered as required\n        // resolution). In order to avoid this, we preemptively set styleUrls to an empty array,\n        // preserve current styles available on Component def and restore styles back once compilation\n        // is complete.\n        const override = overrideStyleUrls ? { template, styles: [], styleUrls: [], styleUrl: undefined } : { template };\n        this.overrideComponent(type, { set: override });\n        if (overrideStyleUrls && def.styles && def.styles.length > 0) {\n            this.existingComponentStyles.set(type, def.styles);\n        }\n        // Set the component's scope to be the testing module.\n        this.componentToModuleScope.set(type, TestingModuleOverride.OVERRIDE_TEMPLATE);\n    }\n    async resolvePendingComponentsWithAsyncMetadata() {\n        if (this.componentsWithAsyncMetadata.size === 0)\n            return;\n        const promises = [];\n        for (const component of this.componentsWithAsyncMetadata) {\n            const asyncMetadataFn = ɵgetAsyncClassMetadataFn(component);\n            if (asyncMetadataFn) {\n                promises.push(asyncMetadataFn());\n            }\n        }\n        this.componentsWithAsyncMetadata.clear();\n        const resolvedDeps = await Promise.all(promises);\n        const flatResolvedDeps = resolvedDeps.flat(2);\n        this.queueTypesFromModulesArray(flatResolvedDeps);\n        // Loaded standalone components might contain imports of NgModules\n        // with providers, make sure we override providers there too.\n        for (const component of flatResolvedDeps) {\n            this.applyProviderOverridesInScope(component);\n        }\n    }\n    async compileComponents() {\n        this.clearComponentResolutionQueue();\n        // Wait for all async metadata for components that were\n        // overridden, we need resolved metadata to perform an override\n        // and re-compile a component.\n        await this.resolvePendingComponentsWithAsyncMetadata();\n        // Verify that there were no standalone components present in the `declarations` field\n        // during the `TestBed.configureTestingModule` call. We perform this check here in addition\n        // to the logic in the `configureTestingModule` function, since at this point we have\n        // all async metadata resolved.\n        assertNoStandaloneComponents(this.declarations, this.resolvers.component, '\"TestBed.configureTestingModule\" call');\n        // Run compilers for all queued types.\n        let needsAsyncResources = this.compileTypesSync();\n        // compileComponents() should not be async unless it needs to be.\n        if (needsAsyncResources) {\n            let resourceLoader;\n            let resolver = (url) => {\n                if (!resourceLoader) {\n                    resourceLoader = this.injector.get(ResourceLoader);\n                }\n                return Promise.resolve(resourceLoader.get(url));\n            };\n            await ɵresolveComponentResources(resolver);\n        }\n    }\n    finalize() {\n        // One last compile\n        this.compileTypesSync();\n        // Create the testing module itself.\n        this.compileTestModule();\n        this.applyTransitiveScopes();\n        this.applyProviderOverrides();\n        // Patch previously stored `styles` Component values (taken from ɵcmp), in case these\n        // Components have `styleUrls` fields defined and template override was requested.\n        this.patchComponentsWithExistingStyles();\n        // Clear the componentToModuleScope map, so that future compilations don't reset the scope of\n        // every component.\n        this.componentToModuleScope.clear();\n        const parentInjector = this.platform.injector;\n        this.testModuleRef = new ɵRender3NgModuleRef(this.testModuleType, parentInjector, []);\n        // ApplicationInitStatus.runInitializers() is marked @internal to core.\n        // Cast it to any before accessing it.\n        this.testModuleRef.injector.get(ApplicationInitStatus).runInitializers();\n        // Set locale ID after running app initializers, since locale information might be updated while\n        // running initializers. This is also consistent with the execution order while bootstrapping an\n        // app (see `packages/core/src/application_ref.ts` file).\n        const localeId = this.testModuleRef.injector.get(LOCALE_ID, ɵDEFAULT_LOCALE_ID);\n        ɵsetLocaleId(localeId);\n        return this.testModuleRef;\n    }\n    /**\n     * @internal\n     */\n    _compileNgModuleSync(moduleType) {\n        this.queueTypesFromModulesArray([moduleType]);\n        this.compileTypesSync();\n        this.applyProviderOverrides();\n        this.applyProviderOverridesInScope(moduleType);\n        this.applyTransitiveScopes();\n    }\n    /**\n     * @internal\n     */\n    async _compileNgModuleAsync(moduleType) {\n        this.queueTypesFromModulesArray([moduleType]);\n        await this.compileComponents();\n        this.applyProviderOverrides();\n        this.applyProviderOverridesInScope(moduleType);\n        this.applyTransitiveScopes();\n    }\n    /**\n     * @internal\n     */\n    _getModuleResolver() {\n        return this.resolvers.module;\n    }\n    /**\n     * @internal\n     */\n    _getComponentFactories(moduleType) {\n        return maybeUnwrapFn(moduleType.ɵmod.declarations).reduce((factories, declaration) => {\n            const componentDef = declaration.ɵcmp;\n            componentDef && factories.push(new ɵRender3ComponentFactory(componentDef, this.testModuleRef));\n            return factories;\n        }, []);\n    }\n    compileTypesSync() {\n        // Compile all queued components, directives, pipes.\n        let needsAsyncResources = false;\n        this.pendingComponents.forEach(declaration => {\n            if (ɵgetAsyncClassMetadataFn(declaration)) {\n                throw new Error(`Component '${declaration.name}' has unresolved metadata. ` +\n                    `Please call \\`await TestBed.compileComponents()\\` before running this test.`);\n            }\n            needsAsyncResources = needsAsyncResources || ɵisComponentDefPendingResolution(declaration);\n            const metadata = this.resolvers.component.resolve(declaration);\n            if (metadata === null) {\n                throw invalidTypeError(declaration.name, 'Component');\n            }\n            this.maybeStoreNgDef(ɵNG_COMP_DEF, declaration);\n            if (ɵUSE_RUNTIME_DEPS_TRACKER_FOR_JIT) {\n                ɵdepsTracker.clearScopeCacheFor(declaration);\n            }\n            ɵcompileComponent(declaration, metadata);\n        });\n        this.pendingComponents.clear();\n        this.pendingDirectives.forEach(declaration => {\n            const metadata = this.resolvers.directive.resolve(declaration);\n            if (metadata === null) {\n                throw invalidTypeError(declaration.name, 'Directive');\n            }\n            this.maybeStoreNgDef(ɵNG_DIR_DEF, declaration);\n            ɵcompileDirective(declaration, metadata);\n        });\n        this.pendingDirectives.clear();\n        this.pendingPipes.forEach(declaration => {\n            const metadata = this.resolvers.pipe.resolve(declaration);\n            if (metadata === null) {\n                throw invalidTypeError(declaration.name, 'Pipe');\n            }\n            this.maybeStoreNgDef(ɵNG_PIPE_DEF, declaration);\n            ɵcompilePipe(declaration, metadata);\n        });\n        this.pendingPipes.clear();\n        return needsAsyncResources;\n    }\n    applyTransitiveScopes() {\n        if (this.overriddenModules.size > 0) {\n            // Module overrides (via `TestBed.overrideModule`) might affect scopes that were previously\n            // calculated and stored in `transitiveCompileScopes`. If module overrides are present,\n            // collect all affected modules and reset scopes to force their re-calculation.\n            const testingModuleDef = this.testModuleType[ɵNG_MOD_DEF];\n            const affectedModules = this.collectModulesAffectedByOverrides(testingModuleDef.imports);\n            if (affectedModules.size > 0) {\n                affectedModules.forEach(moduleType => {\n                    if (!ɵUSE_RUNTIME_DEPS_TRACKER_FOR_JIT) {\n                        this.storeFieldOfDefOnType(moduleType, ɵNG_MOD_DEF, 'transitiveCompileScopes');\n                        moduleType[ɵNG_MOD_DEF].transitiveCompileScopes = null;\n                    }\n                    else {\n                        ɵdepsTracker.clearScopeCacheFor(moduleType);\n                    }\n                });\n            }\n        }\n        const moduleToScope = new Map();\n        const getScopeOfModule = (moduleType) => {\n            if (!moduleToScope.has(moduleType)) {\n                const isTestingModule = isTestingModuleOverride(moduleType);\n                const realType = isTestingModule ? this.testModuleType : moduleType;\n                moduleToScope.set(moduleType, ɵtransitiveScopesFor(realType));\n            }\n            return moduleToScope.get(moduleType);\n        };\n        this.componentToModuleScope.forEach((moduleType, componentType) => {\n            if (moduleType !== null) {\n                const moduleScope = getScopeOfModule(moduleType);\n                this.storeFieldOfDefOnType(componentType, ɵNG_COMP_DEF, 'directiveDefs');\n                this.storeFieldOfDefOnType(componentType, ɵNG_COMP_DEF, 'pipeDefs');\n                ɵpatchComponentDefWithScope(getComponentDef(componentType), moduleScope);\n            }\n            // `tView` that is stored on component def contains information about directives and pipes\n            // that are in the scope of this component. Patching component scope will cause `tView` to be\n            // changed. Store original `tView` before patching scope, so the `tView` (including scope\n            // information) is restored back to its previous/original state before running next test.\n            // Resetting `tView` is also needed for cases when we apply provider overrides and those\n            // providers are defined on component's level, in which case they may end up included into\n            // `tView.blueprint`.\n            this.storeFieldOfDefOnType(componentType, ɵNG_COMP_DEF, 'tView');\n        });\n        this.componentToModuleScope.clear();\n    }\n    applyProviderOverrides() {\n        const maybeApplyOverrides = (field) => (type) => {\n            const resolver = field === ɵNG_COMP_DEF ? this.resolvers.component : this.resolvers.directive;\n            const metadata = resolver.resolve(type);\n            if (this.hasProviderOverrides(metadata.providers)) {\n                this.patchDefWithProviderOverrides(type, field);\n            }\n        };\n        this.seenComponents.forEach(maybeApplyOverrides(ɵNG_COMP_DEF));\n        this.seenDirectives.forEach(maybeApplyOverrides(ɵNG_DIR_DEF));\n        this.seenComponents.clear();\n        this.seenDirectives.clear();\n    }\n    /**\n     * Applies provider overrides to a given type (either an NgModule or a standalone component)\n     * and all imported NgModules and standalone components recursively.\n     */\n    applyProviderOverridesInScope(type) {\n        const hasScope = isStandaloneComponent(type) || isNgModule(type);\n        // The function can be re-entered recursively while inspecting dependencies\n        // of an NgModule or a standalone component. Exit early if we come across a\n        // type that can not have a scope (directive or pipe) or the type is already\n        // processed earlier.\n        if (!hasScope || this.scopesWithOverriddenProviders.has(type)) {\n            return;\n        }\n        this.scopesWithOverriddenProviders.add(type);\n        // NOTE: the line below triggers JIT compilation of the module injector,\n        // which also invokes verification of the NgModule semantics, which produces\n        // detailed error messages. The fact that the code relies on this line being\n        // present here is suspicious and should be refactored in a way that the line\n        // below can be moved (for ex. after an early exit check below).\n        const injectorDef = type[ɵNG_INJ_DEF];\n        // No provider overrides, exit early.\n        if (this.providerOverridesByToken.size === 0)\n            return;\n        if (isStandaloneComponent(type)) {\n            // Visit all component dependencies and override providers there.\n            const def = getComponentDef(type);\n            const dependencies = maybeUnwrapFn(def.dependencies ?? []);\n            for (const dependency of dependencies) {\n                this.applyProviderOverridesInScope(dependency);\n            }\n        }\n        else {\n            const providers = [\n                ...injectorDef.providers,\n                ...(this.providerOverridesByModule.get(type) || [])\n            ];\n            if (this.hasProviderOverrides(providers)) {\n                this.maybeStoreNgDef(ɵNG_INJ_DEF, type);\n                this.storeFieldOfDefOnType(type, ɵNG_INJ_DEF, 'providers');\n                injectorDef.providers = this.getOverriddenProviders(providers);\n            }\n            // Apply provider overrides to imported modules recursively\n            const moduleDef = type[ɵNG_MOD_DEF];\n            const imports = maybeUnwrapFn(moduleDef.imports);\n            for (const importedModule of imports) {\n                this.applyProviderOverridesInScope(importedModule);\n            }\n            // Also override the providers on any ModuleWithProviders imports since those don't appear in\n            // the moduleDef.\n            for (const importedModule of flatten(injectorDef.imports)) {\n                if (isModuleWithProviders(importedModule)) {\n                    this.defCleanupOps.push({\n                        object: importedModule,\n                        fieldName: 'providers',\n                        originalValue: importedModule.providers\n                    });\n                    importedModule.providers = this.getOverriddenProviders(importedModule.providers);\n                }\n            }\n        }\n    }\n    patchComponentsWithExistingStyles() {\n        this.existingComponentStyles.forEach((styles, type) => type[ɵNG_COMP_DEF].styles = styles);\n        this.existingComponentStyles.clear();\n    }\n    queueTypeArray(arr, moduleType) {\n        for (const value of arr) {\n            if (Array.isArray(value)) {\n                this.queueTypeArray(value, moduleType);\n            }\n            else {\n                this.queueType(value, moduleType);\n            }\n        }\n    }\n    recompileNgModule(ngModule, metadata) {\n        // Cache the initial ngModuleDef as it will be overwritten.\n        this.maybeStoreNgDef(ɵNG_MOD_DEF, ngModule);\n        this.maybeStoreNgDef(ɵNG_INJ_DEF, ngModule);\n        ɵcompileNgModuleDefs(ngModule, metadata);\n    }\n    maybeRegisterComponentWithAsyncMetadata(type) {\n        const asyncMetadataFn = ɵgetAsyncClassMetadataFn(type);\n        if (asyncMetadataFn) {\n            this.componentsWithAsyncMetadata.add(type);\n        }\n    }\n    queueType(type, moduleType) {\n        // If this is a component with async metadata (i.e. a component with a `@defer` block\n        // in a template) - store it for future processing.\n        this.maybeRegisterComponentWithAsyncMetadata(type);\n        const component = this.resolvers.component.resolve(type);\n        if (component) {\n            // Check whether a give Type has respective NG def (ɵcmp) and compile if def is\n            // missing. That might happen in case a class without any Angular decorators extends another\n            // class where Component/Directive/Pipe decorator is defined.\n            if (ɵisComponentDefPendingResolution(type) || !type.hasOwnProperty(ɵNG_COMP_DEF)) {\n                this.pendingComponents.add(type);\n            }\n            this.seenComponents.add(type);\n            // Keep track of the module which declares this component, so later the component's scope\n            // can be set correctly. If the component has already been recorded here, then one of several\n            // cases is true:\n            // * the module containing the component was imported multiple times (common).\n            // * the component is declared in multiple modules (which is an error).\n            // * the component was in 'declarations' of the testing module, and also in an imported module\n            //   in which case the module scope will be TestingModuleOverride.DECLARATION.\n            // * overrideTemplateUsingTestingModule was called for the component in which case the module\n            //   scope will be TestingModuleOverride.OVERRIDE_TEMPLATE.\n            //\n            // If the component was previously in the testing module's 'declarations' (meaning the\n            // current value is TestingModuleOverride.DECLARATION), then `moduleType` is the component's\n            // real module, which was imported. This pattern is understood to mean that the component\n            // should use its original scope, but that the testing module should also contain the\n            // component in its scope.\n            if ((!this.componentToModuleScope.has(type) ||\n                this.componentToModuleScope.get(type) === TestingModuleOverride.DECLARATION)) {\n                this.componentToModuleScope.set(type, moduleType);\n            }\n            return;\n        }\n        const directive = this.resolvers.directive.resolve(type);\n        if (directive) {\n            if (!type.hasOwnProperty(ɵNG_DIR_DEF)) {\n                this.pendingDirectives.add(type);\n            }\n            this.seenDirectives.add(type);\n            return;\n        }\n        const pipe = this.resolvers.pipe.resolve(type);\n        if (pipe && !type.hasOwnProperty(ɵNG_PIPE_DEF)) {\n            this.pendingPipes.add(type);\n            return;\n        }\n    }\n    queueTypesFromModulesArray(arr) {\n        // Because we may encounter the same NgModule or a standalone Component while processing\n        // the dependencies of an NgModule or a standalone Component, we cache them in this set so we\n        // can skip ones that have already been seen encountered. In some test setups, this caching\n        // resulted in 10X runtime improvement.\n        const processedDefs = new Set();\n        const queueTypesFromModulesArrayRecur = (arr) => {\n            for (const value of arr) {\n                if (Array.isArray(value)) {\n                    queueTypesFromModulesArrayRecur(value);\n                }\n                else if (hasNgModuleDef(value)) {\n                    const def = value.ɵmod;\n                    if (processedDefs.has(def)) {\n                        continue;\n                    }\n                    processedDefs.add(def);\n                    // Look through declarations, imports, and exports, and queue\n                    // everything found there.\n                    this.queueTypeArray(maybeUnwrapFn(def.declarations), value);\n                    queueTypesFromModulesArrayRecur(maybeUnwrapFn(def.imports));\n                    queueTypesFromModulesArrayRecur(maybeUnwrapFn(def.exports));\n                }\n                else if (isModuleWithProviders(value)) {\n                    queueTypesFromModulesArrayRecur([value.ngModule]);\n                }\n                else if (isStandaloneComponent(value)) {\n                    this.queueType(value, null);\n                    const def = getComponentDef(value);\n                    if (processedDefs.has(def)) {\n                        continue;\n                    }\n                    processedDefs.add(def);\n                    const dependencies = maybeUnwrapFn(def.dependencies ?? []);\n                    dependencies.forEach((dependency) => {\n                        // Note: in AOT, the `dependencies` might also contain regular\n                        // (NgModule-based) Component, Directive and Pipes, so we handle\n                        // them separately and proceed with recursive process for standalone\n                        // Components and NgModules only.\n                        if (isStandaloneComponent(dependency) || hasNgModuleDef(dependency)) {\n                            queueTypesFromModulesArrayRecur([dependency]);\n                        }\n                        else {\n                            this.queueType(dependency, null);\n                        }\n                    });\n                }\n            }\n        };\n        queueTypesFromModulesArrayRecur(arr);\n    }\n    // When module overrides (via `TestBed.overrideModule`) are present, it might affect all modules\n    // that import (even transitively) an overridden one. For all affected modules we need to\n    // recalculate their scopes for a given test run and restore original scopes at the end. The goal\n    // of this function is to collect all affected modules in a set for further processing. Example:\n    // if we have the following module hierarchy: A -> B -> C (where `->` means `imports`) and module\n    // `C` is overridden, we consider `A` and `B` as affected, since their scopes might become\n    // invalidated with the override.\n    collectModulesAffectedByOverrides(arr) {\n        const seenModules = new Set();\n        const affectedModules = new Set();\n        const calcAffectedModulesRecur = (arr, path) => {\n            for (const value of arr) {\n                if (Array.isArray(value)) {\n                    // If the value is an array, just flatten it (by invoking this function recursively),\n                    // keeping \"path\" the same.\n                    calcAffectedModulesRecur(value, path);\n                }\n                else if (hasNgModuleDef(value)) {\n                    if (seenModules.has(value)) {\n                        // If we've seen this module before and it's included into \"affected modules\" list, mark\n                        // the whole path that leads to that module as affected, but do not descend into its\n                        // imports, since we already examined them before.\n                        if (affectedModules.has(value)) {\n                            path.forEach(item => affectedModules.add(item));\n                        }\n                        continue;\n                    }\n                    seenModules.add(value);\n                    if (this.overriddenModules.has(value)) {\n                        path.forEach(item => affectedModules.add(item));\n                    }\n                    // Examine module imports recursively to look for overridden modules.\n                    const moduleDef = value[ɵNG_MOD_DEF];\n                    calcAffectedModulesRecur(maybeUnwrapFn(moduleDef.imports), path.concat(value));\n                }\n            }\n        };\n        calcAffectedModulesRecur(arr, []);\n        return affectedModules;\n    }\n    /**\n     * Preserve an original def (such as ɵmod, ɵinj, etc) before applying an override.\n     * Note: one class may have multiple defs (for example: ɵmod and ɵinj in case of\n     * an NgModule). If there is a def in a set already, don't override it, since\n     * an original one should be restored at the end of a test.\n     */\n    maybeStoreNgDef(prop, type) {\n        if (!this.initialNgDefs.has(type)) {\n            this.initialNgDefs.set(type, new Map());\n        }\n        const currentDefs = this.initialNgDefs.get(type);\n        if (!currentDefs.has(prop)) {\n            const currentDef = Object.getOwnPropertyDescriptor(type, prop);\n            currentDefs.set(prop, currentDef);\n        }\n    }\n    storeFieldOfDefOnType(type, defField, fieldName) {\n        const def = type[defField];\n        const originalValue = def[fieldName];\n        this.defCleanupOps.push({ object: def, fieldName, originalValue });\n    }\n    /**\n     * Clears current components resolution queue, but stores the state of the queue, so we can\n     * restore it later. Clearing the queue is required before we try to compile components (via\n     * `TestBed.compileComponents`), so that component defs are in sync with the resolution queue.\n     */\n    clearComponentResolutionQueue() {\n        if (this.originalComponentResolutionQueue === null) {\n            this.originalComponentResolutionQueue = new Map();\n        }\n        ɵclearResolutionOfComponentResourcesQueue().forEach((value, key) => this.originalComponentResolutionQueue.set(key, value));\n    }\n    /*\n     * Restores component resolution queue to the previously saved state. This operation is performed\n     * as a part of restoring the state after completion of the current set of tests (that might\n     * potentially mutate the state).\n     */\n    restoreComponentResolutionQueue() {\n        if (this.originalComponentResolutionQueue !== null) {\n            ɵrestoreComponentResolutionQueue(this.originalComponentResolutionQueue);\n            this.originalComponentResolutionQueue = null;\n        }\n    }\n    restoreOriginalState() {\n        // Process cleanup ops in reverse order so the field's original value is restored correctly (in\n        // case there were multiple overrides for the same field).\n        forEachRight(this.defCleanupOps, (op) => {\n            op.object[op.fieldName] = op.originalValue;\n        });\n        // Restore initial component/directive/pipe defs\n        this.initialNgDefs.forEach((defs, type) => {\n            if (ɵUSE_RUNTIME_DEPS_TRACKER_FOR_JIT) {\n                ɵdepsTracker.clearScopeCacheFor(type);\n            }\n            defs.forEach((descriptor, prop) => {\n                if (!descriptor) {\n                    // Delete operations are generally undesirable since they have performance\n                    // implications on objects they were applied to. In this particular case, situations\n                    // where this code is invoked should be quite rare to cause any noticeable impact,\n                    // since it's applied only to some test cases (for example when class with no\n                    // annotations extends some @Component) when we need to clear 'ɵcmp' field on a given\n                    // class to restore its original state (before applying overrides and running tests).\n                    delete type[prop];\n                }\n                else {\n                    Object.defineProperty(type, prop, descriptor);\n                }\n            });\n        });\n        this.initialNgDefs.clear();\n        this.scopesWithOverriddenProviders.clear();\n        this.restoreComponentResolutionQueue();\n        // Restore the locale ID to the default value, this shouldn't be necessary but we never know\n        ɵsetLocaleId(ɵDEFAULT_LOCALE_ID);\n    }\n    compileTestModule() {\n        class RootScopeModule {\n        }\n        ɵcompileNgModuleDefs(RootScopeModule, {\n            providers: [...this.rootProviderOverrides],\n        });\n        const providers = [\n            provideZoneChangeDetection(),\n            { provide: Compiler, useFactory: () => new R3TestCompiler(this) },\n            { provide: ɵDEFER_BLOCK_CONFIG, useValue: { behavior: this.deferBlockBehavior } },\n            ...this.providers,\n            ...this.providerOverrides,\n        ];\n        const imports = [RootScopeModule, this.additionalModuleTypes, this.imports || []];\n        // clang-format off\n        ɵcompileNgModuleDefs(this.testModuleType, {\n            declarations: this.declarations,\n            imports,\n            schemas: this.schemas,\n            providers,\n        }, /* allowDuplicateDeclarationsInRoot */ true);\n        // clang-format on\n        this.applyProviderOverridesInScope(this.testModuleType);\n    }\n    get injector() {\n        if (this._injector !== null) {\n            return this._injector;\n        }\n        const providers = [];\n        const compilerOptions = this.platform.injector.get(COMPILER_OPTIONS);\n        compilerOptions.forEach(opts => {\n            if (opts.providers) {\n                providers.push(opts.providers);\n            }\n        });\n        if (this.compilerProviders !== null) {\n            providers.push(...this.compilerProviders);\n        }\n        this._injector = Injector.create({ providers, parent: this.platform.injector });\n        return this._injector;\n    }\n    // get overrides for a specific provider (if any)\n    getSingleProviderOverrides(provider) {\n        const token = getProviderToken(provider);\n        return this.providerOverridesByToken.get(token) || null;\n    }\n    getProviderOverrides(providers) {\n        if (!providers || !providers.length || this.providerOverridesByToken.size === 0)\n            return [];\n        // There are two flattening operations here. The inner flattenProviders() operates on the\n        // metadata's providers and applies a mapping function which retrieves overrides for each\n        // incoming provider. The outer flatten() then flattens the produced overrides array. If this is\n        // not done, the array can contain other empty arrays (e.g. `[[], []]`) which leak into the\n        // providers array and contaminate any error messages that might be generated.\n        return flatten(flattenProviders(providers, (provider) => this.getSingleProviderOverrides(provider) || []));\n    }\n    getOverriddenProviders(providers) {\n        if (!providers || !providers.length || this.providerOverridesByToken.size === 0)\n            return [];\n        const flattenedProviders = flattenProviders(providers);\n        const overrides = this.getProviderOverrides(flattenedProviders);\n        const overriddenProviders = [...flattenedProviders, ...overrides];\n        const final = [];\n        const seenOverriddenProviders = new Set();\n        // We iterate through the list of providers in reverse order to make sure provider overrides\n        // take precedence over the values defined in provider list. We also filter out all providers\n        // that have overrides, keeping overridden values only. This is needed, since presence of a\n        // provider with `ngOnDestroy` hook will cause this hook to be registered and invoked later.\n        forEachRight(overriddenProviders, (provider) => {\n            const token = getProviderToken(provider);\n            if (this.providerOverridesByToken.has(token)) {\n                if (!seenOverriddenProviders.has(token)) {\n                    seenOverriddenProviders.add(token);\n                    // Treat all overridden providers as `{multi: false}` (even if it's a multi-provider) to\n                    // make sure that provided override takes highest precedence and is not combined with\n                    // other instances of the same multi provider.\n                    final.unshift({ ...provider, multi: false });\n                }\n            }\n            else {\n                final.unshift(provider);\n            }\n        });\n        return final;\n    }\n    hasProviderOverrides(providers) {\n        return this.getProviderOverrides(providers).length > 0;\n    }\n    patchDefWithProviderOverrides(declaration, field) {\n        const def = declaration[field];\n        if (def && def.providersResolver) {\n            this.maybeStoreNgDef(field, declaration);\n            const resolver = def.providersResolver;\n            const processProvidersFn = (providers) => this.getOverriddenProviders(providers);\n            this.storeFieldOfDefOnType(declaration, field, 'providersResolver');\n            def.providersResolver = (ngDef) => resolver(ngDef, processProvidersFn);\n        }\n    }\n}\nfunction initResolvers() {\n    return {\n        module: new NgModuleResolver(),\n        component: new ComponentResolver(),\n        directive: new DirectiveResolver(),\n        pipe: new PipeResolver()\n    };\n}\nfunction isStandaloneComponent(value) {\n    const def = getComponentDef(value);\n    return !!def?.standalone;\n}\nfunction getComponentDef(value) {\n    return value.ɵcmp ?? null;\n}\nfunction hasNgModuleDef(value) {\n    return value.hasOwnProperty('ɵmod');\n}\nfunction isNgModule(value) {\n    return hasNgModuleDef(value);\n}\nfunction maybeUnwrapFn(maybeFn) {\n    return maybeFn instanceof Function ? maybeFn() : maybeFn;\n}\nfunction flatten(values) {\n    const out = [];\n    values.forEach(value => {\n        if (Array.isArray(value)) {\n            out.push(...flatten(value));\n        }\n        else {\n            out.push(value);\n        }\n    });\n    return out;\n}\nfunction identityFn(value) {\n    return value;\n}\nfunction flattenProviders(providers, mapFn = identityFn) {\n    const out = [];\n    for (let provider of providers) {\n        if (ɵisEnvironmentProviders(provider)) {\n            provider = provider.ɵproviders;\n        }\n        if (Array.isArray(provider)) {\n            out.push(...flattenProviders(provider, mapFn));\n        }\n        else {\n            out.push(mapFn(provider));\n        }\n    }\n    return out;\n}\nfunction getProviderField(provider, field) {\n    return provider && typeof provider === 'object' && provider[field];\n}\nfunction getProviderToken(provider) {\n    return getProviderField(provider, 'provide') || provider;\n}\nfunction isModuleWithProviders(value) {\n    return value.hasOwnProperty('ngModule');\n}\nfunction forEachRight(values, fn) {\n    for (let idx = values.length - 1; idx >= 0; idx--) {\n        fn(values[idx], idx);\n    }\n}\nfunction invalidTypeError(name, expectedType) {\n    return new Error(`${name} class doesn't have @${expectedType} decorator or is missing metadata.`);\n}\nclass R3TestCompiler {\n    constructor(testBed) {\n        this.testBed = testBed;\n    }\n    compileModuleSync(moduleType) {\n        this.testBed._compileNgModuleSync(moduleType);\n        return new ɵNgModuleFactory(moduleType);\n    }\n    async compileModuleAsync(moduleType) {\n        await this.testBed._compileNgModuleAsync(moduleType);\n        return new ɵNgModuleFactory(moduleType);\n    }\n    compileModuleAndAllComponentsSync(moduleType) {\n        const ngModuleFactory = this.compileModuleSync(moduleType);\n        const componentFactories = this.testBed._getComponentFactories(moduleType);\n        return new ModuleWithComponentFactories(ngModuleFactory, componentFactories);\n    }\n    async compileModuleAndAllComponentsAsync(moduleType) {\n        const ngModuleFactory = await this.compileModuleAsync(moduleType);\n        const componentFactories = this.testBed._getComponentFactories(moduleType);\n        return new ModuleWithComponentFactories(ngModuleFactory, componentFactories);\n    }\n    clearCache() { }\n    clearCacheFor(type) { }\n    getModuleId(moduleType) {\n        const meta = this.testBed._getModuleResolver().resolve(moduleType);\n        return meta && meta.id || undefined;\n    }\n}\n\n// The formatter and CI disagree on how this import statement should be formatted. Both try to keep\nlet _nextRootElementId = 0;\n/**\n * Returns a singleton of the `TestBed` class.\n *\n * @publicApi\n */\nfunction getTestBed() {\n    return TestBedImpl.INSTANCE;\n}\n/**\n * @description\n * Configures and initializes environment for unit testing and provides methods for\n * creating components and services in unit tests.\n *\n * TestBed is the primary api for writing unit tests for Angular applications and libraries.\n */\nclass TestBedImpl {\n    constructor() {\n        /**\n         * Defer block behavior option that specifies whether defer blocks will be triggered manually\n         * or set to play through.\n         */\n        this._instanceDeferBlockBehavior = DEFER_BLOCK_DEFAULT_BEHAVIOR;\n        // Properties\n        this.platform = null;\n        this.ngModule = null;\n        this._compiler = null;\n        this._testModuleRef = null;\n        this._activeFixtures = [];\n        /**\n         * Internal-only flag to indicate whether a module\n         * scoping queue has been checked and flushed already.\n         * @nodoc\n         */\n        this.globalCompilationChecked = false;\n    }\n    static { this._INSTANCE = null; }\n    static get INSTANCE() {\n        return TestBedImpl._INSTANCE = TestBedImpl._INSTANCE || new TestBedImpl();\n    }\n    /**\n     * Initialize the environment for testing with a compiler factory, a PlatformRef, and an\n     * angular module. These are common to every test in the suite.\n     *\n     * This may only be called once, to set up the common providers for the current test\n     * suite on the current platform. If you absolutely need to change the providers,\n     * first use `resetTestEnvironment`.\n     *\n     * Test modules and platforms for individual platforms are available from\n     * '@angular/<platform_name>/testing'.\n     *\n     * @publicApi\n     */\n    static initTestEnvironment(ngModule, platform, options) {\n        const testBed = TestBedImpl.INSTANCE;\n        testBed.initTestEnvironment(ngModule, platform, options);\n        return testBed;\n    }\n    /**\n     * Reset the providers for the test injector.\n     *\n     * @publicApi\n     */\n    static resetTestEnvironment() {\n        TestBedImpl.INSTANCE.resetTestEnvironment();\n    }\n    static configureCompiler(config) {\n        return TestBedImpl.INSTANCE.configureCompiler(config);\n    }\n    /**\n     * Allows overriding default providers, directives, pipes, modules of the test injector,\n     * which are defined in test_injector.js\n     */\n    static configureTestingModule(moduleDef) {\n        return TestBedImpl.INSTANCE.configureTestingModule(moduleDef);\n    }\n    /**\n     * Compile components with a `templateUrl` for the test's NgModule.\n     * It is necessary to call this function\n     * as fetching urls is asynchronous.\n     */\n    static compileComponents() {\n        return TestBedImpl.INSTANCE.compileComponents();\n    }\n    static overrideModule(ngModule, override) {\n        return TestBedImpl.INSTANCE.overrideModule(ngModule, override);\n    }\n    static overrideComponent(component, override) {\n        return TestBedImpl.INSTANCE.overrideComponent(component, override);\n    }\n    static overrideDirective(directive, override) {\n        return TestBedImpl.INSTANCE.overrideDirective(directive, override);\n    }\n    static overridePipe(pipe, override) {\n        return TestBedImpl.INSTANCE.overridePipe(pipe, override);\n    }\n    static overrideTemplate(component, template) {\n        return TestBedImpl.INSTANCE.overrideTemplate(component, template);\n    }\n    /**\n     * Overrides the template of the given component, compiling the template\n     * in the context of the TestingModule.\n     *\n     * Note: This works for JIT and AOTed components as well.\n     */\n    static overrideTemplateUsingTestingModule(component, template) {\n        return TestBedImpl.INSTANCE.overrideTemplateUsingTestingModule(component, template);\n    }\n    static overrideProvider(token, provider) {\n        return TestBedImpl.INSTANCE.overrideProvider(token, provider);\n    }\n    static inject(token, notFoundValue, flags) {\n        return TestBedImpl.INSTANCE.inject(token, notFoundValue, ɵconvertToBitFlags(flags));\n    }\n    /** @deprecated from v9.0.0 use TestBed.inject */\n    static get(token, notFoundValue = Injector.THROW_IF_NOT_FOUND, flags = InjectFlags.Default) {\n        return TestBedImpl.INSTANCE.inject(token, notFoundValue, flags);\n    }\n    /**\n     * Runs the given function in the `EnvironmentInjector` context of `TestBed`.\n     *\n     * @see {@link EnvironmentInjector#runInContext}\n     */\n    static runInInjectionContext(fn) {\n        return TestBedImpl.INSTANCE.runInInjectionContext(fn);\n    }\n    static createComponent(component) {\n        return TestBedImpl.INSTANCE.createComponent(component);\n    }\n    static resetTestingModule() {\n        return TestBedImpl.INSTANCE.resetTestingModule();\n    }\n    static execute(tokens, fn, context) {\n        return TestBedImpl.INSTANCE.execute(tokens, fn, context);\n    }\n    static get platform() {\n        return TestBedImpl.INSTANCE.platform;\n    }\n    static get ngModule() {\n        return TestBedImpl.INSTANCE.ngModule;\n    }\n    static flushEffects() {\n        return TestBedImpl.INSTANCE.flushEffects();\n    }\n    /**\n     * Initialize the environment for testing with a compiler factory, a PlatformRef, and an\n     * angular module. These are common to every test in the suite.\n     *\n     * This may only be called once, to set up the common providers for the current test\n     * suite on the current platform. If you absolutely need to change the providers,\n     * first use `resetTestEnvironment`.\n     *\n     * Test modules and platforms for individual platforms are available from\n     * '@angular/<platform_name>/testing'.\n     *\n     * @publicApi\n     */\n    initTestEnvironment(ngModule, platform, options) {\n        if (this.platform || this.ngModule) {\n            throw new Error('Cannot set base providers because it has already been called');\n        }\n        TestBedImpl._environmentTeardownOptions = options?.teardown;\n        TestBedImpl._environmentErrorOnUnknownElementsOption = options?.errorOnUnknownElements;\n        TestBedImpl._environmentErrorOnUnknownPropertiesOption = options?.errorOnUnknownProperties;\n        this.platform = platform;\n        this.ngModule = ngModule;\n        this._compiler = new TestBedCompiler(this.platform, this.ngModule);\n        // TestBed does not have an API which can reliably detect the start of a test, and thus could be\n        // used to track the state of the NgModule registry and reset it correctly. Instead, when we\n        // know we're in a testing scenario, we disable the check for duplicate NgModule registration\n        // completely.\n        ɵsetAllowDuplicateNgModuleIdsForTest(true);\n    }\n    /**\n     * Reset the providers for the test injector.\n     *\n     * @publicApi\n     */\n    resetTestEnvironment() {\n        this.resetTestingModule();\n        this._compiler = null;\n        this.platform = null;\n        this.ngModule = null;\n        TestBedImpl._environmentTeardownOptions = undefined;\n        ɵsetAllowDuplicateNgModuleIdsForTest(false);\n    }\n    resetTestingModule() {\n        this.checkGlobalCompilationFinished();\n        ɵresetCompiledComponents();\n        if (this._compiler !== null) {\n            this.compiler.restoreOriginalState();\n        }\n        this._compiler = new TestBedCompiler(this.platform, this.ngModule);\n        // Restore the previous value of the \"error on unknown elements\" option\n        ɵsetUnknownElementStrictMode(this._previousErrorOnUnknownElementsOption ?? THROW_ON_UNKNOWN_ELEMENTS_DEFAULT);\n        // Restore the previous value of the \"error on unknown properties\" option\n        ɵsetUnknownPropertyStrictMode(this._previousErrorOnUnknownPropertiesOption ?? THROW_ON_UNKNOWN_PROPERTIES_DEFAULT);\n        // We have to chain a couple of try/finally blocks, because each step can\n        // throw errors and we don't want it to interrupt the next step and we also\n        // want an error to be thrown at the end.\n        try {\n            this.destroyActiveFixtures();\n        }\n        finally {\n            try {\n                if (this.shouldTearDownTestingModule()) {\n                    this.tearDownTestingModule();\n                }\n            }\n            finally {\n                this._testModuleRef = null;\n                this._instanceTeardownOptions = undefined;\n                this._instanceErrorOnUnknownElementsOption = undefined;\n                this._instanceErrorOnUnknownPropertiesOption = undefined;\n                this._instanceDeferBlockBehavior = DEFER_BLOCK_DEFAULT_BEHAVIOR;\n            }\n        }\n        return this;\n    }\n    configureCompiler(config) {\n        if (config.useJit != null) {\n            throw new Error('JIT compiler is not configurable via TestBed APIs.');\n        }\n        if (config.providers !== undefined) {\n            this.compiler.setCompilerProviders(config.providers);\n        }\n        return this;\n    }\n    configureTestingModule(moduleDef) {\n        this.assertNotInstantiated('TestBed.configureTestingModule', 'configure the test module');\n        // Trigger module scoping queue flush before executing other TestBed operations in a test.\n        // This is needed for the first test invocation to ensure that globally declared modules have\n        // their components scoped properly. See the `checkGlobalCompilationFinished` function\n        // description for additional info.\n        this.checkGlobalCompilationFinished();\n        // Always re-assign the options, even if they're undefined.\n        // This ensures that we don't carry them between tests.\n        this._instanceTeardownOptions = moduleDef.teardown;\n        this._instanceErrorOnUnknownElementsOption = moduleDef.errorOnUnknownElements;\n        this._instanceErrorOnUnknownPropertiesOption = moduleDef.errorOnUnknownProperties;\n        this._instanceDeferBlockBehavior = moduleDef.deferBlockBehavior ?? DEFER_BLOCK_DEFAULT_BEHAVIOR;\n        // Store the current value of the strict mode option,\n        // so we can restore it later\n        this._previousErrorOnUnknownElementsOption = ɵgetUnknownElementStrictMode();\n        ɵsetUnknownElementStrictMode(this.shouldThrowErrorOnUnknownElements());\n        this._previousErrorOnUnknownPropertiesOption = ɵgetUnknownPropertyStrictMode();\n        ɵsetUnknownPropertyStrictMode(this.shouldThrowErrorOnUnknownProperties());\n        this.compiler.configureTestingModule(moduleDef);\n        return this;\n    }\n    compileComponents() {\n        return this.compiler.compileComponents();\n    }\n    inject(token, notFoundValue, flags) {\n        if (token === TestBed) {\n            return this;\n        }\n        const UNDEFINED = {};\n        const result = this.testModuleRef.injector.get(token, UNDEFINED, ɵconvertToBitFlags(flags));\n        return result === UNDEFINED ? this.compiler.injector.get(token, notFoundValue, flags) :\n            result;\n    }\n    /** @deprecated from v9.0.0 use TestBed.inject */\n    get(token, notFoundValue = Injector.THROW_IF_NOT_FOUND, flags = InjectFlags.Default) {\n        return this.inject(token, notFoundValue, flags);\n    }\n    runInInjectionContext(fn) {\n        return runInInjectionContext(this.inject(EnvironmentInjector), fn);\n    }\n    execute(tokens, fn, context) {\n        const params = tokens.map(t => this.inject(t));\n        return fn.apply(context, params);\n    }\n    overrideModule(ngModule, override) {\n        this.assertNotInstantiated('overrideModule', 'override module metadata');\n        this.compiler.overrideModule(ngModule, override);\n        return this;\n    }\n    overrideComponent(component, override) {\n        this.assertNotInstantiated('overrideComponent', 'override component metadata');\n        this.compiler.overrideComponent(component, override);\n        return this;\n    }\n    overrideTemplateUsingTestingModule(component, template) {\n        this.assertNotInstantiated('TestBed.overrideTemplateUsingTestingModule', 'Cannot override template when the test module has already been instantiated');\n        this.compiler.overrideTemplateUsingTestingModule(component, template);\n        return this;\n    }\n    overrideDirective(directive, override) {\n        this.assertNotInstantiated('overrideDirective', 'override directive metadata');\n        this.compiler.overrideDirective(directive, override);\n        return this;\n    }\n    overridePipe(pipe, override) {\n        this.assertNotInstantiated('overridePipe', 'override pipe metadata');\n        this.compiler.overridePipe(pipe, override);\n        return this;\n    }\n    /**\n     * Overwrites all providers for the given token with the given provider definition.\n     */\n    overrideProvider(token, provider) {\n        this.assertNotInstantiated('overrideProvider', 'override provider');\n        this.compiler.overrideProvider(token, provider);\n        return this;\n    }\n    overrideTemplate(component, template) {\n        return this.overrideComponent(component, { set: { template, templateUrl: null } });\n    }\n    createComponent(type) {\n        const testComponentRenderer = this.inject(TestComponentRenderer);\n        const rootElId = `root${_nextRootElementId++}`;\n        testComponentRenderer.insertRootElement(rootElId);\n        if (ɵgetAsyncClassMetadataFn(type)) {\n            throw new Error(`Component '${type.name}' has unresolved metadata. ` +\n                `Please call \\`await TestBed.compileComponents()\\` before running this test.`);\n        }\n        const componentDef = type.ɵcmp;\n        if (!componentDef) {\n            throw new Error(`It looks like '${ɵstringify(type)}' has not been compiled.`);\n        }\n        const componentFactory = new ɵRender3ComponentFactory(componentDef);\n        const initComponent = () => {\n            const componentRef = componentFactory.create(Injector.NULL, [], `#${rootElId}`, this.testModuleRef);\n            return this.runInInjectionContext(() => {\n                const hasScheduler = this.inject(ɵChangeDetectionScheduler, null) !== null;\n                const fixture = hasScheduler ? new ScheduledComponentFixture(componentRef) :\n                    new PseudoApplicationComponentFixture(componentRef);\n                fixture.initialize();\n                return fixture;\n            });\n        };\n        const noNgZone = this.inject(ComponentFixtureNoNgZone, false);\n        const ngZone = noNgZone ? null : this.inject(NgZone, null);\n        const fixture = ngZone ? ngZone.run(initComponent) : initComponent();\n        this._activeFixtures.push(fixture);\n        return fixture;\n    }\n    /**\n     * @internal strip this from published d.ts files due to\n     * https://github.com/microsoft/TypeScript/issues/36216\n     */\n    get compiler() {\n        if (this._compiler === null) {\n            throw new Error(`Need to call TestBed.initTestEnvironment() first`);\n        }\n        return this._compiler;\n    }\n    /**\n     * @internal strip this from published d.ts files due to\n     * https://github.com/microsoft/TypeScript/issues/36216\n     */\n    get testModuleRef() {\n        if (this._testModuleRef === null) {\n            this._testModuleRef = this.compiler.finalize();\n        }\n        return this._testModuleRef;\n    }\n    assertNotInstantiated(methodName, methodDescription) {\n        if (this._testModuleRef !== null) {\n            throw new Error(`Cannot ${methodDescription} when the test module has already been instantiated. ` +\n                `Make sure you are not using \\`inject\\` before \\`${methodName}\\`.`);\n        }\n    }\n    /**\n     * Check whether the module scoping queue should be flushed, and flush it if needed.\n     *\n     * When the TestBed is reset, it clears the JIT module compilation queue, cancelling any\n     * in-progress module compilation. This creates a potential hazard - the very first time the\n     * TestBed is initialized (or if it's reset without being initialized), there may be pending\n     * compilations of modules declared in global scope. These compilations should be finished.\n     *\n     * To ensure that globally declared modules have their components scoped properly, this function\n     * is called whenever TestBed is initialized or reset. The _first_ time that this happens, prior\n     * to any other operations, the scoping queue is flushed.\n     */\n    checkGlobalCompilationFinished() {\n        // Checking _testNgModuleRef is null should not be necessary, but is left in as an additional\n        // guard that compilations queued in tests (after instantiation) are never flushed accidentally.\n        if (!this.globalCompilationChecked && this._testModuleRef === null) {\n            ɵflushModuleScopingQueueAsMuchAsPossible();\n        }\n        this.globalCompilationChecked = true;\n    }\n    destroyActiveFixtures() {\n        let errorCount = 0;\n        this._activeFixtures.forEach((fixture) => {\n            try {\n                fixture.destroy();\n            }\n            catch (e) {\n                errorCount++;\n                console.error('Error during cleanup of component', {\n                    component: fixture.componentInstance,\n                    stacktrace: e,\n                });\n            }\n        });\n        this._activeFixtures = [];\n        if (errorCount > 0 && this.shouldRethrowTeardownErrors()) {\n            throw Error(`${errorCount} ${(errorCount === 1 ? 'component' : 'components')} ` +\n                `threw errors during cleanup`);\n        }\n    }\n    shouldRethrowTeardownErrors() {\n        const instanceOptions = this._instanceTeardownOptions;\n        const environmentOptions = TestBedImpl._environmentTeardownOptions;\n        // If the new teardown behavior hasn't been configured, preserve the old behavior.\n        if (!instanceOptions && !environmentOptions) {\n            return TEARDOWN_TESTING_MODULE_ON_DESTROY_DEFAULT;\n        }\n        // Otherwise use the configured behavior or default to rethrowing.\n        return instanceOptions?.rethrowErrors ?? environmentOptions?.rethrowErrors ??\n            this.shouldTearDownTestingModule();\n    }\n    shouldThrowErrorOnUnknownElements() {\n        // Check if a configuration has been provided to throw when an unknown element is found\n        return this._instanceErrorOnUnknownElementsOption ??\n            TestBedImpl._environmentErrorOnUnknownElementsOption ?? THROW_ON_UNKNOWN_ELEMENTS_DEFAULT;\n    }\n    shouldThrowErrorOnUnknownProperties() {\n        // Check if a configuration has been provided to throw when an unknown property is found\n        return this._instanceErrorOnUnknownPropertiesOption ??\n            TestBedImpl._environmentErrorOnUnknownPropertiesOption ??\n            THROW_ON_UNKNOWN_PROPERTIES_DEFAULT;\n    }\n    shouldTearDownTestingModule() {\n        return this._instanceTeardownOptions?.destroyAfterEach ??\n            TestBedImpl._environmentTeardownOptions?.destroyAfterEach ??\n            TEARDOWN_TESTING_MODULE_ON_DESTROY_DEFAULT;\n    }\n    getDeferBlockBehavior() {\n        return this._instanceDeferBlockBehavior;\n    }\n    tearDownTestingModule() {\n        // If the module ref has already been destroyed, we won't be able to get a test renderer.\n        if (this._testModuleRef === null) {\n            return;\n        }\n        // Resolve the renderer ahead of time, because we want to remove the root elements as the very\n        // last step, but the injector will be destroyed as a part of the module ref destruction.\n        const testRenderer = this.inject(TestComponentRenderer);\n        try {\n            this._testModuleRef.destroy();\n        }\n        catch (e) {\n            if (this.shouldRethrowTeardownErrors()) {\n                throw e;\n            }\n            else {\n                console.error('Error during cleanup of a testing module', {\n                    component: this._testModuleRef.instance,\n                    stacktrace: e,\n                });\n            }\n        }\n        finally {\n            testRenderer.removeAllRootElements?.();\n        }\n    }\n    /**\n     * Execute any pending effects.\n     *\n     * @developerPreview\n     */\n    flushEffects() {\n        this.inject(ɵEffectScheduler).flush();\n    }\n}\n/**\n * @description\n * Configures and initializes environment for unit testing and provides methods for\n * creating components and services in unit tests.\n *\n * `TestBed` is the primary api for writing unit tests for Angular applications and libraries.\n *\n * @publicApi\n */\nconst TestBed = TestBedImpl;\n/**\n * Allows injecting dependencies in `beforeEach()` and `it()`. Note: this function\n * (imported from the `@angular/core/testing` package) can **only** be used to inject dependencies\n * in tests. To inject dependencies in your application code, use the [`inject`](api/core/inject)\n * function from the `@angular/core` package instead.\n *\n * Example:\n *\n * ```\n * beforeEach(inject([Dependency, AClass], (dep, object) => {\n *   // some code that uses `dep` and `object`\n *   // ...\n * }));\n *\n * it('...', inject([AClass], (object) => {\n *   object.doSomething();\n *   expect(...);\n * })\n * ```\n *\n * @publicApi\n */\nfunction inject(tokens, fn) {\n    const testBed = TestBedImpl.INSTANCE;\n    // Not using an arrow function to preserve context passed from call site\n    return function () {\n        return testBed.execute(tokens, fn, this);\n    };\n}\n/**\n * @publicApi\n */\nclass InjectSetupWrapper {\n    constructor(_moduleDef) {\n        this._moduleDef = _moduleDef;\n    }\n    _addModule() {\n        const moduleDef = this._moduleDef();\n        if (moduleDef) {\n            TestBedImpl.configureTestingModule(moduleDef);\n        }\n    }\n    inject(tokens, fn) {\n        const self = this;\n        // Not using an arrow function to preserve context passed from call site\n        return function () {\n            self._addModule();\n            return inject(tokens, fn).call(this);\n        };\n    }\n}\nfunction withModule(moduleDef, fn) {\n    if (fn) {\n        // Not using an arrow function to preserve context passed from call site\n        return function () {\n            const testBed = TestBedImpl.INSTANCE;\n            if (moduleDef) {\n                testBed.configureTestingModule(moduleDef);\n            }\n            return fn.apply(this);\n        };\n    }\n    return new InjectSetupWrapper(() => moduleDef);\n}\n\n/**\n * Public Test Library for unit testing Angular applications. Assumes that you are running\n * with Jasmine, Mocha, or a similar framework which exports a beforeEach function and\n * allows tests to be asynchronous by either returning a promise or using a 'done' parameter.\n */\n// Reset the test providers and the fake async zone before each test.\n// We keep a guard because somehow this file can make it into a bundle and be executed\n// beforeEach is only defined when executing the tests\nglobalThis.beforeEach?.(getCleanupHook(false));\n// We provide both a `beforeEach` and `afterEach`, because the updated behavior for\n// tearing down the module is supposed to run after the test so that we can associate\n// teardown errors with the correct test.\n// We keep a guard because somehow this file can make it into a bundle and be executed\n// afterEach is only defined when executing the tests\nglobalThis.afterEach?.(getCleanupHook(true));\nfunction getCleanupHook(expectedTeardownValue) {\n    return () => {\n        const testBed = TestBedImpl.INSTANCE;\n        if (testBed.shouldTearDownTestingModule() === expectedTeardownValue) {\n            testBed.resetTestingModule();\n            resetFakeAsyncZoneIfExists();\n        }\n    };\n}\n/**\n * This API should be removed. But doing so seems to break `google3` and so it requires a bit of\n * investigation.\n *\n * A work around is to mark it as `@codeGenApi` for now and investigate later.\n *\n * @codeGenApi\n */\n// TODO(iminar): Remove this code in a safe way.\nconst __core_private_testing_placeholder__ = '';\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the core/testing package.\n */\n\n/// <reference types=\"jasmine\" />\n// This file only reexports content of the `src` folder. Keep it that way.\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ComponentFixture, ComponentFixtureAutoDetect, ComponentFixtureNoNgZone, DeferBlockFixture, InjectSetupWrapper, TestBed, TestComponentRenderer, __core_private_testing_placeholder__, async, discardPeriodicTasks, fakeAsync, flush, flushMicrotasks, getTestBed, inject, resetFakeAsyncZone, tick, waitForAsync, withModule, MetadataOverrider as ɵMetadataOverrider };\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,gBAAgB,EAAEC,uBAAuB,EAAEC,sBAAsB,EAAEC,wBAAwB,EAAEC,eAAe,EAAEC,mBAAmB,EAAEC,cAAc,EAAEC,MAAM,IAAIC,QAAQ,EAAEC,WAAW,EAAEC,MAAM,EAAEC,gBAAgB,EAAEC,cAAc,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,aAAa,EAAEC,UAAU,EAAEC,uBAAuB,EAAEC,SAAS,EAAEC,SAAS,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,wBAAwB,EAAEC,sCAAsC,EAAEC,iCAAiC,EAAEC,YAAY,EAAEC,iBAAiB,EAAEC,iBAAiB,EAAEC,YAAY,EAAEC,gCAAgC,EAAEC,0BAA0B,EAAEC,mBAAmB,EAAEC,qBAAqB,EAAEC,SAAS,EAAEC,kBAAkB,EAAEC,YAAY,EAAEC,wBAAwB,EAAEC,iBAAiB,EAAEC,WAAW,EAAEC,iBAAiB,EAAEC,YAAY,EAAEC,YAAY,EAAEC,WAAW,EAAEC,oBAAoB,EAAEC,2BAA2B,EAAEC,WAAW,EAAEC,oBAAoB,EAAEC,yCAAyC,EAAEC,gCAAgC,EAAEC,0BAA0B,EAAEC,QAAQ,EAAEC,mBAAmB,EAAEC,gBAAgB,EAAEC,QAAQ,EAAEC,uBAAuB,EAAEC,gBAAgB,EAAEC,4BAA4B,EAAEC,kBAAkB,EAAEC,WAAW,EAAEC,oCAAoC,EAAEC,wBAAwB,EAAEC,4BAA4B,EAAEC,6BAA6B,EAAEC,4BAA4B,EAAEC,6BAA6B,EAAEC,qBAAqB,EAAEC,mBAAmB,EAAEC,yBAAyB,EAAEC,wCAAwC,QAAQ,eAAe;AACz7C,SAAS/D,mBAAmB,IAAIgE,kBAAkB,EAAErE,gBAAgB,IAAIsE,eAAe,QAAQ,eAAe;AAC9G,SAASC,YAAY,QAAQ,MAAM;AACnC,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,cAAc,QAAQ,mBAAmB;;AAElD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,EAAE,EAAE;EACtB,MAAMC,KAAK,GAAG,OAAOC,IAAI,KAAK,WAAW,GAAGA,IAAI,GAAG,IAAI;EACvD,IAAI,CAACD,KAAK,EAAE;IACR,OAAO,YAAY;MACf,OAAOE,OAAO,CAACC,MAAM,CAAC,4EAA4E,GAC9F,yDAAyD,CAAC;IAClE,CAAC;EACL;EACA,MAAMC,SAAS,GAAGJ,KAAK,IAAIA,KAAK,CAACA,KAAK,CAACK,UAAU,CAAC,WAAW,CAAC,CAAC;EAC/D,IAAI,OAAOD,SAAS,KAAK,UAAU,EAAE;IACjC,OAAOA,SAAS,CAACL,EAAE,CAAC;EACxB;EACA,OAAO,YAAY;IACf,OAAOG,OAAO,CAACC,MAAM,CAAC,gFAAgF,GAClG,iEAAiE,CAAC;EAC1E,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,KAAKA,CAACP,EAAE,EAAE;EACf,OAAOD,YAAY,CAACC,EAAE,CAAC;AAC3B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMQ,iBAAiB,CAAC;EACpB;EACAC,WAAWA,CAACC,KAAK,EAAEC,gBAAgB,EAAE;IACjC,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;EAC5C;EACA;AACJ;AACA;AACA;EACUC,MAAMA,CAACC,KAAK,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAChB,IAAI,CAACC,gBAAgB,CAACH,KAAK,EAAEC,KAAI,CAACJ,KAAK,CAAC,EAAE;QACtC,MAAMO,aAAa,GAAGC,8BAA8B,CAACL,KAAK,CAAC;QAC3D,MAAM,IAAIM,KAAK,CAAE,6CAA4CF,aAAc,YAAW,GACjF,qBAAoBA,aAAa,CAACG,WAAW,CAAC,CAAE,+BAA8B,CAAC;MACxF;MACA,IAAIP,KAAK,KAAKxF,gBAAgB,CAACgG,QAAQ,EAAE;QACrC,MAAM/F,uBAAuB,CAACwF,KAAI,CAACJ,KAAK,CAACY,QAAQ,EAAER,KAAI,CAACJ,KAAK,CAACa,KAAK,EAAET,KAAI,CAACJ,KAAK,CAACc,KAAK,CAAC;MAC1F;MACA;MACA;MACA,MAAMC,mBAAmB,GAAG,IAAI;MAChClG,sBAAsB,CAACsF,KAAK,EAAEC,KAAI,CAACJ,KAAK,CAACc,KAAK,EAAEV,KAAI,CAACJ,KAAK,CAACgB,UAAU,EAAED,mBAAmB,CAAC;MAC3FX,KAAI,CAACH,gBAAgB,CAACgB,aAAa,CAAC,CAAC;IAAC;EAC1C;EACA;AACJ;AACA;AACA;EACIC,cAAcA,CAAA,EAAG;IACb,MAAMC,WAAW,GAAG,EAAE;IACtB;IACA;IACA;IACA,MAAMC,kBAAkB,GAAG,EAAE;IAC7B,IAAI,IAAI,CAACpB,KAAK,CAACgB,UAAU,CAACK,MAAM,IAAIvG,wBAAwB,EAAE;MAC1D,MAAM+F,KAAK,GAAG,IAAI,CAACb,KAAK,CAACgB,UAAU,CAAClG,wBAAwB,CAAC;MAC7DC,eAAe,CAAC8F,KAAK,EAAEM,WAAW,CAAC;MACnC,KAAK,MAAMnB,KAAK,IAAImB,WAAW,EAAE;QAC7BC,kBAAkB,CAACE,IAAI,CAAC,IAAIxB,iBAAiB,CAACE,KAAK,EAAE,IAAI,CAACC,gBAAgB,CAAC,CAAC;MAChF;IACJ;IACA,OAAOR,OAAO,CAAC8B,OAAO,CAACH,kBAAkB,CAAC;EAC9C;AACJ;AACA,SAASd,gBAAgBA,CAACH,KAAK,EAAEH,KAAK,EAAE;EACpC,QAAQG,KAAK;IACT,KAAKxF,gBAAgB,CAAC6G,WAAW;MAC7B,OAAOxB,KAAK,CAACY,QAAQ,CAACa,oBAAoB,KAAK,IAAI;IACvD,KAAK9G,gBAAgB,CAAC+G,OAAO;MACzB,OAAO1B,KAAK,CAACY,QAAQ,CAACe,gBAAgB,KAAK,IAAI;IACnD,KAAKhH,gBAAgB,CAAC8F,KAAK;MACvB,OAAOT,KAAK,CAACY,QAAQ,CAACgB,cAAc,KAAK,IAAI;IACjD,KAAKjH,gBAAgB,CAACgG,QAAQ;MAC1B,OAAO,IAAI;IACf;MACI,OAAO,KAAK;EACpB;AACJ;AACA,SAASH,8BAA8BA,CAACL,KAAK,EAAE;EAC3C,QAAQA,KAAK;IACT,KAAKxF,gBAAgB,CAAC6G,WAAW;MAC7B,OAAO,aAAa;IACxB,KAAK7G,gBAAgB,CAAC+G,OAAO;MACzB,OAAO,SAAS;IACpB,KAAK/G,gBAAgB,CAAC8F,KAAK;MACvB,OAAO,OAAO;IAClB;MACI,OAAO,MAAM;EACrB;AACJ;;AAEA;AACA,MAAMoB,0CAA0C,GAAG,IAAI;AACvD;AACA,MAAMC,iCAAiC,GAAG,KAAK;AAC/C;AACA,MAAMC,mCAAmC,GAAG,KAAK;AACjD;AACA,MAAMC,4BAA4B,GAAGhH,mBAAmB,CAACiH,WAAW;AACpE;AACA;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,CAAC;EACxBC,iBAAiBA,CAACC,aAAa,EAAE,CAAE;EACnCC,qBAAqBA,CAAA,EAAG,CAAE;AAC9B;AACA;AACA;AACA;AACA,MAAMC,0BAA0B,GAAG,IAAIrH,cAAc,CAAC,4BAA4B,CAAC;AACnF;AACA;AACA;AACA;AACA,MAAMsH,wDAAwD,GAAG,IAAItH,cAAc,CAAC,0DAA0D,CAAC;AAC/I;AACA;AACA;AACA,MAAMuH,wBAAwB,GAAG,IAAIvH,cAAc,CAAC,0BAA0B,CAAC;;AAE/E;AACA;AACA;AACA;AACA;AACA,MAAMwH,gBAAgB,CAAC;EACnB;EACA1C,WAAWA,CAAC2C,YAAY,EAAE;IACtB,IAAI,CAACA,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB;IACA,IAAI,CAACC,kBAAkB,GAAGzH,QAAQ,CAACqH,wBAAwB,EAAE;MAAEK,QAAQ,EAAE;IAAK,CAAC,CAAC;IAChF;IACA,IAAI,CAACC,OAAO,GAAG,IAAI,CAACF,kBAAkB,GAAG,IAAIxH,WAAW,CAAC,CAAC,GAAGD,QAAQ,CAACE,MAAM,CAAC;IAC7E;IACA,IAAI,CAAC0H,aAAa,GAAG5H,QAAQ,CAACG,gBAAgB,CAAC;IAC/C;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAAC0H,OAAO,GAAG7H,QAAQ,CAACI,cAAc,CAAC;IACvC;IACA,IAAI,CAAC0H,WAAW,GAAG,IAAI,CAACD,OAAO;IAC/B;IACA,IAAI,CAACE,MAAM,GAAG,IAAI,CAACN,kBAAkB,GAAG,IAAI,GAAG,IAAI,CAACE,OAAO;IAC3D,IAAI,CAACK,iBAAiB,GAAGT,YAAY,CAACS,iBAAiB;IACvD,IAAI,CAACC,UAAU,GAAGV,YAAY,CAACW,QAAQ;IACvC,IAAI,CAACC,YAAY,GAAG9H,YAAY,CAAC,IAAI,CAAC4H,UAAU,CAACG,aAAa,CAAC;IAC/D,IAAI,CAACC,iBAAiB,GAAGd,YAAY,CAACe,QAAQ;IAC9C,IAAI,CAACF,aAAa,GAAG,IAAI,CAACH,UAAU,CAACG,aAAa;IAClD,IAAI,CAACb,YAAY,GAAGA,YAAY;EACpC;EACA;AACJ;AACA;EACIgB,cAAcA,CAAA,EAAG;IACb,IAAI,CAACP,iBAAiB,CAACO,cAAc,CAAC,CAAC;EAC3C;EACA;AACJ;AACA;AACA;AACA;EACIxC,cAAcA,CAAA,EAAG;IACb,MAAMC,WAAW,GAAG,EAAE;IACtB,MAAMN,KAAK,GAAG,IAAI,CAAC6B,YAAY,CAACiB,QAAQ,CAAC,QAAQ,CAAC;IAClD5I,eAAe,CAAC8F,KAAK,EAAEM,WAAW,CAAC;IACnC,MAAMC,kBAAkB,GAAG,EAAE;IAC7B,KAAK,MAAMpB,KAAK,IAAImB,WAAW,EAAE;MAC7BC,kBAAkB,CAACE,IAAI,CAAC,IAAIxB,iBAAiB,CAACE,KAAK,EAAE,IAAI,CAAC,CAAC;IAC/D;IACA,OAAOP,OAAO,CAAC8B,OAAO,CAACH,kBAAkB,CAAC;EAC9C;EACAwC,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAACC,SAAS,KAAKC,SAAS,EAAE;MAC9B,IAAI,CAACD,SAAS,GAAG,IAAI,CAACnB,YAAY,CAACqB,QAAQ,CAACC,GAAG,CAACvI,gBAAgB,EAAE,IAAI,CAAC;IAC3E;IACA,OAAO,IAAI,CAACoI,SAAS;EACzB;EACA;AACJ;AACA;EACII,iBAAiBA,CAAA,EAAG;IAChB,MAAMC,QAAQ,GAAG,IAAI,CAACN,YAAY,CAAC,CAAC;IACpC,IAAIM,QAAQ,IAAIA,QAAQ,CAACD,iBAAiB,EAAE;MACxC,OAAOC,QAAQ,CAACD,iBAAiB,CAAC,CAAC;IACvC;IACA,OAAO,IAAI,CAACE,UAAU,CAAC,CAAC;EAC5B;EACA;AACJ;AACA;EACIC,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC,IAAI,CAACzB,YAAY,EAAE;MACpB,IAAI,CAACD,YAAY,CAAC0B,OAAO,CAAC,CAAC;MAC3B,IAAI,CAACzB,YAAY,GAAG,IAAI;IAC5B;EACJ;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM0B,yBAAyB,SAAS5B,gBAAgB,CAAC;EACrD1C,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGuE,SAAS,CAAC;IACnB,IAAI,CAACC,yBAAyB,GAAGpJ,QAAQ,CAACoH,wDAAwD,EAAE;MAAEM,QAAQ,EAAE;IAAK,CAAC,CAAC,IAAI,KAAK;IAChI,IAAI,CAAC2B,YAAY,GAAGrJ,QAAQ,CAACO,aAAa,CAAC;EAC/C;EACA+I,UAAUA,CAAA,EAAG;IACT,IAAI,CAACzB,OAAO,CAAC0B,UAAU,CAAC,IAAI,CAAChC,YAAY,CAACiB,QAAQ,CAAC;EACvD;EACA1C,aAAaA,CAACyC,cAAc,GAAG,IAAI,EAAE;IACjC,IAAI,CAAC,IAAI,CAACa,yBAAyB,EAAE;MACjC,MAAM,IAAI9D,KAAK,CAAC,2EAA2E,GACvF,6DAA6D,CAAC;IACtE,CAAC,MACI,IAAI,CAACiD,cAAc,EAAE;MACtB,MAAM,IAAIjD,KAAK,CAAC,yDAAyD,GACrE,gFAAgF,CAAC;IACzF;IACA,IAAI,CAACsC,aAAa,CAAC4B,KAAK,CAAC,CAAC;IAC1B,IAAI,CAAC3B,OAAO,CAAC4B,IAAI,CAAC,CAAC;IACnB,IAAI,CAAC7B,aAAa,CAAC4B,KAAK,CAAC,CAAC;EAC9B;EACAE,QAAQA,CAAA,EAAG;IACP,OAAO,CAAC,IAAI,CAACL,YAAY,CAACM,eAAe,CAACC,KAAK;EACnD;EACAZ,UAAUA,CAAA,EAAG;IACT,IAAI,IAAI,CAACU,QAAQ,CAAC,CAAC,EAAE;MACjB,OAAOpF,OAAO,CAAC8B,OAAO,CAAC,KAAK,CAAC;IACjC;IACA,OAAO,IAAI,CAACyB,OAAO,CAAC6B,QAAQ,CAACG,IAAI,CAAC7F,KAAK,CAAE8F,MAAM,IAAKA,MAAM,CAAC,CAAC,CAACC,SAAS,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM,IAAI,CAAC;EAC7F;EACAC,iBAAiBA,CAACC,UAAU,EAAE;IAC1B,MAAM,IAAI5E,KAAK,CAAC,uEAAuE,CAAC;EAC5F;AACJ;AACA;AACA;AACA;AACA,MAAM6E,iCAAiC,SAAS7C,gBAAgB,CAAC;EAC7D1C,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGuE,SAAS,CAAC;IACnB,IAAI,CAACiB,cAAc,GAAG,IAAIrG,YAAY,CAAC,CAAC;IACxC,IAAI,CAACsG,WAAW,GAAGrK,QAAQ,CAACmH,0BAA0B,EAAE;MAAEO,QAAQ,EAAE;IAAK,CAAC,CAAC,IAAI,KAAK;IACpF,IAAI,CAAC4C,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,qBAAqB,GAAG9B,SAAS;IACtC,IAAI,CAAC+B,wBAAwB,GAAG/B,SAAS;EAC7C;EACAW,UAAUA,CAAA,EAAG;IACT;IACA;IACA,IAAI,CAAC3B,OAAO,CAACgD,iBAAiB,CAAC,MAAM;MACjC,IAAI,CAACP,cAAc,CAACQ,GAAG,CAAC,IAAI,CAACjD,OAAO,CAACkD,UAAU,CAACC,SAAS,CAAC;QACtDC,IAAI,EAAEA,CAAA,KAAM;UACR,IAAI,CAACT,SAAS,GAAG,KAAK;QAC1B;MACJ,CAAC,CAAC,CAAC;MACH,IAAI,CAACF,cAAc,CAACQ,GAAG,CAAC,IAAI,CAACjD,OAAO,CAACqD,gBAAgB,CAACF,SAAS,CAAC;QAC5DC,IAAI,EAAEA,CAAA,KAAM;UACR,IAAI,IAAI,CAACV,WAAW,EAAE;YAClB;YACA;YACA,IAAI,CAACvE,aAAa,CAAC,IAAI,CAAC;UAC5B;QACJ;MACJ,CAAC,CAAC,CAAC;MACH,IAAI,CAACsE,cAAc,CAACQ,GAAG,CAAC,IAAI,CAACjD,OAAO,CAACsD,QAAQ,CAACH,SAAS,CAAC;QACpDC,IAAI,EAAEA,CAAA,KAAM;UACR,IAAI,CAACT,SAAS,GAAG,IAAI;UACrB;UACA,IAAI,IAAI,CAACC,QAAQ,KAAK,IAAI,EAAE;YACxB;YACA;YACA;YACAW,cAAc,CAAC,MAAM;cACjB,IAAI,CAAC,IAAI,CAACvD,OAAO,CAACwD,oBAAoB,EAAE;gBACpC,IAAI,IAAI,CAACZ,QAAQ,KAAK,IAAI,EAAE;kBACxB,IAAI,CAACC,QAAQ,CAAC,IAAI,CAAC;kBACnB,IAAI,CAACA,QAAQ,GAAG,IAAI;kBACpB,IAAI,CAACD,QAAQ,GAAG,IAAI;gBACxB;cACJ;YACJ,CAAC,CAAC;UACN;QACJ;MACJ,CAAC,CAAC,CAAC;MACH,IAAI,CAACH,cAAc,CAACQ,GAAG,CAAC,IAAI,CAACjD,OAAO,CAACyD,OAAO,CAACN,SAAS,CAAC;QACnDC,IAAI,EAAGM,KAAK,IAAK;UACb,MAAMA,KAAK;QACf;MACJ,CAAC,CAAC,CAAC;IACP,CAAC,CAAC;EACN;EACAvF,aAAaA,CAACyC,cAAc,GAAG,IAAI,EAAE;IACjC,IAAI,CAACX,aAAa,CAAC4B,KAAK,CAAC,CAAC;IAC1B;IACA;IACA,IAAI,CAAC7B,OAAO,CAAC2D,GAAG,CAAC,MAAM;MACnB,IAAI,CAACtD,iBAAiB,CAAClC,aAAa,CAAC,CAAC;MACtC,IAAIyC,cAAc,EAAE;QAChB,IAAI,CAACA,cAAc,CAAC,CAAC;MACzB;IACJ,CAAC,CAAC;IACF;IACA;IACA,IAAI,CAACX,aAAa,CAAC4B,KAAK,CAAC,CAAC;EAC9B;EACAE,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACY,SAAS,IAAI,CAAC,IAAI,CAAC3C,OAAO,CAACwD,oBAAoB;EAC/D;EACAnC,UAAUA,CAAA,EAAG;IACT,IAAI,IAAI,CAACU,QAAQ,CAAC,CAAC,EAAE;MACjB,OAAOpF,OAAO,CAAC8B,OAAO,CAAC,KAAK,CAAC;IACjC,CAAC,MACI,IAAI,IAAI,CAACmE,QAAQ,KAAK,IAAI,EAAE;MAC7B,OAAO,IAAI,CAACA,QAAQ;IACxB,CAAC,MACI;MACD,IAAI,CAACA,QAAQ,GAAG,IAAIjG,OAAO,CAAEiH,GAAG,IAAK;QACjC,IAAI,CAACf,QAAQ,GAAGe,GAAG;MACvB,CAAC,CAAC;MACF,OAAO,IAAI,CAAChB,QAAQ;IACxB;EACJ;EACAN,iBAAiBA,CAACC,UAAU,GAAG,IAAI,EAAE;IACjC,IAAI,IAAI,CAACzC,kBAAkB,EAAE;MACzB,MAAM,IAAInC,KAAK,CAAC,qEAAqE,CAAC;IAC1F;IACA,IAAI,CAAC+E,WAAW,GAAGH,UAAU;IAC7B,IAAI,CAACpE,aAAa,CAAC,CAAC;EACxB;EACAmD,OAAOA,CAAA,EAAG;IACN,IAAI,CAACmB,cAAc,CAACoB,WAAW,CAAC,CAAC;IACjC,KAAK,CAACvC,OAAO,CAAC,CAAC;EACnB;AACJ;AAEA,MAAM7E,KAAK,GAAG,OAAOC,IAAI,KAAK,WAAW,GAAGA,IAAI,GAAG,IAAI;AACvD,MAAMoH,mBAAmB,GAAGrH,KAAK,IAAIA,KAAK,CAACA,KAAK,CAACK,UAAU,CAAC,eAAe,CAAC,CAAC;AAC7E,MAAMiH,wCAAwC,GAAI;AAClD,wEAAwE;AACxE;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,kBAAkBA,CAAA,EAAG;EAC1B,IAAIF,mBAAmB,EAAE;IACrB,OAAOA,mBAAmB,CAACE,kBAAkB,CAAC,CAAC;EACnD;EACA,MAAM,IAAIrG,KAAK,CAACoG,wCAAwC,CAAC;AAC7D;AACA,SAASE,0BAA0BA,CAAA,EAAG;EAClC,IAAIH,mBAAmB,EAAE;IACrBA,mBAAmB,CAACE,kBAAkB,CAAC,CAAC;EAC5C;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,SAASA,CAAC1H,EAAE,EAAE;EACnB,IAAIsH,mBAAmB,EAAE;IACrB,OAAOA,mBAAmB,CAACI,SAAS,CAAC1H,EAAE,CAAC;EAC5C;EACA,MAAM,IAAImB,KAAK,CAACoG,wCAAwC,CAAC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASjC,IAAIA,CAACqC,MAAM,GAAG,CAAC,EAAEC,WAAW,GAAG;EACpCC,iCAAiC,EAAE;AACvC,CAAC,EAAE;EACC,IAAIP,mBAAmB,EAAE;IACrB,OAAOA,mBAAmB,CAAChC,IAAI,CAACqC,MAAM,EAAEC,WAAW,CAAC;EACxD;EACA,MAAM,IAAIzG,KAAK,CAACoG,wCAAwC,CAAC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASlC,KAAKA,CAACyC,QAAQ,EAAE;EACrB,IAAIR,mBAAmB,EAAE;IACrB,OAAOA,mBAAmB,CAACjC,KAAK,CAACyC,QAAQ,CAAC;EAC9C;EACA,MAAM,IAAI3G,KAAK,CAACoG,wCAAwC,CAAC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA,SAASQ,oBAAoBA,CAAA,EAAG;EAC5B,IAAIT,mBAAmB,EAAE;IACrB,OAAOA,mBAAmB,CAACS,oBAAoB,CAAC,CAAC;EACrD;EACA,MAAM,IAAI5G,KAAK,CAACoG,wCAAwC,CAAC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA,SAASS,eAAeA,CAAA,EAAG;EACvB,IAAIV,mBAAmB,EAAE;IACrB,OAAOA,mBAAmB,CAACU,eAAe,CAAC,CAAC;EAChD;EACA,MAAM,IAAI7G,KAAK,CAACoG,wCAAwC,CAAC;AAC7D;AAEA,IAAIU,gBAAgB,GAAG,CAAC;AACxB,MAAMC,iBAAiB,CAAC;EACpBzH,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC0H,WAAW,GAAG,IAAIC,GAAG,CAAC,CAAC;EAChC;EACA;AACJ;AACA;AACA;EACIC,gBAAgBA,CAACC,aAAa,EAAEC,WAAW,EAAEC,QAAQ,EAAE;IACnD,MAAMC,KAAK,GAAG,CAAC,CAAC;IAChB,IAAIF,WAAW,EAAE;MACbG,WAAW,CAACH,WAAW,CAAC,CAACI,OAAO,CAAEC,IAAI,IAAKH,KAAK,CAACG,IAAI,CAAC,GAAGL,WAAW,CAACK,IAAI,CAAC,CAAC;IAC/E;IACA,IAAIJ,QAAQ,CAACK,GAAG,EAAE;MACd,IAAIL,QAAQ,CAACM,MAAM,IAAIN,QAAQ,CAAC/B,GAAG,EAAE;QACjC,MAAM,IAAItF,KAAK,CAAE,6BAA4B9E,UAAU,CAACiM,aAAa,CAAE,oBAAmB,CAAC;MAC/F;MACAS,WAAW,CAACN,KAAK,EAAED,QAAQ,CAACK,GAAG,CAAC;IACpC;IACA,IAAIL,QAAQ,CAACM,MAAM,EAAE;MACjBE,cAAc,CAACP,KAAK,EAAED,QAAQ,CAACM,MAAM,EAAE,IAAI,CAACX,WAAW,CAAC;IAC5D;IACA,IAAIK,QAAQ,CAAC/B,GAAG,EAAE;MACdwC,WAAW,CAACR,KAAK,EAAED,QAAQ,CAAC/B,GAAG,CAAC;IACpC;IACA,OAAO,IAAI6B,aAAa,CAACG,KAAK,CAAC;EACnC;AACJ;AACA,SAASO,cAAcA,CAACE,QAAQ,EAAEJ,MAAM,EAAEK,UAAU,EAAE;EAClD,MAAMC,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC/B,KAAK,MAAMT,IAAI,IAAIE,MAAM,EAAE;IACvB,MAAMQ,WAAW,GAAGR,MAAM,CAACF,IAAI,CAAC;IAChC,IAAIW,KAAK,CAACC,OAAO,CAACF,WAAW,CAAC,EAAE;MAC5BA,WAAW,CAACX,OAAO,CAAElD,KAAK,IAAK;QAC3B2D,aAAa,CAAC3C,GAAG,CAACgD,YAAY,CAACb,IAAI,EAAEnD,KAAK,EAAE0D,UAAU,CAAC,CAAC;MAC5D,CAAC,CAAC;IACN,CAAC,MACI;MACDC,aAAa,CAAC3C,GAAG,CAACgD,YAAY,CAACb,IAAI,EAAEU,WAAW,EAAEH,UAAU,CAAC,CAAC;IAClE;EACJ;EACA,KAAK,MAAMP,IAAI,IAAIM,QAAQ,EAAE;IACzB,MAAMQ,SAAS,GAAGR,QAAQ,CAACN,IAAI,CAAC;IAChC,IAAIW,KAAK,CAACC,OAAO,CAACE,SAAS,CAAC,EAAE;MAC1BR,QAAQ,CAACN,IAAI,CAAC,GAAGc,SAAS,CAACC,MAAM,CAAElE,KAAK,IAAK,CAAC2D,aAAa,CAACQ,GAAG,CAACH,YAAY,CAACb,IAAI,EAAEnD,KAAK,EAAE0D,UAAU,CAAC,CAAC,CAAC;IAC3G,CAAC,MACI;MACD,IAAIC,aAAa,CAACQ,GAAG,CAACH,YAAY,CAACb,IAAI,EAAEc,SAAS,EAAEP,UAAU,CAAC,CAAC,EAAE;QAC9DD,QAAQ,CAACN,IAAI,CAAC,GAAGpE,SAAS;MAC9B;IACJ;EACJ;AACJ;AACA,SAASyE,WAAWA,CAACC,QAAQ,EAAEzC,GAAG,EAAE;EAChC,KAAK,MAAMmC,IAAI,IAAInC,GAAG,EAAE;IACpB,MAAMoD,QAAQ,GAAGpD,GAAG,CAACmC,IAAI,CAAC;IAC1B,MAAMc,SAAS,GAAGR,QAAQ,CAACN,IAAI,CAAC;IAChC,IAAIc,SAAS,IAAI,IAAI,IAAIH,KAAK,CAACC,OAAO,CAACE,SAAS,CAAC,EAAE;MAC/CR,QAAQ,CAACN,IAAI,CAAC,GAAGc,SAAS,CAACI,MAAM,CAACD,QAAQ,CAAC;IAC/C,CAAC,MACI;MACDX,QAAQ,CAACN,IAAI,CAAC,GAAGiB,QAAQ;IAC7B;EACJ;AACJ;AACA,SAASd,WAAWA,CAACG,QAAQ,EAAEL,GAAG,EAAE;EAChC,KAAK,MAAMD,IAAI,IAAIC,GAAG,EAAE;IACpBK,QAAQ,CAACN,IAAI,CAAC,GAAGC,GAAG,CAACD,IAAI,CAAC;EAC9B;AACJ;AACA,SAASa,YAAYA,CAACM,QAAQ,EAAEL,SAAS,EAAEP,UAAU,EAAE;EACnD,IAAIa,YAAY,GAAG,CAAC;EACpB,MAAMC,SAAS,GAAG,IAAI7B,GAAG,CAAC,CAAC;EAC3B,MAAM8B,QAAQ,GAAGA,CAACC,GAAG,EAAE1E,KAAK,KAAK;IAC7B,IAAIA,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7C,IAAIwE,SAAS,CAACL,GAAG,CAACnE,KAAK,CAAC,EAAE;QACtB,OAAOwE,SAAS,CAACvF,GAAG,CAACe,KAAK,CAAC;MAC/B;MACA;MACA;MACAwE,SAAS,CAACpB,GAAG,CAACpD,KAAK,EAAG,QAAOuE,YAAY,EAAG,EAAC,CAAC;MAC9C;MACA,OAAOvE,KAAK;IAChB,CAAC,MACI,IAAI,OAAOA,KAAK,KAAK,UAAU,EAAE;MAClCA,KAAK,GAAG2E,mBAAmB,CAAC3E,KAAK,EAAE0D,UAAU,CAAC;IAClD;IACA,OAAO1D,KAAK;EAChB,CAAC;EACD,OAAQ,GAAEsE,QAAS,IAAGM,IAAI,CAACC,SAAS,CAACZ,SAAS,EAAEQ,QAAQ,CAAE,EAAC;AAC/D;AACA,SAASE,mBAAmBA,CAACG,GAAG,EAAEpB,UAAU,EAAE;EAC1C,IAAIqB,EAAE,GAAGrB,UAAU,CAACzE,GAAG,CAAC6F,GAAG,CAAC;EAC5B,IAAI,CAACC,EAAE,EAAE;IACLA,EAAE,GAAI,GAAEnO,UAAU,CAACkO,GAAG,CAAE,GAAEtC,gBAAgB,EAAG,EAAC;IAC9CkB,UAAU,CAACN,GAAG,CAAC0B,GAAG,EAAEC,EAAE,CAAC;EAC3B;EACA,OAAOA,EAAE;AACb;AACA,SAAS9B,WAAWA,CAAC+B,GAAG,EAAE;EACtB,MAAMhC,KAAK,GAAG,EAAE;EAChB;EACAiC,MAAM,CAACC,IAAI,CAACF,GAAG,CAAC,CAAC9B,OAAO,CAAEC,IAAI,IAAK;IAC/B,IAAI,CAACA,IAAI,CAACgC,UAAU,CAAC,GAAG,CAAC,EAAE;MACvBnC,KAAK,CAACzG,IAAI,CAAC4G,IAAI,CAAC;IACpB;EACJ,CAAC,CAAC;EACF;EACA,IAAIiC,KAAK,GAAGJ,GAAG;EACf,OAAOI,KAAK,GAAGH,MAAM,CAACI,cAAc,CAACD,KAAK,CAAC,EAAE;IACzCH,MAAM,CAACC,IAAI,CAACE,KAAK,CAAC,CAAClC,OAAO,CAAEoC,SAAS,IAAK;MACtC,MAAMC,IAAI,GAAGN,MAAM,CAACO,wBAAwB,CAACJ,KAAK,EAAEE,SAAS,CAAC;MAC9D,IAAI,CAACA,SAAS,CAACH,UAAU,CAAC,GAAG,CAAC,IAAII,IAAI,IAAI,KAAK,IAAIA,IAAI,EAAE;QACrDvC,KAAK,CAACzG,IAAI,CAAC+I,SAAS,CAAC;MACzB;IACJ,CAAC,CAAC;EACN;EACA,OAAOtC,KAAK;AAChB;AAEA,MAAMyC,UAAU,GAAG,IAAI5O,uBAAuB,CAAC,CAAC;AAChD;AACA;AACA;AACA,MAAM6O,gBAAgB,CAAC;EACnB1K,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC2K,SAAS,GAAG,IAAIhD,GAAG,CAAC,CAAC;IAC1B,IAAI,CAACiD,QAAQ,GAAG,IAAIjD,GAAG,CAAC,CAAC;EAC7B;EACAkD,WAAWA,CAACC,IAAI,EAAE/C,QAAQ,EAAE;IACxB,MAAM4C,SAAS,GAAG,IAAI,CAACA,SAAS,CAAC1G,GAAG,CAAC6G,IAAI,CAAC,IAAI,EAAE;IAChDH,SAAS,CAACpJ,IAAI,CAACwG,QAAQ,CAAC;IACxB,IAAI,CAAC4C,SAAS,CAACvC,GAAG,CAAC0C,IAAI,EAAEH,SAAS,CAAC;IACnC,IAAI,CAACC,QAAQ,CAACG,MAAM,CAACD,IAAI,CAAC;EAC9B;EACAE,YAAYA,CAACL,SAAS,EAAE;IACpB,IAAI,CAACA,SAAS,CAACM,KAAK,CAAC,CAAC;IACtBN,SAAS,CAACzC,OAAO,CAAC,CAAC,CAAC4C,IAAI,EAAE/C,QAAQ,CAAC,KAAK;MACpC,IAAI,CAAC8C,WAAW,CAACC,IAAI,EAAE/C,QAAQ,CAAC;IACpC,CAAC,CAAC;EACN;EACAmD,aAAaA,CAACJ,IAAI,EAAE;IAChB,MAAMK,WAAW,GAAGV,UAAU,CAACU,WAAW,CAACL,IAAI,CAAC;IAChD;IACA;IACA;IACA;IACA;IACA,KAAK,IAAIM,CAAC,GAAGD,WAAW,CAAC7J,MAAM,GAAG,CAAC,EAAE8J,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC9C,MAAMC,UAAU,GAAGF,WAAW,CAACC,CAAC,CAAC;MACjC,MAAME,WAAW,GAAGD,UAAU,YAAYvP,SAAS,IAAIuP,UAAU,YAAYtP,SAAS,IAClFsP,UAAU,YAAYrP,IAAI,IAAIqP,UAAU,YAAYpP,QAAQ;MAChE,IAAIqP,WAAW,EAAE;QACb,OAAOD,UAAU,YAAY,IAAI,CAACP,IAAI,GAAGO,UAAU,GAAG,IAAI;MAC9D;IACJ;IACA,OAAO,IAAI;EACf;EACA7J,OAAOA,CAACsJ,IAAI,EAAE;IACV,IAAIF,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC3G,GAAG,CAAC6G,IAAI,CAAC,IAAI,IAAI;IAC9C,IAAI,CAACF,QAAQ,EAAE;MACXA,QAAQ,GAAG,IAAI,CAACM,aAAa,CAACJ,IAAI,CAAC;MACnC,IAAIF,QAAQ,EAAE;QACV,MAAMD,SAAS,GAAG,IAAI,CAACA,SAAS,CAAC1G,GAAG,CAAC6G,IAAI,CAAC;QAC1C,IAAIH,SAAS,EAAE;UACX,MAAMY,SAAS,GAAG,IAAI9D,iBAAiB,CAAC,CAAC;UACzCkD,SAAS,CAACzC,OAAO,CAACH,QAAQ,IAAI;YAC1B6C,QAAQ,GAAGW,SAAS,CAAC3D,gBAAgB,CAAC,IAAI,CAACkD,IAAI,EAAEF,QAAQ,EAAE7C,QAAQ,CAAC;UACxE,CAAC,CAAC;QACN;MACJ;MACA,IAAI,CAAC6C,QAAQ,CAACxC,GAAG,CAAC0C,IAAI,EAAEF,QAAQ,CAAC;IACrC;IACA,OAAOA,QAAQ;EACnB;AACJ;AACA,MAAMY,iBAAiB,SAASd,gBAAgB,CAAC;EAC7C,IAAII,IAAIA,CAAA,EAAG;IACP,OAAOhP,SAAS;EACpB;AACJ;AACA,MAAM2P,iBAAiB,SAASf,gBAAgB,CAAC;EAC7C,IAAII,IAAIA,CAAA,EAAG;IACP,OAAO/O,SAAS;EACpB;AACJ;AACA,MAAM2P,YAAY,SAAShB,gBAAgB,CAAC;EACxC,IAAII,IAAIA,CAAA,EAAG;IACP,OAAO9O,IAAI;EACf;AACJ;AACA,MAAM2P,gBAAgB,SAASjB,gBAAgB,CAAC;EAC5C,IAAII,IAAIA,CAAA,EAAG;IACP,OAAO7O,QAAQ;EACnB;AACJ;AAEA,IAAI2P,qBAAqB;AACzB,CAAC,UAAUA,qBAAqB,EAAE;EAC9BA,qBAAqB,CAACA,qBAAqB,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,GAAG,aAAa;EAC/EA,qBAAqB,CAACA,qBAAqB,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,GAAG,mBAAmB;AAC/F,CAAC,EAAEA,qBAAqB,KAAKA,qBAAqB,GAAG,CAAC,CAAC,CAAC,CAAC;AACzD,SAASC,uBAAuBA,CAAC7G,KAAK,EAAE;EACpC,OAAOA,KAAK,KAAK4G,qBAAqB,CAACE,WAAW,IAC9C9G,KAAK,KAAK4G,qBAAqB,CAACG,iBAAiB;AACzD;AACA,SAASC,4BAA4BA,CAACC,KAAK,EAAEC,QAAQ,EAAE5I,QAAQ,EAAE;EAC7D2I,KAAK,CAAC/D,OAAO,CAAC4C,IAAI,IAAI;IAClB,IAAI,CAAC5O,wBAAwB,CAAC4O,IAAI,CAAC,EAAE;MACjC,MAAMqB,SAAS,GAAGD,QAAQ,CAAC1K,OAAO,CAACsJ,IAAI,CAAC;MACxC,IAAIqB,SAAS,IAAIA,SAAS,CAACC,UAAU,EAAE;QACnC,MAAM,IAAI1L,KAAK,CAACvE,sCAAsC,CAAC2O,IAAI,EAAExH,QAAQ,CAAC,CAAC;MAC3E;IACJ;EACJ,CAAC,CAAC;AACN;AACA,MAAM+I,eAAe,CAAC;EAClBrM,WAAWA,CAACsM,QAAQ,EAAEC,qBAAqB,EAAE;IACzC,IAAI,CAACD,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAACC,gCAAgC,GAAG,IAAI;IAC5C;IACA,IAAI,CAACC,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB;IACA,IAAI,CAACC,iBAAiB,GAAG,IAAIjE,GAAG,CAAC,CAAC;IAClC,IAAI,CAACkE,iBAAiB,GAAG,IAAIlE,GAAG,CAAC,CAAC;IAClC,IAAI,CAACmE,YAAY,GAAG,IAAInE,GAAG,CAAC,CAAC;IAC7B;IACA;IACA,IAAI,CAACoE,2BAA2B,GAAG,IAAIpE,GAAG,CAAC,CAAC;IAC5C;IACA,IAAI,CAACqE,cAAc,GAAG,IAAIrE,GAAG,CAAC,CAAC;IAC/B,IAAI,CAACsE,cAAc,GAAG,IAAItE,GAAG,CAAC,CAAC;IAC/B;IACA,IAAI,CAACuE,iBAAiB,GAAG,IAAIvE,GAAG,CAAC,CAAC;IAClC;IACA;IACA,IAAI,CAACwE,uBAAuB,GAAG,IAAIzF,GAAG,CAAC,CAAC;IACxC,IAAI,CAAC0F,SAAS,GAAGC,aAAa,CAAC,CAAC;IAChC;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACC,sBAAsB,GAAG,IAAI5F,GAAG,CAAC,CAAC;IACvC;IACA;IACA;IACA;IACA;IACA,IAAI,CAAC6F,aAAa,GAAG,IAAI7F,GAAG,CAAC,CAAC;IAC9B;IACA;IACA,IAAI,CAAC8F,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACC,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACC,qBAAqB,GAAG,EAAE;IAC/B;IACA;IACA,IAAI,CAACC,yBAAyB,GAAG,IAAInG,GAAG,CAAC,CAAC;IAC1C,IAAI,CAACoG,wBAAwB,GAAG,IAAIpG,GAAG,CAAC,CAAC;IACzC,IAAI,CAACqG,6BAA6B,GAAG,IAAIpF,GAAG,CAAC,CAAC;IAC9C,IAAI,CAACqF,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,kBAAkB,GAAGjM,4BAA4B;IACtD,MAAMkM,iBAAiB,CAAC;IAExB,IAAI,CAACC,cAAc,GAAGD,iBAAiB;EAC3C;EACAE,oBAAoBA,CAAC1B,SAAS,EAAE;IAC5B,IAAI,CAACgB,iBAAiB,GAAGhB,SAAS;IAClC,IAAI,CAACe,SAAS,GAAG,IAAI;EACzB;EACAY,sBAAsBA,CAACC,SAAS,EAAE;IAC9B;IACA,IAAIA,SAAS,CAAC9B,YAAY,KAAK1I,SAAS,EAAE;MACtC;MACAiI,4BAA4B,CAACuC,SAAS,CAAC9B,YAAY,EAAE,IAAI,CAACY,SAAS,CAAClB,SAAS,EAAE,uCAAuC,CAAC;MACvH,IAAI,CAACqC,cAAc,CAACD,SAAS,CAAC9B,YAAY,EAAEb,qBAAqB,CAACE,WAAW,CAAC;MAC9E,IAAI,CAACW,YAAY,CAAClL,IAAI,CAAC,GAAGgN,SAAS,CAAC9B,YAAY,CAAC;IACrD;IACA;IACA,IAAI8B,SAAS,CAAC7B,OAAO,KAAK3I,SAAS,EAAE;MACjC,IAAI,CAAC0K,0BAA0B,CAACF,SAAS,CAAC7B,OAAO,CAAC;MAClD,IAAI,CAACA,OAAO,CAACnL,IAAI,CAAC,GAAGgN,SAAS,CAAC7B,OAAO,CAAC;IAC3C;IACA,IAAI6B,SAAS,CAAC5B,SAAS,KAAK5I,SAAS,EAAE;MACnC,IAAI,CAAC4I,SAAS,CAACpL,IAAI,CAAC,GAAGgN,SAAS,CAAC5B,SAAS,CAAC;IAC/C;IACA,IAAI4B,SAAS,CAAC3B,OAAO,KAAK7I,SAAS,EAAE;MACjC,IAAI,CAAC6I,OAAO,CAACrL,IAAI,CAAC,GAAGgN,SAAS,CAAC3B,OAAO,CAAC;IAC3C;IACA,IAAI,CAACsB,kBAAkB,GAAGK,SAAS,CAACL,kBAAkB,IAAIjM,4BAA4B;EAC1F;EACAyM,cAAcA,CAACC,QAAQ,EAAE5G,QAAQ,EAAE;IAC/B,IAAI3L,iCAAiC,EAAE;MACnCC,YAAY,CAACuS,kBAAkB,CAACD,QAAQ,CAAC;IAC7C;IACA,IAAI,CAACxB,iBAAiB,CAACnH,GAAG,CAAC2I,QAAQ,CAAC;IACpC;IACA,IAAI,CAACtB,SAAS,CAACwB,MAAM,CAAChE,WAAW,CAAC8D,QAAQ,EAAE5G,QAAQ,CAAC;IACrD,MAAMU,QAAQ,GAAG,IAAI,CAAC4E,SAAS,CAACwB,MAAM,CAACrN,OAAO,CAACmN,QAAQ,CAAC;IACxD,IAAIlG,QAAQ,KAAK,IAAI,EAAE;MACnB,MAAMqG,gBAAgB,CAACH,QAAQ,CAACI,IAAI,EAAE,UAAU,CAAC;IACrD;IACA,IAAI,CAACC,iBAAiB,CAACL,QAAQ,EAAElG,QAAQ,CAAC;IAC1C;IACA;IACA;IACA,IAAI,CAACgG,0BAA0B,CAAC,CAACE,QAAQ,CAAC,CAAC;EAC/C;EACAM,iBAAiBA,CAAC9C,SAAS,EAAEpE,QAAQ,EAAE;IACnC,IAAI,CAACmH,+BAA+B,CAAC/C,SAAS,EAAEpE,QAAQ,CAAC;IACzD,IAAI,CAACsF,SAAS,CAAClB,SAAS,CAACtB,WAAW,CAACsB,SAAS,EAAEpE,QAAQ,CAAC;IACzD,IAAI,CAAC8E,iBAAiB,CAAC7G,GAAG,CAACmG,SAAS,CAAC;IACrC;IACA;IACA,IAAI,CAACgD,uCAAuC,CAAChD,SAAS,CAAC;EAC3D;EACAiD,iBAAiBA,CAACC,SAAS,EAAEtH,QAAQ,EAAE;IACnC,IAAI,CAACmH,+BAA+B,CAACG,SAAS,EAAEtH,QAAQ,CAAC;IACzD,IAAI,CAACsF,SAAS,CAACgC,SAAS,CAACxE,WAAW,CAACwE,SAAS,EAAEtH,QAAQ,CAAC;IACzD,IAAI,CAAC+E,iBAAiB,CAAC9G,GAAG,CAACqJ,SAAS,CAAC;EACzC;EACAC,YAAYA,CAACrK,IAAI,EAAE8C,QAAQ,EAAE;IACzB,IAAI,CAACmH,+BAA+B,CAACjK,IAAI,EAAE8C,QAAQ,CAAC;IACpD,IAAI,CAACsF,SAAS,CAACpI,IAAI,CAAC4F,WAAW,CAAC5F,IAAI,EAAE8C,QAAQ,CAAC;IAC/C,IAAI,CAACgF,YAAY,CAAC/G,GAAG,CAACf,IAAI,CAAC;EAC/B;EACAiK,+BAA+BA,CAACpE,IAAI,EAAE/C,QAAQ,EAAE;IAC5C,IAAIA,QAAQ,CAAC/B,GAAG,EAAEuJ,cAAc,CAAC,YAAY,CAAC,IAAIxH,QAAQ,CAACK,GAAG,EAAEmH,cAAc,CAAC,YAAY,CAAC,IACxFxH,QAAQ,CAACM,MAAM,EAAEkH,cAAc,CAAC,YAAY,CAAC,EAAE;MAC/C,MAAM,IAAI7O,KAAK,CAAE,uBAAsBoK,IAAI,CAACiE,IAAK,sCAAqC,GACjF,0EAAyE,CAAC;IACnF;EACJ;EACAS,gBAAgBA,CAACC,KAAK,EAAEC,QAAQ,EAAE;IAC9B,IAAIC,WAAW;IACf,IAAID,QAAQ,CAACE,UAAU,KAAK7L,SAAS,EAAE;MACnC4L,WAAW,GAAG;QACVE,OAAO,EAAEJ,KAAK;QACdG,UAAU,EAAEF,QAAQ,CAACE,UAAU;QAC/BE,IAAI,EAAEJ,QAAQ,CAACI,IAAI,IAAI,EAAE;QACzBC,KAAK,EAAEL,QAAQ,CAACK;MACpB,CAAC;IACL,CAAC,MACI,IAAIL,QAAQ,CAACM,QAAQ,KAAKjM,SAAS,EAAE;MACtC4L,WAAW,GAAG;QAAEE,OAAO,EAAEJ,KAAK;QAAEO,QAAQ,EAAEN,QAAQ,CAACM,QAAQ;QAAED,KAAK,EAAEL,QAAQ,CAACK;MAAM,CAAC;IACxF,CAAC,MACI;MACDJ,WAAW,GAAG;QAAEE,OAAO,EAAEJ;MAAM,CAAC;IACpC;IACA,MAAMQ,aAAa,GAAG,OAAOR,KAAK,KAAK,QAAQ,GAAGnT,iBAAiB,CAACmT,KAAK,CAAC,GAAG,IAAI;IACjF,MAAMS,UAAU,GAAGD,aAAa,KAAK,IAAI,GAAG,IAAI,GAAG1T,iBAAiB,CAAC0T,aAAa,CAACC,UAAU,CAAC;IAC9F,MAAMC,eAAe,GAAGD,UAAU,KAAK,MAAM,GAAG,IAAI,CAACrC,qBAAqB,GAAG,IAAI,CAACD,iBAAiB;IACnGuC,eAAe,CAAC5O,IAAI,CAACoO,WAAW,CAAC;IACjC;IACA,IAAI,CAAC5B,wBAAwB,CAAC3F,GAAG,CAACqH,KAAK,EAAEE,WAAW,CAAC;IACrD,IAAIM,aAAa,KAAK,IAAI,IAAIC,UAAU,KAAK,IAAI,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;MACjF,MAAME,iBAAiB,GAAG,IAAI,CAACtC,yBAAyB,CAAC7J,GAAG,CAACiM,UAAU,CAAC;MACxE,IAAIE,iBAAiB,KAAKrM,SAAS,EAAE;QACjCqM,iBAAiB,CAAC7O,IAAI,CAACoO,WAAW,CAAC;MACvC,CAAC,MACI;QACD,IAAI,CAAC7B,yBAAyB,CAAC1F,GAAG,CAAC8H,UAAU,EAAE,CAACP,WAAW,CAAC,CAAC;MACjE;IACJ;EACJ;EACAU,kCAAkCA,CAACvF,IAAI,EAAEwF,QAAQ,EAAE;IAC/C,MAAMC,GAAG,GAAGzF,IAAI,CAACtO,YAAY,CAAC;IAC9B,MAAMgU,YAAY,GAAGA,CAAA,KAAM;MACvB,MAAM/H,QAAQ,GAAG,IAAI,CAAC4E,SAAS,CAAClB,SAAS,CAAC3K,OAAO,CAACsJ,IAAI,CAAC;MACvD,OAAO,CAAC,CAACrC,QAAQ,CAACgI,QAAQ,IAAI,CAAC,CAAChI,QAAQ,CAACiI,SAAS,EAAEpP,MAAM;IAC9D,CAAC;IACD,MAAMqP,iBAAiB,GAAG,CAAC,CAACJ,GAAG,IAAI,CAAC9T,gCAAgC,CAACqO,IAAI,CAAC,IAAI0F,YAAY,CAAC,CAAC;IAC5F;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMzI,QAAQ,GAAG4I,iBAAiB,GAAG;MAAEL,QAAQ;MAAEM,MAAM,EAAE,EAAE;MAAEF,SAAS,EAAE,EAAE;MAAED,QAAQ,EAAE1M;IAAU,CAAC,GAAG;MAAEuM;IAAS,CAAC;IAChH,IAAI,CAACrB,iBAAiB,CAACnE,IAAI,EAAE;MAAE1C,GAAG,EAAEL;IAAS,CAAC,CAAC;IAC/C,IAAI4I,iBAAiB,IAAIJ,GAAG,CAACK,MAAM,IAAIL,GAAG,CAACK,MAAM,CAACtP,MAAM,GAAG,CAAC,EAAE;MAC1D,IAAI,CAAC8L,uBAAuB,CAAChF,GAAG,CAAC0C,IAAI,EAAEyF,GAAG,CAACK,MAAM,CAAC;IACtD;IACA;IACA,IAAI,CAACrD,sBAAsB,CAACnF,GAAG,CAAC0C,IAAI,EAAEc,qBAAqB,CAACG,iBAAiB,CAAC;EAClF;EACM8E,yCAAyCA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAxQ,iBAAA;MAC9C,IAAIwQ,MAAI,CAAC9D,2BAA2B,CAAC+D,IAAI,KAAK,CAAC,EAC3C;MACJ,MAAMC,QAAQ,GAAG,EAAE;MACnB,KAAK,MAAM7E,SAAS,IAAI2E,MAAI,CAAC9D,2BAA2B,EAAE;QACtD,MAAMiE,eAAe,GAAG/U,wBAAwB,CAACiQ,SAAS,CAAC;QAC3D,IAAI8E,eAAe,EAAE;UACjBD,QAAQ,CAACzP,IAAI,CAAC0P,eAAe,CAAC,CAAC,CAAC;QACpC;MACJ;MACAH,MAAI,CAAC9D,2BAA2B,CAAC/B,KAAK,CAAC,CAAC;MACxC,MAAMiG,YAAY,SAASxR,OAAO,CAACyR,GAAG,CAACH,QAAQ,CAAC;MAChD,MAAMI,gBAAgB,GAAGF,YAAY,CAACG,IAAI,CAAC,CAAC,CAAC;MAC7CP,MAAI,CAACrC,0BAA0B,CAAC2C,gBAAgB,CAAC;MACjD;MACA;MACA,KAAK,MAAMjF,SAAS,IAAIiF,gBAAgB,EAAE;QACtCN,MAAI,CAACQ,6BAA6B,CAACnF,SAAS,CAAC;MACjD;IAAC;EACL;EACMoF,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAlR,iBAAA;MACtBkR,MAAI,CAACC,6BAA6B,CAAC,CAAC;MACpC;MACA;MACA;MACA,MAAMD,MAAI,CAACX,yCAAyC,CAAC,CAAC;MACtD;MACA;MACA;MACA;MACA7E,4BAA4B,CAACwF,MAAI,CAAC/E,YAAY,EAAE+E,MAAI,CAACnE,SAAS,CAAClB,SAAS,EAAE,uCAAuC,CAAC;MAClH;MACA,IAAIuF,mBAAmB,GAAGF,MAAI,CAACG,gBAAgB,CAAC,CAAC;MACjD;MACA,IAAID,mBAAmB,EAAE;QACrB,IAAIE,cAAc;QAClB,IAAI1F,QAAQ,GAAI2F,GAAG,IAAK;UACpB,IAAI,CAACD,cAAc,EAAE;YACjBA,cAAc,GAAGJ,MAAI,CAACxN,QAAQ,CAACC,GAAG,CAAC5E,cAAc,CAAC;UACtD;UACA,OAAOK,OAAO,CAAC8B,OAAO,CAACoQ,cAAc,CAAC3N,GAAG,CAAC4N,GAAG,CAAC,CAAC;QACnD,CAAC;QACD,MAAMnV,0BAA0B,CAACwP,QAAQ,CAAC;MAC9C;IAAC;EACL;EACA4F,QAAQA,CAAA,EAAG;IACP;IACA,IAAI,CAACH,gBAAgB,CAAC,CAAC;IACvB;IACA,IAAI,CAACI,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAACC,qBAAqB,CAAC,CAAC;IAC5B,IAAI,CAACC,sBAAsB,CAAC,CAAC;IAC7B;IACA;IACA,IAAI,CAACC,iCAAiC,CAAC,CAAC;IACxC;IACA;IACA,IAAI,CAAC3E,sBAAsB,CAACtC,KAAK,CAAC,CAAC;IACnC,MAAMkH,cAAc,GAAG,IAAI,CAAC7F,QAAQ,CAACtI,QAAQ;IAC7C,IAAI,CAACiK,aAAa,GAAG,IAAItR,mBAAmB,CAAC,IAAI,CAACyR,cAAc,EAAE+D,cAAc,EAAE,EAAE,CAAC;IACrF;IACA;IACA,IAAI,CAAClE,aAAa,CAACjK,QAAQ,CAACC,GAAG,CAACrH,qBAAqB,CAAC,CAACwV,eAAe,CAAC,CAAC;IACxE;IACA;IACA;IACA,MAAMC,QAAQ,GAAG,IAAI,CAACpE,aAAa,CAACjK,QAAQ,CAACC,GAAG,CAACpH,SAAS,EAAEC,kBAAkB,CAAC;IAC/EC,YAAY,CAACsV,QAAQ,CAAC;IACtB,OAAO,IAAI,CAACpE,aAAa;EAC7B;EACA;AACJ;AACA;EACIqE,oBAAoBA,CAACC,UAAU,EAAE;IAC7B,IAAI,CAAC9D,0BAA0B,CAAC,CAAC8D,UAAU,CAAC,CAAC;IAC7C,IAAI,CAACZ,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACM,sBAAsB,CAAC,CAAC;IAC7B,IAAI,CAACX,6BAA6B,CAACiB,UAAU,CAAC;IAC9C,IAAI,CAACP,qBAAqB,CAAC,CAAC;EAChC;EACA;AACJ;AACA;EACUQ,qBAAqBA,CAACD,UAAU,EAAE;IAAA,IAAAE,MAAA;IAAA,OAAAnS,iBAAA;MACpCmS,MAAI,CAAChE,0BAA0B,CAAC,CAAC8D,UAAU,CAAC,CAAC;MAC7C,MAAME,MAAI,CAAClB,iBAAiB,CAAC,CAAC;MAC9BkB,MAAI,CAACR,sBAAsB,CAAC,CAAC;MAC7BQ,MAAI,CAACnB,6BAA6B,CAACiB,UAAU,CAAC;MAC9CE,MAAI,CAACT,qBAAqB,CAAC,CAAC;IAAC;EACjC;EACA;AACJ;AACA;EACIU,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACrF,SAAS,CAACwB,MAAM;EAChC;EACA;AACJ;AACA;EACI8D,sBAAsBA,CAACJ,UAAU,EAAE;IAC/B,OAAOK,aAAa,CAACL,UAAU,CAACM,IAAI,CAACpG,YAAY,CAAC,CAACqG,MAAM,CAAC,CAACC,SAAS,EAAEC,WAAW,KAAK;MAClF,MAAMC,YAAY,GAAGD,WAAW,CAACE,IAAI;MACrCD,YAAY,IAAIF,SAAS,CAACxR,IAAI,CAAC,IAAIvE,wBAAwB,CAACiW,YAAY,EAAE,IAAI,CAAChF,aAAa,CAAC,CAAC;MAC9F,OAAO8E,SAAS;IACpB,CAAC,EAAE,EAAE,CAAC;EACV;EACApB,gBAAgBA,CAAA,EAAG;IACf;IACA,IAAID,mBAAmB,GAAG,KAAK;IAC/B,IAAI,CAAC7E,iBAAiB,CAAC3E,OAAO,CAAC8K,WAAW,IAAI;MAC1C,IAAI9W,wBAAwB,CAAC8W,WAAW,CAAC,EAAE;QACvC,MAAM,IAAItS,KAAK,CAAE,cAAasS,WAAW,CAACjE,IAAK,6BAA4B,GACtE,6EAA4E,CAAC;MACtF;MACA2C,mBAAmB,GAAGA,mBAAmB,IAAIjV,gCAAgC,CAACuW,WAAW,CAAC;MAC1F,MAAMvK,QAAQ,GAAG,IAAI,CAAC4E,SAAS,CAAClB,SAAS,CAAC3K,OAAO,CAACwR,WAAW,CAAC;MAC9D,IAAIvK,QAAQ,KAAK,IAAI,EAAE;QACnB,MAAMqG,gBAAgB,CAACkE,WAAW,CAACjE,IAAI,EAAE,WAAW,CAAC;MACzD;MACA,IAAI,CAACoE,eAAe,CAAC3W,YAAY,EAAEwW,WAAW,CAAC;MAC/C,IAAI5W,iCAAiC,EAAE;QACnCC,YAAY,CAACuS,kBAAkB,CAACoE,WAAW,CAAC;MAChD;MACA/V,iBAAiB,CAAC+V,WAAW,EAAEvK,QAAQ,CAAC;IAC5C,CAAC,CAAC;IACF,IAAI,CAACoE,iBAAiB,CAAC5B,KAAK,CAAC,CAAC;IAC9B,IAAI,CAAC6B,iBAAiB,CAAC5E,OAAO,CAAC8K,WAAW,IAAI;MAC1C,MAAMvK,QAAQ,GAAG,IAAI,CAAC4E,SAAS,CAACgC,SAAS,CAAC7N,OAAO,CAACwR,WAAW,CAAC;MAC9D,IAAIvK,QAAQ,KAAK,IAAI,EAAE;QACnB,MAAMqG,gBAAgB,CAACkE,WAAW,CAACjE,IAAI,EAAE,WAAW,CAAC;MACzD;MACA,IAAI,CAACoE,eAAe,CAACjW,WAAW,EAAE8V,WAAW,CAAC;MAC9C7V,iBAAiB,CAAC6V,WAAW,EAAEvK,QAAQ,CAAC;IAC5C,CAAC,CAAC;IACF,IAAI,CAACqE,iBAAiB,CAAC7B,KAAK,CAAC,CAAC;IAC9B,IAAI,CAAC8B,YAAY,CAAC7E,OAAO,CAAC8K,WAAW,IAAI;MACrC,MAAMvK,QAAQ,GAAG,IAAI,CAAC4E,SAAS,CAACpI,IAAI,CAACzD,OAAO,CAACwR,WAAW,CAAC;MACzD,IAAIvK,QAAQ,KAAK,IAAI,EAAE;QACnB,MAAMqG,gBAAgB,CAACkE,WAAW,CAACjE,IAAI,EAAE,MAAM,CAAC;MACpD;MACA,IAAI,CAACoE,eAAe,CAAC/V,YAAY,EAAE4V,WAAW,CAAC;MAC/C3V,YAAY,CAAC2V,WAAW,EAAEvK,QAAQ,CAAC;IACvC,CAAC,CAAC;IACF,IAAI,CAACsE,YAAY,CAAC9B,KAAK,CAAC,CAAC;IACzB,OAAOyG,mBAAmB;EAC9B;EACAM,qBAAqBA,CAAA,EAAG;IACpB,IAAI,IAAI,CAAC7E,iBAAiB,CAAC4D,IAAI,GAAG,CAAC,EAAE;MACjC;MACA;MACA;MACA,MAAMqC,gBAAgB,GAAG,IAAI,CAAChF,cAAc,CAAC9Q,WAAW,CAAC;MACzD,MAAM+V,eAAe,GAAG,IAAI,CAACC,iCAAiC,CAACF,gBAAgB,CAAC1G,OAAO,CAAC;MACxF,IAAI2G,eAAe,CAACtC,IAAI,GAAG,CAAC,EAAE;QAC1BsC,eAAe,CAACnL,OAAO,CAACqK,UAAU,IAAI;UAClC,IAAI,CAACnW,iCAAiC,EAAE;YACpC,IAAI,CAACmX,qBAAqB,CAAChB,UAAU,EAAEjV,WAAW,EAAE,yBAAyB,CAAC;YAC9EiV,UAAU,CAACjV,WAAW,CAAC,CAACkW,uBAAuB,GAAG,IAAI;UAC1D,CAAC,MACI;YACDnX,YAAY,CAACuS,kBAAkB,CAAC2D,UAAU,CAAC;UAC/C;QACJ,CAAC,CAAC;MACN;IACJ;IACA,MAAMkB,aAAa,GAAG,IAAI9L,GAAG,CAAC,CAAC;IAC/B,MAAM+L,gBAAgB,GAAInB,UAAU,IAAK;MACrC,IAAI,CAACkB,aAAa,CAACtK,GAAG,CAACoJ,UAAU,CAAC,EAAE;QAChC,MAAMoB,eAAe,GAAG9H,uBAAuB,CAAC0G,UAAU,CAAC;QAC3D,MAAMqB,QAAQ,GAAGD,eAAe,GAAG,IAAI,CAACvF,cAAc,GAAGmE,UAAU;QACnEkB,aAAa,CAACrL,GAAG,CAACmK,UAAU,EAAEhV,oBAAoB,CAACqW,QAAQ,CAAC,CAAC;MACjE;MACA,OAAOH,aAAa,CAACxP,GAAG,CAACsO,UAAU,CAAC;IACxC,CAAC;IACD,IAAI,CAAChF,sBAAsB,CAACrF,OAAO,CAAC,CAACqK,UAAU,EAAEsB,aAAa,KAAK;MAC/D,IAAItB,UAAU,KAAK,IAAI,EAAE;QACrB,MAAMuB,WAAW,GAAGJ,gBAAgB,CAACnB,UAAU,CAAC;QAChD,IAAI,CAACgB,qBAAqB,CAACM,aAAa,EAAErX,YAAY,EAAE,eAAe,CAAC;QACxE,IAAI,CAAC+W,qBAAqB,CAACM,aAAa,EAAErX,YAAY,EAAE,UAAU,CAAC;QACnEgB,2BAA2B,CAACuW,eAAe,CAACF,aAAa,CAAC,EAAEC,WAAW,CAAC;MAC5E;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,CAACP,qBAAqB,CAACM,aAAa,EAAErX,YAAY,EAAE,OAAO,CAAC;IACpE,CAAC,CAAC;IACF,IAAI,CAAC+Q,sBAAsB,CAACtC,KAAK,CAAC,CAAC;EACvC;EACAgH,sBAAsBA,CAAA,EAAG;IACrB,MAAM+B,mBAAmB,GAAIC,KAAK,IAAMnJ,IAAI,IAAK;MAC7C,MAAMoB,QAAQ,GAAG+H,KAAK,KAAKzX,YAAY,GAAG,IAAI,CAAC6Q,SAAS,CAAClB,SAAS,GAAG,IAAI,CAACkB,SAAS,CAACgC,SAAS;MAC7F,MAAM5G,QAAQ,GAAGyD,QAAQ,CAAC1K,OAAO,CAACsJ,IAAI,CAAC;MACvC,IAAI,IAAI,CAACoJ,oBAAoB,CAACzL,QAAQ,CAACkE,SAAS,CAAC,EAAE;QAC/C,IAAI,CAACwH,6BAA6B,CAACrJ,IAAI,EAAEmJ,KAAK,CAAC;MACnD;IACJ,CAAC;IACD,IAAI,CAAChH,cAAc,CAAC/E,OAAO,CAAC8L,mBAAmB,CAACxX,YAAY,CAAC,CAAC;IAC9D,IAAI,CAAC0Q,cAAc,CAAChF,OAAO,CAAC8L,mBAAmB,CAAC9W,WAAW,CAAC,CAAC;IAC7D,IAAI,CAAC+P,cAAc,CAAChC,KAAK,CAAC,CAAC;IAC3B,IAAI,CAACiC,cAAc,CAACjC,KAAK,CAAC,CAAC;EAC/B;EACA;AACJ;AACA;AACA;EACIqG,6BAA6BA,CAACxG,IAAI,EAAE;IAChC,MAAMsJ,QAAQ,GAAGC,qBAAqB,CAACvJ,IAAI,CAAC,IAAIwJ,UAAU,CAACxJ,IAAI,CAAC;IAChE;IACA;IACA;IACA;IACA,IAAI,CAACsJ,QAAQ,IAAI,IAAI,CAACpG,6BAA6B,CAAC7E,GAAG,CAAC2B,IAAI,CAAC,EAAE;MAC3D;IACJ;IACA,IAAI,CAACkD,6BAA6B,CAAChI,GAAG,CAAC8E,IAAI,CAAC;IAC5C;IACA;IACA;IACA;IACA;IACA,MAAMyJ,WAAW,GAAGzJ,IAAI,CAACrN,WAAW,CAAC;IACrC;IACA,IAAI,IAAI,CAACsQ,wBAAwB,CAACgD,IAAI,KAAK,CAAC,EACxC;IACJ,IAAIsD,qBAAqB,CAACvJ,IAAI,CAAC,EAAE;MAC7B;MACA,MAAMyF,GAAG,GAAGwD,eAAe,CAACjJ,IAAI,CAAC;MACjC,MAAM0J,YAAY,GAAG5B,aAAa,CAACrC,GAAG,CAACiE,YAAY,IAAI,EAAE,CAAC;MAC1D,KAAK,MAAMC,UAAU,IAAID,YAAY,EAAE;QACnC,IAAI,CAAClD,6BAA6B,CAACmD,UAAU,CAAC;MAClD;IACJ,CAAC,MACI;MACD,MAAM9H,SAAS,GAAG,CACd,GAAG4H,WAAW,CAAC5H,SAAS,EACxB,IAAI,IAAI,CAACmB,yBAAyB,CAAC7J,GAAG,CAAC6G,IAAI,CAAC,IAAI,EAAE,CAAC,CACtD;MACD,IAAI,IAAI,CAACoJ,oBAAoB,CAACvH,SAAS,CAAC,EAAE;QACtC,IAAI,CAACwG,eAAe,CAAC1V,WAAW,EAAEqN,IAAI,CAAC;QACvC,IAAI,CAACyI,qBAAqB,CAACzI,IAAI,EAAErN,WAAW,EAAE,WAAW,CAAC;QAC1D8W,WAAW,CAAC5H,SAAS,GAAG,IAAI,CAAC+H,sBAAsB,CAAC/H,SAAS,CAAC;MAClE;MACA;MACA,MAAM4B,SAAS,GAAGzD,IAAI,CAACxN,WAAW,CAAC;MACnC,MAAMoP,OAAO,GAAGkG,aAAa,CAACrE,SAAS,CAAC7B,OAAO,CAAC;MAChD,KAAK,MAAMiI,cAAc,IAAIjI,OAAO,EAAE;QAClC,IAAI,CAAC4E,6BAA6B,CAACqD,cAAc,CAAC;MACtD;MACA;MACA;MACA,KAAK,MAAMA,cAAc,IAAIC,OAAO,CAACL,WAAW,CAAC7H,OAAO,CAAC,EAAE;QACvD,IAAImI,qBAAqB,CAACF,cAAc,CAAC,EAAE;UACvC,IAAI,CAAClH,aAAa,CAAClM,IAAI,CAAC;YACpBuT,MAAM,EAAEH,cAAc;YACtBI,SAAS,EAAE,WAAW;YACtBC,aAAa,EAAEL,cAAc,CAAChI;UAClC,CAAC,CAAC;UACFgI,cAAc,CAAChI,SAAS,GAAG,IAAI,CAAC+H,sBAAsB,CAACC,cAAc,CAAChI,SAAS,CAAC;QACpF;MACJ;IACJ;EACJ;EACAuF,iCAAiCA,CAAA,EAAG;IAChC,IAAI,CAAC9E,uBAAuB,CAAClF,OAAO,CAAC,CAAC0I,MAAM,EAAE9F,IAAI,KAAKA,IAAI,CAACtO,YAAY,CAAC,CAACoU,MAAM,GAAGA,MAAM,CAAC;IAC1F,IAAI,CAACxD,uBAAuB,CAACnC,KAAK,CAAC,CAAC;EACxC;EACAuD,cAAcA,CAACyG,GAAG,EAAE1C,UAAU,EAAE;IAC5B,KAAK,MAAMvN,KAAK,IAAIiQ,GAAG,EAAE;MACrB,IAAInM,KAAK,CAACC,OAAO,CAAC/D,KAAK,CAAC,EAAE;QACtB,IAAI,CAACwJ,cAAc,CAACxJ,KAAK,EAAEuN,UAAU,CAAC;MAC1C,CAAC,MACI;QACD,IAAI,CAAC2C,SAAS,CAAClQ,KAAK,EAAEuN,UAAU,CAAC;MACrC;IACJ;EACJ;EACAvD,iBAAiBA,CAACL,QAAQ,EAAElG,QAAQ,EAAE;IAClC;IACA,IAAI,CAAC0K,eAAe,CAAC7V,WAAW,EAAEqR,QAAQ,CAAC;IAC3C,IAAI,CAACwE,eAAe,CAAC1V,WAAW,EAAEkR,QAAQ,CAAC;IAC3CjR,oBAAoB,CAACiR,QAAQ,EAAElG,QAAQ,CAAC;EAC5C;EACA0G,uCAAuCA,CAACrE,IAAI,EAAE;IAC1C,MAAMmG,eAAe,GAAG/U,wBAAwB,CAAC4O,IAAI,CAAC;IACtD,IAAImG,eAAe,EAAE;MACjB,IAAI,CAACjE,2BAA2B,CAAChH,GAAG,CAAC8E,IAAI,CAAC;IAC9C;EACJ;EACAoK,SAASA,CAACpK,IAAI,EAAEyH,UAAU,EAAE;IACxB;IACA;IACA,IAAI,CAACpD,uCAAuC,CAACrE,IAAI,CAAC;IAClD,MAAMqB,SAAS,GAAG,IAAI,CAACkB,SAAS,CAAClB,SAAS,CAAC3K,OAAO,CAACsJ,IAAI,CAAC;IACxD,IAAIqB,SAAS,EAAE;MACX;MACA;MACA;MACA,IAAI1P,gCAAgC,CAACqO,IAAI,CAAC,IAAI,CAACA,IAAI,CAACyE,cAAc,CAAC/S,YAAY,CAAC,EAAE;QAC9E,IAAI,CAACqQ,iBAAiB,CAAC7G,GAAG,CAAC8E,IAAI,CAAC;MACpC;MACA,IAAI,CAACmC,cAAc,CAACjH,GAAG,CAAC8E,IAAI,CAAC;MAC7B;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAK,CAAC,IAAI,CAACyC,sBAAsB,CAACpE,GAAG,CAAC2B,IAAI,CAAC,IACvC,IAAI,CAACyC,sBAAsB,CAACtJ,GAAG,CAAC6G,IAAI,CAAC,KAAKc,qBAAqB,CAACE,WAAW,EAAG;QAC9E,IAAI,CAACyB,sBAAsB,CAACnF,GAAG,CAAC0C,IAAI,EAAEyH,UAAU,CAAC;MACrD;MACA;IACJ;IACA,MAAMlD,SAAS,GAAG,IAAI,CAAChC,SAAS,CAACgC,SAAS,CAAC7N,OAAO,CAACsJ,IAAI,CAAC;IACxD,IAAIuE,SAAS,EAAE;MACX,IAAI,CAACvE,IAAI,CAACyE,cAAc,CAACrS,WAAW,CAAC,EAAE;QACnC,IAAI,CAAC4P,iBAAiB,CAAC9G,GAAG,CAAC8E,IAAI,CAAC;MACpC;MACA,IAAI,CAACoC,cAAc,CAAClH,GAAG,CAAC8E,IAAI,CAAC;MAC7B;IACJ;IACA,MAAM7F,IAAI,GAAG,IAAI,CAACoI,SAAS,CAACpI,IAAI,CAACzD,OAAO,CAACsJ,IAAI,CAAC;IAC9C,IAAI7F,IAAI,IAAI,CAAC6F,IAAI,CAACyE,cAAc,CAACnS,YAAY,CAAC,EAAE;MAC5C,IAAI,CAAC2P,YAAY,CAAC/G,GAAG,CAAC8E,IAAI,CAAC;MAC3B;IACJ;EACJ;EACA2D,0BAA0BA,CAACwG,GAAG,EAAE;IAC5B;IACA;IACA;IACA;IACA,MAAME,aAAa,GAAG,IAAIvM,GAAG,CAAC,CAAC;IAC/B,MAAMwM,+BAA+B,GAAIH,GAAG,IAAK;MAC7C,KAAK,MAAMjQ,KAAK,IAAIiQ,GAAG,EAAE;QACrB,IAAInM,KAAK,CAACC,OAAO,CAAC/D,KAAK,CAAC,EAAE;UACtBoQ,+BAA+B,CAACpQ,KAAK,CAAC;QAC1C,CAAC,MACI,IAAIqQ,cAAc,CAACrQ,KAAK,CAAC,EAAE;UAC5B,MAAMuL,GAAG,GAAGvL,KAAK,CAAC6N,IAAI;UACtB,IAAIsC,aAAa,CAAChM,GAAG,CAACoH,GAAG,CAAC,EAAE;YACxB;UACJ;UACA4E,aAAa,CAACnP,GAAG,CAACuK,GAAG,CAAC;UACtB;UACA;UACA,IAAI,CAAC/B,cAAc,CAACoE,aAAa,CAACrC,GAAG,CAAC9D,YAAY,CAAC,EAAEzH,KAAK,CAAC;UAC3DoQ,+BAA+B,CAACxC,aAAa,CAACrC,GAAG,CAAC7D,OAAO,CAAC,CAAC;UAC3D0I,+BAA+B,CAACxC,aAAa,CAACrC,GAAG,CAAC+E,OAAO,CAAC,CAAC;QAC/D,CAAC,MACI,IAAIT,qBAAqB,CAAC7P,KAAK,CAAC,EAAE;UACnCoQ,+BAA+B,CAAC,CAACpQ,KAAK,CAAC2J,QAAQ,CAAC,CAAC;QACrD,CAAC,MACI,IAAI0F,qBAAqB,CAACrP,KAAK,CAAC,EAAE;UACnC,IAAI,CAACkQ,SAAS,CAAClQ,KAAK,EAAE,IAAI,CAAC;UAC3B,MAAMuL,GAAG,GAAGwD,eAAe,CAAC/O,KAAK,CAAC;UAClC,IAAImQ,aAAa,CAAChM,GAAG,CAACoH,GAAG,CAAC,EAAE;YACxB;UACJ;UACA4E,aAAa,CAACnP,GAAG,CAACuK,GAAG,CAAC;UACtB,MAAMiE,YAAY,GAAG5B,aAAa,CAACrC,GAAG,CAACiE,YAAY,IAAI,EAAE,CAAC;UAC1DA,YAAY,CAACtM,OAAO,CAAEuM,UAAU,IAAK;YACjC;YACA;YACA;YACA;YACA,IAAIJ,qBAAqB,CAACI,UAAU,CAAC,IAAIY,cAAc,CAACZ,UAAU,CAAC,EAAE;cACjEW,+BAA+B,CAAC,CAACX,UAAU,CAAC,CAAC;YACjD,CAAC,MACI;cACD,IAAI,CAACS,SAAS,CAACT,UAAU,EAAE,IAAI,CAAC;YACpC;UACJ,CAAC,CAAC;QACN;MACJ;IACJ,CAAC;IACDW,+BAA+B,CAACH,GAAG,CAAC;EACxC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA3B,iCAAiCA,CAAC2B,GAAG,EAAE;IACnC,MAAMM,WAAW,GAAG,IAAI3M,GAAG,CAAC,CAAC;IAC7B,MAAMyK,eAAe,GAAG,IAAIzK,GAAG,CAAC,CAAC;IACjC,MAAM4M,wBAAwB,GAAGA,CAACP,GAAG,EAAEQ,IAAI,KAAK;MAC5C,KAAK,MAAMzQ,KAAK,IAAIiQ,GAAG,EAAE;QACrB,IAAInM,KAAK,CAACC,OAAO,CAAC/D,KAAK,CAAC,EAAE;UACtB;UACA;UACAwQ,wBAAwB,CAACxQ,KAAK,EAAEyQ,IAAI,CAAC;QACzC,CAAC,MACI,IAAIJ,cAAc,CAACrQ,KAAK,CAAC,EAAE;UAC5B,IAAIuQ,WAAW,CAACpM,GAAG,CAACnE,KAAK,CAAC,EAAE;YACxB;YACA;YACA;YACA,IAAIqO,eAAe,CAAClK,GAAG,CAACnE,KAAK,CAAC,EAAE;cAC5ByQ,IAAI,CAACvN,OAAO,CAACwN,IAAI,IAAIrC,eAAe,CAACrN,GAAG,CAAC0P,IAAI,CAAC,CAAC;YACnD;YACA;UACJ;UACAH,WAAW,CAACvP,GAAG,CAAChB,KAAK,CAAC;UACtB,IAAI,IAAI,CAACmI,iBAAiB,CAAChE,GAAG,CAACnE,KAAK,CAAC,EAAE;YACnCyQ,IAAI,CAACvN,OAAO,CAACwN,IAAI,IAAIrC,eAAe,CAACrN,GAAG,CAAC0P,IAAI,CAAC,CAAC;UACnD;UACA;UACA,MAAMnH,SAAS,GAAGvJ,KAAK,CAAC1H,WAAW,CAAC;UACpCkY,wBAAwB,CAAC5C,aAAa,CAACrE,SAAS,CAAC7B,OAAO,CAAC,EAAE+I,IAAI,CAACpM,MAAM,CAACrE,KAAK,CAAC,CAAC;QAClF;MACJ;IACJ,CAAC;IACDwQ,wBAAwB,CAACP,GAAG,EAAE,EAAE,CAAC;IACjC,OAAO5B,eAAe;EAC1B;EACA;AACJ;AACA;AACA;AACA;AACA;EACIF,eAAeA,CAAChL,IAAI,EAAE2C,IAAI,EAAE;IACxB,IAAI,CAAC,IAAI,CAAC0C,aAAa,CAACrE,GAAG,CAAC2B,IAAI,CAAC,EAAE;MAC/B,IAAI,CAAC0C,aAAa,CAACpF,GAAG,CAAC0C,IAAI,EAAE,IAAInD,GAAG,CAAC,CAAC,CAAC;IAC3C;IACA,MAAMgO,WAAW,GAAG,IAAI,CAACnI,aAAa,CAACvJ,GAAG,CAAC6G,IAAI,CAAC;IAChD,IAAI,CAAC6K,WAAW,CAACxM,GAAG,CAAChB,IAAI,CAAC,EAAE;MACxB,MAAMyN,UAAU,GAAG3L,MAAM,CAACO,wBAAwB,CAACM,IAAI,EAAE3C,IAAI,CAAC;MAC9DwN,WAAW,CAACvN,GAAG,CAACD,IAAI,EAAEyN,UAAU,CAAC;IACrC;EACJ;EACArC,qBAAqBA,CAACzI,IAAI,EAAE+K,QAAQ,EAAEd,SAAS,EAAE;IAC7C,MAAMxE,GAAG,GAAGzF,IAAI,CAAC+K,QAAQ,CAAC;IAC1B,MAAMb,aAAa,GAAGzE,GAAG,CAACwE,SAAS,CAAC;IACpC,IAAI,CAACtH,aAAa,CAAClM,IAAI,CAAC;MAAEuT,MAAM,EAAEvE,GAAG;MAAEwE,SAAS;MAAEC;IAAc,CAAC,CAAC;EACtE;EACA;AACJ;AACA;AACA;AACA;EACIvD,6BAA6BA,CAAA,EAAG;IAC5B,IAAI,IAAI,CAACjF,gCAAgC,KAAK,IAAI,EAAE;MAChD,IAAI,CAACA,gCAAgC,GAAG,IAAI7E,GAAG,CAAC,CAAC;IACrD;IACAhK,yCAAyC,CAAC,CAAC,CAACuK,OAAO,CAAC,CAAClD,KAAK,EAAE0E,GAAG,KAAK,IAAI,CAAC8C,gCAAgC,CAACpE,GAAG,CAACsB,GAAG,EAAE1E,KAAK,CAAC,CAAC;EAC9H;EACA;AACJ;AACA;AACA;AACA;EACI8Q,+BAA+BA,CAAA,EAAG;IAC9B,IAAI,IAAI,CAACtJ,gCAAgC,KAAK,IAAI,EAAE;MAChD5O,gCAAgC,CAAC,IAAI,CAAC4O,gCAAgC,CAAC;MACvE,IAAI,CAACA,gCAAgC,GAAG,IAAI;IAChD;EACJ;EACAuJ,oBAAoBA,CAAA,EAAG;IACnB;IACA;IACAC,YAAY,CAAC,IAAI,CAACvI,aAAa,EAAGwI,EAAE,IAAK;MACrCA,EAAE,CAACnB,MAAM,CAACmB,EAAE,CAAClB,SAAS,CAAC,GAAGkB,EAAE,CAACjB,aAAa;IAC9C,CAAC,CAAC;IACF;IACA,IAAI,CAACxH,aAAa,CAACtF,OAAO,CAAC,CAACgO,IAAI,EAAEpL,IAAI,KAAK;MACvC,IAAI1O,iCAAiC,EAAE;QACnCC,YAAY,CAACuS,kBAAkB,CAAC9D,IAAI,CAAC;MACzC;MACAoL,IAAI,CAAChO,OAAO,CAAC,CAACiO,UAAU,EAAEhO,IAAI,KAAK;QAC/B,IAAI,CAACgO,UAAU,EAAE;UACb;UACA;UACA;UACA;UACA;UACA;UACA,OAAOrL,IAAI,CAAC3C,IAAI,CAAC;QACrB,CAAC,MACI;UACD8B,MAAM,CAACmM,cAAc,CAACtL,IAAI,EAAE3C,IAAI,EAAEgO,UAAU,CAAC;QACjD;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;IACF,IAAI,CAAC3I,aAAa,CAACvC,KAAK,CAAC,CAAC;IAC1B,IAAI,CAAC+C,6BAA6B,CAAC/C,KAAK,CAAC,CAAC;IAC1C,IAAI,CAAC6K,+BAA+B,CAAC,CAAC;IACtC;IACA/Y,YAAY,CAACD,kBAAkB,CAAC;EACpC;EACAiV,iBAAiBA,CAAA,EAAG;IAChB,MAAMsE,eAAe,CAAC;IAEtB3Y,oBAAoB,CAAC2Y,eAAe,EAAE;MAClC1J,SAAS,EAAE,CAAC,GAAG,IAAI,CAACkB,qBAAqB;IAC7C,CAAC,CAAC;IACF,MAAMlB,SAAS,GAAG,CACd9O,0BAA0B,CAAC,CAAC,EAC5B;MAAEgS,OAAO,EAAE/R,QAAQ;MAAE8R,UAAU,EAAEA,CAAA,KAAM,IAAI0G,cAAc,CAAC,IAAI;IAAE,CAAC,EACjE;MAAEzG,OAAO,EAAE9R,mBAAmB;MAAEiS,QAAQ,EAAE;QAAEuG,QAAQ,EAAE,IAAI,CAACrI;MAAmB;IAAE,CAAC,EACjF,GAAG,IAAI,CAACvB,SAAS,EACjB,GAAG,IAAI,CAACiB,iBAAiB,CAC5B;IACD,MAAMlB,OAAO,GAAG,CAAC2J,eAAe,EAAE,IAAI,CAAC9J,qBAAqB,EAAE,IAAI,CAACG,OAAO,IAAI,EAAE,CAAC;IACjF;IACAhP,oBAAoB,CAAC,IAAI,CAAC0Q,cAAc,EAAE;MACtC3B,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BC,OAAO;MACPE,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBD;IACJ,CAAC,EAAE,sCAAuC,IAAI,CAAC;IAC/C;IACA,IAAI,CAAC2E,6BAA6B,CAAC,IAAI,CAAClD,cAAc,CAAC;EAC3D;EACA,IAAIpK,QAAQA,CAAA,EAAG;IACX,IAAI,IAAI,CAAC0J,SAAS,KAAK,IAAI,EAAE;MACzB,OAAO,IAAI,CAACA,SAAS;IACzB;IACA,MAAMf,SAAS,GAAG,EAAE;IACpB,MAAM6J,eAAe,GAAG,IAAI,CAAClK,QAAQ,CAACtI,QAAQ,CAACC,GAAG,CAACjG,gBAAgB,CAAC;IACpEwY,eAAe,CAACtO,OAAO,CAACuO,IAAI,IAAI;MAC5B,IAAIA,IAAI,CAAC9J,SAAS,EAAE;QAChBA,SAAS,CAACpL,IAAI,CAACkV,IAAI,CAAC9J,SAAS,CAAC;MAClC;IACJ,CAAC,CAAC;IACF,IAAI,IAAI,CAACgB,iBAAiB,KAAK,IAAI,EAAE;MACjChB,SAAS,CAACpL,IAAI,CAAC,GAAG,IAAI,CAACoM,iBAAiB,CAAC;IAC7C;IACA,IAAI,CAACD,SAAS,GAAGzP,QAAQ,CAACyY,MAAM,CAAC;MAAE/J,SAAS;MAAEgK,MAAM,EAAE,IAAI,CAACrK,QAAQ,CAACtI;IAAS,CAAC,CAAC;IAC/E,OAAO,IAAI,CAAC0J,SAAS;EACzB;EACA;EACAkJ,0BAA0BA,CAAClH,QAAQ,EAAE;IACjC,MAAMD,KAAK,GAAGoH,gBAAgB,CAACnH,QAAQ,CAAC;IACxC,OAAO,IAAI,CAAC3B,wBAAwB,CAAC9J,GAAG,CAACwL,KAAK,CAAC,IAAI,IAAI;EAC3D;EACAqH,oBAAoBA,CAACnK,SAAS,EAAE;IAC5B,IAAI,CAACA,SAAS,IAAI,CAACA,SAAS,CAACrL,MAAM,IAAI,IAAI,CAACyM,wBAAwB,CAACgD,IAAI,KAAK,CAAC,EAC3E,OAAO,EAAE;IACb;IACA;IACA;IACA;IACA;IACA,OAAO6D,OAAO,CAACmC,gBAAgB,CAACpK,SAAS,EAAG+C,QAAQ,IAAK,IAAI,CAACkH,0BAA0B,CAAClH,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;EAC9G;EACAgF,sBAAsBA,CAAC/H,SAAS,EAAE;IAC9B,IAAI,CAACA,SAAS,IAAI,CAACA,SAAS,CAACrL,MAAM,IAAI,IAAI,CAACyM,wBAAwB,CAACgD,IAAI,KAAK,CAAC,EAC3E,OAAO,EAAE;IACb,MAAMiG,kBAAkB,GAAGD,gBAAgB,CAACpK,SAAS,CAAC;IACtD,MAAMhC,SAAS,GAAG,IAAI,CAACmM,oBAAoB,CAACE,kBAAkB,CAAC;IAC/D,MAAMC,mBAAmB,GAAG,CAAC,GAAGD,kBAAkB,EAAE,GAAGrM,SAAS,CAAC;IACjE,MAAMuM,KAAK,GAAG,EAAE;IAChB,MAAMC,uBAAuB,GAAG,IAAIvO,GAAG,CAAC,CAAC;IACzC;IACA;IACA;IACA;IACAoN,YAAY,CAACiB,mBAAmB,EAAGvH,QAAQ,IAAK;MAC5C,MAAMD,KAAK,GAAGoH,gBAAgB,CAACnH,QAAQ,CAAC;MACxC,IAAI,IAAI,CAAC3B,wBAAwB,CAAC5E,GAAG,CAACsG,KAAK,CAAC,EAAE;QAC1C,IAAI,CAAC0H,uBAAuB,CAAChO,GAAG,CAACsG,KAAK,CAAC,EAAE;UACrC0H,uBAAuB,CAACnR,GAAG,CAACyJ,KAAK,CAAC;UAClC;UACA;UACA;UACAyH,KAAK,CAACE,OAAO,CAAC;YAAE,GAAG1H,QAAQ;YAAEK,KAAK,EAAE;UAAM,CAAC,CAAC;QAChD;MACJ,CAAC,MACI;QACDmH,KAAK,CAACE,OAAO,CAAC1H,QAAQ,CAAC;MAC3B;IACJ,CAAC,CAAC;IACF,OAAOwH,KAAK;EAChB;EACAhD,oBAAoBA,CAACvH,SAAS,EAAE;IAC5B,OAAO,IAAI,CAACmK,oBAAoB,CAACnK,SAAS,CAAC,CAACrL,MAAM,GAAG,CAAC;EAC1D;EACA6S,6BAA6BA,CAACnB,WAAW,EAAEiB,KAAK,EAAE;IAC9C,MAAM1D,GAAG,GAAGyC,WAAW,CAACiB,KAAK,CAAC;IAC9B,IAAI1D,GAAG,IAAIA,GAAG,CAAC8G,iBAAiB,EAAE;MAC9B,IAAI,CAAClE,eAAe,CAACc,KAAK,EAAEjB,WAAW,CAAC;MACxC,MAAM9G,QAAQ,GAAGqE,GAAG,CAAC8G,iBAAiB;MACtC,MAAMC,kBAAkB,GAAI3K,SAAS,IAAK,IAAI,CAAC+H,sBAAsB,CAAC/H,SAAS,CAAC;MAChF,IAAI,CAAC4G,qBAAqB,CAACP,WAAW,EAAEiB,KAAK,EAAE,mBAAmB,CAAC;MACnE1D,GAAG,CAAC8G,iBAAiB,GAAIE,KAAK,IAAKrL,QAAQ,CAACqL,KAAK,EAAED,kBAAkB,CAAC;IAC1E;EACJ;AACJ;AACA,SAAShK,aAAaA,CAAA,EAAG;EACrB,OAAO;IACHuB,MAAM,EAAE,IAAIlD,gBAAgB,CAAC,CAAC;IAC9BQ,SAAS,EAAE,IAAIV,iBAAiB,CAAC,CAAC;IAClC4D,SAAS,EAAE,IAAI7D,iBAAiB,CAAC,CAAC;IAClCvG,IAAI,EAAE,IAAIyG,YAAY,CAAC;EAC3B,CAAC;AACL;AACA,SAAS2I,qBAAqBA,CAACrP,KAAK,EAAE;EAClC,MAAMuL,GAAG,GAAGwD,eAAe,CAAC/O,KAAK,CAAC;EAClC,OAAO,CAAC,CAACuL,GAAG,EAAEnE,UAAU;AAC5B;AACA,SAAS2H,eAAeA,CAAC/O,KAAK,EAAE;EAC5B,OAAOA,KAAK,CAACkO,IAAI,IAAI,IAAI;AAC7B;AACA,SAASmC,cAAcA,CAACrQ,KAAK,EAAE;EAC3B,OAAOA,KAAK,CAACuK,cAAc,CAAC,MAAM,CAAC;AACvC;AACA,SAAS+E,UAAUA,CAACtP,KAAK,EAAE;EACvB,OAAOqQ,cAAc,CAACrQ,KAAK,CAAC;AAChC;AACA,SAAS4N,aAAaA,CAAC4E,OAAO,EAAE;EAC5B,OAAOA,OAAO,YAAYC,QAAQ,GAAGD,OAAO,CAAC,CAAC,GAAGA,OAAO;AAC5D;AACA,SAAS5C,OAAOA,CAAC8C,MAAM,EAAE;EACrB,MAAMC,GAAG,GAAG,EAAE;EACdD,MAAM,CAACxP,OAAO,CAAClD,KAAK,IAAI;IACpB,IAAI8D,KAAK,CAACC,OAAO,CAAC/D,KAAK,CAAC,EAAE;MACtB2S,GAAG,CAACpW,IAAI,CAAC,GAAGqT,OAAO,CAAC5P,KAAK,CAAC,CAAC;IAC/B,CAAC,MACI;MACD2S,GAAG,CAACpW,IAAI,CAACyD,KAAK,CAAC;IACnB;EACJ,CAAC,CAAC;EACF,OAAO2S,GAAG;AACd;AACA,SAASC,UAAUA,CAAC5S,KAAK,EAAE;EACvB,OAAOA,KAAK;AAChB;AACA,SAAS+R,gBAAgBA,CAACpK,SAAS,EAAEkL,KAAK,GAAGD,UAAU,EAAE;EACrD,MAAMD,GAAG,GAAG,EAAE;EACd,KAAK,IAAIjI,QAAQ,IAAI/C,SAAS,EAAE;IAC5B,IAAIzO,uBAAuB,CAACwR,QAAQ,CAAC,EAAE;MACnCA,QAAQ,GAAGA,QAAQ,CAACoI,UAAU;IAClC;IACA,IAAIhP,KAAK,CAACC,OAAO,CAAC2G,QAAQ,CAAC,EAAE;MACzBiI,GAAG,CAACpW,IAAI,CAAC,GAAGwV,gBAAgB,CAACrH,QAAQ,EAAEmI,KAAK,CAAC,CAAC;IAClD,CAAC,MACI;MACDF,GAAG,CAACpW,IAAI,CAACsW,KAAK,CAACnI,QAAQ,CAAC,CAAC;IAC7B;EACJ;EACA,OAAOiI,GAAG;AACd;AACA,SAASI,gBAAgBA,CAACrI,QAAQ,EAAEuE,KAAK,EAAE;EACvC,OAAOvE,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,CAACuE,KAAK,CAAC;AACtE;AACA,SAAS4C,gBAAgBA,CAACnH,QAAQ,EAAE;EAChC,OAAOqI,gBAAgB,CAACrI,QAAQ,EAAE,SAAS,CAAC,IAAIA,QAAQ;AAC5D;AACA,SAASmF,qBAAqBA,CAAC7P,KAAK,EAAE;EAClC,OAAOA,KAAK,CAACuK,cAAc,CAAC,UAAU,CAAC;AAC3C;AACA,SAASyG,YAAYA,CAAC0B,MAAM,EAAEnY,EAAE,EAAE;EAC9B,KAAK,IAAIyY,GAAG,GAAGN,MAAM,CAACpW,MAAM,GAAG,CAAC,EAAE0W,GAAG,IAAI,CAAC,EAAEA,GAAG,EAAE,EAAE;IAC/CzY,EAAE,CAACmY,MAAM,CAACM,GAAG,CAAC,EAAEA,GAAG,CAAC;EACxB;AACJ;AACA,SAASlJ,gBAAgBA,CAACC,IAAI,EAAEkJ,YAAY,EAAE;EAC1C,OAAO,IAAIvX,KAAK,CAAE,GAAEqO,IAAK,wBAAuBkJ,YAAa,oCAAmC,CAAC;AACrG;AACA,MAAM3B,cAAc,CAAC;EACjBtW,WAAWA,CAACkY,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;EAC1B;EACAC,iBAAiBA,CAAC5F,UAAU,EAAE;IAC1B,IAAI,CAAC2F,OAAO,CAAC5F,oBAAoB,CAACC,UAAU,CAAC;IAC7C,OAAO,IAAIpU,gBAAgB,CAACoU,UAAU,CAAC;EAC3C;EACM6F,kBAAkBA,CAAC7F,UAAU,EAAE;IAAA,IAAA8F,MAAA;IAAA,OAAA/X,iBAAA;MACjC,MAAM+X,MAAI,CAACH,OAAO,CAAC1F,qBAAqB,CAACD,UAAU,CAAC;MACpD,OAAO,IAAIpU,gBAAgB,CAACoU,UAAU,CAAC;IAAC;EAC5C;EACA+F,iCAAiCA,CAAC/F,UAAU,EAAE;IAC1C,MAAMgG,eAAe,GAAG,IAAI,CAACJ,iBAAiB,CAAC5F,UAAU,CAAC;IAC1D,MAAMiG,kBAAkB,GAAG,IAAI,CAACN,OAAO,CAACvF,sBAAsB,CAACJ,UAAU,CAAC;IAC1E,OAAO,IAAInU,4BAA4B,CAACma,eAAe,EAAEC,kBAAkB,CAAC;EAChF;EACMC,kCAAkCA,CAAClG,UAAU,EAAE;IAAA,IAAAmG,MAAA;IAAA,OAAApY,iBAAA;MACjD,MAAMiY,eAAe,SAASG,MAAI,CAACN,kBAAkB,CAAC7F,UAAU,CAAC;MACjE,MAAMiG,kBAAkB,GAAGE,MAAI,CAACR,OAAO,CAACvF,sBAAsB,CAACJ,UAAU,CAAC;MAC1E,OAAO,IAAInU,4BAA4B,CAACma,eAAe,EAAEC,kBAAkB,CAAC;IAAC;EACjF;EACAG,UAAUA,CAAA,EAAG,CAAE;EACfC,aAAaA,CAAC9N,IAAI,EAAE,CAAE;EACtB+N,WAAWA,CAACtG,UAAU,EAAE;IACpB,MAAMuG,IAAI,GAAG,IAAI,CAACZ,OAAO,CAACxF,kBAAkB,CAAC,CAAC,CAAClR,OAAO,CAAC+Q,UAAU,CAAC;IAClE,OAAOuG,IAAI,IAAIA,IAAI,CAAC/O,EAAE,IAAIhG,SAAS;EACvC;AACJ;;AAEA;AACA,IAAIgV,kBAAkB,GAAG,CAAC;AAC1B;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAAA,EAAG;EAClB,OAAOC,WAAW,CAACC,QAAQ;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMD,WAAW,CAAC;EACdjZ,WAAWA,CAAA,EAAG;IACV;AACR;AACA;AACA;IACQ,IAAI,CAACmZ,2BAA2B,GAAGlX,4BAA4B;IAC/D;IACA,IAAI,CAACqK,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACqC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACyK,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,wBAAwB,GAAG,KAAK;EACzC;EACA;IAAS,IAAI,CAACC,SAAS,GAAG,IAAI;EAAE;EAChC,WAAWN,QAAQA,CAAA,EAAG;IAClB,OAAOD,WAAW,CAACO,SAAS,GAAGP,WAAW,CAACO,SAAS,IAAI,IAAIP,WAAW,CAAC,CAAC;EAC7E;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOQ,mBAAmBA,CAAC9K,QAAQ,EAAErC,QAAQ,EAAEoN,OAAO,EAAE;IACpD,MAAMxB,OAAO,GAAGe,WAAW,CAACC,QAAQ;IACpChB,OAAO,CAACuB,mBAAmB,CAAC9K,QAAQ,EAAErC,QAAQ,EAAEoN,OAAO,CAAC;IACxD,OAAOxB,OAAO;EAClB;EACA;AACJ;AACA;AACA;AACA;EACI,OAAOyB,oBAAoBA,CAAA,EAAG;IAC1BV,WAAW,CAACC,QAAQ,CAACS,oBAAoB,CAAC,CAAC;EAC/C;EACA,OAAOC,iBAAiBA,CAACC,MAAM,EAAE;IAC7B,OAAOZ,WAAW,CAACC,QAAQ,CAACU,iBAAiB,CAACC,MAAM,CAAC;EACzD;EACA;AACJ;AACA;AACA;EACI,OAAOvL,sBAAsBA,CAACC,SAAS,EAAE;IACrC,OAAO0K,WAAW,CAACC,QAAQ,CAAC5K,sBAAsB,CAACC,SAAS,CAAC;EACjE;EACA;AACJ;AACA;AACA;AACA;EACI,OAAOgD,iBAAiBA,CAAA,EAAG;IACvB,OAAO0H,WAAW,CAACC,QAAQ,CAAC3H,iBAAiB,CAAC,CAAC;EACnD;EACA,OAAO7C,cAAcA,CAACC,QAAQ,EAAE5G,QAAQ,EAAE;IACtC,OAAOkR,WAAW,CAACC,QAAQ,CAACxK,cAAc,CAACC,QAAQ,EAAE5G,QAAQ,CAAC;EAClE;EACA,OAAOkH,iBAAiBA,CAAC9C,SAAS,EAAEpE,QAAQ,EAAE;IAC1C,OAAOkR,WAAW,CAACC,QAAQ,CAACjK,iBAAiB,CAAC9C,SAAS,EAAEpE,QAAQ,CAAC;EACtE;EACA,OAAOqH,iBAAiBA,CAACC,SAAS,EAAEtH,QAAQ,EAAE;IAC1C,OAAOkR,WAAW,CAACC,QAAQ,CAAC9J,iBAAiB,CAACC,SAAS,EAAEtH,QAAQ,CAAC;EACtE;EACA,OAAOuH,YAAYA,CAACrK,IAAI,EAAE8C,QAAQ,EAAE;IAChC,OAAOkR,WAAW,CAACC,QAAQ,CAAC5J,YAAY,CAACrK,IAAI,EAAE8C,QAAQ,CAAC;EAC5D;EACA,OAAO+R,gBAAgBA,CAAC3N,SAAS,EAAEmE,QAAQ,EAAE;IACzC,OAAO2I,WAAW,CAACC,QAAQ,CAACY,gBAAgB,CAAC3N,SAAS,EAAEmE,QAAQ,CAAC;EACrE;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,OAAOD,kCAAkCA,CAAClE,SAAS,EAAEmE,QAAQ,EAAE;IAC3D,OAAO2I,WAAW,CAACC,QAAQ,CAAC7I,kCAAkC,CAAClE,SAAS,EAAEmE,QAAQ,CAAC;EACvF;EACA,OAAOd,gBAAgBA,CAACC,KAAK,EAAEC,QAAQ,EAAE;IACrC,OAAOuJ,WAAW,CAACC,QAAQ,CAAC1J,gBAAgB,CAACC,KAAK,EAAEC,QAAQ,CAAC;EACjE;EACA,OAAOvU,MAAMA,CAACsU,KAAK,EAAEsK,aAAa,EAAEC,KAAK,EAAE;IACvC,OAAOf,WAAW,CAACC,QAAQ,CAAC/d,MAAM,CAACsU,KAAK,EAAEsK,aAAa,EAAE1b,kBAAkB,CAAC2b,KAAK,CAAC,CAAC;EACvF;EACA;EACA,OAAO/V,GAAGA,CAACwL,KAAK,EAAEsK,aAAa,GAAG9b,QAAQ,CAACgc,kBAAkB,EAAED,KAAK,GAAG1b,WAAW,CAAC4b,OAAO,EAAE;IACxF,OAAOjB,WAAW,CAACC,QAAQ,CAAC/d,MAAM,CAACsU,KAAK,EAAEsK,aAAa,EAAEC,KAAK,CAAC;EACnE;EACA;AACJ;AACA;AACA;AACA;EACI,OAAOnb,qBAAqBA,CAACU,EAAE,EAAE;IAC7B,OAAO0Z,WAAW,CAACC,QAAQ,CAACra,qBAAqB,CAACU,EAAE,CAAC;EACzD;EACA,OAAO4a,eAAeA,CAAChO,SAAS,EAAE;IAC9B,OAAO8M,WAAW,CAACC,QAAQ,CAACiB,eAAe,CAAChO,SAAS,CAAC;EAC1D;EACA,OAAOiO,kBAAkBA,CAAA,EAAG;IACxB,OAAOnB,WAAW,CAACC,QAAQ,CAACkB,kBAAkB,CAAC,CAAC;EACpD;EACA,OAAOC,OAAOA,CAACC,MAAM,EAAE/a,EAAE,EAAEgb,OAAO,EAAE;IAChC,OAAOtB,WAAW,CAACC,QAAQ,CAACmB,OAAO,CAACC,MAAM,EAAE/a,EAAE,EAAEgb,OAAO,CAAC;EAC5D;EACA,WAAWjO,QAAQA,CAAA,EAAG;IAClB,OAAO2M,WAAW,CAACC,QAAQ,CAAC5M,QAAQ;EACxC;EACA,WAAWqC,QAAQA,CAAA,EAAG;IAClB,OAAOsK,WAAW,CAACC,QAAQ,CAACvK,QAAQ;EACxC;EACA,OAAO6L,YAAYA,CAAA,EAAG;IAClB,OAAOvB,WAAW,CAACC,QAAQ,CAACsB,YAAY,CAAC,CAAC;EAC9C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIf,mBAAmBA,CAAC9K,QAAQ,EAAErC,QAAQ,EAAEoN,OAAO,EAAE;IAC7C,IAAI,IAAI,CAACpN,QAAQ,IAAI,IAAI,CAACqC,QAAQ,EAAE;MAChC,MAAM,IAAIjO,KAAK,CAAC,8DAA8D,CAAC;IACnF;IACAuY,WAAW,CAACwB,2BAA2B,GAAGf,OAAO,EAAEgB,QAAQ;IAC3DzB,WAAW,CAAC0B,wCAAwC,GAAGjB,OAAO,EAAEkB,sBAAsB;IACtF3B,WAAW,CAAC4B,0CAA0C,GAAGnB,OAAO,EAAEoB,wBAAwB;IAC1F,IAAI,CAACxO,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACqC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACyK,SAAS,GAAG,IAAI/M,eAAe,CAAC,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACqC,QAAQ,CAAC;IAClE;IACA;IACA;IACA;IACApQ,oCAAoC,CAAC,IAAI,CAAC;EAC9C;EACA;AACJ;AACA;AACA;AACA;EACIob,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACS,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAAChB,SAAS,GAAG,IAAI;IACrB,IAAI,CAAC9M,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACqC,QAAQ,GAAG,IAAI;IACpBsK,WAAW,CAACwB,2BAA2B,GAAG1W,SAAS;IACnDxF,oCAAoC,CAAC,KAAK,CAAC;EAC/C;EACA6b,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACW,8BAA8B,CAAC,CAAC;IACrCvc,wBAAwB,CAAC,CAAC;IAC1B,IAAI,IAAI,CAAC4a,SAAS,KAAK,IAAI,EAAE;MACzB,IAAI,CAAC4B,QAAQ,CAACjF,oBAAoB,CAAC,CAAC;IACxC;IACA,IAAI,CAACqD,SAAS,GAAG,IAAI/M,eAAe,CAAC,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACqC,QAAQ,CAAC;IAClE;IACAlQ,4BAA4B,CAAC,IAAI,CAACwc,qCAAqC,IAAIlZ,iCAAiC,CAAC;IAC7G;IACArD,6BAA6B,CAAC,IAAI,CAACwc,uCAAuC,IAAIlZ,mCAAmC,CAAC;IAClH;IACA;IACA;IACA,IAAI;MACA,IAAI,CAACmZ,qBAAqB,CAAC,CAAC;IAChC,CAAC,SACO;MACJ,IAAI;QACA,IAAI,IAAI,CAACC,2BAA2B,CAAC,CAAC,EAAE;UACpC,IAAI,CAACC,qBAAqB,CAAC,CAAC;QAChC;MACJ,CAAC,SACO;QACJ,IAAI,CAAChC,cAAc,GAAG,IAAI;QAC1B,IAAI,CAACiC,wBAAwB,GAAGvX,SAAS;QACzC,IAAI,CAACwX,qCAAqC,GAAGxX,SAAS;QACtD,IAAI,CAACyX,uCAAuC,GAAGzX,SAAS;QACxD,IAAI,CAACoV,2BAA2B,GAAGlX,4BAA4B;MACnE;IACJ;IACA,OAAO,IAAI;EACf;EACA2X,iBAAiBA,CAACC,MAAM,EAAE;IACtB,IAAIA,MAAM,CAAC4B,MAAM,IAAI,IAAI,EAAE;MACvB,MAAM,IAAI/a,KAAK,CAAC,oDAAoD,CAAC;IACzE;IACA,IAAImZ,MAAM,CAAClN,SAAS,KAAK5I,SAAS,EAAE;MAChC,IAAI,CAACiX,QAAQ,CAAC3M,oBAAoB,CAACwL,MAAM,CAAClN,SAAS,CAAC;IACxD;IACA,OAAO,IAAI;EACf;EACA2B,sBAAsBA,CAACC,SAAS,EAAE;IAC9B,IAAI,CAACmN,qBAAqB,CAAC,gCAAgC,EAAE,2BAA2B,CAAC;IACzF;IACA;IACA;IACA;IACA,IAAI,CAACX,8BAA8B,CAAC,CAAC;IACrC;IACA;IACA,IAAI,CAACO,wBAAwB,GAAG/M,SAAS,CAACmM,QAAQ;IAClD,IAAI,CAACa,qCAAqC,GAAGhN,SAAS,CAACqM,sBAAsB;IAC7E,IAAI,CAACY,uCAAuC,GAAGjN,SAAS,CAACuM,wBAAwB;IACjF,IAAI,CAAC3B,2BAA2B,GAAG5K,SAAS,CAACL,kBAAkB,IAAIjM,4BAA4B;IAC/F;IACA;IACA,IAAI,CAACgZ,qCAAqC,GAAGtc,4BAA4B,CAAC,CAAC;IAC3EF,4BAA4B,CAAC,IAAI,CAACkd,iCAAiC,CAAC,CAAC,CAAC;IACtE,IAAI,CAACT,uCAAuC,GAAGtc,6BAA6B,CAAC,CAAC;IAC9EF,6BAA6B,CAAC,IAAI,CAACkd,mCAAmC,CAAC,CAAC,CAAC;IACzE,IAAI,CAACZ,QAAQ,CAAC1M,sBAAsB,CAACC,SAAS,CAAC;IAC/C,OAAO,IAAI;EACf;EACAgD,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACyJ,QAAQ,CAACzJ,iBAAiB,CAAC,CAAC;EAC5C;EACApW,MAAMA,CAACsU,KAAK,EAAEsK,aAAa,EAAEC,KAAK,EAAE;IAChC,IAAIvK,KAAK,KAAKoM,OAAO,EAAE;MACnB,OAAO,IAAI;IACf;IACA,MAAMC,SAAS,GAAG,CAAC,CAAC;IACpB,MAAMC,MAAM,GAAG,IAAI,CAAC9N,aAAa,CAACjK,QAAQ,CAACC,GAAG,CAACwL,KAAK,EAAEqM,SAAS,EAAEzd,kBAAkB,CAAC2b,KAAK,CAAC,CAAC;IAC3F,OAAO+B,MAAM,KAAKD,SAAS,GAAG,IAAI,CAACd,QAAQ,CAAChX,QAAQ,CAACC,GAAG,CAACwL,KAAK,EAAEsK,aAAa,EAAEC,KAAK,CAAC,GACjF+B,MAAM;EACd;EACA;EACA9X,GAAGA,CAACwL,KAAK,EAAEsK,aAAa,GAAG9b,QAAQ,CAACgc,kBAAkB,EAAED,KAAK,GAAG1b,WAAW,CAAC4b,OAAO,EAAE;IACjF,OAAO,IAAI,CAAC/e,MAAM,CAACsU,KAAK,EAAEsK,aAAa,EAAEC,KAAK,CAAC;EACnD;EACAnb,qBAAqBA,CAACU,EAAE,EAAE;IACtB,OAAOV,qBAAqB,CAAC,IAAI,CAAC1D,MAAM,CAAC2D,mBAAmB,CAAC,EAAES,EAAE,CAAC;EACtE;EACA8a,OAAOA,CAACC,MAAM,EAAE/a,EAAE,EAAEgb,OAAO,EAAE;IACzB,MAAMyB,MAAM,GAAG1B,MAAM,CAAC2B,GAAG,CAACC,CAAC,IAAI,IAAI,CAAC/gB,MAAM,CAAC+gB,CAAC,CAAC,CAAC;IAC9C,OAAO3c,EAAE,CAAC4c,KAAK,CAAC5B,OAAO,EAAEyB,MAAM,CAAC;EACpC;EACAtN,cAAcA,CAACC,QAAQ,EAAE5G,QAAQ,EAAE;IAC/B,IAAI,CAAC2T,qBAAqB,CAAC,gBAAgB,EAAE,0BAA0B,CAAC;IACxE,IAAI,CAACV,QAAQ,CAACtM,cAAc,CAACC,QAAQ,EAAE5G,QAAQ,CAAC;IAChD,OAAO,IAAI;EACf;EACAkH,iBAAiBA,CAAC9C,SAAS,EAAEpE,QAAQ,EAAE;IACnC,IAAI,CAAC2T,qBAAqB,CAAC,mBAAmB,EAAE,6BAA6B,CAAC;IAC9E,IAAI,CAACV,QAAQ,CAAC/L,iBAAiB,CAAC9C,SAAS,EAAEpE,QAAQ,CAAC;IACpD,OAAO,IAAI;EACf;EACAsI,kCAAkCA,CAAClE,SAAS,EAAEmE,QAAQ,EAAE;IACpD,IAAI,CAACoL,qBAAqB,CAAC,4CAA4C,EAAE,6EAA6E,CAAC;IACvJ,IAAI,CAACV,QAAQ,CAAC3K,kCAAkC,CAAClE,SAAS,EAAEmE,QAAQ,CAAC;IACrE,OAAO,IAAI;EACf;EACAlB,iBAAiBA,CAACC,SAAS,EAAEtH,QAAQ,EAAE;IACnC,IAAI,CAAC2T,qBAAqB,CAAC,mBAAmB,EAAE,6BAA6B,CAAC;IAC9E,IAAI,CAACV,QAAQ,CAAC5L,iBAAiB,CAACC,SAAS,EAAEtH,QAAQ,CAAC;IACpD,OAAO,IAAI;EACf;EACAuH,YAAYA,CAACrK,IAAI,EAAE8C,QAAQ,EAAE;IACzB,IAAI,CAAC2T,qBAAqB,CAAC,cAAc,EAAE,wBAAwB,CAAC;IACpE,IAAI,CAACV,QAAQ,CAAC1L,YAAY,CAACrK,IAAI,EAAE8C,QAAQ,CAAC;IAC1C,OAAO,IAAI;EACf;EACA;AACJ;AACA;EACIyH,gBAAgBA,CAACC,KAAK,EAAEC,QAAQ,EAAE;IAC9B,IAAI,CAACgM,qBAAqB,CAAC,kBAAkB,EAAE,mBAAmB,CAAC;IACnE,IAAI,CAACV,QAAQ,CAACxL,gBAAgB,CAACC,KAAK,EAAEC,QAAQ,CAAC;IAC/C,OAAO,IAAI;EACf;EACAoK,gBAAgBA,CAAC3N,SAAS,EAAEmE,QAAQ,EAAE;IAClC,OAAO,IAAI,CAACrB,iBAAiB,CAAC9C,SAAS,EAAE;MAAE/D,GAAG,EAAE;QAAEkI,QAAQ;QAAE8L,WAAW,EAAE;MAAK;IAAE,CAAC,CAAC;EACtF;EACAjC,eAAeA,CAACrP,IAAI,EAAE;IAClB,MAAMuR,qBAAqB,GAAG,IAAI,CAAClhB,MAAM,CAACgH,qBAAqB,CAAC;IAChE,MAAMma,QAAQ,GAAI,OAAMvD,kBAAkB,EAAG,EAAC;IAC9CsD,qBAAqB,CAACja,iBAAiB,CAACka,QAAQ,CAAC;IACjD,IAAIpgB,wBAAwB,CAAC4O,IAAI,CAAC,EAAE;MAChC,MAAM,IAAIpK,KAAK,CAAE,cAAaoK,IAAI,CAACiE,IAAK,6BAA4B,GAC/D,6EAA4E,CAAC;IACtF;IACA,MAAMkE,YAAY,GAAGnI,IAAI,CAACoI,IAAI;IAC9B,IAAI,CAACD,YAAY,EAAE;MACf,MAAM,IAAIvS,KAAK,CAAE,kBAAiB9E,UAAU,CAACkP,IAAI,CAAE,0BAAyB,CAAC;IACjF;IACA,MAAMyR,gBAAgB,GAAG,IAAIvf,wBAAwB,CAACiW,YAAY,CAAC;IACnE,MAAMuJ,aAAa,GAAGA,CAAA,KAAM;MACxB,MAAM7Z,YAAY,GAAG4Z,gBAAgB,CAAC7F,MAAM,CAACzY,QAAQ,CAACwe,IAAI,EAAE,EAAE,EAAG,IAAGH,QAAS,EAAC,EAAE,IAAI,CAACrO,aAAa,CAAC;MACnG,OAAO,IAAI,CAACpP,qBAAqB,CAAC,MAAM;QACpC,MAAM6d,YAAY,GAAG,IAAI,CAACvhB,MAAM,CAAC4D,yBAAyB,EAAE,IAAI,CAAC,KAAK,IAAI;QAC1E,MAAM4d,OAAO,GAAGD,YAAY,GAAG,IAAIpY,yBAAyB,CAAC3B,YAAY,CAAC,GACtE,IAAI4C,iCAAiC,CAAC5C,YAAY,CAAC;QACvDga,OAAO,CAACjY,UAAU,CAAC,CAAC;QACpB,OAAOiY,OAAO;MAClB,CAAC,CAAC;IACN,CAAC;IACD,MAAMC,QAAQ,GAAG,IAAI,CAACzhB,MAAM,CAACsH,wBAAwB,EAAE,KAAK,CAAC;IAC7D,MAAMU,MAAM,GAAGyZ,QAAQ,GAAG,IAAI,GAAG,IAAI,CAACzhB,MAAM,CAACG,MAAM,EAAE,IAAI,CAAC;IAC1D,MAAMqhB,OAAO,GAAGxZ,MAAM,GAAGA,MAAM,CAACuD,GAAG,CAAC8V,aAAa,CAAC,GAAGA,aAAa,CAAC,CAAC;IACpE,IAAI,CAAClD,eAAe,CAAC/X,IAAI,CAACob,OAAO,CAAC;IAClC,OAAOA,OAAO;EAClB;EACA;AACJ;AACA;AACA;EACI,IAAI3B,QAAQA,CAAA,EAAG;IACX,IAAI,IAAI,CAAC5B,SAAS,KAAK,IAAI,EAAE;MACzB,MAAM,IAAI1Y,KAAK,CAAE,kDAAiD,CAAC;IACvE;IACA,OAAO,IAAI,CAAC0Y,SAAS;EACzB;EACA;AACJ;AACA;AACA;EACI,IAAInL,aAAaA,CAAA,EAAG;IAChB,IAAI,IAAI,CAACoL,cAAc,KAAK,IAAI,EAAE;MAC9B,IAAI,CAACA,cAAc,GAAG,IAAI,CAAC2B,QAAQ,CAAClJ,QAAQ,CAAC,CAAC;IAClD;IACA,OAAO,IAAI,CAACuH,cAAc;EAC9B;EACAqC,qBAAqBA,CAACmB,UAAU,EAAEC,iBAAiB,EAAE;IACjD,IAAI,IAAI,CAACzD,cAAc,KAAK,IAAI,EAAE;MAC9B,MAAM,IAAI3Y,KAAK,CAAE,UAASoc,iBAAkB,uDAAsD,GAC7F,mDAAkDD,UAAW,KAAI,CAAC;IAC3E;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI9B,8BAA8BA,CAAA,EAAG;IAC7B;IACA;IACA,IAAI,CAAC,IAAI,CAACxB,wBAAwB,IAAI,IAAI,CAACF,cAAc,KAAK,IAAI,EAAE;MAChEra,wCAAwC,CAAC,CAAC;IAC9C;IACA,IAAI,CAACua,wBAAwB,GAAG,IAAI;EACxC;EACA4B,qBAAqBA,CAAA,EAAG;IACpB,IAAI4B,UAAU,GAAG,CAAC;IAClB,IAAI,CAACzD,eAAe,CAACpR,OAAO,CAAEyU,OAAO,IAAK;MACtC,IAAI;QACAA,OAAO,CAACtY,OAAO,CAAC,CAAC;MACrB,CAAC,CACD,OAAO2Y,CAAC,EAAE;QACND,UAAU,EAAE;QACZE,OAAO,CAACxW,KAAK,CAAC,mCAAmC,EAAE;UAC/C0F,SAAS,EAAEwQ,OAAO,CAAClZ,iBAAiB;UACpCyZ,UAAU,EAAEF;QAChB,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;IACF,IAAI,CAAC1D,eAAe,GAAG,EAAE;IACzB,IAAIyD,UAAU,GAAG,CAAC,IAAI,IAAI,CAACI,2BAA2B,CAAC,CAAC,EAAE;MACtD,MAAMzc,KAAK,CAAE,GAAEqc,UAAW,IAAIA,UAAU,KAAK,CAAC,GAAG,WAAW,GAAG,YAAc,GAAE,GAC1E,6BAA4B,CAAC;IACtC;EACJ;EACAI,2BAA2BA,CAAA,EAAG;IAC1B,MAAMC,eAAe,GAAG,IAAI,CAAC9B,wBAAwB;IACrD,MAAM+B,kBAAkB,GAAGpE,WAAW,CAACwB,2BAA2B;IAClE;IACA,IAAI,CAAC2C,eAAe,IAAI,CAACC,kBAAkB,EAAE;MACzC,OAAOvb,0CAA0C;IACrD;IACA;IACA,OAAOsb,eAAe,EAAEE,aAAa,IAAID,kBAAkB,EAAEC,aAAa,IACtE,IAAI,CAAClC,2BAA2B,CAAC,CAAC;EAC1C;EACAO,iCAAiCA,CAAA,EAAG;IAChC;IACA,OAAO,IAAI,CAACJ,qCAAqC,IAC7CtC,WAAW,CAAC0B,wCAAwC,IAAI5Y,iCAAiC;EACjG;EACA6Z,mCAAmCA,CAAA,EAAG;IAClC;IACA,OAAO,IAAI,CAACJ,uCAAuC,IAC/CvC,WAAW,CAAC4B,0CAA0C,IACtD7Y,mCAAmC;EAC3C;EACAoZ,2BAA2BA,CAAA,EAAG;IAC1B,OAAO,IAAI,CAACE,wBAAwB,EAAEiC,gBAAgB,IAClDtE,WAAW,CAACwB,2BAA2B,EAAE8C,gBAAgB,IACzDzb,0CAA0C;EAClD;EACA0b,qBAAqBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACrE,2BAA2B;EAC3C;EACAkC,qBAAqBA,CAAA,EAAG;IACpB;IACA,IAAI,IAAI,CAAChC,cAAc,KAAK,IAAI,EAAE;MAC9B;IACJ;IACA;IACA;IACA,MAAMoE,YAAY,GAAG,IAAI,CAACtiB,MAAM,CAACgH,qBAAqB,CAAC;IACvD,IAAI;MACA,IAAI,CAACkX,cAAc,CAAChV,OAAO,CAAC,CAAC;IACjC,CAAC,CACD,OAAO2Y,CAAC,EAAE;MACN,IAAI,IAAI,CAACG,2BAA2B,CAAC,CAAC,EAAE;QACpC,MAAMH,CAAC;MACX,CAAC,MACI;QACDC,OAAO,CAACxW,KAAK,CAAC,0CAA0C,EAAE;UACtD0F,SAAS,EAAE,IAAI,CAACkN,cAAc,CAAC3V,QAAQ;UACvCwZ,UAAU,EAAEF;QAChB,CAAC,CAAC;MACN;IACJ,CAAC,SACO;MACJS,YAAY,CAACnb,qBAAqB,GAAG,CAAC;IAC1C;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIkY,YAAYA,CAAA,EAAG;IACX,IAAI,CAACrf,MAAM,CAACI,gBAAgB,CAAC,CAACqJ,KAAK,CAAC,CAAC;EACzC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMiX,OAAO,GAAG5C,WAAW;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS9d,MAAMA,CAACmf,MAAM,EAAE/a,EAAE,EAAE;EACxB,MAAM2Y,OAAO,GAAGe,WAAW,CAACC,QAAQ;EACpC;EACA,OAAO,YAAY;IACf,OAAOhB,OAAO,CAACmC,OAAO,CAACC,MAAM,EAAE/a,EAAE,EAAE,IAAI,CAAC;EAC5C,CAAC;AACL;AACA;AACA;AACA;AACA,MAAMme,kBAAkB,CAAC;EACrB1d,WAAWA,CAAC2d,UAAU,EAAE;IACpB,IAAI,CAACA,UAAU,GAAGA,UAAU;EAChC;EACAC,UAAUA,CAAA,EAAG;IACT,MAAMrP,SAAS,GAAG,IAAI,CAACoP,UAAU,CAAC,CAAC;IACnC,IAAIpP,SAAS,EAAE;MACX0K,WAAW,CAAC3K,sBAAsB,CAACC,SAAS,CAAC;IACjD;EACJ;EACApT,MAAMA,CAACmf,MAAM,EAAE/a,EAAE,EAAE;IACf,MAAMse,IAAI,GAAG,IAAI;IACjB;IACA,OAAO,YAAY;MACfA,IAAI,CAACD,UAAU,CAAC,CAAC;MACjB,OAAOziB,MAAM,CAACmf,MAAM,EAAE/a,EAAE,CAAC,CAACue,IAAI,CAAC,IAAI,CAAC;IACxC,CAAC;EACL;AACJ;AACA,SAASC,UAAUA,CAACxP,SAAS,EAAEhP,EAAE,EAAE;EAC/B,IAAIA,EAAE,EAAE;IACJ;IACA,OAAO,YAAY;MACf,MAAM2Y,OAAO,GAAGe,WAAW,CAACC,QAAQ;MACpC,IAAI3K,SAAS,EAAE;QACX2J,OAAO,CAAC5J,sBAAsB,CAACC,SAAS,CAAC;MAC7C;MACA,OAAOhP,EAAE,CAAC4c,KAAK,CAAC,IAAI,CAAC;IACzB,CAAC;EACL;EACA,OAAO,IAAIuB,kBAAkB,CAAC,MAAMnP,SAAS,CAAC;AAClD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAyP,UAAU,CAACC,UAAU,GAAGC,cAAc,CAAC,KAAK,CAAC,CAAC;AAC9C;AACA;AACA;AACA;AACA;AACAF,UAAU,CAACG,SAAS,GAAGD,cAAc,CAAC,IAAI,CAAC,CAAC;AAC5C,SAASA,cAAcA,CAACE,qBAAqB,EAAE;EAC3C,OAAO,MAAM;IACT,MAAMlG,OAAO,GAAGe,WAAW,CAACC,QAAQ;IACpC,IAAIhB,OAAO,CAACkD,2BAA2B,CAAC,CAAC,KAAKgD,qBAAqB,EAAE;MACjElG,OAAO,CAACkC,kBAAkB,CAAC,CAAC;MAC5BpT,0BAA0B,CAAC,CAAC;IAChC;EACJ,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMqX,oCAAoC,GAAG,EAAE;;AAE/C;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA,SAAS3b,gBAAgB,EAAEH,0BAA0B,EAAEE,wBAAwB,EAAE1C,iBAAiB,EAAE2d,kBAAkB,EAAE7B,OAAO,EAAE1Z,qBAAqB,EAAEkc,oCAAoC,EAAEve,KAAK,EAAEwH,oBAAoB,EAAEL,SAAS,EAAErC,KAAK,EAAE2C,eAAe,EAAEyR,UAAU,EAAE7d,MAAM,EAAE4L,kBAAkB,EAAElC,IAAI,EAAEvF,YAAY,EAAEye,UAAU,EAAEtW,iBAAiB,IAAI6W,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}