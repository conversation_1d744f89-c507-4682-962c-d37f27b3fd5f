{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./admin.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./admin.component.css?ngResource\";\nimport { Component, inject } from '@angular/core';\nimport { RouterOutlet } from '@angular/router';\nimport { CommonModule } from '@angular/common';\nimport { TogglingService } from '../toggling.service';\nimport { AuthService } from '../../shared/services/auth.service';\nlet AdminComponent = class AdminComponent {\n  constructor(authService) {\n    this.authService = authService;\n    this.togglingservice = inject(TogglingService);\n  }\n  get isAdmin() {\n    return this.authService.isAdmin();\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: AuthService\n    }];\n  }\n};\nAdminComponent = __decorate([Component({\n  selector: 'app-admin',\n  standalone: true,\n  imports: [CommonModule, RouterOutlet],\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], AdminComponent);\nexport { AdminComponent };", "map": {"version": 3, "names": ["Component", "inject", "RouterOutlet", "CommonModule", "TogglingService", "AuthService", "AdminComponent", "constructor", "authService", "togglingservice", "isAdmin", "__decorate", "selector", "standalone", "imports", "template", "__NG_CLI_RESOURCE__0"], "sources": ["C:\\Users\\<USER>\\source\\ai-hub\\WebApp\\src\\app\\admin\\admin.component.ts"], "sourcesContent": ["import { Component, inject } from '@angular/core';\r\nimport { RouterOutlet } from '@angular/router';\r\nimport { CommonModule } from '@angular/common';\r\nimport { TogglingService } from '../toggling.service';\r\nimport { AuthService } from '../../shared/services/auth.service';\r\n\r\n@Component({\r\n  selector: 'app-admin',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    RouterOutlet\r\n  ],\r\n  templateUrl: './admin.component.html',\r\n  styleUrl: './admin.component.css',\r\n})\r\nexport class AdminComponent {\r\n  constructor(private authService: AuthService) { }\r\n\r\n  togglingservice = inject(TogglingService)\r\n\r\n  get isAdmin() {\r\n    return this.authService.isAdmin();\r\n  }\r\n}\r\n"], "mappings": ";;;AAAA,SAASA,SAAS,EAAEC,MAAM,QAAQ,eAAe;AACjD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,WAAW,QAAQ,oCAAoC;AAYzD,IAAMC,cAAc,GAApB,MAAMA,cAAc;EACzBC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;IAE/B,KAAAC,eAAe,GAAGR,MAAM,CAACG,eAAe,CAAC;EAFO;EAIhD,IAAIM,OAAOA,CAAA;IACT,OAAO,IAAI,CAACF,WAAW,CAACE,OAAO,EAAE;EACnC;;;;;;;AAPWJ,cAAc,GAAAK,UAAA,EAV1BX,SAAS,CAAC;EACTY,QAAQ,EAAE,WAAW;EACrBC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPX,YAAY,EACZD,YAAY,CACb;EACDa,QAAA,EAAAC,oBAAqC;;CAEtC,CAAC,C,EACWV,cAAc,CAQ1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}