{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/source/ai-hub/WebApp/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n(function () {\n  \"use strict\";\n\n  try {\n    if (typeof document < \"u\") {\n      var o = document.createElement(\"style\");\n      o.appendChild(document.createTextNode(`.link-tool{position:relative}.link-tool__input{padding-left:38px;background-image:url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' fill='none'%3E%3Cpath stroke='%23707684' stroke-linecap='round' stroke-width='2' d='m7.7 12.6-.021.02a2.795 2.795 0 0 0-.044 4.005v0a2.795 2.795 0 0 0 3.936.006l1.455-1.438a3 3 0 0 0 .34-3.866l-.146-.207'/%3E%3Cpath stroke='%23707684' stroke-linecap='round' stroke-width='2' d='m16.22 11.12.136-.14c.933-.954.992-2.46.135-3.483v0a2.597 2.597 0 0 0-3.664-.32L11.39 8.386a3 3 0 0 0-.301 4.3l.031.034'/%3E%3C/svg%3E\");background-repeat:no-repeat;background-position:10px;white-space:nowrap;text-overflow:ellipsis;overflow:hidden}.link-tool__input-holder{position:relative}.link-tool__input-holder--error .link-tool__input{background-image:url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' fill='none'%3E%3Cpath stroke='rgb(224, 147, 147)' stroke-linecap='round' stroke-width='2' d='m7.7 12.6-.021.02a2.795 2.795 0 0 0-.044 4.005v0a2.795 2.795 0 0 0 3.936.006l1.455-1.438a3 3 0 0 0 .34-3.866l-.146-.207'/%3E%3Cpath stroke='rgb(224, 147, 147)' stroke-linecap='round' stroke-width='2' d='m16.22 11.12.136-.14c.933-.954.992-2.46.135-3.483v0a2.597 2.597 0 0 0-3.664-.32L11.39 8.386a3 3 0 0 0-.301 4.3l.031.034'/%3E%3C/svg%3E\");background-color:#fff3f6;border-color:#f3e0e0;color:#a95a5a;box-shadow:inset 0 1px 3px #923e3e0d}.link-tool__input[contentEditable=true][data-placeholder]:before{position:absolute;content:attr(data-placeholder);color:#707684;font-weight:400;opacity:0}.link-tool__input[contentEditable=true][data-placeholder]:empty:before{opacity:1}.link-tool__input[contentEditable=true][data-placeholder]:empty:focus:before{opacity:0}.link-tool__progress{position:absolute;box-shadow:inset 0 1px 3px #66556b0a;height:100%;width:0;background-color:#f4f5f7;z-index:-1}.link-tool__progress--loading{-webkit-animation:progress .5s ease-in;-webkit-animation-fill-mode:forwards}.link-tool__progress--loaded{width:100%}.link-tool__content{display:block;padding:25px;border-radius:2px;box-shadow:0 0 0 2px #fff;color:initial!important;text-decoration:none!important}.link-tool__content:after{content:\"\";clear:both;display:table}.link-tool__content--rendered{background:#fff;border:1px solid rgba(201,201,204,.48);box-shadow:0 1px 3px #0000001a;border-radius:6px;will-change:filter;animation:link-in .45s 1 cubic-bezier(.215,.61,.355,1)}.link-tool__content--rendered:hover{box-shadow:0 0 3px #00000029}.link-tool__image{background-position:center center;background-repeat:no-repeat;background-size:cover;margin:0 0 0 30px;width:65px;height:65px;border-radius:3px;float:right}.link-tool__title{font-size:17px;font-weight:600;line-height:1.5em;margin:0 0 10px}.link-tool__title+.link-tool__anchor{margin-top:25px}.link-tool__description{margin:0 0 20px;font-size:15px;line-height:1.55em;display:-webkit-box;-webkit-line-clamp:3;-webkit-box-orient:vertical;overflow:hidden}.link-tool__anchor{display:block;font-size:15px;line-height:1em;color:#888!important;border:0!important;padding:0!important}@keyframes link-in{0%{filter:blur(5px)}to{filter:none}}.codex-editor--narrow .link-tool__image{display:none}@-webkit-keyframes progress{0%{width:0}to{width:85%}}`)), document.head.appendChild(o);\n    }\n  } catch (t) {\n    console.error(\"vite-plugin-css-injected-by-js\", t);\n  }\n})();\nvar C = typeof globalThis < \"u\" ? globalThis : typeof window < \"u\" ? window : typeof global < \"u\" ? global : typeof self < \"u\" ? self : {};\nfunction O(k) {\n  return k && k.__esModule && Object.prototype.hasOwnProperty.call(k, \"default\") ? k.default : k;\n}\n(function (k) {\n  var w = function () {\n      try {\n        return !!Symbol.iterator;\n      } catch {\n        return !1;\n      }\n    },\n    d = w(),\n    v = function (n) {\n      var o = {\n        next: function () {\n          var e = n.shift();\n          return {\n            done: e === void 0,\n            value: e\n          };\n        }\n      };\n      return d && (o[Symbol.iterator] = function () {\n        return o;\n      }), o;\n    },\n    c = function (n) {\n      return encodeURIComponent(n).replace(/%20/g, \"+\");\n    },\n    i = function (n) {\n      return decodeURIComponent(String(n).replace(/\\+/g, \" \"));\n    },\n    a = function () {\n      var n = function (e) {\n          Object.defineProperty(this, \"_entries\", {\n            writable: !0,\n            value: {}\n          });\n          var s = typeof e;\n          if (s !== \"undefined\") if (s === \"string\") e !== \"\" && this._fromString(e);else if (e instanceof n) {\n            var h = this;\n            e.forEach(function (u, f) {\n              h.append(f, u);\n            });\n          } else if (e !== null && s === \"object\") {\n            if (Object.prototype.toString.call(e) === \"[object Array]\") for (var t = 0; t < e.length; t++) {\n              var y = e[t];\n              if (Object.prototype.toString.call(y) === \"[object Array]\" || y.length !== 2) this.append(y[0], y[1]);else throw new TypeError(\"Expected [string, any] as entry at index \" + t + \" of URLSearchParams's input\");\n            } else for (var r in e) e.hasOwnProperty(r) && this.append(r, e[r]);\n          } else throw new TypeError(\"Unsupported input's type for URLSearchParams\");\n        },\n        o = n.prototype;\n      o.append = function (e, s) {\n        e in this._entries ? this._entries[e].push(String(s)) : this._entries[e] = [String(s)];\n      }, o.delete = function (e) {\n        delete this._entries[e];\n      }, o.get = function (e) {\n        return e in this._entries ? this._entries[e][0] : null;\n      }, o.getAll = function (e) {\n        return e in this._entries ? this._entries[e].slice(0) : [];\n      }, o.has = function (e) {\n        return e in this._entries;\n      }, o.set = function (e, s) {\n        this._entries[e] = [String(s)];\n      }, o.forEach = function (e, s) {\n        var h;\n        for (var t in this._entries) if (this._entries.hasOwnProperty(t)) {\n          h = this._entries[t];\n          for (var y = 0; y < h.length; y++) e.call(s, h[y], t, this);\n        }\n      }, o.keys = function () {\n        var e = [];\n        return this.forEach(function (s, h) {\n          e.push(h);\n        }), v(e);\n      }, o.values = function () {\n        var e = [];\n        return this.forEach(function (s) {\n          e.push(s);\n        }), v(e);\n      }, o.entries = function () {\n        var e = [];\n        return this.forEach(function (s, h) {\n          e.push([h, s]);\n        }), v(e);\n      }, d && (o[Symbol.iterator] = o.entries), o.toString = function () {\n        var e = [];\n        return this.forEach(function (s, h) {\n          e.push(c(h) + \"=\" + c(s));\n        }), e.join(\"&\");\n      }, k.URLSearchParams = n;\n    },\n    p = function () {\n      try {\n        var n = k.URLSearchParams;\n        return new n(\"?a=1\").toString() === \"a=1\" && typeof n.prototype.set == \"function\";\n      } catch {\n        return !1;\n      }\n    };\n  p() || a();\n  var l = k.URLSearchParams.prototype;\n  typeof l.sort != \"function\" && (l.sort = function () {\n    var n = this,\n      o = [];\n    this.forEach(function (s, h) {\n      o.push([h, s]), n._entries || n.delete(h);\n    }), o.sort(function (s, h) {\n      return s[0] < h[0] ? -1 : s[0] > h[0] ? 1 : 0;\n    }), n._entries && (n._entries = {});\n    for (var e = 0; e < o.length; e++) this.append(o[e][0], o[e][1]);\n  }), typeof l._fromString != \"function\" && Object.defineProperty(l, \"_fromString\", {\n    enumerable: !1,\n    configurable: !1,\n    writable: !1,\n    value: function (n) {\n      if (this._entries) this._entries = {};else {\n        var o = [];\n        this.forEach(function (t, y) {\n          o.push(y);\n        });\n        for (var e = 0; e < o.length; e++) this.delete(o[e]);\n      }\n      n = n.replace(/^\\?/, \"\");\n      for (var s = n.split(\"&\"), h, e = 0; e < s.length; e++) h = s[e].split(\"=\"), this.append(i(h[0]), h.length > 1 ? i(h[1]) : \"\");\n    }\n  });\n})(typeof C < \"u\" ? C : typeof window < \"u\" ? window : typeof self < \"u\" ? self : C);\n(function (k) {\n  var w = function () {\n      try {\n        var c = new k.URL(\"b\", \"http://a\");\n        return c.pathname = \"c d\", c.href === \"http://a/c%20d\" && c.searchParams;\n      } catch {\n        return !1;\n      }\n    },\n    d = function () {\n      var c = k.URL,\n        i = function (l, n) {\n          typeof l != \"string\" && (l = String(l));\n          var o = document,\n            e;\n          if (n && (k.location === void 0 || n !== k.location.href)) {\n            o = document.implementation.createHTMLDocument(\"\"), e = o.createElement(\"base\"), e.href = n, o.head.appendChild(e);\n            try {\n              if (e.href.indexOf(n) !== 0) throw new Error(e.href);\n            } catch (m) {\n              throw new Error(\"URL unable to set base \" + n + \" due to \" + m);\n            }\n          }\n          var s = o.createElement(\"a\");\n          s.href = l, e && (o.body.appendChild(s), s.href = s.href);\n          var h = o.createElement(\"input\");\n          if (h.type = \"url\", h.value = l, s.protocol === \":\" || !/:/.test(s.href) || !h.checkValidity() && !n) throw new TypeError(\"Invalid URL\");\n          Object.defineProperty(this, \"_anchorElement\", {\n            value: s\n          });\n          var t = new k.URLSearchParams(this.search),\n            y = !0,\n            r = !0,\n            u = this;\n          [\"append\", \"delete\", \"set\"].forEach(function (m) {\n            var b = t[m];\n            t[m] = function () {\n              b.apply(t, arguments), y && (r = !1, u.search = t.toString(), r = !0);\n            };\n          }), Object.defineProperty(this, \"searchParams\", {\n            value: t,\n            enumerable: !0\n          });\n          var f = void 0;\n          Object.defineProperty(this, \"_updateSearchParams\", {\n            enumerable: !1,\n            configurable: !1,\n            writable: !1,\n            value: function () {\n              this.search !== f && (f = this.search, r && (y = !1, this.searchParams._fromString(this.search), y = !0));\n            }\n          });\n        },\n        a = i.prototype,\n        p = function (l) {\n          Object.defineProperty(a, l, {\n            get: function () {\n              return this._anchorElement[l];\n            },\n            set: function (n) {\n              this._anchorElement[l] = n;\n            },\n            enumerable: !0\n          });\n        };\n      [\"hash\", \"host\", \"hostname\", \"port\", \"protocol\"].forEach(function (l) {\n        p(l);\n      }), Object.defineProperty(a, \"search\", {\n        get: function () {\n          return this._anchorElement.search;\n        },\n        set: function (l) {\n          this._anchorElement.search = l, this._updateSearchParams();\n        },\n        enumerable: !0\n      }), Object.defineProperties(a, {\n        toString: {\n          get: function () {\n            var l = this;\n            return function () {\n              return l.href;\n            };\n          }\n        },\n        href: {\n          get: function () {\n            return this._anchorElement.href.replace(/\\?$/, \"\");\n          },\n          set: function (l) {\n            this._anchorElement.href = l, this._updateSearchParams();\n          },\n          enumerable: !0\n        },\n        pathname: {\n          get: function () {\n            return this._anchorElement.pathname.replace(/(^\\/?)/, \"/\");\n          },\n          set: function (l) {\n            this._anchorElement.pathname = l;\n          },\n          enumerable: !0\n        },\n        origin: {\n          get: function () {\n            var l = {\n                \"http:\": 80,\n                \"https:\": 443,\n                \"ftp:\": 21\n              }[this._anchorElement.protocol],\n              n = this._anchorElement.port != l && this._anchorElement.port !== \"\";\n            return this._anchorElement.protocol + \"//\" + this._anchorElement.hostname + (n ? \":\" + this._anchorElement.port : \"\");\n          },\n          enumerable: !0\n        },\n        password: {\n          // TODO\n          get: function () {\n            return \"\";\n          },\n          set: function (l) {},\n          enumerable: !0\n        },\n        username: {\n          // TODO\n          get: function () {\n            return \"\";\n          },\n          set: function (l) {},\n          enumerable: !0\n        }\n      }), i.createObjectURL = function (l) {\n        return c.createObjectURL.apply(c, arguments);\n      }, i.revokeObjectURL = function (l) {\n        return c.revokeObjectURL.apply(c, arguments);\n      }, k.URL = i;\n    };\n  if (w() || d(), k.location !== void 0 && !(\"origin\" in k.location)) {\n    var v = function () {\n      return k.location.protocol + \"//\" + k.location.hostname + (k.location.port ? \":\" + k.location.port : \"\");\n    };\n    try {\n      Object.defineProperty(k.location, \"origin\", {\n        get: v,\n        enumerable: !0\n      });\n    } catch {\n      setInterval(function () {\n        k.location.origin = v();\n      }, 100);\n    }\n  }\n})(typeof C < \"u\" ? C : typeof window < \"u\" ? window : typeof self < \"u\" ? self : C);\nvar j = {\n  exports: {}\n};\n(function (k, w) {\n  (function (d, v) {\n    k.exports = v();\n  })(window, function () {\n    return function (d) {\n      var v = {};\n      function c(i) {\n        if (v[i]) return v[i].exports;\n        var a = v[i] = {\n          i,\n          l: !1,\n          exports: {}\n        };\n        return d[i].call(a.exports, a, a.exports, c), a.l = !0, a.exports;\n      }\n      return c.m = d, c.c = v, c.d = function (i, a, p) {\n        c.o(i, a) || Object.defineProperty(i, a, {\n          enumerable: !0,\n          get: p\n        });\n      }, c.r = function (i) {\n        typeof Symbol < \"u\" && Symbol.toStringTag && Object.defineProperty(i, Symbol.toStringTag, {\n          value: \"Module\"\n        }), Object.defineProperty(i, \"__esModule\", {\n          value: !0\n        });\n      }, c.t = function (i, a) {\n        if (1 & a && (i = c(i)), 8 & a || 4 & a && typeof i == \"object\" && i && i.__esModule) return i;\n        var p = /* @__PURE__ */Object.create(null);\n        if (c.r(p), Object.defineProperty(p, \"default\", {\n          enumerable: !0,\n          value: i\n        }), 2 & a && typeof i != \"string\") for (var l in i) c.d(p, l, function (n) {\n          return i[n];\n        }.bind(null, l));\n        return p;\n      }, c.n = function (i) {\n        var a = i && i.__esModule ? function () {\n          return i.default;\n        } : function () {\n          return i;\n        };\n        return c.d(a, \"a\", a), a;\n      }, c.o = function (i, a) {\n        return Object.prototype.hasOwnProperty.call(i, a);\n      }, c.p = \"\", c(c.s = 3);\n    }([function (d, v) {\n      var c;\n      c = function () {\n        return this;\n      }();\n      try {\n        c = c || new Function(\"return this\")();\n      } catch {\n        typeof window == \"object\" && (c = window);\n      }\n      d.exports = c;\n    }, function (d, v, c) {\n      (function (i) {\n        var a = c(2),\n          p = setTimeout;\n        function l() {}\n        function n(r) {\n          if (!(this instanceof n)) throw new TypeError(\"Promises must be constructed via new\");\n          if (typeof r != \"function\") throw new TypeError(\"not a function\");\n          this._state = 0, this._handled = !1, this._value = void 0, this._deferreds = [], y(r, this);\n        }\n        function o(r, u) {\n          for (; r._state === 3;) r = r._value;\n          r._state !== 0 ? (r._handled = !0, n._immediateFn(function () {\n            var f = r._state === 1 ? u.onFulfilled : u.onRejected;\n            if (f !== null) {\n              var m;\n              try {\n                m = f(r._value);\n              } catch (b) {\n                return void s(u.promise, b);\n              }\n              e(u.promise, m);\n            } else (r._state === 1 ? e : s)(u.promise, r._value);\n          })) : r._deferreds.push(u);\n        }\n        function e(r, u) {\n          try {\n            if (u === r) throw new TypeError(\"A promise cannot be resolved with itself.\");\n            if (u && (typeof u == \"object\" || typeof u == \"function\")) {\n              var f = u.then;\n              if (u instanceof n) return r._state = 3, r._value = u, void h(r);\n              if (typeof f == \"function\") return void y((m = f, b = u, function () {\n                m.apply(b, arguments);\n              }), r);\n            }\n            r._state = 1, r._value = u, h(r);\n          } catch (g) {\n            s(r, g);\n          }\n          var m, b;\n        }\n        function s(r, u) {\n          r._state = 2, r._value = u, h(r);\n        }\n        function h(r) {\n          r._state === 2 && r._deferreds.length === 0 && n._immediateFn(function () {\n            r._handled || n._unhandledRejectionFn(r._value);\n          });\n          for (var u = 0, f = r._deferreds.length; u < f; u++) o(r, r._deferreds[u]);\n          r._deferreds = null;\n        }\n        function t(r, u, f) {\n          this.onFulfilled = typeof r == \"function\" ? r : null, this.onRejected = typeof u == \"function\" ? u : null, this.promise = f;\n        }\n        function y(r, u) {\n          var f = !1;\n          try {\n            r(function (m) {\n              f || (f = !0, e(u, m));\n            }, function (m) {\n              f || (f = !0, s(u, m));\n            });\n          } catch (m) {\n            if (f) return;\n            f = !0, s(u, m);\n          }\n        }\n        n.prototype.catch = function (r) {\n          return this.then(null, r);\n        }, n.prototype.then = function (r, u) {\n          var f = new this.constructor(l);\n          return o(this, new t(r, u, f)), f;\n        }, n.prototype.finally = a.a, n.all = function (r) {\n          return new n(function (u, f) {\n            if (!r || r.length === void 0) throw new TypeError(\"Promise.all accepts an array\");\n            var m = Array.prototype.slice.call(r);\n            if (m.length === 0) return u([]);\n            var b = m.length;\n            function g(T, E) {\n              try {\n                if (E && (typeof E == \"object\" || typeof E == \"function\")) {\n                  var S = E.then;\n                  if (typeof S == \"function\") return void S.call(E, function (L) {\n                    g(T, L);\n                  }, f);\n                }\n                m[T] = E, --b == 0 && u(m);\n              } catch (L) {\n                f(L);\n              }\n            }\n            for (var _ = 0; _ < m.length; _++) g(_, m[_]);\n          });\n        }, n.resolve = function (r) {\n          return r && typeof r == \"object\" && r.constructor === n ? r : new n(function (u) {\n            u(r);\n          });\n        }, n.reject = function (r) {\n          return new n(function (u, f) {\n            f(r);\n          });\n        }, n.race = function (r) {\n          return new n(function (u, f) {\n            for (var m = 0, b = r.length; m < b; m++) r[m].then(u, f);\n          });\n        }, n._immediateFn = typeof i == \"function\" && function (r) {\n          i(r);\n        } || function (r) {\n          p(r, 0);\n        }, n._unhandledRejectionFn = function (r) {\n          typeof console < \"u\" && console && console.warn(\"Possible Unhandled Promise Rejection:\", r);\n        }, v.a = n;\n      }).call(this, c(5).setImmediate);\n    }, function (d, v, c) {\n      v.a = function (i) {\n        var a = this.constructor;\n        return this.then(function (p) {\n          return a.resolve(i()).then(function () {\n            return p;\n          });\n        }, function (p) {\n          return a.resolve(i()).then(function () {\n            return a.reject(p);\n          });\n        });\n      };\n    }, function (d, v, c) {\n      function i(t) {\n        return (i = typeof Symbol == \"function\" && typeof Symbol.iterator == \"symbol\" ? function (y) {\n          return typeof y;\n        } : function (y) {\n          return y && typeof Symbol == \"function\" && y.constructor === Symbol && y !== Symbol.prototype ? \"symbol\" : typeof y;\n        })(t);\n      }\n      c(4);\n      var a,\n        p,\n        l,\n        n,\n        o,\n        e,\n        s = c(8),\n        h = (p = function (t) {\n          return new Promise(function (y, r) {\n            t = n(t), t = o(t);\n            var u = window.XMLHttpRequest ? new window.XMLHttpRequest() : new window.ActiveXObject(\"Microsoft.XMLHTTP\");\n            u.open(t.method, t.url), u.setRequestHeader(\"X-Requested-With\", \"XMLHttpRequest\"), Object.keys(t.headers).forEach(function (m) {\n              var b = t.headers[m];\n              u.setRequestHeader(m, b);\n            });\n            var f = t.ratio;\n            u.upload.addEventListener(\"progress\", function (m) {\n              var b = Math.round(m.loaded / m.total * 100),\n                g = Math.ceil(b * f / 100);\n              t.progress(g);\n            }, !1), u.addEventListener(\"progress\", function (m) {\n              var b = Math.round(m.loaded / m.total * 100),\n                g = Math.ceil(b * (100 - f) / 100) + f;\n              t.progress(g);\n            }, !1), u.onreadystatechange = function () {\n              if (u.readyState === 4) {\n                var m = u.response;\n                try {\n                  m = JSON.parse(m);\n                } catch {}\n                var b = s.parseHeaders(u.getAllResponseHeaders()),\n                  g = {\n                    body: m,\n                    code: u.status,\n                    headers: b\n                  };\n                u.status === 200 ? y(g) : r(g);\n              }\n            }, u.send(t.data);\n          });\n        }, l = function (t) {\n          return t.method = \"POST\", p(t);\n        }, n = function () {\n          var t = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n          if (t.url && typeof t.url != \"string\") throw new Error(\"Url must be a string\");\n          if (t.url = t.url || \"\", t.method && typeof t.method != \"string\") throw new Error(\"`method` must be a string or null\");\n          if (t.method = t.method ? t.method.toUpperCase() : \"GET\", t.headers && i(t.headers) !== \"object\") throw new Error(\"`headers` must be an object or null\");\n          if (t.headers = t.headers || {}, t.type && (typeof t.type != \"string\" || !Object.values(a).includes(t.type))) throw new Error(\"`type` must be taken from module's «contentType» library\");\n          if (t.progress && typeof t.progress != \"function\") throw new Error(\"`progress` must be a function or null\");\n          if (t.progress = t.progress || function (y) {}, t.beforeSend = t.beforeSend || function (y) {}, t.ratio && typeof t.ratio != \"number\") throw new Error(\"`ratio` must be a number\");\n          if (t.ratio < 0 || t.ratio > 100) throw new Error(\"`ratio` must be in a 0-100 interval\");\n          if (t.ratio = t.ratio || 90, t.accept && typeof t.accept != \"string\") throw new Error(\"`accept` must be a string with a list of allowed mime-types\");\n          if (t.accept = t.accept || \"*/*\", t.multiple && typeof t.multiple != \"boolean\") throw new Error(\"`multiple` must be a true or false\");\n          if (t.multiple = t.multiple || !1, t.fieldName && typeof t.fieldName != \"string\") throw new Error(\"`fieldName` must be a string\");\n          return t.fieldName = t.fieldName || \"files\", t;\n        }, o = function (t) {\n          switch (t.method) {\n            case \"GET\":\n              var y = e(t.data, a.URLENCODED);\n              delete t.data, t.url = /\\?/.test(t.url) ? t.url + \"&\" + y : t.url + \"?\" + y;\n              break;\n            case \"POST\":\n            case \"PUT\":\n            case \"DELETE\":\n            case \"UPDATE\":\n              var r = function () {\n                return (arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {}).type || a.JSON;\n              }(t);\n              (s.isFormData(t.data) || s.isFormElement(t.data)) && (r = a.FORM), t.data = e(t.data, r), r !== h.contentType.FORM && (t.headers[\"content-type\"] = r);\n          }\n          return t;\n        }, e = function () {\n          var t = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n          switch (arguments.length > 1 ? arguments[1] : void 0) {\n            case a.URLENCODED:\n              return s.urlEncode(t);\n            case a.JSON:\n              return s.jsonEncode(t);\n            case a.FORM:\n              return s.formEncode(t);\n            default:\n              return t;\n          }\n        }, {\n          contentType: a = {\n            URLENCODED: \"application/x-www-form-urlencoded; charset=utf-8\",\n            FORM: \"multipart/form-data\",\n            JSON: \"application/json; charset=utf-8\"\n          },\n          request: p,\n          get: function (t) {\n            return t.method = \"GET\", p(t);\n          },\n          post: l,\n          transport: function (t) {\n            return t = n(t), s.selectFiles(t).then(function (y) {\n              for (var r = new FormData(), u = 0; u < y.length; u++) r.append(t.fieldName, y[u], y[u].name);\n              return s.isObject(t.data) && Object.keys(t.data).forEach(function (f) {\n                var m = t.data[f];\n                r.append(f, m);\n              }), t.beforeSend && t.beforeSend(y), t.data = r, l(t);\n            });\n          },\n          selectFiles: function (t) {\n            return delete (t = n(t)).beforeSend, s.selectFiles(t);\n          }\n        });\n      d.exports = h;\n    }, function (d, v, c) {\n      c.r(v);\n      var i = c(1);\n      window.Promise = window.Promise || i.a;\n    }, function (d, v, c) {\n      (function (i) {\n        var a = i !== void 0 && i || typeof self < \"u\" && self || window,\n          p = Function.prototype.apply;\n        function l(n, o) {\n          this._id = n, this._clearFn = o;\n        }\n        v.setTimeout = function () {\n          return new l(p.call(setTimeout, a, arguments), clearTimeout);\n        }, v.setInterval = function () {\n          return new l(p.call(setInterval, a, arguments), clearInterval);\n        }, v.clearTimeout = v.clearInterval = function (n) {\n          n && n.close();\n        }, l.prototype.unref = l.prototype.ref = function () {}, l.prototype.close = function () {\n          this._clearFn.call(a, this._id);\n        }, v.enroll = function (n, o) {\n          clearTimeout(n._idleTimeoutId), n._idleTimeout = o;\n        }, v.unenroll = function (n) {\n          clearTimeout(n._idleTimeoutId), n._idleTimeout = -1;\n        }, v._unrefActive = v.active = function (n) {\n          clearTimeout(n._idleTimeoutId);\n          var o = n._idleTimeout;\n          o >= 0 && (n._idleTimeoutId = setTimeout(function () {\n            n._onTimeout && n._onTimeout();\n          }, o));\n        }, c(6), v.setImmediate = typeof self < \"u\" && self.setImmediate || i !== void 0 && i.setImmediate || this && this.setImmediate, v.clearImmediate = typeof self < \"u\" && self.clearImmediate || i !== void 0 && i.clearImmediate || this && this.clearImmediate;\n      }).call(this, c(0));\n    }, function (d, v, c) {\n      (function (i, a) {\n        (function (p, l) {\n          if (!p.setImmediate) {\n            var n,\n              o,\n              e,\n              s,\n              h,\n              t = 1,\n              y = {},\n              r = !1,\n              u = p.document,\n              f = Object.getPrototypeOf && Object.getPrototypeOf(p);\n            f = f && f.setTimeout ? f : p, {}.toString.call(p.process) === \"[object process]\" ? n = function (g) {\n              a.nextTick(function () {\n                b(g);\n              });\n            } : function () {\n              if (p.postMessage && !p.importScripts) {\n                var g = !0,\n                  _ = p.onmessage;\n                return p.onmessage = function () {\n                  g = !1;\n                }, p.postMessage(\"\", \"*\"), p.onmessage = _, g;\n              }\n            }() ? (s = \"setImmediate$\" + Math.random() + \"$\", h = function (g) {\n              g.source === p && typeof g.data == \"string\" && g.data.indexOf(s) === 0 && b(+g.data.slice(s.length));\n            }, p.addEventListener ? p.addEventListener(\"message\", h, !1) : p.attachEvent(\"onmessage\", h), n = function (g) {\n              p.postMessage(s + g, \"*\");\n            }) : p.MessageChannel ? ((e = new MessageChannel()).port1.onmessage = function (g) {\n              b(g.data);\n            }, n = function (g) {\n              e.port2.postMessage(g);\n            }) : u && \"onreadystatechange\" in u.createElement(\"script\") ? (o = u.documentElement, n = function (g) {\n              var _ = u.createElement(\"script\");\n              _.onreadystatechange = function () {\n                b(g), _.onreadystatechange = null, o.removeChild(_), _ = null;\n              }, o.appendChild(_);\n            }) : n = function (g) {\n              setTimeout(b, 0, g);\n            }, f.setImmediate = function (g) {\n              typeof g != \"function\" && (g = new Function(\"\" + g));\n              for (var _ = new Array(arguments.length - 1), T = 0; T < _.length; T++) _[T] = arguments[T + 1];\n              var E = {\n                callback: g,\n                args: _\n              };\n              return y[t] = E, n(t), t++;\n            }, f.clearImmediate = m;\n          }\n          function m(g) {\n            delete y[g];\n          }\n          function b(g) {\n            if (r) setTimeout(b, 0, g);else {\n              var _ = y[g];\n              if (_) {\n                r = !0;\n                try {\n                  (function (T) {\n                    var E = T.callback,\n                      S = T.args;\n                    switch (S.length) {\n                      case 0:\n                        E();\n                        break;\n                      case 1:\n                        E(S[0]);\n                        break;\n                      case 2:\n                        E(S[0], S[1]);\n                        break;\n                      case 3:\n                        E(S[0], S[1], S[2]);\n                        break;\n                      default:\n                        E.apply(l, S);\n                    }\n                  })(_);\n                } finally {\n                  m(g), r = !1;\n                }\n              }\n            }\n          }\n        })(typeof self > \"u\" ? i === void 0 ? this : i : self);\n      }).call(this, c(0), c(7));\n    }, function (d, v) {\n      var c,\n        i,\n        a = d.exports = {};\n      function p() {\n        throw new Error(\"setTimeout has not been defined\");\n      }\n      function l() {\n        throw new Error(\"clearTimeout has not been defined\");\n      }\n      function n(f) {\n        if (c === setTimeout) return setTimeout(f, 0);\n        if ((c === p || !c) && setTimeout) return c = setTimeout, setTimeout(f, 0);\n        try {\n          return c(f, 0);\n        } catch {\n          try {\n            return c.call(null, f, 0);\n          } catch {\n            return c.call(this, f, 0);\n          }\n        }\n      }\n      (function () {\n        try {\n          c = typeof setTimeout == \"function\" ? setTimeout : p;\n        } catch {\n          c = p;\n        }\n        try {\n          i = typeof clearTimeout == \"function\" ? clearTimeout : l;\n        } catch {\n          i = l;\n        }\n      })();\n      var o,\n        e = [],\n        s = !1,\n        h = -1;\n      function t() {\n        s && o && (s = !1, o.length ? e = o.concat(e) : h = -1, e.length && y());\n      }\n      function y() {\n        if (!s) {\n          var f = n(t);\n          s = !0;\n          for (var m = e.length; m;) {\n            for (o = e, e = []; ++h < m;) o && o[h].run();\n            h = -1, m = e.length;\n          }\n          o = null, s = !1, function (b) {\n            if (i === clearTimeout) return clearTimeout(b);\n            if ((i === l || !i) && clearTimeout) return i = clearTimeout, clearTimeout(b);\n            try {\n              i(b);\n            } catch {\n              try {\n                return i.call(null, b);\n              } catch {\n                return i.call(this, b);\n              }\n            }\n          }(f);\n        }\n      }\n      function r(f, m) {\n        this.fun = f, this.array = m;\n      }\n      function u() {}\n      a.nextTick = function (f) {\n        var m = new Array(arguments.length - 1);\n        if (arguments.length > 1) for (var b = 1; b < arguments.length; b++) m[b - 1] = arguments[b];\n        e.push(new r(f, m)), e.length !== 1 || s || n(y);\n      }, r.prototype.run = function () {\n        this.fun.apply(null, this.array);\n      }, a.title = \"browser\", a.browser = !0, a.env = {}, a.argv = [], a.version = \"\", a.versions = {}, a.on = u, a.addListener = u, a.once = u, a.off = u, a.removeListener = u, a.removeAllListeners = u, a.emit = u, a.prependListener = u, a.prependOnceListener = u, a.listeners = function (f) {\n        return [];\n      }, a.binding = function (f) {\n        throw new Error(\"process.binding is not supported\");\n      }, a.cwd = function () {\n        return \"/\";\n      }, a.chdir = function (f) {\n        throw new Error(\"process.chdir is not supported\");\n      }, a.umask = function () {\n        return 0;\n      };\n    }, function (d, v, c) {\n      function i(p, l) {\n        for (var n = 0; n < l.length; n++) {\n          var o = l[n];\n          o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(p, o.key, o);\n        }\n      }\n      var a = c(9);\n      d.exports = function () {\n        function p() {\n          (function (e, s) {\n            if (!(e instanceof s)) throw new TypeError(\"Cannot call a class as a function\");\n          })(this, p);\n        }\n        var l, n, o;\n        return l = p, o = [{\n          key: \"urlEncode\",\n          value: function (e) {\n            return a(e);\n          }\n        }, {\n          key: \"jsonEncode\",\n          value: function (e) {\n            return JSON.stringify(e);\n          }\n        }, {\n          key: \"formEncode\",\n          value: function (e) {\n            if (this.isFormData(e)) return e;\n            if (this.isFormElement(e)) return new FormData(e);\n            if (this.isObject(e)) {\n              var s = new FormData();\n              return Object.keys(e).forEach(function (h) {\n                var t = e[h];\n                s.append(h, t);\n              }), s;\n            }\n            throw new Error(\"`data` must be an instance of Object, FormData or <FORM> HTMLElement\");\n          }\n        }, {\n          key: \"isObject\",\n          value: function (e) {\n            return Object.prototype.toString.call(e) === \"[object Object]\";\n          }\n        }, {\n          key: \"isFormData\",\n          value: function (e) {\n            return e instanceof FormData;\n          }\n        }, {\n          key: \"isFormElement\",\n          value: function (e) {\n            return e instanceof HTMLFormElement;\n          }\n        }, {\n          key: \"selectFiles\",\n          value: function () {\n            var e = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n            return new Promise(function (s, h) {\n              var t = document.createElement(\"INPUT\");\n              t.type = \"file\", e.multiple && t.setAttribute(\"multiple\", \"multiple\"), e.accept && t.setAttribute(\"accept\", e.accept), t.style.display = \"none\", document.body.appendChild(t), t.addEventListener(\"change\", function (y) {\n                var r = y.target.files;\n                s(r), document.body.removeChild(t);\n              }, !1), t.click();\n            });\n          }\n        }, {\n          key: \"parseHeaders\",\n          value: function (e) {\n            var s = e.trim().split(/[\\r\\n]+/),\n              h = {};\n            return s.forEach(function (t) {\n              var y = t.split(\": \"),\n                r = y.shift(),\n                u = y.join(\": \");\n              r && (h[r] = u);\n            }), h;\n          }\n        }], (n = null) && i(l.prototype, n), o && i(l, o), p;\n      }();\n    }, function (d, v) {\n      var c = function (a) {\n          return encodeURIComponent(a).replace(/[!'()*]/g, escape).replace(/%20/g, \"+\");\n        },\n        i = function (a, p, l, n) {\n          return p = p || null, l = l || \"&\", n = n || null, a ? function (o) {\n            for (var e = new Array(), s = 0; s < o.length; s++) o[s] && e.push(o[s]);\n            return e;\n          }(Object.keys(a).map(function (o) {\n            var e,\n              s,\n              h = o;\n            if (n && (h = n + \"[\" + h + \"]\"), typeof a[o] == \"object\" && a[o] !== null) e = i(a[o], null, l, h);else {\n              p && (s = h, h = !isNaN(parseFloat(s)) && isFinite(s) ? p + Number(h) : h);\n              var t = a[o];\n              t = (t = (t = (t = t === !0 ? \"1\" : t) === !1 ? \"0\" : t) === 0 ? \"0\" : t) || \"\", e = c(h) + \"=\" + c(t);\n            }\n            return e;\n          })).join(l).replace(/[!'()*]/g, \"\") : \"\";\n        };\n      d.exports = i;\n    }]);\n  });\n})(j);\nvar P = j.exports;\nconst R = /* @__PURE__ */O(P),\n  F = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M7.69998 12.6L7.67896 12.62C6.53993 13.7048 6.52012 15.5155 7.63516 16.625V16.625C8.72293 17.7073 10.4799 17.7102 11.5712 16.6314L13.0263 15.193C14.0703 14.1609 14.2141 12.525 13.3662 11.3266L13.22 11.12\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M16.22 11.12L16.3564 10.9805C17.2895 10.0265 17.3478 8.5207 16.4914 7.49733V7.49733C15.569 6.39509 13.9269 6.25143 12.8271 7.17675L11.39 8.38588C10.0935 9.47674 9.95704 11.4241 11.0887 12.6852L11.12 12.72\"/></svg>';\nclass I {\n  /**\n   * Notify core that read-only mode supported\n   *\n   * @returns {boolean}\n   */\n  static get isReadOnlySupported() {\n    return !0;\n  }\n  /**\n   * Get Tool toolbox settings\n   * icon - Tool icon's SVG\n   * title - title to show in toolbox\n   *\n   * @returns {{icon: string, title: string}}\n   */\n  static get toolbox() {\n    return {\n      icon: F,\n      title: \"Link\"\n    };\n  }\n  /**\n   * Allow to press Enter inside the LinkTool input\n   *\n   * @returns {boolean}\n   * @public\n   */\n  static get enableLineBreaks() {\n    return !0;\n  }\n  /**\n   * @param {object} options - Tool constructor options fot from Editor.js\n   * @param {LinkToolData} options.data - previously saved data\n   * @param {LinkToolConfig} options.config - user config for Tool\n   * @param {object} options.api - Editor.js API\n   * @param {boolean} options.readOnly - read-only mode flag\n   */\n  constructor({\n    data: w,\n    config: d,\n    api: v,\n    readOnly: c\n  }) {\n    this.api = v, this.readOnly = c, this.config = {\n      endpoint: d.endpoint || \"\",\n      headers: d.headers || {}\n    }, this.nodes = {\n      wrapper: null,\n      container: null,\n      progress: null,\n      input: null,\n      inputHolder: null,\n      linkContent: null,\n      linkImage: null,\n      linkTitle: null,\n      linkDescription: null,\n      linkText: null\n    }, this._data = {\n      link: \"\",\n      meta: {}\n    }, this.data = w;\n  }\n  /**\n   * Renders Block content\n   *\n   * @public\n   *\n   * @returns {HTMLDivElement}\n   */\n  render() {\n    return this.nodes.wrapper = this.make(\"div\", this.CSS.baseClass), this.nodes.container = this.make(\"div\", this.CSS.container), this.nodes.inputHolder = this.makeInputHolder(), this.nodes.linkContent = this.prepareLinkPreview(), Object.keys(this.data.meta).length ? (this.nodes.container.appendChild(this.nodes.linkContent), this.showLinkPreview(this.data.meta)) : this.nodes.container.appendChild(this.nodes.inputHolder), this.nodes.wrapper.appendChild(this.nodes.container), this.nodes.wrapper;\n  }\n  /**\n   * Return Block data\n   *\n   * @public\n   *\n   * @returns {LinkToolData}\n   */\n  save() {\n    return this.data;\n  }\n  /**\n   * Validate Block data\n   * - check if given link is an empty string or not.\n   *\n   * @public\n   *\n   * @returns {boolean} false if saved data is incorrect, otherwise true\n   */\n  validate() {\n    return this.data.link.trim() !== \"\";\n  }\n  /**\n   * Stores all Tool's data\n   *\n   * @param {LinkToolData} data - data to store\n   */\n  set data(w) {\n    this._data = Object.assign({}, {\n      link: w.link || this._data.link,\n      meta: w.meta || this._data.meta\n    });\n  }\n  /**\n   * Return Tool data\n   *\n   * @returns {LinkToolData}\n   */\n  get data() {\n    return this._data;\n  }\n  /**\n   * @returns {object} - Link Tool styles\n   */\n  get CSS() {\n    return {\n      baseClass: this.api.styles.block,\n      input: this.api.styles.input,\n      /**\n       * Tool's classes\n       */\n      container: \"link-tool\",\n      inputEl: \"link-tool__input\",\n      inputHolder: \"link-tool__input-holder\",\n      inputError: \"link-tool__input-holder--error\",\n      linkContent: \"link-tool__content\",\n      linkContentRendered: \"link-tool__content--rendered\",\n      linkImage: \"link-tool__image\",\n      linkTitle: \"link-tool__title\",\n      linkDescription: \"link-tool__description\",\n      linkText: \"link-tool__anchor\",\n      progress: \"link-tool__progress\",\n      progressLoading: \"link-tool__progress--loading\",\n      progressLoaded: \"link-tool__progress--loaded\"\n    };\n  }\n  /**\n   * Prepare input holder\n   *\n   * @returns {HTMLElement}\n   */\n  makeInputHolder() {\n    const w = this.make(\"div\", this.CSS.inputHolder);\n    return this.nodes.progress = this.make(\"label\", this.CSS.progress), this.nodes.input = this.make(\"div\", [this.CSS.input, this.CSS.inputEl], {\n      contentEditable: !this.readOnly\n    }), this.nodes.input.dataset.placeholder = this.api.i18n.t(\"Link\"), this.readOnly || (this.nodes.input.addEventListener(\"paste\", d => {\n      this.startFetching(d);\n    }), this.nodes.input.addEventListener(\"keydown\", d => {\n      const [v, c] = [13, 65],\n        i = d.ctrlKey || d.metaKey;\n      switch (d.keyCode) {\n        case v:\n          d.preventDefault(), d.stopPropagation(), this.startFetching(d);\n          break;\n        case c:\n          i && this.selectLinkUrl(d);\n          break;\n      }\n    })), w.appendChild(this.nodes.progress), w.appendChild(this.nodes.input), w;\n  }\n  /**\n   * Activates link data fetching by url\n   *\n   * @param {PasteEvent|KeyboardEvent} event - fetching could be fired by a pase or keydown events\n   */\n  startFetching(w) {\n    let d = this.nodes.input.textContent;\n    w.type === \"paste\" && (d = (w.clipboardData || window.clipboardData).getData(\"text\")), this.removeErrorStyle(), this.fetchLinkData(d);\n  }\n  /**\n   * If previous link data fetching failed, remove error styles\n   */\n  removeErrorStyle() {\n    this.nodes.inputHolder.classList.remove(this.CSS.inputError), this.nodes.inputHolder.insertBefore(this.nodes.progress, this.nodes.input);\n  }\n  /**\n   * Select LinkTool input content by CMD+A\n   *\n   * @param {KeyboardEvent} event - keydown\n   */\n  selectLinkUrl(w) {\n    w.preventDefault(), w.stopPropagation();\n    const d = window.getSelection(),\n      v = new Range(),\n      a = d.anchorNode.parentNode.closest(`.${this.CSS.inputHolder}`).querySelector(`.${this.CSS.inputEl}`);\n    v.selectNodeContents(a), d.removeAllRanges(), d.addRange(v);\n  }\n  /**\n   * Prepare link preview holder\n   *\n   * @returns {HTMLElement}\n   */\n  prepareLinkPreview() {\n    const w = this.make(\"a\", this.CSS.linkContent, {\n      target: \"_blank\",\n      rel: \"nofollow noindex noreferrer\"\n    });\n    return this.nodes.linkImage = this.make(\"div\", this.CSS.linkImage), this.nodes.linkTitle = this.make(\"div\", this.CSS.linkTitle), this.nodes.linkDescription = this.make(\"p\", this.CSS.linkDescription), this.nodes.linkText = this.make(\"span\", this.CSS.linkText), w;\n  }\n  /**\n   * Compose link preview from fetched data\n   *\n   * @param {metaData} meta - link meta data\n   */\n  showLinkPreview({\n    image: w,\n    title: d,\n    description: v\n  }) {\n    this.nodes.container.appendChild(this.nodes.linkContent), w && w.url && (this.nodes.linkImage.style.backgroundImage = \"url(\" + w.url + \")\", this.nodes.linkContent.appendChild(this.nodes.linkImage)), d && (this.nodes.linkTitle.textContent = d, this.nodes.linkContent.appendChild(this.nodes.linkTitle)), v && (this.nodes.linkDescription.textContent = v, this.nodes.linkContent.appendChild(this.nodes.linkDescription)), this.nodes.linkContent.classList.add(this.CSS.linkContentRendered), this.nodes.linkContent.setAttribute(\"href\", this.data.link), this.nodes.linkContent.appendChild(this.nodes.linkText);\n    try {\n      this.nodes.linkText.textContent = new URL(this.data.link).hostname;\n    } catch {\n      this.nodes.linkText.textContent = this.data.link;\n    }\n  }\n  /**\n   * Show loading progress bar\n   */\n  showProgress() {\n    this.nodes.progress.classList.add(this.CSS.progressLoading);\n  }\n  /**\n   * Hide loading progress bar\n   *\n   * @returns {Promise<void>}\n   */\n  hideProgress() {\n    return new Promise(w => {\n      this.nodes.progress.classList.remove(this.CSS.progressLoading), this.nodes.progress.classList.add(this.CSS.progressLoaded), setTimeout(w, 500);\n    });\n  }\n  /**\n   * If data fetching failed, set input error style\n   */\n  applyErrorStyle() {\n    this.nodes.inputHolder.classList.add(this.CSS.inputError), this.nodes.progress.remove();\n  }\n  /**\n   * Sends to backend pasted url and receives link data\n   *\n   * @param {string} url - link source url\n   */\n  fetchLinkData(w) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.showProgress(), _this.data = {\n        link: w\n      };\n      try {\n        const {\n          body: d\n        } = yield R.get({\n          url: _this.config.endpoint,\n          headers: _this.config.headers,\n          data: {\n            url: w\n          }\n        });\n        _this.onFetch(d);\n      } catch {\n        _this.fetchingFailed(_this.api.i18n.t(\"Couldn't fetch the link data\"));\n      }\n    })();\n  }\n  /**\n   * Link data fetching callback\n   *\n   * @param {UploadResponseFormat} response - backend response\n   */\n  onFetch(w) {\n    if (!w || !w.success) {\n      this.fetchingFailed(this.api.i18n.t(\"Couldn't get this link data, try the other one\"));\n      return;\n    }\n    const d = w.meta,\n      v = w.link || this.data.link;\n    if (this.data = {\n      meta: d,\n      link: v\n    }, !d) {\n      this.fetchingFailed(this.api.i18n.t(\"Wrong response format from the server\"));\n      return;\n    }\n    this.hideProgress().then(() => {\n      this.nodes.inputHolder.remove(), this.showLinkPreview(d);\n    });\n  }\n  /**\n   * Handle link fetching errors\n   *\n   * @private\n   *\n   * @param {string} errorMessage - message to explain user what he should do\n   */\n  fetchingFailed(w) {\n    this.api.notifier.show({\n      message: w,\n      style: \"error\"\n    }), this.applyErrorStyle();\n  }\n  /**\n   * Helper method for elements creation\n   *\n   * @param {string} tagName - name of creating element\n   * @param {string|string[]} [classNames] - list of CSS classes to add\n   * @param {object} [attributes] - object with attributes to add\n   * @returns {HTMLElement}\n   */\n  make(w, d = null, v = {}) {\n    const c = document.createElement(w);\n    Array.isArray(d) ? c.classList.add(...d) : d && c.classList.add(d);\n    for (const i in v) c[i] = v[i];\n    return c;\n  }\n}\nexport { I as default };", "map": {"version": 3, "names": ["document", "o", "createElement", "append<PERSON><PERSON><PERSON>", "createTextNode", "head", "t", "console", "error", "C", "globalThis", "window", "global", "self", "O", "k", "__esModule", "Object", "prototype", "hasOwnProperty", "call", "default", "w", "Symbol", "iterator", "d", "v", "n", "next", "e", "shift", "done", "value", "c", "encodeURIComponent", "replace", "i", "decodeURIComponent", "String", "a", "defineProperty", "writable", "s", "_fromString", "h", "for<PERSON>ach", "u", "f", "append", "toString", "length", "y", "TypeError", "r", "_entries", "push", "delete", "get", "getAll", "slice", "has", "set", "keys", "values", "entries", "join", "URLSearchParams", "p", "l", "sort", "enumerable", "configurable", "split", "URL", "pathname", "href", "searchParams", "location", "implementation", "createHTMLDocument", "indexOf", "Error", "m", "body", "type", "protocol", "test", "checkValidity", "search", "b", "apply", "arguments", "_anchorElement", "_updateSearchParams", "defineProperties", "origin", "port", "hostname", "password", "username", "createObjectURL", "revokeObjectURL", "setInterval", "j", "exports", "toStringTag", "create", "bind", "Function", "setTimeout", "_state", "_handled", "_value", "_deferreds", "_immediateFn", "onFulfilled", "onRejected", "promise", "then", "g", "_unhandledRejectionFn", "catch", "constructor", "finally", "all", "Array", "T", "E", "S", "L", "_", "resolve", "reject", "race", "warn", "setImmediate", "Promise", "XMLHttpRequest", "ActiveXObject", "open", "method", "url", "setRequestHeader", "headers", "ratio", "upload", "addEventListener", "Math", "round", "loaded", "total", "ceil", "progress", "onreadystatechange", "readyState", "response", "JSON", "parse", "parseHeaders", "getAllResponseHeaders", "code", "status", "send", "data", "toUpperCase", "includes", "beforeSend", "accept", "multiple", "fieldName", "URLENCODED", "isFormData", "isFormElement", "FORM", "contentType", "urlEncode", "jsonEncode", "formEncode", "request", "post", "transport", "selectFiles", "FormData", "name", "isObject", "_id", "_clearFn", "clearTimeout", "clearInterval", "close", "unref", "ref", "enroll", "_idleTimeoutId", "_idleTimeout", "unenroll", "_unrefActive", "active", "_onTimeout", "clearImmediate", "getPrototypeOf", "process", "nextTick", "postMessage", "importScripts", "onmessage", "random", "source", "attachEvent", "MessageChannel", "port1", "port2", "documentElement", "<PERSON><PERSON><PERSON><PERSON>", "callback", "args", "concat", "run", "fun", "array", "title", "browser", "env", "argv", "version", "versions", "on", "addListener", "once", "off", "removeListener", "removeAllListeners", "emit", "prependListener", "prependOnceListener", "listeners", "binding", "cwd", "chdir", "umask", "key", "stringify", "HTMLFormElement", "setAttribute", "style", "display", "target", "files", "click", "trim", "escape", "map", "isNaN", "parseFloat", "isFinite", "Number", "P", "R", "F", "I", "isReadOnlySupported", "toolbox", "icon", "enableLineBreaks", "config", "api", "readOnly", "endpoint", "nodes", "wrapper", "container", "input", "inputHolder", "linkContent", "linkImage", "linkTitle", "linkDescription", "linkText", "_data", "link", "meta", "render", "make", "CSS", "baseClass", "makeInputHolder", "prepareLinkPreview", "showLinkPreview", "save", "validate", "assign", "styles", "block", "inputEl", "inputError", "link<PERSON><PERSON><PERSON><PERSON><PERSON>ed", "progressLoading", "progressLoaded", "contentEditable", "dataset", "placeholder", "i18n", "startFetching", "ctrl<PERSON>ey", "metaKey", "keyCode", "preventDefault", "stopPropagation", "selectLinkUrl", "textContent", "clipboardData", "getData", "removeErrorStyle", "fetchLinkData", "classList", "remove", "insertBefore", "getSelection", "Range", "anchorNode", "parentNode", "closest", "querySelector", "selectNodeContents", "removeAllRanges", "addRange", "rel", "image", "description", "backgroundImage", "add", "showProgress", "hideProgress", "applyErrorStyle", "_this", "_asyncToGenerator", "onFetch", "fetchingFailed", "success", "notifier", "show", "message", "isArray"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/@editorjs/link/dist/link.mjs"], "sourcesContent": ["(function(){\"use strict\";try{if(typeof document<\"u\"){var o=document.createElement(\"style\");o.appendChild(document.createTextNode(`.link-tool{position:relative}.link-tool__input{padding-left:38px;background-image:url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' fill='none'%3E%3Cpath stroke='%23707684' stroke-linecap='round' stroke-width='2' d='m7.7 12.6-.021.02a2.795 2.795 0 0 0-.044 4.005v0a2.795 2.795 0 0 0 3.936.006l1.455-1.438a3 3 0 0 0 .34-3.866l-.146-.207'/%3E%3Cpath stroke='%23707684' stroke-linecap='round' stroke-width='2' d='m16.22 11.12.136-.14c.933-.954.992-2.46.135-3.483v0a2.597 2.597 0 0 0-3.664-.32L11.39 8.386a3 3 0 0 0-.301 4.3l.031.034'/%3E%3C/svg%3E\");background-repeat:no-repeat;background-position:10px;white-space:nowrap;text-overflow:ellipsis;overflow:hidden}.link-tool__input-holder{position:relative}.link-tool__input-holder--error .link-tool__input{background-image:url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' fill='none'%3E%3Cpath stroke='rgb(224, 147, 147)' stroke-linecap='round' stroke-width='2' d='m7.7 12.6-.021.02a2.795 2.795 0 0 0-.044 4.005v0a2.795 2.795 0 0 0 3.936.006l1.455-1.438a3 3 0 0 0 .34-3.866l-.146-.207'/%3E%3Cpath stroke='rgb(224, 147, 147)' stroke-linecap='round' stroke-width='2' d='m16.22 11.12.136-.14c.933-.954.992-2.46.135-3.483v0a2.597 2.597 0 0 0-3.664-.32L11.39 8.386a3 3 0 0 0-.301 4.3l.031.034'/%3E%3C/svg%3E\");background-color:#fff3f6;border-color:#f3e0e0;color:#a95a5a;box-shadow:inset 0 1px 3px #923e3e0d}.link-tool__input[contentEditable=true][data-placeholder]:before{position:absolute;content:attr(data-placeholder);color:#707684;font-weight:400;opacity:0}.link-tool__input[contentEditable=true][data-placeholder]:empty:before{opacity:1}.link-tool__input[contentEditable=true][data-placeholder]:empty:focus:before{opacity:0}.link-tool__progress{position:absolute;box-shadow:inset 0 1px 3px #66556b0a;height:100%;width:0;background-color:#f4f5f7;z-index:-1}.link-tool__progress--loading{-webkit-animation:progress .5s ease-in;-webkit-animation-fill-mode:forwards}.link-tool__progress--loaded{width:100%}.link-tool__content{display:block;padding:25px;border-radius:2px;box-shadow:0 0 0 2px #fff;color:initial!important;text-decoration:none!important}.link-tool__content:after{content:\"\";clear:both;display:table}.link-tool__content--rendered{background:#fff;border:1px solid rgba(201,201,204,.48);box-shadow:0 1px 3px #0000001a;border-radius:6px;will-change:filter;animation:link-in .45s 1 cubic-bezier(.215,.61,.355,1)}.link-tool__content--rendered:hover{box-shadow:0 0 3px #00000029}.link-tool__image{background-position:center center;background-repeat:no-repeat;background-size:cover;margin:0 0 0 30px;width:65px;height:65px;border-radius:3px;float:right}.link-tool__title{font-size:17px;font-weight:600;line-height:1.5em;margin:0 0 10px}.link-tool__title+.link-tool__anchor{margin-top:25px}.link-tool__description{margin:0 0 20px;font-size:15px;line-height:1.55em;display:-webkit-box;-webkit-line-clamp:3;-webkit-box-orient:vertical;overflow:hidden}.link-tool__anchor{display:block;font-size:15px;line-height:1em;color:#888!important;border:0!important;padding:0!important}@keyframes link-in{0%{filter:blur(5px)}to{filter:none}}.codex-editor--narrow .link-tool__image{display:none}@-webkit-keyframes progress{0%{width:0}to{width:85%}}`)),document.head.appendChild(o)}}catch(t){console.error(\"vite-plugin-css-injected-by-js\",t)}})();\nvar C = typeof globalThis < \"u\" ? globalThis : typeof window < \"u\" ? window : typeof global < \"u\" ? global : typeof self < \"u\" ? self : {};\nfunction O(k) {\n  return k && k.__esModule && Object.prototype.hasOwnProperty.call(k, \"default\") ? k.default : k;\n}\n(function(k) {\n  var w = function() {\n    try {\n      return !!Symbol.iterator;\n    } catch {\n      return !1;\n    }\n  }, d = w(), v = function(n) {\n    var o = {\n      next: function() {\n        var e = n.shift();\n        return { done: e === void 0, value: e };\n      }\n    };\n    return d && (o[Symbol.iterator] = function() {\n      return o;\n    }), o;\n  }, c = function(n) {\n    return encodeURIComponent(n).replace(/%20/g, \"+\");\n  }, i = function(n) {\n    return decodeURIComponent(String(n).replace(/\\+/g, \" \"));\n  }, a = function() {\n    var n = function(e) {\n      Object.defineProperty(this, \"_entries\", { writable: !0, value: {} });\n      var s = typeof e;\n      if (s !== \"undefined\")\n        if (s === \"string\")\n          e !== \"\" && this._fromString(e);\n        else if (e instanceof n) {\n          var h = this;\n          e.forEach(function(u, f) {\n            h.append(f, u);\n          });\n        } else if (e !== null && s === \"object\")\n          if (Object.prototype.toString.call(e) === \"[object Array]\")\n            for (var t = 0; t < e.length; t++) {\n              var y = e[t];\n              if (Object.prototype.toString.call(y) === \"[object Array]\" || y.length !== 2)\n                this.append(y[0], y[1]);\n              else\n                throw new TypeError(\"Expected [string, any] as entry at index \" + t + \" of URLSearchParams's input\");\n            }\n          else\n            for (var r in e)\n              e.hasOwnProperty(r) && this.append(r, e[r]);\n        else\n          throw new TypeError(\"Unsupported input's type for URLSearchParams\");\n    }, o = n.prototype;\n    o.append = function(e, s) {\n      e in this._entries ? this._entries[e].push(String(s)) : this._entries[e] = [String(s)];\n    }, o.delete = function(e) {\n      delete this._entries[e];\n    }, o.get = function(e) {\n      return e in this._entries ? this._entries[e][0] : null;\n    }, o.getAll = function(e) {\n      return e in this._entries ? this._entries[e].slice(0) : [];\n    }, o.has = function(e) {\n      return e in this._entries;\n    }, o.set = function(e, s) {\n      this._entries[e] = [String(s)];\n    }, o.forEach = function(e, s) {\n      var h;\n      for (var t in this._entries)\n        if (this._entries.hasOwnProperty(t)) {\n          h = this._entries[t];\n          for (var y = 0; y < h.length; y++)\n            e.call(s, h[y], t, this);\n        }\n    }, o.keys = function() {\n      var e = [];\n      return this.forEach(function(s, h) {\n        e.push(h);\n      }), v(e);\n    }, o.values = function() {\n      var e = [];\n      return this.forEach(function(s) {\n        e.push(s);\n      }), v(e);\n    }, o.entries = function() {\n      var e = [];\n      return this.forEach(function(s, h) {\n        e.push([h, s]);\n      }), v(e);\n    }, d && (o[Symbol.iterator] = o.entries), o.toString = function() {\n      var e = [];\n      return this.forEach(function(s, h) {\n        e.push(c(h) + \"=\" + c(s));\n      }), e.join(\"&\");\n    }, k.URLSearchParams = n;\n  }, p = function() {\n    try {\n      var n = k.URLSearchParams;\n      return new n(\"?a=1\").toString() === \"a=1\" && typeof n.prototype.set == \"function\";\n    } catch {\n      return !1;\n    }\n  };\n  p() || a();\n  var l = k.URLSearchParams.prototype;\n  typeof l.sort != \"function\" && (l.sort = function() {\n    var n = this, o = [];\n    this.forEach(function(s, h) {\n      o.push([h, s]), n._entries || n.delete(h);\n    }), o.sort(function(s, h) {\n      return s[0] < h[0] ? -1 : s[0] > h[0] ? 1 : 0;\n    }), n._entries && (n._entries = {});\n    for (var e = 0; e < o.length; e++)\n      this.append(o[e][0], o[e][1]);\n  }), typeof l._fromString != \"function\" && Object.defineProperty(l, \"_fromString\", {\n    enumerable: !1,\n    configurable: !1,\n    writable: !1,\n    value: function(n) {\n      if (this._entries)\n        this._entries = {};\n      else {\n        var o = [];\n        this.forEach(function(t, y) {\n          o.push(y);\n        });\n        for (var e = 0; e < o.length; e++)\n          this.delete(o[e]);\n      }\n      n = n.replace(/^\\?/, \"\");\n      for (var s = n.split(\"&\"), h, e = 0; e < s.length; e++)\n        h = s[e].split(\"=\"), this.append(\n          i(h[0]),\n          h.length > 1 ? i(h[1]) : \"\"\n        );\n    }\n  });\n})(\n  typeof C < \"u\" ? C : typeof window < \"u\" ? window : typeof self < \"u\" ? self : C\n);\n(function(k) {\n  var w = function() {\n    try {\n      var c = new k.URL(\"b\", \"http://a\");\n      return c.pathname = \"c d\", c.href === \"http://a/c%20d\" && c.searchParams;\n    } catch {\n      return !1;\n    }\n  }, d = function() {\n    var c = k.URL, i = function(l, n) {\n      typeof l != \"string\" && (l = String(l));\n      var o = document, e;\n      if (n && (k.location === void 0 || n !== k.location.href)) {\n        o = document.implementation.createHTMLDocument(\"\"), e = o.createElement(\"base\"), e.href = n, o.head.appendChild(e);\n        try {\n          if (e.href.indexOf(n) !== 0)\n            throw new Error(e.href);\n        } catch (m) {\n          throw new Error(\"URL unable to set base \" + n + \" due to \" + m);\n        }\n      }\n      var s = o.createElement(\"a\");\n      s.href = l, e && (o.body.appendChild(s), s.href = s.href);\n      var h = o.createElement(\"input\");\n      if (h.type = \"url\", h.value = l, s.protocol === \":\" || !/:/.test(s.href) || !h.checkValidity() && !n)\n        throw new TypeError(\"Invalid URL\");\n      Object.defineProperty(this, \"_anchorElement\", {\n        value: s\n      });\n      var t = new k.URLSearchParams(this.search), y = !0, r = !0, u = this;\n      [\"append\", \"delete\", \"set\"].forEach(function(m) {\n        var b = t[m];\n        t[m] = function() {\n          b.apply(t, arguments), y && (r = !1, u.search = t.toString(), r = !0);\n        };\n      }), Object.defineProperty(this, \"searchParams\", {\n        value: t,\n        enumerable: !0\n      });\n      var f = void 0;\n      Object.defineProperty(this, \"_updateSearchParams\", {\n        enumerable: !1,\n        configurable: !1,\n        writable: !1,\n        value: function() {\n          this.search !== f && (f = this.search, r && (y = !1, this.searchParams._fromString(this.search), y = !0));\n        }\n      });\n    }, a = i.prototype, p = function(l) {\n      Object.defineProperty(a, l, {\n        get: function() {\n          return this._anchorElement[l];\n        },\n        set: function(n) {\n          this._anchorElement[l] = n;\n        },\n        enumerable: !0\n      });\n    };\n    [\"hash\", \"host\", \"hostname\", \"port\", \"protocol\"].forEach(function(l) {\n      p(l);\n    }), Object.defineProperty(a, \"search\", {\n      get: function() {\n        return this._anchorElement.search;\n      },\n      set: function(l) {\n        this._anchorElement.search = l, this._updateSearchParams();\n      },\n      enumerable: !0\n    }), Object.defineProperties(a, {\n      toString: {\n        get: function() {\n          var l = this;\n          return function() {\n            return l.href;\n          };\n        }\n      },\n      href: {\n        get: function() {\n          return this._anchorElement.href.replace(/\\?$/, \"\");\n        },\n        set: function(l) {\n          this._anchorElement.href = l, this._updateSearchParams();\n        },\n        enumerable: !0\n      },\n      pathname: {\n        get: function() {\n          return this._anchorElement.pathname.replace(/(^\\/?)/, \"/\");\n        },\n        set: function(l) {\n          this._anchorElement.pathname = l;\n        },\n        enumerable: !0\n      },\n      origin: {\n        get: function() {\n          var l = { \"http:\": 80, \"https:\": 443, \"ftp:\": 21 }[this._anchorElement.protocol], n = this._anchorElement.port != l && this._anchorElement.port !== \"\";\n          return this._anchorElement.protocol + \"//\" + this._anchorElement.hostname + (n ? \":\" + this._anchorElement.port : \"\");\n        },\n        enumerable: !0\n      },\n      password: {\n        // TODO\n        get: function() {\n          return \"\";\n        },\n        set: function(l) {\n        },\n        enumerable: !0\n      },\n      username: {\n        // TODO\n        get: function() {\n          return \"\";\n        },\n        set: function(l) {\n        },\n        enumerable: !0\n      }\n    }), i.createObjectURL = function(l) {\n      return c.createObjectURL.apply(c, arguments);\n    }, i.revokeObjectURL = function(l) {\n      return c.revokeObjectURL.apply(c, arguments);\n    }, k.URL = i;\n  };\n  if (w() || d(), k.location !== void 0 && !(\"origin\" in k.location)) {\n    var v = function() {\n      return k.location.protocol + \"//\" + k.location.hostname + (k.location.port ? \":\" + k.location.port : \"\");\n    };\n    try {\n      Object.defineProperty(k.location, \"origin\", {\n        get: v,\n        enumerable: !0\n      });\n    } catch {\n      setInterval(function() {\n        k.location.origin = v();\n      }, 100);\n    }\n  }\n})(\n  typeof C < \"u\" ? C : typeof window < \"u\" ? window : typeof self < \"u\" ? self : C\n);\nvar j = { exports: {} };\n(function(k, w) {\n  (function(d, v) {\n    k.exports = v();\n  })(window, function() {\n    return function(d) {\n      var v = {};\n      function c(i) {\n        if (v[i])\n          return v[i].exports;\n        var a = v[i] = { i, l: !1, exports: {} };\n        return d[i].call(a.exports, a, a.exports, c), a.l = !0, a.exports;\n      }\n      return c.m = d, c.c = v, c.d = function(i, a, p) {\n        c.o(i, a) || Object.defineProperty(i, a, { enumerable: !0, get: p });\n      }, c.r = function(i) {\n        typeof Symbol < \"u\" && Symbol.toStringTag && Object.defineProperty(i, Symbol.toStringTag, { value: \"Module\" }), Object.defineProperty(i, \"__esModule\", { value: !0 });\n      }, c.t = function(i, a) {\n        if (1 & a && (i = c(i)), 8 & a || 4 & a && typeof i == \"object\" && i && i.__esModule)\n          return i;\n        var p = /* @__PURE__ */ Object.create(null);\n        if (c.r(p), Object.defineProperty(p, \"default\", { enumerable: !0, value: i }), 2 & a && typeof i != \"string\")\n          for (var l in i)\n            c.d(p, l, (function(n) {\n              return i[n];\n            }).bind(null, l));\n        return p;\n      }, c.n = function(i) {\n        var a = i && i.__esModule ? function() {\n          return i.default;\n        } : function() {\n          return i;\n        };\n        return c.d(a, \"a\", a), a;\n      }, c.o = function(i, a) {\n        return Object.prototype.hasOwnProperty.call(i, a);\n      }, c.p = \"\", c(c.s = 3);\n    }([function(d, v) {\n      var c;\n      c = function() {\n        return this;\n      }();\n      try {\n        c = c || new Function(\"return this\")();\n      } catch {\n        typeof window == \"object\" && (c = window);\n      }\n      d.exports = c;\n    }, function(d, v, c) {\n      (function(i) {\n        var a = c(2), p = setTimeout;\n        function l() {\n        }\n        function n(r) {\n          if (!(this instanceof n))\n            throw new TypeError(\"Promises must be constructed via new\");\n          if (typeof r != \"function\")\n            throw new TypeError(\"not a function\");\n          this._state = 0, this._handled = !1, this._value = void 0, this._deferreds = [], y(r, this);\n        }\n        function o(r, u) {\n          for (; r._state === 3; )\n            r = r._value;\n          r._state !== 0 ? (r._handled = !0, n._immediateFn(function() {\n            var f = r._state === 1 ? u.onFulfilled : u.onRejected;\n            if (f !== null) {\n              var m;\n              try {\n                m = f(r._value);\n              } catch (b) {\n                return void s(u.promise, b);\n              }\n              e(u.promise, m);\n            } else\n              (r._state === 1 ? e : s)(u.promise, r._value);\n          })) : r._deferreds.push(u);\n        }\n        function e(r, u) {\n          try {\n            if (u === r)\n              throw new TypeError(\"A promise cannot be resolved with itself.\");\n            if (u && (typeof u == \"object\" || typeof u == \"function\")) {\n              var f = u.then;\n              if (u instanceof n)\n                return r._state = 3, r._value = u, void h(r);\n              if (typeof f == \"function\")\n                return void y((m = f, b = u, function() {\n                  m.apply(b, arguments);\n                }), r);\n            }\n            r._state = 1, r._value = u, h(r);\n          } catch (g) {\n            s(r, g);\n          }\n          var m, b;\n        }\n        function s(r, u) {\n          r._state = 2, r._value = u, h(r);\n        }\n        function h(r) {\n          r._state === 2 && r._deferreds.length === 0 && n._immediateFn(function() {\n            r._handled || n._unhandledRejectionFn(r._value);\n          });\n          for (var u = 0, f = r._deferreds.length; u < f; u++)\n            o(r, r._deferreds[u]);\n          r._deferreds = null;\n        }\n        function t(r, u, f) {\n          this.onFulfilled = typeof r == \"function\" ? r : null, this.onRejected = typeof u == \"function\" ? u : null, this.promise = f;\n        }\n        function y(r, u) {\n          var f = !1;\n          try {\n            r(function(m) {\n              f || (f = !0, e(u, m));\n            }, function(m) {\n              f || (f = !0, s(u, m));\n            });\n          } catch (m) {\n            if (f)\n              return;\n            f = !0, s(u, m);\n          }\n        }\n        n.prototype.catch = function(r) {\n          return this.then(null, r);\n        }, n.prototype.then = function(r, u) {\n          var f = new this.constructor(l);\n          return o(this, new t(r, u, f)), f;\n        }, n.prototype.finally = a.a, n.all = function(r) {\n          return new n(function(u, f) {\n            if (!r || r.length === void 0)\n              throw new TypeError(\"Promise.all accepts an array\");\n            var m = Array.prototype.slice.call(r);\n            if (m.length === 0)\n              return u([]);\n            var b = m.length;\n            function g(T, E) {\n              try {\n                if (E && (typeof E == \"object\" || typeof E == \"function\")) {\n                  var S = E.then;\n                  if (typeof S == \"function\")\n                    return void S.call(E, function(L) {\n                      g(T, L);\n                    }, f);\n                }\n                m[T] = E, --b == 0 && u(m);\n              } catch (L) {\n                f(L);\n              }\n            }\n            for (var _ = 0; _ < m.length; _++)\n              g(_, m[_]);\n          });\n        }, n.resolve = function(r) {\n          return r && typeof r == \"object\" && r.constructor === n ? r : new n(function(u) {\n            u(r);\n          });\n        }, n.reject = function(r) {\n          return new n(function(u, f) {\n            f(r);\n          });\n        }, n.race = function(r) {\n          return new n(function(u, f) {\n            for (var m = 0, b = r.length; m < b; m++)\n              r[m].then(u, f);\n          });\n        }, n._immediateFn = typeof i == \"function\" && function(r) {\n          i(r);\n        } || function(r) {\n          p(r, 0);\n        }, n._unhandledRejectionFn = function(r) {\n          typeof console < \"u\" && console && console.warn(\"Possible Unhandled Promise Rejection:\", r);\n        }, v.a = n;\n      }).call(this, c(5).setImmediate);\n    }, function(d, v, c) {\n      v.a = function(i) {\n        var a = this.constructor;\n        return this.then(function(p) {\n          return a.resolve(i()).then(function() {\n            return p;\n          });\n        }, function(p) {\n          return a.resolve(i()).then(function() {\n            return a.reject(p);\n          });\n        });\n      };\n    }, function(d, v, c) {\n      function i(t) {\n        return (i = typeof Symbol == \"function\" && typeof Symbol.iterator == \"symbol\" ? function(y) {\n          return typeof y;\n        } : function(y) {\n          return y && typeof Symbol == \"function\" && y.constructor === Symbol && y !== Symbol.prototype ? \"symbol\" : typeof y;\n        })(t);\n      }\n      c(4);\n      var a, p, l, n, o, e, s = c(8), h = (p = function(t) {\n        return new Promise(function(y, r) {\n          t = n(t), t = o(t);\n          var u = window.XMLHttpRequest ? new window.XMLHttpRequest() : new window.ActiveXObject(\"Microsoft.XMLHTTP\");\n          u.open(t.method, t.url), u.setRequestHeader(\"X-Requested-With\", \"XMLHttpRequest\"), Object.keys(t.headers).forEach(function(m) {\n            var b = t.headers[m];\n            u.setRequestHeader(m, b);\n          });\n          var f = t.ratio;\n          u.upload.addEventListener(\"progress\", function(m) {\n            var b = Math.round(m.loaded / m.total * 100), g = Math.ceil(b * f / 100);\n            t.progress(g);\n          }, !1), u.addEventListener(\"progress\", function(m) {\n            var b = Math.round(m.loaded / m.total * 100), g = Math.ceil(b * (100 - f) / 100) + f;\n            t.progress(g);\n          }, !1), u.onreadystatechange = function() {\n            if (u.readyState === 4) {\n              var m = u.response;\n              try {\n                m = JSON.parse(m);\n              } catch {\n              }\n              var b = s.parseHeaders(u.getAllResponseHeaders()), g = { body: m, code: u.status, headers: b };\n              u.status === 200 ? y(g) : r(g);\n            }\n          }, u.send(t.data);\n        });\n      }, l = function(t) {\n        return t.method = \"POST\", p(t);\n      }, n = function() {\n        var t = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        if (t.url && typeof t.url != \"string\")\n          throw new Error(\"Url must be a string\");\n        if (t.url = t.url || \"\", t.method && typeof t.method != \"string\")\n          throw new Error(\"`method` must be a string or null\");\n        if (t.method = t.method ? t.method.toUpperCase() : \"GET\", t.headers && i(t.headers) !== \"object\")\n          throw new Error(\"`headers` must be an object or null\");\n        if (t.headers = t.headers || {}, t.type && (typeof t.type != \"string\" || !Object.values(a).includes(t.type)))\n          throw new Error(\"`type` must be taken from module's «contentType» library\");\n        if (t.progress && typeof t.progress != \"function\")\n          throw new Error(\"`progress` must be a function or null\");\n        if (t.progress = t.progress || function(y) {\n        }, t.beforeSend = t.beforeSend || function(y) {\n        }, t.ratio && typeof t.ratio != \"number\")\n          throw new Error(\"`ratio` must be a number\");\n        if (t.ratio < 0 || t.ratio > 100)\n          throw new Error(\"`ratio` must be in a 0-100 interval\");\n        if (t.ratio = t.ratio || 90, t.accept && typeof t.accept != \"string\")\n          throw new Error(\"`accept` must be a string with a list of allowed mime-types\");\n        if (t.accept = t.accept || \"*/*\", t.multiple && typeof t.multiple != \"boolean\")\n          throw new Error(\"`multiple` must be a true or false\");\n        if (t.multiple = t.multiple || !1, t.fieldName && typeof t.fieldName != \"string\")\n          throw new Error(\"`fieldName` must be a string\");\n        return t.fieldName = t.fieldName || \"files\", t;\n      }, o = function(t) {\n        switch (t.method) {\n          case \"GET\":\n            var y = e(t.data, a.URLENCODED);\n            delete t.data, t.url = /\\?/.test(t.url) ? t.url + \"&\" + y : t.url + \"?\" + y;\n            break;\n          case \"POST\":\n          case \"PUT\":\n          case \"DELETE\":\n          case \"UPDATE\":\n            var r = function() {\n              return (arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {}).type || a.JSON;\n            }(t);\n            (s.isFormData(t.data) || s.isFormElement(t.data)) && (r = a.FORM), t.data = e(t.data, r), r !== h.contentType.FORM && (t.headers[\"content-type\"] = r);\n        }\n        return t;\n      }, e = function() {\n        var t = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        switch (arguments.length > 1 ? arguments[1] : void 0) {\n          case a.URLENCODED:\n            return s.urlEncode(t);\n          case a.JSON:\n            return s.jsonEncode(t);\n          case a.FORM:\n            return s.formEncode(t);\n          default:\n            return t;\n        }\n      }, { contentType: a = { URLENCODED: \"application/x-www-form-urlencoded; charset=utf-8\", FORM: \"multipart/form-data\", JSON: \"application/json; charset=utf-8\" }, request: p, get: function(t) {\n        return t.method = \"GET\", p(t);\n      }, post: l, transport: function(t) {\n        return t = n(t), s.selectFiles(t).then(function(y) {\n          for (var r = new FormData(), u = 0; u < y.length; u++)\n            r.append(t.fieldName, y[u], y[u].name);\n          return s.isObject(t.data) && Object.keys(t.data).forEach(function(f) {\n            var m = t.data[f];\n            r.append(f, m);\n          }), t.beforeSend && t.beforeSend(y), t.data = r, l(t);\n        });\n      }, selectFiles: function(t) {\n        return delete (t = n(t)).beforeSend, s.selectFiles(t);\n      } });\n      d.exports = h;\n    }, function(d, v, c) {\n      c.r(v);\n      var i = c(1);\n      window.Promise = window.Promise || i.a;\n    }, function(d, v, c) {\n      (function(i) {\n        var a = i !== void 0 && i || typeof self < \"u\" && self || window, p = Function.prototype.apply;\n        function l(n, o) {\n          this._id = n, this._clearFn = o;\n        }\n        v.setTimeout = function() {\n          return new l(p.call(setTimeout, a, arguments), clearTimeout);\n        }, v.setInterval = function() {\n          return new l(p.call(setInterval, a, arguments), clearInterval);\n        }, v.clearTimeout = v.clearInterval = function(n) {\n          n && n.close();\n        }, l.prototype.unref = l.prototype.ref = function() {\n        }, l.prototype.close = function() {\n          this._clearFn.call(a, this._id);\n        }, v.enroll = function(n, o) {\n          clearTimeout(n._idleTimeoutId), n._idleTimeout = o;\n        }, v.unenroll = function(n) {\n          clearTimeout(n._idleTimeoutId), n._idleTimeout = -1;\n        }, v._unrefActive = v.active = function(n) {\n          clearTimeout(n._idleTimeoutId);\n          var o = n._idleTimeout;\n          o >= 0 && (n._idleTimeoutId = setTimeout(function() {\n            n._onTimeout && n._onTimeout();\n          }, o));\n        }, c(6), v.setImmediate = typeof self < \"u\" && self.setImmediate || i !== void 0 && i.setImmediate || this && this.setImmediate, v.clearImmediate = typeof self < \"u\" && self.clearImmediate || i !== void 0 && i.clearImmediate || this && this.clearImmediate;\n      }).call(this, c(0));\n    }, function(d, v, c) {\n      (function(i, a) {\n        (function(p, l) {\n          if (!p.setImmediate) {\n            var n, o, e, s, h, t = 1, y = {}, r = !1, u = p.document, f = Object.getPrototypeOf && Object.getPrototypeOf(p);\n            f = f && f.setTimeout ? f : p, {}.toString.call(p.process) === \"[object process]\" ? n = function(g) {\n              a.nextTick(function() {\n                b(g);\n              });\n            } : function() {\n              if (p.postMessage && !p.importScripts) {\n                var g = !0, _ = p.onmessage;\n                return p.onmessage = function() {\n                  g = !1;\n                }, p.postMessage(\"\", \"*\"), p.onmessage = _, g;\n              }\n            }() ? (s = \"setImmediate$\" + Math.random() + \"$\", h = function(g) {\n              g.source === p && typeof g.data == \"string\" && g.data.indexOf(s) === 0 && b(+g.data.slice(s.length));\n            }, p.addEventListener ? p.addEventListener(\"message\", h, !1) : p.attachEvent(\"onmessage\", h), n = function(g) {\n              p.postMessage(s + g, \"*\");\n            }) : p.MessageChannel ? ((e = new MessageChannel()).port1.onmessage = function(g) {\n              b(g.data);\n            }, n = function(g) {\n              e.port2.postMessage(g);\n            }) : u && \"onreadystatechange\" in u.createElement(\"script\") ? (o = u.documentElement, n = function(g) {\n              var _ = u.createElement(\"script\");\n              _.onreadystatechange = function() {\n                b(g), _.onreadystatechange = null, o.removeChild(_), _ = null;\n              }, o.appendChild(_);\n            }) : n = function(g) {\n              setTimeout(b, 0, g);\n            }, f.setImmediate = function(g) {\n              typeof g != \"function\" && (g = new Function(\"\" + g));\n              for (var _ = new Array(arguments.length - 1), T = 0; T < _.length; T++)\n                _[T] = arguments[T + 1];\n              var E = { callback: g, args: _ };\n              return y[t] = E, n(t), t++;\n            }, f.clearImmediate = m;\n          }\n          function m(g) {\n            delete y[g];\n          }\n          function b(g) {\n            if (r)\n              setTimeout(b, 0, g);\n            else {\n              var _ = y[g];\n              if (_) {\n                r = !0;\n                try {\n                  (function(T) {\n                    var E = T.callback, S = T.args;\n                    switch (S.length) {\n                      case 0:\n                        E();\n                        break;\n                      case 1:\n                        E(S[0]);\n                        break;\n                      case 2:\n                        E(S[0], S[1]);\n                        break;\n                      case 3:\n                        E(S[0], S[1], S[2]);\n                        break;\n                      default:\n                        E.apply(l, S);\n                    }\n                  })(_);\n                } finally {\n                  m(g), r = !1;\n                }\n              }\n            }\n          }\n        })(typeof self > \"u\" ? i === void 0 ? this : i : self);\n      }).call(this, c(0), c(7));\n    }, function(d, v) {\n      var c, i, a = d.exports = {};\n      function p() {\n        throw new Error(\"setTimeout has not been defined\");\n      }\n      function l() {\n        throw new Error(\"clearTimeout has not been defined\");\n      }\n      function n(f) {\n        if (c === setTimeout)\n          return setTimeout(f, 0);\n        if ((c === p || !c) && setTimeout)\n          return c = setTimeout, setTimeout(f, 0);\n        try {\n          return c(f, 0);\n        } catch {\n          try {\n            return c.call(null, f, 0);\n          } catch {\n            return c.call(this, f, 0);\n          }\n        }\n      }\n      (function() {\n        try {\n          c = typeof setTimeout == \"function\" ? setTimeout : p;\n        } catch {\n          c = p;\n        }\n        try {\n          i = typeof clearTimeout == \"function\" ? clearTimeout : l;\n        } catch {\n          i = l;\n        }\n      })();\n      var o, e = [], s = !1, h = -1;\n      function t() {\n        s && o && (s = !1, o.length ? e = o.concat(e) : h = -1, e.length && y());\n      }\n      function y() {\n        if (!s) {\n          var f = n(t);\n          s = !0;\n          for (var m = e.length; m; ) {\n            for (o = e, e = []; ++h < m; )\n              o && o[h].run();\n            h = -1, m = e.length;\n          }\n          o = null, s = !1, function(b) {\n            if (i === clearTimeout)\n              return clearTimeout(b);\n            if ((i === l || !i) && clearTimeout)\n              return i = clearTimeout, clearTimeout(b);\n            try {\n              i(b);\n            } catch {\n              try {\n                return i.call(null, b);\n              } catch {\n                return i.call(this, b);\n              }\n            }\n          }(f);\n        }\n      }\n      function r(f, m) {\n        this.fun = f, this.array = m;\n      }\n      function u() {\n      }\n      a.nextTick = function(f) {\n        var m = new Array(arguments.length - 1);\n        if (arguments.length > 1)\n          for (var b = 1; b < arguments.length; b++)\n            m[b - 1] = arguments[b];\n        e.push(new r(f, m)), e.length !== 1 || s || n(y);\n      }, r.prototype.run = function() {\n        this.fun.apply(null, this.array);\n      }, a.title = \"browser\", a.browser = !0, a.env = {}, a.argv = [], a.version = \"\", a.versions = {}, a.on = u, a.addListener = u, a.once = u, a.off = u, a.removeListener = u, a.removeAllListeners = u, a.emit = u, a.prependListener = u, a.prependOnceListener = u, a.listeners = function(f) {\n        return [];\n      }, a.binding = function(f) {\n        throw new Error(\"process.binding is not supported\");\n      }, a.cwd = function() {\n        return \"/\";\n      }, a.chdir = function(f) {\n        throw new Error(\"process.chdir is not supported\");\n      }, a.umask = function() {\n        return 0;\n      };\n    }, function(d, v, c) {\n      function i(p, l) {\n        for (var n = 0; n < l.length; n++) {\n          var o = l[n];\n          o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(p, o.key, o);\n        }\n      }\n      var a = c(9);\n      d.exports = function() {\n        function p() {\n          (function(e, s) {\n            if (!(e instanceof s))\n              throw new TypeError(\"Cannot call a class as a function\");\n          })(this, p);\n        }\n        var l, n, o;\n        return l = p, o = [{ key: \"urlEncode\", value: function(e) {\n          return a(e);\n        } }, { key: \"jsonEncode\", value: function(e) {\n          return JSON.stringify(e);\n        } }, { key: \"formEncode\", value: function(e) {\n          if (this.isFormData(e))\n            return e;\n          if (this.isFormElement(e))\n            return new FormData(e);\n          if (this.isObject(e)) {\n            var s = new FormData();\n            return Object.keys(e).forEach(function(h) {\n              var t = e[h];\n              s.append(h, t);\n            }), s;\n          }\n          throw new Error(\"`data` must be an instance of Object, FormData or <FORM> HTMLElement\");\n        } }, { key: \"isObject\", value: function(e) {\n          return Object.prototype.toString.call(e) === \"[object Object]\";\n        } }, { key: \"isFormData\", value: function(e) {\n          return e instanceof FormData;\n        } }, { key: \"isFormElement\", value: function(e) {\n          return e instanceof HTMLFormElement;\n        } }, { key: \"selectFiles\", value: function() {\n          var e = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n          return new Promise(function(s, h) {\n            var t = document.createElement(\"INPUT\");\n            t.type = \"file\", e.multiple && t.setAttribute(\"multiple\", \"multiple\"), e.accept && t.setAttribute(\"accept\", e.accept), t.style.display = \"none\", document.body.appendChild(t), t.addEventListener(\"change\", function(y) {\n              var r = y.target.files;\n              s(r), document.body.removeChild(t);\n            }, !1), t.click();\n          });\n        } }, { key: \"parseHeaders\", value: function(e) {\n          var s = e.trim().split(/[\\r\\n]+/), h = {};\n          return s.forEach(function(t) {\n            var y = t.split(\": \"), r = y.shift(), u = y.join(\": \");\n            r && (h[r] = u);\n          }), h;\n        } }], (n = null) && i(l.prototype, n), o && i(l, o), p;\n      }();\n    }, function(d, v) {\n      var c = function(a) {\n        return encodeURIComponent(a).replace(/[!'()*]/g, escape).replace(/%20/g, \"+\");\n      }, i = function(a, p, l, n) {\n        return p = p || null, l = l || \"&\", n = n || null, a ? function(o) {\n          for (var e = new Array(), s = 0; s < o.length; s++)\n            o[s] && e.push(o[s]);\n          return e;\n        }(Object.keys(a).map(function(o) {\n          var e, s, h = o;\n          if (n && (h = n + \"[\" + h + \"]\"), typeof a[o] == \"object\" && a[o] !== null)\n            e = i(a[o], null, l, h);\n          else {\n            p && (s = h, h = !isNaN(parseFloat(s)) && isFinite(s) ? p + Number(h) : h);\n            var t = a[o];\n            t = (t = (t = (t = t === !0 ? \"1\" : t) === !1 ? \"0\" : t) === 0 ? \"0\" : t) || \"\", e = c(h) + \"=\" + c(t);\n          }\n          return e;\n        })).join(l).replace(/[!'()*]/g, \"\") : \"\";\n      };\n      d.exports = i;\n    }]);\n  });\n})(j);\nvar P = j.exports;\nconst R = /* @__PURE__ */ O(P), F = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M7.69998 12.6L7.67896 12.62C6.53993 13.7048 6.52012 15.5155 7.63516 16.625V16.625C8.72293 17.7073 10.4799 17.7102 11.5712 16.6314L13.0263 15.193C14.0703 14.1609 14.2141 12.525 13.3662 11.3266L13.22 11.12\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M16.22 11.12L16.3564 10.9805C17.2895 10.0265 17.3478 8.5207 16.4914 7.49733V7.49733C15.569 6.39509 13.9269 6.25143 12.8271 7.17675L11.39 8.38588C10.0935 9.47674 9.95704 11.4241 11.0887 12.6852L11.12 12.72\"/></svg>';\nclass I {\n  /**\n   * Notify core that read-only mode supported\n   *\n   * @returns {boolean}\n   */\n  static get isReadOnlySupported() {\n    return !0;\n  }\n  /**\n   * Get Tool toolbox settings\n   * icon - Tool icon's SVG\n   * title - title to show in toolbox\n   *\n   * @returns {{icon: string, title: string}}\n   */\n  static get toolbox() {\n    return {\n      icon: F,\n      title: \"Link\"\n    };\n  }\n  /**\n   * Allow to press Enter inside the LinkTool input\n   *\n   * @returns {boolean}\n   * @public\n   */\n  static get enableLineBreaks() {\n    return !0;\n  }\n  /**\n   * @param {object} options - Tool constructor options fot from Editor.js\n   * @param {LinkToolData} options.data - previously saved data\n   * @param {LinkToolConfig} options.config - user config for Tool\n   * @param {object} options.api - Editor.js API\n   * @param {boolean} options.readOnly - read-only mode flag\n   */\n  constructor({ data: w, config: d, api: v, readOnly: c }) {\n    this.api = v, this.readOnly = c, this.config = {\n      endpoint: d.endpoint || \"\",\n      headers: d.headers || {}\n    }, this.nodes = {\n      wrapper: null,\n      container: null,\n      progress: null,\n      input: null,\n      inputHolder: null,\n      linkContent: null,\n      linkImage: null,\n      linkTitle: null,\n      linkDescription: null,\n      linkText: null\n    }, this._data = {\n      link: \"\",\n      meta: {}\n    }, this.data = w;\n  }\n  /**\n   * Renders Block content\n   *\n   * @public\n   *\n   * @returns {HTMLDivElement}\n   */\n  render() {\n    return this.nodes.wrapper = this.make(\"div\", this.CSS.baseClass), this.nodes.container = this.make(\"div\", this.CSS.container), this.nodes.inputHolder = this.makeInputHolder(), this.nodes.linkContent = this.prepareLinkPreview(), Object.keys(this.data.meta).length ? (this.nodes.container.appendChild(this.nodes.linkContent), this.showLinkPreview(this.data.meta)) : this.nodes.container.appendChild(this.nodes.inputHolder), this.nodes.wrapper.appendChild(this.nodes.container), this.nodes.wrapper;\n  }\n  /**\n   * Return Block data\n   *\n   * @public\n   *\n   * @returns {LinkToolData}\n   */\n  save() {\n    return this.data;\n  }\n  /**\n   * Validate Block data\n   * - check if given link is an empty string or not.\n   *\n   * @public\n   *\n   * @returns {boolean} false if saved data is incorrect, otherwise true\n   */\n  validate() {\n    return this.data.link.trim() !== \"\";\n  }\n  /**\n   * Stores all Tool's data\n   *\n   * @param {LinkToolData} data - data to store\n   */\n  set data(w) {\n    this._data = Object.assign({}, {\n      link: w.link || this._data.link,\n      meta: w.meta || this._data.meta\n    });\n  }\n  /**\n   * Return Tool data\n   *\n   * @returns {LinkToolData}\n   */\n  get data() {\n    return this._data;\n  }\n  /**\n   * @returns {object} - Link Tool styles\n   */\n  get CSS() {\n    return {\n      baseClass: this.api.styles.block,\n      input: this.api.styles.input,\n      /**\n       * Tool's classes\n       */\n      container: \"link-tool\",\n      inputEl: \"link-tool__input\",\n      inputHolder: \"link-tool__input-holder\",\n      inputError: \"link-tool__input-holder--error\",\n      linkContent: \"link-tool__content\",\n      linkContentRendered: \"link-tool__content--rendered\",\n      linkImage: \"link-tool__image\",\n      linkTitle: \"link-tool__title\",\n      linkDescription: \"link-tool__description\",\n      linkText: \"link-tool__anchor\",\n      progress: \"link-tool__progress\",\n      progressLoading: \"link-tool__progress--loading\",\n      progressLoaded: \"link-tool__progress--loaded\"\n    };\n  }\n  /**\n   * Prepare input holder\n   *\n   * @returns {HTMLElement}\n   */\n  makeInputHolder() {\n    const w = this.make(\"div\", this.CSS.inputHolder);\n    return this.nodes.progress = this.make(\"label\", this.CSS.progress), this.nodes.input = this.make(\"div\", [this.CSS.input, this.CSS.inputEl], {\n      contentEditable: !this.readOnly\n    }), this.nodes.input.dataset.placeholder = this.api.i18n.t(\"Link\"), this.readOnly || (this.nodes.input.addEventListener(\"paste\", (d) => {\n      this.startFetching(d);\n    }), this.nodes.input.addEventListener(\"keydown\", (d) => {\n      const [v, c] = [13, 65], i = d.ctrlKey || d.metaKey;\n      switch (d.keyCode) {\n        case v:\n          d.preventDefault(), d.stopPropagation(), this.startFetching(d);\n          break;\n        case c:\n          i && this.selectLinkUrl(d);\n          break;\n      }\n    })), w.appendChild(this.nodes.progress), w.appendChild(this.nodes.input), w;\n  }\n  /**\n   * Activates link data fetching by url\n   *\n   * @param {PasteEvent|KeyboardEvent} event - fetching could be fired by a pase or keydown events\n   */\n  startFetching(w) {\n    let d = this.nodes.input.textContent;\n    w.type === \"paste\" && (d = (w.clipboardData || window.clipboardData).getData(\"text\")), this.removeErrorStyle(), this.fetchLinkData(d);\n  }\n  /**\n   * If previous link data fetching failed, remove error styles\n   */\n  removeErrorStyle() {\n    this.nodes.inputHolder.classList.remove(this.CSS.inputError), this.nodes.inputHolder.insertBefore(this.nodes.progress, this.nodes.input);\n  }\n  /**\n   * Select LinkTool input content by CMD+A\n   *\n   * @param {KeyboardEvent} event - keydown\n   */\n  selectLinkUrl(w) {\n    w.preventDefault(), w.stopPropagation();\n    const d = window.getSelection(), v = new Range(), a = d.anchorNode.parentNode.closest(`.${this.CSS.inputHolder}`).querySelector(`.${this.CSS.inputEl}`);\n    v.selectNodeContents(a), d.removeAllRanges(), d.addRange(v);\n  }\n  /**\n   * Prepare link preview holder\n   *\n   * @returns {HTMLElement}\n   */\n  prepareLinkPreview() {\n    const w = this.make(\"a\", this.CSS.linkContent, {\n      target: \"_blank\",\n      rel: \"nofollow noindex noreferrer\"\n    });\n    return this.nodes.linkImage = this.make(\"div\", this.CSS.linkImage), this.nodes.linkTitle = this.make(\"div\", this.CSS.linkTitle), this.nodes.linkDescription = this.make(\"p\", this.CSS.linkDescription), this.nodes.linkText = this.make(\"span\", this.CSS.linkText), w;\n  }\n  /**\n   * Compose link preview from fetched data\n   *\n   * @param {metaData} meta - link meta data\n   */\n  showLinkPreview({ image: w, title: d, description: v }) {\n    this.nodes.container.appendChild(this.nodes.linkContent), w && w.url && (this.nodes.linkImage.style.backgroundImage = \"url(\" + w.url + \")\", this.nodes.linkContent.appendChild(this.nodes.linkImage)), d && (this.nodes.linkTitle.textContent = d, this.nodes.linkContent.appendChild(this.nodes.linkTitle)), v && (this.nodes.linkDescription.textContent = v, this.nodes.linkContent.appendChild(this.nodes.linkDescription)), this.nodes.linkContent.classList.add(this.CSS.linkContentRendered), this.nodes.linkContent.setAttribute(\"href\", this.data.link), this.nodes.linkContent.appendChild(this.nodes.linkText);\n    try {\n      this.nodes.linkText.textContent = new URL(this.data.link).hostname;\n    } catch {\n      this.nodes.linkText.textContent = this.data.link;\n    }\n  }\n  /**\n   * Show loading progress bar\n   */\n  showProgress() {\n    this.nodes.progress.classList.add(this.CSS.progressLoading);\n  }\n  /**\n   * Hide loading progress bar\n   *\n   * @returns {Promise<void>}\n   */\n  hideProgress() {\n    return new Promise((w) => {\n      this.nodes.progress.classList.remove(this.CSS.progressLoading), this.nodes.progress.classList.add(this.CSS.progressLoaded), setTimeout(w, 500);\n    });\n  }\n  /**\n   * If data fetching failed, set input error style\n   */\n  applyErrorStyle() {\n    this.nodes.inputHolder.classList.add(this.CSS.inputError), this.nodes.progress.remove();\n  }\n  /**\n   * Sends to backend pasted url and receives link data\n   *\n   * @param {string} url - link source url\n   */\n  async fetchLinkData(w) {\n    this.showProgress(), this.data = { link: w };\n    try {\n      const { body: d } = await R.get({\n        url: this.config.endpoint,\n        headers: this.config.headers,\n        data: {\n          url: w\n        }\n      });\n      this.onFetch(d);\n    } catch {\n      this.fetchingFailed(this.api.i18n.t(\"Couldn't fetch the link data\"));\n    }\n  }\n  /**\n   * Link data fetching callback\n   *\n   * @param {UploadResponseFormat} response - backend response\n   */\n  onFetch(w) {\n    if (!w || !w.success) {\n      this.fetchingFailed(this.api.i18n.t(\"Couldn't get this link data, try the other one\"));\n      return;\n    }\n    const d = w.meta, v = w.link || this.data.link;\n    if (this.data = {\n      meta: d,\n      link: v\n    }, !d) {\n      this.fetchingFailed(this.api.i18n.t(\"Wrong response format from the server\"));\n      return;\n    }\n    this.hideProgress().then(() => {\n      this.nodes.inputHolder.remove(), this.showLinkPreview(d);\n    });\n  }\n  /**\n   * Handle link fetching errors\n   *\n   * @private\n   *\n   * @param {string} errorMessage - message to explain user what he should do\n   */\n  fetchingFailed(w) {\n    this.api.notifier.show({\n      message: w,\n      style: \"error\"\n    }), this.applyErrorStyle();\n  }\n  /**\n   * Helper method for elements creation\n   *\n   * @param {string} tagName - name of creating element\n   * @param {string|string[]} [classNames] - list of CSS classes to add\n   * @param {object} [attributes] - object with attributes to add\n   * @returns {HTMLElement}\n   */\n  make(w, d = null, v = {}) {\n    const c = document.createElement(w);\n    Array.isArray(d) ? c.classList.add(...d) : d && c.classList.add(d);\n    for (const i in v)\n      c[i] = v[i];\n    return c;\n  }\n}\nexport {\n  I as default\n};\n"], "mappings": ";AAAA,CAAC,YAAU;EAAC,YAAY;;EAAC,IAAG;IAAC,IAAG,OAAOA,QAAQ,GAAC,GAAG,EAAC;MAAC,IAAIC,CAAC,GAACD,QAAQ,CAACE,aAAa,CAAC,OAAO,CAAC;MAACD,CAAC,CAACE,WAAW,CAACH,QAAQ,CAACI,cAAc,CAAE,8rGAA6rG,CAAC,CAAC,EAACJ,QAAQ,CAACK,IAAI,CAACF,WAAW,CAACF,CAAC,CAAC;IAAA;EAAC,CAAC,QAAMK,CAAC,EAAC;IAACC,OAAO,CAACC,KAAK,CAAC,gCAAgC,EAACF,CAAC,CAAC;EAAA;AAAC,CAAC,EAAE,CAAC;AAC/5G,IAAIG,CAAC,GAAG,OAAOC,UAAU,GAAG,GAAG,GAAGA,UAAU,GAAG,OAAOC,MAAM,GAAG,GAAG,GAAGA,MAAM,GAAG,OAAOC,MAAM,GAAG,GAAG,GAAGA,MAAM,GAAG,OAAOC,IAAI,GAAG,GAAG,GAAGA,IAAI,GAAG,CAAC,CAAC;AAC1I,SAASC,CAACA,CAACC,CAAC,EAAE;EACZ,OAAOA,CAAC,IAAIA,CAAC,CAACC,UAAU,IAAIC,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACL,CAAC,EAAE,SAAS,CAAC,GAAGA,CAAC,CAACM,OAAO,GAAGN,CAAC;AAChG;AACA,CAAC,UAASA,CAAC,EAAE;EACX,IAAIO,CAAC,GAAG,SAAAA,CAAA,EAAW;MACjB,IAAI;QACF,OAAO,CAAC,CAACC,MAAM,CAACC,QAAQ;MAC1B,CAAC,CAAC,MAAM;QACN,OAAO,CAAC,CAAC;MACX;IACF,CAAC;IAAEC,CAAC,GAAGH,CAAC,CAAC,CAAC;IAAEI,CAAC,GAAG,SAAAA,CAASC,CAAC,EAAE;MAC1B,IAAI1B,CAAC,GAAG;QACN2B,IAAI,EAAE,SAAAA,CAAA,EAAW;UACf,IAAIC,CAAC,GAAGF,CAAC,CAACG,KAAK,CAAC,CAAC;UACjB,OAAO;YAAEC,IAAI,EAAEF,CAAC,KAAK,KAAK,CAAC;YAAEG,KAAK,EAAEH;UAAE,CAAC;QACzC;MACF,CAAC;MACD,OAAOJ,CAAC,KAAKxB,CAAC,CAACsB,MAAM,CAACC,QAAQ,CAAC,GAAG,YAAW;QAC3C,OAAOvB,CAAC;MACV,CAAC,CAAC,EAAEA,CAAC;IACP,CAAC;IAAEgC,CAAC,GAAG,SAAAA,CAASN,CAAC,EAAE;MACjB,OAAOO,kBAAkB,CAACP,CAAC,CAAC,CAACQ,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;IACnD,CAAC;IAAEC,CAAC,GAAG,SAAAA,CAAST,CAAC,EAAE;MACjB,OAAOU,kBAAkB,CAACC,MAAM,CAACX,CAAC,CAAC,CAACQ,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAC1D,CAAC;IAAEI,CAAC,GAAG,SAAAA,CAAA,EAAW;MAChB,IAAIZ,CAAC,GAAG,SAAAA,CAASE,CAAC,EAAE;UAClBZ,MAAM,CAACuB,cAAc,CAAC,IAAI,EAAE,UAAU,EAAE;YAAEC,QAAQ,EAAE,CAAC,CAAC;YAAET,KAAK,EAAE,CAAC;UAAE,CAAC,CAAC;UACpE,IAAIU,CAAC,GAAG,OAAOb,CAAC;UAChB,IAAIa,CAAC,KAAK,WAAW,EACnB,IAAIA,CAAC,KAAK,QAAQ,EAChBb,CAAC,KAAK,EAAE,IAAI,IAAI,CAACc,WAAW,CAACd,CAAC,CAAC,CAAC,KAC7B,IAAIA,CAAC,YAAYF,CAAC,EAAE;YACvB,IAAIiB,CAAC,GAAG,IAAI;YACZf,CAAC,CAACgB,OAAO,CAAC,UAASC,CAAC,EAAEC,CAAC,EAAE;cACvBH,CAAC,CAACI,MAAM,CAACD,CAAC,EAAED,CAAC,CAAC;YAChB,CAAC,CAAC;UACJ,CAAC,MAAM,IAAIjB,CAAC,KAAK,IAAI,IAAIa,CAAC,KAAK,QAAQ;YACrC,IAAIzB,MAAM,CAACC,SAAS,CAAC+B,QAAQ,CAAC7B,IAAI,CAACS,CAAC,CAAC,KAAK,gBAAgB,EACxD,KAAK,IAAIvB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuB,CAAC,CAACqB,MAAM,EAAE5C,CAAC,EAAE,EAAE;cACjC,IAAI6C,CAAC,GAAGtB,CAAC,CAACvB,CAAC,CAAC;cACZ,IAAIW,MAAM,CAACC,SAAS,CAAC+B,QAAQ,CAAC7B,IAAI,CAAC+B,CAAC,CAAC,KAAK,gBAAgB,IAAIA,CAAC,CAACD,MAAM,KAAK,CAAC,EAC1E,IAAI,CAACF,MAAM,CAACG,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAExB,MAAM,IAAIC,SAAS,CAAC,2CAA2C,GAAG9C,CAAC,GAAG,6BAA6B,CAAC;YACxG,CAAC,MAED,KAAK,IAAI+C,CAAC,IAAIxB,CAAC,EACbA,CAAC,CAACV,cAAc,CAACkC,CAAC,CAAC,IAAI,IAAI,CAACL,MAAM,CAACK,CAAC,EAAExB,CAAC,CAACwB,CAAC,CAAC,CAAC;UAAC,OAEhD,MAAM,IAAID,SAAS,CAAC,8CAA8C,CAAC;QACzE,CAAC;QAAEnD,CAAC,GAAG0B,CAAC,CAACT,SAAS;MAClBjB,CAAC,CAAC+C,MAAM,GAAG,UAASnB,CAAC,EAAEa,CAAC,EAAE;QACxBb,CAAC,IAAI,IAAI,CAACyB,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACzB,CAAC,CAAC,CAAC0B,IAAI,CAACjB,MAAM,CAACI,CAAC,CAAC,CAAC,GAAG,IAAI,CAACY,QAAQ,CAACzB,CAAC,CAAC,GAAG,CAACS,MAAM,CAACI,CAAC,CAAC,CAAC;MACxF,CAAC,EAAEzC,CAAC,CAACuD,MAAM,GAAG,UAAS3B,CAAC,EAAE;QACxB,OAAO,IAAI,CAACyB,QAAQ,CAACzB,CAAC,CAAC;MACzB,CAAC,EAAE5B,CAAC,CAACwD,GAAG,GAAG,UAAS5B,CAAC,EAAE;QACrB,OAAOA,CAAC,IAAI,IAAI,CAACyB,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACzB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;MACxD,CAAC,EAAE5B,CAAC,CAACyD,MAAM,GAAG,UAAS7B,CAAC,EAAE;QACxB,OAAOA,CAAC,IAAI,IAAI,CAACyB,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACzB,CAAC,CAAC,CAAC8B,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE;MAC5D,CAAC,EAAE1D,CAAC,CAAC2D,GAAG,GAAG,UAAS/B,CAAC,EAAE;QACrB,OAAOA,CAAC,IAAI,IAAI,CAACyB,QAAQ;MAC3B,CAAC,EAAErD,CAAC,CAAC4D,GAAG,GAAG,UAAShC,CAAC,EAAEa,CAAC,EAAE;QACxB,IAAI,CAACY,QAAQ,CAACzB,CAAC,CAAC,GAAG,CAACS,MAAM,CAACI,CAAC,CAAC,CAAC;MAChC,CAAC,EAAEzC,CAAC,CAAC4C,OAAO,GAAG,UAAShB,CAAC,EAAEa,CAAC,EAAE;QAC5B,IAAIE,CAAC;QACL,KAAK,IAAItC,CAAC,IAAI,IAAI,CAACgD,QAAQ,EACzB,IAAI,IAAI,CAACA,QAAQ,CAACnC,cAAc,CAACb,CAAC,CAAC,EAAE;UACnCsC,CAAC,GAAG,IAAI,CAACU,QAAQ,CAAChD,CAAC,CAAC;UACpB,KAAK,IAAI6C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,CAAC,CAACM,MAAM,EAAEC,CAAC,EAAE,EAC/BtB,CAAC,CAACT,IAAI,CAACsB,CAAC,EAAEE,CAAC,CAACO,CAAC,CAAC,EAAE7C,CAAC,EAAE,IAAI,CAAC;QAC5B;MACJ,CAAC,EAAEL,CAAC,CAAC6D,IAAI,GAAG,YAAW;QACrB,IAAIjC,CAAC,GAAG,EAAE;QACV,OAAO,IAAI,CAACgB,OAAO,CAAC,UAASH,CAAC,EAAEE,CAAC,EAAE;UACjCf,CAAC,CAAC0B,IAAI,CAACX,CAAC,CAAC;QACX,CAAC,CAAC,EAAElB,CAAC,CAACG,CAAC,CAAC;MACV,CAAC,EAAE5B,CAAC,CAAC8D,MAAM,GAAG,YAAW;QACvB,IAAIlC,CAAC,GAAG,EAAE;QACV,OAAO,IAAI,CAACgB,OAAO,CAAC,UAASH,CAAC,EAAE;UAC9Bb,CAAC,CAAC0B,IAAI,CAACb,CAAC,CAAC;QACX,CAAC,CAAC,EAAEhB,CAAC,CAACG,CAAC,CAAC;MACV,CAAC,EAAE5B,CAAC,CAAC+D,OAAO,GAAG,YAAW;QACxB,IAAInC,CAAC,GAAG,EAAE;QACV,OAAO,IAAI,CAACgB,OAAO,CAAC,UAASH,CAAC,EAAEE,CAAC,EAAE;UACjCf,CAAC,CAAC0B,IAAI,CAAC,CAACX,CAAC,EAAEF,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,EAAEhB,CAAC,CAACG,CAAC,CAAC;MACV,CAAC,EAAEJ,CAAC,KAAKxB,CAAC,CAACsB,MAAM,CAACC,QAAQ,CAAC,GAAGvB,CAAC,CAAC+D,OAAO,CAAC,EAAE/D,CAAC,CAACgD,QAAQ,GAAG,YAAW;QAChE,IAAIpB,CAAC,GAAG,EAAE;QACV,OAAO,IAAI,CAACgB,OAAO,CAAC,UAASH,CAAC,EAAEE,CAAC,EAAE;UACjCf,CAAC,CAAC0B,IAAI,CAACtB,CAAC,CAACW,CAAC,CAAC,GAAG,GAAG,GAAGX,CAAC,CAACS,CAAC,CAAC,CAAC;QAC3B,CAAC,CAAC,EAAEb,CAAC,CAACoC,IAAI,CAAC,GAAG,CAAC;MACjB,CAAC,EAAElD,CAAC,CAACmD,eAAe,GAAGvC,CAAC;IAC1B,CAAC;IAAEwC,CAAC,GAAG,SAAAA,CAAA,EAAW;MAChB,IAAI;QACF,IAAIxC,CAAC,GAAGZ,CAAC,CAACmD,eAAe;QACzB,OAAO,IAAIvC,CAAC,CAAC,MAAM,CAAC,CAACsB,QAAQ,CAAC,CAAC,KAAK,KAAK,IAAI,OAAOtB,CAAC,CAACT,SAAS,CAAC2C,GAAG,IAAI,UAAU;MACnF,CAAC,CAAC,MAAM;QACN,OAAO,CAAC,CAAC;MACX;IACF,CAAC;EACDM,CAAC,CAAC,CAAC,IAAI5B,CAAC,CAAC,CAAC;EACV,IAAI6B,CAAC,GAAGrD,CAAC,CAACmD,eAAe,CAAChD,SAAS;EACnC,OAAOkD,CAAC,CAACC,IAAI,IAAI,UAAU,KAAKD,CAAC,CAACC,IAAI,GAAG,YAAW;IAClD,IAAI1C,CAAC,GAAG,IAAI;MAAE1B,CAAC,GAAG,EAAE;IACpB,IAAI,CAAC4C,OAAO,CAAC,UAASH,CAAC,EAAEE,CAAC,EAAE;MAC1B3C,CAAC,CAACsD,IAAI,CAAC,CAACX,CAAC,EAAEF,CAAC,CAAC,CAAC,EAAEf,CAAC,CAAC2B,QAAQ,IAAI3B,CAAC,CAAC6B,MAAM,CAACZ,CAAC,CAAC;IAC3C,CAAC,CAAC,EAAE3C,CAAC,CAACoE,IAAI,CAAC,UAAS3B,CAAC,EAAEE,CAAC,EAAE;MACxB,OAAOF,CAAC,CAAC,CAAC,CAAC,GAAGE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAGF,CAAC,CAAC,CAAC,CAAC,GAAGE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;IAC/C,CAAC,CAAC,EAAEjB,CAAC,CAAC2B,QAAQ,KAAK3B,CAAC,CAAC2B,QAAQ,GAAG,CAAC,CAAC,CAAC;IACnC,KAAK,IAAIzB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5B,CAAC,CAACiD,MAAM,EAAErB,CAAC,EAAE,EAC/B,IAAI,CAACmB,MAAM,CAAC/C,CAAC,CAAC4B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE5B,CAAC,CAAC4B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjC,CAAC,CAAC,EAAE,OAAOuC,CAAC,CAACzB,WAAW,IAAI,UAAU,IAAI1B,MAAM,CAACuB,cAAc,CAAC4B,CAAC,EAAE,aAAa,EAAE;IAChFE,UAAU,EAAE,CAAC,CAAC;IACdC,YAAY,EAAE,CAAC,CAAC;IAChB9B,QAAQ,EAAE,CAAC,CAAC;IACZT,KAAK,EAAE,SAAAA,CAASL,CAAC,EAAE;MACjB,IAAI,IAAI,CAAC2B,QAAQ,EACf,IAAI,CAACA,QAAQ,GAAG,CAAC,CAAC,CAAC,KAChB;QACH,IAAIrD,CAAC,GAAG,EAAE;QACV,IAAI,CAAC4C,OAAO,CAAC,UAASvC,CAAC,EAAE6C,CAAC,EAAE;UAC1BlD,CAAC,CAACsD,IAAI,CAACJ,CAAC,CAAC;QACX,CAAC,CAAC;QACF,KAAK,IAAItB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5B,CAAC,CAACiD,MAAM,EAAErB,CAAC,EAAE,EAC/B,IAAI,CAAC2B,MAAM,CAACvD,CAAC,CAAC4B,CAAC,CAAC,CAAC;MACrB;MACAF,CAAC,GAAGA,CAAC,CAACQ,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;MACxB,KAAK,IAAIO,CAAC,GAAGf,CAAC,CAAC6C,KAAK,CAAC,GAAG,CAAC,EAAE5B,CAAC,EAAEf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGa,CAAC,CAACQ,MAAM,EAAErB,CAAC,EAAE,EACpDe,CAAC,GAAGF,CAAC,CAACb,CAAC,CAAC,CAAC2C,KAAK,CAAC,GAAG,CAAC,EAAE,IAAI,CAACxB,MAAM,CAC9BZ,CAAC,CAACQ,CAAC,CAAC,CAAC,CAAC,CAAC,EACPA,CAAC,CAACM,MAAM,GAAG,CAAC,GAAGd,CAAC,CAACQ,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAC3B,CAAC;IACL;EACF,CAAC,CAAC;AACJ,CAAC,EACC,OAAOnC,CAAC,GAAG,GAAG,GAAGA,CAAC,GAAG,OAAOE,MAAM,GAAG,GAAG,GAAGA,MAAM,GAAG,OAAOE,IAAI,GAAG,GAAG,GAAGA,IAAI,GAAGJ,CACjF,CAAC;AACD,CAAC,UAASM,CAAC,EAAE;EACX,IAAIO,CAAC,GAAG,SAAAA,CAAA,EAAW;MACjB,IAAI;QACF,IAAIW,CAAC,GAAG,IAAIlB,CAAC,CAAC0D,GAAG,CAAC,GAAG,EAAE,UAAU,CAAC;QAClC,OAAOxC,CAAC,CAACyC,QAAQ,GAAG,KAAK,EAAEzC,CAAC,CAAC0C,IAAI,KAAK,gBAAgB,IAAI1C,CAAC,CAAC2C,YAAY;MAC1E,CAAC,CAAC,MAAM;QACN,OAAO,CAAC,CAAC;MACX;IACF,CAAC;IAAEnD,CAAC,GAAG,SAAAA,CAAA,EAAW;MAChB,IAAIQ,CAAC,GAAGlB,CAAC,CAAC0D,GAAG;QAAErC,CAAC,GAAG,SAAAA,CAASgC,CAAC,EAAEzC,CAAC,EAAE;UAChC,OAAOyC,CAAC,IAAI,QAAQ,KAAKA,CAAC,GAAG9B,MAAM,CAAC8B,CAAC,CAAC,CAAC;UACvC,IAAInE,CAAC,GAAGD,QAAQ;YAAE6B,CAAC;UACnB,IAAIF,CAAC,KAAKZ,CAAC,CAAC8D,QAAQ,KAAK,KAAK,CAAC,IAAIlD,CAAC,KAAKZ,CAAC,CAAC8D,QAAQ,CAACF,IAAI,CAAC,EAAE;YACzD1E,CAAC,GAAGD,QAAQ,CAAC8E,cAAc,CAACC,kBAAkB,CAAC,EAAE,CAAC,EAAElD,CAAC,GAAG5B,CAAC,CAACC,aAAa,CAAC,MAAM,CAAC,EAAE2B,CAAC,CAAC8C,IAAI,GAAGhD,CAAC,EAAE1B,CAAC,CAACI,IAAI,CAACF,WAAW,CAAC0B,CAAC,CAAC;YAClH,IAAI;cACF,IAAIA,CAAC,CAAC8C,IAAI,CAACK,OAAO,CAACrD,CAAC,CAAC,KAAK,CAAC,EACzB,MAAM,IAAIsD,KAAK,CAACpD,CAAC,CAAC8C,IAAI,CAAC;YAC3B,CAAC,CAAC,OAAOO,CAAC,EAAE;cACV,MAAM,IAAID,KAAK,CAAC,yBAAyB,GAAGtD,CAAC,GAAG,UAAU,GAAGuD,CAAC,CAAC;YACjE;UACF;UACA,IAAIxC,CAAC,GAAGzC,CAAC,CAACC,aAAa,CAAC,GAAG,CAAC;UAC5BwC,CAAC,CAACiC,IAAI,GAAGP,CAAC,EAAEvC,CAAC,KAAK5B,CAAC,CAACkF,IAAI,CAAChF,WAAW,CAACuC,CAAC,CAAC,EAAEA,CAAC,CAACiC,IAAI,GAAGjC,CAAC,CAACiC,IAAI,CAAC;UACzD,IAAI/B,CAAC,GAAG3C,CAAC,CAACC,aAAa,CAAC,OAAO,CAAC;UAChC,IAAI0C,CAAC,CAACwC,IAAI,GAAG,KAAK,EAAExC,CAAC,CAACZ,KAAK,GAAGoC,CAAC,EAAE1B,CAAC,CAAC2C,QAAQ,KAAK,GAAG,IAAI,CAAC,GAAG,CAACC,IAAI,CAAC5C,CAAC,CAACiC,IAAI,CAAC,IAAI,CAAC/B,CAAC,CAAC2C,aAAa,CAAC,CAAC,IAAI,CAAC5D,CAAC,EAClG,MAAM,IAAIyB,SAAS,CAAC,aAAa,CAAC;UACpCnC,MAAM,CAACuB,cAAc,CAAC,IAAI,EAAE,gBAAgB,EAAE;YAC5CR,KAAK,EAAEU;UACT,CAAC,CAAC;UACF,IAAIpC,CAAC,GAAG,IAAIS,CAAC,CAACmD,eAAe,CAAC,IAAI,CAACsB,MAAM,CAAC;YAAErC,CAAC,GAAG,CAAC,CAAC;YAAEE,CAAC,GAAG,CAAC,CAAC;YAAEP,CAAC,GAAG,IAAI;UACpE,CAAC,QAAQ,EAAE,QAAQ,EAAE,KAAK,CAAC,CAACD,OAAO,CAAC,UAASqC,CAAC,EAAE;YAC9C,IAAIO,CAAC,GAAGnF,CAAC,CAAC4E,CAAC,CAAC;YACZ5E,CAAC,CAAC4E,CAAC,CAAC,GAAG,YAAW;cAChBO,CAAC,CAACC,KAAK,CAACpF,CAAC,EAAEqF,SAAS,CAAC,EAAExC,CAAC,KAAKE,CAAC,GAAG,CAAC,CAAC,EAAEP,CAAC,CAAC0C,MAAM,GAAGlF,CAAC,CAAC2C,QAAQ,CAAC,CAAC,EAAEI,CAAC,GAAG,CAAC,CAAC,CAAC;YACvE,CAAC;UACH,CAAC,CAAC,EAAEpC,MAAM,CAACuB,cAAc,CAAC,IAAI,EAAE,cAAc,EAAE;YAC9CR,KAAK,EAAE1B,CAAC;YACRgE,UAAU,EAAE,CAAC;UACf,CAAC,CAAC;UACF,IAAIvB,CAAC,GAAG,KAAK,CAAC;UACd9B,MAAM,CAACuB,cAAc,CAAC,IAAI,EAAE,qBAAqB,EAAE;YACjD8B,UAAU,EAAE,CAAC,CAAC;YACdC,YAAY,EAAE,CAAC,CAAC;YAChB9B,QAAQ,EAAE,CAAC,CAAC;YACZT,KAAK,EAAE,SAAAA,CAAA,EAAW;cAChB,IAAI,CAACwD,MAAM,KAAKzC,CAAC,KAAKA,CAAC,GAAG,IAAI,CAACyC,MAAM,EAAEnC,CAAC,KAAKF,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAACyB,YAAY,CAACjC,WAAW,CAAC,IAAI,CAAC6C,MAAM,CAAC,EAAErC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC3G;UACF,CAAC,CAAC;QACJ,CAAC;QAAEZ,CAAC,GAAGH,CAAC,CAAClB,SAAS;QAAEiD,CAAC,GAAG,SAAAA,CAASC,CAAC,EAAE;UAClCnD,MAAM,CAACuB,cAAc,CAACD,CAAC,EAAE6B,CAAC,EAAE;YAC1BX,GAAG,EAAE,SAAAA,CAAA,EAAW;cACd,OAAO,IAAI,CAACmC,cAAc,CAACxB,CAAC,CAAC;YAC/B,CAAC;YACDP,GAAG,EAAE,SAAAA,CAASlC,CAAC,EAAE;cACf,IAAI,CAACiE,cAAc,CAACxB,CAAC,CAAC,GAAGzC,CAAC;YAC5B,CAAC;YACD2C,UAAU,EAAE,CAAC;UACf,CAAC,CAAC;QACJ,CAAC;MACD,CAAC,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,UAAU,CAAC,CAACzB,OAAO,CAAC,UAASuB,CAAC,EAAE;QACnED,CAAC,CAACC,CAAC,CAAC;MACN,CAAC,CAAC,EAAEnD,MAAM,CAACuB,cAAc,CAACD,CAAC,EAAE,QAAQ,EAAE;QACrCkB,GAAG,EAAE,SAAAA,CAAA,EAAW;UACd,OAAO,IAAI,CAACmC,cAAc,CAACJ,MAAM;QACnC,CAAC;QACD3B,GAAG,EAAE,SAAAA,CAASO,CAAC,EAAE;UACf,IAAI,CAACwB,cAAc,CAACJ,MAAM,GAAGpB,CAAC,EAAE,IAAI,CAACyB,mBAAmB,CAAC,CAAC;QAC5D,CAAC;QACDvB,UAAU,EAAE,CAAC;MACf,CAAC,CAAC,EAAErD,MAAM,CAAC6E,gBAAgB,CAACvD,CAAC,EAAE;QAC7BU,QAAQ,EAAE;UACRQ,GAAG,EAAE,SAAAA,CAAA,EAAW;YACd,IAAIW,CAAC,GAAG,IAAI;YACZ,OAAO,YAAW;cAChB,OAAOA,CAAC,CAACO,IAAI;YACf,CAAC;UACH;QACF,CAAC;QACDA,IAAI,EAAE;UACJlB,GAAG,EAAE,SAAAA,CAAA,EAAW;YACd,OAAO,IAAI,CAACmC,cAAc,CAACjB,IAAI,CAACxC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;UACpD,CAAC;UACD0B,GAAG,EAAE,SAAAA,CAASO,CAAC,EAAE;YACf,IAAI,CAACwB,cAAc,CAACjB,IAAI,GAAGP,CAAC,EAAE,IAAI,CAACyB,mBAAmB,CAAC,CAAC;UAC1D,CAAC;UACDvB,UAAU,EAAE,CAAC;QACf,CAAC;QACDI,QAAQ,EAAE;UACRjB,GAAG,EAAE,SAAAA,CAAA,EAAW;YACd,OAAO,IAAI,CAACmC,cAAc,CAAClB,QAAQ,CAACvC,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC;UAC5D,CAAC;UACD0B,GAAG,EAAE,SAAAA,CAASO,CAAC,EAAE;YACf,IAAI,CAACwB,cAAc,CAAClB,QAAQ,GAAGN,CAAC;UAClC,CAAC;UACDE,UAAU,EAAE,CAAC;QACf,CAAC;QACDyB,MAAM,EAAE;UACNtC,GAAG,EAAE,SAAAA,CAAA,EAAW;YACd,IAAIW,CAAC,GAAG;gBAAE,OAAO,EAAE,EAAE;gBAAE,QAAQ,EAAE,GAAG;gBAAE,MAAM,EAAE;cAAG,CAAC,CAAC,IAAI,CAACwB,cAAc,CAACP,QAAQ,CAAC;cAAE1D,CAAC,GAAG,IAAI,CAACiE,cAAc,CAACI,IAAI,IAAI5B,CAAC,IAAI,IAAI,CAACwB,cAAc,CAACI,IAAI,KAAK,EAAE;YACtJ,OAAO,IAAI,CAACJ,cAAc,CAACP,QAAQ,GAAG,IAAI,GAAG,IAAI,CAACO,cAAc,CAACK,QAAQ,IAAItE,CAAC,GAAG,GAAG,GAAG,IAAI,CAACiE,cAAc,CAACI,IAAI,GAAG,EAAE,CAAC;UACvH,CAAC;UACD1B,UAAU,EAAE,CAAC;QACf,CAAC;QACD4B,QAAQ,EAAE;UACR;UACAzC,GAAG,EAAE,SAAAA,CAAA,EAAW;YACd,OAAO,EAAE;UACX,CAAC;UACDI,GAAG,EAAE,SAAAA,CAASO,CAAC,EAAE,CACjB,CAAC;UACDE,UAAU,EAAE,CAAC;QACf,CAAC;QACD6B,QAAQ,EAAE;UACR;UACA1C,GAAG,EAAE,SAAAA,CAAA,EAAW;YACd,OAAO,EAAE;UACX,CAAC;UACDI,GAAG,EAAE,SAAAA,CAASO,CAAC,EAAE,CACjB,CAAC;UACDE,UAAU,EAAE,CAAC;QACf;MACF,CAAC,CAAC,EAAElC,CAAC,CAACgE,eAAe,GAAG,UAAShC,CAAC,EAAE;QAClC,OAAOnC,CAAC,CAACmE,eAAe,CAACV,KAAK,CAACzD,CAAC,EAAE0D,SAAS,CAAC;MAC9C,CAAC,EAAEvD,CAAC,CAACiE,eAAe,GAAG,UAASjC,CAAC,EAAE;QACjC,OAAOnC,CAAC,CAACoE,eAAe,CAACX,KAAK,CAACzD,CAAC,EAAE0D,SAAS,CAAC;MAC9C,CAAC,EAAE5E,CAAC,CAAC0D,GAAG,GAAGrC,CAAC;IACd,CAAC;EACD,IAAId,CAAC,CAAC,CAAC,IAAIG,CAAC,CAAC,CAAC,EAAEV,CAAC,CAAC8D,QAAQ,KAAK,KAAK,CAAC,IAAI,EAAE,QAAQ,IAAI9D,CAAC,CAAC8D,QAAQ,CAAC,EAAE;IAClE,IAAInD,CAAC,GAAG,SAAAA,CAAA,EAAW;MACjB,OAAOX,CAAC,CAAC8D,QAAQ,CAACQ,QAAQ,GAAG,IAAI,GAAGtE,CAAC,CAAC8D,QAAQ,CAACoB,QAAQ,IAAIlF,CAAC,CAAC8D,QAAQ,CAACmB,IAAI,GAAG,GAAG,GAAGjF,CAAC,CAAC8D,QAAQ,CAACmB,IAAI,GAAG,EAAE,CAAC;IAC1G,CAAC;IACD,IAAI;MACF/E,MAAM,CAACuB,cAAc,CAACzB,CAAC,CAAC8D,QAAQ,EAAE,QAAQ,EAAE;QAC1CpB,GAAG,EAAE/B,CAAC;QACN4C,UAAU,EAAE,CAAC;MACf,CAAC,CAAC;IACJ,CAAC,CAAC,MAAM;MACNgC,WAAW,CAAC,YAAW;QACrBvF,CAAC,CAAC8D,QAAQ,CAACkB,MAAM,GAAGrE,CAAC,CAAC,CAAC;MACzB,CAAC,EAAE,GAAG,CAAC;IACT;EACF;AACF,CAAC,EACC,OAAOjB,CAAC,GAAG,GAAG,GAAGA,CAAC,GAAG,OAAOE,MAAM,GAAG,GAAG,GAAGA,MAAM,GAAG,OAAOE,IAAI,GAAG,GAAG,GAAGA,IAAI,GAAGJ,CACjF,CAAC;AACD,IAAI8F,CAAC,GAAG;EAAEC,OAAO,EAAE,CAAC;AAAE,CAAC;AACvB,CAAC,UAASzF,CAAC,EAAEO,CAAC,EAAE;EACd,CAAC,UAASG,CAAC,EAAEC,CAAC,EAAE;IACdX,CAAC,CAACyF,OAAO,GAAG9E,CAAC,CAAC,CAAC;EACjB,CAAC,EAAEf,MAAM,EAAE,YAAW;IACpB,OAAO,UAASc,CAAC,EAAE;MACjB,IAAIC,CAAC,GAAG,CAAC,CAAC;MACV,SAASO,CAACA,CAACG,CAAC,EAAE;QACZ,IAAIV,CAAC,CAACU,CAAC,CAAC,EACN,OAAOV,CAAC,CAACU,CAAC,CAAC,CAACoE,OAAO;QACrB,IAAIjE,CAAC,GAAGb,CAAC,CAACU,CAAC,CAAC,GAAG;UAAEA,CAAC;UAAEgC,CAAC,EAAE,CAAC,CAAC;UAAEoC,OAAO,EAAE,CAAC;QAAE,CAAC;QACxC,OAAO/E,CAAC,CAACW,CAAC,CAAC,CAAChB,IAAI,CAACmB,CAAC,CAACiE,OAAO,EAAEjE,CAAC,EAAEA,CAAC,CAACiE,OAAO,EAAEvE,CAAC,CAAC,EAAEM,CAAC,CAAC6B,CAAC,GAAG,CAAC,CAAC,EAAE7B,CAAC,CAACiE,OAAO;MACnE;MACA,OAAOvE,CAAC,CAACiD,CAAC,GAAGzD,CAAC,EAAEQ,CAAC,CAACA,CAAC,GAAGP,CAAC,EAAEO,CAAC,CAACR,CAAC,GAAG,UAASW,CAAC,EAAEG,CAAC,EAAE4B,CAAC,EAAE;QAC/ClC,CAAC,CAAChC,CAAC,CAACmC,CAAC,EAAEG,CAAC,CAAC,IAAItB,MAAM,CAACuB,cAAc,CAACJ,CAAC,EAAEG,CAAC,EAAE;UAAE+B,UAAU,EAAE,CAAC,CAAC;UAAEb,GAAG,EAAEU;QAAE,CAAC,CAAC;MACtE,CAAC,EAAElC,CAAC,CAACoB,CAAC,GAAG,UAASjB,CAAC,EAAE;QACnB,OAAOb,MAAM,GAAG,GAAG,IAAIA,MAAM,CAACkF,WAAW,IAAIxF,MAAM,CAACuB,cAAc,CAACJ,CAAC,EAAEb,MAAM,CAACkF,WAAW,EAAE;UAAEzE,KAAK,EAAE;QAAS,CAAC,CAAC,EAAEf,MAAM,CAACuB,cAAc,CAACJ,CAAC,EAAE,YAAY,EAAE;UAAEJ,KAAK,EAAE,CAAC;QAAE,CAAC,CAAC;MACvK,CAAC,EAAEC,CAAC,CAAC3B,CAAC,GAAG,UAAS8B,CAAC,EAAEG,CAAC,EAAE;QACtB,IAAI,CAAC,GAAGA,CAAC,KAAKH,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAGG,CAAC,IAAI,CAAC,GAAGA,CAAC,IAAI,OAAOH,CAAC,IAAI,QAAQ,IAAIA,CAAC,IAAIA,CAAC,CAACpB,UAAU,EAClF,OAAOoB,CAAC;QACV,IAAI+B,CAAC,GAAG,eAAgBlD,MAAM,CAACyF,MAAM,CAAC,IAAI,CAAC;QAC3C,IAAIzE,CAAC,CAACoB,CAAC,CAACc,CAAC,CAAC,EAAElD,MAAM,CAACuB,cAAc,CAAC2B,CAAC,EAAE,SAAS,EAAE;UAAEG,UAAU,EAAE,CAAC,CAAC;UAAEtC,KAAK,EAAEI;QAAE,CAAC,CAAC,EAAE,CAAC,GAAGG,CAAC,IAAI,OAAOH,CAAC,IAAI,QAAQ,EAC1G,KAAK,IAAIgC,CAAC,IAAIhC,CAAC,EACbH,CAAC,CAACR,CAAC,CAAC0C,CAAC,EAAEC,CAAC,EAAG,UAASzC,CAAC,EAAE;UACrB,OAAOS,CAAC,CAACT,CAAC,CAAC;QACb,CAAC,CAAEgF,IAAI,CAAC,IAAI,EAAEvC,CAAC,CAAC,CAAC;QACrB,OAAOD,CAAC;MACV,CAAC,EAAElC,CAAC,CAACN,CAAC,GAAG,UAASS,CAAC,EAAE;QACnB,IAAIG,CAAC,GAAGH,CAAC,IAAIA,CAAC,CAACpB,UAAU,GAAG,YAAW;UACrC,OAAOoB,CAAC,CAACf,OAAO;QAClB,CAAC,GAAG,YAAW;UACb,OAAOe,CAAC;QACV,CAAC;QACD,OAAOH,CAAC,CAACR,CAAC,CAACc,CAAC,EAAE,GAAG,EAAEA,CAAC,CAAC,EAAEA,CAAC;MAC1B,CAAC,EAAEN,CAAC,CAAChC,CAAC,GAAG,UAASmC,CAAC,EAAEG,CAAC,EAAE;QACtB,OAAOtB,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACgB,CAAC,EAAEG,CAAC,CAAC;MACnD,CAAC,EAAEN,CAAC,CAACkC,CAAC,GAAG,EAAE,EAAElC,CAAC,CAACA,CAAC,CAACS,CAAC,GAAG,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC,UAASjB,CAAC,EAAEC,CAAC,EAAE;MAChB,IAAIO,CAAC;MACLA,CAAC,GAAG,YAAW;QACb,OAAO,IAAI;MACb,CAAC,CAAC,CAAC;MACH,IAAI;QACFA,CAAC,GAAGA,CAAC,IAAI,IAAI2E,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC;MACxC,CAAC,CAAC,MAAM;QACN,OAAOjG,MAAM,IAAI,QAAQ,KAAKsB,CAAC,GAAGtB,MAAM,CAAC;MAC3C;MACAc,CAAC,CAAC+E,OAAO,GAAGvE,CAAC;IACf,CAAC,EAAE,UAASR,CAAC,EAAEC,CAAC,EAAEO,CAAC,EAAE;MACnB,CAAC,UAASG,CAAC,EAAE;QACX,IAAIG,CAAC,GAAGN,CAAC,CAAC,CAAC,CAAC;UAAEkC,CAAC,GAAG0C,UAAU;QAC5B,SAASzC,CAACA,CAAA,EAAG,CACb;QACA,SAASzC,CAACA,CAAC0B,CAAC,EAAE;UACZ,IAAI,EAAE,IAAI,YAAY1B,CAAC,CAAC,EACtB,MAAM,IAAIyB,SAAS,CAAC,sCAAsC,CAAC;UAC7D,IAAI,OAAOC,CAAC,IAAI,UAAU,EACxB,MAAM,IAAID,SAAS,CAAC,gBAAgB,CAAC;UACvC,IAAI,CAAC0D,MAAM,GAAG,CAAC,EAAE,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAC,EAAE,IAAI,CAACC,MAAM,GAAG,KAAK,CAAC,EAAE,IAAI,CAACC,UAAU,GAAG,EAAE,EAAE9D,CAAC,CAACE,CAAC,EAAE,IAAI,CAAC;QAC7F;QACA,SAASpD,CAACA,CAACoD,CAAC,EAAEP,CAAC,EAAE;UACf,OAAOO,CAAC,CAACyD,MAAM,KAAK,CAAC,GACnBzD,CAAC,GAAGA,CAAC,CAAC2D,MAAM;UACd3D,CAAC,CAACyD,MAAM,KAAK,CAAC,IAAIzD,CAAC,CAAC0D,QAAQ,GAAG,CAAC,CAAC,EAAEpF,CAAC,CAACuF,YAAY,CAAC,YAAW;YAC3D,IAAInE,CAAC,GAAGM,CAAC,CAACyD,MAAM,KAAK,CAAC,GAAGhE,CAAC,CAACqE,WAAW,GAAGrE,CAAC,CAACsE,UAAU;YACrD,IAAIrE,CAAC,KAAK,IAAI,EAAE;cACd,IAAImC,CAAC;cACL,IAAI;gBACFA,CAAC,GAAGnC,CAAC,CAACM,CAAC,CAAC2D,MAAM,CAAC;cACjB,CAAC,CAAC,OAAOvB,CAAC,EAAE;gBACV,OAAO,KAAK/C,CAAC,CAACI,CAAC,CAACuE,OAAO,EAAE5B,CAAC,CAAC;cAC7B;cACA5D,CAAC,CAACiB,CAAC,CAACuE,OAAO,EAAEnC,CAAC,CAAC;YACjB,CAAC,MACC,CAAC7B,CAAC,CAACyD,MAAM,KAAK,CAAC,GAAGjF,CAAC,GAAGa,CAAC,EAAEI,CAAC,CAACuE,OAAO,EAAEhE,CAAC,CAAC2D,MAAM,CAAC;UACjD,CAAC,CAAC,IAAI3D,CAAC,CAAC4D,UAAU,CAAC1D,IAAI,CAACT,CAAC,CAAC;QAC5B;QACA,SAASjB,CAACA,CAACwB,CAAC,EAAEP,CAAC,EAAE;UACf,IAAI;YACF,IAAIA,CAAC,KAAKO,CAAC,EACT,MAAM,IAAID,SAAS,CAAC,2CAA2C,CAAC;YAClE,IAAIN,CAAC,KAAK,OAAOA,CAAC,IAAI,QAAQ,IAAI,OAAOA,CAAC,IAAI,UAAU,CAAC,EAAE;cACzD,IAAIC,CAAC,GAAGD,CAAC,CAACwE,IAAI;cACd,IAAIxE,CAAC,YAAYnB,CAAC,EAChB,OAAO0B,CAAC,CAACyD,MAAM,GAAG,CAAC,EAAEzD,CAAC,CAAC2D,MAAM,GAAGlE,CAAC,EAAE,KAAKF,CAAC,CAACS,CAAC,CAAC;cAC9C,IAAI,OAAON,CAAC,IAAI,UAAU,EACxB,OAAO,KAAKI,CAAC,EAAE+B,CAAC,GAAGnC,CAAC,EAAE0C,CAAC,GAAG3C,CAAC,EAAE,YAAW;gBACtCoC,CAAC,CAACQ,KAAK,CAACD,CAAC,EAAEE,SAAS,CAAC;cACvB,CAAC,GAAGtC,CAAC,CAAC;YACV;YACAA,CAAC,CAACyD,MAAM,GAAG,CAAC,EAAEzD,CAAC,CAAC2D,MAAM,GAAGlE,CAAC,EAAEF,CAAC,CAACS,CAAC,CAAC;UAClC,CAAC,CAAC,OAAOkE,CAAC,EAAE;YACV7E,CAAC,CAACW,CAAC,EAAEkE,CAAC,CAAC;UACT;UACA,IAAIrC,CAAC,EAAEO,CAAC;QACV;QACA,SAAS/C,CAACA,CAACW,CAAC,EAAEP,CAAC,EAAE;UACfO,CAAC,CAACyD,MAAM,GAAG,CAAC,EAAEzD,CAAC,CAAC2D,MAAM,GAAGlE,CAAC,EAAEF,CAAC,CAACS,CAAC,CAAC;QAClC;QACA,SAAST,CAACA,CAACS,CAAC,EAAE;UACZA,CAAC,CAACyD,MAAM,KAAK,CAAC,IAAIzD,CAAC,CAAC4D,UAAU,CAAC/D,MAAM,KAAK,CAAC,IAAIvB,CAAC,CAACuF,YAAY,CAAC,YAAW;YACvE7D,CAAC,CAAC0D,QAAQ,IAAIpF,CAAC,CAAC6F,qBAAqB,CAACnE,CAAC,CAAC2D,MAAM,CAAC;UACjD,CAAC,CAAC;UACF,KAAK,IAAIlE,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGM,CAAC,CAAC4D,UAAU,CAAC/D,MAAM,EAAEJ,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EACjD7C,CAAC,CAACoD,CAAC,EAAEA,CAAC,CAAC4D,UAAU,CAACnE,CAAC,CAAC,CAAC;UACvBO,CAAC,CAAC4D,UAAU,GAAG,IAAI;QACrB;QACA,SAAS3G,CAACA,CAAC+C,CAAC,EAAEP,CAAC,EAAEC,CAAC,EAAE;UAClB,IAAI,CAACoE,WAAW,GAAG,OAAO9D,CAAC,IAAI,UAAU,GAAGA,CAAC,GAAG,IAAI,EAAE,IAAI,CAAC+D,UAAU,GAAG,OAAOtE,CAAC,IAAI,UAAU,GAAGA,CAAC,GAAG,IAAI,EAAE,IAAI,CAACuE,OAAO,GAAGtE,CAAC;QAC7H;QACA,SAASI,CAACA,CAACE,CAAC,EAAEP,CAAC,EAAE;UACf,IAAIC,CAAC,GAAG,CAAC,CAAC;UACV,IAAI;YACFM,CAAC,CAAC,UAAS6B,CAAC,EAAE;cACZnC,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC,EAAElB,CAAC,CAACiB,CAAC,EAAEoC,CAAC,CAAC,CAAC;YACxB,CAAC,EAAE,UAASA,CAAC,EAAE;cACbnC,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC,EAAEL,CAAC,CAACI,CAAC,EAAEoC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC;UACJ,CAAC,CAAC,OAAOA,CAAC,EAAE;YACV,IAAInC,CAAC,EACH;YACFA,CAAC,GAAG,CAAC,CAAC,EAAEL,CAAC,CAACI,CAAC,EAAEoC,CAAC,CAAC;UACjB;QACF;QACAvD,CAAC,CAACT,SAAS,CAACuG,KAAK,GAAG,UAASpE,CAAC,EAAE;UAC9B,OAAO,IAAI,CAACiE,IAAI,CAAC,IAAI,EAAEjE,CAAC,CAAC;QAC3B,CAAC,EAAE1B,CAAC,CAACT,SAAS,CAACoG,IAAI,GAAG,UAASjE,CAAC,EAAEP,CAAC,EAAE;UACnC,IAAIC,CAAC,GAAG,IAAI,IAAI,CAAC2E,WAAW,CAACtD,CAAC,CAAC;UAC/B,OAAOnE,CAAC,CAAC,IAAI,EAAE,IAAIK,CAAC,CAAC+C,CAAC,EAAEP,CAAC,EAAEC,CAAC,CAAC,CAAC,EAAEA,CAAC;QACnC,CAAC,EAAEpB,CAAC,CAACT,SAAS,CAACyG,OAAO,GAAGpF,CAAC,CAACA,CAAC,EAAEZ,CAAC,CAACiG,GAAG,GAAG,UAASvE,CAAC,EAAE;UAChD,OAAO,IAAI1B,CAAC,CAAC,UAASmB,CAAC,EAAEC,CAAC,EAAE;YAC1B,IAAI,CAACM,CAAC,IAAIA,CAAC,CAACH,MAAM,KAAK,KAAK,CAAC,EAC3B,MAAM,IAAIE,SAAS,CAAC,8BAA8B,CAAC;YACrD,IAAI8B,CAAC,GAAG2C,KAAK,CAAC3G,SAAS,CAACyC,KAAK,CAACvC,IAAI,CAACiC,CAAC,CAAC;YACrC,IAAI6B,CAAC,CAAChC,MAAM,KAAK,CAAC,EAChB,OAAOJ,CAAC,CAAC,EAAE,CAAC;YACd,IAAI2C,CAAC,GAAGP,CAAC,CAAChC,MAAM;YAChB,SAASqE,CAACA,CAACO,CAAC,EAAEC,CAAC,EAAE;cACf,IAAI;gBACF,IAAIA,CAAC,KAAK,OAAOA,CAAC,IAAI,QAAQ,IAAI,OAAOA,CAAC,IAAI,UAAU,CAAC,EAAE;kBACzD,IAAIC,CAAC,GAAGD,CAAC,CAACT,IAAI;kBACd,IAAI,OAAOU,CAAC,IAAI,UAAU,EACxB,OAAO,KAAKA,CAAC,CAAC5G,IAAI,CAAC2G,CAAC,EAAE,UAASE,CAAC,EAAE;oBAChCV,CAAC,CAACO,CAAC,EAAEG,CAAC,CAAC;kBACT,CAAC,EAAElF,CAAC,CAAC;gBACT;gBACAmC,CAAC,CAAC4C,CAAC,CAAC,GAAGC,CAAC,EAAE,EAAEtC,CAAC,IAAI,CAAC,IAAI3C,CAAC,CAACoC,CAAC,CAAC;cAC5B,CAAC,CAAC,OAAO+C,CAAC,EAAE;gBACVlF,CAAC,CAACkF,CAAC,CAAC;cACN;YACF;YACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhD,CAAC,CAAChC,MAAM,EAAEgF,CAAC,EAAE,EAC/BX,CAAC,CAACW,CAAC,EAAEhD,CAAC,CAACgD,CAAC,CAAC,CAAC;UACd,CAAC,CAAC;QACJ,CAAC,EAAEvG,CAAC,CAACwG,OAAO,GAAG,UAAS9E,CAAC,EAAE;UACzB,OAAOA,CAAC,IAAI,OAAOA,CAAC,IAAI,QAAQ,IAAIA,CAAC,CAACqE,WAAW,KAAK/F,CAAC,GAAG0B,CAAC,GAAG,IAAI1B,CAAC,CAAC,UAASmB,CAAC,EAAE;YAC9EA,CAAC,CAACO,CAAC,CAAC;UACN,CAAC,CAAC;QACJ,CAAC,EAAE1B,CAAC,CAACyG,MAAM,GAAG,UAAS/E,CAAC,EAAE;UACxB,OAAO,IAAI1B,CAAC,CAAC,UAASmB,CAAC,EAAEC,CAAC,EAAE;YAC1BA,CAAC,CAACM,CAAC,CAAC;UACN,CAAC,CAAC;QACJ,CAAC,EAAE1B,CAAC,CAAC0G,IAAI,GAAG,UAAShF,CAAC,EAAE;UACtB,OAAO,IAAI1B,CAAC,CAAC,UAASmB,CAAC,EAAEC,CAAC,EAAE;YAC1B,KAAK,IAAImC,CAAC,GAAG,CAAC,EAAEO,CAAC,GAAGpC,CAAC,CAACH,MAAM,EAAEgC,CAAC,GAAGO,CAAC,EAAEP,CAAC,EAAE,EACtC7B,CAAC,CAAC6B,CAAC,CAAC,CAACoC,IAAI,CAACxE,CAAC,EAAEC,CAAC,CAAC;UACnB,CAAC,CAAC;QACJ,CAAC,EAAEpB,CAAC,CAACuF,YAAY,GAAG,OAAO9E,CAAC,IAAI,UAAU,IAAI,UAASiB,CAAC,EAAE;UACxDjB,CAAC,CAACiB,CAAC,CAAC;QACN,CAAC,IAAI,UAASA,CAAC,EAAE;UACfc,CAAC,CAACd,CAAC,EAAE,CAAC,CAAC;QACT,CAAC,EAAE1B,CAAC,CAAC6F,qBAAqB,GAAG,UAASnE,CAAC,EAAE;UACvC,OAAO9C,OAAO,GAAG,GAAG,IAAIA,OAAO,IAAIA,OAAO,CAAC+H,IAAI,CAAC,uCAAuC,EAAEjF,CAAC,CAAC;QAC7F,CAAC,EAAE3B,CAAC,CAACa,CAAC,GAAGZ,CAAC;MACZ,CAAC,EAAEP,IAAI,CAAC,IAAI,EAAEa,CAAC,CAAC,CAAC,CAAC,CAACsG,YAAY,CAAC;IAClC,CAAC,EAAE,UAAS9G,CAAC,EAAEC,CAAC,EAAEO,CAAC,EAAE;MACnBP,CAAC,CAACa,CAAC,GAAG,UAASH,CAAC,EAAE;QAChB,IAAIG,CAAC,GAAG,IAAI,CAACmF,WAAW;QACxB,OAAO,IAAI,CAACJ,IAAI,CAAC,UAASnD,CAAC,EAAE;UAC3B,OAAO5B,CAAC,CAAC4F,OAAO,CAAC/F,CAAC,CAAC,CAAC,CAAC,CAACkF,IAAI,CAAC,YAAW;YACpC,OAAOnD,CAAC;UACV,CAAC,CAAC;QACJ,CAAC,EAAE,UAASA,CAAC,EAAE;UACb,OAAO5B,CAAC,CAAC4F,OAAO,CAAC/F,CAAC,CAAC,CAAC,CAAC,CAACkF,IAAI,CAAC,YAAW;YACpC,OAAO/E,CAAC,CAAC6F,MAAM,CAACjE,CAAC,CAAC;UACpB,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC;IACH,CAAC,EAAE,UAAS1C,CAAC,EAAEC,CAAC,EAAEO,CAAC,EAAE;MACnB,SAASG,CAACA,CAAC9B,CAAC,EAAE;QACZ,OAAO,CAAC8B,CAAC,GAAG,OAAOb,MAAM,IAAI,UAAU,IAAI,OAAOA,MAAM,CAACC,QAAQ,IAAI,QAAQ,GAAG,UAAS2B,CAAC,EAAE;UAC1F,OAAO,OAAOA,CAAC;QACjB,CAAC,GAAG,UAASA,CAAC,EAAE;UACd,OAAOA,CAAC,IAAI,OAAO5B,MAAM,IAAI,UAAU,IAAI4B,CAAC,CAACuE,WAAW,KAAKnG,MAAM,IAAI4B,CAAC,KAAK5B,MAAM,CAACL,SAAS,GAAG,QAAQ,GAAG,OAAOiC,CAAC;QACrH,CAAC,EAAE7C,CAAC,CAAC;MACP;MACA2B,CAAC,CAAC,CAAC,CAAC;MACJ,IAAIM,CAAC;QAAE4B,CAAC;QAAEC,CAAC;QAAEzC,CAAC;QAAE1B,CAAC;QAAE4B,CAAC;QAAEa,CAAC,GAAGT,CAAC,CAAC,CAAC,CAAC;QAAEW,CAAC,IAAIuB,CAAC,GAAG,SAAAA,CAAS7D,CAAC,EAAE;UACnD,OAAO,IAAIkI,OAAO,CAAC,UAASrF,CAAC,EAAEE,CAAC,EAAE;YAChC/C,CAAC,GAAGqB,CAAC,CAACrB,CAAC,CAAC,EAAEA,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;YAClB,IAAIwC,CAAC,GAAGnC,MAAM,CAAC8H,cAAc,GAAG,IAAI9H,MAAM,CAAC8H,cAAc,CAAC,CAAC,GAAG,IAAI9H,MAAM,CAAC+H,aAAa,CAAC,mBAAmB,CAAC;YAC3G5F,CAAC,CAAC6F,IAAI,CAACrI,CAAC,CAACsI,MAAM,EAAEtI,CAAC,CAACuI,GAAG,CAAC,EAAE/F,CAAC,CAACgG,gBAAgB,CAAC,kBAAkB,EAAE,gBAAgB,CAAC,EAAE7H,MAAM,CAAC6C,IAAI,CAACxD,CAAC,CAACyI,OAAO,CAAC,CAAClG,OAAO,CAAC,UAASqC,CAAC,EAAE;cAC5H,IAAIO,CAAC,GAAGnF,CAAC,CAACyI,OAAO,CAAC7D,CAAC,CAAC;cACpBpC,CAAC,CAACgG,gBAAgB,CAAC5D,CAAC,EAAEO,CAAC,CAAC;YAC1B,CAAC,CAAC;YACF,IAAI1C,CAAC,GAAGzC,CAAC,CAAC0I,KAAK;YACflG,CAAC,CAACmG,MAAM,CAACC,gBAAgB,CAAC,UAAU,EAAE,UAAShE,CAAC,EAAE;cAChD,IAAIO,CAAC,GAAG0D,IAAI,CAACC,KAAK,CAAClE,CAAC,CAACmE,MAAM,GAAGnE,CAAC,CAACoE,KAAK,GAAG,GAAG,CAAC;gBAAE/B,CAAC,GAAG4B,IAAI,CAACI,IAAI,CAAC9D,CAAC,GAAG1C,CAAC,GAAG,GAAG,CAAC;cACxEzC,CAAC,CAACkJ,QAAQ,CAACjC,CAAC,CAAC;YACf,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEzE,CAAC,CAACoG,gBAAgB,CAAC,UAAU,EAAE,UAAShE,CAAC,EAAE;cACjD,IAAIO,CAAC,GAAG0D,IAAI,CAACC,KAAK,CAAClE,CAAC,CAACmE,MAAM,GAAGnE,CAAC,CAACoE,KAAK,GAAG,GAAG,CAAC;gBAAE/B,CAAC,GAAG4B,IAAI,CAACI,IAAI,CAAC9D,CAAC,IAAI,GAAG,GAAG1C,CAAC,CAAC,GAAG,GAAG,CAAC,GAAGA,CAAC;cACpFzC,CAAC,CAACkJ,QAAQ,CAACjC,CAAC,CAAC;YACf,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEzE,CAAC,CAAC2G,kBAAkB,GAAG,YAAW;cACxC,IAAI3G,CAAC,CAAC4G,UAAU,KAAK,CAAC,EAAE;gBACtB,IAAIxE,CAAC,GAAGpC,CAAC,CAAC6G,QAAQ;gBAClB,IAAI;kBACFzE,CAAC,GAAG0E,IAAI,CAACC,KAAK,CAAC3E,CAAC,CAAC;gBACnB,CAAC,CAAC,MAAM,CACR;gBACA,IAAIO,CAAC,GAAG/C,CAAC,CAACoH,YAAY,CAAChH,CAAC,CAACiH,qBAAqB,CAAC,CAAC,CAAC;kBAAExC,CAAC,GAAG;oBAAEpC,IAAI,EAAED,CAAC;oBAAE8E,IAAI,EAAElH,CAAC,CAACmH,MAAM;oBAAElB,OAAO,EAAEtD;kBAAE,CAAC;gBAC9F3C,CAAC,CAACmH,MAAM,KAAK,GAAG,GAAG9G,CAAC,CAACoE,CAAC,CAAC,GAAGlE,CAAC,CAACkE,CAAC,CAAC;cAChC;YACF,CAAC,EAAEzE,CAAC,CAACoH,IAAI,CAAC5J,CAAC,CAAC6J,IAAI,CAAC;UACnB,CAAC,CAAC;QACJ,CAAC,EAAE/F,CAAC,GAAG,SAAAA,CAAS9D,CAAC,EAAE;UACjB,OAAOA,CAAC,CAACsI,MAAM,GAAG,MAAM,EAAEzE,CAAC,CAAC7D,CAAC,CAAC;QAChC,CAAC,EAAEqB,CAAC,GAAG,SAAAA,CAAA,EAAW;UAChB,IAAIrB,CAAC,GAAGqF,SAAS,CAACzC,MAAM,GAAG,CAAC,IAAIyC,SAAS,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,GAAGA,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;UAC3E,IAAIrF,CAAC,CAACuI,GAAG,IAAI,OAAOvI,CAAC,CAACuI,GAAG,IAAI,QAAQ,EACnC,MAAM,IAAI5D,KAAK,CAAC,sBAAsB,CAAC;UACzC,IAAI3E,CAAC,CAACuI,GAAG,GAAGvI,CAAC,CAACuI,GAAG,IAAI,EAAE,EAAEvI,CAAC,CAACsI,MAAM,IAAI,OAAOtI,CAAC,CAACsI,MAAM,IAAI,QAAQ,EAC9D,MAAM,IAAI3D,KAAK,CAAC,mCAAmC,CAAC;UACtD,IAAI3E,CAAC,CAACsI,MAAM,GAAGtI,CAAC,CAACsI,MAAM,GAAGtI,CAAC,CAACsI,MAAM,CAACwB,WAAW,CAAC,CAAC,GAAG,KAAK,EAAE9J,CAAC,CAACyI,OAAO,IAAI3G,CAAC,CAAC9B,CAAC,CAACyI,OAAO,CAAC,KAAK,QAAQ,EAC9F,MAAM,IAAI9D,KAAK,CAAC,qCAAqC,CAAC;UACxD,IAAI3E,CAAC,CAACyI,OAAO,GAAGzI,CAAC,CAACyI,OAAO,IAAI,CAAC,CAAC,EAAEzI,CAAC,CAAC8E,IAAI,KAAK,OAAO9E,CAAC,CAAC8E,IAAI,IAAI,QAAQ,IAAI,CAACnE,MAAM,CAAC8C,MAAM,CAACxB,CAAC,CAAC,CAAC8H,QAAQ,CAAC/J,CAAC,CAAC8E,IAAI,CAAC,CAAC,EAC1G,MAAM,IAAIH,KAAK,CAAC,0DAA0D,CAAC;UAC7E,IAAI3E,CAAC,CAACkJ,QAAQ,IAAI,OAAOlJ,CAAC,CAACkJ,QAAQ,IAAI,UAAU,EAC/C,MAAM,IAAIvE,KAAK,CAAC,uCAAuC,CAAC;UAC1D,IAAI3E,CAAC,CAACkJ,QAAQ,GAAGlJ,CAAC,CAACkJ,QAAQ,IAAI,UAASrG,CAAC,EAAE,CAC3C,CAAC,EAAE7C,CAAC,CAACgK,UAAU,GAAGhK,CAAC,CAACgK,UAAU,IAAI,UAASnH,CAAC,EAAE,CAC9C,CAAC,EAAE7C,CAAC,CAAC0I,KAAK,IAAI,OAAO1I,CAAC,CAAC0I,KAAK,IAAI,QAAQ,EACtC,MAAM,IAAI/D,KAAK,CAAC,0BAA0B,CAAC;UAC7C,IAAI3E,CAAC,CAAC0I,KAAK,GAAG,CAAC,IAAI1I,CAAC,CAAC0I,KAAK,GAAG,GAAG,EAC9B,MAAM,IAAI/D,KAAK,CAAC,qCAAqC,CAAC;UACxD,IAAI3E,CAAC,CAAC0I,KAAK,GAAG1I,CAAC,CAAC0I,KAAK,IAAI,EAAE,EAAE1I,CAAC,CAACiK,MAAM,IAAI,OAAOjK,CAAC,CAACiK,MAAM,IAAI,QAAQ,EAClE,MAAM,IAAItF,KAAK,CAAC,6DAA6D,CAAC;UAChF,IAAI3E,CAAC,CAACiK,MAAM,GAAGjK,CAAC,CAACiK,MAAM,IAAI,KAAK,EAAEjK,CAAC,CAACkK,QAAQ,IAAI,OAAOlK,CAAC,CAACkK,QAAQ,IAAI,SAAS,EAC5E,MAAM,IAAIvF,KAAK,CAAC,oCAAoC,CAAC;UACvD,IAAI3E,CAAC,CAACkK,QAAQ,GAAGlK,CAAC,CAACkK,QAAQ,IAAI,CAAC,CAAC,EAAElK,CAAC,CAACmK,SAAS,IAAI,OAAOnK,CAAC,CAACmK,SAAS,IAAI,QAAQ,EAC9E,MAAM,IAAIxF,KAAK,CAAC,8BAA8B,CAAC;UACjD,OAAO3E,CAAC,CAACmK,SAAS,GAAGnK,CAAC,CAACmK,SAAS,IAAI,OAAO,EAAEnK,CAAC;QAChD,CAAC,EAAEL,CAAC,GAAG,SAAAA,CAASK,CAAC,EAAE;UACjB,QAAQA,CAAC,CAACsI,MAAM;YACd,KAAK,KAAK;cACR,IAAIzF,CAAC,GAAGtB,CAAC,CAACvB,CAAC,CAAC6J,IAAI,EAAE5H,CAAC,CAACmI,UAAU,CAAC;cAC/B,OAAOpK,CAAC,CAAC6J,IAAI,EAAE7J,CAAC,CAACuI,GAAG,GAAG,IAAI,CAACvD,IAAI,CAAChF,CAAC,CAACuI,GAAG,CAAC,GAAGvI,CAAC,CAACuI,GAAG,GAAG,GAAG,GAAG1F,CAAC,GAAG7C,CAAC,CAACuI,GAAG,GAAG,GAAG,GAAG1F,CAAC;cAC3E;YACF,KAAK,MAAM;YACX,KAAK,KAAK;YACV,KAAK,QAAQ;YACb,KAAK,QAAQ;cACX,IAAIE,CAAC,GAAG,YAAW;gBACjB,OAAO,CAACsC,SAAS,CAACzC,MAAM,GAAG,CAAC,IAAIyC,SAAS,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,GAAGA,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAEP,IAAI,IAAI7C,CAAC,CAACqH,IAAI;cAC7F,CAAC,CAACtJ,CAAC,CAAC;cACJ,CAACoC,CAAC,CAACiI,UAAU,CAACrK,CAAC,CAAC6J,IAAI,CAAC,IAAIzH,CAAC,CAACkI,aAAa,CAACtK,CAAC,CAAC6J,IAAI,CAAC,MAAM9G,CAAC,GAAGd,CAAC,CAACsI,IAAI,CAAC,EAAEvK,CAAC,CAAC6J,IAAI,GAAGtI,CAAC,CAACvB,CAAC,CAAC6J,IAAI,EAAE9G,CAAC,CAAC,EAAEA,CAAC,KAAKT,CAAC,CAACkI,WAAW,CAACD,IAAI,KAAKvK,CAAC,CAACyI,OAAO,CAAC,cAAc,CAAC,GAAG1F,CAAC,CAAC;UACzJ;UACA,OAAO/C,CAAC;QACV,CAAC,EAAEuB,CAAC,GAAG,SAAAA,CAAA,EAAW;UAChB,IAAIvB,CAAC,GAAGqF,SAAS,CAACzC,MAAM,GAAG,CAAC,IAAIyC,SAAS,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,GAAGA,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;UAC3E,QAAQA,SAAS,CAACzC,MAAM,GAAG,CAAC,GAAGyC,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;YAClD,KAAKpD,CAAC,CAACmI,UAAU;cACf,OAAOhI,CAAC,CAACqI,SAAS,CAACzK,CAAC,CAAC;YACvB,KAAKiC,CAAC,CAACqH,IAAI;cACT,OAAOlH,CAAC,CAACsI,UAAU,CAAC1K,CAAC,CAAC;YACxB,KAAKiC,CAAC,CAACsI,IAAI;cACT,OAAOnI,CAAC,CAACuI,UAAU,CAAC3K,CAAC,CAAC;YACxB;cACE,OAAOA,CAAC;UACZ;QACF,CAAC,EAAE;UAAEwK,WAAW,EAAEvI,CAAC,GAAG;YAAEmI,UAAU,EAAE,kDAAkD;YAAEG,IAAI,EAAE,qBAAqB;YAAEjB,IAAI,EAAE;UAAkC,CAAC;UAAEsB,OAAO,EAAE/G,CAAC;UAAEV,GAAG,EAAE,SAAAA,CAASnD,CAAC,EAAE;YAC3L,OAAOA,CAAC,CAACsI,MAAM,GAAG,KAAK,EAAEzE,CAAC,CAAC7D,CAAC,CAAC;UAC/B,CAAC;UAAE6K,IAAI,EAAE/G,CAAC;UAAEgH,SAAS,EAAE,SAAAA,CAAS9K,CAAC,EAAE;YACjC,OAAOA,CAAC,GAAGqB,CAAC,CAACrB,CAAC,CAAC,EAAEoC,CAAC,CAAC2I,WAAW,CAAC/K,CAAC,CAAC,CAACgH,IAAI,CAAC,UAASnE,CAAC,EAAE;cACjD,KAAK,IAAIE,CAAC,GAAG,IAAIiI,QAAQ,CAAC,CAAC,EAAExI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGK,CAAC,CAACD,MAAM,EAAEJ,CAAC,EAAE,EACnDO,CAAC,CAACL,MAAM,CAAC1C,CAAC,CAACmK,SAAS,EAAEtH,CAAC,CAACL,CAAC,CAAC,EAAEK,CAAC,CAACL,CAAC,CAAC,CAACyI,IAAI,CAAC;cACxC,OAAO7I,CAAC,CAAC8I,QAAQ,CAAClL,CAAC,CAAC6J,IAAI,CAAC,IAAIlJ,MAAM,CAAC6C,IAAI,CAACxD,CAAC,CAAC6J,IAAI,CAAC,CAACtH,OAAO,CAAC,UAASE,CAAC,EAAE;gBACnE,IAAImC,CAAC,GAAG5E,CAAC,CAAC6J,IAAI,CAACpH,CAAC,CAAC;gBACjBM,CAAC,CAACL,MAAM,CAACD,CAAC,EAAEmC,CAAC,CAAC;cAChB,CAAC,CAAC,EAAE5E,CAAC,CAACgK,UAAU,IAAIhK,CAAC,CAACgK,UAAU,CAACnH,CAAC,CAAC,EAAE7C,CAAC,CAAC6J,IAAI,GAAG9G,CAAC,EAAEe,CAAC,CAAC9D,CAAC,CAAC;YACvD,CAAC,CAAC;UACJ,CAAC;UAAE+K,WAAW,EAAE,SAAAA,CAAS/K,CAAC,EAAE;YAC1B,OAAO,OAAO,CAACA,CAAC,GAAGqB,CAAC,CAACrB,CAAC,CAAC,EAAEgK,UAAU,EAAE5H,CAAC,CAAC2I,WAAW,CAAC/K,CAAC,CAAC;UACvD;QAAE,CAAC,CAAC;MACJmB,CAAC,CAAC+E,OAAO,GAAG5D,CAAC;IACf,CAAC,EAAE,UAASnB,CAAC,EAAEC,CAAC,EAAEO,CAAC,EAAE;MACnBA,CAAC,CAACoB,CAAC,CAAC3B,CAAC,CAAC;MACN,IAAIU,CAAC,GAAGH,CAAC,CAAC,CAAC,CAAC;MACZtB,MAAM,CAAC6H,OAAO,GAAG7H,MAAM,CAAC6H,OAAO,IAAIpG,CAAC,CAACG,CAAC;IACxC,CAAC,EAAE,UAASd,CAAC,EAAEC,CAAC,EAAEO,CAAC,EAAE;MACnB,CAAC,UAASG,CAAC,EAAE;QACX,IAAIG,CAAC,GAAGH,CAAC,KAAK,KAAK,CAAC,IAAIA,CAAC,IAAI,OAAOvB,IAAI,GAAG,GAAG,IAAIA,IAAI,IAAIF,MAAM;UAAEwD,CAAC,GAAGyC,QAAQ,CAAC1F,SAAS,CAACwE,KAAK;QAC9F,SAAStB,CAACA,CAACzC,CAAC,EAAE1B,CAAC,EAAE;UACf,IAAI,CAACwL,GAAG,GAAG9J,CAAC,EAAE,IAAI,CAAC+J,QAAQ,GAAGzL,CAAC;QACjC;QACAyB,CAAC,CAACmF,UAAU,GAAG,YAAW;UACxB,OAAO,IAAIzC,CAAC,CAACD,CAAC,CAAC/C,IAAI,CAACyF,UAAU,EAAEtE,CAAC,EAAEoD,SAAS,CAAC,EAAEgG,YAAY,CAAC;QAC9D,CAAC,EAAEjK,CAAC,CAAC4E,WAAW,GAAG,YAAW;UAC5B,OAAO,IAAIlC,CAAC,CAACD,CAAC,CAAC/C,IAAI,CAACkF,WAAW,EAAE/D,CAAC,EAAEoD,SAAS,CAAC,EAAEiG,aAAa,CAAC;QAChE,CAAC,EAAElK,CAAC,CAACiK,YAAY,GAAGjK,CAAC,CAACkK,aAAa,GAAG,UAASjK,CAAC,EAAE;UAChDA,CAAC,IAAIA,CAAC,CAACkK,KAAK,CAAC,CAAC;QAChB,CAAC,EAAEzH,CAAC,CAAClD,SAAS,CAAC4K,KAAK,GAAG1H,CAAC,CAAClD,SAAS,CAAC6K,GAAG,GAAG,YAAW,CACpD,CAAC,EAAE3H,CAAC,CAAClD,SAAS,CAAC2K,KAAK,GAAG,YAAW;UAChC,IAAI,CAACH,QAAQ,CAACtK,IAAI,CAACmB,CAAC,EAAE,IAAI,CAACkJ,GAAG,CAAC;QACjC,CAAC,EAAE/J,CAAC,CAACsK,MAAM,GAAG,UAASrK,CAAC,EAAE1B,CAAC,EAAE;UAC3B0L,YAAY,CAAChK,CAAC,CAACsK,cAAc,CAAC,EAAEtK,CAAC,CAACuK,YAAY,GAAGjM,CAAC;QACpD,CAAC,EAAEyB,CAAC,CAACyK,QAAQ,GAAG,UAASxK,CAAC,EAAE;UAC1BgK,YAAY,CAAChK,CAAC,CAACsK,cAAc,CAAC,EAAEtK,CAAC,CAACuK,YAAY,GAAG,CAAC,CAAC;QACrD,CAAC,EAAExK,CAAC,CAAC0K,YAAY,GAAG1K,CAAC,CAAC2K,MAAM,GAAG,UAAS1K,CAAC,EAAE;UACzCgK,YAAY,CAAChK,CAAC,CAACsK,cAAc,CAAC;UAC9B,IAAIhM,CAAC,GAAG0B,CAAC,CAACuK,YAAY;UACtBjM,CAAC,IAAI,CAAC,KAAK0B,CAAC,CAACsK,cAAc,GAAGpF,UAAU,CAAC,YAAW;YAClDlF,CAAC,CAAC2K,UAAU,IAAI3K,CAAC,CAAC2K,UAAU,CAAC,CAAC;UAChC,CAAC,EAAErM,CAAC,CAAC,CAAC;QACR,CAAC,EAAEgC,CAAC,CAAC,CAAC,CAAC,EAAEP,CAAC,CAAC6G,YAAY,GAAG,OAAO1H,IAAI,GAAG,GAAG,IAAIA,IAAI,CAAC0H,YAAY,IAAInG,CAAC,KAAK,KAAK,CAAC,IAAIA,CAAC,CAACmG,YAAY,IAAI,IAAI,IAAI,IAAI,CAACA,YAAY,EAAE7G,CAAC,CAAC6K,cAAc,GAAG,OAAO1L,IAAI,GAAG,GAAG,IAAIA,IAAI,CAAC0L,cAAc,IAAInK,CAAC,KAAK,KAAK,CAAC,IAAIA,CAAC,CAACmK,cAAc,IAAI,IAAI,IAAI,IAAI,CAACA,cAAc;MACjQ,CAAC,EAAEnL,IAAI,CAAC,IAAI,EAAEa,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,EAAE,UAASR,CAAC,EAAEC,CAAC,EAAEO,CAAC,EAAE;MACnB,CAAC,UAASG,CAAC,EAAEG,CAAC,EAAE;QACd,CAAC,UAAS4B,CAAC,EAAEC,CAAC,EAAE;UACd,IAAI,CAACD,CAAC,CAACoE,YAAY,EAAE;YACnB,IAAI5G,CAAC;cAAE1B,CAAC;cAAE4B,CAAC;cAAEa,CAAC;cAAEE,CAAC;cAAEtC,CAAC,GAAG,CAAC;cAAE6C,CAAC,GAAG,CAAC,CAAC;cAAEE,CAAC,GAAG,CAAC,CAAC;cAAEP,CAAC,GAAGqB,CAAC,CAACnE,QAAQ;cAAE+C,CAAC,GAAG9B,MAAM,CAACuL,cAAc,IAAIvL,MAAM,CAACuL,cAAc,CAACrI,CAAC,CAAC;YAC/GpB,CAAC,GAAGA,CAAC,IAAIA,CAAC,CAAC8D,UAAU,GAAG9D,CAAC,GAAGoB,CAAC,EAAE,CAAC,CAAC,CAAClB,QAAQ,CAAC7B,IAAI,CAAC+C,CAAC,CAACsI,OAAO,CAAC,KAAK,kBAAkB,GAAG9K,CAAC,GAAG,SAAAA,CAAS4F,CAAC,EAAE;cAClGhF,CAAC,CAACmK,QAAQ,CAAC,YAAW;gBACpBjH,CAAC,CAAC8B,CAAC,CAAC;cACN,CAAC,CAAC;YACJ,CAAC,GAAG,YAAW;cACb,IAAIpD,CAAC,CAACwI,WAAW,IAAI,CAACxI,CAAC,CAACyI,aAAa,EAAE;gBACrC,IAAIrF,CAAC,GAAG,CAAC,CAAC;kBAAEW,CAAC,GAAG/D,CAAC,CAAC0I,SAAS;gBAC3B,OAAO1I,CAAC,CAAC0I,SAAS,GAAG,YAAW;kBAC9BtF,CAAC,GAAG,CAAC,CAAC;gBACR,CAAC,EAAEpD,CAAC,CAACwI,WAAW,CAAC,EAAE,EAAE,GAAG,CAAC,EAAExI,CAAC,CAAC0I,SAAS,GAAG3E,CAAC,EAAEX,CAAC;cAC/C;YACF,CAAC,CAAC,CAAC,IAAI7E,CAAC,GAAG,eAAe,GAAGyG,IAAI,CAAC2D,MAAM,CAAC,CAAC,GAAG,GAAG,EAAElK,CAAC,GAAG,SAAAA,CAAS2E,CAAC,EAAE;cAChEA,CAAC,CAACwF,MAAM,KAAK5I,CAAC,IAAI,OAAOoD,CAAC,CAAC4C,IAAI,IAAI,QAAQ,IAAI5C,CAAC,CAAC4C,IAAI,CAACnF,OAAO,CAACtC,CAAC,CAAC,KAAK,CAAC,IAAI+C,CAAC,CAAC,CAAC8B,CAAC,CAAC4C,IAAI,CAACxG,KAAK,CAACjB,CAAC,CAACQ,MAAM,CAAC,CAAC;YACtG,CAAC,EAAEiB,CAAC,CAAC+E,gBAAgB,GAAG/E,CAAC,CAAC+E,gBAAgB,CAAC,SAAS,EAAEtG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGuB,CAAC,CAAC6I,WAAW,CAAC,WAAW,EAAEpK,CAAC,CAAC,EAAEjB,CAAC,GAAG,SAAAA,CAAS4F,CAAC,EAAE;cAC5GpD,CAAC,CAACwI,WAAW,CAACjK,CAAC,GAAG6E,CAAC,EAAE,GAAG,CAAC;YAC3B,CAAC,IAAIpD,CAAC,CAAC8I,cAAc,IAAI,CAACpL,CAAC,GAAG,IAAIoL,cAAc,CAAC,CAAC,EAAEC,KAAK,CAACL,SAAS,GAAG,UAAStF,CAAC,EAAE;cAChF9B,CAAC,CAAC8B,CAAC,CAAC4C,IAAI,CAAC;YACX,CAAC,EAAExI,CAAC,GAAG,SAAAA,CAAS4F,CAAC,EAAE;cACjB1F,CAAC,CAACsL,KAAK,CAACR,WAAW,CAACpF,CAAC,CAAC;YACxB,CAAC,IAAIzE,CAAC,IAAI,oBAAoB,IAAIA,CAAC,CAAC5C,aAAa,CAAC,QAAQ,CAAC,IAAID,CAAC,GAAG6C,CAAC,CAACsK,eAAe,EAAEzL,CAAC,GAAG,SAAAA,CAAS4F,CAAC,EAAE;cACpG,IAAIW,CAAC,GAAGpF,CAAC,CAAC5C,aAAa,CAAC,QAAQ,CAAC;cACjCgI,CAAC,CAACuB,kBAAkB,GAAG,YAAW;gBAChChE,CAAC,CAAC8B,CAAC,CAAC,EAAEW,CAAC,CAACuB,kBAAkB,GAAG,IAAI,EAAExJ,CAAC,CAACoN,WAAW,CAACnF,CAAC,CAAC,EAAEA,CAAC,GAAG,IAAI;cAC/D,CAAC,EAAEjI,CAAC,CAACE,WAAW,CAAC+H,CAAC,CAAC;YACrB,CAAC,IAAIvG,CAAC,GAAG,SAAAA,CAAS4F,CAAC,EAAE;cACnBV,UAAU,CAACpB,CAAC,EAAE,CAAC,EAAE8B,CAAC,CAAC;YACrB,CAAC,EAAExE,CAAC,CAACwF,YAAY,GAAG,UAAShB,CAAC,EAAE;cAC9B,OAAOA,CAAC,IAAI,UAAU,KAAKA,CAAC,GAAG,IAAIX,QAAQ,CAAC,EAAE,GAAGW,CAAC,CAAC,CAAC;cACpD,KAAK,IAAIW,CAAC,GAAG,IAAIL,KAAK,CAAClC,SAAS,CAACzC,MAAM,GAAG,CAAC,CAAC,EAAE4E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGI,CAAC,CAAChF,MAAM,EAAE4E,CAAC,EAAE,EACpEI,CAAC,CAACJ,CAAC,CAAC,GAAGnC,SAAS,CAACmC,CAAC,GAAG,CAAC,CAAC;cACzB,IAAIC,CAAC,GAAG;gBAAEuF,QAAQ,EAAE/F,CAAC;gBAAEgG,IAAI,EAAErF;cAAE,CAAC;cAChC,OAAO/E,CAAC,CAAC7C,CAAC,CAAC,GAAGyH,CAAC,EAAEpG,CAAC,CAACrB,CAAC,CAAC,EAAEA,CAAC,EAAE;YAC5B,CAAC,EAAEyC,CAAC,CAACwJ,cAAc,GAAGrH,CAAC;UACzB;UACA,SAASA,CAACA,CAACqC,CAAC,EAAE;YACZ,OAAOpE,CAAC,CAACoE,CAAC,CAAC;UACb;UACA,SAAS9B,CAACA,CAAC8B,CAAC,EAAE;YACZ,IAAIlE,CAAC,EACHwD,UAAU,CAACpB,CAAC,EAAE,CAAC,EAAE8B,CAAC,CAAC,CAAC,KACjB;cACH,IAAIW,CAAC,GAAG/E,CAAC,CAACoE,CAAC,CAAC;cACZ,IAAIW,CAAC,EAAE;gBACL7E,CAAC,GAAG,CAAC,CAAC;gBACN,IAAI;kBACF,CAAC,UAASyE,CAAC,EAAE;oBACX,IAAIC,CAAC,GAAGD,CAAC,CAACwF,QAAQ;sBAAEtF,CAAC,GAAGF,CAAC,CAACyF,IAAI;oBAC9B,QAAQvF,CAAC,CAAC9E,MAAM;sBACd,KAAK,CAAC;wBACJ6E,CAAC,CAAC,CAAC;wBACH;sBACF,KAAK,CAAC;wBACJA,CAAC,CAACC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACP;sBACF,KAAK,CAAC;wBACJD,CAAC,CAACC,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;wBACb;sBACF,KAAK,CAAC;wBACJD,CAAC,CAACC,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;wBACnB;sBACF;wBACED,CAAC,CAACrC,KAAK,CAACtB,CAAC,EAAE4D,CAAC,CAAC;oBACjB;kBACF,CAAC,EAAEE,CAAC,CAAC;gBACP,CAAC,SAAS;kBACRhD,CAAC,CAACqC,CAAC,CAAC,EAAElE,CAAC,GAAG,CAAC,CAAC;gBACd;cACF;YACF;UACF;QACF,CAAC,EAAE,OAAOxC,IAAI,GAAG,GAAG,GAAGuB,CAAC,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,CAAC,GAAGvB,IAAI,CAAC;MACxD,CAAC,EAAEO,IAAI,CAAC,IAAI,EAAEa,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC,EAAE,UAASR,CAAC,EAAEC,CAAC,EAAE;MAChB,IAAIO,CAAC;QAAEG,CAAC;QAAEG,CAAC,GAAGd,CAAC,CAAC+E,OAAO,GAAG,CAAC,CAAC;MAC5B,SAASrC,CAACA,CAAA,EAAG;QACX,MAAM,IAAIc,KAAK,CAAC,iCAAiC,CAAC;MACpD;MACA,SAASb,CAACA,CAAA,EAAG;QACX,MAAM,IAAIa,KAAK,CAAC,mCAAmC,CAAC;MACtD;MACA,SAAStD,CAACA,CAACoB,CAAC,EAAE;QACZ,IAAId,CAAC,KAAK4E,UAAU,EAClB,OAAOA,UAAU,CAAC9D,CAAC,EAAE,CAAC,CAAC;QACzB,IAAI,CAACd,CAAC,KAAKkC,CAAC,IAAI,CAAClC,CAAC,KAAK4E,UAAU,EAC/B,OAAO5E,CAAC,GAAG4E,UAAU,EAAEA,UAAU,CAAC9D,CAAC,EAAE,CAAC,CAAC;QACzC,IAAI;UACF,OAAOd,CAAC,CAACc,CAAC,EAAE,CAAC,CAAC;QAChB,CAAC,CAAC,MAAM;UACN,IAAI;YACF,OAAOd,CAAC,CAACb,IAAI,CAAC,IAAI,EAAE2B,CAAC,EAAE,CAAC,CAAC;UAC3B,CAAC,CAAC,MAAM;YACN,OAAOd,CAAC,CAACb,IAAI,CAAC,IAAI,EAAE2B,CAAC,EAAE,CAAC,CAAC;UAC3B;QACF;MACF;MACA,CAAC,YAAW;QACV,IAAI;UACFd,CAAC,GAAG,OAAO4E,UAAU,IAAI,UAAU,GAAGA,UAAU,GAAG1C,CAAC;QACtD,CAAC,CAAC,MAAM;UACNlC,CAAC,GAAGkC,CAAC;QACP;QACA,IAAI;UACF/B,CAAC,GAAG,OAAOuJ,YAAY,IAAI,UAAU,GAAGA,YAAY,GAAGvH,CAAC;QAC1D,CAAC,CAAC,MAAM;UACNhC,CAAC,GAAGgC,CAAC;QACP;MACF,CAAC,EAAE,CAAC;MACJ,IAAInE,CAAC;QAAE4B,CAAC,GAAG,EAAE;QAAEa,CAAC,GAAG,CAAC,CAAC;QAAEE,CAAC,GAAG,CAAC,CAAC;MAC7B,SAAStC,CAACA,CAAA,EAAG;QACXoC,CAAC,IAAIzC,CAAC,KAAKyC,CAAC,GAAG,CAAC,CAAC,EAAEzC,CAAC,CAACiD,MAAM,GAAGrB,CAAC,GAAG5B,CAAC,CAACuN,MAAM,CAAC3L,CAAC,CAAC,GAAGe,CAAC,GAAG,CAAC,CAAC,EAAEf,CAAC,CAACqB,MAAM,IAAIC,CAAC,CAAC,CAAC,CAAC;MAC1E;MACA,SAASA,CAACA,CAAA,EAAG;QACX,IAAI,CAACT,CAAC,EAAE;UACN,IAAIK,CAAC,GAAGpB,CAAC,CAACrB,CAAC,CAAC;UACZoC,CAAC,GAAG,CAAC,CAAC;UACN,KAAK,IAAIwC,CAAC,GAAGrD,CAAC,CAACqB,MAAM,EAAEgC,CAAC,GAAI;YAC1B,KAAKjF,CAAC,GAAG4B,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAE,EAAEe,CAAC,GAAGsC,CAAC,GACzBjF,CAAC,IAAIA,CAAC,CAAC2C,CAAC,CAAC,CAAC6K,GAAG,CAAC,CAAC;YACjB7K,CAAC,GAAG,CAAC,CAAC,EAAEsC,CAAC,GAAGrD,CAAC,CAACqB,MAAM;UACtB;UACAjD,CAAC,GAAG,IAAI,EAAEyC,CAAC,GAAG,CAAC,CAAC,EAAE,UAAS+C,CAAC,EAAE;YAC5B,IAAIrD,CAAC,KAAKuJ,YAAY,EACpB,OAAOA,YAAY,CAAClG,CAAC,CAAC;YACxB,IAAI,CAACrD,CAAC,KAAKgC,CAAC,IAAI,CAAChC,CAAC,KAAKuJ,YAAY,EACjC,OAAOvJ,CAAC,GAAGuJ,YAAY,EAAEA,YAAY,CAAClG,CAAC,CAAC;YAC1C,IAAI;cACFrD,CAAC,CAACqD,CAAC,CAAC;YACN,CAAC,CAAC,MAAM;cACN,IAAI;gBACF,OAAOrD,CAAC,CAAChB,IAAI,CAAC,IAAI,EAAEqE,CAAC,CAAC;cACxB,CAAC,CAAC,MAAM;gBACN,OAAOrD,CAAC,CAAChB,IAAI,CAAC,IAAI,EAAEqE,CAAC,CAAC;cACxB;YACF;UACF,CAAC,CAAC1C,CAAC,CAAC;QACN;MACF;MACA,SAASM,CAACA,CAACN,CAAC,EAAEmC,CAAC,EAAE;QACf,IAAI,CAACwI,GAAG,GAAG3K,CAAC,EAAE,IAAI,CAAC4K,KAAK,GAAGzI,CAAC;MAC9B;MACA,SAASpC,CAACA,CAAA,EAAG,CACb;MACAP,CAAC,CAACmK,QAAQ,GAAG,UAAS3J,CAAC,EAAE;QACvB,IAAImC,CAAC,GAAG,IAAI2C,KAAK,CAAClC,SAAS,CAACzC,MAAM,GAAG,CAAC,CAAC;QACvC,IAAIyC,SAAS,CAACzC,MAAM,GAAG,CAAC,EACtB,KAAK,IAAIuC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGE,SAAS,CAACzC,MAAM,EAAEuC,CAAC,EAAE,EACvCP,CAAC,CAACO,CAAC,GAAG,CAAC,CAAC,GAAGE,SAAS,CAACF,CAAC,CAAC;QAC3B5D,CAAC,CAAC0B,IAAI,CAAC,IAAIF,CAAC,CAACN,CAAC,EAAEmC,CAAC,CAAC,CAAC,EAAErD,CAAC,CAACqB,MAAM,KAAK,CAAC,IAAIR,CAAC,IAAIf,CAAC,CAACwB,CAAC,CAAC;MAClD,CAAC,EAAEE,CAAC,CAACnC,SAAS,CAACuM,GAAG,GAAG,YAAW;QAC9B,IAAI,CAACC,GAAG,CAAChI,KAAK,CAAC,IAAI,EAAE,IAAI,CAACiI,KAAK,CAAC;MAClC,CAAC,EAAEpL,CAAC,CAACqL,KAAK,GAAG,SAAS,EAAErL,CAAC,CAACsL,OAAO,GAAG,CAAC,CAAC,EAAEtL,CAAC,CAACuL,GAAG,GAAG,CAAC,CAAC,EAAEvL,CAAC,CAACwL,IAAI,GAAG,EAAE,EAAExL,CAAC,CAACyL,OAAO,GAAG,EAAE,EAAEzL,CAAC,CAAC0L,QAAQ,GAAG,CAAC,CAAC,EAAE1L,CAAC,CAAC2L,EAAE,GAAGpL,CAAC,EAAEP,CAAC,CAAC4L,WAAW,GAAGrL,CAAC,EAAEP,CAAC,CAAC6L,IAAI,GAAGtL,CAAC,EAAEP,CAAC,CAAC8L,GAAG,GAAGvL,CAAC,EAAEP,CAAC,CAAC+L,cAAc,GAAGxL,CAAC,EAAEP,CAAC,CAACgM,kBAAkB,GAAGzL,CAAC,EAAEP,CAAC,CAACiM,IAAI,GAAG1L,CAAC,EAAEP,CAAC,CAACkM,eAAe,GAAG3L,CAAC,EAAEP,CAAC,CAACmM,mBAAmB,GAAG5L,CAAC,EAAEP,CAAC,CAACoM,SAAS,GAAG,UAAS5L,CAAC,EAAE;QAC5R,OAAO,EAAE;MACX,CAAC,EAAER,CAAC,CAACqM,OAAO,GAAG,UAAS7L,CAAC,EAAE;QACzB,MAAM,IAAIkC,KAAK,CAAC,kCAAkC,CAAC;MACrD,CAAC,EAAE1C,CAAC,CAACsM,GAAG,GAAG,YAAW;QACpB,OAAO,GAAG;MACZ,CAAC,EAAEtM,CAAC,CAACuM,KAAK,GAAG,UAAS/L,CAAC,EAAE;QACvB,MAAM,IAAIkC,KAAK,CAAC,gCAAgC,CAAC;MACnD,CAAC,EAAE1C,CAAC,CAACwM,KAAK,GAAG,YAAW;QACtB,OAAO,CAAC;MACV,CAAC;IACH,CAAC,EAAE,UAAStN,CAAC,EAAEC,CAAC,EAAEO,CAAC,EAAE;MACnB,SAASG,CAACA,CAAC+B,CAAC,EAAEC,CAAC,EAAE;QACf,KAAK,IAAIzC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyC,CAAC,CAAClB,MAAM,EAAEvB,CAAC,EAAE,EAAE;UACjC,IAAI1B,CAAC,GAAGmE,CAAC,CAACzC,CAAC,CAAC;UACZ1B,CAAC,CAACqE,UAAU,GAAGrE,CAAC,CAACqE,UAAU,IAAI,CAAC,CAAC,EAAErE,CAAC,CAACsE,YAAY,GAAG,CAAC,CAAC,EAAE,OAAO,IAAItE,CAAC,KAAKA,CAAC,CAACwC,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAExB,MAAM,CAACuB,cAAc,CAAC2B,CAAC,EAAElE,CAAC,CAAC+O,GAAG,EAAE/O,CAAC,CAAC;QAC/H;MACF;MACA,IAAIsC,CAAC,GAAGN,CAAC,CAAC,CAAC,CAAC;MACZR,CAAC,CAAC+E,OAAO,GAAG,YAAW;QACrB,SAASrC,CAACA,CAAA,EAAG;UACX,CAAC,UAAStC,CAAC,EAAEa,CAAC,EAAE;YACd,IAAI,EAAEb,CAAC,YAAYa,CAAC,CAAC,EACnB,MAAM,IAAIU,SAAS,CAAC,mCAAmC,CAAC;UAC5D,CAAC,EAAE,IAAI,EAAEe,CAAC,CAAC;QACb;QACA,IAAIC,CAAC,EAAEzC,CAAC,EAAE1B,CAAC;QACX,OAAOmE,CAAC,GAAGD,CAAC,EAAElE,CAAC,GAAG,CAAC;UAAE+O,GAAG,EAAE,WAAW;UAAEhN,KAAK,EAAE,SAAAA,CAASH,CAAC,EAAE;YACxD,OAAOU,CAAC,CAACV,CAAC,CAAC;UACb;QAAE,CAAC,EAAE;UAAEmN,GAAG,EAAE,YAAY;UAAEhN,KAAK,EAAE,SAAAA,CAASH,CAAC,EAAE;YAC3C,OAAO+H,IAAI,CAACqF,SAAS,CAACpN,CAAC,CAAC;UAC1B;QAAE,CAAC,EAAE;UAAEmN,GAAG,EAAE,YAAY;UAAEhN,KAAK,EAAE,SAAAA,CAASH,CAAC,EAAE;YAC3C,IAAI,IAAI,CAAC8I,UAAU,CAAC9I,CAAC,CAAC,EACpB,OAAOA,CAAC;YACV,IAAI,IAAI,CAAC+I,aAAa,CAAC/I,CAAC,CAAC,EACvB,OAAO,IAAIyJ,QAAQ,CAACzJ,CAAC,CAAC;YACxB,IAAI,IAAI,CAAC2J,QAAQ,CAAC3J,CAAC,CAAC,EAAE;cACpB,IAAIa,CAAC,GAAG,IAAI4I,QAAQ,CAAC,CAAC;cACtB,OAAOrK,MAAM,CAAC6C,IAAI,CAACjC,CAAC,CAAC,CAACgB,OAAO,CAAC,UAASD,CAAC,EAAE;gBACxC,IAAItC,CAAC,GAAGuB,CAAC,CAACe,CAAC,CAAC;gBACZF,CAAC,CAACM,MAAM,CAACJ,CAAC,EAAEtC,CAAC,CAAC;cAChB,CAAC,CAAC,EAAEoC,CAAC;YACP;YACA,MAAM,IAAIuC,KAAK,CAAC,sEAAsE,CAAC;UACzF;QAAE,CAAC,EAAE;UAAE+J,GAAG,EAAE,UAAU;UAAEhN,KAAK,EAAE,SAAAA,CAASH,CAAC,EAAE;YACzC,OAAOZ,MAAM,CAACC,SAAS,CAAC+B,QAAQ,CAAC7B,IAAI,CAACS,CAAC,CAAC,KAAK,iBAAiB;UAChE;QAAE,CAAC,EAAE;UAAEmN,GAAG,EAAE,YAAY;UAAEhN,KAAK,EAAE,SAAAA,CAASH,CAAC,EAAE;YAC3C,OAAOA,CAAC,YAAYyJ,QAAQ;UAC9B;QAAE,CAAC,EAAE;UAAE0D,GAAG,EAAE,eAAe;UAAEhN,KAAK,EAAE,SAAAA,CAASH,CAAC,EAAE;YAC9C,OAAOA,CAAC,YAAYqN,eAAe;UACrC;QAAE,CAAC,EAAE;UAAEF,GAAG,EAAE,aAAa;UAAEhN,KAAK,EAAE,SAAAA,CAAA,EAAW;YAC3C,IAAIH,CAAC,GAAG8D,SAAS,CAACzC,MAAM,GAAG,CAAC,IAAIyC,SAAS,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,GAAGA,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YAC3E,OAAO,IAAI6C,OAAO,CAAC,UAAS9F,CAAC,EAAEE,CAAC,EAAE;cAChC,IAAItC,CAAC,GAAGN,QAAQ,CAACE,aAAa,CAAC,OAAO,CAAC;cACvCI,CAAC,CAAC8E,IAAI,GAAG,MAAM,EAAEvD,CAAC,CAAC2I,QAAQ,IAAIlK,CAAC,CAAC6O,YAAY,CAAC,UAAU,EAAE,UAAU,CAAC,EAAEtN,CAAC,CAAC0I,MAAM,IAAIjK,CAAC,CAAC6O,YAAY,CAAC,QAAQ,EAAEtN,CAAC,CAAC0I,MAAM,CAAC,EAAEjK,CAAC,CAAC8O,KAAK,CAACC,OAAO,GAAG,MAAM,EAAErP,QAAQ,CAACmF,IAAI,CAAChF,WAAW,CAACG,CAAC,CAAC,EAAEA,CAAC,CAAC4I,gBAAgB,CAAC,QAAQ,EAAE,UAAS/F,CAAC,EAAE;gBACtN,IAAIE,CAAC,GAAGF,CAAC,CAACmM,MAAM,CAACC,KAAK;gBACtB7M,CAAC,CAACW,CAAC,CAAC,EAAErD,QAAQ,CAACmF,IAAI,CAACkI,WAAW,CAAC/M,CAAC,CAAC;cACpC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEA,CAAC,CAACkP,KAAK,CAAC,CAAC;YACnB,CAAC,CAAC;UACJ;QAAE,CAAC,EAAE;UAAER,GAAG,EAAE,cAAc;UAAEhN,KAAK,EAAE,SAAAA,CAASH,CAAC,EAAE;YAC7C,IAAIa,CAAC,GAAGb,CAAC,CAAC4N,IAAI,CAAC,CAAC,CAACjL,KAAK,CAAC,SAAS,CAAC;cAAE5B,CAAC,GAAG,CAAC,CAAC;YACzC,OAAOF,CAAC,CAACG,OAAO,CAAC,UAASvC,CAAC,EAAE;cAC3B,IAAI6C,CAAC,GAAG7C,CAAC,CAACkE,KAAK,CAAC,IAAI,CAAC;gBAAEnB,CAAC,GAAGF,CAAC,CAACrB,KAAK,CAAC,CAAC;gBAAEgB,CAAC,GAAGK,CAAC,CAACc,IAAI,CAAC,IAAI,CAAC;cACtDZ,CAAC,KAAKT,CAAC,CAACS,CAAC,CAAC,GAAGP,CAAC,CAAC;YACjB,CAAC,CAAC,EAAEF,CAAC;UACP;QAAE,CAAC,CAAC,EAAE,CAACjB,CAAC,GAAG,IAAI,KAAKS,CAAC,CAACgC,CAAC,CAAClD,SAAS,EAAES,CAAC,CAAC,EAAE1B,CAAC,IAAImC,CAAC,CAACgC,CAAC,EAAEnE,CAAC,CAAC,EAAEkE,CAAC;MACxD,CAAC,CAAC,CAAC;IACL,CAAC,EAAE,UAAS1C,CAAC,EAAEC,CAAC,EAAE;MAChB,IAAIO,CAAC,GAAG,SAAAA,CAASM,CAAC,EAAE;UAClB,OAAOL,kBAAkB,CAACK,CAAC,CAAC,CAACJ,OAAO,CAAC,UAAU,EAAEuN,MAAM,CAAC,CAACvN,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;QAC/E,CAAC;QAAEC,CAAC,GAAG,SAAAA,CAASG,CAAC,EAAE4B,CAAC,EAAEC,CAAC,EAAEzC,CAAC,EAAE;UAC1B,OAAOwC,CAAC,GAAGA,CAAC,IAAI,IAAI,EAAEC,CAAC,GAAGA,CAAC,IAAI,GAAG,EAAEzC,CAAC,GAAGA,CAAC,IAAI,IAAI,EAAEY,CAAC,GAAG,UAAStC,CAAC,EAAE;YACjE,KAAK,IAAI4B,CAAC,GAAG,IAAIgG,KAAK,CAAC,CAAC,EAAEnF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGzC,CAAC,CAACiD,MAAM,EAAER,CAAC,EAAE,EAChDzC,CAAC,CAACyC,CAAC,CAAC,IAAIb,CAAC,CAAC0B,IAAI,CAACtD,CAAC,CAACyC,CAAC,CAAC,CAAC;YACtB,OAAOb,CAAC;UACV,CAAC,CAACZ,MAAM,CAAC6C,IAAI,CAACvB,CAAC,CAAC,CAACoN,GAAG,CAAC,UAAS1P,CAAC,EAAE;YAC/B,IAAI4B,CAAC;cAAEa,CAAC;cAAEE,CAAC,GAAG3C,CAAC;YACf,IAAI0B,CAAC,KAAKiB,CAAC,GAAGjB,CAAC,GAAG,GAAG,GAAGiB,CAAC,GAAG,GAAG,CAAC,EAAE,OAAOL,CAAC,CAACtC,CAAC,CAAC,IAAI,QAAQ,IAAIsC,CAAC,CAACtC,CAAC,CAAC,KAAK,IAAI,EACxE4B,CAAC,GAAGO,CAAC,CAACG,CAAC,CAACtC,CAAC,CAAC,EAAE,IAAI,EAAEmE,CAAC,EAAExB,CAAC,CAAC,CAAC,KACrB;cACHuB,CAAC,KAAKzB,CAAC,GAAGE,CAAC,EAAEA,CAAC,GAAG,CAACgN,KAAK,CAACC,UAAU,CAACnN,CAAC,CAAC,CAAC,IAAIoN,QAAQ,CAACpN,CAAC,CAAC,GAAGyB,CAAC,GAAG4L,MAAM,CAACnN,CAAC,CAAC,GAAGA,CAAC,CAAC;cAC1E,IAAItC,CAAC,GAAGiC,CAAC,CAACtC,CAAC,CAAC;cACZK,CAAC,GAAG,CAACA,CAAC,GAAG,CAACA,CAAC,GAAG,CAACA,CAAC,GAAGA,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,GAAGA,CAAC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAGA,CAAC,MAAM,CAAC,GAAG,GAAG,GAAGA,CAAC,KAAK,EAAE,EAAEuB,CAAC,GAAGI,CAAC,CAACW,CAAC,CAAC,GAAG,GAAG,GAAGX,CAAC,CAAC3B,CAAC,CAAC;YACxG;YACA,OAAOuB,CAAC;UACV,CAAC,CAAC,CAAC,CAACoC,IAAI,CAACG,CAAC,CAAC,CAACjC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,GAAG,EAAE;QAC1C,CAAC;MACDV,CAAC,CAAC+E,OAAO,GAAGpE,CAAC;IACf,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;AACJ,CAAC,EAAEmE,CAAC,CAAC;AACL,IAAIyJ,CAAC,GAAGzJ,CAAC,CAACC,OAAO;AACjB,MAAMyJ,CAAC,GAAG,eAAgBnP,CAAC,CAACkP,CAAC,CAAC;EAAEE,CAAC,GAAG,kpBAAkpB;AACtrB,MAAMC,CAAC,CAAC;EACN;AACF;AACA;AACA;AACA;EACE,WAAWC,mBAAmBA,CAAA,EAAG;IAC/B,OAAO,CAAC,CAAC;EACX;EACA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,WAAWC,OAAOA,CAAA,EAAG;IACnB,OAAO;MACLC,IAAI,EAAEJ,CAAC;MACPtC,KAAK,EAAE;IACT,CAAC;EACH;EACA;AACF;AACA;AACA;AACA;AACA;EACE,WAAW2C,gBAAgBA,CAAA,EAAG;IAC5B,OAAO,CAAC,CAAC;EACX;EACA;AACF;AACA;AACA;AACA;AACA;AACA;EACE7I,WAAWA,CAAC;IAAEyC,IAAI,EAAE7I,CAAC;IAAEkP,MAAM,EAAE/O,CAAC;IAAEgP,GAAG,EAAE/O,CAAC;IAAEgP,QAAQ,EAAEzO;EAAE,CAAC,EAAE;IACvD,IAAI,CAACwO,GAAG,GAAG/O,CAAC,EAAE,IAAI,CAACgP,QAAQ,GAAGzO,CAAC,EAAE,IAAI,CAACuO,MAAM,GAAG;MAC7CG,QAAQ,EAAElP,CAAC,CAACkP,QAAQ,IAAI,EAAE;MAC1B5H,OAAO,EAAEtH,CAAC,CAACsH,OAAO,IAAI,CAAC;IACzB,CAAC,EAAE,IAAI,CAAC6H,KAAK,GAAG;MACdC,OAAO,EAAE,IAAI;MACbC,SAAS,EAAE,IAAI;MACftH,QAAQ,EAAE,IAAI;MACduH,KAAK,EAAE,IAAI;MACXC,WAAW,EAAE,IAAI;MACjBC,WAAW,EAAE,IAAI;MACjBC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAE,IAAI;MACfC,eAAe,EAAE,IAAI;MACrBC,QAAQ,EAAE;IACZ,CAAC,EAAE,IAAI,CAACC,KAAK,GAAG;MACdC,IAAI,EAAE,EAAE;MACRC,IAAI,EAAE,CAAC;IACT,CAAC,EAAE,IAAI,CAACrH,IAAI,GAAG7I,CAAC;EAClB;EACA;AACF;AACA;AACA;AACA;AACA;AACA;EACEmQ,MAAMA,CAAA,EAAG;IACP,OAAO,IAAI,CAACb,KAAK,CAACC,OAAO,GAAG,IAAI,CAACa,IAAI,CAAC,KAAK,EAAE,IAAI,CAACC,GAAG,CAACC,SAAS,CAAC,EAAE,IAAI,CAAChB,KAAK,CAACE,SAAS,GAAG,IAAI,CAACY,IAAI,CAAC,KAAK,EAAE,IAAI,CAACC,GAAG,CAACb,SAAS,CAAC,EAAE,IAAI,CAACF,KAAK,CAACI,WAAW,GAAG,IAAI,CAACa,eAAe,CAAC,CAAC,EAAE,IAAI,CAACjB,KAAK,CAACK,WAAW,GAAG,IAAI,CAACa,kBAAkB,CAAC,CAAC,EAAE7Q,MAAM,CAAC6C,IAAI,CAAC,IAAI,CAACqG,IAAI,CAACqH,IAAI,CAAC,CAACtO,MAAM,IAAI,IAAI,CAAC0N,KAAK,CAACE,SAAS,CAAC3Q,WAAW,CAAC,IAAI,CAACyQ,KAAK,CAACK,WAAW,CAAC,EAAE,IAAI,CAACc,eAAe,CAAC,IAAI,CAAC5H,IAAI,CAACqH,IAAI,CAAC,IAAI,IAAI,CAACZ,KAAK,CAACE,SAAS,CAAC3Q,WAAW,CAAC,IAAI,CAACyQ,KAAK,CAACI,WAAW,CAAC,EAAE,IAAI,CAACJ,KAAK,CAACC,OAAO,CAAC1Q,WAAW,CAAC,IAAI,CAACyQ,KAAK,CAACE,SAAS,CAAC,EAAE,IAAI,CAACF,KAAK,CAACC,OAAO;EAChf;EACA;AACF;AACA;AACA;AACA;AACA;AACA;EACEmB,IAAIA,CAAA,EAAG;IACL,OAAO,IAAI,CAAC7H,IAAI;EAClB;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE8H,QAAQA,CAAA,EAAG;IACT,OAAO,IAAI,CAAC9H,IAAI,CAACoH,IAAI,CAAC9B,IAAI,CAAC,CAAC,KAAK,EAAE;EACrC;EACA;AACF;AACA;AACA;AACA;EACE,IAAItF,IAAIA,CAAC7I,CAAC,EAAE;IACV,IAAI,CAACgQ,KAAK,GAAGrQ,MAAM,CAACiR,MAAM,CAAC,CAAC,CAAC,EAAE;MAC7BX,IAAI,EAAEjQ,CAAC,CAACiQ,IAAI,IAAI,IAAI,CAACD,KAAK,CAACC,IAAI;MAC/BC,IAAI,EAAElQ,CAAC,CAACkQ,IAAI,IAAI,IAAI,CAACF,KAAK,CAACE;IAC7B,CAAC,CAAC;EACJ;EACA;AACF;AACA;AACA;AACA;EACE,IAAIrH,IAAIA,CAAA,EAAG;IACT,OAAO,IAAI,CAACmH,KAAK;EACnB;EACA;AACF;AACA;EACE,IAAIK,GAAGA,CAAA,EAAG;IACR,OAAO;MACLC,SAAS,EAAE,IAAI,CAACnB,GAAG,CAAC0B,MAAM,CAACC,KAAK;MAChCrB,KAAK,EAAE,IAAI,CAACN,GAAG,CAAC0B,MAAM,CAACpB,KAAK;MAC5B;AACN;AACA;MACMD,SAAS,EAAE,WAAW;MACtBuB,OAAO,EAAE,kBAAkB;MAC3BrB,WAAW,EAAE,yBAAyB;MACtCsB,UAAU,EAAE,gCAAgC;MAC5CrB,WAAW,EAAE,oBAAoB;MACjCsB,mBAAmB,EAAE,8BAA8B;MACnDrB,SAAS,EAAE,kBAAkB;MAC7BC,SAAS,EAAE,kBAAkB;MAC7BC,eAAe,EAAE,wBAAwB;MACzCC,QAAQ,EAAE,mBAAmB;MAC7B7H,QAAQ,EAAE,qBAAqB;MAC/BgJ,eAAe,EAAE,8BAA8B;MAC/CC,cAAc,EAAE;IAClB,CAAC;EACH;EACA;AACF;AACA;AACA;AACA;EACEZ,eAAeA,CAAA,EAAG;IAChB,MAAMvQ,CAAC,GAAG,IAAI,CAACoQ,IAAI,CAAC,KAAK,EAAE,IAAI,CAACC,GAAG,CAACX,WAAW,CAAC;IAChD,OAAO,IAAI,CAACJ,KAAK,CAACpH,QAAQ,GAAG,IAAI,CAACkI,IAAI,CAAC,OAAO,EAAE,IAAI,CAACC,GAAG,CAACnI,QAAQ,CAAC,EAAE,IAAI,CAACoH,KAAK,CAACG,KAAK,GAAG,IAAI,CAACW,IAAI,CAAC,KAAK,EAAE,CAAC,IAAI,CAACC,GAAG,CAACZ,KAAK,EAAE,IAAI,CAACY,GAAG,CAACU,OAAO,CAAC,EAAE;MAC1IK,eAAe,EAAE,CAAC,IAAI,CAAChC;IACzB,CAAC,CAAC,EAAE,IAAI,CAACE,KAAK,CAACG,KAAK,CAAC4B,OAAO,CAACC,WAAW,GAAG,IAAI,CAACnC,GAAG,CAACoC,IAAI,CAACvS,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,CAACoQ,QAAQ,KAAK,IAAI,CAACE,KAAK,CAACG,KAAK,CAAC7H,gBAAgB,CAAC,OAAO,EAAGzH,CAAC,IAAK;MACtI,IAAI,CAACqR,aAAa,CAACrR,CAAC,CAAC;IACvB,CAAC,CAAC,EAAE,IAAI,CAACmP,KAAK,CAACG,KAAK,CAAC7H,gBAAgB,CAAC,SAAS,EAAGzH,CAAC,IAAK;MACtD,MAAM,CAACC,CAAC,EAAEO,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC;QAAEG,CAAC,GAAGX,CAAC,CAACsR,OAAO,IAAItR,CAAC,CAACuR,OAAO;MACnD,QAAQvR,CAAC,CAACwR,OAAO;QACf,KAAKvR,CAAC;UACJD,CAAC,CAACyR,cAAc,CAAC,CAAC,EAAEzR,CAAC,CAAC0R,eAAe,CAAC,CAAC,EAAE,IAAI,CAACL,aAAa,CAACrR,CAAC,CAAC;UAC9D;QACF,KAAKQ,CAAC;UACJG,CAAC,IAAI,IAAI,CAACgR,aAAa,CAAC3R,CAAC,CAAC;UAC1B;MACJ;IACF,CAAC,CAAC,CAAC,EAAEH,CAAC,CAACnB,WAAW,CAAC,IAAI,CAACyQ,KAAK,CAACpH,QAAQ,CAAC,EAAElI,CAAC,CAACnB,WAAW,CAAC,IAAI,CAACyQ,KAAK,CAACG,KAAK,CAAC,EAAEzP,CAAC;EAC7E;EACA;AACF;AACA;AACA;AACA;EACEwR,aAAaA,CAACxR,CAAC,EAAE;IACf,IAAIG,CAAC,GAAG,IAAI,CAACmP,KAAK,CAACG,KAAK,CAACsC,WAAW;IACpC/R,CAAC,CAAC8D,IAAI,KAAK,OAAO,KAAK3D,CAAC,GAAG,CAACH,CAAC,CAACgS,aAAa,IAAI3S,MAAM,CAAC2S,aAAa,EAAEC,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAACC,gBAAgB,CAAC,CAAC,EAAE,IAAI,CAACC,aAAa,CAAChS,CAAC,CAAC;EACvI;EACA;AACF;AACA;EACE+R,gBAAgBA,CAAA,EAAG;IACjB,IAAI,CAAC5C,KAAK,CAACI,WAAW,CAAC0C,SAAS,CAACC,MAAM,CAAC,IAAI,CAAChC,GAAG,CAACW,UAAU,CAAC,EAAE,IAAI,CAAC1B,KAAK,CAACI,WAAW,CAAC4C,YAAY,CAAC,IAAI,CAAChD,KAAK,CAACpH,QAAQ,EAAE,IAAI,CAACoH,KAAK,CAACG,KAAK,CAAC;EAC1I;EACA;AACF;AACA;AACA;AACA;EACEqC,aAAaA,CAAC9R,CAAC,EAAE;IACfA,CAAC,CAAC4R,cAAc,CAAC,CAAC,EAAE5R,CAAC,CAAC6R,eAAe,CAAC,CAAC;IACvC,MAAM1R,CAAC,GAAGd,MAAM,CAACkT,YAAY,CAAC,CAAC;MAAEnS,CAAC,GAAG,IAAIoS,KAAK,CAAC,CAAC;MAAEvR,CAAC,GAAGd,CAAC,CAACsS,UAAU,CAACC,UAAU,CAACC,OAAO,CAAE,IAAG,IAAI,CAACtC,GAAG,CAACX,WAAY,EAAC,CAAC,CAACkD,aAAa,CAAE,IAAG,IAAI,CAACvC,GAAG,CAACU,OAAQ,EAAC,CAAC;IACvJ3Q,CAAC,CAACyS,kBAAkB,CAAC5R,CAAC,CAAC,EAAEd,CAAC,CAAC2S,eAAe,CAAC,CAAC,EAAE3S,CAAC,CAAC4S,QAAQ,CAAC3S,CAAC,CAAC;EAC7D;EACA;AACF;AACA;AACA;AACA;EACEoQ,kBAAkBA,CAAA,EAAG;IACnB,MAAMxQ,CAAC,GAAG,IAAI,CAACoQ,IAAI,CAAC,GAAG,EAAE,IAAI,CAACC,GAAG,CAACV,WAAW,EAAE;MAC7C3B,MAAM,EAAE,QAAQ;MAChBgF,GAAG,EAAE;IACP,CAAC,CAAC;IACF,OAAO,IAAI,CAAC1D,KAAK,CAACM,SAAS,GAAG,IAAI,CAACQ,IAAI,CAAC,KAAK,EAAE,IAAI,CAACC,GAAG,CAACT,SAAS,CAAC,EAAE,IAAI,CAACN,KAAK,CAACO,SAAS,GAAG,IAAI,CAACO,IAAI,CAAC,KAAK,EAAE,IAAI,CAACC,GAAG,CAACR,SAAS,CAAC,EAAE,IAAI,CAACP,KAAK,CAACQ,eAAe,GAAG,IAAI,CAACM,IAAI,CAAC,GAAG,EAAE,IAAI,CAACC,GAAG,CAACP,eAAe,CAAC,EAAE,IAAI,CAACR,KAAK,CAACS,QAAQ,GAAG,IAAI,CAACK,IAAI,CAAC,MAAM,EAAE,IAAI,CAACC,GAAG,CAACN,QAAQ,CAAC,EAAE/P,CAAC;EACvQ;EACA;AACF;AACA;AACA;AACA;EACEyQ,eAAeA,CAAC;IAAEwC,KAAK,EAAEjT,CAAC;IAAEsM,KAAK,EAAEnM,CAAC;IAAE+S,WAAW,EAAE9S;EAAE,CAAC,EAAE;IACtD,IAAI,CAACkP,KAAK,CAACE,SAAS,CAAC3Q,WAAW,CAAC,IAAI,CAACyQ,KAAK,CAACK,WAAW,CAAC,EAAE3P,CAAC,IAAIA,CAAC,CAACuH,GAAG,KAAK,IAAI,CAAC+H,KAAK,CAACM,SAAS,CAAC9B,KAAK,CAACqF,eAAe,GAAG,MAAM,GAAGnT,CAAC,CAACuH,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC+H,KAAK,CAACK,WAAW,CAAC9Q,WAAW,CAAC,IAAI,CAACyQ,KAAK,CAACM,SAAS,CAAC,CAAC,EAAEzP,CAAC,KAAK,IAAI,CAACmP,KAAK,CAACO,SAAS,CAACkC,WAAW,GAAG5R,CAAC,EAAE,IAAI,CAACmP,KAAK,CAACK,WAAW,CAAC9Q,WAAW,CAAC,IAAI,CAACyQ,KAAK,CAACO,SAAS,CAAC,CAAC,EAAEzP,CAAC,KAAK,IAAI,CAACkP,KAAK,CAACQ,eAAe,CAACiC,WAAW,GAAG3R,CAAC,EAAE,IAAI,CAACkP,KAAK,CAACK,WAAW,CAAC9Q,WAAW,CAAC,IAAI,CAACyQ,KAAK,CAACQ,eAAe,CAAC,CAAC,EAAE,IAAI,CAACR,KAAK,CAACK,WAAW,CAACyC,SAAS,CAACgB,GAAG,CAAC,IAAI,CAAC/C,GAAG,CAACY,mBAAmB,CAAC,EAAE,IAAI,CAAC3B,KAAK,CAACK,WAAW,CAAC9B,YAAY,CAAC,MAAM,EAAE,IAAI,CAAChF,IAAI,CAACoH,IAAI,CAAC,EAAE,IAAI,CAACX,KAAK,CAACK,WAAW,CAAC9Q,WAAW,CAAC,IAAI,CAACyQ,KAAK,CAACS,QAAQ,CAAC;IACzlB,IAAI;MACF,IAAI,CAACT,KAAK,CAACS,QAAQ,CAACgC,WAAW,GAAG,IAAI5O,GAAG,CAAC,IAAI,CAAC0F,IAAI,CAACoH,IAAI,CAAC,CAACtL,QAAQ;IACpE,CAAC,CAAC,MAAM;MACN,IAAI,CAAC2K,KAAK,CAACS,QAAQ,CAACgC,WAAW,GAAG,IAAI,CAAClJ,IAAI,CAACoH,IAAI;IAClD;EACF;EACA;AACF;AACA;EACEoD,YAAYA,CAAA,EAAG;IACb,IAAI,CAAC/D,KAAK,CAACpH,QAAQ,CAACkK,SAAS,CAACgB,GAAG,CAAC,IAAI,CAAC/C,GAAG,CAACa,eAAe,CAAC;EAC7D;EACA;AACF;AACA;AACA;AACA;EACEoC,YAAYA,CAAA,EAAG;IACb,OAAO,IAAIpM,OAAO,CAAElH,CAAC,IAAK;MACxB,IAAI,CAACsP,KAAK,CAACpH,QAAQ,CAACkK,SAAS,CAACC,MAAM,CAAC,IAAI,CAAChC,GAAG,CAACa,eAAe,CAAC,EAAE,IAAI,CAAC5B,KAAK,CAACpH,QAAQ,CAACkK,SAAS,CAACgB,GAAG,CAAC,IAAI,CAAC/C,GAAG,CAACc,cAAc,CAAC,EAAE5L,UAAU,CAACvF,CAAC,EAAE,GAAG,CAAC;IAChJ,CAAC,CAAC;EACJ;EACA;AACF;AACA;EACEuT,eAAeA,CAAA,EAAG;IAChB,IAAI,CAACjE,KAAK,CAACI,WAAW,CAAC0C,SAAS,CAACgB,GAAG,CAAC,IAAI,CAAC/C,GAAG,CAACW,UAAU,CAAC,EAAE,IAAI,CAAC1B,KAAK,CAACpH,QAAQ,CAACmK,MAAM,CAAC,CAAC;EACzF;EACA;AACF;AACA;AACA;AACA;EACQF,aAAaA,CAACnS,CAAC,EAAE;IAAA,IAAAwT,KAAA;IAAA,OAAAC,iBAAA;MACrBD,KAAI,CAACH,YAAY,CAAC,CAAC,EAAEG,KAAI,CAAC3K,IAAI,GAAG;QAAEoH,IAAI,EAAEjQ;MAAE,CAAC;MAC5C,IAAI;QACF,MAAM;UAAE6D,IAAI,EAAE1D;QAAE,CAAC,SAASwO,CAAC,CAACxM,GAAG,CAAC;UAC9BoF,GAAG,EAAEiM,KAAI,CAACtE,MAAM,CAACG,QAAQ;UACzB5H,OAAO,EAAE+L,KAAI,CAACtE,MAAM,CAACzH,OAAO;UAC5BoB,IAAI,EAAE;YACJtB,GAAG,EAAEvH;UACP;QACF,CAAC,CAAC;QACFwT,KAAI,CAACE,OAAO,CAACvT,CAAC,CAAC;MACjB,CAAC,CAAC,MAAM;QACNqT,KAAI,CAACG,cAAc,CAACH,KAAI,CAACrE,GAAG,CAACoC,IAAI,CAACvS,CAAC,CAAC,8BAA8B,CAAC,CAAC;MACtE;IAAC;EACH;EACA;AACF;AACA;AACA;AACA;EACE0U,OAAOA,CAAC1T,CAAC,EAAE;IACT,IAAI,CAACA,CAAC,IAAI,CAACA,CAAC,CAAC4T,OAAO,EAAE;MACpB,IAAI,CAACD,cAAc,CAAC,IAAI,CAACxE,GAAG,CAACoC,IAAI,CAACvS,CAAC,CAAC,gDAAgD,CAAC,CAAC;MACtF;IACF;IACA,MAAMmB,CAAC,GAAGH,CAAC,CAACkQ,IAAI;MAAE9P,CAAC,GAAGJ,CAAC,CAACiQ,IAAI,IAAI,IAAI,CAACpH,IAAI,CAACoH,IAAI;IAC9C,IAAI,IAAI,CAACpH,IAAI,GAAG;MACdqH,IAAI,EAAE/P,CAAC;MACP8P,IAAI,EAAE7P;IACR,CAAC,EAAE,CAACD,CAAC,EAAE;MACL,IAAI,CAACwT,cAAc,CAAC,IAAI,CAACxE,GAAG,CAACoC,IAAI,CAACvS,CAAC,CAAC,uCAAuC,CAAC,CAAC;MAC7E;IACF;IACA,IAAI,CAACsU,YAAY,CAAC,CAAC,CAACtN,IAAI,CAAC,MAAM;MAC7B,IAAI,CAACsJ,KAAK,CAACI,WAAW,CAAC2C,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC5B,eAAe,CAACtQ,CAAC,CAAC;IAC1D,CAAC,CAAC;EACJ;EACA;AACF;AACA;AACA;AACA;AACA;AACA;EACEwT,cAAcA,CAAC3T,CAAC,EAAE;IAChB,IAAI,CAACmP,GAAG,CAAC0E,QAAQ,CAACC,IAAI,CAAC;MACrBC,OAAO,EAAE/T,CAAC;MACV8N,KAAK,EAAE;IACT,CAAC,CAAC,EAAE,IAAI,CAACyF,eAAe,CAAC,CAAC;EAC5B;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEnD,IAAIA,CAACpQ,CAAC,EAAEG,CAAC,GAAG,IAAI,EAAEC,CAAC,GAAG,CAAC,CAAC,EAAE;IACxB,MAAMO,CAAC,GAAGjC,QAAQ,CAACE,aAAa,CAACoB,CAAC,CAAC;IACnCuG,KAAK,CAACyN,OAAO,CAAC7T,CAAC,CAAC,GAAGQ,CAAC,CAACyR,SAAS,CAACgB,GAAG,CAAC,GAAGjT,CAAC,CAAC,GAAGA,CAAC,IAAIQ,CAAC,CAACyR,SAAS,CAACgB,GAAG,CAACjT,CAAC,CAAC;IAClE,KAAK,MAAMW,CAAC,IAAIV,CAAC,EACfO,CAAC,CAACG,CAAC,CAAC,GAAGV,CAAC,CAACU,CAAC,CAAC;IACb,OAAOH,CAAC;EACV;AACF;AACA,SACEkO,CAAC,IAAI9O,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}