{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { BehaviorSubject } from 'rxjs';\nlet ChatListService = class ChatListService {\n  constructor() {\n    this.groupedChats = {\n      Today: [],\n      Yesterday: [],\n      'Last 7 Days': [],\n      'Last 30 Days': [],\n      Older: []\n    };\n    // BehaviorSubject to store the current chat history\n    this.currentChatHistorySubject = new BehaviorSubject(null);\n    // Observable to expose the current chat history\n    this.currentChatHistory$ = this.currentChatHistorySubject.asObservable();\n    // Flag to indicate if we have a cached chat history\n    this.hasCachedHistory = false;\n    this.chatList = [];\n    this.chatId = 0;\n  }\n  /**\n   * Updates the current chat history in the service\n   * @param chatHistory The chat history to store\n   */\n  updateCurrentChatHistory(chatHistory) {\n    this.currentChatHistorySubject.next(chatHistory);\n    this.hasCachedHistory = !!chatHistory;\n  }\n  /**\n   * Checks if we have a cached chat history\n   */\n  hasCurrentChatHistory() {\n    return this.hasCachedHistory;\n  }\n  /**\n   * Gets the current chat history value\n   */\n  getCurrentChatHistory() {\n    return this.currentChatHistorySubject.value;\n  }\n  /**\n   * Clears the current chat history\n   */\n  clearCurrentChatHistory() {\n    this.currentChatHistorySubject.next(null);\n    this.hasCachedHistory = false;\n  }\n  groupChatsByDate() {\n    const now = new Date();\n    // Reset groupedChats before pushing new data\n    this.groupedChats = {\n      Today: [],\n      Yesterday: [],\n      'Last 7 Days': [],\n      'Last 30 Days': [],\n      Older: []\n    };\n    this.chatList.forEach(chat => {\n      const createdDate = new Date(chat.createdDate);\n      if (isNaN(createdDate.getTime())) {\n        return; // Skip invalid date\n      }\n      const diffTime = now.getTime() - createdDate.getTime();\n      const diffDays = diffTime / (1000 * 3600 * 24);\n      if (diffDays < 1) {\n        this.groupedChats['Today'].push(chat);\n      } else if (diffDays < 2) {\n        this.groupedChats['Yesterday'].push(chat);\n      } else if (diffDays < 8) {\n        this.groupedChats['Last 7 Days'].push(chat);\n      } else if (diffDays < 31) {\n        this.groupedChats['Last 30 Days'].push(chat);\n      } else {\n        this.groupedChats['Older'].push(chat);\n      }\n    });\n  }\n};\nChatListService = __decorate([Injectable({\n  providedIn: 'root'\n})], ChatListService);\nexport { ChatListService };", "map": {"version": 3, "names": ["Injectable", "BehaviorSubject", "ChatListService", "constructor", "groupedChats", "Today", "Yesterday", "Older", "currentChatHistorySubject", "currentChatHistory$", "asObservable", "hasCachedHistory", "chatList", "chatId", "updateCurrentChatHistory", "chatHistory", "next", "hasCurrentChatHistory", "getCurrentChatHistory", "value", "clearCurrentChatHistory", "groupChatsByDate", "now", "Date", "for<PERSON>ach", "chat", "createdDate", "isNaN", "getTime", "diffTime", "diffDays", "push", "__decorate", "providedIn"], "sources": ["C:\\Users\\<USER>\\source\\ai-hub\\WebApp\\src\\app\\services\\chat-list.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { ChatServiceProxy, ChatHistoryDto } from '../../shared/service-proxies/service-proxies';\r\nimport { BehaviorSubject, Observable } from 'rxjs';\r\n\r\nexport interface ChatHistoryItem {\r\n  id: number;\r\n  title: string;\r\n  timestamp?: Date;\r\n  lastMessage?: string;\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class ChatListService {\r\n  groupedChats: any = {\r\n    Today: [],\r\n    Yesterday: [],\r\n    'Last 7 Days': [],\r\n    'Last 30 Days': [],\r\n    Older: [],\r\n  };\r\n\r\n  // BehaviorSubject to store the current chat history\r\n  private currentChatHistorySubject = new BehaviorSubject<ChatHistoryDto | null>(null);\r\n\r\n  // Observable to expose the current chat history\r\n  currentChatHistory$ = this.currentChatHistorySubject.asObservable();\r\n\r\n  // Flag to indicate if we have a cached chat history\r\n  private hasCachedHistory = false;\r\n\r\n  chatList: any = [];\r\n  chatId: any = 0;\r\n\r\n  /**\r\n   * Updates the current chat history in the service\r\n   * @param chatHistory The chat history to store\r\n   */\r\n  updateCurrentChatHistory(chatHistory: ChatHistoryDto | null): void {\r\n    this.currentChatHistorySubject.next(chatHistory);\r\n    this.hasCachedHistory = !!chatHistory;\r\n  }\r\n\r\n  /**\r\n   * Checks if we have a cached chat history\r\n   */\r\n  hasCurrentChatHistory(): boolean {\r\n    return this.hasCachedHistory;\r\n  }\r\n\r\n  /**\r\n   * Gets the current chat history value\r\n   */\r\n  getCurrentChatHistory(): ChatHistoryDto | null {\r\n    return this.currentChatHistorySubject.value;\r\n  }\r\n\r\n  /**\r\n   * Clears the current chat history\r\n   */\r\n  clearCurrentChatHistory(): void {\r\n    this.currentChatHistorySubject.next(null);\r\n    this.hasCachedHistory = false;\r\n  }\r\n  groupChatsByDate() {\r\n    const now = new Date();\r\n\r\n    // Reset groupedChats before pushing new data\r\n    this.groupedChats = {\r\n      Today: [],\r\n      Yesterday: [],\r\n      'Last 7 Days': [],\r\n      'Last 30 Days': [],\r\n      Older: [],\r\n    };\r\n\r\n    this.chatList.forEach((chat: any) => {\r\n      const createdDate = new Date(chat.createdDate);\r\n\r\n      if (isNaN(createdDate.getTime())) {\r\n        return; // Skip invalid date\r\n      }\r\n\r\n      const diffTime = now.getTime() - createdDate.getTime();\r\n      const diffDays = diffTime / (1000 * 3600 * 24);\r\n\r\n      if (diffDays < 1) {\r\n        this.groupedChats['Today'].push(chat);\r\n      } else if (diffDays < 2) {\r\n        this.groupedChats['Yesterday'].push(chat);\r\n      } else if (diffDays < 8) {\r\n        this.groupedChats['Last 7 Days'].push(chat);\r\n      } else if (diffDays < 31) {\r\n        this.groupedChats['Last 30 Days'].push(chat);\r\n      } else {\r\n        this.groupedChats['Older'].push(chat);\r\n      }\r\n    });\r\n  }\r\n\r\n\r\n}\r\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,eAAe;AAE1C,SAASC,eAAe,QAAoB,MAAM;AAY3C,IAAMC,eAAe,GAArB,MAAMA,eAAe;EAArBC,YAAA;IACL,KAAAC,YAAY,GAAQ;MAClBC,KAAK,EAAE,EAAE;MACTC,SAAS,EAAE,EAAE;MACb,aAAa,EAAE,EAAE;MACjB,cAAc,EAAE,EAAE;MAClBC,KAAK,EAAE;KACR;IAED;IACQ,KAAAC,yBAAyB,GAAG,IAAIP,eAAe,CAAwB,IAAI,CAAC;IAEpF;IACA,KAAAQ,mBAAmB,GAAG,IAAI,CAACD,yBAAyB,CAACE,YAAY,EAAE;IAEnE;IACQ,KAAAC,gBAAgB,GAAG,KAAK;IAEhC,KAAAC,QAAQ,GAAQ,EAAE;IAClB,KAAAC,MAAM,GAAQ,CAAC;EAqEjB;EAnEE;;;;EAIAC,wBAAwBA,CAACC,WAAkC;IACzD,IAAI,CAACP,yBAAyB,CAACQ,IAAI,CAACD,WAAW,CAAC;IAChD,IAAI,CAACJ,gBAAgB,GAAG,CAAC,CAACI,WAAW;EACvC;EAEA;;;EAGAE,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAACN,gBAAgB;EAC9B;EAEA;;;EAGAO,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAACV,yBAAyB,CAACW,KAAK;EAC7C;EAEA;;;EAGAC,uBAAuBA,CAAA;IACrB,IAAI,CAACZ,yBAAyB,CAACQ,IAAI,CAAC,IAAI,CAAC;IACzC,IAAI,CAACL,gBAAgB,GAAG,KAAK;EAC/B;EACAU,gBAAgBA,CAAA;IACd,MAAMC,GAAG,GAAG,IAAIC,IAAI,EAAE;IAEtB;IACA,IAAI,CAACnB,YAAY,GAAG;MAClBC,KAAK,EAAE,EAAE;MACTC,SAAS,EAAE,EAAE;MACb,aAAa,EAAE,EAAE;MACjB,cAAc,EAAE,EAAE;MAClBC,KAAK,EAAE;KACR;IAED,IAAI,CAACK,QAAQ,CAACY,OAAO,CAAEC,IAAS,IAAI;MAClC,MAAMC,WAAW,GAAG,IAAIH,IAAI,CAACE,IAAI,CAACC,WAAW,CAAC;MAE9C,IAAIC,KAAK,CAACD,WAAW,CAACE,OAAO,EAAE,CAAC,EAAE;QAChC,OAAO,CAAC;;MAGV,MAAMC,QAAQ,GAAGP,GAAG,CAACM,OAAO,EAAE,GAAGF,WAAW,CAACE,OAAO,EAAE;MACtD,MAAME,QAAQ,GAAGD,QAAQ,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;MAE9C,IAAIC,QAAQ,GAAG,CAAC,EAAE;QAChB,IAAI,CAAC1B,YAAY,CAAC,OAAO,CAAC,CAAC2B,IAAI,CAACN,IAAI,CAAC;OACtC,MAAM,IAAIK,QAAQ,GAAG,CAAC,EAAE;QACvB,IAAI,CAAC1B,YAAY,CAAC,WAAW,CAAC,CAAC2B,IAAI,CAACN,IAAI,CAAC;OAC1C,MAAM,IAAIK,QAAQ,GAAG,CAAC,EAAE;QACvB,IAAI,CAAC1B,YAAY,CAAC,aAAa,CAAC,CAAC2B,IAAI,CAACN,IAAI,CAAC;OAC5C,MAAM,IAAIK,QAAQ,GAAG,EAAE,EAAE;QACxB,IAAI,CAAC1B,YAAY,CAAC,cAAc,CAAC,CAAC2B,IAAI,CAACN,IAAI,CAAC;OAC7C,MAAM;QACL,IAAI,CAACrB,YAAY,CAAC,OAAO,CAAC,CAAC2B,IAAI,CAACN,IAAI,CAAC;;IAEzC,CAAC,CAAC;EACJ;CAGD;AAxFYvB,eAAe,GAAA8B,UAAA,EAH3BhC,UAAU,CAAC;EACViC,UAAU,EAAE;CACb,CAAC,C,EACW/B,eAAe,CAwF3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}