{"ast": null, "code": "import { startOfWeek, startOf<PERSON>onth, setYear, addYears, setMonth, addMonths, setDay, isSameDay, isSameSecond, isSameMinute, isSameHour, isSameMonth, isSameYear, differenceInCalendarDays, differenceInSeconds, differenceInMinutes, differenceInHours, differenceInCalendarMonths, differenceInCalendarYears, isToday, isValid, isFirstDayOfMonth, isLastDayOfMonth } from 'date-fns';\nimport { warn } from 'ng-zorro-antd/core/logger';\nimport { getLocaleDayPeriods, FormStyle, TranslationWidth } from '@angular/common';\nimport { isNotNil } from 'ng-zorro-antd/core/util';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction wrongSortOrder(rangeValue) {\n  const [start, end] = rangeValue;\n  return !!start && !!end && end.isBeforeDay(start);\n}\nfunction normalizeRangeValue(value, hasTimePicker, type = 'month', activePart = 'left') {\n  const [start, end] = value;\n  let newStart = start || new CandyDate();\n  let newEnd = end || (hasTimePicker ? newStart : newStart.add(1, type));\n  if (start && !end) {\n    newStart = start;\n    newEnd = hasTimePicker ? start : start.add(1, type);\n  } else if (!start && end) {\n    newStart = hasTimePicker ? end : end.add(-1, type);\n    newEnd = end;\n  } else if (start && end && !hasTimePicker) {\n    if (start.isSame(end, type)) {\n      newEnd = newStart.add(1, type);\n    } else {\n      if (activePart === 'left') {\n        newEnd = newStart.add(1, type);\n      } else {\n        newStart = newEnd.add(-1, type);\n      }\n    }\n  }\n  return [newStart, newEnd];\n}\nfunction cloneDate(value) {\n  if (Array.isArray(value)) {\n    return value.map(v => v instanceof CandyDate ? v.clone() : null);\n  } else {\n    return value instanceof CandyDate ? value.clone() : null;\n  }\n}\n/**\n * Wrapping kind APIs for date operating and unify\n * NOTE: every new API return new CandyDate object without side effects to the former Date object\n * NOTE: most APIs are based on local time other than customized locale id (this needs tobe support in future)\n * TODO: support format() against to angular's core API\n */\nclass CandyDate {\n  // locale: string; // Custom specified locale ID\n  constructor(date) {\n    if (date) {\n      if (date instanceof Date) {\n        this.nativeDate = date;\n      } else if (typeof date === 'string' || typeof date === 'number') {\n        warn('The string type is not recommended for date-picker, use \"Date\" type');\n        this.nativeDate = new Date(date);\n      } else {\n        throw new Error('The input date type is not supported (\"Date\" is now recommended)');\n      }\n    } else {\n      this.nativeDate = new Date();\n    }\n  }\n  calendarStart(options) {\n    return new CandyDate(startOfWeek(startOfMonth(this.nativeDate), options));\n  }\n  // ---------------------------------------------------------------------\n  // | Native shortcuts\n  // -----------------------------------------------------------------------------\\\n  getYear() {\n    return this.nativeDate.getFullYear();\n  }\n  getMonth() {\n    return this.nativeDate.getMonth();\n  }\n  getDay() {\n    return this.nativeDate.getDay();\n  }\n  getTime() {\n    return this.nativeDate.getTime();\n  }\n  getDate() {\n    return this.nativeDate.getDate();\n  }\n  getHours() {\n    return this.nativeDate.getHours();\n  }\n  getMinutes() {\n    return this.nativeDate.getMinutes();\n  }\n  getSeconds() {\n    return this.nativeDate.getSeconds();\n  }\n  getMilliseconds() {\n    return this.nativeDate.getMilliseconds();\n  }\n  // ---------------------------------------------------------------------\n  // | New implementing APIs\n  // ---------------------------------------------------------------------\n  clone() {\n    return new CandyDate(new Date(this.nativeDate));\n  }\n  setHms(hour, minute, second) {\n    const newDate = new Date(this.nativeDate.setHours(hour, minute, second));\n    return new CandyDate(newDate);\n  }\n  setYear(year) {\n    return new CandyDate(setYear(this.nativeDate, year));\n  }\n  addYears(amount) {\n    return new CandyDate(addYears(this.nativeDate, amount));\n  }\n  // NOTE: month starts from 0\n  // NOTE: Don't use the native API for month manipulation as it not restrict the date when it overflows, eg. (new Date('2018-7-31')).setMonth(1) will be date of 2018-3-03 instead of 2018-2-28\n  setMonth(month) {\n    return new CandyDate(setMonth(this.nativeDate, month));\n  }\n  addMonths(amount) {\n    return new CandyDate(addMonths(this.nativeDate, amount));\n  }\n  setDay(day, options) {\n    return new CandyDate(setDay(this.nativeDate, day, options));\n  }\n  setDate(amount) {\n    const date = new Date(this.nativeDate);\n    date.setDate(amount);\n    return new CandyDate(date);\n  }\n  addDays(amount) {\n    return this.setDate(this.getDate() + amount);\n  }\n  add(amount, mode) {\n    switch (mode) {\n      case 'decade':\n        return this.addYears(amount * 10);\n      case 'year':\n        return this.addYears(amount);\n      case 'month':\n        return this.addMonths(amount);\n      default:\n        return this.addMonths(amount);\n    }\n  }\n  isSame(date, grain = 'day') {\n    let fn;\n    switch (grain) {\n      case 'decade':\n        fn = (pre, next) => Math.abs(pre.getFullYear() - next.getFullYear()) < 11;\n        break;\n      case 'year':\n        fn = isSameYear;\n        break;\n      case 'month':\n        fn = isSameMonth;\n        break;\n      case 'day':\n        fn = isSameDay;\n        break;\n      case 'hour':\n        fn = isSameHour;\n        break;\n      case 'minute':\n        fn = isSameMinute;\n        break;\n      case 'second':\n        fn = isSameSecond;\n        break;\n      default:\n        fn = isSameDay;\n        break;\n    }\n    return fn(this.nativeDate, this.toNativeDate(date));\n  }\n  isSameYear(date) {\n    return this.isSame(date, 'year');\n  }\n  isSameMonth(date) {\n    return this.isSame(date, 'month');\n  }\n  isSameDay(date) {\n    return this.isSame(date, 'day');\n  }\n  isSameHour(date) {\n    return this.isSame(date, 'hour');\n  }\n  isSameMinute(date) {\n    return this.isSame(date, 'minute');\n  }\n  isSameSecond(date) {\n    return this.isSame(date, 'second');\n  }\n  isBefore(date, grain = 'day') {\n    if (date === null) {\n      return false;\n    }\n    let fn;\n    switch (grain) {\n      case 'year':\n        fn = differenceInCalendarYears;\n        break;\n      case 'month':\n        fn = differenceInCalendarMonths;\n        break;\n      case 'day':\n        fn = differenceInCalendarDays;\n        break;\n      case 'hour':\n        fn = differenceInHours;\n        break;\n      case 'minute':\n        fn = differenceInMinutes;\n        break;\n      case 'second':\n        fn = differenceInSeconds;\n        break;\n      default:\n        fn = differenceInCalendarDays;\n        break;\n    }\n    return fn(this.nativeDate, this.toNativeDate(date)) < 0;\n  }\n  isBeforeYear(date) {\n    return this.isBefore(date, 'year');\n  }\n  isBeforeMonth(date) {\n    return this.isBefore(date, 'month');\n  }\n  isBeforeDay(date) {\n    return this.isBefore(date, 'day');\n  }\n  // Equal to today accurate to \"day\"\n  isToday() {\n    return isToday(this.nativeDate);\n  }\n  isValid() {\n    return isValid(this.nativeDate);\n  }\n  isFirstDayOfMonth() {\n    return isFirstDayOfMonth(this.nativeDate);\n  }\n  isLastDayOfMonth() {\n    return isLastDayOfMonth(this.nativeDate);\n  }\n  toNativeDate(date) {\n    return date instanceof CandyDate ? date.nativeDate : date;\n  }\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst timeUnits = [['Y', 1000 * 60 * 60 * 24 * 365],\n// years\n['M', 1000 * 60 * 60 * 24 * 30],\n// months\n['D', 1000 * 60 * 60 * 24],\n// days\n['H', 1000 * 60 * 60],\n// hours\n['m', 1000 * 60],\n// minutes\n['s', 1000],\n// seconds\n['S', 1] // million seconds\n];\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n// from https://github.com/hsuanxyz/ng-time-parser\nclass NgTimeParser {\n  constructor(format, localeId) {\n    this.format = format;\n    this.localeId = localeId;\n    this.regex = null;\n    this.matchMap = {\n      hour: null,\n      minute: null,\n      second: null,\n      periodNarrow: null,\n      periodWide: null,\n      periodAbbreviated: null\n    };\n    this.genRegexp();\n  }\n  toDate(str) {\n    const result = this.getTimeResult(str);\n    const time = new Date();\n    if (isNotNil(result?.hour)) {\n      time.setHours(result.hour);\n    }\n    if (isNotNil(result?.minute)) {\n      time.setMinutes(result.minute);\n    }\n    if (isNotNil(result?.second)) {\n      time.setSeconds(result.second);\n    }\n    if (result?.period === 1 && time.getHours() < 12) {\n      time.setHours(time.getHours() + 12);\n    }\n    return time;\n  }\n  getTimeResult(str) {\n    const match = this.regex.exec(str);\n    let period = null;\n    if (match) {\n      if (isNotNil(this.matchMap.periodNarrow)) {\n        period = getLocaleDayPeriods(this.localeId, FormStyle.Format, TranslationWidth.Narrow).indexOf(match[this.matchMap.periodNarrow + 1]);\n      }\n      if (isNotNil(this.matchMap.periodWide)) {\n        period = getLocaleDayPeriods(this.localeId, FormStyle.Format, TranslationWidth.Wide).indexOf(match[this.matchMap.periodWide + 1]);\n      }\n      if (isNotNil(this.matchMap.periodAbbreviated)) {\n        period = getLocaleDayPeriods(this.localeId, FormStyle.Format, TranslationWidth.Abbreviated).indexOf(match[this.matchMap.periodAbbreviated + 1]);\n      }\n      return {\n        hour: isNotNil(this.matchMap.hour) ? Number.parseInt(match[this.matchMap.hour + 1], 10) : null,\n        minute: isNotNil(this.matchMap.minute) ? Number.parseInt(match[this.matchMap.minute + 1], 10) : null,\n        second: isNotNil(this.matchMap.second) ? Number.parseInt(match[this.matchMap.second + 1], 10) : null,\n        period\n      };\n    } else {\n      return null;\n    }\n  }\n  genRegexp() {\n    let regexStr = this.format.replace(/([.*+?^=!:${}()|[\\]\\/\\\\])/g, '\\\\$&');\n    const hourRegex = /h{1,2}/i;\n    const minuteRegex = /m{1,2}/;\n    const secondRegex = /s{1,2}/;\n    const periodNarrow = /aaaaa/;\n    const periodWide = /aaaa/;\n    const periodAbbreviated = /a{1,3}/;\n    const hourMatch = hourRegex.exec(this.format);\n    const minuteMatch = minuteRegex.exec(this.format);\n    const secondMatch = secondRegex.exec(this.format);\n    const periodNarrowMatch = periodNarrow.exec(this.format);\n    let periodWideMatch = null;\n    let periodAbbreviatedMatch = null;\n    if (!periodNarrowMatch) {\n      periodWideMatch = periodWide.exec(this.format);\n    }\n    if (!periodWideMatch && !periodNarrowMatch) {\n      periodAbbreviatedMatch = periodAbbreviated.exec(this.format);\n    }\n    const matchs = [hourMatch, minuteMatch, secondMatch, periodNarrowMatch, periodWideMatch, periodAbbreviatedMatch].filter(m => !!m).sort((a, b) => a.index - b.index);\n    matchs.forEach((match, index) => {\n      switch (match) {\n        case hourMatch:\n          this.matchMap.hour = index;\n          regexStr = regexStr.replace(hourRegex, '(\\\\d{1,2})');\n          break;\n        case minuteMatch:\n          this.matchMap.minute = index;\n          regexStr = regexStr.replace(minuteRegex, '(\\\\d{1,2})');\n          break;\n        case secondMatch:\n          this.matchMap.second = index;\n          regexStr = regexStr.replace(secondRegex, '(\\\\d{1,2})');\n          break;\n        case periodNarrowMatch:\n          this.matchMap.periodNarrow = index;\n          const periodsNarrow = getLocaleDayPeriods(this.localeId, FormStyle.Format, TranslationWidth.Narrow).join('|');\n          regexStr = regexStr.replace(periodNarrow, `(${periodsNarrow})`);\n          break;\n        case periodWideMatch:\n          this.matchMap.periodWide = index;\n          const periodsWide = getLocaleDayPeriods(this.localeId, FormStyle.Format, TranslationWidth.Wide).join('|');\n          regexStr = regexStr.replace(periodWide, `(${periodsWide})`);\n          break;\n        case periodAbbreviatedMatch:\n          this.matchMap.periodAbbreviated = index;\n          const periodsAbbreviated = getLocaleDayPeriods(this.localeId, FormStyle.Format, TranslationWidth.Abbreviated).join('|');\n          regexStr = regexStr.replace(periodAbbreviated, `(${periodsAbbreviated})`);\n          break;\n      }\n    });\n    this.regex = new RegExp(regexStr);\n  }\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CandyDate, cloneDate, normalizeRangeValue, timeUnits, wrongSortOrder, NgTimeParser as ɵNgTimeParser };", "map": {"version": 3, "names": ["startOfWeek", "startOfMonth", "setYear", "addYears", "setMonth", "addMonths", "setDay", "isSameDay", "isSameSecond", "isSameMinute", "isSameHour", "isSameMonth", "isSameYear", "differenceInCalendarDays", "differenceInSeconds", "differenceInMinutes", "differenceInHours", "differenceInCalendarMonths", "differenceInCalendarYears", "isToday", "<PERSON><PERSON><PERSON><PERSON>", "isFirstDayOfMonth", "isLastDayOfMonth", "warn", "getLocaleDayPeriods", "FormStyle", "TranslationWidth", "isNotNil", "wrongSortOrder", "rangeValue", "start", "end", "isBeforeDay", "normalizeRangeValue", "value", "hasTimePicker", "type", "activePart", "newStart", "CandyDate", "newEnd", "add", "isSame", "cloneDate", "Array", "isArray", "map", "v", "clone", "constructor", "date", "Date", "nativeDate", "Error", "calendarStart", "options", "getYear", "getFullYear", "getMonth", "getDay", "getTime", "getDate", "getHours", "getMinutes", "getSeconds", "getMilliseconds", "setHms", "hour", "minute", "second", "newDate", "setHours", "year", "amount", "month", "day", "setDate", "addDays", "mode", "grain", "fn", "pre", "next", "Math", "abs", "toNativeDate", "isBefore", "isBeforeYear", "isBeforeMonth", "timeUnits", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "format", "localeId", "regex", "matchMap", "<PERSON><PERSON><PERSON><PERSON>", "periodWide", "periodAbbreviated", "genRegexp", "toDate", "str", "result", "getTimeResult", "time", "setMinutes", "setSeconds", "period", "match", "exec", "Format", "<PERSON>rrow", "indexOf", "Wide", "Abbreviated", "Number", "parseInt", "regexStr", "replace", "hourRegex", "minuteRegex", "secondRegex", "hourMatch", "minuteMatch", "secondMatch", "periodNarrowMatch", "periodWideMatch", "periodAbbreviatedMatch", "matchs", "filter", "m", "sort", "a", "b", "index", "for<PERSON>ach", "<PERSON><PERSON><PERSON><PERSON>", "join", "periodsWide", "periodsAbbreviated", "RegExp", "ɵNgTimeParser"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-core-time.mjs"], "sourcesContent": ["import { startOfWeek, startOf<PERSON>onth, setYear, addYears, setMonth, addMonths, setDay, isSameDay, isSameSecond, isSameMinute, isSameHour, isSameMonth, isSameYear, differenceInCalendarDays, differenceInSeconds, differenceInMinutes, differenceInHours, differenceInCalendarMonths, differenceInCalendarYears, isToday, isValid, isFirstDayOfMonth, isLastDayOfMonth } from 'date-fns';\nimport { warn } from 'ng-zorro-antd/core/logger';\nimport { getLocaleDayPeriods, FormStyle, TranslationWidth } from '@angular/common';\nimport { isNotNil } from 'ng-zorro-antd/core/util';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction wrongSortOrder(rangeValue) {\n    const [start, end] = rangeValue;\n    return !!start && !!end && end.isBeforeDay(start);\n}\nfunction normalizeRangeValue(value, hasTimePicker, type = 'month', activePart = 'left') {\n    const [start, end] = value;\n    let newStart = start || new CandyDate();\n    let newEnd = end || (hasTimePicker ? newStart : newStart.add(1, type));\n    if (start && !end) {\n        newStart = start;\n        newEnd = hasTimePicker ? start : start.add(1, type);\n    }\n    else if (!start && end) {\n        newStart = hasTimePicker ? end : end.add(-1, type);\n        newEnd = end;\n    }\n    else if (start && end && !hasTimePicker) {\n        if (start.isSame(end, type)) {\n            newEnd = newStart.add(1, type);\n        }\n        else {\n            if (activePart === 'left') {\n                newEnd = newStart.add(1, type);\n            }\n            else {\n                newStart = newEnd.add(-1, type);\n            }\n        }\n    }\n    return [newStart, newEnd];\n}\nfunction cloneDate(value) {\n    if (Array.isArray(value)) {\n        return value.map(v => (v instanceof CandyDate ? v.clone() : null));\n    }\n    else {\n        return value instanceof CandyDate ? value.clone() : null;\n    }\n}\n/**\n * Wrapping kind APIs for date operating and unify\n * NOTE: every new API return new CandyDate object without side effects to the former Date object\n * NOTE: most APIs are based on local time other than customized locale id (this needs tobe support in future)\n * TODO: support format() against to angular's core API\n */\nclass CandyDate {\n    // locale: string; // Custom specified locale ID\n    constructor(date) {\n        if (date) {\n            if (date instanceof Date) {\n                this.nativeDate = date;\n            }\n            else if (typeof date === 'string' || typeof date === 'number') {\n                warn('The string type is not recommended for date-picker, use \"Date\" type');\n                this.nativeDate = new Date(date);\n            }\n            else {\n                throw new Error('The input date type is not supported (\"Date\" is now recommended)');\n            }\n        }\n        else {\n            this.nativeDate = new Date();\n        }\n    }\n    calendarStart(options) {\n        return new CandyDate(startOfWeek(startOfMonth(this.nativeDate), options));\n    }\n    // ---------------------------------------------------------------------\n    // | Native shortcuts\n    // -----------------------------------------------------------------------------\\\n    getYear() {\n        return this.nativeDate.getFullYear();\n    }\n    getMonth() {\n        return this.nativeDate.getMonth();\n    }\n    getDay() {\n        return this.nativeDate.getDay();\n    }\n    getTime() {\n        return this.nativeDate.getTime();\n    }\n    getDate() {\n        return this.nativeDate.getDate();\n    }\n    getHours() {\n        return this.nativeDate.getHours();\n    }\n    getMinutes() {\n        return this.nativeDate.getMinutes();\n    }\n    getSeconds() {\n        return this.nativeDate.getSeconds();\n    }\n    getMilliseconds() {\n        return this.nativeDate.getMilliseconds();\n    }\n    // ---------------------------------------------------------------------\n    // | New implementing APIs\n    // ---------------------------------------------------------------------\n    clone() {\n        return new CandyDate(new Date(this.nativeDate));\n    }\n    setHms(hour, minute, second) {\n        const newDate = new Date(this.nativeDate.setHours(hour, minute, second));\n        return new CandyDate(newDate);\n    }\n    setYear(year) {\n        return new CandyDate(setYear(this.nativeDate, year));\n    }\n    addYears(amount) {\n        return new CandyDate(addYears(this.nativeDate, amount));\n    }\n    // NOTE: month starts from 0\n    // NOTE: Don't use the native API for month manipulation as it not restrict the date when it overflows, eg. (new Date('2018-7-31')).setMonth(1) will be date of 2018-3-03 instead of 2018-2-28\n    setMonth(month) {\n        return new CandyDate(setMonth(this.nativeDate, month));\n    }\n    addMonths(amount) {\n        return new CandyDate(addMonths(this.nativeDate, amount));\n    }\n    setDay(day, options) {\n        return new CandyDate(setDay(this.nativeDate, day, options));\n    }\n    setDate(amount) {\n        const date = new Date(this.nativeDate);\n        date.setDate(amount);\n        return new CandyDate(date);\n    }\n    addDays(amount) {\n        return this.setDate(this.getDate() + amount);\n    }\n    add(amount, mode) {\n        switch (mode) {\n            case 'decade':\n                return this.addYears(amount * 10);\n            case 'year':\n                return this.addYears(amount);\n            case 'month':\n                return this.addMonths(amount);\n            default:\n                return this.addMonths(amount);\n        }\n    }\n    isSame(date, grain = 'day') {\n        let fn;\n        switch (grain) {\n            case 'decade':\n                fn = (pre, next) => Math.abs(pre.getFullYear() - next.getFullYear()) < 11;\n                break;\n            case 'year':\n                fn = isSameYear;\n                break;\n            case 'month':\n                fn = isSameMonth;\n                break;\n            case 'day':\n                fn = isSameDay;\n                break;\n            case 'hour':\n                fn = isSameHour;\n                break;\n            case 'minute':\n                fn = isSameMinute;\n                break;\n            case 'second':\n                fn = isSameSecond;\n                break;\n            default:\n                fn = isSameDay;\n                break;\n        }\n        return fn(this.nativeDate, this.toNativeDate(date));\n    }\n    isSameYear(date) {\n        return this.isSame(date, 'year');\n    }\n    isSameMonth(date) {\n        return this.isSame(date, 'month');\n    }\n    isSameDay(date) {\n        return this.isSame(date, 'day');\n    }\n    isSameHour(date) {\n        return this.isSame(date, 'hour');\n    }\n    isSameMinute(date) {\n        return this.isSame(date, 'minute');\n    }\n    isSameSecond(date) {\n        return this.isSame(date, 'second');\n    }\n    isBefore(date, grain = 'day') {\n        if (date === null) {\n            return false;\n        }\n        let fn;\n        switch (grain) {\n            case 'year':\n                fn = differenceInCalendarYears;\n                break;\n            case 'month':\n                fn = differenceInCalendarMonths;\n                break;\n            case 'day':\n                fn = differenceInCalendarDays;\n                break;\n            case 'hour':\n                fn = differenceInHours;\n                break;\n            case 'minute':\n                fn = differenceInMinutes;\n                break;\n            case 'second':\n                fn = differenceInSeconds;\n                break;\n            default:\n                fn = differenceInCalendarDays;\n                break;\n        }\n        return fn(this.nativeDate, this.toNativeDate(date)) < 0;\n    }\n    isBeforeYear(date) {\n        return this.isBefore(date, 'year');\n    }\n    isBeforeMonth(date) {\n        return this.isBefore(date, 'month');\n    }\n    isBeforeDay(date) {\n        return this.isBefore(date, 'day');\n    }\n    // Equal to today accurate to \"day\"\n    isToday() {\n        return isToday(this.nativeDate);\n    }\n    isValid() {\n        return isValid(this.nativeDate);\n    }\n    isFirstDayOfMonth() {\n        return isFirstDayOfMonth(this.nativeDate);\n    }\n    isLastDayOfMonth() {\n        return isLastDayOfMonth(this.nativeDate);\n    }\n    toNativeDate(date) {\n        return date instanceof CandyDate ? date.nativeDate : date;\n    }\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst timeUnits = [\n    ['Y', 1000 * 60 * 60 * 24 * 365], // years\n    ['M', 1000 * 60 * 60 * 24 * 30], // months\n    ['D', 1000 * 60 * 60 * 24], // days\n    ['H', 1000 * 60 * 60], // hours\n    ['m', 1000 * 60], // minutes\n    ['s', 1000], // seconds\n    ['S', 1] // million seconds\n];\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n// from https://github.com/hsuanxyz/ng-time-parser\nclass NgTimeParser {\n    constructor(format, localeId) {\n        this.format = format;\n        this.localeId = localeId;\n        this.regex = null;\n        this.matchMap = {\n            hour: null,\n            minute: null,\n            second: null,\n            periodNarrow: null,\n            periodWide: null,\n            periodAbbreviated: null\n        };\n        this.genRegexp();\n    }\n    toDate(str) {\n        const result = this.getTimeResult(str);\n        const time = new Date();\n        if (isNotNil(result?.hour)) {\n            time.setHours(result.hour);\n        }\n        if (isNotNil(result?.minute)) {\n            time.setMinutes(result.minute);\n        }\n        if (isNotNil(result?.second)) {\n            time.setSeconds(result.second);\n        }\n        if (result?.period === 1 && time.getHours() < 12) {\n            time.setHours(time.getHours() + 12);\n        }\n        return time;\n    }\n    getTimeResult(str) {\n        const match = this.regex.exec(str);\n        let period = null;\n        if (match) {\n            if (isNotNil(this.matchMap.periodNarrow)) {\n                period = getLocaleDayPeriods(this.localeId, FormStyle.Format, TranslationWidth.Narrow).indexOf(match[this.matchMap.periodNarrow + 1]);\n            }\n            if (isNotNil(this.matchMap.periodWide)) {\n                period = getLocaleDayPeriods(this.localeId, FormStyle.Format, TranslationWidth.Wide).indexOf(match[this.matchMap.periodWide + 1]);\n            }\n            if (isNotNil(this.matchMap.periodAbbreviated)) {\n                period = getLocaleDayPeriods(this.localeId, FormStyle.Format, TranslationWidth.Abbreviated).indexOf(match[this.matchMap.periodAbbreviated + 1]);\n            }\n            return {\n                hour: isNotNil(this.matchMap.hour) ? Number.parseInt(match[this.matchMap.hour + 1], 10) : null,\n                minute: isNotNil(this.matchMap.minute) ? Number.parseInt(match[this.matchMap.minute + 1], 10) : null,\n                second: isNotNil(this.matchMap.second) ? Number.parseInt(match[this.matchMap.second + 1], 10) : null,\n                period\n            };\n        }\n        else {\n            return null;\n        }\n    }\n    genRegexp() {\n        let regexStr = this.format.replace(/([.*+?^=!:${}()|[\\]\\/\\\\])/g, '\\\\$&');\n        const hourRegex = /h{1,2}/i;\n        const minuteRegex = /m{1,2}/;\n        const secondRegex = /s{1,2}/;\n        const periodNarrow = /aaaaa/;\n        const periodWide = /aaaa/;\n        const periodAbbreviated = /a{1,3}/;\n        const hourMatch = hourRegex.exec(this.format);\n        const minuteMatch = minuteRegex.exec(this.format);\n        const secondMatch = secondRegex.exec(this.format);\n        const periodNarrowMatch = periodNarrow.exec(this.format);\n        let periodWideMatch = null;\n        let periodAbbreviatedMatch = null;\n        if (!periodNarrowMatch) {\n            periodWideMatch = periodWide.exec(this.format);\n        }\n        if (!periodWideMatch && !periodNarrowMatch) {\n            periodAbbreviatedMatch = periodAbbreviated.exec(this.format);\n        }\n        const matchs = [hourMatch, minuteMatch, secondMatch, periodNarrowMatch, periodWideMatch, periodAbbreviatedMatch]\n            .filter(m => !!m)\n            .sort((a, b) => a.index - b.index);\n        matchs.forEach((match, index) => {\n            switch (match) {\n                case hourMatch:\n                    this.matchMap.hour = index;\n                    regexStr = regexStr.replace(hourRegex, '(\\\\d{1,2})');\n                    break;\n                case minuteMatch:\n                    this.matchMap.minute = index;\n                    regexStr = regexStr.replace(minuteRegex, '(\\\\d{1,2})');\n                    break;\n                case secondMatch:\n                    this.matchMap.second = index;\n                    regexStr = regexStr.replace(secondRegex, '(\\\\d{1,2})');\n                    break;\n                case periodNarrowMatch:\n                    this.matchMap.periodNarrow = index;\n                    const periodsNarrow = getLocaleDayPeriods(this.localeId, FormStyle.Format, TranslationWidth.Narrow).join('|');\n                    regexStr = regexStr.replace(periodNarrow, `(${periodsNarrow})`);\n                    break;\n                case periodWideMatch:\n                    this.matchMap.periodWide = index;\n                    const periodsWide = getLocaleDayPeriods(this.localeId, FormStyle.Format, TranslationWidth.Wide).join('|');\n                    regexStr = regexStr.replace(periodWide, `(${periodsWide})`);\n                    break;\n                case periodAbbreviatedMatch:\n                    this.matchMap.periodAbbreviated = index;\n                    const periodsAbbreviated = getLocaleDayPeriods(this.localeId, FormStyle.Format, TranslationWidth.Abbreviated).join('|');\n                    regexStr = regexStr.replace(periodAbbreviated, `(${periodsAbbreviated})`);\n                    break;\n            }\n        });\n        this.regex = new RegExp(regexStr);\n    }\n}\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CandyDate, cloneDate, normalizeRangeValue, timeUnits, wrongSortOrder, NgTimeParser as ɵNgTimeParser };\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,YAAY,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,SAAS,EAAEC,YAAY,EAAEC,YAAY,EAAEC,UAAU,EAAEC,WAAW,EAAEC,UAAU,EAAEC,wBAAwB,EAAEC,mBAAmB,EAAEC,mBAAmB,EAAEC,iBAAiB,EAAEC,0BAA0B,EAAEC,yBAAyB,EAAEC,OAAO,EAAEC,OAAO,EAAEC,iBAAiB,EAAEC,gBAAgB,QAAQ,UAAU;AACrX,SAASC,IAAI,QAAQ,2BAA2B;AAChD,SAASC,mBAAmB,EAAEC,SAAS,EAAEC,gBAAgB,QAAQ,iBAAiB;AAClF,SAASC,QAAQ,QAAQ,yBAAyB;;AAElD;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACC,UAAU,EAAE;EAChC,MAAM,CAACC,KAAK,EAAEC,GAAG,CAAC,GAAGF,UAAU;EAC/B,OAAO,CAAC,CAACC,KAAK,IAAI,CAAC,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,CAACF,KAAK,CAAC;AACrD;AACA,SAASG,mBAAmBA,CAACC,KAAK,EAAEC,aAAa,EAAEC,IAAI,GAAG,OAAO,EAAEC,UAAU,GAAG,MAAM,EAAE;EACpF,MAAM,CAACP,KAAK,EAAEC,GAAG,CAAC,GAAGG,KAAK;EAC1B,IAAII,QAAQ,GAAGR,KAAK,IAAI,IAAIS,SAAS,CAAC,CAAC;EACvC,IAAIC,MAAM,GAAGT,GAAG,KAAKI,aAAa,GAAGG,QAAQ,GAAGA,QAAQ,CAACG,GAAG,CAAC,CAAC,EAAEL,IAAI,CAAC,CAAC;EACtE,IAAIN,KAAK,IAAI,CAACC,GAAG,EAAE;IACfO,QAAQ,GAAGR,KAAK;IAChBU,MAAM,GAAGL,aAAa,GAAGL,KAAK,GAAGA,KAAK,CAACW,GAAG,CAAC,CAAC,EAAEL,IAAI,CAAC;EACvD,CAAC,MACI,IAAI,CAACN,KAAK,IAAIC,GAAG,EAAE;IACpBO,QAAQ,GAAGH,aAAa,GAAGJ,GAAG,GAAGA,GAAG,CAACU,GAAG,CAAC,CAAC,CAAC,EAAEL,IAAI,CAAC;IAClDI,MAAM,GAAGT,GAAG;EAChB,CAAC,MACI,IAAID,KAAK,IAAIC,GAAG,IAAI,CAACI,aAAa,EAAE;IACrC,IAAIL,KAAK,CAACY,MAAM,CAACX,GAAG,EAAEK,IAAI,CAAC,EAAE;MACzBI,MAAM,GAAGF,QAAQ,CAACG,GAAG,CAAC,CAAC,EAAEL,IAAI,CAAC;IAClC,CAAC,MACI;MACD,IAAIC,UAAU,KAAK,MAAM,EAAE;QACvBG,MAAM,GAAGF,QAAQ,CAACG,GAAG,CAAC,CAAC,EAAEL,IAAI,CAAC;MAClC,CAAC,MACI;QACDE,QAAQ,GAAGE,MAAM,CAACC,GAAG,CAAC,CAAC,CAAC,EAAEL,IAAI,CAAC;MACnC;IACJ;EACJ;EACA,OAAO,CAACE,QAAQ,EAAEE,MAAM,CAAC;AAC7B;AACA,SAASG,SAASA,CAACT,KAAK,EAAE;EACtB,IAAIU,KAAK,CAACC,OAAO,CAACX,KAAK,CAAC,EAAE;IACtB,OAAOA,KAAK,CAACY,GAAG,CAACC,CAAC,IAAKA,CAAC,YAAYR,SAAS,GAAGQ,CAAC,CAACC,KAAK,CAAC,CAAC,GAAG,IAAK,CAAC;EACtE,CAAC,MACI;IACD,OAAOd,KAAK,YAAYK,SAAS,GAAGL,KAAK,CAACc,KAAK,CAAC,CAAC,GAAG,IAAI;EAC5D;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMT,SAAS,CAAC;EACZ;EACAU,WAAWA,CAACC,IAAI,EAAE;IACd,IAAIA,IAAI,EAAE;MACN,IAAIA,IAAI,YAAYC,IAAI,EAAE;QACtB,IAAI,CAACC,UAAU,GAAGF,IAAI;MAC1B,CAAC,MACI,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;QAC3D3B,IAAI,CAAC,qEAAqE,CAAC;QAC3E,IAAI,CAAC6B,UAAU,GAAG,IAAID,IAAI,CAACD,IAAI,CAAC;MACpC,CAAC,MACI;QACD,MAAM,IAAIG,KAAK,CAAC,kEAAkE,CAAC;MACvF;IACJ,CAAC,MACI;MACD,IAAI,CAACD,UAAU,GAAG,IAAID,IAAI,CAAC,CAAC;IAChC;EACJ;EACAG,aAAaA,CAACC,OAAO,EAAE;IACnB,OAAO,IAAIhB,SAAS,CAACvC,WAAW,CAACC,YAAY,CAAC,IAAI,CAACmD,UAAU,CAAC,EAAEG,OAAO,CAAC,CAAC;EAC7E;EACA;EACA;EACA;EACAC,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACJ,UAAU,CAACK,WAAW,CAAC,CAAC;EACxC;EACAC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACN,UAAU,CAACM,QAAQ,CAAC,CAAC;EACrC;EACAC,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAACP,UAAU,CAACO,MAAM,CAAC,CAAC;EACnC;EACAC,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACR,UAAU,CAACQ,OAAO,CAAC,CAAC;EACpC;EACAC,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACT,UAAU,CAACS,OAAO,CAAC,CAAC;EACpC;EACAC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACV,UAAU,CAACU,QAAQ,CAAC,CAAC;EACrC;EACAC,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACX,UAAU,CAACW,UAAU,CAAC,CAAC;EACvC;EACAC,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACZ,UAAU,CAACY,UAAU,CAAC,CAAC;EACvC;EACAC,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAACb,UAAU,CAACa,eAAe,CAAC,CAAC;EAC5C;EACA;EACA;EACA;EACAjB,KAAKA,CAAA,EAAG;IACJ,OAAO,IAAIT,SAAS,CAAC,IAAIY,IAAI,CAAC,IAAI,CAACC,UAAU,CAAC,CAAC;EACnD;EACAc,MAAMA,CAACC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAE;IACzB,MAAMC,OAAO,GAAG,IAAInB,IAAI,CAAC,IAAI,CAACC,UAAU,CAACmB,QAAQ,CAACJ,IAAI,EAAEC,MAAM,EAAEC,MAAM,CAAC,CAAC;IACxE,OAAO,IAAI9B,SAAS,CAAC+B,OAAO,CAAC;EACjC;EACApE,OAAOA,CAACsE,IAAI,EAAE;IACV,OAAO,IAAIjC,SAAS,CAACrC,OAAO,CAAC,IAAI,CAACkD,UAAU,EAAEoB,IAAI,CAAC,CAAC;EACxD;EACArE,QAAQA,CAACsE,MAAM,EAAE;IACb,OAAO,IAAIlC,SAAS,CAACpC,QAAQ,CAAC,IAAI,CAACiD,UAAU,EAAEqB,MAAM,CAAC,CAAC;EAC3D;EACA;EACA;EACArE,QAAQA,CAACsE,KAAK,EAAE;IACZ,OAAO,IAAInC,SAAS,CAACnC,QAAQ,CAAC,IAAI,CAACgD,UAAU,EAAEsB,KAAK,CAAC,CAAC;EAC1D;EACArE,SAASA,CAACoE,MAAM,EAAE;IACd,OAAO,IAAIlC,SAAS,CAAClC,SAAS,CAAC,IAAI,CAAC+C,UAAU,EAAEqB,MAAM,CAAC,CAAC;EAC5D;EACAnE,MAAMA,CAACqE,GAAG,EAAEpB,OAAO,EAAE;IACjB,OAAO,IAAIhB,SAAS,CAACjC,MAAM,CAAC,IAAI,CAAC8C,UAAU,EAAEuB,GAAG,EAAEpB,OAAO,CAAC,CAAC;EAC/D;EACAqB,OAAOA,CAACH,MAAM,EAAE;IACZ,MAAMvB,IAAI,GAAG,IAAIC,IAAI,CAAC,IAAI,CAACC,UAAU,CAAC;IACtCF,IAAI,CAAC0B,OAAO,CAACH,MAAM,CAAC;IACpB,OAAO,IAAIlC,SAAS,CAACW,IAAI,CAAC;EAC9B;EACA2B,OAAOA,CAACJ,MAAM,EAAE;IACZ,OAAO,IAAI,CAACG,OAAO,CAAC,IAAI,CAACf,OAAO,CAAC,CAAC,GAAGY,MAAM,CAAC;EAChD;EACAhC,GAAGA,CAACgC,MAAM,EAAEK,IAAI,EAAE;IACd,QAAQA,IAAI;MACR,KAAK,QAAQ;QACT,OAAO,IAAI,CAAC3E,QAAQ,CAACsE,MAAM,GAAG,EAAE,CAAC;MACrC,KAAK,MAAM;QACP,OAAO,IAAI,CAACtE,QAAQ,CAACsE,MAAM,CAAC;MAChC,KAAK,OAAO;QACR,OAAO,IAAI,CAACpE,SAAS,CAACoE,MAAM,CAAC;MACjC;QACI,OAAO,IAAI,CAACpE,SAAS,CAACoE,MAAM,CAAC;IACrC;EACJ;EACA/B,MAAMA,CAACQ,IAAI,EAAE6B,KAAK,GAAG,KAAK,EAAE;IACxB,IAAIC,EAAE;IACN,QAAQD,KAAK;MACT,KAAK,QAAQ;QACTC,EAAE,GAAGA,CAACC,GAAG,EAAEC,IAAI,KAAKC,IAAI,CAACC,GAAG,CAACH,GAAG,CAACxB,WAAW,CAAC,CAAC,GAAGyB,IAAI,CAACzB,WAAW,CAAC,CAAC,CAAC,GAAG,EAAE;QACzE;MACJ,KAAK,MAAM;QACPuB,EAAE,GAAGpE,UAAU;QACf;MACJ,KAAK,OAAO;QACRoE,EAAE,GAAGrE,WAAW;QAChB;MACJ,KAAK,KAAK;QACNqE,EAAE,GAAGzE,SAAS;QACd;MACJ,KAAK,MAAM;QACPyE,EAAE,GAAGtE,UAAU;QACf;MACJ,KAAK,QAAQ;QACTsE,EAAE,GAAGvE,YAAY;QACjB;MACJ,KAAK,QAAQ;QACTuE,EAAE,GAAGxE,YAAY;QACjB;MACJ;QACIwE,EAAE,GAAGzE,SAAS;QACd;IACR;IACA,OAAOyE,EAAE,CAAC,IAAI,CAAC5B,UAAU,EAAE,IAAI,CAACiC,YAAY,CAACnC,IAAI,CAAC,CAAC;EACvD;EACAtC,UAAUA,CAACsC,IAAI,EAAE;IACb,OAAO,IAAI,CAACR,MAAM,CAACQ,IAAI,EAAE,MAAM,CAAC;EACpC;EACAvC,WAAWA,CAACuC,IAAI,EAAE;IACd,OAAO,IAAI,CAACR,MAAM,CAACQ,IAAI,EAAE,OAAO,CAAC;EACrC;EACA3C,SAASA,CAAC2C,IAAI,EAAE;IACZ,OAAO,IAAI,CAACR,MAAM,CAACQ,IAAI,EAAE,KAAK,CAAC;EACnC;EACAxC,UAAUA,CAACwC,IAAI,EAAE;IACb,OAAO,IAAI,CAACR,MAAM,CAACQ,IAAI,EAAE,MAAM,CAAC;EACpC;EACAzC,YAAYA,CAACyC,IAAI,EAAE;IACf,OAAO,IAAI,CAACR,MAAM,CAACQ,IAAI,EAAE,QAAQ,CAAC;EACtC;EACA1C,YAAYA,CAAC0C,IAAI,EAAE;IACf,OAAO,IAAI,CAACR,MAAM,CAACQ,IAAI,EAAE,QAAQ,CAAC;EACtC;EACAoC,QAAQA,CAACpC,IAAI,EAAE6B,KAAK,GAAG,KAAK,EAAE;IAC1B,IAAI7B,IAAI,KAAK,IAAI,EAAE;MACf,OAAO,KAAK;IAChB;IACA,IAAI8B,EAAE;IACN,QAAQD,KAAK;MACT,KAAK,MAAM;QACPC,EAAE,GAAG9D,yBAAyB;QAC9B;MACJ,KAAK,OAAO;QACR8D,EAAE,GAAG/D,0BAA0B;QAC/B;MACJ,KAAK,KAAK;QACN+D,EAAE,GAAGnE,wBAAwB;QAC7B;MACJ,KAAK,MAAM;QACPmE,EAAE,GAAGhE,iBAAiB;QACtB;MACJ,KAAK,QAAQ;QACTgE,EAAE,GAAGjE,mBAAmB;QACxB;MACJ,KAAK,QAAQ;QACTiE,EAAE,GAAGlE,mBAAmB;QACxB;MACJ;QACIkE,EAAE,GAAGnE,wBAAwB;QAC7B;IACR;IACA,OAAOmE,EAAE,CAAC,IAAI,CAAC5B,UAAU,EAAE,IAAI,CAACiC,YAAY,CAACnC,IAAI,CAAC,CAAC,GAAG,CAAC;EAC3D;EACAqC,YAAYA,CAACrC,IAAI,EAAE;IACf,OAAO,IAAI,CAACoC,QAAQ,CAACpC,IAAI,EAAE,MAAM,CAAC;EACtC;EACAsC,aAAaA,CAACtC,IAAI,EAAE;IAChB,OAAO,IAAI,CAACoC,QAAQ,CAACpC,IAAI,EAAE,OAAO,CAAC;EACvC;EACAlB,WAAWA,CAACkB,IAAI,EAAE;IACd,OAAO,IAAI,CAACoC,QAAQ,CAACpC,IAAI,EAAE,KAAK,CAAC;EACrC;EACA;EACA/B,OAAOA,CAAA,EAAG;IACN,OAAOA,OAAO,CAAC,IAAI,CAACiC,UAAU,CAAC;EACnC;EACAhC,OAAOA,CAAA,EAAG;IACN,OAAOA,OAAO,CAAC,IAAI,CAACgC,UAAU,CAAC;EACnC;EACA/B,iBAAiBA,CAAA,EAAG;IAChB,OAAOA,iBAAiB,CAAC,IAAI,CAAC+B,UAAU,CAAC;EAC7C;EACA9B,gBAAgBA,CAAA,EAAG;IACf,OAAOA,gBAAgB,CAAC,IAAI,CAAC8B,UAAU,CAAC;EAC5C;EACAiC,YAAYA,CAACnC,IAAI,EAAE;IACf,OAAOA,IAAI,YAAYX,SAAS,GAAGW,IAAI,CAACE,UAAU,GAAGF,IAAI;EAC7D;AACJ;;AAEA;AACA;AACA;AACA;AACA,MAAMuC,SAAS,GAAG,CACd,CAAC,GAAG,EAAE,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC;AAAE;AAClC,CAAC,GAAG,EAAE,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AAAE;AACjC,CAAC,GAAG,EAAE,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AAAE;AAC5B,CAAC,GAAG,EAAE,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;AAAE;AACvB,CAAC,GAAG,EAAE,IAAI,GAAG,EAAE,CAAC;AAAE;AAClB,CAAC,GAAG,EAAE,IAAI,CAAC;AAAE;AACb,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAAA,CACZ;;AAED;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,CAAC;EACfzC,WAAWA,CAAC0C,MAAM,EAAEC,QAAQ,EAAE;IAC1B,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,KAAK,GAAG,IAAI;IACjB,IAAI,CAACC,QAAQ,GAAG;MACZ3B,IAAI,EAAE,IAAI;MACVC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZ0B,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE,IAAI;MAChBC,iBAAiB,EAAE;IACvB,CAAC;IACD,IAAI,CAACC,SAAS,CAAC,CAAC;EACpB;EACAC,MAAMA,CAACC,GAAG,EAAE;IACR,MAAMC,MAAM,GAAG,IAAI,CAACC,aAAa,CAACF,GAAG,CAAC;IACtC,MAAMG,IAAI,GAAG,IAAIpD,IAAI,CAAC,CAAC;IACvB,IAAIxB,QAAQ,CAAC0E,MAAM,EAAElC,IAAI,CAAC,EAAE;MACxBoC,IAAI,CAAChC,QAAQ,CAAC8B,MAAM,CAAClC,IAAI,CAAC;IAC9B;IACA,IAAIxC,QAAQ,CAAC0E,MAAM,EAAEjC,MAAM,CAAC,EAAE;MAC1BmC,IAAI,CAACC,UAAU,CAACH,MAAM,CAACjC,MAAM,CAAC;IAClC;IACA,IAAIzC,QAAQ,CAAC0E,MAAM,EAAEhC,MAAM,CAAC,EAAE;MAC1BkC,IAAI,CAACE,UAAU,CAACJ,MAAM,CAAChC,MAAM,CAAC;IAClC;IACA,IAAIgC,MAAM,EAAEK,MAAM,KAAK,CAAC,IAAIH,IAAI,CAACzC,QAAQ,CAAC,CAAC,GAAG,EAAE,EAAE;MAC9CyC,IAAI,CAAChC,QAAQ,CAACgC,IAAI,CAACzC,QAAQ,CAAC,CAAC,GAAG,EAAE,CAAC;IACvC;IACA,OAAOyC,IAAI;EACf;EACAD,aAAaA,CAACF,GAAG,EAAE;IACf,MAAMO,KAAK,GAAG,IAAI,CAACd,KAAK,CAACe,IAAI,CAACR,GAAG,CAAC;IAClC,IAAIM,MAAM,GAAG,IAAI;IACjB,IAAIC,KAAK,EAAE;MACP,IAAIhF,QAAQ,CAAC,IAAI,CAACmE,QAAQ,CAACC,YAAY,CAAC,EAAE;QACtCW,MAAM,GAAGlF,mBAAmB,CAAC,IAAI,CAACoE,QAAQ,EAAEnE,SAAS,CAACoF,MAAM,EAAEnF,gBAAgB,CAACoF,MAAM,CAAC,CAACC,OAAO,CAACJ,KAAK,CAAC,IAAI,CAACb,QAAQ,CAACC,YAAY,GAAG,CAAC,CAAC,CAAC;MACzI;MACA,IAAIpE,QAAQ,CAAC,IAAI,CAACmE,QAAQ,CAACE,UAAU,CAAC,EAAE;QACpCU,MAAM,GAAGlF,mBAAmB,CAAC,IAAI,CAACoE,QAAQ,EAAEnE,SAAS,CAACoF,MAAM,EAAEnF,gBAAgB,CAACsF,IAAI,CAAC,CAACD,OAAO,CAACJ,KAAK,CAAC,IAAI,CAACb,QAAQ,CAACE,UAAU,GAAG,CAAC,CAAC,CAAC;MACrI;MACA,IAAIrE,QAAQ,CAAC,IAAI,CAACmE,QAAQ,CAACG,iBAAiB,CAAC,EAAE;QAC3CS,MAAM,GAAGlF,mBAAmB,CAAC,IAAI,CAACoE,QAAQ,EAAEnE,SAAS,CAACoF,MAAM,EAAEnF,gBAAgB,CAACuF,WAAW,CAAC,CAACF,OAAO,CAACJ,KAAK,CAAC,IAAI,CAACb,QAAQ,CAACG,iBAAiB,GAAG,CAAC,CAAC,CAAC;MACnJ;MACA,OAAO;QACH9B,IAAI,EAAExC,QAAQ,CAAC,IAAI,CAACmE,QAAQ,CAAC3B,IAAI,CAAC,GAAG+C,MAAM,CAACC,QAAQ,CAACR,KAAK,CAAC,IAAI,CAACb,QAAQ,CAAC3B,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,IAAI;QAC9FC,MAAM,EAAEzC,QAAQ,CAAC,IAAI,CAACmE,QAAQ,CAAC1B,MAAM,CAAC,GAAG8C,MAAM,CAACC,QAAQ,CAACR,KAAK,CAAC,IAAI,CAACb,QAAQ,CAAC1B,MAAM,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,IAAI;QACpGC,MAAM,EAAE1C,QAAQ,CAAC,IAAI,CAACmE,QAAQ,CAACzB,MAAM,CAAC,GAAG6C,MAAM,CAACC,QAAQ,CAACR,KAAK,CAAC,IAAI,CAACb,QAAQ,CAACzB,MAAM,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,IAAI;QACpGqC;MACJ,CAAC;IACL,CAAC,MACI;MACD,OAAO,IAAI;IACf;EACJ;EACAR,SAASA,CAAA,EAAG;IACR,IAAIkB,QAAQ,GAAG,IAAI,CAACzB,MAAM,CAAC0B,OAAO,CAAC,4BAA4B,EAAE,MAAM,CAAC;IACxE,MAAMC,SAAS,GAAG,SAAS;IAC3B,MAAMC,WAAW,GAAG,QAAQ;IAC5B,MAAMC,WAAW,GAAG,QAAQ;IAC5B,MAAMzB,YAAY,GAAG,OAAO;IAC5B,MAAMC,UAAU,GAAG,MAAM;IACzB,MAAMC,iBAAiB,GAAG,QAAQ;IAClC,MAAMwB,SAAS,GAAGH,SAAS,CAACV,IAAI,CAAC,IAAI,CAACjB,MAAM,CAAC;IAC7C,MAAM+B,WAAW,GAAGH,WAAW,CAACX,IAAI,CAAC,IAAI,CAACjB,MAAM,CAAC;IACjD,MAAMgC,WAAW,GAAGH,WAAW,CAACZ,IAAI,CAAC,IAAI,CAACjB,MAAM,CAAC;IACjD,MAAMiC,iBAAiB,GAAG7B,YAAY,CAACa,IAAI,CAAC,IAAI,CAACjB,MAAM,CAAC;IACxD,IAAIkC,eAAe,GAAG,IAAI;IAC1B,IAAIC,sBAAsB,GAAG,IAAI;IACjC,IAAI,CAACF,iBAAiB,EAAE;MACpBC,eAAe,GAAG7B,UAAU,CAACY,IAAI,CAAC,IAAI,CAACjB,MAAM,CAAC;IAClD;IACA,IAAI,CAACkC,eAAe,IAAI,CAACD,iBAAiB,EAAE;MACxCE,sBAAsB,GAAG7B,iBAAiB,CAACW,IAAI,CAAC,IAAI,CAACjB,MAAM,CAAC;IAChE;IACA,MAAMoC,MAAM,GAAG,CAACN,SAAS,EAAEC,WAAW,EAAEC,WAAW,EAAEC,iBAAiB,EAAEC,eAAe,EAAEC,sBAAsB,CAAC,CAC3GE,MAAM,CAACC,CAAC,IAAI,CAAC,CAACA,CAAC,CAAC,CAChBC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACE,KAAK,GAAGD,CAAC,CAACC,KAAK,CAAC;IACtCN,MAAM,CAACO,OAAO,CAAC,CAAC3B,KAAK,EAAE0B,KAAK,KAAK;MAC7B,QAAQ1B,KAAK;QACT,KAAKc,SAAS;UACV,IAAI,CAAC3B,QAAQ,CAAC3B,IAAI,GAAGkE,KAAK;UAC1BjB,QAAQ,GAAGA,QAAQ,CAACC,OAAO,CAACC,SAAS,EAAE,YAAY,CAAC;UACpD;QACJ,KAAKI,WAAW;UACZ,IAAI,CAAC5B,QAAQ,CAAC1B,MAAM,GAAGiE,KAAK;UAC5BjB,QAAQ,GAAGA,QAAQ,CAACC,OAAO,CAACE,WAAW,EAAE,YAAY,CAAC;UACtD;QACJ,KAAKI,WAAW;UACZ,IAAI,CAAC7B,QAAQ,CAACzB,MAAM,GAAGgE,KAAK;UAC5BjB,QAAQ,GAAGA,QAAQ,CAACC,OAAO,CAACG,WAAW,EAAE,YAAY,CAAC;UACtD;QACJ,KAAKI,iBAAiB;UAClB,IAAI,CAAC9B,QAAQ,CAACC,YAAY,GAAGsC,KAAK;UAClC,MAAME,aAAa,GAAG/G,mBAAmB,CAAC,IAAI,CAACoE,QAAQ,EAAEnE,SAAS,CAACoF,MAAM,EAAEnF,gBAAgB,CAACoF,MAAM,CAAC,CAAC0B,IAAI,CAAC,GAAG,CAAC;UAC7GpB,QAAQ,GAAGA,QAAQ,CAACC,OAAO,CAACtB,YAAY,EAAG,IAAGwC,aAAc,GAAE,CAAC;UAC/D;QACJ,KAAKV,eAAe;UAChB,IAAI,CAAC/B,QAAQ,CAACE,UAAU,GAAGqC,KAAK;UAChC,MAAMI,WAAW,GAAGjH,mBAAmB,CAAC,IAAI,CAACoE,QAAQ,EAAEnE,SAAS,CAACoF,MAAM,EAAEnF,gBAAgB,CAACsF,IAAI,CAAC,CAACwB,IAAI,CAAC,GAAG,CAAC;UACzGpB,QAAQ,GAAGA,QAAQ,CAACC,OAAO,CAACrB,UAAU,EAAG,IAAGyC,WAAY,GAAE,CAAC;UAC3D;QACJ,KAAKX,sBAAsB;UACvB,IAAI,CAAChC,QAAQ,CAACG,iBAAiB,GAAGoC,KAAK;UACvC,MAAMK,kBAAkB,GAAGlH,mBAAmB,CAAC,IAAI,CAACoE,QAAQ,EAAEnE,SAAS,CAACoF,MAAM,EAAEnF,gBAAgB,CAACuF,WAAW,CAAC,CAACuB,IAAI,CAAC,GAAG,CAAC;UACvHpB,QAAQ,GAAGA,QAAQ,CAACC,OAAO,CAACpB,iBAAiB,EAAG,IAAGyC,kBAAmB,GAAE,CAAC;UACzE;MACR;IACJ,CAAC,CAAC;IACF,IAAI,CAAC7C,KAAK,GAAG,IAAI8C,MAAM,CAACvB,QAAQ,CAAC;EACrC;AACJ;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAAS7E,SAAS,EAAEI,SAAS,EAAEV,mBAAmB,EAAEwD,SAAS,EAAE7D,cAAc,EAAE8D,YAAY,IAAIkD,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}