{"ast": null, "code": "import Inline from '../blots/inline.js';\nclass Underline extends Inline {\n  static blotName = 'underline';\n  static tagName = 'U';\n}\nexport default Underline;", "map": {"version": 3, "names": ["Inline", "Underline", "blotName", "tagName"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/quill/formats/underline.js"], "sourcesContent": ["import Inline from '../blots/inline.js';\nclass Underline extends Inline {\n  static blotName = 'underline';\n  static tagName = 'U';\n}\nexport default Underline;\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,MAAMC,SAAS,SAASD,MAAM,CAAC;EAC7B,OAAOE,QAAQ,GAAG,WAAW;EAC7B,OAAOC,OAAO,GAAG,GAAG;AACtB;AACA,eAAeF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}