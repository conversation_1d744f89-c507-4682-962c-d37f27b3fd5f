{"ast": null, "code": "import { merge } from 'lodash-es';\nimport Emitter from '../core/emitter.js';\nimport BaseTheme, { BaseTooltip } from './base.js';\nimport { Range } from '../core/selection.js';\nimport icons from '../ui/icons.js';\nimport Quill from '../core/quill.js';\nconst TOOLBAR_CONFIG = [['bold', 'italic', 'link'], [{\n  header: 1\n}, {\n  header: 2\n}, 'blockquote']];\nclass BubbleTooltip extends BaseTooltip {\n  static TEMPLATE = ['<span class=\"ql-tooltip-arrow\"></span>', '<div class=\"ql-tooltip-editor\">', '<input type=\"text\" data-formula=\"e=mc^2\" data-link=\"https://quilljs.com\" data-video=\"Embed URL\">', '<a class=\"ql-close\"></a>', '</div>'].join('');\n  constructor(quill, bounds) {\n    super(quill, bounds);\n    this.quill.on(Emitter.events.EDITOR_CHANGE, (type, range, oldRange, source) => {\n      if (type !== Emitter.events.SELECTION_CHANGE) return;\n      if (range != null && range.length > 0 && source === Emitter.sources.USER) {\n        this.show();\n        // Lock our width so we will expand beyond our offsetParent boundaries\n        this.root.style.left = '0px';\n        this.root.style.width = '';\n        this.root.style.width = `${this.root.offsetWidth}px`;\n        const lines = this.quill.getLines(range.index, range.length);\n        if (lines.length === 1) {\n          const bounds = this.quill.getBounds(range);\n          if (bounds != null) {\n            this.position(bounds);\n          }\n        } else {\n          const lastLine = lines[lines.length - 1];\n          const index = this.quill.getIndex(lastLine);\n          const length = Math.min(lastLine.length() - 1, range.index + range.length - index);\n          const indexBounds = this.quill.getBounds(new Range(index, length));\n          if (indexBounds != null) {\n            this.position(indexBounds);\n          }\n        }\n      } else if (document.activeElement !== this.textbox && this.quill.hasFocus()) {\n        this.hide();\n      }\n    });\n  }\n  listen() {\n    super.listen();\n    // @ts-expect-error Fix me later\n    this.root.querySelector('.ql-close').addEventListener('click', () => {\n      this.root.classList.remove('ql-editing');\n    });\n    this.quill.on(Emitter.events.SCROLL_OPTIMIZE, () => {\n      // Let selection be restored by toolbar handlers before repositioning\n      setTimeout(() => {\n        if (this.root.classList.contains('ql-hidden')) return;\n        const range = this.quill.getSelection();\n        if (range != null) {\n          const bounds = this.quill.getBounds(range);\n          if (bounds != null) {\n            this.position(bounds);\n          }\n        }\n      }, 1);\n    });\n  }\n  cancel() {\n    this.show();\n  }\n  position(reference) {\n    const shift = super.position(reference);\n    const arrow = this.root.querySelector('.ql-tooltip-arrow');\n    // @ts-expect-error\n    arrow.style.marginLeft = '';\n    if (shift !== 0) {\n      // @ts-expect-error\n      arrow.style.marginLeft = `${-1 * shift - arrow.offsetWidth / 2}px`;\n    }\n    return shift;\n  }\n}\nclass BubbleTheme extends BaseTheme {\n  constructor(quill, options) {\n    if (options.modules.toolbar != null && options.modules.toolbar.container == null) {\n      options.modules.toolbar.container = TOOLBAR_CONFIG;\n    }\n    super(quill, options);\n    this.quill.container.classList.add('ql-bubble');\n  }\n  extendToolbar(toolbar) {\n    // @ts-expect-error\n    this.tooltip = new BubbleTooltip(this.quill, this.options.bounds);\n    if (toolbar.container != null) {\n      this.tooltip.root.appendChild(toolbar.container);\n      this.buildButtons(toolbar.container.querySelectorAll('button'), icons);\n      this.buildPickers(toolbar.container.querySelectorAll('select'), icons);\n    }\n  }\n}\nBubbleTheme.DEFAULTS = merge({}, BaseTheme.DEFAULTS, {\n  modules: {\n    toolbar: {\n      handlers: {\n        link(value) {\n          if (!value) {\n            this.quill.format('link', false, Quill.sources.USER);\n          } else {\n            // @ts-expect-error\n            this.quill.theme.tooltip.edit();\n          }\n        }\n      }\n    }\n  }\n});\nexport { BubbleTooltip, BubbleTheme as default };", "map": {"version": 3, "names": ["merge", "Emitter", "BaseTheme", "BaseTooltip", "Range", "icons", "<PERSON><PERSON><PERSON>", "TOOLBAR_CONFIG", "header", "BubbleTooltip", "TEMPLATE", "join", "constructor", "quill", "bounds", "on", "events", "EDITOR_CHANGE", "type", "range", "oldRange", "source", "SELECTION_CHANGE", "length", "sources", "USER", "show", "root", "style", "left", "width", "offsetWidth", "lines", "getLines", "index", "getBounds", "position", "lastLine", "getIndex", "Math", "min", "indexBounds", "document", "activeElement", "textbox", "hasFocus", "hide", "listen", "querySelector", "addEventListener", "classList", "remove", "SCROLL_OPTIMIZE", "setTimeout", "contains", "getSelection", "cancel", "reference", "shift", "arrow", "marginLeft", "BubbleTheme", "options", "modules", "toolbar", "container", "add", "extendToolbar", "tooltip", "append<PERSON><PERSON><PERSON>", "buildButtons", "querySelectorAll", "buildPickers", "DEFAULTS", "handlers", "link", "value", "format", "theme", "edit", "default"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/quill/themes/bubble.js"], "sourcesContent": ["import { merge } from 'lodash-es';\nimport Emitter from '../core/emitter.js';\nimport BaseTheme, { BaseTooltip } from './base.js';\nimport { Range } from '../core/selection.js';\nimport icons from '../ui/icons.js';\nimport Quill from '../core/quill.js';\nconst TOOLBAR_CONFIG = [['bold', 'italic', 'link'], [{\n  header: 1\n}, {\n  header: 2\n}, 'blockquote']];\nclass BubbleTooltip extends BaseTooltip {\n  static TEMPLATE = ['<span class=\"ql-tooltip-arrow\"></span>', '<div class=\"ql-tooltip-editor\">', '<input type=\"text\" data-formula=\"e=mc^2\" data-link=\"https://quilljs.com\" data-video=\"Embed URL\">', '<a class=\"ql-close\"></a>', '</div>'].join('');\n  constructor(quill, bounds) {\n    super(quill, bounds);\n    this.quill.on(Emitter.events.EDITOR_CHANGE, (type, range, oldRange, source) => {\n      if (type !== Emitter.events.SELECTION_CHANGE) return;\n      if (range != null && range.length > 0 && source === Emitter.sources.USER) {\n        this.show();\n        // Lock our width so we will expand beyond our offsetParent boundaries\n        this.root.style.left = '0px';\n        this.root.style.width = '';\n        this.root.style.width = `${this.root.offsetWidth}px`;\n        const lines = this.quill.getLines(range.index, range.length);\n        if (lines.length === 1) {\n          const bounds = this.quill.getBounds(range);\n          if (bounds != null) {\n            this.position(bounds);\n          }\n        } else {\n          const lastLine = lines[lines.length - 1];\n          const index = this.quill.getIndex(lastLine);\n          const length = Math.min(lastLine.length() - 1, range.index + range.length - index);\n          const indexBounds = this.quill.getBounds(new Range(index, length));\n          if (indexBounds != null) {\n            this.position(indexBounds);\n          }\n        }\n      } else if (document.activeElement !== this.textbox && this.quill.hasFocus()) {\n        this.hide();\n      }\n    });\n  }\n  listen() {\n    super.listen();\n    // @ts-expect-error Fix me later\n    this.root.querySelector('.ql-close').addEventListener('click', () => {\n      this.root.classList.remove('ql-editing');\n    });\n    this.quill.on(Emitter.events.SCROLL_OPTIMIZE, () => {\n      // Let selection be restored by toolbar handlers before repositioning\n      setTimeout(() => {\n        if (this.root.classList.contains('ql-hidden')) return;\n        const range = this.quill.getSelection();\n        if (range != null) {\n          const bounds = this.quill.getBounds(range);\n          if (bounds != null) {\n            this.position(bounds);\n          }\n        }\n      }, 1);\n    });\n  }\n  cancel() {\n    this.show();\n  }\n  position(reference) {\n    const shift = super.position(reference);\n    const arrow = this.root.querySelector('.ql-tooltip-arrow');\n    // @ts-expect-error\n    arrow.style.marginLeft = '';\n    if (shift !== 0) {\n      // @ts-expect-error\n      arrow.style.marginLeft = `${-1 * shift - arrow.offsetWidth / 2}px`;\n    }\n    return shift;\n  }\n}\nclass BubbleTheme extends BaseTheme {\n  constructor(quill, options) {\n    if (options.modules.toolbar != null && options.modules.toolbar.container == null) {\n      options.modules.toolbar.container = TOOLBAR_CONFIG;\n    }\n    super(quill, options);\n    this.quill.container.classList.add('ql-bubble');\n  }\n  extendToolbar(toolbar) {\n    // @ts-expect-error\n    this.tooltip = new BubbleTooltip(this.quill, this.options.bounds);\n    if (toolbar.container != null) {\n      this.tooltip.root.appendChild(toolbar.container);\n      this.buildButtons(toolbar.container.querySelectorAll('button'), icons);\n      this.buildPickers(toolbar.container.querySelectorAll('select'), icons);\n    }\n  }\n}\nBubbleTheme.DEFAULTS = merge({}, BaseTheme.DEFAULTS, {\n  modules: {\n    toolbar: {\n      handlers: {\n        link(value) {\n          if (!value) {\n            this.quill.format('link', false, Quill.sources.USER);\n          } else {\n            // @ts-expect-error\n            this.quill.theme.tooltip.edit();\n          }\n        }\n      }\n    }\n  }\n});\nexport { BubbleTooltip, BubbleTheme as default };\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,WAAW;AACjC,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,SAAS,IAAIC,WAAW,QAAQ,WAAW;AAClD,SAASC,KAAK,QAAQ,sBAAsB;AAC5C,OAAOC,KAAK,MAAM,gBAAgB;AAClC,OAAOC,KAAK,MAAM,kBAAkB;AACpC,MAAMC,cAAc,GAAG,CAAC,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,CAAC,EAAE,CAAC;EACnDC,MAAM,EAAE;AACV,CAAC,EAAE;EACDA,MAAM,EAAE;AACV,CAAC,EAAE,YAAY,CAAC,CAAC;AACjB,MAAMC,aAAa,SAASN,WAAW,CAAC;EACtC,OAAOO,QAAQ,GAAG,CAAC,wCAAwC,EAAE,iCAAiC,EAAE,kGAAkG,EAAE,0BAA0B,EAAE,QAAQ,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;EAClPC,WAAWA,CAACC,KAAK,EAAEC,MAAM,EAAE;IACzB,KAAK,CAACD,KAAK,EAAEC,MAAM,CAAC;IACpB,IAAI,CAACD,KAAK,CAACE,EAAE,CAACd,OAAO,CAACe,MAAM,CAACC,aAAa,EAAE,CAACC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,KAAK;MAC7E,IAAIH,IAAI,KAAKjB,OAAO,CAACe,MAAM,CAACM,gBAAgB,EAAE;MAC9C,IAAIH,KAAK,IAAI,IAAI,IAAIA,KAAK,CAACI,MAAM,GAAG,CAAC,IAAIF,MAAM,KAAKpB,OAAO,CAACuB,OAAO,CAACC,IAAI,EAAE;QACxE,IAAI,CAACC,IAAI,CAAC,CAAC;QACX;QACA,IAAI,CAACC,IAAI,CAACC,KAAK,CAACC,IAAI,GAAG,KAAK;QAC5B,IAAI,CAACF,IAAI,CAACC,KAAK,CAACE,KAAK,GAAG,EAAE;QAC1B,IAAI,CAACH,IAAI,CAACC,KAAK,CAACE,KAAK,GAAI,GAAE,IAAI,CAACH,IAAI,CAACI,WAAY,IAAG;QACpD,MAAMC,KAAK,GAAG,IAAI,CAACnB,KAAK,CAACoB,QAAQ,CAACd,KAAK,CAACe,KAAK,EAAEf,KAAK,CAACI,MAAM,CAAC;QAC5D,IAAIS,KAAK,CAACT,MAAM,KAAK,CAAC,EAAE;UACtB,MAAMT,MAAM,GAAG,IAAI,CAACD,KAAK,CAACsB,SAAS,CAAChB,KAAK,CAAC;UAC1C,IAAIL,MAAM,IAAI,IAAI,EAAE;YAClB,IAAI,CAACsB,QAAQ,CAACtB,MAAM,CAAC;UACvB;QACF,CAAC,MAAM;UACL,MAAMuB,QAAQ,GAAGL,KAAK,CAACA,KAAK,CAACT,MAAM,GAAG,CAAC,CAAC;UACxC,MAAMW,KAAK,GAAG,IAAI,CAACrB,KAAK,CAACyB,QAAQ,CAACD,QAAQ,CAAC;UAC3C,MAAMd,MAAM,GAAGgB,IAAI,CAACC,GAAG,CAACH,QAAQ,CAACd,MAAM,CAAC,CAAC,GAAG,CAAC,EAAEJ,KAAK,CAACe,KAAK,GAAGf,KAAK,CAACI,MAAM,GAAGW,KAAK,CAAC;UAClF,MAAMO,WAAW,GAAG,IAAI,CAAC5B,KAAK,CAACsB,SAAS,CAAC,IAAI/B,KAAK,CAAC8B,KAAK,EAAEX,MAAM,CAAC,CAAC;UAClE,IAAIkB,WAAW,IAAI,IAAI,EAAE;YACvB,IAAI,CAACL,QAAQ,CAACK,WAAW,CAAC;UAC5B;QACF;MACF,CAAC,MAAM,IAAIC,QAAQ,CAACC,aAAa,KAAK,IAAI,CAACC,OAAO,IAAI,IAAI,CAAC/B,KAAK,CAACgC,QAAQ,CAAC,CAAC,EAAE;QAC3E,IAAI,CAACC,IAAI,CAAC,CAAC;MACb;IACF,CAAC,CAAC;EACJ;EACAC,MAAMA,CAAA,EAAG;IACP,KAAK,CAACA,MAAM,CAAC,CAAC;IACd;IACA,IAAI,CAACpB,IAAI,CAACqB,aAAa,CAAC,WAAW,CAAC,CAACC,gBAAgB,CAAC,OAAO,EAAE,MAAM;MACnE,IAAI,CAACtB,IAAI,CAACuB,SAAS,CAACC,MAAM,CAAC,YAAY,CAAC;IAC1C,CAAC,CAAC;IACF,IAAI,CAACtC,KAAK,CAACE,EAAE,CAACd,OAAO,CAACe,MAAM,CAACoC,eAAe,EAAE,MAAM;MAClD;MACAC,UAAU,CAAC,MAAM;QACf,IAAI,IAAI,CAAC1B,IAAI,CAACuB,SAAS,CAACI,QAAQ,CAAC,WAAW,CAAC,EAAE;QAC/C,MAAMnC,KAAK,GAAG,IAAI,CAACN,KAAK,CAAC0C,YAAY,CAAC,CAAC;QACvC,IAAIpC,KAAK,IAAI,IAAI,EAAE;UACjB,MAAML,MAAM,GAAG,IAAI,CAACD,KAAK,CAACsB,SAAS,CAAChB,KAAK,CAAC;UAC1C,IAAIL,MAAM,IAAI,IAAI,EAAE;YAClB,IAAI,CAACsB,QAAQ,CAACtB,MAAM,CAAC;UACvB;QACF;MACF,CAAC,EAAE,CAAC,CAAC;IACP,CAAC,CAAC;EACJ;EACA0C,MAAMA,CAAA,EAAG;IACP,IAAI,CAAC9B,IAAI,CAAC,CAAC;EACb;EACAU,QAAQA,CAACqB,SAAS,EAAE;IAClB,MAAMC,KAAK,GAAG,KAAK,CAACtB,QAAQ,CAACqB,SAAS,CAAC;IACvC,MAAME,KAAK,GAAG,IAAI,CAAChC,IAAI,CAACqB,aAAa,CAAC,mBAAmB,CAAC;IAC1D;IACAW,KAAK,CAAC/B,KAAK,CAACgC,UAAU,GAAG,EAAE;IAC3B,IAAIF,KAAK,KAAK,CAAC,EAAE;MACf;MACAC,KAAK,CAAC/B,KAAK,CAACgC,UAAU,GAAI,GAAE,CAAC,CAAC,GAAGF,KAAK,GAAGC,KAAK,CAAC5B,WAAW,GAAG,CAAE,IAAG;IACpE;IACA,OAAO2B,KAAK;EACd;AACF;AACA,MAAMG,WAAW,SAAS3D,SAAS,CAAC;EAClCU,WAAWA,CAACC,KAAK,EAAEiD,OAAO,EAAE;IAC1B,IAAIA,OAAO,CAACC,OAAO,CAACC,OAAO,IAAI,IAAI,IAAIF,OAAO,CAACC,OAAO,CAACC,OAAO,CAACC,SAAS,IAAI,IAAI,EAAE;MAChFH,OAAO,CAACC,OAAO,CAACC,OAAO,CAACC,SAAS,GAAG1D,cAAc;IACpD;IACA,KAAK,CAACM,KAAK,EAAEiD,OAAO,CAAC;IACrB,IAAI,CAACjD,KAAK,CAACoD,SAAS,CAACf,SAAS,CAACgB,GAAG,CAAC,WAAW,CAAC;EACjD;EACAC,aAAaA,CAACH,OAAO,EAAE;IACrB;IACA,IAAI,CAACI,OAAO,GAAG,IAAI3D,aAAa,CAAC,IAAI,CAACI,KAAK,EAAE,IAAI,CAACiD,OAAO,CAAChD,MAAM,CAAC;IACjE,IAAIkD,OAAO,CAACC,SAAS,IAAI,IAAI,EAAE;MAC7B,IAAI,CAACG,OAAO,CAACzC,IAAI,CAAC0C,WAAW,CAACL,OAAO,CAACC,SAAS,CAAC;MAChD,IAAI,CAACK,YAAY,CAACN,OAAO,CAACC,SAAS,CAACM,gBAAgB,CAAC,QAAQ,CAAC,EAAElE,KAAK,CAAC;MACtE,IAAI,CAACmE,YAAY,CAACR,OAAO,CAACC,SAAS,CAACM,gBAAgB,CAAC,QAAQ,CAAC,EAAElE,KAAK,CAAC;IACxE;EACF;AACF;AACAwD,WAAW,CAACY,QAAQ,GAAGzE,KAAK,CAAC,CAAC,CAAC,EAAEE,SAAS,CAACuE,QAAQ,EAAE;EACnDV,OAAO,EAAE;IACPC,OAAO,EAAE;MACPU,QAAQ,EAAE;QACRC,IAAIA,CAACC,KAAK,EAAE;UACV,IAAI,CAACA,KAAK,EAAE;YACV,IAAI,CAAC/D,KAAK,CAACgE,MAAM,CAAC,MAAM,EAAE,KAAK,EAAEvE,KAAK,CAACkB,OAAO,CAACC,IAAI,CAAC;UACtD,CAAC,MAAM;YACL;YACA,IAAI,CAACZ,KAAK,CAACiE,KAAK,CAACV,OAAO,CAACW,IAAI,CAAC,CAAC;UACjC;QACF;MACF;IACF;EACF;AACF,CAAC,CAAC;AACF,SAAStE,aAAa,EAAEoD,WAAW,IAAImB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}