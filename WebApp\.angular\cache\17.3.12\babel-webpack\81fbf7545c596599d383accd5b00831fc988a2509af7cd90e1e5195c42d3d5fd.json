{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, inject, ApplicationRef, EnvironmentInjector, createComponent, Injectable, Inject, InjectionToken, booleanAttribute, Directive, Optional, SkipSelf, Input, EventEmitter, Self, Output, NgModule } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\nimport * as i1 from '@angular/cdk/scrolling';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport { _getEventTarget, normalizePassiveListenerOptions, _getShadowRoot } from '@angular/cdk/platform';\nimport { coerceElement, coerceNumberProperty, coerceArray } from '@angular/cdk/coercion';\nimport { isFakeTouchstartFromScreenReader, isFakeMousedownFromScreenReader } from '@angular/cdk/a11y';\nimport { Subject, Subscription, interval, animationFrameScheduler, Observable, merge, BehaviorSubject } from 'rxjs';\nimport { takeUntil, map, take, tap, switchMap, startWith } from 'rxjs/operators';\nimport * as i1$1 from '@angular/cdk/bidi';\n\n/**\n * Shallow-extends a stylesheet object with another stylesheet-like object.\n * Note that the keys in `source` have to be dash-cased.\n * @docs-private\n */\nfunction extendStyles(dest, source, importantProperties) {\n  for (let key in source) {\n    if (source.hasOwnProperty(key)) {\n      const value = source[key];\n      if (value) {\n        dest.setProperty(key, value, importantProperties?.has(key) ? 'important' : '');\n      } else {\n        dest.removeProperty(key);\n      }\n    }\n  }\n  return dest;\n}\n/**\n * Toggles whether the native drag interactions should be enabled for an element.\n * @param element Element on which to toggle the drag interactions.\n * @param enable Whether the drag interactions should be enabled.\n * @docs-private\n */\nfunction toggleNativeDragInteractions(element, enable) {\n  const userSelect = enable ? '' : 'none';\n  extendStyles(element.style, {\n    'touch-action': enable ? '' : 'none',\n    '-webkit-user-drag': enable ? '' : 'none',\n    '-webkit-tap-highlight-color': enable ? '' : 'transparent',\n    'user-select': userSelect,\n    '-ms-user-select': userSelect,\n    '-webkit-user-select': userSelect,\n    '-moz-user-select': userSelect\n  });\n}\n/**\n * Toggles whether an element is visible while preserving its dimensions.\n * @param element Element whose visibility to toggle\n * @param enable Whether the element should be visible.\n * @param importantProperties Properties to be set as `!important`.\n * @docs-private\n */\nfunction toggleVisibility(element, enable, importantProperties) {\n  extendStyles(element.style, {\n    position: enable ? '' : 'fixed',\n    top: enable ? '' : '0',\n    opacity: enable ? '' : '0',\n    left: enable ? '' : '-999em'\n  }, importantProperties);\n}\n/**\n * Combines a transform string with an optional other transform\n * that exited before the base transform was applied.\n */\nfunction combineTransforms(transform, initialTransform) {\n  return initialTransform && initialTransform != 'none' ? transform + ' ' + initialTransform : transform;\n}\n/**\n * Matches the target element's size to the source's size.\n * @param target Element that needs to be resized.\n * @param sourceRect Dimensions of the source element.\n */\nfunction matchElementSize(target, sourceRect) {\n  target.style.width = `${sourceRect.width}px`;\n  target.style.height = `${sourceRect.height}px`;\n  target.style.transform = getTransform(sourceRect.left, sourceRect.top);\n}\n/**\n * Gets a 3d `transform` that can be applied to an element.\n * @param x Desired position of the element along the X axis.\n * @param y Desired position of the element along the Y axis.\n */\nfunction getTransform(x, y) {\n  // Round the transforms since some browsers will\n  // blur the elements for sub-pixel transforms.\n  return `translate3d(${Math.round(x)}px, ${Math.round(y)}px, 0)`;\n}\n\n/** Gets a mutable version of an element's bounding `DOMRect`. */\nfunction getMutableClientRect(element) {\n  const rect = element.getBoundingClientRect();\n  // We need to clone the `clientRect` here, because all the values on it are readonly\n  // and we need to be able to update them. Also we can't use a spread here, because\n  // the values on a `DOMRect` aren't own properties. See:\n  // https://developer.mozilla.org/en-US/docs/Web/API/Element/getBoundingClientRect#Notes\n  return {\n    top: rect.top,\n    right: rect.right,\n    bottom: rect.bottom,\n    left: rect.left,\n    width: rect.width,\n    height: rect.height,\n    x: rect.x,\n    y: rect.y\n  };\n}\n/**\n * Checks whether some coordinates are within a `DOMRect`.\n * @param clientRect DOMRect that is being checked.\n * @param x Coordinates along the X axis.\n * @param y Coordinates along the Y axis.\n */\nfunction isInsideClientRect(clientRect, x, y) {\n  const {\n    top,\n    bottom,\n    left,\n    right\n  } = clientRect;\n  return y >= top && y <= bottom && x >= left && x <= right;\n}\n/**\n * Updates the top/left positions of a `DOMRect`, as well as their bottom/right counterparts.\n * @param domRect `DOMRect` that should be updated.\n * @param top Amount to add to the `top` position.\n * @param left Amount to add to the `left` position.\n */\nfunction adjustDomRect(domRect, top, left) {\n  domRect.top += top;\n  domRect.bottom = domRect.top + domRect.height;\n  domRect.left += left;\n  domRect.right = domRect.left + domRect.width;\n}\n/**\n * Checks whether the pointer coordinates are close to a DOMRect.\n * @param rect DOMRect to check against.\n * @param threshold Threshold around the DOMRect.\n * @param pointerX Coordinates along the X axis.\n * @param pointerY Coordinates along the Y axis.\n */\nfunction isPointerNearDomRect(rect, threshold, pointerX, pointerY) {\n  const {\n    top,\n    right,\n    bottom,\n    left,\n    width,\n    height\n  } = rect;\n  const xThreshold = width * threshold;\n  const yThreshold = height * threshold;\n  return pointerY > top - yThreshold && pointerY < bottom + yThreshold && pointerX > left - xThreshold && pointerX < right + xThreshold;\n}\n\n/** Keeps track of the scroll position and dimensions of the parents of an element. */\nclass ParentPositionTracker {\n  constructor(_document) {\n    this._document = _document;\n    /** Cached positions of the scrollable parent elements. */\n    this.positions = new Map();\n  }\n  /** Clears the cached positions. */\n  clear() {\n    this.positions.clear();\n  }\n  /** Caches the positions. Should be called at the beginning of a drag sequence. */\n  cache(elements) {\n    this.clear();\n    this.positions.set(this._document, {\n      scrollPosition: this.getViewportScrollPosition()\n    });\n    elements.forEach(element => {\n      this.positions.set(element, {\n        scrollPosition: {\n          top: element.scrollTop,\n          left: element.scrollLeft\n        },\n        clientRect: getMutableClientRect(element)\n      });\n    });\n  }\n  /** Handles scrolling while a drag is taking place. */\n  handleScroll(event) {\n    const target = _getEventTarget(event);\n    const cachedPosition = this.positions.get(target);\n    if (!cachedPosition) {\n      return null;\n    }\n    const scrollPosition = cachedPosition.scrollPosition;\n    let newTop;\n    let newLeft;\n    if (target === this._document) {\n      const viewportScrollPosition = this.getViewportScrollPosition();\n      newTop = viewportScrollPosition.top;\n      newLeft = viewportScrollPosition.left;\n    } else {\n      newTop = target.scrollTop;\n      newLeft = target.scrollLeft;\n    }\n    const topDifference = scrollPosition.top - newTop;\n    const leftDifference = scrollPosition.left - newLeft;\n    // Go through and update the cached positions of the scroll\n    // parents that are inside the element that was scrolled.\n    this.positions.forEach((position, node) => {\n      if (position.clientRect && target !== node && target.contains(node)) {\n        adjustDomRect(position.clientRect, topDifference, leftDifference);\n      }\n    });\n    scrollPosition.top = newTop;\n    scrollPosition.left = newLeft;\n    return {\n      top: topDifference,\n      left: leftDifference\n    };\n  }\n  /**\n   * Gets the scroll position of the viewport. Note that we use the scrollX and scrollY directly,\n   * instead of going through the `ViewportRuler`, because the first value the ruler looks at is\n   * the top/left offset of the `document.documentElement` which works for most cases, but breaks\n   * if the element is offset by something like the `BlockScrollStrategy`.\n   */\n  getViewportScrollPosition() {\n    return {\n      top: window.scrollY,\n      left: window.scrollX\n    };\n  }\n}\n\n/** Creates a deep clone of an element. */\nfunction deepCloneNode(node) {\n  const clone = node.cloneNode(true);\n  const descendantsWithId = clone.querySelectorAll('[id]');\n  const nodeName = node.nodeName.toLowerCase();\n  // Remove the `id` to avoid having multiple elements with the same id on the page.\n  clone.removeAttribute('id');\n  for (let i = 0; i < descendantsWithId.length; i++) {\n    descendantsWithId[i].removeAttribute('id');\n  }\n  if (nodeName === 'canvas') {\n    transferCanvasData(node, clone);\n  } else if (nodeName === 'input' || nodeName === 'select' || nodeName === 'textarea') {\n    transferInputData(node, clone);\n  }\n  transferData('canvas', node, clone, transferCanvasData);\n  transferData('input, textarea, select', node, clone, transferInputData);\n  return clone;\n}\n/** Matches elements between an element and its clone and allows for their data to be cloned. */\nfunction transferData(selector, node, clone, callback) {\n  const descendantElements = node.querySelectorAll(selector);\n  if (descendantElements.length) {\n    const cloneElements = clone.querySelectorAll(selector);\n    for (let i = 0; i < descendantElements.length; i++) {\n      callback(descendantElements[i], cloneElements[i]);\n    }\n  }\n}\n// Counter for unique cloned radio button names.\nlet cloneUniqueId = 0;\n/** Transfers the data of one input element to another. */\nfunction transferInputData(source, clone) {\n  // Browsers throw an error when assigning the value of a file input programmatically.\n  if (clone.type !== 'file') {\n    clone.value = source.value;\n  }\n  // Radio button `name` attributes must be unique for radio button groups\n  // otherwise original radio buttons can lose their checked state\n  // once the clone is inserted in the DOM.\n  if (clone.type === 'radio' && clone.name) {\n    clone.name = `mat-clone-${clone.name}-${cloneUniqueId++}`;\n  }\n}\n/** Transfers the data of one canvas element to another. */\nfunction transferCanvasData(source, clone) {\n  const context = clone.getContext('2d');\n  if (context) {\n    // In some cases `drawImage` can throw (e.g. if the canvas size is 0x0).\n    // We can't do much about it so just ignore the error.\n    try {\n      context.drawImage(source, 0, 0);\n    } catch {}\n  }\n}\n\n/**\n * Gets the root HTML element of an embedded view.\n * If the root is not an HTML element it gets wrapped in one.\n */\nfunction getRootNode(viewRef, _document) {\n  const rootNodes = viewRef.rootNodes;\n  if (rootNodes.length === 1 && rootNodes[0].nodeType === _document.ELEMENT_NODE) {\n    return rootNodes[0];\n  }\n  const wrapper = _document.createElement('div');\n  rootNodes.forEach(node => wrapper.appendChild(node));\n  return wrapper;\n}\n\n/** Parses a CSS time value to milliseconds. */\nfunction parseCssTimeUnitsToMs(value) {\n  // Some browsers will return it in seconds, whereas others will return milliseconds.\n  const multiplier = value.toLowerCase().indexOf('ms') > -1 ? 1 : 1000;\n  return parseFloat(value) * multiplier;\n}\n/** Gets the transform transition duration, including the delay, of an element in milliseconds. */\nfunction getTransformTransitionDurationInMs(element) {\n  const computedStyle = getComputedStyle(element);\n  const transitionedProperties = parseCssPropertyValue(computedStyle, 'transition-property');\n  const property = transitionedProperties.find(prop => prop === 'transform' || prop === 'all');\n  // If there's no transition for `all` or `transform`, we shouldn't do anything.\n  if (!property) {\n    return 0;\n  }\n  // Get the index of the property that we're interested in and match\n  // it up to the same index in `transition-delay` and `transition-duration`.\n  const propertyIndex = transitionedProperties.indexOf(property);\n  const rawDurations = parseCssPropertyValue(computedStyle, 'transition-duration');\n  const rawDelays = parseCssPropertyValue(computedStyle, 'transition-delay');\n  return parseCssTimeUnitsToMs(rawDurations[propertyIndex]) + parseCssTimeUnitsToMs(rawDelays[propertyIndex]);\n}\n/** Parses out multiple values from a computed style into an array. */\nfunction parseCssPropertyValue(computedStyle, name) {\n  const value = computedStyle.getPropertyValue(name);\n  return value.split(',').map(part => part.trim());\n}\n\n/** Inline styles to be set as `!important` while dragging. */\nconst importantProperties = new Set([\n// Needs to be important, because some `mat-table` sets `position: sticky !important`. See #22781.\n'position']);\nclass PreviewRef {\n  constructor(_document, _rootElement, _direction, _initialDomRect, _previewTemplate, _previewClass, _pickupPositionOnPage, _initialTransform, _zIndex) {\n    this._document = _document;\n    this._rootElement = _rootElement;\n    this._direction = _direction;\n    this._initialDomRect = _initialDomRect;\n    this._previewTemplate = _previewTemplate;\n    this._previewClass = _previewClass;\n    this._pickupPositionOnPage = _pickupPositionOnPage;\n    this._initialTransform = _initialTransform;\n    this._zIndex = _zIndex;\n  }\n  attach(parent) {\n    this._preview = this._createPreview();\n    parent.appendChild(this._preview);\n    // The null check is necessary for browsers that don't support the popover API.\n    // Note that we use a string access for compatibility with Closure.\n    if ('showPopover' in this._preview) {\n      this._preview['showPopover']();\n    }\n  }\n  destroy() {\n    this._preview.remove();\n    this._previewEmbeddedView?.destroy();\n    this._preview = this._previewEmbeddedView = null;\n  }\n  setTransform(value) {\n    this._preview.style.transform = value;\n  }\n  getBoundingClientRect() {\n    return this._preview.getBoundingClientRect();\n  }\n  addClass(className) {\n    this._preview.classList.add(className);\n  }\n  getTransitionDuration() {\n    return getTransformTransitionDurationInMs(this._preview);\n  }\n  addEventListener(name, handler) {\n    this._preview.addEventListener(name, handler);\n  }\n  removeEventListener(name, handler) {\n    this._preview.removeEventListener(name, handler);\n  }\n  _createPreview() {\n    const previewConfig = this._previewTemplate;\n    const previewClass = this._previewClass;\n    const previewTemplate = previewConfig ? previewConfig.template : null;\n    let preview;\n    if (previewTemplate && previewConfig) {\n      // Measure the element before we've inserted the preview\n      // since the insertion could throw off the measurement.\n      const rootRect = previewConfig.matchSize ? this._initialDomRect : null;\n      const viewRef = previewConfig.viewContainer.createEmbeddedView(previewTemplate, previewConfig.context);\n      viewRef.detectChanges();\n      preview = getRootNode(viewRef, this._document);\n      this._previewEmbeddedView = viewRef;\n      if (previewConfig.matchSize) {\n        matchElementSize(preview, rootRect);\n      } else {\n        preview.style.transform = getTransform(this._pickupPositionOnPage.x, this._pickupPositionOnPage.y);\n      }\n    } else {\n      preview = deepCloneNode(this._rootElement);\n      matchElementSize(preview, this._initialDomRect);\n      if (this._initialTransform) {\n        preview.style.transform = this._initialTransform;\n      }\n    }\n    extendStyles(preview.style, {\n      // It's important that we disable the pointer events on the preview, because\n      // it can throw off the `document.elementFromPoint` calls in the `CdkDropList`.\n      'pointer-events': 'none',\n      // We have to reset the margin, because it can throw off positioning relative to the viewport.\n      'margin': '0',\n      'position': 'fixed',\n      'top': '0',\n      'left': '0',\n      'z-index': this._zIndex + ''\n    }, importantProperties);\n    toggleNativeDragInteractions(preview, false);\n    preview.classList.add('cdk-drag-preview');\n    preview.setAttribute('popover', 'manual');\n    preview.setAttribute('dir', this._direction);\n    if (previewClass) {\n      if (Array.isArray(previewClass)) {\n        previewClass.forEach(className => preview.classList.add(className));\n      } else {\n        preview.classList.add(previewClass);\n      }\n    }\n    return preview;\n  }\n}\n\n/** Options that can be used to bind a passive event listener. */\nconst passiveEventListenerOptions = normalizePassiveListenerOptions({\n  passive: true\n});\n/** Options that can be used to bind an active event listener. */\nconst activeEventListenerOptions = normalizePassiveListenerOptions({\n  passive: false\n});\n/** Event options that can be used to bind an active, capturing event. */\nconst activeCapturingEventOptions$1 = normalizePassiveListenerOptions({\n  passive: false,\n  capture: true\n});\n/**\n * Time in milliseconds for which to ignore mouse events, after\n * receiving a touch event. Used to avoid doing double work for\n * touch devices where the browser fires fake mouse events, in\n * addition to touch events.\n */\nconst MOUSE_EVENT_IGNORE_TIME = 800;\n/** Inline styles to be set as `!important` while dragging. */\nconst dragImportantProperties = new Set([\n// Needs to be important, because some `mat-table` sets `position: sticky !important`. See #22781.\n'position']);\n/**\n * Reference to a draggable item. Used to manipulate or dispose of the item.\n */\nclass DragRef {\n  /** Whether starting to drag this element is disabled. */\n  get disabled() {\n    return this._disabled || !!(this._dropContainer && this._dropContainer.disabled);\n  }\n  set disabled(value) {\n    if (value !== this._disabled) {\n      this._disabled = value;\n      this._toggleNativeDragInteractions();\n      this._handles.forEach(handle => toggleNativeDragInteractions(handle, value));\n    }\n  }\n  constructor(element, _config, _document, _ngZone, _viewportRuler, _dragDropRegistry) {\n    this._config = _config;\n    this._document = _document;\n    this._ngZone = _ngZone;\n    this._viewportRuler = _viewportRuler;\n    this._dragDropRegistry = _dragDropRegistry;\n    /**\n     * CSS `transform` applied to the element when it isn't being dragged. We need a\n     * passive transform in order for the dragged element to retain its new position\n     * after the user has stopped dragging and because we need to know the relative\n     * position in case they start dragging again. This corresponds to `element.style.transform`.\n     */\n    this._passiveTransform = {\n      x: 0,\n      y: 0\n    };\n    /** CSS `transform` that is applied to the element while it's being dragged. */\n    this._activeTransform = {\n      x: 0,\n      y: 0\n    };\n    /**\n     * Whether the dragging sequence has been started. Doesn't\n     * necessarily mean that the element has been moved.\n     */\n    this._hasStartedDragging = false;\n    /** Emits when the item is being moved. */\n    this._moveEvents = new Subject();\n    /** Subscription to pointer movement events. */\n    this._pointerMoveSubscription = Subscription.EMPTY;\n    /** Subscription to the event that is dispatched when the user lifts their pointer. */\n    this._pointerUpSubscription = Subscription.EMPTY;\n    /** Subscription to the viewport being scrolled. */\n    this._scrollSubscription = Subscription.EMPTY;\n    /** Subscription to the viewport being resized. */\n    this._resizeSubscription = Subscription.EMPTY;\n    /** Cached reference to the boundary element. */\n    this._boundaryElement = null;\n    /** Whether the native dragging interactions have been enabled on the root element. */\n    this._nativeInteractionsEnabled = true;\n    /** Elements that can be used to drag the draggable item. */\n    this._handles = [];\n    /** Registered handles that are currently disabled. */\n    this._disabledHandles = new Set();\n    /** Layout direction of the item. */\n    this._direction = 'ltr';\n    /**\n     * Amount of milliseconds to wait after the user has put their\n     * pointer down before starting to drag the element.\n     */\n    this.dragStartDelay = 0;\n    this._disabled = false;\n    /** Emits as the drag sequence is being prepared. */\n    this.beforeStarted = new Subject();\n    /** Emits when the user starts dragging the item. */\n    this.started = new Subject();\n    /** Emits when the user has released a drag item, before any animations have started. */\n    this.released = new Subject();\n    /** Emits when the user stops dragging an item in the container. */\n    this.ended = new Subject();\n    /** Emits when the user has moved the item into a new container. */\n    this.entered = new Subject();\n    /** Emits when the user removes the item its container by dragging it into another container. */\n    this.exited = new Subject();\n    /** Emits when the user drops the item inside a container. */\n    this.dropped = new Subject();\n    /**\n     * Emits as the user is dragging the item. Use with caution,\n     * because this event will fire for every pixel that the user has dragged.\n     */\n    this.moved = this._moveEvents;\n    /** Handler for the `mousedown`/`touchstart` events. */\n    this._pointerDown = event => {\n      this.beforeStarted.next();\n      // Delegate the event based on whether it started from a handle or the element itself.\n      if (this._handles.length) {\n        const targetHandle = this._getTargetHandle(event);\n        if (targetHandle && !this._disabledHandles.has(targetHandle) && !this.disabled) {\n          this._initializeDragSequence(targetHandle, event);\n        }\n      } else if (!this.disabled) {\n        this._initializeDragSequence(this._rootElement, event);\n      }\n    };\n    /** Handler that is invoked when the user moves their pointer after they've initiated a drag. */\n    this._pointerMove = event => {\n      const pointerPosition = this._getPointerPositionOnPage(event);\n      if (!this._hasStartedDragging) {\n        const distanceX = Math.abs(pointerPosition.x - this._pickupPositionOnPage.x);\n        const distanceY = Math.abs(pointerPosition.y - this._pickupPositionOnPage.y);\n        const isOverThreshold = distanceX + distanceY >= this._config.dragStartThreshold;\n        // Only start dragging after the user has moved more than the minimum distance in either\n        // direction. Note that this is preferable over doing something like `skip(minimumDistance)`\n        // in the `pointerMove` subscription, because we're not guaranteed to have one move event\n        // per pixel of movement (e.g. if the user moves their pointer quickly).\n        if (isOverThreshold) {\n          const isDelayElapsed = Date.now() >= this._dragStartTime + this._getDragStartDelay(event);\n          const container = this._dropContainer;\n          if (!isDelayElapsed) {\n            this._endDragSequence(event);\n            return;\n          }\n          // Prevent other drag sequences from starting while something in the container is still\n          // being dragged. This can happen while we're waiting for the drop animation to finish\n          // and can cause errors, because some elements might still be moving around.\n          if (!container || !container.isDragging() && !container.isReceiving()) {\n            // Prevent the default action as soon as the dragging sequence is considered as\n            // \"started\" since waiting for the next event can allow the device to begin scrolling.\n            if (event.cancelable) {\n              event.preventDefault();\n            }\n            this._hasStartedDragging = true;\n            this._ngZone.run(() => this._startDragSequence(event));\n          }\n        }\n        return;\n      }\n      // We prevent the default action down here so that we know that dragging has started. This is\n      // important for touch devices where doing this too early can unnecessarily block scrolling,\n      // if there's a dragging delay.\n      if (event.cancelable) {\n        event.preventDefault();\n      }\n      const constrainedPointerPosition = this._getConstrainedPointerPosition(pointerPosition);\n      this._hasMoved = true;\n      this._lastKnownPointerPosition = pointerPosition;\n      this._updatePointerDirectionDelta(constrainedPointerPosition);\n      if (this._dropContainer) {\n        this._updateActiveDropContainer(constrainedPointerPosition, pointerPosition);\n      } else {\n        // If there's a position constraint function, we want the element's top/left to be at the\n        // specific position on the page. Use the initial position as a reference if that's the case.\n        const offset = this.constrainPosition ? this._initialDomRect : this._pickupPositionOnPage;\n        const activeTransform = this._activeTransform;\n        activeTransform.x = constrainedPointerPosition.x - offset.x + this._passiveTransform.x;\n        activeTransform.y = constrainedPointerPosition.y - offset.y + this._passiveTransform.y;\n        this._applyRootElementTransform(activeTransform.x, activeTransform.y);\n      }\n      // Since this event gets fired for every pixel while dragging, we only\n      // want to fire it if the consumer opted into it. Also we have to\n      // re-enter the zone because we run all of the events on the outside.\n      if (this._moveEvents.observers.length) {\n        this._ngZone.run(() => {\n          this._moveEvents.next({\n            source: this,\n            pointerPosition: constrainedPointerPosition,\n            event,\n            distance: this._getDragDistance(constrainedPointerPosition),\n            delta: this._pointerDirectionDelta\n          });\n        });\n      }\n    };\n    /** Handler that is invoked when the user lifts their pointer up, after initiating a drag. */\n    this._pointerUp = event => {\n      this._endDragSequence(event);\n    };\n    /** Handles a native `dragstart` event. */\n    this._nativeDragStart = event => {\n      if (this._handles.length) {\n        const targetHandle = this._getTargetHandle(event);\n        if (targetHandle && !this._disabledHandles.has(targetHandle) && !this.disabled) {\n          event.preventDefault();\n        }\n      } else if (!this.disabled) {\n        // Usually this isn't necessary since the we prevent the default action in `pointerDown`,\n        // but some cases like dragging of links can slip through (see #24403).\n        event.preventDefault();\n      }\n    };\n    this.withRootElement(element).withParent(_config.parentDragRef || null);\n    this._parentPositions = new ParentPositionTracker(_document);\n    _dragDropRegistry.registerDragItem(this);\n  }\n  /**\n   * Returns the element that is being used as a placeholder\n   * while the current element is being dragged.\n   */\n  getPlaceholderElement() {\n    return this._placeholder;\n  }\n  /** Returns the root draggable element. */\n  getRootElement() {\n    return this._rootElement;\n  }\n  /**\n   * Gets the currently-visible element that represents the drag item.\n   * While dragging this is the placeholder, otherwise it's the root element.\n   */\n  getVisibleElement() {\n    return this.isDragging() ? this.getPlaceholderElement() : this.getRootElement();\n  }\n  /** Registers the handles that can be used to drag the element. */\n  withHandles(handles) {\n    this._handles = handles.map(handle => coerceElement(handle));\n    this._handles.forEach(handle => toggleNativeDragInteractions(handle, this.disabled));\n    this._toggleNativeDragInteractions();\n    // Delete any lingering disabled handles that may have been destroyed. Note that we re-create\n    // the set, rather than iterate over it and filter out the destroyed handles, because while\n    // the ES spec allows for sets to be modified while they're being iterated over, some polyfills\n    // use an array internally which may throw an error.\n    const disabledHandles = new Set();\n    this._disabledHandles.forEach(handle => {\n      if (this._handles.indexOf(handle) > -1) {\n        disabledHandles.add(handle);\n      }\n    });\n    this._disabledHandles = disabledHandles;\n    return this;\n  }\n  /**\n   * Registers the template that should be used for the drag preview.\n   * @param template Template that from which to stamp out the preview.\n   */\n  withPreviewTemplate(template) {\n    this._previewTemplate = template;\n    return this;\n  }\n  /**\n   * Registers the template that should be used for the drag placeholder.\n   * @param template Template that from which to stamp out the placeholder.\n   */\n  withPlaceholderTemplate(template) {\n    this._placeholderTemplate = template;\n    return this;\n  }\n  /**\n   * Sets an alternate drag root element. The root element is the element that will be moved as\n   * the user is dragging. Passing an alternate root element is useful when trying to enable\n   * dragging on an element that you might not have access to.\n   */\n  withRootElement(rootElement) {\n    const element = coerceElement(rootElement);\n    if (element !== this._rootElement) {\n      if (this._rootElement) {\n        this._removeRootElementListeners(this._rootElement);\n      }\n      this._ngZone.runOutsideAngular(() => {\n        element.addEventListener('mousedown', this._pointerDown, activeEventListenerOptions);\n        element.addEventListener('touchstart', this._pointerDown, passiveEventListenerOptions);\n        element.addEventListener('dragstart', this._nativeDragStart, activeEventListenerOptions);\n      });\n      this._initialTransform = undefined;\n      this._rootElement = element;\n    }\n    if (typeof SVGElement !== 'undefined' && this._rootElement instanceof SVGElement) {\n      this._ownerSVGElement = this._rootElement.ownerSVGElement;\n    }\n    return this;\n  }\n  /**\n   * Element to which the draggable's position will be constrained.\n   */\n  withBoundaryElement(boundaryElement) {\n    this._boundaryElement = boundaryElement ? coerceElement(boundaryElement) : null;\n    this._resizeSubscription.unsubscribe();\n    if (boundaryElement) {\n      this._resizeSubscription = this._viewportRuler.change(10).subscribe(() => this._containInsideBoundaryOnResize());\n    }\n    return this;\n  }\n  /** Sets the parent ref that the ref is nested in.  */\n  withParent(parent) {\n    this._parentDragRef = parent;\n    return this;\n  }\n  /** Removes the dragging functionality from the DOM element. */\n  dispose() {\n    this._removeRootElementListeners(this._rootElement);\n    // Do this check before removing from the registry since it'll\n    // stop being considered as dragged once it is removed.\n    if (this.isDragging()) {\n      // Since we move out the element to the end of the body while it's being\n      // dragged, we have to make sure that it's removed if it gets destroyed.\n      this._rootElement?.remove();\n    }\n    this._anchor?.remove();\n    this._destroyPreview();\n    this._destroyPlaceholder();\n    this._dragDropRegistry.removeDragItem(this);\n    this._removeListeners();\n    this.beforeStarted.complete();\n    this.started.complete();\n    this.released.complete();\n    this.ended.complete();\n    this.entered.complete();\n    this.exited.complete();\n    this.dropped.complete();\n    this._moveEvents.complete();\n    this._handles = [];\n    this._disabledHandles.clear();\n    this._dropContainer = undefined;\n    this._resizeSubscription.unsubscribe();\n    this._parentPositions.clear();\n    this._boundaryElement = this._rootElement = this._ownerSVGElement = this._placeholderTemplate = this._previewTemplate = this._anchor = this._parentDragRef = null;\n  }\n  /** Checks whether the element is currently being dragged. */\n  isDragging() {\n    return this._hasStartedDragging && this._dragDropRegistry.isDragging(this);\n  }\n  /** Resets a standalone drag item to its initial position. */\n  reset() {\n    this._rootElement.style.transform = this._initialTransform || '';\n    this._activeTransform = {\n      x: 0,\n      y: 0\n    };\n    this._passiveTransform = {\n      x: 0,\n      y: 0\n    };\n  }\n  /**\n   * Sets a handle as disabled. While a handle is disabled, it'll capture and interrupt dragging.\n   * @param handle Handle element that should be disabled.\n   */\n  disableHandle(handle) {\n    if (!this._disabledHandles.has(handle) && this._handles.indexOf(handle) > -1) {\n      this._disabledHandles.add(handle);\n      toggleNativeDragInteractions(handle, true);\n    }\n  }\n  /**\n   * Enables a handle, if it has been disabled.\n   * @param handle Handle element to be enabled.\n   */\n  enableHandle(handle) {\n    if (this._disabledHandles.has(handle)) {\n      this._disabledHandles.delete(handle);\n      toggleNativeDragInteractions(handle, this.disabled);\n    }\n  }\n  /** Sets the layout direction of the draggable item. */\n  withDirection(direction) {\n    this._direction = direction;\n    return this;\n  }\n  /** Sets the container that the item is part of. */\n  _withDropContainer(container) {\n    this._dropContainer = container;\n  }\n  /**\n   * Gets the current position in pixels the draggable outside of a drop container.\n   */\n  getFreeDragPosition() {\n    const position = this.isDragging() ? this._activeTransform : this._passiveTransform;\n    return {\n      x: position.x,\n      y: position.y\n    };\n  }\n  /**\n   * Sets the current position in pixels the draggable outside of a drop container.\n   * @param value New position to be set.\n   */\n  setFreeDragPosition(value) {\n    this._activeTransform = {\n      x: 0,\n      y: 0\n    };\n    this._passiveTransform.x = value.x;\n    this._passiveTransform.y = value.y;\n    if (!this._dropContainer) {\n      this._applyRootElementTransform(value.x, value.y);\n    }\n    return this;\n  }\n  /**\n   * Sets the container into which to insert the preview element.\n   * @param value Container into which to insert the preview.\n   */\n  withPreviewContainer(value) {\n    this._previewContainer = value;\n    return this;\n  }\n  /** Updates the item's sort order based on the last-known pointer position. */\n  _sortFromLastPointerPosition() {\n    const position = this._lastKnownPointerPosition;\n    if (position && this._dropContainer) {\n      this._updateActiveDropContainer(this._getConstrainedPointerPosition(position), position);\n    }\n  }\n  /** Unsubscribes from the global subscriptions. */\n  _removeListeners() {\n    this._pointerMoveSubscription.unsubscribe();\n    this._pointerUpSubscription.unsubscribe();\n    this._scrollSubscription.unsubscribe();\n    this._getShadowRoot()?.removeEventListener('selectstart', shadowDomSelectStart, activeCapturingEventOptions$1);\n  }\n  /** Destroys the preview element and its ViewRef. */\n  _destroyPreview() {\n    this._preview?.destroy();\n    this._preview = null;\n  }\n  /** Destroys the placeholder element and its ViewRef. */\n  _destroyPlaceholder() {\n    this._placeholder?.remove();\n    this._placeholderRef?.destroy();\n    this._placeholder = this._placeholderRef = null;\n  }\n  /**\n   * Clears subscriptions and stops the dragging sequence.\n   * @param event Browser event object that ended the sequence.\n   */\n  _endDragSequence(event) {\n    // Note that here we use `isDragging` from the service, rather than from `this`.\n    // The difference is that the one from the service reflects whether a dragging sequence\n    // has been initiated, whereas the one on `this` includes whether the user has passed\n    // the minimum dragging threshold.\n    if (!this._dragDropRegistry.isDragging(this)) {\n      return;\n    }\n    this._removeListeners();\n    this._dragDropRegistry.stopDragging(this);\n    this._toggleNativeDragInteractions();\n    if (this._handles) {\n      this._rootElement.style.webkitTapHighlightColor = this._rootElementTapHighlight;\n    }\n    if (!this._hasStartedDragging) {\n      return;\n    }\n    this.released.next({\n      source: this,\n      event\n    });\n    if (this._dropContainer) {\n      // Stop scrolling immediately, instead of waiting for the animation to finish.\n      this._dropContainer._stopScrolling();\n      this._animatePreviewToPlaceholder().then(() => {\n        this._cleanupDragArtifacts(event);\n        this._cleanupCachedDimensions();\n        this._dragDropRegistry.stopDragging(this);\n      });\n    } else {\n      // Convert the active transform into a passive one. This means that next time\n      // the user starts dragging the item, its position will be calculated relatively\n      // to the new passive transform.\n      this._passiveTransform.x = this._activeTransform.x;\n      const pointerPosition = this._getPointerPositionOnPage(event);\n      this._passiveTransform.y = this._activeTransform.y;\n      this._ngZone.run(() => {\n        this.ended.next({\n          source: this,\n          distance: this._getDragDistance(pointerPosition),\n          dropPoint: pointerPosition,\n          event\n        });\n      });\n      this._cleanupCachedDimensions();\n      this._dragDropRegistry.stopDragging(this);\n    }\n  }\n  /** Starts the dragging sequence. */\n  _startDragSequence(event) {\n    if (isTouchEvent(event)) {\n      this._lastTouchEventTime = Date.now();\n    }\n    this._toggleNativeDragInteractions();\n    // Needs to happen before the root element is moved.\n    const shadowRoot = this._getShadowRoot();\n    const dropContainer = this._dropContainer;\n    if (shadowRoot) {\n      // In some browsers the global `selectstart` that we maintain in the `DragDropRegistry`\n      // doesn't cross the shadow boundary so we have to prevent it at the shadow root (see #28792).\n      this._ngZone.runOutsideAngular(() => {\n        shadowRoot.addEventListener('selectstart', shadowDomSelectStart, activeCapturingEventOptions$1);\n      });\n    }\n    if (dropContainer) {\n      const element = this._rootElement;\n      const parent = element.parentNode;\n      const placeholder = this._placeholder = this._createPlaceholderElement();\n      const anchor = this._anchor = this._anchor || this._document.createComment('');\n      // Insert an anchor node so that we can restore the element's position in the DOM.\n      parent.insertBefore(anchor, element);\n      // There's no risk of transforms stacking when inside a drop container so\n      // we can keep the initial transform up to date any time dragging starts.\n      this._initialTransform = element.style.transform || '';\n      // Create the preview after the initial transform has\n      // been cached, because it can be affected by the transform.\n      this._preview = new PreviewRef(this._document, this._rootElement, this._direction, this._initialDomRect, this._previewTemplate || null, this.previewClass || null, this._pickupPositionOnPage, this._initialTransform, this._config.zIndex || 1000);\n      this._preview.attach(this._getPreviewInsertionPoint(parent, shadowRoot));\n      // We move the element out at the end of the body and we make it hidden, because keeping it in\n      // place will throw off the consumer's `:last-child` selectors. We can't remove the element\n      // from the DOM completely, because iOS will stop firing all subsequent events in the chain.\n      toggleVisibility(element, false, dragImportantProperties);\n      this._document.body.appendChild(parent.replaceChild(placeholder, element));\n      this.started.next({\n        source: this,\n        event\n      }); // Emit before notifying the container.\n      dropContainer.start();\n      this._initialContainer = dropContainer;\n      this._initialIndex = dropContainer.getItemIndex(this);\n    } else {\n      this.started.next({\n        source: this,\n        event\n      });\n      this._initialContainer = this._initialIndex = undefined;\n    }\n    // Important to run after we've called `start` on the parent container\n    // so that it has had time to resolve its scrollable parents.\n    this._parentPositions.cache(dropContainer ? dropContainer.getScrollableParents() : []);\n  }\n  /**\n   * Sets up the different variables and subscriptions\n   * that will be necessary for the dragging sequence.\n   * @param referenceElement Element that started the drag sequence.\n   * @param event Browser event object that started the sequence.\n   */\n  _initializeDragSequence(referenceElement, event) {\n    // Stop propagation if the item is inside another\n    // draggable so we don't start multiple drag sequences.\n    if (this._parentDragRef) {\n      event.stopPropagation();\n    }\n    const isDragging = this.isDragging();\n    const isTouchSequence = isTouchEvent(event);\n    const isAuxiliaryMouseButton = !isTouchSequence && event.button !== 0;\n    const rootElement = this._rootElement;\n    const target = _getEventTarget(event);\n    const isSyntheticEvent = !isTouchSequence && this._lastTouchEventTime && this._lastTouchEventTime + MOUSE_EVENT_IGNORE_TIME > Date.now();\n    const isFakeEvent = isTouchSequence ? isFakeTouchstartFromScreenReader(event) : isFakeMousedownFromScreenReader(event);\n    // If the event started from an element with the native HTML drag&drop, it'll interfere\n    // with our own dragging (e.g. `img` tags do it by default). Prevent the default action\n    // to stop it from happening. Note that preventing on `dragstart` also seems to work, but\n    // it's flaky and it fails if the user drags it away quickly. Also note that we only want\n    // to do this for `mousedown` since doing the same for `touchstart` will stop any `click`\n    // events from firing on touch devices.\n    if (target && target.draggable && event.type === 'mousedown') {\n      event.preventDefault();\n    }\n    // Abort if the user is already dragging or is using a mouse button other than the primary one.\n    if (isDragging || isAuxiliaryMouseButton || isSyntheticEvent || isFakeEvent) {\n      return;\n    }\n    // If we've got handles, we need to disable the tap highlight on the entire root element,\n    // otherwise iOS will still add it, even though all the drag interactions on the handle\n    // are disabled.\n    if (this._handles.length) {\n      const rootStyles = rootElement.style;\n      this._rootElementTapHighlight = rootStyles.webkitTapHighlightColor || '';\n      rootStyles.webkitTapHighlightColor = 'transparent';\n    }\n    this._hasStartedDragging = this._hasMoved = false;\n    // Avoid multiple subscriptions and memory leaks when multi touch\n    // (isDragging check above isn't enough because of possible temporal and/or dimensional delays)\n    this._removeListeners();\n    this._initialDomRect = this._rootElement.getBoundingClientRect();\n    this._pointerMoveSubscription = this._dragDropRegistry.pointerMove.subscribe(this._pointerMove);\n    this._pointerUpSubscription = this._dragDropRegistry.pointerUp.subscribe(this._pointerUp);\n    this._scrollSubscription = this._dragDropRegistry.scrolled(this._getShadowRoot()).subscribe(scrollEvent => this._updateOnScroll(scrollEvent));\n    if (this._boundaryElement) {\n      this._boundaryRect = getMutableClientRect(this._boundaryElement);\n    }\n    // If we have a custom preview we can't know ahead of time how large it'll be so we position\n    // it next to the cursor. The exception is when the consumer has opted into making the preview\n    // the same size as the root element, in which case we do know the size.\n    const previewTemplate = this._previewTemplate;\n    this._pickupPositionInElement = previewTemplate && previewTemplate.template && !previewTemplate.matchSize ? {\n      x: 0,\n      y: 0\n    } : this._getPointerPositionInElement(this._initialDomRect, referenceElement, event);\n    const pointerPosition = this._pickupPositionOnPage = this._lastKnownPointerPosition = this._getPointerPositionOnPage(event);\n    this._pointerDirectionDelta = {\n      x: 0,\n      y: 0\n    };\n    this._pointerPositionAtLastDirectionChange = {\n      x: pointerPosition.x,\n      y: pointerPosition.y\n    };\n    this._dragStartTime = Date.now();\n    this._dragDropRegistry.startDragging(this, event);\n  }\n  /** Cleans up the DOM artifacts that were added to facilitate the element being dragged. */\n  _cleanupDragArtifacts(event) {\n    // Restore the element's visibility and insert it at its old position in the DOM.\n    // It's important that we maintain the position, because moving the element around in the DOM\n    // can throw off `NgFor` which does smart diffing and re-creates elements only when necessary,\n    // while moving the existing elements in all other cases.\n    toggleVisibility(this._rootElement, true, dragImportantProperties);\n    this._anchor.parentNode.replaceChild(this._rootElement, this._anchor);\n    this._destroyPreview();\n    this._destroyPlaceholder();\n    this._initialDomRect = this._boundaryRect = this._previewRect = this._initialTransform = undefined;\n    // Re-enter the NgZone since we bound `document` events on the outside.\n    this._ngZone.run(() => {\n      const container = this._dropContainer;\n      const currentIndex = container.getItemIndex(this);\n      const pointerPosition = this._getPointerPositionOnPage(event);\n      const distance = this._getDragDistance(pointerPosition);\n      const isPointerOverContainer = container._isOverContainer(pointerPosition.x, pointerPosition.y);\n      this.ended.next({\n        source: this,\n        distance,\n        dropPoint: pointerPosition,\n        event\n      });\n      this.dropped.next({\n        item: this,\n        currentIndex,\n        previousIndex: this._initialIndex,\n        container: container,\n        previousContainer: this._initialContainer,\n        isPointerOverContainer,\n        distance,\n        dropPoint: pointerPosition,\n        event\n      });\n      container.drop(this, currentIndex, this._initialIndex, this._initialContainer, isPointerOverContainer, distance, pointerPosition, event);\n      this._dropContainer = this._initialContainer;\n    });\n  }\n  /**\n   * Updates the item's position in its drop container, or moves it\n   * into a new one, depending on its current drag position.\n   */\n  _updateActiveDropContainer({\n    x,\n    y\n  }, {\n    x: rawX,\n    y: rawY\n  }) {\n    // Drop container that draggable has been moved into.\n    let newContainer = this._initialContainer._getSiblingContainerFromPosition(this, x, y);\n    // If we couldn't find a new container to move the item into, and the item has left its\n    // initial container, check whether the it's over the initial container. This handles the\n    // case where two containers are connected one way and the user tries to undo dragging an\n    // item into a new container.\n    if (!newContainer && this._dropContainer !== this._initialContainer && this._initialContainer._isOverContainer(x, y)) {\n      newContainer = this._initialContainer;\n    }\n    if (newContainer && newContainer !== this._dropContainer) {\n      this._ngZone.run(() => {\n        // Notify the old container that the item has left.\n        this.exited.next({\n          item: this,\n          container: this._dropContainer\n        });\n        this._dropContainer.exit(this);\n        // Notify the new container that the item has entered.\n        this._dropContainer = newContainer;\n        this._dropContainer.enter(this, x, y, newContainer === this._initialContainer &&\n        // If we're re-entering the initial container and sorting is disabled,\n        // put item the into its starting index to begin with.\n        newContainer.sortingDisabled ? this._initialIndex : undefined);\n        this.entered.next({\n          item: this,\n          container: newContainer,\n          currentIndex: newContainer.getItemIndex(this)\n        });\n      });\n    }\n    // Dragging may have been interrupted as a result of the events above.\n    if (this.isDragging()) {\n      this._dropContainer._startScrollingIfNecessary(rawX, rawY);\n      this._dropContainer._sortItem(this, x, y, this._pointerDirectionDelta);\n      if (this.constrainPosition) {\n        this._applyPreviewTransform(x, y);\n      } else {\n        this._applyPreviewTransform(x - this._pickupPositionInElement.x, y - this._pickupPositionInElement.y);\n      }\n    }\n  }\n  /**\n   * Animates the preview element from its current position to the location of the drop placeholder.\n   * @returns Promise that resolves when the animation completes.\n   */\n  _animatePreviewToPlaceholder() {\n    // If the user hasn't moved yet, the transitionend event won't fire.\n    if (!this._hasMoved) {\n      return Promise.resolve();\n    }\n    const placeholderRect = this._placeholder.getBoundingClientRect();\n    // Apply the class that adds a transition to the preview.\n    this._preview.addClass('cdk-drag-animating');\n    // Move the preview to the placeholder position.\n    this._applyPreviewTransform(placeholderRect.left, placeholderRect.top);\n    // If the element doesn't have a `transition`, the `transitionend` event won't fire. Since\n    // we need to trigger a style recalculation in order for the `cdk-drag-animating` class to\n    // apply its style, we take advantage of the available info to figure out whether we need to\n    // bind the event in the first place.\n    const duration = this._preview.getTransitionDuration();\n    if (duration === 0) {\n      return Promise.resolve();\n    }\n    return this._ngZone.runOutsideAngular(() => {\n      return new Promise(resolve => {\n        const handler = event => {\n          if (!event || _getEventTarget(event) === this._preview && event.propertyName === 'transform') {\n            this._preview?.removeEventListener('transitionend', handler);\n            resolve();\n            clearTimeout(timeout);\n          }\n        };\n        // If a transition is short enough, the browser might not fire the `transitionend` event.\n        // Since we know how long it's supposed to take, add a timeout with a 50% buffer that'll\n        // fire if the transition hasn't completed when it was supposed to.\n        const timeout = setTimeout(handler, duration * 1.5);\n        this._preview.addEventListener('transitionend', handler);\n      });\n    });\n  }\n  /** Creates an element that will be shown instead of the current element while dragging. */\n  _createPlaceholderElement() {\n    const placeholderConfig = this._placeholderTemplate;\n    const placeholderTemplate = placeholderConfig ? placeholderConfig.template : null;\n    let placeholder;\n    if (placeholderTemplate) {\n      this._placeholderRef = placeholderConfig.viewContainer.createEmbeddedView(placeholderTemplate, placeholderConfig.context);\n      this._placeholderRef.detectChanges();\n      placeholder = getRootNode(this._placeholderRef, this._document);\n    } else {\n      placeholder = deepCloneNode(this._rootElement);\n    }\n    // Stop pointer events on the preview so the user can't\n    // interact with it while the preview is animating.\n    placeholder.style.pointerEvents = 'none';\n    placeholder.classList.add('cdk-drag-placeholder');\n    return placeholder;\n  }\n  /**\n   * Figures out the coordinates at which an element was picked up.\n   * @param referenceElement Element that initiated the dragging.\n   * @param event Event that initiated the dragging.\n   */\n  _getPointerPositionInElement(elementRect, referenceElement, event) {\n    const handleElement = referenceElement === this._rootElement ? null : referenceElement;\n    const referenceRect = handleElement ? handleElement.getBoundingClientRect() : elementRect;\n    const point = isTouchEvent(event) ? event.targetTouches[0] : event;\n    const scrollPosition = this._getViewportScrollPosition();\n    const x = point.pageX - referenceRect.left - scrollPosition.left;\n    const y = point.pageY - referenceRect.top - scrollPosition.top;\n    return {\n      x: referenceRect.left - elementRect.left + x,\n      y: referenceRect.top - elementRect.top + y\n    };\n  }\n  /** Determines the point of the page that was touched by the user. */\n  _getPointerPositionOnPage(event) {\n    const scrollPosition = this._getViewportScrollPosition();\n    const point = isTouchEvent(event) ?\n    // `touches` will be empty for start/end events so we have to fall back to `changedTouches`.\n    // Also note that on real devices we're guaranteed for either `touches` or `changedTouches`\n    // to have a value, but Firefox in device emulation mode has a bug where both can be empty\n    // for `touchstart` and `touchend` so we fall back to a dummy object in order to avoid\n    // throwing an error. The value returned here will be incorrect, but since this only\n    // breaks inside a developer tool and the value is only used for secondary information,\n    // we can get away with it. See https://bugzilla.mozilla.org/show_bug.cgi?id=1615824.\n    event.touches[0] || event.changedTouches[0] || {\n      pageX: 0,\n      pageY: 0\n    } : event;\n    const x = point.pageX - scrollPosition.left;\n    const y = point.pageY - scrollPosition.top;\n    // if dragging SVG element, try to convert from the screen coordinate system to the SVG\n    // coordinate system\n    if (this._ownerSVGElement) {\n      const svgMatrix = this._ownerSVGElement.getScreenCTM();\n      if (svgMatrix) {\n        const svgPoint = this._ownerSVGElement.createSVGPoint();\n        svgPoint.x = x;\n        svgPoint.y = y;\n        return svgPoint.matrixTransform(svgMatrix.inverse());\n      }\n    }\n    return {\n      x,\n      y\n    };\n  }\n  /** Gets the pointer position on the page, accounting for any position constraints. */\n  _getConstrainedPointerPosition(point) {\n    const dropContainerLock = this._dropContainer ? this._dropContainer.lockAxis : null;\n    let {\n      x,\n      y\n    } = this.constrainPosition ? this.constrainPosition(point, this, this._initialDomRect, this._pickupPositionInElement) : point;\n    if (this.lockAxis === 'x' || dropContainerLock === 'x') {\n      y = this._pickupPositionOnPage.y - (this.constrainPosition ? this._pickupPositionInElement.y : 0);\n    } else if (this.lockAxis === 'y' || dropContainerLock === 'y') {\n      x = this._pickupPositionOnPage.x - (this.constrainPosition ? this._pickupPositionInElement.x : 0);\n    }\n    if (this._boundaryRect) {\n      // If not using a custom constrain we need to account for the pickup position in the element\n      // otherwise we do not need to do this, as it has already been accounted for\n      const {\n        x: pickupX,\n        y: pickupY\n      } = !this.constrainPosition ? this._pickupPositionInElement : {\n        x: 0,\n        y: 0\n      };\n      const boundaryRect = this._boundaryRect;\n      const {\n        width: previewWidth,\n        height: previewHeight\n      } = this._getPreviewRect();\n      const minY = boundaryRect.top + pickupY;\n      const maxY = boundaryRect.bottom - (previewHeight - pickupY);\n      const minX = boundaryRect.left + pickupX;\n      const maxX = boundaryRect.right - (previewWidth - pickupX);\n      x = clamp$1(x, minX, maxX);\n      y = clamp$1(y, minY, maxY);\n    }\n    return {\n      x,\n      y\n    };\n  }\n  /** Updates the current drag delta, based on the user's current pointer position on the page. */\n  _updatePointerDirectionDelta(pointerPositionOnPage) {\n    const {\n      x,\n      y\n    } = pointerPositionOnPage;\n    const delta = this._pointerDirectionDelta;\n    const positionSinceLastChange = this._pointerPositionAtLastDirectionChange;\n    // Amount of pixels the user has dragged since the last time the direction changed.\n    const changeX = Math.abs(x - positionSinceLastChange.x);\n    const changeY = Math.abs(y - positionSinceLastChange.y);\n    // Because we handle pointer events on a per-pixel basis, we don't want the delta\n    // to change for every pixel, otherwise anything that depends on it can look erratic.\n    // To make the delta more consistent, we track how much the user has moved since the last\n    // delta change and we only update it after it has reached a certain threshold.\n    if (changeX > this._config.pointerDirectionChangeThreshold) {\n      delta.x = x > positionSinceLastChange.x ? 1 : -1;\n      positionSinceLastChange.x = x;\n    }\n    if (changeY > this._config.pointerDirectionChangeThreshold) {\n      delta.y = y > positionSinceLastChange.y ? 1 : -1;\n      positionSinceLastChange.y = y;\n    }\n    return delta;\n  }\n  /** Toggles the native drag interactions, based on how many handles are registered. */\n  _toggleNativeDragInteractions() {\n    if (!this._rootElement || !this._handles) {\n      return;\n    }\n    const shouldEnable = this._handles.length > 0 || !this.isDragging();\n    if (shouldEnable !== this._nativeInteractionsEnabled) {\n      this._nativeInteractionsEnabled = shouldEnable;\n      toggleNativeDragInteractions(this._rootElement, shouldEnable);\n    }\n  }\n  /** Removes the manually-added event listeners from the root element. */\n  _removeRootElementListeners(element) {\n    element.removeEventListener('mousedown', this._pointerDown, activeEventListenerOptions);\n    element.removeEventListener('touchstart', this._pointerDown, passiveEventListenerOptions);\n    element.removeEventListener('dragstart', this._nativeDragStart, activeEventListenerOptions);\n  }\n  /**\n   * Applies a `transform` to the root element, taking into account any existing transforms on it.\n   * @param x New transform value along the X axis.\n   * @param y New transform value along the Y axis.\n   */\n  _applyRootElementTransform(x, y) {\n    const transform = getTransform(x, y);\n    const styles = this._rootElement.style;\n    // Cache the previous transform amount only after the first drag sequence, because\n    // we don't want our own transforms to stack on top of each other.\n    // Should be excluded none because none + translate3d(x, y, x) is invalid css\n    if (this._initialTransform == null) {\n      this._initialTransform = styles.transform && styles.transform != 'none' ? styles.transform : '';\n    }\n    // Preserve the previous `transform` value, if there was one. Note that we apply our own\n    // transform before the user's, because things like rotation can affect which direction\n    // the element will be translated towards.\n    styles.transform = combineTransforms(transform, this._initialTransform);\n  }\n  /**\n   * Applies a `transform` to the preview, taking into account any existing transforms on it.\n   * @param x New transform value along the X axis.\n   * @param y New transform value along the Y axis.\n   */\n  _applyPreviewTransform(x, y) {\n    // Only apply the initial transform if the preview is a clone of the original element, otherwise\n    // it could be completely different and the transform might not make sense anymore.\n    const initialTransform = this._previewTemplate?.template ? undefined : this._initialTransform;\n    const transform = getTransform(x, y);\n    this._preview.setTransform(combineTransforms(transform, initialTransform));\n  }\n  /**\n   * Gets the distance that the user has dragged during the current drag sequence.\n   * @param currentPosition Current position of the user's pointer.\n   */\n  _getDragDistance(currentPosition) {\n    const pickupPosition = this._pickupPositionOnPage;\n    if (pickupPosition) {\n      return {\n        x: currentPosition.x - pickupPosition.x,\n        y: currentPosition.y - pickupPosition.y\n      };\n    }\n    return {\n      x: 0,\n      y: 0\n    };\n  }\n  /** Cleans up any cached element dimensions that we don't need after dragging has stopped. */\n  _cleanupCachedDimensions() {\n    this._boundaryRect = this._previewRect = undefined;\n    this._parentPositions.clear();\n  }\n  /**\n   * Checks whether the element is still inside its boundary after the viewport has been resized.\n   * If not, the position is adjusted so that the element fits again.\n   */\n  _containInsideBoundaryOnResize() {\n    let {\n      x,\n      y\n    } = this._passiveTransform;\n    if (x === 0 && y === 0 || this.isDragging() || !this._boundaryElement) {\n      return;\n    }\n    // Note: don't use `_clientRectAtStart` here, because we want the latest position.\n    const elementRect = this._rootElement.getBoundingClientRect();\n    const boundaryRect = this._boundaryElement.getBoundingClientRect();\n    // It's possible that the element got hidden away after dragging (e.g. by switching to a\n    // different tab). Don't do anything in this case so we don't clear the user's position.\n    if (boundaryRect.width === 0 && boundaryRect.height === 0 || elementRect.width === 0 && elementRect.height === 0) {\n      return;\n    }\n    const leftOverflow = boundaryRect.left - elementRect.left;\n    const rightOverflow = elementRect.right - boundaryRect.right;\n    const topOverflow = boundaryRect.top - elementRect.top;\n    const bottomOverflow = elementRect.bottom - boundaryRect.bottom;\n    // If the element has become wider than the boundary, we can't\n    // do much to make it fit so we just anchor it to the left.\n    if (boundaryRect.width > elementRect.width) {\n      if (leftOverflow > 0) {\n        x += leftOverflow;\n      }\n      if (rightOverflow > 0) {\n        x -= rightOverflow;\n      }\n    } else {\n      x = 0;\n    }\n    // If the element has become taller than the boundary, we can't\n    // do much to make it fit so we just anchor it to the top.\n    if (boundaryRect.height > elementRect.height) {\n      if (topOverflow > 0) {\n        y += topOverflow;\n      }\n      if (bottomOverflow > 0) {\n        y -= bottomOverflow;\n      }\n    } else {\n      y = 0;\n    }\n    if (x !== this._passiveTransform.x || y !== this._passiveTransform.y) {\n      this.setFreeDragPosition({\n        y,\n        x\n      });\n    }\n  }\n  /** Gets the drag start delay, based on the event type. */\n  _getDragStartDelay(event) {\n    const value = this.dragStartDelay;\n    if (typeof value === 'number') {\n      return value;\n    } else if (isTouchEvent(event)) {\n      return value.touch;\n    }\n    return value ? value.mouse : 0;\n  }\n  /** Updates the internal state of the draggable element when scrolling has occurred. */\n  _updateOnScroll(event) {\n    const scrollDifference = this._parentPositions.handleScroll(event);\n    if (scrollDifference) {\n      const target = _getEventTarget(event);\n      // DOMRect dimensions are based on the scroll position of the page and its parent\n      // node so we have to update the cached boundary DOMRect if the user has scrolled.\n      if (this._boundaryRect && target !== this._boundaryElement && target.contains(this._boundaryElement)) {\n        adjustDomRect(this._boundaryRect, scrollDifference.top, scrollDifference.left);\n      }\n      this._pickupPositionOnPage.x += scrollDifference.left;\n      this._pickupPositionOnPage.y += scrollDifference.top;\n      // If we're in free drag mode, we have to update the active transform, because\n      // it isn't relative to the viewport like the preview inside a drop list.\n      if (!this._dropContainer) {\n        this._activeTransform.x -= scrollDifference.left;\n        this._activeTransform.y -= scrollDifference.top;\n        this._applyRootElementTransform(this._activeTransform.x, this._activeTransform.y);\n      }\n    }\n  }\n  /** Gets the scroll position of the viewport. */\n  _getViewportScrollPosition() {\n    return this._parentPositions.positions.get(this._document)?.scrollPosition || this._parentPositions.getViewportScrollPosition();\n  }\n  /**\n   * Lazily resolves and returns the shadow root of the element. We do this in a function, rather\n   * than saving it in property directly on init, because we want to resolve it as late as possible\n   * in order to ensure that the element has been moved into the shadow DOM. Doing it inside the\n   * constructor might be too early if the element is inside of something like `ngFor` or `ngIf`.\n   */\n  _getShadowRoot() {\n    if (this._cachedShadowRoot === undefined) {\n      this._cachedShadowRoot = _getShadowRoot(this._rootElement);\n    }\n    return this._cachedShadowRoot;\n  }\n  /** Gets the element into which the drag preview should be inserted. */\n  _getPreviewInsertionPoint(initialParent, shadowRoot) {\n    const previewContainer = this._previewContainer || 'global';\n    if (previewContainer === 'parent') {\n      return initialParent;\n    }\n    if (previewContainer === 'global') {\n      const documentRef = this._document;\n      // We can't use the body if the user is in fullscreen mode,\n      // because the preview will render under the fullscreen element.\n      // TODO(crisbeto): dedupe this with the `FullscreenOverlayContainer` eventually.\n      return shadowRoot || documentRef.fullscreenElement || documentRef.webkitFullscreenElement || documentRef.mozFullScreenElement || documentRef.msFullscreenElement || documentRef.body;\n    }\n    return coerceElement(previewContainer);\n  }\n  /** Lazily resolves and returns the dimensions of the preview. */\n  _getPreviewRect() {\n    // Cache the preview element rect if we haven't cached it already or if\n    // we cached it too early before the element dimensions were computed.\n    if (!this._previewRect || !this._previewRect.width && !this._previewRect.height) {\n      this._previewRect = this._preview ? this._preview.getBoundingClientRect() : this._initialDomRect;\n    }\n    return this._previewRect;\n  }\n  /** Gets a handle that is the target of an event. */\n  _getTargetHandle(event) {\n    return this._handles.find(handle => {\n      return event.target && (event.target === handle || handle.contains(event.target));\n    });\n  }\n}\n/** Clamps a value between a minimum and a maximum. */\nfunction clamp$1(value, min, max) {\n  return Math.max(min, Math.min(max, value));\n}\n/** Determines whether an event is a touch event. */\nfunction isTouchEvent(event) {\n  // This function is called for every pixel that the user has dragged so we need it to be\n  // as fast as possible. Since we only bind mouse events and touch events, we can assume\n  // that if the event's name starts with `t`, it's a touch event.\n  return event.type[0] === 't';\n}\n/** Callback invoked for `selectstart` events inside the shadow DOM. */\nfunction shadowDomSelectStart(event) {\n  event.preventDefault();\n}\n\n/**\n * Moves an item one index in an array to another.\n * @param array Array in which to move the item.\n * @param fromIndex Starting index of the item.\n * @param toIndex Index to which the item should be moved.\n */\nfunction moveItemInArray(array, fromIndex, toIndex) {\n  const from = clamp(fromIndex, array.length - 1);\n  const to = clamp(toIndex, array.length - 1);\n  if (from === to) {\n    return;\n  }\n  const target = array[from];\n  const delta = to < from ? -1 : 1;\n  for (let i = from; i !== to; i += delta) {\n    array[i] = array[i + delta];\n  }\n  array[to] = target;\n}\n/**\n * Moves an item from one array to another.\n * @param currentArray Array from which to transfer the item.\n * @param targetArray Array into which to put the item.\n * @param currentIndex Index of the item in its current array.\n * @param targetIndex Index at which to insert the item.\n */\nfunction transferArrayItem(currentArray, targetArray, currentIndex, targetIndex) {\n  const from = clamp(currentIndex, currentArray.length - 1);\n  const to = clamp(targetIndex, targetArray.length);\n  if (currentArray.length) {\n    targetArray.splice(to, 0, currentArray.splice(from, 1)[0]);\n  }\n}\n/**\n * Copies an item from one array to another, leaving it in its\n * original position in current array.\n * @param currentArray Array from which to copy the item.\n * @param targetArray Array into which is copy the item.\n * @param currentIndex Index of the item in its current array.\n * @param targetIndex Index at which to insert the item.\n *\n */\nfunction copyArrayItem(currentArray, targetArray, currentIndex, targetIndex) {\n  const to = clamp(targetIndex, targetArray.length);\n  if (currentArray.length) {\n    targetArray.splice(to, 0, currentArray[currentIndex]);\n  }\n}\n/** Clamps a number between zero and a maximum. */\nfunction clamp(value, max) {\n  return Math.max(0, Math.min(max, value));\n}\n\n/**\n * Strategy that only supports sorting along a single axis.\n * Items are reordered using CSS transforms which allows for sorting to be animated.\n * @docs-private\n */\nclass SingleAxisSortStrategy {\n  constructor(_element, _dragDropRegistry) {\n    this._element = _element;\n    this._dragDropRegistry = _dragDropRegistry;\n    /** Cache of the dimensions of all the items inside the container. */\n    this._itemPositions = [];\n    /** Direction in which the list is oriented. */\n    this.orientation = 'vertical';\n    /**\n     * Keeps track of the item that was last swapped with the dragged item, as well as what direction\n     * the pointer was moving in when the swap occurred and whether the user's pointer continued to\n     * overlap with the swapped item after the swapping occurred.\n     */\n    this._previousSwap = {\n      drag: null,\n      delta: 0,\n      overlaps: false\n    };\n  }\n  /**\n   * To be called when the drag sequence starts.\n   * @param items Items that are currently in the list.\n   */\n  start(items) {\n    this.withItems(items);\n  }\n  /**\n   * To be called when an item is being sorted.\n   * @param item Item to be sorted.\n   * @param pointerX Position of the item along the X axis.\n   * @param pointerY Position of the item along the Y axis.\n   * @param pointerDelta Direction in which the pointer is moving along each axis.\n   */\n  sort(item, pointerX, pointerY, pointerDelta) {\n    const siblings = this._itemPositions;\n    const newIndex = this._getItemIndexFromPointerPosition(item, pointerX, pointerY, pointerDelta);\n    if (newIndex === -1 && siblings.length > 0) {\n      return null;\n    }\n    const isHorizontal = this.orientation === 'horizontal';\n    const currentIndex = siblings.findIndex(currentItem => currentItem.drag === item);\n    const siblingAtNewPosition = siblings[newIndex];\n    const currentPosition = siblings[currentIndex].clientRect;\n    const newPosition = siblingAtNewPosition.clientRect;\n    const delta = currentIndex > newIndex ? 1 : -1;\n    // How many pixels the item's placeholder should be offset.\n    const itemOffset = this._getItemOffsetPx(currentPosition, newPosition, delta);\n    // How many pixels all the other items should be offset.\n    const siblingOffset = this._getSiblingOffsetPx(currentIndex, siblings, delta);\n    // Save the previous order of the items before moving the item to its new index.\n    // We use this to check whether an item has been moved as a result of the sorting.\n    const oldOrder = siblings.slice();\n    // Shuffle the array in place.\n    moveItemInArray(siblings, currentIndex, newIndex);\n    siblings.forEach((sibling, index) => {\n      // Don't do anything if the position hasn't changed.\n      if (oldOrder[index] === sibling) {\n        return;\n      }\n      const isDraggedItem = sibling.drag === item;\n      const offset = isDraggedItem ? itemOffset : siblingOffset;\n      const elementToOffset = isDraggedItem ? item.getPlaceholderElement() : sibling.drag.getRootElement();\n      // Update the offset to reflect the new position.\n      sibling.offset += offset;\n      // Since we're moving the items with a `transform`, we need to adjust their cached\n      // client rects to reflect their new position, as well as swap their positions in the cache.\n      // Note that we shouldn't use `getBoundingClientRect` here to update the cache, because the\n      // elements may be mid-animation which will give us a wrong result.\n      if (isHorizontal) {\n        // Round the transforms since some browsers will\n        // blur the elements, for sub-pixel transforms.\n        elementToOffset.style.transform = combineTransforms(`translate3d(${Math.round(sibling.offset)}px, 0, 0)`, sibling.initialTransform);\n        adjustDomRect(sibling.clientRect, 0, offset);\n      } else {\n        elementToOffset.style.transform = combineTransforms(`translate3d(0, ${Math.round(sibling.offset)}px, 0)`, sibling.initialTransform);\n        adjustDomRect(sibling.clientRect, offset, 0);\n      }\n    });\n    // Note that it's important that we do this after the client rects have been adjusted.\n    this._previousSwap.overlaps = isInsideClientRect(newPosition, pointerX, pointerY);\n    this._previousSwap.drag = siblingAtNewPosition.drag;\n    this._previousSwap.delta = isHorizontal ? pointerDelta.x : pointerDelta.y;\n    return {\n      previousIndex: currentIndex,\n      currentIndex: newIndex\n    };\n  }\n  /**\n   * Called when an item is being moved into the container.\n   * @param item Item that was moved into the container.\n   * @param pointerX Position of the item along the X axis.\n   * @param pointerY Position of the item along the Y axis.\n   * @param index Index at which the item entered. If omitted, the container will try to figure it\n   *   out automatically.\n   */\n  enter(item, pointerX, pointerY, index) {\n    const newIndex = index == null || index < 0 ?\n    // We use the coordinates of where the item entered the drop\n    // zone to figure out at which index it should be inserted.\n    this._getItemIndexFromPointerPosition(item, pointerX, pointerY) : index;\n    const activeDraggables = this._activeDraggables;\n    const currentIndex = activeDraggables.indexOf(item);\n    const placeholder = item.getPlaceholderElement();\n    let newPositionReference = activeDraggables[newIndex];\n    // If the item at the new position is the same as the item that is being dragged,\n    // it means that we're trying to restore the item to its initial position. In this\n    // case we should use the next item from the list as the reference.\n    if (newPositionReference === item) {\n      newPositionReference = activeDraggables[newIndex + 1];\n    }\n    // If we didn't find a new position reference, it means that either the item didn't start off\n    // in this container, or that the item requested to be inserted at the end of the list.\n    if (!newPositionReference && (newIndex == null || newIndex === -1 || newIndex < activeDraggables.length - 1) && this._shouldEnterAsFirstChild(pointerX, pointerY)) {\n      newPositionReference = activeDraggables[0];\n    }\n    // Since the item may be in the `activeDraggables` already (e.g. if the user dragged it\n    // into another container and back again), we have to ensure that it isn't duplicated.\n    if (currentIndex > -1) {\n      activeDraggables.splice(currentIndex, 1);\n    }\n    // Don't use items that are being dragged as a reference, because\n    // their element has been moved down to the bottom of the body.\n    if (newPositionReference && !this._dragDropRegistry.isDragging(newPositionReference)) {\n      const element = newPositionReference.getRootElement();\n      element.parentElement.insertBefore(placeholder, element);\n      activeDraggables.splice(newIndex, 0, item);\n    } else {\n      coerceElement(this._element).appendChild(placeholder);\n      activeDraggables.push(item);\n    }\n    // The transform needs to be cleared so it doesn't throw off the measurements.\n    placeholder.style.transform = '';\n    // Note that usually `start` is called together with `enter` when an item goes into a new\n    // container. This will cache item positions, but we need to refresh them since the amount\n    // of items has changed.\n    this._cacheItemPositions();\n  }\n  /** Sets the items that are currently part of the list. */\n  withItems(items) {\n    this._activeDraggables = items.slice();\n    this._cacheItemPositions();\n  }\n  /** Assigns a sort predicate to the strategy. */\n  withSortPredicate(predicate) {\n    this._sortPredicate = predicate;\n  }\n  /** Resets the strategy to its initial state before dragging was started. */\n  reset() {\n    // TODO(crisbeto): may have to wait for the animations to finish.\n    this._activeDraggables.forEach(item => {\n      const rootElement = item.getRootElement();\n      if (rootElement) {\n        const initialTransform = this._itemPositions.find(p => p.drag === item)?.initialTransform;\n        rootElement.style.transform = initialTransform || '';\n      }\n    });\n    this._itemPositions = [];\n    this._activeDraggables = [];\n    this._previousSwap.drag = null;\n    this._previousSwap.delta = 0;\n    this._previousSwap.overlaps = false;\n  }\n  /**\n   * Gets a snapshot of items currently in the list.\n   * Can include items that we dragged in from another list.\n   */\n  getActiveItemsSnapshot() {\n    return this._activeDraggables;\n  }\n  /** Gets the index of a specific item. */\n  getItemIndex(item) {\n    // Items are sorted always by top/left in the cache, however they flow differently in RTL.\n    // The rest of the logic still stands no matter what orientation we're in, however\n    // we need to invert the array when determining the index.\n    const items = this.orientation === 'horizontal' && this.direction === 'rtl' ? this._itemPositions.slice().reverse() : this._itemPositions;\n    return items.findIndex(currentItem => currentItem.drag === item);\n  }\n  /** Used to notify the strategy that the scroll position has changed. */\n  updateOnScroll(topDifference, leftDifference) {\n    // Since we know the amount that the user has scrolled we can shift all of the\n    // client rectangles ourselves. This is cheaper than re-measuring everything and\n    // we can avoid inconsistent behavior where we might be measuring the element before\n    // its position has changed.\n    this._itemPositions.forEach(({\n      clientRect\n    }) => {\n      adjustDomRect(clientRect, topDifference, leftDifference);\n    });\n    // We need two loops for this, because we want all of the cached\n    // positions to be up-to-date before we re-sort the item.\n    this._itemPositions.forEach(({\n      drag\n    }) => {\n      if (this._dragDropRegistry.isDragging(drag)) {\n        // We need to re-sort the item manually, because the pointer move\n        // events won't be dispatched while the user is scrolling.\n        drag._sortFromLastPointerPosition();\n      }\n    });\n  }\n  /** Refreshes the position cache of the items and sibling containers. */\n  _cacheItemPositions() {\n    const isHorizontal = this.orientation === 'horizontal';\n    this._itemPositions = this._activeDraggables.map(drag => {\n      const elementToMeasure = drag.getVisibleElement();\n      return {\n        drag,\n        offset: 0,\n        initialTransform: elementToMeasure.style.transform || '',\n        clientRect: getMutableClientRect(elementToMeasure)\n      };\n    }).sort((a, b) => {\n      return isHorizontal ? a.clientRect.left - b.clientRect.left : a.clientRect.top - b.clientRect.top;\n    });\n  }\n  /**\n   * Gets the offset in pixels by which the item that is being dragged should be moved.\n   * @param currentPosition Current position of the item.\n   * @param newPosition Position of the item where the current item should be moved.\n   * @param delta Direction in which the user is moving.\n   */\n  _getItemOffsetPx(currentPosition, newPosition, delta) {\n    const isHorizontal = this.orientation === 'horizontal';\n    let itemOffset = isHorizontal ? newPosition.left - currentPosition.left : newPosition.top - currentPosition.top;\n    // Account for differences in the item width/height.\n    if (delta === -1) {\n      itemOffset += isHorizontal ? newPosition.width - currentPosition.width : newPosition.height - currentPosition.height;\n    }\n    return itemOffset;\n  }\n  /**\n   * Gets the offset in pixels by which the items that aren't being dragged should be moved.\n   * @param currentIndex Index of the item currently being dragged.\n   * @param siblings All of the items in the list.\n   * @param delta Direction in which the user is moving.\n   */\n  _getSiblingOffsetPx(currentIndex, siblings, delta) {\n    const isHorizontal = this.orientation === 'horizontal';\n    const currentPosition = siblings[currentIndex].clientRect;\n    const immediateSibling = siblings[currentIndex + delta * -1];\n    let siblingOffset = currentPosition[isHorizontal ? 'width' : 'height'] * delta;\n    if (immediateSibling) {\n      const start = isHorizontal ? 'left' : 'top';\n      const end = isHorizontal ? 'right' : 'bottom';\n      // Get the spacing between the start of the current item and the end of the one immediately\n      // after it in the direction in which the user is dragging, or vice versa. We add it to the\n      // offset in order to push the element to where it will be when it's inline and is influenced\n      // by the `margin` of its siblings.\n      if (delta === -1) {\n        siblingOffset -= immediateSibling.clientRect[start] - currentPosition[end];\n      } else {\n        siblingOffset += currentPosition[start] - immediateSibling.clientRect[end];\n      }\n    }\n    return siblingOffset;\n  }\n  /**\n   * Checks if pointer is entering in the first position\n   * @param pointerX Position of the user's pointer along the X axis.\n   * @param pointerY Position of the user's pointer along the Y axis.\n   */\n  _shouldEnterAsFirstChild(pointerX, pointerY) {\n    if (!this._activeDraggables.length) {\n      return false;\n    }\n    const itemPositions = this._itemPositions;\n    const isHorizontal = this.orientation === 'horizontal';\n    // `itemPositions` are sorted by position while `activeDraggables` are sorted by child index\n    // check if container is using some sort of \"reverse\" ordering (eg: flex-direction: row-reverse)\n    const reversed = itemPositions[0].drag !== this._activeDraggables[0];\n    if (reversed) {\n      const lastItemRect = itemPositions[itemPositions.length - 1].clientRect;\n      return isHorizontal ? pointerX >= lastItemRect.right : pointerY >= lastItemRect.bottom;\n    } else {\n      const firstItemRect = itemPositions[0].clientRect;\n      return isHorizontal ? pointerX <= firstItemRect.left : pointerY <= firstItemRect.top;\n    }\n  }\n  /**\n   * Gets the index of an item in the drop container, based on the position of the user's pointer.\n   * @param item Item that is being sorted.\n   * @param pointerX Position of the user's pointer along the X axis.\n   * @param pointerY Position of the user's pointer along the Y axis.\n   * @param delta Direction in which the user is moving their pointer.\n   */\n  _getItemIndexFromPointerPosition(item, pointerX, pointerY, delta) {\n    const isHorizontal = this.orientation === 'horizontal';\n    const index = this._itemPositions.findIndex(({\n      drag,\n      clientRect\n    }) => {\n      // Skip the item itself.\n      if (drag === item) {\n        return false;\n      }\n      if (delta) {\n        const direction = isHorizontal ? delta.x : delta.y;\n        // If the user is still hovering over the same item as last time, their cursor hasn't left\n        // the item after we made the swap, and they didn't change the direction in which they're\n        // dragging, we don't consider it a direction swap.\n        if (drag === this._previousSwap.drag && this._previousSwap.overlaps && direction === this._previousSwap.delta) {\n          return false;\n        }\n      }\n      return isHorizontal ?\n      // Round these down since most browsers report client rects with\n      // sub-pixel precision, whereas the pointer coordinates are rounded to pixels.\n      pointerX >= Math.floor(clientRect.left) && pointerX < Math.floor(clientRect.right) : pointerY >= Math.floor(clientRect.top) && pointerY < Math.floor(clientRect.bottom);\n    });\n    return index === -1 || !this._sortPredicate(index, item) ? -1 : index;\n  }\n}\n\n/**\n * Proximity, as a ratio to width/height, at which a\n * dragged item will affect the drop container.\n */\nconst DROP_PROXIMITY_THRESHOLD = 0.05;\n/**\n * Proximity, as a ratio to width/height at which to start auto-scrolling the drop list or the\n * viewport. The value comes from trying it out manually until it feels right.\n */\nconst SCROLL_PROXIMITY_THRESHOLD = 0.05;\n/** Vertical direction in which we can auto-scroll. */\nvar AutoScrollVerticalDirection;\n(function (AutoScrollVerticalDirection) {\n  AutoScrollVerticalDirection[AutoScrollVerticalDirection[\"NONE\"] = 0] = \"NONE\";\n  AutoScrollVerticalDirection[AutoScrollVerticalDirection[\"UP\"] = 1] = \"UP\";\n  AutoScrollVerticalDirection[AutoScrollVerticalDirection[\"DOWN\"] = 2] = \"DOWN\";\n})(AutoScrollVerticalDirection || (AutoScrollVerticalDirection = {}));\n/** Horizontal direction in which we can auto-scroll. */\nvar AutoScrollHorizontalDirection;\n(function (AutoScrollHorizontalDirection) {\n  AutoScrollHorizontalDirection[AutoScrollHorizontalDirection[\"NONE\"] = 0] = \"NONE\";\n  AutoScrollHorizontalDirection[AutoScrollHorizontalDirection[\"LEFT\"] = 1] = \"LEFT\";\n  AutoScrollHorizontalDirection[AutoScrollHorizontalDirection[\"RIGHT\"] = 2] = \"RIGHT\";\n})(AutoScrollHorizontalDirection || (AutoScrollHorizontalDirection = {}));\n/**\n * Reference to a drop list. Used to manipulate or dispose of the container.\n */\nclass DropListRef {\n  constructor(element, _dragDropRegistry, _document, _ngZone, _viewportRuler) {\n    this._dragDropRegistry = _dragDropRegistry;\n    this._ngZone = _ngZone;\n    this._viewportRuler = _viewportRuler;\n    /** Whether starting a dragging sequence from this container is disabled. */\n    this.disabled = false;\n    /** Whether sorting items within the list is disabled. */\n    this.sortingDisabled = false;\n    /**\n     * Whether auto-scrolling the view when the user\n     * moves their pointer close to the edges is disabled.\n     */\n    this.autoScrollDisabled = false;\n    /** Number of pixels to scroll for each frame when auto-scrolling an element. */\n    this.autoScrollStep = 2;\n    /**\n     * Function that is used to determine whether an item\n     * is allowed to be moved into a drop container.\n     */\n    this.enterPredicate = () => true;\n    /** Function that is used to determine whether an item can be sorted into a particular index. */\n    this.sortPredicate = () => true;\n    /** Emits right before dragging has started. */\n    this.beforeStarted = new Subject();\n    /**\n     * Emits when the user has moved a new drag item into this container.\n     */\n    this.entered = new Subject();\n    /**\n     * Emits when the user removes an item from the container\n     * by dragging it into another container.\n     */\n    this.exited = new Subject();\n    /** Emits when the user drops an item inside the container. */\n    this.dropped = new Subject();\n    /** Emits as the user is swapping items while actively dragging. */\n    this.sorted = new Subject();\n    /** Emits when a dragging sequence is started in a list connected to the current one. */\n    this.receivingStarted = new Subject();\n    /** Emits when a dragging sequence is stopped from a list connected to the current one. */\n    this.receivingStopped = new Subject();\n    /** Whether an item in the list is being dragged. */\n    this._isDragging = false;\n    /** Draggable items in the container. */\n    this._draggables = [];\n    /** Drop lists that are connected to the current one. */\n    this._siblings = [];\n    /** Connected siblings that currently have a dragged item. */\n    this._activeSiblings = new Set();\n    /** Subscription to the window being scrolled. */\n    this._viewportScrollSubscription = Subscription.EMPTY;\n    /** Vertical direction in which the list is currently scrolling. */\n    this._verticalScrollDirection = AutoScrollVerticalDirection.NONE;\n    /** Horizontal direction in which the list is currently scrolling. */\n    this._horizontalScrollDirection = AutoScrollHorizontalDirection.NONE;\n    /** Used to signal to the current auto-scroll sequence when to stop. */\n    this._stopScrollTimers = new Subject();\n    /** Shadow root of the current element. Necessary for `elementFromPoint` to resolve correctly. */\n    this._cachedShadowRoot = null;\n    /** Starts the interval that'll auto-scroll the element. */\n    this._startScrollInterval = () => {\n      this._stopScrolling();\n      interval(0, animationFrameScheduler).pipe(takeUntil(this._stopScrollTimers)).subscribe(() => {\n        const node = this._scrollNode;\n        const scrollStep = this.autoScrollStep;\n        if (this._verticalScrollDirection === AutoScrollVerticalDirection.UP) {\n          node.scrollBy(0, -scrollStep);\n        } else if (this._verticalScrollDirection === AutoScrollVerticalDirection.DOWN) {\n          node.scrollBy(0, scrollStep);\n        }\n        if (this._horizontalScrollDirection === AutoScrollHorizontalDirection.LEFT) {\n          node.scrollBy(-scrollStep, 0);\n        } else if (this._horizontalScrollDirection === AutoScrollHorizontalDirection.RIGHT) {\n          node.scrollBy(scrollStep, 0);\n        }\n      });\n    };\n    this.element = coerceElement(element);\n    this._document = _document;\n    this.withScrollableParents([this.element]);\n    _dragDropRegistry.registerDropContainer(this);\n    this._parentPositions = new ParentPositionTracker(_document);\n    this._sortStrategy = new SingleAxisSortStrategy(this.element, _dragDropRegistry);\n    this._sortStrategy.withSortPredicate((index, item) => this.sortPredicate(index, item, this));\n  }\n  /** Removes the drop list functionality from the DOM element. */\n  dispose() {\n    this._stopScrolling();\n    this._stopScrollTimers.complete();\n    this._viewportScrollSubscription.unsubscribe();\n    this.beforeStarted.complete();\n    this.entered.complete();\n    this.exited.complete();\n    this.dropped.complete();\n    this.sorted.complete();\n    this.receivingStarted.complete();\n    this.receivingStopped.complete();\n    this._activeSiblings.clear();\n    this._scrollNode = null;\n    this._parentPositions.clear();\n    this._dragDropRegistry.removeDropContainer(this);\n  }\n  /** Whether an item from this list is currently being dragged. */\n  isDragging() {\n    return this._isDragging;\n  }\n  /** Starts dragging an item. */\n  start() {\n    this._draggingStarted();\n    this._notifyReceivingSiblings();\n  }\n  /**\n   * Attempts to move an item into the container.\n   * @param item Item that was moved into the container.\n   * @param pointerX Position of the item along the X axis.\n   * @param pointerY Position of the item along the Y axis.\n   * @param index Index at which the item entered. If omitted, the container will try to figure it\n   *   out automatically.\n   */\n  enter(item, pointerX, pointerY, index) {\n    this._draggingStarted();\n    // If sorting is disabled, we want the item to return to its starting\n    // position if the user is returning it to its initial container.\n    if (index == null && this.sortingDisabled) {\n      index = this._draggables.indexOf(item);\n    }\n    this._sortStrategy.enter(item, pointerX, pointerY, index);\n    // Note that this usually happens inside `_draggingStarted` as well, but the dimensions\n    // can change when the sort strategy moves the item around inside `enter`.\n    this._cacheParentPositions();\n    // Notify siblings at the end so that the item has been inserted into the `activeDraggables`.\n    this._notifyReceivingSiblings();\n    this.entered.next({\n      item,\n      container: this,\n      currentIndex: this.getItemIndex(item)\n    });\n  }\n  /**\n   * Removes an item from the container after it was dragged into another container by the user.\n   * @param item Item that was dragged out.\n   */\n  exit(item) {\n    this._reset();\n    this.exited.next({\n      item,\n      container: this\n    });\n  }\n  /**\n   * Drops an item into this container.\n   * @param item Item being dropped into the container.\n   * @param currentIndex Index at which the item should be inserted.\n   * @param previousIndex Index of the item when dragging started.\n   * @param previousContainer Container from which the item got dragged in.\n   * @param isPointerOverContainer Whether the user's pointer was over the\n   *    container when the item was dropped.\n   * @param distance Distance the user has dragged since the start of the dragging sequence.\n   * @param event Event that triggered the dropping sequence.\n   *\n   * @breaking-change 15.0.0 `previousIndex` and `event` parameters to become required.\n   */\n  drop(item, currentIndex, previousIndex, previousContainer, isPointerOverContainer, distance, dropPoint, event = {}) {\n    this._reset();\n    this.dropped.next({\n      item,\n      currentIndex,\n      previousIndex,\n      container: this,\n      previousContainer,\n      isPointerOverContainer,\n      distance,\n      dropPoint,\n      event\n    });\n  }\n  /**\n   * Sets the draggable items that are a part of this list.\n   * @param items Items that are a part of this list.\n   */\n  withItems(items) {\n    const previousItems = this._draggables;\n    this._draggables = items;\n    items.forEach(item => item._withDropContainer(this));\n    if (this.isDragging()) {\n      const draggedItems = previousItems.filter(item => item.isDragging());\n      // If all of the items being dragged were removed\n      // from the list, abort the current drag sequence.\n      if (draggedItems.every(item => items.indexOf(item) === -1)) {\n        this._reset();\n      } else {\n        this._sortStrategy.withItems(this._draggables);\n      }\n    }\n    return this;\n  }\n  /** Sets the layout direction of the drop list. */\n  withDirection(direction) {\n    this._sortStrategy.direction = direction;\n    return this;\n  }\n  /**\n   * Sets the containers that are connected to this one. When two or more containers are\n   * connected, the user will be allowed to transfer items between them.\n   * @param connectedTo Other containers that the current containers should be connected to.\n   */\n  connectedTo(connectedTo) {\n    this._siblings = connectedTo.slice();\n    return this;\n  }\n  /**\n   * Sets the orientation of the container.\n   * @param orientation New orientation for the container.\n   */\n  withOrientation(orientation) {\n    // TODO(crisbeto): eventually we should be constructing the new sort strategy here based on\n    // the new orientation. For now we can assume that it'll always be `SingleAxisSortStrategy`.\n    this._sortStrategy.orientation = orientation;\n    return this;\n  }\n  /**\n   * Sets which parent elements are can be scrolled while the user is dragging.\n   * @param elements Elements that can be scrolled.\n   */\n  withScrollableParents(elements) {\n    const element = coerceElement(this.element);\n    // We always allow the current element to be scrollable\n    // so we need to ensure that it's in the array.\n    this._scrollableElements = elements.indexOf(element) === -1 ? [element, ...elements] : elements.slice();\n    return this;\n  }\n  /** Gets the scrollable parents that are registered with this drop container. */\n  getScrollableParents() {\n    return this._scrollableElements;\n  }\n  /**\n   * Figures out the index of an item in the container.\n   * @param item Item whose index should be determined.\n   */\n  getItemIndex(item) {\n    return this._isDragging ? this._sortStrategy.getItemIndex(item) : this._draggables.indexOf(item);\n  }\n  /**\n   * Whether the list is able to receive the item that\n   * is currently being dragged inside a connected drop list.\n   */\n  isReceiving() {\n    return this._activeSiblings.size > 0;\n  }\n  /**\n   * Sorts an item inside the container based on its position.\n   * @param item Item to be sorted.\n   * @param pointerX Position of the item along the X axis.\n   * @param pointerY Position of the item along the Y axis.\n   * @param pointerDelta Direction in which the pointer is moving along each axis.\n   */\n  _sortItem(item, pointerX, pointerY, pointerDelta) {\n    // Don't sort the item if sorting is disabled or it's out of range.\n    if (this.sortingDisabled || !this._domRect || !isPointerNearDomRect(this._domRect, DROP_PROXIMITY_THRESHOLD, pointerX, pointerY)) {\n      return;\n    }\n    const result = this._sortStrategy.sort(item, pointerX, pointerY, pointerDelta);\n    if (result) {\n      this.sorted.next({\n        previousIndex: result.previousIndex,\n        currentIndex: result.currentIndex,\n        container: this,\n        item\n      });\n    }\n  }\n  /**\n   * Checks whether the user's pointer is close to the edges of either the\n   * viewport or the drop list and starts the auto-scroll sequence.\n   * @param pointerX User's pointer position along the x axis.\n   * @param pointerY User's pointer position along the y axis.\n   */\n  _startScrollingIfNecessary(pointerX, pointerY) {\n    if (this.autoScrollDisabled) {\n      return;\n    }\n    let scrollNode;\n    let verticalScrollDirection = AutoScrollVerticalDirection.NONE;\n    let horizontalScrollDirection = AutoScrollHorizontalDirection.NONE;\n    // Check whether we should start scrolling any of the parent containers.\n    this._parentPositions.positions.forEach((position, element) => {\n      // We have special handling for the `document` below. Also this would be\n      // nicer with a  for...of loop, but it requires changing a compiler flag.\n      if (element === this._document || !position.clientRect || scrollNode) {\n        return;\n      }\n      if (isPointerNearDomRect(position.clientRect, DROP_PROXIMITY_THRESHOLD, pointerX, pointerY)) {\n        [verticalScrollDirection, horizontalScrollDirection] = getElementScrollDirections(element, position.clientRect, this._sortStrategy.direction, pointerX, pointerY);\n        if (verticalScrollDirection || horizontalScrollDirection) {\n          scrollNode = element;\n        }\n      }\n    });\n    // Otherwise check if we can start scrolling the viewport.\n    if (!verticalScrollDirection && !horizontalScrollDirection) {\n      const {\n        width,\n        height\n      } = this._viewportRuler.getViewportSize();\n      const domRect = {\n        width,\n        height,\n        top: 0,\n        right: width,\n        bottom: height,\n        left: 0\n      };\n      verticalScrollDirection = getVerticalScrollDirection(domRect, pointerY);\n      horizontalScrollDirection = getHorizontalScrollDirection(domRect, pointerX);\n      scrollNode = window;\n    }\n    if (scrollNode && (verticalScrollDirection !== this._verticalScrollDirection || horizontalScrollDirection !== this._horizontalScrollDirection || scrollNode !== this._scrollNode)) {\n      this._verticalScrollDirection = verticalScrollDirection;\n      this._horizontalScrollDirection = horizontalScrollDirection;\n      this._scrollNode = scrollNode;\n      if ((verticalScrollDirection || horizontalScrollDirection) && scrollNode) {\n        this._ngZone.runOutsideAngular(this._startScrollInterval);\n      } else {\n        this._stopScrolling();\n      }\n    }\n  }\n  /** Stops any currently-running auto-scroll sequences. */\n  _stopScrolling() {\n    this._stopScrollTimers.next();\n  }\n  /** Starts the dragging sequence within the list. */\n  _draggingStarted() {\n    const styles = coerceElement(this.element).style;\n    this.beforeStarted.next();\n    this._isDragging = true;\n    // We need to disable scroll snapping while the user is dragging, because it breaks automatic\n    // scrolling. The browser seems to round the value based on the snapping points which means\n    // that we can't increment/decrement the scroll position.\n    this._initialScrollSnap = styles.msScrollSnapType || styles.scrollSnapType || '';\n    styles.scrollSnapType = styles.msScrollSnapType = 'none';\n    this._sortStrategy.start(this._draggables);\n    this._cacheParentPositions();\n    this._viewportScrollSubscription.unsubscribe();\n    this._listenToScrollEvents();\n  }\n  /** Caches the positions of the configured scrollable parents. */\n  _cacheParentPositions() {\n    const element = coerceElement(this.element);\n    this._parentPositions.cache(this._scrollableElements);\n    // The list element is always in the `scrollableElements`\n    // so we can take advantage of the cached `DOMRect`.\n    this._domRect = this._parentPositions.positions.get(element).clientRect;\n  }\n  /** Resets the container to its initial state. */\n  _reset() {\n    this._isDragging = false;\n    const styles = coerceElement(this.element).style;\n    styles.scrollSnapType = styles.msScrollSnapType = this._initialScrollSnap;\n    this._siblings.forEach(sibling => sibling._stopReceiving(this));\n    this._sortStrategy.reset();\n    this._stopScrolling();\n    this._viewportScrollSubscription.unsubscribe();\n    this._parentPositions.clear();\n  }\n  /**\n   * Checks whether the user's pointer is positioned over the container.\n   * @param x Pointer position along the X axis.\n   * @param y Pointer position along the Y axis.\n   */\n  _isOverContainer(x, y) {\n    return this._domRect != null && isInsideClientRect(this._domRect, x, y);\n  }\n  /**\n   * Figures out whether an item should be moved into a sibling\n   * drop container, based on its current position.\n   * @param item Drag item that is being moved.\n   * @param x Position of the item along the X axis.\n   * @param y Position of the item along the Y axis.\n   */\n  _getSiblingContainerFromPosition(item, x, y) {\n    return this._siblings.find(sibling => sibling._canReceive(item, x, y));\n  }\n  /**\n   * Checks whether the drop list can receive the passed-in item.\n   * @param item Item that is being dragged into the list.\n   * @param x Position of the item along the X axis.\n   * @param y Position of the item along the Y axis.\n   */\n  _canReceive(item, x, y) {\n    if (!this._domRect || !isInsideClientRect(this._domRect, x, y) || !this.enterPredicate(item, this)) {\n      return false;\n    }\n    const elementFromPoint = this._getShadowRoot().elementFromPoint(x, y);\n    // If there's no element at the pointer position, then\n    // the client rect is probably scrolled out of the view.\n    if (!elementFromPoint) {\n      return false;\n    }\n    const nativeElement = coerceElement(this.element);\n    // The `DOMRect`, that we're using to find the container over which the user is\n    // hovering, doesn't give us any information on whether the element has been scrolled\n    // out of the view or whether it's overlapping with other containers. This means that\n    // we could end up transferring the item into a container that's invisible or is positioned\n    // below another one. We use the result from `elementFromPoint` to get the top-most element\n    // at the pointer position and to find whether it's one of the intersecting drop containers.\n    return elementFromPoint === nativeElement || nativeElement.contains(elementFromPoint);\n  }\n  /**\n   * Called by one of the connected drop lists when a dragging sequence has started.\n   * @param sibling Sibling in which dragging has started.\n   */\n  _startReceiving(sibling, items) {\n    const activeSiblings = this._activeSiblings;\n    if (!activeSiblings.has(sibling) && items.every(item => {\n      // Note that we have to add an exception to the `enterPredicate` for items that started off\n      // in this drop list. The drag ref has logic that allows an item to return to its initial\n      // container, if it has left the initial container and none of the connected containers\n      // allow it to enter. See `DragRef._updateActiveDropContainer` for more context.\n      return this.enterPredicate(item, this) || this._draggables.indexOf(item) > -1;\n    })) {\n      activeSiblings.add(sibling);\n      this._cacheParentPositions();\n      this._listenToScrollEvents();\n      this.receivingStarted.next({\n        initiator: sibling,\n        receiver: this,\n        items\n      });\n    }\n  }\n  /**\n   * Called by a connected drop list when dragging has stopped.\n   * @param sibling Sibling whose dragging has stopped.\n   */\n  _stopReceiving(sibling) {\n    this._activeSiblings.delete(sibling);\n    this._viewportScrollSubscription.unsubscribe();\n    this.receivingStopped.next({\n      initiator: sibling,\n      receiver: this\n    });\n  }\n  /**\n   * Starts listening to scroll events on the viewport.\n   * Used for updating the internal state of the list.\n   */\n  _listenToScrollEvents() {\n    this._viewportScrollSubscription = this._dragDropRegistry.scrolled(this._getShadowRoot()).subscribe(event => {\n      if (this.isDragging()) {\n        const scrollDifference = this._parentPositions.handleScroll(event);\n        if (scrollDifference) {\n          this._sortStrategy.updateOnScroll(scrollDifference.top, scrollDifference.left);\n        }\n      } else if (this.isReceiving()) {\n        this._cacheParentPositions();\n      }\n    });\n  }\n  /**\n   * Lazily resolves and returns the shadow root of the element. We do this in a function, rather\n   * than saving it in property directly on init, because we want to resolve it as late as possible\n   * in order to ensure that the element has been moved into the shadow DOM. Doing it inside the\n   * constructor might be too early if the element is inside of something like `ngFor` or `ngIf`.\n   */\n  _getShadowRoot() {\n    if (!this._cachedShadowRoot) {\n      const shadowRoot = _getShadowRoot(coerceElement(this.element));\n      this._cachedShadowRoot = shadowRoot || this._document;\n    }\n    return this._cachedShadowRoot;\n  }\n  /** Notifies any siblings that may potentially receive the item. */\n  _notifyReceivingSiblings() {\n    const draggedItems = this._sortStrategy.getActiveItemsSnapshot().filter(item => item.isDragging());\n    this._siblings.forEach(sibling => sibling._startReceiving(this, draggedItems));\n  }\n}\n/**\n * Gets whether the vertical auto-scroll direction of a node.\n * @param clientRect Dimensions of the node.\n * @param pointerY Position of the user's pointer along the y axis.\n */\nfunction getVerticalScrollDirection(clientRect, pointerY) {\n  const {\n    top,\n    bottom,\n    height\n  } = clientRect;\n  const yThreshold = height * SCROLL_PROXIMITY_THRESHOLD;\n  if (pointerY >= top - yThreshold && pointerY <= top + yThreshold) {\n    return AutoScrollVerticalDirection.UP;\n  } else if (pointerY >= bottom - yThreshold && pointerY <= bottom + yThreshold) {\n    return AutoScrollVerticalDirection.DOWN;\n  }\n  return AutoScrollVerticalDirection.NONE;\n}\n/**\n * Gets whether the horizontal auto-scroll direction of a node.\n * @param clientRect Dimensions of the node.\n * @param pointerX Position of the user's pointer along the x axis.\n */\nfunction getHorizontalScrollDirection(clientRect, pointerX) {\n  const {\n    left,\n    right,\n    width\n  } = clientRect;\n  const xThreshold = width * SCROLL_PROXIMITY_THRESHOLD;\n  if (pointerX >= left - xThreshold && pointerX <= left + xThreshold) {\n    return AutoScrollHorizontalDirection.LEFT;\n  } else if (pointerX >= right - xThreshold && pointerX <= right + xThreshold) {\n    return AutoScrollHorizontalDirection.RIGHT;\n  }\n  return AutoScrollHorizontalDirection.NONE;\n}\n/**\n * Gets the directions in which an element node should be scrolled,\n * assuming that the user's pointer is already within it scrollable region.\n * @param element Element for which we should calculate the scroll direction.\n * @param clientRect Bounding client rectangle of the element.\n * @param direction Layout direction of the drop list.\n * @param pointerX Position of the user's pointer along the x axis.\n * @param pointerY Position of the user's pointer along the y axis.\n */\nfunction getElementScrollDirections(element, clientRect, direction, pointerX, pointerY) {\n  const computedVertical = getVerticalScrollDirection(clientRect, pointerY);\n  const computedHorizontal = getHorizontalScrollDirection(clientRect, pointerX);\n  let verticalScrollDirection = AutoScrollVerticalDirection.NONE;\n  let horizontalScrollDirection = AutoScrollHorizontalDirection.NONE;\n  // Note that we here we do some extra checks for whether the element is actually scrollable in\n  // a certain direction and we only assign the scroll direction if it is. We do this so that we\n  // can allow other elements to be scrolled, if the current element can't be scrolled anymore.\n  // This allows us to handle cases where the scroll regions of two scrollable elements overlap.\n  if (computedVertical) {\n    const scrollTop = element.scrollTop;\n    if (computedVertical === AutoScrollVerticalDirection.UP) {\n      if (scrollTop > 0) {\n        verticalScrollDirection = AutoScrollVerticalDirection.UP;\n      }\n    } else if (element.scrollHeight - scrollTop > element.clientHeight) {\n      verticalScrollDirection = AutoScrollVerticalDirection.DOWN;\n    }\n  }\n  if (computedHorizontal) {\n    const scrollLeft = element.scrollLeft;\n    if (direction === 'rtl') {\n      if (computedHorizontal === AutoScrollHorizontalDirection.RIGHT) {\n        // In RTL `scrollLeft` will be negative when scrolled.\n        if (scrollLeft < 0) {\n          horizontalScrollDirection = AutoScrollHorizontalDirection.RIGHT;\n        }\n      } else if (element.scrollWidth + scrollLeft > element.clientWidth) {\n        horizontalScrollDirection = AutoScrollHorizontalDirection.LEFT;\n      }\n    } else {\n      if (computedHorizontal === AutoScrollHorizontalDirection.LEFT) {\n        if (scrollLeft > 0) {\n          horizontalScrollDirection = AutoScrollHorizontalDirection.LEFT;\n        }\n      } else if (element.scrollWidth - scrollLeft > element.clientWidth) {\n        horizontalScrollDirection = AutoScrollHorizontalDirection.RIGHT;\n      }\n    }\n  }\n  return [verticalScrollDirection, horizontalScrollDirection];\n}\n\n/** Event options that can be used to bind an active, capturing event. */\nconst activeCapturingEventOptions = normalizePassiveListenerOptions({\n  passive: false,\n  capture: true\n});\n/** Keeps track of the apps currently containing drag items. */\nconst activeApps = new Set();\n/**\n * Component used to load the drag&drop reset styles.\n * @docs-private\n */\nclass _ResetsLoader {\n  static {\n    this.ɵfac = function _ResetsLoader_Factory(t) {\n      return new (t || _ResetsLoader)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: _ResetsLoader,\n      selectors: [[\"ng-component\"]],\n      hostAttrs: [\"cdk-drag-resets-container\", \"\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 0,\n      vars: 0,\n      template: function _ResetsLoader_Template(rf, ctx) {},\n      styles: [\"@layer cdk-resets{.cdk-drag-preview{background:none;border:none;padding:0;color:inherit}}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_ResetsLoader, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      encapsulation: ViewEncapsulation.None,\n      template: '',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        'cdk-drag-resets-container': ''\n      },\n      styles: [\"@layer cdk-resets{.cdk-drag-preview{background:none;border:none;padding:0;color:inherit}}\"]\n    }]\n  }], null, null);\n})();\n/**\n * Service that keeps track of all the drag item and drop container\n * instances, and manages global event listeners on the `document`.\n * @docs-private\n */\n// Note: this class is generic, rather than referencing CdkDrag and CdkDropList directly, in order\n// to avoid circular imports. If we were to reference them here, importing the registry into the\n// classes that are registering themselves will introduce a circular import.\nclass DragDropRegistry {\n  constructor(_ngZone, _document) {\n    this._ngZone = _ngZone;\n    this._appRef = inject(ApplicationRef);\n    this._environmentInjector = inject(EnvironmentInjector);\n    /** Registered drop container instances. */\n    this._dropInstances = new Set();\n    /** Registered drag item instances. */\n    this._dragInstances = new Set();\n    /** Drag item instances that are currently being dragged. */\n    this._activeDragInstances = [];\n    /** Keeps track of the event listeners that we've bound to the `document`. */\n    this._globalListeners = new Map();\n    /**\n     * Predicate function to check if an item is being dragged.  Moved out into a property,\n     * because it'll be called a lot and we don't want to create a new function every time.\n     */\n    this._draggingPredicate = item => item.isDragging();\n    /**\n     * Emits the `touchmove` or `mousemove` events that are dispatched\n     * while the user is dragging a drag item instance.\n     */\n    this.pointerMove = new Subject();\n    /**\n     * Emits the `touchend` or `mouseup` events that are dispatched\n     * while the user is dragging a drag item instance.\n     */\n    this.pointerUp = new Subject();\n    /**\n     * Emits when the viewport has been scrolled while the user is dragging an item.\n     * @deprecated To be turned into a private member. Use the `scrolled` method instead.\n     * @breaking-change 13.0.0\n     */\n    this.scroll = new Subject();\n    /**\n     * Event listener that will prevent the default browser action while the user is dragging.\n     * @param event Event whose default action should be prevented.\n     */\n    this._preventDefaultWhileDragging = event => {\n      if (this._activeDragInstances.length > 0) {\n        event.preventDefault();\n      }\n    };\n    /** Event listener for `touchmove` that is bound even if no dragging is happening. */\n    this._persistentTouchmoveListener = event => {\n      if (this._activeDragInstances.length > 0) {\n        // Note that we only want to prevent the default action after dragging has actually started.\n        // Usually this is the same time at which the item is added to the `_activeDragInstances`,\n        // but it could be pushed back if the user has set up a drag delay or threshold.\n        if (this._activeDragInstances.some(this._draggingPredicate)) {\n          event.preventDefault();\n        }\n        this.pointerMove.next(event);\n      }\n    };\n    this._document = _document;\n  }\n  /** Adds a drop container to the registry. */\n  registerDropContainer(drop) {\n    if (!this._dropInstances.has(drop)) {\n      this._dropInstances.add(drop);\n    }\n  }\n  /** Adds a drag item instance to the registry. */\n  registerDragItem(drag) {\n    this._dragInstances.add(drag);\n    // The `touchmove` event gets bound once, ahead of time, because WebKit\n    // won't preventDefault on a dynamically-added `touchmove` listener.\n    // See https://bugs.webkit.org/show_bug.cgi?id=184250.\n    if (this._dragInstances.size === 1) {\n      this._ngZone.runOutsideAngular(() => {\n        // The event handler has to be explicitly active,\n        // because newer browsers make it passive by default.\n        this._document.addEventListener('touchmove', this._persistentTouchmoveListener, activeCapturingEventOptions);\n      });\n    }\n  }\n  /** Removes a drop container from the registry. */\n  removeDropContainer(drop) {\n    this._dropInstances.delete(drop);\n  }\n  /** Removes a drag item instance from the registry. */\n  removeDragItem(drag) {\n    this._dragInstances.delete(drag);\n    this.stopDragging(drag);\n    if (this._dragInstances.size === 0) {\n      this._document.removeEventListener('touchmove', this._persistentTouchmoveListener, activeCapturingEventOptions);\n    }\n  }\n  /**\n   * Starts the dragging sequence for a drag instance.\n   * @param drag Drag instance which is being dragged.\n   * @param event Event that initiated the dragging.\n   */\n  startDragging(drag, event) {\n    // Do not process the same drag twice to avoid memory leaks and redundant listeners\n    if (this._activeDragInstances.indexOf(drag) > -1) {\n      return;\n    }\n    this._loadResets();\n    this._activeDragInstances.push(drag);\n    if (this._activeDragInstances.length === 1) {\n      const isTouchEvent = event.type.startsWith('touch');\n      // We explicitly bind __active__ listeners here, because newer browsers will default to\n      // passive ones for `mousemove` and `touchmove`. The events need to be active, because we\n      // use `preventDefault` to prevent the page from scrolling while the user is dragging.\n      this._globalListeners.set(isTouchEvent ? 'touchend' : 'mouseup', {\n        handler: e => this.pointerUp.next(e),\n        options: true\n      }).set('scroll', {\n        handler: e => this.scroll.next(e),\n        // Use capturing so that we pick up scroll changes in any scrollable nodes that aren't\n        // the document. See https://github.com/angular/components/issues/17144.\n        options: true\n      })\n      // Preventing the default action on `mousemove` isn't enough to disable text selection\n      // on Safari so we need to prevent the selection event as well. Alternatively this can\n      // be done by setting `user-select: none` on the `body`, however it has causes a style\n      // recalculation which can be expensive on pages with a lot of elements.\n      .set('selectstart', {\n        handler: this._preventDefaultWhileDragging,\n        options: activeCapturingEventOptions\n      });\n      // We don't have to bind a move event for touch drag sequences, because\n      // we already have a persistent global one bound from `registerDragItem`.\n      if (!isTouchEvent) {\n        this._globalListeners.set('mousemove', {\n          handler: e => this.pointerMove.next(e),\n          options: activeCapturingEventOptions\n        });\n      }\n      this._ngZone.runOutsideAngular(() => {\n        this._globalListeners.forEach((config, name) => {\n          this._document.addEventListener(name, config.handler, config.options);\n        });\n      });\n    }\n  }\n  /** Stops dragging a drag item instance. */\n  stopDragging(drag) {\n    const index = this._activeDragInstances.indexOf(drag);\n    if (index > -1) {\n      this._activeDragInstances.splice(index, 1);\n      if (this._activeDragInstances.length === 0) {\n        this._clearGlobalListeners();\n      }\n    }\n  }\n  /** Gets whether a drag item instance is currently being dragged. */\n  isDragging(drag) {\n    return this._activeDragInstances.indexOf(drag) > -1;\n  }\n  /**\n   * Gets a stream that will emit when any element on the page is scrolled while an item is being\n   * dragged.\n   * @param shadowRoot Optional shadow root that the current dragging sequence started from.\n   *   Top-level listeners won't pick up events coming from the shadow DOM so this parameter can\n   *   be used to include an additional top-level listener at the shadow root level.\n   */\n  scrolled(shadowRoot) {\n    const streams = [this.scroll];\n    if (shadowRoot && shadowRoot !== this._document) {\n      // Note that this is basically the same as `fromEvent` from rxjs, but we do it ourselves,\n      // because we want to guarantee that the event is bound outside of the `NgZone`. With\n      // `fromEvent` it'll only happen if the subscription is outside the `NgZone`.\n      streams.push(new Observable(observer => {\n        return this._ngZone.runOutsideAngular(() => {\n          const eventOptions = true;\n          const callback = event => {\n            if (this._activeDragInstances.length) {\n              observer.next(event);\n            }\n          };\n          shadowRoot.addEventListener('scroll', callback, eventOptions);\n          return () => {\n            shadowRoot.removeEventListener('scroll', callback, eventOptions);\n          };\n        });\n      }));\n    }\n    return merge(...streams);\n  }\n  ngOnDestroy() {\n    this._dragInstances.forEach(instance => this.removeDragItem(instance));\n    this._dropInstances.forEach(instance => this.removeDropContainer(instance));\n    this._clearGlobalListeners();\n    this.pointerMove.complete();\n    this.pointerUp.complete();\n  }\n  /** Clears out the global event listeners from the `document`. */\n  _clearGlobalListeners() {\n    this._globalListeners.forEach((config, name) => {\n      this._document.removeEventListener(name, config.handler, config.options);\n    });\n    this._globalListeners.clear();\n  }\n  // TODO(crisbeto): abstract this away into something reusable.\n  /** Loads the CSS resets needed for the module to work correctly. */\n  _loadResets() {\n    if (!activeApps.has(this._appRef)) {\n      activeApps.add(this._appRef);\n      const componentRef = createComponent(_ResetsLoader, {\n        environmentInjector: this._environmentInjector\n      });\n      this._appRef.onDestroy(() => {\n        activeApps.delete(this._appRef);\n        if (activeApps.size === 0) {\n          componentRef.destroy();\n        }\n      });\n    }\n  }\n  static {\n    this.ɵfac = function DragDropRegistry_Factory(t) {\n      return new (t || DragDropRegistry)(i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: DragDropRegistry,\n      factory: DragDropRegistry.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DragDropRegistry, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n\n/** Default configuration to be used when creating a `DragRef`. */\nconst DEFAULT_CONFIG = {\n  dragStartThreshold: 5,\n  pointerDirectionChangeThreshold: 5\n};\n/**\n * Service that allows for drag-and-drop functionality to be attached to DOM elements.\n */\nclass DragDrop {\n  constructor(_document, _ngZone, _viewportRuler, _dragDropRegistry) {\n    this._document = _document;\n    this._ngZone = _ngZone;\n    this._viewportRuler = _viewportRuler;\n    this._dragDropRegistry = _dragDropRegistry;\n  }\n  /**\n   * Turns an element into a draggable item.\n   * @param element Element to which to attach the dragging functionality.\n   * @param config Object used to configure the dragging behavior.\n   */\n  createDrag(element, config = DEFAULT_CONFIG) {\n    return new DragRef(element, config, this._document, this._ngZone, this._viewportRuler, this._dragDropRegistry);\n  }\n  /**\n   * Turns an element into a drop list.\n   * @param element Element to which to attach the drop list functionality.\n   */\n  createDropList(element) {\n    return new DropListRef(element, this._dragDropRegistry, this._document, this._ngZone, this._viewportRuler);\n  }\n  static {\n    this.ɵfac = function DragDrop_Factory(t) {\n      return new (t || DragDrop)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(i1.ViewportRuler), i0.ɵɵinject(DragDropRegistry));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: DragDrop,\n      factory: DragDrop.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DragDrop, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: i0.NgZone\n  }, {\n    type: i1.ViewportRuler\n  }, {\n    type: DragDropRegistry\n  }], null);\n})();\n\n/**\n * Injection token that can be used for a `CdkDrag` to provide itself as a parent to the\n * drag-specific child directive (`CdkDragHandle`, `CdkDragPreview` etc.). Used primarily\n * to avoid circular imports.\n * @docs-private\n */\nconst CDK_DRAG_PARENT = new InjectionToken('CDK_DRAG_PARENT');\n\n/**\n * Asserts that a particular node is an element.\n * @param node Node to be checked.\n * @param name Name to attach to the error message.\n */\nfunction assertElementNode(node, name) {\n  if (node.nodeType !== 1) {\n    throw Error(`${name} must be attached to an element node. ` + `Currently attached to \"${node.nodeName}\".`);\n  }\n}\n\n/**\n * Injection token that can be used to reference instances of `CdkDragHandle`. It serves as\n * alternative token to the actual `CdkDragHandle` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DRAG_HANDLE = new InjectionToken('CdkDragHandle');\n/** Handle that can be used to drag a CdkDrag instance. */\nclass CdkDragHandle {\n  /** Whether starting to drag through this handle is disabled. */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = value;\n    this._stateChanges.next(this);\n  }\n  constructor(element, _parentDrag) {\n    this.element = element;\n    this._parentDrag = _parentDrag;\n    /** Emits when the state of the handle has changed. */\n    this._stateChanges = new Subject();\n    this._disabled = false;\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      assertElementNode(element.nativeElement, 'cdkDragHandle');\n    }\n    _parentDrag?._addHandle(this);\n  }\n  ngOnDestroy() {\n    this._parentDrag?._removeHandle(this);\n    this._stateChanges.complete();\n  }\n  static {\n    this.ɵfac = function CdkDragHandle_Factory(t) {\n      return new (t || CdkDragHandle)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(CDK_DRAG_PARENT, 12));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkDragHandle,\n      selectors: [[\"\", \"cdkDragHandle\", \"\"]],\n      hostAttrs: [1, \"cdk-drag-handle\"],\n      inputs: {\n        disabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"cdkDragHandleDisabled\", \"disabled\", booleanAttribute]\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CDK_DRAG_HANDLE,\n        useExisting: CdkDragHandle\n      }]), i0.ɵɵInputTransformsFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkDragHandle, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkDragHandle]',\n      standalone: true,\n      host: {\n        'class': 'cdk-drag-handle'\n      },\n      providers: [{\n        provide: CDK_DRAG_HANDLE,\n        useExisting: CdkDragHandle\n      }]\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [CDK_DRAG_PARENT]\n    }, {\n      type: Optional\n    }, {\n      type: SkipSelf\n    }]\n  }], {\n    disabled: [{\n      type: Input,\n      args: [{\n        alias: 'cdkDragHandleDisabled',\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/**\n * Injection token that can be used to configure the\n * behavior of the drag&drop-related components.\n */\nconst CDK_DRAG_CONFIG = new InjectionToken('CDK_DRAG_CONFIG');\nconst DRAG_HOST_CLASS = 'cdk-drag';\n/**\n * Injection token that can be used to reference instances of `CdkDropList`. It serves as\n * alternative token to the actual `CdkDropList` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DROP_LIST = new InjectionToken('CdkDropList');\n/** Element that can be moved inside a CdkDropList container. */\nclass CdkDrag {\n  static {\n    this._dragInstances = [];\n  }\n  /** Whether starting to drag this element is disabled. */\n  get disabled() {\n    return this._disabled || this.dropContainer && this.dropContainer.disabled;\n  }\n  set disabled(value) {\n    this._disabled = value;\n    this._dragRef.disabled = this._disabled;\n  }\n  constructor( /** Element that the draggable is attached to. */\n  element, /** Droppable container that the draggable is a part of. */\n  dropContainer,\n  /**\n   * @deprecated `_document` parameter no longer being used and will be removed.\n   * @breaking-change 12.0.0\n   */\n  _document, _ngZone, _viewContainerRef, config, _dir, dragDrop, _changeDetectorRef, _selfHandle, _parentDrag) {\n    this.element = element;\n    this.dropContainer = dropContainer;\n    this._ngZone = _ngZone;\n    this._viewContainerRef = _viewContainerRef;\n    this._dir = _dir;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._selfHandle = _selfHandle;\n    this._parentDrag = _parentDrag;\n    this._destroyed = new Subject();\n    this._handles = new BehaviorSubject([]);\n    /** Emits when the user starts dragging the item. */\n    this.started = new EventEmitter();\n    /** Emits when the user has released a drag item, before any animations have started. */\n    this.released = new EventEmitter();\n    /** Emits when the user stops dragging an item in the container. */\n    this.ended = new EventEmitter();\n    /** Emits when the user has moved the item into a new container. */\n    this.entered = new EventEmitter();\n    /** Emits when the user removes the item its container by dragging it into another container. */\n    this.exited = new EventEmitter();\n    /** Emits when the user drops the item inside a container. */\n    this.dropped = new EventEmitter();\n    /**\n     * Emits as the user is dragging the item. Use with caution,\n     * because this event will fire for every pixel that the user has dragged.\n     */\n    this.moved = new Observable(observer => {\n      const subscription = this._dragRef.moved.pipe(map(movedEvent => ({\n        source: this,\n        pointerPosition: movedEvent.pointerPosition,\n        event: movedEvent.event,\n        delta: movedEvent.delta,\n        distance: movedEvent.distance\n      }))).subscribe(observer);\n      return () => {\n        subscription.unsubscribe();\n      };\n    });\n    this._dragRef = dragDrop.createDrag(element, {\n      dragStartThreshold: config && config.dragStartThreshold != null ? config.dragStartThreshold : 5,\n      pointerDirectionChangeThreshold: config && config.pointerDirectionChangeThreshold != null ? config.pointerDirectionChangeThreshold : 5,\n      zIndex: config?.zIndex\n    });\n    this._dragRef.data = this;\n    // We have to keep track of the drag instances in order to be able to match an element to\n    // a drag instance. We can't go through the global registry of `DragRef`, because the root\n    // element could be different.\n    CdkDrag._dragInstances.push(this);\n    if (config) {\n      this._assignDefaults(config);\n    }\n    // Note that usually the container is assigned when the drop list is picks up the item, but in\n    // some cases (mainly transplanted views with OnPush, see #18341) we may end up in a situation\n    // where there are no items on the first change detection pass, but the items get picked up as\n    // soon as the user triggers another pass by dragging. This is a problem, because the item would\n    // have to switch from standalone mode to drag mode in the middle of the dragging sequence which\n    // is too late since the two modes save different kinds of information. We work around it by\n    // assigning the drop container both from here and the list.\n    if (dropContainer) {\n      this._dragRef._withDropContainer(dropContainer._dropListRef);\n      dropContainer.addItem(this);\n    }\n    this._syncInputs(this._dragRef);\n    this._handleEvents(this._dragRef);\n  }\n  /**\n   * Returns the element that is being used as a placeholder\n   * while the current element is being dragged.\n   */\n  getPlaceholderElement() {\n    return this._dragRef.getPlaceholderElement();\n  }\n  /** Returns the root draggable element. */\n  getRootElement() {\n    return this._dragRef.getRootElement();\n  }\n  /** Resets a standalone drag item to its initial position. */\n  reset() {\n    this._dragRef.reset();\n  }\n  /**\n   * Gets the pixel coordinates of the draggable outside of a drop container.\n   */\n  getFreeDragPosition() {\n    return this._dragRef.getFreeDragPosition();\n  }\n  /**\n   * Sets the current position in pixels the draggable outside of a drop container.\n   * @param value New position to be set.\n   */\n  setFreeDragPosition(value) {\n    this._dragRef.setFreeDragPosition(value);\n  }\n  ngAfterViewInit() {\n    // Normally this isn't in the zone, but it can cause major performance regressions for apps\n    // using `zone-patch-rxjs` because it'll trigger a change detection when it unsubscribes.\n    this._ngZone.runOutsideAngular(() => {\n      // We need to wait for the zone to stabilize, in order for the reference\n      // element to be in the proper place in the DOM. This is mostly relevant\n      // for draggable elements inside portals since they get stamped out in\n      // their original DOM position and then they get transferred to the portal.\n      this._ngZone.onStable.pipe(take(1), takeUntil(this._destroyed)).subscribe(() => {\n        this._updateRootElement();\n        this._setupHandlesListener();\n        if (this.freeDragPosition) {\n          this._dragRef.setFreeDragPosition(this.freeDragPosition);\n        }\n      });\n    });\n  }\n  ngOnChanges(changes) {\n    const rootSelectorChange = changes['rootElementSelector'];\n    const positionChange = changes['freeDragPosition'];\n    // We don't have to react to the first change since it's being\n    // handled in `ngAfterViewInit` where it needs to be deferred.\n    if (rootSelectorChange && !rootSelectorChange.firstChange) {\n      this._updateRootElement();\n    }\n    // Skip the first change since it's being handled in `ngAfterViewInit`.\n    if (positionChange && !positionChange.firstChange && this.freeDragPosition) {\n      this._dragRef.setFreeDragPosition(this.freeDragPosition);\n    }\n  }\n  ngOnDestroy() {\n    if (this.dropContainer) {\n      this.dropContainer.removeItem(this);\n    }\n    const index = CdkDrag._dragInstances.indexOf(this);\n    if (index > -1) {\n      CdkDrag._dragInstances.splice(index, 1);\n    }\n    // Unnecessary in most cases, but used to avoid extra change detections with `zone-paths-rxjs`.\n    this._ngZone.runOutsideAngular(() => {\n      this._handles.complete();\n      this._destroyed.next();\n      this._destroyed.complete();\n      this._dragRef.dispose();\n    });\n  }\n  _addHandle(handle) {\n    const handles = this._handles.getValue();\n    handles.push(handle);\n    this._handles.next(handles);\n  }\n  _removeHandle(handle) {\n    const handles = this._handles.getValue();\n    const index = handles.indexOf(handle);\n    if (index > -1) {\n      handles.splice(index, 1);\n      this._handles.next(handles);\n    }\n  }\n  _setPreviewTemplate(preview) {\n    this._previewTemplate = preview;\n  }\n  _resetPreviewTemplate(preview) {\n    if (preview === this._previewTemplate) {\n      this._previewTemplate = null;\n    }\n  }\n  _setPlaceholderTemplate(placeholder) {\n    this._placeholderTemplate = placeholder;\n  }\n  _resetPlaceholderTemplate(placeholder) {\n    if (placeholder === this._placeholderTemplate) {\n      this._placeholderTemplate = null;\n    }\n  }\n  /** Syncs the root element with the `DragRef`. */\n  _updateRootElement() {\n    const element = this.element.nativeElement;\n    let rootElement = element;\n    if (this.rootElementSelector) {\n      rootElement = element.closest !== undefined ? element.closest(this.rootElementSelector) :\n      // Comment tag doesn't have closest method, so use parent's one.\n      element.parentElement?.closest(this.rootElementSelector);\n    }\n    if (rootElement && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      assertElementNode(rootElement, 'cdkDrag');\n    }\n    this._dragRef.withRootElement(rootElement || element);\n  }\n  /** Gets the boundary element, based on the `boundaryElement` value. */\n  _getBoundaryElement() {\n    const boundary = this.boundaryElement;\n    if (!boundary) {\n      return null;\n    }\n    if (typeof boundary === 'string') {\n      return this.element.nativeElement.closest(boundary);\n    }\n    return coerceElement(boundary);\n  }\n  /** Syncs the inputs of the CdkDrag with the options of the underlying DragRef. */\n  _syncInputs(ref) {\n    ref.beforeStarted.subscribe(() => {\n      if (!ref.isDragging()) {\n        const dir = this._dir;\n        const dragStartDelay = this.dragStartDelay;\n        const placeholder = this._placeholderTemplate ? {\n          template: this._placeholderTemplate.templateRef,\n          context: this._placeholderTemplate.data,\n          viewContainer: this._viewContainerRef\n        } : null;\n        const preview = this._previewTemplate ? {\n          template: this._previewTemplate.templateRef,\n          context: this._previewTemplate.data,\n          matchSize: this._previewTemplate.matchSize,\n          viewContainer: this._viewContainerRef\n        } : null;\n        ref.disabled = this.disabled;\n        ref.lockAxis = this.lockAxis;\n        ref.dragStartDelay = typeof dragStartDelay === 'object' && dragStartDelay ? dragStartDelay : coerceNumberProperty(dragStartDelay);\n        ref.constrainPosition = this.constrainPosition;\n        ref.previewClass = this.previewClass;\n        ref.withBoundaryElement(this._getBoundaryElement()).withPlaceholderTemplate(placeholder).withPreviewTemplate(preview).withPreviewContainer(this.previewContainer || 'global');\n        if (dir) {\n          ref.withDirection(dir.value);\n        }\n      }\n    });\n    // This only needs to be resolved once.\n    ref.beforeStarted.pipe(take(1)).subscribe(() => {\n      // If we managed to resolve a parent through DI, use it.\n      if (this._parentDrag) {\n        ref.withParent(this._parentDrag._dragRef);\n        return;\n      }\n      // Otherwise fall back to resolving the parent by looking up the DOM. This can happen if\n      // the item was projected into another item by something like `ngTemplateOutlet`.\n      let parent = this.element.nativeElement.parentElement;\n      while (parent) {\n        if (parent.classList.contains(DRAG_HOST_CLASS)) {\n          ref.withParent(CdkDrag._dragInstances.find(drag => {\n            return drag.element.nativeElement === parent;\n          })?._dragRef || null);\n          break;\n        }\n        parent = parent.parentElement;\n      }\n    });\n  }\n  /** Handles the events from the underlying `DragRef`. */\n  _handleEvents(ref) {\n    ref.started.subscribe(startEvent => {\n      this.started.emit({\n        source: this,\n        event: startEvent.event\n      });\n      // Since all of these events run outside of change detection,\n      // we need to ensure that everything is marked correctly.\n      this._changeDetectorRef.markForCheck();\n    });\n    ref.released.subscribe(releaseEvent => {\n      this.released.emit({\n        source: this,\n        event: releaseEvent.event\n      });\n    });\n    ref.ended.subscribe(endEvent => {\n      this.ended.emit({\n        source: this,\n        distance: endEvent.distance,\n        dropPoint: endEvent.dropPoint,\n        event: endEvent.event\n      });\n      // Since all of these events run outside of change detection,\n      // we need to ensure that everything is marked correctly.\n      this._changeDetectorRef.markForCheck();\n    });\n    ref.entered.subscribe(enterEvent => {\n      this.entered.emit({\n        container: enterEvent.container.data,\n        item: this,\n        currentIndex: enterEvent.currentIndex\n      });\n    });\n    ref.exited.subscribe(exitEvent => {\n      this.exited.emit({\n        container: exitEvent.container.data,\n        item: this\n      });\n    });\n    ref.dropped.subscribe(dropEvent => {\n      this.dropped.emit({\n        previousIndex: dropEvent.previousIndex,\n        currentIndex: dropEvent.currentIndex,\n        previousContainer: dropEvent.previousContainer.data,\n        container: dropEvent.container.data,\n        isPointerOverContainer: dropEvent.isPointerOverContainer,\n        item: this,\n        distance: dropEvent.distance,\n        dropPoint: dropEvent.dropPoint,\n        event: dropEvent.event\n      });\n    });\n  }\n  /** Assigns the default input values based on a provided config object. */\n  _assignDefaults(config) {\n    const {\n      lockAxis,\n      dragStartDelay,\n      constrainPosition,\n      previewClass,\n      boundaryElement,\n      draggingDisabled,\n      rootElementSelector,\n      previewContainer\n    } = config;\n    this.disabled = draggingDisabled == null ? false : draggingDisabled;\n    this.dragStartDelay = dragStartDelay || 0;\n    if (lockAxis) {\n      this.lockAxis = lockAxis;\n    }\n    if (constrainPosition) {\n      this.constrainPosition = constrainPosition;\n    }\n    if (previewClass) {\n      this.previewClass = previewClass;\n    }\n    if (boundaryElement) {\n      this.boundaryElement = boundaryElement;\n    }\n    if (rootElementSelector) {\n      this.rootElementSelector = rootElementSelector;\n    }\n    if (previewContainer) {\n      this.previewContainer = previewContainer;\n    }\n  }\n  /** Sets up the listener that syncs the handles with the drag ref. */\n  _setupHandlesListener() {\n    // Listen for any newly-added handles.\n    this._handles.pipe(\n    // Sync the new handles with the DragRef.\n    tap(handles => {\n      const handleElements = handles.map(handle => handle.element);\n      // Usually handles are only allowed to be a descendant of the drag element, but if\n      // the consumer defined a different drag root, we should allow the drag element\n      // itself to be a handle too.\n      if (this._selfHandle && this.rootElementSelector) {\n        handleElements.push(this.element);\n      }\n      this._dragRef.withHandles(handleElements);\n    }),\n    // Listen if the state of any of the handles changes.\n    switchMap(handles => {\n      return merge(...handles.map(item => item._stateChanges.pipe(startWith(item))));\n    }), takeUntil(this._destroyed)).subscribe(handleInstance => {\n      // Enabled/disable the handle that changed in the DragRef.\n      const dragRef = this._dragRef;\n      const handle = handleInstance.element.nativeElement;\n      handleInstance.disabled ? dragRef.disableHandle(handle) : dragRef.enableHandle(handle);\n    });\n  }\n  static {\n    this.ɵfac = function CdkDrag_Factory(t) {\n      return new (t || CdkDrag)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(CDK_DROP_LIST, 12), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(CDK_DRAG_CONFIG, 8), i0.ɵɵdirectiveInject(i1$1.Directionality, 8), i0.ɵɵdirectiveInject(DragDrop), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(CDK_DRAG_HANDLE, 10), i0.ɵɵdirectiveInject(CDK_DRAG_PARENT, 12));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkDrag,\n      selectors: [[\"\", \"cdkDrag\", \"\"]],\n      hostAttrs: [1, \"cdk-drag\"],\n      hostVars: 4,\n      hostBindings: function CdkDrag_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"cdk-drag-disabled\", ctx.disabled)(\"cdk-drag-dragging\", ctx._dragRef.isDragging());\n        }\n      },\n      inputs: {\n        data: [i0.ɵɵInputFlags.None, \"cdkDragData\", \"data\"],\n        lockAxis: [i0.ɵɵInputFlags.None, \"cdkDragLockAxis\", \"lockAxis\"],\n        rootElementSelector: [i0.ɵɵInputFlags.None, \"cdkDragRootElement\", \"rootElementSelector\"],\n        boundaryElement: [i0.ɵɵInputFlags.None, \"cdkDragBoundary\", \"boundaryElement\"],\n        dragStartDelay: [i0.ɵɵInputFlags.None, \"cdkDragStartDelay\", \"dragStartDelay\"],\n        freeDragPosition: [i0.ɵɵInputFlags.None, \"cdkDragFreeDragPosition\", \"freeDragPosition\"],\n        disabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"cdkDragDisabled\", \"disabled\", booleanAttribute],\n        constrainPosition: [i0.ɵɵInputFlags.None, \"cdkDragConstrainPosition\", \"constrainPosition\"],\n        previewClass: [i0.ɵɵInputFlags.None, \"cdkDragPreviewClass\", \"previewClass\"],\n        previewContainer: [i0.ɵɵInputFlags.None, \"cdkDragPreviewContainer\", \"previewContainer\"]\n      },\n      outputs: {\n        started: \"cdkDragStarted\",\n        released: \"cdkDragReleased\",\n        ended: \"cdkDragEnded\",\n        entered: \"cdkDragEntered\",\n        exited: \"cdkDragExited\",\n        dropped: \"cdkDragDropped\",\n        moved: \"cdkDragMoved\"\n      },\n      exportAs: [\"cdkDrag\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CDK_DRAG_PARENT,\n        useExisting: CdkDrag\n      }]), i0.ɵɵInputTransformsFeature, i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkDrag, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkDrag]',\n      exportAs: 'cdkDrag',\n      standalone: true,\n      host: {\n        'class': DRAG_HOST_CLASS,\n        '[class.cdk-drag-disabled]': 'disabled',\n        '[class.cdk-drag-dragging]': '_dragRef.isDragging()'\n      },\n      providers: [{\n        provide: CDK_DRAG_PARENT,\n        useExisting: CdkDrag\n      }]\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [CDK_DROP_LIST]\n    }, {\n      type: Optional\n    }, {\n      type: SkipSelf\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: i0.NgZone\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [CDK_DRAG_CONFIG]\n    }]\n  }, {\n    type: i1$1.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: DragDrop\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: CdkDragHandle,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Self\n    }, {\n      type: Inject,\n      args: [CDK_DRAG_HANDLE]\n    }]\n  }, {\n    type: CdkDrag,\n    decorators: [{\n      type: Optional\n    }, {\n      type: SkipSelf\n    }, {\n      type: Inject,\n      args: [CDK_DRAG_PARENT]\n    }]\n  }], {\n    data: [{\n      type: Input,\n      args: ['cdkDragData']\n    }],\n    lockAxis: [{\n      type: Input,\n      args: ['cdkDragLockAxis']\n    }],\n    rootElementSelector: [{\n      type: Input,\n      args: ['cdkDragRootElement']\n    }],\n    boundaryElement: [{\n      type: Input,\n      args: ['cdkDragBoundary']\n    }],\n    dragStartDelay: [{\n      type: Input,\n      args: ['cdkDragStartDelay']\n    }],\n    freeDragPosition: [{\n      type: Input,\n      args: ['cdkDragFreeDragPosition']\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        alias: 'cdkDragDisabled',\n        transform: booleanAttribute\n      }]\n    }],\n    constrainPosition: [{\n      type: Input,\n      args: ['cdkDragConstrainPosition']\n    }],\n    previewClass: [{\n      type: Input,\n      args: ['cdkDragPreviewClass']\n    }],\n    previewContainer: [{\n      type: Input,\n      args: ['cdkDragPreviewContainer']\n    }],\n    started: [{\n      type: Output,\n      args: ['cdkDragStarted']\n    }],\n    released: [{\n      type: Output,\n      args: ['cdkDragReleased']\n    }],\n    ended: [{\n      type: Output,\n      args: ['cdkDragEnded']\n    }],\n    entered: [{\n      type: Output,\n      args: ['cdkDragEntered']\n    }],\n    exited: [{\n      type: Output,\n      args: ['cdkDragExited']\n    }],\n    dropped: [{\n      type: Output,\n      args: ['cdkDragDropped']\n    }],\n    moved: [{\n      type: Output,\n      args: ['cdkDragMoved']\n    }]\n  });\n})();\n\n/**\n * Injection token that can be used to reference instances of `CdkDropListGroup`. It serves as\n * alternative token to the actual `CdkDropListGroup` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DROP_LIST_GROUP = new InjectionToken('CdkDropListGroup');\n/**\n * Declaratively connects sibling `cdkDropList` instances together. All of the `cdkDropList`\n * elements that are placed inside a `cdkDropListGroup` will be connected to each other\n * automatically. Can be used as an alternative to the `cdkDropListConnectedTo` input\n * from `cdkDropList`.\n */\nclass CdkDropListGroup {\n  constructor() {\n    /** Drop lists registered inside the group. */\n    this._items = new Set();\n    /** Whether starting a dragging sequence from inside this group is disabled. */\n    this.disabled = false;\n  }\n  ngOnDestroy() {\n    this._items.clear();\n  }\n  static {\n    this.ɵfac = function CdkDropListGroup_Factory(t) {\n      return new (t || CdkDropListGroup)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkDropListGroup,\n      selectors: [[\"\", \"cdkDropListGroup\", \"\"]],\n      inputs: {\n        disabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"cdkDropListGroupDisabled\", \"disabled\", booleanAttribute]\n      },\n      exportAs: [\"cdkDropListGroup\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CDK_DROP_LIST_GROUP,\n        useExisting: CdkDropListGroup\n      }]), i0.ɵɵInputTransformsFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkDropListGroup, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkDropListGroup]',\n      exportAs: 'cdkDropListGroup',\n      standalone: true,\n      providers: [{\n        provide: CDK_DROP_LIST_GROUP,\n        useExisting: CdkDropListGroup\n      }]\n    }]\n  }], null, {\n    disabled: [{\n      type: Input,\n      args: [{\n        alias: 'cdkDropListGroupDisabled',\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/** Counter used to generate unique ids for drop zones. */\nlet _uniqueIdCounter = 0;\n/** Container that wraps a set of draggable items. */\nclass CdkDropList {\n  /** Keeps track of the drop lists that are currently on the page. */\n  static {\n    this._dropLists = [];\n  }\n  /** Whether starting a dragging sequence from this container is disabled. */\n  get disabled() {\n    return this._disabled || !!this._group && this._group.disabled;\n  }\n  set disabled(value) {\n    // Usually we sync the directive and ref state right before dragging starts, in order to have\n    // a single point of failure and to avoid having to use setters for everything. `disabled` is\n    // a special case, because it can prevent the `beforeStarted` event from firing, which can lock\n    // the user in a disabled state, so we also need to sync it as it's being set.\n    this._dropListRef.disabled = this._disabled = value;\n  }\n  constructor( /** Element that the drop list is attached to. */\n  element, dragDrop, _changeDetectorRef, _scrollDispatcher, _dir, _group, config) {\n    this.element = element;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._scrollDispatcher = _scrollDispatcher;\n    this._dir = _dir;\n    this._group = _group;\n    /** Emits when the list has been destroyed. */\n    this._destroyed = new Subject();\n    /**\n     * Other draggable containers that this container is connected to and into which the\n     * container's items can be transferred. Can either be references to other drop containers,\n     * or their unique IDs.\n     */\n    this.connectedTo = [];\n    /**\n     * Unique ID for the drop zone. Can be used as a reference\n     * in the `connectedTo` of another `CdkDropList`.\n     */\n    this.id = `cdk-drop-list-${_uniqueIdCounter++}`;\n    /**\n     * Function that is used to determine whether an item\n     * is allowed to be moved into a drop container.\n     */\n    this.enterPredicate = () => true;\n    /** Functions that is used to determine whether an item can be sorted into a particular index. */\n    this.sortPredicate = () => true;\n    /** Emits when the user drops an item inside the container. */\n    this.dropped = new EventEmitter();\n    /**\n     * Emits when the user has moved a new drag item into this container.\n     */\n    this.entered = new EventEmitter();\n    /**\n     * Emits when the user removes an item from the container\n     * by dragging it into another container.\n     */\n    this.exited = new EventEmitter();\n    /** Emits as the user is swapping items while actively dragging. */\n    this.sorted = new EventEmitter();\n    /**\n     * Keeps track of the items that are registered with this container. Historically we used to\n     * do this with a `ContentChildren` query, however queries don't handle transplanted views very\n     * well which means that we can't handle cases like dragging the headers of a `mat-table`\n     * correctly. What we do instead is to have the items register themselves with the container\n     * and then we sort them based on their position in the DOM.\n     */\n    this._unsortedItems = new Set();\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      assertElementNode(element.nativeElement, 'cdkDropList');\n    }\n    this._dropListRef = dragDrop.createDropList(element);\n    this._dropListRef.data = this;\n    if (config) {\n      this._assignDefaults(config);\n    }\n    this._dropListRef.enterPredicate = (drag, drop) => {\n      return this.enterPredicate(drag.data, drop.data);\n    };\n    this._dropListRef.sortPredicate = (index, drag, drop) => {\n      return this.sortPredicate(index, drag.data, drop.data);\n    };\n    this._setupInputSyncSubscription(this._dropListRef);\n    this._handleEvents(this._dropListRef);\n    CdkDropList._dropLists.push(this);\n    if (_group) {\n      _group._items.add(this);\n    }\n  }\n  /** Registers an items with the drop list. */\n  addItem(item) {\n    this._unsortedItems.add(item);\n    if (this._dropListRef.isDragging()) {\n      this._syncItemsWithRef();\n    }\n  }\n  /** Removes an item from the drop list. */\n  removeItem(item) {\n    this._unsortedItems.delete(item);\n    if (this._dropListRef.isDragging()) {\n      this._syncItemsWithRef();\n    }\n  }\n  /** Gets the registered items in the list, sorted by their position in the DOM. */\n  getSortedItems() {\n    return Array.from(this._unsortedItems).sort((a, b) => {\n      const documentPosition = a._dragRef.getVisibleElement().compareDocumentPosition(b._dragRef.getVisibleElement());\n      // `compareDocumentPosition` returns a bitmask so we have to use a bitwise operator.\n      // https://developer.mozilla.org/en-US/docs/Web/API/Node/compareDocumentPosition\n      // tslint:disable-next-line:no-bitwise\n      return documentPosition & Node.DOCUMENT_POSITION_FOLLOWING ? -1 : 1;\n    });\n  }\n  ngOnDestroy() {\n    const index = CdkDropList._dropLists.indexOf(this);\n    if (index > -1) {\n      CdkDropList._dropLists.splice(index, 1);\n    }\n    if (this._group) {\n      this._group._items.delete(this);\n    }\n    this._unsortedItems.clear();\n    this._dropListRef.dispose();\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n  /** Syncs the inputs of the CdkDropList with the options of the underlying DropListRef. */\n  _setupInputSyncSubscription(ref) {\n    if (this._dir) {\n      this._dir.change.pipe(startWith(this._dir.value), takeUntil(this._destroyed)).subscribe(value => ref.withDirection(value));\n    }\n    ref.beforeStarted.subscribe(() => {\n      const siblings = coerceArray(this.connectedTo).map(drop => {\n        if (typeof drop === 'string') {\n          const correspondingDropList = CdkDropList._dropLists.find(list => list.id === drop);\n          if (!correspondingDropList && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            console.warn(`CdkDropList could not find connected drop list with id \"${drop}\"`);\n          }\n          return correspondingDropList;\n        }\n        return drop;\n      });\n      if (this._group) {\n        this._group._items.forEach(drop => {\n          if (siblings.indexOf(drop) === -1) {\n            siblings.push(drop);\n          }\n        });\n      }\n      // Note that we resolve the scrollable parents here so that we delay the resolution\n      // as long as possible, ensuring that the element is in its final place in the DOM.\n      if (!this._scrollableParentsResolved) {\n        const scrollableParents = this._scrollDispatcher.getAncestorScrollContainers(this.element).map(scrollable => scrollable.getElementRef().nativeElement);\n        this._dropListRef.withScrollableParents(scrollableParents);\n        // Only do this once since it involves traversing the DOM and the parents\n        // shouldn't be able to change without the drop list being destroyed.\n        this._scrollableParentsResolved = true;\n      }\n      ref.disabled = this.disabled;\n      ref.lockAxis = this.lockAxis;\n      ref.sortingDisabled = this.sortingDisabled;\n      ref.autoScrollDisabled = this.autoScrollDisabled;\n      ref.autoScrollStep = coerceNumberProperty(this.autoScrollStep, 2);\n      ref.connectedTo(siblings.filter(drop => drop && drop !== this).map(list => list._dropListRef)).withOrientation(this.orientation);\n    });\n  }\n  /** Handles events from the underlying DropListRef. */\n  _handleEvents(ref) {\n    ref.beforeStarted.subscribe(() => {\n      this._syncItemsWithRef();\n      this._changeDetectorRef.markForCheck();\n    });\n    ref.entered.subscribe(event => {\n      this.entered.emit({\n        container: this,\n        item: event.item.data,\n        currentIndex: event.currentIndex\n      });\n    });\n    ref.exited.subscribe(event => {\n      this.exited.emit({\n        container: this,\n        item: event.item.data\n      });\n      this._changeDetectorRef.markForCheck();\n    });\n    ref.sorted.subscribe(event => {\n      this.sorted.emit({\n        previousIndex: event.previousIndex,\n        currentIndex: event.currentIndex,\n        container: this,\n        item: event.item.data\n      });\n    });\n    ref.dropped.subscribe(dropEvent => {\n      this.dropped.emit({\n        previousIndex: dropEvent.previousIndex,\n        currentIndex: dropEvent.currentIndex,\n        previousContainer: dropEvent.previousContainer.data,\n        container: dropEvent.container.data,\n        item: dropEvent.item.data,\n        isPointerOverContainer: dropEvent.isPointerOverContainer,\n        distance: dropEvent.distance,\n        dropPoint: dropEvent.dropPoint,\n        event: dropEvent.event\n      });\n      // Mark for check since all of these events run outside of change\n      // detection and we're not guaranteed for something else to have triggered it.\n      this._changeDetectorRef.markForCheck();\n    });\n    merge(ref.receivingStarted, ref.receivingStopped).subscribe(() => this._changeDetectorRef.markForCheck());\n  }\n  /** Assigns the default input values based on a provided config object. */\n  _assignDefaults(config) {\n    const {\n      lockAxis,\n      draggingDisabled,\n      sortingDisabled,\n      listAutoScrollDisabled,\n      listOrientation\n    } = config;\n    this.disabled = draggingDisabled == null ? false : draggingDisabled;\n    this.sortingDisabled = sortingDisabled == null ? false : sortingDisabled;\n    this.autoScrollDisabled = listAutoScrollDisabled == null ? false : listAutoScrollDisabled;\n    this.orientation = listOrientation || 'vertical';\n    if (lockAxis) {\n      this.lockAxis = lockAxis;\n    }\n  }\n  /** Syncs up the registered drag items with underlying drop list ref. */\n  _syncItemsWithRef() {\n    this._dropListRef.withItems(this.getSortedItems().map(item => item._dragRef));\n  }\n  static {\n    this.ɵfac = function CdkDropList_Factory(t) {\n      return new (t || CdkDropList)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(DragDrop), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.ScrollDispatcher), i0.ɵɵdirectiveInject(i1$1.Directionality, 8), i0.ɵɵdirectiveInject(CDK_DROP_LIST_GROUP, 12), i0.ɵɵdirectiveInject(CDK_DRAG_CONFIG, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkDropList,\n      selectors: [[\"\", \"cdkDropList\", \"\"], [\"cdk-drop-list\"]],\n      hostAttrs: [1, \"cdk-drop-list\"],\n      hostVars: 7,\n      hostBindings: function CdkDropList_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"id\", ctx.id);\n          i0.ɵɵclassProp(\"cdk-drop-list-disabled\", ctx.disabled)(\"cdk-drop-list-dragging\", ctx._dropListRef.isDragging())(\"cdk-drop-list-receiving\", ctx._dropListRef.isReceiving());\n        }\n      },\n      inputs: {\n        connectedTo: [i0.ɵɵInputFlags.None, \"cdkDropListConnectedTo\", \"connectedTo\"],\n        data: [i0.ɵɵInputFlags.None, \"cdkDropListData\", \"data\"],\n        orientation: [i0.ɵɵInputFlags.None, \"cdkDropListOrientation\", \"orientation\"],\n        id: \"id\",\n        lockAxis: [i0.ɵɵInputFlags.None, \"cdkDropListLockAxis\", \"lockAxis\"],\n        disabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"cdkDropListDisabled\", \"disabled\", booleanAttribute],\n        sortingDisabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"cdkDropListSortingDisabled\", \"sortingDisabled\", booleanAttribute],\n        enterPredicate: [i0.ɵɵInputFlags.None, \"cdkDropListEnterPredicate\", \"enterPredicate\"],\n        sortPredicate: [i0.ɵɵInputFlags.None, \"cdkDropListSortPredicate\", \"sortPredicate\"],\n        autoScrollDisabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"cdkDropListAutoScrollDisabled\", \"autoScrollDisabled\", booleanAttribute],\n        autoScrollStep: [i0.ɵɵInputFlags.None, \"cdkDropListAutoScrollStep\", \"autoScrollStep\"]\n      },\n      outputs: {\n        dropped: \"cdkDropListDropped\",\n        entered: \"cdkDropListEntered\",\n        exited: \"cdkDropListExited\",\n        sorted: \"cdkDropListSorted\"\n      },\n      exportAs: [\"cdkDropList\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([\n      // Prevent child drop lists from picking up the same group as their parent.\n      {\n        provide: CDK_DROP_LIST_GROUP,\n        useValue: undefined\n      }, {\n        provide: CDK_DROP_LIST,\n        useExisting: CdkDropList\n      }]), i0.ɵɵInputTransformsFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkDropList, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkDropList], cdk-drop-list',\n      exportAs: 'cdkDropList',\n      standalone: true,\n      providers: [\n      // Prevent child drop lists from picking up the same group as their parent.\n      {\n        provide: CDK_DROP_LIST_GROUP,\n        useValue: undefined\n      }, {\n        provide: CDK_DROP_LIST,\n        useExisting: CdkDropList\n      }],\n      host: {\n        'class': 'cdk-drop-list',\n        '[attr.id]': 'id',\n        '[class.cdk-drop-list-disabled]': 'disabled',\n        '[class.cdk-drop-list-dragging]': '_dropListRef.isDragging()',\n        '[class.cdk-drop-list-receiving]': '_dropListRef.isReceiving()'\n      }\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: DragDrop\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1.ScrollDispatcher\n  }, {\n    type: i1$1.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: CdkDropListGroup,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [CDK_DROP_LIST_GROUP]\n    }, {\n      type: SkipSelf\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [CDK_DRAG_CONFIG]\n    }]\n  }], {\n    connectedTo: [{\n      type: Input,\n      args: ['cdkDropListConnectedTo']\n    }],\n    data: [{\n      type: Input,\n      args: ['cdkDropListData']\n    }],\n    orientation: [{\n      type: Input,\n      args: ['cdkDropListOrientation']\n    }],\n    id: [{\n      type: Input\n    }],\n    lockAxis: [{\n      type: Input,\n      args: ['cdkDropListLockAxis']\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        alias: 'cdkDropListDisabled',\n        transform: booleanAttribute\n      }]\n    }],\n    sortingDisabled: [{\n      type: Input,\n      args: [{\n        alias: 'cdkDropListSortingDisabled',\n        transform: booleanAttribute\n      }]\n    }],\n    enterPredicate: [{\n      type: Input,\n      args: ['cdkDropListEnterPredicate']\n    }],\n    sortPredicate: [{\n      type: Input,\n      args: ['cdkDropListSortPredicate']\n    }],\n    autoScrollDisabled: [{\n      type: Input,\n      args: [{\n        alias: 'cdkDropListAutoScrollDisabled',\n        transform: booleanAttribute\n      }]\n    }],\n    autoScrollStep: [{\n      type: Input,\n      args: ['cdkDropListAutoScrollStep']\n    }],\n    dropped: [{\n      type: Output,\n      args: ['cdkDropListDropped']\n    }],\n    entered: [{\n      type: Output,\n      args: ['cdkDropListEntered']\n    }],\n    exited: [{\n      type: Output,\n      args: ['cdkDropListExited']\n    }],\n    sorted: [{\n      type: Output,\n      args: ['cdkDropListSorted']\n    }]\n  });\n})();\n\n/**\n * Injection token that can be used to reference instances of `CdkDragPreview`. It serves as\n * alternative token to the actual `CdkDragPreview` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DRAG_PREVIEW = new InjectionToken('CdkDragPreview');\n/**\n * Element that will be used as a template for the preview\n * of a CdkDrag when it is being dragged.\n */\nclass CdkDragPreview {\n  constructor(templateRef) {\n    this.templateRef = templateRef;\n    this._drag = inject(CDK_DRAG_PARENT, {\n      optional: true\n    });\n    /** Whether the preview should preserve the same size as the item that is being dragged. */\n    this.matchSize = false;\n    this._drag?._setPreviewTemplate(this);\n  }\n  ngOnDestroy() {\n    this._drag?._resetPreviewTemplate(this);\n  }\n  static {\n    this.ɵfac = function CdkDragPreview_Factory(t) {\n      return new (t || CdkDragPreview)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkDragPreview,\n      selectors: [[\"ng-template\", \"cdkDragPreview\", \"\"]],\n      inputs: {\n        data: \"data\",\n        matchSize: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"matchSize\", \"matchSize\", booleanAttribute]\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CDK_DRAG_PREVIEW,\n        useExisting: CdkDragPreview\n      }]), i0.ɵɵInputTransformsFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkDragPreview, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[cdkDragPreview]',\n      standalone: true,\n      providers: [{\n        provide: CDK_DRAG_PREVIEW,\n        useExisting: CdkDragPreview\n      }]\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], {\n    data: [{\n      type: Input\n    }],\n    matchSize: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/**\n * Injection token that can be used to reference instances of `CdkDragPlaceholder`. It serves as\n * alternative token to the actual `CdkDragPlaceholder` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DRAG_PLACEHOLDER = new InjectionToken('CdkDragPlaceholder');\n/**\n * Element that will be used as a template for the placeholder of a CdkDrag when\n * it is being dragged. The placeholder is displayed in place of the element being dragged.\n */\nclass CdkDragPlaceholder {\n  constructor(templateRef) {\n    this.templateRef = templateRef;\n    this._drag = inject(CDK_DRAG_PARENT, {\n      optional: true\n    });\n    this._drag?._setPlaceholderTemplate(this);\n  }\n  ngOnDestroy() {\n    this._drag?._resetPlaceholderTemplate(this);\n  }\n  static {\n    this.ɵfac = function CdkDragPlaceholder_Factory(t) {\n      return new (t || CdkDragPlaceholder)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkDragPlaceholder,\n      selectors: [[\"ng-template\", \"cdkDragPlaceholder\", \"\"]],\n      inputs: {\n        data: \"data\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CDK_DRAG_PLACEHOLDER,\n        useExisting: CdkDragPlaceholder\n      }])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkDragPlaceholder, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[cdkDragPlaceholder]',\n      standalone: true,\n      providers: [{\n        provide: CDK_DRAG_PLACEHOLDER,\n        useExisting: CdkDragPlaceholder\n      }]\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], {\n    data: [{\n      type: Input\n    }]\n  });\n})();\nconst DRAG_DROP_DIRECTIVES = [CdkDropList, CdkDropListGroup, CdkDrag, CdkDragHandle, CdkDragPreview, CdkDragPlaceholder];\nclass DragDropModule {\n  static {\n    this.ɵfac = function DragDropModule_Factory(t) {\n      return new (t || DragDropModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: DragDropModule,\n      imports: [CdkDropList, CdkDropListGroup, CdkDrag, CdkDragHandle, CdkDragPreview, CdkDragPlaceholder],\n      exports: [CdkScrollableModule, CdkDropList, CdkDropListGroup, CdkDrag, CdkDragHandle, CdkDragPreview, CdkDragPlaceholder]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [DragDrop],\n      imports: [CdkScrollableModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DragDropModule, [{\n    type: NgModule,\n    args: [{\n      imports: DRAG_DROP_DIRECTIVES,\n      exports: [CdkScrollableModule, ...DRAG_DROP_DIRECTIVES],\n      providers: [DragDrop]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CDK_DRAG_CONFIG, CDK_DRAG_HANDLE, CDK_DRAG_PARENT, CDK_DRAG_PLACEHOLDER, CDK_DRAG_PREVIEW, CDK_DROP_LIST, CDK_DROP_LIST_GROUP, CdkDrag, CdkDragHandle, CdkDragPlaceholder, CdkDragPreview, CdkDropList, CdkDropListGroup, DragDrop, DragDropModule, DragDropRegistry, DragRef, DropListRef, copyArrayItem, moveItemInArray, transferArrayItem };", "map": {"version": 3, "names": ["i0", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "inject", "ApplicationRef", "EnvironmentInjector", "createComponent", "Injectable", "Inject", "InjectionToken", "booleanAttribute", "Directive", "Optional", "SkipSelf", "Input", "EventEmitter", "Self", "Output", "NgModule", "DOCUMENT", "i1", "CdkScrollableModule", "_getEventTarget", "normalizePassiveListenerOptions", "_getShadowRoot", "coerceElement", "coerceNumberProperty", "coerce<PERSON><PERSON><PERSON>", "isFakeTouchstartFromScreenReader", "isFakeMousedownFromScreenReader", "Subject", "Subscription", "interval", "animationFrameScheduler", "Observable", "merge", "BehaviorSubject", "takeUntil", "map", "take", "tap", "switchMap", "startWith", "i1$1", "extendStyles", "dest", "source", "importantProperties", "key", "hasOwnProperty", "value", "setProperty", "has", "removeProperty", "toggleNativeDragInteractions", "element", "enable", "userSelect", "style", "toggleVisibility", "position", "top", "opacity", "left", "combineTransforms", "transform", "initialTransform", "matchElementSize", "target", "sourceRect", "width", "height", "getTransform", "x", "y", "Math", "round", "getMutableClientRect", "rect", "getBoundingClientRect", "right", "bottom", "isInsideClientRect", "clientRect", "adjustDomRect", "domRect", "isPointerNearDomRect", "threshold", "pointerX", "pointerY", "xThreshold", "yT<PERSON><PERSON>old", "ParentPositionTracker", "constructor", "_document", "positions", "Map", "clear", "cache", "elements", "set", "scrollPosition", "getViewportScrollPosition", "for<PERSON>ach", "scrollTop", "scrollLeft", "handleScroll", "event", "cachedPosition", "get", "newTop", "newLeft", "viewportScrollPosition", "topDifference", "leftDifference", "node", "contains", "window", "scrollY", "scrollX", "deepCloneNode", "clone", "cloneNode", "descendantsWithId", "querySelectorAll", "nodeName", "toLowerCase", "removeAttribute", "i", "length", "transferCanvasData", "transferInputData", "transferData", "selector", "callback", "descendantElements", "cloneElements", "cloneUniqueId", "type", "name", "context", "getContext", "drawImage", "getRootNode", "viewRef", "rootNodes", "nodeType", "ELEMENT_NODE", "wrapper", "createElement", "append<PERSON><PERSON><PERSON>", "parseCssTimeUnitsToMs", "multiplier", "indexOf", "parseFloat", "getTransformTransitionDurationInMs", "computedStyle", "getComputedStyle", "transitionedProperties", "parseCssPropertyValue", "property", "find", "prop", "propertyIndex", "rawDurations", "rawDelays", "getPropertyValue", "split", "part", "trim", "Set", "PreviewRef", "_rootElement", "_direction", "_initialDomRect", "_previewTemplate", "_previewClass", "_pickupPositionOnPage", "_initialTransform", "_zIndex", "attach", "parent", "_preview", "_createPreview", "destroy", "remove", "_previewEmbeddedView", "setTransform", "addClass", "className", "classList", "add", "getTransitionDuration", "addEventListener", "handler", "removeEventListener", "previewConfig", "previewClass", "previewTemplate", "template", "preview", "rootRect", "matchSize", "viewContainer", "createEmbeddedView", "detectChanges", "setAttribute", "Array", "isArray", "passiveEventListenerOptions", "passive", "activeEventListenerOptions", "activeCapturingEventOptions$1", "capture", "MOUSE_EVENT_IGNORE_TIME", "dragImportantProperties", "DragRef", "disabled", "_disabled", "_dropContainer", "_toggleNativeDragInteractions", "_handles", "handle", "_config", "_ngZone", "_viewportRuler", "_dragDropRegistry", "_passiveTransform", "_activeTransform", "_hasStartedDragging", "_moveEvents", "_pointerMoveSubscription", "EMPTY", "_pointerUpSubscription", "_scrollSubscription", "_resizeSubscription", "_boundaryElement", "_nativeInteractionsEnabled", "_<PERSON><PERSON><PERSON><PERSON>", "dragStartDelay", "beforeStarted", "started", "released", "ended", "entered", "exited", "dropped", "moved", "_pointerDown", "next", "targetHandle", "_getTar<PERSON><PERSON><PERSON>le", "_initializeDragSequence", "_pointerMove", "pointerPosition", "_getPointerPositionOnPage", "distanceX", "abs", "distanceY", "isOverThreshold", "dragStartThreshold", "isDelayElapsed", "Date", "now", "_dragStartTime", "_getDragStartDelay", "container", "_endDragSequence", "isDragging", "isReceiving", "cancelable", "preventDefault", "run", "_startDragSequence", "constrainedPointerPosition", "_getConstrainedPointerPosition", "_hasMoved", "_lastKnownPointerPosition", "_updatePointerDirectionDelta", "_updateActiveDropContainer", "offset", "constrainPosition", "activeTransform", "_applyRootElementTransform", "observers", "distance", "_getDragDistance", "delta", "_pointerDirectionDelta", "_pointerUp", "_nativeDragStart", "withRootElement", "with<PERSON><PERSON>nt", "parentDragRef", "_parentPositions", "registerDragItem", "getPlaceholderElement", "_placeholder", "getRootElement", "getVisibleElement", "<PERSON><PERSON><PERSON><PERSON>", "handles", "<PERSON><PERSON><PERSON><PERSON>", "withPreviewTemplate", "withPlaceholderTemplate", "_placeholderTemplate", "rootElement", "_removeRootElementListeners", "runOutsideAngular", "undefined", "SVGElement", "_ownerSVGElement", "ownerSVGElement", "withBoundaryElement", "boundaryElement", "unsubscribe", "change", "subscribe", "_containInsideBoundaryOnResize", "_parentDragRef", "dispose", "_anchor", "_destroyPreview", "_destroyPlaceholder", "removeDragItem", "_removeListeners", "complete", "reset", "disable<PERSON><PERSON><PERSON>", "enableHandle", "delete", "withDirection", "direction", "_withDropContainer", "getFreeDragPosition", "setFreeDragPosition", "withPreviewContainer", "_previewContainer", "_sortFromLastPointerPosition", "shadowDomSelectStart", "_placeholderRef", "stopDragging", "webkitTapHighlightColor", "_rootElementTapHighlight", "_stopScrolling", "_animatePreviewToPlaceholder", "then", "_cleanupDragArtifacts", "_cleanupCachedDimensions", "dropPoint", "isTouchEvent", "_lastTouchEventTime", "shadowRoot", "dropContainer", "parentNode", "placeholder", "_createPlaceholderElement", "anchor", "createComment", "insertBefore", "zIndex", "_getPreviewInsertionPoint", "body", "<PERSON><PERSON><PERSON><PERSON>", "start", "_initialContainer", "_initialIndex", "getItemIndex", "getScrollableParents", "referenceElement", "stopPropagation", "isTouchSequence", "isAuxiliaryMouseButton", "button", "isSyntheticEvent", "isFakeEvent", "draggable", "rootStyles", "pointer<PERSON><PERSON>", "pointerUp", "scrolled", "scrollEvent", "_updateOnScroll", "_boundaryRect", "_pickupPositionInElement", "_getPointerPositionInElement", "_pointerPositionAtLastDirectionChange", "startDragging", "_previewRect", "currentIndex", "isPointerOverContainer", "_isOverContainer", "item", "previousIndex", "previousContainer", "drop", "rawX", "rawY", "newContainer", "_getSiblingContainerFromPosition", "exit", "enter", "sortingDisabled", "_startScrollingIfNecessary", "_sortItem", "_applyPreviewTransform", "Promise", "resolve", "placeholder<PERSON><PERSON><PERSON>", "duration", "propertyName", "clearTimeout", "timeout", "setTimeout", "placeholder<PERSON><PERSON><PERSON>g", "placeholderTemplate", "pointerEvents", "elementRect", "handleElement", "referenceRect", "point", "targetTouches", "_getViewportScrollPosition", "pageX", "pageY", "touches", "changedTouches", "svgMatrix", "getScreenCTM", "svgPoint", "createSVGPoint", "matrixTransform", "inverse", "dropContainerLock", "lockAxis", "pickupX", "pickupY", "boundaryRect", "previewWidth", "previewHeight", "_getPreviewRect", "minY", "maxY", "minX", "maxX", "clamp$1", "pointerPositionOnPage", "positionSinceLastChange", "changeX", "changeY", "pointerDirectionChangeThreshold", "shouldEnable", "styles", "currentPosition", "pickupPosition", "leftOverflow", "rightOverflow", "topOverflow", "bottomOverflow", "touch", "mouse", "scrollDifference", "_cachedShadowRoot", "initialParent", "previewContainer", "documentRef", "fullscreenElement", "webkitFullscreenElement", "mozFullScreenElement", "msFullscreenElement", "min", "max", "moveItemInArray", "array", "fromIndex", "toIndex", "from", "clamp", "to", "transferArrayItem", "currentArray", "targetArray", "targetIndex", "splice", "copyArrayItem", "SingleAxisSortStrategy", "_element", "_itemPositions", "orientation", "_previousSwap", "drag", "overlaps", "items", "withItems", "sort", "pointer<PERSON><PERSON><PERSON>", "siblings", "newIndex", "_getItemIndexFromPointerPosition", "isHorizontal", "findIndex", "currentItem", "siblingAtNewPosition", "newPosition", "itemOffset", "_getItemOffsetPx", "siblingOffset", "_getSiblingOffsetPx", "oldOrder", "slice", "sibling", "index", "isDraggedItem", "elementToOffset", "activeDraggables", "_activeDraggables", "newPositionReference", "_shouldEnterAsFirstChild", "parentElement", "push", "_cacheItemPositions", "withSortPredicate", "predicate", "_sortPredicate", "p", "getActiveItemsSnapshot", "reverse", "updateOnScroll", "elementToMeasure", "a", "b", "immediateSibling", "end", "itemPositions", "reversed", "lastItemRect", "firstItemRect", "floor", "DROP_PROXIMITY_THRESHOLD", "SCROLL_PROXIMITY_THRESHOLD", "AutoScrollVerticalDirection", "AutoScrollHorizontalDirection", "DropListRef", "autoScrollDisabled", "autoScrollStep", "enterPredicate", "sortPredicate", "sorted", "receivingStarted", "receivingStopped", "_isDragging", "_draggables", "_siblings", "_activeSiblings", "_viewportScrollSubscription", "_verticalScrollDirection", "NONE", "_horizontalScrollDirection", "_stopScrollTimers", "_startScrollInterval", "pipe", "_scrollNode", "scrollStep", "UP", "scrollBy", "DOWN", "LEFT", "RIGHT", "withScrollableParents", "registerDropContainer", "_sortStrategy", "removeDropContainer", "_draggingStarted", "_notifyReceivingSiblings", "_cacheParentPositions", "_reset", "previousItems", "draggedItems", "filter", "every", "connectedTo", "withOrientation", "_scrollableElements", "size", "_domRect", "result", "scrollNode", "verticalScrollDirection", "horizontalScrollDirection", "getElementScrollDirections", "getViewportSize", "getVerticalScrollDirection", "getHorizontalScrollDirection", "_initialScrollSnap", "msScrollSnapType", "scrollSnapType", "_listenToScrollEvents", "_stopReceiving", "_canReceive", "elementFromPoint", "nativeElement", "_startReceiving", "activeSiblings", "initiator", "receiver", "computedVertical", "computedHorizontal", "scrollHeight", "clientHeight", "scrollWidth", "clientWidth", "activeCapturingEventOptions", "activeApps", "_Resets<PERSON><PERSON>der", "ɵfac", "_ResetsLoader_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "selectors", "hostAttrs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "_ResetsLoader_Template", "rf", "ctx", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "None", "OnPush", "host", "DragDropRegistry", "_appRef", "_environmentInjector", "_dropInstances", "_dragInstances", "_activeDragInstances", "_globalListeners", "_draggingPredicate", "scroll", "_preventDefaultWhileDragging", "_persistentTouchmoveListener", "some", "_loadResets", "startsWith", "e", "options", "config", "_clearGlobalListeners", "streams", "observer", "eventOptions", "ngOnDestroy", "instance", "componentRef", "environmentInjector", "onDestroy", "DragDropRegistry_Factory", "ɵɵinject", "NgZone", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "decorators", "DEFAULT_CONFIG", "DragDrop", "createDrag", "createDropList", "DragDrop_Factory", "ViewportRuler", "CDK_DRAG_PARENT", "assertElementNode", "Error", "CDK_DRAG_HANDLE", "CdkDragHandle", "_stateChanges", "_parentDrag", "_add<PERSON>andle", "_remove<PERSON><PERSON><PERSON>", "CdkDragHandle_Factory", "ɵɵdirectiveInject", "ElementRef", "ɵdir", "ɵɵdefineDirective", "inputs", "ɵɵInputFlags", "HasDecoratorInputTransform", "ɵɵProvidersFeature", "provide", "useExisting", "ɵɵInputTransformsFeature", "providers", "alias", "CDK_DRAG_CONFIG", "DRAG_HOST_CLASS", "CDK_DROP_LIST", "CdkDrag", "_dragRef", "_viewContainerRef", "_dir", "dragDrop", "_changeDetectorRef", "_selfHandle", "_destroyed", "subscription", "movedEvent", "data", "_assignDefaults", "_dropListRef", "addItem", "_syncInputs", "_handleEvents", "ngAfterViewInit", "onStable", "_updateRootElement", "_setupHandlesListener", "freeDragPosition", "ngOnChanges", "changes", "rootSelectorChange", "positionChange", "firstChange", "removeItem", "getValue", "_setPreviewTemplate", "_resetPreviewTemplate", "_setPlaceholderTemplate", "_resetPlaceholderTemplate", "rootElementSelector", "closest", "_getBoundaryElement", "boundary", "ref", "dir", "templateRef", "startEvent", "emit", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "releaseEvent", "endEvent", "enterEvent", "exitEvent", "dropEvent", "draggingDisabled", "handleElements", "handleInstance", "dragRef", "CdkDrag_Factory", "ViewContainerRef", "Directionality", "ChangeDetectorRef", "hostVars", "hostBindings", "CdkDrag_HostBindings", "ɵɵclassProp", "outputs", "exportAs", "ɵɵNgOnChangesFeature", "CDK_DROP_LIST_GROUP", "CdkDropListGroup", "_items", "CdkDropListGroup_Factory", "_uniqueIdCounter", "CdkDropList", "_dropLists", "_group", "_scrollDispatcher", "id", "_unsortedItems", "_setupInputSyncSubscription", "_syncItemsWithRef", "getSortedItems", "documentPosition", "compareDocumentPosition", "Node", "DOCUMENT_POSITION_FOLLOWING", "correspondingDropList", "list", "console", "warn", "_scrollableParentsResolved", "scrollableParents", "getAncestorScrollContainers", "scrollable", "getElementRef", "listAutoScrollDisabled", "listOrientation", "CdkDropList_Factory", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "CdkDropList_HostBindings", "ɵɵattribute", "useValue", "CDK_DRAG_PREVIEW", "CdkDragPreview", "_drag", "optional", "CdkDragPreview_Factory", "TemplateRef", "CDK_DRAG_PLACEHOLDER", "CdkDragPlaceholder", "CdkDragPlaceholder_Factory", "DRAG_DROP_DIRECTIVES", "DragDropModule", "DragDropModule_Factory", "ɵmod", "ɵɵdefineNgModule", "imports", "exports", "ɵinj", "ɵɵdefineInjector"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/@angular/cdk/fesm2022/drag-drop.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, inject, ApplicationRef, EnvironmentInjector, createComponent, Injectable, Inject, InjectionToken, booleanAttribute, Directive, Optional, SkipSelf, Input, EventEmitter, Self, Output, NgModule } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\nimport * as i1 from '@angular/cdk/scrolling';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport { _getEventTarget, normalizePassiveListenerOptions, _getShadowRoot } from '@angular/cdk/platform';\nimport { coerceElement, coerceNumberProperty, coerceArray } from '@angular/cdk/coercion';\nimport { isFakeTouchstartFromScreenReader, isFakeMousedownFromScreenReader } from '@angular/cdk/a11y';\nimport { Subject, Subscription, interval, animationFrameScheduler, Observable, merge, BehaviorSubject } from 'rxjs';\nimport { takeUntil, map, take, tap, switchMap, startWith } from 'rxjs/operators';\nimport * as i1$1 from '@angular/cdk/bidi';\n\n/**\n * Shallow-extends a stylesheet object with another stylesheet-like object.\n * Note that the keys in `source` have to be dash-cased.\n * @docs-private\n */\nfunction extendStyles(dest, source, importantProperties) {\n    for (let key in source) {\n        if (source.hasOwnProperty(key)) {\n            const value = source[key];\n            if (value) {\n                dest.setProperty(key, value, importantProperties?.has(key) ? 'important' : '');\n            }\n            else {\n                dest.removeProperty(key);\n            }\n        }\n    }\n    return dest;\n}\n/**\n * Toggles whether the native drag interactions should be enabled for an element.\n * @param element Element on which to toggle the drag interactions.\n * @param enable Whether the drag interactions should be enabled.\n * @docs-private\n */\nfunction toggleNativeDragInteractions(element, enable) {\n    const userSelect = enable ? '' : 'none';\n    extendStyles(element.style, {\n        'touch-action': enable ? '' : 'none',\n        '-webkit-user-drag': enable ? '' : 'none',\n        '-webkit-tap-highlight-color': enable ? '' : 'transparent',\n        'user-select': userSelect,\n        '-ms-user-select': userSelect,\n        '-webkit-user-select': userSelect,\n        '-moz-user-select': userSelect,\n    });\n}\n/**\n * Toggles whether an element is visible while preserving its dimensions.\n * @param element Element whose visibility to toggle\n * @param enable Whether the element should be visible.\n * @param importantProperties Properties to be set as `!important`.\n * @docs-private\n */\nfunction toggleVisibility(element, enable, importantProperties) {\n    extendStyles(element.style, {\n        position: enable ? '' : 'fixed',\n        top: enable ? '' : '0',\n        opacity: enable ? '' : '0',\n        left: enable ? '' : '-999em',\n    }, importantProperties);\n}\n/**\n * Combines a transform string with an optional other transform\n * that exited before the base transform was applied.\n */\nfunction combineTransforms(transform, initialTransform) {\n    return initialTransform && initialTransform != 'none'\n        ? transform + ' ' + initialTransform\n        : transform;\n}\n/**\n * Matches the target element's size to the source's size.\n * @param target Element that needs to be resized.\n * @param sourceRect Dimensions of the source element.\n */\nfunction matchElementSize(target, sourceRect) {\n    target.style.width = `${sourceRect.width}px`;\n    target.style.height = `${sourceRect.height}px`;\n    target.style.transform = getTransform(sourceRect.left, sourceRect.top);\n}\n/**\n * Gets a 3d `transform` that can be applied to an element.\n * @param x Desired position of the element along the X axis.\n * @param y Desired position of the element along the Y axis.\n */\nfunction getTransform(x, y) {\n    // Round the transforms since some browsers will\n    // blur the elements for sub-pixel transforms.\n    return `translate3d(${Math.round(x)}px, ${Math.round(y)}px, 0)`;\n}\n\n/** Gets a mutable version of an element's bounding `DOMRect`. */\nfunction getMutableClientRect(element) {\n    const rect = element.getBoundingClientRect();\n    // We need to clone the `clientRect` here, because all the values on it are readonly\n    // and we need to be able to update them. Also we can't use a spread here, because\n    // the values on a `DOMRect` aren't own properties. See:\n    // https://developer.mozilla.org/en-US/docs/Web/API/Element/getBoundingClientRect#Notes\n    return {\n        top: rect.top,\n        right: rect.right,\n        bottom: rect.bottom,\n        left: rect.left,\n        width: rect.width,\n        height: rect.height,\n        x: rect.x,\n        y: rect.y,\n    };\n}\n/**\n * Checks whether some coordinates are within a `DOMRect`.\n * @param clientRect DOMRect that is being checked.\n * @param x Coordinates along the X axis.\n * @param y Coordinates along the Y axis.\n */\nfunction isInsideClientRect(clientRect, x, y) {\n    const { top, bottom, left, right } = clientRect;\n    return y >= top && y <= bottom && x >= left && x <= right;\n}\n/**\n * Updates the top/left positions of a `DOMRect`, as well as their bottom/right counterparts.\n * @param domRect `DOMRect` that should be updated.\n * @param top Amount to add to the `top` position.\n * @param left Amount to add to the `left` position.\n */\nfunction adjustDomRect(domRect, top, left) {\n    domRect.top += top;\n    domRect.bottom = domRect.top + domRect.height;\n    domRect.left += left;\n    domRect.right = domRect.left + domRect.width;\n}\n/**\n * Checks whether the pointer coordinates are close to a DOMRect.\n * @param rect DOMRect to check against.\n * @param threshold Threshold around the DOMRect.\n * @param pointerX Coordinates along the X axis.\n * @param pointerY Coordinates along the Y axis.\n */\nfunction isPointerNearDomRect(rect, threshold, pointerX, pointerY) {\n    const { top, right, bottom, left, width, height } = rect;\n    const xThreshold = width * threshold;\n    const yThreshold = height * threshold;\n    return (pointerY > top - yThreshold &&\n        pointerY < bottom + yThreshold &&\n        pointerX > left - xThreshold &&\n        pointerX < right + xThreshold);\n}\n\n/** Keeps track of the scroll position and dimensions of the parents of an element. */\nclass ParentPositionTracker {\n    constructor(_document) {\n        this._document = _document;\n        /** Cached positions of the scrollable parent elements. */\n        this.positions = new Map();\n    }\n    /** Clears the cached positions. */\n    clear() {\n        this.positions.clear();\n    }\n    /** Caches the positions. Should be called at the beginning of a drag sequence. */\n    cache(elements) {\n        this.clear();\n        this.positions.set(this._document, {\n            scrollPosition: this.getViewportScrollPosition(),\n        });\n        elements.forEach(element => {\n            this.positions.set(element, {\n                scrollPosition: { top: element.scrollTop, left: element.scrollLeft },\n                clientRect: getMutableClientRect(element),\n            });\n        });\n    }\n    /** Handles scrolling while a drag is taking place. */\n    handleScroll(event) {\n        const target = _getEventTarget(event);\n        const cachedPosition = this.positions.get(target);\n        if (!cachedPosition) {\n            return null;\n        }\n        const scrollPosition = cachedPosition.scrollPosition;\n        let newTop;\n        let newLeft;\n        if (target === this._document) {\n            const viewportScrollPosition = this.getViewportScrollPosition();\n            newTop = viewportScrollPosition.top;\n            newLeft = viewportScrollPosition.left;\n        }\n        else {\n            newTop = target.scrollTop;\n            newLeft = target.scrollLeft;\n        }\n        const topDifference = scrollPosition.top - newTop;\n        const leftDifference = scrollPosition.left - newLeft;\n        // Go through and update the cached positions of the scroll\n        // parents that are inside the element that was scrolled.\n        this.positions.forEach((position, node) => {\n            if (position.clientRect && target !== node && target.contains(node)) {\n                adjustDomRect(position.clientRect, topDifference, leftDifference);\n            }\n        });\n        scrollPosition.top = newTop;\n        scrollPosition.left = newLeft;\n        return { top: topDifference, left: leftDifference };\n    }\n    /**\n     * Gets the scroll position of the viewport. Note that we use the scrollX and scrollY directly,\n     * instead of going through the `ViewportRuler`, because the first value the ruler looks at is\n     * the top/left offset of the `document.documentElement` which works for most cases, but breaks\n     * if the element is offset by something like the `BlockScrollStrategy`.\n     */\n    getViewportScrollPosition() {\n        return { top: window.scrollY, left: window.scrollX };\n    }\n}\n\n/** Creates a deep clone of an element. */\nfunction deepCloneNode(node) {\n    const clone = node.cloneNode(true);\n    const descendantsWithId = clone.querySelectorAll('[id]');\n    const nodeName = node.nodeName.toLowerCase();\n    // Remove the `id` to avoid having multiple elements with the same id on the page.\n    clone.removeAttribute('id');\n    for (let i = 0; i < descendantsWithId.length; i++) {\n        descendantsWithId[i].removeAttribute('id');\n    }\n    if (nodeName === 'canvas') {\n        transferCanvasData(node, clone);\n    }\n    else if (nodeName === 'input' || nodeName === 'select' || nodeName === 'textarea') {\n        transferInputData(node, clone);\n    }\n    transferData('canvas', node, clone, transferCanvasData);\n    transferData('input, textarea, select', node, clone, transferInputData);\n    return clone;\n}\n/** Matches elements between an element and its clone and allows for their data to be cloned. */\nfunction transferData(selector, node, clone, callback) {\n    const descendantElements = node.querySelectorAll(selector);\n    if (descendantElements.length) {\n        const cloneElements = clone.querySelectorAll(selector);\n        for (let i = 0; i < descendantElements.length; i++) {\n            callback(descendantElements[i], cloneElements[i]);\n        }\n    }\n}\n// Counter for unique cloned radio button names.\nlet cloneUniqueId = 0;\n/** Transfers the data of one input element to another. */\nfunction transferInputData(source, clone) {\n    // Browsers throw an error when assigning the value of a file input programmatically.\n    if (clone.type !== 'file') {\n        clone.value = source.value;\n    }\n    // Radio button `name` attributes must be unique for radio button groups\n    // otherwise original radio buttons can lose their checked state\n    // once the clone is inserted in the DOM.\n    if (clone.type === 'radio' && clone.name) {\n        clone.name = `mat-clone-${clone.name}-${cloneUniqueId++}`;\n    }\n}\n/** Transfers the data of one canvas element to another. */\nfunction transferCanvasData(source, clone) {\n    const context = clone.getContext('2d');\n    if (context) {\n        // In some cases `drawImage` can throw (e.g. if the canvas size is 0x0).\n        // We can't do much about it so just ignore the error.\n        try {\n            context.drawImage(source, 0, 0);\n        }\n        catch { }\n    }\n}\n\n/**\n * Gets the root HTML element of an embedded view.\n * If the root is not an HTML element it gets wrapped in one.\n */\nfunction getRootNode(viewRef, _document) {\n    const rootNodes = viewRef.rootNodes;\n    if (rootNodes.length === 1 && rootNodes[0].nodeType === _document.ELEMENT_NODE) {\n        return rootNodes[0];\n    }\n    const wrapper = _document.createElement('div');\n    rootNodes.forEach(node => wrapper.appendChild(node));\n    return wrapper;\n}\n\n/** Parses a CSS time value to milliseconds. */\nfunction parseCssTimeUnitsToMs(value) {\n    // Some browsers will return it in seconds, whereas others will return milliseconds.\n    const multiplier = value.toLowerCase().indexOf('ms') > -1 ? 1 : 1000;\n    return parseFloat(value) * multiplier;\n}\n/** Gets the transform transition duration, including the delay, of an element in milliseconds. */\nfunction getTransformTransitionDurationInMs(element) {\n    const computedStyle = getComputedStyle(element);\n    const transitionedProperties = parseCssPropertyValue(computedStyle, 'transition-property');\n    const property = transitionedProperties.find(prop => prop === 'transform' || prop === 'all');\n    // If there's no transition for `all` or `transform`, we shouldn't do anything.\n    if (!property) {\n        return 0;\n    }\n    // Get the index of the property that we're interested in and match\n    // it up to the same index in `transition-delay` and `transition-duration`.\n    const propertyIndex = transitionedProperties.indexOf(property);\n    const rawDurations = parseCssPropertyValue(computedStyle, 'transition-duration');\n    const rawDelays = parseCssPropertyValue(computedStyle, 'transition-delay');\n    return (parseCssTimeUnitsToMs(rawDurations[propertyIndex]) +\n        parseCssTimeUnitsToMs(rawDelays[propertyIndex]));\n}\n/** Parses out multiple values from a computed style into an array. */\nfunction parseCssPropertyValue(computedStyle, name) {\n    const value = computedStyle.getPropertyValue(name);\n    return value.split(',').map(part => part.trim());\n}\n\n/** Inline styles to be set as `!important` while dragging. */\nconst importantProperties = new Set([\n    // Needs to be important, because some `mat-table` sets `position: sticky !important`. See #22781.\n    'position',\n]);\nclass PreviewRef {\n    constructor(_document, _rootElement, _direction, _initialDomRect, _previewTemplate, _previewClass, _pickupPositionOnPage, _initialTransform, _zIndex) {\n        this._document = _document;\n        this._rootElement = _rootElement;\n        this._direction = _direction;\n        this._initialDomRect = _initialDomRect;\n        this._previewTemplate = _previewTemplate;\n        this._previewClass = _previewClass;\n        this._pickupPositionOnPage = _pickupPositionOnPage;\n        this._initialTransform = _initialTransform;\n        this._zIndex = _zIndex;\n    }\n    attach(parent) {\n        this._preview = this._createPreview();\n        parent.appendChild(this._preview);\n        // The null check is necessary for browsers that don't support the popover API.\n        // Note that we use a string access for compatibility with Closure.\n        if ('showPopover' in this._preview) {\n            this._preview['showPopover']();\n        }\n    }\n    destroy() {\n        this._preview.remove();\n        this._previewEmbeddedView?.destroy();\n        this._preview = this._previewEmbeddedView = null;\n    }\n    setTransform(value) {\n        this._preview.style.transform = value;\n    }\n    getBoundingClientRect() {\n        return this._preview.getBoundingClientRect();\n    }\n    addClass(className) {\n        this._preview.classList.add(className);\n    }\n    getTransitionDuration() {\n        return getTransformTransitionDurationInMs(this._preview);\n    }\n    addEventListener(name, handler) {\n        this._preview.addEventListener(name, handler);\n    }\n    removeEventListener(name, handler) {\n        this._preview.removeEventListener(name, handler);\n    }\n    _createPreview() {\n        const previewConfig = this._previewTemplate;\n        const previewClass = this._previewClass;\n        const previewTemplate = previewConfig ? previewConfig.template : null;\n        let preview;\n        if (previewTemplate && previewConfig) {\n            // Measure the element before we've inserted the preview\n            // since the insertion could throw off the measurement.\n            const rootRect = previewConfig.matchSize ? this._initialDomRect : null;\n            const viewRef = previewConfig.viewContainer.createEmbeddedView(previewTemplate, previewConfig.context);\n            viewRef.detectChanges();\n            preview = getRootNode(viewRef, this._document);\n            this._previewEmbeddedView = viewRef;\n            if (previewConfig.matchSize) {\n                matchElementSize(preview, rootRect);\n            }\n            else {\n                preview.style.transform = getTransform(this._pickupPositionOnPage.x, this._pickupPositionOnPage.y);\n            }\n        }\n        else {\n            preview = deepCloneNode(this._rootElement);\n            matchElementSize(preview, this._initialDomRect);\n            if (this._initialTransform) {\n                preview.style.transform = this._initialTransform;\n            }\n        }\n        extendStyles(preview.style, {\n            // It's important that we disable the pointer events on the preview, because\n            // it can throw off the `document.elementFromPoint` calls in the `CdkDropList`.\n            'pointer-events': 'none',\n            // We have to reset the margin, because it can throw off positioning relative to the viewport.\n            'margin': '0',\n            'position': 'fixed',\n            'top': '0',\n            'left': '0',\n            'z-index': this._zIndex + '',\n        }, importantProperties);\n        toggleNativeDragInteractions(preview, false);\n        preview.classList.add('cdk-drag-preview');\n        preview.setAttribute('popover', 'manual');\n        preview.setAttribute('dir', this._direction);\n        if (previewClass) {\n            if (Array.isArray(previewClass)) {\n                previewClass.forEach(className => preview.classList.add(className));\n            }\n            else {\n                preview.classList.add(previewClass);\n            }\n        }\n        return preview;\n    }\n}\n\n/** Options that can be used to bind a passive event listener. */\nconst passiveEventListenerOptions = normalizePassiveListenerOptions({ passive: true });\n/** Options that can be used to bind an active event listener. */\nconst activeEventListenerOptions = normalizePassiveListenerOptions({ passive: false });\n/** Event options that can be used to bind an active, capturing event. */\nconst activeCapturingEventOptions$1 = normalizePassiveListenerOptions({\n    passive: false,\n    capture: true,\n});\n/**\n * Time in milliseconds for which to ignore mouse events, after\n * receiving a touch event. Used to avoid doing double work for\n * touch devices where the browser fires fake mouse events, in\n * addition to touch events.\n */\nconst MOUSE_EVENT_IGNORE_TIME = 800;\n/** Inline styles to be set as `!important` while dragging. */\nconst dragImportantProperties = new Set([\n    // Needs to be important, because some `mat-table` sets `position: sticky !important`. See #22781.\n    'position',\n]);\n/**\n * Reference to a draggable item. Used to manipulate or dispose of the item.\n */\nclass DragRef {\n    /** Whether starting to drag this element is disabled. */\n    get disabled() {\n        return this._disabled || !!(this._dropContainer && this._dropContainer.disabled);\n    }\n    set disabled(value) {\n        if (value !== this._disabled) {\n            this._disabled = value;\n            this._toggleNativeDragInteractions();\n            this._handles.forEach(handle => toggleNativeDragInteractions(handle, value));\n        }\n    }\n    constructor(element, _config, _document, _ngZone, _viewportRuler, _dragDropRegistry) {\n        this._config = _config;\n        this._document = _document;\n        this._ngZone = _ngZone;\n        this._viewportRuler = _viewportRuler;\n        this._dragDropRegistry = _dragDropRegistry;\n        /**\n         * CSS `transform` applied to the element when it isn't being dragged. We need a\n         * passive transform in order for the dragged element to retain its new position\n         * after the user has stopped dragging and because we need to know the relative\n         * position in case they start dragging again. This corresponds to `element.style.transform`.\n         */\n        this._passiveTransform = { x: 0, y: 0 };\n        /** CSS `transform` that is applied to the element while it's being dragged. */\n        this._activeTransform = { x: 0, y: 0 };\n        /**\n         * Whether the dragging sequence has been started. Doesn't\n         * necessarily mean that the element has been moved.\n         */\n        this._hasStartedDragging = false;\n        /** Emits when the item is being moved. */\n        this._moveEvents = new Subject();\n        /** Subscription to pointer movement events. */\n        this._pointerMoveSubscription = Subscription.EMPTY;\n        /** Subscription to the event that is dispatched when the user lifts their pointer. */\n        this._pointerUpSubscription = Subscription.EMPTY;\n        /** Subscription to the viewport being scrolled. */\n        this._scrollSubscription = Subscription.EMPTY;\n        /** Subscription to the viewport being resized. */\n        this._resizeSubscription = Subscription.EMPTY;\n        /** Cached reference to the boundary element. */\n        this._boundaryElement = null;\n        /** Whether the native dragging interactions have been enabled on the root element. */\n        this._nativeInteractionsEnabled = true;\n        /** Elements that can be used to drag the draggable item. */\n        this._handles = [];\n        /** Registered handles that are currently disabled. */\n        this._disabledHandles = new Set();\n        /** Layout direction of the item. */\n        this._direction = 'ltr';\n        /**\n         * Amount of milliseconds to wait after the user has put their\n         * pointer down before starting to drag the element.\n         */\n        this.dragStartDelay = 0;\n        this._disabled = false;\n        /** Emits as the drag sequence is being prepared. */\n        this.beforeStarted = new Subject();\n        /** Emits when the user starts dragging the item. */\n        this.started = new Subject();\n        /** Emits when the user has released a drag item, before any animations have started. */\n        this.released = new Subject();\n        /** Emits when the user stops dragging an item in the container. */\n        this.ended = new Subject();\n        /** Emits when the user has moved the item into a new container. */\n        this.entered = new Subject();\n        /** Emits when the user removes the item its container by dragging it into another container. */\n        this.exited = new Subject();\n        /** Emits when the user drops the item inside a container. */\n        this.dropped = new Subject();\n        /**\n         * Emits as the user is dragging the item. Use with caution,\n         * because this event will fire for every pixel that the user has dragged.\n         */\n        this.moved = this._moveEvents;\n        /** Handler for the `mousedown`/`touchstart` events. */\n        this._pointerDown = (event) => {\n            this.beforeStarted.next();\n            // Delegate the event based on whether it started from a handle or the element itself.\n            if (this._handles.length) {\n                const targetHandle = this._getTargetHandle(event);\n                if (targetHandle && !this._disabledHandles.has(targetHandle) && !this.disabled) {\n                    this._initializeDragSequence(targetHandle, event);\n                }\n            }\n            else if (!this.disabled) {\n                this._initializeDragSequence(this._rootElement, event);\n            }\n        };\n        /** Handler that is invoked when the user moves their pointer after they've initiated a drag. */\n        this._pointerMove = (event) => {\n            const pointerPosition = this._getPointerPositionOnPage(event);\n            if (!this._hasStartedDragging) {\n                const distanceX = Math.abs(pointerPosition.x - this._pickupPositionOnPage.x);\n                const distanceY = Math.abs(pointerPosition.y - this._pickupPositionOnPage.y);\n                const isOverThreshold = distanceX + distanceY >= this._config.dragStartThreshold;\n                // Only start dragging after the user has moved more than the minimum distance in either\n                // direction. Note that this is preferable over doing something like `skip(minimumDistance)`\n                // in the `pointerMove` subscription, because we're not guaranteed to have one move event\n                // per pixel of movement (e.g. if the user moves their pointer quickly).\n                if (isOverThreshold) {\n                    const isDelayElapsed = Date.now() >= this._dragStartTime + this._getDragStartDelay(event);\n                    const container = this._dropContainer;\n                    if (!isDelayElapsed) {\n                        this._endDragSequence(event);\n                        return;\n                    }\n                    // Prevent other drag sequences from starting while something in the container is still\n                    // being dragged. This can happen while we're waiting for the drop animation to finish\n                    // and can cause errors, because some elements might still be moving around.\n                    if (!container || (!container.isDragging() && !container.isReceiving())) {\n                        // Prevent the default action as soon as the dragging sequence is considered as\n                        // \"started\" since waiting for the next event can allow the device to begin scrolling.\n                        if (event.cancelable) {\n                            event.preventDefault();\n                        }\n                        this._hasStartedDragging = true;\n                        this._ngZone.run(() => this._startDragSequence(event));\n                    }\n                }\n                return;\n            }\n            // We prevent the default action down here so that we know that dragging has started. This is\n            // important for touch devices where doing this too early can unnecessarily block scrolling,\n            // if there's a dragging delay.\n            if (event.cancelable) {\n                event.preventDefault();\n            }\n            const constrainedPointerPosition = this._getConstrainedPointerPosition(pointerPosition);\n            this._hasMoved = true;\n            this._lastKnownPointerPosition = pointerPosition;\n            this._updatePointerDirectionDelta(constrainedPointerPosition);\n            if (this._dropContainer) {\n                this._updateActiveDropContainer(constrainedPointerPosition, pointerPosition);\n            }\n            else {\n                // If there's a position constraint function, we want the element's top/left to be at the\n                // specific position on the page. Use the initial position as a reference if that's the case.\n                const offset = this.constrainPosition ? this._initialDomRect : this._pickupPositionOnPage;\n                const activeTransform = this._activeTransform;\n                activeTransform.x = constrainedPointerPosition.x - offset.x + this._passiveTransform.x;\n                activeTransform.y = constrainedPointerPosition.y - offset.y + this._passiveTransform.y;\n                this._applyRootElementTransform(activeTransform.x, activeTransform.y);\n            }\n            // Since this event gets fired for every pixel while dragging, we only\n            // want to fire it if the consumer opted into it. Also we have to\n            // re-enter the zone because we run all of the events on the outside.\n            if (this._moveEvents.observers.length) {\n                this._ngZone.run(() => {\n                    this._moveEvents.next({\n                        source: this,\n                        pointerPosition: constrainedPointerPosition,\n                        event,\n                        distance: this._getDragDistance(constrainedPointerPosition),\n                        delta: this._pointerDirectionDelta,\n                    });\n                });\n            }\n        };\n        /** Handler that is invoked when the user lifts their pointer up, after initiating a drag. */\n        this._pointerUp = (event) => {\n            this._endDragSequence(event);\n        };\n        /** Handles a native `dragstart` event. */\n        this._nativeDragStart = (event) => {\n            if (this._handles.length) {\n                const targetHandle = this._getTargetHandle(event);\n                if (targetHandle && !this._disabledHandles.has(targetHandle) && !this.disabled) {\n                    event.preventDefault();\n                }\n            }\n            else if (!this.disabled) {\n                // Usually this isn't necessary since the we prevent the default action in `pointerDown`,\n                // but some cases like dragging of links can slip through (see #24403).\n                event.preventDefault();\n            }\n        };\n        this.withRootElement(element).withParent(_config.parentDragRef || null);\n        this._parentPositions = new ParentPositionTracker(_document);\n        _dragDropRegistry.registerDragItem(this);\n    }\n    /**\n     * Returns the element that is being used as a placeholder\n     * while the current element is being dragged.\n     */\n    getPlaceholderElement() {\n        return this._placeholder;\n    }\n    /** Returns the root draggable element. */\n    getRootElement() {\n        return this._rootElement;\n    }\n    /**\n     * Gets the currently-visible element that represents the drag item.\n     * While dragging this is the placeholder, otherwise it's the root element.\n     */\n    getVisibleElement() {\n        return this.isDragging() ? this.getPlaceholderElement() : this.getRootElement();\n    }\n    /** Registers the handles that can be used to drag the element. */\n    withHandles(handles) {\n        this._handles = handles.map(handle => coerceElement(handle));\n        this._handles.forEach(handle => toggleNativeDragInteractions(handle, this.disabled));\n        this._toggleNativeDragInteractions();\n        // Delete any lingering disabled handles that may have been destroyed. Note that we re-create\n        // the set, rather than iterate over it and filter out the destroyed handles, because while\n        // the ES spec allows for sets to be modified while they're being iterated over, some polyfills\n        // use an array internally which may throw an error.\n        const disabledHandles = new Set();\n        this._disabledHandles.forEach(handle => {\n            if (this._handles.indexOf(handle) > -1) {\n                disabledHandles.add(handle);\n            }\n        });\n        this._disabledHandles = disabledHandles;\n        return this;\n    }\n    /**\n     * Registers the template that should be used for the drag preview.\n     * @param template Template that from which to stamp out the preview.\n     */\n    withPreviewTemplate(template) {\n        this._previewTemplate = template;\n        return this;\n    }\n    /**\n     * Registers the template that should be used for the drag placeholder.\n     * @param template Template that from which to stamp out the placeholder.\n     */\n    withPlaceholderTemplate(template) {\n        this._placeholderTemplate = template;\n        return this;\n    }\n    /**\n     * Sets an alternate drag root element. The root element is the element that will be moved as\n     * the user is dragging. Passing an alternate root element is useful when trying to enable\n     * dragging on an element that you might not have access to.\n     */\n    withRootElement(rootElement) {\n        const element = coerceElement(rootElement);\n        if (element !== this._rootElement) {\n            if (this._rootElement) {\n                this._removeRootElementListeners(this._rootElement);\n            }\n            this._ngZone.runOutsideAngular(() => {\n                element.addEventListener('mousedown', this._pointerDown, activeEventListenerOptions);\n                element.addEventListener('touchstart', this._pointerDown, passiveEventListenerOptions);\n                element.addEventListener('dragstart', this._nativeDragStart, activeEventListenerOptions);\n            });\n            this._initialTransform = undefined;\n            this._rootElement = element;\n        }\n        if (typeof SVGElement !== 'undefined' && this._rootElement instanceof SVGElement) {\n            this._ownerSVGElement = this._rootElement.ownerSVGElement;\n        }\n        return this;\n    }\n    /**\n     * Element to which the draggable's position will be constrained.\n     */\n    withBoundaryElement(boundaryElement) {\n        this._boundaryElement = boundaryElement ? coerceElement(boundaryElement) : null;\n        this._resizeSubscription.unsubscribe();\n        if (boundaryElement) {\n            this._resizeSubscription = this._viewportRuler\n                .change(10)\n                .subscribe(() => this._containInsideBoundaryOnResize());\n        }\n        return this;\n    }\n    /** Sets the parent ref that the ref is nested in.  */\n    withParent(parent) {\n        this._parentDragRef = parent;\n        return this;\n    }\n    /** Removes the dragging functionality from the DOM element. */\n    dispose() {\n        this._removeRootElementListeners(this._rootElement);\n        // Do this check before removing from the registry since it'll\n        // stop being considered as dragged once it is removed.\n        if (this.isDragging()) {\n            // Since we move out the element to the end of the body while it's being\n            // dragged, we have to make sure that it's removed if it gets destroyed.\n            this._rootElement?.remove();\n        }\n        this._anchor?.remove();\n        this._destroyPreview();\n        this._destroyPlaceholder();\n        this._dragDropRegistry.removeDragItem(this);\n        this._removeListeners();\n        this.beforeStarted.complete();\n        this.started.complete();\n        this.released.complete();\n        this.ended.complete();\n        this.entered.complete();\n        this.exited.complete();\n        this.dropped.complete();\n        this._moveEvents.complete();\n        this._handles = [];\n        this._disabledHandles.clear();\n        this._dropContainer = undefined;\n        this._resizeSubscription.unsubscribe();\n        this._parentPositions.clear();\n        this._boundaryElement =\n            this._rootElement =\n                this._ownerSVGElement =\n                    this._placeholderTemplate =\n                        this._previewTemplate =\n                            this._anchor =\n                                this._parentDragRef =\n                                    null;\n    }\n    /** Checks whether the element is currently being dragged. */\n    isDragging() {\n        return this._hasStartedDragging && this._dragDropRegistry.isDragging(this);\n    }\n    /** Resets a standalone drag item to its initial position. */\n    reset() {\n        this._rootElement.style.transform = this._initialTransform || '';\n        this._activeTransform = { x: 0, y: 0 };\n        this._passiveTransform = { x: 0, y: 0 };\n    }\n    /**\n     * Sets a handle as disabled. While a handle is disabled, it'll capture and interrupt dragging.\n     * @param handle Handle element that should be disabled.\n     */\n    disableHandle(handle) {\n        if (!this._disabledHandles.has(handle) && this._handles.indexOf(handle) > -1) {\n            this._disabledHandles.add(handle);\n            toggleNativeDragInteractions(handle, true);\n        }\n    }\n    /**\n     * Enables a handle, if it has been disabled.\n     * @param handle Handle element to be enabled.\n     */\n    enableHandle(handle) {\n        if (this._disabledHandles.has(handle)) {\n            this._disabledHandles.delete(handle);\n            toggleNativeDragInteractions(handle, this.disabled);\n        }\n    }\n    /** Sets the layout direction of the draggable item. */\n    withDirection(direction) {\n        this._direction = direction;\n        return this;\n    }\n    /** Sets the container that the item is part of. */\n    _withDropContainer(container) {\n        this._dropContainer = container;\n    }\n    /**\n     * Gets the current position in pixels the draggable outside of a drop container.\n     */\n    getFreeDragPosition() {\n        const position = this.isDragging() ? this._activeTransform : this._passiveTransform;\n        return { x: position.x, y: position.y };\n    }\n    /**\n     * Sets the current position in pixels the draggable outside of a drop container.\n     * @param value New position to be set.\n     */\n    setFreeDragPosition(value) {\n        this._activeTransform = { x: 0, y: 0 };\n        this._passiveTransform.x = value.x;\n        this._passiveTransform.y = value.y;\n        if (!this._dropContainer) {\n            this._applyRootElementTransform(value.x, value.y);\n        }\n        return this;\n    }\n    /**\n     * Sets the container into which to insert the preview element.\n     * @param value Container into which to insert the preview.\n     */\n    withPreviewContainer(value) {\n        this._previewContainer = value;\n        return this;\n    }\n    /** Updates the item's sort order based on the last-known pointer position. */\n    _sortFromLastPointerPosition() {\n        const position = this._lastKnownPointerPosition;\n        if (position && this._dropContainer) {\n            this._updateActiveDropContainer(this._getConstrainedPointerPosition(position), position);\n        }\n    }\n    /** Unsubscribes from the global subscriptions. */\n    _removeListeners() {\n        this._pointerMoveSubscription.unsubscribe();\n        this._pointerUpSubscription.unsubscribe();\n        this._scrollSubscription.unsubscribe();\n        this._getShadowRoot()?.removeEventListener('selectstart', shadowDomSelectStart, activeCapturingEventOptions$1);\n    }\n    /** Destroys the preview element and its ViewRef. */\n    _destroyPreview() {\n        this._preview?.destroy();\n        this._preview = null;\n    }\n    /** Destroys the placeholder element and its ViewRef. */\n    _destroyPlaceholder() {\n        this._placeholder?.remove();\n        this._placeholderRef?.destroy();\n        this._placeholder = this._placeholderRef = null;\n    }\n    /**\n     * Clears subscriptions and stops the dragging sequence.\n     * @param event Browser event object that ended the sequence.\n     */\n    _endDragSequence(event) {\n        // Note that here we use `isDragging` from the service, rather than from `this`.\n        // The difference is that the one from the service reflects whether a dragging sequence\n        // has been initiated, whereas the one on `this` includes whether the user has passed\n        // the minimum dragging threshold.\n        if (!this._dragDropRegistry.isDragging(this)) {\n            return;\n        }\n        this._removeListeners();\n        this._dragDropRegistry.stopDragging(this);\n        this._toggleNativeDragInteractions();\n        if (this._handles) {\n            this._rootElement.style.webkitTapHighlightColor =\n                this._rootElementTapHighlight;\n        }\n        if (!this._hasStartedDragging) {\n            return;\n        }\n        this.released.next({ source: this, event });\n        if (this._dropContainer) {\n            // Stop scrolling immediately, instead of waiting for the animation to finish.\n            this._dropContainer._stopScrolling();\n            this._animatePreviewToPlaceholder().then(() => {\n                this._cleanupDragArtifacts(event);\n                this._cleanupCachedDimensions();\n                this._dragDropRegistry.stopDragging(this);\n            });\n        }\n        else {\n            // Convert the active transform into a passive one. This means that next time\n            // the user starts dragging the item, its position will be calculated relatively\n            // to the new passive transform.\n            this._passiveTransform.x = this._activeTransform.x;\n            const pointerPosition = this._getPointerPositionOnPage(event);\n            this._passiveTransform.y = this._activeTransform.y;\n            this._ngZone.run(() => {\n                this.ended.next({\n                    source: this,\n                    distance: this._getDragDistance(pointerPosition),\n                    dropPoint: pointerPosition,\n                    event,\n                });\n            });\n            this._cleanupCachedDimensions();\n            this._dragDropRegistry.stopDragging(this);\n        }\n    }\n    /** Starts the dragging sequence. */\n    _startDragSequence(event) {\n        if (isTouchEvent(event)) {\n            this._lastTouchEventTime = Date.now();\n        }\n        this._toggleNativeDragInteractions();\n        // Needs to happen before the root element is moved.\n        const shadowRoot = this._getShadowRoot();\n        const dropContainer = this._dropContainer;\n        if (shadowRoot) {\n            // In some browsers the global `selectstart` that we maintain in the `DragDropRegistry`\n            // doesn't cross the shadow boundary so we have to prevent it at the shadow root (see #28792).\n            this._ngZone.runOutsideAngular(() => {\n                shadowRoot.addEventListener('selectstart', shadowDomSelectStart, activeCapturingEventOptions$1);\n            });\n        }\n        if (dropContainer) {\n            const element = this._rootElement;\n            const parent = element.parentNode;\n            const placeholder = (this._placeholder = this._createPlaceholderElement());\n            const anchor = (this._anchor = this._anchor || this._document.createComment(''));\n            // Insert an anchor node so that we can restore the element's position in the DOM.\n            parent.insertBefore(anchor, element);\n            // There's no risk of transforms stacking when inside a drop container so\n            // we can keep the initial transform up to date any time dragging starts.\n            this._initialTransform = element.style.transform || '';\n            // Create the preview after the initial transform has\n            // been cached, because it can be affected by the transform.\n            this._preview = new PreviewRef(this._document, this._rootElement, this._direction, this._initialDomRect, this._previewTemplate || null, this.previewClass || null, this._pickupPositionOnPage, this._initialTransform, this._config.zIndex || 1000);\n            this._preview.attach(this._getPreviewInsertionPoint(parent, shadowRoot));\n            // We move the element out at the end of the body and we make it hidden, because keeping it in\n            // place will throw off the consumer's `:last-child` selectors. We can't remove the element\n            // from the DOM completely, because iOS will stop firing all subsequent events in the chain.\n            toggleVisibility(element, false, dragImportantProperties);\n            this._document.body.appendChild(parent.replaceChild(placeholder, element));\n            this.started.next({ source: this, event }); // Emit before notifying the container.\n            dropContainer.start();\n            this._initialContainer = dropContainer;\n            this._initialIndex = dropContainer.getItemIndex(this);\n        }\n        else {\n            this.started.next({ source: this, event });\n            this._initialContainer = this._initialIndex = undefined;\n        }\n        // Important to run after we've called `start` on the parent container\n        // so that it has had time to resolve its scrollable parents.\n        this._parentPositions.cache(dropContainer ? dropContainer.getScrollableParents() : []);\n    }\n    /**\n     * Sets up the different variables and subscriptions\n     * that will be necessary for the dragging sequence.\n     * @param referenceElement Element that started the drag sequence.\n     * @param event Browser event object that started the sequence.\n     */\n    _initializeDragSequence(referenceElement, event) {\n        // Stop propagation if the item is inside another\n        // draggable so we don't start multiple drag sequences.\n        if (this._parentDragRef) {\n            event.stopPropagation();\n        }\n        const isDragging = this.isDragging();\n        const isTouchSequence = isTouchEvent(event);\n        const isAuxiliaryMouseButton = !isTouchSequence && event.button !== 0;\n        const rootElement = this._rootElement;\n        const target = _getEventTarget(event);\n        const isSyntheticEvent = !isTouchSequence &&\n            this._lastTouchEventTime &&\n            this._lastTouchEventTime + MOUSE_EVENT_IGNORE_TIME > Date.now();\n        const isFakeEvent = isTouchSequence\n            ? isFakeTouchstartFromScreenReader(event)\n            : isFakeMousedownFromScreenReader(event);\n        // If the event started from an element with the native HTML drag&drop, it'll interfere\n        // with our own dragging (e.g. `img` tags do it by default). Prevent the default action\n        // to stop it from happening. Note that preventing on `dragstart` also seems to work, but\n        // it's flaky and it fails if the user drags it away quickly. Also note that we only want\n        // to do this for `mousedown` since doing the same for `touchstart` will stop any `click`\n        // events from firing on touch devices.\n        if (target && target.draggable && event.type === 'mousedown') {\n            event.preventDefault();\n        }\n        // Abort if the user is already dragging or is using a mouse button other than the primary one.\n        if (isDragging || isAuxiliaryMouseButton || isSyntheticEvent || isFakeEvent) {\n            return;\n        }\n        // If we've got handles, we need to disable the tap highlight on the entire root element,\n        // otherwise iOS will still add it, even though all the drag interactions on the handle\n        // are disabled.\n        if (this._handles.length) {\n            const rootStyles = rootElement.style;\n            this._rootElementTapHighlight = rootStyles.webkitTapHighlightColor || '';\n            rootStyles.webkitTapHighlightColor = 'transparent';\n        }\n        this._hasStartedDragging = this._hasMoved = false;\n        // Avoid multiple subscriptions and memory leaks when multi touch\n        // (isDragging check above isn't enough because of possible temporal and/or dimensional delays)\n        this._removeListeners();\n        this._initialDomRect = this._rootElement.getBoundingClientRect();\n        this._pointerMoveSubscription = this._dragDropRegistry.pointerMove.subscribe(this._pointerMove);\n        this._pointerUpSubscription = this._dragDropRegistry.pointerUp.subscribe(this._pointerUp);\n        this._scrollSubscription = this._dragDropRegistry\n            .scrolled(this._getShadowRoot())\n            .subscribe(scrollEvent => this._updateOnScroll(scrollEvent));\n        if (this._boundaryElement) {\n            this._boundaryRect = getMutableClientRect(this._boundaryElement);\n        }\n        // If we have a custom preview we can't know ahead of time how large it'll be so we position\n        // it next to the cursor. The exception is when the consumer has opted into making the preview\n        // the same size as the root element, in which case we do know the size.\n        const previewTemplate = this._previewTemplate;\n        this._pickupPositionInElement =\n            previewTemplate && previewTemplate.template && !previewTemplate.matchSize\n                ? { x: 0, y: 0 }\n                : this._getPointerPositionInElement(this._initialDomRect, referenceElement, event);\n        const pointerPosition = (this._pickupPositionOnPage =\n            this._lastKnownPointerPosition =\n                this._getPointerPositionOnPage(event));\n        this._pointerDirectionDelta = { x: 0, y: 0 };\n        this._pointerPositionAtLastDirectionChange = { x: pointerPosition.x, y: pointerPosition.y };\n        this._dragStartTime = Date.now();\n        this._dragDropRegistry.startDragging(this, event);\n    }\n    /** Cleans up the DOM artifacts that were added to facilitate the element being dragged. */\n    _cleanupDragArtifacts(event) {\n        // Restore the element's visibility and insert it at its old position in the DOM.\n        // It's important that we maintain the position, because moving the element around in the DOM\n        // can throw off `NgFor` which does smart diffing and re-creates elements only when necessary,\n        // while moving the existing elements in all other cases.\n        toggleVisibility(this._rootElement, true, dragImportantProperties);\n        this._anchor.parentNode.replaceChild(this._rootElement, this._anchor);\n        this._destroyPreview();\n        this._destroyPlaceholder();\n        this._initialDomRect =\n            this._boundaryRect =\n                this._previewRect =\n                    this._initialTransform =\n                        undefined;\n        // Re-enter the NgZone since we bound `document` events on the outside.\n        this._ngZone.run(() => {\n            const container = this._dropContainer;\n            const currentIndex = container.getItemIndex(this);\n            const pointerPosition = this._getPointerPositionOnPage(event);\n            const distance = this._getDragDistance(pointerPosition);\n            const isPointerOverContainer = container._isOverContainer(pointerPosition.x, pointerPosition.y);\n            this.ended.next({ source: this, distance, dropPoint: pointerPosition, event });\n            this.dropped.next({\n                item: this,\n                currentIndex,\n                previousIndex: this._initialIndex,\n                container: container,\n                previousContainer: this._initialContainer,\n                isPointerOverContainer,\n                distance,\n                dropPoint: pointerPosition,\n                event,\n            });\n            container.drop(this, currentIndex, this._initialIndex, this._initialContainer, isPointerOverContainer, distance, pointerPosition, event);\n            this._dropContainer = this._initialContainer;\n        });\n    }\n    /**\n     * Updates the item's position in its drop container, or moves it\n     * into a new one, depending on its current drag position.\n     */\n    _updateActiveDropContainer({ x, y }, { x: rawX, y: rawY }) {\n        // Drop container that draggable has been moved into.\n        let newContainer = this._initialContainer._getSiblingContainerFromPosition(this, x, y);\n        // If we couldn't find a new container to move the item into, and the item has left its\n        // initial container, check whether the it's over the initial container. This handles the\n        // case where two containers are connected one way and the user tries to undo dragging an\n        // item into a new container.\n        if (!newContainer &&\n            this._dropContainer !== this._initialContainer &&\n            this._initialContainer._isOverContainer(x, y)) {\n            newContainer = this._initialContainer;\n        }\n        if (newContainer && newContainer !== this._dropContainer) {\n            this._ngZone.run(() => {\n                // Notify the old container that the item has left.\n                this.exited.next({ item: this, container: this._dropContainer });\n                this._dropContainer.exit(this);\n                // Notify the new container that the item has entered.\n                this._dropContainer = newContainer;\n                this._dropContainer.enter(this, x, y, newContainer === this._initialContainer &&\n                    // If we're re-entering the initial container and sorting is disabled,\n                    // put item the into its starting index to begin with.\n                    newContainer.sortingDisabled\n                    ? this._initialIndex\n                    : undefined);\n                this.entered.next({\n                    item: this,\n                    container: newContainer,\n                    currentIndex: newContainer.getItemIndex(this),\n                });\n            });\n        }\n        // Dragging may have been interrupted as a result of the events above.\n        if (this.isDragging()) {\n            this._dropContainer._startScrollingIfNecessary(rawX, rawY);\n            this._dropContainer._sortItem(this, x, y, this._pointerDirectionDelta);\n            if (this.constrainPosition) {\n                this._applyPreviewTransform(x, y);\n            }\n            else {\n                this._applyPreviewTransform(x - this._pickupPositionInElement.x, y - this._pickupPositionInElement.y);\n            }\n        }\n    }\n    /**\n     * Animates the preview element from its current position to the location of the drop placeholder.\n     * @returns Promise that resolves when the animation completes.\n     */\n    _animatePreviewToPlaceholder() {\n        // If the user hasn't moved yet, the transitionend event won't fire.\n        if (!this._hasMoved) {\n            return Promise.resolve();\n        }\n        const placeholderRect = this._placeholder.getBoundingClientRect();\n        // Apply the class that adds a transition to the preview.\n        this._preview.addClass('cdk-drag-animating');\n        // Move the preview to the placeholder position.\n        this._applyPreviewTransform(placeholderRect.left, placeholderRect.top);\n        // If the element doesn't have a `transition`, the `transitionend` event won't fire. Since\n        // we need to trigger a style recalculation in order for the `cdk-drag-animating` class to\n        // apply its style, we take advantage of the available info to figure out whether we need to\n        // bind the event in the first place.\n        const duration = this._preview.getTransitionDuration();\n        if (duration === 0) {\n            return Promise.resolve();\n        }\n        return this._ngZone.runOutsideAngular(() => {\n            return new Promise(resolve => {\n                const handler = ((event) => {\n                    if (!event ||\n                        (_getEventTarget(event) === this._preview && event.propertyName === 'transform')) {\n                        this._preview?.removeEventListener('transitionend', handler);\n                        resolve();\n                        clearTimeout(timeout);\n                    }\n                });\n                // If a transition is short enough, the browser might not fire the `transitionend` event.\n                // Since we know how long it's supposed to take, add a timeout with a 50% buffer that'll\n                // fire if the transition hasn't completed when it was supposed to.\n                const timeout = setTimeout(handler, duration * 1.5);\n                this._preview.addEventListener('transitionend', handler);\n            });\n        });\n    }\n    /** Creates an element that will be shown instead of the current element while dragging. */\n    _createPlaceholderElement() {\n        const placeholderConfig = this._placeholderTemplate;\n        const placeholderTemplate = placeholderConfig ? placeholderConfig.template : null;\n        let placeholder;\n        if (placeholderTemplate) {\n            this._placeholderRef = placeholderConfig.viewContainer.createEmbeddedView(placeholderTemplate, placeholderConfig.context);\n            this._placeholderRef.detectChanges();\n            placeholder = getRootNode(this._placeholderRef, this._document);\n        }\n        else {\n            placeholder = deepCloneNode(this._rootElement);\n        }\n        // Stop pointer events on the preview so the user can't\n        // interact with it while the preview is animating.\n        placeholder.style.pointerEvents = 'none';\n        placeholder.classList.add('cdk-drag-placeholder');\n        return placeholder;\n    }\n    /**\n     * Figures out the coordinates at which an element was picked up.\n     * @param referenceElement Element that initiated the dragging.\n     * @param event Event that initiated the dragging.\n     */\n    _getPointerPositionInElement(elementRect, referenceElement, event) {\n        const handleElement = referenceElement === this._rootElement ? null : referenceElement;\n        const referenceRect = handleElement ? handleElement.getBoundingClientRect() : elementRect;\n        const point = isTouchEvent(event) ? event.targetTouches[0] : event;\n        const scrollPosition = this._getViewportScrollPosition();\n        const x = point.pageX - referenceRect.left - scrollPosition.left;\n        const y = point.pageY - referenceRect.top - scrollPosition.top;\n        return {\n            x: referenceRect.left - elementRect.left + x,\n            y: referenceRect.top - elementRect.top + y,\n        };\n    }\n    /** Determines the point of the page that was touched by the user. */\n    _getPointerPositionOnPage(event) {\n        const scrollPosition = this._getViewportScrollPosition();\n        const point = isTouchEvent(event)\n            ? // `touches` will be empty for start/end events so we have to fall back to `changedTouches`.\n                // Also note that on real devices we're guaranteed for either `touches` or `changedTouches`\n                // to have a value, but Firefox in device emulation mode has a bug where both can be empty\n                // for `touchstart` and `touchend` so we fall back to a dummy object in order to avoid\n                // throwing an error. The value returned here will be incorrect, but since this only\n                // breaks inside a developer tool and the value is only used for secondary information,\n                // we can get away with it. See https://bugzilla.mozilla.org/show_bug.cgi?id=1615824.\n                event.touches[0] || event.changedTouches[0] || { pageX: 0, pageY: 0 }\n            : event;\n        const x = point.pageX - scrollPosition.left;\n        const y = point.pageY - scrollPosition.top;\n        // if dragging SVG element, try to convert from the screen coordinate system to the SVG\n        // coordinate system\n        if (this._ownerSVGElement) {\n            const svgMatrix = this._ownerSVGElement.getScreenCTM();\n            if (svgMatrix) {\n                const svgPoint = this._ownerSVGElement.createSVGPoint();\n                svgPoint.x = x;\n                svgPoint.y = y;\n                return svgPoint.matrixTransform(svgMatrix.inverse());\n            }\n        }\n        return { x, y };\n    }\n    /** Gets the pointer position on the page, accounting for any position constraints. */\n    _getConstrainedPointerPosition(point) {\n        const dropContainerLock = this._dropContainer ? this._dropContainer.lockAxis : null;\n        let { x, y } = this.constrainPosition\n            ? this.constrainPosition(point, this, this._initialDomRect, this._pickupPositionInElement)\n            : point;\n        if (this.lockAxis === 'x' || dropContainerLock === 'x') {\n            y =\n                this._pickupPositionOnPage.y -\n                    (this.constrainPosition ? this._pickupPositionInElement.y : 0);\n        }\n        else if (this.lockAxis === 'y' || dropContainerLock === 'y') {\n            x =\n                this._pickupPositionOnPage.x -\n                    (this.constrainPosition ? this._pickupPositionInElement.x : 0);\n        }\n        if (this._boundaryRect) {\n            // If not using a custom constrain we need to account for the pickup position in the element\n            // otherwise we do not need to do this, as it has already been accounted for\n            const { x: pickupX, y: pickupY } = !this.constrainPosition\n                ? this._pickupPositionInElement\n                : { x: 0, y: 0 };\n            const boundaryRect = this._boundaryRect;\n            const { width: previewWidth, height: previewHeight } = this._getPreviewRect();\n            const minY = boundaryRect.top + pickupY;\n            const maxY = boundaryRect.bottom - (previewHeight - pickupY);\n            const minX = boundaryRect.left + pickupX;\n            const maxX = boundaryRect.right - (previewWidth - pickupX);\n            x = clamp$1(x, minX, maxX);\n            y = clamp$1(y, minY, maxY);\n        }\n        return { x, y };\n    }\n    /** Updates the current drag delta, based on the user's current pointer position on the page. */\n    _updatePointerDirectionDelta(pointerPositionOnPage) {\n        const { x, y } = pointerPositionOnPage;\n        const delta = this._pointerDirectionDelta;\n        const positionSinceLastChange = this._pointerPositionAtLastDirectionChange;\n        // Amount of pixels the user has dragged since the last time the direction changed.\n        const changeX = Math.abs(x - positionSinceLastChange.x);\n        const changeY = Math.abs(y - positionSinceLastChange.y);\n        // Because we handle pointer events on a per-pixel basis, we don't want the delta\n        // to change for every pixel, otherwise anything that depends on it can look erratic.\n        // To make the delta more consistent, we track how much the user has moved since the last\n        // delta change and we only update it after it has reached a certain threshold.\n        if (changeX > this._config.pointerDirectionChangeThreshold) {\n            delta.x = x > positionSinceLastChange.x ? 1 : -1;\n            positionSinceLastChange.x = x;\n        }\n        if (changeY > this._config.pointerDirectionChangeThreshold) {\n            delta.y = y > positionSinceLastChange.y ? 1 : -1;\n            positionSinceLastChange.y = y;\n        }\n        return delta;\n    }\n    /** Toggles the native drag interactions, based on how many handles are registered. */\n    _toggleNativeDragInteractions() {\n        if (!this._rootElement || !this._handles) {\n            return;\n        }\n        const shouldEnable = this._handles.length > 0 || !this.isDragging();\n        if (shouldEnable !== this._nativeInteractionsEnabled) {\n            this._nativeInteractionsEnabled = shouldEnable;\n            toggleNativeDragInteractions(this._rootElement, shouldEnable);\n        }\n    }\n    /** Removes the manually-added event listeners from the root element. */\n    _removeRootElementListeners(element) {\n        element.removeEventListener('mousedown', this._pointerDown, activeEventListenerOptions);\n        element.removeEventListener('touchstart', this._pointerDown, passiveEventListenerOptions);\n        element.removeEventListener('dragstart', this._nativeDragStart, activeEventListenerOptions);\n    }\n    /**\n     * Applies a `transform` to the root element, taking into account any existing transforms on it.\n     * @param x New transform value along the X axis.\n     * @param y New transform value along the Y axis.\n     */\n    _applyRootElementTransform(x, y) {\n        const transform = getTransform(x, y);\n        const styles = this._rootElement.style;\n        // Cache the previous transform amount only after the first drag sequence, because\n        // we don't want our own transforms to stack on top of each other.\n        // Should be excluded none because none + translate3d(x, y, x) is invalid css\n        if (this._initialTransform == null) {\n            this._initialTransform =\n                styles.transform && styles.transform != 'none' ? styles.transform : '';\n        }\n        // Preserve the previous `transform` value, if there was one. Note that we apply our own\n        // transform before the user's, because things like rotation can affect which direction\n        // the element will be translated towards.\n        styles.transform = combineTransforms(transform, this._initialTransform);\n    }\n    /**\n     * Applies a `transform` to the preview, taking into account any existing transforms on it.\n     * @param x New transform value along the X axis.\n     * @param y New transform value along the Y axis.\n     */\n    _applyPreviewTransform(x, y) {\n        // Only apply the initial transform if the preview is a clone of the original element, otherwise\n        // it could be completely different and the transform might not make sense anymore.\n        const initialTransform = this._previewTemplate?.template ? undefined : this._initialTransform;\n        const transform = getTransform(x, y);\n        this._preview.setTransform(combineTransforms(transform, initialTransform));\n    }\n    /**\n     * Gets the distance that the user has dragged during the current drag sequence.\n     * @param currentPosition Current position of the user's pointer.\n     */\n    _getDragDistance(currentPosition) {\n        const pickupPosition = this._pickupPositionOnPage;\n        if (pickupPosition) {\n            return { x: currentPosition.x - pickupPosition.x, y: currentPosition.y - pickupPosition.y };\n        }\n        return { x: 0, y: 0 };\n    }\n    /** Cleans up any cached element dimensions that we don't need after dragging has stopped. */\n    _cleanupCachedDimensions() {\n        this._boundaryRect = this._previewRect = undefined;\n        this._parentPositions.clear();\n    }\n    /**\n     * Checks whether the element is still inside its boundary after the viewport has been resized.\n     * If not, the position is adjusted so that the element fits again.\n     */\n    _containInsideBoundaryOnResize() {\n        let { x, y } = this._passiveTransform;\n        if ((x === 0 && y === 0) || this.isDragging() || !this._boundaryElement) {\n            return;\n        }\n        // Note: don't use `_clientRectAtStart` here, because we want the latest position.\n        const elementRect = this._rootElement.getBoundingClientRect();\n        const boundaryRect = this._boundaryElement.getBoundingClientRect();\n        // It's possible that the element got hidden away after dragging (e.g. by switching to a\n        // different tab). Don't do anything in this case so we don't clear the user's position.\n        if ((boundaryRect.width === 0 && boundaryRect.height === 0) ||\n            (elementRect.width === 0 && elementRect.height === 0)) {\n            return;\n        }\n        const leftOverflow = boundaryRect.left - elementRect.left;\n        const rightOverflow = elementRect.right - boundaryRect.right;\n        const topOverflow = boundaryRect.top - elementRect.top;\n        const bottomOverflow = elementRect.bottom - boundaryRect.bottom;\n        // If the element has become wider than the boundary, we can't\n        // do much to make it fit so we just anchor it to the left.\n        if (boundaryRect.width > elementRect.width) {\n            if (leftOverflow > 0) {\n                x += leftOverflow;\n            }\n            if (rightOverflow > 0) {\n                x -= rightOverflow;\n            }\n        }\n        else {\n            x = 0;\n        }\n        // If the element has become taller than the boundary, we can't\n        // do much to make it fit so we just anchor it to the top.\n        if (boundaryRect.height > elementRect.height) {\n            if (topOverflow > 0) {\n                y += topOverflow;\n            }\n            if (bottomOverflow > 0) {\n                y -= bottomOverflow;\n            }\n        }\n        else {\n            y = 0;\n        }\n        if (x !== this._passiveTransform.x || y !== this._passiveTransform.y) {\n            this.setFreeDragPosition({ y, x });\n        }\n    }\n    /** Gets the drag start delay, based on the event type. */\n    _getDragStartDelay(event) {\n        const value = this.dragStartDelay;\n        if (typeof value === 'number') {\n            return value;\n        }\n        else if (isTouchEvent(event)) {\n            return value.touch;\n        }\n        return value ? value.mouse : 0;\n    }\n    /** Updates the internal state of the draggable element when scrolling has occurred. */\n    _updateOnScroll(event) {\n        const scrollDifference = this._parentPositions.handleScroll(event);\n        if (scrollDifference) {\n            const target = _getEventTarget(event);\n            // DOMRect dimensions are based on the scroll position of the page and its parent\n            // node so we have to update the cached boundary DOMRect if the user has scrolled.\n            if (this._boundaryRect &&\n                target !== this._boundaryElement &&\n                target.contains(this._boundaryElement)) {\n                adjustDomRect(this._boundaryRect, scrollDifference.top, scrollDifference.left);\n            }\n            this._pickupPositionOnPage.x += scrollDifference.left;\n            this._pickupPositionOnPage.y += scrollDifference.top;\n            // If we're in free drag mode, we have to update the active transform, because\n            // it isn't relative to the viewport like the preview inside a drop list.\n            if (!this._dropContainer) {\n                this._activeTransform.x -= scrollDifference.left;\n                this._activeTransform.y -= scrollDifference.top;\n                this._applyRootElementTransform(this._activeTransform.x, this._activeTransform.y);\n            }\n        }\n    }\n    /** Gets the scroll position of the viewport. */\n    _getViewportScrollPosition() {\n        return (this._parentPositions.positions.get(this._document)?.scrollPosition ||\n            this._parentPositions.getViewportScrollPosition());\n    }\n    /**\n     * Lazily resolves and returns the shadow root of the element. We do this in a function, rather\n     * than saving it in property directly on init, because we want to resolve it as late as possible\n     * in order to ensure that the element has been moved into the shadow DOM. Doing it inside the\n     * constructor might be too early if the element is inside of something like `ngFor` or `ngIf`.\n     */\n    _getShadowRoot() {\n        if (this._cachedShadowRoot === undefined) {\n            this._cachedShadowRoot = _getShadowRoot(this._rootElement);\n        }\n        return this._cachedShadowRoot;\n    }\n    /** Gets the element into which the drag preview should be inserted. */\n    _getPreviewInsertionPoint(initialParent, shadowRoot) {\n        const previewContainer = this._previewContainer || 'global';\n        if (previewContainer === 'parent') {\n            return initialParent;\n        }\n        if (previewContainer === 'global') {\n            const documentRef = this._document;\n            // We can't use the body if the user is in fullscreen mode,\n            // because the preview will render under the fullscreen element.\n            // TODO(crisbeto): dedupe this with the `FullscreenOverlayContainer` eventually.\n            return (shadowRoot ||\n                documentRef.fullscreenElement ||\n                documentRef.webkitFullscreenElement ||\n                documentRef.mozFullScreenElement ||\n                documentRef.msFullscreenElement ||\n                documentRef.body);\n        }\n        return coerceElement(previewContainer);\n    }\n    /** Lazily resolves and returns the dimensions of the preview. */\n    _getPreviewRect() {\n        // Cache the preview element rect if we haven't cached it already or if\n        // we cached it too early before the element dimensions were computed.\n        if (!this._previewRect || (!this._previewRect.width && !this._previewRect.height)) {\n            this._previewRect = this._preview\n                ? this._preview.getBoundingClientRect()\n                : this._initialDomRect;\n        }\n        return this._previewRect;\n    }\n    /** Gets a handle that is the target of an event. */\n    _getTargetHandle(event) {\n        return this._handles.find(handle => {\n            return event.target && (event.target === handle || handle.contains(event.target));\n        });\n    }\n}\n/** Clamps a value between a minimum and a maximum. */\nfunction clamp$1(value, min, max) {\n    return Math.max(min, Math.min(max, value));\n}\n/** Determines whether an event is a touch event. */\nfunction isTouchEvent(event) {\n    // This function is called for every pixel that the user has dragged so we need it to be\n    // as fast as possible. Since we only bind mouse events and touch events, we can assume\n    // that if the event's name starts with `t`, it's a touch event.\n    return event.type[0] === 't';\n}\n/** Callback invoked for `selectstart` events inside the shadow DOM. */\nfunction shadowDomSelectStart(event) {\n    event.preventDefault();\n}\n\n/**\n * Moves an item one index in an array to another.\n * @param array Array in which to move the item.\n * @param fromIndex Starting index of the item.\n * @param toIndex Index to which the item should be moved.\n */\nfunction moveItemInArray(array, fromIndex, toIndex) {\n    const from = clamp(fromIndex, array.length - 1);\n    const to = clamp(toIndex, array.length - 1);\n    if (from === to) {\n        return;\n    }\n    const target = array[from];\n    const delta = to < from ? -1 : 1;\n    for (let i = from; i !== to; i += delta) {\n        array[i] = array[i + delta];\n    }\n    array[to] = target;\n}\n/**\n * Moves an item from one array to another.\n * @param currentArray Array from which to transfer the item.\n * @param targetArray Array into which to put the item.\n * @param currentIndex Index of the item in its current array.\n * @param targetIndex Index at which to insert the item.\n */\nfunction transferArrayItem(currentArray, targetArray, currentIndex, targetIndex) {\n    const from = clamp(currentIndex, currentArray.length - 1);\n    const to = clamp(targetIndex, targetArray.length);\n    if (currentArray.length) {\n        targetArray.splice(to, 0, currentArray.splice(from, 1)[0]);\n    }\n}\n/**\n * Copies an item from one array to another, leaving it in its\n * original position in current array.\n * @param currentArray Array from which to copy the item.\n * @param targetArray Array into which is copy the item.\n * @param currentIndex Index of the item in its current array.\n * @param targetIndex Index at which to insert the item.\n *\n */\nfunction copyArrayItem(currentArray, targetArray, currentIndex, targetIndex) {\n    const to = clamp(targetIndex, targetArray.length);\n    if (currentArray.length) {\n        targetArray.splice(to, 0, currentArray[currentIndex]);\n    }\n}\n/** Clamps a number between zero and a maximum. */\nfunction clamp(value, max) {\n    return Math.max(0, Math.min(max, value));\n}\n\n/**\n * Strategy that only supports sorting along a single axis.\n * Items are reordered using CSS transforms which allows for sorting to be animated.\n * @docs-private\n */\nclass SingleAxisSortStrategy {\n    constructor(_element, _dragDropRegistry) {\n        this._element = _element;\n        this._dragDropRegistry = _dragDropRegistry;\n        /** Cache of the dimensions of all the items inside the container. */\n        this._itemPositions = [];\n        /** Direction in which the list is oriented. */\n        this.orientation = 'vertical';\n        /**\n         * Keeps track of the item that was last swapped with the dragged item, as well as what direction\n         * the pointer was moving in when the swap occurred and whether the user's pointer continued to\n         * overlap with the swapped item after the swapping occurred.\n         */\n        this._previousSwap = {\n            drag: null,\n            delta: 0,\n            overlaps: false,\n        };\n    }\n    /**\n     * To be called when the drag sequence starts.\n     * @param items Items that are currently in the list.\n     */\n    start(items) {\n        this.withItems(items);\n    }\n    /**\n     * To be called when an item is being sorted.\n     * @param item Item to be sorted.\n     * @param pointerX Position of the item along the X axis.\n     * @param pointerY Position of the item along the Y axis.\n     * @param pointerDelta Direction in which the pointer is moving along each axis.\n     */\n    sort(item, pointerX, pointerY, pointerDelta) {\n        const siblings = this._itemPositions;\n        const newIndex = this._getItemIndexFromPointerPosition(item, pointerX, pointerY, pointerDelta);\n        if (newIndex === -1 && siblings.length > 0) {\n            return null;\n        }\n        const isHorizontal = this.orientation === 'horizontal';\n        const currentIndex = siblings.findIndex(currentItem => currentItem.drag === item);\n        const siblingAtNewPosition = siblings[newIndex];\n        const currentPosition = siblings[currentIndex].clientRect;\n        const newPosition = siblingAtNewPosition.clientRect;\n        const delta = currentIndex > newIndex ? 1 : -1;\n        // How many pixels the item's placeholder should be offset.\n        const itemOffset = this._getItemOffsetPx(currentPosition, newPosition, delta);\n        // How many pixels all the other items should be offset.\n        const siblingOffset = this._getSiblingOffsetPx(currentIndex, siblings, delta);\n        // Save the previous order of the items before moving the item to its new index.\n        // We use this to check whether an item has been moved as a result of the sorting.\n        const oldOrder = siblings.slice();\n        // Shuffle the array in place.\n        moveItemInArray(siblings, currentIndex, newIndex);\n        siblings.forEach((sibling, index) => {\n            // Don't do anything if the position hasn't changed.\n            if (oldOrder[index] === sibling) {\n                return;\n            }\n            const isDraggedItem = sibling.drag === item;\n            const offset = isDraggedItem ? itemOffset : siblingOffset;\n            const elementToOffset = isDraggedItem\n                ? item.getPlaceholderElement()\n                : sibling.drag.getRootElement();\n            // Update the offset to reflect the new position.\n            sibling.offset += offset;\n            // Since we're moving the items with a `transform`, we need to adjust their cached\n            // client rects to reflect their new position, as well as swap their positions in the cache.\n            // Note that we shouldn't use `getBoundingClientRect` here to update the cache, because the\n            // elements may be mid-animation which will give us a wrong result.\n            if (isHorizontal) {\n                // Round the transforms since some browsers will\n                // blur the elements, for sub-pixel transforms.\n                elementToOffset.style.transform = combineTransforms(`translate3d(${Math.round(sibling.offset)}px, 0, 0)`, sibling.initialTransform);\n                adjustDomRect(sibling.clientRect, 0, offset);\n            }\n            else {\n                elementToOffset.style.transform = combineTransforms(`translate3d(0, ${Math.round(sibling.offset)}px, 0)`, sibling.initialTransform);\n                adjustDomRect(sibling.clientRect, offset, 0);\n            }\n        });\n        // Note that it's important that we do this after the client rects have been adjusted.\n        this._previousSwap.overlaps = isInsideClientRect(newPosition, pointerX, pointerY);\n        this._previousSwap.drag = siblingAtNewPosition.drag;\n        this._previousSwap.delta = isHorizontal ? pointerDelta.x : pointerDelta.y;\n        return { previousIndex: currentIndex, currentIndex: newIndex };\n    }\n    /**\n     * Called when an item is being moved into the container.\n     * @param item Item that was moved into the container.\n     * @param pointerX Position of the item along the X axis.\n     * @param pointerY Position of the item along the Y axis.\n     * @param index Index at which the item entered. If omitted, the container will try to figure it\n     *   out automatically.\n     */\n    enter(item, pointerX, pointerY, index) {\n        const newIndex = index == null || index < 0\n            ? // We use the coordinates of where the item entered the drop\n                // zone to figure out at which index it should be inserted.\n                this._getItemIndexFromPointerPosition(item, pointerX, pointerY)\n            : index;\n        const activeDraggables = this._activeDraggables;\n        const currentIndex = activeDraggables.indexOf(item);\n        const placeholder = item.getPlaceholderElement();\n        let newPositionReference = activeDraggables[newIndex];\n        // If the item at the new position is the same as the item that is being dragged,\n        // it means that we're trying to restore the item to its initial position. In this\n        // case we should use the next item from the list as the reference.\n        if (newPositionReference === item) {\n            newPositionReference = activeDraggables[newIndex + 1];\n        }\n        // If we didn't find a new position reference, it means that either the item didn't start off\n        // in this container, or that the item requested to be inserted at the end of the list.\n        if (!newPositionReference &&\n            (newIndex == null || newIndex === -1 || newIndex < activeDraggables.length - 1) &&\n            this._shouldEnterAsFirstChild(pointerX, pointerY)) {\n            newPositionReference = activeDraggables[0];\n        }\n        // Since the item may be in the `activeDraggables` already (e.g. if the user dragged it\n        // into another container and back again), we have to ensure that it isn't duplicated.\n        if (currentIndex > -1) {\n            activeDraggables.splice(currentIndex, 1);\n        }\n        // Don't use items that are being dragged as a reference, because\n        // their element has been moved down to the bottom of the body.\n        if (newPositionReference && !this._dragDropRegistry.isDragging(newPositionReference)) {\n            const element = newPositionReference.getRootElement();\n            element.parentElement.insertBefore(placeholder, element);\n            activeDraggables.splice(newIndex, 0, item);\n        }\n        else {\n            coerceElement(this._element).appendChild(placeholder);\n            activeDraggables.push(item);\n        }\n        // The transform needs to be cleared so it doesn't throw off the measurements.\n        placeholder.style.transform = '';\n        // Note that usually `start` is called together with `enter` when an item goes into a new\n        // container. This will cache item positions, but we need to refresh them since the amount\n        // of items has changed.\n        this._cacheItemPositions();\n    }\n    /** Sets the items that are currently part of the list. */\n    withItems(items) {\n        this._activeDraggables = items.slice();\n        this._cacheItemPositions();\n    }\n    /** Assigns a sort predicate to the strategy. */\n    withSortPredicate(predicate) {\n        this._sortPredicate = predicate;\n    }\n    /** Resets the strategy to its initial state before dragging was started. */\n    reset() {\n        // TODO(crisbeto): may have to wait for the animations to finish.\n        this._activeDraggables.forEach(item => {\n            const rootElement = item.getRootElement();\n            if (rootElement) {\n                const initialTransform = this._itemPositions.find(p => p.drag === item)?.initialTransform;\n                rootElement.style.transform = initialTransform || '';\n            }\n        });\n        this._itemPositions = [];\n        this._activeDraggables = [];\n        this._previousSwap.drag = null;\n        this._previousSwap.delta = 0;\n        this._previousSwap.overlaps = false;\n    }\n    /**\n     * Gets a snapshot of items currently in the list.\n     * Can include items that we dragged in from another list.\n     */\n    getActiveItemsSnapshot() {\n        return this._activeDraggables;\n    }\n    /** Gets the index of a specific item. */\n    getItemIndex(item) {\n        // Items are sorted always by top/left in the cache, however they flow differently in RTL.\n        // The rest of the logic still stands no matter what orientation we're in, however\n        // we need to invert the array when determining the index.\n        const items = this.orientation === 'horizontal' && this.direction === 'rtl'\n            ? this._itemPositions.slice().reverse()\n            : this._itemPositions;\n        return items.findIndex(currentItem => currentItem.drag === item);\n    }\n    /** Used to notify the strategy that the scroll position has changed. */\n    updateOnScroll(topDifference, leftDifference) {\n        // Since we know the amount that the user has scrolled we can shift all of the\n        // client rectangles ourselves. This is cheaper than re-measuring everything and\n        // we can avoid inconsistent behavior where we might be measuring the element before\n        // its position has changed.\n        this._itemPositions.forEach(({ clientRect }) => {\n            adjustDomRect(clientRect, topDifference, leftDifference);\n        });\n        // We need two loops for this, because we want all of the cached\n        // positions to be up-to-date before we re-sort the item.\n        this._itemPositions.forEach(({ drag }) => {\n            if (this._dragDropRegistry.isDragging(drag)) {\n                // We need to re-sort the item manually, because the pointer move\n                // events won't be dispatched while the user is scrolling.\n                drag._sortFromLastPointerPosition();\n            }\n        });\n    }\n    /** Refreshes the position cache of the items and sibling containers. */\n    _cacheItemPositions() {\n        const isHorizontal = this.orientation === 'horizontal';\n        this._itemPositions = this._activeDraggables\n            .map(drag => {\n            const elementToMeasure = drag.getVisibleElement();\n            return {\n                drag,\n                offset: 0,\n                initialTransform: elementToMeasure.style.transform || '',\n                clientRect: getMutableClientRect(elementToMeasure),\n            };\n        })\n            .sort((a, b) => {\n            return isHorizontal\n                ? a.clientRect.left - b.clientRect.left\n                : a.clientRect.top - b.clientRect.top;\n        });\n    }\n    /**\n     * Gets the offset in pixels by which the item that is being dragged should be moved.\n     * @param currentPosition Current position of the item.\n     * @param newPosition Position of the item where the current item should be moved.\n     * @param delta Direction in which the user is moving.\n     */\n    _getItemOffsetPx(currentPosition, newPosition, delta) {\n        const isHorizontal = this.orientation === 'horizontal';\n        let itemOffset = isHorizontal\n            ? newPosition.left - currentPosition.left\n            : newPosition.top - currentPosition.top;\n        // Account for differences in the item width/height.\n        if (delta === -1) {\n            itemOffset += isHorizontal\n                ? newPosition.width - currentPosition.width\n                : newPosition.height - currentPosition.height;\n        }\n        return itemOffset;\n    }\n    /**\n     * Gets the offset in pixels by which the items that aren't being dragged should be moved.\n     * @param currentIndex Index of the item currently being dragged.\n     * @param siblings All of the items in the list.\n     * @param delta Direction in which the user is moving.\n     */\n    _getSiblingOffsetPx(currentIndex, siblings, delta) {\n        const isHorizontal = this.orientation === 'horizontal';\n        const currentPosition = siblings[currentIndex].clientRect;\n        const immediateSibling = siblings[currentIndex + delta * -1];\n        let siblingOffset = currentPosition[isHorizontal ? 'width' : 'height'] * delta;\n        if (immediateSibling) {\n            const start = isHorizontal ? 'left' : 'top';\n            const end = isHorizontal ? 'right' : 'bottom';\n            // Get the spacing between the start of the current item and the end of the one immediately\n            // after it in the direction in which the user is dragging, or vice versa. We add it to the\n            // offset in order to push the element to where it will be when it's inline and is influenced\n            // by the `margin` of its siblings.\n            if (delta === -1) {\n                siblingOffset -= immediateSibling.clientRect[start] - currentPosition[end];\n            }\n            else {\n                siblingOffset += currentPosition[start] - immediateSibling.clientRect[end];\n            }\n        }\n        return siblingOffset;\n    }\n    /**\n     * Checks if pointer is entering in the first position\n     * @param pointerX Position of the user's pointer along the X axis.\n     * @param pointerY Position of the user's pointer along the Y axis.\n     */\n    _shouldEnterAsFirstChild(pointerX, pointerY) {\n        if (!this._activeDraggables.length) {\n            return false;\n        }\n        const itemPositions = this._itemPositions;\n        const isHorizontal = this.orientation === 'horizontal';\n        // `itemPositions` are sorted by position while `activeDraggables` are sorted by child index\n        // check if container is using some sort of \"reverse\" ordering (eg: flex-direction: row-reverse)\n        const reversed = itemPositions[0].drag !== this._activeDraggables[0];\n        if (reversed) {\n            const lastItemRect = itemPositions[itemPositions.length - 1].clientRect;\n            return isHorizontal ? pointerX >= lastItemRect.right : pointerY >= lastItemRect.bottom;\n        }\n        else {\n            const firstItemRect = itemPositions[0].clientRect;\n            return isHorizontal ? pointerX <= firstItemRect.left : pointerY <= firstItemRect.top;\n        }\n    }\n    /**\n     * Gets the index of an item in the drop container, based on the position of the user's pointer.\n     * @param item Item that is being sorted.\n     * @param pointerX Position of the user's pointer along the X axis.\n     * @param pointerY Position of the user's pointer along the Y axis.\n     * @param delta Direction in which the user is moving their pointer.\n     */\n    _getItemIndexFromPointerPosition(item, pointerX, pointerY, delta) {\n        const isHorizontal = this.orientation === 'horizontal';\n        const index = this._itemPositions.findIndex(({ drag, clientRect }) => {\n            // Skip the item itself.\n            if (drag === item) {\n                return false;\n            }\n            if (delta) {\n                const direction = isHorizontal ? delta.x : delta.y;\n                // If the user is still hovering over the same item as last time, their cursor hasn't left\n                // the item after we made the swap, and they didn't change the direction in which they're\n                // dragging, we don't consider it a direction swap.\n                if (drag === this._previousSwap.drag &&\n                    this._previousSwap.overlaps &&\n                    direction === this._previousSwap.delta) {\n                    return false;\n                }\n            }\n            return isHorizontal\n                ? // Round these down since most browsers report client rects with\n                    // sub-pixel precision, whereas the pointer coordinates are rounded to pixels.\n                    pointerX >= Math.floor(clientRect.left) && pointerX < Math.floor(clientRect.right)\n                : pointerY >= Math.floor(clientRect.top) && pointerY < Math.floor(clientRect.bottom);\n        });\n        return index === -1 || !this._sortPredicate(index, item) ? -1 : index;\n    }\n}\n\n/**\n * Proximity, as a ratio to width/height, at which a\n * dragged item will affect the drop container.\n */\nconst DROP_PROXIMITY_THRESHOLD = 0.05;\n/**\n * Proximity, as a ratio to width/height at which to start auto-scrolling the drop list or the\n * viewport. The value comes from trying it out manually until it feels right.\n */\nconst SCROLL_PROXIMITY_THRESHOLD = 0.05;\n/** Vertical direction in which we can auto-scroll. */\nvar AutoScrollVerticalDirection;\n(function (AutoScrollVerticalDirection) {\n    AutoScrollVerticalDirection[AutoScrollVerticalDirection[\"NONE\"] = 0] = \"NONE\";\n    AutoScrollVerticalDirection[AutoScrollVerticalDirection[\"UP\"] = 1] = \"UP\";\n    AutoScrollVerticalDirection[AutoScrollVerticalDirection[\"DOWN\"] = 2] = \"DOWN\";\n})(AutoScrollVerticalDirection || (AutoScrollVerticalDirection = {}));\n/** Horizontal direction in which we can auto-scroll. */\nvar AutoScrollHorizontalDirection;\n(function (AutoScrollHorizontalDirection) {\n    AutoScrollHorizontalDirection[AutoScrollHorizontalDirection[\"NONE\"] = 0] = \"NONE\";\n    AutoScrollHorizontalDirection[AutoScrollHorizontalDirection[\"LEFT\"] = 1] = \"LEFT\";\n    AutoScrollHorizontalDirection[AutoScrollHorizontalDirection[\"RIGHT\"] = 2] = \"RIGHT\";\n})(AutoScrollHorizontalDirection || (AutoScrollHorizontalDirection = {}));\n/**\n * Reference to a drop list. Used to manipulate or dispose of the container.\n */\nclass DropListRef {\n    constructor(element, _dragDropRegistry, _document, _ngZone, _viewportRuler) {\n        this._dragDropRegistry = _dragDropRegistry;\n        this._ngZone = _ngZone;\n        this._viewportRuler = _viewportRuler;\n        /** Whether starting a dragging sequence from this container is disabled. */\n        this.disabled = false;\n        /** Whether sorting items within the list is disabled. */\n        this.sortingDisabled = false;\n        /**\n         * Whether auto-scrolling the view when the user\n         * moves their pointer close to the edges is disabled.\n         */\n        this.autoScrollDisabled = false;\n        /** Number of pixels to scroll for each frame when auto-scrolling an element. */\n        this.autoScrollStep = 2;\n        /**\n         * Function that is used to determine whether an item\n         * is allowed to be moved into a drop container.\n         */\n        this.enterPredicate = () => true;\n        /** Function that is used to determine whether an item can be sorted into a particular index. */\n        this.sortPredicate = () => true;\n        /** Emits right before dragging has started. */\n        this.beforeStarted = new Subject();\n        /**\n         * Emits when the user has moved a new drag item into this container.\n         */\n        this.entered = new Subject();\n        /**\n         * Emits when the user removes an item from the container\n         * by dragging it into another container.\n         */\n        this.exited = new Subject();\n        /** Emits when the user drops an item inside the container. */\n        this.dropped = new Subject();\n        /** Emits as the user is swapping items while actively dragging. */\n        this.sorted = new Subject();\n        /** Emits when a dragging sequence is started in a list connected to the current one. */\n        this.receivingStarted = new Subject();\n        /** Emits when a dragging sequence is stopped from a list connected to the current one. */\n        this.receivingStopped = new Subject();\n        /** Whether an item in the list is being dragged. */\n        this._isDragging = false;\n        /** Draggable items in the container. */\n        this._draggables = [];\n        /** Drop lists that are connected to the current one. */\n        this._siblings = [];\n        /** Connected siblings that currently have a dragged item. */\n        this._activeSiblings = new Set();\n        /** Subscription to the window being scrolled. */\n        this._viewportScrollSubscription = Subscription.EMPTY;\n        /** Vertical direction in which the list is currently scrolling. */\n        this._verticalScrollDirection = AutoScrollVerticalDirection.NONE;\n        /** Horizontal direction in which the list is currently scrolling. */\n        this._horizontalScrollDirection = AutoScrollHorizontalDirection.NONE;\n        /** Used to signal to the current auto-scroll sequence when to stop. */\n        this._stopScrollTimers = new Subject();\n        /** Shadow root of the current element. Necessary for `elementFromPoint` to resolve correctly. */\n        this._cachedShadowRoot = null;\n        /** Starts the interval that'll auto-scroll the element. */\n        this._startScrollInterval = () => {\n            this._stopScrolling();\n            interval(0, animationFrameScheduler)\n                .pipe(takeUntil(this._stopScrollTimers))\n                .subscribe(() => {\n                const node = this._scrollNode;\n                const scrollStep = this.autoScrollStep;\n                if (this._verticalScrollDirection === AutoScrollVerticalDirection.UP) {\n                    node.scrollBy(0, -scrollStep);\n                }\n                else if (this._verticalScrollDirection === AutoScrollVerticalDirection.DOWN) {\n                    node.scrollBy(0, scrollStep);\n                }\n                if (this._horizontalScrollDirection === AutoScrollHorizontalDirection.LEFT) {\n                    node.scrollBy(-scrollStep, 0);\n                }\n                else if (this._horizontalScrollDirection === AutoScrollHorizontalDirection.RIGHT) {\n                    node.scrollBy(scrollStep, 0);\n                }\n            });\n        };\n        this.element = coerceElement(element);\n        this._document = _document;\n        this.withScrollableParents([this.element]);\n        _dragDropRegistry.registerDropContainer(this);\n        this._parentPositions = new ParentPositionTracker(_document);\n        this._sortStrategy = new SingleAxisSortStrategy(this.element, _dragDropRegistry);\n        this._sortStrategy.withSortPredicate((index, item) => this.sortPredicate(index, item, this));\n    }\n    /** Removes the drop list functionality from the DOM element. */\n    dispose() {\n        this._stopScrolling();\n        this._stopScrollTimers.complete();\n        this._viewportScrollSubscription.unsubscribe();\n        this.beforeStarted.complete();\n        this.entered.complete();\n        this.exited.complete();\n        this.dropped.complete();\n        this.sorted.complete();\n        this.receivingStarted.complete();\n        this.receivingStopped.complete();\n        this._activeSiblings.clear();\n        this._scrollNode = null;\n        this._parentPositions.clear();\n        this._dragDropRegistry.removeDropContainer(this);\n    }\n    /** Whether an item from this list is currently being dragged. */\n    isDragging() {\n        return this._isDragging;\n    }\n    /** Starts dragging an item. */\n    start() {\n        this._draggingStarted();\n        this._notifyReceivingSiblings();\n    }\n    /**\n     * Attempts to move an item into the container.\n     * @param item Item that was moved into the container.\n     * @param pointerX Position of the item along the X axis.\n     * @param pointerY Position of the item along the Y axis.\n     * @param index Index at which the item entered. If omitted, the container will try to figure it\n     *   out automatically.\n     */\n    enter(item, pointerX, pointerY, index) {\n        this._draggingStarted();\n        // If sorting is disabled, we want the item to return to its starting\n        // position if the user is returning it to its initial container.\n        if (index == null && this.sortingDisabled) {\n            index = this._draggables.indexOf(item);\n        }\n        this._sortStrategy.enter(item, pointerX, pointerY, index);\n        // Note that this usually happens inside `_draggingStarted` as well, but the dimensions\n        // can change when the sort strategy moves the item around inside `enter`.\n        this._cacheParentPositions();\n        // Notify siblings at the end so that the item has been inserted into the `activeDraggables`.\n        this._notifyReceivingSiblings();\n        this.entered.next({ item, container: this, currentIndex: this.getItemIndex(item) });\n    }\n    /**\n     * Removes an item from the container after it was dragged into another container by the user.\n     * @param item Item that was dragged out.\n     */\n    exit(item) {\n        this._reset();\n        this.exited.next({ item, container: this });\n    }\n    /**\n     * Drops an item into this container.\n     * @param item Item being dropped into the container.\n     * @param currentIndex Index at which the item should be inserted.\n     * @param previousIndex Index of the item when dragging started.\n     * @param previousContainer Container from which the item got dragged in.\n     * @param isPointerOverContainer Whether the user's pointer was over the\n     *    container when the item was dropped.\n     * @param distance Distance the user has dragged since the start of the dragging sequence.\n     * @param event Event that triggered the dropping sequence.\n     *\n     * @breaking-change 15.0.0 `previousIndex` and `event` parameters to become required.\n     */\n    drop(item, currentIndex, previousIndex, previousContainer, isPointerOverContainer, distance, dropPoint, event = {}) {\n        this._reset();\n        this.dropped.next({\n            item,\n            currentIndex,\n            previousIndex,\n            container: this,\n            previousContainer,\n            isPointerOverContainer,\n            distance,\n            dropPoint,\n            event,\n        });\n    }\n    /**\n     * Sets the draggable items that are a part of this list.\n     * @param items Items that are a part of this list.\n     */\n    withItems(items) {\n        const previousItems = this._draggables;\n        this._draggables = items;\n        items.forEach(item => item._withDropContainer(this));\n        if (this.isDragging()) {\n            const draggedItems = previousItems.filter(item => item.isDragging());\n            // If all of the items being dragged were removed\n            // from the list, abort the current drag sequence.\n            if (draggedItems.every(item => items.indexOf(item) === -1)) {\n                this._reset();\n            }\n            else {\n                this._sortStrategy.withItems(this._draggables);\n            }\n        }\n        return this;\n    }\n    /** Sets the layout direction of the drop list. */\n    withDirection(direction) {\n        this._sortStrategy.direction = direction;\n        return this;\n    }\n    /**\n     * Sets the containers that are connected to this one. When two or more containers are\n     * connected, the user will be allowed to transfer items between them.\n     * @param connectedTo Other containers that the current containers should be connected to.\n     */\n    connectedTo(connectedTo) {\n        this._siblings = connectedTo.slice();\n        return this;\n    }\n    /**\n     * Sets the orientation of the container.\n     * @param orientation New orientation for the container.\n     */\n    withOrientation(orientation) {\n        // TODO(crisbeto): eventually we should be constructing the new sort strategy here based on\n        // the new orientation. For now we can assume that it'll always be `SingleAxisSortStrategy`.\n        this._sortStrategy.orientation = orientation;\n        return this;\n    }\n    /**\n     * Sets which parent elements are can be scrolled while the user is dragging.\n     * @param elements Elements that can be scrolled.\n     */\n    withScrollableParents(elements) {\n        const element = coerceElement(this.element);\n        // We always allow the current element to be scrollable\n        // so we need to ensure that it's in the array.\n        this._scrollableElements =\n            elements.indexOf(element) === -1 ? [element, ...elements] : elements.slice();\n        return this;\n    }\n    /** Gets the scrollable parents that are registered with this drop container. */\n    getScrollableParents() {\n        return this._scrollableElements;\n    }\n    /**\n     * Figures out the index of an item in the container.\n     * @param item Item whose index should be determined.\n     */\n    getItemIndex(item) {\n        return this._isDragging\n            ? this._sortStrategy.getItemIndex(item)\n            : this._draggables.indexOf(item);\n    }\n    /**\n     * Whether the list is able to receive the item that\n     * is currently being dragged inside a connected drop list.\n     */\n    isReceiving() {\n        return this._activeSiblings.size > 0;\n    }\n    /**\n     * Sorts an item inside the container based on its position.\n     * @param item Item to be sorted.\n     * @param pointerX Position of the item along the X axis.\n     * @param pointerY Position of the item along the Y axis.\n     * @param pointerDelta Direction in which the pointer is moving along each axis.\n     */\n    _sortItem(item, pointerX, pointerY, pointerDelta) {\n        // Don't sort the item if sorting is disabled or it's out of range.\n        if (this.sortingDisabled ||\n            !this._domRect ||\n            !isPointerNearDomRect(this._domRect, DROP_PROXIMITY_THRESHOLD, pointerX, pointerY)) {\n            return;\n        }\n        const result = this._sortStrategy.sort(item, pointerX, pointerY, pointerDelta);\n        if (result) {\n            this.sorted.next({\n                previousIndex: result.previousIndex,\n                currentIndex: result.currentIndex,\n                container: this,\n                item,\n            });\n        }\n    }\n    /**\n     * Checks whether the user's pointer is close to the edges of either the\n     * viewport or the drop list and starts the auto-scroll sequence.\n     * @param pointerX User's pointer position along the x axis.\n     * @param pointerY User's pointer position along the y axis.\n     */\n    _startScrollingIfNecessary(pointerX, pointerY) {\n        if (this.autoScrollDisabled) {\n            return;\n        }\n        let scrollNode;\n        let verticalScrollDirection = AutoScrollVerticalDirection.NONE;\n        let horizontalScrollDirection = AutoScrollHorizontalDirection.NONE;\n        // Check whether we should start scrolling any of the parent containers.\n        this._parentPositions.positions.forEach((position, element) => {\n            // We have special handling for the `document` below. Also this would be\n            // nicer with a  for...of loop, but it requires changing a compiler flag.\n            if (element === this._document || !position.clientRect || scrollNode) {\n                return;\n            }\n            if (isPointerNearDomRect(position.clientRect, DROP_PROXIMITY_THRESHOLD, pointerX, pointerY)) {\n                [verticalScrollDirection, horizontalScrollDirection] = getElementScrollDirections(element, position.clientRect, this._sortStrategy.direction, pointerX, pointerY);\n                if (verticalScrollDirection || horizontalScrollDirection) {\n                    scrollNode = element;\n                }\n            }\n        });\n        // Otherwise check if we can start scrolling the viewport.\n        if (!verticalScrollDirection && !horizontalScrollDirection) {\n            const { width, height } = this._viewportRuler.getViewportSize();\n            const domRect = {\n                width,\n                height,\n                top: 0,\n                right: width,\n                bottom: height,\n                left: 0,\n            };\n            verticalScrollDirection = getVerticalScrollDirection(domRect, pointerY);\n            horizontalScrollDirection = getHorizontalScrollDirection(domRect, pointerX);\n            scrollNode = window;\n        }\n        if (scrollNode &&\n            (verticalScrollDirection !== this._verticalScrollDirection ||\n                horizontalScrollDirection !== this._horizontalScrollDirection ||\n                scrollNode !== this._scrollNode)) {\n            this._verticalScrollDirection = verticalScrollDirection;\n            this._horizontalScrollDirection = horizontalScrollDirection;\n            this._scrollNode = scrollNode;\n            if ((verticalScrollDirection || horizontalScrollDirection) && scrollNode) {\n                this._ngZone.runOutsideAngular(this._startScrollInterval);\n            }\n            else {\n                this._stopScrolling();\n            }\n        }\n    }\n    /** Stops any currently-running auto-scroll sequences. */\n    _stopScrolling() {\n        this._stopScrollTimers.next();\n    }\n    /** Starts the dragging sequence within the list. */\n    _draggingStarted() {\n        const styles = coerceElement(this.element).style;\n        this.beforeStarted.next();\n        this._isDragging = true;\n        // We need to disable scroll snapping while the user is dragging, because it breaks automatic\n        // scrolling. The browser seems to round the value based on the snapping points which means\n        // that we can't increment/decrement the scroll position.\n        this._initialScrollSnap = styles.msScrollSnapType || styles.scrollSnapType || '';\n        styles.scrollSnapType = styles.msScrollSnapType = 'none';\n        this._sortStrategy.start(this._draggables);\n        this._cacheParentPositions();\n        this._viewportScrollSubscription.unsubscribe();\n        this._listenToScrollEvents();\n    }\n    /** Caches the positions of the configured scrollable parents. */\n    _cacheParentPositions() {\n        const element = coerceElement(this.element);\n        this._parentPositions.cache(this._scrollableElements);\n        // The list element is always in the `scrollableElements`\n        // so we can take advantage of the cached `DOMRect`.\n        this._domRect = this._parentPositions.positions.get(element).clientRect;\n    }\n    /** Resets the container to its initial state. */\n    _reset() {\n        this._isDragging = false;\n        const styles = coerceElement(this.element).style;\n        styles.scrollSnapType = styles.msScrollSnapType = this._initialScrollSnap;\n        this._siblings.forEach(sibling => sibling._stopReceiving(this));\n        this._sortStrategy.reset();\n        this._stopScrolling();\n        this._viewportScrollSubscription.unsubscribe();\n        this._parentPositions.clear();\n    }\n    /**\n     * Checks whether the user's pointer is positioned over the container.\n     * @param x Pointer position along the X axis.\n     * @param y Pointer position along the Y axis.\n     */\n    _isOverContainer(x, y) {\n        return this._domRect != null && isInsideClientRect(this._domRect, x, y);\n    }\n    /**\n     * Figures out whether an item should be moved into a sibling\n     * drop container, based on its current position.\n     * @param item Drag item that is being moved.\n     * @param x Position of the item along the X axis.\n     * @param y Position of the item along the Y axis.\n     */\n    _getSiblingContainerFromPosition(item, x, y) {\n        return this._siblings.find(sibling => sibling._canReceive(item, x, y));\n    }\n    /**\n     * Checks whether the drop list can receive the passed-in item.\n     * @param item Item that is being dragged into the list.\n     * @param x Position of the item along the X axis.\n     * @param y Position of the item along the Y axis.\n     */\n    _canReceive(item, x, y) {\n        if (!this._domRect ||\n            !isInsideClientRect(this._domRect, x, y) ||\n            !this.enterPredicate(item, this)) {\n            return false;\n        }\n        const elementFromPoint = this._getShadowRoot().elementFromPoint(x, y);\n        // If there's no element at the pointer position, then\n        // the client rect is probably scrolled out of the view.\n        if (!elementFromPoint) {\n            return false;\n        }\n        const nativeElement = coerceElement(this.element);\n        // The `DOMRect`, that we're using to find the container over which the user is\n        // hovering, doesn't give us any information on whether the element has been scrolled\n        // out of the view or whether it's overlapping with other containers. This means that\n        // we could end up transferring the item into a container that's invisible or is positioned\n        // below another one. We use the result from `elementFromPoint` to get the top-most element\n        // at the pointer position and to find whether it's one of the intersecting drop containers.\n        return elementFromPoint === nativeElement || nativeElement.contains(elementFromPoint);\n    }\n    /**\n     * Called by one of the connected drop lists when a dragging sequence has started.\n     * @param sibling Sibling in which dragging has started.\n     */\n    _startReceiving(sibling, items) {\n        const activeSiblings = this._activeSiblings;\n        if (!activeSiblings.has(sibling) &&\n            items.every(item => {\n                // Note that we have to add an exception to the `enterPredicate` for items that started off\n                // in this drop list. The drag ref has logic that allows an item to return to its initial\n                // container, if it has left the initial container and none of the connected containers\n                // allow it to enter. See `DragRef._updateActiveDropContainer` for more context.\n                return this.enterPredicate(item, this) || this._draggables.indexOf(item) > -1;\n            })) {\n            activeSiblings.add(sibling);\n            this._cacheParentPositions();\n            this._listenToScrollEvents();\n            this.receivingStarted.next({\n                initiator: sibling,\n                receiver: this,\n                items,\n            });\n        }\n    }\n    /**\n     * Called by a connected drop list when dragging has stopped.\n     * @param sibling Sibling whose dragging has stopped.\n     */\n    _stopReceiving(sibling) {\n        this._activeSiblings.delete(sibling);\n        this._viewportScrollSubscription.unsubscribe();\n        this.receivingStopped.next({ initiator: sibling, receiver: this });\n    }\n    /**\n     * Starts listening to scroll events on the viewport.\n     * Used for updating the internal state of the list.\n     */\n    _listenToScrollEvents() {\n        this._viewportScrollSubscription = this._dragDropRegistry\n            .scrolled(this._getShadowRoot())\n            .subscribe(event => {\n            if (this.isDragging()) {\n                const scrollDifference = this._parentPositions.handleScroll(event);\n                if (scrollDifference) {\n                    this._sortStrategy.updateOnScroll(scrollDifference.top, scrollDifference.left);\n                }\n            }\n            else if (this.isReceiving()) {\n                this._cacheParentPositions();\n            }\n        });\n    }\n    /**\n     * Lazily resolves and returns the shadow root of the element. We do this in a function, rather\n     * than saving it in property directly on init, because we want to resolve it as late as possible\n     * in order to ensure that the element has been moved into the shadow DOM. Doing it inside the\n     * constructor might be too early if the element is inside of something like `ngFor` or `ngIf`.\n     */\n    _getShadowRoot() {\n        if (!this._cachedShadowRoot) {\n            const shadowRoot = _getShadowRoot(coerceElement(this.element));\n            this._cachedShadowRoot = (shadowRoot || this._document);\n        }\n        return this._cachedShadowRoot;\n    }\n    /** Notifies any siblings that may potentially receive the item. */\n    _notifyReceivingSiblings() {\n        const draggedItems = this._sortStrategy\n            .getActiveItemsSnapshot()\n            .filter(item => item.isDragging());\n        this._siblings.forEach(sibling => sibling._startReceiving(this, draggedItems));\n    }\n}\n/**\n * Gets whether the vertical auto-scroll direction of a node.\n * @param clientRect Dimensions of the node.\n * @param pointerY Position of the user's pointer along the y axis.\n */\nfunction getVerticalScrollDirection(clientRect, pointerY) {\n    const { top, bottom, height } = clientRect;\n    const yThreshold = height * SCROLL_PROXIMITY_THRESHOLD;\n    if (pointerY >= top - yThreshold && pointerY <= top + yThreshold) {\n        return AutoScrollVerticalDirection.UP;\n    }\n    else if (pointerY >= bottom - yThreshold && pointerY <= bottom + yThreshold) {\n        return AutoScrollVerticalDirection.DOWN;\n    }\n    return AutoScrollVerticalDirection.NONE;\n}\n/**\n * Gets whether the horizontal auto-scroll direction of a node.\n * @param clientRect Dimensions of the node.\n * @param pointerX Position of the user's pointer along the x axis.\n */\nfunction getHorizontalScrollDirection(clientRect, pointerX) {\n    const { left, right, width } = clientRect;\n    const xThreshold = width * SCROLL_PROXIMITY_THRESHOLD;\n    if (pointerX >= left - xThreshold && pointerX <= left + xThreshold) {\n        return AutoScrollHorizontalDirection.LEFT;\n    }\n    else if (pointerX >= right - xThreshold && pointerX <= right + xThreshold) {\n        return AutoScrollHorizontalDirection.RIGHT;\n    }\n    return AutoScrollHorizontalDirection.NONE;\n}\n/**\n * Gets the directions in which an element node should be scrolled,\n * assuming that the user's pointer is already within it scrollable region.\n * @param element Element for which we should calculate the scroll direction.\n * @param clientRect Bounding client rectangle of the element.\n * @param direction Layout direction of the drop list.\n * @param pointerX Position of the user's pointer along the x axis.\n * @param pointerY Position of the user's pointer along the y axis.\n */\nfunction getElementScrollDirections(element, clientRect, direction, pointerX, pointerY) {\n    const computedVertical = getVerticalScrollDirection(clientRect, pointerY);\n    const computedHorizontal = getHorizontalScrollDirection(clientRect, pointerX);\n    let verticalScrollDirection = AutoScrollVerticalDirection.NONE;\n    let horizontalScrollDirection = AutoScrollHorizontalDirection.NONE;\n    // Note that we here we do some extra checks for whether the element is actually scrollable in\n    // a certain direction and we only assign the scroll direction if it is. We do this so that we\n    // can allow other elements to be scrolled, if the current element can't be scrolled anymore.\n    // This allows us to handle cases where the scroll regions of two scrollable elements overlap.\n    if (computedVertical) {\n        const scrollTop = element.scrollTop;\n        if (computedVertical === AutoScrollVerticalDirection.UP) {\n            if (scrollTop > 0) {\n                verticalScrollDirection = AutoScrollVerticalDirection.UP;\n            }\n        }\n        else if (element.scrollHeight - scrollTop > element.clientHeight) {\n            verticalScrollDirection = AutoScrollVerticalDirection.DOWN;\n        }\n    }\n    if (computedHorizontal) {\n        const scrollLeft = element.scrollLeft;\n        if (direction === 'rtl') {\n            if (computedHorizontal === AutoScrollHorizontalDirection.RIGHT) {\n                // In RTL `scrollLeft` will be negative when scrolled.\n                if (scrollLeft < 0) {\n                    horizontalScrollDirection = AutoScrollHorizontalDirection.RIGHT;\n                }\n            }\n            else if (element.scrollWidth + scrollLeft > element.clientWidth) {\n                horizontalScrollDirection = AutoScrollHorizontalDirection.LEFT;\n            }\n        }\n        else {\n            if (computedHorizontal === AutoScrollHorizontalDirection.LEFT) {\n                if (scrollLeft > 0) {\n                    horizontalScrollDirection = AutoScrollHorizontalDirection.LEFT;\n                }\n            }\n            else if (element.scrollWidth - scrollLeft > element.clientWidth) {\n                horizontalScrollDirection = AutoScrollHorizontalDirection.RIGHT;\n            }\n        }\n    }\n    return [verticalScrollDirection, horizontalScrollDirection];\n}\n\n/** Event options that can be used to bind an active, capturing event. */\nconst activeCapturingEventOptions = normalizePassiveListenerOptions({\n    passive: false,\n    capture: true,\n});\n/** Keeps track of the apps currently containing drag items. */\nconst activeApps = new Set();\n/**\n * Component used to load the drag&drop reset styles.\n * @docs-private\n */\nclass _ResetsLoader {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: _ResetsLoader, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.2.0\", type: _ResetsLoader, isStandalone: true, selector: \"ng-component\", host: { attributes: { \"cdk-drag-resets-container\": \"\" } }, ngImport: i0, template: '', isInline: true, styles: [\"@layer cdk-resets{.cdk-drag-preview{background:none;border:none;padding:0;color:inherit}}\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: _ResetsLoader, decorators: [{\n            type: Component,\n            args: [{ standalone: true, encapsulation: ViewEncapsulation.None, template: '', changeDetection: ChangeDetectionStrategy.OnPush, host: { 'cdk-drag-resets-container': '' }, styles: [\"@layer cdk-resets{.cdk-drag-preview{background:none;border:none;padding:0;color:inherit}}\"] }]\n        }] });\n/**\n * Service that keeps track of all the drag item and drop container\n * instances, and manages global event listeners on the `document`.\n * @docs-private\n */\n// Note: this class is generic, rather than referencing CdkDrag and CdkDropList directly, in order\n// to avoid circular imports. If we were to reference them here, importing the registry into the\n// classes that are registering themselves will introduce a circular import.\nclass DragDropRegistry {\n    constructor(_ngZone, _document) {\n        this._ngZone = _ngZone;\n        this._appRef = inject(ApplicationRef);\n        this._environmentInjector = inject(EnvironmentInjector);\n        /** Registered drop container instances. */\n        this._dropInstances = new Set();\n        /** Registered drag item instances. */\n        this._dragInstances = new Set();\n        /** Drag item instances that are currently being dragged. */\n        this._activeDragInstances = [];\n        /** Keeps track of the event listeners that we've bound to the `document`. */\n        this._globalListeners = new Map();\n        /**\n         * Predicate function to check if an item is being dragged.  Moved out into a property,\n         * because it'll be called a lot and we don't want to create a new function every time.\n         */\n        this._draggingPredicate = (item) => item.isDragging();\n        /**\n         * Emits the `touchmove` or `mousemove` events that are dispatched\n         * while the user is dragging a drag item instance.\n         */\n        this.pointerMove = new Subject();\n        /**\n         * Emits the `touchend` or `mouseup` events that are dispatched\n         * while the user is dragging a drag item instance.\n         */\n        this.pointerUp = new Subject();\n        /**\n         * Emits when the viewport has been scrolled while the user is dragging an item.\n         * @deprecated To be turned into a private member. Use the `scrolled` method instead.\n         * @breaking-change 13.0.0\n         */\n        this.scroll = new Subject();\n        /**\n         * Event listener that will prevent the default browser action while the user is dragging.\n         * @param event Event whose default action should be prevented.\n         */\n        this._preventDefaultWhileDragging = (event) => {\n            if (this._activeDragInstances.length > 0) {\n                event.preventDefault();\n            }\n        };\n        /** Event listener for `touchmove` that is bound even if no dragging is happening. */\n        this._persistentTouchmoveListener = (event) => {\n            if (this._activeDragInstances.length > 0) {\n                // Note that we only want to prevent the default action after dragging has actually started.\n                // Usually this is the same time at which the item is added to the `_activeDragInstances`,\n                // but it could be pushed back if the user has set up a drag delay or threshold.\n                if (this._activeDragInstances.some(this._draggingPredicate)) {\n                    event.preventDefault();\n                }\n                this.pointerMove.next(event);\n            }\n        };\n        this._document = _document;\n    }\n    /** Adds a drop container to the registry. */\n    registerDropContainer(drop) {\n        if (!this._dropInstances.has(drop)) {\n            this._dropInstances.add(drop);\n        }\n    }\n    /** Adds a drag item instance to the registry. */\n    registerDragItem(drag) {\n        this._dragInstances.add(drag);\n        // The `touchmove` event gets bound once, ahead of time, because WebKit\n        // won't preventDefault on a dynamically-added `touchmove` listener.\n        // See https://bugs.webkit.org/show_bug.cgi?id=184250.\n        if (this._dragInstances.size === 1) {\n            this._ngZone.runOutsideAngular(() => {\n                // The event handler has to be explicitly active,\n                // because newer browsers make it passive by default.\n                this._document.addEventListener('touchmove', this._persistentTouchmoveListener, activeCapturingEventOptions);\n            });\n        }\n    }\n    /** Removes a drop container from the registry. */\n    removeDropContainer(drop) {\n        this._dropInstances.delete(drop);\n    }\n    /** Removes a drag item instance from the registry. */\n    removeDragItem(drag) {\n        this._dragInstances.delete(drag);\n        this.stopDragging(drag);\n        if (this._dragInstances.size === 0) {\n            this._document.removeEventListener('touchmove', this._persistentTouchmoveListener, activeCapturingEventOptions);\n        }\n    }\n    /**\n     * Starts the dragging sequence for a drag instance.\n     * @param drag Drag instance which is being dragged.\n     * @param event Event that initiated the dragging.\n     */\n    startDragging(drag, event) {\n        // Do not process the same drag twice to avoid memory leaks and redundant listeners\n        if (this._activeDragInstances.indexOf(drag) > -1) {\n            return;\n        }\n        this._loadResets();\n        this._activeDragInstances.push(drag);\n        if (this._activeDragInstances.length === 1) {\n            const isTouchEvent = event.type.startsWith('touch');\n            // We explicitly bind __active__ listeners here, because newer browsers will default to\n            // passive ones for `mousemove` and `touchmove`. The events need to be active, because we\n            // use `preventDefault` to prevent the page from scrolling while the user is dragging.\n            this._globalListeners\n                .set(isTouchEvent ? 'touchend' : 'mouseup', {\n                handler: (e) => this.pointerUp.next(e),\n                options: true,\n            })\n                .set('scroll', {\n                handler: (e) => this.scroll.next(e),\n                // Use capturing so that we pick up scroll changes in any scrollable nodes that aren't\n                // the document. See https://github.com/angular/components/issues/17144.\n                options: true,\n            })\n                // Preventing the default action on `mousemove` isn't enough to disable text selection\n                // on Safari so we need to prevent the selection event as well. Alternatively this can\n                // be done by setting `user-select: none` on the `body`, however it has causes a style\n                // recalculation which can be expensive on pages with a lot of elements.\n                .set('selectstart', {\n                handler: this._preventDefaultWhileDragging,\n                options: activeCapturingEventOptions,\n            });\n            // We don't have to bind a move event for touch drag sequences, because\n            // we already have a persistent global one bound from `registerDragItem`.\n            if (!isTouchEvent) {\n                this._globalListeners.set('mousemove', {\n                    handler: (e) => this.pointerMove.next(e),\n                    options: activeCapturingEventOptions,\n                });\n            }\n            this._ngZone.runOutsideAngular(() => {\n                this._globalListeners.forEach((config, name) => {\n                    this._document.addEventListener(name, config.handler, config.options);\n                });\n            });\n        }\n    }\n    /** Stops dragging a drag item instance. */\n    stopDragging(drag) {\n        const index = this._activeDragInstances.indexOf(drag);\n        if (index > -1) {\n            this._activeDragInstances.splice(index, 1);\n            if (this._activeDragInstances.length === 0) {\n                this._clearGlobalListeners();\n            }\n        }\n    }\n    /** Gets whether a drag item instance is currently being dragged. */\n    isDragging(drag) {\n        return this._activeDragInstances.indexOf(drag) > -1;\n    }\n    /**\n     * Gets a stream that will emit when any element on the page is scrolled while an item is being\n     * dragged.\n     * @param shadowRoot Optional shadow root that the current dragging sequence started from.\n     *   Top-level listeners won't pick up events coming from the shadow DOM so this parameter can\n     *   be used to include an additional top-level listener at the shadow root level.\n     */\n    scrolled(shadowRoot) {\n        const streams = [this.scroll];\n        if (shadowRoot && shadowRoot !== this._document) {\n            // Note that this is basically the same as `fromEvent` from rxjs, but we do it ourselves,\n            // because we want to guarantee that the event is bound outside of the `NgZone`. With\n            // `fromEvent` it'll only happen if the subscription is outside the `NgZone`.\n            streams.push(new Observable((observer) => {\n                return this._ngZone.runOutsideAngular(() => {\n                    const eventOptions = true;\n                    const callback = (event) => {\n                        if (this._activeDragInstances.length) {\n                            observer.next(event);\n                        }\n                    };\n                    shadowRoot.addEventListener('scroll', callback, eventOptions);\n                    return () => {\n                        shadowRoot.removeEventListener('scroll', callback, eventOptions);\n                    };\n                });\n            }));\n        }\n        return merge(...streams);\n    }\n    ngOnDestroy() {\n        this._dragInstances.forEach(instance => this.removeDragItem(instance));\n        this._dropInstances.forEach(instance => this.removeDropContainer(instance));\n        this._clearGlobalListeners();\n        this.pointerMove.complete();\n        this.pointerUp.complete();\n    }\n    /** Clears out the global event listeners from the `document`. */\n    _clearGlobalListeners() {\n        this._globalListeners.forEach((config, name) => {\n            this._document.removeEventListener(name, config.handler, config.options);\n        });\n        this._globalListeners.clear();\n    }\n    // TODO(crisbeto): abstract this away into something reusable.\n    /** Loads the CSS resets needed for the module to work correctly. */\n    _loadResets() {\n        if (!activeApps.has(this._appRef)) {\n            activeApps.add(this._appRef);\n            const componentRef = createComponent(_ResetsLoader, {\n                environmentInjector: this._environmentInjector,\n            });\n            this._appRef.onDestroy(() => {\n                activeApps.delete(this._appRef);\n                if (activeApps.size === 0) {\n                    componentRef.destroy();\n                }\n            });\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: DragDropRegistry, deps: [{ token: i0.NgZone }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: DragDropRegistry, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: DragDropRegistry, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }] });\n\n/** Default configuration to be used when creating a `DragRef`. */\nconst DEFAULT_CONFIG = {\n    dragStartThreshold: 5,\n    pointerDirectionChangeThreshold: 5,\n};\n/**\n * Service that allows for drag-and-drop functionality to be attached to DOM elements.\n */\nclass DragDrop {\n    constructor(_document, _ngZone, _viewportRuler, _dragDropRegistry) {\n        this._document = _document;\n        this._ngZone = _ngZone;\n        this._viewportRuler = _viewportRuler;\n        this._dragDropRegistry = _dragDropRegistry;\n    }\n    /**\n     * Turns an element into a draggable item.\n     * @param element Element to which to attach the dragging functionality.\n     * @param config Object used to configure the dragging behavior.\n     */\n    createDrag(element, config = DEFAULT_CONFIG) {\n        return new DragRef(element, config, this._document, this._ngZone, this._viewportRuler, this._dragDropRegistry);\n    }\n    /**\n     * Turns an element into a drop list.\n     * @param element Element to which to attach the drop list functionality.\n     */\n    createDropList(element) {\n        return new DropListRef(element, this._dragDropRegistry, this._document, this._ngZone, this._viewportRuler);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: DragDrop, deps: [{ token: DOCUMENT }, { token: i0.NgZone }, { token: i1.ViewportRuler }, { token: DragDropRegistry }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: DragDrop, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: DragDrop, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i0.NgZone }, { type: i1.ViewportRuler }, { type: DragDropRegistry }] });\n\n/**\n * Injection token that can be used for a `CdkDrag` to provide itself as a parent to the\n * drag-specific child directive (`CdkDragHandle`, `CdkDragPreview` etc.). Used primarily\n * to avoid circular imports.\n * @docs-private\n */\nconst CDK_DRAG_PARENT = new InjectionToken('CDK_DRAG_PARENT');\n\n/**\n * Asserts that a particular node is an element.\n * @param node Node to be checked.\n * @param name Name to attach to the error message.\n */\nfunction assertElementNode(node, name) {\n    if (node.nodeType !== 1) {\n        throw Error(`${name} must be attached to an element node. ` + `Currently attached to \"${node.nodeName}\".`);\n    }\n}\n\n/**\n * Injection token that can be used to reference instances of `CdkDragHandle`. It serves as\n * alternative token to the actual `CdkDragHandle` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DRAG_HANDLE = new InjectionToken('CdkDragHandle');\n/** Handle that can be used to drag a CdkDrag instance. */\nclass CdkDragHandle {\n    /** Whether starting to drag through this handle is disabled. */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(value) {\n        this._disabled = value;\n        this._stateChanges.next(this);\n    }\n    constructor(element, _parentDrag) {\n        this.element = element;\n        this._parentDrag = _parentDrag;\n        /** Emits when the state of the handle has changed. */\n        this._stateChanges = new Subject();\n        this._disabled = false;\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            assertElementNode(element.nativeElement, 'cdkDragHandle');\n        }\n        _parentDrag?._addHandle(this);\n    }\n    ngOnDestroy() {\n        this._parentDrag?._removeHandle(this);\n        this._stateChanges.complete();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkDragHandle, deps: [{ token: i0.ElementRef }, { token: CDK_DRAG_PARENT, optional: true, skipSelf: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"17.2.0\", type: CdkDragHandle, isStandalone: true, selector: \"[cdkDragHandle]\", inputs: { disabled: [\"cdkDragHandleDisabled\", \"disabled\", booleanAttribute] }, host: { classAttribute: \"cdk-drag-handle\" }, providers: [{ provide: CDK_DRAG_HANDLE, useExisting: CdkDragHandle }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkDragHandle, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkDragHandle]',\n                    standalone: true,\n                    host: {\n                        'class': 'cdk-drag-handle',\n                    },\n                    providers: [{ provide: CDK_DRAG_HANDLE, useExisting: CdkDragHandle }],\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [CDK_DRAG_PARENT]\n                }, {\n                    type: Optional\n                }, {\n                    type: SkipSelf\n                }] }], propDecorators: { disabled: [{\n                type: Input,\n                args: [{ alias: 'cdkDragHandleDisabled', transform: booleanAttribute }]\n            }] } });\n\n/**\n * Injection token that can be used to configure the\n * behavior of the drag&drop-related components.\n */\nconst CDK_DRAG_CONFIG = new InjectionToken('CDK_DRAG_CONFIG');\n\nconst DRAG_HOST_CLASS = 'cdk-drag';\n/**\n * Injection token that can be used to reference instances of `CdkDropList`. It serves as\n * alternative token to the actual `CdkDropList` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DROP_LIST = new InjectionToken('CdkDropList');\n/** Element that can be moved inside a CdkDropList container. */\nclass CdkDrag {\n    static { this._dragInstances = []; }\n    /** Whether starting to drag this element is disabled. */\n    get disabled() {\n        return this._disabled || (this.dropContainer && this.dropContainer.disabled);\n    }\n    set disabled(value) {\n        this._disabled = value;\n        this._dragRef.disabled = this._disabled;\n    }\n    constructor(\n    /** Element that the draggable is attached to. */\n    element, \n    /** Droppable container that the draggable is a part of. */\n    dropContainer, \n    /**\n     * @deprecated `_document` parameter no longer being used and will be removed.\n     * @breaking-change 12.0.0\n     */\n    _document, _ngZone, _viewContainerRef, config, _dir, dragDrop, _changeDetectorRef, _selfHandle, _parentDrag) {\n        this.element = element;\n        this.dropContainer = dropContainer;\n        this._ngZone = _ngZone;\n        this._viewContainerRef = _viewContainerRef;\n        this._dir = _dir;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._selfHandle = _selfHandle;\n        this._parentDrag = _parentDrag;\n        this._destroyed = new Subject();\n        this._handles = new BehaviorSubject([]);\n        /** Emits when the user starts dragging the item. */\n        this.started = new EventEmitter();\n        /** Emits when the user has released a drag item, before any animations have started. */\n        this.released = new EventEmitter();\n        /** Emits when the user stops dragging an item in the container. */\n        this.ended = new EventEmitter();\n        /** Emits when the user has moved the item into a new container. */\n        this.entered = new EventEmitter();\n        /** Emits when the user removes the item its container by dragging it into another container. */\n        this.exited = new EventEmitter();\n        /** Emits when the user drops the item inside a container. */\n        this.dropped = new EventEmitter();\n        /**\n         * Emits as the user is dragging the item. Use with caution,\n         * because this event will fire for every pixel that the user has dragged.\n         */\n        this.moved = new Observable((observer) => {\n            const subscription = this._dragRef.moved\n                .pipe(map(movedEvent => ({\n                source: this,\n                pointerPosition: movedEvent.pointerPosition,\n                event: movedEvent.event,\n                delta: movedEvent.delta,\n                distance: movedEvent.distance,\n            })))\n                .subscribe(observer);\n            return () => {\n                subscription.unsubscribe();\n            };\n        });\n        this._dragRef = dragDrop.createDrag(element, {\n            dragStartThreshold: config && config.dragStartThreshold != null ? config.dragStartThreshold : 5,\n            pointerDirectionChangeThreshold: config && config.pointerDirectionChangeThreshold != null\n                ? config.pointerDirectionChangeThreshold\n                : 5,\n            zIndex: config?.zIndex,\n        });\n        this._dragRef.data = this;\n        // We have to keep track of the drag instances in order to be able to match an element to\n        // a drag instance. We can't go through the global registry of `DragRef`, because the root\n        // element could be different.\n        CdkDrag._dragInstances.push(this);\n        if (config) {\n            this._assignDefaults(config);\n        }\n        // Note that usually the container is assigned when the drop list is picks up the item, but in\n        // some cases (mainly transplanted views with OnPush, see #18341) we may end up in a situation\n        // where there are no items on the first change detection pass, but the items get picked up as\n        // soon as the user triggers another pass by dragging. This is a problem, because the item would\n        // have to switch from standalone mode to drag mode in the middle of the dragging sequence which\n        // is too late since the two modes save different kinds of information. We work around it by\n        // assigning the drop container both from here and the list.\n        if (dropContainer) {\n            this._dragRef._withDropContainer(dropContainer._dropListRef);\n            dropContainer.addItem(this);\n        }\n        this._syncInputs(this._dragRef);\n        this._handleEvents(this._dragRef);\n    }\n    /**\n     * Returns the element that is being used as a placeholder\n     * while the current element is being dragged.\n     */\n    getPlaceholderElement() {\n        return this._dragRef.getPlaceholderElement();\n    }\n    /** Returns the root draggable element. */\n    getRootElement() {\n        return this._dragRef.getRootElement();\n    }\n    /** Resets a standalone drag item to its initial position. */\n    reset() {\n        this._dragRef.reset();\n    }\n    /**\n     * Gets the pixel coordinates of the draggable outside of a drop container.\n     */\n    getFreeDragPosition() {\n        return this._dragRef.getFreeDragPosition();\n    }\n    /**\n     * Sets the current position in pixels the draggable outside of a drop container.\n     * @param value New position to be set.\n     */\n    setFreeDragPosition(value) {\n        this._dragRef.setFreeDragPosition(value);\n    }\n    ngAfterViewInit() {\n        // Normally this isn't in the zone, but it can cause major performance regressions for apps\n        // using `zone-patch-rxjs` because it'll trigger a change detection when it unsubscribes.\n        this._ngZone.runOutsideAngular(() => {\n            // We need to wait for the zone to stabilize, in order for the reference\n            // element to be in the proper place in the DOM. This is mostly relevant\n            // for draggable elements inside portals since they get stamped out in\n            // their original DOM position and then they get transferred to the portal.\n            this._ngZone.onStable.pipe(take(1), takeUntil(this._destroyed)).subscribe(() => {\n                this._updateRootElement();\n                this._setupHandlesListener();\n                if (this.freeDragPosition) {\n                    this._dragRef.setFreeDragPosition(this.freeDragPosition);\n                }\n            });\n        });\n    }\n    ngOnChanges(changes) {\n        const rootSelectorChange = changes['rootElementSelector'];\n        const positionChange = changes['freeDragPosition'];\n        // We don't have to react to the first change since it's being\n        // handled in `ngAfterViewInit` where it needs to be deferred.\n        if (rootSelectorChange && !rootSelectorChange.firstChange) {\n            this._updateRootElement();\n        }\n        // Skip the first change since it's being handled in `ngAfterViewInit`.\n        if (positionChange && !positionChange.firstChange && this.freeDragPosition) {\n            this._dragRef.setFreeDragPosition(this.freeDragPosition);\n        }\n    }\n    ngOnDestroy() {\n        if (this.dropContainer) {\n            this.dropContainer.removeItem(this);\n        }\n        const index = CdkDrag._dragInstances.indexOf(this);\n        if (index > -1) {\n            CdkDrag._dragInstances.splice(index, 1);\n        }\n        // Unnecessary in most cases, but used to avoid extra change detections with `zone-paths-rxjs`.\n        this._ngZone.runOutsideAngular(() => {\n            this._handles.complete();\n            this._destroyed.next();\n            this._destroyed.complete();\n            this._dragRef.dispose();\n        });\n    }\n    _addHandle(handle) {\n        const handles = this._handles.getValue();\n        handles.push(handle);\n        this._handles.next(handles);\n    }\n    _removeHandle(handle) {\n        const handles = this._handles.getValue();\n        const index = handles.indexOf(handle);\n        if (index > -1) {\n            handles.splice(index, 1);\n            this._handles.next(handles);\n        }\n    }\n    _setPreviewTemplate(preview) {\n        this._previewTemplate = preview;\n    }\n    _resetPreviewTemplate(preview) {\n        if (preview === this._previewTemplate) {\n            this._previewTemplate = null;\n        }\n    }\n    _setPlaceholderTemplate(placeholder) {\n        this._placeholderTemplate = placeholder;\n    }\n    _resetPlaceholderTemplate(placeholder) {\n        if (placeholder === this._placeholderTemplate) {\n            this._placeholderTemplate = null;\n        }\n    }\n    /** Syncs the root element with the `DragRef`. */\n    _updateRootElement() {\n        const element = this.element.nativeElement;\n        let rootElement = element;\n        if (this.rootElementSelector) {\n            rootElement =\n                element.closest !== undefined\n                    ? element.closest(this.rootElementSelector)\n                    : // Comment tag doesn't have closest method, so use parent's one.\n                        element.parentElement?.closest(this.rootElementSelector);\n        }\n        if (rootElement && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            assertElementNode(rootElement, 'cdkDrag');\n        }\n        this._dragRef.withRootElement(rootElement || element);\n    }\n    /** Gets the boundary element, based on the `boundaryElement` value. */\n    _getBoundaryElement() {\n        const boundary = this.boundaryElement;\n        if (!boundary) {\n            return null;\n        }\n        if (typeof boundary === 'string') {\n            return this.element.nativeElement.closest(boundary);\n        }\n        return coerceElement(boundary);\n    }\n    /** Syncs the inputs of the CdkDrag with the options of the underlying DragRef. */\n    _syncInputs(ref) {\n        ref.beforeStarted.subscribe(() => {\n            if (!ref.isDragging()) {\n                const dir = this._dir;\n                const dragStartDelay = this.dragStartDelay;\n                const placeholder = this._placeholderTemplate\n                    ? {\n                        template: this._placeholderTemplate.templateRef,\n                        context: this._placeholderTemplate.data,\n                        viewContainer: this._viewContainerRef,\n                    }\n                    : null;\n                const preview = this._previewTemplate\n                    ? {\n                        template: this._previewTemplate.templateRef,\n                        context: this._previewTemplate.data,\n                        matchSize: this._previewTemplate.matchSize,\n                        viewContainer: this._viewContainerRef,\n                    }\n                    : null;\n                ref.disabled = this.disabled;\n                ref.lockAxis = this.lockAxis;\n                ref.dragStartDelay =\n                    typeof dragStartDelay === 'object' && dragStartDelay\n                        ? dragStartDelay\n                        : coerceNumberProperty(dragStartDelay);\n                ref.constrainPosition = this.constrainPosition;\n                ref.previewClass = this.previewClass;\n                ref\n                    .withBoundaryElement(this._getBoundaryElement())\n                    .withPlaceholderTemplate(placeholder)\n                    .withPreviewTemplate(preview)\n                    .withPreviewContainer(this.previewContainer || 'global');\n                if (dir) {\n                    ref.withDirection(dir.value);\n                }\n            }\n        });\n        // This only needs to be resolved once.\n        ref.beforeStarted.pipe(take(1)).subscribe(() => {\n            // If we managed to resolve a parent through DI, use it.\n            if (this._parentDrag) {\n                ref.withParent(this._parentDrag._dragRef);\n                return;\n            }\n            // Otherwise fall back to resolving the parent by looking up the DOM. This can happen if\n            // the item was projected into another item by something like `ngTemplateOutlet`.\n            let parent = this.element.nativeElement.parentElement;\n            while (parent) {\n                if (parent.classList.contains(DRAG_HOST_CLASS)) {\n                    ref.withParent(CdkDrag._dragInstances.find(drag => {\n                        return drag.element.nativeElement === parent;\n                    })?._dragRef || null);\n                    break;\n                }\n                parent = parent.parentElement;\n            }\n        });\n    }\n    /** Handles the events from the underlying `DragRef`. */\n    _handleEvents(ref) {\n        ref.started.subscribe(startEvent => {\n            this.started.emit({ source: this, event: startEvent.event });\n            // Since all of these events run outside of change detection,\n            // we need to ensure that everything is marked correctly.\n            this._changeDetectorRef.markForCheck();\n        });\n        ref.released.subscribe(releaseEvent => {\n            this.released.emit({ source: this, event: releaseEvent.event });\n        });\n        ref.ended.subscribe(endEvent => {\n            this.ended.emit({\n                source: this,\n                distance: endEvent.distance,\n                dropPoint: endEvent.dropPoint,\n                event: endEvent.event,\n            });\n            // Since all of these events run outside of change detection,\n            // we need to ensure that everything is marked correctly.\n            this._changeDetectorRef.markForCheck();\n        });\n        ref.entered.subscribe(enterEvent => {\n            this.entered.emit({\n                container: enterEvent.container.data,\n                item: this,\n                currentIndex: enterEvent.currentIndex,\n            });\n        });\n        ref.exited.subscribe(exitEvent => {\n            this.exited.emit({\n                container: exitEvent.container.data,\n                item: this,\n            });\n        });\n        ref.dropped.subscribe(dropEvent => {\n            this.dropped.emit({\n                previousIndex: dropEvent.previousIndex,\n                currentIndex: dropEvent.currentIndex,\n                previousContainer: dropEvent.previousContainer.data,\n                container: dropEvent.container.data,\n                isPointerOverContainer: dropEvent.isPointerOverContainer,\n                item: this,\n                distance: dropEvent.distance,\n                dropPoint: dropEvent.dropPoint,\n                event: dropEvent.event,\n            });\n        });\n    }\n    /** Assigns the default input values based on a provided config object. */\n    _assignDefaults(config) {\n        const { lockAxis, dragStartDelay, constrainPosition, previewClass, boundaryElement, draggingDisabled, rootElementSelector, previewContainer, } = config;\n        this.disabled = draggingDisabled == null ? false : draggingDisabled;\n        this.dragStartDelay = dragStartDelay || 0;\n        if (lockAxis) {\n            this.lockAxis = lockAxis;\n        }\n        if (constrainPosition) {\n            this.constrainPosition = constrainPosition;\n        }\n        if (previewClass) {\n            this.previewClass = previewClass;\n        }\n        if (boundaryElement) {\n            this.boundaryElement = boundaryElement;\n        }\n        if (rootElementSelector) {\n            this.rootElementSelector = rootElementSelector;\n        }\n        if (previewContainer) {\n            this.previewContainer = previewContainer;\n        }\n    }\n    /** Sets up the listener that syncs the handles with the drag ref. */\n    _setupHandlesListener() {\n        // Listen for any newly-added handles.\n        this._handles\n            .pipe(\n        // Sync the new handles with the DragRef.\n        tap(handles => {\n            const handleElements = handles.map(handle => handle.element);\n            // Usually handles are only allowed to be a descendant of the drag element, but if\n            // the consumer defined a different drag root, we should allow the drag element\n            // itself to be a handle too.\n            if (this._selfHandle && this.rootElementSelector) {\n                handleElements.push(this.element);\n            }\n            this._dragRef.withHandles(handleElements);\n        }), \n        // Listen if the state of any of the handles changes.\n        switchMap((handles) => {\n            return merge(...handles.map(item => item._stateChanges.pipe(startWith(item))));\n        }), takeUntil(this._destroyed))\n            .subscribe(handleInstance => {\n            // Enabled/disable the handle that changed in the DragRef.\n            const dragRef = this._dragRef;\n            const handle = handleInstance.element.nativeElement;\n            handleInstance.disabled ? dragRef.disableHandle(handle) : dragRef.enableHandle(handle);\n        });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkDrag, deps: [{ token: i0.ElementRef }, { token: CDK_DROP_LIST, optional: true, skipSelf: true }, { token: DOCUMENT }, { token: i0.NgZone }, { token: i0.ViewContainerRef }, { token: CDK_DRAG_CONFIG, optional: true }, { token: i1$1.Directionality, optional: true }, { token: DragDrop }, { token: i0.ChangeDetectorRef }, { token: CDK_DRAG_HANDLE, optional: true, self: true }, { token: CDK_DRAG_PARENT, optional: true, skipSelf: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"17.2.0\", type: CdkDrag, isStandalone: true, selector: \"[cdkDrag]\", inputs: { data: [\"cdkDragData\", \"data\"], lockAxis: [\"cdkDragLockAxis\", \"lockAxis\"], rootElementSelector: [\"cdkDragRootElement\", \"rootElementSelector\"], boundaryElement: [\"cdkDragBoundary\", \"boundaryElement\"], dragStartDelay: [\"cdkDragStartDelay\", \"dragStartDelay\"], freeDragPosition: [\"cdkDragFreeDragPosition\", \"freeDragPosition\"], disabled: [\"cdkDragDisabled\", \"disabled\", booleanAttribute], constrainPosition: [\"cdkDragConstrainPosition\", \"constrainPosition\"], previewClass: [\"cdkDragPreviewClass\", \"previewClass\"], previewContainer: [\"cdkDragPreviewContainer\", \"previewContainer\"] }, outputs: { started: \"cdkDragStarted\", released: \"cdkDragReleased\", ended: \"cdkDragEnded\", entered: \"cdkDragEntered\", exited: \"cdkDragExited\", dropped: \"cdkDragDropped\", moved: \"cdkDragMoved\" }, host: { properties: { \"class.cdk-drag-disabled\": \"disabled\", \"class.cdk-drag-dragging\": \"_dragRef.isDragging()\" }, classAttribute: \"cdk-drag\" }, providers: [{ provide: CDK_DRAG_PARENT, useExisting: CdkDrag }], exportAs: [\"cdkDrag\"], usesOnChanges: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkDrag, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkDrag]',\n                    exportAs: 'cdkDrag',\n                    standalone: true,\n                    host: {\n                        'class': DRAG_HOST_CLASS,\n                        '[class.cdk-drag-disabled]': 'disabled',\n                        '[class.cdk-drag-dragging]': '_dragRef.isDragging()',\n                    },\n                    providers: [{ provide: CDK_DRAG_PARENT, useExisting: CdkDrag }],\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [CDK_DROP_LIST]\n                }, {\n                    type: Optional\n                }, {\n                    type: SkipSelf\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i0.NgZone }, { type: i0.ViewContainerRef }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [CDK_DRAG_CONFIG]\n                }] }, { type: i1$1.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: DragDrop }, { type: i0.ChangeDetectorRef }, { type: CdkDragHandle, decorators: [{\n                    type: Optional\n                }, {\n                    type: Self\n                }, {\n                    type: Inject,\n                    args: [CDK_DRAG_HANDLE]\n                }] }, { type: CdkDrag, decorators: [{\n                    type: Optional\n                }, {\n                    type: SkipSelf\n                }, {\n                    type: Inject,\n                    args: [CDK_DRAG_PARENT]\n                }] }], propDecorators: { data: [{\n                type: Input,\n                args: ['cdkDragData']\n            }], lockAxis: [{\n                type: Input,\n                args: ['cdkDragLockAxis']\n            }], rootElementSelector: [{\n                type: Input,\n                args: ['cdkDragRootElement']\n            }], boundaryElement: [{\n                type: Input,\n                args: ['cdkDragBoundary']\n            }], dragStartDelay: [{\n                type: Input,\n                args: ['cdkDragStartDelay']\n            }], freeDragPosition: [{\n                type: Input,\n                args: ['cdkDragFreeDragPosition']\n            }], disabled: [{\n                type: Input,\n                args: [{ alias: 'cdkDragDisabled', transform: booleanAttribute }]\n            }], constrainPosition: [{\n                type: Input,\n                args: ['cdkDragConstrainPosition']\n            }], previewClass: [{\n                type: Input,\n                args: ['cdkDragPreviewClass']\n            }], previewContainer: [{\n                type: Input,\n                args: ['cdkDragPreviewContainer']\n            }], started: [{\n                type: Output,\n                args: ['cdkDragStarted']\n            }], released: [{\n                type: Output,\n                args: ['cdkDragReleased']\n            }], ended: [{\n                type: Output,\n                args: ['cdkDragEnded']\n            }], entered: [{\n                type: Output,\n                args: ['cdkDragEntered']\n            }], exited: [{\n                type: Output,\n                args: ['cdkDragExited']\n            }], dropped: [{\n                type: Output,\n                args: ['cdkDragDropped']\n            }], moved: [{\n                type: Output,\n                args: ['cdkDragMoved']\n            }] } });\n\n/**\n * Injection token that can be used to reference instances of `CdkDropListGroup`. It serves as\n * alternative token to the actual `CdkDropListGroup` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DROP_LIST_GROUP = new InjectionToken('CdkDropListGroup');\n/**\n * Declaratively connects sibling `cdkDropList` instances together. All of the `cdkDropList`\n * elements that are placed inside a `cdkDropListGroup` will be connected to each other\n * automatically. Can be used as an alternative to the `cdkDropListConnectedTo` input\n * from `cdkDropList`.\n */\nclass CdkDropListGroup {\n    constructor() {\n        /** Drop lists registered inside the group. */\n        this._items = new Set();\n        /** Whether starting a dragging sequence from inside this group is disabled. */\n        this.disabled = false;\n    }\n    ngOnDestroy() {\n        this._items.clear();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkDropListGroup, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"17.2.0\", type: CdkDropListGroup, isStandalone: true, selector: \"[cdkDropListGroup]\", inputs: { disabled: [\"cdkDropListGroupDisabled\", \"disabled\", booleanAttribute] }, providers: [{ provide: CDK_DROP_LIST_GROUP, useExisting: CdkDropListGroup }], exportAs: [\"cdkDropListGroup\"], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkDropListGroup, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkDropListGroup]',\n                    exportAs: 'cdkDropListGroup',\n                    standalone: true,\n                    providers: [{ provide: CDK_DROP_LIST_GROUP, useExisting: CdkDropListGroup }],\n                }]\n        }], propDecorators: { disabled: [{\n                type: Input,\n                args: [{ alias: 'cdkDropListGroupDisabled', transform: booleanAttribute }]\n            }] } });\n\n/** Counter used to generate unique ids for drop zones. */\nlet _uniqueIdCounter = 0;\n/** Container that wraps a set of draggable items. */\nclass CdkDropList {\n    /** Keeps track of the drop lists that are currently on the page. */\n    static { this._dropLists = []; }\n    /** Whether starting a dragging sequence from this container is disabled. */\n    get disabled() {\n        return this._disabled || (!!this._group && this._group.disabled);\n    }\n    set disabled(value) {\n        // Usually we sync the directive and ref state right before dragging starts, in order to have\n        // a single point of failure and to avoid having to use setters for everything. `disabled` is\n        // a special case, because it can prevent the `beforeStarted` event from firing, which can lock\n        // the user in a disabled state, so we also need to sync it as it's being set.\n        this._dropListRef.disabled = this._disabled = value;\n    }\n    constructor(\n    /** Element that the drop list is attached to. */\n    element, dragDrop, _changeDetectorRef, _scrollDispatcher, _dir, _group, config) {\n        this.element = element;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._scrollDispatcher = _scrollDispatcher;\n        this._dir = _dir;\n        this._group = _group;\n        /** Emits when the list has been destroyed. */\n        this._destroyed = new Subject();\n        /**\n         * Other draggable containers that this container is connected to and into which the\n         * container's items can be transferred. Can either be references to other drop containers,\n         * or their unique IDs.\n         */\n        this.connectedTo = [];\n        /**\n         * Unique ID for the drop zone. Can be used as a reference\n         * in the `connectedTo` of another `CdkDropList`.\n         */\n        this.id = `cdk-drop-list-${_uniqueIdCounter++}`;\n        /**\n         * Function that is used to determine whether an item\n         * is allowed to be moved into a drop container.\n         */\n        this.enterPredicate = () => true;\n        /** Functions that is used to determine whether an item can be sorted into a particular index. */\n        this.sortPredicate = () => true;\n        /** Emits when the user drops an item inside the container. */\n        this.dropped = new EventEmitter();\n        /**\n         * Emits when the user has moved a new drag item into this container.\n         */\n        this.entered = new EventEmitter();\n        /**\n         * Emits when the user removes an item from the container\n         * by dragging it into another container.\n         */\n        this.exited = new EventEmitter();\n        /** Emits as the user is swapping items while actively dragging. */\n        this.sorted = new EventEmitter();\n        /**\n         * Keeps track of the items that are registered with this container. Historically we used to\n         * do this with a `ContentChildren` query, however queries don't handle transplanted views very\n         * well which means that we can't handle cases like dragging the headers of a `mat-table`\n         * correctly. What we do instead is to have the items register themselves with the container\n         * and then we sort them based on their position in the DOM.\n         */\n        this._unsortedItems = new Set();\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            assertElementNode(element.nativeElement, 'cdkDropList');\n        }\n        this._dropListRef = dragDrop.createDropList(element);\n        this._dropListRef.data = this;\n        if (config) {\n            this._assignDefaults(config);\n        }\n        this._dropListRef.enterPredicate = (drag, drop) => {\n            return this.enterPredicate(drag.data, drop.data);\n        };\n        this._dropListRef.sortPredicate = (index, drag, drop) => {\n            return this.sortPredicate(index, drag.data, drop.data);\n        };\n        this._setupInputSyncSubscription(this._dropListRef);\n        this._handleEvents(this._dropListRef);\n        CdkDropList._dropLists.push(this);\n        if (_group) {\n            _group._items.add(this);\n        }\n    }\n    /** Registers an items with the drop list. */\n    addItem(item) {\n        this._unsortedItems.add(item);\n        if (this._dropListRef.isDragging()) {\n            this._syncItemsWithRef();\n        }\n    }\n    /** Removes an item from the drop list. */\n    removeItem(item) {\n        this._unsortedItems.delete(item);\n        if (this._dropListRef.isDragging()) {\n            this._syncItemsWithRef();\n        }\n    }\n    /** Gets the registered items in the list, sorted by their position in the DOM. */\n    getSortedItems() {\n        return Array.from(this._unsortedItems).sort((a, b) => {\n            const documentPosition = a._dragRef\n                .getVisibleElement()\n                .compareDocumentPosition(b._dragRef.getVisibleElement());\n            // `compareDocumentPosition` returns a bitmask so we have to use a bitwise operator.\n            // https://developer.mozilla.org/en-US/docs/Web/API/Node/compareDocumentPosition\n            // tslint:disable-next-line:no-bitwise\n            return documentPosition & Node.DOCUMENT_POSITION_FOLLOWING ? -1 : 1;\n        });\n    }\n    ngOnDestroy() {\n        const index = CdkDropList._dropLists.indexOf(this);\n        if (index > -1) {\n            CdkDropList._dropLists.splice(index, 1);\n        }\n        if (this._group) {\n            this._group._items.delete(this);\n        }\n        this._unsortedItems.clear();\n        this._dropListRef.dispose();\n        this._destroyed.next();\n        this._destroyed.complete();\n    }\n    /** Syncs the inputs of the CdkDropList with the options of the underlying DropListRef. */\n    _setupInputSyncSubscription(ref) {\n        if (this._dir) {\n            this._dir.change\n                .pipe(startWith(this._dir.value), takeUntil(this._destroyed))\n                .subscribe(value => ref.withDirection(value));\n        }\n        ref.beforeStarted.subscribe(() => {\n            const siblings = coerceArray(this.connectedTo).map(drop => {\n                if (typeof drop === 'string') {\n                    const correspondingDropList = CdkDropList._dropLists.find(list => list.id === drop);\n                    if (!correspondingDropList && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                        console.warn(`CdkDropList could not find connected drop list with id \"${drop}\"`);\n                    }\n                    return correspondingDropList;\n                }\n                return drop;\n            });\n            if (this._group) {\n                this._group._items.forEach(drop => {\n                    if (siblings.indexOf(drop) === -1) {\n                        siblings.push(drop);\n                    }\n                });\n            }\n            // Note that we resolve the scrollable parents here so that we delay the resolution\n            // as long as possible, ensuring that the element is in its final place in the DOM.\n            if (!this._scrollableParentsResolved) {\n                const scrollableParents = this._scrollDispatcher\n                    .getAncestorScrollContainers(this.element)\n                    .map(scrollable => scrollable.getElementRef().nativeElement);\n                this._dropListRef.withScrollableParents(scrollableParents);\n                // Only do this once since it involves traversing the DOM and the parents\n                // shouldn't be able to change without the drop list being destroyed.\n                this._scrollableParentsResolved = true;\n            }\n            ref.disabled = this.disabled;\n            ref.lockAxis = this.lockAxis;\n            ref.sortingDisabled = this.sortingDisabled;\n            ref.autoScrollDisabled = this.autoScrollDisabled;\n            ref.autoScrollStep = coerceNumberProperty(this.autoScrollStep, 2);\n            ref\n                .connectedTo(siblings.filter(drop => drop && drop !== this).map(list => list._dropListRef))\n                .withOrientation(this.orientation);\n        });\n    }\n    /** Handles events from the underlying DropListRef. */\n    _handleEvents(ref) {\n        ref.beforeStarted.subscribe(() => {\n            this._syncItemsWithRef();\n            this._changeDetectorRef.markForCheck();\n        });\n        ref.entered.subscribe(event => {\n            this.entered.emit({\n                container: this,\n                item: event.item.data,\n                currentIndex: event.currentIndex,\n            });\n        });\n        ref.exited.subscribe(event => {\n            this.exited.emit({\n                container: this,\n                item: event.item.data,\n            });\n            this._changeDetectorRef.markForCheck();\n        });\n        ref.sorted.subscribe(event => {\n            this.sorted.emit({\n                previousIndex: event.previousIndex,\n                currentIndex: event.currentIndex,\n                container: this,\n                item: event.item.data,\n            });\n        });\n        ref.dropped.subscribe(dropEvent => {\n            this.dropped.emit({\n                previousIndex: dropEvent.previousIndex,\n                currentIndex: dropEvent.currentIndex,\n                previousContainer: dropEvent.previousContainer.data,\n                container: dropEvent.container.data,\n                item: dropEvent.item.data,\n                isPointerOverContainer: dropEvent.isPointerOverContainer,\n                distance: dropEvent.distance,\n                dropPoint: dropEvent.dropPoint,\n                event: dropEvent.event,\n            });\n            // Mark for check since all of these events run outside of change\n            // detection and we're not guaranteed for something else to have triggered it.\n            this._changeDetectorRef.markForCheck();\n        });\n        merge(ref.receivingStarted, ref.receivingStopped).subscribe(() => this._changeDetectorRef.markForCheck());\n    }\n    /** Assigns the default input values based on a provided config object. */\n    _assignDefaults(config) {\n        const { lockAxis, draggingDisabled, sortingDisabled, listAutoScrollDisabled, listOrientation } = config;\n        this.disabled = draggingDisabled == null ? false : draggingDisabled;\n        this.sortingDisabled = sortingDisabled == null ? false : sortingDisabled;\n        this.autoScrollDisabled = listAutoScrollDisabled == null ? false : listAutoScrollDisabled;\n        this.orientation = listOrientation || 'vertical';\n        if (lockAxis) {\n            this.lockAxis = lockAxis;\n        }\n    }\n    /** Syncs up the registered drag items with underlying drop list ref. */\n    _syncItemsWithRef() {\n        this._dropListRef.withItems(this.getSortedItems().map(item => item._dragRef));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkDropList, deps: [{ token: i0.ElementRef }, { token: DragDrop }, { token: i0.ChangeDetectorRef }, { token: i1.ScrollDispatcher }, { token: i1$1.Directionality, optional: true }, { token: CDK_DROP_LIST_GROUP, optional: true, skipSelf: true }, { token: CDK_DRAG_CONFIG, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"17.2.0\", type: CdkDropList, isStandalone: true, selector: \"[cdkDropList], cdk-drop-list\", inputs: { connectedTo: [\"cdkDropListConnectedTo\", \"connectedTo\"], data: [\"cdkDropListData\", \"data\"], orientation: [\"cdkDropListOrientation\", \"orientation\"], id: \"id\", lockAxis: [\"cdkDropListLockAxis\", \"lockAxis\"], disabled: [\"cdkDropListDisabled\", \"disabled\", booleanAttribute], sortingDisabled: [\"cdkDropListSortingDisabled\", \"sortingDisabled\", booleanAttribute], enterPredicate: [\"cdkDropListEnterPredicate\", \"enterPredicate\"], sortPredicate: [\"cdkDropListSortPredicate\", \"sortPredicate\"], autoScrollDisabled: [\"cdkDropListAutoScrollDisabled\", \"autoScrollDisabled\", booleanAttribute], autoScrollStep: [\"cdkDropListAutoScrollStep\", \"autoScrollStep\"] }, outputs: { dropped: \"cdkDropListDropped\", entered: \"cdkDropListEntered\", exited: \"cdkDropListExited\", sorted: \"cdkDropListSorted\" }, host: { properties: { \"attr.id\": \"id\", \"class.cdk-drop-list-disabled\": \"disabled\", \"class.cdk-drop-list-dragging\": \"_dropListRef.isDragging()\", \"class.cdk-drop-list-receiving\": \"_dropListRef.isReceiving()\" }, classAttribute: \"cdk-drop-list\" }, providers: [\n            // Prevent child drop lists from picking up the same group as their parent.\n            { provide: CDK_DROP_LIST_GROUP, useValue: undefined },\n            { provide: CDK_DROP_LIST, useExisting: CdkDropList },\n        ], exportAs: [\"cdkDropList\"], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkDropList, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkDropList], cdk-drop-list',\n                    exportAs: 'cdkDropList',\n                    standalone: true,\n                    providers: [\n                        // Prevent child drop lists from picking up the same group as their parent.\n                        { provide: CDK_DROP_LIST_GROUP, useValue: undefined },\n                        { provide: CDK_DROP_LIST, useExisting: CdkDropList },\n                    ],\n                    host: {\n                        'class': 'cdk-drop-list',\n                        '[attr.id]': 'id',\n                        '[class.cdk-drop-list-disabled]': 'disabled',\n                        '[class.cdk-drop-list-dragging]': '_dropListRef.isDragging()',\n                        '[class.cdk-drop-list-receiving]': '_dropListRef.isReceiving()',\n                    },\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: DragDrop }, { type: i0.ChangeDetectorRef }, { type: i1.ScrollDispatcher }, { type: i1$1.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: CdkDropListGroup, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [CDK_DROP_LIST_GROUP]\n                }, {\n                    type: SkipSelf\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [CDK_DRAG_CONFIG]\n                }] }], propDecorators: { connectedTo: [{\n                type: Input,\n                args: ['cdkDropListConnectedTo']\n            }], data: [{\n                type: Input,\n                args: ['cdkDropListData']\n            }], orientation: [{\n                type: Input,\n                args: ['cdkDropListOrientation']\n            }], id: [{\n                type: Input\n            }], lockAxis: [{\n                type: Input,\n                args: ['cdkDropListLockAxis']\n            }], disabled: [{\n                type: Input,\n                args: [{ alias: 'cdkDropListDisabled', transform: booleanAttribute }]\n            }], sortingDisabled: [{\n                type: Input,\n                args: [{ alias: 'cdkDropListSortingDisabled', transform: booleanAttribute }]\n            }], enterPredicate: [{\n                type: Input,\n                args: ['cdkDropListEnterPredicate']\n            }], sortPredicate: [{\n                type: Input,\n                args: ['cdkDropListSortPredicate']\n            }], autoScrollDisabled: [{\n                type: Input,\n                args: [{ alias: 'cdkDropListAutoScrollDisabled', transform: booleanAttribute }]\n            }], autoScrollStep: [{\n                type: Input,\n                args: ['cdkDropListAutoScrollStep']\n            }], dropped: [{\n                type: Output,\n                args: ['cdkDropListDropped']\n            }], entered: [{\n                type: Output,\n                args: ['cdkDropListEntered']\n            }], exited: [{\n                type: Output,\n                args: ['cdkDropListExited']\n            }], sorted: [{\n                type: Output,\n                args: ['cdkDropListSorted']\n            }] } });\n\n/**\n * Injection token that can be used to reference instances of `CdkDragPreview`. It serves as\n * alternative token to the actual `CdkDragPreview` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DRAG_PREVIEW = new InjectionToken('CdkDragPreview');\n/**\n * Element that will be used as a template for the preview\n * of a CdkDrag when it is being dragged.\n */\nclass CdkDragPreview {\n    constructor(templateRef) {\n        this.templateRef = templateRef;\n        this._drag = inject(CDK_DRAG_PARENT, { optional: true });\n        /** Whether the preview should preserve the same size as the item that is being dragged. */\n        this.matchSize = false;\n        this._drag?._setPreviewTemplate(this);\n    }\n    ngOnDestroy() {\n        this._drag?._resetPreviewTemplate(this);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkDragPreview, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"17.2.0\", type: CdkDragPreview, isStandalone: true, selector: \"ng-template[cdkDragPreview]\", inputs: { data: \"data\", matchSize: [\"matchSize\", \"matchSize\", booleanAttribute] }, providers: [{ provide: CDK_DRAG_PREVIEW, useExisting: CdkDragPreview }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkDragPreview, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ng-template[cdkDragPreview]',\n                    standalone: true,\n                    providers: [{ provide: CDK_DRAG_PREVIEW, useExisting: CdkDragPreview }],\n                }]\n        }], ctorParameters: () => [{ type: i0.TemplateRef }], propDecorators: { data: [{\n                type: Input\n            }], matchSize: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }] } });\n\n/**\n * Injection token that can be used to reference instances of `CdkDragPlaceholder`. It serves as\n * alternative token to the actual `CdkDragPlaceholder` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DRAG_PLACEHOLDER = new InjectionToken('CdkDragPlaceholder');\n/**\n * Element that will be used as a template for the placeholder of a CdkDrag when\n * it is being dragged. The placeholder is displayed in place of the element being dragged.\n */\nclass CdkDragPlaceholder {\n    constructor(templateRef) {\n        this.templateRef = templateRef;\n        this._drag = inject(CDK_DRAG_PARENT, { optional: true });\n        this._drag?._setPlaceholderTemplate(this);\n    }\n    ngOnDestroy() {\n        this._drag?._resetPlaceholderTemplate(this);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkDragPlaceholder, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: CdkDragPlaceholder, isStandalone: true, selector: \"ng-template[cdkDragPlaceholder]\", inputs: { data: \"data\" }, providers: [{ provide: CDK_DRAG_PLACEHOLDER, useExisting: CdkDragPlaceholder }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkDragPlaceholder, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ng-template[cdkDragPlaceholder]',\n                    standalone: true,\n                    providers: [{ provide: CDK_DRAG_PLACEHOLDER, useExisting: CdkDragPlaceholder }],\n                }]\n        }], ctorParameters: () => [{ type: i0.TemplateRef }], propDecorators: { data: [{\n                type: Input\n            }] } });\n\nconst DRAG_DROP_DIRECTIVES = [\n    CdkDropList,\n    CdkDropListGroup,\n    CdkDrag,\n    CdkDragHandle,\n    CdkDragPreview,\n    CdkDragPlaceholder,\n];\nclass DragDropModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: DragDropModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.2.0\", ngImport: i0, type: DragDropModule, imports: [CdkDropList,\n            CdkDropListGroup,\n            CdkDrag,\n            CdkDragHandle,\n            CdkDragPreview,\n            CdkDragPlaceholder], exports: [CdkScrollableModule, CdkDropList,\n            CdkDropListGroup,\n            CdkDrag,\n            CdkDragHandle,\n            CdkDragPreview,\n            CdkDragPlaceholder] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: DragDropModule, providers: [DragDrop], imports: [CdkScrollableModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: DragDropModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: DRAG_DROP_DIRECTIVES,\n                    exports: [CdkScrollableModule, ...DRAG_DROP_DIRECTIVES],\n                    providers: [DragDrop],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CDK_DRAG_CONFIG, CDK_DRAG_HANDLE, CDK_DRAG_PARENT, CDK_DRAG_PLACEHOLDER, CDK_DRAG_PREVIEW, CDK_DROP_LIST, CDK_DROP_LIST_GROUP, CdkDrag, CdkDragHandle, CdkDragPlaceholder, CdkDragPreview, CdkDropList, CdkDropListGroup, DragDrop, DragDropModule, DragDropRegistry, DragRef, DropListRef, copyArrayItem, moveItemInArray, transferArrayItem };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,MAAM,EAAEC,cAAc,EAAEC,mBAAmB,EAAEC,eAAe,EAAEC,UAAU,EAAEC,MAAM,EAAEC,cAAc,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,YAAY,EAAEC,IAAI,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AACrR,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,KAAKC,EAAE,MAAM,wBAAwB;AAC5C,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,eAAe,EAAEC,+BAA+B,EAAEC,cAAc,QAAQ,uBAAuB;AACxG,SAASC,aAAa,EAAEC,oBAAoB,EAAEC,WAAW,QAAQ,uBAAuB;AACxF,SAASC,gCAAgC,EAAEC,+BAA+B,QAAQ,mBAAmB;AACrG,SAASC,OAAO,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,uBAAuB,EAAEC,UAAU,EAAEC,KAAK,EAAEC,eAAe,QAAQ,MAAM;AACnH,SAASC,SAAS,EAAEC,GAAG,EAAEC,IAAI,EAAEC,GAAG,EAAEC,SAAS,EAAEC,SAAS,QAAQ,gBAAgB;AAChF,OAAO,KAAKC,IAAI,MAAM,mBAAmB;;AAEzC;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,IAAI,EAAEC,MAAM,EAAEC,mBAAmB,EAAE;EACrD,KAAK,IAAIC,GAAG,IAAIF,MAAM,EAAE;IACpB,IAAIA,MAAM,CAACG,cAAc,CAACD,GAAG,CAAC,EAAE;MAC5B,MAAME,KAAK,GAAGJ,MAAM,CAACE,GAAG,CAAC;MACzB,IAAIE,KAAK,EAAE;QACPL,IAAI,CAACM,WAAW,CAACH,GAAG,EAAEE,KAAK,EAAEH,mBAAmB,EAAEK,GAAG,CAACJ,GAAG,CAAC,GAAG,WAAW,GAAG,EAAE,CAAC;MAClF,CAAC,MACI;QACDH,IAAI,CAACQ,cAAc,CAACL,GAAG,CAAC;MAC5B;IACJ;EACJ;EACA,OAAOH,IAAI;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASS,4BAA4BA,CAACC,OAAO,EAAEC,MAAM,EAAE;EACnD,MAAMC,UAAU,GAAGD,MAAM,GAAG,EAAE,GAAG,MAAM;EACvCZ,YAAY,CAACW,OAAO,CAACG,KAAK,EAAE;IACxB,cAAc,EAAEF,MAAM,GAAG,EAAE,GAAG,MAAM;IACpC,mBAAmB,EAAEA,MAAM,GAAG,EAAE,GAAG,MAAM;IACzC,6BAA6B,EAAEA,MAAM,GAAG,EAAE,GAAG,aAAa;IAC1D,aAAa,EAAEC,UAAU;IACzB,iBAAiB,EAAEA,UAAU;IAC7B,qBAAqB,EAAEA,UAAU;IACjC,kBAAkB,EAAEA;EACxB,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,gBAAgBA,CAACJ,OAAO,EAAEC,MAAM,EAAET,mBAAmB,EAAE;EAC5DH,YAAY,CAACW,OAAO,CAACG,KAAK,EAAE;IACxBE,QAAQ,EAAEJ,MAAM,GAAG,EAAE,GAAG,OAAO;IAC/BK,GAAG,EAAEL,MAAM,GAAG,EAAE,GAAG,GAAG;IACtBM,OAAO,EAAEN,MAAM,GAAG,EAAE,GAAG,GAAG;IAC1BO,IAAI,EAAEP,MAAM,GAAG,EAAE,GAAG;EACxB,CAAC,EAAET,mBAAmB,CAAC;AAC3B;AACA;AACA;AACA;AACA;AACA,SAASiB,iBAAiBA,CAACC,SAAS,EAAEC,gBAAgB,EAAE;EACpD,OAAOA,gBAAgB,IAAIA,gBAAgB,IAAI,MAAM,GAC/CD,SAAS,GAAG,GAAG,GAAGC,gBAAgB,GAClCD,SAAS;AACnB;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,gBAAgBA,CAACC,MAAM,EAAEC,UAAU,EAAE;EAC1CD,MAAM,CAACV,KAAK,CAACY,KAAK,GAAI,GAAED,UAAU,CAACC,KAAM,IAAG;EAC5CF,MAAM,CAACV,KAAK,CAACa,MAAM,GAAI,GAAEF,UAAU,CAACE,MAAO,IAAG;EAC9CH,MAAM,CAACV,KAAK,CAACO,SAAS,GAAGO,YAAY,CAACH,UAAU,CAACN,IAAI,EAAEM,UAAU,CAACR,GAAG,CAAC;AAC1E;AACA;AACA;AACA;AACA;AACA;AACA,SAASW,YAAYA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACxB;EACA;EACA,OAAQ,eAAcC,IAAI,CAACC,KAAK,CAACH,CAAC,CAAE,OAAME,IAAI,CAACC,KAAK,CAACF,CAAC,CAAE,QAAO;AACnE;;AAEA;AACA,SAASG,oBAAoBA,CAACtB,OAAO,EAAE;EACnC,MAAMuB,IAAI,GAAGvB,OAAO,CAACwB,qBAAqB,CAAC,CAAC;EAC5C;EACA;EACA;EACA;EACA,OAAO;IACHlB,GAAG,EAAEiB,IAAI,CAACjB,GAAG;IACbmB,KAAK,EAAEF,IAAI,CAACE,KAAK;IACjBC,MAAM,EAAEH,IAAI,CAACG,MAAM;IACnBlB,IAAI,EAAEe,IAAI,CAACf,IAAI;IACfO,KAAK,EAAEQ,IAAI,CAACR,KAAK;IACjBC,MAAM,EAAEO,IAAI,CAACP,MAAM;IACnBE,CAAC,EAAEK,IAAI,CAACL,CAAC;IACTC,CAAC,EAAEI,IAAI,CAACJ;EACZ,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASQ,kBAAkBA,CAACC,UAAU,EAAEV,CAAC,EAAEC,CAAC,EAAE;EAC1C,MAAM;IAAEb,GAAG;IAAEoB,MAAM;IAAElB,IAAI;IAAEiB;EAAM,CAAC,GAAGG,UAAU;EAC/C,OAAOT,CAAC,IAAIb,GAAG,IAAIa,CAAC,IAAIO,MAAM,IAAIR,CAAC,IAAIV,IAAI,IAAIU,CAAC,IAAIO,KAAK;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,aAAaA,CAACC,OAAO,EAAExB,GAAG,EAAEE,IAAI,EAAE;EACvCsB,OAAO,CAACxB,GAAG,IAAIA,GAAG;EAClBwB,OAAO,CAACJ,MAAM,GAAGI,OAAO,CAACxB,GAAG,GAAGwB,OAAO,CAACd,MAAM;EAC7Cc,OAAO,CAACtB,IAAI,IAAIA,IAAI;EACpBsB,OAAO,CAACL,KAAK,GAAGK,OAAO,CAACtB,IAAI,GAAGsB,OAAO,CAACf,KAAK;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASgB,oBAAoBA,CAACR,IAAI,EAAES,SAAS,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;EAC/D,MAAM;IAAE5B,GAAG;IAAEmB,KAAK;IAAEC,MAAM;IAAElB,IAAI;IAAEO,KAAK;IAAEC;EAAO,CAAC,GAAGO,IAAI;EACxD,MAAMY,UAAU,GAAGpB,KAAK,GAAGiB,SAAS;EACpC,MAAMI,UAAU,GAAGpB,MAAM,GAAGgB,SAAS;EACrC,OAAQE,QAAQ,GAAG5B,GAAG,GAAG8B,UAAU,IAC/BF,QAAQ,GAAGR,MAAM,GAAGU,UAAU,IAC9BH,QAAQ,GAAGzB,IAAI,GAAG2B,UAAU,IAC5BF,QAAQ,GAAGR,KAAK,GAAGU,UAAU;AACrC;;AAEA;AACA,MAAME,qBAAqB,CAAC;EACxBC,WAAWA,CAACC,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B;IACA,IAAI,CAACC,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC9B;EACA;EACAC,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACF,SAAS,CAACE,KAAK,CAAC,CAAC;EAC1B;EACA;EACAC,KAAKA,CAACC,QAAQ,EAAE;IACZ,IAAI,CAACF,KAAK,CAAC,CAAC;IACZ,IAAI,CAACF,SAAS,CAACK,GAAG,CAAC,IAAI,CAACN,SAAS,EAAE;MAC/BO,cAAc,EAAE,IAAI,CAACC,yBAAyB,CAAC;IACnD,CAAC,CAAC;IACFH,QAAQ,CAACI,OAAO,CAAChD,OAAO,IAAI;MACxB,IAAI,CAACwC,SAAS,CAACK,GAAG,CAAC7C,OAAO,EAAE;QACxB8C,cAAc,EAAE;UAAExC,GAAG,EAAEN,OAAO,CAACiD,SAAS;UAAEzC,IAAI,EAAER,OAAO,CAACkD;QAAW,CAAC;QACpEtB,UAAU,EAAEN,oBAAoB,CAACtB,OAAO;MAC5C,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA;EACAmD,YAAYA,CAACC,KAAK,EAAE;IAChB,MAAMvC,MAAM,GAAG9C,eAAe,CAACqF,KAAK,CAAC;IACrC,MAAMC,cAAc,GAAG,IAAI,CAACb,SAAS,CAACc,GAAG,CAACzC,MAAM,CAAC;IACjD,IAAI,CAACwC,cAAc,EAAE;MACjB,OAAO,IAAI;IACf;IACA,MAAMP,cAAc,GAAGO,cAAc,CAACP,cAAc;IACpD,IAAIS,MAAM;IACV,IAAIC,OAAO;IACX,IAAI3C,MAAM,KAAK,IAAI,CAAC0B,SAAS,EAAE;MAC3B,MAAMkB,sBAAsB,GAAG,IAAI,CAACV,yBAAyB,CAAC,CAAC;MAC/DQ,MAAM,GAAGE,sBAAsB,CAACnD,GAAG;MACnCkD,OAAO,GAAGC,sBAAsB,CAACjD,IAAI;IACzC,CAAC,MACI;MACD+C,MAAM,GAAG1C,MAAM,CAACoC,SAAS;MACzBO,OAAO,GAAG3C,MAAM,CAACqC,UAAU;IAC/B;IACA,MAAMQ,aAAa,GAAGZ,cAAc,CAACxC,GAAG,GAAGiD,MAAM;IACjD,MAAMI,cAAc,GAAGb,cAAc,CAACtC,IAAI,GAAGgD,OAAO;IACpD;IACA;IACA,IAAI,CAAChB,SAAS,CAACQ,OAAO,CAAC,CAAC3C,QAAQ,EAAEuD,IAAI,KAAK;MACvC,IAAIvD,QAAQ,CAACuB,UAAU,IAAIf,MAAM,KAAK+C,IAAI,IAAI/C,MAAM,CAACgD,QAAQ,CAACD,IAAI,CAAC,EAAE;QACjE/B,aAAa,CAACxB,QAAQ,CAACuB,UAAU,EAAE8B,aAAa,EAAEC,cAAc,CAAC;MACrE;IACJ,CAAC,CAAC;IACFb,cAAc,CAACxC,GAAG,GAAGiD,MAAM;IAC3BT,cAAc,CAACtC,IAAI,GAAGgD,OAAO;IAC7B,OAAO;MAAElD,GAAG,EAAEoD,aAAa;MAAElD,IAAI,EAAEmD;IAAe,CAAC;EACvD;EACA;AACJ;AACA;AACA;AACA;AACA;EACIZ,yBAAyBA,CAAA,EAAG;IACxB,OAAO;MAAEzC,GAAG,EAAEwD,MAAM,CAACC,OAAO;MAAEvD,IAAI,EAAEsD,MAAM,CAACE;IAAQ,CAAC;EACxD;AACJ;;AAEA;AACA,SAASC,aAAaA,CAACL,IAAI,EAAE;EACzB,MAAMM,KAAK,GAAGN,IAAI,CAACO,SAAS,CAAC,IAAI,CAAC;EAClC,MAAMC,iBAAiB,GAAGF,KAAK,CAACG,gBAAgB,CAAC,MAAM,CAAC;EACxD,MAAMC,QAAQ,GAAGV,IAAI,CAACU,QAAQ,CAACC,WAAW,CAAC,CAAC;EAC5C;EACAL,KAAK,CAACM,eAAe,CAAC,IAAI,CAAC;EAC3B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,iBAAiB,CAACM,MAAM,EAAED,CAAC,EAAE,EAAE;IAC/CL,iBAAiB,CAACK,CAAC,CAAC,CAACD,eAAe,CAAC,IAAI,CAAC;EAC9C;EACA,IAAIF,QAAQ,KAAK,QAAQ,EAAE;IACvBK,kBAAkB,CAACf,IAAI,EAAEM,KAAK,CAAC;EACnC,CAAC,MACI,IAAII,QAAQ,KAAK,OAAO,IAAIA,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,KAAK,UAAU,EAAE;IAC/EM,iBAAiB,CAAChB,IAAI,EAAEM,KAAK,CAAC;EAClC;EACAW,YAAY,CAAC,QAAQ,EAAEjB,IAAI,EAAEM,KAAK,EAAES,kBAAkB,CAAC;EACvDE,YAAY,CAAC,yBAAyB,EAAEjB,IAAI,EAAEM,KAAK,EAAEU,iBAAiB,CAAC;EACvE,OAAOV,KAAK;AAChB;AACA;AACA,SAASW,YAAYA,CAACC,QAAQ,EAAElB,IAAI,EAAEM,KAAK,EAAEa,QAAQ,EAAE;EACnD,MAAMC,kBAAkB,GAAGpB,IAAI,CAACS,gBAAgB,CAACS,QAAQ,CAAC;EAC1D,IAAIE,kBAAkB,CAACN,MAAM,EAAE;IAC3B,MAAMO,aAAa,GAAGf,KAAK,CAACG,gBAAgB,CAACS,QAAQ,CAAC;IACtD,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGO,kBAAkB,CAACN,MAAM,EAAED,CAAC,EAAE,EAAE;MAChDM,QAAQ,CAACC,kBAAkB,CAACP,CAAC,CAAC,EAAEQ,aAAa,CAACR,CAAC,CAAC,CAAC;IACrD;EACJ;AACJ;AACA;AACA,IAAIS,aAAa,GAAG,CAAC;AACrB;AACA,SAASN,iBAAiBA,CAACrF,MAAM,EAAE2E,KAAK,EAAE;EACtC;EACA,IAAIA,KAAK,CAACiB,IAAI,KAAK,MAAM,EAAE;IACvBjB,KAAK,CAACvE,KAAK,GAAGJ,MAAM,CAACI,KAAK;EAC9B;EACA;EACA;EACA;EACA,IAAIuE,KAAK,CAACiB,IAAI,KAAK,OAAO,IAAIjB,KAAK,CAACkB,IAAI,EAAE;IACtClB,KAAK,CAACkB,IAAI,GAAI,aAAYlB,KAAK,CAACkB,IAAK,IAAGF,aAAa,EAAG,EAAC;EAC7D;AACJ;AACA;AACA,SAASP,kBAAkBA,CAACpF,MAAM,EAAE2E,KAAK,EAAE;EACvC,MAAMmB,OAAO,GAAGnB,KAAK,CAACoB,UAAU,CAAC,IAAI,CAAC;EACtC,IAAID,OAAO,EAAE;IACT;IACA;IACA,IAAI;MACAA,OAAO,CAACE,SAAS,CAAChG,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;IACnC,CAAC,CACD,MAAM,CAAE;EACZ;AACJ;;AAEA;AACA;AACA;AACA;AACA,SAASiG,WAAWA,CAACC,OAAO,EAAElD,SAAS,EAAE;EACrC,MAAMmD,SAAS,GAAGD,OAAO,CAACC,SAAS;EACnC,IAAIA,SAAS,CAAChB,MAAM,KAAK,CAAC,IAAIgB,SAAS,CAAC,CAAC,CAAC,CAACC,QAAQ,KAAKpD,SAAS,CAACqD,YAAY,EAAE;IAC5E,OAAOF,SAAS,CAAC,CAAC,CAAC;EACvB;EACA,MAAMG,OAAO,GAAGtD,SAAS,CAACuD,aAAa,CAAC,KAAK,CAAC;EAC9CJ,SAAS,CAAC1C,OAAO,CAACY,IAAI,IAAIiC,OAAO,CAACE,WAAW,CAACnC,IAAI,CAAC,CAAC;EACpD,OAAOiC,OAAO;AAClB;;AAEA;AACA,SAASG,qBAAqBA,CAACrG,KAAK,EAAE;EAClC;EACA,MAAMsG,UAAU,GAAGtG,KAAK,CAAC4E,WAAW,CAAC,CAAC,CAAC2B,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI;EACpE,OAAOC,UAAU,CAACxG,KAAK,CAAC,GAAGsG,UAAU;AACzC;AACA;AACA,SAASG,kCAAkCA,CAACpG,OAAO,EAAE;EACjD,MAAMqG,aAAa,GAAGC,gBAAgB,CAACtG,OAAO,CAAC;EAC/C,MAAMuG,sBAAsB,GAAGC,qBAAqB,CAACH,aAAa,EAAE,qBAAqB,CAAC;EAC1F,MAAMI,QAAQ,GAAGF,sBAAsB,CAACG,IAAI,CAACC,IAAI,IAAIA,IAAI,KAAK,WAAW,IAAIA,IAAI,KAAK,KAAK,CAAC;EAC5F;EACA,IAAI,CAACF,QAAQ,EAAE;IACX,OAAO,CAAC;EACZ;EACA;EACA;EACA,MAAMG,aAAa,GAAGL,sBAAsB,CAACL,OAAO,CAACO,QAAQ,CAAC;EAC9D,MAAMI,YAAY,GAAGL,qBAAqB,CAACH,aAAa,EAAE,qBAAqB,CAAC;EAChF,MAAMS,SAAS,GAAGN,qBAAqB,CAACH,aAAa,EAAE,kBAAkB,CAAC;EAC1E,OAAQL,qBAAqB,CAACa,YAAY,CAACD,aAAa,CAAC,CAAC,GACtDZ,qBAAqB,CAACc,SAAS,CAACF,aAAa,CAAC,CAAC;AACvD;AACA;AACA,SAASJ,qBAAqBA,CAACH,aAAa,EAAEjB,IAAI,EAAE;EAChD,MAAMzF,KAAK,GAAG0G,aAAa,CAACU,gBAAgB,CAAC3B,IAAI,CAAC;EAClD,OAAOzF,KAAK,CAACqH,KAAK,CAAC,GAAG,CAAC,CAACjI,GAAG,CAACkI,IAAI,IAAIA,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC;AACpD;;AAEA;AACA,MAAM1H,mBAAmB,GAAG,IAAI2H,GAAG,CAAC;AAChC;AACA,UAAU,CACb,CAAC;AACF,MAAMC,UAAU,CAAC;EACb9E,WAAWA,CAACC,SAAS,EAAE8E,YAAY,EAAEC,UAAU,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,aAAa,EAAEC,qBAAqB,EAAEC,iBAAiB,EAAEC,OAAO,EAAE;IAClJ,IAAI,CAACrF,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAC8E,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,OAAO,GAAGA,OAAO;EAC1B;EACAC,MAAMA,CAACC,MAAM,EAAE;IACX,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACrCF,MAAM,CAAC/B,WAAW,CAAC,IAAI,CAACgC,QAAQ,CAAC;IACjC;IACA;IACA,IAAI,aAAa,IAAI,IAAI,CAACA,QAAQ,EAAE;MAChC,IAAI,CAACA,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC;IAClC;EACJ;EACAE,OAAOA,CAAA,EAAG;IACN,IAAI,CAACF,QAAQ,CAACG,MAAM,CAAC,CAAC;IACtB,IAAI,CAACC,oBAAoB,EAAEF,OAAO,CAAC,CAAC;IACpC,IAAI,CAACF,QAAQ,GAAG,IAAI,CAACI,oBAAoB,GAAG,IAAI;EACpD;EACAC,YAAYA,CAACzI,KAAK,EAAE;IAChB,IAAI,CAACoI,QAAQ,CAAC5H,KAAK,CAACO,SAAS,GAAGf,KAAK;EACzC;EACA6B,qBAAqBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACuG,QAAQ,CAACvG,qBAAqB,CAAC,CAAC;EAChD;EACA6G,QAAQA,CAACC,SAAS,EAAE;IAChB,IAAI,CAACP,QAAQ,CAACQ,SAAS,CAACC,GAAG,CAACF,SAAS,CAAC;EAC1C;EACAG,qBAAqBA,CAAA,EAAG;IACpB,OAAOrC,kCAAkC,CAAC,IAAI,CAAC2B,QAAQ,CAAC;EAC5D;EACAW,gBAAgBA,CAACtD,IAAI,EAAEuD,OAAO,EAAE;IAC5B,IAAI,CAACZ,QAAQ,CAACW,gBAAgB,CAACtD,IAAI,EAAEuD,OAAO,CAAC;EACjD;EACAC,mBAAmBA,CAACxD,IAAI,EAAEuD,OAAO,EAAE;IAC/B,IAAI,CAACZ,QAAQ,CAACa,mBAAmB,CAACxD,IAAI,EAAEuD,OAAO,CAAC;EACpD;EACAX,cAAcA,CAAA,EAAG;IACb,MAAMa,aAAa,GAAG,IAAI,CAACrB,gBAAgB;IAC3C,MAAMsB,YAAY,GAAG,IAAI,CAACrB,aAAa;IACvC,MAAMsB,eAAe,GAAGF,aAAa,GAAGA,aAAa,CAACG,QAAQ,GAAG,IAAI;IACrE,IAAIC,OAAO;IACX,IAAIF,eAAe,IAAIF,aAAa,EAAE;MAClC;MACA;MACA,MAAMK,QAAQ,GAAGL,aAAa,CAACM,SAAS,GAAG,IAAI,CAAC5B,eAAe,GAAG,IAAI;MACtE,MAAM9B,OAAO,GAAGoD,aAAa,CAACO,aAAa,CAACC,kBAAkB,CAACN,eAAe,EAAEF,aAAa,CAACxD,OAAO,CAAC;MACtGI,OAAO,CAAC6D,aAAa,CAAC,CAAC;MACvBL,OAAO,GAAGzD,WAAW,CAACC,OAAO,EAAE,IAAI,CAAClD,SAAS,CAAC;MAC9C,IAAI,CAAC4F,oBAAoB,GAAG1C,OAAO;MACnC,IAAIoD,aAAa,CAACM,SAAS,EAAE;QACzBvI,gBAAgB,CAACqI,OAAO,EAAEC,QAAQ,CAAC;MACvC,CAAC,MACI;QACDD,OAAO,CAAC9I,KAAK,CAACO,SAAS,GAAGO,YAAY,CAAC,IAAI,CAACyG,qBAAqB,CAACxG,CAAC,EAAE,IAAI,CAACwG,qBAAqB,CAACvG,CAAC,CAAC;MACtG;IACJ,CAAC,MACI;MACD8H,OAAO,GAAGhF,aAAa,CAAC,IAAI,CAACoD,YAAY,CAAC;MAC1CzG,gBAAgB,CAACqI,OAAO,EAAE,IAAI,CAAC1B,eAAe,CAAC;MAC/C,IAAI,IAAI,CAACI,iBAAiB,EAAE;QACxBsB,OAAO,CAAC9I,KAAK,CAACO,SAAS,GAAG,IAAI,CAACiH,iBAAiB;MACpD;IACJ;IACAtI,YAAY,CAAC4J,OAAO,CAAC9I,KAAK,EAAE;MACxB;MACA;MACA,gBAAgB,EAAE,MAAM;MACxB;MACA,QAAQ,EAAE,GAAG;MACb,UAAU,EAAE,OAAO;MACnB,KAAK,EAAE,GAAG;MACV,MAAM,EAAE,GAAG;MACX,SAAS,EAAE,IAAI,CAACyH,OAAO,GAAG;IAC9B,CAAC,EAAEpI,mBAAmB,CAAC;IACvBO,4BAA4B,CAACkJ,OAAO,EAAE,KAAK,CAAC;IAC5CA,OAAO,CAACV,SAAS,CAACC,GAAG,CAAC,kBAAkB,CAAC;IACzCS,OAAO,CAACM,YAAY,CAAC,SAAS,EAAE,QAAQ,CAAC;IACzCN,OAAO,CAACM,YAAY,CAAC,KAAK,EAAE,IAAI,CAACjC,UAAU,CAAC;IAC5C,IAAIwB,YAAY,EAAE;MACd,IAAIU,KAAK,CAACC,OAAO,CAACX,YAAY,CAAC,EAAE;QAC7BA,YAAY,CAAC9F,OAAO,CAACsF,SAAS,IAAIW,OAAO,CAACV,SAAS,CAACC,GAAG,CAACF,SAAS,CAAC,CAAC;MACvE,CAAC,MACI;QACDW,OAAO,CAACV,SAAS,CAACC,GAAG,CAACM,YAAY,CAAC;MACvC;IACJ;IACA,OAAOG,OAAO;EAClB;AACJ;;AAEA;AACA,MAAMS,2BAA2B,GAAG1L,+BAA+B,CAAC;EAAE2L,OAAO,EAAE;AAAK,CAAC,CAAC;AACtF;AACA,MAAMC,0BAA0B,GAAG5L,+BAA+B,CAAC;EAAE2L,OAAO,EAAE;AAAM,CAAC,CAAC;AACtF;AACA,MAAME,6BAA6B,GAAG7L,+BAA+B,CAAC;EAClE2L,OAAO,EAAE,KAAK;EACdG,OAAO,EAAE;AACb,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,uBAAuB,GAAG,GAAG;AACnC;AACA,MAAMC,uBAAuB,GAAG,IAAI7C,GAAG,CAAC;AACpC;AACA,UAAU,CACb,CAAC;AACF;AACA;AACA;AACA,MAAM8C,OAAO,CAAC;EACV;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS,IAAI,CAAC,EAAE,IAAI,CAACC,cAAc,IAAI,IAAI,CAACA,cAAc,CAACF,QAAQ,CAAC;EACpF;EACA,IAAIA,QAAQA,CAACvK,KAAK,EAAE;IAChB,IAAIA,KAAK,KAAK,IAAI,CAACwK,SAAS,EAAE;MAC1B,IAAI,CAACA,SAAS,GAAGxK,KAAK;MACtB,IAAI,CAAC0K,6BAA6B,CAAC,CAAC;MACpC,IAAI,CAACC,QAAQ,CAACtH,OAAO,CAACuH,MAAM,IAAIxK,4BAA4B,CAACwK,MAAM,EAAE5K,KAAK,CAAC,CAAC;IAChF;EACJ;EACA2C,WAAWA,CAACtC,OAAO,EAAEwK,OAAO,EAAEjI,SAAS,EAAEkI,OAAO,EAAEC,cAAc,EAAEC,iBAAiB,EAAE;IACjF,IAAI,CAACH,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACjI,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACkI,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC1C;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,iBAAiB,GAAG;MAAE1J,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IACvC;IACA,IAAI,CAAC0J,gBAAgB,GAAG;MAAE3J,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IACtC;AACR;AACA;AACA;IACQ,IAAI,CAAC2J,mBAAmB,GAAG,KAAK;IAChC;IACA,IAAI,CAACC,WAAW,GAAG,IAAIxM,OAAO,CAAC,CAAC;IAChC;IACA,IAAI,CAACyM,wBAAwB,GAAGxM,YAAY,CAACyM,KAAK;IAClD;IACA,IAAI,CAACC,sBAAsB,GAAG1M,YAAY,CAACyM,KAAK;IAChD;IACA,IAAI,CAACE,mBAAmB,GAAG3M,YAAY,CAACyM,KAAK;IAC7C;IACA,IAAI,CAACG,mBAAmB,GAAG5M,YAAY,CAACyM,KAAK;IAC7C;IACA,IAAI,CAACI,gBAAgB,GAAG,IAAI;IAC5B;IACA,IAAI,CAACC,0BAA0B,GAAG,IAAI;IACtC;IACA,IAAI,CAAChB,QAAQ,GAAG,EAAE;IAClB;IACA,IAAI,CAACiB,gBAAgB,GAAG,IAAIpE,GAAG,CAAC,CAAC;IACjC;IACA,IAAI,CAACG,UAAU,GAAG,KAAK;IACvB;AACR;AACA;AACA;IACQ,IAAI,CAACkE,cAAc,GAAG,CAAC;IACvB,IAAI,CAACrB,SAAS,GAAG,KAAK;IACtB;IACA,IAAI,CAACsB,aAAa,GAAG,IAAIlN,OAAO,CAAC,CAAC;IAClC;IACA,IAAI,CAACmN,OAAO,GAAG,IAAInN,OAAO,CAAC,CAAC;IAC5B;IACA,IAAI,CAACoN,QAAQ,GAAG,IAAIpN,OAAO,CAAC,CAAC;IAC7B;IACA,IAAI,CAACqN,KAAK,GAAG,IAAIrN,OAAO,CAAC,CAAC;IAC1B;IACA,IAAI,CAACsN,OAAO,GAAG,IAAItN,OAAO,CAAC,CAAC;IAC5B;IACA,IAAI,CAACuN,MAAM,GAAG,IAAIvN,OAAO,CAAC,CAAC;IAC3B;IACA,IAAI,CAACwN,OAAO,GAAG,IAAIxN,OAAO,CAAC,CAAC;IAC5B;AACR;AACA;AACA;IACQ,IAAI,CAACyN,KAAK,GAAG,IAAI,CAACjB,WAAW;IAC7B;IACA,IAAI,CAACkB,YAAY,GAAI7I,KAAK,IAAK;MAC3B,IAAI,CAACqI,aAAa,CAACS,IAAI,CAAC,CAAC;MACzB;MACA,IAAI,IAAI,CAAC5B,QAAQ,CAAC5F,MAAM,EAAE;QACtB,MAAMyH,YAAY,GAAG,IAAI,CAACC,gBAAgB,CAAChJ,KAAK,CAAC;QACjD,IAAI+I,YAAY,IAAI,CAAC,IAAI,CAACZ,gBAAgB,CAAC1L,GAAG,CAACsM,YAAY,CAAC,IAAI,CAAC,IAAI,CAACjC,QAAQ,EAAE;UAC5E,IAAI,CAACmC,uBAAuB,CAACF,YAAY,EAAE/I,KAAK,CAAC;QACrD;MACJ,CAAC,MACI,IAAI,CAAC,IAAI,CAAC8G,QAAQ,EAAE;QACrB,IAAI,CAACmC,uBAAuB,CAAC,IAAI,CAAChF,YAAY,EAAEjE,KAAK,CAAC;MAC1D;IACJ,CAAC;IACD;IACA,IAAI,CAACkJ,YAAY,GAAIlJ,KAAK,IAAK;MAC3B,MAAMmJ,eAAe,GAAG,IAAI,CAACC,yBAAyB,CAACpJ,KAAK,CAAC;MAC7D,IAAI,CAAC,IAAI,CAAC0H,mBAAmB,EAAE;QAC3B,MAAM2B,SAAS,GAAGrL,IAAI,CAACsL,GAAG,CAACH,eAAe,CAACrL,CAAC,GAAG,IAAI,CAACwG,qBAAqB,CAACxG,CAAC,CAAC;QAC5E,MAAMyL,SAAS,GAAGvL,IAAI,CAACsL,GAAG,CAACH,eAAe,CAACpL,CAAC,GAAG,IAAI,CAACuG,qBAAqB,CAACvG,CAAC,CAAC;QAC5E,MAAMyL,eAAe,GAAGH,SAAS,GAAGE,SAAS,IAAI,IAAI,CAACnC,OAAO,CAACqC,kBAAkB;QAChF;QACA;QACA;QACA;QACA,IAAID,eAAe,EAAE;UACjB,MAAME,cAAc,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,IAAI,IAAI,CAACC,cAAc,GAAG,IAAI,CAACC,kBAAkB,CAAC9J,KAAK,CAAC;UACzF,MAAM+J,SAAS,GAAG,IAAI,CAAC/C,cAAc;UACrC,IAAI,CAAC0C,cAAc,EAAE;YACjB,IAAI,CAACM,gBAAgB,CAAChK,KAAK,CAAC;YAC5B;UACJ;UACA;UACA;UACA;UACA,IAAI,CAAC+J,SAAS,IAAK,CAACA,SAAS,CAACE,UAAU,CAAC,CAAC,IAAI,CAACF,SAAS,CAACG,WAAW,CAAC,CAAE,EAAE;YACrE;YACA;YACA,IAAIlK,KAAK,CAACmK,UAAU,EAAE;cAClBnK,KAAK,CAACoK,cAAc,CAAC,CAAC;YAC1B;YACA,IAAI,CAAC1C,mBAAmB,GAAG,IAAI;YAC/B,IAAI,CAACL,OAAO,CAACgD,GAAG,CAAC,MAAM,IAAI,CAACC,kBAAkB,CAACtK,KAAK,CAAC,CAAC;UAC1D;QACJ;QACA;MACJ;MACA;MACA;MACA;MACA,IAAIA,KAAK,CAACmK,UAAU,EAAE;QAClBnK,KAAK,CAACoK,cAAc,CAAC,CAAC;MAC1B;MACA,MAAMG,0BAA0B,GAAG,IAAI,CAACC,8BAA8B,CAACrB,eAAe,CAAC;MACvF,IAAI,CAACsB,SAAS,GAAG,IAAI;MACrB,IAAI,CAACC,yBAAyB,GAAGvB,eAAe;MAChD,IAAI,CAACwB,4BAA4B,CAACJ,0BAA0B,CAAC;MAC7D,IAAI,IAAI,CAACvD,cAAc,EAAE;QACrB,IAAI,CAAC4D,0BAA0B,CAACL,0BAA0B,EAAEpB,eAAe,CAAC;MAChF,CAAC,MACI;QACD;QACA;QACA,MAAM0B,MAAM,GAAG,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAAC3G,eAAe,GAAG,IAAI,CAACG,qBAAqB;QACzF,MAAMyG,eAAe,GAAG,IAAI,CAACtD,gBAAgB;QAC7CsD,eAAe,CAACjN,CAAC,GAAGyM,0BAA0B,CAACzM,CAAC,GAAG+M,MAAM,CAAC/M,CAAC,GAAG,IAAI,CAAC0J,iBAAiB,CAAC1J,CAAC;QACtFiN,eAAe,CAAChN,CAAC,GAAGwM,0BAA0B,CAACxM,CAAC,GAAG8M,MAAM,CAAC9M,CAAC,GAAG,IAAI,CAACyJ,iBAAiB,CAACzJ,CAAC;QACtF,IAAI,CAACiN,0BAA0B,CAACD,eAAe,CAACjN,CAAC,EAAEiN,eAAe,CAAChN,CAAC,CAAC;MACzE;MACA;MACA;MACA;MACA,IAAI,IAAI,CAAC4J,WAAW,CAACsD,SAAS,CAAC3J,MAAM,EAAE;QACnC,IAAI,CAAC+F,OAAO,CAACgD,GAAG,CAAC,MAAM;UACnB,IAAI,CAAC1C,WAAW,CAACmB,IAAI,CAAC;YAClB3M,MAAM,EAAE,IAAI;YACZgN,eAAe,EAAEoB,0BAA0B;YAC3CvK,KAAK;YACLkL,QAAQ,EAAE,IAAI,CAACC,gBAAgB,CAACZ,0BAA0B,CAAC;YAC3Da,KAAK,EAAE,IAAI,CAACC;UAChB,CAAC,CAAC;QACN,CAAC,CAAC;MACN;IACJ,CAAC;IACD;IACA,IAAI,CAACC,UAAU,GAAItL,KAAK,IAAK;MACzB,IAAI,CAACgK,gBAAgB,CAAChK,KAAK,CAAC;IAChC,CAAC;IACD;IACA,IAAI,CAACuL,gBAAgB,GAAIvL,KAAK,IAAK;MAC/B,IAAI,IAAI,CAACkH,QAAQ,CAAC5F,MAAM,EAAE;QACtB,MAAMyH,YAAY,GAAG,IAAI,CAACC,gBAAgB,CAAChJ,KAAK,CAAC;QACjD,IAAI+I,YAAY,IAAI,CAAC,IAAI,CAACZ,gBAAgB,CAAC1L,GAAG,CAACsM,YAAY,CAAC,IAAI,CAAC,IAAI,CAACjC,QAAQ,EAAE;UAC5E9G,KAAK,CAACoK,cAAc,CAAC,CAAC;QAC1B;MACJ,CAAC,MACI,IAAI,CAAC,IAAI,CAACtD,QAAQ,EAAE;QACrB;QACA;QACA9G,KAAK,CAACoK,cAAc,CAAC,CAAC;MAC1B;IACJ,CAAC;IACD,IAAI,CAACoB,eAAe,CAAC5O,OAAO,CAAC,CAAC6O,UAAU,CAACrE,OAAO,CAACsE,aAAa,IAAI,IAAI,CAAC;IACvE,IAAI,CAACC,gBAAgB,GAAG,IAAI1M,qBAAqB,CAACE,SAAS,CAAC;IAC5DoI,iBAAiB,CAACqE,gBAAgB,CAAC,IAAI,CAAC;EAC5C;EACA;AACJ;AACA;AACA;EACIC,qBAAqBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACC,YAAY;EAC5B;EACA;EACAC,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC9H,YAAY;EAC5B;EACA;AACJ;AACA;AACA;EACI+H,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAC/B,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC4B,qBAAqB,CAAC,CAAC,GAAG,IAAI,CAACE,cAAc,CAAC,CAAC;EACnF;EACA;EACAE,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAI,CAAChF,QAAQ,GAAGgF,OAAO,CAACvQ,GAAG,CAACwL,MAAM,IAAIrM,aAAa,CAACqM,MAAM,CAAC,CAAC;IAC5D,IAAI,CAACD,QAAQ,CAACtH,OAAO,CAACuH,MAAM,IAAIxK,4BAA4B,CAACwK,MAAM,EAAE,IAAI,CAACL,QAAQ,CAAC,CAAC;IACpF,IAAI,CAACG,6BAA6B,CAAC,CAAC;IACpC;IACA;IACA;IACA;IACA,MAAMkF,eAAe,GAAG,IAAIpI,GAAG,CAAC,CAAC;IACjC,IAAI,CAACoE,gBAAgB,CAACvI,OAAO,CAACuH,MAAM,IAAI;MACpC,IAAI,IAAI,CAACD,QAAQ,CAACpE,OAAO,CAACqE,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE;QACpCgF,eAAe,CAAC/G,GAAG,CAAC+B,MAAM,CAAC;MAC/B;IACJ,CAAC,CAAC;IACF,IAAI,CAACgB,gBAAgB,GAAGgE,eAAe;IACvC,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIC,mBAAmBA,CAACxG,QAAQ,EAAE;IAC1B,IAAI,CAACxB,gBAAgB,GAAGwB,QAAQ;IAChC,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIyG,uBAAuBA,CAACzG,QAAQ,EAAE;IAC9B,IAAI,CAAC0G,oBAAoB,GAAG1G,QAAQ;IACpC,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACI4F,eAAeA,CAACe,WAAW,EAAE;IACzB,MAAM3P,OAAO,GAAG9B,aAAa,CAACyR,WAAW,CAAC;IAC1C,IAAI3P,OAAO,KAAK,IAAI,CAACqH,YAAY,EAAE;MAC/B,IAAI,IAAI,CAACA,YAAY,EAAE;QACnB,IAAI,CAACuI,2BAA2B,CAAC,IAAI,CAACvI,YAAY,CAAC;MACvD;MACA,IAAI,CAACoD,OAAO,CAACoF,iBAAiB,CAAC,MAAM;QACjC7P,OAAO,CAAC0I,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACuD,YAAY,EAAErC,0BAA0B,CAAC;QACpF5J,OAAO,CAAC0I,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAACuD,YAAY,EAAEvC,2BAA2B,CAAC;QACtF1J,OAAO,CAAC0I,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACiG,gBAAgB,EAAE/E,0BAA0B,CAAC;MAC5F,CAAC,CAAC;MACF,IAAI,CAACjC,iBAAiB,GAAGmI,SAAS;MAClC,IAAI,CAACzI,YAAY,GAAGrH,OAAO;IAC/B;IACA,IAAI,OAAO+P,UAAU,KAAK,WAAW,IAAI,IAAI,CAAC1I,YAAY,YAAY0I,UAAU,EAAE;MAC9E,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAAC3I,YAAY,CAAC4I,eAAe;IAC7D;IACA,OAAO,IAAI;EACf;EACA;AACJ;AACA;EACIC,mBAAmBA,CAACC,eAAe,EAAE;IACjC,IAAI,CAAC9E,gBAAgB,GAAG8E,eAAe,GAAGjS,aAAa,CAACiS,eAAe,CAAC,GAAG,IAAI;IAC/E,IAAI,CAAC/E,mBAAmB,CAACgF,WAAW,CAAC,CAAC;IACtC,IAAID,eAAe,EAAE;MACjB,IAAI,CAAC/E,mBAAmB,GAAG,IAAI,CAACV,cAAc,CACzC2F,MAAM,CAAC,EAAE,CAAC,CACVC,SAAS,CAAC,MAAM,IAAI,CAACC,8BAA8B,CAAC,CAAC,CAAC;IAC/D;IACA,OAAO,IAAI;EACf;EACA;EACA1B,UAAUA,CAAC/G,MAAM,EAAE;IACf,IAAI,CAAC0I,cAAc,GAAG1I,MAAM;IAC5B,OAAO,IAAI;EACf;EACA;EACA2I,OAAOA,CAAA,EAAG;IACN,IAAI,CAACb,2BAA2B,CAAC,IAAI,CAACvI,YAAY,CAAC;IACnD;IACA;IACA,IAAI,IAAI,CAACgG,UAAU,CAAC,CAAC,EAAE;MACnB;MACA;MACA,IAAI,CAAChG,YAAY,EAAEa,MAAM,CAAC,CAAC;IAC/B;IACA,IAAI,CAACwI,OAAO,EAAExI,MAAM,CAAC,CAAC;IACtB,IAAI,CAACyI,eAAe,CAAC,CAAC;IACtB,IAAI,CAACC,mBAAmB,CAAC,CAAC;IAC1B,IAAI,CAACjG,iBAAiB,CAACkG,cAAc,CAAC,IAAI,CAAC;IAC3C,IAAI,CAACC,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACrF,aAAa,CAACsF,QAAQ,CAAC,CAAC;IAC7B,IAAI,CAACrF,OAAO,CAACqF,QAAQ,CAAC,CAAC;IACvB,IAAI,CAACpF,QAAQ,CAACoF,QAAQ,CAAC,CAAC;IACxB,IAAI,CAACnF,KAAK,CAACmF,QAAQ,CAAC,CAAC;IACrB,IAAI,CAAClF,OAAO,CAACkF,QAAQ,CAAC,CAAC;IACvB,IAAI,CAACjF,MAAM,CAACiF,QAAQ,CAAC,CAAC;IACtB,IAAI,CAAChF,OAAO,CAACgF,QAAQ,CAAC,CAAC;IACvB,IAAI,CAAChG,WAAW,CAACgG,QAAQ,CAAC,CAAC;IAC3B,IAAI,CAACzG,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACiB,gBAAgB,CAAC7I,KAAK,CAAC,CAAC;IAC7B,IAAI,CAAC0H,cAAc,GAAG0F,SAAS;IAC/B,IAAI,CAAC1E,mBAAmB,CAACgF,WAAW,CAAC,CAAC;IACtC,IAAI,CAACrB,gBAAgB,CAACrM,KAAK,CAAC,CAAC;IAC7B,IAAI,CAAC2I,gBAAgB,GACjB,IAAI,CAAChE,YAAY,GACb,IAAI,CAAC2I,gBAAgB,GACjB,IAAI,CAACN,oBAAoB,GACrB,IAAI,CAAClI,gBAAgB,GACjB,IAAI,CAACkJ,OAAO,GACR,IAAI,CAACF,cAAc,GACf,IAAI;EACpC;EACA;EACAnD,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACvC,mBAAmB,IAAI,IAAI,CAACH,iBAAiB,CAAC0C,UAAU,CAAC,IAAI,CAAC;EAC9E;EACA;EACA2D,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC3J,YAAY,CAAClH,KAAK,CAACO,SAAS,GAAG,IAAI,CAACiH,iBAAiB,IAAI,EAAE;IAChE,IAAI,CAACkD,gBAAgB,GAAG;MAAE3J,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IACtC,IAAI,CAACyJ,iBAAiB,GAAG;MAAE1J,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;EAC3C;EACA;AACJ;AACA;AACA;EACI8P,aAAaA,CAAC1G,MAAM,EAAE;IAClB,IAAI,CAAC,IAAI,CAACgB,gBAAgB,CAAC1L,GAAG,CAAC0K,MAAM,CAAC,IAAI,IAAI,CAACD,QAAQ,CAACpE,OAAO,CAACqE,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE;MAC1E,IAAI,CAACgB,gBAAgB,CAAC/C,GAAG,CAAC+B,MAAM,CAAC;MACjCxK,4BAA4B,CAACwK,MAAM,EAAE,IAAI,CAAC;IAC9C;EACJ;EACA;AACJ;AACA;AACA;EACI2G,YAAYA,CAAC3G,MAAM,EAAE;IACjB,IAAI,IAAI,CAACgB,gBAAgB,CAAC1L,GAAG,CAAC0K,MAAM,CAAC,EAAE;MACnC,IAAI,CAACgB,gBAAgB,CAAC4F,MAAM,CAAC5G,MAAM,CAAC;MACpCxK,4BAA4B,CAACwK,MAAM,EAAE,IAAI,CAACL,QAAQ,CAAC;IACvD;EACJ;EACA;EACAkH,aAAaA,CAACC,SAAS,EAAE;IACrB,IAAI,CAAC/J,UAAU,GAAG+J,SAAS;IAC3B,OAAO,IAAI;EACf;EACA;EACAC,kBAAkBA,CAACnE,SAAS,EAAE;IAC1B,IAAI,CAAC/C,cAAc,GAAG+C,SAAS;EACnC;EACA;AACJ;AACA;EACIoE,mBAAmBA,CAAA,EAAG;IAClB,MAAMlR,QAAQ,GAAG,IAAI,CAACgN,UAAU,CAAC,CAAC,GAAG,IAAI,CAACxC,gBAAgB,GAAG,IAAI,CAACD,iBAAiB;IACnF,OAAO;MAAE1J,CAAC,EAAEb,QAAQ,CAACa,CAAC;MAAEC,CAAC,EAAEd,QAAQ,CAACc;IAAE,CAAC;EAC3C;EACA;AACJ;AACA;AACA;EACIqQ,mBAAmBA,CAAC7R,KAAK,EAAE;IACvB,IAAI,CAACkL,gBAAgB,GAAG;MAAE3J,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IACtC,IAAI,CAACyJ,iBAAiB,CAAC1J,CAAC,GAAGvB,KAAK,CAACuB,CAAC;IAClC,IAAI,CAAC0J,iBAAiB,CAACzJ,CAAC,GAAGxB,KAAK,CAACwB,CAAC;IAClC,IAAI,CAAC,IAAI,CAACiJ,cAAc,EAAE;MACtB,IAAI,CAACgE,0BAA0B,CAACzO,KAAK,CAACuB,CAAC,EAAEvB,KAAK,CAACwB,CAAC,CAAC;IACrD;IACA,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIsQ,oBAAoBA,CAAC9R,KAAK,EAAE;IACxB,IAAI,CAAC+R,iBAAiB,GAAG/R,KAAK;IAC9B,OAAO,IAAI;EACf;EACA;EACAgS,4BAA4BA,CAAA,EAAG;IAC3B,MAAMtR,QAAQ,GAAG,IAAI,CAACyN,yBAAyB;IAC/C,IAAIzN,QAAQ,IAAI,IAAI,CAAC+J,cAAc,EAAE;MACjC,IAAI,CAAC4D,0BAA0B,CAAC,IAAI,CAACJ,8BAA8B,CAACvN,QAAQ,CAAC,EAAEA,QAAQ,CAAC;IAC5F;EACJ;EACA;EACAyQ,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAC9F,wBAAwB,CAACoF,WAAW,CAAC,CAAC;IAC3C,IAAI,CAAClF,sBAAsB,CAACkF,WAAW,CAAC,CAAC;IACzC,IAAI,CAACjF,mBAAmB,CAACiF,WAAW,CAAC,CAAC;IACtC,IAAI,CAACnS,cAAc,CAAC,CAAC,EAAE2K,mBAAmB,CAAC,aAAa,EAAEgJ,oBAAoB,EAAE/H,6BAA6B,CAAC;EAClH;EACA;EACA8G,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC5I,QAAQ,EAAEE,OAAO,CAAC,CAAC;IACxB,IAAI,CAACF,QAAQ,GAAG,IAAI;EACxB;EACA;EACA6I,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAAC1B,YAAY,EAAEhH,MAAM,CAAC,CAAC;IAC3B,IAAI,CAAC2J,eAAe,EAAE5J,OAAO,CAAC,CAAC;IAC/B,IAAI,CAACiH,YAAY,GAAG,IAAI,CAAC2C,eAAe,GAAG,IAAI;EACnD;EACA;AACJ;AACA;AACA;EACIzE,gBAAgBA,CAAChK,KAAK,EAAE;IACpB;IACA;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAACuH,iBAAiB,CAAC0C,UAAU,CAAC,IAAI,CAAC,EAAE;MAC1C;IACJ;IACA,IAAI,CAACyD,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACnG,iBAAiB,CAACmH,YAAY,CAAC,IAAI,CAAC;IACzC,IAAI,CAACzH,6BAA6B,CAAC,CAAC;IACpC,IAAI,IAAI,CAACC,QAAQ,EAAE;MACf,IAAI,CAACjD,YAAY,CAAClH,KAAK,CAAC4R,uBAAuB,GAC3C,IAAI,CAACC,wBAAwB;IACrC;IACA,IAAI,CAAC,IAAI,CAAClH,mBAAmB,EAAE;MAC3B;IACJ;IACA,IAAI,CAACa,QAAQ,CAACO,IAAI,CAAC;MAAE3M,MAAM,EAAE,IAAI;MAAE6D;IAAM,CAAC,CAAC;IAC3C,IAAI,IAAI,CAACgH,cAAc,EAAE;MACrB;MACA,IAAI,CAACA,cAAc,CAAC6H,cAAc,CAAC,CAAC;MACpC,IAAI,CAACC,4BAA4B,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;QAC3C,IAAI,CAACC,qBAAqB,CAAChP,KAAK,CAAC;QACjC,IAAI,CAACiP,wBAAwB,CAAC,CAAC;QAC/B,IAAI,CAAC1H,iBAAiB,CAACmH,YAAY,CAAC,IAAI,CAAC;MAC7C,CAAC,CAAC;IACN,CAAC,MACI;MACD;MACA;MACA;MACA,IAAI,CAAClH,iBAAiB,CAAC1J,CAAC,GAAG,IAAI,CAAC2J,gBAAgB,CAAC3J,CAAC;MAClD,MAAMqL,eAAe,GAAG,IAAI,CAACC,yBAAyB,CAACpJ,KAAK,CAAC;MAC7D,IAAI,CAACwH,iBAAiB,CAACzJ,CAAC,GAAG,IAAI,CAAC0J,gBAAgB,CAAC1J,CAAC;MAClD,IAAI,CAACsJ,OAAO,CAACgD,GAAG,CAAC,MAAM;QACnB,IAAI,CAAC7B,KAAK,CAACM,IAAI,CAAC;UACZ3M,MAAM,EAAE,IAAI;UACZ+O,QAAQ,EAAE,IAAI,CAACC,gBAAgB,CAAChC,eAAe,CAAC;UAChD+F,SAAS,EAAE/F,eAAe;UAC1BnJ;QACJ,CAAC,CAAC;MACN,CAAC,CAAC;MACF,IAAI,CAACiP,wBAAwB,CAAC,CAAC;MAC/B,IAAI,CAAC1H,iBAAiB,CAACmH,YAAY,CAAC,IAAI,CAAC;IAC7C;EACJ;EACA;EACApE,kBAAkBA,CAACtK,KAAK,EAAE;IACtB,IAAImP,YAAY,CAACnP,KAAK,CAAC,EAAE;MACrB,IAAI,CAACoP,mBAAmB,GAAGzF,IAAI,CAACC,GAAG,CAAC,CAAC;IACzC;IACA,IAAI,CAAC3C,6BAA6B,CAAC,CAAC;IACpC;IACA,MAAMoI,UAAU,GAAG,IAAI,CAACxU,cAAc,CAAC,CAAC;IACxC,MAAMyU,aAAa,GAAG,IAAI,CAACtI,cAAc;IACzC,IAAIqI,UAAU,EAAE;MACZ;MACA;MACA,IAAI,CAAChI,OAAO,CAACoF,iBAAiB,CAAC,MAAM;QACjC4C,UAAU,CAAC/J,gBAAgB,CAAC,aAAa,EAAEkJ,oBAAoB,EAAE/H,6BAA6B,CAAC;MACnG,CAAC,CAAC;IACN;IACA,IAAI6I,aAAa,EAAE;MACf,MAAM1S,OAAO,GAAG,IAAI,CAACqH,YAAY;MACjC,MAAMS,MAAM,GAAG9H,OAAO,CAAC2S,UAAU;MACjC,MAAMC,WAAW,GAAI,IAAI,CAAC1D,YAAY,GAAG,IAAI,CAAC2D,yBAAyB,CAAC,CAAE;MAC1E,MAAMC,MAAM,GAAI,IAAI,CAACpC,OAAO,GAAG,IAAI,CAACA,OAAO,IAAI,IAAI,CAACnO,SAAS,CAACwQ,aAAa,CAAC,EAAE,CAAE;MAChF;MACAjL,MAAM,CAACkL,YAAY,CAACF,MAAM,EAAE9S,OAAO,CAAC;MACpC;MACA;MACA,IAAI,CAAC2H,iBAAiB,GAAG3H,OAAO,CAACG,KAAK,CAACO,SAAS,IAAI,EAAE;MACtD;MACA;MACA,IAAI,CAACqH,QAAQ,GAAG,IAAIX,UAAU,CAAC,IAAI,CAAC7E,SAAS,EAAE,IAAI,CAAC8E,YAAY,EAAE,IAAI,CAACC,UAAU,EAAE,IAAI,CAACC,eAAe,EAAE,IAAI,CAACC,gBAAgB,IAAI,IAAI,EAAE,IAAI,CAACsB,YAAY,IAAI,IAAI,EAAE,IAAI,CAACpB,qBAAqB,EAAE,IAAI,CAACC,iBAAiB,EAAE,IAAI,CAAC6C,OAAO,CAACyI,MAAM,IAAI,IAAI,CAAC;MACnP,IAAI,CAAClL,QAAQ,CAACF,MAAM,CAAC,IAAI,CAACqL,yBAAyB,CAACpL,MAAM,EAAE2K,UAAU,CAAC,CAAC;MACxE;MACA;MACA;MACArS,gBAAgB,CAACJ,OAAO,EAAE,KAAK,EAAEgK,uBAAuB,CAAC;MACzD,IAAI,CAACzH,SAAS,CAAC4Q,IAAI,CAACpN,WAAW,CAAC+B,MAAM,CAACsL,YAAY,CAACR,WAAW,EAAE5S,OAAO,CAAC,CAAC;MAC1E,IAAI,CAAC0L,OAAO,CAACQ,IAAI,CAAC;QAAE3M,MAAM,EAAE,IAAI;QAAE6D;MAAM,CAAC,CAAC,CAAC,CAAC;MAC5CsP,aAAa,CAACW,KAAK,CAAC,CAAC;MACrB,IAAI,CAACC,iBAAiB,GAAGZ,aAAa;MACtC,IAAI,CAACa,aAAa,GAAGb,aAAa,CAACc,YAAY,CAAC,IAAI,CAAC;IACzD,CAAC,MACI;MACD,IAAI,CAAC9H,OAAO,CAACQ,IAAI,CAAC;QAAE3M,MAAM,EAAE,IAAI;QAAE6D;MAAM,CAAC,CAAC;MAC1C,IAAI,CAACkQ,iBAAiB,GAAG,IAAI,CAACC,aAAa,GAAGzD,SAAS;IAC3D;IACA;IACA;IACA,IAAI,CAACf,gBAAgB,CAACpM,KAAK,CAAC+P,aAAa,GAAGA,aAAa,CAACe,oBAAoB,CAAC,CAAC,GAAG,EAAE,CAAC;EAC1F;EACA;AACJ;AACA;AACA;AACA;AACA;EACIpH,uBAAuBA,CAACqH,gBAAgB,EAAEtQ,KAAK,EAAE;IAC7C;IACA;IACA,IAAI,IAAI,CAACoN,cAAc,EAAE;MACrBpN,KAAK,CAACuQ,eAAe,CAAC,CAAC;IAC3B;IACA,MAAMtG,UAAU,GAAG,IAAI,CAACA,UAAU,CAAC,CAAC;IACpC,MAAMuG,eAAe,GAAGrB,YAAY,CAACnP,KAAK,CAAC;IAC3C,MAAMyQ,sBAAsB,GAAG,CAACD,eAAe,IAAIxQ,KAAK,CAAC0Q,MAAM,KAAK,CAAC;IACrE,MAAMnE,WAAW,GAAG,IAAI,CAACtI,YAAY;IACrC,MAAMxG,MAAM,GAAG9C,eAAe,CAACqF,KAAK,CAAC;IACrC,MAAM2Q,gBAAgB,GAAG,CAACH,eAAe,IACrC,IAAI,CAACpB,mBAAmB,IACxB,IAAI,CAACA,mBAAmB,GAAGzI,uBAAuB,GAAGgD,IAAI,CAACC,GAAG,CAAC,CAAC;IACnE,MAAMgH,WAAW,GAAGJ,eAAe,GAC7BvV,gCAAgC,CAAC+E,KAAK,CAAC,GACvC9E,+BAA+B,CAAC8E,KAAK,CAAC;IAC5C;IACA;IACA;IACA;IACA;IACA;IACA,IAAIvC,MAAM,IAAIA,MAAM,CAACoT,SAAS,IAAI7Q,KAAK,CAAC+B,IAAI,KAAK,WAAW,EAAE;MAC1D/B,KAAK,CAACoK,cAAc,CAAC,CAAC;IAC1B;IACA;IACA,IAAIH,UAAU,IAAIwG,sBAAsB,IAAIE,gBAAgB,IAAIC,WAAW,EAAE;MACzE;IACJ;IACA;IACA;IACA;IACA,IAAI,IAAI,CAAC1J,QAAQ,CAAC5F,MAAM,EAAE;MACtB,MAAMwP,UAAU,GAAGvE,WAAW,CAACxP,KAAK;MACpC,IAAI,CAAC6R,wBAAwB,GAAGkC,UAAU,CAACnC,uBAAuB,IAAI,EAAE;MACxEmC,UAAU,CAACnC,uBAAuB,GAAG,aAAa;IACtD;IACA,IAAI,CAACjH,mBAAmB,GAAG,IAAI,CAAC+C,SAAS,GAAG,KAAK;IACjD;IACA;IACA,IAAI,CAACiD,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACvJ,eAAe,GAAG,IAAI,CAACF,YAAY,CAAC7F,qBAAqB,CAAC,CAAC;IAChE,IAAI,CAACwJ,wBAAwB,GAAG,IAAI,CAACL,iBAAiB,CAACwJ,WAAW,CAAC7D,SAAS,CAAC,IAAI,CAAChE,YAAY,CAAC;IAC/F,IAAI,CAACpB,sBAAsB,GAAG,IAAI,CAACP,iBAAiB,CAACyJ,SAAS,CAAC9D,SAAS,CAAC,IAAI,CAAC5B,UAAU,CAAC;IACzF,IAAI,CAACvD,mBAAmB,GAAG,IAAI,CAACR,iBAAiB,CAC5C0J,QAAQ,CAAC,IAAI,CAACpW,cAAc,CAAC,CAAC,CAAC,CAC/BqS,SAAS,CAACgE,WAAW,IAAI,IAAI,CAACC,eAAe,CAACD,WAAW,CAAC,CAAC;IAChE,IAAI,IAAI,CAACjJ,gBAAgB,EAAE;MACvB,IAAI,CAACmJ,aAAa,GAAGlT,oBAAoB,CAAC,IAAI,CAAC+J,gBAAgB,CAAC;IACpE;IACA;IACA;IACA;IACA,MAAMtC,eAAe,GAAG,IAAI,CAACvB,gBAAgB;IAC7C,IAAI,CAACiN,wBAAwB,GACzB1L,eAAe,IAAIA,eAAe,CAACC,QAAQ,IAAI,CAACD,eAAe,CAACI,SAAS,GACnE;MAAEjI,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC,GACd,IAAI,CAACuT,4BAA4B,CAAC,IAAI,CAACnN,eAAe,EAAEmM,gBAAgB,EAAEtQ,KAAK,CAAC;IAC1F,MAAMmJ,eAAe,GAAI,IAAI,CAAC7E,qBAAqB,GAC/C,IAAI,CAACoG,yBAAyB,GAC1B,IAAI,CAACtB,yBAAyB,CAACpJ,KAAK,CAAE;IAC9C,IAAI,CAACqL,sBAAsB,GAAG;MAAEvN,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IAC5C,IAAI,CAACwT,qCAAqC,GAAG;MAAEzT,CAAC,EAAEqL,eAAe,CAACrL,CAAC;MAAEC,CAAC,EAAEoL,eAAe,CAACpL;IAAE,CAAC;IAC3F,IAAI,CAAC8L,cAAc,GAAGF,IAAI,CAACC,GAAG,CAAC,CAAC;IAChC,IAAI,CAACrC,iBAAiB,CAACiK,aAAa,CAAC,IAAI,EAAExR,KAAK,CAAC;EACrD;EACA;EACAgP,qBAAqBA,CAAChP,KAAK,EAAE;IACzB;IACA;IACA;IACA;IACAhD,gBAAgB,CAAC,IAAI,CAACiH,YAAY,EAAE,IAAI,EAAE2C,uBAAuB,CAAC;IAClE,IAAI,CAAC0G,OAAO,CAACiC,UAAU,CAACS,YAAY,CAAC,IAAI,CAAC/L,YAAY,EAAE,IAAI,CAACqJ,OAAO,CAAC;IACrE,IAAI,CAACC,eAAe,CAAC,CAAC;IACtB,IAAI,CAACC,mBAAmB,CAAC,CAAC;IAC1B,IAAI,CAACrJ,eAAe,GAChB,IAAI,CAACiN,aAAa,GACd,IAAI,CAACK,YAAY,GACb,IAAI,CAAClN,iBAAiB,GAClBmI,SAAS;IACzB;IACA,IAAI,CAACrF,OAAO,CAACgD,GAAG,CAAC,MAAM;MACnB,MAAMN,SAAS,GAAG,IAAI,CAAC/C,cAAc;MACrC,MAAM0K,YAAY,GAAG3H,SAAS,CAACqG,YAAY,CAAC,IAAI,CAAC;MACjD,MAAMjH,eAAe,GAAG,IAAI,CAACC,yBAAyB,CAACpJ,KAAK,CAAC;MAC7D,MAAMkL,QAAQ,GAAG,IAAI,CAACC,gBAAgB,CAAChC,eAAe,CAAC;MACvD,MAAMwI,sBAAsB,GAAG5H,SAAS,CAAC6H,gBAAgB,CAACzI,eAAe,CAACrL,CAAC,EAAEqL,eAAe,CAACpL,CAAC,CAAC;MAC/F,IAAI,CAACyK,KAAK,CAACM,IAAI,CAAC;QAAE3M,MAAM,EAAE,IAAI;QAAE+O,QAAQ;QAAEgE,SAAS,EAAE/F,eAAe;QAAEnJ;MAAM,CAAC,CAAC;MAC9E,IAAI,CAAC2I,OAAO,CAACG,IAAI,CAAC;QACd+I,IAAI,EAAE,IAAI;QACVH,YAAY;QACZI,aAAa,EAAE,IAAI,CAAC3B,aAAa;QACjCpG,SAAS,EAAEA,SAAS;QACpBgI,iBAAiB,EAAE,IAAI,CAAC7B,iBAAiB;QACzCyB,sBAAsB;QACtBzG,QAAQ;QACRgE,SAAS,EAAE/F,eAAe;QAC1BnJ;MACJ,CAAC,CAAC;MACF+J,SAAS,CAACiI,IAAI,CAAC,IAAI,EAAEN,YAAY,EAAE,IAAI,CAACvB,aAAa,EAAE,IAAI,CAACD,iBAAiB,EAAEyB,sBAAsB,EAAEzG,QAAQ,EAAE/B,eAAe,EAAEnJ,KAAK,CAAC;MACxI,IAAI,CAACgH,cAAc,GAAG,IAAI,CAACkJ,iBAAiB;IAChD,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACItF,0BAA0BA,CAAC;IAAE9M,CAAC;IAAEC;EAAE,CAAC,EAAE;IAAED,CAAC,EAAEmU,IAAI;IAAElU,CAAC,EAAEmU;EAAK,CAAC,EAAE;IACvD;IACA,IAAIC,YAAY,GAAG,IAAI,CAACjC,iBAAiB,CAACkC,gCAAgC,CAAC,IAAI,EAAEtU,CAAC,EAAEC,CAAC,CAAC;IACtF;IACA;IACA;IACA;IACA,IAAI,CAACoU,YAAY,IACb,IAAI,CAACnL,cAAc,KAAK,IAAI,CAACkJ,iBAAiB,IAC9C,IAAI,CAACA,iBAAiB,CAAC0B,gBAAgB,CAAC9T,CAAC,EAAEC,CAAC,CAAC,EAAE;MAC/CoU,YAAY,GAAG,IAAI,CAACjC,iBAAiB;IACzC;IACA,IAAIiC,YAAY,IAAIA,YAAY,KAAK,IAAI,CAACnL,cAAc,EAAE;MACtD,IAAI,CAACK,OAAO,CAACgD,GAAG,CAAC,MAAM;QACnB;QACA,IAAI,CAAC3B,MAAM,CAACI,IAAI,CAAC;UAAE+I,IAAI,EAAE,IAAI;UAAE9H,SAAS,EAAE,IAAI,CAAC/C;QAAe,CAAC,CAAC;QAChE,IAAI,CAACA,cAAc,CAACqL,IAAI,CAAC,IAAI,CAAC;QAC9B;QACA,IAAI,CAACrL,cAAc,GAAGmL,YAAY;QAClC,IAAI,CAACnL,cAAc,CAACsL,KAAK,CAAC,IAAI,EAAExU,CAAC,EAAEC,CAAC,EAAEoU,YAAY,KAAK,IAAI,CAACjC,iBAAiB;QACzE;QACA;QACAiC,YAAY,CAACI,eAAe,GAC1B,IAAI,CAACpC,aAAa,GAClBzD,SAAS,CAAC;QAChB,IAAI,CAACjE,OAAO,CAACK,IAAI,CAAC;UACd+I,IAAI,EAAE,IAAI;UACV9H,SAAS,EAAEoI,YAAY;UACvBT,YAAY,EAAES,YAAY,CAAC/B,YAAY,CAAC,IAAI;QAChD,CAAC,CAAC;MACN,CAAC,CAAC;IACN;IACA;IACA,IAAI,IAAI,CAACnG,UAAU,CAAC,CAAC,EAAE;MACnB,IAAI,CAACjD,cAAc,CAACwL,0BAA0B,CAACP,IAAI,EAAEC,IAAI,CAAC;MAC1D,IAAI,CAAClL,cAAc,CAACyL,SAAS,CAAC,IAAI,EAAE3U,CAAC,EAAEC,CAAC,EAAE,IAAI,CAACsN,sBAAsB,CAAC;MACtE,IAAI,IAAI,CAACP,iBAAiB,EAAE;QACxB,IAAI,CAAC4H,sBAAsB,CAAC5U,CAAC,EAAEC,CAAC,CAAC;MACrC,CAAC,MACI;QACD,IAAI,CAAC2U,sBAAsB,CAAC5U,CAAC,GAAG,IAAI,CAACuT,wBAAwB,CAACvT,CAAC,EAAEC,CAAC,GAAG,IAAI,CAACsT,wBAAwB,CAACtT,CAAC,CAAC;MACzG;IACJ;EACJ;EACA;AACJ;AACA;AACA;EACI+Q,4BAA4BA,CAAA,EAAG;IAC3B;IACA,IAAI,CAAC,IAAI,CAACrE,SAAS,EAAE;MACjB,OAAOkI,OAAO,CAACC,OAAO,CAAC,CAAC;IAC5B;IACA,MAAMC,eAAe,GAAG,IAAI,CAAC/G,YAAY,CAAC1N,qBAAqB,CAAC,CAAC;IACjE;IACA,IAAI,CAACuG,QAAQ,CAACM,QAAQ,CAAC,oBAAoB,CAAC;IAC5C;IACA,IAAI,CAACyN,sBAAsB,CAACG,eAAe,CAACzV,IAAI,EAAEyV,eAAe,CAAC3V,GAAG,CAAC;IACtE;IACA;IACA;IACA;IACA,MAAM4V,QAAQ,GAAG,IAAI,CAACnO,QAAQ,CAACU,qBAAqB,CAAC,CAAC;IACtD,IAAIyN,QAAQ,KAAK,CAAC,EAAE;MAChB,OAAOH,OAAO,CAACC,OAAO,CAAC,CAAC;IAC5B;IACA,OAAO,IAAI,CAACvL,OAAO,CAACoF,iBAAiB,CAAC,MAAM;MACxC,OAAO,IAAIkG,OAAO,CAACC,OAAO,IAAI;QAC1B,MAAMrN,OAAO,GAAKvF,KAAK,IAAK;UACxB,IAAI,CAACA,KAAK,IACLrF,eAAe,CAACqF,KAAK,CAAC,KAAK,IAAI,CAAC2E,QAAQ,IAAI3E,KAAK,CAAC+S,YAAY,KAAK,WAAY,EAAE;YAClF,IAAI,CAACpO,QAAQ,EAAEa,mBAAmB,CAAC,eAAe,EAAED,OAAO,CAAC;YAC5DqN,OAAO,CAAC,CAAC;YACTI,YAAY,CAACC,OAAO,CAAC;UACzB;QACJ,CAAE;QACF;QACA;QACA;QACA,MAAMA,OAAO,GAAGC,UAAU,CAAC3N,OAAO,EAAEuN,QAAQ,GAAG,GAAG,CAAC;QACnD,IAAI,CAACnO,QAAQ,CAACW,gBAAgB,CAAC,eAAe,EAAEC,OAAO,CAAC;MAC5D,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA;EACAkK,yBAAyBA,CAAA,EAAG;IACxB,MAAM0D,iBAAiB,GAAG,IAAI,CAAC7G,oBAAoB;IACnD,MAAM8G,mBAAmB,GAAGD,iBAAiB,GAAGA,iBAAiB,CAACvN,QAAQ,GAAG,IAAI;IACjF,IAAI4J,WAAW;IACf,IAAI4D,mBAAmB,EAAE;MACrB,IAAI,CAAC3E,eAAe,GAAG0E,iBAAiB,CAACnN,aAAa,CAACC,kBAAkB,CAACmN,mBAAmB,EAAED,iBAAiB,CAAClR,OAAO,CAAC;MACzH,IAAI,CAACwM,eAAe,CAACvI,aAAa,CAAC,CAAC;MACpCsJ,WAAW,GAAGpN,WAAW,CAAC,IAAI,CAACqM,eAAe,EAAE,IAAI,CAACtP,SAAS,CAAC;IACnE,CAAC,MACI;MACDqQ,WAAW,GAAG3O,aAAa,CAAC,IAAI,CAACoD,YAAY,CAAC;IAClD;IACA;IACA;IACAuL,WAAW,CAACzS,KAAK,CAACsW,aAAa,GAAG,MAAM;IACxC7D,WAAW,CAACrK,SAAS,CAACC,GAAG,CAAC,sBAAsB,CAAC;IACjD,OAAOoK,WAAW;EACtB;EACA;AACJ;AACA;AACA;AACA;EACI8B,4BAA4BA,CAACgC,WAAW,EAAEhD,gBAAgB,EAAEtQ,KAAK,EAAE;IAC/D,MAAMuT,aAAa,GAAGjD,gBAAgB,KAAK,IAAI,CAACrM,YAAY,GAAG,IAAI,GAAGqM,gBAAgB;IACtF,MAAMkD,aAAa,GAAGD,aAAa,GAAGA,aAAa,CAACnV,qBAAqB,CAAC,CAAC,GAAGkV,WAAW;IACzF,MAAMG,KAAK,GAAGtE,YAAY,CAACnP,KAAK,CAAC,GAAGA,KAAK,CAAC0T,aAAa,CAAC,CAAC,CAAC,GAAG1T,KAAK;IAClE,MAAMN,cAAc,GAAG,IAAI,CAACiU,0BAA0B,CAAC,CAAC;IACxD,MAAM7V,CAAC,GAAG2V,KAAK,CAACG,KAAK,GAAGJ,aAAa,CAACpW,IAAI,GAAGsC,cAAc,CAACtC,IAAI;IAChE,MAAMW,CAAC,GAAG0V,KAAK,CAACI,KAAK,GAAGL,aAAa,CAACtW,GAAG,GAAGwC,cAAc,CAACxC,GAAG;IAC9D,OAAO;MACHY,CAAC,EAAE0V,aAAa,CAACpW,IAAI,GAAGkW,WAAW,CAAClW,IAAI,GAAGU,CAAC;MAC5CC,CAAC,EAAEyV,aAAa,CAACtW,GAAG,GAAGoW,WAAW,CAACpW,GAAG,GAAGa;IAC7C,CAAC;EACL;EACA;EACAqL,yBAAyBA,CAACpJ,KAAK,EAAE;IAC7B,MAAMN,cAAc,GAAG,IAAI,CAACiU,0BAA0B,CAAC,CAAC;IACxD,MAAMF,KAAK,GAAGtE,YAAY,CAACnP,KAAK,CAAC;IAC3B;IACE;IACA;IACA;IACA;IACA;IACA;IACAA,KAAK,CAAC8T,OAAO,CAAC,CAAC,CAAC,IAAI9T,KAAK,CAAC+T,cAAc,CAAC,CAAC,CAAC,IAAI;MAAEH,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAE,CAAC,GACvE7T,KAAK;IACX,MAAMlC,CAAC,GAAG2V,KAAK,CAACG,KAAK,GAAGlU,cAAc,CAACtC,IAAI;IAC3C,MAAMW,CAAC,GAAG0V,KAAK,CAACI,KAAK,GAAGnU,cAAc,CAACxC,GAAG;IAC1C;IACA;IACA,IAAI,IAAI,CAAC0P,gBAAgB,EAAE;MACvB,MAAMoH,SAAS,GAAG,IAAI,CAACpH,gBAAgB,CAACqH,YAAY,CAAC,CAAC;MACtD,IAAID,SAAS,EAAE;QACX,MAAME,QAAQ,GAAG,IAAI,CAACtH,gBAAgB,CAACuH,cAAc,CAAC,CAAC;QACvDD,QAAQ,CAACpW,CAAC,GAAGA,CAAC;QACdoW,QAAQ,CAACnW,CAAC,GAAGA,CAAC;QACd,OAAOmW,QAAQ,CAACE,eAAe,CAACJ,SAAS,CAACK,OAAO,CAAC,CAAC,CAAC;MACxD;IACJ;IACA,OAAO;MAAEvW,CAAC;MAAEC;IAAE,CAAC;EACnB;EACA;EACAyM,8BAA8BA,CAACiJ,KAAK,EAAE;IAClC,MAAMa,iBAAiB,GAAG,IAAI,CAACtN,cAAc,GAAG,IAAI,CAACA,cAAc,CAACuN,QAAQ,GAAG,IAAI;IACnF,IAAI;MAAEzW,CAAC;MAAEC;IAAE,CAAC,GAAG,IAAI,CAAC+M,iBAAiB,GAC/B,IAAI,CAACA,iBAAiB,CAAC2I,KAAK,EAAE,IAAI,EAAE,IAAI,CAACtP,eAAe,EAAE,IAAI,CAACkN,wBAAwB,CAAC,GACxFoC,KAAK;IACX,IAAI,IAAI,CAACc,QAAQ,KAAK,GAAG,IAAID,iBAAiB,KAAK,GAAG,EAAE;MACpDvW,CAAC,GACG,IAAI,CAACuG,qBAAqB,CAACvG,CAAC,IACvB,IAAI,CAAC+M,iBAAiB,GAAG,IAAI,CAACuG,wBAAwB,CAACtT,CAAC,GAAG,CAAC,CAAC;IAC1E,CAAC,MACI,IAAI,IAAI,CAACwW,QAAQ,KAAK,GAAG,IAAID,iBAAiB,KAAK,GAAG,EAAE;MACzDxW,CAAC,GACG,IAAI,CAACwG,qBAAqB,CAACxG,CAAC,IACvB,IAAI,CAACgN,iBAAiB,GAAG,IAAI,CAACuG,wBAAwB,CAACvT,CAAC,GAAG,CAAC,CAAC;IAC1E;IACA,IAAI,IAAI,CAACsT,aAAa,EAAE;MACpB;MACA;MACA,MAAM;QAAEtT,CAAC,EAAE0W,OAAO;QAAEzW,CAAC,EAAE0W;MAAQ,CAAC,GAAG,CAAC,IAAI,CAAC3J,iBAAiB,GACpD,IAAI,CAACuG,wBAAwB,GAC7B;QAAEvT,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC;MACpB,MAAM2W,YAAY,GAAG,IAAI,CAACtD,aAAa;MACvC,MAAM;QAAEzT,KAAK,EAAEgX,YAAY;QAAE/W,MAAM,EAAEgX;MAAc,CAAC,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;MAC7E,MAAMC,IAAI,GAAGJ,YAAY,CAACxX,GAAG,GAAGuX,OAAO;MACvC,MAAMM,IAAI,GAAGL,YAAY,CAACpW,MAAM,IAAIsW,aAAa,GAAGH,OAAO,CAAC;MAC5D,MAAMO,IAAI,GAAGN,YAAY,CAACtX,IAAI,GAAGoX,OAAO;MACxC,MAAMS,IAAI,GAAGP,YAAY,CAACrW,KAAK,IAAIsW,YAAY,GAAGH,OAAO,CAAC;MAC1D1W,CAAC,GAAGoX,OAAO,CAACpX,CAAC,EAAEkX,IAAI,EAAEC,IAAI,CAAC;MAC1BlX,CAAC,GAAGmX,OAAO,CAACnX,CAAC,EAAE+W,IAAI,EAAEC,IAAI,CAAC;IAC9B;IACA,OAAO;MAAEjX,CAAC;MAAEC;IAAE,CAAC;EACnB;EACA;EACA4M,4BAA4BA,CAACwK,qBAAqB,EAAE;IAChD,MAAM;MAAErX,CAAC;MAAEC;IAAE,CAAC,GAAGoX,qBAAqB;IACtC,MAAM/J,KAAK,GAAG,IAAI,CAACC,sBAAsB;IACzC,MAAM+J,uBAAuB,GAAG,IAAI,CAAC7D,qCAAqC;IAC1E;IACA,MAAM8D,OAAO,GAAGrX,IAAI,CAACsL,GAAG,CAACxL,CAAC,GAAGsX,uBAAuB,CAACtX,CAAC,CAAC;IACvD,MAAMwX,OAAO,GAAGtX,IAAI,CAACsL,GAAG,CAACvL,CAAC,GAAGqX,uBAAuB,CAACrX,CAAC,CAAC;IACvD;IACA;IACA;IACA;IACA,IAAIsX,OAAO,GAAG,IAAI,CAACjO,OAAO,CAACmO,+BAA+B,EAAE;MACxDnK,KAAK,CAACtN,CAAC,GAAGA,CAAC,GAAGsX,uBAAuB,CAACtX,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;MAChDsX,uBAAuB,CAACtX,CAAC,GAAGA,CAAC;IACjC;IACA,IAAIwX,OAAO,GAAG,IAAI,CAAClO,OAAO,CAACmO,+BAA+B,EAAE;MACxDnK,KAAK,CAACrN,CAAC,GAAGA,CAAC,GAAGqX,uBAAuB,CAACrX,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;MAChDqX,uBAAuB,CAACrX,CAAC,GAAGA,CAAC;IACjC;IACA,OAAOqN,KAAK;EAChB;EACA;EACAnE,6BAA6BA,CAAA,EAAG;IAC5B,IAAI,CAAC,IAAI,CAAChD,YAAY,IAAI,CAAC,IAAI,CAACiD,QAAQ,EAAE;MACtC;IACJ;IACA,MAAMsO,YAAY,GAAG,IAAI,CAACtO,QAAQ,CAAC5F,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC2I,UAAU,CAAC,CAAC;IACnE,IAAIuL,YAAY,KAAK,IAAI,CAACtN,0BAA0B,EAAE;MAClD,IAAI,CAACA,0BAA0B,GAAGsN,YAAY;MAC9C7Y,4BAA4B,CAAC,IAAI,CAACsH,YAAY,EAAEuR,YAAY,CAAC;IACjE;EACJ;EACA;EACAhJ,2BAA2BA,CAAC5P,OAAO,EAAE;IACjCA,OAAO,CAAC4I,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACqD,YAAY,EAAErC,0BAA0B,CAAC;IACvF5J,OAAO,CAAC4I,mBAAmB,CAAC,YAAY,EAAE,IAAI,CAACqD,YAAY,EAAEvC,2BAA2B,CAAC;IACzF1J,OAAO,CAAC4I,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC+F,gBAAgB,EAAE/E,0BAA0B,CAAC;EAC/F;EACA;AACJ;AACA;AACA;AACA;EACIwE,0BAA0BA,CAAClN,CAAC,EAAEC,CAAC,EAAE;IAC7B,MAAMT,SAAS,GAAGO,YAAY,CAACC,CAAC,EAAEC,CAAC,CAAC;IACpC,MAAM0X,MAAM,GAAG,IAAI,CAACxR,YAAY,CAAClH,KAAK;IACtC;IACA;IACA;IACA,IAAI,IAAI,CAACwH,iBAAiB,IAAI,IAAI,EAAE;MAChC,IAAI,CAACA,iBAAiB,GAClBkR,MAAM,CAACnY,SAAS,IAAImY,MAAM,CAACnY,SAAS,IAAI,MAAM,GAAGmY,MAAM,CAACnY,SAAS,GAAG,EAAE;IAC9E;IACA;IACA;IACA;IACAmY,MAAM,CAACnY,SAAS,GAAGD,iBAAiB,CAACC,SAAS,EAAE,IAAI,CAACiH,iBAAiB,CAAC;EAC3E;EACA;AACJ;AACA;AACA;AACA;EACImO,sBAAsBA,CAAC5U,CAAC,EAAEC,CAAC,EAAE;IACzB;IACA;IACA,MAAMR,gBAAgB,GAAG,IAAI,CAAC6G,gBAAgB,EAAEwB,QAAQ,GAAG8G,SAAS,GAAG,IAAI,CAACnI,iBAAiB;IAC7F,MAAMjH,SAAS,GAAGO,YAAY,CAACC,CAAC,EAAEC,CAAC,CAAC;IACpC,IAAI,CAAC4G,QAAQ,CAACK,YAAY,CAAC3H,iBAAiB,CAACC,SAAS,EAAEC,gBAAgB,CAAC,CAAC;EAC9E;EACA;AACJ;AACA;AACA;EACI4N,gBAAgBA,CAACuK,eAAe,EAAE;IAC9B,MAAMC,cAAc,GAAG,IAAI,CAACrR,qBAAqB;IACjD,IAAIqR,cAAc,EAAE;MAChB,OAAO;QAAE7X,CAAC,EAAE4X,eAAe,CAAC5X,CAAC,GAAG6X,cAAc,CAAC7X,CAAC;QAAEC,CAAC,EAAE2X,eAAe,CAAC3X,CAAC,GAAG4X,cAAc,CAAC5X;MAAE,CAAC;IAC/F;IACA,OAAO;MAAED,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;EACzB;EACA;EACAkR,wBAAwBA,CAAA,EAAG;IACvB,IAAI,CAACmC,aAAa,GAAG,IAAI,CAACK,YAAY,GAAG/E,SAAS;IAClD,IAAI,CAACf,gBAAgB,CAACrM,KAAK,CAAC,CAAC;EACjC;EACA;AACJ;AACA;AACA;EACI6N,8BAA8BA,CAAA,EAAG;IAC7B,IAAI;MAAErP,CAAC;MAAEC;IAAE,CAAC,GAAG,IAAI,CAACyJ,iBAAiB;IACrC,IAAK1J,CAAC,KAAK,CAAC,IAAIC,CAAC,KAAK,CAAC,IAAK,IAAI,CAACkM,UAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAAChC,gBAAgB,EAAE;MACrE;IACJ;IACA;IACA,MAAMqL,WAAW,GAAG,IAAI,CAACrP,YAAY,CAAC7F,qBAAqB,CAAC,CAAC;IAC7D,MAAMsW,YAAY,GAAG,IAAI,CAACzM,gBAAgB,CAAC7J,qBAAqB,CAAC,CAAC;IAClE;IACA;IACA,IAAKsW,YAAY,CAAC/W,KAAK,KAAK,CAAC,IAAI+W,YAAY,CAAC9W,MAAM,KAAK,CAAC,IACrD0V,WAAW,CAAC3V,KAAK,KAAK,CAAC,IAAI2V,WAAW,CAAC1V,MAAM,KAAK,CAAE,EAAE;MACvD;IACJ;IACA,MAAMgY,YAAY,GAAGlB,YAAY,CAACtX,IAAI,GAAGkW,WAAW,CAAClW,IAAI;IACzD,MAAMyY,aAAa,GAAGvC,WAAW,CAACjV,KAAK,GAAGqW,YAAY,CAACrW,KAAK;IAC5D,MAAMyX,WAAW,GAAGpB,YAAY,CAACxX,GAAG,GAAGoW,WAAW,CAACpW,GAAG;IACtD,MAAM6Y,cAAc,GAAGzC,WAAW,CAAChV,MAAM,GAAGoW,YAAY,CAACpW,MAAM;IAC/D;IACA;IACA,IAAIoW,YAAY,CAAC/W,KAAK,GAAG2V,WAAW,CAAC3V,KAAK,EAAE;MACxC,IAAIiY,YAAY,GAAG,CAAC,EAAE;QAClB9X,CAAC,IAAI8X,YAAY;MACrB;MACA,IAAIC,aAAa,GAAG,CAAC,EAAE;QACnB/X,CAAC,IAAI+X,aAAa;MACtB;IACJ,CAAC,MACI;MACD/X,CAAC,GAAG,CAAC;IACT;IACA;IACA;IACA,IAAI4W,YAAY,CAAC9W,MAAM,GAAG0V,WAAW,CAAC1V,MAAM,EAAE;MAC1C,IAAIkY,WAAW,GAAG,CAAC,EAAE;QACjB/X,CAAC,IAAI+X,WAAW;MACpB;MACA,IAAIC,cAAc,GAAG,CAAC,EAAE;QACpBhY,CAAC,IAAIgY,cAAc;MACvB;IACJ,CAAC,MACI;MACDhY,CAAC,GAAG,CAAC;IACT;IACA,IAAID,CAAC,KAAK,IAAI,CAAC0J,iBAAiB,CAAC1J,CAAC,IAAIC,CAAC,KAAK,IAAI,CAACyJ,iBAAiB,CAACzJ,CAAC,EAAE;MAClE,IAAI,CAACqQ,mBAAmB,CAAC;QAAErQ,CAAC;QAAED;MAAE,CAAC,CAAC;IACtC;EACJ;EACA;EACAgM,kBAAkBA,CAAC9J,KAAK,EAAE;IACtB,MAAMzD,KAAK,GAAG,IAAI,CAAC6L,cAAc;IACjC,IAAI,OAAO7L,KAAK,KAAK,QAAQ,EAAE;MAC3B,OAAOA,KAAK;IAChB,CAAC,MACI,IAAI4S,YAAY,CAACnP,KAAK,CAAC,EAAE;MAC1B,OAAOzD,KAAK,CAACyZ,KAAK;IACtB;IACA,OAAOzZ,KAAK,GAAGA,KAAK,CAAC0Z,KAAK,GAAG,CAAC;EAClC;EACA;EACA9E,eAAeA,CAACnR,KAAK,EAAE;IACnB,MAAMkW,gBAAgB,GAAG,IAAI,CAACvK,gBAAgB,CAAC5L,YAAY,CAACC,KAAK,CAAC;IAClE,IAAIkW,gBAAgB,EAAE;MAClB,MAAMzY,MAAM,GAAG9C,eAAe,CAACqF,KAAK,CAAC;MACrC;MACA;MACA,IAAI,IAAI,CAACoR,aAAa,IAClB3T,MAAM,KAAK,IAAI,CAACwK,gBAAgB,IAChCxK,MAAM,CAACgD,QAAQ,CAAC,IAAI,CAACwH,gBAAgB,CAAC,EAAE;QACxCxJ,aAAa,CAAC,IAAI,CAAC2S,aAAa,EAAE8E,gBAAgB,CAAChZ,GAAG,EAAEgZ,gBAAgB,CAAC9Y,IAAI,CAAC;MAClF;MACA,IAAI,CAACkH,qBAAqB,CAACxG,CAAC,IAAIoY,gBAAgB,CAAC9Y,IAAI;MACrD,IAAI,CAACkH,qBAAqB,CAACvG,CAAC,IAAImY,gBAAgB,CAAChZ,GAAG;MACpD;MACA;MACA,IAAI,CAAC,IAAI,CAAC8J,cAAc,EAAE;QACtB,IAAI,CAACS,gBAAgB,CAAC3J,CAAC,IAAIoY,gBAAgB,CAAC9Y,IAAI;QAChD,IAAI,CAACqK,gBAAgB,CAAC1J,CAAC,IAAImY,gBAAgB,CAAChZ,GAAG;QAC/C,IAAI,CAAC8N,0BAA0B,CAAC,IAAI,CAACvD,gBAAgB,CAAC3J,CAAC,EAAE,IAAI,CAAC2J,gBAAgB,CAAC1J,CAAC,CAAC;MACrF;IACJ;EACJ;EACA;EACA4V,0BAA0BA,CAAA,EAAG;IACzB,OAAQ,IAAI,CAAChI,gBAAgB,CAACvM,SAAS,CAACc,GAAG,CAAC,IAAI,CAACf,SAAS,CAAC,EAAEO,cAAc,IACvE,IAAI,CAACiM,gBAAgB,CAAChM,yBAAyB,CAAC,CAAC;EACzD;EACA;AACJ;AACA;AACA;AACA;AACA;EACI9E,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAACsb,iBAAiB,KAAKzJ,SAAS,EAAE;MACtC,IAAI,CAACyJ,iBAAiB,GAAGtb,cAAc,CAAC,IAAI,CAACoJ,YAAY,CAAC;IAC9D;IACA,OAAO,IAAI,CAACkS,iBAAiB;EACjC;EACA;EACArG,yBAAyBA,CAACsG,aAAa,EAAE/G,UAAU,EAAE;IACjD,MAAMgH,gBAAgB,GAAG,IAAI,CAAC/H,iBAAiB,IAAI,QAAQ;IAC3D,IAAI+H,gBAAgB,KAAK,QAAQ,EAAE;MAC/B,OAAOD,aAAa;IACxB;IACA,IAAIC,gBAAgB,KAAK,QAAQ,EAAE;MAC/B,MAAMC,WAAW,GAAG,IAAI,CAACnX,SAAS;MAClC;MACA;MACA;MACA,OAAQkQ,UAAU,IACdiH,WAAW,CAACC,iBAAiB,IAC7BD,WAAW,CAACE,uBAAuB,IACnCF,WAAW,CAACG,oBAAoB,IAChCH,WAAW,CAACI,mBAAmB,IAC/BJ,WAAW,CAACvG,IAAI;IACxB;IACA,OAAOjV,aAAa,CAACub,gBAAgB,CAAC;EAC1C;EACA;EACAxB,eAAeA,CAAA,EAAG;IACd;IACA;IACA,IAAI,CAAC,IAAI,CAACpD,YAAY,IAAK,CAAC,IAAI,CAACA,YAAY,CAAC9T,KAAK,IAAI,CAAC,IAAI,CAAC8T,YAAY,CAAC7T,MAAO,EAAE;MAC/E,IAAI,CAAC6T,YAAY,GAAG,IAAI,CAAC9M,QAAQ,GAC3B,IAAI,CAACA,QAAQ,CAACvG,qBAAqB,CAAC,CAAC,GACrC,IAAI,CAAC+F,eAAe;IAC9B;IACA,OAAO,IAAI,CAACsN,YAAY;EAC5B;EACA;EACAzI,gBAAgBA,CAAChJ,KAAK,EAAE;IACpB,OAAO,IAAI,CAACkH,QAAQ,CAAC5D,IAAI,CAAC6D,MAAM,IAAI;MAChC,OAAOnH,KAAK,CAACvC,MAAM,KAAKuC,KAAK,CAACvC,MAAM,KAAK0J,MAAM,IAAIA,MAAM,CAAC1G,QAAQ,CAACT,KAAK,CAACvC,MAAM,CAAC,CAAC;IACrF,CAAC,CAAC;EACN;AACJ;AACA;AACA,SAASyX,OAAOA,CAAC3Y,KAAK,EAAEoa,GAAG,EAAEC,GAAG,EAAE;EAC9B,OAAO5Y,IAAI,CAAC4Y,GAAG,CAACD,GAAG,EAAE3Y,IAAI,CAAC2Y,GAAG,CAACC,GAAG,EAAEra,KAAK,CAAC,CAAC;AAC9C;AACA;AACA,SAAS4S,YAAYA,CAACnP,KAAK,EAAE;EACzB;EACA;EACA;EACA,OAAOA,KAAK,CAAC+B,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG;AAChC;AACA;AACA,SAASyM,oBAAoBA,CAACxO,KAAK,EAAE;EACjCA,KAAK,CAACoK,cAAc,CAAC,CAAC;AAC1B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASyM,eAAeA,CAACC,KAAK,EAAEC,SAAS,EAAEC,OAAO,EAAE;EAChD,MAAMC,IAAI,GAAGC,KAAK,CAACH,SAAS,EAAED,KAAK,CAACxV,MAAM,GAAG,CAAC,CAAC;EAC/C,MAAM6V,EAAE,GAAGD,KAAK,CAACF,OAAO,EAAEF,KAAK,CAACxV,MAAM,GAAG,CAAC,CAAC;EAC3C,IAAI2V,IAAI,KAAKE,EAAE,EAAE;IACb;EACJ;EACA,MAAM1Z,MAAM,GAAGqZ,KAAK,CAACG,IAAI,CAAC;EAC1B,MAAM7L,KAAK,GAAG+L,EAAE,GAAGF,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC;EAChC,KAAK,IAAI5V,CAAC,GAAG4V,IAAI,EAAE5V,CAAC,KAAK8V,EAAE,EAAE9V,CAAC,IAAI+J,KAAK,EAAE;IACrC0L,KAAK,CAACzV,CAAC,CAAC,GAAGyV,KAAK,CAACzV,CAAC,GAAG+J,KAAK,CAAC;EAC/B;EACA0L,KAAK,CAACK,EAAE,CAAC,GAAG1Z,MAAM;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS2Z,iBAAiBA,CAACC,YAAY,EAAEC,WAAW,EAAE5F,YAAY,EAAE6F,WAAW,EAAE;EAC7E,MAAMN,IAAI,GAAGC,KAAK,CAACxF,YAAY,EAAE2F,YAAY,CAAC/V,MAAM,GAAG,CAAC,CAAC;EACzD,MAAM6V,EAAE,GAAGD,KAAK,CAACK,WAAW,EAAED,WAAW,CAAChW,MAAM,CAAC;EACjD,IAAI+V,YAAY,CAAC/V,MAAM,EAAE;IACrBgW,WAAW,CAACE,MAAM,CAACL,EAAE,EAAE,CAAC,EAAEE,YAAY,CAACG,MAAM,CAACP,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9D;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASQ,aAAaA,CAACJ,YAAY,EAAEC,WAAW,EAAE5F,YAAY,EAAE6F,WAAW,EAAE;EACzE,MAAMJ,EAAE,GAAGD,KAAK,CAACK,WAAW,EAAED,WAAW,CAAChW,MAAM,CAAC;EACjD,IAAI+V,YAAY,CAAC/V,MAAM,EAAE;IACrBgW,WAAW,CAACE,MAAM,CAACL,EAAE,EAAE,CAAC,EAAEE,YAAY,CAAC3F,YAAY,CAAC,CAAC;EACzD;AACJ;AACA;AACA,SAASwF,KAAKA,CAAC3a,KAAK,EAAEqa,GAAG,EAAE;EACvB,OAAO5Y,IAAI,CAAC4Y,GAAG,CAAC,CAAC,EAAE5Y,IAAI,CAAC2Y,GAAG,CAACC,GAAG,EAAEra,KAAK,CAAC,CAAC;AAC5C;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMmb,sBAAsB,CAAC;EACzBxY,WAAWA,CAACyY,QAAQ,EAAEpQ,iBAAiB,EAAE;IACrC,IAAI,CAACoQ,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACpQ,iBAAiB,GAAGA,iBAAiB;IAC1C;IACA,IAAI,CAACqQ,cAAc,GAAG,EAAE;IACxB;IACA,IAAI,CAACC,WAAW,GAAG,UAAU;IAC7B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,aAAa,GAAG;MACjBC,IAAI,EAAE,IAAI;MACV3M,KAAK,EAAE,CAAC;MACR4M,QAAQ,EAAE;IACd,CAAC;EACL;EACA;AACJ;AACA;AACA;EACI/H,KAAKA,CAACgI,KAAK,EAAE;IACT,IAAI,CAACC,SAAS,CAACD,KAAK,CAAC;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIE,IAAIA,CAACtG,IAAI,EAAEhT,QAAQ,EAAEC,QAAQ,EAAEsZ,YAAY,EAAE;IACzC,MAAMC,QAAQ,GAAG,IAAI,CAACT,cAAc;IACpC,MAAMU,QAAQ,GAAG,IAAI,CAACC,gCAAgC,CAAC1G,IAAI,EAAEhT,QAAQ,EAAEC,QAAQ,EAAEsZ,YAAY,CAAC;IAC9F,IAAIE,QAAQ,KAAK,CAAC,CAAC,IAAID,QAAQ,CAAC/W,MAAM,GAAG,CAAC,EAAE;MACxC,OAAO,IAAI;IACf;IACA,MAAMkX,YAAY,GAAG,IAAI,CAACX,WAAW,KAAK,YAAY;IACtD,MAAMnG,YAAY,GAAG2G,QAAQ,CAACI,SAAS,CAACC,WAAW,IAAIA,WAAW,CAACX,IAAI,KAAKlG,IAAI,CAAC;IACjF,MAAM8G,oBAAoB,GAAGN,QAAQ,CAACC,QAAQ,CAAC;IAC/C,MAAM5C,eAAe,GAAG2C,QAAQ,CAAC3G,YAAY,CAAC,CAAClT,UAAU;IACzD,MAAMoa,WAAW,GAAGD,oBAAoB,CAACna,UAAU;IACnD,MAAM4M,KAAK,GAAGsG,YAAY,GAAG4G,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC;IAC9C;IACA,MAAMO,UAAU,GAAG,IAAI,CAACC,gBAAgB,CAACpD,eAAe,EAAEkD,WAAW,EAAExN,KAAK,CAAC;IAC7E;IACA,MAAM2N,aAAa,GAAG,IAAI,CAACC,mBAAmB,CAACtH,YAAY,EAAE2G,QAAQ,EAAEjN,KAAK,CAAC;IAC7E;IACA;IACA,MAAM6N,QAAQ,GAAGZ,QAAQ,CAACa,KAAK,CAAC,CAAC;IACjC;IACArC,eAAe,CAACwB,QAAQ,EAAE3G,YAAY,EAAE4G,QAAQ,CAAC;IACjDD,QAAQ,CAACzY,OAAO,CAAC,CAACuZ,OAAO,EAAEC,KAAK,KAAK;MACjC;MACA,IAAIH,QAAQ,CAACG,KAAK,CAAC,KAAKD,OAAO,EAAE;QAC7B;MACJ;MACA,MAAME,aAAa,GAAGF,OAAO,CAACpB,IAAI,KAAKlG,IAAI;MAC3C,MAAMhH,MAAM,GAAGwO,aAAa,GAAGR,UAAU,GAAGE,aAAa;MACzD,MAAMO,eAAe,GAAGD,aAAa,GAC/BxH,IAAI,CAAChG,qBAAqB,CAAC,CAAC,GAC5BsN,OAAO,CAACpB,IAAI,CAAChM,cAAc,CAAC,CAAC;MACnC;MACAoN,OAAO,CAACtO,MAAM,IAAIA,MAAM;MACxB;MACA;MACA;MACA;MACA,IAAI2N,YAAY,EAAE;QACd;QACA;QACAc,eAAe,CAACvc,KAAK,CAACO,SAAS,GAAGD,iBAAiB,CAAE,eAAcW,IAAI,CAACC,KAAK,CAACkb,OAAO,CAACtO,MAAM,CAAE,WAAU,EAAEsO,OAAO,CAAC5b,gBAAgB,CAAC;QACnIkB,aAAa,CAAC0a,OAAO,CAAC3a,UAAU,EAAE,CAAC,EAAEqM,MAAM,CAAC;MAChD,CAAC,MACI;QACDyO,eAAe,CAACvc,KAAK,CAACO,SAAS,GAAGD,iBAAiB,CAAE,kBAAiBW,IAAI,CAACC,KAAK,CAACkb,OAAO,CAACtO,MAAM,CAAE,QAAO,EAAEsO,OAAO,CAAC5b,gBAAgB,CAAC;QACnIkB,aAAa,CAAC0a,OAAO,CAAC3a,UAAU,EAAEqM,MAAM,EAAE,CAAC,CAAC;MAChD;IACJ,CAAC,CAAC;IACF;IACA,IAAI,CAACiN,aAAa,CAACE,QAAQ,GAAGzZ,kBAAkB,CAACqa,WAAW,EAAE/Z,QAAQ,EAAEC,QAAQ,CAAC;IACjF,IAAI,CAACgZ,aAAa,CAACC,IAAI,GAAGY,oBAAoB,CAACZ,IAAI;IACnD,IAAI,CAACD,aAAa,CAAC1M,KAAK,GAAGoN,YAAY,GAAGJ,YAAY,CAACta,CAAC,GAAGsa,YAAY,CAACra,CAAC;IACzE,OAAO;MAAE+T,aAAa,EAAEJ,YAAY;MAAEA,YAAY,EAAE4G;IAAS,CAAC;EAClE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIhG,KAAKA,CAACT,IAAI,EAAEhT,QAAQ,EAAEC,QAAQ,EAAEsa,KAAK,EAAE;IACnC,MAAMd,QAAQ,GAAGc,KAAK,IAAI,IAAI,IAAIA,KAAK,GAAG,CAAC;IACrC;IACE;IACA,IAAI,CAACb,gCAAgC,CAAC1G,IAAI,EAAEhT,QAAQ,EAAEC,QAAQ,CAAC,GACjEsa,KAAK;IACX,MAAMG,gBAAgB,GAAG,IAAI,CAACC,iBAAiB;IAC/C,MAAM9H,YAAY,GAAG6H,gBAAgB,CAACzW,OAAO,CAAC+O,IAAI,CAAC;IACnD,MAAMrC,WAAW,GAAGqC,IAAI,CAAChG,qBAAqB,CAAC,CAAC;IAChD,IAAI4N,oBAAoB,GAAGF,gBAAgB,CAACjB,QAAQ,CAAC;IACrD;IACA;IACA;IACA,IAAImB,oBAAoB,KAAK5H,IAAI,EAAE;MAC/B4H,oBAAoB,GAAGF,gBAAgB,CAACjB,QAAQ,GAAG,CAAC,CAAC;IACzD;IACA;IACA;IACA,IAAI,CAACmB,oBAAoB,KACpBnB,QAAQ,IAAI,IAAI,IAAIA,QAAQ,KAAK,CAAC,CAAC,IAAIA,QAAQ,GAAGiB,gBAAgB,CAACjY,MAAM,GAAG,CAAC,CAAC,IAC/E,IAAI,CAACoY,wBAAwB,CAAC7a,QAAQ,EAAEC,QAAQ,CAAC,EAAE;MACnD2a,oBAAoB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;IAC9C;IACA;IACA;IACA,IAAI7H,YAAY,GAAG,CAAC,CAAC,EAAE;MACnB6H,gBAAgB,CAAC/B,MAAM,CAAC9F,YAAY,EAAE,CAAC,CAAC;IAC5C;IACA;IACA;IACA,IAAI+H,oBAAoB,IAAI,CAAC,IAAI,CAAClS,iBAAiB,CAAC0C,UAAU,CAACwP,oBAAoB,CAAC,EAAE;MAClF,MAAM7c,OAAO,GAAG6c,oBAAoB,CAAC1N,cAAc,CAAC,CAAC;MACrDnP,OAAO,CAAC+c,aAAa,CAAC/J,YAAY,CAACJ,WAAW,EAAE5S,OAAO,CAAC;MACxD2c,gBAAgB,CAAC/B,MAAM,CAACc,QAAQ,EAAE,CAAC,EAAEzG,IAAI,CAAC;IAC9C,CAAC,MACI;MACD/W,aAAa,CAAC,IAAI,CAAC6c,QAAQ,CAAC,CAAChV,WAAW,CAAC6M,WAAW,CAAC;MACrD+J,gBAAgB,CAACK,IAAI,CAAC/H,IAAI,CAAC;IAC/B;IACA;IACArC,WAAW,CAACzS,KAAK,CAACO,SAAS,GAAG,EAAE;IAChC;IACA;IACA;IACA,IAAI,CAACuc,mBAAmB,CAAC,CAAC;EAC9B;EACA;EACA3B,SAASA,CAACD,KAAK,EAAE;IACb,IAAI,CAACuB,iBAAiB,GAAGvB,KAAK,CAACiB,KAAK,CAAC,CAAC;IACtC,IAAI,CAACW,mBAAmB,CAAC,CAAC;EAC9B;EACA;EACAC,iBAAiBA,CAACC,SAAS,EAAE;IACzB,IAAI,CAACC,cAAc,GAAGD,SAAS;EACnC;EACA;EACAnM,KAAKA,CAAA,EAAG;IACJ;IACA,IAAI,CAAC4L,iBAAiB,CAAC5Z,OAAO,CAACiS,IAAI,IAAI;MACnC,MAAMtF,WAAW,GAAGsF,IAAI,CAAC9F,cAAc,CAAC,CAAC;MACzC,IAAIQ,WAAW,EAAE;QACb,MAAMhP,gBAAgB,GAAG,IAAI,CAACqa,cAAc,CAACtU,IAAI,CAAC2W,CAAC,IAAIA,CAAC,CAAClC,IAAI,KAAKlG,IAAI,CAAC,EAAEtU,gBAAgB;QACzFgP,WAAW,CAACxP,KAAK,CAACO,SAAS,GAAGC,gBAAgB,IAAI,EAAE;MACxD;IACJ,CAAC,CAAC;IACF,IAAI,CAACqa,cAAc,GAAG,EAAE;IACxB,IAAI,CAAC4B,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAAC1B,aAAa,CAACC,IAAI,GAAG,IAAI;IAC9B,IAAI,CAACD,aAAa,CAAC1M,KAAK,GAAG,CAAC;IAC5B,IAAI,CAAC0M,aAAa,CAACE,QAAQ,GAAG,KAAK;EACvC;EACA;AACJ;AACA;AACA;EACIkC,sBAAsBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACV,iBAAiB;EACjC;EACA;EACApJ,YAAYA,CAACyB,IAAI,EAAE;IACf;IACA;IACA;IACA,MAAMoG,KAAK,GAAG,IAAI,CAACJ,WAAW,KAAK,YAAY,IAAI,IAAI,CAAC5J,SAAS,KAAK,KAAK,GACrE,IAAI,CAAC2J,cAAc,CAACsB,KAAK,CAAC,CAAC,CAACiB,OAAO,CAAC,CAAC,GACrC,IAAI,CAACvC,cAAc;IACzB,OAAOK,KAAK,CAACQ,SAAS,CAACC,WAAW,IAAIA,WAAW,CAACX,IAAI,KAAKlG,IAAI,CAAC;EACpE;EACA;EACAuI,cAAcA,CAAC9Z,aAAa,EAAEC,cAAc,EAAE;IAC1C;IACA;IACA;IACA;IACA,IAAI,CAACqX,cAAc,CAAChY,OAAO,CAAC,CAAC;MAAEpB;IAAW,CAAC,KAAK;MAC5CC,aAAa,CAACD,UAAU,EAAE8B,aAAa,EAAEC,cAAc,CAAC;IAC5D,CAAC,CAAC;IACF;IACA;IACA,IAAI,CAACqX,cAAc,CAAChY,OAAO,CAAC,CAAC;MAAEmY;IAAK,CAAC,KAAK;MACtC,IAAI,IAAI,CAACxQ,iBAAiB,CAAC0C,UAAU,CAAC8N,IAAI,CAAC,EAAE;QACzC;QACA;QACAA,IAAI,CAACxJ,4BAA4B,CAAC,CAAC;MACvC;IACJ,CAAC,CAAC;EACN;EACA;EACAsL,mBAAmBA,CAAA,EAAG;IAClB,MAAMrB,YAAY,GAAG,IAAI,CAACX,WAAW,KAAK,YAAY;IACtD,IAAI,CAACD,cAAc,GAAG,IAAI,CAAC4B,iBAAiB,CACvC7d,GAAG,CAACoc,IAAI,IAAI;MACb,MAAMsC,gBAAgB,GAAGtC,IAAI,CAAC/L,iBAAiB,CAAC,CAAC;MACjD,OAAO;QACH+L,IAAI;QACJlN,MAAM,EAAE,CAAC;QACTtN,gBAAgB,EAAE8c,gBAAgB,CAACtd,KAAK,CAACO,SAAS,IAAI,EAAE;QACxDkB,UAAU,EAAEN,oBAAoB,CAACmc,gBAAgB;MACrD,CAAC;IACL,CAAC,CAAC,CACGlC,IAAI,CAAC,CAACmC,CAAC,EAAEC,CAAC,KAAK;MAChB,OAAO/B,YAAY,GACb8B,CAAC,CAAC9b,UAAU,CAACpB,IAAI,GAAGmd,CAAC,CAAC/b,UAAU,CAACpB,IAAI,GACrCkd,CAAC,CAAC9b,UAAU,CAACtB,GAAG,GAAGqd,CAAC,CAAC/b,UAAU,CAACtB,GAAG;IAC7C,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;EACI4b,gBAAgBA,CAACpD,eAAe,EAAEkD,WAAW,EAAExN,KAAK,EAAE;IAClD,MAAMoN,YAAY,GAAG,IAAI,CAACX,WAAW,KAAK,YAAY;IACtD,IAAIgB,UAAU,GAAGL,YAAY,GACvBI,WAAW,CAACxb,IAAI,GAAGsY,eAAe,CAACtY,IAAI,GACvCwb,WAAW,CAAC1b,GAAG,GAAGwY,eAAe,CAACxY,GAAG;IAC3C;IACA,IAAIkO,KAAK,KAAK,CAAC,CAAC,EAAE;MACdyN,UAAU,IAAIL,YAAY,GACpBI,WAAW,CAACjb,KAAK,GAAG+X,eAAe,CAAC/X,KAAK,GACzCib,WAAW,CAAChb,MAAM,GAAG8X,eAAe,CAAC9X,MAAM;IACrD;IACA,OAAOib,UAAU;EACrB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIG,mBAAmBA,CAACtH,YAAY,EAAE2G,QAAQ,EAAEjN,KAAK,EAAE;IAC/C,MAAMoN,YAAY,GAAG,IAAI,CAACX,WAAW,KAAK,YAAY;IACtD,MAAMnC,eAAe,GAAG2C,QAAQ,CAAC3G,YAAY,CAAC,CAAClT,UAAU;IACzD,MAAMgc,gBAAgB,GAAGnC,QAAQ,CAAC3G,YAAY,GAAGtG,KAAK,GAAG,CAAC,CAAC,CAAC;IAC5D,IAAI2N,aAAa,GAAGrD,eAAe,CAAC8C,YAAY,GAAG,OAAO,GAAG,QAAQ,CAAC,GAAGpN,KAAK;IAC9E,IAAIoP,gBAAgB,EAAE;MAClB,MAAMvK,KAAK,GAAGuI,YAAY,GAAG,MAAM,GAAG,KAAK;MAC3C,MAAMiC,GAAG,GAAGjC,YAAY,GAAG,OAAO,GAAG,QAAQ;MAC7C;MACA;MACA;MACA;MACA,IAAIpN,KAAK,KAAK,CAAC,CAAC,EAAE;QACd2N,aAAa,IAAIyB,gBAAgB,CAAChc,UAAU,CAACyR,KAAK,CAAC,GAAGyF,eAAe,CAAC+E,GAAG,CAAC;MAC9E,CAAC,MACI;QACD1B,aAAa,IAAIrD,eAAe,CAACzF,KAAK,CAAC,GAAGuK,gBAAgB,CAAChc,UAAU,CAACic,GAAG,CAAC;MAC9E;IACJ;IACA,OAAO1B,aAAa;EACxB;EACA;AACJ;AACA;AACA;AACA;EACIW,wBAAwBA,CAAC7a,QAAQ,EAAEC,QAAQ,EAAE;IACzC,IAAI,CAAC,IAAI,CAAC0a,iBAAiB,CAAClY,MAAM,EAAE;MAChC,OAAO,KAAK;IAChB;IACA,MAAMoZ,aAAa,GAAG,IAAI,CAAC9C,cAAc;IACzC,MAAMY,YAAY,GAAG,IAAI,CAACX,WAAW,KAAK,YAAY;IACtD;IACA;IACA,MAAM8C,QAAQ,GAAGD,aAAa,CAAC,CAAC,CAAC,CAAC3C,IAAI,KAAK,IAAI,CAACyB,iBAAiB,CAAC,CAAC,CAAC;IACpE,IAAImB,QAAQ,EAAE;MACV,MAAMC,YAAY,GAAGF,aAAa,CAACA,aAAa,CAACpZ,MAAM,GAAG,CAAC,CAAC,CAAC9C,UAAU;MACvE,OAAOga,YAAY,GAAG3Z,QAAQ,IAAI+b,YAAY,CAACvc,KAAK,GAAGS,QAAQ,IAAI8b,YAAY,CAACtc,MAAM;IAC1F,CAAC,MACI;MACD,MAAMuc,aAAa,GAAGH,aAAa,CAAC,CAAC,CAAC,CAAClc,UAAU;MACjD,OAAOga,YAAY,GAAG3Z,QAAQ,IAAIgc,aAAa,CAACzd,IAAI,GAAG0B,QAAQ,IAAI+b,aAAa,CAAC3d,GAAG;IACxF;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIqb,gCAAgCA,CAAC1G,IAAI,EAAEhT,QAAQ,EAAEC,QAAQ,EAAEsM,KAAK,EAAE;IAC9D,MAAMoN,YAAY,GAAG,IAAI,CAACX,WAAW,KAAK,YAAY;IACtD,MAAMuB,KAAK,GAAG,IAAI,CAACxB,cAAc,CAACa,SAAS,CAAC,CAAC;MAAEV,IAAI;MAAEvZ;IAAW,CAAC,KAAK;MAClE;MACA,IAAIuZ,IAAI,KAAKlG,IAAI,EAAE;QACf,OAAO,KAAK;MAChB;MACA,IAAIzG,KAAK,EAAE;QACP,MAAM6C,SAAS,GAAGuK,YAAY,GAAGpN,KAAK,CAACtN,CAAC,GAAGsN,KAAK,CAACrN,CAAC;QAClD;QACA;QACA;QACA,IAAIga,IAAI,KAAK,IAAI,CAACD,aAAa,CAACC,IAAI,IAChC,IAAI,CAACD,aAAa,CAACE,QAAQ,IAC3B/J,SAAS,KAAK,IAAI,CAAC6J,aAAa,CAAC1M,KAAK,EAAE;UACxC,OAAO,KAAK;QAChB;MACJ;MACA,OAAOoN,YAAY;MACb;MACE;MACA3Z,QAAQ,IAAIb,IAAI,CAAC8c,KAAK,CAACtc,UAAU,CAACpB,IAAI,CAAC,IAAIyB,QAAQ,GAAGb,IAAI,CAAC8c,KAAK,CAACtc,UAAU,CAACH,KAAK,CAAC,GACpFS,QAAQ,IAAId,IAAI,CAAC8c,KAAK,CAACtc,UAAU,CAACtB,GAAG,CAAC,IAAI4B,QAAQ,GAAGd,IAAI,CAAC8c,KAAK,CAACtc,UAAU,CAACF,MAAM,CAAC;IAC5F,CAAC,CAAC;IACF,OAAO8a,KAAK,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAACY,cAAc,CAACZ,KAAK,EAAEvH,IAAI,CAAC,GAAG,CAAC,CAAC,GAAGuH,KAAK;EACzE;AACJ;;AAEA;AACA;AACA;AACA;AACA,MAAM2B,wBAAwB,GAAG,IAAI;AACrC;AACA;AACA;AACA;AACA,MAAMC,0BAA0B,GAAG,IAAI;AACvC;AACA,IAAIC,2BAA2B;AAC/B,CAAC,UAAUA,2BAA2B,EAAE;EACpCA,2BAA2B,CAACA,2BAA2B,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EAC7EA,2BAA2B,CAACA,2BAA2B,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI;EACzEA,2BAA2B,CAACA,2BAA2B,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;AACjF,CAAC,EAAEA,2BAA2B,KAAKA,2BAA2B,GAAG,CAAC,CAAC,CAAC,CAAC;AACrE;AACA,IAAIC,6BAA6B;AACjC,CAAC,UAAUA,6BAA6B,EAAE;EACtCA,6BAA6B,CAACA,6BAA6B,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EACjFA,6BAA6B,CAACA,6BAA6B,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EACjFA,6BAA6B,CAACA,6BAA6B,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;AACvF,CAAC,EAAEA,6BAA6B,KAAKA,6BAA6B,GAAG,CAAC,CAAC,CAAC,CAAC;AACzE;AACA;AACA;AACA,MAAMC,WAAW,CAAC;EACdjc,WAAWA,CAACtC,OAAO,EAAE2K,iBAAiB,EAAEpI,SAAS,EAAEkI,OAAO,EAAEC,cAAc,EAAE;IACxE,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACF,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC;IACA,IAAI,CAACR,QAAQ,GAAG,KAAK;IACrB;IACA,IAAI,CAACyL,eAAe,GAAG,KAAK;IAC5B;AACR;AACA;AACA;IACQ,IAAI,CAAC6I,kBAAkB,GAAG,KAAK;IAC/B;IACA,IAAI,CAACC,cAAc,GAAG,CAAC;IACvB;AACR;AACA;AACA;IACQ,IAAI,CAACC,cAAc,GAAG,MAAM,IAAI;IAChC;IACA,IAAI,CAACC,aAAa,GAAG,MAAM,IAAI;IAC/B;IACA,IAAI,CAAClT,aAAa,GAAG,IAAIlN,OAAO,CAAC,CAAC;IAClC;AACR;AACA;IACQ,IAAI,CAACsN,OAAO,GAAG,IAAItN,OAAO,CAAC,CAAC;IAC5B;AACR;AACA;AACA;IACQ,IAAI,CAACuN,MAAM,GAAG,IAAIvN,OAAO,CAAC,CAAC;IAC3B;IACA,IAAI,CAACwN,OAAO,GAAG,IAAIxN,OAAO,CAAC,CAAC;IAC5B;IACA,IAAI,CAACqgB,MAAM,GAAG,IAAIrgB,OAAO,CAAC,CAAC;IAC3B;IACA,IAAI,CAACsgB,gBAAgB,GAAG,IAAItgB,OAAO,CAAC,CAAC;IACrC;IACA,IAAI,CAACugB,gBAAgB,GAAG,IAAIvgB,OAAO,CAAC,CAAC;IACrC;IACA,IAAI,CAACwgB,WAAW,GAAG,KAAK;IACxB;IACA,IAAI,CAACC,WAAW,GAAG,EAAE;IACrB;IACA,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB;IACA,IAAI,CAACC,eAAe,GAAG,IAAI/X,GAAG,CAAC,CAAC;IAChC;IACA,IAAI,CAACgY,2BAA2B,GAAG3gB,YAAY,CAACyM,KAAK;IACrD;IACA,IAAI,CAACmU,wBAAwB,GAAGf,2BAA2B,CAACgB,IAAI;IAChE;IACA,IAAI,CAACC,0BAA0B,GAAGhB,6BAA6B,CAACe,IAAI;IACpE;IACA,IAAI,CAACE,iBAAiB,GAAG,IAAIhhB,OAAO,CAAC,CAAC;IACtC;IACA,IAAI,CAACgb,iBAAiB,GAAG,IAAI;IAC7B;IACA,IAAI,CAACiG,oBAAoB,GAAG,MAAM;MAC9B,IAAI,CAACvN,cAAc,CAAC,CAAC;MACrBxT,QAAQ,CAAC,CAAC,EAAEC,uBAAuB,CAAC,CAC/B+gB,IAAI,CAAC3gB,SAAS,CAAC,IAAI,CAACygB,iBAAiB,CAAC,CAAC,CACvCjP,SAAS,CAAC,MAAM;QACjB,MAAM1M,IAAI,GAAG,IAAI,CAAC8b,WAAW;QAC7B,MAAMC,UAAU,GAAG,IAAI,CAAClB,cAAc;QACtC,IAAI,IAAI,CAACW,wBAAwB,KAAKf,2BAA2B,CAACuB,EAAE,EAAE;UAClEhc,IAAI,CAACic,QAAQ,CAAC,CAAC,EAAE,CAACF,UAAU,CAAC;QACjC,CAAC,MACI,IAAI,IAAI,CAACP,wBAAwB,KAAKf,2BAA2B,CAACyB,IAAI,EAAE;UACzElc,IAAI,CAACic,QAAQ,CAAC,CAAC,EAAEF,UAAU,CAAC;QAChC;QACA,IAAI,IAAI,CAACL,0BAA0B,KAAKhB,6BAA6B,CAACyB,IAAI,EAAE;UACxEnc,IAAI,CAACic,QAAQ,CAAC,CAACF,UAAU,EAAE,CAAC,CAAC;QACjC,CAAC,MACI,IAAI,IAAI,CAACL,0BAA0B,KAAKhB,6BAA6B,CAAC0B,KAAK,EAAE;UAC9Epc,IAAI,CAACic,QAAQ,CAACF,UAAU,EAAE,CAAC,CAAC;QAChC;MACJ,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAAC3f,OAAO,GAAG9B,aAAa,CAAC8B,OAAO,CAAC;IACrC,IAAI,CAACuC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAC0d,qBAAqB,CAAC,CAAC,IAAI,CAACjgB,OAAO,CAAC,CAAC;IAC1C2K,iBAAiB,CAACuV,qBAAqB,CAAC,IAAI,CAAC;IAC7C,IAAI,CAACnR,gBAAgB,GAAG,IAAI1M,qBAAqB,CAACE,SAAS,CAAC;IAC5D,IAAI,CAAC4d,aAAa,GAAG,IAAIrF,sBAAsB,CAAC,IAAI,CAAC9a,OAAO,EAAE2K,iBAAiB,CAAC;IAChF,IAAI,CAACwV,aAAa,CAACjD,iBAAiB,CAAC,CAACV,KAAK,EAAEvH,IAAI,KAAK,IAAI,CAAC0J,aAAa,CAACnC,KAAK,EAAEvH,IAAI,EAAE,IAAI,CAAC,CAAC;EAChG;EACA;EACAxE,OAAOA,CAAA,EAAG;IACN,IAAI,CAACwB,cAAc,CAAC,CAAC;IACrB,IAAI,CAACsN,iBAAiB,CAACxO,QAAQ,CAAC,CAAC;IACjC,IAAI,CAACoO,2BAA2B,CAAC/O,WAAW,CAAC,CAAC;IAC9C,IAAI,CAAC3E,aAAa,CAACsF,QAAQ,CAAC,CAAC;IAC7B,IAAI,CAAClF,OAAO,CAACkF,QAAQ,CAAC,CAAC;IACvB,IAAI,CAACjF,MAAM,CAACiF,QAAQ,CAAC,CAAC;IACtB,IAAI,CAAChF,OAAO,CAACgF,QAAQ,CAAC,CAAC;IACvB,IAAI,CAAC6N,MAAM,CAAC7N,QAAQ,CAAC,CAAC;IACtB,IAAI,CAAC8N,gBAAgB,CAAC9N,QAAQ,CAAC,CAAC;IAChC,IAAI,CAAC+N,gBAAgB,CAAC/N,QAAQ,CAAC,CAAC;IAChC,IAAI,CAACmO,eAAe,CAACxc,KAAK,CAAC,CAAC;IAC5B,IAAI,CAACgd,WAAW,GAAG,IAAI;IACvB,IAAI,CAAC3Q,gBAAgB,CAACrM,KAAK,CAAC,CAAC;IAC7B,IAAI,CAACiI,iBAAiB,CAACyV,mBAAmB,CAAC,IAAI,CAAC;EACpD;EACA;EACA/S,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAAC0R,WAAW;EAC3B;EACA;EACA1L,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACgN,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACC,wBAAwB,CAAC,CAAC;EACnC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI5K,KAAKA,CAACT,IAAI,EAAEhT,QAAQ,EAAEC,QAAQ,EAAEsa,KAAK,EAAE;IACnC,IAAI,CAAC6D,gBAAgB,CAAC,CAAC;IACvB;IACA;IACA,IAAI7D,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC7G,eAAe,EAAE;MACvC6G,KAAK,GAAG,IAAI,CAACwC,WAAW,CAAC9Y,OAAO,CAAC+O,IAAI,CAAC;IAC1C;IACA,IAAI,CAACkL,aAAa,CAACzK,KAAK,CAACT,IAAI,EAAEhT,QAAQ,EAAEC,QAAQ,EAAEsa,KAAK,CAAC;IACzD;IACA;IACA,IAAI,CAAC+D,qBAAqB,CAAC,CAAC;IAC5B;IACA,IAAI,CAACD,wBAAwB,CAAC,CAAC;IAC/B,IAAI,CAACzU,OAAO,CAACK,IAAI,CAAC;MAAE+I,IAAI;MAAE9H,SAAS,EAAE,IAAI;MAAE2H,YAAY,EAAE,IAAI,CAACtB,YAAY,CAACyB,IAAI;IAAE,CAAC,CAAC;EACvF;EACA;AACJ;AACA;AACA;EACIQ,IAAIA,CAACR,IAAI,EAAE;IACP,IAAI,CAACuL,MAAM,CAAC,CAAC;IACb,IAAI,CAAC1U,MAAM,CAACI,IAAI,CAAC;MAAE+I,IAAI;MAAE9H,SAAS,EAAE;IAAK,CAAC,CAAC;EAC/C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIiI,IAAIA,CAACH,IAAI,EAAEH,YAAY,EAAEI,aAAa,EAAEC,iBAAiB,EAAEJ,sBAAsB,EAAEzG,QAAQ,EAAEgE,SAAS,EAAElP,KAAK,GAAG,CAAC,CAAC,EAAE;IAChH,IAAI,CAACod,MAAM,CAAC,CAAC;IACb,IAAI,CAACzU,OAAO,CAACG,IAAI,CAAC;MACd+I,IAAI;MACJH,YAAY;MACZI,aAAa;MACb/H,SAAS,EAAE,IAAI;MACfgI,iBAAiB;MACjBJ,sBAAsB;MACtBzG,QAAQ;MACRgE,SAAS;MACTlP;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACIkY,SAASA,CAACD,KAAK,EAAE;IACb,MAAMoF,aAAa,GAAG,IAAI,CAACzB,WAAW;IACtC,IAAI,CAACA,WAAW,GAAG3D,KAAK;IACxBA,KAAK,CAACrY,OAAO,CAACiS,IAAI,IAAIA,IAAI,CAAC3D,kBAAkB,CAAC,IAAI,CAAC,CAAC;IACpD,IAAI,IAAI,CAACjE,UAAU,CAAC,CAAC,EAAE;MACnB,MAAMqT,YAAY,GAAGD,aAAa,CAACE,MAAM,CAAC1L,IAAI,IAAIA,IAAI,CAAC5H,UAAU,CAAC,CAAC,CAAC;MACpE;MACA;MACA,IAAIqT,YAAY,CAACE,KAAK,CAAC3L,IAAI,IAAIoG,KAAK,CAACnV,OAAO,CAAC+O,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;QACxD,IAAI,CAACuL,MAAM,CAAC,CAAC;MACjB,CAAC,MACI;QACD,IAAI,CAACL,aAAa,CAAC7E,SAAS,CAAC,IAAI,CAAC0D,WAAW,CAAC;MAClD;IACJ;IACA,OAAO,IAAI;EACf;EACA;EACA5N,aAAaA,CAACC,SAAS,EAAE;IACrB,IAAI,CAAC8O,aAAa,CAAC9O,SAAS,GAAGA,SAAS;IACxC,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACIwP,WAAWA,CAACA,WAAW,EAAE;IACrB,IAAI,CAAC5B,SAAS,GAAG4B,WAAW,CAACvE,KAAK,CAAC,CAAC;IACpC,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIwE,eAAeA,CAAC7F,WAAW,EAAE;IACzB;IACA;IACA,IAAI,CAACkF,aAAa,CAAClF,WAAW,GAAGA,WAAW;IAC5C,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIgF,qBAAqBA,CAACrd,QAAQ,EAAE;IAC5B,MAAM5C,OAAO,GAAG9B,aAAa,CAAC,IAAI,CAAC8B,OAAO,CAAC;IAC3C;IACA;IACA,IAAI,CAAC+gB,mBAAmB,GACpBne,QAAQ,CAACsD,OAAO,CAAClG,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,CAACA,OAAO,EAAE,GAAG4C,QAAQ,CAAC,GAAGA,QAAQ,CAAC0Z,KAAK,CAAC,CAAC;IAChF,OAAO,IAAI;EACf;EACA;EACA7I,oBAAoBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACsN,mBAAmB;EACnC;EACA;AACJ;AACA;AACA;EACIvN,YAAYA,CAACyB,IAAI,EAAE;IACf,OAAO,IAAI,CAAC8J,WAAW,GACjB,IAAI,CAACoB,aAAa,CAAC3M,YAAY,CAACyB,IAAI,CAAC,GACrC,IAAI,CAAC+J,WAAW,CAAC9Y,OAAO,CAAC+O,IAAI,CAAC;EACxC;EACA;AACJ;AACA;AACA;EACI3H,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC4R,eAAe,CAAC8B,IAAI,GAAG,CAAC;EACxC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACInL,SAASA,CAACZ,IAAI,EAAEhT,QAAQ,EAAEC,QAAQ,EAAEsZ,YAAY,EAAE;IAC9C;IACA,IAAI,IAAI,CAAC7F,eAAe,IACpB,CAAC,IAAI,CAACsL,QAAQ,IACd,CAAClf,oBAAoB,CAAC,IAAI,CAACkf,QAAQ,EAAE9C,wBAAwB,EAAElc,QAAQ,EAAEC,QAAQ,CAAC,EAAE;MACpF;IACJ;IACA,MAAMgf,MAAM,GAAG,IAAI,CAACf,aAAa,CAAC5E,IAAI,CAACtG,IAAI,EAAEhT,QAAQ,EAAEC,QAAQ,EAAEsZ,YAAY,CAAC;IAC9E,IAAI0F,MAAM,EAAE;MACR,IAAI,CAACtC,MAAM,CAAC1S,IAAI,CAAC;QACbgJ,aAAa,EAAEgM,MAAM,CAAChM,aAAa;QACnCJ,YAAY,EAAEoM,MAAM,CAACpM,YAAY;QACjC3H,SAAS,EAAE,IAAI;QACf8H;MACJ,CAAC,CAAC;IACN;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIW,0BAA0BA,CAAC3T,QAAQ,EAAEC,QAAQ,EAAE;IAC3C,IAAI,IAAI,CAACsc,kBAAkB,EAAE;MACzB;IACJ;IACA,IAAI2C,UAAU;IACd,IAAIC,uBAAuB,GAAG/C,2BAA2B,CAACgB,IAAI;IAC9D,IAAIgC,yBAAyB,GAAG/C,6BAA6B,CAACe,IAAI;IAClE;IACA,IAAI,CAACtQ,gBAAgB,CAACvM,SAAS,CAACQ,OAAO,CAAC,CAAC3C,QAAQ,EAAEL,OAAO,KAAK;MAC3D;MACA;MACA,IAAIA,OAAO,KAAK,IAAI,CAACuC,SAAS,IAAI,CAAClC,QAAQ,CAACuB,UAAU,IAAIuf,UAAU,EAAE;QAClE;MACJ;MACA,IAAIpf,oBAAoB,CAAC1B,QAAQ,CAACuB,UAAU,EAAEuc,wBAAwB,EAAElc,QAAQ,EAAEC,QAAQ,CAAC,EAAE;QACzF,CAACkf,uBAAuB,EAAEC,yBAAyB,CAAC,GAAGC,0BAA0B,CAACthB,OAAO,EAAEK,QAAQ,CAACuB,UAAU,EAAE,IAAI,CAACue,aAAa,CAAC9O,SAAS,EAAEpP,QAAQ,EAAEC,QAAQ,CAAC;QACjK,IAAIkf,uBAAuB,IAAIC,yBAAyB,EAAE;UACtDF,UAAU,GAAGnhB,OAAO;QACxB;MACJ;IACJ,CAAC,CAAC;IACF;IACA,IAAI,CAACohB,uBAAuB,IAAI,CAACC,yBAAyB,EAAE;MACxD,MAAM;QAAEtgB,KAAK;QAAEC;MAAO,CAAC,GAAG,IAAI,CAAC0J,cAAc,CAAC6W,eAAe,CAAC,CAAC;MAC/D,MAAMzf,OAAO,GAAG;QACZf,KAAK;QACLC,MAAM;QACNV,GAAG,EAAE,CAAC;QACNmB,KAAK,EAAEV,KAAK;QACZW,MAAM,EAAEV,MAAM;QACdR,IAAI,EAAE;MACV,CAAC;MACD4gB,uBAAuB,GAAGI,0BAA0B,CAAC1f,OAAO,EAAEI,QAAQ,CAAC;MACvEmf,yBAAyB,GAAGI,4BAA4B,CAAC3f,OAAO,EAAEG,QAAQ,CAAC;MAC3Ekf,UAAU,GAAGrd,MAAM;IACvB;IACA,IAAIqd,UAAU,KACTC,uBAAuB,KAAK,IAAI,CAAChC,wBAAwB,IACtDiC,yBAAyB,KAAK,IAAI,CAAC/B,0BAA0B,IAC7D6B,UAAU,KAAK,IAAI,CAACzB,WAAW,CAAC,EAAE;MACtC,IAAI,CAACN,wBAAwB,GAAGgC,uBAAuB;MACvD,IAAI,CAAC9B,0BAA0B,GAAG+B,yBAAyB;MAC3D,IAAI,CAAC3B,WAAW,GAAGyB,UAAU;MAC7B,IAAI,CAACC,uBAAuB,IAAIC,yBAAyB,KAAKF,UAAU,EAAE;QACtE,IAAI,CAAC1W,OAAO,CAACoF,iBAAiB,CAAC,IAAI,CAAC2P,oBAAoB,CAAC;MAC7D,CAAC,MACI;QACD,IAAI,CAACvN,cAAc,CAAC,CAAC;MACzB;IACJ;EACJ;EACA;EACAA,cAAcA,CAAA,EAAG;IACb,IAAI,CAACsN,iBAAiB,CAACrT,IAAI,CAAC,CAAC;EACjC;EACA;EACAmU,gBAAgBA,CAAA,EAAG;IACf,MAAMxH,MAAM,GAAG3a,aAAa,CAAC,IAAI,CAAC8B,OAAO,CAAC,CAACG,KAAK;IAChD,IAAI,CAACsL,aAAa,CAACS,IAAI,CAAC,CAAC;IACzB,IAAI,CAAC6S,WAAW,GAAG,IAAI;IACvB;IACA;IACA;IACA,IAAI,CAAC2C,kBAAkB,GAAG7I,MAAM,CAAC8I,gBAAgB,IAAI9I,MAAM,CAAC+I,cAAc,IAAI,EAAE;IAChF/I,MAAM,CAAC+I,cAAc,GAAG/I,MAAM,CAAC8I,gBAAgB,GAAG,MAAM;IACxD,IAAI,CAACxB,aAAa,CAAC9M,KAAK,CAAC,IAAI,CAAC2L,WAAW,CAAC;IAC1C,IAAI,CAACuB,qBAAqB,CAAC,CAAC;IAC5B,IAAI,CAACpB,2BAA2B,CAAC/O,WAAW,CAAC,CAAC;IAC9C,IAAI,CAACyR,qBAAqB,CAAC,CAAC;EAChC;EACA;EACAtB,qBAAqBA,CAAA,EAAG;IACpB,MAAMvgB,OAAO,GAAG9B,aAAa,CAAC,IAAI,CAAC8B,OAAO,CAAC;IAC3C,IAAI,CAAC+O,gBAAgB,CAACpM,KAAK,CAAC,IAAI,CAACoe,mBAAmB,CAAC;IACrD;IACA;IACA,IAAI,CAACE,QAAQ,GAAG,IAAI,CAAClS,gBAAgB,CAACvM,SAAS,CAACc,GAAG,CAACtD,OAAO,CAAC,CAAC4B,UAAU;EAC3E;EACA;EACA4e,MAAMA,CAAA,EAAG;IACL,IAAI,CAACzB,WAAW,GAAG,KAAK;IACxB,MAAMlG,MAAM,GAAG3a,aAAa,CAAC,IAAI,CAAC8B,OAAO,CAAC,CAACG,KAAK;IAChD0Y,MAAM,CAAC+I,cAAc,GAAG/I,MAAM,CAAC8I,gBAAgB,GAAG,IAAI,CAACD,kBAAkB;IACzE,IAAI,CAACzC,SAAS,CAACjc,OAAO,CAACuZ,OAAO,IAAIA,OAAO,CAACuF,cAAc,CAAC,IAAI,CAAC,CAAC;IAC/D,IAAI,CAAC3B,aAAa,CAACnP,KAAK,CAAC,CAAC;IAC1B,IAAI,CAACiB,cAAc,CAAC,CAAC;IACrB,IAAI,CAACkN,2BAA2B,CAAC/O,WAAW,CAAC,CAAC;IAC9C,IAAI,CAACrB,gBAAgB,CAACrM,KAAK,CAAC,CAAC;EACjC;EACA;AACJ;AACA;AACA;AACA;EACIsS,gBAAgBA,CAAC9T,CAAC,EAAEC,CAAC,EAAE;IACnB,OAAO,IAAI,CAAC8f,QAAQ,IAAI,IAAI,IAAItf,kBAAkB,CAAC,IAAI,CAACsf,QAAQ,EAAE/f,CAAC,EAAEC,CAAC,CAAC;EAC3E;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIqU,gCAAgCA,CAACP,IAAI,EAAE/T,CAAC,EAAEC,CAAC,EAAE;IACzC,OAAO,IAAI,CAAC8d,SAAS,CAACvY,IAAI,CAAC6V,OAAO,IAAIA,OAAO,CAACwF,WAAW,CAAC9M,IAAI,EAAE/T,CAAC,EAAEC,CAAC,CAAC,CAAC;EAC1E;EACA;AACJ;AACA;AACA;AACA;AACA;EACI4gB,WAAWA,CAAC9M,IAAI,EAAE/T,CAAC,EAAEC,CAAC,EAAE;IACpB,IAAI,CAAC,IAAI,CAAC8f,QAAQ,IACd,CAACtf,kBAAkB,CAAC,IAAI,CAACsf,QAAQ,EAAE/f,CAAC,EAAEC,CAAC,CAAC,IACxC,CAAC,IAAI,CAACud,cAAc,CAACzJ,IAAI,EAAE,IAAI,CAAC,EAAE;MAClC,OAAO,KAAK;IAChB;IACA,MAAM+M,gBAAgB,GAAG,IAAI,CAAC/jB,cAAc,CAAC,CAAC,CAAC+jB,gBAAgB,CAAC9gB,CAAC,EAAEC,CAAC,CAAC;IACrE;IACA;IACA,IAAI,CAAC6gB,gBAAgB,EAAE;MACnB,OAAO,KAAK;IAChB;IACA,MAAMC,aAAa,GAAG/jB,aAAa,CAAC,IAAI,CAAC8B,OAAO,CAAC;IACjD;IACA;IACA;IACA;IACA;IACA;IACA,OAAOgiB,gBAAgB,KAAKC,aAAa,IAAIA,aAAa,CAACpe,QAAQ,CAACme,gBAAgB,CAAC;EACzF;EACA;AACJ;AACA;AACA;EACIE,eAAeA,CAAC3F,OAAO,EAAElB,KAAK,EAAE;IAC5B,MAAM8G,cAAc,GAAG,IAAI,CAACjD,eAAe;IAC3C,IAAI,CAACiD,cAAc,CAACtiB,GAAG,CAAC0c,OAAO,CAAC,IAC5BlB,KAAK,CAACuF,KAAK,CAAC3L,IAAI,IAAI;MAChB;MACA;MACA;MACA;MACA,OAAO,IAAI,CAACyJ,cAAc,CAACzJ,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC+J,WAAW,CAAC9Y,OAAO,CAAC+O,IAAI,CAAC,GAAG,CAAC,CAAC;IACjF,CAAC,CAAC,EAAE;MACJkN,cAAc,CAAC3Z,GAAG,CAAC+T,OAAO,CAAC;MAC3B,IAAI,CAACgE,qBAAqB,CAAC,CAAC;MAC5B,IAAI,CAACsB,qBAAqB,CAAC,CAAC;MAC5B,IAAI,CAAChD,gBAAgB,CAAC3S,IAAI,CAAC;QACvBkW,SAAS,EAAE7F,OAAO;QAClB8F,QAAQ,EAAE,IAAI;QACdhH;MACJ,CAAC,CAAC;IACN;EACJ;EACA;AACJ;AACA;AACA;EACIyG,cAAcA,CAACvF,OAAO,EAAE;IACpB,IAAI,CAAC2C,eAAe,CAAC/N,MAAM,CAACoL,OAAO,CAAC;IACpC,IAAI,CAAC4C,2BAA2B,CAAC/O,WAAW,CAAC,CAAC;IAC9C,IAAI,CAAC0O,gBAAgB,CAAC5S,IAAI,CAAC;MAAEkW,SAAS,EAAE7F,OAAO;MAAE8F,QAAQ,EAAE;IAAK,CAAC,CAAC;EACtE;EACA;AACJ;AACA;AACA;EACIR,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAAC1C,2BAA2B,GAAG,IAAI,CAACxU,iBAAiB,CACpD0J,QAAQ,CAAC,IAAI,CAACpW,cAAc,CAAC,CAAC,CAAC,CAC/BqS,SAAS,CAAClN,KAAK,IAAI;MACpB,IAAI,IAAI,CAACiK,UAAU,CAAC,CAAC,EAAE;QACnB,MAAMiM,gBAAgB,GAAG,IAAI,CAACvK,gBAAgB,CAAC5L,YAAY,CAACC,KAAK,CAAC;QAClE,IAAIkW,gBAAgB,EAAE;UAClB,IAAI,CAAC6G,aAAa,CAAC3C,cAAc,CAAClE,gBAAgB,CAAChZ,GAAG,EAAEgZ,gBAAgB,CAAC9Y,IAAI,CAAC;QAClF;MACJ,CAAC,MACI,IAAI,IAAI,CAAC8M,WAAW,CAAC,CAAC,EAAE;QACzB,IAAI,CAACiT,qBAAqB,CAAC,CAAC;MAChC;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;EACItiB,cAAcA,CAAA,EAAG;IACb,IAAI,CAAC,IAAI,CAACsb,iBAAiB,EAAE;MACzB,MAAM9G,UAAU,GAAGxU,cAAc,CAACC,aAAa,CAAC,IAAI,CAAC8B,OAAO,CAAC,CAAC;MAC9D,IAAI,CAACuZ,iBAAiB,GAAI9G,UAAU,IAAI,IAAI,CAAClQ,SAAU;IAC3D;IACA,OAAO,IAAI,CAACgX,iBAAiB;EACjC;EACA;EACA+G,wBAAwBA,CAAA,EAAG;IACvB,MAAMI,YAAY,GAAG,IAAI,CAACP,aAAa,CAClC7C,sBAAsB,CAAC,CAAC,CACxBqD,MAAM,CAAC1L,IAAI,IAAIA,IAAI,CAAC5H,UAAU,CAAC,CAAC,CAAC;IACtC,IAAI,CAAC4R,SAAS,CAACjc,OAAO,CAACuZ,OAAO,IAAIA,OAAO,CAAC2F,eAAe,CAAC,IAAI,EAAExB,YAAY,CAAC,CAAC;EAClF;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,SAASc,0BAA0BA,CAAC5f,UAAU,EAAEM,QAAQ,EAAE;EACtD,MAAM;IAAE5B,GAAG;IAAEoB,MAAM;IAAEV;EAAO,CAAC,GAAGY,UAAU;EAC1C,MAAMQ,UAAU,GAAGpB,MAAM,GAAGod,0BAA0B;EACtD,IAAIlc,QAAQ,IAAI5B,GAAG,GAAG8B,UAAU,IAAIF,QAAQ,IAAI5B,GAAG,GAAG8B,UAAU,EAAE;IAC9D,OAAOic,2BAA2B,CAACuB,EAAE;EACzC,CAAC,MACI,IAAI1d,QAAQ,IAAIR,MAAM,GAAGU,UAAU,IAAIF,QAAQ,IAAIR,MAAM,GAAGU,UAAU,EAAE;IACzE,OAAOic,2BAA2B,CAACyB,IAAI;EAC3C;EACA,OAAOzB,2BAA2B,CAACgB,IAAI;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA,SAASoC,4BAA4BA,CAAC7f,UAAU,EAAEK,QAAQ,EAAE;EACxD,MAAM;IAAEzB,IAAI;IAAEiB,KAAK;IAAEV;EAAM,CAAC,GAAGa,UAAU;EACzC,MAAMO,UAAU,GAAGpB,KAAK,GAAGqd,0BAA0B;EACrD,IAAInc,QAAQ,IAAIzB,IAAI,GAAG2B,UAAU,IAAIF,QAAQ,IAAIzB,IAAI,GAAG2B,UAAU,EAAE;IAChE,OAAOmc,6BAA6B,CAACyB,IAAI;EAC7C,CAAC,MACI,IAAI9d,QAAQ,IAAIR,KAAK,GAAGU,UAAU,IAAIF,QAAQ,IAAIR,KAAK,GAAGU,UAAU,EAAE;IACvE,OAAOmc,6BAA6B,CAAC0B,KAAK;EAC9C;EACA,OAAO1B,6BAA6B,CAACe,IAAI;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASiC,0BAA0BA,CAACthB,OAAO,EAAE4B,UAAU,EAAEyP,SAAS,EAAEpP,QAAQ,EAAEC,QAAQ,EAAE;EACpF,MAAMogB,gBAAgB,GAAGd,0BAA0B,CAAC5f,UAAU,EAAEM,QAAQ,CAAC;EACzE,MAAMqgB,kBAAkB,GAAGd,4BAA4B,CAAC7f,UAAU,EAAEK,QAAQ,CAAC;EAC7E,IAAImf,uBAAuB,GAAG/C,2BAA2B,CAACgB,IAAI;EAC9D,IAAIgC,yBAAyB,GAAG/C,6BAA6B,CAACe,IAAI;EAClE;EACA;EACA;EACA;EACA,IAAIiD,gBAAgB,EAAE;IAClB,MAAMrf,SAAS,GAAGjD,OAAO,CAACiD,SAAS;IACnC,IAAIqf,gBAAgB,KAAKjE,2BAA2B,CAACuB,EAAE,EAAE;MACrD,IAAI3c,SAAS,GAAG,CAAC,EAAE;QACfme,uBAAuB,GAAG/C,2BAA2B,CAACuB,EAAE;MAC5D;IACJ,CAAC,MACI,IAAI5f,OAAO,CAACwiB,YAAY,GAAGvf,SAAS,GAAGjD,OAAO,CAACyiB,YAAY,EAAE;MAC9DrB,uBAAuB,GAAG/C,2BAA2B,CAACyB,IAAI;IAC9D;EACJ;EACA,IAAIyC,kBAAkB,EAAE;IACpB,MAAMrf,UAAU,GAAGlD,OAAO,CAACkD,UAAU;IACrC,IAAImO,SAAS,KAAK,KAAK,EAAE;MACrB,IAAIkR,kBAAkB,KAAKjE,6BAA6B,CAAC0B,KAAK,EAAE;QAC5D;QACA,IAAI9c,UAAU,GAAG,CAAC,EAAE;UAChBme,yBAAyB,GAAG/C,6BAA6B,CAAC0B,KAAK;QACnE;MACJ,CAAC,MACI,IAAIhgB,OAAO,CAAC0iB,WAAW,GAAGxf,UAAU,GAAGlD,OAAO,CAAC2iB,WAAW,EAAE;QAC7DtB,yBAAyB,GAAG/C,6BAA6B,CAACyB,IAAI;MAClE;IACJ,CAAC,MACI;MACD,IAAIwC,kBAAkB,KAAKjE,6BAA6B,CAACyB,IAAI,EAAE;QAC3D,IAAI7c,UAAU,GAAG,CAAC,EAAE;UAChBme,yBAAyB,GAAG/C,6BAA6B,CAACyB,IAAI;QAClE;MACJ,CAAC,MACI,IAAI/f,OAAO,CAAC0iB,WAAW,GAAGxf,UAAU,GAAGlD,OAAO,CAAC2iB,WAAW,EAAE;QAC7DtB,yBAAyB,GAAG/C,6BAA6B,CAAC0B,KAAK;MACnE;IACJ;EACJ;EACA,OAAO,CAACoB,uBAAuB,EAAEC,yBAAyB,CAAC;AAC/D;;AAEA;AACA,MAAMuB,2BAA2B,GAAG5kB,+BAA+B,CAAC;EAChE2L,OAAO,EAAE,KAAK;EACdG,OAAO,EAAE;AACb,CAAC,CAAC;AACF;AACA,MAAM+Y,UAAU,GAAG,IAAI1b,GAAG,CAAC,CAAC;AAC5B;AACA;AACA;AACA;AACA,MAAM2b,aAAa,CAAC;EAChB;IAAS,IAAI,CAACC,IAAI,YAAAC,sBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFH,aAAa;IAAA,CAAmD;EAAE;EAC5K;IAAS,IAAI,CAACI,IAAI,kBAD8E1mB,EAAE,CAAA2mB,iBAAA;MAAAhe,IAAA,EACJ2d,aAAa;MAAAM,SAAA;MAAAC,SAAA,gCAAmG,EAAE;MAAAC,UAAA;MAAAC,QAAA,GADhH/mB,EAAE,CAAAgnB,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAA1a,QAAA,WAAA2a,uBAAAC,EAAA,EAAAC,GAAA;MAAAhL,MAAA;MAAAiL,aAAA;MAAAC,eAAA;IAAA,EACsW;EAAE;AAC9c;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoGxnB,EAAE,CAAAynB,iBAAA,CAGXnB,aAAa,EAAc,CAAC;IAC3G3d,IAAI,EAAE1I,SAAS;IACfynB,IAAI,EAAE,CAAC;MAAEZ,UAAU,EAAE,IAAI;MAAEQ,aAAa,EAAEpnB,iBAAiB,CAACynB,IAAI;MAAEnb,QAAQ,EAAE,EAAE;MAAE+a,eAAe,EAAEpnB,uBAAuB,CAACynB,MAAM;MAAEC,IAAI,EAAE;QAAE,2BAA2B,EAAE;MAAG,CAAC;MAAExL,MAAM,EAAE,CAAC,2FAA2F;IAAE,CAAC;EACvR,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMyL,gBAAgB,CAAC;EACnBhiB,WAAWA,CAACmI,OAAO,EAAElI,SAAS,EAAE;IAC5B,IAAI,CAACkI,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC8Z,OAAO,GAAG3nB,MAAM,CAACC,cAAc,CAAC;IACrC,IAAI,CAAC2nB,oBAAoB,GAAG5nB,MAAM,CAACE,mBAAmB,CAAC;IACvD;IACA,IAAI,CAAC2nB,cAAc,GAAG,IAAItd,GAAG,CAAC,CAAC;IAC/B;IACA,IAAI,CAACud,cAAc,GAAG,IAAIvd,GAAG,CAAC,CAAC;IAC/B;IACA,IAAI,CAACwd,oBAAoB,GAAG,EAAE;IAC9B;IACA,IAAI,CAACC,gBAAgB,GAAG,IAAIniB,GAAG,CAAC,CAAC;IACjC;AACR;AACA;AACA;IACQ,IAAI,CAACoiB,kBAAkB,GAAI5P,IAAI,IAAKA,IAAI,CAAC5H,UAAU,CAAC,CAAC;IACrD;AACR;AACA;AACA;IACQ,IAAI,CAAC8G,WAAW,GAAG,IAAI5V,OAAO,CAAC,CAAC;IAChC;AACR;AACA;AACA;IACQ,IAAI,CAAC6V,SAAS,GAAG,IAAI7V,OAAO,CAAC,CAAC;IAC9B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACumB,MAAM,GAAG,IAAIvmB,OAAO,CAAC,CAAC;IAC3B;AACR;AACA;AACA;IACQ,IAAI,CAACwmB,4BAA4B,GAAI3hB,KAAK,IAAK;MAC3C,IAAI,IAAI,CAACuhB,oBAAoB,CAACjgB,MAAM,GAAG,CAAC,EAAE;QACtCtB,KAAK,CAACoK,cAAc,CAAC,CAAC;MAC1B;IACJ,CAAC;IACD;IACA,IAAI,CAACwX,4BAA4B,GAAI5hB,KAAK,IAAK;MAC3C,IAAI,IAAI,CAACuhB,oBAAoB,CAACjgB,MAAM,GAAG,CAAC,EAAE;QACtC;QACA;QACA;QACA,IAAI,IAAI,CAACigB,oBAAoB,CAACM,IAAI,CAAC,IAAI,CAACJ,kBAAkB,CAAC,EAAE;UACzDzhB,KAAK,CAACoK,cAAc,CAAC,CAAC;QAC1B;QACA,IAAI,CAAC2G,WAAW,CAACjI,IAAI,CAAC9I,KAAK,CAAC;MAChC;IACJ,CAAC;IACD,IAAI,CAACb,SAAS,GAAGA,SAAS;EAC9B;EACA;EACA2d,qBAAqBA,CAAC9K,IAAI,EAAE;IACxB,IAAI,CAAC,IAAI,CAACqP,cAAc,CAAC5kB,GAAG,CAACuV,IAAI,CAAC,EAAE;MAChC,IAAI,CAACqP,cAAc,CAACjc,GAAG,CAAC4M,IAAI,CAAC;IACjC;EACJ;EACA;EACApG,gBAAgBA,CAACmM,IAAI,EAAE;IACnB,IAAI,CAACuJ,cAAc,CAAClc,GAAG,CAAC2S,IAAI,CAAC;IAC7B;IACA;IACA;IACA,IAAI,IAAI,CAACuJ,cAAc,CAAC1D,IAAI,KAAK,CAAC,EAAE;MAChC,IAAI,CAACvW,OAAO,CAACoF,iBAAiB,CAAC,MAAM;QACjC;QACA;QACA,IAAI,CAACtN,SAAS,CAACmG,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACsc,4BAA4B,EAAEpC,2BAA2B,CAAC;MAChH,CAAC,CAAC;IACN;EACJ;EACA;EACAxC,mBAAmBA,CAAChL,IAAI,EAAE;IACtB,IAAI,CAACqP,cAAc,CAACtT,MAAM,CAACiE,IAAI,CAAC;EACpC;EACA;EACAvE,cAAcA,CAACsK,IAAI,EAAE;IACjB,IAAI,CAACuJ,cAAc,CAACvT,MAAM,CAACgK,IAAI,CAAC;IAChC,IAAI,CAACrJ,YAAY,CAACqJ,IAAI,CAAC;IACvB,IAAI,IAAI,CAACuJ,cAAc,CAAC1D,IAAI,KAAK,CAAC,EAAE;MAChC,IAAI,CAACze,SAAS,CAACqG,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACoc,4BAA4B,EAAEpC,2BAA2B,CAAC;IACnH;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIhO,aAAaA,CAACuG,IAAI,EAAE/X,KAAK,EAAE;IACvB;IACA,IAAI,IAAI,CAACuhB,oBAAoB,CAACze,OAAO,CAACiV,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;MAC9C;IACJ;IACA,IAAI,CAAC+J,WAAW,CAAC,CAAC;IAClB,IAAI,CAACP,oBAAoB,CAAC3H,IAAI,CAAC7B,IAAI,CAAC;IACpC,IAAI,IAAI,CAACwJ,oBAAoB,CAACjgB,MAAM,KAAK,CAAC,EAAE;MACxC,MAAM6N,YAAY,GAAGnP,KAAK,CAAC+B,IAAI,CAACggB,UAAU,CAAC,OAAO,CAAC;MACnD;MACA;MACA;MACA,IAAI,CAACP,gBAAgB,CAChB/hB,GAAG,CAAC0P,YAAY,GAAG,UAAU,GAAG,SAAS,EAAE;QAC5C5J,OAAO,EAAGyc,CAAC,IAAK,IAAI,CAAChR,SAAS,CAAClI,IAAI,CAACkZ,CAAC,CAAC;QACtCC,OAAO,EAAE;MACb,CAAC,CAAC,CACGxiB,GAAG,CAAC,QAAQ,EAAE;QACf8F,OAAO,EAAGyc,CAAC,IAAK,IAAI,CAACN,MAAM,CAAC5Y,IAAI,CAACkZ,CAAC,CAAC;QACnC;QACA;QACAC,OAAO,EAAE;MACb,CAAC;MACG;MACA;MACA;MACA;MAAA,CACCxiB,GAAG,CAAC,aAAa,EAAE;QACpB8F,OAAO,EAAE,IAAI,CAACoc,4BAA4B;QAC1CM,OAAO,EAAEzC;MACb,CAAC,CAAC;MACF;MACA;MACA,IAAI,CAACrQ,YAAY,EAAE;QACf,IAAI,CAACqS,gBAAgB,CAAC/hB,GAAG,CAAC,WAAW,EAAE;UACnC8F,OAAO,EAAGyc,CAAC,IAAK,IAAI,CAACjR,WAAW,CAACjI,IAAI,CAACkZ,CAAC,CAAC;UACxCC,OAAO,EAAEzC;QACb,CAAC,CAAC;MACN;MACA,IAAI,CAACnY,OAAO,CAACoF,iBAAiB,CAAC,MAAM;QACjC,IAAI,CAAC+U,gBAAgB,CAAC5hB,OAAO,CAAC,CAACsiB,MAAM,EAAElgB,IAAI,KAAK;UAC5C,IAAI,CAAC7C,SAAS,CAACmG,gBAAgB,CAACtD,IAAI,EAAEkgB,MAAM,CAAC3c,OAAO,EAAE2c,MAAM,CAACD,OAAO,CAAC;QACzE,CAAC,CAAC;MACN,CAAC,CAAC;IACN;EACJ;EACA;EACAvT,YAAYA,CAACqJ,IAAI,EAAE;IACf,MAAMqB,KAAK,GAAG,IAAI,CAACmI,oBAAoB,CAACze,OAAO,CAACiV,IAAI,CAAC;IACrD,IAAIqB,KAAK,GAAG,CAAC,CAAC,EAAE;MACZ,IAAI,CAACmI,oBAAoB,CAAC/J,MAAM,CAAC4B,KAAK,EAAE,CAAC,CAAC;MAC1C,IAAI,IAAI,CAACmI,oBAAoB,CAACjgB,MAAM,KAAK,CAAC,EAAE;QACxC,IAAI,CAAC6gB,qBAAqB,CAAC,CAAC;MAChC;IACJ;EACJ;EACA;EACAlY,UAAUA,CAAC8N,IAAI,EAAE;IACb,OAAO,IAAI,CAACwJ,oBAAoB,CAACze,OAAO,CAACiV,IAAI,CAAC,GAAG,CAAC,CAAC;EACvD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI9G,QAAQA,CAAC5B,UAAU,EAAE;IACjB,MAAM+S,OAAO,GAAG,CAAC,IAAI,CAACV,MAAM,CAAC;IAC7B,IAAIrS,UAAU,IAAIA,UAAU,KAAK,IAAI,CAAClQ,SAAS,EAAE;MAC7C;MACA;MACA;MACAijB,OAAO,CAACxI,IAAI,CAAC,IAAIre,UAAU,CAAE8mB,QAAQ,IAAK;QACtC,OAAO,IAAI,CAAChb,OAAO,CAACoF,iBAAiB,CAAC,MAAM;UACxC,MAAM6V,YAAY,GAAG,IAAI;UACzB,MAAM3gB,QAAQ,GAAI3B,KAAK,IAAK;YACxB,IAAI,IAAI,CAACuhB,oBAAoB,CAACjgB,MAAM,EAAE;cAClC+gB,QAAQ,CAACvZ,IAAI,CAAC9I,KAAK,CAAC;YACxB;UACJ,CAAC;UACDqP,UAAU,CAAC/J,gBAAgB,CAAC,QAAQ,EAAE3D,QAAQ,EAAE2gB,YAAY,CAAC;UAC7D,OAAO,MAAM;YACTjT,UAAU,CAAC7J,mBAAmB,CAAC,QAAQ,EAAE7D,QAAQ,EAAE2gB,YAAY,CAAC;UACpE,CAAC;QACL,CAAC,CAAC;MACN,CAAC,CAAC,CAAC;IACP;IACA,OAAO9mB,KAAK,CAAC,GAAG4mB,OAAO,CAAC;EAC5B;EACAG,WAAWA,CAAA,EAAG;IACV,IAAI,CAACjB,cAAc,CAAC1hB,OAAO,CAAC4iB,QAAQ,IAAI,IAAI,CAAC/U,cAAc,CAAC+U,QAAQ,CAAC,CAAC;IACtE,IAAI,CAACnB,cAAc,CAACzhB,OAAO,CAAC4iB,QAAQ,IAAI,IAAI,CAACxF,mBAAmB,CAACwF,QAAQ,CAAC,CAAC;IAC3E,IAAI,CAACL,qBAAqB,CAAC,CAAC;IAC5B,IAAI,CAACpR,WAAW,CAACpD,QAAQ,CAAC,CAAC;IAC3B,IAAI,CAACqD,SAAS,CAACrD,QAAQ,CAAC,CAAC;EAC7B;EACA;EACAwU,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAACX,gBAAgB,CAAC5hB,OAAO,CAAC,CAACsiB,MAAM,EAAElgB,IAAI,KAAK;MAC5C,IAAI,CAAC7C,SAAS,CAACqG,mBAAmB,CAACxD,IAAI,EAAEkgB,MAAM,CAAC3c,OAAO,EAAE2c,MAAM,CAACD,OAAO,CAAC;IAC5E,CAAC,CAAC;IACF,IAAI,CAACT,gBAAgB,CAACliB,KAAK,CAAC,CAAC;EACjC;EACA;EACA;EACAwiB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACrC,UAAU,CAAChjB,GAAG,CAAC,IAAI,CAAC0kB,OAAO,CAAC,EAAE;MAC/B1B,UAAU,CAACra,GAAG,CAAC,IAAI,CAAC+b,OAAO,CAAC;MAC5B,MAAMsB,YAAY,GAAG9oB,eAAe,CAAC+lB,aAAa,EAAE;QAChDgD,mBAAmB,EAAE,IAAI,CAACtB;MAC9B,CAAC,CAAC;MACF,IAAI,CAACD,OAAO,CAACwB,SAAS,CAAC,MAAM;QACzBlD,UAAU,CAAC1R,MAAM,CAAC,IAAI,CAACoT,OAAO,CAAC;QAC/B,IAAI1B,UAAU,CAAC7B,IAAI,KAAK,CAAC,EAAE;UACvB6E,YAAY,CAAC5d,OAAO,CAAC,CAAC;QAC1B;MACJ,CAAC,CAAC;IACN;EACJ;EACA;IAAS,IAAI,CAAC8a,IAAI,YAAAiD,yBAAA/C,CAAA;MAAA,YAAAA,CAAA,IAAwFqB,gBAAgB,EArO1B9nB,EAAE,CAAAypB,QAAA,CAqO0CzpB,EAAE,CAAC0pB,MAAM,GArOrD1pB,EAAE,CAAAypB,QAAA,CAqOgEroB,QAAQ;IAAA,CAA6C;EAAE;EACzN;IAAS,IAAI,CAACuoB,KAAK,kBAtO6E3pB,EAAE,CAAA4pB,kBAAA;MAAAC,KAAA,EAsOY/B,gBAAgB;MAAAgC,OAAA,EAAhBhC,gBAAgB,CAAAvB,IAAA;MAAAwD,UAAA,EAAc;IAAM,EAAG;EAAE;AAC3J;AACA;EAAA,QAAAvC,SAAA,oBAAAA,SAAA,KAxOoGxnB,EAAE,CAAAynB,iBAAA,CAwOXK,gBAAgB,EAAc,CAAC;IAC9Gnf,IAAI,EAAEnI,UAAU;IAChBknB,IAAI,EAAE,CAAC;MAAEqC,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEphB,IAAI,EAAE3I,EAAE,CAAC0pB;EAAO,CAAC,EAAE;IAAE/gB,IAAI,EAAE2K,SAAS;IAAE0W,UAAU,EAAE,CAAC;MACpErhB,IAAI,EAAElI,MAAM;MACZinB,IAAI,EAAE,CAACtmB,QAAQ;IACnB,CAAC;EAAE,CAAC,CAAC;AAAA;;AAErB;AACA,MAAM6oB,cAAc,GAAG;EACnB5Z,kBAAkB,EAAE,CAAC;EACrB8L,+BAA+B,EAAE;AACrC,CAAC;AACD;AACA;AACA;AACA,MAAM+N,QAAQ,CAAC;EACXpkB,WAAWA,CAACC,SAAS,EAAEkI,OAAO,EAAEC,cAAc,EAAEC,iBAAiB,EAAE;IAC/D,IAAI,CAACpI,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACkI,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;EAC9C;EACA;AACJ;AACA;AACA;AACA;EACIgc,UAAUA,CAAC3mB,OAAO,EAAEslB,MAAM,GAAGmB,cAAc,EAAE;IACzC,OAAO,IAAIxc,OAAO,CAACjK,OAAO,EAAEslB,MAAM,EAAE,IAAI,CAAC/iB,SAAS,EAAE,IAAI,CAACkI,OAAO,EAAE,IAAI,CAACC,cAAc,EAAE,IAAI,CAACC,iBAAiB,CAAC;EAClH;EACA;AACJ;AACA;AACA;EACIic,cAAcA,CAAC5mB,OAAO,EAAE;IACpB,OAAO,IAAIue,WAAW,CAACve,OAAO,EAAE,IAAI,CAAC2K,iBAAiB,EAAE,IAAI,CAACpI,SAAS,EAAE,IAAI,CAACkI,OAAO,EAAE,IAAI,CAACC,cAAc,CAAC;EAC9G;EACA;IAAS,IAAI,CAACqY,IAAI,YAAA8D,iBAAA5D,CAAA;MAAA,YAAAA,CAAA,IAAwFyD,QAAQ,EA9QlBlqB,EAAE,CAAAypB,QAAA,CA8QkCroB,QAAQ,GA9Q5CpB,EAAE,CAAAypB,QAAA,CA8QuDzpB,EAAE,CAAC0pB,MAAM,GA9QlE1pB,EAAE,CAAAypB,QAAA,CA8Q6EpoB,EAAE,CAACipB,aAAa,GA9Q/FtqB,EAAE,CAAAypB,QAAA,CA8Q0G3B,gBAAgB;IAAA,CAA6C;EAAE;EAC3Q;IAAS,IAAI,CAAC6B,KAAK,kBA/Q6E3pB,EAAE,CAAA4pB,kBAAA;MAAAC,KAAA,EA+QYK,QAAQ;MAAAJ,OAAA,EAARI,QAAQ,CAAA3D,IAAA;MAAAwD,UAAA,EAAc;IAAM,EAAG;EAAE;AACnJ;AACA;EAAA,QAAAvC,SAAA,oBAAAA,SAAA,KAjRoGxnB,EAAE,CAAAynB,iBAAA,CAiRXyC,QAAQ,EAAc,CAAC;IACtGvhB,IAAI,EAAEnI,UAAU;IAChBknB,IAAI,EAAE,CAAC;MAAEqC,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEphB,IAAI,EAAE2K,SAAS;IAAE0W,UAAU,EAAE,CAAC;MAC/CrhB,IAAI,EAAElI,MAAM;MACZinB,IAAI,EAAE,CAACtmB,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAEuH,IAAI,EAAE3I,EAAE,CAAC0pB;EAAO,CAAC,EAAE;IAAE/gB,IAAI,EAAEtH,EAAE,CAACipB;EAAc,CAAC,EAAE;IAAE3hB,IAAI,EAAEmf;EAAiB,CAAC,CAAC;AAAA;;AAElG;AACA;AACA;AACA;AACA;AACA;AACA,MAAMyC,eAAe,GAAG,IAAI7pB,cAAc,CAAC,iBAAiB,CAAC;;AAE7D;AACA;AACA;AACA;AACA;AACA,SAAS8pB,iBAAiBA,CAACpjB,IAAI,EAAEwB,IAAI,EAAE;EACnC,IAAIxB,IAAI,CAAC+B,QAAQ,KAAK,CAAC,EAAE;IACrB,MAAMshB,KAAK,CAAE,GAAE7hB,IAAK,wCAAuC,GAAI,0BAAyBxB,IAAI,CAACU,QAAS,IAAG,CAAC;EAC9G;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAM4iB,eAAe,GAAG,IAAIhqB,cAAc,CAAC,eAAe,CAAC;AAC3D;AACA,MAAMiqB,aAAa,CAAC;EAChB;EACA,IAAIjd,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAACvK,KAAK,EAAE;IAChB,IAAI,CAACwK,SAAS,GAAGxK,KAAK;IACtB,IAAI,CAACynB,aAAa,CAAClb,IAAI,CAAC,IAAI,CAAC;EACjC;EACA5J,WAAWA,CAACtC,OAAO,EAAEqnB,WAAW,EAAE;IAC9B,IAAI,CAACrnB,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACqnB,WAAW,GAAGA,WAAW;IAC9B;IACA,IAAI,CAACD,aAAa,GAAG,IAAI7oB,OAAO,CAAC,CAAC;IAClC,IAAI,CAAC4L,SAAS,GAAG,KAAK;IACtB,IAAI,OAAO6Z,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/CgD,iBAAiB,CAAChnB,OAAO,CAACiiB,aAAa,EAAE,eAAe,CAAC;IAC7D;IACAoF,WAAW,EAAEC,UAAU,CAAC,IAAI,CAAC;EACjC;EACA3B,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC0B,WAAW,EAAEE,aAAa,CAAC,IAAI,CAAC;IACrC,IAAI,CAACH,aAAa,CAACrW,QAAQ,CAAC,CAAC;EACjC;EACA;IAAS,IAAI,CAACgS,IAAI,YAAAyE,sBAAAvE,CAAA;MAAA,YAAAA,CAAA,IAAwFkE,aAAa,EA3UvB3qB,EAAE,CAAAirB,iBAAA,CA2UuCjrB,EAAE,CAACkrB,UAAU,GA3UtDlrB,EAAE,CAAAirB,iBAAA,CA2UiEV,eAAe;IAAA,CAA4E;EAAE;EAChQ;IAAS,IAAI,CAACY,IAAI,kBA5U8EnrB,EAAE,CAAAorB,iBAAA;MAAAziB,IAAA,EA4UJgiB,aAAa;MAAA/D,SAAA;MAAAC,SAAA;MAAAwE,MAAA;QAAA3d,QAAA,GA5UX1N,EAAE,CAAAsrB,YAAA,CAAAC,0BAAA,uCA4UsH5qB,gBAAgB;MAAA;MAAAmmB,UAAA;MAAAC,QAAA,GA5UxI/mB,EAAE,CAAAwrB,kBAAA,CA4UmM,CAAC;QAAEC,OAAO,EAAEf,eAAe;QAAEgB,WAAW,EAAEf;MAAc,CAAC,CAAC,GA5U/P3qB,EAAE,CAAA2rB,wBAAA;IAAA,EA4U8Q;EAAE;AACtX;AACA;EAAA,QAAAnE,SAAA,oBAAAA,SAAA,KA9UoGxnB,EAAE,CAAAynB,iBAAA,CA8UXkD,aAAa,EAAc,CAAC;IAC3GhiB,IAAI,EAAE/H,SAAS;IACf8mB,IAAI,EAAE,CAAC;MACCpf,QAAQ,EAAE,iBAAiB;MAC3Bwe,UAAU,EAAE,IAAI;MAChBe,IAAI,EAAE;QACF,OAAO,EAAE;MACb,CAAC;MACD+D,SAAS,EAAE,CAAC;QAAEH,OAAO,EAAEf,eAAe;QAAEgB,WAAW,EAAEf;MAAc,CAAC;IACxE,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEhiB,IAAI,EAAE3I,EAAE,CAACkrB;EAAW,CAAC,EAAE;IAAEviB,IAAI,EAAE2K,SAAS;IAAE0W,UAAU,EAAE,CAAC;MACxErhB,IAAI,EAAElI,MAAM;MACZinB,IAAI,EAAE,CAAC6C,eAAe;IAC1B,CAAC,EAAE;MACC5hB,IAAI,EAAE9H;IACV,CAAC,EAAE;MACC8H,IAAI,EAAE7H;IACV,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAE4M,QAAQ,EAAE,CAAC;MACpC/E,IAAI,EAAE5H,KAAK;MACX2mB,IAAI,EAAE,CAAC;QAAEmE,KAAK,EAAE,uBAAuB;QAAE3nB,SAAS,EAAEvD;MAAiB,CAAC;IAC1E,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAMmrB,eAAe,GAAG,IAAIprB,cAAc,CAAC,iBAAiB,CAAC;AAE7D,MAAMqrB,eAAe,GAAG,UAAU;AAClC;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,GAAG,IAAItrB,cAAc,CAAC,aAAa,CAAC;AACvD;AACA,MAAMurB,OAAO,CAAC;EACV;IAAS,IAAI,CAAC/D,cAAc,GAAG,EAAE;EAAE;EACnC;EACA,IAAIxa,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS,IAAK,IAAI,CAACuI,aAAa,IAAI,IAAI,CAACA,aAAa,CAACxI,QAAS;EAChF;EACA,IAAIA,QAAQA,CAACvK,KAAK,EAAE;IAChB,IAAI,CAACwK,SAAS,GAAGxK,KAAK;IACtB,IAAI,CAAC+oB,QAAQ,CAACxe,QAAQ,GAAG,IAAI,CAACC,SAAS;EAC3C;EACA7H,WAAWA,CAAA,CACX;EACAtC,OAAO,EACP;EACA0S,aAAa;EACb;AACJ;AACA;AACA;EACInQ,SAAS,EAAEkI,OAAO,EAAEke,iBAAiB,EAAErD,MAAM,EAAEsD,IAAI,EAAEC,QAAQ,EAAEC,kBAAkB,EAAEC,WAAW,EAAE1B,WAAW,EAAE;IACzG,IAAI,CAACrnB,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC0S,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACjI,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACke,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACE,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAAC1B,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAAC2B,UAAU,GAAG,IAAIzqB,OAAO,CAAC,CAAC;IAC/B,IAAI,CAAC+L,QAAQ,GAAG,IAAIzL,eAAe,CAAC,EAAE,CAAC;IACvC;IACA,IAAI,CAAC6M,OAAO,GAAG,IAAIlO,YAAY,CAAC,CAAC;IACjC;IACA,IAAI,CAACmO,QAAQ,GAAG,IAAInO,YAAY,CAAC,CAAC;IAClC;IACA,IAAI,CAACoO,KAAK,GAAG,IAAIpO,YAAY,CAAC,CAAC;IAC/B;IACA,IAAI,CAACqO,OAAO,GAAG,IAAIrO,YAAY,CAAC,CAAC;IACjC;IACA,IAAI,CAACsO,MAAM,GAAG,IAAItO,YAAY,CAAC,CAAC;IAChC;IACA,IAAI,CAACuO,OAAO,GAAG,IAAIvO,YAAY,CAAC,CAAC;IACjC;AACR;AACA;AACA;IACQ,IAAI,CAACwO,KAAK,GAAG,IAAIrN,UAAU,CAAE8mB,QAAQ,IAAK;MACtC,MAAMwD,YAAY,GAAG,IAAI,CAACP,QAAQ,CAAC1c,KAAK,CACnCyT,IAAI,CAAC1gB,GAAG,CAACmqB,UAAU,KAAK;QACzB3pB,MAAM,EAAE,IAAI;QACZgN,eAAe,EAAE2c,UAAU,CAAC3c,eAAe;QAC3CnJ,KAAK,EAAE8lB,UAAU,CAAC9lB,KAAK;QACvBoL,KAAK,EAAE0a,UAAU,CAAC1a,KAAK;QACvBF,QAAQ,EAAE4a,UAAU,CAAC5a;MACzB,CAAC,CAAC,CAAC,CAAC,CACCgC,SAAS,CAACmV,QAAQ,CAAC;MACxB,OAAO,MAAM;QACTwD,YAAY,CAAC7Y,WAAW,CAAC,CAAC;MAC9B,CAAC;IACL,CAAC,CAAC;IACF,IAAI,CAACsY,QAAQ,GAAGG,QAAQ,CAAClC,UAAU,CAAC3mB,OAAO,EAAE;MACzC6M,kBAAkB,EAAEyY,MAAM,IAAIA,MAAM,CAACzY,kBAAkB,IAAI,IAAI,GAAGyY,MAAM,CAACzY,kBAAkB,GAAG,CAAC;MAC/F8L,+BAA+B,EAAE2M,MAAM,IAAIA,MAAM,CAAC3M,+BAA+B,IAAI,IAAI,GACnF2M,MAAM,CAAC3M,+BAA+B,GACtC,CAAC;MACP1F,MAAM,EAAEqS,MAAM,EAAErS;IACpB,CAAC,CAAC;IACF,IAAI,CAACyV,QAAQ,CAACS,IAAI,GAAG,IAAI;IACzB;IACA;IACA;IACAV,OAAO,CAAC/D,cAAc,CAAC1H,IAAI,CAAC,IAAI,CAAC;IACjC,IAAIsI,MAAM,EAAE;MACR,IAAI,CAAC8D,eAAe,CAAC9D,MAAM,CAAC;IAChC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI5S,aAAa,EAAE;MACf,IAAI,CAACgW,QAAQ,CAACpX,kBAAkB,CAACoB,aAAa,CAAC2W,YAAY,CAAC;MAC5D3W,aAAa,CAAC4W,OAAO,CAAC,IAAI,CAAC;IAC/B;IACA,IAAI,CAACC,WAAW,CAAC,IAAI,CAACb,QAAQ,CAAC;IAC/B,IAAI,CAACc,aAAa,CAAC,IAAI,CAACd,QAAQ,CAAC;EACrC;EACA;AACJ;AACA;AACA;EACIzZ,qBAAqBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACyZ,QAAQ,CAACzZ,qBAAqB,CAAC,CAAC;EAChD;EACA;EACAE,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACuZ,QAAQ,CAACvZ,cAAc,CAAC,CAAC;EACzC;EACA;EACA6B,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC0X,QAAQ,CAAC1X,KAAK,CAAC,CAAC;EACzB;EACA;AACJ;AACA;EACIO,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACmX,QAAQ,CAACnX,mBAAmB,CAAC,CAAC;EAC9C;EACA;AACJ;AACA;AACA;EACIC,mBAAmBA,CAAC7R,KAAK,EAAE;IACvB,IAAI,CAAC+oB,QAAQ,CAAClX,mBAAmB,CAAC7R,KAAK,CAAC;EAC5C;EACA8pB,eAAeA,CAAA,EAAG;IACd;IACA;IACA,IAAI,CAAChf,OAAO,CAACoF,iBAAiB,CAAC,MAAM;MACjC;MACA;MACA;MACA;MACA,IAAI,CAACpF,OAAO,CAACif,QAAQ,CAACjK,IAAI,CAACzgB,IAAI,CAAC,CAAC,CAAC,EAAEF,SAAS,CAAC,IAAI,CAACkqB,UAAU,CAAC,CAAC,CAAC1Y,SAAS,CAAC,MAAM;QAC5E,IAAI,CAACqZ,kBAAkB,CAAC,CAAC;QACzB,IAAI,CAACC,qBAAqB,CAAC,CAAC;QAC5B,IAAI,IAAI,CAACC,gBAAgB,EAAE;UACvB,IAAI,CAACnB,QAAQ,CAAClX,mBAAmB,CAAC,IAAI,CAACqY,gBAAgB,CAAC;QAC5D;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACAC,WAAWA,CAACC,OAAO,EAAE;IACjB,MAAMC,kBAAkB,GAAGD,OAAO,CAAC,qBAAqB,CAAC;IACzD,MAAME,cAAc,GAAGF,OAAO,CAAC,kBAAkB,CAAC;IAClD;IACA;IACA,IAAIC,kBAAkB,IAAI,CAACA,kBAAkB,CAACE,WAAW,EAAE;MACvD,IAAI,CAACP,kBAAkB,CAAC,CAAC;IAC7B;IACA;IACA,IAAIM,cAAc,IAAI,CAACA,cAAc,CAACC,WAAW,IAAI,IAAI,CAACL,gBAAgB,EAAE;MACxE,IAAI,CAACnB,QAAQ,CAAClX,mBAAmB,CAAC,IAAI,CAACqY,gBAAgB,CAAC;IAC5D;EACJ;EACAlE,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACjT,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAACyX,UAAU,CAAC,IAAI,CAAC;IACvC;IACA,MAAM3N,KAAK,GAAGiM,OAAO,CAAC/D,cAAc,CAACxe,OAAO,CAAC,IAAI,CAAC;IAClD,IAAIsW,KAAK,GAAG,CAAC,CAAC,EAAE;MACZiM,OAAO,CAAC/D,cAAc,CAAC9J,MAAM,CAAC4B,KAAK,EAAE,CAAC,CAAC;IAC3C;IACA;IACA,IAAI,CAAC/R,OAAO,CAACoF,iBAAiB,CAAC,MAAM;MACjC,IAAI,CAACvF,QAAQ,CAACyG,QAAQ,CAAC,CAAC;MACxB,IAAI,CAACiY,UAAU,CAAC9c,IAAI,CAAC,CAAC;MACtB,IAAI,CAAC8c,UAAU,CAACjY,QAAQ,CAAC,CAAC;MAC1B,IAAI,CAAC2X,QAAQ,CAACjY,OAAO,CAAC,CAAC;IAC3B,CAAC,CAAC;EACN;EACA6W,UAAUA,CAAC/c,MAAM,EAAE;IACf,MAAM+E,OAAO,GAAG,IAAI,CAAChF,QAAQ,CAAC8f,QAAQ,CAAC,CAAC;IACxC9a,OAAO,CAAC0N,IAAI,CAACzS,MAAM,CAAC;IACpB,IAAI,CAACD,QAAQ,CAAC4B,IAAI,CAACoD,OAAO,CAAC;EAC/B;EACAiY,aAAaA,CAAChd,MAAM,EAAE;IAClB,MAAM+E,OAAO,GAAG,IAAI,CAAChF,QAAQ,CAAC8f,QAAQ,CAAC,CAAC;IACxC,MAAM5N,KAAK,GAAGlN,OAAO,CAACpJ,OAAO,CAACqE,MAAM,CAAC;IACrC,IAAIiS,KAAK,GAAG,CAAC,CAAC,EAAE;MACZlN,OAAO,CAACsL,MAAM,CAAC4B,KAAK,EAAE,CAAC,CAAC;MACxB,IAAI,CAAClS,QAAQ,CAAC4B,IAAI,CAACoD,OAAO,CAAC;IAC/B;EACJ;EACA+a,mBAAmBA,CAACphB,OAAO,EAAE;IACzB,IAAI,CAACzB,gBAAgB,GAAGyB,OAAO;EACnC;EACAqhB,qBAAqBA,CAACrhB,OAAO,EAAE;IAC3B,IAAIA,OAAO,KAAK,IAAI,CAACzB,gBAAgB,EAAE;MACnC,IAAI,CAACA,gBAAgB,GAAG,IAAI;IAChC;EACJ;EACA+iB,uBAAuBA,CAAC3X,WAAW,EAAE;IACjC,IAAI,CAAClD,oBAAoB,GAAGkD,WAAW;EAC3C;EACA4X,yBAAyBA,CAAC5X,WAAW,EAAE;IACnC,IAAIA,WAAW,KAAK,IAAI,CAAClD,oBAAoB,EAAE;MAC3C,IAAI,CAACA,oBAAoB,GAAG,IAAI;IACpC;EACJ;EACA;EACAia,kBAAkBA,CAAA,EAAG;IACjB,MAAM3pB,OAAO,GAAG,IAAI,CAACA,OAAO,CAACiiB,aAAa;IAC1C,IAAItS,WAAW,GAAG3P,OAAO;IACzB,IAAI,IAAI,CAACyqB,mBAAmB,EAAE;MAC1B9a,WAAW,GACP3P,OAAO,CAAC0qB,OAAO,KAAK5a,SAAS,GACvB9P,OAAO,CAAC0qB,OAAO,CAAC,IAAI,CAACD,mBAAmB,CAAC;MACzC;MACEzqB,OAAO,CAAC+c,aAAa,EAAE2N,OAAO,CAAC,IAAI,CAACD,mBAAmB,CAAC;IACxE;IACA,IAAI9a,WAAW,KAAK,OAAOqU,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAChEgD,iBAAiB,CAACrX,WAAW,EAAE,SAAS,CAAC;IAC7C;IACA,IAAI,CAAC+Y,QAAQ,CAAC9Z,eAAe,CAACe,WAAW,IAAI3P,OAAO,CAAC;EACzD;EACA;EACA2qB,mBAAmBA,CAAA,EAAG;IAClB,MAAMC,QAAQ,GAAG,IAAI,CAACza,eAAe;IACrC,IAAI,CAACya,QAAQ,EAAE;MACX,OAAO,IAAI;IACf;IACA,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;MAC9B,OAAO,IAAI,CAAC5qB,OAAO,CAACiiB,aAAa,CAACyI,OAAO,CAACE,QAAQ,CAAC;IACvD;IACA,OAAO1sB,aAAa,CAAC0sB,QAAQ,CAAC;EAClC;EACA;EACArB,WAAWA,CAACsB,GAAG,EAAE;IACbA,GAAG,CAACpf,aAAa,CAAC6E,SAAS,CAAC,MAAM;MAC9B,IAAI,CAACua,GAAG,CAACxd,UAAU,CAAC,CAAC,EAAE;QACnB,MAAMyd,GAAG,GAAG,IAAI,CAAClC,IAAI;QACrB,MAAMpd,cAAc,GAAG,IAAI,CAACA,cAAc;QAC1C,MAAMoH,WAAW,GAAG,IAAI,CAAClD,oBAAoB,GACvC;UACE1G,QAAQ,EAAE,IAAI,CAAC0G,oBAAoB,CAACqb,WAAW;UAC/C1lB,OAAO,EAAE,IAAI,CAACqK,oBAAoB,CAACyZ,IAAI;UACvC/f,aAAa,EAAE,IAAI,CAACuf;QACxB,CAAC,GACC,IAAI;QACV,MAAM1f,OAAO,GAAG,IAAI,CAACzB,gBAAgB,GAC/B;UACEwB,QAAQ,EAAE,IAAI,CAACxB,gBAAgB,CAACujB,WAAW;UAC3C1lB,OAAO,EAAE,IAAI,CAACmC,gBAAgB,CAAC2hB,IAAI;UACnChgB,SAAS,EAAE,IAAI,CAAC3B,gBAAgB,CAAC2B,SAAS;UAC1CC,aAAa,EAAE,IAAI,CAACuf;QACxB,CAAC,GACC,IAAI;QACVkC,GAAG,CAAC3gB,QAAQ,GAAG,IAAI,CAACA,QAAQ;QAC5B2gB,GAAG,CAAClT,QAAQ,GAAG,IAAI,CAACA,QAAQ;QAC5BkT,GAAG,CAACrf,cAAc,GACd,OAAOA,cAAc,KAAK,QAAQ,IAAIA,cAAc,GAC9CA,cAAc,GACdrN,oBAAoB,CAACqN,cAAc,CAAC;QAC9Cqf,GAAG,CAAC3c,iBAAiB,GAAG,IAAI,CAACA,iBAAiB;QAC9C2c,GAAG,CAAC/hB,YAAY,GAAG,IAAI,CAACA,YAAY;QACpC+hB,GAAG,CACE3a,mBAAmB,CAAC,IAAI,CAACya,mBAAmB,CAAC,CAAC,CAAC,CAC/Clb,uBAAuB,CAACmD,WAAW,CAAC,CACpCpD,mBAAmB,CAACvG,OAAO,CAAC,CAC5BwI,oBAAoB,CAAC,IAAI,CAACgI,gBAAgB,IAAI,QAAQ,CAAC;QAC5D,IAAIqR,GAAG,EAAE;UACLD,GAAG,CAACzZ,aAAa,CAAC0Z,GAAG,CAACnrB,KAAK,CAAC;QAChC;MACJ;IACJ,CAAC,CAAC;IACF;IACAkrB,GAAG,CAACpf,aAAa,CAACgU,IAAI,CAACzgB,IAAI,CAAC,CAAC,CAAC,CAAC,CAACsR,SAAS,CAAC,MAAM;MAC5C;MACA,IAAI,IAAI,CAAC+W,WAAW,EAAE;QAClBwD,GAAG,CAAChc,UAAU,CAAC,IAAI,CAACwY,WAAW,CAACqB,QAAQ,CAAC;QACzC;MACJ;MACA;MACA;MACA,IAAI5gB,MAAM,GAAG,IAAI,CAAC9H,OAAO,CAACiiB,aAAa,CAAClF,aAAa;MACrD,OAAOjV,MAAM,EAAE;QACX,IAAIA,MAAM,CAACS,SAAS,CAAC1E,QAAQ,CAAC0kB,eAAe,CAAC,EAAE;UAC5CsC,GAAG,CAAChc,UAAU,CAAC4Z,OAAO,CAAC/D,cAAc,CAAChe,IAAI,CAACyU,IAAI,IAAI;YAC/C,OAAOA,IAAI,CAACnb,OAAO,CAACiiB,aAAa,KAAKna,MAAM;UAChD,CAAC,CAAC,EAAE4gB,QAAQ,IAAI,IAAI,CAAC;UACrB;QACJ;QACA5gB,MAAM,GAAGA,MAAM,CAACiV,aAAa;MACjC;IACJ,CAAC,CAAC;EACN;EACA;EACAyM,aAAaA,CAACqB,GAAG,EAAE;IACfA,GAAG,CAACnf,OAAO,CAAC4E,SAAS,CAAC0a,UAAU,IAAI;MAChC,IAAI,CAACtf,OAAO,CAACuf,IAAI,CAAC;QAAE1rB,MAAM,EAAE,IAAI;QAAE6D,KAAK,EAAE4nB,UAAU,CAAC5nB;MAAM,CAAC,CAAC;MAC5D;MACA;MACA,IAAI,CAAC0lB,kBAAkB,CAACoC,YAAY,CAAC,CAAC;IAC1C,CAAC,CAAC;IACFL,GAAG,CAAClf,QAAQ,CAAC2E,SAAS,CAAC6a,YAAY,IAAI;MACnC,IAAI,CAACxf,QAAQ,CAACsf,IAAI,CAAC;QAAE1rB,MAAM,EAAE,IAAI;QAAE6D,KAAK,EAAE+nB,YAAY,CAAC/nB;MAAM,CAAC,CAAC;IACnE,CAAC,CAAC;IACFynB,GAAG,CAACjf,KAAK,CAAC0E,SAAS,CAAC8a,QAAQ,IAAI;MAC5B,IAAI,CAACxf,KAAK,CAACqf,IAAI,CAAC;QACZ1rB,MAAM,EAAE,IAAI;QACZ+O,QAAQ,EAAE8c,QAAQ,CAAC9c,QAAQ;QAC3BgE,SAAS,EAAE8Y,QAAQ,CAAC9Y,SAAS;QAC7BlP,KAAK,EAAEgoB,QAAQ,CAAChoB;MACpB,CAAC,CAAC;MACF;MACA;MACA,IAAI,CAAC0lB,kBAAkB,CAACoC,YAAY,CAAC,CAAC;IAC1C,CAAC,CAAC;IACFL,GAAG,CAAChf,OAAO,CAACyE,SAAS,CAAC+a,UAAU,IAAI;MAChC,IAAI,CAACxf,OAAO,CAACof,IAAI,CAAC;QACd9d,SAAS,EAAEke,UAAU,CAACle,SAAS,CAACgc,IAAI;QACpClU,IAAI,EAAE,IAAI;QACVH,YAAY,EAAEuW,UAAU,CAACvW;MAC7B,CAAC,CAAC;IACN,CAAC,CAAC;IACF+V,GAAG,CAAC/e,MAAM,CAACwE,SAAS,CAACgb,SAAS,IAAI;MAC9B,IAAI,CAACxf,MAAM,CAACmf,IAAI,CAAC;QACb9d,SAAS,EAAEme,SAAS,CAACne,SAAS,CAACgc,IAAI;QACnClU,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;IACF4V,GAAG,CAAC9e,OAAO,CAACuE,SAAS,CAACib,SAAS,IAAI;MAC/B,IAAI,CAACxf,OAAO,CAACkf,IAAI,CAAC;QACd/V,aAAa,EAAEqW,SAAS,CAACrW,aAAa;QACtCJ,YAAY,EAAEyW,SAAS,CAACzW,YAAY;QACpCK,iBAAiB,EAAEoW,SAAS,CAACpW,iBAAiB,CAACgU,IAAI;QACnDhc,SAAS,EAAEoe,SAAS,CAACpe,SAAS,CAACgc,IAAI;QACnCpU,sBAAsB,EAAEwW,SAAS,CAACxW,sBAAsB;QACxDE,IAAI,EAAE,IAAI;QACV3G,QAAQ,EAAEid,SAAS,CAACjd,QAAQ;QAC5BgE,SAAS,EAAEiZ,SAAS,CAACjZ,SAAS;QAC9BlP,KAAK,EAAEmoB,SAAS,CAACnoB;MACrB,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA;EACAgmB,eAAeA,CAAC9D,MAAM,EAAE;IACpB,MAAM;MAAE3N,QAAQ;MAAEnM,cAAc;MAAE0C,iBAAiB;MAAEpF,YAAY;MAAEqH,eAAe;MAAEqb,gBAAgB;MAAEf,mBAAmB;MAAEhR;IAAkB,CAAC,GAAG6L,MAAM;IACvJ,IAAI,CAACpb,QAAQ,GAAGshB,gBAAgB,IAAI,IAAI,GAAG,KAAK,GAAGA,gBAAgB;IACnE,IAAI,CAAChgB,cAAc,GAAGA,cAAc,IAAI,CAAC;IACzC,IAAImM,QAAQ,EAAE;MACV,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IAC5B;IACA,IAAIzJ,iBAAiB,EAAE;MACnB,IAAI,CAACA,iBAAiB,GAAGA,iBAAiB;IAC9C;IACA,IAAIpF,YAAY,EAAE;MACd,IAAI,CAACA,YAAY,GAAGA,YAAY;IACpC;IACA,IAAIqH,eAAe,EAAE;MACjB,IAAI,CAACA,eAAe,GAAGA,eAAe;IAC1C;IACA,IAAIsa,mBAAmB,EAAE;MACrB,IAAI,CAACA,mBAAmB,GAAGA,mBAAmB;IAClD;IACA,IAAIhR,gBAAgB,EAAE;MAClB,IAAI,CAACA,gBAAgB,GAAGA,gBAAgB;IAC5C;EACJ;EACA;EACAmQ,qBAAqBA,CAAA,EAAG;IACpB;IACA,IAAI,CAACtf,QAAQ,CACRmV,IAAI;IACT;IACAxgB,GAAG,CAACqQ,OAAO,IAAI;MACX,MAAMmc,cAAc,GAAGnc,OAAO,CAACvQ,GAAG,CAACwL,MAAM,IAAIA,MAAM,CAACvK,OAAO,CAAC;MAC5D;MACA;MACA;MACA,IAAI,IAAI,CAAC+oB,WAAW,IAAI,IAAI,CAAC0B,mBAAmB,EAAE;QAC9CgB,cAAc,CAACzO,IAAI,CAAC,IAAI,CAAChd,OAAO,CAAC;MACrC;MACA,IAAI,CAAC0oB,QAAQ,CAACrZ,WAAW,CAACoc,cAAc,CAAC;IAC7C,CAAC,CAAC;IACF;IACAvsB,SAAS,CAAEoQ,OAAO,IAAK;MACnB,OAAO1Q,KAAK,CAAC,GAAG0Q,OAAO,CAACvQ,GAAG,CAACkW,IAAI,IAAIA,IAAI,CAACmS,aAAa,CAAC3H,IAAI,CAACtgB,SAAS,CAAC8V,IAAI,CAAC,CAAC,CAAC,CAAC;IAClF,CAAC,CAAC,EAAEnW,SAAS,CAAC,IAAI,CAACkqB,UAAU,CAAC,CAAC,CAC1B1Y,SAAS,CAACob,cAAc,IAAI;MAC7B;MACA,MAAMC,OAAO,GAAG,IAAI,CAACjD,QAAQ;MAC7B,MAAMne,MAAM,GAAGmhB,cAAc,CAAC1rB,OAAO,CAACiiB,aAAa;MACnDyJ,cAAc,CAACxhB,QAAQ,GAAGyhB,OAAO,CAAC1a,aAAa,CAAC1G,MAAM,CAAC,GAAGohB,OAAO,CAACza,YAAY,CAAC3G,MAAM,CAAC;IAC1F,CAAC,CAAC;EACN;EACA;IAAS,IAAI,CAACwY,IAAI,YAAA6I,gBAAA3I,CAAA;MAAA,YAAAA,CAAA,IAAwFwF,OAAO,EA7uBjBjsB,EAAE,CAAAirB,iBAAA,CA6uBiCjrB,EAAE,CAACkrB,UAAU,GA7uBhDlrB,EAAE,CAAAirB,iBAAA,CA6uB2De,aAAa,OA7uB1EhsB,EAAE,CAAAirB,iBAAA,CA6uBqH7pB,QAAQ,GA7uB/HpB,EAAE,CAAAirB,iBAAA,CA6uB0IjrB,EAAE,CAAC0pB,MAAM,GA7uBrJ1pB,EAAE,CAAAirB,iBAAA,CA6uBgKjrB,EAAE,CAACqvB,gBAAgB,GA7uBrLrvB,EAAE,CAAAirB,iBAAA,CA6uBgMa,eAAe,MA7uBjN9rB,EAAE,CAAAirB,iBAAA,CA6uB4OroB,IAAI,CAAC0sB,cAAc,MA7uBjQtvB,EAAE,CAAAirB,iBAAA,CA6uB4Rf,QAAQ,GA7uBtSlqB,EAAE,CAAAirB,iBAAA,CA6uBiTjrB,EAAE,CAACuvB,iBAAiB,GA7uBvUvvB,EAAE,CAAAirB,iBAAA,CA6uBkVP,eAAe,OA7uBnW1qB,EAAE,CAAAirB,iBAAA,CA6uB0YV,eAAe;IAAA,CAA4E;EAAE;EACzkB;IAAS,IAAI,CAACY,IAAI,kBA9uB8EnrB,EAAE,CAAAorB,iBAAA;MAAAziB,IAAA,EA8uBJsjB,OAAO;MAAArF,SAAA;MAAAC,SAAA;MAAA2I,QAAA;MAAAC,YAAA,WAAAC,qBAAAtI,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA9uBLpnB,EAAE,CAAA2vB,WAAA,sBAAAtI,GAAA,CAAA3Z,QA8uBE,CAAC,sBAAP2Z,GAAA,CAAA6E,QAAA,CAAArb,UAAA,CAAoB,CAAd,CAAC;QAAA;MAAA;MAAAwa,MAAA;QAAAsB,IAAA,GA9uBL3sB,EAAE,CAAAsrB,YAAA,CAAA3D,IAAA;QAAAxM,QAAA,GAAFnb,EAAE,CAAAsrB,YAAA,CAAA3D,IAAA;QAAAsG,mBAAA,GAAFjuB,EAAE,CAAAsrB,YAAA,CAAA3D,IAAA;QAAAhU,eAAA,GAAF3T,EAAE,CAAAsrB,YAAA,CAAA3D,IAAA;QAAA3Y,cAAA,GAAFhP,EAAE,CAAAsrB,YAAA,CAAA3D,IAAA;QAAA0F,gBAAA,GAAFrtB,EAAE,CAAAsrB,YAAA,CAAA3D,IAAA;QAAAja,QAAA,GAAF1N,EAAE,CAAAsrB,YAAA,CAAAC,0BAAA,iCA8uBua5qB,gBAAgB;QAAA+Q,iBAAA,GA9uBzb1R,EAAE,CAAAsrB,YAAA,CAAA3D,IAAA;QAAArb,YAAA,GAAFtM,EAAE,CAAAsrB,YAAA,CAAA3D,IAAA;QAAA1K,gBAAA,GAAFjd,EAAE,CAAAsrB,YAAA,CAAA3D,IAAA;MAAA;MAAAiI,OAAA;QAAA1gB,OAAA;QAAAC,QAAA;QAAAC,KAAA;QAAAC,OAAA;QAAAC,MAAA;QAAAC,OAAA;QAAAC,KAAA;MAAA;MAAAqgB,QAAA;MAAA/I,UAAA;MAAAC,QAAA,GAAF/mB,EAAE,CAAAwrB,kBAAA,CA8uB09B,CAAC;QAAEC,OAAO,EAAElB,eAAe;QAAEmB,WAAW,EAAEO;MAAQ,CAAC,CAAC,GA9uBhhCjsB,EAAE,CAAA2rB,wBAAA,EAAF3rB,EAAE,CAAA8vB,oBAAA;IAAA,EA8uB2kC;EAAE;AACnrC;AACA;EAAA,QAAAtI,SAAA,oBAAAA,SAAA,KAhvBoGxnB,EAAE,CAAAynB,iBAAA,CAgvBXwE,OAAO,EAAc,CAAC;IACrGtjB,IAAI,EAAE/H,SAAS;IACf8mB,IAAI,EAAE,CAAC;MACCpf,QAAQ,EAAE,WAAW;MACrBunB,QAAQ,EAAE,SAAS;MACnB/I,UAAU,EAAE,IAAI;MAChBe,IAAI,EAAE;QACF,OAAO,EAAEkE,eAAe;QACxB,2BAA2B,EAAE,UAAU;QACvC,2BAA2B,EAAE;MACjC,CAAC;MACDH,SAAS,EAAE,CAAC;QAAEH,OAAO,EAAElB,eAAe;QAAEmB,WAAW,EAAEO;MAAQ,CAAC;IAClE,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEtjB,IAAI,EAAE3I,EAAE,CAACkrB;EAAW,CAAC,EAAE;IAAEviB,IAAI,EAAE2K,SAAS;IAAE0W,UAAU,EAAE,CAAC;MACxErhB,IAAI,EAAElI,MAAM;MACZinB,IAAI,EAAE,CAACsE,aAAa;IACxB,CAAC,EAAE;MACCrjB,IAAI,EAAE9H;IACV,CAAC,EAAE;MACC8H,IAAI,EAAE7H;IACV,CAAC;EAAE,CAAC,EAAE;IAAE6H,IAAI,EAAE2K,SAAS;IAAE0W,UAAU,EAAE,CAAC;MAClCrhB,IAAI,EAAElI,MAAM;MACZinB,IAAI,EAAE,CAACtmB,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAEuH,IAAI,EAAE3I,EAAE,CAAC0pB;EAAO,CAAC,EAAE;IAAE/gB,IAAI,EAAE3I,EAAE,CAACqvB;EAAiB,CAAC,EAAE;IAAE1mB,IAAI,EAAE2K,SAAS;IAAE0W,UAAU,EAAE,CAAC;MACtFrhB,IAAI,EAAE9H;IACV,CAAC,EAAE;MACC8H,IAAI,EAAElI,MAAM;MACZinB,IAAI,EAAE,CAACoE,eAAe;IAC1B,CAAC;EAAE,CAAC,EAAE;IAAEnjB,IAAI,EAAE/F,IAAI,CAAC0sB,cAAc;IAAEtF,UAAU,EAAE,CAAC;MAC5CrhB,IAAI,EAAE9H;IACV,CAAC;EAAE,CAAC,EAAE;IAAE8H,IAAI,EAAEuhB;EAAS,CAAC,EAAE;IAAEvhB,IAAI,EAAE3I,EAAE,CAACuvB;EAAkB,CAAC,EAAE;IAAE5mB,IAAI,EAAEgiB,aAAa;IAAEX,UAAU,EAAE,CAAC;MAC1FrhB,IAAI,EAAE9H;IACV,CAAC,EAAE;MACC8H,IAAI,EAAE1H;IACV,CAAC,EAAE;MACC0H,IAAI,EAAElI,MAAM;MACZinB,IAAI,EAAE,CAACgD,eAAe;IAC1B,CAAC;EAAE,CAAC,EAAE;IAAE/hB,IAAI,EAAEsjB,OAAO;IAAEjC,UAAU,EAAE,CAAC;MAChCrhB,IAAI,EAAE9H;IACV,CAAC,EAAE;MACC8H,IAAI,EAAE7H;IACV,CAAC,EAAE;MACC6H,IAAI,EAAElI,MAAM;MACZinB,IAAI,EAAE,CAAC6C,eAAe;IAC1B,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEoC,IAAI,EAAE,CAAC;MAChChkB,IAAI,EAAE5H,KAAK;MACX2mB,IAAI,EAAE,CAAC,aAAa;IACxB,CAAC,CAAC;IAAEvM,QAAQ,EAAE,CAAC;MACXxS,IAAI,EAAE5H,KAAK;MACX2mB,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAEuG,mBAAmB,EAAE,CAAC;MACtBtlB,IAAI,EAAE5H,KAAK;MACX2mB,IAAI,EAAE,CAAC,oBAAoB;IAC/B,CAAC,CAAC;IAAE/T,eAAe,EAAE,CAAC;MAClBhL,IAAI,EAAE5H,KAAK;MACX2mB,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAE1Y,cAAc,EAAE,CAAC;MACjBrG,IAAI,EAAE5H,KAAK;MACX2mB,IAAI,EAAE,CAAC,mBAAmB;IAC9B,CAAC,CAAC;IAAE2F,gBAAgB,EAAE,CAAC;MACnB1kB,IAAI,EAAE5H,KAAK;MACX2mB,IAAI,EAAE,CAAC,yBAAyB;IACpC,CAAC,CAAC;IAAEha,QAAQ,EAAE,CAAC;MACX/E,IAAI,EAAE5H,KAAK;MACX2mB,IAAI,EAAE,CAAC;QAAEmE,KAAK,EAAE,iBAAiB;QAAE3nB,SAAS,EAAEvD;MAAiB,CAAC;IACpE,CAAC,CAAC;IAAE+Q,iBAAiB,EAAE,CAAC;MACpB/I,IAAI,EAAE5H,KAAK;MACX2mB,IAAI,EAAE,CAAC,0BAA0B;IACrC,CAAC,CAAC;IAAEpb,YAAY,EAAE,CAAC;MACf3D,IAAI,EAAE5H,KAAK;MACX2mB,IAAI,EAAE,CAAC,qBAAqB;IAChC,CAAC,CAAC;IAAEzK,gBAAgB,EAAE,CAAC;MACnBtU,IAAI,EAAE5H,KAAK;MACX2mB,IAAI,EAAE,CAAC,yBAAyB;IACpC,CAAC,CAAC;IAAExY,OAAO,EAAE,CAAC;MACVvG,IAAI,EAAEzH,MAAM;MACZwmB,IAAI,EAAE,CAAC,gBAAgB;IAC3B,CAAC,CAAC;IAAEvY,QAAQ,EAAE,CAAC;MACXxG,IAAI,EAAEzH,MAAM;MACZwmB,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAEtY,KAAK,EAAE,CAAC;MACRzG,IAAI,EAAEzH,MAAM;MACZwmB,IAAI,EAAE,CAAC,cAAc;IACzB,CAAC,CAAC;IAAErY,OAAO,EAAE,CAAC;MACV1G,IAAI,EAAEzH,MAAM;MACZwmB,IAAI,EAAE,CAAC,gBAAgB;IAC3B,CAAC,CAAC;IAAEpY,MAAM,EAAE,CAAC;MACT3G,IAAI,EAAEzH,MAAM;MACZwmB,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC,CAAC;IAAEnY,OAAO,EAAE,CAAC;MACV5G,IAAI,EAAEzH,MAAM;MACZwmB,IAAI,EAAE,CAAC,gBAAgB;IAC3B,CAAC,CAAC;IAAElY,KAAK,EAAE,CAAC;MACR7G,IAAI,EAAEzH,MAAM;MACZwmB,IAAI,EAAE,CAAC,cAAc;IACzB,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA,MAAMqI,mBAAmB,GAAG,IAAIrvB,cAAc,CAAC,kBAAkB,CAAC;AAClE;AACA;AACA;AACA;AACA;AACA;AACA,MAAMsvB,gBAAgB,CAAC;EACnBlqB,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAACmqB,MAAM,GAAG,IAAItlB,GAAG,CAAC,CAAC;IACvB;IACA,IAAI,CAAC+C,QAAQ,GAAG,KAAK;EACzB;EACAyb,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC8G,MAAM,CAAC/pB,KAAK,CAAC,CAAC;EACvB;EACA;IAAS,IAAI,CAACqgB,IAAI,YAAA2J,yBAAAzJ,CAAA;MAAA,YAAAA,CAAA,IAAwFuJ,gBAAgB;IAAA,CAAmD;EAAE;EAC/K;IAAS,IAAI,CAAC7E,IAAI,kBAx2B8EnrB,EAAE,CAAAorB,iBAAA;MAAAziB,IAAA,EAw2BJqnB,gBAAgB;MAAApJ,SAAA;MAAAyE,MAAA;QAAA3d,QAAA,GAx2Bd1N,EAAE,CAAAsrB,YAAA,CAAAC,0BAAA,0CAw2B+H5qB,gBAAgB;MAAA;MAAAkvB,QAAA;MAAA/I,UAAA;MAAAC,QAAA,GAx2BjJ/mB,EAAE,CAAAwrB,kBAAA,CAw2B+J,CAAC;QAAEC,OAAO,EAAEsE,mBAAmB;QAAErE,WAAW,EAAEsE;MAAiB,CAAC,CAAC,GAx2BlOhwB,EAAE,CAAA2rB,wBAAA;IAAA,EAw2BiR;EAAE;AACzX;AACA;EAAA,QAAAnE,SAAA,oBAAAA,SAAA,KA12BoGxnB,EAAE,CAAAynB,iBAAA,CA02BXuI,gBAAgB,EAAc,CAAC;IAC9GrnB,IAAI,EAAE/H,SAAS;IACf8mB,IAAI,EAAE,CAAC;MACCpf,QAAQ,EAAE,oBAAoB;MAC9BunB,QAAQ,EAAE,kBAAkB;MAC5B/I,UAAU,EAAE,IAAI;MAChB8E,SAAS,EAAE,CAAC;QAAEH,OAAO,EAAEsE,mBAAmB;QAAErE,WAAW,EAAEsE;MAAiB,CAAC;IAC/E,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEtiB,QAAQ,EAAE,CAAC;MACzB/E,IAAI,EAAE5H,KAAK;MACX2mB,IAAI,EAAE,CAAC;QAAEmE,KAAK,EAAE,0BAA0B;QAAE3nB,SAAS,EAAEvD;MAAiB,CAAC;IAC7E,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,IAAIwvB,gBAAgB,GAAG,CAAC;AACxB;AACA,MAAMC,WAAW,CAAC;EACd;EACA;IAAS,IAAI,CAACC,UAAU,GAAG,EAAE;EAAE;EAC/B;EACA,IAAI3iB,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS,IAAK,CAAC,CAAC,IAAI,CAAC2iB,MAAM,IAAI,IAAI,CAACA,MAAM,CAAC5iB,QAAS;EACpE;EACA,IAAIA,QAAQA,CAACvK,KAAK,EAAE;IAChB;IACA;IACA;IACA;IACA,IAAI,CAAC0pB,YAAY,CAACnf,QAAQ,GAAG,IAAI,CAACC,SAAS,GAAGxK,KAAK;EACvD;EACA2C,WAAWA,CAAA,CACX;EACAtC,OAAO,EAAE6oB,QAAQ,EAAEC,kBAAkB,EAAEiE,iBAAiB,EAAEnE,IAAI,EAAEkE,MAAM,EAAExH,MAAM,EAAE;IAC5E,IAAI,CAACtlB,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC8oB,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACiE,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACnE,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACkE,MAAM,GAAGA,MAAM;IACpB;IACA,IAAI,CAAC9D,UAAU,GAAG,IAAIzqB,OAAO,CAAC,CAAC;IAC/B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACsiB,WAAW,GAAG,EAAE;IACrB;AACR;AACA;AACA;IACQ,IAAI,CAACmM,EAAE,GAAI,iBAAgBL,gBAAgB,EAAG,EAAC;IAC/C;AACR;AACA;AACA;IACQ,IAAI,CAACjO,cAAc,GAAG,MAAM,IAAI;IAChC;IACA,IAAI,CAACC,aAAa,GAAG,MAAM,IAAI;IAC/B;IACA,IAAI,CAAC5S,OAAO,GAAG,IAAIvO,YAAY,CAAC,CAAC;IACjC;AACR;AACA;IACQ,IAAI,CAACqO,OAAO,GAAG,IAAIrO,YAAY,CAAC,CAAC;IACjC;AACR;AACA;AACA;IACQ,IAAI,CAACsO,MAAM,GAAG,IAAItO,YAAY,CAAC,CAAC;IAChC;IACA,IAAI,CAACohB,MAAM,GAAG,IAAIphB,YAAY,CAAC,CAAC;IAChC;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACyvB,cAAc,GAAG,IAAI9lB,GAAG,CAAC,CAAC;IAC/B,IAAI,OAAO6c,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/CgD,iBAAiB,CAAChnB,OAAO,CAACiiB,aAAa,EAAE,aAAa,CAAC;IAC3D;IACA,IAAI,CAACoH,YAAY,GAAGR,QAAQ,CAACjC,cAAc,CAAC5mB,OAAO,CAAC;IACpD,IAAI,CAACqpB,YAAY,CAACF,IAAI,GAAG,IAAI;IAC7B,IAAI7D,MAAM,EAAE;MACR,IAAI,CAAC8D,eAAe,CAAC9D,MAAM,CAAC;IAChC;IACA,IAAI,CAAC+D,YAAY,CAAC3K,cAAc,GAAG,CAACvD,IAAI,EAAE/F,IAAI,KAAK;MAC/C,OAAO,IAAI,CAACsJ,cAAc,CAACvD,IAAI,CAACgO,IAAI,EAAE/T,IAAI,CAAC+T,IAAI,CAAC;IACpD,CAAC;IACD,IAAI,CAACE,YAAY,CAAC1K,aAAa,GAAG,CAACnC,KAAK,EAAErB,IAAI,EAAE/F,IAAI,KAAK;MACrD,OAAO,IAAI,CAACuJ,aAAa,CAACnC,KAAK,EAAErB,IAAI,CAACgO,IAAI,EAAE/T,IAAI,CAAC+T,IAAI,CAAC;IAC1D,CAAC;IACD,IAAI,CAAC+D,2BAA2B,CAAC,IAAI,CAAC7D,YAAY,CAAC;IACnD,IAAI,CAACG,aAAa,CAAC,IAAI,CAACH,YAAY,CAAC;IACrCuD,WAAW,CAACC,UAAU,CAAC7P,IAAI,CAAC,IAAI,CAAC;IACjC,IAAI8P,MAAM,EAAE;MACRA,MAAM,CAACL,MAAM,CAACjkB,GAAG,CAAC,IAAI,CAAC;IAC3B;EACJ;EACA;EACA8gB,OAAOA,CAACrU,IAAI,EAAE;IACV,IAAI,CAACgY,cAAc,CAACzkB,GAAG,CAACyM,IAAI,CAAC;IAC7B,IAAI,IAAI,CAACoU,YAAY,CAAChc,UAAU,CAAC,CAAC,EAAE;MAChC,IAAI,CAAC8f,iBAAiB,CAAC,CAAC;IAC5B;EACJ;EACA;EACAhD,UAAUA,CAAClV,IAAI,EAAE;IACb,IAAI,CAACgY,cAAc,CAAC9b,MAAM,CAAC8D,IAAI,CAAC;IAChC,IAAI,IAAI,CAACoU,YAAY,CAAChc,UAAU,CAAC,CAAC,EAAE;MAChC,IAAI,CAAC8f,iBAAiB,CAAC,CAAC;IAC5B;EACJ;EACA;EACAC,cAAcA,CAAA,EAAG;IACb,OAAO5jB,KAAK,CAAC6Q,IAAI,CAAC,IAAI,CAAC4S,cAAc,CAAC,CAAC1R,IAAI,CAAC,CAACmC,CAAC,EAAEC,CAAC,KAAK;MAClD,MAAM0P,gBAAgB,GAAG3P,CAAC,CAACgL,QAAQ,CAC9BtZ,iBAAiB,CAAC,CAAC,CACnBke,uBAAuB,CAAC3P,CAAC,CAAC+K,QAAQ,CAACtZ,iBAAiB,CAAC,CAAC,CAAC;MAC5D;MACA;MACA;MACA,OAAOie,gBAAgB,GAAGE,IAAI,CAACC,2BAA2B,GAAG,CAAC,CAAC,GAAG,CAAC;IACvE,CAAC,CAAC;EACN;EACA7H,WAAWA,CAAA,EAAG;IACV,MAAMnJ,KAAK,GAAGoQ,WAAW,CAACC,UAAU,CAAC3mB,OAAO,CAAC,IAAI,CAAC;IAClD,IAAIsW,KAAK,GAAG,CAAC,CAAC,EAAE;MACZoQ,WAAW,CAACC,UAAU,CAACjS,MAAM,CAAC4B,KAAK,EAAE,CAAC,CAAC;IAC3C;IACA,IAAI,IAAI,CAACsQ,MAAM,EAAE;MACb,IAAI,CAACA,MAAM,CAACL,MAAM,CAACtb,MAAM,CAAC,IAAI,CAAC;IACnC;IACA,IAAI,CAAC8b,cAAc,CAACvqB,KAAK,CAAC,CAAC;IAC3B,IAAI,CAAC2mB,YAAY,CAAC5Y,OAAO,CAAC,CAAC;IAC3B,IAAI,CAACuY,UAAU,CAAC9c,IAAI,CAAC,CAAC;IACtB,IAAI,CAAC8c,UAAU,CAACjY,QAAQ,CAAC,CAAC;EAC9B;EACA;EACAmc,2BAA2BA,CAACrC,GAAG,EAAE;IAC7B,IAAI,IAAI,CAACjC,IAAI,EAAE;MACX,IAAI,CAACA,IAAI,CAACvY,MAAM,CACXoP,IAAI,CAACtgB,SAAS,CAAC,IAAI,CAACypB,IAAI,CAACjpB,KAAK,CAAC,EAAEb,SAAS,CAAC,IAAI,CAACkqB,UAAU,CAAC,CAAC,CAC5D1Y,SAAS,CAAC3Q,KAAK,IAAIkrB,GAAG,CAACzZ,aAAa,CAACzR,KAAK,CAAC,CAAC;IACrD;IACAkrB,GAAG,CAACpf,aAAa,CAAC6E,SAAS,CAAC,MAAM;MAC9B,MAAMmL,QAAQ,GAAGrd,WAAW,CAAC,IAAI,CAACyiB,WAAW,CAAC,CAAC9hB,GAAG,CAACqW,IAAI,IAAI;QACvD,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;UAC1B,MAAMqY,qBAAqB,GAAGb,WAAW,CAACC,UAAU,CAACnmB,IAAI,CAACgnB,IAAI,IAAIA,IAAI,CAACV,EAAE,KAAK5X,IAAI,CAAC;UACnF,IAAI,CAACqY,qBAAqB,KAAK,OAAOzJ,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;YAC3E2J,OAAO,CAACC,IAAI,CAAE,2DAA0DxY,IAAK,GAAE,CAAC;UACpF;UACA,OAAOqY,qBAAqB;QAChC;QACA,OAAOrY,IAAI;MACf,CAAC,CAAC;MACF,IAAI,IAAI,CAAC0X,MAAM,EAAE;QACb,IAAI,CAACA,MAAM,CAACL,MAAM,CAACzpB,OAAO,CAACoS,IAAI,IAAI;UAC/B,IAAIqG,QAAQ,CAACvV,OAAO,CAACkP,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YAC/BqG,QAAQ,CAACuB,IAAI,CAAC5H,IAAI,CAAC;UACvB;QACJ,CAAC,CAAC;MACN;MACA;MACA;MACA,IAAI,CAAC,IAAI,CAACyY,0BAA0B,EAAE;QAClC,MAAMC,iBAAiB,GAAG,IAAI,CAACf,iBAAiB,CAC3CgB,2BAA2B,CAAC,IAAI,CAAC/tB,OAAO,CAAC,CACzCjB,GAAG,CAACivB,UAAU,IAAIA,UAAU,CAACC,aAAa,CAAC,CAAC,CAAChM,aAAa,CAAC;QAChE,IAAI,CAACoH,YAAY,CAACpJ,qBAAqB,CAAC6N,iBAAiB,CAAC;QAC1D;QACA;QACA,IAAI,CAACD,0BAA0B,GAAG,IAAI;MAC1C;MACAhD,GAAG,CAAC3gB,QAAQ,GAAG,IAAI,CAACA,QAAQ;MAC5B2gB,GAAG,CAAClT,QAAQ,GAAG,IAAI,CAACA,QAAQ;MAC5BkT,GAAG,CAAClV,eAAe,GAAG,IAAI,CAACA,eAAe;MAC1CkV,GAAG,CAACrM,kBAAkB,GAAG,IAAI,CAACA,kBAAkB;MAChDqM,GAAG,CAACpM,cAAc,GAAGtgB,oBAAoB,CAAC,IAAI,CAACsgB,cAAc,EAAE,CAAC,CAAC;MACjEoM,GAAG,CACEhK,WAAW,CAACpF,QAAQ,CAACkF,MAAM,CAACvL,IAAI,IAAIA,IAAI,IAAIA,IAAI,KAAK,IAAI,CAAC,CAACrW,GAAG,CAAC2uB,IAAI,IAAIA,IAAI,CAACrE,YAAY,CAAC,CAAC,CAC1FvI,eAAe,CAAC,IAAI,CAAC7F,WAAW,CAAC;IAC1C,CAAC,CAAC;EACN;EACA;EACAuO,aAAaA,CAACqB,GAAG,EAAE;IACfA,GAAG,CAACpf,aAAa,CAAC6E,SAAS,CAAC,MAAM;MAC9B,IAAI,CAAC6c,iBAAiB,CAAC,CAAC;MACxB,IAAI,CAACrE,kBAAkB,CAACoC,YAAY,CAAC,CAAC;IAC1C,CAAC,CAAC;IACFL,GAAG,CAAChf,OAAO,CAACyE,SAAS,CAAClN,KAAK,IAAI;MAC3B,IAAI,CAACyI,OAAO,CAACof,IAAI,CAAC;QACd9d,SAAS,EAAE,IAAI;QACf8H,IAAI,EAAE7R,KAAK,CAAC6R,IAAI,CAACkU,IAAI;QACrBrU,YAAY,EAAE1R,KAAK,CAAC0R;MACxB,CAAC,CAAC;IACN,CAAC,CAAC;IACF+V,GAAG,CAAC/e,MAAM,CAACwE,SAAS,CAAClN,KAAK,IAAI;MAC1B,IAAI,CAAC0I,MAAM,CAACmf,IAAI,CAAC;QACb9d,SAAS,EAAE,IAAI;QACf8H,IAAI,EAAE7R,KAAK,CAAC6R,IAAI,CAACkU;MACrB,CAAC,CAAC;MACF,IAAI,CAACL,kBAAkB,CAACoC,YAAY,CAAC,CAAC;IAC1C,CAAC,CAAC;IACFL,GAAG,CAACjM,MAAM,CAACtO,SAAS,CAAClN,KAAK,IAAI;MAC1B,IAAI,CAACwb,MAAM,CAACqM,IAAI,CAAC;QACb/V,aAAa,EAAE9R,KAAK,CAAC8R,aAAa;QAClCJ,YAAY,EAAE1R,KAAK,CAAC0R,YAAY;QAChC3H,SAAS,EAAE,IAAI;QACf8H,IAAI,EAAE7R,KAAK,CAAC6R,IAAI,CAACkU;MACrB,CAAC,CAAC;IACN,CAAC,CAAC;IACF0B,GAAG,CAAC9e,OAAO,CAACuE,SAAS,CAACib,SAAS,IAAI;MAC/B,IAAI,CAACxf,OAAO,CAACkf,IAAI,CAAC;QACd/V,aAAa,EAAEqW,SAAS,CAACrW,aAAa;QACtCJ,YAAY,EAAEyW,SAAS,CAACzW,YAAY;QACpCK,iBAAiB,EAAEoW,SAAS,CAACpW,iBAAiB,CAACgU,IAAI;QACnDhc,SAAS,EAAEoe,SAAS,CAACpe,SAAS,CAACgc,IAAI;QACnClU,IAAI,EAAEsW,SAAS,CAACtW,IAAI,CAACkU,IAAI;QACzBpU,sBAAsB,EAAEwW,SAAS,CAACxW,sBAAsB;QACxDzG,QAAQ,EAAEid,SAAS,CAACjd,QAAQ;QAC5BgE,SAAS,EAAEiZ,SAAS,CAACjZ,SAAS;QAC9BlP,KAAK,EAAEmoB,SAAS,CAACnoB;MACrB,CAAC,CAAC;MACF;MACA;MACA,IAAI,CAAC0lB,kBAAkB,CAACoC,YAAY,CAAC,CAAC;IAC1C,CAAC,CAAC;IACFtsB,KAAK,CAACisB,GAAG,CAAChM,gBAAgB,EAAEgM,GAAG,CAAC/L,gBAAgB,CAAC,CAACxO,SAAS,CAAC,MAAM,IAAI,CAACwY,kBAAkB,CAACoC,YAAY,CAAC,CAAC,CAAC;EAC7G;EACA;EACA9B,eAAeA,CAAC9D,MAAM,EAAE;IACpB,MAAM;MAAE3N,QAAQ;MAAE6T,gBAAgB;MAAE7V,eAAe;MAAEuY,sBAAsB;MAAEC;IAAgB,CAAC,GAAG7I,MAAM;IACvG,IAAI,CAACpb,QAAQ,GAAGshB,gBAAgB,IAAI,IAAI,GAAG,KAAK,GAAGA,gBAAgB;IACnE,IAAI,CAAC7V,eAAe,GAAGA,eAAe,IAAI,IAAI,GAAG,KAAK,GAAGA,eAAe;IACxE,IAAI,CAAC6I,kBAAkB,GAAG0P,sBAAsB,IAAI,IAAI,GAAG,KAAK,GAAGA,sBAAsB;IACzF,IAAI,CAACjT,WAAW,GAAGkT,eAAe,IAAI,UAAU;IAChD,IAAIxW,QAAQ,EAAE;MACV,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IAC5B;EACJ;EACA;EACAwV,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC9D,YAAY,CAAC/N,SAAS,CAAC,IAAI,CAAC8R,cAAc,CAAC,CAAC,CAACruB,GAAG,CAACkW,IAAI,IAAIA,IAAI,CAACyT,QAAQ,CAAC,CAAC;EACjF;EACA;IAAS,IAAI,CAAC3F,IAAI,YAAAqL,oBAAAnL,CAAA;MAAA,YAAAA,CAAA,IAAwF2J,WAAW,EAhmCrBpwB,EAAE,CAAAirB,iBAAA,CAgmCqCjrB,EAAE,CAACkrB,UAAU,GAhmCpDlrB,EAAE,CAAAirB,iBAAA,CAgmC+Df,QAAQ,GAhmCzElqB,EAAE,CAAAirB,iBAAA,CAgmCoFjrB,EAAE,CAACuvB,iBAAiB,GAhmC1GvvB,EAAE,CAAAirB,iBAAA,CAgmCqH5pB,EAAE,CAACwwB,gBAAgB,GAhmC1I7xB,EAAE,CAAAirB,iBAAA,CAgmCqJroB,IAAI,CAAC0sB,cAAc,MAhmC1KtvB,EAAE,CAAAirB,iBAAA,CAgmCqM8E,mBAAmB,OAhmC1N/vB,EAAE,CAAAirB,iBAAA,CAgmCqQa,eAAe;IAAA,CAA4D;EAAE;EACpb;IAAS,IAAI,CAACX,IAAI,kBAjmC8EnrB,EAAE,CAAAorB,iBAAA;MAAAziB,IAAA,EAimCJynB,WAAW;MAAAxJ,SAAA;MAAAC,SAAA;MAAA2I,QAAA;MAAAC,YAAA,WAAAqC,yBAAA1K,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAjmCTpnB,EAAE,CAAA+xB,WAAA,OAAA1K,GAAA,CAAAmJ,EAAA;UAAFxwB,EAAE,CAAA2vB,WAAA,2BAAAtI,GAAA,CAAA3Z,QAimCM,CAAC,2BAAX2Z,GAAA,CAAAwF,YAAA,CAAAhc,UAAA,CAAwB,CAAd,CAAC,4BAAXwW,GAAA,CAAAwF,YAAA,CAAA/b,WAAA,CAAyB,CAAf,CAAC;QAAA;MAAA;MAAAua,MAAA;QAAAhH,WAAA,GAjmCTrkB,EAAE,CAAAsrB,YAAA,CAAA3D,IAAA;QAAAgF,IAAA,GAAF3sB,EAAE,CAAAsrB,YAAA,CAAA3D,IAAA;QAAAlJ,WAAA,GAAFze,EAAE,CAAAsrB,YAAA,CAAA3D,IAAA;QAAA6I,EAAA;QAAArV,QAAA,GAAFnb,EAAE,CAAAsrB,YAAA,CAAA3D,IAAA;QAAAja,QAAA,GAAF1N,EAAE,CAAAsrB,YAAA,CAAAC,0BAAA,qCAimC2U5qB,gBAAgB;QAAAwY,eAAA,GAjmC7VnZ,EAAE,CAAAsrB,YAAA,CAAAC,0BAAA,mDAimCia5qB,gBAAgB;QAAAuhB,cAAA,GAjmCnbliB,EAAE,CAAAsrB,YAAA,CAAA3D,IAAA;QAAAxF,aAAA,GAAFniB,EAAE,CAAAsrB,YAAA,CAAA3D,IAAA;QAAA3F,kBAAA,GAAFhiB,EAAE,CAAAsrB,YAAA,CAAAC,0BAAA,yDAimC+nB5qB,gBAAgB;QAAAshB,cAAA,GAjmCjpBjiB,EAAE,CAAAsrB,YAAA,CAAA3D,IAAA;MAAA;MAAAiI,OAAA;QAAArgB,OAAA;QAAAF,OAAA;QAAAC,MAAA;QAAA8S,MAAA;MAAA;MAAAyN,QAAA;MAAA/I,UAAA;MAAAC,QAAA,GAAF/mB,EAAE,CAAAwrB,kBAAA,CAimCylC;MACnrC;MACA;QAAEC,OAAO,EAAEsE,mBAAmB;QAAEiC,QAAQ,EAAE1e;MAAU,CAAC,EACrD;QAAEmY,OAAO,EAAEO,aAAa;QAAEN,WAAW,EAAE0E;MAAY,CAAC,CACvD,GArmC2FpwB,EAAE,CAAA2rB,wBAAA;IAAA,EAqmCjD;EAAE;AACvD;AACA;EAAA,QAAAnE,SAAA,oBAAAA,SAAA,KAvmCoGxnB,EAAE,CAAAynB,iBAAA,CAumCX2I,WAAW,EAAc,CAAC;IACzGznB,IAAI,EAAE/H,SAAS;IACf8mB,IAAI,EAAE,CAAC;MACCpf,QAAQ,EAAE,8BAA8B;MACxCunB,QAAQ,EAAE,aAAa;MACvB/I,UAAU,EAAE,IAAI;MAChB8E,SAAS,EAAE;MACP;MACA;QAAEH,OAAO,EAAEsE,mBAAmB;QAAEiC,QAAQ,EAAE1e;MAAU,CAAC,EACrD;QAAEmY,OAAO,EAAEO,aAAa;QAAEN,WAAW,EAAE0E;MAAY,CAAC,CACvD;MACDvI,IAAI,EAAE;QACF,OAAO,EAAE,eAAe;QACxB,WAAW,EAAE,IAAI;QACjB,gCAAgC,EAAE,UAAU;QAC5C,gCAAgC,EAAE,2BAA2B;QAC7D,iCAAiC,EAAE;MACvC;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAElf,IAAI,EAAE3I,EAAE,CAACkrB;EAAW,CAAC,EAAE;IAAEviB,IAAI,EAAEuhB;EAAS,CAAC,EAAE;IAAEvhB,IAAI,EAAE3I,EAAE,CAACuvB;EAAkB,CAAC,EAAE;IAAE5mB,IAAI,EAAEtH,EAAE,CAACwwB;EAAiB,CAAC,EAAE;IAAElpB,IAAI,EAAE/F,IAAI,CAAC0sB,cAAc;IAAEtF,UAAU,EAAE,CAAC;MACrKrhB,IAAI,EAAE9H;IACV,CAAC;EAAE,CAAC,EAAE;IAAE8H,IAAI,EAAEqnB,gBAAgB;IAAEhG,UAAU,EAAE,CAAC;MACzCrhB,IAAI,EAAE9H;IACV,CAAC,EAAE;MACC8H,IAAI,EAAElI,MAAM;MACZinB,IAAI,EAAE,CAACqI,mBAAmB;IAC9B,CAAC,EAAE;MACCpnB,IAAI,EAAE7H;IACV,CAAC;EAAE,CAAC,EAAE;IAAE6H,IAAI,EAAE2K,SAAS;IAAE0W,UAAU,EAAE,CAAC;MAClCrhB,IAAI,EAAE9H;IACV,CAAC,EAAE;MACC8H,IAAI,EAAElI,MAAM;MACZinB,IAAI,EAAE,CAACoE,eAAe;IAC1B,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEzH,WAAW,EAAE,CAAC;MACvC1b,IAAI,EAAE5H,KAAK;MACX2mB,IAAI,EAAE,CAAC,wBAAwB;IACnC,CAAC,CAAC;IAAEiF,IAAI,EAAE,CAAC;MACPhkB,IAAI,EAAE5H,KAAK;MACX2mB,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAEjJ,WAAW,EAAE,CAAC;MACd9V,IAAI,EAAE5H,KAAK;MACX2mB,IAAI,EAAE,CAAC,wBAAwB;IACnC,CAAC,CAAC;IAAE8I,EAAE,EAAE,CAAC;MACL7nB,IAAI,EAAE5H;IACV,CAAC,CAAC;IAAEoa,QAAQ,EAAE,CAAC;MACXxS,IAAI,EAAE5H,KAAK;MACX2mB,IAAI,EAAE,CAAC,qBAAqB;IAChC,CAAC,CAAC;IAAEha,QAAQ,EAAE,CAAC;MACX/E,IAAI,EAAE5H,KAAK;MACX2mB,IAAI,EAAE,CAAC;QAAEmE,KAAK,EAAE,qBAAqB;QAAE3nB,SAAS,EAAEvD;MAAiB,CAAC;IACxE,CAAC,CAAC;IAAEwY,eAAe,EAAE,CAAC;MAClBxQ,IAAI,EAAE5H,KAAK;MACX2mB,IAAI,EAAE,CAAC;QAAEmE,KAAK,EAAE,4BAA4B;QAAE3nB,SAAS,EAAEvD;MAAiB,CAAC;IAC/E,CAAC,CAAC;IAAEuhB,cAAc,EAAE,CAAC;MACjBvZ,IAAI,EAAE5H,KAAK;MACX2mB,IAAI,EAAE,CAAC,2BAA2B;IACtC,CAAC,CAAC;IAAEvF,aAAa,EAAE,CAAC;MAChBxZ,IAAI,EAAE5H,KAAK;MACX2mB,IAAI,EAAE,CAAC,0BAA0B;IACrC,CAAC,CAAC;IAAE1F,kBAAkB,EAAE,CAAC;MACrBrZ,IAAI,EAAE5H,KAAK;MACX2mB,IAAI,EAAE,CAAC;QAAEmE,KAAK,EAAE,+BAA+B;QAAE3nB,SAAS,EAAEvD;MAAiB,CAAC;IAClF,CAAC,CAAC;IAAEshB,cAAc,EAAE,CAAC;MACjBtZ,IAAI,EAAE5H,KAAK;MACX2mB,IAAI,EAAE,CAAC,2BAA2B;IACtC,CAAC,CAAC;IAAEnY,OAAO,EAAE,CAAC;MACV5G,IAAI,EAAEzH,MAAM;MACZwmB,IAAI,EAAE,CAAC,oBAAoB;IAC/B,CAAC,CAAC;IAAErY,OAAO,EAAE,CAAC;MACV1G,IAAI,EAAEzH,MAAM;MACZwmB,IAAI,EAAE,CAAC,oBAAoB;IAC/B,CAAC,CAAC;IAAEpY,MAAM,EAAE,CAAC;MACT3G,IAAI,EAAEzH,MAAM;MACZwmB,IAAI,EAAE,CAAC,mBAAmB;IAC9B,CAAC,CAAC;IAAEtF,MAAM,EAAE,CAAC;MACTzZ,IAAI,EAAEzH,MAAM;MACZwmB,IAAI,EAAE,CAAC,mBAAmB;IAC9B,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA,MAAMuK,gBAAgB,GAAG,IAAIvxB,cAAc,CAAC,gBAAgB,CAAC;AAC7D;AACA;AACA;AACA;AACA,MAAMwxB,cAAc,CAAC;EACjBpsB,WAAWA,CAACyoB,WAAW,EAAE;IACrB,IAAI,CAACA,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAAC4D,KAAK,GAAG/xB,MAAM,CAACmqB,eAAe,EAAE;MAAE6H,QAAQ,EAAE;IAAK,CAAC,CAAC;IACxD;IACA,IAAI,CAACzlB,SAAS,GAAG,KAAK;IACtB,IAAI,CAACwlB,KAAK,EAAEtE,mBAAmB,CAAC,IAAI,CAAC;EACzC;EACA1E,WAAWA,CAAA,EAAG;IACV,IAAI,CAACgJ,KAAK,EAAErE,qBAAqB,CAAC,IAAI,CAAC;EAC3C;EACA;IAAS,IAAI,CAACvH,IAAI,YAAA8L,uBAAA5L,CAAA;MAAA,YAAAA,CAAA,IAAwFyL,cAAc,EA3sCxBlyB,EAAE,CAAAirB,iBAAA,CA2sCwCjrB,EAAE,CAACsyB,WAAW;IAAA,CAA4C;EAAE;EACtM;IAAS,IAAI,CAACnH,IAAI,kBA5sC8EnrB,EAAE,CAAAorB,iBAAA;MAAAziB,IAAA,EA4sCJupB,cAAc;MAAAtL,SAAA;MAAAyE,MAAA;QAAAsB,IAAA;QAAAhgB,SAAA,GA5sCZ3M,EAAE,CAAAsrB,YAAA,CAAAC,0BAAA,4BA4sCuI5qB,gBAAgB;MAAA;MAAAmmB,UAAA;MAAAC,QAAA,GA5sCzJ/mB,EAAE,CAAAwrB,kBAAA,CA4sCuK,CAAC;QAAEC,OAAO,EAAEwG,gBAAgB;QAAEvG,WAAW,EAAEwG;MAAe,CAAC,CAAC,GA5sCrOlyB,EAAE,CAAA2rB,wBAAA;IAAA,EA4sCoP;EAAE;AAC5V;AACA;EAAA,QAAAnE,SAAA,oBAAAA,SAAA,KA9sCoGxnB,EAAE,CAAAynB,iBAAA,CA8sCXyK,cAAc,EAAc,CAAC;IAC5GvpB,IAAI,EAAE/H,SAAS;IACf8mB,IAAI,EAAE,CAAC;MACCpf,QAAQ,EAAE,6BAA6B;MACvCwe,UAAU,EAAE,IAAI;MAChB8E,SAAS,EAAE,CAAC;QAAEH,OAAO,EAAEwG,gBAAgB;QAAEvG,WAAW,EAAEwG;MAAe,CAAC;IAC1E,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEvpB,IAAI,EAAE3I,EAAE,CAACsyB;EAAY,CAAC,CAAC,EAAkB;IAAE3F,IAAI,EAAE,CAAC;MACvEhkB,IAAI,EAAE5H;IACV,CAAC,CAAC;IAAE4L,SAAS,EAAE,CAAC;MACZhE,IAAI,EAAE5H,KAAK;MACX2mB,IAAI,EAAE,CAAC;QAAExjB,SAAS,EAAEvD;MAAiB,CAAC;IAC1C,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA,MAAM4xB,oBAAoB,GAAG,IAAI7xB,cAAc,CAAC,oBAAoB,CAAC;AACrE;AACA;AACA;AACA;AACA,MAAM8xB,kBAAkB,CAAC;EACrB1sB,WAAWA,CAACyoB,WAAW,EAAE;IACrB,IAAI,CAACA,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAAC4D,KAAK,GAAG/xB,MAAM,CAACmqB,eAAe,EAAE;MAAE6H,QAAQ,EAAE;IAAK,CAAC,CAAC;IACxD,IAAI,CAACD,KAAK,EAAEpE,uBAAuB,CAAC,IAAI,CAAC;EAC7C;EACA5E,WAAWA,CAAA,EAAG;IACV,IAAI,CAACgJ,KAAK,EAAEnE,yBAAyB,CAAC,IAAI,CAAC;EAC/C;EACA;IAAS,IAAI,CAACzH,IAAI,YAAAkM,2BAAAhM,CAAA;MAAA,YAAAA,CAAA,IAAwF+L,kBAAkB,EA/uC5BxyB,EAAE,CAAAirB,iBAAA,CA+uC4CjrB,EAAE,CAACsyB,WAAW;IAAA,CAA4C;EAAE;EAC1M;IAAS,IAAI,CAACnH,IAAI,kBAhvC8EnrB,EAAE,CAAAorB,iBAAA;MAAAziB,IAAA,EAgvCJ6pB,kBAAkB;MAAA5L,SAAA;MAAAyE,MAAA;QAAAsB,IAAA;MAAA;MAAA7F,UAAA;MAAAC,QAAA,GAhvChB/mB,EAAE,CAAAwrB,kBAAA,CAgvCsH,CAAC;QAAEC,OAAO,EAAE8G,oBAAoB;QAAE7G,WAAW,EAAE8G;MAAmB,CAAC,CAAC;IAAA,EAAiB;EAAE;AACnT;AACA;EAAA,QAAAhL,SAAA,oBAAAA,SAAA,KAlvCoGxnB,EAAE,CAAAynB,iBAAA,CAkvCX+K,kBAAkB,EAAc,CAAC;IAChH7pB,IAAI,EAAE/H,SAAS;IACf8mB,IAAI,EAAE,CAAC;MACCpf,QAAQ,EAAE,iCAAiC;MAC3Cwe,UAAU,EAAE,IAAI;MAChB8E,SAAS,EAAE,CAAC;QAAEH,OAAO,EAAE8G,oBAAoB;QAAE7G,WAAW,EAAE8G;MAAmB,CAAC;IAClF,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE7pB,IAAI,EAAE3I,EAAE,CAACsyB;EAAY,CAAC,CAAC,EAAkB;IAAE3F,IAAI,EAAE,CAAC;MACvEhkB,IAAI,EAAE5H;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM2xB,oBAAoB,GAAG,CACzBtC,WAAW,EACXJ,gBAAgB,EAChB/D,OAAO,EACPtB,aAAa,EACbuH,cAAc,EACdM,kBAAkB,CACrB;AACD,MAAMG,cAAc,CAAC;EACjB;IAAS,IAAI,CAACpM,IAAI,YAAAqM,uBAAAnM,CAAA;MAAA,YAAAA,CAAA,IAAwFkM,cAAc;IAAA,CAAkD;EAAE;EAC5K;IAAS,IAAI,CAACE,IAAI,kBAvwC8E7yB,EAAE,CAAA8yB,gBAAA;MAAAnqB,IAAA,EAuwCSgqB,cAAc;MAAAI,OAAA,GAAY3C,WAAW,EACxIJ,gBAAgB,EAChB/D,OAAO,EACPtB,aAAa,EACbuH,cAAc,EACdM,kBAAkB;MAAAQ,OAAA,GAAa1xB,mBAAmB,EAAE8uB,WAAW,EAC/DJ,gBAAgB,EAChB/D,OAAO,EACPtB,aAAa,EACbuH,cAAc,EACdM,kBAAkB;IAAA,EAAI;EAAE;EAChC;IAAS,IAAI,CAACS,IAAI,kBAlxC8EjzB,EAAE,CAAAkzB,gBAAA;MAAAtH,SAAA,EAkxCoC,CAAC1B,QAAQ,CAAC;MAAA6I,OAAA,GAAYzxB,mBAAmB;IAAA,EAAI;EAAE;AACzL;AACA;EAAA,QAAAkmB,SAAA,oBAAAA,SAAA,KApxCoGxnB,EAAE,CAAAynB,iBAAA,CAoxCXkL,cAAc,EAAc,CAAC;IAC5GhqB,IAAI,EAAExH,QAAQ;IACdumB,IAAI,EAAE,CAAC;MACCqL,OAAO,EAAEL,oBAAoB;MAC7BM,OAAO,EAAE,CAAC1xB,mBAAmB,EAAE,GAAGoxB,oBAAoB,CAAC;MACvD9G,SAAS,EAAE,CAAC1B,QAAQ;IACxB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAAS4B,eAAe,EAAEpB,eAAe,EAAEH,eAAe,EAAEgI,oBAAoB,EAAEN,gBAAgB,EAAEjG,aAAa,EAAE+D,mBAAmB,EAAE9D,OAAO,EAAEtB,aAAa,EAAE6H,kBAAkB,EAAEN,cAAc,EAAE9B,WAAW,EAAEJ,gBAAgB,EAAE9F,QAAQ,EAAEyI,cAAc,EAAE7K,gBAAgB,EAAEra,OAAO,EAAEsU,WAAW,EAAE1D,aAAa,EAAEZ,eAAe,EAAEO,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}