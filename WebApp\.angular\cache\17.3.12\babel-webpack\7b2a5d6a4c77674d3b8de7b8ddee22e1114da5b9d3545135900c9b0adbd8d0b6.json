{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/source/ai-hub/WebApp/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Subject } from 'rxjs';\n// import { HubConnection, HubConnectionBuilder } from '@microsoft/signalr';\nimport { tap } from 'rxjs/operators';\n// export interface ChatResponseDto {\n//   answer: string;\n//   sources: SourceDocumentDto[];\n// }\n// export interface SourceDocumentDto {\n//   id: number;\n//   title: string;\n// }\nlet NotesService = class NotesService {\n  constructor(http) {\n    // this.hubConnection = new HubConnectionBuilder()\n    //   .withUrl('https://localhost:44350/chatHub')  // Update with your API URL\n    //   .build();\n    this.http = http;\n    this.apiUrl = 'https://localhost:44350/api';\n    this.favoriteNotesChanged = new Subject();\n    this.recentNotesChanged = new Subject();\n    this.journalUpdated = new Subject();\n    // private sourcesSubject = new Subject<SourceDocumentDto[]>();\n    this.cancelSubject = new Subject();\n    this.startConnection();\n    this.addListeners();\n  }\n  startConnection() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        // await this.hubConnection.start();\n        console.log('SignalR Connected!');\n      } catch (err) {\n        console.error('SignalR Connection Error: ', err);\n        setTimeout(() => _this.startConnection(), 5000);\n      }\n    })();\n  }\n  addListeners() {\n    // Remove existing listeners before adding new ones\n    //   this.hubConnection.off('ReceiveMessage');\n    //   this.hubConnection.off('ReceiveSources');\n    //   this.hubConnection.off('CompleteChatResponse');\n    //   this.hubConnection.on('ReceiveMessage', (message: string) => {\n    //     console.log('SignalR received message:', message); // Debug log\n    //     if (this.currentMessageSubject) {\n    //       this.currentMessageSubject.next(message);\n    //     } else {\n    //       console.warn('No message subject available'); // Debug log\n    //     }\n    //   });\n    //   this.hubConnection.on('ReceiveSources', (sources: SourceDocumentDto[]) => {\n    //     console.log('SignalR received sources:', sources); // Debug log\n    //     this.sourcesSubject.next(sources);\n    //   });\n    //   this.hubConnection.on('CompleteChatResponse', () => {\n    //     console.log('SignalR chat response completed'); // Debug log\n    //     if (this.currentMessageSubject) {\n    //       this.currentMessageSubject.complete();\n    //     }\n    //     this.sourcesSubject.complete();\n    //   });\n  }\n  // Get all notes\n  getAllNotes() {\n    return this.http.get(`${this.apiUrl}/MyNotes/GetAll`);\n  }\n  // Get note by ID\n  getNoteById(id) {\n    return this.http.get(`${this.apiUrl}/MyNotes/GetById?id=${id}`);\n  }\n  // Create or update note\n  createOrUpdateNote(note) {\n    return this.http.post(`${this.apiUrl}/MyNotes/CreateOrUpdate`, note).pipe(tap(() => {\n      this.recentNotesChanged.next();\n    }));\n  }\n  // Delete note\n  deleteNote(id) {\n    return this.http.delete(`${this.apiUrl}/MyNotes/Delete?id=${id}`);\n  }\n  // Get favorite notes\n  getFavoriteNotes() {\n    return this.http.get(`${this.apiUrl}/MyNotes/GetFavouriteNotes`);\n  }\n  // Toggle favorite status\n  toggleFavorite(id) {\n    return this.http.post(`${this.apiUrl}/MyNotes/ToggleFavourite`, null, {\n      params: {\n        id: id.toString()\n      }\n    }).pipe(tap(() => {\n      this.favoriteNotesChanged.next();\n    }));\n  }\n  // Get chat response\n  getChatResponse(message, sessionId) {\n    // const connectionId = this.hubConnection.connectionId;\n    // if (!connectionId) {\n    //   throw new Error('SignalR connection not established');\n    // }\n    // console.log('Starting chat response with connectionId:', connectionId); // Debug log\n    // // Create new subjects for this conversation\n    // this.currentMessageSubject = new Subject<string>();\n    // this.sourcesSubject = new Subject<SourceDocumentDto[]>();\n    // this.cancelSubject = new Subject<void>();\n    // // Re-add listeners for this conversation\n    // this.addListeners();\n    // // Make the HTTP request\n    // this.http.get(`${this.apiUrl}/Ai/chat-response`, {\n    //   params: { message, sessionId, connectionId }\n    // })\n    // .pipe(\n    //   takeUntil(this.cancelSubject)\n    // )\n    // .subscribe({\n    //   error: (error) => {\n    //     console.error('HTTP request error:', error);\n    //     this.currentMessageSubject?.error(error);\n    //     this.sourcesSubject.error(error);\n    //   },\n    //   complete: () => {\n    //     console.log('HTTP request completed');\n    //   }\n    // });\n    // return {\n    //   messages: this.currentMessageSubject.asObservable(),\n    //   sources: this.sourcesSubject.asObservable(),\n    //   stop: () => {\n    //     console.log('Stopping chat response');\n    //     this.cancelSubject.next();\n    //     this.cancelSubject.complete();\n    //   }\n    // };\n  }\n  // Get recent notes\n  getRecentNotes(limit) {\n    return this.http.get(`${this.apiUrl}/MyNotes/recent`, {\n      params: {\n        limit: limit.toString()\n      }\n    });\n  }\n  // Update recently opened\n  updateRecentlyOpened(id) {\n    return this.http.post(`${this.apiUrl}/MyNotes/UpdateRecentlyOpened`, null, {\n      params: {\n        id: id.toString()\n      }\n    });\n  }\n  // Search notes\n  searchNotes(query) {\n    return this.http.get(`${this.apiUrl}/MyNotes/SearchNotes`, {\n      params: {\n        searchTerm: query\n      }\n    });\n  }\n  // Get all journal documents\n  getAllJournalDocs() {\n    return this.http.get(`${this.apiUrl}/MyNotes/GetAllJournalDocs`);\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: HttpClient\n    }];\n  }\n};\nNotesService = __decorate([Injectable({\n  providedIn: 'root'\n})], NotesService);\nexport { NotesService };", "map": {"version": 3, "names": ["Injectable", "HttpClient", "Subject", "tap", "NotesService", "constructor", "http", "apiUrl", "favoriteNotesChanged", "recentNotesChanged", "journalUpdated", "cancelSubject", "startConnection", "addListeners", "_this", "_asyncToGenerator", "console", "log", "err", "error", "setTimeout", "getAllNotes", "get", "getNoteById", "id", "createOrUpdateNote", "note", "post", "pipe", "next", "deleteNote", "delete", "getFavoriteNotes", "toggleFavorite", "params", "toString", "getChatResponse", "message", "sessionId", "getRecentNotes", "limit", "updateRecentlyOpened", "searchNotes", "query", "searchTerm", "getAllJournalDocs", "__decorate", "providedIn"], "sources": ["C:\\Users\\<USER>\\source\\ai-hub\\WebApp\\src\\app\\MyNotesProjects\\services\\notes.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { Observable, Subject, takeUntil } from 'rxjs';\r\n// import { HubConnection, HubConnectionBuilder } from '@microsoft/signalr';\r\nimport { tap } from 'rxjs/operators';\r\nimport { BehaviorSubject } from 'rxjs';\r\n\r\nexport interface Note {\r\n  id: number;\r\n  title: string;\r\n  content: any;\r\n  createdAt: Date;\r\n  isFavourite: boolean;\r\n  isJournal: boolean;\r\n\r\n}\r\n\r\n// export interface ChatResponseDto {\r\n//   answer: string;\r\n//   sources: SourceDocumentDto[];\r\n// }\r\n\r\n// export interface SourceDocumentDto {\r\n//   id: number;\r\n//   title: string;\r\n// }\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\n\r\nexport class NotesService {\r\n  private apiUrl = 'https://localhost:44350/api';\r\n  favoriteNotesChanged = new Subject<void>();\r\n  recentNotesChanged = new Subject<void>();\r\n  journalUpdated = new Subject<void>();\r\n  // private hubConnection: HubConnection;\r\n  private currentMessageSubject?: Subject<string>;\r\n  // private sourcesSubject = new Subject<SourceDocumentDto[]>();\r\n  private cancelSubject = new Subject<void>();\r\n\r\n  constructor(private http: HttpClient) {\r\n    // this.hubConnection = new HubConnectionBuilder()\r\n    //   .withUrl('https://localhost:44350/chatHub')  // Update with your API URL\r\n    //   .build();\r\n\r\n    this.startConnection();\r\n    this.addListeners();\r\n  }\r\n\r\n  private async startConnection() {\r\n    try {\r\n      // await this.hubConnection.start();\r\n      console.log('SignalR Connected!');\r\n    } catch (err) {\r\n      console.error('SignalR Connection Error: ', err);\r\n      setTimeout(() => this.startConnection(), 5000);\r\n    }\r\n  }\r\n\r\n  private addListeners() {\r\n    // Remove existing listeners before adding new ones\r\n    //   this.hubConnection.off('ReceiveMessage');\r\n    //   this.hubConnection.off('ReceiveSources');\r\n    //   this.hubConnection.off('CompleteChatResponse');\r\n\r\n    //   this.hubConnection.on('ReceiveMessage', (message: string) => {\r\n    //     console.log('SignalR received message:', message); // Debug log\r\n    //     if (this.currentMessageSubject) {\r\n    //       this.currentMessageSubject.next(message);\r\n    //     } else {\r\n    //       console.warn('No message subject available'); // Debug log\r\n    //     }\r\n    //   });\r\n\r\n    //   this.hubConnection.on('ReceiveSources', (sources: SourceDocumentDto[]) => {\r\n    //     console.log('SignalR received sources:', sources); // Debug log\r\n    //     this.sourcesSubject.next(sources);\r\n    //   });\r\n\r\n    //   this.hubConnection.on('CompleteChatResponse', () => {\r\n    //     console.log('SignalR chat response completed'); // Debug log\r\n    //     if (this.currentMessageSubject) {\r\n    //       this.currentMessageSubject.complete();\r\n    //     }\r\n    //     this.sourcesSubject.complete();\r\n    //   });\r\n  }\r\n\r\n  // Get all notes\r\n  getAllNotes(): Observable<Note[]> {\r\n    return this.http.get<Note[]>(`${this.apiUrl}/MyNotes/GetAll`);\r\n  }\r\n\r\n  // Get note by ID\r\n  getNoteById(id: number): Observable<Note> {\r\n    return this.http.get<Note>(`${this.apiUrl}/MyNotes/GetById?id=${id}`);\r\n  }\r\n\r\n  // Create or update note\r\n  createOrUpdateNote(note: Partial<Note>): Observable<Note> {\r\n    return this.http.post<Note>(`${this.apiUrl}/MyNotes/CreateOrUpdate`, note).pipe(\r\n      tap(() => {\r\n        this.recentNotesChanged.next();\r\n      })\r\n    );\r\n  }\r\n\r\n  // Delete note\r\n  deleteNote(id: number): Observable<any> {\r\n    return this.http.delete(`${this.apiUrl}/MyNotes/Delete?id=${id}`);\r\n  }\r\n\r\n  // Get favorite notes\r\n  getFavoriteNotes(): Observable<Note[]> {\r\n    return this.http.get<Note[]>(`${this.apiUrl}/MyNotes/GetFavouriteNotes`);\r\n  }\r\n\r\n  // Toggle favorite status\r\n  toggleFavorite(id: number): Observable<Note> {\r\n    return this.http.post<Note>(`${this.apiUrl}/MyNotes/ToggleFavourite`, null, {\r\n      params: { id: id.toString() }\r\n    }).pipe(\r\n      tap(() => {\r\n        this.favoriteNotesChanged.next();\r\n      })\r\n    );\r\n  }\r\n\r\n  // Get chat response\r\n  getChatResponse(message: string, sessionId: string) {\r\n    // const connectionId = this.hubConnection.connectionId;\r\n    // if (!connectionId) {\r\n    //   throw new Error('SignalR connection not established');\r\n    // }\r\n\r\n    // console.log('Starting chat response with connectionId:', connectionId); // Debug log\r\n\r\n    // // Create new subjects for this conversation\r\n    // this.currentMessageSubject = new Subject<string>();\r\n    // this.sourcesSubject = new Subject<SourceDocumentDto[]>();\r\n    // this.cancelSubject = new Subject<void>();\r\n\r\n    // // Re-add listeners for this conversation\r\n    // this.addListeners();\r\n\r\n    // // Make the HTTP request\r\n    // this.http.get(`${this.apiUrl}/Ai/chat-response`, {\r\n    //   params: { message, sessionId, connectionId }\r\n    // })\r\n    // .pipe(\r\n    //   takeUntil(this.cancelSubject)\r\n    // )\r\n    // .subscribe({\r\n    //   error: (error) => {\r\n    //     console.error('HTTP request error:', error);\r\n    //     this.currentMessageSubject?.error(error);\r\n    //     this.sourcesSubject.error(error);\r\n    //   },\r\n    //   complete: () => {\r\n    //     console.log('HTTP request completed');\r\n    //   }\r\n    // });\r\n\r\n    // return {\r\n    //   messages: this.currentMessageSubject.asObservable(),\r\n    //   sources: this.sourcesSubject.asObservable(),\r\n    //   stop: () => {\r\n    //     console.log('Stopping chat response');\r\n    //     this.cancelSubject.next();\r\n    //     this.cancelSubject.complete();\r\n    //   }\r\n    // };\r\n  }\r\n\r\n  // Get recent notes\r\n  getRecentNotes(limit: number): Observable<Note[]> {\r\n    return this.http.get<Note[]>(`${this.apiUrl}/MyNotes/recent`, {\r\n      params: { limit: limit.toString() }\r\n    });\r\n  }\r\n\r\n  // Update recently opened\r\n  updateRecentlyOpened(id: number): Observable<Note> {\r\n    return this.http.post<Note>(`${this.apiUrl}/MyNotes/UpdateRecentlyOpened`, null, {\r\n      params: { id: id.toString() }\r\n    });\r\n  }\r\n\r\n  // Search notes\r\n  searchNotes(query: string): Observable<Note[]> {\r\n    return this.http.get<Note[]>(`${this.apiUrl}/MyNotes/SearchNotes`, {\r\n      params: { searchTerm: query }\r\n    });\r\n  }\r\n\r\n  // Get all journal documents\r\n  getAllJournalDocs(): Observable<Note[]> {\r\n    return this.http.get<Note[]>(`${this.apiUrl}/MyNotes/GetAllJournalDocs`);\r\n  }\r\n}\r\n"], "mappings": ";;AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SAAqBC,OAAO,QAAmB,MAAM;AACrD;AACA,SAASC,GAAG,QAAQ,gBAAgB;AAapC;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAKO,IAAMC,YAAY,GAAlB,MAAMA,YAAY;EAUvBC,YAAoBC,IAAgB;IAClC;IACA;IACA;IAHkB,KAAAA,IAAI,GAAJA,IAAI;IAThB,KAAAC,MAAM,GAAG,6BAA6B;IAC9C,KAAAC,oBAAoB,GAAG,IAAIN,OAAO,EAAQ;IAC1C,KAAAO,kBAAkB,GAAG,IAAIP,OAAO,EAAQ;IACxC,KAAAQ,cAAc,GAAG,IAAIR,OAAO,EAAQ;IAGpC;IACQ,KAAAS,aAAa,GAAG,IAAIT,OAAO,EAAQ;IAOzC,IAAI,CAACU,eAAe,EAAE;IACtB,IAAI,CAACC,YAAY,EAAE;EACrB;EAEcD,eAAeA,CAAA;IAAA,IAAAE,KAAA;IAAA,OAAAC,iBAAA;MAC3B,IAAI;QACF;QACAC,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;OAClC,CAAC,OAAOC,GAAG,EAAE;QACZF,OAAO,CAACG,KAAK,CAAC,4BAA4B,EAAED,GAAG,CAAC;QAChDE,UAAU,CAAC,MAAMN,KAAI,CAACF,eAAe,EAAE,EAAE,IAAI,CAAC;;IAC/C;EACH;EAEQC,YAAYA,CAAA;IAClB;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;EAAA;EAGF;EACAQ,WAAWA,CAAA;IACT,OAAO,IAAI,CAACf,IAAI,CAACgB,GAAG,CAAS,GAAG,IAAI,CAACf,MAAM,iBAAiB,CAAC;EAC/D;EAEA;EACAgB,WAAWA,CAACC,EAAU;IACpB,OAAO,IAAI,CAAClB,IAAI,CAACgB,GAAG,CAAO,GAAG,IAAI,CAACf,MAAM,uBAAuBiB,EAAE,EAAE,CAAC;EACvE;EAEA;EACAC,kBAAkBA,CAACC,IAAmB;IACpC,OAAO,IAAI,CAACpB,IAAI,CAACqB,IAAI,CAAO,GAAG,IAAI,CAACpB,MAAM,yBAAyB,EAAEmB,IAAI,CAAC,CAACE,IAAI,CAC7EzB,GAAG,CAAC,MAAK;MACP,IAAI,CAACM,kBAAkB,CAACoB,IAAI,EAAE;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACAC,UAAUA,CAACN,EAAU;IACnB,OAAO,IAAI,CAAClB,IAAI,CAACyB,MAAM,CAAC,GAAG,IAAI,CAACxB,MAAM,sBAAsBiB,EAAE,EAAE,CAAC;EACnE;EAEA;EACAQ,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAAC1B,IAAI,CAACgB,GAAG,CAAS,GAAG,IAAI,CAACf,MAAM,4BAA4B,CAAC;EAC1E;EAEA;EACA0B,cAAcA,CAACT,EAAU;IACvB,OAAO,IAAI,CAAClB,IAAI,CAACqB,IAAI,CAAO,GAAG,IAAI,CAACpB,MAAM,0BAA0B,EAAE,IAAI,EAAE;MAC1E2B,MAAM,EAAE;QAAEV,EAAE,EAAEA,EAAE,CAACW,QAAQ;MAAE;KAC5B,CAAC,CAACP,IAAI,CACLzB,GAAG,CAAC,MAAK;MACP,IAAI,CAACK,oBAAoB,CAACqB,IAAI,EAAE;IAClC,CAAC,CAAC,CACH;EACH;EAEA;EACAO,eAAeA,CAACC,OAAe,EAAEC,SAAiB;IAChD;IACA;IACA;IACA;IAEA;IAEA;IACA;IACA;IACA;IAEA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EAAA;EAGF;EACAC,cAAcA,CAACC,KAAa;IAC1B,OAAO,IAAI,CAAClC,IAAI,CAACgB,GAAG,CAAS,GAAG,IAAI,CAACf,MAAM,iBAAiB,EAAE;MAC5D2B,MAAM,EAAE;QAAEM,KAAK,EAAEA,KAAK,CAACL,QAAQ;MAAE;KAClC,CAAC;EACJ;EAEA;EACAM,oBAAoBA,CAACjB,EAAU;IAC7B,OAAO,IAAI,CAAClB,IAAI,CAACqB,IAAI,CAAO,GAAG,IAAI,CAACpB,MAAM,+BAA+B,EAAE,IAAI,EAAE;MAC/E2B,MAAM,EAAE;QAAEV,EAAE,EAAEA,EAAE,CAACW,QAAQ;MAAE;KAC5B,CAAC;EACJ;EAEA;EACAO,WAAWA,CAACC,KAAa;IACvB,OAAO,IAAI,CAACrC,IAAI,CAACgB,GAAG,CAAS,GAAG,IAAI,CAACf,MAAM,sBAAsB,EAAE;MACjE2B,MAAM,EAAE;QAAEU,UAAU,EAAED;MAAK;KAC5B,CAAC;EACJ;EAEA;EACAE,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAACvC,IAAI,CAACgB,GAAG,CAAS,GAAG,IAAI,CAACf,MAAM,4BAA4B,CAAC;EAC1E;;;;;;;AAxKWH,YAAY,GAAA0C,UAAA,EAJxB9C,UAAU,CAAC;EACV+C,UAAU,EAAE;CACb,CAAC,C,EAEW3C,YAAY,CAyKxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}