{"ast": null, "code": "import { asyncScheduler } from '../scheduler/async';\nimport { delayWhen } from './delayWhen';\nimport { timer } from '../observable/timer';\nexport function delay(due, scheduler = asyncScheduler) {\n  const duration = timer(due, scheduler);\n  return delayWhen(() => duration);\n}", "map": {"version": 3, "names": ["asyncScheduler", "<PERSON><PERSON>hen", "timer", "delay", "due", "scheduler", "duration"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/rxjs/dist/esm/internal/operators/delay.js"], "sourcesContent": ["import { asyncScheduler } from '../scheduler/async';\nimport { delayWhen } from './delayWhen';\nimport { timer } from '../observable/timer';\nexport function delay(due, scheduler = asyncScheduler) {\n    const duration = timer(due, scheduler);\n    return delayWhen(() => duration);\n}\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,oBAAoB;AACnD,SAASC,SAAS,QAAQ,aAAa;AACvC,SAASC,KAAK,QAAQ,qBAAqB;AAC3C,OAAO,SAASC,KAAKA,CAACC,GAAG,EAAEC,SAAS,GAAGL,cAAc,EAAE;EACnD,MAAMM,QAAQ,GAAGJ,KAAK,CAACE,GAAG,EAAEC,SAAS,CAAC;EACtC,OAAOJ,SAAS,CAAC,MAAMK,QAAQ,CAAC;AACpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}