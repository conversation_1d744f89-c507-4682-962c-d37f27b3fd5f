{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/source/ai-hub/WebApp/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./conection.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"C:/Users/<USER>/source/ai-hub/WebApp/src/app/admin/conection/conection.component.ts.css?ngResource!=!C:\\\\Users\\\\<USER>\\\\source\\\\ai-hub\\\\WebApp\\\\node_modules\\\\@ngtools\\\\webpack\\\\src\\\\loaders\\\\inline-resource.js?data=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%2BIGRpdiB7CiAgICAgIGJhY2tncm91bmQtY29sb3I6IHZhcigtLWhvdmVyLWJsdWUtZ3JheSk7CiAgICAgIGJvcmRlci1jb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpOwogICAgfQoKICAgIDpob3N0LWNvbnRleHQoLmRhcmspIC5ncmlkID4gZGl2OmhvdmVyIHsKICAgICAgYm94LXNoYWRvdzogMCAxMHB4IDE1cHggLTNweCByZ2JhKDAsIDAsIDAsIDAuNCksIDAgNHB4IDZweCAtMnB4IHJnYmEoMCwgMCwgMCwgMC4yKTsKICAgIH0KCiAgICA6aG9zdC1jb250ZXh0KC5kYXJrKSAuZ3JpZCA%2BIGRpdiAuYm9yZGVyLWIsCiAgICA6aG9zdC1jb250ZXh0KC5kYXJrKSAuZ3JpZCA%2BIGRpdiAuYm9yZGVyLXQgewogICAgICBib3JkZXItY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKTsKICAgIH0KCiAgICA6aG9zdC1jb250ZXh0KC5kYXJrKSAuZ3JpZCA%2BIGRpdiAuYmctW3ZhcigtLWhvdmVyLWJsdWUtZ3JheSldIHsKICAgICAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgwLCAwLCAwLCAwLjIpOwogICAgfQoKICAgIC8qIFRvb2x0aXAgc3R5bGVzICovCiAgICAudG9vbHRpcCB7CiAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTsKICAgIH0KCiAgICAudG9vbHRpcCAudG9vbHRpcC10ZXh0IHsKICAgICAgdmlzaWJpbGl0eTogaGlkZGVuOwogICAgICB3aWR0aDogMTIwcHg7CiAgICAgIGJhY2tncm91bmQtY29sb3I6ICMzMzM7CiAgICAgIGNvbG9yOiAjZmZmOwogICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgICAgIGJvcmRlci1yYWRpdXM6IDZweDsKICAgICAgcGFkZGluZzogNXB4OwogICAgICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgICAgIHotaW5kZXg6IDE7CiAgICAgIGJvdHRvbTogMTI1JTsKICAgICAgbGVmdDogNTAlOwogICAgICBtYXJnaW4tbGVmdDogLTYwcHg7CiAgICAgIG9wYWNpdHk6IDA7CiAgICAgIHRyYW5zaXRpb246IG9wYWNpdHkgMC4zczsKICAgIH0KCiAgICAudG9vbHRpcDpob3ZlciAudG9vbHRpcC10ZXh0IHsKICAgICAgdmlzaWJpbGl0eTogdmlzaWJsZTsKICAgICAgb3BhY2l0eTogMTsKICAgIH0KCiAgICAvKiBSZXNwb25zaXZlIHN0eWxlcyAqLwogICAgQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7CiAgICAgIC5hY3Rpb24tYnV0dG9uIHsKICAgICAgICBvcGFjaXR5OiAxOwogICAgICB9CiAgICB9CiAg!C:/Users/<USER>/source/ai-hub/WebApp/src/app/admin/conection/conection.component.ts\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ApiCredentialsServiceProxy, ModelDetailsServiceProxy } from '../../../shared/service-proxies/service-proxies';\nimport { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';\nimport { FormsModule } from '@angular/forms';\nimport { NzMessageService } from 'ng-zorro-antd/message';\nimport { finalize } from 'rxjs/operators';\nimport { SpinnerComponent } from '../../shared/components/spinner/spinner.component';\nlet ConnectionsComponent = class ConnectionsComponent {\n  constructor(apiCredentialsService, nzMessageService, modelDetailsService) {\n    this.apiCredentialsService = apiCredentialsService;\n    this.nzMessageService = nzMessageService;\n    this.modelDetailsService = modelDetailsService;\n    this.apiLists = [];\n    this.filteredApis = []; // For search functionality\n    this.searchQuery = ''; // For search input\n    this.isLoading = false; // Loading state for spinner\n    this.apiData = {\n      tokenUrl: '',\n      apiKey: '',\n      hasCustomModels: false,\n      customModels: [],\n      customModelsText: ''\n    };\n    // For No API toggle\n    this.noApiMode = false;\n    // For editing custom models\n    this.editCustomModelsData = {\n      id: '',\n      customModels: [],\n      customModelsText: ''\n    };\n    this.showEditCustomModelsForm = false;\n    this.showForm = false;\n    this.isCredentialsValid = false;\n    this.isValidating = false;\n    this.isTokenUrlValid = false;\n    this.validationMessage = '';\n    this.isResyncingAll = false;\n    this.isResyncing = {};\n  }\n  ngOnInit() {\n    this.loadApiCredentials();\n  }\n  loadApiCredentials() {\n    this.isLoading = true; // Show spinner\n    this.apiCredentialsService.getAll().subscribe({\n      next: res => {\n        if (res) {\n          this.apiLists = res;\n          // Apply current search filter if exists, otherwise show all\n          if (this.searchQuery) {\n            this.filterApis();\n          } else {\n            this.filteredApis = [...this.apiLists]; // Initialize filtered list with all APIs\n          }\n        }\n        this.isLoading = false; // Hide spinner\n      },\n      error: error => {\n        console.error('Error loading API credentials:', error);\n        this.isLoading = false; // Hide spinner on error\n      }\n    });\n  }\n  /**\n   * Filter APIs based on search query\n   */\n  filterApis() {\n    if (!this.searchQuery) {\n      this.filteredApis = [...this.apiLists];\n      return;\n    }\n    const query = this.searchQuery.toLowerCase();\n    this.filteredApis = this.apiLists.filter(api => api.tokenUrl?.toLowerCase().includes(query) || (api.tokenUrl?.split('.')[1]?.toLowerCase() || '').includes(query));\n  }\n  /**\n   * Clear search and reset filtered APIs\n   */\n  clearSearch() {\n    this.searchQuery = '';\n    this.filteredApis = [...this.apiLists];\n  }\n  onAddApi() {\n    this.showForm = true;\n  }\n  validateCredentials() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      // Reset validation state\n      _this.isValidating = true;\n      _this.isCredentialsValid = false;\n      _this.validationMessage = '';\n      // Validate token URL first\n      if (!_this.apiData.tokenUrl || _this.apiData.tokenUrl.trim() === '') {\n        _this.isValidating = false;\n        _this.validationMessage = 'Token URL is required';\n        _this.nzMessageService.error(_this.validationMessage);\n        return;\n      }\n      // Basic URL validation\n      try {\n        // Check if it's a valid URL format\n        new URL(_this.apiData.tokenUrl);\n        _this.isTokenUrlValid = true;\n      } catch (e) {\n        _this.isValidating = false;\n        _this.isTokenUrlValid = false;\n        _this.validationMessage = 'Please enter a valid URL';\n        _this.nzMessageService.error(_this.validationMessage);\n        return;\n      }\n      // Always check validation first\n      if (!_this.apiData.tokenUrl || _this.apiData.tokenUrl.trim() === '') {\n        _this.isValidating = false;\n        _this.validationMessage = 'Token URL is required';\n        _this.nzMessageService.error(_this.validationMessage);\n        return;\n      }\n      // Validate API key for normal mode\n      if (!_this.apiData.apiKey || _this.apiData.apiKey.trim() === '') {\n        _this.isValidating = false;\n        _this.validationMessage = 'API Key is required';\n        _this.nzMessageService.error(_this.validationMessage);\n        return;\n      }\n      // Show loading message and validate with API\n      // const loadingMessage = this.nzMessageService.loading('Validating credentials...');\n      try {\n        let res = yield _this.apiCredentialsService.validate(_this.apiData).toPromise();\n        _this.isValidating = false;\n        if (!res.isError) {\n          _this.isCredentialsValid = true;\n          _this.validationMessage = 'Credentials validated successfully';\n          _this.nzMessageService.success(_this.validationMessage);\n        } else {\n          _this.isCredentialsValid = false;\n          _this.validationMessage = res.message || 'Invalid credentials';\n          _this.nzMessageService.error(_this.validationMessage);\n        }\n      } catch (error) {\n        _this.isValidating = false;\n        _this.isCredentialsValid = false;\n        _this.validationMessage = error.message || 'Validation failed';\n        _this.nzMessageService.error(_this.validationMessage);\n      }\n    })();\n  }\n  saveApi() {\n    // Double-check validation before saving\n    if (!this.isCredentialsValid) {\n      this.nzMessageService.error('Please validate your credentials before saving');\n      return;\n    }\n    // Show loading message\n    const loadingMessage = this.nzMessageService.loading('Saving API credentials...');\n    this.apiCredentialsService.create(this.apiData).subscribe({\n      next: res => {\n        if (res) {\n          this.apiLists.push(this.apiData);\n          this.filteredApis = [...this.apiLists]; // Update filtered list\n          this.nzMessageService.success(res.message || 'API credentials saved successfully');\n          this.resetForm();\n        }\n      },\n      error: error => {\n        this.nzMessageService.error(error.message || 'Failed to save API credentials');\n      },\n      complete: () => {\n        // Remove loading message\n        this.nzMessageService.remove(loadingMessage.messageId);\n      }\n    });\n  }\n  searchName(url) {\n    const providers = {\n      'azure': 'Azure OpenAI',\n      'openai': 'OpenAI',\n      'googleapis': 'Google APIs'\n    };\n    for (const [key, value] of Object.entries(providers)) {\n      if (url.includes(key)) {\n        return value;\n      }\n    }\n    return 'Local';\n  }\n  deleteApi(api) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        let res = yield _this2.apiCredentialsService.delete(api.id).toPromise();\n        if (!res.isError) {\n          _this2.apiLists = _this2.apiLists.filter(item => item.id != api.id);\n          _this2.filteredApis = _this2.filteredApis.filter(item => item.id != api.id); // Update filtered list\n          _this2.nzMessageService.success(res.message);\n        }\n      } catch (error) {\n        _this2.nzMessageService.error('Cannot delete credentials; remove linked models in Agent first');\n      }\n    })();\n  }\n  resetForm() {\n    this.apiData = {\n      tokenUrl: '',\n      apiKey: '',\n      hasCustomModels: false,\n      customModels: [],\n      customModelsText: ''\n    };\n    this.isCredentialsValid = false;\n    this.isTokenUrlValid = false;\n    this.validationMessage = '';\n    this.isValidating = false;\n    this.showForm = false;\n    this.noApiMode = false;\n  }\n  /**\n   * Toggle No API mode and set API key accordingly\n   */\n  toggleNoApiMode() {\n    if (this.noApiMode) {\n      // Set API key to \"no api\" when toggle is enabled\n      this.apiData.apiKey = \"no api\";\n      // Only auto-validate if token URL is valid\n      if (this.apiData.tokenUrl && this.isTokenUrlValid) {\n        this.validationMessage = 'No API mode is valid';\n      } else {\n        // Token URL still needs validation\n        this.isCredentialsValid = false;\n      }\n    } else {\n      // Clear API key when toggle is disabled\n      this.apiData.apiKey = \"\";\n      this.isCredentialsValid = false;\n      this.validationMessage = '';\n    }\n  }\n  /**\n   * Update custom models array from text input\n   */\n  updateCustomModels() {\n    if (!this.apiData.customModelsText) {\n      this.apiData.customModels = [];\n      return;\n    }\n    // Split by newline and filter out empty lines\n    this.apiData.customModels = this.apiData.customModelsText.split('\\n').map(model => model.trim()).filter(model => model.length > 0);\n  }\n  /**\n   * Update edit custom models array from text input\n   */\n  updateEditCustomModels() {\n    if (!this.editCustomModelsData.customModelsText) {\n      this.editCustomModelsData.customModels = [];\n      return;\n    }\n    // Split by newline and filter out empty lines\n    this.editCustomModelsData.customModels = this.editCustomModelsData.customModelsText.split('\\n').map(model => model.trim()).filter(model => model.length > 0);\n  }\n  /**\n   * Open the edit custom models form\n   */\n  onEditCustomModels(api) {\n    this.editCustomModelsData = {\n      id: api.id,\n      customModels: [],\n      customModelsText: ''\n    };\n    // Get existing models for this connection\n    this.apiCredentialsService.getById(api.id).subscribe(credentials => {\n      if (credentials) {\n        // Get models for this provider\n        this.modelDetailsService.getByApiCredentialsId(api.id).subscribe(models => {\n          if (models && models.length > 0) {\n            // Extract model names and join with newlines\n            const modelNames = models.map(model => this.removeProvider(model.modelName));\n            this.editCustomModelsData.customModelsText = modelNames.join('\\n');\n            this.updateEditCustomModels();\n            this.showEditCustomModelsForm = true;\n          } else {\n            this.nzMessageService.error('No models found for this connection');\n          }\n        });\n      }\n    });\n  }\n  removeProvider(model) {\n    const parts = model.split('_');\n    return parts.slice(1).join('_');\n  }\n  /**\n   * Save updated custom models\n   */\n  saveEditCustomModels() {\n    if (this.editCustomModelsData.customModels.length === 0) {\n      this.nzMessageService.error('Please enter at least one model name');\n      return;\n    }\n    const loadingMessage = this.nzMessageService.loading('Saving custom models...');\n    this.apiCredentialsService.addCustomModels(this.editCustomModelsData.id, this.editCustomModelsData.customModels).subscribe({\n      next: response => {\n        this.nzMessageService.remove(loadingMessage.messageId);\n        if (!response.isError) {\n          this.nzMessageService.success(response.message || 'Custom models updated successfully');\n          this.resetEditCustomModelsForm();\n          // Refresh the API list to reflect any changes\n          this.loadApiCredentials();\n        } else {\n          this.nzMessageService.error(response.message || 'Failed to update custom models');\n        }\n      },\n      error: error => {\n        this.nzMessageService.remove(loadingMessage.messageId);\n        this.nzMessageService.error('Error updating custom models: ' + (error.message || 'Unknown error'));\n      }\n    });\n  }\n  /**\n   * Reset the edit custom models form\n   */\n  resetEditCustomModelsForm() {\n    this.editCustomModelsData = {\n      id: '',\n      customModels: [],\n      customModelsText: ''\n    };\n    this.showEditCustomModelsForm = false;\n  }\n  /**\n   * Resync models for a specific API connection\n   */\n  resyncModels(api) {\n    if (this.isResyncing[api.id]) {\n      return; // Prevent multiple clicks\n    }\n    this.isResyncing[api.id] = true;\n    const loadingMessage = this.nzMessageService.loading('Resyncing models...');\n    this.apiCredentialsService.resyncModels(api.id).pipe(finalize(() => {\n      this.isResyncing[api.id] = false;\n    })).subscribe({\n      next: response => {\n        this.nzMessageService.remove(loadingMessage.messageId);\n        if (!response.isError) {\n          this.nzMessageService.success(response.message || 'Models resynced successfully');\n          // Refresh the API list to reflect any changes\n          this.loadApiCredentials();\n          // The search will be maintained as loadApiCredentials calls filterApis\n        } else {\n          this.nzMessageService.error(response.message || 'Failed to resync models');\n        }\n      },\n      error: error => {\n        this.nzMessageService.remove(loadingMessage.messageId);\n        this.nzMessageService.error('Error resyncing models: ' + (error.message || 'Unknown error'));\n      }\n    });\n  }\n  /**\n   * Resync models for all API connections\n   */\n  resyncAllModels() {\n    if (this.isResyncingAll) {\n      return; // Prevent multiple clicks\n    }\n    this.isResyncingAll = true;\n    const loadingMessage = this.nzMessageService.loading('Resyncing all models...');\n    this.apiCredentialsService.resyncAllModels().pipe(finalize(() => {\n      this.isResyncingAll = false;\n    })).subscribe({\n      next: response => {\n        this.nzMessageService.remove(loadingMessage.messageId);\n        if (!response.isError) {\n          this.nzMessageService.success(response.message || 'All models resynced successfully');\n          // Refresh the API list to reflect any changes\n          this.loadApiCredentials();\n          // The search will be maintained as loadApiCredentials calls filterApis\n        } else {\n          this.nzMessageService.error(response.message || 'Failed to resync all models');\n        }\n      },\n      error: error => {\n        this.nzMessageService.remove(loadingMessage.messageId);\n        this.nzMessageService.error('Error resyncing all models: ' + (error.message || 'Unknown error'));\n      }\n    });\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: ApiCredentialsServiceProxy\n    }, {\n      type: NzMessageService\n    }, {\n      type: ModelDetailsServiceProxy\n    }];\n  }\n};\nConnectionsComponent = __decorate([Component({\n  selector: 'app-connections-settings',\n  standalone: true,\n  imports: [CommonModule, ServiceProxyModule, FormsModule, SpinnerComponent],\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], ConnectionsComponent);\nexport { ConnectionsComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "ApiCredentialsServiceProxy", "ModelDetailsServiceProxy", "ServiceProxyModule", "FormsModule", "NzMessageService", "finalize", "SpinnerComponent", "ConnectionsComponent", "constructor", "apiCredentialsService", "nzMessageService", "modelDetailsService", "apiLists", "filteredApis", "searchQuery", "isLoading", "apiData", "tokenUrl", "<PERSON><PERSON><PERSON><PERSON>", "hasCustomModels", "customModels", "customModelsText", "noApiMode", "editCustomModelsData", "id", "showEditCustomModelsForm", "showForm", "isCredentialsValid", "isValidating", "isTokenUrlValid", "validationMessage", "isResyncingAll", "isResyncing", "ngOnInit", "loadApiCredentials", "getAll", "subscribe", "next", "res", "filterApis", "error", "console", "query", "toLowerCase", "filter", "api", "includes", "split", "clearSearch", "onAddApi", "validateCredentials", "_this", "_asyncToGenerator", "trim", "URL", "e", "validate", "to<PERSON>romise", "isError", "success", "message", "saveApi", "loadingMessage", "loading", "create", "push", "resetForm", "complete", "remove", "messageId", "searchName", "url", "providers", "key", "value", "Object", "entries", "deleteApi", "_this2", "delete", "item", "toggleNoApiMode", "updateCustomModels", "map", "model", "length", "updateEditCustomModels", "onEditCustomModels", "getById", "credentials", "getByApiCredentialsId", "models", "modelNames", "removeProvider", "modelName", "join", "parts", "slice", "saveEditCustomModels", "addCustomModels", "response", "resetEditCustomModelsForm", "resyncModels", "pipe", "resyncAllModels", "__decorate", "selector", "standalone", "imports", "template", "__NG_CLI_RESOURCE__0"], "sources": ["C:\\Users\\<USER>\\source\\ai-hub\\WebApp\\src\\app\\admin\\conection\\conection.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { ApiCredentialsServiceProxy, ModelDetailsServiceProxy, ResponseMessage } from '../../../shared/service-proxies/service-proxies';\r\nimport { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { NzMessageService } from 'ng-zorro-antd/message';\r\nimport { concatMap, finalize } from 'rxjs/operators';\r\nimport { SpinnerComponent } from '../../shared/components/spinner/spinner.component';\r\n\r\n@Component({\r\n  selector: 'app-connections-settings',\r\n  standalone: true,\r\n  imports: [CommonModule, ServiceProxyModule, FormsModule, SpinnerComponent],\r\n  templateUrl: './conection.component.html',\r\n  styles: [`\r\n    /* Animations */\r\n    @keyframes fadeIn {\r\n      from { opacity: 0; transform: translateY(-10px); }\r\n      to { opacity: 1; transform: translateY(0); }\r\n    }\r\n\r\n    @keyframes pulse {\r\n      0% { transform: scale(1); }\r\n      50% { transform: scale(1.05); }\r\n      100% { transform: scale(1); }\r\n    }\r\n\r\n    @keyframes slideInRight {\r\n      from { opacity: 0; transform: translateX(20px); }\r\n      to { opacity: 1; transform: translateX(0); }\r\n    }\r\n\r\n    .animate-fadeIn {\r\n      animation: fadeIn 0.5s ease-in-out;\r\n    }\r\n\r\n    .animate-pulse {\r\n      animation: pulse 2s ease-in-out infinite;\r\n    }\r\n\r\n    /* Header styles */\r\n    .sticky-header {\r\n      position: sticky;\r\n      top: 0;\r\n      z-index: 10;\r\n      backdrop-filter: blur(5px);\r\n    }\r\n\r\n    /* Button styles */\r\n    .action-button {\r\n      opacity: 0.9;\r\n      transition: all 0.3s ease;\r\n      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);\r\n    }\r\n\r\n    .action-button:hover {\r\n      opacity: 1;\r\n      transform: translateY(-2px) !important;\r\n      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n    }\r\n\r\n    /* Card hover effects for action buttons */\r\n    .api-card .action-button {\r\n      opacity: 0;\r\n      transform: translateY(10px);\r\n      transition: all 0.3s ease;\r\n    }\r\n\r\n    .api-card:hover .action-button {\r\n      opacity: 1;\r\n      transform: translateY(0);\r\n    }\r\n\r\n    /* Staggered animation for multiple buttons */\r\n    .api-card:hover .action-button:nth-child(1) {\r\n      transition-delay: 0.05s;\r\n    }\r\n\r\n    .api-card:hover .action-button:nth-child(2) {\r\n      transition-delay: 0.1s;\r\n    }\r\n\r\n    /* Dark mode specific styles */\r\n    :host-context(.dark) input,\r\n    :host-context(.dark) select,\r\n    :host-context(.dark) button {\r\n      color: var(--text-dark);\r\n      border-color: rgba(255, 255, 255, 0.1);\r\n    }\r\n\r\n    :host-context(.dark) .action-button {\r\n      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);\r\n    }\r\n\r\n    :host-context(.dark) button[title=\"Resync models\"] {\r\n      background-color: #1E3A8A;\r\n    }\r\n\r\n    :host-context(.dark) button[title=\"Resync models\"]:hover {\r\n      background-color: #2563EB;\r\n    }\r\n\r\n    :host-context(.dark) button[title=\"Delete API\"] {\r\n      background-color: #742A2A;\r\n    }\r\n\r\n    :host-context(.dark) button[title=\"Delete API\"]:hover {\r\n      background-color: #9B2C2C;\r\n    }\r\n\r\n    /* Card view dark mode styles */\r\n    :host-context(.dark) .grid > div {\r\n      background-color: var(--hover-blue-gray);\r\n      border-color: rgba(255, 255, 255, 0.1);\r\n    }\r\n\r\n    :host-context(.dark) .grid > div:hover {\r\n      box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.2);\r\n    }\r\n\r\n    :host-context(.dark) .grid > div .border-b,\r\n    :host-context(.dark) .grid > div .border-t {\r\n      border-color: rgba(255, 255, 255, 0.1);\r\n    }\r\n\r\n    :host-context(.dark) .grid > div .bg-[var(--hover-blue-gray)] {\r\n      background-color: rgba(0, 0, 0, 0.2);\r\n    }\r\n\r\n    /* Tooltip styles */\r\n    .tooltip {\r\n      position: relative;\r\n    }\r\n\r\n    .tooltip .tooltip-text {\r\n      visibility: hidden;\r\n      width: 120px;\r\n      background-color: #333;\r\n      color: #fff;\r\n      text-align: center;\r\n      border-radius: 6px;\r\n      padding: 5px;\r\n      position: absolute;\r\n      z-index: 1;\r\n      bottom: 125%;\r\n      left: 50%;\r\n      margin-left: -60px;\r\n      opacity: 0;\r\n      transition: opacity 0.3s;\r\n    }\r\n\r\n    .tooltip:hover .tooltip-text {\r\n      visibility: visible;\r\n      opacity: 1;\r\n    }\r\n\r\n    /* Responsive styles */\r\n    @media (max-width: 768px) {\r\n      .action-button {\r\n        opacity: 1;\r\n      }\r\n    }\r\n  `]\r\n})\r\nexport class ConnectionsComponent implements OnInit {\r\n  apiLists: any = [];\r\n  filteredApis: any = []; // For search functionality\r\n  searchQuery: string = ''; // For search input\r\n  isLoading: boolean = false; // Loading state for spinner\r\n  apiData: any = {\r\n    tokenUrl: '',\r\n    apiKey: '',\r\n    hasCustomModels: false,\r\n    customModels: [],\r\n    customModelsText: ''\r\n  };\r\n\r\n  // For No API toggle\r\n  noApiMode: boolean = false;\r\n\r\n  // For editing custom models\r\n  editCustomModelsData: any = {\r\n    id: '',\r\n    customModels: [],\r\n    customModelsText: ''\r\n  };\r\n  showEditCustomModelsForm = false;\r\n  showForm = false;\r\n  isCredentialsValid = false;\r\n  isValidating = false;\r\n  isTokenUrlValid = false;\r\n  validationMessage = '';\r\n  isResyncingAll = false;\r\n  isResyncing: { [key: string]: boolean } = {};\r\n\r\n  constructor(\r\n    private apiCredentialsService: ApiCredentialsServiceProxy,\r\n    private nzMessageService: NzMessageService,\r\n    private modelDetailsService: ModelDetailsServiceProxy\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n    this.loadApiCredentials();\r\n  }\r\n\r\n  loadApiCredentials() {\r\n    this.isLoading = true; // Show spinner\r\n    this.apiCredentialsService.getAll().subscribe({\r\n      next: (res: any) => {\r\n        if (res) {\r\n          this.apiLists = res;\r\n          // Apply current search filter if exists, otherwise show all\r\n          if (this.searchQuery) {\r\n            this.filterApis();\r\n          } else {\r\n            this.filteredApis = [...this.apiLists]; // Initialize filtered list with all APIs\r\n          }\r\n        }\r\n        this.isLoading = false; // Hide spinner\r\n      },\r\n      error: (error: any) => {\r\n        console.error('Error loading API credentials:', error);\r\n        this.isLoading = false; // Hide spinner on error\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Filter APIs based on search query\r\n   */\r\n  filterApis() {\r\n    if (!this.searchQuery) {\r\n      this.filteredApis = [...this.apiLists];\r\n      return;\r\n    }\r\n\r\n    const query = this.searchQuery.toLowerCase();\r\n    this.filteredApis = this.apiLists.filter((api: any) =>\r\n      api.tokenUrl?.toLowerCase().includes(query) ||\r\n      (api.tokenUrl?.split('.')[1]?.toLowerCase() || '').includes(query)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Clear search and reset filtered APIs\r\n   */\r\n  clearSearch() {\r\n    this.searchQuery = '';\r\n    this.filteredApis = [...this.apiLists];\r\n  }\r\n\r\n  onAddApi() {\r\n    this.showForm = true;\r\n  }\r\n\r\n  async validateCredentials() {\r\n    // Reset validation state\r\n    this.isValidating = true;\r\n    this.isCredentialsValid = false;\r\n    this.validationMessage = '';\r\n\r\n    // Validate token URL first\r\n    if (!this.apiData.tokenUrl || this.apiData.tokenUrl.trim() === '') {\r\n      this.isValidating = false;\r\n      this.validationMessage = 'Token URL is required';\r\n      this.nzMessageService.error(this.validationMessage);\r\n      return;\r\n    }\r\n\r\n    // Basic URL validation\r\n    try {\r\n      // Check if it's a valid URL format\r\n      new URL(this.apiData.tokenUrl);\r\n      this.isTokenUrlValid = true;\r\n    } catch (e) {\r\n      this.isValidating = false;\r\n      this.isTokenUrlValid = false;\r\n      this.validationMessage = 'Please enter a valid URL';\r\n      this.nzMessageService.error(this.validationMessage);\r\n      return;\r\n    }\r\n\r\n    // Always check validation first\r\n    if (!this.apiData.tokenUrl || this.apiData.tokenUrl.trim() === '') {\r\n      this.isValidating = false;\r\n      this.validationMessage = 'Token URL is required';\r\n      this.nzMessageService.error(this.validationMessage);\r\n      return;\r\n    }\r\n\r\n    // Validate API key for normal mode\r\n    if (!this.apiData.apiKey || this.apiData.apiKey.trim() === '') {\r\n      this.isValidating = false;\r\n      this.validationMessage = 'API Key is required';\r\n      this.nzMessageService.error(this.validationMessage);\r\n      return;\r\n    }\r\n\r\n    // Show loading message and validate with API\r\n    // const loadingMessage = this.nzMessageService.loading('Validating credentials...');\r\n\r\n    try {\r\n      let res: any = await this.apiCredentialsService\r\n        .validate(this.apiData)\r\n        .toPromise();\r\n\r\n      this.isValidating = false;\r\n\r\n      if (!res.isError) {\r\n        this.isCredentialsValid = true;\r\n        this.validationMessage = 'Credentials validated successfully';\r\n        this.nzMessageService.success(this.validationMessage);\r\n      } else {\r\n        this.isCredentialsValid = false;\r\n        this.validationMessage = res.message || 'Invalid credentials';\r\n        this.nzMessageService.error(this.validationMessage);\r\n      }\r\n    } catch (error: any) {\r\n      this.isValidating = false;\r\n      this.isCredentialsValid = false;\r\n      this.validationMessage = error.message || 'Validation failed';\r\n      this.nzMessageService.error(this.validationMessage);\r\n    }\r\n  }\r\n\r\n  saveApi() {\r\n    // Double-check validation before saving\r\n    if (!this.isCredentialsValid) {\r\n      this.nzMessageService.error('Please validate your credentials before saving');\r\n      return;\r\n    }\r\n\r\n    // Show loading message\r\n    const loadingMessage = this.nzMessageService.loading('Saving API credentials...');\r\n\r\n    this.apiCredentialsService.create(this.apiData).subscribe({\r\n      next: (res: any) => {\r\n        if (res) {\r\n          this.apiLists.push(this.apiData);\r\n          this.filteredApis = [...this.apiLists]; // Update filtered list\r\n          this.nzMessageService.success(res.message || 'API credentials saved successfully');\r\n          this.resetForm();\r\n        }\r\n      },\r\n      error: (error: any) => {\r\n        this.nzMessageService.error(error.message || 'Failed to save API credentials');\r\n      },\r\n      complete: () => {\r\n        // Remove loading message\r\n        this.nzMessageService.remove(loadingMessage.messageId);\r\n      }\r\n    });\r\n  }\r\n\r\n  searchName(url: string): string {\r\n    const providers = {\r\n      'azure': 'Azure OpenAI',\r\n      'openai': 'OpenAI',\r\n      'googleapis': 'Google APIs',\r\n    };\r\n\r\n    for (const [key, value] of Object.entries(providers)) {\r\n      if (url.includes(key)) {\r\n        return value;\r\n      }\r\n    }\r\n\r\n    return 'Local';\r\n  }\r\n\r\n  async deleteApi(api: any) {\r\n    try {\r\n      let res: any = await this.apiCredentialsService.delete(api.id).toPromise();\r\n\r\n      if (!res.isError) {\r\n        this.apiLists = this.apiLists.filter((item: any) => item.id != api.id);\r\n        this.filteredApis = this.filteredApis.filter((item: any) => item.id != api.id); // Update filtered list\r\n        this.nzMessageService.success(res.message);\r\n      }\r\n    } catch (error: any) {\r\n      this.nzMessageService.error(\r\n        'Cannot delete credentials; remove linked models in Agent first'\r\n      );\r\n    }\r\n  }\r\n\r\n  resetForm() {\r\n    this.apiData = {\r\n      tokenUrl: '',\r\n      apiKey: '',\r\n      hasCustomModels: false,\r\n      customModels: [],\r\n      customModelsText: ''\r\n    };\r\n    this.isCredentialsValid = false;\r\n    this.isTokenUrlValid = false;\r\n    this.validationMessage = '';\r\n    this.isValidating = false;\r\n    this.showForm = false;\r\n    this.noApiMode = false;\r\n  }\r\n\r\n  /**\r\n   * Toggle No API mode and set API key accordingly\r\n   */\r\n  toggleNoApiMode() {\r\n    if (this.noApiMode) {\r\n      // Set API key to \"no api\" when toggle is enabled\r\n      this.apiData.apiKey = \"no api\";\r\n\r\n      // Only auto-validate if token URL is valid\r\n      if (this.apiData.tokenUrl && this.isTokenUrlValid) {\r\n        this.validationMessage = 'No API mode is valid';\r\n      } else {\r\n        // Token URL still needs validation\r\n        this.isCredentialsValid = false;\r\n      }\r\n    } else {\r\n      // Clear API key when toggle is disabled\r\n      this.apiData.apiKey = \"\";\r\n      this.isCredentialsValid = false;\r\n      this.validationMessage = '';\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update custom models array from text input\r\n   */\r\n  updateCustomModels() {\r\n    if (!this.apiData.customModelsText) {\r\n      this.apiData.customModels = [];\r\n      return;\r\n    }\r\n\r\n    // Split by newline and filter out empty lines\r\n    this.apiData.customModels = this.apiData.customModelsText\r\n      .split('\\n')\r\n      .map((model: string) => model.trim())\r\n      .filter((model: string | any[]) => model.length > 0);\r\n  }\r\n\r\n  /**\r\n   * Update edit custom models array from text input\r\n   */\r\n  updateEditCustomModels() {\r\n    if (!this.editCustomModelsData.customModelsText) {\r\n      this.editCustomModelsData.customModels = [];\r\n      return;\r\n    }\r\n\r\n    // Split by newline and filter out empty lines\r\n    this.editCustomModelsData.customModels = this.editCustomModelsData.customModelsText\r\n      .split('\\n')\r\n      .map((model: string) => model.trim())\r\n      .filter((model: string | any[]) => model.length > 0);\r\n  }\r\n\r\n  /**\r\n   * Open the edit custom models form\r\n   */\r\n  onEditCustomModels(api: any) {\r\n    this.editCustomModelsData = {\r\n      id: api.id,\r\n      customModels: [],\r\n      customModelsText: ''\r\n    };\r\n\r\n    // Get existing models for this connection\r\n    this.apiCredentialsService.getById(api.id).subscribe((credentials: any) => {\r\n      if (credentials) {\r\n        // Get models for this provider\r\n        this.modelDetailsService.getByApiCredentialsId(api.id).subscribe((models: any) => {\r\n          if (models && models.length > 0) {\r\n            // Extract model names and join with newlines\r\n            const modelNames = models.map((model: any) => this.removeProvider(model.modelName));\r\n            this.editCustomModelsData.customModelsText = modelNames.join('\\n');\r\n            this.updateEditCustomModels();\r\n            this.showEditCustomModelsForm = true;\r\n          } else {\r\n            this.nzMessageService.error('No models found for this connection');\r\n          }\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  removeProvider(model: string) {\r\n    const parts = model.split('_');\r\n    return parts.slice(1).join('_');\r\n  }\r\n\r\n  /**\r\n   * Save updated custom models\r\n   */\r\n  saveEditCustomModels() {\r\n    if (this.editCustomModelsData.customModels.length === 0) {\r\n      this.nzMessageService.error('Please enter at least one model name');\r\n      return;\r\n    }\r\n\r\n    const loadingMessage = this.nzMessageService.loading('Saving custom models...');\r\n\r\n    this.apiCredentialsService.addCustomModels(this.editCustomModelsData.id, this.editCustomModelsData.customModels)\r\n      .subscribe({\r\n        next: (response: ResponseMessage) => {\r\n          this.nzMessageService.remove(loadingMessage.messageId);\r\n          if (!response.isError) {\r\n            this.nzMessageService.success(response.message || 'Custom models updated successfully');\r\n            this.resetEditCustomModelsForm();\r\n            // Refresh the API list to reflect any changes\r\n            this.loadApiCredentials();\r\n          } else {\r\n            this.nzMessageService.error(response.message || 'Failed to update custom models');\r\n          }\r\n        },\r\n        error: (error) => {\r\n          this.nzMessageService.remove(loadingMessage.messageId);\r\n          this.nzMessageService.error('Error updating custom models: ' + (error.message || 'Unknown error'));\r\n        }\r\n      });\r\n  }\r\n\r\n  /**\r\n   * Reset the edit custom models form\r\n   */\r\n  resetEditCustomModelsForm() {\r\n    this.editCustomModelsData = {\r\n      id: '',\r\n      customModels: [],\r\n      customModelsText: ''\r\n    };\r\n    this.showEditCustomModelsForm = false;\r\n  }\r\n\r\n  /**\r\n   * Resync models for a specific API connection\r\n   */\r\n  resyncModels(api: any) {\r\n    if (this.isResyncing[api.id]) {\r\n      return; // Prevent multiple clicks\r\n    }\r\n\r\n    this.isResyncing[api.id] = true;\r\n    const loadingMessage = this.nzMessageService.loading('Resyncing models...');\r\n\r\n    this.apiCredentialsService.resyncModels(api.id)\r\n      .pipe(\r\n        finalize(() => {\r\n          this.isResyncing[api.id] = false;\r\n        })\r\n      )\r\n      .subscribe({\r\n        next: (response: ResponseMessage) => {\r\n          this.nzMessageService.remove(loadingMessage.messageId);\r\n          if (!response.isError) {\r\n            this.nzMessageService.success(response.message || 'Models resynced successfully');\r\n            // Refresh the API list to reflect any changes\r\n            this.loadApiCredentials();\r\n            // The search will be maintained as loadApiCredentials calls filterApis\r\n          } else {\r\n            this.nzMessageService.error(response.message || 'Failed to resync models');\r\n          }\r\n        },\r\n        error: (error) => {\r\n          this.nzMessageService.remove(loadingMessage.messageId);\r\n          this.nzMessageService.error('Error resyncing models: ' + (error.message || 'Unknown error'));\r\n        }\r\n      });\r\n  }\r\n\r\n  /**\r\n   * Resync models for all API connections\r\n   */\r\n  resyncAllModels() {\r\n    if (this.isResyncingAll) {\r\n      return; // Prevent multiple clicks\r\n    }\r\n\r\n    this.isResyncingAll = true;\r\n    const loadingMessage = this.nzMessageService.loading('Resyncing all models...');\r\n\r\n    this.apiCredentialsService.resyncAllModels()\r\n      .pipe(\r\n        finalize(() => {\r\n          this.isResyncingAll = false;\r\n        })\r\n      )\r\n      .subscribe({\r\n        next: (response: ResponseMessage) => {\r\n          this.nzMessageService.remove(loadingMessage.messageId);\r\n          if (!response.isError) {\r\n            this.nzMessageService.success(response.message || 'All models resynced successfully');\r\n            // Refresh the API list to reflect any changes\r\n            this.loadApiCredentials();\r\n            // The search will be maintained as loadApiCredentials calls filterApis\r\n          } else {\r\n            this.nzMessageService.error(response.message || 'Failed to resync all models');\r\n          }\r\n        },\r\n        error: (error) => {\r\n          this.nzMessageService.remove(loadingMessage.messageId);\r\n          this.nzMessageService.error('Error resyncing all models: ' + (error.message || 'Unknown error'));\r\n        }\r\n      });\r\n  }\r\n}\r\n"], "mappings": ";;;;AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,0BAA0B,EAAEC,wBAAwB,QAAyB,iDAAiD;AACvI,SAASC,kBAAkB,QAAQ,sDAAsD;AACzF,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,gBAAgB,QAAQ,uBAAuB;AACxD,SAAoBC,QAAQ,QAAQ,gBAAgB;AACpD,SAASC,gBAAgB,QAAQ,mDAAmD;AA6J7E,IAAMC,oBAAoB,GAA1B,MAAMA,oBAAoB;EA+B/BC,YACUC,qBAAiD,EACjDC,gBAAkC,EAClCC,mBAA6C;IAF7C,KAAAF,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAjC7B,KAAAC,QAAQ,GAAQ,EAAE;IAClB,KAAAC,YAAY,GAAQ,EAAE,CAAC,CAAC;IACxB,KAAAC,WAAW,GAAW,EAAE,CAAC,CAAC;IAC1B,KAAAC,SAAS,GAAY,KAAK,CAAC,CAAC;IAC5B,KAAAC,OAAO,GAAQ;MACbC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,EAAE;MACVC,eAAe,EAAE,KAAK;MACtBC,YAAY,EAAE,EAAE;MAChBC,gBAAgB,EAAE;KACnB;IAED;IACA,KAAAC,SAAS,GAAY,KAAK;IAE1B;IACA,KAAAC,oBAAoB,GAAQ;MAC1BC,EAAE,EAAE,EAAE;MACNJ,YAAY,EAAE,EAAE;MAChBC,gBAAgB,EAAE;KACnB;IACD,KAAAI,wBAAwB,GAAG,KAAK;IAChC,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,iBAAiB,GAAG,EAAE;IACtB,KAAAC,cAAc,GAAG,KAAK;IACtB,KAAAC,WAAW,GAA+B,EAAE;EAMxC;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEAA,kBAAkBA,CAAA;IAChB,IAAI,CAACnB,SAAS,GAAG,IAAI,CAAC,CAAC;IACvB,IAAI,CAACN,qBAAqB,CAAC0B,MAAM,EAAE,CAACC,SAAS,CAAC;MAC5CC,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAIA,GAAG,EAAE;UACP,IAAI,CAAC1B,QAAQ,GAAG0B,GAAG;UACnB;UACA,IAAI,IAAI,CAACxB,WAAW,EAAE;YACpB,IAAI,CAACyB,UAAU,EAAE;WAClB,MAAM;YACL,IAAI,CAAC1B,YAAY,GAAG,CAAC,GAAG,IAAI,CAACD,QAAQ,CAAC,CAAC,CAAC;;;QAG5C,IAAI,CAACG,SAAS,GAAG,KAAK,CAAC,CAAC;MAC1B,CAAC;MACDyB,KAAK,EAAGA,KAAU,IAAI;QACpBC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,IAAI,CAACzB,SAAS,GAAG,KAAK,CAAC,CAAC;MAC1B;KACD,CAAC;EACJ;EAEA;;;EAGAwB,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAACzB,WAAW,EAAE;MACrB,IAAI,CAACD,YAAY,GAAG,CAAC,GAAG,IAAI,CAACD,QAAQ,CAAC;MACtC;;IAGF,MAAM8B,KAAK,GAAG,IAAI,CAAC5B,WAAW,CAAC6B,WAAW,EAAE;IAC5C,IAAI,CAAC9B,YAAY,GAAG,IAAI,CAACD,QAAQ,CAACgC,MAAM,CAAEC,GAAQ,IAChDA,GAAG,CAAC5B,QAAQ,EAAE0B,WAAW,EAAE,CAACG,QAAQ,CAACJ,KAAK,CAAC,IAC3C,CAACG,GAAG,CAAC5B,QAAQ,EAAE8B,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAEJ,WAAW,EAAE,IAAI,EAAE,EAAEG,QAAQ,CAACJ,KAAK,CAAC,CACnE;EACH;EAEA;;;EAGAM,WAAWA,CAAA;IACT,IAAI,CAAClC,WAAW,GAAG,EAAE;IACrB,IAAI,CAACD,YAAY,GAAG,CAAC,GAAG,IAAI,CAACD,QAAQ,CAAC;EACxC;EAEAqC,QAAQA,CAAA;IACN,IAAI,CAACvB,QAAQ,GAAG,IAAI;EACtB;EAEMwB,mBAAmBA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACvB;MACAD,KAAI,CAACvB,YAAY,GAAG,IAAI;MACxBuB,KAAI,CAACxB,kBAAkB,GAAG,KAAK;MAC/BwB,KAAI,CAACrB,iBAAiB,GAAG,EAAE;MAE3B;MACA,IAAI,CAACqB,KAAI,CAACnC,OAAO,CAACC,QAAQ,IAAIkC,KAAI,CAACnC,OAAO,CAACC,QAAQ,CAACoC,IAAI,EAAE,KAAK,EAAE,EAAE;QACjEF,KAAI,CAACvB,YAAY,GAAG,KAAK;QACzBuB,KAAI,CAACrB,iBAAiB,GAAG,uBAAuB;QAChDqB,KAAI,CAACzC,gBAAgB,CAAC8B,KAAK,CAACW,KAAI,CAACrB,iBAAiB,CAAC;QACnD;;MAGF;MACA,IAAI;QACF;QACA,IAAIwB,GAAG,CAACH,KAAI,CAACnC,OAAO,CAACC,QAAQ,CAAC;QAC9BkC,KAAI,CAACtB,eAAe,GAAG,IAAI;OAC5B,CAAC,OAAO0B,CAAC,EAAE;QACVJ,KAAI,CAACvB,YAAY,GAAG,KAAK;QACzBuB,KAAI,CAACtB,eAAe,GAAG,KAAK;QAC5BsB,KAAI,CAACrB,iBAAiB,GAAG,0BAA0B;QACnDqB,KAAI,CAACzC,gBAAgB,CAAC8B,KAAK,CAACW,KAAI,CAACrB,iBAAiB,CAAC;QACnD;;MAGF;MACA,IAAI,CAACqB,KAAI,CAACnC,OAAO,CAACC,QAAQ,IAAIkC,KAAI,CAACnC,OAAO,CAACC,QAAQ,CAACoC,IAAI,EAAE,KAAK,EAAE,EAAE;QACjEF,KAAI,CAACvB,YAAY,GAAG,KAAK;QACzBuB,KAAI,CAACrB,iBAAiB,GAAG,uBAAuB;QAChDqB,KAAI,CAACzC,gBAAgB,CAAC8B,KAAK,CAACW,KAAI,CAACrB,iBAAiB,CAAC;QACnD;;MAGF;MACA,IAAI,CAACqB,KAAI,CAACnC,OAAO,CAACE,MAAM,IAAIiC,KAAI,CAACnC,OAAO,CAACE,MAAM,CAACmC,IAAI,EAAE,KAAK,EAAE,EAAE;QAC7DF,KAAI,CAACvB,YAAY,GAAG,KAAK;QACzBuB,KAAI,CAACrB,iBAAiB,GAAG,qBAAqB;QAC9CqB,KAAI,CAACzC,gBAAgB,CAAC8B,KAAK,CAACW,KAAI,CAACrB,iBAAiB,CAAC;QACnD;;MAGF;MACA;MAEA,IAAI;QACF,IAAIQ,GAAG,SAAca,KAAI,CAAC1C,qBAAqB,CAC5C+C,QAAQ,CAACL,KAAI,CAACnC,OAAO,CAAC,CACtByC,SAAS,EAAE;QAEdN,KAAI,CAACvB,YAAY,GAAG,KAAK;QAEzB,IAAI,CAACU,GAAG,CAACoB,OAAO,EAAE;UAChBP,KAAI,CAACxB,kBAAkB,GAAG,IAAI;UAC9BwB,KAAI,CAACrB,iBAAiB,GAAG,oCAAoC;UAC7DqB,KAAI,CAACzC,gBAAgB,CAACiD,OAAO,CAACR,KAAI,CAACrB,iBAAiB,CAAC;SACtD,MAAM;UACLqB,KAAI,CAACxB,kBAAkB,GAAG,KAAK;UAC/BwB,KAAI,CAACrB,iBAAiB,GAAGQ,GAAG,CAACsB,OAAO,IAAI,qBAAqB;UAC7DT,KAAI,CAACzC,gBAAgB,CAAC8B,KAAK,CAACW,KAAI,CAACrB,iBAAiB,CAAC;;OAEtD,CAAC,OAAOU,KAAU,EAAE;QACnBW,KAAI,CAACvB,YAAY,GAAG,KAAK;QACzBuB,KAAI,CAACxB,kBAAkB,GAAG,KAAK;QAC/BwB,KAAI,CAACrB,iBAAiB,GAAGU,KAAK,CAACoB,OAAO,IAAI,mBAAmB;QAC7DT,KAAI,CAACzC,gBAAgB,CAAC8B,KAAK,CAACW,KAAI,CAACrB,iBAAiB,CAAC;;IACpD;EACH;EAEA+B,OAAOA,CAAA;IACL;IACA,IAAI,CAAC,IAAI,CAAClC,kBAAkB,EAAE;MAC5B,IAAI,CAACjB,gBAAgB,CAAC8B,KAAK,CAAC,gDAAgD,CAAC;MAC7E;;IAGF;IACA,MAAMsB,cAAc,GAAG,IAAI,CAACpD,gBAAgB,CAACqD,OAAO,CAAC,2BAA2B,CAAC;IAEjF,IAAI,CAACtD,qBAAqB,CAACuD,MAAM,CAAC,IAAI,CAAChD,OAAO,CAAC,CAACoB,SAAS,CAAC;MACxDC,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAIA,GAAG,EAAE;UACP,IAAI,CAAC1B,QAAQ,CAACqD,IAAI,CAAC,IAAI,CAACjD,OAAO,CAAC;UAChC,IAAI,CAACH,YAAY,GAAG,CAAC,GAAG,IAAI,CAACD,QAAQ,CAAC,CAAC,CAAC;UACxC,IAAI,CAACF,gBAAgB,CAACiD,OAAO,CAACrB,GAAG,CAACsB,OAAO,IAAI,oCAAoC,CAAC;UAClF,IAAI,CAACM,SAAS,EAAE;;MAEpB,CAAC;MACD1B,KAAK,EAAGA,KAAU,IAAI;QACpB,IAAI,CAAC9B,gBAAgB,CAAC8B,KAAK,CAACA,KAAK,CAACoB,OAAO,IAAI,gCAAgC,CAAC;MAChF,CAAC;MACDO,QAAQ,EAAEA,CAAA,KAAK;QACb;QACA,IAAI,CAACzD,gBAAgB,CAAC0D,MAAM,CAACN,cAAc,CAACO,SAAS,CAAC;MACxD;KACD,CAAC;EACJ;EAEAC,UAAUA,CAACC,GAAW;IACpB,MAAMC,SAAS,GAAG;MAChB,OAAO,EAAE,cAAc;MACvB,QAAQ,EAAE,QAAQ;MAClB,YAAY,EAAE;KACf;IAED,KAAK,MAAM,CAACC,GAAG,EAAEC,KAAK,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACJ,SAAS,CAAC,EAAE;MACpD,IAAID,GAAG,CAACzB,QAAQ,CAAC2B,GAAG,CAAC,EAAE;QACrB,OAAOC,KAAK;;;IAIhB,OAAO,OAAO;EAChB;EAEMG,SAASA,CAAChC,GAAQ;IAAA,IAAAiC,MAAA;IAAA,OAAA1B,iBAAA;MACtB,IAAI;QACF,IAAId,GAAG,SAAcwC,MAAI,CAACrE,qBAAqB,CAACsE,MAAM,CAAClC,GAAG,CAACrB,EAAE,CAAC,CAACiC,SAAS,EAAE;QAE1E,IAAI,CAACnB,GAAG,CAACoB,OAAO,EAAE;UAChBoB,MAAI,CAAClE,QAAQ,GAAGkE,MAAI,CAAClE,QAAQ,CAACgC,MAAM,CAAEoC,IAAS,IAAKA,IAAI,CAACxD,EAAE,IAAIqB,GAAG,CAACrB,EAAE,CAAC;UACtEsD,MAAI,CAACjE,YAAY,GAAGiE,MAAI,CAACjE,YAAY,CAAC+B,MAAM,CAAEoC,IAAS,IAAKA,IAAI,CAACxD,EAAE,IAAIqB,GAAG,CAACrB,EAAE,CAAC,CAAC,CAAC;UAChFsD,MAAI,CAACpE,gBAAgB,CAACiD,OAAO,CAACrB,GAAG,CAACsB,OAAO,CAAC;;OAE7C,CAAC,OAAOpB,KAAU,EAAE;QACnBsC,MAAI,CAACpE,gBAAgB,CAAC8B,KAAK,CACzB,gEAAgE,CACjE;;IACF;EACH;EAEA0B,SAASA,CAAA;IACP,IAAI,CAAClD,OAAO,GAAG;MACbC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,EAAE;MACVC,eAAe,EAAE,KAAK;MACtBC,YAAY,EAAE,EAAE;MAChBC,gBAAgB,EAAE;KACnB;IACD,IAAI,CAACM,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACE,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACF,YAAY,GAAG,KAAK;IACzB,IAAI,CAACF,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACJ,SAAS,GAAG,KAAK;EACxB;EAEA;;;EAGA2D,eAAeA,CAAA;IACb,IAAI,IAAI,CAAC3D,SAAS,EAAE;MAClB;MACA,IAAI,CAACN,OAAO,CAACE,MAAM,GAAG,QAAQ;MAE9B;MACA,IAAI,IAAI,CAACF,OAAO,CAACC,QAAQ,IAAI,IAAI,CAACY,eAAe,EAAE;QACjD,IAAI,CAACC,iBAAiB,GAAG,sBAAsB;OAChD,MAAM;QACL;QACA,IAAI,CAACH,kBAAkB,GAAG,KAAK;;KAElC,MAAM;MACL;MACA,IAAI,CAACX,OAAO,CAACE,MAAM,GAAG,EAAE;MACxB,IAAI,CAACS,kBAAkB,GAAG,KAAK;MAC/B,IAAI,CAACG,iBAAiB,GAAG,EAAE;;EAE/B;EAEA;;;EAGAoD,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAAClE,OAAO,CAACK,gBAAgB,EAAE;MAClC,IAAI,CAACL,OAAO,CAACI,YAAY,GAAG,EAAE;MAC9B;;IAGF;IACA,IAAI,CAACJ,OAAO,CAACI,YAAY,GAAG,IAAI,CAACJ,OAAO,CAACK,gBAAgB,CACtD0B,KAAK,CAAC,IAAI,CAAC,CACXoC,GAAG,CAAEC,KAAa,IAAKA,KAAK,CAAC/B,IAAI,EAAE,CAAC,CACpCT,MAAM,CAAEwC,KAAqB,IAAKA,KAAK,CAACC,MAAM,GAAG,CAAC,CAAC;EACxD;EAEA;;;EAGAC,sBAAsBA,CAAA;IACpB,IAAI,CAAC,IAAI,CAAC/D,oBAAoB,CAACF,gBAAgB,EAAE;MAC/C,IAAI,CAACE,oBAAoB,CAACH,YAAY,GAAG,EAAE;MAC3C;;IAGF;IACA,IAAI,CAACG,oBAAoB,CAACH,YAAY,GAAG,IAAI,CAACG,oBAAoB,CAACF,gBAAgB,CAChF0B,KAAK,CAAC,IAAI,CAAC,CACXoC,GAAG,CAAEC,KAAa,IAAKA,KAAK,CAAC/B,IAAI,EAAE,CAAC,CACpCT,MAAM,CAAEwC,KAAqB,IAAKA,KAAK,CAACC,MAAM,GAAG,CAAC,CAAC;EACxD;EAEA;;;EAGAE,kBAAkBA,CAAC1C,GAAQ;IACzB,IAAI,CAACtB,oBAAoB,GAAG;MAC1BC,EAAE,EAAEqB,GAAG,CAACrB,EAAE;MACVJ,YAAY,EAAE,EAAE;MAChBC,gBAAgB,EAAE;KACnB;IAED;IACA,IAAI,CAACZ,qBAAqB,CAAC+E,OAAO,CAAC3C,GAAG,CAACrB,EAAE,CAAC,CAACY,SAAS,CAAEqD,WAAgB,IAAI;MACxE,IAAIA,WAAW,EAAE;QACf;QACA,IAAI,CAAC9E,mBAAmB,CAAC+E,qBAAqB,CAAC7C,GAAG,CAACrB,EAAE,CAAC,CAACY,SAAS,CAAEuD,MAAW,IAAI;UAC/E,IAAIA,MAAM,IAAIA,MAAM,CAACN,MAAM,GAAG,CAAC,EAAE;YAC/B;YACA,MAAMO,UAAU,GAAGD,MAAM,CAACR,GAAG,CAAEC,KAAU,IAAK,IAAI,CAACS,cAAc,CAACT,KAAK,CAACU,SAAS,CAAC,CAAC;YACnF,IAAI,CAACvE,oBAAoB,CAACF,gBAAgB,GAAGuE,UAAU,CAACG,IAAI,CAAC,IAAI,CAAC;YAClE,IAAI,CAACT,sBAAsB,EAAE;YAC7B,IAAI,CAAC7D,wBAAwB,GAAG,IAAI;WACrC,MAAM;YACL,IAAI,CAACf,gBAAgB,CAAC8B,KAAK,CAAC,qCAAqC,CAAC;;QAEtE,CAAC,CAAC;;IAEN,CAAC,CAAC;EACJ;EAEAqD,cAAcA,CAACT,KAAa;IAC1B,MAAMY,KAAK,GAAGZ,KAAK,CAACrC,KAAK,CAAC,GAAG,CAAC;IAC9B,OAAOiD,KAAK,CAACC,KAAK,CAAC,CAAC,CAAC,CAACF,IAAI,CAAC,GAAG,CAAC;EACjC;EAEA;;;EAGAG,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAAC3E,oBAAoB,CAACH,YAAY,CAACiE,MAAM,KAAK,CAAC,EAAE;MACvD,IAAI,CAAC3E,gBAAgB,CAAC8B,KAAK,CAAC,sCAAsC,CAAC;MACnE;;IAGF,MAAMsB,cAAc,GAAG,IAAI,CAACpD,gBAAgB,CAACqD,OAAO,CAAC,yBAAyB,CAAC;IAE/E,IAAI,CAACtD,qBAAqB,CAAC0F,eAAe,CAAC,IAAI,CAAC5E,oBAAoB,CAACC,EAAE,EAAE,IAAI,CAACD,oBAAoB,CAACH,YAAY,CAAC,CAC7GgB,SAAS,CAAC;MACTC,IAAI,EAAG+D,QAAyB,IAAI;QAClC,IAAI,CAAC1F,gBAAgB,CAAC0D,MAAM,CAACN,cAAc,CAACO,SAAS,CAAC;QACtD,IAAI,CAAC+B,QAAQ,CAAC1C,OAAO,EAAE;UACrB,IAAI,CAAChD,gBAAgB,CAACiD,OAAO,CAACyC,QAAQ,CAACxC,OAAO,IAAI,oCAAoC,CAAC;UACvF,IAAI,CAACyC,yBAAyB,EAAE;UAChC;UACA,IAAI,CAACnE,kBAAkB,EAAE;SAC1B,MAAM;UACL,IAAI,CAACxB,gBAAgB,CAAC8B,KAAK,CAAC4D,QAAQ,CAACxC,OAAO,IAAI,gCAAgC,CAAC;;MAErF,CAAC;MACDpB,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC9B,gBAAgB,CAAC0D,MAAM,CAACN,cAAc,CAACO,SAAS,CAAC;QACtD,IAAI,CAAC3D,gBAAgB,CAAC8B,KAAK,CAAC,gCAAgC,IAAIA,KAAK,CAACoB,OAAO,IAAI,eAAe,CAAC,CAAC;MACpG;KACD,CAAC;EACN;EAEA;;;EAGAyC,yBAAyBA,CAAA;IACvB,IAAI,CAAC9E,oBAAoB,GAAG;MAC1BC,EAAE,EAAE,EAAE;MACNJ,YAAY,EAAE,EAAE;MAChBC,gBAAgB,EAAE;KACnB;IACD,IAAI,CAACI,wBAAwB,GAAG,KAAK;EACvC;EAEA;;;EAGA6E,YAAYA,CAACzD,GAAQ;IACnB,IAAI,IAAI,CAACb,WAAW,CAACa,GAAG,CAACrB,EAAE,CAAC,EAAE;MAC5B,OAAO,CAAC;;IAGV,IAAI,CAACQ,WAAW,CAACa,GAAG,CAACrB,EAAE,CAAC,GAAG,IAAI;IAC/B,MAAMsC,cAAc,GAAG,IAAI,CAACpD,gBAAgB,CAACqD,OAAO,CAAC,qBAAqB,CAAC;IAE3E,IAAI,CAACtD,qBAAqB,CAAC6F,YAAY,CAACzD,GAAG,CAACrB,EAAE,CAAC,CAC5C+E,IAAI,CACHlG,QAAQ,CAAC,MAAK;MACZ,IAAI,CAAC2B,WAAW,CAACa,GAAG,CAACrB,EAAE,CAAC,GAAG,KAAK;IAClC,CAAC,CAAC,CACH,CACAY,SAAS,CAAC;MACTC,IAAI,EAAG+D,QAAyB,IAAI;QAClC,IAAI,CAAC1F,gBAAgB,CAAC0D,MAAM,CAACN,cAAc,CAACO,SAAS,CAAC;QACtD,IAAI,CAAC+B,QAAQ,CAAC1C,OAAO,EAAE;UACrB,IAAI,CAAChD,gBAAgB,CAACiD,OAAO,CAACyC,QAAQ,CAACxC,OAAO,IAAI,8BAA8B,CAAC;UACjF;UACA,IAAI,CAAC1B,kBAAkB,EAAE;UACzB;SACD,MAAM;UACL,IAAI,CAACxB,gBAAgB,CAAC8B,KAAK,CAAC4D,QAAQ,CAACxC,OAAO,IAAI,yBAAyB,CAAC;;MAE9E,CAAC;MACDpB,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC9B,gBAAgB,CAAC0D,MAAM,CAACN,cAAc,CAACO,SAAS,CAAC;QACtD,IAAI,CAAC3D,gBAAgB,CAAC8B,KAAK,CAAC,0BAA0B,IAAIA,KAAK,CAACoB,OAAO,IAAI,eAAe,CAAC,CAAC;MAC9F;KACD,CAAC;EACN;EAEA;;;EAGA4C,eAAeA,CAAA;IACb,IAAI,IAAI,CAACzE,cAAc,EAAE;MACvB,OAAO,CAAC;;IAGV,IAAI,CAACA,cAAc,GAAG,IAAI;IAC1B,MAAM+B,cAAc,GAAG,IAAI,CAACpD,gBAAgB,CAACqD,OAAO,CAAC,yBAAyB,CAAC;IAE/E,IAAI,CAACtD,qBAAqB,CAAC+F,eAAe,EAAE,CACzCD,IAAI,CACHlG,QAAQ,CAAC,MAAK;MACZ,IAAI,CAAC0B,cAAc,GAAG,KAAK;IAC7B,CAAC,CAAC,CACH,CACAK,SAAS,CAAC;MACTC,IAAI,EAAG+D,QAAyB,IAAI;QAClC,IAAI,CAAC1F,gBAAgB,CAAC0D,MAAM,CAACN,cAAc,CAACO,SAAS,CAAC;QACtD,IAAI,CAAC+B,QAAQ,CAAC1C,OAAO,EAAE;UACrB,IAAI,CAAChD,gBAAgB,CAACiD,OAAO,CAACyC,QAAQ,CAACxC,OAAO,IAAI,kCAAkC,CAAC;UACrF;UACA,IAAI,CAAC1B,kBAAkB,EAAE;UACzB;SACD,MAAM;UACL,IAAI,CAACxB,gBAAgB,CAAC8B,KAAK,CAAC4D,QAAQ,CAACxC,OAAO,IAAI,6BAA6B,CAAC;;MAElF,CAAC;MACDpB,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC9B,gBAAgB,CAAC0D,MAAM,CAACN,cAAc,CAACO,SAAS,CAAC;QACtD,IAAI,CAAC3D,gBAAgB,CAAC8B,KAAK,CAAC,8BAA8B,IAAIA,KAAK,CAACoB,OAAO,IAAI,eAAe,CAAC,CAAC;MAClG;KACD,CAAC;EACN;;;;;;;;;;;AAxbWrD,oBAAoB,GAAAkG,UAAA,EA3JhC3G,SAAS,CAAC;EACT4G,QAAQ,EAAE,0BAA0B;EACpCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAAC7G,YAAY,EAAEG,kBAAkB,EAAEC,WAAW,EAAEG,gBAAgB,CAAC;EAC1EuG,QAAA,EAAAC,oBAAyC;;CAsJ1C,CAAC,C,EACWvG,oBAAoB,CAybhC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}