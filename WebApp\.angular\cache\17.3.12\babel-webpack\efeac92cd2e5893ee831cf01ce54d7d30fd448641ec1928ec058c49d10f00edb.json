{"ast": null, "code": "import { AttributorS<PERSON>, BlockBlot, EmbedBlot, LeafBlot, Scope } from 'parchment';\nimport Delta from 'quill-delta';\nimport Break from './break.js';\nimport Inline from './inline.js';\nimport TextBlot from './text.js';\nconst NEWLINE_LENGTH = 1;\nclass Block extends BlockBlot {\n  cache = {};\n  delta() {\n    if (this.cache.delta == null) {\n      this.cache.delta = blockDelta(this);\n    }\n    return this.cache.delta;\n  }\n  deleteAt(index, length) {\n    super.deleteAt(index, length);\n    this.cache = {};\n  }\n  formatAt(index, length, name, value) {\n    if (length <= 0) return;\n    if (this.scroll.query(name, Scope.BLOCK)) {\n      if (index + length === this.length()) {\n        this.format(name, value);\n      }\n    } else {\n      super.formatAt(index, Math.min(length, this.length() - index - 1), name, value);\n    }\n    this.cache = {};\n  }\n  insertAt(index, value, def) {\n    if (def != null) {\n      super.insertAt(index, value, def);\n      this.cache = {};\n      return;\n    }\n    if (value.length === 0) return;\n    const lines = value.split('\\n');\n    const text = lines.shift();\n    if (text.length > 0) {\n      if (index < this.length() - 1 || this.children.tail == null) {\n        super.insertAt(Math.min(index, this.length() - 1), text);\n      } else {\n        this.children.tail.insertAt(this.children.tail.length(), text);\n      }\n      this.cache = {};\n    }\n    // TODO: Fix this next time the file is edited.\n    // eslint-disable-next-line @typescript-eslint/no-this-alias\n    let block = this;\n    lines.reduce((lineIndex, line) => {\n      // @ts-expect-error Fix me later\n      block = block.split(lineIndex, true);\n      block.insertAt(0, line);\n      return line.length;\n    }, index + text.length);\n  }\n  insertBefore(blot, ref) {\n    const {\n      head\n    } = this.children;\n    super.insertBefore(blot, ref);\n    if (head instanceof Break) {\n      head.remove();\n    }\n    this.cache = {};\n  }\n  length() {\n    if (this.cache.length == null) {\n      this.cache.length = super.length() + NEWLINE_LENGTH;\n    }\n    return this.cache.length;\n  }\n  moveChildren(target, ref) {\n    super.moveChildren(target, ref);\n    this.cache = {};\n  }\n  optimize(context) {\n    super.optimize(context);\n    this.cache = {};\n  }\n  path(index) {\n    return super.path(index, true);\n  }\n  removeChild(child) {\n    super.removeChild(child);\n    this.cache = {};\n  }\n  split(index) {\n    let force = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    if (force && (index === 0 || index >= this.length() - NEWLINE_LENGTH)) {\n      const clone = this.clone();\n      if (index === 0) {\n        this.parent.insertBefore(clone, this);\n        return this;\n      }\n      this.parent.insertBefore(clone, this.next);\n      return clone;\n    }\n    const next = super.split(index, force);\n    this.cache = {};\n    return next;\n  }\n}\nBlock.blotName = 'block';\nBlock.tagName = 'P';\nBlock.defaultChild = Break;\nBlock.allowedChildren = [Break, Inline, EmbedBlot, TextBlot];\nclass BlockEmbed extends EmbedBlot {\n  attach() {\n    super.attach();\n    this.attributes = new AttributorStore(this.domNode);\n  }\n  delta() {\n    return new Delta().insert(this.value(), {\n      ...this.formats(),\n      ...this.attributes.values()\n    });\n  }\n  format(name, value) {\n    const attribute = this.scroll.query(name, Scope.BLOCK_ATTRIBUTE);\n    if (attribute != null) {\n      // @ts-expect-error TODO: Scroll#query() should return Attributor when scope is attribute\n      this.attributes.attribute(attribute, value);\n    }\n  }\n  formatAt(index, length, name, value) {\n    this.format(name, value);\n  }\n  insertAt(index, value, def) {\n    if (def != null) {\n      super.insertAt(index, value, def);\n      return;\n    }\n    const lines = value.split('\\n');\n    const text = lines.pop();\n    const blocks = lines.map(line => {\n      const block = this.scroll.create(Block.blotName);\n      block.insertAt(0, line);\n      return block;\n    });\n    const ref = this.split(index);\n    blocks.forEach(block => {\n      this.parent.insertBefore(block, ref);\n    });\n    if (text) {\n      this.parent.insertBefore(this.scroll.create('text', text), ref);\n    }\n  }\n}\nBlockEmbed.scope = Scope.BLOCK_BLOT;\n// It is important for cursor behavior BlockEmbeds use tags that are block level elements\n\nfunction blockDelta(blot) {\n  let filter = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n  return blot.descendants(LeafBlot).reduce((delta, leaf) => {\n    if (leaf.length() === 0) {\n      return delta;\n    }\n    return delta.insert(leaf.value(), bubbleFormats(leaf, {}, filter));\n  }, new Delta()).insert('\\n', bubbleFormats(blot));\n}\nfunction bubbleFormats(blot) {\n  let formats = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  let filter = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n  if (blot == null) return formats;\n  if ('formats' in blot && typeof blot.formats === 'function') {\n    formats = {\n      ...formats,\n      ...blot.formats()\n    };\n    if (filter) {\n      // exclude syntax highlighting from deltas and getFormat()\n      delete formats['code-token'];\n    }\n  }\n  if (blot.parent == null || blot.parent.statics.blotName === 'scroll' || blot.parent.statics.scope !== blot.statics.scope) {\n    return formats;\n  }\n  return bubbleFormats(blot.parent, formats, filter);\n}\nexport { blockDelta, bubbleFormats, BlockEmbed, Block as default };", "map": {"version": 3, "names": ["AttributorStore", "BlockBlot", "EmbedBlot", "LeafBlot", "<PERSON><PERSON>", "Delta", "Break", "Inline", "TextBlot", "NEWLINE_LENGTH", "Block", "cache", "delta", "blockDelta", "deleteAt", "index", "length", "formatAt", "name", "value", "scroll", "query", "BLOCK", "format", "Math", "min", "insertAt", "def", "lines", "split", "text", "shift", "children", "tail", "block", "reduce", "lineIndex", "line", "insertBefore", "blot", "ref", "head", "remove", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "target", "optimize", "context", "path", "<PERSON><PERSON><PERSON><PERSON>", "child", "force", "arguments", "undefined", "clone", "parent", "next", "blotName", "tagName", "defaultChild", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "BlockEmbed", "attach", "attributes", "domNode", "insert", "formats", "values", "attribute", "BLOCK_ATTRIBUTE", "pop", "blocks", "map", "create", "for<PERSON>ach", "scope", "BLOCK_BLOT", "filter", "descendants", "leaf", "bubbleFormats", "statics", "default"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/quill/blots/block.js"], "sourcesContent": ["import { AttributorS<PERSON>, BlockBlot, EmbedBlot, LeafBlot, Scope } from 'parchment';\nimport Delta from 'quill-delta';\nimport Break from './break.js';\nimport Inline from './inline.js';\nimport TextBlot from './text.js';\nconst NEWLINE_LENGTH = 1;\nclass Block extends BlockBlot {\n  cache = {};\n  delta() {\n    if (this.cache.delta == null) {\n      this.cache.delta = blockDelta(this);\n    }\n    return this.cache.delta;\n  }\n  deleteAt(index, length) {\n    super.deleteAt(index, length);\n    this.cache = {};\n  }\n  formatAt(index, length, name, value) {\n    if (length <= 0) return;\n    if (this.scroll.query(name, Scope.BLOCK)) {\n      if (index + length === this.length()) {\n        this.format(name, value);\n      }\n    } else {\n      super.formatAt(index, Math.min(length, this.length() - index - 1), name, value);\n    }\n    this.cache = {};\n  }\n  insertAt(index, value, def) {\n    if (def != null) {\n      super.insertAt(index, value, def);\n      this.cache = {};\n      return;\n    }\n    if (value.length === 0) return;\n    const lines = value.split('\\n');\n    const text = lines.shift();\n    if (text.length > 0) {\n      if (index < this.length() - 1 || this.children.tail == null) {\n        super.insertAt(Math.min(index, this.length() - 1), text);\n      } else {\n        this.children.tail.insertAt(this.children.tail.length(), text);\n      }\n      this.cache = {};\n    }\n    // TODO: Fix this next time the file is edited.\n    // eslint-disable-next-line @typescript-eslint/no-this-alias\n    let block = this;\n    lines.reduce((lineIndex, line) => {\n      // @ts-expect-error Fix me later\n      block = block.split(lineIndex, true);\n      block.insertAt(0, line);\n      return line.length;\n    }, index + text.length);\n  }\n  insertBefore(blot, ref) {\n    const {\n      head\n    } = this.children;\n    super.insertBefore(blot, ref);\n    if (head instanceof Break) {\n      head.remove();\n    }\n    this.cache = {};\n  }\n  length() {\n    if (this.cache.length == null) {\n      this.cache.length = super.length() + NEWLINE_LENGTH;\n    }\n    return this.cache.length;\n  }\n  moveChildren(target, ref) {\n    super.moveChildren(target, ref);\n    this.cache = {};\n  }\n  optimize(context) {\n    super.optimize(context);\n    this.cache = {};\n  }\n  path(index) {\n    return super.path(index, true);\n  }\n  removeChild(child) {\n    super.removeChild(child);\n    this.cache = {};\n  }\n  split(index) {\n    let force = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    if (force && (index === 0 || index >= this.length() - NEWLINE_LENGTH)) {\n      const clone = this.clone();\n      if (index === 0) {\n        this.parent.insertBefore(clone, this);\n        return this;\n      }\n      this.parent.insertBefore(clone, this.next);\n      return clone;\n    }\n    const next = super.split(index, force);\n    this.cache = {};\n    return next;\n  }\n}\nBlock.blotName = 'block';\nBlock.tagName = 'P';\nBlock.defaultChild = Break;\nBlock.allowedChildren = [Break, Inline, EmbedBlot, TextBlot];\nclass BlockEmbed extends EmbedBlot {\n  attach() {\n    super.attach();\n    this.attributes = new AttributorStore(this.domNode);\n  }\n  delta() {\n    return new Delta().insert(this.value(), {\n      ...this.formats(),\n      ...this.attributes.values()\n    });\n  }\n  format(name, value) {\n    const attribute = this.scroll.query(name, Scope.BLOCK_ATTRIBUTE);\n    if (attribute != null) {\n      // @ts-expect-error TODO: Scroll#query() should return Attributor when scope is attribute\n      this.attributes.attribute(attribute, value);\n    }\n  }\n  formatAt(index, length, name, value) {\n    this.format(name, value);\n  }\n  insertAt(index, value, def) {\n    if (def != null) {\n      super.insertAt(index, value, def);\n      return;\n    }\n    const lines = value.split('\\n');\n    const text = lines.pop();\n    const blocks = lines.map(line => {\n      const block = this.scroll.create(Block.blotName);\n      block.insertAt(0, line);\n      return block;\n    });\n    const ref = this.split(index);\n    blocks.forEach(block => {\n      this.parent.insertBefore(block, ref);\n    });\n    if (text) {\n      this.parent.insertBefore(this.scroll.create('text', text), ref);\n    }\n  }\n}\nBlockEmbed.scope = Scope.BLOCK_BLOT;\n// It is important for cursor behavior BlockEmbeds use tags that are block level elements\n\nfunction blockDelta(blot) {\n  let filter = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n  return blot.descendants(LeafBlot).reduce((delta, leaf) => {\n    if (leaf.length() === 0) {\n      return delta;\n    }\n    return delta.insert(leaf.value(), bubbleFormats(leaf, {}, filter));\n  }, new Delta()).insert('\\n', bubbleFormats(blot));\n}\nfunction bubbleFormats(blot) {\n  let formats = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  let filter = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n  if (blot == null) return formats;\n  if ('formats' in blot && typeof blot.formats === 'function') {\n    formats = {\n      ...formats,\n      ...blot.formats()\n    };\n    if (filter) {\n      // exclude syntax highlighting from deltas and getFormat()\n      delete formats['code-token'];\n    }\n  }\n  if (blot.parent == null || blot.parent.statics.blotName === 'scroll' || blot.parent.statics.scope !== blot.statics.scope) {\n    return formats;\n  }\n  return bubbleFormats(blot.parent, formats, filter);\n}\nexport { blockDelta, bubbleFormats, BlockEmbed, Block as default };\n"], "mappings": "AAAA,SAASA,eAAe,EAAEC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,KAAK,QAAQ,WAAW;AAClF,OAAOC,KAAK,MAAM,aAAa;AAC/B,OAAOC,KAAK,MAAM,YAAY;AAC9B,OAAOC,MAAM,MAAM,aAAa;AAChC,OAAOC,QAAQ,MAAM,WAAW;AAChC,MAAMC,cAAc,GAAG,CAAC;AACxB,MAAMC,KAAK,SAAST,SAAS,CAAC;EAC5BU,KAAK,GAAG,CAAC,CAAC;EACVC,KAAKA,CAAA,EAAG;IACN,IAAI,IAAI,CAACD,KAAK,CAACC,KAAK,IAAI,IAAI,EAAE;MAC5B,IAAI,CAACD,KAAK,CAACC,KAAK,GAAGC,UAAU,CAAC,IAAI,CAAC;IACrC;IACA,OAAO,IAAI,CAACF,KAAK,CAACC,KAAK;EACzB;EACAE,QAAQA,CAACC,KAAK,EAAEC,MAAM,EAAE;IACtB,KAAK,CAACF,QAAQ,CAACC,KAAK,EAAEC,MAAM,CAAC;IAC7B,IAAI,CAACL,KAAK,GAAG,CAAC,CAAC;EACjB;EACAM,QAAQA,CAACF,KAAK,EAAEC,MAAM,EAAEE,IAAI,EAAEC,KAAK,EAAE;IACnC,IAAIH,MAAM,IAAI,CAAC,EAAE;IACjB,IAAI,IAAI,CAACI,MAAM,CAACC,KAAK,CAACH,IAAI,EAAEd,KAAK,CAACkB,KAAK,CAAC,EAAE;MACxC,IAAIP,KAAK,GAAGC,MAAM,KAAK,IAAI,CAACA,MAAM,CAAC,CAAC,EAAE;QACpC,IAAI,CAACO,MAAM,CAACL,IAAI,EAAEC,KAAK,CAAC;MAC1B;IACF,CAAC,MAAM;MACL,KAAK,CAACF,QAAQ,CAACF,KAAK,EAAES,IAAI,CAACC,GAAG,CAACT,MAAM,EAAE,IAAI,CAACA,MAAM,CAAC,CAAC,GAAGD,KAAK,GAAG,CAAC,CAAC,EAAEG,IAAI,EAAEC,KAAK,CAAC;IACjF;IACA,IAAI,CAACR,KAAK,GAAG,CAAC,CAAC;EACjB;EACAe,QAAQA,CAACX,KAAK,EAAEI,KAAK,EAAEQ,GAAG,EAAE;IAC1B,IAAIA,GAAG,IAAI,IAAI,EAAE;MACf,KAAK,CAACD,QAAQ,CAACX,KAAK,EAAEI,KAAK,EAAEQ,GAAG,CAAC;MACjC,IAAI,CAAChB,KAAK,GAAG,CAAC,CAAC;MACf;IACF;IACA,IAAIQ,KAAK,CAACH,MAAM,KAAK,CAAC,EAAE;IACxB,MAAMY,KAAK,GAAGT,KAAK,CAACU,KAAK,CAAC,IAAI,CAAC;IAC/B,MAAMC,IAAI,GAAGF,KAAK,CAACG,KAAK,CAAC,CAAC;IAC1B,IAAID,IAAI,CAACd,MAAM,GAAG,CAAC,EAAE;MACnB,IAAID,KAAK,GAAG,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAACgB,QAAQ,CAACC,IAAI,IAAI,IAAI,EAAE;QAC3D,KAAK,CAACP,QAAQ,CAACF,IAAI,CAACC,GAAG,CAACV,KAAK,EAAE,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAEc,IAAI,CAAC;MAC1D,CAAC,MAAM;QACL,IAAI,CAACE,QAAQ,CAACC,IAAI,CAACP,QAAQ,CAAC,IAAI,CAACM,QAAQ,CAACC,IAAI,CAACjB,MAAM,CAAC,CAAC,EAAEc,IAAI,CAAC;MAChE;MACA,IAAI,CAACnB,KAAK,GAAG,CAAC,CAAC;IACjB;IACA;IACA;IACA,IAAIuB,KAAK,GAAG,IAAI;IAChBN,KAAK,CAACO,MAAM,CAAC,CAACC,SAAS,EAAEC,IAAI,KAAK;MAChC;MACAH,KAAK,GAAGA,KAAK,CAACL,KAAK,CAACO,SAAS,EAAE,IAAI,CAAC;MACpCF,KAAK,CAACR,QAAQ,CAAC,CAAC,EAAEW,IAAI,CAAC;MACvB,OAAOA,IAAI,CAACrB,MAAM;IACpB,CAAC,EAAED,KAAK,GAAGe,IAAI,CAACd,MAAM,CAAC;EACzB;EACAsB,YAAYA,CAACC,IAAI,EAAEC,GAAG,EAAE;IACtB,MAAM;MACJC;IACF,CAAC,GAAG,IAAI,CAACT,QAAQ;IACjB,KAAK,CAACM,YAAY,CAACC,IAAI,EAAEC,GAAG,CAAC;IAC7B,IAAIC,IAAI,YAAYnC,KAAK,EAAE;MACzBmC,IAAI,CAACC,MAAM,CAAC,CAAC;IACf;IACA,IAAI,CAAC/B,KAAK,GAAG,CAAC,CAAC;EACjB;EACAK,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAACL,KAAK,CAACK,MAAM,IAAI,IAAI,EAAE;MAC7B,IAAI,CAACL,KAAK,CAACK,MAAM,GAAG,KAAK,CAACA,MAAM,CAAC,CAAC,GAAGP,cAAc;IACrD;IACA,OAAO,IAAI,CAACE,KAAK,CAACK,MAAM;EAC1B;EACA2B,YAAYA,CAACC,MAAM,EAAEJ,GAAG,EAAE;IACxB,KAAK,CAACG,YAAY,CAACC,MAAM,EAAEJ,GAAG,CAAC;IAC/B,IAAI,CAAC7B,KAAK,GAAG,CAAC,CAAC;EACjB;EACAkC,QAAQA,CAACC,OAAO,EAAE;IAChB,KAAK,CAACD,QAAQ,CAACC,OAAO,CAAC;IACvB,IAAI,CAACnC,KAAK,GAAG,CAAC,CAAC;EACjB;EACAoC,IAAIA,CAAChC,KAAK,EAAE;IACV,OAAO,KAAK,CAACgC,IAAI,CAAChC,KAAK,EAAE,IAAI,CAAC;EAChC;EACAiC,WAAWA,CAACC,KAAK,EAAE;IACjB,KAAK,CAACD,WAAW,CAACC,KAAK,CAAC;IACxB,IAAI,CAACtC,KAAK,GAAG,CAAC,CAAC;EACjB;EACAkB,KAAKA,CAACd,KAAK,EAAE;IACX,IAAImC,KAAK,GAAGC,SAAS,CAACnC,MAAM,GAAG,CAAC,IAAImC,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;IACrF,IAAID,KAAK,KAAKnC,KAAK,KAAK,CAAC,IAAIA,KAAK,IAAI,IAAI,CAACC,MAAM,CAAC,CAAC,GAAGP,cAAc,CAAC,EAAE;MACrE,MAAM4C,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC,CAAC;MAC1B,IAAItC,KAAK,KAAK,CAAC,EAAE;QACf,IAAI,CAACuC,MAAM,CAAChB,YAAY,CAACe,KAAK,EAAE,IAAI,CAAC;QACrC,OAAO,IAAI;MACb;MACA,IAAI,CAACC,MAAM,CAAChB,YAAY,CAACe,KAAK,EAAE,IAAI,CAACE,IAAI,CAAC;MAC1C,OAAOF,KAAK;IACd;IACA,MAAME,IAAI,GAAG,KAAK,CAAC1B,KAAK,CAACd,KAAK,EAAEmC,KAAK,CAAC;IACtC,IAAI,CAACvC,KAAK,GAAG,CAAC,CAAC;IACf,OAAO4C,IAAI;EACb;AACF;AACA7C,KAAK,CAAC8C,QAAQ,GAAG,OAAO;AACxB9C,KAAK,CAAC+C,OAAO,GAAG,GAAG;AACnB/C,KAAK,CAACgD,YAAY,GAAGpD,KAAK;AAC1BI,KAAK,CAACiD,eAAe,GAAG,CAACrD,KAAK,EAAEC,MAAM,EAAEL,SAAS,EAAEM,QAAQ,CAAC;AAC5D,MAAMoD,UAAU,SAAS1D,SAAS,CAAC;EACjC2D,MAAMA,CAAA,EAAG;IACP,KAAK,CAACA,MAAM,CAAC,CAAC;IACd,IAAI,CAACC,UAAU,GAAG,IAAI9D,eAAe,CAAC,IAAI,CAAC+D,OAAO,CAAC;EACrD;EACAnD,KAAKA,CAAA,EAAG;IACN,OAAO,IAAIP,KAAK,CAAC,CAAC,CAAC2D,MAAM,CAAC,IAAI,CAAC7C,KAAK,CAAC,CAAC,EAAE;MACtC,GAAG,IAAI,CAAC8C,OAAO,CAAC,CAAC;MACjB,GAAG,IAAI,CAACH,UAAU,CAACI,MAAM,CAAC;IAC5B,CAAC,CAAC;EACJ;EACA3C,MAAMA,CAACL,IAAI,EAAEC,KAAK,EAAE;IAClB,MAAMgD,SAAS,GAAG,IAAI,CAAC/C,MAAM,CAACC,KAAK,CAACH,IAAI,EAAEd,KAAK,CAACgE,eAAe,CAAC;IAChE,IAAID,SAAS,IAAI,IAAI,EAAE;MACrB;MACA,IAAI,CAACL,UAAU,CAACK,SAAS,CAACA,SAAS,EAAEhD,KAAK,CAAC;IAC7C;EACF;EACAF,QAAQA,CAACF,KAAK,EAAEC,MAAM,EAAEE,IAAI,EAAEC,KAAK,EAAE;IACnC,IAAI,CAACI,MAAM,CAACL,IAAI,EAAEC,KAAK,CAAC;EAC1B;EACAO,QAAQA,CAACX,KAAK,EAAEI,KAAK,EAAEQ,GAAG,EAAE;IAC1B,IAAIA,GAAG,IAAI,IAAI,EAAE;MACf,KAAK,CAACD,QAAQ,CAACX,KAAK,EAAEI,KAAK,EAAEQ,GAAG,CAAC;MACjC;IACF;IACA,MAAMC,KAAK,GAAGT,KAAK,CAACU,KAAK,CAAC,IAAI,CAAC;IAC/B,MAAMC,IAAI,GAAGF,KAAK,CAACyC,GAAG,CAAC,CAAC;IACxB,MAAMC,MAAM,GAAG1C,KAAK,CAAC2C,GAAG,CAAClC,IAAI,IAAI;MAC/B,MAAMH,KAAK,GAAG,IAAI,CAACd,MAAM,CAACoD,MAAM,CAAC9D,KAAK,CAAC8C,QAAQ,CAAC;MAChDtB,KAAK,CAACR,QAAQ,CAAC,CAAC,EAAEW,IAAI,CAAC;MACvB,OAAOH,KAAK;IACd,CAAC,CAAC;IACF,MAAMM,GAAG,GAAG,IAAI,CAACX,KAAK,CAACd,KAAK,CAAC;IAC7BuD,MAAM,CAACG,OAAO,CAACvC,KAAK,IAAI;MACtB,IAAI,CAACoB,MAAM,CAAChB,YAAY,CAACJ,KAAK,EAAEM,GAAG,CAAC;IACtC,CAAC,CAAC;IACF,IAAIV,IAAI,EAAE;MACR,IAAI,CAACwB,MAAM,CAAChB,YAAY,CAAC,IAAI,CAAClB,MAAM,CAACoD,MAAM,CAAC,MAAM,EAAE1C,IAAI,CAAC,EAAEU,GAAG,CAAC;IACjE;EACF;AACF;AACAoB,UAAU,CAACc,KAAK,GAAGtE,KAAK,CAACuE,UAAU;AACnC;;AAEA,SAAS9D,UAAUA,CAAC0B,IAAI,EAAE;EACxB,IAAIqC,MAAM,GAAGzB,SAAS,CAACnC,MAAM,GAAG,CAAC,IAAImC,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;EACrF,OAAOZ,IAAI,CAACsC,WAAW,CAAC1E,QAAQ,CAAC,CAACgC,MAAM,CAAC,CAACvB,KAAK,EAAEkE,IAAI,KAAK;IACxD,IAAIA,IAAI,CAAC9D,MAAM,CAAC,CAAC,KAAK,CAAC,EAAE;MACvB,OAAOJ,KAAK;IACd;IACA,OAAOA,KAAK,CAACoD,MAAM,CAACc,IAAI,CAAC3D,KAAK,CAAC,CAAC,EAAE4D,aAAa,CAACD,IAAI,EAAE,CAAC,CAAC,EAAEF,MAAM,CAAC,CAAC;EACpE,CAAC,EAAE,IAAIvE,KAAK,CAAC,CAAC,CAAC,CAAC2D,MAAM,CAAC,IAAI,EAAEe,aAAa,CAACxC,IAAI,CAAC,CAAC;AACnD;AACA,SAASwC,aAAaA,CAACxC,IAAI,EAAE;EAC3B,IAAI0B,OAAO,GAAGd,SAAS,CAACnC,MAAM,GAAG,CAAC,IAAImC,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACpF,IAAIyB,MAAM,GAAGzB,SAAS,CAACnC,MAAM,GAAG,CAAC,IAAImC,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;EACrF,IAAIZ,IAAI,IAAI,IAAI,EAAE,OAAO0B,OAAO;EAChC,IAAI,SAAS,IAAI1B,IAAI,IAAI,OAAOA,IAAI,CAAC0B,OAAO,KAAK,UAAU,EAAE;IAC3DA,OAAO,GAAG;MACR,GAAGA,OAAO;MACV,GAAG1B,IAAI,CAAC0B,OAAO,CAAC;IAClB,CAAC;IACD,IAAIW,MAAM,EAAE;MACV;MACA,OAAOX,OAAO,CAAC,YAAY,CAAC;IAC9B;EACF;EACA,IAAI1B,IAAI,CAACe,MAAM,IAAI,IAAI,IAAIf,IAAI,CAACe,MAAM,CAAC0B,OAAO,CAACxB,QAAQ,KAAK,QAAQ,IAAIjB,IAAI,CAACe,MAAM,CAAC0B,OAAO,CAACN,KAAK,KAAKnC,IAAI,CAACyC,OAAO,CAACN,KAAK,EAAE;IACxH,OAAOT,OAAO;EAChB;EACA,OAAOc,aAAa,CAACxC,IAAI,CAACe,MAAM,EAAEW,OAAO,EAAEW,MAAM,CAAC;AACpD;AACA,SAAS/D,UAAU,EAAEkE,aAAa,EAAEnB,UAAU,EAAElD,KAAK,IAAIuE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}