{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./plugin-details.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./plugin-details.component.css?ngResource\";\nimport { Component } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { CommonModule } from '@angular/common';\nimport { NzCardModule } from 'ng-zorro-antd/card';\nimport { NzTagModule } from 'ng-zorro-antd/tag';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport { NzButtonModule } from 'ng-zorro-antd/button';\nimport { NzToolTipModule } from 'ng-zorro-antd/tooltip';\nimport { NzMessageService } from 'ng-zorro-antd/message';\nimport { PluginServiceProxy } from '../../../../shared/service-proxies/service-proxies';\nlet PluginDetailsComponent = class PluginDetailsComponent {\n  constructor(route, router, pluginService, message) {\n    this.route = route;\n    this.router = router;\n    this.pluginService = pluginService;\n    this.message = message;\n    this.loading = true;\n    this.pluginName = '';\n  }\n  ngOnInit() {\n    this.route.params.subscribe(params => {\n      this.pluginName = params['pluginName'];\n      if (this.pluginName) {\n        this.loadPluginDetails();\n      }\n    });\n  }\n  loadPluginDetails() {\n    this.loading = true;\n    this.pluginService.getByName(this.pluginName).subscribe({\n      next: plugin => {\n        this.plugin = plugin;\n        this.loading = false;\n      },\n      error: error => {\n        this.message.error(`Failed to load plugin details for ${this.pluginName}`);\n        this.loading = false;\n        console.error('Error loading plugin details:', error);\n      }\n    });\n  }\n  resyncPlugin() {\n    if (!this.plugin?.pluginName) return;\n    this.loading = true;\n    if (this.plugin.type?.toLowerCase() === 'openapi') {\n      this.pluginService.resyncOpenApiPlugin(this.plugin.pluginName).subscribe({\n        next: plugin => {\n          this.plugin = plugin;\n          this.loading = false;\n          this.message.success(`Plugin ${this.plugin.pluginName} resynced successfully`);\n        },\n        error: error => {\n          this.message.error(`Failed to resync plugin ${this.plugin?.pluginName}`);\n          this.loading = false;\n          console.error('Error resyncing plugin:', error);\n        }\n      });\n    }\n  }\n  getPluginTypeColor(type) {\n    if (!type) return 'default';\n    switch (type.toLowerCase()) {\n      case 'openapi':\n        return 'blue';\n      case 'customplugin':\n        return 'green';\n      default:\n        return 'default';\n    }\n  }\n  formatDate(date) {\n    if (!date) return 'N/A';\n    return new Date(date.toString()).toLocaleDateString();\n  }\n  getFunctionList(functions) {\n    if (!functions) return [];\n    return functions.split('\\r\\n');\n  }\n  goBack() {\n    this.router.navigate(['/settings/plugins']);\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: ActivatedRoute\n    }, {\n      type: Router\n    }, {\n      type: PluginServiceProxy\n    }, {\n      type: NzMessageService\n    }];\n  }\n};\nPluginDetailsComponent = __decorate([Component({\n  selector: 'app-plugin-details',\n  standalone: true,\n  imports: [CommonModule, NzCardModule, NzTagModule, NzIconModule, NzButtonModule, NzToolTipModule],\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], PluginDetailsComponent);\nexport { PluginDetailsComponent };", "map": {"version": 3, "names": ["Component", "ActivatedRoute", "Router", "CommonModule", "NzCardModule", "NzTagModule", "NzIconModule", "NzButtonModule", "NzToolTipModule", "NzMessageService", "PluginServiceProxy", "PluginDetailsComponent", "constructor", "route", "router", "pluginService", "message", "loading", "pluginName", "ngOnInit", "params", "subscribe", "loadPluginDetails", "getByName", "next", "plugin", "error", "console", "resyncPlugin", "type", "toLowerCase", "resyncOpenApiPlugin", "success", "getPluginTypeColor", "formatDate", "date", "Date", "toString", "toLocaleDateString", "getFunctionList", "functions", "split", "goBack", "navigate", "__decorate", "selector", "standalone", "imports", "template", "__NG_CLI_RESOURCE__0"], "sources": ["C:\\Users\\<USER>\\source\\ai-hub\\WebApp\\src\\app\\admin\\plugins\\plugin-details\\plugin-details.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NzCardModule } from 'ng-zorro-antd/card';\r\nimport { NzTagModule } from 'ng-zorro-antd/tag';\r\nimport { NzIconModule } from 'ng-zorro-antd/icon';\r\nimport { NzButtonModule } from 'ng-zorro-antd/button';\r\nimport { NzToolTipModule } from 'ng-zorro-antd/tooltip';\r\nimport { NzMessageService } from 'ng-zorro-antd/message';\r\nimport { PluginServiceProxy, PluginResponseDto } from '../../../../shared/service-proxies/service-proxies';\r\n\r\n@Component({\r\n  selector: 'app-plugin-details',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    NzCardModule,\r\n    NzTagModule,\r\n    NzIconModule,\r\n    NzButtonModule,\r\n    NzToolTipModule\r\n  ],\r\n  templateUrl: './plugin-details.component.html',\r\n  styleUrl: './plugin-details.component.css'\r\n})\r\nexport class PluginDetailsComponent implements OnInit {\r\n  plugin?: PluginResponseDto;\r\n  loading = true;\r\n  pluginName: string = '';\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private pluginService: PluginServiceProxy,\r\n    private message: NzMessageService\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    this.route.params.subscribe(params => {\r\n      this.pluginName = params['pluginName'];\r\n      if (this.pluginName) {\r\n        this.loadPluginDetails();\r\n      }\r\n    });\r\n  }\r\n\r\n  loadPluginDetails() {\r\n    this.loading = true;\r\n    this.pluginService.getByName(this.pluginName).subscribe({\r\n      next: (plugin) => {\r\n        this.plugin = plugin;\r\n        this.loading = false;\r\n      },\r\n      error: (error) => {\r\n        this.message.error(`Failed to load plugin details for ${this.pluginName}`);\r\n        this.loading = false;\r\n        console.error('Error loading plugin details:', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  resyncPlugin() {\r\n    if (!this.plugin?.pluginName) return;\r\n\r\n    this.loading = true;\r\n    if (this.plugin.type?.toLowerCase() === 'openapi') {\r\n      this.pluginService.resyncOpenApiPlugin(this.plugin.pluginName).subscribe({\r\n        next: (plugin) => {\r\n          this.plugin = plugin;\r\n          this.loading = false;\r\n          this.message.success(`Plugin ${this.plugin.pluginName} resynced successfully`);\r\n        },\r\n        error: (error) => {\r\n          this.message.error(`Failed to resync plugin ${this.plugin?.pluginName}`);\r\n          this.loading = false;\r\n          console.error('Error resyncing plugin:', error);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  getPluginTypeColor(type: string | undefined): string {\r\n    if (!type) return 'default';\r\n    switch (type.toLowerCase()) {\r\n      case 'openapi':\r\n        return 'blue';\r\n      case 'customplugin':\r\n        return 'green';\r\n      default:\r\n        return 'default';\r\n    }\r\n  }\r\n\r\n  formatDate(date: any): string {\r\n    if (!date) return 'N/A';\r\n    return new Date(date.toString()).toLocaleDateString();\r\n  }\r\n\r\n  getFunctionList(functions: string | undefined): string[] {\r\n    if (!functions) return [];\r\n    return functions.split('\\r\\n');\r\n  }\r\n\r\n  goBack() {\r\n    this.router.navigate(['/settings/plugins']);\r\n  }\r\n}\r\n"], "mappings": ";;;AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAASC,cAAc,EAAEC,MAAM,QAAQ,iBAAiB;AACxD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,eAAe,QAAQ,uBAAuB;AACvD,SAASC,gBAAgB,QAAQ,uBAAuB;AACxD,SAASC,kBAAkB,QAA2B,oDAAoD;AAgBnG,IAAMC,sBAAsB,GAA5B,MAAMA,sBAAsB;EAKjCC,YACUC,KAAqB,EACrBC,MAAc,EACdC,aAAiC,EACjCC,OAAyB;IAHzB,KAAAH,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IAPjB,KAAAC,OAAO,GAAG,IAAI;IACd,KAAAC,UAAU,GAAW,EAAE;EAOpB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACN,KAAK,CAACO,MAAM,CAACC,SAAS,CAACD,MAAM,IAAG;MACnC,IAAI,CAACF,UAAU,GAAGE,MAAM,CAAC,YAAY,CAAC;MACtC,IAAI,IAAI,CAACF,UAAU,EAAE;QACnB,IAAI,CAACI,iBAAiB,EAAE;;IAE5B,CAAC,CAAC;EACJ;EAEAA,iBAAiBA,CAAA;IACf,IAAI,CAACL,OAAO,GAAG,IAAI;IACnB,IAAI,CAACF,aAAa,CAACQ,SAAS,CAAC,IAAI,CAACL,UAAU,CAAC,CAACG,SAAS,CAAC;MACtDG,IAAI,EAAGC,MAAM,IAAI;QACf,IAAI,CAACA,MAAM,GAAGA,MAAM;QACpB,IAAI,CAACR,OAAO,GAAG,KAAK;MACtB,CAAC;MACDS,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACV,OAAO,CAACU,KAAK,CAAC,qCAAqC,IAAI,CAACR,UAAU,EAAE,CAAC;QAC1E,IAAI,CAACD,OAAO,GAAG,KAAK;QACpBU,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACvD;KACD,CAAC;EACJ;EAEAE,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAACH,MAAM,EAAEP,UAAU,EAAE;IAE9B,IAAI,CAACD,OAAO,GAAG,IAAI;IACnB,IAAI,IAAI,CAACQ,MAAM,CAACI,IAAI,EAAEC,WAAW,EAAE,KAAK,SAAS,EAAE;MACjD,IAAI,CAACf,aAAa,CAACgB,mBAAmB,CAAC,IAAI,CAACN,MAAM,CAACP,UAAU,CAAC,CAACG,SAAS,CAAC;QACvEG,IAAI,EAAGC,MAAM,IAAI;UACf,IAAI,CAACA,MAAM,GAAGA,MAAM;UACpB,IAAI,CAACR,OAAO,GAAG,KAAK;UACpB,IAAI,CAACD,OAAO,CAACgB,OAAO,CAAC,UAAU,IAAI,CAACP,MAAM,CAACP,UAAU,wBAAwB,CAAC;QAChF,CAAC;QACDQ,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACV,OAAO,CAACU,KAAK,CAAC,2BAA2B,IAAI,CAACD,MAAM,EAAEP,UAAU,EAAE,CAAC;UACxE,IAAI,CAACD,OAAO,GAAG,KAAK;UACpBU,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QACjD;OACD,CAAC;;EAEN;EAEAO,kBAAkBA,CAACJ,IAAwB;IACzC,IAAI,CAACA,IAAI,EAAE,OAAO,SAAS;IAC3B,QAAQA,IAAI,CAACC,WAAW,EAAE;MACxB,KAAK,SAAS;QACZ,OAAO,MAAM;MACf,KAAK,cAAc;QACjB,OAAO,OAAO;MAChB;QACE,OAAO,SAAS;;EAEtB;EAEAI,UAAUA,CAACC,IAAS;IAClB,IAAI,CAACA,IAAI,EAAE,OAAO,KAAK;IACvB,OAAO,IAAIC,IAAI,CAACD,IAAI,CAACE,QAAQ,EAAE,CAAC,CAACC,kBAAkB,EAAE;EACvD;EAEAC,eAAeA,CAACC,SAA6B;IAC3C,IAAI,CAACA,SAAS,EAAE,OAAO,EAAE;IACzB,OAAOA,SAAS,CAACC,KAAK,CAAC,MAAM,CAAC;EAChC;EAEAC,MAAMA,CAAA;IACJ,IAAI,CAAC5B,MAAM,CAAC6B,QAAQ,CAAC,CAAC,mBAAmB,CAAC,CAAC;EAC7C;;;;;;;;;;;;;AAhFWhC,sBAAsB,GAAAiC,UAAA,EAdlC5C,SAAS,CAAC;EACT6C,QAAQ,EAAE,oBAAoB;EAC9BC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACP5C,YAAY,EACZC,YAAY,EACZC,WAAW,EACXC,YAAY,EACZC,cAAc,EACdC,eAAe,CAChB;EACDwC,QAAA,EAAAC,oBAA8C;;CAE/C,CAAC,C,EACWtC,sBAAsB,CAiFlC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}