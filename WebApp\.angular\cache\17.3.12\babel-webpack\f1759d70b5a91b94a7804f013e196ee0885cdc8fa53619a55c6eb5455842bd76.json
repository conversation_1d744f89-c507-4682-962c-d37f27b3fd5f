{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/source/ai-hub/WebApp/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { TestBed } from '@angular/core/testing';\nimport { FileAddComponent } from './file-add.component';\ndescribe('FileAddComponent', () => {\n  let component;\n  let fixture;\n  beforeEach( /*#__PURE__*/_asyncToGenerator(function* () {\n    yield TestBed.configureTestingModule({\n      imports: [FileAddComponent]\n    }).compileComponents();\n    fixture = TestBed.createComponent(FileAddComponent);\n    component = fixture.componentInstance;\n    fixture.detectChanges();\n  }));\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "FileAddComponent", "describe", "component", "fixture", "beforeEach", "_asyncToGenerator", "configureTestingModule", "imports", "compileComponents", "createComponent", "componentInstance", "detectChanges", "it", "expect", "toBeTruthy"], "sources": ["C:\\Users\\<USER>\\source\\ai-hub\\WebApp\\src\\app\\admin\\settings\\Files\\file-add\\file-add.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed } from '@angular/core/testing';\r\n\r\nimport { FileAddComponent } from './file-add.component';\r\n\r\ndescribe('FileAddComponent', () => {\r\n  let component: FileAddComponent;\r\n  let fixture: ComponentFixture<FileAddComponent>;\r\n\r\n  beforeEach(async () => {\r\n    await TestBed.configureTestingModule({\r\n      imports: [FileAddComponent]\r\n    })\r\n    .compileComponents();\r\n    \r\n    fixture = TestBed.createComponent(FileAddComponent);\r\n    component = fixture.componentInstance;\r\n    fixture.detectChanges();\r\n  });\r\n\r\n  it('should create', () => {\r\n    expect(component).toBeTruthy();\r\n  });\r\n});\r\n"], "mappings": ";AAAA,SAA2BA,OAAO,QAAQ,uBAAuB;AAEjE,SAASC,gBAAgB,QAAQ,sBAAsB;AAEvDC,QAAQ,CAAC,kBAAkB,EAAE,MAAK;EAChC,IAAIC,SAA2B;EAC/B,IAAIC,OAA2C;EAE/CC,UAAU,eAAAC,iBAAA,CAAC,aAAW;IACpB,MAAMN,OAAO,CAACO,sBAAsB,CAAC;MACnCC,OAAO,EAAE,CAACP,gBAAgB;KAC3B,CAAC,CACDQ,iBAAiB,EAAE;IAEpBL,OAAO,GAAGJ,OAAO,CAACU,eAAe,CAACT,gBAAgB,CAAC;IACnDE,SAAS,GAAGC,OAAO,CAACO,iBAAiB;IACrCP,OAAO,CAACQ,aAAa,EAAE;EACzB,CAAC,EAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAACX,SAAS,CAAC,CAACY,UAAU,EAAE;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}