{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, Directive, Optional, Input, Host, ContentChild, SkipSelf, NgModule } from '@angular/core';\nimport { NzGridModule } from 'ng-zorro-antd/grid';\nimport { NgClass } from '@angular/common';\nimport { AbstractControl, NgModel, FormControlName, FormControlDirective, NgControl } from '@angular/forms';\nimport { Subject, Subscription } from 'rxjs';\nimport { filter, map, takeUntil, startWith, tap } from 'rxjs/operators';\nimport { helpMotion } from 'ng-zorro-antd/core/animation';\nimport * as i4 from 'ng-zorro-antd/core/form';\nimport { NzFormStatusService } from 'ng-zorro-antd/core/form';\nimport * as i5 from 'ng-zorro-antd/core/outlet';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport { InputBoolean, toBoolean } from 'ng-zorro-antd/core/util';\nimport * as i2$1 from 'ng-zorro-antd/i18n';\nimport { __decorate } from 'tslib';\nimport * as i1 from 'ng-zorro-antd/core/config';\nimport { WithConfig } from 'ng-zorro-antd/core/config';\nimport * as i2 from '@angular/cdk/bidi';\nimport * as i3 from 'ng-zorro-antd/icon';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport { NzTooltipDirective } from 'ng-zorro-antd/tooltip';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/** should add nz-row directive to host, track https://github.com/angular/angular/issues/8785 **/\nconst _c0 = [\"*\"];\nconst _c1 = a0 => [a0];\nconst _c2 = a0 => ({\n  $implicit: a0\n});\nfunction NzFormControlComponent_Conditional_3_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.innerTip);\n  }\n}\nfunction NzFormControlComponent_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 4);\n    i0.ɵɵtemplate(2, NzFormControlComponent_Conditional_3_ng_container_2_Template, 2, 1, \"ng-container\", 5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"@helpMotion\", undefined);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c1, \"ant-form-item-explain-\" + ctx_r0.status));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx_r0.innerTip)(\"nzStringTemplateOutletContext\", i0.ɵɵpureFunction1(6, _c2, ctx_r0.validateControl));\n  }\n}\nfunction NzFormControlComponent_Conditional_4_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.nzExtra);\n  }\n}\nfunction NzFormControlComponent_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵtemplate(1, NzFormControlComponent_Conditional_4_ng_container_1_Template, 2, 1, \"ng-container\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx_r0.nzExtra);\n  }\n}\nfunction NzFormLabelComponent_Conditional_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"span\", 2);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const tooltipIconType_r1 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzType\", tooltipIconType_r1)(\"nzTheme\", ctx_r1.tooltipIcon.theme);\n  }\n}\nfunction NzFormLabelComponent_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 0);\n    i0.ɵɵtemplate(1, NzFormLabelComponent_Conditional_2_ng_container_1_Template, 2, 2, \"ng-container\", 1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"nzTooltipTitle\", ctx_r1.nzTooltipTitle);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx_r1.tooltipIcon.type);\n  }\n}\nclass NzFormItemComponent {\n  setWithHelpViaTips(value) {\n    this.withHelpClass = value;\n    this.cdr.markForCheck();\n  }\n  setStatus(status) {\n    this.status = status;\n    this.cdr.markForCheck();\n  }\n  setHasFeedback(hasFeedback) {\n    this.hasFeedback = hasFeedback;\n    this.cdr.markForCheck();\n  }\n  constructor(cdr) {\n    this.cdr = cdr;\n    this.status = '';\n    this.hasFeedback = false;\n    this.withHelpClass = false;\n    this.destroy$ = new Subject();\n  }\n  ngOnDestroy() {\n    this.destroy$.next(true);\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzFormItemComponent_Factory(t) {\n      return new (t || NzFormItemComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzFormItemComponent,\n      selectors: [[\"nz-form-item\"]],\n      hostAttrs: [1, \"ant-form-item\"],\n      hostVars: 12,\n      hostBindings: function NzFormItemComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-form-item-has-success\", ctx.status === \"success\")(\"ant-form-item-has-warning\", ctx.status === \"warning\")(\"ant-form-item-has-error\", ctx.status === \"error\")(\"ant-form-item-is-validating\", ctx.status === \"validating\")(\"ant-form-item-has-feedback\", ctx.hasFeedback && ctx.status)(\"ant-form-item-with-help\", ctx.withHelpClass);\n        }\n      },\n      exportAs: [\"nzFormItem\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function NzFormItemComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzFormItemComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-form-item',\n      exportAs: 'nzFormItem',\n      preserveWhitespaces: false,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'ant-form-item',\n        '[class.ant-form-item-has-success]': 'status === \"success\"',\n        '[class.ant-form-item-has-warning]': 'status === \"warning\"',\n        '[class.ant-form-item-has-error]': 'status === \"error\"',\n        '[class.ant-form-item-is-validating]': 'status === \"validating\"',\n        '[class.ant-form-item-has-feedback]': 'hasFeedback && status',\n        '[class.ant-form-item-with-help]': 'withHelpClass'\n      },\n      template: ` <ng-content></ng-content> `,\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }], null);\n})();\nconst NZ_CONFIG_MODULE_NAME = 'form';\nconst DefaultTooltipIcon = {\n  type: 'question-circle',\n  theme: 'outline'\n};\nclass NzFormDirective {\n  getInputObservable(changeType) {\n    return this.inputChanges$.pipe(filter(changes => changeType in changes), map(value => value[changeType]));\n  }\n  constructor(nzConfigService, directionality) {\n    this.nzConfigService = nzConfigService;\n    this.directionality = directionality;\n    this._nzModuleName = NZ_CONFIG_MODULE_NAME;\n    this.nzLayout = 'horizontal';\n    this.nzNoColon = false;\n    this.nzAutoTips = {};\n    this.nzDisableAutoTips = false;\n    this.nzTooltipIcon = DefaultTooltipIcon;\n    this.nzLabelAlign = 'right';\n    this.nzLabelWrap = false;\n    this.dir = 'ltr';\n    this.destroy$ = new Subject();\n    this.inputChanges$ = new Subject();\n    this.dir = this.directionality.value;\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n    });\n  }\n  ngOnChanges(changes) {\n    this.inputChanges$.next(changes);\n  }\n  ngOnDestroy() {\n    this.inputChanges$.complete();\n    this.destroy$.next(true);\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzFormDirective_Factory(t) {\n      return new (t || NzFormDirective)(i0.ɵɵdirectiveInject(i1.NzConfigService), i0.ɵɵdirectiveInject(i2.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzFormDirective,\n      selectors: [[\"\", \"nz-form\", \"\"]],\n      hostAttrs: [1, \"ant-form\"],\n      hostVars: 8,\n      hostBindings: function NzFormDirective_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-form-horizontal\", ctx.nzLayout === \"horizontal\")(\"ant-form-vertical\", ctx.nzLayout === \"vertical\")(\"ant-form-inline\", ctx.nzLayout === \"inline\")(\"ant-form-rtl\", ctx.dir === \"rtl\");\n        }\n      },\n      inputs: {\n        nzLayout: \"nzLayout\",\n        nzNoColon: \"nzNoColon\",\n        nzAutoTips: \"nzAutoTips\",\n        nzDisableAutoTips: \"nzDisableAutoTips\",\n        nzTooltipIcon: \"nzTooltipIcon\",\n        nzLabelAlign: \"nzLabelAlign\",\n        nzLabelWrap: \"nzLabelWrap\"\n      },\n      exportAs: [\"nzForm\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n__decorate([WithConfig(), InputBoolean()], NzFormDirective.prototype, \"nzNoColon\", void 0);\n__decorate([WithConfig()], NzFormDirective.prototype, \"nzAutoTips\", void 0);\n__decorate([InputBoolean()], NzFormDirective.prototype, \"nzDisableAutoTips\", void 0);\n__decorate([WithConfig()], NzFormDirective.prototype, \"nzTooltipIcon\", void 0);\n__decorate([WithConfig(), InputBoolean()], NzFormDirective.prototype, \"nzLabelWrap\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzFormDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[nz-form]',\n      exportAs: 'nzForm',\n      host: {\n        class: 'ant-form',\n        '[class.ant-form-horizontal]': `nzLayout === 'horizontal'`,\n        '[class.ant-form-vertical]': `nzLayout === 'vertical'`,\n        '[class.ant-form-inline]': `nzLayout === 'inline'`,\n        '[class.ant-form-rtl]': `dir === 'rtl'`\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: i1.NzConfigService\n  }, {\n    type: i2.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    nzLayout: [{\n      type: Input\n    }],\n    nzNoColon: [{\n      type: Input\n    }],\n    nzAutoTips: [{\n      type: Input\n    }],\n    nzDisableAutoTips: [{\n      type: Input\n    }],\n    nzTooltipIcon: [{\n      type: Input\n    }],\n    nzLabelAlign: [{\n      type: Input\n    }],\n    nzLabelWrap: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzFormControlComponent {\n  get disableAutoTips() {\n    return this.nzDisableAutoTips !== 'default' ? toBoolean(this.nzDisableAutoTips) : this.nzFormDirective?.nzDisableAutoTips;\n  }\n  set nzHasFeedback(value) {\n    this._hasFeedback = toBoolean(value);\n    this.nzFormStatusService.formStatusChanges.next({\n      status: this.status,\n      hasFeedback: this._hasFeedback\n    });\n    if (this.nzFormItemComponent) {\n      this.nzFormItemComponent.setHasFeedback(this._hasFeedback);\n    }\n  }\n  get nzHasFeedback() {\n    return this._hasFeedback;\n  }\n  set nzValidateStatus(value) {\n    if (value instanceof AbstractControl || value instanceof NgModel) {\n      this.validateControl = value;\n      this.validateString = null;\n      this.watchControl();\n    } else if (value instanceof FormControlName) {\n      this.validateControl = value.control;\n      this.validateString = null;\n      this.watchControl();\n    } else {\n      this.validateString = value;\n      this.validateControl = null;\n      this.setStatus();\n    }\n  }\n  watchControl() {\n    this.validateChanges.unsubscribe();\n    /** miss detect https://github.com/angular/angular/issues/10887 **/\n    if (this.validateControl && this.validateControl.statusChanges) {\n      this.validateChanges = this.validateControl.statusChanges.pipe(startWith(null), takeUntil(this.destroyed$)).subscribe(() => {\n        if (!this.disableAutoTips) {\n          this.updateAutoErrorTip();\n        }\n        this.setStatus();\n        this.cdr.markForCheck();\n      });\n    }\n  }\n  setStatus() {\n    this.status = this.getControlStatus(this.validateString);\n    this.innerTip = this.getInnerTip(this.status);\n    this.nzFormStatusService.formStatusChanges.next({\n      status: this.status,\n      hasFeedback: this.nzHasFeedback\n    });\n    if (this.nzFormItemComponent) {\n      this.nzFormItemComponent.setWithHelpViaTips(!!this.innerTip);\n      this.nzFormItemComponent.setStatus(this.status);\n    }\n  }\n  getControlStatus(validateString) {\n    let status;\n    if (validateString === 'warning' || this.validateControlStatus('INVALID', 'warning')) {\n      status = 'warning';\n    } else if (validateString === 'error' || this.validateControlStatus('INVALID')) {\n      status = 'error';\n    } else if (validateString === 'validating' || validateString === 'pending' || this.validateControlStatus('PENDING')) {\n      status = 'validating';\n    } else if (validateString === 'success' || this.validateControlStatus('VALID')) {\n      status = 'success';\n    } else {\n      status = '';\n    }\n    return status;\n  }\n  validateControlStatus(validStatus, statusType) {\n    if (!this.validateControl) {\n      return false;\n    } else {\n      const {\n        dirty,\n        touched,\n        status\n      } = this.validateControl;\n      return (!!dirty || !!touched) && (statusType ? this.validateControl.hasError(statusType) : status === validStatus);\n    }\n  }\n  getInnerTip(status) {\n    switch (status) {\n      case 'error':\n        return !this.disableAutoTips && this.autoErrorTip || this.nzErrorTip || null;\n      case 'validating':\n        return this.nzValidatingTip || null;\n      case 'success':\n        return this.nzSuccessTip || null;\n      case 'warning':\n        return this.nzWarningTip || null;\n      default:\n        return null;\n    }\n  }\n  updateAutoErrorTip() {\n    if (this.validateControl) {\n      const errors = this.validateControl.errors || {};\n      let autoErrorTip = '';\n      for (const key in errors) {\n        if (errors.hasOwnProperty(key)) {\n          autoErrorTip = errors[key]?.[this.localeId] ?? this.nzAutoTips?.[this.localeId]?.[key] ?? this.nzAutoTips.default?.[key] ?? this.nzFormDirective?.nzAutoTips?.[this.localeId]?.[key] ?? this.nzFormDirective?.nzAutoTips.default?.[key];\n        }\n        if (!!autoErrorTip) {\n          break;\n        }\n      }\n      this.autoErrorTip = autoErrorTip;\n    }\n  }\n  subscribeAutoTips(observable) {\n    observable?.pipe(takeUntil(this.destroyed$)).subscribe(() => {\n      if (!this.disableAutoTips) {\n        this.updateAutoErrorTip();\n        this.setStatus();\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  constructor(nzFormItemComponent, cdr, i18n, nzFormDirective, nzFormStatusService) {\n    this.nzFormItemComponent = nzFormItemComponent;\n    this.cdr = cdr;\n    this.nzFormDirective = nzFormDirective;\n    this.nzFormStatusService = nzFormStatusService;\n    this._hasFeedback = false;\n    this.validateChanges = Subscription.EMPTY;\n    this.validateString = null;\n    this.destroyed$ = new Subject();\n    this.status = '';\n    this.validateControl = null;\n    this.innerTip = null;\n    this.nzAutoTips = {};\n    this.nzDisableAutoTips = 'default';\n    this.subscribeAutoTips(i18n.localeChange.pipe(tap(locale => this.localeId = locale.locale)));\n    this.subscribeAutoTips(this.nzFormDirective?.getInputObservable('nzAutoTips'));\n    this.subscribeAutoTips(this.nzFormDirective?.getInputObservable('nzDisableAutoTips').pipe(filter(() => this.nzDisableAutoTips === 'default')));\n  }\n  ngOnChanges(changes) {\n    const {\n      nzDisableAutoTips,\n      nzAutoTips,\n      nzSuccessTip,\n      nzWarningTip,\n      nzErrorTip,\n      nzValidatingTip\n    } = changes;\n    if (nzDisableAutoTips || nzAutoTips) {\n      this.updateAutoErrorTip();\n      this.setStatus();\n    } else if (nzSuccessTip || nzWarningTip || nzErrorTip || nzValidatingTip) {\n      this.setStatus();\n    }\n  }\n  ngOnInit() {\n    this.setStatus();\n  }\n  ngOnDestroy() {\n    this.destroyed$.next();\n    this.destroyed$.complete();\n  }\n  ngAfterContentInit() {\n    if (!this.validateControl && !this.validateString) {\n      if (this.defaultValidateControl instanceof FormControlDirective) {\n        this.nzValidateStatus = this.defaultValidateControl.control;\n      } else {\n        this.nzValidateStatus = this.defaultValidateControl;\n      }\n    }\n  }\n  static {\n    this.ɵfac = function NzFormControlComponent_Factory(t) {\n      return new (t || NzFormControlComponent)(i0.ɵɵdirectiveInject(NzFormItemComponent, 9), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2$1.NzI18nService), i0.ɵɵdirectiveInject(NzFormDirective, 8), i0.ɵɵdirectiveInject(i4.NzFormStatusService));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzFormControlComponent,\n      selectors: [[\"nz-form-control\"]],\n      contentQueries: function NzFormControlComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, NgControl, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.defaultValidateControl = _t.first);\n        }\n      },\n      hostAttrs: [1, \"ant-form-item-control\"],\n      inputs: {\n        nzSuccessTip: \"nzSuccessTip\",\n        nzWarningTip: \"nzWarningTip\",\n        nzErrorTip: \"nzErrorTip\",\n        nzValidatingTip: \"nzValidatingTip\",\n        nzExtra: \"nzExtra\",\n        nzAutoTips: \"nzAutoTips\",\n        nzDisableAutoTips: \"nzDisableAutoTips\",\n        nzHasFeedback: \"nzHasFeedback\",\n        nzValidateStatus: \"nzValidateStatus\"\n      },\n      exportAs: [\"nzFormControl\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([NzFormStatusService]), i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 5,\n      vars: 2,\n      consts: [[1, \"ant-form-item-control-input\"], [1, \"ant-form-item-control-input-content\"], [1, \"ant-form-item-explain\", \"ant-form-item-explain-connected\"], [1, \"ant-form-item-extra\"], [\"role\", \"alert\", 3, \"ngClass\"], [4, \"nzStringTemplateOutlet\", \"nzStringTemplateOutletContext\"], [4, \"nzStringTemplateOutlet\"]],\n      template: function NzFormControlComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵprojection(2);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(3, NzFormControlComponent_Conditional_3_Template, 3, 8, \"div\", 2)(4, NzFormControlComponent_Conditional_4_Template, 2, 1, \"div\", 3);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵconditional(3, ctx.innerTip ? 3 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(4, ctx.nzExtra ? 4 : -1);\n        }\n      },\n      dependencies: [NgClass, NzOutletModule, i5.NzStringTemplateOutletDirective],\n      encapsulation: 2,\n      data: {\n        animation: [helpMotion]\n      },\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzFormControlComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-form-control',\n      exportAs: 'nzFormControl',\n      preserveWhitespaces: false,\n      animations: [helpMotion],\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    <div class=\"ant-form-item-control-input\">\n      <div class=\"ant-form-item-control-input-content\">\n        <ng-content></ng-content>\n      </div>\n    </div>\n    @if (innerTip) {\n      <div @helpMotion class=\"ant-form-item-explain ant-form-item-explain-connected\">\n        <div role=\"alert\" [ngClass]=\"['ant-form-item-explain-' + status]\">\n          <ng-container *nzStringTemplateOutlet=\"innerTip; context: { $implicit: validateControl }\">{{\n            innerTip\n          }}</ng-container>\n        </div>\n      </div>\n    }\n\n    @if (nzExtra) {\n      <div class=\"ant-form-item-extra\">\n        <ng-container *nzStringTemplateOutlet=\"nzExtra\">{{ nzExtra }}</ng-container>\n      </div>\n    }\n  `,\n      providers: [NzFormStatusService],\n      host: {\n        class: 'ant-form-item-control'\n      },\n      imports: [NgClass, NzOutletModule],\n      standalone: true\n    }]\n  }], () => [{\n    type: NzFormItemComponent,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Host\n    }]\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i2$1.NzI18nService\n  }, {\n    type: NzFormDirective,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i4.NzFormStatusService\n  }], {\n    defaultValidateControl: [{\n      type: ContentChild,\n      args: [NgControl, {\n        static: false\n      }]\n    }],\n    nzSuccessTip: [{\n      type: Input\n    }],\n    nzWarningTip: [{\n      type: Input\n    }],\n    nzErrorTip: [{\n      type: Input\n    }],\n    nzValidatingTip: [{\n      type: Input\n    }],\n    nzExtra: [{\n      type: Input\n    }],\n    nzAutoTips: [{\n      type: Input\n    }],\n    nzDisableAutoTips: [{\n      type: Input\n    }],\n    nzHasFeedback: [{\n      type: Input\n    }],\n    nzValidateStatus: [{\n      type: Input\n    }]\n  });\n})();\nfunction toTooltipIcon(value) {\n  const icon = typeof value === 'string' ? {\n    type: value\n  } : value;\n  return {\n    ...DefaultTooltipIcon,\n    ...icon\n  };\n}\nclass NzFormLabelComponent {\n  set nzNoColon(value) {\n    this.noColon = toBoolean(value);\n  }\n  get nzNoColon() {\n    return this.noColon !== 'default' ? this.noColon : this.nzFormDirective?.nzNoColon;\n  }\n  set nzTooltipIcon(value) {\n    this._tooltipIcon = toTooltipIcon(value);\n  }\n  // due to 'get' and 'set' accessor must have the same type, so it was renamed to `tooltipIcon`\n  get tooltipIcon() {\n    return this._tooltipIcon !== 'default' ? this._tooltipIcon : toTooltipIcon(this.nzFormDirective?.nzTooltipIcon || DefaultTooltipIcon);\n  }\n  set nzLabelAlign(value) {\n    this.labelAlign = value;\n  }\n  get nzLabelAlign() {\n    return this.labelAlign !== 'default' ? this.labelAlign : this.nzFormDirective?.nzLabelAlign || 'right';\n  }\n  set nzLabelWrap(value) {\n    this.labelWrap = toBoolean(value);\n  }\n  get nzLabelWrap() {\n    return this.labelWrap !== 'default' ? this.labelWrap : this.nzFormDirective?.nzLabelWrap;\n  }\n  constructor(cdr, nzFormDirective) {\n    this.cdr = cdr;\n    this.nzFormDirective = nzFormDirective;\n    this.nzRequired = false;\n    this.noColon = 'default';\n    this._tooltipIcon = 'default';\n    this.labelAlign = 'default';\n    this.labelWrap = 'default';\n    this.destroy$ = new Subject();\n    if (this.nzFormDirective) {\n      this.nzFormDirective.getInputObservable('nzNoColon').pipe(filter(() => this.noColon === 'default'), takeUntil(this.destroy$)).subscribe(() => this.cdr.markForCheck());\n      this.nzFormDirective.getInputObservable('nzTooltipIcon').pipe(filter(() => this._tooltipIcon === 'default'), takeUntil(this.destroy$)).subscribe(() => this.cdr.markForCheck());\n      this.nzFormDirective.getInputObservable('nzLabelAlign').pipe(filter(() => this.labelAlign === 'default'), takeUntil(this.destroy$)).subscribe(() => this.cdr.markForCheck());\n      this.nzFormDirective.getInputObservable('nzLabelWrap').pipe(filter(() => this.labelWrap === 'default'), takeUntil(this.destroy$)).subscribe(() => this.cdr.markForCheck());\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next(true);\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzFormLabelComponent_Factory(t) {\n      return new (t || NzFormLabelComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(NzFormDirective, 12));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzFormLabelComponent,\n      selectors: [[\"nz-form-label\"]],\n      hostAttrs: [1, \"ant-form-item-label\"],\n      hostVars: 4,\n      hostBindings: function NzFormLabelComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-form-item-label-left\", ctx.nzLabelAlign === \"left\")(\"ant-form-item-label-wrap\", ctx.nzLabelWrap);\n        }\n      },\n      inputs: {\n        nzFor: \"nzFor\",\n        nzRequired: \"nzRequired\",\n        nzNoColon: \"nzNoColon\",\n        nzTooltipTitle: \"nzTooltipTitle\",\n        nzTooltipIcon: \"nzTooltipIcon\",\n        nzLabelAlign: \"nzLabelAlign\",\n        nzLabelWrap: \"nzLabelWrap\"\n      },\n      exportAs: [\"nzFormLabel\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 3,\n      vars: 6,\n      consts: [[\"nz-tooltip\", \"\", 1, \"ant-form-item-tooltip\", 3, \"nzTooltipTitle\"], [4, \"nzStringTemplateOutlet\"], [\"nz-icon\", \"\", 3, \"nzType\", \"nzTheme\"]],\n      template: function NzFormLabelComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"label\");\n          i0.ɵɵprojection(1);\n          i0.ɵɵtemplate(2, NzFormLabelComponent_Conditional_2_Template, 2, 2, \"span\", 0);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-form-item-no-colon\", ctx.nzNoColon)(\"ant-form-item-required\", ctx.nzRequired);\n          i0.ɵɵattribute(\"for\", ctx.nzFor);\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(2, ctx.nzTooltipTitle ? 2 : -1);\n        }\n      },\n      dependencies: [NzOutletModule, i5.NzStringTemplateOutletDirective, NzTooltipDirective, NzIconModule, i3.NzIconDirective],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([InputBoolean()], NzFormLabelComponent.prototype, \"nzRequired\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzFormLabelComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-form-label',\n      exportAs: 'nzFormLabel',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    <label [attr.for]=\"nzFor\" [class.ant-form-item-no-colon]=\"nzNoColon\" [class.ant-form-item-required]=\"nzRequired\">\n      <ng-content></ng-content>\n      @if (nzTooltipTitle) {\n        <span class=\"ant-form-item-tooltip\" nz-tooltip [nzTooltipTitle]=\"nzTooltipTitle\">\n          <ng-container *nzStringTemplateOutlet=\"tooltipIcon.type; let tooltipIconType\">\n            <span nz-icon [nzType]=\"tooltipIconType\" [nzTheme]=\"tooltipIcon.theme\"></span>\n          </ng-container>\n        </span>\n      }\n    </label>\n  `,\n      host: {\n        class: 'ant-form-item-label',\n        '[class.ant-form-item-label-left]': `nzLabelAlign === 'left'`,\n        '[class.ant-form-item-label-wrap]': `nzLabelWrap`\n      },\n      imports: [NzOutletModule, NzTooltipDirective, NzIconModule],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: NzFormDirective,\n    decorators: [{\n      type: Optional\n    }, {\n      type: SkipSelf\n    }]\n  }], {\n    nzFor: [{\n      type: Input\n    }],\n    nzRequired: [{\n      type: Input\n    }],\n    nzNoColon: [{\n      type: Input\n    }],\n    nzTooltipTitle: [{\n      type: Input\n    }],\n    nzTooltipIcon: [{\n      type: Input\n    }],\n    nzLabelAlign: [{\n      type: Input\n    }],\n    nzLabelWrap: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzFormSplitComponent {\n  static {\n    this.ɵfac = function NzFormSplitComponent_Factory(t) {\n      return new (t || NzFormSplitComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzFormSplitComponent,\n      selectors: [[\"nz-form-split\"]],\n      hostAttrs: [1, \"ant-form-split\"],\n      exportAs: [\"nzFormSplit\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function NzFormSplitComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzFormSplitComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-form-split',\n      exportAs: 'nzFormSplit',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: ` <ng-content></ng-content> `,\n      host: {\n        class: 'ant-form-split'\n      },\n      standalone: true\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzFormTextComponent {\n  static {\n    this.ɵfac = function NzFormTextComponent_Factory(t) {\n      return new (t || NzFormTextComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzFormTextComponent,\n      selectors: [[\"nz-form-text\"]],\n      hostAttrs: [1, \"ant-form-text\"],\n      exportAs: [\"nzFormText\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function NzFormTextComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzFormTextComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-form-text',\n      exportAs: 'nzFormText',\n      preserveWhitespaces: false,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: ` <ng-content></ng-content> `,\n      host: {\n        class: 'ant-form-text'\n      },\n      standalone: true\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzFormModule {\n  static {\n    this.ɵfac = function NzFormModule_Factory(t) {\n      return new (t || NzFormModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzFormModule,\n      imports: [NzFormDirective, NzFormItemComponent, NzFormLabelComponent, NzFormControlComponent, NzFormTextComponent, NzFormSplitComponent],\n      exports: [NzGridModule, NzFormDirective, NzFormItemComponent, NzFormLabelComponent, NzFormControlComponent, NzFormTextComponent, NzFormSplitComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [NzFormLabelComponent, NzFormControlComponent, NzGridModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzFormModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzFormDirective, NzFormItemComponent, NzFormLabelComponent, NzFormControlComponent, NzFormTextComponent, NzFormSplitComponent],\n      exports: [NzGridModule, NzFormDirective, NzFormItemComponent, NzFormLabelComponent, NzFormControlComponent, NzFormTextComponent, NzFormSplitComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { DefaultTooltipIcon, NzFormControlComponent, NzFormDirective, NzFormItemComponent, NzFormLabelComponent, NzFormModule, NzFormSplitComponent, NzFormTextComponent };", "map": {"version": 3, "names": ["i0", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Directive", "Optional", "Input", "Host", "ContentChild", "SkipSelf", "NgModule", "NzGridModule", "Ng<PERSON><PERSON>", "AbstractControl", "NgModel", "FormControlName", "FormControlDirective", "NgControl", "Subject", "Subscription", "filter", "map", "takeUntil", "startWith", "tap", "helpMotion", "i4", "NzFormStatusService", "i5", "NzOutletModule", "InputBoolean", "toBoolean", "i2$1", "__decorate", "i1", "WithConfig", "i2", "i3", "NzIconModule", "NzTooltipDirective", "_c0", "_c1", "a0", "_c2", "$implicit", "NzFormControlComponent_Conditional_3_ng_container_2_Template", "rf", "ctx", "ɵɵelementContainerStart", "ɵɵtext", "ɵɵelementContainerEnd", "ctx_r0", "ɵɵnextContext", "ɵɵadvance", "ɵɵtextInterpolate", "innerTip", "NzFormControlComponent_Conditional_3_Template", "ɵɵelementStart", "ɵɵtemplate", "ɵɵelementEnd", "ɵɵproperty", "undefined", "ɵɵpureFunction1", "status", "validateControl", "NzFormControlComponent_Conditional_4_ng_container_1_Template", "nzExtra", "NzFormControlComponent_Conditional_4_Template", "NzFormLabelComponent_Conditional_2_ng_container_1_Template", "ɵɵelement", "tooltipIconType_r1", "ctx_r1", "tooltipIcon", "theme", "NzFormLabelComponent_Conditional_2_Template", "nzTooltipTitle", "type", "NzFormItemComponent", "setWithHelpViaTips", "value", "withHelpClass", "cdr", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setStatus", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hasFeedback", "constructor", "destroy$", "ngOnDestroy", "next", "complete", "ɵfac", "NzFormItemComponent_Factory", "t", "ɵɵdirectiveInject", "ChangeDetectorRef", "ɵcmp", "ɵɵdefineComponent", "selectors", "hostAttrs", "hostVars", "hostBindings", "NzFormItemComponent_HostBindings", "ɵɵclassProp", "exportAs", "standalone", "features", "ɵɵStandaloneFeature", "ngContentSelectors", "decls", "vars", "template", "NzFormItemComponent_Template", "ɵɵprojectionDef", "ɵɵprojection", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "preserveWhitespaces", "OnPush", "None", "host", "class", "NZ_CONFIG_MODULE_NAME", "DefaultTooltipIcon", "NzFormDirective", "getInputObservable", "changeType", "inputChanges$", "pipe", "changes", "nzConfigService", "directionality", "_nzModuleName", "nzLayout", "nzNoColon", "nzAutoTips", "nzDisableAutoTips", "nzTooltipIcon", "nzLabelAlign", "nzLabelWrap", "dir", "change", "subscribe", "direction", "ngOnChanges", "NzFormDirective_Factory", "NzConfigService", "Directionality", "ɵdir", "ɵɵdefineDirective", "NzFormDirective_HostBindings", "inputs", "ɵɵNgOnChangesFeature", "prototype", "decorators", "NzFormControlComponent", "disableAutoTips", "nzFormDirective", "nzHasFeedback", "_hasFeedback", "nzFormStatusService", "formStatusChanges", "nzFormItemComponent", "nzValidateStatus", "validateString", "watchControl", "control", "validateChanges", "unsubscribe", "statusChanges", "destroyed$", "updateAutoErrorTip", "getControlStatus", "getInnerTip", "validateControlStatus", "validStatus", "statusType", "dirty", "touched", "<PERSON><PERSON><PERSON><PERSON>", "autoErrorTip", "nzErrorTip", "nzValidatingTip", "nzSuccessTip", "nzWarningTip", "errors", "key", "hasOwnProperty", "localeId", "default", "subscribeAutoTips", "observable", "i18n", "EMPTY", "localeChange", "locale", "ngOnInit", "ngAfterContentInit", "defaultValidateControl", "NzFormControlComponent_Factory", "NzI18nService", "contentQueries", "NzFormControlComponent_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "ɵɵProvidersFeature", "consts", "NzFormControlComponent_Template", "ɵɵconditional", "dependencies", "NzStringTemplateOutletDirective", "data", "animation", "animations", "providers", "imports", "static", "toTooltipIcon", "icon", "NzFormLabelComponent", "noColon", "_tooltipIcon", "labelAlign", "labelWrap", "nzRequired", "NzFormLabelComponent_Factory", "NzFormLabelComponent_HostBindings", "nzFor", "NzFormLabelComponent_Template", "ɵɵattribute", "NzIconDirective", "NzFormSplitComponent", "NzFormSplitComponent_Factory", "NzFormSplitComponent_Template", "NzFormTextComponent", "NzFormTextComponent_Factory", "NzFormTextComponent_Template", "NzFormModule", "NzFormModule_Factory", "ɵmod", "ɵɵdefineNgModule", "exports", "ɵinj", "ɵɵdefineInjector"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-form.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, Directive, Optional, Input, Host, ContentChild, SkipSelf, NgModule } from '@angular/core';\nimport { NzGridModule } from 'ng-zorro-antd/grid';\nimport { NgClass } from '@angular/common';\nimport { AbstractControl, NgModel, FormControlName, FormControlDirective, NgControl } from '@angular/forms';\nimport { Subject, Subscription } from 'rxjs';\nimport { filter, map, takeUntil, startWith, tap } from 'rxjs/operators';\nimport { helpMotion } from 'ng-zorro-antd/core/animation';\nimport * as i4 from 'ng-zorro-antd/core/form';\nimport { NzFormStatusService } from 'ng-zorro-antd/core/form';\nimport * as i5 from 'ng-zorro-antd/core/outlet';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport { InputBoolean, toBoolean } from 'ng-zorro-antd/core/util';\nimport * as i2$1 from 'ng-zorro-antd/i18n';\nimport { __decorate } from 'tslib';\nimport * as i1 from 'ng-zorro-antd/core/config';\nimport { WithConfig } from 'ng-zorro-antd/core/config';\nimport * as i2 from '@angular/cdk/bidi';\nimport * as i3 from 'ng-zorro-antd/icon';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport { NzTooltipDirective } from 'ng-zorro-antd/tooltip';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/** should add nz-row directive to host, track https://github.com/angular/angular/issues/8785 **/\nclass NzFormItemComponent {\n    setWithHelpViaTips(value) {\n        this.withHelpClass = value;\n        this.cdr.markForCheck();\n    }\n    setStatus(status) {\n        this.status = status;\n        this.cdr.markForCheck();\n    }\n    setHasFeedback(hasFeedback) {\n        this.hasFeedback = hasFeedback;\n        this.cdr.markForCheck();\n    }\n    constructor(cdr) {\n        this.cdr = cdr;\n        this.status = '';\n        this.hasFeedback = false;\n        this.withHelpClass = false;\n        this.destroy$ = new Subject();\n    }\n    ngOnDestroy() {\n        this.destroy$.next(true);\n        this.destroy$.complete();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzFormItemComponent, deps: [{ token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzFormItemComponent, isStandalone: true, selector: \"nz-form-item\", host: { properties: { \"class.ant-form-item-has-success\": \"status === \\\"success\\\"\", \"class.ant-form-item-has-warning\": \"status === \\\"warning\\\"\", \"class.ant-form-item-has-error\": \"status === \\\"error\\\"\", \"class.ant-form-item-is-validating\": \"status === \\\"validating\\\"\", \"class.ant-form-item-has-feedback\": \"hasFeedback && status\", \"class.ant-form-item-with-help\": \"withHelpClass\" }, classAttribute: \"ant-form-item\" }, exportAs: [\"nzFormItem\"], ngImport: i0, template: ` <ng-content></ng-content> `, isInline: true, changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzFormItemComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'nz-form-item',\n                    exportAs: 'nzFormItem',\n                    preserveWhitespaces: false,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    encapsulation: ViewEncapsulation.None,\n                    host: {\n                        class: 'ant-form-item',\n                        '[class.ant-form-item-has-success]': 'status === \"success\"',\n                        '[class.ant-form-item-has-warning]': 'status === \"warning\"',\n                        '[class.ant-form-item-has-error]': 'status === \"error\"',\n                        '[class.ant-form-item-is-validating]': 'status === \"validating\"',\n                        '[class.ant-form-item-has-feedback]': 'hasFeedback && status',\n                        '[class.ant-form-item-with-help]': 'withHelpClass'\n                    },\n                    template: ` <ng-content></ng-content> `,\n                    standalone: true\n                }]\n        }], ctorParameters: () => [{ type: i0.ChangeDetectorRef }] });\n\nconst NZ_CONFIG_MODULE_NAME = 'form';\nconst DefaultTooltipIcon = {\n    type: 'question-circle',\n    theme: 'outline'\n};\nclass NzFormDirective {\n    getInputObservable(changeType) {\n        return this.inputChanges$.pipe(filter(changes => changeType in changes), map(value => value[changeType]));\n    }\n    constructor(nzConfigService, directionality) {\n        this.nzConfigService = nzConfigService;\n        this.directionality = directionality;\n        this._nzModuleName = NZ_CONFIG_MODULE_NAME;\n        this.nzLayout = 'horizontal';\n        this.nzNoColon = false;\n        this.nzAutoTips = {};\n        this.nzDisableAutoTips = false;\n        this.nzTooltipIcon = DefaultTooltipIcon;\n        this.nzLabelAlign = 'right';\n        this.nzLabelWrap = false;\n        this.dir = 'ltr';\n        this.destroy$ = new Subject();\n        this.inputChanges$ = new Subject();\n        this.dir = this.directionality.value;\n        this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe((direction) => {\n            this.dir = direction;\n        });\n    }\n    ngOnChanges(changes) {\n        this.inputChanges$.next(changes);\n    }\n    ngOnDestroy() {\n        this.inputChanges$.complete();\n        this.destroy$.next(true);\n        this.destroy$.complete();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzFormDirective, deps: [{ token: i1.NzConfigService }, { token: i2.Directionality, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzFormDirective, isStandalone: true, selector: \"[nz-form]\", inputs: { nzLayout: \"nzLayout\", nzNoColon: \"nzNoColon\", nzAutoTips: \"nzAutoTips\", nzDisableAutoTips: \"nzDisableAutoTips\", nzTooltipIcon: \"nzTooltipIcon\", nzLabelAlign: \"nzLabelAlign\", nzLabelWrap: \"nzLabelWrap\" }, host: { properties: { \"class.ant-form-horizontal\": \"nzLayout === 'horizontal'\", \"class.ant-form-vertical\": \"nzLayout === 'vertical'\", \"class.ant-form-inline\": \"nzLayout === 'inline'\", \"class.ant-form-rtl\": \"dir === 'rtl'\" }, classAttribute: \"ant-form\" }, exportAs: [\"nzForm\"], usesOnChanges: true, ngImport: i0 }); }\n}\n__decorate([\n    WithConfig(),\n    InputBoolean()\n], NzFormDirective.prototype, \"nzNoColon\", void 0);\n__decorate([\n    WithConfig()\n], NzFormDirective.prototype, \"nzAutoTips\", void 0);\n__decorate([\n    InputBoolean()\n], NzFormDirective.prototype, \"nzDisableAutoTips\", void 0);\n__decorate([\n    WithConfig()\n], NzFormDirective.prototype, \"nzTooltipIcon\", void 0);\n__decorate([\n    WithConfig(),\n    InputBoolean()\n], NzFormDirective.prototype, \"nzLabelWrap\", void 0);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzFormDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[nz-form]',\n                    exportAs: 'nzForm',\n                    host: {\n                        class: 'ant-form',\n                        '[class.ant-form-horizontal]': `nzLayout === 'horizontal'`,\n                        '[class.ant-form-vertical]': `nzLayout === 'vertical'`,\n                        '[class.ant-form-inline]': `nzLayout === 'inline'`,\n                        '[class.ant-form-rtl]': `dir === 'rtl'`\n                    },\n                    standalone: true\n                }]\n        }], ctorParameters: () => [{ type: i1.NzConfigService }, { type: i2.Directionality, decorators: [{\n                    type: Optional\n                }] }], propDecorators: { nzLayout: [{\n                type: Input\n            }], nzNoColon: [{\n                type: Input\n            }], nzAutoTips: [{\n                type: Input\n            }], nzDisableAutoTips: [{\n                type: Input\n            }], nzTooltipIcon: [{\n                type: Input\n            }], nzLabelAlign: [{\n                type: Input\n            }], nzLabelWrap: [{\n                type: Input\n            }] } });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzFormControlComponent {\n    get disableAutoTips() {\n        return this.nzDisableAutoTips !== 'default'\n            ? toBoolean(this.nzDisableAutoTips)\n            : this.nzFormDirective?.nzDisableAutoTips;\n    }\n    set nzHasFeedback(value) {\n        this._hasFeedback = toBoolean(value);\n        this.nzFormStatusService.formStatusChanges.next({ status: this.status, hasFeedback: this._hasFeedback });\n        if (this.nzFormItemComponent) {\n            this.nzFormItemComponent.setHasFeedback(this._hasFeedback);\n        }\n    }\n    get nzHasFeedback() {\n        return this._hasFeedback;\n    }\n    set nzValidateStatus(value) {\n        if (value instanceof AbstractControl || value instanceof NgModel) {\n            this.validateControl = value;\n            this.validateString = null;\n            this.watchControl();\n        }\n        else if (value instanceof FormControlName) {\n            this.validateControl = value.control;\n            this.validateString = null;\n            this.watchControl();\n        }\n        else {\n            this.validateString = value;\n            this.validateControl = null;\n            this.setStatus();\n        }\n    }\n    watchControl() {\n        this.validateChanges.unsubscribe();\n        /** miss detect https://github.com/angular/angular/issues/10887 **/\n        if (this.validateControl && this.validateControl.statusChanges) {\n            this.validateChanges = this.validateControl.statusChanges\n                .pipe(startWith(null), takeUntil(this.destroyed$))\n                .subscribe(() => {\n                if (!this.disableAutoTips) {\n                    this.updateAutoErrorTip();\n                }\n                this.setStatus();\n                this.cdr.markForCheck();\n            });\n        }\n    }\n    setStatus() {\n        this.status = this.getControlStatus(this.validateString);\n        this.innerTip = this.getInnerTip(this.status);\n        this.nzFormStatusService.formStatusChanges.next({ status: this.status, hasFeedback: this.nzHasFeedback });\n        if (this.nzFormItemComponent) {\n            this.nzFormItemComponent.setWithHelpViaTips(!!this.innerTip);\n            this.nzFormItemComponent.setStatus(this.status);\n        }\n    }\n    getControlStatus(validateString) {\n        let status;\n        if (validateString === 'warning' || this.validateControlStatus('INVALID', 'warning')) {\n            status = 'warning';\n        }\n        else if (validateString === 'error' || this.validateControlStatus('INVALID')) {\n            status = 'error';\n        }\n        else if (validateString === 'validating' ||\n            validateString === 'pending' ||\n            this.validateControlStatus('PENDING')) {\n            status = 'validating';\n        }\n        else if (validateString === 'success' || this.validateControlStatus('VALID')) {\n            status = 'success';\n        }\n        else {\n            status = '';\n        }\n        return status;\n    }\n    validateControlStatus(validStatus, statusType) {\n        if (!this.validateControl) {\n            return false;\n        }\n        else {\n            const { dirty, touched, status } = this.validateControl;\n            return ((!!dirty || !!touched) && (statusType ? this.validateControl.hasError(statusType) : status === validStatus));\n        }\n    }\n    getInnerTip(status) {\n        switch (status) {\n            case 'error':\n                return (!this.disableAutoTips && this.autoErrorTip) || this.nzErrorTip || null;\n            case 'validating':\n                return this.nzValidatingTip || null;\n            case 'success':\n                return this.nzSuccessTip || null;\n            case 'warning':\n                return this.nzWarningTip || null;\n            default:\n                return null;\n        }\n    }\n    updateAutoErrorTip() {\n        if (this.validateControl) {\n            const errors = this.validateControl.errors || {};\n            let autoErrorTip = '';\n            for (const key in errors) {\n                if (errors.hasOwnProperty(key)) {\n                    autoErrorTip =\n                        errors[key]?.[this.localeId] ??\n                            this.nzAutoTips?.[this.localeId]?.[key] ??\n                            this.nzAutoTips.default?.[key] ??\n                            this.nzFormDirective?.nzAutoTips?.[this.localeId]?.[key] ??\n                            this.nzFormDirective?.nzAutoTips.default?.[key];\n                }\n                if (!!autoErrorTip) {\n                    break;\n                }\n            }\n            this.autoErrorTip = autoErrorTip;\n        }\n    }\n    subscribeAutoTips(observable) {\n        observable?.pipe(takeUntil(this.destroyed$)).subscribe(() => {\n            if (!this.disableAutoTips) {\n                this.updateAutoErrorTip();\n                this.setStatus();\n                this.cdr.markForCheck();\n            }\n        });\n    }\n    constructor(nzFormItemComponent, cdr, i18n, nzFormDirective, nzFormStatusService) {\n        this.nzFormItemComponent = nzFormItemComponent;\n        this.cdr = cdr;\n        this.nzFormDirective = nzFormDirective;\n        this.nzFormStatusService = nzFormStatusService;\n        this._hasFeedback = false;\n        this.validateChanges = Subscription.EMPTY;\n        this.validateString = null;\n        this.destroyed$ = new Subject();\n        this.status = '';\n        this.validateControl = null;\n        this.innerTip = null;\n        this.nzAutoTips = {};\n        this.nzDisableAutoTips = 'default';\n        this.subscribeAutoTips(i18n.localeChange.pipe(tap(locale => (this.localeId = locale.locale))));\n        this.subscribeAutoTips(this.nzFormDirective?.getInputObservable('nzAutoTips'));\n        this.subscribeAutoTips(this.nzFormDirective\n            ?.getInputObservable('nzDisableAutoTips')\n            .pipe(filter(() => this.nzDisableAutoTips === 'default')));\n    }\n    ngOnChanges(changes) {\n        const { nzDisableAutoTips, nzAutoTips, nzSuccessTip, nzWarningTip, nzErrorTip, nzValidatingTip } = changes;\n        if (nzDisableAutoTips || nzAutoTips) {\n            this.updateAutoErrorTip();\n            this.setStatus();\n        }\n        else if (nzSuccessTip || nzWarningTip || nzErrorTip || nzValidatingTip) {\n            this.setStatus();\n        }\n    }\n    ngOnInit() {\n        this.setStatus();\n    }\n    ngOnDestroy() {\n        this.destroyed$.next();\n        this.destroyed$.complete();\n    }\n    ngAfterContentInit() {\n        if (!this.validateControl && !this.validateString) {\n            if (this.defaultValidateControl instanceof FormControlDirective) {\n                this.nzValidateStatus = this.defaultValidateControl.control;\n            }\n            else {\n                this.nzValidateStatus = this.defaultValidateControl;\n            }\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzFormControlComponent, deps: [{ token: NzFormItemComponent, host: true, optional: true }, { token: i0.ChangeDetectorRef }, { token: i2$1.NzI18nService }, { token: NzFormDirective, optional: true }, { token: i4.NzFormStatusService }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"17.3.8\", type: NzFormControlComponent, isStandalone: true, selector: \"nz-form-control\", inputs: { nzSuccessTip: \"nzSuccessTip\", nzWarningTip: \"nzWarningTip\", nzErrorTip: \"nzErrorTip\", nzValidatingTip: \"nzValidatingTip\", nzExtra: \"nzExtra\", nzAutoTips: \"nzAutoTips\", nzDisableAutoTips: \"nzDisableAutoTips\", nzHasFeedback: \"nzHasFeedback\", nzValidateStatus: \"nzValidateStatus\" }, host: { classAttribute: \"ant-form-item-control\" }, providers: [NzFormStatusService], queries: [{ propertyName: \"defaultValidateControl\", first: true, predicate: NgControl, descendants: true }], exportAs: [\"nzFormControl\"], usesOnChanges: true, ngImport: i0, template: `\n    <div class=\"ant-form-item-control-input\">\n      <div class=\"ant-form-item-control-input-content\">\n        <ng-content></ng-content>\n      </div>\n    </div>\n    @if (innerTip) {\n      <div @helpMotion class=\"ant-form-item-explain ant-form-item-explain-connected\">\n        <div role=\"alert\" [ngClass]=\"['ant-form-item-explain-' + status]\">\n          <ng-container *nzStringTemplateOutlet=\"innerTip; context: { $implicit: validateControl }\">{{\n            innerTip\n          }}</ng-container>\n        </div>\n      </div>\n    }\n\n    @if (nzExtra) {\n      <div class=\"ant-form-item-extra\">\n        <ng-container *nzStringTemplateOutlet=\"nzExtra\">{{ nzExtra }}</ng-container>\n      </div>\n    }\n  `, isInline: true, dependencies: [{ kind: \"directive\", type: NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"ngmodule\", type: NzOutletModule }, { kind: \"directive\", type: i5.NzStringTemplateOutletDirective, selector: \"[nzStringTemplateOutlet]\", inputs: [\"nzStringTemplateOutletContext\", \"nzStringTemplateOutlet\"], exportAs: [\"nzStringTemplateOutlet\"] }], animations: [helpMotion], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzFormControlComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'nz-form-control',\n                    exportAs: 'nzFormControl',\n                    preserveWhitespaces: false,\n                    animations: [helpMotion],\n                    encapsulation: ViewEncapsulation.None,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    template: `\n    <div class=\"ant-form-item-control-input\">\n      <div class=\"ant-form-item-control-input-content\">\n        <ng-content></ng-content>\n      </div>\n    </div>\n    @if (innerTip) {\n      <div @helpMotion class=\"ant-form-item-explain ant-form-item-explain-connected\">\n        <div role=\"alert\" [ngClass]=\"['ant-form-item-explain-' + status]\">\n          <ng-container *nzStringTemplateOutlet=\"innerTip; context: { $implicit: validateControl }\">{{\n            innerTip\n          }}</ng-container>\n        </div>\n      </div>\n    }\n\n    @if (nzExtra) {\n      <div class=\"ant-form-item-extra\">\n        <ng-container *nzStringTemplateOutlet=\"nzExtra\">{{ nzExtra }}</ng-container>\n      </div>\n    }\n  `,\n                    providers: [NzFormStatusService],\n                    host: {\n                        class: 'ant-form-item-control'\n                    },\n                    imports: [NgClass, NzOutletModule],\n                    standalone: true\n                }]\n        }], ctorParameters: () => [{ type: NzFormItemComponent, decorators: [{\n                    type: Optional\n                }, {\n                    type: Host\n                }] }, { type: i0.ChangeDetectorRef }, { type: i2$1.NzI18nService }, { type: NzFormDirective, decorators: [{\n                    type: Optional\n                }] }, { type: i4.NzFormStatusService }], propDecorators: { defaultValidateControl: [{\n                type: ContentChild,\n                args: [NgControl, { static: false }]\n            }], nzSuccessTip: [{\n                type: Input\n            }], nzWarningTip: [{\n                type: Input\n            }], nzErrorTip: [{\n                type: Input\n            }], nzValidatingTip: [{\n                type: Input\n            }], nzExtra: [{\n                type: Input\n            }], nzAutoTips: [{\n                type: Input\n            }], nzDisableAutoTips: [{\n                type: Input\n            }], nzHasFeedback: [{\n                type: Input\n            }], nzValidateStatus: [{\n                type: Input\n            }] } });\n\nfunction toTooltipIcon(value) {\n    const icon = typeof value === 'string' ? { type: value } : value;\n    return { ...DefaultTooltipIcon, ...icon };\n}\nclass NzFormLabelComponent {\n    set nzNoColon(value) {\n        this.noColon = toBoolean(value);\n    }\n    get nzNoColon() {\n        return this.noColon !== 'default' ? this.noColon : this.nzFormDirective?.nzNoColon;\n    }\n    set nzTooltipIcon(value) {\n        this._tooltipIcon = toTooltipIcon(value);\n    }\n    // due to 'get' and 'set' accessor must have the same type, so it was renamed to `tooltipIcon`\n    get tooltipIcon() {\n        return this._tooltipIcon !== 'default'\n            ? this._tooltipIcon\n            : toTooltipIcon(this.nzFormDirective?.nzTooltipIcon || DefaultTooltipIcon);\n    }\n    set nzLabelAlign(value) {\n        this.labelAlign = value;\n    }\n    get nzLabelAlign() {\n        return this.labelAlign !== 'default' ? this.labelAlign : this.nzFormDirective?.nzLabelAlign || 'right';\n    }\n    set nzLabelWrap(value) {\n        this.labelWrap = toBoolean(value);\n    }\n    get nzLabelWrap() {\n        return this.labelWrap !== 'default' ? this.labelWrap : this.nzFormDirective?.nzLabelWrap;\n    }\n    constructor(cdr, nzFormDirective) {\n        this.cdr = cdr;\n        this.nzFormDirective = nzFormDirective;\n        this.nzRequired = false;\n        this.noColon = 'default';\n        this._tooltipIcon = 'default';\n        this.labelAlign = 'default';\n        this.labelWrap = 'default';\n        this.destroy$ = new Subject();\n        if (this.nzFormDirective) {\n            this.nzFormDirective\n                .getInputObservable('nzNoColon')\n                .pipe(filter(() => this.noColon === 'default'), takeUntil(this.destroy$))\n                .subscribe(() => this.cdr.markForCheck());\n            this.nzFormDirective\n                .getInputObservable('nzTooltipIcon')\n                .pipe(filter(() => this._tooltipIcon === 'default'), takeUntil(this.destroy$))\n                .subscribe(() => this.cdr.markForCheck());\n            this.nzFormDirective\n                .getInputObservable('nzLabelAlign')\n                .pipe(filter(() => this.labelAlign === 'default'), takeUntil(this.destroy$))\n                .subscribe(() => this.cdr.markForCheck());\n            this.nzFormDirective\n                .getInputObservable('nzLabelWrap')\n                .pipe(filter(() => this.labelWrap === 'default'), takeUntil(this.destroy$))\n                .subscribe(() => this.cdr.markForCheck());\n        }\n    }\n    ngOnDestroy() {\n        this.destroy$.next(true);\n        this.destroy$.complete();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzFormLabelComponent, deps: [{ token: i0.ChangeDetectorRef }, { token: NzFormDirective, optional: true, skipSelf: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"17.3.8\", type: NzFormLabelComponent, isStandalone: true, selector: \"nz-form-label\", inputs: { nzFor: \"nzFor\", nzRequired: \"nzRequired\", nzNoColon: \"nzNoColon\", nzTooltipTitle: \"nzTooltipTitle\", nzTooltipIcon: \"nzTooltipIcon\", nzLabelAlign: \"nzLabelAlign\", nzLabelWrap: \"nzLabelWrap\" }, host: { properties: { \"class.ant-form-item-label-left\": \"nzLabelAlign === 'left'\", \"class.ant-form-item-label-wrap\": \"nzLabelWrap\" }, classAttribute: \"ant-form-item-label\" }, exportAs: [\"nzFormLabel\"], ngImport: i0, template: `\n    <label [attr.for]=\"nzFor\" [class.ant-form-item-no-colon]=\"nzNoColon\" [class.ant-form-item-required]=\"nzRequired\">\n      <ng-content></ng-content>\n      @if (nzTooltipTitle) {\n        <span class=\"ant-form-item-tooltip\" nz-tooltip [nzTooltipTitle]=\"nzTooltipTitle\">\n          <ng-container *nzStringTemplateOutlet=\"tooltipIcon.type; let tooltipIconType\">\n            <span nz-icon [nzType]=\"tooltipIconType\" [nzTheme]=\"tooltipIcon.theme\"></span>\n          </ng-container>\n        </span>\n      }\n    </label>\n  `, isInline: true, dependencies: [{ kind: \"ngmodule\", type: NzOutletModule }, { kind: \"directive\", type: i5.NzStringTemplateOutletDirective, selector: \"[nzStringTemplateOutlet]\", inputs: [\"nzStringTemplateOutletContext\", \"nzStringTemplateOutlet\"], exportAs: [\"nzStringTemplateOutlet\"] }, { kind: \"directive\", type: NzTooltipDirective, selector: \"[nz-tooltip]\", inputs: [\"nzTooltipTitle\", \"nzTooltipTitleContext\", \"nz-tooltip\", \"nzTooltipTrigger\", \"nzTooltipPlacement\", \"nzTooltipOrigin\", \"nzTooltipVisible\", \"nzTooltipMouseEnterDelay\", \"nzTooltipMouseLeaveDelay\", \"nzTooltipOverlayClassName\", \"nzTooltipOverlayStyle\", \"nzTooltipArrowPointAtCenter\", \"cdkConnectedOverlayPush\", \"nzTooltipColor\"], outputs: [\"nzTooltipVisibleChange\"], exportAs: [\"nzTooltip\"] }, { kind: \"ngmodule\", type: NzIconModule }, { kind: \"directive\", type: i3.NzIconDirective, selector: \"[nz-icon]\", inputs: [\"nzSpin\", \"nzRotate\", \"nzType\", \"nzTheme\", \"nzTwotoneColor\", \"nzIconfont\"], exportAs: [\"nzIcon\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\n__decorate([\n    InputBoolean()\n], NzFormLabelComponent.prototype, \"nzRequired\", void 0);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzFormLabelComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'nz-form-label',\n                    exportAs: 'nzFormLabel',\n                    preserveWhitespaces: false,\n                    encapsulation: ViewEncapsulation.None,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    template: `\n    <label [attr.for]=\"nzFor\" [class.ant-form-item-no-colon]=\"nzNoColon\" [class.ant-form-item-required]=\"nzRequired\">\n      <ng-content></ng-content>\n      @if (nzTooltipTitle) {\n        <span class=\"ant-form-item-tooltip\" nz-tooltip [nzTooltipTitle]=\"nzTooltipTitle\">\n          <ng-container *nzStringTemplateOutlet=\"tooltipIcon.type; let tooltipIconType\">\n            <span nz-icon [nzType]=\"tooltipIconType\" [nzTheme]=\"tooltipIcon.theme\"></span>\n          </ng-container>\n        </span>\n      }\n    </label>\n  `,\n                    host: {\n                        class: 'ant-form-item-label',\n                        '[class.ant-form-item-label-left]': `nzLabelAlign === 'left'`,\n                        '[class.ant-form-item-label-wrap]': `nzLabelWrap`\n                    },\n                    imports: [NzOutletModule, NzTooltipDirective, NzIconModule],\n                    standalone: true\n                }]\n        }], ctorParameters: () => [{ type: i0.ChangeDetectorRef }, { type: NzFormDirective, decorators: [{\n                    type: Optional\n                }, {\n                    type: SkipSelf\n                }] }], propDecorators: { nzFor: [{\n                type: Input\n            }], nzRequired: [{\n                type: Input\n            }], nzNoColon: [{\n                type: Input\n            }], nzTooltipTitle: [{\n                type: Input\n            }], nzTooltipIcon: [{\n                type: Input\n            }], nzLabelAlign: [{\n                type: Input\n            }], nzLabelWrap: [{\n                type: Input\n            }] } });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzFormSplitComponent {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzFormSplitComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzFormSplitComponent, isStandalone: true, selector: \"nz-form-split\", host: { classAttribute: \"ant-form-split\" }, exportAs: [\"nzFormSplit\"], ngImport: i0, template: ` <ng-content></ng-content> `, isInline: true, changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzFormSplitComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'nz-form-split',\n                    exportAs: 'nzFormSplit',\n                    preserveWhitespaces: false,\n                    encapsulation: ViewEncapsulation.None,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    template: ` <ng-content></ng-content> `,\n                    host: {\n                        class: 'ant-form-split'\n                    },\n                    standalone: true\n                }]\n        }] });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzFormTextComponent {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzFormTextComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzFormTextComponent, isStandalone: true, selector: \"nz-form-text\", host: { classAttribute: \"ant-form-text\" }, exportAs: [\"nzFormText\"], ngImport: i0, template: ` <ng-content></ng-content> `, isInline: true, changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzFormTextComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'nz-form-text',\n                    exportAs: 'nzFormText',\n                    preserveWhitespaces: false,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    encapsulation: ViewEncapsulation.None,\n                    template: ` <ng-content></ng-content> `,\n                    host: {\n                        class: 'ant-form-text'\n                    },\n                    standalone: true\n                }]\n        }] });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzFormModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzFormModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.3.8\", ngImport: i0, type: NzFormModule, imports: [NzFormDirective,\n            NzFormItemComponent,\n            NzFormLabelComponent,\n            NzFormControlComponent,\n            NzFormTextComponent,\n            NzFormSplitComponent], exports: [NzGridModule,\n            NzFormDirective,\n            NzFormItemComponent,\n            NzFormLabelComponent,\n            NzFormControlComponent,\n            NzFormTextComponent,\n            NzFormSplitComponent] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzFormModule, imports: [NzFormLabelComponent,\n            NzFormControlComponent, NzGridModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzFormModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        NzFormDirective,\n                        NzFormItemComponent,\n                        NzFormLabelComponent,\n                        NzFormControlComponent,\n                        NzFormTextComponent,\n                        NzFormSplitComponent\n                    ],\n                    exports: [\n                        NzGridModule,\n                        NzFormDirective,\n                        NzFormItemComponent,\n                        NzFormLabelComponent,\n                        NzFormControlComponent,\n                        NzFormTextComponent,\n                        NzFormSplitComponent\n                    ]\n                }]\n        }] });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { DefaultTooltipIcon, NzFormControlComponent, NzFormDirective, NzFormItemComponent, NzFormLabelComponent, NzFormModule, NzFormSplitComponent, NzFormTextComponent };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,IAAI,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,eAAe;AACzJ,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,eAAe,EAAEC,OAAO,EAAEC,eAAe,EAAEC,oBAAoB,EAAEC,SAAS,QAAQ,gBAAgB;AAC3G,SAASC,OAAO,EAAEC,YAAY,QAAQ,MAAM;AAC5C,SAASC,MAAM,EAAEC,GAAG,EAAEC,SAAS,EAAEC,SAAS,EAAEC,GAAG,QAAQ,gBAAgB;AACvE,SAASC,UAAU,QAAQ,8BAA8B;AACzD,OAAO,KAAKC,EAAE,MAAM,yBAAyB;AAC7C,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,OAAO,KAAKC,EAAE,MAAM,2BAA2B;AAC/C,SAASC,cAAc,QAAQ,2BAA2B;AAC1D,SAASC,YAAY,EAAEC,SAAS,QAAQ,yBAAyB;AACjE,OAAO,KAAKC,IAAI,MAAM,oBAAoB;AAC1C,SAASC,UAAU,QAAQ,OAAO;AAClC,OAAO,KAAKC,EAAE,MAAM,2BAA2B;AAC/C,SAASC,UAAU,QAAQ,2BAA2B;AACtD,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,OAAO,KAAKC,EAAE,MAAM,oBAAoB;AACxC,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,kBAAkB,QAAQ,uBAAuB;;AAE1D;AACA;AACA;AACA;AACA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA,GAAAC,EAAA,KAAAA,EAAA;AAAA,MAAAC,GAAA,GAAAD,EAAA;EAAAE,SAAA,EAAAF;AAAA;AAAA,SAAAG,6DAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAyBoG9C,EAAE,CAAAgD,uBAAA,EAgTH,CAAC;IAhTAhD,EAAE,CAAAiD,MAAA,EAkT3F,CAAC;IAlTwFjD,EAAE,CAAAkD,qBAAA;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,MAAA,GAAFnD,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAAqD,SAAA,CAkT3F,CAAC;IAlTwFrD,EAAE,CAAAsD,iBAAA,CAAAH,MAAA,CAAAI,QAkT3F,CAAC;EAAA;AAAA;AAAA,SAAAC,8CAAAV,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlTwF9C,EAAE,CAAAyD,cAAA,YA8SlB,CAAC,YACZ,CAAC;IA/S0BzD,EAAE,CAAA0D,UAAA,IAAAb,4DAAA,yBAgTH,CAAC;IAhTA7C,EAAE,CAAA2D,YAAA,CAmTzF,CAAC,CACH,CAAC;EAAA;EAAA,IAAAb,EAAA;IAAA,MAAAK,MAAA,GApTwFnD,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAA4D,UAAA,gBAAAC,SA8SjF,CAAC;IA9S8E7D,EAAE,CAAAqD,SAAA,CA+S9B,CAAC;IA/S2BrD,EAAE,CAAA4D,UAAA,YAAF5D,EAAE,CAAA8D,eAAA,IAAArB,GAAA,6BAAAU,MAAA,CAAAY,MAAA,CA+S9B,CAAC;IA/S2B/D,EAAE,CAAAqD,SAAA,CAgT5C,CAAC;IAhTyCrD,EAAE,CAAA4D,UAAA,2BAAAT,MAAA,CAAAI,QAgT5C,CAAC,kCAhTyCvD,EAAE,CAAA8D,eAAA,IAAAnB,GAAA,EAAAQ,MAAA,CAAAa,eAAA,CAgTL,CAAC;EAAA;AAAA;AAAA,SAAAC,6DAAAnB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAhTE9C,EAAE,CAAAgD,uBAAA,EAyT/C,CAAC;IAzT4ChD,EAAE,CAAAiD,MAAA,EAyTlC,CAAC;IAzT+BjD,EAAE,CAAAkD,qBAAA;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,MAAA,GAAFnD,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAAqD,SAAA,CAyTlC,CAAC;IAzT+BrD,EAAE,CAAAsD,iBAAA,CAAAH,MAAA,CAAAe,OAyTlC,CAAC;EAAA;AAAA;AAAA,SAAAC,8CAAArB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAzT+B9C,EAAE,CAAAyD,cAAA,YAwThE,CAAC;IAxT6DzD,EAAE,CAAA0D,UAAA,IAAAO,4DAAA,yBAyT/C,CAAC;IAzT4CjE,EAAE,CAAA2D,YAAA,CA0T3F,CAAC;EAAA;EAAA,IAAAb,EAAA;IAAA,MAAAK,MAAA,GA1TwFnD,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAAqD,SAAA,CAyTjD,CAAC;IAzT8CrD,EAAE,CAAA4D,UAAA,2BAAAT,MAAA,CAAAe,OAyTjD,CAAC;EAAA;AAAA;AAAA,SAAAE,2DAAAtB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAzT8C9C,EAAE,CAAAgD,uBAAA,EAucf,CAAC;IAvcYhD,EAAE,CAAAqE,SAAA,aAwcb,CAAC;IAxcUrE,EAAE,CAAAkD,qBAAA;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAwB,kBAAA,GAAAvB,GAAA,CAAAH,SAAA;IAAA,MAAA2B,MAAA,GAAFvE,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAAqD,SAAA,CAwcnD,CAAC;IAxcgDrD,EAAE,CAAA4D,UAAA,WAAAU,kBAwcnD,CAAC,YAAAC,MAAA,CAAAC,WAAA,CAAAC,KAA6B,CAAC;EAAA;AAAA;AAAA,SAAAC,4CAAA5B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAxckB9C,EAAE,CAAAyD,cAAA,aAscd,CAAC;IAtcWzD,EAAE,CAAA0D,UAAA,IAAAU,0DAAA,yBAucf,CAAC;IAvcYpE,EAAE,CAAA2D,YAAA,CA0cxF,CAAC;EAAA;EAAA,IAAAb,EAAA;IAAA,MAAAyB,MAAA,GA1cqFvE,EAAE,CAAAoD,aAAA;IAAFpD,EAAE,CAAA4D,UAAA,mBAAAW,MAAA,CAAAI,cAscf,CAAC;IAtcY3E,EAAE,CAAAqD,SAAA,CAucpC,CAAC;IAvciCrD,EAAE,CAAA4D,UAAA,2BAAAW,MAAA,CAAAC,WAAA,CAAAI,IAucpC,CAAC;EAAA;AAAA;AA/dnE,MAAMC,mBAAmB,CAAC;EACtBC,kBAAkBA,CAACC,KAAK,EAAE;IACtB,IAAI,CAACC,aAAa,GAAGD,KAAK;IAC1B,IAAI,CAACE,GAAG,CAACC,YAAY,CAAC,CAAC;EAC3B;EACAC,SAASA,CAACpB,MAAM,EAAE;IACd,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACkB,GAAG,CAACC,YAAY,CAAC,CAAC;EAC3B;EACAE,cAAcA,CAACC,WAAW,EAAE;IACxB,IAAI,CAACA,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACJ,GAAG,CAACC,YAAY,CAAC,CAAC;EAC3B;EACAI,WAAWA,CAACL,GAAG,EAAE;IACb,IAAI,CAACA,GAAG,GAAGA,GAAG;IACd,IAAI,CAAClB,MAAM,GAAG,EAAE;IAChB,IAAI,CAACsB,WAAW,GAAG,KAAK;IACxB,IAAI,CAACL,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACO,QAAQ,GAAG,IAAIrE,OAAO,CAAC,CAAC;EACjC;EACAsE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACD,QAAQ,CAACE,IAAI,CAAC,IAAI,CAAC;IACxB,IAAI,CAACF,QAAQ,CAACG,QAAQ,CAAC,CAAC;EAC5B;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,4BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFhB,mBAAmB,EAA7B7E,EAAE,CAAA8F,iBAAA,CAA6C9F,EAAE,CAAC+F,iBAAiB;IAAA,CAA4C;EAAE;EACjN;IAAS,IAAI,CAACC,IAAI,kBAD8EhG,EAAE,CAAAiG,iBAAA;MAAArB,IAAA,EACJC,mBAAmB;MAAAqB,SAAA;MAAAC,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,iCAAAxD,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADjB9C,EAAE,CAAAuG,WAAA,8BAAAxD,GAAA,CAAAgB,MAAA,KACO,SAAO,CAAC,8BAAAhB,GAAA,CAAAgB,MAAA,KAAR,SAAO,CAAC,4BAAAhB,GAAA,CAAAgB,MAAA,KAAR,OAAO,CAAC,gCAAAhB,GAAA,CAAAgB,MAAA,KAAR,YAAO,CAAC,+BAAAhB,GAAA,CAAAsC,WAAA,IAAAtC,GAAA,CAAAgB,MAAD,CAAC,4BAAAhB,GAAA,CAAAiC,aAAD,CAAC;QAAA;MAAA;MAAAwB,QAAA;MAAAC,UAAA;MAAAC,QAAA,GADjB1G,EAAE,CAAA2G,mBAAA;MAAAC,kBAAA,EAAApE,GAAA;MAAAqE,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,6BAAAlE,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF9C,EAAE,CAAAiH,eAAA;UAAFjH,EAAE,CAAAkH,YAAA,EAC0iB,CAAC;QAAA;MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAAmH;EAAE;AACtwB;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoGrH,EAAE,CAAAsH,iBAAA,CAGXzC,mBAAmB,EAAc,CAAC;IACjHD,IAAI,EAAE3E,SAAS;IACfsH,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,cAAc;MACxBhB,QAAQ,EAAE,YAAY;MACtBiB,mBAAmB,EAAE,KAAK;MAC1BL,eAAe,EAAElH,uBAAuB,CAACwH,MAAM;MAC/CP,aAAa,EAAEhH,iBAAiB,CAACwH,IAAI;MACrCC,IAAI,EAAE;QACFC,KAAK,EAAE,eAAe;QACtB,mCAAmC,EAAE,sBAAsB;QAC3D,mCAAmC,EAAE,sBAAsB;QAC3D,iCAAiC,EAAE,oBAAoB;QACvD,qCAAqC,EAAE,yBAAyB;QAChE,oCAAoC,EAAE,uBAAuB;QAC7D,iCAAiC,EAAE;MACvC,CAAC;MACDd,QAAQ,EAAG,6BAA4B;MACvCN,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE7B,IAAI,EAAE5E,EAAE,CAAC+F;EAAkB,CAAC,CAAC;AAAA;AAElE,MAAM+B,qBAAqB,GAAG,MAAM;AACpC,MAAMC,kBAAkB,GAAG;EACvBnD,IAAI,EAAE,iBAAiB;EACvBH,KAAK,EAAE;AACX,CAAC;AACD,MAAMuD,eAAe,CAAC;EAClBC,kBAAkBA,CAACC,UAAU,EAAE;IAC3B,OAAO,IAAI,CAACC,aAAa,CAACC,IAAI,CAAChH,MAAM,CAACiH,OAAO,IAAIH,UAAU,IAAIG,OAAO,CAAC,EAAEhH,GAAG,CAAC0D,KAAK,IAAIA,KAAK,CAACmD,UAAU,CAAC,CAAC,CAAC;EAC7G;EACA5C,WAAWA,CAACgD,eAAe,EAAEC,cAAc,EAAE;IACzC,IAAI,CAACD,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,aAAa,GAAGV,qBAAqB;IAC1C,IAAI,CAACW,QAAQ,GAAG,YAAY;IAC5B,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,UAAU,GAAG,CAAC,CAAC;IACpB,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACC,aAAa,GAAGd,kBAAkB;IACvC,IAAI,CAACe,YAAY,GAAG,OAAO;IAC3B,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,GAAG,GAAG,KAAK;IAChB,IAAI,CAACzD,QAAQ,GAAG,IAAIrE,OAAO,CAAC,CAAC;IAC7B,IAAI,CAACiH,aAAa,GAAG,IAAIjH,OAAO,CAAC,CAAC;IAClC,IAAI,CAAC8H,GAAG,GAAG,IAAI,CAACT,cAAc,CAACxD,KAAK;IACpC,IAAI,CAACwD,cAAc,CAACU,MAAM,EAAEb,IAAI,CAAC9G,SAAS,CAAC,IAAI,CAACiE,QAAQ,CAAC,CAAC,CAAC2D,SAAS,CAAEC,SAAS,IAAK;MAChF,IAAI,CAACH,GAAG,GAAGG,SAAS;IACxB,CAAC,CAAC;EACN;EACAC,WAAWA,CAACf,OAAO,EAAE;IACjB,IAAI,CAACF,aAAa,CAAC1C,IAAI,CAAC4C,OAAO,CAAC;EACpC;EACA7C,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC2C,aAAa,CAACzC,QAAQ,CAAC,CAAC;IAC7B,IAAI,CAACH,QAAQ,CAACE,IAAI,CAAC,IAAI,CAAC;IACxB,IAAI,CAACF,QAAQ,CAACG,QAAQ,CAAC,CAAC;EAC5B;EACA;IAAS,IAAI,CAACC,IAAI,YAAA0D,wBAAAxD,CAAA;MAAA,YAAAA,CAAA,IAAwFmC,eAAe,EA7DzBhI,EAAE,CAAA8F,iBAAA,CA6DyC5D,EAAE,CAACoH,eAAe,GA7D7DtJ,EAAE,CAAA8F,iBAAA,CA6DwE1D,EAAE,CAACmH,cAAc;IAAA,CAA4D;EAAE;EACzP;IAAS,IAAI,CAACC,IAAI,kBA9D8ExJ,EAAE,CAAAyJ,iBAAA;MAAA7E,IAAA,EA8DJoD,eAAe;MAAA9B,SAAA;MAAAC,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAqD,6BAAA5G,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA9Db9C,EAAE,CAAAuG,WAAA,wBAAAxD,GAAA,CAAA0F,QAAA,KA8DS,YAAC,CAAC,sBAAA1F,GAAA,CAAA0F,QAAA,KAAF,UAAC,CAAC,oBAAA1F,GAAA,CAAA0F,QAAA,KAAF,QAAC,CAAC,iBAAA1F,GAAA,CAAAiG,GAAA,KAAP,KAAM,CAAC;QAAA;MAAA;MAAAW,MAAA;QAAAlB,QAAA;QAAAC,SAAA;QAAAC,UAAA;QAAAC,iBAAA;QAAAC,aAAA;QAAAC,YAAA;QAAAC,WAAA;MAAA;MAAAvC,QAAA;MAAAC,UAAA;MAAAC,QAAA,GA9Db1G,EAAE,CAAA4J,oBAAA;IAAA,EA8DukB;EAAE;AAC/qB;AACA3H,UAAU,CAAC,CACPE,UAAU,CAAC,CAAC,EACZL,YAAY,CAAC,CAAC,CACjB,EAAEkG,eAAe,CAAC6B,SAAS,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC;AAClD5H,UAAU,CAAC,CACPE,UAAU,CAAC,CAAC,CACf,EAAE6F,eAAe,CAAC6B,SAAS,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC;AACnD5H,UAAU,CAAC,CACPH,YAAY,CAAC,CAAC,CACjB,EAAEkG,eAAe,CAAC6B,SAAS,EAAE,mBAAmB,EAAE,KAAK,CAAC,CAAC;AAC1D5H,UAAU,CAAC,CACPE,UAAU,CAAC,CAAC,CACf,EAAE6F,eAAe,CAAC6B,SAAS,EAAE,eAAe,EAAE,KAAK,CAAC,CAAC;AACtD5H,UAAU,CAAC,CACPE,UAAU,CAAC,CAAC,EACZL,YAAY,CAAC,CAAC,CACjB,EAAEkG,eAAe,CAAC6B,SAAS,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC;AACpD;EAAA,QAAAxC,SAAA,oBAAAA,SAAA,KAjFoGrH,EAAE,CAAAsH,iBAAA,CAiFXU,eAAe,EAAc,CAAC;IAC7GpD,IAAI,EAAExE,SAAS;IACfmH,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,WAAW;MACrBhB,QAAQ,EAAE,QAAQ;MAClBoB,IAAI,EAAE;QACFC,KAAK,EAAE,UAAU;QACjB,6BAA6B,EAAG,2BAA0B;QAC1D,2BAA2B,EAAG,yBAAwB;QACtD,yBAAyB,EAAG,uBAAsB;QAClD,sBAAsB,EAAG;MAC7B,CAAC;MACDpB,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE7B,IAAI,EAAE1C,EAAE,CAACoH;EAAgB,CAAC,EAAE;IAAE1E,IAAI,EAAExC,EAAE,CAACmH,cAAc;IAAEO,UAAU,EAAE,CAAC;MACrFlF,IAAI,EAAEvE;IACV,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEoI,QAAQ,EAAE,CAAC;MACpC7D,IAAI,EAAEtE;IACV,CAAC,CAAC;IAAEoI,SAAS,EAAE,CAAC;MACZ9D,IAAI,EAAEtE;IACV,CAAC,CAAC;IAAEqI,UAAU,EAAE,CAAC;MACb/D,IAAI,EAAEtE;IACV,CAAC,CAAC;IAAEsI,iBAAiB,EAAE,CAAC;MACpBhE,IAAI,EAAEtE;IACV,CAAC,CAAC;IAAEuI,aAAa,EAAE,CAAC;MAChBjE,IAAI,EAAEtE;IACV,CAAC,CAAC;IAAEwI,YAAY,EAAE,CAAC;MACflE,IAAI,EAAEtE;IACV,CAAC,CAAC;IAAEyI,WAAW,EAAE,CAAC;MACdnE,IAAI,EAAEtE;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAMyJ,sBAAsB,CAAC;EACzB,IAAIC,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACpB,iBAAiB,KAAK,SAAS,GACrC7G,SAAS,CAAC,IAAI,CAAC6G,iBAAiB,CAAC,GACjC,IAAI,CAACqB,eAAe,EAAErB,iBAAiB;EACjD;EACA,IAAIsB,aAAaA,CAACnF,KAAK,EAAE;IACrB,IAAI,CAACoF,YAAY,GAAGpI,SAAS,CAACgD,KAAK,CAAC;IACpC,IAAI,CAACqF,mBAAmB,CAACC,iBAAiB,CAAC5E,IAAI,CAAC;MAAE1B,MAAM,EAAE,IAAI,CAACA,MAAM;MAAEsB,WAAW,EAAE,IAAI,CAAC8E;IAAa,CAAC,CAAC;IACxG,IAAI,IAAI,CAACG,mBAAmB,EAAE;MAC1B,IAAI,CAACA,mBAAmB,CAAClF,cAAc,CAAC,IAAI,CAAC+E,YAAY,CAAC;IAC9D;EACJ;EACA,IAAID,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACC,YAAY;EAC5B;EACA,IAAII,gBAAgBA,CAACxF,KAAK,EAAE;IACxB,IAAIA,KAAK,YAAYlE,eAAe,IAAIkE,KAAK,YAAYjE,OAAO,EAAE;MAC9D,IAAI,CAACkD,eAAe,GAAGe,KAAK;MAC5B,IAAI,CAACyF,cAAc,GAAG,IAAI;MAC1B,IAAI,CAACC,YAAY,CAAC,CAAC;IACvB,CAAC,MACI,IAAI1F,KAAK,YAAYhE,eAAe,EAAE;MACvC,IAAI,CAACiD,eAAe,GAAGe,KAAK,CAAC2F,OAAO;MACpC,IAAI,CAACF,cAAc,GAAG,IAAI;MAC1B,IAAI,CAACC,YAAY,CAAC,CAAC;IACvB,CAAC,MACI;MACD,IAAI,CAACD,cAAc,GAAGzF,KAAK;MAC3B,IAAI,CAACf,eAAe,GAAG,IAAI;MAC3B,IAAI,CAACmB,SAAS,CAAC,CAAC;IACpB;EACJ;EACAsF,YAAYA,CAAA,EAAG;IACX,IAAI,CAACE,eAAe,CAACC,WAAW,CAAC,CAAC;IAClC;IACA,IAAI,IAAI,CAAC5G,eAAe,IAAI,IAAI,CAACA,eAAe,CAAC6G,aAAa,EAAE;MAC5D,IAAI,CAACF,eAAe,GAAG,IAAI,CAAC3G,eAAe,CAAC6G,aAAa,CACpDzC,IAAI,CAAC7G,SAAS,CAAC,IAAI,CAAC,EAAED,SAAS,CAAC,IAAI,CAACwJ,UAAU,CAAC,CAAC,CACjD5B,SAAS,CAAC,MAAM;QACjB,IAAI,CAAC,IAAI,CAACc,eAAe,EAAE;UACvB,IAAI,CAACe,kBAAkB,CAAC,CAAC;QAC7B;QACA,IAAI,CAAC5F,SAAS,CAAC,CAAC;QAChB,IAAI,CAACF,GAAG,CAACC,YAAY,CAAC,CAAC;MAC3B,CAAC,CAAC;IACN;EACJ;EACAC,SAASA,CAAA,EAAG;IACR,IAAI,CAACpB,MAAM,GAAG,IAAI,CAACiH,gBAAgB,CAAC,IAAI,CAACR,cAAc,CAAC;IACxD,IAAI,CAACjH,QAAQ,GAAG,IAAI,CAAC0H,WAAW,CAAC,IAAI,CAAClH,MAAM,CAAC;IAC7C,IAAI,CAACqG,mBAAmB,CAACC,iBAAiB,CAAC5E,IAAI,CAAC;MAAE1B,MAAM,EAAE,IAAI,CAACA,MAAM;MAAEsB,WAAW,EAAE,IAAI,CAAC6E;IAAc,CAAC,CAAC;IACzG,IAAI,IAAI,CAACI,mBAAmB,EAAE;MAC1B,IAAI,CAACA,mBAAmB,CAACxF,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAACvB,QAAQ,CAAC;MAC5D,IAAI,CAAC+G,mBAAmB,CAACnF,SAAS,CAAC,IAAI,CAACpB,MAAM,CAAC;IACnD;EACJ;EACAiH,gBAAgBA,CAACR,cAAc,EAAE;IAC7B,IAAIzG,MAAM;IACV,IAAIyG,cAAc,KAAK,SAAS,IAAI,IAAI,CAACU,qBAAqB,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE;MAClFnH,MAAM,GAAG,SAAS;IACtB,CAAC,MACI,IAAIyG,cAAc,KAAK,OAAO,IAAI,IAAI,CAACU,qBAAqB,CAAC,SAAS,CAAC,EAAE;MAC1EnH,MAAM,GAAG,OAAO;IACpB,CAAC,MACI,IAAIyG,cAAc,KAAK,YAAY,IACpCA,cAAc,KAAK,SAAS,IAC5B,IAAI,CAACU,qBAAqB,CAAC,SAAS,CAAC,EAAE;MACvCnH,MAAM,GAAG,YAAY;IACzB,CAAC,MACI,IAAIyG,cAAc,KAAK,SAAS,IAAI,IAAI,CAACU,qBAAqB,CAAC,OAAO,CAAC,EAAE;MAC1EnH,MAAM,GAAG,SAAS;IACtB,CAAC,MACI;MACDA,MAAM,GAAG,EAAE;IACf;IACA,OAAOA,MAAM;EACjB;EACAmH,qBAAqBA,CAACC,WAAW,EAAEC,UAAU,EAAE;IAC3C,IAAI,CAAC,IAAI,CAACpH,eAAe,EAAE;MACvB,OAAO,KAAK;IAChB,CAAC,MACI;MACD,MAAM;QAAEqH,KAAK;QAAEC,OAAO;QAAEvH;MAAO,CAAC,GAAG,IAAI,CAACC,eAAe;MACvD,OAAQ,CAAC,CAAC,CAACqH,KAAK,IAAI,CAAC,CAACC,OAAO,MAAMF,UAAU,GAAG,IAAI,CAACpH,eAAe,CAACuH,QAAQ,CAACH,UAAU,CAAC,GAAGrH,MAAM,KAAKoH,WAAW,CAAC;IACvH;EACJ;EACAF,WAAWA,CAAClH,MAAM,EAAE;IAChB,QAAQA,MAAM;MACV,KAAK,OAAO;QACR,OAAQ,CAAC,IAAI,CAACiG,eAAe,IAAI,IAAI,CAACwB,YAAY,IAAK,IAAI,CAACC,UAAU,IAAI,IAAI;MAClF,KAAK,YAAY;QACb,OAAO,IAAI,CAACC,eAAe,IAAI,IAAI;MACvC,KAAK,SAAS;QACV,OAAO,IAAI,CAACC,YAAY,IAAI,IAAI;MACpC,KAAK,SAAS;QACV,OAAO,IAAI,CAACC,YAAY,IAAI,IAAI;MACpC;QACI,OAAO,IAAI;IACnB;EACJ;EACAb,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAAC/G,eAAe,EAAE;MACtB,MAAM6H,MAAM,GAAG,IAAI,CAAC7H,eAAe,CAAC6H,MAAM,IAAI,CAAC,CAAC;MAChD,IAAIL,YAAY,GAAG,EAAE;MACrB,KAAK,MAAMM,GAAG,IAAID,MAAM,EAAE;QACtB,IAAIA,MAAM,CAACE,cAAc,CAACD,GAAG,CAAC,EAAE;UAC5BN,YAAY,GACRK,MAAM,CAACC,GAAG,CAAC,GAAG,IAAI,CAACE,QAAQ,CAAC,IACxB,IAAI,CAACrD,UAAU,GAAG,IAAI,CAACqD,QAAQ,CAAC,GAAGF,GAAG,CAAC,IACvC,IAAI,CAACnD,UAAU,CAACsD,OAAO,GAAGH,GAAG,CAAC,IAC9B,IAAI,CAAC7B,eAAe,EAAEtB,UAAU,GAAG,IAAI,CAACqD,QAAQ,CAAC,GAAGF,GAAG,CAAC,IACxD,IAAI,CAAC7B,eAAe,EAAEtB,UAAU,CAACsD,OAAO,GAAGH,GAAG,CAAC;QAC3D;QACA,IAAI,CAAC,CAACN,YAAY,EAAE;UAChB;QACJ;MACJ;MACA,IAAI,CAACA,YAAY,GAAGA,YAAY;IACpC;EACJ;EACAU,iBAAiBA,CAACC,UAAU,EAAE;IAC1BA,UAAU,EAAE/D,IAAI,CAAC9G,SAAS,CAAC,IAAI,CAACwJ,UAAU,CAAC,CAAC,CAAC5B,SAAS,CAAC,MAAM;MACzD,IAAI,CAAC,IAAI,CAACc,eAAe,EAAE;QACvB,IAAI,CAACe,kBAAkB,CAAC,CAAC;QACzB,IAAI,CAAC5F,SAAS,CAAC,CAAC;QAChB,IAAI,CAACF,GAAG,CAACC,YAAY,CAAC,CAAC;MAC3B;IACJ,CAAC,CAAC;EACN;EACAI,WAAWA,CAACgF,mBAAmB,EAAErF,GAAG,EAAEmH,IAAI,EAAEnC,eAAe,EAAEG,mBAAmB,EAAE;IAC9E,IAAI,CAACE,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACrF,GAAG,GAAGA,GAAG;IACd,IAAI,CAACgF,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACG,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACD,YAAY,GAAG,KAAK;IACzB,IAAI,CAACQ,eAAe,GAAGxJ,YAAY,CAACkL,KAAK;IACzC,IAAI,CAAC7B,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACM,UAAU,GAAG,IAAI5J,OAAO,CAAC,CAAC;IAC/B,IAAI,CAAC6C,MAAM,GAAG,EAAE;IAChB,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACT,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACoF,UAAU,GAAG,CAAC,CAAC;IACpB,IAAI,CAACC,iBAAiB,GAAG,SAAS;IAClC,IAAI,CAACsD,iBAAiB,CAACE,IAAI,CAACE,YAAY,CAAClE,IAAI,CAAC5G,GAAG,CAAC+K,MAAM,IAAK,IAAI,CAACP,QAAQ,GAAGO,MAAM,CAACA,MAAO,CAAC,CAAC,CAAC;IAC9F,IAAI,CAACL,iBAAiB,CAAC,IAAI,CAACjC,eAAe,EAAEhC,kBAAkB,CAAC,YAAY,CAAC,CAAC;IAC9E,IAAI,CAACiE,iBAAiB,CAAC,IAAI,CAACjC,eAAe,EACrChC,kBAAkB,CAAC,mBAAmB,CAAC,CACxCG,IAAI,CAAChH,MAAM,CAAC,MAAM,IAAI,CAACwH,iBAAiB,KAAK,SAAS,CAAC,CAAC,CAAC;EAClE;EACAQ,WAAWA,CAACf,OAAO,EAAE;IACjB,MAAM;MAAEO,iBAAiB;MAAED,UAAU;MAAEgD,YAAY;MAAEC,YAAY;MAAEH,UAAU;MAAEC;IAAgB,CAAC,GAAGrD,OAAO;IAC1G,IAAIO,iBAAiB,IAAID,UAAU,EAAE;MACjC,IAAI,CAACoC,kBAAkB,CAAC,CAAC;MACzB,IAAI,CAAC5F,SAAS,CAAC,CAAC;IACpB,CAAC,MACI,IAAIwG,YAAY,IAAIC,YAAY,IAAIH,UAAU,IAAIC,eAAe,EAAE;MACpE,IAAI,CAACvG,SAAS,CAAC,CAAC;IACpB;EACJ;EACAqH,QAAQA,CAAA,EAAG;IACP,IAAI,CAACrH,SAAS,CAAC,CAAC;EACpB;EACAK,WAAWA,CAAA,EAAG;IACV,IAAI,CAACsF,UAAU,CAACrF,IAAI,CAAC,CAAC;IACtB,IAAI,CAACqF,UAAU,CAACpF,QAAQ,CAAC,CAAC;EAC9B;EACA+G,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAACzI,eAAe,IAAI,CAAC,IAAI,CAACwG,cAAc,EAAE;MAC/C,IAAI,IAAI,CAACkC,sBAAsB,YAAY1L,oBAAoB,EAAE;QAC7D,IAAI,CAACuJ,gBAAgB,GAAG,IAAI,CAACmC,sBAAsB,CAAChC,OAAO;MAC/D,CAAC,MACI;QACD,IAAI,CAACH,gBAAgB,GAAG,IAAI,CAACmC,sBAAsB;MACvD;IACJ;EACJ;EACA;IAAS,IAAI,CAAC/G,IAAI,YAAAgH,+BAAA9G,CAAA;MAAA,YAAAA,CAAA,IAAwFkE,sBAAsB,EAtShC/J,EAAE,CAAA8F,iBAAA,CAsSgDjB,mBAAmB,MAtSrE7E,EAAE,CAAA8F,iBAAA,CAsS4G9F,EAAE,CAAC+F,iBAAiB,GAtSlI/F,EAAE,CAAA8F,iBAAA,CAsS6I9D,IAAI,CAAC4K,aAAa,GAtSjK5M,EAAE,CAAA8F,iBAAA,CAsS4KkC,eAAe,MAtS7LhI,EAAE,CAAA8F,iBAAA,CAsSwNpE,EAAE,CAACC,mBAAmB;IAAA,CAA4C;EAAE;EAC9X;IAAS,IAAI,CAACqE,IAAI,kBAvS8EhG,EAAE,CAAAiG,iBAAA;MAAArB,IAAA,EAuSJmF,sBAAsB;MAAA7D,SAAA;MAAA2G,cAAA,WAAAC,sCAAAhK,EAAA,EAAAC,GAAA,EAAAgK,QAAA;QAAA,IAAAjK,EAAA;UAvSpB9C,EAAE,CAAAgN,cAAA,CAAAD,QAAA,EAuSwgB9L,SAAS;QAAA;QAAA,IAAA6B,EAAA;UAAA,IAAAmK,EAAA;UAvSnhBjN,EAAE,CAAAkN,cAAA,CAAAD,EAAA,GAAFjN,EAAE,CAAAmN,WAAA,QAAApK,GAAA,CAAA2J,sBAAA,GAAAO,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAjH,SAAA;MAAAwD,MAAA;QAAAgC,YAAA;QAAAC,YAAA;QAAAH,UAAA;QAAAC,eAAA;QAAAxH,OAAA;QAAAyE,UAAA;QAAAC,iBAAA;QAAAsB,aAAA;QAAAK,gBAAA;MAAA;MAAA/D,QAAA;MAAAC,UAAA;MAAAC,QAAA,GAAF1G,EAAE,CAAAqN,kBAAA,CAuSqa,CAAC1L,mBAAmB,CAAC,GAvS5b3B,EAAE,CAAA4J,oBAAA,EAAF5J,EAAE,CAAA2G,mBAAA;MAAAC,kBAAA,EAAApE,GAAA;MAAAqE,KAAA;MAAAC,IAAA;MAAAwG,MAAA;MAAAvG,QAAA,WAAAwG,gCAAAzK,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF9C,EAAE,CAAAiH,eAAA;UAAFjH,EAAE,CAAAyD,cAAA,YAwS1D,CAAC,YACS,CAAC;UAzS6CzD,EAAE,CAAAkH,YAAA,EA0StE,CAAC;UA1SmElH,EAAE,CAAA2D,YAAA,CA2S3F,CAAC,CACH,CAAC;UA5S0F3D,EAAE,CAAA0D,UAAA,IAAAF,6CAAA,gBA6SnF,CAAC,IAAAW,6CAAA,gBAUF,CAAC;QAAA;QAAA,IAAArB,EAAA;UAvTiF9C,EAAE,CAAAqD,SAAA,EAqTlG,CAAC;UArT+FrD,EAAE,CAAAwN,aAAA,IAAAzK,GAAA,CAAAQ,QAAA,SAqTlG,CAAC;UArT+FvD,EAAE,CAAAqD,SAAA,CA2TlG,CAAC;UA3T+FrD,EAAE,CAAAwN,aAAA,IAAAzK,GAAA,CAAAmB,OAAA,SA2TlG,CAAC;QAAA;MAAA;MAAAuJ,YAAA,GAC0D7M,OAAO,EAAmFiB,cAAc,EAA+BD,EAAE,CAAC8L,+BAA+B;MAAAvG,aAAA;MAAAwG,IAAA;QAAAC,SAAA,EAAkK,CAACnM,UAAU;MAAC;MAAA2F,eAAA;IAAA,EAAiG;EAAE;AACzf;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA9ToGrH,EAAE,CAAAsH,iBAAA,CA8TXyC,sBAAsB,EAAc,CAAC;IACpHnF,IAAI,EAAE3E,SAAS;IACfsH,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,iBAAiB;MAC3BhB,QAAQ,EAAE,eAAe;MACzBiB,mBAAmB,EAAE,KAAK;MAC1BoG,UAAU,EAAE,CAACpM,UAAU,CAAC;MACxB0F,aAAa,EAAEhH,iBAAiB,CAACwH,IAAI;MACrCP,eAAe,EAAElH,uBAAuB,CAACwH,MAAM;MAC/CX,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiB+G,SAAS,EAAE,CAACnM,mBAAmB,CAAC;MAChCiG,IAAI,EAAE;QACFC,KAAK,EAAE;MACX,CAAC;MACDkG,OAAO,EAAE,CAACnN,OAAO,EAAEiB,cAAc,CAAC;MAClC4E,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE7B,IAAI,EAAEC,mBAAmB;IAAEiF,UAAU,EAAE,CAAC;MACzDlF,IAAI,EAAEvE;IACV,CAAC,EAAE;MACCuE,IAAI,EAAErE;IACV,CAAC;EAAE,CAAC,EAAE;IAAEqE,IAAI,EAAE5E,EAAE,CAAC+F;EAAkB,CAAC,EAAE;IAAEnB,IAAI,EAAE5C,IAAI,CAAC4K;EAAc,CAAC,EAAE;IAAEhI,IAAI,EAAEoD,eAAe;IAAE8B,UAAU,EAAE,CAAC;MACtGlF,IAAI,EAAEvE;IACV,CAAC;EAAE,CAAC,EAAE;IAAEuE,IAAI,EAAElD,EAAE,CAACC;EAAoB,CAAC,CAAC,EAAkB;IAAE+K,sBAAsB,EAAE,CAAC;MACpF9H,IAAI,EAAEpE,YAAY;MAClB+G,IAAI,EAAE,CAACtG,SAAS,EAAE;QAAE+M,MAAM,EAAE;MAAM,CAAC;IACvC,CAAC,CAAC;IAAErC,YAAY,EAAE,CAAC;MACf/G,IAAI,EAAEtE;IACV,CAAC,CAAC;IAAEsL,YAAY,EAAE,CAAC;MACfhH,IAAI,EAAEtE;IACV,CAAC,CAAC;IAAEmL,UAAU,EAAE,CAAC;MACb7G,IAAI,EAAEtE;IACV,CAAC,CAAC;IAAEoL,eAAe,EAAE,CAAC;MAClB9G,IAAI,EAAEtE;IACV,CAAC,CAAC;IAAE4D,OAAO,EAAE,CAAC;MACVU,IAAI,EAAEtE;IACV,CAAC,CAAC;IAAEqI,UAAU,EAAE,CAAC;MACb/D,IAAI,EAAEtE;IACV,CAAC,CAAC;IAAEsI,iBAAiB,EAAE,CAAC;MACpBhE,IAAI,EAAEtE;IACV,CAAC,CAAC;IAAE4J,aAAa,EAAE,CAAC;MAChBtF,IAAI,EAAEtE;IACV,CAAC,CAAC;IAAEiK,gBAAgB,EAAE,CAAC;MACnB3F,IAAI,EAAEtE;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,SAAS2N,aAAaA,CAAClJ,KAAK,EAAE;EAC1B,MAAMmJ,IAAI,GAAG,OAAOnJ,KAAK,KAAK,QAAQ,GAAG;IAAEH,IAAI,EAAEG;EAAM,CAAC,GAAGA,KAAK;EAChE,OAAO;IAAE,GAAGgD,kBAAkB;IAAE,GAAGmG;EAAK,CAAC;AAC7C;AACA,MAAMC,oBAAoB,CAAC;EACvB,IAAIzF,SAASA,CAAC3D,KAAK,EAAE;IACjB,IAAI,CAACqJ,OAAO,GAAGrM,SAAS,CAACgD,KAAK,CAAC;EACnC;EACA,IAAI2D,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC0F,OAAO,KAAK,SAAS,GAAG,IAAI,CAACA,OAAO,GAAG,IAAI,CAACnE,eAAe,EAAEvB,SAAS;EACtF;EACA,IAAIG,aAAaA,CAAC9D,KAAK,EAAE;IACrB,IAAI,CAACsJ,YAAY,GAAGJ,aAAa,CAAClJ,KAAK,CAAC;EAC5C;EACA;EACA,IAAIP,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC6J,YAAY,KAAK,SAAS,GAChC,IAAI,CAACA,YAAY,GACjBJ,aAAa,CAAC,IAAI,CAAChE,eAAe,EAAEpB,aAAa,IAAId,kBAAkB,CAAC;EAClF;EACA,IAAIe,YAAYA,CAAC/D,KAAK,EAAE;IACpB,IAAI,CAACuJ,UAAU,GAAGvJ,KAAK;EAC3B;EACA,IAAI+D,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACwF,UAAU,KAAK,SAAS,GAAG,IAAI,CAACA,UAAU,GAAG,IAAI,CAACrE,eAAe,EAAEnB,YAAY,IAAI,OAAO;EAC1G;EACA,IAAIC,WAAWA,CAAChE,KAAK,EAAE;IACnB,IAAI,CAACwJ,SAAS,GAAGxM,SAAS,CAACgD,KAAK,CAAC;EACrC;EACA,IAAIgE,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACwF,SAAS,KAAK,SAAS,GAAG,IAAI,CAACA,SAAS,GAAG,IAAI,CAACtE,eAAe,EAAElB,WAAW;EAC5F;EACAzD,WAAWA,CAACL,GAAG,EAAEgF,eAAe,EAAE;IAC9B,IAAI,CAAChF,GAAG,GAAGA,GAAG;IACd,IAAI,CAACgF,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACuE,UAAU,GAAG,KAAK;IACvB,IAAI,CAACJ,OAAO,GAAG,SAAS;IACxB,IAAI,CAACC,YAAY,GAAG,SAAS;IAC7B,IAAI,CAACC,UAAU,GAAG,SAAS;IAC3B,IAAI,CAACC,SAAS,GAAG,SAAS;IAC1B,IAAI,CAAChJ,QAAQ,GAAG,IAAIrE,OAAO,CAAC,CAAC;IAC7B,IAAI,IAAI,CAAC+I,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,CACfhC,kBAAkB,CAAC,WAAW,CAAC,CAC/BG,IAAI,CAAChH,MAAM,CAAC,MAAM,IAAI,CAACgN,OAAO,KAAK,SAAS,CAAC,EAAE9M,SAAS,CAAC,IAAI,CAACiE,QAAQ,CAAC,CAAC,CACxE2D,SAAS,CAAC,MAAM,IAAI,CAACjE,GAAG,CAACC,YAAY,CAAC,CAAC,CAAC;MAC7C,IAAI,CAAC+E,eAAe,CACfhC,kBAAkB,CAAC,eAAe,CAAC,CACnCG,IAAI,CAAChH,MAAM,CAAC,MAAM,IAAI,CAACiN,YAAY,KAAK,SAAS,CAAC,EAAE/M,SAAS,CAAC,IAAI,CAACiE,QAAQ,CAAC,CAAC,CAC7E2D,SAAS,CAAC,MAAM,IAAI,CAACjE,GAAG,CAACC,YAAY,CAAC,CAAC,CAAC;MAC7C,IAAI,CAAC+E,eAAe,CACfhC,kBAAkB,CAAC,cAAc,CAAC,CAClCG,IAAI,CAAChH,MAAM,CAAC,MAAM,IAAI,CAACkN,UAAU,KAAK,SAAS,CAAC,EAAEhN,SAAS,CAAC,IAAI,CAACiE,QAAQ,CAAC,CAAC,CAC3E2D,SAAS,CAAC,MAAM,IAAI,CAACjE,GAAG,CAACC,YAAY,CAAC,CAAC,CAAC;MAC7C,IAAI,CAAC+E,eAAe,CACfhC,kBAAkB,CAAC,aAAa,CAAC,CACjCG,IAAI,CAAChH,MAAM,CAAC,MAAM,IAAI,CAACmN,SAAS,KAAK,SAAS,CAAC,EAAEjN,SAAS,CAAC,IAAI,CAACiE,QAAQ,CAAC,CAAC,CAC1E2D,SAAS,CAAC,MAAM,IAAI,CAACjE,GAAG,CAACC,YAAY,CAAC,CAAC,CAAC;IACjD;EACJ;EACAM,WAAWA,CAAA,EAAG;IACV,IAAI,CAACD,QAAQ,CAACE,IAAI,CAAC,IAAI,CAAC;IACxB,IAAI,CAACF,QAAQ,CAACG,QAAQ,CAAC,CAAC;EAC5B;EACA;IAAS,IAAI,CAACC,IAAI,YAAA8I,6BAAA5I,CAAA;MAAA,YAAAA,CAAA,IAAwFsI,oBAAoB,EAjc9BnO,EAAE,CAAA8F,iBAAA,CAic8C9F,EAAE,CAAC+F,iBAAiB,GAjcpE/F,EAAE,CAAA8F,iBAAA,CAic+EkC,eAAe;IAAA,CAA4E;EAAE;EAC9Q;IAAS,IAAI,CAAChC,IAAI,kBAlc8EhG,EAAE,CAAAiG,iBAAA;MAAArB,IAAA,EAkcJuJ,oBAAoB;MAAAjI,SAAA;MAAAC,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAqI,kCAAA5L,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAlclB9C,EAAE,CAAAuG,WAAA,6BAAAxD,GAAA,CAAA+F,YAAA,KAkca,MAAE,CAAC,6BAAA/F,GAAA,CAAAgG,WAAD,CAAC;QAAA;MAAA;MAAAY,MAAA;QAAAgF,KAAA;QAAAH,UAAA;QAAA9F,SAAA;QAAA/D,cAAA;QAAAkE,aAAA;QAAAC,YAAA;QAAAC,WAAA;MAAA;MAAAvC,QAAA;MAAAC,UAAA;MAAAC,QAAA,GAlclB1G,EAAE,CAAA2G,mBAAA;MAAAC,kBAAA,EAAApE,GAAA;MAAAqE,KAAA;MAAAC,IAAA;MAAAwG,MAAA;MAAAvG,QAAA,WAAA6H,8BAAA9L,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF9C,EAAE,CAAAiH,eAAA;UAAFjH,EAAE,CAAAyD,cAAA,WAmcc,CAAC;UAncjBzD,EAAE,CAAAkH,YAAA,EAocxE,CAAC;UApcqElH,EAAE,CAAA0D,UAAA,IAAAgB,2CAAA,iBAqc3E,CAAC;UArcwE1E,EAAE,CAAA2D,YAAA,CA4c3F,CAAC;QAAA;QAAA,IAAAb,EAAA;UA5cwF9C,EAAE,CAAAuG,WAAA,2BAAAxD,GAAA,CAAA2F,SAmc/B,CAAC,2BAAA3F,GAAA,CAAAyL,UAA2C,CAAC;UAnchBxO,EAAE,CAAA6O,WAAA,QAAA9L,GAAA,CAAA4L,KAAA;UAAF3O,EAAE,CAAAqD,SAAA,EA2chG,CAAC;UA3c6FrD,EAAE,CAAAwN,aAAA,IAAAzK,GAAA,CAAA4B,cAAA,SA2chG,CAAC;QAAA;MAAA;MAAA8I,YAAA,GAEuD5L,cAAc,EAA+BD,EAAE,CAAC8L,+BAA+B,EAAgLnL,kBAAkB,EAAocD,YAAY,EAA+BD,EAAE,CAACyM,eAAe;MAAA3H,aAAA;MAAAC,eAAA;IAAA,EAAsO;EAAE;AACxjC;AACAnF,UAAU,CAAC,CACPH,YAAY,CAAC,CAAC,CACjB,EAAEqM,oBAAoB,CAACtE,SAAS,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC;AACxD;EAAA,QAAAxC,SAAA,oBAAAA,SAAA,KAldoGrH,EAAE,CAAAsH,iBAAA,CAkdX6G,oBAAoB,EAAc,CAAC;IAClHvJ,IAAI,EAAE3E,SAAS;IACfsH,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,eAAe;MACzBhB,QAAQ,EAAE,aAAa;MACvBiB,mBAAmB,EAAE,KAAK;MAC1BN,aAAa,EAAEhH,iBAAiB,CAACwH,IAAI;MACrCP,eAAe,EAAElH,uBAAuB,CAACwH,MAAM;MAC/CX,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiBa,IAAI,EAAE;QACFC,KAAK,EAAE,qBAAqB;QAC5B,kCAAkC,EAAG,yBAAwB;QAC7D,kCAAkC,EAAG;MACzC,CAAC;MACDkG,OAAO,EAAE,CAAClM,cAAc,EAAEU,kBAAkB,EAAED,YAAY,CAAC;MAC3DmE,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE7B,IAAI,EAAE5E,EAAE,CAAC+F;EAAkB,CAAC,EAAE;IAAEnB,IAAI,EAAEoD,eAAe;IAAE8B,UAAU,EAAE,CAAC;MACrFlF,IAAI,EAAEvE;IACV,CAAC,EAAE;MACCuE,IAAI,EAAEnE;IACV,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEkO,KAAK,EAAE,CAAC;MACjC/J,IAAI,EAAEtE;IACV,CAAC,CAAC;IAAEkO,UAAU,EAAE,CAAC;MACb5J,IAAI,EAAEtE;IACV,CAAC,CAAC;IAAEoI,SAAS,EAAE,CAAC;MACZ9D,IAAI,EAAEtE;IACV,CAAC,CAAC;IAAEqE,cAAc,EAAE,CAAC;MACjBC,IAAI,EAAEtE;IACV,CAAC,CAAC;IAAEuI,aAAa,EAAE,CAAC;MAChBjE,IAAI,EAAEtE;IACV,CAAC,CAAC;IAAEwI,YAAY,EAAE,CAAC;MACflE,IAAI,EAAEtE;IACV,CAAC,CAAC;IAAEyI,WAAW,EAAE,CAAC;MACdnE,IAAI,EAAEtE;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAMyO,oBAAoB,CAAC;EACvB;IAAS,IAAI,CAACpJ,IAAI,YAAAqJ,6BAAAnJ,CAAA;MAAA,YAAAA,CAAA,IAAwFkJ,oBAAoB;IAAA,CAAmD;EAAE;EACnL;IAAS,IAAI,CAAC/I,IAAI,kBAxgB8EhG,EAAE,CAAAiG,iBAAA;MAAArB,IAAA,EAwgBJmK,oBAAoB;MAAA7I,SAAA;MAAAC,SAAA;MAAAK,QAAA;MAAAC,UAAA;MAAAC,QAAA,GAxgBlB1G,EAAE,CAAA2G,mBAAA;MAAAC,kBAAA,EAAApE,GAAA;MAAAqE,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAkI,8BAAAnM,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF9C,EAAE,CAAAiH,eAAA;UAAFjH,EAAE,CAAAkH,YAAA,EAwgB0L,CAAC;QAAA;MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAAmH;EAAE;AACtZ;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA1gBoGrH,EAAE,CAAAsH,iBAAA,CA0gBXyH,oBAAoB,EAAc,CAAC;IAClHnK,IAAI,EAAE3E,SAAS;IACfsH,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,eAAe;MACzBhB,QAAQ,EAAE,aAAa;MACvBiB,mBAAmB,EAAE,KAAK;MAC1BN,aAAa,EAAEhH,iBAAiB,CAACwH,IAAI;MACrCP,eAAe,EAAElH,uBAAuB,CAACwH,MAAM;MAC/CX,QAAQ,EAAG,6BAA4B;MACvCa,IAAI,EAAE;QACFC,KAAK,EAAE;MACX,CAAC;MACDpB,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA,MAAMyI,mBAAmB,CAAC;EACtB;IAAS,IAAI,CAACvJ,IAAI,YAAAwJ,4BAAAtJ,CAAA;MAAA,YAAAA,CAAA,IAAwFqJ,mBAAmB;IAAA,CAAmD;EAAE;EAClL;IAAS,IAAI,CAAClJ,IAAI,kBAhiB8EhG,EAAE,CAAAiG,iBAAA;MAAArB,IAAA,EAgiBJsK,mBAAmB;MAAAhJ,SAAA;MAAAC,SAAA;MAAAK,QAAA;MAAAC,UAAA;MAAAC,QAAA,GAhiBjB1G,EAAE,CAAA2G,mBAAA;MAAAC,kBAAA,EAAApE,GAAA;MAAAqE,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAqI,6BAAAtM,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF9C,EAAE,CAAAiH,eAAA;UAAFjH,EAAE,CAAAkH,YAAA,EAgiBsL,CAAC;QAAA;MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAAmH;EAAE;AAClZ;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAliBoGrH,EAAE,CAAAsH,iBAAA,CAkiBX4H,mBAAmB,EAAc,CAAC;IACjHtK,IAAI,EAAE3E,SAAS;IACfsH,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,cAAc;MACxBhB,QAAQ,EAAE,YAAY;MACtBiB,mBAAmB,EAAE,KAAK;MAC1BL,eAAe,EAAElH,uBAAuB,CAACwH,MAAM;MAC/CP,aAAa,EAAEhH,iBAAiB,CAACwH,IAAI;MACrCZ,QAAQ,EAAG,6BAA4B;MACvCa,IAAI,EAAE;QACFC,KAAK,EAAE;MACX,CAAC;MACDpB,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA,MAAM4I,YAAY,CAAC;EACf;IAAS,IAAI,CAAC1J,IAAI,YAAA2J,qBAAAzJ,CAAA;MAAA,YAAAA,CAAA,IAAwFwJ,YAAY;IAAA,CAAkD;EAAE;EAC1K;IAAS,IAAI,CAACE,IAAI,kBAxjB8EvP,EAAE,CAAAwP,gBAAA;MAAA5K,IAAA,EAwjBSyK,YAAY;MAAAtB,OAAA,GAAY/F,eAAe,EAC1InD,mBAAmB,EACnBsJ,oBAAoB,EACpBpE,sBAAsB,EACtBmF,mBAAmB,EACnBH,oBAAoB;MAAAU,OAAA,GAAa9O,YAAY,EAC7CqH,eAAe,EACfnD,mBAAmB,EACnBsJ,oBAAoB,EACpBpE,sBAAsB,EACtBmF,mBAAmB,EACnBH,oBAAoB;IAAA,EAAI;EAAE;EAClC;IAAS,IAAI,CAACW,IAAI,kBApkB8E1P,EAAE,CAAA2P,gBAAA;MAAA5B,OAAA,GAokBiCI,oBAAoB,EAC/IpE,sBAAsB,EAAEpJ,YAAY;IAAA,EAAI;EAAE;AACtD;AACA;EAAA,QAAA0G,SAAA,oBAAAA,SAAA,KAvkBoGrH,EAAE,CAAAsH,iBAAA,CAukBX+H,YAAY,EAAc,CAAC;IAC1GzK,IAAI,EAAElE,QAAQ;IACd6G,IAAI,EAAE,CAAC;MACCwG,OAAO,EAAE,CACL/F,eAAe,EACfnD,mBAAmB,EACnBsJ,oBAAoB,EACpBpE,sBAAsB,EACtBmF,mBAAmB,EACnBH,oBAAoB,CACvB;MACDU,OAAO,EAAE,CACL9O,YAAY,EACZqH,eAAe,EACfnD,mBAAmB,EACnBsJ,oBAAoB,EACpBpE,sBAAsB,EACtBmF,mBAAmB,EACnBH,oBAAoB;IAE5B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAAShH,kBAAkB,EAAEgC,sBAAsB,EAAE/B,eAAe,EAAEnD,mBAAmB,EAAEsJ,oBAAoB,EAAEkB,YAAY,EAAEN,oBAAoB,EAAEG,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}