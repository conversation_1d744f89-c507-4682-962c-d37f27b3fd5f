{"ast": null, "code": "import { ClassAttributor, Scope, StyleAttributor } from 'parchment';\nconst config = {\n  scope: Scope.INLINE,\n  whitelist: ['serif', 'monospace']\n};\nconst FontClass = new ClassAttributor('font', 'ql-font', config);\nclass FontStyleAttributor extends StyleAttributor {\n  value(node) {\n    return super.value(node).replace(/[\"']/g, '');\n  }\n}\nconst FontStyle = new FontStyleAttributor('font', 'font-family', config);\nexport { FontStyle, FontClass };", "map": {"version": 3, "names": ["ClassAttributor", "<PERSON><PERSON>", "StyleAttributor", "config", "scope", "INLINE", "whitelist", "FontClass", "FontStyleAttributor", "value", "node", "replace", "FontStyle"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/quill/formats/font.js"], "sourcesContent": ["import { ClassAttributor, Scope, StyleAttributor } from 'parchment';\nconst config = {\n  scope: Scope.INLINE,\n  whitelist: ['serif', 'monospace']\n};\nconst FontClass = new ClassAttributor('font', 'ql-font', config);\nclass FontStyleAttributor extends StyleAttributor {\n  value(node) {\n    return super.value(node).replace(/[\"']/g, '');\n  }\n}\nconst FontStyle = new FontStyleAttributor('font', 'font-family', config);\nexport { FontStyle, FontClass };\n"], "mappings": "AAAA,SAASA,eAAe,EAAEC,KAAK,EAAEC,eAAe,QAAQ,WAAW;AACnE,MAAMC,MAAM,GAAG;EACbC,KAAK,EAAEH,KAAK,CAACI,MAAM;EACnBC,SAAS,EAAE,CAAC,OAAO,EAAE,WAAW;AAClC,CAAC;AACD,MAAMC,SAAS,GAAG,IAAIP,eAAe,CAAC,MAAM,EAAE,SAAS,EAAEG,MAAM,CAAC;AAChE,MAAMK,mBAAmB,SAASN,eAAe,CAAC;EAChDO,KAAKA,CAACC,IAAI,EAAE;IACV,OAAO,KAAK,CAACD,KAAK,CAACC,IAAI,CAAC,CAACC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;EAC/C;AACF;AACA,MAAMC,SAAS,GAAG,IAAIJ,mBAAmB,CAAC,MAAM,EAAE,aAAa,EAAEL,MAAM,CAAC;AACxE,SAASS,SAAS,EAAEL,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}