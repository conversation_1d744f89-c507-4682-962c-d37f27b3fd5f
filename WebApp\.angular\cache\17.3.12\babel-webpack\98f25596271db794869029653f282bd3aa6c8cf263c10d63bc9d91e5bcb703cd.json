{"ast": null, "code": "(function () {\n  \"use strict\";\n\n  try {\n    if (typeof document < \"u\") {\n      var e = document.createElement(\"style\");\n      e.appendChild(document.createTextNode('.cdx-list{margin:0;padding:0;outline:none;display:grid;counter-reset:item;gap:var(--spacing-s);padding:var(--spacing-xs);--spacing-s: 8px;--spacing-xs: 6px;--list-counter-type: numeric;--radius-border: 5px;--checkbox-background: #fff;--color-border: #C9C9C9;--color-bg-checked: #369FFF;--line-height: 1.45em;--color-bg-checked-hover: #0059AB;--color-tick: #fff;--size-checkbox: 1.2em}.cdx-list__item{line-height:var(--line-height);display:grid;grid-template-columns:auto 1fr;grid-template-rows:auto auto;grid-template-areas:\"checkbox content\" \". child\"}.cdx-list__item-children{display:grid;grid-area:child;gap:var(--spacing-s);padding-top:var(--spacing-s)}.cdx-list__item [contenteditable]{outline:none}.cdx-list__item-content{word-break:break-word;white-space:pre-wrap;grid-area:content;padding-left:var(--spacing-s)}.cdx-list__item:before{counter-increment:item;white-space:nowrap}.cdx-list-ordered .cdx-list__item:before{content:counters(item,\".\",var(--list-counter-type)) \".\"}.cdx-list-ordered{counter-reset:item}.cdx-list-unordered .cdx-list__item:before{content:\"•\"}.cdx-list-checklist .cdx-list__item:before{content:\"\"}.cdx-list__settings .cdx-settings-button{width:50%}.cdx-list__checkbox{padding-top:calc((var(--line-height) - var(--size-checkbox)) / 2);grid-area:checkbox;width:var(--size-checkbox);height:var(--size-checkbox);display:flex;cursor:pointer}.cdx-list__checkbox svg{opacity:0;height:var(--size-checkbox);width:var(--size-checkbox);left:-1px;top:-1px;position:absolute}@media (hover: hover){.cdx-list__checkbox:not(.cdx-list__checkbox--no-hover):hover .cdx-list__checkbox-check svg{opacity:1}}.cdx-list__checkbox--checked{line-height:var(--line-height)}@media (hover: hover){.cdx-list__checkbox--checked:not(.cdx-list__checkbox--checked--no-hover):hover .cdx-checklist__checkbox-check{background:var(--color-bg-checked-hover);border-color:var(--color-bg-checked-hover)}}.cdx-list__checkbox--checked .cdx-list__checkbox-check{background:var(--color-bg-checked);border-color:var(--color-bg-checked)}.cdx-list__checkbox--checked .cdx-list__checkbox-check svg{opacity:1}.cdx-list__checkbox--checked .cdx-list__checkbox-check svg path{stroke:var(--color-tick)}.cdx-list__checkbox--checked .cdx-list__checkbox-check:before{opacity:0;visibility:visible;transform:scale(2.5)}.cdx-list__checkbox-check{cursor:pointer;display:inline-block;position:relative;margin:0 auto;width:var(--size-checkbox);height:var(--size-checkbox);box-sizing:border-box;border-radius:var(--radius-border);border:1px solid var(--color-border);background:var(--checkbox-background)}.cdx-list__checkbox-check:before{content:\"\";position:absolute;top:0;right:0;bottom:0;left:0;border-radius:100%;background-color:var(--color-bg-checked);visibility:hidden;pointer-events:none;transform:scale(1);transition:transform .4s ease-out,opacity .4s}.cdx-list-start-with-field{background:#F8F8F8;border:1px solid rgba(226,226,229,.2);border-radius:6px;padding:2px;display:grid;grid-template-columns:auto auto 1fr;grid-template-rows:auto}.cdx-list-start-with-field--invalid{background:#FFECED;border:1px solid #E13F3F}.cdx-list-start-with-field--invalid .cdx-list-start-with-field__input{color:#e13f3f}.cdx-list-start-with-field__input{font-size:14px;outline:none;font-weight:500;font-family:inherit;border:0;background:transparent;margin:0;padding:0;line-height:22px;min-width:calc(100% - var(--toolbox-buttons-size) - var(--icon-margin-right))}.cdx-list-start-with-field__input::placeholder{color:var(--grayText);font-weight:500}')), document.head.appendChild(e);\n    }\n  } catch (c) {\n    console.error(\"vite-plugin-css-injected-by-js\", c);\n  }\n})();\nconst Ct = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M7 12L10.4884 15.8372C10.5677 15.9245 10.705 15.9245 10.7844 15.8372L17 9\"/></svg>',\n  Ae = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M9.2 12L11.0586 13.8586C11.1367 13.9367 11.2633 13.9367 11.3414 13.8586L14.7 10.5\"/><rect width=\"14\" height=\"14\" x=\"5\" y=\"5\" stroke=\"currentColor\" stroke-width=\"2\" rx=\"4\"/></svg>',\n  $e = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><line x1=\"9\" x2=\"19\" y1=\"7\" y2=\"7\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"/><line x1=\"9\" x2=\"19\" y1=\"12\" y2=\"12\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"/><line x1=\"9\" x2=\"19\" y1=\"17\" y2=\"17\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M5.00001 17H4.99002\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M5.00001 12H4.99002\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M5.00001 7H4.99002\"/></svg>',\n  Be = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><line x1=\"12\" x2=\"19\" y1=\"7\" y2=\"7\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"/><line x1=\"12\" x2=\"19\" y1=\"12\" y2=\"12\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"/><line x1=\"12\" x2=\"19\" y1=\"17\" y2=\"17\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M7.79999 14L7.79999 7.2135C7.79999 7.12872 7.7011 7.0824 7.63597 7.13668L4.79999 9.5\"/></svg>',\n  St = '<svg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M10 14.2L10 7.4135C10 7.32872 9.90111 7.28241 9.83598 7.33668L7 9.7\" stroke=\"black\" stroke-width=\"1.6\" stroke-linecap=\"round\"/><path d=\"M13.2087 14.2H13.2\" stroke=\"black\" stroke-width=\"1.6\" stroke-linecap=\"round\"/></svg>',\n  Ot = '<svg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M13.2087 14.2H13.2\" stroke=\"black\" stroke-width=\"1.6\" stroke-linecap=\"round\"/><path d=\"M10 14.2L10 9.5\" stroke=\"black\" stroke-width=\"1.6\" stroke-linecap=\"round\"/><path d=\"M10 7.01L10 7\" stroke=\"black\" stroke-width=\"1.8\" stroke-linecap=\"round\"/></svg>',\n  kt = '<svg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M13.2087 14.2H13.2\" stroke=\"black\" stroke-width=\"1.6\" stroke-linecap=\"round\"/><path d=\"M10 14.2L10 7.2\" stroke=\"black\" stroke-width=\"1.6\" stroke-linecap=\"round\"/></svg>',\n  _t = '<svg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M16.0087 14.2H16\" stroke=\"black\" stroke-width=\"1.6\" stroke-linecap=\"round\"/><path d=\"M7 14.2L7.78865 12M13 14.2L12.1377 12M7.78865 12C7.78865 12 9.68362 7 10 7C10.3065 7 12.1377 12 12.1377 12M7.78865 12L12.1377 12\" stroke=\"black\" stroke-width=\"1.6\" stroke-linecap=\"round\"/></svg>',\n  Et = '<svg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M14.2087 14.2H14.2\" stroke=\"black\" stroke-width=\"1.6\" stroke-linecap=\"round\"/><path d=\"M11.5 14.5C11.5 14.5 11 13.281 11 12.5M7 9.5C7 9.5 7.5 8.5 9 8.5C10.5 8.5 11 9.5 11 10.5L11 11.5M11 11.5L11 12.5M11 11.5C11 11.5 7 11 7 13C7 15.3031 11 15 11 12.5\" stroke=\"black\" stroke-width=\"1.6\" stroke-linecap=\"round\"/></svg>',\n  It = '<svg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M8 14.2L8 7.4135C8 7.32872 7.90111 7.28241 7.83598 7.33668L5 9.7\" stroke=\"black\" stroke-width=\"1.6\" stroke-linecap=\"round\"/><path d=\"M14 13L16.4167 10.7778M16.4167 10.7778L14 8.5M16.4167 10.7778H11.6562\" stroke=\"black\" stroke-width=\"1.6\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>';\nvar A = typeof globalThis < \"u\" ? globalThis : typeof window < \"u\" ? window : typeof global < \"u\" ? global : typeof self < \"u\" ? self : {};\nfunction wt(e) {\n  if (e.__esModule) return e;\n  var t = e.default;\n  if (typeof t == \"function\") {\n    var n = function r() {\n      return this instanceof r ? Reflect.construct(t, arguments, this.constructor) : t.apply(this, arguments);\n    };\n    n.prototype = t.prototype;\n  } else n = {};\n  return Object.defineProperty(n, \"__esModule\", {\n    value: !0\n  }), Object.keys(e).forEach(function (r) {\n    var i = Object.getOwnPropertyDescriptor(e, r);\n    Object.defineProperty(n, r, i.get ? i : {\n      enumerable: !0,\n      get: function () {\n        return e[r];\n      }\n    });\n  }), n;\n}\nvar c = {},\n  V = {},\n  Y = {};\nObject.defineProperty(Y, \"__esModule\", {\n  value: !0\n});\nY.allInputsSelector = Pt;\nfunction Pt() {\n  var e = [\"text\", \"password\", \"email\", \"number\", \"search\", \"tel\", \"url\"];\n  return \"[contenteditable=true], textarea, input:not([type]), \" + e.map(function (t) {\n    return 'input[type=\"'.concat(t, '\"]');\n  }).join(\", \");\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.allInputsSelector = void 0;\n  var t = Y;\n  Object.defineProperty(e, \"allInputsSelector\", {\n    enumerable: !0,\n    get: function () {\n      return t.allInputsSelector;\n    }\n  });\n})(V);\nvar k = {},\n  J = {};\nObject.defineProperty(J, \"__esModule\", {\n  value: !0\n});\nJ.isNativeInput = jt;\nfunction jt(e) {\n  var t = [\"INPUT\", \"TEXTAREA\"];\n  return e && e.tagName ? t.includes(e.tagName) : !1;\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.isNativeInput = void 0;\n  var t = J;\n  Object.defineProperty(e, \"isNativeInput\", {\n    enumerable: !0,\n    get: function () {\n      return t.isNativeInput;\n    }\n  });\n})(k);\nvar Fe = {},\n  Q = {};\nObject.defineProperty(Q, \"__esModule\", {\n  value: !0\n});\nQ.append = Tt;\nfunction Tt(e, t) {\n  Array.isArray(t) ? t.forEach(function (n) {\n    e.appendChild(n);\n  }) : e.appendChild(t);\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.append = void 0;\n  var t = Q;\n  Object.defineProperty(e, \"append\", {\n    enumerable: !0,\n    get: function () {\n      return t.append;\n    }\n  });\n})(Fe);\nvar Z = {},\n  x = {};\nObject.defineProperty(x, \"__esModule\", {\n  value: !0\n});\nx.blockElements = Lt;\nfunction Lt() {\n  return [\"address\", \"article\", \"aside\", \"blockquote\", \"canvas\", \"div\", \"dl\", \"dt\", \"fieldset\", \"figcaption\", \"figure\", \"footer\", \"form\", \"h1\", \"h2\", \"h3\", \"h4\", \"h5\", \"h6\", \"header\", \"hgroup\", \"hr\", \"li\", \"main\", \"nav\", \"noscript\", \"ol\", \"output\", \"p\", \"pre\", \"ruby\", \"section\", \"table\", \"tbody\", \"thead\", \"tr\", \"tfoot\", \"ul\", \"video\"];\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.blockElements = void 0;\n  var t = x;\n  Object.defineProperty(e, \"blockElements\", {\n    enumerable: !0,\n    get: function () {\n      return t.blockElements;\n    }\n  });\n})(Z);\nvar Re = {},\n  ee = {};\nObject.defineProperty(ee, \"__esModule\", {\n  value: !0\n});\nee.calculateBaseline = Mt;\nfunction Mt(e) {\n  var t = window.getComputedStyle(e),\n    n = parseFloat(t.fontSize),\n    r = parseFloat(t.lineHeight) || n * 1.2,\n    i = parseFloat(t.paddingTop),\n    a = parseFloat(t.borderTopWidth),\n    l = parseFloat(t.marginTop),\n    s = n * 0.8,\n    o = (r - n) / 2,\n    d = l + a + i + o + s;\n  return d;\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.calculateBaseline = void 0;\n  var t = ee;\n  Object.defineProperty(e, \"calculateBaseline\", {\n    enumerable: !0,\n    get: function () {\n      return t.calculateBaseline;\n    }\n  });\n})(Re);\nvar qe = {},\n  te = {},\n  ne = {},\n  re = {};\nObject.defineProperty(re, \"__esModule\", {\n  value: !0\n});\nre.isContentEditable = Nt;\nfunction Nt(e) {\n  return e.contentEditable === \"true\";\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.isContentEditable = void 0;\n  var t = re;\n  Object.defineProperty(e, \"isContentEditable\", {\n    enumerable: !0,\n    get: function () {\n      return t.isContentEditable;\n    }\n  });\n})(ne);\nObject.defineProperty(te, \"__esModule\", {\n  value: !0\n});\nte.canSetCaret = Bt;\nvar At = k,\n  $t = ne;\nfunction Bt(e) {\n  var t = !0;\n  if ((0, At.isNativeInput)(e)) switch (e.type) {\n    case \"file\":\n    case \"checkbox\":\n    case \"radio\":\n    case \"hidden\":\n    case \"submit\":\n    case \"button\":\n    case \"image\":\n    case \"reset\":\n      t = !1;\n      break;\n  } else t = (0, $t.isContentEditable)(e);\n  return t;\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.canSetCaret = void 0;\n  var t = te;\n  Object.defineProperty(e, \"canSetCaret\", {\n    enumerable: !0,\n    get: function () {\n      return t.canSetCaret;\n    }\n  });\n})(qe);\nvar $ = {},\n  ie = {};\nfunction Wt(e, t, n) {\n  const r = n.value !== void 0 ? \"value\" : \"get\",\n    i = n[r],\n    a = `#${t}Cache`;\n  if (n[r] = function (...l) {\n    return this[a] === void 0 && (this[a] = i.apply(this, l)), this[a];\n  }, r === \"get\" && n.set) {\n    const l = n.set;\n    n.set = function (s) {\n      delete e[a], l.apply(this, s);\n    };\n  }\n  return n;\n}\nfunction Ue() {\n  const e = {\n      win: !1,\n      mac: !1,\n      x11: !1,\n      linux: !1\n    },\n    t = Object.keys(e).find(n => window.navigator.appVersion.toLowerCase().indexOf(n) !== -1);\n  return t !== void 0 && (e[t] = !0), e;\n}\nfunction ae(e) {\n  return e != null && e !== \"\" && (typeof e != \"object\" || Object.keys(e).length > 0);\n}\nfunction Dt(e) {\n  return !ae(e);\n}\nconst Ht = () => typeof window < \"u\" && window.navigator !== null && ae(window.navigator.platform) && (/iP(ad|hone|od)/.test(window.navigator.platform) || window.navigator.platform === \"MacIntel\" && window.navigator.maxTouchPoints > 1);\nfunction Ft(e) {\n  const t = Ue();\n  return e = e.replace(/shift/gi, \"⇧\").replace(/backspace/gi, \"⌫\").replace(/enter/gi, \"⏎\").replace(/up/gi, \"↑\").replace(/left/gi, \"→\").replace(/down/gi, \"↓\").replace(/right/gi, \"←\").replace(/escape/gi, \"⎋\").replace(/insert/gi, \"Ins\").replace(/delete/gi, \"␡\").replace(/\\+/gi, \"+\"), t.mac ? e = e.replace(/ctrl|cmd/gi, \"⌘\").replace(/alt/gi, \"⌥\") : e = e.replace(/cmd/gi, \"Ctrl\").replace(/windows/gi, \"WIN\"), e;\n}\nfunction Rt(e) {\n  return e[0].toUpperCase() + e.slice(1);\n}\nfunction qt(e) {\n  const t = document.createElement(\"div\");\n  t.style.position = \"absolute\", t.style.left = \"-999px\", t.style.bottom = \"-999px\", t.innerHTML = e, document.body.appendChild(t);\n  const n = window.getSelection(),\n    r = document.createRange();\n  if (r.selectNode(t), n === null) throw new Error(\"Cannot copy text to clipboard\");\n  n.removeAllRanges(), n.addRange(r), document.execCommand(\"copy\"), document.body.removeChild(t);\n}\nfunction Ut(e, t, n) {\n  let r;\n  return (...i) => {\n    const a = this,\n      l = () => {\n        r = void 0, n !== !0 && e.apply(a, i);\n      },\n      s = n === !0 && r !== void 0;\n    window.clearTimeout(r), r = window.setTimeout(l, t), s && e.apply(a, i);\n  };\n}\nfunction S(e) {\n  return Object.prototype.toString.call(e).match(/\\s([a-zA-Z]+)/)[1].toLowerCase();\n}\nfunction zt(e) {\n  return S(e) === \"boolean\";\n}\nfunction ze(e) {\n  return S(e) === \"function\" || S(e) === \"asyncfunction\";\n}\nfunction Kt(e) {\n  return ze(e) && /^\\s*class\\s+/.test(e.toString());\n}\nfunction Xt(e) {\n  return S(e) === \"number\";\n}\nfunction M(e) {\n  return S(e) === \"object\";\n}\nfunction Gt(e) {\n  return Promise.resolve(e) === e;\n}\nfunction Vt(e) {\n  return S(e) === \"string\";\n}\nfunction Yt(e) {\n  return S(e) === \"undefined\";\n}\nfunction X(e, ...t) {\n  if (!t.length) return e;\n  const n = t.shift();\n  if (M(e) && M(n)) for (const r in n) M(n[r]) ? (e[r] === void 0 && Object.assign(e, {\n    [r]: {}\n  }), X(e[r], n[r])) : Object.assign(e, {\n    [r]: n[r]\n  });\n  return X(e, ...t);\n}\nfunction Jt(e, t, n) {\n  const r = `«${t}» is deprecated and will be removed in the next major release. Please use the «${n}» instead.`;\n  e && console.warn(r);\n}\nfunction Qt(e) {\n  try {\n    return new URL(e).href;\n  } catch {}\n  return e.substring(0, 2) === \"//\" ? window.location.protocol + e : window.location.origin + e;\n}\nfunction Zt(e) {\n  return e > 47 && e < 58 || e === 32 || e === 13 || e === 229 || e > 64 && e < 91 || e > 95 && e < 112 || e > 185 && e < 193 || e > 218 && e < 223;\n}\nconst xt = {\n    BACKSPACE: 8,\n    TAB: 9,\n    ENTER: 13,\n    SHIFT: 16,\n    CTRL: 17,\n    ALT: 18,\n    ESC: 27,\n    SPACE: 32,\n    LEFT: 37,\n    UP: 38,\n    DOWN: 40,\n    RIGHT: 39,\n    DELETE: 46,\n    META: 91,\n    SLASH: 191\n  },\n  en = {\n    LEFT: 0,\n    WHEEL: 1,\n    RIGHT: 2,\n    BACKWARD: 3,\n    FORWARD: 4\n  };\nclass tn {\n  constructor() {\n    this.completed = Promise.resolve();\n  }\n  /**\n   * Add new promise to queue\n   * @param operation - promise should be added to queue\n   */\n  add(t) {\n    return new Promise((n, r) => {\n      this.completed = this.completed.then(t).then(n).catch(r);\n    });\n  }\n}\nfunction nn(e, t, n = void 0) {\n  let r,\n    i,\n    a,\n    l = null,\n    s = 0;\n  n || (n = {});\n  const o = function () {\n    s = n.leading === !1 ? 0 : Date.now(), l = null, a = e.apply(r, i), l === null && (r = i = null);\n  };\n  return function () {\n    const d = Date.now();\n    !s && n.leading === !1 && (s = d);\n    const u = t - (d - s);\n    return r = this, i = arguments, u <= 0 || u > t ? (l && (clearTimeout(l), l = null), s = d, a = e.apply(r, i), l === null && (r = i = null)) : !l && n.trailing !== !1 && (l = setTimeout(o, u)), a;\n  };\n}\nconst rn = /* @__PURE__ */Object.freeze( /* @__PURE__ */Object.defineProperty({\n    __proto__: null,\n    PromiseQueue: tn,\n    beautifyShortcut: Ft,\n    cacheable: Wt,\n    capitalize: Rt,\n    copyTextToClipboard: qt,\n    debounce: Ut,\n    deepMerge: X,\n    deprecationAssert: Jt,\n    getUserOS: Ue,\n    getValidUrl: Qt,\n    isBoolean: zt,\n    isClass: Kt,\n    isEmpty: Dt,\n    isFunction: ze,\n    isIosDevice: Ht,\n    isNumber: Xt,\n    isObject: M,\n    isPrintableKey: Zt,\n    isPromise: Gt,\n    isString: Vt,\n    isUndefined: Yt,\n    keyCodes: xt,\n    mouseButtons: en,\n    notEmpty: ae,\n    throttle: nn,\n    typeOf: S\n  }, Symbol.toStringTag, {\n    value: \"Module\"\n  })),\n  le = /* @__PURE__ */wt(rn);\nObject.defineProperty(ie, \"__esModule\", {\n  value: !0\n});\nie.containsOnlyInlineElements = sn;\nvar an = le,\n  ln = Z;\nfunction sn(e) {\n  var t;\n  (0, an.isString)(e) ? (t = document.createElement(\"div\"), t.innerHTML = e) : t = e;\n  var n = function (r) {\n    return !(0, ln.blockElements)().includes(r.tagName.toLowerCase()) && Array.from(r.children).every(n);\n  };\n  return Array.from(t.children).every(n);\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.containsOnlyInlineElements = void 0;\n  var t = ie;\n  Object.defineProperty(e, \"containsOnlyInlineElements\", {\n    enumerable: !0,\n    get: function () {\n      return t.containsOnlyInlineElements;\n    }\n  });\n})($);\nvar Ke = {},\n  se = {},\n  B = {},\n  oe = {};\nObject.defineProperty(oe, \"__esModule\", {\n  value: !0\n});\noe.make = on;\nfunction on(e, t, n) {\n  var r;\n  t === void 0 && (t = null), n === void 0 && (n = {});\n  var i = document.createElement(e);\n  if (Array.isArray(t)) {\n    var a = t.filter(function (s) {\n      return s !== void 0;\n    });\n    (r = i.classList).add.apply(r, a);\n  } else t !== null && i.classList.add(t);\n  for (var l in n) Object.prototype.hasOwnProperty.call(n, l) && (i[l] = n[l]);\n  return i;\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.make = void 0;\n  var t = oe;\n  Object.defineProperty(e, \"make\", {\n    enumerable: !0,\n    get: function () {\n      return t.make;\n    }\n  });\n})(B);\nObject.defineProperty(se, \"__esModule\", {\n  value: !0\n});\nse.fragmentToString = cn;\nvar un = B;\nfunction cn(e) {\n  var t = (0, un.make)(\"div\");\n  return t.appendChild(e), t.innerHTML;\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.fragmentToString = void 0;\n  var t = se;\n  Object.defineProperty(e, \"fragmentToString\", {\n    enumerable: !0,\n    get: function () {\n      return t.fragmentToString;\n    }\n  });\n})(Ke);\nvar Xe = {},\n  ue = {};\nObject.defineProperty(ue, \"__esModule\", {\n  value: !0\n});\nue.getContentLength = fn;\nvar dn = k;\nfunction fn(e) {\n  var t, n;\n  return (0, dn.isNativeInput)(e) ? e.value.length : e.nodeType === Node.TEXT_NODE ? e.length : (n = (t = e.textContent) === null || t === void 0 ? void 0 : t.length) !== null && n !== void 0 ? n : 0;\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.getContentLength = void 0;\n  var t = ue;\n  Object.defineProperty(e, \"getContentLength\", {\n    enumerable: !0,\n    get: function () {\n      return t.getContentLength;\n    }\n  });\n})(Xe);\nvar ce = {},\n  de = {},\n  We = A && A.__spreadArray || function (e, t, n) {\n    if (n || arguments.length === 2) for (var r = 0, i = t.length, a; r < i; r++) (a || !(r in t)) && (a || (a = Array.prototype.slice.call(t, 0, r)), a[r] = t[r]);\n    return e.concat(a || Array.prototype.slice.call(t));\n  };\nObject.defineProperty(de, \"__esModule\", {\n  value: !0\n});\nde.getDeepestBlockElements = Ge;\nvar pn = $;\nfunction Ge(e) {\n  return (0, pn.containsOnlyInlineElements)(e) ? [e] : Array.from(e.children).reduce(function (t, n) {\n    return We(We([], t, !0), Ge(n), !0);\n  }, []);\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.getDeepestBlockElements = void 0;\n  var t = de;\n  Object.defineProperty(e, \"getDeepestBlockElements\", {\n    enumerable: !0,\n    get: function () {\n      return t.getDeepestBlockElements;\n    }\n  });\n})(ce);\nvar Ve = {},\n  fe = {},\n  W = {},\n  pe = {};\nObject.defineProperty(pe, \"__esModule\", {\n  value: !0\n});\npe.isLineBreakTag = hn;\nfunction hn(e) {\n  return [\"BR\", \"WBR\"].includes(e.tagName);\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.isLineBreakTag = void 0;\n  var t = pe;\n  Object.defineProperty(e, \"isLineBreakTag\", {\n    enumerable: !0,\n    get: function () {\n      return t.isLineBreakTag;\n    }\n  });\n})(W);\nvar D = {},\n  he = {};\nObject.defineProperty(he, \"__esModule\", {\n  value: !0\n});\nhe.isSingleTag = mn;\nfunction mn(e) {\n  return [\"AREA\", \"BASE\", \"BR\", \"COL\", \"COMMAND\", \"EMBED\", \"HR\", \"IMG\", \"INPUT\", \"KEYGEN\", \"LINK\", \"META\", \"PARAM\", \"SOURCE\", \"TRACK\", \"WBR\"].includes(e.tagName);\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.isSingleTag = void 0;\n  var t = he;\n  Object.defineProperty(e, \"isSingleTag\", {\n    enumerable: !0,\n    get: function () {\n      return t.isSingleTag;\n    }\n  });\n})(D);\nObject.defineProperty(fe, \"__esModule\", {\n  value: !0\n});\nfe.getDeepestNode = Ye;\nvar gn = k,\n  vn = W,\n  bn = D;\nfunction Ye(e, t) {\n  t === void 0 && (t = !1);\n  var n = t ? \"lastChild\" : \"firstChild\",\n    r = t ? \"previousSibling\" : \"nextSibling\";\n  if (e.nodeType === Node.ELEMENT_NODE && e[n]) {\n    var i = e[n];\n    if ((0, bn.isSingleTag)(i) && !(0, gn.isNativeInput)(i) && !(0, vn.isLineBreakTag)(i)) if (i[r]) i = i[r];else if (i.parentNode !== null && i.parentNode[r]) i = i.parentNode[r];else return i.parentNode;\n    return Ye(i, t);\n  }\n  return e;\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.getDeepestNode = void 0;\n  var t = fe;\n  Object.defineProperty(e, \"getDeepestNode\", {\n    enumerable: !0,\n    get: function () {\n      return t.getDeepestNode;\n    }\n  });\n})(Ve);\nvar Je = {},\n  me = {},\n  T = A && A.__spreadArray || function (e, t, n) {\n    if (n || arguments.length === 2) for (var r = 0, i = t.length, a; r < i; r++) (a || !(r in t)) && (a || (a = Array.prototype.slice.call(t, 0, r)), a[r] = t[r]);\n    return e.concat(a || Array.prototype.slice.call(t));\n  };\nObject.defineProperty(me, \"__esModule\", {\n  value: !0\n});\nme.findAllInputs = kn;\nvar yn = $,\n  Cn = ce,\n  Sn = V,\n  On = k;\nfunction kn(e) {\n  return Array.from(e.querySelectorAll((0, Sn.allInputsSelector)())).reduce(function (t, n) {\n    return (0, On.isNativeInput)(n) || (0, yn.containsOnlyInlineElements)(n) ? T(T([], t, !0), [n], !1) : T(T([], t, !0), (0, Cn.getDeepestBlockElements)(n), !0);\n  }, []);\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.findAllInputs = void 0;\n  var t = me;\n  Object.defineProperty(e, \"findAllInputs\", {\n    enumerable: !0,\n    get: function () {\n      return t.findAllInputs;\n    }\n  });\n})(Je);\nvar Qe = {},\n  ge = {};\nObject.defineProperty(ge, \"__esModule\", {\n  value: !0\n});\nge.isCollapsedWhitespaces = _n;\nfunction _n(e) {\n  return !/[^\\t\\n\\r ]/.test(e);\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.isCollapsedWhitespaces = void 0;\n  var t = ge;\n  Object.defineProperty(e, \"isCollapsedWhitespaces\", {\n    enumerable: !0,\n    get: function () {\n      return t.isCollapsedWhitespaces;\n    }\n  });\n})(Qe);\nvar ve = {},\n  be = {};\nObject.defineProperty(be, \"__esModule\", {\n  value: !0\n});\nbe.isElement = In;\nvar En = le;\nfunction In(e) {\n  return (0, En.isNumber)(e) ? !1 : !!e && !!e.nodeType && e.nodeType === Node.ELEMENT_NODE;\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.isElement = void 0;\n  var t = be;\n  Object.defineProperty(e, \"isElement\", {\n    enumerable: !0,\n    get: function () {\n      return t.isElement;\n    }\n  });\n})(ve);\nvar Ze = {},\n  ye = {},\n  Ce = {},\n  Se = {};\nObject.defineProperty(Se, \"__esModule\", {\n  value: !0\n});\nSe.isLeaf = wn;\nfunction wn(e) {\n  return e === null ? !1 : e.childNodes.length === 0;\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.isLeaf = void 0;\n  var t = Se;\n  Object.defineProperty(e, \"isLeaf\", {\n    enumerable: !0,\n    get: function () {\n      return t.isLeaf;\n    }\n  });\n})(Ce);\nvar Oe = {},\n  ke = {};\nObject.defineProperty(ke, \"__esModule\", {\n  value: !0\n});\nke.isNodeEmpty = Mn;\nvar Pn = W,\n  jn = ve,\n  Tn = k,\n  Ln = D;\nfunction Mn(e, t) {\n  var n = \"\";\n  return (0, Ln.isSingleTag)(e) && !(0, Pn.isLineBreakTag)(e) ? !1 : ((0, jn.isElement)(e) && (0, Tn.isNativeInput)(e) ? n = e.value : e.textContent !== null && (n = e.textContent.replace(\"​\", \"\")), t !== void 0 && (n = n.replace(new RegExp(t, \"g\"), \"\")), n.trim().length === 0);\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.isNodeEmpty = void 0;\n  var t = ke;\n  Object.defineProperty(e, \"isNodeEmpty\", {\n    enumerable: !0,\n    get: function () {\n      return t.isNodeEmpty;\n    }\n  });\n})(Oe);\nObject.defineProperty(ye, \"__esModule\", {\n  value: !0\n});\nye.isEmpty = $n;\nvar Nn = Ce,\n  An = Oe;\nfunction $n(e, t) {\n  e.normalize();\n  for (var n = [e]; n.length > 0;) {\n    var r = n.shift();\n    if (r) {\n      if (e = r, (0, Nn.isLeaf)(e) && !(0, An.isNodeEmpty)(e, t)) return !1;\n      n.push.apply(n, Array.from(e.childNodes));\n    }\n  }\n  return !0;\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.isEmpty = void 0;\n  var t = ye;\n  Object.defineProperty(e, \"isEmpty\", {\n    enumerable: !0,\n    get: function () {\n      return t.isEmpty;\n    }\n  });\n})(Ze);\nvar xe = {},\n  _e = {};\nObject.defineProperty(_e, \"__esModule\", {\n  value: !0\n});\n_e.isFragment = Wn;\nvar Bn = le;\nfunction Wn(e) {\n  return (0, Bn.isNumber)(e) ? !1 : !!e && !!e.nodeType && e.nodeType === Node.DOCUMENT_FRAGMENT_NODE;\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.isFragment = void 0;\n  var t = _e;\n  Object.defineProperty(e, \"isFragment\", {\n    enumerable: !0,\n    get: function () {\n      return t.isFragment;\n    }\n  });\n})(xe);\nvar et = {},\n  Ee = {};\nObject.defineProperty(Ee, \"__esModule\", {\n  value: !0\n});\nEe.isHTMLString = Hn;\nvar Dn = B;\nfunction Hn(e) {\n  var t = (0, Dn.make)(\"div\");\n  return t.innerHTML = e, t.childElementCount > 0;\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.isHTMLString = void 0;\n  var t = Ee;\n  Object.defineProperty(e, \"isHTMLString\", {\n    enumerable: !0,\n    get: function () {\n      return t.isHTMLString;\n    }\n  });\n})(et);\nvar tt = {},\n  Ie = {};\nObject.defineProperty(Ie, \"__esModule\", {\n  value: !0\n});\nIe.offset = Fn;\nfunction Fn(e) {\n  var t = e.getBoundingClientRect(),\n    n = window.pageXOffset || document.documentElement.scrollLeft,\n    r = window.pageYOffset || document.documentElement.scrollTop,\n    i = t.top + r,\n    a = t.left + n;\n  return {\n    top: i,\n    left: a,\n    bottom: i + t.height,\n    right: a + t.width\n  };\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.offset = void 0;\n  var t = Ie;\n  Object.defineProperty(e, \"offset\", {\n    enumerable: !0,\n    get: function () {\n      return t.offset;\n    }\n  });\n})(tt);\nvar nt = {},\n  we = {};\nObject.defineProperty(we, \"__esModule\", {\n  value: !0\n});\nwe.prepend = Rn;\nfunction Rn(e, t) {\n  Array.isArray(t) ? (t = t.reverse(), t.forEach(function (n) {\n    return e.prepend(n);\n  })) : e.prepend(t);\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.prepend = void 0;\n  var t = we;\n  Object.defineProperty(e, \"prepend\", {\n    enumerable: !0,\n    get: function () {\n      return t.prepend;\n    }\n  });\n})(nt);\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.prepend = e.offset = e.make = e.isLineBreakTag = e.isSingleTag = e.isNodeEmpty = e.isLeaf = e.isHTMLString = e.isFragment = e.isEmpty = e.isElement = e.isContentEditable = e.isCollapsedWhitespaces = e.findAllInputs = e.isNativeInput = e.allInputsSelector = e.getDeepestNode = e.getDeepestBlockElements = e.getContentLength = e.fragmentToString = e.containsOnlyInlineElements = e.canSetCaret = e.calculateBaseline = e.blockElements = e.append = void 0;\n  var t = V;\n  Object.defineProperty(e, \"allInputsSelector\", {\n    enumerable: !0,\n    get: function () {\n      return t.allInputsSelector;\n    }\n  });\n  var n = k;\n  Object.defineProperty(e, \"isNativeInput\", {\n    enumerable: !0,\n    get: function () {\n      return n.isNativeInput;\n    }\n  });\n  var r = Fe;\n  Object.defineProperty(e, \"append\", {\n    enumerable: !0,\n    get: function () {\n      return r.append;\n    }\n  });\n  var i = Z;\n  Object.defineProperty(e, \"blockElements\", {\n    enumerable: !0,\n    get: function () {\n      return i.blockElements;\n    }\n  });\n  var a = Re;\n  Object.defineProperty(e, \"calculateBaseline\", {\n    enumerable: !0,\n    get: function () {\n      return a.calculateBaseline;\n    }\n  });\n  var l = qe;\n  Object.defineProperty(e, \"canSetCaret\", {\n    enumerable: !0,\n    get: function () {\n      return l.canSetCaret;\n    }\n  });\n  var s = $;\n  Object.defineProperty(e, \"containsOnlyInlineElements\", {\n    enumerable: !0,\n    get: function () {\n      return s.containsOnlyInlineElements;\n    }\n  });\n  var o = Ke;\n  Object.defineProperty(e, \"fragmentToString\", {\n    enumerable: !0,\n    get: function () {\n      return o.fragmentToString;\n    }\n  });\n  var d = Xe;\n  Object.defineProperty(e, \"getContentLength\", {\n    enumerable: !0,\n    get: function () {\n      return d.getContentLength;\n    }\n  });\n  var u = ce;\n  Object.defineProperty(e, \"getDeepestBlockElements\", {\n    enumerable: !0,\n    get: function () {\n      return u.getDeepestBlockElements;\n    }\n  });\n  var p = Ve;\n  Object.defineProperty(e, \"getDeepestNode\", {\n    enumerable: !0,\n    get: function () {\n      return p.getDeepestNode;\n    }\n  });\n  var g = Je;\n  Object.defineProperty(e, \"findAllInputs\", {\n    enumerable: !0,\n    get: function () {\n      return g.findAllInputs;\n    }\n  });\n  var w = Qe;\n  Object.defineProperty(e, \"isCollapsedWhitespaces\", {\n    enumerable: !0,\n    get: function () {\n      return w.isCollapsedWhitespaces;\n    }\n  });\n  var _ = ne;\n  Object.defineProperty(e, \"isContentEditable\", {\n    enumerable: !0,\n    get: function () {\n      return _.isContentEditable;\n    }\n  });\n  var ut = ve;\n  Object.defineProperty(e, \"isElement\", {\n    enumerable: !0,\n    get: function () {\n      return ut.isElement;\n    }\n  });\n  var ct = Ze;\n  Object.defineProperty(e, \"isEmpty\", {\n    enumerable: !0,\n    get: function () {\n      return ct.isEmpty;\n    }\n  });\n  var dt = xe;\n  Object.defineProperty(e, \"isFragment\", {\n    enumerable: !0,\n    get: function () {\n      return dt.isFragment;\n    }\n  });\n  var ft = et;\n  Object.defineProperty(e, \"isHTMLString\", {\n    enumerable: !0,\n    get: function () {\n      return ft.isHTMLString;\n    }\n  });\n  var pt = Ce;\n  Object.defineProperty(e, \"isLeaf\", {\n    enumerable: !0,\n    get: function () {\n      return pt.isLeaf;\n    }\n  });\n  var ht = Oe;\n  Object.defineProperty(e, \"isNodeEmpty\", {\n    enumerable: !0,\n    get: function () {\n      return ht.isNodeEmpty;\n    }\n  });\n  var mt = W;\n  Object.defineProperty(e, \"isLineBreakTag\", {\n    enumerable: !0,\n    get: function () {\n      return mt.isLineBreakTag;\n    }\n  });\n  var gt = D;\n  Object.defineProperty(e, \"isSingleTag\", {\n    enumerable: !0,\n    get: function () {\n      return gt.isSingleTag;\n    }\n  });\n  var vt = B;\n  Object.defineProperty(e, \"make\", {\n    enumerable: !0,\n    get: function () {\n      return vt.make;\n    }\n  });\n  var bt = tt;\n  Object.defineProperty(e, \"offset\", {\n    enumerable: !0,\n    get: function () {\n      return bt.offset;\n    }\n  });\n  var yt = nt;\n  Object.defineProperty(e, \"prepend\", {\n    enumerable: !0,\n    get: function () {\n      return yt.prepend;\n    }\n  });\n})(c);\nconst m = \"cdx-list\",\n  h = {\n    wrapper: m,\n    item: `${m}__item`,\n    itemContent: `${m}__item-content`,\n    itemChildren: `${m}__item-children`\n  };\nclass v {\n  /**\n   * Getter for all CSS classes used in unordered list rendering\n   */\n  static get CSS() {\n    return {\n      ...h,\n      orderedList: `${m}-ordered`\n    };\n  }\n  /**\n   * Assign passed readonly mode and config to relevant class properties\n   * @param readonly - read-only mode flag\n   * @param config - user config for Tool\n   */\n  constructor(t, n) {\n    this.config = n, this.readOnly = t;\n  }\n  /**\n   * Renders ol wrapper for list\n   * @param isRoot - boolean variable that represents level of the wrappre (root or childList)\n   * @returns - created html ol element\n   */\n  renderWrapper(t) {\n    let n;\n    return t === !0 ? n = c.make(\"ol\", [v.CSS.wrapper, v.CSS.orderedList]) : n = c.make(\"ol\", [v.CSS.orderedList, v.CSS.itemChildren]), n;\n  }\n  /**\n   * Redners list item element\n   * @param content - content used in list item rendering\n   * @param _meta - meta of the list item unused in rendering of the ordered list\n   * @returns - created html list item element\n   */\n  renderItem(t, n) {\n    const r = c.make(\"li\", v.CSS.item),\n      i = c.make(\"div\", v.CSS.itemContent, {\n        innerHTML: t,\n        contentEditable: (!this.readOnly).toString()\n      });\n    return r.appendChild(i), r;\n  }\n  /**\n   * Return the item content\n   * @param item - item wrapper (<li>)\n   * @returns - item content string\n   */\n  getItemContent(t) {\n    const n = t.querySelector(`.${v.CSS.itemContent}`);\n    return !n || c.isEmpty(n) ? \"\" : n.innerHTML;\n  }\n  /**\n   * Returns item meta, for ordered list\n   * @returns item meta object\n   */\n  getItemMeta() {\n    return {};\n  }\n  /**\n   * Returns default item meta used on creation of the new item\n   */\n  composeDefaultMeta() {\n    return {};\n  }\n}\nclass b {\n  /**\n   * Getter for all CSS classes used in unordered list rendering\n   */\n  static get CSS() {\n    return {\n      ...h,\n      unorderedList: `${m}-unordered`\n    };\n  }\n  /**\n   * Assign passed readonly mode and config to relevant class properties\n   * @param readonly - read-only mode flag\n   * @param config - user config for Tool\n   */\n  constructor(t, n) {\n    this.config = n, this.readOnly = t;\n  }\n  /**\n   * Renders ol wrapper for list\n   * @param isRoot - boolean variable that represents level of the wrappre (root or childList)\n   * @returns - created html ul element\n   */\n  renderWrapper(t) {\n    let n;\n    return t === !0 ? n = c.make(\"ul\", [b.CSS.wrapper, b.CSS.unorderedList]) : n = c.make(\"ul\", [b.CSS.unorderedList, b.CSS.itemChildren]), n;\n  }\n  /**\n   * Redners list item element\n   * @param content - content used in list item rendering\n   * @param _meta - meta of the list item unused in rendering of the unordered list\n   * @returns - created html list item element\n   */\n  renderItem(t, n) {\n    const r = c.make(\"li\", b.CSS.item),\n      i = c.make(\"div\", b.CSS.itemContent, {\n        innerHTML: t,\n        contentEditable: (!this.readOnly).toString()\n      });\n    return r.appendChild(i), r;\n  }\n  /**\n   * Return the item content\n   * @param item - item wrapper (<li>)\n   * @returns - item content string\n   */\n  getItemContent(t) {\n    const n = t.querySelector(`.${b.CSS.itemContent}`);\n    return !n || c.isEmpty(n) ? \"\" : n.innerHTML;\n  }\n  /**\n   * Returns item meta, for unordered list\n   * @returns Item meta object\n   */\n  getItemMeta() {\n    return {};\n  }\n  /**\n   * Returns default item meta used on creation of the new item\n   */\n  composeDefaultMeta() {\n    return {};\n  }\n}\nfunction O(e) {\n  return e.nodeType === Node.ELEMENT_NODE;\n}\nvar j = {},\n  Pe = {},\n  H = {},\n  F = {};\nObject.defineProperty(F, \"__esModule\", {\n  value: !0\n});\nF.getContenteditableSlice = Un;\nvar qn = c;\nfunction Un(e, t, n, r, i) {\n  var a;\n  i === void 0 && (i = !1);\n  var l = document.createRange();\n  if (r === \"left\" ? (l.setStart(e, 0), l.setEnd(t, n)) : (l.setStart(t, n), l.setEnd(e, e.childNodes.length)), i === !0) {\n    var s = l.extractContents();\n    return (0, qn.fragmentToString)(s);\n  }\n  var o = l.cloneContents(),\n    d = document.createElement(\"div\");\n  d.appendChild(o);\n  var u = (a = d.textContent) !== null && a !== void 0 ? a : \"\";\n  return u;\n}\nObject.defineProperty(H, \"__esModule\", {\n  value: !0\n});\nH.checkContenteditableSliceForEmptiness = Xn;\nvar zn = c,\n  Kn = F;\nfunction Xn(e, t, n, r) {\n  var i = (0, Kn.getContenteditableSlice)(e, t, n, r);\n  return (0, zn.isCollapsedWhitespaces)(i);\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.checkContenteditableSliceForEmptiness = void 0;\n  var t = H;\n  Object.defineProperty(e, \"checkContenteditableSliceForEmptiness\", {\n    enumerable: !0,\n    get: function () {\n      return t.checkContenteditableSliceForEmptiness;\n    }\n  });\n})(Pe);\nvar rt = {};\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.getContenteditableSlice = void 0;\n  var t = F;\n  Object.defineProperty(e, \"getContenteditableSlice\", {\n    enumerable: !0,\n    get: function () {\n      return t.getContenteditableSlice;\n    }\n  });\n})(rt);\nvar it = {},\n  je = {};\nObject.defineProperty(je, \"__esModule\", {\n  value: !0\n});\nje.focus = Vn;\nvar Gn = c;\nfunction Vn(e, t) {\n  var n, r;\n  if (t === void 0 && (t = !0), (0, Gn.isNativeInput)(e)) {\n    e.focus();\n    var i = t ? 0 : e.value.length;\n    e.setSelectionRange(i, i);\n  } else {\n    var a = document.createRange(),\n      l = window.getSelection();\n    if (!l) return;\n    var s = function (g, w) {\n        w === void 0 && (w = !1);\n        var _ = document.createTextNode(\"\");\n        w ? g.insertBefore(_, g.firstChild) : g.appendChild(_), a.setStart(_, 0), a.setEnd(_, 0);\n      },\n      o = function (g) {\n        return g != null;\n      },\n      d = e.childNodes,\n      u = t ? d[0] : d[d.length - 1];\n    if (o(u)) {\n      for (; o(u) && u.nodeType !== Node.TEXT_NODE;) u = t ? u.firstChild : u.lastChild;\n      if (o(u) && u.nodeType === Node.TEXT_NODE) {\n        var p = (r = (n = u.textContent) === null || n === void 0 ? void 0 : n.length) !== null && r !== void 0 ? r : 0,\n          i = t ? 0 : p;\n        a.setStart(u, i), a.setEnd(u, i);\n      } else s(e, t);\n    } else s(e);\n    l.removeAllRanges(), l.addRange(a);\n  }\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.focus = void 0;\n  var t = je;\n  Object.defineProperty(e, \"focus\", {\n    enumerable: !0,\n    get: function () {\n      return t.focus;\n    }\n  });\n})(it);\nvar Te = {},\n  R = {};\nObject.defineProperty(R, \"__esModule\", {\n  value: !0\n});\nR.getCaretNodeAndOffset = Yn;\nfunction Yn() {\n  var e = window.getSelection();\n  if (e === null) return [null, 0];\n  var t = e.focusNode,\n    n = e.focusOffset;\n  return t === null ? [null, 0] : (t.nodeType !== Node.TEXT_NODE && t.childNodes.length > 0 && (t.childNodes[n] !== void 0 ? (t = t.childNodes[n], n = 0) : (t = t.childNodes[n - 1], t.textContent !== null && (n = t.textContent.length))), [t, n]);\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.getCaretNodeAndOffset = void 0;\n  var t = R;\n  Object.defineProperty(e, \"getCaretNodeAndOffset\", {\n    enumerable: !0,\n    get: function () {\n      return t.getCaretNodeAndOffset;\n    }\n  });\n})(Te);\nvar at = {},\n  q = {};\nObject.defineProperty(q, \"__esModule\", {\n  value: !0\n});\nq.getRange = Jn;\nfunction Jn() {\n  var e = window.getSelection();\n  return e && e.rangeCount ? e.getRangeAt(0) : null;\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.getRange = void 0;\n  var t = q;\n  Object.defineProperty(e, \"getRange\", {\n    enumerable: !0,\n    get: function () {\n      return t.getRange;\n    }\n  });\n})(at);\nvar lt = {},\n  Le = {};\nObject.defineProperty(Le, \"__esModule\", {\n  value: !0\n});\nLe.isCaretAtEndOfInput = xn;\nvar De = c,\n  Qn = Te,\n  Zn = Pe;\nfunction xn(e) {\n  var t = (0, De.getDeepestNode)(e, !0);\n  if (t === null) return !0;\n  if ((0, De.isNativeInput)(t)) return t.selectionEnd === t.value.length;\n  var n = (0, Qn.getCaretNodeAndOffset)(),\n    r = n[0],\n    i = n[1];\n  return r === null ? !1 : (0, Zn.checkContenteditableSliceForEmptiness)(e, r, i, \"right\");\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.isCaretAtEndOfInput = void 0;\n  var t = Le;\n  Object.defineProperty(e, \"isCaretAtEndOfInput\", {\n    enumerable: !0,\n    get: function () {\n      return t.isCaretAtEndOfInput;\n    }\n  });\n})(lt);\nvar st = {},\n  Me = {};\nObject.defineProperty(Me, \"__esModule\", {\n  value: !0\n});\nMe.isCaretAtStartOfInput = nr;\nvar L = c,\n  er = R,\n  tr = H;\nfunction nr(e) {\n  var t = (0, L.getDeepestNode)(e);\n  if (t === null || (0, L.isEmpty)(e)) return !0;\n  if ((0, L.isNativeInput)(t)) return t.selectionEnd === 0;\n  if ((0, L.isEmpty)(e)) return !0;\n  var n = (0, er.getCaretNodeAndOffset)(),\n    r = n[0],\n    i = n[1];\n  return r === null ? !1 : (0, tr.checkContenteditableSliceForEmptiness)(e, r, i, \"left\");\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.isCaretAtStartOfInput = void 0;\n  var t = Me;\n  Object.defineProperty(e, \"isCaretAtStartOfInput\", {\n    enumerable: !0,\n    get: function () {\n      return t.isCaretAtStartOfInput;\n    }\n  });\n})(st);\nvar ot = {},\n  Ne = {};\nObject.defineProperty(Ne, \"__esModule\", {\n  value: !0\n});\nNe.save = ar;\nvar rr = c,\n  ir = q;\nfunction ar() {\n  var e = (0, ir.getRange)(),\n    t = (0, rr.make)(\"span\");\n  if (t.id = \"cursor\", t.hidden = !0, !!e) return e.insertNode(t), function () {\n    var r = window.getSelection();\n    r && (e.setStartAfter(t), e.setEndAfter(t), r.removeAllRanges(), r.addRange(e), setTimeout(function () {\n      t.remove();\n    }, 150));\n  };\n}\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.save = void 0;\n  var t = Ne;\n  Object.defineProperty(e, \"save\", {\n    enumerable: !0,\n    get: function () {\n      return t.save;\n    }\n  });\n})(ot);\n(function (e) {\n  Object.defineProperty(e, \"__esModule\", {\n    value: !0\n  }), e.save = e.isCaretAtStartOfInput = e.isCaretAtEndOfInput = e.getRange = e.getCaretNodeAndOffset = e.focus = e.getContenteditableSlice = e.checkContenteditableSliceForEmptiness = void 0;\n  var t = Pe;\n  Object.defineProperty(e, \"checkContenteditableSliceForEmptiness\", {\n    enumerable: !0,\n    get: function () {\n      return t.checkContenteditableSliceForEmptiness;\n    }\n  });\n  var n = rt;\n  Object.defineProperty(e, \"getContenteditableSlice\", {\n    enumerable: !0,\n    get: function () {\n      return n.getContenteditableSlice;\n    }\n  });\n  var r = it;\n  Object.defineProperty(e, \"focus\", {\n    enumerable: !0,\n    get: function () {\n      return r.focus;\n    }\n  });\n  var i = Te;\n  Object.defineProperty(e, \"getCaretNodeAndOffset\", {\n    enumerable: !0,\n    get: function () {\n      return i.getCaretNodeAndOffset;\n    }\n  });\n  var a = at;\n  Object.defineProperty(e, \"getRange\", {\n    enumerable: !0,\n    get: function () {\n      return a.getRange;\n    }\n  });\n  var l = lt;\n  Object.defineProperty(e, \"isCaretAtEndOfInput\", {\n    enumerable: !0,\n    get: function () {\n      return l.isCaretAtEndOfInput;\n    }\n  });\n  var s = st;\n  Object.defineProperty(e, \"isCaretAtStartOfInput\", {\n    enumerable: !0,\n    get: function () {\n      return s.isCaretAtStartOfInput;\n    }\n  });\n  var o = ot;\n  Object.defineProperty(e, \"save\", {\n    enumerable: !0,\n    get: function () {\n      return o.save;\n    }\n  });\n})(j);\nclass f {\n  /**\n   * Getter for all CSS classes used in unordered list rendering\n   */\n  static get CSS() {\n    return {\n      ...h,\n      checklist: `${m}-checklist`,\n      itemChecked: `${m}__checkbox--checked`,\n      noHover: `${m}__checkbox--no-hover`,\n      checkbox: `${m}__checkbox-check`,\n      checkboxContainer: `${m}__checkbox`\n    };\n  }\n  /**\n   * Assign passed readonly mode and config to relevant class properties\n   * @param readonly - read-only mode flag\n   * @param config - user config for Tool\n   */\n  constructor(t, n) {\n    this.config = n, this.readOnly = t;\n  }\n  /**\n   * Renders ul wrapper for list\n   * @param isRoot - boolean variable that represents level of the wrappre (root or childList)\n   * @returns - created html ul element\n   */\n  renderWrapper(t) {\n    let n;\n    return t === !0 ? (n = c.make(\"ul\", [f.CSS.wrapper, f.CSS.checklist]), n.addEventListener(\"click\", r => {\n      const i = r.target;\n      if (i) {\n        const a = i.closest(`.${f.CSS.checkboxContainer}`);\n        a && a.contains(i) && this.toggleCheckbox(a);\n      }\n    })) : n = c.make(\"ul\", [f.CSS.checklist, f.CSS.itemChildren]), n;\n  }\n  /**\n   * Redners list item element\n   * @param content - content used in list item rendering\n   * @param meta - meta of the list item used in rendering of the checklist\n   * @returns - created html list item element\n   */\n  renderItem(t, n) {\n    const r = c.make(\"li\", [f.CSS.item, f.CSS.item]),\n      i = c.make(\"div\", f.CSS.itemContent, {\n        innerHTML: t,\n        contentEditable: (!this.readOnly).toString()\n      }),\n      a = c.make(\"span\", f.CSS.checkbox),\n      l = c.make(\"div\", f.CSS.checkboxContainer);\n    return n.checked === !0 && l.classList.add(f.CSS.itemChecked), a.innerHTML = Ct, l.appendChild(a), r.appendChild(l), r.appendChild(i), r;\n  }\n  /**\n   * Return the item content\n   * @param item - item wrapper (<li>)\n   * @returns - item content string\n   */\n  getItemContent(t) {\n    const n = t.querySelector(`.${f.CSS.itemContent}`);\n    return !n || c.isEmpty(n) ? \"\" : n.innerHTML;\n  }\n  /**\n   * Return meta object of certain element\n   * @param item - will be returned meta information of this item\n   * @returns Item meta object\n   */\n  getItemMeta(t) {\n    const n = t.querySelector(`.${f.CSS.checkboxContainer}`);\n    return {\n      checked: n ? n.classList.contains(f.CSS.itemChecked) : !1\n    };\n  }\n  /**\n   * Returns default item meta used on creation of the new item\n   */\n  composeDefaultMeta() {\n    return {\n      checked: !1\n    };\n  }\n  /**\n   * Toggle checklist item state\n   * @param checkbox - checkbox element to be toggled\n   */\n  toggleCheckbox(t) {\n    t.classList.toggle(f.CSS.itemChecked), t.classList.add(f.CSS.noHover), t.addEventListener(\"mouseleave\", () => this.removeSpecialHoverBehavior(t), {\n      once: !0\n    });\n  }\n  /**\n   * Removes class responsible for special hover behavior on an item\n   * @param el - item wrapper\n   */\n  removeSpecialHoverBehavior(t) {\n    t.classList.remove(f.CSS.noHover);\n  }\n}\nfunction U(e, t = \"after\") {\n  const n = [];\n  let r;\n  function i(a) {\n    switch (t) {\n      case \"after\":\n        return a.nextElementSibling;\n      case \"before\":\n        return a.previousElementSibling;\n    }\n  }\n  for (r = i(e); r !== null;) n.push(r), r = i(r);\n  return n.length !== 0 ? n : null;\n}\nfunction y(e, t = !0) {\n  let n = e;\n  return e.classList.contains(h.item) && (n = e.querySelector(`.${h.itemChildren}`)), n === null ? [] : t ? Array.from(n.querySelectorAll(`:scope > .${h.item}`)) : Array.from(n.querySelectorAll(`.${h.item}`));\n}\nfunction lr(e) {\n  return e.nextElementSibling === null;\n}\nfunction sr(e) {\n  return e.querySelector(`.${h.itemChildren}`) !== null;\n}\nfunction C(e) {\n  return e.querySelector(`.${h.itemChildren}`);\n}\nfunction z(e) {\n  let t = e;\n  e.classList.contains(h.item) && (t = C(e)), t !== null && y(t).length === 0 && t.remove();\n}\nfunction N(e) {\n  return e.querySelector(`.${h.itemContent}`);\n}\nfunction E(e, t = !0) {\n  const n = N(e);\n  n && j.focus(n, t);\n}\nclass K {\n  /**\n   * Getter method to get current item\n   * @returns current list item or null if caret position is not undefined\n   */\n  get currentItem() {\n    const t = window.getSelection();\n    if (!t) return null;\n    let n = t.anchorNode;\n    return !n || (O(n) || (n = n.parentNode), !n) || !O(n) ? null : n.closest(`.${h.item}`);\n  }\n  /**\n   * Method that returns nesting level of the current item, null if there is no selection\n   */\n  get currentItemLevel() {\n    const t = this.currentItem;\n    if (t === null) return null;\n    let n = t.parentNode,\n      r = 0;\n    for (; n !== null && n !== this.listWrapper;) O(n) && n.classList.contains(h.item) && (r += 1), n = n.parentNode;\n    return r + 1;\n  }\n  /**\n   * Assign all passed params and renderer to relevant class properties\n   * @param params - tool constructor options\n   * @param params.data - previously saved data\n   * @param params.config - user config for Tool\n   * @param params.api - Editor.js API\n   * @param params.readOnly - read-only mode flag\n   * @param renderer - renderer instance initialized in tool class\n   */\n  constructor({\n    data: t,\n    config: n,\n    api: r,\n    readOnly: i,\n    block: a\n  }, l) {\n    this.config = n, this.data = t, this.readOnly = i, this.api = r, this.block = a, this.renderer = l;\n  }\n  /**\n   * Function that is responsible for rendering list with contents\n   * @returns Filled with content wrapper element of the list\n   */\n  render() {\n    return this.listWrapper = this.renderer.renderWrapper(!0), this.data.items.length ? this.appendItems(this.data.items, this.listWrapper) : this.appendItems([{\n      content: \"\",\n      meta: {},\n      items: []\n    }], this.listWrapper), this.readOnly || this.listWrapper.addEventListener(\"keydown\", t => {\n      switch (t.key) {\n        case \"Enter\":\n          this.enterPressed(t);\n          break;\n        case \"Backspace\":\n          this.backspace(t);\n          break;\n        case \"Tab\":\n          t.shiftKey ? this.shiftTab(t) : this.addTab(t);\n          break;\n      }\n    }, !1), \"start\" in this.data.meta && this.data.meta.start !== void 0 && this.changeStartWith(this.data.meta.start), \"counterType\" in this.data.meta && this.data.meta.counterType !== void 0 && this.changeCounters(this.data.meta.counterType), this.listWrapper;\n  }\n  /**\n   * Function that is responsible for list content saving\n   * @param wrapper - optional argument wrapper\n   * @returns whole list saved data if wrapper not passes, otherwise will return data of the passed wrapper\n   */\n  save(t) {\n    const n = t ?? this.listWrapper,\n      r = l => y(l).map(o => {\n        const d = C(o),\n          u = this.renderer.getItemContent(o),\n          p = this.renderer.getItemMeta(o),\n          g = d ? r(d) : [];\n        return {\n          content: u,\n          meta: p,\n          items: g\n        };\n      }),\n      i = n ? r(n) : [];\n    let a = {\n      style: this.data.style,\n      meta: {},\n      items: i\n    };\n    return this.data.style === \"ordered\" && (a.meta = {\n      start: this.data.meta.start,\n      counterType: this.data.meta.counterType\n    }), a;\n  }\n  /**\n   * On paste sanitzation config. Allow only tags that are allowed in the Tool.\n   * @returns - config that determines tags supposted by paste handler\n   * @todo - refactor and move to list instance\n   */\n  static get pasteConfig() {\n    return {\n      tags: [\"OL\", \"UL\", \"LI\"]\n    };\n  }\n  /**\n   * Method that specified hot to merge two List blocks.\n   * Called by Editor.js by backspace at the beginning of the Block\n   *\n   * Content of the first item of the next List would be merged with deepest item in current list\n   * Other items of the next List would be appended to the current list without any changes in nesting levels\n   * @param data - data of the second list to be merged with current\n   */\n  merge(t) {\n    const n = this.block.holder.querySelectorAll(`.${h.item}`),\n      r = n[n.length - 1],\n      i = N(r);\n    if (r === null || i === null || (i.insertAdjacentHTML(\"beforeend\", t.items[0].content), this.listWrapper === void 0)) return;\n    const a = y(this.listWrapper);\n    if (a.length === 0) return;\n    const l = a[a.length - 1];\n    let s = C(l);\n    const o = t.items.shift();\n    o !== void 0 && (o.items.length !== 0 && (s === null && (s = this.renderer.renderWrapper(!1)), this.appendItems(o.items, s)), t.items.length > 0 && this.appendItems(t.items, this.listWrapper));\n  }\n  /**\n   * On paste callback that is fired from Editor.\n   * @param event - event with pasted data\n   * @todo - refactor and move to list instance\n   */\n  onPaste(t) {\n    const n = t.detail.data;\n    this.data = this.pasteHandler(n);\n    const r = this.listWrapper;\n    r && r.parentNode && r.parentNode.replaceChild(this.render(), r);\n  }\n  /**\n   * Handle UL, OL and LI tags paste and returns List data\n   * @param element - html element that contains whole list\n   * @todo - refactor and move to list instance\n   */\n  pasteHandler(t) {\n    const {\n      tagName: n\n    } = t;\n    let r = \"unordered\",\n      i;\n    switch (n) {\n      case \"OL\":\n        r = \"ordered\", i = \"ol\";\n        break;\n      case \"UL\":\n      case \"LI\":\n        r = \"unordered\", i = \"ul\";\n    }\n    const a = {\n      style: r,\n      meta: {},\n      items: []\n    };\n    r === \"ordered\" && (this.data.meta.counterType = \"numeric\", this.data.meta.start = 1);\n    const l = s => Array.from(s.querySelectorAll(\":scope > li\")).map(d => {\n      const u = d.querySelector(`:scope > ${i}`),\n        p = u ? l(u) : [];\n      return {\n        content: d.innerHTML ?? \"\",\n        meta: {},\n        items: p\n      };\n    });\n    return a.items = l(t), a;\n  }\n  /**\n   * Changes ordered list start property value\n   * @param index - new value of the start property\n   */\n  changeStartWith(t) {\n    this.listWrapper.style.setProperty(\"counter-reset\", `item ${t - 1}`), this.data.meta.start = t;\n  }\n  /**\n   * Changes ordered list counterType property value\n   * @param counterType - new value of the counterType value\n   */\n  changeCounters(t) {\n    this.listWrapper.style.setProperty(\"--list-counter-type\", t), this.data.meta.counterType = t;\n  }\n  /**\n   * Handles Enter keypress\n   * @param event - keydown\n   */\n  enterPressed(t) {\n    var s;\n    const n = this.currentItem;\n    if (t.stopPropagation(), t.preventDefault(), t.isComposing || n === null) return;\n    const r = ((s = this.renderer) == null ? void 0 : s.getItemContent(n).trim().length) === 0,\n      i = n.parentNode === this.listWrapper,\n      a = n.previousElementSibling === null,\n      l = this.api.blocks.getCurrentBlockIndex();\n    if (i && r) {\n      if (lr(n) && !sr(n)) {\n        a ? this.convertItemToDefaultBlock(l, !0) : this.convertItemToDefaultBlock();\n        return;\n      } else {\n        this.splitList(n);\n        return;\n      }\n    } else if (r) {\n      this.unshiftItem(n);\n      return;\n    } else this.splitItem(n);\n  }\n  /**\n   * Handle backspace\n   * @param event - keydown\n   */\n  backspace(t) {\n    var r;\n    const n = this.currentItem;\n    if (n !== null && j.isCaretAtStartOfInput(n) && ((r = window.getSelection()) == null ? void 0 : r.isCollapsed) !== !1) {\n      if (t.stopPropagation(), n.parentNode === this.listWrapper && n.previousElementSibling === null) {\n        this.convertFirstItemToDefaultBlock();\n        return;\n      }\n      t.preventDefault(), this.mergeItemWithPrevious(n);\n    }\n  }\n  /**\n   * Reduce indentation for current item\n   * @param event - keydown\n   */\n  shiftTab(t) {\n    t.stopPropagation(), t.preventDefault(), this.currentItem !== null && this.unshiftItem(this.currentItem);\n  }\n  /**\n   * Decrease indentation of the passed item\n   * @param item - list item to be unshifted\n   */\n  unshiftItem(t) {\n    if (!t.parentNode || !O(t.parentNode)) return;\n    const n = t.parentNode.closest(`.${h.item}`);\n    if (!n) return;\n    let r = C(t);\n    if (t.parentElement === null) return;\n    const i = U(t);\n    i !== null && (r === null && (r = this.renderer.renderWrapper(!1)), i.forEach(a => {\n      r.appendChild(a);\n    }), t.appendChild(r)), n.after(t), E(t, !1), z(n);\n  }\n  /**\n   * Method that is used for list splitting and moving trailing items to the new separated list\n   * @param item - current item html element\n   */\n  splitList(t) {\n    const n = y(t),\n      r = this.block,\n      i = this.api.blocks.getCurrentBlockIndex();\n    if (n.length !== 0) {\n      const o = n[0];\n      this.unshiftItem(o), E(t, !1);\n    }\n    if (t.previousElementSibling === null && t.parentNode === this.listWrapper) {\n      this.convertItemToDefaultBlock(i);\n      return;\n    }\n    const a = U(t);\n    if (a === null) return;\n    const l = this.renderer.renderWrapper(!0);\n    a.forEach(o => {\n      l.appendChild(o);\n    });\n    const s = this.save(l);\n    s.meta.start = this.data.style == \"ordered\" ? 1 : void 0, this.api.blocks.insert(r == null ? void 0 : r.name, s, this.config, i + 1), this.convertItemToDefaultBlock(i + 1), l.remove();\n  }\n  /**\n   * Method that is used for splitting item content and moving trailing content to the new sibling item\n   * @param currentItem - current item html element\n   */\n  splitItem(t) {\n    const [n, r] = j.getCaretNodeAndOffset();\n    if (n === null) return;\n    const i = N(t);\n    let a;\n    i === null ? a = \"\" : a = j.getContenteditableSlice(i, n, r, \"right\", !0);\n    const l = C(t),\n      s = this.renderItem(a);\n    t == null || t.after(s), l && s.appendChild(l), E(s);\n  }\n  /**\n   * Method that is used for merging current item with previous one\n   * Content of the current item would be appended to the previous item\n   * Current item children would not change nesting level\n   * @param item - current item html element\n   */\n  mergeItemWithPrevious(t) {\n    const n = t.previousElementSibling,\n      r = t.parentNode;\n    if (r === null || !O(r)) return;\n    const i = r.closest(`.${h.item}`);\n    if (!n && !i || n && !O(n)) return;\n    let a;\n    if (n) {\n      const p = y(n, !1);\n      p.length !== 0 && p.length !== 0 ? a = p[p.length - 1] : a = n;\n    } else a = i;\n    const l = this.renderer.getItemContent(t);\n    if (!a) return;\n    E(a, !1);\n    const s = N(a);\n    if (s === null) return;\n    s.insertAdjacentHTML(\"beforeend\", l);\n    const o = y(t);\n    if (o.length === 0) {\n      t.remove(), z(a);\n      return;\n    }\n    const d = n || i,\n      u = C(d) ?? this.renderer.renderWrapper(!1);\n    n ? o.forEach(p => {\n      u.appendChild(p);\n    }) : o.forEach(p => {\n      u.prepend(p);\n    }), C(d) === null && a.appendChild(u), t.remove();\n  }\n  /**\n   * Add indentation to current item\n   * @param event - keydown\n   */\n  addTab(t) {\n    var a;\n    t.stopPropagation(), t.preventDefault();\n    const n = this.currentItem;\n    if (!n) return;\n    if (((a = this.config) == null ? void 0 : a.maxLevel) !== void 0) {\n      const l = this.currentItemLevel;\n      if (l !== null && l === this.config.maxLevel) return;\n    }\n    const r = n.previousSibling;\n    if (r === null || !O(r)) return;\n    const i = C(r);\n    if (i) i.appendChild(n), y(n).forEach(s => {\n      i.appendChild(s);\n    });else {\n      const l = this.renderer.renderWrapper(!1);\n      l.appendChild(n), y(n).forEach(o => {\n        l.appendChild(o);\n      }), r.appendChild(l);\n    }\n    z(n), E(n, !1);\n  }\n  /**\n   * Convert current item to default block with passed index\n   * @param newBloxkIndex - optional parameter represents index, where would be inseted default block\n   * @param removeList - optional parameter, that represents condition, if List should be removed\n   */\n  convertItemToDefaultBlock(t, n) {\n    let r;\n    const i = this.currentItem,\n      a = i !== null ? this.renderer.getItemContent(i) : \"\";\n    n === !0 && this.api.blocks.delete(), t !== void 0 ? r = this.api.blocks.insert(void 0, {\n      text: a\n    }, void 0, t) : r = this.api.blocks.insert(), i == null || i.remove(), this.api.caret.setToBlock(r, \"start\");\n  }\n  /**\n   * Convert first item of the list to default block\n   * This method could be called when backspace button pressed at start of the first item of the list\n   * First item of the list would be converted to the paragraph and first item children would be unshifted\n   */\n  convertFirstItemToDefaultBlock() {\n    const t = this.currentItem;\n    if (t === null) return;\n    const n = y(t);\n    if (n.length !== 0) {\n      const l = n[0];\n      this.unshiftItem(l), E(t);\n    }\n    const r = U(t),\n      i = this.api.blocks.getCurrentBlockIndex(),\n      a = r === null;\n    this.convertItemToDefaultBlock(i, a);\n  }\n  /**\n   * Method that calls render function of the renderer with a necessary item meta cast\n   * @param itemContent - content to be rendered in new item\n   * @param meta - meta used in list item rendering\n   * @returns html element of the rendered item\n   */\n  renderItem(t, n) {\n    const r = n ?? this.renderer.composeDefaultMeta();\n    switch (!0) {\n      case this.renderer instanceof v:\n        return this.renderer.renderItem(t, r);\n      case this.renderer instanceof b:\n        return this.renderer.renderItem(t, r);\n      default:\n        return this.renderer.renderItem(t, r);\n    }\n  }\n  /**\n   * Renders children list\n   * @param items - list data used in item rendering\n   * @param parentElement - where to append passed items\n   */\n  appendItems(t, n) {\n    t.forEach(r => {\n      var a;\n      const i = this.renderItem(r.content, r.meta);\n      if (n.appendChild(i), r.items.length) {\n        const l = (a = this.renderer) == null ? void 0 : a.renderWrapper(!1);\n        this.appendItems(r.items, l), i.appendChild(l);\n      }\n    });\n  }\n}\nconst I = {\n  wrapper: `${m}-start-with-field`,\n  input: `${m}-start-with-field__input`,\n  startWithElementWrapperInvalid: `${m}-start-with-field--invalid`\n};\nfunction or(e, {\n  value: t,\n  placeholder: n,\n  attributes: r,\n  sanitize: i\n}) {\n  const a = c.make(\"div\", I.wrapper),\n    l = c.make(\"input\", I.input, {\n      placeholder: n,\n      /**\n       * Used to prevent focusing on the input by Tab key\n       * (Popover in the Toolbar lays below the blocks,\n       * so Tab in the last block will focus this hidden input if this property is not set)\n       */\n      tabIndex: -1,\n      /**\n       * Value of the start property, if it is not specified, then it is set to one\n       */\n      value: t\n    });\n  for (const s in r) l.setAttribute(s, r[s]);\n  return a.appendChild(l), l.addEventListener(\"input\", () => {\n    i !== void 0 && (l.value = i(l.value));\n    const s = l.checkValidity();\n    !s && !a.classList.contains(I.startWithElementWrapperInvalid) && a.classList.add(I.startWithElementWrapperInvalid), s && a.classList.contains(I.startWithElementWrapperInvalid) && a.classList.remove(I.startWithElementWrapperInvalid), s && e(l.value);\n  }), a;\n}\nconst P = /* @__PURE__ */new Map([\n  /**\n   * Value that represents default arabic numbers for counters\n   */\n  [\"Numeric\", \"numeric\"],\n  /**\n   * Value that represents lower roman numbers for counteres\n   */\n  [\"Lower Roman\", \"lower-roman\"],\n  /**\n   * Value that represents upper roman numbers for counters\n   */\n  [\"Upper Roman\", \"upper-roman\"],\n  /**\n   * Value that represents lower alpha characters for counters\n   */\n  [\"Lower Alpha\", \"lower-alpha\"],\n  /**\n   * Value that represents upper alpha characters for counters\n   */\n  [\"Upper Alpha\", \"upper-alpha\"]]),\n  He = /* @__PURE__ */new Map([\n  /**\n   * Value that represents Icon for Numeric counter type\n   */\n  [\"numeric\", St],\n  /**\n   * Value that represents Icon for Lower Roman counter type\n   */\n  [\"lower-roman\", Ot],\n  /**\n   * Value that represents Icon for Upper Roman counter type\n   */\n  [\"upper-roman\", kt],\n  /**\n   * Value that represents Icon for Lower Alpha counter type\n   */\n  [\"lower-alpha\", Et],\n  /**\n   * Value that represents Icon for Upper Alpha counter type\n   */\n  [\"upper-alpha\", _t]]);\nfunction ur(e) {\n  return e.replace(/\\D+/g, \"\");\n}\nfunction cr(e) {\n  return typeof e.items[0] == \"string\";\n}\nfunction dr(e) {\n  return !(\"meta\" in e);\n}\nfunction fr(e) {\n  return typeof e.items[0] != \"string\" && \"text\" in e.items[0] && \"checked\" in e.items[0] && typeof e.items[0].text == \"string\" && typeof e.items[0].checked == \"boolean\";\n}\nfunction pr(e) {\n  const t = [];\n  return cr(e) ? (e.items.forEach(n => {\n    t.push({\n      content: n,\n      meta: {},\n      items: []\n    });\n  }), {\n    style: e.style,\n    meta: {},\n    items: t\n  }) : fr(e) ? (e.items.forEach(n => {\n    t.push({\n      content: n.text,\n      meta: {\n        checked: n.checked\n      },\n      items: []\n    });\n  }), {\n    style: \"checklist\",\n    meta: {},\n    items: t\n  }) : dr(e) ? {\n    style: e.style,\n    meta: {},\n    items: e.items\n  } : e;\n}\nclass G {\n  /**\n   * Notify core that read-only mode is supported\n   */\n  static get isReadOnlySupported() {\n    return !0;\n  }\n  /**\n   * Allow to use native Enter behaviour\n   */\n  static get enableLineBreaks() {\n    return !0;\n  }\n  /**\n   * Get Tool toolbox settings\n   * icon - Tool icon's SVG\n   * title - title to show in toolbox\n   */\n  static get toolbox() {\n    return [{\n      icon: $e,\n      title: \"Unordered List\",\n      data: {\n        style: \"unordered\"\n      }\n    }, {\n      icon: Be,\n      title: \"Ordered List\",\n      data: {\n        style: \"ordered\"\n      }\n    }, {\n      icon: Ae,\n      title: \"Checklist\",\n      data: {\n        style: \"checklist\"\n      }\n    }];\n  }\n  /**\n   * On paste sanitzation config. Allow only tags that are allowed in the Tool.\n   * @returns - paste config object used in editor\n   */\n  static get pasteConfig() {\n    return {\n      tags: [\"OL\", \"UL\", \"LI\"]\n    };\n  }\n  /**\n   * Convert from text to list with import and export list to text\n   */\n  static get conversionConfig() {\n    return {\n      export: t => G.joinRecursive(t),\n      import: (t, n) => ({\n        meta: {},\n        items: [{\n          content: t,\n          meta: {},\n          items: []\n        }],\n        style: (n == null ? void 0 : n.defaultStyle) !== void 0 ? n.defaultStyle : \"unordered\"\n      })\n    };\n  }\n  /**\n   * Get list style name\n   */\n  get listStyle() {\n    return this.data.style || this.defaultListStyle;\n  }\n  /**\n   * Set list style\n   * @param style - new style to set\n   */\n  set listStyle(t) {\n    var r;\n    this.data.style = t, this.changeTabulatorByStyle();\n    const n = this.list.render();\n    (r = this.listElement) == null || r.replaceWith(n), this.listElement = n;\n  }\n  /**\n   * Render plugin`s main Element and fill it with saved data\n   * @param params - tool constructor options\n   * @param params.data - previously saved data\n   * @param params.config - user config for Tool\n   * @param params.api - Editor.js API\n   * @param params.readOnly - read-only mode flag\n   */\n  constructor({\n    data: t,\n    config: n,\n    api: r,\n    readOnly: i,\n    block: a\n  }) {\n    var s;\n    this.api = r, this.readOnly = i, this.config = n, this.block = a, this.defaultListStyle = ((s = this.config) == null ? void 0 : s.defaultStyle) || \"unordered\", this.defaultCounterTypes = this.config.counterTypes || Array.from(P.values());\n    const l = {\n      style: this.defaultListStyle,\n      meta: {},\n      items: []\n    };\n    this.data = Object.keys(t).length ? pr(t) : l, this.listStyle === \"ordered\" && this.data.meta.counterType === void 0 && (this.data.meta.counterType = \"numeric\"), this.changeTabulatorByStyle();\n  }\n  /**\n   * Convert from list to text for conversionConfig\n   * @param data - current data of the list\n   * @returns - string of the recursively merged contents of the items of the list\n   */\n  static joinRecursive(t) {\n    return t.items.map(n => `${n.content} ${G.joinRecursive(n)}`).join(\"\");\n  }\n  /**\n   * Function that is responsible for content rendering\n   * @returns rendered list wrapper with all contents\n   */\n  render() {\n    return this.listElement = this.list.render(), this.listElement;\n  }\n  /**\n   * Function that is responsible for content saving\n   * @returns formatted content used in editor\n   */\n  save() {\n    return this.data = this.list.save(), this.data;\n  }\n  /**\n   * Function that is responsible for mergind two lists into one\n   * @param data - data of the next standing list, that should be merged with current\n   */\n  merge(t) {\n    this.list.merge(t);\n  }\n  /**\n   * Creates Block Tune allowing to change the list style\n   * @returns array of tune configs\n   */\n  renderSettings() {\n    const t = [{\n      label: this.api.i18n.t(\"Unordered\"),\n      icon: $e,\n      closeOnActivate: !0,\n      isActive: this.listStyle == \"unordered\",\n      onActivate: () => {\n        this.listStyle = \"unordered\";\n      }\n    }, {\n      label: this.api.i18n.t(\"Ordered\"),\n      icon: Be,\n      closeOnActivate: !0,\n      isActive: this.listStyle == \"ordered\",\n      onActivate: () => {\n        this.listStyle = \"ordered\";\n      }\n    }, {\n      label: this.api.i18n.t(\"Checklist\"),\n      icon: Ae,\n      closeOnActivate: !0,\n      isActive: this.listStyle == \"checklist\",\n      onActivate: () => {\n        this.listStyle = \"checklist\";\n      }\n    }];\n    if (this.listStyle === \"ordered\") {\n      const n = or(a => this.changeStartWith(Number(a)), {\n          value: String(this.data.meta.start ?? 1),\n          placeholder: \"\",\n          attributes: {\n            required: \"true\"\n          },\n          sanitize: a => ur(a)\n        }),\n        r = [{\n          label: this.api.i18n.t(\"Start with\"),\n          icon: It,\n          children: {\n            items: [{\n              element: n,\n              // @ts-expect-error ts(2820) can not use PopoverItem enum from editor.js types\n              type: \"html\"\n            }]\n          }\n        }],\n        i = {\n          label: this.api.i18n.t(\"Counter type\"),\n          icon: He.get(this.data.meta.counterType),\n          children: {\n            items: []\n          }\n        };\n      P.forEach((a, l) => {\n        const s = P.get(l);\n        this.defaultCounterTypes.includes(s) && i.children.items.push({\n          title: this.api.i18n.t(l),\n          icon: He.get(s),\n          isActive: this.data.meta.counterType === P.get(l),\n          closeOnActivate: !0,\n          onActivate: () => {\n            this.changeCounters(P.get(l));\n          }\n        });\n      }), i.children.items.length > 1 && r.push(i), t.push({\n        type: \"separator\"\n      }, ...r);\n    }\n    return t;\n  }\n  /**\n   * On paste callback that is fired from Editor.\n   * @param event - event with pasted data\n   */\n  onPaste(t) {\n    const {\n      tagName: n\n    } = t.detail.data;\n    switch (n) {\n      case \"OL\":\n        this.listStyle = \"ordered\";\n        break;\n      case \"UL\":\n      case \"LI\":\n        this.listStyle = \"unordered\";\n    }\n    this.list.onPaste(t);\n  }\n  /**\n   * Handle UL, OL and LI tags paste and returns List data\n   * @param element - html element that contains whole list\n   */\n  pasteHandler(t) {\n    return this.list.pasteHandler(t);\n  }\n  /**\n   * Changes ordered list counterType property value\n   * @param counterType - new value of the counterType value\n   */\n  changeCounters(t) {\n    var n;\n    (n = this.list) == null || n.changeCounters(t), this.data.meta.counterType = t;\n  }\n  /**\n   * Changes ordered list start property value\n   * @param index - new value of the start property\n   */\n  changeStartWith(t) {\n    var n;\n    (n = this.list) == null || n.changeStartWith(t), this.data.meta.start = t;\n  }\n  /**\n   * This method allows changing tabulator respectfully to passed style\n   */\n  changeTabulatorByStyle() {\n    switch (this.listStyle) {\n      case \"ordered\":\n        this.list = new K({\n          data: this.data,\n          readOnly: this.readOnly,\n          api: this.api,\n          config: this.config,\n          block: this.block\n        }, new v(this.readOnly, this.config));\n        break;\n      case \"unordered\":\n        this.list = new K({\n          data: this.data,\n          readOnly: this.readOnly,\n          api: this.api,\n          config: this.config,\n          block: this.block\n        }, new b(this.readOnly, this.config));\n        break;\n      case \"checklist\":\n        this.list = new K({\n          data: this.data,\n          readOnly: this.readOnly,\n          api: this.api,\n          config: this.config,\n          block: this.block\n        }, new f(this.readOnly, this.config));\n        break;\n    }\n  }\n}\nexport { G as default };", "map": {"version": 3, "names": ["document", "e", "createElement", "append<PERSON><PERSON><PERSON>", "createTextNode", "head", "c", "console", "error", "Ct", "Ae", "$e", "Be", "St", "<PERSON>t", "kt", "_t", "Et", "It", "A", "globalThis", "window", "global", "self", "wt", "__esModule", "t", "default", "n", "r", "Reflect", "construct", "arguments", "constructor", "apply", "prototype", "Object", "defineProperty", "value", "keys", "for<PERSON>ach", "i", "getOwnPropertyDescriptor", "get", "enumerable", "V", "Y", "allInputsSelector", "Pt", "map", "concat", "join", "k", "J", "isNativeInput", "jt", "tagName", "includes", "Fe", "Q", "append", "Tt", "Array", "isArray", "Z", "x", "blockElements", "Lt", "Re", "ee", "calculateBaseline", "Mt", "getComputedStyle", "parseFloat", "fontSize", "lineHeight", "paddingTop", "a", "borderTopWidth", "l", "marginTop", "s", "o", "d", "qe", "te", "ne", "re", "isContentEditable", "Nt", "contentEditable", "canSetCaret", "Bt", "At", "$t", "type", "$", "ie", "Wt", "set", "Ue", "win", "mac", "x11", "linux", "find", "navigator", "appVersion", "toLowerCase", "indexOf", "ae", "length", "Dt", "Ht", "platform", "test", "maxTouchPoints", "Ft", "replace", "Rt", "toUpperCase", "slice", "qt", "style", "position", "left", "bottom", "innerHTML", "body", "getSelection", "createRange", "selectNode", "Error", "removeAllRanges", "addRange", "execCommand", "<PERSON><PERSON><PERSON><PERSON>", "Ut", "clearTimeout", "setTimeout", "S", "toString", "call", "match", "zt", "ze", "Kt", "Xt", "M", "Gt", "Promise", "resolve", "Vt", "Yt", "X", "shift", "assign", "Jt", "warn", "Qt", "URL", "href", "substring", "location", "protocol", "origin", "Zt", "xt", "BACKSPACE", "TAB", "ENTER", "SHIFT", "CTRL", "ALT", "ESC", "SPACE", "LEFT", "UP", "DOWN", "RIGHT", "DELETE", "META", "SLASH", "en", "WHEEL", "BACKWARD", "FORWARD", "tn", "completed", "add", "then", "catch", "nn", "leading", "Date", "now", "u", "trailing", "rn", "freeze", "__proto__", "PromiseQueue", "beautifyShortcut", "cacheable", "capitalize", "copyTextToClipboard", "debounce", "deepMerge", "deprecationAssert", "getUserOS", "getValidUrl", "isBoolean", "isClass", "isEmpty", "isFunction", "isIosDevice", "isNumber", "isObject", "isPrintableKey", "isPromise", "isString", "isUndefined", "keyCodes", "mouseButtons", "notEmpty", "throttle", "typeOf", "Symbol", "toStringTag", "le", "containsOnlyInlineElements", "sn", "an", "ln", "from", "children", "every", "<PERSON>", "se", "B", "oe", "make", "on", "filter", "classList", "hasOwnProperty", "fragmentToString", "cn", "un", "Xe", "ue", "getContentLength", "fn", "dn", "nodeType", "Node", "TEXT_NODE", "textContent", "ce", "de", "We", "__spread<PERSON><PERSON>y", "getDeepestBlockElements", "Ge", "pn", "reduce", "Ve", "fe", "W", "pe", "isLineBreakTag", "hn", "D", "he", "isSingleTag", "mn", "getDeepestNode", "Ye", "gn", "vn", "bn", "ELEMENT_NODE", "parentNode", "Je", "me", "T", "findAllInputs", "kn", "yn", "Cn", "Sn", "On", "querySelectorAll", "Qe", "ge", "isCollapsedWhitespaces", "_n", "ve", "be", "isElement", "In", "En", "Ze", "ye", "Ce", "Se", "<PERSON><PERSON><PERSON><PERSON>", "wn", "childNodes", "Oe", "ke", "isNodeEmpty", "Mn", "Pn", "jn", "Tn", "Ln", "RegExp", "trim", "$n", "Nn", "An", "normalize", "push", "xe", "_e", "isFragment", "Wn", "Bn", "DOCUMENT_FRAGMENT_NODE", "et", "Ee", "isHTMLString", "Hn", "Dn", "childElementCount", "tt", "Ie", "offset", "Fn", "getBoundingClientRect", "pageXOffset", "documentElement", "scrollLeft", "pageYOffset", "scrollTop", "top", "height", "right", "width", "nt", "we", "prepend", "Rn", "reverse", "p", "g", "w", "_", "ut", "ct", "dt", "ft", "pt", "ht", "mt", "gt", "vt", "bt", "yt", "m", "h", "wrapper", "item", "itemContent", "itemChildren", "v", "CSS", "orderedList", "config", "readOnly", "renderWrapper", "renderItem", "getItemContent", "querySelector", "getItemMeta", "composeDefaultMeta", "b", "unorderedList", "O", "j", "Pe", "H", "F", "getContenteditableSlice", "Un", "qn", "setStart", "setEnd", "extractContents", "cloneContents", "checkContenteditableSliceForEmptiness", "Xn", "zn", "Kn", "rt", "it", "je", "focus", "Vn", "Gn", "setSelectionRange", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Te", "R", "getCaretNodeAndOffset", "Yn", "focusNode", "focusOffset", "at", "q", "getRange", "Jn", "rangeCount", "getRangeAt", "lt", "Le", "isCaretAtEndOfInput", "xn", "De", "Qn", "Zn", "selectionEnd", "st", "Me", "isCaretAtStartOfInput", "nr", "L", "er", "tr", "ot", "Ne", "save", "ar", "rr", "ir", "id", "hidden", "insertNode", "setStartAfter", "setEndAfter", "remove", "f", "checklist", "itemChecked", "noHover", "checkbox", "checkboxContainer", "addEventListener", "target", "closest", "contains", "toggleCheckbox", "checked", "toggle", "removeSpecialHoverBehavior", "once", "U", "nextElement<PERSON><PERSON>ling", "previousElementSibling", "y", "lr", "sr", "C", "z", "N", "E", "K", "currentItem", "anchorNode", "currentItemLevel", "listWrapper", "data", "api", "block", "renderer", "render", "items", "appendItems", "content", "meta", "key", "enterPressed", "backspace", "shift<PERSON>ey", "shiftTab", "addTab", "start", "changeStartWith", "counterType", "changeCounters", "pasteConfig", "tags", "merge", "holder", "insertAdjacentHTML", "onPaste", "detail", "pasteHandler", "<PERSON><PERSON><PERSON><PERSON>", "setProperty", "stopPropagation", "preventDefault", "isComposing", "blocks", "getCurrentBlockIndex", "convertItemToDefaultBlock", "splitList", "unshiftItem", "splitItem", "isCollapsed", "convertFirstItemToDefaultBlock", "mergeItemWithPrevious", "parentElement", "after", "insert", "name", "maxLevel", "previousSibling", "delete", "text", "caret", "setT<PERSON><PERSON><PERSON>", "I", "input", "startWithElementWrapperInvalid", "or", "placeholder", "attributes", "sanitize", "tabIndex", "setAttribute", "checkValidity", "P", "Map", "He", "ur", "cr", "dr", "fr", "pr", "G", "isReadOnlySupported", "enableLineBreaks", "toolbox", "icon", "title", "conversionConfig", "export", "joinRecursive", "import", "defaultStyle", "listStyle", "defaultListStyle", "changeTabulatorByStyle", "list", "listElement", "replaceWith", "defaultCounterTypes", "counterTypes", "values", "renderSettings", "label", "i18n", "closeOnActivate", "isActive", "onActivate", "Number", "String", "required", "element"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/@editorjs/list/dist/editorjs-list.mjs"], "sourcesContent": ["(function(){\"use strict\";try{if(typeof document<\"u\"){var e=document.createElement(\"style\");e.appendChild(document.createTextNode('.cdx-list{margin:0;padding:0;outline:none;display:grid;counter-reset:item;gap:var(--spacing-s);padding:var(--spacing-xs);--spacing-s: 8px;--spacing-xs: 6px;--list-counter-type: numeric;--radius-border: 5px;--checkbox-background: #fff;--color-border: #C9C9C9;--color-bg-checked: #369FFF;--line-height: 1.45em;--color-bg-checked-hover: #0059AB;--color-tick: #fff;--size-checkbox: 1.2em}.cdx-list__item{line-height:var(--line-height);display:grid;grid-template-columns:auto 1fr;grid-template-rows:auto auto;grid-template-areas:\"checkbox content\" \". child\"}.cdx-list__item-children{display:grid;grid-area:child;gap:var(--spacing-s);padding-top:var(--spacing-s)}.cdx-list__item [contenteditable]{outline:none}.cdx-list__item-content{word-break:break-word;white-space:pre-wrap;grid-area:content;padding-left:var(--spacing-s)}.cdx-list__item:before{counter-increment:item;white-space:nowrap}.cdx-list-ordered .cdx-list__item:before{content:counters(item,\".\",var(--list-counter-type)) \".\"}.cdx-list-ordered{counter-reset:item}.cdx-list-unordered .cdx-list__item:before{content:\"•\"}.cdx-list-checklist .cdx-list__item:before{content:\"\"}.cdx-list__settings .cdx-settings-button{width:50%}.cdx-list__checkbox{padding-top:calc((var(--line-height) - var(--size-checkbox)) / 2);grid-area:checkbox;width:var(--size-checkbox);height:var(--size-checkbox);display:flex;cursor:pointer}.cdx-list__checkbox svg{opacity:0;height:var(--size-checkbox);width:var(--size-checkbox);left:-1px;top:-1px;position:absolute}@media (hover: hover){.cdx-list__checkbox:not(.cdx-list__checkbox--no-hover):hover .cdx-list__checkbox-check svg{opacity:1}}.cdx-list__checkbox--checked{line-height:var(--line-height)}@media (hover: hover){.cdx-list__checkbox--checked:not(.cdx-list__checkbox--checked--no-hover):hover .cdx-checklist__checkbox-check{background:var(--color-bg-checked-hover);border-color:var(--color-bg-checked-hover)}}.cdx-list__checkbox--checked .cdx-list__checkbox-check{background:var(--color-bg-checked);border-color:var(--color-bg-checked)}.cdx-list__checkbox--checked .cdx-list__checkbox-check svg{opacity:1}.cdx-list__checkbox--checked .cdx-list__checkbox-check svg path{stroke:var(--color-tick)}.cdx-list__checkbox--checked .cdx-list__checkbox-check:before{opacity:0;visibility:visible;transform:scale(2.5)}.cdx-list__checkbox-check{cursor:pointer;display:inline-block;position:relative;margin:0 auto;width:var(--size-checkbox);height:var(--size-checkbox);box-sizing:border-box;border-radius:var(--radius-border);border:1px solid var(--color-border);background:var(--checkbox-background)}.cdx-list__checkbox-check:before{content:\"\";position:absolute;top:0;right:0;bottom:0;left:0;border-radius:100%;background-color:var(--color-bg-checked);visibility:hidden;pointer-events:none;transform:scale(1);transition:transform .4s ease-out,opacity .4s}.cdx-list-start-with-field{background:#F8F8F8;border:1px solid rgba(226,226,229,.2);border-radius:6px;padding:2px;display:grid;grid-template-columns:auto auto 1fr;grid-template-rows:auto}.cdx-list-start-with-field--invalid{background:#FFECED;border:1px solid #E13F3F}.cdx-list-start-with-field--invalid .cdx-list-start-with-field__input{color:#e13f3f}.cdx-list-start-with-field__input{font-size:14px;outline:none;font-weight:500;font-family:inherit;border:0;background:transparent;margin:0;padding:0;line-height:22px;min-width:calc(100% - var(--toolbox-buttons-size) - var(--icon-margin-right))}.cdx-list-start-with-field__input::placeholder{color:var(--grayText);font-weight:500}')),document.head.appendChild(e)}}catch(c){console.error(\"vite-plugin-css-injected-by-js\",c)}})();\nconst Ct = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M7 12L10.4884 15.8372C10.5677 15.9245 10.705 15.9245 10.7844 15.8372L17 9\"/></svg>', Ae = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M9.2 12L11.0586 13.8586C11.1367 13.9367 11.2633 13.9367 11.3414 13.8586L14.7 10.5\"/><rect width=\"14\" height=\"14\" x=\"5\" y=\"5\" stroke=\"currentColor\" stroke-width=\"2\" rx=\"4\"/></svg>', $e = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><line x1=\"9\" x2=\"19\" y1=\"7\" y2=\"7\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"/><line x1=\"9\" x2=\"19\" y1=\"12\" y2=\"12\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"/><line x1=\"9\" x2=\"19\" y1=\"17\" y2=\"17\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M5.00001 17H4.99002\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M5.00001 12H4.99002\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M5.00001 7H4.99002\"/></svg>', Be = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><line x1=\"12\" x2=\"19\" y1=\"7\" y2=\"7\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"/><line x1=\"12\" x2=\"19\" y1=\"12\" y2=\"12\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"/><line x1=\"12\" x2=\"19\" y1=\"17\" y2=\"17\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M7.79999 14L7.79999 7.2135C7.79999 7.12872 7.7011 7.0824 7.63597 7.13668L4.79999 9.5\"/></svg>', St = '<svg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M10 14.2L10 7.4135C10 7.32872 9.90111 7.28241 9.83598 7.33668L7 9.7\" stroke=\"black\" stroke-width=\"1.6\" stroke-linecap=\"round\"/><path d=\"M13.2087 14.2H13.2\" stroke=\"black\" stroke-width=\"1.6\" stroke-linecap=\"round\"/></svg>', Ot = '<svg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M13.2087 14.2H13.2\" stroke=\"black\" stroke-width=\"1.6\" stroke-linecap=\"round\"/><path d=\"M10 14.2L10 9.5\" stroke=\"black\" stroke-width=\"1.6\" stroke-linecap=\"round\"/><path d=\"M10 7.01L10 7\" stroke=\"black\" stroke-width=\"1.8\" stroke-linecap=\"round\"/></svg>', kt = '<svg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M13.2087 14.2H13.2\" stroke=\"black\" stroke-width=\"1.6\" stroke-linecap=\"round\"/><path d=\"M10 14.2L10 7.2\" stroke=\"black\" stroke-width=\"1.6\" stroke-linecap=\"round\"/></svg>', _t = '<svg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M16.0087 14.2H16\" stroke=\"black\" stroke-width=\"1.6\" stroke-linecap=\"round\"/><path d=\"M7 14.2L7.78865 12M13 14.2L12.1377 12M7.78865 12C7.78865 12 9.68362 7 10 7C10.3065 7 12.1377 12 12.1377 12M7.78865 12L12.1377 12\" stroke=\"black\" stroke-width=\"1.6\" stroke-linecap=\"round\"/></svg>', Et = '<svg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M14.2087 14.2H14.2\" stroke=\"black\" stroke-width=\"1.6\" stroke-linecap=\"round\"/><path d=\"M11.5 14.5C11.5 14.5 11 13.281 11 12.5M7 9.5C7 9.5 7.5 8.5 9 8.5C10.5 8.5 11 9.5 11 10.5L11 11.5M11 11.5L11 12.5M11 11.5C11 11.5 7 11 7 13C7 15.3031 11 15 11 12.5\" stroke=\"black\" stroke-width=\"1.6\" stroke-linecap=\"round\"/></svg>', It = '<svg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M8 14.2L8 7.4135C8 7.32872 7.90111 7.28241 7.83598 7.33668L5 9.7\" stroke=\"black\" stroke-width=\"1.6\" stroke-linecap=\"round\"/><path d=\"M14 13L16.4167 10.7778M16.4167 10.7778L14 8.5M16.4167 10.7778H11.6562\" stroke=\"black\" stroke-width=\"1.6\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>';\nvar A = typeof globalThis < \"u\" ? globalThis : typeof window < \"u\" ? window : typeof global < \"u\" ? global : typeof self < \"u\" ? self : {};\nfunction wt(e) {\n  if (e.__esModule)\n    return e;\n  var t = e.default;\n  if (typeof t == \"function\") {\n    var n = function r() {\n      return this instanceof r ? Reflect.construct(t, arguments, this.constructor) : t.apply(this, arguments);\n    };\n    n.prototype = t.prototype;\n  } else\n    n = {};\n  return Object.defineProperty(n, \"__esModule\", { value: !0 }), Object.keys(e).forEach(function(r) {\n    var i = Object.getOwnPropertyDescriptor(e, r);\n    Object.defineProperty(n, r, i.get ? i : {\n      enumerable: !0,\n      get: function() {\n        return e[r];\n      }\n    });\n  }), n;\n}\nvar c = {}, V = {}, Y = {};\nObject.defineProperty(Y, \"__esModule\", { value: !0 });\nY.allInputsSelector = Pt;\nfunction Pt() {\n  var e = [\"text\", \"password\", \"email\", \"number\", \"search\", \"tel\", \"url\"];\n  return \"[contenteditable=true], textarea, input:not([type]), \" + e.map(function(t) {\n    return 'input[type=\"'.concat(t, '\"]');\n  }).join(\", \");\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.allInputsSelector = void 0;\n  var t = Y;\n  Object.defineProperty(e, \"allInputsSelector\", { enumerable: !0, get: function() {\n    return t.allInputsSelector;\n  } });\n})(V);\nvar k = {}, J = {};\nObject.defineProperty(J, \"__esModule\", { value: !0 });\nJ.isNativeInput = jt;\nfunction jt(e) {\n  var t = [\n    \"INPUT\",\n    \"TEXTAREA\"\n  ];\n  return e && e.tagName ? t.includes(e.tagName) : !1;\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.isNativeInput = void 0;\n  var t = J;\n  Object.defineProperty(e, \"isNativeInput\", { enumerable: !0, get: function() {\n    return t.isNativeInput;\n  } });\n})(k);\nvar Fe = {}, Q = {};\nObject.defineProperty(Q, \"__esModule\", { value: !0 });\nQ.append = Tt;\nfunction Tt(e, t) {\n  Array.isArray(t) ? t.forEach(function(n) {\n    e.appendChild(n);\n  }) : e.appendChild(t);\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.append = void 0;\n  var t = Q;\n  Object.defineProperty(e, \"append\", { enumerable: !0, get: function() {\n    return t.append;\n  } });\n})(Fe);\nvar Z = {}, x = {};\nObject.defineProperty(x, \"__esModule\", { value: !0 });\nx.blockElements = Lt;\nfunction Lt() {\n  return [\n    \"address\",\n    \"article\",\n    \"aside\",\n    \"blockquote\",\n    \"canvas\",\n    \"div\",\n    \"dl\",\n    \"dt\",\n    \"fieldset\",\n    \"figcaption\",\n    \"figure\",\n    \"footer\",\n    \"form\",\n    \"h1\",\n    \"h2\",\n    \"h3\",\n    \"h4\",\n    \"h5\",\n    \"h6\",\n    \"header\",\n    \"hgroup\",\n    \"hr\",\n    \"li\",\n    \"main\",\n    \"nav\",\n    \"noscript\",\n    \"ol\",\n    \"output\",\n    \"p\",\n    \"pre\",\n    \"ruby\",\n    \"section\",\n    \"table\",\n    \"tbody\",\n    \"thead\",\n    \"tr\",\n    \"tfoot\",\n    \"ul\",\n    \"video\"\n  ];\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.blockElements = void 0;\n  var t = x;\n  Object.defineProperty(e, \"blockElements\", { enumerable: !0, get: function() {\n    return t.blockElements;\n  } });\n})(Z);\nvar Re = {}, ee = {};\nObject.defineProperty(ee, \"__esModule\", { value: !0 });\nee.calculateBaseline = Mt;\nfunction Mt(e) {\n  var t = window.getComputedStyle(e), n = parseFloat(t.fontSize), r = parseFloat(t.lineHeight) || n * 1.2, i = parseFloat(t.paddingTop), a = parseFloat(t.borderTopWidth), l = parseFloat(t.marginTop), s = n * 0.8, o = (r - n) / 2, d = l + a + i + o + s;\n  return d;\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.calculateBaseline = void 0;\n  var t = ee;\n  Object.defineProperty(e, \"calculateBaseline\", { enumerable: !0, get: function() {\n    return t.calculateBaseline;\n  } });\n})(Re);\nvar qe = {}, te = {}, ne = {}, re = {};\nObject.defineProperty(re, \"__esModule\", { value: !0 });\nre.isContentEditable = Nt;\nfunction Nt(e) {\n  return e.contentEditable === \"true\";\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.isContentEditable = void 0;\n  var t = re;\n  Object.defineProperty(e, \"isContentEditable\", { enumerable: !0, get: function() {\n    return t.isContentEditable;\n  } });\n})(ne);\nObject.defineProperty(te, \"__esModule\", { value: !0 });\nte.canSetCaret = Bt;\nvar At = k, $t = ne;\nfunction Bt(e) {\n  var t = !0;\n  if ((0, At.isNativeInput)(e))\n    switch (e.type) {\n      case \"file\":\n      case \"checkbox\":\n      case \"radio\":\n      case \"hidden\":\n      case \"submit\":\n      case \"button\":\n      case \"image\":\n      case \"reset\":\n        t = !1;\n        break;\n    }\n  else\n    t = (0, $t.isContentEditable)(e);\n  return t;\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.canSetCaret = void 0;\n  var t = te;\n  Object.defineProperty(e, \"canSetCaret\", { enumerable: !0, get: function() {\n    return t.canSetCaret;\n  } });\n})(qe);\nvar $ = {}, ie = {};\nfunction Wt(e, t, n) {\n  const r = n.value !== void 0 ? \"value\" : \"get\", i = n[r], a = `#${t}Cache`;\n  if (n[r] = function(...l) {\n    return this[a] === void 0 && (this[a] = i.apply(this, l)), this[a];\n  }, r === \"get\" && n.set) {\n    const l = n.set;\n    n.set = function(s) {\n      delete e[a], l.apply(this, s);\n    };\n  }\n  return n;\n}\nfunction Ue() {\n  const e = {\n    win: !1,\n    mac: !1,\n    x11: !1,\n    linux: !1\n  }, t = Object.keys(e).find((n) => window.navigator.appVersion.toLowerCase().indexOf(n) !== -1);\n  return t !== void 0 && (e[t] = !0), e;\n}\nfunction ae(e) {\n  return e != null && e !== \"\" && (typeof e != \"object\" || Object.keys(e).length > 0);\n}\nfunction Dt(e) {\n  return !ae(e);\n}\nconst Ht = () => typeof window < \"u\" && window.navigator !== null && ae(window.navigator.platform) && (/iP(ad|hone|od)/.test(window.navigator.platform) || window.navigator.platform === \"MacIntel\" && window.navigator.maxTouchPoints > 1);\nfunction Ft(e) {\n  const t = Ue();\n  return e = e.replace(/shift/gi, \"⇧\").replace(/backspace/gi, \"⌫\").replace(/enter/gi, \"⏎\").replace(/up/gi, \"↑\").replace(/left/gi, \"→\").replace(/down/gi, \"↓\").replace(/right/gi, \"←\").replace(/escape/gi, \"⎋\").replace(/insert/gi, \"Ins\").replace(/delete/gi, \"␡\").replace(/\\+/gi, \"+\"), t.mac ? e = e.replace(/ctrl|cmd/gi, \"⌘\").replace(/alt/gi, \"⌥\") : e = e.replace(/cmd/gi, \"Ctrl\").replace(/windows/gi, \"WIN\"), e;\n}\nfunction Rt(e) {\n  return e[0].toUpperCase() + e.slice(1);\n}\nfunction qt(e) {\n  const t = document.createElement(\"div\");\n  t.style.position = \"absolute\", t.style.left = \"-999px\", t.style.bottom = \"-999px\", t.innerHTML = e, document.body.appendChild(t);\n  const n = window.getSelection(), r = document.createRange();\n  if (r.selectNode(t), n === null)\n    throw new Error(\"Cannot copy text to clipboard\");\n  n.removeAllRanges(), n.addRange(r), document.execCommand(\"copy\"), document.body.removeChild(t);\n}\nfunction Ut(e, t, n) {\n  let r;\n  return (...i) => {\n    const a = this, l = () => {\n      r = void 0, n !== !0 && e.apply(a, i);\n    }, s = n === !0 && r !== void 0;\n    window.clearTimeout(r), r = window.setTimeout(l, t), s && e.apply(a, i);\n  };\n}\nfunction S(e) {\n  return Object.prototype.toString.call(e).match(/\\s([a-zA-Z]+)/)[1].toLowerCase();\n}\nfunction zt(e) {\n  return S(e) === \"boolean\";\n}\nfunction ze(e) {\n  return S(e) === \"function\" || S(e) === \"asyncfunction\";\n}\nfunction Kt(e) {\n  return ze(e) && /^\\s*class\\s+/.test(e.toString());\n}\nfunction Xt(e) {\n  return S(e) === \"number\";\n}\nfunction M(e) {\n  return S(e) === \"object\";\n}\nfunction Gt(e) {\n  return Promise.resolve(e) === e;\n}\nfunction Vt(e) {\n  return S(e) === \"string\";\n}\nfunction Yt(e) {\n  return S(e) === \"undefined\";\n}\nfunction X(e, ...t) {\n  if (!t.length)\n    return e;\n  const n = t.shift();\n  if (M(e) && M(n))\n    for (const r in n)\n      M(n[r]) ? (e[r] === void 0 && Object.assign(e, { [r]: {} }), X(e[r], n[r])) : Object.assign(e, { [r]: n[r] });\n  return X(e, ...t);\n}\nfunction Jt(e, t, n) {\n  const r = `«${t}» is deprecated and will be removed in the next major release. Please use the «${n}» instead.`;\n  e && console.warn(r);\n}\nfunction Qt(e) {\n  try {\n    return new URL(e).href;\n  } catch {\n  }\n  return e.substring(0, 2) === \"//\" ? window.location.protocol + e : window.location.origin + e;\n}\nfunction Zt(e) {\n  return e > 47 && e < 58 || e === 32 || e === 13 || e === 229 || e > 64 && e < 91 || e > 95 && e < 112 || e > 185 && e < 193 || e > 218 && e < 223;\n}\nconst xt = {\n  BACKSPACE: 8,\n  TAB: 9,\n  ENTER: 13,\n  SHIFT: 16,\n  CTRL: 17,\n  ALT: 18,\n  ESC: 27,\n  SPACE: 32,\n  LEFT: 37,\n  UP: 38,\n  DOWN: 40,\n  RIGHT: 39,\n  DELETE: 46,\n  META: 91,\n  SLASH: 191\n}, en = {\n  LEFT: 0,\n  WHEEL: 1,\n  RIGHT: 2,\n  BACKWARD: 3,\n  FORWARD: 4\n};\nclass tn {\n  constructor() {\n    this.completed = Promise.resolve();\n  }\n  /**\n   * Add new promise to queue\n   * @param operation - promise should be added to queue\n   */\n  add(t) {\n    return new Promise((n, r) => {\n      this.completed = this.completed.then(t).then(n).catch(r);\n    });\n  }\n}\nfunction nn(e, t, n = void 0) {\n  let r, i, a, l = null, s = 0;\n  n || (n = {});\n  const o = function() {\n    s = n.leading === !1 ? 0 : Date.now(), l = null, a = e.apply(r, i), l === null && (r = i = null);\n  };\n  return function() {\n    const d = Date.now();\n    !s && n.leading === !1 && (s = d);\n    const u = t - (d - s);\n    return r = this, i = arguments, u <= 0 || u > t ? (l && (clearTimeout(l), l = null), s = d, a = e.apply(r, i), l === null && (r = i = null)) : !l && n.trailing !== !1 && (l = setTimeout(o, u)), a;\n  };\n}\nconst rn = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({\n  __proto__: null,\n  PromiseQueue: tn,\n  beautifyShortcut: Ft,\n  cacheable: Wt,\n  capitalize: Rt,\n  copyTextToClipboard: qt,\n  debounce: Ut,\n  deepMerge: X,\n  deprecationAssert: Jt,\n  getUserOS: Ue,\n  getValidUrl: Qt,\n  isBoolean: zt,\n  isClass: Kt,\n  isEmpty: Dt,\n  isFunction: ze,\n  isIosDevice: Ht,\n  isNumber: Xt,\n  isObject: M,\n  isPrintableKey: Zt,\n  isPromise: Gt,\n  isString: Vt,\n  isUndefined: Yt,\n  keyCodes: xt,\n  mouseButtons: en,\n  notEmpty: ae,\n  throttle: nn,\n  typeOf: S\n}, Symbol.toStringTag, { value: \"Module\" })), le = /* @__PURE__ */ wt(rn);\nObject.defineProperty(ie, \"__esModule\", { value: !0 });\nie.containsOnlyInlineElements = sn;\nvar an = le, ln = Z;\nfunction sn(e) {\n  var t;\n  (0, an.isString)(e) ? (t = document.createElement(\"div\"), t.innerHTML = e) : t = e;\n  var n = function(r) {\n    return !(0, ln.blockElements)().includes(r.tagName.toLowerCase()) && Array.from(r.children).every(n);\n  };\n  return Array.from(t.children).every(n);\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.containsOnlyInlineElements = void 0;\n  var t = ie;\n  Object.defineProperty(e, \"containsOnlyInlineElements\", { enumerable: !0, get: function() {\n    return t.containsOnlyInlineElements;\n  } });\n})($);\nvar Ke = {}, se = {}, B = {}, oe = {};\nObject.defineProperty(oe, \"__esModule\", { value: !0 });\noe.make = on;\nfunction on(e, t, n) {\n  var r;\n  t === void 0 && (t = null), n === void 0 && (n = {});\n  var i = document.createElement(e);\n  if (Array.isArray(t)) {\n    var a = t.filter(function(s) {\n      return s !== void 0;\n    });\n    (r = i.classList).add.apply(r, a);\n  } else\n    t !== null && i.classList.add(t);\n  for (var l in n)\n    Object.prototype.hasOwnProperty.call(n, l) && (i[l] = n[l]);\n  return i;\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.make = void 0;\n  var t = oe;\n  Object.defineProperty(e, \"make\", { enumerable: !0, get: function() {\n    return t.make;\n  } });\n})(B);\nObject.defineProperty(se, \"__esModule\", { value: !0 });\nse.fragmentToString = cn;\nvar un = B;\nfunction cn(e) {\n  var t = (0, un.make)(\"div\");\n  return t.appendChild(e), t.innerHTML;\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.fragmentToString = void 0;\n  var t = se;\n  Object.defineProperty(e, \"fragmentToString\", { enumerable: !0, get: function() {\n    return t.fragmentToString;\n  } });\n})(Ke);\nvar Xe = {}, ue = {};\nObject.defineProperty(ue, \"__esModule\", { value: !0 });\nue.getContentLength = fn;\nvar dn = k;\nfunction fn(e) {\n  var t, n;\n  return (0, dn.isNativeInput)(e) ? e.value.length : e.nodeType === Node.TEXT_NODE ? e.length : (n = (t = e.textContent) === null || t === void 0 ? void 0 : t.length) !== null && n !== void 0 ? n : 0;\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.getContentLength = void 0;\n  var t = ue;\n  Object.defineProperty(e, \"getContentLength\", { enumerable: !0, get: function() {\n    return t.getContentLength;\n  } });\n})(Xe);\nvar ce = {}, de = {}, We = A && A.__spreadArray || function(e, t, n) {\n  if (n || arguments.length === 2)\n    for (var r = 0, i = t.length, a; r < i; r++)\n      (a || !(r in t)) && (a || (a = Array.prototype.slice.call(t, 0, r)), a[r] = t[r]);\n  return e.concat(a || Array.prototype.slice.call(t));\n};\nObject.defineProperty(de, \"__esModule\", { value: !0 });\nde.getDeepestBlockElements = Ge;\nvar pn = $;\nfunction Ge(e) {\n  return (0, pn.containsOnlyInlineElements)(e) ? [e] : Array.from(e.children).reduce(function(t, n) {\n    return We(We([], t, !0), Ge(n), !0);\n  }, []);\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.getDeepestBlockElements = void 0;\n  var t = de;\n  Object.defineProperty(e, \"getDeepestBlockElements\", { enumerable: !0, get: function() {\n    return t.getDeepestBlockElements;\n  } });\n})(ce);\nvar Ve = {}, fe = {}, W = {}, pe = {};\nObject.defineProperty(pe, \"__esModule\", { value: !0 });\npe.isLineBreakTag = hn;\nfunction hn(e) {\n  return [\n    \"BR\",\n    \"WBR\"\n  ].includes(e.tagName);\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.isLineBreakTag = void 0;\n  var t = pe;\n  Object.defineProperty(e, \"isLineBreakTag\", { enumerable: !0, get: function() {\n    return t.isLineBreakTag;\n  } });\n})(W);\nvar D = {}, he = {};\nObject.defineProperty(he, \"__esModule\", { value: !0 });\nhe.isSingleTag = mn;\nfunction mn(e) {\n  return [\n    \"AREA\",\n    \"BASE\",\n    \"BR\",\n    \"COL\",\n    \"COMMAND\",\n    \"EMBED\",\n    \"HR\",\n    \"IMG\",\n    \"INPUT\",\n    \"KEYGEN\",\n    \"LINK\",\n    \"META\",\n    \"PARAM\",\n    \"SOURCE\",\n    \"TRACK\",\n    \"WBR\"\n  ].includes(e.tagName);\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.isSingleTag = void 0;\n  var t = he;\n  Object.defineProperty(e, \"isSingleTag\", { enumerable: !0, get: function() {\n    return t.isSingleTag;\n  } });\n})(D);\nObject.defineProperty(fe, \"__esModule\", { value: !0 });\nfe.getDeepestNode = Ye;\nvar gn = k, vn = W, bn = D;\nfunction Ye(e, t) {\n  t === void 0 && (t = !1);\n  var n = t ? \"lastChild\" : \"firstChild\", r = t ? \"previousSibling\" : \"nextSibling\";\n  if (e.nodeType === Node.ELEMENT_NODE && e[n]) {\n    var i = e[n];\n    if ((0, bn.isSingleTag)(i) && !(0, gn.isNativeInput)(i) && !(0, vn.isLineBreakTag)(i))\n      if (i[r])\n        i = i[r];\n      else if (i.parentNode !== null && i.parentNode[r])\n        i = i.parentNode[r];\n      else\n        return i.parentNode;\n    return Ye(i, t);\n  }\n  return e;\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.getDeepestNode = void 0;\n  var t = fe;\n  Object.defineProperty(e, \"getDeepestNode\", { enumerable: !0, get: function() {\n    return t.getDeepestNode;\n  } });\n})(Ve);\nvar Je = {}, me = {}, T = A && A.__spreadArray || function(e, t, n) {\n  if (n || arguments.length === 2)\n    for (var r = 0, i = t.length, a; r < i; r++)\n      (a || !(r in t)) && (a || (a = Array.prototype.slice.call(t, 0, r)), a[r] = t[r]);\n  return e.concat(a || Array.prototype.slice.call(t));\n};\nObject.defineProperty(me, \"__esModule\", { value: !0 });\nme.findAllInputs = kn;\nvar yn = $, Cn = ce, Sn = V, On = k;\nfunction kn(e) {\n  return Array.from(e.querySelectorAll((0, Sn.allInputsSelector)())).reduce(function(t, n) {\n    return (0, On.isNativeInput)(n) || (0, yn.containsOnlyInlineElements)(n) ? T(T([], t, !0), [n], !1) : T(T([], t, !0), (0, Cn.getDeepestBlockElements)(n), !0);\n  }, []);\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.findAllInputs = void 0;\n  var t = me;\n  Object.defineProperty(e, \"findAllInputs\", { enumerable: !0, get: function() {\n    return t.findAllInputs;\n  } });\n})(Je);\nvar Qe = {}, ge = {};\nObject.defineProperty(ge, \"__esModule\", { value: !0 });\nge.isCollapsedWhitespaces = _n;\nfunction _n(e) {\n  return !/[^\\t\\n\\r ]/.test(e);\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.isCollapsedWhitespaces = void 0;\n  var t = ge;\n  Object.defineProperty(e, \"isCollapsedWhitespaces\", { enumerable: !0, get: function() {\n    return t.isCollapsedWhitespaces;\n  } });\n})(Qe);\nvar ve = {}, be = {};\nObject.defineProperty(be, \"__esModule\", { value: !0 });\nbe.isElement = In;\nvar En = le;\nfunction In(e) {\n  return (0, En.isNumber)(e) ? !1 : !!e && !!e.nodeType && e.nodeType === Node.ELEMENT_NODE;\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.isElement = void 0;\n  var t = be;\n  Object.defineProperty(e, \"isElement\", { enumerable: !0, get: function() {\n    return t.isElement;\n  } });\n})(ve);\nvar Ze = {}, ye = {}, Ce = {}, Se = {};\nObject.defineProperty(Se, \"__esModule\", { value: !0 });\nSe.isLeaf = wn;\nfunction wn(e) {\n  return e === null ? !1 : e.childNodes.length === 0;\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.isLeaf = void 0;\n  var t = Se;\n  Object.defineProperty(e, \"isLeaf\", { enumerable: !0, get: function() {\n    return t.isLeaf;\n  } });\n})(Ce);\nvar Oe = {}, ke = {};\nObject.defineProperty(ke, \"__esModule\", { value: !0 });\nke.isNodeEmpty = Mn;\nvar Pn = W, jn = ve, Tn = k, Ln = D;\nfunction Mn(e, t) {\n  var n = \"\";\n  return (0, Ln.isSingleTag)(e) && !(0, Pn.isLineBreakTag)(e) ? !1 : ((0, jn.isElement)(e) && (0, Tn.isNativeInput)(e) ? n = e.value : e.textContent !== null && (n = e.textContent.replace(\"​\", \"\")), t !== void 0 && (n = n.replace(new RegExp(t, \"g\"), \"\")), n.trim().length === 0);\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.isNodeEmpty = void 0;\n  var t = ke;\n  Object.defineProperty(e, \"isNodeEmpty\", { enumerable: !0, get: function() {\n    return t.isNodeEmpty;\n  } });\n})(Oe);\nObject.defineProperty(ye, \"__esModule\", { value: !0 });\nye.isEmpty = $n;\nvar Nn = Ce, An = Oe;\nfunction $n(e, t) {\n  e.normalize();\n  for (var n = [e]; n.length > 0; ) {\n    var r = n.shift();\n    if (r) {\n      if (e = r, (0, Nn.isLeaf)(e) && !(0, An.isNodeEmpty)(e, t))\n        return !1;\n      n.push.apply(n, Array.from(e.childNodes));\n    }\n  }\n  return !0;\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.isEmpty = void 0;\n  var t = ye;\n  Object.defineProperty(e, \"isEmpty\", { enumerable: !0, get: function() {\n    return t.isEmpty;\n  } });\n})(Ze);\nvar xe = {}, _e = {};\nObject.defineProperty(_e, \"__esModule\", { value: !0 });\n_e.isFragment = Wn;\nvar Bn = le;\nfunction Wn(e) {\n  return (0, Bn.isNumber)(e) ? !1 : !!e && !!e.nodeType && e.nodeType === Node.DOCUMENT_FRAGMENT_NODE;\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.isFragment = void 0;\n  var t = _e;\n  Object.defineProperty(e, \"isFragment\", { enumerable: !0, get: function() {\n    return t.isFragment;\n  } });\n})(xe);\nvar et = {}, Ee = {};\nObject.defineProperty(Ee, \"__esModule\", { value: !0 });\nEe.isHTMLString = Hn;\nvar Dn = B;\nfunction Hn(e) {\n  var t = (0, Dn.make)(\"div\");\n  return t.innerHTML = e, t.childElementCount > 0;\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.isHTMLString = void 0;\n  var t = Ee;\n  Object.defineProperty(e, \"isHTMLString\", { enumerable: !0, get: function() {\n    return t.isHTMLString;\n  } });\n})(et);\nvar tt = {}, Ie = {};\nObject.defineProperty(Ie, \"__esModule\", { value: !0 });\nIe.offset = Fn;\nfunction Fn(e) {\n  var t = e.getBoundingClientRect(), n = window.pageXOffset || document.documentElement.scrollLeft, r = window.pageYOffset || document.documentElement.scrollTop, i = t.top + r, a = t.left + n;\n  return {\n    top: i,\n    left: a,\n    bottom: i + t.height,\n    right: a + t.width\n  };\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.offset = void 0;\n  var t = Ie;\n  Object.defineProperty(e, \"offset\", { enumerable: !0, get: function() {\n    return t.offset;\n  } });\n})(tt);\nvar nt = {}, we = {};\nObject.defineProperty(we, \"__esModule\", { value: !0 });\nwe.prepend = Rn;\nfunction Rn(e, t) {\n  Array.isArray(t) ? (t = t.reverse(), t.forEach(function(n) {\n    return e.prepend(n);\n  })) : e.prepend(t);\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.prepend = void 0;\n  var t = we;\n  Object.defineProperty(e, \"prepend\", { enumerable: !0, get: function() {\n    return t.prepend;\n  } });\n})(nt);\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.prepend = e.offset = e.make = e.isLineBreakTag = e.isSingleTag = e.isNodeEmpty = e.isLeaf = e.isHTMLString = e.isFragment = e.isEmpty = e.isElement = e.isContentEditable = e.isCollapsedWhitespaces = e.findAllInputs = e.isNativeInput = e.allInputsSelector = e.getDeepestNode = e.getDeepestBlockElements = e.getContentLength = e.fragmentToString = e.containsOnlyInlineElements = e.canSetCaret = e.calculateBaseline = e.blockElements = e.append = void 0;\n  var t = V;\n  Object.defineProperty(e, \"allInputsSelector\", { enumerable: !0, get: function() {\n    return t.allInputsSelector;\n  } });\n  var n = k;\n  Object.defineProperty(e, \"isNativeInput\", { enumerable: !0, get: function() {\n    return n.isNativeInput;\n  } });\n  var r = Fe;\n  Object.defineProperty(e, \"append\", { enumerable: !0, get: function() {\n    return r.append;\n  } });\n  var i = Z;\n  Object.defineProperty(e, \"blockElements\", { enumerable: !0, get: function() {\n    return i.blockElements;\n  } });\n  var a = Re;\n  Object.defineProperty(e, \"calculateBaseline\", { enumerable: !0, get: function() {\n    return a.calculateBaseline;\n  } });\n  var l = qe;\n  Object.defineProperty(e, \"canSetCaret\", { enumerable: !0, get: function() {\n    return l.canSetCaret;\n  } });\n  var s = $;\n  Object.defineProperty(e, \"containsOnlyInlineElements\", { enumerable: !0, get: function() {\n    return s.containsOnlyInlineElements;\n  } });\n  var o = Ke;\n  Object.defineProperty(e, \"fragmentToString\", { enumerable: !0, get: function() {\n    return o.fragmentToString;\n  } });\n  var d = Xe;\n  Object.defineProperty(e, \"getContentLength\", { enumerable: !0, get: function() {\n    return d.getContentLength;\n  } });\n  var u = ce;\n  Object.defineProperty(e, \"getDeepestBlockElements\", { enumerable: !0, get: function() {\n    return u.getDeepestBlockElements;\n  } });\n  var p = Ve;\n  Object.defineProperty(e, \"getDeepestNode\", { enumerable: !0, get: function() {\n    return p.getDeepestNode;\n  } });\n  var g = Je;\n  Object.defineProperty(e, \"findAllInputs\", { enumerable: !0, get: function() {\n    return g.findAllInputs;\n  } });\n  var w = Qe;\n  Object.defineProperty(e, \"isCollapsedWhitespaces\", { enumerable: !0, get: function() {\n    return w.isCollapsedWhitespaces;\n  } });\n  var _ = ne;\n  Object.defineProperty(e, \"isContentEditable\", { enumerable: !0, get: function() {\n    return _.isContentEditable;\n  } });\n  var ut = ve;\n  Object.defineProperty(e, \"isElement\", { enumerable: !0, get: function() {\n    return ut.isElement;\n  } });\n  var ct = Ze;\n  Object.defineProperty(e, \"isEmpty\", { enumerable: !0, get: function() {\n    return ct.isEmpty;\n  } });\n  var dt = xe;\n  Object.defineProperty(e, \"isFragment\", { enumerable: !0, get: function() {\n    return dt.isFragment;\n  } });\n  var ft = et;\n  Object.defineProperty(e, \"isHTMLString\", { enumerable: !0, get: function() {\n    return ft.isHTMLString;\n  } });\n  var pt = Ce;\n  Object.defineProperty(e, \"isLeaf\", { enumerable: !0, get: function() {\n    return pt.isLeaf;\n  } });\n  var ht = Oe;\n  Object.defineProperty(e, \"isNodeEmpty\", { enumerable: !0, get: function() {\n    return ht.isNodeEmpty;\n  } });\n  var mt = W;\n  Object.defineProperty(e, \"isLineBreakTag\", { enumerable: !0, get: function() {\n    return mt.isLineBreakTag;\n  } });\n  var gt = D;\n  Object.defineProperty(e, \"isSingleTag\", { enumerable: !0, get: function() {\n    return gt.isSingleTag;\n  } });\n  var vt = B;\n  Object.defineProperty(e, \"make\", { enumerable: !0, get: function() {\n    return vt.make;\n  } });\n  var bt = tt;\n  Object.defineProperty(e, \"offset\", { enumerable: !0, get: function() {\n    return bt.offset;\n  } });\n  var yt = nt;\n  Object.defineProperty(e, \"prepend\", { enumerable: !0, get: function() {\n    return yt.prepend;\n  } });\n})(c);\nconst m = \"cdx-list\", h = {\n  wrapper: m,\n  item: `${m}__item`,\n  itemContent: `${m}__item-content`,\n  itemChildren: `${m}__item-children`\n};\nclass v {\n  /**\n   * Getter for all CSS classes used in unordered list rendering\n   */\n  static get CSS() {\n    return {\n      ...h,\n      orderedList: `${m}-ordered`\n    };\n  }\n  /**\n   * Assign passed readonly mode and config to relevant class properties\n   * @param readonly - read-only mode flag\n   * @param config - user config for Tool\n   */\n  constructor(t, n) {\n    this.config = n, this.readOnly = t;\n  }\n  /**\n   * Renders ol wrapper for list\n   * @param isRoot - boolean variable that represents level of the wrappre (root or childList)\n   * @returns - created html ol element\n   */\n  renderWrapper(t) {\n    let n;\n    return t === !0 ? n = c.make(\"ol\", [v.CSS.wrapper, v.CSS.orderedList]) : n = c.make(\"ol\", [v.CSS.orderedList, v.CSS.itemChildren]), n;\n  }\n  /**\n   * Redners list item element\n   * @param content - content used in list item rendering\n   * @param _meta - meta of the list item unused in rendering of the ordered list\n   * @returns - created html list item element\n   */\n  renderItem(t, n) {\n    const r = c.make(\"li\", v.CSS.item), i = c.make(\"div\", v.CSS.itemContent, {\n      innerHTML: t,\n      contentEditable: (!this.readOnly).toString()\n    });\n    return r.appendChild(i), r;\n  }\n  /**\n   * Return the item content\n   * @param item - item wrapper (<li>)\n   * @returns - item content string\n   */\n  getItemContent(t) {\n    const n = t.querySelector(`.${v.CSS.itemContent}`);\n    return !n || c.isEmpty(n) ? \"\" : n.innerHTML;\n  }\n  /**\n   * Returns item meta, for ordered list\n   * @returns item meta object\n   */\n  getItemMeta() {\n    return {};\n  }\n  /**\n   * Returns default item meta used on creation of the new item\n   */\n  composeDefaultMeta() {\n    return {};\n  }\n}\nclass b {\n  /**\n   * Getter for all CSS classes used in unordered list rendering\n   */\n  static get CSS() {\n    return {\n      ...h,\n      unorderedList: `${m}-unordered`\n    };\n  }\n  /**\n   * Assign passed readonly mode and config to relevant class properties\n   * @param readonly - read-only mode flag\n   * @param config - user config for Tool\n   */\n  constructor(t, n) {\n    this.config = n, this.readOnly = t;\n  }\n  /**\n   * Renders ol wrapper for list\n   * @param isRoot - boolean variable that represents level of the wrappre (root or childList)\n   * @returns - created html ul element\n   */\n  renderWrapper(t) {\n    let n;\n    return t === !0 ? n = c.make(\"ul\", [b.CSS.wrapper, b.CSS.unorderedList]) : n = c.make(\"ul\", [b.CSS.unorderedList, b.CSS.itemChildren]), n;\n  }\n  /**\n   * Redners list item element\n   * @param content - content used in list item rendering\n   * @param _meta - meta of the list item unused in rendering of the unordered list\n   * @returns - created html list item element\n   */\n  renderItem(t, n) {\n    const r = c.make(\"li\", b.CSS.item), i = c.make(\"div\", b.CSS.itemContent, {\n      innerHTML: t,\n      contentEditable: (!this.readOnly).toString()\n    });\n    return r.appendChild(i), r;\n  }\n  /**\n   * Return the item content\n   * @param item - item wrapper (<li>)\n   * @returns - item content string\n   */\n  getItemContent(t) {\n    const n = t.querySelector(`.${b.CSS.itemContent}`);\n    return !n || c.isEmpty(n) ? \"\" : n.innerHTML;\n  }\n  /**\n   * Returns item meta, for unordered list\n   * @returns Item meta object\n   */\n  getItemMeta() {\n    return {};\n  }\n  /**\n   * Returns default item meta used on creation of the new item\n   */\n  composeDefaultMeta() {\n    return {};\n  }\n}\nfunction O(e) {\n  return e.nodeType === Node.ELEMENT_NODE;\n}\nvar j = {}, Pe = {}, H = {}, F = {};\nObject.defineProperty(F, \"__esModule\", { value: !0 });\nF.getContenteditableSlice = Un;\nvar qn = c;\nfunction Un(e, t, n, r, i) {\n  var a;\n  i === void 0 && (i = !1);\n  var l = document.createRange();\n  if (r === \"left\" ? (l.setStart(e, 0), l.setEnd(t, n)) : (l.setStart(t, n), l.setEnd(e, e.childNodes.length)), i === !0) {\n    var s = l.extractContents();\n    return (0, qn.fragmentToString)(s);\n  }\n  var o = l.cloneContents(), d = document.createElement(\"div\");\n  d.appendChild(o);\n  var u = (a = d.textContent) !== null && a !== void 0 ? a : \"\";\n  return u;\n}\nObject.defineProperty(H, \"__esModule\", { value: !0 });\nH.checkContenteditableSliceForEmptiness = Xn;\nvar zn = c, Kn = F;\nfunction Xn(e, t, n, r) {\n  var i = (0, Kn.getContenteditableSlice)(e, t, n, r);\n  return (0, zn.isCollapsedWhitespaces)(i);\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.checkContenteditableSliceForEmptiness = void 0;\n  var t = H;\n  Object.defineProperty(e, \"checkContenteditableSliceForEmptiness\", { enumerable: !0, get: function() {\n    return t.checkContenteditableSliceForEmptiness;\n  } });\n})(Pe);\nvar rt = {};\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.getContenteditableSlice = void 0;\n  var t = F;\n  Object.defineProperty(e, \"getContenteditableSlice\", { enumerable: !0, get: function() {\n    return t.getContenteditableSlice;\n  } });\n})(rt);\nvar it = {}, je = {};\nObject.defineProperty(je, \"__esModule\", { value: !0 });\nje.focus = Vn;\nvar Gn = c;\nfunction Vn(e, t) {\n  var n, r;\n  if (t === void 0 && (t = !0), (0, Gn.isNativeInput)(e)) {\n    e.focus();\n    var i = t ? 0 : e.value.length;\n    e.setSelectionRange(i, i);\n  } else {\n    var a = document.createRange(), l = window.getSelection();\n    if (!l)\n      return;\n    var s = function(g, w) {\n      w === void 0 && (w = !1);\n      var _ = document.createTextNode(\"\");\n      w ? g.insertBefore(_, g.firstChild) : g.appendChild(_), a.setStart(_, 0), a.setEnd(_, 0);\n    }, o = function(g) {\n      return g != null;\n    }, d = e.childNodes, u = t ? d[0] : d[d.length - 1];\n    if (o(u)) {\n      for (; o(u) && u.nodeType !== Node.TEXT_NODE; )\n        u = t ? u.firstChild : u.lastChild;\n      if (o(u) && u.nodeType === Node.TEXT_NODE) {\n        var p = (r = (n = u.textContent) === null || n === void 0 ? void 0 : n.length) !== null && r !== void 0 ? r : 0, i = t ? 0 : p;\n        a.setStart(u, i), a.setEnd(u, i);\n      } else\n        s(e, t);\n    } else\n      s(e);\n    l.removeAllRanges(), l.addRange(a);\n  }\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.focus = void 0;\n  var t = je;\n  Object.defineProperty(e, \"focus\", { enumerable: !0, get: function() {\n    return t.focus;\n  } });\n})(it);\nvar Te = {}, R = {};\nObject.defineProperty(R, \"__esModule\", { value: !0 });\nR.getCaretNodeAndOffset = Yn;\nfunction Yn() {\n  var e = window.getSelection();\n  if (e === null)\n    return [null, 0];\n  var t = e.focusNode, n = e.focusOffset;\n  return t === null ? [null, 0] : (t.nodeType !== Node.TEXT_NODE && t.childNodes.length > 0 && (t.childNodes[n] !== void 0 ? (t = t.childNodes[n], n = 0) : (t = t.childNodes[n - 1], t.textContent !== null && (n = t.textContent.length))), [t, n]);\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.getCaretNodeAndOffset = void 0;\n  var t = R;\n  Object.defineProperty(e, \"getCaretNodeAndOffset\", { enumerable: !0, get: function() {\n    return t.getCaretNodeAndOffset;\n  } });\n})(Te);\nvar at = {}, q = {};\nObject.defineProperty(q, \"__esModule\", { value: !0 });\nq.getRange = Jn;\nfunction Jn() {\n  var e = window.getSelection();\n  return e && e.rangeCount ? e.getRangeAt(0) : null;\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.getRange = void 0;\n  var t = q;\n  Object.defineProperty(e, \"getRange\", { enumerable: !0, get: function() {\n    return t.getRange;\n  } });\n})(at);\nvar lt = {}, Le = {};\nObject.defineProperty(Le, \"__esModule\", { value: !0 });\nLe.isCaretAtEndOfInput = xn;\nvar De = c, Qn = Te, Zn = Pe;\nfunction xn(e) {\n  var t = (0, De.getDeepestNode)(e, !0);\n  if (t === null)\n    return !0;\n  if ((0, De.isNativeInput)(t))\n    return t.selectionEnd === t.value.length;\n  var n = (0, Qn.getCaretNodeAndOffset)(), r = n[0], i = n[1];\n  return r === null ? !1 : (0, Zn.checkContenteditableSliceForEmptiness)(e, r, i, \"right\");\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.isCaretAtEndOfInput = void 0;\n  var t = Le;\n  Object.defineProperty(e, \"isCaretAtEndOfInput\", { enumerable: !0, get: function() {\n    return t.isCaretAtEndOfInput;\n  } });\n})(lt);\nvar st = {}, Me = {};\nObject.defineProperty(Me, \"__esModule\", { value: !0 });\nMe.isCaretAtStartOfInput = nr;\nvar L = c, er = R, tr = H;\nfunction nr(e) {\n  var t = (0, L.getDeepestNode)(e);\n  if (t === null || (0, L.isEmpty)(e))\n    return !0;\n  if ((0, L.isNativeInput)(t))\n    return t.selectionEnd === 0;\n  if ((0, L.isEmpty)(e))\n    return !0;\n  var n = (0, er.getCaretNodeAndOffset)(), r = n[0], i = n[1];\n  return r === null ? !1 : (0, tr.checkContenteditableSliceForEmptiness)(e, r, i, \"left\");\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.isCaretAtStartOfInput = void 0;\n  var t = Me;\n  Object.defineProperty(e, \"isCaretAtStartOfInput\", { enumerable: !0, get: function() {\n    return t.isCaretAtStartOfInput;\n  } });\n})(st);\nvar ot = {}, Ne = {};\nObject.defineProperty(Ne, \"__esModule\", { value: !0 });\nNe.save = ar;\nvar rr = c, ir = q;\nfunction ar() {\n  var e = (0, ir.getRange)(), t = (0, rr.make)(\"span\");\n  if (t.id = \"cursor\", t.hidden = !0, !!e)\n    return e.insertNode(t), function() {\n      var r = window.getSelection();\n      r && (e.setStartAfter(t), e.setEndAfter(t), r.removeAllRanges(), r.addRange(e), setTimeout(function() {\n        t.remove();\n      }, 150));\n    };\n}\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.save = void 0;\n  var t = Ne;\n  Object.defineProperty(e, \"save\", { enumerable: !0, get: function() {\n    return t.save;\n  } });\n})(ot);\n(function(e) {\n  Object.defineProperty(e, \"__esModule\", { value: !0 }), e.save = e.isCaretAtStartOfInput = e.isCaretAtEndOfInput = e.getRange = e.getCaretNodeAndOffset = e.focus = e.getContenteditableSlice = e.checkContenteditableSliceForEmptiness = void 0;\n  var t = Pe;\n  Object.defineProperty(e, \"checkContenteditableSliceForEmptiness\", { enumerable: !0, get: function() {\n    return t.checkContenteditableSliceForEmptiness;\n  } });\n  var n = rt;\n  Object.defineProperty(e, \"getContenteditableSlice\", { enumerable: !0, get: function() {\n    return n.getContenteditableSlice;\n  } });\n  var r = it;\n  Object.defineProperty(e, \"focus\", { enumerable: !0, get: function() {\n    return r.focus;\n  } });\n  var i = Te;\n  Object.defineProperty(e, \"getCaretNodeAndOffset\", { enumerable: !0, get: function() {\n    return i.getCaretNodeAndOffset;\n  } });\n  var a = at;\n  Object.defineProperty(e, \"getRange\", { enumerable: !0, get: function() {\n    return a.getRange;\n  } });\n  var l = lt;\n  Object.defineProperty(e, \"isCaretAtEndOfInput\", { enumerable: !0, get: function() {\n    return l.isCaretAtEndOfInput;\n  } });\n  var s = st;\n  Object.defineProperty(e, \"isCaretAtStartOfInput\", { enumerable: !0, get: function() {\n    return s.isCaretAtStartOfInput;\n  } });\n  var o = ot;\n  Object.defineProperty(e, \"save\", { enumerable: !0, get: function() {\n    return o.save;\n  } });\n})(j);\nclass f {\n  /**\n   * Getter for all CSS classes used in unordered list rendering\n   */\n  static get CSS() {\n    return {\n      ...h,\n      checklist: `${m}-checklist`,\n      itemChecked: `${m}__checkbox--checked`,\n      noHover: `${m}__checkbox--no-hover`,\n      checkbox: `${m}__checkbox-check`,\n      checkboxContainer: `${m}__checkbox`\n    };\n  }\n  /**\n   * Assign passed readonly mode and config to relevant class properties\n   * @param readonly - read-only mode flag\n   * @param config - user config for Tool\n   */\n  constructor(t, n) {\n    this.config = n, this.readOnly = t;\n  }\n  /**\n   * Renders ul wrapper for list\n   * @param isRoot - boolean variable that represents level of the wrappre (root or childList)\n   * @returns - created html ul element\n   */\n  renderWrapper(t) {\n    let n;\n    return t === !0 ? (n = c.make(\"ul\", [f.CSS.wrapper, f.CSS.checklist]), n.addEventListener(\"click\", (r) => {\n      const i = r.target;\n      if (i) {\n        const a = i.closest(`.${f.CSS.checkboxContainer}`);\n        a && a.contains(i) && this.toggleCheckbox(a);\n      }\n    })) : n = c.make(\"ul\", [f.CSS.checklist, f.CSS.itemChildren]), n;\n  }\n  /**\n   * Redners list item element\n   * @param content - content used in list item rendering\n   * @param meta - meta of the list item used in rendering of the checklist\n   * @returns - created html list item element\n   */\n  renderItem(t, n) {\n    const r = c.make(\"li\", [f.CSS.item, f.CSS.item]), i = c.make(\"div\", f.CSS.itemContent, {\n      innerHTML: t,\n      contentEditable: (!this.readOnly).toString()\n    }), a = c.make(\"span\", f.CSS.checkbox), l = c.make(\"div\", f.CSS.checkboxContainer);\n    return n.checked === !0 && l.classList.add(f.CSS.itemChecked), a.innerHTML = Ct, l.appendChild(a), r.appendChild(l), r.appendChild(i), r;\n  }\n  /**\n   * Return the item content\n   * @param item - item wrapper (<li>)\n   * @returns - item content string\n   */\n  getItemContent(t) {\n    const n = t.querySelector(`.${f.CSS.itemContent}`);\n    return !n || c.isEmpty(n) ? \"\" : n.innerHTML;\n  }\n  /**\n   * Return meta object of certain element\n   * @param item - will be returned meta information of this item\n   * @returns Item meta object\n   */\n  getItemMeta(t) {\n    const n = t.querySelector(`.${f.CSS.checkboxContainer}`);\n    return {\n      checked: n ? n.classList.contains(f.CSS.itemChecked) : !1\n    };\n  }\n  /**\n   * Returns default item meta used on creation of the new item\n   */\n  composeDefaultMeta() {\n    return { checked: !1 };\n  }\n  /**\n   * Toggle checklist item state\n   * @param checkbox - checkbox element to be toggled\n   */\n  toggleCheckbox(t) {\n    t.classList.toggle(f.CSS.itemChecked), t.classList.add(f.CSS.noHover), t.addEventListener(\"mouseleave\", () => this.removeSpecialHoverBehavior(t), { once: !0 });\n  }\n  /**\n   * Removes class responsible for special hover behavior on an item\n   * @param el - item wrapper\n   */\n  removeSpecialHoverBehavior(t) {\n    t.classList.remove(f.CSS.noHover);\n  }\n}\nfunction U(e, t = \"after\") {\n  const n = [];\n  let r;\n  function i(a) {\n    switch (t) {\n      case \"after\":\n        return a.nextElementSibling;\n      case \"before\":\n        return a.previousElementSibling;\n    }\n  }\n  for (r = i(e); r !== null; )\n    n.push(r), r = i(r);\n  return n.length !== 0 ? n : null;\n}\nfunction y(e, t = !0) {\n  let n = e;\n  return e.classList.contains(h.item) && (n = e.querySelector(`.${h.itemChildren}`)), n === null ? [] : t ? Array.from(n.querySelectorAll(`:scope > .${h.item}`)) : Array.from(n.querySelectorAll(`.${h.item}`));\n}\nfunction lr(e) {\n  return e.nextElementSibling === null;\n}\nfunction sr(e) {\n  return e.querySelector(`.${h.itemChildren}`) !== null;\n}\nfunction C(e) {\n  return e.querySelector(`.${h.itemChildren}`);\n}\nfunction z(e) {\n  let t = e;\n  e.classList.contains(h.item) && (t = C(e)), t !== null && y(t).length === 0 && t.remove();\n}\nfunction N(e) {\n  return e.querySelector(`.${h.itemContent}`);\n}\nfunction E(e, t = !0) {\n  const n = N(e);\n  n && j.focus(n, t);\n}\nclass K {\n  /**\n   * Getter method to get current item\n   * @returns current list item or null if caret position is not undefined\n   */\n  get currentItem() {\n    const t = window.getSelection();\n    if (!t)\n      return null;\n    let n = t.anchorNode;\n    return !n || (O(n) || (n = n.parentNode), !n) || !O(n) ? null : n.closest(`.${h.item}`);\n  }\n  /**\n   * Method that returns nesting level of the current item, null if there is no selection\n   */\n  get currentItemLevel() {\n    const t = this.currentItem;\n    if (t === null)\n      return null;\n    let n = t.parentNode, r = 0;\n    for (; n !== null && n !== this.listWrapper; )\n      O(n) && n.classList.contains(h.item) && (r += 1), n = n.parentNode;\n    return r + 1;\n  }\n  /**\n   * Assign all passed params and renderer to relevant class properties\n   * @param params - tool constructor options\n   * @param params.data - previously saved data\n   * @param params.config - user config for Tool\n   * @param params.api - Editor.js API\n   * @param params.readOnly - read-only mode flag\n   * @param renderer - renderer instance initialized in tool class\n   */\n  constructor({ data: t, config: n, api: r, readOnly: i, block: a }, l) {\n    this.config = n, this.data = t, this.readOnly = i, this.api = r, this.block = a, this.renderer = l;\n  }\n  /**\n   * Function that is responsible for rendering list with contents\n   * @returns Filled with content wrapper element of the list\n   */\n  render() {\n    return this.listWrapper = this.renderer.renderWrapper(!0), this.data.items.length ? this.appendItems(this.data.items, this.listWrapper) : this.appendItems(\n      [\n        {\n          content: \"\",\n          meta: {},\n          items: []\n        }\n      ],\n      this.listWrapper\n    ), this.readOnly || this.listWrapper.addEventListener(\n      \"keydown\",\n      (t) => {\n        switch (t.key) {\n          case \"Enter\":\n            this.enterPressed(t);\n            break;\n          case \"Backspace\":\n            this.backspace(t);\n            break;\n          case \"Tab\":\n            t.shiftKey ? this.shiftTab(t) : this.addTab(t);\n            break;\n        }\n      },\n      !1\n    ), \"start\" in this.data.meta && this.data.meta.start !== void 0 && this.changeStartWith(this.data.meta.start), \"counterType\" in this.data.meta && this.data.meta.counterType !== void 0 && this.changeCounters(this.data.meta.counterType), this.listWrapper;\n  }\n  /**\n   * Function that is responsible for list content saving\n   * @param wrapper - optional argument wrapper\n   * @returns whole list saved data if wrapper not passes, otherwise will return data of the passed wrapper\n   */\n  save(t) {\n    const n = t ?? this.listWrapper, r = (l) => y(l).map((o) => {\n      const d = C(o), u = this.renderer.getItemContent(o), p = this.renderer.getItemMeta(o), g = d ? r(d) : [];\n      return {\n        content: u,\n        meta: p,\n        items: g\n      };\n    }), i = n ? r(n) : [];\n    let a = {\n      style: this.data.style,\n      meta: {},\n      items: i\n    };\n    return this.data.style === \"ordered\" && (a.meta = {\n      start: this.data.meta.start,\n      counterType: this.data.meta.counterType\n    }), a;\n  }\n  /**\n   * On paste sanitzation config. Allow only tags that are allowed in the Tool.\n   * @returns - config that determines tags supposted by paste handler\n   * @todo - refactor and move to list instance\n   */\n  static get pasteConfig() {\n    return {\n      tags: [\"OL\", \"UL\", \"LI\"]\n    };\n  }\n  /**\n   * Method that specified hot to merge two List blocks.\n   * Called by Editor.js by backspace at the beginning of the Block\n   *\n   * Content of the first item of the next List would be merged with deepest item in current list\n   * Other items of the next List would be appended to the current list without any changes in nesting levels\n   * @param data - data of the second list to be merged with current\n   */\n  merge(t) {\n    const n = this.block.holder.querySelectorAll(`.${h.item}`), r = n[n.length - 1], i = N(r);\n    if (r === null || i === null || (i.insertAdjacentHTML(\"beforeend\", t.items[0].content), this.listWrapper === void 0))\n      return;\n    const a = y(this.listWrapper);\n    if (a.length === 0)\n      return;\n    const l = a[a.length - 1];\n    let s = C(l);\n    const o = t.items.shift();\n    o !== void 0 && (o.items.length !== 0 && (s === null && (s = this.renderer.renderWrapper(!1)), this.appendItems(o.items, s)), t.items.length > 0 && this.appendItems(t.items, this.listWrapper));\n  }\n  /**\n   * On paste callback that is fired from Editor.\n   * @param event - event with pasted data\n   * @todo - refactor and move to list instance\n   */\n  onPaste(t) {\n    const n = t.detail.data;\n    this.data = this.pasteHandler(n);\n    const r = this.listWrapper;\n    r && r.parentNode && r.parentNode.replaceChild(this.render(), r);\n  }\n  /**\n   * Handle UL, OL and LI tags paste and returns List data\n   * @param element - html element that contains whole list\n   * @todo - refactor and move to list instance\n   */\n  pasteHandler(t) {\n    const { tagName: n } = t;\n    let r = \"unordered\", i;\n    switch (n) {\n      case \"OL\":\n        r = \"ordered\", i = \"ol\";\n        break;\n      case \"UL\":\n      case \"LI\":\n        r = \"unordered\", i = \"ul\";\n    }\n    const a = {\n      style: r,\n      meta: {},\n      items: []\n    };\n    r === \"ordered\" && (this.data.meta.counterType = \"numeric\", this.data.meta.start = 1);\n    const l = (s) => Array.from(s.querySelectorAll(\":scope > li\")).map((d) => {\n      const u = d.querySelector(`:scope > ${i}`), p = u ? l(u) : [];\n      return {\n        content: d.innerHTML ?? \"\",\n        meta: {},\n        items: p\n      };\n    });\n    return a.items = l(t), a;\n  }\n  /**\n   * Changes ordered list start property value\n   * @param index - new value of the start property\n   */\n  changeStartWith(t) {\n    this.listWrapper.style.setProperty(\"counter-reset\", `item ${t - 1}`), this.data.meta.start = t;\n  }\n  /**\n   * Changes ordered list counterType property value\n   * @param counterType - new value of the counterType value\n   */\n  changeCounters(t) {\n    this.listWrapper.style.setProperty(\"--list-counter-type\", t), this.data.meta.counterType = t;\n  }\n  /**\n   * Handles Enter keypress\n   * @param event - keydown\n   */\n  enterPressed(t) {\n    var s;\n    const n = this.currentItem;\n    if (t.stopPropagation(), t.preventDefault(), t.isComposing || n === null)\n      return;\n    const r = ((s = this.renderer) == null ? void 0 : s.getItemContent(n).trim().length) === 0, i = n.parentNode === this.listWrapper, a = n.previousElementSibling === null, l = this.api.blocks.getCurrentBlockIndex();\n    if (i && r)\n      if (lr(n) && !sr(n)) {\n        a ? this.convertItemToDefaultBlock(l, !0) : this.convertItemToDefaultBlock();\n        return;\n      } else {\n        this.splitList(n);\n        return;\n      }\n    else if (r) {\n      this.unshiftItem(n);\n      return;\n    } else\n      this.splitItem(n);\n  }\n  /**\n   * Handle backspace\n   * @param event - keydown\n   */\n  backspace(t) {\n    var r;\n    const n = this.currentItem;\n    if (n !== null && j.isCaretAtStartOfInput(n) && ((r = window.getSelection()) == null ? void 0 : r.isCollapsed) !== !1) {\n      if (t.stopPropagation(), n.parentNode === this.listWrapper && n.previousElementSibling === null) {\n        this.convertFirstItemToDefaultBlock();\n        return;\n      }\n      t.preventDefault(), this.mergeItemWithPrevious(n);\n    }\n  }\n  /**\n   * Reduce indentation for current item\n   * @param event - keydown\n   */\n  shiftTab(t) {\n    t.stopPropagation(), t.preventDefault(), this.currentItem !== null && this.unshiftItem(this.currentItem);\n  }\n  /**\n   * Decrease indentation of the passed item\n   * @param item - list item to be unshifted\n   */\n  unshiftItem(t) {\n    if (!t.parentNode || !O(t.parentNode))\n      return;\n    const n = t.parentNode.closest(`.${h.item}`);\n    if (!n)\n      return;\n    let r = C(t);\n    if (t.parentElement === null)\n      return;\n    const i = U(t);\n    i !== null && (r === null && (r = this.renderer.renderWrapper(!1)), i.forEach((a) => {\n      r.appendChild(a);\n    }), t.appendChild(r)), n.after(t), E(t, !1), z(n);\n  }\n  /**\n   * Method that is used for list splitting and moving trailing items to the new separated list\n   * @param item - current item html element\n   */\n  splitList(t) {\n    const n = y(t), r = this.block, i = this.api.blocks.getCurrentBlockIndex();\n    if (n.length !== 0) {\n      const o = n[0];\n      this.unshiftItem(o), E(t, !1);\n    }\n    if (t.previousElementSibling === null && t.parentNode === this.listWrapper) {\n      this.convertItemToDefaultBlock(i);\n      return;\n    }\n    const a = U(t);\n    if (a === null)\n      return;\n    const l = this.renderer.renderWrapper(!0);\n    a.forEach((o) => {\n      l.appendChild(o);\n    });\n    const s = this.save(l);\n    s.meta.start = this.data.style == \"ordered\" ? 1 : void 0, this.api.blocks.insert(r == null ? void 0 : r.name, s, this.config, i + 1), this.convertItemToDefaultBlock(i + 1), l.remove();\n  }\n  /**\n   * Method that is used for splitting item content and moving trailing content to the new sibling item\n   * @param currentItem - current item html element\n   */\n  splitItem(t) {\n    const [n, r] = j.getCaretNodeAndOffset();\n    if (n === null)\n      return;\n    const i = N(t);\n    let a;\n    i === null ? a = \"\" : a = j.getContenteditableSlice(i, n, r, \"right\", !0);\n    const l = C(t), s = this.renderItem(a);\n    t == null || t.after(s), l && s.appendChild(l), E(s);\n  }\n  /**\n   * Method that is used for merging current item with previous one\n   * Content of the current item would be appended to the previous item\n   * Current item children would not change nesting level\n   * @param item - current item html element\n   */\n  mergeItemWithPrevious(t) {\n    const n = t.previousElementSibling, r = t.parentNode;\n    if (r === null || !O(r))\n      return;\n    const i = r.closest(`.${h.item}`);\n    if (!n && !i || n && !O(n))\n      return;\n    let a;\n    if (n) {\n      const p = y(n, !1);\n      p.length !== 0 && p.length !== 0 ? a = p[p.length - 1] : a = n;\n    } else\n      a = i;\n    const l = this.renderer.getItemContent(t);\n    if (!a)\n      return;\n    E(a, !1);\n    const s = N(a);\n    if (s === null)\n      return;\n    s.insertAdjacentHTML(\"beforeend\", l);\n    const o = y(t);\n    if (o.length === 0) {\n      t.remove(), z(a);\n      return;\n    }\n    const d = n || i, u = C(d) ?? this.renderer.renderWrapper(!1);\n    n ? o.forEach((p) => {\n      u.appendChild(p);\n    }) : o.forEach((p) => {\n      u.prepend(p);\n    }), C(d) === null && a.appendChild(u), t.remove();\n  }\n  /**\n   * Add indentation to current item\n   * @param event - keydown\n   */\n  addTab(t) {\n    var a;\n    t.stopPropagation(), t.preventDefault();\n    const n = this.currentItem;\n    if (!n)\n      return;\n    if (((a = this.config) == null ? void 0 : a.maxLevel) !== void 0) {\n      const l = this.currentItemLevel;\n      if (l !== null && l === this.config.maxLevel)\n        return;\n    }\n    const r = n.previousSibling;\n    if (r === null || !O(r))\n      return;\n    const i = C(r);\n    if (i)\n      i.appendChild(n), y(n).forEach((s) => {\n        i.appendChild(s);\n      });\n    else {\n      const l = this.renderer.renderWrapper(!1);\n      l.appendChild(n), y(n).forEach((o) => {\n        l.appendChild(o);\n      }), r.appendChild(l);\n    }\n    z(n), E(n, !1);\n  }\n  /**\n   * Convert current item to default block with passed index\n   * @param newBloxkIndex - optional parameter represents index, where would be inseted default block\n   * @param removeList - optional parameter, that represents condition, if List should be removed\n   */\n  convertItemToDefaultBlock(t, n) {\n    let r;\n    const i = this.currentItem, a = i !== null ? this.renderer.getItemContent(i) : \"\";\n    n === !0 && this.api.blocks.delete(), t !== void 0 ? r = this.api.blocks.insert(void 0, { text: a }, void 0, t) : r = this.api.blocks.insert(), i == null || i.remove(), this.api.caret.setToBlock(r, \"start\");\n  }\n  /**\n   * Convert first item of the list to default block\n   * This method could be called when backspace button pressed at start of the first item of the list\n   * First item of the list would be converted to the paragraph and first item children would be unshifted\n   */\n  convertFirstItemToDefaultBlock() {\n    const t = this.currentItem;\n    if (t === null)\n      return;\n    const n = y(t);\n    if (n.length !== 0) {\n      const l = n[0];\n      this.unshiftItem(l), E(t);\n    }\n    const r = U(t), i = this.api.blocks.getCurrentBlockIndex(), a = r === null;\n    this.convertItemToDefaultBlock(i, a);\n  }\n  /**\n   * Method that calls render function of the renderer with a necessary item meta cast\n   * @param itemContent - content to be rendered in new item\n   * @param meta - meta used in list item rendering\n   * @returns html element of the rendered item\n   */\n  renderItem(t, n) {\n    const r = n ?? this.renderer.composeDefaultMeta();\n    switch (!0) {\n      case this.renderer instanceof v:\n        return this.renderer.renderItem(t, r);\n      case this.renderer instanceof b:\n        return this.renderer.renderItem(t, r);\n      default:\n        return this.renderer.renderItem(t, r);\n    }\n  }\n  /**\n   * Renders children list\n   * @param items - list data used in item rendering\n   * @param parentElement - where to append passed items\n   */\n  appendItems(t, n) {\n    t.forEach((r) => {\n      var a;\n      const i = this.renderItem(r.content, r.meta);\n      if (n.appendChild(i), r.items.length) {\n        const l = (a = this.renderer) == null ? void 0 : a.renderWrapper(!1);\n        this.appendItems(r.items, l), i.appendChild(l);\n      }\n    });\n  }\n}\nconst I = {\n  wrapper: `${m}-start-with-field`,\n  input: `${m}-start-with-field__input`,\n  startWithElementWrapperInvalid: `${m}-start-with-field--invalid`\n};\nfunction or(e, { value: t, placeholder: n, attributes: r, sanitize: i }) {\n  const a = c.make(\"div\", I.wrapper), l = c.make(\"input\", I.input, {\n    placeholder: n,\n    /**\n     * Used to prevent focusing on the input by Tab key\n     * (Popover in the Toolbar lays below the blocks,\n     * so Tab in the last block will focus this hidden input if this property is not set)\n     */\n    tabIndex: -1,\n    /**\n     * Value of the start property, if it is not specified, then it is set to one\n     */\n    value: t\n  });\n  for (const s in r)\n    l.setAttribute(s, r[s]);\n  return a.appendChild(l), l.addEventListener(\"input\", () => {\n    i !== void 0 && (l.value = i(l.value));\n    const s = l.checkValidity();\n    !s && !a.classList.contains(I.startWithElementWrapperInvalid) && a.classList.add(I.startWithElementWrapperInvalid), s && a.classList.contains(I.startWithElementWrapperInvalid) && a.classList.remove(I.startWithElementWrapperInvalid), s && e(l.value);\n  }), a;\n}\nconst P = /* @__PURE__ */ new Map([\n  /**\n   * Value that represents default arabic numbers for counters\n   */\n  [\"Numeric\", \"numeric\"],\n  /**\n   * Value that represents lower roman numbers for counteres\n   */\n  [\"Lower Roman\", \"lower-roman\"],\n  /**\n   * Value that represents upper roman numbers for counters\n   */\n  [\"Upper Roman\", \"upper-roman\"],\n  /**\n   * Value that represents lower alpha characters for counters\n   */\n  [\"Lower Alpha\", \"lower-alpha\"],\n  /**\n   * Value that represents upper alpha characters for counters\n   */\n  [\"Upper Alpha\", \"upper-alpha\"]\n]), He = /* @__PURE__ */ new Map([\n  /**\n   * Value that represents Icon for Numeric counter type\n   */\n  [\"numeric\", St],\n  /**\n   * Value that represents Icon for Lower Roman counter type\n   */\n  [\"lower-roman\", Ot],\n  /**\n   * Value that represents Icon for Upper Roman counter type\n   */\n  [\"upper-roman\", kt],\n  /**\n   * Value that represents Icon for Lower Alpha counter type\n   */\n  [\"lower-alpha\", Et],\n  /**\n   * Value that represents Icon for Upper Alpha counter type\n   */\n  [\"upper-alpha\", _t]\n]);\nfunction ur(e) {\n  return e.replace(/\\D+/g, \"\");\n}\nfunction cr(e) {\n  return typeof e.items[0] == \"string\";\n}\nfunction dr(e) {\n  return !(\"meta\" in e);\n}\nfunction fr(e) {\n  return typeof e.items[0] != \"string\" && \"text\" in e.items[0] && \"checked\" in e.items[0] && typeof e.items[0].text == \"string\" && typeof e.items[0].checked == \"boolean\";\n}\nfunction pr(e) {\n  const t = [];\n  return cr(e) ? (e.items.forEach((n) => {\n    t.push({\n      content: n,\n      meta: {},\n      items: []\n    });\n  }), {\n    style: e.style,\n    meta: {},\n    items: t\n  }) : fr(e) ? (e.items.forEach((n) => {\n    t.push({\n      content: n.text,\n      meta: {\n        checked: n.checked\n      },\n      items: []\n    });\n  }), {\n    style: \"checklist\",\n    meta: {},\n    items: t\n  }) : dr(e) ? {\n    style: e.style,\n    meta: {},\n    items: e.items\n  } : e;\n}\nclass G {\n  /**\n   * Notify core that read-only mode is supported\n   */\n  static get isReadOnlySupported() {\n    return !0;\n  }\n  /**\n   * Allow to use native Enter behaviour\n   */\n  static get enableLineBreaks() {\n    return !0;\n  }\n  /**\n   * Get Tool toolbox settings\n   * icon - Tool icon's SVG\n   * title - title to show in toolbox\n   */\n  static get toolbox() {\n    return [\n      {\n        icon: $e,\n        title: \"Unordered List\",\n        data: {\n          style: \"unordered\"\n        }\n      },\n      {\n        icon: Be,\n        title: \"Ordered List\",\n        data: {\n          style: \"ordered\"\n        }\n      },\n      {\n        icon: Ae,\n        title: \"Checklist\",\n        data: {\n          style: \"checklist\"\n        }\n      }\n    ];\n  }\n  /**\n   * On paste sanitzation config. Allow only tags that are allowed in the Tool.\n   * @returns - paste config object used in editor\n   */\n  static get pasteConfig() {\n    return {\n      tags: [\"OL\", \"UL\", \"LI\"]\n    };\n  }\n  /**\n   * Convert from text to list with import and export list to text\n   */\n  static get conversionConfig() {\n    return {\n      export: (t) => G.joinRecursive(t),\n      import: (t, n) => ({\n        meta: {},\n        items: [\n          {\n            content: t,\n            meta: {},\n            items: []\n          }\n        ],\n        style: (n == null ? void 0 : n.defaultStyle) !== void 0 ? n.defaultStyle : \"unordered\"\n      })\n    };\n  }\n  /**\n   * Get list style name\n   */\n  get listStyle() {\n    return this.data.style || this.defaultListStyle;\n  }\n  /**\n   * Set list style\n   * @param style - new style to set\n   */\n  set listStyle(t) {\n    var r;\n    this.data.style = t, this.changeTabulatorByStyle();\n    const n = this.list.render();\n    (r = this.listElement) == null || r.replaceWith(n), this.listElement = n;\n  }\n  /**\n   * Render plugin`s main Element and fill it with saved data\n   * @param params - tool constructor options\n   * @param params.data - previously saved data\n   * @param params.config - user config for Tool\n   * @param params.api - Editor.js API\n   * @param params.readOnly - read-only mode flag\n   */\n  constructor({ data: t, config: n, api: r, readOnly: i, block: a }) {\n    var s;\n    this.api = r, this.readOnly = i, this.config = n, this.block = a, this.defaultListStyle = ((s = this.config) == null ? void 0 : s.defaultStyle) || \"unordered\", this.defaultCounterTypes = this.config.counterTypes || Array.from(P.values());\n    const l = {\n      style: this.defaultListStyle,\n      meta: {},\n      items: []\n    };\n    this.data = Object.keys(t).length ? pr(t) : l, this.listStyle === \"ordered\" && this.data.meta.counterType === void 0 && (this.data.meta.counterType = \"numeric\"), this.changeTabulatorByStyle();\n  }\n  /**\n   * Convert from list to text for conversionConfig\n   * @param data - current data of the list\n   * @returns - string of the recursively merged contents of the items of the list\n   */\n  static joinRecursive(t) {\n    return t.items.map((n) => `${n.content} ${G.joinRecursive(n)}`).join(\"\");\n  }\n  /**\n   * Function that is responsible for content rendering\n   * @returns rendered list wrapper with all contents\n   */\n  render() {\n    return this.listElement = this.list.render(), this.listElement;\n  }\n  /**\n   * Function that is responsible for content saving\n   * @returns formatted content used in editor\n   */\n  save() {\n    return this.data = this.list.save(), this.data;\n  }\n  /**\n   * Function that is responsible for mergind two lists into one\n   * @param data - data of the next standing list, that should be merged with current\n   */\n  merge(t) {\n    this.list.merge(t);\n  }\n  /**\n   * Creates Block Tune allowing to change the list style\n   * @returns array of tune configs\n   */\n  renderSettings() {\n    const t = [\n      {\n        label: this.api.i18n.t(\"Unordered\"),\n        icon: $e,\n        closeOnActivate: !0,\n        isActive: this.listStyle == \"unordered\",\n        onActivate: () => {\n          this.listStyle = \"unordered\";\n        }\n      },\n      {\n        label: this.api.i18n.t(\"Ordered\"),\n        icon: Be,\n        closeOnActivate: !0,\n        isActive: this.listStyle == \"ordered\",\n        onActivate: () => {\n          this.listStyle = \"ordered\";\n        }\n      },\n      {\n        label: this.api.i18n.t(\"Checklist\"),\n        icon: Ae,\n        closeOnActivate: !0,\n        isActive: this.listStyle == \"checklist\",\n        onActivate: () => {\n          this.listStyle = \"checklist\";\n        }\n      }\n    ];\n    if (this.listStyle === \"ordered\") {\n      const n = or(\n        (a) => this.changeStartWith(Number(a)),\n        {\n          value: String(this.data.meta.start ?? 1),\n          placeholder: \"\",\n          attributes: {\n            required: \"true\"\n          },\n          sanitize: (a) => ur(a)\n        }\n      ), r = [\n        {\n          label: this.api.i18n.t(\"Start with\"),\n          icon: It,\n          children: {\n            items: [\n              {\n                element: n,\n                // @ts-expect-error ts(2820) can not use PopoverItem enum from editor.js types\n                type: \"html\"\n              }\n            ]\n          }\n        }\n      ], i = {\n        label: this.api.i18n.t(\"Counter type\"),\n        icon: He.get(this.data.meta.counterType),\n        children: {\n          items: []\n        }\n      };\n      P.forEach((a, l) => {\n        const s = P.get(l);\n        this.defaultCounterTypes.includes(s) && i.children.items.push({\n          title: this.api.i18n.t(l),\n          icon: He.get(s),\n          isActive: this.data.meta.counterType === P.get(l),\n          closeOnActivate: !0,\n          onActivate: () => {\n            this.changeCounters(P.get(l));\n          }\n        });\n      }), i.children.items.length > 1 && r.push(i), t.push({ type: \"separator\" }, ...r);\n    }\n    return t;\n  }\n  /**\n   * On paste callback that is fired from Editor.\n   * @param event - event with pasted data\n   */\n  onPaste(t) {\n    const { tagName: n } = t.detail.data;\n    switch (n) {\n      case \"OL\":\n        this.listStyle = \"ordered\";\n        break;\n      case \"UL\":\n      case \"LI\":\n        this.listStyle = \"unordered\";\n    }\n    this.list.onPaste(t);\n  }\n  /**\n   * Handle UL, OL and LI tags paste and returns List data\n   * @param element - html element that contains whole list\n   */\n  pasteHandler(t) {\n    return this.list.pasteHandler(t);\n  }\n  /**\n   * Changes ordered list counterType property value\n   * @param counterType - new value of the counterType value\n   */\n  changeCounters(t) {\n    var n;\n    (n = this.list) == null || n.changeCounters(t), this.data.meta.counterType = t;\n  }\n  /**\n   * Changes ordered list start property value\n   * @param index - new value of the start property\n   */\n  changeStartWith(t) {\n    var n;\n    (n = this.list) == null || n.changeStartWith(t), this.data.meta.start = t;\n  }\n  /**\n   * This method allows changing tabulator respectfully to passed style\n   */\n  changeTabulatorByStyle() {\n    switch (this.listStyle) {\n      case \"ordered\":\n        this.list = new K(\n          {\n            data: this.data,\n            readOnly: this.readOnly,\n            api: this.api,\n            config: this.config,\n            block: this.block\n          },\n          new v(this.readOnly, this.config)\n        );\n        break;\n      case \"unordered\":\n        this.list = new K(\n          {\n            data: this.data,\n            readOnly: this.readOnly,\n            api: this.api,\n            config: this.config,\n            block: this.block\n          },\n          new b(this.readOnly, this.config)\n        );\n        break;\n      case \"checklist\":\n        this.list = new K(\n          {\n            data: this.data,\n            readOnly: this.readOnly,\n            api: this.api,\n            config: this.config,\n            block: this.block\n          },\n          new f(this.readOnly, this.config)\n        );\n        break;\n    }\n  }\n}\nexport {\n  G as default\n};\n"], "mappings": "AAAA,CAAC,YAAU;EAAC,YAAY;;EAAC,IAAG;IAAC,IAAG,OAAOA,QAAQ,GAAC,GAAG,EAAC;MAAC,IAAIC,CAAC,GAACD,QAAQ,CAACE,aAAa,CAAC,OAAO,CAAC;MAACD,CAAC,CAACE,WAAW,CAACH,QAAQ,CAACI,cAAc,CAAC,o7GAAo7G,CAAC,CAAC,EAACJ,QAAQ,CAACK,IAAI,CAACF,WAAW,CAACF,CAAC,CAAC;IAAA;EAAC,CAAC,QAAMK,CAAC,EAAC;IAACC,OAAO,CAACC,KAAK,CAAC,gCAAgC,EAACF,CAAC,CAAC;EAAA;AAAC,CAAC,EAAE,CAAC;AACrpH,MAAMG,EAAE,GAAG,0PAA0P;EAAEC,EAAE,GAAG,0VAA0V;EAAEC,EAAE,GAAG,uqBAAuqB;EAAEC,EAAE,GAAG,kjBAAkjB;EAAEC,EAAE,GAAG,sUAAsU;EAAEC,EAAE,GAAG,oWAAoW;EAAEC,EAAE,GAAG,kRAAkR;EAAEC,EAAE,GAAG,iYAAiY;EAAEC,EAAE,GAAG,qaAAqa;EAAEC,EAAE,GAAG,8YAA8Y;AACv+H,IAAIC,CAAC,GAAG,OAAOC,UAAU,GAAG,GAAG,GAAGA,UAAU,GAAG,OAAOC,MAAM,GAAG,GAAG,GAAGA,MAAM,GAAG,OAAOC,MAAM,GAAG,GAAG,GAAGA,MAAM,GAAG,OAAOC,IAAI,GAAG,GAAG,GAAGA,IAAI,GAAG,CAAC,CAAC;AAC1I,SAASC,EAAEA,CAACvB,CAAC,EAAE;EACb,IAAIA,CAAC,CAACwB,UAAU,EACd,OAAOxB,CAAC;EACV,IAAIyB,CAAC,GAAGzB,CAAC,CAAC0B,OAAO;EACjB,IAAI,OAAOD,CAAC,IAAI,UAAU,EAAE;IAC1B,IAAIE,CAAC,GAAG,SAASC,CAACA,CAAA,EAAG;MACnB,OAAO,IAAI,YAAYA,CAAC,GAAGC,OAAO,CAACC,SAAS,CAACL,CAAC,EAAEM,SAAS,EAAE,IAAI,CAACC,WAAW,CAAC,GAAGP,CAAC,CAACQ,KAAK,CAAC,IAAI,EAAEF,SAAS,CAAC;IACzG,CAAC;IACDJ,CAAC,CAACO,SAAS,GAAGT,CAAC,CAACS,SAAS;EAC3B,CAAC,MACCP,CAAC,GAAG,CAAC,CAAC;EACR,OAAOQ,MAAM,CAACC,cAAc,CAACT,CAAC,EAAE,YAAY,EAAE;IAAEU,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAEF,MAAM,CAACG,IAAI,CAACtC,CAAC,CAAC,CAACuC,OAAO,CAAC,UAASX,CAAC,EAAE;IAC/F,IAAIY,CAAC,GAAGL,MAAM,CAACM,wBAAwB,CAACzC,CAAC,EAAE4B,CAAC,CAAC;IAC7CO,MAAM,CAACC,cAAc,CAACT,CAAC,EAAEC,CAAC,EAAEY,CAAC,CAACE,GAAG,GAAGF,CAAC,GAAG;MACtCG,UAAU,EAAE,CAAC,CAAC;MACdD,GAAG,EAAE,SAAAA,CAAA,EAAW;QACd,OAAO1C,CAAC,CAAC4B,CAAC,CAAC;MACb;IACF,CAAC,CAAC;EACJ,CAAC,CAAC,EAAED,CAAC;AACP;AACA,IAAItB,CAAC,GAAG,CAAC,CAAC;EAAEuC,CAAC,GAAG,CAAC,CAAC;EAAEC,CAAC,GAAG,CAAC,CAAC;AAC1BV,MAAM,CAACC,cAAc,CAACS,CAAC,EAAE,YAAY,EAAE;EAAER,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACrDQ,CAAC,CAACC,iBAAiB,GAAGC,EAAE;AACxB,SAASA,EAAEA,CAAA,EAAG;EACZ,IAAI/C,CAAC,GAAG,CAAC,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,CAAC;EACvE,OAAO,uDAAuD,GAAGA,CAAC,CAACgD,GAAG,CAAC,UAASvB,CAAC,EAAE;IACjF,OAAO,cAAc,CAACwB,MAAM,CAACxB,CAAC,EAAE,IAAI,CAAC;EACvC,CAAC,CAAC,CAACyB,IAAI,CAAC,IAAI,CAAC;AACf;AACA,CAAC,UAASlD,CAAC,EAAE;EACXmC,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,YAAY,EAAE;IAAEqC,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAErC,CAAC,CAAC8C,iBAAiB,GAAG,KAAK,CAAC;EACnF,IAAIrB,CAAC,GAAGoB,CAAC;EACTV,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,mBAAmB,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAC9E,OAAOjB,CAAC,CAACqB,iBAAiB;IAC5B;EAAE,CAAC,CAAC;AACN,CAAC,EAAEF,CAAC,CAAC;AACL,IAAIO,CAAC,GAAG,CAAC,CAAC;EAAEC,CAAC,GAAG,CAAC,CAAC;AAClBjB,MAAM,CAACC,cAAc,CAACgB,CAAC,EAAE,YAAY,EAAE;EAAEf,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACrDe,CAAC,CAACC,aAAa,GAAGC,EAAE;AACpB,SAASA,EAAEA,CAACtD,CAAC,EAAE;EACb,IAAIyB,CAAC,GAAG,CACN,OAAO,EACP,UAAU,CACX;EACD,OAAOzB,CAAC,IAAIA,CAAC,CAACuD,OAAO,GAAG9B,CAAC,CAAC+B,QAAQ,CAACxD,CAAC,CAACuD,OAAO,CAAC,GAAG,CAAC,CAAC;AACpD;AACA,CAAC,UAASvD,CAAC,EAAE;EACXmC,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,YAAY,EAAE;IAAEqC,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAErC,CAAC,CAACqD,aAAa,GAAG,KAAK,CAAC;EAC/E,IAAI5B,CAAC,GAAG2B,CAAC;EACTjB,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,eAAe,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAC1E,OAAOjB,CAAC,CAAC4B,aAAa;IACxB;EAAE,CAAC,CAAC;AACN,CAAC,EAAEF,CAAC,CAAC;AACL,IAAIM,EAAE,GAAG,CAAC,CAAC;EAAEC,CAAC,GAAG,CAAC,CAAC;AACnBvB,MAAM,CAACC,cAAc,CAACsB,CAAC,EAAE,YAAY,EAAE;EAAErB,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACrDqB,CAAC,CAACC,MAAM,GAAGC,EAAE;AACb,SAASA,EAAEA,CAAC5D,CAAC,EAAEyB,CAAC,EAAE;EAChBoC,KAAK,CAACC,OAAO,CAACrC,CAAC,CAAC,GAAGA,CAAC,CAACc,OAAO,CAAC,UAASZ,CAAC,EAAE;IACvC3B,CAAC,CAACE,WAAW,CAACyB,CAAC,CAAC;EAClB,CAAC,CAAC,GAAG3B,CAAC,CAACE,WAAW,CAACuB,CAAC,CAAC;AACvB;AACA,CAAC,UAASzB,CAAC,EAAE;EACXmC,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,YAAY,EAAE;IAAEqC,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAErC,CAAC,CAAC2D,MAAM,GAAG,KAAK,CAAC;EACxE,IAAIlC,CAAC,GAAGiC,CAAC;EACTvB,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,QAAQ,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACnE,OAAOjB,CAAC,CAACkC,MAAM;IACjB;EAAE,CAAC,CAAC;AACN,CAAC,EAAEF,EAAE,CAAC;AACN,IAAIM,CAAC,GAAG,CAAC,CAAC;EAAEC,CAAC,GAAG,CAAC,CAAC;AAClB7B,MAAM,CAACC,cAAc,CAAC4B,CAAC,EAAE,YAAY,EAAE;EAAE3B,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACrD2B,CAAC,CAACC,aAAa,GAAGC,EAAE;AACpB,SAASA,EAAEA,CAAA,EAAG;EACZ,OAAO,CACL,SAAS,EACT,SAAS,EACT,OAAO,EACP,YAAY,EACZ,QAAQ,EACR,KAAK,EACL,IAAI,EACJ,IAAI,EACJ,UAAU,EACV,YAAY,EACZ,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,QAAQ,EACR,QAAQ,EACR,IAAI,EACJ,IAAI,EACJ,MAAM,EACN,KAAK,EACL,UAAU,EACV,IAAI,EACJ,QAAQ,EACR,GAAG,EACH,KAAK,EACL,MAAM,EACN,SAAS,EACT,OAAO,EACP,OAAO,EACP,OAAO,EACP,IAAI,EACJ,OAAO,EACP,IAAI,EACJ,OAAO,CACR;AACH;AACA,CAAC,UAASlE,CAAC,EAAE;EACXmC,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,YAAY,EAAE;IAAEqC,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAErC,CAAC,CAACiE,aAAa,GAAG,KAAK,CAAC;EAC/E,IAAIxC,CAAC,GAAGuC,CAAC;EACT7B,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,eAAe,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAC1E,OAAOjB,CAAC,CAACwC,aAAa;IACxB;EAAE,CAAC,CAAC;AACN,CAAC,EAAEF,CAAC,CAAC;AACL,IAAII,EAAE,GAAG,CAAC,CAAC;EAAEC,EAAE,GAAG,CAAC,CAAC;AACpBjC,MAAM,CAACC,cAAc,CAACgC,EAAE,EAAE,YAAY,EAAE;EAAE/B,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACtD+B,EAAE,CAACC,iBAAiB,GAAGC,EAAE;AACzB,SAASA,EAAEA,CAACtE,CAAC,EAAE;EACb,IAAIyB,CAAC,GAAGL,MAAM,CAACmD,gBAAgB,CAACvE,CAAC,CAAC;IAAE2B,CAAC,GAAG6C,UAAU,CAAC/C,CAAC,CAACgD,QAAQ,CAAC;IAAE7C,CAAC,GAAG4C,UAAU,CAAC/C,CAAC,CAACiD,UAAU,CAAC,IAAI/C,CAAC,GAAG,GAAG;IAAEa,CAAC,GAAGgC,UAAU,CAAC/C,CAAC,CAACkD,UAAU,CAAC;IAAEC,CAAC,GAAGJ,UAAU,CAAC/C,CAAC,CAACoD,cAAc,CAAC;IAAEC,CAAC,GAAGN,UAAU,CAAC/C,CAAC,CAACsD,SAAS,CAAC;IAAEC,CAAC,GAAGrD,CAAC,GAAG,GAAG;IAAEsD,CAAC,GAAG,CAACrD,CAAC,GAAGD,CAAC,IAAI,CAAC;IAAEuD,CAAC,GAAGJ,CAAC,GAAGF,CAAC,GAAGpC,CAAC,GAAGyC,CAAC,GAAGD,CAAC;EACzP,OAAOE,CAAC;AACV;AACA,CAAC,UAASlF,CAAC,EAAE;EACXmC,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,YAAY,EAAE;IAAEqC,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAErC,CAAC,CAACqE,iBAAiB,GAAG,KAAK,CAAC;EACnF,IAAI5C,CAAC,GAAG2C,EAAE;EACVjC,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,mBAAmB,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAC9E,OAAOjB,CAAC,CAAC4C,iBAAiB;IAC5B;EAAE,CAAC,CAAC;AACN,CAAC,EAAEF,EAAE,CAAC;AACN,IAAIgB,EAAE,GAAG,CAAC,CAAC;EAAEC,EAAE,GAAG,CAAC,CAAC;EAAEC,EAAE,GAAG,CAAC,CAAC;EAAEC,EAAE,GAAG,CAAC,CAAC;AACtCnD,MAAM,CAACC,cAAc,CAACkD,EAAE,EAAE,YAAY,EAAE;EAAEjD,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACtDiD,EAAE,CAACC,iBAAiB,GAAGC,EAAE;AACzB,SAASA,EAAEA,CAACxF,CAAC,EAAE;EACb,OAAOA,CAAC,CAACyF,eAAe,KAAK,MAAM;AACrC;AACA,CAAC,UAASzF,CAAC,EAAE;EACXmC,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,YAAY,EAAE;IAAEqC,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAErC,CAAC,CAACuF,iBAAiB,GAAG,KAAK,CAAC;EACnF,IAAI9D,CAAC,GAAG6D,EAAE;EACVnD,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,mBAAmB,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAC9E,OAAOjB,CAAC,CAAC8D,iBAAiB;IAC5B;EAAE,CAAC,CAAC;AACN,CAAC,EAAEF,EAAE,CAAC;AACNlD,MAAM,CAACC,cAAc,CAACgD,EAAE,EAAE,YAAY,EAAE;EAAE/C,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACtD+C,EAAE,CAACM,WAAW,GAAGC,EAAE;AACnB,IAAIC,EAAE,GAAGzC,CAAC;EAAE0C,EAAE,GAAGR,EAAE;AACnB,SAASM,EAAEA,CAAC3F,CAAC,EAAE;EACb,IAAIyB,CAAC,GAAG,CAAC,CAAC;EACV,IAAI,CAAC,CAAC,EAAEmE,EAAE,CAACvC,aAAa,EAAErD,CAAC,CAAC,EAC1B,QAAQA,CAAC,CAAC8F,IAAI;IACZ,KAAK,MAAM;IACX,KAAK,UAAU;IACf,KAAK,OAAO;IACZ,KAAK,QAAQ;IACb,KAAK,QAAQ;IACb,KAAK,QAAQ;IACb,KAAK,OAAO;IACZ,KAAK,OAAO;MACVrE,CAAC,GAAG,CAAC,CAAC;MACN;EACJ,CAAC,MAEDA,CAAC,GAAG,CAAC,CAAC,EAAEoE,EAAE,CAACN,iBAAiB,EAAEvF,CAAC,CAAC;EAClC,OAAOyB,CAAC;AACV;AACA,CAAC,UAASzB,CAAC,EAAE;EACXmC,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,YAAY,EAAE;IAAEqC,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAErC,CAAC,CAAC0F,WAAW,GAAG,KAAK,CAAC;EAC7E,IAAIjE,CAAC,GAAG2D,EAAE;EACVjD,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,aAAa,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACxE,OAAOjB,CAAC,CAACiE,WAAW;IACtB;EAAE,CAAC,CAAC;AACN,CAAC,EAAEP,EAAE,CAAC;AACN,IAAIY,CAAC,GAAG,CAAC,CAAC;EAAEC,EAAE,GAAG,CAAC,CAAC;AACnB,SAASC,EAAEA,CAACjG,CAAC,EAAEyB,CAAC,EAAEE,CAAC,EAAE;EACnB,MAAMC,CAAC,GAAGD,CAAC,CAACU,KAAK,KAAK,KAAK,CAAC,GAAG,OAAO,GAAG,KAAK;IAAEG,CAAC,GAAGb,CAAC,CAACC,CAAC,CAAC;IAAEgD,CAAC,GAAI,IAAGnD,CAAE,OAAM;EAC1E,IAAIE,CAAC,CAACC,CAAC,CAAC,GAAG,UAAS,GAAGkD,CAAC,EAAE;IACxB,OAAO,IAAI,CAACF,CAAC,CAAC,KAAK,KAAK,CAAC,KAAK,IAAI,CAACA,CAAC,CAAC,GAAGpC,CAAC,CAACP,KAAK,CAAC,IAAI,EAAE6C,CAAC,CAAC,CAAC,EAAE,IAAI,CAACF,CAAC,CAAC;EACpE,CAAC,EAAEhD,CAAC,KAAK,KAAK,IAAID,CAAC,CAACuE,GAAG,EAAE;IACvB,MAAMpB,CAAC,GAAGnD,CAAC,CAACuE,GAAG;IACfvE,CAAC,CAACuE,GAAG,GAAG,UAASlB,CAAC,EAAE;MAClB,OAAOhF,CAAC,CAAC4E,CAAC,CAAC,EAAEE,CAAC,CAAC7C,KAAK,CAAC,IAAI,EAAE+C,CAAC,CAAC;IAC/B,CAAC;EACH;EACA,OAAOrD,CAAC;AACV;AACA,SAASwE,EAAEA,CAAA,EAAG;EACZ,MAAMnG,CAAC,GAAG;MACRoG,GAAG,EAAE,CAAC,CAAC;MACPC,GAAG,EAAE,CAAC,CAAC;MACPC,GAAG,EAAE,CAAC,CAAC;MACPC,KAAK,EAAE,CAAC;IACV,CAAC;IAAE9E,CAAC,GAAGU,MAAM,CAACG,IAAI,CAACtC,CAAC,CAAC,CAACwG,IAAI,CAAE7E,CAAC,IAAKP,MAAM,CAACqF,SAAS,CAACC,UAAU,CAACC,WAAW,CAAC,CAAC,CAACC,OAAO,CAACjF,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;EAC9F,OAAOF,CAAC,KAAK,KAAK,CAAC,KAAKzB,CAAC,CAACyB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAEzB,CAAC;AACvC;AACA,SAAS6G,EAAEA,CAAC7G,CAAC,EAAE;EACb,OAAOA,CAAC,IAAI,IAAI,IAAIA,CAAC,KAAK,EAAE,KAAK,OAAOA,CAAC,IAAI,QAAQ,IAAImC,MAAM,CAACG,IAAI,CAACtC,CAAC,CAAC,CAAC8G,MAAM,GAAG,CAAC,CAAC;AACrF;AACA,SAASC,EAAEA,CAAC/G,CAAC,EAAE;EACb,OAAO,CAAC6G,EAAE,CAAC7G,CAAC,CAAC;AACf;AACA,MAAMgH,EAAE,GAAGA,CAAA,KAAM,OAAO5F,MAAM,GAAG,GAAG,IAAIA,MAAM,CAACqF,SAAS,KAAK,IAAI,IAAII,EAAE,CAACzF,MAAM,CAACqF,SAAS,CAACQ,QAAQ,CAAC,KAAK,gBAAgB,CAACC,IAAI,CAAC9F,MAAM,CAACqF,SAAS,CAACQ,QAAQ,CAAC,IAAI7F,MAAM,CAACqF,SAAS,CAACQ,QAAQ,KAAK,UAAU,IAAI7F,MAAM,CAACqF,SAAS,CAACU,cAAc,GAAG,CAAC,CAAC;AAC3O,SAASC,EAAEA,CAACpH,CAAC,EAAE;EACb,MAAMyB,CAAC,GAAG0E,EAAE,CAAC,CAAC;EACd,OAAOnG,CAAC,GAAGA,CAAC,CAACqH,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,aAAa,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAACA,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE5F,CAAC,CAAC4E,GAAG,GAAGrG,CAAC,GAAGA,CAAC,CAACqH,OAAO,CAAC,YAAY,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,GAAGrH,CAAC,GAAGA,CAAC,CAACqH,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC,CAACA,OAAO,CAAC,WAAW,EAAE,KAAK,CAAC,EAAErH,CAAC;AACvZ;AACA,SAASsH,EAAEA,CAACtH,CAAC,EAAE;EACb,OAAOA,CAAC,CAAC,CAAC,CAAC,CAACuH,WAAW,CAAC,CAAC,GAAGvH,CAAC,CAACwH,KAAK,CAAC,CAAC,CAAC;AACxC;AACA,SAASC,EAAEA,CAACzH,CAAC,EAAE;EACb,MAAMyB,CAAC,GAAG1B,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC;EACvCwB,CAAC,CAACiG,KAAK,CAACC,QAAQ,GAAG,UAAU,EAAElG,CAAC,CAACiG,KAAK,CAACE,IAAI,GAAG,QAAQ,EAAEnG,CAAC,CAACiG,KAAK,CAACG,MAAM,GAAG,QAAQ,EAAEpG,CAAC,CAACqG,SAAS,GAAG9H,CAAC,EAAED,QAAQ,CAACgI,IAAI,CAAC7H,WAAW,CAACuB,CAAC,CAAC;EAChI,MAAME,CAAC,GAAGP,MAAM,CAAC4G,YAAY,CAAC,CAAC;IAAEpG,CAAC,GAAG7B,QAAQ,CAACkI,WAAW,CAAC,CAAC;EAC3D,IAAIrG,CAAC,CAACsG,UAAU,CAACzG,CAAC,CAAC,EAAEE,CAAC,KAAK,IAAI,EAC7B,MAAM,IAAIwG,KAAK,CAAC,+BAA+B,CAAC;EAClDxG,CAAC,CAACyG,eAAe,CAAC,CAAC,EAAEzG,CAAC,CAAC0G,QAAQ,CAACzG,CAAC,CAAC,EAAE7B,QAAQ,CAACuI,WAAW,CAAC,MAAM,CAAC,EAAEvI,QAAQ,CAACgI,IAAI,CAACQ,WAAW,CAAC9G,CAAC,CAAC;AAChG;AACA,SAAS+G,EAAEA,CAACxI,CAAC,EAAEyB,CAAC,EAAEE,CAAC,EAAE;EACnB,IAAIC,CAAC;EACL,OAAO,CAAC,GAAGY,CAAC,KAAK;IACf,MAAMoC,CAAC,GAAG,IAAI;MAAEE,CAAC,GAAGA,CAAA,KAAM;QACxBlD,CAAC,GAAG,KAAK,CAAC,EAAED,CAAC,KAAK,CAAC,CAAC,IAAI3B,CAAC,CAACiC,KAAK,CAAC2C,CAAC,EAAEpC,CAAC,CAAC;MACvC,CAAC;MAAEwC,CAAC,GAAGrD,CAAC,KAAK,CAAC,CAAC,IAAIC,CAAC,KAAK,KAAK,CAAC;IAC/BR,MAAM,CAACqH,YAAY,CAAC7G,CAAC,CAAC,EAAEA,CAAC,GAAGR,MAAM,CAACsH,UAAU,CAAC5D,CAAC,EAAErD,CAAC,CAAC,EAAEuD,CAAC,IAAIhF,CAAC,CAACiC,KAAK,CAAC2C,CAAC,EAAEpC,CAAC,CAAC;EACzE,CAAC;AACH;AACA,SAASmG,CAACA,CAAC3I,CAAC,EAAE;EACZ,OAAOmC,MAAM,CAACD,SAAS,CAAC0G,QAAQ,CAACC,IAAI,CAAC7I,CAAC,CAAC,CAAC8I,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAACnC,WAAW,CAAC,CAAC;AAClF;AACA,SAASoC,EAAEA,CAAC/I,CAAC,EAAE;EACb,OAAO2I,CAAC,CAAC3I,CAAC,CAAC,KAAK,SAAS;AAC3B;AACA,SAASgJ,EAAEA,CAAChJ,CAAC,EAAE;EACb,OAAO2I,CAAC,CAAC3I,CAAC,CAAC,KAAK,UAAU,IAAI2I,CAAC,CAAC3I,CAAC,CAAC,KAAK,eAAe;AACxD;AACA,SAASiJ,EAAEA,CAACjJ,CAAC,EAAE;EACb,OAAOgJ,EAAE,CAAChJ,CAAC,CAAC,IAAI,cAAc,CAACkH,IAAI,CAAClH,CAAC,CAAC4I,QAAQ,CAAC,CAAC,CAAC;AACnD;AACA,SAASM,EAAEA,CAAClJ,CAAC,EAAE;EACb,OAAO2I,CAAC,CAAC3I,CAAC,CAAC,KAAK,QAAQ;AAC1B;AACA,SAASmJ,CAACA,CAACnJ,CAAC,EAAE;EACZ,OAAO2I,CAAC,CAAC3I,CAAC,CAAC,KAAK,QAAQ;AAC1B;AACA,SAASoJ,EAAEA,CAACpJ,CAAC,EAAE;EACb,OAAOqJ,OAAO,CAACC,OAAO,CAACtJ,CAAC,CAAC,KAAKA,CAAC;AACjC;AACA,SAASuJ,EAAEA,CAACvJ,CAAC,EAAE;EACb,OAAO2I,CAAC,CAAC3I,CAAC,CAAC,KAAK,QAAQ;AAC1B;AACA,SAASwJ,EAAEA,CAACxJ,CAAC,EAAE;EACb,OAAO2I,CAAC,CAAC3I,CAAC,CAAC,KAAK,WAAW;AAC7B;AACA,SAASyJ,CAACA,CAACzJ,CAAC,EAAE,GAAGyB,CAAC,EAAE;EAClB,IAAI,CAACA,CAAC,CAACqF,MAAM,EACX,OAAO9G,CAAC;EACV,MAAM2B,CAAC,GAAGF,CAAC,CAACiI,KAAK,CAAC,CAAC;EACnB,IAAIP,CAAC,CAACnJ,CAAC,CAAC,IAAImJ,CAAC,CAACxH,CAAC,CAAC,EACd,KAAK,MAAMC,CAAC,IAAID,CAAC,EACfwH,CAAC,CAACxH,CAAC,CAACC,CAAC,CAAC,CAAC,IAAI5B,CAAC,CAAC4B,CAAC,CAAC,KAAK,KAAK,CAAC,IAAIO,MAAM,CAACwH,MAAM,CAAC3J,CAAC,EAAE;IAAE,CAAC4B,CAAC,GAAG,CAAC;EAAE,CAAC,CAAC,EAAE6H,CAAC,CAACzJ,CAAC,CAAC4B,CAAC,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,CAAC,IAAIO,MAAM,CAACwH,MAAM,CAAC3J,CAAC,EAAE;IAAE,CAAC4B,CAAC,GAAGD,CAAC,CAACC,CAAC;EAAE,CAAC,CAAC;EACjH,OAAO6H,CAAC,CAACzJ,CAAC,EAAE,GAAGyB,CAAC,CAAC;AACnB;AACA,SAASmI,EAAEA,CAAC5J,CAAC,EAAEyB,CAAC,EAAEE,CAAC,EAAE;EACnB,MAAMC,CAAC,GAAI,IAAGH,CAAE,kFAAiFE,CAAE,YAAW;EAC9G3B,CAAC,IAAIM,OAAO,CAACuJ,IAAI,CAACjI,CAAC,CAAC;AACtB;AACA,SAASkI,EAAEA,CAAC9J,CAAC,EAAE;EACb,IAAI;IACF,OAAO,IAAI+J,GAAG,CAAC/J,CAAC,CAAC,CAACgK,IAAI;EACxB,CAAC,CAAC,MAAM,CACR;EACA,OAAOhK,CAAC,CAACiK,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,IAAI,GAAG7I,MAAM,CAAC8I,QAAQ,CAACC,QAAQ,GAAGnK,CAAC,GAAGoB,MAAM,CAAC8I,QAAQ,CAACE,MAAM,GAAGpK,CAAC;AAC/F;AACA,SAASqK,EAAEA,CAACrK,CAAC,EAAE;EACb,OAAOA,CAAC,GAAG,EAAE,IAAIA,CAAC,GAAG,EAAE,IAAIA,CAAC,KAAK,EAAE,IAAIA,CAAC,KAAK,EAAE,IAAIA,CAAC,KAAK,GAAG,IAAIA,CAAC,GAAG,EAAE,IAAIA,CAAC,GAAG,EAAE,IAAIA,CAAC,GAAG,EAAE,IAAIA,CAAC,GAAG,GAAG,IAAIA,CAAC,GAAG,GAAG,IAAIA,CAAC,GAAG,GAAG,IAAIA,CAAC,GAAG,GAAG,IAAIA,CAAC,GAAG,GAAG;AACnJ;AACA,MAAMsK,EAAE,GAAG;IACTC,SAAS,EAAE,CAAC;IACZC,GAAG,EAAE,CAAC;IACNC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,EAAE;IACTC,IAAI,EAAE,EAAE;IACRC,GAAG,EAAE,EAAE;IACPC,GAAG,EAAE,EAAE;IACPC,KAAK,EAAE,EAAE;IACTC,IAAI,EAAE,EAAE;IACRC,EAAE,EAAE,EAAE;IACNC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE;EACT,CAAC;EAAEC,EAAE,GAAG;IACNP,IAAI,EAAE,CAAC;IACPQ,KAAK,EAAE,CAAC;IACRL,KAAK,EAAE,CAAC;IACRM,QAAQ,EAAE,CAAC;IACXC,OAAO,EAAE;EACX,CAAC;AACD,MAAMC,EAAE,CAAC;EACP1J,WAAWA,CAAA,EAAG;IACZ,IAAI,CAAC2J,SAAS,GAAGtC,OAAO,CAACC,OAAO,CAAC,CAAC;EACpC;EACA;AACF;AACA;AACA;EACEsC,GAAGA,CAACnK,CAAC,EAAE;IACL,OAAO,IAAI4H,OAAO,CAAC,CAAC1H,CAAC,EAAEC,CAAC,KAAK;MAC3B,IAAI,CAAC+J,SAAS,GAAG,IAAI,CAACA,SAAS,CAACE,IAAI,CAACpK,CAAC,CAAC,CAACoK,IAAI,CAAClK,CAAC,CAAC,CAACmK,KAAK,CAAClK,CAAC,CAAC;IAC1D,CAAC,CAAC;EACJ;AACF;AACA,SAASmK,EAAEA,CAAC/L,CAAC,EAAEyB,CAAC,EAAEE,CAAC,GAAG,KAAK,CAAC,EAAE;EAC5B,IAAIC,CAAC;IAAEY,CAAC;IAAEoC,CAAC;IAAEE,CAAC,GAAG,IAAI;IAAEE,CAAC,GAAG,CAAC;EAC5BrD,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC,CAAC;EACb,MAAMsD,CAAC,GAAG,SAAAA,CAAA,EAAW;IACnBD,CAAC,GAAGrD,CAAC,CAACqK,OAAO,KAAK,CAAC,CAAC,GAAG,CAAC,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEpH,CAAC,GAAG,IAAI,EAAEF,CAAC,GAAG5E,CAAC,CAACiC,KAAK,CAACL,CAAC,EAAEY,CAAC,CAAC,EAAEsC,CAAC,KAAK,IAAI,KAAKlD,CAAC,GAAGY,CAAC,GAAG,IAAI,CAAC;EAClG,CAAC;EACD,OAAO,YAAW;IAChB,MAAM0C,CAAC,GAAG+G,IAAI,CAACC,GAAG,CAAC,CAAC;IACpB,CAAClH,CAAC,IAAIrD,CAAC,CAACqK,OAAO,KAAK,CAAC,CAAC,KAAKhH,CAAC,GAAGE,CAAC,CAAC;IACjC,MAAMiH,CAAC,GAAG1K,CAAC,IAAIyD,CAAC,GAAGF,CAAC,CAAC;IACrB,OAAOpD,CAAC,GAAG,IAAI,EAAEY,CAAC,GAAGT,SAAS,EAAEoK,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAG1K,CAAC,IAAIqD,CAAC,KAAK2D,YAAY,CAAC3D,CAAC,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC,EAAEE,CAAC,GAAGE,CAAC,EAAEN,CAAC,GAAG5E,CAAC,CAACiC,KAAK,CAACL,CAAC,EAAEY,CAAC,CAAC,EAAEsC,CAAC,KAAK,IAAI,KAAKlD,CAAC,GAAGY,CAAC,GAAG,IAAI,CAAC,IAAI,CAACsC,CAAC,IAAInD,CAAC,CAACyK,QAAQ,KAAK,CAAC,CAAC,KAAKtH,CAAC,GAAG4D,UAAU,CAACzD,CAAC,EAAEkH,CAAC,CAAC,CAAC,EAAEvH,CAAC;EACrM,CAAC;AACH;AACA,MAAMyH,EAAE,GAAG,eAAgBlK,MAAM,CAACmK,MAAM,EAAC,eAAgBnK,MAAM,CAACC,cAAc,CAAC;IAC7EmK,SAAS,EAAE,IAAI;IACfC,YAAY,EAAEd,EAAE;IAChBe,gBAAgB,EAAErF,EAAE;IACpBsF,SAAS,EAAEzG,EAAE;IACb0G,UAAU,EAAErF,EAAE;IACdsF,mBAAmB,EAAEnF,EAAE;IACvBoF,QAAQ,EAAErE,EAAE;IACZsE,SAAS,EAAErD,CAAC;IACZsD,iBAAiB,EAAEnD,EAAE;IACrBoD,SAAS,EAAE7G,EAAE;IACb8G,WAAW,EAAEnD,EAAE;IACfoD,SAAS,EAAEnE,EAAE;IACboE,OAAO,EAAElE,EAAE;IACXmE,OAAO,EAAErG,EAAE;IACXsG,UAAU,EAAErE,EAAE;IACdsE,WAAW,EAAEtG,EAAE;IACfuG,QAAQ,EAAErE,EAAE;IACZsE,QAAQ,EAAErE,CAAC;IACXsE,cAAc,EAAEpD,EAAE;IAClBqD,SAAS,EAAEtE,EAAE;IACbuE,QAAQ,EAAEpE,EAAE;IACZqE,WAAW,EAAEpE,EAAE;IACfqE,QAAQ,EAAEvD,EAAE;IACZwD,YAAY,EAAExC,EAAE;IAChByC,QAAQ,EAAElH,EAAE;IACZmH,QAAQ,EAAEjC,EAAE;IACZkC,MAAM,EAAEtF;EACV,CAAC,EAAEuF,MAAM,CAACC,WAAW,EAAE;IAAE9L,KAAK,EAAE;EAAS,CAAC,CAAC,CAAC;EAAE+L,EAAE,GAAG,eAAgB7M,EAAE,CAAC8K,EAAE,CAAC;AACzElK,MAAM,CAACC,cAAc,CAAC4D,EAAE,EAAE,YAAY,EAAE;EAAE3D,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACtD2D,EAAE,CAACqI,0BAA0B,GAAGC,EAAE;AAClC,IAAIC,EAAE,GAAGH,EAAE;EAAEI,EAAE,GAAGzK,CAAC;AACnB,SAASuK,EAAEA,CAACtO,CAAC,EAAE;EACb,IAAIyB,CAAC;EACL,CAAC,CAAC,EAAE8M,EAAE,CAACZ,QAAQ,EAAE3N,CAAC,CAAC,IAAIyB,CAAC,GAAG1B,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC,EAAEwB,CAAC,CAACqG,SAAS,GAAG9H,CAAC,IAAIyB,CAAC,GAAGzB,CAAC;EAClF,IAAI2B,CAAC,GAAG,SAAAA,CAASC,CAAC,EAAE;IAClB,OAAO,CAAC,CAAC,CAAC,EAAE4M,EAAE,CAACvK,aAAa,EAAE,CAAC,CAACT,QAAQ,CAAC5B,CAAC,CAAC2B,OAAO,CAACoD,WAAW,CAAC,CAAC,CAAC,IAAI9C,KAAK,CAAC4K,IAAI,CAAC7M,CAAC,CAAC8M,QAAQ,CAAC,CAACC,KAAK,CAAChN,CAAC,CAAC;EACtG,CAAC;EACD,OAAOkC,KAAK,CAAC4K,IAAI,CAAChN,CAAC,CAACiN,QAAQ,CAAC,CAACC,KAAK,CAAChN,CAAC,CAAC;AACxC;AACA,CAAC,UAAS3B,CAAC,EAAE;EACXmC,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,YAAY,EAAE;IAAEqC,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAErC,CAAC,CAACqO,0BAA0B,GAAG,KAAK,CAAC;EAC5F,IAAI5M,CAAC,GAAGuE,EAAE;EACV7D,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,4BAA4B,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACvF,OAAOjB,CAAC,CAAC4M,0BAA0B;IACrC;EAAE,CAAC,CAAC;AACN,CAAC,EAAEtI,CAAC,CAAC;AACL,IAAI6I,EAAE,GAAG,CAAC,CAAC;EAAEC,EAAE,GAAG,CAAC,CAAC;EAAEC,CAAC,GAAG,CAAC,CAAC;EAAEC,EAAE,GAAG,CAAC,CAAC;AACrC5M,MAAM,CAACC,cAAc,CAAC2M,EAAE,EAAE,YAAY,EAAE;EAAE1M,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACtD0M,EAAE,CAACC,IAAI,GAAGC,EAAE;AACZ,SAASA,EAAEA,CAACjP,CAAC,EAAEyB,CAAC,EAAEE,CAAC,EAAE;EACnB,IAAIC,CAAC;EACLH,CAAC,KAAK,KAAK,CAAC,KAAKA,CAAC,GAAG,IAAI,CAAC,EAAEE,CAAC,KAAK,KAAK,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC,CAAC;EACpD,IAAIa,CAAC,GAAGzC,QAAQ,CAACE,aAAa,CAACD,CAAC,CAAC;EACjC,IAAI6D,KAAK,CAACC,OAAO,CAACrC,CAAC,CAAC,EAAE;IACpB,IAAImD,CAAC,GAAGnD,CAAC,CAACyN,MAAM,CAAC,UAASlK,CAAC,EAAE;MAC3B,OAAOA,CAAC,KAAK,KAAK,CAAC;IACrB,CAAC,CAAC;IACF,CAACpD,CAAC,GAAGY,CAAC,CAAC2M,SAAS,EAAEvD,GAAG,CAAC3J,KAAK,CAACL,CAAC,EAAEgD,CAAC,CAAC;EACnC,CAAC,MACCnD,CAAC,KAAK,IAAI,IAAIe,CAAC,CAAC2M,SAAS,CAACvD,GAAG,CAACnK,CAAC,CAAC;EAClC,KAAK,IAAIqD,CAAC,IAAInD,CAAC,EACbQ,MAAM,CAACD,SAAS,CAACkN,cAAc,CAACvG,IAAI,CAAClH,CAAC,EAAEmD,CAAC,CAAC,KAAKtC,CAAC,CAACsC,CAAC,CAAC,GAAGnD,CAAC,CAACmD,CAAC,CAAC,CAAC;EAC7D,OAAOtC,CAAC;AACV;AACA,CAAC,UAASxC,CAAC,EAAE;EACXmC,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,YAAY,EAAE;IAAEqC,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAErC,CAAC,CAACgP,IAAI,GAAG,KAAK,CAAC;EACtE,IAAIvN,CAAC,GAAGsN,EAAE;EACV5M,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,MAAM,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACjE,OAAOjB,CAAC,CAACuN,IAAI;IACf;EAAE,CAAC,CAAC;AACN,CAAC,EAAEF,CAAC,CAAC;AACL3M,MAAM,CAACC,cAAc,CAACyM,EAAE,EAAE,YAAY,EAAE;EAAExM,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACtDwM,EAAE,CAACQ,gBAAgB,GAAGC,EAAE;AACxB,IAAIC,EAAE,GAAGT,CAAC;AACV,SAASQ,EAAEA,CAACtP,CAAC,EAAE;EACb,IAAIyB,CAAC,GAAG,CAAC,CAAC,EAAE8N,EAAE,CAACP,IAAI,EAAE,KAAK,CAAC;EAC3B,OAAOvN,CAAC,CAACvB,WAAW,CAACF,CAAC,CAAC,EAAEyB,CAAC,CAACqG,SAAS;AACtC;AACA,CAAC,UAAS9H,CAAC,EAAE;EACXmC,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,YAAY,EAAE;IAAEqC,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAErC,CAAC,CAACqP,gBAAgB,GAAG,KAAK,CAAC;EAClF,IAAI5N,CAAC,GAAGoN,EAAE;EACV1M,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,kBAAkB,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAC7E,OAAOjB,CAAC,CAAC4N,gBAAgB;IAC3B;EAAE,CAAC,CAAC;AACN,CAAC,EAAET,EAAE,CAAC;AACN,IAAIY,EAAE,GAAG,CAAC,CAAC;EAAEC,EAAE,GAAG,CAAC,CAAC;AACpBtN,MAAM,CAACC,cAAc,CAACqN,EAAE,EAAE,YAAY,EAAE;EAAEpN,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACtDoN,EAAE,CAACC,gBAAgB,GAAGC,EAAE;AACxB,IAAIC,EAAE,GAAGzM,CAAC;AACV,SAASwM,EAAEA,CAAC3P,CAAC,EAAE;EACb,IAAIyB,CAAC,EAAEE,CAAC;EACR,OAAO,CAAC,CAAC,EAAEiO,EAAE,CAACvM,aAAa,EAAErD,CAAC,CAAC,GAAGA,CAAC,CAACqC,KAAK,CAACyE,MAAM,GAAG9G,CAAC,CAAC6P,QAAQ,KAAKC,IAAI,CAACC,SAAS,GAAG/P,CAAC,CAAC8G,MAAM,GAAG,CAACnF,CAAC,GAAG,CAACF,CAAC,GAAGzB,CAAC,CAACgQ,WAAW,MAAM,IAAI,IAAIvO,CAAC,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,CAAC,CAACqF,MAAM,MAAM,IAAI,IAAInF,CAAC,KAAK,KAAK,CAAC,GAAGA,CAAC,GAAG,CAAC;AACvM;AACA,CAAC,UAAS3B,CAAC,EAAE;EACXmC,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,YAAY,EAAE;IAAEqC,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAErC,CAAC,CAAC0P,gBAAgB,GAAG,KAAK,CAAC;EAClF,IAAIjO,CAAC,GAAGgO,EAAE;EACVtN,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,kBAAkB,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAC7E,OAAOjB,CAAC,CAACiO,gBAAgB;IAC3B;EAAE,CAAC,CAAC;AACN,CAAC,EAAEF,EAAE,CAAC;AACN,IAAIS,EAAE,GAAG,CAAC,CAAC;EAAEC,EAAE,GAAG,CAAC,CAAC;EAAEC,EAAE,GAAGjP,CAAC,IAAIA,CAAC,CAACkP,aAAa,IAAI,UAASpQ,CAAC,EAAEyB,CAAC,EAAEE,CAAC,EAAE;IACnE,IAAIA,CAAC,IAAII,SAAS,CAAC+E,MAAM,KAAK,CAAC,EAC7B,KAAK,IAAIlF,CAAC,GAAG,CAAC,EAAEY,CAAC,GAAGf,CAAC,CAACqF,MAAM,EAAElC,CAAC,EAAEhD,CAAC,GAAGY,CAAC,EAAEZ,CAAC,EAAE,EACzC,CAACgD,CAAC,IAAI,EAAEhD,CAAC,IAAIH,CAAC,CAAC,MAAMmD,CAAC,KAAKA,CAAC,GAAGf,KAAK,CAAC3B,SAAS,CAACsF,KAAK,CAACqB,IAAI,CAACpH,CAAC,EAAE,CAAC,EAAEG,CAAC,CAAC,CAAC,EAAEgD,CAAC,CAAChD,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC,CAAC;IACrF,OAAO5B,CAAC,CAACiD,MAAM,CAAC2B,CAAC,IAAIf,KAAK,CAAC3B,SAAS,CAACsF,KAAK,CAACqB,IAAI,CAACpH,CAAC,CAAC,CAAC;EACrD,CAAC;AACDU,MAAM,CAACC,cAAc,CAAC8N,EAAE,EAAE,YAAY,EAAE;EAAE7N,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACtD6N,EAAE,CAACG,uBAAuB,GAAGC,EAAE;AAC/B,IAAIC,EAAE,GAAGxK,CAAC;AACV,SAASuK,EAAEA,CAACtQ,CAAC,EAAE;EACb,OAAO,CAAC,CAAC,EAAEuQ,EAAE,CAAClC,0BAA0B,EAAErO,CAAC,CAAC,GAAG,CAACA,CAAC,CAAC,GAAG6D,KAAK,CAAC4K,IAAI,CAACzO,CAAC,CAAC0O,QAAQ,CAAC,CAAC8B,MAAM,CAAC,UAAS/O,CAAC,EAAEE,CAAC,EAAE;IAChG,OAAOwO,EAAE,CAACA,EAAE,CAAC,EAAE,EAAE1O,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE6O,EAAE,CAAC3O,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACrC,CAAC,EAAE,EAAE,CAAC;AACR;AACA,CAAC,UAAS3B,CAAC,EAAE;EACXmC,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,YAAY,EAAE;IAAEqC,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAErC,CAAC,CAACqQ,uBAAuB,GAAG,KAAK,CAAC;EACzF,IAAI5O,CAAC,GAAGyO,EAAE;EACV/N,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,yBAAyB,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACpF,OAAOjB,CAAC,CAAC4O,uBAAuB;IAClC;EAAE,CAAC,CAAC;AACN,CAAC,EAAEJ,EAAE,CAAC;AACN,IAAIQ,EAAE,GAAG,CAAC,CAAC;EAAEC,EAAE,GAAG,CAAC,CAAC;EAAEC,CAAC,GAAG,CAAC,CAAC;EAAEC,EAAE,GAAG,CAAC,CAAC;AACrCzO,MAAM,CAACC,cAAc,CAACwO,EAAE,EAAE,YAAY,EAAE;EAAEvO,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACtDuO,EAAE,CAACC,cAAc,GAAGC,EAAE;AACtB,SAASA,EAAEA,CAAC9Q,CAAC,EAAE;EACb,OAAO,CACL,IAAI,EACJ,KAAK,CACN,CAACwD,QAAQ,CAACxD,CAAC,CAACuD,OAAO,CAAC;AACvB;AACA,CAAC,UAASvD,CAAC,EAAE;EACXmC,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,YAAY,EAAE;IAAEqC,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAErC,CAAC,CAAC6Q,cAAc,GAAG,KAAK,CAAC;EAChF,IAAIpP,CAAC,GAAGmP,EAAE;EACVzO,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,gBAAgB,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAC3E,OAAOjB,CAAC,CAACoP,cAAc;IACzB;EAAE,CAAC,CAAC;AACN,CAAC,EAAEF,CAAC,CAAC;AACL,IAAII,CAAC,GAAG,CAAC,CAAC;EAAEC,EAAE,GAAG,CAAC,CAAC;AACnB7O,MAAM,CAACC,cAAc,CAAC4O,EAAE,EAAE,YAAY,EAAE;EAAE3O,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACtD2O,EAAE,CAACC,WAAW,GAAGC,EAAE;AACnB,SAASA,EAAEA,CAAClR,CAAC,EAAE;EACb,OAAO,CACL,MAAM,EACN,MAAM,EACN,IAAI,EACJ,KAAK,EACL,SAAS,EACT,OAAO,EACP,IAAI,EACJ,KAAK,EACL,OAAO,EACP,QAAQ,EACR,MAAM,EACN,MAAM,EACN,OAAO,EACP,QAAQ,EACR,OAAO,EACP,KAAK,CACN,CAACwD,QAAQ,CAACxD,CAAC,CAACuD,OAAO,CAAC;AACvB;AACA,CAAC,UAASvD,CAAC,EAAE;EACXmC,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,YAAY,EAAE;IAAEqC,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAErC,CAAC,CAACiR,WAAW,GAAG,KAAK,CAAC;EAC7E,IAAIxP,CAAC,GAAGuP,EAAE;EACV7O,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,aAAa,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACxE,OAAOjB,CAAC,CAACwP,WAAW;IACtB;EAAE,CAAC,CAAC;AACN,CAAC,EAAEF,CAAC,CAAC;AACL5O,MAAM,CAACC,cAAc,CAACsO,EAAE,EAAE,YAAY,EAAE;EAAErO,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACtDqO,EAAE,CAACS,cAAc,GAAGC,EAAE;AACtB,IAAIC,EAAE,GAAGlO,CAAC;EAAEmO,EAAE,GAAGX,CAAC;EAAEY,EAAE,GAAGR,CAAC;AAC1B,SAASK,EAAEA,CAACpR,CAAC,EAAEyB,CAAC,EAAE;EAChBA,CAAC,KAAK,KAAK,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC,CAAC;EACxB,IAAIE,CAAC,GAAGF,CAAC,GAAG,WAAW,GAAG,YAAY;IAAEG,CAAC,GAAGH,CAAC,GAAG,iBAAiB,GAAG,aAAa;EACjF,IAAIzB,CAAC,CAAC6P,QAAQ,KAAKC,IAAI,CAAC0B,YAAY,IAAIxR,CAAC,CAAC2B,CAAC,CAAC,EAAE;IAC5C,IAAIa,CAAC,GAAGxC,CAAC,CAAC2B,CAAC,CAAC;IACZ,IAAI,CAAC,CAAC,EAAE4P,EAAE,CAACN,WAAW,EAAEzO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE6O,EAAE,CAAChO,aAAa,EAAEb,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE8O,EAAE,CAACT,cAAc,EAAErO,CAAC,CAAC,EACnF,IAAIA,CAAC,CAACZ,CAAC,CAAC,EACNY,CAAC,GAAGA,CAAC,CAACZ,CAAC,CAAC,CAAC,KACN,IAAIY,CAAC,CAACiP,UAAU,KAAK,IAAI,IAAIjP,CAAC,CAACiP,UAAU,CAAC7P,CAAC,CAAC,EAC/CY,CAAC,GAAGA,CAAC,CAACiP,UAAU,CAAC7P,CAAC,CAAC,CAAC,KAEpB,OAAOY,CAAC,CAACiP,UAAU;IACvB,OAAOL,EAAE,CAAC5O,CAAC,EAAEf,CAAC,CAAC;EACjB;EACA,OAAOzB,CAAC;AACV;AACA,CAAC,UAASA,CAAC,EAAE;EACXmC,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,YAAY,EAAE;IAAEqC,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAErC,CAAC,CAACmR,cAAc,GAAG,KAAK,CAAC;EAChF,IAAI1P,CAAC,GAAGiP,EAAE;EACVvO,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,gBAAgB,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAC3E,OAAOjB,CAAC,CAAC0P,cAAc;IACzB;EAAE,CAAC,CAAC;AACN,CAAC,EAAEV,EAAE,CAAC;AACN,IAAIiB,EAAE,GAAG,CAAC,CAAC;EAAEC,EAAE,GAAG,CAAC,CAAC;EAAEC,CAAC,GAAG1Q,CAAC,IAAIA,CAAC,CAACkP,aAAa,IAAI,UAASpQ,CAAC,EAAEyB,CAAC,EAAEE,CAAC,EAAE;IAClE,IAAIA,CAAC,IAAII,SAAS,CAAC+E,MAAM,KAAK,CAAC,EAC7B,KAAK,IAAIlF,CAAC,GAAG,CAAC,EAAEY,CAAC,GAAGf,CAAC,CAACqF,MAAM,EAAElC,CAAC,EAAEhD,CAAC,GAAGY,CAAC,EAAEZ,CAAC,EAAE,EACzC,CAACgD,CAAC,IAAI,EAAEhD,CAAC,IAAIH,CAAC,CAAC,MAAMmD,CAAC,KAAKA,CAAC,GAAGf,KAAK,CAAC3B,SAAS,CAACsF,KAAK,CAACqB,IAAI,CAACpH,CAAC,EAAE,CAAC,EAAEG,CAAC,CAAC,CAAC,EAAEgD,CAAC,CAAChD,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC,CAAC;IACrF,OAAO5B,CAAC,CAACiD,MAAM,CAAC2B,CAAC,IAAIf,KAAK,CAAC3B,SAAS,CAACsF,KAAK,CAACqB,IAAI,CAACpH,CAAC,CAAC,CAAC;EACrD,CAAC;AACDU,MAAM,CAACC,cAAc,CAACuP,EAAE,EAAE,YAAY,EAAE;EAAEtP,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACtDsP,EAAE,CAACE,aAAa,GAAGC,EAAE;AACrB,IAAIC,EAAE,GAAGhM,CAAC;EAAEiM,EAAE,GAAG/B,EAAE;EAAEgC,EAAE,GAAGrP,CAAC;EAAEsP,EAAE,GAAG/O,CAAC;AACnC,SAAS2O,EAAEA,CAAC9R,CAAC,EAAE;EACb,OAAO6D,KAAK,CAAC4K,IAAI,CAACzO,CAAC,CAACmS,gBAAgB,CAAC,CAAC,CAAC,EAAEF,EAAE,CAACnP,iBAAiB,EAAE,CAAC,CAAC,CAAC,CAAC0N,MAAM,CAAC,UAAS/O,CAAC,EAAEE,CAAC,EAAE;IACvF,OAAO,CAAC,CAAC,EAAEuQ,EAAE,CAAC7O,aAAa,EAAE1B,CAAC,CAAC,IAAI,CAAC,CAAC,EAAEoQ,EAAE,CAAC1D,0BAA0B,EAAE1M,CAAC,CAAC,GAAGiQ,CAAC,CAACA,CAAC,CAAC,EAAE,EAAEnQ,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAACE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGiQ,CAAC,CAACA,CAAC,CAAC,EAAE,EAAEnQ,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAEuQ,EAAE,CAAC3B,uBAAuB,EAAE1O,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAC/J,CAAC,EAAE,EAAE,CAAC;AACR;AACA,CAAC,UAAS3B,CAAC,EAAE;EACXmC,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,YAAY,EAAE;IAAEqC,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAErC,CAAC,CAAC6R,aAAa,GAAG,KAAK,CAAC;EAC/E,IAAIpQ,CAAC,GAAGkQ,EAAE;EACVxP,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,eAAe,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAC1E,OAAOjB,CAAC,CAACoQ,aAAa;IACxB;EAAE,CAAC,CAAC;AACN,CAAC,EAAEH,EAAE,CAAC;AACN,IAAIU,EAAE,GAAG,CAAC,CAAC;EAAEC,EAAE,GAAG,CAAC,CAAC;AACpBlQ,MAAM,CAACC,cAAc,CAACiQ,EAAE,EAAE,YAAY,EAAE;EAAEhQ,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACtDgQ,EAAE,CAACC,sBAAsB,GAAGC,EAAE;AAC9B,SAASA,EAAEA,CAACvS,CAAC,EAAE;EACb,OAAO,CAAC,YAAY,CAACkH,IAAI,CAAClH,CAAC,CAAC;AAC9B;AACA,CAAC,UAASA,CAAC,EAAE;EACXmC,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,YAAY,EAAE;IAAEqC,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAErC,CAAC,CAACsS,sBAAsB,GAAG,KAAK,CAAC;EACxF,IAAI7Q,CAAC,GAAG4Q,EAAE;EACVlQ,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,wBAAwB,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACnF,OAAOjB,CAAC,CAAC6Q,sBAAsB;IACjC;EAAE,CAAC,CAAC;AACN,CAAC,EAAEF,EAAE,CAAC;AACN,IAAII,EAAE,GAAG,CAAC,CAAC;EAAEC,EAAE,GAAG,CAAC,CAAC;AACpBtQ,MAAM,CAACC,cAAc,CAACqQ,EAAE,EAAE,YAAY,EAAE;EAAEpQ,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACtDoQ,EAAE,CAACC,SAAS,GAAGC,EAAE;AACjB,IAAIC,EAAE,GAAGxE,EAAE;AACX,SAASuE,EAAEA,CAAC3S,CAAC,EAAE;EACb,OAAO,CAAC,CAAC,EAAE4S,EAAE,CAACrF,QAAQ,EAAEvN,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAACA,CAAC,IAAI,CAAC,CAACA,CAAC,CAAC6P,QAAQ,IAAI7P,CAAC,CAAC6P,QAAQ,KAAKC,IAAI,CAAC0B,YAAY;AAC3F;AACA,CAAC,UAASxR,CAAC,EAAE;EACXmC,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,YAAY,EAAE;IAAEqC,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAErC,CAAC,CAAC0S,SAAS,GAAG,KAAK,CAAC;EAC3E,IAAIjR,CAAC,GAAGgR,EAAE;EACVtQ,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,WAAW,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACtE,OAAOjB,CAAC,CAACiR,SAAS;IACpB;EAAE,CAAC,CAAC;AACN,CAAC,EAAEF,EAAE,CAAC;AACN,IAAIK,EAAE,GAAG,CAAC,CAAC;EAAEC,EAAE,GAAG,CAAC,CAAC;EAAEC,EAAE,GAAG,CAAC,CAAC;EAAEC,EAAE,GAAG,CAAC,CAAC;AACtC7Q,MAAM,CAACC,cAAc,CAAC4Q,EAAE,EAAE,YAAY,EAAE;EAAE3Q,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACtD2Q,EAAE,CAACC,MAAM,GAAGC,EAAE;AACd,SAASA,EAAEA,CAAClT,CAAC,EAAE;EACb,OAAOA,CAAC,KAAK,IAAI,GAAG,CAAC,CAAC,GAAGA,CAAC,CAACmT,UAAU,CAACrM,MAAM,KAAK,CAAC;AACpD;AACA,CAAC,UAAS9G,CAAC,EAAE;EACXmC,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,YAAY,EAAE;IAAEqC,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAErC,CAAC,CAACiT,MAAM,GAAG,KAAK,CAAC;EACxE,IAAIxR,CAAC,GAAGuR,EAAE;EACV7Q,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,QAAQ,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACnE,OAAOjB,CAAC,CAACwR,MAAM;IACjB;EAAE,CAAC,CAAC;AACN,CAAC,EAAEF,EAAE,CAAC;AACN,IAAIK,EAAE,GAAG,CAAC,CAAC;EAAEC,EAAE,GAAG,CAAC,CAAC;AACpBlR,MAAM,CAACC,cAAc,CAACiR,EAAE,EAAE,YAAY,EAAE;EAAEhR,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACtDgR,EAAE,CAACC,WAAW,GAAGC,EAAE;AACnB,IAAIC,EAAE,GAAG7C,CAAC;EAAE8C,EAAE,GAAGjB,EAAE;EAAEkB,EAAE,GAAGvQ,CAAC;EAAEwQ,EAAE,GAAG5C,CAAC;AACnC,SAASwC,EAAEA,CAACvT,CAAC,EAAEyB,CAAC,EAAE;EAChB,IAAIE,CAAC,GAAG,EAAE;EACV,OAAO,CAAC,CAAC,EAAEgS,EAAE,CAAC1C,WAAW,EAAEjR,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAEwT,EAAE,CAAC3C,cAAc,EAAE7Q,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAEyT,EAAE,CAACf,SAAS,EAAE1S,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE0T,EAAE,CAACrQ,aAAa,EAAErD,CAAC,CAAC,GAAG2B,CAAC,GAAG3B,CAAC,CAACqC,KAAK,GAAGrC,CAAC,CAACgQ,WAAW,KAAK,IAAI,KAAKrO,CAAC,GAAG3B,CAAC,CAACgQ,WAAW,CAAC3I,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,EAAE5F,CAAC,KAAK,KAAK,CAAC,KAAKE,CAAC,GAAGA,CAAC,CAAC0F,OAAO,CAAC,IAAIuM,MAAM,CAACnS,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,EAAEE,CAAC,CAACkS,IAAI,CAAC,CAAC,CAAC/M,MAAM,KAAK,CAAC,CAAC;AACtR;AACA,CAAC,UAAS9G,CAAC,EAAE;EACXmC,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,YAAY,EAAE;IAAEqC,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAErC,CAAC,CAACsT,WAAW,GAAG,KAAK,CAAC;EAC7E,IAAI7R,CAAC,GAAG4R,EAAE;EACVlR,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,aAAa,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACxE,OAAOjB,CAAC,CAAC6R,WAAW;IACtB;EAAE,CAAC,CAAC;AACN,CAAC,EAAEF,EAAE,CAAC;AACNjR,MAAM,CAACC,cAAc,CAAC0Q,EAAE,EAAE,YAAY,EAAE;EAAEzQ,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACtDyQ,EAAE,CAAC1F,OAAO,GAAG0G,EAAE;AACf,IAAIC,EAAE,GAAGhB,EAAE;EAAEiB,EAAE,GAAGZ,EAAE;AACpB,SAASU,EAAEA,CAAC9T,CAAC,EAAEyB,CAAC,EAAE;EAChBzB,CAAC,CAACiU,SAAS,CAAC,CAAC;EACb,KAAK,IAAItS,CAAC,GAAG,CAAC3B,CAAC,CAAC,EAAE2B,CAAC,CAACmF,MAAM,GAAG,CAAC,GAAI;IAChC,IAAIlF,CAAC,GAAGD,CAAC,CAAC+H,KAAK,CAAC,CAAC;IACjB,IAAI9H,CAAC,EAAE;MACL,IAAI5B,CAAC,GAAG4B,CAAC,EAAE,CAAC,CAAC,EAAEmS,EAAE,CAACd,MAAM,EAAEjT,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAEgU,EAAE,CAACV,WAAW,EAAEtT,CAAC,EAAEyB,CAAC,CAAC,EACxD,OAAO,CAAC,CAAC;MACXE,CAAC,CAACuS,IAAI,CAACjS,KAAK,CAACN,CAAC,EAAEkC,KAAK,CAAC4K,IAAI,CAACzO,CAAC,CAACmT,UAAU,CAAC,CAAC;IAC3C;EACF;EACA,OAAO,CAAC,CAAC;AACX;AACA,CAAC,UAASnT,CAAC,EAAE;EACXmC,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,YAAY,EAAE;IAAEqC,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAErC,CAAC,CAACoN,OAAO,GAAG,KAAK,CAAC;EACzE,IAAI3L,CAAC,GAAGqR,EAAE;EACV3Q,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,SAAS,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACpE,OAAOjB,CAAC,CAAC2L,OAAO;IAClB;EAAE,CAAC,CAAC;AACN,CAAC,EAAEyF,EAAE,CAAC;AACN,IAAIsB,EAAE,GAAG,CAAC,CAAC;EAAEC,EAAE,GAAG,CAAC,CAAC;AACpBjS,MAAM,CAACC,cAAc,CAACgS,EAAE,EAAE,YAAY,EAAE;EAAE/R,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACtD+R,EAAE,CAACC,UAAU,GAAGC,EAAE;AAClB,IAAIC,EAAE,GAAGnG,EAAE;AACX,SAASkG,EAAEA,CAACtU,CAAC,EAAE;EACb,OAAO,CAAC,CAAC,EAAEuU,EAAE,CAAChH,QAAQ,EAAEvN,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAACA,CAAC,IAAI,CAAC,CAACA,CAAC,CAAC6P,QAAQ,IAAI7P,CAAC,CAAC6P,QAAQ,KAAKC,IAAI,CAAC0E,sBAAsB;AACrG;AACA,CAAC,UAASxU,CAAC,EAAE;EACXmC,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,YAAY,EAAE;IAAEqC,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAErC,CAAC,CAACqU,UAAU,GAAG,KAAK,CAAC;EAC5E,IAAI5S,CAAC,GAAG2S,EAAE;EACVjS,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,YAAY,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACvE,OAAOjB,CAAC,CAAC4S,UAAU;IACrB;EAAE,CAAC,CAAC;AACN,CAAC,EAAEF,EAAE,CAAC;AACN,IAAIM,EAAE,GAAG,CAAC,CAAC;EAAEC,EAAE,GAAG,CAAC,CAAC;AACpBvS,MAAM,CAACC,cAAc,CAACsS,EAAE,EAAE,YAAY,EAAE;EAAErS,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACtDqS,EAAE,CAACC,YAAY,GAAGC,EAAE;AACpB,IAAIC,EAAE,GAAG/F,CAAC;AACV,SAAS8F,EAAEA,CAAC5U,CAAC,EAAE;EACb,IAAIyB,CAAC,GAAG,CAAC,CAAC,EAAEoT,EAAE,CAAC7F,IAAI,EAAE,KAAK,CAAC;EAC3B,OAAOvN,CAAC,CAACqG,SAAS,GAAG9H,CAAC,EAAEyB,CAAC,CAACqT,iBAAiB,GAAG,CAAC;AACjD;AACA,CAAC,UAAS9U,CAAC,EAAE;EACXmC,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,YAAY,EAAE;IAAEqC,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAErC,CAAC,CAAC2U,YAAY,GAAG,KAAK,CAAC;EAC9E,IAAIlT,CAAC,GAAGiT,EAAE;EACVvS,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,cAAc,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACzE,OAAOjB,CAAC,CAACkT,YAAY;IACvB;EAAE,CAAC,CAAC;AACN,CAAC,EAAEF,EAAE,CAAC;AACN,IAAIM,EAAE,GAAG,CAAC,CAAC;EAAEC,EAAE,GAAG,CAAC,CAAC;AACpB7S,MAAM,CAACC,cAAc,CAAC4S,EAAE,EAAE,YAAY,EAAE;EAAE3S,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACtD2S,EAAE,CAACC,MAAM,GAAGC,EAAE;AACd,SAASA,EAAEA,CAAClV,CAAC,EAAE;EACb,IAAIyB,CAAC,GAAGzB,CAAC,CAACmV,qBAAqB,CAAC,CAAC;IAAExT,CAAC,GAAGP,MAAM,CAACgU,WAAW,IAAIrV,QAAQ,CAACsV,eAAe,CAACC,UAAU;IAAE1T,CAAC,GAAGR,MAAM,CAACmU,WAAW,IAAIxV,QAAQ,CAACsV,eAAe,CAACG,SAAS;IAAEhT,CAAC,GAAGf,CAAC,CAACgU,GAAG,GAAG7T,CAAC;IAAEgD,CAAC,GAAGnD,CAAC,CAACmG,IAAI,GAAGjG,CAAC;EAC7L,OAAO;IACL8T,GAAG,EAAEjT,CAAC;IACNoF,IAAI,EAAEhD,CAAC;IACPiD,MAAM,EAAErF,CAAC,GAAGf,CAAC,CAACiU,MAAM;IACpBC,KAAK,EAAE/Q,CAAC,GAAGnD,CAAC,CAACmU;EACf,CAAC;AACH;AACA,CAAC,UAAS5V,CAAC,EAAE;EACXmC,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,YAAY,EAAE;IAAEqC,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAErC,CAAC,CAACiV,MAAM,GAAG,KAAK,CAAC;EACxE,IAAIxT,CAAC,GAAGuT,EAAE;EACV7S,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,QAAQ,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACnE,OAAOjB,CAAC,CAACwT,MAAM;IACjB;EAAE,CAAC,CAAC;AACN,CAAC,EAAEF,EAAE,CAAC;AACN,IAAIc,EAAE,GAAG,CAAC,CAAC;EAAEC,EAAE,GAAG,CAAC,CAAC;AACpB3T,MAAM,CAACC,cAAc,CAAC0T,EAAE,EAAE,YAAY,EAAE;EAAEzT,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACtDyT,EAAE,CAACC,OAAO,GAAGC,EAAE;AACf,SAASA,EAAEA,CAAChW,CAAC,EAAEyB,CAAC,EAAE;EAChBoC,KAAK,CAACC,OAAO,CAACrC,CAAC,CAAC,IAAIA,CAAC,GAAGA,CAAC,CAACwU,OAAO,CAAC,CAAC,EAAExU,CAAC,CAACc,OAAO,CAAC,UAASZ,CAAC,EAAE;IACzD,OAAO3B,CAAC,CAAC+V,OAAO,CAACpU,CAAC,CAAC;EACrB,CAAC,CAAC,IAAI3B,CAAC,CAAC+V,OAAO,CAACtU,CAAC,CAAC;AACpB;AACA,CAAC,UAASzB,CAAC,EAAE;EACXmC,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,YAAY,EAAE;IAAEqC,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAErC,CAAC,CAAC+V,OAAO,GAAG,KAAK,CAAC;EACzE,IAAItU,CAAC,GAAGqU,EAAE;EACV3T,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,SAAS,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACpE,OAAOjB,CAAC,CAACsU,OAAO;IAClB;EAAE,CAAC,CAAC;AACN,CAAC,EAAEF,EAAE,CAAC;AACN,CAAC,UAAS7V,CAAC,EAAE;EACXmC,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,YAAY,EAAE;IAAEqC,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAErC,CAAC,CAAC+V,OAAO,GAAG/V,CAAC,CAACiV,MAAM,GAAGjV,CAAC,CAACgP,IAAI,GAAGhP,CAAC,CAAC6Q,cAAc,GAAG7Q,CAAC,CAACiR,WAAW,GAAGjR,CAAC,CAACsT,WAAW,GAAGtT,CAAC,CAACiT,MAAM,GAAGjT,CAAC,CAAC2U,YAAY,GAAG3U,CAAC,CAACqU,UAAU,GAAGrU,CAAC,CAACoN,OAAO,GAAGpN,CAAC,CAAC0S,SAAS,GAAG1S,CAAC,CAACuF,iBAAiB,GAAGvF,CAAC,CAACsS,sBAAsB,GAAGtS,CAAC,CAAC6R,aAAa,GAAG7R,CAAC,CAACqD,aAAa,GAAGrD,CAAC,CAAC8C,iBAAiB,GAAG9C,CAAC,CAACmR,cAAc,GAAGnR,CAAC,CAACqQ,uBAAuB,GAAGrQ,CAAC,CAAC0P,gBAAgB,GAAG1P,CAAC,CAACqP,gBAAgB,GAAGrP,CAAC,CAACqO,0BAA0B,GAAGrO,CAAC,CAAC0F,WAAW,GAAG1F,CAAC,CAACqE,iBAAiB,GAAGrE,CAAC,CAACiE,aAAa,GAAGjE,CAAC,CAAC2D,MAAM,GAAG,KAAK,CAAC;EAC3f,IAAIlC,CAAC,GAAGmB,CAAC;EACTT,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,mBAAmB,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAC9E,OAAOjB,CAAC,CAACqB,iBAAiB;IAC5B;EAAE,CAAC,CAAC;EACJ,IAAInB,CAAC,GAAGwB,CAAC;EACThB,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,eAAe,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAC1E,OAAOf,CAAC,CAAC0B,aAAa;IACxB;EAAE,CAAC,CAAC;EACJ,IAAIzB,CAAC,GAAG6B,EAAE;EACVtB,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,QAAQ,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACnE,OAAOd,CAAC,CAAC+B,MAAM;IACjB;EAAE,CAAC,CAAC;EACJ,IAAInB,CAAC,GAAGuB,CAAC;EACT5B,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,eAAe,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAC1E,OAAOF,CAAC,CAACyB,aAAa;IACxB;EAAE,CAAC,CAAC;EACJ,IAAIW,CAAC,GAAGT,EAAE;EACVhC,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,mBAAmB,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAC9E,OAAOkC,CAAC,CAACP,iBAAiB;IAC5B;EAAE,CAAC,CAAC;EACJ,IAAIS,CAAC,GAAGK,EAAE;EACVhD,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,aAAa,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACxE,OAAOoC,CAAC,CAACY,WAAW;IACtB;EAAE,CAAC,CAAC;EACJ,IAAIV,CAAC,GAAGe,CAAC;EACT5D,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,4BAA4B,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACvF,OAAOsC,CAAC,CAACqJ,0BAA0B;IACrC;EAAE,CAAC,CAAC;EACJ,IAAIpJ,CAAC,GAAG2J,EAAE;EACVzM,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,kBAAkB,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAC7E,OAAOuC,CAAC,CAACoK,gBAAgB;IAC3B;EAAE,CAAC,CAAC;EACJ,IAAInK,CAAC,GAAGsK,EAAE;EACVrN,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,kBAAkB,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAC7E,OAAOwC,CAAC,CAACwK,gBAAgB;IAC3B;EAAE,CAAC,CAAC;EACJ,IAAIvD,CAAC,GAAG8D,EAAE;EACV9N,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,yBAAyB,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACpF,OAAOyJ,CAAC,CAACkE,uBAAuB;IAClC;EAAE,CAAC,CAAC;EACJ,IAAI6F,CAAC,GAAGzF,EAAE;EACVtO,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,gBAAgB,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAC3E,OAAOwT,CAAC,CAAC/E,cAAc;IACzB;EAAE,CAAC,CAAC;EACJ,IAAIgF,CAAC,GAAGzE,EAAE;EACVvP,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,eAAe,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAC1E,OAAOyT,CAAC,CAACtE,aAAa;IACxB;EAAE,CAAC,CAAC;EACJ,IAAIuE,CAAC,GAAGhE,EAAE;EACVjQ,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,wBAAwB,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACnF,OAAO0T,CAAC,CAAC9D,sBAAsB;IACjC;EAAE,CAAC,CAAC;EACJ,IAAI+D,CAAC,GAAGhR,EAAE;EACVlD,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,mBAAmB,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAC9E,OAAO2T,CAAC,CAAC9Q,iBAAiB;IAC5B;EAAE,CAAC,CAAC;EACJ,IAAI+Q,EAAE,GAAG9D,EAAE;EACXrQ,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,WAAW,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACtE,OAAO4T,EAAE,CAAC5D,SAAS;IACrB;EAAE,CAAC,CAAC;EACJ,IAAI6D,EAAE,GAAG1D,EAAE;EACX1Q,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,SAAS,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACpE,OAAO6T,EAAE,CAACnJ,OAAO;IACnB;EAAE,CAAC,CAAC;EACJ,IAAIoJ,EAAE,GAAGrC,EAAE;EACXhS,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,YAAY,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACvE,OAAO8T,EAAE,CAACnC,UAAU;IACtB;EAAE,CAAC,CAAC;EACJ,IAAIoC,EAAE,GAAGhC,EAAE;EACXtS,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,cAAc,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACzE,OAAO+T,EAAE,CAAC9B,YAAY;IACxB;EAAE,CAAC,CAAC;EACJ,IAAI+B,EAAE,GAAG3D,EAAE;EACX5Q,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,QAAQ,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACnE,OAAOgU,EAAE,CAACzD,MAAM;IAClB;EAAE,CAAC,CAAC;EACJ,IAAI0D,EAAE,GAAGvD,EAAE;EACXjR,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,aAAa,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACxE,OAAOiU,EAAE,CAACrD,WAAW;IACvB;EAAE,CAAC,CAAC;EACJ,IAAIsD,EAAE,GAAGjG,CAAC;EACVxO,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,gBAAgB,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAC3E,OAAOkU,EAAE,CAAC/F,cAAc;IAC1B;EAAE,CAAC,CAAC;EACJ,IAAIgG,EAAE,GAAG9F,CAAC;EACV5O,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,aAAa,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACxE,OAAOmU,EAAE,CAAC5F,WAAW;IACvB;EAAE,CAAC,CAAC;EACJ,IAAI6F,EAAE,GAAGhI,CAAC;EACV3M,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,MAAM,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACjE,OAAOoU,EAAE,CAAC9H,IAAI;IAChB;EAAE,CAAC,CAAC;EACJ,IAAI+H,EAAE,GAAGhC,EAAE;EACX5S,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,QAAQ,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACnE,OAAOqU,EAAE,CAAC9B,MAAM;IAClB;EAAE,CAAC,CAAC;EACJ,IAAI+B,EAAE,GAAGnB,EAAE;EACX1T,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,SAAS,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACpE,OAAOsU,EAAE,CAACjB,OAAO;IACnB;EAAE,CAAC,CAAC;AACN,CAAC,EAAE1V,CAAC,CAAC;AACL,MAAM4W,CAAC,GAAG,UAAU;EAAEC,CAAC,GAAG;IACxBC,OAAO,EAAEF,CAAC;IACVG,IAAI,EAAG,GAAEH,CAAE,QAAO;IAClBI,WAAW,EAAG,GAAEJ,CAAE,gBAAe;IACjCK,YAAY,EAAG,GAAEL,CAAE;EACrB,CAAC;AACD,MAAMM,CAAC,CAAC;EACN;AACF;AACA;EACE,WAAWC,GAAGA,CAAA,EAAG;IACf,OAAO;MACL,GAAGN,CAAC;MACJO,WAAW,EAAG,GAAER,CAAE;IACpB,CAAC;EACH;EACA;AACF;AACA;AACA;AACA;EACEjV,WAAWA,CAACP,CAAC,EAAEE,CAAC,EAAE;IAChB,IAAI,CAAC+V,MAAM,GAAG/V,CAAC,EAAE,IAAI,CAACgW,QAAQ,GAAGlW,CAAC;EACpC;EACA;AACF;AACA;AACA;AACA;EACEmW,aAAaA,CAACnW,CAAC,EAAE;IACf,IAAIE,CAAC;IACL,OAAOF,CAAC,KAAK,CAAC,CAAC,GAAGE,CAAC,GAAGtB,CAAC,CAAC2O,IAAI,CAAC,IAAI,EAAE,CAACuI,CAAC,CAACC,GAAG,CAACL,OAAO,EAAEI,CAAC,CAACC,GAAG,CAACC,WAAW,CAAC,CAAC,GAAG9V,CAAC,GAAGtB,CAAC,CAAC2O,IAAI,CAAC,IAAI,EAAE,CAACuI,CAAC,CAACC,GAAG,CAACC,WAAW,EAAEF,CAAC,CAACC,GAAG,CAACF,YAAY,CAAC,CAAC,EAAE3V,CAAC;EACvI;EACA;AACF;AACA;AACA;AACA;AACA;EACEkW,UAAUA,CAACpW,CAAC,EAAEE,CAAC,EAAE;IACf,MAAMC,CAAC,GAAGvB,CAAC,CAAC2O,IAAI,CAAC,IAAI,EAAEuI,CAAC,CAACC,GAAG,CAACJ,IAAI,CAAC;MAAE5U,CAAC,GAAGnC,CAAC,CAAC2O,IAAI,CAAC,KAAK,EAAEuI,CAAC,CAACC,GAAG,CAACH,WAAW,EAAE;QACvEvP,SAAS,EAAErG,CAAC;QACZgE,eAAe,EAAE,CAAC,CAAC,IAAI,CAACkS,QAAQ,EAAE/O,QAAQ,CAAC;MAC7C,CAAC,CAAC;IACF,OAAOhH,CAAC,CAAC1B,WAAW,CAACsC,CAAC,CAAC,EAAEZ,CAAC;EAC5B;EACA;AACF;AACA;AACA;AACA;EACEkW,cAAcA,CAACrW,CAAC,EAAE;IAChB,MAAME,CAAC,GAAGF,CAAC,CAACsW,aAAa,CAAE,IAAGR,CAAC,CAACC,GAAG,CAACH,WAAY,EAAC,CAAC;IAClD,OAAO,CAAC1V,CAAC,IAAItB,CAAC,CAAC+M,OAAO,CAACzL,CAAC,CAAC,GAAG,EAAE,GAAGA,CAAC,CAACmG,SAAS;EAC9C;EACA;AACF;AACA;AACA;EACEkQ,WAAWA,CAAA,EAAG;IACZ,OAAO,CAAC,CAAC;EACX;EACA;AACF;AACA;EACEC,kBAAkBA,CAAA,EAAG;IACnB,OAAO,CAAC,CAAC;EACX;AACF;AACA,MAAMC,CAAC,CAAC;EACN;AACF;AACA;EACE,WAAWV,GAAGA,CAAA,EAAG;IACf,OAAO;MACL,GAAGN,CAAC;MACJiB,aAAa,EAAG,GAAElB,CAAE;IACtB,CAAC;EACH;EACA;AACF;AACA;AACA;AACA;EACEjV,WAAWA,CAACP,CAAC,EAAEE,CAAC,EAAE;IAChB,IAAI,CAAC+V,MAAM,GAAG/V,CAAC,EAAE,IAAI,CAACgW,QAAQ,GAAGlW,CAAC;EACpC;EACA;AACF;AACA;AACA;AACA;EACEmW,aAAaA,CAACnW,CAAC,EAAE;IACf,IAAIE,CAAC;IACL,OAAOF,CAAC,KAAK,CAAC,CAAC,GAAGE,CAAC,GAAGtB,CAAC,CAAC2O,IAAI,CAAC,IAAI,EAAE,CAACkJ,CAAC,CAACV,GAAG,CAACL,OAAO,EAAEe,CAAC,CAACV,GAAG,CAACW,aAAa,CAAC,CAAC,GAAGxW,CAAC,GAAGtB,CAAC,CAAC2O,IAAI,CAAC,IAAI,EAAE,CAACkJ,CAAC,CAACV,GAAG,CAACW,aAAa,EAAED,CAAC,CAACV,GAAG,CAACF,YAAY,CAAC,CAAC,EAAE3V,CAAC;EAC3I;EACA;AACF;AACA;AACA;AACA;AACA;EACEkW,UAAUA,CAACpW,CAAC,EAAEE,CAAC,EAAE;IACf,MAAMC,CAAC,GAAGvB,CAAC,CAAC2O,IAAI,CAAC,IAAI,EAAEkJ,CAAC,CAACV,GAAG,CAACJ,IAAI,CAAC;MAAE5U,CAAC,GAAGnC,CAAC,CAAC2O,IAAI,CAAC,KAAK,EAAEkJ,CAAC,CAACV,GAAG,CAACH,WAAW,EAAE;QACvEvP,SAAS,EAAErG,CAAC;QACZgE,eAAe,EAAE,CAAC,CAAC,IAAI,CAACkS,QAAQ,EAAE/O,QAAQ,CAAC;MAC7C,CAAC,CAAC;IACF,OAAOhH,CAAC,CAAC1B,WAAW,CAACsC,CAAC,CAAC,EAAEZ,CAAC;EAC5B;EACA;AACF;AACA;AACA;AACA;EACEkW,cAAcA,CAACrW,CAAC,EAAE;IAChB,MAAME,CAAC,GAAGF,CAAC,CAACsW,aAAa,CAAE,IAAGG,CAAC,CAACV,GAAG,CAACH,WAAY,EAAC,CAAC;IAClD,OAAO,CAAC1V,CAAC,IAAItB,CAAC,CAAC+M,OAAO,CAACzL,CAAC,CAAC,GAAG,EAAE,GAAGA,CAAC,CAACmG,SAAS;EAC9C;EACA;AACF;AACA;AACA;EACEkQ,WAAWA,CAAA,EAAG;IACZ,OAAO,CAAC,CAAC;EACX;EACA;AACF;AACA;EACEC,kBAAkBA,CAAA,EAAG;IACnB,OAAO,CAAC,CAAC;EACX;AACF;AACA,SAASG,CAACA,CAACpY,CAAC,EAAE;EACZ,OAAOA,CAAC,CAAC6P,QAAQ,KAAKC,IAAI,CAAC0B,YAAY;AACzC;AACA,IAAI6G,CAAC,GAAG,CAAC,CAAC;EAAEC,EAAE,GAAG,CAAC,CAAC;EAAEC,CAAC,GAAG,CAAC,CAAC;EAAEC,CAAC,GAAG,CAAC,CAAC;AACnCrW,MAAM,CAACC,cAAc,CAACoW,CAAC,EAAE,YAAY,EAAE;EAAEnW,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACrDmW,CAAC,CAACC,uBAAuB,GAAGC,EAAE;AAC9B,IAAIC,EAAE,GAAGtY,CAAC;AACV,SAASqY,EAAEA,CAAC1Y,CAAC,EAAEyB,CAAC,EAAEE,CAAC,EAAEC,CAAC,EAAEY,CAAC,EAAE;EACzB,IAAIoC,CAAC;EACLpC,CAAC,KAAK,KAAK,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC,CAAC;EACxB,IAAIsC,CAAC,GAAG/E,QAAQ,CAACkI,WAAW,CAAC,CAAC;EAC9B,IAAIrG,CAAC,KAAK,MAAM,IAAIkD,CAAC,CAAC8T,QAAQ,CAAC5Y,CAAC,EAAE,CAAC,CAAC,EAAE8E,CAAC,CAAC+T,MAAM,CAACpX,CAAC,EAAEE,CAAC,CAAC,KAAKmD,CAAC,CAAC8T,QAAQ,CAACnX,CAAC,EAAEE,CAAC,CAAC,EAAEmD,CAAC,CAAC+T,MAAM,CAAC7Y,CAAC,EAAEA,CAAC,CAACmT,UAAU,CAACrM,MAAM,CAAC,CAAC,EAAEtE,CAAC,KAAK,CAAC,CAAC,EAAE;IACtH,IAAIwC,CAAC,GAAGF,CAAC,CAACgU,eAAe,CAAC,CAAC;IAC3B,OAAO,CAAC,CAAC,EAAEH,EAAE,CAACtJ,gBAAgB,EAAErK,CAAC,CAAC;EACpC;EACA,IAAIC,CAAC,GAAGH,CAAC,CAACiU,aAAa,CAAC,CAAC;IAAE7T,CAAC,GAAGnF,QAAQ,CAACE,aAAa,CAAC,KAAK,CAAC;EAC5DiF,CAAC,CAAChF,WAAW,CAAC+E,CAAC,CAAC;EAChB,IAAIkH,CAAC,GAAG,CAACvH,CAAC,GAAGM,CAAC,CAAC8K,WAAW,MAAM,IAAI,IAAIpL,CAAC,KAAK,KAAK,CAAC,GAAGA,CAAC,GAAG,EAAE;EAC7D,OAAOuH,CAAC;AACV;AACAhK,MAAM,CAACC,cAAc,CAACmW,CAAC,EAAE,YAAY,EAAE;EAAElW,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACrDkW,CAAC,CAACS,qCAAqC,GAAGC,EAAE;AAC5C,IAAIC,EAAE,GAAG7Y,CAAC;EAAE8Y,EAAE,GAAGX,CAAC;AAClB,SAASS,EAAEA,CAACjZ,CAAC,EAAEyB,CAAC,EAAEE,CAAC,EAAEC,CAAC,EAAE;EACtB,IAAIY,CAAC,GAAG,CAAC,CAAC,EAAE2W,EAAE,CAACV,uBAAuB,EAAEzY,CAAC,EAAEyB,CAAC,EAAEE,CAAC,EAAEC,CAAC,CAAC;EACnD,OAAO,CAAC,CAAC,EAAEsX,EAAE,CAAC5G,sBAAsB,EAAE9P,CAAC,CAAC;AAC1C;AACA,CAAC,UAASxC,CAAC,EAAE;EACXmC,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,YAAY,EAAE;IAAEqC,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAErC,CAAC,CAACgZ,qCAAqC,GAAG,KAAK,CAAC;EACvG,IAAIvX,CAAC,GAAG8W,CAAC;EACTpW,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,uCAAuC,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAClG,OAAOjB,CAAC,CAACuX,qCAAqC;IAChD;EAAE,CAAC,CAAC;AACN,CAAC,EAAEV,EAAE,CAAC;AACN,IAAIc,EAAE,GAAG,CAAC,CAAC;AACX,CAAC,UAASpZ,CAAC,EAAE;EACXmC,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,YAAY,EAAE;IAAEqC,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAErC,CAAC,CAACyY,uBAAuB,GAAG,KAAK,CAAC;EACzF,IAAIhX,CAAC,GAAG+W,CAAC;EACTrW,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,yBAAyB,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACpF,OAAOjB,CAAC,CAACgX,uBAAuB;IAClC;EAAE,CAAC,CAAC;AACN,CAAC,EAAEW,EAAE,CAAC;AACN,IAAIC,EAAE,GAAG,CAAC,CAAC;EAAEC,EAAE,GAAG,CAAC,CAAC;AACpBnX,MAAM,CAACC,cAAc,CAACkX,EAAE,EAAE,YAAY,EAAE;EAAEjX,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACtDiX,EAAE,CAACC,KAAK,GAAGC,EAAE;AACb,IAAIC,EAAE,GAAGpZ,CAAC;AACV,SAASmZ,EAAEA,CAACxZ,CAAC,EAAEyB,CAAC,EAAE;EAChB,IAAIE,CAAC,EAAEC,CAAC;EACR,IAAIH,CAAC,KAAK,KAAK,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAEgY,EAAE,CAACpW,aAAa,EAAErD,CAAC,CAAC,EAAE;IACtDA,CAAC,CAACuZ,KAAK,CAAC,CAAC;IACT,IAAI/W,CAAC,GAAGf,CAAC,GAAG,CAAC,GAAGzB,CAAC,CAACqC,KAAK,CAACyE,MAAM;IAC9B9G,CAAC,CAAC0Z,iBAAiB,CAAClX,CAAC,EAAEA,CAAC,CAAC;EAC3B,CAAC,MAAM;IACL,IAAIoC,CAAC,GAAG7E,QAAQ,CAACkI,WAAW,CAAC,CAAC;MAAEnD,CAAC,GAAG1D,MAAM,CAAC4G,YAAY,CAAC,CAAC;IACzD,IAAI,CAAClD,CAAC,EACJ;IACF,IAAIE,CAAC,GAAG,SAAAA,CAASmR,CAAC,EAAEC,CAAC,EAAE;QACrBA,CAAC,KAAK,KAAK,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC,CAAC;QACxB,IAAIC,CAAC,GAAGtW,QAAQ,CAACI,cAAc,CAAC,EAAE,CAAC;QACnCiW,CAAC,GAAGD,CAAC,CAACwD,YAAY,CAACtD,CAAC,EAAEF,CAAC,CAACyD,UAAU,CAAC,GAAGzD,CAAC,CAACjW,WAAW,CAACmW,CAAC,CAAC,EAAEzR,CAAC,CAACgU,QAAQ,CAACvC,CAAC,EAAE,CAAC,CAAC,EAAEzR,CAAC,CAACiU,MAAM,CAACxC,CAAC,EAAE,CAAC,CAAC;MAC1F,CAAC;MAAEpR,CAAC,GAAG,SAAAA,CAASkR,CAAC,EAAE;QACjB,OAAOA,CAAC,IAAI,IAAI;MAClB,CAAC;MAAEjR,CAAC,GAAGlF,CAAC,CAACmT,UAAU;MAAEhH,CAAC,GAAG1K,CAAC,GAAGyD,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAACA,CAAC,CAAC4B,MAAM,GAAG,CAAC,CAAC;IACnD,IAAI7B,CAAC,CAACkH,CAAC,CAAC,EAAE;MACR,OAAOlH,CAAC,CAACkH,CAAC,CAAC,IAAIA,CAAC,CAAC0D,QAAQ,KAAKC,IAAI,CAACC,SAAS,GAC1C5D,CAAC,GAAG1K,CAAC,GAAG0K,CAAC,CAACyN,UAAU,GAAGzN,CAAC,CAAC0N,SAAS;MACpC,IAAI5U,CAAC,CAACkH,CAAC,CAAC,IAAIA,CAAC,CAAC0D,QAAQ,KAAKC,IAAI,CAACC,SAAS,EAAE;QACzC,IAAImG,CAAC,GAAG,CAACtU,CAAC,GAAG,CAACD,CAAC,GAAGwK,CAAC,CAAC6D,WAAW,MAAM,IAAI,IAAIrO,CAAC,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,CAAC,CAACmF,MAAM,MAAM,IAAI,IAAIlF,CAAC,KAAK,KAAK,CAAC,GAAGA,CAAC,GAAG,CAAC;UAAEY,CAAC,GAAGf,CAAC,GAAG,CAAC,GAAGyU,CAAC;QAC9HtR,CAAC,CAACgU,QAAQ,CAACzM,CAAC,EAAE3J,CAAC,CAAC,EAAEoC,CAAC,CAACiU,MAAM,CAAC1M,CAAC,EAAE3J,CAAC,CAAC;MAClC,CAAC,MACCwC,CAAC,CAAChF,CAAC,EAAEyB,CAAC,CAAC;IACX,CAAC,MACCuD,CAAC,CAAChF,CAAC,CAAC;IACN8E,CAAC,CAACsD,eAAe,CAAC,CAAC,EAAEtD,CAAC,CAACuD,QAAQ,CAACzD,CAAC,CAAC;EACpC;AACF;AACA,CAAC,UAAS5E,CAAC,EAAE;EACXmC,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,YAAY,EAAE;IAAEqC,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAErC,CAAC,CAACuZ,KAAK,GAAG,KAAK,CAAC;EACvE,IAAI9X,CAAC,GAAG6X,EAAE;EACVnX,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,OAAO,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAClE,OAAOjB,CAAC,CAAC8X,KAAK;IAChB;EAAE,CAAC,CAAC;AACN,CAAC,EAAEF,EAAE,CAAC;AACN,IAAIS,EAAE,GAAG,CAAC,CAAC;EAAEC,CAAC,GAAG,CAAC,CAAC;AACnB5X,MAAM,CAACC,cAAc,CAAC2X,CAAC,EAAE,YAAY,EAAE;EAAE1X,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACrD0X,CAAC,CAACC,qBAAqB,GAAGC,EAAE;AAC5B,SAASA,EAAEA,CAAA,EAAG;EACZ,IAAIja,CAAC,GAAGoB,MAAM,CAAC4G,YAAY,CAAC,CAAC;EAC7B,IAAIhI,CAAC,KAAK,IAAI,EACZ,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;EAClB,IAAIyB,CAAC,GAAGzB,CAAC,CAACka,SAAS;IAAEvY,CAAC,GAAG3B,CAAC,CAACma,WAAW;EACtC,OAAO1Y,CAAC,KAAK,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,IAAIA,CAAC,CAACoO,QAAQ,KAAKC,IAAI,CAACC,SAAS,IAAItO,CAAC,CAAC0R,UAAU,CAACrM,MAAM,GAAG,CAAC,KAAKrF,CAAC,CAAC0R,UAAU,CAACxR,CAAC,CAAC,KAAK,KAAK,CAAC,IAAIF,CAAC,GAAGA,CAAC,CAAC0R,UAAU,CAACxR,CAAC,CAAC,EAAEA,CAAC,GAAG,CAAC,KAAKF,CAAC,GAAGA,CAAC,CAAC0R,UAAU,CAACxR,CAAC,GAAG,CAAC,CAAC,EAAEF,CAAC,CAACuO,WAAW,KAAK,IAAI,KAAKrO,CAAC,GAAGF,CAAC,CAACuO,WAAW,CAAClJ,MAAM,CAAC,CAAC,CAAC,EAAE,CAACrF,CAAC,EAAEE,CAAC,CAAC,CAAC;AACrP;AACA,CAAC,UAAS3B,CAAC,EAAE;EACXmC,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,YAAY,EAAE;IAAEqC,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAErC,CAAC,CAACga,qBAAqB,GAAG,KAAK,CAAC;EACvF,IAAIvY,CAAC,GAAGsY,CAAC;EACT5X,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,uBAAuB,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAClF,OAAOjB,CAAC,CAACuY,qBAAqB;IAChC;EAAE,CAAC,CAAC;AACN,CAAC,EAAEF,EAAE,CAAC;AACN,IAAIM,EAAE,GAAG,CAAC,CAAC;EAAEC,CAAC,GAAG,CAAC,CAAC;AACnBlY,MAAM,CAACC,cAAc,CAACiY,CAAC,EAAE,YAAY,EAAE;EAAEhY,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACrDgY,CAAC,CAACC,QAAQ,GAAGC,EAAE;AACf,SAASA,EAAEA,CAAA,EAAG;EACZ,IAAIva,CAAC,GAAGoB,MAAM,CAAC4G,YAAY,CAAC,CAAC;EAC7B,OAAOhI,CAAC,IAAIA,CAAC,CAACwa,UAAU,GAAGxa,CAAC,CAACya,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI;AACnD;AACA,CAAC,UAASza,CAAC,EAAE;EACXmC,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,YAAY,EAAE;IAAEqC,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAErC,CAAC,CAACsa,QAAQ,GAAG,KAAK,CAAC;EAC1E,IAAI7Y,CAAC,GAAG4Y,CAAC;EACTlY,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,UAAU,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACrE,OAAOjB,CAAC,CAAC6Y,QAAQ;IACnB;EAAE,CAAC,CAAC;AACN,CAAC,EAAEF,EAAE,CAAC;AACN,IAAIM,EAAE,GAAG,CAAC,CAAC;EAAEC,EAAE,GAAG,CAAC,CAAC;AACpBxY,MAAM,CAACC,cAAc,CAACuY,EAAE,EAAE,YAAY,EAAE;EAAEtY,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACtDsY,EAAE,CAACC,mBAAmB,GAAGC,EAAE;AAC3B,IAAIC,EAAE,GAAGza,CAAC;EAAE0a,EAAE,GAAGjB,EAAE;EAAEkB,EAAE,GAAG1C,EAAE;AAC5B,SAASuC,EAAEA,CAAC7a,CAAC,EAAE;EACb,IAAIyB,CAAC,GAAG,CAAC,CAAC,EAAEqZ,EAAE,CAAC3J,cAAc,EAAEnR,CAAC,EAAE,CAAC,CAAC,CAAC;EACrC,IAAIyB,CAAC,KAAK,IAAI,EACZ,OAAO,CAAC,CAAC;EACX,IAAI,CAAC,CAAC,EAAEqZ,EAAE,CAACzX,aAAa,EAAE5B,CAAC,CAAC,EAC1B,OAAOA,CAAC,CAACwZ,YAAY,KAAKxZ,CAAC,CAACY,KAAK,CAACyE,MAAM;EAC1C,IAAInF,CAAC,GAAG,CAAC,CAAC,EAAEoZ,EAAE,CAACf,qBAAqB,EAAE,CAAC;IAAEpY,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC;IAAEa,CAAC,GAAGb,CAAC,CAAC,CAAC,CAAC;EAC3D,OAAOC,CAAC,KAAK,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAEoZ,EAAE,CAAChC,qCAAqC,EAAEhZ,CAAC,EAAE4B,CAAC,EAAEY,CAAC,EAAE,OAAO,CAAC;AAC1F;AACA,CAAC,UAASxC,CAAC,EAAE;EACXmC,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,YAAY,EAAE;IAAEqC,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAErC,CAAC,CAAC4a,mBAAmB,GAAG,KAAK,CAAC;EACrF,IAAInZ,CAAC,GAAGkZ,EAAE;EACVxY,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,qBAAqB,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAChF,OAAOjB,CAAC,CAACmZ,mBAAmB;IAC9B;EAAE,CAAC,CAAC;AACN,CAAC,EAAEF,EAAE,CAAC;AACN,IAAIQ,EAAE,GAAG,CAAC,CAAC;EAAEC,EAAE,GAAG,CAAC,CAAC;AACpBhZ,MAAM,CAACC,cAAc,CAAC+Y,EAAE,EAAE,YAAY,EAAE;EAAE9Y,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACtD8Y,EAAE,CAACC,qBAAqB,GAAGC,EAAE;AAC7B,IAAIC,CAAC,GAAGjb,CAAC;EAAEkb,EAAE,GAAGxB,CAAC;EAAEyB,EAAE,GAAGjD,CAAC;AACzB,SAAS8C,EAAEA,CAACrb,CAAC,EAAE;EACb,IAAIyB,CAAC,GAAG,CAAC,CAAC,EAAE6Z,CAAC,CAACnK,cAAc,EAAEnR,CAAC,CAAC;EAChC,IAAIyB,CAAC,KAAK,IAAI,IAAI,CAAC,CAAC,EAAE6Z,CAAC,CAAClO,OAAO,EAAEpN,CAAC,CAAC,EACjC,OAAO,CAAC,CAAC;EACX,IAAI,CAAC,CAAC,EAAEsb,CAAC,CAACjY,aAAa,EAAE5B,CAAC,CAAC,EACzB,OAAOA,CAAC,CAACwZ,YAAY,KAAK,CAAC;EAC7B,IAAI,CAAC,CAAC,EAAEK,CAAC,CAAClO,OAAO,EAAEpN,CAAC,CAAC,EACnB,OAAO,CAAC,CAAC;EACX,IAAI2B,CAAC,GAAG,CAAC,CAAC,EAAE4Z,EAAE,CAACvB,qBAAqB,EAAE,CAAC;IAAEpY,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC;IAAEa,CAAC,GAAGb,CAAC,CAAC,CAAC,CAAC;EAC3D,OAAOC,CAAC,KAAK,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE4Z,EAAE,CAACxC,qCAAqC,EAAEhZ,CAAC,EAAE4B,CAAC,EAAEY,CAAC,EAAE,MAAM,CAAC;AACzF;AACA,CAAC,UAASxC,CAAC,EAAE;EACXmC,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,YAAY,EAAE;IAAEqC,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAErC,CAAC,CAACob,qBAAqB,GAAG,KAAK,CAAC;EACvF,IAAI3Z,CAAC,GAAG0Z,EAAE;EACVhZ,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,uBAAuB,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAClF,OAAOjB,CAAC,CAAC2Z,qBAAqB;IAChC;EAAE,CAAC,CAAC;AACN,CAAC,EAAEF,EAAE,CAAC;AACN,IAAIO,EAAE,GAAG,CAAC,CAAC;EAAEC,EAAE,GAAG,CAAC,CAAC;AACpBvZ,MAAM,CAACC,cAAc,CAACsZ,EAAE,EAAE,YAAY,EAAE;EAAErZ,KAAK,EAAE,CAAC;AAAE,CAAC,CAAC;AACtDqZ,EAAE,CAACC,IAAI,GAAGC,EAAE;AACZ,IAAIC,EAAE,GAAGxb,CAAC;EAAEyb,EAAE,GAAGzB,CAAC;AAClB,SAASuB,EAAEA,CAAA,EAAG;EACZ,IAAI5b,CAAC,GAAG,CAAC,CAAC,EAAE8b,EAAE,CAACxB,QAAQ,EAAE,CAAC;IAAE7Y,CAAC,GAAG,CAAC,CAAC,EAAEoa,EAAE,CAAC7M,IAAI,EAAE,MAAM,CAAC;EACpD,IAAIvN,CAAC,CAACsa,EAAE,GAAG,QAAQ,EAAEta,CAAC,CAACua,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,CAAChc,CAAC,EACrC,OAAOA,CAAC,CAACic,UAAU,CAACxa,CAAC,CAAC,EAAE,YAAW;IACjC,IAAIG,CAAC,GAAGR,MAAM,CAAC4G,YAAY,CAAC,CAAC;IAC7BpG,CAAC,KAAK5B,CAAC,CAACkc,aAAa,CAACza,CAAC,CAAC,EAAEzB,CAAC,CAACmc,WAAW,CAAC1a,CAAC,CAAC,EAAEG,CAAC,CAACwG,eAAe,CAAC,CAAC,EAAExG,CAAC,CAACyG,QAAQ,CAACrI,CAAC,CAAC,EAAE0I,UAAU,CAAC,YAAW;MACpGjH,CAAC,CAAC2a,MAAM,CAAC,CAAC;IACZ,CAAC,EAAE,GAAG,CAAC,CAAC;EACV,CAAC;AACL;AACA,CAAC,UAASpc,CAAC,EAAE;EACXmC,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,YAAY,EAAE;IAAEqC,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAErC,CAAC,CAAC2b,IAAI,GAAG,KAAK,CAAC;EACtE,IAAIla,CAAC,GAAGia,EAAE;EACVvZ,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,MAAM,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACjE,OAAOjB,CAAC,CAACka,IAAI;IACf;EAAE,CAAC,CAAC;AACN,CAAC,EAAEF,EAAE,CAAC;AACN,CAAC,UAASzb,CAAC,EAAE;EACXmC,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,YAAY,EAAE;IAAEqC,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAErC,CAAC,CAAC2b,IAAI,GAAG3b,CAAC,CAACob,qBAAqB,GAAGpb,CAAC,CAAC4a,mBAAmB,GAAG5a,CAAC,CAACsa,QAAQ,GAAGta,CAAC,CAACga,qBAAqB,GAAGha,CAAC,CAACuZ,KAAK,GAAGvZ,CAAC,CAACyY,uBAAuB,GAAGzY,CAAC,CAACgZ,qCAAqC,GAAG,KAAK,CAAC;EAC/O,IAAIvX,CAAC,GAAG6W,EAAE;EACVnW,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,uCAAuC,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAClG,OAAOjB,CAAC,CAACuX,qCAAqC;IAChD;EAAE,CAAC,CAAC;EACJ,IAAIrX,CAAC,GAAGyX,EAAE;EACVjX,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,yBAAyB,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACpF,OAAOf,CAAC,CAAC8W,uBAAuB;IAClC;EAAE,CAAC,CAAC;EACJ,IAAI7W,CAAC,GAAGyX,EAAE;EACVlX,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,OAAO,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAClE,OAAOd,CAAC,CAAC2X,KAAK;IAChB;EAAE,CAAC,CAAC;EACJ,IAAI/W,CAAC,GAAGsX,EAAE;EACV3X,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,uBAAuB,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAClF,OAAOF,CAAC,CAACwX,qBAAqB;IAChC;EAAE,CAAC,CAAC;EACJ,IAAIpV,CAAC,GAAGwV,EAAE;EACVjY,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,UAAU,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACrE,OAAOkC,CAAC,CAAC0V,QAAQ;IACnB;EAAE,CAAC,CAAC;EACJ,IAAIxV,CAAC,GAAG4V,EAAE;EACVvY,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,qBAAqB,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAChF,OAAOoC,CAAC,CAAC8V,mBAAmB;IAC9B;EAAE,CAAC,CAAC;EACJ,IAAI5V,CAAC,GAAGkW,EAAE;EACV/Y,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,uBAAuB,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MAClF,OAAOsC,CAAC,CAACoW,qBAAqB;IAChC;EAAE,CAAC,CAAC;EACJ,IAAInW,CAAC,GAAGwW,EAAE;EACVtZ,MAAM,CAACC,cAAc,CAACpC,CAAC,EAAE,MAAM,EAAE;IAAE2C,UAAU,EAAE,CAAC,CAAC;IAAED,GAAG,EAAE,SAAAA,CAAA,EAAW;MACjE,OAAOuC,CAAC,CAAC0W,IAAI;IACf;EAAE,CAAC,CAAC;AACN,CAAC,EAAEtD,CAAC,CAAC;AACL,MAAMgE,CAAC,CAAC;EACN;AACF;AACA;EACE,WAAW7E,GAAGA,CAAA,EAAG;IACf,OAAO;MACL,GAAGN,CAAC;MACJoF,SAAS,EAAG,GAAErF,CAAE,YAAW;MAC3BsF,WAAW,EAAG,GAAEtF,CAAE,qBAAoB;MACtCuF,OAAO,EAAG,GAAEvF,CAAE,sBAAqB;MACnCwF,QAAQ,EAAG,GAAExF,CAAE,kBAAiB;MAChCyF,iBAAiB,EAAG,GAAEzF,CAAE;IAC1B,CAAC;EACH;EACA;AACF;AACA;AACA;AACA;EACEjV,WAAWA,CAACP,CAAC,EAAEE,CAAC,EAAE;IAChB,IAAI,CAAC+V,MAAM,GAAG/V,CAAC,EAAE,IAAI,CAACgW,QAAQ,GAAGlW,CAAC;EACpC;EACA;AACF;AACA;AACA;AACA;EACEmW,aAAaA,CAACnW,CAAC,EAAE;IACf,IAAIE,CAAC;IACL,OAAOF,CAAC,KAAK,CAAC,CAAC,IAAIE,CAAC,GAAGtB,CAAC,CAAC2O,IAAI,CAAC,IAAI,EAAE,CAACqN,CAAC,CAAC7E,GAAG,CAACL,OAAO,EAAEkF,CAAC,CAAC7E,GAAG,CAAC8E,SAAS,CAAC,CAAC,EAAE3a,CAAC,CAACgb,gBAAgB,CAAC,OAAO,EAAG/a,CAAC,IAAK;MACxG,MAAMY,CAAC,GAAGZ,CAAC,CAACgb,MAAM;MAClB,IAAIpa,CAAC,EAAE;QACL,MAAMoC,CAAC,GAAGpC,CAAC,CAACqa,OAAO,CAAE,IAAGR,CAAC,CAAC7E,GAAG,CAACkF,iBAAkB,EAAC,CAAC;QAClD9X,CAAC,IAAIA,CAAC,CAACkY,QAAQ,CAACta,CAAC,CAAC,IAAI,IAAI,CAACua,cAAc,CAACnY,CAAC,CAAC;MAC9C;IACF,CAAC,CAAC,IAAIjD,CAAC,GAAGtB,CAAC,CAAC2O,IAAI,CAAC,IAAI,EAAE,CAACqN,CAAC,CAAC7E,GAAG,CAAC8E,SAAS,EAAED,CAAC,CAAC7E,GAAG,CAACF,YAAY,CAAC,CAAC,EAAE3V,CAAC;EAClE;EACA;AACF;AACA;AACA;AACA;AACA;EACEkW,UAAUA,CAACpW,CAAC,EAAEE,CAAC,EAAE;IACf,MAAMC,CAAC,GAAGvB,CAAC,CAAC2O,IAAI,CAAC,IAAI,EAAE,CAACqN,CAAC,CAAC7E,GAAG,CAACJ,IAAI,EAAEiF,CAAC,CAAC7E,GAAG,CAACJ,IAAI,CAAC,CAAC;MAAE5U,CAAC,GAAGnC,CAAC,CAAC2O,IAAI,CAAC,KAAK,EAAEqN,CAAC,CAAC7E,GAAG,CAACH,WAAW,EAAE;QACrFvP,SAAS,EAAErG,CAAC;QACZgE,eAAe,EAAE,CAAC,CAAC,IAAI,CAACkS,QAAQ,EAAE/O,QAAQ,CAAC;MAC7C,CAAC,CAAC;MAAEhE,CAAC,GAAGvE,CAAC,CAAC2O,IAAI,CAAC,MAAM,EAAEqN,CAAC,CAAC7E,GAAG,CAACiF,QAAQ,CAAC;MAAE3X,CAAC,GAAGzE,CAAC,CAAC2O,IAAI,CAAC,KAAK,EAAEqN,CAAC,CAAC7E,GAAG,CAACkF,iBAAiB,CAAC;IAClF,OAAO/a,CAAC,CAACqb,OAAO,KAAK,CAAC,CAAC,IAAIlY,CAAC,CAACqK,SAAS,CAACvD,GAAG,CAACyQ,CAAC,CAAC7E,GAAG,CAAC+E,WAAW,CAAC,EAAE3X,CAAC,CAACkD,SAAS,GAAGtH,EAAE,EAAEsE,CAAC,CAAC5E,WAAW,CAAC0E,CAAC,CAAC,EAAEhD,CAAC,CAAC1B,WAAW,CAAC4E,CAAC,CAAC,EAAElD,CAAC,CAAC1B,WAAW,CAACsC,CAAC,CAAC,EAAEZ,CAAC;EAC1I;EACA;AACF;AACA;AACA;AACA;EACEkW,cAAcA,CAACrW,CAAC,EAAE;IAChB,MAAME,CAAC,GAAGF,CAAC,CAACsW,aAAa,CAAE,IAAGsE,CAAC,CAAC7E,GAAG,CAACH,WAAY,EAAC,CAAC;IAClD,OAAO,CAAC1V,CAAC,IAAItB,CAAC,CAAC+M,OAAO,CAACzL,CAAC,CAAC,GAAG,EAAE,GAAGA,CAAC,CAACmG,SAAS;EAC9C;EACA;AACF;AACA;AACA;AACA;EACEkQ,WAAWA,CAACvW,CAAC,EAAE;IACb,MAAME,CAAC,GAAGF,CAAC,CAACsW,aAAa,CAAE,IAAGsE,CAAC,CAAC7E,GAAG,CAACkF,iBAAkB,EAAC,CAAC;IACxD,OAAO;MACLM,OAAO,EAAErb,CAAC,GAAGA,CAAC,CAACwN,SAAS,CAAC2N,QAAQ,CAACT,CAAC,CAAC7E,GAAG,CAAC+E,WAAW,CAAC,GAAG,CAAC;IAC1D,CAAC;EACH;EACA;AACF;AACA;EACEtE,kBAAkBA,CAAA,EAAG;IACnB,OAAO;MAAE+E,OAAO,EAAE,CAAC;IAAE,CAAC;EACxB;EACA;AACF;AACA;AACA;EACED,cAAcA,CAACtb,CAAC,EAAE;IAChBA,CAAC,CAAC0N,SAAS,CAAC8N,MAAM,CAACZ,CAAC,CAAC7E,GAAG,CAAC+E,WAAW,CAAC,EAAE9a,CAAC,CAAC0N,SAAS,CAACvD,GAAG,CAACyQ,CAAC,CAAC7E,GAAG,CAACgF,OAAO,CAAC,EAAE/a,CAAC,CAACkb,gBAAgB,CAAC,YAAY,EAAE,MAAM,IAAI,CAACO,0BAA0B,CAACzb,CAAC,CAAC,EAAE;MAAE0b,IAAI,EAAE,CAAC;IAAE,CAAC,CAAC;EACjK;EACA;AACF;AACA;AACA;EACED,0BAA0BA,CAACzb,CAAC,EAAE;IAC5BA,CAAC,CAAC0N,SAAS,CAACiN,MAAM,CAACC,CAAC,CAAC7E,GAAG,CAACgF,OAAO,CAAC;EACnC;AACF;AACA,SAASY,CAACA,CAACpd,CAAC,EAAEyB,CAAC,GAAG,OAAO,EAAE;EACzB,MAAME,CAAC,GAAG,EAAE;EACZ,IAAIC,CAAC;EACL,SAASY,CAACA,CAACoC,CAAC,EAAE;IACZ,QAAQnD,CAAC;MACP,KAAK,OAAO;QACV,OAAOmD,CAAC,CAACyY,kBAAkB;MAC7B,KAAK,QAAQ;QACX,OAAOzY,CAAC,CAAC0Y,sBAAsB;IACnC;EACF;EACA,KAAK1b,CAAC,GAAGY,CAAC,CAACxC,CAAC,CAAC,EAAE4B,CAAC,KAAK,IAAI,GACvBD,CAAC,CAACuS,IAAI,CAACtS,CAAC,CAAC,EAAEA,CAAC,GAAGY,CAAC,CAACZ,CAAC,CAAC;EACrB,OAAOD,CAAC,CAACmF,MAAM,KAAK,CAAC,GAAGnF,CAAC,GAAG,IAAI;AAClC;AACA,SAAS4b,CAACA,CAACvd,CAAC,EAAEyB,CAAC,GAAG,CAAC,CAAC,EAAE;EACpB,IAAIE,CAAC,GAAG3B,CAAC;EACT,OAAOA,CAAC,CAACmP,SAAS,CAAC2N,QAAQ,CAAC5F,CAAC,CAACE,IAAI,CAAC,KAAKzV,CAAC,GAAG3B,CAAC,CAAC+X,aAAa,CAAE,IAAGb,CAAC,CAACI,YAAa,EAAC,CAAC,CAAC,EAAE3V,CAAC,KAAK,IAAI,GAAG,EAAE,GAAGF,CAAC,GAAGoC,KAAK,CAAC4K,IAAI,CAAC9M,CAAC,CAACwQ,gBAAgB,CAAE,aAAY+E,CAAC,CAACE,IAAK,EAAC,CAAC,CAAC,GAAGvT,KAAK,CAAC4K,IAAI,CAAC9M,CAAC,CAACwQ,gBAAgB,CAAE,IAAG+E,CAAC,CAACE,IAAK,EAAC,CAAC,CAAC;AAChN;AACA,SAASoG,EAAEA,CAACxd,CAAC,EAAE;EACb,OAAOA,CAAC,CAACqd,kBAAkB,KAAK,IAAI;AACtC;AACA,SAASI,EAAEA,CAACzd,CAAC,EAAE;EACb,OAAOA,CAAC,CAAC+X,aAAa,CAAE,IAAGb,CAAC,CAACI,YAAa,EAAC,CAAC,KAAK,IAAI;AACvD;AACA,SAASoG,CAACA,CAAC1d,CAAC,EAAE;EACZ,OAAOA,CAAC,CAAC+X,aAAa,CAAE,IAAGb,CAAC,CAACI,YAAa,EAAC,CAAC;AAC9C;AACA,SAASqG,CAACA,CAAC3d,CAAC,EAAE;EACZ,IAAIyB,CAAC,GAAGzB,CAAC;EACTA,CAAC,CAACmP,SAAS,CAAC2N,QAAQ,CAAC5F,CAAC,CAACE,IAAI,CAAC,KAAK3V,CAAC,GAAGic,CAAC,CAAC1d,CAAC,CAAC,CAAC,EAAEyB,CAAC,KAAK,IAAI,IAAI8b,CAAC,CAAC9b,CAAC,CAAC,CAACqF,MAAM,KAAK,CAAC,IAAIrF,CAAC,CAAC2a,MAAM,CAAC,CAAC;AAC3F;AACA,SAASwB,CAACA,CAAC5d,CAAC,EAAE;EACZ,OAAOA,CAAC,CAAC+X,aAAa,CAAE,IAAGb,CAAC,CAACG,WAAY,EAAC,CAAC;AAC7C;AACA,SAASwG,CAACA,CAAC7d,CAAC,EAAEyB,CAAC,GAAG,CAAC,CAAC,EAAE;EACpB,MAAME,CAAC,GAAGic,CAAC,CAAC5d,CAAC,CAAC;EACd2B,CAAC,IAAI0W,CAAC,CAACkB,KAAK,CAAC5X,CAAC,EAAEF,CAAC,CAAC;AACpB;AACA,MAAMqc,CAAC,CAAC;EACN;AACF;AACA;AACA;EACE,IAAIC,WAAWA,CAAA,EAAG;IAChB,MAAMtc,CAAC,GAAGL,MAAM,CAAC4G,YAAY,CAAC,CAAC;IAC/B,IAAI,CAACvG,CAAC,EACJ,OAAO,IAAI;IACb,IAAIE,CAAC,GAAGF,CAAC,CAACuc,UAAU;IACpB,OAAO,CAACrc,CAAC,KAAKyW,CAAC,CAACzW,CAAC,CAAC,KAAKA,CAAC,GAAGA,CAAC,CAAC8P,UAAU,CAAC,EAAE,CAAC9P,CAAC,CAAC,IAAI,CAACyW,CAAC,CAACzW,CAAC,CAAC,GAAG,IAAI,GAAGA,CAAC,CAACkb,OAAO,CAAE,IAAG3F,CAAC,CAACE,IAAK,EAAC,CAAC;EACzF;EACA;AACF;AACA;EACE,IAAI6G,gBAAgBA,CAAA,EAAG;IACrB,MAAMxc,CAAC,GAAG,IAAI,CAACsc,WAAW;IAC1B,IAAItc,CAAC,KAAK,IAAI,EACZ,OAAO,IAAI;IACb,IAAIE,CAAC,GAAGF,CAAC,CAACgQ,UAAU;MAAE7P,CAAC,GAAG,CAAC;IAC3B,OAAOD,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAK,IAAI,CAACuc,WAAW,GACzC9F,CAAC,CAACzW,CAAC,CAAC,IAAIA,CAAC,CAACwN,SAAS,CAAC2N,QAAQ,CAAC5F,CAAC,CAACE,IAAI,CAAC,KAAKxV,CAAC,IAAI,CAAC,CAAC,EAAED,CAAC,GAAGA,CAAC,CAAC8P,UAAU;IACpE,OAAO7P,CAAC,GAAG,CAAC;EACd;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEI,WAAWA,CAAC;IAAEmc,IAAI,EAAE1c,CAAC;IAAEiW,MAAM,EAAE/V,CAAC;IAAEyc,GAAG,EAAExc,CAAC;IAAE+V,QAAQ,EAAEnV,CAAC;IAAE6b,KAAK,EAAEzZ;EAAE,CAAC,EAAEE,CAAC,EAAE;IACpE,IAAI,CAAC4S,MAAM,GAAG/V,CAAC,EAAE,IAAI,CAACwc,IAAI,GAAG1c,CAAC,EAAE,IAAI,CAACkW,QAAQ,GAAGnV,CAAC,EAAE,IAAI,CAAC4b,GAAG,GAAGxc,CAAC,EAAE,IAAI,CAACyc,KAAK,GAAGzZ,CAAC,EAAE,IAAI,CAAC0Z,QAAQ,GAAGxZ,CAAC;EACpG;EACA;AACF;AACA;AACA;EACEyZ,MAAMA,CAAA,EAAG;IACP,OAAO,IAAI,CAACL,WAAW,GAAG,IAAI,CAACI,QAAQ,CAAC1G,aAAa,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAACuG,IAAI,CAACK,KAAK,CAAC1X,MAAM,GAAG,IAAI,CAAC2X,WAAW,CAAC,IAAI,CAACN,IAAI,CAACK,KAAK,EAAE,IAAI,CAACN,WAAW,CAAC,GAAG,IAAI,CAACO,WAAW,CACxJ,CACE;MACEC,OAAO,EAAE,EAAE;MACXC,IAAI,EAAE,CAAC,CAAC;MACRH,KAAK,EAAE;IACT,CAAC,CACF,EACD,IAAI,CAACN,WACP,CAAC,EAAE,IAAI,CAACvG,QAAQ,IAAI,IAAI,CAACuG,WAAW,CAACvB,gBAAgB,CACnD,SAAS,EACRlb,CAAC,IAAK;MACL,QAAQA,CAAC,CAACmd,GAAG;QACX,KAAK,OAAO;UACV,IAAI,CAACC,YAAY,CAACpd,CAAC,CAAC;UACpB;QACF,KAAK,WAAW;UACd,IAAI,CAACqd,SAAS,CAACrd,CAAC,CAAC;UACjB;QACF,KAAK,KAAK;UACRA,CAAC,CAACsd,QAAQ,GAAG,IAAI,CAACC,QAAQ,CAACvd,CAAC,CAAC,GAAG,IAAI,CAACwd,MAAM,CAACxd,CAAC,CAAC;UAC9C;MACJ;IACF,CAAC,EACD,CAAC,CACH,CAAC,EAAE,OAAO,IAAI,IAAI,CAAC0c,IAAI,CAACQ,IAAI,IAAI,IAAI,CAACR,IAAI,CAACQ,IAAI,CAACO,KAAK,KAAK,KAAK,CAAC,IAAI,IAAI,CAACC,eAAe,CAAC,IAAI,CAAChB,IAAI,CAACQ,IAAI,CAACO,KAAK,CAAC,EAAE,aAAa,IAAI,IAAI,CAACf,IAAI,CAACQ,IAAI,IAAI,IAAI,CAACR,IAAI,CAACQ,IAAI,CAACS,WAAW,KAAK,KAAK,CAAC,IAAI,IAAI,CAACC,cAAc,CAAC,IAAI,CAAClB,IAAI,CAACQ,IAAI,CAACS,WAAW,CAAC,EAAE,IAAI,CAAClB,WAAW;EAC9P;EACA;AACF;AACA;AACA;AACA;EACEvC,IAAIA,CAACla,CAAC,EAAE;IACN,MAAME,CAAC,GAAGF,CAAC,IAAI,IAAI,CAACyc,WAAW;MAAEtc,CAAC,GAAIkD,CAAC,IAAKyY,CAAC,CAACzY,CAAC,CAAC,CAAC9B,GAAG,CAAEiC,CAAC,IAAK;QAC1D,MAAMC,CAAC,GAAGwY,CAAC,CAACzY,CAAC,CAAC;UAAEkH,CAAC,GAAG,IAAI,CAACmS,QAAQ,CAACxG,cAAc,CAAC7S,CAAC,CAAC;UAAEiR,CAAC,GAAG,IAAI,CAACoI,QAAQ,CAACtG,WAAW,CAAC/S,CAAC,CAAC;UAAEkR,CAAC,GAAGjR,CAAC,GAAGtD,CAAC,CAACsD,CAAC,CAAC,GAAG,EAAE;QACxG,OAAO;UACLwZ,OAAO,EAAEvS,CAAC;UACVwS,IAAI,EAAEzI,CAAC;UACPsI,KAAK,EAAErI;QACT,CAAC;MACH,CAAC,CAAC;MAAE3T,CAAC,GAAGb,CAAC,GAAGC,CAAC,CAACD,CAAC,CAAC,GAAG,EAAE;IACrB,IAAIiD,CAAC,GAAG;MACN8C,KAAK,EAAE,IAAI,CAACyW,IAAI,CAACzW,KAAK;MACtBiX,IAAI,EAAE,CAAC,CAAC;MACRH,KAAK,EAAEhc;IACT,CAAC;IACD,OAAO,IAAI,CAAC2b,IAAI,CAACzW,KAAK,KAAK,SAAS,KAAK9C,CAAC,CAAC+Z,IAAI,GAAG;MAChDO,KAAK,EAAE,IAAI,CAACf,IAAI,CAACQ,IAAI,CAACO,KAAK;MAC3BE,WAAW,EAAE,IAAI,CAACjB,IAAI,CAACQ,IAAI,CAACS;IAC9B,CAAC,CAAC,EAAExa,CAAC;EACP;EACA;AACF;AACA;AACA;AACA;EACE,WAAW0a,WAAWA,CAAA,EAAG;IACvB,OAAO;MACLC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI;IACzB,CAAC;EACH;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,KAAKA,CAAC/d,CAAC,EAAE;IACP,MAAME,CAAC,GAAG,IAAI,CAAC0c,KAAK,CAACoB,MAAM,CAACtN,gBAAgB,CAAE,IAAG+E,CAAC,CAACE,IAAK,EAAC,CAAC;MAAExV,CAAC,GAAGD,CAAC,CAACA,CAAC,CAACmF,MAAM,GAAG,CAAC,CAAC;MAAEtE,CAAC,GAAGob,CAAC,CAAChc,CAAC,CAAC;IACzF,IAAIA,CAAC,KAAK,IAAI,IAAIY,CAAC,KAAK,IAAI,KAAKA,CAAC,CAACkd,kBAAkB,CAAC,WAAW,EAAEje,CAAC,CAAC+c,KAAK,CAAC,CAAC,CAAC,CAACE,OAAO,CAAC,EAAE,IAAI,CAACR,WAAW,KAAK,KAAK,CAAC,CAAC,EAClH;IACF,MAAMtZ,CAAC,GAAG2Y,CAAC,CAAC,IAAI,CAACW,WAAW,CAAC;IAC7B,IAAItZ,CAAC,CAACkC,MAAM,KAAK,CAAC,EAChB;IACF,MAAMhC,CAAC,GAAGF,CAAC,CAACA,CAAC,CAACkC,MAAM,GAAG,CAAC,CAAC;IACzB,IAAI9B,CAAC,GAAG0Y,CAAC,CAAC5Y,CAAC,CAAC;IACZ,MAAMG,CAAC,GAAGxD,CAAC,CAAC+c,KAAK,CAAC9U,KAAK,CAAC,CAAC;IACzBzE,CAAC,KAAK,KAAK,CAAC,KAAKA,CAAC,CAACuZ,KAAK,CAAC1X,MAAM,KAAK,CAAC,KAAK9B,CAAC,KAAK,IAAI,KAAKA,CAAC,GAAG,IAAI,CAACsZ,QAAQ,CAAC1G,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC6G,WAAW,CAACxZ,CAAC,CAACuZ,KAAK,EAAExZ,CAAC,CAAC,CAAC,EAAEvD,CAAC,CAAC+c,KAAK,CAAC1X,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC2X,WAAW,CAAChd,CAAC,CAAC+c,KAAK,EAAE,IAAI,CAACN,WAAW,CAAC,CAAC;EAClM;EACA;AACF;AACA;AACA;AACA;EACEyB,OAAOA,CAACle,CAAC,EAAE;IACT,MAAME,CAAC,GAAGF,CAAC,CAACme,MAAM,CAACzB,IAAI;IACvB,IAAI,CAACA,IAAI,GAAG,IAAI,CAAC0B,YAAY,CAACle,CAAC,CAAC;IAChC,MAAMC,CAAC,GAAG,IAAI,CAACsc,WAAW;IAC1Btc,CAAC,IAAIA,CAAC,CAAC6P,UAAU,IAAI7P,CAAC,CAAC6P,UAAU,CAACqO,YAAY,CAAC,IAAI,CAACvB,MAAM,CAAC,CAAC,EAAE3c,CAAC,CAAC;EAClE;EACA;AACF;AACA;AACA;AACA;EACEie,YAAYA,CAACpe,CAAC,EAAE;IACd,MAAM;MAAE8B,OAAO,EAAE5B;IAAE,CAAC,GAAGF,CAAC;IACxB,IAAIG,CAAC,GAAG,WAAW;MAAEY,CAAC;IACtB,QAAQb,CAAC;MACP,KAAK,IAAI;QACPC,CAAC,GAAG,SAAS,EAAEY,CAAC,GAAG,IAAI;QACvB;MACF,KAAK,IAAI;MACT,KAAK,IAAI;QACPZ,CAAC,GAAG,WAAW,EAAEY,CAAC,GAAG,IAAI;IAC7B;IACA,MAAMoC,CAAC,GAAG;MACR8C,KAAK,EAAE9F,CAAC;MACR+c,IAAI,EAAE,CAAC,CAAC;MACRH,KAAK,EAAE;IACT,CAAC;IACD5c,CAAC,KAAK,SAAS,KAAK,IAAI,CAACuc,IAAI,CAACQ,IAAI,CAACS,WAAW,GAAG,SAAS,EAAE,IAAI,CAACjB,IAAI,CAACQ,IAAI,CAACO,KAAK,GAAG,CAAC,CAAC;IACrF,MAAMpa,CAAC,GAAIE,CAAC,IAAKnB,KAAK,CAAC4K,IAAI,CAACzJ,CAAC,CAACmN,gBAAgB,CAAC,aAAa,CAAC,CAAC,CAACnP,GAAG,CAAEkC,CAAC,IAAK;MACxE,MAAMiH,CAAC,GAAGjH,CAAC,CAAC6S,aAAa,CAAE,YAAWvV,CAAE,EAAC,CAAC;QAAE0T,CAAC,GAAG/J,CAAC,GAAGrH,CAAC,CAACqH,CAAC,CAAC,GAAG,EAAE;MAC7D,OAAO;QACLuS,OAAO,EAAExZ,CAAC,CAAC4C,SAAS,IAAI,EAAE;QAC1B6W,IAAI,EAAE,CAAC,CAAC;QACRH,KAAK,EAAEtI;MACT,CAAC;IACH,CAAC,CAAC;IACF,OAAOtR,CAAC,CAAC4Z,KAAK,GAAG1Z,CAAC,CAACrD,CAAC,CAAC,EAAEmD,CAAC;EAC1B;EACA;AACF;AACA;AACA;EACEua,eAAeA,CAAC1d,CAAC,EAAE;IACjB,IAAI,CAACyc,WAAW,CAACxW,KAAK,CAACqY,WAAW,CAAC,eAAe,EAAG,QAAOte,CAAC,GAAG,CAAE,EAAC,CAAC,EAAE,IAAI,CAAC0c,IAAI,CAACQ,IAAI,CAACO,KAAK,GAAGzd,CAAC;EAChG;EACA;AACF;AACA;AACA;EACE4d,cAAcA,CAAC5d,CAAC,EAAE;IAChB,IAAI,CAACyc,WAAW,CAACxW,KAAK,CAACqY,WAAW,CAAC,qBAAqB,EAAEte,CAAC,CAAC,EAAE,IAAI,CAAC0c,IAAI,CAACQ,IAAI,CAACS,WAAW,GAAG3d,CAAC;EAC9F;EACA;AACF;AACA;AACA;EACEod,YAAYA,CAACpd,CAAC,EAAE;IACd,IAAIuD,CAAC;IACL,MAAMrD,CAAC,GAAG,IAAI,CAACoc,WAAW;IAC1B,IAAItc,CAAC,CAACue,eAAe,CAAC,CAAC,EAAEve,CAAC,CAACwe,cAAc,CAAC,CAAC,EAAExe,CAAC,CAACye,WAAW,IAAIve,CAAC,KAAK,IAAI,EACtE;IACF,MAAMC,CAAC,GAAG,CAAC,CAACoD,CAAC,GAAG,IAAI,CAACsZ,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGtZ,CAAC,CAAC8S,cAAc,CAACnW,CAAC,CAAC,CAACkS,IAAI,CAAC,CAAC,CAAC/M,MAAM,MAAM,CAAC;MAAEtE,CAAC,GAAGb,CAAC,CAAC8P,UAAU,KAAK,IAAI,CAACyM,WAAW;MAAEtZ,CAAC,GAAGjD,CAAC,CAAC2b,sBAAsB,KAAK,IAAI;MAAExY,CAAC,GAAG,IAAI,CAACsZ,GAAG,CAAC+B,MAAM,CAACC,oBAAoB,CAAC,CAAC;IACpN,IAAI5d,CAAC,IAAIZ,CAAC;MACR,IAAI4b,EAAE,CAAC7b,CAAC,CAAC,IAAI,CAAC8b,EAAE,CAAC9b,CAAC,CAAC,EAAE;QACnBiD,CAAC,GAAG,IAAI,CAACyb,yBAAyB,CAACvb,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,CAACub,yBAAyB,CAAC,CAAC;QAC5E;MACF,CAAC,MAAM;QACL,IAAI,CAACC,SAAS,CAAC3e,CAAC,CAAC;QACjB;MACF;IAAC,OACE,IAAIC,CAAC,EAAE;MACV,IAAI,CAAC2e,WAAW,CAAC5e,CAAC,CAAC;MACnB;IACF,CAAC,MACC,IAAI,CAAC6e,SAAS,CAAC7e,CAAC,CAAC;EACrB;EACA;AACF;AACA;AACA;EACEmd,SAASA,CAACrd,CAAC,EAAE;IACX,IAAIG,CAAC;IACL,MAAMD,CAAC,GAAG,IAAI,CAACoc,WAAW;IAC1B,IAAIpc,CAAC,KAAK,IAAI,IAAI0W,CAAC,CAAC+C,qBAAqB,CAACzZ,CAAC,CAAC,IAAI,CAAC,CAACC,CAAC,GAAGR,MAAM,CAAC4G,YAAY,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGpG,CAAC,CAAC6e,WAAW,MAAM,CAAC,CAAC,EAAE;MACrH,IAAIhf,CAAC,CAACue,eAAe,CAAC,CAAC,EAAEre,CAAC,CAAC8P,UAAU,KAAK,IAAI,CAACyM,WAAW,IAAIvc,CAAC,CAAC2b,sBAAsB,KAAK,IAAI,EAAE;QAC/F,IAAI,CAACoD,8BAA8B,CAAC,CAAC;QACrC;MACF;MACAjf,CAAC,CAACwe,cAAc,CAAC,CAAC,EAAE,IAAI,CAACU,qBAAqB,CAAChf,CAAC,CAAC;IACnD;EACF;EACA;AACF;AACA;AACA;EACEqd,QAAQA,CAACvd,CAAC,EAAE;IACVA,CAAC,CAACue,eAAe,CAAC,CAAC,EAAEve,CAAC,CAACwe,cAAc,CAAC,CAAC,EAAE,IAAI,CAAClC,WAAW,KAAK,IAAI,IAAI,IAAI,CAACwC,WAAW,CAAC,IAAI,CAACxC,WAAW,CAAC;EAC1G;EACA;AACF;AACA;AACA;EACEwC,WAAWA,CAAC9e,CAAC,EAAE;IACb,IAAI,CAACA,CAAC,CAACgQ,UAAU,IAAI,CAAC2G,CAAC,CAAC3W,CAAC,CAACgQ,UAAU,CAAC,EACnC;IACF,MAAM9P,CAAC,GAAGF,CAAC,CAACgQ,UAAU,CAACoL,OAAO,CAAE,IAAG3F,CAAC,CAACE,IAAK,EAAC,CAAC;IAC5C,IAAI,CAACzV,CAAC,EACJ;IACF,IAAIC,CAAC,GAAG8b,CAAC,CAACjc,CAAC,CAAC;IACZ,IAAIA,CAAC,CAACmf,aAAa,KAAK,IAAI,EAC1B;IACF,MAAMpe,CAAC,GAAG4a,CAAC,CAAC3b,CAAC,CAAC;IACde,CAAC,KAAK,IAAI,KAAKZ,CAAC,KAAK,IAAI,KAAKA,CAAC,GAAG,IAAI,CAAC0c,QAAQ,CAAC1G,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEpV,CAAC,CAACD,OAAO,CAAEqC,CAAC,IAAK;MACnFhD,CAAC,CAAC1B,WAAW,CAAC0E,CAAC,CAAC;IAClB,CAAC,CAAC,EAAEnD,CAAC,CAACvB,WAAW,CAAC0B,CAAC,CAAC,CAAC,EAAED,CAAC,CAACkf,KAAK,CAACpf,CAAC,CAAC,EAAEoc,CAAC,CAACpc,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEkc,CAAC,CAAChc,CAAC,CAAC;EACnD;EACA;AACF;AACA;AACA;EACE2e,SAASA,CAAC7e,CAAC,EAAE;IACX,MAAME,CAAC,GAAG4b,CAAC,CAAC9b,CAAC,CAAC;MAAEG,CAAC,GAAG,IAAI,CAACyc,KAAK;MAAE7b,CAAC,GAAG,IAAI,CAAC4b,GAAG,CAAC+B,MAAM,CAACC,oBAAoB,CAAC,CAAC;IAC1E,IAAIze,CAAC,CAACmF,MAAM,KAAK,CAAC,EAAE;MAClB,MAAM7B,CAAC,GAAGtD,CAAC,CAAC,CAAC,CAAC;MACd,IAAI,CAAC4e,WAAW,CAACtb,CAAC,CAAC,EAAE4Y,CAAC,CAACpc,CAAC,EAAE,CAAC,CAAC,CAAC;IAC/B;IACA,IAAIA,CAAC,CAAC6b,sBAAsB,KAAK,IAAI,IAAI7b,CAAC,CAACgQ,UAAU,KAAK,IAAI,CAACyM,WAAW,EAAE;MAC1E,IAAI,CAACmC,yBAAyB,CAAC7d,CAAC,CAAC;MACjC;IACF;IACA,MAAMoC,CAAC,GAAGwY,CAAC,CAAC3b,CAAC,CAAC;IACd,IAAImD,CAAC,KAAK,IAAI,EACZ;IACF,MAAME,CAAC,GAAG,IAAI,CAACwZ,QAAQ,CAAC1G,aAAa,CAAC,CAAC,CAAC,CAAC;IACzChT,CAAC,CAACrC,OAAO,CAAE0C,CAAC,IAAK;MACfH,CAAC,CAAC5E,WAAW,CAAC+E,CAAC,CAAC;IAClB,CAAC,CAAC;IACF,MAAMD,CAAC,GAAG,IAAI,CAAC2W,IAAI,CAAC7W,CAAC,CAAC;IACtBE,CAAC,CAAC2Z,IAAI,CAACO,KAAK,GAAG,IAAI,CAACf,IAAI,CAACzW,KAAK,IAAI,SAAS,GAAG,CAAC,GAAG,KAAK,CAAC,EAAE,IAAI,CAAC0W,GAAG,CAAC+B,MAAM,CAACW,MAAM,CAAClf,CAAC,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,CAAC,CAACmf,IAAI,EAAE/b,CAAC,EAAE,IAAI,CAAC0S,MAAM,EAAElV,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC6d,yBAAyB,CAAC7d,CAAC,GAAG,CAAC,CAAC,EAAEsC,CAAC,CAACsX,MAAM,CAAC,CAAC;EACzL;EACA;AACF;AACA;AACA;EACEoE,SAASA,CAAC/e,CAAC,EAAE;IACX,MAAM,CAACE,CAAC,EAAEC,CAAC,CAAC,GAAGyW,CAAC,CAAC2B,qBAAqB,CAAC,CAAC;IACxC,IAAIrY,CAAC,KAAK,IAAI,EACZ;IACF,MAAMa,CAAC,GAAGob,CAAC,CAACnc,CAAC,CAAC;IACd,IAAImD,CAAC;IACLpC,CAAC,KAAK,IAAI,GAAGoC,CAAC,GAAG,EAAE,GAAGA,CAAC,GAAGyT,CAAC,CAACI,uBAAuB,CAACjW,CAAC,EAAEb,CAAC,EAAEC,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;IACzE,MAAMkD,CAAC,GAAG4Y,CAAC,CAACjc,CAAC,CAAC;MAAEuD,CAAC,GAAG,IAAI,CAAC6S,UAAU,CAACjT,CAAC,CAAC;IACtCnD,CAAC,IAAI,IAAI,IAAIA,CAAC,CAACof,KAAK,CAAC7b,CAAC,CAAC,EAAEF,CAAC,IAAIE,CAAC,CAAC9E,WAAW,CAAC4E,CAAC,CAAC,EAAE+Y,CAAC,CAAC7Y,CAAC,CAAC;EACtD;EACA;AACF;AACA;AACA;AACA;AACA;EACE2b,qBAAqBA,CAAClf,CAAC,EAAE;IACvB,MAAME,CAAC,GAAGF,CAAC,CAAC6b,sBAAsB;MAAE1b,CAAC,GAAGH,CAAC,CAACgQ,UAAU;IACpD,IAAI7P,CAAC,KAAK,IAAI,IAAI,CAACwW,CAAC,CAACxW,CAAC,CAAC,EACrB;IACF,MAAMY,CAAC,GAAGZ,CAAC,CAACib,OAAO,CAAE,IAAG3F,CAAC,CAACE,IAAK,EAAC,CAAC;IACjC,IAAI,CAACzV,CAAC,IAAI,CAACa,CAAC,IAAIb,CAAC,IAAI,CAACyW,CAAC,CAACzW,CAAC,CAAC,EACxB;IACF,IAAIiD,CAAC;IACL,IAAIjD,CAAC,EAAE;MACL,MAAMuU,CAAC,GAAGqH,CAAC,CAAC5b,CAAC,EAAE,CAAC,CAAC,CAAC;MAClBuU,CAAC,CAACpP,MAAM,KAAK,CAAC,IAAIoP,CAAC,CAACpP,MAAM,KAAK,CAAC,GAAGlC,CAAC,GAAGsR,CAAC,CAACA,CAAC,CAACpP,MAAM,GAAG,CAAC,CAAC,GAAGlC,CAAC,GAAGjD,CAAC;IAChE,CAAC,MACCiD,CAAC,GAAGpC,CAAC;IACP,MAAMsC,CAAC,GAAG,IAAI,CAACwZ,QAAQ,CAACxG,cAAc,CAACrW,CAAC,CAAC;IACzC,IAAI,CAACmD,CAAC,EACJ;IACFiZ,CAAC,CAACjZ,CAAC,EAAE,CAAC,CAAC,CAAC;IACR,MAAMI,CAAC,GAAG4Y,CAAC,CAAChZ,CAAC,CAAC;IACd,IAAII,CAAC,KAAK,IAAI,EACZ;IACFA,CAAC,CAAC0a,kBAAkB,CAAC,WAAW,EAAE5a,CAAC,CAAC;IACpC,MAAMG,CAAC,GAAGsY,CAAC,CAAC9b,CAAC,CAAC;IACd,IAAIwD,CAAC,CAAC6B,MAAM,KAAK,CAAC,EAAE;MAClBrF,CAAC,CAAC2a,MAAM,CAAC,CAAC,EAAEuB,CAAC,CAAC/Y,CAAC,CAAC;MAChB;IACF;IACA,MAAMM,CAAC,GAAGvD,CAAC,IAAIa,CAAC;MAAE2J,CAAC,GAAGuR,CAAC,CAACxY,CAAC,CAAC,IAAI,IAAI,CAACoZ,QAAQ,CAAC1G,aAAa,CAAC,CAAC,CAAC,CAAC;IAC7DjW,CAAC,GAAGsD,CAAC,CAAC1C,OAAO,CAAE2T,CAAC,IAAK;MACnB/J,CAAC,CAACjM,WAAW,CAACgW,CAAC,CAAC;IAClB,CAAC,CAAC,GAAGjR,CAAC,CAAC1C,OAAO,CAAE2T,CAAC,IAAK;MACpB/J,CAAC,CAAC4J,OAAO,CAACG,CAAC,CAAC;IACd,CAAC,CAAC,EAAEwH,CAAC,CAACxY,CAAC,CAAC,KAAK,IAAI,IAAIN,CAAC,CAAC1E,WAAW,CAACiM,CAAC,CAAC,EAAE1K,CAAC,CAAC2a,MAAM,CAAC,CAAC;EACnD;EACA;AACF;AACA;AACA;EACE6C,MAAMA,CAACxd,CAAC,EAAE;IACR,IAAImD,CAAC;IACLnD,CAAC,CAACue,eAAe,CAAC,CAAC,EAAEve,CAAC,CAACwe,cAAc,CAAC,CAAC;IACvC,MAAMte,CAAC,GAAG,IAAI,CAACoc,WAAW;IAC1B,IAAI,CAACpc,CAAC,EACJ;IACF,IAAI,CAAC,CAACiD,CAAC,GAAG,IAAI,CAAC8S,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG9S,CAAC,CAACoc,QAAQ,MAAM,KAAK,CAAC,EAAE;MAChE,MAAMlc,CAAC,GAAG,IAAI,CAACmZ,gBAAgB;MAC/B,IAAInZ,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAK,IAAI,CAAC4S,MAAM,CAACsJ,QAAQ,EAC1C;IACJ;IACA,MAAMpf,CAAC,GAAGD,CAAC,CAACsf,eAAe;IAC3B,IAAIrf,CAAC,KAAK,IAAI,IAAI,CAACwW,CAAC,CAACxW,CAAC,CAAC,EACrB;IACF,MAAMY,CAAC,GAAGkb,CAAC,CAAC9b,CAAC,CAAC;IACd,IAAIY,CAAC,EACHA,CAAC,CAACtC,WAAW,CAACyB,CAAC,CAAC,EAAE4b,CAAC,CAAC5b,CAAC,CAAC,CAACY,OAAO,CAAEyC,CAAC,IAAK;MACpCxC,CAAC,CAACtC,WAAW,CAAC8E,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,KACA;MACH,MAAMF,CAAC,GAAG,IAAI,CAACwZ,QAAQ,CAAC1G,aAAa,CAAC,CAAC,CAAC,CAAC;MACzC9S,CAAC,CAAC5E,WAAW,CAACyB,CAAC,CAAC,EAAE4b,CAAC,CAAC5b,CAAC,CAAC,CAACY,OAAO,CAAE0C,CAAC,IAAK;QACpCH,CAAC,CAAC5E,WAAW,CAAC+E,CAAC,CAAC;MAClB,CAAC,CAAC,EAAErD,CAAC,CAAC1B,WAAW,CAAC4E,CAAC,CAAC;IACtB;IACA6Y,CAAC,CAAChc,CAAC,CAAC,EAAEkc,CAAC,CAAClc,CAAC,EAAE,CAAC,CAAC,CAAC;EAChB;EACA;AACF;AACA;AACA;AACA;EACE0e,yBAAyBA,CAAC5e,CAAC,EAAEE,CAAC,EAAE;IAC9B,IAAIC,CAAC;IACL,MAAMY,CAAC,GAAG,IAAI,CAACub,WAAW;MAAEnZ,CAAC,GAAGpC,CAAC,KAAK,IAAI,GAAG,IAAI,CAAC8b,QAAQ,CAACxG,cAAc,CAACtV,CAAC,CAAC,GAAG,EAAE;IACjFb,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,CAACyc,GAAG,CAAC+B,MAAM,CAACe,MAAM,CAAC,CAAC,EAAEzf,CAAC,KAAK,KAAK,CAAC,GAAGG,CAAC,GAAG,IAAI,CAACwc,GAAG,CAAC+B,MAAM,CAACW,MAAM,CAAC,KAAK,CAAC,EAAE;MAAEK,IAAI,EAAEvc;IAAE,CAAC,EAAE,KAAK,CAAC,EAAEnD,CAAC,CAAC,GAAGG,CAAC,GAAG,IAAI,CAACwc,GAAG,CAAC+B,MAAM,CAACW,MAAM,CAAC,CAAC,EAAEte,CAAC,IAAI,IAAI,IAAIA,CAAC,CAAC4Z,MAAM,CAAC,CAAC,EAAE,IAAI,CAACgC,GAAG,CAACgD,KAAK,CAACC,UAAU,CAACzf,CAAC,EAAE,OAAO,CAAC;EAChN;EACA;AACF;AACA;AACA;AACA;EACE8e,8BAA8BA,CAAA,EAAG;IAC/B,MAAMjf,CAAC,GAAG,IAAI,CAACsc,WAAW;IAC1B,IAAItc,CAAC,KAAK,IAAI,EACZ;IACF,MAAME,CAAC,GAAG4b,CAAC,CAAC9b,CAAC,CAAC;IACd,IAAIE,CAAC,CAACmF,MAAM,KAAK,CAAC,EAAE;MAClB,MAAMhC,CAAC,GAAGnD,CAAC,CAAC,CAAC,CAAC;MACd,IAAI,CAAC4e,WAAW,CAACzb,CAAC,CAAC,EAAE+Y,CAAC,CAACpc,CAAC,CAAC;IAC3B;IACA,MAAMG,CAAC,GAAGwb,CAAC,CAAC3b,CAAC,CAAC;MAAEe,CAAC,GAAG,IAAI,CAAC4b,GAAG,CAAC+B,MAAM,CAACC,oBAAoB,CAAC,CAAC;MAAExb,CAAC,GAAGhD,CAAC,KAAK,IAAI;IAC1E,IAAI,CAACye,yBAAyB,CAAC7d,CAAC,EAAEoC,CAAC,CAAC;EACtC;EACA;AACF;AACA;AACA;AACA;AACA;EACEiT,UAAUA,CAACpW,CAAC,EAAEE,CAAC,EAAE;IACf,MAAMC,CAAC,GAAGD,CAAC,IAAI,IAAI,CAAC2c,QAAQ,CAACrG,kBAAkB,CAAC,CAAC;IACjD,QAAQ,CAAC,CAAC;MACR,KAAK,IAAI,CAACqG,QAAQ,YAAY/G,CAAC;QAC7B,OAAO,IAAI,CAAC+G,QAAQ,CAACzG,UAAU,CAACpW,CAAC,EAAEG,CAAC,CAAC;MACvC,KAAK,IAAI,CAAC0c,QAAQ,YAAYpG,CAAC;QAC7B,OAAO,IAAI,CAACoG,QAAQ,CAACzG,UAAU,CAACpW,CAAC,EAAEG,CAAC,CAAC;MACvC;QACE,OAAO,IAAI,CAAC0c,QAAQ,CAACzG,UAAU,CAACpW,CAAC,EAAEG,CAAC,CAAC;IACzC;EACF;EACA;AACF;AACA;AACA;AACA;EACE6c,WAAWA,CAAChd,CAAC,EAAEE,CAAC,EAAE;IAChBF,CAAC,CAACc,OAAO,CAAEX,CAAC,IAAK;MACf,IAAIgD,CAAC;MACL,MAAMpC,CAAC,GAAG,IAAI,CAACqV,UAAU,CAACjW,CAAC,CAAC8c,OAAO,EAAE9c,CAAC,CAAC+c,IAAI,CAAC;MAC5C,IAAIhd,CAAC,CAACzB,WAAW,CAACsC,CAAC,CAAC,EAAEZ,CAAC,CAAC4c,KAAK,CAAC1X,MAAM,EAAE;QACpC,MAAMhC,CAAC,GAAG,CAACF,CAAC,GAAG,IAAI,CAAC0Z,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG1Z,CAAC,CAACgT,aAAa,CAAC,CAAC,CAAC,CAAC;QACpE,IAAI,CAAC6G,WAAW,CAAC7c,CAAC,CAAC4c,KAAK,EAAE1Z,CAAC,CAAC,EAAEtC,CAAC,CAACtC,WAAW,CAAC4E,CAAC,CAAC;MAChD;IACF,CAAC,CAAC;EACJ;AACF;AACA,MAAMwc,CAAC,GAAG;EACRnK,OAAO,EAAG,GAAEF,CAAE,mBAAkB;EAChCsK,KAAK,EAAG,GAAEtK,CAAE,0BAAyB;EACrCuK,8BAA8B,EAAG,GAAEvK,CAAE;AACvC,CAAC;AACD,SAASwK,EAAEA,CAACzhB,CAAC,EAAE;EAAEqC,KAAK,EAAEZ,CAAC;EAAEigB,WAAW,EAAE/f,CAAC;EAAEggB,UAAU,EAAE/f,CAAC;EAAEggB,QAAQ,EAAEpf;AAAE,CAAC,EAAE;EACvE,MAAMoC,CAAC,GAAGvE,CAAC,CAAC2O,IAAI,CAAC,KAAK,EAAEsS,CAAC,CAACnK,OAAO,CAAC;IAAErS,CAAC,GAAGzE,CAAC,CAAC2O,IAAI,CAAC,OAAO,EAAEsS,CAAC,CAACC,KAAK,EAAE;MAC/DG,WAAW,EAAE/f,CAAC;MACd;AACJ;AACA;AACA;AACA;MACIkgB,QAAQ,EAAE,CAAC,CAAC;MACZ;AACJ;AACA;MACIxf,KAAK,EAAEZ;IACT,CAAC,CAAC;EACF,KAAK,MAAMuD,CAAC,IAAIpD,CAAC,EACfkD,CAAC,CAACgd,YAAY,CAAC9c,CAAC,EAAEpD,CAAC,CAACoD,CAAC,CAAC,CAAC;EACzB,OAAOJ,CAAC,CAAC1E,WAAW,CAAC4E,CAAC,CAAC,EAAEA,CAAC,CAAC6X,gBAAgB,CAAC,OAAO,EAAE,MAAM;IACzDna,CAAC,KAAK,KAAK,CAAC,KAAKsC,CAAC,CAACzC,KAAK,GAAGG,CAAC,CAACsC,CAAC,CAACzC,KAAK,CAAC,CAAC;IACtC,MAAM2C,CAAC,GAAGF,CAAC,CAACid,aAAa,CAAC,CAAC;IAC3B,CAAC/c,CAAC,IAAI,CAACJ,CAAC,CAACuK,SAAS,CAAC2N,QAAQ,CAACwE,CAAC,CAACE,8BAA8B,CAAC,IAAI5c,CAAC,CAACuK,SAAS,CAACvD,GAAG,CAAC0V,CAAC,CAACE,8BAA8B,CAAC,EAAExc,CAAC,IAAIJ,CAAC,CAACuK,SAAS,CAAC2N,QAAQ,CAACwE,CAAC,CAACE,8BAA8B,CAAC,IAAI5c,CAAC,CAACuK,SAAS,CAACiN,MAAM,CAACkF,CAAC,CAACE,8BAA8B,CAAC,EAAExc,CAAC,IAAIhF,CAAC,CAAC8E,CAAC,CAACzC,KAAK,CAAC;EAC1P,CAAC,CAAC,EAAEuC,CAAC;AACP;AACA,MAAMod,CAAC,GAAG,eAAgB,IAAIC,GAAG,CAAC;EAChC;AACF;AACA;EACE,CAAC,SAAS,EAAE,SAAS,CAAC;EACtB;AACF;AACA;EACE,CAAC,aAAa,EAAE,aAAa,CAAC;EAC9B;AACF;AACA;EACE,CAAC,aAAa,EAAE,aAAa,CAAC;EAC9B;AACF;AACA;EACE,CAAC,aAAa,EAAE,aAAa,CAAC;EAC9B;AACF;AACA;EACE,CAAC,aAAa,EAAE,aAAa,CAAC,CAC/B,CAAC;EAAEC,EAAE,GAAG,eAAgB,IAAID,GAAG,CAAC;EAC/B;AACF;AACA;EACE,CAAC,SAAS,EAAErhB,EAAE,CAAC;EACf;AACF;AACA;EACE,CAAC,aAAa,EAAEC,EAAE,CAAC;EACnB;AACF;AACA;EACE,CAAC,aAAa,EAAEC,EAAE,CAAC;EACnB;AACF;AACA;EACE,CAAC,aAAa,EAAEE,EAAE,CAAC;EACnB;AACF;AACA;EACE,CAAC,aAAa,EAAED,EAAE,CAAC,CACpB,CAAC;AACF,SAASohB,EAAEA,CAACniB,CAAC,EAAE;EACb,OAAOA,CAAC,CAACqH,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;AAC9B;AACA,SAAS+a,EAAEA,CAACpiB,CAAC,EAAE;EACb,OAAO,OAAOA,CAAC,CAACwe,KAAK,CAAC,CAAC,CAAC,IAAI,QAAQ;AACtC;AACA,SAAS6D,EAAEA,CAACriB,CAAC,EAAE;EACb,OAAO,EAAE,MAAM,IAAIA,CAAC,CAAC;AACvB;AACA,SAASsiB,EAAEA,CAACtiB,CAAC,EAAE;EACb,OAAO,OAAOA,CAAC,CAACwe,KAAK,CAAC,CAAC,CAAC,IAAI,QAAQ,IAAI,MAAM,IAAIxe,CAAC,CAACwe,KAAK,CAAC,CAAC,CAAC,IAAI,SAAS,IAAIxe,CAAC,CAACwe,KAAK,CAAC,CAAC,CAAC,IAAI,OAAOxe,CAAC,CAACwe,KAAK,CAAC,CAAC,CAAC,CAAC2C,IAAI,IAAI,QAAQ,IAAI,OAAOnhB,CAAC,CAACwe,KAAK,CAAC,CAAC,CAAC,CAACxB,OAAO,IAAI,SAAS;AACzK;AACA,SAASuF,EAAEA,CAACviB,CAAC,EAAE;EACb,MAAMyB,CAAC,GAAG,EAAE;EACZ,OAAO2gB,EAAE,CAACpiB,CAAC,CAAC,IAAIA,CAAC,CAACwe,KAAK,CAACjc,OAAO,CAAEZ,CAAC,IAAK;IACrCF,CAAC,CAACyS,IAAI,CAAC;MACLwK,OAAO,EAAE/c,CAAC;MACVgd,IAAI,EAAE,CAAC,CAAC;MACRH,KAAK,EAAE;IACT,CAAC,CAAC;EACJ,CAAC,CAAC,EAAE;IACF9W,KAAK,EAAE1H,CAAC,CAAC0H,KAAK;IACdiX,IAAI,EAAE,CAAC,CAAC;IACRH,KAAK,EAAE/c;EACT,CAAC,IAAI6gB,EAAE,CAACtiB,CAAC,CAAC,IAAIA,CAAC,CAACwe,KAAK,CAACjc,OAAO,CAAEZ,CAAC,IAAK;IACnCF,CAAC,CAACyS,IAAI,CAAC;MACLwK,OAAO,EAAE/c,CAAC,CAACwf,IAAI;MACfxC,IAAI,EAAE;QACJ3B,OAAO,EAAErb,CAAC,CAACqb;MACb,CAAC;MACDwB,KAAK,EAAE;IACT,CAAC,CAAC;EACJ,CAAC,CAAC,EAAE;IACF9W,KAAK,EAAE,WAAW;IAClBiX,IAAI,EAAE,CAAC,CAAC;IACRH,KAAK,EAAE/c;EACT,CAAC,IAAI4gB,EAAE,CAACriB,CAAC,CAAC,GAAG;IACX0H,KAAK,EAAE1H,CAAC,CAAC0H,KAAK;IACdiX,IAAI,EAAE,CAAC,CAAC;IACRH,KAAK,EAAExe,CAAC,CAACwe;EACX,CAAC,GAAGxe,CAAC;AACP;AACA,MAAMwiB,CAAC,CAAC;EACN;AACF;AACA;EACE,WAAWC,mBAAmBA,CAAA,EAAG;IAC/B,OAAO,CAAC,CAAC;EACX;EACA;AACF;AACA;EACE,WAAWC,gBAAgBA,CAAA,EAAG;IAC5B,OAAO,CAAC,CAAC;EACX;EACA;AACF;AACA;AACA;AACA;EACE,WAAWC,OAAOA,CAAA,EAAG;IACnB,OAAO,CACL;MACEC,IAAI,EAAEliB,EAAE;MACRmiB,KAAK,EAAE,gBAAgB;MACvB1E,IAAI,EAAE;QACJzW,KAAK,EAAE;MACT;IACF,CAAC,EACD;MACEkb,IAAI,EAAEjiB,EAAE;MACRkiB,KAAK,EAAE,cAAc;MACrB1E,IAAI,EAAE;QACJzW,KAAK,EAAE;MACT;IACF,CAAC,EACD;MACEkb,IAAI,EAAEniB,EAAE;MACRoiB,KAAK,EAAE,WAAW;MAClB1E,IAAI,EAAE;QACJzW,KAAK,EAAE;MACT;IACF,CAAC,CACF;EACH;EACA;AACF;AACA;AACA;EACE,WAAW4X,WAAWA,CAAA,EAAG;IACvB,OAAO;MACLC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI;IACzB,CAAC;EACH;EACA;AACF;AACA;EACE,WAAWuD,gBAAgBA,CAAA,EAAG;IAC5B,OAAO;MACLC,MAAM,EAAGthB,CAAC,IAAK+gB,CAAC,CAACQ,aAAa,CAACvhB,CAAC,CAAC;MACjCwhB,MAAM,EAAEA,CAACxhB,CAAC,EAAEE,CAAC,MAAM;QACjBgd,IAAI,EAAE,CAAC,CAAC;QACRH,KAAK,EAAE,CACL;UACEE,OAAO,EAAEjd,CAAC;UACVkd,IAAI,EAAE,CAAC,CAAC;UACRH,KAAK,EAAE;QACT,CAAC,CACF;QACD9W,KAAK,EAAE,CAAC/F,CAAC,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,CAAC,CAACuhB,YAAY,MAAM,KAAK,CAAC,GAAGvhB,CAAC,CAACuhB,YAAY,GAAG;MAC7E,CAAC;IACH,CAAC;EACH;EACA;AACF;AACA;EACE,IAAIC,SAASA,CAAA,EAAG;IACd,OAAO,IAAI,CAAChF,IAAI,CAACzW,KAAK,IAAI,IAAI,CAAC0b,gBAAgB;EACjD;EACA;AACF;AACA;AACA;EACE,IAAID,SAASA,CAAC1hB,CAAC,EAAE;IACf,IAAIG,CAAC;IACL,IAAI,CAACuc,IAAI,CAACzW,KAAK,GAAGjG,CAAC,EAAE,IAAI,CAAC4hB,sBAAsB,CAAC,CAAC;IAClD,MAAM1hB,CAAC,GAAG,IAAI,CAAC2hB,IAAI,CAAC/E,MAAM,CAAC,CAAC;IAC5B,CAAC3c,CAAC,GAAG,IAAI,CAAC2hB,WAAW,KAAK,IAAI,IAAI3hB,CAAC,CAAC4hB,WAAW,CAAC7hB,CAAC,CAAC,EAAE,IAAI,CAAC4hB,WAAW,GAAG5hB,CAAC;EAC1E;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEK,WAAWA,CAAC;IAAEmc,IAAI,EAAE1c,CAAC;IAAEiW,MAAM,EAAE/V,CAAC;IAAEyc,GAAG,EAAExc,CAAC;IAAE+V,QAAQ,EAAEnV,CAAC;IAAE6b,KAAK,EAAEzZ;EAAE,CAAC,EAAE;IACjE,IAAII,CAAC;IACL,IAAI,CAACoZ,GAAG,GAAGxc,CAAC,EAAE,IAAI,CAAC+V,QAAQ,GAAGnV,CAAC,EAAE,IAAI,CAACkV,MAAM,GAAG/V,CAAC,EAAE,IAAI,CAAC0c,KAAK,GAAGzZ,CAAC,EAAE,IAAI,CAACwe,gBAAgB,GAAG,CAAC,CAACpe,CAAC,GAAG,IAAI,CAAC0S,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG1S,CAAC,CAACke,YAAY,KAAK,WAAW,EAAE,IAAI,CAACO,mBAAmB,GAAG,IAAI,CAAC/L,MAAM,CAACgM,YAAY,IAAI7f,KAAK,CAAC4K,IAAI,CAACuT,CAAC,CAAC2B,MAAM,CAAC,CAAC,CAAC;IAC7O,MAAM7e,CAAC,GAAG;MACR4C,KAAK,EAAE,IAAI,CAAC0b,gBAAgB;MAC5BzE,IAAI,EAAE,CAAC,CAAC;MACRH,KAAK,EAAE;IACT,CAAC;IACD,IAAI,CAACL,IAAI,GAAGhc,MAAM,CAACG,IAAI,CAACb,CAAC,CAAC,CAACqF,MAAM,GAAGyb,EAAE,CAAC9gB,CAAC,CAAC,GAAGqD,CAAC,EAAE,IAAI,CAACqe,SAAS,KAAK,SAAS,IAAI,IAAI,CAAChF,IAAI,CAACQ,IAAI,CAACS,WAAW,KAAK,KAAK,CAAC,KAAK,IAAI,CAACjB,IAAI,CAACQ,IAAI,CAACS,WAAW,GAAG,SAAS,CAAC,EAAE,IAAI,CAACiE,sBAAsB,CAAC,CAAC;EACjM;EACA;AACF;AACA;AACA;AACA;EACE,OAAOL,aAAaA,CAACvhB,CAAC,EAAE;IACtB,OAAOA,CAAC,CAAC+c,KAAK,CAACxb,GAAG,CAAErB,CAAC,IAAM,GAAEA,CAAC,CAAC+c,OAAQ,IAAG8D,CAAC,CAACQ,aAAa,CAACrhB,CAAC,CAAE,EAAC,CAAC,CAACuB,IAAI,CAAC,EAAE,CAAC;EAC1E;EACA;AACF;AACA;AACA;EACEqb,MAAMA,CAAA,EAAG;IACP,OAAO,IAAI,CAACgF,WAAW,GAAG,IAAI,CAACD,IAAI,CAAC/E,MAAM,CAAC,CAAC,EAAE,IAAI,CAACgF,WAAW;EAChE;EACA;AACF;AACA;AACA;EACE5H,IAAIA,CAAA,EAAG;IACL,OAAO,IAAI,CAACwC,IAAI,GAAG,IAAI,CAACmF,IAAI,CAAC3H,IAAI,CAAC,CAAC,EAAE,IAAI,CAACwC,IAAI;EAChD;EACA;AACF;AACA;AACA;EACEqB,KAAKA,CAAC/d,CAAC,EAAE;IACP,IAAI,CAAC6hB,IAAI,CAAC9D,KAAK,CAAC/d,CAAC,CAAC;EACpB;EACA;AACF;AACA;AACA;EACEmiB,cAAcA,CAAA,EAAG;IACf,MAAMniB,CAAC,GAAG,CACR;MACEoiB,KAAK,EAAE,IAAI,CAACzF,GAAG,CAAC0F,IAAI,CAACriB,CAAC,CAAC,WAAW,CAAC;MACnCmhB,IAAI,EAAEliB,EAAE;MACRqjB,eAAe,EAAE,CAAC,CAAC;MACnBC,QAAQ,EAAE,IAAI,CAACb,SAAS,IAAI,WAAW;MACvCc,UAAU,EAAEA,CAAA,KAAM;QAChB,IAAI,CAACd,SAAS,GAAG,WAAW;MAC9B;IACF,CAAC,EACD;MACEU,KAAK,EAAE,IAAI,CAACzF,GAAG,CAAC0F,IAAI,CAACriB,CAAC,CAAC,SAAS,CAAC;MACjCmhB,IAAI,EAAEjiB,EAAE;MACRojB,eAAe,EAAE,CAAC,CAAC;MACnBC,QAAQ,EAAE,IAAI,CAACb,SAAS,IAAI,SAAS;MACrCc,UAAU,EAAEA,CAAA,KAAM;QAChB,IAAI,CAACd,SAAS,GAAG,SAAS;MAC5B;IACF,CAAC,EACD;MACEU,KAAK,EAAE,IAAI,CAACzF,GAAG,CAAC0F,IAAI,CAACriB,CAAC,CAAC,WAAW,CAAC;MACnCmhB,IAAI,EAAEniB,EAAE;MACRsjB,eAAe,EAAE,CAAC,CAAC;MACnBC,QAAQ,EAAE,IAAI,CAACb,SAAS,IAAI,WAAW;MACvCc,UAAU,EAAEA,CAAA,KAAM;QAChB,IAAI,CAACd,SAAS,GAAG,WAAW;MAC9B;IACF,CAAC,CACF;IACD,IAAI,IAAI,CAACA,SAAS,KAAK,SAAS,EAAE;MAChC,MAAMxhB,CAAC,GAAG8f,EAAE,CACT7c,CAAC,IAAK,IAAI,CAACua,eAAe,CAAC+E,MAAM,CAACtf,CAAC,CAAC,CAAC,EACtC;UACEvC,KAAK,EAAE8hB,MAAM,CAAC,IAAI,CAAChG,IAAI,CAACQ,IAAI,CAACO,KAAK,IAAI,CAAC,CAAC;UACxCwC,WAAW,EAAE,EAAE;UACfC,UAAU,EAAE;YACVyC,QAAQ,EAAE;UACZ,CAAC;UACDxC,QAAQ,EAAGhd,CAAC,IAAKud,EAAE,CAACvd,CAAC;QACvB,CACF,CAAC;QAAEhD,CAAC,GAAG,CACL;UACEiiB,KAAK,EAAE,IAAI,CAACzF,GAAG,CAAC0F,IAAI,CAACriB,CAAC,CAAC,YAAY,CAAC;UACpCmhB,IAAI,EAAE3hB,EAAE;UACRyN,QAAQ,EAAE;YACR8P,KAAK,EAAE,CACL;cACE6F,OAAO,EAAE1iB,CAAC;cACV;cACAmE,IAAI,EAAE;YACR,CAAC;UAEL;QACF,CAAC,CACF;QAAEtD,CAAC,GAAG;UACLqhB,KAAK,EAAE,IAAI,CAACzF,GAAG,CAAC0F,IAAI,CAACriB,CAAC,CAAC,cAAc,CAAC;UACtCmhB,IAAI,EAAEV,EAAE,CAACxf,GAAG,CAAC,IAAI,CAACyb,IAAI,CAACQ,IAAI,CAACS,WAAW,CAAC;UACxC1Q,QAAQ,EAAE;YACR8P,KAAK,EAAE;UACT;QACF,CAAC;MACDwD,CAAC,CAACzf,OAAO,CAAC,CAACqC,CAAC,EAAEE,CAAC,KAAK;QAClB,MAAME,CAAC,GAAGgd,CAAC,CAACtf,GAAG,CAACoC,CAAC,CAAC;QAClB,IAAI,CAAC2e,mBAAmB,CAACjgB,QAAQ,CAACwB,CAAC,CAAC,IAAIxC,CAAC,CAACkM,QAAQ,CAAC8P,KAAK,CAACtK,IAAI,CAAC;UAC5D2O,KAAK,EAAE,IAAI,CAACzE,GAAG,CAAC0F,IAAI,CAACriB,CAAC,CAACqD,CAAC,CAAC;UACzB8d,IAAI,EAAEV,EAAE,CAACxf,GAAG,CAACsC,CAAC,CAAC;UACfgf,QAAQ,EAAE,IAAI,CAAC7F,IAAI,CAACQ,IAAI,CAACS,WAAW,KAAK4C,CAAC,CAACtf,GAAG,CAACoC,CAAC,CAAC;UACjDif,eAAe,EAAE,CAAC,CAAC;UACnBE,UAAU,EAAEA,CAAA,KAAM;YAChB,IAAI,CAAC5E,cAAc,CAAC2C,CAAC,CAACtf,GAAG,CAACoC,CAAC,CAAC,CAAC;UAC/B;QACF,CAAC,CAAC;MACJ,CAAC,CAAC,EAAEtC,CAAC,CAACkM,QAAQ,CAAC8P,KAAK,CAAC1X,MAAM,GAAG,CAAC,IAAIlF,CAAC,CAACsS,IAAI,CAAC1R,CAAC,CAAC,EAAEf,CAAC,CAACyS,IAAI,CAAC;QAAEpO,IAAI,EAAE;MAAY,CAAC,EAAE,GAAGlE,CAAC,CAAC;IACnF;IACA,OAAOH,CAAC;EACV;EACA;AACF;AACA;AACA;EACEke,OAAOA,CAACle,CAAC,EAAE;IACT,MAAM;MAAE8B,OAAO,EAAE5B;IAAE,CAAC,GAAGF,CAAC,CAACme,MAAM,CAACzB,IAAI;IACpC,QAAQxc,CAAC;MACP,KAAK,IAAI;QACP,IAAI,CAACwhB,SAAS,GAAG,SAAS;QAC1B;MACF,KAAK,IAAI;MACT,KAAK,IAAI;QACP,IAAI,CAACA,SAAS,GAAG,WAAW;IAChC;IACA,IAAI,CAACG,IAAI,CAAC3D,OAAO,CAACle,CAAC,CAAC;EACtB;EACA;AACF;AACA;AACA;EACEoe,YAAYA,CAACpe,CAAC,EAAE;IACd,OAAO,IAAI,CAAC6hB,IAAI,CAACzD,YAAY,CAACpe,CAAC,CAAC;EAClC;EACA;AACF;AACA;AACA;EACE4d,cAAcA,CAAC5d,CAAC,EAAE;IAChB,IAAIE,CAAC;IACL,CAACA,CAAC,GAAG,IAAI,CAAC2hB,IAAI,KAAK,IAAI,IAAI3hB,CAAC,CAAC0d,cAAc,CAAC5d,CAAC,CAAC,EAAE,IAAI,CAAC0c,IAAI,CAACQ,IAAI,CAACS,WAAW,GAAG3d,CAAC;EAChF;EACA;AACF;AACA;AACA;EACE0d,eAAeA,CAAC1d,CAAC,EAAE;IACjB,IAAIE,CAAC;IACL,CAACA,CAAC,GAAG,IAAI,CAAC2hB,IAAI,KAAK,IAAI,IAAI3hB,CAAC,CAACwd,eAAe,CAAC1d,CAAC,CAAC,EAAE,IAAI,CAAC0c,IAAI,CAACQ,IAAI,CAACO,KAAK,GAAGzd,CAAC;EAC3E;EACA;AACF;AACA;EACE4hB,sBAAsBA,CAAA,EAAG;IACvB,QAAQ,IAAI,CAACF,SAAS;MACpB,KAAK,SAAS;QACZ,IAAI,CAACG,IAAI,GAAG,IAAIxF,CAAC,CACf;UACEK,IAAI,EAAE,IAAI,CAACA,IAAI;UACfxG,QAAQ,EAAE,IAAI,CAACA,QAAQ;UACvByG,GAAG,EAAE,IAAI,CAACA,GAAG;UACb1G,MAAM,EAAE,IAAI,CAACA,MAAM;UACnB2G,KAAK,EAAE,IAAI,CAACA;QACd,CAAC,EACD,IAAI9G,CAAC,CAAC,IAAI,CAACI,QAAQ,EAAE,IAAI,CAACD,MAAM,CAClC,CAAC;QACD;MACF,KAAK,WAAW;QACd,IAAI,CAAC4L,IAAI,GAAG,IAAIxF,CAAC,CACf;UACEK,IAAI,EAAE,IAAI,CAACA,IAAI;UACfxG,QAAQ,EAAE,IAAI,CAACA,QAAQ;UACvByG,GAAG,EAAE,IAAI,CAACA,GAAG;UACb1G,MAAM,EAAE,IAAI,CAACA,MAAM;UACnB2G,KAAK,EAAE,IAAI,CAACA;QACd,CAAC,EACD,IAAInG,CAAC,CAAC,IAAI,CAACP,QAAQ,EAAE,IAAI,CAACD,MAAM,CAClC,CAAC;QACD;MACF,KAAK,WAAW;QACd,IAAI,CAAC4L,IAAI,GAAG,IAAIxF,CAAC,CACf;UACEK,IAAI,EAAE,IAAI,CAACA,IAAI;UACfxG,QAAQ,EAAE,IAAI,CAACA,QAAQ;UACvByG,GAAG,EAAE,IAAI,CAACA,GAAG;UACb1G,MAAM,EAAE,IAAI,CAACA,MAAM;UACnB2G,KAAK,EAAE,IAAI,CAACA;QACd,CAAC,EACD,IAAIhC,CAAC,CAAC,IAAI,CAAC1E,QAAQ,EAAE,IAAI,CAACD,MAAM,CAClC,CAAC;QACD;IACJ;EACF;AACF;AACA,SACE8K,CAAC,IAAI9gB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}