{"ast": null, "code": "import { Attributor, ClassAttributor, Scope, StyleAttributor } from 'parchment';\nconst config = {\n  scope: Scope.BLOCK,\n  whitelist: ['right', 'center', 'justify']\n};\nconst AlignAttribute = new Attributor('align', 'align', config);\nconst AlignClass = new ClassAttributor('align', 'ql-align', config);\nconst AlignStyle = new StyleAttributor('align', 'text-align', config);\nexport { AlignAttribute, AlignClass, AlignStyle };", "map": {"version": 3, "names": ["Attributor", "ClassAttributor", "<PERSON><PERSON>", "StyleAttributor", "config", "scope", "BLOCK", "whitelist", "AlignAttribute", "AlignClass", "AlignStyle"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/quill/formats/align.js"], "sourcesContent": ["import { Attributor, ClassAttributor, Scope, StyleAttributor } from 'parchment';\nconst config = {\n  scope: Scope.BLOCK,\n  whitelist: ['right', 'center', 'justify']\n};\nconst AlignAttribute = new Attributor('align', 'align', config);\nconst AlignClass = new ClassAttributor('align', 'ql-align', config);\nconst AlignStyle = new StyleAttributor('align', 'text-align', config);\nexport { AlignAttribute, AlignClass, AlignStyle };\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,eAAe,EAAEC,KAAK,EAAEC,eAAe,QAAQ,WAAW;AAC/E,MAAMC,MAAM,GAAG;EACbC,KAAK,EAAEH,KAAK,CAACI,KAAK;EAClBC,SAAS,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,SAAS;AAC1C,CAAC;AACD,MAAMC,cAAc,GAAG,IAAIR,UAAU,CAAC,OAAO,EAAE,OAAO,EAAEI,MAAM,CAAC;AAC/D,MAAMK,UAAU,GAAG,IAAIR,eAAe,CAAC,OAAO,EAAE,UAAU,EAAEG,MAAM,CAAC;AACnE,MAAMM,UAAU,GAAG,IAAIP,eAAe,CAAC,OAAO,EAAE,YAAY,EAAEC,MAAM,CAAC;AACrE,SAASI,cAAc,EAAEC,UAAU,EAAEC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}