{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./app.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./app.component.css?ngResource\";\nimport { Component, inject, ChangeDetectorRef } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterOutlet, Router, NavigationStart, NavigationEnd, NavigationCancel, NavigationError } from '@angular/router';\nimport { SidebarComponent } from './components/sidebar/sidebar.component';\nimport { HeaderComponent } from './components/header/header.component';\nimport { TogglingService } from './toggling.service';\nimport { AuthService } from '../shared/services/auth.service';\nimport { NzButtonModule } from 'ng-zorro-antd/button';\nimport { ThemeService } from '../shared/services/theam.service';\nimport { AngularSplitModule } from 'angular-split';\nlet AppComponent = class AppComponent {\n  constructor() {\n    this.title = 'chatapp';\n    this.togglingService = inject(TogglingService);\n    this.authService = inject(AuthService);\n    this.themeService = inject(ThemeService);\n    this.router = inject(Router);\n    this.cdr = inject(ChangeDetectorRef);\n    this.isDailyInsightMode = false;\n    this.isNavigating = false;\n    // Sidebar states\n    this.sidebarState = 'narrow';\n    // Split area sizes for different states\n    this.splitSizes = {\n      sidebarArea: 20,\n      contentArea: 80\n    };\n    // Size configurations for different states\n    this.sizeConfigs = {\n      collapsed: {\n        sidebarArea: 4,\n        contentArea: 96\n      },\n      narrow: {\n        sidebarArea: 25,\n        contentArea: 75\n      },\n      expanded: {\n        sidebarArea: 40,\n        contentArea: 60\n      }\n    };\n    // Minimum sizes for split areas\n    this.minSizes = {\n      sidebarArea: 4,\n      contentArea: 50\n    };\n    // Storage keys for localStorage\n    this.STORAGE_KEYS = {\n      sidebarState: 'sidebarState',\n      splitSizes: 'splitSizes',\n      previousSidebarState: 'previousSidebarState'\n    };\n    // Previous split sizes to restore when toggling sidebar\n    this.previousSplitSizes = {\n      sidebarArea: 25,\n      contentArea: 75\n    };\n    // Flag to indicate if the user is currently dragging the splitter\n    this.isDragging = false;\n    // Reference to Math for use in the template\n    this.Math = Math;\n    /**\n     * Handler for sidebar toggle events\n     */\n    this.handleSidebarToggle = event => {\n      const toggleAction = event.detail.action;\n      const currentState = this.sidebarState;\n      console.log('Sidebar toggle event received:', toggleAction, 'Current state:', currentState);\n      // Save current split sizes before changing state\n      if (this.sidebarState !== 'collapsed') {\n        this.previousSplitSizes = {\n          ...this.splitSizes\n        };\n      }\n      // Handle different toggle actions\n      switch (toggleAction) {\n        case 'toggle':\n          // Cycle through states: collapsed -> narrow -> expanded -> collapsed\n          if (currentState === 'collapsed') {\n            this.sidebarState = 'narrow';\n            this.splitSizes = {\n              ...this.sizeConfigs.narrow\n            };\n          } else if (currentState === 'narrow') {\n            this.sidebarState = 'expanded';\n            this.splitSizes = {\n              ...this.sizeConfigs.expanded\n            };\n          } else {\n            this.sidebarState = 'collapsed';\n            this.splitSizes = {\n              ...this.sizeConfigs.collapsed\n            };\n          }\n          break;\n        case 'collapse':\n          this.sidebarState = 'collapsed';\n          this.splitSizes = {\n            ...this.sizeConfigs.collapsed\n          };\n          break;\n        case 'expand':\n          this.sidebarState = 'expanded';\n          this.splitSizes = {\n            ...this.sizeConfigs.expanded\n          };\n          break;\n        case 'narrow':\n          this.sidebarState = 'narrow';\n          this.splitSizes = {\n            ...this.sizeConfigs.narrow\n          };\n          break;\n        default:\n          // Default toggle behavior (between collapsed and narrow)\n          if (currentState === 'collapsed') {\n            this.sidebarState = 'narrow';\n            this.splitSizes = {\n              ...this.sizeConfigs.narrow\n            };\n          } else {\n            this.sidebarState = 'collapsed';\n            this.splitSizes = {\n              ...this.sizeConfigs.collapsed\n            };\n          }\n      }\n      // Save the current state to localStorage\n      this.saveSplitterState();\n    };\n    // Track drag start position\n    this.dragStartX = 0;\n    this.isDragDirectionValid = false;\n    // Variables for custom splitter drag\n    this.isSplitterDragging = false;\n    this.splitterDragStartX = 0;\n    /**\n     * Document-level mouse move handler\n     * Bound to this instance to maintain context\n     */\n    this.onDocumentMouseMove = event => {\n      this.onSplitterMouseMove(event);\n    };\n    /**\n     * Document-level mouse up handler\n     * Bound to this instance to maintain context\n     */\n    this.onDocumentMouseUp = _event => {\n      this.onSplitterMouseUp(_event);\n    };\n    const url = window.location.href;\n    this.isWorkspaceUrl = url.includes('workspaces');\n    console.log(this.isWorkspaceUrl);\n  }\n  ngOnInit() {\n    // Handle navbar state from localStorage\n    const storedValue = localStorage.getItem('isNavbarOpen');\n    if (storedValue === 'true') {\n      this.togglingService.isNavbarOpen = true;\n    } else if (storedValue === 'false') {\n      this.togglingService.isNavbarOpen = false;\n    } else {\n      this.togglingService.isNavbarOpen = true; // Default value if not set in localStorage\n    }\n    console.log('isNavbarOpen', this.togglingService.isNavbarOpen);\n    // Load saved sidebar state from localStorage\n    const savedSidebarState = localStorage.getItem(this.STORAGE_KEYS.sidebarState);\n    if (savedSidebarState && ['collapsed', 'narrow', 'expanded'].includes(savedSidebarState)) {\n      this.sidebarState = savedSidebarState;\n    }\n    // Load saved split sizes from localStorage if available\n    const savedSplitSizes = localStorage.getItem(this.STORAGE_KEYS.splitSizes);\n    if (savedSplitSizes) {\n      try {\n        const parsedSizes = JSON.parse(savedSplitSizes);\n        // Validate the parsed sizes to ensure they're valid\n        if (this.isValidSplitSizes(parsedSizes)) {\n          this.splitSizes = parsedSizes;\n          // Ensure the sidebar state matches the loaded split sizes\n          this.updateSidebarStateFromSizes(this.splitSizes.sidebarArea);\n          // Also store as previous split sizes if not in collapsed state\n          if (this.sidebarState !== 'collapsed') {\n            this.previousSplitSizes = {\n              ...this.splitSizes\n            };\n          }\n        } else {\n          console.warn('Invalid split sizes in localStorage, using defaults');\n          this.resetToDefaultSizes();\n        }\n      } catch (e) {\n        console.error('Error parsing saved split sizes:', e);\n        this.resetToDefaultSizes();\n      }\n    } else {\n      // If no saved split sizes, use the default for the current state\n      this.resetToDefaultSizes();\n    }\n    // Sync the toggling service state with our sidebar state\n    this.togglingService.isNavbarOpen = this.sidebarState !== 'collapsed';\n    // Listen for sidebar toggle events\n    window.addEventListener('sidebar-toggle', this.handleSidebarToggle);\n    // Subscribe to router events to handle transitions\n    this.routerSubscription = this.router.events.subscribe(event => {\n      if (event instanceof NavigationStart) {\n        // Check if this navigation should have animation disabled\n        const currentNav = this.router.getCurrentNavigation();\n        const noAnimation = currentNav?.extras?.state?.['noAnimation'];\n        if (!noAnimation) {\n          // Only show animation if not explicitly disabled\n          this.isNavigating = true;\n        }\n        // Clear any existing timeout\n        if (this.navigationTimeout) {\n          clearTimeout(this.navigationTimeout);\n        }\n      } else if (event instanceof NavigationEnd || event instanceof NavigationCancel || event instanceof NavigationError) {\n        // Get the current URL to check if we're navigating to a chat route\n        const currentUrl = this.router.url;\n        const isChatRoute = currentUrl.includes('/chat/');\n        // Check if we have a chat ID in the router state\n        const navigation = this.router.getCurrentNavigation();\n        const hasChatId = navigation?.extras?.state?.['chatId'];\n        // If we're navigating to a chat route with a specific ID or have a chat ID in state,\n        // use a shorter delay to make the transition smoother\n        const transitionDelay = isChatRoute || hasChatId ? 0 : 100;\n        // Navigation complete - fade in new view after a short delay\n        // This delay ensures the fade-out completes before fade-in starts\n        this.navigationTimeout = setTimeout(() => {\n          this.isNavigating = false;\n        }, transitionDelay);\n      }\n    });\n  }\n  ngDoCheck() {\n    // Check for workspace URL\n    const url = window.location.href;\n    this.isWorkspaceUrl = url.includes('workspaces');\n  }\n  ngOnDestroy() {\n    // Clean up subscriptions\n    if (this.routerSubscription) {\n      this.routerSubscription.unsubscribe();\n    }\n    // Clear any pending timeouts\n    if (this.navigationTimeout) {\n      clearTimeout(this.navigationTimeout);\n    }\n    // Remove event listeners\n    window.removeEventListener('sidebar-toggle', this.handleSidebarToggle);\n  }\n  /**\n   * Handles the drag progress event from the split component\n   * @param sizes The current sizes during dragging\n   */\n  onSplitDragProgress(sizes) {\n    // If this is the first drag event, record the starting position\n    if (!this.isDragging) {\n      this.dragStartX = this.splitSizes.sidebarArea;\n      // We'll determine if the drag direction is valid after some movement\n    }\n    // Set dragging flag to true\n    this.isDragging = true;\n    // Check drag direction based on the sidebar size change\n    // Only consider drag valid if moving from left to right (expanding)\n    // or if we're already in a valid drag operation\n    const isMovingRightward = sizes[0] > this.dragStartX;\n    if (isMovingRightward) {\n      this.isDragDirectionValid = true;\n    }\n    // Only update sizes if the drag direction is valid\n    if (this.isDragDirectionValid) {\n      // Update sizes during drag without saving to localStorage\n      this.splitSizes = {\n        sidebarArea: sizes[0],\n        contentArea: sizes[1]\n      };\n      // Update sidebar state during drag for immediate visual feedback\n      if (sizes[0] <= this.sizeConfigs.collapsed.sidebarArea + 2) {\n        this.sidebarState = 'collapsed';\n      } else if (sizes[0] <= this.sizeConfigs.narrow.sidebarArea + 5) {\n        this.sidebarState = 'narrow';\n      } else {\n        this.sidebarState = 'expanded';\n      }\n    }\n  }\n  /**\n   * Handles the drag end event from the split component\n   * @param sizes The new sizes of the split areas\n   */\n  onSplitDragEnd(sizes) {\n    console.log('Split sizes updated:', sizes);\n    // Reset dragging flags\n    this.isDragging = false;\n    const wasDragValid = this.isDragDirectionValid;\n    this.isDragDirectionValid = false;\n    this.dragStartX = 0;\n    // If the drag was not in a valid direction, don't update anything\n    if (!wasDragValid) {\n      return;\n    }\n    // Determine the sidebar state based on the new size\n    let newState;\n    let snapToSize;\n    if (sizes[0] <= this.sizeConfigs.collapsed.sidebarArea + 2) {\n      newState = 'collapsed';\n      snapToSize = this.sizeConfigs.collapsed.sidebarArea;\n    } else if (sizes[0] <= this.sizeConfigs.narrow.sidebarArea + 5) {\n      newState = 'narrow';\n      snapToSize = this.sizeConfigs.narrow.sidebarArea;\n    } else {\n      newState = 'expanded';\n      snapToSize = this.sizeConfigs.expanded.sidebarArea;\n    }\n    // Snap to preset sizes for better UX\n    const snapThreshold = 4; // Threshold in percentage points\n    // Check if we're close to a preset size\n    if (Math.abs(sizes[0] - this.sizeConfigs.collapsed.sidebarArea) <= snapThreshold) {\n      snapToSize = this.sizeConfigs.collapsed.sidebarArea;\n      newState = 'collapsed';\n    } else if (Math.abs(sizes[0] - this.sizeConfigs.narrow.sidebarArea) <= snapThreshold) {\n      snapToSize = this.sizeConfigs.narrow.sidebarArea;\n      newState = 'narrow';\n    } else if (Math.abs(sizes[0] - this.sizeConfigs.expanded.sidebarArea) <= snapThreshold) {\n      snapToSize = this.sizeConfigs.expanded.sidebarArea;\n      newState = 'expanded';\n    }\n    // Update sizes with snap-to behavior\n    this.splitSizes = {\n      sidebarArea: snapToSize,\n      contentArea: 100 - snapToSize\n    };\n    this.sidebarState = newState;\n    // Save the split sizes and state to localStorage\n    this.saveSplitterState();\n    // Update the toggling service state\n    this.togglingService.isNavbarOpen = this.sidebarState !== 'collapsed';\n  }\n  /**\n   * Handles double-click on the gutter\n   * Cycles through sidebar states: collapsed -> narrow -> expanded -> collapsed\n   */\n  onGutterDoubleClick(event) {\n    console.log('Gutter double-clicked:', event);\n    // Cycle through states\n    if (this.sidebarState === 'collapsed') {\n      this.sidebarState = 'narrow';\n      this.splitSizes = {\n        ...this.sizeConfigs.narrow\n      };\n    } else if (this.sidebarState === 'narrow') {\n      this.sidebarState = 'expanded';\n      this.splitSizes = {\n        ...this.sizeConfigs.expanded\n      };\n    } else {\n      this.sidebarState = 'collapsed';\n      this.splitSizes = {\n        ...this.sizeConfigs.collapsed\n      };\n    }\n    // Save the split sizes and state to localStorage\n    this.saveSplitterState();\n    // Update the toggling service state\n    this.togglingService.isNavbarOpen = this.sidebarState !== 'collapsed';\n  }\n  // We'll use the gutterClick event from the Angular Split component\n  /**\n   * Handles click on the gutter\n   * Toggles between collapsed and previous state\n   */\n  onGutterClick(event) {\n    console.log('Gutter clicked:', event);\n    // If we're currently dragging, ignore the click\n    if (this.isDragging || this.isSplitterDragging) {\n      return;\n    }\n    // For the Angular Split component's gutterClick event, we don't need to check\n    // for genuine clicks as the component already handles this internally\n    // We just need to implement the toggle behavior\n    // Toggle between collapsed and previous state\n    if (this.sidebarState === 'collapsed') {\n      // If collapsed, expand to the previous state (or default to narrow)\n      const previousState = localStorage.getItem(this.STORAGE_KEYS.previousSidebarState) || 'narrow';\n      this.sidebarState = previousState;\n      this.splitSizes = {\n        ...this.sizeConfigs[this.sidebarState]\n      };\n    } else {\n      // If expanded or narrow, save current state and collapse\n      localStorage.setItem(this.STORAGE_KEYS.previousSidebarState, this.sidebarState);\n      this.sidebarState = 'collapsed';\n      this.splitSizes = {\n        ...this.sizeConfigs.collapsed\n      };\n    }\n    // Save the split sizes and state to localStorage\n    this.saveSplitterState();\n    // Update the toggling service state\n    this.togglingService.isNavbarOpen = this.sidebarState !== 'collapsed';\n  }\n  /**\n   * Handles mouse down on the splitter hover area\n   * Initiates dragging\n   */\n  onSplitterMouseDown(event) {\n    // Only handle left mouse button\n    if (event.button !== 0) return;\n    // Record starting position\n    this.splitterDragStartX = event.clientX;\n    this.isSplitterDragging = true;\n    // Store the current sidebar size as the starting point for calculations\n    this.dragStartX = this.splitSizes.sidebarArea;\n    // Add a class to the body to indicate dragging state\n    document.body.classList.add('splitter-dragging');\n    // Prevent default to avoid text selection\n    event.preventDefault();\n    event.stopPropagation();\n    // Add document-level event listeners for move and up events\n    document.addEventListener('mousemove', this.onDocumentMouseMove, {\n      capture: true\n    });\n    document.addEventListener('mouseup', this.onDocumentMouseUp, {\n      capture: true\n    });\n  }\n  /**\n   * Handles mouse move on the splitter hover area\n   * Updates the sidebar size during drag\n   */\n  onSplitterMouseMove(event) {\n    // Only process if we're dragging\n    if (!this.isSplitterDragging) return;\n    // Prevent default behavior and stop propagation\n    event.preventDefault();\n    event.stopPropagation();\n    // Calculate the drag distance\n    const deltaX = event.clientX - this.splitterDragStartX;\n    // Set dragging flag for visual feedback\n    this.isDragging = true;\n    // Calculate new sidebar size based on the drag distance\n    // Convert pixels to percentage of window width\n    const containerWidth = window.innerWidth;\n    const pixelChange = deltaX;\n    const percentageChange = pixelChange / containerWidth * 100;\n    // Calculate new sizes - allow both expanding and collapsing\n    let newSidebarSize = this.dragStartX + percentageChange;\n    // Enforce minimum and maximum constraints\n    newSidebarSize = Math.max(this.minSizes.sidebarArea, newSidebarSize);\n    newSidebarSize = Math.min(50, newSidebarSize); // Maximum 50% of screen width\n    const newContentSize = 100 - newSidebarSize;\n    // Update sizes\n    this.splitSizes = {\n      sidebarArea: newSidebarSize,\n      contentArea: newContentSize\n    };\n    // Update sidebar state based on new size\n    if (newSidebarSize <= this.sizeConfigs.collapsed.sidebarArea + 2) {\n      this.sidebarState = 'collapsed';\n    } else if (newSidebarSize <= this.sizeConfigs.narrow.sidebarArea + 5) {\n      this.sidebarState = 'narrow';\n    } else {\n      this.sidebarState = 'expanded';\n    }\n    // Force a layout update\n    this.cdr.detectChanges();\n  }\n  /**\n   * Handles mouse up on the splitter hover area\n   * Finalizes dragging\n   */\n  onSplitterMouseUp(_event) {\n    // Only process if we're dragging\n    if (!this.isSplitterDragging) return;\n    // Prevent default behavior\n    if (_event) {\n      _event.preventDefault();\n      _event.stopPropagation();\n    }\n    // End the drag operation\n    this.isSplitterDragging = false;\n    this.isDragging = false;\n    // Remove dragging class from body\n    document.body.classList.remove('splitter-dragging');\n    // Determine the sidebar state based on the new size\n    let newState;\n    let snapToSize;\n    if (this.splitSizes.sidebarArea <= this.sizeConfigs.collapsed.sidebarArea + 2) {\n      newState = 'collapsed';\n      snapToSize = this.sizeConfigs.collapsed.sidebarArea;\n    } else if (this.splitSizes.sidebarArea <= this.sizeConfigs.narrow.sidebarArea + 5) {\n      newState = 'narrow';\n      snapToSize = this.sizeConfigs.narrow.sidebarArea;\n    } else {\n      newState = 'expanded';\n      snapToSize = this.sizeConfigs.expanded.sidebarArea;\n    }\n    // Snap to preset sizes for better UX\n    const snapThreshold = 5; // Threshold in percentage points\n    // Check if we're close to a preset size\n    if (Math.abs(this.splitSizes.sidebarArea - this.sizeConfigs.collapsed.sidebarArea) <= snapThreshold) {\n      snapToSize = this.sizeConfigs.collapsed.sidebarArea;\n      newState = 'collapsed';\n    } else if (Math.abs(this.splitSizes.sidebarArea - this.sizeConfigs.narrow.sidebarArea) <= snapThreshold) {\n      snapToSize = this.sizeConfigs.narrow.sidebarArea;\n      newState = 'narrow';\n    } else if (Math.abs(this.splitSizes.sidebarArea - this.sizeConfigs.expanded.sidebarArea) <= snapThreshold) {\n      snapToSize = this.sizeConfigs.expanded.sidebarArea;\n      newState = 'expanded';\n    }\n    // Update sizes with snap-to behavior\n    this.splitSizes = {\n      sidebarArea: snapToSize,\n      contentArea: 100 - snapToSize\n    };\n    this.sidebarState = newState;\n    // Save the split sizes and state to localStorage\n    this.saveSplitterState();\n    // Update the toggling service state\n    this.togglingService.isNavbarOpen = this.sidebarState !== 'collapsed';\n    // Remove document-level event listeners\n    document.removeEventListener('mousemove', this.onDocumentMouseMove, {\n      capture: true\n    });\n    document.removeEventListener('mouseup', this.onDocumentMouseUp, {\n      capture: true\n    });\n    // Force a layout update\n    this.cdr.detectChanges();\n  }\n  /**\n   * Validates that the split sizes object has the correct structure and values\n   * @param sizes The split sizes object to validate\n   * @returns True if the sizes are valid, false otherwise\n   */\n  isValidSplitSizes(sizes) {\n    // Check if the object has the required properties\n    if (!sizes || typeof sizes !== 'object' || !('sidebarArea' in sizes) || !('contentArea' in sizes)) {\n      return false;\n    }\n    // Check if the values are numbers and within valid ranges\n    const sidebarSize = sizes.sidebarArea;\n    const contentSize = sizes.contentArea;\n    if (typeof sidebarSize !== 'number' || typeof contentSize !== 'number') {\n      return false;\n    }\n    // Check if the values are within valid ranges\n    if (sidebarSize < this.minSizes.sidebarArea || sidebarSize > 50) {\n      return false;\n    }\n    if (contentSize < this.minSizes.contentArea || contentSize > 96) {\n      return false;\n    }\n    // Check if the values sum to approximately 100\n    const sum = sidebarSize + contentSize;\n    if (sum < 99.5 || sum > 100.5) {\n      return false;\n    }\n    return true;\n  }\n  /**\n   * Updates the sidebar state based on the current sidebar width\n   * @param sidebarWidth The current width of the sidebar area\n   */\n  updateSidebarStateFromSizes(sidebarWidth) {\n    // Determine the sidebar state based on the width\n    if (sidebarWidth <= this.sizeConfigs.collapsed.sidebarArea + 2) {\n      this.sidebarState = 'collapsed';\n    } else if (sidebarWidth <= this.sizeConfigs.narrow.sidebarArea + 5) {\n      this.sidebarState = 'narrow';\n    } else {\n      this.sidebarState = 'expanded';\n    }\n  }\n  /**\n   * Resets the split sizes to the default for the current sidebar state\n   */\n  resetToDefaultSizes() {\n    // Use the default sizes for the current state\n    this.splitSizes = {\n      ...this.sizeConfigs[this.sidebarState]\n    };\n    // If we're not in collapsed state, also update the previous split sizes\n    if (this.sidebarState !== 'collapsed') {\n      this.previousSplitSizes = {\n        ...this.splitSizes\n      };\n    }\n  }\n  /**\n   * Saves the current split sizes and state to localStorage\n   */\n  saveSplitterState() {\n    // Only save valid split sizes\n    if (this.isValidSplitSizes(this.splitSizes)) {\n      localStorage.setItem(this.STORAGE_KEYS.splitSizes, JSON.stringify(this.splitSizes));\n      localStorage.setItem(this.STORAGE_KEYS.sidebarState, this.sidebarState);\n    } else {\n      console.warn('Attempted to save invalid split sizes, using defaults instead');\n      this.resetToDefaultSizes();\n      localStorage.setItem(this.STORAGE_KEYS.splitSizes, JSON.stringify(this.splitSizes));\n      localStorage.setItem(this.STORAGE_KEYS.sidebarState, this.sidebarState);\n    }\n  }\n  static {\n    this.ctorParameters = () => [];\n  }\n};\nAppComponent = __decorate([Component({\n  selector: 'app-root',\n  standalone: true,\n  imports: [CommonModule, SidebarComponent, RouterOutlet, HeaderComponent, NzButtonModule, AngularSplitModule],\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], AppComponent);\nexport { AppComponent };", "map": {"version": 3, "names": ["Component", "inject", "ChangeDetectorRef", "CommonModule", "RouterOutlet", "Router", "NavigationStart", "NavigationEnd", "NavigationCancel", "NavigationError", "SidebarComponent", "HeaderComponent", "TogglingService", "AuthService", "NzButtonModule", "ThemeService", "AngularSplitModule", "AppComponent", "constructor", "title", "togglingService", "authService", "themeService", "router", "cdr", "isDailyInsightMode", "isNavigating", "sidebarState", "splitSizes", "sidebarArea", "contentArea", "sizeConfigs", "collapsed", "narrow", "expanded", "minSizes", "STORAGE_KEYS", "previousSidebarState", "previousSplitSizes", "isDragging", "Math", "handleSidebarToggle", "event", "toggleAction", "detail", "action", "currentState", "console", "log", "saveSplitterState", "dragStartX", "isDragDirectionValid", "isSplitterDragging", "splitterDragStartX", "onDocumentMouseMove", "onSplitterMouseMove", "onDocumentMouseUp", "_event", "onSplitterMouseUp", "url", "window", "location", "href", "isWorkspaceUrl", "includes", "ngOnInit", "storedValue", "localStorage", "getItem", "isNavbarOpen", "savedSidebarState", "savedSplitSizes", "parsedSizes", "JSON", "parse", "isValidSplitSizes", "updateSidebarStateFromSizes", "warn", "resetToDefaultSizes", "e", "error", "addEventListener", "routerSubscription", "events", "subscribe", "currentNav", "getCurrentNavigation", "noAnimation", "extras", "state", "navigationTimeout", "clearTimeout", "currentUrl", "isChatRoute", "navigation", "hasChatId", "transitionDelay", "setTimeout", "ngDoCheck", "ngOnDestroy", "unsubscribe", "removeEventListener", "onSplitDragProgress", "sizes", "isMovingRightward", "onSplitDragEnd", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "newState", "snapToSize", "snapThreshold", "abs", "onGutterDoubleClick", "onGutterClick", "previousState", "setItem", "onSplitterMouseDown", "button", "clientX", "document", "body", "classList", "add", "preventDefault", "stopPropagation", "capture", "deltaX", "containerWidth", "innerWidth", "pixelChange", "percentageChange", "newSidebarSize", "max", "min", "newContentSize", "detectChanges", "remove", "sidebarSize", "contentSize", "sum", "sidebarWidth", "stringify", "__decorate", "selector", "standalone", "imports", "template", "__NG_CLI_RESOURCE__0"], "sources": ["C:\\Users\\<USER>\\source\\ai-hub\\WebApp\\src\\app\\app.component.ts"], "sourcesContent": ["import { Component, inject, <PERSON><PERSON><PERSON><PERSON>, OnInit, OnDestroy, ChangeDetectorRef } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { RouterOutlet, Router, NavigationStart, NavigationEnd, NavigationCancel, NavigationError } from '@angular/router';\r\nimport { SidebarComponent } from './components/sidebar/sidebar.component';\r\nimport { HeaderComponent } from './components/header/header.component';\r\nimport { HeroComponent } from './pages/hero/hero.component';\r\nimport { TogglingService } from './toggling.service';\r\nimport { AuthService } from '../shared/services/auth.service';\r\nimport { Subscription } from 'rxjs';\r\n\r\nimport { NzButtonModule } from 'ng-zorro-antd/button';\r\nimport { ThemeService } from '../shared/services/theam.service';\r\nimport { AngularSplitModule } from 'angular-split';\r\n\r\n@Component({\r\n  selector: 'app-root',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    SidebarComponent,\r\n    RouterOutlet,\r\n    HeaderComponent,\r\n    NzButtonModule,\r\n    AngularSplitModule\r\n  ],\r\n  templateUrl: './app.component.html',\r\n  styleUrl: './app.component.css',\r\n})\r\nexport class AppComponent implements OnInit, OnDestroy {\r\n  title = 'chatapp';\r\n  togglingService = inject(TogglingService);\r\n  authService = inject(AuthService);\r\n  themeService = inject(ThemeService);\r\n  router = inject(Router);\r\n  cdr = inject(ChangeDetectorRef);\r\n\r\n  isWorkspaceUrl: boolean;\r\n  isDailyInsightMode: boolean = false;\r\n  isNavigating: boolean = false;\r\n  private routerSubscription: Subscription | undefined;\r\n  private navigationTimeout: any;\r\n\r\n  // Sidebar states\r\n  sidebarState: 'collapsed' | 'narrow' | 'expanded' = 'narrow';\r\n\r\n  // Split area sizes for different states\r\n  splitSizes = {\r\n    sidebarArea: 20,\r\n    contentArea: 80\r\n  };\r\n\r\n  // Size configurations for different states\r\n  sizeConfigs = {\r\n    collapsed: {\r\n      sidebarArea: 4,\r\n      contentArea: 96\r\n    },\r\n    narrow: {\r\n      sidebarArea: 25,\r\n      contentArea: 75\r\n    },\r\n    expanded: {\r\n      sidebarArea: 40,\r\n      contentArea: 60\r\n    }\r\n  };\r\n\r\n  // Minimum sizes for split areas\r\n  minSizes = {\r\n    sidebarArea: 4, // Changed from 5 to 4 to allow collapsed state\r\n    contentArea: 50\r\n  };\r\n\r\n  // Storage keys for localStorage\r\n  readonly STORAGE_KEYS = {\r\n    sidebarState: 'sidebarState',\r\n    splitSizes: 'splitSizes',\r\n    previousSidebarState: 'previousSidebarState'\r\n  };\r\n\r\n  // Previous split sizes to restore when toggling sidebar\r\n  previousSplitSizes = {\r\n    sidebarArea: 25,\r\n    contentArea: 75\r\n  };\r\n\r\n  // Flag to indicate if the user is currently dragging the splitter\r\n  isDragging = false;\r\n\r\n  // Reference to Math for use in the template\r\n  Math = Math;\r\n\r\n  constructor() {\r\n    const url = window.location.href;\r\n    this.isWorkspaceUrl = url.includes('workspaces');\r\n    console.log(this.isWorkspaceUrl);\r\n  }\r\n\r\n  ngOnInit() {\r\n    // Handle navbar state from localStorage\r\n    const storedValue = localStorage.getItem('isNavbarOpen');\r\n    if (storedValue === 'true') {\r\n      this.togglingService.isNavbarOpen = true;\r\n    } else if (storedValue === 'false') {\r\n      this.togglingService.isNavbarOpen = false;\r\n    } else {\r\n      this.togglingService.isNavbarOpen = true; // Default value if not set in localStorage\r\n    }\r\n    console.log('isNavbarOpen', this.togglingService.isNavbarOpen);\r\n\r\n    // Load saved sidebar state from localStorage\r\n    const savedSidebarState = localStorage.getItem(this.STORAGE_KEYS.sidebarState);\r\n    if (savedSidebarState && ['collapsed', 'narrow', 'expanded'].includes(savedSidebarState)) {\r\n      this.sidebarState = savedSidebarState as 'collapsed' | 'narrow' | 'expanded';\r\n    }\r\n\r\n    // Load saved split sizes from localStorage if available\r\n    const savedSplitSizes = localStorage.getItem(this.STORAGE_KEYS.splitSizes);\r\n    if (savedSplitSizes) {\r\n      try {\r\n        const parsedSizes = JSON.parse(savedSplitSizes);\r\n\r\n        // Validate the parsed sizes to ensure they're valid\r\n        if (this.isValidSplitSizes(parsedSizes)) {\r\n          this.splitSizes = parsedSizes;\r\n\r\n          // Ensure the sidebar state matches the loaded split sizes\r\n          this.updateSidebarStateFromSizes(this.splitSizes.sidebarArea);\r\n\r\n          // Also store as previous split sizes if not in collapsed state\r\n          if (this.sidebarState !== 'collapsed') {\r\n            this.previousSplitSizes = { ...this.splitSizes };\r\n          }\r\n        } else {\r\n          console.warn('Invalid split sizes in localStorage, using defaults');\r\n          this.resetToDefaultSizes();\r\n        }\r\n      } catch (e) {\r\n        console.error('Error parsing saved split sizes:', e);\r\n        this.resetToDefaultSizes();\r\n      }\r\n    } else {\r\n      // If no saved split sizes, use the default for the current state\r\n      this.resetToDefaultSizes();\r\n    }\r\n\r\n    // Sync the toggling service state with our sidebar state\r\n    this.togglingService.isNavbarOpen = this.sidebarState !== 'collapsed';\r\n\r\n    // Listen for sidebar toggle events\r\n    window.addEventListener('sidebar-toggle', this.handleSidebarToggle as EventListener);\r\n\r\n    // Subscribe to router events to handle transitions\r\n    this.routerSubscription = this.router.events.subscribe(event => {\r\n      if (event instanceof NavigationStart) {\r\n        // Check if this navigation should have animation disabled\r\n        const currentNav = this.router.getCurrentNavigation();\r\n        const noAnimation = currentNav?.extras?.state?.['noAnimation'];\r\n\r\n        if (!noAnimation) {\r\n          // Only show animation if not explicitly disabled\r\n          this.isNavigating = true;\r\n        }\r\n\r\n        // Clear any existing timeout\r\n        if (this.navigationTimeout) {\r\n          clearTimeout(this.navigationTimeout);\r\n        }\r\n      } else if (\r\n        event instanceof NavigationEnd ||\r\n        event instanceof NavigationCancel ||\r\n        event instanceof NavigationError\r\n      ) {\r\n        // Get the current URL to check if we're navigating to a chat route\r\n        const currentUrl = this.router.url;\r\n        const isChatRoute = currentUrl.includes('/chat/');\r\n\r\n        // Check if we have a chat ID in the router state\r\n        const navigation = this.router.getCurrentNavigation();\r\n        const hasChatId = navigation?.extras?.state?.['chatId'];\r\n\r\n        // If we're navigating to a chat route with a specific ID or have a chat ID in state,\r\n        // use a shorter delay to make the transition smoother\r\n        const transitionDelay = (isChatRoute || hasChatId) ? 0 : 100;\r\n\r\n        // Navigation complete - fade in new view after a short delay\r\n        // This delay ensures the fade-out completes before fade-in starts\r\n        this.navigationTimeout = setTimeout(() => {\r\n          this.isNavigating = false;\r\n        }, transitionDelay);\r\n      }\r\n    });\r\n  }\r\n\r\n  ngDoCheck(): void {\r\n    // Check for workspace URL\r\n    const url = window.location.href;\r\n    this.isWorkspaceUrl = url.includes('workspaces');\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // Clean up subscriptions\r\n    if (this.routerSubscription) {\r\n      this.routerSubscription.unsubscribe();\r\n    }\r\n\r\n    // Clear any pending timeouts\r\n    if (this.navigationTimeout) {\r\n      clearTimeout(this.navigationTimeout);\r\n    }\r\n\r\n    // Remove event listeners\r\n    window.removeEventListener('sidebar-toggle', this.handleSidebarToggle as EventListener);\r\n  }\r\n\r\n  /**\r\n   * Handler for sidebar toggle events\r\n   */\r\n  private handleSidebarToggle = (event: CustomEvent) => {\r\n    const toggleAction = event.detail.action;\r\n    const currentState = this.sidebarState;\r\n\r\n    console.log('Sidebar toggle event received:', toggleAction, 'Current state:', currentState);\r\n\r\n    // Save current split sizes before changing state\r\n    if (this.sidebarState !== 'collapsed') {\r\n      this.previousSplitSizes = { ...this.splitSizes };\r\n    }\r\n\r\n    // Handle different toggle actions\r\n    switch (toggleAction) {\r\n      case 'toggle':\r\n        // Cycle through states: collapsed -> narrow -> expanded -> collapsed\r\n        if (currentState === 'collapsed') {\r\n          this.sidebarState = 'narrow';\r\n          this.splitSizes = { ...this.sizeConfigs.narrow };\r\n        } else if (currentState === 'narrow') {\r\n          this.sidebarState = 'expanded';\r\n          this.splitSizes = { ...this.sizeConfigs.expanded };\r\n        } else {\r\n          this.sidebarState = 'collapsed';\r\n          this.splitSizes = { ...this.sizeConfigs.collapsed };\r\n        }\r\n        break;\r\n\r\n      case 'collapse':\r\n        this.sidebarState = 'collapsed';\r\n        this.splitSizes = { ...this.sizeConfigs.collapsed };\r\n        break;\r\n\r\n      case 'expand':\r\n        this.sidebarState = 'expanded';\r\n        this.splitSizes = { ...this.sizeConfigs.expanded };\r\n        break;\r\n\r\n      case 'narrow':\r\n        this.sidebarState = 'narrow';\r\n        this.splitSizes = { ...this.sizeConfigs.narrow };\r\n        break;\r\n\r\n      default:\r\n        // Default toggle behavior (between collapsed and narrow)\r\n        if (currentState === 'collapsed') {\r\n          this.sidebarState = 'narrow';\r\n          this.splitSizes = { ...this.sizeConfigs.narrow };\r\n        } else {\r\n          this.sidebarState = 'collapsed';\r\n          this.splitSizes = { ...this.sizeConfigs.collapsed };\r\n        }\r\n    }\r\n\r\n    // Save the current state to localStorage\r\n    this.saveSplitterState();\r\n  }\r\n\r\n  // Track drag start position\r\n  private dragStartX: number = 0;\r\n  private isDragDirectionValid: boolean = false;\r\n\r\n  // Variables for custom splitter drag\r\n  private isSplitterDragging: boolean = false;\r\n  private splitterDragStartX: number = 0;\r\n\r\n  /**\r\n   * Handles the drag progress event from the split component\r\n   * @param sizes The current sizes during dragging\r\n   */\r\n  onSplitDragProgress(sizes: any) {\r\n    // If this is the first drag event, record the starting position\r\n    if (!this.isDragging) {\r\n      this.dragStartX = this.splitSizes.sidebarArea;\r\n      // We'll determine if the drag direction is valid after some movement\r\n    }\r\n\r\n    // Set dragging flag to true\r\n    this.isDragging = true;\r\n\r\n    // Check drag direction based on the sidebar size change\r\n    // Only consider drag valid if moving from left to right (expanding)\r\n    // or if we're already in a valid drag operation\r\n    const isMovingRightward = sizes[0] > this.dragStartX;\r\n    if (isMovingRightward) {\r\n      this.isDragDirectionValid = true;\r\n    }\r\n\r\n    // Only update sizes if the drag direction is valid\r\n    if (this.isDragDirectionValid) {\r\n      // Update sizes during drag without saving to localStorage\r\n      this.splitSizes = {\r\n        sidebarArea: sizes[0],\r\n        contentArea: sizes[1]\r\n      };\r\n\r\n      // Update sidebar state during drag for immediate visual feedback\r\n      if (sizes[0] <= this.sizeConfigs.collapsed.sidebarArea + 2) {\r\n        this.sidebarState = 'collapsed';\r\n      } else if (sizes[0] <= this.sizeConfigs.narrow.sidebarArea + 5) {\r\n        this.sidebarState = 'narrow';\r\n      } else {\r\n        this.sidebarState = 'expanded';\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Handles the drag end event from the split component\r\n   * @param sizes The new sizes of the split areas\r\n   */\r\n  onSplitDragEnd(sizes: any) {\r\n    console.log('Split sizes updated:', sizes);\r\n\r\n    // Reset dragging flags\r\n    this.isDragging = false;\r\n    const wasDragValid = this.isDragDirectionValid;\r\n    this.isDragDirectionValid = false;\r\n    this.dragStartX = 0;\r\n\r\n    // If the drag was not in a valid direction, don't update anything\r\n    if (!wasDragValid) {\r\n      return;\r\n    }\r\n\r\n    // Determine the sidebar state based on the new size\r\n    let newState: 'collapsed' | 'narrow' | 'expanded';\r\n    let snapToSize: number;\r\n\r\n    if (sizes[0] <= this.sizeConfigs.collapsed.sidebarArea + 2) {\r\n      newState = 'collapsed';\r\n      snapToSize = this.sizeConfigs.collapsed.sidebarArea;\r\n    } else if (sizes[0] <= this.sizeConfigs.narrow.sidebarArea + 5) {\r\n      newState = 'narrow';\r\n      snapToSize = this.sizeConfigs.narrow.sidebarArea;\r\n    } else {\r\n      newState = 'expanded';\r\n      snapToSize = this.sizeConfigs.expanded.sidebarArea;\r\n    }\r\n\r\n    // Snap to preset sizes for better UX\r\n    const snapThreshold = 4; // Threshold in percentage points\r\n\r\n    // Check if we're close to a preset size\r\n    if (Math.abs(sizes[0] - this.sizeConfigs.collapsed.sidebarArea) <= snapThreshold) {\r\n      snapToSize = this.sizeConfigs.collapsed.sidebarArea;\r\n      newState = 'collapsed';\r\n    } else if (Math.abs(sizes[0] - this.sizeConfigs.narrow.sidebarArea) <= snapThreshold) {\r\n      snapToSize = this.sizeConfigs.narrow.sidebarArea;\r\n      newState = 'narrow';\r\n    } else if (Math.abs(sizes[0] - this.sizeConfigs.expanded.sidebarArea) <= snapThreshold) {\r\n      snapToSize = this.sizeConfigs.expanded.sidebarArea;\r\n      newState = 'expanded';\r\n    }\r\n\r\n    // Update sizes with snap-to behavior\r\n    this.splitSizes = {\r\n      sidebarArea: snapToSize,\r\n      contentArea: 100 - snapToSize\r\n    };\r\n\r\n    this.sidebarState = newState;\r\n\r\n    // Save the split sizes and state to localStorage\r\n    this.saveSplitterState();\r\n\r\n    // Update the toggling service state\r\n    this.togglingService.isNavbarOpen = this.sidebarState !== 'collapsed';\r\n  }\r\n\r\n  /**\r\n   * Handles double-click on the gutter\r\n   * Cycles through sidebar states: collapsed -> narrow -> expanded -> collapsed\r\n   */\r\n  onGutterDoubleClick(event: any) {\r\n    console.log('Gutter double-clicked:', event);\r\n\r\n    // Cycle through states\r\n    if (this.sidebarState === 'collapsed') {\r\n      this.sidebarState = 'narrow';\r\n      this.splitSizes = { ...this.sizeConfigs.narrow };\r\n    } else if (this.sidebarState === 'narrow') {\r\n      this.sidebarState = 'expanded';\r\n      this.splitSizes = { ...this.sizeConfigs.expanded };\r\n    } else {\r\n      this.sidebarState = 'collapsed';\r\n      this.splitSizes = { ...this.sizeConfigs.collapsed };\r\n    }\r\n\r\n    // Save the split sizes and state to localStorage\r\n    this.saveSplitterState();\r\n\r\n    // Update the toggling service state\r\n    this.togglingService.isNavbarOpen = this.sidebarState !== 'collapsed';\r\n  }\r\n\r\n  // We'll use the gutterClick event from the Angular Split component\r\n\r\n  /**\r\n   * Handles click on the gutter\r\n   * Toggles between collapsed and previous state\r\n   */\r\n  onGutterClick(event: any) {\r\n    console.log('Gutter clicked:', event);\r\n\r\n    // If we're currently dragging, ignore the click\r\n    if (this.isDragging || this.isSplitterDragging) {\r\n      return;\r\n    }\r\n\r\n    // For the Angular Split component's gutterClick event, we don't need to check\r\n    // for genuine clicks as the component already handles this internally\r\n    // We just need to implement the toggle behavior\r\n\r\n    // Toggle between collapsed and previous state\r\n    if (this.sidebarState === 'collapsed') {\r\n      // If collapsed, expand to the previous state (or default to narrow)\r\n      const previousState = localStorage.getItem(this.STORAGE_KEYS.previousSidebarState) || 'narrow';\r\n      this.sidebarState = previousState as 'narrow' | 'expanded';\r\n      this.splitSizes = { ...this.sizeConfigs[this.sidebarState] };\r\n    } else {\r\n      // If expanded or narrow, save current state and collapse\r\n      localStorage.setItem(this.STORAGE_KEYS.previousSidebarState, this.sidebarState);\r\n      this.sidebarState = 'collapsed';\r\n      this.splitSizes = { ...this.sizeConfigs.collapsed };\r\n    }\r\n\r\n    // Save the split sizes and state to localStorage\r\n    this.saveSplitterState();\r\n\r\n    // Update the toggling service state\r\n    this.togglingService.isNavbarOpen = this.sidebarState !== 'collapsed';\r\n  }\r\n\r\n  /**\r\n   * Handles mouse down on the splitter hover area\r\n   * Initiates dragging\r\n   */\r\n  onSplitterMouseDown(event: MouseEvent) {\r\n    // Only handle left mouse button\r\n    if (event.button !== 0) return;\r\n\r\n    // Record starting position\r\n    this.splitterDragStartX = event.clientX;\r\n    this.isSplitterDragging = true;\r\n\r\n    // Store the current sidebar size as the starting point for calculations\r\n    this.dragStartX = this.splitSizes.sidebarArea;\r\n\r\n    // Add a class to the body to indicate dragging state\r\n    document.body.classList.add('splitter-dragging');\r\n\r\n    // Prevent default to avoid text selection\r\n    event.preventDefault();\r\n    event.stopPropagation();\r\n\r\n    // Add document-level event listeners for move and up events\r\n    document.addEventListener('mousemove', this.onDocumentMouseMove, { capture: true });\r\n    document.addEventListener('mouseup', this.onDocumentMouseUp, { capture: true });\r\n  }\r\n\r\n  /**\r\n   * Handles mouse move on the splitter hover area\r\n   * Updates the sidebar size during drag\r\n   */\r\n  onSplitterMouseMove(event: MouseEvent) {\r\n    // Only process if we're dragging\r\n    if (!this.isSplitterDragging) return;\r\n\r\n    // Prevent default behavior and stop propagation\r\n    event.preventDefault();\r\n    event.stopPropagation();\r\n\r\n    // Calculate the drag distance\r\n    const deltaX = event.clientX - this.splitterDragStartX;\r\n\r\n    // Set dragging flag for visual feedback\r\n    this.isDragging = true;\r\n\r\n    // Calculate new sidebar size based on the drag distance\r\n    // Convert pixels to percentage of window width\r\n    const containerWidth = window.innerWidth;\r\n    const pixelChange = deltaX;\r\n    const percentageChange = (pixelChange / containerWidth) * 100;\r\n\r\n    // Calculate new sizes - allow both expanding and collapsing\r\n    let newSidebarSize = this.dragStartX + percentageChange;\r\n\r\n    // Enforce minimum and maximum constraints\r\n    newSidebarSize = Math.max(this.minSizes.sidebarArea, newSidebarSize);\r\n    newSidebarSize = Math.min(50, newSidebarSize); // Maximum 50% of screen width\r\n\r\n    const newContentSize = 100 - newSidebarSize;\r\n\r\n    // Update sizes\r\n    this.splitSizes = {\r\n      sidebarArea: newSidebarSize,\r\n      contentArea: newContentSize\r\n    };\r\n\r\n    // Update sidebar state based on new size\r\n    if (newSidebarSize <= this.sizeConfigs.collapsed.sidebarArea + 2) {\r\n      this.sidebarState = 'collapsed';\r\n    } else if (newSidebarSize <= this.sizeConfigs.narrow.sidebarArea + 5) {\r\n      this.sidebarState = 'narrow';\r\n    } else {\r\n      this.sidebarState = 'expanded';\r\n    }\r\n\r\n    // Force a layout update\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  /**\r\n   * Handles mouse up on the splitter hover area\r\n   * Finalizes dragging\r\n   */\r\n  onSplitterMouseUp(_event: MouseEvent) {\r\n    // Only process if we're dragging\r\n    if (!this.isSplitterDragging) return;\r\n\r\n    // Prevent default behavior\r\n    if (_event) {\r\n      _event.preventDefault();\r\n      _event.stopPropagation();\r\n    }\r\n\r\n    // End the drag operation\r\n    this.isSplitterDragging = false;\r\n    this.isDragging = false;\r\n\r\n    // Remove dragging class from body\r\n    document.body.classList.remove('splitter-dragging');\r\n\r\n    // Determine the sidebar state based on the new size\r\n    let newState: 'collapsed' | 'narrow' | 'expanded';\r\n    let snapToSize: number;\r\n\r\n    if (this.splitSizes.sidebarArea <= this.sizeConfigs.collapsed.sidebarArea + 2) {\r\n      newState = 'collapsed';\r\n      snapToSize = this.sizeConfigs.collapsed.sidebarArea;\r\n    } else if (this.splitSizes.sidebarArea <= this.sizeConfigs.narrow.sidebarArea + 5) {\r\n      newState = 'narrow';\r\n      snapToSize = this.sizeConfigs.narrow.sidebarArea;\r\n    } else {\r\n      newState = 'expanded';\r\n      snapToSize = this.sizeConfigs.expanded.sidebarArea;\r\n    }\r\n\r\n    // Snap to preset sizes for better UX\r\n    const snapThreshold = 5; // Threshold in percentage points\r\n\r\n    // Check if we're close to a preset size\r\n    if (Math.abs(this.splitSizes.sidebarArea - this.sizeConfigs.collapsed.sidebarArea) <= snapThreshold) {\r\n      snapToSize = this.sizeConfigs.collapsed.sidebarArea;\r\n      newState = 'collapsed';\r\n    } else if (Math.abs(this.splitSizes.sidebarArea - this.sizeConfigs.narrow.sidebarArea) <= snapThreshold) {\r\n      snapToSize = this.sizeConfigs.narrow.sidebarArea;\r\n      newState = 'narrow';\r\n    } else if (Math.abs(this.splitSizes.sidebarArea - this.sizeConfigs.expanded.sidebarArea) <= snapThreshold) {\r\n      snapToSize = this.sizeConfigs.expanded.sidebarArea;\r\n      newState = 'expanded';\r\n    }\r\n\r\n    // Update sizes with snap-to behavior\r\n    this.splitSizes = {\r\n      sidebarArea: snapToSize,\r\n      contentArea: 100 - snapToSize\r\n    };\r\n\r\n    this.sidebarState = newState;\r\n\r\n    // Save the split sizes and state to localStorage\r\n    this.saveSplitterState();\r\n\r\n    // Update the toggling service state\r\n    this.togglingService.isNavbarOpen = this.sidebarState !== 'collapsed';\r\n\r\n    // Remove document-level event listeners\r\n    document.removeEventListener('mousemove', this.onDocumentMouseMove, { capture: true });\r\n    document.removeEventListener('mouseup', this.onDocumentMouseUp, { capture: true });\r\n\r\n    // Force a layout update\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  /**\r\n   * Document-level mouse move handler\r\n   * Bound to this instance to maintain context\r\n   */\r\n  private onDocumentMouseMove = (event: MouseEvent) => {\r\n    this.onSplitterMouseMove(event);\r\n  };\r\n\r\n  /**\r\n   * Document-level mouse up handler\r\n   * Bound to this instance to maintain context\r\n   */\r\n  private onDocumentMouseUp = (_event: MouseEvent) => {\r\n    this.onSplitterMouseUp(_event);\r\n  };\r\n\r\n  /**\r\n   * Validates that the split sizes object has the correct structure and values\r\n   * @param sizes The split sizes object to validate\r\n   * @returns True if the sizes are valid, false otherwise\r\n   */\r\n  private isValidSplitSizes(sizes: any): boolean {\r\n    // Check if the object has the required properties\r\n    if (!sizes || typeof sizes !== 'object' || !('sidebarArea' in sizes) || !('contentArea' in sizes)) {\r\n      return false;\r\n    }\r\n\r\n    // Check if the values are numbers and within valid ranges\r\n    const sidebarSize = sizes.sidebarArea;\r\n    const contentSize = sizes.contentArea;\r\n\r\n    if (typeof sidebarSize !== 'number' || typeof contentSize !== 'number') {\r\n      return false;\r\n    }\r\n\r\n    // Check if the values are within valid ranges\r\n    if (sidebarSize < this.minSizes.sidebarArea || sidebarSize > 50) {\r\n      return false;\r\n    }\r\n\r\n    if (contentSize < this.minSizes.contentArea || contentSize > 96) {\r\n      return false;\r\n    }\r\n\r\n    // Check if the values sum to approximately 100\r\n    const sum = sidebarSize + contentSize;\r\n    if (sum < 99.5 || sum > 100.5) {\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Updates the sidebar state based on the current sidebar width\r\n   * @param sidebarWidth The current width of the sidebar area\r\n   */\r\n  private updateSidebarStateFromSizes(sidebarWidth: number): void {\r\n    // Determine the sidebar state based on the width\r\n    if (sidebarWidth <= this.sizeConfigs.collapsed.sidebarArea + 2) {\r\n      this.sidebarState = 'collapsed';\r\n    } else if (sidebarWidth <= this.sizeConfigs.narrow.sidebarArea + 5) {\r\n      this.sidebarState = 'narrow';\r\n    } else {\r\n      this.sidebarState = 'expanded';\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Resets the split sizes to the default for the current sidebar state\r\n   */\r\n  private resetToDefaultSizes(): void {\r\n    // Use the default sizes for the current state\r\n    this.splitSizes = { ...this.sizeConfigs[this.sidebarState] };\r\n\r\n    // If we're not in collapsed state, also update the previous split sizes\r\n    if (this.sidebarState !== 'collapsed') {\r\n      this.previousSplitSizes = { ...this.splitSizes };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Saves the current split sizes and state to localStorage\r\n   */\r\n  private saveSplitterState(): void {\r\n    // Only save valid split sizes\r\n    if (this.isValidSplitSizes(this.splitSizes)) {\r\n      localStorage.setItem(this.STORAGE_KEYS.splitSizes, JSON.stringify(this.splitSizes));\r\n      localStorage.setItem(this.STORAGE_KEYS.sidebarState, this.sidebarState);\r\n    } else {\r\n      console.warn('Attempted to save invalid split sizes, using defaults instead');\r\n      this.resetToDefaultSizes();\r\n      localStorage.setItem(this.STORAGE_KEYS.splitSizes, JSON.stringify(this.splitSizes));\r\n      localStorage.setItem(this.STORAGE_KEYS.sidebarState, this.sidebarState);\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;;AAAA,SAASA,SAAS,EAAEC,MAAM,EAAoCC,iBAAiB,QAAQ,eAAe;AACtG,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,EAAEC,MAAM,EAAEC,eAAe,EAAEC,aAAa,EAAEC,gBAAgB,EAAEC,eAAe,QAAQ,iBAAiB;AACzH,SAASC,gBAAgB,QAAQ,wCAAwC;AACzE,SAASC,eAAe,QAAQ,sCAAsC;AAEtE,SAASC,eAAe,QAAQ,oBAAoB;AACpD,SAASC,WAAW,QAAQ,iCAAiC;AAG7D,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,YAAY,QAAQ,kCAAkC;AAC/D,SAASC,kBAAkB,QAAQ,eAAe;AAgB3C,IAAMC,YAAY,GAAlB,MAAMA,YAAY;EAgEvBC,YAAA;IA/DA,KAAAC,KAAK,GAAG,SAAS;IACjB,KAAAC,eAAe,GAAGnB,MAAM,CAACW,eAAe,CAAC;IACzC,KAAAS,WAAW,GAAGpB,MAAM,CAACY,WAAW,CAAC;IACjC,KAAAS,YAAY,GAAGrB,MAAM,CAACc,YAAY,CAAC;IACnC,KAAAQ,MAAM,GAAGtB,MAAM,CAACI,MAAM,CAAC;IACvB,KAAAmB,GAAG,GAAGvB,MAAM,CAACC,iBAAiB,CAAC;IAG/B,KAAAuB,kBAAkB,GAAY,KAAK;IACnC,KAAAC,YAAY,GAAY,KAAK;IAI7B;IACA,KAAAC,YAAY,GAAwC,QAAQ;IAE5D;IACA,KAAAC,UAAU,GAAG;MACXC,WAAW,EAAE,EAAE;MACfC,WAAW,EAAE;KACd;IAED;IACA,KAAAC,WAAW,GAAG;MACZC,SAAS,EAAE;QACTH,WAAW,EAAE,CAAC;QACdC,WAAW,EAAE;OACd;MACDG,MAAM,EAAE;QACNJ,WAAW,EAAE,EAAE;QACfC,WAAW,EAAE;OACd;MACDI,QAAQ,EAAE;QACRL,WAAW,EAAE,EAAE;QACfC,WAAW,EAAE;;KAEhB;IAED;IACA,KAAAK,QAAQ,GAAG;MACTN,WAAW,EAAE,CAAC;MACdC,WAAW,EAAE;KACd;IAED;IACS,KAAAM,YAAY,GAAG;MACtBT,YAAY,EAAE,cAAc;MAC5BC,UAAU,EAAE,YAAY;MACxBS,oBAAoB,EAAE;KACvB;IAED;IACA,KAAAC,kBAAkB,GAAG;MACnBT,WAAW,EAAE,EAAE;MACfC,WAAW,EAAE;KACd;IAED;IACA,KAAAS,UAAU,GAAG,KAAK;IAElB;IACA,KAAAC,IAAI,GAAGA,IAAI;IA6HX;;;IAGQ,KAAAC,mBAAmB,GAAIC,KAAkB,IAAI;MACnD,MAAMC,YAAY,GAAGD,KAAK,CAACE,MAAM,CAACC,MAAM;MACxC,MAAMC,YAAY,GAAG,IAAI,CAACnB,YAAY;MAEtCoB,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEL,YAAY,EAAE,gBAAgB,EAAEG,YAAY,CAAC;MAE3F;MACA,IAAI,IAAI,CAACnB,YAAY,KAAK,WAAW,EAAE;QACrC,IAAI,CAACW,kBAAkB,GAAG;UAAE,GAAG,IAAI,CAACV;QAAU,CAAE;;MAGlD;MACA,QAAQe,YAAY;QAClB,KAAK,QAAQ;UACX;UACA,IAAIG,YAAY,KAAK,WAAW,EAAE;YAChC,IAAI,CAACnB,YAAY,GAAG,QAAQ;YAC5B,IAAI,CAACC,UAAU,GAAG;cAAE,GAAG,IAAI,CAACG,WAAW,CAACE;YAAM,CAAE;WACjD,MAAM,IAAIa,YAAY,KAAK,QAAQ,EAAE;YACpC,IAAI,CAACnB,YAAY,GAAG,UAAU;YAC9B,IAAI,CAACC,UAAU,GAAG;cAAE,GAAG,IAAI,CAACG,WAAW,CAACG;YAAQ,CAAE;WACnD,MAAM;YACL,IAAI,CAACP,YAAY,GAAG,WAAW;YAC/B,IAAI,CAACC,UAAU,GAAG;cAAE,GAAG,IAAI,CAACG,WAAW,CAACC;YAAS,CAAE;;UAErD;QAEF,KAAK,UAAU;UACb,IAAI,CAACL,YAAY,GAAG,WAAW;UAC/B,IAAI,CAACC,UAAU,GAAG;YAAE,GAAG,IAAI,CAACG,WAAW,CAACC;UAAS,CAAE;UACnD;QAEF,KAAK,QAAQ;UACX,IAAI,CAACL,YAAY,GAAG,UAAU;UAC9B,IAAI,CAACC,UAAU,GAAG;YAAE,GAAG,IAAI,CAACG,WAAW,CAACG;UAAQ,CAAE;UAClD;QAEF,KAAK,QAAQ;UACX,IAAI,CAACP,YAAY,GAAG,QAAQ;UAC5B,IAAI,CAACC,UAAU,GAAG;YAAE,GAAG,IAAI,CAACG,WAAW,CAACE;UAAM,CAAE;UAChD;QAEF;UACE;UACA,IAAIa,YAAY,KAAK,WAAW,EAAE;YAChC,IAAI,CAACnB,YAAY,GAAG,QAAQ;YAC5B,IAAI,CAACC,UAAU,GAAG;cAAE,GAAG,IAAI,CAACG,WAAW,CAACE;YAAM,CAAE;WACjD,MAAM;YACL,IAAI,CAACN,YAAY,GAAG,WAAW;YAC/B,IAAI,CAACC,UAAU,GAAG;cAAE,GAAG,IAAI,CAACG,WAAW,CAACC;YAAS,CAAE;;;MAIzD;MACA,IAAI,CAACiB,iBAAiB,EAAE;IAC1B,CAAC;IAED;IACQ,KAAAC,UAAU,GAAW,CAAC;IACtB,KAAAC,oBAAoB,GAAY,KAAK;IAE7C;IACQ,KAAAC,kBAAkB,GAAY,KAAK;IACnC,KAAAC,kBAAkB,GAAW,CAAC;IAkUtC;;;;IAIQ,KAAAC,mBAAmB,GAAIZ,KAAiB,IAAI;MAClD,IAAI,CAACa,mBAAmB,CAACb,KAAK,CAAC;IACjC,CAAC;IAED;;;;IAIQ,KAAAc,iBAAiB,GAAIC,MAAkB,IAAI;MACjD,IAAI,CAACC,iBAAiB,CAACD,MAAM,CAAC;IAChC,CAAC;IA5gBC,MAAME,GAAG,GAAGC,MAAM,CAACC,QAAQ,CAACC,IAAI;IAChC,IAAI,CAACC,cAAc,GAAGJ,GAAG,CAACK,QAAQ,CAAC,YAAY,CAAC;IAChDjB,OAAO,CAACC,GAAG,CAAC,IAAI,CAACe,cAAc,CAAC;EAClC;EAEAE,QAAQA,CAAA;IACN;IACA,MAAMC,WAAW,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;IACxD,IAAIF,WAAW,KAAK,MAAM,EAAE;MAC1B,IAAI,CAAC9C,eAAe,CAACiD,YAAY,GAAG,IAAI;KACzC,MAAM,IAAIH,WAAW,KAAK,OAAO,EAAE;MAClC,IAAI,CAAC9C,eAAe,CAACiD,YAAY,GAAG,KAAK;KAC1C,MAAM;MACL,IAAI,CAACjD,eAAe,CAACiD,YAAY,GAAG,IAAI,CAAC,CAAC;;IAE5CtB,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC5B,eAAe,CAACiD,YAAY,CAAC;IAE9D;IACA,MAAMC,iBAAiB,GAAGH,YAAY,CAACC,OAAO,CAAC,IAAI,CAAChC,YAAY,CAACT,YAAY,CAAC;IAC9E,IAAI2C,iBAAiB,IAAI,CAAC,WAAW,EAAE,QAAQ,EAAE,UAAU,CAAC,CAACN,QAAQ,CAACM,iBAAiB,CAAC,EAAE;MACxF,IAAI,CAAC3C,YAAY,GAAG2C,iBAAwD;;IAG9E;IACA,MAAMC,eAAe,GAAGJ,YAAY,CAACC,OAAO,CAAC,IAAI,CAAChC,YAAY,CAACR,UAAU,CAAC;IAC1E,IAAI2C,eAAe,EAAE;MACnB,IAAI;QACF,MAAMC,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACH,eAAe,CAAC;QAE/C;QACA,IAAI,IAAI,CAACI,iBAAiB,CAACH,WAAW,CAAC,EAAE;UACvC,IAAI,CAAC5C,UAAU,GAAG4C,WAAW;UAE7B;UACA,IAAI,CAACI,2BAA2B,CAAC,IAAI,CAAChD,UAAU,CAACC,WAAW,CAAC;UAE7D;UACA,IAAI,IAAI,CAACF,YAAY,KAAK,WAAW,EAAE;YACrC,IAAI,CAACW,kBAAkB,GAAG;cAAE,GAAG,IAAI,CAACV;YAAU,CAAE;;SAEnD,MAAM;UACLmB,OAAO,CAAC8B,IAAI,CAAC,qDAAqD,CAAC;UACnE,IAAI,CAACC,mBAAmB,EAAE;;OAE7B,CAAC,OAAOC,CAAC,EAAE;QACVhC,OAAO,CAACiC,KAAK,CAAC,kCAAkC,EAAED,CAAC,CAAC;QACpD,IAAI,CAACD,mBAAmB,EAAE;;KAE7B,MAAM;MACL;MACA,IAAI,CAACA,mBAAmB,EAAE;;IAG5B;IACA,IAAI,CAAC1D,eAAe,CAACiD,YAAY,GAAG,IAAI,CAAC1C,YAAY,KAAK,WAAW;IAErE;IACAiC,MAAM,CAACqB,gBAAgB,CAAC,gBAAgB,EAAE,IAAI,CAACxC,mBAAoC,CAAC;IAEpF;IACA,IAAI,CAACyC,kBAAkB,GAAG,IAAI,CAAC3D,MAAM,CAAC4D,MAAM,CAACC,SAAS,CAAC1C,KAAK,IAAG;MAC7D,IAAIA,KAAK,YAAYpC,eAAe,EAAE;QACpC;QACA,MAAM+E,UAAU,GAAG,IAAI,CAAC9D,MAAM,CAAC+D,oBAAoB,EAAE;QACrD,MAAMC,WAAW,GAAGF,UAAU,EAAEG,MAAM,EAAEC,KAAK,GAAG,aAAa,CAAC;QAE9D,IAAI,CAACF,WAAW,EAAE;UAChB;UACA,IAAI,CAAC7D,YAAY,GAAG,IAAI;;QAG1B;QACA,IAAI,IAAI,CAACgE,iBAAiB,EAAE;UAC1BC,YAAY,CAAC,IAAI,CAACD,iBAAiB,CAAC;;OAEvC,MAAM,IACLhD,KAAK,YAAYnC,aAAa,IAC9BmC,KAAK,YAAYlC,gBAAgB,IACjCkC,KAAK,YAAYjC,eAAe,EAChC;QACA;QACA,MAAMmF,UAAU,GAAG,IAAI,CAACrE,MAAM,CAACoC,GAAG;QAClC,MAAMkC,WAAW,GAAGD,UAAU,CAAC5B,QAAQ,CAAC,QAAQ,CAAC;QAEjD;QACA,MAAM8B,UAAU,GAAG,IAAI,CAACvE,MAAM,CAAC+D,oBAAoB,EAAE;QACrD,MAAMS,SAAS,GAAGD,UAAU,EAAEN,MAAM,EAAEC,KAAK,GAAG,QAAQ,CAAC;QAEvD;QACA;QACA,MAAMO,eAAe,GAAIH,WAAW,IAAIE,SAAS,GAAI,CAAC,GAAG,GAAG;QAE5D;QACA;QACA,IAAI,CAACL,iBAAiB,GAAGO,UAAU,CAAC,MAAK;UACvC,IAAI,CAACvE,YAAY,GAAG,KAAK;QAC3B,CAAC,EAAEsE,eAAe,CAAC;;IAEvB,CAAC,CAAC;EACJ;EAEAE,SAASA,CAAA;IACP;IACA,MAAMvC,GAAG,GAAGC,MAAM,CAACC,QAAQ,CAACC,IAAI;IAChC,IAAI,CAACC,cAAc,GAAGJ,GAAG,CAACK,QAAQ,CAAC,YAAY,CAAC;EAClD;EAEAmC,WAAWA,CAAA;IACT;IACA,IAAI,IAAI,CAACjB,kBAAkB,EAAE;MAC3B,IAAI,CAACA,kBAAkB,CAACkB,WAAW,EAAE;;IAGvC;IACA,IAAI,IAAI,CAACV,iBAAiB,EAAE;MAC1BC,YAAY,CAAC,IAAI,CAACD,iBAAiB,CAAC;;IAGtC;IACA9B,MAAM,CAACyC,mBAAmB,CAAC,gBAAgB,EAAE,IAAI,CAAC5D,mBAAoC,CAAC;EACzF;EAsEA;;;;EAIA6D,mBAAmBA,CAACC,KAAU;IAC5B;IACA,IAAI,CAAC,IAAI,CAAChE,UAAU,EAAE;MACpB,IAAI,CAACW,UAAU,GAAG,IAAI,CAACtB,UAAU,CAACC,WAAW;MAC7C;;IAGF;IACA,IAAI,CAACU,UAAU,GAAG,IAAI;IAEtB;IACA;IACA;IACA,MAAMiE,iBAAiB,GAAGD,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAACrD,UAAU;IACpD,IAAIsD,iBAAiB,EAAE;MACrB,IAAI,CAACrD,oBAAoB,GAAG,IAAI;;IAGlC;IACA,IAAI,IAAI,CAACA,oBAAoB,EAAE;MAC7B;MACA,IAAI,CAACvB,UAAU,GAAG;QAChBC,WAAW,EAAE0E,KAAK,CAAC,CAAC,CAAC;QACrBzE,WAAW,EAAEyE,KAAK,CAAC,CAAC;OACrB;MAED;MACA,IAAIA,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,CAACxE,WAAW,CAACC,SAAS,CAACH,WAAW,GAAG,CAAC,EAAE;QAC1D,IAAI,CAACF,YAAY,GAAG,WAAW;OAChC,MAAM,IAAI4E,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,CAACxE,WAAW,CAACE,MAAM,CAACJ,WAAW,GAAG,CAAC,EAAE;QAC9D,IAAI,CAACF,YAAY,GAAG,QAAQ;OAC7B,MAAM;QACL,IAAI,CAACA,YAAY,GAAG,UAAU;;;EAGpC;EAEA;;;;EAIA8E,cAAcA,CAACF,KAAU;IACvBxD,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEuD,KAAK,CAAC;IAE1C;IACA,IAAI,CAAChE,UAAU,GAAG,KAAK;IACvB,MAAMmE,YAAY,GAAG,IAAI,CAACvD,oBAAoB;IAC9C,IAAI,CAACA,oBAAoB,GAAG,KAAK;IACjC,IAAI,CAACD,UAAU,GAAG,CAAC;IAEnB;IACA,IAAI,CAACwD,YAAY,EAAE;MACjB;;IAGF;IACA,IAAIC,QAA6C;IACjD,IAAIC,UAAkB;IAEtB,IAAIL,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,CAACxE,WAAW,CAACC,SAAS,CAACH,WAAW,GAAG,CAAC,EAAE;MAC1D8E,QAAQ,GAAG,WAAW;MACtBC,UAAU,GAAG,IAAI,CAAC7E,WAAW,CAACC,SAAS,CAACH,WAAW;KACpD,MAAM,IAAI0E,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,CAACxE,WAAW,CAACE,MAAM,CAACJ,WAAW,GAAG,CAAC,EAAE;MAC9D8E,QAAQ,GAAG,QAAQ;MACnBC,UAAU,GAAG,IAAI,CAAC7E,WAAW,CAACE,MAAM,CAACJ,WAAW;KACjD,MAAM;MACL8E,QAAQ,GAAG,UAAU;MACrBC,UAAU,GAAG,IAAI,CAAC7E,WAAW,CAACG,QAAQ,CAACL,WAAW;;IAGpD;IACA,MAAMgF,aAAa,GAAG,CAAC,CAAC,CAAC;IAEzB;IACA,IAAIrE,IAAI,CAACsE,GAAG,CAACP,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAACxE,WAAW,CAACC,SAAS,CAACH,WAAW,CAAC,IAAIgF,aAAa,EAAE;MAChFD,UAAU,GAAG,IAAI,CAAC7E,WAAW,CAACC,SAAS,CAACH,WAAW;MACnD8E,QAAQ,GAAG,WAAW;KACvB,MAAM,IAAInE,IAAI,CAACsE,GAAG,CAACP,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAACxE,WAAW,CAACE,MAAM,CAACJ,WAAW,CAAC,IAAIgF,aAAa,EAAE;MACpFD,UAAU,GAAG,IAAI,CAAC7E,WAAW,CAACE,MAAM,CAACJ,WAAW;MAChD8E,QAAQ,GAAG,QAAQ;KACpB,MAAM,IAAInE,IAAI,CAACsE,GAAG,CAACP,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAACxE,WAAW,CAACG,QAAQ,CAACL,WAAW,CAAC,IAAIgF,aAAa,EAAE;MACtFD,UAAU,GAAG,IAAI,CAAC7E,WAAW,CAACG,QAAQ,CAACL,WAAW;MAClD8E,QAAQ,GAAG,UAAU;;IAGvB;IACA,IAAI,CAAC/E,UAAU,GAAG;MAChBC,WAAW,EAAE+E,UAAU;MACvB9E,WAAW,EAAE,GAAG,GAAG8E;KACpB;IAED,IAAI,CAACjF,YAAY,GAAGgF,QAAQ;IAE5B;IACA,IAAI,CAAC1D,iBAAiB,EAAE;IAExB;IACA,IAAI,CAAC7B,eAAe,CAACiD,YAAY,GAAG,IAAI,CAAC1C,YAAY,KAAK,WAAW;EACvE;EAEA;;;;EAIAoF,mBAAmBA,CAACrE,KAAU;IAC5BK,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEN,KAAK,CAAC;IAE5C;IACA,IAAI,IAAI,CAACf,YAAY,KAAK,WAAW,EAAE;MACrC,IAAI,CAACA,YAAY,GAAG,QAAQ;MAC5B,IAAI,CAACC,UAAU,GAAG;QAAE,GAAG,IAAI,CAACG,WAAW,CAACE;MAAM,CAAE;KACjD,MAAM,IAAI,IAAI,CAACN,YAAY,KAAK,QAAQ,EAAE;MACzC,IAAI,CAACA,YAAY,GAAG,UAAU;MAC9B,IAAI,CAACC,UAAU,GAAG;QAAE,GAAG,IAAI,CAACG,WAAW,CAACG;MAAQ,CAAE;KACnD,MAAM;MACL,IAAI,CAACP,YAAY,GAAG,WAAW;MAC/B,IAAI,CAACC,UAAU,GAAG;QAAE,GAAG,IAAI,CAACG,WAAW,CAACC;MAAS,CAAE;;IAGrD;IACA,IAAI,CAACiB,iBAAiB,EAAE;IAExB;IACA,IAAI,CAAC7B,eAAe,CAACiD,YAAY,GAAG,IAAI,CAAC1C,YAAY,KAAK,WAAW;EACvE;EAEA;EAEA;;;;EAIAqF,aAAaA,CAACtE,KAAU;IACtBK,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEN,KAAK,CAAC;IAErC;IACA,IAAI,IAAI,CAACH,UAAU,IAAI,IAAI,CAACa,kBAAkB,EAAE;MAC9C;;IAGF;IACA;IACA;IAEA;IACA,IAAI,IAAI,CAACzB,YAAY,KAAK,WAAW,EAAE;MACrC;MACA,MAAMsF,aAAa,GAAG9C,YAAY,CAACC,OAAO,CAAC,IAAI,CAAChC,YAAY,CAACC,oBAAoB,CAAC,IAAI,QAAQ;MAC9F,IAAI,CAACV,YAAY,GAAGsF,aAAsC;MAC1D,IAAI,CAACrF,UAAU,GAAG;QAAE,GAAG,IAAI,CAACG,WAAW,CAAC,IAAI,CAACJ,YAAY;MAAC,CAAE;KAC7D,MAAM;MACL;MACAwC,YAAY,CAAC+C,OAAO,CAAC,IAAI,CAAC9E,YAAY,CAACC,oBAAoB,EAAE,IAAI,CAACV,YAAY,CAAC;MAC/E,IAAI,CAACA,YAAY,GAAG,WAAW;MAC/B,IAAI,CAACC,UAAU,GAAG;QAAE,GAAG,IAAI,CAACG,WAAW,CAACC;MAAS,CAAE;;IAGrD;IACA,IAAI,CAACiB,iBAAiB,EAAE;IAExB;IACA,IAAI,CAAC7B,eAAe,CAACiD,YAAY,GAAG,IAAI,CAAC1C,YAAY,KAAK,WAAW;EACvE;EAEA;;;;EAIAwF,mBAAmBA,CAACzE,KAAiB;IACnC;IACA,IAAIA,KAAK,CAAC0E,MAAM,KAAK,CAAC,EAAE;IAExB;IACA,IAAI,CAAC/D,kBAAkB,GAAGX,KAAK,CAAC2E,OAAO;IACvC,IAAI,CAACjE,kBAAkB,GAAG,IAAI;IAE9B;IACA,IAAI,CAACF,UAAU,GAAG,IAAI,CAACtB,UAAU,CAACC,WAAW;IAE7C;IACAyF,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,mBAAmB,CAAC;IAEhD;IACA/E,KAAK,CAACgF,cAAc,EAAE;IACtBhF,KAAK,CAACiF,eAAe,EAAE;IAEvB;IACAL,QAAQ,CAACrC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC3B,mBAAmB,EAAE;MAAEsE,OAAO,EAAE;IAAI,CAAE,CAAC;IACnFN,QAAQ,CAACrC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACzB,iBAAiB,EAAE;MAAEoE,OAAO,EAAE;IAAI,CAAE,CAAC;EACjF;EAEA;;;;EAIArE,mBAAmBA,CAACb,KAAiB;IACnC;IACA,IAAI,CAAC,IAAI,CAACU,kBAAkB,EAAE;IAE9B;IACAV,KAAK,CAACgF,cAAc,EAAE;IACtBhF,KAAK,CAACiF,eAAe,EAAE;IAEvB;IACA,MAAME,MAAM,GAAGnF,KAAK,CAAC2E,OAAO,GAAG,IAAI,CAAChE,kBAAkB;IAEtD;IACA,IAAI,CAACd,UAAU,GAAG,IAAI;IAEtB;IACA;IACA,MAAMuF,cAAc,GAAGlE,MAAM,CAACmE,UAAU;IACxC,MAAMC,WAAW,GAAGH,MAAM;IAC1B,MAAMI,gBAAgB,GAAID,WAAW,GAAGF,cAAc,GAAI,GAAG;IAE7D;IACA,IAAII,cAAc,GAAG,IAAI,CAAChF,UAAU,GAAG+E,gBAAgB;IAEvD;IACAC,cAAc,GAAG1F,IAAI,CAAC2F,GAAG,CAAC,IAAI,CAAChG,QAAQ,CAACN,WAAW,EAAEqG,cAAc,CAAC;IACpEA,cAAc,GAAG1F,IAAI,CAAC4F,GAAG,CAAC,EAAE,EAAEF,cAAc,CAAC,CAAC,CAAC;IAE/C,MAAMG,cAAc,GAAG,GAAG,GAAGH,cAAc;IAE3C;IACA,IAAI,CAACtG,UAAU,GAAG;MAChBC,WAAW,EAAEqG,cAAc;MAC3BpG,WAAW,EAAEuG;KACd;IAED;IACA,IAAIH,cAAc,IAAI,IAAI,CAACnG,WAAW,CAACC,SAAS,CAACH,WAAW,GAAG,CAAC,EAAE;MAChE,IAAI,CAACF,YAAY,GAAG,WAAW;KAChC,MAAM,IAAIuG,cAAc,IAAI,IAAI,CAACnG,WAAW,CAACE,MAAM,CAACJ,WAAW,GAAG,CAAC,EAAE;MACpE,IAAI,CAACF,YAAY,GAAG,QAAQ;KAC7B,MAAM;MACL,IAAI,CAACA,YAAY,GAAG,UAAU;;IAGhC;IACA,IAAI,CAACH,GAAG,CAAC8G,aAAa,EAAE;EAC1B;EAEA;;;;EAIA5E,iBAAiBA,CAACD,MAAkB;IAClC;IACA,IAAI,CAAC,IAAI,CAACL,kBAAkB,EAAE;IAE9B;IACA,IAAIK,MAAM,EAAE;MACVA,MAAM,CAACiE,cAAc,EAAE;MACvBjE,MAAM,CAACkE,eAAe,EAAE;;IAG1B;IACA,IAAI,CAACvE,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACb,UAAU,GAAG,KAAK;IAEvB;IACA+E,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACe,MAAM,CAAC,mBAAmB,CAAC;IAEnD;IACA,IAAI5B,QAA6C;IACjD,IAAIC,UAAkB;IAEtB,IAAI,IAAI,CAAChF,UAAU,CAACC,WAAW,IAAI,IAAI,CAACE,WAAW,CAACC,SAAS,CAACH,WAAW,GAAG,CAAC,EAAE;MAC7E8E,QAAQ,GAAG,WAAW;MACtBC,UAAU,GAAG,IAAI,CAAC7E,WAAW,CAACC,SAAS,CAACH,WAAW;KACpD,MAAM,IAAI,IAAI,CAACD,UAAU,CAACC,WAAW,IAAI,IAAI,CAACE,WAAW,CAACE,MAAM,CAACJ,WAAW,GAAG,CAAC,EAAE;MACjF8E,QAAQ,GAAG,QAAQ;MACnBC,UAAU,GAAG,IAAI,CAAC7E,WAAW,CAACE,MAAM,CAACJ,WAAW;KACjD,MAAM;MACL8E,QAAQ,GAAG,UAAU;MACrBC,UAAU,GAAG,IAAI,CAAC7E,WAAW,CAACG,QAAQ,CAACL,WAAW;;IAGpD;IACA,MAAMgF,aAAa,GAAG,CAAC,CAAC,CAAC;IAEzB;IACA,IAAIrE,IAAI,CAACsE,GAAG,CAAC,IAAI,CAAClF,UAAU,CAACC,WAAW,GAAG,IAAI,CAACE,WAAW,CAACC,SAAS,CAACH,WAAW,CAAC,IAAIgF,aAAa,EAAE;MACnGD,UAAU,GAAG,IAAI,CAAC7E,WAAW,CAACC,SAAS,CAACH,WAAW;MACnD8E,QAAQ,GAAG,WAAW;KACvB,MAAM,IAAInE,IAAI,CAACsE,GAAG,CAAC,IAAI,CAAClF,UAAU,CAACC,WAAW,GAAG,IAAI,CAACE,WAAW,CAACE,MAAM,CAACJ,WAAW,CAAC,IAAIgF,aAAa,EAAE;MACvGD,UAAU,GAAG,IAAI,CAAC7E,WAAW,CAACE,MAAM,CAACJ,WAAW;MAChD8E,QAAQ,GAAG,QAAQ;KACpB,MAAM,IAAInE,IAAI,CAACsE,GAAG,CAAC,IAAI,CAAClF,UAAU,CAACC,WAAW,GAAG,IAAI,CAACE,WAAW,CAACG,QAAQ,CAACL,WAAW,CAAC,IAAIgF,aAAa,EAAE;MACzGD,UAAU,GAAG,IAAI,CAAC7E,WAAW,CAACG,QAAQ,CAACL,WAAW;MAClD8E,QAAQ,GAAG,UAAU;;IAGvB;IACA,IAAI,CAAC/E,UAAU,GAAG;MAChBC,WAAW,EAAE+E,UAAU;MACvB9E,WAAW,EAAE,GAAG,GAAG8E;KACpB;IAED,IAAI,CAACjF,YAAY,GAAGgF,QAAQ;IAE5B;IACA,IAAI,CAAC1D,iBAAiB,EAAE;IAExB;IACA,IAAI,CAAC7B,eAAe,CAACiD,YAAY,GAAG,IAAI,CAAC1C,YAAY,KAAK,WAAW;IAErE;IACA2F,QAAQ,CAACjB,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC/C,mBAAmB,EAAE;MAAEsE,OAAO,EAAE;IAAI,CAAE,CAAC;IACtFN,QAAQ,CAACjB,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC7C,iBAAiB,EAAE;MAAEoE,OAAO,EAAE;IAAI,CAAE,CAAC;IAElF;IACA,IAAI,CAACpG,GAAG,CAAC8G,aAAa,EAAE;EAC1B;EAkBA;;;;;EAKQ3D,iBAAiBA,CAAC4B,KAAU;IAClC;IACA,IAAI,CAACA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,EAAE,aAAa,IAAIA,KAAK,CAAC,IAAI,EAAE,aAAa,IAAIA,KAAK,CAAC,EAAE;MACjG,OAAO,KAAK;;IAGd;IACA,MAAMiC,WAAW,GAAGjC,KAAK,CAAC1E,WAAW;IACrC,MAAM4G,WAAW,GAAGlC,KAAK,CAACzE,WAAW;IAErC,IAAI,OAAO0G,WAAW,KAAK,QAAQ,IAAI,OAAOC,WAAW,KAAK,QAAQ,EAAE;MACtE,OAAO,KAAK;;IAGd;IACA,IAAID,WAAW,GAAG,IAAI,CAACrG,QAAQ,CAACN,WAAW,IAAI2G,WAAW,GAAG,EAAE,EAAE;MAC/D,OAAO,KAAK;;IAGd,IAAIC,WAAW,GAAG,IAAI,CAACtG,QAAQ,CAACL,WAAW,IAAI2G,WAAW,GAAG,EAAE,EAAE;MAC/D,OAAO,KAAK;;IAGd;IACA,MAAMC,GAAG,GAAGF,WAAW,GAAGC,WAAW;IACrC,IAAIC,GAAG,GAAG,IAAI,IAAIA,GAAG,GAAG,KAAK,EAAE;MAC7B,OAAO,KAAK;;IAGd,OAAO,IAAI;EACb;EAEA;;;;EAIQ9D,2BAA2BA,CAAC+D,YAAoB;IACtD;IACA,IAAIA,YAAY,IAAI,IAAI,CAAC5G,WAAW,CAACC,SAAS,CAACH,WAAW,GAAG,CAAC,EAAE;MAC9D,IAAI,CAACF,YAAY,GAAG,WAAW;KAChC,MAAM,IAAIgH,YAAY,IAAI,IAAI,CAAC5G,WAAW,CAACE,MAAM,CAACJ,WAAW,GAAG,CAAC,EAAE;MAClE,IAAI,CAACF,YAAY,GAAG,QAAQ;KAC7B,MAAM;MACL,IAAI,CAACA,YAAY,GAAG,UAAU;;EAElC;EAEA;;;EAGQmD,mBAAmBA,CAAA;IACzB;IACA,IAAI,CAAClD,UAAU,GAAG;MAAE,GAAG,IAAI,CAACG,WAAW,CAAC,IAAI,CAACJ,YAAY;IAAC,CAAE;IAE5D;IACA,IAAI,IAAI,CAACA,YAAY,KAAK,WAAW,EAAE;MACrC,IAAI,CAACW,kBAAkB,GAAG;QAAE,GAAG,IAAI,CAACV;MAAU,CAAE;;EAEpD;EAEA;;;EAGQqB,iBAAiBA,CAAA;IACvB;IACA,IAAI,IAAI,CAAC0B,iBAAiB,CAAC,IAAI,CAAC/C,UAAU,CAAC,EAAE;MAC3CuC,YAAY,CAAC+C,OAAO,CAAC,IAAI,CAAC9E,YAAY,CAACR,UAAU,EAAE6C,IAAI,CAACmE,SAAS,CAAC,IAAI,CAAChH,UAAU,CAAC,CAAC;MACnFuC,YAAY,CAAC+C,OAAO,CAAC,IAAI,CAAC9E,YAAY,CAACT,YAAY,EAAE,IAAI,CAACA,YAAY,CAAC;KACxE,MAAM;MACLoB,OAAO,CAAC8B,IAAI,CAAC,+DAA+D,CAAC;MAC7E,IAAI,CAACC,mBAAmB,EAAE;MAC1BX,YAAY,CAAC+C,OAAO,CAAC,IAAI,CAAC9E,YAAY,CAACR,UAAU,EAAE6C,IAAI,CAACmE,SAAS,CAAC,IAAI,CAAChH,UAAU,CAAC,CAAC;MACnFuC,YAAY,CAAC+C,OAAO,CAAC,IAAI,CAAC9E,YAAY,CAACT,YAAY,EAAE,IAAI,CAACA,YAAY,CAAC;;EAE3E;;;;;AA9pBWV,YAAY,GAAA4H,UAAA,EAdxB7I,SAAS,CAAC;EACT8I,QAAQ,EAAE,UAAU;EACpBC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACP7I,YAAY,EACZO,gBAAgB,EAChBN,YAAY,EACZO,eAAe,EACfG,cAAc,EACdE,kBAAkB,CACnB;EACDiI,QAAA,EAAAC,oBAAmC;;CAEpC,CAAC,C,EACWjI,YAAY,CA+pBxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}