{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"C:/Users/<USER>/source/ai-hub/WebApp/src/app/components/theme-toogle.component.ts.css?ngResource!=!C:\\\\Users\\\\<USER>\\\\source\\\\ai-hub\\\\WebApp\\\\node_modules\\\\@ngtools\\\\webpack\\\\src\\\\loaders\\\\inline-resource.js?data=CiAgICAgIDpob3N0IHsKICAgICAgICBkaXNwbGF5OiBmbGV4OwogICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOwogICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgIH0KICAgICAgLnRyYW5zLTAgewogICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWCgtMXB4KTsKICAgICAgICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMC4zcyBlYXNlLWluLW91dDsKICAgICAgfQogICAgICAudHJhbnMtMzAgewogICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWCgyOXB4KTsKICAgICAgICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMC4zcyBlYXNlLWluLW91dDsKICAgICAgfQogICAg!C:/Users/<USER>/source/ai-hub/WebApp/src/app/components/theme-toogle.component.ts\";\nimport { Component, inject } from '@angular/core';\nimport { ThemeService } from '../../shared/services/theam.service';\nimport { CommonModule } from '@angular/common';\nlet ThemeToggleComponent = class ThemeToggleComponent {\n  constructor() {\n    this.themeService = inject(ThemeService);\n  }\n};\nThemeToggleComponent = __decorate([Component({\n  selector: 'app-theme-toggle',\n  standalone: true,\n  imports: [CommonModule],\n  template: `\n    <div\n      class=\"relative w-auto h-8 flex items-center rounded-md p-1 pr-3 bg-[#E8ECEF] px-2 overflow-hidden cursor-pointer transition-[var(--transition-default)] shadow-[inset_0_0_5px_rgba(0,0,0,0.2)]\"\n      (click)=\"themeService.toggleTheme()\"\n      aria-label=\"Toggle theme\"\n    >\n      <span\n        class=\"absolute w-8 h-[85%] bg-[var(--background-white)] shadow-[0_2px_5px_rgba(0,0,0,0.2)] rounded-md flex items-center justify-center\"\n        [ngClass]=\"{\n          ' trans-0 ': !themeService.isDarkMode(),\n          'trans-30': themeService.isDarkMode()\n        }\"\n      ></span>\n      <i\n        class=\"ri-sun-line text-[#000000] text-base z-10 ml-1\"\n        [ngClass]=\"{\n          'translate-x-0': !themeService.isDarkMode(),\n          'translate-x-6 ': themeService.isDarkMode()\n        }\"\n      ></i>\n      <i\n        class=\"ri-moon-line text-[#000000] text-base z-10 ml-4 transform transition-[var(--transition-default)]\"\n        [ngClass]=\"{\n          'translate-x-0 !text-white': themeService.isDarkMode(),\n        }\"\n      ></i>\n    </div>\n  `,\n  styles: [__NG_CLI_RESOURCE__0]\n})], ThemeToggleComponent);\nexport { ThemeToggleComponent };", "map": {"version": 3, "names": ["Component", "inject", "ThemeService", "CommonModule", "ThemeToggleComponent", "constructor", "themeService", "__decorate", "selector", "standalone", "imports", "template"], "sources": ["C:\\Users\\<USER>\\source\\ai-hub\\WebApp\\src\\app\\components\\theme-toogle.component.ts"], "sourcesContent": ["import { Component, inject } from '@angular/core';\r\nimport { ThemeService } from '../../shared/services/theam.service';\r\nimport { CommonModule } from '@angular/common';\r\n\r\n@Component({\r\n  selector: 'app-theme-toggle',\r\n  standalone: true,\r\n  imports: [CommonModule],\r\n  template: `\r\n    <div\r\n      class=\"relative w-auto h-8 flex items-center rounded-md p-1 pr-3 bg-[#E8ECEF] px-2 overflow-hidden cursor-pointer transition-[var(--transition-default)] shadow-[inset_0_0_5px_rgba(0,0,0,0.2)]\"\r\n      (click)=\"themeService.toggleTheme()\"\r\n      aria-label=\"Toggle theme\"\r\n    >\r\n      <span\r\n        class=\"absolute w-8 h-[85%] bg-[var(--background-white)] shadow-[0_2px_5px_rgba(0,0,0,0.2)] rounded-md flex items-center justify-center\"\r\n        [ngClass]=\"{\r\n          ' trans-0 ': !themeService.isDarkMode(),\r\n          'trans-30': themeService.isDarkMode()\r\n        }\"\r\n      ></span>\r\n      <i\r\n        class=\"ri-sun-line text-[#000000] text-base z-10 ml-1\"\r\n        [ngClass]=\"{\r\n          'translate-x-0': !themeService.isDarkMode(),\r\n          'translate-x-6 ': themeService.isDarkMode()\r\n        }\"\r\n      ></i>\r\n      <i\r\n        class=\"ri-moon-line text-[#000000] text-base z-10 ml-4 transform transition-[var(--transition-default)]\"\r\n        [ngClass]=\"{\r\n          'translate-x-0 !text-white': themeService.isDarkMode(),\r\n        }\"\r\n      ></i>\r\n    </div>\r\n  `,\r\n  styles: [\r\n    `\r\n      :host {\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n      }\r\n      .trans-0 {\r\n        transform: translateX(-1px);\r\n        transition: transform 0.3s ease-in-out;\r\n      }\r\n      .trans-30 {\r\n        transform: translateX(29px);\r\n        transition: transform 0.3s ease-in-out;\r\n      }\r\n    `,\r\n  ],\r\n})\r\nexport class ThemeToggleComponent {\r\n  themeService = inject(ThemeService);\r\n}\r\n"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,MAAM,QAAQ,eAAe;AACjD,SAASC,YAAY,QAAQ,qCAAqC;AAClE,SAASC,YAAY,QAAQ,iBAAiB;AAoDvC,IAAMC,oBAAoB,GAA1B,MAAMA,oBAAoB;EAA1BC,YAAA;IACL,KAAAC,YAAY,GAAGL,MAAM,CAACC,YAAY,CAAC;EACrC;CAAC;AAFYE,oBAAoB,GAAAG,UAAA,EAlDhCP,SAAS,CAAC;EACTQ,QAAQ,EAAE,kBAAkB;EAC5BC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACP,YAAY,CAAC;EACvBQ,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2BT;;CAkBF,CAAC,C,EACWP,oBAAoB,CAEhC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}