{"ast": null, "code": "import Delta from 'quill-delta';\nimport Emitter from '../core/emitter.js';\nimport Module from '../core/module.js';\nclass Uploader extends Module {\n  constructor(quill, options) {\n    super(quill, options);\n    quill.root.addEventListener('drop', e => {\n      e.preventDefault();\n      let native = null;\n      if (document.caretRangeFromPoint) {\n        native = document.caretRangeFromPoint(e.clientX, e.clientY);\n        // @ts-expect-error\n      } else if (document.caretPositionFromPoint) {\n        // @ts-expect-error\n        const position = document.caretPositionFromPoint(e.clientX, e.clientY);\n        native = document.createRange();\n        native.setStart(position.offsetNode, position.offset);\n        native.setEnd(position.offsetNode, position.offset);\n      }\n      const normalized = native && quill.selection.normalizeNative(native);\n      if (normalized) {\n        const range = quill.selection.normalizedToRange(normalized);\n        if (e.dataTransfer?.files) {\n          this.upload(range, e.dataTransfer.files);\n        }\n      }\n    });\n  }\n  upload(range, files) {\n    const uploads = [];\n    Array.from(files).forEach(file => {\n      if (file && this.options.mimetypes?.includes(file.type)) {\n        uploads.push(file);\n      }\n    });\n    if (uploads.length > 0) {\n      // @ts-expect-error Fix me later\n      this.options.handler.call(this, range, uploads);\n    }\n  }\n}\nUploader.DEFAULTS = {\n  mimetypes: ['image/png', 'image/jpeg'],\n  handler(range, files) {\n    if (!this.quill.scroll.query('image')) {\n      return;\n    }\n    const promises = files.map(file => {\n      return new Promise(resolve => {\n        const reader = new FileReader();\n        reader.onload = () => {\n          resolve(reader.result);\n        };\n        reader.readAsDataURL(file);\n      });\n    });\n    Promise.all(promises).then(images => {\n      const update = images.reduce((delta, image) => {\n        return delta.insert({\n          image\n        });\n      }, new Delta().retain(range.index).delete(range.length));\n      this.quill.updateContents(update, Emitter.sources.USER);\n      this.quill.setSelection(range.index + images.length, Emitter.sources.SILENT);\n    });\n  }\n};\nexport default Uploader;", "map": {"version": 3, "names": ["Delta", "Emitter", "<PERSON><PERSON><PERSON>", "Uploader", "constructor", "quill", "options", "root", "addEventListener", "e", "preventDefault", "native", "document", "caretRangeFromPoint", "clientX", "clientY", "caretPositionFromPoint", "position", "createRange", "setStart", "offsetNode", "offset", "setEnd", "normalized", "selection", "normalizeNative", "range", "normalizedToRange", "dataTransfer", "files", "upload", "uploads", "Array", "from", "for<PERSON>ach", "file", "mimetypes", "includes", "type", "push", "length", "handler", "call", "DEFAULTS", "scroll", "query", "promises", "map", "Promise", "resolve", "reader", "FileReader", "onload", "result", "readAsDataURL", "all", "then", "images", "update", "reduce", "delta", "image", "insert", "retain", "index", "delete", "updateContents", "sources", "USER", "setSelection", "SILENT"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/quill/modules/uploader.js"], "sourcesContent": ["import Delta from 'quill-delta';\nimport Emitter from '../core/emitter.js';\nimport Module from '../core/module.js';\nclass Uploader extends Module {\n  constructor(quill, options) {\n    super(quill, options);\n    quill.root.addEventListener('drop', e => {\n      e.preventDefault();\n      let native = null;\n      if (document.caretRangeFromPoint) {\n        native = document.caretRangeFromPoint(e.clientX, e.clientY);\n        // @ts-expect-error\n      } else if (document.caretPositionFromPoint) {\n        // @ts-expect-error\n        const position = document.caretPositionFromPoint(e.clientX, e.clientY);\n        native = document.createRange();\n        native.setStart(position.offsetNode, position.offset);\n        native.setEnd(position.offsetNode, position.offset);\n      }\n      const normalized = native && quill.selection.normalizeNative(native);\n      if (normalized) {\n        const range = quill.selection.normalizedToRange(normalized);\n        if (e.dataTransfer?.files) {\n          this.upload(range, e.dataTransfer.files);\n        }\n      }\n    });\n  }\n  upload(range, files) {\n    const uploads = [];\n    Array.from(files).forEach(file => {\n      if (file && this.options.mimetypes?.includes(file.type)) {\n        uploads.push(file);\n      }\n    });\n    if (uploads.length > 0) {\n      // @ts-expect-error Fix me later\n      this.options.handler.call(this, range, uploads);\n    }\n  }\n}\nUploader.DEFAULTS = {\n  mimetypes: ['image/png', 'image/jpeg'],\n  handler(range, files) {\n    if (!this.quill.scroll.query('image')) {\n      return;\n    }\n    const promises = files.map(file => {\n      return new Promise(resolve => {\n        const reader = new FileReader();\n        reader.onload = () => {\n          resolve(reader.result);\n        };\n        reader.readAsDataURL(file);\n      });\n    });\n    Promise.all(promises).then(images => {\n      const update = images.reduce((delta, image) => {\n        return delta.insert({\n          image\n        });\n      }, new Delta().retain(range.index).delete(range.length));\n      this.quill.updateContents(update, Emitter.sources.USER);\n      this.quill.setSelection(range.index + images.length, Emitter.sources.SILENT);\n    });\n  }\n};\nexport default Uploader;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,aAAa;AAC/B,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,MAAM,MAAM,mBAAmB;AACtC,MAAMC,QAAQ,SAASD,MAAM,CAAC;EAC5BE,WAAWA,CAACC,KAAK,EAAEC,OAAO,EAAE;IAC1B,KAAK,CAACD,KAAK,EAAEC,OAAO,CAAC;IACrBD,KAAK,CAACE,IAAI,CAACC,gBAAgB,CAAC,MAAM,EAAEC,CAAC,IAAI;MACvCA,CAAC,CAACC,cAAc,CAAC,CAAC;MAClB,IAAIC,MAAM,GAAG,IAAI;MACjB,IAAIC,QAAQ,CAACC,mBAAmB,EAAE;QAChCF,MAAM,GAAGC,QAAQ,CAACC,mBAAmB,CAACJ,CAAC,CAACK,OAAO,EAAEL,CAAC,CAACM,OAAO,CAAC;QAC3D;MACF,CAAC,MAAM,IAAIH,QAAQ,CAACI,sBAAsB,EAAE;QAC1C;QACA,MAAMC,QAAQ,GAAGL,QAAQ,CAACI,sBAAsB,CAACP,CAAC,CAACK,OAAO,EAAEL,CAAC,CAACM,OAAO,CAAC;QACtEJ,MAAM,GAAGC,QAAQ,CAACM,WAAW,CAAC,CAAC;QAC/BP,MAAM,CAACQ,QAAQ,CAACF,QAAQ,CAACG,UAAU,EAAEH,QAAQ,CAACI,MAAM,CAAC;QACrDV,MAAM,CAACW,MAAM,CAACL,QAAQ,CAACG,UAAU,EAAEH,QAAQ,CAACI,MAAM,CAAC;MACrD;MACA,MAAME,UAAU,GAAGZ,MAAM,IAAIN,KAAK,CAACmB,SAAS,CAACC,eAAe,CAACd,MAAM,CAAC;MACpE,IAAIY,UAAU,EAAE;QACd,MAAMG,KAAK,GAAGrB,KAAK,CAACmB,SAAS,CAACG,iBAAiB,CAACJ,UAAU,CAAC;QAC3D,IAAId,CAAC,CAACmB,YAAY,EAAEC,KAAK,EAAE;UACzB,IAAI,CAACC,MAAM,CAACJ,KAAK,EAAEjB,CAAC,CAACmB,YAAY,CAACC,KAAK,CAAC;QAC1C;MACF;IACF,CAAC,CAAC;EACJ;EACAC,MAAMA,CAACJ,KAAK,EAAEG,KAAK,EAAE;IACnB,MAAME,OAAO,GAAG,EAAE;IAClBC,KAAK,CAACC,IAAI,CAACJ,KAAK,CAAC,CAACK,OAAO,CAACC,IAAI,IAAI;MAChC,IAAIA,IAAI,IAAI,IAAI,CAAC7B,OAAO,CAAC8B,SAAS,EAAEC,QAAQ,CAACF,IAAI,CAACG,IAAI,CAAC,EAAE;QACvDP,OAAO,CAACQ,IAAI,CAACJ,IAAI,CAAC;MACpB;IACF,CAAC,CAAC;IACF,IAAIJ,OAAO,CAACS,MAAM,GAAG,CAAC,EAAE;MACtB;MACA,IAAI,CAAClC,OAAO,CAACmC,OAAO,CAACC,IAAI,CAAC,IAAI,EAAEhB,KAAK,EAAEK,OAAO,CAAC;IACjD;EACF;AACF;AACA5B,QAAQ,CAACwC,QAAQ,GAAG;EAClBP,SAAS,EAAE,CAAC,WAAW,EAAE,YAAY,CAAC;EACtCK,OAAOA,CAACf,KAAK,EAAEG,KAAK,EAAE;IACpB,IAAI,CAAC,IAAI,CAACxB,KAAK,CAACuC,MAAM,CAACC,KAAK,CAAC,OAAO,CAAC,EAAE;MACrC;IACF;IACA,MAAMC,QAAQ,GAAGjB,KAAK,CAACkB,GAAG,CAACZ,IAAI,IAAI;MACjC,OAAO,IAAIa,OAAO,CAACC,OAAO,IAAI;QAC5B,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;QAC/BD,MAAM,CAACE,MAAM,GAAG,MAAM;UACpBH,OAAO,CAACC,MAAM,CAACG,MAAM,CAAC;QACxB,CAAC;QACDH,MAAM,CAACI,aAAa,CAACnB,IAAI,CAAC;MAC5B,CAAC,CAAC;IACJ,CAAC,CAAC;IACFa,OAAO,CAACO,GAAG,CAACT,QAAQ,CAAC,CAACU,IAAI,CAACC,MAAM,IAAI;MACnC,MAAMC,MAAM,GAAGD,MAAM,CAACE,MAAM,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAK;QAC7C,OAAOD,KAAK,CAACE,MAAM,CAAC;UAClBD;QACF,CAAC,CAAC;MACJ,CAAC,EAAE,IAAI7D,KAAK,CAAC,CAAC,CAAC+D,MAAM,CAACrC,KAAK,CAACsC,KAAK,CAAC,CAACC,MAAM,CAACvC,KAAK,CAACc,MAAM,CAAC,CAAC;MACxD,IAAI,CAACnC,KAAK,CAAC6D,cAAc,CAACR,MAAM,EAAEzD,OAAO,CAACkE,OAAO,CAACC,IAAI,CAAC;MACvD,IAAI,CAAC/D,KAAK,CAACgE,YAAY,CAAC3C,KAAK,CAACsC,KAAK,GAAGP,MAAM,CAACjB,MAAM,EAAEvC,OAAO,CAACkE,OAAO,CAACG,MAAM,CAAC;IAC9E,CAAC,CAAC;EACJ;AACF,CAAC;AACD,eAAenE,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}