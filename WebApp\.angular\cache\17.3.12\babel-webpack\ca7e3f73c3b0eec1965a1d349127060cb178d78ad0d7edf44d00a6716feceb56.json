{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/source/ai-hub/WebApp/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./add-or-edit-memory.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./add-or-edit-memory.component.css?ngResource\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { MarkdownModule } from 'ngx-markdown';\nimport EditorJS from '@editorjs/editorjs';\nimport Header from '@editorjs/header';\nimport List from '@editorjs/list';\nimport Checklist from '@editorjs/checklist';\nimport Quote from '@editorjs/quote';\nimport Warning from '@editorjs/warning';\nimport Marker from '@editorjs/marker';\nimport CodeTool from '@editorjs/code';\nimport Delimiter from '@editorjs/delimiter';\nimport InlineCode from '@editorjs/inline-code';\nimport Link from '@editorjs/link';\nimport Table from '@editorjs/table';\nimport { MemoryServiceProxy } from '../../../../shared/service-proxies/service-proxies';\n// Import the actual MemoryDto from service-proxies\nimport { MemoryDto } from '../../../../shared/service-proxies/service-proxies';\nimport { ServiceProxyModule } from '../../../../shared/service-proxies/service-proxy.module';\nimport { NzModalModule } from 'ng-zorro-antd/modal';\nlet AddOrEditMemoryComponent = class AddOrEditMemoryComponent {\n  constructor(router, route, _memoryService) {\n    this.router = router;\n    this.route = route;\n    this._memoryService = _memoryService;\n    this.memories = new MemoryDto();\n    this.editor = null;\n    this.editorInitialized = false;\n    this.pendingContent = null;\n    this.isSaving = false;\n    this.hasContent = false;\n    this.isEditMode = false;\n    this.showTagInput = false;\n  }\n  ngOnInit() {\n    this.route.params.subscribe(params => {\n      this.currentMemoryId = params['id'];\n      if (this.currentMemoryId == \"add\") {\n        // This is create mode (new memory)\n        this.isEditMode = false;\n        // Initialize with empty memory for create mode\n        this.memories = new MemoryDto();\n        this.memories.id = \"00000000-0000-0000-0000-000000000000\";\n      } else if (this.currentMemoryId) {\n        // This is edit mode (existing memory)\n        this.isEditMode = true;\n        this.loadMemoryData();\n      } else {\n        // Fallback: Initialize with empty memory for create mode\n        this.isEditMode = false;\n        this.memories = new MemoryDto();\n        this.memories.id = \"00000000-0000-0000-0000-000000000000\";\n      }\n    });\n  }\n  loadMemoryData() {\n    if (!this.currentMemoryId) return;\n    this._memoryService.getById(this.currentMemoryId).subscribe({\n      next: res => {\n        console.log('Loaded memory data:', res);\n        // Create a new MemoryDto and copy properties from the response\n        const customMemory = new MemoryDto();\n        customMemory.id = res.id; // Assign the actual memory ID\n        customMemory.content = res.content;\n        this.memories = customMemory;\n        // Try to parse the content if it's in JSON format\n        if (res.content && typeof res.content === 'string') {\n          try {\n            // Check if content is in EditorJS format\n            if (res.content.startsWith('{')) {\n              const parsedContent = JSON.parse(res.content);\n              // Remove metadata if present\n              if (parsedContent.metadata) {\n                delete parsedContent.metadata;\n              }\n              // Clean the loaded content to remove any HTML entities\n              const cleanedContent = this.cleanEditorContent(parsedContent);\n              // If editor is already initialized, update it directly\n              if (this.editorInitialized && this.editor) {\n                this.updateEditorContent(cleanedContent);\n              } else {\n                // Otherwise, store it to be loaded when editor is ready\n                this.pendingContent = cleanedContent;\n              }\n            } else {\n              // If content is plain text, clean it and prepare it for editor\n              const cleanedText = this.cleanTextContent(res.content);\n              if (this.editorInitialized && this.editor) {\n                this.editor.blocks.clear();\n                this.editor.blocks.insert('paragraph', {\n                  text: cleanedText\n                });\n              } else {\n                // Create a simple blocks structure for plain text\n                this.pendingContent = {\n                  blocks: [{\n                    type: 'paragraph',\n                    data: {\n                      text: cleanedText\n                    }\n                  }]\n                };\n              }\n            }\n          } catch (e) {\n            console.error('Error parsing memory content:', e);\n            // Handle as plain text if parsing fails\n            const cleanedText = this.cleanTextContent(res.content);\n            if (this.editorInitialized && this.editor) {\n              this.editor.blocks.clear();\n              this.editor.blocks.insert('paragraph', {\n                text: cleanedText\n              });\n            } else {\n              this.pendingContent = {\n                blocks: [{\n                  type: 'paragraph',\n                  data: {\n                    text: cleanedText\n                  }\n                }]\n              };\n            }\n          }\n        }\n      },\n      error: err => {\n        console.error('Error loading memory:', err);\n      }\n    });\n  }\n  ngAfterViewInit() {\n    setTimeout(() => {\n      this.initializeEditor();\n      // Clear any default content that might be loaded\n      if (this.editor && this.editor.blocks) {\n        this.editor.blocks.clear();\n      }\n    }, 0);\n  }\n  ngOnDestroy() {\n    if (this.editor) {\n      this.editor.destroy();\n    }\n  }\n  initializeEditor() {\n    if (this.editor) return; // Prevent re-initialization\n    const editorElement = document.getElementById('editor');\n    if (!editorElement) {\n      console.error('Element with ID \"editor\" is missing.');\n      return;\n    }\n    this.editor = new EditorJS({\n      holder: 'editor',\n      minHeight: 400,\n      placeholder: 'Begin typing your memory content here...',\n      autofocus: true,\n      // Enable the built-in toolbar since we removed the custom one\n      inlineToolbar: true,\n      onChange: () => {\n        this.editor?.save().then(data => {\n          this.hasContent = data.blocks.length > 0;\n        });\n      },\n      onReady: () => {\n        this.editorInitialized = true;\n        if (this.pendingContent) {\n          this.updateEditorContent(this.pendingContent);\n          this.pendingContent = null;\n        } else {\n          // Ensure no default content is shown\n          this.editor?.blocks.clear();\n        }\n      },\n      tools: {\n        header: {\n          class: Header,\n          inlineToolbar: true,\n          config: {\n            levels: [1, 2, 3, 4],\n            defaultLevel: 1\n          }\n        },\n        list: {\n          class: List,\n          inlineToolbar: true\n        },\n        checklist: {\n          class: Checklist,\n          inlineToolbar: true\n        },\n        quote: {\n          class: Quote,\n          inlineToolbar: true\n        },\n        warning: {\n          class: Warning,\n          inlineToolbar: true\n        },\n        marker: {\n          class: Marker,\n          inlineToolbar: true\n        },\n        code: {\n          class: CodeTool,\n          inlineToolbar: true\n        },\n        delimiter: {\n          class: Delimiter\n        },\n        inlineCode: {\n          class: InlineCode\n        },\n        link: {\n          class: Link,\n          inlineToolbar: true\n        },\n        table: {\n          class: Table,\n          inlineToolbar: true\n        }\n      }\n    });\n  }\n  saveContent() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.isSaving = true;\n      try {\n        // Get editor content\n        const editorData = yield _this.editor?.save();\n        if (!editorData) {\n          throw new Error('Failed to get editor data');\n        }\n        // Clean the editor data before saving\n        const cleanedEditorData = _this.cleanEditorContent(editorData);\n        // Set content as JSON string\n        _this.memories.content = JSON.stringify(cleanedEditorData);\n        // Create a standard MemoryDto for the API\n        const memoryDto = new MemoryDto();\n        memoryDto.content = _this.memories.content.trim();\n        // Set ID based on mode:\n        // - For create mode (new memory): use empty GUID\n        // - For edit mode (existing memory): use actual memory ID\n        if (_this.isEditMode) {\n          // Edit mode: use the actual memory ID\n          memoryDto.id = _this.memories.id;\n          console.log('Updating existing memory with ID:', memoryDto.id);\n        } else {\n          // Create mode: use empty GUID\n          memoryDto.id = \"00000000-0000-0000-0000-000000000000\";\n          console.log('Creating new memory with empty GUID');\n        }\n        console.log('Cleaned memory data:', memoryDto);\n        // Save the memory\n        _this._memoryService.createOrEdit(memoryDto).subscribe({\n          next: () => {\n            console.log('Memory saved successfully');\n            _this.router.navigate(['/settings/memory']);\n          },\n          error: err => {\n            console.error('Error saving memory:', err);\n            _this.isSaving = false;\n          }\n        });\n      } catch (error) {\n        console.error('Error preparing memory data:', error);\n        _this.isSaving = false;\n      }\n    })();\n  }\n  clearContent() {\n    if (this.editor && this.editor.blocks) {\n      this.editor.blocks.clear();\n    }\n  }\n  /**\n   * Clean EditorJS content by removing HTML entities and excessive whitespace\n   * while preserving the block structure\n   */\n  cleanEditorContent(editorData) {\n    if (!editorData || !editorData.blocks) {\n      return editorData;\n    }\n    // Create a deep copy to avoid modifying the original data\n    const cleanedData = JSON.parse(JSON.stringify(editorData));\n    // Process each block\n    cleanedData.blocks = cleanedData.blocks.map(block => {\n      return this.cleanBlock(block);\n    });\n    return cleanedData;\n  }\n  /**\n   * Clean individual block content based on block type\n   */\n  cleanBlock(block) {\n    if (!block || !block.data) {\n      return block;\n    }\n    const cleanedBlock = {\n      ...block\n    };\n    switch (block.type) {\n      case 'paragraph':\n      case 'header':\n        if (cleanedBlock.data.text) {\n          cleanedBlock.data.text = this.cleanTextContent(cleanedBlock.data.text);\n        }\n        break;\n      case 'list':\n        if (cleanedBlock.data.items && Array.isArray(cleanedBlock.data.items)) {\n          cleanedBlock.data.items = cleanedBlock.data.items.map(item => {\n            if (typeof item === 'string') {\n              return this.cleanTextContent(item);\n            } else if (item && typeof item === 'object' && item.content) {\n              return {\n                ...item,\n                content: this.cleanTextContent(item.content)\n              };\n            }\n            return item;\n          });\n        }\n        break;\n      case 'checklist':\n        if (cleanedBlock.data.items && Array.isArray(cleanedBlock.data.items)) {\n          cleanedBlock.data.items = cleanedBlock.data.items.map(item => {\n            if (item && item.text) {\n              return {\n                ...item,\n                text: this.cleanTextContent(item.text)\n              };\n            }\n            return item;\n          });\n        }\n        break;\n      case 'quote':\n        if (cleanedBlock.data.text) {\n          cleanedBlock.data.text = this.cleanTextContent(cleanedBlock.data.text);\n        }\n        if (cleanedBlock.data.caption) {\n          cleanedBlock.data.caption = this.cleanTextContent(cleanedBlock.data.caption);\n        }\n        break;\n      case 'warning':\n        if (cleanedBlock.data.title) {\n          cleanedBlock.data.title = this.cleanTextContent(cleanedBlock.data.title);\n        }\n        if (cleanedBlock.data.message) {\n          cleanedBlock.data.message = this.cleanTextContent(cleanedBlock.data.message);\n        }\n        break;\n      case 'code':\n        if (cleanedBlock.data.code) {\n          // For code blocks, we want to preserve formatting but still clean HTML entities\n          cleanedBlock.data.code = this.cleanHtmlEntities(cleanedBlock.data.code);\n        }\n        break;\n      case 'table':\n        if (cleanedBlock.data.content && Array.isArray(cleanedBlock.data.content)) {\n          cleanedBlock.data.content = cleanedBlock.data.content.map(row => {\n            return row.map(cell => this.cleanTextContent(cell));\n          });\n        }\n        break;\n      case 'image':\n        if (cleanedBlock.data.caption) {\n          cleanedBlock.data.caption = this.cleanTextContent(cleanedBlock.data.caption);\n        }\n        break;\n      default:\n        // For any other block types, try to clean common text properties\n        if (cleanedBlock.data.text) {\n          cleanedBlock.data.text = this.cleanTextContent(cleanedBlock.data.text);\n        }\n        if (cleanedBlock.data.caption) {\n          cleanedBlock.data.caption = this.cleanTextContent(cleanedBlock.data.caption);\n        }\n        break;\n    }\n    return cleanedBlock;\n  }\n  /**\n   * Clean text content by removing HTML entities and normalizing whitespace\n   */\n  cleanTextContent(text) {\n    if (!text || typeof text !== 'string') {\n      return text;\n    }\n    // First, decode HTML entities\n    let cleanedText = this.cleanHtmlEntities(text);\n    // Normalize whitespace: replace multiple spaces with single space\n    cleanedText = cleanedText.replace(/\\s+/g, ' ');\n    // Trim leading and trailing whitespace\n    cleanedText = cleanedText.trim();\n    return cleanedText;\n  }\n  /**\n   * Convert HTML entities back to regular characters\n   */\n  cleanHtmlEntities(text) {\n    if (!text || typeof text !== 'string') {\n      return text;\n    }\n    // Create a temporary DOM element to decode HTML entities\n    const tempDiv = document.createElement('div');\n    tempDiv.innerHTML = text;\n    let decodedText = tempDiv.textContent || tempDiv.innerText || '';\n    // Additional manual replacements for common entities that might not be caught\n    decodedText = decodedText.replace(/&nbsp;/g, ' ').replace(/&amp;/g, '&').replace(/&lt;/g, '<').replace(/&gt;/g, '>').replace(/&quot;/g, '\"').replace(/&#39;/g, \"'\").replace(/&apos;/g, \"'\");\n    return decodedText;\n  }\n  updateEditorContent(newContent) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        if (!_this2.editor) {\n          console.warn('Editor not initialized yet');\n          return;\n        }\n        yield _this2.editor.isReady;\n        yield _this2.editor.blocks.clear();\n        yield _this2.editor.render(newContent);\n        _this2.hasContent = true;\n      } catch (error) {\n        console.error('Error updating editor content:', error);\n      }\n    })();\n  }\n  showTagInputField() {\n    this.showTagInput = true;\n    setTimeout(() => {\n      document.getElementById('tagInput')?.focus();\n    }, 0);\n  }\n  // Helper methods for toolbar buttons\n  addHeader() {\n    if (this.editor) {\n      this.editor.blocks.insert('header');\n    }\n  }\n  addList(style) {\n    if (this.editor) {\n      this.editor.blocks.insert('list', {\n        style\n      });\n    }\n  }\n  addChecklist() {\n    if (this.editor) {\n      this.editor.blocks.insert('checklist');\n    }\n  }\n  addQuote() {\n    if (this.editor) {\n      this.editor.blocks.insert('quote');\n    }\n  }\n  addCode() {\n    if (this.editor) {\n      this.editor.blocks.insert('code');\n    }\n  }\n  addImage() {\n    if (this.editor) {\n      this.editor.blocks.insert('image');\n    }\n  }\n  addTable() {\n    if (this.editor) {\n      this.editor.blocks.insert('table');\n    }\n  }\n  addWarning() {\n    if (this.editor) {\n      this.editor.blocks.insert('warning');\n    }\n  }\n  addDelimiter() {\n    if (this.editor) {\n      this.editor.blocks.insert('delimiter');\n    }\n  }\n  addLink() {\n    if (this.editor) {\n      this.editor.blocks.insert('link');\n    }\n  }\n  /**\n   * Test method to verify content cleaning functionality\n   * This can be called from browser console for debugging: component.testContentCleaning()\n   */\n  testContentCleaning() {\n    const testData = {\n      blocks: [{\n        type: 'paragraph',\n        data: {\n          text: 'This is a test&nbsp;&nbsp;&nbsp;with multiple&nbsp;spaces and&amp;entities'\n        }\n      }, {\n        type: 'header',\n        data: {\n          text: 'Header&nbsp;with&nbsp;&nbsp;spaces',\n          level: 2\n        }\n      }, {\n        type: 'list',\n        data: {\n          items: ['Item&nbsp;one&nbsp;&nbsp;with spaces', 'Item&nbsp;two&amp;with&lt;entities&gt;']\n        }\n      }]\n    };\n    console.log('Original data:', testData);\n    const cleaned = this.cleanEditorContent(testData);\n    console.log('Cleaned data:', cleaned);\n    return {\n      original: testData,\n      cleaned: cleaned\n    };\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: Router\n    }, {\n      type: ActivatedRoute\n    }, {\n      type: MemoryServiceProxy\n    }];\n  }\n};\nAddOrEditMemoryComponent = __decorate([Component({\n  selector: 'app-add-or-edit-memory',\n  standalone: true,\n  imports: [CommonModule, FormsModule, MarkdownModule, ServiceProxyModule],\n  template: __NG_CLI_RESOURCE__0,\n  providers: [MemoryServiceProxy, ServiceProxyModule, NzModalModule],\n  styles: [__NG_CLI_RESOURCE__1]\n})], AddOrEditMemoryComponent);\nexport { AddOrEditMemoryComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "FormsModule", "ActivatedRoute", "Router", "MarkdownModule", "EditorJS", "Header", "List", "Checklist", "Quote", "Warning", "<PERSON><PERSON>", "CodeTool", "Delimiter", "InlineCode", "Link", "Table", "MemoryServiceProxy", "MemoryDto", "ServiceProxyModule", "NzModalModule", "AddOrEditMemoryComponent", "constructor", "router", "route", "_memoryService", "memories", "editor", "editorInitialized", "pendingContent", "isSaving", "<PERSON><PERSON><PERSON><PERSON>", "isEditMode", "showTagInput", "ngOnInit", "params", "subscribe", "currentMemoryId", "id", "loadMemoryData", "getById", "next", "res", "console", "log", "customMemory", "content", "startsWith", "parsed<PERSON><PERSON><PERSON>", "JSON", "parse", "metadata", "cleanedContent", "cleanEditorContent", "updateEditorContent", "cleanedText", "cleanTextContent", "blocks", "clear", "insert", "text", "type", "data", "e", "error", "err", "ngAfterViewInit", "setTimeout", "initializeEditor", "ngOnDestroy", "destroy", "editor<PERSON><PERSON>", "document", "getElementById", "holder", "minHeight", "placeholder", "autofocus", "inlineToolbar", "onChange", "save", "then", "length", "onReady", "tools", "header", "class", "config", "levels", "defaultLevel", "list", "checklist", "quote", "warning", "marker", "code", "delimiter", "inlineCode", "link", "table", "saveContent", "_this", "_asyncToGenerator", "editorData", "Error", "cleanedEditorData", "stringify", "memoryDto", "trim", "createOrEdit", "navigate", "clearContent", "cleanedData", "map", "block", "cleanBlock", "cleanedBlock", "items", "Array", "isArray", "item", "caption", "title", "message", "cleanHtmlEntities", "row", "cell", "replace", "tempDiv", "createElement", "innerHTML", "decodedText", "textContent", "innerText", "newContent", "_this2", "warn", "isReady", "render", "showTagInputField", "focus", "addHeader", "addList", "style", "addChecklist", "addQuote", "addCode", "addImage", "addTable", "addWarning", "addDelimiter", "addLink", "testContentCleaning", "testData", "level", "cleaned", "original", "__decorate", "selector", "standalone", "imports", "template", "__NG_CLI_RESOURCE__0", "providers"], "sources": ["C:\\Users\\<USER>\\source\\ai-hub\\WebApp\\src\\app\\admin\\Notes\\add-or-edit-memory\\add-or-edit-memory.component.ts"], "sourcesContent": ["import { <PERSON>mpo<PERSON>, OnInit, After<PERSON><PERSON>w<PERSON>ni<PERSON>, <PERSON><PERSON><PERSON>roy } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { MarkdownModule } from 'ngx-markdown';\r\n\r\nimport EditorJS, { ToolConstructable } from '@editorjs/editorjs';\r\nimport Header from '@editorjs/header';\r\nimport List from '@editorjs/list';\r\nimport Checklist from '@editorjs/checklist';\r\nimport Quote from '@editorjs/quote';\r\nimport Warning from '@editorjs/warning';\r\nimport Marker from '@editorjs/marker';\r\nimport CodeTool from '@editorjs/code';\r\nimport Delimiter from '@editorjs/delimiter';\r\nimport InlineCode from '@editorjs/inline-code';\r\nimport Link from '@editorjs/link';\r\nimport Table from '@editorjs/table';\r\nimport ImageTool from '@editorjs/image';\r\nimport { MemoryServiceProxy } from '../../../../shared/service-proxies/service-proxies';\r\n\r\n// Import the actual MemoryDto from service-proxies\r\nimport { MemoryDto   } from '../../../../shared/service-proxies/service-proxies';\r\nimport { ServiceProxyModule } from '../../../../shared/service-proxies/service-proxy.module';\r\nimport { NzModalModule } from 'ng-zorro-antd/modal';\r\n\r\n\r\n@Component({\r\n  selector: 'app-add-or-edit-memory',\r\n  standalone: true,\r\n  imports: [CommonModule, FormsModule, MarkdownModule,ServiceProxyModule],\r\n  templateUrl: './add-or-edit-memory.component.html',\r\n  styleUrls: ['./add-or-edit-memory.component.css'],\r\n  providers: [MemoryServiceProxy,ServiceProxyModule,NzModalModule],\r\n})\r\nexport class AddOrEditMemoryComponent implements OnInit, AfterViewInit, OnDestroy {\r\n\r\n  memories : MemoryDto = new MemoryDto();\r\n\r\n\r\n  public editor: EditorJS | null = null;\r\n  private editorInitialized = false;\r\n  private pendingContent: any = null;\r\n\r\n  currentMemoryId?: string;\r\n  isSaving = false;\r\n  hasContent = false;\r\n  isEditMode = false;\r\n\r\n  showTagInput = false;\r\n\r\n  constructor(\r\n    public router: Router,\r\n    private route: ActivatedRoute,\r\n    private _memoryService: MemoryServiceProxy,\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n    this.route.params.subscribe(params => {\r\n      this.currentMemoryId = params['id'];\r\n      if (this.currentMemoryId == \"add\") {\r\n        // This is create mode (new memory)\r\n        this.isEditMode = false;\r\n        // Initialize with empty memory for create mode\r\n        this.memories = new MemoryDto();\r\n        this.memories.id = \"00000000-0000-0000-0000-000000000000\";\r\n      } else if (this.currentMemoryId) {\r\n        // This is edit mode (existing memory)\r\n        this.isEditMode = true;\r\n        this.loadMemoryData();\r\n      } else {\r\n        // Fallback: Initialize with empty memory for create mode\r\n        this.isEditMode = false;\r\n        this.memories = new MemoryDto();\r\n        this.memories.id = \"00000000-0000-0000-0000-000000000000\";\r\n      }\r\n    });\r\n  }\r\n\r\n  loadMemoryData() {\r\n    if (!this.currentMemoryId) return;\r\n\r\n    this._memoryService.getById(this.currentMemoryId).subscribe({\r\n      next: (res: any) => {\r\n        console.log('Loaded memory data:', res);\r\n        // Create a new MemoryDto and copy properties from the response\r\n        const customMemory = new MemoryDto();\r\n        customMemory.id = res.id; // Assign the actual memory ID\r\n        customMemory.content = res.content;\r\n\r\n        this.memories = customMemory;\r\n\r\n        // Try to parse the content if it's in JSON format\r\n        if (res.content && typeof res.content === 'string') {\r\n          try {\r\n            // Check if content is in EditorJS format\r\n            if (res.content.startsWith('{')) {\r\n              const parsedContent = JSON.parse(res.content);\r\n\r\n              // Remove metadata if present\r\n              if (parsedContent.metadata) {\r\n                delete parsedContent.metadata;\r\n              }\r\n\r\n              // Clean the loaded content to remove any HTML entities\r\n              const cleanedContent = this.cleanEditorContent(parsedContent);\r\n\r\n              // If editor is already initialized, update it directly\r\n              if (this.editorInitialized && this.editor) {\r\n                this.updateEditorContent(cleanedContent);\r\n              } else {\r\n                // Otherwise, store it to be loaded when editor is ready\r\n                this.pendingContent = cleanedContent;\r\n              }\r\n            } else {\r\n              // If content is plain text, clean it and prepare it for editor\r\n              const cleanedText = this.cleanTextContent(res.content);\r\n\r\n              if (this.editorInitialized && this.editor) {\r\n                this.editor.blocks.clear();\r\n                this.editor.blocks.insert('paragraph', {\r\n                  text: cleanedText\r\n                });\r\n              } else {\r\n                // Create a simple blocks structure for plain text\r\n                this.pendingContent = {\r\n                  blocks: [\r\n                    {\r\n                      type: 'paragraph',\r\n                      data: {\r\n                        text: cleanedText\r\n                      }\r\n                    }\r\n                  ]\r\n                };\r\n              }\r\n            }\r\n          } catch (e) {\r\n            console.error('Error parsing memory content:', e);\r\n            // Handle as plain text if parsing fails\r\n            const cleanedText = this.cleanTextContent(res.content);\r\n\r\n            if (this.editorInitialized && this.editor) {\r\n              this.editor.blocks.clear();\r\n              this.editor.blocks.insert('paragraph', {\r\n                text: cleanedText\r\n              });\r\n            } else {\r\n              this.pendingContent = {\r\n                blocks: [\r\n                  {\r\n                    type: 'paragraph',\r\n                    data: {\r\n                      text: cleanedText\r\n                    }\r\n                  }\r\n                ]\r\n              };\r\n            }\r\n          }\r\n        }\r\n      },\r\n      error: (err) => {\r\n        console.error('Error loading memory:', err);\r\n      }\r\n    });\r\n  }\r\n\r\n  ngAfterViewInit() {\r\n    setTimeout(() => {\r\n      this.initializeEditor();\r\n      // Clear any default content that might be loaded\r\n      if (this.editor && this.editor.blocks) {\r\n        this.editor.blocks.clear();\r\n      }\r\n    }, 0);\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    if (this.editor) {\r\n      this.editor.destroy();\r\n    }\r\n  }\r\n\r\n\r\n\r\n  private initializeEditor() {\r\n    if (this.editor) return; // Prevent re-initialization\r\n\r\n    const editorElement = document.getElementById('editor');\r\n    if (!editorElement) {\r\n      console.error('Element with ID \"editor\" is missing.');\r\n      return;\r\n    }\r\n\r\n    this.editor = new EditorJS({\r\n      holder: 'editor',\r\n      minHeight: 400,\r\n      placeholder: 'Begin typing your memory content here...',\r\n      autofocus: true,\r\n      // Enable the built-in toolbar since we removed the custom one\r\n      inlineToolbar: true,\r\n      onChange: () => {\r\n        this.editor?.save().then(data => {\r\n          this.hasContent = data.blocks.length > 0;\r\n        });\r\n      },\r\n      onReady: () => {\r\n        this.editorInitialized = true;\r\n        if (this.pendingContent) {\r\n          this.updateEditorContent(this.pendingContent);\r\n          this.pendingContent = null;\r\n        } else {\r\n          // Ensure no default content is shown\r\n          this.editor?.blocks.clear();\r\n        }\r\n      },\r\n      tools: {\r\n        header: {\r\n          class: Header as unknown as ToolConstructable,\r\n          inlineToolbar: true,\r\n          config: {\r\n            levels: [1, 2, 3, 4],\r\n            defaultLevel: 1\r\n          }\r\n        },\r\n        list: {\r\n          class: List as unknown as ToolConstructable,\r\n          inlineToolbar: true\r\n        },\r\n        checklist: {\r\n          class: Checklist as unknown as ToolConstructable,\r\n          inlineToolbar: true\r\n        },\r\n        quote: {\r\n          class: Quote as unknown as ToolConstructable,\r\n          inlineToolbar: true\r\n        },\r\n        warning: {\r\n          class: Warning as unknown as ToolConstructable,\r\n          inlineToolbar: true\r\n        },\r\n        marker: {\r\n          class: Marker as unknown as ToolConstructable,\r\n          inlineToolbar: true\r\n        },\r\n        code: {\r\n          class: CodeTool as unknown as ToolConstructable,\r\n          inlineToolbar: true\r\n        },\r\n        delimiter: {\r\n          class: Delimiter as unknown as ToolConstructable\r\n        },\r\n        inlineCode: {\r\n          class: InlineCode as unknown as ToolConstructable\r\n        },\r\n        link: {\r\n          class: Link as unknown as ToolConstructable,\r\n          inlineToolbar: true\r\n        },\r\n        table: {\r\n          class: Table as unknown as ToolConstructable,\r\n          inlineToolbar: true\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  async saveContent() {\r\n    this.isSaving = true;\r\n    try {\r\n      // Get editor content\r\n      const editorData = await this.editor?.save();\r\n      if (!editorData) {\r\n        throw new Error('Failed to get editor data');\r\n      }\r\n\r\n      // Clean the editor data before saving\r\n      const cleanedEditorData = this.cleanEditorContent(editorData);\r\n\r\n      // Set content as JSON string\r\n      this.memories.content = JSON.stringify(cleanedEditorData);\r\n\r\n      // Create a standard MemoryDto for the API\r\n      const memoryDto = new MemoryDto();\r\n      memoryDto.content = this.memories.content.trim();\r\n\r\n      // Set ID based on mode:\r\n      // - For create mode (new memory): use empty GUID\r\n      // - For edit mode (existing memory): use actual memory ID\r\n      if (this.isEditMode) {\r\n        // Edit mode: use the actual memory ID\r\n        memoryDto.id = this.memories.id;\r\n        console.log('Updating existing memory with ID:', memoryDto.id);\r\n      } else {\r\n        // Create mode: use empty GUID\r\n        memoryDto.id = \"00000000-0000-0000-0000-000000000000\";\r\n        console.log('Creating new memory with empty GUID');\r\n      }\r\n\r\n      console.log('Cleaned memory data:', memoryDto);\r\n\r\n      // Save the memory\r\n      this._memoryService.createOrEdit(memoryDto).subscribe({\r\n        next: () => {\r\n          console.log('Memory saved successfully');\r\n          this.router.navigate(['/settings/memory']);\r\n        },\r\n        error: (err) => {\r\n          console.error('Error saving memory:', err);\r\n          this.isSaving = false;\r\n        }\r\n      });\r\n    } catch (error) {\r\n      console.error('Error preparing memory data:', error);\r\n      this.isSaving = false;\r\n    }\r\n  }\r\n\r\n  clearContent() {\r\n    if (this.editor && this.editor.blocks) {\r\n      this.editor.blocks.clear();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Clean EditorJS content by removing HTML entities and excessive whitespace\r\n   * while preserving the block structure\r\n   */\r\n  private cleanEditorContent(editorData: any): any {\r\n    if (!editorData || !editorData.blocks) {\r\n      return editorData;\r\n    }\r\n\r\n    // Create a deep copy to avoid modifying the original data\r\n    const cleanedData = JSON.parse(JSON.stringify(editorData));\r\n\r\n    // Process each block\r\n    cleanedData.blocks = cleanedData.blocks.map((block: any) => {\r\n      return this.cleanBlock(block);\r\n    });\r\n\r\n    return cleanedData;\r\n  }\r\n\r\n  /**\r\n   * Clean individual block content based on block type\r\n   */\r\n  private cleanBlock(block: any): any {\r\n    if (!block || !block.data) {\r\n      return block;\r\n    }\r\n\r\n    const cleanedBlock = { ...block };\r\n\r\n    switch (block.type) {\r\n      case 'paragraph':\r\n      case 'header':\r\n        if (cleanedBlock.data.text) {\r\n          cleanedBlock.data.text = this.cleanTextContent(cleanedBlock.data.text);\r\n        }\r\n        break;\r\n\r\n      case 'list':\r\n        if (cleanedBlock.data.items && Array.isArray(cleanedBlock.data.items)) {\r\n          cleanedBlock.data.items = cleanedBlock.data.items.map((item: any) => {\r\n            if (typeof item === 'string') {\r\n              return this.cleanTextContent(item);\r\n            } else if (item && typeof item === 'object' && item.content) {\r\n              return {\r\n                ...item,\r\n                content: this.cleanTextContent(item.content)\r\n              };\r\n            }\r\n            return item;\r\n          });\r\n        }\r\n        break;\r\n\r\n      case 'checklist':\r\n        if (cleanedBlock.data.items && Array.isArray(cleanedBlock.data.items)) {\r\n          cleanedBlock.data.items = cleanedBlock.data.items.map((item: any) => {\r\n            if (item && item.text) {\r\n              return {\r\n                ...item,\r\n                text: this.cleanTextContent(item.text)\r\n              };\r\n            }\r\n            return item;\r\n          });\r\n        }\r\n        break;\r\n\r\n      case 'quote':\r\n        if (cleanedBlock.data.text) {\r\n          cleanedBlock.data.text = this.cleanTextContent(cleanedBlock.data.text);\r\n        }\r\n        if (cleanedBlock.data.caption) {\r\n          cleanedBlock.data.caption = this.cleanTextContent(cleanedBlock.data.caption);\r\n        }\r\n        break;\r\n\r\n      case 'warning':\r\n        if (cleanedBlock.data.title) {\r\n          cleanedBlock.data.title = this.cleanTextContent(cleanedBlock.data.title);\r\n        }\r\n        if (cleanedBlock.data.message) {\r\n          cleanedBlock.data.message = this.cleanTextContent(cleanedBlock.data.message);\r\n        }\r\n        break;\r\n\r\n      case 'code':\r\n        if (cleanedBlock.data.code) {\r\n          // For code blocks, we want to preserve formatting but still clean HTML entities\r\n          cleanedBlock.data.code = this.cleanHtmlEntities(cleanedBlock.data.code);\r\n        }\r\n        break;\r\n\r\n      case 'table':\r\n        if (cleanedBlock.data.content && Array.isArray(cleanedBlock.data.content)) {\r\n          cleanedBlock.data.content = cleanedBlock.data.content.map((row: any[]) => {\r\n            return row.map((cell: string) => this.cleanTextContent(cell));\r\n          });\r\n        }\r\n        break;\r\n\r\n      case 'image':\r\n        if (cleanedBlock.data.caption) {\r\n          cleanedBlock.data.caption = this.cleanTextContent(cleanedBlock.data.caption);\r\n        }\r\n        break;\r\n\r\n      default:\r\n        // For any other block types, try to clean common text properties\r\n        if (cleanedBlock.data.text) {\r\n          cleanedBlock.data.text = this.cleanTextContent(cleanedBlock.data.text);\r\n        }\r\n        if (cleanedBlock.data.caption) {\r\n          cleanedBlock.data.caption = this.cleanTextContent(cleanedBlock.data.caption);\r\n        }\r\n        break;\r\n    }\r\n\r\n    return cleanedBlock;\r\n  }\r\n\r\n  /**\r\n   * Clean text content by removing HTML entities and normalizing whitespace\r\n   */\r\n  private cleanTextContent(text: string): string {\r\n    if (!text || typeof text !== 'string') {\r\n      return text;\r\n    }\r\n\r\n    // First, decode HTML entities\r\n    let cleanedText = this.cleanHtmlEntities(text);\r\n\r\n    // Normalize whitespace: replace multiple spaces with single space\r\n    cleanedText = cleanedText.replace(/\\s+/g, ' ');\r\n\r\n    // Trim leading and trailing whitespace\r\n    cleanedText = cleanedText.trim();\r\n\r\n    return cleanedText;\r\n  }\r\n\r\n  /**\r\n   * Convert HTML entities back to regular characters\r\n   */\r\n  private cleanHtmlEntities(text: string): string {\r\n    if (!text || typeof text !== 'string') {\r\n      return text;\r\n    }\r\n\r\n    // Create a temporary DOM element to decode HTML entities\r\n    const tempDiv = document.createElement('div');\r\n    tempDiv.innerHTML = text;\r\n    let decodedText = tempDiv.textContent || tempDiv.innerText || '';\r\n\r\n    // Additional manual replacements for common entities that might not be caught\r\n    decodedText = decodedText\r\n      .replace(/&nbsp;/g, ' ')\r\n      .replace(/&amp;/g, '&')\r\n      .replace(/&lt;/g, '<')\r\n      .replace(/&gt;/g, '>')\r\n      .replace(/&quot;/g, '\"')\r\n      .replace(/&#39;/g, \"'\")\r\n      .replace(/&apos;/g, \"'\");\r\n\r\n    return decodedText;\r\n  }\r\n\r\n  async updateEditorContent(newContent: any) {\r\n    try {\r\n      if (!this.editor) {\r\n        console.warn('Editor not initialized yet');\r\n        return;\r\n      }\r\n\r\n      await this.editor.isReady;\r\n      await this.editor.blocks.clear();\r\n      await this.editor.render(newContent);\r\n\r\n      this.hasContent = true;\r\n    } catch (error) {\r\n      console.error('Error updating editor content:', error);\r\n    }\r\n  }\r\n\r\n\r\n  showTagInputField() {\r\n    this.showTagInput = true;\r\n    setTimeout(() => {\r\n      document.getElementById('tagInput')?.focus();\r\n    }, 0);\r\n  }\r\n\r\n  // Helper methods for toolbar buttons\r\n  addHeader() {\r\n    if (this.editor) {\r\n      this.editor.blocks.insert('header');\r\n    }\r\n  }\r\n\r\n  addList(style: 'ordered' | 'unordered') {\r\n    if (this.editor) {\r\n      this.editor.blocks.insert('list', { style });\r\n    }\r\n  }\r\n\r\n  addChecklist() {\r\n    if (this.editor) {\r\n      this.editor.blocks.insert('checklist');\r\n    }\r\n  }\r\n\r\n  addQuote() {\r\n    if (this.editor) {\r\n      this.editor.blocks.insert('quote');\r\n    }\r\n  }\r\n\r\n  addCode() {\r\n    if (this.editor) {\r\n      this.editor.blocks.insert('code');\r\n    }\r\n  }\r\n\r\n  addImage() {\r\n    if (this.editor) {\r\n      this.editor.blocks.insert('image');\r\n    }\r\n  }\r\n\r\n  addTable() {\r\n    if (this.editor) {\r\n      this.editor.blocks.insert('table');\r\n    }\r\n  }\r\n\r\n  addWarning() {\r\n    if (this.editor) {\r\n      this.editor.blocks.insert('warning');\r\n    }\r\n  }\r\n\r\n  addDelimiter() {\r\n    if (this.editor) {\r\n      this.editor.blocks.insert('delimiter');\r\n    }\r\n  }\r\n\r\n  addLink() {\r\n    if (this.editor) {\r\n      this.editor.blocks.insert('link');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Test method to verify content cleaning functionality\r\n   * This can be called from browser console for debugging: component.testContentCleaning()\r\n   */\r\n  testContentCleaning() {\r\n    const testData = {\r\n      blocks: [\r\n        {\r\n          type: 'paragraph',\r\n          data: {\r\n            text: 'This is a test&nbsp;&nbsp;&nbsp;with multiple&nbsp;spaces and&amp;entities'\r\n          }\r\n        },\r\n        {\r\n          type: 'header',\r\n          data: {\r\n            text: 'Header&nbsp;with&nbsp;&nbsp;spaces',\r\n            level: 2\r\n          }\r\n        },\r\n        {\r\n          type: 'list',\r\n          data: {\r\n            items: [\r\n              'Item&nbsp;one&nbsp;&nbsp;with spaces',\r\n              'Item&nbsp;two&amp;with&lt;entities&gt;'\r\n            ]\r\n          }\r\n        }\r\n      ]\r\n    };\r\n\r\n    console.log('Original data:', testData);\r\n    const cleaned = this.cleanEditorContent(testData);\r\n    console.log('Cleaned data:', cleaned);\r\n\r\n    return {\r\n      original: testData,\r\n      cleaned: cleaned\r\n    };\r\n  }\r\n}\r\n"], "mappings": ";;;;AAAA,SAASA,SAAS,QAA0C,eAAe;AAC3E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,cAAc,EAAEC,MAAM,QAAQ,iBAAiB;AACxD,SAASC,cAAc,QAAQ,cAAc;AAE7C,OAAOC,QAA+B,MAAM,oBAAoB;AAChE,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAOC,IAAI,MAAM,gBAAgB;AACjC,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,OAAOC,KAAK,MAAM,iBAAiB;AACnC,OAAOC,OAAO,MAAM,mBAAmB;AACvC,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,OAAOC,IAAI,MAAM,gBAAgB;AACjC,OAAOC,KAAK,MAAM,iBAAiB;AAEnC,SAASC,kBAAkB,QAAQ,oDAAoD;AAEvF;AACA,SAASC,SAAS,QAAU,oDAAoD;AAChF,SAASC,kBAAkB,QAAQ,yDAAyD;AAC5F,SAASC,aAAa,QAAQ,qBAAqB;AAW5C,IAAMC,wBAAwB,GAA9B,MAAMA,wBAAwB;EAgBnCC,YACSC,MAAc,EACbC,KAAqB,EACrBC,cAAkC;IAFnC,KAAAF,MAAM,GAANA,MAAM;IACL,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,cAAc,GAAdA,cAAc;IAjBxB,KAAAC,QAAQ,GAAe,IAAIR,SAAS,EAAE;IAG/B,KAAAS,MAAM,GAAoB,IAAI;IAC7B,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,cAAc,GAAQ,IAAI;IAGlC,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,UAAU,GAAG,KAAK;IAElB,KAAAC,YAAY,GAAG,KAAK;EAMhB;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACV,KAAK,CAACW,MAAM,CAACC,SAAS,CAACD,MAAM,IAAG;MACnC,IAAI,CAACE,eAAe,GAAGF,MAAM,CAAC,IAAI,CAAC;MACnC,IAAI,IAAI,CAACE,eAAe,IAAI,KAAK,EAAE;QACjC;QACA,IAAI,CAACL,UAAU,GAAG,KAAK;QACvB;QACA,IAAI,CAACN,QAAQ,GAAG,IAAIR,SAAS,EAAE;QAC/B,IAAI,CAACQ,QAAQ,CAACY,EAAE,GAAG,sCAAsC;OAC1D,MAAM,IAAI,IAAI,CAACD,eAAe,EAAE;QAC/B;QACA,IAAI,CAACL,UAAU,GAAG,IAAI;QACtB,IAAI,CAACO,cAAc,EAAE;OACtB,MAAM;QACL;QACA,IAAI,CAACP,UAAU,GAAG,KAAK;QACvB,IAAI,CAACN,QAAQ,GAAG,IAAIR,SAAS,EAAE;QAC/B,IAAI,CAACQ,QAAQ,CAACY,EAAE,GAAG,sCAAsC;;IAE7D,CAAC,CAAC;EACJ;EAEAC,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACF,eAAe,EAAE;IAE3B,IAAI,CAACZ,cAAc,CAACe,OAAO,CAAC,IAAI,CAACH,eAAe,CAAC,CAACD,SAAS,CAAC;MAC1DK,IAAI,EAAGC,GAAQ,IAAI;QACjBC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEF,GAAG,CAAC;QACvC;QACA,MAAMG,YAAY,GAAG,IAAI3B,SAAS,EAAE;QACpC2B,YAAY,CAACP,EAAE,GAAGI,GAAG,CAACJ,EAAE,CAAC,CAAC;QAC1BO,YAAY,CAACC,OAAO,GAAGJ,GAAG,CAACI,OAAO;QAElC,IAAI,CAACpB,QAAQ,GAAGmB,YAAY;QAE5B;QACA,IAAIH,GAAG,CAACI,OAAO,IAAI,OAAOJ,GAAG,CAACI,OAAO,KAAK,QAAQ,EAAE;UAClD,IAAI;YACF;YACA,IAAIJ,GAAG,CAACI,OAAO,CAACC,UAAU,CAAC,GAAG,CAAC,EAAE;cAC/B,MAAMC,aAAa,GAAGC,IAAI,CAACC,KAAK,CAACR,GAAG,CAACI,OAAO,CAAC;cAE7C;cACA,IAAIE,aAAa,CAACG,QAAQ,EAAE;gBAC1B,OAAOH,aAAa,CAACG,QAAQ;;cAG/B;cACA,MAAMC,cAAc,GAAG,IAAI,CAACC,kBAAkB,CAACL,aAAa,CAAC;cAE7D;cACA,IAAI,IAAI,CAACpB,iBAAiB,IAAI,IAAI,CAACD,MAAM,EAAE;gBACzC,IAAI,CAAC2B,mBAAmB,CAACF,cAAc,CAAC;eACzC,MAAM;gBACL;gBACA,IAAI,CAACvB,cAAc,GAAGuB,cAAc;;aAEvC,MAAM;cACL;cACA,MAAMG,WAAW,GAAG,IAAI,CAACC,gBAAgB,CAACd,GAAG,CAACI,OAAO,CAAC;cAEtD,IAAI,IAAI,CAAClB,iBAAiB,IAAI,IAAI,CAACD,MAAM,EAAE;gBACzC,IAAI,CAACA,MAAM,CAAC8B,MAAM,CAACC,KAAK,EAAE;gBAC1B,IAAI,CAAC/B,MAAM,CAAC8B,MAAM,CAACE,MAAM,CAAC,WAAW,EAAE;kBACrCC,IAAI,EAAEL;iBACP,CAAC;eACH,MAAM;gBACL;gBACA,IAAI,CAAC1B,cAAc,GAAG;kBACpB4B,MAAM,EAAE,CACN;oBACEI,IAAI,EAAE,WAAW;oBACjBC,IAAI,EAAE;sBACJF,IAAI,EAAEL;;mBAET;iBAEJ;;;WAGN,CAAC,OAAOQ,CAAC,EAAE;YACVpB,OAAO,CAACqB,KAAK,CAAC,+BAA+B,EAAED,CAAC,CAAC;YACjD;YACA,MAAMR,WAAW,GAAG,IAAI,CAACC,gBAAgB,CAACd,GAAG,CAACI,OAAO,CAAC;YAEtD,IAAI,IAAI,CAAClB,iBAAiB,IAAI,IAAI,CAACD,MAAM,EAAE;cACzC,IAAI,CAACA,MAAM,CAAC8B,MAAM,CAACC,KAAK,EAAE;cAC1B,IAAI,CAAC/B,MAAM,CAAC8B,MAAM,CAACE,MAAM,CAAC,WAAW,EAAE;gBACrCC,IAAI,EAAEL;eACP,CAAC;aACH,MAAM;cACL,IAAI,CAAC1B,cAAc,GAAG;gBACpB4B,MAAM,EAAE,CACN;kBACEI,IAAI,EAAE,WAAW;kBACjBC,IAAI,EAAE;oBACJF,IAAI,EAAEL;;iBAET;eAEJ;;;;MAIT,CAAC;MACDS,KAAK,EAAGC,GAAG,IAAI;QACbtB,OAAO,CAACqB,KAAK,CAAC,uBAAuB,EAAEC,GAAG,CAAC;MAC7C;KACD,CAAC;EACJ;EAEAC,eAAeA,CAAA;IACbC,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,gBAAgB,EAAE;MACvB;MACA,IAAI,IAAI,CAACzC,MAAM,IAAI,IAAI,CAACA,MAAM,CAAC8B,MAAM,EAAE;QACrC,IAAI,CAAC9B,MAAM,CAAC8B,MAAM,CAACC,KAAK,EAAE;;IAE9B,CAAC,EAAE,CAAC,CAAC;EACP;EAEAW,WAAWA,CAAA;IACT,IAAI,IAAI,CAAC1C,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAAC2C,OAAO,EAAE;;EAEzB;EAIQF,gBAAgBA,CAAA;IACtB,IAAI,IAAI,CAACzC,MAAM,EAAE,OAAO,CAAC;IAEzB,MAAM4C,aAAa,GAAGC,QAAQ,CAACC,cAAc,CAAC,QAAQ,CAAC;IACvD,IAAI,CAACF,aAAa,EAAE;MAClB5B,OAAO,CAACqB,KAAK,CAAC,sCAAsC,CAAC;MACrD;;IAGF,IAAI,CAACrC,MAAM,GAAG,IAAItB,QAAQ,CAAC;MACzBqE,MAAM,EAAE,QAAQ;MAChBC,SAAS,EAAE,GAAG;MACdC,WAAW,EAAE,0CAA0C;MACvDC,SAAS,EAAE,IAAI;MACf;MACAC,aAAa,EAAE,IAAI;MACnBC,QAAQ,EAAEA,CAAA,KAAK;QACb,IAAI,CAACpD,MAAM,EAAEqD,IAAI,EAAE,CAACC,IAAI,CAACnB,IAAI,IAAG;UAC9B,IAAI,CAAC/B,UAAU,GAAG+B,IAAI,CAACL,MAAM,CAACyB,MAAM,GAAG,CAAC;QAC1C,CAAC,CAAC;MACJ,CAAC;MACDC,OAAO,EAAEA,CAAA,KAAK;QACZ,IAAI,CAACvD,iBAAiB,GAAG,IAAI;QAC7B,IAAI,IAAI,CAACC,cAAc,EAAE;UACvB,IAAI,CAACyB,mBAAmB,CAAC,IAAI,CAACzB,cAAc,CAAC;UAC7C,IAAI,CAACA,cAAc,GAAG,IAAI;SAC3B,MAAM;UACL;UACA,IAAI,CAACF,MAAM,EAAE8B,MAAM,CAACC,KAAK,EAAE;;MAE/B,CAAC;MACD0B,KAAK,EAAE;QACLC,MAAM,EAAE;UACNC,KAAK,EAAEhF,MAAsC;UAC7CwE,aAAa,EAAE,IAAI;UACnBS,MAAM,EAAE;YACNC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACpBC,YAAY,EAAE;;SAEjB;QACDC,IAAI,EAAE;UACJJ,KAAK,EAAE/E,IAAoC;UAC3CuE,aAAa,EAAE;SAChB;QACDa,SAAS,EAAE;UACTL,KAAK,EAAE9E,SAAyC;UAChDsE,aAAa,EAAE;SAChB;QACDc,KAAK,EAAE;UACLN,KAAK,EAAE7E,KAAqC;UAC5CqE,aAAa,EAAE;SAChB;QACDe,OAAO,EAAE;UACPP,KAAK,EAAE5E,OAAuC;UAC9CoE,aAAa,EAAE;SAChB;QACDgB,MAAM,EAAE;UACNR,KAAK,EAAE3E,MAAsC;UAC7CmE,aAAa,EAAE;SAChB;QACDiB,IAAI,EAAE;UACJT,KAAK,EAAE1E,QAAwC;UAC/CkE,aAAa,EAAE;SAChB;QACDkB,SAAS,EAAE;UACTV,KAAK,EAAEzE;SACR;QACDoF,UAAU,EAAE;UACVX,KAAK,EAAExE;SACR;QACDoF,IAAI,EAAE;UACJZ,KAAK,EAAEvE,IAAoC;UAC3C+D,aAAa,EAAE;SAChB;QACDqB,KAAK,EAAE;UACLb,KAAK,EAAEtE,KAAqC;UAC5C8D,aAAa,EAAE;;;KAGpB,CAAC;EACJ;EAEMsB,WAAWA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACfD,KAAI,CAACvE,QAAQ,GAAG,IAAI;MACpB,IAAI;QACF;QACA,MAAMyE,UAAU,SAASF,KAAI,CAAC1E,MAAM,EAAEqD,IAAI,EAAE;QAC5C,IAAI,CAACuB,UAAU,EAAE;UACf,MAAM,IAAIC,KAAK,CAAC,2BAA2B,CAAC;;QAG9C;QACA,MAAMC,iBAAiB,GAAGJ,KAAI,CAAChD,kBAAkB,CAACkD,UAAU,CAAC;QAE7D;QACAF,KAAI,CAAC3E,QAAQ,CAACoB,OAAO,GAAGG,IAAI,CAACyD,SAAS,CAACD,iBAAiB,CAAC;QAEzD;QACA,MAAME,SAAS,GAAG,IAAIzF,SAAS,EAAE;QACjCyF,SAAS,CAAC7D,OAAO,GAAGuD,KAAI,CAAC3E,QAAQ,CAACoB,OAAO,CAAC8D,IAAI,EAAE;QAEhD;QACA;QACA;QACA,IAAIP,KAAI,CAACrE,UAAU,EAAE;UACnB;UACA2E,SAAS,CAACrE,EAAE,GAAG+D,KAAI,CAAC3E,QAAQ,CAACY,EAAE;UAC/BK,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE+D,SAAS,CAACrE,EAAE,CAAC;SAC/D,MAAM;UACL;UACAqE,SAAS,CAACrE,EAAE,GAAG,sCAAsC;UACrDK,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;;QAGpDD,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE+D,SAAS,CAAC;QAE9C;QACAN,KAAI,CAAC5E,cAAc,CAACoF,YAAY,CAACF,SAAS,CAAC,CAACvE,SAAS,CAAC;UACpDK,IAAI,EAAEA,CAAA,KAAK;YACTE,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;YACxCyD,KAAI,CAAC9E,MAAM,CAACuF,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;UAC5C,CAAC;UACD9C,KAAK,EAAGC,GAAG,IAAI;YACbtB,OAAO,CAACqB,KAAK,CAAC,sBAAsB,EAAEC,GAAG,CAAC;YAC1CoC,KAAI,CAACvE,QAAQ,GAAG,KAAK;UACvB;SACD,CAAC;OACH,CAAC,OAAOkC,KAAK,EAAE;QACdrB,OAAO,CAACqB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpDqC,KAAI,CAACvE,QAAQ,GAAG,KAAK;;IACtB;EACH;EAEAiF,YAAYA,CAAA;IACV,IAAI,IAAI,CAACpF,MAAM,IAAI,IAAI,CAACA,MAAM,CAAC8B,MAAM,EAAE;MACrC,IAAI,CAAC9B,MAAM,CAAC8B,MAAM,CAACC,KAAK,EAAE;;EAE9B;EAEA;;;;EAIQL,kBAAkBA,CAACkD,UAAe;IACxC,IAAI,CAACA,UAAU,IAAI,CAACA,UAAU,CAAC9C,MAAM,EAAE;MACrC,OAAO8C,UAAU;;IAGnB;IACA,MAAMS,WAAW,GAAG/D,IAAI,CAACC,KAAK,CAACD,IAAI,CAACyD,SAAS,CAACH,UAAU,CAAC,CAAC;IAE1D;IACAS,WAAW,CAACvD,MAAM,GAAGuD,WAAW,CAACvD,MAAM,CAACwD,GAAG,CAAEC,KAAU,IAAI;MACzD,OAAO,IAAI,CAACC,UAAU,CAACD,KAAK,CAAC;IAC/B,CAAC,CAAC;IAEF,OAAOF,WAAW;EACpB;EAEA;;;EAGQG,UAAUA,CAACD,KAAU;IAC3B,IAAI,CAACA,KAAK,IAAI,CAACA,KAAK,CAACpD,IAAI,EAAE;MACzB,OAAOoD,KAAK;;IAGd,MAAME,YAAY,GAAG;MAAE,GAAGF;IAAK,CAAE;IAEjC,QAAQA,KAAK,CAACrD,IAAI;MAChB,KAAK,WAAW;MAChB,KAAK,QAAQ;QACX,IAAIuD,YAAY,CAACtD,IAAI,CAACF,IAAI,EAAE;UAC1BwD,YAAY,CAACtD,IAAI,CAACF,IAAI,GAAG,IAAI,CAACJ,gBAAgB,CAAC4D,YAAY,CAACtD,IAAI,CAACF,IAAI,CAAC;;QAExE;MAEF,KAAK,MAAM;QACT,IAAIwD,YAAY,CAACtD,IAAI,CAACuD,KAAK,IAAIC,KAAK,CAACC,OAAO,CAACH,YAAY,CAACtD,IAAI,CAACuD,KAAK,CAAC,EAAE;UACrED,YAAY,CAACtD,IAAI,CAACuD,KAAK,GAAGD,YAAY,CAACtD,IAAI,CAACuD,KAAK,CAACJ,GAAG,CAAEO,IAAS,IAAI;YAClE,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;cAC5B,OAAO,IAAI,CAAChE,gBAAgB,CAACgE,IAAI,CAAC;aACnC,MAAM,IAAIA,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAIA,IAAI,CAAC1E,OAAO,EAAE;cAC3D,OAAO;gBACL,GAAG0E,IAAI;gBACP1E,OAAO,EAAE,IAAI,CAACU,gBAAgB,CAACgE,IAAI,CAAC1E,OAAO;eAC5C;;YAEH,OAAO0E,IAAI;UACb,CAAC,CAAC;;QAEJ;MAEF,KAAK,WAAW;QACd,IAAIJ,YAAY,CAACtD,IAAI,CAACuD,KAAK,IAAIC,KAAK,CAACC,OAAO,CAACH,YAAY,CAACtD,IAAI,CAACuD,KAAK,CAAC,EAAE;UACrED,YAAY,CAACtD,IAAI,CAACuD,KAAK,GAAGD,YAAY,CAACtD,IAAI,CAACuD,KAAK,CAACJ,GAAG,CAAEO,IAAS,IAAI;YAClE,IAAIA,IAAI,IAAIA,IAAI,CAAC5D,IAAI,EAAE;cACrB,OAAO;gBACL,GAAG4D,IAAI;gBACP5D,IAAI,EAAE,IAAI,CAACJ,gBAAgB,CAACgE,IAAI,CAAC5D,IAAI;eACtC;;YAEH,OAAO4D,IAAI;UACb,CAAC,CAAC;;QAEJ;MAEF,KAAK,OAAO;QACV,IAAIJ,YAAY,CAACtD,IAAI,CAACF,IAAI,EAAE;UAC1BwD,YAAY,CAACtD,IAAI,CAACF,IAAI,GAAG,IAAI,CAACJ,gBAAgB,CAAC4D,YAAY,CAACtD,IAAI,CAACF,IAAI,CAAC;;QAExE,IAAIwD,YAAY,CAACtD,IAAI,CAAC2D,OAAO,EAAE;UAC7BL,YAAY,CAACtD,IAAI,CAAC2D,OAAO,GAAG,IAAI,CAACjE,gBAAgB,CAAC4D,YAAY,CAACtD,IAAI,CAAC2D,OAAO,CAAC;;QAE9E;MAEF,KAAK,SAAS;QACZ,IAAIL,YAAY,CAACtD,IAAI,CAAC4D,KAAK,EAAE;UAC3BN,YAAY,CAACtD,IAAI,CAAC4D,KAAK,GAAG,IAAI,CAAClE,gBAAgB,CAAC4D,YAAY,CAACtD,IAAI,CAAC4D,KAAK,CAAC;;QAE1E,IAAIN,YAAY,CAACtD,IAAI,CAAC6D,OAAO,EAAE;UAC7BP,YAAY,CAACtD,IAAI,CAAC6D,OAAO,GAAG,IAAI,CAACnE,gBAAgB,CAAC4D,YAAY,CAACtD,IAAI,CAAC6D,OAAO,CAAC;;QAE9E;MAEF,KAAK,MAAM;QACT,IAAIP,YAAY,CAACtD,IAAI,CAACiC,IAAI,EAAE;UAC1B;UACAqB,YAAY,CAACtD,IAAI,CAACiC,IAAI,GAAG,IAAI,CAAC6B,iBAAiB,CAACR,YAAY,CAACtD,IAAI,CAACiC,IAAI,CAAC;;QAEzE;MAEF,KAAK,OAAO;QACV,IAAIqB,YAAY,CAACtD,IAAI,CAAChB,OAAO,IAAIwE,KAAK,CAACC,OAAO,CAACH,YAAY,CAACtD,IAAI,CAAChB,OAAO,CAAC,EAAE;UACzEsE,YAAY,CAACtD,IAAI,CAAChB,OAAO,GAAGsE,YAAY,CAACtD,IAAI,CAAChB,OAAO,CAACmE,GAAG,CAAEY,GAAU,IAAI;YACvE,OAAOA,GAAG,CAACZ,GAAG,CAAEa,IAAY,IAAK,IAAI,CAACtE,gBAAgB,CAACsE,IAAI,CAAC,CAAC;UAC/D,CAAC,CAAC;;QAEJ;MAEF,KAAK,OAAO;QACV,IAAIV,YAAY,CAACtD,IAAI,CAAC2D,OAAO,EAAE;UAC7BL,YAAY,CAACtD,IAAI,CAAC2D,OAAO,GAAG,IAAI,CAACjE,gBAAgB,CAAC4D,YAAY,CAACtD,IAAI,CAAC2D,OAAO,CAAC;;QAE9E;MAEF;QACE;QACA,IAAIL,YAAY,CAACtD,IAAI,CAACF,IAAI,EAAE;UAC1BwD,YAAY,CAACtD,IAAI,CAACF,IAAI,GAAG,IAAI,CAACJ,gBAAgB,CAAC4D,YAAY,CAACtD,IAAI,CAACF,IAAI,CAAC;;QAExE,IAAIwD,YAAY,CAACtD,IAAI,CAAC2D,OAAO,EAAE;UAC7BL,YAAY,CAACtD,IAAI,CAAC2D,OAAO,GAAG,IAAI,CAACjE,gBAAgB,CAAC4D,YAAY,CAACtD,IAAI,CAAC2D,OAAO,CAAC;;QAE9E;;IAGJ,OAAOL,YAAY;EACrB;EAEA;;;EAGQ5D,gBAAgBA,CAACI,IAAY;IACnC,IAAI,CAACA,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MACrC,OAAOA,IAAI;;IAGb;IACA,IAAIL,WAAW,GAAG,IAAI,CAACqE,iBAAiB,CAAChE,IAAI,CAAC;IAE9C;IACAL,WAAW,GAAGA,WAAW,CAACwE,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;IAE9C;IACAxE,WAAW,GAAGA,WAAW,CAACqD,IAAI,EAAE;IAEhC,OAAOrD,WAAW;EACpB;EAEA;;;EAGQqE,iBAAiBA,CAAChE,IAAY;IACpC,IAAI,CAACA,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MACrC,OAAOA,IAAI;;IAGb;IACA,MAAMoE,OAAO,GAAGxD,QAAQ,CAACyD,aAAa,CAAC,KAAK,CAAC;IAC7CD,OAAO,CAACE,SAAS,GAAGtE,IAAI;IACxB,IAAIuE,WAAW,GAAGH,OAAO,CAACI,WAAW,IAAIJ,OAAO,CAACK,SAAS,IAAI,EAAE;IAEhE;IACAF,WAAW,GAAGA,WAAW,CACtBJ,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CACvBA,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,CACtBA,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CACrBA,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CACrBA,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CACvBA,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,CACtBA,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC;IAE1B,OAAOI,WAAW;EACpB;EAEM7E,mBAAmBA,CAACgF,UAAe;IAAA,IAAAC,MAAA;IAAA,OAAAjC,iBAAA;MACvC,IAAI;QACF,IAAI,CAACiC,MAAI,CAAC5G,MAAM,EAAE;UAChBgB,OAAO,CAAC6F,IAAI,CAAC,4BAA4B,CAAC;UAC1C;;QAGF,MAAMD,MAAI,CAAC5G,MAAM,CAAC8G,OAAO;QACzB,MAAMF,MAAI,CAAC5G,MAAM,CAAC8B,MAAM,CAACC,KAAK,EAAE;QAChC,MAAM6E,MAAI,CAAC5G,MAAM,CAAC+G,MAAM,CAACJ,UAAU,CAAC;QAEpCC,MAAI,CAACxG,UAAU,GAAG,IAAI;OACvB,CAAC,OAAOiC,KAAK,EAAE;QACdrB,OAAO,CAACqB,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;;IACvD;EACH;EAGA2E,iBAAiBA,CAAA;IACf,IAAI,CAAC1G,YAAY,GAAG,IAAI;IACxBkC,UAAU,CAAC,MAAK;MACdK,QAAQ,CAACC,cAAc,CAAC,UAAU,CAAC,EAAEmE,KAAK,EAAE;IAC9C,CAAC,EAAE,CAAC,CAAC;EACP;EAEA;EACAC,SAASA,CAAA;IACP,IAAI,IAAI,CAAClH,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAAC8B,MAAM,CAACE,MAAM,CAAC,QAAQ,CAAC;;EAEvC;EAEAmF,OAAOA,CAACC,KAA8B;IACpC,IAAI,IAAI,CAACpH,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAAC8B,MAAM,CAACE,MAAM,CAAC,MAAM,EAAE;QAAEoF;MAAK,CAAE,CAAC;;EAEhD;EAEAC,YAAYA,CAAA;IACV,IAAI,IAAI,CAACrH,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAAC8B,MAAM,CAACE,MAAM,CAAC,WAAW,CAAC;;EAE1C;EAEAsF,QAAQA,CAAA;IACN,IAAI,IAAI,CAACtH,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAAC8B,MAAM,CAACE,MAAM,CAAC,OAAO,CAAC;;EAEtC;EAEAuF,OAAOA,CAAA;IACL,IAAI,IAAI,CAACvH,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAAC8B,MAAM,CAACE,MAAM,CAAC,MAAM,CAAC;;EAErC;EAEAwF,QAAQA,CAAA;IACN,IAAI,IAAI,CAACxH,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAAC8B,MAAM,CAACE,MAAM,CAAC,OAAO,CAAC;;EAEtC;EAEAyF,QAAQA,CAAA;IACN,IAAI,IAAI,CAACzH,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAAC8B,MAAM,CAACE,MAAM,CAAC,OAAO,CAAC;;EAEtC;EAEA0F,UAAUA,CAAA;IACR,IAAI,IAAI,CAAC1H,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAAC8B,MAAM,CAACE,MAAM,CAAC,SAAS,CAAC;;EAExC;EAEA2F,YAAYA,CAAA;IACV,IAAI,IAAI,CAAC3H,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAAC8B,MAAM,CAACE,MAAM,CAAC,WAAW,CAAC;;EAE1C;EAEA4F,OAAOA,CAAA;IACL,IAAI,IAAI,CAAC5H,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAAC8B,MAAM,CAACE,MAAM,CAAC,MAAM,CAAC;;EAErC;EAEA;;;;EAIA6F,mBAAmBA,CAAA;IACjB,MAAMC,QAAQ,GAAG;MACfhG,MAAM,EAAE,CACN;QACEI,IAAI,EAAE,WAAW;QACjBC,IAAI,EAAE;UACJF,IAAI,EAAE;;OAET,EACD;QACEC,IAAI,EAAE,QAAQ;QACdC,IAAI,EAAE;UACJF,IAAI,EAAE,oCAAoC;UAC1C8F,KAAK,EAAE;;OAEV,EACD;QACE7F,IAAI,EAAE,MAAM;QACZC,IAAI,EAAE;UACJuD,KAAK,EAAE,CACL,sCAAsC,EACtC,wCAAwC;;OAG7C;KAEJ;IAED1E,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE6G,QAAQ,CAAC;IACvC,MAAME,OAAO,GAAG,IAAI,CAACtG,kBAAkB,CAACoG,QAAQ,CAAC;IACjD9G,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE+G,OAAO,CAAC;IAErC,OAAO;MACLC,QAAQ,EAAEH,QAAQ;MAClBE,OAAO,EAAEA;KACV;EACH;;;;;;;;;;;AAvkBWtI,wBAAwB,GAAAwI,UAAA,EARpC9J,SAAS,CAAC;EACT+J,QAAQ,EAAE,wBAAwB;EAClCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAAChK,YAAY,EAAEC,WAAW,EAAEG,cAAc,EAACe,kBAAkB,CAAC;EACvE8I,QAAA,EAAAC,oBAAkD;EAElDC,SAAS,EAAE,CAAClJ,kBAAkB,EAACE,kBAAkB,EAACC,aAAa,CAAC;;CACjE,CAAC,C,EACWC,wBAAwB,CAwkBpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}