{"ast": null, "code": "import Block from '../blots/block.js';\nclass Blockquote extends Block {\n  static blotName = 'blockquote';\n  static tagName = 'blockquote';\n}\nexport default Blockquote;", "map": {"version": 3, "names": ["Block", "Blockquote", "blotName", "tagName"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/quill/formats/blockquote.js"], "sourcesContent": ["import Block from '../blots/block.js';\nclass Blockquote extends Block {\n  static blotName = 'blockquote';\n  static tagName = 'blockquote';\n}\nexport default Blockquote;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,mBAAmB;AACrC,MAAMC,UAAU,SAASD,KAAK,CAAC;EAC7B,OAAOE,QAAQ,GAAG,YAAY;EAC9B,OAAOC,OAAO,GAAG,YAAY;AAC/B;AACA,eAAeF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}