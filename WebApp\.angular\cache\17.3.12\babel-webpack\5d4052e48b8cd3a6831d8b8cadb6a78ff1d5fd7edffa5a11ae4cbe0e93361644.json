{"ast": null, "code": "// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\nimport { MessageType } from \"./IHubProtocol\";\nimport { LogLevel } from \"./ILogger\";\nimport { TransferFormat } from \"./ITransport\";\nimport { NullLogger } from \"./Loggers\";\nimport { TextMessageFormat } from \"./TextMessageFormat\";\nconst JSON_HUB_PROTOCOL_NAME = \"json\";\n/** Implements the JSON Hub Protocol. */\nexport class JsonHubProtocol {\n  constructor() {\n    /** @inheritDoc */\n    this.name = JSON_HUB_PROTOCOL_NAME;\n    /** @inheritDoc */\n    this.version = 2;\n    /** @inheritDoc */\n    this.transferFormat = TransferFormat.Text;\n  }\n  /** Creates an array of {@link @microsoft/signalr.HubMessage} objects from the specified serialized representation.\r\n   *\r\n   * @param {string} input A string containing the serialized representation.\r\n   * @param {ILogger} logger A logger that will be used to log messages that occur during parsing.\r\n   */\n  parseMessages(input, logger) {\n    // The interface does allow \"ArrayBuffer\" to be passed in, but this implementation does not. So let's throw a useful error.\n    if (typeof input !== \"string\") {\n      throw new Error(\"Invalid input for JSON hub protocol. Expected a string.\");\n    }\n    if (!input) {\n      return [];\n    }\n    if (logger === null) {\n      logger = NullLogger.instance;\n    }\n    // Parse the messages\n    const messages = TextMessageFormat.parse(input);\n    const hubMessages = [];\n    for (const message of messages) {\n      const parsedMessage = JSON.parse(message);\n      if (typeof parsedMessage.type !== \"number\") {\n        throw new Error(\"Invalid payload.\");\n      }\n      switch (parsedMessage.type) {\n        case MessageType.Invocation:\n          this._isInvocationMessage(parsedMessage);\n          break;\n        case MessageType.StreamItem:\n          this._isStreamItemMessage(parsedMessage);\n          break;\n        case MessageType.Completion:\n          this._isCompletionMessage(parsedMessage);\n          break;\n        case MessageType.Ping:\n          // Single value, no need to validate\n          break;\n        case MessageType.Close:\n          // All optional values, no need to validate\n          break;\n        case MessageType.Ack:\n          this._isAckMessage(parsedMessage);\n          break;\n        case MessageType.Sequence:\n          this._isSequenceMessage(parsedMessage);\n          break;\n        default:\n          // Future protocol changes can add message types, old clients can ignore them\n          logger.log(LogLevel.Information, \"Unknown message type '\" + parsedMessage.type + \"' ignored.\");\n          continue;\n      }\n      hubMessages.push(parsedMessage);\n    }\n    return hubMessages;\n  }\n  /** Writes the specified {@link @microsoft/signalr.HubMessage} to a string and returns it.\r\n   *\r\n   * @param {HubMessage} message The message to write.\r\n   * @returns {string} A string containing the serialized representation of the message.\r\n   */\n  writeMessage(message) {\n    return TextMessageFormat.write(JSON.stringify(message));\n  }\n  _isInvocationMessage(message) {\n    this._assertNotEmptyString(message.target, \"Invalid payload for Invocation message.\");\n    if (message.invocationId !== undefined) {\n      this._assertNotEmptyString(message.invocationId, \"Invalid payload for Invocation message.\");\n    }\n  }\n  _isStreamItemMessage(message) {\n    this._assertNotEmptyString(message.invocationId, \"Invalid payload for StreamItem message.\");\n    if (message.item === undefined) {\n      throw new Error(\"Invalid payload for StreamItem message.\");\n    }\n  }\n  _isCompletionMessage(message) {\n    if (message.result && message.error) {\n      throw new Error(\"Invalid payload for Completion message.\");\n    }\n    if (!message.result && message.error) {\n      this._assertNotEmptyString(message.error, \"Invalid payload for Completion message.\");\n    }\n    this._assertNotEmptyString(message.invocationId, \"Invalid payload for Completion message.\");\n  }\n  _isAckMessage(message) {\n    if (typeof message.sequenceId !== 'number') {\n      throw new Error(\"Invalid SequenceId for Ack message.\");\n    }\n  }\n  _isSequenceMessage(message) {\n    if (typeof message.sequenceId !== 'number') {\n      throw new Error(\"Invalid SequenceId for Sequence message.\");\n    }\n  }\n  _assertNotEmptyString(value, errorMessage) {\n    if (typeof value !== \"string\" || value === \"\") {\n      throw new Error(errorMessage);\n    }\n  }\n}", "map": {"version": 3, "names": ["MessageType", "LogLevel", "TransferFormat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TextMessageFormat", "JSON_HUB_PROTOCOL_NAME", "JsonHubProtocol", "constructor", "name", "version", "transferFormat", "Text", "parseMessages", "input", "logger", "Error", "instance", "messages", "parse", "hubMessages", "message", "parsedMessage", "JSON", "type", "Invocation", "_isInvocationMessage", "StreamItem", "_isStreamItemMessage", "Completion", "_isCompletionMessage", "<PERSON>", "Close", "Ack", "_isAckMessage", "Sequence", "_isSequenceMessage", "log", "Information", "push", "writeMessage", "write", "stringify", "_assertNotEmptyString", "target", "invocationId", "undefined", "item", "result", "error", "sequenceId", "value", "errorMessage"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/@microsoft/signalr/dist/esm/JsonHubProtocol.js"], "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\nimport { MessageType } from \"./IHubProtocol\";\r\nimport { LogLevel } from \"./ILogger\";\r\nimport { TransferFormat } from \"./ITransport\";\r\nimport { NullLogger } from \"./Loggers\";\r\nimport { TextMessageFormat } from \"./TextMessageFormat\";\r\nconst JSON_HUB_PROTOCOL_NAME = \"json\";\r\n/** Implements the JSON Hub Protocol. */\r\nexport class JsonHubProtocol {\r\n    constructor() {\r\n        /** @inheritDoc */\r\n        this.name = JSON_HUB_PROTOCOL_NAME;\r\n        /** @inheritDoc */\r\n        this.version = 2;\r\n        /** @inheritDoc */\r\n        this.transferFormat = TransferFormat.Text;\r\n    }\r\n    /** Creates an array of {@link @microsoft/signalr.HubMessage} objects from the specified serialized representation.\r\n     *\r\n     * @param {string} input A string containing the serialized representation.\r\n     * @param {ILogger} logger A logger that will be used to log messages that occur during parsing.\r\n     */\r\n    parseMessages(input, logger) {\r\n        // The interface does allow \"ArrayBuffer\" to be passed in, but this implementation does not. So let's throw a useful error.\r\n        if (typeof input !== \"string\") {\r\n            throw new Error(\"Invalid input for JSON hub protocol. Expected a string.\");\r\n        }\r\n        if (!input) {\r\n            return [];\r\n        }\r\n        if (logger === null) {\r\n            logger = NullLogger.instance;\r\n        }\r\n        // Parse the messages\r\n        const messages = TextMessageFormat.parse(input);\r\n        const hubMessages = [];\r\n        for (const message of messages) {\r\n            const parsedMessage = JSON.parse(message);\r\n            if (typeof parsedMessage.type !== \"number\") {\r\n                throw new Error(\"Invalid payload.\");\r\n            }\r\n            switch (parsedMessage.type) {\r\n                case MessageType.Invocation:\r\n                    this._isInvocationMessage(parsedMessage);\r\n                    break;\r\n                case MessageType.StreamItem:\r\n                    this._isStreamItemMessage(parsedMessage);\r\n                    break;\r\n                case MessageType.Completion:\r\n                    this._isCompletionMessage(parsedMessage);\r\n                    break;\r\n                case MessageType.Ping:\r\n                    // Single value, no need to validate\r\n                    break;\r\n                case MessageType.Close:\r\n                    // All optional values, no need to validate\r\n                    break;\r\n                case MessageType.Ack:\r\n                    this._isAckMessage(parsedMessage);\r\n                    break;\r\n                case MessageType.Sequence:\r\n                    this._isSequenceMessage(parsedMessage);\r\n                    break;\r\n                default:\r\n                    // Future protocol changes can add message types, old clients can ignore them\r\n                    logger.log(LogLevel.Information, \"Unknown message type '\" + parsedMessage.type + \"' ignored.\");\r\n                    continue;\r\n            }\r\n            hubMessages.push(parsedMessage);\r\n        }\r\n        return hubMessages;\r\n    }\r\n    /** Writes the specified {@link @microsoft/signalr.HubMessage} to a string and returns it.\r\n     *\r\n     * @param {HubMessage} message The message to write.\r\n     * @returns {string} A string containing the serialized representation of the message.\r\n     */\r\n    writeMessage(message) {\r\n        return TextMessageFormat.write(JSON.stringify(message));\r\n    }\r\n    _isInvocationMessage(message) {\r\n        this._assertNotEmptyString(message.target, \"Invalid payload for Invocation message.\");\r\n        if (message.invocationId !== undefined) {\r\n            this._assertNotEmptyString(message.invocationId, \"Invalid payload for Invocation message.\");\r\n        }\r\n    }\r\n    _isStreamItemMessage(message) {\r\n        this._assertNotEmptyString(message.invocationId, \"Invalid payload for StreamItem message.\");\r\n        if (message.item === undefined) {\r\n            throw new Error(\"Invalid payload for StreamItem message.\");\r\n        }\r\n    }\r\n    _isCompletionMessage(message) {\r\n        if (message.result && message.error) {\r\n            throw new Error(\"Invalid payload for Completion message.\");\r\n        }\r\n        if (!message.result && message.error) {\r\n            this._assertNotEmptyString(message.error, \"Invalid payload for Completion message.\");\r\n        }\r\n        this._assertNotEmptyString(message.invocationId, \"Invalid payload for Completion message.\");\r\n    }\r\n    _isAckMessage(message) {\r\n        if (typeof message.sequenceId !== 'number') {\r\n            throw new Error(\"Invalid SequenceId for Ack message.\");\r\n        }\r\n    }\r\n    _isSequenceMessage(message) {\r\n        if (typeof message.sequenceId !== 'number') {\r\n            throw new Error(\"Invalid SequenceId for Sequence message.\");\r\n        }\r\n    }\r\n    _assertNotEmptyString(value, errorMessage) {\r\n        if (typeof value !== \"string\" || value === \"\") {\r\n            throw new Error(errorMessage);\r\n        }\r\n    }\r\n}\r\n"], "mappings": "AAAA;AACA;AACA,SAASA,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,QAAQ,QAAQ,WAAW;AACpC,SAASC,cAAc,QAAQ,cAAc;AAC7C,SAASC,UAAU,QAAQ,WAAW;AACtC,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,MAAMC,sBAAsB,GAAG,MAAM;AACrC;AACA,OAAO,MAAMC,eAAe,CAAC;EACzBC,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAACC,IAAI,GAAGH,sBAAsB;IAClC;IACA,IAAI,CAACI,OAAO,GAAG,CAAC;IAChB;IACA,IAAI,CAACC,cAAc,GAAGR,cAAc,CAACS,IAAI;EAC7C;EACA;AACJ;AACA;AACA;AACA;EACIC,aAAaA,CAACC,KAAK,EAAEC,MAAM,EAAE;IACzB;IACA,IAAI,OAAOD,KAAK,KAAK,QAAQ,EAAE;MAC3B,MAAM,IAAIE,KAAK,CAAC,yDAAyD,CAAC;IAC9E;IACA,IAAI,CAACF,KAAK,EAAE;MACR,OAAO,EAAE;IACb;IACA,IAAIC,MAAM,KAAK,IAAI,EAAE;MACjBA,MAAM,GAAGX,UAAU,CAACa,QAAQ;IAChC;IACA;IACA,MAAMC,QAAQ,GAAGb,iBAAiB,CAACc,KAAK,CAACL,KAAK,CAAC;IAC/C,MAAMM,WAAW,GAAG,EAAE;IACtB,KAAK,MAAMC,OAAO,IAAIH,QAAQ,EAAE;MAC5B,MAAMI,aAAa,GAAGC,IAAI,CAACJ,KAAK,CAACE,OAAO,CAAC;MACzC,IAAI,OAAOC,aAAa,CAACE,IAAI,KAAK,QAAQ,EAAE;QACxC,MAAM,IAAIR,KAAK,CAAC,kBAAkB,CAAC;MACvC;MACA,QAAQM,aAAa,CAACE,IAAI;QACtB,KAAKvB,WAAW,CAACwB,UAAU;UACvB,IAAI,CAACC,oBAAoB,CAACJ,aAAa,CAAC;UACxC;QACJ,KAAKrB,WAAW,CAAC0B,UAAU;UACvB,IAAI,CAACC,oBAAoB,CAACN,aAAa,CAAC;UACxC;QACJ,KAAKrB,WAAW,CAAC4B,UAAU;UACvB,IAAI,CAACC,oBAAoB,CAACR,aAAa,CAAC;UACxC;QACJ,KAAKrB,WAAW,CAAC8B,IAAI;UACjB;UACA;QACJ,KAAK9B,WAAW,CAAC+B,KAAK;UAClB;UACA;QACJ,KAAK/B,WAAW,CAACgC,GAAG;UAChB,IAAI,CAACC,aAAa,CAACZ,aAAa,CAAC;UACjC;QACJ,KAAKrB,WAAW,CAACkC,QAAQ;UACrB,IAAI,CAACC,kBAAkB,CAACd,aAAa,CAAC;UACtC;QACJ;UACI;UACAP,MAAM,CAACsB,GAAG,CAACnC,QAAQ,CAACoC,WAAW,EAAE,wBAAwB,GAAGhB,aAAa,CAACE,IAAI,GAAG,YAAY,CAAC;UAC9F;MACR;MACAJ,WAAW,CAACmB,IAAI,CAACjB,aAAa,CAAC;IACnC;IACA,OAAOF,WAAW;EACtB;EACA;AACJ;AACA;AACA;AACA;EACIoB,YAAYA,CAACnB,OAAO,EAAE;IAClB,OAAOhB,iBAAiB,CAACoC,KAAK,CAAClB,IAAI,CAACmB,SAAS,CAACrB,OAAO,CAAC,CAAC;EAC3D;EACAK,oBAAoBA,CAACL,OAAO,EAAE;IAC1B,IAAI,CAACsB,qBAAqB,CAACtB,OAAO,CAACuB,MAAM,EAAE,yCAAyC,CAAC;IACrF,IAAIvB,OAAO,CAACwB,YAAY,KAAKC,SAAS,EAAE;MACpC,IAAI,CAACH,qBAAqB,CAACtB,OAAO,CAACwB,YAAY,EAAE,yCAAyC,CAAC;IAC/F;EACJ;EACAjB,oBAAoBA,CAACP,OAAO,EAAE;IAC1B,IAAI,CAACsB,qBAAqB,CAACtB,OAAO,CAACwB,YAAY,EAAE,yCAAyC,CAAC;IAC3F,IAAIxB,OAAO,CAAC0B,IAAI,KAAKD,SAAS,EAAE;MAC5B,MAAM,IAAI9B,KAAK,CAAC,yCAAyC,CAAC;IAC9D;EACJ;EACAc,oBAAoBA,CAACT,OAAO,EAAE;IAC1B,IAAIA,OAAO,CAAC2B,MAAM,IAAI3B,OAAO,CAAC4B,KAAK,EAAE;MACjC,MAAM,IAAIjC,KAAK,CAAC,yCAAyC,CAAC;IAC9D;IACA,IAAI,CAACK,OAAO,CAAC2B,MAAM,IAAI3B,OAAO,CAAC4B,KAAK,EAAE;MAClC,IAAI,CAACN,qBAAqB,CAACtB,OAAO,CAAC4B,KAAK,EAAE,yCAAyC,CAAC;IACxF;IACA,IAAI,CAACN,qBAAqB,CAACtB,OAAO,CAACwB,YAAY,EAAE,yCAAyC,CAAC;EAC/F;EACAX,aAAaA,CAACb,OAAO,EAAE;IACnB,IAAI,OAAOA,OAAO,CAAC6B,UAAU,KAAK,QAAQ,EAAE;MACxC,MAAM,IAAIlC,KAAK,CAAC,qCAAqC,CAAC;IAC1D;EACJ;EACAoB,kBAAkBA,CAACf,OAAO,EAAE;IACxB,IAAI,OAAOA,OAAO,CAAC6B,UAAU,KAAK,QAAQ,EAAE;MACxC,MAAM,IAAIlC,KAAK,CAAC,0CAA0C,CAAC;IAC/D;EACJ;EACA2B,qBAAqBA,CAACQ,KAAK,EAAEC,YAAY,EAAE;IACvC,IAAI,OAAOD,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,EAAE,EAAE;MAC3C,MAAM,IAAInC,KAAK,CAACoC,YAAY,CAAC;IACjC;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}