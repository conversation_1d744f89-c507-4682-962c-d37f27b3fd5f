{"ast": null, "code": "import { ClassAttributor, Scope, StyleAttributor } from 'parchment';\nconst SizeClass = new ClassAttributor('size', 'ql-size', {\n  scope: Scope.INLINE,\n  whitelist: ['small', 'large', 'huge']\n});\nconst SizeStyle = new StyleAttributor('size', 'font-size', {\n  scope: Scope.INLINE,\n  whitelist: ['10px', '18px', '32px']\n});\nexport { SizeClass, SizeStyle };", "map": {"version": 3, "names": ["ClassAttributor", "<PERSON><PERSON>", "StyleAttributor", "SizeClass", "scope", "INLINE", "whitelist", "SizeStyle"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/quill/formats/size.js"], "sourcesContent": ["import { ClassAttributor, Scope, StyleAttributor } from 'parchment';\nconst SizeClass = new ClassAttributor('size', 'ql-size', {\n  scope: Scope.INLINE,\n  whitelist: ['small', 'large', 'huge']\n});\nconst SizeStyle = new StyleAttributor('size', 'font-size', {\n  scope: Scope.INLINE,\n  whitelist: ['10px', '18px', '32px']\n});\nexport { SizeClass, SizeStyle };\n"], "mappings": "AAAA,SAASA,eAAe,EAAEC,KAAK,EAAEC,eAAe,QAAQ,WAAW;AACnE,MAAMC,SAAS,GAAG,IAAIH,eAAe,CAAC,MAAM,EAAE,SAAS,EAAE;EACvDI,KAAK,EAAEH,KAAK,CAACI,MAAM;EACnBC,SAAS,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM;AACtC,CAAC,CAAC;AACF,MAAMC,SAAS,GAAG,IAAIL,eAAe,CAAC,MAAM,EAAE,WAAW,EAAE;EACzDE,KAAK,EAAEH,KAAK,CAACI,MAAM;EACnBC,SAAS,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM;AACpC,CAAC,CAAC;AACF,SAASH,SAAS,EAAEI,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}