{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { BehaviorSubject } from 'rxjs';\nlet ActiveDocumentService = class ActiveDocumentService {\n  constructor() {\n    this.activeDocumentSubject = new BehaviorSubject(null);\n    // Observable for components to subscribe to active document changes\n    this.activeDocument$ = this.activeDocumentSubject.asObservable();\n    console.log('🔧 ActiveDocumentService initialized');\n  }\n  /**\n   * Set the currently active document\n   * @param document The document to set as active\n   * @param context Optional context information (route, workspace, etc.)\n   */\n  setActiveDocument(document, context) {\n    if (!document || !document.id) {\n      console.warn('⚠️ Attempted to set invalid document as active:', document);\n      return;\n    }\n    const activeDoc = {\n      id: document.id,\n      title: document.title || document.name || `Document ${document.id}`,\n      workspaceName: context?.workspaceName,\n      route: context?.route\n    };\n    console.log('📄 Setting active document:', activeDoc);\n    this.activeDocumentSubject.next(activeDoc);\n  }\n  /**\n   * Clear the currently active document\n   */\n  clearActiveDocument() {\n    console.log('🗑️ Clearing active document');\n    this.activeDocumentSubject.next(null);\n  }\n  /**\n   * Get the current active document (synchronous)\n   * @returns The currently active document or null\n   */\n  getCurrentActiveDocument() {\n    return this.activeDocumentSubject.value;\n  }\n  /**\n   * Check if a specific document is currently active\n   * @param documentId The ID of the document to check\n   * @returns True if the document is active, false otherwise\n   */\n  isDocumentActive(documentId) {\n    const activeDoc = this.getCurrentActiveDocument();\n    const isActive = activeDoc !== null && activeDoc.id === documentId;\n    if (isActive) {\n      console.log('✅ Document is active:', activeDoc.title, 'ID:', documentId);\n    }\n    return isActive;\n  }\n  /**\n   * Get the active document ID (convenience method)\n   * @returns The ID of the active document or null\n   */\n  getActiveDocumentId() {\n    const activeDoc = this.getCurrentActiveDocument();\n    return activeDoc ? activeDoc.id : null;\n  }\n  /**\n   * Update active document properties without changing the ID\n   * @param updates Partial updates to apply to the active document\n   */\n  updateActiveDocument(updates) {\n    const currentDoc = this.getCurrentActiveDocument();\n    if (currentDoc) {\n      const updatedDoc = {\n        ...currentDoc,\n        ...updates\n      };\n      console.log('🔄 Updating active document:', updatedDoc);\n      this.activeDocumentSubject.next(updatedDoc);\n    }\n  }\n  /**\n   * Set active document by ID only (useful when you only have the ID)\n   * @param documentId The ID of the document to set as active\n   * @param title Optional title for the document\n   * @param context Optional context information\n   */\n  setActiveDocumentById(documentId, title, context) {\n    const activeDoc = {\n      id: documentId,\n      title: title || `Document ${documentId}`,\n      workspaceName: context?.workspaceName,\n      route: context?.route\n    };\n    console.log('📄 Setting active document by ID:', activeDoc);\n    this.activeDocumentSubject.next(activeDoc);\n  }\n  static {\n    this.ctorParameters = () => [];\n  }\n};\nActiveDocumentService = __decorate([Injectable({\n  providedIn: 'root'\n})], ActiveDocumentService);\nexport { ActiveDocumentService };", "map": {"version": 3, "names": ["Injectable", "BehaviorSubject", "ActiveDocumentService", "constructor", "activeDocumentSubject", "activeDocument$", "asObservable", "console", "log", "setActiveDocument", "document", "context", "id", "warn", "activeDoc", "title", "name", "workspaceName", "route", "next", "clearActiveDocument", "getCurrentActiveDocument", "value", "isDocumentActive", "documentId", "isActive", "getActiveDocumentId", "updateActiveDocument", "updates", "currentDoc", "updatedDoc", "setActiveDocumentById", "__decorate", "providedIn"], "sources": ["C:\\Users\\<USER>\\source\\ai-hub\\WebApp\\src\\app\\shared\\services\\active-document.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { BehaviorSubject, Observable } from 'rxjs';\n\nexport interface ActiveDocument {\n  id: number;\n  title: string;\n  workspaceName?: string;\n  route?: string; // Optional: track which route/context the document is active in\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class ActiveDocumentService {\n  private activeDocumentSubject = new BehaviorSubject<ActiveDocument | null>(null);\n  \n  // Observable for components to subscribe to active document changes\n  public activeDocument$: Observable<ActiveDocument | null> = this.activeDocumentSubject.asObservable();\n\n  constructor() {\n    console.log('🔧 ActiveDocumentService initialized');\n  }\n\n  /**\n   * Set the currently active document\n   * @param document The document to set as active\n   * @param context Optional context information (route, workspace, etc.)\n   */\n  setActiveDocument(document: any, context?: { workspaceName?: string; route?: string }): void {\n    if (!document || !document.id) {\n      console.warn('⚠️ Attempted to set invalid document as active:', document);\n      return;\n    }\n\n    const activeDoc: ActiveDocument = {\n      id: document.id,\n      title: document.title || document.name || `Document ${document.id}`,\n      workspaceName: context?.workspaceName,\n      route: context?.route\n    };\n\n    console.log('📄 Setting active document:', activeDoc);\n    this.activeDocumentSubject.next(activeDoc);\n  }\n\n  /**\n   * Clear the currently active document\n   */\n  clearActiveDocument(): void {\n    console.log('🗑️ Clearing active document');\n    this.activeDocumentSubject.next(null);\n  }\n\n  /**\n   * Get the current active document (synchronous)\n   * @returns The currently active document or null\n   */\n  getCurrentActiveDocument(): ActiveDocument | null {\n    return this.activeDocumentSubject.value;\n  }\n\n  /**\n   * Check if a specific document is currently active\n   * @param documentId The ID of the document to check\n   * @returns True if the document is active, false otherwise\n   */\n  isDocumentActive(documentId: number): boolean {\n    const activeDoc = this.getCurrentActiveDocument();\n    const isActive = activeDoc !== null && activeDoc.id === documentId;\n    \n    if (isActive) {\n      console.log('✅ Document is active:', activeDoc.title, 'ID:', documentId);\n    }\n    \n    return isActive;\n  }\n\n  /**\n   * Get the active document ID (convenience method)\n   * @returns The ID of the active document or null\n   */\n  getActiveDocumentId(): number | null {\n    const activeDoc = this.getCurrentActiveDocument();\n    return activeDoc ? activeDoc.id : null;\n  }\n\n  /**\n   * Update active document properties without changing the ID\n   * @param updates Partial updates to apply to the active document\n   */\n  updateActiveDocument(updates: Partial<ActiveDocument>): void {\n    const currentDoc = this.getCurrentActiveDocument();\n    if (currentDoc) {\n      const updatedDoc = { ...currentDoc, ...updates };\n      console.log('🔄 Updating active document:', updatedDoc);\n      this.activeDocumentSubject.next(updatedDoc);\n    }\n  }\n\n  /**\n   * Set active document by ID only (useful when you only have the ID)\n   * @param documentId The ID of the document to set as active\n   * @param title Optional title for the document\n   * @param context Optional context information\n   */\n  setActiveDocumentById(documentId: number, title?: string, context?: { workspaceName?: string; route?: string }): void {\n    const activeDoc: ActiveDocument = {\n      id: documentId,\n      title: title || `Document ${documentId}`,\n      workspaceName: context?.workspaceName,\n      route: context?.route\n    };\n\n    console.log('📄 Setting active document by ID:', activeDoc);\n    this.activeDocumentSubject.next(activeDoc);\n  }\n}\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,eAAe,QAAoB,MAAM;AAY3C,IAAMC,qBAAqB,GAA3B,MAAMA,qBAAqB;EAMhCC,YAAA;IALQ,KAAAC,qBAAqB,GAAG,IAAIH,eAAe,CAAwB,IAAI,CAAC;IAEhF;IACO,KAAAI,eAAe,GAAsC,IAAI,CAACD,qBAAqB,CAACE,YAAY,EAAE;IAGnGC,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;EACrD;EAEA;;;;;EAKAC,iBAAiBA,CAACC,QAAa,EAAEC,OAAoD;IACnF,IAAI,CAACD,QAAQ,IAAI,CAACA,QAAQ,CAACE,EAAE,EAAE;MAC7BL,OAAO,CAACM,IAAI,CAAC,iDAAiD,EAAEH,QAAQ,CAAC;MACzE;;IAGF,MAAMI,SAAS,GAAmB;MAChCF,EAAE,EAAEF,QAAQ,CAACE,EAAE;MACfG,KAAK,EAAEL,QAAQ,CAACK,KAAK,IAAIL,QAAQ,CAACM,IAAI,IAAI,YAAYN,QAAQ,CAACE,EAAE,EAAE;MACnEK,aAAa,EAAEN,OAAO,EAAEM,aAAa;MACrCC,KAAK,EAAEP,OAAO,EAAEO;KACjB;IAEDX,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEM,SAAS,CAAC;IACrD,IAAI,CAACV,qBAAqB,CAACe,IAAI,CAACL,SAAS,CAAC;EAC5C;EAEA;;;EAGAM,mBAAmBA,CAAA;IACjBb,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;IAC3C,IAAI,CAACJ,qBAAqB,CAACe,IAAI,CAAC,IAAI,CAAC;EACvC;EAEA;;;;EAIAE,wBAAwBA,CAAA;IACtB,OAAO,IAAI,CAACjB,qBAAqB,CAACkB,KAAK;EACzC;EAEA;;;;;EAKAC,gBAAgBA,CAACC,UAAkB;IACjC,MAAMV,SAAS,GAAG,IAAI,CAACO,wBAAwB,EAAE;IACjD,MAAMI,QAAQ,GAAGX,SAAS,KAAK,IAAI,IAAIA,SAAS,CAACF,EAAE,KAAKY,UAAU;IAElE,IAAIC,QAAQ,EAAE;MACZlB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEM,SAAS,CAACC,KAAK,EAAE,KAAK,EAAES,UAAU,CAAC;;IAG1E,OAAOC,QAAQ;EACjB;EAEA;;;;EAIAC,mBAAmBA,CAAA;IACjB,MAAMZ,SAAS,GAAG,IAAI,CAACO,wBAAwB,EAAE;IACjD,OAAOP,SAAS,GAAGA,SAAS,CAACF,EAAE,GAAG,IAAI;EACxC;EAEA;;;;EAIAe,oBAAoBA,CAACC,OAAgC;IACnD,MAAMC,UAAU,GAAG,IAAI,CAACR,wBAAwB,EAAE;IAClD,IAAIQ,UAAU,EAAE;MACd,MAAMC,UAAU,GAAG;QAAE,GAAGD,UAAU;QAAE,GAAGD;MAAO,CAAE;MAChDrB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEsB,UAAU,CAAC;MACvD,IAAI,CAAC1B,qBAAqB,CAACe,IAAI,CAACW,UAAU,CAAC;;EAE/C;EAEA;;;;;;EAMAC,qBAAqBA,CAACP,UAAkB,EAAET,KAAc,EAAEJ,OAAoD;IAC5G,MAAMG,SAAS,GAAmB;MAChCF,EAAE,EAAEY,UAAU;MACdT,KAAK,EAAEA,KAAK,IAAI,YAAYS,UAAU,EAAE;MACxCP,aAAa,EAAEN,OAAO,EAAEM,aAAa;MACrCC,KAAK,EAAEP,OAAO,EAAEO;KACjB;IAEDX,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEM,SAAS,CAAC;IAC3D,IAAI,CAACV,qBAAqB,CAACe,IAAI,CAACL,SAAS,CAAC;EAC5C;;;;;AAtGWZ,qBAAqB,GAAA8B,UAAA,EAHjChC,UAAU,CAAC;EACViC,UAAU,EAAE;CACb,CAAC,C,EACW/B,qBAAqB,CAuGjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}