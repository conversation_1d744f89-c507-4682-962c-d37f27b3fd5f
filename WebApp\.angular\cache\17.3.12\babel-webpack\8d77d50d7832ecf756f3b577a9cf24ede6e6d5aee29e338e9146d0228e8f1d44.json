{"ast": null, "code": "import Block from '../blots/block.js';\nclass Header extends Block {\n  static blotName = 'header';\n  static tagName = ['H1', 'H2', 'H3', 'H4', 'H5', 'H6'];\n  static formats(domNode) {\n    return this.tagName.indexOf(domNode.tagName) + 1;\n  }\n}\nexport default Header;", "map": {"version": 3, "names": ["Block", "Header", "blotName", "tagName", "formats", "domNode", "indexOf"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/quill/formats/header.js"], "sourcesContent": ["import Block from '../blots/block.js';\nclass Header extends Block {\n  static blotName = 'header';\n  static tagName = ['H1', 'H2', 'H3', 'H4', 'H5', 'H6'];\n  static formats(domNode) {\n    return this.tagName.indexOf(domNode.tagName) + 1;\n  }\n}\nexport default Header;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,mBAAmB;AACrC,MAAMC,MAAM,SAASD,KAAK,CAAC;EACzB,OAAOE,QAAQ,GAAG,QAAQ;EAC1B,OAAOC,OAAO,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACrD,OAAOC,OAAOA,CAACC,OAAO,EAAE;IACtB,OAAO,IAAI,CAACF,OAAO,CAACG,OAAO,CAACD,OAAO,CAACF,OAAO,CAAC,GAAG,CAAC;EAClD;AACF;AACA,eAAeF,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}