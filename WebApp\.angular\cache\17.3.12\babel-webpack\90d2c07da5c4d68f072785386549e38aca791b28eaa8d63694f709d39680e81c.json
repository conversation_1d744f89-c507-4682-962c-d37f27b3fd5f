{"ast": null, "code": "import { __decorate } from 'tslib';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Injectable, SkipSelf, Optional, Inject, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, ContentChildren, EventEmitter, Output, ElementRef, Host, ViewChild, forwardRef, inject, Directive, NgModule } from '@angular/core';\nimport { Subject, BehaviorSubject, merge, combineLatest } from 'rxjs';\nimport { map, mergeMap, filter, mapTo, auditTime, distinctUntilChanged, takeUntil, startWith, switchMap } from 'rxjs/operators';\nimport { InputBoolean } from 'ng-zorro-antd/core/util';\nimport * as i4 from '@angular/router';\nimport { NavigationEnd, RouterLink } from '@angular/router';\nimport * as i1 from '@angular/cdk/bidi';\nimport * as i6 from '@angular/cdk/overlay';\nimport { CdkOverlayOrigin, OverlayModule } from '@angular/cdk/overlay';\nimport * as i5 from 'ng-zorro-antd/core/no-animation';\nimport { NzNoAnimationDirective } from 'ng-zorro-antd/core/no-animation';\nimport { POSITION_MAP, getPlacementName } from 'ng-zorro-antd/core/overlay';\nimport { NgTemplateOutlet, NgClass } from '@angular/common';\nimport { collapseMotion, zoomBigMotion, slideMotion } from 'ng-zorro-antd/core/animation';\nimport * as i3 from 'ng-zorro-antd/core/outlet';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport * as i2 from 'ng-zorro-antd/icon';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport * as i3$1 from '@angular/cdk/platform';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst _c0 = [\"nz-menu-item\", \"\"];\nconst _c1 = [\"*\"];\nconst _c2 = [\"nz-submenu-inline-child\", \"\"];\nfunction NzSubmenuInlineChildComponent_ng_template_0_Template(rf, ctx) {}\nconst _c3 = [\"nz-submenu-none-inline-child\", \"\"];\nfunction NzSubmenuNoneInlineChildComponent_ng_template_1_Template(rf, ctx) {}\nconst _c4 = [\"nz-submenu-title\", \"\"];\nfunction NzSubMenuTitleComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 0);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"nzType\", ctx_r0.nzIcon);\n  }\n}\nfunction NzSubMenuTitleComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 3);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.nzTitle);\n  }\n}\nfunction NzSubMenuTitleComponent_Conditional_3_Case_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 4);\n  }\n}\nfunction NzSubMenuTitleComponent_Conditional_3_Case_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 5);\n  }\n}\nfunction NzSubMenuTitleComponent_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 2);\n    i0.ɵɵtemplate(1, NzSubMenuTitleComponent_Conditional_3_Case_1_Template, 1, 0)(2, NzSubMenuTitleComponent_Conditional_3_Case_2_Template, 1, 0);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(1, (tmp_1_0 = ctx_r0.dir) === \"rtl\" ? 1 : 2);\n  }\n}\nfunction NzSubMenuTitleComponent_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 6);\n  }\n}\nconst _c5 = [\"nz-submenu\", \"\"];\nconst _c6 = [[[\"\", \"title\", \"\"]], \"*\"];\nconst _c7 = [\"[title]\", \"*\"];\nfunction NzSubMenuComponent_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nfunction NzSubMenuComponent_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 3);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    const subMenuTemplate_r3 = i0.ɵɵreference(6);\n    i0.ɵɵproperty(\"mode\", ctx_r1.mode)(\"nzOpen\", ctx_r1.nzOpen)(\"@.disabled\", !!(ctx_r1.noAnimation == null ? null : ctx_r1.noAnimation.nzNoAnimation))(\"nzNoAnimation\", ctx_r1.noAnimation == null ? null : ctx_r1.noAnimation.nzNoAnimation)(\"menuClass\", ctx_r1.nzMenuClassName)(\"templateOutlet\", subMenuTemplate_r3);\n  }\n}\nfunction NzSubMenuComponent_Conditional_4_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵlistener(\"subMenuMouseState\", function NzSubMenuComponent_Conditional_4_ng_template_0_Template_div_subMenuMouseState_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.setMouseEnterState($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    const subMenuTemplate_r3 = i0.ɵɵreference(6);\n    i0.ɵɵproperty(\"theme\", ctx_r1.theme)(\"mode\", ctx_r1.mode)(\"nzOpen\", ctx_r1.nzOpen)(\"position\", ctx_r1.position)(\"nzDisabled\", ctx_r1.nzDisabled)(\"isMenuInsideDropDown\", ctx_r1.isMenuInsideDropDown)(\"templateOutlet\", subMenuTemplate_r3)(\"menuClass\", ctx_r1.nzMenuClassName)(\"@.disabled\", !!(ctx_r1.noAnimation == null ? null : ctx_r1.noAnimation.nzNoAnimation))(\"nzNoAnimation\", ctx_r1.noAnimation == null ? null : ctx_r1.noAnimation.nzNoAnimation);\n  }\n}\nfunction NzSubMenuComponent_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵtemplate(0, NzSubMenuComponent_Conditional_4_ng_template_0_Template, 1, 10, \"ng-template\", 4);\n    i0.ɵɵlistener(\"positionChange\", function NzSubMenuComponent_Conditional_4_Template_ng_template_positionChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPositionChange($event));\n    });\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    const origin_r6 = i0.ɵɵreference(1);\n    i0.ɵɵproperty(\"cdkConnectedOverlayPositions\", ctx_r1.overlayPositions)(\"cdkConnectedOverlayOrigin\", origin_r6)(\"cdkConnectedOverlayWidth\", ctx_r1.triggerWidth)(\"cdkConnectedOverlayOpen\", ctx_r1.nzOpen)(\"cdkConnectedOverlayTransformOriginOn\", \".ant-menu-submenu\");\n  }\n}\nfunction NzSubMenuComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 1);\n  }\n}\nconst _c8 = [\"titleElement\"];\nconst _c9 = [\"nz-menu-group\", \"\"];\nconst _c10 = [\"*\", [[\"\", \"title\", \"\"]]];\nconst _c11 = [\"*\", \"[title]\"];\nfunction NzMenuGroupComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.nzTitle);\n  }\n}\nfunction NzMenuGroupComponent_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 1);\n  }\n}\nconst NzIsMenuInsideDropDownToken = new InjectionToken('NzIsInDropDownMenuToken');\nconst NzMenuServiceLocalToken = new InjectionToken('NzMenuServiceLocalToken');\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass MenuService {\n  constructor() {\n    /** all descendant menu click **/\n    this.descendantMenuItemClick$ = new Subject();\n    /** child menu item click **/\n    this.childMenuItemClick$ = new Subject();\n    this.theme$ = new BehaviorSubject('light');\n    this.mode$ = new BehaviorSubject('vertical');\n    this.inlineIndent$ = new BehaviorSubject(24);\n    this.isChildSubMenuOpen$ = new BehaviorSubject(false);\n  }\n  onDescendantMenuItemClick(menu) {\n    this.descendantMenuItemClick$.next(menu);\n  }\n  onChildMenuItemClick(menu) {\n    this.childMenuItemClick$.next(menu);\n  }\n  setMode(mode) {\n    this.mode$.next(mode);\n  }\n  setTheme(theme) {\n    this.theme$.next(theme);\n  }\n  setInlineIndent(indent) {\n    this.inlineIndent$.next(indent);\n  }\n  static {\n    this.ɵfac = function MenuService_Factory(t) {\n      return new (t || MenuService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: MenuService,\n      factory: MenuService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MenuService, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzSubmenuService {\n  /**\n   * menu item inside submenu clicked\n   *\n   * @param menu\n   */\n  onChildMenuItemClick(menu) {\n    this.childMenuItemClick$.next(menu);\n  }\n  setOpenStateWithoutDebounce(value) {\n    this.isCurrentSubMenuOpen$.next(value);\n  }\n  setMouseEnterTitleOrOverlayState(value) {\n    this.isMouseEnterTitleOrOverlay$.next(value);\n  }\n  constructor(nzHostSubmenuService, nzMenuService, isMenuInsideDropDown) {\n    this.nzHostSubmenuService = nzHostSubmenuService;\n    this.nzMenuService = nzMenuService;\n    this.isMenuInsideDropDown = isMenuInsideDropDown;\n    this.mode$ = this.nzMenuService.mode$.pipe(map(mode => {\n      if (mode === 'inline') {\n        return 'inline';\n        /** if inside another submenu, set the mode to vertical **/\n      } else if (mode === 'vertical' || this.nzHostSubmenuService) {\n        return 'vertical';\n      } else {\n        return 'horizontal';\n      }\n    }));\n    this.level = 1;\n    this.isCurrentSubMenuOpen$ = new BehaviorSubject(false);\n    this.isChildSubMenuOpen$ = new BehaviorSubject(false);\n    /** submenu title & overlay mouse enter status **/\n    this.isMouseEnterTitleOrOverlay$ = new Subject();\n    this.childMenuItemClick$ = new Subject();\n    this.destroy$ = new Subject();\n    if (this.nzHostSubmenuService) {\n      this.level = this.nzHostSubmenuService.level + 1;\n    }\n    /** close if menu item clicked **/\n    const isClosedByMenuItemClick = this.childMenuItemClick$.pipe(mergeMap(() => this.mode$), filter(mode => mode !== 'inline' || this.isMenuInsideDropDown), mapTo(false));\n    const isCurrentSubmenuOpen$ = merge(this.isMouseEnterTitleOrOverlay$, isClosedByMenuItemClick);\n    /** combine the child submenu status with current submenu status to calculate host submenu open **/\n    const isSubMenuOpenWithDebounce$ = combineLatest([this.isChildSubMenuOpen$, isCurrentSubmenuOpen$]).pipe(map(([isChildSubMenuOpen, isCurrentSubmenuOpen]) => isChildSubMenuOpen || isCurrentSubmenuOpen), auditTime(150), distinctUntilChanged(), takeUntil(this.destroy$));\n    isSubMenuOpenWithDebounce$.pipe(distinctUntilChanged()).subscribe(data => {\n      this.setOpenStateWithoutDebounce(data);\n      if (this.nzHostSubmenuService) {\n        /** set parent submenu's child submenu open status **/\n        this.nzHostSubmenuService.isChildSubMenuOpen$.next(data);\n      } else {\n        this.nzMenuService.isChildSubMenuOpen$.next(data);\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzSubmenuService_Factory(t) {\n      return new (t || NzSubmenuService)(i0.ɵɵinject(NzSubmenuService, 12), i0.ɵɵinject(MenuService), i0.ɵɵinject(NzIsMenuInsideDropDownToken));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NzSubmenuService,\n      factory: NzSubmenuService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSubmenuService, [{\n    type: Injectable\n  }], () => [{\n    type: NzSubmenuService,\n    decorators: [{\n      type: SkipSelf\n    }, {\n      type: Optional\n    }]\n  }, {\n    type: MenuService\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [NzIsMenuInsideDropDownToken]\n    }]\n  }], null);\n})();\nclass NzMenuItemComponent {\n  /** clear all item selected status except this */\n  clickMenuItem(e) {\n    if (this.nzDisabled) {\n      e.preventDefault();\n      e.stopPropagation();\n    } else {\n      this.nzMenuService.onDescendantMenuItemClick(this);\n      if (this.nzSubmenuService) {\n        /** menu item inside the submenu **/\n        this.nzSubmenuService.onChildMenuItemClick(this);\n      } else {\n        /** menu item inside the root menu **/\n        this.nzMenuService.onChildMenuItemClick(this);\n      }\n    }\n  }\n  setSelectedState(value) {\n    this.nzSelected = value;\n    this.selected$.next(value);\n  }\n  updateRouterActive() {\n    if (!this.listOfRouterLink || !this.router || !this.router.navigated || !this.nzMatchRouter) {\n      return;\n    }\n    Promise.resolve().then(() => {\n      const hasActiveLinks = this.hasActiveLinks();\n      if (this.nzSelected !== hasActiveLinks) {\n        this.nzSelected = hasActiveLinks;\n        this.setSelectedState(this.nzSelected);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  hasActiveLinks() {\n    const isActiveCheckFn = this.isLinkActive(this.router);\n    return this.routerLink && isActiveCheckFn(this.routerLink) || this.listOfRouterLink.some(isActiveCheckFn);\n  }\n  isLinkActive(router) {\n    return link => router.isActive(link.urlTree || '', {\n      paths: this.nzMatchRouterExact ? 'exact' : 'subset',\n      queryParams: this.nzMatchRouterExact ? 'exact' : 'subset',\n      fragment: 'ignored',\n      matrixParams: 'ignored'\n    });\n  }\n  constructor(nzMenuService, cdr, nzSubmenuService, isMenuInsideDropDown, directionality, routerLink, router) {\n    this.nzMenuService = nzMenuService;\n    this.cdr = cdr;\n    this.nzSubmenuService = nzSubmenuService;\n    this.isMenuInsideDropDown = isMenuInsideDropDown;\n    this.directionality = directionality;\n    this.routerLink = routerLink;\n    this.router = router;\n    this.destroy$ = new Subject();\n    this.level = this.nzSubmenuService ? this.nzSubmenuService.level + 1 : 1;\n    this.selected$ = new Subject();\n    this.inlinePaddingLeft = null;\n    this.dir = 'ltr';\n    this.nzDisabled = false;\n    this.nzSelected = false;\n    this.nzDanger = false;\n    this.nzMatchRouterExact = false;\n    this.nzMatchRouter = false;\n    if (router) {\n      this.router.events.pipe(takeUntil(this.destroy$), filter(e => e instanceof NavigationEnd)).subscribe(() => {\n        this.updateRouterActive();\n      });\n    }\n  }\n  ngOnInit() {\n    /** store origin padding in padding */\n    combineLatest([this.nzMenuService.mode$, this.nzMenuService.inlineIndent$]).pipe(takeUntil(this.destroy$)).subscribe(([mode, inlineIndent]) => {\n      this.inlinePaddingLeft = mode === 'inline' ? this.level * inlineIndent : null;\n    });\n    this.dir = this.directionality.value;\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n    });\n  }\n  ngAfterContentInit() {\n    this.listOfRouterLink.changes.pipe(takeUntil(this.destroy$)).subscribe(() => this.updateRouterActive());\n    this.updateRouterActive();\n  }\n  ngOnChanges(changes) {\n    if (changes.nzSelected) {\n      this.setSelectedState(this.nzSelected);\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next(true);\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzMenuItemComponent_Factory(t) {\n      return new (t || NzMenuItemComponent)(i0.ɵɵdirectiveInject(MenuService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(NzSubmenuService, 8), i0.ɵɵdirectiveInject(NzIsMenuInsideDropDownToken), i0.ɵɵdirectiveInject(i1.Directionality, 8), i0.ɵɵdirectiveInject(i4.RouterLink, 8), i0.ɵɵdirectiveInject(i4.Router, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzMenuItemComponent,\n      selectors: [[\"\", \"nz-menu-item\", \"\"]],\n      contentQueries: function NzMenuItemComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, RouterLink, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfRouterLink = _t);\n        }\n      },\n      hostVars: 20,\n      hostBindings: function NzMenuItemComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function NzMenuItemComponent_click_HostBindingHandler($event) {\n            return ctx.clickMenuItem($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"padding-left\", ctx.dir === \"rtl\" ? null : ctx.nzPaddingLeft || ctx.inlinePaddingLeft, \"px\")(\"padding-right\", ctx.dir === \"rtl\" ? ctx.nzPaddingLeft || ctx.inlinePaddingLeft : null, \"px\");\n          i0.ɵɵclassProp(\"ant-dropdown-menu-item\", ctx.isMenuInsideDropDown)(\"ant-dropdown-menu-item-selected\", ctx.isMenuInsideDropDown && ctx.nzSelected)(\"ant-dropdown-menu-item-danger\", ctx.isMenuInsideDropDown && ctx.nzDanger)(\"ant-dropdown-menu-item-disabled\", ctx.isMenuInsideDropDown && ctx.nzDisabled)(\"ant-menu-item\", !ctx.isMenuInsideDropDown)(\"ant-menu-item-selected\", !ctx.isMenuInsideDropDown && ctx.nzSelected)(\"ant-menu-item-danger\", !ctx.isMenuInsideDropDown && ctx.nzDanger)(\"ant-menu-item-disabled\", !ctx.isMenuInsideDropDown && ctx.nzDisabled);\n        }\n      },\n      inputs: {\n        nzPaddingLeft: \"nzPaddingLeft\",\n        nzDisabled: \"nzDisabled\",\n        nzSelected: \"nzSelected\",\n        nzDanger: \"nzDanger\",\n        nzMatchRouterExact: \"nzMatchRouterExact\",\n        nzMatchRouter: \"nzMatchRouter\"\n      },\n      exportAs: [\"nzMenuItem\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      attrs: _c0,\n      ngContentSelectors: _c1,\n      decls: 2,\n      vars: 0,\n      consts: [[1, \"ant-menu-title-content\"]],\n      template: function NzMenuItemComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"span\", 0);\n          i0.ɵɵprojection(1);\n          i0.ɵɵelementEnd();\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([InputBoolean()], NzMenuItemComponent.prototype, \"nzDisabled\", void 0);\n__decorate([InputBoolean()], NzMenuItemComponent.prototype, \"nzSelected\", void 0);\n__decorate([InputBoolean()], NzMenuItemComponent.prototype, \"nzDanger\", void 0);\n__decorate([InputBoolean()], NzMenuItemComponent.prototype, \"nzMatchRouterExact\", void 0);\n__decorate([InputBoolean()], NzMenuItemComponent.prototype, \"nzMatchRouter\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzMenuItemComponent, [{\n    type: Component,\n    args: [{\n      selector: '[nz-menu-item]',\n      exportAs: 'nzMenuItem',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      preserveWhitespaces: false,\n      template: `\n    <span class=\"ant-menu-title-content\">\n      <ng-content></ng-content>\n    </span>\n  `,\n      host: {\n        '[class.ant-dropdown-menu-item]': `isMenuInsideDropDown`,\n        '[class.ant-dropdown-menu-item-selected]': `isMenuInsideDropDown && nzSelected`,\n        '[class.ant-dropdown-menu-item-danger]': `isMenuInsideDropDown && nzDanger`,\n        '[class.ant-dropdown-menu-item-disabled]': `isMenuInsideDropDown && nzDisabled`,\n        '[class.ant-menu-item]': `!isMenuInsideDropDown`,\n        '[class.ant-menu-item-selected]': `!isMenuInsideDropDown && nzSelected`,\n        '[class.ant-menu-item-danger]': `!isMenuInsideDropDown && nzDanger`,\n        '[class.ant-menu-item-disabled]': `!isMenuInsideDropDown && nzDisabled`,\n        '[style.paddingLeft.px]': `dir === 'rtl' ? null : nzPaddingLeft || inlinePaddingLeft`,\n        '[style.paddingRight.px]': `dir === 'rtl' ? nzPaddingLeft || inlinePaddingLeft : null`,\n        '(click)': 'clickMenuItem($event)'\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: MenuService\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: NzSubmenuService,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [NzIsMenuInsideDropDownToken]\n    }]\n  }, {\n    type: i1.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i4.RouterLink,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i4.Router,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    nzPaddingLeft: [{\n      type: Input\n    }],\n    nzDisabled: [{\n      type: Input\n    }],\n    nzSelected: [{\n      type: Input\n    }],\n    nzDanger: [{\n      type: Input\n    }],\n    nzMatchRouterExact: [{\n      type: Input\n    }],\n    nzMatchRouter: [{\n      type: Input\n    }],\n    listOfRouterLink: [{\n      type: ContentChildren,\n      args: [RouterLink, {\n        descendants: true\n      }]\n    }]\n  });\n})();\nclass NzSubmenuInlineChildComponent {\n  constructor(elementRef, renderer, directionality) {\n    this.elementRef = elementRef;\n    this.renderer = renderer;\n    this.directionality = directionality;\n    this.templateOutlet = null;\n    this.menuClass = '';\n    this.mode = 'vertical';\n    this.nzOpen = false;\n    this.listOfCacheClassName = [];\n    this.expandState = 'collapsed';\n    this.dir = 'ltr';\n    this.destroy$ = new Subject();\n  }\n  calcMotionState() {\n    if (this.nzOpen) {\n      this.expandState = 'expanded';\n    } else {\n      this.expandState = 'collapsed';\n    }\n  }\n  ngOnInit() {\n    this.calcMotionState();\n    this.dir = this.directionality.value;\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n    });\n  }\n  ngOnChanges(changes) {\n    const {\n      mode,\n      nzOpen,\n      menuClass\n    } = changes;\n    if (mode || nzOpen) {\n      this.calcMotionState();\n    }\n    if (menuClass) {\n      if (this.listOfCacheClassName.length) {\n        this.listOfCacheClassName.filter(item => !!item).forEach(className => {\n          this.renderer.removeClass(this.elementRef.nativeElement, className);\n        });\n      }\n      if (this.menuClass) {\n        this.listOfCacheClassName = this.menuClass.split(' ');\n        this.listOfCacheClassName.filter(item => !!item).forEach(className => {\n          this.renderer.addClass(this.elementRef.nativeElement, className);\n        });\n      }\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzSubmenuInlineChildComponent_Factory(t) {\n      return new (t || NzSubmenuInlineChildComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i1.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzSubmenuInlineChildComponent,\n      selectors: [[\"\", \"nz-submenu-inline-child\", \"\"]],\n      hostAttrs: [1, \"ant-menu\", \"ant-menu-inline\", \"ant-menu-sub\"],\n      hostVars: 3,\n      hostBindings: function NzSubmenuInlineChildComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵsyntheticHostProperty(\"@collapseMotion\", ctx.expandState);\n          i0.ɵɵclassProp(\"ant-menu-rtl\", ctx.dir === \"rtl\");\n        }\n      },\n      inputs: {\n        templateOutlet: \"templateOutlet\",\n        menuClass: \"menuClass\",\n        mode: \"mode\",\n        nzOpen: \"nzOpen\"\n      },\n      exportAs: [\"nzSubmenuInlineChild\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      attrs: _c2,\n      decls: 1,\n      vars: 1,\n      consts: [[3, \"ngTemplateOutlet\"]],\n      template: function NzSubmenuInlineChildComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, NzSubmenuInlineChildComponent_ng_template_0_Template, 0, 0, \"ng-template\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.templateOutlet);\n        }\n      },\n      dependencies: [NgTemplateOutlet],\n      encapsulation: 2,\n      data: {\n        animation: [collapseMotion]\n      },\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSubmenuInlineChildComponent, [{\n    type: Component,\n    args: [{\n      selector: '[nz-submenu-inline-child]',\n      animations: [collapseMotion],\n      exportAs: 'nzSubmenuInlineChild',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: ` <ng-template [ngTemplateOutlet]=\"templateOutlet\"></ng-template> `,\n      host: {\n        class: 'ant-menu ant-menu-inline ant-menu-sub',\n        '[class.ant-menu-rtl]': `dir === 'rtl'`,\n        '[@collapseMotion]': 'expandState'\n      },\n      imports: [NgTemplateOutlet],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i1.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    templateOutlet: [{\n      type: Input\n    }],\n    menuClass: [{\n      type: Input\n    }],\n    mode: [{\n      type: Input\n    }],\n    nzOpen: [{\n      type: Input\n    }]\n  });\n})();\nclass NzSubmenuNoneInlineChildComponent {\n  constructor(directionality) {\n    this.directionality = directionality;\n    this.menuClass = '';\n    this.theme = 'light';\n    this.templateOutlet = null;\n    this.isMenuInsideDropDown = false;\n    this.mode = 'vertical';\n    this.position = 'right';\n    this.nzDisabled = false;\n    this.nzOpen = false;\n    this.subMenuMouseState = new EventEmitter();\n    this.expandState = 'collapsed';\n    this.dir = 'ltr';\n    this.destroy$ = new Subject();\n  }\n  setMouseState(state) {\n    if (!this.nzDisabled) {\n      this.subMenuMouseState.next(state);\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  calcMotionState() {\n    if (this.nzOpen) {\n      if (this.mode === 'horizontal') {\n        this.expandState = 'bottom';\n      } else if (this.mode === 'vertical') {\n        this.expandState = 'active';\n      }\n    } else {\n      this.expandState = 'collapsed';\n    }\n  }\n  ngOnInit() {\n    this.calcMotionState();\n    this.dir = this.directionality.value;\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n    });\n  }\n  ngOnChanges(changes) {\n    const {\n      mode,\n      nzOpen\n    } = changes;\n    if (mode || nzOpen) {\n      this.calcMotionState();\n    }\n  }\n  static {\n    this.ɵfac = function NzSubmenuNoneInlineChildComponent_Factory(t) {\n      return new (t || NzSubmenuNoneInlineChildComponent)(i0.ɵɵdirectiveInject(i1.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzSubmenuNoneInlineChildComponent,\n      selectors: [[\"\", \"nz-submenu-none-inline-child\", \"\"]],\n      hostAttrs: [1, \"ant-menu-submenu\", \"ant-menu-submenu-popup\"],\n      hostVars: 14,\n      hostBindings: function NzSubmenuNoneInlineChildComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"mouseenter\", function NzSubmenuNoneInlineChildComponent_mouseenter_HostBindingHandler() {\n            return ctx.setMouseState(true);\n          })(\"mouseleave\", function NzSubmenuNoneInlineChildComponent_mouseleave_HostBindingHandler() {\n            return ctx.setMouseState(false);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵsyntheticHostProperty(\"@slideMotion\", ctx.expandState)(\"@zoomBigMotion\", ctx.expandState);\n          i0.ɵɵclassProp(\"ant-menu-light\", ctx.theme === \"light\")(\"ant-menu-dark\", ctx.theme === \"dark\")(\"ant-menu-submenu-placement-bottom\", ctx.mode === \"horizontal\")(\"ant-menu-submenu-placement-right\", ctx.mode === \"vertical\" && ctx.position === \"right\")(\"ant-menu-submenu-placement-left\", ctx.mode === \"vertical\" && ctx.position === \"left\")(\"ant-menu-submenu-rtl\", ctx.dir === \"rtl\");\n        }\n      },\n      inputs: {\n        menuClass: \"menuClass\",\n        theme: \"theme\",\n        templateOutlet: \"templateOutlet\",\n        isMenuInsideDropDown: \"isMenuInsideDropDown\",\n        mode: \"mode\",\n        position: \"position\",\n        nzDisabled: \"nzDisabled\",\n        nzOpen: \"nzOpen\"\n      },\n      outputs: {\n        subMenuMouseState: \"subMenuMouseState\"\n      },\n      exportAs: [\"nzSubmenuNoneInlineChild\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      attrs: _c3,\n      decls: 2,\n      vars: 16,\n      consts: [[3, \"ngClass\"], [3, \"ngTemplateOutlet\"]],\n      template: function NzSubmenuNoneInlineChildComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, NzSubmenuNoneInlineChildComponent_ng_template_1_Template, 0, 0, \"ng-template\", 1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-dropdown-menu\", ctx.isMenuInsideDropDown)(\"ant-menu\", !ctx.isMenuInsideDropDown)(\"ant-dropdown-menu-vertical\", ctx.isMenuInsideDropDown)(\"ant-menu-vertical\", !ctx.isMenuInsideDropDown)(\"ant-dropdown-menu-sub\", ctx.isMenuInsideDropDown)(\"ant-menu-sub\", !ctx.isMenuInsideDropDown)(\"ant-menu-rtl\", ctx.dir === \"rtl\");\n          i0.ɵɵproperty(\"ngClass\", ctx.menuClass);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.templateOutlet);\n        }\n      },\n      dependencies: [NgClass, NgTemplateOutlet],\n      encapsulation: 2,\n      data: {\n        animation: [zoomBigMotion, slideMotion]\n      },\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSubmenuNoneInlineChildComponent, [{\n    type: Component,\n    args: [{\n      selector: '[nz-submenu-none-inline-child]',\n      exportAs: 'nzSubmenuNoneInlineChild',\n      encapsulation: ViewEncapsulation.None,\n      animations: [zoomBigMotion, slideMotion],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    <div\n      [class.ant-dropdown-menu]=\"isMenuInsideDropDown\"\n      [class.ant-menu]=\"!isMenuInsideDropDown\"\n      [class.ant-dropdown-menu-vertical]=\"isMenuInsideDropDown\"\n      [class.ant-menu-vertical]=\"!isMenuInsideDropDown\"\n      [class.ant-dropdown-menu-sub]=\"isMenuInsideDropDown\"\n      [class.ant-menu-sub]=\"!isMenuInsideDropDown\"\n      [class.ant-menu-rtl]=\"dir === 'rtl'\"\n      [ngClass]=\"menuClass\"\n    >\n      <ng-template [ngTemplateOutlet]=\"templateOutlet\"></ng-template>\n    </div>\n  `,\n      host: {\n        class: 'ant-menu-submenu ant-menu-submenu-popup',\n        '[class.ant-menu-light]': \"theme === 'light'\",\n        '[class.ant-menu-dark]': \"theme === 'dark'\",\n        '[class.ant-menu-submenu-placement-bottom]': \"mode === 'horizontal'\",\n        '[class.ant-menu-submenu-placement-right]': \"mode === 'vertical' && position === 'right'\",\n        '[class.ant-menu-submenu-placement-left]': \"mode === 'vertical' && position === 'left'\",\n        '[class.ant-menu-submenu-rtl]': 'dir ===\"rtl\"',\n        '[@slideMotion]': 'expandState',\n        '[@zoomBigMotion]': 'expandState',\n        '(mouseenter)': 'setMouseState(true)',\n        '(mouseleave)': 'setMouseState(false)'\n      },\n      imports: [NgClass, NgTemplateOutlet],\n      standalone: true\n    }]\n  }], () => [{\n    type: i1.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    menuClass: [{\n      type: Input\n    }],\n    theme: [{\n      type: Input\n    }],\n    templateOutlet: [{\n      type: Input\n    }],\n    isMenuInsideDropDown: [{\n      type: Input\n    }],\n    mode: [{\n      type: Input\n    }],\n    position: [{\n      type: Input\n    }],\n    nzDisabled: [{\n      type: Input\n    }],\n    nzOpen: [{\n      type: Input\n    }],\n    subMenuMouseState: [{\n      type: Output\n    }]\n  });\n})();\nclass NzSubMenuTitleComponent {\n  constructor(cdr, directionality) {\n    this.cdr = cdr;\n    this.directionality = directionality;\n    this.nzIcon = null;\n    this.nzTitle = null;\n    this.isMenuInsideDropDown = false;\n    this.nzDisabled = false;\n    this.paddingLeft = null;\n    this.mode = 'vertical';\n    this.toggleSubMenu = new EventEmitter();\n    this.subMenuMouseState = new EventEmitter();\n    this.dir = 'ltr';\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {\n    this.dir = this.directionality.value;\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  setMouseState(state) {\n    if (!this.nzDisabled) {\n      this.subMenuMouseState.next(state);\n    }\n  }\n  clickTitle() {\n    if (this.mode === 'inline' && !this.nzDisabled) {\n      this.toggleSubMenu.emit();\n    }\n  }\n  static {\n    this.ɵfac = function NzSubMenuTitleComponent_Factory(t) {\n      return new (t || NzSubMenuTitleComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzSubMenuTitleComponent,\n      selectors: [[\"\", \"nz-submenu-title\", \"\"]],\n      hostVars: 8,\n      hostBindings: function NzSubMenuTitleComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function NzSubMenuTitleComponent_click_HostBindingHandler() {\n            return ctx.clickTitle();\n          })(\"mouseenter\", function NzSubMenuTitleComponent_mouseenter_HostBindingHandler() {\n            return ctx.setMouseState(true);\n          })(\"mouseleave\", function NzSubMenuTitleComponent_mouseleave_HostBindingHandler() {\n            return ctx.setMouseState(false);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"padding-left\", ctx.dir === \"rtl\" ? null : ctx.paddingLeft, \"px\")(\"padding-right\", ctx.dir === \"rtl\" ? ctx.paddingLeft : null, \"px\");\n          i0.ɵɵclassProp(\"ant-dropdown-menu-submenu-title\", ctx.isMenuInsideDropDown)(\"ant-menu-submenu-title\", !ctx.isMenuInsideDropDown);\n        }\n      },\n      inputs: {\n        nzIcon: \"nzIcon\",\n        nzTitle: \"nzTitle\",\n        isMenuInsideDropDown: \"isMenuInsideDropDown\",\n        nzDisabled: \"nzDisabled\",\n        paddingLeft: \"paddingLeft\",\n        mode: \"mode\"\n      },\n      outputs: {\n        toggleSubMenu: \"toggleSubMenu\",\n        subMenuMouseState: \"subMenuMouseState\"\n      },\n      exportAs: [\"nzSubmenuTitle\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      attrs: _c4,\n      ngContentSelectors: _c1,\n      decls: 5,\n      vars: 3,\n      consts: [[\"nz-icon\", \"\", 3, \"nzType\"], [4, \"nzStringTemplateOutlet\"], [1, \"ant-dropdown-menu-submenu-expand-icon\"], [1, \"ant-menu-title-content\"], [\"nz-icon\", \"\", \"nzType\", \"left\", 1, \"ant-dropdown-menu-submenu-arrow-icon\"], [\"nz-icon\", \"\", \"nzType\", \"right\", 1, \"ant-dropdown-menu-submenu-arrow-icon\"], [1, \"ant-menu-submenu-arrow\"]],\n      template: function NzSubMenuTitleComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, NzSubMenuTitleComponent_Conditional_0_Template, 1, 1, \"span\", 0)(1, NzSubMenuTitleComponent_ng_container_1_Template, 3, 1, \"ng-container\", 1);\n          i0.ɵɵprojection(2);\n          i0.ɵɵtemplate(3, NzSubMenuTitleComponent_Conditional_3_Template, 3, 1, \"span\", 2)(4, NzSubMenuTitleComponent_Conditional_4_Template, 1, 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(0, ctx.nzIcon ? 0 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx.nzTitle);\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(3, ctx.isMenuInsideDropDown ? 3 : 4);\n        }\n      },\n      dependencies: [NzIconModule, i2.NzIconDirective, NzOutletModule, i3.NzStringTemplateOutletDirective],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSubMenuTitleComponent, [{\n    type: Component,\n    args: [{\n      selector: '[nz-submenu-title]',\n      exportAs: 'nzSubmenuTitle',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    @if (nzIcon) {\n      <span nz-icon [nzType]=\"nzIcon\"></span>\n    }\n    <ng-container *nzStringTemplateOutlet=\"nzTitle\">\n      <span class=\"ant-menu-title-content\">{{ nzTitle }}</span>\n    </ng-container>\n    <ng-content />\n    @if (isMenuInsideDropDown) {\n      <span class=\"ant-dropdown-menu-submenu-expand-icon\">\n        @switch (dir) {\n          @case ('rtl') {\n            <span nz-icon nzType=\"left\" class=\"ant-dropdown-menu-submenu-arrow-icon\"></span>\n          }\n          @default {\n            <span nz-icon nzType=\"right\" class=\"ant-dropdown-menu-submenu-arrow-icon\"></span>\n          }\n        }\n      </span>\n    } @else {\n      <span class=\"ant-menu-submenu-arrow\"></span>\n    }\n  `,\n      host: {\n        '[class.ant-dropdown-menu-submenu-title]': 'isMenuInsideDropDown',\n        '[class.ant-menu-submenu-title]': '!isMenuInsideDropDown',\n        '[style.paddingLeft.px]': `dir === 'rtl' ? null : paddingLeft `,\n        '[style.paddingRight.px]': `dir === 'rtl' ? paddingLeft : null`,\n        '(click)': 'clickTitle()',\n        '(mouseenter)': 'setMouseState(true)',\n        '(mouseleave)': 'setMouseState(false)'\n      },\n      imports: [NzIconModule, NzOutletModule],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    nzIcon: [{\n      type: Input\n    }],\n    nzTitle: [{\n      type: Input\n    }],\n    isMenuInsideDropDown: [{\n      type: Input\n    }],\n    nzDisabled: [{\n      type: Input\n    }],\n    paddingLeft: [{\n      type: Input\n    }],\n    mode: [{\n      type: Input\n    }],\n    toggleSubMenu: [{\n      type: Output\n    }],\n    subMenuMouseState: [{\n      type: Output\n    }]\n  });\n})();\nconst listOfVerticalPositions = [POSITION_MAP.rightTop, POSITION_MAP.right, POSITION_MAP.rightBottom, POSITION_MAP.leftTop, POSITION_MAP.left, POSITION_MAP.leftBottom];\nconst listOfHorizontalPositions = [POSITION_MAP.bottomLeft, POSITION_MAP.bottomRight, POSITION_MAP.topRight, POSITION_MAP.topLeft];\nclass NzSubMenuComponent {\n  /** set the submenu host open status directly **/\n  setOpenStateWithoutDebounce(open) {\n    this.nzSubmenuService.setOpenStateWithoutDebounce(open);\n  }\n  toggleSubMenu() {\n    this.setOpenStateWithoutDebounce(!this.nzOpen);\n  }\n  setMouseEnterState(value) {\n    this.isActive = value;\n    if (this.mode !== 'inline') {\n      this.nzSubmenuService.setMouseEnterTitleOrOverlayState(value);\n    }\n  }\n  setTriggerWidth() {\n    if (this.mode === 'horizontal' && this.platform.isBrowser && this.cdkOverlayOrigin && this.nzPlacement === 'bottomLeft') {\n      /** TODO: fast dom **/\n      this.triggerWidth = this.cdkOverlayOrigin.nativeElement.getBoundingClientRect().width;\n    }\n  }\n  onPositionChange(position) {\n    const placement = getPlacementName(position);\n    if (placement === 'rightTop' || placement === 'rightBottom' || placement === 'right') {\n      this.position = 'right';\n    } else if (placement === 'leftTop' || placement === 'leftBottom' || placement === 'left') {\n      this.position = 'left';\n    }\n  }\n  constructor(nzMenuService, cdr, nzSubmenuService, platform, isMenuInsideDropDown, directionality, noAnimation) {\n    this.nzMenuService = nzMenuService;\n    this.cdr = cdr;\n    this.nzSubmenuService = nzSubmenuService;\n    this.platform = platform;\n    this.isMenuInsideDropDown = isMenuInsideDropDown;\n    this.directionality = directionality;\n    this.noAnimation = noAnimation;\n    this.nzMenuClassName = '';\n    this.nzPaddingLeft = null;\n    this.nzTitle = null;\n    this.nzIcon = null;\n    this.nzOpen = false;\n    this.nzDisabled = false;\n    this.nzPlacement = 'bottomLeft';\n    this.nzOpenChange = new EventEmitter();\n    this.cdkOverlayOrigin = null;\n    // fix errors about circular dependency\n    // Can't construct a query for the property ... since the query selector wasn't defined\"\n    this.listOfNzSubMenuComponent = null;\n    this.listOfNzMenuItemDirective = null;\n    this.level = this.nzSubmenuService.level;\n    this.destroy$ = new Subject();\n    this.position = 'right';\n    this.triggerWidth = null;\n    this.theme = 'light';\n    this.mode = 'vertical';\n    this.inlinePaddingLeft = null;\n    this.overlayPositions = listOfVerticalPositions;\n    this.isSelected = false;\n    this.isActive = false;\n    this.dir = 'ltr';\n  }\n  ngOnInit() {\n    /** submenu theme update **/\n    this.nzMenuService.theme$.pipe(takeUntil(this.destroy$)).subscribe(theme => {\n      this.theme = theme;\n      this.cdr.markForCheck();\n    });\n    /** submenu mode update **/\n    this.nzSubmenuService.mode$.pipe(takeUntil(this.destroy$)).subscribe(mode => {\n      this.mode = mode;\n      if (mode === 'horizontal') {\n        this.overlayPositions = [POSITION_MAP[this.nzPlacement], ...listOfHorizontalPositions];\n      } else if (mode === 'vertical') {\n        this.overlayPositions = listOfVerticalPositions;\n      }\n      this.cdr.markForCheck();\n    });\n    /** inlineIndent update **/\n    combineLatest([this.nzSubmenuService.mode$, this.nzMenuService.inlineIndent$]).pipe(takeUntil(this.destroy$)).subscribe(([mode, inlineIndent]) => {\n      this.inlinePaddingLeft = mode === 'inline' ? this.level * inlineIndent : null;\n      this.cdr.markForCheck();\n    });\n    /** current submenu open status **/\n    this.nzSubmenuService.isCurrentSubMenuOpen$.pipe(takeUntil(this.destroy$)).subscribe(open => {\n      this.isActive = open;\n      if (open !== this.nzOpen) {\n        this.setTriggerWidth();\n        this.nzOpen = open;\n        this.nzOpenChange.emit(this.nzOpen);\n        this.cdr.markForCheck();\n      }\n    });\n    this.dir = this.directionality.value;\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.cdr.markForCheck();\n    });\n  }\n  ngAfterContentInit() {\n    this.setTriggerWidth();\n    const listOfNzMenuItemDirective = this.listOfNzMenuItemDirective;\n    const changes = listOfNzMenuItemDirective.changes;\n    const mergedObservable = merge(...[changes, ...listOfNzMenuItemDirective.map(menu => menu.selected$)]);\n    changes.pipe(startWith(listOfNzMenuItemDirective), switchMap(() => mergedObservable), startWith(true), map(() => listOfNzMenuItemDirective.some(e => e.nzSelected)), takeUntil(this.destroy$)).subscribe(selected => {\n      this.isSelected = selected;\n      this.cdr.markForCheck();\n    });\n  }\n  ngOnChanges(changes) {\n    const {\n      nzOpen\n    } = changes;\n    if (nzOpen) {\n      this.nzSubmenuService.setOpenStateWithoutDebounce(this.nzOpen);\n      this.setTriggerWidth();\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzSubMenuComponent_Factory(t) {\n      return new (t || NzSubMenuComponent)(i0.ɵɵdirectiveInject(MenuService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(NzSubmenuService), i0.ɵɵdirectiveInject(i3$1.Platform), i0.ɵɵdirectiveInject(NzIsMenuInsideDropDownToken), i0.ɵɵdirectiveInject(i1.Directionality, 8), i0.ɵɵdirectiveInject(i5.NzNoAnimationDirective, 9));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzSubMenuComponent,\n      selectors: [[\"\", \"nz-submenu\", \"\"]],\n      contentQueries: function NzSubMenuComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, NzSubMenuComponent, 5);\n          i0.ɵɵcontentQuery(dirIndex, NzMenuItemComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfNzSubMenuComponent = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfNzMenuItemDirective = _t);\n        }\n      },\n      viewQuery: function NzSubMenuComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(CdkOverlayOrigin, 7, ElementRef);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.cdkOverlayOrigin = _t.first);\n        }\n      },\n      hostVars: 34,\n      hostBindings: function NzSubMenuComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-dropdown-menu-submenu\", ctx.isMenuInsideDropDown)(\"ant-dropdown-menu-submenu-disabled\", ctx.isMenuInsideDropDown && ctx.nzDisabled)(\"ant-dropdown-menu-submenu-open\", ctx.isMenuInsideDropDown && ctx.nzOpen)(\"ant-dropdown-menu-submenu-selected\", ctx.isMenuInsideDropDown && ctx.isSelected)(\"ant-dropdown-menu-submenu-vertical\", ctx.isMenuInsideDropDown && ctx.mode === \"vertical\")(\"ant-dropdown-menu-submenu-horizontal\", ctx.isMenuInsideDropDown && ctx.mode === \"horizontal\")(\"ant-dropdown-menu-submenu-inline\", ctx.isMenuInsideDropDown && ctx.mode === \"inline\")(\"ant-dropdown-menu-submenu-active\", ctx.isMenuInsideDropDown && ctx.isActive)(\"ant-menu-submenu\", !ctx.isMenuInsideDropDown)(\"ant-menu-submenu-disabled\", !ctx.isMenuInsideDropDown && ctx.nzDisabled)(\"ant-menu-submenu-open\", !ctx.isMenuInsideDropDown && ctx.nzOpen)(\"ant-menu-submenu-selected\", !ctx.isMenuInsideDropDown && ctx.isSelected)(\"ant-menu-submenu-vertical\", !ctx.isMenuInsideDropDown && ctx.mode === \"vertical\")(\"ant-menu-submenu-horizontal\", !ctx.isMenuInsideDropDown && ctx.mode === \"horizontal\")(\"ant-menu-submenu-inline\", !ctx.isMenuInsideDropDown && ctx.mode === \"inline\")(\"ant-menu-submenu-active\", !ctx.isMenuInsideDropDown && ctx.isActive)(\"ant-menu-submenu-rtl\", ctx.dir === \"rtl\");\n        }\n      },\n      inputs: {\n        nzMenuClassName: \"nzMenuClassName\",\n        nzPaddingLeft: \"nzPaddingLeft\",\n        nzTitle: \"nzTitle\",\n        nzIcon: \"nzIcon\",\n        nzOpen: \"nzOpen\",\n        nzDisabled: \"nzDisabled\",\n        nzPlacement: \"nzPlacement\"\n      },\n      outputs: {\n        nzOpenChange: \"nzOpenChange\"\n      },\n      exportAs: [\"nzSubmenu\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([NzSubmenuService]), i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      attrs: _c5,\n      ngContentSelectors: _c7,\n      decls: 7,\n      vars: 8,\n      consts: [[\"origin\", \"cdkOverlayOrigin\"], [\"subMenuTemplate\", \"\"], [\"nz-submenu-title\", \"\", \"cdkOverlayOrigin\", \"\", 3, \"subMenuMouseState\", \"toggleSubMenu\", \"nzIcon\", \"nzTitle\", \"mode\", \"nzDisabled\", \"isMenuInsideDropDown\", \"paddingLeft\"], [\"nz-submenu-inline-child\", \"\", 3, \"mode\", \"nzOpen\", \"nzNoAnimation\", \"menuClass\", \"templateOutlet\"], [\"cdkConnectedOverlay\", \"\", 3, \"positionChange\", \"cdkConnectedOverlayPositions\", \"cdkConnectedOverlayOrigin\", \"cdkConnectedOverlayWidth\", \"cdkConnectedOverlayOpen\", \"cdkConnectedOverlayTransformOriginOn\"], [\"nz-submenu-none-inline-child\", \"\", 3, \"subMenuMouseState\", \"theme\", \"mode\", \"nzOpen\", \"position\", \"nzDisabled\", \"isMenuInsideDropDown\", \"templateOutlet\", \"menuClass\", \"nzNoAnimation\"]],\n      template: function NzSubMenuComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵprojectionDef(_c6);\n          i0.ɵɵelementStart(0, \"div\", 2, 0);\n          i0.ɵɵlistener(\"subMenuMouseState\", function NzSubMenuComponent_Template_div_subMenuMouseState_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.setMouseEnterState($event));\n          })(\"toggleSubMenu\", function NzSubMenuComponent_Template_div_toggleSubMenu_0_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.toggleSubMenu());\n          });\n          i0.ɵɵtemplate(2, NzSubMenuComponent_Conditional_2_Template, 1, 0);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(3, NzSubMenuComponent_Conditional_3_Template, 1, 6, \"div\", 3)(4, NzSubMenuComponent_Conditional_4_Template, 1, 5)(5, NzSubMenuComponent_ng_template_5_Template, 1, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"nzIcon\", ctx.nzIcon)(\"nzTitle\", ctx.nzTitle)(\"mode\", ctx.mode)(\"nzDisabled\", ctx.nzDisabled)(\"isMenuInsideDropDown\", ctx.isMenuInsideDropDown)(\"paddingLeft\", ctx.nzPaddingLeft || ctx.inlinePaddingLeft);\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(2, !ctx.nzTitle ? 2 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(3, ctx.mode === \"inline\" ? 3 : 4);\n        }\n      },\n      dependencies: [NzSubMenuTitleComponent, NzSubmenuInlineChildComponent, NzNoAnimationDirective, NzSubmenuNoneInlineChildComponent, OverlayModule, i6.CdkConnectedOverlay, i6.CdkOverlayOrigin],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([InputBoolean()], NzSubMenuComponent.prototype, \"nzOpen\", void 0);\n__decorate([InputBoolean()], NzSubMenuComponent.prototype, \"nzDisabled\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSubMenuComponent, [{\n    type: Component,\n    args: [{\n      selector: '[nz-submenu]',\n      exportAs: 'nzSubmenu',\n      providers: [NzSubmenuService],\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      preserveWhitespaces: false,\n      template: `\n    <div\n      nz-submenu-title\n      cdkOverlayOrigin\n      #origin=\"cdkOverlayOrigin\"\n      [nzIcon]=\"nzIcon\"\n      [nzTitle]=\"nzTitle\"\n      [mode]=\"mode\"\n      [nzDisabled]=\"nzDisabled\"\n      [isMenuInsideDropDown]=\"isMenuInsideDropDown\"\n      [paddingLeft]=\"nzPaddingLeft || inlinePaddingLeft\"\n      (subMenuMouseState)=\"setMouseEnterState($event)\"\n      (toggleSubMenu)=\"toggleSubMenu()\"\n    >\n      @if (!nzTitle) {\n        <ng-content select=\"[title]\" />\n      }\n    </div>\n    @if (mode === 'inline') {\n      <div\n        nz-submenu-inline-child\n        [mode]=\"mode\"\n        [nzOpen]=\"nzOpen\"\n        [@.disabled]=\"!!noAnimation?.nzNoAnimation\"\n        [nzNoAnimation]=\"noAnimation?.nzNoAnimation\"\n        [menuClass]=\"nzMenuClassName\"\n        [templateOutlet]=\"subMenuTemplate\"\n      ></div>\n    } @else {\n      <ng-template\n        cdkConnectedOverlay\n        (positionChange)=\"onPositionChange($event)\"\n        [cdkConnectedOverlayPositions]=\"overlayPositions\"\n        [cdkConnectedOverlayOrigin]=\"origin\"\n        [cdkConnectedOverlayWidth]=\"triggerWidth!\"\n        [cdkConnectedOverlayOpen]=\"nzOpen\"\n        [cdkConnectedOverlayTransformOriginOn]=\"'.ant-menu-submenu'\"\n      >\n        <div\n          nz-submenu-none-inline-child\n          [theme]=\"theme\"\n          [mode]=\"mode\"\n          [nzOpen]=\"nzOpen\"\n          [position]=\"position\"\n          [nzDisabled]=\"nzDisabled\"\n          [isMenuInsideDropDown]=\"isMenuInsideDropDown\"\n          [templateOutlet]=\"subMenuTemplate\"\n          [menuClass]=\"nzMenuClassName\"\n          [@.disabled]=\"!!noAnimation?.nzNoAnimation\"\n          [nzNoAnimation]=\"noAnimation?.nzNoAnimation\"\n          (subMenuMouseState)=\"setMouseEnterState($event)\"\n        ></div>\n      </ng-template>\n    }\n\n    <ng-template #subMenuTemplate>\n      <ng-content />\n    </ng-template>\n  `,\n      host: {\n        '[class.ant-dropdown-menu-submenu]': `isMenuInsideDropDown`,\n        '[class.ant-dropdown-menu-submenu-disabled]': `isMenuInsideDropDown && nzDisabled`,\n        '[class.ant-dropdown-menu-submenu-open]': `isMenuInsideDropDown && nzOpen`,\n        '[class.ant-dropdown-menu-submenu-selected]': `isMenuInsideDropDown && isSelected`,\n        '[class.ant-dropdown-menu-submenu-vertical]': `isMenuInsideDropDown && mode === 'vertical'`,\n        '[class.ant-dropdown-menu-submenu-horizontal]': `isMenuInsideDropDown && mode === 'horizontal'`,\n        '[class.ant-dropdown-menu-submenu-inline]': `isMenuInsideDropDown && mode === 'inline'`,\n        '[class.ant-dropdown-menu-submenu-active]': `isMenuInsideDropDown && isActive`,\n        '[class.ant-menu-submenu]': `!isMenuInsideDropDown`,\n        '[class.ant-menu-submenu-disabled]': `!isMenuInsideDropDown && nzDisabled`,\n        '[class.ant-menu-submenu-open]': `!isMenuInsideDropDown && nzOpen`,\n        '[class.ant-menu-submenu-selected]': `!isMenuInsideDropDown && isSelected`,\n        '[class.ant-menu-submenu-vertical]': `!isMenuInsideDropDown && mode === 'vertical'`,\n        '[class.ant-menu-submenu-horizontal]': `!isMenuInsideDropDown && mode === 'horizontal'`,\n        '[class.ant-menu-submenu-inline]': `!isMenuInsideDropDown && mode === 'inline'`,\n        '[class.ant-menu-submenu-active]': `!isMenuInsideDropDown && isActive`,\n        '[class.ant-menu-submenu-rtl]': `dir === 'rtl'`\n      },\n      imports: [NzSubMenuTitleComponent, NzSubmenuInlineChildComponent, NzNoAnimationDirective, NzSubmenuNoneInlineChildComponent, OverlayModule],\n      standalone: true\n    }]\n  }], () => [{\n    type: MenuService\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: NzSubmenuService\n  }, {\n    type: i3$1.Platform\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [NzIsMenuInsideDropDownToken]\n    }]\n  }, {\n    type: i1.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i5.NzNoAnimationDirective,\n    decorators: [{\n      type: Host\n    }, {\n      type: Optional\n    }]\n  }], {\n    nzMenuClassName: [{\n      type: Input\n    }],\n    nzPaddingLeft: [{\n      type: Input\n    }],\n    nzTitle: [{\n      type: Input\n    }],\n    nzIcon: [{\n      type: Input\n    }],\n    nzOpen: [{\n      type: Input\n    }],\n    nzDisabled: [{\n      type: Input\n    }],\n    nzPlacement: [{\n      type: Input\n    }],\n    nzOpenChange: [{\n      type: Output\n    }],\n    cdkOverlayOrigin: [{\n      type: ViewChild,\n      args: [CdkOverlayOrigin, {\n        static: true,\n        read: ElementRef\n      }]\n    }],\n    listOfNzSubMenuComponent: [{\n      type: ContentChildren,\n      args: [forwardRef(() => NzSubMenuComponent), {\n        descendants: true\n      }]\n    }],\n    listOfNzMenuItemDirective: [{\n      type: ContentChildren,\n      args: [NzMenuItemComponent, {\n        descendants: true\n      }]\n    }]\n  });\n})();\nfunction MenuServiceFactory() {\n  const serviceInsideDropDown = inject(MenuService, {\n    skipSelf: true,\n    optional: true\n  });\n  const serviceOutsideDropDown = inject(NzMenuServiceLocalToken);\n  return serviceInsideDropDown ?? serviceOutsideDropDown;\n}\nfunction MenuDropDownTokenFactory() {\n  const isMenuInsideDropDownToken = inject(NzIsMenuInsideDropDownToken, {\n    skipSelf: true,\n    optional: true\n  });\n  return isMenuInsideDropDownToken ?? false;\n}\nclass NzMenuDirective {\n  setInlineCollapsed(inlineCollapsed) {\n    this.nzInlineCollapsed = inlineCollapsed;\n    this.inlineCollapsed$.next(inlineCollapsed);\n  }\n  updateInlineCollapse() {\n    if (this.listOfNzMenuItemDirective) {\n      if (this.nzInlineCollapsed) {\n        this.listOfOpenedNzSubMenuComponent = this.listOfNzSubMenuComponent.filter(submenu => submenu.nzOpen);\n        this.listOfNzSubMenuComponent.forEach(submenu => submenu.setOpenStateWithoutDebounce(false));\n      } else {\n        this.listOfOpenedNzSubMenuComponent.forEach(submenu => submenu.setOpenStateWithoutDebounce(true));\n        this.listOfOpenedNzSubMenuComponent = [];\n      }\n    }\n  }\n  constructor(nzMenuService, isMenuInsideDropDown, cdr, directionality) {\n    this.nzMenuService = nzMenuService;\n    this.isMenuInsideDropDown = isMenuInsideDropDown;\n    this.cdr = cdr;\n    this.directionality = directionality;\n    this.nzInlineIndent = 24;\n    this.nzTheme = 'light';\n    this.nzMode = 'vertical';\n    this.nzInlineCollapsed = false;\n    this.nzSelectable = !this.isMenuInsideDropDown;\n    this.nzClick = new EventEmitter();\n    this.actualMode = 'vertical';\n    this.dir = 'ltr';\n    this.inlineCollapsed$ = new BehaviorSubject(this.nzInlineCollapsed);\n    this.mode$ = new BehaviorSubject(this.nzMode);\n    this.destroy$ = new Subject();\n    this.listOfOpenedNzSubMenuComponent = [];\n  }\n  ngOnInit() {\n    combineLatest([this.inlineCollapsed$, this.mode$]).pipe(takeUntil(this.destroy$)).subscribe(([inlineCollapsed, mode]) => {\n      this.actualMode = inlineCollapsed ? 'vertical' : mode;\n      this.nzMenuService.setMode(this.actualMode);\n      this.cdr.markForCheck();\n    });\n    this.nzMenuService.descendantMenuItemClick$.pipe(takeUntil(this.destroy$)).subscribe(menu => {\n      this.nzClick.emit(menu);\n      if (this.nzSelectable && !menu.nzMatchRouter) {\n        this.listOfNzMenuItemDirective.forEach(item => item.setSelectedState(item === menu));\n      }\n    });\n    this.dir = this.directionality.value;\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.nzMenuService.setMode(this.actualMode);\n      this.cdr.markForCheck();\n    });\n  }\n  ngAfterContentInit() {\n    this.inlineCollapsed$.pipe(takeUntil(this.destroy$)).subscribe(() => {\n      this.updateInlineCollapse();\n      this.cdr.markForCheck();\n    });\n  }\n  ngOnChanges(changes) {\n    const {\n      nzInlineCollapsed,\n      nzInlineIndent,\n      nzTheme,\n      nzMode\n    } = changes;\n    if (nzInlineCollapsed) {\n      this.inlineCollapsed$.next(this.nzInlineCollapsed);\n    }\n    if (nzInlineIndent) {\n      this.nzMenuService.setInlineIndent(this.nzInlineIndent);\n    }\n    if (nzTheme) {\n      this.nzMenuService.setTheme(this.nzTheme);\n    }\n    if (nzMode) {\n      this.mode$.next(this.nzMode);\n      if (!changes.nzMode.isFirstChange() && this.listOfNzSubMenuComponent) {\n        this.listOfNzSubMenuComponent.forEach(submenu => submenu.setOpenStateWithoutDebounce(false));\n      }\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next(true);\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzMenuDirective_Factory(t) {\n      return new (t || NzMenuDirective)(i0.ɵɵdirectiveInject(MenuService), i0.ɵɵdirectiveInject(NzIsMenuInsideDropDownToken), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzMenuDirective,\n      selectors: [[\"\", \"nz-menu\", \"\"]],\n      contentQueries: function NzMenuDirective_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, NzMenuItemComponent, 5);\n          i0.ɵɵcontentQuery(dirIndex, NzSubMenuComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfNzMenuItemDirective = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfNzSubMenuComponent = _t);\n        }\n      },\n      hostVars: 34,\n      hostBindings: function NzMenuDirective_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-dropdown-menu\", ctx.isMenuInsideDropDown)(\"ant-dropdown-menu-root\", ctx.isMenuInsideDropDown)(\"ant-dropdown-menu-light\", ctx.isMenuInsideDropDown && ctx.nzTheme === \"light\")(\"ant-dropdown-menu-dark\", ctx.isMenuInsideDropDown && ctx.nzTheme === \"dark\")(\"ant-dropdown-menu-vertical\", ctx.isMenuInsideDropDown && ctx.actualMode === \"vertical\")(\"ant-dropdown-menu-horizontal\", ctx.isMenuInsideDropDown && ctx.actualMode === \"horizontal\")(\"ant-dropdown-menu-inline\", ctx.isMenuInsideDropDown && ctx.actualMode === \"inline\")(\"ant-dropdown-menu-inline-collapsed\", ctx.isMenuInsideDropDown && ctx.nzInlineCollapsed)(\"ant-menu\", !ctx.isMenuInsideDropDown)(\"ant-menu-root\", !ctx.isMenuInsideDropDown)(\"ant-menu-light\", !ctx.isMenuInsideDropDown && ctx.nzTheme === \"light\")(\"ant-menu-dark\", !ctx.isMenuInsideDropDown && ctx.nzTheme === \"dark\")(\"ant-menu-vertical\", !ctx.isMenuInsideDropDown && ctx.actualMode === \"vertical\")(\"ant-menu-horizontal\", !ctx.isMenuInsideDropDown && ctx.actualMode === \"horizontal\")(\"ant-menu-inline\", !ctx.isMenuInsideDropDown && ctx.actualMode === \"inline\")(\"ant-menu-inline-collapsed\", !ctx.isMenuInsideDropDown && ctx.nzInlineCollapsed)(\"ant-menu-rtl\", ctx.dir === \"rtl\");\n        }\n      },\n      inputs: {\n        nzInlineIndent: \"nzInlineIndent\",\n        nzTheme: \"nzTheme\",\n        nzMode: \"nzMode\",\n        nzInlineCollapsed: \"nzInlineCollapsed\",\n        nzSelectable: \"nzSelectable\"\n      },\n      outputs: {\n        nzClick: \"nzClick\"\n      },\n      exportAs: [\"nzMenu\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: NzMenuServiceLocalToken,\n        useClass: MenuService\n      }, /** use the top level service **/\n      {\n        provide: MenuService,\n        useFactory: MenuServiceFactory\n      }, /** check if menu inside dropdown-menu component **/\n      {\n        provide: NzIsMenuInsideDropDownToken,\n        useFactory: MenuDropDownTokenFactory\n      }]), i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n__decorate([InputBoolean()], NzMenuDirective.prototype, \"nzInlineCollapsed\", void 0);\n__decorate([InputBoolean()], NzMenuDirective.prototype, \"nzSelectable\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzMenuDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[nz-menu]',\n      exportAs: 'nzMenu',\n      providers: [{\n        provide: NzMenuServiceLocalToken,\n        useClass: MenuService\n      }, /** use the top level service **/\n      {\n        provide: MenuService,\n        useFactory: MenuServiceFactory\n      }, /** check if menu inside dropdown-menu component **/\n      {\n        provide: NzIsMenuInsideDropDownToken,\n        useFactory: MenuDropDownTokenFactory\n      }],\n      host: {\n        '[class.ant-dropdown-menu]': `isMenuInsideDropDown`,\n        '[class.ant-dropdown-menu-root]': `isMenuInsideDropDown`,\n        '[class.ant-dropdown-menu-light]': `isMenuInsideDropDown && nzTheme === 'light'`,\n        '[class.ant-dropdown-menu-dark]': `isMenuInsideDropDown && nzTheme === 'dark'`,\n        '[class.ant-dropdown-menu-vertical]': `isMenuInsideDropDown && actualMode === 'vertical'`,\n        '[class.ant-dropdown-menu-horizontal]': `isMenuInsideDropDown && actualMode === 'horizontal'`,\n        '[class.ant-dropdown-menu-inline]': `isMenuInsideDropDown && actualMode === 'inline'`,\n        '[class.ant-dropdown-menu-inline-collapsed]': `isMenuInsideDropDown && nzInlineCollapsed`,\n        '[class.ant-menu]': `!isMenuInsideDropDown`,\n        '[class.ant-menu-root]': `!isMenuInsideDropDown`,\n        '[class.ant-menu-light]': `!isMenuInsideDropDown && nzTheme === 'light'`,\n        '[class.ant-menu-dark]': `!isMenuInsideDropDown && nzTheme === 'dark'`,\n        '[class.ant-menu-vertical]': `!isMenuInsideDropDown && actualMode === 'vertical'`,\n        '[class.ant-menu-horizontal]': `!isMenuInsideDropDown && actualMode === 'horizontal'`,\n        '[class.ant-menu-inline]': `!isMenuInsideDropDown && actualMode === 'inline'`,\n        '[class.ant-menu-inline-collapsed]': `!isMenuInsideDropDown && nzInlineCollapsed`,\n        '[class.ant-menu-rtl]': `dir === 'rtl'`\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: MenuService\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [NzIsMenuInsideDropDownToken]\n    }]\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    listOfNzMenuItemDirective: [{\n      type: ContentChildren,\n      args: [NzMenuItemComponent, {\n        descendants: true\n      }]\n    }],\n    listOfNzSubMenuComponent: [{\n      type: ContentChildren,\n      args: [NzSubMenuComponent, {\n        descendants: true\n      }]\n    }],\n    nzInlineIndent: [{\n      type: Input\n    }],\n    nzTheme: [{\n      type: Input\n    }],\n    nzMode: [{\n      type: Input\n    }],\n    nzInlineCollapsed: [{\n      type: Input\n    }],\n    nzSelectable: [{\n      type: Input\n    }],\n    nzClick: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction MenuGroupFactory() {\n  const isMenuInsideDropDownToken = inject(NzIsMenuInsideDropDownToken, {\n    optional: true,\n    skipSelf: true\n  });\n  return isMenuInsideDropDownToken ?? false;\n}\nclass NzMenuGroupComponent {\n  constructor(elementRef, renderer, isMenuInsideDropDown) {\n    this.elementRef = elementRef;\n    this.renderer = renderer;\n    this.isMenuInsideDropDown = isMenuInsideDropDown;\n    const className = this.isMenuInsideDropDown ? 'ant-dropdown-menu-item-group' : 'ant-menu-item-group';\n    this.renderer.addClass(elementRef.nativeElement, className);\n  }\n  ngAfterViewInit() {\n    const ulElement = this.titleElement.nativeElement.nextElementSibling;\n    if (ulElement) {\n      /** add classname to ul **/\n      const className = this.isMenuInsideDropDown ? 'ant-dropdown-menu-item-group-list' : 'ant-menu-item-group-list';\n      this.renderer.addClass(ulElement, className);\n    }\n  }\n  static {\n    this.ɵfac = function NzMenuGroupComponent_Factory(t) {\n      return new (t || NzMenuGroupComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(NzIsMenuInsideDropDownToken));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzMenuGroupComponent,\n      selectors: [[\"\", \"nz-menu-group\", \"\"]],\n      viewQuery: function NzMenuGroupComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c8, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.titleElement = _t.first);\n        }\n      },\n      inputs: {\n        nzTitle: \"nzTitle\"\n      },\n      exportAs: [\"nzMenuGroup\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([/** check if menu inside dropdown-menu component **/\n      {\n        provide: NzIsMenuInsideDropDownToken,\n        useFactory: MenuGroupFactory\n      }]), i0.ɵɵStandaloneFeature],\n      attrs: _c9,\n      ngContentSelectors: _c11,\n      decls: 5,\n      vars: 6,\n      consts: [[\"titleElement\", \"\"], [4, \"nzStringTemplateOutlet\"]],\n      template: function NzMenuGroupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c10);\n          i0.ɵɵelementStart(0, \"div\", null, 0);\n          i0.ɵɵtemplate(2, NzMenuGroupComponent_ng_container_2_Template, 2, 1, \"ng-container\", 1)(3, NzMenuGroupComponent_Conditional_3_Template, 1, 0);\n          i0.ɵɵelementEnd();\n          i0.ɵɵprojection(4);\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-menu-item-group-title\", !ctx.isMenuInsideDropDown)(\"ant-dropdown-menu-item-group-title\", ctx.isMenuInsideDropDown);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx.nzTitle);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(3, !ctx.nzTitle ? 3 : -1);\n        }\n      },\n      dependencies: [NzOutletModule, i3.NzStringTemplateOutletDirective],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzMenuGroupComponent, [{\n    type: Component,\n    args: [{\n      selector: '[nz-menu-group]',\n      exportAs: 'nzMenuGroup',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [/** check if menu inside dropdown-menu component **/\n      {\n        provide: NzIsMenuInsideDropDownToken,\n        useFactory: MenuGroupFactory\n      }],\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <div\n      [class.ant-menu-item-group-title]=\"!isMenuInsideDropDown\"\n      [class.ant-dropdown-menu-item-group-title]=\"isMenuInsideDropDown\"\n      #titleElement\n    >\n      <ng-container *nzStringTemplateOutlet=\"nzTitle\">{{ nzTitle }}</ng-container>\n      @if (!nzTitle) {\n        <ng-content select=\"[title]\" />\n      }\n    </div>\n    <ng-content></ng-content>\n  `,\n      preserveWhitespaces: false,\n      imports: [NzOutletModule],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [NzIsMenuInsideDropDownToken]\n    }]\n  }], {\n    nzTitle: [{\n      type: Input\n    }],\n    titleElement: [{\n      type: ViewChild,\n      args: ['titleElement']\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzMenuDividerDirective {\n  constructor(elementRef) {\n    this.elementRef = elementRef;\n  }\n  static {\n    this.ɵfac = function NzMenuDividerDirective_Factory(t) {\n      return new (t || NzMenuDividerDirective)(i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzMenuDividerDirective,\n      selectors: [[\"\", \"nz-menu-divider\", \"\"]],\n      hostAttrs: [1, \"ant-dropdown-menu-item-divider\"],\n      exportAs: [\"nzMenuDivider\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzMenuDividerDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[nz-menu-divider]',\n      exportAs: 'nzMenuDivider',\n      host: {\n        class: 'ant-dropdown-menu-item-divider'\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }], null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzMenuModule {\n  static {\n    this.ɵfac = function NzMenuModule_Factory(t) {\n      return new (t || NzMenuModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzMenuModule,\n      imports: [NzMenuDirective, NzMenuItemComponent, NzSubMenuComponent, NzMenuDividerDirective, NzMenuGroupComponent, NzSubMenuTitleComponent, NzSubmenuInlineChildComponent, NzSubmenuNoneInlineChildComponent],\n      exports: [NzMenuDirective, NzMenuItemComponent, NzSubMenuComponent, NzMenuDividerDirective, NzMenuGroupComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [NzSubMenuComponent, NzMenuGroupComponent, NzSubMenuTitleComponent]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzMenuModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzMenuDirective, NzMenuItemComponent, NzSubMenuComponent, NzMenuDividerDirective, NzMenuGroupComponent, NzSubMenuTitleComponent, NzSubmenuInlineChildComponent, NzSubmenuNoneInlineChildComponent],\n      exports: [NzMenuDirective, NzMenuItemComponent, NzSubMenuComponent, NzMenuDividerDirective, NzMenuGroupComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MenuDropDownTokenFactory, MenuGroupFactory, MenuService, MenuServiceFactory, NzIsMenuInsideDropDownToken, NzMenuDirective, NzMenuDividerDirective, NzMenuGroupComponent, NzMenuItemComponent, NzMenuModule, NzMenuServiceLocalToken, NzSubMenuComponent, NzSubMenuTitleComponent, NzSubmenuInlineChildComponent, NzSubmenuNoneInlineChildComponent, NzSubmenuService };", "map": {"version": 3, "names": ["__decorate", "i0", "InjectionToken", "Injectable", "SkipSelf", "Optional", "Inject", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Input", "ContentChildren", "EventEmitter", "Output", "ElementRef", "Host", "ViewChild", "forwardRef", "inject", "Directive", "NgModule", "Subject", "BehaviorSubject", "merge", "combineLatest", "map", "mergeMap", "filter", "mapTo", "auditTime", "distinctUntilChanged", "takeUntil", "startWith", "switchMap", "InputBoolean", "i4", "NavigationEnd", "RouterLink", "i1", "i6", "CdkOverlayOrigin", "OverlayModule", "i5", "NzNoAnimationDirective", "POSITION_MAP", "getPlacementName", "NgTemplateOutlet", "Ng<PERSON><PERSON>", "collapseMotion", "zoomBigMotion", "slideMotion", "i3", "NzOutletModule", "i2", "NzIconModule", "i3$1", "_c0", "_c1", "_c2", "NzSubmenuInlineChildComponent_ng_template_0_Template", "rf", "ctx", "_c3", "NzSubmenuNoneInlineChildComponent_ng_template_1_Template", "_c4", "NzSubMenuTitleComponent_Conditional_0_Template", "ɵɵelement", "ctx_r0", "ɵɵnextContext", "ɵɵproperty", "nzIcon", "NzSubMenuTitleComponent_ng_container_1_Template", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelementContainerEnd", "ɵɵadvance", "ɵɵtextInterpolate", "nzTitle", "NzSubMenuTitleComponent_Conditional_3_Case_1_Template", "NzSubMenuTitleComponent_Conditional_3_Case_2_Template", "NzSubMenuTitleComponent_Conditional_3_Template", "ɵɵtemplate", "tmp_1_0", "ɵɵconditional", "dir", "NzSubMenuTitleComponent_Conditional_4_Template", "_c5", "_c6", "_c7", "NzSubMenuComponent_Conditional_2_Template", "ɵɵprojection", "NzSubMenuComponent_Conditional_3_Template", "ctx_r1", "subMenuTemplate_r3", "ɵɵreference", "mode", "nzOpen", "noAnimation", "nzNoAnimation", "nzMenuClassName", "NzSubMenuComponent_Conditional_4_ng_template_0_Template", "_r5", "ɵɵgetCurrentView", "ɵɵlistener", "NzSubMenuComponent_Conditional_4_ng_template_0_Template_div_subMenuMouseState_0_listener", "$event", "ɵɵrestoreView", "ɵɵresetView", "setMouseEnterState", "theme", "position", "nzDisabled", "isMenuInsideDropDown", "NzSubMenuComponent_Conditional_4_Template", "_r4", "NzSubMenuComponent_Conditional_4_Template_ng_template_positionChange_0_listener", "onPositionChange", "origin_r6", "overlayPositions", "triggerWidth", "NzSubMenuComponent_ng_template_5_Template", "_c8", "_c9", "_c10", "_c11", "NzMenuGroupComponent_ng_container_2_Template", "NzMenuGroupComponent_Conditional_3_Template", "NzIsMenuInsideDropDownToken", "NzMenuServiceLocalToken", "MenuService", "constructor", "descendantMenuItemClick$", "childMenuItemClick$", "theme$", "mode$", "inlineIndent$", "isChildSubMenuOpen$", "onDescendantMenuItemClick", "menu", "next", "onChildMenuItemClick", "setMode", "setTheme", "setInlineIndent", "indent", "ɵfac", "MenuService_Factory", "t", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ngDevMode", "ɵsetClassMetadata", "type", "NzSubmenuService", "setOpenStateWithoutDebounce", "value", "isCurrentSubMenuOpen$", "setMouseEnterTitleOrOverlayState", "isMouseEnterTitleOrOverlay$", "nzHostSubmenuService", "nzMenuService", "pipe", "level", "destroy$", "isClosedByMenuItemClick", "isCurrentSubmenuOpen$", "isSubMenuOpenWithDebounce$", "isChildSubMenuOpen", "isCurrentSubmenuOpen", "subscribe", "data", "ngOnDestroy", "complete", "NzSubmenuService_Factory", "ɵɵinject", "decorators", "undefined", "args", "NzMenuItemComponent", "clickMenuItem", "e", "preventDefault", "stopPropagation", "nzSubmenuService", "setSelectedState", "nzSelected", "selected$", "updateRouterActive", "listOfRouterLink", "router", "navigated", "nzMatchRouter", "Promise", "resolve", "then", "hasActiveLinks", "cdr", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isActiveCheckFn", "isLinkActive", "routerLink", "some", "link", "isActive", "urlTree", "paths", "nzMatchRouterExact", "queryParams", "fragment", "matrixParams", "directionality", "inlinePaddingLeft", "nzDanger", "events", "ngOnInit", "inlineIndent", "change", "direction", "ngAfterContentInit", "changes", "ngOnChanges", "NzMenuItemComponent_Factory", "ɵɵdirectiveInject", "ChangeDetectorRef", "Directionality", "Router", "ɵcmp", "ɵɵdefineComponent", "selectors", "contentQueries", "NzMenuItemComponent_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "hostVars", "hostBindings", "NzMenuItemComponent_HostBindings", "NzMenuItemComponent_click_HostBindingHandler", "ɵɵstyleProp", "nzPaddingLeft", "ɵɵclassProp", "inputs", "exportAs", "standalone", "features", "ɵɵNgOnChangesFeature", "ɵɵStandaloneFeature", "attrs", "ngContentSelectors", "decls", "vars", "consts", "template", "NzMenuItemComponent_Template", "ɵɵprojectionDef", "encapsulation", "changeDetection", "prototype", "selector", "OnPush", "None", "preserveWhitespaces", "host", "descendants", "NzSubmenuInlineChildComponent", "elementRef", "renderer", "templateOutlet", "menuClass", "listOfCacheClassName", "expandState", "calcMotionState", "length", "item", "for<PERSON>ach", "className", "removeClass", "nativeElement", "split", "addClass", "NzSubmenuInlineChildComponent_Factory", "Renderer2", "hostAttrs", "NzSubmenuInlineChildComponent_HostBindings", "ɵɵsyntheticHostProperty", "NzSubmenuInlineChildComponent_Template", "dependencies", "animation", "animations", "class", "imports", "NzSubmenuNoneInlineChildComponent", "subMenuMouseState", "setMouseState", "state", "NzSubmenuNoneInlineChildComponent_Factory", "NzSubmenuNoneInlineChildComponent_HostBindings", "NzSubmenuNoneInlineChildComponent_mouseenter_HostBindingHandler", "NzSubmenuNoneInlineChildComponent_mouseleave_HostBindingHandler", "outputs", "NzSubmenuNoneInlineChildComponent_Template", "NzSubMenuTitleComponent", "paddingLeft", "toggleSubMenu", "detectChanges", "clickTitle", "emit", "NzSubMenuTitleComponent_Factory", "NzSubMenuTitleComponent_HostBindings", "NzSubMenuTitleComponent_click_HostBindingHandler", "NzSubMenuTitleComponent_mouseenter_HostBindingHandler", "NzSubMenuTitleComponent_mouseleave_HostBindingHandler", "NzSubMenuTitleComponent_Template", "NzIconDirective", "NzStringTemplateOutletDirective", "listOfVerticalPositions", "rightTop", "right", "rightBottom", "leftTop", "left", "leftBottom", "listOfHorizontalPositions", "bottomLeft", "bottomRight", "topRight", "topLeft", "NzSubMenuComponent", "open", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "platform", "<PERSON><PERSON><PERSON><PERSON>", "cdkOverlayOrigin", "nzPlacement", "getBoundingClientRect", "width", "placement", "nzOpenChange", "listOfNzSubMenuComponent", "listOfNzMenuItemDirective", "isSelected", "mergedObservable", "selected", "NzSubMenuComponent_Factory", "Platform", "NzSubMenuComponent_ContentQueries", "viewQuery", "NzSubMenuComponent_Query", "ɵɵviewQuery", "first", "NzSubMenuComponent_HostBindings", "ɵɵProvidersFeature", "NzSubMenuComponent_Template", "_r1", "NzSubMenuComponent_Template_div_subMenuMouseState_0_listener", "NzSubMenuComponent_Template_div_toggleSubMenu_0_listener", "ɵɵtemplateRefExtractor", "CdkConnectedOverlay", "providers", "static", "read", "MenuServiceFactory", "serviceInsideDropDown", "skipSelf", "optional", "serviceOutsideDropDown", "MenuDropDownTokenFactory", "isMenuInsideDropDownToken", "NzMenuDirective", "setInlineCollapsed", "inlineCollapsed", "nzInlineCollapsed", "inlineCollapsed$", "updateInlineCollapse", "listOfOpenedNzSubMenuComponent", "submenu", "nzInlineIndent", "nzTheme", "nzMode", "nzSelectable", "nzClick", "actualMode", "isFirstChange", "NzMenuDirective_Factory", "ɵdir", "ɵɵdefineDirective", "NzMenuDirective_ContentQueries", "NzMenuDirective_HostBindings", "provide", "useClass", "useFactory", "MenuGroupFactory", "NzMenuGroupComponent", "ngAfterViewInit", "ulElement", "titleElement", "nextElement<PERSON><PERSON>ling", "NzMenuGroupComponent_Factory", "NzMenuGroupComponent_Query", "NzMenuGroupComponent_Template", "NzMenuDividerDirective", "NzMenuDividerDirective_Factory", "NzMenuModule", "NzMenuModule_Factory", "ɵmod", "ɵɵdefineNgModule", "exports", "ɵinj", "ɵɵdefineInjector"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-menu.mjs"], "sourcesContent": ["import { __decorate } from 'tslib';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Injectable, SkipSelf, Optional, Inject, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, ContentChildren, EventEmitter, Output, ElementRef, Host, ViewChild, forwardRef, inject, Directive, NgModule } from '@angular/core';\nimport { Subject, BehaviorSubject, merge, combineLatest } from 'rxjs';\nimport { map, mergeMap, filter, mapTo, auditTime, distinctUntilChanged, takeUntil, startWith, switchMap } from 'rxjs/operators';\nimport { InputBoolean } from 'ng-zorro-antd/core/util';\nimport * as i4 from '@angular/router';\nimport { NavigationEnd, RouterLink } from '@angular/router';\nimport * as i1 from '@angular/cdk/bidi';\nimport * as i6 from '@angular/cdk/overlay';\nimport { CdkOverlayOrigin, OverlayModule } from '@angular/cdk/overlay';\nimport * as i5 from 'ng-zorro-antd/core/no-animation';\nimport { NzNoAnimationDirective } from 'ng-zorro-antd/core/no-animation';\nimport { POSITION_MAP, getPlacementName } from 'ng-zorro-antd/core/overlay';\nimport { NgTemplateOutlet, NgClass } from '@angular/common';\nimport { collapseMotion, zoomBigMotion, slideMotion } from 'ng-zorro-antd/core/animation';\nimport * as i3 from 'ng-zorro-antd/core/outlet';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport * as i2 from 'ng-zorro-antd/icon';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport * as i3$1 from '@angular/cdk/platform';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst NzIsMenuInsideDropDownToken = new InjectionToken('NzIsInDropDownMenuToken');\nconst NzMenuServiceLocalToken = new InjectionToken('NzMenuServiceLocalToken');\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass MenuService {\n    constructor() {\n        /** all descendant menu click **/\n        this.descendantMenuItemClick$ = new Subject();\n        /** child menu item click **/\n        this.childMenuItemClick$ = new Subject();\n        this.theme$ = new BehaviorSubject('light');\n        this.mode$ = new BehaviorSubject('vertical');\n        this.inlineIndent$ = new BehaviorSubject(24);\n        this.isChildSubMenuOpen$ = new BehaviorSubject(false);\n    }\n    onDescendantMenuItemClick(menu) {\n        this.descendantMenuItemClick$.next(menu);\n    }\n    onChildMenuItemClick(menu) {\n        this.childMenuItemClick$.next(menu);\n    }\n    setMode(mode) {\n        this.mode$.next(mode);\n    }\n    setTheme(theme) {\n        this.theme$.next(theme);\n    }\n    setInlineIndent(indent) {\n        this.inlineIndent$.next(indent);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: MenuService, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: MenuService }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: MenuService, decorators: [{\n            type: Injectable\n        }] });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzSubmenuService {\n    /**\n     * menu item inside submenu clicked\n     *\n     * @param menu\n     */\n    onChildMenuItemClick(menu) {\n        this.childMenuItemClick$.next(menu);\n    }\n    setOpenStateWithoutDebounce(value) {\n        this.isCurrentSubMenuOpen$.next(value);\n    }\n    setMouseEnterTitleOrOverlayState(value) {\n        this.isMouseEnterTitleOrOverlay$.next(value);\n    }\n    constructor(nzHostSubmenuService, nzMenuService, isMenuInsideDropDown) {\n        this.nzHostSubmenuService = nzHostSubmenuService;\n        this.nzMenuService = nzMenuService;\n        this.isMenuInsideDropDown = isMenuInsideDropDown;\n        this.mode$ = this.nzMenuService.mode$.pipe(map(mode => {\n            if (mode === 'inline') {\n                return 'inline';\n                /** if inside another submenu, set the mode to vertical **/\n            }\n            else if (mode === 'vertical' || this.nzHostSubmenuService) {\n                return 'vertical';\n            }\n            else {\n                return 'horizontal';\n            }\n        }));\n        this.level = 1;\n        this.isCurrentSubMenuOpen$ = new BehaviorSubject(false);\n        this.isChildSubMenuOpen$ = new BehaviorSubject(false);\n        /** submenu title & overlay mouse enter status **/\n        this.isMouseEnterTitleOrOverlay$ = new Subject();\n        this.childMenuItemClick$ = new Subject();\n        this.destroy$ = new Subject();\n        if (this.nzHostSubmenuService) {\n            this.level = this.nzHostSubmenuService.level + 1;\n        }\n        /** close if menu item clicked **/\n        const isClosedByMenuItemClick = this.childMenuItemClick$.pipe(mergeMap(() => this.mode$), filter(mode => mode !== 'inline' || this.isMenuInsideDropDown), mapTo(false));\n        const isCurrentSubmenuOpen$ = merge(this.isMouseEnterTitleOrOverlay$, isClosedByMenuItemClick);\n        /** combine the child submenu status with current submenu status to calculate host submenu open **/\n        const isSubMenuOpenWithDebounce$ = combineLatest([this.isChildSubMenuOpen$, isCurrentSubmenuOpen$]).pipe(map(([isChildSubMenuOpen, isCurrentSubmenuOpen]) => isChildSubMenuOpen || isCurrentSubmenuOpen), auditTime(150), distinctUntilChanged(), takeUntil(this.destroy$));\n        isSubMenuOpenWithDebounce$.pipe(distinctUntilChanged()).subscribe(data => {\n            this.setOpenStateWithoutDebounce(data);\n            if (this.nzHostSubmenuService) {\n                /** set parent submenu's child submenu open status **/\n                this.nzHostSubmenuService.isChildSubMenuOpen$.next(data);\n            }\n            else {\n                this.nzMenuService.isChildSubMenuOpen$.next(data);\n            }\n        });\n    }\n    ngOnDestroy() {\n        this.destroy$.next();\n        this.destroy$.complete();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzSubmenuService, deps: [{ token: NzSubmenuService, optional: true, skipSelf: true }, { token: MenuService }, { token: NzIsMenuInsideDropDownToken }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzSubmenuService }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzSubmenuService, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: NzSubmenuService, decorators: [{\n                    type: SkipSelf\n                }, {\n                    type: Optional\n                }] }, { type: MenuService }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [NzIsMenuInsideDropDownToken]\n                }] }] });\n\nclass NzMenuItemComponent {\n    /** clear all item selected status except this */\n    clickMenuItem(e) {\n        if (this.nzDisabled) {\n            e.preventDefault();\n            e.stopPropagation();\n        }\n        else {\n            this.nzMenuService.onDescendantMenuItemClick(this);\n            if (this.nzSubmenuService) {\n                /** menu item inside the submenu **/\n                this.nzSubmenuService.onChildMenuItemClick(this);\n            }\n            else {\n                /** menu item inside the root menu **/\n                this.nzMenuService.onChildMenuItemClick(this);\n            }\n        }\n    }\n    setSelectedState(value) {\n        this.nzSelected = value;\n        this.selected$.next(value);\n    }\n    updateRouterActive() {\n        if (!this.listOfRouterLink || !this.router || !this.router.navigated || !this.nzMatchRouter) {\n            return;\n        }\n        Promise.resolve().then(() => {\n            const hasActiveLinks = this.hasActiveLinks();\n            if (this.nzSelected !== hasActiveLinks) {\n                this.nzSelected = hasActiveLinks;\n                this.setSelectedState(this.nzSelected);\n                this.cdr.markForCheck();\n            }\n        });\n    }\n    hasActiveLinks() {\n        const isActiveCheckFn = this.isLinkActive(this.router);\n        return (this.routerLink && isActiveCheckFn(this.routerLink)) || this.listOfRouterLink.some(isActiveCheckFn);\n    }\n    isLinkActive(router) {\n        return (link) => router.isActive(link.urlTree || '', {\n            paths: this.nzMatchRouterExact ? 'exact' : 'subset',\n            queryParams: this.nzMatchRouterExact ? 'exact' : 'subset',\n            fragment: 'ignored',\n            matrixParams: 'ignored'\n        });\n    }\n    constructor(nzMenuService, cdr, nzSubmenuService, isMenuInsideDropDown, directionality, routerLink, router) {\n        this.nzMenuService = nzMenuService;\n        this.cdr = cdr;\n        this.nzSubmenuService = nzSubmenuService;\n        this.isMenuInsideDropDown = isMenuInsideDropDown;\n        this.directionality = directionality;\n        this.routerLink = routerLink;\n        this.router = router;\n        this.destroy$ = new Subject();\n        this.level = this.nzSubmenuService ? this.nzSubmenuService.level + 1 : 1;\n        this.selected$ = new Subject();\n        this.inlinePaddingLeft = null;\n        this.dir = 'ltr';\n        this.nzDisabled = false;\n        this.nzSelected = false;\n        this.nzDanger = false;\n        this.nzMatchRouterExact = false;\n        this.nzMatchRouter = false;\n        if (router) {\n            this.router.events.pipe(takeUntil(this.destroy$), filter(e => e instanceof NavigationEnd)).subscribe(() => {\n                this.updateRouterActive();\n            });\n        }\n    }\n    ngOnInit() {\n        /** store origin padding in padding */\n        combineLatest([this.nzMenuService.mode$, this.nzMenuService.inlineIndent$])\n            .pipe(takeUntil(this.destroy$))\n            .subscribe(([mode, inlineIndent]) => {\n            this.inlinePaddingLeft = mode === 'inline' ? this.level * inlineIndent : null;\n        });\n        this.dir = this.directionality.value;\n        this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe((direction) => {\n            this.dir = direction;\n        });\n    }\n    ngAfterContentInit() {\n        this.listOfRouterLink.changes.pipe(takeUntil(this.destroy$)).subscribe(() => this.updateRouterActive());\n        this.updateRouterActive();\n    }\n    ngOnChanges(changes) {\n        if (changes.nzSelected) {\n            this.setSelectedState(this.nzSelected);\n        }\n    }\n    ngOnDestroy() {\n        this.destroy$.next(true);\n        this.destroy$.complete();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzMenuItemComponent, deps: [{ token: MenuService }, { token: i0.ChangeDetectorRef }, { token: NzSubmenuService, optional: true }, { token: NzIsMenuInsideDropDownToken }, { token: i1.Directionality, optional: true }, { token: i4.RouterLink, optional: true }, { token: i4.Router, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzMenuItemComponent, isStandalone: true, selector: \"[nz-menu-item]\", inputs: { nzPaddingLeft: \"nzPaddingLeft\", nzDisabled: \"nzDisabled\", nzSelected: \"nzSelected\", nzDanger: \"nzDanger\", nzMatchRouterExact: \"nzMatchRouterExact\", nzMatchRouter: \"nzMatchRouter\" }, host: { listeners: { \"click\": \"clickMenuItem($event)\" }, properties: { \"class.ant-dropdown-menu-item\": \"isMenuInsideDropDown\", \"class.ant-dropdown-menu-item-selected\": \"isMenuInsideDropDown && nzSelected\", \"class.ant-dropdown-menu-item-danger\": \"isMenuInsideDropDown && nzDanger\", \"class.ant-dropdown-menu-item-disabled\": \"isMenuInsideDropDown && nzDisabled\", \"class.ant-menu-item\": \"!isMenuInsideDropDown\", \"class.ant-menu-item-selected\": \"!isMenuInsideDropDown && nzSelected\", \"class.ant-menu-item-danger\": \"!isMenuInsideDropDown && nzDanger\", \"class.ant-menu-item-disabled\": \"!isMenuInsideDropDown && nzDisabled\", \"style.paddingLeft.px\": \"dir === 'rtl' ? null : nzPaddingLeft || inlinePaddingLeft\", \"style.paddingRight.px\": \"dir === 'rtl' ? nzPaddingLeft || inlinePaddingLeft : null\" } }, queries: [{ propertyName: \"listOfRouterLink\", predicate: RouterLink, descendants: true }], exportAs: [\"nzMenuItem\"], usesOnChanges: true, ngImport: i0, template: `\n    <span class=\"ant-menu-title-content\">\n      <ng-content></ng-content>\n    </span>\n  `, isInline: true, changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\n__decorate([\n    InputBoolean()\n], NzMenuItemComponent.prototype, \"nzDisabled\", void 0);\n__decorate([\n    InputBoolean()\n], NzMenuItemComponent.prototype, \"nzSelected\", void 0);\n__decorate([\n    InputBoolean()\n], NzMenuItemComponent.prototype, \"nzDanger\", void 0);\n__decorate([\n    InputBoolean()\n], NzMenuItemComponent.prototype, \"nzMatchRouterExact\", void 0);\n__decorate([\n    InputBoolean()\n], NzMenuItemComponent.prototype, \"nzMatchRouter\", void 0);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzMenuItemComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: '[nz-menu-item]',\n                    exportAs: 'nzMenuItem',\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    encapsulation: ViewEncapsulation.None,\n                    preserveWhitespaces: false,\n                    template: `\n    <span class=\"ant-menu-title-content\">\n      <ng-content></ng-content>\n    </span>\n  `,\n                    host: {\n                        '[class.ant-dropdown-menu-item]': `isMenuInsideDropDown`,\n                        '[class.ant-dropdown-menu-item-selected]': `isMenuInsideDropDown && nzSelected`,\n                        '[class.ant-dropdown-menu-item-danger]': `isMenuInsideDropDown && nzDanger`,\n                        '[class.ant-dropdown-menu-item-disabled]': `isMenuInsideDropDown && nzDisabled`,\n                        '[class.ant-menu-item]': `!isMenuInsideDropDown`,\n                        '[class.ant-menu-item-selected]': `!isMenuInsideDropDown && nzSelected`,\n                        '[class.ant-menu-item-danger]': `!isMenuInsideDropDown && nzDanger`,\n                        '[class.ant-menu-item-disabled]': `!isMenuInsideDropDown && nzDisabled`,\n                        '[style.paddingLeft.px]': `dir === 'rtl' ? null : nzPaddingLeft || inlinePaddingLeft`,\n                        '[style.paddingRight.px]': `dir === 'rtl' ? nzPaddingLeft || inlinePaddingLeft : null`,\n                        '(click)': 'clickMenuItem($event)'\n                    },\n                    standalone: true\n                }]\n        }], ctorParameters: () => [{ type: MenuService }, { type: i0.ChangeDetectorRef }, { type: NzSubmenuService, decorators: [{\n                    type: Optional\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [NzIsMenuInsideDropDownToken]\n                }] }, { type: i1.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i4.RouterLink, decorators: [{\n                    type: Optional\n                }] }, { type: i4.Router, decorators: [{\n                    type: Optional\n                }] }], propDecorators: { nzPaddingLeft: [{\n                type: Input\n            }], nzDisabled: [{\n                type: Input\n            }], nzSelected: [{\n                type: Input\n            }], nzDanger: [{\n                type: Input\n            }], nzMatchRouterExact: [{\n                type: Input\n            }], nzMatchRouter: [{\n                type: Input\n            }], listOfRouterLink: [{\n                type: ContentChildren,\n                args: [RouterLink, { descendants: true }]\n            }] } });\n\nclass NzSubmenuInlineChildComponent {\n    constructor(elementRef, renderer, directionality) {\n        this.elementRef = elementRef;\n        this.renderer = renderer;\n        this.directionality = directionality;\n        this.templateOutlet = null;\n        this.menuClass = '';\n        this.mode = 'vertical';\n        this.nzOpen = false;\n        this.listOfCacheClassName = [];\n        this.expandState = 'collapsed';\n        this.dir = 'ltr';\n        this.destroy$ = new Subject();\n    }\n    calcMotionState() {\n        if (this.nzOpen) {\n            this.expandState = 'expanded';\n        }\n        else {\n            this.expandState = 'collapsed';\n        }\n    }\n    ngOnInit() {\n        this.calcMotionState();\n        this.dir = this.directionality.value;\n        this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe((direction) => {\n            this.dir = direction;\n        });\n    }\n    ngOnChanges(changes) {\n        const { mode, nzOpen, menuClass } = changes;\n        if (mode || nzOpen) {\n            this.calcMotionState();\n        }\n        if (menuClass) {\n            if (this.listOfCacheClassName.length) {\n                this.listOfCacheClassName\n                    .filter(item => !!item)\n                    .forEach(className => {\n                    this.renderer.removeClass(this.elementRef.nativeElement, className);\n                });\n            }\n            if (this.menuClass) {\n                this.listOfCacheClassName = this.menuClass.split(' ');\n                this.listOfCacheClassName\n                    .filter(item => !!item)\n                    .forEach(className => {\n                    this.renderer.addClass(this.elementRef.nativeElement, className);\n                });\n            }\n        }\n    }\n    ngOnDestroy() {\n        this.destroy$.next();\n        this.destroy$.complete();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzSubmenuInlineChildComponent, deps: [{ token: i0.ElementRef }, { token: i0.Renderer2 }, { token: i1.Directionality, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzSubmenuInlineChildComponent, isStandalone: true, selector: \"[nz-submenu-inline-child]\", inputs: { templateOutlet: \"templateOutlet\", menuClass: \"menuClass\", mode: \"mode\", nzOpen: \"nzOpen\" }, host: { properties: { \"class.ant-menu-rtl\": \"dir === 'rtl'\", \"@collapseMotion\": \"expandState\" }, classAttribute: \"ant-menu ant-menu-inline ant-menu-sub\" }, exportAs: [\"nzSubmenuInlineChild\"], usesOnChanges: true, ngImport: i0, template: ` <ng-template [ngTemplateOutlet]=\"templateOutlet\"></ng-template> `, isInline: true, dependencies: [{ kind: \"directive\", type: NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }], animations: [collapseMotion], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzSubmenuInlineChildComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: '[nz-submenu-inline-child]',\n                    animations: [collapseMotion],\n                    exportAs: 'nzSubmenuInlineChild',\n                    encapsulation: ViewEncapsulation.None,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    template: ` <ng-template [ngTemplateOutlet]=\"templateOutlet\"></ng-template> `,\n                    host: {\n                        class: 'ant-menu ant-menu-inline ant-menu-sub',\n                        '[class.ant-menu-rtl]': `dir === 'rtl'`,\n                        '[@collapseMotion]': 'expandState'\n                    },\n                    imports: [NgTemplateOutlet],\n                    standalone: true\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: i0.Renderer2 }, { type: i1.Directionality, decorators: [{\n                    type: Optional\n                }] }], propDecorators: { templateOutlet: [{\n                type: Input\n            }], menuClass: [{\n                type: Input\n            }], mode: [{\n                type: Input\n            }], nzOpen: [{\n                type: Input\n            }] } });\n\nclass NzSubmenuNoneInlineChildComponent {\n    constructor(directionality) {\n        this.directionality = directionality;\n        this.menuClass = '';\n        this.theme = 'light';\n        this.templateOutlet = null;\n        this.isMenuInsideDropDown = false;\n        this.mode = 'vertical';\n        this.position = 'right';\n        this.nzDisabled = false;\n        this.nzOpen = false;\n        this.subMenuMouseState = new EventEmitter();\n        this.expandState = 'collapsed';\n        this.dir = 'ltr';\n        this.destroy$ = new Subject();\n    }\n    setMouseState(state) {\n        if (!this.nzDisabled) {\n            this.subMenuMouseState.next(state);\n        }\n    }\n    ngOnDestroy() {\n        this.destroy$.next();\n        this.destroy$.complete();\n    }\n    calcMotionState() {\n        if (this.nzOpen) {\n            if (this.mode === 'horizontal') {\n                this.expandState = 'bottom';\n            }\n            else if (this.mode === 'vertical') {\n                this.expandState = 'active';\n            }\n        }\n        else {\n            this.expandState = 'collapsed';\n        }\n    }\n    ngOnInit() {\n        this.calcMotionState();\n        this.dir = this.directionality.value;\n        this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe((direction) => {\n            this.dir = direction;\n        });\n    }\n    ngOnChanges(changes) {\n        const { mode, nzOpen } = changes;\n        if (mode || nzOpen) {\n            this.calcMotionState();\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzSubmenuNoneInlineChildComponent, deps: [{ token: i1.Directionality, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzSubmenuNoneInlineChildComponent, isStandalone: true, selector: \"[nz-submenu-none-inline-child]\", inputs: { menuClass: \"menuClass\", theme: \"theme\", templateOutlet: \"templateOutlet\", isMenuInsideDropDown: \"isMenuInsideDropDown\", mode: \"mode\", position: \"position\", nzDisabled: \"nzDisabled\", nzOpen: \"nzOpen\" }, outputs: { subMenuMouseState: \"subMenuMouseState\" }, host: { listeners: { \"mouseenter\": \"setMouseState(true)\", \"mouseleave\": \"setMouseState(false)\" }, properties: { \"class.ant-menu-light\": \"theme === 'light'\", \"class.ant-menu-dark\": \"theme === 'dark'\", \"class.ant-menu-submenu-placement-bottom\": \"mode === 'horizontal'\", \"class.ant-menu-submenu-placement-right\": \"mode === 'vertical' && position === 'right'\", \"class.ant-menu-submenu-placement-left\": \"mode === 'vertical' && position === 'left'\", \"class.ant-menu-submenu-rtl\": \"dir ===\\\"rtl\\\"\", \"@slideMotion\": \"expandState\", \"@zoomBigMotion\": \"expandState\" }, classAttribute: \"ant-menu-submenu ant-menu-submenu-popup\" }, exportAs: [\"nzSubmenuNoneInlineChild\"], usesOnChanges: true, ngImport: i0, template: `\n    <div\n      [class.ant-dropdown-menu]=\"isMenuInsideDropDown\"\n      [class.ant-menu]=\"!isMenuInsideDropDown\"\n      [class.ant-dropdown-menu-vertical]=\"isMenuInsideDropDown\"\n      [class.ant-menu-vertical]=\"!isMenuInsideDropDown\"\n      [class.ant-dropdown-menu-sub]=\"isMenuInsideDropDown\"\n      [class.ant-menu-sub]=\"!isMenuInsideDropDown\"\n      [class.ant-menu-rtl]=\"dir === 'rtl'\"\n      [ngClass]=\"menuClass\"\n    >\n      <ng-template [ngTemplateOutlet]=\"templateOutlet\"></ng-template>\n    </div>\n  `, isInline: true, dependencies: [{ kind: \"directive\", type: NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }], animations: [zoomBigMotion, slideMotion], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzSubmenuNoneInlineChildComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: '[nz-submenu-none-inline-child]',\n                    exportAs: 'nzSubmenuNoneInlineChild',\n                    encapsulation: ViewEncapsulation.None,\n                    animations: [zoomBigMotion, slideMotion],\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    template: `\n    <div\n      [class.ant-dropdown-menu]=\"isMenuInsideDropDown\"\n      [class.ant-menu]=\"!isMenuInsideDropDown\"\n      [class.ant-dropdown-menu-vertical]=\"isMenuInsideDropDown\"\n      [class.ant-menu-vertical]=\"!isMenuInsideDropDown\"\n      [class.ant-dropdown-menu-sub]=\"isMenuInsideDropDown\"\n      [class.ant-menu-sub]=\"!isMenuInsideDropDown\"\n      [class.ant-menu-rtl]=\"dir === 'rtl'\"\n      [ngClass]=\"menuClass\"\n    >\n      <ng-template [ngTemplateOutlet]=\"templateOutlet\"></ng-template>\n    </div>\n  `,\n                    host: {\n                        class: 'ant-menu-submenu ant-menu-submenu-popup',\n                        '[class.ant-menu-light]': \"theme === 'light'\",\n                        '[class.ant-menu-dark]': \"theme === 'dark'\",\n                        '[class.ant-menu-submenu-placement-bottom]': \"mode === 'horizontal'\",\n                        '[class.ant-menu-submenu-placement-right]': \"mode === 'vertical' && position === 'right'\",\n                        '[class.ant-menu-submenu-placement-left]': \"mode === 'vertical' && position === 'left'\",\n                        '[class.ant-menu-submenu-rtl]': 'dir ===\"rtl\"',\n                        '[@slideMotion]': 'expandState',\n                        '[@zoomBigMotion]': 'expandState',\n                        '(mouseenter)': 'setMouseState(true)',\n                        '(mouseleave)': 'setMouseState(false)'\n                    },\n                    imports: [NgClass, NgTemplateOutlet],\n                    standalone: true\n                }]\n        }], ctorParameters: () => [{ type: i1.Directionality, decorators: [{\n                    type: Optional\n                }] }], propDecorators: { menuClass: [{\n                type: Input\n            }], theme: [{\n                type: Input\n            }], templateOutlet: [{\n                type: Input\n            }], isMenuInsideDropDown: [{\n                type: Input\n            }], mode: [{\n                type: Input\n            }], position: [{\n                type: Input\n            }], nzDisabled: [{\n                type: Input\n            }], nzOpen: [{\n                type: Input\n            }], subMenuMouseState: [{\n                type: Output\n            }] } });\n\nclass NzSubMenuTitleComponent {\n    constructor(cdr, directionality) {\n        this.cdr = cdr;\n        this.directionality = directionality;\n        this.nzIcon = null;\n        this.nzTitle = null;\n        this.isMenuInsideDropDown = false;\n        this.nzDisabled = false;\n        this.paddingLeft = null;\n        this.mode = 'vertical';\n        this.toggleSubMenu = new EventEmitter();\n        this.subMenuMouseState = new EventEmitter();\n        this.dir = 'ltr';\n        this.destroy$ = new Subject();\n    }\n    ngOnInit() {\n        this.dir = this.directionality.value;\n        this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe((direction) => {\n            this.dir = direction;\n            this.cdr.detectChanges();\n        });\n    }\n    ngOnDestroy() {\n        this.destroy$.next();\n        this.destroy$.complete();\n    }\n    setMouseState(state) {\n        if (!this.nzDisabled) {\n            this.subMenuMouseState.next(state);\n        }\n    }\n    clickTitle() {\n        if (this.mode === 'inline' && !this.nzDisabled) {\n            this.toggleSubMenu.emit();\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzSubMenuTitleComponent, deps: [{ token: i0.ChangeDetectorRef }, { token: i1.Directionality, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"17.3.8\", type: NzSubMenuTitleComponent, isStandalone: true, selector: \"[nz-submenu-title]\", inputs: { nzIcon: \"nzIcon\", nzTitle: \"nzTitle\", isMenuInsideDropDown: \"isMenuInsideDropDown\", nzDisabled: \"nzDisabled\", paddingLeft: \"paddingLeft\", mode: \"mode\" }, outputs: { toggleSubMenu: \"toggleSubMenu\", subMenuMouseState: \"subMenuMouseState\" }, host: { listeners: { \"click\": \"clickTitle()\", \"mouseenter\": \"setMouseState(true)\", \"mouseleave\": \"setMouseState(false)\" }, properties: { \"class.ant-dropdown-menu-submenu-title\": \"isMenuInsideDropDown\", \"class.ant-menu-submenu-title\": \"!isMenuInsideDropDown\", \"style.paddingLeft.px\": \"dir === 'rtl' ? null : paddingLeft \", \"style.paddingRight.px\": \"dir === 'rtl' ? paddingLeft : null\" } }, exportAs: [\"nzSubmenuTitle\"], ngImport: i0, template: `\n    @if (nzIcon) {\n      <span nz-icon [nzType]=\"nzIcon\"></span>\n    }\n    <ng-container *nzStringTemplateOutlet=\"nzTitle\">\n      <span class=\"ant-menu-title-content\">{{ nzTitle }}</span>\n    </ng-container>\n    <ng-content />\n    @if (isMenuInsideDropDown) {\n      <span class=\"ant-dropdown-menu-submenu-expand-icon\">\n        @switch (dir) {\n          @case ('rtl') {\n            <span nz-icon nzType=\"left\" class=\"ant-dropdown-menu-submenu-arrow-icon\"></span>\n          }\n          @default {\n            <span nz-icon nzType=\"right\" class=\"ant-dropdown-menu-submenu-arrow-icon\"></span>\n          }\n        }\n      </span>\n    } @else {\n      <span class=\"ant-menu-submenu-arrow\"></span>\n    }\n  `, isInline: true, dependencies: [{ kind: \"ngmodule\", type: NzIconModule }, { kind: \"directive\", type: i2.NzIconDirective, selector: \"[nz-icon]\", inputs: [\"nzSpin\", \"nzRotate\", \"nzType\", \"nzTheme\", \"nzTwotoneColor\", \"nzIconfont\"], exportAs: [\"nzIcon\"] }, { kind: \"ngmodule\", type: NzOutletModule }, { kind: \"directive\", type: i3.NzStringTemplateOutletDirective, selector: \"[nzStringTemplateOutlet]\", inputs: [\"nzStringTemplateOutletContext\", \"nzStringTemplateOutlet\"], exportAs: [\"nzStringTemplateOutlet\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzSubMenuTitleComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: '[nz-submenu-title]',\n                    exportAs: 'nzSubmenuTitle',\n                    encapsulation: ViewEncapsulation.None,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    template: `\n    @if (nzIcon) {\n      <span nz-icon [nzType]=\"nzIcon\"></span>\n    }\n    <ng-container *nzStringTemplateOutlet=\"nzTitle\">\n      <span class=\"ant-menu-title-content\">{{ nzTitle }}</span>\n    </ng-container>\n    <ng-content />\n    @if (isMenuInsideDropDown) {\n      <span class=\"ant-dropdown-menu-submenu-expand-icon\">\n        @switch (dir) {\n          @case ('rtl') {\n            <span nz-icon nzType=\"left\" class=\"ant-dropdown-menu-submenu-arrow-icon\"></span>\n          }\n          @default {\n            <span nz-icon nzType=\"right\" class=\"ant-dropdown-menu-submenu-arrow-icon\"></span>\n          }\n        }\n      </span>\n    } @else {\n      <span class=\"ant-menu-submenu-arrow\"></span>\n    }\n  `,\n                    host: {\n                        '[class.ant-dropdown-menu-submenu-title]': 'isMenuInsideDropDown',\n                        '[class.ant-menu-submenu-title]': '!isMenuInsideDropDown',\n                        '[style.paddingLeft.px]': `dir === 'rtl' ? null : paddingLeft `,\n                        '[style.paddingRight.px]': `dir === 'rtl' ? paddingLeft : null`,\n                        '(click)': 'clickTitle()',\n                        '(mouseenter)': 'setMouseState(true)',\n                        '(mouseleave)': 'setMouseState(false)'\n                    },\n                    imports: [NzIconModule, NzOutletModule],\n                    standalone: true\n                }]\n        }], ctorParameters: () => [{ type: i0.ChangeDetectorRef }, { type: i1.Directionality, decorators: [{\n                    type: Optional\n                }] }], propDecorators: { nzIcon: [{\n                type: Input\n            }], nzTitle: [{\n                type: Input\n            }], isMenuInsideDropDown: [{\n                type: Input\n            }], nzDisabled: [{\n                type: Input\n            }], paddingLeft: [{\n                type: Input\n            }], mode: [{\n                type: Input\n            }], toggleSubMenu: [{\n                type: Output\n            }], subMenuMouseState: [{\n                type: Output\n            }] } });\n\nconst listOfVerticalPositions = [\n    POSITION_MAP.rightTop,\n    POSITION_MAP.right,\n    POSITION_MAP.rightBottom,\n    POSITION_MAP.leftTop,\n    POSITION_MAP.left,\n    POSITION_MAP.leftBottom\n];\nconst listOfHorizontalPositions = [\n    POSITION_MAP.bottomLeft,\n    POSITION_MAP.bottomRight,\n    POSITION_MAP.topRight,\n    POSITION_MAP.topLeft\n];\nclass NzSubMenuComponent {\n    /** set the submenu host open status directly **/\n    setOpenStateWithoutDebounce(open) {\n        this.nzSubmenuService.setOpenStateWithoutDebounce(open);\n    }\n    toggleSubMenu() {\n        this.setOpenStateWithoutDebounce(!this.nzOpen);\n    }\n    setMouseEnterState(value) {\n        this.isActive = value;\n        if (this.mode !== 'inline') {\n            this.nzSubmenuService.setMouseEnterTitleOrOverlayState(value);\n        }\n    }\n    setTriggerWidth() {\n        if (this.mode === 'horizontal' &&\n            this.platform.isBrowser &&\n            this.cdkOverlayOrigin &&\n            this.nzPlacement === 'bottomLeft') {\n            /** TODO: fast dom **/\n            this.triggerWidth = this.cdkOverlayOrigin.nativeElement.getBoundingClientRect().width;\n        }\n    }\n    onPositionChange(position) {\n        const placement = getPlacementName(position);\n        if (placement === 'rightTop' || placement === 'rightBottom' || placement === 'right') {\n            this.position = 'right';\n        }\n        else if (placement === 'leftTop' || placement === 'leftBottom' || placement === 'left') {\n            this.position = 'left';\n        }\n    }\n    constructor(nzMenuService, cdr, nzSubmenuService, platform, isMenuInsideDropDown, directionality, noAnimation) {\n        this.nzMenuService = nzMenuService;\n        this.cdr = cdr;\n        this.nzSubmenuService = nzSubmenuService;\n        this.platform = platform;\n        this.isMenuInsideDropDown = isMenuInsideDropDown;\n        this.directionality = directionality;\n        this.noAnimation = noAnimation;\n        this.nzMenuClassName = '';\n        this.nzPaddingLeft = null;\n        this.nzTitle = null;\n        this.nzIcon = null;\n        this.nzOpen = false;\n        this.nzDisabled = false;\n        this.nzPlacement = 'bottomLeft';\n        this.nzOpenChange = new EventEmitter();\n        this.cdkOverlayOrigin = null;\n        // fix errors about circular dependency\n        // Can't construct a query for the property ... since the query selector wasn't defined\"\n        this.listOfNzSubMenuComponent = null;\n        this.listOfNzMenuItemDirective = null;\n        this.level = this.nzSubmenuService.level;\n        this.destroy$ = new Subject();\n        this.position = 'right';\n        this.triggerWidth = null;\n        this.theme = 'light';\n        this.mode = 'vertical';\n        this.inlinePaddingLeft = null;\n        this.overlayPositions = listOfVerticalPositions;\n        this.isSelected = false;\n        this.isActive = false;\n        this.dir = 'ltr';\n    }\n    ngOnInit() {\n        /** submenu theme update **/\n        this.nzMenuService.theme$.pipe(takeUntil(this.destroy$)).subscribe(theme => {\n            this.theme = theme;\n            this.cdr.markForCheck();\n        });\n        /** submenu mode update **/\n        this.nzSubmenuService.mode$.pipe(takeUntil(this.destroy$)).subscribe(mode => {\n            this.mode = mode;\n            if (mode === 'horizontal') {\n                this.overlayPositions = [POSITION_MAP[this.nzPlacement], ...listOfHorizontalPositions];\n            }\n            else if (mode === 'vertical') {\n                this.overlayPositions = listOfVerticalPositions;\n            }\n            this.cdr.markForCheck();\n        });\n        /** inlineIndent update **/\n        combineLatest([this.nzSubmenuService.mode$, this.nzMenuService.inlineIndent$])\n            .pipe(takeUntil(this.destroy$))\n            .subscribe(([mode, inlineIndent]) => {\n            this.inlinePaddingLeft = mode === 'inline' ? this.level * inlineIndent : null;\n            this.cdr.markForCheck();\n        });\n        /** current submenu open status **/\n        this.nzSubmenuService.isCurrentSubMenuOpen$.pipe(takeUntil(this.destroy$)).subscribe(open => {\n            this.isActive = open;\n            if (open !== this.nzOpen) {\n                this.setTriggerWidth();\n                this.nzOpen = open;\n                this.nzOpenChange.emit(this.nzOpen);\n                this.cdr.markForCheck();\n            }\n        });\n        this.dir = this.directionality.value;\n        this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe((direction) => {\n            this.dir = direction;\n            this.cdr.markForCheck();\n        });\n    }\n    ngAfterContentInit() {\n        this.setTriggerWidth();\n        const listOfNzMenuItemDirective = this.listOfNzMenuItemDirective;\n        const changes = listOfNzMenuItemDirective.changes;\n        const mergedObservable = merge(...[changes, ...listOfNzMenuItemDirective.map(menu => menu.selected$)]);\n        changes\n            .pipe(startWith(listOfNzMenuItemDirective), switchMap(() => mergedObservable), startWith(true), map(() => listOfNzMenuItemDirective.some(e => e.nzSelected)), takeUntil(this.destroy$))\n            .subscribe(selected => {\n            this.isSelected = selected;\n            this.cdr.markForCheck();\n        });\n    }\n    ngOnChanges(changes) {\n        const { nzOpen } = changes;\n        if (nzOpen) {\n            this.nzSubmenuService.setOpenStateWithoutDebounce(this.nzOpen);\n            this.setTriggerWidth();\n        }\n    }\n    ngOnDestroy() {\n        this.destroy$.next();\n        this.destroy$.complete();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzSubMenuComponent, deps: [{ token: MenuService }, { token: i0.ChangeDetectorRef }, { token: NzSubmenuService }, { token: i3$1.Platform }, { token: NzIsMenuInsideDropDownToken }, { token: i1.Directionality, optional: true }, { token: i5.NzNoAnimationDirective, host: true, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"17.3.8\", type: NzSubMenuComponent, isStandalone: true, selector: \"[nz-submenu]\", inputs: { nzMenuClassName: \"nzMenuClassName\", nzPaddingLeft: \"nzPaddingLeft\", nzTitle: \"nzTitle\", nzIcon: \"nzIcon\", nzOpen: \"nzOpen\", nzDisabled: \"nzDisabled\", nzPlacement: \"nzPlacement\" }, outputs: { nzOpenChange: \"nzOpenChange\" }, host: { properties: { \"class.ant-dropdown-menu-submenu\": \"isMenuInsideDropDown\", \"class.ant-dropdown-menu-submenu-disabled\": \"isMenuInsideDropDown && nzDisabled\", \"class.ant-dropdown-menu-submenu-open\": \"isMenuInsideDropDown && nzOpen\", \"class.ant-dropdown-menu-submenu-selected\": \"isMenuInsideDropDown && isSelected\", \"class.ant-dropdown-menu-submenu-vertical\": \"isMenuInsideDropDown && mode === 'vertical'\", \"class.ant-dropdown-menu-submenu-horizontal\": \"isMenuInsideDropDown && mode === 'horizontal'\", \"class.ant-dropdown-menu-submenu-inline\": \"isMenuInsideDropDown && mode === 'inline'\", \"class.ant-dropdown-menu-submenu-active\": \"isMenuInsideDropDown && isActive\", \"class.ant-menu-submenu\": \"!isMenuInsideDropDown\", \"class.ant-menu-submenu-disabled\": \"!isMenuInsideDropDown && nzDisabled\", \"class.ant-menu-submenu-open\": \"!isMenuInsideDropDown && nzOpen\", \"class.ant-menu-submenu-selected\": \"!isMenuInsideDropDown && isSelected\", \"class.ant-menu-submenu-vertical\": \"!isMenuInsideDropDown && mode === 'vertical'\", \"class.ant-menu-submenu-horizontal\": \"!isMenuInsideDropDown && mode === 'horizontal'\", \"class.ant-menu-submenu-inline\": \"!isMenuInsideDropDown && mode === 'inline'\", \"class.ant-menu-submenu-active\": \"!isMenuInsideDropDown && isActive\", \"class.ant-menu-submenu-rtl\": \"dir === 'rtl'\" } }, providers: [NzSubmenuService], queries: [{ propertyName: \"listOfNzSubMenuComponent\", predicate: i0.forwardRef(() => NzSubMenuComponent), descendants: true }, { propertyName: \"listOfNzMenuItemDirective\", predicate: NzMenuItemComponent, descendants: true }], viewQueries: [{ propertyName: \"cdkOverlayOrigin\", first: true, predicate: CdkOverlayOrigin, descendants: true, read: ElementRef, static: true }], exportAs: [\"nzSubmenu\"], usesOnChanges: true, ngImport: i0, template: `\n    <div\n      nz-submenu-title\n      cdkOverlayOrigin\n      #origin=\"cdkOverlayOrigin\"\n      [nzIcon]=\"nzIcon\"\n      [nzTitle]=\"nzTitle\"\n      [mode]=\"mode\"\n      [nzDisabled]=\"nzDisabled\"\n      [isMenuInsideDropDown]=\"isMenuInsideDropDown\"\n      [paddingLeft]=\"nzPaddingLeft || inlinePaddingLeft\"\n      (subMenuMouseState)=\"setMouseEnterState($event)\"\n      (toggleSubMenu)=\"toggleSubMenu()\"\n    >\n      @if (!nzTitle) {\n        <ng-content select=\"[title]\" />\n      }\n    </div>\n    @if (mode === 'inline') {\n      <div\n        nz-submenu-inline-child\n        [mode]=\"mode\"\n        [nzOpen]=\"nzOpen\"\n        [@.disabled]=\"!!noAnimation?.nzNoAnimation\"\n        [nzNoAnimation]=\"noAnimation?.nzNoAnimation\"\n        [menuClass]=\"nzMenuClassName\"\n        [templateOutlet]=\"subMenuTemplate\"\n      ></div>\n    } @else {\n      <ng-template\n        cdkConnectedOverlay\n        (positionChange)=\"onPositionChange($event)\"\n        [cdkConnectedOverlayPositions]=\"overlayPositions\"\n        [cdkConnectedOverlayOrigin]=\"origin\"\n        [cdkConnectedOverlayWidth]=\"triggerWidth!\"\n        [cdkConnectedOverlayOpen]=\"nzOpen\"\n        [cdkConnectedOverlayTransformOriginOn]=\"'.ant-menu-submenu'\"\n      >\n        <div\n          nz-submenu-none-inline-child\n          [theme]=\"theme\"\n          [mode]=\"mode\"\n          [nzOpen]=\"nzOpen\"\n          [position]=\"position\"\n          [nzDisabled]=\"nzDisabled\"\n          [isMenuInsideDropDown]=\"isMenuInsideDropDown\"\n          [templateOutlet]=\"subMenuTemplate\"\n          [menuClass]=\"nzMenuClassName\"\n          [@.disabled]=\"!!noAnimation?.nzNoAnimation\"\n          [nzNoAnimation]=\"noAnimation?.nzNoAnimation\"\n          (subMenuMouseState)=\"setMouseEnterState($event)\"\n        ></div>\n      </ng-template>\n    }\n\n    <ng-template #subMenuTemplate>\n      <ng-content />\n    </ng-template>\n  `, isInline: true, dependencies: [{ kind: \"component\", type: NzSubMenuTitleComponent, selector: \"[nz-submenu-title]\", inputs: [\"nzIcon\", \"nzTitle\", \"isMenuInsideDropDown\", \"nzDisabled\", \"paddingLeft\", \"mode\"], outputs: [\"toggleSubMenu\", \"subMenuMouseState\"], exportAs: [\"nzSubmenuTitle\"] }, { kind: \"component\", type: NzSubmenuInlineChildComponent, selector: \"[nz-submenu-inline-child]\", inputs: [\"templateOutlet\", \"menuClass\", \"mode\", \"nzOpen\"], exportAs: [\"nzSubmenuInlineChild\"] }, { kind: \"directive\", type: NzNoAnimationDirective, selector: \"[nzNoAnimation]\", inputs: [\"nzNoAnimation\"], exportAs: [\"nzNoAnimation\"] }, { kind: \"component\", type: NzSubmenuNoneInlineChildComponent, selector: \"[nz-submenu-none-inline-child]\", inputs: [\"menuClass\", \"theme\", \"templateOutlet\", \"isMenuInsideDropDown\", \"mode\", \"position\", \"nzDisabled\", \"nzOpen\"], outputs: [\"subMenuMouseState\"], exportAs: [\"nzSubmenuNoneInlineChild\"] }, { kind: \"ngmodule\", type: OverlayModule }, { kind: \"directive\", type: i6.CdkConnectedOverlay, selector: \"[cdk-connected-overlay], [connected-overlay], [cdkConnectedOverlay]\", inputs: [\"cdkConnectedOverlayOrigin\", \"cdkConnectedOverlayPositions\", \"cdkConnectedOverlayPositionStrategy\", \"cdkConnectedOverlayOffsetX\", \"cdkConnectedOverlayOffsetY\", \"cdkConnectedOverlayWidth\", \"cdkConnectedOverlayHeight\", \"cdkConnectedOverlayMinWidth\", \"cdkConnectedOverlayMinHeight\", \"cdkConnectedOverlayBackdropClass\", \"cdkConnectedOverlayPanelClass\", \"cdkConnectedOverlayViewportMargin\", \"cdkConnectedOverlayScrollStrategy\", \"cdkConnectedOverlayOpen\", \"cdkConnectedOverlayDisableClose\", \"cdkConnectedOverlayTransformOriginOn\", \"cdkConnectedOverlayHasBackdrop\", \"cdkConnectedOverlayLockPosition\", \"cdkConnectedOverlayFlexibleDimensions\", \"cdkConnectedOverlayGrowAfterOpen\", \"cdkConnectedOverlayPush\", \"cdkConnectedOverlayDisposeOnNavigation\"], outputs: [\"backdropClick\", \"positionChange\", \"attach\", \"detach\", \"overlayKeydown\", \"overlayOutsideClick\"], exportAs: [\"cdkConnectedOverlay\"] }, { kind: \"directive\", type: i6.CdkOverlayOrigin, selector: \"[cdk-overlay-origin], [overlay-origin], [cdkOverlayOrigin]\", exportAs: [\"cdkOverlayOrigin\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\n__decorate([\n    InputBoolean()\n], NzSubMenuComponent.prototype, \"nzOpen\", void 0);\n__decorate([\n    InputBoolean()\n], NzSubMenuComponent.prototype, \"nzDisabled\", void 0);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzSubMenuComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: '[nz-submenu]',\n                    exportAs: 'nzSubmenu',\n                    providers: [NzSubmenuService],\n                    encapsulation: ViewEncapsulation.None,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    preserveWhitespaces: false,\n                    template: `\n    <div\n      nz-submenu-title\n      cdkOverlayOrigin\n      #origin=\"cdkOverlayOrigin\"\n      [nzIcon]=\"nzIcon\"\n      [nzTitle]=\"nzTitle\"\n      [mode]=\"mode\"\n      [nzDisabled]=\"nzDisabled\"\n      [isMenuInsideDropDown]=\"isMenuInsideDropDown\"\n      [paddingLeft]=\"nzPaddingLeft || inlinePaddingLeft\"\n      (subMenuMouseState)=\"setMouseEnterState($event)\"\n      (toggleSubMenu)=\"toggleSubMenu()\"\n    >\n      @if (!nzTitle) {\n        <ng-content select=\"[title]\" />\n      }\n    </div>\n    @if (mode === 'inline') {\n      <div\n        nz-submenu-inline-child\n        [mode]=\"mode\"\n        [nzOpen]=\"nzOpen\"\n        [@.disabled]=\"!!noAnimation?.nzNoAnimation\"\n        [nzNoAnimation]=\"noAnimation?.nzNoAnimation\"\n        [menuClass]=\"nzMenuClassName\"\n        [templateOutlet]=\"subMenuTemplate\"\n      ></div>\n    } @else {\n      <ng-template\n        cdkConnectedOverlay\n        (positionChange)=\"onPositionChange($event)\"\n        [cdkConnectedOverlayPositions]=\"overlayPositions\"\n        [cdkConnectedOverlayOrigin]=\"origin\"\n        [cdkConnectedOverlayWidth]=\"triggerWidth!\"\n        [cdkConnectedOverlayOpen]=\"nzOpen\"\n        [cdkConnectedOverlayTransformOriginOn]=\"'.ant-menu-submenu'\"\n      >\n        <div\n          nz-submenu-none-inline-child\n          [theme]=\"theme\"\n          [mode]=\"mode\"\n          [nzOpen]=\"nzOpen\"\n          [position]=\"position\"\n          [nzDisabled]=\"nzDisabled\"\n          [isMenuInsideDropDown]=\"isMenuInsideDropDown\"\n          [templateOutlet]=\"subMenuTemplate\"\n          [menuClass]=\"nzMenuClassName\"\n          [@.disabled]=\"!!noAnimation?.nzNoAnimation\"\n          [nzNoAnimation]=\"noAnimation?.nzNoAnimation\"\n          (subMenuMouseState)=\"setMouseEnterState($event)\"\n        ></div>\n      </ng-template>\n    }\n\n    <ng-template #subMenuTemplate>\n      <ng-content />\n    </ng-template>\n  `,\n                    host: {\n                        '[class.ant-dropdown-menu-submenu]': `isMenuInsideDropDown`,\n                        '[class.ant-dropdown-menu-submenu-disabled]': `isMenuInsideDropDown && nzDisabled`,\n                        '[class.ant-dropdown-menu-submenu-open]': `isMenuInsideDropDown && nzOpen`,\n                        '[class.ant-dropdown-menu-submenu-selected]': `isMenuInsideDropDown && isSelected`,\n                        '[class.ant-dropdown-menu-submenu-vertical]': `isMenuInsideDropDown && mode === 'vertical'`,\n                        '[class.ant-dropdown-menu-submenu-horizontal]': `isMenuInsideDropDown && mode === 'horizontal'`,\n                        '[class.ant-dropdown-menu-submenu-inline]': `isMenuInsideDropDown && mode === 'inline'`,\n                        '[class.ant-dropdown-menu-submenu-active]': `isMenuInsideDropDown && isActive`,\n                        '[class.ant-menu-submenu]': `!isMenuInsideDropDown`,\n                        '[class.ant-menu-submenu-disabled]': `!isMenuInsideDropDown && nzDisabled`,\n                        '[class.ant-menu-submenu-open]': `!isMenuInsideDropDown && nzOpen`,\n                        '[class.ant-menu-submenu-selected]': `!isMenuInsideDropDown && isSelected`,\n                        '[class.ant-menu-submenu-vertical]': `!isMenuInsideDropDown && mode === 'vertical'`,\n                        '[class.ant-menu-submenu-horizontal]': `!isMenuInsideDropDown && mode === 'horizontal'`,\n                        '[class.ant-menu-submenu-inline]': `!isMenuInsideDropDown && mode === 'inline'`,\n                        '[class.ant-menu-submenu-active]': `!isMenuInsideDropDown && isActive`,\n                        '[class.ant-menu-submenu-rtl]': `dir === 'rtl'`\n                    },\n                    imports: [\n                        NzSubMenuTitleComponent,\n                        NzSubmenuInlineChildComponent,\n                        NzNoAnimationDirective,\n                        NzSubmenuNoneInlineChildComponent,\n                        OverlayModule\n                    ],\n                    standalone: true\n                }]\n        }], ctorParameters: () => [{ type: MenuService }, { type: i0.ChangeDetectorRef }, { type: NzSubmenuService }, { type: i3$1.Platform }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [NzIsMenuInsideDropDownToken]\n                }] }, { type: i1.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i5.NzNoAnimationDirective, decorators: [{\n                    type: Host\n                }, {\n                    type: Optional\n                }] }], propDecorators: { nzMenuClassName: [{\n                type: Input\n            }], nzPaddingLeft: [{\n                type: Input\n            }], nzTitle: [{\n                type: Input\n            }], nzIcon: [{\n                type: Input\n            }], nzOpen: [{\n                type: Input\n            }], nzDisabled: [{\n                type: Input\n            }], nzPlacement: [{\n                type: Input\n            }], nzOpenChange: [{\n                type: Output\n            }], cdkOverlayOrigin: [{\n                type: ViewChild,\n                args: [CdkOverlayOrigin, { static: true, read: ElementRef }]\n            }], listOfNzSubMenuComponent: [{\n                type: ContentChildren,\n                args: [forwardRef(() => NzSubMenuComponent), { descendants: true }]\n            }], listOfNzMenuItemDirective: [{\n                type: ContentChildren,\n                args: [NzMenuItemComponent, { descendants: true }]\n            }] } });\n\nfunction MenuServiceFactory() {\n    const serviceInsideDropDown = inject(MenuService, { skipSelf: true, optional: true });\n    const serviceOutsideDropDown = inject(NzMenuServiceLocalToken);\n    return serviceInsideDropDown ?? serviceOutsideDropDown;\n}\nfunction MenuDropDownTokenFactory() {\n    const isMenuInsideDropDownToken = inject(NzIsMenuInsideDropDownToken, { skipSelf: true, optional: true });\n    return isMenuInsideDropDownToken ?? false;\n}\nclass NzMenuDirective {\n    setInlineCollapsed(inlineCollapsed) {\n        this.nzInlineCollapsed = inlineCollapsed;\n        this.inlineCollapsed$.next(inlineCollapsed);\n    }\n    updateInlineCollapse() {\n        if (this.listOfNzMenuItemDirective) {\n            if (this.nzInlineCollapsed) {\n                this.listOfOpenedNzSubMenuComponent = this.listOfNzSubMenuComponent.filter(submenu => submenu.nzOpen);\n                this.listOfNzSubMenuComponent.forEach(submenu => submenu.setOpenStateWithoutDebounce(false));\n            }\n            else {\n                this.listOfOpenedNzSubMenuComponent.forEach(submenu => submenu.setOpenStateWithoutDebounce(true));\n                this.listOfOpenedNzSubMenuComponent = [];\n            }\n        }\n    }\n    constructor(nzMenuService, isMenuInsideDropDown, cdr, directionality) {\n        this.nzMenuService = nzMenuService;\n        this.isMenuInsideDropDown = isMenuInsideDropDown;\n        this.cdr = cdr;\n        this.directionality = directionality;\n        this.nzInlineIndent = 24;\n        this.nzTheme = 'light';\n        this.nzMode = 'vertical';\n        this.nzInlineCollapsed = false;\n        this.nzSelectable = !this.isMenuInsideDropDown;\n        this.nzClick = new EventEmitter();\n        this.actualMode = 'vertical';\n        this.dir = 'ltr';\n        this.inlineCollapsed$ = new BehaviorSubject(this.nzInlineCollapsed);\n        this.mode$ = new BehaviorSubject(this.nzMode);\n        this.destroy$ = new Subject();\n        this.listOfOpenedNzSubMenuComponent = [];\n    }\n    ngOnInit() {\n        combineLatest([this.inlineCollapsed$, this.mode$])\n            .pipe(takeUntil(this.destroy$))\n            .subscribe(([inlineCollapsed, mode]) => {\n            this.actualMode = inlineCollapsed ? 'vertical' : mode;\n            this.nzMenuService.setMode(this.actualMode);\n            this.cdr.markForCheck();\n        });\n        this.nzMenuService.descendantMenuItemClick$.pipe(takeUntil(this.destroy$)).subscribe(menu => {\n            this.nzClick.emit(menu);\n            if (this.nzSelectable && !menu.nzMatchRouter) {\n                this.listOfNzMenuItemDirective.forEach(item => item.setSelectedState(item === menu));\n            }\n        });\n        this.dir = this.directionality.value;\n        this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe((direction) => {\n            this.dir = direction;\n            this.nzMenuService.setMode(this.actualMode);\n            this.cdr.markForCheck();\n        });\n    }\n    ngAfterContentInit() {\n        this.inlineCollapsed$.pipe(takeUntil(this.destroy$)).subscribe(() => {\n            this.updateInlineCollapse();\n            this.cdr.markForCheck();\n        });\n    }\n    ngOnChanges(changes) {\n        const { nzInlineCollapsed, nzInlineIndent, nzTheme, nzMode } = changes;\n        if (nzInlineCollapsed) {\n            this.inlineCollapsed$.next(this.nzInlineCollapsed);\n        }\n        if (nzInlineIndent) {\n            this.nzMenuService.setInlineIndent(this.nzInlineIndent);\n        }\n        if (nzTheme) {\n            this.nzMenuService.setTheme(this.nzTheme);\n        }\n        if (nzMode) {\n            this.mode$.next(this.nzMode);\n            if (!changes.nzMode.isFirstChange() && this.listOfNzSubMenuComponent) {\n                this.listOfNzSubMenuComponent.forEach(submenu => submenu.setOpenStateWithoutDebounce(false));\n            }\n        }\n    }\n    ngOnDestroy() {\n        this.destroy$.next(true);\n        this.destroy$.complete();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzMenuDirective, deps: [{ token: MenuService }, { token: NzIsMenuInsideDropDownToken }, { token: i0.ChangeDetectorRef }, { token: i1.Directionality, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzMenuDirective, isStandalone: true, selector: \"[nz-menu]\", inputs: { nzInlineIndent: \"nzInlineIndent\", nzTheme: \"nzTheme\", nzMode: \"nzMode\", nzInlineCollapsed: \"nzInlineCollapsed\", nzSelectable: \"nzSelectable\" }, outputs: { nzClick: \"nzClick\" }, host: { properties: { \"class.ant-dropdown-menu\": \"isMenuInsideDropDown\", \"class.ant-dropdown-menu-root\": \"isMenuInsideDropDown\", \"class.ant-dropdown-menu-light\": \"isMenuInsideDropDown && nzTheme === 'light'\", \"class.ant-dropdown-menu-dark\": \"isMenuInsideDropDown && nzTheme === 'dark'\", \"class.ant-dropdown-menu-vertical\": \"isMenuInsideDropDown && actualMode === 'vertical'\", \"class.ant-dropdown-menu-horizontal\": \"isMenuInsideDropDown && actualMode === 'horizontal'\", \"class.ant-dropdown-menu-inline\": \"isMenuInsideDropDown && actualMode === 'inline'\", \"class.ant-dropdown-menu-inline-collapsed\": \"isMenuInsideDropDown && nzInlineCollapsed\", \"class.ant-menu\": \"!isMenuInsideDropDown\", \"class.ant-menu-root\": \"!isMenuInsideDropDown\", \"class.ant-menu-light\": \"!isMenuInsideDropDown && nzTheme === 'light'\", \"class.ant-menu-dark\": \"!isMenuInsideDropDown && nzTheme === 'dark'\", \"class.ant-menu-vertical\": \"!isMenuInsideDropDown && actualMode === 'vertical'\", \"class.ant-menu-horizontal\": \"!isMenuInsideDropDown && actualMode === 'horizontal'\", \"class.ant-menu-inline\": \"!isMenuInsideDropDown && actualMode === 'inline'\", \"class.ant-menu-inline-collapsed\": \"!isMenuInsideDropDown && nzInlineCollapsed\", \"class.ant-menu-rtl\": \"dir === 'rtl'\" } }, providers: [\n            {\n                provide: NzMenuServiceLocalToken,\n                useClass: MenuService\n            },\n            /** use the top level service **/\n            {\n                provide: MenuService,\n                useFactory: MenuServiceFactory\n            },\n            /** check if menu inside dropdown-menu component **/\n            {\n                provide: NzIsMenuInsideDropDownToken,\n                useFactory: MenuDropDownTokenFactory\n            }\n        ], queries: [{ propertyName: \"listOfNzMenuItemDirective\", predicate: NzMenuItemComponent, descendants: true }, { propertyName: \"listOfNzSubMenuComponent\", predicate: NzSubMenuComponent, descendants: true }], exportAs: [\"nzMenu\"], usesOnChanges: true, ngImport: i0 }); }\n}\n__decorate([\n    InputBoolean()\n], NzMenuDirective.prototype, \"nzInlineCollapsed\", void 0);\n__decorate([\n    InputBoolean()\n], NzMenuDirective.prototype, \"nzSelectable\", void 0);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzMenuDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[nz-menu]',\n                    exportAs: 'nzMenu',\n                    providers: [\n                        {\n                            provide: NzMenuServiceLocalToken,\n                            useClass: MenuService\n                        },\n                        /** use the top level service **/\n                        {\n                            provide: MenuService,\n                            useFactory: MenuServiceFactory\n                        },\n                        /** check if menu inside dropdown-menu component **/\n                        {\n                            provide: NzIsMenuInsideDropDownToken,\n                            useFactory: MenuDropDownTokenFactory\n                        }\n                    ],\n                    host: {\n                        '[class.ant-dropdown-menu]': `isMenuInsideDropDown`,\n                        '[class.ant-dropdown-menu-root]': `isMenuInsideDropDown`,\n                        '[class.ant-dropdown-menu-light]': `isMenuInsideDropDown && nzTheme === 'light'`,\n                        '[class.ant-dropdown-menu-dark]': `isMenuInsideDropDown && nzTheme === 'dark'`,\n                        '[class.ant-dropdown-menu-vertical]': `isMenuInsideDropDown && actualMode === 'vertical'`,\n                        '[class.ant-dropdown-menu-horizontal]': `isMenuInsideDropDown && actualMode === 'horizontal'`,\n                        '[class.ant-dropdown-menu-inline]': `isMenuInsideDropDown && actualMode === 'inline'`,\n                        '[class.ant-dropdown-menu-inline-collapsed]': `isMenuInsideDropDown && nzInlineCollapsed`,\n                        '[class.ant-menu]': `!isMenuInsideDropDown`,\n                        '[class.ant-menu-root]': `!isMenuInsideDropDown`,\n                        '[class.ant-menu-light]': `!isMenuInsideDropDown && nzTheme === 'light'`,\n                        '[class.ant-menu-dark]': `!isMenuInsideDropDown && nzTheme === 'dark'`,\n                        '[class.ant-menu-vertical]': `!isMenuInsideDropDown && actualMode === 'vertical'`,\n                        '[class.ant-menu-horizontal]': `!isMenuInsideDropDown && actualMode === 'horizontal'`,\n                        '[class.ant-menu-inline]': `!isMenuInsideDropDown && actualMode === 'inline'`,\n                        '[class.ant-menu-inline-collapsed]': `!isMenuInsideDropDown && nzInlineCollapsed`,\n                        '[class.ant-menu-rtl]': `dir === 'rtl'`\n                    },\n                    standalone: true\n                }]\n        }], ctorParameters: () => [{ type: MenuService }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [NzIsMenuInsideDropDownToken]\n                }] }, { type: i0.ChangeDetectorRef }, { type: i1.Directionality, decorators: [{\n                    type: Optional\n                }] }], propDecorators: { listOfNzMenuItemDirective: [{\n                type: ContentChildren,\n                args: [NzMenuItemComponent, { descendants: true }]\n            }], listOfNzSubMenuComponent: [{\n                type: ContentChildren,\n                args: [NzSubMenuComponent, { descendants: true }]\n            }], nzInlineIndent: [{\n                type: Input\n            }], nzTheme: [{\n                type: Input\n            }], nzMode: [{\n                type: Input\n            }], nzInlineCollapsed: [{\n                type: Input\n            }], nzSelectable: [{\n                type: Input\n            }], nzClick: [{\n                type: Output\n            }] } });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction MenuGroupFactory() {\n    const isMenuInsideDropDownToken = inject(NzIsMenuInsideDropDownToken, { optional: true, skipSelf: true });\n    return isMenuInsideDropDownToken ?? false;\n}\nclass NzMenuGroupComponent {\n    constructor(elementRef, renderer, isMenuInsideDropDown) {\n        this.elementRef = elementRef;\n        this.renderer = renderer;\n        this.isMenuInsideDropDown = isMenuInsideDropDown;\n        const className = this.isMenuInsideDropDown ? 'ant-dropdown-menu-item-group' : 'ant-menu-item-group';\n        this.renderer.addClass(elementRef.nativeElement, className);\n    }\n    ngAfterViewInit() {\n        const ulElement = this.titleElement.nativeElement.nextElementSibling;\n        if (ulElement) {\n            /** add classname to ul **/\n            const className = this.isMenuInsideDropDown ? 'ant-dropdown-menu-item-group-list' : 'ant-menu-item-group-list';\n            this.renderer.addClass(ulElement, className);\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzMenuGroupComponent, deps: [{ token: i0.ElementRef }, { token: i0.Renderer2 }, { token: NzIsMenuInsideDropDownToken }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"17.3.8\", type: NzMenuGroupComponent, isStandalone: true, selector: \"[nz-menu-group]\", inputs: { nzTitle: \"nzTitle\" }, providers: [\n            /** check if menu inside dropdown-menu component **/\n            {\n                provide: NzIsMenuInsideDropDownToken,\n                useFactory: MenuGroupFactory\n            }\n        ], viewQueries: [{ propertyName: \"titleElement\", first: true, predicate: [\"titleElement\"], descendants: true }], exportAs: [\"nzMenuGroup\"], ngImport: i0, template: `\n    <div\n      [class.ant-menu-item-group-title]=\"!isMenuInsideDropDown\"\n      [class.ant-dropdown-menu-item-group-title]=\"isMenuInsideDropDown\"\n      #titleElement\n    >\n      <ng-container *nzStringTemplateOutlet=\"nzTitle\">{{ nzTitle }}</ng-container>\n      @if (!nzTitle) {\n        <ng-content select=\"[title]\" />\n      }\n    </div>\n    <ng-content></ng-content>\n  `, isInline: true, dependencies: [{ kind: \"ngmodule\", type: NzOutletModule }, { kind: \"directive\", type: i3.NzStringTemplateOutletDirective, selector: \"[nzStringTemplateOutlet]\", inputs: [\"nzStringTemplateOutletContext\", \"nzStringTemplateOutlet\"], exportAs: [\"nzStringTemplateOutlet\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzMenuGroupComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: '[nz-menu-group]',\n                    exportAs: 'nzMenuGroup',\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    providers: [\n                        /** check if menu inside dropdown-menu component **/\n                        {\n                            provide: NzIsMenuInsideDropDownToken,\n                            useFactory: MenuGroupFactory\n                        }\n                    ],\n                    encapsulation: ViewEncapsulation.None,\n                    template: `\n    <div\n      [class.ant-menu-item-group-title]=\"!isMenuInsideDropDown\"\n      [class.ant-dropdown-menu-item-group-title]=\"isMenuInsideDropDown\"\n      #titleElement\n    >\n      <ng-container *nzStringTemplateOutlet=\"nzTitle\">{{ nzTitle }}</ng-container>\n      @if (!nzTitle) {\n        <ng-content select=\"[title]\" />\n      }\n    </div>\n    <ng-content></ng-content>\n  `,\n                    preserveWhitespaces: false,\n                    imports: [NzOutletModule],\n                    standalone: true\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: i0.Renderer2 }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [NzIsMenuInsideDropDownToken]\n                }] }], propDecorators: { nzTitle: [{\n                type: Input\n            }], titleElement: [{\n                type: ViewChild,\n                args: ['titleElement']\n            }] } });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzMenuDividerDirective {\n    constructor(elementRef) {\n        this.elementRef = elementRef;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzMenuDividerDirective, deps: [{ token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzMenuDividerDirective, isStandalone: true, selector: \"[nz-menu-divider]\", host: { classAttribute: \"ant-dropdown-menu-item-divider\" }, exportAs: [\"nzMenuDivider\"], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzMenuDividerDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[nz-menu-divider]',\n                    exportAs: 'nzMenuDivider',\n                    host: {\n                        class: 'ant-dropdown-menu-item-divider'\n                    },\n                    standalone: true\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }] });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzMenuModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzMenuModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.3.8\", ngImport: i0, type: NzMenuModule, imports: [NzMenuDirective,\n            NzMenuItemComponent,\n            NzSubMenuComponent,\n            NzMenuDividerDirective,\n            NzMenuGroupComponent,\n            NzSubMenuTitleComponent,\n            NzSubmenuInlineChildComponent,\n            NzSubmenuNoneInlineChildComponent], exports: [NzMenuDirective, NzMenuItemComponent, NzSubMenuComponent, NzMenuDividerDirective, NzMenuGroupComponent] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzMenuModule, imports: [NzSubMenuComponent,\n            NzMenuGroupComponent,\n            NzSubMenuTitleComponent] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzMenuModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        NzMenuDirective,\n                        NzMenuItemComponent,\n                        NzSubMenuComponent,\n                        NzMenuDividerDirective,\n                        NzMenuGroupComponent,\n                        NzSubMenuTitleComponent,\n                        NzSubmenuInlineChildComponent,\n                        NzSubmenuNoneInlineChildComponent\n                    ],\n                    exports: [NzMenuDirective, NzMenuItemComponent, NzSubMenuComponent, NzMenuDividerDirective, NzMenuGroupComponent]\n                }]\n        }] });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MenuDropDownTokenFactory, MenuGroupFactory, MenuService, MenuServiceFactory, NzIsMenuInsideDropDownToken, NzMenuDirective, NzMenuDividerDirective, NzMenuGroupComponent, NzMenuItemComponent, NzMenuModule, NzMenuServiceLocalToken, NzSubMenuComponent, NzSubMenuTitleComponent, NzSubmenuInlineChildComponent, NzSubmenuNoneInlineChildComponent, NzSubmenuService };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,KAAK,EAAEC,eAAe,EAAEC,YAAY,EAAEC,MAAM,EAAEC,UAAU,EAAEC,IAAI,EAAEC,SAAS,EAAEC,UAAU,EAAEC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AACjQ,SAASC,OAAO,EAAEC,eAAe,EAAEC,KAAK,EAAEC,aAAa,QAAQ,MAAM;AACrE,SAASC,GAAG,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAEC,SAAS,EAAEC,oBAAoB,EAAEC,SAAS,EAAEC,SAAS,EAAEC,SAAS,QAAQ,gBAAgB;AAC/H,SAASC,YAAY,QAAQ,yBAAyB;AACtD,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,aAAa,EAAEC,UAAU,QAAQ,iBAAiB;AAC3D,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,OAAO,KAAKC,EAAE,MAAM,sBAAsB;AAC1C,SAASC,gBAAgB,EAAEC,aAAa,QAAQ,sBAAsB;AACtE,OAAO,KAAKC,EAAE,MAAM,iCAAiC;AACrD,SAASC,sBAAsB,QAAQ,iCAAiC;AACxE,SAASC,YAAY,EAAEC,gBAAgB,QAAQ,4BAA4B;AAC3E,SAASC,gBAAgB,EAAEC,OAAO,QAAQ,iBAAiB;AAC3D,SAASC,cAAc,EAAEC,aAAa,EAAEC,WAAW,QAAQ,8BAA8B;AACzF,OAAO,KAAKC,EAAE,MAAM,2BAA2B;AAC/C,SAASC,cAAc,QAAQ,2BAA2B;AAC1D,OAAO,KAAKC,EAAE,MAAM,oBAAoB;AACxC,SAASC,YAAY,QAAQ,oBAAoB;AACjD,OAAO,KAAKC,IAAI,MAAM,uBAAuB;;AAE7C;AACA;AACA;AACA;AAHA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,qDAAAC,EAAA,EAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,yDAAAH,EAAA,EAAAC,GAAA;AAAA,MAAAG,GAAA;AAAA,SAAAC,+CAAAL,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAqCoG3D,EAAE,CAAAiE,SAAA,aAmgB1D,CAAC;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAO,MAAA,GAngBuDlE,EAAE,CAAAmE,aAAA;IAAFnE,EAAE,CAAAoE,UAAA,WAAAF,MAAA,CAAAG,MAmgBlE,CAAC;EAAA;AAAA;AAAA,SAAAC,gDAAAX,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAngB+D3D,EAAE,CAAAuE,uBAAA,EAqgBnD,CAAC;IArgBgDvE,EAAE,CAAAwE,cAAA,aAsgB5D,CAAC;IAtgByDxE,EAAE,CAAAyE,MAAA,EAsgB/C,CAAC;IAtgB4CzE,EAAE,CAAA0E,YAAA,CAsgBxC,CAAC;IAtgBqC1E,EAAE,CAAA2E,qBAAA;EAAA;EAAA,IAAAhB,EAAA;IAAA,MAAAO,MAAA,GAAFlE,EAAE,CAAAmE,aAAA;IAAFnE,EAAE,CAAA4E,SAAA,EAsgB/C,CAAC;IAtgB4C5E,EAAE,CAAA6E,iBAAA,CAAAX,MAAA,CAAAY,OAsgB/C,CAAC;EAAA;AAAA;AAAA,SAAAC,sDAAApB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAtgB4C3D,EAAE,CAAAiE,SAAA,aA6gBX,CAAC;EAAA;AAAA;AAAA,SAAAe,sDAAArB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7gBQ3D,EAAE,CAAAiE,SAAA,aAghBV,CAAC;EAAA;AAAA;AAAA,SAAAgB,+CAAAtB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAhhBO3D,EAAE,CAAAwE,cAAA,aA0gB7C,CAAC;IA1gB0CxE,EAAE,CAAAkF,UAAA,IAAAH,qDAAA,MA4gB9E,CAAC,IAAAC,qDAAA,MAGN,CAAC;IA/gBgFhF,EAAE,CAAA0E,YAAA,CAmhB1F,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,IAAAwB,OAAA;IAAA,MAAAjB,MAAA,GAnhBuFlE,EAAE,CAAAmE,aAAA;IAAFnE,EAAE,CAAA4E,SAAA,CAkhB9F,CAAC;IAlhB2F5E,EAAE,CAAAoF,aAAA,KAAAD,OAAA,GAAAjB,MAAA,CAAAmB,GAAA,MA2gB9F,KAAK,QAOL,CAAC;EAAA;AAAA;AAAA,SAAAC,+CAAA3B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlhB2F3D,EAAE,CAAAiE,SAAA,aAqhBrD,CAAC;EAAA;AAAA;AAAA,MAAAsB,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,0CAAA/B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArhBkD3D,EAAE,CAAA2F,YAAA,EAqvBhE,CAAC;EAAA;AAAA;AAAA,SAAAC,0CAAAjC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArvB6D3D,EAAE,CAAAiE,SAAA,YAiwB1F,CAAC;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAkC,MAAA,GAjwBuF7F,EAAE,CAAAmE,aAAA;IAAA,MAAA2B,kBAAA,GAAF9F,EAAE,CAAA+F,WAAA;IAAF/F,EAAE,CAAAoE,UAAA,SAAAyB,MAAA,CAAAG,IA2vBlF,CAAC,WAAAH,MAAA,CAAAI,MACG,CAAC,kBAAAJ,MAAA,CAAAK,WAAA,kBAAAL,MAAA,CAAAK,WAAA,CAAAC,aAAA,CACyB,CAAC,kBAAAN,MAAA,CAAAK,WAAA,kBAAAL,MAAA,CAAAK,WAAA,CAAAC,aACA,CAAC,cAAAN,MAAA,CAAAO,eAChB,CAAC,mBAAAN,kBACI,CAAC;EAAA;AAAA;AAAA,SAAAO,wDAAA1C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA2C,GAAA,GAhwB0DtG,EAAE,CAAAuG,gBAAA;IAAFvG,EAAE,CAAAwE,cAAA,YAyxB9F,CAAC;IAzxB2FxE,EAAE,CAAAwG,UAAA,+BAAAC,yFAAAC,MAAA;MAAF1G,EAAE,CAAA2G,aAAA,CAAAL,GAAA;MAAA,MAAAT,MAAA,GAAF7F,EAAE,CAAAmE,aAAA;MAAA,OAAFnE,EAAE,CAAA4G,WAAA,CAwxBvEf,MAAA,CAAAgB,kBAAA,CAAAH,MAAyB,CAAC;IAAA,EAAC;IAxxB0C1G,EAAE,CAAA0E,YAAA,CAyxBxF,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAkC,MAAA,GAzxBqF7F,EAAE,CAAAmE,aAAA;IAAA,MAAA2B,kBAAA,GAAF9F,EAAE,CAAA+F,WAAA;IAAF/F,EAAE,CAAAoE,UAAA,UAAAyB,MAAA,CAAAiB,KA8wB9E,CAAC,SAAAjB,MAAA,CAAAG,IACH,CAAC,WAAAH,MAAA,CAAAI,MACG,CAAC,aAAAJ,MAAA,CAAAkB,QACG,CAAC,eAAAlB,MAAA,CAAAmB,UACG,CAAC,yBAAAnB,MAAA,CAAAoB,oBACmB,CAAC,mBAAAnB,kBACZ,CAAC,cAAAD,MAAA,CAAAO,eACN,CAAC,kBAAAP,MAAA,CAAAK,WAAA,kBAAAL,MAAA,CAAAK,WAAA,CAAAC,aAAA,CACa,CAAC,kBAAAN,MAAA,CAAAK,WAAA,kBAAAL,MAAA,CAAAK,WAAA,CAAAC,aACA,CAAC;EAAA;AAAA;AAAA,SAAAe,0CAAAvD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAwD,GAAA,GAvxB8CnH,EAAE,CAAAuG,gBAAA;IAAFvG,EAAE,CAAAkF,UAAA,IAAAmB,uDAAA,yBA2wBhG,CAAC;IA3wB6FrG,EAAE,CAAAwG,UAAA,4BAAAY,gFAAAV,MAAA;MAAF1G,EAAE,CAAA2G,aAAA,CAAAQ,GAAA;MAAA,MAAAtB,MAAA,GAAF7F,EAAE,CAAAmE,aAAA;MAAA,OAAFnE,EAAE,CAAA4G,WAAA,CAqwB5Ef,MAAA,CAAAwB,gBAAA,CAAAX,MAAuB,CAAC;IAAA,EAAC;EAAA;EAAA,IAAA/C,EAAA;IAAA,MAAAkC,MAAA,GArwBiD7F,EAAE,CAAAmE,aAAA;IAAA,MAAAmD,SAAA,GAAFtH,EAAE,CAAA+F,WAAA;IAAF/F,EAAE,CAAAoE,UAAA,iCAAAyB,MAAA,CAAA0B,gBAswB9C,CAAC,8BAAAD,SACd,CAAC,6BAAAzB,MAAA,CAAA2B,YACK,CAAC,4BAAA3B,MAAA,CAAAI,MACT,CAAC,4DACyB,CAAC;EAAA;AAAA;AAAA,SAAAwB,0CAAA9D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA1wBgC3D,EAAE,CAAA2F,YAAA,KA8xBnF,CAAC;EAAA;AAAA;AAAA,MAAA+B,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,IAAA;AAAA,MAAAC,IAAA;AAAA,SAAAC,6CAAAnE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9xBgF3D,EAAE,CAAAuE,uBAAA,EAyoCjD,CAAC;IAzoC8CvE,EAAE,CAAAyE,MAAA,EAyoCpC,CAAC;IAzoCiCzE,EAAE,CAAA2E,qBAAA;EAAA;EAAA,IAAAhB,EAAA;IAAA,MAAAO,MAAA,GAAFlE,EAAE,CAAAmE,aAAA;IAAFnE,EAAE,CAAA4E,SAAA,CAyoCpC,CAAC;IAzoCiC5E,EAAE,CAAA6E,iBAAA,CAAAX,MAAA,CAAAY,OAyoCpC,CAAC;EAAA;AAAA;AAAA,SAAAiD,4CAAApE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAzoCiC3D,EAAE,CAAA2F,YAAA,KA2oChE,CAAC;EAAA;AAAA;AA5qCvC,MAAMqC,2BAA2B,GAAG,IAAI/H,cAAc,CAAC,yBAAyB,CAAC;AACjF,MAAMgI,uBAAuB,GAAG,IAAIhI,cAAc,CAAC,yBAAyB,CAAC;;AAE7E;AACA;AACA;AACA;AACA,MAAMiI,WAAW,CAAC;EACdC,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAACC,wBAAwB,GAAG,IAAIhH,OAAO,CAAC,CAAC;IAC7C;IACA,IAAI,CAACiH,mBAAmB,GAAG,IAAIjH,OAAO,CAAC,CAAC;IACxC,IAAI,CAACkH,MAAM,GAAG,IAAIjH,eAAe,CAAC,OAAO,CAAC;IAC1C,IAAI,CAACkH,KAAK,GAAG,IAAIlH,eAAe,CAAC,UAAU,CAAC;IAC5C,IAAI,CAACmH,aAAa,GAAG,IAAInH,eAAe,CAAC,EAAE,CAAC;IAC5C,IAAI,CAACoH,mBAAmB,GAAG,IAAIpH,eAAe,CAAC,KAAK,CAAC;EACzD;EACAqH,yBAAyBA,CAACC,IAAI,EAAE;IAC5B,IAAI,CAACP,wBAAwB,CAACQ,IAAI,CAACD,IAAI,CAAC;EAC5C;EACAE,oBAAoBA,CAACF,IAAI,EAAE;IACvB,IAAI,CAACN,mBAAmB,CAACO,IAAI,CAACD,IAAI,CAAC;EACvC;EACAG,OAAOA,CAAC9C,IAAI,EAAE;IACV,IAAI,CAACuC,KAAK,CAACK,IAAI,CAAC5C,IAAI,CAAC;EACzB;EACA+C,QAAQA,CAACjC,KAAK,EAAE;IACZ,IAAI,CAACwB,MAAM,CAACM,IAAI,CAAC9B,KAAK,CAAC;EAC3B;EACAkC,eAAeA,CAACC,MAAM,EAAE;IACpB,IAAI,CAACT,aAAa,CAACI,IAAI,CAACK,MAAM,CAAC;EACnC;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,oBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFlB,WAAW;IAAA,CAAoD;EAAE;EAC3K;IAAS,IAAI,CAACmB,KAAK,kBAD6ErJ,EAAE,CAAAsJ,kBAAA;MAAAC,KAAA,EACYrB,WAAW;MAAAsB,OAAA,EAAXtB,WAAW,CAAAgB;IAAA,EAAG;EAAE;AAClI;AACA;EAAA,QAAAO,SAAA,oBAAAA,SAAA,KAHoGzJ,EAAE,CAAA0J,iBAAA,CAGXxB,WAAW,EAAc,CAAC;IACzGyB,IAAI,EAAEzJ;EACV,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA,MAAM0J,gBAAgB,CAAC;EACnB;AACJ;AACA;AACA;AACA;EACIf,oBAAoBA,CAACF,IAAI,EAAE;IACvB,IAAI,CAACN,mBAAmB,CAACO,IAAI,CAACD,IAAI,CAAC;EACvC;EACAkB,2BAA2BA,CAACC,KAAK,EAAE;IAC/B,IAAI,CAACC,qBAAqB,CAACnB,IAAI,CAACkB,KAAK,CAAC;EAC1C;EACAE,gCAAgCA,CAACF,KAAK,EAAE;IACpC,IAAI,CAACG,2BAA2B,CAACrB,IAAI,CAACkB,KAAK,CAAC;EAChD;EACA3B,WAAWA,CAAC+B,oBAAoB,EAAEC,aAAa,EAAElD,oBAAoB,EAAE;IACnE,IAAI,CAACiD,oBAAoB,GAAGA,oBAAoB;IAChD,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAAClD,oBAAoB,GAAGA,oBAAoB;IAChD,IAAI,CAACsB,KAAK,GAAG,IAAI,CAAC4B,aAAa,CAAC5B,KAAK,CAAC6B,IAAI,CAAC5I,GAAG,CAACwE,IAAI,IAAI;MACnD,IAAIA,IAAI,KAAK,QAAQ,EAAE;QACnB,OAAO,QAAQ;QACf;MACJ,CAAC,MACI,IAAIA,IAAI,KAAK,UAAU,IAAI,IAAI,CAACkE,oBAAoB,EAAE;QACvD,OAAO,UAAU;MACrB,CAAC,MACI;QACD,OAAO,YAAY;MACvB;IACJ,CAAC,CAAC,CAAC;IACH,IAAI,CAACG,KAAK,GAAG,CAAC;IACd,IAAI,CAACN,qBAAqB,GAAG,IAAI1I,eAAe,CAAC,KAAK,CAAC;IACvD,IAAI,CAACoH,mBAAmB,GAAG,IAAIpH,eAAe,CAAC,KAAK,CAAC;IACrD;IACA,IAAI,CAAC4I,2BAA2B,GAAG,IAAI7I,OAAO,CAAC,CAAC;IAChD,IAAI,CAACiH,mBAAmB,GAAG,IAAIjH,OAAO,CAAC,CAAC;IACxC,IAAI,CAACkJ,QAAQ,GAAG,IAAIlJ,OAAO,CAAC,CAAC;IAC7B,IAAI,IAAI,CAAC8I,oBAAoB,EAAE;MAC3B,IAAI,CAACG,KAAK,GAAG,IAAI,CAACH,oBAAoB,CAACG,KAAK,GAAG,CAAC;IACpD;IACA;IACA,MAAME,uBAAuB,GAAG,IAAI,CAAClC,mBAAmB,CAAC+B,IAAI,CAAC3I,QAAQ,CAAC,MAAM,IAAI,CAAC8G,KAAK,CAAC,EAAE7G,MAAM,CAACsE,IAAI,IAAIA,IAAI,KAAK,QAAQ,IAAI,IAAI,CAACiB,oBAAoB,CAAC,EAAEtF,KAAK,CAAC,KAAK,CAAC,CAAC;IACvK,MAAM6I,qBAAqB,GAAGlJ,KAAK,CAAC,IAAI,CAAC2I,2BAA2B,EAAEM,uBAAuB,CAAC;IAC9F;IACA,MAAME,0BAA0B,GAAGlJ,aAAa,CAAC,CAAC,IAAI,CAACkH,mBAAmB,EAAE+B,qBAAqB,CAAC,CAAC,CAACJ,IAAI,CAAC5I,GAAG,CAAC,CAAC,CAACkJ,kBAAkB,EAAEC,oBAAoB,CAAC,KAAKD,kBAAkB,IAAIC,oBAAoB,CAAC,EAAE/I,SAAS,CAAC,GAAG,CAAC,EAAEC,oBAAoB,CAAC,CAAC,EAAEC,SAAS,CAAC,IAAI,CAACwI,QAAQ,CAAC,CAAC;IAC3QG,0BAA0B,CAACL,IAAI,CAACvI,oBAAoB,CAAC,CAAC,CAAC,CAAC+I,SAAS,CAACC,IAAI,IAAI;MACtE,IAAI,CAAChB,2BAA2B,CAACgB,IAAI,CAAC;MACtC,IAAI,IAAI,CAACX,oBAAoB,EAAE;QAC3B;QACA,IAAI,CAACA,oBAAoB,CAACzB,mBAAmB,CAACG,IAAI,CAACiC,IAAI,CAAC;MAC5D,CAAC,MACI;QACD,IAAI,CAACV,aAAa,CAAC1B,mBAAmB,CAACG,IAAI,CAACiC,IAAI,CAAC;MACrD;IACJ,CAAC,CAAC;EACN;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACR,QAAQ,CAAC1B,IAAI,CAAC,CAAC;IACpB,IAAI,CAAC0B,QAAQ,CAACS,QAAQ,CAAC,CAAC;EAC5B;EACA;IAAS,IAAI,CAAC7B,IAAI,YAAA8B,yBAAA5B,CAAA;MAAA,YAAAA,CAAA,IAAwFQ,gBAAgB,EAxE1B5J,EAAE,CAAAiL,QAAA,CAwE0CrB,gBAAgB,OAxE5D5J,EAAE,CAAAiL,QAAA,CAwEuG/C,WAAW,GAxEpHlI,EAAE,CAAAiL,QAAA,CAwE+HjD,2BAA2B;IAAA,CAA6C;EAAE;EAC3S;IAAS,IAAI,CAACqB,KAAK,kBAzE6ErJ,EAAE,CAAAsJ,kBAAA;MAAAC,KAAA,EAyEYK,gBAAgB;MAAAJ,OAAA,EAAhBI,gBAAgB,CAAAV;IAAA,EAAG;EAAE;AACvI;AACA;EAAA,QAAAO,SAAA,oBAAAA,SAAA,KA3EoGzJ,EAAE,CAAA0J,iBAAA,CA2EXE,gBAAgB,EAAc,CAAC;IAC9GD,IAAI,EAAEzJ;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEyJ,IAAI,EAAEC,gBAAgB;IAAEsB,UAAU,EAAE,CAAC;MACtDvB,IAAI,EAAExJ;IACV,CAAC,EAAE;MACCwJ,IAAI,EAAEvJ;IACV,CAAC;EAAE,CAAC,EAAE;IAAEuJ,IAAI,EAAEzB;EAAY,CAAC,EAAE;IAAEyB,IAAI,EAAEwB,SAAS;IAAED,UAAU,EAAE,CAAC;MACzDvB,IAAI,EAAEtJ,MAAM;MACZ+K,IAAI,EAAE,CAACpD,2BAA2B;IACtC,CAAC;EAAE,CAAC,CAAC;AAAA;AAErB,MAAMqD,mBAAmB,CAAC;EACtB;EACAC,aAAaA,CAACC,CAAC,EAAE;IACb,IAAI,IAAI,CAACvE,UAAU,EAAE;MACjBuE,CAAC,CAACC,cAAc,CAAC,CAAC;MAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;IACvB,CAAC,MACI;MACD,IAAI,CAACtB,aAAa,CAACzB,yBAAyB,CAAC,IAAI,CAAC;MAClD,IAAI,IAAI,CAACgD,gBAAgB,EAAE;QACvB;QACA,IAAI,CAACA,gBAAgB,CAAC7C,oBAAoB,CAAC,IAAI,CAAC;MACpD,CAAC,MACI;QACD;QACA,IAAI,CAACsB,aAAa,CAACtB,oBAAoB,CAAC,IAAI,CAAC;MACjD;IACJ;EACJ;EACA8C,gBAAgBA,CAAC7B,KAAK,EAAE;IACpB,IAAI,CAAC8B,UAAU,GAAG9B,KAAK;IACvB,IAAI,CAAC+B,SAAS,CAACjD,IAAI,CAACkB,KAAK,CAAC;EAC9B;EACAgC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAACC,gBAAgB,IAAI,CAAC,IAAI,CAACC,MAAM,IAAI,CAAC,IAAI,CAACA,MAAM,CAACC,SAAS,IAAI,CAAC,IAAI,CAACC,aAAa,EAAE;MACzF;IACJ;IACAC,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;MACzB,MAAMC,cAAc,GAAG,IAAI,CAACA,cAAc,CAAC,CAAC;MAC5C,IAAI,IAAI,CAACV,UAAU,KAAKU,cAAc,EAAE;QACpC,IAAI,CAACV,UAAU,GAAGU,cAAc;QAChC,IAAI,CAACX,gBAAgB,CAAC,IAAI,CAACC,UAAU,CAAC;QACtC,IAAI,CAACW,GAAG,CAACC,YAAY,CAAC,CAAC;MAC3B;IACJ,CAAC,CAAC;EACN;EACAF,cAAcA,CAAA,EAAG;IACb,MAAMG,eAAe,GAAG,IAAI,CAACC,YAAY,CAAC,IAAI,CAACV,MAAM,CAAC;IACtD,OAAQ,IAAI,CAACW,UAAU,IAAIF,eAAe,CAAC,IAAI,CAACE,UAAU,CAAC,IAAK,IAAI,CAACZ,gBAAgB,CAACa,IAAI,CAACH,eAAe,CAAC;EAC/G;EACAC,YAAYA,CAACV,MAAM,EAAE;IACjB,OAAQa,IAAI,IAAKb,MAAM,CAACc,QAAQ,CAACD,IAAI,CAACE,OAAO,IAAI,EAAE,EAAE;MACjDC,KAAK,EAAE,IAAI,CAACC,kBAAkB,GAAG,OAAO,GAAG,QAAQ;MACnDC,WAAW,EAAE,IAAI,CAACD,kBAAkB,GAAG,OAAO,GAAG,QAAQ;MACzDE,QAAQ,EAAE,SAAS;MACnBC,YAAY,EAAE;IAClB,CAAC,CAAC;EACN;EACAjF,WAAWA,CAACgC,aAAa,EAAEoC,GAAG,EAAEb,gBAAgB,EAAEzE,oBAAoB,EAAEoG,cAAc,EAAEV,UAAU,EAAEX,MAAM,EAAE;IACxG,IAAI,CAAC7B,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACoC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACb,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACzE,oBAAoB,GAAGA,oBAAoB;IAChD,IAAI,CAACoG,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACV,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACX,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC1B,QAAQ,GAAG,IAAIlJ,OAAO,CAAC,CAAC;IAC7B,IAAI,CAACiJ,KAAK,GAAG,IAAI,CAACqB,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACrB,KAAK,GAAG,CAAC,GAAG,CAAC;IACxE,IAAI,CAACwB,SAAS,GAAG,IAAIzK,OAAO,CAAC,CAAC;IAC9B,IAAI,CAACkM,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACjI,GAAG,GAAG,KAAK;IAChB,IAAI,CAAC2B,UAAU,GAAG,KAAK;IACvB,IAAI,CAAC4E,UAAU,GAAG,KAAK;IACvB,IAAI,CAAC2B,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACN,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACf,aAAa,GAAG,KAAK;IAC1B,IAAIF,MAAM,EAAE;MACR,IAAI,CAACA,MAAM,CAACwB,MAAM,CAACpD,IAAI,CAACtI,SAAS,CAAC,IAAI,CAACwI,QAAQ,CAAC,EAAE5I,MAAM,CAAC6J,CAAC,IAAIA,CAAC,YAAYpJ,aAAa,CAAC,CAAC,CAACyI,SAAS,CAAC,MAAM;QACvG,IAAI,CAACkB,kBAAkB,CAAC,CAAC;MAC7B,CAAC,CAAC;IACN;EACJ;EACA2B,QAAQA,CAAA,EAAG;IACP;IACAlM,aAAa,CAAC,CAAC,IAAI,CAAC4I,aAAa,CAAC5B,KAAK,EAAE,IAAI,CAAC4B,aAAa,CAAC3B,aAAa,CAAC,CAAC,CACtE4B,IAAI,CAACtI,SAAS,CAAC,IAAI,CAACwI,QAAQ,CAAC,CAAC,CAC9BM,SAAS,CAAC,CAAC,CAAC5E,IAAI,EAAE0H,YAAY,CAAC,KAAK;MACrC,IAAI,CAACJ,iBAAiB,GAAGtH,IAAI,KAAK,QAAQ,GAAG,IAAI,CAACqE,KAAK,GAAGqD,YAAY,GAAG,IAAI;IACjF,CAAC,CAAC;IACF,IAAI,CAACrI,GAAG,GAAG,IAAI,CAACgI,cAAc,CAACvD,KAAK;IACpC,IAAI,CAACuD,cAAc,CAACM,MAAM,EAAEvD,IAAI,CAACtI,SAAS,CAAC,IAAI,CAACwI,QAAQ,CAAC,CAAC,CAACM,SAAS,CAAEgD,SAAS,IAAK;MAChF,IAAI,CAACvI,GAAG,GAAGuI,SAAS;IACxB,CAAC,CAAC;EACN;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC9B,gBAAgB,CAAC+B,OAAO,CAAC1D,IAAI,CAACtI,SAAS,CAAC,IAAI,CAACwI,QAAQ,CAAC,CAAC,CAACM,SAAS,CAAC,MAAM,IAAI,CAACkB,kBAAkB,CAAC,CAAC,CAAC;IACvG,IAAI,CAACA,kBAAkB,CAAC,CAAC;EAC7B;EACAiC,WAAWA,CAACD,OAAO,EAAE;IACjB,IAAIA,OAAO,CAAClC,UAAU,EAAE;MACpB,IAAI,CAACD,gBAAgB,CAAC,IAAI,CAACC,UAAU,CAAC;IAC1C;EACJ;EACAd,WAAWA,CAAA,EAAG;IACV,IAAI,CAACR,QAAQ,CAAC1B,IAAI,CAAC,IAAI,CAAC;IACxB,IAAI,CAAC0B,QAAQ,CAACS,QAAQ,CAAC,CAAC;EAC5B;EACA;IAAS,IAAI,CAAC7B,IAAI,YAAA8E,4BAAA5E,CAAA;MAAA,YAAAA,CAAA,IAAwFiC,mBAAmB,EAvL7BrL,EAAE,CAAAiO,iBAAA,CAuL6C/F,WAAW,GAvL1DlI,EAAE,CAAAiO,iBAAA,CAuLqEjO,EAAE,CAACkO,iBAAiB,GAvL3FlO,EAAE,CAAAiO,iBAAA,CAuLsGrE,gBAAgB,MAvLxH5J,EAAE,CAAAiO,iBAAA,CAuLmJjG,2BAA2B,GAvLhLhI,EAAE,CAAAiO,iBAAA,CAuL2L5L,EAAE,CAAC8L,cAAc,MAvL9MnO,EAAE,CAAAiO,iBAAA,CAuLyO/L,EAAE,CAACE,UAAU,MAvLxPpC,EAAE,CAAAiO,iBAAA,CAuLmR/L,EAAE,CAACkM,MAAM;IAAA,CAA4D;EAAE;EAC5b;IAAS,IAAI,CAACC,IAAI,kBAxL8ErO,EAAE,CAAAsO,iBAAA;MAAA3E,IAAA,EAwLJ0B,mBAAmB;MAAAkD,SAAA;MAAAC,cAAA,WAAAC,mCAAA9K,EAAA,EAAAC,GAAA,EAAA8K,QAAA;QAAA,IAAA/K,EAAA;UAxLjB3D,EAAE,CAAA2O,cAAA,CAAAD,QAAA,EAwLklCtM,UAAU;QAAA;QAAA,IAAAuB,EAAA;UAAA,IAAAiL,EAAA;UAxL9lC5O,EAAE,CAAA6O,cAAA,CAAAD,EAAA,GAAF5O,EAAE,CAAA8O,WAAA,QAAAlL,GAAA,CAAAmI,gBAAA,GAAA6C,EAAA;QAAA;MAAA;MAAAG,QAAA;MAAAC,YAAA,WAAAC,iCAAAtL,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF3D,EAAE,CAAAwG,UAAA,mBAAA0I,6CAAAxI,MAAA;YAAA,OAwLJ9C,GAAA,CAAA0H,aAAA,CAAA5E,MAAoB,CAAC;UAAA,CAAH,CAAC;QAAA;QAAA,IAAA/C,EAAA;UAxLjB3D,EAAE,CAAAmP,WAAA,iBAAAvL,GAAA,CAAAyB,GAAA,KAwLI,KAAK,GAAG,IAAI,GAAAzB,GAAA,CAAAwL,aAAA,IAAAxL,GAAA,CAAA0J,iBAAA,MAAF,CAAC,kBAAA1J,GAAA,CAAAyB,GAAA,KAAX,KAAK,GAAAzB,GAAA,CAAAwL,aAAA,IAAAxL,GAAA,CAAA0J,iBAAA,GAAwC,IAAI,MAAvC,CAAC;UAxLjBtN,EAAE,CAAAqP,WAAA,2BAAAzL,GAAA,CAAAqD,oBAwLc,CAAC,oCAAArD,GAAA,CAAAqD,oBAAA,IAAArD,GAAA,CAAAgI,UAAD,CAAC,kCAAAhI,GAAA,CAAAqD,oBAAA,IAAArD,GAAA,CAAA2J,QAAD,CAAC,oCAAA3J,GAAA,CAAAqD,oBAAA,IAAArD,GAAA,CAAAoD,UAAD,CAAC,mBAAApD,GAAA,CAAAqD,oBAAD,CAAC,4BAAArD,GAAA,CAAAqD,oBAAA,IAAArD,GAAA,CAAAgI,UAAD,CAAC,0BAAAhI,GAAA,CAAAqD,oBAAA,IAAArD,GAAA,CAAA2J,QAAD,CAAC,4BAAA3J,GAAA,CAAAqD,oBAAA,IAAArD,GAAA,CAAAoD,UAAD,CAAC;QAAA;MAAA;MAAAsI,MAAA;QAAAF,aAAA;QAAApI,UAAA;QAAA4E,UAAA;QAAA2B,QAAA;QAAAN,kBAAA;QAAAf,aAAA;MAAA;MAAAqD,QAAA;MAAAC,UAAA;MAAAC,QAAA,GAxLjBzP,EAAE,CAAA0P,oBAAA,EAAF1P,EAAE,CAAA2P,mBAAA;MAAAC,KAAA,EAAArM,GAAA;MAAAsM,kBAAA,EAAArM,GAAA;MAAAsM,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAvM,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF3D,EAAE,CAAAmQ,eAAA;UAAFnQ,EAAE,CAAAwE,cAAA,aAyL9D,CAAC;UAzL2DxE,EAAE,CAAA2F,YAAA,EA0LxE,CAAC;UA1LqE3F,EAAE,CAAA0E,YAAA,CA2L5F,CAAC;QAAA;MAAA;MAAA0L,aAAA;MAAAC,eAAA;IAAA,EACyG;EAAE;AACtH;AACAtQ,UAAU,CAAC,CACPkC,YAAY,CAAC,CAAC,CACjB,EAAEoJ,mBAAmB,CAACiF,SAAS,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC;AACvDvQ,UAAU,CAAC,CACPkC,YAAY,CAAC,CAAC,CACjB,EAAEoJ,mBAAmB,CAACiF,SAAS,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC;AACvDvQ,UAAU,CAAC,CACPkC,YAAY,CAAC,CAAC,CACjB,EAAEoJ,mBAAmB,CAACiF,SAAS,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;AACrDvQ,UAAU,CAAC,CACPkC,YAAY,CAAC,CAAC,CACjB,EAAEoJ,mBAAmB,CAACiF,SAAS,EAAE,oBAAoB,EAAE,KAAK,CAAC,CAAC;AAC/DvQ,UAAU,CAAC,CACPkC,YAAY,CAAC,CAAC,CACjB,EAAEoJ,mBAAmB,CAACiF,SAAS,EAAE,eAAe,EAAE,KAAK,CAAC,CAAC;AAC1D;EAAA,QAAA7G,SAAA,oBAAAA,SAAA,KA7MoGzJ,EAAE,CAAA0J,iBAAA,CA6MX2B,mBAAmB,EAAc,CAAC;IACjH1B,IAAI,EAAErJ,SAAS;IACf8K,IAAI,EAAE,CAAC;MACCmF,QAAQ,EAAE,gBAAgB;MAC1BhB,QAAQ,EAAE,YAAY;MACtBc,eAAe,EAAE9P,uBAAuB,CAACiQ,MAAM;MAC/CJ,aAAa,EAAE5P,iBAAiB,CAACiQ,IAAI;MACrCC,mBAAmB,EAAE,KAAK;MAC1BT,QAAQ,EAAG;AAC/B;AACA;AACA;AACA,GAAG;MACiBU,IAAI,EAAE;QACF,gCAAgC,EAAG,sBAAqB;QACxD,yCAAyC,EAAG,oCAAmC;QAC/E,uCAAuC,EAAG,kCAAiC;QAC3E,yCAAyC,EAAG,oCAAmC;QAC/E,uBAAuB,EAAG,uBAAsB;QAChD,gCAAgC,EAAG,qCAAoC;QACvE,8BAA8B,EAAG,mCAAkC;QACnE,gCAAgC,EAAG,qCAAoC;QACvE,wBAAwB,EAAG,2DAA0D;QACrF,yBAAyB,EAAG,2DAA0D;QACtF,SAAS,EAAE;MACf,CAAC;MACDnB,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE7F,IAAI,EAAEzB;EAAY,CAAC,EAAE;IAAEyB,IAAI,EAAE3J,EAAE,CAACkO;EAAkB,CAAC,EAAE;IAAEvE,IAAI,EAAEC,gBAAgB;IAAEsB,UAAU,EAAE,CAAC;MAC7GvB,IAAI,EAAEvJ;IACV,CAAC;EAAE,CAAC,EAAE;IAAEuJ,IAAI,EAAEwB,SAAS;IAAED,UAAU,EAAE,CAAC;MAClCvB,IAAI,EAAEtJ,MAAM;MACZ+K,IAAI,EAAE,CAACpD,2BAA2B;IACtC,CAAC;EAAE,CAAC,EAAE;IAAE2B,IAAI,EAAEtH,EAAE,CAAC8L,cAAc;IAAEjD,UAAU,EAAE,CAAC;MAC1CvB,IAAI,EAAEvJ;IACV,CAAC;EAAE,CAAC,EAAE;IAAEuJ,IAAI,EAAEzH,EAAE,CAACE,UAAU;IAAE8I,UAAU,EAAE,CAAC;MACtCvB,IAAI,EAAEvJ;IACV,CAAC;EAAE,CAAC,EAAE;IAAEuJ,IAAI,EAAEzH,EAAE,CAACkM,MAAM;IAAElD,UAAU,EAAE,CAAC;MAClCvB,IAAI,EAAEvJ;IACV,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEgP,aAAa,EAAE,CAAC;MACzCzF,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAEuG,UAAU,EAAE,CAAC;MACb2C,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAEmL,UAAU,EAAE,CAAC;MACbjC,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAE8M,QAAQ,EAAE,CAAC;MACX5D,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAEwM,kBAAkB,EAAE,CAAC;MACrBtD,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAEyL,aAAa,EAAE,CAAC;MAChBvC,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAEsL,gBAAgB,EAAE,CAAC;MACnBpC,IAAI,EAAEjJ,eAAe;MACrB0K,IAAI,EAAE,CAAChJ,UAAU,EAAE;QAAEwO,WAAW,EAAE;MAAK,CAAC;IAC5C,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMC,6BAA6B,CAAC;EAChC1I,WAAWA,CAAC2I,UAAU,EAAEC,QAAQ,EAAE1D,cAAc,EAAE;IAC9C,IAAI,CAACyD,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAC1D,cAAc,GAAGA,cAAc;IACpC,IAAI,CAAC2D,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACjL,IAAI,GAAG,UAAU;IACtB,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACiL,oBAAoB,GAAG,EAAE;IAC9B,IAAI,CAACC,WAAW,GAAG,WAAW;IAC9B,IAAI,CAAC9L,GAAG,GAAG,KAAK;IAChB,IAAI,CAACiF,QAAQ,GAAG,IAAIlJ,OAAO,CAAC,CAAC;EACjC;EACAgQ,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACnL,MAAM,EAAE;MACb,IAAI,CAACkL,WAAW,GAAG,UAAU;IACjC,CAAC,MACI;MACD,IAAI,CAACA,WAAW,GAAG,WAAW;IAClC;EACJ;EACA1D,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC2D,eAAe,CAAC,CAAC;IACtB,IAAI,CAAC/L,GAAG,GAAG,IAAI,CAACgI,cAAc,CAACvD,KAAK;IACpC,IAAI,CAACuD,cAAc,CAACM,MAAM,EAAEvD,IAAI,CAACtI,SAAS,CAAC,IAAI,CAACwI,QAAQ,CAAC,CAAC,CAACM,SAAS,CAAEgD,SAAS,IAAK;MAChF,IAAI,CAACvI,GAAG,GAAGuI,SAAS;IACxB,CAAC,CAAC;EACN;EACAG,WAAWA,CAACD,OAAO,EAAE;IACjB,MAAM;MAAE9H,IAAI;MAAEC,MAAM;MAAEgL;IAAU,CAAC,GAAGnD,OAAO;IAC3C,IAAI9H,IAAI,IAAIC,MAAM,EAAE;MAChB,IAAI,CAACmL,eAAe,CAAC,CAAC;IAC1B;IACA,IAAIH,SAAS,EAAE;MACX,IAAI,IAAI,CAACC,oBAAoB,CAACG,MAAM,EAAE;QAClC,IAAI,CAACH,oBAAoB,CACpBxP,MAAM,CAAC4P,IAAI,IAAI,CAAC,CAACA,IAAI,CAAC,CACtBC,OAAO,CAACC,SAAS,IAAI;UACtB,IAAI,CAACT,QAAQ,CAACU,WAAW,CAAC,IAAI,CAACX,UAAU,CAACY,aAAa,EAAEF,SAAS,CAAC;QACvE,CAAC,CAAC;MACN;MACA,IAAI,IAAI,CAACP,SAAS,EAAE;QAChB,IAAI,CAACC,oBAAoB,GAAG,IAAI,CAACD,SAAS,CAACU,KAAK,CAAC,GAAG,CAAC;QACrD,IAAI,CAACT,oBAAoB,CACpBxP,MAAM,CAAC4P,IAAI,IAAI,CAAC,CAACA,IAAI,CAAC,CACtBC,OAAO,CAACC,SAAS,IAAI;UACtB,IAAI,CAACT,QAAQ,CAACa,QAAQ,CAAC,IAAI,CAACd,UAAU,CAACY,aAAa,EAAEF,SAAS,CAAC;QACpE,CAAC,CAAC;MACN;IACJ;EACJ;EACA1G,WAAWA,CAAA,EAAG;IACV,IAAI,CAACR,QAAQ,CAAC1B,IAAI,CAAC,CAAC;IACpB,IAAI,CAAC0B,QAAQ,CAACS,QAAQ,CAAC,CAAC;EAC5B;EACA;IAAS,IAAI,CAAC7B,IAAI,YAAA2I,sCAAAzI,CAAA;MAAA,YAAAA,CAAA,IAAwFyH,6BAA6B,EA7TvC7Q,EAAE,CAAAiO,iBAAA,CA6TuDjO,EAAE,CAACa,UAAU,GA7TtEb,EAAE,CAAAiO,iBAAA,CA6TiFjO,EAAE,CAAC8R,SAAS,GA7T/F9R,EAAE,CAAAiO,iBAAA,CA6T0G5L,EAAE,CAAC8L,cAAc;IAAA,CAA4D;EAAE;EAC3R;IAAS,IAAI,CAACE,IAAI,kBA9T8ErO,EAAE,CAAAsO,iBAAA;MAAA3E,IAAA,EA8TJkH,6BAA6B;MAAAtC,SAAA;MAAAwD,SAAA;MAAAhD,QAAA;MAAAC,YAAA,WAAAgD,2CAAArO,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA9T3B3D,EAAE,CAAAiS,uBAAA,oBAAArO,GAAA,CAAAuN,WA8TwB,CAAC;UA9T3BnR,EAAE,CAAAqP,WAAA,iBAAAzL,GAAA,CAAAyB,GAAA,KA8TI,KAAoB,CAAC;QAAA;MAAA;MAAAiK,MAAA;QAAA0B,cAAA;QAAAC,SAAA;QAAAjL,IAAA;QAAAC,MAAA;MAAA;MAAAsJ,QAAA;MAAAC,UAAA;MAAAC,QAAA,GA9T3BzP,EAAE,CAAA0P,oBAAA,EAAF1P,EAAE,CAAA2P,mBAAA;MAAAC,KAAA,EAAAnM,GAAA;MAAAqM,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAiC,uCAAAvO,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF3D,EAAE,CAAAkF,UAAA,IAAAxB,oDAAA,wBA8T2d,CAAC;QAAA;QAAA,IAAAC,EAAA;UA9T9d3D,EAAE,CAAAoE,UAAA,qBAAAR,GAAA,CAAAoN,cA8T0d,CAAC;QAAA;MAAA;MAAAmB,YAAA,GAA6EtP,gBAAgB;MAAAuN,aAAA;MAAAvF,IAAA;QAAAuH,SAAA,EAAsI,CAACrP,cAAc;MAAC;MAAAsN,eAAA;IAAA,EAAiG;EAAE;AACv5B;AACA;EAAA,QAAA5G,SAAA,oBAAAA,SAAA,KAhUoGzJ,EAAE,CAAA0J,iBAAA,CAgUXmH,6BAA6B,EAAc,CAAC;IAC3HlH,IAAI,EAAErJ,SAAS;IACf8K,IAAI,EAAE,CAAC;MACCmF,QAAQ,EAAE,2BAA2B;MACrC8B,UAAU,EAAE,CAACtP,cAAc,CAAC;MAC5BwM,QAAQ,EAAE,sBAAsB;MAChCa,aAAa,EAAE5P,iBAAiB,CAACiQ,IAAI;MACrCJ,eAAe,EAAE9P,uBAAuB,CAACiQ,MAAM;MAC/CP,QAAQ,EAAG,mEAAkE;MAC7EU,IAAI,EAAE;QACF2B,KAAK,EAAE,uCAAuC;QAC9C,sBAAsB,EAAG,eAAc;QACvC,mBAAmB,EAAE;MACzB,CAAC;MACDC,OAAO,EAAE,CAAC1P,gBAAgB,CAAC;MAC3B2M,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE7F,IAAI,EAAE3J,EAAE,CAACa;EAAW,CAAC,EAAE;IAAE8I,IAAI,EAAE3J,EAAE,CAAC8R;EAAU,CAAC,EAAE;IAAEnI,IAAI,EAAEtH,EAAE,CAAC8L,cAAc;IAAEjD,UAAU,EAAE,CAAC;MACxGvB,IAAI,EAAEvJ;IACV,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAE4Q,cAAc,EAAE,CAAC;MAC1CrH,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAEwQ,SAAS,EAAE,CAAC;MACZtH,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAEuF,IAAI,EAAE,CAAC;MACP2D,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAEwF,MAAM,EAAE,CAAC;MACT0D,IAAI,EAAElJ;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM+R,iCAAiC,CAAC;EACpCrK,WAAWA,CAACkF,cAAc,EAAE;IACxB,IAAI,CAACA,cAAc,GAAGA,cAAc;IACpC,IAAI,CAAC4D,SAAS,GAAG,EAAE;IACnB,IAAI,CAACnK,KAAK,GAAG,OAAO;IACpB,IAAI,CAACkK,cAAc,GAAG,IAAI;IAC1B,IAAI,CAAC/J,oBAAoB,GAAG,KAAK;IACjC,IAAI,CAACjB,IAAI,GAAG,UAAU;IACtB,IAAI,CAACe,QAAQ,GAAG,OAAO;IACvB,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACf,MAAM,GAAG,KAAK;IACnB,IAAI,CAACwM,iBAAiB,GAAG,IAAI9R,YAAY,CAAC,CAAC;IAC3C,IAAI,CAACwQ,WAAW,GAAG,WAAW;IAC9B,IAAI,CAAC9L,GAAG,GAAG,KAAK;IAChB,IAAI,CAACiF,QAAQ,GAAG,IAAIlJ,OAAO,CAAC,CAAC;EACjC;EACAsR,aAAaA,CAACC,KAAK,EAAE;IACjB,IAAI,CAAC,IAAI,CAAC3L,UAAU,EAAE;MAClB,IAAI,CAACyL,iBAAiB,CAAC7J,IAAI,CAAC+J,KAAK,CAAC;IACtC;EACJ;EACA7H,WAAWA,CAAA,EAAG;IACV,IAAI,CAACR,QAAQ,CAAC1B,IAAI,CAAC,CAAC;IACpB,IAAI,CAAC0B,QAAQ,CAACS,QAAQ,CAAC,CAAC;EAC5B;EACAqG,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACnL,MAAM,EAAE;MACb,IAAI,IAAI,CAACD,IAAI,KAAK,YAAY,EAAE;QAC5B,IAAI,CAACmL,WAAW,GAAG,QAAQ;MAC/B,CAAC,MACI,IAAI,IAAI,CAACnL,IAAI,KAAK,UAAU,EAAE;QAC/B,IAAI,CAACmL,WAAW,GAAG,QAAQ;MAC/B;IACJ,CAAC,MACI;MACD,IAAI,CAACA,WAAW,GAAG,WAAW;IAClC;EACJ;EACA1D,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC2D,eAAe,CAAC,CAAC;IACtB,IAAI,CAAC/L,GAAG,GAAG,IAAI,CAACgI,cAAc,CAACvD,KAAK;IACpC,IAAI,CAACuD,cAAc,CAACM,MAAM,EAAEvD,IAAI,CAACtI,SAAS,CAAC,IAAI,CAACwI,QAAQ,CAAC,CAAC,CAACM,SAAS,CAAEgD,SAAS,IAAK;MAChF,IAAI,CAACvI,GAAG,GAAGuI,SAAS;IACxB,CAAC,CAAC;EACN;EACAG,WAAWA,CAACD,OAAO,EAAE;IACjB,MAAM;MAAE9H,IAAI;MAAEC;IAAO,CAAC,GAAG6H,OAAO;IAChC,IAAI9H,IAAI,IAAIC,MAAM,EAAE;MAChB,IAAI,CAACmL,eAAe,CAAC,CAAC;IAC1B;EACJ;EACA;IAAS,IAAI,CAAClI,IAAI,YAAA0J,0CAAAxJ,CAAA;MAAA,YAAAA,CAAA,IAAwFoJ,iCAAiC,EAhZ3CxS,EAAE,CAAAiO,iBAAA,CAgZ2D5L,EAAE,CAAC8L,cAAc;IAAA,CAA4D;EAAE;EAC5O;IAAS,IAAI,CAACE,IAAI,kBAjZ8ErO,EAAE,CAAAsO,iBAAA;MAAA3E,IAAA,EAiZJ6I,iCAAiC;MAAAjE,SAAA;MAAAwD,SAAA;MAAAhD,QAAA;MAAAC,YAAA,WAAA6D,+CAAAlP,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAjZ/B3D,EAAE,CAAAwG,UAAA,wBAAAsM,gEAAA;YAAA,OAiZJlP,GAAA,CAAA8O,aAAA,CAAc,IAAI,CAAC;UAAA,CAAa,CAAC,wBAAAK,gEAAA;YAAA,OAAjCnP,GAAA,CAAA8O,aAAA,CAAc,KAAK,CAAC;UAAA,CAAY,CAAC;QAAA;QAAA,IAAA/O,EAAA;UAjZ/B3D,EAAE,CAAAiS,uBAAA,iBAAArO,GAAA,CAAAuN,WAiZ4B,CAAC,mBAAAvN,GAAA,CAAAuN,WAAD,CAAC;UAjZ/BnR,EAAE,CAAAqP,WAAA,mBAAAzL,GAAA,CAAAkD,KAAA,KAiZM,OAAsB,CAAC,kBAAAlD,GAAA,CAAAkD,KAAA,KAAvB,MAAsB,CAAC,sCAAAlD,GAAA,CAAAoC,IAAA,KAAxB,YAAuB,CAAC,qCAAApC,GAAA,CAAAoC,IAAA,KAAxB,UAAU,IAAApC,GAAA,CAAAmD,QAAA,KAAiB,OAAJ,CAAC,oCAAAnD,GAAA,CAAAoC,IAAA,KAAxB,UAAU,IAAApC,GAAA,CAAAmD,QAAA,KAAiB,MAAJ,CAAC,yBAAAnD,GAAA,CAAAyB,GAAA,KAA1B,KAAyB,CAAC;QAAA;MAAA;MAAAiK,MAAA;QAAA2B,SAAA;QAAAnK,KAAA;QAAAkK,cAAA;QAAA/J,oBAAA;QAAAjB,IAAA;QAAAe,QAAA;QAAAC,UAAA;QAAAf,MAAA;MAAA;MAAA+M,OAAA;QAAAP,iBAAA;MAAA;MAAAlD,QAAA;MAAAC,UAAA;MAAAC,QAAA,GAjZ/BzP,EAAE,CAAA0P,oBAAA,EAAF1P,EAAE,CAAA2P,mBAAA;MAAAC,KAAA,EAAA/L,GAAA;MAAAiM,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAgD,2CAAAtP,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF3D,EAAE,CAAAwE,cAAA,YA2ZlG,CAAC;UA3Z+FxE,EAAE,CAAAkF,UAAA,IAAApB,wDAAA,wBA4ZhD,CAAC;UA5Z6C9D,EAAE,CAAA0E,YAAA,CA6Z7F,CAAC;QAAA;QAAA,IAAAf,EAAA;UA7Z0F3D,EAAE,CAAAqP,WAAA,sBAAAzL,GAAA,CAAAqD,oBAmZjD,CAAC,cAAArD,GAAA,CAAAqD,oBACT,CAAC,+BAAArD,GAAA,CAAAqD,oBACgB,CAAC,uBAAArD,GAAA,CAAAqD,oBACT,CAAC,0BAAArD,GAAA,CAAAqD,oBACE,CAAC,kBAAArD,GAAA,CAAAqD,oBACT,CAAC,iBAAArD,GAAA,CAAAyB,GAAA,UACT,CAAC;UAzZ0DrF,EAAE,CAAAoE,UAAA,YAAAR,GAAA,CAAAqN,SA0Z5E,CAAC;UA1ZyEjR,EAAE,CAAA4E,SAAA,CA4ZjD,CAAC;UA5Z8C5E,EAAE,CAAAoE,UAAA,qBAAAR,GAAA,CAAAoN,cA4ZjD,CAAC;QAAA;MAAA;MAAAmB,YAAA,GAESrP,OAAO,EAAoFD,gBAAgB;MAAAuN,aAAA;MAAAvF,IAAA;QAAAuH,SAAA,EAAsI,CAACpP,aAAa,EAAEC,WAAW;MAAC;MAAAoN,eAAA;IAAA,EAAiG;EAAE;AAC/a;AACA;EAAA,QAAA5G,SAAA,oBAAAA,SAAA,KAhaoGzJ,EAAE,CAAA0J,iBAAA,CAgaX8I,iCAAiC,EAAc,CAAC;IAC/H7I,IAAI,EAAErJ,SAAS;IACf8K,IAAI,EAAE,CAAC;MACCmF,QAAQ,EAAE,gCAAgC;MAC1ChB,QAAQ,EAAE,0BAA0B;MACpCa,aAAa,EAAE5P,iBAAiB,CAACiQ,IAAI;MACrC4B,UAAU,EAAE,CAACrP,aAAa,EAAEC,WAAW,CAAC;MACxCoN,eAAe,EAAE9P,uBAAuB,CAACiQ,MAAM;MAC/CP,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiBU,IAAI,EAAE;QACF2B,KAAK,EAAE,yCAAyC;QAChD,wBAAwB,EAAE,mBAAmB;QAC7C,uBAAuB,EAAE,kBAAkB;QAC3C,2CAA2C,EAAE,uBAAuB;QACpE,0CAA0C,EAAE,6CAA6C;QACzF,yCAAyC,EAAE,4CAA4C;QACvF,8BAA8B,EAAE,cAAc;QAC9C,gBAAgB,EAAE,aAAa;QAC/B,kBAAkB,EAAE,aAAa;QACjC,cAAc,EAAE,qBAAqB;QACrC,cAAc,EAAE;MACpB,CAAC;MACDC,OAAO,EAAE,CAACzP,OAAO,EAAED,gBAAgB,CAAC;MACpC2M,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE7F,IAAI,EAAEtH,EAAE,CAAC8L,cAAc;IAAEjD,UAAU,EAAE,CAAC;MACvDvB,IAAI,EAAEvJ;IACV,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAE6Q,SAAS,EAAE,CAAC;MACrCtH,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAEqG,KAAK,EAAE,CAAC;MACR6C,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAEuQ,cAAc,EAAE,CAAC;MACjBrH,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAEwG,oBAAoB,EAAE,CAAC;MACvB0C,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAEuF,IAAI,EAAE,CAAC;MACP2D,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAEsG,QAAQ,EAAE,CAAC;MACX4C,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAEuG,UAAU,EAAE,CAAC;MACb2C,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAEwF,MAAM,EAAE,CAAC;MACT0D,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAEgS,iBAAiB,EAAE,CAAC;MACpB9I,IAAI,EAAE/I;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMsS,uBAAuB,CAAC;EAC1B/K,WAAWA,CAACoE,GAAG,EAAEc,cAAc,EAAE;IAC7B,IAAI,CAACd,GAAG,GAAGA,GAAG;IACd,IAAI,CAACc,cAAc,GAAGA,cAAc;IACpC,IAAI,CAAChJ,MAAM,GAAG,IAAI;IAClB,IAAI,CAACS,OAAO,GAAG,IAAI;IACnB,IAAI,CAACmC,oBAAoB,GAAG,KAAK;IACjC,IAAI,CAACD,UAAU,GAAG,KAAK;IACvB,IAAI,CAACmM,WAAW,GAAG,IAAI;IACvB,IAAI,CAACnN,IAAI,GAAG,UAAU;IACtB,IAAI,CAACoN,aAAa,GAAG,IAAIzS,YAAY,CAAC,CAAC;IACvC,IAAI,CAAC8R,iBAAiB,GAAG,IAAI9R,YAAY,CAAC,CAAC;IAC3C,IAAI,CAAC0E,GAAG,GAAG,KAAK;IAChB,IAAI,CAACiF,QAAQ,GAAG,IAAIlJ,OAAO,CAAC,CAAC;EACjC;EACAqM,QAAQA,CAAA,EAAG;IACP,IAAI,CAACpI,GAAG,GAAG,IAAI,CAACgI,cAAc,CAACvD,KAAK;IACpC,IAAI,CAACuD,cAAc,CAACM,MAAM,EAAEvD,IAAI,CAACtI,SAAS,CAAC,IAAI,CAACwI,QAAQ,CAAC,CAAC,CAACM,SAAS,CAAEgD,SAAS,IAAK;MAChF,IAAI,CAACvI,GAAG,GAAGuI,SAAS;MACpB,IAAI,CAACrB,GAAG,CAAC8G,aAAa,CAAC,CAAC;IAC5B,CAAC,CAAC;EACN;EACAvI,WAAWA,CAAA,EAAG;IACV,IAAI,CAACR,QAAQ,CAAC1B,IAAI,CAAC,CAAC;IACpB,IAAI,CAAC0B,QAAQ,CAACS,QAAQ,CAAC,CAAC;EAC5B;EACA2H,aAAaA,CAACC,KAAK,EAAE;IACjB,IAAI,CAAC,IAAI,CAAC3L,UAAU,EAAE;MAClB,IAAI,CAACyL,iBAAiB,CAAC7J,IAAI,CAAC+J,KAAK,CAAC;IACtC;EACJ;EACAW,UAAUA,CAAA,EAAG;IACT,IAAI,IAAI,CAACtN,IAAI,KAAK,QAAQ,IAAI,CAAC,IAAI,CAACgB,UAAU,EAAE;MAC5C,IAAI,CAACoM,aAAa,CAACG,IAAI,CAAC,CAAC;IAC7B;EACJ;EACA;IAAS,IAAI,CAACrK,IAAI,YAAAsK,gCAAApK,CAAA;MAAA,YAAAA,CAAA,IAAwF8J,uBAAuB,EAhgBjClT,EAAE,CAAAiO,iBAAA,CAggBiDjO,EAAE,CAACkO,iBAAiB,GAhgBvElO,EAAE,CAAAiO,iBAAA,CAggBkF5L,EAAE,CAAC8L,cAAc;IAAA,CAA4D;EAAE;EACnQ;IAAS,IAAI,CAACE,IAAI,kBAjgB8ErO,EAAE,CAAAsO,iBAAA;MAAA3E,IAAA,EAigBJuJ,uBAAuB;MAAA3E,SAAA;MAAAQ,QAAA;MAAAC,YAAA,WAAAyE,qCAAA9P,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAjgBrB3D,EAAE,CAAAwG,UAAA,mBAAAkN,iDAAA;YAAA,OAigBJ9P,GAAA,CAAA0P,UAAA,CAAW,CAAC;UAAA,CAAU,CAAC,wBAAAK,sDAAA;YAAA,OAAvB/P,GAAA,CAAA8O,aAAA,CAAc,IAAI,CAAC;UAAA,CAAG,CAAC,wBAAAkB,sDAAA;YAAA,OAAvBhQ,GAAA,CAAA8O,aAAA,CAAc,KAAK,CAAC;UAAA,CAAE,CAAC;QAAA;QAAA,IAAA/O,EAAA;UAjgBrB3D,EAAE,CAAAmP,WAAA,iBAAAvL,GAAA,CAAAyB,GAAA,KAigBI,KAAK,GAAG,IAAI,GAAAzB,GAAA,CAAAuP,WAAA,MAAE,CAAC,kBAAAvP,GAAA,CAAAyB,GAAA,KAAf,KAAK,GAAAzB,GAAA,CAAAuP,WAAA,GAAiB,IAAI,MAAZ,CAAC;UAjgBrBnT,EAAE,CAAAqP,WAAA,oCAAAzL,GAAA,CAAAqD,oBAigBkB,CAAC,4BAAArD,GAAA,CAAAqD,oBAAD,CAAC;QAAA;MAAA;MAAAqI,MAAA;QAAAjL,MAAA;QAAAS,OAAA;QAAAmC,oBAAA;QAAAD,UAAA;QAAAmM,WAAA;QAAAnN,IAAA;MAAA;MAAAgN,OAAA;QAAAI,aAAA;QAAAX,iBAAA;MAAA;MAAAlD,QAAA;MAAAC,UAAA;MAAAC,QAAA,GAjgBrBzP,EAAE,CAAA2P,mBAAA;MAAAC,KAAA,EAAA7L,GAAA;MAAA8L,kBAAA,EAAArM,GAAA;MAAAsM,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA4D,iCAAAlQ,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF3D,EAAE,CAAAmQ,eAAA;UAAFnQ,EAAE,CAAAkF,UAAA,IAAAlB,8CAAA,iBAkgBrF,CAAC,IAAAM,+CAAA,yBAGiC,CAAC;UArgBgDtE,EAAE,CAAA2F,YAAA,EAwgBrF,CAAC;UAxgBkF3F,EAAE,CAAAkF,UAAA,IAAAD,8CAAA,iBAygBvE,CAAC,IAAAK,8CAAA,MAWpB,CAAC;QAAA;QAAA,IAAA3B,EAAA;UAphBuF3D,EAAE,CAAAoF,aAAA,IAAAxB,GAAA,CAAAS,MAAA,SAogBlG,CAAC;UApgB+FrE,EAAE,CAAA4E,SAAA,CAqgBrD,CAAC;UArgBkD5E,EAAE,CAAAoE,UAAA,2BAAAR,GAAA,CAAAkB,OAqgBrD,CAAC;UArgBkD9E,EAAE,CAAA4E,SAAA,EAshBlG,CAAC;UAthB+F5E,EAAE,CAAAoF,aAAA,IAAAxB,GAAA,CAAAqD,oBAAA,QAshBlG,CAAC;QAAA;MAAA;MAAAkL,YAAA,GACyD9O,YAAY,EAA+BD,EAAE,CAAC0Q,eAAe,EAAgK3Q,cAAc,EAA+BD,EAAE,CAAC6Q,+BAA+B;MAAA3D,aAAA;MAAAC,eAAA;IAAA,EAAqP;EAAE;AACjmB;AACA;EAAA,QAAA5G,SAAA,oBAAAA,SAAA,KAzhBoGzJ,EAAE,CAAA0J,iBAAA,CAyhBXwJ,uBAAuB,EAAc,CAAC;IACrHvJ,IAAI,EAAErJ,SAAS;IACf8K,IAAI,EAAE,CAAC;MACCmF,QAAQ,EAAE,oBAAoB;MAC9BhB,QAAQ,EAAE,gBAAgB;MAC1Ba,aAAa,EAAE5P,iBAAiB,CAACiQ,IAAI;MACrCJ,eAAe,EAAE9P,uBAAuB,CAACiQ,MAAM;MAC/CP,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiBU,IAAI,EAAE;QACF,yCAAyC,EAAE,sBAAsB;QACjE,gCAAgC,EAAE,uBAAuB;QACzD,wBAAwB,EAAG,qCAAoC;QAC/D,yBAAyB,EAAG,oCAAmC;QAC/D,SAAS,EAAE,cAAc;QACzB,cAAc,EAAE,qBAAqB;QACrC,cAAc,EAAE;MACpB,CAAC;MACD4B,OAAO,EAAE,CAAClP,YAAY,EAAEF,cAAc,CAAC;MACvCqM,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE7F,IAAI,EAAE3J,EAAE,CAACkO;EAAkB,CAAC,EAAE;IAAEvE,IAAI,EAAEtH,EAAE,CAAC8L,cAAc;IAAEjD,UAAU,EAAE,CAAC;MACvFvB,IAAI,EAAEvJ;IACV,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEiE,MAAM,EAAE,CAAC;MAClCsF,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAEqE,OAAO,EAAE,CAAC;MACV6E,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAEwG,oBAAoB,EAAE,CAAC;MACvB0C,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAEuG,UAAU,EAAE,CAAC;MACb2C,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAE0S,WAAW,EAAE,CAAC;MACdxJ,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAEuF,IAAI,EAAE,CAAC;MACP2D,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAE2S,aAAa,EAAE,CAAC;MAChBzJ,IAAI,EAAE/I;IACV,CAAC,CAAC;IAAE6R,iBAAiB,EAAE,CAAC;MACpB9I,IAAI,EAAE/I;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMoT,uBAAuB,GAAG,CAC5BrR,YAAY,CAACsR,QAAQ,EACrBtR,YAAY,CAACuR,KAAK,EAClBvR,YAAY,CAACwR,WAAW,EACxBxR,YAAY,CAACyR,OAAO,EACpBzR,YAAY,CAAC0R,IAAI,EACjB1R,YAAY,CAAC2R,UAAU,CAC1B;AACD,MAAMC,yBAAyB,GAAG,CAC9B5R,YAAY,CAAC6R,UAAU,EACvB7R,YAAY,CAAC8R,WAAW,EACxB9R,YAAY,CAAC+R,QAAQ,EACrB/R,YAAY,CAACgS,OAAO,CACvB;AACD,MAAMC,kBAAkB,CAAC;EACrB;EACA/K,2BAA2BA,CAACgL,IAAI,EAAE;IAC9B,IAAI,CAACnJ,gBAAgB,CAAC7B,2BAA2B,CAACgL,IAAI,CAAC;EAC3D;EACAzB,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACvJ,2BAA2B,CAAC,CAAC,IAAI,CAAC5D,MAAM,CAAC;EAClD;EACAY,kBAAkBA,CAACiD,KAAK,EAAE;IACtB,IAAI,CAACgD,QAAQ,GAAGhD,KAAK;IACrB,IAAI,IAAI,CAAC9D,IAAI,KAAK,QAAQ,EAAE;MACxB,IAAI,CAAC0F,gBAAgB,CAAC1B,gCAAgC,CAACF,KAAK,CAAC;IACjE;EACJ;EACAgL,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAAC9O,IAAI,KAAK,YAAY,IAC1B,IAAI,CAAC+O,QAAQ,CAACC,SAAS,IACvB,IAAI,CAACC,gBAAgB,IACrB,IAAI,CAACC,WAAW,KAAK,YAAY,EAAE;MACnC;MACA,IAAI,CAAC1N,YAAY,GAAG,IAAI,CAACyN,gBAAgB,CAACvD,aAAa,CAACyD,qBAAqB,CAAC,CAAC,CAACC,KAAK;IACzF;EACJ;EACA/N,gBAAgBA,CAACN,QAAQ,EAAE;IACvB,MAAMsO,SAAS,GAAGzS,gBAAgB,CAACmE,QAAQ,CAAC;IAC5C,IAAIsO,SAAS,KAAK,UAAU,IAAIA,SAAS,KAAK,aAAa,IAAIA,SAAS,KAAK,OAAO,EAAE;MAClF,IAAI,CAACtO,QAAQ,GAAG,OAAO;IAC3B,CAAC,MACI,IAAIsO,SAAS,KAAK,SAAS,IAAIA,SAAS,KAAK,YAAY,IAAIA,SAAS,KAAK,MAAM,EAAE;MACpF,IAAI,CAACtO,QAAQ,GAAG,MAAM;IAC1B;EACJ;EACAoB,WAAWA,CAACgC,aAAa,EAAEoC,GAAG,EAAEb,gBAAgB,EAAEqJ,QAAQ,EAAE9N,oBAAoB,EAAEoG,cAAc,EAAEnH,WAAW,EAAE;IAC3G,IAAI,CAACiE,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACoC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACb,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACqJ,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAC9N,oBAAoB,GAAGA,oBAAoB;IAChD,IAAI,CAACoG,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACnH,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACE,eAAe,GAAG,EAAE;IACzB,IAAI,CAACgJ,aAAa,GAAG,IAAI;IACzB,IAAI,CAACtK,OAAO,GAAG,IAAI;IACnB,IAAI,CAACT,MAAM,GAAG,IAAI;IAClB,IAAI,CAAC4B,MAAM,GAAG,KAAK;IACnB,IAAI,CAACe,UAAU,GAAG,KAAK;IACvB,IAAI,CAACkO,WAAW,GAAG,YAAY;IAC/B,IAAI,CAACI,YAAY,GAAG,IAAI3U,YAAY,CAAC,CAAC;IACtC,IAAI,CAACsU,gBAAgB,GAAG,IAAI;IAC5B;IACA;IACA,IAAI,CAACM,wBAAwB,GAAG,IAAI;IACpC,IAAI,CAACC,yBAAyB,GAAG,IAAI;IACrC,IAAI,CAACnL,KAAK,GAAG,IAAI,CAACqB,gBAAgB,CAACrB,KAAK;IACxC,IAAI,CAACC,QAAQ,GAAG,IAAIlJ,OAAO,CAAC,CAAC;IAC7B,IAAI,CAAC2F,QAAQ,GAAG,OAAO;IACvB,IAAI,CAACS,YAAY,GAAG,IAAI;IACxB,IAAI,CAACV,KAAK,GAAG,OAAO;IACpB,IAAI,CAACd,IAAI,GAAG,UAAU;IACtB,IAAI,CAACsH,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAAC/F,gBAAgB,GAAGyM,uBAAuB;IAC/C,IAAI,CAACyB,UAAU,GAAG,KAAK;IACvB,IAAI,CAAC3I,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACzH,GAAG,GAAG,KAAK;EACpB;EACAoI,QAAQA,CAAA,EAAG;IACP;IACA,IAAI,CAACtD,aAAa,CAAC7B,MAAM,CAAC8B,IAAI,CAACtI,SAAS,CAAC,IAAI,CAACwI,QAAQ,CAAC,CAAC,CAACM,SAAS,CAAC9D,KAAK,IAAI;MACxE,IAAI,CAACA,KAAK,GAAGA,KAAK;MAClB,IAAI,CAACyF,GAAG,CAACC,YAAY,CAAC,CAAC;IAC3B,CAAC,CAAC;IACF;IACA,IAAI,CAACd,gBAAgB,CAACnD,KAAK,CAAC6B,IAAI,CAACtI,SAAS,CAAC,IAAI,CAACwI,QAAQ,CAAC,CAAC,CAACM,SAAS,CAAC5E,IAAI,IAAI;MACzE,IAAI,CAACA,IAAI,GAAGA,IAAI;MAChB,IAAIA,IAAI,KAAK,YAAY,EAAE;QACvB,IAAI,CAACuB,gBAAgB,GAAG,CAAC5E,YAAY,CAAC,IAAI,CAACuS,WAAW,CAAC,EAAE,GAAGX,yBAAyB,CAAC;MAC1F,CAAC,MACI,IAAIvO,IAAI,KAAK,UAAU,EAAE;QAC1B,IAAI,CAACuB,gBAAgB,GAAGyM,uBAAuB;MACnD;MACA,IAAI,CAACzH,GAAG,CAACC,YAAY,CAAC,CAAC;IAC3B,CAAC,CAAC;IACF;IACAjL,aAAa,CAAC,CAAC,IAAI,CAACmK,gBAAgB,CAACnD,KAAK,EAAE,IAAI,CAAC4B,aAAa,CAAC3B,aAAa,CAAC,CAAC,CACzE4B,IAAI,CAACtI,SAAS,CAAC,IAAI,CAACwI,QAAQ,CAAC,CAAC,CAC9BM,SAAS,CAAC,CAAC,CAAC5E,IAAI,EAAE0H,YAAY,CAAC,KAAK;MACrC,IAAI,CAACJ,iBAAiB,GAAGtH,IAAI,KAAK,QAAQ,GAAG,IAAI,CAACqE,KAAK,GAAGqD,YAAY,GAAG,IAAI;MAC7E,IAAI,CAACnB,GAAG,CAACC,YAAY,CAAC,CAAC;IAC3B,CAAC,CAAC;IACF;IACA,IAAI,CAACd,gBAAgB,CAAC3B,qBAAqB,CAACK,IAAI,CAACtI,SAAS,CAAC,IAAI,CAACwI,QAAQ,CAAC,CAAC,CAACM,SAAS,CAACiK,IAAI,IAAI;MACzF,IAAI,CAAC/H,QAAQ,GAAG+H,IAAI;MACpB,IAAIA,IAAI,KAAK,IAAI,CAAC5O,MAAM,EAAE;QACtB,IAAI,CAAC6O,eAAe,CAAC,CAAC;QACtB,IAAI,CAAC7O,MAAM,GAAG4O,IAAI;QAClB,IAAI,CAACS,YAAY,CAAC/B,IAAI,CAAC,IAAI,CAACtN,MAAM,CAAC;QACnC,IAAI,CAACsG,GAAG,CAACC,YAAY,CAAC,CAAC;MAC3B;IACJ,CAAC,CAAC;IACF,IAAI,CAACnH,GAAG,GAAG,IAAI,CAACgI,cAAc,CAACvD,KAAK;IACpC,IAAI,CAACuD,cAAc,CAACM,MAAM,EAAEvD,IAAI,CAACtI,SAAS,CAAC,IAAI,CAACwI,QAAQ,CAAC,CAAC,CAACM,SAAS,CAAEgD,SAAS,IAAK;MAChF,IAAI,CAACvI,GAAG,GAAGuI,SAAS;MACpB,IAAI,CAACrB,GAAG,CAACC,YAAY,CAAC,CAAC;IAC3B,CAAC,CAAC;EACN;EACAqB,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACiH,eAAe,CAAC,CAAC;IACtB,MAAMU,yBAAyB,GAAG,IAAI,CAACA,yBAAyB;IAChE,MAAM1H,OAAO,GAAG0H,yBAAyB,CAAC1H,OAAO;IACjD,MAAM4H,gBAAgB,GAAGpU,KAAK,CAAC,GAAG,CAACwM,OAAO,EAAE,GAAG0H,yBAAyB,CAAChU,GAAG,CAACmH,IAAI,IAAIA,IAAI,CAACkD,SAAS,CAAC,CAAC,CAAC;IACtGiC,OAAO,CACF1D,IAAI,CAACrI,SAAS,CAACyT,yBAAyB,CAAC,EAAExT,SAAS,CAAC,MAAM0T,gBAAgB,CAAC,EAAE3T,SAAS,CAAC,IAAI,CAAC,EAAEP,GAAG,CAAC,MAAMgU,yBAAyB,CAAC5I,IAAI,CAACrB,CAAC,IAAIA,CAAC,CAACK,UAAU,CAAC,CAAC,EAAE9J,SAAS,CAAC,IAAI,CAACwI,QAAQ,CAAC,CAAC,CACtLM,SAAS,CAAC+K,QAAQ,IAAI;MACvB,IAAI,CAACF,UAAU,GAAGE,QAAQ;MAC1B,IAAI,CAACpJ,GAAG,CAACC,YAAY,CAAC,CAAC;IAC3B,CAAC,CAAC;EACN;EACAuB,WAAWA,CAACD,OAAO,EAAE;IACjB,MAAM;MAAE7H;IAAO,CAAC,GAAG6H,OAAO;IAC1B,IAAI7H,MAAM,EAAE;MACR,IAAI,CAACyF,gBAAgB,CAAC7B,2BAA2B,CAAC,IAAI,CAAC5D,MAAM,CAAC;MAC9D,IAAI,CAAC6O,eAAe,CAAC,CAAC;IAC1B;EACJ;EACAhK,WAAWA,CAAA,EAAG;IACV,IAAI,CAACR,QAAQ,CAAC1B,IAAI,CAAC,CAAC;IACpB,IAAI,CAAC0B,QAAQ,CAACS,QAAQ,CAAC,CAAC;EAC5B;EACA;IAAS,IAAI,CAAC7B,IAAI,YAAA0M,2BAAAxM,CAAA;MAAA,YAAAA,CAAA,IAAwFwL,kBAAkB,EAruB5B5U,EAAE,CAAAiO,iBAAA,CAquB4C/F,WAAW,GAruBzDlI,EAAE,CAAAiO,iBAAA,CAquBoEjO,EAAE,CAACkO,iBAAiB,GAruB1FlO,EAAE,CAAAiO,iBAAA,CAquBqGrE,gBAAgB,GAruBvH5J,EAAE,CAAAiO,iBAAA,CAquBkI3K,IAAI,CAACuS,QAAQ,GAruBjJ7V,EAAE,CAAAiO,iBAAA,CAquB4JjG,2BAA2B,GAruBzLhI,EAAE,CAAAiO,iBAAA,CAquBoM5L,EAAE,CAAC8L,cAAc,MAruBvNnO,EAAE,CAAAiO,iBAAA,CAquBkPxL,EAAE,CAACC,sBAAsB;IAAA,CAAwE;EAAE;EACvb;IAAS,IAAI,CAAC2L,IAAI,kBAtuB8ErO,EAAE,CAAAsO,iBAAA;MAAA3E,IAAA,EAsuBJiL,kBAAkB;MAAArG,SAAA;MAAAC,cAAA,WAAAsH,kCAAAnS,EAAA,EAAAC,GAAA,EAAA8K,QAAA;QAAA,IAAA/K,EAAA;UAtuBhB3D,EAAE,CAAA2O,cAAA,CAAAD,QAAA,EAsuBorDkG,kBAAkB;UAtuBxsD5U,EAAE,CAAA2O,cAAA,CAAAD,QAAA,EAsuBsxDrD,mBAAmB;QAAA;QAAA,IAAA1H,EAAA;UAAA,IAAAiL,EAAA;UAtuB3yD5O,EAAE,CAAA6O,cAAA,CAAAD,EAAA,GAAF5O,EAAE,CAAA8O,WAAA,QAAAlL,GAAA,CAAA2R,wBAAA,GAAA3G,EAAA;UAAF5O,EAAE,CAAA6O,cAAA,CAAAD,EAAA,GAAF5O,EAAE,CAAA8O,WAAA,QAAAlL,GAAA,CAAA4R,yBAAA,GAAA5G,EAAA;QAAA;MAAA;MAAAmH,SAAA,WAAAC,yBAAArS,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF3D,EAAE,CAAAiW,WAAA,CAsuB24D1T,gBAAgB,KAA2B1B,UAAU;QAAA;QAAA,IAAA8C,EAAA;UAAA,IAAAiL,EAAA;UAtuBl8D5O,EAAE,CAAA6O,cAAA,CAAAD,EAAA,GAAF5O,EAAE,CAAA8O,WAAA,QAAAlL,GAAA,CAAAqR,gBAAA,GAAArG,EAAA,CAAAsH,KAAA;QAAA;MAAA;MAAAnH,QAAA;MAAAC,YAAA,WAAAmH,gCAAAxS,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF3D,EAAE,CAAAqP,WAAA,8BAAAzL,GAAA,CAAAqD,oBAsuBa,CAAC,uCAAArD,GAAA,CAAAqD,oBAAA,IAAArD,GAAA,CAAAoD,UAAD,CAAC,mCAAApD,GAAA,CAAAqD,oBAAA,IAAArD,GAAA,CAAAqC,MAAD,CAAC,uCAAArC,GAAA,CAAAqD,oBAAA,IAAArD,GAAA,CAAA6R,UAAD,CAAC,uCAAA7R,GAAA,CAAAqD,oBAAA,IAAArD,GAAA,CAAAoC,IAAA,KAAe,UAAhB,CAAC,yCAAApC,GAAA,CAAAqD,oBAAA,IAAArD,GAAA,CAAAoC,IAAA,KAAe,YAAhB,CAAC,qCAAApC,GAAA,CAAAqD,oBAAA,IAAArD,GAAA,CAAAoC,IAAA,KAAe,QAAhB,CAAC,qCAAApC,GAAA,CAAAqD,oBAAA,IAAArD,GAAA,CAAAkJ,QAAD,CAAC,sBAAAlJ,GAAA,CAAAqD,oBAAD,CAAC,+BAAArD,GAAA,CAAAqD,oBAAA,IAAArD,GAAA,CAAAoD,UAAD,CAAC,2BAAApD,GAAA,CAAAqD,oBAAA,IAAArD,GAAA,CAAAqC,MAAD,CAAC,+BAAArC,GAAA,CAAAqD,oBAAA,IAAArD,GAAA,CAAA6R,UAAD,CAAC,+BAAA7R,GAAA,CAAAqD,oBAAA,IAAArD,GAAA,CAAAoC,IAAA,KAAgB,UAAjB,CAAC,iCAAApC,GAAA,CAAAqD,oBAAA,IAAArD,GAAA,CAAAoC,IAAA,KAAgB,YAAjB,CAAC,6BAAApC,GAAA,CAAAqD,oBAAA,IAAArD,GAAA,CAAAoC,IAAA,KAAgB,QAAjB,CAAC,6BAAApC,GAAA,CAAAqD,oBAAA,IAAArD,GAAA,CAAAkJ,QAAD,CAAC,yBAAAlJ,GAAA,CAAAyB,GAAA,KAAV,KAAS,CAAC;QAAA;MAAA;MAAAiK,MAAA;QAAAlJ,eAAA;QAAAgJ,aAAA;QAAAtK,OAAA;QAAAT,MAAA;QAAA4B,MAAA;QAAAe,UAAA;QAAAkO,WAAA;MAAA;MAAAlC,OAAA;QAAAsC,YAAA;MAAA;MAAA/F,QAAA;MAAAC,UAAA;MAAAC,QAAA,GAtuBhBzP,EAAE,CAAAoW,kBAAA,CAsuB2kD,CAACxM,gBAAgB,CAAC,GAtuB/lD5J,EAAE,CAAA0P,oBAAA,EAAF1P,EAAE,CAAA2P,mBAAA;MAAAC,KAAA,EAAArK,GAAA;MAAAsK,kBAAA,EAAApK,GAAA;MAAAqK,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAoG,4BAAA1S,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAA,MAAA2S,GAAA,GAAFtW,EAAE,CAAAuG,gBAAA;UAAFvG,EAAE,CAAAmQ,eAAA,CAAA3K,GAAA;UAAFxF,EAAE,CAAAwE,cAAA,eAmvBlG,CAAC;UAnvB+FxE,EAAE,CAAAwG,UAAA,+BAAA+P,6DAAA7P,MAAA;YAAF1G,EAAE,CAAA2G,aAAA,CAAA2P,GAAA;YAAA,OAAFtW,EAAE,CAAA4G,WAAA,CAivB3EhD,GAAA,CAAAiD,kBAAA,CAAAH,MAAyB,CAAC;UAAA,EAAC,2BAAA8P,yDAAA;YAjvB8CxW,EAAE,CAAA2G,aAAA,CAAA2P,GAAA;YAAA,OAAFtW,EAAE,CAAA4G,WAAA,CAkvB/EhD,GAAA,CAAAwP,aAAA,CAAc,CAAC;UAAA,EAAC;UAlvB6DpT,EAAE,CAAAkF,UAAA,IAAAQ,yCAAA,MAovBjF,CAAC;UApvB8E1F,EAAE,CAAA0E,YAAA,CAuvB7F,CAAC;UAvvB0F1E,EAAE,CAAAkF,UAAA,IAAAU,yCAAA,gBAwvB1E,CAAC,IAAAsB,yCAAA,MAUjB,CAAC,IAAAO,yCAAA,gCAlwBuFzH,EAAE,CAAAyW,sBA6xBrE,CAAC;QAAA;QAAA,IAAA9S,EAAA;UA7xBkE3D,EAAE,CAAAoE,UAAA,WAAAR,GAAA,CAAAS,MA2uBhF,CAAC,YAAAT,GAAA,CAAAkB,OACC,CAAC,SAAAlB,GAAA,CAAAoC,IACP,CAAC,eAAApC,GAAA,CAAAoD,UACW,CAAC,yBAAApD,GAAA,CAAAqD,oBACmB,CAAC,gBAAArD,GAAA,CAAAwL,aAAA,IAAAxL,GAAA,CAAA0J,iBACI,CAAC;UAhvB4CtN,EAAE,CAAA4E,SAAA,EAsvBhG,CAAC;UAtvB6F5E,EAAE,CAAAoF,aAAA,KAAAxB,GAAA,CAAAkB,OAAA,SAsvBhG,CAAC;UAtvB6F9E,EAAE,CAAA4E,SAAA,CA2xBlG,CAAC;UA3xB+F5E,EAAE,CAAAoF,aAAA,IAAAxB,GAAA,CAAAoC,IAAA,qBA2xBlG,CAAC;QAAA;MAAA;MAAAmM,YAAA,GAK0De,uBAAuB,EAA0OrC,6BAA6B,EAAqKnO,sBAAsB,EAAoH8P,iCAAiC,EAAwQhQ,aAAa,EAA+BF,EAAE,CAACoU,mBAAmB,EAA4+BpU,EAAE,CAACC,gBAAgB;MAAA6N,aAAA;MAAAC,eAAA;IAAA,EAA4M;EAAE;AACpsE;AACAtQ,UAAU,CAAC,CACPkC,YAAY,CAAC,CAAC,CACjB,EAAE2S,kBAAkB,CAACtE,SAAS,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;AAClDvQ,UAAU,CAAC,CACPkC,YAAY,CAAC,CAAC,CACjB,EAAE2S,kBAAkB,CAACtE,SAAS,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC;AACtD;EAAA,QAAA7G,SAAA,oBAAAA,SAAA,KAxyBoGzJ,EAAE,CAAA0J,iBAAA,CAwyBXkL,kBAAkB,EAAc,CAAC;IAChHjL,IAAI,EAAErJ,SAAS;IACf8K,IAAI,EAAE,CAAC;MACCmF,QAAQ,EAAE,cAAc;MACxBhB,QAAQ,EAAE,WAAW;MACrBoH,SAAS,EAAE,CAAC/M,gBAAgB,CAAC;MAC7BwG,aAAa,EAAE5P,iBAAiB,CAACiQ,IAAI;MACrCJ,eAAe,EAAE9P,uBAAuB,CAACiQ,MAAM;MAC/CE,mBAAmB,EAAE,KAAK;MAC1BT,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiBU,IAAI,EAAE;QACF,mCAAmC,EAAG,sBAAqB;QAC3D,4CAA4C,EAAG,oCAAmC;QAClF,wCAAwC,EAAG,gCAA+B;QAC1E,4CAA4C,EAAG,oCAAmC;QAClF,4CAA4C,EAAG,6CAA4C;QAC3F,8CAA8C,EAAG,+CAA8C;QAC/F,0CAA0C,EAAG,2CAA0C;QACvF,0CAA0C,EAAG,kCAAiC;QAC9E,0BAA0B,EAAG,uBAAsB;QACnD,mCAAmC,EAAG,qCAAoC;QAC1E,+BAA+B,EAAG,iCAAgC;QAClE,mCAAmC,EAAG,qCAAoC;QAC1E,mCAAmC,EAAG,8CAA6C;QACnF,qCAAqC,EAAG,gDAA+C;QACvF,iCAAiC,EAAG,4CAA2C;QAC/E,iCAAiC,EAAG,mCAAkC;QACtE,8BAA8B,EAAG;MACrC,CAAC;MACD4B,OAAO,EAAE,CACLW,uBAAuB,EACvBrC,6BAA6B,EAC7BnO,sBAAsB,EACtB8P,iCAAiC,EACjChQ,aAAa,CAChB;MACDgN,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE7F,IAAI,EAAEzB;EAAY,CAAC,EAAE;IAAEyB,IAAI,EAAE3J,EAAE,CAACkO;EAAkB,CAAC,EAAE;IAAEvE,IAAI,EAAEC;EAAiB,CAAC,EAAE;IAAED,IAAI,EAAErG,IAAI,CAACuS;EAAS,CAAC,EAAE;IAAElM,IAAI,EAAEwB,SAAS;IAAED,UAAU,EAAE,CAAC;MAC3JvB,IAAI,EAAEtJ,MAAM;MACZ+K,IAAI,EAAE,CAACpD,2BAA2B;IACtC,CAAC;EAAE,CAAC,EAAE;IAAE2B,IAAI,EAAEtH,EAAE,CAAC8L,cAAc;IAAEjD,UAAU,EAAE,CAAC;MAC1CvB,IAAI,EAAEvJ;IACV,CAAC;EAAE,CAAC,EAAE;IAAEuJ,IAAI,EAAElH,EAAE,CAACC,sBAAsB;IAAEwI,UAAU,EAAE,CAAC;MAClDvB,IAAI,EAAE7I;IACV,CAAC,EAAE;MACC6I,IAAI,EAAEvJ;IACV,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEgG,eAAe,EAAE,CAAC;MAC3CuD,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAE2O,aAAa,EAAE,CAAC;MAChBzF,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAEqE,OAAO,EAAE,CAAC;MACV6E,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAE4D,MAAM,EAAE,CAAC;MACTsF,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAEwF,MAAM,EAAE,CAAC;MACT0D,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAEuG,UAAU,EAAE,CAAC;MACb2C,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAEyU,WAAW,EAAE,CAAC;MACdvL,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAE6U,YAAY,EAAE,CAAC;MACf3L,IAAI,EAAE/I;IACV,CAAC,CAAC;IAAEqU,gBAAgB,EAAE,CAAC;MACnBtL,IAAI,EAAE5I,SAAS;MACfqK,IAAI,EAAE,CAAC7I,gBAAgB,EAAE;QAAEqU,MAAM,EAAE,IAAI;QAAEC,IAAI,EAAEhW;MAAW,CAAC;IAC/D,CAAC,CAAC;IAAE0U,wBAAwB,EAAE,CAAC;MAC3B5L,IAAI,EAAEjJ,eAAe;MACrB0K,IAAI,EAAE,CAACpK,UAAU,CAAC,MAAM4T,kBAAkB,CAAC,EAAE;QAAEhE,WAAW,EAAE;MAAK,CAAC;IACtE,CAAC,CAAC;IAAE4E,yBAAyB,EAAE,CAAC;MAC5B7L,IAAI,EAAEjJ,eAAe;MACrB0K,IAAI,EAAE,CAACC,mBAAmB,EAAE;QAAEuF,WAAW,EAAE;MAAK,CAAC;IACrD,CAAC;EAAE,CAAC;AAAA;AAEhB,SAASkG,kBAAkBA,CAAA,EAAG;EAC1B,MAAMC,qBAAqB,GAAG9V,MAAM,CAACiH,WAAW,EAAE;IAAE8O,QAAQ,EAAE,IAAI;IAAEC,QAAQ,EAAE;EAAK,CAAC,CAAC;EACrF,MAAMC,sBAAsB,GAAGjW,MAAM,CAACgH,uBAAuB,CAAC;EAC9D,OAAO8O,qBAAqB,IAAIG,sBAAsB;AAC1D;AACA,SAASC,wBAAwBA,CAAA,EAAG;EAChC,MAAMC,yBAAyB,GAAGnW,MAAM,CAAC+G,2BAA2B,EAAE;IAAEgP,QAAQ,EAAE,IAAI;IAAEC,QAAQ,EAAE;EAAK,CAAC,CAAC;EACzG,OAAOG,yBAAyB,IAAI,KAAK;AAC7C;AACA,MAAMC,eAAe,CAAC;EAClBC,kBAAkBA,CAACC,eAAe,EAAE;IAChC,IAAI,CAACC,iBAAiB,GAAGD,eAAe;IACxC,IAAI,CAACE,gBAAgB,CAAC7O,IAAI,CAAC2O,eAAe,CAAC;EAC/C;EACAG,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAAClC,yBAAyB,EAAE;MAChC,IAAI,IAAI,CAACgC,iBAAiB,EAAE;QACxB,IAAI,CAACG,8BAA8B,GAAG,IAAI,CAACpC,wBAAwB,CAAC7T,MAAM,CAACkW,OAAO,IAAIA,OAAO,CAAC3R,MAAM,CAAC;QACrG,IAAI,CAACsP,wBAAwB,CAAChE,OAAO,CAACqG,OAAO,IAAIA,OAAO,CAAC/N,2BAA2B,CAAC,KAAK,CAAC,CAAC;MAChG,CAAC,MACI;QACD,IAAI,CAAC8N,8BAA8B,CAACpG,OAAO,CAACqG,OAAO,IAAIA,OAAO,CAAC/N,2BAA2B,CAAC,IAAI,CAAC,CAAC;QACjG,IAAI,CAAC8N,8BAA8B,GAAG,EAAE;MAC5C;IACJ;EACJ;EACAxP,WAAWA,CAACgC,aAAa,EAAElD,oBAAoB,EAAEsF,GAAG,EAAEc,cAAc,EAAE;IAClE,IAAI,CAAClD,aAAa,GAAGA,aAAa;IAClC,IAAI,CAAClD,oBAAoB,GAAGA,oBAAoB;IAChD,IAAI,CAACsF,GAAG,GAAGA,GAAG;IACd,IAAI,CAACc,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACwK,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,OAAO,GAAG,OAAO;IACtB,IAAI,CAACC,MAAM,GAAG,UAAU;IACxB,IAAI,CAACP,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACQ,YAAY,GAAG,CAAC,IAAI,CAAC/Q,oBAAoB;IAC9C,IAAI,CAACgR,OAAO,GAAG,IAAItX,YAAY,CAAC,CAAC;IACjC,IAAI,CAACuX,UAAU,GAAG,UAAU;IAC5B,IAAI,CAAC7S,GAAG,GAAG,KAAK;IAChB,IAAI,CAACoS,gBAAgB,GAAG,IAAIpW,eAAe,CAAC,IAAI,CAACmW,iBAAiB,CAAC;IACnE,IAAI,CAACjP,KAAK,GAAG,IAAIlH,eAAe,CAAC,IAAI,CAAC0W,MAAM,CAAC;IAC7C,IAAI,CAACzN,QAAQ,GAAG,IAAIlJ,OAAO,CAAC,CAAC;IAC7B,IAAI,CAACuW,8BAA8B,GAAG,EAAE;EAC5C;EACAlK,QAAQA,CAAA,EAAG;IACPlM,aAAa,CAAC,CAAC,IAAI,CAACkW,gBAAgB,EAAE,IAAI,CAAClP,KAAK,CAAC,CAAC,CAC7C6B,IAAI,CAACtI,SAAS,CAAC,IAAI,CAACwI,QAAQ,CAAC,CAAC,CAC9BM,SAAS,CAAC,CAAC,CAAC2M,eAAe,EAAEvR,IAAI,CAAC,KAAK;MACxC,IAAI,CAACkS,UAAU,GAAGX,eAAe,GAAG,UAAU,GAAGvR,IAAI;MACrD,IAAI,CAACmE,aAAa,CAACrB,OAAO,CAAC,IAAI,CAACoP,UAAU,CAAC;MAC3C,IAAI,CAAC3L,GAAG,CAACC,YAAY,CAAC,CAAC;IAC3B,CAAC,CAAC;IACF,IAAI,CAACrC,aAAa,CAAC/B,wBAAwB,CAACgC,IAAI,CAACtI,SAAS,CAAC,IAAI,CAACwI,QAAQ,CAAC,CAAC,CAACM,SAAS,CAACjC,IAAI,IAAI;MACzF,IAAI,CAACsP,OAAO,CAAC1E,IAAI,CAAC5K,IAAI,CAAC;MACvB,IAAI,IAAI,CAACqP,YAAY,IAAI,CAACrP,IAAI,CAACuD,aAAa,EAAE;QAC1C,IAAI,CAACsJ,yBAAyB,CAACjE,OAAO,CAACD,IAAI,IAAIA,IAAI,CAAC3F,gBAAgB,CAAC2F,IAAI,KAAK3I,IAAI,CAAC,CAAC;MACxF;IACJ,CAAC,CAAC;IACF,IAAI,CAACtD,GAAG,GAAG,IAAI,CAACgI,cAAc,CAACvD,KAAK;IACpC,IAAI,CAACuD,cAAc,CAACM,MAAM,EAAEvD,IAAI,CAACtI,SAAS,CAAC,IAAI,CAACwI,QAAQ,CAAC,CAAC,CAACM,SAAS,CAAEgD,SAAS,IAAK;MAChF,IAAI,CAACvI,GAAG,GAAGuI,SAAS;MACpB,IAAI,CAACzD,aAAa,CAACrB,OAAO,CAAC,IAAI,CAACoP,UAAU,CAAC;MAC3C,IAAI,CAAC3L,GAAG,CAACC,YAAY,CAAC,CAAC;IAC3B,CAAC,CAAC;EACN;EACAqB,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC4J,gBAAgB,CAACrN,IAAI,CAACtI,SAAS,CAAC,IAAI,CAACwI,QAAQ,CAAC,CAAC,CAACM,SAAS,CAAC,MAAM;MACjE,IAAI,CAAC8M,oBAAoB,CAAC,CAAC;MAC3B,IAAI,CAACnL,GAAG,CAACC,YAAY,CAAC,CAAC;IAC3B,CAAC,CAAC;EACN;EACAuB,WAAWA,CAACD,OAAO,EAAE;IACjB,MAAM;MAAE0J,iBAAiB;MAAEK,cAAc;MAAEC,OAAO;MAAEC;IAAO,CAAC,GAAGjK,OAAO;IACtE,IAAI0J,iBAAiB,EAAE;MACnB,IAAI,CAACC,gBAAgB,CAAC7O,IAAI,CAAC,IAAI,CAAC4O,iBAAiB,CAAC;IACtD;IACA,IAAIK,cAAc,EAAE;MAChB,IAAI,CAAC1N,aAAa,CAACnB,eAAe,CAAC,IAAI,CAAC6O,cAAc,CAAC;IAC3D;IACA,IAAIC,OAAO,EAAE;MACT,IAAI,CAAC3N,aAAa,CAACpB,QAAQ,CAAC,IAAI,CAAC+O,OAAO,CAAC;IAC7C;IACA,IAAIC,MAAM,EAAE;MACR,IAAI,CAACxP,KAAK,CAACK,IAAI,CAAC,IAAI,CAACmP,MAAM,CAAC;MAC5B,IAAI,CAACjK,OAAO,CAACiK,MAAM,CAACI,aAAa,CAAC,CAAC,IAAI,IAAI,CAAC5C,wBAAwB,EAAE;QAClE,IAAI,CAACA,wBAAwB,CAAChE,OAAO,CAACqG,OAAO,IAAIA,OAAO,CAAC/N,2BAA2B,CAAC,KAAK,CAAC,CAAC;MAChG;IACJ;EACJ;EACAiB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACR,QAAQ,CAAC1B,IAAI,CAAC,IAAI,CAAC;IACxB,IAAI,CAAC0B,QAAQ,CAACS,QAAQ,CAAC,CAAC;EAC5B;EACA;IAAS,IAAI,CAAC7B,IAAI,YAAAkP,wBAAAhP,CAAA;MAAA,YAAAA,CAAA,IAAwFiO,eAAe,EAzgCzBrX,EAAE,CAAAiO,iBAAA,CAygCyC/F,WAAW,GAzgCtDlI,EAAE,CAAAiO,iBAAA,CAygCiEjG,2BAA2B,GAzgC9FhI,EAAE,CAAAiO,iBAAA,CAygCyGjO,EAAE,CAACkO,iBAAiB,GAzgC/HlO,EAAE,CAAAiO,iBAAA,CAygC0I5L,EAAE,CAAC8L,cAAc;IAAA,CAA4D;EAAE;EAC3T;IAAS,IAAI,CAACkK,IAAI,kBA1gC8ErY,EAAE,CAAAsY,iBAAA;MAAA3O,IAAA,EA0gCJ0N,eAAe;MAAA9I,SAAA;MAAAC,cAAA,WAAA+J,+BAAA5U,EAAA,EAAAC,GAAA,EAAA8K,QAAA;QAAA,IAAA/K,EAAA;UA1gCb3D,EAAE,CAAA2O,cAAA,CAAAD,QAAA,EAyhCzBrD,mBAAmB;UAzhCIrL,EAAE,CAAA2O,cAAA,CAAAD,QAAA,EAyhCwEkG,kBAAkB;QAAA;QAAA,IAAAjR,EAAA;UAAA,IAAAiL,EAAA;UAzhC5F5O,EAAE,CAAA6O,cAAA,CAAAD,EAAA,GAAF5O,EAAE,CAAA8O,WAAA,QAAAlL,GAAA,CAAA4R,yBAAA,GAAA5G,EAAA;UAAF5O,EAAE,CAAA6O,cAAA,CAAAD,EAAA,GAAF5O,EAAE,CAAA8O,WAAA,QAAAlL,GAAA,CAAA2R,wBAAA,GAAA3G,EAAA;QAAA;MAAA;MAAAG,QAAA;MAAAC,YAAA,WAAAwJ,6BAAA7U,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF3D,EAAE,CAAAqP,WAAA,sBAAAzL,GAAA,CAAAqD,oBA0gCU,CAAC,2BAAArD,GAAA,CAAAqD,oBAAD,CAAC,4BAAArD,GAAA,CAAAqD,oBAAA,IAAArD,GAAA,CAAAkU,OAAA,KAAqB,OAAtB,CAAC,2BAAAlU,GAAA,CAAAqD,oBAAA,IAAArD,GAAA,CAAAkU,OAAA,KAAqB,MAAtB,CAAC,+BAAAlU,GAAA,CAAAqD,oBAAA,IAAArD,GAAA,CAAAsU,UAAA,KAAwB,UAAzB,CAAC,iCAAAtU,GAAA,CAAAqD,oBAAA,IAAArD,GAAA,CAAAsU,UAAA,KAAwB,YAAzB,CAAC,6BAAAtU,GAAA,CAAAqD,oBAAA,IAAArD,GAAA,CAAAsU,UAAA,KAAwB,QAAzB,CAAC,uCAAAtU,GAAA,CAAAqD,oBAAA,IAAArD,GAAA,CAAA4T,iBAAD,CAAC,cAAA5T,GAAA,CAAAqD,oBAAD,CAAC,mBAAArD,GAAA,CAAAqD,oBAAD,CAAC,oBAAArD,GAAA,CAAAqD,oBAAA,IAAArD,GAAA,CAAAkU,OAAA,KAAsB,OAAvB,CAAC,mBAAAlU,GAAA,CAAAqD,oBAAA,IAAArD,GAAA,CAAAkU,OAAA,KAAsB,MAAvB,CAAC,uBAAAlU,GAAA,CAAAqD,oBAAA,IAAArD,GAAA,CAAAsU,UAAA,KAAyB,UAA1B,CAAC,yBAAAtU,GAAA,CAAAqD,oBAAA,IAAArD,GAAA,CAAAsU,UAAA,KAAyB,YAA1B,CAAC,qBAAAtU,GAAA,CAAAqD,oBAAA,IAAArD,GAAA,CAAAsU,UAAA,KAAyB,QAA1B,CAAC,+BAAAtU,GAAA,CAAAqD,oBAAA,IAAArD,GAAA,CAAA4T,iBAAD,CAAC,iBAAA5T,GAAA,CAAAyB,GAAA,KAAP,KAAM,CAAC;QAAA;MAAA;MAAAiK,MAAA;QAAAuI,cAAA;QAAAC,OAAA;QAAAC,MAAA;QAAAP,iBAAA;QAAAQ,YAAA;MAAA;MAAAhF,OAAA;QAAAiF,OAAA;MAAA;MAAA1I,QAAA;MAAAC,UAAA;MAAAC,QAAA,GA1gCbzP,EAAE,CAAAoW,kBAAA,CA0gCy9C,CACnjD;QACIqC,OAAO,EAAExQ,uBAAuB;QAChCyQ,QAAQ,EAAExQ;MACd,CAAC,EACD;MACA;QACIuQ,OAAO,EAAEvQ,WAAW;QACpByQ,UAAU,EAAE7B;MAChB,CAAC,EACD;MACA;QACI2B,OAAO,EAAEzQ,2BAA2B;QACpC2Q,UAAU,EAAExB;MAChB,CAAC,CACJ,GAzhC2FnX,EAAE,CAAA0P,oBAAA;IAAA,EAyhC4K;EAAE;AACpR;AACA3P,UAAU,CAAC,CACPkC,YAAY,CAAC,CAAC,CACjB,EAAEoV,eAAe,CAAC/G,SAAS,EAAE,mBAAmB,EAAE,KAAK,CAAC,CAAC;AAC1DvQ,UAAU,CAAC,CACPkC,YAAY,CAAC,CAAC,CACjB,EAAEoV,eAAe,CAAC/G,SAAS,EAAE,cAAc,EAAE,KAAK,CAAC,CAAC;AACrD;EAAA,QAAA7G,SAAA,oBAAAA,SAAA,KAjiCoGzJ,EAAE,CAAA0J,iBAAA,CAiiCX2N,eAAe,EAAc,CAAC;IAC7G1N,IAAI,EAAEzI,SAAS;IACfkK,IAAI,EAAE,CAAC;MACCmF,QAAQ,EAAE,WAAW;MACrBhB,QAAQ,EAAE,QAAQ;MAClBoH,SAAS,EAAE,CACP;QACI8B,OAAO,EAAExQ,uBAAuB;QAChCyQ,QAAQ,EAAExQ;MACd,CAAC,EACD;MACA;QACIuQ,OAAO,EAAEvQ,WAAW;QACpByQ,UAAU,EAAE7B;MAChB,CAAC,EACD;MACA;QACI2B,OAAO,EAAEzQ,2BAA2B;QACpC2Q,UAAU,EAAExB;MAChB,CAAC,CACJ;MACDxG,IAAI,EAAE;QACF,2BAA2B,EAAG,sBAAqB;QACnD,gCAAgC,EAAG,sBAAqB;QACxD,iCAAiC,EAAG,6CAA4C;QAChF,gCAAgC,EAAG,4CAA2C;QAC9E,oCAAoC,EAAG,mDAAkD;QACzF,sCAAsC,EAAG,qDAAoD;QAC7F,kCAAkC,EAAG,iDAAgD;QACrF,4CAA4C,EAAG,2CAA0C;QACzF,kBAAkB,EAAG,uBAAsB;QAC3C,uBAAuB,EAAG,uBAAsB;QAChD,wBAAwB,EAAG,8CAA6C;QACxE,uBAAuB,EAAG,6CAA4C;QACtE,2BAA2B,EAAG,oDAAmD;QACjF,6BAA6B,EAAG,sDAAqD;QACrF,yBAAyB,EAAG,kDAAiD;QAC7E,mCAAmC,EAAG,4CAA2C;QACjF,sBAAsB,EAAG;MAC7B,CAAC;MACDnB,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE7F,IAAI,EAAEzB;EAAY,CAAC,EAAE;IAAEyB,IAAI,EAAEwB,SAAS;IAAED,UAAU,EAAE,CAAC;MACtEvB,IAAI,EAAEtJ,MAAM;MACZ+K,IAAI,EAAE,CAACpD,2BAA2B;IACtC,CAAC;EAAE,CAAC,EAAE;IAAE2B,IAAI,EAAE3J,EAAE,CAACkO;EAAkB,CAAC,EAAE;IAAEvE,IAAI,EAAEtH,EAAE,CAAC8L,cAAc;IAAEjD,UAAU,EAAE,CAAC;MAC1EvB,IAAI,EAAEvJ;IACV,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEoV,yBAAyB,EAAE,CAAC;MACrD7L,IAAI,EAAEjJ,eAAe;MACrB0K,IAAI,EAAE,CAACC,mBAAmB,EAAE;QAAEuF,WAAW,EAAE;MAAK,CAAC;IACrD,CAAC,CAAC;IAAE2E,wBAAwB,EAAE,CAAC;MAC3B5L,IAAI,EAAEjJ,eAAe;MACrB0K,IAAI,EAAE,CAACwJ,kBAAkB,EAAE;QAAEhE,WAAW,EAAE;MAAK,CAAC;IACpD,CAAC,CAAC;IAAEiH,cAAc,EAAE,CAAC;MACjBlO,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAEqX,OAAO,EAAE,CAAC;MACVnO,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAEsX,MAAM,EAAE,CAAC;MACTpO,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAE+W,iBAAiB,EAAE,CAAC;MACpB7N,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAEuX,YAAY,EAAE,CAAC;MACfrO,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAEwX,OAAO,EAAE,CAAC;MACVtO,IAAI,EAAE/I;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,SAASgY,gBAAgBA,CAAA,EAAG;EACxB,MAAMxB,yBAAyB,GAAGnW,MAAM,CAAC+G,2BAA2B,EAAE;IAAEiP,QAAQ,EAAE,IAAI;IAAED,QAAQ,EAAE;EAAK,CAAC,CAAC;EACzG,OAAOI,yBAAyB,IAAI,KAAK;AAC7C;AACA,MAAMyB,oBAAoB,CAAC;EACvB1Q,WAAWA,CAAC2I,UAAU,EAAEC,QAAQ,EAAE9J,oBAAoB,EAAE;IACpD,IAAI,CAAC6J,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAC9J,oBAAoB,GAAGA,oBAAoB;IAChD,MAAMuK,SAAS,GAAG,IAAI,CAACvK,oBAAoB,GAAG,8BAA8B,GAAG,qBAAqB;IACpG,IAAI,CAAC8J,QAAQ,CAACa,QAAQ,CAACd,UAAU,CAACY,aAAa,EAAEF,SAAS,CAAC;EAC/D;EACAsH,eAAeA,CAAA,EAAG;IACd,MAAMC,SAAS,GAAG,IAAI,CAACC,YAAY,CAACtH,aAAa,CAACuH,kBAAkB;IACpE,IAAIF,SAAS,EAAE;MACX;MACA,MAAMvH,SAAS,GAAG,IAAI,CAACvK,oBAAoB,GAAG,mCAAmC,GAAG,0BAA0B;MAC9G,IAAI,CAAC8J,QAAQ,CAACa,QAAQ,CAACmH,SAAS,EAAEvH,SAAS,CAAC;IAChD;EACJ;EACA;IAAS,IAAI,CAACtI,IAAI,YAAAgQ,6BAAA9P,CAAA;MAAA,YAAAA,CAAA,IAAwFyP,oBAAoB,EA5nC9B7Y,EAAE,CAAAiO,iBAAA,CA4nC8CjO,EAAE,CAACa,UAAU,GA5nC7Db,EAAE,CAAAiO,iBAAA,CA4nCwEjO,EAAE,CAAC8R,SAAS,GA5nCtF9R,EAAE,CAAAiO,iBAAA,CA4nCiGjG,2BAA2B;IAAA,CAA4C;EAAE;EAC5Q;IAAS,IAAI,CAACqG,IAAI,kBA7nC8ErO,EAAE,CAAAsO,iBAAA;MAAA3E,IAAA,EA6nCJkP,oBAAoB;MAAAtK,SAAA;MAAAwH,SAAA,WAAAoD,2BAAAxV,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA7nClB3D,EAAE,CAAAiW,WAAA,CAAAvO,GAAA;QAAA;QAAA,IAAA/D,EAAA;UAAA,IAAAiL,EAAA;UAAF5O,EAAE,CAAA6O,cAAA,CAAAD,EAAA,GAAF5O,EAAE,CAAA8O,WAAA,QAAAlL,GAAA,CAAAoV,YAAA,GAAApK,EAAA,CAAAsH,KAAA;QAAA;MAAA;MAAA5G,MAAA;QAAAxK,OAAA;MAAA;MAAAyK,QAAA;MAAAC,UAAA;MAAAC,QAAA,GAAFzP,EAAE,CAAAoW,kBAAA,CA6nC8G,CACxM;MACA;QACIqC,OAAO,EAAEzQ,2BAA2B;QACpC2Q,UAAU,EAAEC;MAChB,CAAC,CACJ,GAnoC2F5Y,EAAE,CAAA2P,mBAAA;MAAAC,KAAA,EAAAjI,GAAA;MAAAkI,kBAAA,EAAAhI,IAAA;MAAAiI,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAmJ,8BAAAzV,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF3D,EAAE,CAAAmQ,eAAA,CAAAvI,IAAA;UAAF5H,EAAE,CAAAwE,cAAA,kBAwoClG,CAAC;UAxoC+FxE,EAAE,CAAAkF,UAAA,IAAA4C,4CAAA,yBAyoCjD,CAAC,IAAAC,2CAAA,MACjC,CAAC;UA1oC8E/H,EAAE,CAAA0E,YAAA,CA6oC7F,CAAC;UA7oC0F1E,EAAE,CAAA2F,YAAA,EA8oC1E,CAAC;QAAA;QAAA,IAAAhC,EAAA;UA9oCuE3D,EAAE,CAAAqP,WAAA,+BAAAzL,GAAA,CAAAqD,oBAqoCxC,CAAC,uCAAArD,GAAA,CAAAqD,oBACO,CAAC;UAtoC6BjH,EAAE,CAAA4E,SAAA,EAyoCnD,CAAC;UAzoCgD5E,EAAE,CAAAoE,UAAA,2BAAAR,GAAA,CAAAkB,OAyoCnD,CAAC;UAzoCgD9E,EAAE,CAAA4E,SAAA,CA4oChG,CAAC;UA5oC6F5E,EAAE,CAAAoF,aAAA,KAAAxB,GAAA,CAAAkB,OAAA,SA4oChG,CAAC;QAAA;MAAA;MAAAqN,YAAA,GAGuDhP,cAAc,EAA+BD,EAAE,CAAC6Q,+BAA+B;MAAA3D,aAAA;MAAAC,eAAA;IAAA,EAAqP;EAAE;AACpY;AACA;EAAA,QAAA5G,SAAA,oBAAAA,SAAA,KAjpCoGzJ,EAAE,CAAA0J,iBAAA,CAipCXmP,oBAAoB,EAAc,CAAC;IAClHlP,IAAI,EAAErJ,SAAS;IACf8K,IAAI,EAAE,CAAC;MACCmF,QAAQ,EAAE,iBAAiB;MAC3BhB,QAAQ,EAAE,aAAa;MACvBc,eAAe,EAAE9P,uBAAuB,CAACiQ,MAAM;MAC/CmG,SAAS,EAAE,CACP;MACA;QACI8B,OAAO,EAAEzQ,2BAA2B;QACpC2Q,UAAU,EAAEC;MAChB,CAAC,CACJ;MACDxI,aAAa,EAAE5P,iBAAiB,CAACiQ,IAAI;MACrCR,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiBS,mBAAmB,EAAE,KAAK;MAC1B6B,OAAO,EAAE,CAACpP,cAAc,CAAC;MACzBqM,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE7F,IAAI,EAAE3J,EAAE,CAACa;EAAW,CAAC,EAAE;IAAE8I,IAAI,EAAE3J,EAAE,CAAC8R;EAAU,CAAC,EAAE;IAAEnI,IAAI,EAAEwB,SAAS;IAAED,UAAU,EAAE,CAAC;MAChGvB,IAAI,EAAEtJ,MAAM;MACZ+K,IAAI,EAAE,CAACpD,2BAA2B;IACtC,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAElD,OAAO,EAAE,CAAC;MACnC6E,IAAI,EAAElJ;IACV,CAAC,CAAC;IAAEuY,YAAY,EAAE,CAAC;MACfrP,IAAI,EAAE5I,SAAS;MACfqK,IAAI,EAAE,CAAC,cAAc;IACzB,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAMiO,sBAAsB,CAAC;EACzBlR,WAAWA,CAAC2I,UAAU,EAAE;IACpB,IAAI,CAACA,UAAU,GAAGA,UAAU;EAChC;EACA;IAAS,IAAI,CAAC5H,IAAI,YAAAoQ,+BAAAlQ,CAAA;MAAA,YAAAA,CAAA,IAAwFiQ,sBAAsB,EAlsChCrZ,EAAE,CAAAiO,iBAAA,CAksCgDjO,EAAE,CAACa,UAAU;IAAA,CAA4C;EAAE;EAC7M;IAAS,IAAI,CAACwX,IAAI,kBAnsC8ErY,EAAE,CAAAsY,iBAAA;MAAA3O,IAAA,EAmsCJ0P,sBAAsB;MAAA9K,SAAA;MAAAwD,SAAA;MAAAxC,QAAA;MAAAC,UAAA;IAAA,EAA6J;EAAE;AACvR;AACA;EAAA,QAAA/F,SAAA,oBAAAA,SAAA,KArsCoGzJ,EAAE,CAAA0J,iBAAA,CAqsCX2P,sBAAsB,EAAc,CAAC;IACpH1P,IAAI,EAAEzI,SAAS;IACfkK,IAAI,EAAE,CAAC;MACCmF,QAAQ,EAAE,mBAAmB;MAC7BhB,QAAQ,EAAE,eAAe;MACzBoB,IAAI,EAAE;QACF2B,KAAK,EAAE;MACX,CAAC;MACD9C,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE7F,IAAI,EAAE3J,EAAE,CAACa;EAAW,CAAC,CAAC;AAAA;;AAE3D;AACA;AACA;AACA;AACA,MAAM0Y,YAAY,CAAC;EACf;IAAS,IAAI,CAACrQ,IAAI,YAAAsQ,qBAAApQ,CAAA;MAAA,YAAAA,CAAA,IAAwFmQ,YAAY;IAAA,CAAkD;EAAE;EAC1K;IAAS,IAAI,CAACE,IAAI,kBAvtC8EzZ,EAAE,CAAA0Z,gBAAA;MAAA/P,IAAA,EAutCS4P,YAAY;MAAAhH,OAAA,GAAY8E,eAAe,EAC1IhM,mBAAmB,EACnBuJ,kBAAkB,EAClByE,sBAAsB,EACtBR,oBAAoB,EACpB3F,uBAAuB,EACvBrC,6BAA6B,EAC7B2B,iCAAiC;MAAAmH,OAAA,GAAatC,eAAe,EAAEhM,mBAAmB,EAAEuJ,kBAAkB,EAAEyE,sBAAsB,EAAER,oBAAoB;IAAA,EAAI;EAAE;EAClK;IAAS,IAAI,CAACe,IAAI,kBA/tC8E5Z,EAAE,CAAA6Z,gBAAA;MAAAtH,OAAA,GA+tCiCqC,kBAAkB,EAC7IiE,oBAAoB,EACpB3F,uBAAuB;IAAA,EAAI;EAAE;AACzC;AACA;EAAA,QAAAzJ,SAAA,oBAAAA,SAAA,KAnuCoGzJ,EAAE,CAAA0J,iBAAA,CAmuCX6P,YAAY,EAAc,CAAC;IAC1G5P,IAAI,EAAExI,QAAQ;IACdiK,IAAI,EAAE,CAAC;MACCmH,OAAO,EAAE,CACL8E,eAAe,EACfhM,mBAAmB,EACnBuJ,kBAAkB,EAClByE,sBAAsB,EACtBR,oBAAoB,EACpB3F,uBAAuB,EACvBrC,6BAA6B,EAC7B2B,iCAAiC,CACpC;MACDmH,OAAO,EAAE,CAACtC,eAAe,EAAEhM,mBAAmB,EAAEuJ,kBAAkB,EAAEyE,sBAAsB,EAAER,oBAAoB;IACpH,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAAS1B,wBAAwB,EAAEyB,gBAAgB,EAAE1Q,WAAW,EAAE4O,kBAAkB,EAAE9O,2BAA2B,EAAEqP,eAAe,EAAEgC,sBAAsB,EAAER,oBAAoB,EAAExN,mBAAmB,EAAEkO,YAAY,EAAEtR,uBAAuB,EAAE2M,kBAAkB,EAAE1B,uBAAuB,EAAErC,6BAA6B,EAAE2B,iCAAiC,EAAE5I,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}