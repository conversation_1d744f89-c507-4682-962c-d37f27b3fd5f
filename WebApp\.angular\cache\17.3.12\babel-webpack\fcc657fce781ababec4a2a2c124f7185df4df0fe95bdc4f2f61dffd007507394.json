{"ast": null, "code": "import Bold from './bold.js';\nclass Strike extends Bold {\n  static blotName = 'strike';\n  static tagName = ['S', 'STRIKE'];\n}\nexport default Strike;", "map": {"version": 3, "names": ["Bold", "Strike", "blotName", "tagName"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/quill/formats/strike.js"], "sourcesContent": ["import Bold from './bold.js';\nclass Strike extends Bold {\n  static blotName = 'strike';\n  static tagName = ['S', 'STRIKE'];\n}\nexport default Strike;\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,WAAW;AAC5B,MAAMC,MAAM,SAASD,IAAI,CAAC;EACxB,OAAOE,QAAQ,GAAG,QAAQ;EAC1B,OAAOC,OAAO,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC;AAClC;AACA,eAAeF,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}