{"ast": null, "code": "import Inline from '../blots/inline.js';\nclass Bold extends Inline {\n  static blotName = 'bold';\n  static tagName = ['STRONG', 'B'];\n  static create() {\n    return super.create();\n  }\n  static formats() {\n    return true;\n  }\n  optimize(context) {\n    super.optimize(context);\n    if (this.domNode.tagName !== this.statics.tagName[0]) {\n      this.replaceWith(this.statics.blotName);\n    }\n  }\n}\nexport default Bold;", "map": {"version": 3, "names": ["Inline", "Bold", "blotName", "tagName", "create", "formats", "optimize", "context", "domNode", "statics", "replaceWith"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/quill/formats/bold.js"], "sourcesContent": ["import Inline from '../blots/inline.js';\nclass Bold extends Inline {\n  static blotName = 'bold';\n  static tagName = ['STRONG', 'B'];\n  static create() {\n    return super.create();\n  }\n  static formats() {\n    return true;\n  }\n  optimize(context) {\n    super.optimize(context);\n    if (this.domNode.tagName !== this.statics.tagName[0]) {\n      this.replaceWith(this.statics.blotName);\n    }\n  }\n}\nexport default Bold;\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,MAAMC,IAAI,SAASD,MAAM,CAAC;EACxB,OAAOE,QAAQ,GAAG,MAAM;EACxB,OAAOC,OAAO,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC;EAChC,OAAOC,MAAMA,CAAA,EAAG;IACd,OAAO,KAAK,CAACA,MAAM,CAAC,CAAC;EACvB;EACA,OAAOC,OAAOA,CAAA,EAAG;IACf,OAAO,IAAI;EACb;EACAC,QAAQA,CAACC,OAAO,EAAE;IAChB,KAAK,CAACD,QAAQ,CAACC,OAAO,CAAC;IACvB,IAAI,IAAI,CAACC,OAAO,CAACL,OAAO,KAAK,IAAI,CAACM,OAAO,CAACN,OAAO,CAAC,CAAC,CAAC,EAAE;MACpD,IAAI,CAACO,WAAW,CAAC,IAAI,CAACD,OAAO,CAACP,QAAQ,CAAC;IACzC;EACF;AACF;AACA,eAAeD,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}