{"ast": null, "code": "import { EventEmitter } from 'eventemitter3';\nimport instances from './instances.js';\nimport logger from './logger.js';\nconst debug = logger('quill:events');\nconst EVENTS = ['selectionchange', 'mousedown', 'mouseup', 'click'];\nEVENTS.forEach(eventName => {\n  document.addEventListener(eventName, function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    Array.from(document.querySelectorAll('.ql-container')).forEach(node => {\n      const quill = instances.get(node);\n      if (quill && quill.emitter) {\n        quill.emitter.handleDOM(...args);\n      }\n    });\n  });\n});\nclass Emitter extends EventEmitter {\n  static events = {\n    EDITOR_CHANGE: 'editor-change',\n    SCROLL_BEFORE_UPDATE: 'scroll-before-update',\n    SCROLL_BLOT_MOUNT: 'scroll-blot-mount',\n    SCROLL_BLOT_UNMOUNT: 'scroll-blot-unmount',\n    SCROLL_OPTIMIZE: 'scroll-optimize',\n    SCROLL_UPDATE: 'scroll-update',\n    SCROLL_EMBED_UPDATE: 'scroll-embed-update',\n    SELECTION_CHANGE: 'selection-change',\n    TEXT_CHANGE: 'text-change',\n    COMPOSITION_BEFORE_START: 'composition-before-start',\n    COMPOSITION_START: 'composition-start',\n    COMPOSITION_BEFORE_END: 'composition-before-end',\n    COMPOSITION_END: 'composition-end'\n  };\n  static sources = {\n    API: 'api',\n    SILENT: 'silent',\n    USER: 'user'\n  };\n  constructor() {\n    super();\n    this.domListeners = {};\n    this.on('error', debug.error);\n  }\n  emit() {\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n    debug.log.call(debug, ...args);\n    // @ts-expect-error\n    return super.emit(...args);\n  }\n  handleDOM(event) {\n    for (var _len3 = arguments.length, args = new Array(_len3 > 1 ? _len3 - 1 : 0), _key3 = 1; _key3 < _len3; _key3++) {\n      args[_key3 - 1] = arguments[_key3];\n    }\n    (this.domListeners[event.type] || []).forEach(_ref => {\n      let {\n        node,\n        handler\n      } = _ref;\n      if (event.target === node || node.contains(event.target)) {\n        handler(event, ...args);\n      }\n    });\n  }\n  listenDOM(eventName, node, handler) {\n    if (!this.domListeners[eventName]) {\n      this.domListeners[eventName] = [];\n    }\n    this.domListeners[eventName].push({\n      node,\n      handler\n    });\n  }\n}\nexport default Emitter;", "map": {"version": 3, "names": ["EventEmitter", "instances", "logger", "debug", "EVENTS", "for<PERSON>ach", "eventName", "document", "addEventListener", "_len", "arguments", "length", "args", "Array", "_key", "from", "querySelectorAll", "node", "quill", "get", "emitter", "handleDOM", "Emitter", "events", "EDITOR_CHANGE", "SCROLL_BEFORE_UPDATE", "SCROLL_BLOT_MOUNT", "SCROLL_BLOT_UNMOUNT", "SCROLL_OPTIMIZE", "SCROLL_UPDATE", "SCROLL_EMBED_UPDATE", "SELECTION_CHANGE", "TEXT_CHANGE", "COMPOSITION_BEFORE_START", "COMPOSITION_START", "COMPOSITION_BEFORE_END", "COMPOSITION_END", "sources", "API", "SILENT", "USER", "constructor", "domListeners", "on", "error", "emit", "_len2", "_key2", "log", "call", "event", "_len3", "_key3", "type", "_ref", "handler", "target", "contains", "listenDOM", "push"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/quill/core/emitter.js"], "sourcesContent": ["import { EventEmitter } from 'eventemitter3';\nimport instances from './instances.js';\nimport logger from './logger.js';\nconst debug = logger('quill:events');\nconst EVENTS = ['selectionchange', 'mousedown', 'mouseup', 'click'];\nEVENTS.forEach(eventName => {\n  document.addEventListener(eventName, function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    Array.from(document.querySelectorAll('.ql-container')).forEach(node => {\n      const quill = instances.get(node);\n      if (quill && quill.emitter) {\n        quill.emitter.handleDOM(...args);\n      }\n    });\n  });\n});\nclass Emitter extends EventEmitter {\n  static events = {\n    EDITOR_CHANGE: 'editor-change',\n    SCROLL_BEFORE_UPDATE: 'scroll-before-update',\n    SCROLL_BLOT_MOUNT: 'scroll-blot-mount',\n    SCROLL_BLOT_UNMOUNT: 'scroll-blot-unmount',\n    SCROLL_OPTIMIZE: 'scroll-optimize',\n    SCROLL_UPDATE: 'scroll-update',\n    SCROLL_EMBED_UPDATE: 'scroll-embed-update',\n    SELECTION_CHANGE: 'selection-change',\n    TEXT_CHANGE: 'text-change',\n    COMPOSITION_BEFORE_START: 'composition-before-start',\n    COMPOSITION_START: 'composition-start',\n    COMPOSITION_BEFORE_END: 'composition-before-end',\n    COMPOSITION_END: 'composition-end'\n  };\n  static sources = {\n    API: 'api',\n    SILENT: 'silent',\n    USER: 'user'\n  };\n  constructor() {\n    super();\n    this.domListeners = {};\n    this.on('error', debug.error);\n  }\n  emit() {\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n    debug.log.call(debug, ...args);\n    // @ts-expect-error\n    return super.emit(...args);\n  }\n  handleDOM(event) {\n    for (var _len3 = arguments.length, args = new Array(_len3 > 1 ? _len3 - 1 : 0), _key3 = 1; _key3 < _len3; _key3++) {\n      args[_key3 - 1] = arguments[_key3];\n    }\n    (this.domListeners[event.type] || []).forEach(_ref => {\n      let {\n        node,\n        handler\n      } = _ref;\n      if (event.target === node || node.contains(event.target)) {\n        handler(event, ...args);\n      }\n    });\n  }\n  listenDOM(eventName, node, handler) {\n    if (!this.domListeners[eventName]) {\n      this.domListeners[eventName] = [];\n    }\n    this.domListeners[eventName].push({\n      node,\n      handler\n    });\n  }\n}\nexport default Emitter;\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,eAAe;AAC5C,OAAOC,SAAS,MAAM,gBAAgB;AACtC,OAAOC,MAAM,MAAM,aAAa;AAChC,MAAMC,KAAK,GAAGD,MAAM,CAAC,cAAc,CAAC;AACpC,MAAME,MAAM,GAAG,CAAC,iBAAiB,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,CAAC;AACnEA,MAAM,CAACC,OAAO,CAACC,SAAS,IAAI;EAC1BC,QAAQ,CAACC,gBAAgB,CAACF,SAAS,EAAE,YAAY;IAC/C,KAAK,IAAIG,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;IAC9B;IACAD,KAAK,CAACE,IAAI,CAACR,QAAQ,CAACS,gBAAgB,CAAC,eAAe,CAAC,CAAC,CAACX,OAAO,CAACY,IAAI,IAAI;MACrE,MAAMC,KAAK,GAAGjB,SAAS,CAACkB,GAAG,CAACF,IAAI,CAAC;MACjC,IAAIC,KAAK,IAAIA,KAAK,CAACE,OAAO,EAAE;QAC1BF,KAAK,CAACE,OAAO,CAACC,SAAS,CAAC,GAAGT,IAAI,CAAC;MAClC;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC;AACF,MAAMU,OAAO,SAAStB,YAAY,CAAC;EACjC,OAAOuB,MAAM,GAAG;IACdC,aAAa,EAAE,eAAe;IAC9BC,oBAAoB,EAAE,sBAAsB;IAC5CC,iBAAiB,EAAE,mBAAmB;IACtCC,mBAAmB,EAAE,qBAAqB;IAC1CC,eAAe,EAAE,iBAAiB;IAClCC,aAAa,EAAE,eAAe;IAC9BC,mBAAmB,EAAE,qBAAqB;IAC1CC,gBAAgB,EAAE,kBAAkB;IACpCC,WAAW,EAAE,aAAa;IAC1BC,wBAAwB,EAAE,0BAA0B;IACpDC,iBAAiB,EAAE,mBAAmB;IACtCC,sBAAsB,EAAE,wBAAwB;IAChDC,eAAe,EAAE;EACnB,CAAC;EACD,OAAOC,OAAO,GAAG;IACfC,GAAG,EAAE,KAAK;IACVC,MAAM,EAAE,QAAQ;IAChBC,IAAI,EAAE;EACR,CAAC;EACDC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,YAAY,GAAG,CAAC,CAAC;IACtB,IAAI,CAACC,EAAE,CAAC,OAAO,EAAExC,KAAK,CAACyC,KAAK,CAAC;EAC/B;EACAC,IAAIA,CAAA,EAAG;IACL,KAAK,IAAIC,KAAK,GAAGpC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACiC,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;MAC7FnC,IAAI,CAACmC,KAAK,CAAC,GAAGrC,SAAS,CAACqC,KAAK,CAAC;IAChC;IACA5C,KAAK,CAAC6C,GAAG,CAACC,IAAI,CAAC9C,KAAK,EAAE,GAAGS,IAAI,CAAC;IAC9B;IACA,OAAO,KAAK,CAACiC,IAAI,CAAC,GAAGjC,IAAI,CAAC;EAC5B;EACAS,SAASA,CAAC6B,KAAK,EAAE;IACf,KAAK,IAAIC,KAAK,GAAGzC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACsC,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;MACjHxC,IAAI,CAACwC,KAAK,GAAG,CAAC,CAAC,GAAG1C,SAAS,CAAC0C,KAAK,CAAC;IACpC;IACA,CAAC,IAAI,CAACV,YAAY,CAACQ,KAAK,CAACG,IAAI,CAAC,IAAI,EAAE,EAAEhD,OAAO,CAACiD,IAAI,IAAI;MACpD,IAAI;QACFrC,IAAI;QACJsC;MACF,CAAC,GAAGD,IAAI;MACR,IAAIJ,KAAK,CAACM,MAAM,KAAKvC,IAAI,IAAIA,IAAI,CAACwC,QAAQ,CAACP,KAAK,CAACM,MAAM,CAAC,EAAE;QACxDD,OAAO,CAACL,KAAK,EAAE,GAAGtC,IAAI,CAAC;MACzB;IACF,CAAC,CAAC;EACJ;EACA8C,SAASA,CAACpD,SAAS,EAAEW,IAAI,EAAEsC,OAAO,EAAE;IAClC,IAAI,CAAC,IAAI,CAACb,YAAY,CAACpC,SAAS,CAAC,EAAE;MACjC,IAAI,CAACoC,YAAY,CAACpC,SAAS,CAAC,GAAG,EAAE;IACnC;IACA,IAAI,CAACoC,YAAY,CAACpC,SAAS,CAAC,CAACqD,IAAI,CAAC;MAChC1C,IAAI;MACJsC;IACF,CAAC,CAAC;EACJ;AACF;AACA,eAAejC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}