{"ast": null, "code": "\"use strict\";\n\n// import { CommonModule } from '@angular/common';\n// import { Component } from '@angular/core';\n// import { FormsModule } from '@angular/forms';\n// import { EmbeddingConfigServiceProxy } from '../../../shared/service-proxies/service-proxies';\n// import { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';\n// import { NzModalModule, NzModalService } from 'ng-zorro-antd/modal';\n// import { AddOrEditEmbeddingComponent } from './add-or-edit-embedding/add-or-edit-embedding.component';\n// import { NzMessageService } from 'ng-zorro-antd/message';\n// import { ChangeActiveModelComponent } from './change-active-model/change-active-model.component';\n// @Component({\n//   selector: 'app-embedding',\n//   standalone: true,\n//   imports: [CommonModule, FormsModule, ServiceProxyModule, NzModalModule, AddOrEditEmbeddingComponent],\n//   templateUrl: './embedding.component.html',\n//   styleUrl: './embedding.component.css',\n// })\n// export class EmbeddingComponent {\n//   apiConfigs: any = [];\n//   isUpdating = false;\n//   apiConfig: any = { id: 0, modelId: '', provider: '', apiKey: '', isActive: true };\n//   currentActiveModelData: any;\n//   isActiveModel = false;\n//   constructor(\n//     private _embeddingService: EmbeddingConfigServiceProxy,\n//     public modal: NzModalService,\n//     private message: NzMessageService\n//   ) { }\n//   ngOnInit() {\n//     this.loadEmbedding();\n//     this.currentActiveModel();\n//   }\n//   currentActiveModel() {\n//     this._embeddingService.current().subscribe((res: any) => {\n//       if (res) {\n//         this.currentActiveModelData = res;\n//         this.isActiveModel = true;\n//       }\n//       console.log(res);\n//     });\n//   }\n//   changeActiveModel() {\n//     const modalRef = this.modal.create({\n//       nzTitle: '',\n//       nzContent: ChangeActiveModelComponent,\n//       nzFooter: null,\n//       nzWidth: 500,\n//       nzClassName: 'change-active-model-modal',\n//     });\n//     const instance = modalRef.componentInstance;\n//     if (instance) {\n//       instance.modelChanged.subscribe((result: any) => {\n//         this.currentActiveModel();\n//         this.loadEmbedding();\n//         this.message.success('Active model changed successfully');\n//       });\n//     }\n//   }\n//   resetForm() {\n//     this.apiConfig = {\n//       id: 0,\n//       modelId: '',\n//       provider: '',\n//       apiKey: '',\n//       isActive: false,\n//     };\n//   }\n//   loadEmbedding() {\n//     this._embeddingService.getAllConfigurations().subscribe((res: any) => {\n//       if (res) {\n//         console.log(res);\n//         this.apiConfigs = res;\n//       }\n//     });\n//   }\n//   openAddEditModal(config?: any) {\n//     this.isUpdating = !!config;\n//     if (config) {\n//       this.apiConfig = { ...config };\n//     } else {\n//       this.resetForm();\n//     }\n//     const modalRef = this.modal.create({\n//       nzTitle: this.isUpdating ? 'Update API Configuration' : 'Add API Configuration',\n//       nzContent: AddOrEditEmbeddingComponent,\n//       nzData: {\n//         isUpdating: this.isUpdating,\n//         apiConfig: this.apiConfig,\n//         id: this.apiConfig.id\n//       },\n//       nzFooter: null,\n//       nzWidth: 400,\n//       nzClassName: 'embedding-modal',\n//     });\n//     const instance = modalRef.componentInstance;\n//     if (instance) {\n//       instance.save.subscribe((result: any) => {\n//         if (this.isUpdating) {\n//           this.editConfig(result);\n//         } else {\n//           this.addApiConfig(result);\n//         }\n//       });\n//       instance.cancel.subscribe(() => {\n//         this.modal.closeAll();\n//       });\n//     }\n//   }\n//   addApiConfig(config: any) {\n//     this._embeddingService.createEmbeddingConfig(config).subscribe(\n//       (res: any) => {\n//         console.log(res);\n//         this.loadEmbedding();\n//         this.currentActiveModel();\n//         this.message.success('Configuration created successfully');\n//         this.modal.closeAll();\n//       },\n//       (error: any) => {\n//         this.message.error('Failed to create embedding configuration');\n//         console.error('Failed to create embedding configuration:', error);\n//       }\n//     );\n//   }\n//   editConfig(config: any) {\n//     const index = this.apiConfigs.findIndex(\n//       (c: any) => c.id === config.id\n//     );\n//     if (index !== -1) {\n//       this.apiConfigs[index] = { ...config };\n//       this.message.success('Configuration updated successfully');\n//     }\n//     this.modal.closeAll();\n//     this.loadEmbedding();\n//     this.currentActiveModel();\n//   }\n//   deleteConfig(config: any) {\n//     this.modal.confirm({\n//       nzTitle: 'Are you sure you want to delete this configuration?',\n//       nzContent: 'This action cannot be undone.',\n//       nzOkText: 'Yes',\n//       nzOkType: 'primary',\n//       nzOkDanger: true,\n//       nzOnOk: () => {\n//         console.log(`Deleting configuration with id: ${config.id}`);\n//         this._embeddingService.deleteEmbeddingConfig(config.id).subscribe((res: any) => {\n//           this.message.success('Configuration deleted successfully');\n//           this.loadEmbedding();\n//         }, (error: any) => {\n//           if (error.isError && error.message) {\n//             this.message.error(error.message);\n//           } else {\n//             this.message.error('Failed to delete embedding configuration');\n//             console.error('Failed to delete embedding configuration:', error);\n//           }\n//         });\n//       },\n//       nzCancelText: 'No',\n//     });\n//   }\n// }", "map": {"version": 3, "names": [], "sources": ["C:\\Users\\<USER>\\source\\ai-hub\\WebApp\\src\\app\\admin\\embedding\\embedding.component.ts"], "sourcesContent": ["// import { CommonModule } from '@angular/common';\r\n// import { Component } from '@angular/core';\r\n// import { FormsModule } from '@angular/forms';\r\n// import { EmbeddingConfigServiceProxy } from '../../../shared/service-proxies/service-proxies';\r\n// import { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';\r\n// import { NzModalModule, NzModalService } from 'ng-zorro-antd/modal';\r\n// import { AddOrEditEmbeddingComponent } from './add-or-edit-embedding/add-or-edit-embedding.component';\r\n// import { NzMessageService } from 'ng-zorro-antd/message';\r\n// import { ChangeActiveModelComponent } from './change-active-model/change-active-model.component';\r\n\r\n// @Component({\r\n//   selector: 'app-embedding',\r\n//   standalone: true,\r\n//   imports: [CommonModule, FormsModule, ServiceProxyModule, NzModalModule, AddOrEditEmbeddingComponent],\r\n//   templateUrl: './embedding.component.html',\r\n//   styleUrl: './embedding.component.css',\r\n// })\r\n// export class EmbeddingComponent {\r\n//   apiConfigs: any = [];\r\n//   isUpdating = false;\r\n//   apiConfig: any = { id: 0, modelId: '', provider: '', apiKey: '', isActive: true };\r\n\r\n//   currentActiveModelData: any;\r\n//   isActiveModel = false;\r\n\r\n\r\n\r\n//   constructor(\r\n//     private _embeddingService: EmbeddingConfigServiceProxy,\r\n//     public modal: NzModalService,\r\n//     private message: NzMessageService\r\n//   ) { }\r\n\r\n//   ngOnInit() {\r\n//     this.loadEmbedding();\r\n//     this.currentActiveModel();\r\n//   }\r\n\r\n//   currentActiveModel() {\r\n//     this._embeddingService.current().subscribe((res: any) => {\r\n//       if (res) {\r\n//         this.currentActiveModelData = res;\r\n//         this.isActiveModel = true;\r\n//       }\r\n\r\n//       console.log(res);\r\n//     });\r\n//   }\r\n\r\n//   changeActiveModel() {\r\n//     const modalRef = this.modal.create({\r\n//       nzTitle: '',\r\n//       nzContent: ChangeActiveModelComponent,\r\n//       nzFooter: null,\r\n//       nzWidth: 500,\r\n//       nzClassName: 'change-active-model-modal',\r\n//     });\r\n\r\n//     const instance = modalRef.componentInstance;\r\n//     if (instance) {\r\n//       instance.modelChanged.subscribe((result: any) => {\r\n//         this.currentActiveModel();\r\n//         this.loadEmbedding();\r\n//         this.message.success('Active model changed successfully');\r\n//       });\r\n//     }\r\n//   }\r\n\r\n\r\n//   resetForm() {\r\n//     this.apiConfig = {\r\n//       id: 0,\r\n//       modelId: '',\r\n//       provider: '',\r\n//       apiKey: '',\r\n//       isActive: false,\r\n//     };\r\n//   }\r\n\r\n//   loadEmbedding() {\r\n//     this._embeddingService.getAllConfigurations().subscribe((res: any) => {\r\n//       if (res) {\r\n//         console.log(res);\r\n//         this.apiConfigs = res;\r\n//       }\r\n//     });\r\n\r\n//   }\r\n\r\n//   openAddEditModal(config?: any) {\r\n//     this.isUpdating = !!config;\r\n//     if (config) {\r\n//       this.apiConfig = { ...config };\r\n//     } else {\r\n//       this.resetForm();\r\n//     }\r\n\r\n//     const modalRef = this.modal.create({\r\n//       nzTitle: this.isUpdating ? 'Update API Configuration' : 'Add API Configuration',\r\n//       nzContent: AddOrEditEmbeddingComponent,\r\n//       nzData: {\r\n//         isUpdating: this.isUpdating,\r\n//         apiConfig: this.apiConfig,\r\n//         id: this.apiConfig.id\r\n//       },\r\n//       nzFooter: null,\r\n//       nzWidth: 400,\r\n//       nzClassName: 'embedding-modal',\r\n//     });\r\n\r\n//     const instance = modalRef.componentInstance;\r\n//     if (instance) {\r\n//       instance.save.subscribe((result: any) => {\r\n//         if (this.isUpdating) {\r\n//           this.editConfig(result);\r\n//         } else {\r\n//           this.addApiConfig(result);\r\n//         }\r\n//       });\r\n\r\n//       instance.cancel.subscribe(() => {\r\n//         this.modal.closeAll();\r\n//       });\r\n//     }\r\n//   }\r\n\r\n\r\n\r\n//   addApiConfig(config: any) {\r\n//     this._embeddingService.createEmbeddingConfig(config).subscribe(\r\n//       (res: any) => {\r\n//         console.log(res);\r\n//         this.loadEmbedding();\r\n//         this.currentActiveModel();\r\n//         this.message.success('Configuration created successfully');\r\n//         this.modal.closeAll();\r\n//       },\r\n//       (error: any) => {\r\n//         this.message.error('Failed to create embedding configuration');\r\n//         console.error('Failed to create embedding configuration:', error);\r\n//       }\r\n//     );\r\n//   }\r\n\r\n\r\n//   editConfig(config: any) {\r\n//     const index = this.apiConfigs.findIndex(\r\n//       (c: any) => c.id === config.id\r\n//     );\r\n//     if (index !== -1) {\r\n//       this.apiConfigs[index] = { ...config };\r\n//       this.message.success('Configuration updated successfully');\r\n//     }\r\n//     this.modal.closeAll();\r\n//     this.loadEmbedding();\r\n//     this.currentActiveModel();\r\n//   }\r\n\r\n//   deleteConfig(config: any) {\r\n//     this.modal.confirm({\r\n//       nzTitle: 'Are you sure you want to delete this configuration?',\r\n//       nzContent: 'This action cannot be undone.',\r\n//       nzOkText: 'Yes',\r\n//       nzOkType: 'primary',\r\n//       nzOkDanger: true,\r\n//       nzOnOk: () => {\r\n//         console.log(`Deleting configuration with id: ${config.id}`);\r\n//         this._embeddingService.deleteEmbeddingConfig(config.id).subscribe((res: any) => {\r\n//           this.message.success('Configuration deleted successfully');\r\n//           this.loadEmbedding();\r\n//         }, (error: any) => {\r\n//           if (error.isError && error.message) {\r\n//             this.message.error(error.message);\r\n//           } else {\r\n//             this.message.error('Failed to delete embedding configuration');\r\n//             console.error('Failed to delete embedding configuration:', error);\r\n//           }\r\n//         });\r\n//       },\r\n//       nzCancelText: 'No',\r\n//     });\r\n//   }\r\n// }\r\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAIA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}