{"ast": null, "code": "const getParentElement = element => element.parentElement || element.getRootNode().host || null;\nconst getElementRect = element => {\n  const rect = element.getBoundingClientRect();\n  const scaleX = 'offsetWidth' in element && Math.abs(rect.width) / element.offsetWidth || 1;\n  const scaleY = 'offsetHeight' in element && Math.abs(rect.height) / element.offsetHeight || 1;\n  return {\n    top: rect.top,\n    right: rect.left + element.clientWidth * scaleX,\n    bottom: rect.top + element.clientHeight * scaleY,\n    left: rect.left\n  };\n};\nconst paddingValueToInt = value => {\n  const number = parseInt(value, 10);\n  return Number.isNaN(number) ? 0 : number;\n};\n\n// Follow the steps described in https://www.w3.org/TR/cssom-view-1/#element-scrolling-members,\n// assuming that the scroll option is set to 'nearest'.\nconst getScrollDistance = (targetStart, targetEnd, scrollStart, scrollEnd, scrollPaddingStart, scrollPaddingEnd) => {\n  if (targetStart < scrollStart && targetEnd > scrollEnd) {\n    return 0;\n  }\n  if (targetStart < scrollStart) {\n    return -(scrollStart - targetStart + scrollPaddingStart);\n  }\n  if (targetEnd > scrollEnd) {\n    return targetEnd - targetStart > scrollEnd - scrollStart ? targetStart + scrollPaddingStart - scrollStart : targetEnd - scrollEnd + scrollPaddingEnd;\n  }\n  return 0;\n};\nconst scrollRectIntoView = (root, targetRect) => {\n  const document = root.ownerDocument;\n  let rect = targetRect;\n  let current = root;\n  while (current) {\n    const isDocumentBody = current === document.body;\n    const bounding = isDocumentBody ? {\n      top: 0,\n      right: window.visualViewport?.width ?? document.documentElement.clientWidth,\n      bottom: window.visualViewport?.height ?? document.documentElement.clientHeight,\n      left: 0\n    } : getElementRect(current);\n    const style = getComputedStyle(current);\n    const scrollDistanceX = getScrollDistance(rect.left, rect.right, bounding.left, bounding.right, paddingValueToInt(style.scrollPaddingLeft), paddingValueToInt(style.scrollPaddingRight));\n    const scrollDistanceY = getScrollDistance(rect.top, rect.bottom, bounding.top, bounding.bottom, paddingValueToInt(style.scrollPaddingTop), paddingValueToInt(style.scrollPaddingBottom));\n    if (scrollDistanceX || scrollDistanceY) {\n      if (isDocumentBody) {\n        document.defaultView?.scrollBy(scrollDistanceX, scrollDistanceY);\n      } else {\n        const {\n          scrollLeft,\n          scrollTop\n        } = current;\n        if (scrollDistanceY) {\n          current.scrollTop += scrollDistanceY;\n        }\n        if (scrollDistanceX) {\n          current.scrollLeft += scrollDistanceX;\n        }\n        const scrolledLeft = current.scrollLeft - scrollLeft;\n        const scrolledTop = current.scrollTop - scrollTop;\n        rect = {\n          left: rect.left - scrolledLeft,\n          top: rect.top - scrolledTop,\n          right: rect.right - scrolledLeft,\n          bottom: rect.bottom - scrolledTop\n        };\n      }\n    }\n    current = isDocumentBody || style.position === 'fixed' ? null : getParentElement(current);\n  }\n};\nexport default scrollRectIntoView;", "map": {"version": 3, "names": ["getParentElement", "element", "parentElement", "getRootNode", "host", "getElementRect", "rect", "getBoundingClientRect", "scaleX", "Math", "abs", "width", "offsetWidth", "scaleY", "height", "offsetHeight", "top", "right", "left", "clientWidth", "bottom", "clientHeight", "paddingValueToInt", "value", "number", "parseInt", "Number", "isNaN", "getScrollDistance", "targetStart", "targetEnd", "scrollStart", "scrollEnd", "scrollPaddingStart", "scrollPaddingEnd", "scrollRectIntoView", "root", "targetRect", "document", "ownerDocument", "current", "isDocumentBody", "body", "bounding", "window", "visualViewport", "documentElement", "style", "getComputedStyle", "scrollDistanceX", "scrollPaddingLeft", "scrollPaddingRight", "scrollDistanceY", "scrollPaddingTop", "scrollPaddingBottom", "defaultView", "scrollBy", "scrollLeft", "scrollTop", "scrolledLeft", "scrolledTop", "position"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/quill/core/utils/scrollRectIntoView.js"], "sourcesContent": ["const getParentElement = element => element.parentElement || element.getRootNode().host || null;\nconst getElementRect = element => {\n  const rect = element.getBoundingClientRect();\n  const scaleX = 'offsetWidth' in element && Math.abs(rect.width) / element.offsetWidth || 1;\n  const scaleY = 'offsetHeight' in element && Math.abs(rect.height) / element.offsetHeight || 1;\n  return {\n    top: rect.top,\n    right: rect.left + element.clientWidth * scaleX,\n    bottom: rect.top + element.clientHeight * scaleY,\n    left: rect.left\n  };\n};\nconst paddingValueToInt = value => {\n  const number = parseInt(value, 10);\n  return Number.isNaN(number) ? 0 : number;\n};\n\n// Follow the steps described in https://www.w3.org/TR/cssom-view-1/#element-scrolling-members,\n// assuming that the scroll option is set to 'nearest'.\nconst getScrollDistance = (targetStart, targetEnd, scrollStart, scrollEnd, scrollPaddingStart, scrollPaddingEnd) => {\n  if (targetStart < scrollStart && targetEnd > scrollEnd) {\n    return 0;\n  }\n  if (targetStart < scrollStart) {\n    return -(scrollStart - targetStart + scrollPaddingStart);\n  }\n  if (targetEnd > scrollEnd) {\n    return targetEnd - targetStart > scrollEnd - scrollStart ? targetStart + scrollPaddingStart - scrollStart : targetEnd - scrollEnd + scrollPaddingEnd;\n  }\n  return 0;\n};\nconst scrollRectIntoView = (root, targetRect) => {\n  const document = root.ownerDocument;\n  let rect = targetRect;\n  let current = root;\n  while (current) {\n    const isDocumentBody = current === document.body;\n    const bounding = isDocumentBody ? {\n      top: 0,\n      right: window.visualViewport?.width ?? document.documentElement.clientWidth,\n      bottom: window.visualViewport?.height ?? document.documentElement.clientHeight,\n      left: 0\n    } : getElementRect(current);\n    const style = getComputedStyle(current);\n    const scrollDistanceX = getScrollDistance(rect.left, rect.right, bounding.left, bounding.right, paddingValueToInt(style.scrollPaddingLeft), paddingValueToInt(style.scrollPaddingRight));\n    const scrollDistanceY = getScrollDistance(rect.top, rect.bottom, bounding.top, bounding.bottom, paddingValueToInt(style.scrollPaddingTop), paddingValueToInt(style.scrollPaddingBottom));\n    if (scrollDistanceX || scrollDistanceY) {\n      if (isDocumentBody) {\n        document.defaultView?.scrollBy(scrollDistanceX, scrollDistanceY);\n      } else {\n        const {\n          scrollLeft,\n          scrollTop\n        } = current;\n        if (scrollDistanceY) {\n          current.scrollTop += scrollDistanceY;\n        }\n        if (scrollDistanceX) {\n          current.scrollLeft += scrollDistanceX;\n        }\n        const scrolledLeft = current.scrollLeft - scrollLeft;\n        const scrolledTop = current.scrollTop - scrollTop;\n        rect = {\n          left: rect.left - scrolledLeft,\n          top: rect.top - scrolledTop,\n          right: rect.right - scrolledLeft,\n          bottom: rect.bottom - scrolledTop\n        };\n      }\n    }\n    current = isDocumentBody || style.position === 'fixed' ? null : getParentElement(current);\n  }\n};\nexport default scrollRectIntoView;\n"], "mappings": "AAAA,MAAMA,gBAAgB,GAAGC,OAAO,IAAIA,OAAO,CAACC,aAAa,IAAID,OAAO,CAACE,WAAW,CAAC,CAAC,CAACC,IAAI,IAAI,IAAI;AAC/F,MAAMC,cAAc,GAAGJ,OAAO,IAAI;EAChC,MAAMK,IAAI,GAAGL,OAAO,CAACM,qBAAqB,CAAC,CAAC;EAC5C,MAAMC,MAAM,GAAG,aAAa,IAAIP,OAAO,IAAIQ,IAAI,CAACC,GAAG,CAACJ,IAAI,CAACK,KAAK,CAAC,GAAGV,OAAO,CAACW,WAAW,IAAI,CAAC;EAC1F,MAAMC,MAAM,GAAG,cAAc,IAAIZ,OAAO,IAAIQ,IAAI,CAACC,GAAG,CAACJ,IAAI,CAACQ,MAAM,CAAC,GAAGb,OAAO,CAACc,YAAY,IAAI,CAAC;EAC7F,OAAO;IACLC,GAAG,EAAEV,IAAI,CAACU,GAAG;IACbC,KAAK,EAAEX,IAAI,CAACY,IAAI,GAAGjB,OAAO,CAACkB,WAAW,GAAGX,MAAM;IAC/CY,MAAM,EAAEd,IAAI,CAACU,GAAG,GAAGf,OAAO,CAACoB,YAAY,GAAGR,MAAM;IAChDK,IAAI,EAAEZ,IAAI,CAACY;EACb,CAAC;AACH,CAAC;AACD,MAAMI,iBAAiB,GAAGC,KAAK,IAAI;EACjC,MAAMC,MAAM,GAAGC,QAAQ,CAACF,KAAK,EAAE,EAAE,CAAC;EAClC,OAAOG,MAAM,CAACC,KAAK,CAACH,MAAM,CAAC,GAAG,CAAC,GAAGA,MAAM;AAC1C,CAAC;;AAED;AACA;AACA,MAAMI,iBAAiB,GAAGA,CAACC,WAAW,EAAEC,SAAS,EAAEC,WAAW,EAAEC,SAAS,EAAEC,kBAAkB,EAAEC,gBAAgB,KAAK;EAClH,IAAIL,WAAW,GAAGE,WAAW,IAAID,SAAS,GAAGE,SAAS,EAAE;IACtD,OAAO,CAAC;EACV;EACA,IAAIH,WAAW,GAAGE,WAAW,EAAE;IAC7B,OAAO,EAAEA,WAAW,GAAGF,WAAW,GAAGI,kBAAkB,CAAC;EAC1D;EACA,IAAIH,SAAS,GAAGE,SAAS,EAAE;IACzB,OAAOF,SAAS,GAAGD,WAAW,GAAGG,SAAS,GAAGD,WAAW,GAAGF,WAAW,GAAGI,kBAAkB,GAAGF,WAAW,GAAGD,SAAS,GAAGE,SAAS,GAAGE,gBAAgB;EACtJ;EACA,OAAO,CAAC;AACV,CAAC;AACD,MAAMC,kBAAkB,GAAGA,CAACC,IAAI,EAAEC,UAAU,KAAK;EAC/C,MAAMC,QAAQ,GAAGF,IAAI,CAACG,aAAa;EACnC,IAAIjC,IAAI,GAAG+B,UAAU;EACrB,IAAIG,OAAO,GAAGJ,IAAI;EAClB,OAAOI,OAAO,EAAE;IACd,MAAMC,cAAc,GAAGD,OAAO,KAAKF,QAAQ,CAACI,IAAI;IAChD,MAAMC,QAAQ,GAAGF,cAAc,GAAG;MAChCzB,GAAG,EAAE,CAAC;MACNC,KAAK,EAAE2B,MAAM,CAACC,cAAc,EAAElC,KAAK,IAAI2B,QAAQ,CAACQ,eAAe,CAAC3B,WAAW;MAC3EC,MAAM,EAAEwB,MAAM,CAACC,cAAc,EAAE/B,MAAM,IAAIwB,QAAQ,CAACQ,eAAe,CAACzB,YAAY;MAC9EH,IAAI,EAAE;IACR,CAAC,GAAGb,cAAc,CAACmC,OAAO,CAAC;IAC3B,MAAMO,KAAK,GAAGC,gBAAgB,CAACR,OAAO,CAAC;IACvC,MAAMS,eAAe,GAAGrB,iBAAiB,CAACtB,IAAI,CAACY,IAAI,EAAEZ,IAAI,CAACW,KAAK,EAAE0B,QAAQ,CAACzB,IAAI,EAAEyB,QAAQ,CAAC1B,KAAK,EAAEK,iBAAiB,CAACyB,KAAK,CAACG,iBAAiB,CAAC,EAAE5B,iBAAiB,CAACyB,KAAK,CAACI,kBAAkB,CAAC,CAAC;IACxL,MAAMC,eAAe,GAAGxB,iBAAiB,CAACtB,IAAI,CAACU,GAAG,EAAEV,IAAI,CAACc,MAAM,EAAEuB,QAAQ,CAAC3B,GAAG,EAAE2B,QAAQ,CAACvB,MAAM,EAAEE,iBAAiB,CAACyB,KAAK,CAACM,gBAAgB,CAAC,EAAE/B,iBAAiB,CAACyB,KAAK,CAACO,mBAAmB,CAAC,CAAC;IACxL,IAAIL,eAAe,IAAIG,eAAe,EAAE;MACtC,IAAIX,cAAc,EAAE;QAClBH,QAAQ,CAACiB,WAAW,EAAEC,QAAQ,CAACP,eAAe,EAAEG,eAAe,CAAC;MAClE,CAAC,MAAM;QACL,MAAM;UACJK,UAAU;UACVC;QACF,CAAC,GAAGlB,OAAO;QACX,IAAIY,eAAe,EAAE;UACnBZ,OAAO,CAACkB,SAAS,IAAIN,eAAe;QACtC;QACA,IAAIH,eAAe,EAAE;UACnBT,OAAO,CAACiB,UAAU,IAAIR,eAAe;QACvC;QACA,MAAMU,YAAY,GAAGnB,OAAO,CAACiB,UAAU,GAAGA,UAAU;QACpD,MAAMG,WAAW,GAAGpB,OAAO,CAACkB,SAAS,GAAGA,SAAS;QACjDpD,IAAI,GAAG;UACLY,IAAI,EAAEZ,IAAI,CAACY,IAAI,GAAGyC,YAAY;UAC9B3C,GAAG,EAAEV,IAAI,CAACU,GAAG,GAAG4C,WAAW;UAC3B3C,KAAK,EAAEX,IAAI,CAACW,KAAK,GAAG0C,YAAY;UAChCvC,MAAM,EAAEd,IAAI,CAACc,MAAM,GAAGwC;QACxB,CAAC;MACH;IACF;IACApB,OAAO,GAAGC,cAAc,IAAIM,KAAK,CAACc,QAAQ,KAAK,OAAO,GAAG,IAAI,GAAG7D,gBAAgB,CAACwC,OAAO,CAAC;EAC3F;AACF,CAAC;AACD,eAAeL,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}