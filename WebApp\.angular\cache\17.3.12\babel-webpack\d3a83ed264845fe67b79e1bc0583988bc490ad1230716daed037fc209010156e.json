{"ast": null, "code": "import { ClassAttributor, Scope } from 'parchment';\nclass IndentAttributor extends ClassAttributor {\n  add(node, value) {\n    let normalizedValue = 0;\n    if (value === '+1' || value === '-1') {\n      const indent = this.value(node) || 0;\n      normalizedValue = value === '+1' ? indent + 1 : indent - 1;\n    } else if (typeof value === 'number') {\n      normalizedValue = value;\n    }\n    if (normalizedValue === 0) {\n      this.remove(node);\n      return true;\n    }\n    return super.add(node, normalizedValue.toString());\n  }\n  canAdd(node, value) {\n    return super.canAdd(node, value) || super.canAdd(node, parseInt(value, 10));\n  }\n  value(node) {\n    return parseInt(super.value(node), 10) || undefined; // Don't return NaN\n  }\n}\nconst IndentClass = new IndentAttributor('indent', 'ql-indent', {\n  scope: Scope.BLOCK,\n  // @ts-expect-error\n  whitelist: [1, 2, 3, 4, 5, 6, 7, 8]\n});\nexport default IndentClass;", "map": {"version": 3, "names": ["ClassAttributor", "<PERSON><PERSON>", "IndentAttributor", "add", "node", "value", "normalizedValue", "indent", "remove", "toString", "canAdd", "parseInt", "undefined", "IndentClass", "scope", "BLOCK", "whitelist"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/quill/formats/indent.js"], "sourcesContent": ["import { ClassAttributor, Scope } from 'parchment';\nclass IndentAttributor extends ClassAttributor {\n  add(node, value) {\n    let normalizedValue = 0;\n    if (value === '+1' || value === '-1') {\n      const indent = this.value(node) || 0;\n      normalizedValue = value === '+1' ? indent + 1 : indent - 1;\n    } else if (typeof value === 'number') {\n      normalizedValue = value;\n    }\n    if (normalizedValue === 0) {\n      this.remove(node);\n      return true;\n    }\n    return super.add(node, normalizedValue.toString());\n  }\n  canAdd(node, value) {\n    return super.canAdd(node, value) || super.canAdd(node, parseInt(value, 10));\n  }\n  value(node) {\n    return parseInt(super.value(node), 10) || undefined; // Don't return NaN\n  }\n}\nconst IndentClass = new IndentAttributor('indent', 'ql-indent', {\n  scope: Scope.BLOCK,\n  // @ts-expect-error\n  whitelist: [1, 2, 3, 4, 5, 6, 7, 8]\n});\nexport default IndentClass;\n"], "mappings": "AAAA,SAASA,eAAe,EAAEC,KAAK,QAAQ,WAAW;AAClD,MAAMC,gBAAgB,SAASF,eAAe,CAAC;EAC7CG,GAAGA,CAACC,IAAI,EAAEC,KAAK,EAAE;IACf,IAAIC,eAAe,GAAG,CAAC;IACvB,IAAID,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,IAAI,EAAE;MACpC,MAAME,MAAM,GAAG,IAAI,CAACF,KAAK,CAACD,IAAI,CAAC,IAAI,CAAC;MACpCE,eAAe,GAAGD,KAAK,KAAK,IAAI,GAAGE,MAAM,GAAG,CAAC,GAAGA,MAAM,GAAG,CAAC;IAC5D,CAAC,MAAM,IAAI,OAAOF,KAAK,KAAK,QAAQ,EAAE;MACpCC,eAAe,GAAGD,KAAK;IACzB;IACA,IAAIC,eAAe,KAAK,CAAC,EAAE;MACzB,IAAI,CAACE,MAAM,CAACJ,IAAI,CAAC;MACjB,OAAO,IAAI;IACb;IACA,OAAO,KAAK,CAACD,GAAG,CAACC,IAAI,EAAEE,eAAe,CAACG,QAAQ,CAAC,CAAC,CAAC;EACpD;EACAC,MAAMA,CAACN,IAAI,EAAEC,KAAK,EAAE;IAClB,OAAO,KAAK,CAACK,MAAM,CAACN,IAAI,EAAEC,KAAK,CAAC,IAAI,KAAK,CAACK,MAAM,CAACN,IAAI,EAAEO,QAAQ,CAACN,KAAK,EAAE,EAAE,CAAC,CAAC;EAC7E;EACAA,KAAKA,CAACD,IAAI,EAAE;IACV,OAAOO,QAAQ,CAAC,KAAK,CAACN,KAAK,CAACD,IAAI,CAAC,EAAE,EAAE,CAAC,IAAIQ,SAAS,CAAC,CAAC;EACvD;AACF;AACA,MAAMC,WAAW,GAAG,IAAIX,gBAAgB,CAAC,QAAQ,EAAE,WAAW,EAAE;EAC9DY,KAAK,EAAEb,KAAK,CAACc,KAAK;EAClB;EACAC,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AACpC,CAAC,CAAC;AACF,eAAeH,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}