{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./new-note-book.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./new-note-book.component.css?ngResource\";\nimport { CommonModule } from '@angular/common';\nimport { Component, ViewChild } from '@angular/core';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport EditorJS from '@editorjs/editorjs';\nimport Header from '@editorjs/header';\nimport Table from '@editorjs/table';\nimport { MarkdownModule } from 'ngx-markdown';\nimport List from '@editorjs/list';\nimport SimpleImage from '@editorjs/simple-image';\nimport Checklist from '@editorjs/checklist';\nimport Quote from '@editorjs/quote';\nimport Warning from '@editorjs/warning';\nimport Marker from '@editorjs/marker';\nimport CodeTool from '@editorjs/code';\nimport Delimiter from '@editorjs/delimiter';\nimport InlineCode from '@editorjs/inline-code';\nimport LinkTool from '@editorjs/link';\nimport Embed from '@editorjs/embed';\nlet NewNoteBookComponent = class NewNoteBookComponent {\n  constructor() {\n    this.hasContent = false;\n  }\n  ngOnInit() {\n    // Remove initialization from here\n  }\n  ngAfterViewInit() {\n    this.initializeEditor();\n  }\n  initializeEditor() {\n    this.editor = new EditorJS({\n      holder: 'editor',\n      minHeight: 200,\n      placeholder: 'Start writing or paste your content here...',\n      onChange: () => {\n        this.editor.save().then(data => {\n          this.hasContent = data.blocks.length > 0;\n        });\n      },\n      tools: {\n        header: {\n          class: Header,\n          inlineToolbar: true,\n          config: {\n            levels: [1, 2, 3, 4],\n            defaultLevel: 1,\n            placeholder: 'Enter a heading'\n          }\n        },\n        image: {\n          class: SimpleImage,\n          config: {\n            placeholder: 'Paste image URL or choose file',\n            buttonContent: 'Choose an image',\n            uploader: {\n              uploadByFile(file) {\n                // Create FormData\n                const formData = new FormData();\n                formData.append('image', file);\n                // Upload to your API endpoint\n                return fetch('https://localhost:44350/api/Upload/image', {\n                  method: 'POST',\n                  body: formData\n                }).then(response => response.json()).then(data => {\n                  return {\n                    success: 1,\n                    file: {\n                      url: data.url\n                    }\n                  };\n                });\n              },\n              uploadByUrl(url) {\n                return fetch('https://localhost:44350/api/Upload/url', {\n                  method: 'POST',\n                  headers: {\n                    'Content-Type': 'application/json'\n                  },\n                  body: JSON.stringify({\n                    url\n                  })\n                }).then(response => response.json()).then(data => {\n                  return {\n                    success: 1,\n                    file: {\n                      url: data.url\n                    }\n                  };\n                });\n              }\n            }\n          }\n        },\n        list: List,\n        checklist: Checklist,\n        quote: Quote,\n        warning: Warning,\n        marker: Marker,\n        code: CodeTool,\n        delimiter: Delimiter,\n        inlineCode: InlineCode,\n        linkTool: LinkTool,\n        embed: Embed,\n        table: Table\n      },\n      defaultBlock: 'paragraph'\n    });\n  }\n  showEditorData() {\n    this.editor.save().then(data => {\n      console.log(JSON.stringify(data, null, 2));\n    });\n  }\n  static {\n    this.ctorParameters = () => [];\n  }\n  static {\n    this.propDecorators = {\n      editorElement: [{\n        type: ViewChild,\n        args: ['editor', {\n          static: true\n        }]\n      }]\n    };\n  }\n};\nNewNoteBookComponent = __decorate([Component({\n  selector: 'app-new-note-book',\n  imports: [CommonModule, MarkdownModule, FormsModule, ReactiveFormsModule],\n  standalone: true,\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], NewNoteBookComponent);\nexport { NewNoteBookComponent };", "map": {"version": 3, "names": ["CommonModule", "Component", "ViewChild", "FormsModule", "ReactiveFormsModule", "EditorJS", "Header", "Table", "MarkdownModule", "List", "SimpleImage", "Checklist", "Quote", "Warning", "<PERSON><PERSON>", "CodeTool", "Delimiter", "InlineCode", "LinkTool", "Embed", "NewNoteBookComponent", "constructor", "<PERSON><PERSON><PERSON><PERSON>", "ngOnInit", "ngAfterViewInit", "initializeEditor", "editor", "holder", "minHeight", "placeholder", "onChange", "save", "then", "data", "blocks", "length", "tools", "header", "class", "inlineToolbar", "config", "levels", "defaultLevel", "image", "buttonContent", "uploader", "uploadByFile", "file", "formData", "FormData", "append", "fetch", "method", "body", "response", "json", "success", "url", "uploadByUrl", "headers", "JSON", "stringify", "list", "checklist", "quote", "warning", "marker", "code", "delimiter", "inlineCode", "linkTool", "embed", "table", "defaultBlock", "showEditorData", "console", "log", "args", "static", "__decorate", "selector", "imports", "standalone", "template", "__NG_CLI_RESOURCE__0"], "sources": ["C:\\Users\\<USER>\\source\\ai-hub\\WebApp\\src\\app\\MyNotesProjects\\new-note-book\\new-note-book.component.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\r\nimport { AfterViewInit, Component, ElementRef, OnInit, ViewChild } from '@angular/core';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport EditorJS, { ToolConstructable } from '@editorjs/editorjs';\r\n\r\nimport Header from '@editorjs/header';\r\nimport Table from '@editorjs/table';\r\nimport { MarkdownModule } from 'ngx-markdown';\r\nimport List from '@editorjs/list';\r\nimport SimpleImage from '@editorjs/simple-image';\r\nimport Checklist from '@editorjs/checklist';\r\nimport Quote from '@editorjs/quote';\r\nimport Warning from '@editorjs/warning';\r\nimport Marker from '@editorjs/marker';\r\nimport CodeTool from '@editorjs/code';\r\nimport Delimiter from '@editorjs/delimiter';\r\nimport InlineCode from '@editorjs/inline-code';\r\nimport LinkTool from '@editorjs/link';\r\nimport Embed from '@editorjs/embed';\r\n\r\n\r\n@Component({\r\n  selector: 'app-new-note-book',\r\n  imports: [CommonModule, MarkdownModule, FormsModule, ReactiveFormsModule],\r\n  standalone: true,\r\n  templateUrl: './new-note-book.component.html',\r\n  styleUrl: './new-note-book.component.css'\r\n})\r\nexport class NewNoteBookComponent implements OnInit, AfterViewInit {\r\n  @ViewChild('editor', { static: true })\r\n  editorElement!: ElementRef;\r\n\r\n  private editor!: EditorJS;\r\n  hasContent = false;\r\n\r\n  constructor() { }\r\n\r\n  ngOnInit(): void {\r\n    // Remove initialization from here\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    this.initializeEditor();\r\n  }\r\n\r\n  private initializeEditor() {\r\n    this.editor = new EditorJS({\r\n      holder: 'editor',\r\n      minHeight: 200,\r\n      placeholder: 'Start writing or paste your content here...',\r\n      onChange: () => {\r\n        this.editor.save().then(data => {\r\n          this.hasContent = data.blocks.length > 0;\r\n        });\r\n      },\r\n      tools: {\r\n        header: {\r\n          class: Header as unknown as ToolConstructable,\r\n          inlineToolbar: true,\r\n          config: {\r\n            levels: [1, 2, 3, 4],\r\n            defaultLevel: 1,\r\n            placeholder: 'Enter a heading'\r\n          }\r\n        },\r\n        image: {\r\n          class: SimpleImage as unknown as ToolConstructable,\r\n          config: {\r\n            placeholder: 'Paste image URL or choose file',\r\n            buttonContent: 'Choose an image',\r\n            uploader: {\r\n              uploadByFile(file: File): Promise<{ success: number; file: { url: string } }> {\r\n                // Create FormData\r\n                const formData = new FormData();\r\n                formData.append('image', file);\r\n\r\n                // Upload to your API endpoint\r\n                return fetch('https://localhost:44350/api/Upload/image', {\r\n                  method: 'POST',\r\n                  body: formData\r\n                })\r\n                .then(response => response.json())\r\n                .then(data => {\r\n                  return {\r\n                    success: 1,\r\n                    file: {\r\n                      url: data.url\r\n                    }\r\n                  };\r\n                });\r\n              },\r\n              uploadByUrl(url: string) {\r\n                return fetch('https://localhost:44350/api/Upload/url', {\r\n                  method: 'POST',\r\n                  headers: {\r\n                    'Content-Type': 'application/json'\r\n                  },\r\n                  body: JSON.stringify({ url })\r\n                })\r\n                .then(response => response.json())\r\n                .then(data => {\r\n                  return {\r\n                    success: 1,\r\n                    file: {\r\n                      url: data.url\r\n                    }\r\n                  };\r\n                });\r\n              }\r\n            }\r\n          }\r\n        },\r\n        list: List as unknown as ToolConstructable,\r\n        checklist: Checklist as unknown as ToolConstructable,\r\n        quote: Quote as unknown as ToolConstructable,\r\n        warning: Warning as unknown as ToolConstructable,\r\n        marker: Marker as unknown as ToolConstructable,\r\n        code: CodeTool as unknown as ToolConstructable,\r\n        delimiter: Delimiter as unknown as ToolConstructable,\r\n        inlineCode: InlineCode as unknown as ToolConstructable,\r\n        linkTool: LinkTool as unknown as ToolConstructable,\r\n        embed: Embed as unknown as ToolConstructable,\r\n        table: Table as unknown as ToolConstructable\r\n      },\r\n      defaultBlock: 'paragraph'\r\n    });\r\n  }\r\n\r\n  showEditorData() {\r\n    this.editor.save().then(data => {\r\n      console.log(JSON.stringify(data, null, 2));\r\n    });\r\n  }\r\n}\r\n"], "mappings": ";;;AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAAwBC,SAAS,EAAsBC,SAAS,QAAQ,eAAe;AACvF,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,OAAOC,QAA+B,MAAM,oBAAoB;AAEhE,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAOC,KAAK,MAAM,iBAAiB;AACnC,SAASC,cAAc,QAAQ,cAAc;AAC7C,OAAOC,IAAI,MAAM,gBAAgB;AACjC,OAAOC,WAAW,MAAM,wBAAwB;AAChD,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,OAAOC,KAAK,MAAM,iBAAiB;AACnC,OAAOC,OAAO,MAAM,mBAAmB;AACvC,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,KAAK,MAAM,iBAAiB;AAU5B,IAAMC,oBAAoB,GAA1B,MAAMA,oBAAoB;EAO/BC,YAAA;IAFA,KAAAC,UAAU,GAAG,KAAK;EAEF;EAEhBC,QAAQA,CAAA;IACN;EAAA;EAGFC,eAAeA,CAAA;IACb,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEQA,gBAAgBA,CAAA;IACtB,IAAI,CAACC,MAAM,GAAG,IAAIrB,QAAQ,CAAC;MACzBsB,MAAM,EAAE,QAAQ;MAChBC,SAAS,EAAE,GAAG;MACdC,WAAW,EAAE,6CAA6C;MAC1DC,QAAQ,EAAEA,CAAA,KAAK;QACb,IAAI,CAACJ,MAAM,CAACK,IAAI,EAAE,CAACC,IAAI,CAACC,IAAI,IAAG;UAC7B,IAAI,CAACX,UAAU,GAAGW,IAAI,CAACC,MAAM,CAACC,MAAM,GAAG,CAAC;QAC1C,CAAC,CAAC;MACJ,CAAC;MACDC,KAAK,EAAE;QACLC,MAAM,EAAE;UACNC,KAAK,EAAEhC,MAAsC;UAC7CiC,aAAa,EAAE,IAAI;UACnBC,MAAM,EAAE;YACNC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACpBC,YAAY,EAAE,CAAC;YACfb,WAAW,EAAE;;SAEhB;QACDc,KAAK,EAAE;UACLL,KAAK,EAAE5B,WAA2C;UAClD8B,MAAM,EAAE;YACNX,WAAW,EAAE,gCAAgC;YAC7Ce,aAAa,EAAE,iBAAiB;YAChCC,QAAQ,EAAE;cACRC,YAAYA,CAACC,IAAU;gBACrB;gBACA,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;gBAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEH,IAAI,CAAC;gBAE9B;gBACA,OAAOI,KAAK,CAAC,0CAA0C,EAAE;kBACvDC,MAAM,EAAE,MAAM;kBACdC,IAAI,EAAEL;iBACP,CAAC,CACDhB,IAAI,CAACsB,QAAQ,IAAIA,QAAQ,CAACC,IAAI,EAAE,CAAC,CACjCvB,IAAI,CAACC,IAAI,IAAG;kBACX,OAAO;oBACLuB,OAAO,EAAE,CAAC;oBACVT,IAAI,EAAE;sBACJU,GAAG,EAAExB,IAAI,CAACwB;;mBAEb;gBACH,CAAC,CAAC;cACJ,CAAC;cACDC,WAAWA,CAACD,GAAW;gBACrB,OAAON,KAAK,CAAC,wCAAwC,EAAE;kBACrDC,MAAM,EAAE,MAAM;kBACdO,OAAO,EAAE;oBACP,cAAc,EAAE;mBACjB;kBACDN,IAAI,EAAEO,IAAI,CAACC,SAAS,CAAC;oBAAEJ;kBAAG,CAAE;iBAC7B,CAAC,CACDzB,IAAI,CAACsB,QAAQ,IAAIA,QAAQ,CAACC,IAAI,EAAE,CAAC,CACjCvB,IAAI,CAACC,IAAI,IAAG;kBACX,OAAO;oBACLuB,OAAO,EAAE,CAAC;oBACVT,IAAI,EAAE;sBACJU,GAAG,EAAExB,IAAI,CAACwB;;mBAEb;gBACH,CAAC,CAAC;cACJ;;;SAGL;QACDK,IAAI,EAAErD,IAAoC;QAC1CsD,SAAS,EAAEpD,SAAyC;QACpDqD,KAAK,EAAEpD,KAAqC;QAC5CqD,OAAO,EAAEpD,OAAuC;QAChDqD,MAAM,EAAEpD,MAAsC;QAC9CqD,IAAI,EAAEpD,QAAwC;QAC9CqD,SAAS,EAAEpD,SAAyC;QACpDqD,UAAU,EAAEpD,UAA0C;QACtDqD,QAAQ,EAAEpD,QAAwC;QAClDqD,KAAK,EAAEpD,KAAqC;QAC5CqD,KAAK,EAAEjE;OACR;MACDkE,YAAY,EAAE;KACf,CAAC;EACJ;EAEAC,cAAcA,CAAA;IACZ,IAAI,CAAChD,MAAM,CAACK,IAAI,EAAE,CAACC,IAAI,CAACC,IAAI,IAAG;MAC7B0C,OAAO,CAACC,GAAG,CAAChB,IAAI,CAACC,SAAS,CAAC5B,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAC5C,CAAC,CAAC;EACJ;;;;;;;cAvGC/B,SAAS;QAAA2E,IAAA,GAAC,QAAQ,EAAE;UAAEC,MAAM,EAAE;QAAI,CAAE;MAAA;;;;AAD1B1D,oBAAoB,GAAA2D,UAAA,EAPhC9E,SAAS,CAAC;EACT+E,QAAQ,EAAE,mBAAmB;EAC7BC,OAAO,EAAE,CAACjF,YAAY,EAAEQ,cAAc,EAAEL,WAAW,EAAEC,mBAAmB,CAAC;EACzE8E,UAAU,EAAE,IAAI;EAChBC,QAAA,EAAAC,oBAA6C;;CAE9C,CAAC,C,EACWhE,oBAAoB,CAyGhC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}