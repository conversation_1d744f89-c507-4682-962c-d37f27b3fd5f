{"ast": null, "code": "// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\n/** Represents an HTTP response. */\nexport class HttpResponse {\n  constructor(statusCode, statusText, content) {\n    this.statusCode = statusCode;\n    this.statusText = statusText;\n    this.content = content;\n  }\n}\n/** Abstraction over an HTTP client.\r\n *\r\n * This class provides an abstraction over an HTTP client so that a different implementation can be provided on different platforms.\r\n */\nexport class HttpClient {\n  get(url, options) {\n    return this.send({\n      ...options,\n      method: \"GET\",\n      url\n    });\n  }\n  post(url, options) {\n    return this.send({\n      ...options,\n      method: \"POST\",\n      url\n    });\n  }\n  delete(url, options) {\n    return this.send({\n      ...options,\n      method: \"DELETE\",\n      url\n    });\n  }\n  /** Gets all cookies that apply to the specified URL.\r\n   *\r\n   * @param url The URL that the cookies are valid for.\r\n   * @returns {string} A string containing all the key-value cookie pairs for the specified URL.\r\n   */\n  // @ts-ignore\n  getCookieString(url) {\n    return \"\";\n  }\n}", "map": {"version": 3, "names": ["HttpResponse", "constructor", "statusCode", "statusText", "content", "HttpClient", "get", "url", "options", "send", "method", "post", "delete", "getCookieString"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/@microsoft/signalr/dist/esm/HttpClient.js"], "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n/** Represents an HTTP response. */\r\nexport class HttpResponse {\r\n    constructor(statusCode, statusText, content) {\r\n        this.statusCode = statusCode;\r\n        this.statusText = statusText;\r\n        this.content = content;\r\n    }\r\n}\r\n/** Abstraction over an HTTP client.\r\n *\r\n * This class provides an abstraction over an HTTP client so that a different implementation can be provided on different platforms.\r\n */\r\nexport class HttpClient {\r\n    get(url, options) {\r\n        return this.send({\r\n            ...options,\r\n            method: \"GET\",\r\n            url,\r\n        });\r\n    }\r\n    post(url, options) {\r\n        return this.send({\r\n            ...options,\r\n            method: \"POST\",\r\n            url,\r\n        });\r\n    }\r\n    delete(url, options) {\r\n        return this.send({\r\n            ...options,\r\n            method: \"DELETE\",\r\n            url,\r\n        });\r\n    }\r\n    /** Gets all cookies that apply to the specified URL.\r\n     *\r\n     * @param url The URL that the cookies are valid for.\r\n     * @returns {string} A string containing all the key-value cookie pairs for the specified URL.\r\n     */\r\n    // @ts-ignore\r\n    getCookieString(url) {\r\n        return \"\";\r\n    }\r\n}\r\n"], "mappings": "AAAA;AACA;AACA;AACA,OAAO,MAAMA,YAAY,CAAC;EACtBC,WAAWA,CAACC,UAAU,EAAEC,UAAU,EAAEC,OAAO,EAAE;IACzC,IAAI,CAACF,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,OAAO,GAAGA,OAAO;EAC1B;AACJ;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,UAAU,CAAC;EACpBC,GAAGA,CAACC,GAAG,EAAEC,OAAO,EAAE;IACd,OAAO,IAAI,CAACC,IAAI,CAAC;MACb,GAAGD,OAAO;MACVE,MAAM,EAAE,KAAK;MACbH;IACJ,CAAC,CAAC;EACN;EACAI,IAAIA,CAACJ,GAAG,EAAEC,OAAO,EAAE;IACf,OAAO,IAAI,CAACC,IAAI,CAAC;MACb,GAAGD,OAAO;MACVE,MAAM,EAAE,MAAM;MACdH;IACJ,CAAC,CAAC;EACN;EACAK,MAAMA,CAACL,GAAG,EAAEC,OAAO,EAAE;IACjB,OAAO,IAAI,CAACC,IAAI,CAAC;MACb,GAAGD,OAAO;MACVE,MAAM,EAAE,QAAQ;MAChBH;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACI;EACAM,eAAeA,CAACN,GAAG,EAAE;IACjB,OAAO,EAAE;EACb;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}