{"ast": null, "code": "(function () {\n  \"use strict\";\n\n  try {\n    if (typeof document < \"u\") {\n      var e = document.createElement(\"style\");\n      e.appendChild(document.createTextNode(\".ce-header{padding:.6em 0 3px;margin:0;line-height:1.25em;outline:none}.ce-header p,.ce-header div{padding:0!important;margin:0!important}\")), document.head.appendChild(e);\n    }\n  } catch (n) {\n    console.error(\"vite-plugin-css-injected-by-js\", n);\n  }\n})();\nconst a = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M6 7L6 12M6 17L6 12M6 12L12 12M12 7V12M12 17L12 12\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M19 17V10.2135C19 10.1287 18.9011 10.0824 18.836 10.1367L16 12.5\"/></svg>',\n  l = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M6 7L6 12M6 17L6 12M6 12L12 12M12 7V12M12 17L12 12\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M16 11C16 10 19 9.5 19 12C19 13.9771 16.0684 13.9997 16.0012 16.8981C15.9999 16.9533 16.0448 17 16.1 17L19.3 17\"/></svg>',\n  o = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M6 7L6 12M6 17L6 12M6 12L12 12M12 7V12M12 17L12 12\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M16 11C16 10.5 16.8323 10 17.6 10C18.3677 10 19.5 10.311 19.5 11.5C19.5 12.5315 18.7474 12.9022 18.548 12.9823C18.5378 12.9864 18.5395 13.0047 18.5503 13.0063C18.8115 13.0456 20 13.3065 20 14.8C20 16 19.5 17 17.8 17C17.8 17 16 17 16 16.3\"/></svg>',\n  h = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M6 7L6 12M6 17L6 12M6 12L12 12M12 7V12M12 17L12 12\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M18 10L15.2834 14.8511C15.246 14.9178 15.294 15 15.3704 15C16.8489 15 18.7561 15 20.2 15M19 17C19 15.7187 19 14.8813 19 13.6\"/></svg>',\n  d = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M6 7L6 12M6 17L6 12M6 12L12 12M12 7V12M12 17L12 12\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M16 15.9C16 15.9 16.3768 17 17.8 17C19.5 17 20 15.6199 20 14.7C20 12.7323 17.6745 12.0486 16.1635 12.9894C16.094 13.0327 16 12.9846 16 12.9027V10.1C16 10.0448 16.0448 10 16.1 10H19.8\"/></svg>',\n  u = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M6 7L6 12M6 17L6 12M6 12L12 12M12 7V12M12 17L12 12\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M19.5 10C16.5 10.5 16 13.3285 16 15M16 15V15C16 16.1046 16.8954 17 18 17H18.3246C19.3251 17 20.3191 16.3492 20.2522 15.3509C20.0612 12.4958 16 12.6611 16 15Z\"/></svg>',\n  g = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M9 7L9 12M9 17V12M9 12L15 12M15 7V12M15 17L15 12\"/></svg>';\n/**\n * Header block for the Editor.js.\n *\n * <AUTHOR> (<EMAIL>)\n * @copyright CodeX 2018\n * @license MIT\n * @version 2.0.0\n */\nclass v {\n  constructor({\n    data: e,\n    config: t,\n    api: s,\n    readOnly: r\n  }) {\n    this.api = s, this.readOnly = r, this._settings = t, this._data = this.normalizeData(e), this._element = this.getTag();\n  }\n  /**\n   * Styles\n   */\n  get _CSS() {\n    return {\n      block: this.api.styles.block,\n      wrapper: \"ce-header\"\n    };\n  }\n  /**\n   * Check if data is valid\n   * \n   * @param {any} data - data to check\n   * @returns {data is HeaderData}\n   * @private\n   */\n  isHeaderData(e) {\n    return e.text !== void 0;\n  }\n  /**\n   * Normalize input data\n   *\n   * @param {HeaderData} data - saved data to process\n   *\n   * @returns {HeaderData}\n   * @private\n   */\n  normalizeData(e) {\n    const t = {\n      text: \"\",\n      level: this.defaultLevel.number\n    };\n    return this.isHeaderData(e) && (t.text = e.text || \"\", e.level !== void 0 && !isNaN(parseInt(e.level.toString())) && (t.level = parseInt(e.level.toString()))), t;\n  }\n  /**\n   * Return Tool's view\n   *\n   * @returns {HTMLHeadingElement}\n   * @public\n   */\n  render() {\n    return this._element;\n  }\n  /**\n   * Returns header block tunes config\n   *\n   * @returns {Array}\n   */\n  renderSettings() {\n    return this.levels.map(e => ({\n      icon: e.svg,\n      label: this.api.i18n.t(`Heading ${e.number}`),\n      onActivate: () => this.setLevel(e.number),\n      closeOnActivate: !0,\n      isActive: this.currentLevel.number === e.number,\n      render: () => document.createElement(\"div\")\n    }));\n  }\n  /**\n   * Callback for Block's settings buttons\n   *\n   * @param {number} level - level to set\n   */\n  setLevel(e) {\n    this.data = {\n      level: e,\n      text: this.data.text\n    };\n  }\n  /**\n   * Method that specified how to merge two Text blocks.\n   * Called by Editor.js by backspace at the beginning of the Block\n   *\n   * @param {HeaderData} data - saved data to merger with current block\n   * @public\n   */\n  merge(e) {\n    this._element.insertAdjacentHTML(\"beforeend\", e.text);\n  }\n  /**\n   * Validate Text block data:\n   * - check for emptiness\n   *\n   * @param {HeaderData} blockData — data received after saving\n   * @returns {boolean} false if saved data is not correct, otherwise true\n   * @public\n   */\n  validate(e) {\n    return e.text.trim() !== \"\";\n  }\n  /**\n   * Extract Tool's data from the view\n   *\n   * @param {HTMLHeadingElement} toolsContent - Text tools rendered view\n   * @returns {HeaderData} - saved data\n   * @public\n   */\n  save(e) {\n    return {\n      text: e.innerHTML,\n      level: this.currentLevel.number\n    };\n  }\n  /**\n   * Allow Header to be converted to/from other blocks\n   */\n  static get conversionConfig() {\n    return {\n      export: \"text\",\n      // use 'text' property for other blocks\n      import: \"text\"\n      // fill 'text' property from other block's export string\n    };\n  }\n  /**\n   * Sanitizer Rules\n   */\n  static get sanitize() {\n    return {\n      level: !1,\n      text: {}\n    };\n  }\n  /**\n   * Returns true to notify core that read-only is supported\n   *\n   * @returns {boolean}\n   */\n  static get isReadOnlySupported() {\n    return !0;\n  }\n  /**\n   * Get current Tools`s data\n   *\n   * @returns {HeaderData} Current data\n   * @private\n   */\n  get data() {\n    return this._data.text = this._element.innerHTML, this._data.level = this.currentLevel.number, this._data;\n  }\n  /**\n   * Store data in plugin:\n   * - at the this._data property\n   * - at the HTML\n   *\n   * @param {HeaderData} data — data to set\n   * @private\n   */\n  set data(e) {\n    if (this._data = this.normalizeData(e), e.level !== void 0 && this._element.parentNode) {\n      const t = this.getTag();\n      t.innerHTML = this._element.innerHTML, this._element.parentNode.replaceChild(t, this._element), this._element = t;\n    }\n    e.text !== void 0 && (this._element.innerHTML = this._data.text || \"\");\n  }\n  /**\n   * Get tag for target level\n   * By default returns second-leveled header\n   *\n   * @returns {HTMLElement}\n   */\n  getTag() {\n    const e = document.createElement(this.currentLevel.tag);\n    return e.innerHTML = this._data.text || \"\", e.classList.add(this._CSS.wrapper), e.contentEditable = this.readOnly ? \"false\" : \"true\", e.dataset.placeholder = this.api.i18n.t(this._settings.placeholder || \"\"), e;\n  }\n  /**\n   * Get current level\n   *\n   * @returns {level}\n   */\n  get currentLevel() {\n    let e = this.levels.find(t => t.number === this._data.level);\n    return e || (e = this.defaultLevel), e;\n  }\n  /**\n   * Return default level\n   *\n   * @returns {level}\n   */\n  get defaultLevel() {\n    if (this._settings.defaultLevel) {\n      const e = this.levels.find(t => t.number === this._settings.defaultLevel);\n      if (e) return e;\n      console.warn(\"(ง'̀-'́)ง Heading Tool: the default level specified was not found in available levels\");\n    }\n    return this.levels[1];\n  }\n  /**\n   * @typedef {object} level\n   * @property {number} number - level number\n   * @property {string} tag - tag corresponds with level number\n   * @property {string} svg - icon\n   */\n  /**\n   * Available header levels\n   *\n   * @returns {level[]}\n   */\n  get levels() {\n    const e = [{\n      number: 1,\n      tag: \"H1\",\n      svg: a\n    }, {\n      number: 2,\n      tag: \"H2\",\n      svg: l\n    }, {\n      number: 3,\n      tag: \"H3\",\n      svg: o\n    }, {\n      number: 4,\n      tag: \"H4\",\n      svg: h\n    }, {\n      number: 5,\n      tag: \"H5\",\n      svg: d\n    }, {\n      number: 6,\n      tag: \"H6\",\n      svg: u\n    }];\n    return this._settings.levels ? e.filter(t => this._settings.levels.includes(t.number)) : e;\n  }\n  /**\n   * Handle H1-H6 tags on paste to substitute it with header Tool\n   *\n   * @param {PasteEvent} event - event with pasted content\n   */\n  onPaste(e) {\n    const t = e.detail;\n    if (\"data\" in t) {\n      const s = t.data;\n      let r = this.defaultLevel.number;\n      switch (s.tagName) {\n        case \"H1\":\n          r = 1;\n          break;\n        case \"H2\":\n          r = 2;\n          break;\n        case \"H3\":\n          r = 3;\n          break;\n        case \"H4\":\n          r = 4;\n          break;\n        case \"H5\":\n          r = 5;\n          break;\n        case \"H6\":\n          r = 6;\n          break;\n      }\n      this._settings.levels && (r = this._settings.levels.reduce((n, i) => Math.abs(i - r) < Math.abs(n - r) ? i : n)), this.data = {\n        level: r,\n        text: s.innerHTML\n      };\n    }\n  }\n  /**\n   * Used by Editor.js paste handling API.\n   * Provides configuration to handle H1-H6 tags.\n   *\n   * @returns {{handler: (function(HTMLElement): {text: string}), tags: string[]}}\n   */\n  static get pasteConfig() {\n    return {\n      tags: [\"H1\", \"H2\", \"H3\", \"H4\", \"H5\", \"H6\"]\n    };\n  }\n  /**\n   * Get Tool toolbox settings\n   * icon - Tool icon's SVG\n   * title - title to show in toolbox\n   *\n   * @returns {{icon: string, title: string}}\n   */\n  static get toolbox() {\n    return {\n      icon: g,\n      title: \"Heading\"\n    };\n  }\n}\nexport { v as default };", "map": {"version": 3, "names": ["document", "e", "createElement", "append<PERSON><PERSON><PERSON>", "createTextNode", "head", "n", "console", "error", "a", "l", "o", "h", "d", "u", "g", "v", "constructor", "data", "config", "t", "api", "s", "readOnly", "r", "_settings", "_data", "normalizeData", "_element", "getTag", "_CSS", "block", "styles", "wrapper", "isHeaderData", "text", "level", "defaultLevel", "number", "isNaN", "parseInt", "toString", "render", "renderSettings", "levels", "map", "icon", "svg", "label", "i18n", "onActivate", "setLevel", "closeOnActivate", "isActive", "currentLevel", "merge", "insertAdjacentHTML", "validate", "trim", "save", "innerHTML", "conversionConfig", "export", "import", "sanitize", "isReadOnlySupported", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "tag", "classList", "add", "contentEditable", "dataset", "placeholder", "find", "warn", "filter", "includes", "onPaste", "detail", "tagName", "reduce", "i", "Math", "abs", "pasteConfig", "tags", "toolbox", "title", "default"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/@editorjs/header/dist/header.mjs"], "sourcesContent": ["(function(){\"use strict\";try{if(typeof document<\"u\"){var e=document.createElement(\"style\");e.appendChild(document.createTextNode(\".ce-header{padding:.6em 0 3px;margin:0;line-height:1.25em;outline:none}.ce-header p,.ce-header div{padding:0!important;margin:0!important}\")),document.head.appendChild(e)}}catch(n){console.error(\"vite-plugin-css-injected-by-js\",n)}})();\nconst a = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M6 7L6 12M6 17L6 12M6 12L12 12M12 7V12M12 17L12 12\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M19 17V10.2135C19 10.1287 18.9011 10.0824 18.836 10.1367L16 12.5\"/></svg>', l = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M6 7L6 12M6 17L6 12M6 12L12 12M12 7V12M12 17L12 12\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M16 11C16 10 19 9.5 19 12C19 13.9771 16.0684 13.9997 16.0012 16.8981C15.9999 16.9533 16.0448 17 16.1 17L19.3 17\"/></svg>', o = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M6 7L6 12M6 17L6 12M6 12L12 12M12 7V12M12 17L12 12\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M16 11C16 10.5 16.8323 10 17.6 10C18.3677 10 19.5 10.311 19.5 11.5C19.5 12.5315 18.7474 12.9022 18.548 12.9823C18.5378 12.9864 18.5395 13.0047 18.5503 13.0063C18.8115 13.0456 20 13.3065 20 14.8C20 16 19.5 17 17.8 17C17.8 17 16 17 16 16.3\"/></svg>', h = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M6 7L6 12M6 17L6 12M6 12L12 12M12 7V12M12 17L12 12\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M18 10L15.2834 14.8511C15.246 14.9178 15.294 15 15.3704 15C16.8489 15 18.7561 15 20.2 15M19 17C19 15.7187 19 14.8813 19 13.6\"/></svg>', d = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M6 7L6 12M6 17L6 12M6 12L12 12M12 7V12M12 17L12 12\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M16 15.9C16 15.9 16.3768 17 17.8 17C19.5 17 20 15.6199 20 14.7C20 12.7323 17.6745 12.0486 16.1635 12.9894C16.094 13.0327 16 12.9846 16 12.9027V10.1C16 10.0448 16.0448 10 16.1 10H19.8\"/></svg>', u = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M6 7L6 12M6 17L6 12M6 12L12 12M12 7V12M12 17L12 12\"/><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M19.5 10C16.5 10.5 16 13.3285 16 15M16 15V15C16 16.1046 16.8954 17 18 17H18.3246C19.3251 17 20.3191 16.3492 20.2522 15.3509C20.0612 12.4958 16 12.6611 16 15Z\"/></svg>', g = '<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"none\" viewBox=\"0 0 24 24\"><path stroke=\"currentColor\" stroke-linecap=\"round\" stroke-width=\"2\" d=\"M9 7L9 12M9 17V12M9 12L15 12M15 7V12M15 17L15 12\"/></svg>';\n/**\n * Header block for the Editor.js.\n *\n * <AUTHOR> (<EMAIL>)\n * @copyright CodeX 2018\n * @license MIT\n * @version 2.0.0\n */\nclass v {\n  constructor({ data: e, config: t, api: s, readOnly: r }) {\n    this.api = s, this.readOnly = r, this._settings = t, this._data = this.normalizeData(e), this._element = this.getTag();\n  }\n  /**\n   * Styles\n   */\n  get _CSS() {\n    return {\n      block: this.api.styles.block,\n      wrapper: \"ce-header\"\n    };\n  }\n  /**\n   * Check if data is valid\n   * \n   * @param {any} data - data to check\n   * @returns {data is HeaderData}\n   * @private\n   */\n  isHeaderData(e) {\n    return e.text !== void 0;\n  }\n  /**\n   * Normalize input data\n   *\n   * @param {HeaderData} data - saved data to process\n   *\n   * @returns {HeaderData}\n   * @private\n   */\n  normalizeData(e) {\n    const t = { text: \"\", level: this.defaultLevel.number };\n    return this.isHeaderData(e) && (t.text = e.text || \"\", e.level !== void 0 && !isNaN(parseInt(e.level.toString())) && (t.level = parseInt(e.level.toString()))), t;\n  }\n  /**\n   * Return Tool's view\n   *\n   * @returns {HTMLHeadingElement}\n   * @public\n   */\n  render() {\n    return this._element;\n  }\n  /**\n   * Returns header block tunes config\n   *\n   * @returns {Array}\n   */\n  renderSettings() {\n    return this.levels.map((e) => ({\n      icon: e.svg,\n      label: this.api.i18n.t(`Heading ${e.number}`),\n      onActivate: () => this.setLevel(e.number),\n      closeOnActivate: !0,\n      isActive: this.currentLevel.number === e.number,\n      render: () => document.createElement(\"div\")\n    }));\n  }\n  /**\n   * Callback for Block's settings buttons\n   *\n   * @param {number} level - level to set\n   */\n  setLevel(e) {\n    this.data = {\n      level: e,\n      text: this.data.text\n    };\n  }\n  /**\n   * Method that specified how to merge two Text blocks.\n   * Called by Editor.js by backspace at the beginning of the Block\n   *\n   * @param {HeaderData} data - saved data to merger with current block\n   * @public\n   */\n  merge(e) {\n    this._element.insertAdjacentHTML(\"beforeend\", e.text);\n  }\n  /**\n   * Validate Text block data:\n   * - check for emptiness\n   *\n   * @param {HeaderData} blockData — data received after saving\n   * @returns {boolean} false if saved data is not correct, otherwise true\n   * @public\n   */\n  validate(e) {\n    return e.text.trim() !== \"\";\n  }\n  /**\n   * Extract Tool's data from the view\n   *\n   * @param {HTMLHeadingElement} toolsContent - Text tools rendered view\n   * @returns {HeaderData} - saved data\n   * @public\n   */\n  save(e) {\n    return {\n      text: e.innerHTML,\n      level: this.currentLevel.number\n    };\n  }\n  /**\n   * Allow Header to be converted to/from other blocks\n   */\n  static get conversionConfig() {\n    return {\n      export: \"text\",\n      // use 'text' property for other blocks\n      import: \"text\"\n      // fill 'text' property from other block's export string\n    };\n  }\n  /**\n   * Sanitizer Rules\n   */\n  static get sanitize() {\n    return {\n      level: !1,\n      text: {}\n    };\n  }\n  /**\n   * Returns true to notify core that read-only is supported\n   *\n   * @returns {boolean}\n   */\n  static get isReadOnlySupported() {\n    return !0;\n  }\n  /**\n   * Get current Tools`s data\n   *\n   * @returns {HeaderData} Current data\n   * @private\n   */\n  get data() {\n    return this._data.text = this._element.innerHTML, this._data.level = this.currentLevel.number, this._data;\n  }\n  /**\n   * Store data in plugin:\n   * - at the this._data property\n   * - at the HTML\n   *\n   * @param {HeaderData} data — data to set\n   * @private\n   */\n  set data(e) {\n    if (this._data = this.normalizeData(e), e.level !== void 0 && this._element.parentNode) {\n      const t = this.getTag();\n      t.innerHTML = this._element.innerHTML, this._element.parentNode.replaceChild(t, this._element), this._element = t;\n    }\n    e.text !== void 0 && (this._element.innerHTML = this._data.text || \"\");\n  }\n  /**\n   * Get tag for target level\n   * By default returns second-leveled header\n   *\n   * @returns {HTMLElement}\n   */\n  getTag() {\n    const e = document.createElement(this.currentLevel.tag);\n    return e.innerHTML = this._data.text || \"\", e.classList.add(this._CSS.wrapper), e.contentEditable = this.readOnly ? \"false\" : \"true\", e.dataset.placeholder = this.api.i18n.t(this._settings.placeholder || \"\"), e;\n  }\n  /**\n   * Get current level\n   *\n   * @returns {level}\n   */\n  get currentLevel() {\n    let e = this.levels.find((t) => t.number === this._data.level);\n    return e || (e = this.defaultLevel), e;\n  }\n  /**\n   * Return default level\n   *\n   * @returns {level}\n   */\n  get defaultLevel() {\n    if (this._settings.defaultLevel) {\n      const e = this.levels.find((t) => t.number === this._settings.defaultLevel);\n      if (e)\n        return e;\n      console.warn(\"(ง'̀-'́)ง Heading Tool: the default level specified was not found in available levels\");\n    }\n    return this.levels[1];\n  }\n  /**\n   * @typedef {object} level\n   * @property {number} number - level number\n   * @property {string} tag - tag corresponds with level number\n   * @property {string} svg - icon\n   */\n  /**\n   * Available header levels\n   *\n   * @returns {level[]}\n   */\n  get levels() {\n    const e = [\n      {\n        number: 1,\n        tag: \"H1\",\n        svg: a\n      },\n      {\n        number: 2,\n        tag: \"H2\",\n        svg: l\n      },\n      {\n        number: 3,\n        tag: \"H3\",\n        svg: o\n      },\n      {\n        number: 4,\n        tag: \"H4\",\n        svg: h\n      },\n      {\n        number: 5,\n        tag: \"H5\",\n        svg: d\n      },\n      {\n        number: 6,\n        tag: \"H6\",\n        svg: u\n      }\n    ];\n    return this._settings.levels ? e.filter(\n      (t) => this._settings.levels.includes(t.number)\n    ) : e;\n  }\n  /**\n   * Handle H1-H6 tags on paste to substitute it with header Tool\n   *\n   * @param {PasteEvent} event - event with pasted content\n   */\n  onPaste(e) {\n    const t = e.detail;\n    if (\"data\" in t) {\n      const s = t.data;\n      let r = this.defaultLevel.number;\n      switch (s.tagName) {\n        case \"H1\":\n          r = 1;\n          break;\n        case \"H2\":\n          r = 2;\n          break;\n        case \"H3\":\n          r = 3;\n          break;\n        case \"H4\":\n          r = 4;\n          break;\n        case \"H5\":\n          r = 5;\n          break;\n        case \"H6\":\n          r = 6;\n          break;\n      }\n      this._settings.levels && (r = this._settings.levels.reduce((n, i) => Math.abs(i - r) < Math.abs(n - r) ? i : n)), this.data = {\n        level: r,\n        text: s.innerHTML\n      };\n    }\n  }\n  /**\n   * Used by Editor.js paste handling API.\n   * Provides configuration to handle H1-H6 tags.\n   *\n   * @returns {{handler: (function(HTMLElement): {text: string}), tags: string[]}}\n   */\n  static get pasteConfig() {\n    return {\n      tags: [\"H1\", \"H2\", \"H3\", \"H4\", \"H5\", \"H6\"]\n    };\n  }\n  /**\n   * Get Tool toolbox settings\n   * icon - Tool icon's SVG\n   * title - title to show in toolbox\n   *\n   * @returns {{icon: string, title: string}}\n   */\n  static get toolbox() {\n    return {\n      icon: g,\n      title: \"Heading\"\n    };\n  }\n}\nexport {\n  v as default\n};\n"], "mappings": "AAAA,CAAC,YAAU;EAAC,YAAY;;EAAC,IAAG;IAAC,IAAG,OAAOA,QAAQ,GAAC,GAAG,EAAC;MAAC,IAAIC,CAAC,GAACD,QAAQ,CAACE,aAAa,CAAC,OAAO,CAAC;MAACD,CAAC,CAACE,WAAW,CAACH,QAAQ,CAACI,cAAc,CAAC,4IAA4I,CAAC,CAAC,EAACJ,QAAQ,CAACK,IAAI,CAACF,WAAW,CAACF,CAAC,CAAC;IAAA;EAAC,CAAC,QAAMK,CAAC,EAAC;IAACC,OAAO,CAACC,KAAK,CAAC,gCAAgC,EAACF,CAAC,CAAC;EAAA;AAAC,CAAC,EAAE,CAAC;AAC7W,MAAMG,CAAC,GAAG,6WAA6W;EAAEC,CAAC,GAAG,4ZAA4Z;EAAEC,CAAC,GAAG,0hBAA0hB;EAAEC,CAAC,GAAG,yaAAya;EAAEC,CAAC,GAAG,meAAme;EAAEC,CAAC,GAAG,0cAA0c;EAAEC,CAAC,GAAG,iOAAiO;AACx4F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,CAAC,CAAC;EACNC,WAAWA,CAAC;IAAEC,IAAI,EAAEjB,CAAC;IAAEkB,MAAM,EAAEC,CAAC;IAAEC,GAAG,EAAEC,CAAC;IAAEC,QAAQ,EAAEC;EAAE,CAAC,EAAE;IACvD,IAAI,CAACH,GAAG,GAAGC,CAAC,EAAE,IAAI,CAACC,QAAQ,GAAGC,CAAC,EAAE,IAAI,CAACC,SAAS,GAAGL,CAAC,EAAE,IAAI,CAACM,KAAK,GAAG,IAAI,CAACC,aAAa,CAAC1B,CAAC,CAAC,EAAE,IAAI,CAAC2B,QAAQ,GAAG,IAAI,CAACC,MAAM,CAAC,CAAC;EACxH;EACA;AACF;AACA;EACE,IAAIC,IAAIA,CAAA,EAAG;IACT,OAAO;MACLC,KAAK,EAAE,IAAI,CAACV,GAAG,CAACW,MAAM,CAACD,KAAK;MAC5BE,OAAO,EAAE;IACX,CAAC;EACH;EACA;AACF;AACA;AACA;AACA;AACA;AACA;EACEC,YAAYA,CAACjC,CAAC,EAAE;IACd,OAAOA,CAAC,CAACkC,IAAI,KAAK,KAAK,CAAC;EAC1B;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACER,aAAaA,CAAC1B,CAAC,EAAE;IACf,MAAMmB,CAAC,GAAG;MAAEe,IAAI,EAAE,EAAE;MAAEC,KAAK,EAAE,IAAI,CAACC,YAAY,CAACC;IAAO,CAAC;IACvD,OAAO,IAAI,CAACJ,YAAY,CAACjC,CAAC,CAAC,KAAKmB,CAAC,CAACe,IAAI,GAAGlC,CAAC,CAACkC,IAAI,IAAI,EAAE,EAAElC,CAAC,CAACmC,KAAK,KAAK,KAAK,CAAC,IAAI,CAACG,KAAK,CAACC,QAAQ,CAACvC,CAAC,CAACmC,KAAK,CAACK,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAKrB,CAAC,CAACgB,KAAK,GAAGI,QAAQ,CAACvC,CAAC,CAACmC,KAAK,CAACK,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAErB,CAAC;EACnK;EACA;AACF;AACA;AACA;AACA;AACA;EACEsB,MAAMA,CAAA,EAAG;IACP,OAAO,IAAI,CAACd,QAAQ;EACtB;EACA;AACF;AACA;AACA;AACA;EACEe,cAAcA,CAAA,EAAG;IACf,OAAO,IAAI,CAACC,MAAM,CAACC,GAAG,CAAE5C,CAAC,KAAM;MAC7B6C,IAAI,EAAE7C,CAAC,CAAC8C,GAAG;MACXC,KAAK,EAAE,IAAI,CAAC3B,GAAG,CAAC4B,IAAI,CAAC7B,CAAC,CAAE,WAAUnB,CAAC,CAACqC,MAAO,EAAC,CAAC;MAC7CY,UAAU,EAAEA,CAAA,KAAM,IAAI,CAACC,QAAQ,CAAClD,CAAC,CAACqC,MAAM,CAAC;MACzCc,eAAe,EAAE,CAAC,CAAC;MACnBC,QAAQ,EAAE,IAAI,CAACC,YAAY,CAAChB,MAAM,KAAKrC,CAAC,CAACqC,MAAM;MAC/CI,MAAM,EAAEA,CAAA,KAAM1C,QAAQ,CAACE,aAAa,CAAC,KAAK;IAC5C,CAAC,CAAC,CAAC;EACL;EACA;AACF;AACA;AACA;AACA;EACEiD,QAAQA,CAAClD,CAAC,EAAE;IACV,IAAI,CAACiB,IAAI,GAAG;MACVkB,KAAK,EAAEnC,CAAC;MACRkC,IAAI,EAAE,IAAI,CAACjB,IAAI,CAACiB;IAClB,CAAC;EACH;EACA;AACF;AACA;AACA;AACA;AACA;AACA;EACEoB,KAAKA,CAACtD,CAAC,EAAE;IACP,IAAI,CAAC2B,QAAQ,CAAC4B,kBAAkB,CAAC,WAAW,EAAEvD,CAAC,CAACkC,IAAI,CAAC;EACvD;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEsB,QAAQA,CAACxD,CAAC,EAAE;IACV,OAAOA,CAAC,CAACkC,IAAI,CAACuB,IAAI,CAAC,CAAC,KAAK,EAAE;EAC7B;EACA;AACF;AACA;AACA;AACA;AACA;AACA;EACEC,IAAIA,CAAC1D,CAAC,EAAE;IACN,OAAO;MACLkC,IAAI,EAAElC,CAAC,CAAC2D,SAAS;MACjBxB,KAAK,EAAE,IAAI,CAACkB,YAAY,CAAChB;IAC3B,CAAC;EACH;EACA;AACF;AACA;EACE,WAAWuB,gBAAgBA,CAAA,EAAG;IAC5B,OAAO;MACLC,MAAM,EAAE,MAAM;MACd;MACAC,MAAM,EAAE;MACR;IACF,CAAC;EACH;EACA;AACF;AACA;EACE,WAAWC,QAAQA,CAAA,EAAG;IACpB,OAAO;MACL5B,KAAK,EAAE,CAAC,CAAC;MACTD,IAAI,EAAE,CAAC;IACT,CAAC;EACH;EACA;AACF;AACA;AACA;AACA;EACE,WAAW8B,mBAAmBA,CAAA,EAAG;IAC/B,OAAO,CAAC,CAAC;EACX;EACA;AACF;AACA;AACA;AACA;AACA;EACE,IAAI/C,IAAIA,CAAA,EAAG;IACT,OAAO,IAAI,CAACQ,KAAK,CAACS,IAAI,GAAG,IAAI,CAACP,QAAQ,CAACgC,SAAS,EAAE,IAAI,CAAClC,KAAK,CAACU,KAAK,GAAG,IAAI,CAACkB,YAAY,CAAChB,MAAM,EAAE,IAAI,CAACZ,KAAK;EAC3G;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,IAAIR,IAAIA,CAACjB,CAAC,EAAE;IACV,IAAI,IAAI,CAACyB,KAAK,GAAG,IAAI,CAACC,aAAa,CAAC1B,CAAC,CAAC,EAAEA,CAAC,CAACmC,KAAK,KAAK,KAAK,CAAC,IAAI,IAAI,CAACR,QAAQ,CAACsC,UAAU,EAAE;MACtF,MAAM9C,CAAC,GAAG,IAAI,CAACS,MAAM,CAAC,CAAC;MACvBT,CAAC,CAACwC,SAAS,GAAG,IAAI,CAAChC,QAAQ,CAACgC,SAAS,EAAE,IAAI,CAAChC,QAAQ,CAACsC,UAAU,CAACC,YAAY,CAAC/C,CAAC,EAAE,IAAI,CAACQ,QAAQ,CAAC,EAAE,IAAI,CAACA,QAAQ,GAAGR,CAAC;IACnH;IACAnB,CAAC,CAACkC,IAAI,KAAK,KAAK,CAAC,KAAK,IAAI,CAACP,QAAQ,CAACgC,SAAS,GAAG,IAAI,CAAClC,KAAK,CAACS,IAAI,IAAI,EAAE,CAAC;EACxE;EACA;AACF;AACA;AACA;AACA;AACA;EACEN,MAAMA,CAAA,EAAG;IACP,MAAM5B,CAAC,GAAGD,QAAQ,CAACE,aAAa,CAAC,IAAI,CAACoD,YAAY,CAACc,GAAG,CAAC;IACvD,OAAOnE,CAAC,CAAC2D,SAAS,GAAG,IAAI,CAAClC,KAAK,CAACS,IAAI,IAAI,EAAE,EAAElC,CAAC,CAACoE,SAAS,CAACC,GAAG,CAAC,IAAI,CAACxC,IAAI,CAACG,OAAO,CAAC,EAAEhC,CAAC,CAACsE,eAAe,GAAG,IAAI,CAAChD,QAAQ,GAAG,OAAO,GAAG,MAAM,EAAEtB,CAAC,CAACuE,OAAO,CAACC,WAAW,GAAG,IAAI,CAACpD,GAAG,CAAC4B,IAAI,CAAC7B,CAAC,CAAC,IAAI,CAACK,SAAS,CAACgD,WAAW,IAAI,EAAE,CAAC,EAAExE,CAAC;EACpN;EACA;AACF;AACA;AACA;AACA;EACE,IAAIqD,YAAYA,CAAA,EAAG;IACjB,IAAIrD,CAAC,GAAG,IAAI,CAAC2C,MAAM,CAAC8B,IAAI,CAAEtD,CAAC,IAAKA,CAAC,CAACkB,MAAM,KAAK,IAAI,CAACZ,KAAK,CAACU,KAAK,CAAC;IAC9D,OAAOnC,CAAC,KAAKA,CAAC,GAAG,IAAI,CAACoC,YAAY,CAAC,EAAEpC,CAAC;EACxC;EACA;AACF;AACA;AACA;AACA;EACE,IAAIoC,YAAYA,CAAA,EAAG;IACjB,IAAI,IAAI,CAACZ,SAAS,CAACY,YAAY,EAAE;MAC/B,MAAMpC,CAAC,GAAG,IAAI,CAAC2C,MAAM,CAAC8B,IAAI,CAAEtD,CAAC,IAAKA,CAAC,CAACkB,MAAM,KAAK,IAAI,CAACb,SAAS,CAACY,YAAY,CAAC;MAC3E,IAAIpC,CAAC,EACH,OAAOA,CAAC;MACVM,OAAO,CAACoE,IAAI,CAAC,uFAAuF,CAAC;IACvG;IACA,OAAO,IAAI,CAAC/B,MAAM,CAAC,CAAC,CAAC;EACvB;EACA;AACF;AACA;AACA;AACA;AACA;EACE;AACF;AACA;AACA;AACA;EACE,IAAIA,MAAMA,CAAA,EAAG;IACX,MAAM3C,CAAC,GAAG,CACR;MACEqC,MAAM,EAAE,CAAC;MACT8B,GAAG,EAAE,IAAI;MACTrB,GAAG,EAAEtC;IACP,CAAC,EACD;MACE6B,MAAM,EAAE,CAAC;MACT8B,GAAG,EAAE,IAAI;MACTrB,GAAG,EAAErC;IACP,CAAC,EACD;MACE4B,MAAM,EAAE,CAAC;MACT8B,GAAG,EAAE,IAAI;MACTrB,GAAG,EAAEpC;IACP,CAAC,EACD;MACE2B,MAAM,EAAE,CAAC;MACT8B,GAAG,EAAE,IAAI;MACTrB,GAAG,EAAEnC;IACP,CAAC,EACD;MACE0B,MAAM,EAAE,CAAC;MACT8B,GAAG,EAAE,IAAI;MACTrB,GAAG,EAAElC;IACP,CAAC,EACD;MACEyB,MAAM,EAAE,CAAC;MACT8B,GAAG,EAAE,IAAI;MACTrB,GAAG,EAAEjC;IACP,CAAC,CACF;IACD,OAAO,IAAI,CAACW,SAAS,CAACmB,MAAM,GAAG3C,CAAC,CAAC2E,MAAM,CACpCxD,CAAC,IAAK,IAAI,CAACK,SAAS,CAACmB,MAAM,CAACiC,QAAQ,CAACzD,CAAC,CAACkB,MAAM,CAChD,CAAC,GAAGrC,CAAC;EACP;EACA;AACF;AACA;AACA;AACA;EACE6E,OAAOA,CAAC7E,CAAC,EAAE;IACT,MAAMmB,CAAC,GAAGnB,CAAC,CAAC8E,MAAM;IAClB,IAAI,MAAM,IAAI3D,CAAC,EAAE;MACf,MAAME,CAAC,GAAGF,CAAC,CAACF,IAAI;MAChB,IAAIM,CAAC,GAAG,IAAI,CAACa,YAAY,CAACC,MAAM;MAChC,QAAQhB,CAAC,CAAC0D,OAAO;QACf,KAAK,IAAI;UACPxD,CAAC,GAAG,CAAC;UACL;QACF,KAAK,IAAI;UACPA,CAAC,GAAG,CAAC;UACL;QACF,KAAK,IAAI;UACPA,CAAC,GAAG,CAAC;UACL;QACF,KAAK,IAAI;UACPA,CAAC,GAAG,CAAC;UACL;QACF,KAAK,IAAI;UACPA,CAAC,GAAG,CAAC;UACL;QACF,KAAK,IAAI;UACPA,CAAC,GAAG,CAAC;UACL;MACJ;MACA,IAAI,CAACC,SAAS,CAACmB,MAAM,KAAKpB,CAAC,GAAG,IAAI,CAACC,SAAS,CAACmB,MAAM,CAACqC,MAAM,CAAC,CAAC3E,CAAC,EAAE4E,CAAC,KAAKC,IAAI,CAACC,GAAG,CAACF,CAAC,GAAG1D,CAAC,CAAC,GAAG2D,IAAI,CAACC,GAAG,CAAC9E,CAAC,GAAGkB,CAAC,CAAC,GAAG0D,CAAC,GAAG5E,CAAC,CAAC,CAAC,EAAE,IAAI,CAACY,IAAI,GAAG;QAC5HkB,KAAK,EAAEZ,CAAC;QACRW,IAAI,EAAEb,CAAC,CAACsC;MACV,CAAC;IACH;EACF;EACA;AACF;AACA;AACA;AACA;AACA;EACE,WAAWyB,WAAWA,CAAA,EAAG;IACvB,OAAO;MACLC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;IAC3C,CAAC;EACH;EACA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,WAAWC,OAAOA,CAAA,EAAG;IACnB,OAAO;MACLzC,IAAI,EAAE/B,CAAC;MACPyE,KAAK,EAAE;IACT,CAAC;EACH;AACF;AACA,SACExE,CAAC,IAAIyE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}