{"ast": null, "code": "import Picker from './picker.js';\nclass ColorPicker extends Picker {\n  constructor(select, label) {\n    super(select);\n    this.label.innerHTML = label;\n    this.container.classList.add('ql-color-picker');\n    Array.from(this.container.querySelectorAll('.ql-picker-item')).slice(0, 7).forEach(item => {\n      item.classList.add('ql-primary');\n    });\n  }\n  buildItem(option) {\n    const item = super.buildItem(option);\n    item.style.backgroundColor = option.getAttribute('value') || '';\n    return item;\n  }\n  selectItem(item, trigger) {\n    super.selectItem(item, trigger);\n    const colorLabel = this.label.querySelector('.ql-color-label');\n    const value = item ? item.getAttribute('data-value') || '' : '';\n    if (colorLabel) {\n      if (colorLabel.tagName === 'line') {\n        colorLabel.style.stroke = value;\n      } else {\n        colorLabel.style.fill = value;\n      }\n    }\n  }\n}\nexport default ColorPicker;", "map": {"version": 3, "names": ["Picker", "ColorPicker", "constructor", "select", "label", "innerHTML", "container", "classList", "add", "Array", "from", "querySelectorAll", "slice", "for<PERSON>ach", "item", "buildItem", "option", "style", "backgroundColor", "getAttribute", "selectItem", "trigger", "colorLabel", "querySelector", "value", "tagName", "stroke", "fill"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/quill/ui/color-picker.js"], "sourcesContent": ["import Picker from './picker.js';\nclass ColorPicker extends Picker {\n  constructor(select, label) {\n    super(select);\n    this.label.innerHTML = label;\n    this.container.classList.add('ql-color-picker');\n    Array.from(this.container.querySelectorAll('.ql-picker-item')).slice(0, 7).forEach(item => {\n      item.classList.add('ql-primary');\n    });\n  }\n  buildItem(option) {\n    const item = super.buildItem(option);\n    item.style.backgroundColor = option.getAttribute('value') || '';\n    return item;\n  }\n  selectItem(item, trigger) {\n    super.selectItem(item, trigger);\n    const colorLabel = this.label.querySelector('.ql-color-label');\n    const value = item ? item.getAttribute('data-value') || '' : '';\n    if (colorLabel) {\n      if (colorLabel.tagName === 'line') {\n        colorLabel.style.stroke = value;\n      } else {\n        colorLabel.style.fill = value;\n      }\n    }\n  }\n}\nexport default ColorPicker;\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,aAAa;AAChC,MAAMC,WAAW,SAASD,MAAM,CAAC;EAC/BE,WAAWA,CAACC,MAAM,EAAEC,KAAK,EAAE;IACzB,KAAK,CAACD,MAAM,CAAC;IACb,IAAI,CAACC,KAAK,CAACC,SAAS,GAAGD,KAAK;IAC5B,IAAI,CAACE,SAAS,CAACC,SAAS,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAC/CC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACJ,SAAS,CAACK,gBAAgB,CAAC,iBAAiB,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,OAAO,CAACC,IAAI,IAAI;MACzFA,IAAI,CAACP,SAAS,CAACC,GAAG,CAAC,YAAY,CAAC;IAClC,CAAC,CAAC;EACJ;EACAO,SAASA,CAACC,MAAM,EAAE;IAChB,MAAMF,IAAI,GAAG,KAAK,CAACC,SAAS,CAACC,MAAM,CAAC;IACpCF,IAAI,CAACG,KAAK,CAACC,eAAe,GAAGF,MAAM,CAACG,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE;IAC/D,OAAOL,IAAI;EACb;EACAM,UAAUA,CAACN,IAAI,EAAEO,OAAO,EAAE;IACxB,KAAK,CAACD,UAAU,CAACN,IAAI,EAAEO,OAAO,CAAC;IAC/B,MAAMC,UAAU,GAAG,IAAI,CAAClB,KAAK,CAACmB,aAAa,CAAC,iBAAiB,CAAC;IAC9D,MAAMC,KAAK,GAAGV,IAAI,GAAGA,IAAI,CAACK,YAAY,CAAC,YAAY,CAAC,IAAI,EAAE,GAAG,EAAE;IAC/D,IAAIG,UAAU,EAAE;MACd,IAAIA,UAAU,CAACG,OAAO,KAAK,MAAM,EAAE;QACjCH,UAAU,CAACL,KAAK,CAACS,MAAM,GAAGF,KAAK;MACjC,CAAC,MAAM;QACLF,UAAU,CAACL,KAAK,CAACU,IAAI,GAAGH,KAAK;MAC/B;IACF;EACF;AACF;AACA,eAAevB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}