{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/source/ai-hub/WebApp/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { BehaviorSubject, Subject } from 'rxjs';\nimport * as signalR from '@microsoft/signalr';\nimport { getRemoteServiceBaseUrl } from '../app.config';\nimport { Router, NavigationStart } from '@angular/router';\nlet ChatService = class ChatService {\n  constructor(router) {\n    this.router = router;\n    this.isChatOpenSubject = new BehaviorSubject(false);\n    this.messageReceivedSubject = new Subject();\n    this.isConnecting = false;\n    this.connectionPromise = null;\n    this.isChatOpen$ = this.isChatOpenSubject.asObservable();\n    this.messageReceived$ = this.messageReceivedSubject.asObservable();\n    this.isChatOpen = false;\n    this.sessionId = this.getOrCreateSessionId();\n    // Start the SignalR connection\n    this.startSignalRConnection();\n    // Listen for route changes to maintain connection during navigation\n    this.router.events.subscribe(event => {\n      if (event instanceof NavigationStart) {\n        // Ensure connection is maintained during navigation\n        this.ensureConnection();\n      }\n    });\n  }\n  getOrCreateSessionId() {\n    let sessionId = crypto.randomUUID();\n    return sessionId;\n  }\n  getSessionId() {\n    return this.sessionId;\n  }\n  /**\n   * Ensures that the SignalR connection is active\n   * @returns Promise that resolves when the connection is established\n   */\n  ensureConnection() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      // If we're already connected, return immediately\n      if (_this.hubConnection?.state === signalR.HubConnectionState.Connected) {\n        return Promise.resolve();\n      }\n      // If we're in the process of connecting, return the existing promise\n      if (_this.isConnecting && _this.connectionPromise) {\n        return _this.connectionPromise;\n      }\n      // Otherwise, start a new connection\n      return _this.startSignalRConnection();\n    })();\n  }\n  /**\n   * Starts or restarts the SignalR connection\n   * @returns Promise that resolves when the connection is established\n   */\n  startSignalRConnection() {\n    // Set connecting flag to prevent multiple simultaneous connection attempts\n    this.isConnecting = true;\n    // Create the connection if it doesn't exist\n    if (!this.hubConnection) {\n      let url = getRemoteServiceBaseUrl() + '/chatHub';\n      this.hubConnection = new signalR.HubConnectionBuilder().withUrl(url).withAutomaticReconnect([0, 2000, 5000, 10000, 15000, 30000 // Retry quickly at first, then with increasing delays\n      ]).build();\n      // Set up event handlers\n      this.setupSignalREventHandlers();\n    }\n    // Start the connection and store the promise\n    this.connectionPromise = this.hubConnection.start().then(() => {\n      console.log('SignalR Connected!');\n      this.isConnecting = false;\n    }).catch(err => {\n      console.error('Error while establishing connection: ', err);\n      this.isConnecting = false;\n      throw err; // Rethrow to allow caller to handle\n    });\n    return this.connectionPromise;\n  }\n  /**\n   * Sets up the SignalR event handlers\n   */\n  setupSignalREventHandlers() {\n    if (!this.hubConnection) return;\n    // Message handlers\n    this.hubConnection.on('ReceiveMessage', message => {\n      this.messageReceivedSubject.next({\n        message,\n        isError: false,\n        isComplete: false\n      });\n    });\n    this.hubConnection.on('ReceiveError', message => {\n      this.messageReceivedSubject.next({\n        message,\n        isError: true,\n        isComplete: true\n      });\n    });\n    this.hubConnection.on('ReceiveComplete', message => {\n      this.messageReceivedSubject.next({\n        message,\n        isError: false,\n        isComplete: true\n      });\n    });\n    // Connection state handlers\n    this.hubConnection.onreconnecting(error => {\n      console.log('SignalR reconnecting:', error);\n    });\n    this.hubConnection.onreconnected(connectionId => {\n      console.log('SignalR reconnected with ID:', connectionId);\n    });\n    this.hubConnection.onclose(error => {\n      console.log('SignalR connection closed:', error);\n      // Attempt to reconnect if closed unexpectedly\n      if (error) {\n        setTimeout(() => this.ensureConnection(), 5000);\n      }\n    });\n  }\n  toggleChat() {\n    this.isChatOpen = !this.isChatOpen;\n    this.isChatOpenSubject.next(this.isChatOpen);\n    return this.isChatOpen;\n  }\n  getInitialState() {\n    return this.isChatOpen;\n  }\n  closeChat() {\n    this.isChatOpenSubject.next(false);\n  }\n  /**\n   * Disconnects the SignalR connection\n   * @returns Promise that resolves when the connection is stopped\n   */\n  disconnect() {\n    if (this.hubConnection) {\n      return this.hubConnection.stop().then(() => {\n        console.log('SignalR connection stopped');\n        this.isConnecting = false;\n        this.connectionPromise = null;\n      }).catch(err => {\n        console.error('Error stopping SignalR connection:', err);\n        // Reset state even if there's an error\n        this.isConnecting = false;\n        this.connectionPromise = null;\n        throw err;\n      });\n    }\n    return Promise.resolve();\n  }\n  sendMessage(message, isError = false) {\n    this.messageReceivedSubject.next({\n      message,\n      isError,\n      isComplete: false\n    });\n  }\n  completeMessage(message, isError = false) {\n    this.messageReceivedSubject.next({\n      message,\n      isError,\n      isComplete: true\n    });\n  }\n  setIsChatOpen(isOpen) {\n    this.isChatOpenSubject.next(isOpen);\n    localStorage.setItem('chatOpen', String(isOpen));\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: Router\n    }];\n  }\n};\nChatService = __decorate([Injectable({\n  providedIn: 'root'\n})], ChatService);\nexport { ChatService };", "map": {"version": 3, "names": ["Injectable", "BehaviorSubject", "Subject", "signalR", "getRemoteServiceBaseUrl", "Router", "NavigationStart", "ChatService", "constructor", "router", "isChatOpenSubject", "messageReceivedSubject", "isConnecting", "connectionPromise", "isChatOpen$", "asObservable", "messageReceived$", "isChatOpen", "sessionId", "getOrCreateSessionId", "startSignalRConnection", "events", "subscribe", "event", "ensureConnection", "crypto", "randomUUID", "getSessionId", "_this", "_asyncToGenerator", "hubConnection", "state", "HubConnectionState", "Connected", "Promise", "resolve", "url", "HubConnectionBuilder", "withUrl", "withAutomaticReconnect", "build", "setupSignalREventHandlers", "start", "then", "console", "log", "catch", "err", "error", "on", "message", "next", "isError", "isComplete", "onreconnecting", "onreconnected", "connectionId", "onclose", "setTimeout", "toggleChat", "getInitialState", "closeChat", "disconnect", "stop", "sendMessage", "completeMessage", "setIsChatOpen", "isOpen", "localStorage", "setItem", "String", "__decorate", "providedIn"], "sources": ["C:\\Users\\<USER>\\source\\ai-hub\\WebApp\\src\\app\\services\\chat.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { BehaviorSubject, Subject, Observable, from } from 'rxjs';\nimport { catchError, tap } from 'rxjs/operators';\nimport * as signalR from '@microsoft/signalr';\nimport { ResponseMessage } from '../../shared/service-proxies/service-proxies';\nimport { getRemoteServiceBaseUrl } from '../app.config';\nimport { Router, NavigationStart } from '@angular/router';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class ChatService {\n  private isChatOpen: boolean;\n  private isChatOpenSubject = new BehaviorSubject<boolean>(false);\n  private hubConnection!: signalR.HubConnection;\n  private messageReceivedSubject = new Subject<{ message: ResponseMessage, isError: boolean, isComplete?: boolean }>();\n  private sessionId: string;\n  private isConnecting: boolean = false;\n  private connectionPromise: Promise<void> | null = null;\n\n  isChatOpen$ = this.isChatOpenSubject.asObservable();\n  messageReceived$ = this.messageReceivedSubject.asObservable();\n\n  constructor(private router: Router) {\n    this.isChatOpen = false;\n    this.sessionId = this.getOrCreateSessionId();\n\n    // Start the SignalR connection\n    this.startSignalRConnection();\n\n    // Listen for route changes to maintain connection during navigation\n    this.router.events.subscribe(event => {\n      if (event instanceof NavigationStart) {\n        // Ensure connection is maintained during navigation\n        this.ensureConnection();\n      }\n    });\n  }\n\n  private getOrCreateSessionId(): string {\n    let sessionId = crypto.randomUUID();\n    return sessionId;\n  }\n\n  getSessionId(): string {\n    return this.sessionId;\n  }\n\n  /**\n   * Ensures that the SignalR connection is active\n   * @returns Promise that resolves when the connection is established\n   */\n  public async ensureConnection(): Promise<void> {\n    // If we're already connected, return immediately\n    if (this.hubConnection?.state === signalR.HubConnectionState.Connected) {\n      return Promise.resolve();\n    }\n\n    // If we're in the process of connecting, return the existing promise\n    if (this.isConnecting && this.connectionPromise) {\n      return this.connectionPromise;\n    }\n\n    // Otherwise, start a new connection\n    return this.startSignalRConnection();\n  }\n\n  /**\n   * Starts or restarts the SignalR connection\n   * @returns Promise that resolves when the connection is established\n   */\n  private startSignalRConnection(): Promise<void> {\n    // Set connecting flag to prevent multiple simultaneous connection attempts\n    this.isConnecting = true;\n\n    // Create the connection if it doesn't exist\n    if (!this.hubConnection) {\n      let url = getRemoteServiceBaseUrl() + '/chatHub';\n      this.hubConnection = new signalR.HubConnectionBuilder()\n        .withUrl(url)\n        .withAutomaticReconnect([\n          0, 2000, 5000, 10000, 15000, 30000 // Retry quickly at first, then with increasing delays\n        ])\n        .build();\n\n      // Set up event handlers\n      this.setupSignalREventHandlers();\n    }\n\n    // Start the connection and store the promise\n    this.connectionPromise = this.hubConnection.start()\n      .then(() => {\n        console.log('SignalR Connected!');\n        this.isConnecting = false;\n      })\n      .catch(err => {\n        console.error('Error while establishing connection: ', err);\n        this.isConnecting = false;\n        throw err; // Rethrow to allow caller to handle\n      });\n\n    return this.connectionPromise;\n  }\n\n  /**\n   * Sets up the SignalR event handlers\n   */\n  private setupSignalREventHandlers(): void {\n    if (!this.hubConnection) return;\n\n    // Message handlers\n    this.hubConnection.on('ReceiveMessage', (message: ResponseMessage) => {\n      this.messageReceivedSubject.next({ message, isError: false, isComplete: false });\n    });\n\n    this.hubConnection.on('ReceiveError', (message: ResponseMessage) => {\n      this.messageReceivedSubject.next({ message, isError: true, isComplete: true });\n    });\n\n    this.hubConnection.on('ReceiveComplete', (message: ResponseMessage) => {\n      this.messageReceivedSubject.next({ message, isError: false, isComplete: true });\n    });\n\n    // Connection state handlers\n    this.hubConnection.onreconnecting(error => {\n      console.log('SignalR reconnecting:', error);\n    });\n\n    this.hubConnection.onreconnected(connectionId => {\n      console.log('SignalR reconnected with ID:', connectionId);\n    });\n\n    this.hubConnection.onclose(error => {\n      console.log('SignalR connection closed:', error);\n      // Attempt to reconnect if closed unexpectedly\n      if (error) {\n        setTimeout(() => this.ensureConnection(), 5000);\n      }\n    });\n  }\n\n  toggleChat(): boolean {\n    this.isChatOpen = !this.isChatOpen;\n    this.isChatOpenSubject.next(this.isChatOpen);\n    return this.isChatOpen;\n  }\n\n  getInitialState(): boolean {\n    return this.isChatOpen;\n  }\n\n  closeChat() {\n    this.isChatOpenSubject.next(false);\n  }\n\n  /**\n   * Disconnects the SignalR connection\n   * @returns Promise that resolves when the connection is stopped\n   */\n  disconnect(): Promise<void> {\n    if (this.hubConnection) {\n      return this.hubConnection.stop()\n        .then(() => {\n          console.log('SignalR connection stopped');\n          this.isConnecting = false;\n          this.connectionPromise = null;\n        })\n        .catch(err => {\n          console.error('Error stopping SignalR connection:', err);\n          // Reset state even if there's an error\n          this.isConnecting = false;\n          this.connectionPromise = null;\n          throw err;\n        });\n    }\n    return Promise.resolve();\n  }\n\n  sendMessage(message: ResponseMessage, isError: boolean = false) {\n    this.messageReceivedSubject.next({ message, isError, isComplete: false });\n  }\n\n  completeMessage(message: ResponseMessage, isError: boolean = false) {\n    this.messageReceivedSubject.next({ message, isError, isComplete: true });\n  }\n\n  setIsChatOpen(isOpen: boolean): void {\n    this.isChatOpenSubject.next(isOpen);\n    localStorage.setItem('chatOpen', String(isOpen));\n  }\n}\n"], "mappings": ";;AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,eAAe,EAAEC,OAAO,QAA0B,MAAM;AAEjE,OAAO,KAAKC,OAAO,MAAM,oBAAoB;AAE7C,SAASC,uBAAuB,QAAQ,eAAe;AACvD,SAASC,MAAM,EAAEC,eAAe,QAAQ,iBAAiB;AAKlD,IAAMC,WAAW,GAAjB,MAAMA,WAAW;EAYtBC,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;IAVlB,KAAAC,iBAAiB,GAAG,IAAIT,eAAe,CAAU,KAAK,CAAC;IAEvD,KAAAU,sBAAsB,GAAG,IAAIT,OAAO,EAAwE;IAE5G,KAAAU,YAAY,GAAY,KAAK;IAC7B,KAAAC,iBAAiB,GAAyB,IAAI;IAEtD,KAAAC,WAAW,GAAG,IAAI,CAACJ,iBAAiB,CAACK,YAAY,EAAE;IACnD,KAAAC,gBAAgB,GAAG,IAAI,CAACL,sBAAsB,CAACI,YAAY,EAAE;IAG3D,IAAI,CAACE,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,SAAS,GAAG,IAAI,CAACC,oBAAoB,EAAE;IAE5C;IACA,IAAI,CAACC,sBAAsB,EAAE;IAE7B;IACA,IAAI,CAACX,MAAM,CAACY,MAAM,CAACC,SAAS,CAACC,KAAK,IAAG;MACnC,IAAIA,KAAK,YAAYjB,eAAe,EAAE;QACpC;QACA,IAAI,CAACkB,gBAAgB,EAAE;;IAE3B,CAAC,CAAC;EACJ;EAEQL,oBAAoBA,CAAA;IAC1B,IAAID,SAAS,GAAGO,MAAM,CAACC,UAAU,EAAE;IACnC,OAAOR,SAAS;EAClB;EAEAS,YAAYA,CAAA;IACV,OAAO,IAAI,CAACT,SAAS;EACvB;EAEA;;;;EAIaM,gBAAgBA,CAAA;IAAA,IAAAI,KAAA;IAAA,OAAAC,iBAAA;MAC3B;MACA,IAAID,KAAI,CAACE,aAAa,EAAEC,KAAK,KAAK5B,OAAO,CAAC6B,kBAAkB,CAACC,SAAS,EAAE;QACtE,OAAOC,OAAO,CAACC,OAAO,EAAE;;MAG1B;MACA,IAAIP,KAAI,CAAChB,YAAY,IAAIgB,KAAI,CAACf,iBAAiB,EAAE;QAC/C,OAAOe,KAAI,CAACf,iBAAiB;;MAG/B;MACA,OAAOe,KAAI,CAACR,sBAAsB,EAAE;IAAC;EACvC;EAEA;;;;EAIQA,sBAAsBA,CAAA;IAC5B;IACA,IAAI,CAACR,YAAY,GAAG,IAAI;IAExB;IACA,IAAI,CAAC,IAAI,CAACkB,aAAa,EAAE;MACvB,IAAIM,GAAG,GAAGhC,uBAAuB,EAAE,GAAG,UAAU;MAChD,IAAI,CAAC0B,aAAa,GAAG,IAAI3B,OAAO,CAACkC,oBAAoB,EAAE,CACpDC,OAAO,CAACF,GAAG,CAAC,CACZG,sBAAsB,CAAC,CACtB,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;MAAA,CACpC,CAAC,CACDC,KAAK,EAAE;MAEV;MACA,IAAI,CAACC,yBAAyB,EAAE;;IAGlC;IACA,IAAI,CAAC5B,iBAAiB,GAAG,IAAI,CAACiB,aAAa,CAACY,KAAK,EAAE,CAChDC,IAAI,CAAC,MAAK;MACTC,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;MACjC,IAAI,CAACjC,YAAY,GAAG,KAAK;IAC3B,CAAC,CAAC,CACDkC,KAAK,CAACC,GAAG,IAAG;MACXH,OAAO,CAACI,KAAK,CAAC,uCAAuC,EAAED,GAAG,CAAC;MAC3D,IAAI,CAACnC,YAAY,GAAG,KAAK;MACzB,MAAMmC,GAAG,CAAC,CAAC;IACb,CAAC,CAAC;IAEJ,OAAO,IAAI,CAAClC,iBAAiB;EAC/B;EAEA;;;EAGQ4B,yBAAyBA,CAAA;IAC/B,IAAI,CAAC,IAAI,CAACX,aAAa,EAAE;IAEzB;IACA,IAAI,CAACA,aAAa,CAACmB,EAAE,CAAC,gBAAgB,EAAGC,OAAwB,IAAI;MACnE,IAAI,CAACvC,sBAAsB,CAACwC,IAAI,CAAC;QAAED,OAAO;QAAEE,OAAO,EAAE,KAAK;QAAEC,UAAU,EAAE;MAAK,CAAE,CAAC;IAClF,CAAC,CAAC;IAEF,IAAI,CAACvB,aAAa,CAACmB,EAAE,CAAC,cAAc,EAAGC,OAAwB,IAAI;MACjE,IAAI,CAACvC,sBAAsB,CAACwC,IAAI,CAAC;QAAED,OAAO;QAAEE,OAAO,EAAE,IAAI;QAAEC,UAAU,EAAE;MAAI,CAAE,CAAC;IAChF,CAAC,CAAC;IAEF,IAAI,CAACvB,aAAa,CAACmB,EAAE,CAAC,iBAAiB,EAAGC,OAAwB,IAAI;MACpE,IAAI,CAACvC,sBAAsB,CAACwC,IAAI,CAAC;QAAED,OAAO;QAAEE,OAAO,EAAE,KAAK;QAAEC,UAAU,EAAE;MAAI,CAAE,CAAC;IACjF,CAAC,CAAC;IAEF;IACA,IAAI,CAACvB,aAAa,CAACwB,cAAc,CAACN,KAAK,IAAG;MACxCJ,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEG,KAAK,CAAC;IAC7C,CAAC,CAAC;IAEF,IAAI,CAAClB,aAAa,CAACyB,aAAa,CAACC,YAAY,IAAG;MAC9CZ,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEW,YAAY,CAAC;IAC3D,CAAC,CAAC;IAEF,IAAI,CAAC1B,aAAa,CAAC2B,OAAO,CAACT,KAAK,IAAG;MACjCJ,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEG,KAAK,CAAC;MAChD;MACA,IAAIA,KAAK,EAAE;QACTU,UAAU,CAAC,MAAM,IAAI,CAAClC,gBAAgB,EAAE,EAAE,IAAI,CAAC;;IAEnD,CAAC,CAAC;EACJ;EAEAmC,UAAUA,CAAA;IACR,IAAI,CAAC1C,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;IAClC,IAAI,CAACP,iBAAiB,CAACyC,IAAI,CAAC,IAAI,CAAClC,UAAU,CAAC;IAC5C,OAAO,IAAI,CAACA,UAAU;EACxB;EAEA2C,eAAeA,CAAA;IACb,OAAO,IAAI,CAAC3C,UAAU;EACxB;EAEA4C,SAASA,CAAA;IACP,IAAI,CAACnD,iBAAiB,CAACyC,IAAI,CAAC,KAAK,CAAC;EACpC;EAEA;;;;EAIAW,UAAUA,CAAA;IACR,IAAI,IAAI,CAAChC,aAAa,EAAE;MACtB,OAAO,IAAI,CAACA,aAAa,CAACiC,IAAI,EAAE,CAC7BpB,IAAI,CAAC,MAAK;QACTC,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;QACzC,IAAI,CAACjC,YAAY,GAAG,KAAK;QACzB,IAAI,CAACC,iBAAiB,GAAG,IAAI;MAC/B,CAAC,CAAC,CACDiC,KAAK,CAACC,GAAG,IAAG;QACXH,OAAO,CAACI,KAAK,CAAC,oCAAoC,EAAED,GAAG,CAAC;QACxD;QACA,IAAI,CAACnC,YAAY,GAAG,KAAK;QACzB,IAAI,CAACC,iBAAiB,GAAG,IAAI;QAC7B,MAAMkC,GAAG;MACX,CAAC,CAAC;;IAEN,OAAOb,OAAO,CAACC,OAAO,EAAE;EAC1B;EAEA6B,WAAWA,CAACd,OAAwB,EAAEE,OAAA,GAAmB,KAAK;IAC5D,IAAI,CAACzC,sBAAsB,CAACwC,IAAI,CAAC;MAAED,OAAO;MAAEE,OAAO;MAAEC,UAAU,EAAE;IAAK,CAAE,CAAC;EAC3E;EAEAY,eAAeA,CAACf,OAAwB,EAAEE,OAAA,GAAmB,KAAK;IAChE,IAAI,CAACzC,sBAAsB,CAACwC,IAAI,CAAC;MAAED,OAAO;MAAEE,OAAO;MAAEC,UAAU,EAAE;IAAI,CAAE,CAAC;EAC1E;EAEAa,aAAaA,CAACC,MAAe;IAC3B,IAAI,CAACzD,iBAAiB,CAACyC,IAAI,CAACgB,MAAM,CAAC;IACnCC,YAAY,CAACC,OAAO,CAAC,UAAU,EAAEC,MAAM,CAACH,MAAM,CAAC,CAAC;EAClD;;;;;;;AAlLW5D,WAAW,GAAAgE,UAAA,EAHvBvE,UAAU,CAAC;EACVwE,UAAU,EAAE;CACb,CAAC,C,EACWjE,WAAW,CAmLvB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}