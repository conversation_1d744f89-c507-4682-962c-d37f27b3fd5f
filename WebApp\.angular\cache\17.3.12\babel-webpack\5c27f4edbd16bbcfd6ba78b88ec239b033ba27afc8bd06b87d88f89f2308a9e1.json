{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, inject, TemplateRef, Directive, ElementRef, computed, signal, untracked, NgZone, numberAttribute, input, output, ViewContainerRef, effect, Injector, Renderer2, contentChildren, contentChild, booleanAttribute, isDevMode, Component, ChangeDetectionStrategy, HostBinding, NgModule } from '@angular/core';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { merge, fromEvent, filter, Observable, switchMap, take, map, takeUntil, tap, timeInterval, scan, mergeMap, of, delay, repeat, Subject, startWith, pairwise, skipWhile } from 'rxjs';\nimport { DOCUMENT, NgStyle, NgTemplateOutlet } from '@angular/common';\nconst _c0 = [\"*\"];\nconst _c1 = (a0, a1, a2, a3, a4, a5) => ({\n  areaBefore: a0,\n  areaAfter: a1,\n  gutterNum: a2,\n  first: a3,\n  last: a4,\n  isDragged: a5\n});\nfunction SplitComponent_For_2_Conditional_0_Conditional_2_ng_container_0_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction SplitComponent_For_2_Conditional_0_Conditional_2_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, SplitComponent_For_2_Conditional_0_Conditional_2_ng_container_0_ng_container_1_Template, 1, 0, \"ng-container\", 4);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const injector_r5 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(3);\n    const area_r7 = ctx_r5.$implicit;\n    const $index_r2 = ctx_r5.$index;\n    const ɵ$index_2_r8 = ctx_r5.$index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.customGutter().template)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction6(3, _c1, area_r7, ctx_r2._areas()[$index_r2 + 1], $index_r2 + 1, ɵ$index_2_r8 === 0, $index_r2 === ctx_r2._areas().length - 2, ctx_r2.draggedGutterIndex() === $index_r2))(\"ngTemplateOutletInjector\", injector_r5);\n  }\n}\nfunction SplitComponent_For_2_Conditional_0_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, SplitComponent_For_2_Conditional_0_Conditional_2_ng_container_0_Template, 2, 10, \"ng-container\", 3);\n  }\n  if (rf & 2) {\n    const $index_r2 = i0.ɵɵnextContext(2).$index;\n    i0.ɵɵproperty(\"asSplitGutterDynamicInjector\", $index_r2 + 1);\n  }\n}\nfunction SplitComponent_For_2_Conditional_0_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 5);\n  }\n}\nfunction SplitComponent_For_2_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2, 0);\n    i0.ɵɵlistener(\"asSplitCustomClick\", function SplitComponent_For_2_Conditional_0_Template_div_asSplitCustomClick_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const $index_r2 = i0.ɵɵnextContext().$index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.gutterClicked($index_r2));\n    })(\"asSplitCustomDblClick\", function SplitComponent_For_2_Conditional_0_Template_div_asSplitCustomDblClick_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const $index_r2 = i0.ɵɵnextContext().$index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.gutterDoubleClicked($index_r2));\n    })(\"asSplitCustomMouseDown\", function SplitComponent_For_2_Conditional_0_Template_div_asSplitCustomMouseDown_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const gutter_r4 = i0.ɵɵreference(1);\n      const $index_r2 = i0.ɵɵnextContext().$index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.gutterMouseDown($event, gutter_r4, $index_r2, $index_r2, $index_r2 + 1));\n    })(\"asSplitCustomKeyDown\", function SplitComponent_For_2_Conditional_0_Template_div_asSplitCustomKeyDown_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const $index_r2 = i0.ɵɵnextContext().$index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.gutterKeyDown($event, $index_r2, $index_r2, $index_r2 + 1));\n    });\n    i0.ɵɵtemplate(2, SplitComponent_For_2_Conditional_0_Conditional_2_Template, 1, 1, \"ng-container\")(3, SplitComponent_For_2_Conditional_0_Conditional_3_Template, 1, 0);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_22_0;\n    const ctx_r5 = i0.ɵɵnextContext();\n    const area_r7 = ctx_r5.$implicit;\n    const $index_r2 = ctx_r5.$index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"as-dragged\", ctx_r2.draggedGutterIndex() === $index_r2);\n    i0.ɵɵproperty(\"ngStyle\", ctx_r2.getGutterGridStyle($index_r2 + 1))(\"asSplitCustomMultiClickThreshold\", ctx_r2.gutterDblClickDuration())(\"asSplitCustomClickDeltaInPx\", ctx_r2.gutterClickDeltaPx());\n    i0.ɵɵattribute(\"aria-label\", ctx_r2.gutterAriaLabel())(\"aria-orientation\", ctx_r2.direction())(\"aria-valuemin\", ctx_r2.getAriaValue(area_r7.minSize()))(\"aria-valuemax\", ctx_r2.getAriaValue(area_r7.maxSize()))(\"aria-valuenow\", ctx_r2.getAriaValue(area_r7._internalSize()))(\"aria-valuetext\", ctx_r2.getAriaAreaSizeText(area_r7));\n    i0.ɵɵadvance(2);\n    i0.ɵɵconditional(2, ((tmp_22_0 = ctx_r2.customGutter()) == null ? null : tmp_22_0.template) ? 2 : 3);\n  }\n}\nfunction SplitComponent_For_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, SplitComponent_For_2_Conditional_0_Template, 4, 12, \"div\", 1);\n  }\n  if (rf & 2) {\n    const ɵ$index_2_r8 = ctx.$index;\n    const ɵ$count_2_r9 = ctx.$count;\n    i0.ɵɵconditional(0, !(ɵ$index_2_r8 === ɵ$count_2_r9 - 1) ? 0 : -1);\n  }\n}\nfunction SplitAreaComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 0);\n  }\n}\nconst defaultOptions = {\n  dir: 'ltr',\n  direction: 'horizontal',\n  disabled: false,\n  gutterDblClickDuration: 0,\n  gutterSize: 11,\n  gutterStep: 1,\n  gutterClickDeltaPx: 2,\n  restrictMove: false,\n  unit: 'percent',\n  useTransition: false\n};\nconst ANGULAR_SPLIT_DEFAULT_OPTIONS = new InjectionToken('angular-split-global-config', {\n  providedIn: 'root',\n  factory: () => defaultOptions\n});\n/**\n * Provides default options for angular split. The options object has hierarchical inheritance\n * which means only the declared properties will be overridden\n */\nfunction provideAngularSplitOptions(options) {\n  return {\n    provide: ANGULAR_SPLIT_DEFAULT_OPTIONS,\n    useFactory: () => ({\n      ...inject(ANGULAR_SPLIT_DEFAULT_OPTIONS, {\n        skipSelf: true\n      }),\n      ...options\n    })\n  };\n}\nclass SplitGutterDirective {\n  constructor() {\n    this.template = inject(TemplateRef);\n    /**\n     * The map holds reference to the drag handle elements inside instances\n     * of the provided template.\n     *\n     * @internal\n     */\n    this._gutterToHandleElementMap = new Map();\n    /**\n     * The map holds reference to the excluded drag elements inside instances\n     * of the provided template.\n     *\n     * @internal\n     */\n    this._gutterToExcludeDragElementMap = new Map();\n  }\n  /**\n   * @internal\n   */\n  _canStartDragging(originElement, gutterNum) {\n    if (this._gutterToExcludeDragElementMap.has(gutterNum)) {\n      const isInsideExclude = this._gutterToExcludeDragElementMap.get(gutterNum).some(gutterExcludeElement => gutterExcludeElement.nativeElement.contains(originElement));\n      if (isInsideExclude) {\n        return false;\n      }\n    }\n    if (this._gutterToHandleElementMap.has(gutterNum)) {\n      return this._gutterToHandleElementMap.get(gutterNum).some(gutterHandleElement => gutterHandleElement.nativeElement.contains(originElement));\n    }\n    return true;\n  }\n  /**\n   * @internal\n   */\n  _addToMap(map, gutterNum, elementRef) {\n    if (map.has(gutterNum)) {\n      map.get(gutterNum).push(elementRef);\n    } else {\n      map.set(gutterNum, [elementRef]);\n    }\n  }\n  /**\n   * @internal\n   */\n  _removedFromMap(map, gutterNum, elementRef) {\n    const elements = map.get(gutterNum);\n    elements.splice(elements.indexOf(elementRef), 1);\n    if (elements.length === 0) {\n      map.delete(gutterNum);\n    }\n  }\n  static ngTemplateContextGuard(_dir, ctx) {\n    return true;\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function SplitGutterDirective_Factory(t) {\n      return new (t || SplitGutterDirective)();\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: SplitGutterDirective,\n      selectors: [[\"\", \"asSplitGutter\", \"\"]],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SplitGutterDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[asSplitGutter]',\n      standalone: true\n    }]\n  }], null, null);\n})();\n\n/**\n * Identifies the gutter by number through DI\n * to allow SplitGutterDragHandleDirective and SplitGutterExcludeFromDragDirective to know\n * the gutter template context without inputs\n */\nconst GUTTER_NUM_TOKEN = new InjectionToken('Gutter num');\nclass SplitGutterDragHandleDirective {\n  constructor() {\n    this.gutterNum = inject(GUTTER_NUM_TOKEN);\n    this.elementRef = inject(ElementRef);\n    this.gutterDir = inject(SplitGutterDirective);\n    this.gutterDir._addToMap(this.gutterDir._gutterToHandleElementMap, this.gutterNum, this.elementRef);\n  }\n  ngOnDestroy() {\n    this.gutterDir._removedFromMap(this.gutterDir._gutterToHandleElementMap, this.gutterNum, this.elementRef);\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function SplitGutterDragHandleDirective_Factory(t) {\n      return new (t || SplitGutterDragHandleDirective)();\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: SplitGutterDragHandleDirective,\n      selectors: [[\"\", \"asSplitGutterDragHandle\", \"\"]],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SplitGutterDragHandleDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[asSplitGutterDragHandle]',\n      standalone: true\n    }]\n  }], () => [], null);\n})();\nclass SplitGutterExcludeFromDragDirective {\n  constructor() {\n    this.gutterNum = inject(GUTTER_NUM_TOKEN);\n    this.elementRef = inject(ElementRef);\n    this.gutterDir = inject(SplitGutterDirective);\n    this.gutterDir._addToMap(this.gutterDir._gutterToExcludeDragElementMap, this.gutterNum, this.elementRef);\n  }\n  ngOnDestroy() {\n    this.gutterDir._removedFromMap(this.gutterDir._gutterToExcludeDragElementMap, this.gutterNum, this.elementRef);\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function SplitGutterExcludeFromDragDirective_Factory(t) {\n      return new (t || SplitGutterExcludeFromDragDirective)();\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: SplitGutterExcludeFromDragDirective,\n      selectors: [[\"\", \"asSplitGutterExcludeFromDrag\", \"\"]],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SplitGutterExcludeFromDragDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[asSplitGutterExcludeFromDrag]',\n      standalone: true\n    }]\n  }], () => [], null);\n})();\n\n/**\n * Only supporting a single {@link TouchEvent} point\n */\nfunction getPointFromEvent(event) {\n  // NOTE: In firefox TouchEvent is only defined for touch capable devices\n  const isTouchEvent = e => window.TouchEvent && event instanceof TouchEvent;\n  if (isTouchEvent(event)) {\n    if (event.changedTouches.length === 0) {\n      return undefined;\n    }\n    const {\n      clientX,\n      clientY\n    } = event.changedTouches[0];\n    return {\n      x: clientX,\n      y: clientY\n    };\n  }\n  if (event instanceof KeyboardEvent) {\n    const target = event.target;\n    // Calculate element midpoint\n    return {\n      x: target.offsetLeft + target.offsetWidth / 2,\n      y: target.offsetTop + target.offsetHeight / 2\n    };\n  }\n  return {\n    x: event.clientX,\n    y: event.clientY\n  };\n}\nfunction gutterEventsEqualWithDelta(startEvent, endEvent, deltaInPx, gutterElement) {\n  if (!gutterElement.contains(startEvent.target) || !gutterElement.contains(endEvent.target)) {\n    return false;\n  }\n  const startPoint = getPointFromEvent(startEvent);\n  const endPoint = getPointFromEvent(endEvent);\n  return Math.abs(endPoint.x - startPoint.x) <= deltaInPx && Math.abs(endPoint.y - startPoint.y) <= deltaInPx;\n}\nfunction fromMouseDownEvent(target) {\n  return merge(fromEvent(target, 'mousedown').pipe(filter(e => e.button === 0)),\n  // We must prevent default here so we declare it as non passive explicitly\n  fromEvent(target, 'touchstart', {\n    passive: false\n  }));\n}\nfunction fromMouseMoveEvent(target) {\n  return merge(fromEvent(target, 'mousemove'), fromEvent(target, 'touchmove'));\n}\nfunction fromMouseUpEvent(target, includeTouchCancel = false) {\n  const withoutTouchCancel = merge(fromEvent(target, 'mouseup'), fromEvent(target, 'touchend'));\n  return includeTouchCancel ? merge(withoutTouchCancel, fromEvent(target, 'touchcancel')) : withoutTouchCancel;\n}\nfunction sum(array, fn) {\n  return array.reduce((sum, item) => sum + fn(item), 0);\n}\nfunction toRecord(array, fn) {\n  return array.reduce((record, item, index) => {\n    const [key, value] = fn(item, index);\n    record[key] = value;\n    return record;\n  }, {});\n}\nfunction createClassesString(classesRecord) {\n  return Object.entries(classesRecord).filter(([, value]) => value).map(([key]) => key).join(' ');\n}\n/**\n * Creates a semi signal which allows writes but is based on an existing signal\n * Whenever the original signal changes the mirror signal gets aligned\n * overriding the current value inside.\n */\nfunction mirrorSignal(outer) {\n  const inner = computed(() => signal(outer()));\n  const mirror = () => inner()();\n  mirror.set = value => untracked(inner).set(value);\n  mirror.reset = () => untracked(() => inner().set(outer()));\n  return mirror;\n}\nfunction leaveNgZone() {\n  return source => new Observable(observer => inject(NgZone).runOutsideAngular(() => source.subscribe(observer)));\n}\nconst numberAttributeWithFallback = fallback => value => numberAttribute(value, fallback);\nconst assertUnreachable = (value, name) => {\n  throw new Error(`as-split: unknown value \"${value}\" for \"${name}\"`);\n};\n\n/* eslint-disable @angular-eslint/no-output-native */\n/* eslint-disable @angular-eslint/no-output-rename */\n/* eslint-disable @angular-eslint/no-input-rename */\n/**\n * Emits mousedown, click, double click and keydown out of zone\n *\n * Emulates browser behavior of click and double click with new features:\n * 1. Supports touch events (tap and double tap)\n * 2. Ignores the first click in a double click with the side effect of a bit slower emission of the click event\n * 3. Allow customizing the delay after mouse down to count another mouse down as a double click\n */\nclass SplitCustomEventsBehaviorDirective {\n  constructor() {\n    this.elementRef = inject(ElementRef);\n    this.document = inject(DOCUMENT);\n    this.multiClickThreshold = input.required({\n      alias: 'asSplitCustomMultiClickThreshold'\n    });\n    this.deltaInPx = input.required({\n      alias: 'asSplitCustomClickDeltaInPx'\n    });\n    this.mouseDown = output({\n      alias: 'asSplitCustomMouseDown'\n    });\n    this.click = output({\n      alias: 'asSplitCustomClick'\n    });\n    this.dblClick = output({\n      alias: 'asSplitCustomDblClick'\n    });\n    this.keyDown = output({\n      alias: 'asSplitCustomKeyDown'\n    });\n    fromEvent(this.elementRef.nativeElement, 'keydown').pipe(leaveNgZone(), takeUntilDestroyed()).subscribe(e => this.keyDown.emit(e));\n    // We just need to know when drag start to cancel all click related interactions\n    const dragStarted$ = fromMouseDownEvent(this.elementRef.nativeElement).pipe(switchMap(mouseDownEvent => fromMouseMoveEvent(this.document).pipe(filter(e => !gutterEventsEqualWithDelta(mouseDownEvent, e, this.deltaInPx(), this.elementRef.nativeElement)), take(1), map(() => true), takeUntil(fromMouseUpEvent(this.document)))));\n    fromMouseDownEvent(this.elementRef.nativeElement).pipe(tap(e => this.mouseDown.emit(e)),\n    // Gather mousedown events intervals to identify whether it is a single double or more click\n    timeInterval(),\n    // We only count a click as part of a multi click if the multiClickThreshold wasn't reached\n    scan((sum, {\n      interval\n    }) => interval >= this.multiClickThreshold() ? 1 : sum + 1, 0),\n    // As mouseup always comes after mousedown if the delayed mouseup has yet to come\n    // but a new mousedown arrived we can discard the older mouseup as we are part of a multi click\n    switchMap(numOfConsecutiveClicks =>\n    // In case of a double click we directly emit as we don't care about more than two consecutive clicks\n    // so we don't have to wait compared to a single click that might be followed by another for a double.\n    // In case of a mouse up that was too long after the mouse down\n    // we don't have to wait as we know it won't be a multi click but a single click\n    fromMouseUpEvent(this.elementRef.nativeElement).pipe(timeInterval(), take(1), numOfConsecutiveClicks === 2 ? map(() => numOfConsecutiveClicks) : mergeMap(({\n      interval\n    }) => interval >= this.multiClickThreshold() ? of(numOfConsecutiveClicks) : of(numOfConsecutiveClicks).pipe(delay(this.multiClickThreshold() - interval))))),\n    // Discard everything once drag started and listen again (repeat) to mouse down\n    takeUntil(dragStarted$), repeat(), leaveNgZone(), takeUntilDestroyed()).subscribe(amount => {\n      if (amount === 1) {\n        this.click.emit();\n      } else if (amount === 2) {\n        this.dblClick.emit();\n      }\n    });\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function SplitCustomEventsBehaviorDirective_Factory(t) {\n      return new (t || SplitCustomEventsBehaviorDirective)();\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: SplitCustomEventsBehaviorDirective,\n      selectors: [[\"\", \"asSplitCustomEventsBehavior\", \"\"]],\n      inputs: {\n        multiClickThreshold: [i0.ɵɵInputFlags.SignalBased, \"asSplitCustomMultiClickThreshold\", \"multiClickThreshold\"],\n        deltaInPx: [i0.ɵɵInputFlags.SignalBased, \"asSplitCustomClickDeltaInPx\", \"deltaInPx\"]\n      },\n      outputs: {\n        mouseDown: \"asSplitCustomMouseDown\",\n        click: \"asSplitCustomClick\",\n        dblClick: \"asSplitCustomDblClick\",\n        keyDown: \"asSplitCustomKeyDown\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SplitCustomEventsBehaviorDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[asSplitCustomEventsBehavior]',\n      standalone: true\n    }]\n  }], () => [], null);\n})();\nfunction areAreasValid(areas, unit, logWarnings) {\n  if (areas.length === 0) {\n    return true;\n  }\n  const areaSizes = areas.map(area => {\n    const size = area.size();\n    return size === 'auto' ? '*' : size;\n  });\n  const wildcardAreas = areaSizes.filter(areaSize => areaSize === '*');\n  if (wildcardAreas.length > 1) {\n    if (logWarnings) {\n      console.warn('as-split: Maximum one * area is allowed');\n    }\n    return false;\n  }\n  if (unit === 'pixel') {\n    if (wildcardAreas.length === 1) {\n      return true;\n    }\n    if (logWarnings) {\n      console.warn('as-split: Pixel mode must have exactly one * area');\n    }\n    return false;\n  }\n  const sumPercent = sum(areaSizes, areaSize => areaSize === '*' ? 0 : areaSize);\n  // As percent calculation isn't perfect we allow for a small margin of error\n  if (wildcardAreas.length === 1) {\n    if (sumPercent <= 100.1) {\n      return true;\n    }\n    if (logWarnings) {\n      console.warn(`as-split: Percent areas must total 100%`);\n    }\n    return false;\n  }\n  if (sumPercent < 99.9 || sumPercent > 100.1) {\n    if (logWarnings) {\n      console.warn('as-split: Percent areas must total 100%');\n    }\n    return false;\n  }\n  return true;\n}\n\n/**\n * This directive allows creating a dynamic injector inside ngFor\n * with dynamic gutter num and expose the injector for ngTemplateOutlet usage\n */\nclass SplitGutterDynamicInjectorDirective {\n  constructor() {\n    this.vcr = inject(ViewContainerRef);\n    this.templateRef = inject(TemplateRef);\n    this.gutterNum = input.required({\n      alias: 'asSplitGutterDynamicInjector'\n    });\n    effect(() => {\n      this.vcr.clear();\n      const injector = Injector.create({\n        providers: [{\n          provide: GUTTER_NUM_TOKEN,\n          useValue: this.gutterNum()\n        }],\n        parent: this.vcr.injector\n      });\n      this.vcr.createEmbeddedView(this.templateRef, {\n        $implicit: injector\n      });\n    });\n  }\n  static ngTemplateContextGuard(_dir, ctx) {\n    return true;\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function SplitGutterDynamicInjectorDirective_Factory(t) {\n      return new (t || SplitGutterDynamicInjectorDirective)();\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: SplitGutterDynamicInjectorDirective,\n      selectors: [[\"\", \"asSplitGutterDynamicInjector\", \"\"]],\n      inputs: {\n        gutterNum: [i0.ɵɵInputFlags.SignalBased, \"asSplitGutterDynamicInjector\", \"gutterNum\"]\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SplitGutterDynamicInjectorDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[asSplitGutterDynamicInjector]',\n      standalone: true\n    }]\n  }], () => [], null);\n})();\nconst SPLIT_AREA_CONTRACT = new InjectionToken('Split Area Contract');\nclass SplitComponent {\n  get hostClassesBinding() {\n    return this.hostClasses();\n  }\n  get hostDirBinding() {\n    return this.dir();\n  }\n  constructor() {\n    this.document = inject(DOCUMENT);\n    this.renderer = inject(Renderer2);\n    this.elementRef = inject(ElementRef);\n    this.ngZone = inject(NgZone);\n    this.defaultOptions = inject(ANGULAR_SPLIT_DEFAULT_OPTIONS);\n    this.gutterMouseDownSubject = new Subject();\n    this.dragProgressSubject = new Subject();\n    /**\n     * @internal\n     */\n    this._areas = contentChildren(SPLIT_AREA_CONTRACT);\n    this.customGutter = contentChild(SplitGutterDirective);\n    this.gutterSize = input(this.defaultOptions.gutterSize, {\n      transform: numberAttributeWithFallback(this.defaultOptions.gutterSize)\n    });\n    this.gutterStep = input(this.defaultOptions.gutterStep, {\n      transform: numberAttributeWithFallback(this.defaultOptions.gutterStep)\n    });\n    this.disabled = input(this.defaultOptions.disabled, {\n      transform: booleanAttribute\n    });\n    this.gutterClickDeltaPx = input(this.defaultOptions.gutterClickDeltaPx, {\n      transform: numberAttributeWithFallback(this.defaultOptions.gutterClickDeltaPx)\n    });\n    this.direction = input(this.defaultOptions.direction);\n    this.dir = input(this.defaultOptions.dir);\n    this.unit = input(this.defaultOptions.unit);\n    this.gutterAriaLabel = input();\n    this.restrictMove = input(this.defaultOptions.restrictMove, {\n      transform: booleanAttribute\n    });\n    this.useTransition = input(this.defaultOptions.useTransition, {\n      transform: booleanAttribute\n    });\n    this.gutterDblClickDuration = input(this.defaultOptions.gutterDblClickDuration, {\n      transform: numberAttributeWithFallback(this.defaultOptions.gutterDblClickDuration)\n    });\n    this.gutterClick = output();\n    this.gutterDblClick = output();\n    this.dragStart = output();\n    this.dragEnd = output();\n    this.transitionEnd = output();\n    this.dragProgress$ = this.dragProgressSubject.asObservable();\n    /**\n     * @internal\n     */\n    this._visibleAreas = computed(() => this._areas().filter(area => area.visible()));\n    this.gridTemplateColumnsStyle = computed(() => this.createGridTemplateColumnsStyle());\n    this.hostClasses = computed(() => createClassesString({\n      [`as-${this.direction()}`]: true,\n      [`as-${this.unit()}`]: true,\n      ['as-disabled']: this.disabled(),\n      ['as-dragging']: this._isDragging(),\n      ['as-transition']: this.useTransition() && !this._isDragging()\n    }));\n    this.draggedGutterIndex = signal(undefined);\n    /**\n     * @internal\n     */\n    this._isDragging = computed(() => this.draggedGutterIndex() !== undefined);\n    /**\n     * @internal\n     * Should only be used by {@link SplitAreaComponent._internalSize}\n     */\n    this._alignedVisibleAreasSizes = computed(() => this.createAlignedVisibleAreasSize());\n    if (isDevMode()) {\n      // Logs warnings to console when the provided areas sizes are invalid\n      effect(() => {\n        // Special mode when no size input was declared which is a valid mode\n        if (this.unit() === 'percent' && this._visibleAreas().every(area => area.size() === 'auto')) {\n          return;\n        }\n        areAreasValid(this._visibleAreas(), this.unit(), true);\n      });\n    }\n    // Responsible for updating grid template style. Must be this way and not based on HostBinding\n    // as change detection for host binding is bound to the parent component and this style\n    // is updated on every mouse move. Doing it this way will prevent change detection cycles in parent.\n    effect(() => {\n      const gridTemplateColumnsStyle = this.gridTemplateColumnsStyle();\n      this.renderer.setStyle(this.elementRef.nativeElement, 'grid-template', gridTemplateColumnsStyle);\n    });\n    this.gutterMouseDownSubject.pipe(filter(context => !this.customGutter() || this.customGutter()._canStartDragging(context.mouseDownEvent.target, context.gutterIndex + 1)), switchMap(mouseDownContext =>\n    // As we have gutterClickDeltaPx we can't just start the drag but need to make sure\n    // we are out of the delta pixels. As the delta can be any number we make sure\n    // we always start the drag if we go out of the gutter (delta based on mouse position is larger than gutter).\n    // As moving can start inside the drag and end outside of it we always keep track of the previous event\n    // so once the current is out of the delta size we use the previous one as the drag start baseline.\n    fromMouseMoveEvent(this.document).pipe(startWith(mouseDownContext.mouseDownEvent), pairwise(), skipWhile(([, currMoveEvent]) => gutterEventsEqualWithDelta(mouseDownContext.mouseDownEvent, currMoveEvent, this.gutterClickDeltaPx(), mouseDownContext.gutterElement)), take(1), takeUntil(fromMouseUpEvent(this.document, true)), tap(() => {\n      this.ngZone.run(() => {\n        this.dragStart.emit(this.createDragInteractionEvent(mouseDownContext.gutterIndex));\n        this.draggedGutterIndex.set(mouseDownContext.gutterIndex);\n      });\n    }), map(([prevMouseEvent]) => this.createDragStartContext(prevMouseEvent, mouseDownContext.areaBeforeGutterIndex, mouseDownContext.areaAfterGutterIndex)), switchMap(dragStartContext => fromMouseMoveEvent(this.document).pipe(tap(moveEvent => this.mouseDragMove(moveEvent, dragStartContext)), takeUntil(fromMouseUpEvent(this.document, true)), tap({\n      complete: () => this.ngZone.run(() => {\n        this.dragEnd.emit(this.createDragInteractionEvent(this.draggedGutterIndex()));\n        this.draggedGutterIndex.set(undefined);\n      })\n    }))))), takeUntilDestroyed()).subscribe();\n    fromEvent(this.elementRef.nativeElement, 'transitionend').pipe(filter(e => e.propertyName.startsWith('grid-template')), leaveNgZone(), takeUntilDestroyed()).subscribe(() => this.ngZone.run(() => this.transitionEnd.emit(this.createAreaSizes())));\n  }\n  gutterClicked(gutterIndex) {\n    this.ngZone.run(() => this.gutterClick.emit(this.createDragInteractionEvent(gutterIndex)));\n  }\n  gutterDoubleClicked(gutterIndex) {\n    this.ngZone.run(() => this.gutterDblClick.emit(this.createDragInteractionEvent(gutterIndex)));\n  }\n  gutterMouseDown(e, gutterElement, gutterIndex, areaBeforeGutterIndex, areaAfterGutterIndex) {\n    if (this.disabled()) {\n      return;\n    }\n    e.preventDefault();\n    e.stopPropagation();\n    this.gutterMouseDownSubject.next({\n      mouseDownEvent: e,\n      gutterElement,\n      gutterIndex,\n      areaBeforeGutterIndex,\n      areaAfterGutterIndex\n    });\n  }\n  gutterKeyDown(e, gutterIndex, areaBeforeGutterIndex, areaAfterGutterIndex) {\n    if (this.disabled()) {\n      return;\n    }\n    const pixelsToMove = 50;\n    const pageMoveMultiplier = 10;\n    let xPointOffset = 0;\n    let yPointOffset = 0;\n    if (this.direction() === 'horizontal') {\n      // Even though we are going in the x axis we support page up and down\n      switch (e.key) {\n        case 'ArrowLeft':\n          xPointOffset -= pixelsToMove;\n          break;\n        case 'ArrowRight':\n          xPointOffset += pixelsToMove;\n          break;\n        case 'PageUp':\n          if (this.dir() === 'rtl') {\n            xPointOffset -= pixelsToMove * pageMoveMultiplier;\n          } else {\n            xPointOffset += pixelsToMove * pageMoveMultiplier;\n          }\n          break;\n        case 'PageDown':\n          if (this.dir() === 'rtl') {\n            xPointOffset += pixelsToMove * pageMoveMultiplier;\n          } else {\n            xPointOffset -= pixelsToMove * pageMoveMultiplier;\n          }\n          break;\n        default:\n          return;\n      }\n    } else {\n      switch (e.key) {\n        case 'ArrowUp':\n          yPointOffset -= pixelsToMove;\n          break;\n        case 'ArrowDown':\n          yPointOffset += pixelsToMove;\n          break;\n        case 'PageUp':\n          yPointOffset -= pixelsToMove * pageMoveMultiplier;\n          break;\n        case 'PageDown':\n          yPointOffset += pixelsToMove * pageMoveMultiplier;\n          break;\n        default:\n          return;\n      }\n    }\n    e.preventDefault();\n    e.stopPropagation();\n    const gutterMidPoint = getPointFromEvent(e);\n    const dragStartContext = this.createDragStartContext(e, areaBeforeGutterIndex, areaAfterGutterIndex);\n    this.ngZone.run(() => {\n      this.dragStart.emit(this.createDragInteractionEvent(gutterIndex));\n      this.draggedGutterIndex.set(gutterIndex);\n    });\n    this.dragMoveToPoint({\n      x: gutterMidPoint.x + xPointOffset,\n      y: gutterMidPoint.y + yPointOffset\n    }, dragStartContext);\n    this.ngZone.run(() => {\n      this.dragEnd.emit(this.createDragInteractionEvent(gutterIndex));\n      this.draggedGutterIndex.set(undefined);\n    });\n  }\n  getGutterGridStyle(nextAreaIndex) {\n    const gutterNum = nextAreaIndex * 2;\n    const style = `${gutterNum} / ${gutterNum}`;\n    return {\n      ['grid-column']: this.direction() === 'horizontal' ? style : '1',\n      ['grid-row']: this.direction() === 'vertical' ? style : '1'\n    };\n  }\n  getAriaAreaSizeText(area) {\n    const size = area._internalSize();\n    if (size === '*') {\n      return undefined;\n    }\n    return `${size.toFixed(0)} ${this.unit()}`;\n  }\n  getAriaValue(size) {\n    return size === '*' ? undefined : size;\n  }\n  createDragInteractionEvent(gutterIndex) {\n    return {\n      gutterNum: gutterIndex + 1,\n      sizes: this.createAreaSizes()\n    };\n  }\n  createAreaSizes() {\n    return this._visibleAreas().map(area => area._internalSize());\n  }\n  createDragStartContext(startEvent, areaBeforeGutterIndex, areaAfterGutterIndex) {\n    const splitBoundingRect = this.elementRef.nativeElement.getBoundingClientRect();\n    const splitSize = this.direction() === 'horizontal' ? splitBoundingRect.width : splitBoundingRect.height;\n    const totalAreasPixelSize = splitSize - (this._visibleAreas().length - 1) * this.gutterSize();\n    // Use the internal size and split size to calculate the pixel size from wildcard and percent areas\n    const areaPixelSizesWithWildcard = this._areas().map(area => {\n      if (this.unit() === 'pixel') {\n        return area._internalSize();\n      } else {\n        const size = area._internalSize();\n        if (size === '*') {\n          return size;\n        }\n        return size / 100 * totalAreasPixelSize;\n      }\n    });\n    const remainingSize = Math.max(0, totalAreasPixelSize - sum(areaPixelSizesWithWildcard, size => size === '*' ? 0 : size));\n    const areasPixelSizes = areaPixelSizesWithWildcard.map(size => size === '*' ? remainingSize : size);\n    return {\n      startEvent,\n      areaBeforeGutterIndex,\n      areaAfterGutterIndex,\n      areasPixelSizes,\n      totalAreasPixelSize,\n      areaIndexToBoundaries: toRecord(this._areas(), (area, index) => {\n        const percentToPixels = percent => percent / 100 * totalAreasPixelSize;\n        const value = this.unit() === 'pixel' ? {\n          min: area._normalizedMinSize(),\n          max: area._normalizedMaxSize()\n        } : {\n          min: percentToPixels(area._normalizedMinSize()),\n          max: percentToPixels(area._normalizedMaxSize())\n        };\n        return [index.toString(), value];\n      })\n    };\n  }\n  mouseDragMove(moveEvent, dragStartContext) {\n    moveEvent.preventDefault();\n    moveEvent.stopPropagation();\n    const endPoint = getPointFromEvent(moveEvent);\n    this.dragMoveToPoint(endPoint, dragStartContext);\n  }\n  dragMoveToPoint(endPoint, dragStartContext) {\n    const startPoint = getPointFromEvent(dragStartContext.startEvent);\n    const preDirOffset = this.direction() === 'horizontal' ? endPoint.x - startPoint.x : endPoint.y - startPoint.y;\n    const offset = this.direction() === 'horizontal' && this.dir() === 'rtl' ? -preDirOffset : preDirOffset;\n    const isDraggingForward = offset > 0;\n    // Align offset with gutter step and abs it as we need absolute pixels movement\n    const absSteppedOffset = Math.abs(Math.round(offset / this.gutterStep()) * this.gutterStep());\n    // Copy as we don't want to edit the original array\n    const tempAreasPixelSizes = [...dragStartContext.areasPixelSizes];\n    // As we are going to shuffle the areas order for easier iterations we should work with area indices array\n    // instead of actual area sizes array.\n    const areasIndices = tempAreasPixelSizes.map((_, index) => index);\n    // The two variables below are ordered for iterations with real area indices inside.\n    // We must also remove the invisible ones as we can't expand or shrink them.\n    const areasIndicesBeforeGutter = this.restrictMove() ? [dragStartContext.areaBeforeGutterIndex] : areasIndices.slice(0, dragStartContext.areaBeforeGutterIndex + 1).filter(index => this._areas()[index].visible()).reverse();\n    const areasIndicesAfterGutter = this.restrictMove() ? [dragStartContext.areaAfterGutterIndex] : areasIndices.slice(dragStartContext.areaAfterGutterIndex).filter(index => this._areas()[index].visible());\n    // Based on direction we need to decide which areas are expanding and which are shrinking\n    const potentialAreasIndicesArrToShrink = isDraggingForward ? areasIndicesAfterGutter : areasIndicesBeforeGutter;\n    const potentialAreasIndicesArrToExpand = isDraggingForward ? areasIndicesBeforeGutter : areasIndicesAfterGutter;\n    let remainingPixels = absSteppedOffset;\n    let potentialShrinkArrIndex = 0;\n    let potentialExpandArrIndex = 0;\n    // We gradually run in both expand and shrink direction transferring pixels from the offset.\n    // We stop once no pixels are left or we reached the end of either the expanding areas or the shrinking areas\n    while (remainingPixels !== 0 && potentialShrinkArrIndex < potentialAreasIndicesArrToShrink.length && potentialExpandArrIndex < potentialAreasIndicesArrToExpand.length) {\n      const areaIndexToShrink = potentialAreasIndicesArrToShrink[potentialShrinkArrIndex];\n      const areaIndexToExpand = potentialAreasIndicesArrToExpand[potentialExpandArrIndex];\n      const areaToShrinkSize = tempAreasPixelSizes[areaIndexToShrink];\n      const areaToExpandSize = tempAreasPixelSizes[areaIndexToExpand];\n      const areaToShrinkMinSize = dragStartContext.areaIndexToBoundaries[areaIndexToShrink].min;\n      const areaToExpandMaxSize = dragStartContext.areaIndexToBoundaries[areaIndexToExpand].max;\n      // We can only transfer pixels based on the shrinking area min size and the expanding area max size\n      // to avoid overflow. If any pixels left they will be handled by the next area in the next `while` iteration\n      const maxPixelsToShrink = areaToShrinkSize - areaToShrinkMinSize;\n      const maxPixelsToExpand = areaToExpandMaxSize - areaToExpandSize;\n      const pixelsToTransfer = Math.min(maxPixelsToShrink, maxPixelsToExpand, remainingPixels);\n      // Actual pixels transfer\n      tempAreasPixelSizes[areaIndexToShrink] -= pixelsToTransfer;\n      tempAreasPixelSizes[areaIndexToExpand] += pixelsToTransfer;\n      remainingPixels -= pixelsToTransfer;\n      // Once min threshold reached we need to move to the next area in turn\n      if (tempAreasPixelSizes[areaIndexToShrink] === areaToShrinkMinSize) {\n        potentialShrinkArrIndex++;\n      }\n      // Once max threshold reached we need to move to the next area in turn\n      if (tempAreasPixelSizes[areaIndexToExpand] === areaToExpandMaxSize) {\n        potentialExpandArrIndex++;\n      }\n    }\n    this._areas().forEach((area, index) => {\n      // No need to update wildcard size\n      if (area._internalSize() === '*') {\n        return;\n      }\n      if (this.unit() === 'pixel') {\n        area._internalSize.set(tempAreasPixelSizes[index]);\n      } else {\n        const percentSize = tempAreasPixelSizes[index] / dragStartContext.totalAreasPixelSize * 100;\n        // Fix javascript only working with float numbers which are inaccurate compared to decimals\n        area._internalSize.set(parseFloat(percentSize.toFixed(10)));\n      }\n    });\n    this.dragProgressSubject.next(this.createDragInteractionEvent(this.draggedGutterIndex()));\n  }\n  createGridTemplateColumnsStyle() {\n    const columns = [];\n    const sumNonWildcardSizes = sum(this._visibleAreas(), area => {\n      const size = area._internalSize();\n      return size === '*' ? 0 : size;\n    });\n    const visibleAreasCount = this._visibleAreas().length;\n    let visitedVisibleAreas = 0;\n    this._areas().forEach((area, index, areas) => {\n      const unit = this.unit();\n      const areaSize = area._internalSize();\n      // Add area size column\n      if (!area.visible()) {\n        columns.push(unit === 'percent' || areaSize === '*' ? '0fr' : '0px');\n      } else {\n        if (unit === 'pixel') {\n          const columnValue = areaSize === '*' ? '1fr' : `${areaSize}px`;\n          columns.push(columnValue);\n        } else {\n          const percentSize = areaSize === '*' ? 100 - sumNonWildcardSizes : areaSize;\n          const columnValue = `${percentSize}fr`;\n          columns.push(columnValue);\n        }\n        visitedVisibleAreas++;\n      }\n      const isLastArea = index === areas.length - 1;\n      if (isLastArea) {\n        return;\n      }\n      const remainingVisibleAreas = visibleAreasCount - visitedVisibleAreas;\n      // Only add gutter with size if this area is visible and there are more visible areas after this one\n      // to avoid ghost gutters\n      if (area.visible() && remainingVisibleAreas > 0) {\n        columns.push(`${this.gutterSize()}px`);\n      } else {\n        columns.push('0px');\n      }\n    });\n    return this.direction() === 'horizontal' ? `1fr / ${columns.join(' ')}` : `${columns.join(' ')} / 1fr`;\n  }\n  createAlignedVisibleAreasSize() {\n    const visibleAreasSizes = this._visibleAreas().map(area => {\n      const size = area.size();\n      return size === 'auto' ? '*' : size;\n    });\n    const isValid = areAreasValid(this._visibleAreas(), this.unit(), false);\n    if (isValid) {\n      return visibleAreasSizes;\n    }\n    const unit = this.unit();\n    if (unit === 'percent') {\n      // Distribute sizes equally\n      const defaultPercentSize = 100 / visibleAreasSizes.length;\n      return visibleAreasSizes.map(() => defaultPercentSize);\n    }\n    if (unit === 'pixel') {\n      // Make sure only one wildcard area\n      const wildcardAreas = visibleAreasSizes.filter(areaSize => areaSize === '*');\n      if (wildcardAreas.length === 0) {\n        return ['*', ...visibleAreasSizes.slice(1)];\n      } else {\n        const firstWildcardIndex = visibleAreasSizes.findIndex(areaSize => areaSize === '*');\n        const defaultPxSize = 100;\n        return visibleAreasSizes.map((areaSize, index) => index === firstWildcardIndex || areaSize !== '*' ? areaSize : defaultPxSize);\n      }\n    }\n    return assertUnreachable(unit, 'SplitUnit');\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function SplitComponent_Factory(t) {\n      return new (t || SplitComponent)();\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: SplitComponent,\n      selectors: [[\"as-split\"]],\n      contentQueries: function SplitComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuerySignal(dirIndex, ctx._areas, SPLIT_AREA_CONTRACT, 4);\n          i0.ɵɵcontentQuerySignal(dirIndex, ctx.customGutter, SplitGutterDirective, 5);\n        }\n        if (rf & 2) {\n          i0.ɵɵqueryAdvance(2);\n        }\n      },\n      hostVars: 3,\n      hostBindings: function SplitComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"dir\", ctx.hostDirBinding);\n          i0.ɵɵclassMap(ctx.hostClassesBinding);\n        }\n      },\n      inputs: {\n        gutterSize: [i0.ɵɵInputFlags.SignalBased, \"gutterSize\"],\n        gutterStep: [i0.ɵɵInputFlags.SignalBased, \"gutterStep\"],\n        disabled: [i0.ɵɵInputFlags.SignalBased, \"disabled\"],\n        gutterClickDeltaPx: [i0.ɵɵInputFlags.SignalBased, \"gutterClickDeltaPx\"],\n        direction: [i0.ɵɵInputFlags.SignalBased, \"direction\"],\n        dir: [i0.ɵɵInputFlags.SignalBased, \"dir\"],\n        unit: [i0.ɵɵInputFlags.SignalBased, \"unit\"],\n        gutterAriaLabel: [i0.ɵɵInputFlags.SignalBased, \"gutterAriaLabel\"],\n        restrictMove: [i0.ɵɵInputFlags.SignalBased, \"restrictMove\"],\n        useTransition: [i0.ɵɵInputFlags.SignalBased, \"useTransition\"],\n        gutterDblClickDuration: [i0.ɵɵInputFlags.SignalBased, \"gutterDblClickDuration\"]\n      },\n      outputs: {\n        gutterClick: \"gutterClick\",\n        gutterDblClick: \"gutterDblClick\",\n        dragStart: \"dragStart\",\n        dragEnd: \"dragEnd\",\n        transitionEnd: \"transitionEnd\"\n      },\n      exportAs: [\"asSplit\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 3,\n      vars: 0,\n      consts: [[\"gutter\", \"\"], [\"role\", \"separator\", \"tabindex\", \"0\", \"asSplitCustomEventsBehavior\", \"\", 1, \"as-split-gutter\", 3, \"ngStyle\", \"as-dragged\", \"asSplitCustomMultiClickThreshold\", \"asSplitCustomClickDeltaInPx\"], [\"role\", \"separator\", \"tabindex\", \"0\", \"asSplitCustomEventsBehavior\", \"\", 1, \"as-split-gutter\", 3, \"asSplitCustomClick\", \"asSplitCustomDblClick\", \"asSplitCustomMouseDown\", \"asSplitCustomKeyDown\", \"ngStyle\", \"asSplitCustomMultiClickThreshold\", \"asSplitCustomClickDeltaInPx\"], [4, \"asSplitGutterDynamicInjector\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\", \"ngTemplateOutletInjector\"], [1, \"as-split-gutter-icon\"]],\n      template: function SplitComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n          i0.ɵɵrepeaterCreate(1, SplitComponent_For_2_Template, 1, 1, null, null, i0.ɵɵrepeaterTrackByIdentity);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵrepeater(ctx._areas());\n        }\n      },\n      dependencies: [NgStyle, SplitCustomEventsBehaviorDirective, SplitGutterDynamicInjectorDirective, NgTemplateOutlet],\n      styles: [\"@property --as-gutter-background-color{syntax: \\\"<color>\\\"; inherits: true; initial-value: #eeeeee;}@property --as-gutter-icon-horizontal{syntax: \\\"<url>\\\"; inherits: true; initial-value: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAeCAYAAADkftS9AAAAIklEQVQoU2M4c+bMfxAGAgYYmwGrIIiDjrELjpo5aiZeMwF+yNnOs5KSvgAAAABJRU5ErkJggg==);}@property --as-gutter-icon-vertical{syntax: \\\"<url>\\\"; inherits: true; initial-value: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAFCAMAAABl/6zIAAAABlBMVEUAAADMzMzIT8AyAAAAAXRSTlMAQObYZgAAABRJREFUeAFjYGRkwIMJSeMHlBkOABP7AEGzSuPKAAAAAElFTkSuQmCC);}@property --as-gutter-icon-disabled{syntax: \\\"<url>\\\"; inherits: true; initial-value: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAeCAYAAADkftS9AAAAIklEQVQoU2M4c+bMfxAGAgYYmwGrIIiDjrELjpo5aiZeMwF+yNnOs5KSvgAAAABJRU5ErkJggg==);}@property --as-transition-duration{syntax: \\\"<time>\\\"; inherits: true; initial-value: .3s;}@property --as-gutter-disabled-cursor{syntax: \\\"*\\\"; inherits: true; initial-value: default;}[_nghost-%COMP%]{--_as-gutter-background-color: var(--as-gutter-background-color, #eeeeee);--_as-gutter-icon-horizontal: var( --as-gutter-icon-horizontal, url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAeCAYAAADkftS9AAAAIklEQVQoU2M4c+bMfxAGAgYYmwGrIIiDjrELjpo5aiZeMwF+yNnOs5KSvgAAAABJRU5ErkJggg==) );--_as-gutter-icon-vertical: var( --as-gutter-icon-vertical, url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAFCAMAAABl/6zIAAAABlBMVEUAAADMzMzIT8AyAAAAAXRSTlMAQObYZgAAABRJREFUeAFjYGRkwIMJSeMHlBkOABP7AEGzSuPKAAAAAElFTkSuQmCC) );--_as-gutter-icon-disabled: var( --as-gutter-icon-disabled, url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAeCAYAAADkftS9AAAAIklEQVQoU2M4c+bMfxAGAgYYmwGrIIiDjrELjpo5aiZeMwF+yNnOs5KSvgAAAABJRU5ErkJggg==) );--_as-transition-duration: var(--as-transition-duration, .3s);--_as-gutter-disabled-cursor: var(--as-gutter-disabled-cursor, default)}[_nghost-%COMP%]{display:grid;overflow:hidden;height:100%;width:100%}.as-transition[_nghost-%COMP%]{transition:grid-template var(--_as-transition-duration)}.as-split-gutter[_ngcontent-%COMP%]{background-color:var(--_as-gutter-background-color);display:flex;align-items:center;justify-content:center;touch-action:none}.as-horizontal[_nghost-%COMP%] > .as-split-gutter[_ngcontent-%COMP%]{cursor:col-resize;height:100%}.as-vertical[_nghost-%COMP%] > .as-split-gutter[_ngcontent-%COMP%]{cursor:row-resize;width:100%}.as-disabled[_nghost-%COMP%] > .as-split-gutter[_ngcontent-%COMP%]{cursor:var(--_as-gutter-disabled-cursor)}.as-split-gutter-icon[_ngcontent-%COMP%]{width:100%;height:100%;background-position:center center;background-repeat:no-repeat}.as-horizontal[_nghost-%COMP%] > .as-split-gutter[_ngcontent-%COMP%] > .as-split-gutter-icon[_ngcontent-%COMP%]{background-image:var(--_as-gutter-icon-horizontal)}.as-vertical[_nghost-%COMP%] > .as-split-gutter[_ngcontent-%COMP%] > .as-split-gutter-icon[_ngcontent-%COMP%]{background-image:var(--_as-gutter-icon-vertical)}.as-disabled[_nghost-%COMP%] > .as-split-gutter[_ngcontent-%COMP%] > .as-split-gutter-icon[_ngcontent-%COMP%]{background-image:var(--_as-gutter-icon-disabled)}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SplitComponent, [{\n    type: Component,\n    args: [{\n      selector: 'as-split',\n      imports: [NgStyle, SplitCustomEventsBehaviorDirective, SplitGutterDynamicInjectorDirective, NgTemplateOutlet],\n      exportAs: 'asSplit',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<ng-content></ng-content>\\n@for (area of _areas(); track area) {\\n  @if (!$last) {\\n    <div\\n      #gutter\\n      class=\\\"as-split-gutter\\\"\\n      role=\\\"separator\\\"\\n      tabindex=\\\"0\\\"\\n      [attr.aria-label]=\\\"gutterAriaLabel()\\\"\\n      [attr.aria-orientation]=\\\"direction()\\\"\\n      [attr.aria-valuemin]=\\\"getAriaValue(area.minSize())\\\"\\n      [attr.aria-valuemax]=\\\"getAriaValue(area.maxSize())\\\"\\n      [attr.aria-valuenow]=\\\"getAriaValue(area._internalSize())\\\"\\n      [attr.aria-valuetext]=\\\"getAriaAreaSizeText(area)\\\"\\n      [ngStyle]=\\\"getGutterGridStyle($index + 1)\\\"\\n      [class.as-dragged]=\\\"draggedGutterIndex() === $index\\\"\\n      asSplitCustomEventsBehavior\\n      [asSplitCustomMultiClickThreshold]=\\\"gutterDblClickDuration()\\\"\\n      [asSplitCustomClickDeltaInPx]=\\\"gutterClickDeltaPx()\\\"\\n      (asSplitCustomClick)=\\\"gutterClicked($index)\\\"\\n      (asSplitCustomDblClick)=\\\"gutterDoubleClicked($index)\\\"\\n      (asSplitCustomMouseDown)=\\\"gutterMouseDown($event, gutter, $index, $index, $index + 1)\\\"\\n      (asSplitCustomKeyDown)=\\\"gutterKeyDown($event, $index, $index, $index + 1)\\\"\\n    >\\n      @if (customGutter()?.template) {\\n        <ng-container *asSplitGutterDynamicInjector=\\\"$index + 1; let injector\\\">\\n          <ng-container\\n            *ngTemplateOutlet=\\\"\\n              customGutter().template;\\n              context: {\\n                areaBefore: area,\\n                areaAfter: _areas()[$index + 1],\\n                gutterNum: $index + 1,\\n                first: $first,\\n                last: $index === _areas().length - 2,\\n                isDragged: draggedGutterIndex() === $index\\n              };\\n              injector: injector\\n            \\\"\\n          ></ng-container>\\n        </ng-container>\\n      } @else {\\n        <div class=\\\"as-split-gutter-icon\\\"></div>\\n      }\\n    </div>\\n  }\\n}\\n\",\n      styles: [\"@property --as-gutter-background-color{syntax: \\\"<color>\\\"; inherits: true; initial-value: #eeeeee;}@property --as-gutter-icon-horizontal{syntax: \\\"<url>\\\"; inherits: true; initial-value: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAeCAYAAADkftS9AAAAIklEQVQoU2M4c+bMfxAGAgYYmwGrIIiDjrELjpo5aiZeMwF+yNnOs5KSvgAAAABJRU5ErkJggg==);}@property --as-gutter-icon-vertical{syntax: \\\"<url>\\\"; inherits: true; initial-value: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAFCAMAAABl/6zIAAAABlBMVEUAAADMzMzIT8AyAAAAAXRSTlMAQObYZgAAABRJREFUeAFjYGRkwIMJSeMHlBkOABP7AEGzSuPKAAAAAElFTkSuQmCC);}@property --as-gutter-icon-disabled{syntax: \\\"<url>\\\"; inherits: true; initial-value: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAeCAYAAADkftS9AAAAIklEQVQoU2M4c+bMfxAGAgYYmwGrIIiDjrELjpo5aiZeMwF+yNnOs5KSvgAAAABJRU5ErkJggg==);}@property --as-transition-duration{syntax: \\\"<time>\\\"; inherits: true; initial-value: .3s;}@property --as-gutter-disabled-cursor{syntax: \\\"*\\\"; inherits: true; initial-value: default;}:host{--_as-gutter-background-color: var(--as-gutter-background-color, #eeeeee);--_as-gutter-icon-horizontal: var( --as-gutter-icon-horizontal, url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAeCAYAAADkftS9AAAAIklEQVQoU2M4c+bMfxAGAgYYmwGrIIiDjrELjpo5aiZeMwF+yNnOs5KSvgAAAABJRU5ErkJggg==) );--_as-gutter-icon-vertical: var( --as-gutter-icon-vertical, url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAFCAMAAABl/6zIAAAABlBMVEUAAADMzMzIT8AyAAAAAXRSTlMAQObYZgAAABRJREFUeAFjYGRkwIMJSeMHlBkOABP7AEGzSuPKAAAAAElFTkSuQmCC) );--_as-gutter-icon-disabled: var( --as-gutter-icon-disabled, url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAeCAYAAADkftS9AAAAIklEQVQoU2M4c+bMfxAGAgYYmwGrIIiDjrELjpo5aiZeMwF+yNnOs5KSvgAAAABJRU5ErkJggg==) );--_as-transition-duration: var(--as-transition-duration, .3s);--_as-gutter-disabled-cursor: var(--as-gutter-disabled-cursor, default)}:host{display:grid;overflow:hidden;height:100%;width:100%}:host(.as-transition){transition:grid-template var(--_as-transition-duration)}.as-split-gutter{background-color:var(--_as-gutter-background-color);display:flex;align-items:center;justify-content:center;touch-action:none}:host(.as-horizontal)>.as-split-gutter{cursor:col-resize;height:100%}:host(.as-vertical)>.as-split-gutter{cursor:row-resize;width:100%}:host(.as-disabled)>.as-split-gutter{cursor:var(--_as-gutter-disabled-cursor)}.as-split-gutter-icon{width:100%;height:100%;background-position:center center;background-repeat:no-repeat}:host(.as-horizontal)>.as-split-gutter>.as-split-gutter-icon{background-image:var(--_as-gutter-icon-horizontal)}:host(.as-vertical)>.as-split-gutter>.as-split-gutter-icon{background-image:var(--_as-gutter-icon-vertical)}:host(.as-disabled)>.as-split-gutter>.as-split-gutter-icon{background-image:var(--_as-gutter-icon-disabled)}\\n\"]\n    }]\n  }], () => [], {\n    hostClassesBinding: [{\n      type: HostBinding,\n      args: ['class']\n    }],\n    hostDirBinding: [{\n      type: HostBinding,\n      args: ['dir']\n    }]\n  });\n})();\nconst internalAreaSizeTransform = areaSize => areaSize === undefined || areaSize === null || areaSize === '*' ? '*' : +areaSize;\nconst areaSizeTransform = areaSize => internalAreaSizeTransform(areaSize);\nconst boundaryAreaSizeTransform = areaSize => internalAreaSizeTransform(areaSize);\nclass SplitAreaComponent {\n  constructor() {\n    this.split = inject(SplitComponent);\n    this.size = input('auto', {\n      transform: areaSizeTransform\n    });\n    this.minSize = input('*', {\n      transform: boundaryAreaSizeTransform\n    });\n    this.maxSize = input('*', {\n      transform: boundaryAreaSizeTransform\n    });\n    this.lockSize = input(false, {\n      transform: booleanAttribute\n    });\n    this.visible = input(true, {\n      transform: booleanAttribute\n    });\n    /**\n     * @internal\n     */\n    this._internalSize = mirrorSignal(\n    // As size is an input and we can change the size without the outside\n    // listening to the change we need an intermediate writeable signal\n    computed(() => {\n      if (!this.visible()) {\n        return 0;\n      }\n      const visibleIndex = this.split._visibleAreas().findIndex(area => area === this);\n      return this.split._alignedVisibleAreasSizes()[visibleIndex];\n    }));\n    /**\n     * @internal\n     */\n    this._normalizedMinSize = computed(() => this.normalizeMinSize());\n    /**\n     * @internal\n     */\n    this._normalizedMaxSize = computed(() => this.normalizeMaxSize());\n    this.index = computed(() => this.split._areas().findIndex(area => area === this));\n    this.gridAreaNum = computed(() => this.index() * 2 + 1);\n    this.hostClasses = computed(() => createClassesString({\n      ['as-split-area']: true,\n      ['as-min']: this.visible() && this._internalSize() === this._normalizedMinSize(),\n      ['as-max']: this.visible() && this._internalSize() === this._normalizedMaxSize(),\n      ['as-hidden']: !this.visible()\n    }));\n  }\n  get hostClassesBinding() {\n    return this.hostClasses();\n  }\n  get hostGridColumnStyleBinding() {\n    return this.split.direction() === 'horizontal' ? `${this.gridAreaNum()} / ${this.gridAreaNum()}` : undefined;\n  }\n  get hostGridRowStyleBinding() {\n    return this.split.direction() === 'vertical' ? `${this.gridAreaNum()} / ${this.gridAreaNum()}` : undefined;\n  }\n  get hostPositionStyleBinding() {\n    return this.split._isDragging() ? 'relative' : undefined;\n  }\n  normalizeMinSize() {\n    const defaultMinSize = 0;\n    if (!this.visible()) {\n      return defaultMinSize;\n    }\n    const minSize = this.normalizeSizeBoundary(this.minSize, defaultMinSize);\n    const size = this.size();\n    if (size !== '*' && size !== 'auto' && size < minSize) {\n      if (isDevMode()) {\n        console.warn('as-split: size cannot be smaller than minSize');\n      }\n      return defaultMinSize;\n    }\n    return minSize;\n  }\n  normalizeMaxSize() {\n    const defaultMaxSize = Infinity;\n    if (!this.visible()) {\n      return defaultMaxSize;\n    }\n    const maxSize = this.normalizeSizeBoundary(this.maxSize, defaultMaxSize);\n    const size = this.size();\n    if (size !== '*' && size !== 'auto' && size > maxSize) {\n      if (isDevMode()) {\n        console.warn('as-split: size cannot be larger than maxSize');\n      }\n      return defaultMaxSize;\n    }\n    return maxSize;\n  }\n  normalizeSizeBoundary(sizeBoundarySignal, defaultBoundarySize) {\n    const size = this.size();\n    const lockSize = this.lockSize();\n    const boundarySize = sizeBoundarySignal();\n    if (lockSize) {\n      if (isDevMode() && boundarySize !== '*') {\n        console.warn('as-split: lockSize overwrites maxSize/minSize');\n      }\n      if (size === '*' || size === 'auto') {\n        if (isDevMode()) {\n          console.warn(`as-split: lockSize isn't supported on area with * size or without size`);\n        }\n        return defaultBoundarySize;\n      }\n      return size;\n    }\n    if (boundarySize === '*') {\n      return defaultBoundarySize;\n    }\n    if (size === '*' || size === 'auto') {\n      if (isDevMode()) {\n        console.warn('as-split: maxSize/minSize not allowed on * or without size');\n      }\n      return defaultBoundarySize;\n    }\n    return boundarySize;\n  }\n  /** @nocollapse */\n  static {\n    this.ɵfac = function SplitAreaComponent_Factory(t) {\n      return new (t || SplitAreaComponent)();\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: SplitAreaComponent,\n      selectors: [[\"as-split-area\"]],\n      hostVars: 8,\n      hostBindings: function SplitAreaComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.hostClassesBinding);\n          i0.ɵɵstyleProp(\"grid-column\", ctx.hostGridColumnStyleBinding)(\"grid-row\", ctx.hostGridRowStyleBinding)(\"position\", ctx.hostPositionStyleBinding);\n        }\n      },\n      inputs: {\n        size: [i0.ɵɵInputFlags.SignalBased, \"size\"],\n        minSize: [i0.ɵɵInputFlags.SignalBased, \"minSize\"],\n        maxSize: [i0.ɵɵInputFlags.SignalBased, \"maxSize\"],\n        lockSize: [i0.ɵɵInputFlags.SignalBased, \"lockSize\"],\n        visible: [i0.ɵɵInputFlags.SignalBased, \"visible\"]\n      },\n      exportAs: [\"asSplitArea\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: SPLIT_AREA_CONTRACT,\n        useExisting: SplitAreaComponent\n      }]), i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 2,\n      vars: 1,\n      consts: [[1, \"as-iframe-fix\"]],\n      template: function SplitAreaComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n          i0.ɵɵtemplate(1, SplitAreaComponent_Conditional_1_Template, 1, 0, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(1, ctx.split._isDragging() ? 1 : -1);\n        }\n      },\n      styles: [\"[_nghost-%COMP%]{overflow-x:hidden;overflow-y:auto}.as-horizontal > [_nghost-%COMP%]{height:100%}.as-vertical > [_nghost-%COMP%]{width:100%}.as-iframe-fix[_ngcontent-%COMP%]{position:absolute;top:0;left:0;width:100%;height:100%}\"],\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SplitAreaComponent, [{\n    type: Component,\n    args: [{\n      selector: 'as-split-area',\n      standalone: true,\n      exportAs: 'asSplitArea',\n      providers: [{\n        provide: SPLIT_AREA_CONTRACT,\n        useExisting: SplitAreaComponent\n      }],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: \"<ng-content></ng-content>\\n@if (split._isDragging()) {\\n  <div class=\\\"as-iframe-fix\\\"></div>\\n}\\n\",\n      styles: [\":host{overflow-x:hidden;overflow-y:auto}.as-horizontal>:host{height:100%}.as-vertical>:host{width:100%}.as-iframe-fix{position:absolute;top:0;left:0;width:100%;height:100%}\\n\"]\n    }]\n  }], null, {\n    hostClassesBinding: [{\n      type: HostBinding,\n      args: ['class']\n    }],\n    hostGridColumnStyleBinding: [{\n      type: HostBinding,\n      args: ['style.grid-column']\n    }],\n    hostGridRowStyleBinding: [{\n      type: HostBinding,\n      args: ['style.grid-row']\n    }],\n    hostPositionStyleBinding: [{\n      type: HostBinding,\n      args: ['style.position']\n    }]\n  });\n})();\nclass AngularSplitModule {\n  /** @nocollapse */static {\n    this.ɵfac = function AngularSplitModule_Factory(t) {\n      return new (t || AngularSplitModule)();\n    };\n  }\n  /** @nocollapse */\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: AngularSplitModule,\n      imports: [SplitComponent, SplitAreaComponent, SplitGutterDirective, SplitGutterDragHandleDirective, SplitGutterExcludeFromDragDirective],\n      exports: [SplitComponent, SplitAreaComponent, SplitGutterDirective, SplitGutterDragHandleDirective, SplitGutterExcludeFromDragDirective]\n    });\n  }\n  /** @nocollapse */\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AngularSplitModule, [{\n    type: NgModule,\n    args: [{\n      imports: [SplitComponent, SplitAreaComponent, SplitGutterDirective, SplitGutterDragHandleDirective, SplitGutterExcludeFromDragDirective],\n      exports: [SplitComponent, SplitAreaComponent, SplitGutterDirective, SplitGutterDragHandleDirective, SplitGutterExcludeFromDragDirective]\n    }]\n  }], null, null);\n})();\n\n/*\n * Public API Surface of angular-split\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AngularSplitModule, SplitAreaComponent, SplitComponent, SplitGutterDirective, SplitGutterDragHandleDirective, SplitGutterExcludeFromDragDirective, provideAngularSplitOptions };", "map": {"version": 3, "names": ["i0", "InjectionToken", "inject", "TemplateRef", "Directive", "ElementRef", "computed", "signal", "untracked", "NgZone", "numberAttribute", "input", "output", "ViewContainerRef", "effect", "Injector", "Renderer2", "contentChildren", "contentChild", "booleanAttribute", "isDevMode", "Component", "ChangeDetectionStrategy", "HostBinding", "NgModule", "takeUntilDestroyed", "merge", "fromEvent", "filter", "Observable", "switchMap", "take", "map", "takeUntil", "tap", "timeInterval", "scan", "mergeMap", "of", "delay", "repeat", "Subject", "startWith", "pairwise", "<PERSON><PERSON><PERSON><PERSON>", "DOCUMENT", "NgStyle", "NgTemplateOutlet", "_c0", "_c1", "a0", "a1", "a2", "a3", "a4", "a5", "areaBefore", "areaAfter", "gutterNum", "first", "last", "isDragged", "SplitComponent_For_2_Conditional_0_Conditional_2_ng_container_0_ng_container_1_Template", "rf", "ctx", "ɵɵelementContainer", "SplitComponent_For_2_Conditional_0_Conditional_2_ng_container_0_Template", "ɵɵelementContainerStart", "ɵɵtemplate", "ɵɵelementContainerEnd", "injector_r5", "$implicit", "ctx_r5", "ɵɵnextContext", "area_r7", "$index_r2", "$index", "ɵ$index_2_r8", "ctx_r2", "ɵɵadvance", "ɵɵproperty", "customGutter", "template", "ɵɵpureFunction6", "_areas", "length", "draggedGutterIndex", "SplitComponent_For_2_Conditional_0_Conditional_2_Template", "SplitComponent_For_2_Conditional_0_Conditional_3_Template", "ɵɵelement", "SplitComponent_For_2_Conditional_0_Template", "_r1", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "SplitComponent_For_2_Conditional_0_Template_div_asSplitCustomClick_0_listener", "ɵɵrestoreView", "ɵɵresetView", "gutterClicked", "SplitComponent_For_2_Conditional_0_Template_div_asSplitCustomDblClick_0_listener", "gutterDoubleClicked", "SplitComponent_For_2_Conditional_0_Template_div_asSplitCustomMouseDown_0_listener", "$event", "gutter_r4", "ɵɵreference", "gutterMouseDown", "SplitComponent_For_2_Conditional_0_Template_div_asSplitCustomKeyDown_0_listener", "gutterKeyDown", "ɵɵelementEnd", "tmp_22_0", "ɵɵclassProp", "getGutterGridStyle", "gutterDblClickDuration", "gutterClickDeltaPx", "ɵɵattribute", "gutterAriaLabel", "direction", "getAriaValue", "minSize", "maxSize", "_internalSize", "getAriaAreaSizeText", "ɵɵconditional", "SplitComponent_For_2_Template", "ɵ$count_2_r9", "$count", "SplitAreaComponent_Conditional_1_Template", "defaultOptions", "dir", "disabled", "gutterSize", "gutterStep", "restrictMove", "unit", "useTransition", "ANGULAR_SPLIT_DEFAULT_OPTIONS", "providedIn", "factory", "provideAngularSplitOptions", "options", "provide", "useFactory", "skipSelf", "SplitGutterDirective", "constructor", "_gutterToHandleElementMap", "Map", "_gutterToExcludeDragElementMap", "_canStartDragging", "originElement", "has", "isInsideExclude", "get", "some", "gutterExcludeElement", "nativeElement", "contains", "gutterHandleElement", "_addToMap", "elementRef", "push", "set", "_removedFromMap", "elements", "splice", "indexOf", "delete", "ngTemplateContextGuard", "_dir", "ɵfac", "SplitGutterDirective_Factory", "t", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "standalone", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "GUTTER_NUM_TOKEN", "SplitGutterDragHandleDirective", "gutterDir", "ngOnDestroy", "SplitGutterDragHandleDirective_Factory", "SplitGutterExcludeFromDragDirective", "SplitGutterExcludeFromDragDirective_Factory", "getPointFromEvent", "event", "isTouchEvent", "e", "window", "TouchEvent", "changedTouches", "undefined", "clientX", "clientY", "x", "y", "KeyboardEvent", "target", "offsetLeft", "offsetWidth", "offsetTop", "offsetHeight", "gutterEventsEqualWithDelta", "startEvent", "endEvent", "deltaInPx", "gutterElement", "startPoint", "endPoint", "Math", "abs", "fromMouseDownEvent", "pipe", "button", "passive", "fromMouseMoveEvent", "fromMouseUpEvent", "includeTouchCancel", "withoutTouchCancel", "sum", "array", "fn", "reduce", "item", "toRecord", "record", "index", "key", "value", "createClassesString", "classesRecord", "Object", "entries", "join", "mirrorSignal", "outer", "inner", "mirror", "reset", "leaveNgZone", "source", "observer", "runOutsideAngular", "subscribe", "numberAttributeWithFallback", "fallback", "assertUnreachable", "name", "Error", "SplitCustomEventsBehaviorDirective", "document", "multiClickThreshold", "required", "alias", "mouseDown", "click", "dblClick", "keyDown", "emit", "dragStarted$", "mouseDownEvent", "interval", "numOfConsecutiveClicks", "amount", "SplitCustomEventsBehaviorDirective_Factory", "inputs", "ɵɵInputFlags", "SignalBased", "outputs", "are<PERSON><PERSON>s<PERSON><PERSON>", "areas", "logWarnings", "areaSizes", "area", "size", "wildcard<PERSON><PERSON><PERSON>", "areaSize", "console", "warn", "sumPercent", "SplitGutterDynamicInjectorDirective", "vcr", "templateRef", "clear", "injector", "create", "providers", "useValue", "parent", "createEmbeddedView", "SplitGutterDynamicInjectorDirective_Factory", "SPLIT_AREA_CONTRACT", "SplitComponent", "hostClassesBinding", "hostClasses", "hostDirBinding", "renderer", "ngZone", "gutterMouseDownSubject", "dragProgressSubject", "transform", "gutterClick", "gutterDblClick", "dragStart", "dragEnd", "transitionEnd", "dragProgress$", "asObservable", "_visible<PERSON><PERSON>s", "visible", "gridTemplateColumnsStyle", "createGridTemplateColumnsStyle", "_isDragging", "_alignedVisibleAreasSizes", "createAlignedVisibleAreasSize", "every", "setStyle", "context", "gutterIndex", "mouseDownContext", "currMoveEvent", "run", "createDragInteractionEvent", "prevMouseEvent", "createDragStartContext", "areaBeforeGutterIndex", "areaAfterGutterIndex", "dragStartContext", "moveEvent", "mouseDragMove", "complete", "propertyName", "startsWith", "createAreaSizes", "preventDefault", "stopPropagation", "next", "pixelsToMove", "pageMoveMultiplier", "xPointOffset", "yPointOffset", "gutterMidPoint", "dragMoveToPoint", "nextAreaIndex", "style", "toFixed", "sizes", "splitBoundingRect", "getBoundingClientRect", "splitSize", "width", "height", "totalAreasPixelSize", "areaPixelSizesWithWildcard", "remainingSize", "max", "areasPixelSizes", "areaIndexToBoundaries", "percentToPixels", "percent", "min", "_normalizedMinSize", "_normalizedMaxSize", "toString", "preDirOffset", "offset", "isDraggingForward", "absSteppedOffset", "round", "tempAreasPixelSizes", "areasIndices", "_", "areasIndicesBeforeGutter", "slice", "reverse", "areasIndicesAfterGutter", "potentialAreasIndicesArrToShrink", "potentialAreasIndicesArrToExpand", "remainingPixels", "potentialShrinkArrIndex", "potentialExpandArrIndex", "areaIndexToShrink", "areaIndexToExpand", "areaToShrinkSize", "areaToExpandSize", "areaToShrinkMinSize", "areaToExpandMaxSize", "maxPixelsToShrink", "maxPixelsToExpand", "pixelsToTransfer", "for<PERSON>ach", "percentSize", "parseFloat", "columns", "sumNonWildcardSizes", "visibleAreasCount", "visitedVisibleAreas", "columnValue", "isLastArea", "remainingVisibleAreas", "visibleAreasSizes", "<PERSON><PERSON><PERSON><PERSON>", "defaultPercentSize", "firstWildcardIndex", "findIndex", "defaultPxSize", "SplitComponent_Factory", "ɵcmp", "ɵɵdefineComponent", "contentQueries", "SplitComponent_ContentQueries", "dirIndex", "ɵɵcontentQuerySignal", "ɵɵqueryAdvance", "hostVars", "hostBindings", "SplitComponent_HostBindings", "ɵɵhostProperty", "ɵɵclassMap", "exportAs", "features", "ɵɵStandaloneFeature", "ngContentSelectors", "decls", "vars", "consts", "SplitComponent_Template", "ɵɵprojectionDef", "ɵɵprojection", "ɵɵrepeaterCreate", "ɵɵrepeaterTrackByIdentity", "ɵɵrepeater", "dependencies", "styles", "changeDetection", "imports", "OnPush", "internalAreaSizeTransform", "areaSizeTransform", "boundaryAreaSizeTransform", "SplitAreaComponent", "split", "lockSize", "visibleIndex", "normalizeMinSize", "normalizeMaxSize", "gridAreaNum", "hostGridColumnStyleBinding", "hostGridRowStyleBinding", "hostPositionStyleBinding", "defaultMinSize", "normalizeSizeBoundary", "defaultMaxSize", "Infinity", "sizeBoundarySignal", "defaultBoundarySize", "boundarySize", "SplitAreaComponent_Factory", "SplitAreaComponent_HostBindings", "ɵɵstyleProp", "ɵɵProvidersFeature", "useExisting", "SplitAreaComponent_Template", "AngularSplitModule", "AngularSplitModule_Factory", "ɵmod", "ɵɵdefineNgModule", "exports", "ɵinj", "ɵɵdefineInjector"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/angular-split/fesm2022/angular-split.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, inject, TemplateRef, Directive, ElementRef, computed, signal, untracked, NgZone, numberAttribute, input, output, ViewContainerRef, effect, Injector, Renderer2, contentChildren, contentChild, booleanAttribute, isDevMode, Component, ChangeDetectionStrategy, HostBinding, NgModule } from '@angular/core';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { merge, fromEvent, filter, Observable, switchMap, take, map, takeUntil, tap, timeInterval, scan, mergeMap, of, delay, repeat, Subject, startWith, pairwise, skipWhile } from 'rxjs';\nimport { DOCUMENT, NgStyle, NgTemplateOutlet } from '@angular/common';\n\nconst defaultOptions = {\n    dir: 'ltr',\n    direction: 'horizontal',\n    disabled: false,\n    gutterDblClickDuration: 0,\n    gutterSize: 11,\n    gutterStep: 1,\n    gutterClickDeltaPx: 2,\n    restrictMove: false,\n    unit: 'percent',\n    useTransition: false,\n};\nconst ANGULAR_SPLIT_DEFAULT_OPTIONS = new InjectionToken('angular-split-global-config', { providedIn: 'root', factory: () => defaultOptions });\n/**\n * Provides default options for angular split. The options object has hierarchical inheritance\n * which means only the declared properties will be overridden\n */\nfunction provideAngularSplitOptions(options) {\n    return {\n        provide: ANGULAR_SPLIT_DEFAULT_OPTIONS,\n        useFactory: () => ({\n            ...inject(ANGULAR_SPLIT_DEFAULT_OPTIONS, { skipSelf: true }),\n            ...options,\n        }),\n    };\n}\n\nclass SplitGutterDirective {\n    constructor() {\n        this.template = inject(TemplateRef);\n        /**\n         * The map holds reference to the drag handle elements inside instances\n         * of the provided template.\n         *\n         * @internal\n         */\n        this._gutterToHandleElementMap = new Map();\n        /**\n         * The map holds reference to the excluded drag elements inside instances\n         * of the provided template.\n         *\n         * @internal\n         */\n        this._gutterToExcludeDragElementMap = new Map();\n    }\n    /**\n     * @internal\n     */\n    _canStartDragging(originElement, gutterNum) {\n        if (this._gutterToExcludeDragElementMap.has(gutterNum)) {\n            const isInsideExclude = this._gutterToExcludeDragElementMap\n                .get(gutterNum)\n                .some((gutterExcludeElement) => gutterExcludeElement.nativeElement.contains(originElement));\n            if (isInsideExclude) {\n                return false;\n            }\n        }\n        if (this._gutterToHandleElementMap.has(gutterNum)) {\n            return this._gutterToHandleElementMap\n                .get(gutterNum)\n                .some((gutterHandleElement) => gutterHandleElement.nativeElement.contains(originElement));\n        }\n        return true;\n    }\n    /**\n     * @internal\n     */\n    _addToMap(map, gutterNum, elementRef) {\n        if (map.has(gutterNum)) {\n            map.get(gutterNum).push(elementRef);\n        }\n        else {\n            map.set(gutterNum, [elementRef]);\n        }\n    }\n    /**\n     * @internal\n     */\n    _removedFromMap(map, gutterNum, elementRef) {\n        const elements = map.get(gutterNum);\n        elements.splice(elements.indexOf(elementRef), 1);\n        if (elements.length === 0) {\n            map.delete(gutterNum);\n        }\n    }\n    static ngTemplateContextGuard(_dir, ctx) {\n        return true;\n    }\n    /** @nocollapse */ static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.0.5\", ngImport: i0, type: SplitGutterDirective, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    /** @nocollapse */ static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.0.5\", type: SplitGutterDirective, isStandalone: true, selector: \"[asSplitGutter]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.0.5\", ngImport: i0, type: SplitGutterDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[asSplitGutter]',\n                    standalone: true,\n                }]\n        }] });\n\n/**\n * Identifies the gutter by number through DI\n * to allow SplitGutterDragHandleDirective and SplitGutterExcludeFromDragDirective to know\n * the gutter template context without inputs\n */\nconst GUTTER_NUM_TOKEN = new InjectionToken('Gutter num');\n\nclass SplitGutterDragHandleDirective {\n    constructor() {\n        this.gutterNum = inject(GUTTER_NUM_TOKEN);\n        this.elementRef = inject(ElementRef);\n        this.gutterDir = inject(SplitGutterDirective);\n        this.gutterDir._addToMap(this.gutterDir._gutterToHandleElementMap, this.gutterNum, this.elementRef);\n    }\n    ngOnDestroy() {\n        this.gutterDir._removedFromMap(this.gutterDir._gutterToHandleElementMap, this.gutterNum, this.elementRef);\n    }\n    /** @nocollapse */ static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.0.5\", ngImport: i0, type: SplitGutterDragHandleDirective, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    /** @nocollapse */ static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.0.5\", type: SplitGutterDragHandleDirective, isStandalone: true, selector: \"[asSplitGutterDragHandle]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.0.5\", ngImport: i0, type: SplitGutterDragHandleDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[asSplitGutterDragHandle]',\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [] });\n\nclass SplitGutterExcludeFromDragDirective {\n    constructor() {\n        this.gutterNum = inject(GUTTER_NUM_TOKEN);\n        this.elementRef = inject(ElementRef);\n        this.gutterDir = inject(SplitGutterDirective);\n        this.gutterDir._addToMap(this.gutterDir._gutterToExcludeDragElementMap, this.gutterNum, this.elementRef);\n    }\n    ngOnDestroy() {\n        this.gutterDir._removedFromMap(this.gutterDir._gutterToExcludeDragElementMap, this.gutterNum, this.elementRef);\n    }\n    /** @nocollapse */ static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.0.5\", ngImport: i0, type: SplitGutterExcludeFromDragDirective, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    /** @nocollapse */ static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.0.5\", type: SplitGutterExcludeFromDragDirective, isStandalone: true, selector: \"[asSplitGutterExcludeFromDrag]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.0.5\", ngImport: i0, type: SplitGutterExcludeFromDragDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[asSplitGutterExcludeFromDrag]',\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [] });\n\n/**\n * Only supporting a single {@link TouchEvent} point\n */\nfunction getPointFromEvent(event) {\n    // NOTE: In firefox TouchEvent is only defined for touch capable devices\n    const isTouchEvent = (e) => window.TouchEvent && event instanceof TouchEvent;\n    if (isTouchEvent(event)) {\n        if (event.changedTouches.length === 0) {\n            return undefined;\n        }\n        const { clientX, clientY } = event.changedTouches[0];\n        return {\n            x: clientX,\n            y: clientY,\n        };\n    }\n    if (event instanceof KeyboardEvent) {\n        const target = event.target;\n        // Calculate element midpoint\n        return {\n            x: target.offsetLeft + target.offsetWidth / 2,\n            y: target.offsetTop + target.offsetHeight / 2,\n        };\n    }\n    return {\n        x: event.clientX,\n        y: event.clientY,\n    };\n}\nfunction gutterEventsEqualWithDelta(startEvent, endEvent, deltaInPx, gutterElement) {\n    if (!gutterElement.contains(startEvent.target) ||\n        !gutterElement.contains(endEvent.target)) {\n        return false;\n    }\n    const startPoint = getPointFromEvent(startEvent);\n    const endPoint = getPointFromEvent(endEvent);\n    return Math.abs(endPoint.x - startPoint.x) <= deltaInPx && Math.abs(endPoint.y - startPoint.y) <= deltaInPx;\n}\nfunction fromMouseDownEvent(target) {\n    return merge(fromEvent(target, 'mousedown').pipe(filter((e) => e.button === 0)), \n    // We must prevent default here so we declare it as non passive explicitly\n    fromEvent(target, 'touchstart', { passive: false }));\n}\nfunction fromMouseMoveEvent(target) {\n    return merge(fromEvent(target, 'mousemove'), fromEvent(target, 'touchmove'));\n}\nfunction fromMouseUpEvent(target, includeTouchCancel = false) {\n    const withoutTouchCancel = merge(fromEvent(target, 'mouseup'), fromEvent(target, 'touchend'));\n    return includeTouchCancel\n        ? merge(withoutTouchCancel, fromEvent(target, 'touchcancel'))\n        : withoutTouchCancel;\n}\nfunction sum(array, fn) {\n    return array.reduce((sum, item) => sum + fn(item), 0);\n}\nfunction toRecord(array, fn) {\n    return array.reduce((record, item, index) => {\n        const [key, value] = fn(item, index);\n        record[key] = value;\n        return record;\n    }, {});\n}\nfunction createClassesString(classesRecord) {\n    return Object.entries(classesRecord)\n        .filter(([, value]) => value)\n        .map(([key]) => key)\n        .join(' ');\n}\n/**\n * Creates a semi signal which allows writes but is based on an existing signal\n * Whenever the original signal changes the mirror signal gets aligned\n * overriding the current value inside.\n */\nfunction mirrorSignal(outer) {\n    const inner = computed(() => signal(outer()));\n    const mirror = () => inner()();\n    mirror.set = (value) => untracked(inner).set(value);\n    mirror.reset = () => untracked(() => inner().set(outer()));\n    return mirror;\n}\nfunction leaveNgZone() {\n    return (source) => new Observable((observer) => inject(NgZone).runOutsideAngular(() => source.subscribe(observer)));\n}\nconst numberAttributeWithFallback = (fallback) => (value) => numberAttribute(value, fallback);\nconst assertUnreachable = (value, name) => {\n    throw new Error(`as-split: unknown value \"${value}\" for \"${name}\"`);\n};\n\n/* eslint-disable @angular-eslint/no-output-native */\n/* eslint-disable @angular-eslint/no-output-rename */\n/* eslint-disable @angular-eslint/no-input-rename */\n/**\n * Emits mousedown, click, double click and keydown out of zone\n *\n * Emulates browser behavior of click and double click with new features:\n * 1. Supports touch events (tap and double tap)\n * 2. Ignores the first click in a double click with the side effect of a bit slower emission of the click event\n * 3. Allow customizing the delay after mouse down to count another mouse down as a double click\n */\nclass SplitCustomEventsBehaviorDirective {\n    constructor() {\n        this.elementRef = inject(ElementRef);\n        this.document = inject(DOCUMENT);\n        this.multiClickThreshold = input.required({ alias: 'asSplitCustomMultiClickThreshold' });\n        this.deltaInPx = input.required({ alias: 'asSplitCustomClickDeltaInPx' });\n        this.mouseDown = output({ alias: 'asSplitCustomMouseDown' });\n        this.click = output({ alias: 'asSplitCustomClick' });\n        this.dblClick = output({ alias: 'asSplitCustomDblClick' });\n        this.keyDown = output({ alias: 'asSplitCustomKeyDown' });\n        fromEvent(this.elementRef.nativeElement, 'keydown')\n            .pipe(leaveNgZone(), takeUntilDestroyed())\n            .subscribe((e) => this.keyDown.emit(e));\n        // We just need to know when drag start to cancel all click related interactions\n        const dragStarted$ = fromMouseDownEvent(this.elementRef.nativeElement).pipe(switchMap((mouseDownEvent) => fromMouseMoveEvent(this.document).pipe(filter((e) => !gutterEventsEqualWithDelta(mouseDownEvent, e, this.deltaInPx(), this.elementRef.nativeElement)), take(1), map(() => true), takeUntil(fromMouseUpEvent(this.document)))));\n        fromMouseDownEvent(this.elementRef.nativeElement)\n            .pipe(tap((e) => this.mouseDown.emit(e)), \n        // Gather mousedown events intervals to identify whether it is a single double or more click\n        timeInterval(), \n        // We only count a click as part of a multi click if the multiClickThreshold wasn't reached\n        scan((sum, { interval }) => (interval >= this.multiClickThreshold() ? 1 : sum + 1), 0), \n        // As mouseup always comes after mousedown if the delayed mouseup has yet to come\n        // but a new mousedown arrived we can discard the older mouseup as we are part of a multi click\n        switchMap((numOfConsecutiveClicks) => \n        // In case of a double click we directly emit as we don't care about more than two consecutive clicks\n        // so we don't have to wait compared to a single click that might be followed by another for a double.\n        // In case of a mouse up that was too long after the mouse down\n        // we don't have to wait as we know it won't be a multi click but a single click\n        fromMouseUpEvent(this.elementRef.nativeElement).pipe(timeInterval(), take(1), numOfConsecutiveClicks === 2\n            ? map(() => numOfConsecutiveClicks)\n            : mergeMap(({ interval }) => interval >= this.multiClickThreshold()\n                ? of(numOfConsecutiveClicks)\n                : of(numOfConsecutiveClicks).pipe(delay(this.multiClickThreshold() - interval))))), \n        // Discard everything once drag started and listen again (repeat) to mouse down\n        takeUntil(dragStarted$), repeat(), leaveNgZone(), takeUntilDestroyed())\n            .subscribe((amount) => {\n            if (amount === 1) {\n                this.click.emit();\n            }\n            else if (amount === 2) {\n                this.dblClick.emit();\n            }\n        });\n    }\n    /** @nocollapse */ static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.0.5\", ngImport: i0, type: SplitCustomEventsBehaviorDirective, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    /** @nocollapse */ static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"17.1.0\", version: \"19.0.5\", type: SplitCustomEventsBehaviorDirective, isStandalone: true, selector: \"[asSplitCustomEventsBehavior]\", inputs: { multiClickThreshold: { classPropertyName: \"multiClickThreshold\", publicName: \"asSplitCustomMultiClickThreshold\", isSignal: true, isRequired: true, transformFunction: null }, deltaInPx: { classPropertyName: \"deltaInPx\", publicName: \"asSplitCustomClickDeltaInPx\", isSignal: true, isRequired: true, transformFunction: null } }, outputs: { mouseDown: \"asSplitCustomMouseDown\", click: \"asSplitCustomClick\", dblClick: \"asSplitCustomDblClick\", keyDown: \"asSplitCustomKeyDown\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.0.5\", ngImport: i0, type: SplitCustomEventsBehaviorDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[asSplitCustomEventsBehavior]',\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [] });\n\nfunction areAreasValid(areas, unit, logWarnings) {\n    if (areas.length === 0) {\n        return true;\n    }\n    const areaSizes = areas.map((area) => {\n        const size = area.size();\n        return size === 'auto' ? '*' : size;\n    });\n    const wildcardAreas = areaSizes.filter((areaSize) => areaSize === '*');\n    if (wildcardAreas.length > 1) {\n        if (logWarnings) {\n            console.warn('as-split: Maximum one * area is allowed');\n        }\n        return false;\n    }\n    if (unit === 'pixel') {\n        if (wildcardAreas.length === 1) {\n            return true;\n        }\n        if (logWarnings) {\n            console.warn('as-split: Pixel mode must have exactly one * area');\n        }\n        return false;\n    }\n    const sumPercent = sum(areaSizes, (areaSize) => (areaSize === '*' ? 0 : areaSize));\n    // As percent calculation isn't perfect we allow for a small margin of error\n    if (wildcardAreas.length === 1) {\n        if (sumPercent <= 100.1) {\n            return true;\n        }\n        if (logWarnings) {\n            console.warn(`as-split: Percent areas must total 100%`);\n        }\n        return false;\n    }\n    if (sumPercent < 99.9 || sumPercent > 100.1) {\n        if (logWarnings) {\n            console.warn('as-split: Percent areas must total 100%');\n        }\n        return false;\n    }\n    return true;\n}\n\n/**\n * This directive allows creating a dynamic injector inside ngFor\n * with dynamic gutter num and expose the injector for ngTemplateOutlet usage\n */\nclass SplitGutterDynamicInjectorDirective {\n    constructor() {\n        this.vcr = inject(ViewContainerRef);\n        this.templateRef = inject(TemplateRef);\n        this.gutterNum = input.required({ alias: 'asSplitGutterDynamicInjector' });\n        effect(() => {\n            this.vcr.clear();\n            const injector = Injector.create({\n                providers: [\n                    {\n                        provide: GUTTER_NUM_TOKEN,\n                        useValue: this.gutterNum(),\n                    },\n                ],\n                parent: this.vcr.injector,\n            });\n            this.vcr.createEmbeddedView(this.templateRef, { $implicit: injector });\n        });\n    }\n    static ngTemplateContextGuard(_dir, ctx) {\n        return true;\n    }\n    /** @nocollapse */ static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.0.5\", ngImport: i0, type: SplitGutterDynamicInjectorDirective, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    /** @nocollapse */ static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"17.1.0\", version: \"19.0.5\", type: SplitGutterDynamicInjectorDirective, isStandalone: true, selector: \"[asSplitGutterDynamicInjector]\", inputs: { gutterNum: { classPropertyName: \"gutterNum\", publicName: \"asSplitGutterDynamicInjector\", isSignal: true, isRequired: true, transformFunction: null } }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.0.5\", ngImport: i0, type: SplitGutterDynamicInjectorDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[asSplitGutterDynamicInjector]',\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [] });\n\nconst SPLIT_AREA_CONTRACT = new InjectionToken('Split Area Contract');\nclass SplitComponent {\n    get hostClassesBinding() {\n        return this.hostClasses();\n    }\n    get hostDirBinding() {\n        return this.dir();\n    }\n    constructor() {\n        this.document = inject(DOCUMENT);\n        this.renderer = inject(Renderer2);\n        this.elementRef = inject(ElementRef);\n        this.ngZone = inject(NgZone);\n        this.defaultOptions = inject(ANGULAR_SPLIT_DEFAULT_OPTIONS);\n        this.gutterMouseDownSubject = new Subject();\n        this.dragProgressSubject = new Subject();\n        /**\n         * @internal\n         */\n        this._areas = contentChildren(SPLIT_AREA_CONTRACT);\n        this.customGutter = contentChild(SplitGutterDirective);\n        this.gutterSize = input(this.defaultOptions.gutterSize, {\n            transform: numberAttributeWithFallback(this.defaultOptions.gutterSize),\n        });\n        this.gutterStep = input(this.defaultOptions.gutterStep, {\n            transform: numberAttributeWithFallback(this.defaultOptions.gutterStep),\n        });\n        this.disabled = input(this.defaultOptions.disabled, { transform: booleanAttribute });\n        this.gutterClickDeltaPx = input(this.defaultOptions.gutterClickDeltaPx, {\n            transform: numberAttributeWithFallback(this.defaultOptions.gutterClickDeltaPx),\n        });\n        this.direction = input(this.defaultOptions.direction);\n        this.dir = input(this.defaultOptions.dir);\n        this.unit = input(this.defaultOptions.unit);\n        this.gutterAriaLabel = input();\n        this.restrictMove = input(this.defaultOptions.restrictMove, { transform: booleanAttribute });\n        this.useTransition = input(this.defaultOptions.useTransition, { transform: booleanAttribute });\n        this.gutterDblClickDuration = input(this.defaultOptions.gutterDblClickDuration, {\n            transform: numberAttributeWithFallback(this.defaultOptions.gutterDblClickDuration),\n        });\n        this.gutterClick = output();\n        this.gutterDblClick = output();\n        this.dragStart = output();\n        this.dragEnd = output();\n        this.transitionEnd = output();\n        this.dragProgress$ = this.dragProgressSubject.asObservable();\n        /**\n         * @internal\n         */\n        this._visibleAreas = computed(() => this._areas().filter((area) => area.visible()));\n        this.gridTemplateColumnsStyle = computed(() => this.createGridTemplateColumnsStyle());\n        this.hostClasses = computed(() => createClassesString({\n            [`as-${this.direction()}`]: true,\n            [`as-${this.unit()}`]: true,\n            ['as-disabled']: this.disabled(),\n            ['as-dragging']: this._isDragging(),\n            ['as-transition']: this.useTransition() && !this._isDragging(),\n        }));\n        this.draggedGutterIndex = signal(undefined);\n        /**\n         * @internal\n         */\n        this._isDragging = computed(() => this.draggedGutterIndex() !== undefined);\n        /**\n         * @internal\n         * Should only be used by {@link SplitAreaComponent._internalSize}\n         */\n        this._alignedVisibleAreasSizes = computed(() => this.createAlignedVisibleAreasSize());\n        if (isDevMode()) {\n            // Logs warnings to console when the provided areas sizes are invalid\n            effect(() => {\n                // Special mode when no size input was declared which is a valid mode\n                if (this.unit() === 'percent' && this._visibleAreas().every((area) => area.size() === 'auto')) {\n                    return;\n                }\n                areAreasValid(this._visibleAreas(), this.unit(), true);\n            });\n        }\n        // Responsible for updating grid template style. Must be this way and not based on HostBinding\n        // as change detection for host binding is bound to the parent component and this style\n        // is updated on every mouse move. Doing it this way will prevent change detection cycles in parent.\n        effect(() => {\n            const gridTemplateColumnsStyle = this.gridTemplateColumnsStyle();\n            this.renderer.setStyle(this.elementRef.nativeElement, 'grid-template', gridTemplateColumnsStyle);\n        });\n        this.gutterMouseDownSubject\n            .pipe(filter((context) => !this.customGutter() ||\n            this.customGutter()._canStartDragging(context.mouseDownEvent.target, context.gutterIndex + 1)), switchMap((mouseDownContext) => \n        // As we have gutterClickDeltaPx we can't just start the drag but need to make sure\n        // we are out of the delta pixels. As the delta can be any number we make sure\n        // we always start the drag if we go out of the gutter (delta based on mouse position is larger than gutter).\n        // As moving can start inside the drag and end outside of it we always keep track of the previous event\n        // so once the current is out of the delta size we use the previous one as the drag start baseline.\n        fromMouseMoveEvent(this.document).pipe(startWith(mouseDownContext.mouseDownEvent), pairwise(), skipWhile(([, currMoveEvent]) => gutterEventsEqualWithDelta(mouseDownContext.mouseDownEvent, currMoveEvent, this.gutterClickDeltaPx(), mouseDownContext.gutterElement)), take(1), takeUntil(fromMouseUpEvent(this.document, true)), tap(() => {\n            this.ngZone.run(() => {\n                this.dragStart.emit(this.createDragInteractionEvent(mouseDownContext.gutterIndex));\n                this.draggedGutterIndex.set(mouseDownContext.gutterIndex);\n            });\n        }), map(([prevMouseEvent]) => this.createDragStartContext(prevMouseEvent, mouseDownContext.areaBeforeGutterIndex, mouseDownContext.areaAfterGutterIndex)), switchMap((dragStartContext) => fromMouseMoveEvent(this.document).pipe(tap((moveEvent) => this.mouseDragMove(moveEvent, dragStartContext)), takeUntil(fromMouseUpEvent(this.document, true)), tap({\n            complete: () => this.ngZone.run(() => {\n                this.dragEnd.emit(this.createDragInteractionEvent(this.draggedGutterIndex()));\n                this.draggedGutterIndex.set(undefined);\n            }),\n        }))))), takeUntilDestroyed())\n            .subscribe();\n        fromEvent(this.elementRef.nativeElement, 'transitionend')\n            .pipe(filter((e) => e.propertyName.startsWith('grid-template')), leaveNgZone(), takeUntilDestroyed())\n            .subscribe(() => this.ngZone.run(() => this.transitionEnd.emit(this.createAreaSizes())));\n    }\n    gutterClicked(gutterIndex) {\n        this.ngZone.run(() => this.gutterClick.emit(this.createDragInteractionEvent(gutterIndex)));\n    }\n    gutterDoubleClicked(gutterIndex) {\n        this.ngZone.run(() => this.gutterDblClick.emit(this.createDragInteractionEvent(gutterIndex)));\n    }\n    gutterMouseDown(e, gutterElement, gutterIndex, areaBeforeGutterIndex, areaAfterGutterIndex) {\n        if (this.disabled()) {\n            return;\n        }\n        e.preventDefault();\n        e.stopPropagation();\n        this.gutterMouseDownSubject.next({\n            mouseDownEvent: e,\n            gutterElement,\n            gutterIndex,\n            areaBeforeGutterIndex,\n            areaAfterGutterIndex,\n        });\n    }\n    gutterKeyDown(e, gutterIndex, areaBeforeGutterIndex, areaAfterGutterIndex) {\n        if (this.disabled()) {\n            return;\n        }\n        const pixelsToMove = 50;\n        const pageMoveMultiplier = 10;\n        let xPointOffset = 0;\n        let yPointOffset = 0;\n        if (this.direction() === 'horizontal') {\n            // Even though we are going in the x axis we support page up and down\n            switch (e.key) {\n                case 'ArrowLeft':\n                    xPointOffset -= pixelsToMove;\n                    break;\n                case 'ArrowRight':\n                    xPointOffset += pixelsToMove;\n                    break;\n                case 'PageUp':\n                    if (this.dir() === 'rtl') {\n                        xPointOffset -= pixelsToMove * pageMoveMultiplier;\n                    }\n                    else {\n                        xPointOffset += pixelsToMove * pageMoveMultiplier;\n                    }\n                    break;\n                case 'PageDown':\n                    if (this.dir() === 'rtl') {\n                        xPointOffset += pixelsToMove * pageMoveMultiplier;\n                    }\n                    else {\n                        xPointOffset -= pixelsToMove * pageMoveMultiplier;\n                    }\n                    break;\n                default:\n                    return;\n            }\n        }\n        else {\n            switch (e.key) {\n                case 'ArrowUp':\n                    yPointOffset -= pixelsToMove;\n                    break;\n                case 'ArrowDown':\n                    yPointOffset += pixelsToMove;\n                    break;\n                case 'PageUp':\n                    yPointOffset -= pixelsToMove * pageMoveMultiplier;\n                    break;\n                case 'PageDown':\n                    yPointOffset += pixelsToMove * pageMoveMultiplier;\n                    break;\n                default:\n                    return;\n            }\n        }\n        e.preventDefault();\n        e.stopPropagation();\n        const gutterMidPoint = getPointFromEvent(e);\n        const dragStartContext = this.createDragStartContext(e, areaBeforeGutterIndex, areaAfterGutterIndex);\n        this.ngZone.run(() => {\n            this.dragStart.emit(this.createDragInteractionEvent(gutterIndex));\n            this.draggedGutterIndex.set(gutterIndex);\n        });\n        this.dragMoveToPoint({ x: gutterMidPoint.x + xPointOffset, y: gutterMidPoint.y + yPointOffset }, dragStartContext);\n        this.ngZone.run(() => {\n            this.dragEnd.emit(this.createDragInteractionEvent(gutterIndex));\n            this.draggedGutterIndex.set(undefined);\n        });\n    }\n    getGutterGridStyle(nextAreaIndex) {\n        const gutterNum = nextAreaIndex * 2;\n        const style = `${gutterNum} / ${gutterNum}`;\n        return {\n            ['grid-column']: this.direction() === 'horizontal' ? style : '1',\n            ['grid-row']: this.direction() === 'vertical' ? style : '1',\n        };\n    }\n    getAriaAreaSizeText(area) {\n        const size = area._internalSize();\n        if (size === '*') {\n            return undefined;\n        }\n        return `${size.toFixed(0)} ${this.unit()}`;\n    }\n    getAriaValue(size) {\n        return size === '*' ? undefined : size;\n    }\n    createDragInteractionEvent(gutterIndex) {\n        return {\n            gutterNum: gutterIndex + 1,\n            sizes: this.createAreaSizes(),\n        };\n    }\n    createAreaSizes() {\n        return this._visibleAreas().map((area) => area._internalSize());\n    }\n    createDragStartContext(startEvent, areaBeforeGutterIndex, areaAfterGutterIndex) {\n        const splitBoundingRect = this.elementRef.nativeElement.getBoundingClientRect();\n        const splitSize = this.direction() === 'horizontal' ? splitBoundingRect.width : splitBoundingRect.height;\n        const totalAreasPixelSize = splitSize - (this._visibleAreas().length - 1) * this.gutterSize();\n        // Use the internal size and split size to calculate the pixel size from wildcard and percent areas\n        const areaPixelSizesWithWildcard = this._areas().map((area) => {\n            if (this.unit() === 'pixel') {\n                return area._internalSize();\n            }\n            else {\n                const size = area._internalSize();\n                if (size === '*') {\n                    return size;\n                }\n                return (size / 100) * totalAreasPixelSize;\n            }\n        });\n        const remainingSize = Math.max(0, totalAreasPixelSize - sum(areaPixelSizesWithWildcard, (size) => (size === '*' ? 0 : size)));\n        const areasPixelSizes = areaPixelSizesWithWildcard.map((size) => (size === '*' ? remainingSize : size));\n        return {\n            startEvent,\n            areaBeforeGutterIndex,\n            areaAfterGutterIndex,\n            areasPixelSizes,\n            totalAreasPixelSize,\n            areaIndexToBoundaries: toRecord(this._areas(), (area, index) => {\n                const percentToPixels = (percent) => (percent / 100) * totalAreasPixelSize;\n                const value = this.unit() === 'pixel'\n                    ? {\n                        min: area._normalizedMinSize(),\n                        max: area._normalizedMaxSize(),\n                    }\n                    : {\n                        min: percentToPixels(area._normalizedMinSize()),\n                        max: percentToPixels(area._normalizedMaxSize()),\n                    };\n                return [index.toString(), value];\n            }),\n        };\n    }\n    mouseDragMove(moveEvent, dragStartContext) {\n        moveEvent.preventDefault();\n        moveEvent.stopPropagation();\n        const endPoint = getPointFromEvent(moveEvent);\n        this.dragMoveToPoint(endPoint, dragStartContext);\n    }\n    dragMoveToPoint(endPoint, dragStartContext) {\n        const startPoint = getPointFromEvent(dragStartContext.startEvent);\n        const preDirOffset = this.direction() === 'horizontal' ? endPoint.x - startPoint.x : endPoint.y - startPoint.y;\n        const offset = this.direction() === 'horizontal' && this.dir() === 'rtl' ? -preDirOffset : preDirOffset;\n        const isDraggingForward = offset > 0;\n        // Align offset with gutter step and abs it as we need absolute pixels movement\n        const absSteppedOffset = Math.abs(Math.round(offset / this.gutterStep()) * this.gutterStep());\n        // Copy as we don't want to edit the original array\n        const tempAreasPixelSizes = [...dragStartContext.areasPixelSizes];\n        // As we are going to shuffle the areas order for easier iterations we should work with area indices array\n        // instead of actual area sizes array.\n        const areasIndices = tempAreasPixelSizes.map((_, index) => index);\n        // The two variables below are ordered for iterations with real area indices inside.\n        // We must also remove the invisible ones as we can't expand or shrink them.\n        const areasIndicesBeforeGutter = this.restrictMove()\n            ? [dragStartContext.areaBeforeGutterIndex]\n            : areasIndices\n                .slice(0, dragStartContext.areaBeforeGutterIndex + 1)\n                .filter((index) => this._areas()[index].visible())\n                .reverse();\n        const areasIndicesAfterGutter = this.restrictMove()\n            ? [dragStartContext.areaAfterGutterIndex]\n            : areasIndices.slice(dragStartContext.areaAfterGutterIndex).filter((index) => this._areas()[index].visible());\n        // Based on direction we need to decide which areas are expanding and which are shrinking\n        const potentialAreasIndicesArrToShrink = isDraggingForward ? areasIndicesAfterGutter : areasIndicesBeforeGutter;\n        const potentialAreasIndicesArrToExpand = isDraggingForward ? areasIndicesBeforeGutter : areasIndicesAfterGutter;\n        let remainingPixels = absSteppedOffset;\n        let potentialShrinkArrIndex = 0;\n        let potentialExpandArrIndex = 0;\n        // We gradually run in both expand and shrink direction transferring pixels from the offset.\n        // We stop once no pixels are left or we reached the end of either the expanding areas or the shrinking areas\n        while (remainingPixels !== 0 &&\n            potentialShrinkArrIndex < potentialAreasIndicesArrToShrink.length &&\n            potentialExpandArrIndex < potentialAreasIndicesArrToExpand.length) {\n            const areaIndexToShrink = potentialAreasIndicesArrToShrink[potentialShrinkArrIndex];\n            const areaIndexToExpand = potentialAreasIndicesArrToExpand[potentialExpandArrIndex];\n            const areaToShrinkSize = tempAreasPixelSizes[areaIndexToShrink];\n            const areaToExpandSize = tempAreasPixelSizes[areaIndexToExpand];\n            const areaToShrinkMinSize = dragStartContext.areaIndexToBoundaries[areaIndexToShrink].min;\n            const areaToExpandMaxSize = dragStartContext.areaIndexToBoundaries[areaIndexToExpand].max;\n            // We can only transfer pixels based on the shrinking area min size and the expanding area max size\n            // to avoid overflow. If any pixels left they will be handled by the next area in the next `while` iteration\n            const maxPixelsToShrink = areaToShrinkSize - areaToShrinkMinSize;\n            const maxPixelsToExpand = areaToExpandMaxSize - areaToExpandSize;\n            const pixelsToTransfer = Math.min(maxPixelsToShrink, maxPixelsToExpand, remainingPixels);\n            // Actual pixels transfer\n            tempAreasPixelSizes[areaIndexToShrink] -= pixelsToTransfer;\n            tempAreasPixelSizes[areaIndexToExpand] += pixelsToTransfer;\n            remainingPixels -= pixelsToTransfer;\n            // Once min threshold reached we need to move to the next area in turn\n            if (tempAreasPixelSizes[areaIndexToShrink] === areaToShrinkMinSize) {\n                potentialShrinkArrIndex++;\n            }\n            // Once max threshold reached we need to move to the next area in turn\n            if (tempAreasPixelSizes[areaIndexToExpand] === areaToExpandMaxSize) {\n                potentialExpandArrIndex++;\n            }\n        }\n        this._areas().forEach((area, index) => {\n            // No need to update wildcard size\n            if (area._internalSize() === '*') {\n                return;\n            }\n            if (this.unit() === 'pixel') {\n                area._internalSize.set(tempAreasPixelSizes[index]);\n            }\n            else {\n                const percentSize = (tempAreasPixelSizes[index] / dragStartContext.totalAreasPixelSize) * 100;\n                // Fix javascript only working with float numbers which are inaccurate compared to decimals\n                area._internalSize.set(parseFloat(percentSize.toFixed(10)));\n            }\n        });\n        this.dragProgressSubject.next(this.createDragInteractionEvent(this.draggedGutterIndex()));\n    }\n    createGridTemplateColumnsStyle() {\n        const columns = [];\n        const sumNonWildcardSizes = sum(this._visibleAreas(), (area) => {\n            const size = area._internalSize();\n            return size === '*' ? 0 : size;\n        });\n        const visibleAreasCount = this._visibleAreas().length;\n        let visitedVisibleAreas = 0;\n        this._areas().forEach((area, index, areas) => {\n            const unit = this.unit();\n            const areaSize = area._internalSize();\n            // Add area size column\n            if (!area.visible()) {\n                columns.push(unit === 'percent' || areaSize === '*' ? '0fr' : '0px');\n            }\n            else {\n                if (unit === 'pixel') {\n                    const columnValue = areaSize === '*' ? '1fr' : `${areaSize}px`;\n                    columns.push(columnValue);\n                }\n                else {\n                    const percentSize = areaSize === '*' ? 100 - sumNonWildcardSizes : areaSize;\n                    const columnValue = `${percentSize}fr`;\n                    columns.push(columnValue);\n                }\n                visitedVisibleAreas++;\n            }\n            const isLastArea = index === areas.length - 1;\n            if (isLastArea) {\n                return;\n            }\n            const remainingVisibleAreas = visibleAreasCount - visitedVisibleAreas;\n            // Only add gutter with size if this area is visible and there are more visible areas after this one\n            // to avoid ghost gutters\n            if (area.visible() && remainingVisibleAreas > 0) {\n                columns.push(`${this.gutterSize()}px`);\n            }\n            else {\n                columns.push('0px');\n            }\n        });\n        return this.direction() === 'horizontal' ? `1fr / ${columns.join(' ')}` : `${columns.join(' ')} / 1fr`;\n    }\n    createAlignedVisibleAreasSize() {\n        const visibleAreasSizes = this._visibleAreas().map((area) => {\n            const size = area.size();\n            return size === 'auto' ? '*' : size;\n        });\n        const isValid = areAreasValid(this._visibleAreas(), this.unit(), false);\n        if (isValid) {\n            return visibleAreasSizes;\n        }\n        const unit = this.unit();\n        if (unit === 'percent') {\n            // Distribute sizes equally\n            const defaultPercentSize = 100 / visibleAreasSizes.length;\n            return visibleAreasSizes.map(() => defaultPercentSize);\n        }\n        if (unit === 'pixel') {\n            // Make sure only one wildcard area\n            const wildcardAreas = visibleAreasSizes.filter((areaSize) => areaSize === '*');\n            if (wildcardAreas.length === 0) {\n                return ['*', ...visibleAreasSizes.slice(1)];\n            }\n            else {\n                const firstWildcardIndex = visibleAreasSizes.findIndex((areaSize) => areaSize === '*');\n                const defaultPxSize = 100;\n                return visibleAreasSizes.map((areaSize, index) => index === firstWildcardIndex || areaSize !== '*' ? areaSize : defaultPxSize);\n            }\n        }\n        return assertUnreachable(unit, 'SplitUnit');\n    }\n    /** @nocollapse */ static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.0.5\", ngImport: i0, type: SplitComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    /** @nocollapse */ static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"19.0.5\", type: SplitComponent, isStandalone: true, selector: \"as-split\", inputs: { gutterSize: { classPropertyName: \"gutterSize\", publicName: \"gutterSize\", isSignal: true, isRequired: false, transformFunction: null }, gutterStep: { classPropertyName: \"gutterStep\", publicName: \"gutterStep\", isSignal: true, isRequired: false, transformFunction: null }, disabled: { classPropertyName: \"disabled\", publicName: \"disabled\", isSignal: true, isRequired: false, transformFunction: null }, gutterClickDeltaPx: { classPropertyName: \"gutterClickDeltaPx\", publicName: \"gutterClickDeltaPx\", isSignal: true, isRequired: false, transformFunction: null }, direction: { classPropertyName: \"direction\", publicName: \"direction\", isSignal: true, isRequired: false, transformFunction: null }, dir: { classPropertyName: \"dir\", publicName: \"dir\", isSignal: true, isRequired: false, transformFunction: null }, unit: { classPropertyName: \"unit\", publicName: \"unit\", isSignal: true, isRequired: false, transformFunction: null }, gutterAriaLabel: { classPropertyName: \"gutterAriaLabel\", publicName: \"gutterAriaLabel\", isSignal: true, isRequired: false, transformFunction: null }, restrictMove: { classPropertyName: \"restrictMove\", publicName: \"restrictMove\", isSignal: true, isRequired: false, transformFunction: null }, useTransition: { classPropertyName: \"useTransition\", publicName: \"useTransition\", isSignal: true, isRequired: false, transformFunction: null }, gutterDblClickDuration: { classPropertyName: \"gutterDblClickDuration\", publicName: \"gutterDblClickDuration\", isSignal: true, isRequired: false, transformFunction: null } }, outputs: { gutterClick: \"gutterClick\", gutterDblClick: \"gutterDblClick\", dragStart: \"dragStart\", dragEnd: \"dragEnd\", transitionEnd: \"transitionEnd\" }, host: { properties: { \"class\": \"this.hostClassesBinding\", \"dir\": \"this.hostDirBinding\" } }, queries: [{ propertyName: \"_areas\", predicate: SPLIT_AREA_CONTRACT, isSignal: true }, { propertyName: \"customGutter\", first: true, predicate: SplitGutterDirective, descendants: true, isSignal: true }], exportAs: [\"asSplit\"], ngImport: i0, template: \"<ng-content></ng-content>\\n@for (area of _areas(); track area) {\\n  @if (!$last) {\\n    <div\\n      #gutter\\n      class=\\\"as-split-gutter\\\"\\n      role=\\\"separator\\\"\\n      tabindex=\\\"0\\\"\\n      [attr.aria-label]=\\\"gutterAriaLabel()\\\"\\n      [attr.aria-orientation]=\\\"direction()\\\"\\n      [attr.aria-valuemin]=\\\"getAriaValue(area.minSize())\\\"\\n      [attr.aria-valuemax]=\\\"getAriaValue(area.maxSize())\\\"\\n      [attr.aria-valuenow]=\\\"getAriaValue(area._internalSize())\\\"\\n      [attr.aria-valuetext]=\\\"getAriaAreaSizeText(area)\\\"\\n      [ngStyle]=\\\"getGutterGridStyle($index + 1)\\\"\\n      [class.as-dragged]=\\\"draggedGutterIndex() === $index\\\"\\n      asSplitCustomEventsBehavior\\n      [asSplitCustomMultiClickThreshold]=\\\"gutterDblClickDuration()\\\"\\n      [asSplitCustomClickDeltaInPx]=\\\"gutterClickDeltaPx()\\\"\\n      (asSplitCustomClick)=\\\"gutterClicked($index)\\\"\\n      (asSplitCustomDblClick)=\\\"gutterDoubleClicked($index)\\\"\\n      (asSplitCustomMouseDown)=\\\"gutterMouseDown($event, gutter, $index, $index, $index + 1)\\\"\\n      (asSplitCustomKeyDown)=\\\"gutterKeyDown($event, $index, $index, $index + 1)\\\"\\n    >\\n      @if (customGutter()?.template) {\\n        <ng-container *asSplitGutterDynamicInjector=\\\"$index + 1; let injector\\\">\\n          <ng-container\\n            *ngTemplateOutlet=\\\"\\n              customGutter().template;\\n              context: {\\n                areaBefore: area,\\n                areaAfter: _areas()[$index + 1],\\n                gutterNum: $index + 1,\\n                first: $first,\\n                last: $index === _areas().length - 2,\\n                isDragged: draggedGutterIndex() === $index\\n              };\\n              injector: injector\\n            \\\"\\n          ></ng-container>\\n        </ng-container>\\n      } @else {\\n        <div class=\\\"as-split-gutter-icon\\\"></div>\\n      }\\n    </div>\\n  }\\n}\\n\", styles: [\"@property --as-gutter-background-color{syntax: \\\"<color>\\\"; inherits: true; initial-value: #eeeeee;}@property --as-gutter-icon-horizontal{syntax: \\\"<url>\\\"; inherits: true; initial-value: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAeCAYAAADkftS9AAAAIklEQVQoU2M4c+bMfxAGAgYYmwGrIIiDjrELjpo5aiZeMwF+yNnOs5KSvgAAAABJRU5ErkJggg==);}@property --as-gutter-icon-vertical{syntax: \\\"<url>\\\"; inherits: true; initial-value: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAFCAMAAABl/6zIAAAABlBMVEUAAADMzMzIT8AyAAAAAXRSTlMAQObYZgAAABRJREFUeAFjYGRkwIMJSeMHlBkOABP7AEGzSuPKAAAAAElFTkSuQmCC);}@property --as-gutter-icon-disabled{syntax: \\\"<url>\\\"; inherits: true; initial-value: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAeCAYAAADkftS9AAAAIklEQVQoU2M4c+bMfxAGAgYYmwGrIIiDjrELjpo5aiZeMwF+yNnOs5KSvgAAAABJRU5ErkJggg==);}@property --as-transition-duration{syntax: \\\"<time>\\\"; inherits: true; initial-value: .3s;}@property --as-gutter-disabled-cursor{syntax: \\\"*\\\"; inherits: true; initial-value: default;}:host{--_as-gutter-background-color: var(--as-gutter-background-color, #eeeeee);--_as-gutter-icon-horizontal: var( --as-gutter-icon-horizontal, url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAeCAYAAADkftS9AAAAIklEQVQoU2M4c+bMfxAGAgYYmwGrIIiDjrELjpo5aiZeMwF+yNnOs5KSvgAAAABJRU5ErkJggg==) );--_as-gutter-icon-vertical: var( --as-gutter-icon-vertical, url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAFCAMAAABl/6zIAAAABlBMVEUAAADMzMzIT8AyAAAAAXRSTlMAQObYZgAAABRJREFUeAFjYGRkwIMJSeMHlBkOABP7AEGzSuPKAAAAAElFTkSuQmCC) );--_as-gutter-icon-disabled: var( --as-gutter-icon-disabled, url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAeCAYAAADkftS9AAAAIklEQVQoU2M4c+bMfxAGAgYYmwGrIIiDjrELjpo5aiZeMwF+yNnOs5KSvgAAAABJRU5ErkJggg==) );--_as-transition-duration: var(--as-transition-duration, .3s);--_as-gutter-disabled-cursor: var(--as-gutter-disabled-cursor, default)}:host{display:grid;overflow:hidden;height:100%;width:100%}:host(.as-transition){transition:grid-template var(--_as-transition-duration)}.as-split-gutter{background-color:var(--_as-gutter-background-color);display:flex;align-items:center;justify-content:center;touch-action:none}:host(.as-horizontal)>.as-split-gutter{cursor:col-resize;height:100%}:host(.as-vertical)>.as-split-gutter{cursor:row-resize;width:100%}:host(.as-disabled)>.as-split-gutter{cursor:var(--_as-gutter-disabled-cursor)}.as-split-gutter-icon{width:100%;height:100%;background-position:center center;background-repeat:no-repeat}:host(.as-horizontal)>.as-split-gutter>.as-split-gutter-icon{background-image:var(--_as-gutter-icon-horizontal)}:host(.as-vertical)>.as-split-gutter>.as-split-gutter-icon{background-image:var(--_as-gutter-icon-vertical)}:host(.as-disabled)>.as-split-gutter>.as-split-gutter-icon{background-image:var(--_as-gutter-icon-disabled)}\\n\"], dependencies: [{ kind: \"directive\", type: NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"directive\", type: SplitCustomEventsBehaviorDirective, selector: \"[asSplitCustomEventsBehavior]\", inputs: [\"asSplitCustomMultiClickThreshold\", \"asSplitCustomClickDeltaInPx\"], outputs: [\"asSplitCustomMouseDown\", \"asSplitCustomClick\", \"asSplitCustomDblClick\", \"asSplitCustomKeyDown\"] }, { kind: \"directive\", type: SplitGutterDynamicInjectorDirective, selector: \"[asSplitGutterDynamicInjector]\", inputs: [\"asSplitGutterDynamicInjector\"] }, { kind: \"directive\", type: NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.0.5\", ngImport: i0, type: SplitComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'as-split', imports: [NgStyle, SplitCustomEventsBehaviorDirective, SplitGutterDynamicInjectorDirective, NgTemplateOutlet], exportAs: 'asSplit', changeDetection: ChangeDetectionStrategy.OnPush, template: \"<ng-content></ng-content>\\n@for (area of _areas(); track area) {\\n  @if (!$last) {\\n    <div\\n      #gutter\\n      class=\\\"as-split-gutter\\\"\\n      role=\\\"separator\\\"\\n      tabindex=\\\"0\\\"\\n      [attr.aria-label]=\\\"gutterAriaLabel()\\\"\\n      [attr.aria-orientation]=\\\"direction()\\\"\\n      [attr.aria-valuemin]=\\\"getAriaValue(area.minSize())\\\"\\n      [attr.aria-valuemax]=\\\"getAriaValue(area.maxSize())\\\"\\n      [attr.aria-valuenow]=\\\"getAriaValue(area._internalSize())\\\"\\n      [attr.aria-valuetext]=\\\"getAriaAreaSizeText(area)\\\"\\n      [ngStyle]=\\\"getGutterGridStyle($index + 1)\\\"\\n      [class.as-dragged]=\\\"draggedGutterIndex() === $index\\\"\\n      asSplitCustomEventsBehavior\\n      [asSplitCustomMultiClickThreshold]=\\\"gutterDblClickDuration()\\\"\\n      [asSplitCustomClickDeltaInPx]=\\\"gutterClickDeltaPx()\\\"\\n      (asSplitCustomClick)=\\\"gutterClicked($index)\\\"\\n      (asSplitCustomDblClick)=\\\"gutterDoubleClicked($index)\\\"\\n      (asSplitCustomMouseDown)=\\\"gutterMouseDown($event, gutter, $index, $index, $index + 1)\\\"\\n      (asSplitCustomKeyDown)=\\\"gutterKeyDown($event, $index, $index, $index + 1)\\\"\\n    >\\n      @if (customGutter()?.template) {\\n        <ng-container *asSplitGutterDynamicInjector=\\\"$index + 1; let injector\\\">\\n          <ng-container\\n            *ngTemplateOutlet=\\\"\\n              customGutter().template;\\n              context: {\\n                areaBefore: area,\\n                areaAfter: _areas()[$index + 1],\\n                gutterNum: $index + 1,\\n                first: $first,\\n                last: $index === _areas().length - 2,\\n                isDragged: draggedGutterIndex() === $index\\n              };\\n              injector: injector\\n            \\\"\\n          ></ng-container>\\n        </ng-container>\\n      } @else {\\n        <div class=\\\"as-split-gutter-icon\\\"></div>\\n      }\\n    </div>\\n  }\\n}\\n\", styles: [\"@property --as-gutter-background-color{syntax: \\\"<color>\\\"; inherits: true; initial-value: #eeeeee;}@property --as-gutter-icon-horizontal{syntax: \\\"<url>\\\"; inherits: true; initial-value: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAeCAYAAADkftS9AAAAIklEQVQoU2M4c+bMfxAGAgYYmwGrIIiDjrELjpo5aiZeMwF+yNnOs5KSvgAAAABJRU5ErkJggg==);}@property --as-gutter-icon-vertical{syntax: \\\"<url>\\\"; inherits: true; initial-value: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAFCAMAAABl/6zIAAAABlBMVEUAAADMzMzIT8AyAAAAAXRSTlMAQObYZgAAABRJREFUeAFjYGRkwIMJSeMHlBkOABP7AEGzSuPKAAAAAElFTkSuQmCC);}@property --as-gutter-icon-disabled{syntax: \\\"<url>\\\"; inherits: true; initial-value: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAeCAYAAADkftS9AAAAIklEQVQoU2M4c+bMfxAGAgYYmwGrIIiDjrELjpo5aiZeMwF+yNnOs5KSvgAAAABJRU5ErkJggg==);}@property --as-transition-duration{syntax: \\\"<time>\\\"; inherits: true; initial-value: .3s;}@property --as-gutter-disabled-cursor{syntax: \\\"*\\\"; inherits: true; initial-value: default;}:host{--_as-gutter-background-color: var(--as-gutter-background-color, #eeeeee);--_as-gutter-icon-horizontal: var( --as-gutter-icon-horizontal, url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAeCAYAAADkftS9AAAAIklEQVQoU2M4c+bMfxAGAgYYmwGrIIiDjrELjpo5aiZeMwF+yNnOs5KSvgAAAABJRU5ErkJggg==) );--_as-gutter-icon-vertical: var( --as-gutter-icon-vertical, url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAFCAMAAABl/6zIAAAABlBMVEUAAADMzMzIT8AyAAAAAXRSTlMAQObYZgAAABRJREFUeAFjYGRkwIMJSeMHlBkOABP7AEGzSuPKAAAAAElFTkSuQmCC) );--_as-gutter-icon-disabled: var( --as-gutter-icon-disabled, url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAeCAYAAADkftS9AAAAIklEQVQoU2M4c+bMfxAGAgYYmwGrIIiDjrELjpo5aiZeMwF+yNnOs5KSvgAAAABJRU5ErkJggg==) );--_as-transition-duration: var(--as-transition-duration, .3s);--_as-gutter-disabled-cursor: var(--as-gutter-disabled-cursor, default)}:host{display:grid;overflow:hidden;height:100%;width:100%}:host(.as-transition){transition:grid-template var(--_as-transition-duration)}.as-split-gutter{background-color:var(--_as-gutter-background-color);display:flex;align-items:center;justify-content:center;touch-action:none}:host(.as-horizontal)>.as-split-gutter{cursor:col-resize;height:100%}:host(.as-vertical)>.as-split-gutter{cursor:row-resize;width:100%}:host(.as-disabled)>.as-split-gutter{cursor:var(--_as-gutter-disabled-cursor)}.as-split-gutter-icon{width:100%;height:100%;background-position:center center;background-repeat:no-repeat}:host(.as-horizontal)>.as-split-gutter>.as-split-gutter-icon{background-image:var(--_as-gutter-icon-horizontal)}:host(.as-vertical)>.as-split-gutter>.as-split-gutter-icon{background-image:var(--_as-gutter-icon-vertical)}:host(.as-disabled)>.as-split-gutter>.as-split-gutter-icon{background-image:var(--_as-gutter-icon-disabled)}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { hostClassesBinding: [{\n                type: HostBinding,\n                args: ['class']\n            }], hostDirBinding: [{\n                type: HostBinding,\n                args: ['dir']\n            }] } });\n\nconst internalAreaSizeTransform = (areaSize) => areaSize === undefined || areaSize === null || areaSize === '*' ? '*' : +areaSize;\nconst areaSizeTransform = (areaSize) => internalAreaSizeTransform(areaSize);\nconst boundaryAreaSizeTransform = (areaSize) => internalAreaSizeTransform(areaSize);\n\nclass SplitAreaComponent {\n    constructor() {\n        this.split = inject(SplitComponent);\n        this.size = input('auto', { transform: areaSizeTransform });\n        this.minSize = input('*', { transform: boundaryAreaSizeTransform });\n        this.maxSize = input('*', { transform: boundaryAreaSizeTransform });\n        this.lockSize = input(false, { transform: booleanAttribute });\n        this.visible = input(true, { transform: booleanAttribute });\n        /**\n         * @internal\n         */\n        this._internalSize = mirrorSignal(\n        // As size is an input and we can change the size without the outside\n        // listening to the change we need an intermediate writeable signal\n        computed(() => {\n            if (!this.visible()) {\n                return 0;\n            }\n            const visibleIndex = this.split._visibleAreas().findIndex((area) => area === this);\n            return this.split._alignedVisibleAreasSizes()[visibleIndex];\n        }));\n        /**\n         * @internal\n         */\n        this._normalizedMinSize = computed(() => this.normalizeMinSize());\n        /**\n         * @internal\n         */\n        this._normalizedMaxSize = computed(() => this.normalizeMaxSize());\n        this.index = computed(() => this.split._areas().findIndex((area) => area === this));\n        this.gridAreaNum = computed(() => this.index() * 2 + 1);\n        this.hostClasses = computed(() => createClassesString({\n            ['as-split-area']: true,\n            ['as-min']: this.visible() && this._internalSize() === this._normalizedMinSize(),\n            ['as-max']: this.visible() && this._internalSize() === this._normalizedMaxSize(),\n            ['as-hidden']: !this.visible(),\n        }));\n    }\n    get hostClassesBinding() {\n        return this.hostClasses();\n    }\n    get hostGridColumnStyleBinding() {\n        return this.split.direction() === 'horizontal' ? `${this.gridAreaNum()} / ${this.gridAreaNum()}` : undefined;\n    }\n    get hostGridRowStyleBinding() {\n        return this.split.direction() === 'vertical' ? `${this.gridAreaNum()} / ${this.gridAreaNum()}` : undefined;\n    }\n    get hostPositionStyleBinding() {\n        return this.split._isDragging() ? 'relative' : undefined;\n    }\n    normalizeMinSize() {\n        const defaultMinSize = 0;\n        if (!this.visible()) {\n            return defaultMinSize;\n        }\n        const minSize = this.normalizeSizeBoundary(this.minSize, defaultMinSize);\n        const size = this.size();\n        if (size !== '*' && size !== 'auto' && size < minSize) {\n            if (isDevMode()) {\n                console.warn('as-split: size cannot be smaller than minSize');\n            }\n            return defaultMinSize;\n        }\n        return minSize;\n    }\n    normalizeMaxSize() {\n        const defaultMaxSize = Infinity;\n        if (!this.visible()) {\n            return defaultMaxSize;\n        }\n        const maxSize = this.normalizeSizeBoundary(this.maxSize, defaultMaxSize);\n        const size = this.size();\n        if (size !== '*' && size !== 'auto' && size > maxSize) {\n            if (isDevMode()) {\n                console.warn('as-split: size cannot be larger than maxSize');\n            }\n            return defaultMaxSize;\n        }\n        return maxSize;\n    }\n    normalizeSizeBoundary(sizeBoundarySignal, defaultBoundarySize) {\n        const size = this.size();\n        const lockSize = this.lockSize();\n        const boundarySize = sizeBoundarySignal();\n        if (lockSize) {\n            if (isDevMode() && boundarySize !== '*') {\n                console.warn('as-split: lockSize overwrites maxSize/minSize');\n            }\n            if (size === '*' || size === 'auto') {\n                if (isDevMode()) {\n                    console.warn(`as-split: lockSize isn't supported on area with * size or without size`);\n                }\n                return defaultBoundarySize;\n            }\n            return size;\n        }\n        if (boundarySize === '*') {\n            return defaultBoundarySize;\n        }\n        if (size === '*' || size === 'auto') {\n            if (isDevMode()) {\n                console.warn('as-split: maxSize/minSize not allowed on * or without size');\n            }\n            return defaultBoundarySize;\n        }\n        return boundarySize;\n    }\n    /** @nocollapse */ static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.0.5\", ngImport: i0, type: SplitAreaComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    /** @nocollapse */ static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"19.0.5\", type: SplitAreaComponent, isStandalone: true, selector: \"as-split-area\", inputs: { size: { classPropertyName: \"size\", publicName: \"size\", isSignal: true, isRequired: false, transformFunction: null }, minSize: { classPropertyName: \"minSize\", publicName: \"minSize\", isSignal: true, isRequired: false, transformFunction: null }, maxSize: { classPropertyName: \"maxSize\", publicName: \"maxSize\", isSignal: true, isRequired: false, transformFunction: null }, lockSize: { classPropertyName: \"lockSize\", publicName: \"lockSize\", isSignal: true, isRequired: false, transformFunction: null }, visible: { classPropertyName: \"visible\", publicName: \"visible\", isSignal: true, isRequired: false, transformFunction: null } }, host: { properties: { \"class\": \"this.hostClassesBinding\", \"style.grid-column\": \"this.hostGridColumnStyleBinding\", \"style.grid-row\": \"this.hostGridRowStyleBinding\", \"style.position\": \"this.hostPositionStyleBinding\" } }, providers: [\n            {\n                provide: SPLIT_AREA_CONTRACT,\n                useExisting: SplitAreaComponent,\n            },\n        ], exportAs: [\"asSplitArea\"], ngImport: i0, template: \"<ng-content></ng-content>\\n@if (split._isDragging()) {\\n  <div class=\\\"as-iframe-fix\\\"></div>\\n}\\n\", styles: [\":host{overflow-x:hidden;overflow-y:auto}.as-horizontal>:host{height:100%}.as-vertical>:host{width:100%}.as-iframe-fix{position:absolute;top:0;left:0;width:100%;height:100%}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.0.5\", ngImport: i0, type: SplitAreaComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'as-split-area', standalone: true, exportAs: 'asSplitArea', providers: [\n                        {\n                            provide: SPLIT_AREA_CONTRACT,\n                            useExisting: SplitAreaComponent,\n                        },\n                    ], changeDetection: ChangeDetectionStrategy.OnPush, template: \"<ng-content></ng-content>\\n@if (split._isDragging()) {\\n  <div class=\\\"as-iframe-fix\\\"></div>\\n}\\n\", styles: [\":host{overflow-x:hidden;overflow-y:auto}.as-horizontal>:host{height:100%}.as-vertical>:host{width:100%}.as-iframe-fix{position:absolute;top:0;left:0;width:100%;height:100%}\\n\"] }]\n        }], propDecorators: { hostClassesBinding: [{\n                type: HostBinding,\n                args: ['class']\n            }], hostGridColumnStyleBinding: [{\n                type: HostBinding,\n                args: ['style.grid-column']\n            }], hostGridRowStyleBinding: [{\n                type: HostBinding,\n                args: ['style.grid-row']\n            }], hostPositionStyleBinding: [{\n                type: HostBinding,\n                args: ['style.position']\n            }] } });\n\nclass AngularSplitModule {\n    /** @nocollapse */ static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.0.5\", ngImport: i0, type: AngularSplitModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    /** @nocollapse */ static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.0.5\", ngImport: i0, type: AngularSplitModule, imports: [SplitComponent,\n            SplitAreaComponent,\n            SplitGutterDirective,\n            SplitGutterDragHandleDirective,\n            SplitGutterExcludeFromDragDirective], exports: [SplitComponent,\n            SplitAreaComponent,\n            SplitGutterDirective,\n            SplitGutterDragHandleDirective,\n            SplitGutterExcludeFromDragDirective] }); }\n    /** @nocollapse */ static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.0.5\", ngImport: i0, type: AngularSplitModule }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.0.5\", ngImport: i0, type: AngularSplitModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        SplitComponent,\n                        SplitAreaComponent,\n                        SplitGutterDirective,\n                        SplitGutterDragHandleDirective,\n                        SplitGutterExcludeFromDragDirective,\n                    ],\n                    exports: [\n                        SplitComponent,\n                        SplitAreaComponent,\n                        SplitGutterDirective,\n                        SplitGutterDragHandleDirective,\n                        SplitGutterExcludeFromDragDirective,\n                    ],\n                }]\n        }] });\n\n/*\n * Public API Surface of angular-split\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AngularSplitModule, SplitAreaComponent, SplitComponent, SplitGutterDirective, SplitGutterDragHandleDirective, SplitGutterExcludeFromDragDirective, provideAngularSplitOptions };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,MAAM,EAAEC,WAAW,EAAEC,SAAS,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,MAAM,EAAEC,eAAe,EAAEC,KAAK,EAAEC,MAAM,EAAEC,gBAAgB,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,eAAe,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,WAAW,EAAEC,QAAQ,QAAQ,eAAe;AACrU,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,SAASC,KAAK,EAAEC,SAAS,EAAEC,MAAM,EAAEC,UAAU,EAAEC,SAAS,EAAEC,IAAI,EAAEC,GAAG,EAAEC,SAAS,EAAEC,GAAG,EAAEC,YAAY,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,EAAE,EAAEC,KAAK,EAAEC,MAAM,EAAEC,OAAO,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,MAAM;AAC3L,SAASC,QAAQ,EAAEC,OAAO,EAAEC,gBAAgB,QAAQ,iBAAiB;AAAC,MAAAC,GAAA;AAAA,MAAAC,GAAA,GAAAA,CAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA;EAAAC,UAAA,EAAAN,EAAA;EAAAO,SAAA,EAAAN,EAAA;EAAAO,SAAA,EAAAN,EAAA;EAAAO,KAAA,EAAAN,EAAA;EAAAO,IAAA,EAAAN,EAAA;EAAAO,SAAA,EAAAN;AAAA;AAAA,SAAAO,wFAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA0FiD/D,EAAE,CAAAiE,kBAAA,EAysByuH,CAAC;EAAA;AAAA;AAAA,SAAAC,yEAAAH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAzsB5uH/D,EAAE,CAAAmE,uBAAA,EAysB4vG,CAAC;IAzsB/vGnE,EAAE,CAAAoE,UAAA,IAAAN,uFAAA,yBAysB0tH,CAAC;IAzsB7tH9D,EAAE,CAAAqE,qBAAA;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAO,WAAA,GAAAN,GAAA,CAAAO,SAAA;IAAA,MAAAC,MAAA,GAAFxE,EAAE,CAAAyE,aAAA;IAAA,MAAAC,OAAA,GAAAF,MAAA,CAAAD,SAAA;IAAA,MAAAI,SAAA,GAAAH,MAAA,CAAAI,MAAA;IAAA,MAAAC,YAAA,GAAAL,MAAA,CAAAI,MAAA;IAAA,MAAAE,MAAA,GAAF9E,EAAE,CAAAyE,aAAA;IAAFzE,EAAE,CAAA+E,SAAA,CAysB63G,CAAC;IAzsBh4G/E,EAAE,CAAAgF,UAAA,qBAAAF,MAAA,CAAAG,YAAA,GAAAC,QAysB63G,CAAC,4BAzsBh4GlF,EAAE,CAAAmF,eAAA,IAAAlC,GAAA,EAAAyB,OAAA,EAAAI,MAAA,CAAAM,MAAA,GAAAT,SAAA,OAAAA,SAAA,MAAAE,YAAA,QAAAF,SAAA,KAAAG,MAAA,CAAAM,MAAA,GAAAC,MAAA,MAAAP,MAAA,CAAAQ,kBAAA,OAAAX,SAAA,CAysBirH,CAAC,6BAAAL,WAAiB,CAAC;EAAA;AAAA;AAAA,SAAAiB,0DAAAxB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAzsBtsH/D,EAAE,CAAAoE,UAAA,IAAAF,wEAAA,0BAysB4vG,CAAC;EAAA;EAAA,IAAAH,EAAA;IAAA,MAAAY,SAAA,GAzsB/vG3E,EAAE,CAAAyE,aAAA,IAAAG,MAAA;IAAF5E,EAAE,CAAAgF,UAAA,iCAAAL,SAAA,IAysB6uG,CAAC;EAAA;AAAA;AAAA,SAAAa,0DAAAzB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAzsBhvG/D,EAAE,CAAAyF,SAAA,YAysBu0H,CAAC;EAAA;AAAA;AAAA,SAAAC,4CAAA3B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA4B,GAAA,GAzsB10H3F,EAAE,CAAA4F,gBAAA;IAAF5F,EAAE,CAAA6F,cAAA,eAysBioG,CAAC;IAzsBpoG7F,EAAE,CAAA8F,UAAA,gCAAAC,8EAAA;MAAF/F,EAAE,CAAAgG,aAAA,CAAAL,GAAA;MAAA,MAAAhB,SAAA,GAAF3E,EAAE,CAAAyE,aAAA,GAAAG,MAAA;MAAA,MAAAE,MAAA,GAAF9E,EAAE,CAAAyE,aAAA;MAAA,OAAFzE,EAAE,CAAAiG,WAAA,CAysBi3FnB,MAAA,CAAAoB,aAAA,CAAAvB,SAAoB,CAAC;IAAA,CAAC,CAAC,mCAAAwB,iFAAA;MAzsB14FnG,EAAE,CAAAgG,aAAA,CAAAL,GAAA;MAAA,MAAAhB,SAAA,GAAF3E,EAAE,CAAAyE,aAAA,GAAAG,MAAA;MAAA,MAAAE,MAAA,GAAF9E,EAAE,CAAAyE,aAAA;MAAA,OAAFzE,EAAE,CAAAiG,WAAA,CAysB06FnB,MAAA,CAAAsB,mBAAA,CAAAzB,SAA0B,CAAC;IAAA,CAAC,CAAC,oCAAA0B,kFAAAC,MAAA;MAzsBz8FtG,EAAE,CAAAgG,aAAA,CAAAL,GAAA;MAAA,MAAAY,SAAA,GAAFvG,EAAE,CAAAwG,WAAA;MAAA,MAAA7B,SAAA,GAAF3E,EAAE,CAAAyE,aAAA,GAAAG,MAAA;MAAA,MAAAE,MAAA,GAAF9E,EAAE,CAAAyE,aAAA;MAAA,OAAFzE,EAAE,CAAAiG,WAAA,CAysB0+FnB,MAAA,CAAA2B,eAAA,CAAAH,MAAA,EAAAC,SAAA,EAAA5B,SAAA,EAAAA,SAAA,EAAAA,SAAA,GAAyD,CAAC,CAAC;IAAA,CAAC,CAAC,kCAAA+B,gFAAAJ,MAAA;MAzsBziGtG,EAAE,CAAAgG,aAAA,CAAAL,GAAA;MAAA,MAAAhB,SAAA,GAAF3E,EAAE,CAAAyE,aAAA,GAAAG,MAAA;MAAA,MAAAE,MAAA,GAAF9E,EAAE,CAAAyE,aAAA;MAAA,OAAFzE,EAAE,CAAAiG,WAAA,CAysBwkGnB,MAAA,CAAA6B,aAAA,CAAAL,MAAA,EAAA3B,SAAA,EAAAA,SAAA,EAAAA,SAAA,GAA+C,CAAC,CAAC;IAAA,CAAC,CAAC;IAzsB7nG3E,EAAE,CAAAoE,UAAA,IAAAmB,yDAAA,sBAysByqG,CAAC,IAAAC,yDAAA,MAAymB,CAAC;IAzsBtxHxF,EAAE,CAAA4G,YAAA,CAysB41H,CAAC;EAAA;EAAA,IAAA7C,EAAA;IAAA,IAAA8C,QAAA;IAAA,MAAArC,MAAA,GAzsB/1HxE,EAAE,CAAAyE,aAAA;IAAA,MAAAC,OAAA,GAAAF,MAAA,CAAAD,SAAA;IAAA,MAAAI,SAAA,GAAAH,MAAA,CAAAI,MAAA;IAAA,MAAAE,MAAA,GAAF9E,EAAE,CAAAyE,aAAA;IAAFzE,EAAE,CAAA8G,WAAA,eAAAhC,MAAA,CAAAQ,kBAAA,OAAAX,SAysByqF,CAAC;IAzsB5qF3E,EAAE,CAAAgF,UAAA,YAAAF,MAAA,CAAAiC,kBAAA,CAAApC,SAAA,KAysB2mF,CAAC,qCAAAG,MAAA,CAAAkC,sBAAA,EAAuK,CAAC,gCAAAlC,MAAA,CAAAmC,kBAAA,EAA6D,CAAC;IAzsBp1FjH,EAAE,CAAAkH,WAAA,eAAApC,MAAA,CAAAqC,eAAA,wBAAArC,MAAA,CAAAsC,SAAA,qBAAAtC,MAAA,CAAAuC,YAAA,CAAA3C,OAAA,CAAA4C,OAAA,sBAAAxC,MAAA,CAAAuC,YAAA,CAAA3C,OAAA,CAAA6C,OAAA,sBAAAzC,MAAA,CAAAuC,YAAA,CAAA3C,OAAA,CAAA8C,aAAA,uBAAA1C,MAAA,CAAA2C,mBAAA,CAAA/C,OAAA;IAAF1E,EAAE,CAAA+E,SAAA,EAysBg1H,CAAC;IAzsBn1H/E,EAAE,CAAA0H,aAAA,MAAAb,QAAA,GAAA/B,MAAA,CAAAG,YAAA,qBAAA4B,QAAA,CAAA3B,QAAA,SAysBg1H,CAAC;EAAA;AAAA;AAAA,SAAAyC,8BAAA5D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAzsBn1H/D,EAAE,CAAAoE,UAAA,IAAAsB,2CAAA,iBAysBunE,CAAC;EAAA;EAAA,IAAA3B,EAAA;IAAA,MAAAc,YAAA,GAAAb,GAAA,CAAAY,MAAA;IAAA,MAAAgD,YAAA,GAAA5D,GAAA,CAAA6D,MAAA;IAzsB1nE7H,EAAE,CAAA0H,aAAA,MAAA7C,YAAA,KAAA+C,YAAA,cAysBi2H,CAAC;EAAA;AAAA;AAAA,SAAAE,0CAAA/D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAzsBp2H/D,EAAE,CAAAyF,SAAA,YA20BkC,CAAC;EAAA;AAAA;AAn6B5J,MAAMsC,cAAc,GAAG;EACnBC,GAAG,EAAE,KAAK;EACVZ,SAAS,EAAE,YAAY;EACvBa,QAAQ,EAAE,KAAK;EACfjB,sBAAsB,EAAE,CAAC;EACzBkB,UAAU,EAAE,EAAE;EACdC,UAAU,EAAE,CAAC;EACblB,kBAAkB,EAAE,CAAC;EACrBmB,YAAY,EAAE,KAAK;EACnBC,IAAI,EAAE,SAAS;EACfC,aAAa,EAAE;AACnB,CAAC;AACD,MAAMC,6BAA6B,GAAG,IAAItI,cAAc,CAAC,6BAA6B,EAAE;EAAEuI,UAAU,EAAE,MAAM;EAAEC,OAAO,EAAEA,CAAA,KAAMV;AAAe,CAAC,CAAC;AAC9I;AACA;AACA;AACA;AACA,SAASW,0BAA0BA,CAACC,OAAO,EAAE;EACzC,OAAO;IACHC,OAAO,EAAEL,6BAA6B;IACtCM,UAAU,EAAEA,CAAA,MAAO;MACf,GAAG3I,MAAM,CAACqI,6BAA6B,EAAE;QAAEO,QAAQ,EAAE;MAAK,CAAC,CAAC;MAC5D,GAAGH;IACP,CAAC;EACL,CAAC;AACL;AAEA,MAAMI,oBAAoB,CAAC;EACvBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC9D,QAAQ,GAAGhF,MAAM,CAACC,WAAW,CAAC;IACnC;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAAC8I,yBAAyB,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC1C;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,8BAA8B,GAAG,IAAID,GAAG,CAAC,CAAC;EACnD;EACA;AACJ;AACA;EACIE,iBAAiBA,CAACC,aAAa,EAAE3F,SAAS,EAAE;IACxC,IAAI,IAAI,CAACyF,8BAA8B,CAACG,GAAG,CAAC5F,SAAS,CAAC,EAAE;MACpD,MAAM6F,eAAe,GAAG,IAAI,CAACJ,8BAA8B,CACtDK,GAAG,CAAC9F,SAAS,CAAC,CACd+F,IAAI,CAAEC,oBAAoB,IAAKA,oBAAoB,CAACC,aAAa,CAACC,QAAQ,CAACP,aAAa,CAAC,CAAC;MAC/F,IAAIE,eAAe,EAAE;QACjB,OAAO,KAAK;MAChB;IACJ;IACA,IAAI,IAAI,CAACN,yBAAyB,CAACK,GAAG,CAAC5F,SAAS,CAAC,EAAE;MAC/C,OAAO,IAAI,CAACuF,yBAAyB,CAChCO,GAAG,CAAC9F,SAAS,CAAC,CACd+F,IAAI,CAAEI,mBAAmB,IAAKA,mBAAmB,CAACF,aAAa,CAACC,QAAQ,CAACP,aAAa,CAAC,CAAC;IACjG;IACA,OAAO,IAAI;EACf;EACA;AACJ;AACA;EACIS,SAASA,CAAC9H,GAAG,EAAE0B,SAAS,EAAEqG,UAAU,EAAE;IAClC,IAAI/H,GAAG,CAACsH,GAAG,CAAC5F,SAAS,CAAC,EAAE;MACpB1B,GAAG,CAACwH,GAAG,CAAC9F,SAAS,CAAC,CAACsG,IAAI,CAACD,UAAU,CAAC;IACvC,CAAC,MACI;MACD/H,GAAG,CAACiI,GAAG,CAACvG,SAAS,EAAE,CAACqG,UAAU,CAAC,CAAC;IACpC;EACJ;EACA;AACJ;AACA;EACIG,eAAeA,CAAClI,GAAG,EAAE0B,SAAS,EAAEqG,UAAU,EAAE;IACxC,MAAMI,QAAQ,GAAGnI,GAAG,CAACwH,GAAG,CAAC9F,SAAS,CAAC;IACnCyG,QAAQ,CAACC,MAAM,CAACD,QAAQ,CAACE,OAAO,CAACN,UAAU,CAAC,EAAE,CAAC,CAAC;IAChD,IAAII,QAAQ,CAAC9E,MAAM,KAAK,CAAC,EAAE;MACvBrD,GAAG,CAACsI,MAAM,CAAC5G,SAAS,CAAC;IACzB;EACJ;EACA,OAAO6G,sBAAsBA,CAACC,IAAI,EAAExG,GAAG,EAAE;IACrC,OAAO,IAAI;EACf;EACA;EAAmB;IAAS,IAAI,CAACyG,IAAI,YAAAC,6BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwF5B,oBAAoB;IAAA,CAAmD;EAAE;EACtM;EAAmB;IAAS,IAAI,CAAC6B,IAAI,kBAD8E5K,EAAE,CAAA6K,iBAAA;MAAAC,IAAA,EACJ/B,oBAAoB;MAAAgC,SAAA;MAAAC,UAAA;IAAA,EAAkE;EAAE;AAC7M;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHuHjL,EAAE,CAAAkL,iBAAA,CAG9BnC,oBAAoB,EAAc,CAAC;IAClH+B,IAAI,EAAE1K,SAAS;IACf+K,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,iBAAiB;MAC3BJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA,MAAMK,gBAAgB,GAAG,IAAIpL,cAAc,CAAC,YAAY,CAAC;AAEzD,MAAMqL,8BAA8B,CAAC;EACjCtC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACtF,SAAS,GAAGxD,MAAM,CAACmL,gBAAgB,CAAC;IACzC,IAAI,CAACtB,UAAU,GAAG7J,MAAM,CAACG,UAAU,CAAC;IACpC,IAAI,CAACkL,SAAS,GAAGrL,MAAM,CAAC6I,oBAAoB,CAAC;IAC7C,IAAI,CAACwC,SAAS,CAACzB,SAAS,CAAC,IAAI,CAACyB,SAAS,CAACtC,yBAAyB,EAAE,IAAI,CAACvF,SAAS,EAAE,IAAI,CAACqG,UAAU,CAAC;EACvG;EACAyB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACD,SAAS,CAACrB,eAAe,CAAC,IAAI,CAACqB,SAAS,CAACtC,yBAAyB,EAAE,IAAI,CAACvF,SAAS,EAAE,IAAI,CAACqG,UAAU,CAAC;EAC7G;EACA;EAAmB;IAAS,IAAI,CAACU,IAAI,YAAAgB,uCAAAd,CAAA;MAAA,YAAAA,CAAA,IAAwFW,8BAA8B;IAAA,CAAmD;EAAE;EAChN;EAAmB;IAAS,IAAI,CAACV,IAAI,kBA7B8E5K,EAAE,CAAA6K,iBAAA;MAAAC,IAAA,EA6BJQ,8BAA8B;MAAAP,SAAA;MAAAC,UAAA;IAAA,EAA4E;EAAE;AACjO;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA/BuHjL,EAAE,CAAAkL,iBAAA,CA+B9BI,8BAA8B,EAAc,CAAC;IAC5HR,IAAI,EAAE1K,SAAS;IACf+K,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,2BAA2B;MACrCJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AAEpC,MAAMU,mCAAmC,CAAC;EACtC1C,WAAWA,CAAA,EAAG;IACV,IAAI,CAACtF,SAAS,GAAGxD,MAAM,CAACmL,gBAAgB,CAAC;IACzC,IAAI,CAACtB,UAAU,GAAG7J,MAAM,CAACG,UAAU,CAAC;IACpC,IAAI,CAACkL,SAAS,GAAGrL,MAAM,CAAC6I,oBAAoB,CAAC;IAC7C,IAAI,CAACwC,SAAS,CAACzB,SAAS,CAAC,IAAI,CAACyB,SAAS,CAACpC,8BAA8B,EAAE,IAAI,CAACzF,SAAS,EAAE,IAAI,CAACqG,UAAU,CAAC;EAC5G;EACAyB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACD,SAAS,CAACrB,eAAe,CAAC,IAAI,CAACqB,SAAS,CAACpC,8BAA8B,EAAE,IAAI,CAACzF,SAAS,EAAE,IAAI,CAACqG,UAAU,CAAC;EAClH;EACA;EAAmB;IAAS,IAAI,CAACU,IAAI,YAAAkB,4CAAAhB,CAAA;MAAA,YAAAA,CAAA,IAAwFe,mCAAmC;IAAA,CAAmD;EAAE;EACrN;EAAmB;IAAS,IAAI,CAACd,IAAI,kBAlD8E5K,EAAE,CAAA6K,iBAAA;MAAAC,IAAA,EAkDJY,mCAAmC;MAAAX,SAAA;MAAAC,UAAA;IAAA,EAAiF;EAAE;AAC3O;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KApDuHjL,EAAE,CAAAkL,iBAAA,CAoD9BQ,mCAAmC,EAAc,CAAC;IACjIZ,IAAI,EAAE1K,SAAS;IACf+K,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,gCAAgC;MAC1CJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA;AACA;AACA,SAASY,iBAAiBA,CAACC,KAAK,EAAE;EAC9B;EACA,MAAMC,YAAY,GAAIC,CAAC,IAAKC,MAAM,CAACC,UAAU,IAAIJ,KAAK,YAAYI,UAAU;EAC5E,IAAIH,YAAY,CAACD,KAAK,CAAC,EAAE;IACrB,IAAIA,KAAK,CAACK,cAAc,CAAC7G,MAAM,KAAK,CAAC,EAAE;MACnC,OAAO8G,SAAS;IACpB;IACA,MAAM;MAAEC,OAAO;MAAEC;IAAQ,CAAC,GAAGR,KAAK,CAACK,cAAc,CAAC,CAAC,CAAC;IACpD,OAAO;MACHI,CAAC,EAAEF,OAAO;MACVG,CAAC,EAAEF;IACP,CAAC;EACL;EACA,IAAIR,KAAK,YAAYW,aAAa,EAAE;IAChC,MAAMC,MAAM,GAAGZ,KAAK,CAACY,MAAM;IAC3B;IACA,OAAO;MACHH,CAAC,EAAEG,MAAM,CAACC,UAAU,GAAGD,MAAM,CAACE,WAAW,GAAG,CAAC;MAC7CJ,CAAC,EAAEE,MAAM,CAACG,SAAS,GAAGH,MAAM,CAACI,YAAY,GAAG;IAChD,CAAC;EACL;EACA,OAAO;IACHP,CAAC,EAAET,KAAK,CAACO,OAAO;IAChBG,CAAC,EAAEV,KAAK,CAACQ;EACb,CAAC;AACL;AACA,SAASS,0BAA0BA,CAACC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,aAAa,EAAE;EAChF,IAAI,CAACA,aAAa,CAACtD,QAAQ,CAACmD,UAAU,CAACN,MAAM,CAAC,IAC1C,CAACS,aAAa,CAACtD,QAAQ,CAACoD,QAAQ,CAACP,MAAM,CAAC,EAAE;IAC1C,OAAO,KAAK;EAChB;EACA,MAAMU,UAAU,GAAGvB,iBAAiB,CAACmB,UAAU,CAAC;EAChD,MAAMK,QAAQ,GAAGxB,iBAAiB,CAACoB,QAAQ,CAAC;EAC5C,OAAOK,IAAI,CAACC,GAAG,CAACF,QAAQ,CAACd,CAAC,GAAGa,UAAU,CAACb,CAAC,CAAC,IAAIW,SAAS,IAAII,IAAI,CAACC,GAAG,CAACF,QAAQ,CAACb,CAAC,GAAGY,UAAU,CAACZ,CAAC,CAAC,IAAIU,SAAS;AAC/G;AACA,SAASM,kBAAkBA,CAACd,MAAM,EAAE;EAChC,OAAO/K,KAAK,CAACC,SAAS,CAAC8K,MAAM,EAAE,WAAW,CAAC,CAACe,IAAI,CAAC5L,MAAM,CAAEmK,CAAC,IAAKA,CAAC,CAAC0B,MAAM,KAAK,CAAC,CAAC,CAAC;EAC/E;EACA9L,SAAS,CAAC8K,MAAM,EAAE,YAAY,EAAE;IAAEiB,OAAO,EAAE;EAAM,CAAC,CAAC,CAAC;AACxD;AACA,SAASC,kBAAkBA,CAAClB,MAAM,EAAE;EAChC,OAAO/K,KAAK,CAACC,SAAS,CAAC8K,MAAM,EAAE,WAAW,CAAC,EAAE9K,SAAS,CAAC8K,MAAM,EAAE,WAAW,CAAC,CAAC;AAChF;AACA,SAASmB,gBAAgBA,CAACnB,MAAM,EAAEoB,kBAAkB,GAAG,KAAK,EAAE;EAC1D,MAAMC,kBAAkB,GAAGpM,KAAK,CAACC,SAAS,CAAC8K,MAAM,EAAE,SAAS,CAAC,EAAE9K,SAAS,CAAC8K,MAAM,EAAE,UAAU,CAAC,CAAC;EAC7F,OAAOoB,kBAAkB,GACnBnM,KAAK,CAACoM,kBAAkB,EAAEnM,SAAS,CAAC8K,MAAM,EAAE,aAAa,CAAC,CAAC,GAC3DqB,kBAAkB;AAC5B;AACA,SAASC,GAAGA,CAACC,KAAK,EAAEC,EAAE,EAAE;EACpB,OAAOD,KAAK,CAACE,MAAM,CAAC,CAACH,GAAG,EAAEI,IAAI,KAAKJ,GAAG,GAAGE,EAAE,CAACE,IAAI,CAAC,EAAE,CAAC,CAAC;AACzD;AACA,SAASC,QAAQA,CAACJ,KAAK,EAAEC,EAAE,EAAE;EACzB,OAAOD,KAAK,CAACE,MAAM,CAAC,CAACG,MAAM,EAAEF,IAAI,EAAEG,KAAK,KAAK;IACzC,MAAM,CAACC,GAAG,EAAEC,KAAK,CAAC,GAAGP,EAAE,CAACE,IAAI,EAAEG,KAAK,CAAC;IACpCD,MAAM,CAACE,GAAG,CAAC,GAAGC,KAAK;IACnB,OAAOH,MAAM;EACjB,CAAC,EAAE,CAAC,CAAC,CAAC;AACV;AACA,SAASI,mBAAmBA,CAACC,aAAa,EAAE;EACxC,OAAOC,MAAM,CAACC,OAAO,CAACF,aAAa,CAAC,CAC/B9M,MAAM,CAAC,CAAC,GAAG4M,KAAK,CAAC,KAAKA,KAAK,CAAC,CAC5BxM,GAAG,CAAC,CAAC,CAACuM,GAAG,CAAC,KAAKA,GAAG,CAAC,CACnBM,IAAI,CAAC,GAAG,CAAC;AAClB;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,KAAK,EAAE;EACzB,MAAMC,KAAK,GAAG1O,QAAQ,CAAC,MAAMC,MAAM,CAACwO,KAAK,CAAC,CAAC,CAAC,CAAC;EAC7C,MAAME,MAAM,GAAGA,CAAA,KAAMD,KAAK,CAAC,CAAC,CAAC,CAAC;EAC9BC,MAAM,CAAChF,GAAG,GAAIuE,KAAK,IAAKhO,SAAS,CAACwO,KAAK,CAAC,CAAC/E,GAAG,CAACuE,KAAK,CAAC;EACnDS,MAAM,CAACC,KAAK,GAAG,MAAM1O,SAAS,CAAC,MAAMwO,KAAK,CAAC,CAAC,CAAC/E,GAAG,CAAC8E,KAAK,CAAC,CAAC,CAAC,CAAC;EAC1D,OAAOE,MAAM;AACjB;AACA,SAASE,WAAWA,CAAA,EAAG;EACnB,OAAQC,MAAM,IAAK,IAAIvN,UAAU,CAAEwN,QAAQ,IAAKnP,MAAM,CAACO,MAAM,CAAC,CAAC6O,iBAAiB,CAAC,MAAMF,MAAM,CAACG,SAAS,CAACF,QAAQ,CAAC,CAAC,CAAC;AACvH;AACA,MAAMG,2BAA2B,GAAIC,QAAQ,IAAMjB,KAAK,IAAK9N,eAAe,CAAC8N,KAAK,EAAEiB,QAAQ,CAAC;AAC7F,MAAMC,iBAAiB,GAAGA,CAAClB,KAAK,EAAEmB,IAAI,KAAK;EACvC,MAAM,IAAIC,KAAK,CAAE,4BAA2BpB,KAAM,UAASmB,IAAK,GAAE,CAAC;AACvE,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,kCAAkC,CAAC;EACrC7G,WAAWA,CAAA,EAAG;IACV,IAAI,CAACe,UAAU,GAAG7J,MAAM,CAACG,UAAU,CAAC;IACpC,IAAI,CAACyP,QAAQ,GAAG5P,MAAM,CAAC2C,QAAQ,CAAC;IAChC,IAAI,CAACkN,mBAAmB,GAAGpP,KAAK,CAACqP,QAAQ,CAAC;MAAEC,KAAK,EAAE;IAAmC,CAAC,CAAC;IACxF,IAAI,CAAChD,SAAS,GAAGtM,KAAK,CAACqP,QAAQ,CAAC;MAAEC,KAAK,EAAE;IAA8B,CAAC,CAAC;IACzE,IAAI,CAACC,SAAS,GAAGtP,MAAM,CAAC;MAAEqP,KAAK,EAAE;IAAyB,CAAC,CAAC;IAC5D,IAAI,CAACE,KAAK,GAAGvP,MAAM,CAAC;MAAEqP,KAAK,EAAE;IAAqB,CAAC,CAAC;IACpD,IAAI,CAACG,QAAQ,GAAGxP,MAAM,CAAC;MAAEqP,KAAK,EAAE;IAAwB,CAAC,CAAC;IAC1D,IAAI,CAACI,OAAO,GAAGzP,MAAM,CAAC;MAAEqP,KAAK,EAAE;IAAuB,CAAC,CAAC;IACxDtO,SAAS,CAAC,IAAI,CAACoI,UAAU,CAACJ,aAAa,EAAE,SAAS,CAAC,CAC9C6D,IAAI,CAAC2B,WAAW,CAAC,CAAC,EAAE1N,kBAAkB,CAAC,CAAC,CAAC,CACzC8N,SAAS,CAAExD,CAAC,IAAK,IAAI,CAACsE,OAAO,CAACC,IAAI,CAACvE,CAAC,CAAC,CAAC;IAC3C;IACA,MAAMwE,YAAY,GAAGhD,kBAAkB,CAAC,IAAI,CAACxD,UAAU,CAACJ,aAAa,CAAC,CAAC6D,IAAI,CAAC1L,SAAS,CAAE0O,cAAc,IAAK7C,kBAAkB,CAAC,IAAI,CAACmC,QAAQ,CAAC,CAACtC,IAAI,CAAC5L,MAAM,CAAEmK,CAAC,IAAK,CAACe,0BAA0B,CAAC0D,cAAc,EAAEzE,CAAC,EAAE,IAAI,CAACkB,SAAS,CAAC,CAAC,EAAE,IAAI,CAAClD,UAAU,CAACJ,aAAa,CAAC,CAAC,EAAE5H,IAAI,CAAC,CAAC,CAAC,EAAEC,GAAG,CAAC,MAAM,IAAI,CAAC,EAAEC,SAAS,CAAC2L,gBAAgB,CAAC,IAAI,CAACkC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IACxUvC,kBAAkB,CAAC,IAAI,CAACxD,UAAU,CAACJ,aAAa,CAAC,CAC5C6D,IAAI,CAACtL,GAAG,CAAE6J,CAAC,IAAK,IAAI,CAACmE,SAAS,CAACI,IAAI,CAACvE,CAAC,CAAC,CAAC;IAC5C;IACA5J,YAAY,CAAC,CAAC;IACd;IACAC,IAAI,CAAC,CAAC2L,GAAG,EAAE;MAAE0C;IAAS,CAAC,KAAMA,QAAQ,IAAI,IAAI,CAACV,mBAAmB,CAAC,CAAC,GAAG,CAAC,GAAGhC,GAAG,GAAG,CAAE,EAAE,CAAC,CAAC;IACtF;IACA;IACAjM,SAAS,CAAE4O,sBAAsB;IACjC;IACA;IACA;IACA;IACA9C,gBAAgB,CAAC,IAAI,CAAC7D,UAAU,CAACJ,aAAa,CAAC,CAAC6D,IAAI,CAACrL,YAAY,CAAC,CAAC,EAAEJ,IAAI,CAAC,CAAC,CAAC,EAAE2O,sBAAsB,KAAK,CAAC,GACpG1O,GAAG,CAAC,MAAM0O,sBAAsB,CAAC,GACjCrO,QAAQ,CAAC,CAAC;MAAEoO;IAAS,CAAC,KAAKA,QAAQ,IAAI,IAAI,CAACV,mBAAmB,CAAC,CAAC,GAC7DzN,EAAE,CAACoO,sBAAsB,CAAC,GAC1BpO,EAAE,CAACoO,sBAAsB,CAAC,CAAClD,IAAI,CAACjL,KAAK,CAAC,IAAI,CAACwN,mBAAmB,CAAC,CAAC,GAAGU,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1F;IACAxO,SAAS,CAACsO,YAAY,CAAC,EAAE/N,MAAM,CAAC,CAAC,EAAE2M,WAAW,CAAC,CAAC,EAAE1N,kBAAkB,CAAC,CAAC,CAAC,CAClE8N,SAAS,CAAEoB,MAAM,IAAK;MACvB,IAAIA,MAAM,KAAK,CAAC,EAAE;QACd,IAAI,CAACR,KAAK,CAACG,IAAI,CAAC,CAAC;MACrB,CAAC,MACI,IAAIK,MAAM,KAAK,CAAC,EAAE;QACnB,IAAI,CAACP,QAAQ,CAACE,IAAI,CAAC,CAAC;MACxB;IACJ,CAAC,CAAC;EACN;EACA;EAAmB;IAAS,IAAI,CAAC7F,IAAI,YAAAmG,2CAAAjG,CAAA;MAAA,YAAAA,CAAA,IAAwFkF,kCAAkC;IAAA,CAAmD;EAAE;EACpN;EAAmB;IAAS,IAAI,CAACjF,IAAI,kBA5M8E5K,EAAE,CAAA6K,iBAAA;MAAAC,IAAA,EA4MJ+E,kCAAkC;MAAA9E,SAAA;MAAA8F,MAAA;QAAAd,mBAAA,GA5MhC/P,EAAE,CAAA8Q,YAAA,CAAAC,WAAA;QAAA9D,SAAA,GAAFjN,EAAE,CAAA8Q,YAAA,CAAAC,WAAA;MAAA;MAAAC,OAAA;QAAAd,SAAA;QAAAC,KAAA;QAAAC,QAAA;QAAAC,OAAA;MAAA;MAAArF,UAAA;IAAA,EA4MglB;EAAE;AAC3sB;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA9MuHjL,EAAE,CAAAkL,iBAAA,CA8M9B2E,kCAAkC,EAAc,CAAC;IAChI/E,IAAI,EAAE1K,SAAS;IACf+K,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,+BAA+B;MACzCJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AAEpC,SAASiG,aAAaA,CAACC,KAAK,EAAE7I,IAAI,EAAE8I,WAAW,EAAE;EAC7C,IAAID,KAAK,CAAC7L,MAAM,KAAK,CAAC,EAAE;IACpB,OAAO,IAAI;EACf;EACA,MAAM+L,SAAS,GAAGF,KAAK,CAAClP,GAAG,CAAEqP,IAAI,IAAK;IAClC,MAAMC,IAAI,GAAGD,IAAI,CAACC,IAAI,CAAC,CAAC;IACxB,OAAOA,IAAI,KAAK,MAAM,GAAG,GAAG,GAAGA,IAAI;EACvC,CAAC,CAAC;EACF,MAAMC,aAAa,GAAGH,SAAS,CAACxP,MAAM,CAAE4P,QAAQ,IAAKA,QAAQ,KAAK,GAAG,CAAC;EACtE,IAAID,aAAa,CAAClM,MAAM,GAAG,CAAC,EAAE;IAC1B,IAAI8L,WAAW,EAAE;MACbM,OAAO,CAACC,IAAI,CAAC,yCAAyC,CAAC;IAC3D;IACA,OAAO,KAAK;EAChB;EACA,IAAIrJ,IAAI,KAAK,OAAO,EAAE;IAClB,IAAIkJ,aAAa,CAAClM,MAAM,KAAK,CAAC,EAAE;MAC5B,OAAO,IAAI;IACf;IACA,IAAI8L,WAAW,EAAE;MACbM,OAAO,CAACC,IAAI,CAAC,mDAAmD,CAAC;IACrE;IACA,OAAO,KAAK;EAChB;EACA,MAAMC,UAAU,GAAG5D,GAAG,CAACqD,SAAS,EAAGI,QAAQ,IAAMA,QAAQ,KAAK,GAAG,GAAG,CAAC,GAAGA,QAAS,CAAC;EAClF;EACA,IAAID,aAAa,CAAClM,MAAM,KAAK,CAAC,EAAE;IAC5B,IAAIsM,UAAU,IAAI,KAAK,EAAE;MACrB,OAAO,IAAI;IACf;IACA,IAAIR,WAAW,EAAE;MACbM,OAAO,CAACC,IAAI,CAAE,yCAAwC,CAAC;IAC3D;IACA,OAAO,KAAK;EAChB;EACA,IAAIC,UAAU,GAAG,IAAI,IAAIA,UAAU,GAAG,KAAK,EAAE;IACzC,IAAIR,WAAW,EAAE;MACbM,OAAO,CAACC,IAAI,CAAC,yCAAyC,CAAC;IAC3D;IACA,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA;AACA,MAAME,mCAAmC,CAAC;EACtC5I,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC6I,GAAG,GAAG3R,MAAM,CAACW,gBAAgB,CAAC;IACnC,IAAI,CAACiR,WAAW,GAAG5R,MAAM,CAACC,WAAW,CAAC;IACtC,IAAI,CAACuD,SAAS,GAAG/C,KAAK,CAACqP,QAAQ,CAAC;MAAEC,KAAK,EAAE;IAA+B,CAAC,CAAC;IAC1EnP,MAAM,CAAC,MAAM;MACT,IAAI,CAAC+Q,GAAG,CAACE,KAAK,CAAC,CAAC;MAChB,MAAMC,QAAQ,GAAGjR,QAAQ,CAACkR,MAAM,CAAC;QAC7BC,SAAS,EAAE,CACP;UACItJ,OAAO,EAAEyC,gBAAgB;UACzB8G,QAAQ,EAAE,IAAI,CAACzO,SAAS,CAAC;QAC7B,CAAC,CACJ;QACD0O,MAAM,EAAE,IAAI,CAACP,GAAG,CAACG;MACrB,CAAC,CAAC;MACF,IAAI,CAACH,GAAG,CAACQ,kBAAkB,CAAC,IAAI,CAACP,WAAW,EAAE;QAAEvN,SAAS,EAAEyN;MAAS,CAAC,CAAC;IAC1E,CAAC,CAAC;EACN;EACA,OAAOzH,sBAAsBA,CAACC,IAAI,EAAExG,GAAG,EAAE;IACrC,OAAO,IAAI;EACf;EACA;EAAmB;IAAS,IAAI,CAACyG,IAAI,YAAA6H,4CAAA3H,CAAA;MAAA,YAAAA,CAAA,IAAwFiH,mCAAmC;IAAA,CAAmD;EAAE;EACrN;EAAmB;IAAS,IAAI,CAAChH,IAAI,kBA7R8E5K,EAAE,CAAA6K,iBAAA;MAAAC,IAAA,EA6RJ8G,mCAAmC;MAAA7G,SAAA;MAAA8F,MAAA;QAAAnN,SAAA,GA7RjC1D,EAAE,CAAA8Q,YAAA,CAAAC,WAAA;MAAA;MAAA/F,UAAA;IAAA,EA6RkR;EAAE;AAC7Y;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA/RuHjL,EAAE,CAAAkL,iBAAA,CA+R9B0G,mCAAmC,EAAc,CAAC;IACjI9G,IAAI,EAAE1K,SAAS;IACf+K,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,gCAAgC;MAC1CJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AAEpC,MAAMuH,mBAAmB,GAAG,IAAItS,cAAc,CAAC,qBAAqB,CAAC;AACrE,MAAMuS,cAAc,CAAC;EACjB,IAAIC,kBAAkBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACC,WAAW,CAAC,CAAC;EAC7B;EACA,IAAIC,cAAcA,CAAA,EAAG;IACjB,OAAO,IAAI,CAAC3K,GAAG,CAAC,CAAC;EACrB;EACAgB,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC8G,QAAQ,GAAG5P,MAAM,CAAC2C,QAAQ,CAAC;IAChC,IAAI,CAAC+P,QAAQ,GAAG1S,MAAM,CAACc,SAAS,CAAC;IACjC,IAAI,CAAC+I,UAAU,GAAG7J,MAAM,CAACG,UAAU,CAAC;IACpC,IAAI,CAACwS,MAAM,GAAG3S,MAAM,CAACO,MAAM,CAAC;IAC5B,IAAI,CAACsH,cAAc,GAAG7H,MAAM,CAACqI,6BAA6B,CAAC;IAC3D,IAAI,CAACuK,sBAAsB,GAAG,IAAIrQ,OAAO,CAAC,CAAC;IAC3C,IAAI,CAACsQ,mBAAmB,GAAG,IAAItQ,OAAO,CAAC,CAAC;IACxC;AACR;AACA;IACQ,IAAI,CAAC2C,MAAM,GAAGnE,eAAe,CAACsR,mBAAmB,CAAC;IAClD,IAAI,CAACtN,YAAY,GAAG/D,YAAY,CAAC6H,oBAAoB,CAAC;IACtD,IAAI,CAACb,UAAU,GAAGvH,KAAK,CAAC,IAAI,CAACoH,cAAc,CAACG,UAAU,EAAE;MACpD8K,SAAS,EAAExD,2BAA2B,CAAC,IAAI,CAACzH,cAAc,CAACG,UAAU;IACzE,CAAC,CAAC;IACF,IAAI,CAACC,UAAU,GAAGxH,KAAK,CAAC,IAAI,CAACoH,cAAc,CAACI,UAAU,EAAE;MACpD6K,SAAS,EAAExD,2BAA2B,CAAC,IAAI,CAACzH,cAAc,CAACI,UAAU;IACzE,CAAC,CAAC;IACF,IAAI,CAACF,QAAQ,GAAGtH,KAAK,CAAC,IAAI,CAACoH,cAAc,CAACE,QAAQ,EAAE;MAAE+K,SAAS,EAAE7R;IAAiB,CAAC,CAAC;IACpF,IAAI,CAAC8F,kBAAkB,GAAGtG,KAAK,CAAC,IAAI,CAACoH,cAAc,CAACd,kBAAkB,EAAE;MACpE+L,SAAS,EAAExD,2BAA2B,CAAC,IAAI,CAACzH,cAAc,CAACd,kBAAkB;IACjF,CAAC,CAAC;IACF,IAAI,CAACG,SAAS,GAAGzG,KAAK,CAAC,IAAI,CAACoH,cAAc,CAACX,SAAS,CAAC;IACrD,IAAI,CAACY,GAAG,GAAGrH,KAAK,CAAC,IAAI,CAACoH,cAAc,CAACC,GAAG,CAAC;IACzC,IAAI,CAACK,IAAI,GAAG1H,KAAK,CAAC,IAAI,CAACoH,cAAc,CAACM,IAAI,CAAC;IAC3C,IAAI,CAAClB,eAAe,GAAGxG,KAAK,CAAC,CAAC;IAC9B,IAAI,CAACyH,YAAY,GAAGzH,KAAK,CAAC,IAAI,CAACoH,cAAc,CAACK,YAAY,EAAE;MAAE4K,SAAS,EAAE7R;IAAiB,CAAC,CAAC;IAC5F,IAAI,CAACmH,aAAa,GAAG3H,KAAK,CAAC,IAAI,CAACoH,cAAc,CAACO,aAAa,EAAE;MAAE0K,SAAS,EAAE7R;IAAiB,CAAC,CAAC;IAC9F,IAAI,CAAC6F,sBAAsB,GAAGrG,KAAK,CAAC,IAAI,CAACoH,cAAc,CAACf,sBAAsB,EAAE;MAC5EgM,SAAS,EAAExD,2BAA2B,CAAC,IAAI,CAACzH,cAAc,CAACf,sBAAsB;IACrF,CAAC,CAAC;IACF,IAAI,CAACiM,WAAW,GAAGrS,MAAM,CAAC,CAAC;IAC3B,IAAI,CAACsS,cAAc,GAAGtS,MAAM,CAAC,CAAC;IAC9B,IAAI,CAACuS,SAAS,GAAGvS,MAAM,CAAC,CAAC;IACzB,IAAI,CAACwS,OAAO,GAAGxS,MAAM,CAAC,CAAC;IACvB,IAAI,CAACyS,aAAa,GAAGzS,MAAM,CAAC,CAAC;IAC7B,IAAI,CAAC0S,aAAa,GAAG,IAAI,CAACP,mBAAmB,CAACQ,YAAY,CAAC,CAAC;IAC5D;AACR;AACA;IACQ,IAAI,CAACC,aAAa,GAAGlT,QAAQ,CAAC,MAAM,IAAI,CAAC8E,MAAM,CAAC,CAAC,CAACxD,MAAM,CAAEyP,IAAI,IAAKA,IAAI,CAACoC,OAAO,CAAC,CAAC,CAAC,CAAC;IACnF,IAAI,CAACC,wBAAwB,GAAGpT,QAAQ,CAAC,MAAM,IAAI,CAACqT,8BAA8B,CAAC,CAAC,CAAC;IACrF,IAAI,CAACjB,WAAW,GAAGpS,QAAQ,CAAC,MAAMmO,mBAAmB,CAAC;MAClD,CAAE,MAAK,IAAI,CAACrH,SAAS,CAAC,CAAE,EAAC,GAAG,IAAI;MAChC,CAAE,MAAK,IAAI,CAACiB,IAAI,CAAC,CAAE,EAAC,GAAG,IAAI;MAC3B,CAAC,aAAa,GAAG,IAAI,CAACJ,QAAQ,CAAC,CAAC;MAChC,CAAC,aAAa,GAAG,IAAI,CAAC2L,WAAW,CAAC,CAAC;MACnC,CAAC,eAAe,GAAG,IAAI,CAACtL,aAAa,CAAC,CAAC,IAAI,CAAC,IAAI,CAACsL,WAAW,CAAC;IACjE,CAAC,CAAC,CAAC;IACH,IAAI,CAACtO,kBAAkB,GAAG/E,MAAM,CAAC4L,SAAS,CAAC;IAC3C;AACR;AACA;IACQ,IAAI,CAACyH,WAAW,GAAGtT,QAAQ,CAAC,MAAM,IAAI,CAACgF,kBAAkB,CAAC,CAAC,KAAK6G,SAAS,CAAC;IAC1E;AACR;AACA;AACA;IACQ,IAAI,CAAC0H,yBAAyB,GAAGvT,QAAQ,CAAC,MAAM,IAAI,CAACwT,6BAA6B,CAAC,CAAC,CAAC;IACrF,IAAI1S,SAAS,CAAC,CAAC,EAAE;MACb;MACAN,MAAM,CAAC,MAAM;QACT;QACA,IAAI,IAAI,CAACuH,IAAI,CAAC,CAAC,KAAK,SAAS,IAAI,IAAI,CAACmL,aAAa,CAAC,CAAC,CAACO,KAAK,CAAE1C,IAAI,IAAKA,IAAI,CAACC,IAAI,CAAC,CAAC,KAAK,MAAM,CAAC,EAAE;UAC3F;QACJ;QACAL,aAAa,CAAC,IAAI,CAACuC,aAAa,CAAC,CAAC,EAAE,IAAI,CAACnL,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC;MAC1D,CAAC,CAAC;IACN;IACA;IACA;IACA;IACAvH,MAAM,CAAC,MAAM;MACT,MAAM4S,wBAAwB,GAAG,IAAI,CAACA,wBAAwB,CAAC,CAAC;MAChE,IAAI,CAACd,QAAQ,CAACoB,QAAQ,CAAC,IAAI,CAACjK,UAAU,CAACJ,aAAa,EAAE,eAAe,EAAE+J,wBAAwB,CAAC;IACpG,CAAC,CAAC;IACF,IAAI,CAACZ,sBAAsB,CACtBtF,IAAI,CAAC5L,MAAM,CAAEqS,OAAO,IAAK,CAAC,IAAI,CAAChP,YAAY,CAAC,CAAC,IAC9C,IAAI,CAACA,YAAY,CAAC,CAAC,CAACmE,iBAAiB,CAAC6K,OAAO,CAACzD,cAAc,CAAC/D,MAAM,EAAEwH,OAAO,CAACC,WAAW,GAAG,CAAC,CAAC,CAAC,EAAEpS,SAAS,CAAEqS,gBAAgB;IAC/H;IACA;IACA;IACA;IACA;IACAxG,kBAAkB,CAAC,IAAI,CAACmC,QAAQ,CAAC,CAACtC,IAAI,CAAC9K,SAAS,CAACyR,gBAAgB,CAAC3D,cAAc,CAAC,EAAE7N,QAAQ,CAAC,CAAC,EAAEC,SAAS,CAAC,CAAC,GAAGwR,aAAa,CAAC,KAAKtH,0BAA0B,CAACqH,gBAAgB,CAAC3D,cAAc,EAAE4D,aAAa,EAAE,IAAI,CAACnN,kBAAkB,CAAC,CAAC,EAAEkN,gBAAgB,CAACjH,aAAa,CAAC,CAAC,EAAEnL,IAAI,CAAC,CAAC,CAAC,EAAEE,SAAS,CAAC2L,gBAAgB,CAAC,IAAI,CAACkC,QAAQ,EAAE,IAAI,CAAC,CAAC,EAAE5N,GAAG,CAAC,MAAM;MACzU,IAAI,CAAC2Q,MAAM,CAACwB,GAAG,CAAC,MAAM;QAClB,IAAI,CAAClB,SAAS,CAAC7C,IAAI,CAAC,IAAI,CAACgE,0BAA0B,CAACH,gBAAgB,CAACD,WAAW,CAAC,CAAC;QAClF,IAAI,CAAC5O,kBAAkB,CAAC2E,GAAG,CAACkK,gBAAgB,CAACD,WAAW,CAAC;MAC7D,CAAC,CAAC;IACN,CAAC,CAAC,EAAElS,GAAG,CAAC,CAAC,CAACuS,cAAc,CAAC,KAAK,IAAI,CAACC,sBAAsB,CAACD,cAAc,EAAEJ,gBAAgB,CAACM,qBAAqB,EAAEN,gBAAgB,CAACO,oBAAoB,CAAC,CAAC,EAAE5S,SAAS,CAAE6S,gBAAgB,IAAKhH,kBAAkB,CAAC,IAAI,CAACmC,QAAQ,CAAC,CAACtC,IAAI,CAACtL,GAAG,CAAE0S,SAAS,IAAK,IAAI,CAACC,aAAa,CAACD,SAAS,EAAED,gBAAgB,CAAC,CAAC,EAAE1S,SAAS,CAAC2L,gBAAgB,CAAC,IAAI,CAACkC,QAAQ,EAAE,IAAI,CAAC,CAAC,EAAE5N,GAAG,CAAC;MACzV4S,QAAQ,EAAEA,CAAA,KAAM,IAAI,CAACjC,MAAM,CAACwB,GAAG,CAAC,MAAM;QAClC,IAAI,CAACjB,OAAO,CAAC9C,IAAI,CAAC,IAAI,CAACgE,0BAA0B,CAAC,IAAI,CAAChP,kBAAkB,CAAC,CAAC,CAAC,CAAC;QAC7E,IAAI,CAACA,kBAAkB,CAAC2E,GAAG,CAACkC,SAAS,CAAC;MAC1C,CAAC;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE1K,kBAAkB,CAAC,CAAC,CAAC,CACxB8N,SAAS,CAAC,CAAC;IAChB5N,SAAS,CAAC,IAAI,CAACoI,UAAU,CAACJ,aAAa,EAAE,eAAe,CAAC,CACpD6D,IAAI,CAAC5L,MAAM,CAAEmK,CAAC,IAAKA,CAAC,CAACgJ,YAAY,CAACC,UAAU,CAAC,eAAe,CAAC,CAAC,EAAE7F,WAAW,CAAC,CAAC,EAAE1N,kBAAkB,CAAC,CAAC,CAAC,CACpG8N,SAAS,CAAC,MAAM,IAAI,CAACsD,MAAM,CAACwB,GAAG,CAAC,MAAM,IAAI,CAAChB,aAAa,CAAC/C,IAAI,CAAC,IAAI,CAAC2E,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;EAChG;EACA/O,aAAaA,CAACgO,WAAW,EAAE;IACvB,IAAI,CAACrB,MAAM,CAACwB,GAAG,CAAC,MAAM,IAAI,CAACpB,WAAW,CAAC3C,IAAI,CAAC,IAAI,CAACgE,0BAA0B,CAACJ,WAAW,CAAC,CAAC,CAAC;EAC9F;EACA9N,mBAAmBA,CAAC8N,WAAW,EAAE;IAC7B,IAAI,CAACrB,MAAM,CAACwB,GAAG,CAAC,MAAM,IAAI,CAACnB,cAAc,CAAC5C,IAAI,CAAC,IAAI,CAACgE,0BAA0B,CAACJ,WAAW,CAAC,CAAC,CAAC;EACjG;EACAzN,eAAeA,CAACsF,CAAC,EAAEmB,aAAa,EAAEgH,WAAW,EAAEO,qBAAqB,EAAEC,oBAAoB,EAAE;IACxF,IAAI,IAAI,CAACzM,QAAQ,CAAC,CAAC,EAAE;MACjB;IACJ;IACA8D,CAAC,CAACmJ,cAAc,CAAC,CAAC;IAClBnJ,CAAC,CAACoJ,eAAe,CAAC,CAAC;IACnB,IAAI,CAACrC,sBAAsB,CAACsC,IAAI,CAAC;MAC7B5E,cAAc,EAAEzE,CAAC;MACjBmB,aAAa;MACbgH,WAAW;MACXO,qBAAqB;MACrBC;IACJ,CAAC,CAAC;EACN;EACA/N,aAAaA,CAACoF,CAAC,EAAEmI,WAAW,EAAEO,qBAAqB,EAAEC,oBAAoB,EAAE;IACvE,IAAI,IAAI,CAACzM,QAAQ,CAAC,CAAC,EAAE;MACjB;IACJ;IACA,MAAMoN,YAAY,GAAG,EAAE;IACvB,MAAMC,kBAAkB,GAAG,EAAE;IAC7B,IAAIC,YAAY,GAAG,CAAC;IACpB,IAAIC,YAAY,GAAG,CAAC;IACpB,IAAI,IAAI,CAACpO,SAAS,CAAC,CAAC,KAAK,YAAY,EAAE;MACnC;MACA,QAAQ2E,CAAC,CAACwC,GAAG;QACT,KAAK,WAAW;UACZgH,YAAY,IAAIF,YAAY;UAC5B;QACJ,KAAK,YAAY;UACbE,YAAY,IAAIF,YAAY;UAC5B;QACJ,KAAK,QAAQ;UACT,IAAI,IAAI,CAACrN,GAAG,CAAC,CAAC,KAAK,KAAK,EAAE;YACtBuN,YAAY,IAAIF,YAAY,GAAGC,kBAAkB;UACrD,CAAC,MACI;YACDC,YAAY,IAAIF,YAAY,GAAGC,kBAAkB;UACrD;UACA;QACJ,KAAK,UAAU;UACX,IAAI,IAAI,CAACtN,GAAG,CAAC,CAAC,KAAK,KAAK,EAAE;YACtBuN,YAAY,IAAIF,YAAY,GAAGC,kBAAkB;UACrD,CAAC,MACI;YACDC,YAAY,IAAIF,YAAY,GAAGC,kBAAkB;UACrD;UACA;QACJ;UACI;MACR;IACJ,CAAC,MACI;MACD,QAAQvJ,CAAC,CAACwC,GAAG;QACT,KAAK,SAAS;UACViH,YAAY,IAAIH,YAAY;UAC5B;QACJ,KAAK,WAAW;UACZG,YAAY,IAAIH,YAAY;UAC5B;QACJ,KAAK,QAAQ;UACTG,YAAY,IAAIH,YAAY,GAAGC,kBAAkB;UACjD;QACJ,KAAK,UAAU;UACXE,YAAY,IAAIH,YAAY,GAAGC,kBAAkB;UACjD;QACJ;UACI;MACR;IACJ;IACAvJ,CAAC,CAACmJ,cAAc,CAAC,CAAC;IAClBnJ,CAAC,CAACoJ,eAAe,CAAC,CAAC;IACnB,MAAMM,cAAc,GAAG7J,iBAAiB,CAACG,CAAC,CAAC;IAC3C,MAAM4I,gBAAgB,GAAG,IAAI,CAACH,sBAAsB,CAACzI,CAAC,EAAE0I,qBAAqB,EAAEC,oBAAoB,CAAC;IACpG,IAAI,CAAC7B,MAAM,CAACwB,GAAG,CAAC,MAAM;MAClB,IAAI,CAAClB,SAAS,CAAC7C,IAAI,CAAC,IAAI,CAACgE,0BAA0B,CAACJ,WAAW,CAAC,CAAC;MACjE,IAAI,CAAC5O,kBAAkB,CAAC2E,GAAG,CAACiK,WAAW,CAAC;IAC5C,CAAC,CAAC;IACF,IAAI,CAACwB,eAAe,CAAC;MAAEpJ,CAAC,EAAEmJ,cAAc,CAACnJ,CAAC,GAAGiJ,YAAY;MAAEhJ,CAAC,EAAEkJ,cAAc,CAAClJ,CAAC,GAAGiJ;IAAa,CAAC,EAAEb,gBAAgB,CAAC;IAClH,IAAI,CAAC9B,MAAM,CAACwB,GAAG,CAAC,MAAM;MAClB,IAAI,CAACjB,OAAO,CAAC9C,IAAI,CAAC,IAAI,CAACgE,0BAA0B,CAACJ,WAAW,CAAC,CAAC;MAC/D,IAAI,CAAC5O,kBAAkB,CAAC2E,GAAG,CAACkC,SAAS,CAAC;IAC1C,CAAC,CAAC;EACN;EACApF,kBAAkBA,CAAC4O,aAAa,EAAE;IAC9B,MAAMjS,SAAS,GAAGiS,aAAa,GAAG,CAAC;IACnC,MAAMC,KAAK,GAAI,GAAElS,SAAU,MAAKA,SAAU,EAAC;IAC3C,OAAO;MACH,CAAC,aAAa,GAAG,IAAI,CAAC0D,SAAS,CAAC,CAAC,KAAK,YAAY,GAAGwO,KAAK,GAAG,GAAG;MAChE,CAAC,UAAU,GAAG,IAAI,CAACxO,SAAS,CAAC,CAAC,KAAK,UAAU,GAAGwO,KAAK,GAAG;IAC5D,CAAC;EACL;EACAnO,mBAAmBA,CAAC4J,IAAI,EAAE;IACtB,MAAMC,IAAI,GAAGD,IAAI,CAAC7J,aAAa,CAAC,CAAC;IACjC,IAAI8J,IAAI,KAAK,GAAG,EAAE;MACd,OAAOnF,SAAS;IACpB;IACA,OAAQ,GAAEmF,IAAI,CAACuE,OAAO,CAAC,CAAC,CAAE,IAAG,IAAI,CAACxN,IAAI,CAAC,CAAE,EAAC;EAC9C;EACAhB,YAAYA,CAACiK,IAAI,EAAE;IACf,OAAOA,IAAI,KAAK,GAAG,GAAGnF,SAAS,GAAGmF,IAAI;EAC1C;EACAgD,0BAA0BA,CAACJ,WAAW,EAAE;IACpC,OAAO;MACHxQ,SAAS,EAAEwQ,WAAW,GAAG,CAAC;MAC1B4B,KAAK,EAAE,IAAI,CAACb,eAAe,CAAC;IAChC,CAAC;EACL;EACAA,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAACzB,aAAa,CAAC,CAAC,CAACxR,GAAG,CAAEqP,IAAI,IAAKA,IAAI,CAAC7J,aAAa,CAAC,CAAC,CAAC;EACnE;EACAgN,sBAAsBA,CAACzH,UAAU,EAAE0H,qBAAqB,EAAEC,oBAAoB,EAAE;IAC5E,MAAMqB,iBAAiB,GAAG,IAAI,CAAChM,UAAU,CAACJ,aAAa,CAACqM,qBAAqB,CAAC,CAAC;IAC/E,MAAMC,SAAS,GAAG,IAAI,CAAC7O,SAAS,CAAC,CAAC,KAAK,YAAY,GAAG2O,iBAAiB,CAACG,KAAK,GAAGH,iBAAiB,CAACI,MAAM;IACxG,MAAMC,mBAAmB,GAAGH,SAAS,GAAG,CAAC,IAAI,CAACzC,aAAa,CAAC,CAAC,CAACnO,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC6C,UAAU,CAAC,CAAC;IAC7F;IACA,MAAMmO,0BAA0B,GAAG,IAAI,CAACjR,MAAM,CAAC,CAAC,CAACpD,GAAG,CAAEqP,IAAI,IAAK;MAC3D,IAAI,IAAI,CAAChJ,IAAI,CAAC,CAAC,KAAK,OAAO,EAAE;QACzB,OAAOgJ,IAAI,CAAC7J,aAAa,CAAC,CAAC;MAC/B,CAAC,MACI;QACD,MAAM8J,IAAI,GAAGD,IAAI,CAAC7J,aAAa,CAAC,CAAC;QACjC,IAAI8J,IAAI,KAAK,GAAG,EAAE;UACd,OAAOA,IAAI;QACf;QACA,OAAQA,IAAI,GAAG,GAAG,GAAI8E,mBAAmB;MAC7C;IACJ,CAAC,CAAC;IACF,MAAME,aAAa,GAAGjJ,IAAI,CAACkJ,GAAG,CAAC,CAAC,EAAEH,mBAAmB,GAAGrI,GAAG,CAACsI,0BAA0B,EAAG/E,IAAI,IAAMA,IAAI,KAAK,GAAG,GAAG,CAAC,GAAGA,IAAK,CAAC,CAAC;IAC7H,MAAMkF,eAAe,GAAGH,0BAA0B,CAACrU,GAAG,CAAEsP,IAAI,IAAMA,IAAI,KAAK,GAAG,GAAGgF,aAAa,GAAGhF,IAAK,CAAC;IACvG,OAAO;MACHvE,UAAU;MACV0H,qBAAqB;MACrBC,oBAAoB;MACpB8B,eAAe;MACfJ,mBAAmB;MACnBK,qBAAqB,EAAErI,QAAQ,CAAC,IAAI,CAAChJ,MAAM,CAAC,CAAC,EAAE,CAACiM,IAAI,EAAE/C,KAAK,KAAK;QAC5D,MAAMoI,eAAe,GAAIC,OAAO,IAAMA,OAAO,GAAG,GAAG,GAAIP,mBAAmB;QAC1E,MAAM5H,KAAK,GAAG,IAAI,CAACnG,IAAI,CAAC,CAAC,KAAK,OAAO,GAC/B;UACEuO,GAAG,EAAEvF,IAAI,CAACwF,kBAAkB,CAAC,CAAC;UAC9BN,GAAG,EAAElF,IAAI,CAACyF,kBAAkB,CAAC;QACjC,CAAC,GACC;UACEF,GAAG,EAAEF,eAAe,CAACrF,IAAI,CAACwF,kBAAkB,CAAC,CAAC,CAAC;UAC/CN,GAAG,EAAEG,eAAe,CAACrF,IAAI,CAACyF,kBAAkB,CAAC,CAAC;QAClD,CAAC;QACL,OAAO,CAACxI,KAAK,CAACyI,QAAQ,CAAC,CAAC,EAAEvI,KAAK,CAAC;MACpC,CAAC;IACL,CAAC;EACL;EACAqG,aAAaA,CAACD,SAAS,EAAED,gBAAgB,EAAE;IACvCC,SAAS,CAACM,cAAc,CAAC,CAAC;IAC1BN,SAAS,CAACO,eAAe,CAAC,CAAC;IAC3B,MAAM/H,QAAQ,GAAGxB,iBAAiB,CAACgJ,SAAS,CAAC;IAC7C,IAAI,CAACc,eAAe,CAACtI,QAAQ,EAAEuH,gBAAgB,CAAC;EACpD;EACAe,eAAeA,CAACtI,QAAQ,EAAEuH,gBAAgB,EAAE;IACxC,MAAMxH,UAAU,GAAGvB,iBAAiB,CAAC+I,gBAAgB,CAAC5H,UAAU,CAAC;IACjE,MAAMiK,YAAY,GAAG,IAAI,CAAC5P,SAAS,CAAC,CAAC,KAAK,YAAY,GAAGgG,QAAQ,CAACd,CAAC,GAAGa,UAAU,CAACb,CAAC,GAAGc,QAAQ,CAACb,CAAC,GAAGY,UAAU,CAACZ,CAAC;IAC9G,MAAM0K,MAAM,GAAG,IAAI,CAAC7P,SAAS,CAAC,CAAC,KAAK,YAAY,IAAI,IAAI,CAACY,GAAG,CAAC,CAAC,KAAK,KAAK,GAAG,CAACgP,YAAY,GAAGA,YAAY;IACvG,MAAME,iBAAiB,GAAGD,MAAM,GAAG,CAAC;IACpC;IACA,MAAME,gBAAgB,GAAG9J,IAAI,CAACC,GAAG,CAACD,IAAI,CAAC+J,KAAK,CAACH,MAAM,GAAG,IAAI,CAAC9O,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAACA,UAAU,CAAC,CAAC,CAAC;IAC7F;IACA,MAAMkP,mBAAmB,GAAG,CAAC,GAAG1C,gBAAgB,CAAC6B,eAAe,CAAC;IACjE;IACA;IACA,MAAMc,YAAY,GAAGD,mBAAmB,CAACrV,GAAG,CAAC,CAACuV,CAAC,EAAEjJ,KAAK,KAAKA,KAAK,CAAC;IACjE;IACA;IACA,MAAMkJ,wBAAwB,GAAG,IAAI,CAACpP,YAAY,CAAC,CAAC,GAC9C,CAACuM,gBAAgB,CAACF,qBAAqB,CAAC,GACxC6C,YAAY,CACTG,KAAK,CAAC,CAAC,EAAE9C,gBAAgB,CAACF,qBAAqB,GAAG,CAAC,CAAC,CACpD7S,MAAM,CAAE0M,KAAK,IAAK,IAAI,CAAClJ,MAAM,CAAC,CAAC,CAACkJ,KAAK,CAAC,CAACmF,OAAO,CAAC,CAAC,CAAC,CACjDiE,OAAO,CAAC,CAAC;IAClB,MAAMC,uBAAuB,GAAG,IAAI,CAACvP,YAAY,CAAC,CAAC,GAC7C,CAACuM,gBAAgB,CAACD,oBAAoB,CAAC,GACvC4C,YAAY,CAACG,KAAK,CAAC9C,gBAAgB,CAACD,oBAAoB,CAAC,CAAC9S,MAAM,CAAE0M,KAAK,IAAK,IAAI,CAAClJ,MAAM,CAAC,CAAC,CAACkJ,KAAK,CAAC,CAACmF,OAAO,CAAC,CAAC,CAAC;IACjH;IACA,MAAMmE,gCAAgC,GAAGV,iBAAiB,GAAGS,uBAAuB,GAAGH,wBAAwB;IAC/G,MAAMK,gCAAgC,GAAGX,iBAAiB,GAAGM,wBAAwB,GAAGG,uBAAuB;IAC/G,IAAIG,eAAe,GAAGX,gBAAgB;IACtC,IAAIY,uBAAuB,GAAG,CAAC;IAC/B,IAAIC,uBAAuB,GAAG,CAAC;IAC/B;IACA;IACA,OAAOF,eAAe,KAAK,CAAC,IACxBC,uBAAuB,GAAGH,gCAAgC,CAACvS,MAAM,IACjE2S,uBAAuB,GAAGH,gCAAgC,CAACxS,MAAM,EAAE;MACnE,MAAM4S,iBAAiB,GAAGL,gCAAgC,CAACG,uBAAuB,CAAC;MACnF,MAAMG,iBAAiB,GAAGL,gCAAgC,CAACG,uBAAuB,CAAC;MACnF,MAAMG,gBAAgB,GAAGd,mBAAmB,CAACY,iBAAiB,CAAC;MAC/D,MAAMG,gBAAgB,GAAGf,mBAAmB,CAACa,iBAAiB,CAAC;MAC/D,MAAMG,mBAAmB,GAAG1D,gBAAgB,CAAC8B,qBAAqB,CAACwB,iBAAiB,CAAC,CAACrB,GAAG;MACzF,MAAM0B,mBAAmB,GAAG3D,gBAAgB,CAAC8B,qBAAqB,CAACyB,iBAAiB,CAAC,CAAC3B,GAAG;MACzF;MACA;MACA,MAAMgC,iBAAiB,GAAGJ,gBAAgB,GAAGE,mBAAmB;MAChE,MAAMG,iBAAiB,GAAGF,mBAAmB,GAAGF,gBAAgB;MAChE,MAAMK,gBAAgB,GAAGpL,IAAI,CAACuJ,GAAG,CAAC2B,iBAAiB,EAAEC,iBAAiB,EAAEV,eAAe,CAAC;MACxF;MACAT,mBAAmB,CAACY,iBAAiB,CAAC,IAAIQ,gBAAgB;MAC1DpB,mBAAmB,CAACa,iBAAiB,CAAC,IAAIO,gBAAgB;MAC1DX,eAAe,IAAIW,gBAAgB;MACnC;MACA,IAAIpB,mBAAmB,CAACY,iBAAiB,CAAC,KAAKI,mBAAmB,EAAE;QAChEN,uBAAuB,EAAE;MAC7B;MACA;MACA,IAAIV,mBAAmB,CAACa,iBAAiB,CAAC,KAAKI,mBAAmB,EAAE;QAChEN,uBAAuB,EAAE;MAC7B;IACJ;IACA,IAAI,CAAC5S,MAAM,CAAC,CAAC,CAACsT,OAAO,CAAC,CAACrH,IAAI,EAAE/C,KAAK,KAAK;MACnC;MACA,IAAI+C,IAAI,CAAC7J,aAAa,CAAC,CAAC,KAAK,GAAG,EAAE;QAC9B;MACJ;MACA,IAAI,IAAI,CAACa,IAAI,CAAC,CAAC,KAAK,OAAO,EAAE;QACzBgJ,IAAI,CAAC7J,aAAa,CAACyC,GAAG,CAACoN,mBAAmB,CAAC/I,KAAK,CAAC,CAAC;MACtD,CAAC,MACI;QACD,MAAMqK,WAAW,GAAItB,mBAAmB,CAAC/I,KAAK,CAAC,GAAGqG,gBAAgB,CAACyB,mBAAmB,GAAI,GAAG;QAC7F;QACA/E,IAAI,CAAC7J,aAAa,CAACyC,GAAG,CAAC2O,UAAU,CAACD,WAAW,CAAC9C,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;MAC/D;IACJ,CAAC,CAAC;IACF,IAAI,CAAC9C,mBAAmB,CAACqC,IAAI,CAAC,IAAI,CAACd,0BAA0B,CAAC,IAAI,CAAChP,kBAAkB,CAAC,CAAC,CAAC,CAAC;EAC7F;EACAqO,8BAA8BA,CAAA,EAAG;IAC7B,MAAMkF,OAAO,GAAG,EAAE;IAClB,MAAMC,mBAAmB,GAAG/K,GAAG,CAAC,IAAI,CAACyF,aAAa,CAAC,CAAC,EAAGnC,IAAI,IAAK;MAC5D,MAAMC,IAAI,GAAGD,IAAI,CAAC7J,aAAa,CAAC,CAAC;MACjC,OAAO8J,IAAI,KAAK,GAAG,GAAG,CAAC,GAAGA,IAAI;IAClC,CAAC,CAAC;IACF,MAAMyH,iBAAiB,GAAG,IAAI,CAACvF,aAAa,CAAC,CAAC,CAACnO,MAAM;IACrD,IAAI2T,mBAAmB,GAAG,CAAC;IAC3B,IAAI,CAAC5T,MAAM,CAAC,CAAC,CAACsT,OAAO,CAAC,CAACrH,IAAI,EAAE/C,KAAK,EAAE4C,KAAK,KAAK;MAC1C,MAAM7I,IAAI,GAAG,IAAI,CAACA,IAAI,CAAC,CAAC;MACxB,MAAMmJ,QAAQ,GAAGH,IAAI,CAAC7J,aAAa,CAAC,CAAC;MACrC;MACA,IAAI,CAAC6J,IAAI,CAACoC,OAAO,CAAC,CAAC,EAAE;QACjBoF,OAAO,CAAC7O,IAAI,CAAC3B,IAAI,KAAK,SAAS,IAAImJ,QAAQ,KAAK,GAAG,GAAG,KAAK,GAAG,KAAK,CAAC;MACxE,CAAC,MACI;QACD,IAAInJ,IAAI,KAAK,OAAO,EAAE;UAClB,MAAM4Q,WAAW,GAAGzH,QAAQ,KAAK,GAAG,GAAG,KAAK,GAAI,GAAEA,QAAS,IAAG;UAC9DqH,OAAO,CAAC7O,IAAI,CAACiP,WAAW,CAAC;QAC7B,CAAC,MACI;UACD,MAAMN,WAAW,GAAGnH,QAAQ,KAAK,GAAG,GAAG,GAAG,GAAGsH,mBAAmB,GAAGtH,QAAQ;UAC3E,MAAMyH,WAAW,GAAI,GAAEN,WAAY,IAAG;UACtCE,OAAO,CAAC7O,IAAI,CAACiP,WAAW,CAAC;QAC7B;QACAD,mBAAmB,EAAE;MACzB;MACA,MAAME,UAAU,GAAG5K,KAAK,KAAK4C,KAAK,CAAC7L,MAAM,GAAG,CAAC;MAC7C,IAAI6T,UAAU,EAAE;QACZ;MACJ;MACA,MAAMC,qBAAqB,GAAGJ,iBAAiB,GAAGC,mBAAmB;MACrE;MACA;MACA,IAAI3H,IAAI,CAACoC,OAAO,CAAC,CAAC,IAAI0F,qBAAqB,GAAG,CAAC,EAAE;QAC7CN,OAAO,CAAC7O,IAAI,CAAE,GAAE,IAAI,CAAC9B,UAAU,CAAC,CAAE,IAAG,CAAC;MAC1C,CAAC,MACI;QACD2Q,OAAO,CAAC7O,IAAI,CAAC,KAAK,CAAC;MACvB;IACJ,CAAC,CAAC;IACF,OAAO,IAAI,CAAC5C,SAAS,CAAC,CAAC,KAAK,YAAY,GAAI,SAAQyR,OAAO,CAAChK,IAAI,CAAC,GAAG,CAAE,EAAC,GAAI,GAAEgK,OAAO,CAAChK,IAAI,CAAC,GAAG,CAAE,QAAO;EAC1G;EACAiF,6BAA6BA,CAAA,EAAG;IAC5B,MAAMsF,iBAAiB,GAAG,IAAI,CAAC5F,aAAa,CAAC,CAAC,CAACxR,GAAG,CAAEqP,IAAI,IAAK;MACzD,MAAMC,IAAI,GAAGD,IAAI,CAACC,IAAI,CAAC,CAAC;MACxB,OAAOA,IAAI,KAAK,MAAM,GAAG,GAAG,GAAGA,IAAI;IACvC,CAAC,CAAC;IACF,MAAM+H,OAAO,GAAGpI,aAAa,CAAC,IAAI,CAACuC,aAAa,CAAC,CAAC,EAAE,IAAI,CAACnL,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC;IACvE,IAAIgR,OAAO,EAAE;MACT,OAAOD,iBAAiB;IAC5B;IACA,MAAM/Q,IAAI,GAAG,IAAI,CAACA,IAAI,CAAC,CAAC;IACxB,IAAIA,IAAI,KAAK,SAAS,EAAE;MACpB;MACA,MAAMiR,kBAAkB,GAAG,GAAG,GAAGF,iBAAiB,CAAC/T,MAAM;MACzD,OAAO+T,iBAAiB,CAACpX,GAAG,CAAC,MAAMsX,kBAAkB,CAAC;IAC1D;IACA,IAAIjR,IAAI,KAAK,OAAO,EAAE;MAClB;MACA,MAAMkJ,aAAa,GAAG6H,iBAAiB,CAACxX,MAAM,CAAE4P,QAAQ,IAAKA,QAAQ,KAAK,GAAG,CAAC;MAC9E,IAAID,aAAa,CAAClM,MAAM,KAAK,CAAC,EAAE;QAC5B,OAAO,CAAC,GAAG,EAAE,GAAG+T,iBAAiB,CAAC3B,KAAK,CAAC,CAAC,CAAC,CAAC;MAC/C,CAAC,MACI;QACD,MAAM8B,kBAAkB,GAAGH,iBAAiB,CAACI,SAAS,CAAEhI,QAAQ,IAAKA,QAAQ,KAAK,GAAG,CAAC;QACtF,MAAMiI,aAAa,GAAG,GAAG;QACzB,OAAOL,iBAAiB,CAACpX,GAAG,CAAC,CAACwP,QAAQ,EAAElD,KAAK,KAAKA,KAAK,KAAKiL,kBAAkB,IAAI/H,QAAQ,KAAK,GAAG,GAAGA,QAAQ,GAAGiI,aAAa,CAAC;MAClI;IACJ;IACA,OAAO/J,iBAAiB,CAACrH,IAAI,EAAE,WAAW,CAAC;EAC/C;EACA;EAAmB;IAAS,IAAI,CAACoC,IAAI,YAAAiP,uBAAA/O,CAAA;MAAA,YAAAA,CAAA,IAAwF6H,cAAc;IAAA,CAAmD;EAAE;EAChM;EAAmB;IAAS,IAAI,CAACmH,IAAI,kBAzsB8E3Z,EAAE,CAAA4Z,iBAAA;MAAA9O,IAAA,EAysBJ0H,cAAc;MAAAzH,SAAA;MAAA8O,cAAA,WAAAC,8BAAA/V,EAAA,EAAAC,GAAA,EAAA+V,QAAA;QAAA,IAAAhW,EAAA;UAzsBZ/D,EAAE,CAAAga,oBAAA,CAAAD,QAAA,EAAA/V,GAAA,CAAAoB,MAAA,EAysB21DmN,mBAAmB;UAzsBh3DvS,EAAE,CAAAga,oBAAA,CAAAD,QAAA,EAAA/V,GAAA,CAAAiB,YAAA,EAysB07D8D,oBAAoB;QAAA;QAAA,IAAAhF,EAAA;UAzsBh9D/D,EAAE,CAAAia,cAAA;QAAA;MAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,4BAAArW,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF/D,EAAE,CAAAqa,cAAA,QAAArW,GAAA,CAAA2O,cAysBS,CAAC;UAzsBZ3S,EAAE,CAAAsa,UAAA,CAAAtW,GAAA,CAAAyO,kBAysBS,CAAC;QAAA;MAAA;MAAA5B,MAAA;QAAA3I,UAAA,GAzsBZlI,EAAE,CAAA8Q,YAAA,CAAAC,WAAA;QAAA5I,UAAA,GAAFnI,EAAE,CAAA8Q,YAAA,CAAAC,WAAA;QAAA9I,QAAA,GAAFjI,EAAE,CAAA8Q,YAAA,CAAAC,WAAA;QAAA9J,kBAAA,GAAFjH,EAAE,CAAA8Q,YAAA,CAAAC,WAAA;QAAA3J,SAAA,GAAFpH,EAAE,CAAA8Q,YAAA,CAAAC,WAAA;QAAA/I,GAAA,GAAFhI,EAAE,CAAA8Q,YAAA,CAAAC,WAAA;QAAA1I,IAAA,GAAFrI,EAAE,CAAA8Q,YAAA,CAAAC,WAAA;QAAA5J,eAAA,GAAFnH,EAAE,CAAA8Q,YAAA,CAAAC,WAAA;QAAA3I,YAAA,GAAFpI,EAAE,CAAA8Q,YAAA,CAAAC,WAAA;QAAAzI,aAAA,GAAFtI,EAAE,CAAA8Q,YAAA,CAAAC,WAAA;QAAA/J,sBAAA,GAAFhH,EAAE,CAAA8Q,YAAA,CAAAC,WAAA;MAAA;MAAAC,OAAA;QAAAiC,WAAA;QAAAC,cAAA;QAAAC,SAAA;QAAAC,OAAA;QAAAC,aAAA;MAAA;MAAAkH,QAAA;MAAAvP,UAAA;MAAAwP,QAAA,GAAFxa,EAAE,CAAAya,mBAAA;MAAAC,kBAAA,EAAA1X,GAAA;MAAA2X,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAA3V,QAAA,WAAA4V,wBAAA/W,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF/D,EAAE,CAAA+a,eAAA;UAAF/a,EAAE,CAAAgb,YAAA,EAysB8jE,CAAC;UAzsBjkEhb,EAAE,CAAAib,gBAAA,IAAAtT,6BAAA,oBAAF3H,EAAE,CAAAkb,yBAysBo2H,CAAC;QAAA;QAAA,IAAAnX,EAAA;UAzsBv2H/D,EAAE,CAAA+E,SAAA,CAysBo2H,CAAC;UAzsBv2H/E,EAAE,CAAAmb,UAAA,CAysBikEnX,GAAA,CAAAoB,MAAA,CAAO,CAA4xD,CAAC;QAAA;MAAA;MAAAgW,YAAA,GAA40FtY,OAAO,EAA2E+M,kCAAkC,EAAmQ+B,mCAAmC,EAAqH7O,gBAAgB;MAAAsY,MAAA;MAAAC,eAAA;IAAA,EAA+K;EAAE;AAC1/O;AACA;EAAA,QAAArQ,SAAA,oBAAAA,SAAA,KA3sBuHjL,EAAE,CAAAkL,iBAAA,CA2sB9BsH,cAAc,EAAc,CAAC;IAC5G1H,IAAI,EAAEzJ,SAAS;IACf8J,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,UAAU;MAAEmQ,OAAO,EAAE,CAACzY,OAAO,EAAE+M,kCAAkC,EAAE+B,mCAAmC,EAAE7O,gBAAgB,CAAC;MAAEwX,QAAQ,EAAE,SAAS;MAAEe,eAAe,EAAEha,uBAAuB,CAACka,MAAM;MAAEtW,QAAQ,EAAE,m0DAAm0D;MAAEmW,MAAM,EAAE,CAAC,ixFAAixF;IAAE,CAAC;EACp0J,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAE5I,kBAAkB,EAAE,CAAC;MAC7D3H,IAAI,EAAEvJ,WAAW;MACjB4J,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAEwH,cAAc,EAAE,CAAC;MACjB7H,IAAI,EAAEvJ,WAAW;MACjB4J,IAAI,EAAE,CAAC,KAAK;IAChB,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMsQ,yBAAyB,GAAIjK,QAAQ,IAAKA,QAAQ,KAAKrF,SAAS,IAAIqF,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,GAAG,GAAG,GAAG,GAAG,CAACA,QAAQ;AACjI,MAAMkK,iBAAiB,GAAIlK,QAAQ,IAAKiK,yBAAyB,CAACjK,QAAQ,CAAC;AAC3E,MAAMmK,yBAAyB,GAAInK,QAAQ,IAAKiK,yBAAyB,CAACjK,QAAQ,CAAC;AAEnF,MAAMoK,kBAAkB,CAAC;EACrB5S,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC6S,KAAK,GAAG3b,MAAM,CAACsS,cAAc,CAAC;IACnC,IAAI,CAAClB,IAAI,GAAG3Q,KAAK,CAAC,MAAM,EAAE;MAAEqS,SAAS,EAAE0I;IAAkB,CAAC,CAAC;IAC3D,IAAI,CAACpU,OAAO,GAAG3G,KAAK,CAAC,GAAG,EAAE;MAAEqS,SAAS,EAAE2I;IAA0B,CAAC,CAAC;IACnE,IAAI,CAACpU,OAAO,GAAG5G,KAAK,CAAC,GAAG,EAAE;MAAEqS,SAAS,EAAE2I;IAA0B,CAAC,CAAC;IACnE,IAAI,CAACG,QAAQ,GAAGnb,KAAK,CAAC,KAAK,EAAE;MAAEqS,SAAS,EAAE7R;IAAiB,CAAC,CAAC;IAC7D,IAAI,CAACsS,OAAO,GAAG9S,KAAK,CAAC,IAAI,EAAE;MAAEqS,SAAS,EAAE7R;IAAiB,CAAC,CAAC;IAC3D;AACR;AACA;IACQ,IAAI,CAACqG,aAAa,GAAGsH,YAAY;IACjC;IACA;IACAxO,QAAQ,CAAC,MAAM;MACX,IAAI,CAAC,IAAI,CAACmT,OAAO,CAAC,CAAC,EAAE;QACjB,OAAO,CAAC;MACZ;MACA,MAAMsI,YAAY,GAAG,IAAI,CAACF,KAAK,CAACrI,aAAa,CAAC,CAAC,CAACgG,SAAS,CAAEnI,IAAI,IAAKA,IAAI,KAAK,IAAI,CAAC;MAClF,OAAO,IAAI,CAACwK,KAAK,CAAChI,yBAAyB,CAAC,CAAC,CAACkI,YAAY,CAAC;IAC/D,CAAC,CAAC,CAAC;IACH;AACR;AACA;IACQ,IAAI,CAAClF,kBAAkB,GAAGvW,QAAQ,CAAC,MAAM,IAAI,CAAC0b,gBAAgB,CAAC,CAAC,CAAC;IACjE;AACR;AACA;IACQ,IAAI,CAAClF,kBAAkB,GAAGxW,QAAQ,CAAC,MAAM,IAAI,CAAC2b,gBAAgB,CAAC,CAAC,CAAC;IACjE,IAAI,CAAC3N,KAAK,GAAGhO,QAAQ,CAAC,MAAM,IAAI,CAACub,KAAK,CAACzW,MAAM,CAAC,CAAC,CAACoU,SAAS,CAAEnI,IAAI,IAAKA,IAAI,KAAK,IAAI,CAAC,CAAC;IACnF,IAAI,CAAC6K,WAAW,GAAG5b,QAAQ,CAAC,MAAM,IAAI,CAACgO,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACvD,IAAI,CAACoE,WAAW,GAAGpS,QAAQ,CAAC,MAAMmO,mBAAmB,CAAC;MAClD,CAAC,eAAe,GAAG,IAAI;MACvB,CAAC,QAAQ,GAAG,IAAI,CAACgF,OAAO,CAAC,CAAC,IAAI,IAAI,CAACjM,aAAa,CAAC,CAAC,KAAK,IAAI,CAACqP,kBAAkB,CAAC,CAAC;MAChF,CAAC,QAAQ,GAAG,IAAI,CAACpD,OAAO,CAAC,CAAC,IAAI,IAAI,CAACjM,aAAa,CAAC,CAAC,KAAK,IAAI,CAACsP,kBAAkB,CAAC,CAAC;MAChF,CAAC,WAAW,GAAG,CAAC,IAAI,CAACrD,OAAO,CAAC;IACjC,CAAC,CAAC,CAAC;EACP;EACA,IAAIhB,kBAAkBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACC,WAAW,CAAC,CAAC;EAC7B;EACA,IAAIyJ,0BAA0BA,CAAA,EAAG;IAC7B,OAAO,IAAI,CAACN,KAAK,CAACzU,SAAS,CAAC,CAAC,KAAK,YAAY,GAAI,GAAE,IAAI,CAAC8U,WAAW,CAAC,CAAE,MAAK,IAAI,CAACA,WAAW,CAAC,CAAE,EAAC,GAAG/P,SAAS;EAChH;EACA,IAAIiQ,uBAAuBA,CAAA,EAAG;IAC1B,OAAO,IAAI,CAACP,KAAK,CAACzU,SAAS,CAAC,CAAC,KAAK,UAAU,GAAI,GAAE,IAAI,CAAC8U,WAAW,CAAC,CAAE,MAAK,IAAI,CAACA,WAAW,CAAC,CAAE,EAAC,GAAG/P,SAAS;EAC9G;EACA,IAAIkQ,wBAAwBA,CAAA,EAAG;IAC3B,OAAO,IAAI,CAACR,KAAK,CAACjI,WAAW,CAAC,CAAC,GAAG,UAAU,GAAGzH,SAAS;EAC5D;EACA6P,gBAAgBA,CAAA,EAAG;IACf,MAAMM,cAAc,GAAG,CAAC;IACxB,IAAI,CAAC,IAAI,CAAC7I,OAAO,CAAC,CAAC,EAAE;MACjB,OAAO6I,cAAc;IACzB;IACA,MAAMhV,OAAO,GAAG,IAAI,CAACiV,qBAAqB,CAAC,IAAI,CAACjV,OAAO,EAAEgV,cAAc,CAAC;IACxE,MAAMhL,IAAI,GAAG,IAAI,CAACA,IAAI,CAAC,CAAC;IACxB,IAAIA,IAAI,KAAK,GAAG,IAAIA,IAAI,KAAK,MAAM,IAAIA,IAAI,GAAGhK,OAAO,EAAE;MACnD,IAAIlG,SAAS,CAAC,CAAC,EAAE;QACbqQ,OAAO,CAACC,IAAI,CAAC,+CAA+C,CAAC;MACjE;MACA,OAAO4K,cAAc;IACzB;IACA,OAAOhV,OAAO;EAClB;EACA2U,gBAAgBA,CAAA,EAAG;IACf,MAAMO,cAAc,GAAGC,QAAQ;IAC/B,IAAI,CAAC,IAAI,CAAChJ,OAAO,CAAC,CAAC,EAAE;MACjB,OAAO+I,cAAc;IACzB;IACA,MAAMjV,OAAO,GAAG,IAAI,CAACgV,qBAAqB,CAAC,IAAI,CAAChV,OAAO,EAAEiV,cAAc,CAAC;IACxE,MAAMlL,IAAI,GAAG,IAAI,CAACA,IAAI,CAAC,CAAC;IACxB,IAAIA,IAAI,KAAK,GAAG,IAAIA,IAAI,KAAK,MAAM,IAAIA,IAAI,GAAG/J,OAAO,EAAE;MACnD,IAAInG,SAAS,CAAC,CAAC,EAAE;QACbqQ,OAAO,CAACC,IAAI,CAAC,8CAA8C,CAAC;MAChE;MACA,OAAO8K,cAAc;IACzB;IACA,OAAOjV,OAAO;EAClB;EACAgV,qBAAqBA,CAACG,kBAAkB,EAAEC,mBAAmB,EAAE;IAC3D,MAAMrL,IAAI,GAAG,IAAI,CAACA,IAAI,CAAC,CAAC;IACxB,MAAMwK,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC,CAAC;IAChC,MAAMc,YAAY,GAAGF,kBAAkB,CAAC,CAAC;IACzC,IAAIZ,QAAQ,EAAE;MACV,IAAI1a,SAAS,CAAC,CAAC,IAAIwb,YAAY,KAAK,GAAG,EAAE;QACrCnL,OAAO,CAACC,IAAI,CAAC,+CAA+C,CAAC;MACjE;MACA,IAAIJ,IAAI,KAAK,GAAG,IAAIA,IAAI,KAAK,MAAM,EAAE;QACjC,IAAIlQ,SAAS,CAAC,CAAC,EAAE;UACbqQ,OAAO,CAACC,IAAI,CAAE,wEAAuE,CAAC;QAC1F;QACA,OAAOiL,mBAAmB;MAC9B;MACA,OAAOrL,IAAI;IACf;IACA,IAAIsL,YAAY,KAAK,GAAG,EAAE;MACtB,OAAOD,mBAAmB;IAC9B;IACA,IAAIrL,IAAI,KAAK,GAAG,IAAIA,IAAI,KAAK,MAAM,EAAE;MACjC,IAAIlQ,SAAS,CAAC,CAAC,EAAE;QACbqQ,OAAO,CAACC,IAAI,CAAC,4DAA4D,CAAC;MAC9E;MACA,OAAOiL,mBAAmB;IAC9B;IACA,OAAOC,YAAY;EACvB;EACA;EAAmB;IAAS,IAAI,CAACnS,IAAI,YAAAoS,2BAAAlS,CAAA;MAAA,YAAAA,CAAA,IAAwFiR,kBAAkB;IAAA,CAAmD;EAAE;EACpM;EAAmB;IAAS,IAAI,CAACjC,IAAI,kBAt0B8E3Z,EAAE,CAAA4Z,iBAAA;MAAA9O,IAAA,EAs0BJ8Q,kBAAkB;MAAA7Q,SAAA;MAAAmP,QAAA;MAAAC,YAAA,WAAA2C,gCAAA/Y,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAt0BhB/D,EAAE,CAAAsa,UAAA,CAAAtW,GAAA,CAAAyO,kBAs0Ba,CAAC;UAt0BhBzS,EAAE,CAAA+c,WAAA,gBAAA/Y,GAAA,CAAAmY,0BAs0Ba,CAAC,aAAAnY,GAAA,CAAAoY,uBAAD,CAAC,aAAApY,GAAA,CAAAqY,wBAAD,CAAC;QAAA;MAAA;MAAAxL,MAAA;QAAAS,IAAA,GAt0BhBtR,EAAE,CAAA8Q,YAAA,CAAAC,WAAA;QAAAzJ,OAAA,GAAFtH,EAAE,CAAA8Q,YAAA,CAAAC,WAAA;QAAAxJ,OAAA,GAAFvH,EAAE,CAAA8Q,YAAA,CAAAC,WAAA;QAAA+K,QAAA,GAAF9b,EAAE,CAAA8Q,YAAA,CAAAC,WAAA;QAAA0C,OAAA,GAAFzT,EAAE,CAAA8Q,YAAA,CAAAC,WAAA;MAAA;MAAAwJ,QAAA;MAAAvP,UAAA;MAAAwP,QAAA,GAAFxa,EAAE,CAAAgd,kBAAA,CAs0Bi6B,CAC9gC;QACIpU,OAAO,EAAE2J,mBAAmB;QAC5B0K,WAAW,EAAErB;MACjB,CAAC,CACJ,GA30B8G5b,EAAE,CAAAya,mBAAA;MAAAC,kBAAA,EAAA1X,GAAA;MAAA2X,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAA3V,QAAA,WAAAgY,4BAAAnZ,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF/D,EAAE,CAAA+a,eAAA;UAAF/a,EAAE,CAAAgb,YAAA,EA20BlC,CAAC;UA30B+Bhb,EAAE,CAAAoE,UAAA,IAAA0D,yCAAA,gBA20BL,CAAC;QAAA;QAAA,IAAA/D,EAAA;UA30BE/D,EAAE,CAAA+E,SAAA,CA20BqC,CAAC;UA30BxC/E,EAAE,CAAA0H,aAAA,IAAA1D,GAAA,CAAA6X,KAAA,CAAAjI,WAAA,WA20BqC,CAAC;QAAA;MAAA;MAAAyH,MAAA;MAAAC,eAAA;IAAA,EAAsP;EAAE;AACvZ;AACA;EAAA,QAAArQ,SAAA,oBAAAA,SAAA,KA70BuHjL,EAAE,CAAAkL,iBAAA,CA60B9B0Q,kBAAkB,EAAc,CAAC;IAChH9Q,IAAI,EAAEzJ,SAAS;IACf8J,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,eAAe;MAAEJ,UAAU,EAAE,IAAI;MAAEuP,QAAQ,EAAE,aAAa;MAAErI,SAAS,EAAE,CAC9E;QACItJ,OAAO,EAAE2J,mBAAmB;QAC5B0K,WAAW,EAAErB;MACjB,CAAC,CACJ;MAAEN,eAAe,EAAEha,uBAAuB,CAACka,MAAM;MAAEtW,QAAQ,EAAE,oGAAoG;MAAEmW,MAAM,EAAE,CAAC,gLAAgL;IAAE,CAAC;EAC5W,CAAC,CAAC,QAAkB;IAAE5I,kBAAkB,EAAE,CAAC;MACnC3H,IAAI,EAAEvJ,WAAW;MACjB4J,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAEgR,0BAA0B,EAAE,CAAC;MAC7BrR,IAAI,EAAEvJ,WAAW;MACjB4J,IAAI,EAAE,CAAC,mBAAmB;IAC9B,CAAC,CAAC;IAAEiR,uBAAuB,EAAE,CAAC;MAC1BtR,IAAI,EAAEvJ,WAAW;MACjB4J,IAAI,EAAE,CAAC,gBAAgB;IAC3B,CAAC,CAAC;IAAEkR,wBAAwB,EAAE,CAAC;MAC3BvR,IAAI,EAAEvJ,WAAW;MACjB4J,IAAI,EAAE,CAAC,gBAAgB;IAC3B,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMgS,kBAAkB,CAAC;EACrB,kBAAmB;IAAS,IAAI,CAAC1S,IAAI,YAAA2S,2BAAAzS,CAAA;MAAA,YAAAA,CAAA,IAAwFwS,kBAAkB;IAAA,CAAkD;EAAE;EACnM;EAAmB;IAAS,IAAI,CAACE,IAAI,kBAr2B8Erd,EAAE,CAAAsd,gBAAA;MAAAxS,IAAA,EAq2BSqS,kBAAkB;MAAA5B,OAAA,GAAY/I,cAAc,EAClKoJ,kBAAkB,EAClB7S,oBAAoB,EACpBuC,8BAA8B,EAC9BI,mCAAmC;MAAA6R,OAAA,GAAa/K,cAAc,EAC9DoJ,kBAAkB,EAClB7S,oBAAoB,EACpBuC,8BAA8B,EAC9BI,mCAAmC;IAAA,EAAI;EAAE;EACjD;EAAmB;IAAS,IAAI,CAAC8R,IAAI,kBA92B8Exd,EAAE,CAAAyd,gBAAA,IA82B8B;EAAE;AACzJ;AACA;EAAA,QAAAxS,SAAA,oBAAAA,SAAA,KAh3BuHjL,EAAE,CAAAkL,iBAAA,CAg3B9BiS,kBAAkB,EAAc,CAAC;IAChHrS,IAAI,EAAEtJ,QAAQ;IACd2J,IAAI,EAAE,CAAC;MACCoQ,OAAO,EAAE,CACL/I,cAAc,EACdoJ,kBAAkB,EAClB7S,oBAAoB,EACpBuC,8BAA8B,EAC9BI,mCAAmC,CACtC;MACD6R,OAAO,EAAE,CACL/K,cAAc,EACdoJ,kBAAkB,EAClB7S,oBAAoB,EACpBuC,8BAA8B,EAC9BI,mCAAmC;IAE3C,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASyR,kBAAkB,EAAEvB,kBAAkB,EAAEpJ,cAAc,EAAEzJ,oBAAoB,EAAEuC,8BAA8B,EAAEI,mCAAmC,EAAEhD,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}