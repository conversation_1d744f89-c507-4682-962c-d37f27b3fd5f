{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/source/ai-hub/WebApp/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { TestBed } from '@angular/core/testing';\nimport { AppComponent } from './app.component';\ndescribe('AppComponent', () => {\n  beforeEach( /*#__PURE__*/_asyncToGenerator(function* () {\n    yield TestBed.configureTestingModule({\n      imports: [AppComponent]\n    }).compileComponents();\n  }));\n  it('should create the app', () => {\n    const fixture = TestBed.createComponent(AppComponent);\n    const app = fixture.componentInstance;\n    expect(app).toBeTruthy();\n  });\n  it(`should have the 'chatapp' title`, () => {\n    const fixture = TestBed.createComponent(AppComponent);\n    const app = fixture.componentInstance;\n    expect(app.title).toEqual('chatapp');\n  });\n  it('should render title', () => {\n    const fixture = TestBed.createComponent(AppComponent);\n    fixture.detectChanges();\n    const compiled = fixture.nativeElement;\n    expect(compiled.querySelector('h1')?.textContent).toContain('Hello, chatapp');\n  });\n});", "map": {"version": 3, "names": ["TestBed", "AppComponent", "describe", "beforeEach", "_asyncToGenerator", "configureTestingModule", "imports", "compileComponents", "it", "fixture", "createComponent", "app", "componentInstance", "expect", "toBeTruthy", "title", "toEqual", "detectChanges", "compiled", "nativeElement", "querySelector", "textContent", "toContain"], "sources": ["C:\\Users\\<USER>\\source\\ai-hub\\WebApp\\src\\app\\app.component.spec.ts"], "sourcesContent": ["import { TestBed } from '@angular/core/testing';\r\nimport { AppComponent } from './app.component';\r\n\r\ndescribe('AppComponent', () => {\r\n  beforeEach(async () => {\r\n    await TestBed.configureTestingModule({\r\n      imports: [AppComponent],\r\n    }).compileComponents();\r\n  });\r\n\r\n  it('should create the app', () => {\r\n    const fixture = TestBed.createComponent(AppComponent);\r\n    const app = fixture.componentInstance;\r\n    expect(app).toBeTruthy();\r\n  });\r\n\r\n  it(`should have the 'chatapp' title`, () => {\r\n    const fixture = TestBed.createComponent(AppComponent);\r\n    const app = fixture.componentInstance;\r\n    expect(app.title).toEqual('chatapp');\r\n  });\r\n\r\n  it('should render title', () => {\r\n    const fixture = TestBed.createComponent(AppComponent);\r\n    fixture.detectChanges();\r\n    const compiled = fixture.nativeElement as HTMLElement;\r\n    expect(compiled.querySelector('h1')?.textContent).toContain('Hello, chatapp');\r\n  });\r\n});\r\n"], "mappings": ";AAAA,SAASA,OAAO,QAAQ,uBAAuB;AAC/C,SAASC,YAAY,QAAQ,iBAAiB;AAE9CC,QAAQ,CAAC,cAAc,EAAE,MAAK;EAC5BC,UAAU,eAAAC,iBAAA,CAAC,aAAW;IACpB,MAAMJ,OAAO,CAACK,sBAAsB,CAAC;MACnCC,OAAO,EAAE,CAACL,YAAY;KACvB,CAAC,CAACM,iBAAiB,EAAE;EACxB,CAAC,EAAC;EAEFC,EAAE,CAAC,uBAAuB,EAAE,MAAK;IAC/B,MAAMC,OAAO,GAAGT,OAAO,CAACU,eAAe,CAACT,YAAY,CAAC;IACrD,MAAMU,GAAG,GAAGF,OAAO,CAACG,iBAAiB;IACrCC,MAAM,CAACF,GAAG,CAAC,CAACG,UAAU,EAAE;EAC1B,CAAC,CAAC;EAEFN,EAAE,CAAC,iCAAiC,EAAE,MAAK;IACzC,MAAMC,OAAO,GAAGT,OAAO,CAACU,eAAe,CAACT,YAAY,CAAC;IACrD,MAAMU,GAAG,GAAGF,OAAO,CAACG,iBAAiB;IACrCC,MAAM,CAACF,GAAG,CAACI,KAAK,CAAC,CAACC,OAAO,CAAC,SAAS,CAAC;EACtC,CAAC,CAAC;EAEFR,EAAE,CAAC,qBAAqB,EAAE,MAAK;IAC7B,MAAMC,OAAO,GAAGT,OAAO,CAACU,eAAe,CAACT,YAAY,CAAC;IACrDQ,OAAO,CAACQ,aAAa,EAAE;IACvB,MAAMC,QAAQ,GAAGT,OAAO,CAACU,aAA4B;IACrDN,MAAM,CAACK,QAAQ,CAACE,aAAa,CAAC,IAAI,CAAC,EAAEC,WAAW,CAAC,CAACC,SAAS,CAAC,gBAAgB,CAAC;EAC/E,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}