{"ast": null, "code": "import { merge } from 'lodash-es';\nimport Emitter from '../core/emitter.js';\nimport BaseTheme, { BaseTooltip } from './base.js';\nimport LinkBlot from '../formats/link.js';\nimport { Range } from '../core/selection.js';\nimport icons from '../ui/icons.js';\nimport Quill from '../core/quill.js';\nconst TOOLBAR_CONFIG = [[{\n  header: ['1', '2', '3', false]\n}], ['bold', 'italic', 'underline', 'link'], [{\n  list: 'ordered'\n}, {\n  list: 'bullet'\n}], ['clean']];\nclass SnowTooltip extends BaseTooltip {\n  static TEMPLATE = ['<a class=\"ql-preview\" rel=\"noopener noreferrer\" target=\"_blank\" href=\"about:blank\"></a>', '<input type=\"text\" data-formula=\"e=mc^2\" data-link=\"https://quilljs.com\" data-video=\"Embed URL\">', '<a class=\"ql-action\"></a>', '<a class=\"ql-remove\"></a>'].join('');\n  preview = this.root.querySelector('a.ql-preview');\n  listen() {\n    super.listen();\n    // @ts-expect-error Fix me later\n    this.root.querySelector('a.ql-action').addEventListener('click', event => {\n      if (this.root.classList.contains('ql-editing')) {\n        this.save();\n      } else {\n        // @ts-expect-error Fix me later\n        this.edit('link', this.preview.textContent);\n      }\n      event.preventDefault();\n    });\n    // @ts-expect-error Fix me later\n    this.root.querySelector('a.ql-remove').addEventListener('click', event => {\n      if (this.linkRange != null) {\n        const range = this.linkRange;\n        this.restoreFocus();\n        this.quill.formatText(range, 'link', false, Emitter.sources.USER);\n        delete this.linkRange;\n      }\n      event.preventDefault();\n      this.hide();\n    });\n    this.quill.on(Emitter.events.SELECTION_CHANGE, (range, oldRange, source) => {\n      if (range == null) return;\n      if (range.length === 0 && source === Emitter.sources.USER) {\n        const [link, offset] = this.quill.scroll.descendant(LinkBlot, range.index);\n        if (link != null) {\n          this.linkRange = new Range(range.index - offset, link.length());\n          const preview = LinkBlot.formats(link.domNode);\n          // @ts-expect-error Fix me later\n          this.preview.textContent = preview;\n          // @ts-expect-error Fix me later\n          this.preview.setAttribute('href', preview);\n          this.show();\n          const bounds = this.quill.getBounds(this.linkRange);\n          if (bounds != null) {\n            this.position(bounds);\n          }\n          return;\n        }\n      } else {\n        delete this.linkRange;\n      }\n      this.hide();\n    });\n  }\n  show() {\n    super.show();\n    this.root.removeAttribute('data-mode');\n  }\n}\nclass SnowTheme extends BaseTheme {\n  constructor(quill, options) {\n    if (options.modules.toolbar != null && options.modules.toolbar.container == null) {\n      options.modules.toolbar.container = TOOLBAR_CONFIG;\n    }\n    super(quill, options);\n    this.quill.container.classList.add('ql-snow');\n  }\n  extendToolbar(toolbar) {\n    if (toolbar.container != null) {\n      toolbar.container.classList.add('ql-snow');\n      this.buildButtons(toolbar.container.querySelectorAll('button'), icons);\n      this.buildPickers(toolbar.container.querySelectorAll('select'), icons);\n      // @ts-expect-error\n      this.tooltip = new SnowTooltip(this.quill, this.options.bounds);\n      if (toolbar.container.querySelector('.ql-link')) {\n        this.quill.keyboard.addBinding({\n          key: 'k',\n          shortKey: true\n        }, (_range, context) => {\n          toolbar.handlers.link.call(toolbar, !context.format.link);\n        });\n      }\n    }\n  }\n}\nSnowTheme.DEFAULTS = merge({}, BaseTheme.DEFAULTS, {\n  modules: {\n    toolbar: {\n      handlers: {\n        link(value) {\n          if (value) {\n            const range = this.quill.getSelection();\n            if (range == null || range.length === 0) return;\n            let preview = this.quill.getText(range);\n            if (/^\\S+@\\S+\\.\\S+$/.test(preview) && preview.indexOf('mailto:') !== 0) {\n              preview = `mailto:${preview}`;\n            }\n            // @ts-expect-error\n            const {\n              tooltip\n            } = this.quill.theme;\n            tooltip.edit('link', preview);\n          } else {\n            this.quill.format('link', false, Quill.sources.USER);\n          }\n        }\n      }\n    }\n  }\n});\nexport default SnowTheme;", "map": {"version": 3, "names": ["merge", "Emitter", "BaseTheme", "BaseTooltip", "LinkBlot", "Range", "icons", "<PERSON><PERSON><PERSON>", "TOOLBAR_CONFIG", "header", "list", "SnowTooltip", "TEMPLATE", "join", "preview", "root", "querySelector", "listen", "addEventListener", "event", "classList", "contains", "save", "edit", "textContent", "preventDefault", "linkRange", "range", "restoreFocus", "quill", "formatText", "sources", "USER", "hide", "on", "events", "SELECTION_CHANGE", "oldRange", "source", "length", "link", "offset", "scroll", "descendant", "index", "formats", "domNode", "setAttribute", "show", "bounds", "getBounds", "position", "removeAttribute", "SnowTheme", "constructor", "options", "modules", "toolbar", "container", "add", "extendToolbar", "buildButtons", "querySelectorAll", "buildPickers", "tooltip", "keyboard", "addBinding", "key", "<PERSON><PERSON><PERSON>", "_range", "context", "handlers", "call", "format", "DEFAULTS", "value", "getSelection", "getText", "test", "indexOf", "theme"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/quill/themes/snow.js"], "sourcesContent": ["import { merge } from 'lodash-es';\nimport Emitter from '../core/emitter.js';\nimport BaseTheme, { BaseTooltip } from './base.js';\nimport LinkBlot from '../formats/link.js';\nimport { Range } from '../core/selection.js';\nimport icons from '../ui/icons.js';\nimport Quill from '../core/quill.js';\nconst TOOLBAR_CONFIG = [[{\n  header: ['1', '2', '3', false]\n}], ['bold', 'italic', 'underline', 'link'], [{\n  list: 'ordered'\n}, {\n  list: 'bullet'\n}], ['clean']];\nclass SnowTooltip extends BaseTooltip {\n  static TEMPLATE = ['<a class=\"ql-preview\" rel=\"noopener noreferrer\" target=\"_blank\" href=\"about:blank\"></a>', '<input type=\"text\" data-formula=\"e=mc^2\" data-link=\"https://quilljs.com\" data-video=\"Embed URL\">', '<a class=\"ql-action\"></a>', '<a class=\"ql-remove\"></a>'].join('');\n  preview = this.root.querySelector('a.ql-preview');\n  listen() {\n    super.listen();\n    // @ts-expect-error Fix me later\n    this.root.querySelector('a.ql-action').addEventListener('click', event => {\n      if (this.root.classList.contains('ql-editing')) {\n        this.save();\n      } else {\n        // @ts-expect-error Fix me later\n        this.edit('link', this.preview.textContent);\n      }\n      event.preventDefault();\n    });\n    // @ts-expect-error Fix me later\n    this.root.querySelector('a.ql-remove').addEventListener('click', event => {\n      if (this.linkRange != null) {\n        const range = this.linkRange;\n        this.restoreFocus();\n        this.quill.formatText(range, 'link', false, Emitter.sources.USER);\n        delete this.linkRange;\n      }\n      event.preventDefault();\n      this.hide();\n    });\n    this.quill.on(Emitter.events.SELECTION_CHANGE, (range, oldRange, source) => {\n      if (range == null) return;\n      if (range.length === 0 && source === Emitter.sources.USER) {\n        const [link, offset] = this.quill.scroll.descendant(LinkBlot, range.index);\n        if (link != null) {\n          this.linkRange = new Range(range.index - offset, link.length());\n          const preview = LinkBlot.formats(link.domNode);\n          // @ts-expect-error Fix me later\n          this.preview.textContent = preview;\n          // @ts-expect-error Fix me later\n          this.preview.setAttribute('href', preview);\n          this.show();\n          const bounds = this.quill.getBounds(this.linkRange);\n          if (bounds != null) {\n            this.position(bounds);\n          }\n          return;\n        }\n      } else {\n        delete this.linkRange;\n      }\n      this.hide();\n    });\n  }\n  show() {\n    super.show();\n    this.root.removeAttribute('data-mode');\n  }\n}\nclass SnowTheme extends BaseTheme {\n  constructor(quill, options) {\n    if (options.modules.toolbar != null && options.modules.toolbar.container == null) {\n      options.modules.toolbar.container = TOOLBAR_CONFIG;\n    }\n    super(quill, options);\n    this.quill.container.classList.add('ql-snow');\n  }\n  extendToolbar(toolbar) {\n    if (toolbar.container != null) {\n      toolbar.container.classList.add('ql-snow');\n      this.buildButtons(toolbar.container.querySelectorAll('button'), icons);\n      this.buildPickers(toolbar.container.querySelectorAll('select'), icons);\n      // @ts-expect-error\n      this.tooltip = new SnowTooltip(this.quill, this.options.bounds);\n      if (toolbar.container.querySelector('.ql-link')) {\n        this.quill.keyboard.addBinding({\n          key: 'k',\n          shortKey: true\n        }, (_range, context) => {\n          toolbar.handlers.link.call(toolbar, !context.format.link);\n        });\n      }\n    }\n  }\n}\nSnowTheme.DEFAULTS = merge({}, BaseTheme.DEFAULTS, {\n  modules: {\n    toolbar: {\n      handlers: {\n        link(value) {\n          if (value) {\n            const range = this.quill.getSelection();\n            if (range == null || range.length === 0) return;\n            let preview = this.quill.getText(range);\n            if (/^\\S+@\\S+\\.\\S+$/.test(preview) && preview.indexOf('mailto:') !== 0) {\n              preview = `mailto:${preview}`;\n            }\n            // @ts-expect-error\n            const {\n              tooltip\n            } = this.quill.theme;\n            tooltip.edit('link', preview);\n          } else {\n            this.quill.format('link', false, Quill.sources.USER);\n          }\n        }\n      }\n    }\n  }\n});\nexport default SnowTheme;\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,WAAW;AACjC,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,SAAS,IAAIC,WAAW,QAAQ,WAAW;AAClD,OAAOC,QAAQ,MAAM,oBAAoB;AACzC,SAASC,KAAK,QAAQ,sBAAsB;AAC5C,OAAOC,KAAK,MAAM,gBAAgB;AAClC,OAAOC,KAAK,MAAM,kBAAkB;AACpC,MAAMC,cAAc,GAAG,CAAC,CAAC;EACvBC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK;AAC/B,CAAC,CAAC,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,MAAM,CAAC,EAAE,CAAC;EAC5CC,IAAI,EAAE;AACR,CAAC,EAAE;EACDA,IAAI,EAAE;AACR,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;AACd,MAAMC,WAAW,SAASR,WAAW,CAAC;EACpC,OAAOS,QAAQ,GAAG,CAAC,yFAAyF,EAAE,kGAAkG,EAAE,2BAA2B,EAAE,2BAA2B,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;EACpRC,OAAO,GAAG,IAAI,CAACC,IAAI,CAACC,aAAa,CAAC,cAAc,CAAC;EACjDC,MAAMA,CAAA,EAAG;IACP,KAAK,CAACA,MAAM,CAAC,CAAC;IACd;IACA,IAAI,CAACF,IAAI,CAACC,aAAa,CAAC,aAAa,CAAC,CAACE,gBAAgB,CAAC,OAAO,EAAEC,KAAK,IAAI;MACxE,IAAI,IAAI,CAACJ,IAAI,CAACK,SAAS,CAACC,QAAQ,CAAC,YAAY,CAAC,EAAE;QAC9C,IAAI,CAACC,IAAI,CAAC,CAAC;MACb,CAAC,MAAM;QACL;QACA,IAAI,CAACC,IAAI,CAAC,MAAM,EAAE,IAAI,CAACT,OAAO,CAACU,WAAW,CAAC;MAC7C;MACAL,KAAK,CAACM,cAAc,CAAC,CAAC;IACxB,CAAC,CAAC;IACF;IACA,IAAI,CAACV,IAAI,CAACC,aAAa,CAAC,aAAa,CAAC,CAACE,gBAAgB,CAAC,OAAO,EAAEC,KAAK,IAAI;MACxE,IAAI,IAAI,CAACO,SAAS,IAAI,IAAI,EAAE;QAC1B,MAAMC,KAAK,GAAG,IAAI,CAACD,SAAS;QAC5B,IAAI,CAACE,YAAY,CAAC,CAAC;QACnB,IAAI,CAACC,KAAK,CAACC,UAAU,CAACH,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE1B,OAAO,CAAC8B,OAAO,CAACC,IAAI,CAAC;QACjE,OAAO,IAAI,CAACN,SAAS;MACvB;MACAP,KAAK,CAACM,cAAc,CAAC,CAAC;MACtB,IAAI,CAACQ,IAAI,CAAC,CAAC;IACb,CAAC,CAAC;IACF,IAAI,CAACJ,KAAK,CAACK,EAAE,CAACjC,OAAO,CAACkC,MAAM,CAACC,gBAAgB,EAAE,CAACT,KAAK,EAAEU,QAAQ,EAAEC,MAAM,KAAK;MAC1E,IAAIX,KAAK,IAAI,IAAI,EAAE;MACnB,IAAIA,KAAK,CAACY,MAAM,KAAK,CAAC,IAAID,MAAM,KAAKrC,OAAO,CAAC8B,OAAO,CAACC,IAAI,EAAE;QACzD,MAAM,CAACQ,IAAI,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACZ,KAAK,CAACa,MAAM,CAACC,UAAU,CAACvC,QAAQ,EAAEuB,KAAK,CAACiB,KAAK,CAAC;QAC1E,IAAIJ,IAAI,IAAI,IAAI,EAAE;UAChB,IAAI,CAACd,SAAS,GAAG,IAAIrB,KAAK,CAACsB,KAAK,CAACiB,KAAK,GAAGH,MAAM,EAAED,IAAI,CAACD,MAAM,CAAC,CAAC,CAAC;UAC/D,MAAMzB,OAAO,GAAGV,QAAQ,CAACyC,OAAO,CAACL,IAAI,CAACM,OAAO,CAAC;UAC9C;UACA,IAAI,CAAChC,OAAO,CAACU,WAAW,GAAGV,OAAO;UAClC;UACA,IAAI,CAACA,OAAO,CAACiC,YAAY,CAAC,MAAM,EAAEjC,OAAO,CAAC;UAC1C,IAAI,CAACkC,IAAI,CAAC,CAAC;UACX,MAAMC,MAAM,GAAG,IAAI,CAACpB,KAAK,CAACqB,SAAS,CAAC,IAAI,CAACxB,SAAS,CAAC;UACnD,IAAIuB,MAAM,IAAI,IAAI,EAAE;YAClB,IAAI,CAACE,QAAQ,CAACF,MAAM,CAAC;UACvB;UACA;QACF;MACF,CAAC,MAAM;QACL,OAAO,IAAI,CAACvB,SAAS;MACvB;MACA,IAAI,CAACO,IAAI,CAAC,CAAC;IACb,CAAC,CAAC;EACJ;EACAe,IAAIA,CAAA,EAAG;IACL,KAAK,CAACA,IAAI,CAAC,CAAC;IACZ,IAAI,CAACjC,IAAI,CAACqC,eAAe,CAAC,WAAW,CAAC;EACxC;AACF;AACA,MAAMC,SAAS,SAASnD,SAAS,CAAC;EAChCoD,WAAWA,CAACzB,KAAK,EAAE0B,OAAO,EAAE;IAC1B,IAAIA,OAAO,CAACC,OAAO,CAACC,OAAO,IAAI,IAAI,IAAIF,OAAO,CAACC,OAAO,CAACC,OAAO,CAACC,SAAS,IAAI,IAAI,EAAE;MAChFH,OAAO,CAACC,OAAO,CAACC,OAAO,CAACC,SAAS,GAAGlD,cAAc;IACpD;IACA,KAAK,CAACqB,KAAK,EAAE0B,OAAO,CAAC;IACrB,IAAI,CAAC1B,KAAK,CAAC6B,SAAS,CAACtC,SAAS,CAACuC,GAAG,CAAC,SAAS,CAAC;EAC/C;EACAC,aAAaA,CAACH,OAAO,EAAE;IACrB,IAAIA,OAAO,CAACC,SAAS,IAAI,IAAI,EAAE;MAC7BD,OAAO,CAACC,SAAS,CAACtC,SAAS,CAACuC,GAAG,CAAC,SAAS,CAAC;MAC1C,IAAI,CAACE,YAAY,CAACJ,OAAO,CAACC,SAAS,CAACI,gBAAgB,CAAC,QAAQ,CAAC,EAAExD,KAAK,CAAC;MACtE,IAAI,CAACyD,YAAY,CAACN,OAAO,CAACC,SAAS,CAACI,gBAAgB,CAAC,QAAQ,CAAC,EAAExD,KAAK,CAAC;MACtE;MACA,IAAI,CAAC0D,OAAO,GAAG,IAAIrD,WAAW,CAAC,IAAI,CAACkB,KAAK,EAAE,IAAI,CAAC0B,OAAO,CAACN,MAAM,CAAC;MAC/D,IAAIQ,OAAO,CAACC,SAAS,CAAC1C,aAAa,CAAC,UAAU,CAAC,EAAE;QAC/C,IAAI,CAACa,KAAK,CAACoC,QAAQ,CAACC,UAAU,CAAC;UAC7BC,GAAG,EAAE,GAAG;UACRC,QAAQ,EAAE;QACZ,CAAC,EAAE,CAACC,MAAM,EAAEC,OAAO,KAAK;UACtBb,OAAO,CAACc,QAAQ,CAAC/B,IAAI,CAACgC,IAAI,CAACf,OAAO,EAAE,CAACa,OAAO,CAACG,MAAM,CAACjC,IAAI,CAAC;QAC3D,CAAC,CAAC;MACJ;IACF;EACF;AACF;AACAa,SAAS,CAACqB,QAAQ,GAAG1E,KAAK,CAAC,CAAC,CAAC,EAAEE,SAAS,CAACwE,QAAQ,EAAE;EACjDlB,OAAO,EAAE;IACPC,OAAO,EAAE;MACPc,QAAQ,EAAE;QACR/B,IAAIA,CAACmC,KAAK,EAAE;UACV,IAAIA,KAAK,EAAE;YACT,MAAMhD,KAAK,GAAG,IAAI,CAACE,KAAK,CAAC+C,YAAY,CAAC,CAAC;YACvC,IAAIjD,KAAK,IAAI,IAAI,IAAIA,KAAK,CAACY,MAAM,KAAK,CAAC,EAAE;YACzC,IAAIzB,OAAO,GAAG,IAAI,CAACe,KAAK,CAACgD,OAAO,CAAClD,KAAK,CAAC;YACvC,IAAI,gBAAgB,CAACmD,IAAI,CAAChE,OAAO,CAAC,IAAIA,OAAO,CAACiE,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;cACtEjE,OAAO,GAAI,UAASA,OAAQ,EAAC;YAC/B;YACA;YACA,MAAM;cACJkD;YACF,CAAC,GAAG,IAAI,CAACnC,KAAK,CAACmD,KAAK;YACpBhB,OAAO,CAACzC,IAAI,CAAC,MAAM,EAAET,OAAO,CAAC;UAC/B,CAAC,MAAM;YACL,IAAI,CAACe,KAAK,CAAC4C,MAAM,CAAC,MAAM,EAAE,KAAK,EAAElE,KAAK,CAACwB,OAAO,CAACC,IAAI,CAAC;UACtD;QACF;MACF;IACF;EACF;AACF,CAAC,CAAC;AACF,eAAeqB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}