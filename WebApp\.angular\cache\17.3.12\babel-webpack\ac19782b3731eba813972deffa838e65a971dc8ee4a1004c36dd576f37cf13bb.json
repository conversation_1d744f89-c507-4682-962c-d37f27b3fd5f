{"ast": null, "code": "// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\n/** Error thrown when an HTTP request fails. */\nexport class HttpError extends Error {\n  /** Constructs a new instance of {@link @microsoft/signalr.HttpError}.\r\n   *\r\n   * @param {string} errorMessage A descriptive error message.\r\n   * @param {number} statusCode The HTTP status code represented by this error.\r\n   */\n  constructor(errorMessage, statusCode) {\n    const trueProto = new.target.prototype;\n    super(`${errorMessage}: Status code '${statusCode}'`);\n    this.statusCode = statusCode;\n    // Workaround issue in Typescript compiler\n    // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\n    this.__proto__ = trueProto;\n  }\n}\n/** Error thrown when a timeout elapses. */\nexport class TimeoutError extends Error {\n  /** Constructs a new instance of {@link @microsoft/signalr.TimeoutError}.\r\n   *\r\n   * @param {string} errorMessage A descriptive error message.\r\n   */\n  constructor(errorMessage = \"A timeout occurred.\") {\n    const trueProto = new.target.prototype;\n    super(errorMessage);\n    // Workaround issue in Typescript compiler\n    // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\n    this.__proto__ = trueProto;\n  }\n}\n/** Error thrown when an action is aborted. */\nexport class AbortError extends Error {\n  /** Constructs a new instance of {@link AbortError}.\r\n   *\r\n   * @param {string} errorMessage A descriptive error message.\r\n   */\n  constructor(errorMessage = \"An abort occurred.\") {\n    const trueProto = new.target.prototype;\n    super(errorMessage);\n    // Workaround issue in Typescript compiler\n    // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\n    this.__proto__ = trueProto;\n  }\n}\n/** Error thrown when the selected transport is unsupported by the browser. */\n/** @private */\nexport class UnsupportedTransportError extends Error {\n  /** Constructs a new instance of {@link @microsoft/signalr.UnsupportedTransportError}.\r\n   *\r\n   * @param {string} message A descriptive error message.\r\n   * @param {HttpTransportType} transport The {@link @microsoft/signalr.HttpTransportType} this error occurred on.\r\n   */\n  constructor(message, transport) {\n    const trueProto = new.target.prototype;\n    super(message);\n    this.transport = transport;\n    this.errorType = 'UnsupportedTransportError';\n    // Workaround issue in Typescript compiler\n    // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\n    this.__proto__ = trueProto;\n  }\n}\n/** Error thrown when the selected transport is disabled by the browser. */\n/** @private */\nexport class DisabledTransportError extends Error {\n  /** Constructs a new instance of {@link @microsoft/signalr.DisabledTransportError}.\r\n   *\r\n   * @param {string} message A descriptive error message.\r\n   * @param {HttpTransportType} transport The {@link @microsoft/signalr.HttpTransportType} this error occurred on.\r\n   */\n  constructor(message, transport) {\n    const trueProto = new.target.prototype;\n    super(message);\n    this.transport = transport;\n    this.errorType = 'DisabledTransportError';\n    // Workaround issue in Typescript compiler\n    // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\n    this.__proto__ = trueProto;\n  }\n}\n/** Error thrown when the selected transport cannot be started. */\n/** @private */\nexport class FailedToStartTransportError extends Error {\n  /** Constructs a new instance of {@link @microsoft/signalr.FailedToStartTransportError}.\r\n   *\r\n   * @param {string} message A descriptive error message.\r\n   * @param {HttpTransportType} transport The {@link @microsoft/signalr.HttpTransportType} this error occurred on.\r\n   */\n  constructor(message, transport) {\n    const trueProto = new.target.prototype;\n    super(message);\n    this.transport = transport;\n    this.errorType = 'FailedToStartTransportError';\n    // Workaround issue in Typescript compiler\n    // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\n    this.__proto__ = trueProto;\n  }\n}\n/** Error thrown when the negotiation with the server failed to complete. */\n/** @private */\nexport class FailedToNegotiateWithServerError extends Error {\n  /** Constructs a new instance of {@link @microsoft/signalr.FailedToNegotiateWithServerError}.\r\n   *\r\n   * @param {string} message A descriptive error message.\r\n   */\n  constructor(message) {\n    const trueProto = new.target.prototype;\n    super(message);\n    this.errorType = 'FailedToNegotiateWithServerError';\n    // Workaround issue in Typescript compiler\n    // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\n    this.__proto__ = trueProto;\n  }\n}\n/** Error thrown when multiple errors have occurred. */\n/** @private */\nexport class AggregateErrors extends Error {\n  /** Constructs a new instance of {@link @microsoft/signalr.AggregateErrors}.\r\n   *\r\n   * @param {string} message A descriptive error message.\r\n   * @param {Error[]} innerErrors The collection of errors this error is aggregating.\r\n   */\n  constructor(message, innerErrors) {\n    const trueProto = new.target.prototype;\n    super(message);\n    this.innerErrors = innerErrors;\n    // Workaround issue in Typescript compiler\n    // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\n    this.__proto__ = trueProto;\n  }\n}", "map": {"version": 3, "names": ["HttpError", "Error", "constructor", "errorMessage", "statusCode", "trueProto", "new", "target", "prototype", "__proto__", "TimeoutError", "AbortError", "UnsupportedTransportError", "message", "transport", "errorType", "DisabledTransportError", "FailedToStartTransportError", "FailedToNegotiateWithServerError", "AggregateErrors", "innerErrors"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/@microsoft/signalr/dist/esm/Errors.js"], "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n/** Error thrown when an HTTP request fails. */\r\nexport class HttpError extends Error {\r\n    /** Constructs a new instance of {@link @microsoft/signalr.HttpError}.\r\n     *\r\n     * @param {string} errorMessage A descriptive error message.\r\n     * @param {number} statusCode The HTTP status code represented by this error.\r\n     */\r\n    constructor(errorMessage, statusCode) {\r\n        const trueProto = new.target.prototype;\r\n        super(`${errorMessage}: Status code '${statusCode}'`);\r\n        this.statusCode = statusCode;\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n/** Error thrown when a timeout elapses. */\r\nexport class TimeoutError extends Error {\r\n    /** Constructs a new instance of {@link @microsoft/signalr.TimeoutError}.\r\n     *\r\n     * @param {string} errorMessage A descriptive error message.\r\n     */\r\n    constructor(errorMessage = \"A timeout occurred.\") {\r\n        const trueProto = new.target.prototype;\r\n        super(errorMessage);\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n/** Error thrown when an action is aborted. */\r\nexport class AbortError extends Error {\r\n    /** Constructs a new instance of {@link AbortError}.\r\n     *\r\n     * @param {string} errorMessage A descriptive error message.\r\n     */\r\n    constructor(errorMessage = \"An abort occurred.\") {\r\n        const trueProto = new.target.prototype;\r\n        super(errorMessage);\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n/** Error thrown when the selected transport is unsupported by the browser. */\r\n/** @private */\r\nexport class UnsupportedTransportError extends Error {\r\n    /** Constructs a new instance of {@link @microsoft/signalr.UnsupportedTransportError}.\r\n     *\r\n     * @param {string} message A descriptive error message.\r\n     * @param {HttpTransportType} transport The {@link @microsoft/signalr.HttpTransportType} this error occurred on.\r\n     */\r\n    constructor(message, transport) {\r\n        const trueProto = new.target.prototype;\r\n        super(message);\r\n        this.transport = transport;\r\n        this.errorType = 'UnsupportedTransportError';\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n/** Error thrown when the selected transport is disabled by the browser. */\r\n/** @private */\r\nexport class DisabledTransportError extends Error {\r\n    /** Constructs a new instance of {@link @microsoft/signalr.DisabledTransportError}.\r\n     *\r\n     * @param {string} message A descriptive error message.\r\n     * @param {HttpTransportType} transport The {@link @microsoft/signalr.HttpTransportType} this error occurred on.\r\n     */\r\n    constructor(message, transport) {\r\n        const trueProto = new.target.prototype;\r\n        super(message);\r\n        this.transport = transport;\r\n        this.errorType = 'DisabledTransportError';\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n/** Error thrown when the selected transport cannot be started. */\r\n/** @private */\r\nexport class FailedToStartTransportError extends Error {\r\n    /** Constructs a new instance of {@link @microsoft/signalr.FailedToStartTransportError}.\r\n     *\r\n     * @param {string} message A descriptive error message.\r\n     * @param {HttpTransportType} transport The {@link @microsoft/signalr.HttpTransportType} this error occurred on.\r\n     */\r\n    constructor(message, transport) {\r\n        const trueProto = new.target.prototype;\r\n        super(message);\r\n        this.transport = transport;\r\n        this.errorType = 'FailedToStartTransportError';\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n/** Error thrown when the negotiation with the server failed to complete. */\r\n/** @private */\r\nexport class FailedToNegotiateWithServerError extends Error {\r\n    /** Constructs a new instance of {@link @microsoft/signalr.FailedToNegotiateWithServerError}.\r\n     *\r\n     * @param {string} message A descriptive error message.\r\n     */\r\n    constructor(message) {\r\n        const trueProto = new.target.prototype;\r\n        super(message);\r\n        this.errorType = 'FailedToNegotiateWithServerError';\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n/** Error thrown when multiple errors have occurred. */\r\n/** @private */\r\nexport class AggregateErrors extends Error {\r\n    /** Constructs a new instance of {@link @microsoft/signalr.AggregateErrors}.\r\n     *\r\n     * @param {string} message A descriptive error message.\r\n     * @param {Error[]} innerErrors The collection of errors this error is aggregating.\r\n     */\r\n    constructor(message, innerErrors) {\r\n        const trueProto = new.target.prototype;\r\n        super(message);\r\n        this.innerErrors = innerErrors;\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n"], "mappings": "AAAA;AACA;AACA;AACA,OAAO,MAAMA,SAAS,SAASC,KAAK,CAAC;EACjC;AACJ;AACA;AACA;AACA;EACIC,WAAWA,CAACC,YAAY,EAAEC,UAAU,EAAE;IAClC,MAAMC,SAAS,GAAGC,GAAG,CAACC,MAAM,CAACC,SAAS;IACtC,KAAK,CAAE,GAAEL,YAAa,kBAAiBC,UAAW,GAAE,CAAC;IACrD,IAAI,CAACA,UAAU,GAAGA,UAAU;IAC5B;IACA;IACA,IAAI,CAACK,SAAS,GAAGJ,SAAS;EAC9B;AACJ;AACA;AACA,OAAO,MAAMK,YAAY,SAAST,KAAK,CAAC;EACpC;AACJ;AACA;AACA;EACIC,WAAWA,CAACC,YAAY,GAAG,qBAAqB,EAAE;IAC9C,MAAME,SAAS,GAAGC,GAAG,CAACC,MAAM,CAACC,SAAS;IACtC,KAAK,CAACL,YAAY,CAAC;IACnB;IACA;IACA,IAAI,CAACM,SAAS,GAAGJ,SAAS;EAC9B;AACJ;AACA;AACA,OAAO,MAAMM,UAAU,SAASV,KAAK,CAAC;EAClC;AACJ;AACA;AACA;EACIC,WAAWA,CAACC,YAAY,GAAG,oBAAoB,EAAE;IAC7C,MAAME,SAAS,GAAGC,GAAG,CAACC,MAAM,CAACC,SAAS;IACtC,KAAK,CAACL,YAAY,CAAC;IACnB;IACA;IACA,IAAI,CAACM,SAAS,GAAGJ,SAAS;EAC9B;AACJ;AACA;AACA;AACA,OAAO,MAAMO,yBAAyB,SAASX,KAAK,CAAC;EACjD;AACJ;AACA;AACA;AACA;EACIC,WAAWA,CAACW,OAAO,EAAEC,SAAS,EAAE;IAC5B,MAAMT,SAAS,GAAGC,GAAG,CAACC,MAAM,CAACC,SAAS;IACtC,KAAK,CAACK,OAAO,CAAC;IACd,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,SAAS,GAAG,2BAA2B;IAC5C;IACA;IACA,IAAI,CAACN,SAAS,GAAGJ,SAAS;EAC9B;AACJ;AACA;AACA;AACA,OAAO,MAAMW,sBAAsB,SAASf,KAAK,CAAC;EAC9C;AACJ;AACA;AACA;AACA;EACIC,WAAWA,CAACW,OAAO,EAAEC,SAAS,EAAE;IAC5B,MAAMT,SAAS,GAAGC,GAAG,CAACC,MAAM,CAACC,SAAS;IACtC,KAAK,CAACK,OAAO,CAAC;IACd,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,SAAS,GAAG,wBAAwB;IACzC;IACA;IACA,IAAI,CAACN,SAAS,GAAGJ,SAAS;EAC9B;AACJ;AACA;AACA;AACA,OAAO,MAAMY,2BAA2B,SAAShB,KAAK,CAAC;EACnD;AACJ;AACA;AACA;AACA;EACIC,WAAWA,CAACW,OAAO,EAAEC,SAAS,EAAE;IAC5B,MAAMT,SAAS,GAAGC,GAAG,CAACC,MAAM,CAACC,SAAS;IACtC,KAAK,CAACK,OAAO,CAAC;IACd,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,SAAS,GAAG,6BAA6B;IAC9C;IACA;IACA,IAAI,CAACN,SAAS,GAAGJ,SAAS;EAC9B;AACJ;AACA;AACA;AACA,OAAO,MAAMa,gCAAgC,SAASjB,KAAK,CAAC;EACxD;AACJ;AACA;AACA;EACIC,WAAWA,CAACW,OAAO,EAAE;IACjB,MAAMR,SAAS,GAAGC,GAAG,CAACC,MAAM,CAACC,SAAS;IACtC,KAAK,CAACK,OAAO,CAAC;IACd,IAAI,CAACE,SAAS,GAAG,kCAAkC;IACnD;IACA;IACA,IAAI,CAACN,SAAS,GAAGJ,SAAS;EAC9B;AACJ;AACA;AACA;AACA,OAAO,MAAMc,eAAe,SAASlB,KAAK,CAAC;EACvC;AACJ;AACA;AACA;AACA;EACIC,WAAWA,CAACW,OAAO,EAAEO,WAAW,EAAE;IAC9B,MAAMf,SAAS,GAAGC,GAAG,CAACC,MAAM,CAACC,SAAS;IACtC,KAAK,CAACK,OAAO,CAAC;IACd,IAAI,CAACO,WAAW,GAAGA,WAAW;IAC9B;IACA;IACA,IAAI,CAACX,SAAS,GAAGJ,SAAS;EAC9B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}