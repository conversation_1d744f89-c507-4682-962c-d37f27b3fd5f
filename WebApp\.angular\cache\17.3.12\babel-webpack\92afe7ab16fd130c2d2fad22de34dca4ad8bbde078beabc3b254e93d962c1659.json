{"ast": null, "code": "import { __decorate } from 'tslib';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport { TemplatePortal } from '@angular/cdk/portal';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, Directive, Input, Output, NgModule, Host, Optional, TemplateRef, Component, ViewEncapsulation, ChangeDetectionStrategy, ViewChild, Injectable } from '@angular/core';\nimport { Subject, BehaviorSubject, merge, fromEvent, EMPTY, combineLatest, Subscription } from 'rxjs';\nimport { mapTo, map, switchMap, filter, auditTime, distinctUntilChanged, takeUntil, first } from 'rxjs/operators';\nimport * as i1 from 'ng-zorro-antd/core/config';\nimport { WithConfig } from 'ng-zorro-antd/core/config';\nimport { POSITION_MAP } from 'ng-zorro-antd/core/overlay';\nimport { InputBoolean } from 'ng-zorro-antd/core/util';\nimport * as i2 from '@angular/cdk/overlay';\nimport { ConnectionPositionPair } from '@angular/cdk/overlay';\nimport * as i3 from '@angular/cdk/platform';\nimport * as i1$2 from 'ng-zorro-antd/menu';\nimport { MenuService, NzIsMenuInsideDropDownToken, NzMenuModule } from 'ng-zorro-antd/menu';\nimport * as i1$1 from 'ng-zorro-antd/button';\nimport { NgClass, NgStyle } from '@angular/common';\nimport { slideMotion } from 'ng-zorro-antd/core/animation';\nimport * as i3$1 from 'ng-zorro-antd/core/no-animation';\nimport { NzNoAnimationDirective } from 'ng-zorro-antd/core/no-animation';\nimport * as i2$1 from '@angular/cdk/bidi';\nconst _c0 = [\"*\"];\nfunction NzDropdownMenuComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 0);\n    i0.ɵɵlistener(\"@slideMotion.done\", function NzDropdownMenuComponent_ng_template_0_Template_div_animation_slideMotion_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAnimationEvent($event));\n    })(\"mouseenter\", function NzDropdownMenuComponent_ng_template_0_Template_div_mouseenter_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.setMouseState(true));\n    })(\"mouseleave\", function NzDropdownMenuComponent_ng_template_0_Template_div_mouseleave_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.setMouseState(false));\n    });\n    i0.ɵɵprojection(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"ant-dropdown-rtl\", ctx_r1.dir === \"rtl\");\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.nzOverlayClassName)(\"ngStyle\", ctx_r1.nzOverlayStyle)(\"@slideMotion\", undefined)(\"@.disabled\", !!(ctx_r1.noAnimation == null ? null : ctx_r1.noAnimation.nzNoAnimation))(\"nzNoAnimation\", ctx_r1.noAnimation == null ? null : ctx_r1.noAnimation.nzNoAnimation);\n  }\n}\nconst NZ_CONFIG_MODULE_NAME = 'dropDown';\nconst listOfPositions = [POSITION_MAP.bottomLeft, POSITION_MAP.bottomRight, POSITION_MAP.topRight, POSITION_MAP.topLeft];\nclass NzDropDownDirective {\n  setDropdownMenuValue(key, value) {\n    if (this.nzDropdownMenu) {\n      this.nzDropdownMenu.setValue(key, value);\n    }\n  }\n  constructor(nzConfigService, elementRef, overlay, renderer, viewContainerRef, platform) {\n    this.nzConfigService = nzConfigService;\n    this.elementRef = elementRef;\n    this.overlay = overlay;\n    this.renderer = renderer;\n    this.viewContainerRef = viewContainerRef;\n    this.platform = platform;\n    this._nzModuleName = NZ_CONFIG_MODULE_NAME;\n    this.overlayRef = null;\n    this.destroy$ = new Subject();\n    this.positionStrategy = this.overlay.position().flexibleConnectedTo(this.elementRef.nativeElement).withLockedPosition().withTransformOriginOn('.ant-dropdown');\n    this.inputVisible$ = new BehaviorSubject(false);\n    this.nzTrigger$ = new BehaviorSubject('hover');\n    this.overlayClose$ = new Subject();\n    this.nzDropdownMenu = null;\n    this.nzTrigger = 'hover';\n    this.nzMatchWidthElement = null;\n    this.nzBackdrop = false;\n    this.nzClickHide = true;\n    this.nzDisabled = false;\n    this.nzVisible = false;\n    this.nzOverlayClassName = '';\n    this.nzOverlayStyle = {};\n    this.nzPlacement = 'bottomLeft';\n    this.nzVisibleChange = new EventEmitter();\n  }\n  ngAfterViewInit() {\n    if (this.nzDropdownMenu) {\n      const nativeElement = this.elementRef.nativeElement;\n      /** host mouse state **/\n      const hostMouseState$ = merge(fromEvent(nativeElement, 'mouseenter').pipe(mapTo(true)), fromEvent(nativeElement, 'mouseleave').pipe(mapTo(false)));\n      /** menu mouse state **/\n      const menuMouseState$ = this.nzDropdownMenu.mouseState$;\n      /** merged mouse state **/\n      const mergedMouseState$ = merge(menuMouseState$, hostMouseState$);\n      /** host click state **/\n      const hostClickState$ = fromEvent(nativeElement, 'click').pipe(map(() => !this.nzVisible));\n      /** visible state switch by nzTrigger **/\n      const visibleStateByTrigger$ = this.nzTrigger$.pipe(switchMap(trigger => {\n        if (trigger === 'hover') {\n          return mergedMouseState$;\n        } else if (trigger === 'click') {\n          return hostClickState$;\n        } else {\n          return EMPTY;\n        }\n      }));\n      const descendantMenuItemClick$ = this.nzDropdownMenu.descendantMenuItemClick$.pipe(filter(() => this.nzClickHide), mapTo(false));\n      const domTriggerVisible$ = merge(visibleStateByTrigger$, descendantMenuItemClick$, this.overlayClose$).pipe(filter(() => !this.nzDisabled));\n      const visible$ = merge(this.inputVisible$, domTriggerVisible$);\n      combineLatest([visible$, this.nzDropdownMenu.isChildSubMenuOpen$]).pipe(map(([visible, sub]) => visible || sub), auditTime(150), distinctUntilChanged(), filter(() => this.platform.isBrowser), takeUntil(this.destroy$)).subscribe(visible => {\n        const element = this.nzMatchWidthElement ? this.nzMatchWidthElement.nativeElement : nativeElement;\n        const triggerWidth = element.getBoundingClientRect().width;\n        if (this.nzVisible !== visible) {\n          this.nzVisibleChange.emit(visible);\n        }\n        this.nzVisible = visible;\n        if (visible) {\n          /** set up overlayRef **/\n          if (!this.overlayRef) {\n            /** new overlay **/\n            this.overlayRef = this.overlay.create({\n              positionStrategy: this.positionStrategy,\n              minWidth: triggerWidth,\n              disposeOnNavigation: true,\n              hasBackdrop: this.nzBackdrop && this.nzTrigger === 'click',\n              scrollStrategy: this.overlay.scrollStrategies.reposition()\n            });\n            merge(this.overlayRef.backdropClick(), this.overlayRef.detachments(), this.overlayRef.outsidePointerEvents().pipe(filter(e => !this.elementRef.nativeElement.contains(e.target))), this.overlayRef.keydownEvents().pipe(filter(e => e.keyCode === ESCAPE && !hasModifierKey(e)))).pipe(takeUntil(this.destroy$)).subscribe(() => {\n              this.overlayClose$.next(false);\n            });\n          } else {\n            /** update overlay config **/\n            const overlayConfig = this.overlayRef.getConfig();\n            overlayConfig.minWidth = triggerWidth;\n          }\n          /** open dropdown with animation **/\n          this.positionStrategy.withPositions([POSITION_MAP[this.nzPlacement], ...listOfPositions]);\n          /** reset portal if needed **/\n          if (!this.portal || this.portal.templateRef !== this.nzDropdownMenu.templateRef) {\n            this.portal = new TemplatePortal(this.nzDropdownMenu.templateRef, this.viewContainerRef);\n          }\n          this.overlayRef.attach(this.portal);\n        } else {\n          /** detach overlayRef if needed **/\n          if (this.overlayRef) {\n            this.overlayRef.detach();\n          }\n        }\n      });\n      this.nzDropdownMenu.animationStateChange$.pipe(takeUntil(this.destroy$)).subscribe(event => {\n        if (event.toState === 'void') {\n          if (this.overlayRef) {\n            this.overlayRef.dispose();\n          }\n          this.overlayRef = null;\n        }\n      });\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next(true);\n    this.destroy$.complete();\n    if (this.overlayRef) {\n      this.overlayRef.dispose();\n      this.overlayRef = null;\n    }\n  }\n  ngOnChanges(changes) {\n    const {\n      nzVisible,\n      nzDisabled,\n      nzOverlayClassName,\n      nzOverlayStyle,\n      nzTrigger\n    } = changes;\n    if (nzTrigger) {\n      this.nzTrigger$.next(this.nzTrigger);\n    }\n    if (nzVisible) {\n      this.inputVisible$.next(this.nzVisible);\n    }\n    if (nzDisabled) {\n      const nativeElement = this.elementRef.nativeElement;\n      if (this.nzDisabled) {\n        this.renderer.setAttribute(nativeElement, 'disabled', '');\n        this.inputVisible$.next(false);\n      } else {\n        this.renderer.removeAttribute(nativeElement, 'disabled');\n      }\n    }\n    if (nzOverlayClassName) {\n      this.setDropdownMenuValue('nzOverlayClassName', this.nzOverlayClassName);\n    }\n    if (nzOverlayStyle) {\n      this.setDropdownMenuValue('nzOverlayStyle', this.nzOverlayStyle);\n    }\n  }\n  static {\n    this.ɵfac = function NzDropDownDirective_Factory(t) {\n      return new (t || NzDropDownDirective)(i0.ɵɵdirectiveInject(i1.NzConfigService), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i2.Overlay), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i3.Platform));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzDropDownDirective,\n      selectors: [[\"\", \"nz-dropdown\", \"\"]],\n      hostAttrs: [1, \"ant-dropdown-trigger\"],\n      inputs: {\n        nzDropdownMenu: \"nzDropdownMenu\",\n        nzTrigger: \"nzTrigger\",\n        nzMatchWidthElement: \"nzMatchWidthElement\",\n        nzBackdrop: \"nzBackdrop\",\n        nzClickHide: \"nzClickHide\",\n        nzDisabled: \"nzDisabled\",\n        nzVisible: \"nzVisible\",\n        nzOverlayClassName: \"nzOverlayClassName\",\n        nzOverlayStyle: \"nzOverlayStyle\",\n        nzPlacement: \"nzPlacement\"\n      },\n      outputs: {\n        nzVisibleChange: \"nzVisibleChange\"\n      },\n      exportAs: [\"nzDropdown\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n__decorate([WithConfig(), InputBoolean()], NzDropDownDirective.prototype, \"nzBackdrop\", void 0);\n__decorate([InputBoolean()], NzDropDownDirective.prototype, \"nzClickHide\", void 0);\n__decorate([InputBoolean()], NzDropDownDirective.prototype, \"nzDisabled\", void 0);\n__decorate([InputBoolean()], NzDropDownDirective.prototype, \"nzVisible\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzDropDownDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[nz-dropdown]',\n      exportAs: 'nzDropdown',\n      host: {\n        class: 'ant-dropdown-trigger'\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: i1.NzConfigService\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i2.Overlay\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: i3.Platform\n  }], {\n    nzDropdownMenu: [{\n      type: Input\n    }],\n    nzTrigger: [{\n      type: Input\n    }],\n    nzMatchWidthElement: [{\n      type: Input\n    }],\n    nzBackdrop: [{\n      type: Input\n    }],\n    nzClickHide: [{\n      type: Input\n    }],\n    nzDisabled: [{\n      type: Input\n    }],\n    nzVisible: [{\n      type: Input\n    }],\n    nzOverlayClassName: [{\n      type: Input\n    }],\n    nzOverlayStyle: [{\n      type: Input\n    }],\n    nzPlacement: [{\n      type: Input\n    }],\n    nzVisibleChange: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzContextMenuServiceModule {\n  static {\n    this.ɵfac = function NzContextMenuServiceModule_Factory(t) {\n      return new (t || NzContextMenuServiceModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzContextMenuServiceModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzContextMenuServiceModule, [{\n    type: NgModule\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzDropDownADirective {\n  constructor() {}\n  static {\n    this.ɵfac = function NzDropDownADirective_Factory(t) {\n      return new (t || NzDropDownADirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzDropDownADirective,\n      selectors: [[\"a\", \"nz-dropdown\", \"\"]],\n      hostAttrs: [1, \"ant-dropdown-link\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzDropDownADirective, [{\n    type: Directive,\n    args: [{\n      selector: 'a[nz-dropdown]',\n      host: {\n        class: 'ant-dropdown-link'\n      },\n      standalone: true\n    }]\n  }], () => [], null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzDropdownButtonDirective {\n  constructor(renderer, nzButtonGroupComponent, elementRef) {\n    this.renderer = renderer;\n    this.nzButtonGroupComponent = nzButtonGroupComponent;\n    this.elementRef = elementRef;\n  }\n  ngAfterViewInit() {\n    const parentElement = this.renderer.parentNode(this.elementRef.nativeElement);\n    if (this.nzButtonGroupComponent && parentElement) {\n      this.renderer.addClass(parentElement, 'ant-dropdown-button');\n    }\n  }\n  static {\n    this.ɵfac = function NzDropdownButtonDirective_Factory(t) {\n      return new (t || NzDropdownButtonDirective)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i1$1.NzButtonGroupComponent, 9), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzDropdownButtonDirective,\n      selectors: [[\"\", \"nz-button\", \"\", \"nz-dropdown\", \"\"]],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzDropdownButtonDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[nz-button][nz-dropdown]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.Renderer2\n  }, {\n    type: i1$1.NzButtonGroupComponent,\n    decorators: [{\n      type: Host\n    }, {\n      type: Optional\n    }]\n  }, {\n    type: i0.ElementRef\n  }], null);\n})();\nclass NzDropdownMenuComponent {\n  onAnimationEvent(event) {\n    this.animationStateChange$.emit(event);\n  }\n  setMouseState(visible) {\n    this.mouseState$.next(visible);\n  }\n  setValue(key, value) {\n    this[key] = value;\n    this.cdr.markForCheck();\n  }\n  constructor(cdr, elementRef, renderer, viewContainerRef, nzMenuService, directionality, noAnimation) {\n    this.cdr = cdr;\n    this.elementRef = elementRef;\n    this.renderer = renderer;\n    this.viewContainerRef = viewContainerRef;\n    this.nzMenuService = nzMenuService;\n    this.directionality = directionality;\n    this.noAnimation = noAnimation;\n    this.mouseState$ = new BehaviorSubject(false);\n    this.isChildSubMenuOpen$ = this.nzMenuService.isChildSubMenuOpen$;\n    this.descendantMenuItemClick$ = this.nzMenuService.descendantMenuItemClick$;\n    this.animationStateChange$ = new EventEmitter();\n    this.nzOverlayClassName = '';\n    this.nzOverlayStyle = {};\n    this.dir = 'ltr';\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n    this.dir = this.directionality.value;\n  }\n  ngAfterContentInit() {\n    this.renderer.removeChild(this.renderer.parentNode(this.elementRef.nativeElement), this.elementRef.nativeElement);\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzDropdownMenuComponent_Factory(t) {\n      return new (t || NzDropdownMenuComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i1$2.MenuService), i0.ɵɵdirectiveInject(i2$1.Directionality, 8), i0.ɵɵdirectiveInject(i3$1.NzNoAnimationDirective, 9));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzDropdownMenuComponent,\n      selectors: [[\"nz-dropdown-menu\"]],\n      viewQuery: function NzDropdownMenuComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(TemplateRef, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templateRef = _t.first);\n        }\n      },\n      exportAs: [\"nzDropdownMenu\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([MenuService, /** menu is inside dropdown-menu component **/\n      {\n        provide: NzIsMenuInsideDropDownToken,\n        useValue: true\n      }]), i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      consts: [[1, \"ant-dropdown\", 3, \"mouseenter\", \"mouseleave\", \"ngClass\", \"ngStyle\", \"nzNoAnimation\"]],\n      template: function NzDropdownMenuComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, NzDropdownMenuComponent_ng_template_0_Template, 2, 7, \"ng-template\");\n        }\n      },\n      dependencies: [NgClass, NgStyle, NzNoAnimationDirective],\n      encapsulation: 2,\n      data: {\n        animation: [slideMotion]\n      },\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzDropdownMenuComponent, [{\n    type: Component,\n    args: [{\n      selector: `nz-dropdown-menu`,\n      exportAs: `nzDropdownMenu`,\n      animations: [slideMotion],\n      providers: [MenuService, /** menu is inside dropdown-menu component **/\n      {\n        provide: NzIsMenuInsideDropDownToken,\n        useValue: true\n      }],\n      template: `\n    <ng-template>\n      <div\n        class=\"ant-dropdown\"\n        [class.ant-dropdown-rtl]=\"dir === 'rtl'\"\n        [ngClass]=\"nzOverlayClassName\"\n        [ngStyle]=\"nzOverlayStyle\"\n        @slideMotion\n        (@slideMotion.done)=\"onAnimationEvent($event)\"\n        [@.disabled]=\"!!noAnimation?.nzNoAnimation\"\n        [nzNoAnimation]=\"noAnimation?.nzNoAnimation\"\n        (mouseenter)=\"setMouseState(true)\"\n        (mouseleave)=\"setMouseState(false)\"\n      >\n        <ng-content></ng-content>\n      </div>\n    </ng-template>\n  `,\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      imports: [NgClass, NgStyle, NzNoAnimationDirective],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: i1$2.MenuService\n  }, {\n    type: i2$1.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i3$1.NzNoAnimationDirective,\n    decorators: [{\n      type: Host\n    }, {\n      type: Optional\n    }]\n  }], {\n    templateRef: [{\n      type: ViewChild,\n      args: [TemplateRef, {\n        static: true\n      }]\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzDropDownModule {\n  static {\n    this.ɵfac = function NzDropDownModule_Factory(t) {\n      return new (t || NzDropDownModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzDropDownModule,\n      imports: [NzDropDownDirective, NzDropDownADirective, NzDropdownMenuComponent, NzDropdownButtonDirective, NzContextMenuServiceModule],\n      exports: [NzMenuModule, NzDropDownDirective, NzDropDownADirective, NzDropdownMenuComponent, NzDropdownButtonDirective]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [NzContextMenuServiceModule, NzMenuModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzDropDownModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzDropDownDirective, NzDropDownADirective, NzDropdownMenuComponent, NzDropdownButtonDirective, NzContextMenuServiceModule],\n      exports: [NzMenuModule, NzDropDownDirective, NzDropDownADirective, NzDropdownMenuComponent, NzDropdownButtonDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst LIST_OF_POSITIONS = [new ConnectionPositionPair({\n  originX: 'start',\n  originY: 'top'\n}, {\n  overlayX: 'start',\n  overlayY: 'top'\n}), new ConnectionPositionPair({\n  originX: 'start',\n  originY: 'top'\n}, {\n  overlayX: 'start',\n  overlayY: 'bottom'\n}), new ConnectionPositionPair({\n  originX: 'start',\n  originY: 'top'\n}, {\n  overlayX: 'end',\n  overlayY: 'bottom'\n}), new ConnectionPositionPair({\n  originX: 'start',\n  originY: 'top'\n}, {\n  overlayX: 'end',\n  overlayY: 'top'\n})];\nclass NzContextMenuService {\n  constructor(ngZone, overlay) {\n    this.ngZone = ngZone;\n    this.overlay = overlay;\n    this.overlayRef = null;\n    this.closeSubscription = Subscription.EMPTY;\n  }\n  create($event, nzDropdownMenuComponent) {\n    this.close(true);\n    const {\n      x,\n      y\n    } = $event;\n    if ($event instanceof MouseEvent) {\n      $event.preventDefault();\n    }\n    const positionStrategy = this.overlay.position().flexibleConnectedTo({\n      x,\n      y\n    }).withPositions(LIST_OF_POSITIONS).withTransformOriginOn('.ant-dropdown');\n    this.overlayRef = this.overlay.create({\n      positionStrategy,\n      disposeOnNavigation: true,\n      scrollStrategy: this.overlay.scrollStrategies.close()\n    });\n    this.closeSubscription = new Subscription();\n    this.closeSubscription.add(nzDropdownMenuComponent.descendantMenuItemClick$.subscribe(() => this.close()));\n    this.closeSubscription.add(this.ngZone.runOutsideAngular(() => merge(fromEvent(document, 'click').pipe(filter(event => !!this.overlayRef && !this.overlayRef.overlayElement.contains(event.target)), /** handle firefox contextmenu event **/\n    filter(event => event.button !== 2)), fromEvent(document, 'keydown').pipe(filter(event => event.key === 'Escape'))).pipe(first()).subscribe(() => this.ngZone.run(() => this.close()))));\n    return this.overlayRef.attach(new TemplatePortal(nzDropdownMenuComponent.templateRef, nzDropdownMenuComponent.viewContainerRef));\n  }\n  close(clear = false) {\n    if (this.overlayRef) {\n      this.overlayRef.detach();\n      if (clear) {\n        this.overlayRef.dispose();\n      }\n      this.overlayRef = null;\n      this.closeSubscription.unsubscribe();\n    }\n  }\n  static {\n    this.ɵfac = function NzContextMenuService_Factory(t) {\n      return new (t || NzContextMenuService)(i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(i2.Overlay));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NzContextMenuService,\n      factory: NzContextMenuService.ɵfac,\n      providedIn: NzContextMenuServiceModule\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzContextMenuService, [{\n    type: Injectable,\n    args: [{\n      providedIn: NzContextMenuServiceModule\n    }]\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: i2.Overlay\n  }], null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzContextMenuService, NzContextMenuServiceModule, NzDropDownADirective, NzDropDownDirective, NzDropDownModule, NzDropdownButtonDirective, NzDropdownMenuComponent };", "map": {"version": 3, "names": ["__decorate", "ESCAPE", "hasModifierKey", "TemplatePortal", "i0", "EventEmitter", "Directive", "Input", "Output", "NgModule", "Host", "Optional", "TemplateRef", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "ViewChild", "Injectable", "Subject", "BehaviorSubject", "merge", "fromEvent", "EMPTY", "combineLatest", "Subscription", "mapTo", "map", "switchMap", "filter", "auditTime", "distinctUntilChanged", "takeUntil", "first", "i1", "WithConfig", "POSITION_MAP", "InputBoolean", "i2", "ConnectionPositionPair", "i3", "i1$2", "MenuService", "NzIsMenuInsideDropDownToken", "NzMenuModule", "i1$1", "Ng<PERSON><PERSON>", "NgStyle", "slideMotion", "i3$1", "NzNoAnimationDirective", "i2$1", "_c0", "NzDropdownMenuComponent_ng_template_0_Template", "rf", "ctx", "_r1", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "NzDropdownMenuComponent_ng_template_0_Template_div_animation_slideMotion_done_0_listener", "$event", "ɵɵrestoreView", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onAnimationEvent", "NzDropdownMenuComponent_ng_template_0_Template_div_mouseenter_0_listener", "setMouseState", "NzDropdownMenuComponent_ng_template_0_Template_div_mouseleave_0_listener", "ɵɵprojection", "ɵɵelementEnd", "ɵɵclassProp", "dir", "ɵɵproperty", "nzOverlayClassName", "nzOverlayStyle", "undefined", "noAnimation", "nzNoAnimation", "NZ_CONFIG_MODULE_NAME", "listOfPositions", "bottomLeft", "bottomRight", "topRight", "topLeft", "NzDropDownDirective", "setDropdownMenuValue", "key", "value", "nzDropdownMenu", "setValue", "constructor", "nzConfigService", "elementRef", "overlay", "renderer", "viewContainerRef", "platform", "_nzModuleName", "overlayRef", "destroy$", "positionStrategy", "position", "flexibleConnectedTo", "nativeElement", "withLockedPosition", "withTransformOriginOn", "inputVisible$", "nzTrigger$", "overlayClose$", "nzTrigger", "nzMatchWidthElement", "nzBackdrop", "nzClickHide", "nzDisabled", "nzVisible", "nzPlacement", "nzVisibleChange", "ngAfterViewInit", "hostMouseState$", "pipe", "menuMouseState$", "mouseState$", "mergedMouseState$", "hostClickState$", "visibleStateByTrigger$", "trigger", "descendantMenuItemClick$", "domTriggerVisible$", "visible$", "isChildSubMenuOpen$", "visible", "sub", "<PERSON><PERSON><PERSON><PERSON>", "subscribe", "element", "triggerWidth", "getBoundingClientRect", "width", "emit", "create", "min<PERSON><PERSON><PERSON>", "disposeOnNavigation", "hasBackdrop", "scrollStrategy", "scrollStrategies", "reposition", "backdropClick", "detachments", "outsidePointerEvents", "e", "contains", "target", "keydownEvents", "keyCode", "next", "overlayConfig", "getConfig", "withPositions", "portal", "templateRef", "attach", "detach", "animationStateChange$", "event", "toState", "dispose", "ngOnDestroy", "complete", "ngOnChanges", "changes", "setAttribute", "removeAttribute", "ɵfac", "NzDropDownDirective_Factory", "t", "ɵɵdirectiveInject", "NzConfigService", "ElementRef", "Overlay", "Renderer2", "ViewContainerRef", "Platform", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostAttrs", "inputs", "outputs", "exportAs", "standalone", "features", "ɵɵNgOnChangesFeature", "prototype", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "host", "class", "NzContextMenuServiceModule", "NzContextMenuServiceModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "NzDropDownADirective", "NzDropDownADirective_Factory", "NzDropdownButtonDirective", "nzButtonGroupComponent", "parentElement", "parentNode", "addClass", "NzDropdownButtonDirective_Factory", "NzButtonGroupComponent", "decorators", "NzDropdownMenuComponent", "cdr", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nzMenuService", "directionality", "ngOnInit", "change", "direction", "detectChanges", "ngAfterContentInit", "<PERSON><PERSON><PERSON><PERSON>", "NzDropdownMenuComponent_Factory", "ChangeDetectorRef", "Directionality", "ɵcmp", "ɵɵdefineComponent", "viewQuery", "NzDropdownMenuComponent_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "ɵɵProvidersFeature", "provide", "useValue", "ɵɵStandaloneFeature", "ngContentSelectors", "decls", "vars", "consts", "template", "NzDropdownMenuComponent_Template", "ɵɵprojectionDef", "ɵɵtemplate", "dependencies", "encapsulation", "data", "animation", "changeDetection", "animations", "providers", "preserveWhitespaces", "None", "OnPush", "imports", "static", "NzDropDownModule", "NzDropDownModule_Factory", "exports", "LIST_OF_POSITIONS", "originX", "originY", "overlayX", "overlayY", "NzContextMenuService", "ngZone", "closeSubscription", "nzDropdownMenuComponent", "close", "x", "y", "MouseEvent", "preventDefault", "add", "runOutsideAngular", "document", "overlayElement", "button", "run", "clear", "unsubscribe", "NzContextMenuService_Factory", "ɵɵinject", "NgZone", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-dropdown.mjs"], "sourcesContent": ["import { __decorate } from 'tslib';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport { TemplatePortal } from '@angular/cdk/portal';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, Directive, Input, Output, NgModule, Host, Optional, TemplateRef, Component, ViewEncapsulation, ChangeDetectionStrategy, ViewChild, Injectable } from '@angular/core';\nimport { Subject, BehaviorSubject, merge, fromEvent, EMPTY, combineLatest, Subscription } from 'rxjs';\nimport { mapTo, map, switchMap, filter, auditTime, distinctUntilChanged, takeUntil, first } from 'rxjs/operators';\nimport * as i1 from 'ng-zorro-antd/core/config';\nimport { WithConfig } from 'ng-zorro-antd/core/config';\nimport { POSITION_MAP } from 'ng-zorro-antd/core/overlay';\nimport { InputBoolean } from 'ng-zorro-antd/core/util';\nimport * as i2 from '@angular/cdk/overlay';\nimport { ConnectionPositionPair } from '@angular/cdk/overlay';\nimport * as i3 from '@angular/cdk/platform';\nimport * as i1$2 from 'ng-zorro-antd/menu';\nimport { MenuService, NzIsMenuInsideDropDownToken, NzMenuModule } from 'ng-zorro-antd/menu';\nimport * as i1$1 from 'ng-zorro-antd/button';\nimport { NgClass, NgStyle } from '@angular/common';\nimport { slideMotion } from 'ng-zorro-antd/core/animation';\nimport * as i3$1 from 'ng-zorro-antd/core/no-animation';\nimport { NzNoAnimationDirective } from 'ng-zorro-antd/core/no-animation';\nimport * as i2$1 from '@angular/cdk/bidi';\n\nconst NZ_CONFIG_MODULE_NAME = 'dropDown';\nconst listOfPositions = [\n    POSITION_MAP.bottomLeft,\n    POSITION_MAP.bottomRight,\n    POSITION_MAP.topRight,\n    POSITION_MAP.topLeft\n];\nclass NzDropDownDirective {\n    setDropdownMenuValue(key, value) {\n        if (this.nzDropdownMenu) {\n            this.nzDropdownMenu.setValue(key, value);\n        }\n    }\n    constructor(nzConfigService, elementRef, overlay, renderer, viewContainerRef, platform) {\n        this.nzConfigService = nzConfigService;\n        this.elementRef = elementRef;\n        this.overlay = overlay;\n        this.renderer = renderer;\n        this.viewContainerRef = viewContainerRef;\n        this.platform = platform;\n        this._nzModuleName = NZ_CONFIG_MODULE_NAME;\n        this.overlayRef = null;\n        this.destroy$ = new Subject();\n        this.positionStrategy = this.overlay\n            .position()\n            .flexibleConnectedTo(this.elementRef.nativeElement)\n            .withLockedPosition()\n            .withTransformOriginOn('.ant-dropdown');\n        this.inputVisible$ = new BehaviorSubject(false);\n        this.nzTrigger$ = new BehaviorSubject('hover');\n        this.overlayClose$ = new Subject();\n        this.nzDropdownMenu = null;\n        this.nzTrigger = 'hover';\n        this.nzMatchWidthElement = null;\n        this.nzBackdrop = false;\n        this.nzClickHide = true;\n        this.nzDisabled = false;\n        this.nzVisible = false;\n        this.nzOverlayClassName = '';\n        this.nzOverlayStyle = {};\n        this.nzPlacement = 'bottomLeft';\n        this.nzVisibleChange = new EventEmitter();\n    }\n    ngAfterViewInit() {\n        if (this.nzDropdownMenu) {\n            const nativeElement = this.elementRef.nativeElement;\n            /** host mouse state **/\n            const hostMouseState$ = merge(fromEvent(nativeElement, 'mouseenter').pipe(mapTo(true)), fromEvent(nativeElement, 'mouseleave').pipe(mapTo(false)));\n            /** menu mouse state **/\n            const menuMouseState$ = this.nzDropdownMenu.mouseState$;\n            /** merged mouse state **/\n            const mergedMouseState$ = merge(menuMouseState$, hostMouseState$);\n            /** host click state **/\n            const hostClickState$ = fromEvent(nativeElement, 'click').pipe(map(() => !this.nzVisible));\n            /** visible state switch by nzTrigger **/\n            const visibleStateByTrigger$ = this.nzTrigger$.pipe(switchMap(trigger => {\n                if (trigger === 'hover') {\n                    return mergedMouseState$;\n                }\n                else if (trigger === 'click') {\n                    return hostClickState$;\n                }\n                else {\n                    return EMPTY;\n                }\n            }));\n            const descendantMenuItemClick$ = this.nzDropdownMenu.descendantMenuItemClick$.pipe(filter(() => this.nzClickHide), mapTo(false));\n            const domTriggerVisible$ = merge(visibleStateByTrigger$, descendantMenuItemClick$, this.overlayClose$).pipe(filter(() => !this.nzDisabled));\n            const visible$ = merge(this.inputVisible$, domTriggerVisible$);\n            combineLatest([visible$, this.nzDropdownMenu.isChildSubMenuOpen$])\n                .pipe(map(([visible, sub]) => visible || sub), auditTime(150), distinctUntilChanged(), filter(() => this.platform.isBrowser), takeUntil(this.destroy$))\n                .subscribe((visible) => {\n                const element = this.nzMatchWidthElement ? this.nzMatchWidthElement.nativeElement : nativeElement;\n                const triggerWidth = element.getBoundingClientRect().width;\n                if (this.nzVisible !== visible) {\n                    this.nzVisibleChange.emit(visible);\n                }\n                this.nzVisible = visible;\n                if (visible) {\n                    /** set up overlayRef **/\n                    if (!this.overlayRef) {\n                        /** new overlay **/\n                        this.overlayRef = this.overlay.create({\n                            positionStrategy: this.positionStrategy,\n                            minWidth: triggerWidth,\n                            disposeOnNavigation: true,\n                            hasBackdrop: this.nzBackdrop && this.nzTrigger === 'click',\n                            scrollStrategy: this.overlay.scrollStrategies.reposition()\n                        });\n                        merge(this.overlayRef.backdropClick(), this.overlayRef.detachments(), this.overlayRef\n                            .outsidePointerEvents()\n                            .pipe(filter((e) => !this.elementRef.nativeElement.contains(e.target))), this.overlayRef.keydownEvents().pipe(filter(e => e.keyCode === ESCAPE && !hasModifierKey(e))))\n                            .pipe(takeUntil(this.destroy$))\n                            .subscribe(() => {\n                            this.overlayClose$.next(false);\n                        });\n                    }\n                    else {\n                        /** update overlay config **/\n                        const overlayConfig = this.overlayRef.getConfig();\n                        overlayConfig.minWidth = triggerWidth;\n                    }\n                    /** open dropdown with animation **/\n                    this.positionStrategy.withPositions([POSITION_MAP[this.nzPlacement], ...listOfPositions]);\n                    /** reset portal if needed **/\n                    if (!this.portal || this.portal.templateRef !== this.nzDropdownMenu.templateRef) {\n                        this.portal = new TemplatePortal(this.nzDropdownMenu.templateRef, this.viewContainerRef);\n                    }\n                    this.overlayRef.attach(this.portal);\n                }\n                else {\n                    /** detach overlayRef if needed **/\n                    if (this.overlayRef) {\n                        this.overlayRef.detach();\n                    }\n                }\n            });\n            this.nzDropdownMenu.animationStateChange$.pipe(takeUntil(this.destroy$)).subscribe(event => {\n                if (event.toState === 'void') {\n                    if (this.overlayRef) {\n                        this.overlayRef.dispose();\n                    }\n                    this.overlayRef = null;\n                }\n            });\n        }\n    }\n    ngOnDestroy() {\n        this.destroy$.next(true);\n        this.destroy$.complete();\n        if (this.overlayRef) {\n            this.overlayRef.dispose();\n            this.overlayRef = null;\n        }\n    }\n    ngOnChanges(changes) {\n        const { nzVisible, nzDisabled, nzOverlayClassName, nzOverlayStyle, nzTrigger } = changes;\n        if (nzTrigger) {\n            this.nzTrigger$.next(this.nzTrigger);\n        }\n        if (nzVisible) {\n            this.inputVisible$.next(this.nzVisible);\n        }\n        if (nzDisabled) {\n            const nativeElement = this.elementRef.nativeElement;\n            if (this.nzDisabled) {\n                this.renderer.setAttribute(nativeElement, 'disabled', '');\n                this.inputVisible$.next(false);\n            }\n            else {\n                this.renderer.removeAttribute(nativeElement, 'disabled');\n            }\n        }\n        if (nzOverlayClassName) {\n            this.setDropdownMenuValue('nzOverlayClassName', this.nzOverlayClassName);\n        }\n        if (nzOverlayStyle) {\n            this.setDropdownMenuValue('nzOverlayStyle', this.nzOverlayStyle);\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzDropDownDirective, deps: [{ token: i1.NzConfigService }, { token: i0.ElementRef }, { token: i2.Overlay }, { token: i0.Renderer2 }, { token: i0.ViewContainerRef }, { token: i3.Platform }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzDropDownDirective, isStandalone: true, selector: \"[nz-dropdown]\", inputs: { nzDropdownMenu: \"nzDropdownMenu\", nzTrigger: \"nzTrigger\", nzMatchWidthElement: \"nzMatchWidthElement\", nzBackdrop: \"nzBackdrop\", nzClickHide: \"nzClickHide\", nzDisabled: \"nzDisabled\", nzVisible: \"nzVisible\", nzOverlayClassName: \"nzOverlayClassName\", nzOverlayStyle: \"nzOverlayStyle\", nzPlacement: \"nzPlacement\" }, outputs: { nzVisibleChange: \"nzVisibleChange\" }, host: { classAttribute: \"ant-dropdown-trigger\" }, exportAs: [\"nzDropdown\"], usesOnChanges: true, ngImport: i0 }); }\n}\n__decorate([\n    WithConfig(),\n    InputBoolean()\n], NzDropDownDirective.prototype, \"nzBackdrop\", void 0);\n__decorate([\n    InputBoolean()\n], NzDropDownDirective.prototype, \"nzClickHide\", void 0);\n__decorate([\n    InputBoolean()\n], NzDropDownDirective.prototype, \"nzDisabled\", void 0);\n__decorate([\n    InputBoolean()\n], NzDropDownDirective.prototype, \"nzVisible\", void 0);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzDropDownDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[nz-dropdown]',\n                    exportAs: 'nzDropdown',\n                    host: {\n                        class: 'ant-dropdown-trigger'\n                    },\n                    standalone: true\n                }]\n        }], ctorParameters: () => [{ type: i1.NzConfigService }, { type: i0.ElementRef }, { type: i2.Overlay }, { type: i0.Renderer2 }, { type: i0.ViewContainerRef }, { type: i3.Platform }], propDecorators: { nzDropdownMenu: [{\n                type: Input\n            }], nzTrigger: [{\n                type: Input\n            }], nzMatchWidthElement: [{\n                type: Input\n            }], nzBackdrop: [{\n                type: Input\n            }], nzClickHide: [{\n                type: Input\n            }], nzDisabled: [{\n                type: Input\n            }], nzVisible: [{\n                type: Input\n            }], nzOverlayClassName: [{\n                type: Input\n            }], nzOverlayStyle: [{\n                type: Input\n            }], nzPlacement: [{\n                type: Input\n            }], nzVisibleChange: [{\n                type: Output\n            }] } });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzContextMenuServiceModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzContextMenuServiceModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.3.8\", ngImport: i0, type: NzContextMenuServiceModule }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzContextMenuServiceModule }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzContextMenuServiceModule, decorators: [{\n            type: NgModule\n        }] });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzDropDownADirective {\n    constructor() { }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzDropDownADirective, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzDropDownADirective, isStandalone: true, selector: \"a[nz-dropdown]\", host: { classAttribute: \"ant-dropdown-link\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzDropDownADirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'a[nz-dropdown]',\n                    host: {\n                        class: 'ant-dropdown-link'\n                    },\n                    standalone: true\n                }]\n        }], ctorParameters: () => [] });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzDropdownButtonDirective {\n    constructor(renderer, nzButtonGroupComponent, elementRef) {\n        this.renderer = renderer;\n        this.nzButtonGroupComponent = nzButtonGroupComponent;\n        this.elementRef = elementRef;\n    }\n    ngAfterViewInit() {\n        const parentElement = this.renderer.parentNode(this.elementRef.nativeElement);\n        if (this.nzButtonGroupComponent && parentElement) {\n            this.renderer.addClass(parentElement, 'ant-dropdown-button');\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzDropdownButtonDirective, deps: [{ token: i0.Renderer2 }, { token: i1$1.NzButtonGroupComponent, host: true, optional: true }, { token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzDropdownButtonDirective, isStandalone: true, selector: \"[nz-button][nz-dropdown]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzDropdownButtonDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[nz-button][nz-dropdown]',\n                    standalone: true\n                }]\n        }], ctorParameters: () => [{ type: i0.Renderer2 }, { type: i1$1.NzButtonGroupComponent, decorators: [{\n                    type: Host\n                }, {\n                    type: Optional\n                }] }, { type: i0.ElementRef }] });\n\nclass NzDropdownMenuComponent {\n    onAnimationEvent(event) {\n        this.animationStateChange$.emit(event);\n    }\n    setMouseState(visible) {\n        this.mouseState$.next(visible);\n    }\n    setValue(key, value) {\n        this[key] = value;\n        this.cdr.markForCheck();\n    }\n    constructor(cdr, elementRef, renderer, viewContainerRef, nzMenuService, directionality, noAnimation) {\n        this.cdr = cdr;\n        this.elementRef = elementRef;\n        this.renderer = renderer;\n        this.viewContainerRef = viewContainerRef;\n        this.nzMenuService = nzMenuService;\n        this.directionality = directionality;\n        this.noAnimation = noAnimation;\n        this.mouseState$ = new BehaviorSubject(false);\n        this.isChildSubMenuOpen$ = this.nzMenuService.isChildSubMenuOpen$;\n        this.descendantMenuItemClick$ = this.nzMenuService.descendantMenuItemClick$;\n        this.animationStateChange$ = new EventEmitter();\n        this.nzOverlayClassName = '';\n        this.nzOverlayStyle = {};\n        this.dir = 'ltr';\n        this.destroy$ = new Subject();\n    }\n    ngOnInit() {\n        this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe((direction) => {\n            this.dir = direction;\n            this.cdr.detectChanges();\n        });\n        this.dir = this.directionality.value;\n    }\n    ngAfterContentInit() {\n        this.renderer.removeChild(this.renderer.parentNode(this.elementRef.nativeElement), this.elementRef.nativeElement);\n    }\n    ngOnDestroy() {\n        this.destroy$.next();\n        this.destroy$.complete();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzDropdownMenuComponent, deps: [{ token: i0.ChangeDetectorRef }, { token: i0.ElementRef }, { token: i0.Renderer2 }, { token: i0.ViewContainerRef }, { token: i1$2.MenuService }, { token: i2$1.Directionality, optional: true }, { token: i3$1.NzNoAnimationDirective, host: true, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzDropdownMenuComponent, isStandalone: true, selector: \"nz-dropdown-menu\", providers: [\n            MenuService,\n            /** menu is inside dropdown-menu component **/\n            {\n                provide: NzIsMenuInsideDropDownToken,\n                useValue: true\n            }\n        ], viewQueries: [{ propertyName: \"templateRef\", first: true, predicate: TemplateRef, descendants: true, static: true }], exportAs: [\"nzDropdownMenu\"], ngImport: i0, template: `\n    <ng-template>\n      <div\n        class=\"ant-dropdown\"\n        [class.ant-dropdown-rtl]=\"dir === 'rtl'\"\n        [ngClass]=\"nzOverlayClassName\"\n        [ngStyle]=\"nzOverlayStyle\"\n        @slideMotion\n        (@slideMotion.done)=\"onAnimationEvent($event)\"\n        [@.disabled]=\"!!noAnimation?.nzNoAnimation\"\n        [nzNoAnimation]=\"noAnimation?.nzNoAnimation\"\n        (mouseenter)=\"setMouseState(true)\"\n        (mouseleave)=\"setMouseState(false)\"\n      >\n        <ng-content></ng-content>\n      </div>\n    </ng-template>\n  `, isInline: true, dependencies: [{ kind: \"directive\", type: NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"directive\", type: NzNoAnimationDirective, selector: \"[nzNoAnimation]\", inputs: [\"nzNoAnimation\"], exportAs: [\"nzNoAnimation\"] }], animations: [slideMotion], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzDropdownMenuComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: `nz-dropdown-menu`,\n                    exportAs: `nzDropdownMenu`,\n                    animations: [slideMotion],\n                    providers: [\n                        MenuService,\n                        /** menu is inside dropdown-menu component **/\n                        {\n                            provide: NzIsMenuInsideDropDownToken,\n                            useValue: true\n                        }\n                    ],\n                    template: `\n    <ng-template>\n      <div\n        class=\"ant-dropdown\"\n        [class.ant-dropdown-rtl]=\"dir === 'rtl'\"\n        [ngClass]=\"nzOverlayClassName\"\n        [ngStyle]=\"nzOverlayStyle\"\n        @slideMotion\n        (@slideMotion.done)=\"onAnimationEvent($event)\"\n        [@.disabled]=\"!!noAnimation?.nzNoAnimation\"\n        [nzNoAnimation]=\"noAnimation?.nzNoAnimation\"\n        (mouseenter)=\"setMouseState(true)\"\n        (mouseleave)=\"setMouseState(false)\"\n      >\n        <ng-content></ng-content>\n      </div>\n    </ng-template>\n  `,\n                    preserveWhitespaces: false,\n                    encapsulation: ViewEncapsulation.None,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    imports: [NgClass, NgStyle, NzNoAnimationDirective],\n                    standalone: true\n                }]\n        }], ctorParameters: () => [{ type: i0.ChangeDetectorRef }, { type: i0.ElementRef }, { type: i0.Renderer2 }, { type: i0.ViewContainerRef }, { type: i1$2.MenuService }, { type: i2$1.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i3$1.NzNoAnimationDirective, decorators: [{\n                    type: Host\n                }, {\n                    type: Optional\n                }] }], propDecorators: { templateRef: [{\n                type: ViewChild,\n                args: [TemplateRef, { static: true }]\n            }] } });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzDropDownModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzDropDownModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.3.8\", ngImport: i0, type: NzDropDownModule, imports: [NzDropDownDirective,\n            NzDropDownADirective,\n            NzDropdownMenuComponent,\n            NzDropdownButtonDirective,\n            NzContextMenuServiceModule], exports: [NzMenuModule, NzDropDownDirective, NzDropDownADirective, NzDropdownMenuComponent, NzDropdownButtonDirective] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzDropDownModule, imports: [NzContextMenuServiceModule, NzMenuModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzDropDownModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        NzDropDownDirective,\n                        NzDropDownADirective,\n                        NzDropdownMenuComponent,\n                        NzDropdownButtonDirective,\n                        NzContextMenuServiceModule\n                    ],\n                    exports: [NzMenuModule, NzDropDownDirective, NzDropDownADirective, NzDropdownMenuComponent, NzDropdownButtonDirective]\n                }]\n        }] });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst LIST_OF_POSITIONS = [\n    new ConnectionPositionPair({ originX: 'start', originY: 'top' }, { overlayX: 'start', overlayY: 'top' }),\n    new ConnectionPositionPair({ originX: 'start', originY: 'top' }, { overlayX: 'start', overlayY: 'bottom' }),\n    new ConnectionPositionPair({ originX: 'start', originY: 'top' }, { overlayX: 'end', overlayY: 'bottom' }),\n    new ConnectionPositionPair({ originX: 'start', originY: 'top' }, { overlayX: 'end', overlayY: 'top' })\n];\nclass NzContextMenuService {\n    constructor(ngZone, overlay) {\n        this.ngZone = ngZone;\n        this.overlay = overlay;\n        this.overlayRef = null;\n        this.closeSubscription = Subscription.EMPTY;\n    }\n    create($event, nzDropdownMenuComponent) {\n        this.close(true);\n        const { x, y } = $event;\n        if ($event instanceof MouseEvent) {\n            $event.preventDefault();\n        }\n        const positionStrategy = this.overlay\n            .position()\n            .flexibleConnectedTo({ x, y })\n            .withPositions(LIST_OF_POSITIONS)\n            .withTransformOriginOn('.ant-dropdown');\n        this.overlayRef = this.overlay.create({\n            positionStrategy,\n            disposeOnNavigation: true,\n            scrollStrategy: this.overlay.scrollStrategies.close()\n        });\n        this.closeSubscription = new Subscription();\n        this.closeSubscription.add(nzDropdownMenuComponent.descendantMenuItemClick$.subscribe(() => this.close()));\n        this.closeSubscription.add(this.ngZone.runOutsideAngular(() => merge(fromEvent(document, 'click').pipe(filter(event => !!this.overlayRef && !this.overlayRef.overlayElement.contains(event.target)), \n        /** handle firefox contextmenu event **/\n        filter(event => event.button !== 2)), fromEvent(document, 'keydown').pipe(filter(event => event.key === 'Escape')))\n            .pipe(first())\n            .subscribe(() => this.ngZone.run(() => this.close()))));\n        return this.overlayRef.attach(new TemplatePortal(nzDropdownMenuComponent.templateRef, nzDropdownMenuComponent.viewContainerRef));\n    }\n    close(clear = false) {\n        if (this.overlayRef) {\n            this.overlayRef.detach();\n            if (clear) {\n                this.overlayRef.dispose();\n            }\n            this.overlayRef = null;\n            this.closeSubscription.unsubscribe();\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzContextMenuService, deps: [{ token: i0.NgZone }, { token: i2.Overlay }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzContextMenuService, providedIn: NzContextMenuServiceModule }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzContextMenuService, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: NzContextMenuServiceModule\n                }]\n        }], ctorParameters: () => [{ type: i0.NgZone }, { type: i2.Overlay }] });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzContextMenuService, NzContextMenuServiceModule, NzDropDownADirective, NzDropDownDirective, NzDropDownModule, NzDropdownButtonDirective, NzDropdownMenuComponent };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,MAAM,EAAEC,cAAc,QAAQ,uBAAuB;AAC9D,SAASC,cAAc,QAAQ,qBAAqB;AACpD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,YAAY,EAAEC,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,SAAS,EAAEC,UAAU,QAAQ,eAAe;AAC3L,SAASC,OAAO,EAAEC,eAAe,EAAEC,KAAK,EAAEC,SAAS,EAAEC,KAAK,EAAEC,aAAa,EAAEC,YAAY,QAAQ,MAAM;AACrG,SAASC,KAAK,EAAEC,GAAG,EAAEC,SAAS,EAAEC,MAAM,EAAEC,SAAS,EAAEC,oBAAoB,EAAEC,SAAS,EAAEC,KAAK,QAAQ,gBAAgB;AACjH,OAAO,KAAKC,EAAE,MAAM,2BAA2B;AAC/C,SAASC,UAAU,QAAQ,2BAA2B;AACtD,SAASC,YAAY,QAAQ,4BAA4B;AACzD,SAASC,YAAY,QAAQ,yBAAyB;AACtD,OAAO,KAAKC,EAAE,MAAM,sBAAsB;AAC1C,SAASC,sBAAsB,QAAQ,sBAAsB;AAC7D,OAAO,KAAKC,EAAE,MAAM,uBAAuB;AAC3C,OAAO,KAAKC,IAAI,MAAM,oBAAoB;AAC1C,SAASC,WAAW,EAAEC,2BAA2B,EAAEC,YAAY,QAAQ,oBAAoB;AAC3F,OAAO,KAAKC,IAAI,MAAM,sBAAsB;AAC5C,SAASC,OAAO,EAAEC,OAAO,QAAQ,iBAAiB;AAClD,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,OAAO,KAAKC,IAAI,MAAM,iCAAiC;AACvD,SAASC,sBAAsB,QAAQ,iCAAiC;AACxE,OAAO,KAAKC,IAAI,MAAM,mBAAmB;AAAC,MAAAC,GAAA;AAAA,SAAAC,+CAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAE,GAAA,GAkK0DnD,EAAE,CAAAoD,gBAAA;IAAFpD,EAAE,CAAAqD,cAAA,YAiLhG,CAAC;IAjL6FrD,EAAE,CAAAsD,UAAA,+BAAAC,yFAAAC,MAAA;MAAFxD,EAAE,CAAAyD,aAAA,CAAAN,GAAA;MAAA,MAAAO,MAAA,GAAF1D,EAAE,CAAA2D,aAAA;MAAA,OAAF3D,EAAE,CAAA4D,WAAA,CA4KzEF,MAAA,CAAAG,gBAAA,CAAAL,MAAuB,CAAC;IAAA,EAAC,wBAAAM,yEAAA;MA5K8C9D,EAAE,CAAAyD,aAAA,CAAAN,GAAA;MAAA,MAAAO,MAAA,GAAF1D,EAAE,CAAA2D,aAAA;MAAA,OAAF3D,EAAE,CAAA4D,WAAA,CA+KhFF,MAAA,CAAAK,aAAA,CAAc,IAAI,CAAC;IAAA,EAAC,wBAAAC,yEAAA;MA/K0DhE,EAAE,CAAAyD,aAAA,CAAAN,GAAA;MAAA,MAAAO,MAAA,GAAF1D,EAAE,CAAA2D,aAAA;MAAA,OAAF3D,EAAE,CAAA4D,WAAA,CAgLhFF,MAAA,CAAAK,aAAA,CAAc,KAAK,CAAC;IAAA,EAAC;IAhLyD/D,EAAE,CAAAiE,YAAA,EAkLtE,CAAC;IAlLmEjE,EAAE,CAAAkE,YAAA,CAmL3F,CAAC;EAAA;EAAA,IAAAjB,EAAA;IAAA,MAAAS,MAAA,GAnLwF1D,EAAE,CAAA2D,aAAA;IAAF3D,EAAE,CAAAmE,WAAA,qBAAAT,MAAA,CAAAU,GAAA,UAwKvD,CAAC;IAxKoDpE,EAAE,CAAAqE,UAAA,YAAAX,MAAA,CAAAY,kBAyKjE,CAAC,YAAAZ,MAAA,CAAAa,cACL,CAAC,iBAAAC,SACf,CAAC,kBAAAd,MAAA,CAAAe,WAAA,kBAAAf,MAAA,CAAAe,WAAA,CAAAC,aAAA,CAE8B,CAAC,kBAAAhB,MAAA,CAAAe,WAAA,kBAAAf,MAAA,CAAAe,WAAA,CAAAC,aACA,CAAC;EAAA;AAAA;AA9UpD,MAAMC,qBAAqB,GAAG,UAAU;AACxC,MAAMC,eAAe,GAAG,CACpB7C,YAAY,CAAC8C,UAAU,EACvB9C,YAAY,CAAC+C,WAAW,EACxB/C,YAAY,CAACgD,QAAQ,EACrBhD,YAAY,CAACiD,OAAO,CACvB;AACD,MAAMC,mBAAmB,CAAC;EACtBC,oBAAoBA,CAACC,GAAG,EAAEC,KAAK,EAAE;IAC7B,IAAI,IAAI,CAACC,cAAc,EAAE;MACrB,IAAI,CAACA,cAAc,CAACC,QAAQ,CAACH,GAAG,EAAEC,KAAK,CAAC;IAC5C;EACJ;EACAG,WAAWA,CAACC,eAAe,EAAEC,UAAU,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,gBAAgB,EAAEC,QAAQ,EAAE;IACpF,IAAI,CAACL,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,aAAa,GAAGnB,qBAAqB;IAC1C,IAAI,CAACoB,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,QAAQ,GAAG,IAAIlF,OAAO,CAAC,CAAC;IAC7B,IAAI,CAACmF,gBAAgB,GAAG,IAAI,CAACP,OAAO,CAC/BQ,QAAQ,CAAC,CAAC,CACVC,mBAAmB,CAAC,IAAI,CAACV,UAAU,CAACW,aAAa,CAAC,CAClDC,kBAAkB,CAAC,CAAC,CACpBC,qBAAqB,CAAC,eAAe,CAAC;IAC3C,IAAI,CAACC,aAAa,GAAG,IAAIxF,eAAe,CAAC,KAAK,CAAC;IAC/C,IAAI,CAACyF,UAAU,GAAG,IAAIzF,eAAe,CAAC,OAAO,CAAC;IAC9C,IAAI,CAAC0F,aAAa,GAAG,IAAI3F,OAAO,CAAC,CAAC;IAClC,IAAI,CAACuE,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACqB,SAAS,GAAG,OAAO;IACxB,IAAI,CAACC,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACzC,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACC,cAAc,GAAG,CAAC,CAAC;IACxB,IAAI,CAACyC,WAAW,GAAG,YAAY;IAC/B,IAAI,CAACC,eAAe,GAAG,IAAIhH,YAAY,CAAC,CAAC;EAC7C;EACAiH,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAAC7B,cAAc,EAAE;MACrB,MAAMe,aAAa,GAAG,IAAI,CAACX,UAAU,CAACW,aAAa;MACnD;MACA,MAAMe,eAAe,GAAGnG,KAAK,CAACC,SAAS,CAACmF,aAAa,EAAE,YAAY,CAAC,CAACgB,IAAI,CAAC/F,KAAK,CAAC,IAAI,CAAC,CAAC,EAAEJ,SAAS,CAACmF,aAAa,EAAE,YAAY,CAAC,CAACgB,IAAI,CAAC/F,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;MAClJ;MACA,MAAMgG,eAAe,GAAG,IAAI,CAAChC,cAAc,CAACiC,WAAW;MACvD;MACA,MAAMC,iBAAiB,GAAGvG,KAAK,CAACqG,eAAe,EAAEF,eAAe,CAAC;MACjE;MACA,MAAMK,eAAe,GAAGvG,SAAS,CAACmF,aAAa,EAAE,OAAO,CAAC,CAACgB,IAAI,CAAC9F,GAAG,CAAC,MAAM,CAAC,IAAI,CAACyF,SAAS,CAAC,CAAC;MAC1F;MACA,MAAMU,sBAAsB,GAAG,IAAI,CAACjB,UAAU,CAACY,IAAI,CAAC7F,SAAS,CAACmG,OAAO,IAAI;QACrE,IAAIA,OAAO,KAAK,OAAO,EAAE;UACrB,OAAOH,iBAAiB;QAC5B,CAAC,MACI,IAAIG,OAAO,KAAK,OAAO,EAAE;UAC1B,OAAOF,eAAe;QAC1B,CAAC,MACI;UACD,OAAOtG,KAAK;QAChB;MACJ,CAAC,CAAC,CAAC;MACH,MAAMyG,wBAAwB,GAAG,IAAI,CAACtC,cAAc,CAACsC,wBAAwB,CAACP,IAAI,CAAC5F,MAAM,CAAC,MAAM,IAAI,CAACqF,WAAW,CAAC,EAAExF,KAAK,CAAC,KAAK,CAAC,CAAC;MAChI,MAAMuG,kBAAkB,GAAG5G,KAAK,CAACyG,sBAAsB,EAAEE,wBAAwB,EAAE,IAAI,CAAClB,aAAa,CAAC,CAACW,IAAI,CAAC5F,MAAM,CAAC,MAAM,CAAC,IAAI,CAACsF,UAAU,CAAC,CAAC;MAC3I,MAAMe,QAAQ,GAAG7G,KAAK,CAAC,IAAI,CAACuF,aAAa,EAAEqB,kBAAkB,CAAC;MAC9DzG,aAAa,CAAC,CAAC0G,QAAQ,EAAE,IAAI,CAACxC,cAAc,CAACyC,mBAAmB,CAAC,CAAC,CAC7DV,IAAI,CAAC9F,GAAG,CAAC,CAAC,CAACyG,OAAO,EAAEC,GAAG,CAAC,KAAKD,OAAO,IAAIC,GAAG,CAAC,EAAEvG,SAAS,CAAC,GAAG,CAAC,EAAEC,oBAAoB,CAAC,CAAC,EAAEF,MAAM,CAAC,MAAM,IAAI,CAACqE,QAAQ,CAACoC,SAAS,CAAC,EAAEtG,SAAS,CAAC,IAAI,CAACqE,QAAQ,CAAC,CAAC,CACtJkC,SAAS,CAAEH,OAAO,IAAK;QACxB,MAAMI,OAAO,GAAG,IAAI,CAACxB,mBAAmB,GAAG,IAAI,CAACA,mBAAmB,CAACP,aAAa,GAAGA,aAAa;QACjG,MAAMgC,YAAY,GAAGD,OAAO,CAACE,qBAAqB,CAAC,CAAC,CAACC,KAAK;QAC1D,IAAI,IAAI,CAACvB,SAAS,KAAKgB,OAAO,EAAE;UAC5B,IAAI,CAACd,eAAe,CAACsB,IAAI,CAACR,OAAO,CAAC;QACtC;QACA,IAAI,CAAChB,SAAS,GAAGgB,OAAO;QACxB,IAAIA,OAAO,EAAE;UACT;UACA,IAAI,CAAC,IAAI,CAAChC,UAAU,EAAE;YAClB;YACA,IAAI,CAACA,UAAU,GAAG,IAAI,CAACL,OAAO,CAAC8C,MAAM,CAAC;cAClCvC,gBAAgB,EAAE,IAAI,CAACA,gBAAgB;cACvCwC,QAAQ,EAAEL,YAAY;cACtBM,mBAAmB,EAAE,IAAI;cACzBC,WAAW,EAAE,IAAI,CAAC/B,UAAU,IAAI,IAAI,CAACF,SAAS,KAAK,OAAO;cAC1DkC,cAAc,EAAE,IAAI,CAAClD,OAAO,CAACmD,gBAAgB,CAACC,UAAU,CAAC;YAC7D,CAAC,CAAC;YACF9H,KAAK,CAAC,IAAI,CAAC+E,UAAU,CAACgD,aAAa,CAAC,CAAC,EAAE,IAAI,CAAChD,UAAU,CAACiD,WAAW,CAAC,CAAC,EAAE,IAAI,CAACjD,UAAU,CAChFkD,oBAAoB,CAAC,CAAC,CACtB7B,IAAI,CAAC5F,MAAM,CAAE0H,CAAC,IAAK,CAAC,IAAI,CAACzD,UAAU,CAACW,aAAa,CAAC+C,QAAQ,CAACD,CAAC,CAACE,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACrD,UAAU,CAACsD,aAAa,CAAC,CAAC,CAACjC,IAAI,CAAC5F,MAAM,CAAC0H,CAAC,IAAIA,CAAC,CAACI,OAAO,KAAKzJ,MAAM,IAAI,CAACC,cAAc,CAACoJ,CAAC,CAAC,CAAC,CAAC,CAAC,CACtK9B,IAAI,CAACzF,SAAS,CAAC,IAAI,CAACqE,QAAQ,CAAC,CAAC,CAC9BkC,SAAS,CAAC,MAAM;cACjB,IAAI,CAACzB,aAAa,CAAC8C,IAAI,CAAC,KAAK,CAAC;YAClC,CAAC,CAAC;UACN,CAAC,MACI;YACD;YACA,MAAMC,aAAa,GAAG,IAAI,CAACzD,UAAU,CAAC0D,SAAS,CAAC,CAAC;YACjDD,aAAa,CAACf,QAAQ,GAAGL,YAAY;UACzC;UACA;UACA,IAAI,CAACnC,gBAAgB,CAACyD,aAAa,CAAC,CAAC3H,YAAY,CAAC,IAAI,CAACiF,WAAW,CAAC,EAAE,GAAGpC,eAAe,CAAC,CAAC;UACzF;UACA,IAAI,CAAC,IAAI,CAAC+E,MAAM,IAAI,IAAI,CAACA,MAAM,CAACC,WAAW,KAAK,IAAI,CAACvE,cAAc,CAACuE,WAAW,EAAE;YAC7E,IAAI,CAACD,MAAM,GAAG,IAAI5J,cAAc,CAAC,IAAI,CAACsF,cAAc,CAACuE,WAAW,EAAE,IAAI,CAAChE,gBAAgB,CAAC;UAC5F;UACA,IAAI,CAACG,UAAU,CAAC8D,MAAM,CAAC,IAAI,CAACF,MAAM,CAAC;QACvC,CAAC,MACI;UACD;UACA,IAAI,IAAI,CAAC5D,UAAU,EAAE;YACjB,IAAI,CAACA,UAAU,CAAC+D,MAAM,CAAC,CAAC;UAC5B;QACJ;MACJ,CAAC,CAAC;MACF,IAAI,CAACzE,cAAc,CAAC0E,qBAAqB,CAAC3C,IAAI,CAACzF,SAAS,CAAC,IAAI,CAACqE,QAAQ,CAAC,CAAC,CAACkC,SAAS,CAAC8B,KAAK,IAAI;QACxF,IAAIA,KAAK,CAACC,OAAO,KAAK,MAAM,EAAE;UAC1B,IAAI,IAAI,CAAClE,UAAU,EAAE;YACjB,IAAI,CAACA,UAAU,CAACmE,OAAO,CAAC,CAAC;UAC7B;UACA,IAAI,CAACnE,UAAU,GAAG,IAAI;QAC1B;MACJ,CAAC,CAAC;IACN;EACJ;EACAoE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACnE,QAAQ,CAACuD,IAAI,CAAC,IAAI,CAAC;IACxB,IAAI,CAACvD,QAAQ,CAACoE,QAAQ,CAAC,CAAC;IACxB,IAAI,IAAI,CAACrE,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAACmE,OAAO,CAAC,CAAC;MACzB,IAAI,CAACnE,UAAU,GAAG,IAAI;IAC1B;EACJ;EACAsE,WAAWA,CAACC,OAAO,EAAE;IACjB,MAAM;MAAEvD,SAAS;MAAED,UAAU;MAAExC,kBAAkB;MAAEC,cAAc;MAAEmC;IAAU,CAAC,GAAG4D,OAAO;IACxF,IAAI5D,SAAS,EAAE;MACX,IAAI,CAACF,UAAU,CAAC+C,IAAI,CAAC,IAAI,CAAC7C,SAAS,CAAC;IACxC;IACA,IAAIK,SAAS,EAAE;MACX,IAAI,CAACR,aAAa,CAACgD,IAAI,CAAC,IAAI,CAACxC,SAAS,CAAC;IAC3C;IACA,IAAID,UAAU,EAAE;MACZ,MAAMV,aAAa,GAAG,IAAI,CAACX,UAAU,CAACW,aAAa;MACnD,IAAI,IAAI,CAACU,UAAU,EAAE;QACjB,IAAI,CAACnB,QAAQ,CAAC4E,YAAY,CAACnE,aAAa,EAAE,UAAU,EAAE,EAAE,CAAC;QACzD,IAAI,CAACG,aAAa,CAACgD,IAAI,CAAC,KAAK,CAAC;MAClC,CAAC,MACI;QACD,IAAI,CAAC5D,QAAQ,CAAC6E,eAAe,CAACpE,aAAa,EAAE,UAAU,CAAC;MAC5D;IACJ;IACA,IAAI9B,kBAAkB,EAAE;MACpB,IAAI,CAACY,oBAAoB,CAAC,oBAAoB,EAAE,IAAI,CAACZ,kBAAkB,CAAC;IAC5E;IACA,IAAIC,cAAc,EAAE;MAChB,IAAI,CAACW,oBAAoB,CAAC,gBAAgB,EAAE,IAAI,CAACX,cAAc,CAAC;IACpE;EACJ;EACA;IAAS,IAAI,CAACkG,IAAI,YAAAC,4BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwF1F,mBAAmB,EAA7BjF,EAAE,CAAA4K,iBAAA,CAA6C/I,EAAE,CAACgJ,eAAe,GAAjE7K,EAAE,CAAA4K,iBAAA,CAA4E5K,EAAE,CAAC8K,UAAU,GAA3F9K,EAAE,CAAA4K,iBAAA,CAAsG3I,EAAE,CAAC8I,OAAO,GAAlH/K,EAAE,CAAA4K,iBAAA,CAA6H5K,EAAE,CAACgL,SAAS,GAA3IhL,EAAE,CAAA4K,iBAAA,CAAsJ5K,EAAE,CAACiL,gBAAgB,GAA3KjL,EAAE,CAAA4K,iBAAA,CAAsLzI,EAAE,CAAC+I,QAAQ;IAAA,CAA4C;EAAE;EACjV;IAAS,IAAI,CAACC,IAAI,kBAD8EnL,EAAE,CAAAoL,iBAAA;MAAAC,IAAA,EACJpG,mBAAmB;MAAAqG,SAAA;MAAAC,SAAA;MAAAC,MAAA;QAAAnG,cAAA;QAAAqB,SAAA;QAAAC,mBAAA;QAAAC,UAAA;QAAAC,WAAA;QAAAC,UAAA;QAAAC,SAAA;QAAAzC,kBAAA;QAAAC,cAAA;QAAAyC,WAAA;MAAA;MAAAyE,OAAA;QAAAxE,eAAA;MAAA;MAAAyE,QAAA;MAAAC,UAAA;MAAAC,QAAA,GADjB5L,EAAE,CAAA6L,oBAAA;IAAA,EACmiB;EAAE;AAC3oB;AACAjM,UAAU,CAAC,CACPkC,UAAU,CAAC,CAAC,EACZE,YAAY,CAAC,CAAC,CACjB,EAAEiD,mBAAmB,CAAC6G,SAAS,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC;AACvDlM,UAAU,CAAC,CACPoC,YAAY,CAAC,CAAC,CACjB,EAAEiD,mBAAmB,CAAC6G,SAAS,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC;AACxDlM,UAAU,CAAC,CACPoC,YAAY,CAAC,CAAC,CACjB,EAAEiD,mBAAmB,CAAC6G,SAAS,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC;AACvDlM,UAAU,CAAC,CACPoC,YAAY,CAAC,CAAC,CACjB,EAAEiD,mBAAmB,CAAC6G,SAAS,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC;AACtD;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAhBoG/L,EAAE,CAAAgM,iBAAA,CAgBX/G,mBAAmB,EAAc,CAAC;IACjHoG,IAAI,EAAEnL,SAAS;IACf+L,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,eAAe;MACzBR,QAAQ,EAAE,YAAY;MACtBS,IAAI,EAAE;QACFC,KAAK,EAAE;MACX,CAAC;MACDT,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEN,IAAI,EAAExJ,EAAE,CAACgJ;EAAgB,CAAC,EAAE;IAAEQ,IAAI,EAAErL,EAAE,CAAC8K;EAAW,CAAC,EAAE;IAAEO,IAAI,EAAEpJ,EAAE,CAAC8I;EAAQ,CAAC,EAAE;IAAEM,IAAI,EAAErL,EAAE,CAACgL;EAAU,CAAC,EAAE;IAAEK,IAAI,EAAErL,EAAE,CAACiL;EAAiB,CAAC,EAAE;IAAEI,IAAI,EAAElJ,EAAE,CAAC+I;EAAS,CAAC,CAAC,EAAkB;IAAE7F,cAAc,EAAE,CAAC;MAClNgG,IAAI,EAAElL;IACV,CAAC,CAAC;IAAEuG,SAAS,EAAE,CAAC;MACZ2E,IAAI,EAAElL;IACV,CAAC,CAAC;IAAEwG,mBAAmB,EAAE,CAAC;MACtB0E,IAAI,EAAElL;IACV,CAAC,CAAC;IAAEyG,UAAU,EAAE,CAAC;MACbyE,IAAI,EAAElL;IACV,CAAC,CAAC;IAAE0G,WAAW,EAAE,CAAC;MACdwE,IAAI,EAAElL;IACV,CAAC,CAAC;IAAE2G,UAAU,EAAE,CAAC;MACbuE,IAAI,EAAElL;IACV,CAAC,CAAC;IAAE4G,SAAS,EAAE,CAAC;MACZsE,IAAI,EAAElL;IACV,CAAC,CAAC;IAAEmE,kBAAkB,EAAE,CAAC;MACrB+G,IAAI,EAAElL;IACV,CAAC,CAAC;IAAEoE,cAAc,EAAE,CAAC;MACjB8G,IAAI,EAAElL;IACV,CAAC,CAAC;IAAE6G,WAAW,EAAE,CAAC;MACdqE,IAAI,EAAElL;IACV,CAAC,CAAC;IAAE8G,eAAe,EAAE,CAAC;MAClBoE,IAAI,EAAEjL;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAMiM,0BAA0B,CAAC;EAC7B;IAAS,IAAI,CAAC5B,IAAI,YAAA6B,mCAAA3B,CAAA;MAAA,YAAAA,CAAA,IAAwF0B,0BAA0B;IAAA,CAAkD;EAAE;EACxL;IAAS,IAAI,CAACE,IAAI,kBAxD8EvM,EAAE,CAAAwM,gBAAA;MAAAnB,IAAA,EAwDSgB;IAA0B,EAAG;EAAE;EAC1I;IAAS,IAAI,CAACI,IAAI,kBAzD8EzM,EAAE,CAAA0M,gBAAA,IAyDsC;EAAE;AAC9I;AACA;EAAA,QAAAX,SAAA,oBAAAA,SAAA,KA3DoG/L,EAAE,CAAAgM,iBAAA,CA2DXK,0BAA0B,EAAc,CAAC;IACxHhB,IAAI,EAAEhL;EACV,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA,MAAMsM,oBAAoB,CAAC;EACvBpH,WAAWA,CAAA,EAAG,CAAE;EAChB;IAAS,IAAI,CAACkF,IAAI,YAAAmC,6BAAAjC,CAAA;MAAA,YAAAA,CAAA,IAAwFgC,oBAAoB;IAAA,CAAmD;EAAE;EACnL;IAAS,IAAI,CAACxB,IAAI,kBAtE8EnL,EAAE,CAAAoL,iBAAA;MAAAC,IAAA,EAsEJsB,oBAAoB;MAAArB,SAAA;MAAAC,SAAA;MAAAI,UAAA;IAAA,EAAgH;EAAE;AACxO;AACA;EAAA,QAAAI,SAAA,oBAAAA,SAAA,KAxEoG/L,EAAE,CAAAgM,iBAAA,CAwEXW,oBAAoB,EAAc,CAAC;IAClHtB,IAAI,EAAEnL,SAAS;IACf+L,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,gBAAgB;MAC1BC,IAAI,EAAE;QACFC,KAAK,EAAE;MACX,CAAC;MACDT,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA;AACA;AACA;AACA,MAAMkB,yBAAyB,CAAC;EAC5BtH,WAAWA,CAACI,QAAQ,EAAEmH,sBAAsB,EAAErH,UAAU,EAAE;IACtD,IAAI,CAACE,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACmH,sBAAsB,GAAGA,sBAAsB;IACpD,IAAI,CAACrH,UAAU,GAAGA,UAAU;EAChC;EACAyB,eAAeA,CAAA,EAAG;IACd,MAAM6F,aAAa,GAAG,IAAI,CAACpH,QAAQ,CAACqH,UAAU,CAAC,IAAI,CAACvH,UAAU,CAACW,aAAa,CAAC;IAC7E,IAAI,IAAI,CAAC0G,sBAAsB,IAAIC,aAAa,EAAE;MAC9C,IAAI,CAACpH,QAAQ,CAACsH,QAAQ,CAACF,aAAa,EAAE,qBAAqB,CAAC;IAChE;EACJ;EACA;IAAS,IAAI,CAACtC,IAAI,YAAAyC,kCAAAvC,CAAA;MAAA,YAAAA,CAAA,IAAwFkC,yBAAyB,EAnGnC7M,EAAE,CAAA4K,iBAAA,CAmGmD5K,EAAE,CAACgL,SAAS,GAnGjEhL,EAAE,CAAA4K,iBAAA,CAmG4EpI,IAAI,CAAC2K,sBAAsB,MAnGzGnN,EAAE,CAAA4K,iBAAA,CAmGgJ5K,EAAE,CAAC8K,UAAU;IAAA,CAA4C;EAAE;EAC7S;IAAS,IAAI,CAACK,IAAI,kBApG8EnL,EAAE,CAAAoL,iBAAA;MAAAC,IAAA,EAoGJwB,yBAAyB;MAAAvB,SAAA;MAAAK,UAAA;IAAA,EAA2E;EAAE;AACxM;AACA;EAAA,QAAAI,SAAA,oBAAAA,SAAA,KAtGoG/L,EAAE,CAAAgM,iBAAA,CAsGXa,yBAAyB,EAAc,CAAC;IACvHxB,IAAI,EAAEnL,SAAS;IACf+L,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,0BAA0B;MACpCP,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEN,IAAI,EAAErL,EAAE,CAACgL;EAAU,CAAC,EAAE;IAAEK,IAAI,EAAE7I,IAAI,CAAC2K,sBAAsB;IAAEC,UAAU,EAAE,CAAC;MACzF/B,IAAI,EAAE/K;IACV,CAAC,EAAE;MACC+K,IAAI,EAAE9K;IACV,CAAC;EAAE,CAAC,EAAE;IAAE8K,IAAI,EAAErL,EAAE,CAAC8K;EAAW,CAAC,CAAC;AAAA;AAE9C,MAAMuC,uBAAuB,CAAC;EAC1BxJ,gBAAgBA,CAACmG,KAAK,EAAE;IACpB,IAAI,CAACD,qBAAqB,CAACxB,IAAI,CAACyB,KAAK,CAAC;EAC1C;EACAjG,aAAaA,CAACgE,OAAO,EAAE;IACnB,IAAI,CAACT,WAAW,CAACiC,IAAI,CAACxB,OAAO,CAAC;EAClC;EACAzC,QAAQA,CAACH,GAAG,EAAEC,KAAK,EAAE;IACjB,IAAI,CAACD,GAAG,CAAC,GAAGC,KAAK;IACjB,IAAI,CAACkI,GAAG,CAACC,YAAY,CAAC,CAAC;EAC3B;EACAhI,WAAWA,CAAC+H,GAAG,EAAE7H,UAAU,EAAEE,QAAQ,EAAEC,gBAAgB,EAAE4H,aAAa,EAAEC,cAAc,EAAEhJ,WAAW,EAAE;IACjG,IAAI,CAAC6I,GAAG,GAAGA,GAAG;IACd,IAAI,CAAC7H,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACE,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAAC4H,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAAChJ,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAAC6C,WAAW,GAAG,IAAIvG,eAAe,CAAC,KAAK,CAAC;IAC7C,IAAI,CAAC+G,mBAAmB,GAAG,IAAI,CAAC0F,aAAa,CAAC1F,mBAAmB;IACjE,IAAI,CAACH,wBAAwB,GAAG,IAAI,CAAC6F,aAAa,CAAC7F,wBAAwB;IAC3E,IAAI,CAACoC,qBAAqB,GAAG,IAAI9J,YAAY,CAAC,CAAC;IAC/C,IAAI,CAACqE,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACC,cAAc,GAAG,CAAC,CAAC;IACxB,IAAI,CAACH,GAAG,GAAG,KAAK;IAChB,IAAI,CAAC4B,QAAQ,GAAG,IAAIlF,OAAO,CAAC,CAAC;EACjC;EACA4M,QAAQA,CAAA,EAAG;IACP,IAAI,CAACD,cAAc,CAACE,MAAM,EAAEvG,IAAI,CAACzF,SAAS,CAAC,IAAI,CAACqE,QAAQ,CAAC,CAAC,CAACkC,SAAS,CAAE0F,SAAS,IAAK;MAChF,IAAI,CAACxJ,GAAG,GAAGwJ,SAAS;MACpB,IAAI,CAACN,GAAG,CAACO,aAAa,CAAC,CAAC;IAC5B,CAAC,CAAC;IACF,IAAI,CAACzJ,GAAG,GAAG,IAAI,CAACqJ,cAAc,CAACrI,KAAK;EACxC;EACA0I,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACnI,QAAQ,CAACoI,WAAW,CAAC,IAAI,CAACpI,QAAQ,CAACqH,UAAU,CAAC,IAAI,CAACvH,UAAU,CAACW,aAAa,CAAC,EAAE,IAAI,CAACX,UAAU,CAACW,aAAa,CAAC;EACrH;EACA+D,WAAWA,CAAA,EAAG;IACV,IAAI,CAACnE,QAAQ,CAACuD,IAAI,CAAC,CAAC;IACpB,IAAI,CAACvD,QAAQ,CAACoE,QAAQ,CAAC,CAAC;EAC5B;EACA;IAAS,IAAI,CAACK,IAAI,YAAAuD,gCAAArD,CAAA;MAAA,YAAAA,CAAA,IAAwF0C,uBAAuB,EA5JjCrN,EAAE,CAAA4K,iBAAA,CA4JiD5K,EAAE,CAACiO,iBAAiB,GA5JvEjO,EAAE,CAAA4K,iBAAA,CA4JkF5K,EAAE,CAAC8K,UAAU,GA5JjG9K,EAAE,CAAA4K,iBAAA,CA4J4G5K,EAAE,CAACgL,SAAS,GA5J1HhL,EAAE,CAAA4K,iBAAA,CA4JqI5K,EAAE,CAACiL,gBAAgB,GA5J1JjL,EAAE,CAAA4K,iBAAA,CA4JqKxI,IAAI,CAACC,WAAW,GA5JvLrC,EAAE,CAAA4K,iBAAA,CA4JkM9H,IAAI,CAACoL,cAAc,MA5JvNlO,EAAE,CAAA4K,iBAAA,CA4JkPhI,IAAI,CAACC,sBAAsB;IAAA,CAAwE;EAAE;EACzb;IAAS,IAAI,CAACsL,IAAI,kBA7J8EnO,EAAE,CAAAoO,iBAAA;MAAA/C,IAAA,EA6JJgC,uBAAuB;MAAA/B,SAAA;MAAA+C,SAAA,WAAAC,8BAAArL,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA7JrBjD,EAAE,CAAAuO,WAAA,CAoKtB/N,WAAW;QAAA;QAAA,IAAAyC,EAAA;UAAA,IAAAuL,EAAA;UApKSxO,EAAE,CAAAyO,cAAA,CAAAD,EAAA,GAAFxO,EAAE,CAAA0O,WAAA,QAAAxL,GAAA,CAAA0G,WAAA,GAAA4E,EAAA,CAAA5M,KAAA;QAAA;MAAA;MAAA8J,QAAA;MAAAC,UAAA;MAAAC,QAAA,GAAF5L,EAAE,CAAA2O,kBAAA,CA6JkF,CAC5KtM,WAAW,EACX;MACA;QACIuM,OAAO,EAAEtM,2BAA2B;QACpCuM,QAAQ,EAAE;MACd,CAAC,CACJ,GApK2F7O,EAAE,CAAA8O,mBAAA;MAAAC,kBAAA,EAAAhM,GAAA;MAAAiM,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAnM,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFjD,EAAE,CAAAqP,eAAA;UAAFrP,EAAE,CAAAsP,UAAA,IAAAtM,8CAAA,qBAqKtF,CAAC;QAAA;MAAA;MAAAuM,YAAA,GAgB8C9M,OAAO,EAAoFC,OAAO,EAA2EG,sBAAsB;MAAA2M,aAAA;MAAAC,IAAA;QAAAC,SAAA,EAAsG,CAAC/M,WAAW;MAAC;MAAAgN,eAAA;IAAA,EAAiG;EAAE;AACxd;AACA;EAAA,QAAA5D,SAAA,oBAAAA,SAAA,KAvLoG/L,EAAE,CAAAgM,iBAAA,CAuLXqB,uBAAuB,EAAc,CAAC;IACrHhC,IAAI,EAAE5K,SAAS;IACfwL,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAG,kBAAiB;MAC5BR,QAAQ,EAAG,gBAAe;MAC1BkE,UAAU,EAAE,CAACjN,WAAW,CAAC;MACzBkN,SAAS,EAAE,CACPxN,WAAW,EACX;MACA;QACIuM,OAAO,EAAEtM,2BAA2B;QACpCuM,QAAQ,EAAE;MACd,CAAC,CACJ;MACDM,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiBW,mBAAmB,EAAE,KAAK;MAC1BN,aAAa,EAAE9O,iBAAiB,CAACqP,IAAI;MACrCJ,eAAe,EAAEhP,uBAAuB,CAACqP,MAAM;MAC/CC,OAAO,EAAE,CAACxN,OAAO,EAAEC,OAAO,EAAEG,sBAAsB,CAAC;MACnD8I,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEN,IAAI,EAAErL,EAAE,CAACiO;EAAkB,CAAC,EAAE;IAAE5C,IAAI,EAAErL,EAAE,CAAC8K;EAAW,CAAC,EAAE;IAAEO,IAAI,EAAErL,EAAE,CAACgL;EAAU,CAAC,EAAE;IAAEK,IAAI,EAAErL,EAAE,CAACiL;EAAiB,CAAC,EAAE;IAAEI,IAAI,EAAEjJ,IAAI,CAACC;EAAY,CAAC,EAAE;IAAEgJ,IAAI,EAAEvI,IAAI,CAACoL,cAAc;IAAEd,UAAU,EAAE,CAAC;MACrM/B,IAAI,EAAE9K;IACV,CAAC;EAAE,CAAC,EAAE;IAAE8K,IAAI,EAAEzI,IAAI,CAACC,sBAAsB;IAAEuK,UAAU,EAAE,CAAC;MACpD/B,IAAI,EAAE/K;IACV,CAAC,EAAE;MACC+K,IAAI,EAAE9K;IACV,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEqJ,WAAW,EAAE,CAAC;MACvCyB,IAAI,EAAEzK,SAAS;MACfqL,IAAI,EAAE,CAACzL,WAAW,EAAE;QAAE0P,MAAM,EAAE;MAAK,CAAC;IACxC,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,CAAC;EACnB;IAAS,IAAI,CAAC1F,IAAI,YAAA2F,yBAAAzF,CAAA;MAAA,YAAAA,CAAA,IAAwFwF,gBAAgB;IAAA,CAAkD;EAAE;EAC9K;IAAS,IAAI,CAAC5D,IAAI,kBA9O8EvM,EAAE,CAAAwM,gBAAA;MAAAnB,IAAA,EA8OS8E,gBAAgB;MAAAF,OAAA,GAAYhL,mBAAmB,EAClJ0H,oBAAoB,EACpBU,uBAAuB,EACvBR,yBAAyB,EACzBR,0BAA0B;MAAAgE,OAAA,GAAa9N,YAAY,EAAE0C,mBAAmB,EAAE0H,oBAAoB,EAAEU,uBAAuB,EAAER,yBAAyB;IAAA,EAAI;EAAE;EAChK;IAAS,IAAI,CAACJ,IAAI,kBAnP8EzM,EAAE,CAAA0M,gBAAA;MAAAuD,OAAA,GAmPqC5D,0BAA0B,EAAE9J,YAAY;IAAA,EAAI;EAAE;AACzL;AACA;EAAA,QAAAwJ,SAAA,oBAAAA,SAAA,KArPoG/L,EAAE,CAAAgM,iBAAA,CAqPXmE,gBAAgB,EAAc,CAAC;IAC9G9E,IAAI,EAAEhL,QAAQ;IACd4L,IAAI,EAAE,CAAC;MACCgE,OAAO,EAAE,CACLhL,mBAAmB,EACnB0H,oBAAoB,EACpBU,uBAAuB,EACvBR,yBAAyB,EACzBR,0BAA0B,CAC7B;MACDgE,OAAO,EAAE,CAAC9N,YAAY,EAAE0C,mBAAmB,EAAE0H,oBAAoB,EAAEU,uBAAuB,EAAER,yBAAyB;IACzH,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA,MAAMyD,iBAAiB,GAAG,CACtB,IAAIpO,sBAAsB,CAAC;EAAEqO,OAAO,EAAE,OAAO;EAAEC,OAAO,EAAE;AAAM,CAAC,EAAE;EAAEC,QAAQ,EAAE,OAAO;EAAEC,QAAQ,EAAE;AAAM,CAAC,CAAC,EACxG,IAAIxO,sBAAsB,CAAC;EAAEqO,OAAO,EAAE,OAAO;EAAEC,OAAO,EAAE;AAAM,CAAC,EAAE;EAAEC,QAAQ,EAAE,OAAO;EAAEC,QAAQ,EAAE;AAAS,CAAC,CAAC,EAC3G,IAAIxO,sBAAsB,CAAC;EAAEqO,OAAO,EAAE,OAAO;EAAEC,OAAO,EAAE;AAAM,CAAC,EAAE;EAAEC,QAAQ,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAS,CAAC,CAAC,EACzG,IAAIxO,sBAAsB,CAAC;EAAEqO,OAAO,EAAE,OAAO;EAAEC,OAAO,EAAE;AAAM,CAAC,EAAE;EAAEC,QAAQ,EAAE,KAAK;EAAEC,QAAQ,EAAE;AAAM,CAAC,CAAC,CACzG;AACD,MAAMC,oBAAoB,CAAC;EACvBpL,WAAWA,CAACqL,MAAM,EAAElL,OAAO,EAAE;IACzB,IAAI,CAACkL,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAClL,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACK,UAAU,GAAG,IAAI;IACtB,IAAI,CAAC8K,iBAAiB,GAAGzP,YAAY,CAACF,KAAK;EAC/C;EACAsH,MAAMA,CAAChF,MAAM,EAAEsN,uBAAuB,EAAE;IACpC,IAAI,CAACC,KAAK,CAAC,IAAI,CAAC;IAChB,MAAM;MAAEC,CAAC;MAAEC;IAAE,CAAC,GAAGzN,MAAM;IACvB,IAAIA,MAAM,YAAY0N,UAAU,EAAE;MAC9B1N,MAAM,CAAC2N,cAAc,CAAC,CAAC;IAC3B;IACA,MAAMlL,gBAAgB,GAAG,IAAI,CAACP,OAAO,CAChCQ,QAAQ,CAAC,CAAC,CACVC,mBAAmB,CAAC;MAAE6K,CAAC;MAAEC;IAAE,CAAC,CAAC,CAC7BvH,aAAa,CAAC4G,iBAAiB,CAAC,CAChChK,qBAAqB,CAAC,eAAe,CAAC;IAC3C,IAAI,CAACP,UAAU,GAAG,IAAI,CAACL,OAAO,CAAC8C,MAAM,CAAC;MAClCvC,gBAAgB;MAChByC,mBAAmB,EAAE,IAAI;MACzBE,cAAc,EAAE,IAAI,CAAClD,OAAO,CAACmD,gBAAgB,CAACkI,KAAK,CAAC;IACxD,CAAC,CAAC;IACF,IAAI,CAACF,iBAAiB,GAAG,IAAIzP,YAAY,CAAC,CAAC;IAC3C,IAAI,CAACyP,iBAAiB,CAACO,GAAG,CAACN,uBAAuB,CAACnJ,wBAAwB,CAACO,SAAS,CAAC,MAAM,IAAI,CAAC6I,KAAK,CAAC,CAAC,CAAC,CAAC;IAC1G,IAAI,CAACF,iBAAiB,CAACO,GAAG,CAAC,IAAI,CAACR,MAAM,CAACS,iBAAiB,CAAC,MAAMrQ,KAAK,CAACC,SAAS,CAACqQ,QAAQ,EAAE,OAAO,CAAC,CAAClK,IAAI,CAAC5F,MAAM,CAACwI,KAAK,IAAI,CAAC,CAAC,IAAI,CAACjE,UAAU,IAAI,CAAC,IAAI,CAACA,UAAU,CAACwL,cAAc,CAACpI,QAAQ,CAACa,KAAK,CAACZ,MAAM,CAAC,CAAC,EACnM;IACA5H,MAAM,CAACwI,KAAK,IAAIA,KAAK,CAACwH,MAAM,KAAK,CAAC,CAAC,CAAC,EAAEvQ,SAAS,CAACqQ,QAAQ,EAAE,SAAS,CAAC,CAAClK,IAAI,CAAC5F,MAAM,CAACwI,KAAK,IAAIA,KAAK,CAAC7E,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC,CAC9GiC,IAAI,CAACxF,KAAK,CAAC,CAAC,CAAC,CACbsG,SAAS,CAAC,MAAM,IAAI,CAAC0I,MAAM,CAACa,GAAG,CAAC,MAAM,IAAI,CAACV,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3D,OAAO,IAAI,CAAChL,UAAU,CAAC8D,MAAM,CAAC,IAAI9J,cAAc,CAAC+Q,uBAAuB,CAAClH,WAAW,EAAEkH,uBAAuB,CAAClL,gBAAgB,CAAC,CAAC;EACpI;EACAmL,KAAKA,CAACW,KAAK,GAAG,KAAK,EAAE;IACjB,IAAI,IAAI,CAAC3L,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAAC+D,MAAM,CAAC,CAAC;MACxB,IAAI4H,KAAK,EAAE;QACP,IAAI,CAAC3L,UAAU,CAACmE,OAAO,CAAC,CAAC;MAC7B;MACA,IAAI,CAACnE,UAAU,GAAG,IAAI;MACtB,IAAI,CAAC8K,iBAAiB,CAACc,WAAW,CAAC,CAAC;IACxC;EACJ;EACA;IAAS,IAAI,CAAClH,IAAI,YAAAmH,6BAAAjH,CAAA;MAAA,YAAAA,CAAA,IAAwFgG,oBAAoB,EAvT9B3Q,EAAE,CAAA6R,QAAA,CAuT8C7R,EAAE,CAAC8R,MAAM,GAvTzD9R,EAAE,CAAA6R,QAAA,CAuToE5P,EAAE,CAAC8I,OAAO;IAAA,CAA6C;EAAE;EAC/N;IAAS,IAAI,CAACgH,KAAK,kBAxT6E/R,EAAE,CAAAgS,kBAAA;MAAAC,KAAA,EAwTYtB,oBAAoB;MAAAuB,OAAA,EAApBvB,oBAAoB,CAAAlG,IAAA;MAAA0H,UAAA,EAAc9F;IAA0B,EAAG;EAAE;AACnL;AACA;EAAA,QAAAN,SAAA,oBAAAA,SAAA,KA1ToG/L,EAAE,CAAAgM,iBAAA,CA0TX2E,oBAAoB,EAAc,CAAC;IAClHtF,IAAI,EAAExK,UAAU;IAChBoL,IAAI,EAAE,CAAC;MACCkG,UAAU,EAAE9F;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEhB,IAAI,EAAErL,EAAE,CAAC8R;EAAO,CAAC,EAAE;IAAEzG,IAAI,EAAEpJ,EAAE,CAAC8I;EAAQ,CAAC,CAAC;AAAA;;AAE7E;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAAS4F,oBAAoB,EAAEtE,0BAA0B,EAAEM,oBAAoB,EAAE1H,mBAAmB,EAAEkL,gBAAgB,EAAEtD,yBAAyB,EAAEQ,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}