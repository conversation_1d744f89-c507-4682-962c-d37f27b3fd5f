{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./project-memory.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./project-memory.component.css?ngResource\";\nimport { CommonModule } from '@angular/common';\nimport { Component } from '@angular/core';\nimport { FormsModule } from '@angular/forms';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';\nimport { ProjectMemoryServiceProxy, WorkspaceServiceProxy } from '../../../shared/service-proxies/service-proxies';\nimport { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';\nimport { NzModalService } from 'ng-zorro-antd/modal';\nimport { AddOrEditMemoryComponent } from '../../dialogs/add-or-edit-memory/add-or-edit-memory.component';\nlet ProjectMemoryComponent = class ProjectMemoryComponent {\n  constructor(projectMemoryService, route, router, workspaceService, modalService) {\n    this.projectMemoryService = projectMemoryService;\n    this.route = route;\n    this.router = router;\n    this.workspaceService = workspaceService;\n    this.modalService = modalService;\n    this.isSidebarOpen = false; // Sidebar is closed by default\n    this.showAddProjectMemoryDialog = false; // Dialog is hidden by default\n    this.projectMemory = {\n      projectCategory: '',\n      projectDescription: '',\n      status: '',\n      workspace: ''\n    };\n    this.projectMemories = []; // Array to store project memories\n    this.filteredMemories = []; // Array to store filtered project memories\n    this.paginatedMemories = []; // Array to store paginated project memories\n    this.isUpdating = false; // Flag to track if the form is in update mode\n    // Pagination properties\n    this.currentPage = 1;\n    this.pageSize = 10;\n    this.totalPages = 1;\n    // Make Math available to the template\n    this.Math = Math;\n  } //\n  ngOnInit() {\n    let router = this.router.url.split('/');\n    this.workspaceName = router[2];\n    this.loadAllProjectMemories();\n  }\n  loadAllProjectMemories() {\n    this.projectMemoryService.getAllProjectMemory(this.workspaceName).subscribe(response => {\n      this.projectMemories = response;\n      this.filteredMemories = [...this.projectMemories];\n      this.updatePagination();\n      console.log('Project Memories loaded:', this.projectMemories);\n    });\n  }\n  /**\n   * Update pagination based on current page and page size\n   */\n  updatePagination() {\n    // Calculate total pages (minimum 1 page)\n    this.totalPages = Math.max(1, Math.ceil(this.filteredMemories.length / this.pageSize));\n    // Ensure current page is within bounds\n    if (this.currentPage < 1) this.currentPage = 1;\n    if (this.currentPage > this.totalPages) this.currentPage = this.totalPages;\n    // Get current page of memories\n    if (this.filteredMemories.length === 0) {\n      this.paginatedMemories = [];\n    } else {\n      const startIndex = (this.currentPage - 1) * this.pageSize;\n      const endIndex = Math.min(startIndex + this.pageSize, this.filteredMemories.length);\n      this.paginatedMemories = this.filteredMemories.slice(startIndex, endIndex);\n    }\n  }\n  /**\n   * Go to a specific page\n   */\n  goToPage(page) {\n    if (page >= 1 && page <= this.totalPages) {\n      this.currentPage = page;\n      this.updatePagination();\n    }\n  }\n  /**\n   * Go to the previous page\n   */\n  previousPage() {\n    if (this.currentPage > 1) {\n      this.currentPage--;\n      this.updatePagination();\n    }\n  }\n  /**\n   * Go to the next page\n   */\n  nextPage() {\n    if (this.currentPage < this.totalPages) {\n      this.currentPage++;\n      this.updatePagination();\n    }\n  }\n  /**\n   * Get page numbers to display in pagination\n   * This method is no longer used with the updated pagination UI\n   * Kept for reference\n   */\n  getPageNumbers() {\n    const pages = [];\n    if (this.totalPages <= 5) {\n      // If 5 or fewer pages, show all page numbers\n      for (let i = 1; i <= this.totalPages; i++) {\n        pages.push(i);\n      }\n    } else {\n      // Always show first page\n      pages.push(1);\n      if (this.currentPage > 3) {\n        // Show ellipsis if current page is far from the beginning\n        pages.push('...');\n      }\n      // Show current page and surrounding pages\n      const startPage = Math.max(2, this.currentPage - 1);\n      const endPage = Math.min(this.totalPages - 1, this.currentPage + 1);\n      for (let i = startPage; i <= endPage; i++) {\n        if (i !== 1 && i !== this.totalPages) {\n          pages.push(i);\n        }\n      }\n      if (this.currentPage < this.totalPages - 2) {\n        // Show ellipsis if current page is far from the end\n        pages.push('...');\n      }\n      // Always show last page\n      pages.push(this.totalPages);\n    }\n    return pages;\n  }\n  addProjectMemory() {\n    let modalRef = this.modalService.create({\n      nzContent: AddOrEditMemoryComponent,\n      nzData: {\n        isUpdating: this.isUpdating,\n        project_memo: null\n      },\n      nzFooter: null,\n      nzWidth: '500px'\n    });\n    modalRef.afterClose.subscribe(result => {\n      if (!result) return; // Check if result is null or undefined\n      this.projectMemories.push(result);\n      this.filteredMemories = [...this.projectMemories];\n      this.updatePagination();\n    });\n    // this.isUpdating = false; // Reset the flag to false when adding a new memory\n    // if (\n    //   this.projectMemory.projectCategory &&\n    //   this.projectMemory.projectDescription\n    // ) {\n    //   const newMemory: any = {\n    //     ...this.projectMemory,\n    //     workspace: this.workspaceName,\n    //   };\n    //   this.projectMemoryService\n    //     .saveProjectMemory(newMemory)\n    //     .subscribe((response: any) => {\n    //       this.projectMemories.push(response); // Add the new memory to the list\n    //       this.showAddProjectMemoryDialog = false;\n    //       this.clearInput(); // Reset form\n    //     });\n    // } else {\n    //   console.log('Please fill all fields');\n    // }\n  }\n  onUpdate(memory) {\n    this.isUpdating = true; // Set the flag to true for update mode\n    this.projectMemory = {\n      ...memory\n    }; // Populate the form with the selected memory data\n    let modalRef = this.modalService.create({\n      nzContent: AddOrEditMemoryComponent,\n      nzData: {\n        isUpdating: this.isUpdating,\n        project_memo: this.projectMemory\n      },\n      nzFooter: null,\n      nzWidth: '500px'\n    });\n    modalRef.afterClose.subscribe(result => {\n      if (!result) return; // Check if result is null or undefined\n      // Update the memory in the list\n      const index = this.projectMemories.findIndex(m => m.id === result.id);\n      if (index !== -1) {\n        this.projectMemories[index] = result;\n        this.filteredMemories = [...this.projectMemories];\n        this.updatePagination();\n      }\n    });\n  }\n  editMemory() {\n    // Logic to edit the memory\n    let obj = {\n      ...this.projectMemory\n    };\n    this.projectMemoryService.saveProjectMemory(obj).subscribe(response => {\n      console.log('Project Memory updated:', response);\n      // Update the memory in the list\n      const index = this.projectMemories.findIndex(m => m.id === response.id);\n      if (index !== -1) {\n        this.projectMemories[index] = response;\n        this.filteredMemories = [...this.projectMemories];\n        this.updatePagination();\n      } else {\n        this.loadAllProjectMemories(); // Reload if not found\n      }\n      this.clearInput();\n      this.showAddProjectMemoryDialog = false; // Hide the dialog after saving\n    });\n  }\n  deleteMemory(id) {\n    this.projectMemoryService.deleteProjectMemory(id).subscribe(response => {\n      console.log('Project Memory deleted:', response);\n      this.projectMemories = this.projectMemories.filter(memory => memory.id !== id);\n      this.filteredMemories = [...this.projectMemories];\n      this.updatePagination();\n    });\n  }\n  clearInput() {\n    this.projectMemory = {\n      projectCategory: '',\n      projectDescription: '',\n      status: '',\n      workspace: this.workspaceName\n    }; // Reset form\n    this.isUpdating = false; // Reset the flag to false when clearing input\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: ProjectMemoryServiceProxy\n    }, {\n      type: ActivatedRoute\n    }, {\n      type: Router\n    }, {\n      type: WorkspaceServiceProxy\n    }, {\n      type: NzModalService\n    }];\n  }\n};\nProjectMemoryComponent = __decorate([Component({\n  selector: 'app-project-memory',\n  standalone: true,\n  imports: [FormsModule, CommonModule, NzBreadCrumbModule, ServiceProxyModule],\n  template: __NG_CLI_RESOURCE__0,\n  providers: [NzModalService],\n  styles: [__NG_CLI_RESOURCE__1]\n})], ProjectMemoryComponent);\nexport { ProjectMemoryComponent };", "map": {"version": 3, "names": ["CommonModule", "Component", "FormsModule", "ActivatedRoute", "Router", "NzBreadCrumbModule", "ProjectMemoryServiceProxy", "WorkspaceServiceProxy", "ServiceProxyModule", "NzModalService", "AddOrEditMemoryComponent", "ProjectMemoryComponent", "constructor", "projectMemoryService", "route", "router", "workspaceService", "modalService", "isSidebarOpen", "showAddProjectMemoryDialog", "projectMemory", "projectCategory", "projectDescription", "status", "workspace", "projectMemories", "filteredMemories", "paginatedMemories", "isUpdating", "currentPage", "pageSize", "totalPages", "Math", "ngOnInit", "url", "split", "workspaceName", "loadAllProjectMemories", "getAllProjectMemory", "subscribe", "response", "updatePagination", "console", "log", "max", "ceil", "length", "startIndex", "endIndex", "min", "slice", "goToPage", "page", "previousPage", "nextPage", "getPageNumbers", "pages", "i", "push", "startPage", "endPage", "addProjectMemory", "modalRef", "create", "nzContent", "nzData", "project_memo", "nz<PERSON><PERSON>er", "nzWidth", "afterClose", "result", "onUpdate", "memory", "index", "findIndex", "m", "id", "edit<PERSON><PERSON>ory", "obj", "saveProjectMemory", "clearInput", "deleteMemory", "deleteProjectMemory", "filter", "__decorate", "selector", "standalone", "imports", "template", "__NG_CLI_RESOURCE__0", "providers"], "sources": ["C:\\Users\\<USER>\\source\\ai-hub\\WebApp\\src\\app\\workspaces\\project-memory\\project-memory.component.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\r\nimport { Component } from '@angular/core';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { ActivatedRoute, Router, RouterLink } from '@angular/router';\r\nimport { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';\r\nimport {\r\n  ProjectMemoryServiceProxy,\r\n  WorkspaceServiceProxy,\r\n} from '../../../shared/service-proxies/service-proxies';\r\nimport { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';\r\nimport { NzModalService } from 'ng-zorro-antd/modal';\r\nimport { AddorEditWorksapceComponent } from '../../dialogs/addor-edit-worksapce/addor-edit-worksapce.component';\r\nimport { AddOrEditMemoryComponent } from '../../dialogs/add-or-edit-memory/add-or-edit-memory.component';\r\n\r\n@Component({\r\n  selector: 'app-project-memory',\r\n  standalone: true,\r\n  imports: [FormsModule, CommonModule, NzBreadCrumbModule, ServiceProxyModule],\r\n  templateUrl: './project-memory.component.html',\r\n  styleUrl: './project-memory.component.css',\r\n  providers: [NzModalService],\r\n})\r\nexport class ProjectMemoryComponent {\r\n  isSidebarOpen = false; // Sidebar is closed by default\r\n  showAddProjectMemoryDialog = false; // Dialog is hidden by default\r\n  workspaceName: any;\r\n  projectMemory = {\r\n    projectCategory: '',\r\n    projectDescription: '',\r\n    status: '',\r\n    workspace: '',\r\n  };\r\n  projectMemories: any[] = []; // Array to store project memories\r\n  filteredMemories: any[] = []; // Array to store filtered project memories\r\n  paginatedMemories: any[] = []; // Array to store paginated project memories\r\n  workspaceId: any; // Variable to store the workspace ID\r\n  isUpdating: boolean = false; // Flag to track if the form is in update mode\r\n\r\n  // Pagination properties\r\n  currentPage: number = 1;\r\n  pageSize: number = 10;\r\n  totalPages: number = 1;\r\n\r\n  // Make Math available to the template\r\n  Math = Math;\r\n  constructor(\r\n    private projectMemoryService: ProjectMemoryServiceProxy,\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private workspaceService: WorkspaceServiceProxy,\r\n    private modalService: NzModalService\r\n  ) {} //\r\n  ngOnInit(): void {\r\n    let router = this.router.url.split('/');\r\n    this.workspaceName = router[2];\r\n    this.loadAllProjectMemories();\r\n  }\r\n\r\n  loadAllProjectMemories() {\r\n    this.projectMemoryService\r\n      .getAllProjectMemory(this.workspaceName)\r\n      .subscribe((response: any) => {\r\n        this.projectMemories = response;\r\n        this.filteredMemories = [...this.projectMemories];\r\n        this.updatePagination();\r\n        console.log('Project Memories loaded:', this.projectMemories);\r\n      });\r\n  }\r\n\r\n  /**\r\n   * Update pagination based on current page and page size\r\n   */\r\n  updatePagination() {\r\n    // Calculate total pages (minimum 1 page)\r\n    this.totalPages = Math.max(1, Math.ceil(this.filteredMemories.length / this.pageSize));\r\n\r\n    // Ensure current page is within bounds\r\n    if (this.currentPage < 1) this.currentPage = 1;\r\n    if (this.currentPage > this.totalPages) this.currentPage = this.totalPages;\r\n\r\n    // Get current page of memories\r\n    if (this.filteredMemories.length === 0) {\r\n      this.paginatedMemories = [];\r\n    } else {\r\n      const startIndex = (this.currentPage - 1) * this.pageSize;\r\n      const endIndex = Math.min(startIndex + this.pageSize, this.filteredMemories.length);\r\n      this.paginatedMemories = this.filteredMemories.slice(startIndex, endIndex);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Go to a specific page\r\n   */\r\n  goToPage(page: number) {\r\n    if (page >= 1 && page <= this.totalPages) {\r\n      this.currentPage = page;\r\n      this.updatePagination();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Go to the previous page\r\n   */\r\n  previousPage() {\r\n    if (this.currentPage > 1) {\r\n      this.currentPage--;\r\n      this.updatePagination();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Go to the next page\r\n   */\r\n  nextPage() {\r\n    if (this.currentPage < this.totalPages) {\r\n      this.currentPage++;\r\n      this.updatePagination();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get page numbers to display in pagination\r\n   * This method is no longer used with the updated pagination UI\r\n   * Kept for reference\r\n   */\r\n  getPageNumbers(): (number | string)[] {\r\n    const pages: (number | string)[] = [];\r\n\r\n    if (this.totalPages <= 5) {\r\n      // If 5 or fewer pages, show all page numbers\r\n      for (let i = 1; i <= this.totalPages; i++) {\r\n        pages.push(i);\r\n      }\r\n    } else {\r\n      // Always show first page\r\n      pages.push(1);\r\n\r\n      if (this.currentPage > 3) {\r\n        // Show ellipsis if current page is far from the beginning\r\n        pages.push('...');\r\n      }\r\n\r\n      // Show current page and surrounding pages\r\n      const startPage = Math.max(2, this.currentPage - 1);\r\n      const endPage = Math.min(this.totalPages - 1, this.currentPage + 1);\r\n\r\n      for (let i = startPage; i <= endPage; i++) {\r\n        if (i !== 1 && i !== this.totalPages) {\r\n          pages.push(i);\r\n        }\r\n      }\r\n\r\n      if (this.currentPage < this.totalPages - 2) {\r\n        // Show ellipsis if current page is far from the end\r\n        pages.push('...');\r\n      }\r\n\r\n      // Always show last page\r\n      pages.push(this.totalPages);\r\n    }\r\n\r\n    return pages;\r\n  }\r\n  addProjectMemory() {\r\n    let modalRef = this.modalService.create({\r\n      nzContent: AddOrEditMemoryComponent,\r\n      nzData: {\r\n        isUpdating: this.isUpdating,\r\n        project_memo: null,\r\n      },\r\n      nzFooter: null, // We handle the footer in the dialog component\r\n      nzWidth: '500px',\r\n    });\r\n    modalRef.afterClose.subscribe((result) => {\r\n      if (!result) return; // Check if result is null or undefined\r\n      this.projectMemories.push(result);\r\n      this.filteredMemories = [...this.projectMemories];\r\n      this.updatePagination();\r\n    });\r\n    // this.isUpdating = false; // Reset the flag to false when adding a new memory\r\n    // if (\r\n    //   this.projectMemory.projectCategory &&\r\n    //   this.projectMemory.projectDescription\r\n    // ) {\r\n    //   const newMemory: any = {\r\n    //     ...this.projectMemory,\r\n    //     workspace: this.workspaceName,\r\n    //   };\r\n    //   this.projectMemoryService\r\n    //     .saveProjectMemory(newMemory)\r\n    //     .subscribe((response: any) => {\r\n    //       this.projectMemories.push(response); // Add the new memory to the list\r\n    //       this.showAddProjectMemoryDialog = false;\r\n    //       this.clearInput(); // Reset form\r\n    //     });\r\n    // } else {\r\n    //   console.log('Please fill all fields');\r\n    // }\r\n  }\r\n  onUpdate(memory: any) {\r\n    this.isUpdating = true; // Set the flag to true for update mode\r\n\r\n    this.projectMemory = { ...memory }; // Populate the form with the selected memory data\r\n\r\n    let modalRef=this.modalService.create({\r\n      nzContent: AddOrEditMemoryComponent,\r\n      nzData: {\r\n        isUpdating: this.isUpdating,\r\n        project_memo: this.projectMemory,\r\n      },\r\n      nzFooter: null, // We handle the footer in the dialog component\r\n      nzWidth: '500px',\r\n    });\r\n    modalRef.afterClose.subscribe((result) => {\r\n      if (!result) return; // Check if result is null or undefined\r\n\r\n      // Update the memory in the list\r\n      const index = this.projectMemories.findIndex(\r\n        (m) => m.id === result.id\r\n      );\r\n      if (index !== -1) {\r\n        this.projectMemories[index] = result;\r\n        this.filteredMemories = [...this.projectMemories];\r\n        this.updatePagination();\r\n      }\r\n    });\r\n  }\r\n\r\n  editMemory() {\r\n    // Logic to edit the memory\r\n    let obj: any = { ...this.projectMemory };\r\n    this.projectMemoryService\r\n      .saveProjectMemory(obj)\r\n      .subscribe((response: any) => {\r\n        console.log('Project Memory updated:', response);\r\n\r\n        // Update the memory in the list\r\n        const index = this.projectMemories.findIndex(m => m.id === response.id);\r\n        if (index !== -1) {\r\n          this.projectMemories[index] = response;\r\n          this.filteredMemories = [...this.projectMemories];\r\n          this.updatePagination();\r\n        } else {\r\n          this.loadAllProjectMemories(); // Reload if not found\r\n        }\r\n\r\n        this.clearInput();\r\n        this.showAddProjectMemoryDialog = false; // Hide the dialog after saving\r\n      });\r\n  }\r\n  deleteMemory(id: number) {\r\n    this.projectMemoryService\r\n      .deleteProjectMemory(id)\r\n      .subscribe((response: any) => {\r\n        console.log('Project Memory deleted:', response);\r\n        this.projectMemories = this.projectMemories.filter(\r\n          (memory) => memory.id !== id\r\n        );\r\n        this.filteredMemories = [...this.projectMemories];\r\n        this.updatePagination();\r\n      });\r\n  }\r\n  clearInput() {\r\n    this.projectMemory = {\r\n      projectCategory: '',\r\n      projectDescription: '',\r\n      status: '',\r\n      workspace: this.workspaceName,\r\n    }; // Reset form\r\n    this.isUpdating = false; // Reset the flag to false when clearing input\r\n  }\r\n}\r\n"], "mappings": ";;;AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,SAAS,QAAQ,eAAe;AACzC,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,cAAc,EAAEC,MAAM,QAAoB,iBAAiB;AACpE,SAASC,kBAAkB,QAAQ,0BAA0B;AAC7D,SACEC,yBAAyB,EACzBC,qBAAqB,QAChB,iDAAiD;AACxD,SAASC,kBAAkB,QAAQ,sDAAsD;AACzF,SAASC,cAAc,QAAQ,qBAAqB;AAEpD,SAASC,wBAAwB,QAAQ,+DAA+D;AAUjG,IAAMC,sBAAsB,GAA5B,MAAMA,sBAAsB;EAuBjCC,YACUC,oBAA+C,EAC/CC,KAAqB,EACrBC,MAAc,EACdC,gBAAuC,EACvCC,YAA4B;IAJ5B,KAAAJ,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,YAAY,GAAZA,YAAY;IA3BtB,KAAAC,aAAa,GAAG,KAAK,CAAC,CAAC;IACvB,KAAAC,0BAA0B,GAAG,KAAK,CAAC,CAAC;IAEpC,KAAAC,aAAa,GAAG;MACdC,eAAe,EAAE,EAAE;MACnBC,kBAAkB,EAAE,EAAE;MACtBC,MAAM,EAAE,EAAE;MACVC,SAAS,EAAE;KACZ;IACD,KAAAC,eAAe,GAAU,EAAE,CAAC,CAAC;IAC7B,KAAAC,gBAAgB,GAAU,EAAE,CAAC,CAAC;IAC9B,KAAAC,iBAAiB,GAAU,EAAE,CAAC,CAAC;IAE/B,KAAAC,UAAU,GAAY,KAAK,CAAC,CAAC;IAE7B;IACA,KAAAC,WAAW,GAAW,CAAC;IACvB,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,UAAU,GAAW,CAAC;IAEtB;IACA,KAAAC,IAAI,GAAGA,IAAI;EAOR,CAAC,CAAC;EACLC,QAAQA,CAAA;IACN,IAAIlB,MAAM,GAAG,IAAI,CAACA,MAAM,CAACmB,GAAG,CAACC,KAAK,CAAC,GAAG,CAAC;IACvC,IAAI,CAACC,aAAa,GAAGrB,MAAM,CAAC,CAAC,CAAC;IAC9B,IAAI,CAACsB,sBAAsB,EAAE;EAC/B;EAEAA,sBAAsBA,CAAA;IACpB,IAAI,CAACxB,oBAAoB,CACtByB,mBAAmB,CAAC,IAAI,CAACF,aAAa,CAAC,CACvCG,SAAS,CAAEC,QAAa,IAAI;MAC3B,IAAI,CAACf,eAAe,GAAGe,QAAQ;MAC/B,IAAI,CAACd,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACD,eAAe,CAAC;MACjD,IAAI,CAACgB,gBAAgB,EAAE;MACvBC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAAClB,eAAe,CAAC;IAC/D,CAAC,CAAC;EACN;EAEA;;;EAGAgB,gBAAgBA,CAAA;IACd;IACA,IAAI,CAACV,UAAU,GAAGC,IAAI,CAACY,GAAG,CAAC,CAAC,EAAEZ,IAAI,CAACa,IAAI,CAAC,IAAI,CAACnB,gBAAgB,CAACoB,MAAM,GAAG,IAAI,CAAChB,QAAQ,CAAC,CAAC;IAEtF;IACA,IAAI,IAAI,CAACD,WAAW,GAAG,CAAC,EAAE,IAAI,CAACA,WAAW,GAAG,CAAC;IAC9C,IAAI,IAAI,CAACA,WAAW,GAAG,IAAI,CAACE,UAAU,EAAE,IAAI,CAACF,WAAW,GAAG,IAAI,CAACE,UAAU;IAE1E;IACA,IAAI,IAAI,CAACL,gBAAgB,CAACoB,MAAM,KAAK,CAAC,EAAE;MACtC,IAAI,CAACnB,iBAAiB,GAAG,EAAE;KAC5B,MAAM;MACL,MAAMoB,UAAU,GAAG,CAAC,IAAI,CAAClB,WAAW,GAAG,CAAC,IAAI,IAAI,CAACC,QAAQ;MACzD,MAAMkB,QAAQ,GAAGhB,IAAI,CAACiB,GAAG,CAACF,UAAU,GAAG,IAAI,CAACjB,QAAQ,EAAE,IAAI,CAACJ,gBAAgB,CAACoB,MAAM,CAAC;MACnF,IAAI,CAACnB,iBAAiB,GAAG,IAAI,CAACD,gBAAgB,CAACwB,KAAK,CAACH,UAAU,EAAEC,QAAQ,CAAC;;EAE9E;EAEA;;;EAGAG,QAAQA,CAACC,IAAY;IACnB,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,IAAI,CAACrB,UAAU,EAAE;MACxC,IAAI,CAACF,WAAW,GAAGuB,IAAI;MACvB,IAAI,CAACX,gBAAgB,EAAE;;EAE3B;EAEA;;;EAGAY,YAAYA,CAAA;IACV,IAAI,IAAI,CAACxB,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;MAClB,IAAI,CAACY,gBAAgB,EAAE;;EAE3B;EAEA;;;EAGAa,QAAQA,CAAA;IACN,IAAI,IAAI,CAACzB,WAAW,GAAG,IAAI,CAACE,UAAU,EAAE;MACtC,IAAI,CAACF,WAAW,EAAE;MAClB,IAAI,CAACY,gBAAgB,EAAE;;EAE3B;EAEA;;;;;EAKAc,cAAcA,CAAA;IACZ,MAAMC,KAAK,GAAwB,EAAE;IAErC,IAAI,IAAI,CAACzB,UAAU,IAAI,CAAC,EAAE;MACxB;MACA,KAAK,IAAI0B,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,IAAI,CAAC1B,UAAU,EAAE0B,CAAC,EAAE,EAAE;QACzCD,KAAK,CAACE,IAAI,CAACD,CAAC,CAAC;;KAEhB,MAAM;MACL;MACAD,KAAK,CAACE,IAAI,CAAC,CAAC,CAAC;MAEb,IAAI,IAAI,CAAC7B,WAAW,GAAG,CAAC,EAAE;QACxB;QACA2B,KAAK,CAACE,IAAI,CAAC,KAAK,CAAC;;MAGnB;MACA,MAAMC,SAAS,GAAG3B,IAAI,CAACY,GAAG,CAAC,CAAC,EAAE,IAAI,CAACf,WAAW,GAAG,CAAC,CAAC;MACnD,MAAM+B,OAAO,GAAG5B,IAAI,CAACiB,GAAG,CAAC,IAAI,CAAClB,UAAU,GAAG,CAAC,EAAE,IAAI,CAACF,WAAW,GAAG,CAAC,CAAC;MAEnE,KAAK,IAAI4B,CAAC,GAAGE,SAAS,EAAEF,CAAC,IAAIG,OAAO,EAAEH,CAAC,EAAE,EAAE;QACzC,IAAIA,CAAC,KAAK,CAAC,IAAIA,CAAC,KAAK,IAAI,CAAC1B,UAAU,EAAE;UACpCyB,KAAK,CAACE,IAAI,CAACD,CAAC,CAAC;;;MAIjB,IAAI,IAAI,CAAC5B,WAAW,GAAG,IAAI,CAACE,UAAU,GAAG,CAAC,EAAE;QAC1C;QACAyB,KAAK,CAACE,IAAI,CAAC,KAAK,CAAC;;MAGnB;MACAF,KAAK,CAACE,IAAI,CAAC,IAAI,CAAC3B,UAAU,CAAC;;IAG7B,OAAOyB,KAAK;EACd;EACAK,gBAAgBA,CAAA;IACd,IAAIC,QAAQ,GAAG,IAAI,CAAC7C,YAAY,CAAC8C,MAAM,CAAC;MACtCC,SAAS,EAAEtD,wBAAwB;MACnCuD,MAAM,EAAE;QACNrC,UAAU,EAAE,IAAI,CAACA,UAAU;QAC3BsC,YAAY,EAAE;OACf;MACDC,QAAQ,EAAE,IAAI;MACdC,OAAO,EAAE;KACV,CAAC;IACFN,QAAQ,CAACO,UAAU,CAAC9B,SAAS,CAAE+B,MAAM,IAAI;MACvC,IAAI,CAACA,MAAM,EAAE,OAAO,CAAC;MACrB,IAAI,CAAC7C,eAAe,CAACiC,IAAI,CAACY,MAAM,CAAC;MACjC,IAAI,CAAC5C,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACD,eAAe,CAAC;MACjD,IAAI,CAACgB,gBAAgB,EAAE;IACzB,CAAC,CAAC;IACF;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF;EACA8B,QAAQA,CAACC,MAAW;IAClB,IAAI,CAAC5C,UAAU,GAAG,IAAI,CAAC,CAAC;IAExB,IAAI,CAACR,aAAa,GAAG;MAAE,GAAGoD;IAAM,CAAE,CAAC,CAAC;IAEpC,IAAIV,QAAQ,GAAC,IAAI,CAAC7C,YAAY,CAAC8C,MAAM,CAAC;MACpCC,SAAS,EAAEtD,wBAAwB;MACnCuD,MAAM,EAAE;QACNrC,UAAU,EAAE,IAAI,CAACA,UAAU;QAC3BsC,YAAY,EAAE,IAAI,CAAC9C;OACpB;MACD+C,QAAQ,EAAE,IAAI;MACdC,OAAO,EAAE;KACV,CAAC;IACFN,QAAQ,CAACO,UAAU,CAAC9B,SAAS,CAAE+B,MAAM,IAAI;MACvC,IAAI,CAACA,MAAM,EAAE,OAAO,CAAC;MAErB;MACA,MAAMG,KAAK,GAAG,IAAI,CAAChD,eAAe,CAACiD,SAAS,CACzCC,CAAC,IAAKA,CAAC,CAACC,EAAE,KAAKN,MAAM,CAACM,EAAE,CAC1B;MACD,IAAIH,KAAK,KAAK,CAAC,CAAC,EAAE;QAChB,IAAI,CAAChD,eAAe,CAACgD,KAAK,CAAC,GAAGH,MAAM;QACpC,IAAI,CAAC5C,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACD,eAAe,CAAC;QACjD,IAAI,CAACgB,gBAAgB,EAAE;;IAE3B,CAAC,CAAC;EACJ;EAEAoC,UAAUA,CAAA;IACR;IACA,IAAIC,GAAG,GAAQ;MAAE,GAAG,IAAI,CAAC1D;IAAa,CAAE;IACxC,IAAI,CAACP,oBAAoB,CACtBkE,iBAAiB,CAACD,GAAG,CAAC,CACtBvC,SAAS,CAAEC,QAAa,IAAI;MAC3BE,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEH,QAAQ,CAAC;MAEhD;MACA,MAAMiC,KAAK,GAAG,IAAI,CAAChD,eAAe,CAACiD,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKpC,QAAQ,CAACoC,EAAE,CAAC;MACvE,IAAIH,KAAK,KAAK,CAAC,CAAC,EAAE;QAChB,IAAI,CAAChD,eAAe,CAACgD,KAAK,CAAC,GAAGjC,QAAQ;QACtC,IAAI,CAACd,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACD,eAAe,CAAC;QACjD,IAAI,CAACgB,gBAAgB,EAAE;OACxB,MAAM;QACL,IAAI,CAACJ,sBAAsB,EAAE,CAAC,CAAC;;MAGjC,IAAI,CAAC2C,UAAU,EAAE;MACjB,IAAI,CAAC7D,0BAA0B,GAAG,KAAK,CAAC,CAAC;IAC3C,CAAC,CAAC;EACN;EACA8D,YAAYA,CAACL,EAAU;IACrB,IAAI,CAAC/D,oBAAoB,CACtBqE,mBAAmB,CAACN,EAAE,CAAC,CACvBrC,SAAS,CAAEC,QAAa,IAAI;MAC3BE,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEH,QAAQ,CAAC;MAChD,IAAI,CAACf,eAAe,GAAG,IAAI,CAACA,eAAe,CAAC0D,MAAM,CAC/CX,MAAM,IAAKA,MAAM,CAACI,EAAE,KAAKA,EAAE,CAC7B;MACD,IAAI,CAAClD,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACD,eAAe,CAAC;MACjD,IAAI,CAACgB,gBAAgB,EAAE;IACzB,CAAC,CAAC;EACN;EACAuC,UAAUA,CAAA;IACR,IAAI,CAAC5D,aAAa,GAAG;MACnBC,eAAe,EAAE,EAAE;MACnBC,kBAAkB,EAAE,EAAE;MACtBC,MAAM,EAAE,EAAE;MACVC,SAAS,EAAE,IAAI,CAACY;KACjB,CAAC,CAAC;IACH,IAAI,CAACR,UAAU,GAAG,KAAK,CAAC,CAAC;EAC3B;;;;;;;;;;;;;;;AAxPWjB,sBAAsB,GAAAyE,UAAA,EARlCnF,SAAS,CAAC;EACToF,QAAQ,EAAE,oBAAoB;EAC9BC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACrF,WAAW,EAAEF,YAAY,EAAEK,kBAAkB,EAAEG,kBAAkB,CAAC;EAC5EgF,QAAA,EAAAC,oBAA8C;EAE9CC,SAAS,EAAE,CAACjF,cAAc,CAAC;;CAC5B,CAAC,C,EACWE,sBAAsB,CAyPlC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}