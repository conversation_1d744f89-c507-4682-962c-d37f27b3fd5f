{"ast": null, "code": "import { TextBlot } from 'parchment';\nclass Text extends TextBlot {}\n\n// https://lodash.com/docs#escape\nconst entityMap = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;',\n  \"'\": '&#39;'\n};\nfunction escapeText(text) {\n  return text.replace(/[&<>\"']/g, s => entityMap[s]);\n}\nexport { Text as default, escapeText };", "map": {"version": 3, "names": ["TextBlot", "Text", "entityMap", "escapeText", "text", "replace", "s", "default"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/quill/blots/text.js"], "sourcesContent": ["import { TextBlot } from 'parchment';\nclass Text extends TextBlot {}\n\n// https://lodash.com/docs#escape\nconst entityMap = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;',\n  \"'\": '&#39;'\n};\nfunction escapeText(text) {\n  return text.replace(/[&<>\"']/g, s => entityMap[s]);\n}\nexport { Text as default, escapeText };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,WAAW;AACpC,MAAMC,IAAI,SAASD,QAAQ,CAAC;;AAE5B;AACA,MAAME,SAAS,GAAG;EAChB,GAAG,EAAE,OAAO;EACZ,GAAG,EAAE,MAAM;EACX,GAAG,EAAE,MAAM;EACX,GAAG,EAAE,QAAQ;EACb,GAAG,EAAE;AACP,CAAC;AACD,SAASC,UAAUA,CAACC,IAAI,EAAE;EACxB,OAAOA,IAAI,CAACC,OAAO,CAAC,UAAU,EAAEC,CAAC,IAAIJ,SAAS,CAACI,CAAC,CAAC,CAAC;AACpD;AACA,SAASL,IAAI,IAAIM,OAAO,EAAEJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}