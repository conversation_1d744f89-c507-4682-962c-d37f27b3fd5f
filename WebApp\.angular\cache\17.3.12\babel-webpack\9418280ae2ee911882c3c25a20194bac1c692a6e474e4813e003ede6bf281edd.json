{"ast": null, "code": "var Scope = /* @__PURE__ */(Scope2 => (Scope2[Scope2.TYPE = 3] = \"TYPE\", Scope2[Scope2.LEVEL = 12] = \"LEVEL\", Scope2[Scope2.ATTRIBUTE = 13] = \"ATTRIBUTE\", Scope2[Scope2.BLOT = 14] = \"BLOT\", Scope2[Scope2.INLINE = 7] = \"INLINE\", Scope2[Scope2.BLOCK = 11] = \"BLOCK\", Scope2[Scope2.BLOCK_BLOT = 10] = \"BLOCK_BLOT\", Scope2[Scope2.INLINE_BLOT = 6] = \"INLINE_BLOT\", Scope2[Scope2.BLOCK_ATTRIBUTE = 9] = \"BLOCK_ATTRIBUTE\", Scope2[Scope2.INLINE_ATTRIBUTE = 5] = \"INLINE_ATTRIBUTE\", Scope2[Scope2.ANY = 15] = \"ANY\", Scope2))(Scope || {});\nclass Attributor {\n  constructor(attrName, keyName, options = {}) {\n    this.attrName = attrName, this.keyName = keyName;\n    const attributeBit = Scope.TYPE & Scope.ATTRIBUTE;\n    this.scope = options.scope != null ?\n    // Ignore type bits, force attribute bit\n    options.scope & Scope.LEVEL | attributeBit : Scope.ATTRIBUTE, options.whitelist != null && (this.whitelist = options.whitelist);\n  }\n  static keys(node) {\n    return Array.from(node.attributes).map(item => item.name);\n  }\n  add(node, value) {\n    return this.canAdd(node, value) ? (node.setAttribute(this.keyName, value), !0) : !1;\n  }\n  canAdd(_node, value) {\n    return this.whitelist == null ? !0 : typeof value == \"string\" ? this.whitelist.indexOf(value.replace(/[\"']/g, \"\")) > -1 : this.whitelist.indexOf(value) > -1;\n  }\n  remove(node) {\n    node.removeAttribute(this.keyName);\n  }\n  value(node) {\n    const value = node.getAttribute(this.keyName);\n    return this.canAdd(node, value) && value ? value : \"\";\n  }\n}\nclass ParchmentError extends Error {\n  constructor(message) {\n    message = \"[Parchment] \" + message, super(message), this.message = message, this.name = this.constructor.name;\n  }\n}\nconst _Registry = class _Registry {\n  constructor() {\n    this.attributes = {}, this.classes = {}, this.tags = {}, this.types = {};\n  }\n  static find(node, bubble = !1) {\n    if (node == null) return null;\n    if (this.blots.has(node)) return this.blots.get(node) || null;\n    if (bubble) {\n      let parentNode = null;\n      try {\n        parentNode = node.parentNode;\n      } catch {\n        return null;\n      }\n      return this.find(parentNode, bubble);\n    }\n    return null;\n  }\n  create(scroll, input, value) {\n    const match2 = this.query(input);\n    if (match2 == null) throw new ParchmentError(`Unable to create ${input} blot`);\n    const blotClass = match2,\n      node =\n      // @ts-expect-error Fix me later\n      input instanceof Node || input.nodeType === Node.TEXT_NODE ? input : blotClass.create(value),\n      blot = new blotClass(scroll, node, value);\n    return _Registry.blots.set(blot.domNode, blot), blot;\n  }\n  find(node, bubble = !1) {\n    return _Registry.find(node, bubble);\n  }\n  query(query, scope = Scope.ANY) {\n    let match2;\n    return typeof query == \"string\" ? match2 = this.types[query] || this.attributes[query] : query instanceof Text || query.nodeType === Node.TEXT_NODE ? match2 = this.types.text : typeof query == \"number\" ? query & Scope.LEVEL & Scope.BLOCK ? match2 = this.types.block : query & Scope.LEVEL & Scope.INLINE && (match2 = this.types.inline) : query instanceof Element && ((query.getAttribute(\"class\") || \"\").split(/\\s+/).some(name => (match2 = this.classes[name], !!match2)), match2 = match2 || this.tags[query.tagName]), match2 == null ? null : \"scope\" in match2 && scope & Scope.LEVEL & match2.scope && scope & Scope.TYPE & match2.scope ? match2 : null;\n  }\n  register(...definitions) {\n    return definitions.map(definition => {\n      const isBlot = (\"blotName\" in definition),\n        isAttr = (\"attrName\" in definition);\n      if (!isBlot && !isAttr) throw new ParchmentError(\"Invalid definition\");\n      if (isBlot && definition.blotName === \"abstract\") throw new ParchmentError(\"Cannot register abstract class\");\n      const key = isBlot ? definition.blotName : isAttr ? definition.attrName : void 0;\n      return this.types[key] = definition, isAttr ? typeof definition.keyName == \"string\" && (this.attributes[definition.keyName] = definition) : isBlot && (definition.className && (this.classes[definition.className] = definition), definition.tagName && (Array.isArray(definition.tagName) ? definition.tagName = definition.tagName.map(tagName => tagName.toUpperCase()) : definition.tagName = definition.tagName.toUpperCase(), (Array.isArray(definition.tagName) ? definition.tagName : [definition.tagName]).forEach(tag => {\n        (this.tags[tag] == null || definition.className == null) && (this.tags[tag] = definition);\n      }))), definition;\n    });\n  }\n};\n_Registry.blots = /* @__PURE__ */new WeakMap();\nlet Registry = _Registry;\nfunction match(node, prefix) {\n  return (node.getAttribute(\"class\") || \"\").split(/\\s+/).filter(name => name.indexOf(`${prefix}-`) === 0);\n}\nclass ClassAttributor extends Attributor {\n  static keys(node) {\n    return (node.getAttribute(\"class\") || \"\").split(/\\s+/).map(name => name.split(\"-\").slice(0, -1).join(\"-\"));\n  }\n  add(node, value) {\n    return this.canAdd(node, value) ? (this.remove(node), node.classList.add(`${this.keyName}-${value}`), !0) : !1;\n  }\n  remove(node) {\n    match(node, this.keyName).forEach(name => {\n      node.classList.remove(name);\n    }), node.classList.length === 0 && node.removeAttribute(\"class\");\n  }\n  value(node) {\n    const value = (match(node, this.keyName)[0] || \"\").slice(this.keyName.length + 1);\n    return this.canAdd(node, value) ? value : \"\";\n  }\n}\nconst ClassAttributor$1 = ClassAttributor;\nfunction camelize(name) {\n  const parts = name.split(\"-\"),\n    rest = parts.slice(1).map(part => part[0].toUpperCase() + part.slice(1)).join(\"\");\n  return parts[0] + rest;\n}\nclass StyleAttributor extends Attributor {\n  static keys(node) {\n    return (node.getAttribute(\"style\") || \"\").split(\";\").map(value => value.split(\":\")[0].trim());\n  }\n  add(node, value) {\n    return this.canAdd(node, value) ? (node.style[camelize(this.keyName)] = value, !0) : !1;\n  }\n  remove(node) {\n    node.style[camelize(this.keyName)] = \"\", node.getAttribute(\"style\") || node.removeAttribute(\"style\");\n  }\n  value(node) {\n    const value = node.style[camelize(this.keyName)];\n    return this.canAdd(node, value) ? value : \"\";\n  }\n}\nconst StyleAttributor$1 = StyleAttributor;\nclass AttributorStore {\n  constructor(domNode) {\n    this.attributes = {}, this.domNode = domNode, this.build();\n  }\n  attribute(attribute, value) {\n    value ? attribute.add(this.domNode, value) && (attribute.value(this.domNode) != null ? this.attributes[attribute.attrName] = attribute : delete this.attributes[attribute.attrName]) : (attribute.remove(this.domNode), delete this.attributes[attribute.attrName]);\n  }\n  build() {\n    this.attributes = {};\n    const blot = Registry.find(this.domNode);\n    if (blot == null) return;\n    const attributes = Attributor.keys(this.domNode),\n      classes = ClassAttributor$1.keys(this.domNode),\n      styles = StyleAttributor$1.keys(this.domNode);\n    attributes.concat(classes).concat(styles).forEach(name => {\n      const attr = blot.scroll.query(name, Scope.ATTRIBUTE);\n      attr instanceof Attributor && (this.attributes[attr.attrName] = attr);\n    });\n  }\n  copy(target) {\n    Object.keys(this.attributes).forEach(key => {\n      const value = this.attributes[key].value(this.domNode);\n      target.format(key, value);\n    });\n  }\n  move(target) {\n    this.copy(target), Object.keys(this.attributes).forEach(key => {\n      this.attributes[key].remove(this.domNode);\n    }), this.attributes = {};\n  }\n  values() {\n    return Object.keys(this.attributes).reduce((attributes, name) => (attributes[name] = this.attributes[name].value(this.domNode), attributes), {});\n  }\n}\nconst AttributorStore$1 = AttributorStore,\n  _ShadowBlot = class _ShadowBlot {\n    constructor(scroll, domNode) {\n      this.scroll = scroll, this.domNode = domNode, Registry.blots.set(domNode, this), this.prev = null, this.next = null;\n    }\n    static create(rawValue) {\n      if (this.tagName == null) throw new ParchmentError(\"Blot definition missing tagName\");\n      let node, value;\n      return Array.isArray(this.tagName) ? (typeof rawValue == \"string\" ? (value = rawValue.toUpperCase(), parseInt(value, 10).toString() === value && (value = parseInt(value, 10))) : typeof rawValue == \"number\" && (value = rawValue), typeof value == \"number\" ? node = document.createElement(this.tagName[value - 1]) : value && this.tagName.indexOf(value) > -1 ? node = document.createElement(value) : node = document.createElement(this.tagName[0])) : node = document.createElement(this.tagName), this.className && node.classList.add(this.className), node;\n    }\n    // Hack for accessing inherited static methods\n    get statics() {\n      return this.constructor;\n    }\n    attach() {}\n    clone() {\n      const domNode = this.domNode.cloneNode(!1);\n      return this.scroll.create(domNode);\n    }\n    detach() {\n      this.parent != null && this.parent.removeChild(this), Registry.blots.delete(this.domNode);\n    }\n    deleteAt(index, length) {\n      this.isolate(index, length).remove();\n    }\n    formatAt(index, length, name, value) {\n      const blot = this.isolate(index, length);\n      if (this.scroll.query(name, Scope.BLOT) != null && value) blot.wrap(name, value);else if (this.scroll.query(name, Scope.ATTRIBUTE) != null) {\n        const parent = this.scroll.create(this.statics.scope);\n        blot.wrap(parent), parent.format(name, value);\n      }\n    }\n    insertAt(index, value, def) {\n      const blot = def == null ? this.scroll.create(\"text\", value) : this.scroll.create(value, def),\n        ref = this.split(index);\n      this.parent.insertBefore(blot, ref || void 0);\n    }\n    isolate(index, length) {\n      const target = this.split(index);\n      if (target == null) throw new Error(\"Attempt to isolate at end\");\n      return target.split(length), target;\n    }\n    length() {\n      return 1;\n    }\n    offset(root = this.parent) {\n      return this.parent == null || this === root ? 0 : this.parent.children.offset(this) + this.parent.offset(root);\n    }\n    optimize(_context) {\n      this.statics.requiredContainer && !(this.parent instanceof this.statics.requiredContainer) && this.wrap(this.statics.requiredContainer.blotName);\n    }\n    remove() {\n      this.domNode.parentNode != null && this.domNode.parentNode.removeChild(this.domNode), this.detach();\n    }\n    replaceWith(name, value) {\n      const replacement = typeof name == \"string\" ? this.scroll.create(name, value) : name;\n      return this.parent != null && (this.parent.insertBefore(replacement, this.next || void 0), this.remove()), replacement;\n    }\n    split(index, _force) {\n      return index === 0 ? this : this.next;\n    }\n    update(_mutations, _context) {}\n    wrap(name, value) {\n      const wrapper = typeof name == \"string\" ? this.scroll.create(name, value) : name;\n      if (this.parent != null && this.parent.insertBefore(wrapper, this.next || void 0), typeof wrapper.appendChild != \"function\") throw new ParchmentError(`Cannot wrap ${name}`);\n      return wrapper.appendChild(this), wrapper;\n    }\n  };\n_ShadowBlot.blotName = \"abstract\";\nlet ShadowBlot = _ShadowBlot;\nconst _LeafBlot = class _LeafBlot extends ShadowBlot {\n  /**\n   * Returns the value represented by domNode if it is this Blot's type\n   * No checking that domNode can represent this Blot type is required so\n   * applications needing it should check externally before calling.\n   */\n  static value(_domNode) {\n    return !0;\n  }\n  /**\n   * Given location represented by node and offset from DOM Selection Range,\n   * return index to that location.\n   */\n  index(node, offset) {\n    return this.domNode === node || this.domNode.compareDocumentPosition(node) & Node.DOCUMENT_POSITION_CONTAINED_BY ? Math.min(offset, 1) : -1;\n  }\n  /**\n   * Given index to location within blot, return node and offset representing\n   * that location, consumable by DOM Selection Range\n   */\n  position(index, _inclusive) {\n    let offset = Array.from(this.parent.domNode.childNodes).indexOf(this.domNode);\n    return index > 0 && (offset += 1), [this.parent.domNode, offset];\n  }\n  /**\n   * Return value represented by this blot\n   * Should not change without interaction from API or\n   * user change detectable by update()\n   */\n  value() {\n    return {\n      [this.statics.blotName]: this.statics.value(this.domNode) || !0\n    };\n  }\n};\n_LeafBlot.scope = Scope.INLINE_BLOT;\nlet LeafBlot = _LeafBlot;\nconst LeafBlot$1 = LeafBlot;\nclass LinkedList {\n  constructor() {\n    this.head = null, this.tail = null, this.length = 0;\n  }\n  append(...nodes) {\n    if (this.insertBefore(nodes[0], null), nodes.length > 1) {\n      const rest = nodes.slice(1);\n      this.append(...rest);\n    }\n  }\n  at(index) {\n    const next = this.iterator();\n    let cur = next();\n    for (; cur && index > 0;) index -= 1, cur = next();\n    return cur;\n  }\n  contains(node) {\n    const next = this.iterator();\n    let cur = next();\n    for (; cur;) {\n      if (cur === node) return !0;\n      cur = next();\n    }\n    return !1;\n  }\n  indexOf(node) {\n    const next = this.iterator();\n    let cur = next(),\n      index = 0;\n    for (; cur;) {\n      if (cur === node) return index;\n      index += 1, cur = next();\n    }\n    return -1;\n  }\n  insertBefore(node, refNode) {\n    node != null && (this.remove(node), node.next = refNode, refNode != null ? (node.prev = refNode.prev, refNode.prev != null && (refNode.prev.next = node), refNode.prev = node, refNode === this.head && (this.head = node)) : this.tail != null ? (this.tail.next = node, node.prev = this.tail, this.tail = node) : (node.prev = null, this.head = this.tail = node), this.length += 1);\n  }\n  offset(target) {\n    let index = 0,\n      cur = this.head;\n    for (; cur != null;) {\n      if (cur === target) return index;\n      index += cur.length(), cur = cur.next;\n    }\n    return -1;\n  }\n  remove(node) {\n    this.contains(node) && (node.prev != null && (node.prev.next = node.next), node.next != null && (node.next.prev = node.prev), node === this.head && (this.head = node.next), node === this.tail && (this.tail = node.prev), this.length -= 1);\n  }\n  iterator(curNode = this.head) {\n    return () => {\n      const ret = curNode;\n      return curNode != null && (curNode = curNode.next), ret;\n    };\n  }\n  find(index, inclusive = !1) {\n    const next = this.iterator();\n    let cur = next();\n    for (; cur;) {\n      const length = cur.length();\n      if (index < length || inclusive && index === length && (cur.next == null || cur.next.length() !== 0)) return [cur, index];\n      index -= length, cur = next();\n    }\n    return [null, 0];\n  }\n  forEach(callback) {\n    const next = this.iterator();\n    let cur = next();\n    for (; cur;) callback(cur), cur = next();\n  }\n  forEachAt(index, length, callback) {\n    if (length <= 0) return;\n    const [startNode, offset] = this.find(index);\n    let curIndex = index - offset;\n    const next = this.iterator(startNode);\n    let cur = next();\n    for (; cur && curIndex < index + length;) {\n      const curLength = cur.length();\n      index > curIndex ? callback(cur, index - curIndex, Math.min(length, curIndex + curLength - index)) : callback(cur, 0, Math.min(curLength, index + length - curIndex)), curIndex += curLength, cur = next();\n    }\n  }\n  map(callback) {\n    return this.reduce((memo, cur) => (memo.push(callback(cur)), memo), []);\n  }\n  reduce(callback, memo) {\n    const next = this.iterator();\n    let cur = next();\n    for (; cur;) memo = callback(memo, cur), cur = next();\n    return memo;\n  }\n}\nfunction makeAttachedBlot(node, scroll) {\n  const found = scroll.find(node);\n  if (found) return found;\n  try {\n    return scroll.create(node);\n  } catch {\n    const blot = scroll.create(Scope.INLINE);\n    return Array.from(node.childNodes).forEach(child => {\n      blot.domNode.appendChild(child);\n    }), node.parentNode && node.parentNode.replaceChild(blot.domNode, node), blot.attach(), blot;\n  }\n}\nconst _ParentBlot = class _ParentBlot extends ShadowBlot {\n  constructor(scroll, domNode) {\n    super(scroll, domNode), this.uiNode = null, this.build();\n  }\n  appendChild(other) {\n    this.insertBefore(other);\n  }\n  attach() {\n    super.attach(), this.children.forEach(child => {\n      child.attach();\n    });\n  }\n  attachUI(node) {\n    this.uiNode != null && this.uiNode.remove(), this.uiNode = node, _ParentBlot.uiClass && this.uiNode.classList.add(_ParentBlot.uiClass), this.uiNode.setAttribute(\"contenteditable\", \"false\"), this.domNode.insertBefore(this.uiNode, this.domNode.firstChild);\n  }\n  /**\n   * Called during construction, should fill its own children LinkedList.\n   */\n  build() {\n    this.children = new LinkedList(), Array.from(this.domNode.childNodes).filter(node => node !== this.uiNode).reverse().forEach(node => {\n      try {\n        const child = makeAttachedBlot(node, this.scroll);\n        this.insertBefore(child, this.children.head || void 0);\n      } catch (err) {\n        if (err instanceof ParchmentError) return;\n        throw err;\n      }\n    });\n  }\n  deleteAt(index, length) {\n    if (index === 0 && length === this.length()) return this.remove();\n    this.children.forEachAt(index, length, (child, offset, childLength) => {\n      child.deleteAt(offset, childLength);\n    });\n  }\n  descendant(criteria, index = 0) {\n    const [child, offset] = this.children.find(index);\n    return criteria.blotName == null && criteria(child) || criteria.blotName != null && child instanceof criteria ? [child, offset] : child instanceof _ParentBlot ? child.descendant(criteria, offset) : [null, -1];\n  }\n  descendants(criteria, index = 0, length = Number.MAX_VALUE) {\n    let descendants = [],\n      lengthLeft = length;\n    return this.children.forEachAt(index, length, (child, childIndex, childLength) => {\n      (criteria.blotName == null && criteria(child) || criteria.blotName != null && child instanceof criteria) && descendants.push(child), child instanceof _ParentBlot && (descendants = descendants.concat(child.descendants(criteria, childIndex, lengthLeft))), lengthLeft -= childLength;\n    }), descendants;\n  }\n  detach() {\n    this.children.forEach(child => {\n      child.detach();\n    }), super.detach();\n  }\n  enforceAllowedChildren() {\n    let done = !1;\n    this.children.forEach(child => {\n      done || this.statics.allowedChildren.some(def => child instanceof def) || (child.statics.scope === Scope.BLOCK_BLOT ? (child.next != null && this.splitAfter(child), child.prev != null && this.splitAfter(child.prev), child.parent.unwrap(), done = !0) : child instanceof _ParentBlot ? child.unwrap() : child.remove());\n    });\n  }\n  formatAt(index, length, name, value) {\n    this.children.forEachAt(index, length, (child, offset, childLength) => {\n      child.formatAt(offset, childLength, name, value);\n    });\n  }\n  insertAt(index, value, def) {\n    const [child, offset] = this.children.find(index);\n    if (child) child.insertAt(offset, value, def);else {\n      const blot = def == null ? this.scroll.create(\"text\", value) : this.scroll.create(value, def);\n      this.appendChild(blot);\n    }\n  }\n  insertBefore(childBlot, refBlot) {\n    childBlot.parent != null && childBlot.parent.children.remove(childBlot);\n    let refDomNode = null;\n    this.children.insertBefore(childBlot, refBlot || null), childBlot.parent = this, refBlot != null && (refDomNode = refBlot.domNode), (this.domNode.parentNode !== childBlot.domNode || this.domNode.nextSibling !== refDomNode) && this.domNode.insertBefore(childBlot.domNode, refDomNode), childBlot.attach();\n  }\n  length() {\n    return this.children.reduce((memo, child) => memo + child.length(), 0);\n  }\n  moveChildren(targetParent, refNode) {\n    this.children.forEach(child => {\n      targetParent.insertBefore(child, refNode);\n    });\n  }\n  optimize(context) {\n    if (super.optimize(context), this.enforceAllowedChildren(), this.uiNode != null && this.uiNode !== this.domNode.firstChild && this.domNode.insertBefore(this.uiNode, this.domNode.firstChild), this.children.length === 0) if (this.statics.defaultChild != null) {\n      const child = this.scroll.create(this.statics.defaultChild.blotName);\n      this.appendChild(child);\n    } else this.remove();\n  }\n  path(index, inclusive = !1) {\n    const [child, offset] = this.children.find(index, inclusive),\n      position = [[this, index]];\n    return child instanceof _ParentBlot ? position.concat(child.path(offset, inclusive)) : (child != null && position.push([child, offset]), position);\n  }\n  removeChild(child) {\n    this.children.remove(child);\n  }\n  replaceWith(name, value) {\n    const replacement = typeof name == \"string\" ? this.scroll.create(name, value) : name;\n    return replacement instanceof _ParentBlot && this.moveChildren(replacement), super.replaceWith(replacement);\n  }\n  split(index, force = !1) {\n    if (!force) {\n      if (index === 0) return this;\n      if (index === this.length()) return this.next;\n    }\n    const after = this.clone();\n    return this.parent && this.parent.insertBefore(after, this.next || void 0), this.children.forEachAt(index, this.length(), (child, offset, _length) => {\n      const split = child.split(offset, force);\n      split != null && after.appendChild(split);\n    }), after;\n  }\n  splitAfter(child) {\n    const after = this.clone();\n    for (; child.next != null;) after.appendChild(child.next);\n    return this.parent && this.parent.insertBefore(after, this.next || void 0), after;\n  }\n  unwrap() {\n    this.parent && this.moveChildren(this.parent, this.next || void 0), this.remove();\n  }\n  update(mutations, _context) {\n    const addedNodes = [],\n      removedNodes = [];\n    mutations.forEach(mutation => {\n      mutation.target === this.domNode && mutation.type === \"childList\" && (addedNodes.push(...mutation.addedNodes), removedNodes.push(...mutation.removedNodes));\n    }), removedNodes.forEach(node => {\n      if (node.parentNode != null &&\n      // @ts-expect-error Fix me later\n      node.tagName !== \"IFRAME\" && document.body.compareDocumentPosition(node) & Node.DOCUMENT_POSITION_CONTAINED_BY) return;\n      const blot = this.scroll.find(node);\n      blot != null && (blot.domNode.parentNode == null || blot.domNode.parentNode === this.domNode) && blot.detach();\n    }), addedNodes.filter(node => node.parentNode === this.domNode && node !== this.uiNode).sort((a, b) => a === b ? 0 : a.compareDocumentPosition(b) & Node.DOCUMENT_POSITION_FOLLOWING ? 1 : -1).forEach(node => {\n      let refBlot = null;\n      node.nextSibling != null && (refBlot = this.scroll.find(node.nextSibling));\n      const blot = makeAttachedBlot(node, this.scroll);\n      (blot.next !== refBlot || blot.next == null) && (blot.parent != null && blot.parent.removeChild(this), this.insertBefore(blot, refBlot || void 0));\n    }), this.enforceAllowedChildren();\n  }\n};\n_ParentBlot.uiClass = \"\";\nlet ParentBlot = _ParentBlot;\nconst ParentBlot$1 = ParentBlot;\nfunction isEqual(obj1, obj2) {\n  if (Object.keys(obj1).length !== Object.keys(obj2).length) return !1;\n  for (const prop in obj1) if (obj1[prop] !== obj2[prop]) return !1;\n  return !0;\n}\nconst _InlineBlot = class _InlineBlot extends ParentBlot$1 {\n  static create(value) {\n    return super.create(value);\n  }\n  static formats(domNode, scroll) {\n    const match2 = scroll.query(_InlineBlot.blotName);\n    if (!(match2 != null && domNode.tagName === match2.tagName)) {\n      if (typeof this.tagName == \"string\") return !0;\n      if (Array.isArray(this.tagName)) return domNode.tagName.toLowerCase();\n    }\n  }\n  constructor(scroll, domNode) {\n    super(scroll, domNode), this.attributes = new AttributorStore$1(this.domNode);\n  }\n  format(name, value) {\n    if (name === this.statics.blotName && !value) this.children.forEach(child => {\n      child instanceof _InlineBlot || (child = child.wrap(_InlineBlot.blotName, !0)), this.attributes.copy(child);\n    }), this.unwrap();else {\n      const format = this.scroll.query(name, Scope.INLINE);\n      if (format == null) return;\n      format instanceof Attributor ? this.attributes.attribute(format, value) : value && (name !== this.statics.blotName || this.formats()[name] !== value) && this.replaceWith(name, value);\n    }\n  }\n  formats() {\n    const formats = this.attributes.values(),\n      format = this.statics.formats(this.domNode, this.scroll);\n    return format != null && (formats[this.statics.blotName] = format), formats;\n  }\n  formatAt(index, length, name, value) {\n    this.formats()[name] != null || this.scroll.query(name, Scope.ATTRIBUTE) ? this.isolate(index, length).format(name, value) : super.formatAt(index, length, name, value);\n  }\n  optimize(context) {\n    super.optimize(context);\n    const formats = this.formats();\n    if (Object.keys(formats).length === 0) return this.unwrap();\n    const next = this.next;\n    next instanceof _InlineBlot && next.prev === this && isEqual(formats, next.formats()) && (next.moveChildren(this), next.remove());\n  }\n  replaceWith(name, value) {\n    const replacement = super.replaceWith(name, value);\n    return this.attributes.copy(replacement), replacement;\n  }\n  update(mutations, context) {\n    super.update(mutations, context), mutations.some(mutation => mutation.target === this.domNode && mutation.type === \"attributes\") && this.attributes.build();\n  }\n  wrap(name, value) {\n    const wrapper = super.wrap(name, value);\n    return wrapper instanceof _InlineBlot && this.attributes.move(wrapper), wrapper;\n  }\n};\n_InlineBlot.allowedChildren = [_InlineBlot, LeafBlot$1], _InlineBlot.blotName = \"inline\", _InlineBlot.scope = Scope.INLINE_BLOT, _InlineBlot.tagName = \"SPAN\";\nlet InlineBlot = _InlineBlot;\nconst InlineBlot$1 = InlineBlot,\n  _BlockBlot = class _BlockBlot extends ParentBlot$1 {\n    static create(value) {\n      return super.create(value);\n    }\n    static formats(domNode, scroll) {\n      const match2 = scroll.query(_BlockBlot.blotName);\n      if (!(match2 != null && domNode.tagName === match2.tagName)) {\n        if (typeof this.tagName == \"string\") return !0;\n        if (Array.isArray(this.tagName)) return domNode.tagName.toLowerCase();\n      }\n    }\n    constructor(scroll, domNode) {\n      super(scroll, domNode), this.attributes = new AttributorStore$1(this.domNode);\n    }\n    format(name, value) {\n      const format = this.scroll.query(name, Scope.BLOCK);\n      format != null && (format instanceof Attributor ? this.attributes.attribute(format, value) : name === this.statics.blotName && !value ? this.replaceWith(_BlockBlot.blotName) : value && (name !== this.statics.blotName || this.formats()[name] !== value) && this.replaceWith(name, value));\n    }\n    formats() {\n      const formats = this.attributes.values(),\n        format = this.statics.formats(this.domNode, this.scroll);\n      return format != null && (formats[this.statics.blotName] = format), formats;\n    }\n    formatAt(index, length, name, value) {\n      this.scroll.query(name, Scope.BLOCK) != null ? this.format(name, value) : super.formatAt(index, length, name, value);\n    }\n    insertAt(index, value, def) {\n      if (def == null || this.scroll.query(value, Scope.INLINE) != null) super.insertAt(index, value, def);else {\n        const after = this.split(index);\n        if (after != null) {\n          const blot = this.scroll.create(value, def);\n          after.parent.insertBefore(blot, after);\n        } else throw new Error(\"Attempt to insertAt after block boundaries\");\n      }\n    }\n    replaceWith(name, value) {\n      const replacement = super.replaceWith(name, value);\n      return this.attributes.copy(replacement), replacement;\n    }\n    update(mutations, context) {\n      super.update(mutations, context), mutations.some(mutation => mutation.target === this.domNode && mutation.type === \"attributes\") && this.attributes.build();\n    }\n  };\n_BlockBlot.blotName = \"block\", _BlockBlot.scope = Scope.BLOCK_BLOT, _BlockBlot.tagName = \"P\", _BlockBlot.allowedChildren = [InlineBlot$1, _BlockBlot, LeafBlot$1];\nlet BlockBlot = _BlockBlot;\nconst BlockBlot$1 = BlockBlot,\n  _ContainerBlot = class _ContainerBlot extends ParentBlot$1 {\n    checkMerge() {\n      return this.next !== null && this.next.statics.blotName === this.statics.blotName;\n    }\n    deleteAt(index, length) {\n      super.deleteAt(index, length), this.enforceAllowedChildren();\n    }\n    formatAt(index, length, name, value) {\n      super.formatAt(index, length, name, value), this.enforceAllowedChildren();\n    }\n    insertAt(index, value, def) {\n      super.insertAt(index, value, def), this.enforceAllowedChildren();\n    }\n    optimize(context) {\n      super.optimize(context), this.children.length > 0 && this.next != null && this.checkMerge() && (this.next.moveChildren(this), this.next.remove());\n    }\n  };\n_ContainerBlot.blotName = \"container\", _ContainerBlot.scope = Scope.BLOCK_BLOT;\nlet ContainerBlot = _ContainerBlot;\nconst ContainerBlot$1 = ContainerBlot;\nclass EmbedBlot extends LeafBlot$1 {\n  static formats(_domNode, _scroll) {}\n  format(name, value) {\n    super.formatAt(0, this.length(), name, value);\n  }\n  formatAt(index, length, name, value) {\n    index === 0 && length === this.length() ? this.format(name, value) : super.formatAt(index, length, name, value);\n  }\n  formats() {\n    return this.statics.formats(this.domNode, this.scroll);\n  }\n}\nconst EmbedBlot$1 = EmbedBlot,\n  OBSERVER_CONFIG = {\n    attributes: !0,\n    characterData: !0,\n    characterDataOldValue: !0,\n    childList: !0,\n    subtree: !0\n  },\n  MAX_OPTIMIZE_ITERATIONS = 100,\n  _ScrollBlot = class _ScrollBlot extends ParentBlot$1 {\n    constructor(registry, node) {\n      super(null, node), this.registry = registry, this.scroll = this, this.build(), this.observer = new MutationObserver(mutations => {\n        this.update(mutations);\n      }), this.observer.observe(this.domNode, OBSERVER_CONFIG), this.attach();\n    }\n    create(input, value) {\n      return this.registry.create(this, input, value);\n    }\n    find(node, bubble = !1) {\n      const blot = this.registry.find(node, bubble);\n      return blot ? blot.scroll === this ? blot : bubble ? this.find(blot.scroll.domNode.parentNode, !0) : null : null;\n    }\n    query(query, scope = Scope.ANY) {\n      return this.registry.query(query, scope);\n    }\n    register(...definitions) {\n      return this.registry.register(...definitions);\n    }\n    build() {\n      this.scroll != null && super.build();\n    }\n    detach() {\n      super.detach(), this.observer.disconnect();\n    }\n    deleteAt(index, length) {\n      this.update(), index === 0 && length === this.length() ? this.children.forEach(child => {\n        child.remove();\n      }) : super.deleteAt(index, length);\n    }\n    formatAt(index, length, name, value) {\n      this.update(), super.formatAt(index, length, name, value);\n    }\n    insertAt(index, value, def) {\n      this.update(), super.insertAt(index, value, def);\n    }\n    optimize(mutations = [], context = {}) {\n      super.optimize(context);\n      const mutationsMap = context.mutationsMap || /* @__PURE__ */new WeakMap();\n      let records = Array.from(this.observer.takeRecords());\n      for (; records.length > 0;) mutations.push(records.pop());\n      const mark = (blot, markParent = !0) => {\n          blot == null || blot === this || blot.domNode.parentNode != null && (mutationsMap.has(blot.domNode) || mutationsMap.set(blot.domNode, []), markParent && mark(blot.parent));\n        },\n        optimize = blot => {\n          mutationsMap.has(blot.domNode) && (blot instanceof ParentBlot$1 && blot.children.forEach(optimize), mutationsMap.delete(blot.domNode), blot.optimize(context));\n        };\n      let remaining = mutations;\n      for (let i = 0; remaining.length > 0; i += 1) {\n        if (i >= MAX_OPTIMIZE_ITERATIONS) throw new Error(\"[Parchment] Maximum optimize iterations reached\");\n        for (remaining.forEach(mutation => {\n          const blot = this.find(mutation.target, !0);\n          blot != null && (blot.domNode === mutation.target && (mutation.type === \"childList\" ? (mark(this.find(mutation.previousSibling, !1)), Array.from(mutation.addedNodes).forEach(node => {\n            const child = this.find(node, !1);\n            mark(child, !1), child instanceof ParentBlot$1 && child.children.forEach(grandChild => {\n              mark(grandChild, !1);\n            });\n          })) : mutation.type === \"attributes\" && mark(blot.prev)), mark(blot));\n        }), this.children.forEach(optimize), remaining = Array.from(this.observer.takeRecords()), records = remaining.slice(); records.length > 0;) mutations.push(records.pop());\n      }\n    }\n    update(mutations, context = {}) {\n      mutations = mutations || this.observer.takeRecords();\n      const mutationsMap = /* @__PURE__ */new WeakMap();\n      mutations.map(mutation => {\n        const blot = this.find(mutation.target, !0);\n        return blot == null ? null : mutationsMap.has(blot.domNode) ? (mutationsMap.get(blot.domNode).push(mutation), null) : (mutationsMap.set(blot.domNode, [mutation]), blot);\n      }).forEach(blot => {\n        blot != null && blot !== this && mutationsMap.has(blot.domNode) && blot.update(mutationsMap.get(blot.domNode) || [], context);\n      }), context.mutationsMap = mutationsMap, mutationsMap.has(this.domNode) && super.update(mutationsMap.get(this.domNode), context), this.optimize(mutations, context);\n    }\n  };\n_ScrollBlot.blotName = \"scroll\", _ScrollBlot.defaultChild = BlockBlot$1, _ScrollBlot.allowedChildren = [BlockBlot$1, ContainerBlot$1], _ScrollBlot.scope = Scope.BLOCK_BLOT, _ScrollBlot.tagName = \"DIV\";\nlet ScrollBlot = _ScrollBlot;\nconst ScrollBlot$1 = ScrollBlot,\n  _TextBlot = class _TextBlot extends LeafBlot$1 {\n    static create(value) {\n      return document.createTextNode(value);\n    }\n    static value(domNode) {\n      return domNode.data;\n    }\n    constructor(scroll, node) {\n      super(scroll, node), this.text = this.statics.value(this.domNode);\n    }\n    deleteAt(index, length) {\n      this.domNode.data = this.text = this.text.slice(0, index) + this.text.slice(index + length);\n    }\n    index(node, offset) {\n      return this.domNode === node ? offset : -1;\n    }\n    insertAt(index, value, def) {\n      def == null ? (this.text = this.text.slice(0, index) + value + this.text.slice(index), this.domNode.data = this.text) : super.insertAt(index, value, def);\n    }\n    length() {\n      return this.text.length;\n    }\n    optimize(context) {\n      super.optimize(context), this.text = this.statics.value(this.domNode), this.text.length === 0 ? this.remove() : this.next instanceof _TextBlot && this.next.prev === this && (this.insertAt(this.length(), this.next.value()), this.next.remove());\n    }\n    position(index, _inclusive = !1) {\n      return [this.domNode, index];\n    }\n    split(index, force = !1) {\n      if (!force) {\n        if (index === 0) return this;\n        if (index === this.length()) return this.next;\n      }\n      const after = this.scroll.create(this.domNode.splitText(index));\n      return this.parent.insertBefore(after, this.next || void 0), this.text = this.statics.value(this.domNode), after;\n    }\n    update(mutations, _context) {\n      mutations.some(mutation => mutation.type === \"characterData\" && mutation.target === this.domNode) && (this.text = this.statics.value(this.domNode));\n    }\n    value() {\n      return this.text;\n    }\n  };\n_TextBlot.blotName = \"text\", _TextBlot.scope = Scope.INLINE_BLOT;\nlet TextBlot = _TextBlot;\nconst TextBlot$1 = TextBlot;\nexport { Attributor, AttributorStore$1 as AttributorStore, BlockBlot$1 as BlockBlot, ClassAttributor$1 as ClassAttributor, ContainerBlot$1 as ContainerBlot, EmbedBlot$1 as EmbedBlot, InlineBlot$1 as InlineBlot, LeafBlot$1 as LeafBlot, ParentBlot$1 as ParentBlot, Registry, Scope, ScrollBlot$1 as ScrollBlot, StyleAttributor$1 as StyleAttributor, TextBlot$1 as TextBlot };", "map": {"version": 3, "names": ["<PERSON><PERSON>", "Scope2", "TYPE", "LEVEL", "ATTRIBUTE", "BLOT", "INLINE", "BLOCK", "BLOCK_BLOT", "INLINE_BLOT", "BLOCK_ATTRIBUTE", "INLINE_ATTRIBUTE", "ANY", "Attributor", "constructor", "attrName", "keyName", "options", "attributeBit", "scope", "whitelist", "keys", "node", "Array", "from", "attributes", "map", "item", "name", "add", "value", "canAdd", "setAttribute", "_node", "indexOf", "replace", "remove", "removeAttribute", "getAttribute", "ParchmentError", "Error", "message", "_Registry", "classes", "tags", "types", "find", "bubble", "blots", "has", "get", "parentNode", "create", "scroll", "input", "match2", "query", "blotClass", "Node", "nodeType", "TEXT_NODE", "blot", "set", "domNode", "Text", "text", "block", "inline", "Element", "split", "some", "tagName", "register", "definitions", "definition", "isBlot", "isAttr", "blotName", "key", "className", "isArray", "toUpperCase", "for<PERSON>ach", "tag", "WeakMap", "Registry", "match", "prefix", "filter", "ClassAttributor", "slice", "join", "classList", "length", "ClassAttributor$1", "camelize", "parts", "rest", "part", "StyleAttributor", "trim", "style", "StyleAttributor$1", "AttributorStore", "build", "attribute", "styles", "concat", "attr", "copy", "target", "Object", "format", "move", "values", "reduce", "AttributorStore$1", "_ShadowBlot", "prev", "next", "rawValue", "parseInt", "toString", "document", "createElement", "statics", "attach", "clone", "cloneNode", "detach", "parent", "<PERSON><PERSON><PERSON><PERSON>", "delete", "deleteAt", "index", "isolate", "formatAt", "wrap", "insertAt", "def", "ref", "insertBefore", "offset", "root", "children", "optimize", "_context", "requiredC<PERSON><PERSON>", "replaceWith", "replacement", "_force", "update", "_mutations", "wrapper", "append<PERSON><PERSON><PERSON>", "ShadowBlot", "_LeafBlot", "_domNode", "compareDocumentPosition", "DOCUMENT_POSITION_CONTAINED_BY", "Math", "min", "position", "_inclusive", "childNodes", "LeafBlot", "LeafBlot$1", "LinkedList", "head", "tail", "append", "nodes", "at", "iterator", "cur", "contains", "refNode", "curNode", "ret", "inclusive", "callback", "forEachAt", "startNode", "curIndex", "curL<PERSON>th", "memo", "push", "makeAttachedBlot", "found", "child", "<PERSON><PERSON><PERSON><PERSON>", "_ParentBlot", "uiNode", "other", "attachUI", "uiClass", "<PERSON><PERSON><PERSON><PERSON>", "reverse", "err", "<PERSON><PERSON><PERSON><PERSON>", "descendant", "criteria", "descendants", "Number", "MAX_VALUE", "lengthLeft", "childIndex", "enforceAllowed<PERSON><PERSON><PERSON><PERSON>", "done", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "splitAfter", "unwrap", "childBlot", "refBlot", "refDomNode", "nextS<PERSON>ling", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "targetParent", "context", "defaultChild", "path", "force", "after", "_length", "mutations", "addedNodes", "removedNodes", "mutation", "type", "body", "sort", "a", "b", "DOCUMENT_POSITION_FOLLOWING", "ParentBlot", "ParentBlot$1", "isEqual", "obj1", "obj2", "prop", "_InlineBlot", "formats", "toLowerCase", "InlineBlot", "InlineBlot$1", "_BlockBlot", "BlockBlot", "BlockBlot$1", "_ContainerBlot", "checkMerge", "ContainerBlot", "ContainerBlot$1", "EmbedBlot", "_scroll", "EmbedBlot$1", "OBSERVER_CONFIG", "characterData", "characterDataOldValue", "childList", "subtree", "MAX_OPTIMIZE_ITERATIONS", "_ScrollBlot", "registry", "observer", "MutationObserver", "observe", "disconnect", "mutationsMap", "records", "takeRecords", "pop", "mark", "mark<PERSON>arent", "remaining", "i", "previousSibling", "<PERSON><PERSON><PERSON><PERSON>", "ScrollBlot", "ScrollBlot$1", "_TextBlot", "createTextNode", "data", "splitText", "TextBlot", "TextBlot$1"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/parchment/dist/parchment.js"], "sourcesContent": ["var Scope = /* @__PURE__ */ ((Scope2) => (Scope2[Scope2.TYPE = 3] = \"TYPE\", Scope2[Scope2.LEVEL = 12] = \"LEVEL\", Scope2[Scope2.ATTRIBUTE = 13] = \"ATTRIBUTE\", Scope2[Scope2.BLOT = 14] = \"BLOT\", Scope2[Scope2.INLINE = 7] = \"INLINE\", Scope2[Scope2.BLOCK = 11] = \"BLOCK\", Scope2[Scope2.BLOCK_BLOT = 10] = \"BLOCK_BLOT\", Scope2[Scope2.INLINE_BLOT = 6] = \"INLINE_BLOT\", Scope2[Scope2.BLOCK_ATTRIBUTE = 9] = \"BLOCK_ATTRIBUTE\", Scope2[Scope2.INLINE_ATTRIBUTE = 5] = \"INLINE_ATTRIBUTE\", Scope2[Scope2.ANY = 15] = \"ANY\", Scope2))(Scope || {});\nclass Attributor {\n  constructor(attrName, keyName, options = {}) {\n    this.attrName = attrName, this.keyName = keyName;\n    const attributeBit = Scope.TYPE & Scope.ATTRIBUTE;\n    this.scope = options.scope != null ? (\n      // Ignore type bits, force attribute bit\n      options.scope & Scope.LEVEL | attributeBit\n    ) : Scope.ATTRIBUTE, options.whitelist != null && (this.whitelist = options.whitelist);\n  }\n  static keys(node) {\n    return Array.from(node.attributes).map((item) => item.name);\n  }\n  add(node, value) {\n    return this.canAdd(node, value) ? (node.setAttribute(this.keyName, value), !0) : !1;\n  }\n  canAdd(_node, value) {\n    return this.whitelist == null ? !0 : typeof value == \"string\" ? this.whitelist.indexOf(value.replace(/[\"']/g, \"\")) > -1 : this.whitelist.indexOf(value) > -1;\n  }\n  remove(node) {\n    node.removeAttribute(this.keyName);\n  }\n  value(node) {\n    const value = node.getAttribute(this.keyName);\n    return this.canAdd(node, value) && value ? value : \"\";\n  }\n}\nclass ParchmentError extends Error {\n  constructor(message) {\n    message = \"[Parchment] \" + message, super(message), this.message = message, this.name = this.constructor.name;\n  }\n}\nconst _Registry = class _Registry {\n  constructor() {\n    this.attributes = {}, this.classes = {}, this.tags = {}, this.types = {};\n  }\n  static find(node, bubble = !1) {\n    if (node == null)\n      return null;\n    if (this.blots.has(node))\n      return this.blots.get(node) || null;\n    if (bubble) {\n      let parentNode = null;\n      try {\n        parentNode = node.parentNode;\n      } catch {\n        return null;\n      }\n      return this.find(parentNode, bubble);\n    }\n    return null;\n  }\n  create(scroll, input, value) {\n    const match2 = this.query(input);\n    if (match2 == null)\n      throw new ParchmentError(`Unable to create ${input} blot`);\n    const blotClass = match2, node = (\n      // @ts-expect-error Fix me later\n      input instanceof Node || input.nodeType === Node.TEXT_NODE ? input : blotClass.create(value)\n    ), blot = new blotClass(scroll, node, value);\n    return _Registry.blots.set(blot.domNode, blot), blot;\n  }\n  find(node, bubble = !1) {\n    return _Registry.find(node, bubble);\n  }\n  query(query, scope = Scope.ANY) {\n    let match2;\n    return typeof query == \"string\" ? match2 = this.types[query] || this.attributes[query] : query instanceof Text || query.nodeType === Node.TEXT_NODE ? match2 = this.types.text : typeof query == \"number\" ? query & Scope.LEVEL & Scope.BLOCK ? match2 = this.types.block : query & Scope.LEVEL & Scope.INLINE && (match2 = this.types.inline) : query instanceof Element && ((query.getAttribute(\"class\") || \"\").split(/\\s+/).some((name) => (match2 = this.classes[name], !!match2)), match2 = match2 || this.tags[query.tagName]), match2 == null ? null : \"scope\" in match2 && scope & Scope.LEVEL & match2.scope && scope & Scope.TYPE & match2.scope ? match2 : null;\n  }\n  register(...definitions) {\n    return definitions.map((definition) => {\n      const isBlot = \"blotName\" in definition, isAttr = \"attrName\" in definition;\n      if (!isBlot && !isAttr)\n        throw new ParchmentError(\"Invalid definition\");\n      if (isBlot && definition.blotName === \"abstract\")\n        throw new ParchmentError(\"Cannot register abstract class\");\n      const key = isBlot ? definition.blotName : isAttr ? definition.attrName : void 0;\n      return this.types[key] = definition, isAttr ? typeof definition.keyName == \"string\" && (this.attributes[definition.keyName] = definition) : isBlot && (definition.className && (this.classes[definition.className] = definition), definition.tagName && (Array.isArray(definition.tagName) ? definition.tagName = definition.tagName.map((tagName) => tagName.toUpperCase()) : definition.tagName = definition.tagName.toUpperCase(), (Array.isArray(definition.tagName) ? definition.tagName : [definition.tagName]).forEach((tag) => {\n        (this.tags[tag] == null || definition.className == null) && (this.tags[tag] = definition);\n      }))), definition;\n    });\n  }\n};\n_Registry.blots = /* @__PURE__ */ new WeakMap();\nlet Registry = _Registry;\nfunction match(node, prefix) {\n  return (node.getAttribute(\"class\") || \"\").split(/\\s+/).filter((name) => name.indexOf(`${prefix}-`) === 0);\n}\nclass ClassAttributor extends Attributor {\n  static keys(node) {\n    return (node.getAttribute(\"class\") || \"\").split(/\\s+/).map((name) => name.split(\"-\").slice(0, -1).join(\"-\"));\n  }\n  add(node, value) {\n    return this.canAdd(node, value) ? (this.remove(node), node.classList.add(`${this.keyName}-${value}`), !0) : !1;\n  }\n  remove(node) {\n    match(node, this.keyName).forEach((name) => {\n      node.classList.remove(name);\n    }), node.classList.length === 0 && node.removeAttribute(\"class\");\n  }\n  value(node) {\n    const value = (match(node, this.keyName)[0] || \"\").slice(this.keyName.length + 1);\n    return this.canAdd(node, value) ? value : \"\";\n  }\n}\nconst ClassAttributor$1 = ClassAttributor;\nfunction camelize(name) {\n  const parts = name.split(\"-\"), rest = parts.slice(1).map((part) => part[0].toUpperCase() + part.slice(1)).join(\"\");\n  return parts[0] + rest;\n}\nclass StyleAttributor extends Attributor {\n  static keys(node) {\n    return (node.getAttribute(\"style\") || \"\").split(\";\").map((value) => value.split(\":\")[0].trim());\n  }\n  add(node, value) {\n    return this.canAdd(node, value) ? (node.style[camelize(this.keyName)] = value, !0) : !1;\n  }\n  remove(node) {\n    node.style[camelize(this.keyName)] = \"\", node.getAttribute(\"style\") || node.removeAttribute(\"style\");\n  }\n  value(node) {\n    const value = node.style[camelize(this.keyName)];\n    return this.canAdd(node, value) ? value : \"\";\n  }\n}\nconst StyleAttributor$1 = StyleAttributor;\nclass AttributorStore {\n  constructor(domNode) {\n    this.attributes = {}, this.domNode = domNode, this.build();\n  }\n  attribute(attribute, value) {\n    value ? attribute.add(this.domNode, value) && (attribute.value(this.domNode) != null ? this.attributes[attribute.attrName] = attribute : delete this.attributes[attribute.attrName]) : (attribute.remove(this.domNode), delete this.attributes[attribute.attrName]);\n  }\n  build() {\n    this.attributes = {};\n    const blot = Registry.find(this.domNode);\n    if (blot == null)\n      return;\n    const attributes = Attributor.keys(this.domNode), classes = ClassAttributor$1.keys(this.domNode), styles = StyleAttributor$1.keys(this.domNode);\n    attributes.concat(classes).concat(styles).forEach((name) => {\n      const attr = blot.scroll.query(name, Scope.ATTRIBUTE);\n      attr instanceof Attributor && (this.attributes[attr.attrName] = attr);\n    });\n  }\n  copy(target) {\n    Object.keys(this.attributes).forEach((key) => {\n      const value = this.attributes[key].value(this.domNode);\n      target.format(key, value);\n    });\n  }\n  move(target) {\n    this.copy(target), Object.keys(this.attributes).forEach((key) => {\n      this.attributes[key].remove(this.domNode);\n    }), this.attributes = {};\n  }\n  values() {\n    return Object.keys(this.attributes).reduce(\n      (attributes, name) => (attributes[name] = this.attributes[name].value(this.domNode), attributes),\n      {}\n    );\n  }\n}\nconst AttributorStore$1 = AttributorStore, _ShadowBlot = class _ShadowBlot {\n  constructor(scroll, domNode) {\n    this.scroll = scroll, this.domNode = domNode, Registry.blots.set(domNode, this), this.prev = null, this.next = null;\n  }\n  static create(rawValue) {\n    if (this.tagName == null)\n      throw new ParchmentError(\"Blot definition missing tagName\");\n    let node, value;\n    return Array.isArray(this.tagName) ? (typeof rawValue == \"string\" ? (value = rawValue.toUpperCase(), parseInt(value, 10).toString() === value && (value = parseInt(value, 10))) : typeof rawValue == \"number\" && (value = rawValue), typeof value == \"number\" ? node = document.createElement(this.tagName[value - 1]) : value && this.tagName.indexOf(value) > -1 ? node = document.createElement(value) : node = document.createElement(this.tagName[0])) : node = document.createElement(this.tagName), this.className && node.classList.add(this.className), node;\n  }\n  // Hack for accessing inherited static methods\n  get statics() {\n    return this.constructor;\n  }\n  attach() {\n  }\n  clone() {\n    const domNode = this.domNode.cloneNode(!1);\n    return this.scroll.create(domNode);\n  }\n  detach() {\n    this.parent != null && this.parent.removeChild(this), Registry.blots.delete(this.domNode);\n  }\n  deleteAt(index, length) {\n    this.isolate(index, length).remove();\n  }\n  formatAt(index, length, name, value) {\n    const blot = this.isolate(index, length);\n    if (this.scroll.query(name, Scope.BLOT) != null && value)\n      blot.wrap(name, value);\n    else if (this.scroll.query(name, Scope.ATTRIBUTE) != null) {\n      const parent = this.scroll.create(this.statics.scope);\n      blot.wrap(parent), parent.format(name, value);\n    }\n  }\n  insertAt(index, value, def) {\n    const blot = def == null ? this.scroll.create(\"text\", value) : this.scroll.create(value, def), ref = this.split(index);\n    this.parent.insertBefore(blot, ref || void 0);\n  }\n  isolate(index, length) {\n    const target = this.split(index);\n    if (target == null)\n      throw new Error(\"Attempt to isolate at end\");\n    return target.split(length), target;\n  }\n  length() {\n    return 1;\n  }\n  offset(root = this.parent) {\n    return this.parent == null || this === root ? 0 : this.parent.children.offset(this) + this.parent.offset(root);\n  }\n  optimize(_context) {\n    this.statics.requiredContainer && !(this.parent instanceof this.statics.requiredContainer) && this.wrap(this.statics.requiredContainer.blotName);\n  }\n  remove() {\n    this.domNode.parentNode != null && this.domNode.parentNode.removeChild(this.domNode), this.detach();\n  }\n  replaceWith(name, value) {\n    const replacement = typeof name == \"string\" ? this.scroll.create(name, value) : name;\n    return this.parent != null && (this.parent.insertBefore(replacement, this.next || void 0), this.remove()), replacement;\n  }\n  split(index, _force) {\n    return index === 0 ? this : this.next;\n  }\n  update(_mutations, _context) {\n  }\n  wrap(name, value) {\n    const wrapper = typeof name == \"string\" ? this.scroll.create(name, value) : name;\n    if (this.parent != null && this.parent.insertBefore(wrapper, this.next || void 0), typeof wrapper.appendChild != \"function\")\n      throw new ParchmentError(`Cannot wrap ${name}`);\n    return wrapper.appendChild(this), wrapper;\n  }\n};\n_ShadowBlot.blotName = \"abstract\";\nlet ShadowBlot = _ShadowBlot;\nconst _LeafBlot = class _LeafBlot extends ShadowBlot {\n  /**\n   * Returns the value represented by domNode if it is this Blot's type\n   * No checking that domNode can represent this Blot type is required so\n   * applications needing it should check externally before calling.\n   */\n  static value(_domNode) {\n    return !0;\n  }\n  /**\n   * Given location represented by node and offset from DOM Selection Range,\n   * return index to that location.\n   */\n  index(node, offset) {\n    return this.domNode === node || this.domNode.compareDocumentPosition(node) & Node.DOCUMENT_POSITION_CONTAINED_BY ? Math.min(offset, 1) : -1;\n  }\n  /**\n   * Given index to location within blot, return node and offset representing\n   * that location, consumable by DOM Selection Range\n   */\n  position(index, _inclusive) {\n    let offset = Array.from(this.parent.domNode.childNodes).indexOf(this.domNode);\n    return index > 0 && (offset += 1), [this.parent.domNode, offset];\n  }\n  /**\n   * Return value represented by this blot\n   * Should not change without interaction from API or\n   * user change detectable by update()\n   */\n  value() {\n    return {\n      [this.statics.blotName]: this.statics.value(this.domNode) || !0\n    };\n  }\n};\n_LeafBlot.scope = Scope.INLINE_BLOT;\nlet LeafBlot = _LeafBlot;\nconst LeafBlot$1 = LeafBlot;\nclass LinkedList {\n  constructor() {\n    this.head = null, this.tail = null, this.length = 0;\n  }\n  append(...nodes) {\n    if (this.insertBefore(nodes[0], null), nodes.length > 1) {\n      const rest = nodes.slice(1);\n      this.append(...rest);\n    }\n  }\n  at(index) {\n    const next = this.iterator();\n    let cur = next();\n    for (; cur && index > 0; )\n      index -= 1, cur = next();\n    return cur;\n  }\n  contains(node) {\n    const next = this.iterator();\n    let cur = next();\n    for (; cur; ) {\n      if (cur === node)\n        return !0;\n      cur = next();\n    }\n    return !1;\n  }\n  indexOf(node) {\n    const next = this.iterator();\n    let cur = next(), index = 0;\n    for (; cur; ) {\n      if (cur === node)\n        return index;\n      index += 1, cur = next();\n    }\n    return -1;\n  }\n  insertBefore(node, refNode) {\n    node != null && (this.remove(node), node.next = refNode, refNode != null ? (node.prev = refNode.prev, refNode.prev != null && (refNode.prev.next = node), refNode.prev = node, refNode === this.head && (this.head = node)) : this.tail != null ? (this.tail.next = node, node.prev = this.tail, this.tail = node) : (node.prev = null, this.head = this.tail = node), this.length += 1);\n  }\n  offset(target) {\n    let index = 0, cur = this.head;\n    for (; cur != null; ) {\n      if (cur === target)\n        return index;\n      index += cur.length(), cur = cur.next;\n    }\n    return -1;\n  }\n  remove(node) {\n    this.contains(node) && (node.prev != null && (node.prev.next = node.next), node.next != null && (node.next.prev = node.prev), node === this.head && (this.head = node.next), node === this.tail && (this.tail = node.prev), this.length -= 1);\n  }\n  iterator(curNode = this.head) {\n    return () => {\n      const ret = curNode;\n      return curNode != null && (curNode = curNode.next), ret;\n    };\n  }\n  find(index, inclusive = !1) {\n    const next = this.iterator();\n    let cur = next();\n    for (; cur; ) {\n      const length = cur.length();\n      if (index < length || inclusive && index === length && (cur.next == null || cur.next.length() !== 0))\n        return [cur, index];\n      index -= length, cur = next();\n    }\n    return [null, 0];\n  }\n  forEach(callback) {\n    const next = this.iterator();\n    let cur = next();\n    for (; cur; )\n      callback(cur), cur = next();\n  }\n  forEachAt(index, length, callback) {\n    if (length <= 0)\n      return;\n    const [startNode, offset] = this.find(index);\n    let curIndex = index - offset;\n    const next = this.iterator(startNode);\n    let cur = next();\n    for (; cur && curIndex < index + length; ) {\n      const curLength = cur.length();\n      index > curIndex ? callback(\n        cur,\n        index - curIndex,\n        Math.min(length, curIndex + curLength - index)\n      ) : callback(cur, 0, Math.min(curLength, index + length - curIndex)), curIndex += curLength, cur = next();\n    }\n  }\n  map(callback) {\n    return this.reduce((memo, cur) => (memo.push(callback(cur)), memo), []);\n  }\n  reduce(callback, memo) {\n    const next = this.iterator();\n    let cur = next();\n    for (; cur; )\n      memo = callback(memo, cur), cur = next();\n    return memo;\n  }\n}\nfunction makeAttachedBlot(node, scroll) {\n  const found = scroll.find(node);\n  if (found)\n    return found;\n  try {\n    return scroll.create(node);\n  } catch {\n    const blot = scroll.create(Scope.INLINE);\n    return Array.from(node.childNodes).forEach((child) => {\n      blot.domNode.appendChild(child);\n    }), node.parentNode && node.parentNode.replaceChild(blot.domNode, node), blot.attach(), blot;\n  }\n}\nconst _ParentBlot = class _ParentBlot extends ShadowBlot {\n  constructor(scroll, domNode) {\n    super(scroll, domNode), this.uiNode = null, this.build();\n  }\n  appendChild(other) {\n    this.insertBefore(other);\n  }\n  attach() {\n    super.attach(), this.children.forEach((child) => {\n      child.attach();\n    });\n  }\n  attachUI(node) {\n    this.uiNode != null && this.uiNode.remove(), this.uiNode = node, _ParentBlot.uiClass && this.uiNode.classList.add(_ParentBlot.uiClass), this.uiNode.setAttribute(\"contenteditable\", \"false\"), this.domNode.insertBefore(this.uiNode, this.domNode.firstChild);\n  }\n  /**\n   * Called during construction, should fill its own children LinkedList.\n   */\n  build() {\n    this.children = new LinkedList(), Array.from(this.domNode.childNodes).filter((node) => node !== this.uiNode).reverse().forEach((node) => {\n      try {\n        const child = makeAttachedBlot(node, this.scroll);\n        this.insertBefore(child, this.children.head || void 0);\n      } catch (err) {\n        if (err instanceof ParchmentError)\n          return;\n        throw err;\n      }\n    });\n  }\n  deleteAt(index, length) {\n    if (index === 0 && length === this.length())\n      return this.remove();\n    this.children.forEachAt(index, length, (child, offset, childLength) => {\n      child.deleteAt(offset, childLength);\n    });\n  }\n  descendant(criteria, index = 0) {\n    const [child, offset] = this.children.find(index);\n    return criteria.blotName == null && criteria(child) || criteria.blotName != null && child instanceof criteria ? [child, offset] : child instanceof _ParentBlot ? child.descendant(criteria, offset) : [null, -1];\n  }\n  descendants(criteria, index = 0, length = Number.MAX_VALUE) {\n    let descendants = [], lengthLeft = length;\n    return this.children.forEachAt(\n      index,\n      length,\n      (child, childIndex, childLength) => {\n        (criteria.blotName == null && criteria(child) || criteria.blotName != null && child instanceof criteria) && descendants.push(child), child instanceof _ParentBlot && (descendants = descendants.concat(\n          child.descendants(criteria, childIndex, lengthLeft)\n        )), lengthLeft -= childLength;\n      }\n    ), descendants;\n  }\n  detach() {\n    this.children.forEach((child) => {\n      child.detach();\n    }), super.detach();\n  }\n  enforceAllowedChildren() {\n    let done = !1;\n    this.children.forEach((child) => {\n      done || this.statics.allowedChildren.some(\n        (def) => child instanceof def\n      ) || (child.statics.scope === Scope.BLOCK_BLOT ? (child.next != null && this.splitAfter(child), child.prev != null && this.splitAfter(child.prev), child.parent.unwrap(), done = !0) : child instanceof _ParentBlot ? child.unwrap() : child.remove());\n    });\n  }\n  formatAt(index, length, name, value) {\n    this.children.forEachAt(index, length, (child, offset, childLength) => {\n      child.formatAt(offset, childLength, name, value);\n    });\n  }\n  insertAt(index, value, def) {\n    const [child, offset] = this.children.find(index);\n    if (child)\n      child.insertAt(offset, value, def);\n    else {\n      const blot = def == null ? this.scroll.create(\"text\", value) : this.scroll.create(value, def);\n      this.appendChild(blot);\n    }\n  }\n  insertBefore(childBlot, refBlot) {\n    childBlot.parent != null && childBlot.parent.children.remove(childBlot);\n    let refDomNode = null;\n    this.children.insertBefore(childBlot, refBlot || null), childBlot.parent = this, refBlot != null && (refDomNode = refBlot.domNode), (this.domNode.parentNode !== childBlot.domNode || this.domNode.nextSibling !== refDomNode) && this.domNode.insertBefore(childBlot.domNode, refDomNode), childBlot.attach();\n  }\n  length() {\n    return this.children.reduce((memo, child) => memo + child.length(), 0);\n  }\n  moveChildren(targetParent, refNode) {\n    this.children.forEach((child) => {\n      targetParent.insertBefore(child, refNode);\n    });\n  }\n  optimize(context) {\n    if (super.optimize(context), this.enforceAllowedChildren(), this.uiNode != null && this.uiNode !== this.domNode.firstChild && this.domNode.insertBefore(this.uiNode, this.domNode.firstChild), this.children.length === 0)\n      if (this.statics.defaultChild != null) {\n        const child = this.scroll.create(this.statics.defaultChild.blotName);\n        this.appendChild(child);\n      } else\n        this.remove();\n  }\n  path(index, inclusive = !1) {\n    const [child, offset] = this.children.find(index, inclusive), position = [[this, index]];\n    return child instanceof _ParentBlot ? position.concat(child.path(offset, inclusive)) : (child != null && position.push([child, offset]), position);\n  }\n  removeChild(child) {\n    this.children.remove(child);\n  }\n  replaceWith(name, value) {\n    const replacement = typeof name == \"string\" ? this.scroll.create(name, value) : name;\n    return replacement instanceof _ParentBlot && this.moveChildren(replacement), super.replaceWith(replacement);\n  }\n  split(index, force = !1) {\n    if (!force) {\n      if (index === 0)\n        return this;\n      if (index === this.length())\n        return this.next;\n    }\n    const after = this.clone();\n    return this.parent && this.parent.insertBefore(after, this.next || void 0), this.children.forEachAt(index, this.length(), (child, offset, _length) => {\n      const split = child.split(offset, force);\n      split != null && after.appendChild(split);\n    }), after;\n  }\n  splitAfter(child) {\n    const after = this.clone();\n    for (; child.next != null; )\n      after.appendChild(child.next);\n    return this.parent && this.parent.insertBefore(after, this.next || void 0), after;\n  }\n  unwrap() {\n    this.parent && this.moveChildren(this.parent, this.next || void 0), this.remove();\n  }\n  update(mutations, _context) {\n    const addedNodes = [], removedNodes = [];\n    mutations.forEach((mutation) => {\n      mutation.target === this.domNode && mutation.type === \"childList\" && (addedNodes.push(...mutation.addedNodes), removedNodes.push(...mutation.removedNodes));\n    }), removedNodes.forEach((node) => {\n      if (node.parentNode != null && // @ts-expect-error Fix me later\n      node.tagName !== \"IFRAME\" && document.body.compareDocumentPosition(node) & Node.DOCUMENT_POSITION_CONTAINED_BY)\n        return;\n      const blot = this.scroll.find(node);\n      blot != null && (blot.domNode.parentNode == null || blot.domNode.parentNode === this.domNode) && blot.detach();\n    }), addedNodes.filter((node) => node.parentNode === this.domNode && node !== this.uiNode).sort((a, b) => a === b ? 0 : a.compareDocumentPosition(b) & Node.DOCUMENT_POSITION_FOLLOWING ? 1 : -1).forEach((node) => {\n      let refBlot = null;\n      node.nextSibling != null && (refBlot = this.scroll.find(node.nextSibling));\n      const blot = makeAttachedBlot(node, this.scroll);\n      (blot.next !== refBlot || blot.next == null) && (blot.parent != null && blot.parent.removeChild(this), this.insertBefore(blot, refBlot || void 0));\n    }), this.enforceAllowedChildren();\n  }\n};\n_ParentBlot.uiClass = \"\";\nlet ParentBlot = _ParentBlot;\nconst ParentBlot$1 = ParentBlot;\nfunction isEqual(obj1, obj2) {\n  if (Object.keys(obj1).length !== Object.keys(obj2).length)\n    return !1;\n  for (const prop in obj1)\n    if (obj1[prop] !== obj2[prop])\n      return !1;\n  return !0;\n}\nconst _InlineBlot = class _InlineBlot extends ParentBlot$1 {\n  static create(value) {\n    return super.create(value);\n  }\n  static formats(domNode, scroll) {\n    const match2 = scroll.query(_InlineBlot.blotName);\n    if (!(match2 != null && domNode.tagName === match2.tagName)) {\n      if (typeof this.tagName == \"string\")\n        return !0;\n      if (Array.isArray(this.tagName))\n        return domNode.tagName.toLowerCase();\n    }\n  }\n  constructor(scroll, domNode) {\n    super(scroll, domNode), this.attributes = new AttributorStore$1(this.domNode);\n  }\n  format(name, value) {\n    if (name === this.statics.blotName && !value)\n      this.children.forEach((child) => {\n        child instanceof _InlineBlot || (child = child.wrap(_InlineBlot.blotName, !0)), this.attributes.copy(child);\n      }), this.unwrap();\n    else {\n      const format = this.scroll.query(name, Scope.INLINE);\n      if (format == null)\n        return;\n      format instanceof Attributor ? this.attributes.attribute(format, value) : value && (name !== this.statics.blotName || this.formats()[name] !== value) && this.replaceWith(name, value);\n    }\n  }\n  formats() {\n    const formats = this.attributes.values(), format = this.statics.formats(this.domNode, this.scroll);\n    return format != null && (formats[this.statics.blotName] = format), formats;\n  }\n  formatAt(index, length, name, value) {\n    this.formats()[name] != null || this.scroll.query(name, Scope.ATTRIBUTE) ? this.isolate(index, length).format(name, value) : super.formatAt(index, length, name, value);\n  }\n  optimize(context) {\n    super.optimize(context);\n    const formats = this.formats();\n    if (Object.keys(formats).length === 0)\n      return this.unwrap();\n    const next = this.next;\n    next instanceof _InlineBlot && next.prev === this && isEqual(formats, next.formats()) && (next.moveChildren(this), next.remove());\n  }\n  replaceWith(name, value) {\n    const replacement = super.replaceWith(name, value);\n    return this.attributes.copy(replacement), replacement;\n  }\n  update(mutations, context) {\n    super.update(mutations, context), mutations.some(\n      (mutation) => mutation.target === this.domNode && mutation.type === \"attributes\"\n    ) && this.attributes.build();\n  }\n  wrap(name, value) {\n    const wrapper = super.wrap(name, value);\n    return wrapper instanceof _InlineBlot && this.attributes.move(wrapper), wrapper;\n  }\n};\n_InlineBlot.allowedChildren = [_InlineBlot, LeafBlot$1], _InlineBlot.blotName = \"inline\", _InlineBlot.scope = Scope.INLINE_BLOT, _InlineBlot.tagName = \"SPAN\";\nlet InlineBlot = _InlineBlot;\nconst InlineBlot$1 = InlineBlot, _BlockBlot = class _BlockBlot extends ParentBlot$1 {\n  static create(value) {\n    return super.create(value);\n  }\n  static formats(domNode, scroll) {\n    const match2 = scroll.query(_BlockBlot.blotName);\n    if (!(match2 != null && domNode.tagName === match2.tagName)) {\n      if (typeof this.tagName == \"string\")\n        return !0;\n      if (Array.isArray(this.tagName))\n        return domNode.tagName.toLowerCase();\n    }\n  }\n  constructor(scroll, domNode) {\n    super(scroll, domNode), this.attributes = new AttributorStore$1(this.domNode);\n  }\n  format(name, value) {\n    const format = this.scroll.query(name, Scope.BLOCK);\n    format != null && (format instanceof Attributor ? this.attributes.attribute(format, value) : name === this.statics.blotName && !value ? this.replaceWith(_BlockBlot.blotName) : value && (name !== this.statics.blotName || this.formats()[name] !== value) && this.replaceWith(name, value));\n  }\n  formats() {\n    const formats = this.attributes.values(), format = this.statics.formats(this.domNode, this.scroll);\n    return format != null && (formats[this.statics.blotName] = format), formats;\n  }\n  formatAt(index, length, name, value) {\n    this.scroll.query(name, Scope.BLOCK) != null ? this.format(name, value) : super.formatAt(index, length, name, value);\n  }\n  insertAt(index, value, def) {\n    if (def == null || this.scroll.query(value, Scope.INLINE) != null)\n      super.insertAt(index, value, def);\n    else {\n      const after = this.split(index);\n      if (after != null) {\n        const blot = this.scroll.create(value, def);\n        after.parent.insertBefore(blot, after);\n      } else\n        throw new Error(\"Attempt to insertAt after block boundaries\");\n    }\n  }\n  replaceWith(name, value) {\n    const replacement = super.replaceWith(name, value);\n    return this.attributes.copy(replacement), replacement;\n  }\n  update(mutations, context) {\n    super.update(mutations, context), mutations.some(\n      (mutation) => mutation.target === this.domNode && mutation.type === \"attributes\"\n    ) && this.attributes.build();\n  }\n};\n_BlockBlot.blotName = \"block\", _BlockBlot.scope = Scope.BLOCK_BLOT, _BlockBlot.tagName = \"P\", _BlockBlot.allowedChildren = [\n  InlineBlot$1,\n  _BlockBlot,\n  LeafBlot$1\n];\nlet BlockBlot = _BlockBlot;\nconst BlockBlot$1 = BlockBlot, _ContainerBlot = class _ContainerBlot extends ParentBlot$1 {\n  checkMerge() {\n    return this.next !== null && this.next.statics.blotName === this.statics.blotName;\n  }\n  deleteAt(index, length) {\n    super.deleteAt(index, length), this.enforceAllowedChildren();\n  }\n  formatAt(index, length, name, value) {\n    super.formatAt(index, length, name, value), this.enforceAllowedChildren();\n  }\n  insertAt(index, value, def) {\n    super.insertAt(index, value, def), this.enforceAllowedChildren();\n  }\n  optimize(context) {\n    super.optimize(context), this.children.length > 0 && this.next != null && this.checkMerge() && (this.next.moveChildren(this), this.next.remove());\n  }\n};\n_ContainerBlot.blotName = \"container\", _ContainerBlot.scope = Scope.BLOCK_BLOT;\nlet ContainerBlot = _ContainerBlot;\nconst ContainerBlot$1 = ContainerBlot;\nclass EmbedBlot extends LeafBlot$1 {\n  static formats(_domNode, _scroll) {\n  }\n  format(name, value) {\n    super.formatAt(0, this.length(), name, value);\n  }\n  formatAt(index, length, name, value) {\n    index === 0 && length === this.length() ? this.format(name, value) : super.formatAt(index, length, name, value);\n  }\n  formats() {\n    return this.statics.formats(this.domNode, this.scroll);\n  }\n}\nconst EmbedBlot$1 = EmbedBlot, OBSERVER_CONFIG = {\n  attributes: !0,\n  characterData: !0,\n  characterDataOldValue: !0,\n  childList: !0,\n  subtree: !0\n}, MAX_OPTIMIZE_ITERATIONS = 100, _ScrollBlot = class _ScrollBlot extends ParentBlot$1 {\n  constructor(registry, node) {\n    super(null, node), this.registry = registry, this.scroll = this, this.build(), this.observer = new MutationObserver((mutations) => {\n      this.update(mutations);\n    }), this.observer.observe(this.domNode, OBSERVER_CONFIG), this.attach();\n  }\n  create(input, value) {\n    return this.registry.create(this, input, value);\n  }\n  find(node, bubble = !1) {\n    const blot = this.registry.find(node, bubble);\n    return blot ? blot.scroll === this ? blot : bubble ? this.find(blot.scroll.domNode.parentNode, !0) : null : null;\n  }\n  query(query, scope = Scope.ANY) {\n    return this.registry.query(query, scope);\n  }\n  register(...definitions) {\n    return this.registry.register(...definitions);\n  }\n  build() {\n    this.scroll != null && super.build();\n  }\n  detach() {\n    super.detach(), this.observer.disconnect();\n  }\n  deleteAt(index, length) {\n    this.update(), index === 0 && length === this.length() ? this.children.forEach((child) => {\n      child.remove();\n    }) : super.deleteAt(index, length);\n  }\n  formatAt(index, length, name, value) {\n    this.update(), super.formatAt(index, length, name, value);\n  }\n  insertAt(index, value, def) {\n    this.update(), super.insertAt(index, value, def);\n  }\n  optimize(mutations = [], context = {}) {\n    super.optimize(context);\n    const mutationsMap = context.mutationsMap || /* @__PURE__ */ new WeakMap();\n    let records = Array.from(this.observer.takeRecords());\n    for (; records.length > 0; )\n      mutations.push(records.pop());\n    const mark = (blot, markParent = !0) => {\n      blot == null || blot === this || blot.domNode.parentNode != null && (mutationsMap.has(blot.domNode) || mutationsMap.set(blot.domNode, []), markParent && mark(blot.parent));\n    }, optimize = (blot) => {\n      mutationsMap.has(blot.domNode) && (blot instanceof ParentBlot$1 && blot.children.forEach(optimize), mutationsMap.delete(blot.domNode), blot.optimize(context));\n    };\n    let remaining = mutations;\n    for (let i = 0; remaining.length > 0; i += 1) {\n      if (i >= MAX_OPTIMIZE_ITERATIONS)\n        throw new Error(\"[Parchment] Maximum optimize iterations reached\");\n      for (remaining.forEach((mutation) => {\n        const blot = this.find(mutation.target, !0);\n        blot != null && (blot.domNode === mutation.target && (mutation.type === \"childList\" ? (mark(this.find(mutation.previousSibling, !1)), Array.from(mutation.addedNodes).forEach((node) => {\n          const child = this.find(node, !1);\n          mark(child, !1), child instanceof ParentBlot$1 && child.children.forEach((grandChild) => {\n            mark(grandChild, !1);\n          });\n        })) : mutation.type === \"attributes\" && mark(blot.prev)), mark(blot));\n      }), this.children.forEach(optimize), remaining = Array.from(this.observer.takeRecords()), records = remaining.slice(); records.length > 0; )\n        mutations.push(records.pop());\n    }\n  }\n  update(mutations, context = {}) {\n    mutations = mutations || this.observer.takeRecords();\n    const mutationsMap = /* @__PURE__ */ new WeakMap();\n    mutations.map((mutation) => {\n      const blot = this.find(mutation.target, !0);\n      return blot == null ? null : mutationsMap.has(blot.domNode) ? (mutationsMap.get(blot.domNode).push(mutation), null) : (mutationsMap.set(blot.domNode, [mutation]), blot);\n    }).forEach((blot) => {\n      blot != null && blot !== this && mutationsMap.has(blot.domNode) && blot.update(mutationsMap.get(blot.domNode) || [], context);\n    }), context.mutationsMap = mutationsMap, mutationsMap.has(this.domNode) && super.update(mutationsMap.get(this.domNode), context), this.optimize(mutations, context);\n  }\n};\n_ScrollBlot.blotName = \"scroll\", _ScrollBlot.defaultChild = BlockBlot$1, _ScrollBlot.allowedChildren = [BlockBlot$1, ContainerBlot$1], _ScrollBlot.scope = Scope.BLOCK_BLOT, _ScrollBlot.tagName = \"DIV\";\nlet ScrollBlot = _ScrollBlot;\nconst ScrollBlot$1 = ScrollBlot, _TextBlot = class _TextBlot extends LeafBlot$1 {\n  static create(value) {\n    return document.createTextNode(value);\n  }\n  static value(domNode) {\n    return domNode.data;\n  }\n  constructor(scroll, node) {\n    super(scroll, node), this.text = this.statics.value(this.domNode);\n  }\n  deleteAt(index, length) {\n    this.domNode.data = this.text = this.text.slice(0, index) + this.text.slice(index + length);\n  }\n  index(node, offset) {\n    return this.domNode === node ? offset : -1;\n  }\n  insertAt(index, value, def) {\n    def == null ? (this.text = this.text.slice(0, index) + value + this.text.slice(index), this.domNode.data = this.text) : super.insertAt(index, value, def);\n  }\n  length() {\n    return this.text.length;\n  }\n  optimize(context) {\n    super.optimize(context), this.text = this.statics.value(this.domNode), this.text.length === 0 ? this.remove() : this.next instanceof _TextBlot && this.next.prev === this && (this.insertAt(this.length(), this.next.value()), this.next.remove());\n  }\n  position(index, _inclusive = !1) {\n    return [this.domNode, index];\n  }\n  split(index, force = !1) {\n    if (!force) {\n      if (index === 0)\n        return this;\n      if (index === this.length())\n        return this.next;\n    }\n    const after = this.scroll.create(this.domNode.splitText(index));\n    return this.parent.insertBefore(after, this.next || void 0), this.text = this.statics.value(this.domNode), after;\n  }\n  update(mutations, _context) {\n    mutations.some((mutation) => mutation.type === \"characterData\" && mutation.target === this.domNode) && (this.text = this.statics.value(this.domNode));\n  }\n  value() {\n    return this.text;\n  }\n};\n_TextBlot.blotName = \"text\", _TextBlot.scope = Scope.INLINE_BLOT;\nlet TextBlot = _TextBlot;\nconst TextBlot$1 = TextBlot;\nexport {\n  Attributor,\n  AttributorStore$1 as AttributorStore,\n  BlockBlot$1 as BlockBlot,\n  ClassAttributor$1 as ClassAttributor,\n  ContainerBlot$1 as ContainerBlot,\n  EmbedBlot$1 as EmbedBlot,\n  InlineBlot$1 as InlineBlot,\n  LeafBlot$1 as LeafBlot,\n  ParentBlot$1 as ParentBlot,\n  Registry,\n  Scope,\n  ScrollBlot$1 as ScrollBlot,\n  StyleAttributor$1 as StyleAttributor,\n  TextBlot$1 as TextBlot\n};\n"], "mappings": "AAAA,IAAIA,KAAK,GAAG,eAAgB,CAAEC,MAAM,KAAMA,MAAM,CAACA,MAAM,CAACC,IAAI,GAAG,CAAC,CAAC,GAAG,MAAM,EAAED,MAAM,CAACA,MAAM,CAACE,KAAK,GAAG,EAAE,CAAC,GAAG,OAAO,EAAEF,MAAM,CAACA,MAAM,CAACG,SAAS,GAAG,EAAE,CAAC,GAAG,WAAW,EAAEH,MAAM,CAACA,MAAM,CAACI,IAAI,GAAG,EAAE,CAAC,GAAG,MAAM,EAAEJ,MAAM,CAACA,MAAM,CAACK,MAAM,GAAG,CAAC,CAAC,GAAG,QAAQ,EAAEL,MAAM,CAACA,MAAM,CAACM,KAAK,GAAG,EAAE,CAAC,GAAG,OAAO,EAAEN,MAAM,CAACA,MAAM,CAACO,UAAU,GAAG,EAAE,CAAC,GAAG,YAAY,EAAEP,MAAM,CAACA,MAAM,CAACQ,WAAW,GAAG,CAAC,CAAC,GAAG,aAAa,EAAER,MAAM,CAACA,MAAM,CAACS,eAAe,GAAG,CAAC,CAAC,GAAG,iBAAiB,EAAET,MAAM,CAACA,MAAM,CAACU,gBAAgB,GAAG,CAAC,CAAC,GAAG,kBAAkB,EAAEV,MAAM,CAACA,MAAM,CAACW,GAAG,GAAG,EAAE,CAAC,GAAG,KAAK,EAAEX,MAAM,CAAC,EAAED,KAAK,IAAI,CAAC,CAAC,CAAC;AACnhB,MAAMa,UAAU,CAAC;EACfC,WAAWA,CAACC,QAAQ,EAAEC,OAAO,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IAC3C,IAAI,CAACF,QAAQ,GAAGA,QAAQ,EAAE,IAAI,CAACC,OAAO,GAAGA,OAAO;IAChD,MAAME,YAAY,GAAGlB,KAAK,CAACE,IAAI,GAAGF,KAAK,CAACI,SAAS;IACjD,IAAI,CAACe,KAAK,GAAGF,OAAO,CAACE,KAAK,IAAI,IAAI;IAChC;IACAF,OAAO,CAACE,KAAK,GAAGnB,KAAK,CAACG,KAAK,GAAGe,YAAY,GACxClB,KAAK,CAACI,SAAS,EAAEa,OAAO,CAACG,SAAS,IAAI,IAAI,KAAK,IAAI,CAACA,SAAS,GAAGH,OAAO,CAACG,SAAS,CAAC;EACxF;EACA,OAAOC,IAAIA,CAACC,IAAI,EAAE;IAChB,OAAOC,KAAK,CAACC,IAAI,CAACF,IAAI,CAACG,UAAU,CAAC,CAACC,GAAG,CAAEC,IAAI,IAAKA,IAAI,CAACC,IAAI,CAAC;EAC7D;EACAC,GAAGA,CAACP,IAAI,EAAEQ,KAAK,EAAE;IACf,OAAO,IAAI,CAACC,MAAM,CAACT,IAAI,EAAEQ,KAAK,CAAC,IAAIR,IAAI,CAACU,YAAY,CAAC,IAAI,CAAChB,OAAO,EAAEc,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;EACrF;EACAC,MAAMA,CAACE,KAAK,EAAEH,KAAK,EAAE;IACnB,OAAO,IAAI,CAACV,SAAS,IAAI,IAAI,GAAG,CAAC,CAAC,GAAG,OAAOU,KAAK,IAAI,QAAQ,GAAG,IAAI,CAACV,SAAS,CAACc,OAAO,CAACJ,KAAK,CAACK,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAACf,SAAS,CAACc,OAAO,CAACJ,KAAK,CAAC,GAAG,CAAC,CAAC;EAC9J;EACAM,MAAMA,CAACd,IAAI,EAAE;IACXA,IAAI,CAACe,eAAe,CAAC,IAAI,CAACrB,OAAO,CAAC;EACpC;EACAc,KAAKA,CAACR,IAAI,EAAE;IACV,MAAMQ,KAAK,GAAGR,IAAI,CAACgB,YAAY,CAAC,IAAI,CAACtB,OAAO,CAAC;IAC7C,OAAO,IAAI,CAACe,MAAM,CAACT,IAAI,EAAEQ,KAAK,CAAC,IAAIA,KAAK,GAAGA,KAAK,GAAG,EAAE;EACvD;AACF;AACA,MAAMS,cAAc,SAASC,KAAK,CAAC;EACjC1B,WAAWA,CAAC2B,OAAO,EAAE;IACnBA,OAAO,GAAG,cAAc,GAAGA,OAAO,EAAE,KAAK,CAACA,OAAO,CAAC,EAAE,IAAI,CAACA,OAAO,GAAGA,OAAO,EAAE,IAAI,CAACb,IAAI,GAAG,IAAI,CAACd,WAAW,CAACc,IAAI;EAC/G;AACF;AACA,MAAMc,SAAS,GAAG,MAAMA,SAAS,CAAC;EAChC5B,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACW,UAAU,GAAG,CAAC,CAAC,EAAE,IAAI,CAACkB,OAAO,GAAG,CAAC,CAAC,EAAE,IAAI,CAACC,IAAI,GAAG,CAAC,CAAC,EAAE,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;EAC1E;EACA,OAAOC,IAAIA,CAACxB,IAAI,EAAEyB,MAAM,GAAG,CAAC,CAAC,EAAE;IAC7B,IAAIzB,IAAI,IAAI,IAAI,EACd,OAAO,IAAI;IACb,IAAI,IAAI,CAAC0B,KAAK,CAACC,GAAG,CAAC3B,IAAI,CAAC,EACtB,OAAO,IAAI,CAAC0B,KAAK,CAACE,GAAG,CAAC5B,IAAI,CAAC,IAAI,IAAI;IACrC,IAAIyB,MAAM,EAAE;MACV,IAAII,UAAU,GAAG,IAAI;MACrB,IAAI;QACFA,UAAU,GAAG7B,IAAI,CAAC6B,UAAU;MAC9B,CAAC,CAAC,MAAM;QACN,OAAO,IAAI;MACb;MACA,OAAO,IAAI,CAACL,IAAI,CAACK,UAAU,EAAEJ,MAAM,CAAC;IACtC;IACA,OAAO,IAAI;EACb;EACAK,MAAMA,CAACC,MAAM,EAAEC,KAAK,EAAExB,KAAK,EAAE;IAC3B,MAAMyB,MAAM,GAAG,IAAI,CAACC,KAAK,CAACF,KAAK,CAAC;IAChC,IAAIC,MAAM,IAAI,IAAI,EAChB,MAAM,IAAIhB,cAAc,CAAE,oBAAmBe,KAAM,OAAM,CAAC;IAC5D,MAAMG,SAAS,GAAGF,MAAM;MAAEjC,IAAI;MAC5B;MACAgC,KAAK,YAAYI,IAAI,IAAIJ,KAAK,CAACK,QAAQ,KAAKD,IAAI,CAACE,SAAS,GAAGN,KAAK,GAAGG,SAAS,CAACL,MAAM,CAACtB,KAAK,CAC5F;MAAE+B,IAAI,GAAG,IAAIJ,SAAS,CAACJ,MAAM,EAAE/B,IAAI,EAAEQ,KAAK,CAAC;IAC5C,OAAOY,SAAS,CAACM,KAAK,CAACc,GAAG,CAACD,IAAI,CAACE,OAAO,EAAEF,IAAI,CAAC,EAAEA,IAAI;EACtD;EACAf,IAAIA,CAACxB,IAAI,EAAEyB,MAAM,GAAG,CAAC,CAAC,EAAE;IACtB,OAAOL,SAAS,CAACI,IAAI,CAACxB,IAAI,EAAEyB,MAAM,CAAC;EACrC;EACAS,KAAKA,CAACA,KAAK,EAAErC,KAAK,GAAGnB,KAAK,CAACY,GAAG,EAAE;IAC9B,IAAI2C,MAAM;IACV,OAAO,OAAOC,KAAK,IAAI,QAAQ,GAAGD,MAAM,GAAG,IAAI,CAACV,KAAK,CAACW,KAAK,CAAC,IAAI,IAAI,CAAC/B,UAAU,CAAC+B,KAAK,CAAC,GAAGA,KAAK,YAAYQ,IAAI,IAAIR,KAAK,CAACG,QAAQ,KAAKD,IAAI,CAACE,SAAS,GAAGL,MAAM,GAAG,IAAI,CAACV,KAAK,CAACoB,IAAI,GAAG,OAAOT,KAAK,IAAI,QAAQ,GAAGA,KAAK,GAAGxD,KAAK,CAACG,KAAK,GAAGH,KAAK,CAACO,KAAK,GAAGgD,MAAM,GAAG,IAAI,CAACV,KAAK,CAACqB,KAAK,GAAGV,KAAK,GAAGxD,KAAK,CAACG,KAAK,GAAGH,KAAK,CAACM,MAAM,KAAKiD,MAAM,GAAG,IAAI,CAACV,KAAK,CAACsB,MAAM,CAAC,GAAGX,KAAK,YAAYY,OAAO,KAAK,CAACZ,KAAK,CAAClB,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE+B,KAAK,CAAC,KAAK,CAAC,CAACC,IAAI,CAAE1C,IAAI,KAAM2B,MAAM,GAAG,IAAI,CAACZ,OAAO,CAACf,IAAI,CAAC,EAAE,CAAC,CAAC2B,MAAM,CAAC,CAAC,EAAEA,MAAM,GAAGA,MAAM,IAAI,IAAI,CAACX,IAAI,CAACY,KAAK,CAACe,OAAO,CAAC,CAAC,EAAEhB,MAAM,IAAI,IAAI,GAAG,IAAI,GAAG,OAAO,IAAIA,MAAM,IAAIpC,KAAK,GAAGnB,KAAK,CAACG,KAAK,GAAGoD,MAAM,CAACpC,KAAK,IAAIA,KAAK,GAAGnB,KAAK,CAACE,IAAI,GAAGqD,MAAM,CAACpC,KAAK,GAAGoC,MAAM,GAAG,IAAI;EAC5oB;EACAiB,QAAQA,CAAC,GAAGC,WAAW,EAAE;IACvB,OAAOA,WAAW,CAAC/C,GAAG,CAAEgD,UAAU,IAAK;MACrC,MAAMC,MAAM,IAAG,UAAU,IAAID,UAAU;QAAEE,MAAM,IAAG,UAAU,IAAIF,UAAU;MAC1E,IAAI,CAACC,MAAM,IAAI,CAACC,MAAM,EACpB,MAAM,IAAIrC,cAAc,CAAC,oBAAoB,CAAC;MAChD,IAAIoC,MAAM,IAAID,UAAU,CAACG,QAAQ,KAAK,UAAU,EAC9C,MAAM,IAAItC,cAAc,CAAC,gCAAgC,CAAC;MAC5D,MAAMuC,GAAG,GAAGH,MAAM,GAAGD,UAAU,CAACG,QAAQ,GAAGD,MAAM,GAAGF,UAAU,CAAC3D,QAAQ,GAAG,KAAK,CAAC;MAChF,OAAO,IAAI,CAAC8B,KAAK,CAACiC,GAAG,CAAC,GAAGJ,UAAU,EAAEE,MAAM,GAAG,OAAOF,UAAU,CAAC1D,OAAO,IAAI,QAAQ,KAAK,IAAI,CAACS,UAAU,CAACiD,UAAU,CAAC1D,OAAO,CAAC,GAAG0D,UAAU,CAAC,GAAGC,MAAM,KAAKD,UAAU,CAACK,SAAS,KAAK,IAAI,CAACpC,OAAO,CAAC+B,UAAU,CAACK,SAAS,CAAC,GAAGL,UAAU,CAAC,EAAEA,UAAU,CAACH,OAAO,KAAKhD,KAAK,CAACyD,OAAO,CAACN,UAAU,CAACH,OAAO,CAAC,GAAGG,UAAU,CAACH,OAAO,GAAGG,UAAU,CAACH,OAAO,CAAC7C,GAAG,CAAE6C,OAAO,IAAKA,OAAO,CAACU,WAAW,CAAC,CAAC,CAAC,GAAGP,UAAU,CAACH,OAAO,GAAGG,UAAU,CAACH,OAAO,CAACU,WAAW,CAAC,CAAC,EAAE,CAAC1D,KAAK,CAACyD,OAAO,CAACN,UAAU,CAACH,OAAO,CAAC,GAAGG,UAAU,CAACH,OAAO,GAAG,CAACG,UAAU,CAACH,OAAO,CAAC,EAAEW,OAAO,CAAEC,GAAG,IAAK;QACrgB,CAAC,IAAI,CAACvC,IAAI,CAACuC,GAAG,CAAC,IAAI,IAAI,IAAIT,UAAU,CAACK,SAAS,IAAI,IAAI,MAAM,IAAI,CAACnC,IAAI,CAACuC,GAAG,CAAC,GAAGT,UAAU,CAAC;MAC3F,CAAC,CAAC,CAAC,CAAC,EAAEA,UAAU;IAClB,CAAC,CAAC;EACJ;AACF,CAAC;AACDhC,SAAS,CAACM,KAAK,GAAG,eAAgB,IAAIoC,OAAO,CAAC,CAAC;AAC/C,IAAIC,QAAQ,GAAG3C,SAAS;AACxB,SAAS4C,KAAKA,CAAChE,IAAI,EAAEiE,MAAM,EAAE;EAC3B,OAAO,CAACjE,IAAI,CAACgB,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE+B,KAAK,CAAC,KAAK,CAAC,CAACmB,MAAM,CAAE5D,IAAI,IAAKA,IAAI,CAACM,OAAO,CAAE,GAAEqD,MAAO,GAAE,CAAC,KAAK,CAAC,CAAC;AAC3G;AACA,MAAME,eAAe,SAAS5E,UAAU,CAAC;EACvC,OAAOQ,IAAIA,CAACC,IAAI,EAAE;IAChB,OAAO,CAACA,IAAI,CAACgB,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE+B,KAAK,CAAC,KAAK,CAAC,CAAC3C,GAAG,CAAEE,IAAI,IAAKA,IAAI,CAACyC,KAAK,CAAC,GAAG,CAAC,CAACqB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC;EAC9G;EACA9D,GAAGA,CAACP,IAAI,EAAEQ,KAAK,EAAE;IACf,OAAO,IAAI,CAACC,MAAM,CAACT,IAAI,EAAEQ,KAAK,CAAC,IAAI,IAAI,CAACM,MAAM,CAACd,IAAI,CAAC,EAAEA,IAAI,CAACsE,SAAS,CAAC/D,GAAG,CAAE,GAAE,IAAI,CAACb,OAAQ,IAAGc,KAAM,EAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;EAChH;EACAM,MAAMA,CAACd,IAAI,EAAE;IACXgE,KAAK,CAAChE,IAAI,EAAE,IAAI,CAACN,OAAO,CAAC,CAACkE,OAAO,CAAEtD,IAAI,IAAK;MAC1CN,IAAI,CAACsE,SAAS,CAACxD,MAAM,CAACR,IAAI,CAAC;IAC7B,CAAC,CAAC,EAAEN,IAAI,CAACsE,SAAS,CAACC,MAAM,KAAK,CAAC,IAAIvE,IAAI,CAACe,eAAe,CAAC,OAAO,CAAC;EAClE;EACAP,KAAKA,CAACR,IAAI,EAAE;IACV,MAAMQ,KAAK,GAAG,CAACwD,KAAK,CAAChE,IAAI,EAAE,IAAI,CAACN,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE0E,KAAK,CAAC,IAAI,CAAC1E,OAAO,CAAC6E,MAAM,GAAG,CAAC,CAAC;IACjF,OAAO,IAAI,CAAC9D,MAAM,CAACT,IAAI,EAAEQ,KAAK,CAAC,GAAGA,KAAK,GAAG,EAAE;EAC9C;AACF;AACA,MAAMgE,iBAAiB,GAAGL,eAAe;AACzC,SAASM,QAAQA,CAACnE,IAAI,EAAE;EACtB,MAAMoE,KAAK,GAAGpE,IAAI,CAACyC,KAAK,CAAC,GAAG,CAAC;IAAE4B,IAAI,GAAGD,KAAK,CAACN,KAAK,CAAC,CAAC,CAAC,CAAChE,GAAG,CAAEwE,IAAI,IAAKA,IAAI,CAAC,CAAC,CAAC,CAACjB,WAAW,CAAC,CAAC,GAAGiB,IAAI,CAACR,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;EAClH,OAAOK,KAAK,CAAC,CAAC,CAAC,GAAGC,IAAI;AACxB;AACA,MAAME,eAAe,SAAStF,UAAU,CAAC;EACvC,OAAOQ,IAAIA,CAACC,IAAI,EAAE;IAChB,OAAO,CAACA,IAAI,CAACgB,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE+B,KAAK,CAAC,GAAG,CAAC,CAAC3C,GAAG,CAAEI,KAAK,IAAKA,KAAK,CAACuC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC+B,IAAI,CAAC,CAAC,CAAC;EACjG;EACAvE,GAAGA,CAACP,IAAI,EAAEQ,KAAK,EAAE;IACf,OAAO,IAAI,CAACC,MAAM,CAACT,IAAI,EAAEQ,KAAK,CAAC,IAAIR,IAAI,CAAC+E,KAAK,CAACN,QAAQ,CAAC,IAAI,CAAC/E,OAAO,CAAC,CAAC,GAAGc,KAAK,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;EACzF;EACAM,MAAMA,CAACd,IAAI,EAAE;IACXA,IAAI,CAAC+E,KAAK,CAACN,QAAQ,CAAC,IAAI,CAAC/E,OAAO,CAAC,CAAC,GAAG,EAAE,EAAEM,IAAI,CAACgB,YAAY,CAAC,OAAO,CAAC,IAAIhB,IAAI,CAACe,eAAe,CAAC,OAAO,CAAC;EACtG;EACAP,KAAKA,CAACR,IAAI,EAAE;IACV,MAAMQ,KAAK,GAAGR,IAAI,CAAC+E,KAAK,CAACN,QAAQ,CAAC,IAAI,CAAC/E,OAAO,CAAC,CAAC;IAChD,OAAO,IAAI,CAACe,MAAM,CAACT,IAAI,EAAEQ,KAAK,CAAC,GAAGA,KAAK,GAAG,EAAE;EAC9C;AACF;AACA,MAAMwE,iBAAiB,GAAGH,eAAe;AACzC,MAAMI,eAAe,CAAC;EACpBzF,WAAWA,CAACiD,OAAO,EAAE;IACnB,IAAI,CAACtC,UAAU,GAAG,CAAC,CAAC,EAAE,IAAI,CAACsC,OAAO,GAAGA,OAAO,EAAE,IAAI,CAACyC,KAAK,CAAC,CAAC;EAC5D;EACAC,SAASA,CAACA,SAAS,EAAE3E,KAAK,EAAE;IAC1BA,KAAK,GAAG2E,SAAS,CAAC5E,GAAG,CAAC,IAAI,CAACkC,OAAO,EAAEjC,KAAK,CAAC,KAAK2E,SAAS,CAAC3E,KAAK,CAAC,IAAI,CAACiC,OAAO,CAAC,IAAI,IAAI,GAAG,IAAI,CAACtC,UAAU,CAACgF,SAAS,CAAC1F,QAAQ,CAAC,GAAG0F,SAAS,GAAG,OAAO,IAAI,CAAChF,UAAU,CAACgF,SAAS,CAAC1F,QAAQ,CAAC,CAAC,IAAI0F,SAAS,CAACrE,MAAM,CAAC,IAAI,CAAC2B,OAAO,CAAC,EAAE,OAAO,IAAI,CAACtC,UAAU,CAACgF,SAAS,CAAC1F,QAAQ,CAAC,CAAC;EACrQ;EACAyF,KAAKA,CAAA,EAAG;IACN,IAAI,CAAC/E,UAAU,GAAG,CAAC,CAAC;IACpB,MAAMoC,IAAI,GAAGwB,QAAQ,CAACvC,IAAI,CAAC,IAAI,CAACiB,OAAO,CAAC;IACxC,IAAIF,IAAI,IAAI,IAAI,EACd;IACF,MAAMpC,UAAU,GAAGZ,UAAU,CAACQ,IAAI,CAAC,IAAI,CAAC0C,OAAO,CAAC;MAAEpB,OAAO,GAAGmD,iBAAiB,CAACzE,IAAI,CAAC,IAAI,CAAC0C,OAAO,CAAC;MAAE2C,MAAM,GAAGJ,iBAAiB,CAACjF,IAAI,CAAC,IAAI,CAAC0C,OAAO,CAAC;IAC/ItC,UAAU,CAACkF,MAAM,CAAChE,OAAO,CAAC,CAACgE,MAAM,CAACD,MAAM,CAAC,CAACxB,OAAO,CAAEtD,IAAI,IAAK;MAC1D,MAAMgF,IAAI,GAAG/C,IAAI,CAACR,MAAM,CAACG,KAAK,CAAC5B,IAAI,EAAE5B,KAAK,CAACI,SAAS,CAAC;MACrDwG,IAAI,YAAY/F,UAAU,KAAK,IAAI,CAACY,UAAU,CAACmF,IAAI,CAAC7F,QAAQ,CAAC,GAAG6F,IAAI,CAAC;IACvE,CAAC,CAAC;EACJ;EACAC,IAAIA,CAACC,MAAM,EAAE;IACXC,MAAM,CAAC1F,IAAI,CAAC,IAAI,CAACI,UAAU,CAAC,CAACyD,OAAO,CAAEJ,GAAG,IAAK;MAC5C,MAAMhD,KAAK,GAAG,IAAI,CAACL,UAAU,CAACqD,GAAG,CAAC,CAAChD,KAAK,CAAC,IAAI,CAACiC,OAAO,CAAC;MACtD+C,MAAM,CAACE,MAAM,CAAClC,GAAG,EAAEhD,KAAK,CAAC;IAC3B,CAAC,CAAC;EACJ;EACAmF,IAAIA,CAACH,MAAM,EAAE;IACX,IAAI,CAACD,IAAI,CAACC,MAAM,CAAC,EAAEC,MAAM,CAAC1F,IAAI,CAAC,IAAI,CAACI,UAAU,CAAC,CAACyD,OAAO,CAAEJ,GAAG,IAAK;MAC/D,IAAI,CAACrD,UAAU,CAACqD,GAAG,CAAC,CAAC1C,MAAM,CAAC,IAAI,CAAC2B,OAAO,CAAC;IAC3C,CAAC,CAAC,EAAE,IAAI,CAACtC,UAAU,GAAG,CAAC,CAAC;EAC1B;EACAyF,MAAMA,CAAA,EAAG;IACP,OAAOH,MAAM,CAAC1F,IAAI,CAAC,IAAI,CAACI,UAAU,CAAC,CAAC0F,MAAM,CACxC,CAAC1F,UAAU,EAAEG,IAAI,MAAMH,UAAU,CAACG,IAAI,CAAC,GAAG,IAAI,CAACH,UAAU,CAACG,IAAI,CAAC,CAACE,KAAK,CAAC,IAAI,CAACiC,OAAO,CAAC,EAAEtC,UAAU,CAAC,EAChG,CAAC,CACH,CAAC;EACH;AACF;AACA,MAAM2F,iBAAiB,GAAGb,eAAe;EAAEc,WAAW,GAAG,MAAMA,WAAW,CAAC;IACzEvG,WAAWA,CAACuC,MAAM,EAAEU,OAAO,EAAE;MAC3B,IAAI,CAACV,MAAM,GAAGA,MAAM,EAAE,IAAI,CAACU,OAAO,GAAGA,OAAO,EAAEsB,QAAQ,CAACrC,KAAK,CAACc,GAAG,CAACC,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,CAACuD,IAAI,GAAG,IAAI,EAAE,IAAI,CAACC,IAAI,GAAG,IAAI;IACrH;IACA,OAAOnE,MAAMA,CAACoE,QAAQ,EAAE;MACtB,IAAI,IAAI,CAACjD,OAAO,IAAI,IAAI,EACtB,MAAM,IAAIhC,cAAc,CAAC,iCAAiC,CAAC;MAC7D,IAAIjB,IAAI,EAAEQ,KAAK;MACf,OAAOP,KAAK,CAACyD,OAAO,CAAC,IAAI,CAACT,OAAO,CAAC,IAAI,OAAOiD,QAAQ,IAAI,QAAQ,IAAI1F,KAAK,GAAG0F,QAAQ,CAACvC,WAAW,CAAC,CAAC,EAAEwC,QAAQ,CAAC3F,KAAK,EAAE,EAAE,CAAC,CAAC4F,QAAQ,CAAC,CAAC,KAAK5F,KAAK,KAAKA,KAAK,GAAG2F,QAAQ,CAAC3F,KAAK,EAAE,EAAE,CAAC,CAAC,IAAI,OAAO0F,QAAQ,IAAI,QAAQ,KAAK1F,KAAK,GAAG0F,QAAQ,CAAC,EAAE,OAAO1F,KAAK,IAAI,QAAQ,GAAGR,IAAI,GAAGqG,QAAQ,CAACC,aAAa,CAAC,IAAI,CAACrD,OAAO,CAACzC,KAAK,GAAG,CAAC,CAAC,CAAC,GAAGA,KAAK,IAAI,IAAI,CAACyC,OAAO,CAACrC,OAAO,CAACJ,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGR,IAAI,GAAGqG,QAAQ,CAACC,aAAa,CAAC9F,KAAK,CAAC,GAAGR,IAAI,GAAGqG,QAAQ,CAACC,aAAa,CAAC,IAAI,CAACrD,OAAO,CAAC,CAAC,CAAC,CAAC,IAAIjD,IAAI,GAAGqG,QAAQ,CAACC,aAAa,CAAC,IAAI,CAACrD,OAAO,CAAC,EAAE,IAAI,CAACQ,SAAS,IAAIzD,IAAI,CAACsE,SAAS,CAAC/D,GAAG,CAAC,IAAI,CAACkD,SAAS,CAAC,EAAEzD,IAAI;IACviB;IACA;IACA,IAAIuG,OAAOA,CAAA,EAAG;MACZ,OAAO,IAAI,CAAC/G,WAAW;IACzB;IACAgH,MAAMA,CAAA,EAAG,CACT;IACAC,KAAKA,CAAA,EAAG;MACN,MAAMhE,OAAO,GAAG,IAAI,CAACA,OAAO,CAACiE,SAAS,CAAC,CAAC,CAAC,CAAC;MAC1C,OAAO,IAAI,CAAC3E,MAAM,CAACD,MAAM,CAACW,OAAO,CAAC;IACpC;IACAkE,MAAMA,CAAA,EAAG;MACP,IAAI,CAACC,MAAM,IAAI,IAAI,IAAI,IAAI,CAACA,MAAM,CAACC,WAAW,CAAC,IAAI,CAAC,EAAE9C,QAAQ,CAACrC,KAAK,CAACoF,MAAM,CAAC,IAAI,CAACrE,OAAO,CAAC;IAC3F;IACAsE,QAAQA,CAACC,KAAK,EAAEzC,MAAM,EAAE;MACtB,IAAI,CAAC0C,OAAO,CAACD,KAAK,EAAEzC,MAAM,CAAC,CAACzD,MAAM,CAAC,CAAC;IACtC;IACAoG,QAAQA,CAACF,KAAK,EAAEzC,MAAM,EAAEjE,IAAI,EAAEE,KAAK,EAAE;MACnC,MAAM+B,IAAI,GAAG,IAAI,CAAC0E,OAAO,CAACD,KAAK,EAAEzC,MAAM,CAAC;MACxC,IAAI,IAAI,CAACxC,MAAM,CAACG,KAAK,CAAC5B,IAAI,EAAE5B,KAAK,CAACK,IAAI,CAAC,IAAI,IAAI,IAAIyB,KAAK,EACtD+B,IAAI,CAAC4E,IAAI,CAAC7G,IAAI,EAAEE,KAAK,CAAC,CAAC,KACpB,IAAI,IAAI,CAACuB,MAAM,CAACG,KAAK,CAAC5B,IAAI,EAAE5B,KAAK,CAACI,SAAS,CAAC,IAAI,IAAI,EAAE;QACzD,MAAM8H,MAAM,GAAG,IAAI,CAAC7E,MAAM,CAACD,MAAM,CAAC,IAAI,CAACyE,OAAO,CAAC1G,KAAK,CAAC;QACrD0C,IAAI,CAAC4E,IAAI,CAACP,MAAM,CAAC,EAAEA,MAAM,CAAClB,MAAM,CAACpF,IAAI,EAAEE,KAAK,CAAC;MAC/C;IACF;IACA4G,QAAQA,CAACJ,KAAK,EAAExG,KAAK,EAAE6G,GAAG,EAAE;MAC1B,MAAM9E,IAAI,GAAG8E,GAAG,IAAI,IAAI,GAAG,IAAI,CAACtF,MAAM,CAACD,MAAM,CAAC,MAAM,EAAEtB,KAAK,CAAC,GAAG,IAAI,CAACuB,MAAM,CAACD,MAAM,CAACtB,KAAK,EAAE6G,GAAG,CAAC;QAAEC,GAAG,GAAG,IAAI,CAACvE,KAAK,CAACiE,KAAK,CAAC;MACtH,IAAI,CAACJ,MAAM,CAACW,YAAY,CAAChF,IAAI,EAAE+E,GAAG,IAAI,KAAK,CAAC,CAAC;IAC/C;IACAL,OAAOA,CAACD,KAAK,EAAEzC,MAAM,EAAE;MACrB,MAAMiB,MAAM,GAAG,IAAI,CAACzC,KAAK,CAACiE,KAAK,CAAC;MAChC,IAAIxB,MAAM,IAAI,IAAI,EAChB,MAAM,IAAItE,KAAK,CAAC,2BAA2B,CAAC;MAC9C,OAAOsE,MAAM,CAACzC,KAAK,CAACwB,MAAM,CAAC,EAAEiB,MAAM;IACrC;IACAjB,MAAMA,CAAA,EAAG;MACP,OAAO,CAAC;IACV;IACAiD,MAAMA,CAACC,IAAI,GAAG,IAAI,CAACb,MAAM,EAAE;MACzB,OAAO,IAAI,CAACA,MAAM,IAAI,IAAI,IAAI,IAAI,KAAKa,IAAI,GAAG,CAAC,GAAG,IAAI,CAACb,MAAM,CAACc,QAAQ,CAACF,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAACZ,MAAM,CAACY,MAAM,CAACC,IAAI,CAAC;IAChH;IACAE,QAAQA,CAACC,QAAQ,EAAE;MACjB,IAAI,CAACrB,OAAO,CAACsB,iBAAiB,IAAI,EAAE,IAAI,CAACjB,MAAM,YAAY,IAAI,CAACL,OAAO,CAACsB,iBAAiB,CAAC,IAAI,IAAI,CAACV,IAAI,CAAC,IAAI,CAACZ,OAAO,CAACsB,iBAAiB,CAACtE,QAAQ,CAAC;IAClJ;IACAzC,MAAMA,CAAA,EAAG;MACP,IAAI,CAAC2B,OAAO,CAACZ,UAAU,IAAI,IAAI,IAAI,IAAI,CAACY,OAAO,CAACZ,UAAU,CAACgF,WAAW,CAAC,IAAI,CAACpE,OAAO,CAAC,EAAE,IAAI,CAACkE,MAAM,CAAC,CAAC;IACrG;IACAmB,WAAWA,CAACxH,IAAI,EAAEE,KAAK,EAAE;MACvB,MAAMuH,WAAW,GAAG,OAAOzH,IAAI,IAAI,QAAQ,GAAG,IAAI,CAACyB,MAAM,CAACD,MAAM,CAACxB,IAAI,EAAEE,KAAK,CAAC,GAAGF,IAAI;MACpF,OAAO,IAAI,CAACsG,MAAM,IAAI,IAAI,KAAK,IAAI,CAACA,MAAM,CAACW,YAAY,CAACQ,WAAW,EAAE,IAAI,CAAC9B,IAAI,IAAI,KAAK,CAAC,CAAC,EAAE,IAAI,CAACnF,MAAM,CAAC,CAAC,CAAC,EAAEiH,WAAW;IACxH;IACAhF,KAAKA,CAACiE,KAAK,EAAEgB,MAAM,EAAE;MACnB,OAAOhB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,CAACf,IAAI;IACvC;IACAgC,MAAMA,CAACC,UAAU,EAAEN,QAAQ,EAAE,CAC7B;IACAT,IAAIA,CAAC7G,IAAI,EAAEE,KAAK,EAAE;MAChB,MAAM2H,OAAO,GAAG,OAAO7H,IAAI,IAAI,QAAQ,GAAG,IAAI,CAACyB,MAAM,CAACD,MAAM,CAACxB,IAAI,EAAEE,KAAK,CAAC,GAAGF,IAAI;MAChF,IAAI,IAAI,CAACsG,MAAM,IAAI,IAAI,IAAI,IAAI,CAACA,MAAM,CAACW,YAAY,CAACY,OAAO,EAAE,IAAI,CAAClC,IAAI,IAAI,KAAK,CAAC,CAAC,EAAE,OAAOkC,OAAO,CAACC,WAAW,IAAI,UAAU,EACzH,MAAM,IAAInH,cAAc,CAAE,eAAcX,IAAK,EAAC,CAAC;MACjD,OAAO6H,OAAO,CAACC,WAAW,CAAC,IAAI,CAAC,EAAED,OAAO;IAC3C;EACF,CAAC;AACDpC,WAAW,CAACxC,QAAQ,GAAG,UAAU;AACjC,IAAI8E,UAAU,GAAGtC,WAAW;AAC5B,MAAMuC,SAAS,GAAG,MAAMA,SAAS,SAASD,UAAU,CAAC;EACnD;AACF;AACA;AACA;AACA;EACE,OAAO7H,KAAKA,CAAC+H,QAAQ,EAAE;IACrB,OAAO,CAAC,CAAC;EACX;EACA;AACF;AACA;AACA;EACEvB,KAAKA,CAAChH,IAAI,EAAEwH,MAAM,EAAE;IAClB,OAAO,IAAI,CAAC/E,OAAO,KAAKzC,IAAI,IAAI,IAAI,CAACyC,OAAO,CAAC+F,uBAAuB,CAACxI,IAAI,CAAC,GAAGoC,IAAI,CAACqG,8BAA8B,GAAGC,IAAI,CAACC,GAAG,CAACnB,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;EAC7I;EACA;AACF;AACA;AACA;EACEoB,QAAQA,CAAC5B,KAAK,EAAE6B,UAAU,EAAE;IAC1B,IAAIrB,MAAM,GAAGvH,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC0G,MAAM,CAACnE,OAAO,CAACqG,UAAU,CAAC,CAAClI,OAAO,CAAC,IAAI,CAAC6B,OAAO,CAAC;IAC7E,OAAOuE,KAAK,GAAG,CAAC,KAAKQ,MAAM,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,CAACZ,MAAM,CAACnE,OAAO,EAAE+E,MAAM,CAAC;EAClE;EACA;AACF;AACA;AACA;AACA;EACEhH,KAAKA,CAAA,EAAG;IACN,OAAO;MACL,CAAC,IAAI,CAAC+F,OAAO,CAAChD,QAAQ,GAAG,IAAI,CAACgD,OAAO,CAAC/F,KAAK,CAAC,IAAI,CAACiC,OAAO,CAAC,IAAI,CAAC;IAChE,CAAC;EACH;AACF,CAAC;AACD6F,SAAS,CAACzI,KAAK,GAAGnB,KAAK,CAACS,WAAW;AACnC,IAAI4J,QAAQ,GAAGT,SAAS;AACxB,MAAMU,UAAU,GAAGD,QAAQ;AAC3B,MAAME,UAAU,CAAC;EACfzJ,WAAWA,CAAA,EAAG;IACZ,IAAI,CAAC0J,IAAI,GAAG,IAAI,EAAE,IAAI,CAACC,IAAI,GAAG,IAAI,EAAE,IAAI,CAAC5E,MAAM,GAAG,CAAC;EACrD;EACA6E,MAAMA,CAAC,GAAGC,KAAK,EAAE;IACf,IAAI,IAAI,CAAC9B,YAAY,CAAC8B,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,EAAEA,KAAK,CAAC9E,MAAM,GAAG,CAAC,EAAE;MACvD,MAAMI,IAAI,GAAG0E,KAAK,CAACjF,KAAK,CAAC,CAAC,CAAC;MAC3B,IAAI,CAACgF,MAAM,CAAC,GAAGzE,IAAI,CAAC;IACtB;EACF;EACA2E,EAAEA,CAACtC,KAAK,EAAE;IACR,MAAMf,IAAI,GAAG,IAAI,CAACsD,QAAQ,CAAC,CAAC;IAC5B,IAAIC,GAAG,GAAGvD,IAAI,CAAC,CAAC;IAChB,OAAOuD,GAAG,IAAIxC,KAAK,GAAG,CAAC,GACrBA,KAAK,IAAI,CAAC,EAAEwC,GAAG,GAAGvD,IAAI,CAAC,CAAC;IAC1B,OAAOuD,GAAG;EACZ;EACAC,QAAQA,CAACzJ,IAAI,EAAE;IACb,MAAMiG,IAAI,GAAG,IAAI,CAACsD,QAAQ,CAAC,CAAC;IAC5B,IAAIC,GAAG,GAAGvD,IAAI,CAAC,CAAC;IAChB,OAAOuD,GAAG,GAAI;MACZ,IAAIA,GAAG,KAAKxJ,IAAI,EACd,OAAO,CAAC,CAAC;MACXwJ,GAAG,GAAGvD,IAAI,CAAC,CAAC;IACd;IACA,OAAO,CAAC,CAAC;EACX;EACArF,OAAOA,CAACZ,IAAI,EAAE;IACZ,MAAMiG,IAAI,GAAG,IAAI,CAACsD,QAAQ,CAAC,CAAC;IAC5B,IAAIC,GAAG,GAAGvD,IAAI,CAAC,CAAC;MAAEe,KAAK,GAAG,CAAC;IAC3B,OAAOwC,GAAG,GAAI;MACZ,IAAIA,GAAG,KAAKxJ,IAAI,EACd,OAAOgH,KAAK;MACdA,KAAK,IAAI,CAAC,EAAEwC,GAAG,GAAGvD,IAAI,CAAC,CAAC;IAC1B;IACA,OAAO,CAAC,CAAC;EACX;EACAsB,YAAYA,CAACvH,IAAI,EAAE0J,OAAO,EAAE;IAC1B1J,IAAI,IAAI,IAAI,KAAK,IAAI,CAACc,MAAM,CAACd,IAAI,CAAC,EAAEA,IAAI,CAACiG,IAAI,GAAGyD,OAAO,EAAEA,OAAO,IAAI,IAAI,IAAI1J,IAAI,CAACgG,IAAI,GAAG0D,OAAO,CAAC1D,IAAI,EAAE0D,OAAO,CAAC1D,IAAI,IAAI,IAAI,KAAK0D,OAAO,CAAC1D,IAAI,CAACC,IAAI,GAAGjG,IAAI,CAAC,EAAE0J,OAAO,CAAC1D,IAAI,GAAGhG,IAAI,EAAE0J,OAAO,KAAK,IAAI,CAACR,IAAI,KAAK,IAAI,CAACA,IAAI,GAAGlJ,IAAI,CAAC,IAAI,IAAI,CAACmJ,IAAI,IAAI,IAAI,IAAI,IAAI,CAACA,IAAI,CAAClD,IAAI,GAAGjG,IAAI,EAAEA,IAAI,CAACgG,IAAI,GAAG,IAAI,CAACmD,IAAI,EAAE,IAAI,CAACA,IAAI,GAAGnJ,IAAI,KAAKA,IAAI,CAACgG,IAAI,GAAG,IAAI,EAAE,IAAI,CAACkD,IAAI,GAAG,IAAI,CAACC,IAAI,GAAGnJ,IAAI,CAAC,EAAE,IAAI,CAACuE,MAAM,IAAI,CAAC,CAAC;EAC1X;EACAiD,MAAMA,CAAChC,MAAM,EAAE;IACb,IAAIwB,KAAK,GAAG,CAAC;MAAEwC,GAAG,GAAG,IAAI,CAACN,IAAI;IAC9B,OAAOM,GAAG,IAAI,IAAI,GAAI;MACpB,IAAIA,GAAG,KAAKhE,MAAM,EAChB,OAAOwB,KAAK;MACdA,KAAK,IAAIwC,GAAG,CAACjF,MAAM,CAAC,CAAC,EAAEiF,GAAG,GAAGA,GAAG,CAACvD,IAAI;IACvC;IACA,OAAO,CAAC,CAAC;EACX;EACAnF,MAAMA,CAACd,IAAI,EAAE;IACX,IAAI,CAACyJ,QAAQ,CAACzJ,IAAI,CAAC,KAAKA,IAAI,CAACgG,IAAI,IAAI,IAAI,KAAKhG,IAAI,CAACgG,IAAI,CAACC,IAAI,GAAGjG,IAAI,CAACiG,IAAI,CAAC,EAAEjG,IAAI,CAACiG,IAAI,IAAI,IAAI,KAAKjG,IAAI,CAACiG,IAAI,CAACD,IAAI,GAAGhG,IAAI,CAACgG,IAAI,CAAC,EAAEhG,IAAI,KAAK,IAAI,CAACkJ,IAAI,KAAK,IAAI,CAACA,IAAI,GAAGlJ,IAAI,CAACiG,IAAI,CAAC,EAAEjG,IAAI,KAAK,IAAI,CAACmJ,IAAI,KAAK,IAAI,CAACA,IAAI,GAAGnJ,IAAI,CAACgG,IAAI,CAAC,EAAE,IAAI,CAACzB,MAAM,IAAI,CAAC,CAAC;EAC/O;EACAgF,QAAQA,CAACI,OAAO,GAAG,IAAI,CAACT,IAAI,EAAE;IAC5B,OAAO,MAAM;MACX,MAAMU,GAAG,GAAGD,OAAO;MACnB,OAAOA,OAAO,IAAI,IAAI,KAAKA,OAAO,GAAGA,OAAO,CAAC1D,IAAI,CAAC,EAAE2D,GAAG;IACzD,CAAC;EACH;EACApI,IAAIA,CAACwF,KAAK,EAAE6C,SAAS,GAAG,CAAC,CAAC,EAAE;IAC1B,MAAM5D,IAAI,GAAG,IAAI,CAACsD,QAAQ,CAAC,CAAC;IAC5B,IAAIC,GAAG,GAAGvD,IAAI,CAAC,CAAC;IAChB,OAAOuD,GAAG,GAAI;MACZ,MAAMjF,MAAM,GAAGiF,GAAG,CAACjF,MAAM,CAAC,CAAC;MAC3B,IAAIyC,KAAK,GAAGzC,MAAM,IAAIsF,SAAS,IAAI7C,KAAK,KAAKzC,MAAM,KAAKiF,GAAG,CAACvD,IAAI,IAAI,IAAI,IAAIuD,GAAG,CAACvD,IAAI,CAAC1B,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,EAClG,OAAO,CAACiF,GAAG,EAAExC,KAAK,CAAC;MACrBA,KAAK,IAAIzC,MAAM,EAAEiF,GAAG,GAAGvD,IAAI,CAAC,CAAC;IAC/B;IACA,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;EAClB;EACArC,OAAOA,CAACkG,QAAQ,EAAE;IAChB,MAAM7D,IAAI,GAAG,IAAI,CAACsD,QAAQ,CAAC,CAAC;IAC5B,IAAIC,GAAG,GAAGvD,IAAI,CAAC,CAAC;IAChB,OAAOuD,GAAG,GACRM,QAAQ,CAACN,GAAG,CAAC,EAAEA,GAAG,GAAGvD,IAAI,CAAC,CAAC;EAC/B;EACA8D,SAASA,CAAC/C,KAAK,EAAEzC,MAAM,EAAEuF,QAAQ,EAAE;IACjC,IAAIvF,MAAM,IAAI,CAAC,EACb;IACF,MAAM,CAACyF,SAAS,EAAExC,MAAM,CAAC,GAAG,IAAI,CAAChG,IAAI,CAACwF,KAAK,CAAC;IAC5C,IAAIiD,QAAQ,GAAGjD,KAAK,GAAGQ,MAAM;IAC7B,MAAMvB,IAAI,GAAG,IAAI,CAACsD,QAAQ,CAACS,SAAS,CAAC;IACrC,IAAIR,GAAG,GAAGvD,IAAI,CAAC,CAAC;IAChB,OAAOuD,GAAG,IAAIS,QAAQ,GAAGjD,KAAK,GAAGzC,MAAM,GAAI;MACzC,MAAM2F,SAAS,GAAGV,GAAG,CAACjF,MAAM,CAAC,CAAC;MAC9ByC,KAAK,GAAGiD,QAAQ,GAAGH,QAAQ,CACzBN,GAAG,EACHxC,KAAK,GAAGiD,QAAQ,EAChBvB,IAAI,CAACC,GAAG,CAACpE,MAAM,EAAE0F,QAAQ,GAAGC,SAAS,GAAGlD,KAAK,CAC/C,CAAC,GAAG8C,QAAQ,CAACN,GAAG,EAAE,CAAC,EAAEd,IAAI,CAACC,GAAG,CAACuB,SAAS,EAAElD,KAAK,GAAGzC,MAAM,GAAG0F,QAAQ,CAAC,CAAC,EAAEA,QAAQ,IAAIC,SAAS,EAAEV,GAAG,GAAGvD,IAAI,CAAC,CAAC;IAC3G;EACF;EACA7F,GAAGA,CAAC0J,QAAQ,EAAE;IACZ,OAAO,IAAI,CAACjE,MAAM,CAAC,CAACsE,IAAI,EAAEX,GAAG,MAAMW,IAAI,CAACC,IAAI,CAACN,QAAQ,CAACN,GAAG,CAAC,CAAC,EAAEW,IAAI,CAAC,EAAE,EAAE,CAAC;EACzE;EACAtE,MAAMA,CAACiE,QAAQ,EAAEK,IAAI,EAAE;IACrB,MAAMlE,IAAI,GAAG,IAAI,CAACsD,QAAQ,CAAC,CAAC;IAC5B,IAAIC,GAAG,GAAGvD,IAAI,CAAC,CAAC;IAChB,OAAOuD,GAAG,GACRW,IAAI,GAAGL,QAAQ,CAACK,IAAI,EAAEX,GAAG,CAAC,EAAEA,GAAG,GAAGvD,IAAI,CAAC,CAAC;IAC1C,OAAOkE,IAAI;EACb;AACF;AACA,SAASE,gBAAgBA,CAACrK,IAAI,EAAE+B,MAAM,EAAE;EACtC,MAAMuI,KAAK,GAAGvI,MAAM,CAACP,IAAI,CAACxB,IAAI,CAAC;EAC/B,IAAIsK,KAAK,EACP,OAAOA,KAAK;EACd,IAAI;IACF,OAAOvI,MAAM,CAACD,MAAM,CAAC9B,IAAI,CAAC;EAC5B,CAAC,CAAC,MAAM;IACN,MAAMuC,IAAI,GAAGR,MAAM,CAACD,MAAM,CAACpD,KAAK,CAACM,MAAM,CAAC;IACxC,OAAOiB,KAAK,CAACC,IAAI,CAACF,IAAI,CAAC8I,UAAU,CAAC,CAAClF,OAAO,CAAE2G,KAAK,IAAK;MACpDhI,IAAI,CAACE,OAAO,CAAC2F,WAAW,CAACmC,KAAK,CAAC;IACjC,CAAC,CAAC,EAAEvK,IAAI,CAAC6B,UAAU,IAAI7B,IAAI,CAAC6B,UAAU,CAAC2I,YAAY,CAACjI,IAAI,CAACE,OAAO,EAAEzC,IAAI,CAAC,EAAEuC,IAAI,CAACiE,MAAM,CAAC,CAAC,EAAEjE,IAAI;EAC9F;AACF;AACA,MAAMkI,WAAW,GAAG,MAAMA,WAAW,SAASpC,UAAU,CAAC;EACvD7I,WAAWA,CAACuC,MAAM,EAAEU,OAAO,EAAE;IAC3B,KAAK,CAACV,MAAM,EAAEU,OAAO,CAAC,EAAE,IAAI,CAACiI,MAAM,GAAG,IAAI,EAAE,IAAI,CAACxF,KAAK,CAAC,CAAC;EAC1D;EACAkD,WAAWA,CAACuC,KAAK,EAAE;IACjB,IAAI,CAACpD,YAAY,CAACoD,KAAK,CAAC;EAC1B;EACAnE,MAAMA,CAAA,EAAG;IACP,KAAK,CAACA,MAAM,CAAC,CAAC,EAAE,IAAI,CAACkB,QAAQ,CAAC9D,OAAO,CAAE2G,KAAK,IAAK;MAC/CA,KAAK,CAAC/D,MAAM,CAAC,CAAC;IAChB,CAAC,CAAC;EACJ;EACAoE,QAAQA,CAAC5K,IAAI,EAAE;IACb,IAAI,CAAC0K,MAAM,IAAI,IAAI,IAAI,IAAI,CAACA,MAAM,CAAC5J,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC4J,MAAM,GAAG1K,IAAI,EAAEyK,WAAW,CAACI,OAAO,IAAI,IAAI,CAACH,MAAM,CAACpG,SAAS,CAAC/D,GAAG,CAACkK,WAAW,CAACI,OAAO,CAAC,EAAE,IAAI,CAACH,MAAM,CAAChK,YAAY,CAAC,iBAAiB,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC+B,OAAO,CAAC8E,YAAY,CAAC,IAAI,CAACmD,MAAM,EAAE,IAAI,CAACjI,OAAO,CAACqI,UAAU,CAAC;EAC/P;EACA;AACF;AACA;EACE5F,KAAKA,CAAA,EAAG;IACN,IAAI,CAACwC,QAAQ,GAAG,IAAIuB,UAAU,CAAC,CAAC,EAAEhJ,KAAK,CAACC,IAAI,CAAC,IAAI,CAACuC,OAAO,CAACqG,UAAU,CAAC,CAAC5E,MAAM,CAAElE,IAAI,IAAKA,IAAI,KAAK,IAAI,CAAC0K,MAAM,CAAC,CAACK,OAAO,CAAC,CAAC,CAACnH,OAAO,CAAE5D,IAAI,IAAK;MACvI,IAAI;QACF,MAAMuK,KAAK,GAAGF,gBAAgB,CAACrK,IAAI,EAAE,IAAI,CAAC+B,MAAM,CAAC;QACjD,IAAI,CAACwF,YAAY,CAACgD,KAAK,EAAE,IAAI,CAAC7C,QAAQ,CAACwB,IAAI,IAAI,KAAK,CAAC,CAAC;MACxD,CAAC,CAAC,OAAO8B,GAAG,EAAE;QACZ,IAAIA,GAAG,YAAY/J,cAAc,EAC/B;QACF,MAAM+J,GAAG;MACX;IACF,CAAC,CAAC;EACJ;EACAjE,QAAQA,CAACC,KAAK,EAAEzC,MAAM,EAAE;IACtB,IAAIyC,KAAK,KAAK,CAAC,IAAIzC,MAAM,KAAK,IAAI,CAACA,MAAM,CAAC,CAAC,EACzC,OAAO,IAAI,CAACzD,MAAM,CAAC,CAAC;IACtB,IAAI,CAAC4G,QAAQ,CAACqC,SAAS,CAAC/C,KAAK,EAAEzC,MAAM,EAAE,CAACgG,KAAK,EAAE/C,MAAM,EAAEyD,WAAW,KAAK;MACrEV,KAAK,CAACxD,QAAQ,CAACS,MAAM,EAAEyD,WAAW,CAAC;IACrC,CAAC,CAAC;EACJ;EACAC,UAAUA,CAACC,QAAQ,EAAEnE,KAAK,GAAG,CAAC,EAAE;IAC9B,MAAM,CAACuD,KAAK,EAAE/C,MAAM,CAAC,GAAG,IAAI,CAACE,QAAQ,CAAClG,IAAI,CAACwF,KAAK,CAAC;IACjD,OAAOmE,QAAQ,CAAC5H,QAAQ,IAAI,IAAI,IAAI4H,QAAQ,CAACZ,KAAK,CAAC,IAAIY,QAAQ,CAAC5H,QAAQ,IAAI,IAAI,IAAIgH,KAAK,YAAYY,QAAQ,GAAG,CAACZ,KAAK,EAAE/C,MAAM,CAAC,GAAG+C,KAAK,YAAYE,WAAW,GAAGF,KAAK,CAACW,UAAU,CAACC,QAAQ,EAAE3D,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;EAClN;EACA4D,WAAWA,CAACD,QAAQ,EAAEnE,KAAK,GAAG,CAAC,EAAEzC,MAAM,GAAG8G,MAAM,CAACC,SAAS,EAAE;IAC1D,IAAIF,WAAW,GAAG,EAAE;MAAEG,UAAU,GAAGhH,MAAM;IACzC,OAAO,IAAI,CAACmD,QAAQ,CAACqC,SAAS,CAC5B/C,KAAK,EACLzC,MAAM,EACN,CAACgG,KAAK,EAAEiB,UAAU,EAAEP,WAAW,KAAK;MAClC,CAACE,QAAQ,CAAC5H,QAAQ,IAAI,IAAI,IAAI4H,QAAQ,CAACZ,KAAK,CAAC,IAAIY,QAAQ,CAAC5H,QAAQ,IAAI,IAAI,IAAIgH,KAAK,YAAYY,QAAQ,KAAKC,WAAW,CAAChB,IAAI,CAACG,KAAK,CAAC,EAAEA,KAAK,YAAYE,WAAW,KAAKW,WAAW,GAAGA,WAAW,CAAC/F,MAAM,CACpMkF,KAAK,CAACa,WAAW,CAACD,QAAQ,EAAEK,UAAU,EAAED,UAAU,CACpD,CAAC,CAAC,EAAEA,UAAU,IAAIN,WAAW;IAC/B,CACF,CAAC,EAAEG,WAAW;EAChB;EACAzE,MAAMA,CAAA,EAAG;IACP,IAAI,CAACe,QAAQ,CAAC9D,OAAO,CAAE2G,KAAK,IAAK;MAC/BA,KAAK,CAAC5D,MAAM,CAAC,CAAC;IAChB,CAAC,CAAC,EAAE,KAAK,CAACA,MAAM,CAAC,CAAC;EACpB;EACA8E,sBAAsBA,CAAA,EAAG;IACvB,IAAIC,IAAI,GAAG,CAAC,CAAC;IACb,IAAI,CAAChE,QAAQ,CAAC9D,OAAO,CAAE2G,KAAK,IAAK;MAC/BmB,IAAI,IAAI,IAAI,CAACnF,OAAO,CAACoF,eAAe,CAAC3I,IAAI,CACtCqE,GAAG,IAAKkD,KAAK,YAAYlD,GAC5B,CAAC,KAAKkD,KAAK,CAAChE,OAAO,CAAC1G,KAAK,KAAKnB,KAAK,CAACQ,UAAU,IAAIqL,KAAK,CAACtE,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC2F,UAAU,CAACrB,KAAK,CAAC,EAAEA,KAAK,CAACvE,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC4F,UAAU,CAACrB,KAAK,CAACvE,IAAI,CAAC,EAAEuE,KAAK,CAAC3D,MAAM,CAACiF,MAAM,CAAC,CAAC,EAAEH,IAAI,GAAG,CAAC,CAAC,IAAInB,KAAK,YAAYE,WAAW,GAAGF,KAAK,CAACsB,MAAM,CAAC,CAAC,GAAGtB,KAAK,CAACzJ,MAAM,CAAC,CAAC,CAAC;IACxP,CAAC,CAAC;EACJ;EACAoG,QAAQA,CAACF,KAAK,EAAEzC,MAAM,EAAEjE,IAAI,EAAEE,KAAK,EAAE;IACnC,IAAI,CAACkH,QAAQ,CAACqC,SAAS,CAAC/C,KAAK,EAAEzC,MAAM,EAAE,CAACgG,KAAK,EAAE/C,MAAM,EAAEyD,WAAW,KAAK;MACrEV,KAAK,CAACrD,QAAQ,CAACM,MAAM,EAAEyD,WAAW,EAAE3K,IAAI,EAAEE,KAAK,CAAC;IAClD,CAAC,CAAC;EACJ;EACA4G,QAAQA,CAACJ,KAAK,EAAExG,KAAK,EAAE6G,GAAG,EAAE;IAC1B,MAAM,CAACkD,KAAK,EAAE/C,MAAM,CAAC,GAAG,IAAI,CAACE,QAAQ,CAAClG,IAAI,CAACwF,KAAK,CAAC;IACjD,IAAIuD,KAAK,EACPA,KAAK,CAACnD,QAAQ,CAACI,MAAM,EAAEhH,KAAK,EAAE6G,GAAG,CAAC,CAAC,KAChC;MACH,MAAM9E,IAAI,GAAG8E,GAAG,IAAI,IAAI,GAAG,IAAI,CAACtF,MAAM,CAACD,MAAM,CAAC,MAAM,EAAEtB,KAAK,CAAC,GAAG,IAAI,CAACuB,MAAM,CAACD,MAAM,CAACtB,KAAK,EAAE6G,GAAG,CAAC;MAC7F,IAAI,CAACe,WAAW,CAAC7F,IAAI,CAAC;IACxB;EACF;EACAgF,YAAYA,CAACuE,SAAS,EAAEC,OAAO,EAAE;IAC/BD,SAAS,CAAClF,MAAM,IAAI,IAAI,IAAIkF,SAAS,CAAClF,MAAM,CAACc,QAAQ,CAAC5G,MAAM,CAACgL,SAAS,CAAC;IACvE,IAAIE,UAAU,GAAG,IAAI;IACrB,IAAI,CAACtE,QAAQ,CAACH,YAAY,CAACuE,SAAS,EAAEC,OAAO,IAAI,IAAI,CAAC,EAAED,SAAS,CAAClF,MAAM,GAAG,IAAI,EAAEmF,OAAO,IAAI,IAAI,KAAKC,UAAU,GAAGD,OAAO,CAACtJ,OAAO,CAAC,EAAE,CAAC,IAAI,CAACA,OAAO,CAACZ,UAAU,KAAKiK,SAAS,CAACrJ,OAAO,IAAI,IAAI,CAACA,OAAO,CAACwJ,WAAW,KAAKD,UAAU,KAAK,IAAI,CAACvJ,OAAO,CAAC8E,YAAY,CAACuE,SAAS,CAACrJ,OAAO,EAAEuJ,UAAU,CAAC,EAAEF,SAAS,CAACtF,MAAM,CAAC,CAAC;EAChT;EACAjC,MAAMA,CAAA,EAAG;IACP,OAAO,IAAI,CAACmD,QAAQ,CAAC7B,MAAM,CAAC,CAACsE,IAAI,EAAEI,KAAK,KAAKJ,IAAI,GAAGI,KAAK,CAAChG,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;EACxE;EACA2H,YAAYA,CAACC,YAAY,EAAEzC,OAAO,EAAE;IAClC,IAAI,CAAChC,QAAQ,CAAC9D,OAAO,CAAE2G,KAAK,IAAK;MAC/B4B,YAAY,CAAC5E,YAAY,CAACgD,KAAK,EAAEb,OAAO,CAAC;IAC3C,CAAC,CAAC;EACJ;EACA/B,QAAQA,CAACyE,OAAO,EAAE;IAChB,IAAI,KAAK,CAACzE,QAAQ,CAACyE,OAAO,CAAC,EAAE,IAAI,CAACX,sBAAsB,CAAC,CAAC,EAAE,IAAI,CAACf,MAAM,IAAI,IAAI,IAAI,IAAI,CAACA,MAAM,KAAK,IAAI,CAACjI,OAAO,CAACqI,UAAU,IAAI,IAAI,CAACrI,OAAO,CAAC8E,YAAY,CAAC,IAAI,CAACmD,MAAM,EAAE,IAAI,CAACjI,OAAO,CAACqI,UAAU,CAAC,EAAE,IAAI,CAACpD,QAAQ,CAACnD,MAAM,KAAK,CAAC,EACvN,IAAI,IAAI,CAACgC,OAAO,CAAC8F,YAAY,IAAI,IAAI,EAAE;MACrC,MAAM9B,KAAK,GAAG,IAAI,CAACxI,MAAM,CAACD,MAAM,CAAC,IAAI,CAACyE,OAAO,CAAC8F,YAAY,CAAC9I,QAAQ,CAAC;MACpE,IAAI,CAAC6E,WAAW,CAACmC,KAAK,CAAC;IACzB,CAAC,MACC,IAAI,CAACzJ,MAAM,CAAC,CAAC;EACnB;EACAwL,IAAIA,CAACtF,KAAK,EAAE6C,SAAS,GAAG,CAAC,CAAC,EAAE;IAC1B,MAAM,CAACU,KAAK,EAAE/C,MAAM,CAAC,GAAG,IAAI,CAACE,QAAQ,CAAClG,IAAI,CAACwF,KAAK,EAAE6C,SAAS,CAAC;MAAEjB,QAAQ,GAAG,CAAC,CAAC,IAAI,EAAE5B,KAAK,CAAC,CAAC;IACxF,OAAOuD,KAAK,YAAYE,WAAW,GAAG7B,QAAQ,CAACvD,MAAM,CAACkF,KAAK,CAAC+B,IAAI,CAAC9E,MAAM,EAAEqC,SAAS,CAAC,CAAC,IAAIU,KAAK,IAAI,IAAI,IAAI3B,QAAQ,CAACwB,IAAI,CAAC,CAACG,KAAK,EAAE/C,MAAM,CAAC,CAAC,EAAEoB,QAAQ,CAAC;EACpJ;EACA/B,WAAWA,CAAC0D,KAAK,EAAE;IACjB,IAAI,CAAC7C,QAAQ,CAAC5G,MAAM,CAACyJ,KAAK,CAAC;EAC7B;EACAzC,WAAWA,CAACxH,IAAI,EAAEE,KAAK,EAAE;IACvB,MAAMuH,WAAW,GAAG,OAAOzH,IAAI,IAAI,QAAQ,GAAG,IAAI,CAACyB,MAAM,CAACD,MAAM,CAACxB,IAAI,EAAEE,KAAK,CAAC,GAAGF,IAAI;IACpF,OAAOyH,WAAW,YAAY0C,WAAW,IAAI,IAAI,CAACyB,YAAY,CAACnE,WAAW,CAAC,EAAE,KAAK,CAACD,WAAW,CAACC,WAAW,CAAC;EAC7G;EACAhF,KAAKA,CAACiE,KAAK,EAAEuF,KAAK,GAAG,CAAC,CAAC,EAAE;IACvB,IAAI,CAACA,KAAK,EAAE;MACV,IAAIvF,KAAK,KAAK,CAAC,EACb,OAAO,IAAI;MACb,IAAIA,KAAK,KAAK,IAAI,CAACzC,MAAM,CAAC,CAAC,EACzB,OAAO,IAAI,CAAC0B,IAAI;IACpB;IACA,MAAMuG,KAAK,GAAG,IAAI,CAAC/F,KAAK,CAAC,CAAC;IAC1B,OAAO,IAAI,CAACG,MAAM,IAAI,IAAI,CAACA,MAAM,CAACW,YAAY,CAACiF,KAAK,EAAE,IAAI,CAACvG,IAAI,IAAI,KAAK,CAAC,CAAC,EAAE,IAAI,CAACyB,QAAQ,CAACqC,SAAS,CAAC/C,KAAK,EAAE,IAAI,CAACzC,MAAM,CAAC,CAAC,EAAE,CAACgG,KAAK,EAAE/C,MAAM,EAAEiF,OAAO,KAAK;MACpJ,MAAM1J,KAAK,GAAGwH,KAAK,CAACxH,KAAK,CAACyE,MAAM,EAAE+E,KAAK,CAAC;MACxCxJ,KAAK,IAAI,IAAI,IAAIyJ,KAAK,CAACpE,WAAW,CAACrF,KAAK,CAAC;IAC3C,CAAC,CAAC,EAAEyJ,KAAK;EACX;EACAZ,UAAUA,CAACrB,KAAK,EAAE;IAChB,MAAMiC,KAAK,GAAG,IAAI,CAAC/F,KAAK,CAAC,CAAC;IAC1B,OAAO8D,KAAK,CAACtE,IAAI,IAAI,IAAI,GACvBuG,KAAK,CAACpE,WAAW,CAACmC,KAAK,CAACtE,IAAI,CAAC;IAC/B,OAAO,IAAI,CAACW,MAAM,IAAI,IAAI,CAACA,MAAM,CAACW,YAAY,CAACiF,KAAK,EAAE,IAAI,CAACvG,IAAI,IAAI,KAAK,CAAC,CAAC,EAAEuG,KAAK;EACnF;EACAX,MAAMA,CAAA,EAAG;IACP,IAAI,CAACjF,MAAM,IAAI,IAAI,CAACsF,YAAY,CAAC,IAAI,CAACtF,MAAM,EAAE,IAAI,CAACX,IAAI,IAAI,KAAK,CAAC,CAAC,EAAE,IAAI,CAACnF,MAAM,CAAC,CAAC;EACnF;EACAmH,MAAMA,CAACyE,SAAS,EAAE9E,QAAQ,EAAE;IAC1B,MAAM+E,UAAU,GAAG,EAAE;MAAEC,YAAY,GAAG,EAAE;IACxCF,SAAS,CAAC9I,OAAO,CAAEiJ,QAAQ,IAAK;MAC9BA,QAAQ,CAACrH,MAAM,KAAK,IAAI,CAAC/C,OAAO,IAAIoK,QAAQ,CAACC,IAAI,KAAK,WAAW,KAAKH,UAAU,CAACvC,IAAI,CAAC,GAAGyC,QAAQ,CAACF,UAAU,CAAC,EAAEC,YAAY,CAACxC,IAAI,CAAC,GAAGyC,QAAQ,CAACD,YAAY,CAAC,CAAC;IAC7J,CAAC,CAAC,EAAEA,YAAY,CAAChJ,OAAO,CAAE5D,IAAI,IAAK;MACjC,IAAIA,IAAI,CAAC6B,UAAU,IAAI,IAAI;MAAI;MAC/B7B,IAAI,CAACiD,OAAO,KAAK,QAAQ,IAAIoD,QAAQ,CAAC0G,IAAI,CAACvE,uBAAuB,CAACxI,IAAI,CAAC,GAAGoC,IAAI,CAACqG,8BAA8B,EAC5G;MACF,MAAMlG,IAAI,GAAG,IAAI,CAACR,MAAM,CAACP,IAAI,CAACxB,IAAI,CAAC;MACnCuC,IAAI,IAAI,IAAI,KAAKA,IAAI,CAACE,OAAO,CAACZ,UAAU,IAAI,IAAI,IAAIU,IAAI,CAACE,OAAO,CAACZ,UAAU,KAAK,IAAI,CAACY,OAAO,CAAC,IAAIF,IAAI,CAACoE,MAAM,CAAC,CAAC;IAChH,CAAC,CAAC,EAAEgG,UAAU,CAACzI,MAAM,CAAElE,IAAI,IAAKA,IAAI,CAAC6B,UAAU,KAAK,IAAI,CAACY,OAAO,IAAIzC,IAAI,KAAK,IAAI,CAAC0K,MAAM,CAAC,CAACsC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,KAAKC,CAAC,GAAG,CAAC,GAAGD,CAAC,CAACzE,uBAAuB,CAAC0E,CAAC,CAAC,GAAG9K,IAAI,CAAC+K,2BAA2B,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAACvJ,OAAO,CAAE5D,IAAI,IAAK;MACjN,IAAI+L,OAAO,GAAG,IAAI;MAClB/L,IAAI,CAACiM,WAAW,IAAI,IAAI,KAAKF,OAAO,GAAG,IAAI,CAAChK,MAAM,CAACP,IAAI,CAACxB,IAAI,CAACiM,WAAW,CAAC,CAAC;MAC1E,MAAM1J,IAAI,GAAG8H,gBAAgB,CAACrK,IAAI,EAAE,IAAI,CAAC+B,MAAM,CAAC;MAChD,CAACQ,IAAI,CAAC0D,IAAI,KAAK8F,OAAO,IAAIxJ,IAAI,CAAC0D,IAAI,IAAI,IAAI,MAAM1D,IAAI,CAACqE,MAAM,IAAI,IAAI,IAAIrE,IAAI,CAACqE,MAAM,CAACC,WAAW,CAAC,IAAI,CAAC,EAAE,IAAI,CAACU,YAAY,CAAChF,IAAI,EAAEwJ,OAAO,IAAI,KAAK,CAAC,CAAC,CAAC;IACpJ,CAAC,CAAC,EAAE,IAAI,CAACN,sBAAsB,CAAC,CAAC;EACnC;AACF,CAAC;AACDhB,WAAW,CAACI,OAAO,GAAG,EAAE;AACxB,IAAIuC,UAAU,GAAG3C,WAAW;AAC5B,MAAM4C,YAAY,GAAGD,UAAU;AAC/B,SAASE,OAAOA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAC3B,IAAI/H,MAAM,CAAC1F,IAAI,CAACwN,IAAI,CAAC,CAAChJ,MAAM,KAAKkB,MAAM,CAAC1F,IAAI,CAACyN,IAAI,CAAC,CAACjJ,MAAM,EACvD,OAAO,CAAC,CAAC;EACX,KAAK,MAAMkJ,IAAI,IAAIF,IAAI,EACrB,IAAIA,IAAI,CAACE,IAAI,CAAC,KAAKD,IAAI,CAACC,IAAI,CAAC,EAC3B,OAAO,CAAC,CAAC;EACb,OAAO,CAAC,CAAC;AACX;AACA,MAAMC,WAAW,GAAG,MAAMA,WAAW,SAASL,YAAY,CAAC;EACzD,OAAOvL,MAAMA,CAACtB,KAAK,EAAE;IACnB,OAAO,KAAK,CAACsB,MAAM,CAACtB,KAAK,CAAC;EAC5B;EACA,OAAOmN,OAAOA,CAAClL,OAAO,EAAEV,MAAM,EAAE;IAC9B,MAAME,MAAM,GAAGF,MAAM,CAACG,KAAK,CAACwL,WAAW,CAACnK,QAAQ,CAAC;IACjD,IAAI,EAAEtB,MAAM,IAAI,IAAI,IAAIQ,OAAO,CAACQ,OAAO,KAAKhB,MAAM,CAACgB,OAAO,CAAC,EAAE;MAC3D,IAAI,OAAO,IAAI,CAACA,OAAO,IAAI,QAAQ,EACjC,OAAO,CAAC,CAAC;MACX,IAAIhD,KAAK,CAACyD,OAAO,CAAC,IAAI,CAACT,OAAO,CAAC,EAC7B,OAAOR,OAAO,CAACQ,OAAO,CAAC2K,WAAW,CAAC,CAAC;IACxC;EACF;EACApO,WAAWA,CAACuC,MAAM,EAAEU,OAAO,EAAE;IAC3B,KAAK,CAACV,MAAM,EAAEU,OAAO,CAAC,EAAE,IAAI,CAACtC,UAAU,GAAG,IAAI2F,iBAAiB,CAAC,IAAI,CAACrD,OAAO,CAAC;EAC/E;EACAiD,MAAMA,CAACpF,IAAI,EAAEE,KAAK,EAAE;IAClB,IAAIF,IAAI,KAAK,IAAI,CAACiG,OAAO,CAAChD,QAAQ,IAAI,CAAC/C,KAAK,EAC1C,IAAI,CAACkH,QAAQ,CAAC9D,OAAO,CAAE2G,KAAK,IAAK;MAC/BA,KAAK,YAAYmD,WAAW,KAAKnD,KAAK,GAAGA,KAAK,CAACpD,IAAI,CAACuG,WAAW,CAACnK,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAACpD,UAAU,CAACoF,IAAI,CAACgF,KAAK,CAAC;IAC7G,CAAC,CAAC,EAAE,IAAI,CAACsB,MAAM,CAAC,CAAC,CAAC,KACf;MACH,MAAMnG,MAAM,GAAG,IAAI,CAAC3D,MAAM,CAACG,KAAK,CAAC5B,IAAI,EAAE5B,KAAK,CAACM,MAAM,CAAC;MACpD,IAAI0G,MAAM,IAAI,IAAI,EAChB;MACFA,MAAM,YAAYnG,UAAU,GAAG,IAAI,CAACY,UAAU,CAACgF,SAAS,CAACO,MAAM,EAAElF,KAAK,CAAC,GAAGA,KAAK,KAAKF,IAAI,KAAK,IAAI,CAACiG,OAAO,CAAChD,QAAQ,IAAI,IAAI,CAACoK,OAAO,CAAC,CAAC,CAACrN,IAAI,CAAC,KAAKE,KAAK,CAAC,IAAI,IAAI,CAACsH,WAAW,CAACxH,IAAI,EAAEE,KAAK,CAAC;IACxL;EACF;EACAmN,OAAOA,CAAA,EAAG;IACR,MAAMA,OAAO,GAAG,IAAI,CAACxN,UAAU,CAACyF,MAAM,CAAC,CAAC;MAAEF,MAAM,GAAG,IAAI,CAACa,OAAO,CAACoH,OAAO,CAAC,IAAI,CAAClL,OAAO,EAAE,IAAI,CAACV,MAAM,CAAC;IAClG,OAAO2D,MAAM,IAAI,IAAI,KAAKiI,OAAO,CAAC,IAAI,CAACpH,OAAO,CAAChD,QAAQ,CAAC,GAAGmC,MAAM,CAAC,EAAEiI,OAAO;EAC7E;EACAzG,QAAQA,CAACF,KAAK,EAAEzC,MAAM,EAAEjE,IAAI,EAAEE,KAAK,EAAE;IACnC,IAAI,CAACmN,OAAO,CAAC,CAAC,CAACrN,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,CAACyB,MAAM,CAACG,KAAK,CAAC5B,IAAI,EAAE5B,KAAK,CAACI,SAAS,CAAC,GAAG,IAAI,CAACmI,OAAO,CAACD,KAAK,EAAEzC,MAAM,CAAC,CAACmB,MAAM,CAACpF,IAAI,EAAEE,KAAK,CAAC,GAAG,KAAK,CAAC0G,QAAQ,CAACF,KAAK,EAAEzC,MAAM,EAAEjE,IAAI,EAAEE,KAAK,CAAC;EACzK;EACAmH,QAAQA,CAACyE,OAAO,EAAE;IAChB,KAAK,CAACzE,QAAQ,CAACyE,OAAO,CAAC;IACvB,MAAMuB,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC,CAAC;IAC9B,IAAIlI,MAAM,CAAC1F,IAAI,CAAC4N,OAAO,CAAC,CAACpJ,MAAM,KAAK,CAAC,EACnC,OAAO,IAAI,CAACsH,MAAM,CAAC,CAAC;IACtB,MAAM5F,IAAI,GAAG,IAAI,CAACA,IAAI;IACtBA,IAAI,YAAYyH,WAAW,IAAIzH,IAAI,CAACD,IAAI,KAAK,IAAI,IAAIsH,OAAO,CAACK,OAAO,EAAE1H,IAAI,CAAC0H,OAAO,CAAC,CAAC,CAAC,KAAK1H,IAAI,CAACiG,YAAY,CAAC,IAAI,CAAC,EAAEjG,IAAI,CAACnF,MAAM,CAAC,CAAC,CAAC;EACnI;EACAgH,WAAWA,CAACxH,IAAI,EAAEE,KAAK,EAAE;IACvB,MAAMuH,WAAW,GAAG,KAAK,CAACD,WAAW,CAACxH,IAAI,EAAEE,KAAK,CAAC;IAClD,OAAO,IAAI,CAACL,UAAU,CAACoF,IAAI,CAACwC,WAAW,CAAC,EAAEA,WAAW;EACvD;EACAE,MAAMA,CAACyE,SAAS,EAAEN,OAAO,EAAE;IACzB,KAAK,CAACnE,MAAM,CAACyE,SAAS,EAAEN,OAAO,CAAC,EAAEM,SAAS,CAAC1J,IAAI,CAC7C6J,QAAQ,IAAKA,QAAQ,CAACrH,MAAM,KAAK,IAAI,CAAC/C,OAAO,IAAIoK,QAAQ,CAACC,IAAI,KAAK,YACtE,CAAC,IAAI,IAAI,CAAC3M,UAAU,CAAC+E,KAAK,CAAC,CAAC;EAC9B;EACAiC,IAAIA,CAAC7G,IAAI,EAAEE,KAAK,EAAE;IAChB,MAAM2H,OAAO,GAAG,KAAK,CAAChB,IAAI,CAAC7G,IAAI,EAAEE,KAAK,CAAC;IACvC,OAAO2H,OAAO,YAAYuF,WAAW,IAAI,IAAI,CAACvN,UAAU,CAACwF,IAAI,CAACwC,OAAO,CAAC,EAAEA,OAAO;EACjF;AACF,CAAC;AACDuF,WAAW,CAAC/B,eAAe,GAAG,CAAC+B,WAAW,EAAE1E,UAAU,CAAC,EAAE0E,WAAW,CAACnK,QAAQ,GAAG,QAAQ,EAAEmK,WAAW,CAAC7N,KAAK,GAAGnB,KAAK,CAACS,WAAW,EAAEuO,WAAW,CAACzK,OAAO,GAAG,MAAM;AAC7J,IAAI4K,UAAU,GAAGH,WAAW;AAC5B,MAAMI,YAAY,GAAGD,UAAU;EAAEE,UAAU,GAAG,MAAMA,UAAU,SAASV,YAAY,CAAC;IAClF,OAAOvL,MAAMA,CAACtB,KAAK,EAAE;MACnB,OAAO,KAAK,CAACsB,MAAM,CAACtB,KAAK,CAAC;IAC5B;IACA,OAAOmN,OAAOA,CAAClL,OAAO,EAAEV,MAAM,EAAE;MAC9B,MAAME,MAAM,GAAGF,MAAM,CAACG,KAAK,CAAC6L,UAAU,CAACxK,QAAQ,CAAC;MAChD,IAAI,EAAEtB,MAAM,IAAI,IAAI,IAAIQ,OAAO,CAACQ,OAAO,KAAKhB,MAAM,CAACgB,OAAO,CAAC,EAAE;QAC3D,IAAI,OAAO,IAAI,CAACA,OAAO,IAAI,QAAQ,EACjC,OAAO,CAAC,CAAC;QACX,IAAIhD,KAAK,CAACyD,OAAO,CAAC,IAAI,CAACT,OAAO,CAAC,EAC7B,OAAOR,OAAO,CAACQ,OAAO,CAAC2K,WAAW,CAAC,CAAC;MACxC;IACF;IACApO,WAAWA,CAACuC,MAAM,EAAEU,OAAO,EAAE;MAC3B,KAAK,CAACV,MAAM,EAAEU,OAAO,CAAC,EAAE,IAAI,CAACtC,UAAU,GAAG,IAAI2F,iBAAiB,CAAC,IAAI,CAACrD,OAAO,CAAC;IAC/E;IACAiD,MAAMA,CAACpF,IAAI,EAAEE,KAAK,EAAE;MAClB,MAAMkF,MAAM,GAAG,IAAI,CAAC3D,MAAM,CAACG,KAAK,CAAC5B,IAAI,EAAE5B,KAAK,CAACO,KAAK,CAAC;MACnDyG,MAAM,IAAI,IAAI,KAAKA,MAAM,YAAYnG,UAAU,GAAG,IAAI,CAACY,UAAU,CAACgF,SAAS,CAACO,MAAM,EAAElF,KAAK,CAAC,GAAGF,IAAI,KAAK,IAAI,CAACiG,OAAO,CAAChD,QAAQ,IAAI,CAAC/C,KAAK,GAAG,IAAI,CAACsH,WAAW,CAACiG,UAAU,CAACxK,QAAQ,CAAC,GAAG/C,KAAK,KAAKF,IAAI,KAAK,IAAI,CAACiG,OAAO,CAAChD,QAAQ,IAAI,IAAI,CAACoK,OAAO,CAAC,CAAC,CAACrN,IAAI,CAAC,KAAKE,KAAK,CAAC,IAAI,IAAI,CAACsH,WAAW,CAACxH,IAAI,EAAEE,KAAK,CAAC,CAAC;IAC/R;IACAmN,OAAOA,CAAA,EAAG;MACR,MAAMA,OAAO,GAAG,IAAI,CAACxN,UAAU,CAACyF,MAAM,CAAC,CAAC;QAAEF,MAAM,GAAG,IAAI,CAACa,OAAO,CAACoH,OAAO,CAAC,IAAI,CAAClL,OAAO,EAAE,IAAI,CAACV,MAAM,CAAC;MAClG,OAAO2D,MAAM,IAAI,IAAI,KAAKiI,OAAO,CAAC,IAAI,CAACpH,OAAO,CAAChD,QAAQ,CAAC,GAAGmC,MAAM,CAAC,EAAEiI,OAAO;IAC7E;IACAzG,QAAQA,CAACF,KAAK,EAAEzC,MAAM,EAAEjE,IAAI,EAAEE,KAAK,EAAE;MACnC,IAAI,CAACuB,MAAM,CAACG,KAAK,CAAC5B,IAAI,EAAE5B,KAAK,CAACO,KAAK,CAAC,IAAI,IAAI,GAAG,IAAI,CAACyG,MAAM,CAACpF,IAAI,EAAEE,KAAK,CAAC,GAAG,KAAK,CAAC0G,QAAQ,CAACF,KAAK,EAAEzC,MAAM,EAAEjE,IAAI,EAAEE,KAAK,CAAC;IACtH;IACA4G,QAAQA,CAACJ,KAAK,EAAExG,KAAK,EAAE6G,GAAG,EAAE;MAC1B,IAAIA,GAAG,IAAI,IAAI,IAAI,IAAI,CAACtF,MAAM,CAACG,KAAK,CAAC1B,KAAK,EAAE9B,KAAK,CAACM,MAAM,CAAC,IAAI,IAAI,EAC/D,KAAK,CAACoI,QAAQ,CAACJ,KAAK,EAAExG,KAAK,EAAE6G,GAAG,CAAC,CAAC,KAC/B;QACH,MAAMmF,KAAK,GAAG,IAAI,CAACzJ,KAAK,CAACiE,KAAK,CAAC;QAC/B,IAAIwF,KAAK,IAAI,IAAI,EAAE;UACjB,MAAMjK,IAAI,GAAG,IAAI,CAACR,MAAM,CAACD,MAAM,CAACtB,KAAK,EAAE6G,GAAG,CAAC;UAC3CmF,KAAK,CAAC5F,MAAM,CAACW,YAAY,CAAChF,IAAI,EAAEiK,KAAK,CAAC;QACxC,CAAC,MACC,MAAM,IAAItL,KAAK,CAAC,4CAA4C,CAAC;MACjE;IACF;IACA4G,WAAWA,CAACxH,IAAI,EAAEE,KAAK,EAAE;MACvB,MAAMuH,WAAW,GAAG,KAAK,CAACD,WAAW,CAACxH,IAAI,EAAEE,KAAK,CAAC;MAClD,OAAO,IAAI,CAACL,UAAU,CAACoF,IAAI,CAACwC,WAAW,CAAC,EAAEA,WAAW;IACvD;IACAE,MAAMA,CAACyE,SAAS,EAAEN,OAAO,EAAE;MACzB,KAAK,CAACnE,MAAM,CAACyE,SAAS,EAAEN,OAAO,CAAC,EAAEM,SAAS,CAAC1J,IAAI,CAC7C6J,QAAQ,IAAKA,QAAQ,CAACrH,MAAM,KAAK,IAAI,CAAC/C,OAAO,IAAIoK,QAAQ,CAACC,IAAI,KAAK,YACtE,CAAC,IAAI,IAAI,CAAC3M,UAAU,CAAC+E,KAAK,CAAC,CAAC;IAC9B;EACF,CAAC;AACD6I,UAAU,CAACxK,QAAQ,GAAG,OAAO,EAAEwK,UAAU,CAAClO,KAAK,GAAGnB,KAAK,CAACQ,UAAU,EAAE6O,UAAU,CAAC9K,OAAO,GAAG,GAAG,EAAE8K,UAAU,CAACpC,eAAe,GAAG,CACzHmC,YAAY,EACZC,UAAU,EACV/E,UAAU,CACX;AACD,IAAIgF,SAAS,GAAGD,UAAU;AAC1B,MAAME,WAAW,GAAGD,SAAS;EAAEE,cAAc,GAAG,MAAMA,cAAc,SAASb,YAAY,CAAC;IACxFc,UAAUA,CAAA,EAAG;MACX,OAAO,IAAI,CAAClI,IAAI,KAAK,IAAI,IAAI,IAAI,CAACA,IAAI,CAACM,OAAO,CAAChD,QAAQ,KAAK,IAAI,CAACgD,OAAO,CAAChD,QAAQ;IACnF;IACAwD,QAAQA,CAACC,KAAK,EAAEzC,MAAM,EAAE;MACtB,KAAK,CAACwC,QAAQ,CAACC,KAAK,EAAEzC,MAAM,CAAC,EAAE,IAAI,CAACkH,sBAAsB,CAAC,CAAC;IAC9D;IACAvE,QAAQA,CAACF,KAAK,EAAEzC,MAAM,EAAEjE,IAAI,EAAEE,KAAK,EAAE;MACnC,KAAK,CAAC0G,QAAQ,CAACF,KAAK,EAAEzC,MAAM,EAAEjE,IAAI,EAAEE,KAAK,CAAC,EAAE,IAAI,CAACiL,sBAAsB,CAAC,CAAC;IAC3E;IACArE,QAAQA,CAACJ,KAAK,EAAExG,KAAK,EAAE6G,GAAG,EAAE;MAC1B,KAAK,CAACD,QAAQ,CAACJ,KAAK,EAAExG,KAAK,EAAE6G,GAAG,CAAC,EAAE,IAAI,CAACoE,sBAAsB,CAAC,CAAC;IAClE;IACA9D,QAAQA,CAACyE,OAAO,EAAE;MAChB,KAAK,CAACzE,QAAQ,CAACyE,OAAO,CAAC,EAAE,IAAI,CAAC1E,QAAQ,CAACnD,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC0B,IAAI,IAAI,IAAI,IAAI,IAAI,CAACkI,UAAU,CAAC,CAAC,KAAK,IAAI,CAAClI,IAAI,CAACiG,YAAY,CAAC,IAAI,CAAC,EAAE,IAAI,CAACjG,IAAI,CAACnF,MAAM,CAAC,CAAC,CAAC;IACnJ;EACF,CAAC;AACDoN,cAAc,CAAC3K,QAAQ,GAAG,WAAW,EAAE2K,cAAc,CAACrO,KAAK,GAAGnB,KAAK,CAACQ,UAAU;AAC9E,IAAIkP,aAAa,GAAGF,cAAc;AAClC,MAAMG,eAAe,GAAGD,aAAa;AACrC,MAAME,SAAS,SAAStF,UAAU,CAAC;EACjC,OAAO2E,OAAOA,CAACpF,QAAQ,EAAEgG,OAAO,EAAE,CAClC;EACA7I,MAAMA,CAACpF,IAAI,EAAEE,KAAK,EAAE;IAClB,KAAK,CAAC0G,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC3C,MAAM,CAAC,CAAC,EAAEjE,IAAI,EAAEE,KAAK,CAAC;EAC/C;EACA0G,QAAQA,CAACF,KAAK,EAAEzC,MAAM,EAAEjE,IAAI,EAAEE,KAAK,EAAE;IACnCwG,KAAK,KAAK,CAAC,IAAIzC,MAAM,KAAK,IAAI,CAACA,MAAM,CAAC,CAAC,GAAG,IAAI,CAACmB,MAAM,CAACpF,IAAI,EAAEE,KAAK,CAAC,GAAG,KAAK,CAAC0G,QAAQ,CAACF,KAAK,EAAEzC,MAAM,EAAEjE,IAAI,EAAEE,KAAK,CAAC;EACjH;EACAmN,OAAOA,CAAA,EAAG;IACR,OAAO,IAAI,CAACpH,OAAO,CAACoH,OAAO,CAAC,IAAI,CAAClL,OAAO,EAAE,IAAI,CAACV,MAAM,CAAC;EACxD;AACF;AACA,MAAMyM,WAAW,GAAGF,SAAS;EAAEG,eAAe,GAAG;IAC/CtO,UAAU,EAAE,CAAC,CAAC;IACduO,aAAa,EAAE,CAAC,CAAC;IACjBC,qBAAqB,EAAE,CAAC,CAAC;IACzBC,SAAS,EAAE,CAAC,CAAC;IACbC,OAAO,EAAE,CAAC;EACZ,CAAC;EAAEC,uBAAuB,GAAG,GAAG;EAAEC,WAAW,GAAG,MAAMA,WAAW,SAAS1B,YAAY,CAAC;IACrF7N,WAAWA,CAACwP,QAAQ,EAAEhP,IAAI,EAAE;MAC1B,KAAK,CAAC,IAAI,EAAEA,IAAI,CAAC,EAAE,IAAI,CAACgP,QAAQ,GAAGA,QAAQ,EAAE,IAAI,CAACjN,MAAM,GAAG,IAAI,EAAE,IAAI,CAACmD,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC+J,QAAQ,GAAG,IAAIC,gBAAgB,CAAExC,SAAS,IAAK;QACjI,IAAI,CAACzE,MAAM,CAACyE,SAAS,CAAC;MACxB,CAAC,CAAC,EAAE,IAAI,CAACuC,QAAQ,CAACE,OAAO,CAAC,IAAI,CAAC1M,OAAO,EAAEgM,eAAe,CAAC,EAAE,IAAI,CAACjI,MAAM,CAAC,CAAC;IACzE;IACA1E,MAAMA,CAACE,KAAK,EAAExB,KAAK,EAAE;MACnB,OAAO,IAAI,CAACwO,QAAQ,CAAClN,MAAM,CAAC,IAAI,EAAEE,KAAK,EAAExB,KAAK,CAAC;IACjD;IACAgB,IAAIA,CAACxB,IAAI,EAAEyB,MAAM,GAAG,CAAC,CAAC,EAAE;MACtB,MAAMc,IAAI,GAAG,IAAI,CAACyM,QAAQ,CAACxN,IAAI,CAACxB,IAAI,EAAEyB,MAAM,CAAC;MAC7C,OAAOc,IAAI,GAAGA,IAAI,CAACR,MAAM,KAAK,IAAI,GAAGQ,IAAI,GAAGd,MAAM,GAAG,IAAI,CAACD,IAAI,CAACe,IAAI,CAACR,MAAM,CAACU,OAAO,CAACZ,UAAU,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI;IAClH;IACAK,KAAKA,CAACA,KAAK,EAAErC,KAAK,GAAGnB,KAAK,CAACY,GAAG,EAAE;MAC9B,OAAO,IAAI,CAAC0P,QAAQ,CAAC9M,KAAK,CAACA,KAAK,EAAErC,KAAK,CAAC;IAC1C;IACAqD,QAAQA,CAAC,GAAGC,WAAW,EAAE;MACvB,OAAO,IAAI,CAAC6L,QAAQ,CAAC9L,QAAQ,CAAC,GAAGC,WAAW,CAAC;IAC/C;IACA+B,KAAKA,CAAA,EAAG;MACN,IAAI,CAACnD,MAAM,IAAI,IAAI,IAAI,KAAK,CAACmD,KAAK,CAAC,CAAC;IACtC;IACAyB,MAAMA,CAAA,EAAG;MACP,KAAK,CAACA,MAAM,CAAC,CAAC,EAAE,IAAI,CAACsI,QAAQ,CAACG,UAAU,CAAC,CAAC;IAC5C;IACArI,QAAQA,CAACC,KAAK,EAAEzC,MAAM,EAAE;MACtB,IAAI,CAAC0D,MAAM,CAAC,CAAC,EAAEjB,KAAK,KAAK,CAAC,IAAIzC,MAAM,KAAK,IAAI,CAACA,MAAM,CAAC,CAAC,GAAG,IAAI,CAACmD,QAAQ,CAAC9D,OAAO,CAAE2G,KAAK,IAAK;QACxFA,KAAK,CAACzJ,MAAM,CAAC,CAAC;MAChB,CAAC,CAAC,GAAG,KAAK,CAACiG,QAAQ,CAACC,KAAK,EAAEzC,MAAM,CAAC;IACpC;IACA2C,QAAQA,CAACF,KAAK,EAAEzC,MAAM,EAAEjE,IAAI,EAAEE,KAAK,EAAE;MACnC,IAAI,CAACyH,MAAM,CAAC,CAAC,EAAE,KAAK,CAACf,QAAQ,CAACF,KAAK,EAAEzC,MAAM,EAAEjE,IAAI,EAAEE,KAAK,CAAC;IAC3D;IACA4G,QAAQA,CAACJ,KAAK,EAAExG,KAAK,EAAE6G,GAAG,EAAE;MAC1B,IAAI,CAACY,MAAM,CAAC,CAAC,EAAE,KAAK,CAACb,QAAQ,CAACJ,KAAK,EAAExG,KAAK,EAAE6G,GAAG,CAAC;IAClD;IACAM,QAAQA,CAAC+E,SAAS,GAAG,EAAE,EAAEN,OAAO,GAAG,CAAC,CAAC,EAAE;MACrC,KAAK,CAACzE,QAAQ,CAACyE,OAAO,CAAC;MACvB,MAAMiD,YAAY,GAAGjD,OAAO,CAACiD,YAAY,IAAI,eAAgB,IAAIvL,OAAO,CAAC,CAAC;MAC1E,IAAIwL,OAAO,GAAGrP,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC+O,QAAQ,CAACM,WAAW,CAAC,CAAC,CAAC;MACrD,OAAOD,OAAO,CAAC/K,MAAM,GAAG,CAAC,GACvBmI,SAAS,CAACtC,IAAI,CAACkF,OAAO,CAACE,GAAG,CAAC,CAAC,CAAC;MAC/B,MAAMC,IAAI,GAAGA,CAAClN,IAAI,EAAEmN,UAAU,GAAG,CAAC,CAAC,KAAK;UACtCnN,IAAI,IAAI,IAAI,IAAIA,IAAI,KAAK,IAAI,IAAIA,IAAI,CAACE,OAAO,CAACZ,UAAU,IAAI,IAAI,KAAKwN,YAAY,CAAC1N,GAAG,CAACY,IAAI,CAACE,OAAO,CAAC,IAAI4M,YAAY,CAAC7M,GAAG,CAACD,IAAI,CAACE,OAAO,EAAE,EAAE,CAAC,EAAEiN,UAAU,IAAID,IAAI,CAAClN,IAAI,CAACqE,MAAM,CAAC,CAAC;QAC7K,CAAC;QAAEe,QAAQ,GAAIpF,IAAI,IAAK;UACtB8M,YAAY,CAAC1N,GAAG,CAACY,IAAI,CAACE,OAAO,CAAC,KAAKF,IAAI,YAAY8K,YAAY,IAAI9K,IAAI,CAACmF,QAAQ,CAAC9D,OAAO,CAAC+D,QAAQ,CAAC,EAAE0H,YAAY,CAACvI,MAAM,CAACvE,IAAI,CAACE,OAAO,CAAC,EAAEF,IAAI,CAACoF,QAAQ,CAACyE,OAAO,CAAC,CAAC;QAChK,CAAC;MACD,IAAIuD,SAAS,GAAGjD,SAAS;MACzB,KAAK,IAAIkD,CAAC,GAAG,CAAC,EAAED,SAAS,CAACpL,MAAM,GAAG,CAAC,EAAEqL,CAAC,IAAI,CAAC,EAAE;QAC5C,IAAIA,CAAC,IAAId,uBAAuB,EAC9B,MAAM,IAAI5N,KAAK,CAAC,iDAAiD,CAAC;QACpE,KAAKyO,SAAS,CAAC/L,OAAO,CAAEiJ,QAAQ,IAAK;UACnC,MAAMtK,IAAI,GAAG,IAAI,CAACf,IAAI,CAACqL,QAAQ,CAACrH,MAAM,EAAE,CAAC,CAAC,CAAC;UAC3CjD,IAAI,IAAI,IAAI,KAAKA,IAAI,CAACE,OAAO,KAAKoK,QAAQ,CAACrH,MAAM,KAAKqH,QAAQ,CAACC,IAAI,KAAK,WAAW,IAAI2C,IAAI,CAAC,IAAI,CAACjO,IAAI,CAACqL,QAAQ,CAACgD,eAAe,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE5P,KAAK,CAACC,IAAI,CAAC2M,QAAQ,CAACF,UAAU,CAAC,CAAC/I,OAAO,CAAE5D,IAAI,IAAK;YACtL,MAAMuK,KAAK,GAAG,IAAI,CAAC/I,IAAI,CAACxB,IAAI,EAAE,CAAC,CAAC,CAAC;YACjCyP,IAAI,CAAClF,KAAK,EAAE,CAAC,CAAC,CAAC,EAAEA,KAAK,YAAY8C,YAAY,IAAI9C,KAAK,CAAC7C,QAAQ,CAAC9D,OAAO,CAAEkM,UAAU,IAAK;cACvFL,IAAI,CAACK,UAAU,EAAE,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC;UACJ,CAAC,CAAC,IAAIjD,QAAQ,CAACC,IAAI,KAAK,YAAY,IAAI2C,IAAI,CAAClN,IAAI,CAACyD,IAAI,CAAC,CAAC,EAAEyJ,IAAI,CAAClN,IAAI,CAAC,CAAC;QACvE,CAAC,CAAC,EAAE,IAAI,CAACmF,QAAQ,CAAC9D,OAAO,CAAC+D,QAAQ,CAAC,EAAEgI,SAAS,GAAG1P,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC+O,QAAQ,CAACM,WAAW,CAAC,CAAC,CAAC,EAAED,OAAO,GAAGK,SAAS,CAACvL,KAAK,CAAC,CAAC,EAAEkL,OAAO,CAAC/K,MAAM,GAAG,CAAC,GACvImI,SAAS,CAACtC,IAAI,CAACkF,OAAO,CAACE,GAAG,CAAC,CAAC,CAAC;MACjC;IACF;IACAvH,MAAMA,CAACyE,SAAS,EAAEN,OAAO,GAAG,CAAC,CAAC,EAAE;MAC9BM,SAAS,GAAGA,SAAS,IAAI,IAAI,CAACuC,QAAQ,CAACM,WAAW,CAAC,CAAC;MACpD,MAAMF,YAAY,GAAG,eAAgB,IAAIvL,OAAO,CAAC,CAAC;MAClD4I,SAAS,CAACtM,GAAG,CAAEyM,QAAQ,IAAK;QAC1B,MAAMtK,IAAI,GAAG,IAAI,CAACf,IAAI,CAACqL,QAAQ,CAACrH,MAAM,EAAE,CAAC,CAAC,CAAC;QAC3C,OAAOjD,IAAI,IAAI,IAAI,GAAG,IAAI,GAAG8M,YAAY,CAAC1N,GAAG,CAACY,IAAI,CAACE,OAAO,CAAC,IAAI4M,YAAY,CAACzN,GAAG,CAACW,IAAI,CAACE,OAAO,CAAC,CAAC2H,IAAI,CAACyC,QAAQ,CAAC,EAAE,IAAI,KAAKwC,YAAY,CAAC7M,GAAG,CAACD,IAAI,CAACE,OAAO,EAAE,CAACoK,QAAQ,CAAC,CAAC,EAAEtK,IAAI,CAAC;MAC1K,CAAC,CAAC,CAACqB,OAAO,CAAErB,IAAI,IAAK;QACnBA,IAAI,IAAI,IAAI,IAAIA,IAAI,KAAK,IAAI,IAAI8M,YAAY,CAAC1N,GAAG,CAACY,IAAI,CAACE,OAAO,CAAC,IAAIF,IAAI,CAAC0F,MAAM,CAACoH,YAAY,CAACzN,GAAG,CAACW,IAAI,CAACE,OAAO,CAAC,IAAI,EAAE,EAAE2J,OAAO,CAAC;MAC/H,CAAC,CAAC,EAAEA,OAAO,CAACiD,YAAY,GAAGA,YAAY,EAAEA,YAAY,CAAC1N,GAAG,CAAC,IAAI,CAACc,OAAO,CAAC,IAAI,KAAK,CAACwF,MAAM,CAACoH,YAAY,CAACzN,GAAG,CAAC,IAAI,CAACa,OAAO,CAAC,EAAE2J,OAAO,CAAC,EAAE,IAAI,CAACzE,QAAQ,CAAC+E,SAAS,EAAEN,OAAO,CAAC;IACrK;EACF,CAAC;AACD2C,WAAW,CAACxL,QAAQ,GAAG,QAAQ,EAAEwL,WAAW,CAAC1C,YAAY,GAAG4B,WAAW,EAAEc,WAAW,CAACpD,eAAe,GAAG,CAACsC,WAAW,EAAEI,eAAe,CAAC,EAAEU,WAAW,CAAClP,KAAK,GAAGnB,KAAK,CAACQ,UAAU,EAAE6P,WAAW,CAAC9L,OAAO,GAAG,KAAK;AACxM,IAAI8M,UAAU,GAAGhB,WAAW;AAC5B,MAAMiB,YAAY,GAAGD,UAAU;EAAEE,SAAS,GAAG,MAAMA,SAAS,SAASjH,UAAU,CAAC;IAC9E,OAAOlH,MAAMA,CAACtB,KAAK,EAAE;MACnB,OAAO6F,QAAQ,CAAC6J,cAAc,CAAC1P,KAAK,CAAC;IACvC;IACA,OAAOA,KAAKA,CAACiC,OAAO,EAAE;MACpB,OAAOA,OAAO,CAAC0N,IAAI;IACrB;IACA3Q,WAAWA,CAACuC,MAAM,EAAE/B,IAAI,EAAE;MACxB,KAAK,CAAC+B,MAAM,EAAE/B,IAAI,CAAC,EAAE,IAAI,CAAC2C,IAAI,GAAG,IAAI,CAAC4D,OAAO,CAAC/F,KAAK,CAAC,IAAI,CAACiC,OAAO,CAAC;IACnE;IACAsE,QAAQA,CAACC,KAAK,EAAEzC,MAAM,EAAE;MACtB,IAAI,CAAC9B,OAAO,CAAC0N,IAAI,GAAG,IAAI,CAACxN,IAAI,GAAG,IAAI,CAACA,IAAI,CAACyB,KAAK,CAAC,CAAC,EAAE4C,KAAK,CAAC,GAAG,IAAI,CAACrE,IAAI,CAACyB,KAAK,CAAC4C,KAAK,GAAGzC,MAAM,CAAC;IAC7F;IACAyC,KAAKA,CAAChH,IAAI,EAAEwH,MAAM,EAAE;MAClB,OAAO,IAAI,CAAC/E,OAAO,KAAKzC,IAAI,GAAGwH,MAAM,GAAG,CAAC,CAAC;IAC5C;IACAJ,QAAQA,CAACJ,KAAK,EAAExG,KAAK,EAAE6G,GAAG,EAAE;MAC1BA,GAAG,IAAI,IAAI,IAAI,IAAI,CAAC1E,IAAI,GAAG,IAAI,CAACA,IAAI,CAACyB,KAAK,CAAC,CAAC,EAAE4C,KAAK,CAAC,GAAGxG,KAAK,GAAG,IAAI,CAACmC,IAAI,CAACyB,KAAK,CAAC4C,KAAK,CAAC,EAAE,IAAI,CAACvE,OAAO,CAAC0N,IAAI,GAAG,IAAI,CAACxN,IAAI,IAAI,KAAK,CAACyE,QAAQ,CAACJ,KAAK,EAAExG,KAAK,EAAE6G,GAAG,CAAC;IAC3J;IACA9C,MAAMA,CAAA,EAAG;MACP,OAAO,IAAI,CAAC5B,IAAI,CAAC4B,MAAM;IACzB;IACAoD,QAAQA,CAACyE,OAAO,EAAE;MAChB,KAAK,CAACzE,QAAQ,CAACyE,OAAO,CAAC,EAAE,IAAI,CAACzJ,IAAI,GAAG,IAAI,CAAC4D,OAAO,CAAC/F,KAAK,CAAC,IAAI,CAACiC,OAAO,CAAC,EAAE,IAAI,CAACE,IAAI,CAAC4B,MAAM,KAAK,CAAC,GAAG,IAAI,CAACzD,MAAM,CAAC,CAAC,GAAG,IAAI,CAACmF,IAAI,YAAYgK,SAAS,IAAI,IAAI,CAAChK,IAAI,CAACD,IAAI,KAAK,IAAI,KAAK,IAAI,CAACoB,QAAQ,CAAC,IAAI,CAAC7C,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC0B,IAAI,CAACzF,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAACyF,IAAI,CAACnF,MAAM,CAAC,CAAC,CAAC;IACpP;IACA8H,QAAQA,CAAC5B,KAAK,EAAE6B,UAAU,GAAG,CAAC,CAAC,EAAE;MAC/B,OAAO,CAAC,IAAI,CAACpG,OAAO,EAAEuE,KAAK,CAAC;IAC9B;IACAjE,KAAKA,CAACiE,KAAK,EAAEuF,KAAK,GAAG,CAAC,CAAC,EAAE;MACvB,IAAI,CAACA,KAAK,EAAE;QACV,IAAIvF,KAAK,KAAK,CAAC,EACb,OAAO,IAAI;QACb,IAAIA,KAAK,KAAK,IAAI,CAACzC,MAAM,CAAC,CAAC,EACzB,OAAO,IAAI,CAAC0B,IAAI;MACpB;MACA,MAAMuG,KAAK,GAAG,IAAI,CAACzK,MAAM,CAACD,MAAM,CAAC,IAAI,CAACW,OAAO,CAAC2N,SAAS,CAACpJ,KAAK,CAAC,CAAC;MAC/D,OAAO,IAAI,CAACJ,MAAM,CAACW,YAAY,CAACiF,KAAK,EAAE,IAAI,CAACvG,IAAI,IAAI,KAAK,CAAC,CAAC,EAAE,IAAI,CAACtD,IAAI,GAAG,IAAI,CAAC4D,OAAO,CAAC/F,KAAK,CAAC,IAAI,CAACiC,OAAO,CAAC,EAAE+J,KAAK;IAClH;IACAvE,MAAMA,CAACyE,SAAS,EAAE9E,QAAQ,EAAE;MAC1B8E,SAAS,CAAC1J,IAAI,CAAE6J,QAAQ,IAAKA,QAAQ,CAACC,IAAI,KAAK,eAAe,IAAID,QAAQ,CAACrH,MAAM,KAAK,IAAI,CAAC/C,OAAO,CAAC,KAAK,IAAI,CAACE,IAAI,GAAG,IAAI,CAAC4D,OAAO,CAAC/F,KAAK,CAAC,IAAI,CAACiC,OAAO,CAAC,CAAC;IACvJ;IACAjC,KAAKA,CAAA,EAAG;MACN,OAAO,IAAI,CAACmC,IAAI;IAClB;EACF,CAAC;AACDsN,SAAS,CAAC1M,QAAQ,GAAG,MAAM,EAAE0M,SAAS,CAACpQ,KAAK,GAAGnB,KAAK,CAACS,WAAW;AAChE,IAAIkR,QAAQ,GAAGJ,SAAS;AACxB,MAAMK,UAAU,GAAGD,QAAQ;AAC3B,SACE9Q,UAAU,EACVuG,iBAAiB,IAAIb,eAAe,EACpCgJ,WAAW,IAAID,SAAS,EACxBxJ,iBAAiB,IAAIL,eAAe,EACpCkK,eAAe,IAAID,aAAa,EAChCI,WAAW,IAAIF,SAAS,EACxBR,YAAY,IAAID,UAAU,EAC1B7E,UAAU,IAAID,QAAQ,EACtBsE,YAAY,IAAID,UAAU,EAC1BrJ,QAAQ,EACRrF,KAAK,EACLsR,YAAY,IAAID,UAAU,EAC1B/K,iBAAiB,IAAIH,eAAe,EACpCyL,UAAU,IAAID,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}